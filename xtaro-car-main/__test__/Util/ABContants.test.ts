import { CarABConstants } from '../../src/pages/xcar/Util/Index';
import { AppContext } from '../../src/pages/xcar/Util/Index';
import { Utils } from '../../src/pages/xcar/Util/Index';

describe('测试 CarABConstants', () => {
  test('测试Isd 融合首页环境', () => {
    jest.spyOn(AppContext, 'setIsHomeCombine').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    expect(CarABConstants.isIsdHomeCombine()).toEqual(false);
  });

  test('测试Osd 融合首页环境', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
    expect(CarABConstants.isIsdHomeCombine()).toEqual(false);
  });
});
