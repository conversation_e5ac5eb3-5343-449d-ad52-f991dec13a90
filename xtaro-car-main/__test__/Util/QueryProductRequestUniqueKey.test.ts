import {
  combineCallbackRequestUniqueKey
} from '../../src/pages/xcar/Util/QueryProductRequestUniqueKey';

describe('测试 QueryProductRequestUniqueKey combineCallbackRequestUniqueKey', () => {
  const key: any = null;
  const pageNum: any = null;
  const mockMap = [
    {
      key: "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0",
      pageNum: 1,
      expected: 'adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0@@PAGENUM@@1@@RECOMMEND@@',
    },
    {
      key,
      pageNum,
      expected: 'null@@PAGENUM@@null@@RECOMMEND@@',
    },
  ];
  test.each(mockMap)(
    'combineCallbackRequestUniqueKey check',
    ({ key, pageNum, expected }) => {
      const data = combineCallbackRequestUniqueKey(key, pageNum);
      expect(data).toEqual(expected);
    },
  );
});