import { getHistoryAreaKey } from '../../src/pages/xcar/Util/HistoryStorage';
import { ClickKey, Country, StorageKey } from '../../src/pages/xcar/Constants/Index';


describe('getHistoryAreaKey', () => {
  it('should return correct historyAreaKey', () => {
    const expectedKey = StorageKey.CAR_AREA_HISTORY_CTRIP;

    const result = getHistoryAreaKey();

    expect(result).toBe(expectedKey);
  });
});

