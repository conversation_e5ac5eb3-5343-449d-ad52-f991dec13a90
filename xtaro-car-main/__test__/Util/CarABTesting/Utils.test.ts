import { AppContext } from '../../../src/pages/xcar/Util/Index';
import { getMockABTestingInfoSyncOutput, getMockMultiABTestingInfoSyncOutput } from '../../../src/pages/xcar/Util/CarABTesting/MockData';

describe('CarABTesting/Utils', () => {
  test('getAbByAppContext/single AB', () => {
    const singleAB = {};
    singleAB['221114_DSJT_yhyjh'] = getMockABTestingInfoSyncOutput;
    AppContext.setABTesting(singleAB);
    const { trace, datas } = AppContext.ABTesting;
    expect(datas['221114_DSJT_yhyjh'].ExpVersion).toEqual('B');
    expect(trace).toEqual('221114_DSJT_yhyjh|B');
  });

  test('getAbByAppContext/multi AB', () => {
    // multi-AB
    AppContext.setABTesting(getMockMultiABTestingInfoSyncOutput);
    const { trace, datas } = AppContext.ABTesting;
    expect(datas['221114_DSJT_yhyjh'].ExpVersion).toEqual('B');
  });
});
