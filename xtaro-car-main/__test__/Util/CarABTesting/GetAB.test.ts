import { xApplication as Application } from '@ctrip/xtaro';
import { isHomeUserLayering2BVer } from '../../../src/pages/xcar/Util/CarABTesting/GetAB';
import { AppContext, Utils } from '../../../src/pages/xcar/Util/Index';
import { getMockMultiABTestingInfoSyncOutput3 } from '../../../src/pages/xcar/Util/CarABTesting/MockData';

describe('CarABTesting/GetAB', () => {
  test('isHomeUserLayering2BVer', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    Object.defineProperty(Application, 'version', {
      value: '8.59.7',
    });
    AppContext.setABTesting(getMockMultiABTestingInfoSyncOutput3);
    expect(isHomeUserLayering2BVer()).toEqual(true);
  });
});
