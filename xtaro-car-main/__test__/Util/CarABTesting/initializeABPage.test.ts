import {
  initializeABPage,
  initializeABHomePage,
  initializeABListPage,
  initializeABVendorListPage,
} from '../../../src/pages/xcar/Util/CarABTesting/InitializeAB';
import ABKey from '../../../src/pages/xcar/Util/CarABTesting/ABKey';
import { Utils } from '../../../src/pages/xcar/Util/Index';
jest.mock('../../../src/pages/xcar/Util/CarABTesting/ABTesting', () => ({
  getABTestingInfoSync: keys => {
    const results: any = [];
    for (let i = 0; i < keys.length; i++) {
      results.push({
        ExpCode: keys[i]?.expCode,
        ExpVersion: 'B',
      });
    }
    return results;
  },
}));

describe('InitializeAB', () => {
  test('initializeABPage', () => {
    const keys = [];
    const isdKeys = [];
    const osdKeys = [];
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => false);
    const isdResult = initializeABPage({
      keys,
      isdKeys,
      osdKeys,
    });
    expect(isdResult).toEqual([]);
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
    const osdResult = initializeABPage({
      keys,
      isdKeys,
      osdKeys,
    });
    expect(osdResult).toEqual([]);
  });

  test('initializeABHomePage', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => false);
    const isdResult = initializeABHomePage();
    expect(isdResult).toEqual([
      { ExpCode: '240118_DSJT_xxlyh', ExpVersion: 'B' },
    ]);
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
    const osdResult = initializeABHomePage();
    expect(osdResult).toEqual([
      {
        ExpCode: '241204_DSJT_lydCN',
        ExpVersion: 'B',
      },
      {
        ExpCode: '240716_DSJT_Cref',
        ExpVersion: 'B',
      },
    ]);
  });

  test('initializeABListPage', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => false);
    const isdResult = initializeABListPage();
    expect(isdResult).toEqual([
      { ExpCode: '240826_DSJT_djyh', ExpVersion: 'B' },
      { ExpCode: '250221_DSJT_huojia15', ExpVersion: 'B' },
    ]);
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
    const osdResult = initializeABListPage();
    expect(osdResult).toEqual([
      {
        ExpCode: '241204_DSJT_lydCN',
        ExpVersion: 'B',
      },
      {
        ExpCode: '240716_DSJT_Cref',
        ExpVersion: 'B',
      },
    ]);
  });

  test('initializeABVendorListPage', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => false);
    const isdResult = initializeABVendorListPage();
    console.log(JSON.stringify(isdResult));
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
    const osdResult = initializeABVendorListPage();
    expect(osdResult).toEqual([]);
  });
});
