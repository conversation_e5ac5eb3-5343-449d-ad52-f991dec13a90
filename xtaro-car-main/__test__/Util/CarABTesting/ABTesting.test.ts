import ABTesting from '@c2x/apis/ABTesting';
import AppContext from '../../../src/pages/xcar/Util/AppContext';
import getABTestingInfoSync from '../../../src/pages/xcar/Util/CarABTesting/ABTesting';
import {
  getMockABTestingInfoSyncOutput,
  getMockMultiABTestingInfoSyncOutput,
} from '../../../src/pages/xcar/Util/CarABTesting/MockData';

const ABKey = {
  // 填写页优化项目
  BookingOptimization: {
    key: '221220_DSJT_txyyh',
    newVersionCode: ['B', 'C'],
    defaultVersionCode: 'A',
  },
  // 新版填写页优惠模块
  BookingNewCoupon: {
    key: '221114_DSJT_yhyjh',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
  },
  // 接入易车VR
  VendorListVR: {
    key: '230411_DSJT_ycvr',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
  },
};

beforeEach(() => {
  jest.resetAllMocks();
  AppContext.setABTesting(null);
});

describe('CarABTesting/ABTesting', () => {
  test('getABTestingInfoSync/Single', () => {
    jest
      .spyOn(ABTesting, 'getABTestingInfoSync')
      .mockReturnValue(getMockABTestingInfoSyncOutput);

    // 新KEY，走api获取实验值（第一次调用 getABTestingInfoSync）
    const log = getABTestingInfoSync({ expCode: ABKey.BookingNewCoupon.key });
    expect(log).toEqual([{ ExpCode: '221114_DSJT_yhyjh', ExpVersion: 'B' }]);
    expect(ABTesting.getABTestingInfoSync).toHaveBeenCalledTimes(1);

    // 同一个KEY，走缓存
    const log2 = getABTestingInfoSync({ expCode: ABKey.BookingNewCoupon.key });
    expect(log2).toEqual([{ ExpCode: '221114_DSJT_yhyjh', ExpVersion: 'B' }]);
    expect(ABTesting.getABTestingInfoSync).toHaveBeenCalledTimes(1);
  });
  test('getABTestingInfoSync/Multi', () => {
    jest
      .spyOn(ABTesting, 'getMultiABTestingInfoSync')
      .mockReturnValue(getMockMultiABTestingInfoSyncOutput);

    // 新KEY，走api获取实验值（第一次调用 getABTestingInfoSync）
    const log = getABTestingInfoSync([
      { expCode: ABKey.BookingOptimization.key },
      { expCode: ABKey.VendorListVR.key },
    ]);
    expect(log).toEqual([
      { ExpCode: '221220_DSJT_txyyh', ExpVersion: 'B' },
      { ExpCode: '230411_DSJT_ycvr', ExpVersion: 'B' },
    ]);
    expect(ABTesting.getMultiABTestingInfoSync).toHaveBeenCalledTimes(1);
  });
});
