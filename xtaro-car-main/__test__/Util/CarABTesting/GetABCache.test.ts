import Util from '@c2x/apis/Util';
import { AppContext } from '../../../src/pages/xcar/Util/Index';
import CarStorage from '../../../src/pages/xcar/Util/CarStorage';
import {
  cacheAB,
  syncCacheAb,
} from '../../../src/pages/xcar/Util/CarABTesting/GetABCache';

Util.isInChromeDebug = false;

jest.mock('../../../src/pages/xcar/Util/CarStorage', () => {
  let CurStorage = {};
  return {
    loadAsync: () => {
      return JSON.stringify(CurStorage);
    },
    loadSync: () => {
      return JSON.stringify(CurStorage);
    },
    save: (key, value) => {
      CurStorage = JSON.parse(value);
    },
    getStore: () => {
      return CurStorage;
    },
  };
});

const ABKey = {
  // 首页用户分层
  HomeUserLayer: {
    key: '230524_DSJT_syfc',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
  },
  // 订单填写页车辆信息优化 TODO 自营险二期 替换实验号
  OSDInsurance2: {
    key: '230828_DSJT_txyyh',
    isCache: true,
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
  },
};
jest.mock('../../../src/pages/xcar/Util/CarABTesting/ABKey', () => ({
  ABKey: ABKey,
  GetABValueType: key => {
    let abValue = null;
    Object.keys(ABKey).forEach(objKey => {
      if (ABKey[objKey].key === key) {
        abValue = ABKey[objKey];
      }
    });
    return abValue;
  },
  GetAllCacheABValueType: () => {
    const allList: any = [];
    Object.keys(ABKey).forEach(objKey => {
      if (ABKey[objKey].isCache) {
        allList.push(ABKey[objKey]);
      }
    });
    return allList;
  },
}));

const allAbResult: any = {
  '210323_DSJT_rlj': { ExpCode: '210323_DSJT_rlj', ExpVersion: '' },
  '210413_DSJT_lbyxg': { ExpCode: '210413_DSJT_lbyxg', ExpVersion: '' },
  '200911_DSJT_xsou': { ExpCode: '200911_DSJT_xsou', ExpVersion: '' },
  '230524_DSJT_syfc': {
    State: true,
    ExpVersion: 'D',
    ExpCode: '230524_DSJT_syfc',
    ExpDefaultVersion: '',
    BeginTime: '',
    ExpResult:
      'L:L132946,D:D,Mod:82,230524_DSJT_syfc:D,ClientCode:12001139890321479536,EffectTime:2023-05-24 20:04:54,IterationId:6',
    End: '',
    EndTime: '',
    Attrs: {},
  },
  '230828_DSJT_txyyh': {
    State: true,
    ExpVersion: 'B',
    ExpCode: '230828_DSJT_txyyh',
    ExpDefaultVersion: '',
    BeginTime: '',
    ExpResult:
      'L:L34627,D:D,Mod:50,230828_DSJT_txyyh:A,ClientCode:12001139890321479536,EffectTime:2023-08-29 19:42:26,IterationId:1',
    End: '',
    EndTime: '',
    Attrs: {},
  }
};

const allAbResult2: any = {
  '210323_DSJT_rlj': { ExpCode: '210323_DSJT_rlj', ExpVersion: '' },
  '210413_DSJT_lbyxg': { ExpCode: '210413_DSJT_lbyxg', ExpVersion: '' },
  '200911_DSJT_xsou': { ExpCode: '200911_DSJT_xsou', ExpVersion: '' },
  '230524_DSJT_syfc': {
    State: true,
    ExpVersion: 'D',
    ExpCode: '230524_DSJT_syfc',
    ExpDefaultVersion: '',
    BeginTime: '',
    ExpResult:
      'L:L132946,D:D,Mod:82,230524_DSJT_syfc:D,ClientCode:12001139890321479536,EffectTime:2023-05-24 20:04:54,IterationId:6',
    End: '',
    EndTime: '',
    Attrs: {},
  },
  '230828_DSJT_txyyh': {
    State: true,
    ExpVersion: 'A',
    ExpCode: '230828_DSJT_txyyh',
    ExpDefaultVersion: '',
    BeginTime: '',
    ExpResult:
      'L:L34627,D:D,Mod:50,230828_DSJT_txyyh:A,ClientCode:12001139890321479536,EffectTime:2023-08-29 19:42:26,IterationId:1',
    End: '',
    EndTime: '',
    Attrs: {},
  },
};

const abCacheResult: any = {
  '230828_DSJT_txyyh': {
    State: true,
    ExpVersion: 'B',
    ExpCode: '230828_DSJT_txyyh',
    ExpDefaultVersion: '',
    BeginTime: '',
    ExpResult:
      'L:L34627,D:D,Mod:50,230828_DSJT_txyyh:A,ClientCode:12001139890321479536,EffectTime:2023-08-29 19:42:26,IterationId:1',
    End: '',
    EndTime: '',
    Attrs: {},
  },
};

const abCacheResult2: any = {
  '230828_DSJT_txyyh': {
    State: true,
    ExpVersion: 'A',
    ExpCode: '230828_DSJT_txyyh',
    ExpDefaultVersion: '',
    BeginTime: '',
    ExpResult:
      'L:L34627,D:D,Mod:50,230828_DSJT_txyyh:A,ClientCode:12001139890321479536,EffectTime:2023-08-29 19:42:26,IterationId:1',
    End: '',
    EndTime: '',
    Attrs: {},
  }
};

beforeEach(() => {
  jest.resetAllMocks();
});

describe('CarABTesting/GetABCache', () => {
  test('cacheAB', async () => {
    AppContext.setABTesting(allAbResult);
    // 缓存的实验号没有配置缓存
    await cacheAB([{ expCode: '230524_DSJT_syfc' }]);
    const store1 = CarStorage.getStore();
    expect(store1).toEqual({});

    // 缓存的实验号配置了缓存
    await cacheAB([{ expCode: '230828_DSJT_txyyh' }]);
    const store2 = CarStorage.getStore();
    expect(store2).toEqual({
      '230828_DSJT_txyyh': {
        State: true,
        ExpVersion: 'B',
        ExpCode: '230828_DSJT_txyyh',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L34627,D:D,Mod:50,230828_DSJT_txyyh:A,ClientCode:12001139890321479536,EffectTime:2023-08-29 19:42:26,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });

    const store3 = CarStorage.getStore();
    expect(store3).toEqual(abCacheResult);

    // 新增两个并替换现有的缓存
    AppContext.setABTesting(allAbResult2);
    await cacheAB([
      { expCode: '230828_DSJT_txyyh' },
    ]);
    const store4 = CarStorage.getStore();
    expect(store4).toEqual(abCacheResult2);
  });

  test('syncCacheAb', async () => {
    AppContext.ABTesting.datas = {};
    CarStorage.save('', JSON.stringify(abCacheResult));
    // 缓存的实验号没有配置缓存
    syncCacheAb();
    expect(AppContext.ABTesting.datas).toEqual(abCacheResult);
  });
});
