import { encipherPhone, encipherID } from '../../src/pages/xcar/Util/Encipher';

describe('Encipher', () => {
  test('encipherPhone 测试电话号码加码', () => {
    expect(encipherPhone('')).toEqual('');
    expect(encipherPhone('13610977186')).toEqual('136****7186');
  });

  test('encipherID 测试ID加码', () => {
    expect(encipherID('', '')).toEqual('');
    expect(encipherID('12345678998765432X', '')).toEqual('1234***********32X');
    expect(encipherID('H1989022617', '2')).toEqual('H19***********17');
  });
});
