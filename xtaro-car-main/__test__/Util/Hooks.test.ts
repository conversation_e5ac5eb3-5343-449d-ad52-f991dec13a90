import { buildSelectedFiltersByCode } from '../../src/pages/xcar/Util/Hooks';

describe('buildSelectedFiltersByCode', () => {
  it('should build selected filters correctly', () => {
    const codes = ['code1', 'code2', 'code3'];
    const result = buildSelectedFiltersByCode(codes);
    expect(result).toEqual({
      bitsFilter: ['code1', 'code2', 'code3'],
      filterLabels: [
        {
          code: 'code1',
          name: '', 
          isSelected: true,
        },
        {
          code: 'code2',
          name: '', 
          isSelected: true,
        },
        {
          code: 'code3',
          name: '',
          isSelected: true,
        },
      ],
    });
  });
});