import Log from '@c2x/apis/Log';
import _ from 'lodash';
import { LogKey, Platform } from '../../../src/pages/xcar/Constants/Index';
import AppContext from '../../../src/pages/xcar/Util/AppContext';
import { Utils } from '../../../src/pages/xcar/Util/Index';
import { LogHistory, LogFavorite } from '../../../src/pages/xcar/Util/Log/UserBehavior';

beforeEach(() => {
  jest.spyOn(AppContext, 'UrlQuery', 'get').mockReturnValue({});
});

describe('UserBehavior', () => {
  const logKey = LogKey.c_car_my_history_isd;
  const historyData = {
    type: 'list',
    params: {
      test: '123',
    },
  };
  const info = _.omit(historyData, 'params');
  const result = {
    appUrl: `${
      Platform.CAR_CROSS_URL.LIST.ISD
    }&channelid=${14514}&data=${encodeURIComponent(
      JSON.stringify(historyData.params),
    )}`,
    ChannelID: 14514,
    ...info,
  };

  test('LogHistory 从浏览历史过来的数据', () => {
    jest
      .spyOn(AppContext, 'UrlQuery', 'get')
      .mockReturnValue({ fromurl: 'history' });
    expect(LogHistory('test')).toEqual(undefined);
  });

  test('LogHistory Application.version<8.23.0', () => {
    jest.spyOn(Utils, 'compareVersion').mockReturnValue(-1);
    expect(LogHistory('test')).toEqual(undefined);
  });

  test('LogHistory', () => {
    jest.spyOn(Utils, 'compareVersion').mockReturnValue(1);
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest.spyOn(Log, 'logTrace').mockImplementation(() => {});
    LogHistory(historyData);
    expect(Log.logTrace).toHaveBeenCalledWith(logKey, result);
  });
});
