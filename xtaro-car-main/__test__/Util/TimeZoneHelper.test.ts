import Util from '@c2x/apis/Util';
import { CarStorage } from '../../src/pages/xcar/Util/Index';
import {
  Instance,
  TimeZoneType,
  convertToMap,
  convertToObject,
  isNumber,
  isCacheExpiration,
} from '../../src/pages/xcar/Util/TimeZoneHelper';

let CurStorage = {};
Util.isInChromeDebug = false;
jest.spyOn(CarStorage, 'privateLoadSync').mockImplementation(() => {
  return CurStorage;
});

jest.spyOn(CarStorage, 'save').mockImplementation((key, storage) => {
  CurStorage = JSON.parse(storage);
});

describe('Util TimeZoneHelper convertToMap', () => {
  const storage = {
    '347': {
      cityId: 347,
      timeZone: -7,
      ticks: +new Date() - 86398000,
      type: 'Api',
    },
  };
  const mockMap = [
    {
      storage,
      expected: 1,
    },
    {
      storage: {},
      expected: 0,
    },
    {
      expected: 0,
    },
  ];
  test.each(mockMap)(
    '测试 Util TimeZoneHelper convertToMap',
    async ({ storage, expected }: any) => {
      const result = convertToMap(storage);
      expect(result.size).toEqual(expected);
    },
  );
});

describe('Util TimeZoneHelper convertToObject', () => {
  const storage = {
    '347': {
      cityId: 347,
      timeZone: -7,
      ticks: +new Date() - 86398000,
      type: 'Api',
    },
  };
  const mapObj = new Map();
  mapObj.set('347', storage['347']);
  const mockMap = [
    {
      mapObj,
      expected: JSON.stringify(storage),
    },
    {
      mapObj: new Map(),
      expected: JSON.stringify({}),
    },
    {
      expected: JSON.stringify({}),
    },
  ];
  test.each(mockMap)(
    '测试 Util TimeZoneHelper convertToObject',
    async ({ mapObj, expected }: any) => {
      const result = convertToObject(mapObj);
      expect(JSON.stringify(result)).toEqual(expected);
    },
  );
});

describe('Util TimeZoneHelper isNumber', () => {
  const mockMap = [
    {
      timeZone: 0,
      expected: true,
    },
    {
      timeZone: -3,
      expected: true,
    },
    {
      timeZone: 8,
      expected: true,
    },
    {
      timeZone: null,
      expected: false,
    },
  ];
  test.each(mockMap)(
    '测试 Util TimeZoneHelper isNumber',
    async ({ timeZone, expected }: any) => {
      const result = isNumber(timeZone);
      expect(result).toEqual(expected);
    },
  );
});

describe('Util TimeZoneHelper isCacheExpiration', () => {
  const timeZone1 = {
    cityId: 347,
    timeZone: -7,
    ticks: +new Date() - 86398000,
    type: 'Api',
  };
  const timeZone2 = {
    cityId: 347,
    timeZone: -7,
    ticks: +new Date() - 86400001,
    type: 'Api',
  };
  const mockMap = [
    {
      timeZone: timeZone1,
      expected: false,
    },
    {
      timeZone: timeZone2,
      expected: true,
    },
  ];
  test.each(mockMap)(
    '测试 Util TimeZoneHelper isCacheExpiration',
    async ({ timeZone, expected }: any) => {
      const result = isCacheExpiration(timeZone);
      expect(result).toEqual(expected);
    },
  );
});

describe('Util TimeZoneHelper Instance getCityTimeZone', () => {
  const storage1 = {};
  const storage2 = {
    '347': {
      cityId: 347,
      timeZone: -7,
      ticks: +new Date() - 86398000,
      type: 'Api',
    },
  };
  const mockMap = [
    // 无内存 查找指定城市时区
    {
      storage: JSON.stringify(storage1),
      cityId: 347,
      expected: undefined,
    },
    // 无内存 查找当前城市时区
    {
      storage: JSON.stringify(storage1),
      cityId: null,
      expected: 8,
    },
    // 有内存 查找指定城市时区
    {
      storage: JSON.stringify(storage2),
      cityId: 347,
      expected: -7,
    },
    // 有内存 查找指定城市时区
    {
      storage: JSON.stringify(storage2),
      cityId: null,
      expected: 8,
    },
  ];
  test.each(mockMap)(
    '测试 Util TimeZoneHelper Instance getCityTimeZone',
    ({ storage, cityId, expected }: any) => {
      Instance.reset();
      CurStorage = storage;
      Instance.initial();
      const timeZone = Instance.getCityTimeZone(cityId);
      expect(timeZone?.timeZone).toEqual(expected);
    },
  );
});

describe('Util TimeZoneHelper Instance getTimeZone', () => {
  const storage1 = {};
  const storage2 = {
    '347': {
      cityId: 347,
      timeZone: -7,
      ticks: +new Date() - 86398000,
      type: 'Api',
    },
  };
  const mockMap = [
    // 无storage存储 查找指定城市时区
    {
      storage: JSON.stringify(storage1),
      cityId: 347,
      expected: undefined,
    },
    // 无storage存储 查找当前城市时区
    {
      storage: JSON.stringify(storage1),
      cityId: undefined,
      expected: 8,
    },
    // 有storage存储 查找指定城市时区
    {
      storage: JSON.stringify(storage2),
      cityId: 347,
      expected: -7,
    },
    // 有storage存储 查找指定城市时区
    {
      storage: JSON.stringify(storage2),
      cityId: undefined,
      expected: 8,
    },
  ];
  test.each(mockMap)(
    '测试 Util TimeZoneHelper Instance getTimeZone',
    ({ storage, cityId, expected }) => {
      Instance.reset();
      CurStorage = storage;
      Instance.initial();
      const timeZone = Instance.getTimeZone(cityId);
      expect(timeZone).toEqual(expected);
    },
  );
});

describe('Util TimeZoneHelper Instance initial', () => {
  const storage1 = {};
  const storage2 = {
    '347': {
      cityId: 347,
      timeZone: -7,
      ticks: +new Date() - 86398000,
      type: 'Api',
    },
  };
  const storage3 = {
    '347': {
      cityId: 347,
      timeZone: -7,
      ticks: +new Date() - 86400001,
      type: 'Api',
    },
  };
  const mockMap = [
    // 无storage数据 无初始化城市时区 读取当前城市时区
    {
      storage: JSON.stringify(storage1),
      cityId: undefined,
      initialCityId: undefined,
      expected: 8,
    },
    // 无storage数据 无初始化城市时区 读取洛杉矶时区
    {
      storage: JSON.stringify(storage1),
      cityId: 347,
      initialCityId: undefined,
      expected: undefined,
    },
    // 有storage数据 无初始化城市时区 读取当前城市时区
    {
      storage: JSON.stringify(storage2),
      cityId: undefined,
      initialCityId: undefined,
      expected: 8,
    },
    // 有storage数据 无初始化城市时区 读取洛杉矶时区 且洛杉矶时区数据有效
    {
      storage: JSON.stringify(storage2),
      cityId: 347,
      initialCityId: undefined,
      expected: -7,
    },
    // 有storage数据 无初始化城市时区 读取洛杉矶时区 且洛杉矶时区数据失效
    {
      storage: JSON.stringify(storage3),
      cityId: 347,
      initialCityId: undefined,
      expected: undefined,
    },

    // 无storage数据 有初始化城市时区 读取当前城市时区
    {
      storage: JSON.stringify(storage1),
      cityId: undefined,
      initialCityId: 347,
      expected: 8,
    },
    // 无storage数据 有初始化城市时区 读取洛杉矶时区
    {
      storage: JSON.stringify(storage1),
      cityId: 347,
      initialCityId: 347,
      expected: undefined,
    },
    // 有storage数据 有初始化城市时区 读取当前城市时区
    {
      storage: JSON.stringify(storage2),
      cityId: undefined,
      initialCityId: 347,
      expected: -7,
    },
    // 有storage数据 有初始化城市时区 读取洛杉矶时区 且洛杉矶时区数据有效
    {
      storage: JSON.stringify(storage2),
      cityId: 347,
      initialCityId: 347,
      expected: -7,
    },
    // 有storage数据 有初始化城市时区 读取洛杉矶时区 且洛杉矶时区数据失效
    {
      storage: JSON.stringify(storage3),
      cityId: 347,
      initialCityId: 347,
      expected: undefined,
    },
  ];
  test.each(mockMap)(
    '测试 Util TimeZoneHelper Instance initial',
    ({ storage, cityId, initialCityId, expected }) => {
      Instance.reset();
      CurStorage = storage;
      Instance.initial(initialCityId);
      const timeZone = Instance.getTimeZone(cityId);
      expect(timeZone).toEqual(expected);
    },
  );
});

describe('Util TimeZoneHelper Instance clear', () => {
  const storage2 = {
    '347': {
      cityId: 347,
      timeZone: -7,
      ticks: +new Date() - 86398000,
      type: 'Api',
    },
  };
  const mockMap = [
    // 无storage数据 无初始化城市时区 读取当前城市时区
    {
      storage: JSON.stringify(storage2),
      cityId: undefined,
      initialCityId: undefined,
      beforeExpected: 8,
      afterExpected: 8,
    },
    // 无storage数据 无初始化城市时区 读取洛杉矶时区
    {
      storage: JSON.stringify(storage2),
      cityId: 347,
      initialCityId: undefined,
      beforeExpected: -7,
      afterExpected: undefined,
    },

    // 无storage数据 有初始化城市时区 读取当前城市时区
    {
      storage: JSON.stringify(storage2),
      cityId: undefined,
      initialCityId: 347,
      beforeExpected: -7,
      afterExpected: 8,
    },
    // 无storage数据 有初始化城市时区 读取洛杉矶时区
    {
      storage: JSON.stringify(storage2),
      cityId: 347,
      initialCityId: 347,
      beforeExpected: -7,
      afterExpected: undefined,
    },
  ];
  test.each(mockMap)(
    '测试 Util TimeZoneHelper Instance clear',
    ({ storage, cityId, initialCityId, beforeExpected, afterExpected }) => {
      Instance.reset();
      CurStorage = storage;
      Instance.initial(initialCityId);
      const beforeTimeZone = Instance.getTimeZone(cityId);
      expect(beforeTimeZone).toEqual(beforeExpected);
      Instance.reset();
      const afterTimeZone = Instance.getTimeZone(cityId);
      expect(afterTimeZone).toEqual(afterExpected);
    },
  );
});

describe('Util TimeZoneHelper Instance setTimeZone', () => {
  const storage1 = {};
  const storage2 = {
    '347': {
      cityId: 347,
      timeZone: -7,
      ticks: +new Date() - 86398000,
      type: 'Api',
    },
  };
  const mockMap = [
    // 设置的时区数据 无时区数据时，不更新
    {
      storage: JSON.stringify(storage2),
      cityId: 347,
      cityTimeZone: {
        cityId: 347,
        timeZone: '7',
        type: TimeZoneType.Api,
        ticks: +new Date(),
      },
      expected: -7,
      isCurrent: false,
    },
    // 设置的时区数据 无属于来源类型时，不更新
    {
      storage: JSON.stringify(storage2),
      cityId: 347,
      cityTimeZone: {
        cityId: 347,
        timeZone: 7,
        ticks: +new Date(),
      },
      expected: -7,
      isCurrent: false,
    },
    // 设置的时区数据 时间戳过期时，不更新
    {
      storage: JSON.stringify(storage2),
      cityId: 347,
      cityTimeZone: {
        cityId: 347,
        timeZone: 7,
        type: TimeZoneType.Api,
        ticks: +new Date() - 86400001,
      },
      expected: -7,
      isCurrent: false,
    },
    // 设置的时区数据 时区数据有效
    {
      storage: JSON.stringify(storage2),
      cityId: 347,
      cityTimeZone: {
        cityId: 347,
        timeZone: 7,
        type: TimeZoneType.Api,
        ticks: +new Date(),
      },
      expected: 7,
      isCurrent: false,
    },
    // 设置的时区数据 时区数据有效 未更新了当前时区数据
    {
      storage: JSON.stringify(storage2),
      cityId: null,
      cityTimeZone: {
        cityId: 347,
        timeZone: 7,
        type: TimeZoneType.Api,
        ticks: +new Date(),
      },
      expected: 8,
      isCurrent: false,
    },
    // 设置的时区数据 时区数据有效 且更新了当前时区数据
    {
      storage: JSON.stringify(storage2),
      cityId: null,
      cityTimeZone: {
        cityId: 347,
        timeZone: 7,
        type: TimeZoneType.Api,
        ticks: +new Date(),
      },
      expected: 7,
      isCurrent: true,
    },
    // 设置的时区数据 时区数据有效 同步了storage
    {
      storage: JSON.stringify(storage1),
      cityId: null,
      cityTimeZone: {
        cityId: 347,
        timeZone: 7,
        type: TimeZoneType.Api,
        ticks: +new Date(),
      },
      expected: 7,
      expectedStore: undefined,
      isCurrent: true,
    },
  ];
  CurStorage = {};
  test.each(mockMap)(
    '测试 Util TimeZoneHelper Instance setTimeZone',
    async ({
      storage,
      cityTimeZone,
      cityId,
      isCurrent,
      expected,
      expectedStore,
    }: any) => {
      Instance.reset();
      CurStorage = storage;
      Instance.initial();
      await Instance.setTimeZone(cityTimeZone, isCurrent);
      const timeZone = Instance.getTimeZone(cityId);
      expect(timeZone).toEqual(expected);
      if (expectedStore) {
        expect(CurStorage[cityTimeZone?.cityId]?.timeZone).toEqual(
          expectedStore,
        );
      }
    },
  );
});
