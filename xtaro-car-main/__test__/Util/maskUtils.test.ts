import maskUtils from '../../src/pages/xcar/Util/maskUtils';

describe('maskUtils', () => {
  test('isEmail', () => {
    expect(maskUtils.isEmail('<EMAIL>')).toBeTruthy();
    expect(maskUtils.isEmail('<EMAIL>')).toBeTruthy();
    expect(maskUtils.isEmail('<EMAIL>')).toBeTruthy();
    expect(maskUtils.isEmail('test@.com')).toBeFalsy();
    expect(maskUtils.isEmail('abc.@com')).toBeFalsy();
    expect(maskUtils.isEmail('test0.0com')).toBeFalsy();
  });

  test('maskMobile', () => {
    expect(maskUtils.maskMobile('13817990000')).toEqual('138****0000');
    expect(maskUtils.maskMobile('138179900001111')).toEqual('1381799****1111');
    expect(maskUtils.maskMobile('0563-817332')).toEqual('056****7332');
  });

  test('maskRange', () => {
    expect(maskUtils.maskRange('13817990000', 2, 5)).toEqual('138**990000');
    expect(maskUtils.maskRange('138179900001111', 5, 5)).toEqual(
      '138179**0001111',
    );
    expect(maskUtils.maskRange('0563-817332', 0, 6)).toEqual('05***817332');
    expect(maskUtils.maskRange('0563-817332', 0, 2)).toEqual('0563-817332');
  });

  test('doMask', () => {
    expect(maskUtils.doMask('13817990000', 2, 5)).toEqual('13*****0000');
    expect(maskUtils.doMask('13817990000', 1, 8)).toEqual('1********00');
    expect(maskUtils.doMask('13817990000', 0, 20)).toEqual(
      '********************',
    );
  });

  test('maskEmail', () => {
    expect(maskUtils.maskEmail('<EMAIL>')).toEqual('t**<EMAIL>');
    expect(maskUtils.maskEmail('<EMAIL>')).toEqual(
      '138****<EMAIL>',
    );
    expect(maskUtils.maskEmail('<EMAIL>')).toEqual('<EMAIL>');
  });
});
