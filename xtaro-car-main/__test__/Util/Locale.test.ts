import Locale from '../../src/pages/xcar/Util/Locale';

describe('Locale', () => {
  const locale = new Locale('en-US');

  test('getStandardLanguage', () => {
    expect(locale.getStandardLanguage()).toEqual('en');
  });

  test('getStandardRegion', () => {
    expect(locale.getStandardRegion()).toEqual('US');
  });

  test('formatLocale', () => {
    expect(locale.formatLocale()).toEqual('en-US');
  });

  test('getSite', () => {
    expect(locale.getSite()).toEqual('us');
  });

  test('getLocale', () => {
    expect(locale.getLocale()).toEqual('en_us');
  });

  test('getLanguage', () => {
    expect(locale.getLanguage()).toEqual('EN');
  });
});
