import {
  getListServerAbversion,
  getServerAbversions,
  getResAbversion,
  getAbBoolean,
  isRecommend,
  AbTestingKey,
} from '../../src/pages/xcar/Util/ServerABTesting';
import { ListReqAndResData } from '../../src/pages/xcar/Global/Cache/Index';

describe('测试 ServerABTesting getListServerAbversion', () => {
  const mockMap = [
    {
      params: {
        extras: {
          abVersion: '220126_DSJT_lqd|B,220323_DSJT_rank2|B,220901_DSJT_RECV|B'
        },
      },
      expected: '220126_DSJT_lqd|B,220323_DSJT_rank2|B,220901_DSJT_RECV|B',
    },
    {
      params: null,
      expected: '220126_DSJT_test|B,223426_DSJT_mock|A',
    },
  ];
  test.each(mockMap)(
    'getListServerAbversion check',
    ({ params, expected }) => {
      jest.spyOn(ListReqAndResData, "getData").mockImplementation(() => ({
        extras: {
          abVersion: '220126_DSJT_test|B,223426_DSJT_mock|A'
        },
      }));
      const data = getListServerAbversion(params);
      expect(data).toEqual(expected);
    },
  );
});

describe('测试 ServerABTesting getServerAbversions', () => {
  const mockMap = [
    {
      params: {
        extras: {
          abVersion: '220126_DSJT_lqd|B,220323_DSJT_rank2|B,220901_DSJT_RECV|B'
        },
      },
      expected: ['220126_DSJT_lqd|B', '220323_DSJT_rank2|B', '220901_DSJT_RECV|B'],
    },
    {
      params: null,
      expected: ['220126_DSJT_test|B', '223426_DSJT_mock|A'],
    },
  ];
  test.each(mockMap)(
    'getServerAbversions check',
    ({ params, expected }) => {
      jest.spyOn(ListReqAndResData, "getData").mockImplementation(() => ({
        extras: {
          abVersion: '220126_DSJT_test|B,223426_DSJT_mock|A'
        },
      }));
      const data = getServerAbversions(params);
      expect(data).toEqual(expected);
    },
  );
});

describe('测试 ServerABTesting getResAbversion', () => {
  const mockMap = [
    {
      params: {
        extras: {
          abVersion: '220126_DSJT_lqd|B,220323_DSJT_rank2|B,220901_DSJT_RECV|B'
        },
      },
      expected: '220126_DSJT_lqd|B,220323_DSJT_rank2|B,220901_DSJT_RECV|B',
    },
    {
      params: null,
      expected: '220126_DSJT_test|B,223426_DSJT_mock|A',
    },
  ];
  test.each(mockMap)(
    'getResAbversion check',
    ({ params, expected }) => {
      jest.spyOn(ListReqAndResData, "getData").mockImplementation(() => ({
        extras: {
          abVersion: '220126_DSJT_test|B,223426_DSJT_mock|A'
        },
      }));
      const data = getResAbversion(params);
      expect(data).toEqual(expected);
    },
  );
});

describe('测试 ServerABTesting getAbBoolean', () => {
  const mockMap = [
    {
      params: {
        extras: {
          abVersion: '230104_DSJT_fil10|B,220323_DSJT_rank2|B,220901_DSJT_RECV|B'
        },
      },
      expected: true,
    },
    {
      params: null,
      expected: false,
    },
  ];
  test.each(mockMap)(
    'getAbBoolean check',
    ({ params, expected }) => {
      jest.spyOn(ListReqAndResData, "getData").mockImplementation(() => ({
        extras: {
          abVersion: '220126_DSJT_test|B,223426_DSJT_mock|A'
        },
      }));
      const data = getAbBoolean(AbTestingKey.IsFilteredRecommend, params);
      expect(data).toEqual(expected);
    },
  );
});

describe('测试 ServerABTesting isRecommend', () => {
  const mockMap = [
    {
      params: {
        extras: {
          hasListSign: "ddfdgdgfgfh44516Nwa"
        },
      },
      expected: true,
    },
    {
      params: {},
      expected: false,
    },
    {
      params: null,
      expected: false,
    },
  ];
  test.each(mockMap)(
    'isRecommend check',
    ({ params, expected }) => {
      jest.spyOn(ListReqAndResData, "getData").mockImplementation(() => ({
        extras: {
          abVersion: '220126_DSJT_test|B,223426_DSJT_mock|A'
        },
      }));
      const data = isRecommend(params);
      expect(data).toEqual(expected);
    },
  );
});