import { APP_TYPE } from '../../src/pages/xcar/Constants/Platform';
import { AppContext, Utils } from '../../src/pages/xcar/Util/Index';
import ShareUtil from '../../src/pages/xcar/Util/ShareUtil';

describe('ShareUtil', () => {
  test('测试 getShareChannelId Isd环境', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    jest.spyOn(Utils, 'getCurPageId').mockImplementation(() => '10650041522');
    expect(ShareUtil.getShareChannelId()).toEqual('235489');
  });

  test('测试 getShareChannelId Osd环境', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    jest.spyOn(Utils, 'getCurPageId').mockImplementation(() => '10650033663');
    expect(ShareUtil.getShareChannelId()).toEqual('235489');
  });

  test('测试 getShareChannelId 不是产品页面', () => {
    jest.spyOn(Utils, 'getCurPageId').mockImplementation(() => '10650039479');
    expect(ShareUtil.getShareChannelId()).toEqual('');
  });
});
