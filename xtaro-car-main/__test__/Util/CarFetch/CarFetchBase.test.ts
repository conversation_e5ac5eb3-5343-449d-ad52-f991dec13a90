import { ApiResCode } from '../../../src/pages/xcar/Constants/Index';
import Utils from '../../../src/pages/xcar/Util/Utils';

describe('getFrontEndExpCode', () => {
  const mocks = [{
    response: '',
    error: {},
    expected: ApiResCode.TraceCode.E1001,
  }, {
    response: '',
    error: { "cancelTimeout": true },
    expected: ApiResCode.TraceCode.E1004,
  }, {
    response: '',
    error: { "code": "-1", "message": "load error:ctrip.android.httpv2.CTHTTPException: 网络请求超时,超过设定timeout(-110), statusCode:0", "userInfo": {}, "nativeStackAndroid": [{ "class": "ctrip.android.httpv2.b$d", "methodName": "onFailure", "file": "SourceFile", "lineNumber": 4 }, { "class": "ctrip.android.http.CtripHTTPClientV2$d$a", "methodName": "run", "file": "SourceFile", "lineNumber": 17 }, { "class": "java.util.concurrent.ThreadPoolExecutor", "methodName": "runWorker", "file": "ThreadPoolExecutor.java", "lineNumber": 1167 }, { "class": "java.util.concurrent.ThreadPoolExecutor$Worker", "methodName": "run", "file": "ThreadPoolExecutor.java", "lineNumber": 641 }, { "class": "java.lang.Thread", "methodName": "run", "file": "Thread.java", "lineNumber": 920 }], "_promise_error_ignore": true },
    expected: ApiResCode.TraceCode.E1004,
  }, {
    response: '',
    error: { "code": "-1", "message": "load error:Certificate validation failed for m.ctrip.com, statusCode:0", "userInfo": {}, "nativeStackAndroid": [{ "class": "ctrip.android.httpv2.b$d", "methodName": "onFailure", "file": "SourceFile", "lineNumber": 4 }, { "class": "ctrip.android.http.CtripHTTPClientV2$d$a", "methodName": "run", "file": "SourceFile", "lineNumber": 17 }, { "class": "java.util.concurrent.ThreadPoolExecutor", "methodName": "runWorker", "file": "ThreadPoolExecutor.java", "lineNumber": 1167 }, { "class": "java.util.concurrent.ThreadPoolExecutor$Worker", "methodName": "run", "file": "ThreadPoolExecutor.java", "lineNumber": 641 }, { "class": "java.lang.Thread", "methodName": "run", "file": "Thread.java", "lineNumber": 933 }], "_promise_error_ignore": true },
    expected: ApiResCode.TraceCode.E1004,
  }, {
    response: '',
    error: { "code": "90002", "message": "未能完成操作。（CTHttpToSotpPipe错误90002。）", "nativeStackIOS": ["0   CTRIP_WIRELESS                      0x00000001054de28c _ZN8facebook5react11JSIExecutor21defaultTimeoutInvokerERKNSt3__18functionIFvvEEENS3_IFNS2_12basic_stringIcNS2_11char_traitsIcEENS2_9allocatorIcEEEEvEEE + 333892", "1   CTRIP_WIRELESS                      0x0000000105679508 _ZN8facebook5react38concreteComponentDescriptorConstructorINS0_23ViewComponentDescriptorEEENSt3__110unique_ptrIKNS0_19ComponentDescriptorENS3_14default_deleteIS6_EEEERKNS0_29ComponentDescriptorParametersE + 395180", "2   CTRIP_WIRELESS                      0x00000001072a3948 _ZN15CTXAppidConvert13GetAppIdTableEv + 5239144", "3   libdispatch.dylib                   0x000000019593ee6c E3EA4F63-5D11-342A-AF19-9F58DBC8E259 + 7788", "4   libdispatch.dylib                   0x0000000195940a30 E3EA4F63-5D11-342A-AF19-9F58DBC8E259 + 14896", "5   libdispatch.dylib                   0x0000000195943b44 E3EA4F63-5D11-342A-AF19-9F58DBC8E259 + 27460", "6   libdispatch.dylib                   0x0000000195952164 E3EA4F63-5D11-342A-AF19-9F58DBC8E259 + 86372", "7   libdispatch.dylib                   0x000000019595296c E3EA4F63-5D11-342A-AF19-9F58DBC8E259 + 88428", "8   libsystem_pthread.dylib             0x000000020763d080 _pthread_wqthread + 228", "9   libsystem_pthread.dylib             0x000000020763ce5c start_wqthread + 8"], "domain": "CTHttpToSotpPipe", "userInfo": { "taskFailType": 213 }, "_promise_error_ignore": true },
    expected: ApiResCode.TraceCode.E1004,
  }];
  test.each(mocks)('测试 ExpCode', ({ response, error, expected }) => {
    const data = Utils.getFrontEndExpCode(response, error);
    expect(data).toEqual(expected);
  })
})

