import {
  graphqlFetch,
  getGraphqlServerUrl,
  domains,
  graphqlPath as path,
  setVariablesBaseRequest,
} from '../../../src/pages/xcar/Util/CarFetch/GraphqlFetch';
import { getBaseRequestParam } from '../../../src/pages/xcar/Util/CarFetch/CarFetchBase';
import { AppContext, Utils } from '../../../src/pages/xcar/Util/Index';
import { APP_TYPE } from '../../../src/pages/xcar/Constants/Platform';
import * as GraphqlClient from '@ctrip/graphql-client';
const mockObj = {};


describe('GraphqlFetch.test.ts', () => {
  test('getGraphqlServerUrl 返回正确', async () => {
    const result = await getGraphqlServerUrl('fat');
    expect(result).toEqual(domains.fat + path);
  });
  test('getGraphqlServerUrl 返回正确', async () => {
    AppContext.setCarEnv({ env: 'fat', appType: APP_TYPE.ISD_C_APP });
    const result = await getGraphqlServerUrl();
    expect(result).toEqual(domains.fat + path);
  });
  test('getGraphqlServerUrl 返回正确', async () => {
    jest.spyOn(Utils, 'getEnvType').mockReturnValueOnce(Promise.resolve('fat'));
    const result = await getGraphqlServerUrl();
    expect(result).toEqual(domains.fat + path);
  });
  test.skip('setVariablesBaseRequest 返回正确', async () => {
    const result = await setVariablesBaseRequest({ demo: {} });
    expect(result).toEqual({ demo: { baseRequest: {} } });
  });
  test('setVariablesBaseRequest 返回正确', async () => {
    const result = await setVariablesBaseRequest({});
    expect(result).toEqual({});
  });
  jest
    .spyOn(GraphqlClient, 'load')
    .mockImplementation(() => Promise.resolve({}));
});
