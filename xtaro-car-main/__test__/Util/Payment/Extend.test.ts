import dayjs from '../../../src/pages/xcar/Common/src/Dayjs/src';
import { BbkUtils } from '../../../src/pages/xcar/Common/src/Utils';
import { getOTimeOutInterval } from '../../../src/pages/xcar/Util/Index';
import { AppContext, Utils } from '../../../src/pages/xcar/Util/Index';
import getExtend from '../../../src/pages/xcar/Util/Payment/Extend';

describe('Pay/Extend getOTimeOutInterval', () => {
  const mockIsdData = [
    {
      mockParams: {
        orderBaseInfo: {
          lastEnablePayTime: dayjs().add(15, 'm').startOf('minute').valueOf(),
        },
      },
      expected: 900,
    },
    {
      mockParams: {},
      expected: 0,
    },
  ];
  const mockOsdData = [
    {
      mockParams: {
        remainSeconds: 500,
      },
      expected: 500,
    },
    {
      mockParams: {
        continuePayInfo: {
          leftMinutes: 14,
          leftSeconds: 30,
        },
      },
      expected: 870,
    },
  ];
  test.each(mockIsdData)(
    'getOTimeOutInterval Isd',
    ({ mockParams, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const result = getOTimeOutInterval(mockParams);
      expect(result).toEqual(expected);
    },
  );
  test.each(mockOsdData)(
    'getOTimeOutInterval Osd',
    ({ mockParams, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const result = getOTimeOutInterval(mockParams);
      expect(result).toEqual(expected);
    },
  );
});

describe('Pay/Extend getExtend', () => {
  const getCTripExtendParams = {
    title: 'testTitle',
    ptime: '20211217100000',
    rtime: '20211218100000',
    pickupLocation: {
      name: '凤凰国际机场T1航站楼',
      latitude: 18.30747,
      longitude: 109.41201,
      cityId: 43,
      cityName: '三亚',
      areaType: 1,
    },
    returnLocation: {
      name: '凤凰国际机场T1航站楼',
      latitude: 18.30747,
      longitude: 109.41201,
      cityId: 43,
      cityName: '三亚',
      areaType: 1,
    },
    payremindTime: '',
  };
  const getCTripExtendResult = {
    useEType: 1,
    titletype: 3,
    subPayType: 0,
    isRealTimePay: 1,
    payremind: [
      { text: '请于', isred: false },
      { text: dayjs().add(1800, 'seconds').format('HH:mm'), isred: true },
      { text: '前完成付款确保订单成功提交', isred: false },
    ],
  };
  const insuranceinfos = [
    {
      provider: 1, // 用车产品目前都走的是携程代保
      amount: 1,
      currency: 'CNY',
    },
  ];
  const mockIsdData = [
    {
      mockParams: {
        isOnlyAlipay: true,
        insExtend: {
          insuranceinfos,
        },
        hideOrderPaySummary: true,
      },
      expected: {
        payTypeList: 4,
        subPayTypeList: 128,
        insuranceinfos,
        ...getCTripExtendResult,
        customtitle: [
          {
            title: '修改订单',
          },
        ],
      },
    },
    {
      mockParams: {
        isNotWeChatPay: true,
        insExtend: {},
        hideOrderPaySummary: true,
        title: getCTripExtendParams.title,
        payremindTime: getCTripExtendParams.payremindTime,
      },
      expected: {
        payTypeList: undefined,
        subPayTypeList: 1279,
        customtitle: [
          {
            title: getCTripExtendParams.title,
          },
        ],
        ...getCTripExtendResult,
      },
    },
    {
      mockParams: {
        isNotWeChatPay: true,
        insExtend: {},
        hideOrderPaySummary: false,
        ...getCTripExtendParams,
        payAmountInfo: {
          ctripInsuranceAmountInfos: [
            {
              productId: '1',
              payAmount: 10,
            },
            {
              productId: '2',
              payAmount: 10,
            },
          ],
        },
      },
      expected: {
        payTypeList: undefined,
        subPayTypeList: 1279,
        insuranceinfos: [
          {
            provider: 1, // 用车产品目前都走的是携程代保
            amount: 20,
            currency: 'CNY',
          },
        ],
        ...getCTripExtendResult,
        customtitle: [
          {
            carmodel: getCTripExtendParams.title,
            takecartime: dayjs(getCTripExtendParams.ptime).format(
              'MM-DD HH:mm',
            ),
            takecaraddress: getCTripExtendParams.pickupLocation,
            recartime: dayjs(getCTripExtendParams.rtime).format('MM-DD HH:mm'),
            recaraddress: getCTripExtendParams.returnLocation,
            useduration: `${BbkUtils.isd_dhm(
              getCTripExtendParams.ptime,
              getCTripExtendParams.rtime,
            )}`,
          },
        ],
      },
    },
  ];
  const mockOsdData = [
    {
      mockParams: {
        isOnlyAlipay: true,
        hideOrderPaySummary: true,
        insExtend: {
          insuranceinfos,
        },
      },
      expected: {
        payTypeList: 4,
        subPayTypeList: 128,
        insuranceinfos,
        ...getCTripExtendResult,
        customtitle: [
          {
            title: '修改订单',
          },
        ],
      },
    },
    {
      mockParams: {
        isNotWeChatPay: true,
        hideOrderPaySummary: true,
        insExtend: {},
      },
      expected: {
        payTypeList: undefined,
        subPayTypeList: 1279,
        insuranceinfos: [],
        ...getCTripExtendResult,
        customtitle: [
          {
            title: '修改订单',
          },
        ],
      },
    },
    {
      mockParams: {
        isNotWeChatPay: true,
        hideOrderPaySummary: true,
        insExtend: {},
        payAmountInfo: {
          ctripInsuranceAmountInfos: [
            {
              productId: '1',
              payAmount: 10,
            },
            {
              productId: '2',
              payAmount: 10,
            },
          ],
        },
      },
      expected: {
        payTypeList: undefined,
        subPayTypeList: 1279,
        insuranceinfos: [
          {
            provider: 1, // 用车产品目前都走的是携程代保
            amount: 20,
            currency: 'CNY',
          },
        ],
        ...getCTripExtendResult,
        customtitle: [
          {
            title: '修改订单',
          },
        ],
      },
    },
  ];
  test.each(mockIsdData)('getExtend Isd', ({ mockParams, expected }) => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(false);
    const result = getExtend(mockParams);
    expect(result).toEqual(expected);
  });
  test.each(mockOsdData)('getExtend Osd', ({ mockParams, expected }) => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const result = getExtend(mockParams);
    expect(result).toEqual(expected);
  });
});
