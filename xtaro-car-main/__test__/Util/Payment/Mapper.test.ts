import { PayTraceType } from '../../../src/pages/xcar/Types/PaymentType';
import { getPayTraceTypeText } from '../../../src/pages/xcar/Util/Payment/Mapper';

describe('Payment/Mapper', () => {
  const testData = [
    {
      value: 'regularPay2',
      exepcted: '老版常规支付',
    },
    {
      value: 'fastPay',
      exepcted: '老版快捷支付',
    },
    {
      value: 'thirdPayByServer',
      exepcted: '支付宝授权',
    },
    {
      value: PayTraceType.middlePayStart,
      exepcted: '准备唤起中台支付',
    },
    {
      value: PayTraceType.middlePayNotSupport,
      exepcted: '暂不支持中台支付，唤起旧版本支付',
    },

    {
      value: PayTraceType.middlePayGetPayInfo,
      exepcted: '请求服务获取支付Token',
    },
    {
      value: PayTraceType.middlePayCatchError,
      exepcted: '支付异常',
    },
    {
      value: PayTraceType.middlePayCallback,
      exepcted: '支付回调',
    },
  ];
  test.each(testData)('getPayTraceTypeText', ({ value, exepcted }) => {
    expect(getPayTraceTypeText(value)).toEqual(exepcted);
  });
});
