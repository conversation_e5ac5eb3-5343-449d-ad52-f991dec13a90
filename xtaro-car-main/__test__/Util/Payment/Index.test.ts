import Pay from '@c2x/apis/Pay';;
import { PayScene } from '../../../src/pages/xcar/Constants/PayEnums';
import {
  MiddlePay,
  getOriginPayApiByScene,
  PaymentByServer,
  ThirdPayByServer
} from '../../../src/pages/xcar/Util/Payment/Index';
import { CarFetch, FastPaymentByServer, CarLog } from '../../../src/pages/xcar/Util/Index';

afterEach(() => {
  jest.clearAllMocks();
})

describe('MiddlePay', () => {
  const payParams = {
    orderId: 21416398081,
    subtitle: "懒人行租车(携程优选)",
    vendorId: "30164",
    currency: "CNY",
    amount: 391,
    payName: "在线预付",
    ptime: "2022-12-04 10:00:00",
    rtime: "2022-12-12 10:00:00",
    pickupLocation: "凤凰国际机场T1航站楼",
    returnLocation: "凤凰国际机场T1航站楼",
    driver: {
      cellPhone: "***********",
      flightNo: "",
      areaCode: "86",
      secondName: "DAN",
      firstName: "LU",
      idtype: "1",
      idnumber: 610112199506300525,
      name: "鲁丹",
      age: 27
    },
    payAmountInfo: {},
    requestId: "404362a5-088f-453c-84b9-766f2212b246",
    isHertzPrepay: false,
    freeCancel: "",
    chargesInfos: [],
    insExtend: {},
    isModifyOrder: true,
    businessType: 1,
    businessId: "362125",
    hideOrderPaySummary: true,
    titletype: 0,
    payType: 1
  };

  jest.spyOn(CarFetch, 'getPayToken').mockReturnValue(Promise.resolve(
    {
      baseResponse: {
        isSuccess: true,
        code: "0",
      },
      resInfo: {
        payToken: "1045431668377776128",
        payLink: "/rn_payment_middle_plat/_crn_config?CRNModuleName=rn_payment_middle_plat&CRNType=1&initialPage=fastPay&isHideNavBar=YES&isTransparentBg=YES&isTransparentBgWithNav=YES&payToken=1045431668377776128",
        merchantId: "50000006",
        orderId: ***********,
        payRequestId: "20221124201212AZ1219383711425472627712"
      },
      isNew: true,
    }));

  test('支付冒烟: status 0', async () => {

    // 正常支付 status 0

    jest.spyOn(Pay, 'middlePay').mockImplementation((params, callBack) => {
      callBack({ status: 0 }, {
        outTradeNo: "***********",
        orderId: "***********",
        resultStatus: 1,
        seqID: "1669289732.991252",
        extend: "",
        tradeNo: "20221124193533TP0486383702199039820800",
        payAmount: "1710.00"
      });
    })
    expect(await MiddlePay({ params: payParams, scene: PayScene.BookingCreateOrder })).toEqual({
      isNew: true,
      payInfo: {
        payToken: "1045431668377776128",
        payLink: "/rn_payment_middle_plat/_crn_config?CRNModuleName=rn_payment_middle_plat&CRNType=1&initialPage=fastPay&isHideNavBar=YES&isTransparentBg=YES&isTransparentBgWithNav=YES&payToken=1045431668377776128",
        merchantId: "50000006",
        orderId: ***********,
        requestId: "20221124201212AZ1219383711425472627712",
      },
      result: {
        extend: "",
        orderId: "***********",
        outTradeNo: "***********",
        payAmount: "1710.00",
        resultStatus: 1,
        seqID: "1669289732.991252",
        tradeNo: "20221124193533TP0486383702199039820800",
      },
      payRequestId: "20221124201212AZ1219383711425472627712",
      success: true,
      showError: false,
      status: {
        status: 0,
      }
    });
  })

  test('支付冒烟: status 1', async () => {
    // 正常支付 status 1
    jest.spyOn(Pay, 'middlePay').mockImplementation((params, callBack) => {
      callBack({ status: 1 }, {
        outTradeNo: "***********",
        orderId: "***********",
        resultStatus: 1,
        seqID: "1669289732.991252",
        extend: "",
        tradeNo: "20221124193533TP0486383702199039820800",
        payAmount: "1710.00"
      });
    })

    expect(await MiddlePay({
      params: payParams,
      scene: PayScene.BookingCreateOrder
    })).toEqual({
      isNew: true,
      payInfo: {
        payToken: "1045431668377776128",
        payLink: "/rn_payment_middle_plat/_crn_config?CRNModuleName=rn_payment_middle_plat&CRNType=1&initialPage=fastPay&isHideNavBar=YES&isTransparentBg=YES&isTransparentBgWithNav=YES&payToken=1045431668377776128",
        merchantId: "50000006",
        orderId: ***********,
        requestId: "20221124201212AZ1219383711425472627712",
      },
      result: {
        extend: "",
        orderId: "***********",
        outTradeNo: "***********",
        payAmount: "1710.00",
        resultStatus: 1,
        seqID: "1669289732.991252",
        tradeNo: "20221124193533TP0486383702199039820800",
      },
      payRequestId: "20221124201212AZ1219383711425472627712",
      success: true,
      showError: false,
      status: {
        status: 1,
      }
    });
  })

  test('支付冒烟: 支付SdK 异常', async () => {
    jest.spyOn(Pay, 'middlePay').mockImplementation((params, callBack) => {
      throw new Error('error')
    });

    expect(await MiddlePay({
      params: payParams,
      scene: PayScene.BookingCreateOrder
    })).toEqual({
      success: false,
      showError: true,
      status: null,
      result: null,
    });
  })

  test('支付冒烟: 服务接口异常', async () => {
    jest.spyOn(CarFetch, 'getPayToken').mockImplementation(() => {
      throw new Error();
    });
    jest.spyOn(CarLog, 'LogDevError')
    expect(await MiddlePay({
      params: payParams,
      scene: PayScene.BookingCreateOrder
    })).toEqual({
      success: false,
      showError: true,
      status: null,
      result: null,
    });
  })
})




describe('getOriginPayApiByScene', () => {
  const mockMap = [{
    scene: PayScene.BookingCreateOrder,
    exepcted: PaymentByServer,
  }, {
    scene: PayScene.BookingCreditRentAuth,
    exepcted: FastPaymentByServer,
  }, {
    scene: PayScene.SesameAuth,
    exepcted: ThirdPayByServer,
  }]
  test.each(mockMap)('%p', ({ scene, exepcted }) => {
    expect(getOriginPayApiByScene(scene)).toEqual(exepcted);
  });
})

