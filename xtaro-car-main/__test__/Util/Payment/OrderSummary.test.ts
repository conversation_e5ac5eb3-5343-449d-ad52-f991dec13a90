import {
  getCardStr,
  getIsdOrderSummary,
  getOsdOrderSummary,
} from '../../../src/pages/xcar/Util/Payment/OrderSummary';

describe('getCardStr', () => {
  const mocksMap = [{
    id: '1',
    expected: '身份证',
  }, {
    id: '2',
    expected: '护照',
  }, {
    id: '3',
    expected: '身份证',
  }, {
    id: '7',
    expected: '回乡证',
  }, {
    id: '8',
    expected: '台胞证',
  }]
  test.each(mocksMap)('%p', ({ id, expected }) => {
    expect(getCardStr(id)).toEqual(expected);
  })
})

describe('getIsdOrderSummary', () => {
  const mocksMap = [{
    params: {
      chargesInfos: [
        {
          title: '租车基本费用',
          subTitle: '',
          description: '',
          code: '1001',
          size: '¥488×2天',
          currencyCode: '¥',
          currenctDailyPrice: 488,
          currentTotalPrice: 976,
          sortNum: 1,
        },
        {
          title: '基础服务费',
          description: '',
          code: '1002',
          currencyCode: '¥',
          currenctDailyPrice: 85,
          currentTotalPrice: 170,
          sortNum: 100,
        },
        {
          title: '车行手续费',
          description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
          code: '1003',
          currencyCode: '¥',
          currenctDailyPrice: 35,
          currentTotalPrice: 35,
          sortNum: 100,
        },
      ],
      driver: {
        cellPhone: '15800000000',
        certificateType: 1,
        name: '张三',
        certificateNumber: '310111199208080000',
      },
      bizMode: 0,
      hideOrderPaySummary: false,
    },
    expected: [
      {
        KOrderDetailMainTitle: "费用明细",
        KOrderDetailMinor: [
          {
            KOrderDetailMinorTitle: "租车基本费用",
            KOrderDetailMinorValue: "¥976"
          },
          {
            KOrderDetailMinorTitle: "基础服务费",
            KOrderDetailMinorValue: "¥170"
          },
          {
            KOrderDetailMinorTitle: "车行手续费",
            KOrderDetailMinorValue: "¥35"
          },
        ],
      },
      {
        KOrderDetailMainComment: "身份证 \n手机号 158****0000",
        KOrderDetailMainTitle: '驾驶员',
        KOrderDetailMainValue: '张三',
      }
    ],
  }, {
    params: {
      chargesInfos: [
        {
          title: '租车基本费用',
          subTitle: '',
          description: '',
          code: '1001',
          size: '¥488×2天',
          currencyCode: '¥',
          currenctDailyPrice: 488,
          currentTotalPrice: 976,
          sortNum: 1,
        },
        {
          title: '基础服务费',
          description: '',
          code: '1002',
          currencyCode: '¥',
          currenctDailyPrice: 85,
          currentTotalPrice: 170,
          sortNum: 100,
        },
        {
          title: '车行手续费',
          description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
          code: '1003',
          currencyCode: '¥',
          currenctDailyPrice: 35,
          currentTotalPrice: 35,
          sortNum: 100,
        },
      ],
      driver: {
        cellPhone: '15800000000',
        certificateType: 1,
        name: '张三',
        certificateNumber: '310111199208080000',
      },
      bizMode: 1,
      hideOrderPaySummary: false,
    },
    expected: undefined,
  }, {
    params: {
      chargesInfos: [
        {
          title: '租车基本费用',
          subTitle: '',
          description: '',
          code: '1001',
          size: '¥488×2天',
          currencyCode: '¥',
          currenctDailyPrice: 488,
          currentTotalPrice: 976,
          sortNum: 1,
        },
        {
          title: '基础服务费',
          description: '',
          code: '1002',
          currencyCode: '¥',
          currenctDailyPrice: 85,
          currentTotalPrice: 170,
          sortNum: 100,
        },
        {
          title: '车行手续费',
          description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
          code: '1003',
          currencyCode: '¥',
          currenctDailyPrice: 35,
          currentTotalPrice: 35,
          sortNum: 100,
        },
      ],
      driver: {
        certificateType: 1,
        name: '张三',
        certificateNumber: '310111199208080000',
      },
      bizMode: 0,
      hideOrderPaySummary: true,
    },
    expected: undefined,
  }]
  test.each(mocksMap)('国内费用明细', ({ params, expected }) => {
    expect(getIsdOrderSummary(params).KOrderDetail).toEqual(expected);
  })
})



describe('getOsdOrderSummary', () => {
  const mocksMap = [{
    params: {
      chargesInfos: [
        {
          title: '租车基本费用',
          subTitle: '',
          description: '',
          code: '1001',
          size: '¥488×2天',
          currencyCode: '¥',
          currenctDailyPrice: 488,
          currentTotalPrice: 976,
          sortNum: 1,
        },
        {
          title: '基础服务费',
          description: '',
          code: '1002',
          currencyCode: '¥',
          currenctDailyPrice: 85,
          currentTotalPrice: 170,
          sortNum: 100,
        },
        {
          title: '车行手续费',
          description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
          code: '1003',
          currencyCode: '¥',
          currenctDailyPrice: 35,
          currentTotalPrice: 35,
          sortNum: 100,
        },
      ],
      driver: {
        cellPhone: '15800000000',
        certificateType: 1,
        name: '张三',
        certificateNumber: '310111199208080000',
        email: '<EMAIL>',
        flightNo: 'xxxxx',
        wechat: 'xxxxxxxx'
      },
    },
    expected: [
      {
        KOrderDetailMainTitle: "费用明细",
        KOrderDetailMinor: [
          {
            KOrderDetailMinorTitle: "租车基本费用",
            KOrderDetailMinorValue: "¥976"
          },
          {
            KOrderDetailMinorTitle: "基础服务费",
            KOrderDetailMinorValue: "¥170"
          },
          {
            KOrderDetailMinorTitle: "车行手续费",
            KOrderDetailMinorValue: "¥35"
          },
        ],
      },
      {
        KOrderDetailMainComment: "手机号 15800000000\n邮箱 <EMAIL>",
        KOrderDetailMainTitle: "驾驶员",
        KOrderDetailMainValue: "张三",
      },
      {
        KOrderDetailMainTitle: "航班号",
        KOrderDetailMainValue: "xxxxx",
      },
      {
        KOrderDetailMainTitle: "微信号",
        KOrderDetailMainValue: "xxxxxxxx",
      },
    ],
  }, {
    params: {
      chargesInfos: [
        {
          title: '租车基本费用',
          subTitle: '',
          description: '',
          code: '1001',
          size: '¥488×2天',
          currencyCode: '¥',
          currenctDailyPrice: 488,
          currentTotalPrice: 976,
          sortNum: 1,
        },
        {
          title: '基础服务费',
          description: '',
          code: '1002',
          currencyCode: '¥',
          currenctDailyPrice: 85,
          currentTotalPrice: 170,
          sortNum: 100,
        },
        {
          title: '车行手续费',
          description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
          code: '1003',
          currencyCode: '¥',
          currenctDailyPrice: 35,
          currentTotalPrice: 35,
          sortNum: 100,
        },
      ],
      driver: {
        cellPhone: '15800000000',
        certificateType: 1,
        name: '张三',
        certificateNumber: '310111199208080000',
      },
    },
    expected: [
      {
        KOrderDetailMainTitle: "费用明细",
        KOrderDetailMinor: [
          {
            KOrderDetailMinorTitle: "租车基本费用",
            KOrderDetailMinorValue: "¥976"
          },
          {
            KOrderDetailMinorTitle: "基础服务费",
            KOrderDetailMinorValue: "¥170"
          },
          {
            KOrderDetailMinorTitle: "车行手续费",
            KOrderDetailMinorValue: "¥35"
          },
        ],
      },
    ],
  }]
  test.each(mocksMap)('境外费用明细', ({ params, expected }) => {
    expect(getOsdOrderSummary(params).KOrderDetail).toEqual(expected);
  })
})
