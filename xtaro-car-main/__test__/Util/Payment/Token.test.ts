import getToken, { getCtripToken } from '../../../src/pages/xcar/Util/Payment/Token';

describe('getCtripToken', () => {
  test('字段完整传入', () => {
    expect(getCtripToken({
      orderId: '12121211',
      amount: 100,
      currency: 'CNY',
      requestid: '2121212123213213131',
      title: '用车支付',
    })).toEqual({
      oid: '12121211',
      title: '用车支付',
      amount: 100,
      currency: 'CNY',
      bustype: 82,
      requestid: '2121212123213213131',
    })
  })

  test('不传 title', () => {
    // @ts-ignore
    expect(getCtripToken({
      orderId: '12121211',
      amount: 100,
      currency: 'CNY',
      requestid: '2121212123213213131',
    })).toEqual({
      oid: '12121211',
      title: '用车支付',
      amount: 100,
      currency: 'CNY',
      bustype: 82,
      requestid: '2121212123213213131',
    })
  })
})