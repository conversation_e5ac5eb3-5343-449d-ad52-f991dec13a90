import {
  packageAbversion,
} from '../../src/pages/xcar/Util/ABTesting';

describe('测试 ABTesting', () => {

  const ABConfig = {
    key: '200911_DSJT_xsou',
    defaultValue: 'B',
    isActive: true,
    noVersionVal: false,
    isSync: true,
  };

});

jest.mock('../../src/pages/xcar/Util/ServerABTesting', () => ({
  getResAbversion: data => {
    return data?.extras?.abVersion;
  },
}));
describe('测试 ABTesting packageAbversion', () => {
  const mockMap = [
    {
      params: {
        extras: {
          abVersion:
            '220126_DSJT_lqd|B,220323_DSJT_rank2|B,220901_DSJT_RECV|B',
        },
      },
      expected:
        '220126_DSJT_lqd|B,220323_DSJT_rank2|B,220901_DSJT_RECV|B',
    },
    {
      params: {
        extras: {
          abVersion: '',
        },
      },
      expected: '',
    },
  ];
  test.each(mockMap)('packageAbversion check', ({ params, expected }) => {
    const data = packageAbversion(params);
    expect(data).toEqual(expected);
  });
});
