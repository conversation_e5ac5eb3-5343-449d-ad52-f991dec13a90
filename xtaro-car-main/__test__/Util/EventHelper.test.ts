import Event from '@c2x/apis/Event';
import { <PERSON><PERSON>og, AppContext, CarStorage } from '../../src/pages/xcar/Util/Index';
import { APP_TYPE } from '../../src/pages/xcar/Constants/Platform';
import { LogKeyDev, EventName } from '../../src/pages/xcar/Constants/Index';
import {
  addEventListener,
  sendEvent,
  removeEventListener,
  EventTriggerType,
} from '../../src/pages/xcar/Util/EventHelper';

describe('Util EventHelper addEventListener', () => {
  let isCallBackExec = false;
  const callback = () => {
    isCallBackExec = true;
  };
  const mockMap = [
    {
      eventName: EventName.ApptypeChange,
      callback,
      isCallBack: true,
      data: {
        appType: APP_TYPE.ISD_C_APP,
      },
      curAppType: APP_TYPE.ISD_C_APP,
      isSyncAppType: true,
      type: EventTriggerType.Default,
    },
    {
      eventName: EventName.ApptypeChange,
      callback,
      isCallBack: false,
      data: {
        appType: APP_TYPE.ISD_C_APP,
      },
      curAppType: APP_TYPE.OSD_C_APP,
      isSyncAppType: true,
      type: EventTriggerType.Default,
    },
  ];
  const LogTraceDev = jest.spyOn(CarLog, 'LogTraceDev');
  test.each(mockMap)(
    '测试 checkUrl',
    ({
      eventName,
      callback,
      isCallBack,
      data,
      curAppType,
      isSyncAppType,
      type,
    }) => {
      isCallBackExec = false;
      jest
        .spyOn(Event, 'addEventListener')
        .mockImplementation((eventName, callback) => {
          callback(data);
        });
      jest
        .spyOn(CarStorage, 'privateLoadSync')
        .mockImplementation(() => '{ "eventName": "ApptypeChange" }');
      AppContext.setCarEnv({
        appType: curAppType,
      });
      addEventListener(eventName, callback, { isSyncAppType, type });
      switch (type) {
        case EventTriggerType.Default:
        default:
          expect(LogTraceDev).toHaveBeenCalledWith({
            key: LogKeyDev.c_car_trace_event_callback,
            info: {
              name: eventName,
              cacheFrom: data.appType,
              sourceFrom: curAppType,
              params: data,
            },
          });
          expect(isCallBackExec).toEqual(isCallBack);
          break;
      }
    },
  );
});

describe('Util EventHelper sendEvent', () => {
  const mockMap = [
    {
      eventName: EventName.ApptypeChange,
      data: {
        appType: APP_TYPE.ISD_C_APP,
      },
      curAppType: APP_TYPE.ISD_C_APP,
      type: EventTriggerType.Default,
    },
    {
      eventName: EventName.ApptypeChange,
      data: {
        appType: APP_TYPE.ISD_C_APP,
      },
      curAppType: APP_TYPE.OSD_C_APP,
      type: EventTriggerType.Default,
    },
  ];
  const EventSend = jest.spyOn(Event, 'sendEvent');
  test.each(mockMap)(
    '测试 checkUrl',
    ({ eventName, data, curAppType, type }) => {
      AppContext.setCarEnv({
        appType: curAppType,
      });
      sendEvent(eventName, data, { type });
      switch (type) {
        case EventTriggerType.Default:
        default:
          expect(EventSend).toBeCalledWith(eventName, {
            ...data,
            appType: AppContext.CarEnv.appType,
          });
          break;
      }
    },
  );
});

describe('Util EventHelper removeEventListener', () => {
  const mockMap = [
    {
      eventName: EventName.ApptypeChange,
      type: EventTriggerType.Default,
    },
  ];
  const EventRemove = jest.spyOn(Event, 'removeEventListener');
  test.each(mockMap)('测试 checkUrl', ({ eventName, type }) => {
    removeEventListener(eventName, type);
    switch (type) {
      case EventTriggerType.Default:
      default:
        expect(EventRemove).toBeCalledWith(eventName);
        break;
    }
  });
});
