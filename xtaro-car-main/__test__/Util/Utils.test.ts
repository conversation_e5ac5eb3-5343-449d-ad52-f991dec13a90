import { ENV_TYPE, DOMAIN_URL } from '../../src/pages/xcar/Common/src/CarFetch';
import Utils from '../../src/pages/xcar/Util/Utils';
import {
  IMAGE_UPLOAD_DOMAIN_URL,
  APP_TYPE,
  APP_ID,
  BUSINESS_TYPE,
  COMPONENT_CHANNEL,
  RENTAL_GAP,
  BUS_TYPE,
  DROPOFF_INTERVAL,
  ClientType,
  SIDE_TOOL_BIZ_TYPE,
  LIST_SHOW_VENDOR_NUM,
} from '../../src/pages/xcar/Constants/Platform';
import AppContext from '../../src/pages/xcar/Util/AppContext';
import dayjs from '../../src/pages/xcar/Common/src/Dayjs/src';
import { BBK_IMAGE_PATH } from '../../src/pages/xcar/Constants/ImageUrl';
import {
  isHomePage,
  isListPage,
  isOrderDetailPage,
  isLocationPage,
  isGuidePage,
} from '../../src/pages/xcar/Routers/LazyRoute';
import uuid from 'uuid';



jest.mock('@c2x/apis/Permission', () => ({
  __esModule: true,
  default: {
    checkPermissions: jest.fn((params, callback) => {
      callback();
      return Promise.resolve();
    }),
  }
}));


describe('Utils', () => {
  let func: jest.Mock;
  let pasuse: Function;

  const OSDTypes = [
    APP_TYPE.OSD_C_APP,
    APP_TYPE.OSD_C_CW,
    APP_TYPE.OSD_C_H5,
    APP_TYPE.OSD_Q_APP,
    APP_TYPE.OSD_ZUCHE_APP,
  ];

  const ISDTypes = [
    APP_TYPE.ISD_C_APP,
    APP_TYPE.ISD_C_CW,
    APP_TYPE.ISD_C_H5,
    APP_TYPE.ISD_Q_APP,
    APP_TYPE.ISD_ZUCHE_APP,
  ];

  beforeEach(() => {
    global['__crn_appId'] = ''; // 清空赋值
    func = jest.fn();
    pasuse = time => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve('done');
        }, time);
      });
    };
  });

  test('getEnvType', async () => {
    const env = await Utils.getEnvType();
    expect(env).toEqual(ENV_TYPE.PROD);
  });

  test('getLabelTraceInfo', () => {
    const allLabels = [
      {
        category: 1,
        sortNum: 10,
        code: '2',
        title: '立即确认',
        colorCode: '8',
        type: 1,
        description: '预订此产品后将立即确认订单并在取车时间为您安排好车辆。',
        labelCode: '3564',
      },
      {
        category: 1,
        sortNum: 15,
        code: '2',
        title: '限时免费取消',
        colorCode: '1',
        type: 1,
        description:
          '支付完成至取车前69.5小时可免费取消;取车前69.5小时后取消将收取预付租金22%作为违约金',
        labelCode: '3581',
      },
      {
        category: 1,
        sortNum: 25,
        code: '2',
        title: '免费取消',
        colorCode: '8',
        type: 1,
        description:
          '支付完成至取车前69.5小时可免费取消;取车前69.5小时后取消将收取预付租金22%作为违约金',
        labelCode: '3563',
      },
      {
        category: 2,
        sortNum: 25,
        title: '免费升级车组',
        colorCode: '2',
        type: 1,
        description: '保证车辆免费升级一个车型组。',
        labelCode: '3635',
      },
      {
        category: 2,
        sortNum: 85,
        code: '2',
        title: '不限里程',
        colorCode: '2',
        type: 1,
        description: '租期内没有公里数限制。',
        labelCode: '3562',
      },
      {
        category: 2,
        sortNum: 90,
        code: '2',
        title: '支持跨境',
        colorCode: '2',
        type: 1,
        description: '支持跨境。',
        labelCode: '3602',
      },
      {
        category: 2,
        sortNum: 92,
        code: '2',
        title: '支持跨洲',
        colorCode: '2',
        type: 1,
        description: '支持跨洲。',
        labelCode: '3603',
      },
      {
        category: 2,
        sortNum: 100,
        code: '2',
        title: '指定车型',
        colorCode: '2',
        type: 1,
        description: '实际提供的车型即为页面所示车型，而非同车型组的其它车型。',
        labelCode: '3551',
      },
      {
        category: 2,
        sortNum: 105,
        title: '免费GPS',
        colorCode: '2',
        type: 1,
        description: '取车时现场柜台提供免费车载GPS导航仪供租车期间使用。',
        labelCode: '3618',
      },
      {
        category: 2,
        sortNum: 107,
        title: '免费WiFi',
        colorCode: '2',
        type: 1,
        description: '免费WiFi。',
        labelCode: '3619',
      },
      {
        category: 2,
        sortNum: 108,
        code: '2',
        title: '免费电话卡',
        colorCode: '2',
        type: 1,
        description:
          '取车即送1张免租金当地电话卡供租车期间使用（还车时需归还，损失或丢失赔偿相应金额）。',
        labelCode: '3621',
      },
      {
        category: 2,
        sortNum: 10000,
        title: '机票用户专享',
        colorCode: '3',
        type: 1,
        description:
          '1. 一年内有国际/中国港澳台机票待出行及已出行订单的用户，预订国际/中国港澳台租车时享出行特惠租车折扣。2.1. 以上费用仅限基础租车费用，不含异地还车费、附加产品等费用；优惠特权不可叠加使用，默认帮你使用力度最大的优惠；3. 覆盖地区：北美、欧洲、澳新、泰国等，其余国家将陆续开放，具体以搜索结果为准。',
        labelCode: '3862',
      },
    ];
    const traceInfo = Utils.getLabelTraceInfo(allLabels);
    expect(traceInfo).toEqual([
      { tagCode: '3564', tagName: '立即确认', tagType: 1 },
      { tagCode: '3581', tagName: '限时免费取消', tagType: 1 },
      { tagCode: '3563', tagName: '免费取消', tagType: 1 },
      { tagCode: '3635', tagName: '免费升级车组', tagType: 2 },
      { tagCode: '3562', tagName: '不限里程', tagType: 2 },
      { tagCode: '3602', tagName: '支持跨境', tagType: 2 },
      { tagCode: '3603', tagName: '支持跨洲', tagType: 2 },
      { tagCode: '3551', tagName: '指定车型', tagType: 2 },
      { tagCode: '3618', tagName: '免费GPS', tagType: 2 },
      { tagCode: '3619', tagName: '免费WiFi', tagType: 2 },
      { tagCode: '3621', tagName: '免费电话卡', tagType: 2 },
      { tagCode: '3862', tagName: '机票用户专享', tagType: 2 },
    ]);
  });

  test('getDomainURL', () => {
    Object.keys(ENV_TYPE).map(key => {
      const env = ENV_TYPE[key];
      expect(Utils.getDomainURL(env)).toEqual(DOMAIN_URL[env]);
    });
  });

  test('getImageUploadDomainURL', () => {
    Object.keys(ENV_TYPE).map(key => {
      if (!IMAGE_UPLOAD_DOMAIN_URL[key]) {
        // skip MOCK
        return;
      }
      const env = ENV_TYPE[key];
      expect(Utils.getImageUploadDomainURL(env)).toEqual(
        IMAGE_UPLOAD_DOMAIN_URL[env],
      );
    });
  });

  test('getAppType', () => {
    expect(Utils.getAppType(APP_TYPE.ISD_C_APP)).toEqual(APP_TYPE.ISD_C_APP);
    expect(Utils.getAppType(APP_TYPE.OSD_C_APP)).toEqual(APP_TYPE.OSD_C_APP);
    global['__crn_appId'] = APP_ID.TRIP;
    expect(Utils.getAppType()).toEqual(APP_TYPE.OSD_T_APP);
  });

  test('getNewAppType', () => {
    expect(Utils.getNewAppType(true, APP_TYPE.ISD_C_APP)).toEqual(
      APP_TYPE.ISD_C_APP,
    );
  });

  test('getBusinessType', () => {
    AppContext.setCarEnv({ appType: '' });

    expect(Utils.getBusinessType()).toEqual(BUSINESS_TYPE.UNKNOW);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getBusinessType()).toEqual(BUSINESS_TYPE.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getBusinessType()).toEqual(BUSINESS_TYPE.OSD);

    global['__crn_appId'] = APP_ID.TRIP;
    expect(Utils.getBusinessType()).toEqual(BUSINESS_TYPE.IBU);
  });

  test('getCurrentEnv', () => {
    AppContext.setCarEnv({ appType: '' });

    expect(Utils.getCurrentEnv()).toEqual(BUSINESS_TYPE.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getCurrentEnv()).toEqual(COMPONENT_CHANNEL.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getCurrentEnv()).toEqual(COMPONENT_CHANNEL.OSD);
  });

  test('getOtherEnv', () => {
    AppContext.setCarEnv({ appType: '' });

    expect(Utils.getOtherEnv()).toEqual(COMPONENT_CHANNEL.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getOtherEnv()).toEqual(COMPONENT_CHANNEL.OSD);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getOtherEnv()).toEqual(COMPONENT_CHANNEL.ISD);
  });

  test('isTrip', () => {
    expect(Utils.isTrip()).toBeFalsy();

    global['__crn_appId'] = APP_ID.TRIP;
    expect(Utils.isTrip()).toBeTruthy();
  });

  test('getParams', () => {
    const mockData = {
      rentalDate: {
        pickUp: {
          dateTime: '20200612100000',
        },
        dropOff: {
          dateTime: '20200614100000',
        },
      },
      rentalLocation: {
        pickUp: {
          country: '中国',
          area: {
            lng: 110.343315,
            lat: 19.984078,
            id: '',
            name: '海口东站',
            type: '2',
          },

          cname: '海口',
          cid: '42',
        },
        dropOff: {
          country: '中国',
          area: {
            lng: 110.343315,
            lat: 19.984078,
            id: '',
            name: '海口东站',
            type: '2',
          },

          cname: '海口',
          cid: '42',
        },
        isShowDropOff: false,
      },
    };
    const dataStr = encodeURIComponent(JSON.stringify(mockData));
    expect(Utils.getParams(dataStr)).toEqual(mockData);
  });

  test('isCtripOsd', () => {
    AppContext.setCarEnv({ appType: '' });

    expect(Utils.isCtripOsd()).toBeFalsy();

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.isCtripOsd()).toBeTruthy();
  });

  test('isCtripIsd', () => {
    AppContext.setCarEnv({ appType: '' });

    expect(Utils.isCtripIsd()).toBeFalsy();

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.isCtripIsd()).toBeTruthy();
  });

  test('isCtripIsdByType', () => {
    ISDTypes.map(m => {
      expect(Utils.isCtripIsdByType(m)).toBeTruthy();
    });
  });

  test('getType', () => {
    AppContext.setCarEnv({ appType: '' });

    expect(Utils.getType()).toEqual('');

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getType()).toEqual('isd');

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getType()).toEqual('osd');
  });

  test('isCtripApp', () => {
    expect(Utils.isCtripApp()).toBeFalsy();

    global['__crn_appId'] = APP_ID.CTRIP;
    expect(Utils.isCtripApp()).toBeTruthy();
  });

  test('isQunarApp', () => {
    AppContext.setCarEnv({ appType: '' });

    expect(Utils.getType()).toEqual('');

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getType()).toEqual('isd');

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getType()).toEqual('osd');
  });

  test('getUBT', () => {
    // NONE:
  });

  test('promisable', async () => {
    const func = (numbA, numbB, numbC, callback) => {
      callback(numbA + numbB + numbC);
    };

    const result = await Utils.promisable(func)(1, 2, 3, 'callback');
    expect(result).toEqual(6);

    const funcError = (numbA, numbB, numbC, callback) => {
      callback(numbA / numbB + numbC);
    };
    const resultErr = await Utils.promisable(funcError)(1, 0, 3, 'callback');
    expect(resultErr).toEqual(Infinity);
  });

  test('dateTimeFormat', () => {
    expect(Utils.dateTimeFormat('20200612100000')).toEqual(
      '2020-06-12 10:00:00',
    );

    expect(Utils.dateTimeFormat('20220719155000')).toEqual(
      '2022-07-19 15:50:00',
    );

    expect(Utils.dateTimeFormat('20220719155')).toEqual('');
  });

  test('fullImgProtocal', () => {
    const urlA = '://dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7.jpg';
    const urlB = 'https://dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7.jpg';
    const urlC = '//dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7.jpg';
    const urlD = 'http://dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7.jpg';

    expect(Utils.fullImgProtocal(urlA)).toEqual(urlA);
    expect(Utils.fullImgProtocal(urlB)).toEqual(urlB);
    expect(Utils.fullImgProtocal(urlC)).toEqual(urlB); // url was changed
    expect(Utils.fullImgProtocal(urlD)).toEqual(urlD);
  });

  test('getRentalGap', () => {
    AppContext.setCarEnv({ appType: '' });

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getRentalGap()).toEqual(RENTAL_GAP.OSD);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getRentalGap()).toEqual(RENTAL_GAP.ISD);
  });

  test('getBusType', () => {
    AppContext.setCarEnv({ appType: '' });
    expect(Utils.getBusType()).toEqual(0);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getBusType()).toEqual(BUS_TYPE.OSD);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getBusType()).toEqual(BUS_TYPE.ISD);
  });

  test('openUrlWithTicket', () => {
    // NONE:
  });

  test('autoProtocol', () => {
    const urlA = '//m.ctrip.com/webapp/cars/osd/osd/osdinvoice?id=6002282663';
    const urlB = '://m.ctrip.com/webapp/cars/osd/osd/osdinvoice?id=6002282663';
    const urlC = 'http://dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7.jpg';
    const urlD = 'https://dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7.jpg';
    const urlE = '';

    const urlAHttp =
      'http://m.ctrip.com/webapp/cars/osd/osd/osdinvoice?id=6002282663';
    const urlAHttps =
      'https://m.ctrip.com/webapp/cars/osd/osd/osdinvoice?id=6002282663';
    expect(Utils.autoProtocol(urlA, 'http')).toEqual(urlAHttp);
    expect(Utils.autoProtocol(urlA, 'https')).toEqual(urlAHttps);
    expect(Utils.autoProtocol(urlA)).toEqual(urlAHttps);

    const urlBHttp =
      'http://m.ctrip.com/webapp/cars/osd/osd/osdinvoice?id=6002282663';
    const urlBHttps =
      'https://m.ctrip.com/webapp/cars/osd/osd/osdinvoice?id=6002282663';
    expect(Utils.autoProtocol(urlB, 'http')).toEqual(urlBHttp);
    expect(Utils.autoProtocol(urlB, 'https')).toEqual(urlBHttps);

    expect(Utils.autoProtocol(urlC)).toEqual(urlC);
    expect(Utils.autoProtocol(urlD)).toEqual(urlD);
    expect(Utils.autoProtocol(urlE)).toEqual(urlE);
  });

  test('openPDF', () => {
    // NONE:
  });

  test('toParams', () => {
    const obj = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };
    expect(Utils.toParams(obj)).toEqual(
      'HelloWorld=helloworld&Year=2022&MONTH=July&day=19',
    );

    expect(Utils.toParams(obj, true)).toEqual(
      'helloworld=helloworld&year=2022&month=July&day=19',
    );
  });

  test('isObjectArray', () => {
    // NONE:
  });

  test('objectArrayToArray', () => {
    // NONE:
  });

  test('toRMB', () => {
    expect(Utils.toRMB('CNY')).toEqual('¥');
    expect(Utils.toRMB('RMB')).toEqual('RMB');
    expect(Utils.toRMB('USD')).toEqual('USD');
  });

  test('cloneDeep', () => {
    const obj = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };
    expect(Utils.cloneDeep(obj)).not.toBe(obj);
  });

  test('getDropOffInterval', () => {
    AppContext.setCarEnv({ appType: '' });
    expect(Utils.getDropOffInterval()).toEqual(DROPOFF_INTERVAL.UNKNOW);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getDropOffInterval()).toEqual(DROPOFF_INTERVAL.OSD);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getDropOffInterval()).toEqual(DROPOFF_INTERVAL.ISD);
  });

  test('getRentalPickerOption', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getRentalPickerOption()).toEqual({
      Dropoff_Interval: 0.5,
      Default_Interval_Days: 7,
    });

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getRentalPickerOption()).toEqual({
      Dropoff_Interval: 0.25,
      Default_Interval_Days: 2,
    });
  });

  test('getClientType', () => {
    expect(Utils.getClientType()).toEqual(ClientType.ctrip);
  });

  test('getUrlParam', () => {
    const url =
      'http://***********:5389/index.ios.bundle?CRNModuleName=rn_car_main&CRNType=1&apptype=ISD_C_APP&initialPage=OrderDetail&orderId=3178239795';
    expect(Utils.getUrlParam(url, 'CRNModuleName')).toEqual('rn_car_main');
    expect(Utils.getUrlParam(url, 'CRNType')).toEqual('1');
    expect(Utils.getUrlParam(url, 'apptype')).toEqual('ISD_C_APP');
    expect(Utils.getUrlParam(url, 'orderId')).toEqual('3178239795');
  });

  test('getQueryString', () => {
    const url =
      'http://***********:5389/index.ios.bundle?CRNModuleName=rn_car_main&CRNType=1&apptype=ISD_C_APP&initialPage=OrderDetail&orderId=3178239795';
    expect(Utils.getQueryString(url)).toEqual({
      orderId: '3178239795',
      driverLienceNo: 0,
      CRNModuleName: 'rn_car_main',
      CRNType: '1',
      apptype: 'ISD_C_APP',
      initialPage: 'OrderDetail',
    });

    expect(Utils.getQueryString('')).toEqual({ orderId: 0, driverLienceNo: 0 });
  });

  test('getQueryParams', () => {
    const url =
      'http://***********:5389/index.ios.bundle?CRNModuleName=rn_car_main&CRNType=1&apptype=ISD_C_APP&initialPage=OrderDetail&orderId=3178239795';
    const url2 =
      'http://10.32.208.18:5389/index.ios.bundle?CRNModuleName=rn_car_main&CRNType=1&initialPage=Market&st=ser&fromurl=common&landingto=Home&apptype=ISD_C_APP&pcid=43&pcid=42';
    expect(Utils.getQueryParams(url)).toEqual({
      orderId: '3178239795',
      CRNModuleName: 'rn_car_main',
      CRNType: '1',
      apptype: 'ISD_C_APP',
      initialPage: 'OrderDetail',
    });

    expect(Utils.getQueryParams(url2)).toEqual({
      CRNModuleName: 'rn_car_main',
      CRNType: '1',
      apptype: 'ISD_C_APP',
      initialPage: 'Market',
      st: 'ser',
      fromurl: 'common',
      landingto: 'Home',
      pcid: '42',
    });

    expect(Utils.getQueryParams('')).toEqual({});
  });

  test('keyMapping', () => {
    const obj = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };

    expect(Utils.keyMapping(obj)).toEqual({
      helloworld: 'helloworld',
      year: 2022,
      month: 'July',
      day: 19,
    });
  });

  test('getRequestUrl', async () => {
    expect(await Utils.getRequestUrl('/18631/createPayOrder')).toEqual(
      'https://m.ctrip.com/18631/createPayOrder',
    );
  });

  test('jumpUrl', () => {
    // NONE:
  });

  test('formatCNPhone', () => {
    expect(Utils.formatCNPhone('13817900002')).toEqual('138 1790 0002');
    expect(Utils.formatCNPhone('132001921')).toEqual('132 0019 21');
    expect(Utils.formatCNPhone('0563-8168711')).toEqual('056 3-81 68711');
  });

  test('strFormat', () => {
    expect(Utils.strFormat('13817900002', 1)).toEqual('138 1790 0002');
    expect(Utils.strFormat('621483******7482', 2)).toEqual(
      '621483 **** **74 82',
    );
    expect(Utils.strFormat('0563-8168711', null)).toEqual('0563-8168711');
  });

  test('strTrim', () => {
    expect(Utils.strTrim(' 1 3 8 1 7 9 0 0 0 0 2 ')).toEqual('13817900002');
    expect(Utils.strTrim('0563 - 8168711')).toEqual('0563-8168711');
  });

  test('format', () => {
    expect(
      Utils.format(
        '{0} is the {1} powerful language{2}, {1}!',
        'javascript',
        'most',
        '!',
      ),
    ).toEqual('javascript is the most powerful language!, most!');
  });

  test('getHost', () => {
    expect(Utils.getHost('fat')).toEqual(
      'https://m.fat10668.qa.nt.ctripcorp.com',
    );
    expect(Utils.getHost('uat')).toEqual('https://m.uat.qa.nt.ctripcorp.com');
    expect(Utils.getHost('prd')).toEqual('https://m.ctrip.com');
    expect(Utils.getHost('')).toEqual('https://m.ctrip.com');
  });

  test('getCarhireHost', () => {
    expect(Utils.getCarhireHost('fat')).toEqual(
      'https://m.fat486.qa.nt.ctripcorp.com',
    );
    expect(Utils.getHost('uat')).toEqual('https://m.uat.qa.nt.ctripcorp.com');
    expect(Utils.getHost('prd')).toEqual('https://m.ctrip.com');
    expect(Utils.getHost('')).toEqual('https://m.ctrip.com');
  });

  test('getRentalMaxMonth', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getRentalMaxMonth()).toEqual(6);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getRentalMaxMonth()).toEqual(12);
  });

  test('getSideToolBizType', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getSideToolBizType()).toEqual(SIDE_TOOL_BIZ_TYPE.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getSideToolBizType()).toEqual(SIDE_TOOL_BIZ_TYPE.OSD);
  });

  test('getListVendorShowNum', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getListVendorShowNum()).toEqual(LIST_SHOW_VENDOR_NUM.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getListVendorShowNum()).toEqual(LIST_SHOW_VENDOR_NUM.OSD);
  });

  test('colorLog', () => {
    // NONE:
  });

  test('isIsdCountry', () => {
    expect(Utils.isIsdCountry({ isDomestic: true })).toBeTruthy();
    expect(Utils.isIsdCountry({ isDomestic: false })).toBeFalsy();
    expect(Utils.isIsdCountry({ country: '中国' })).toBeTruthy();
    expect(Utils.isIsdCountry({ country: '美国' })).toBeFalsy();
    // TODO: ERROR CASE
    expect(
      Utils.isIsdCountry({ country: '美国', isDomestic: true }),
    ).toBeTruthy();
    expect(Utils.isIsdCountry({})).toBeFalsy();
  });

  test('compareProps', () => {
    const prev = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };

    const next = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };

    const next2 = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 0,
    };

    expect(Utils.compareProps(prev, next)).toBeTruthy();
    expect(Utils.compareProps(prev, next2)).toBeFalsy();
  });

  test('compareObject', () => {
    const prev = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };

    const next = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };

    const next2 = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 0,
    };

    const next3 = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
      other: 3,
    };

    const prevArr = [prev];
    const nextArr = [next];
    const nextArr2 = [next2];
    const nextArr3 = [next, next2];

    expect(Utils.compareObject(prev, next)).toBeTruthy();
    expect(Utils.compareObject(prev, next2)).toBeFalsy();
    expect(Utils.compareObject(prev, next3)).toBeFalsy();
    expect(Utils.compareObject(next3, prev)).toBeFalsy();

    expect(Utils.compareObject(prevArr, nextArr)).toBeTruthy();
    expect(Utils.compareObject(prevArr, nextArr2)).toBeFalsy();
    expect(Utils.compareObject(prevArr, nextArr3)).toBeFalsy();
    expect(Utils.compareObject(nextArr3, prevArr)).toBeFalsy();
  });

  test('getMaskCode', () => {
    expect(Utils.getMaskCode({ code: '*', length: 4 })).toEqual('****');
    expect(Utils.getMaskCode({ code: '-', length: 5 })).toEqual('-----');
  });

  test('identifyIdMask', () => {
    expect(Utils.identifyIdMask('341011199910105566')).toEqual(
      '34101119****105566',
    );
  });

  test('phoneNumberMask', () => {
    expect(Utils.phoneNumberMask('13817008899')).toEqual('138****8899');
    expect(Utils.phoneNumberMask('17722220000')).toEqual('177****0000');
  });

  test('getPhoneList', () => {
    expect(Utils.getPhoneList('13817008899;0563-8175678，17755556666')).toEqual(
      ['13817008899', '0563-8175678', '17755556666'],
    );
  });

  test('wrapDebounce', () => {
    // TODO:
  });

  test('getComponentByChannel', () => {
    // TODO:
  });

  test('toNumber', () => {
    const trulyData = ['1.1', 0.9, '222', 1.234];
    const falsyData = ['hell', 'a.o.0'];
    trulyData.map(m => {
      expect(Utils.toNumber(m)).not.toBe(0);
    });

    falsyData.map(m => {
      expect(Utils.toNumber(m)).toEqual(0);
    });
  });

  test('compareVersion', () => {
    expect(Utils.compareVersion('8.11.0', '8.11')).toEqual(1);
    expect(Utils.compareVersion('8.11.0', '8.11.0')).toEqual(0);
    expect(Utils.compareVersion('8.11.0', '8.12')).toEqual(-1);
    expect(Utils.compareVersion('8.11.0', '')).toEqual(1);
    expect(Utils.compareVersion('', '')).toEqual(0);
  });

  test('getCarEnv', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getCarEnv('ISD')).toEqual({
      currentEnv: 'ISD',
      nextEnv: 'OSD',
    });
  });

  test('closestDate', () => {
    const d1 = dayjs('2033-10-01 09:59:59');
    const d2 = dayjs('2033-10-01 09:19:59');
    expect(Utils.closestDate(d1, 30).format('YYYY-MM-DD HH:mm:ss')).toEqual(
      '2033-10-01 10:00:00',
    );
    expect(Utils.closestDate(d2, 30).format('YYYY-MM-DD HH:mm:ss')).toEqual(
      '2033-10-01 09:30:00',
    );
    expect(Utils.closestDate(d2, 10).format('YYYY-MM-DD HH:mm:ss')).toEqual(
      '2033-10-01 09:20:00',
    );
  });

  test('getUniqRequestKeyWithEnv', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getUniqRequestKeyWithEnv()).toEqual(
      expect.stringContaining('ISD_C_APP'),
    );

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getUniqRequestKeyWithEnv()).toEqual(
      expect.stringContaining('OSD_C_APP'),
    );
  });

  test('appendMarkToUniqRequestKey', () => {
    expect(Utils.appendMarkToUniqRequestKey('test_id', true)).toEqual(
      'test_id/true',
    );
  });

  test('getListBatchGroups', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getListBatchGroups()).toEqual([0]);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getListBatchGroups()).toEqual([0, 1]);
  });

  test('isHomePage', () => {
    expect(isHomePage('Market', 'List')).toBeFalsy();
    expect(isHomePage('Market', 'Home')).toBeTruthy();
    expect(isHomePage('Home', undefined)).toBeTruthy();
    expect(isHomePage('', undefined)).toBeTruthy();
  });

  test('isListPage', () => {
    expect(isListPage('Market', 'List')).toBeTruthy();
    expect(isListPage('Market', 'Home')).toBeFalsy();
    expect(isListPage('List', undefined)).toBeTruthy();
    expect(isListPage('', undefined)).toBeFalsy();
  });

  test('isOrderDetailPage', () => {
    expect(isOrderDetailPage('OrderDetail')).toBeTruthy();
    expect(isLocationPage('Market')).toBeFalsy();
  });

  test('isLocationPage', () => {
    expect(isLocationPage('Location')).toBeTruthy();
    expect(isLocationPage('Market')).toBeFalsy();
  });

  test('isGuidePage', () => {
    expect(isGuidePage('Guide')).toBeTruthy();
    expect(isGuidePage('Market')).toBeFalsy();
  });

  test('compatImgUrlWithWebp', () => {
    const urlA = 'https://dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7.jpg';
    expect(Utils.compatImgUrlWithWebp(urlA)).toEqual(urlA + '_.webp');
  });

  test('nameForIDCheck', () => {
    expect(Utils.nameForIDCheck('㑇䶮')).toBeTruthy();
    expect(Utils.nameForIDCheck('张三')).toBeTruthy();
    expect(Utils.nameForIDCheck('LiLei')).toBeFalsy();
    expect(Utils.nameForIDCheck('Li·Lei')).toBeFalsy();
  });

  test('nameForIDTypeCheck', () => {
    expect(Utils.nameForIDTypeCheck(1, '㑇䶮')).toBeTruthy();
    expect(Utils.nameForIDTypeCheck(1, '张三')).toBeTruthy();
    expect(Utils.nameForIDTypeCheck(1, 'LiLei')).toBeFalsy();
    expect(Utils.nameForIDTypeCheck(1, 'Li·Lei')).toBeFalsy();

    expect(Utils.nameForIDTypeCheck(2, '㑇䶮')).toBeTruthy();
    expect(Utils.nameForIDTypeCheck(2, '张三')).toBeTruthy();
    expect(Utils.nameForIDTypeCheck(2, 'LiLei')).toBeFalsy();
    expect(Utils.nameForIDTypeCheck(2, 'Li·Lei')).toBeFalsy();
  });

  test('changeObject2QueryString', () => {
    const obj = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };

    expect(Utils.changeObject2QueryString(obj)).toEqual(
      'HelloWorld=helloworld&Year=2022&MONTH=July&day=19',
    );
  });

  test('getPackageStyle', () => {
    const mockMap = [
      {
        styleList: [],
      },
      {
        styleList: [
          {
            color: '#232322',
          },
          {
            color: '#111111',
          },
        ],
      },
    ];
    mockMap.forEach(item => {
      const { styleList } = item;
      const result = Utils.getPackageStyle(styleList);
      const result2 = Utils.getPackageStyle(styleList);
      expect(result === result2).toBeTruthy();
    });
  });

  test('isInLimitAgeIdCard', () => {
    expect(Utils.isInLimitAgeIdCard('342510198706052211', 0, 40)).toEqual(
      '当前供应商要求，驾驶员最小年龄为40岁，请更换驾驶员或选择其他车行',
    );
    expect(Utils.isInLimitAgeIdCard('342510196706052211', 50, 0)).toEqual(
      '当前供应商要求，驾驶员最大年龄为50岁，请更换驾驶员或选择其他车行',
    );
    expect(Utils.isInLimitAgeIdCard('342510198706052211', 0, 0)).toEqual('');
  });

  test('getCurPageId', () => {
    expect(Utils.getCurPageId()).toEqual('');
  });

  test('is835AndAbove', () => {
    expect(Utils.is835AndAbove()).toBeTruthy();
  });

  test('isImageUrl', () => {
    const urlA = 'https://dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7.jpg';
    const urlB = 'https://dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7';
    const urlC = 'https://dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7.webp';
    expect(Utils.isImageUrl(urlA)).toBeTruthy();
    expect(Utils.isImageUrl(urlB)).toBeFalsy();
    expect(Utils.isImageUrl(urlC)).toBeFalsy();
  });

  test('handleTextOverflow', () => {
    expect(Utils.handleTextOverflow('hellow world!', 5)).toEqual('hell...');
    expect(Utils.handleTextOverflow('点击查看更多', 5)).toEqual('点击查看...');
    expect(Utils.handleTextOverflow('It is amazing', 10)).toEqual(
      'It is ama...',
    );
  });

  test('getCheckRealNameUrl', () => {
    expect(Utils.getCheckRealNameUrl()).toEqual(
      'https://secure.ctrip.com/webapp/paywallet/realname',
    );
  });

  test('getMemberLevelUrl', () => {
    expect(Utils.getMemberLevelUrl()).toEqual(
      'https://m.ctrip.com/webapp/member/newindex?isHideNavBar=YES',
    );
  });

  test('getMemberPointsUrl', () => {
    expect(Utils.getMemberPointsUrl()).toEqual(
      'https://m.ctrip.com/webapp/rewards/mypoint?backtype=1',
    );
  });

  test('getLogStrValue', () => {
    expect(Utils.getLogStrValue('a')).toEqual('0');
    expect(Utils.getLogStrValue('1')).toEqual('1');
    expect(Utils.getLogStrValue(0)).toEqual('0');
  });

  test('hasLocationPermissionPromise', async () => {
    expect(await Utils.hasLocationPermissionPromise()).toEqual({
      status: undefined,
      result: undefined,
    });
  });

  test('toFixed', () => {
    expect(Utils.toFixed(6.005, 2)).toEqual(6.01);
  });

  test('BaseOperation', () => {
    expect(Utils.BaseOperation('3.000001', '3.000003', 0)).toEqual('6.000004');
  });

  test('Add', () => {
    expect(Utils.Add('3.000001', '3.000003')).toEqual('6.000004');
  });

  test('Sub', () => {
    expect(Utils.Sub('5.000001', '3.000003')).toEqual('1.999998');
  });

  test('Multiply', () => {
    expect(Utils.Multiply('5.000001', '3.000003')).toEqual('15.000018000003');
  });

  test('Division', () => {
    expect(Utils.Division('5.000001', '3.000003')).toEqual(
      '1.6666653333346666',
    );
  });

  test('validateIsServerError', () => {
    expect(Utils.validateIsServerError(null)).toBeTruthy();
  });

  test('validateIsSaleOut', () => {
    // TODO: 不应该在Utils中添加业务方法
    expect(Utils.validateIsSaleOut(null, null)).toBeFalsy();
  });

  test('getByteLength', () => {
    expect(Utils.getByteLength('hello')).toEqual(5);
    expect(Utils.getByteLength('你好，中国')).toEqual(10);
    expect(Utils.getByteLength('hello 中国')).toEqual(10);
  });

  test('getByteLengthStr', () => {
    expect(Utils.getByteLengthStr('hello', 3)).toEqual('hel');
    expect(Utils.getByteLengthStr('你好，中国', 4)).toEqual('你好');
    expect(Utils.getByteLengthStr('hello 中国', 9)).toEqual('hello 中');
  });

  test('isBetween', () => {
    // NONE: dayjs库功能不用单测
  });

  test('dayJsUtc', () => {
    // NONE: dayjs库功能不用单测
  });

  test('getDayDiff', () => {
    // NONE: dayjs库功能不用单测
  });

  test('getBbkImageUrl', () => {
    expect(Utils.getBbkImageUrl('hello.png')).toEqual(
      `${BBK_IMAGE_PATH}hello.png`,
    );
  });

  test('isShowHomeHeader', () => {
    expect(Utils.isShowHomeHeader()).toEqual('');
  });

  test('composeError2String', () => {
    expect(Utils.composeError2String(new Error('this is an error'))).toContain(
      'Error: this is an error',
    );
    expect(Utils.composeError2String({})).toEqual('{}');
    expect(Utils.composeError2String([])).toEqual('[]');
    expect(Utils.composeError2String(0)).toEqual('0');
    expect(Utils.composeError2String('')).toEqual('""');
  });
  test('getErrorMessage', () => {
    expect(Utils.getErrorMessage(new Error('this is an error'))).toEqual(
      `this is an error`,
    );
    expect(Utils.getErrorMessage({})).toEqual('{}');
  });

  test('openUrlVendorListPageUniqueId', () => {
    const pageUniqueId = uuid();
    AppContext.setUrlQuery({
      initialPage: 'VendorList',
      pageUniqueId,
    });
    expect(Utils.openUrlVendorListPageUniqueId()).toEqual(pageUniqueId);
    AppContext.setUrlQuery({
      initialPage: '',
      pageUniqueId,
    });
    expect(Utils.openUrlVendorListPageUniqueId()).toEqual(false);
  });

  test('isValid', () => {
    expect(Utils.isValid(1)).toBeTruthy();
    expect(Utils.isValid(undefined)).toBeFalsy();
    expect(Utils.isValid('')).toBeFalsy();
    expect(Utils.isValid(null)).toBeFalsy();
  });

  test('isCoordinateValid', () => {
    expect(Utils.isCoordinateValid(1)).toBeTruthy();
    expect(Utils.isCoordinateValid(undefined)).toBeFalsy();
    expect(Utils.isCoordinateValid('')).toBeFalsy();
    expect(Utils.isCoordinateValid(null)).toBeFalsy();
    expect(Utils.isCoordinateValid(0)).toBeFalsy();
  });

  test('isEqual', () => {
    expect(Utils.isEqual(1, 1)).toBeTruthy();
    expect(Utils.isEqual(1, 0)).toBeFalsy();
  });

  test('isRequestParamCoincident', () => {
    const resRequestInfo_1 = {
      rLongitude: 109.41201,
      pDate: '20230330100000',
      sourceCountryId: 1,
      age: 30,
      rLatitude: 18.30747,
      pLongitude: 109.41201,
      rDate: '20230331100000',
      pickupLocationName: '凤凰国际机场T1航站楼',
      pLatitude: 18.30747,
      returnLocationName: '凤凰国际机场T1航站楼',
    };
    const resRequestInfo_2 = {
      rLongitude: 109.41201,
      pDate: '20230330100000',
      sourceCountryId: 1,
      age: 30,
      rLatitude: 18.307475,
      pLongitude: 109.41201,
      rDate: '20230331100000',
      pickupLocationName: '凤凰国际机场T1航站楼',
      pLatitude: 18.30747,
      returnLocationName: '凤凰国际机场T1航站楼',
    };
    const state = {
      envMeta: { isMultiEnvironment: true },
      rentalLocation: {
        pickUp: {
          cid: 43,
          cname: '三亚',
          country: '中国',
          realcountry: '中国',
          isDomestic: true,
          area: {
            id: '',
            name: '凤凰国际机场T1航站楼',
            lat: 18.30747,
            lng: 109.41201,
            type: '1',
          },
          isFromPosition: false,
        },
        dropOff: {
          cid: 43,
          cname: '三亚',
          country: '中国',
          realcountry: '中国',
          isDomestic: true,
          area: {
            id: '',
            name: '凤凰国际机场T1航站楼',
            lat: 18.30747,
            lng: 109.41201,
            type: '1',
          },
          isFromPosition: false,
        },
        isShowDropOff: false,
      },
      rentalDate: {
        pickUp: { dateTime: '2023-03-30T02:00:00.000Z' },
        dropOff: { dateTime: '2023-03-31T02:00:00.000Z' },
        productDropOff: { dateTime: '2023-03-31T02:00:00.000Z' },
      },
      position: {
        positionInfo: {},
        positionLocation: {},
        positionStatus: { locationOn: false, locationStatus: 'loading' },
      },
      filterItems: [],
      isPickupStation: '1',
      isDropOffStation: '1',
      airPortTransferTip: { isShow: false },
      selectCityId: 0,
    };
    expect(
      Utils.isRequestParamCoincident(resRequestInfo_1, state),
    ).toBeTruthy();
    expect(Utils.isRequestParamCoincident(resRequestInfo_2, state)).toBeFalsy();
  });

  test.each([
    {
      time: '2023/04/01 00:23:01',
      expected: '2023-04-01 00:30:00',
    },
    {
      time: '2023/04/01 00:45:01',
      expected: '2023-04-01 01:00:00',
    },
    {
      time: '2023/04/01 00:00:01',
      expected: '2023-04-01 00:00:00',
    },
    {
      time: '2023/04/01 00:59:01',
      expected: '2023-04-01 01:00:00',
    },
  ])('fixAddMinuteGap', ({ time, expected }) => {
    expect(Utils.fixAddMinuteGap(dayjs(time))).toEqual(expected);
  });

  test('isFixedTelephone', () => {
    expect(Utils.isFixedTelephone('02161898298,45542')).toBeTruthy();
    expect(Utils.isFixedTelephone('61898298,45542')).toBeFalsy();
  });

  test('getGoodFixedTelephone', () => {
    expect(Utils.getGoodFixedTelephone('02161898298,45542')).toEqual(
      '02161898298,45542',
    );
    expect(Utils.getGoodFixedTelephone('61898298,45542')).toEqual(
      `61898298,45542`,
    );
  });

  test('should return true when pos and offsetY are equal', () => {
    const pos = 100;
    const offsetY = 100;
    expect(Utils.isScrollReachPos(pos, offsetY)).toEqual(true);
  });

  test('should return true when offsetY is within the POS_DIFF range of pos', () => {
    const pos = 100;
    const offsetY = 125;
    expect(Utils.isScrollReachPos(pos, offsetY)).toEqual(true);
  });

  test('should return false when offsetY is outside the POS_DIFF range of pos', () => {
    const pos = 100;
    const offsetY = 200;
    expect(Utils.isScrollReachPos(pos, offsetY)).toEqual(false);
  });

  it('should format the certificate number with spaces in the correct positions', () => {
    // Mocking the input parameter
    const value = '1234567890123456';

    // Calling the function to be tested
    const result = Utils.formatCNCertificateNo(value);

    // Assertion
    expect(result).toBe('123456 7890 1234 56');
  });

  it('should handle empty string input and return an empty string', () => {
    // Mocking the input parameter
    const value = '';

    // Calling the function to be tested
    const result = Utils.formatCNCertificateNo(value);

    // Assertion
    expect(result).toBe('');
  });

  it('should handle undefined input and return an empty string', () => {
    // Mocking the input parameter
    const value = undefined;

    // Calling the function to be tested
    const result = Utils.formatCNCertificateNo(value);

    // Assertion
    expect(result).toBe('');
  });

  it('should return the correct host for the "fat" environment', () => {
    // Mocking the input parameter
    const env = 'fat';

    // Calling the function to be tested
    const result = Utils.getUrlHost(env);

    // Assertion
    expect(result).toBe('https://m.fat10668.qa.nt.ctripcorp.com');
  });

  it('should return the correct host for the "uat" environment', () => {
    // Mocking the input parameter
    const env = 'uat';

    // Calling the function to be tested
    const result = Utils.getUrlHost(env);

    // Assertion
    expect(result).toBe('https://m.uat.qa.nt.ctripcorp.com');
  });

  it('should return the correct host for the "prd" environment', () => {
    // Mocking the input parameter
    const env = 'prd';

    // Calling the function to be tested
    const result = Utils.getUrlHost(env);

    // Assertion
    expect(result).toBe('https://m.ctrip.com');
  });

  it('should return the default host for unknown environments', () => {
    // Mocking the input parameter
    const env = 'unknown';

    // Calling the function to be tested
    const result = Utils.getUrlHost(env);

    // Assertion
    expect(result).toBe('https://m.ctrip.com');
  });

  it('should return the default host when called without an environment parameter', () => {
    // Calling the function to be tested
    const result = Utils.getUrlHost();

    // Assertion
    expect(result).toBe('https://m.ctrip.com');
  });

  it('should return the correct host for the "fat" environment', () => {
    // Mocking the input parameter
    const env = 'fat';

    // Calling the function to be tested
    const result = Utils.getTripH5Host(env);

    // Assertion
    expect(result).toBe('https://hk.fat1.qa.nt.tripqate.com/');
  });

  it('should return the correct host for the "uat" environment', () => {
    // Mocking the input parameter
    const env = 'uat';

    // Calling the function to be tested
    const result = Utils.getTripH5Host(env);

    // Assertion
    expect(result).toBe('https://hk.fat1.qa.nt.tripqate.com/');
  });

  it('should return the correct host for the "prd" environment', () => {
    // Mocking the input parameter
    const env = 'prd';

    // Calling the function to be tested
    const result = Utils.getTripH5Host(env);

    // Assertion
    expect(result).toBe('https://hk.trip.com/');
  });

  it('should return the default host for unknown environments', () => {
    // Mocking the input parameter
    const env = 'unknown';

    // Calling the function to be tested
    const result = Utils.getTripH5Host(env);

    // Assertion
    expect(result).toBe('https://hk.trip.com/');
  });

  it('should return the default host when called without an environment parameter', () => {
    // Calling the function to be tested
    const result = Utils.getTripH5Host();

    // Assertion
    expect(result).toBe('https://hk.trip.com/');
  });

  describe('getComponentByChannel', () => {
    const config = {
      [COMPONENT_CHANNEL.COMMON]: 'Common Component',
      [COMPONENT_CHANNEL.OSD]: 'OSD Component',
      [COMPONENT_CHANNEL.ISD]: 'ISD Component',
    };

    it('should return the common component when isCommonChannel is true', () => {
      // Mocking the input parameters
      const isCommonChannel = true;

      // Calling the function to be tested
      const result = Utils.getComponentByChannel(config, isCommonChannel);

      // Assertion
      expect(result).toBe('Common Component');
    });

    it('should return the OSD component when Utils.isCtripOsd() returns true', () => {
      // Mocking the input parameters and Utils.isCtripOsd() method
      const isCommonChannel = false;
      jest.spyOn(Utils, 'isTrip').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

      // Calling the function to be tested
      const result = Utils.getComponentByChannel(config, isCommonChannel);

      // Assertion
      expect(result).toBe('OSD Component');
    });

    it('should return the ISD component when Utils.isCtripIsd() returns true', () => {
      // Mocking the input parameters and Utils.isCtripIsd() method
      const isCommonChannel = false;
      jest.spyOn(Utils, 'isTrip').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

      // Calling the function to be tested
      const result = Utils.getComponentByChannel(config, isCommonChannel);

      // Assertion
      expect(result).toBe('ISD Component');
    });

    it('should return the common component as fallback when no matching channel is found', () => {
      // Mocking the input parameters and all Utils methods
      const isCommonChannel = false;
      jest.spyOn(Utils, 'isTrip').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

      // Calling the function to be tested
      const result = Utils.getComponentByChannel(config, isCommonChannel);

      // Assertion
      expect(result).toBe('Common Component');
    });
  });

  describe('validateIsServerError', () => {
    const errorInfo1 = 'SERVER_ERROR_502';
    const errorInfo2 = 'Client error occurred';

    it('should return true when errorInfo is falsy', () => {
      // Calling the function to be tested with falsy errorInfo
      const result = Utils.validateIsServerError(null);

      // Assertion
      expect(result).toBe(true);
    });

    it('should return false when errorInfo does not include any server error codes', () => {
      // Calling the function to be tested with errorInfo that does not include any server error codes
      const result = Utils.validateIsServerError(errorInfo2);

      // Assertion
      expect(result).toBe(false);
    });

    it('should return true when errorInfo includes server error codes', () => {
      // Calling the function to be tested with errorInfo that includes server error codes
      const result = Utils.validateIsServerError(errorInfo1);

      // Assertion
      expect(result).toBe(true);
    });
  });

  describe('getDayDiff', () => {
    test('should return the correct number of days when startDate is greater than endDate', () => {
      const startDate = dayjs('2022-01-01');
      const endDate = dayjs('2021-12-31');

      expect(Utils.getDayDiff(startDate, endDate)).toBe(1);
    });

    test('should return the correct number of days when endDate is greater than startDate', () => {
      const startDate = dayjs('2021-12-31');
      const endDate = dayjs('2022-01-01');

      expect(Utils.getDayDiff(startDate, endDate)).toBe(1);
    });

    test('should return the correct number of days when startDate and endDate are the same', () => {
      const startDate = dayjs('2022-01-01');
      const endDate = startDate;

      expect(Utils.getDayDiff(startDate, endDate)).toBe(0);
    });
  });

  describe('getClientType', () => {
    afterEach(() => {
      jest.restoreAllMocks(); // Restores all mocks after each test
    });

    test('should return qunar when isQunarApp is true', () => {
      jest.spyOn(Utils, 'isQunarApp').mockReturnValue(true);

      const result = Utils.getClientType();

      expect(result).toBe(ClientType.qunar);
    });
  });
});
