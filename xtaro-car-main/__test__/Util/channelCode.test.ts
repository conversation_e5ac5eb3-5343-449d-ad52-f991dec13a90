import Platform from '@c2x/apis/Platform';
import BbkChannel from '../../src/pages/xcar/Common/src/Utils';
import Utils from '../../src/pages/xcar/Util/Utils';
import CreateChannelCode from '../../src/pages/xcar/Util/channelCode';

enum TechCode {
  Web = 0,
  Native,
  ReactNative,
  Server,
}

enum OSCode {
  iOS = 0,
  Android,
  Windows,
  MacOS,
  Linux,
  Unix,
}

enum AppCode {
  Browser = 0, // 浏览器(不区分具体哪个浏览器)
  Ctrip, // 携程
  Trip,
  Qunar, // 去哪儿
  Bestone, // 百事通
  ZhiXing, // 智行
  TieYou, // 铁友
  CarRental, // 携程租车App
  CarRentalWelfareEdition, // 租车福利版App
  CarRentalExtremeEdition, // 租车极速版App
  QunarCarRental, // 去哪儿租车App
  WeChat, // 微信
  QQ, // QQ
  AliPay, // 支付宝
  Baidu, // 百度
  Toutiao, // 今日头条
  AndroidQuickApp, // 快应用
}

enum MiniAppCode {
  MiniApp = 0, // 小程序
  CtripMiniApp, // 携程主小程序
  CarRentalMiniApp, // 携程租车小程序
  XiCuiMiniApp, // 携程西翠小程序
  QunarCarRentalMiniApp, // 去哪儿租车小程序
  LvgoMiniApp, // 旅购小程序
}

enum HostCode {
  Ctrip = 0,
  Trip,
  Qunar, // 去哪儿
  Bestone, // 百事通
  ZhiXing, // 智行
  TieYou, // 铁友
  Lvgo, // 旅购
}

enum BusinessCode {
  PageForMobileUser = 1,
  WebPageForPCOnlineUser,
  WebPageForPCOfflineUser,
  WebPageForPCVbkUser,
}

enum SubBusinessCode {
  isd = 0,
  osd = 1,
  trip = 2,
}

describe('CreateChannelCode', () => {
  test('CreateChannelCode ctrip isd', () => {
    const params = {
      appCode: undefined,
      subBusinessCode: undefined,
    };
    const expected = {
      'trip-tech-code': TechCode.ReactNative,
      'trip-os-code': Platform.OS === 'ios' ? OSCode.iOS : OSCode.Android,
      'trip-app-code': AppCode.Ctrip,
      'trip-business-code': BusinessCode.PageForMobileUser,
      'trip-subBusiness-code': SubBusinessCode.isd,
    };
    jest.spyOn(Utils, 'isCtripApp').mockReturnValue(true);
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const result = CreateChannelCode(params);
    expect(result).toMatchObject(expected);
  });
  test('CreateChannelCode ctrip osd', () => {
    const params = {
      appCode: undefined,
      subBusinessCode: undefined,
    };
    const expected = {
      'trip-tech-code': TechCode.ReactNative,
      'trip-os-code': Platform.OS === 'ios' ? OSCode.iOS : OSCode.Android,
      'trip-app-code': AppCode.Ctrip,
      'trip-business-code': BusinessCode.PageForMobileUser,
      'trip-subBusiness-code': SubBusinessCode.osd,
    };
    jest.spyOn(Utils, 'isCtripApp').mockReturnValue(true);
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const result = CreateChannelCode(params);
    expect(result).toMatchObject(expected);
  });
  test('CreateChannelCode Qunar Web', () => {
    const params = {
      appCode: undefined,
      subBusinessCode: undefined,
    };
    const expected = {
      'trip-tech-code': TechCode.ReactNative,
      'trip-os-code': Platform.OS === 'ios' ? OSCode.iOS : OSCode.Android,
      'trip-app-code': AppCode.Qunar,
      'trip-business-code': BusinessCode.PageForMobileUser,
      'trip-subBusiness-code': SubBusinessCode.isd,
    };
    jest.spyOn(Utils, 'isCtripApp').mockReturnValue(false);
    jest.spyOn(Utils, 'isQunarApp').mockReturnValue(true);
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const result = CreateChannelCode(params);
    expect(result).toMatchObject(expected);
  });
  test('CreateChannelCode ZhiXing', () => {
    const params = {
      appCode: AppCode.ZhiXing,
      subBusinessCode: undefined,
    };
    const expected = {
      'trip-tech-code': TechCode.ReactNative,
      'trip-os-code': OSCode.iOS,
      'trip-app-code': AppCode.ZhiXing,
      'trip-business-code': BusinessCode.PageForMobileUser,
      'trip-subBusiness-code': undefined,
    };
    jest.spyOn(Utils, 'isCtripApp').mockReturnValue(false);
    jest.spyOn(Utils, 'isTrip').mockReturnValue(false);
    jest.spyOn(Utils, 'isQunarApp').mockReturnValue(false);
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(false);
    jest.spyOn(Utils, 'isTrip').mockReturnValue(false);

    const result = CreateChannelCode(params);
    expect(result).toMatchObject(expected);
  });
});
