import { AppContext, Channel } from '../../src/pages/xcar/Util/Index';
import { PageIdIsd, PageIdOsd } from '../../src/pages/xcar/Constants/PageId/Index';
import { Utils } from '../../src/pages/xcar/Util/Index';
import { APP_TYPE } from '../../src/pages/xcar/Constants/Platform';

describe('测试 Channel', () => {
  test('测试 getPageId Isd环境', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    expect(Channel.getPageId()).toEqual(PageIdIsd);
  });

  test('测试 getPageId Osd环境', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Channel.getPageId()).toEqual(PageIdOsd);
  });
});
