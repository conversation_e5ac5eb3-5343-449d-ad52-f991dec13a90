import { APP_TYPE } from '../../src/pages/xcar/Constants/Platform';
import { logBasicInfo, getPageNameByID } from '../../src/pages/xcar/Util/AppBaseInfoMap';
import AppContext from '../../src/pages/xcar/Util/AppContext';

jest.mock('../../src/pages/xcar/State/Store', () => {
  return {
    getStore: () => ({
      getState: () => ({
        CountryInfo: {},
        LocationAndDate: {},
        DriverAgeAndNumber: {},
      })
    })
  }
})

describe('logBasicInfo', () => {
  test('字段是否取到', () => {
    AppContext.setUrlQuery({
      CRNModuleName: 'rn_car_main'
    });
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    const baseInfo = logBasicInfo();
    expect(baseInfo.CRNModuleName).toEqual('rn_car_main');
    expect(baseInfo.logIdentification).toEqual('FRONT_END_APP');
    expect(baseInfo.sourceFrom).toEqual('ISD_C_APP');
  })

  test('测试值不包含 true or false', () => {
    AppContext.setUrlQuery({
      CRNModuleName: 'rn_car_main'
    });
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    const baseInfo = logBasicInfo();
    for (const key in baseInfo) {
      if (Object.prototype.hasOwnProperty.call(baseInfo, key)) {
        const value = baseInfo[key];
        // console.log('==value', key, value);
        expect(value).not.toEqual(false);
        expect(value).not.toEqual(true);
      }
    }
  })
})

describe('getPageNameByID', () => {
  const mockMap = [{
    pageID: '222025',
    expected: 'OrderDetail',
  },{
    pageID: '10650004922',
    expected: 'DriverList',
  },{
    pageID: '',
    expected: '',
  },]
  test.each(mockMap)('%p', ({ pageID, expected }) => {
    expect(getPageNameByID(pageID)).toEqual(expected);
  });
})
