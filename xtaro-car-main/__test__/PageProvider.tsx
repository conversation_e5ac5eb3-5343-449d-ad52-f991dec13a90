/* eslint-disable @typescript-eslint/default-param-last */
/* eslint-disable import/no-extraneous-dependencies */
import React from 'react';
import produce from 'immer';
import { Provider } from 'react-redux';
import {
  render,
  act,
  xFireEvent as fireEvent,
  RenderResult,
} from '@ctrip/xtaro-mock/testing-library';
import { initializeStore } from '../src/pages/xcar/State/Store';
import {
  setReduxState,
  initializeEnv,
} from '../src/pages/xcar/State/__Environment/Actions';
import { AppContext } from '../src/pages/xcar/Util/Index';
import { waitRender } from './testHelpers';
import { getStore, lazyLoadReduxStore } from '../src/pages/xcar/State/StoreRef';
export const TEST_TIMEOUT = 20 * 1000;

export const updateStore = state => {
  if (state) {
    const store = getStore();
    const curState = store.getState();
    const updateState = { ...curState };
    Object.keys(state).forEach(key => {
      updateState[key] = {
        ...updateState[key],
        ...state[key],
      };
    });
    store.dispatch(setReduxState(updateState));
  }
};

/**
 * updateReduxState 更新应用Store，支持直接修改，无需解构
 * @param updateCb 更新回调
 */
export const updateReduxState = updateCb => {
  const store = getStore();
  const newState = produce(store.getState(), updateCb);
  store.dispatch(setReduxState(newState));
  return store.getState();
};

const getCurrentTestId = () => {
  const testName = expect.getState().currentTestName;
  const expectTestId = testName.match(/{(\d+)}/)?.[1];
  return Number(expectTestId) || '';
};

export async function createPage<Props>(
  WrappedComponent: React.ComponentType<Props>,
  props,
  initialState: any = null,
): Promise<RenderResult> {
  // 基础默认 Props 透传
  const defaultProps = {
    navigation: {
      getCurrentRoutes: () => {},
    },
    ...props,
  };
  // @ts-ignore
  const APP_TYPE = props?.app?.urlQuery?.apptype;
  // @ts-ignore
  global.TESTING_APP_TYPE = APP_TYPE;

  AppContext.setCarEnv({ appType: APP_TYPE });

  const testId = getCurrentTestId();

  // 初始化Store
  // @ts-ignore
  if (!global.TA_CASE_ID_STORE[testId]) {
    initializeStore(props);
    lazyLoadReduxStore();
    // @ts-ignore
    global.TA_CASE_ID_STORE[testId] = true;
  }

  const store = getStore();
  store.dispatch(initializeEnv({}));

  updateStore(initialState);
  const PageContainer = (
    <Provider store={store}>
      <WrappedComponent {...defaultProps} />
    </Provider>
  );
  let Page: RenderResult;
  await waitRender(() => {
    Page = render(PageContainer);
  });
  // @ts-ignore
  return Page;
}

interface InterTestName {
  /**
   * 用例ID，支持单个与多个TestID形式，多个通过数组形式传入，第一个TestID 为主ID，用于接口数据请求的唯一ID
   */
  testId: number | number[];
  /**
   * testHub 用例对应标题
   */
  name: string;
}

export const createInterTestName = ({ testId, name }: InterTestName) => {
  let testNameString;
  if (Array.isArray(testId)) {
    const testIds = testId.map(id => `{${id}}`).join(' ');
    testNameString = `${testIds} ${name}`;
  } else {
    testNameString = `{${testId}} ${name}`;
  }
  return testNameString;
};

export const pressWithTestId = async (Page: RenderResult, testId: string) => {
  await act(async () => {
    const btn = await Page.findByTestId(testId);
    fireEvent.press(btn);
  });
};
