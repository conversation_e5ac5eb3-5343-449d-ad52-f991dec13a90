/* eslint-disable import/no-dynamic-require */
/* eslint-disable import/no-extraneous-dependencies */
/**
 * 测试辅助工具
 */
import Image from '@c2x/components/Image';;
import { runSaga } from 'redux-saga';
import { testSaga } from 'redux-saga-test-plan';
import {
  act,
  RenderResult,
  fireEvent,
  waitFor,
} from '@testing-library/react';

export enum EffectType {
  put = 'put',
}

export interface ExceptType {
  effectType?: EffectType;
  action: any;
}

export const takeEveryGeneratorFunction = takeEveryFunc => {
  return takeEveryFunc().next().value?.payload.args[1];
};

/**
 * 测试saga的执行
 * @param sagaFunc saga函数
 * @param params saga函数参数
 * @param excepts 执行saga后的期望副作用
 */
export const testSagaGeneratorFunction = (
  sagaFunc,
  params,
  excepts: Array<ExceptType>,
) => {
  let nextEffext = testSaga(sagaFunc, params).next();
  excepts.map(item => {
    const { effectType = EffectType.put, action } = item;
    if (action) {
      nextEffext = nextEffext[effectType](action).next();
    }
  });
  nextEffext.isDone();
};

/**
 * 记录saga执行
 * @param {*} takeEverySaga
 * @param {*} initialAction
 * @param {*} state
 * @returns
 */
export const recordSaga = async (
  takeEverySaga,
  params: {
    action: any;
    state: any;
  },
) => {
  const { action, state } = params;
  const sagaGenerator = takeEveryGeneratorFunction(takeEverySaga);
  const dispatched: any = [];

  await runSaga(
    {
      dispatch: putAction => dispatched.push(putAction),
      getState: () => state,
    },
    sagaGenerator,
    action,
  ).toPromise();

  return dispatched;
};

export const mockReturnArrayValue = array => {
  let result = jest.fn();
  array.map(item => {
    result = result.mockImplementationOnce(() => item);
  });
  return result;
};

/**
 * 点击元素
 * @param Page
 * @param testId
 */
export const pressWithTestId = async (Page: RenderResult, testId: string) => {
  await act(async () => {
    const btn = await Page.findByTestId(testId);
    fireEvent.press(btn);
  });
};

/**
 *  查找图片
 * @param Page
 * @param url
 * @returns 图片
 */
export const findPageImage = async (Page: RenderResult, url: string) => {
  const allImages = Page.container.findAllByType(Image);
  const targetImage = allImages?.find(image =>
    image.props.source.uri.includes(url),
  );
  return targetImage;
};

/**
 * 封装 waitFor 方法
 */
export const waitRender = async (callback, timeout = 5000) => {
  await waitFor(callback, { timeout });
};

/**
 * 封装 renderWithAction 方法
 * @param action 触发事件
 * @param update 更新组件回调
 * @param timeout 超时时间
 * @returns null
 */
export const renderWithAction = async ({
  action = () => {}, // 触发事件
  expect = () => {}, // 更新组件回调
  timeout = 5000,
}) => {
  await act(action);
  await waitRender(expect, timeout);
};

/**
 * 延迟执行
 * @param delay 延迟时间
 * @returns 延迟执行异步函数
 */
export const delayPromise = (delay = 2000) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(null);
    }, delay);
  });
};
