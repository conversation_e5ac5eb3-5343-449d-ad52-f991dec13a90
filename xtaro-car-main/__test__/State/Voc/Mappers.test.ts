import getPhoneList from '../../../src/pages/xcar/State/Voc/Mapper';

describe('Voc Mappers getPhoneList', () => {
  const phoneInfo1 = "13916123343或 13916123340 ";
  const phoneInfo2 = "";
  const mockData = [
    {
      data: phoneInfo1,
      expected: [{
        name: '',
        tels: ["13916123343", "13916123340"],
      }],
    },
    {
      data: phoneInfo2,
      expected: [{
        name: '',
        tels: [""],
      }],
    },
    {
      data: null,
      expected: null,
    }
  ];
  test.each(mockData)('Voc Mappers getPhoneList', ({ data, expected }) => {
    expect(getPhoneList(data)).toEqual(expected);
  })
});
