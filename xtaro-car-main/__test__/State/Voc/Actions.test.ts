import {
  queryQuestionnaire,
  setVocModalVisible,
  saveQuestionnaire,
} from '../../../src/pages/xcar/State/Voc/Actions';
import {
  FETCH_QUERY_QUESTIONNAIRE,
  SET_VOC_MODAL_VISIBLE,
  FETCH_SAVE_QUESTIONNAIRE,
} from '../../../src/pages/xcar/State/Voc/Types';

describe('Voc Actions queryQuestionnaire', () => {
  const data: any = {
    name: 12345,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: FETCH_QUERY_QUESTIONNAIRE,
        data,
      },
    },
  ];
  test.each(mockStateMap)('queryQuestionnaire check', ({ data, expected }) => {
    const result = queryQuestionnaire(data);
    expect(result).toEqual(expected);
  });
});

describe('Voc Actions setVocModalVisible', () => {
  const data: any = {
    name: 12345,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: SET_VOC_MODAL_VISIBLE,
        data,
      },
    },
  ];
  test.each(mockStateMap)('setVocModalVisible check', ({ data, expected }) => {
    const result = setVocModalVisible(data);
    expect(result).toEqual(expected);
  });
});

describe('Voc Actions saveQuestionnaire', () => {
  const data: any = {
    name: 12345,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: FETCH_SAVE_QUESTIONNAIRE,
        data,
      },
    },
  ];
  test.each(mockStateMap)('saveQuestionnaire check', ({ data, expected }) => {
    const result = saveQuestionnaire(data);
    expect(result).toEqual(expected);
  });
});
