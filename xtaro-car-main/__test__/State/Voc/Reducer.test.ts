import VocReducer, { getInitalState } from '../../../src/pages/xcar/State/Voc/Reducer';
import {
  FETCH_QUERY_QUESTIONNAIRE,
  FETCH_QUERY_QUESTIONNAIRE_CALLBACK,
  SET_VOC_MODAL_VISIBLE,
} from '../../../src/pages/xcar/State/Voc/Types';

describe('Voc Reducer Test', () => {
  const initState = getInitalState();

  test('Voc Reducer Init', () => {
    expect(VocReducer(undefined, {})).toEqual(initState);
  });

  describe('Voc Reducer FETCH_QUERY_QUESTIONNAIRE', () => {
    const mockActionMap = [
      {
        expected: {
          ...initState,
          questionnaires: [],
          vocModalVisible: false,
        },
      },
    ];
    test.each(mockActionMap)(
      'FETCH_QUERY_QUESTIONNAIRE check',
      ({ expected }) => {
        expect(
          VocReducer(initState, {
            type: FETCH_QUERY_QUESTIONNAIRE,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('Voc Reducer FETCH_QUERY_QUESTIONNAIRE_CALLBACK', () => {
    const questionnaires = [];
    const mockActionMap = [
      {
        data: {
          questionnaires,
        },
        expected: {
          ...initState,
          questionnaires,
          vocModalVisible: true,
        },
      },
    ];
    test.each(mockActionMap)(
      'FETCH_QUERY_QUESTIONNAIRE_CALLBACK check',
      ({ data, expected }) => {
        expect(
          VocReducer(initState, {
            type: FETCH_QUERY_QUESTIONNAIRE_CALLBACK,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('Voc Reducer SET_VOC_MODAL_VISIBLE', () => {
    const vocModalVisible = true;
    const mockActionMap = [
      {
        data: {
          vocModalVisible,
        },
        expected: {
          ...initState,
          vocModalVisible,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_VOC_MODAL_VISIBLE check',
      ({ data, expected }) => {
        expect(
          VocReducer(initState, {
            type: SET_VOC_MODAL_VISIBLE,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });
});
