import { runSaga } from 'redux-saga';
import dayjs from '../../../src/pages/xcar/Common/src/Dayjs/src';
import {
  fetchQueryQuestionnaire,
  fetchSaveQuestionnaire,
  composeVocModalVisible,
} from '../../../src/pages/xcar/State/Voc/Logic';
import { takeEveryGeneratorFunction } from '../../testHelpers';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';
import {
  FETCH_QUERY_QUESTIONNAIRE,
  FETCH_SAVE_QUESTIONNAIRE,
  FETCH_QUERY_QUESTIONNAIRE_CALLBACK,
} from '../../../src/pages/xcar/State/Voc/Types';
describe('composeVocModalVisible', () => {
  test('测试composeVocModalVisible', async () => {
    const data = [
      {
        delayInterval: 4,
      },
    ];
    const After5m = dayjs().add(5, 'm').format('YYYY-MM-DD HH:mm');
    const Before5m = dayjs().subtract(5, 'm').format('YYYY-MM-DD HH:mm');
    const Before1m = dayjs().subtract(1, 'm').format('YYYY-MM-DD HH:mm');
    expect(composeVocModalVisible(data, Before5m)).toBe(0);
    expect(composeVocModalVisible(data, Before1m)).not.toBe(0);
    expect(composeVocModalVisible(data, After5m)).toBe(0);
  });
});
describe('Voc Logic fetchQueryQuestionnaire', () => {
  const res = {
    ResponseStatus: {
      Timestamp: '2021-05-07 16:45:04',
      Ack: 'Success',
      Errors: [],
      Extension: [
        {
          Id: 'CLOGGING_TRACE_ID',
          Value: '9039677794474693147',
        },
        {
          Id: 'RootMessageId',
          Value: '100025527-0a063db3-450104-185030',
        },
      ],
    },
    baseResponse: {
      isSuccess: true,
      code: 'unknown',
      returnMsg: 'success',
      requestId: '',
      cost: 14,
    },
    questionnaires: [
      {
        questionId: 10001,
        content: '您取到的车辆内饰是否已清理干净？',
        type: 0,
        answers: [
          {
            code: 0,
            content: '没有清理',
          },
          {
            code: 1,
            content: '已清理干净',
          },
        ],
      },
      {
        questionId: 10002,
        content: '取车前，门店是否主动联系您以确认订单信息？',
        type: 0,
        answers: [
          {
            code: 0,
            content: '没有联系',
          },
          {
            code: 1,
            content: '联系了',
          },
        ],
      },
      {
        questionId: 10003,
        content: '门店通过以下哪种方式向您收取违章押金？',
        type: 1,
        answers: [
          {
            code: 0,
            content: 'POS机',
          },
          {
            code: 1,
            content: '微信/支付宝等',
          },
        ],
      },
    ],
  };
  test('测试正常调用', async () => {
    const dispatched: any = [];
    const fetchParams: any = {
      orderId: '1231234124',
    };
    const actionMock = {
      type: FETCH_QUERY_QUESTIONNAIRE,
      data: fetchParams,
    };
    const logicFunc = takeEveryGeneratorFunction(fetchQueryQuestionnaire);
    jest
      .spyOn(CarFetch, 'queryQuestionnaire')
      .mockImplementation(() => Promise.resolve(res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          OrderDetail: {
            orderBaseInfo: {
              orderStatusCtrip: 'CAR_COMMITTED',
            },
          },
        }),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: FETCH_QUERY_QUESTIONNAIRE_CALLBACK,
      data: {
        questionnaires: res.questionnaires,
      },
    });
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          OrderDetail: {
            orderBaseInfo: {
              orderStatusCtrip: 'CAR_COMPLETED',
            },
          },
        }),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[1]).toEqual({
      type: FETCH_QUERY_QUESTIONNAIRE_CALLBACK,
      data: {
        questionnaires: res.questionnaires,
      },
    });
  });

  test('测试数据异常调用', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: FETCH_QUERY_QUESTIONNAIRE,
    };
    const logicFunc = takeEveryGeneratorFunction(fetchQueryQuestionnaire);
    jest
      .spyOn(CarFetch, 'queryQuestionnaire')
      .mockReturnValue(Promise.resolve(undefined));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });

  test('接口异常调用', async () => {
    const dispatched: any = [];
    const fetchParams: any = {
      orderId: '1231234124',
    };
    const actionMock = {
      type: FETCH_QUERY_QUESTIONNAIRE,
      data: fetchParams,
    };
    const exceptionError = new Error('fetchQueryQuestionnaire exception');
    const logicFunc = takeEveryGeneratorFunction(fetchQueryQuestionnaire);
    jest.spyOn(CarFetch, 'queryQuestionnaire').mockReturnValue(
      new Promise(() => {
        throw exceptionError;
      }),
    );
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });
});

describe('Voc Logic fetchSaveQuestionnaire', () => {
  test('测试正常调用', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: FETCH_SAVE_QUESTIONNAIRE,
    };
    const logicFunc = takeEveryGeneratorFunction(fetchSaveQuestionnaire);
    jest
      .spyOn(CarFetch, 'saveQuestionnaire')
      .mockImplementation(() => Promise.resolve({}));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });
});
