import {
  getQuestionnaires,
  getVocModalVisible,
  getIsLoading,
} from '../../../src/pages/xcar/State/Voc/Selectors';

describe('Voc Selectors getQuestionnaires', () => {
  const questionnaires = [];
  const mockStateMap = [
    {
      state: {
        Voc: {},
      },
      expected: undefined,
    },
    {
      state: {
        Voc: null,
      },
      expected: undefined,
    },
    {
      state: {
        Voc: {
          questionnaires,
        },
      },
      expected: questionnaires,
    },
  ];
  test.each(mockStateMap)('getQuestionnaires check', ({ state, expected }) => {
    const data = getQuestionnaires(state);
    expect(data).toEqual(expected);
  });
});

describe('Voc Selectors getVocModalVisible', () => {
  const vocModalVisible = true;
  const mockStateMap = [
    {
      state: {
        Voc: {},
      },
      expected: undefined,
    },
    {
      state: {
        Voc: null,
      },
      expected: undefined,
    },
    {
      state: {
        Voc: {
          vocModalVisible,
        },
      },
      expected: vocModalVisible,
    },
  ];
  test.each(mockStateMap)('getVocModalVisible check', ({ state, expected }) => {
    const data = getVocModalVisible(state);
    expect(data).toEqual(expected);
  });
});

describe('Voc Selectors getFaqList', () => {
  const faqList = [];
  const mockStateMap = [
    {
      state: {
        Voc: {},
      },
      expected: undefined,
    },
    {
      state: {
        Voc: null,
      },
      expected: undefined,
    },
    {
      state: {
        Voc: {
          faqList,
        },
      },
      expected: faqList,
    },
  ];
});

describe('Voc Selectors getIsLoading', () => {
  const isLoading = true;
  const mockStateMap = [
    {
      state: {
        Voc: {},
      },
      expected: undefined,
    },
    {
      state: {
        Voc: null,
      },
      expected: undefined,
    },
    {
      state: {
        Voc: {
          isLoading,
        },
      },
      expected: isLoading,
    },
  ];
  test.each(mockStateMap)('getIsLoading check', ({ state, expected }) => {
    const data = getIsLoading(state);
    expect(data).toEqual(expected);
  });
});
