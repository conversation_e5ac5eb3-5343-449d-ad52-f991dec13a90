import { ModalTypes } from '../../../src/pages/xcar/Constants/OrderOnlineAuth';
import { LisenceCardFaceType } from '../../../src/pages/xcar/State/OnlineAuth/FuntionTypes';
import Reducer, { getInitalState } from '../../../src/pages/xcar/State/OnlineAuth/Reducer';
import { SET_LOADING, SET_ORDER_AUTH_INFO, SET_MODAL, SET_ISRETRY_CARDFACE } from '../../../src/pages/xcar/State/OnlineAuth/Types';


describe('Reducer Test', () => {
  const initState = getInitalState();

  test('Reducer Init', () => {
    expect(Reducer(undefined, {})).toEqual(initState);
  });

  test('SET_LOADING', () => {
    expect(
      Reducer(initState, {
        type: SET_LOADING,
        data: true,
      }),
    ).toEqual({
      ...initState,
      isLoading: true,
    });
  });

  test('SET_ORDER_AUTH_INFO', () => {
    expect(
      Reducer(initState, {
        type: SET_ORDER_AUTH_INFO,
        data: {
          supportInfo: {
            "authStatus": 0,
            "certificationStatus": 200,
            "defaultAuthStatus": false,
            "isShow": true,
            "showTitle": "身份证及驾照在线认证",
            "buttonText": "去授权",
            "guideText": "您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续"
          },
        },
      }),
    ).toEqual({
      ...initState,
      orderAuthInfo: {
        supportInfo: {
          "authStatus": 0,
          "certificationStatus": 200,
          "defaultAuthStatus": false,
          "isShow": true,
          "showTitle": "身份证及驾照在线认证",
          "buttonText": "去授权",
          "guideText": "您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续"
        },
      },
    });
  });

  test('SET_MODAL', () => {
    expect(
      Reducer(initState, {
        type: SET_MODAL,
        name: ModalTypes.AUTH_SWITCH,
        data: {
          visible: false,
        },
      }),
    ).toEqual({
      ...initState,
      modals: {
        [ModalTypes.CONFIRM]: {},
        [ModalTypes.AUTH_SWITCH]: {
          visible: false,
        },
        [ModalTypes.OCR_TIP]: {},
      },
    });
  });

  test('SET_ISRETRY_CARDFACE', () => {
    expect(
      Reducer(initState, {
        type: SET_ISRETRY_CARDFACE,
        data: {
          [LisenceCardFaceType.IdCardAType]: true
        },
      }),
    ).toEqual({
      ...initState,
      retryingCardFace: {
        [LisenceCardFaceType.IdCardAType]: true,
        [LisenceCardFaceType.IdCardBType]: false,
        [LisenceCardFaceType.DriverAType]: false,
        [LisenceCardFaceType.DriverBType]: false,
      },
    });
  });
})
