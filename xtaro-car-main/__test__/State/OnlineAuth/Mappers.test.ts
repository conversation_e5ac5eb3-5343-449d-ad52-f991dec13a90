import { AddType, CertificationStatus, LisenceAuthStatusType, LisenceCardFaceType } from '../../../src/pages/xcar/State/OnlineAuth/FuntionTypes';
import {
  getExpiryDate,
  mapLisenceByCardFace,
  getLisenceSaveSuccessTip,
  cardFaceRetryTipMap,
  getOnlineAuthEntryStatus,
  getTraceAuthStatus,
  retryBackTipConfig,
} from '../../../src/pages/xcar/State/OnlineAuth/Mappers';
import { texts } from '../../../src/pages/xcar/ComponentBusiness/LisenceAuth/src/Texts';

describe('getExpiryDate', () => {
  const caseConfig = [
    {
      date: '9999-12-31',
      expected: '有效期为长期',
    },
    {
      date: '1111-01-01',
      expected: '',
    },
    {
      date: '2022-02-28',
      expected: '有效期至2022-02-28',
    },
    {
      date: '2024-10-28',
      expected: '有效期至2024-10-28',
    },
  ]
  test.each(caseConfig)('测试获取证件有效期文案边界值', ({ date, expected }) => {
    expect(getExpiryDate(date)).toEqual(expected);
  })
})

describe('mapLisenceByCardFace', () => {
  const caseConfig = [
    {
      cardFaceType: LisenceCardFaceType.IdCardAType,
      expected: '1',
    },
    {
      cardFaceType: LisenceCardFaceType.IdCardBType,
      expected: '1',
    },
    {
      cardFaceType: LisenceCardFaceType.DriverAType,
      expected: '2',
    },
    {
      cardFaceType: LisenceCardFaceType.DriverAType,
      expected: '2',
    },
  ]
  test.each(caseConfig)('测试驾照类型转换', ({ cardFaceType, expected }) => {
    expect(mapLisenceByCardFace(cardFaceType)).toEqual(expected);
  })
})


describe('getLisenceSaveSuccessTip', () => {
  const caseConfig = [
    {
      params: {
        cardFaceType: LisenceCardFaceType.IdCardAType,
        addType: AddType.Retry,
        resultMap: {
          "1": false,
          "2": false,
          "3": false,
          "4": false
        },
      },
      expected: cardFaceRetryTipMap[LisenceCardFaceType.IdCardAType],
    },
    {
      params: {
        cardFaceType: LisenceCardFaceType.IdCardAType,
        addType: AddType.First,
        resultMap: {
          "1": false,
          "2": false,
          "3": false,
          "4": false
        },
      },
      expected: texts.authSuccessIdCardA,
    },
    {
      params: {
        cardFaceType: LisenceCardFaceType.IdCardBType,
        addType: AddType.First,
        resultMap: {
          "1": false,
          "2": false,
          "3": false,
          "4": false
        },
      },
      expected: texts.authSuccessIdCardB,
    },
    {
      params: {
        cardFaceType: LisenceCardFaceType.IdCardBType,
        addType: AddType.First,
        resultMap: {
          "1": false,
          "2": false,
          "3": true,
          "4": true
        },
      },
      expected: texts.authSuccess,
    },
    {
      params: {
        cardFaceType: LisenceCardFaceType.DriverAType,
        addType: AddType.First,
        resultMap: {
          "1": false,
          "2": false,
          "3": true,
          "4": true
        },
      },
      expected: texts.authSuccessDriverA,
    },
    {
      params: {
        cardFaceType: LisenceCardFaceType.DriverBType,
        addType: AddType.First,
        resultMap: {
          "1": false,
          "2": false,
          "3": true,
          "4": true
        },
      },
      expected: texts.authSuccessDriverB,
    },
    {
      params: {
        cardFaceType: LisenceCardFaceType.DriverBType,
        addType: AddType.First,
        resultMap: {
          "1": true,
          "2": true,
          "3": true,
          "4": true
        },
      },
      expected: texts.authSuccess,
    },
    {
      params: {
        cardFaceType: '',
        addType: AddType.First,
        resultMap: {
          "1": true,
          "2": true,
          "3": true,
          "4": true
        },
      },
      expected: '',
    },    
  ]
  test.each(caseConfig)('测试证件上传成功提示信息', ({ params, expected }) => {
    const { cardFaceType, addType, resultMap } = params;
    expect(getLisenceSaveSuccessTip(cardFaceType, addType, resultMap)).toEqual(expected);
  })
})


describe('getOnlineAuthEntryStatus', () => {
  const caseConfig = [
    {
      params: {
        certificationStatus: CertificationStatus.Finished,
        authStatus: LisenceAuthStatusType.HasAuth,
      },
      expected: 4,
    },
    {
      params: {
        certificationStatus: CertificationStatus.UnFinish,
        authStatus: LisenceAuthStatusType.UnAuth,
      },
      expected: 1,
    },
    {
      params: {
        certificationStatus: CertificationStatus.PartialFinish,
        authStatus: LisenceAuthStatusType.UnAuth,
      },
      expected: 2,
    },
    {
      params: {
        certificationStatus: CertificationStatus.Finished,
        authStatus: LisenceAuthStatusType.UnAuth,
      },
      expected: 3,
    },
    {
      params: {
        certificationStatus: '',
        authStatus: LisenceAuthStatusType.UnAuth,
      },
      expected: 0,
    },    
  ]
  test.each(caseConfig)('测试入口状态埋点返回值', ({ params, expected }) => {
    const { certificationStatus, authStatus } = params;
    expect(getOnlineAuthEntryStatus(certificationStatus, authStatus)).toEqual(expected);
  })
})

describe('getTraceAuthStatus', () => {
  const caseConfig = [
    {
      params: {
        defaultAuthStatus: true,
        authStatus: LisenceAuthStatusType.HasAuth,
      },
      expected: 2,
    },
    {
      params: {
        defaultAuthStatus: false,
        authStatus: LisenceAuthStatusType.HasAuth,
      },
      expected: 1,
    },
    {
      params: {
        defaultAuthStatus: null,
        authStatus: LisenceAuthStatusType.UnAuth,
      },
      expected: 0,
    },
    {
      params: {
        defaultAuthStatus: false,
        authStatus: '',
      },
      expected: 0,
    },     
  ]
  test.each(caseConfig)('测试入口状态埋点返回值', ({ params, expected }) => {
    const { defaultAuthStatus, authStatus } = params;
    expect(getTraceAuthStatus(defaultAuthStatus, authStatus)).toEqual(expected);
  })
})

describe('retryBackTipConfig', () => {
  const caseConfig = [
    {
      cardFaceType: LisenceCardFaceType.IdCardAType,
      expected: {
        title: texts.idcardALeaveTip,
        btnText: [texts.continueAuth, texts.leave],
        type: LisenceCardFaceType.IdCardAType,
      },
    },    
  ]
  test.each(caseConfig)('测试重试提示文案', ({ cardFaceType, expected }) => {
    expect(retryBackTipConfig(cardFaceType )).toEqual(expected);
  })
})