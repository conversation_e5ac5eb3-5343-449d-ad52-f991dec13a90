import { getLisenceAuthInfo, setOrderAuthInfo, setModal, setRetryingCardFace } from '../../../src/pages/xcar/State/OnlineAuth/Actions';
import { AddType, LisenceCardFaceType, UpdateAuthType } from '../../../src/pages/xcar/State/OnlineAuth/FuntionTypes';
import { getLisenceAuthInfoLogic, lisenceOCRCheckSaveLogic, updateLisenceAuthLogic } from '../../../src/pages/xcar/State/OnlineAuth/Logic';
import { GET_LISENCE_AUTH_INFO } from '../../../src/pages/xcar/State/OnlineAuth/Types';
import { CarFetch, CarStorage } from '../../../src/pages/xcar/Util/Index';
import { recordSaga } from '../../testHelpers';
import { texts } from '../../../src/pages/xcar/Pages/OnlineAuth/Texts';
import { ButtonDispalyType, TitleHightlightType } from '../../../src/pages/xcar/ComponentBusiness/OrderConfirmModal/src/Index';
import { ModalTypes } from '../../../src/pages/xcar/Constants/OrderOnlineAuth';

import { xShowToast } from '@ctrip/xtaro';


jest.mock('../../../src/pages/xcar/State/OrderDetail/Selectors', () => {
  return {
    getCustomerInfo: jest.fn(), // 解决循环引用
    getOrderId: () => '3082757675',
    getIsSelfService: () => false,
  }
});

jest.mock('../../../src/pages/xcar/State/OnlineAuth/Selectors', () => {
  return {
    selectIsLoading: () => true,
    getOrderDirverInfo: () => ({ "name": "赵大鹏", "certificateType": 1, "certificateNumber": "130105nlFfwr7c7332#", "mobile": "158dFGD6801" }),
    selectPolicy: () => null,
    selectHasResultMap: () => ({
      "1": false,
      "2": false,
      "3": false,
      "4": false
    })
  }
});



describe('getLisenceAuthInfoLogic ', () => {
  test('getLisenceAuthInfoLogic 正常运行', async () => {
    // mock接口返回数据
    const queryCertificateAPI = jest.spyOn(CarFetch, 'queryCertificate').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      supportInfo: {},
      attrDto: {
        history: true,
      },
      policy: {},
      certificateV3List: {},
    }));

    // 获取saga执行记录
    const dispatched = await recordSaga(getLisenceAuthInfoLogic, {
      action: {
        type: GET_LISENCE_AUTH_INFO,
        data: {},
      },
      state: {},
    });

    expect(queryCertificateAPI).toBeCalled(); // API 被调用
    expect(dispatched[0]).toEqual(setOrderAuthInfo({
      certificateV3List: {},
      policy: {},
      supportInfo: {
        authCardHistory: true
      },
    })); // 更新授权信息
  })
})

describe('lisenceOCRCheckSaveLogic ', () => {
  test('lisenceOCRCheckSaveLogic isSuccess true', async () => {

    const mockApiRessonse = { "baseResponse": { "isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 2306 }, "errorCode": "200", "certificateV3List": [{ "certificateType": "2", "userName": "赵大鹏", "certificateNo": "131125197903093418", "sex": "男", "birthday": "1978-11-25", "address": "xxxxx", "notionality": "中国", "validPeriodBegin": "2013-02-15", "validPeriodEnd": "2023-02-15", "issueAuthority": "xxxxxx", "mainImageUrl": "xxx", "subImageUrl": "xxx", "firstIssueDate": "2007-02-15", "fileNumber": "110005539280", "licenceClass": "C1" }], "multiErrorMsg": "抱歉，您的证件仍无法被识别。\n您可选择放弃认证，并在取车时，向门店出示身份证及驾照原件" };

    // mock接口返回数据
    const queryCertificateAPI = jest.spyOn(CarFetch, 'saveCertificate').mockReturnValue(Promise.resolve(mockApiRessonse));

    const dispatched = await recordSaga(lisenceOCRCheckSaveLogic, {
      action: {
        type: GET_LISENCE_AUTH_INFO,
        data: {
          cardFaceType: LisenceCardFaceType.IdCardAType,
          addType: AddType.Retry,
        },
      },
      state: {
        "OnlineAuth": {
          "isLoading": false,
          "orderAuthInfo": {},
          "retryingCardFace": {
            "1": false,
            "2": false,
            "3": false,
            "4": false
          },
          "modals": {
            "CONFIRM": {},
            "AUTH_SWITCH": {},
            "OCR_TIP": {}
          }
        },
      },
    });

    expect(queryCertificateAPI).toBeCalled(); // API 被调用
    expect(dispatched[1]).toEqual(getLisenceAuthInfo({ orderId: '3082757675' }));
    expect(dispatched[2]).toEqual(setRetryingCardFace({ [LisenceCardFaceType.IdCardAType]: false }));
  })

  test('lisenceOCRCheckSaveLogic errorCode 默认', async () => {

    const mockApiRessonse = { "baseResponse": { "isSuccess": false, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 2306 }, "errorCode": "C1", "certificateV3List": [{ "certificateType": "2", "userName": "赵大鹏", "certificateNo": "131125197903093418", "sex": "男", "birthday": "1978-11-25", "address": "xxxxx", "notionality": "中国", "validPeriodBegin": "2013-02-15", "validPeriodEnd": "2023-02-15", "issueAuthority": "xxxxxx", "mainImageUrl": "xxx", "subImageUrl": "xxx", "firstIssueDate": "2007-02-15", "fileNumber": "110005539280", "licenceClass": "C1" }], "multiErrorMsg": "抱歉，您的证件仍无法被识别。\n您可选择放弃认证，并在取车时，向门店出示身份证及驾照原件" };

    // mock接口返回数据
    const queryCertificateAPI = jest.spyOn(CarFetch, 'saveCertificate').mockReturnValue(Promise.resolve(mockApiRessonse));

    jest.spyOn(CarStorage, 'load').mockImplementation(() => Promise.resolve(null))
    jest.spyOn(CarStorage, 'save').mockImplementation(() => Promise.resolve(null))


    const dispatched = await recordSaga(lisenceOCRCheckSaveLogic, {
      action: {
        type: GET_LISENCE_AUTH_INFO,
        data: {
          cardFaceType: LisenceCardFaceType.IdCardAType,
        },
      },
      state: {
        "OnlineAuth": {
          "isLoading": false,
          "orderAuthInfo": {},
          "retryingCardFace": {
            "1": false,
            "2": false,
            "3": false,
            "4": false
          },
          "modals": {
            "CONFIRM": {},
            "AUTH_SWITCH": {},
            "OCR_TIP": {}
          }
        },
      },
    });

    expect(queryCertificateAPI).toBeCalled(); // API 被调用
    expect(dispatched[1]).toEqual(setModal(ModalTypes.CONFIRM, {
      visible: true,
      title: texts.authFailTitle,
      contentText: texts.authFaildesc,
      btnsDispalyStyle: ButtonDispalyType.Column,
      titleHeightlightStyle: TitleHightlightType.Warning,
    }));
  })

  test('lisenceOCRCheckSaveLogic errorCode C1 Error Conut > 2', async () => {

    const mockApiRessonse = { "baseResponse": { "isSuccess": false, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 2306 }, "errorCode": "C1", "certificateV3List": [{ "certificateType": "2", "userName": "赵大鹏", "certificateNo": "131125197903093418", "sex": "男", "birthday": "1978-11-25", "address": "xxxxx", "notionality": "中国", "validPeriodBegin": "2013-02-15", "validPeriodEnd": "2023-02-15", "issueAuthority": "xxxxxx", "mainImageUrl": "xxx", "subImageUrl": "xxx", "firstIssueDate": "2007-02-15", "fileNumber": "110005539280", "licenceClass": "C1" }], "multiErrorMsg": "抱歉，您的证件仍无法被识别。\n您可选择放弃认证，并在取车时，向门店出示身份证及驾照原件" };

    // mock接口返回数据
    const queryCertificateAPI = jest.spyOn(CarFetch, 'saveCertificate').mockReturnValue(Promise.resolve(mockApiRessonse));

    jest.spyOn(CarStorage, 'load').mockImplementation(() => Promise.resolve('3'))
    jest.spyOn(CarStorage, 'save').mockImplementation(() => Promise.resolve(null))


    const dispatched = await recordSaga(lisenceOCRCheckSaveLogic, {
      action: {
        type: GET_LISENCE_AUTH_INFO,
        data: {
          cardFaceType: LisenceCardFaceType.IdCardAType,
        },
      },
      state: {
        "OnlineAuth": {
          "isLoading": false,
          "orderAuthInfo": {},
          "retryingCardFace": {
            "1": false,
            "2": false,
            "3": false,
            "4": false
          },
          "modals": {
            "CONFIRM": {},
            "AUTH_SWITCH": {},
            "OCR_TIP": {}
          }
        },
      },
    });

    expect(queryCertificateAPI).toBeCalled(); // API 被调用
    expect(dispatched[1]).toEqual(setModal(ModalTypes.CONFIRM, {
      visible: true,
      cardFaceType: LisenceCardFaceType.IdCardAType,
      title: texts.authFailTitle,
      contentText: '抱歉，您的证件仍无法被识别。\n您可选择放弃认证，并在取车时，向门店出示身份证及驾照原件',
      resultCode: 'C99',
      btnsDispalyStyle: ButtonDispalyType.Column,
      titleHeightlightStyle: TitleHightlightType.Warning,
    }));
  })

  test('lisenceOCRCheckSaveLogic errorCode errorTitle', async () => {

    const mockApiRessonse = { "baseResponse": { "isSuccess": false, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 2306 }, "errorCode": "XXX", "certificateV3List": [{ "certificateType": "2", "userName": "赵大鹏", "certificateNo": "131125197903093418", "sex": "男", "birthday": "1978-11-25", "address": "xxxxx", "notionality": "中国", "validPeriodBegin": "2013-02-15", "validPeriodEnd": "2023-02-15", "issueAuthority": "xxxxxx", "mainImageUrl": "xxx", "subImageUrl": "xxx", "firstIssueDate": "2007-02-15", "fileNumber": "110005539280", "licenceClass": "C1" }], "multiErrorMsg": "抱歉，您的证件仍无法被识别。\n您可选择放弃认证，并在取车时，向门店出示身份证及驾照原件", "errorTitle": '错误标题', "errorMsg": ["错误提醒"] };

    // mock接口返回数据
    const queryCertificateAPI = jest.spyOn(CarFetch, 'saveCertificate').mockReturnValue(Promise.resolve(mockApiRessonse));

    jest.spyOn(CarStorage, 'load').mockImplementation(() => Promise.resolve('3'))
    jest.spyOn(CarStorage, 'save').mockImplementation(() => Promise.resolve(null))


    const dispatched = await recordSaga(lisenceOCRCheckSaveLogic, {
      action: {
        type: GET_LISENCE_AUTH_INFO,
        data: {
          cardFaceType: LisenceCardFaceType.IdCardAType,
        },
      },
      state: {
        "OnlineAuth": {
          "isLoading": false,
          "orderAuthInfo": {},
          "retryingCardFace": {
            "1": false,
            "2": false,
            "3": false,
            "4": false
          },
          "modals": {
            "CONFIRM": {},
            "AUTH_SWITCH": {},
            "OCR_TIP": {}
          }
        },
      },
    });

    expect(queryCertificateAPI).toBeCalled(); // API 被调用
    expect(dispatched[1]).toEqual(setModal(ModalTypes.CONFIRM, {
      visible: true,
      cardFaceType: LisenceCardFaceType.IdCardAType,
      btnsDispalyStyle: ButtonDispalyType.Column,
      titleHeightlightStyle: TitleHightlightType.Warning,
      title: '错误标题',
      contentText: "错误提醒",
      descText: texts.confirmModalTip,
      resultCode: 'XXX',
      errorMsg: ["错误提醒"],    
    }));
  })
})

describe('updateLisenceAuthLogic ', () => {
  test('updateLisenceAuthLogic 正常运行', async () => {
    // mock接口返回数据
    const featchFunction = jest.spyOn(CarFetch, 'updateAuthCertificate').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
    }));

    // 获取saga执行记录
    const dispatched = await recordSaga(updateLisenceAuthLogic, {
      action: {
        type: GET_LISENCE_AUTH_INFO,
        data: {
          authType: UpdateAuthType.Auth
        },
      },
      state: {},
    });

    expect(featchFunction).toBeCalled(); // API 被调用
    expect(xShowToast).toHaveBeenCalledWith({"duration": 3000, "title": "人像面信息已更新"}); // API 被调用
    expect(dispatched[0]).toEqual(getLisenceAuthInfo({
      orderId: '3082757675',
      showLoading: true,
    })); // 更新授权信息
  })

  test('updateLisenceAuthLogic 失败', async () => {
    // mock接口返回数据
    const featchFunction = jest.spyOn(CarFetch, 'updateAuthCertificate').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: false,
      },
    }));

    // 获取saga执行记录
    const dispatched = await recordSaga(updateLisenceAuthLogic, {
      action: {
        type: GET_LISENCE_AUTH_INFO,
        data: {
          authType: UpdateAuthType.Auth
        },
      },
      state: {},
    });

    expect(featchFunction).toBeCalled(); // API 被调用
    expect(xShowToast).toHaveBeenCalledWith({"duration": 3000, "title": "系统异常，请重试"}); 
  })
})