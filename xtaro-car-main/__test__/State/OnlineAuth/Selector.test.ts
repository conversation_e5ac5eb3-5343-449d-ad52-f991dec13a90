import {
  selectIdCardResult,
  selectDriverResult,
  selectIsRetrying,
  getOrderDirverInfo,
  getPersonalInfoAuthData,
} from '../../../src/pages/xcar/State/OnlineAuth/Selectors';

describe('getOrderDirverInfo', () => {
  const stateMock = {
    OrderDetail: {
      driverInfo: {
        name: '测试',
        email: '',
        telphone:
          'd492e309ad4397fe9768cf2eea0e92905f1c2faca90ce24ac59052e5dbb7214d',
        areaCode: '+86',
        flightNo: '',
        iDCardType: 1,
        iDCardNo: '2103********24',
        encrypIDCardNo: '210381Ns6SnIxGrH24#',
        distributionMobile: '1561TV71116',
        distributionEmail: '',
        decryptIDCardNo: '210381199405065924',
        decryptTelphone: '15618081116',
      },
    },
  };
  test('getOrderDirverInfo', () => {
    expect(getOrderDirverInfo(stateMock)).toEqual({
      name: '测试',
      certificateType: 1,
      certificateNumber: '210381Ns6SnIxGrH24#',
      mobile: '1561TV71116',
    });
  });
});

describe('selectIdCardResult', () => {
  const mockStateMap = [
    {
      // 测试正常获取证件信息
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
          },
          orderAuthInfo: {
            supportInfo: {
              authStatus: 0,
              certificationStatus: 200,
              defaultAuthStatus: false,
              isShow: true,
              showTitle: '身份证及驾照在线认证',
              buttonText: '去授权',
              guideText:
                '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
            },
            certificateV3List: [
              {
                isActive: true,
                isShow: true,
                certificateV3: {
                  certificateType: '1',
                  userName: '测试一',
                  certificateNo: '111111111111111111',
                  sex: '男',
                  birthday: '1993-11-30',
                  address: 'XXXXXXXXXXXXXXX',
                  notionality: '汉',
                  validPeriodBegin: '2020-10-13',
                  validPeriodEnd: '9999-12-30',
                  issueAuthority: '上海市公安局交通警察支队',
                  mainImageUrl:
                    'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc061200000bhos72FD3.jpg',
                  subImageUrl:
                    'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc2p1200000bi81iDC0F.jpg',
                  firstIssueDate: '1111-01-01',
                  fileNumber: '',
                  licenceClass: '',
                },
              },
            ],
          },
        },
      },
      expected: {
        idNo: '111111111111111111',
        name: '测试一',
        firstIssueDate: '1111-01-01',
        expiryDate: '有效期至9999-12-30',
        issueDate: '1111-01-01',
        cardAImageUrl:
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc061200000bhos72FD3.jpg',
        cardBImageUrl:
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc2p1200000bi81iDC0F.jpg',
        licenceType: '',
        authority: '上海市公安局交通警察支队',
        resultA: true,
        resultB: true,
        isShow: true,
        isActive: true,
      },
    },
    {
      // 测试正常获取证件信息
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
          },
          orderAuthInfo: {
            supportInfo: {
              authStatus: 0,
              certificationStatus: 200,
              defaultAuthStatus: false,
              isShow: true,
              showTitle: '身份证及驾照在线认证',
              buttonText: '去授权',
              guideText:
                '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
            },
            certificateV3List: [
              {
                isActive: true,
                isShow: true,
              },
            ],
          },
        },
      },
      expected: {},
    },
    {
      // 测试无 certificateV3List 返回时，返回 {}
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
          },
          orderAuthInfo: {
            supportInfo: {
              authStatus: 0,
              certificationStatus: 200,
              defaultAuthStatus: false,
              isShow: true,
              showTitle: '身份证及驾照在线认证',
              buttonText: '去授权',
              guideText:
                '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
            },
            certificateV3List: [],
          },
        },
      },
      expected: {},
    },

    {
      // 测试无 orderAuthInfo 返回时，返回 {}
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
          },
          orderAuthInfo: null,
        },
      },
      expected: {},
    },
  ];
  test.each(mockStateMap)('selectIdCardResult check', ({ state, expected }) => {
    const data = selectIdCardResult(state);
    expect(data).toEqual(expected);
  });
});

describe('selectDriverResult', () => {
  const mockStateMap = [
    {
      // 测试正常获取证件信息
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
          },
          orderAuthInfo: {
            supportInfo: {
              authStatus: 0,
              certificationStatus: 200,
              defaultAuthStatus: false,
              isShow: true,
              showTitle: '身份证及驾照在线认证',
              buttonText: '去授权',
              guideText:
                '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
            },
            certificateV3List: [
              {
                isActive: true,
                isShow: true,
                certificateV3: {
                  certificateType: '2',
                  userName: '测试一',
                  certificateNo: '111111111111111111',
                  sex: '男',
                  birthday: '1993-11-30',
                  address: 'XXXXXXXXXXXXXXX',
                  notionality: '中国',
                  validPeriodBegin: '2015-10-08',
                  validPeriodEnd: '2021-10-08',
                  issueAuthority: '上海市公安局交通警察支队',
                  mainImageUrl:
                    'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc4g1200000bh45h09B7.jpg',
                  subImageUrl:
                    'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc1q1200000bh4vx4018.jpg',
                  firstIssueDate: '2015-10-08',
                  fileNumber: '410103446200',
                  licenceClass: 'C1',
                },
              },
            ],
          },
        },
      },
      expected: {
        idNo: '111111111111111111',
        name: '测试一',
        firstIssueDate: '2015-10-08',
        expiryDate: '有效期至2021-10-08',
        issueDate: '2015-10-08',
        cardAImageUrl:
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc4g1200000bh45h09B7.jpg',
        cardBImageUrl:
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc1q1200000bh4vx4018.jpg',
        licenceType: 'C1',
        authority: '上海市公安局交通警察支队',
        resultA: true,
        resultB: true,
        msg: '',
        isShow: true,
        isActive: true,
      },
    },
    {
      // 测试正常获取证件信息
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
          },
          orderAuthInfo: {
            supportInfo: {
              authStatus: 0,
              certificationStatus: 200,
              defaultAuthStatus: false,
              isShow: true,
              showTitle: '身份证及驾照在线认证',
              buttonText: '去授权',
              guideText:
                '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
            },
            certificateV3List: [
              {
                isActive: true,
                isShow: true,
              },
            ],
          },
        },
      },
      expected: {},
    },
    {
      // 测试无 certificateV3List 返回时，返回 {}
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
          },
          orderAuthInfo: {
            supportInfo: {
              authStatus: 0,
              certificationStatus: 200,
              defaultAuthStatus: false,
              isShow: true,
              showTitle: '身份证及驾照在线认证',
              buttonText: '去授权',
              guideText:
                '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
            },
            certificateV3List: [],
          },
        },
      },
      expected: {},
    },
    {
      // 测试无 orderAuthInfo 返回时，返回 {}
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
          },
          orderAuthInfo: null,
        },
      },
      expected: {},
    },
  ];
  test.each(mockStateMap)('selectDriverResult check', ({ state, expected }) => {
    const data = selectDriverResult(state);
    expect(data).toEqual(expected);
  });
});

describe('selectIsRetrying', () => {
  const mockStateMap = [
    {
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': true,
            '2': true,
            '3': true,
            '4': true,
          },
        },
      },
      expected: true,
    },
    {
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': true,
            '2': false,
            '3': false,
            '4': true,
            '5': false,
          },
        },
      },
      expected: true,
    },
    {
      state: {
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
            '5': false,
          },
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)('selectIsRetrying check', ({ state, expected }) => {
    const data = selectIsRetrying(state);
    expect(data).toEqual(expected);
  });
});

describe('getPersonalInfoAuthData', () => {
  const mockStateMap = [
    {
      state: {
        OnlineAuth: {},
      },
      expected: {
        tips: [],
        link: '',
      },
    },
    {
      state: {
        OnlineAuth: {
          orderAuthInfo: {},
        },
      },
      expected: {
        tips: [],
        link: '',
      },
    },
    {
      state: {
        OnlineAuth: {
          orderAuthInfo: {
            policy: {
              personalInfoAuthTips: [],
              personalInfoAuthLink: '',
            },
          },
        },
      },
      expected: {
        tips: [],
        link: '',
      },
    },
    {
      state: {
        OnlineAuth: {
          orderAuthInfo: {
            policy: {
              personalInfoAuthTips: [],
              personalInfoAuthLink:
                'https://ws.downloadfile.fx.ctripcorp.com/files/6/carpic_no_mark/1tg2712000cjzh168FE67.doc',
            },
          },
        },
      },
      expected: {
        tips: [],
        link: 'https://ws.downloadfile.fx.ctripcorp.com/files/6/carpic_no_mark/1tg2712000cjzh168FE67.doc',
      },
    },
    {
      state: {
        OnlineAuth: {
          orderAuthInfo: {
            policy: {
              personalInfoAuthTips: [
                '确认如下内容：根据下单时您已阅读的',
                '个人信息授权声明',
                '，【自助取还】订单的【身份证】、【驾驶证】与您勾选及填写的个人信息均会传输至自助取还租车服务提供方',
              ],
              personalInfoAuthLink: '',
            },
          },
        },
      },
      expected: {
        tips: [
          '确认如下内容：根据下单时您已阅读的',
          '个人信息授权声明',
          '，【自助取还】订单的【身份证】、【驾驶证】与您勾选及填写的个人信息均会传输至自助取还租车服务提供方',
        ],
        link: '',
      },
    },
    {
      state: {
        OnlineAuth: {
          orderAuthInfo: {
            policy: {
              personalInfoAuthTips: [
                '确认如下内容：根据下单时您已阅读的',
                '个人信息授权声明',
                '，【自助取还】订单的【身份证】、【驾驶证】与您勾选及填写的个人信息均会传输至自助取还租车服务提供方',
              ],
              personalInfoAuthLink:
                'https://ws.downloadfile.fx.ctripcorp.com/files/6/carpic_no_mark/1tg2712000cjzh168FE67.doc',
            },
          },
        },
      },
      expected: {
        tips: [
          '确认如下内容：根据下单时您已阅读的',
          '个人信息授权声明',
          '，【自助取还】订单的【身份证】、【驾驶证】与您勾选及填写的个人信息均会传输至自助取还租车服务提供方',
        ],
        link: 'https://ws.downloadfile.fx.ctripcorp.com/files/6/carpic_no_mark/1tg2712000cjzh168FE67.doc',
      },
    },
  ];
  test.each(mockStateMap)(
    'getPersonalInfoAuthData check',
    ({ state, expected }) => {
      const data = getPersonalInfoAuthData(state);
      expect(data).toEqual(expected);
    },
  );
});
