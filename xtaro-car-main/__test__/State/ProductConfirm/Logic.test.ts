import * as bbkUtils from '../../../src/pages/xcar/Common/src/Utils/src/Utils';
import {
  queryVehicleDetailInfo,
  queryDepositInfo,
  queryCommentSummary,
  queryVirtualNumber,
} from '../../../src/pages/xcar/State/ProductConfirm/Logic';
import * as Actions from '../../../src/pages/xcar/State/ProductConfirm/Actions';
import * as Types from '../../../src/pages/xcar/State/ProductConfirm/Types';
import * as supActions from '../../../src/pages/xcar/State/SupplierData/Actions';
import Texts from '../../../src/pages/xcar/State/Product/Texts';
import { CarFetch, CarABTesting } from '../../../src/pages/xcar/Util/Index';
import { recordSaga } from '../../testHelpers';

jest.mock('../../../src/pages/xcar/Common/src/Utils/src/Utils', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/Common/src/Utils/src/Utils'),
}));

jest.spyOn(bbkUtils, 'uuid').mockImplementation(() => 'uuid');

jest.mock('../../../src/pages/xcar/State/List/Mappers', () => {
  return {
    getLimitInfo: jest.fn(), // 解决循环引用
  };
});
jest.mock('../../../src/pages/xcar/State/VendorList/Selectors', () => {
  return {
    getUniqueReference: () => {
      return {
        pStoreCode: 'pStoreCode',
      };
    },
  };
});
jest.mock('../../../src/pages/xcar/State/OrderDetail/Selectors', () => {
  return {
    getCustomerInfo: jest.fn(), // 解决循环引用
  };
});
jest.mock('../../../src/pages/xcar/State/ProductConfirm/Selectors', () => {
  return {
    getPackagedLocationInfo: jest.fn(), // 解决循环引用
    getRequestParams: () => ({}),
    getReviewRequestParams: () => ({}),
    getProductConfirmRequest: () => ({
      parentRequestId: 'parentRequestId',
    }),
  };
});
jest.mock('../../../src/pages/xcar/State/OrderDetail/Selectors', () => {
  return {
    getPickUpStoreId: () => 'storeId',
    getExtendedInfo: () => ({ klbVersion: 1 }),
    getVehicleInfo: () => ({ ctripVehicleID: 13512 }),
  };
});
jest.mock('../../../src/pages/xcar/Global/Cache/ListResSelectors', () => ({
  getBaseResData: jest.fn(), // 解决循环引用
}));

afterEach(() => {
  jest.clearAllMocks();
});

describe('测试 queryVehicleDetailInfo Logic ', () => {
  const testFn = async (response, data?) => {
    const queryVehicleDetailInfoAPI = jest
      .spyOn(CarFetch, 'queryVehicleDetailInfo')
      .mockReturnValueOnce(Promise.resolve(response));
    const dispatched = await recordSaga(queryVehicleDetailInfo, {
      action: {
        type: Types.QUERY,
        data,
      },
      state: {},
    });
    expect(queryVehicleDetailInfoAPI).toBeCalled();
    return dispatched;
  };
  test('测试 订祥调用 queryVehicleDetailInfo', async () => {
    const queryVehicleDetailInfoAPI = jest
      .spyOn(CarFetch, 'queryVehicleDetailInfo')
      .mockReturnValueOnce(Promise.resolve({}));
    await recordSaga(queryVehicleDetailInfo, {
      action: {
        type: Types.QUERY,
        data: { isOrderDetail: true },
      },
      state: {},
    });
    expect(queryVehicleDetailInfoAPI).toBeCalled();
  });

  test('测试 catch', async () => {
    jest.mock('../../../src/pages/xcar/State/ProductConfirm/Selectors', () => {
      return {
        getPackagedLocationInfo: jest.fn(), // 解决循环引用
        getRequestParams: jest.fn(),
      };
    });
    await recordSaga(queryVehicleDetailInfo, {
      action: {
        type: Types.QUERY,
      },
      state: {},
    });
  });
  test('测试 常规 queryVehicleDetailInfo', async () => {
    const reference: any = {
      klbVersion: 1,
    };
    const request = {
      requestParams: {
        reference,
      },
    };
    const dispatched = await testFn({}, request);
    expect(dispatched[0]).toEqual(Actions.clear());
    expect(dispatched[1]).toEqual(Actions.setLoading(true));
    expect(dispatched[2]).toEqual(
      Actions.queryCommentSummary({
        isOrderDetail: undefined,
        reviewRequestParams: undefined,
      }),
    );
    expect(dispatched[5]).toEqual(
      Actions.queryDepositInfo({
        parentRequestId: 'uuid',
        requestParams: {
          parentRequestId: 'uuid',
          reference,
        },
      }),
    );
    expect(dispatched[6]).toEqual(
      supActions.setSupplierData({
        licenseDesc: Texts.licenseDesc,
        companyName: undefined,
        licenseImgUrl: undefined,
        vendorId: undefined,
      }),
    );
    expect(dispatched[7]).toEqual(Actions.setLoading(false));
    expect(dispatched[8]).toEqual(Actions.setFail(false));
  });
});
describe('测试 queryDepositInfo Logic ', () => {
  const testFn = async (response, data?) => {
    const queryVehicleDetailInfoAPI = jest
      .spyOn(CarFetch, 'queryVehicleDetailInfo')
      .mockReturnValueOnce(Promise.resolve(response));
    const dispatched = await recordSaga(queryDepositInfo, {
      action: {
        type: Types.QUERY_DEPOSIT_INFO,
        data,
      },
      state: {},
    });
    expect(queryVehicleDetailInfoAPI).toBeCalled();
    return dispatched;
  };

  test('测试 queryVehicleDetailInfo 接口无返回 depositInfo ', async () => {
    const dispatched = await testFn(
      {},
      {
        parentRequestId: 'uuid',
      },
    );
    expect(dispatched[2]).toEqual(Actions.setDepositFail(true));
  });

  test('测试 queryVehicleDetailInfo action参数中无 parentRequestId', async () => {
    const dispatched = await testFn({});
    expect(dispatched[2]).toEqual(Actions.setDepositFail(true));
  });
  test('测试 queryVehicleDetailInfo 接口抛错 ', async () => {
    jest
      .spyOn(CarFetch, 'queryVehicleDetailInfo')
      .mockReturnValueOnce(Promise.reject(null));
    const dispatched = await recordSaga(queryDepositInfo, {
      action: {
        type: Types.QUERY_DEPOSIT_INFO,
        data: {
          parentRequestId: 'uuid',
        },
      },
      state: {},
    });
    expect(dispatched[3]).toEqual(Actions.setDepositInfo(null));
  });

  test('测试 queryVehicleDetailInfo 接口异常数据 ', async () => {
    const dispatched = await testFn(null, {
      parentRequestId: 'uuid',
    });
    expect(dispatched[3]).toEqual(Actions.setDepositInfo(undefined));
  });
});

describe('测试 queryCommentSummary Logic ', () => {
  test('测试 queryCommentSummary ', async () => {
    const getCommentSummaryApi = jest
      .spyOn(CarFetch, 'getCommentSummary')
      .mockReturnValueOnce(
        Promise.resolve({
          content: '{}',
          userAlbum: {},
          userAlbumUrl:
            '/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&initialPage=PhotoAlbum&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=44283&calabiVehicleId=5374&isHideNavBar=YES',
          userAlbumTotalCount: 11,
        }),
      );
    const dispatched = await recordSaga(queryCommentSummary, {
      action: {
        type: Types.QUERY_REVIEWS,
      },
      state: {},
    });
    expect(getCommentSummaryApi).toBeCalled();
    expect(dispatched[2]).toEqual(Actions.setReviewFail(false));
  });
  test('测试 queryCommentSummary 接口抛错 ', async () => {
    jest
      .spyOn(CarFetch, 'getCommentSummary')
      .mockReturnValueOnce(Promise.reject(null));
    const dispatched = await recordSaga(queryCommentSummary, {
      action: {
        type: Types.QUERY_REVIEWS,
      },
      state: {},
    });
    expect(dispatched[2]).toEqual(Actions.setReviewFail(true));
  });
  test('测试 订祥调用 queryCommentSummary  ', async () => {
    jest
      .spyOn(CarFetch, 'getCommentSummary')
      .mockReturnValueOnce(Promise.reject(null));
    const dispatched = await recordSaga(queryCommentSummary, {
      action: {
        type: Types.QUERY_REVIEWS,
        data: { isOrderDetail: true },
      },
      state: {},
    });
    expect(dispatched[3]).toEqual(
      Actions.setReviewRequestAndResponse({
        request: {
          storeId: 'storeId',
          klbVersion: 1, // 是否是卡比供应商
          calabiVehicleId: 13512,
          invokeFrom: 4,
        },
        response: null,
        userAlbum: null,
        userAlbumUrl: '',
        userAlbumTotalCount: 0,
      }),
    );
  });
});

describe('测试 queryVirtualNumber Logic ', () => {
  const testFn = async response => {
    const queryVirtualNumberApi = jest
      .spyOn(CarFetch, 'queryVirtualNumber')
      .mockReturnValueOnce(Promise.resolve({ response }));
    const dispatched = await recordSaga(queryVirtualNumber, {
      action: {
        type: Types.QUERY_VIRTUAL_NUMBER,
      },
      state: {},
    });
    expect(queryVirtualNumberApi).toBeCalled();
    return dispatched;
  };
  test('测试 queryVirtualNumber ', async () => {
    const response = {
      vNumber: '2001212,1234',
      iconShow: 1,
      bindStatus: 1,
    };
    const dispatched = await testFn(response);
    expect(dispatched[2]).toEqual(Actions.setIconVisible(true));
    expect(dispatched[3]).toEqual(
      Actions.setVirtualNumberInfo({
        vNumber: '2001212,1234',
      }),
    );
  });
  test('测试 queryVirtualNumber 异常数据 ', async () => {
    const dispatched = await testFn(null);
    expect(dispatched[2]).toEqual(Actions.setIconVisible(false));
    expect(dispatched[3]).toEqual(
      Actions.setVirtualNumberInfo({
        vNumber: '',
      }),
    );
  });
});
