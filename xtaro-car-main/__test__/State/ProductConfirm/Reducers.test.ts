import reducer, {
  initialState,
} from '../../../src/pages/xcar/State/ProductConfirm/Reducers';
import * as Types from '../../../src/pages/xcar/State/ProductConfirm/Types';
describe('测试直接赋值的reducer', () => {
  const map = [
    {
      type: Types.SET_LOADING,
      expected: {
        isLoading: true,
      },
    },
    {
      type: Types.SET_FAIL,
      expected: {
        isFail: true,
      },
    },
    {
      type: Types.SET_REVIEWS_FAIL,
      expected: {
        isReviewFail: true,
      },
    },
    {
      type: Types.SET_DEPOSIT_FAIL,
      expected: {
        isDepositFail: true,
      },
    },
    {
      type: Types.SET_DEPOSIT_INFO,
      expected: {
        depositInfo: true,
      },
    },
    {
      type: Types.SET_VIRTUAL_NUMBER_LOADING,
      expected: {
        isVirtualNumberLoading: true,
      },
    },
    {
      type: Types.SET_ICON_VISIBLE,
      expected: {
        isVirtualNumberIconVisible: true,
      },
    },
  ];
  test.each(map)('%p', ({ type, expected }) => {
    const data = reducer(initialState, {
      type,
      data: true,
    });
    expect(data).toEqual({
      ...initialState,
      ...expected,
    });
  });
  test('测试 SET_VIRTUAL_NUMBER 空输入', () => {
    expect(
      reducer(initialState, {
        type: Types.SET_VIRTUAL_NUMBER,
      }),
    ).toEqual(initialState);
  });
  test('测试 SET_DEPOSIT_INFO 空输入', () => {
    expect(
      reducer(initialState, {
        type: Types.SET_DEPOSIT_INFO,
      }),
    ).toEqual(initialState);
  });
});
describe('测试 reducer 状态是否正常', () => {
  test('测试 initialState', () => {
    const data = reducer(initialState, {
      type: Types.SET_REQUEST_RESPONSE,
    });
    expect(data).toEqual(initialState);
  });
  test('测试 SET_REQUEST_RESPONSE', () => {
    const data = reducer(initialState, {
      type: Types.SET_REQUEST_RESPONSE,
      data: {
        request: null,
        response: null,
      },
    });
    expect(data).toEqual({
      ...initialState,
      request: null,
      response: null,
    });
    expect(
      reducer(initialState, {
        type: Types.SET_REQUEST_RESPONSE,
      }),
    ).toEqual({
      ...initialState,
      request: null,
      response: null,
    });
  });
  test('测试 SET_REVIEWS_REQUEST_RESPONSE', () => {
    expect(
      reducer(initialState, {
        type: Types.SET_REVIEWS_REQUEST_RESPONSE,
        data: {
          request: null,
          response: null,
          userAlbum: {},
          userAlbumUrl:
            '/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&initialPage=PhotoAlbum&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=44283&calabiVehicleId=5374&isHideNavBar=YES',
          userAlbumTotalCount: 11,
        },
      }),
    ).toEqual({
      ...initialState,
      reviewRequest: null,
      reviewResponse: null,
      userAlbum: {},
          userAlbumUrl:
            '/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&initialPage=PhotoAlbum&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=44283&calabiVehicleId=5374&isHideNavBar=YES',
          userAlbumTotalCount: 11,
    });
    expect(
      reducer(initialState, {
        type: Types.SET_REVIEWS_REQUEST_RESPONSE,
      }),
    ).toEqual({
      ...initialState,
      reviewRequest: null,
      reviewResponse: null,
    });
  });
  test('测试 SET_REVIEWS_LOADING', () => {
    const data = reducer(initialState, {
      type: Types.SET_REVIEWS_LOADING,
      data: true,
    });
    expect(data).toEqual({
      ...initialState,
      isReviewLoading: true,
      isReviewFail: false,
    });
  });
  test('测试 SET_DEPOSIT_LOADING', () => {
    const data = reducer(initialState, {
      type: Types.SET_DEPOSIT_LOADING,
      data: true,
    });
    expect(data).toEqual({
      ...initialState,
      isDepositLoading: true,
      isDepositFail: false,
    });
  });
  test('测试 CLEAR', () => {
    const data = reducer(initialState, {
      type: Types.CLEAR,
      data: true,
    });
    expect(data).toEqual({
      ...initialState,
      response: null,
      request: null,
      depositInfo: null,
      reviewRequest: null,
      reviewResponse: null,
      userAlbum: null,
      userAlbumUrl: '',
      userAlbumTotalCount: 0,
      isVirtualNumberIconVisible: false,
      isVirtualNumberLoading: true,
      virtualNumberInfo: null,
    });
  });
  test('测试 default', () => {
    expect(
      reducer(initialState, {
        type: 'other',
        data: true,
      }),
    ).toEqual(initialState);
  });
});
