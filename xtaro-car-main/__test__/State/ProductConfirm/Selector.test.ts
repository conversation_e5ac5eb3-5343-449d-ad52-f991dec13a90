import { APP_TYPE } from '../../../src/pages/xcar/Constants/Platform';
import {
  getProductConfirmRequest,
  getProductConfirmResponse,
  getLoading,
  getIsVirtualNumberIconVisible,
  getDepositLoading,
  getVirtualNumber,
  getSecAndPrivacyTips,
  getNonBusinessHoursTips,
  getVirtualNumberLoading,
  getDepositFail,
  getDepositSaleOut,
  getDepositInfo,
  getFail,
  getReviewLoading,
  getReviewFail,
  getVendorImUrl,
  getPackagedLocationInfo,
  getVehicleInfoLogData,
  getFooterBarData,
  getImagesPageParams,
  getGuidePageParams,
  getPriceDetailModalButtonText,
  getReviewRequestParams,
  getImageList,
  getVehicleTags,
  getVendorName,
  getVendorId,
  getPickUpDistanceDesc,
  getVendorVNumber,
  getUserAlbum,
  getUserAlbumUrl,
  getUserAlbumTotalCount,
} from '../../../src/pages/xcar/State/ProductConfirm/Selectors';
import { AppContext } from '../../../src/pages/xcar/Util/Index';

const stateMock = {};

describe('ProductConfirm Selector getProductConfirmRequest', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          request: null,
        },
      },
      expected: null,
    },
  ];

  test.each(mockStateMap)(
    'getProductConfirmRequest check',
    ({ state, expected }) => {
      const result = getProductConfirmRequest(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getProductConfirmResponse', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
      },
      expected: null,
    },
  ];

  test.each(mockStateMap)(
    'getProductConfirmResponse check',
    ({ state, expected }) => {
      const result = getProductConfirmResponse(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getLoading', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          isLoading: false,
        },
      },
      expected: false,
    },
    {
      state: {
        ProductConfirm: {
          isLoading: true,
        },
      },
      expected: true,
    },
  ];

  test.each(mockStateMap)('getLoading check', ({ state, expected }) => {
    const result = getLoading(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getIsVirtualNumberIconVisible', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          isVirtualNumberIconVisible: false,
        },
      },
      expected: false,
    },
    {
      state: {
        ProductConfirm: {
          isVirtualNumberIconVisible: true,
        },
      },
      expected: true,
    },
  ];

  test.each(mockStateMap)(
    'getIsVirtualNumberIconVisible check',
    ({ state, expected }) => {
      const result = getIsVirtualNumberIconVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getDepositLoading', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          isDepositLoading: false,
        },
      },
      expected: false,
    },
    {
      state: {
        ProductConfirm: {
          isDepositLoading: true,
        },
      },
      expected: true,
    },
  ];

  test.each(mockStateMap)('getDepositLoading check', ({ state, expected }) => {
    const result = getDepositLoading(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getVirtualNumber', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          virtualNumberInfo: null,
        },
      },
      expected: undefined,
    },
  ];

  test.each(mockStateMap)('getVirtualNumber check', ({ state, expected }) => {
    const result = getVirtualNumber(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getSecAndPrivacyTips', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          virtualNumberInfo: null,
        },
      },
      expected: undefined,
    },
  ];

  test.each(mockStateMap)(
    'getSecAndPrivacyTips check',
    ({ state, expected }) => {
      const result = getSecAndPrivacyTips(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getNonBusinessHoursTips', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          virtualNumberInfo: null,
        },
      },
      expected: undefined,
    },
  ];

  test.each(mockStateMap)(
    'getNonBusinessHoursTips check',
    ({ state, expected }) => {
      const result = getNonBusinessHoursTips(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getVirtualNumberLoading', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          isVirtualNumberLoading: false,
        },
      },
      expected: false,
    },
    {
      state: {
        ProductConfirm: {
          isVirtualNumberLoading: true,
        },
      },
      expected: true,
    },
  ];

  test.each(mockStateMap)(
    'getVirtualNumberLoading check',
    ({ state, expected }) => {
      const result = getVirtualNumberLoading(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getDepositFail', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          isDepositFail: false,
        },
      },
      expected: false,
    },
    {
      state: {
        ProductConfirm: {
          isDepositFail: true,
        },
      },
      expected: true,
    },
  ];

  test.each(mockStateMap)('getDepositFail check', ({ state, expected }) => {
    const result = getDepositFail(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getDepositSaleOut', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          isDepositSaleOut: false,
        },
      },
      expected: false,
    },
    {
      state: {
        ProductConfirm: {
          isDepositSaleOut: true,
        },
      },
      expected: true,
    },
  ];

  test.each(mockStateMap)('getDepositSaleOut check', ({ state, expected }) => {
    const result = getDepositSaleOut(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getDepositInfo', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          depositInfo: null,
        },
      },
      expected: null,
    },
  ];

  test.each(mockStateMap)('getDepositInfo check', ({ state, expected }) => {
    const result = getDepositInfo(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getFail', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          isFail: false,
        },
      },
      expected: false,
    },
    {
      state: {
        ProductConfirm: {
          isFail: true,
        },
      },
      expected: true,
    },
  ];

  test.each(mockStateMap)('getFail check', ({ state, expected }) => {
    const result = getFail(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getReviewLoading', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          isReviewLoading: false,
        },
      },
      expected: false,
    },
    {
      state: {
        ProductConfirm: {
          isReviewLoading: true,
        },
      },
      expected: true,
    },
  ];

  test.each(mockStateMap)('getReviewLoading check', ({ state, expected }) => {
    const result = getReviewLoading(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getReviewFail', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          isReviewFail: false,
        },
      },
      expected: false,
    },
    {
      state: {
        ProductConfirm: {
          isReviewFail: true,
        },
      },
      expected: true,
    },
  ];

  test.each(mockStateMap)('getReviewFail check', ({ state, expected }) => {
    const result = getReviewFail(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getVendorImUrl', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
      },
      expected: '',
    },
  ];

  test.each(mockStateMap)('getVendorImUrl check', ({ state, expected }) => {
    const result = getVendorImUrl(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getPackagedLocationInfo', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
        LocationAndDate: {
          rentalDate: {
            pickUp: {
              dateTime: '20200612100000',
            },
            dropOff: {
              dateTime: '20200614100000',
            },
          },
          rentalLocation: {
            pickUp: {
              country: '中国',
              area: {
                lng: 110.343315,
                lat: 19.984078,
                id: '',
                name: '海口东站',
                type: '2',
              },

              cname: '海口',
              cid: '42',
            },
            dropOff: {
              country: '中国',
              area: {
                lng: 110.343315,
                lat: 19.984078,
                id: '',
                name: '海口东站',
                type: '2',
              },

              cname: '海口',
              cid: '42',
            },
            isShowDropOff: false,
          },
        },
      },
      expected: {
        pickup: null,
        return: null,
      },
    },
  ];

  test.each(mockStateMap)(
    'getPackagedLocationInfo check',
    ({ state, expected }) => {
      const result = getPackagedLocationInfo(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getVehicleInfoLogData', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
      },
      expected: {
        vehicleName: undefined,
        vehicleCode: undefined,
        vendorName: undefined,
        vendorCode: undefined,
        pStoreCode: undefined,
        rStoreCode: undefined,
      },
    },
  ];

  test.each(mockStateMap)(
    'getVehicleInfoLogData check',
    ({ state, expected }) => {
      const result = getVehicleInfoLogData(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getFooterBarData', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
        VendorList: {
          uniqueCode: '111222',
        },
      },
      expected: {
        price: undefined,
        originPrice: 0,
        discountPrice: undefined,
        currencyCode: undefined,
      },
    },
  ];

  test.each(mockStateMap)('getFooterBarData check', ({ state, expected }) => {
    const result = getFooterBarData(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getImagesPageParams', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
        VendorList: {
          uniqueCode: '111222',
        },
      },
      expected: {
        storeCode: undefined,
        vehicleId: undefined,
        categoryId: 35,
      },
    },
  ];

  test.each(mockStateMap)(
    'getImagesPageParams check',
    ({ state, expected }) => {
      AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
      const result = getImagesPageParams(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getPriceDetailModalButtonText', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
        VendorList: {
          uniqueCode: '111222',
        },
      },
      expected: '立即预订',
    },
  ];

  test.each(mockStateMap)(
    'getPriceDetailModalButtonText check',
    ({ state, expected }) => {
      const result = getPriceDetailModalButtonText(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getReviewRequestParams', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
        VendorList: {
          uniqueCode: '111222',
        },
      },
      expected: {
        storeId: undefined,
        klbVersion: undefined,
      },
    },
  ];

  test.each(mockStateMap)(
    'getReviewRequestParams check',
    ({ state, expected }) => {
      const result = getReviewRequestParams(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getImageList', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: {
            vehicleInfo: {
              transmissionName: '自动挡',
              displacement: '1.3T',
              style: '',
              isHot: false,
              imageList: [
                'https://dimg04.c-ctrip.com//images/0412j120008n88bn29F6D.jpg',
              ],
              multimediaAlbums: [
                {
                  albumName: '门店实拍',
                  note: '以下为参考样图 年款/颜色等以实物为准',
                  albumType: 2,
                  mediaGroup: [
                    {
                      groupType: 2,
                      groupName: '全部',
                      groupSortNum: 1,
                      medias: [
                        {
                          type: 2,
                          url: 'http://video.c-ctrip.com/videos/wf0b1d000001eed5tE410.mp4',
                          cover:
                            'https://dimg04.c-ctrip.com/images/0R44r120009y1hlsiDAB2.jpg',
                          sortNum: 1,
                        },
                        {
                          type: 3,
                          url: 'https://video-preview.ctrip.com/videos/RV0lcn15372j7p8lsDCAF.mp4?auth=Nephele%20nrasnbqocpx3ndetlcovpaxttgwf8c2a%2F202304171549%2Fcar_standard_product%2FdG9rZW46YmQyOTk1OTVmYzI2MTIzMDdkMmEzNDViMjllNDZiZGQ0N2JlM2VhOTc3ODRjNzE5OTliYWMwNmQ%3D',
                          cover:
                            'https://dimg04.c-ctrip.com/images/0R442120009y1g1xiCD4A.jpg',
                          sortNum: 2,
                        },
                        {
                          type: 1,
                          url: 'https://dimg04.c-ctrip.com/images/0R454120009y1ek9e7F9D.jpg',
                          sortNum: 3,
                        },
                        {
                          type: 1,
                          url: 'https://dimg04.c-ctrip.com/images/0R44c120009y1ikc37FCB.jpg',
                          sortNum: 3,
                        },
                        {
                          type: 1,
                          url: 'https://dimg04.c-ctrip.com/images/0R453120009y1iicm5336.jpg',
                          sortNum: 3,
                        },
                        {
                          type: 1,
                          url: 'https://dimg04.c-ctrip.com/images/0R41h120009xqrz8h275B.jpg',
                          sortNum: 3,
                        },
                        {
                          type: 1,
                          url: 'https://dimg04.c-ctrip.com/images/0R409120009xqv3t2925B.jpg',
                          sortNum: 3,
                        },
                        {
                          type: 1,
                          url: 'https://dimg04.c-ctrip.com/images/0R442120009xqyi8b1A3E.jpg',
                          sortNum: 3,
                        },
                        {
                          type: 1,
                          url: 'https://dimg04.c-ctrip.com/images/0R41h120009xqrz8h275B.jpg',
                          sortNum: 3,
                        },
                        {
                          type: 1,
                          url: 'https://dimg04.c-ctrip.com/images/0R409120009xqv3t2925B.jpg',
                          sortNum: 3,
                        },
                        {
                          type: 1,
                          url: 'https://dimg04.c-ctrip.com/images/0R442120009xqyi8b1A3E.jpg',
                          sortNum: 3,
                        },
                      ],
                    },
                  ],
                },
              ],
              storeRealImageList: [],
              vehicleCode: '4836',
              fuel: '92号',
              fuelType: '汽油',
              licenseStyle: '6',
              oilType: 3,
              driveMode: '前置前驱',
              name: '雪佛兰科鲁泽',
              sourcePicInfos: [
                {
                  type: 1,
                  sourceName: '年款/颜色等以实物为准',
                  picList: [
                    {
                      sortNum: -1,
                      imageUrl:
                        'https://dimg04.c-ctrip.com/images/0R44r120009y1hlsiDAB2.jpg',
                    },
                    {
                      sortNum: -1,
                      imageUrl:
                        'https://dimg04.c-ctrip.com/images/0R442120009y1g1xiCD4A.jpg',
                    },
                    {
                      sortNum: -1,
                      imageUrl:
                        'https://dimg04.c-ctrip.com/images/0R454120009y1ek9e7F9D.jpg',
                    },
                    {
                      sortNum: -1,
                      imageUrl:
                        'https://dimg04.c-ctrip.com/images/0R44c120009y1ikc37FCB.jpg',
                    },
                    {
                      sortNum: -1,
                      imageUrl:
                        'https://dimg04.c-ctrip.com/images/0R453120009y1iicm5336.jpg',
                    },
                    {
                      sortNum: -1,
                      imageUrl:
                        'https://dimg04.c-ctrip.com/images/0R41h120009xqrz8h275B.jpg',
                    },
                    {
                      sortNum: -1,
                      imageUrl:
                        'https://dimg04.c-ctrip.com/images/0R409120009xqv3t2925B.jpg',
                    },
                    {
                      sortNum: -1,
                      imageUrl:
                        'https://dimg04.c-ctrip.com/images/0R442120009xqyi8b1A3E.jpg',
                    },
                  ],
                },
                {
                  sourceName: '年款/颜色等以实物为准',
                  source: 0,
                  type: 2,
                  picList: [
                    {
                      sortNum: 0,
                      imageUrl:
                        'https://dimg04.c-ctrip.com//images/0412j120008n88bn29F6D.jpg',
                    },
                  ],
                },
              ],
              brandName: '雪佛兰',
              brandId: 203,
              imageUrl:
                'https://dimg04.c-ctrip.com/images/3/carisd_newcarimage/png/uz0pcn152vdyzhk1d36A6.png',
              passengerNo: 5,
              doorNo: 4,
              isSpecialized: true,
              struct: '三厢车',
              vehicleAccessoryImages: [],
              groupCode: '2',
              gearbox: '手自一体变速箱(AT)',
              luggageNo: 5,
              brandEName: '雪佛兰',
              groupSubClassCode: '',
              groupName: '经济型',
              transmissionType: 1,
              license: '外牌',
            },
          },
        },
      },
      expected: {
        imageList: [
          'https://dimg04.c-ctrip.com/images/0R442120009y1g1xiCD4A.jpg',
          'https://dimg04.c-ctrip.com/images/0R454120009y1ek9e7F9D.jpg',
          'https://dimg04.c-ctrip.com/images/0R44c120009y1ikc37FCB.jpg',
          'https://dimg04.c-ctrip.com/images/0R453120009y1iicm5336.jpg',
          'https://dimg04.c-ctrip.com/images/0R41h120009xqrz8h275B.jpg',
          'https://dimg04.c-ctrip.com/images/0R409120009xqv3t2925B.jpg',
          'https://dimg04.c-ctrip.com/images/0R442120009xqyi8b1A3E.jpg',
          'https://dimg04.c-ctrip.com/images/0R41h120009xqrz8h275B.jpg',
          'https://dimg04.c-ctrip.com/images/0R409120009xqv3t2925B.jpg',
        ],
        video: 'http://video.c-ctrip.com/videos/wf0b1d000001eed5tE410.mp4',
        videoCover:
          'https://dimg04.c-ctrip.com/images/0R44r120009y1hlsiDAB2.jpg',
        imagesTotalNumber: 11,
        albumName: '门店实拍',
      },
    },
  ];

  test.each(mockStateMap)(
    'getImageList check',
    ({ state, expected }) => {
      const result = getImageList(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getVehicleTags', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
      },
      expected: [],
    },
  ];

  test.each(mockStateMap)('getVehicleTags check', ({ state, expected }) => {
    const result = getVehicleTags(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getVendorName', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
        VendorList: {
          uniqueCode: '111222',
        },
      },
      expected: undefined,
    },
  ];

  test.each(mockStateMap)('getVendorName check', ({ state, expected }) => {
    const result = getVendorName(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getVendorId', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
      },
      expected: undefined,
    },
  ];

  test.each(mockStateMap)('getVendorId check', ({ state, expected }) => {
    const result = getVendorId(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getPickUpDistanceDesc', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
        },
      },
      expected: undefined,
    },
  ];

  test.each(mockStateMap)(
    'getPickUpDistanceDesc check',
    ({ state, expected }) => {
      const result = getPickUpDistanceDesc(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getVendorVNumber', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          response: null,
          virtualNumberInfo: {
            vendorVNumber: {
              numbers: '1',
            },
          },
        },
      },
      expected: '1',
    },
  ];

  test.each(mockStateMap)('getVendorVNumber check', ({ state, expected }) => {
    const result = getVendorVNumber(state);
    expect(result).toEqual(expected);
  });
});

describe('ProductConfirm Selector getUserAlbum', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          userAlbum: {},
        },
      },
      expected: {},
    },
  ];

  test.each(mockStateMap)(
    'getUserAlbum check',
    ({ state, expected }) => {
      const result = getUserAlbum(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getUserAlbumUrl', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          userAlbumUrl:
            '/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&initialPage=PhotoAlbum&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=44283&calabiVehicleId=5374&isHideNavBar=YES',
        },
      },
      expected: '/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&initialPage=PhotoAlbum&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=44283&calabiVehicleId=5374&isHideNavBar=YES',
    },
  ];

  test.each(mockStateMap)(
    'getUserAlbumUrl check',
    ({ state, expected }) => {
      const result = getUserAlbumUrl(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ProductConfirm Selector getUserAlbumTotalCount', () => {
  const mockStateMap = [
    {
      state: {
        ProductConfirm: {
          userAlbumTotalCount: 11,
        },
      },
      expected: 11,
    },
  ];

  test.each(mockStateMap)(
    'getUserAlbumTotalCount check',
    ({ state, expected }) => {
      const result = getUserAlbumTotalCount(state);
      expect(result).toEqual(expected);
    },
  );
});