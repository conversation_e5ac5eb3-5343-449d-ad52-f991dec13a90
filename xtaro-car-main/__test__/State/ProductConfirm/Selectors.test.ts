import { getQueryVehicleDetailInfoStoreGuidInfos } from '../../../src/pages/xcar/State/ProductConfirm/Selectors';

jest.mock('../../../src/pages/xcar/State/ProductConfirm/Selectors', () => {
  const originalModule = jest.requireActual(
    '../../../src/pages/xcar/State/ProductConfirm/Selectors',
  );
  return {
    __esModule: true,
    ...originalModule,
    getQueryVehicleDetailInfoStoreGuidInfos: jest.fn(() => {
      return [
        {
          storeGuid: '免费站内取还车 携程租车中心内',
          address: '地址：三亚凤凰国际机场停车楼4楼',
          type: 1,
        },
      ];
    }),
  };
});

describe('Booking Selectors getBookingStoreGuidInfos', () => {
  const mockStateMap = [
    {
      state: {},
      expected: [
        {
          storeGuid: '免费站内取还车 携程租车中心内',
          address: '地址：三亚凤凰国际机场停车楼4楼',
          type: 1,
        },
      ],
    },
  ];
  test.each(mockStateMap)(
    'getBookingStoreGuidInfos check',
    ({ state, expected }) => {
      const data = getQueryVehicleDetailInfoStoreGuidInfos(state);
      expect(data).toEqual(expected);
    },
  );
});
