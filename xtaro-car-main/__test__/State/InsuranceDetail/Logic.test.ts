import { recordSaga } from '../../testHelpers';
import {
  queryInsuranceOrderLogic,
} from '../../../src/pages/xcar/State/InsuranceDetail/Logic';
import * as Types from '../../../src/pages/xcar/State/InsuranceDetail/Types';
import * as Actions from '../../../src/pages/xcar/State/InsuranceDetail/Actions';
import * as OrderActions from '../../../src/pages/xcar/State/OrderDetail/Actions';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';

describe('InsuranceDetail', () => {
  test('queryInsuranceOrderLogic', async () => {
    // mock接口返回数据
    const queryInsuranceOrder = jest.spyOn(CarFetch, 'queryInsuranceOrder').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      orderDetail: {
        orderId: '11111',
      }
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(queryInsuranceOrderLogic, {
      action: {
        type: Types.QUERY_INSURANCE_ORDER,
        data: {
          showLoading: true,
        },
      },
      state: {
        OrderDetail: {},
      },
    });
    expect(queryInsuranceOrder).toBeCalled();
    expect(dispatched).toEqual([
      Actions.loadingToggle({ visible: true }),
      Actions.queryInsuranceOrderCallBack({
        baseResponse: {
          isSuccess: true,
        },
        orderDetail: {
          orderId: '11111',
        }
      }),
      OrderActions.queryOrderDetail({ orderId: '11111' }),
      Actions.loadingToggle({ visible: false }),
    ])
  })
})
