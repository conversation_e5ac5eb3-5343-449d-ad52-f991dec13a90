import Reducer, { initialState } from '../../../src/pages/xcar/State/InsuranceDetail/Reducer';
import * as Actions from '../../../src/pages/xcar/State/InsuranceDetail/Types';
describe('Reducer Test', () => {
  const initState = initialState;

  const actionMap = [{
    type: Actions.QUERY_INSURANCE_ORDER,
    data: {
      insuranceOrderId: '1111',
    },
    expected: {
      ...initState,
      insuranceOrderId: '1111',
    }
  },{
    type: Actions.LODING_TOGGLE,
    data: {
      visible: true,
    },
    expected: {
      ...initState,
      isLoading: true,
    }
  },{
    type: Actions.QUERY_INSURANCE_ORDER_CALLBACK,
    data: {
      orderDetail: {
        insuredInfo: {},
      },
    },
    expected: {
      ...initState,
      insuredInfo: {},
    }
  }, ];

  test.each(actionMap)('%p', ({ type, data, expected }) => {
    expect(Reducer(initState, { type, data })).toEqual(expected);
  })
})

