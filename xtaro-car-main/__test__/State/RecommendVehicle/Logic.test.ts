import { recordSaga } from '../../testHelpers';
import * as Types from '../../../src/pages/xcar/State/RecommendVehicle/Types';
import * as Actions from '../../../src/pages/xcar/State/RecommendVehicle/Actions';
import { AppContext, CarFetch } from '../../../src/pages/xcar/Util/Index';
import {
  apiFlightRecProducts,
  preFetchFlightRecProducts,
  goBooking,
} from '../../../src/pages/xcar/State/RecommendVehicle/Logic';

import { queryVehicleDetailInfo } from '../../../src/pages/xcar/State/ProductConfirm/Actions';

describe('apiFlightRecProducts', () => {
  test('apiFlightRecProducts 需要回调', async () => {
    const nowLocationAnDate = {
      rentalDate: {
        pickUp: {
          dateTime: '20200612100000',
        },
        dropOff: {
          dateTime: '20200614100000',
        },
      },
      rentalLocation: {
        pickUp: {
          country: '中国',
          area: {
            lng: 110.343315,
            lat: 19.984078,
            id: '',
            name: '海口东站',
            type: '2',
          },

          cname: '海口',
          cid: '42',
        },
        dropOff: {
          country: '中国',
          area: {
            lng: 110.343315,
            lat: 19.984078,
            id: '',
            name: '海口东站',
            type: '2',
          },

          cname: '海口',
          cid: '42',
        },
        isShowDropOff: false,
      },
    };
    const apiFlightRecProductsFettch = jest.spyOn(CarFetch, 'queryRecommendProducts').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      products: [{}],
      promptInfos: {}
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(apiFlightRecProducts, {
      action: {
        type: Types.FETCH_FLIGHTREC_PRODUCTS,
        data: {},
      },
      state: {
        LocationAndDate: nowLocationAnDate,
      }
    });
    expect(apiFlightRecProductsFettch).toBeCalled();
    expect(dispatched).toEqual([
      Actions.fetchFlightRecProductsLoading(),
      Actions.fetchFlightRecProductsCallback({
        products: [{}],
        promptInfos: {},
        brforeLocationAndDate: nowLocationAnDate,
      })
    ])
  })
})


describe('preFetchFlightRecProducts', () => {
  test('调用fetchFlightRecProducts', async () => {
    const dispatched = await recordSaga(preFetchFlightRecProducts, {
      action: {
        type: Types.PRE_FETCH_FLIGHTREC_PRODUCTS,
        data: {},
      },
      state: {}
    });
    expect(dispatched).toEqual([
      Actions.fetchFlightRecProducts({
        isNeedCallBack: false,
      }),
    ])
  })
})

describe('goBooking', () => {
  test('跳转填写页', async () => {
    const pushFunc = jest.fn();
    AppContext.setPageInstance({
      push: pushFunc,
    })
    const LocationAndData = {
      "rentalDate": {
        "pickUp": {
          "dateTime": "20221217100000"
        },
        "dropOff": {
          "dateTime": "20221218100000"
        }
      },
      "rentalLocation": {
        "pickUp": {
          "version": "3",
          "cid": 2,
          "cname": "上海",
          "country": "中国",
          "realcountry": "中国",
          "isDomestic": true,
          "area": {
            "id": "PVG",
            "name": "浦东国际机场",
            "lat": 31.150964,
            "lng": 121.803535,
            "type": "1"
          }
        },
        "dropOff": {
          "version": "3",
          "cid": 2,
          "cname": "上海",
          "country": "中国",
          "realcountry": "中国",
          "isDomestic": true,
          "area": {
            "id": "PVG",
            "name": "浦东国际机场",
            "lat": 31.150964,
            "lng": 121.803535,
            "type": "1"
          }
        },
        "isShowDropOff": false
      }
    };
    const dispatched = await recordSaga(goBooking, {
      action: {
        type: Types.GO_BOOKING,
        data: {
          vehicleIndex: 0,
        },
      },
      state: {
        RecommendVehicle: {
          brforeLocationAndDate: LocationAndData,
          products: [
            {
              "product": {
                "vehicleCode": "11137",
                "sortNum": 5,
                "lowestPrice": 71,
                "highestPrice": 71,
                "maximumRating": 4,
                "maximumCommentCount": 0,
                "lowestDistance": 4.6626,
                "vendorPriceList": [
                  {
                    "vendorName": "一嗨租车",
                    "isMinTPriceVendor": true,
                    "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/ehai.png",
                    "commentInfo": {
                      "level": "好",
                      "vendorDesc": "施湾镇沃尔玛送车点",
                      "commentCount": 0,
                      "qCommentCount": 0,
                      "qExposed": "4.0",
                      "overallRating": "4.0",
                      "maximumRating": 5,
                      "commentLabel": "",
                      "hasComment": 0
                    },
                    "priceInfo": {
                      "currentDailyPrice": 71,
                      "currentOriginalDailyPrice": 101,
                      "oTPrice": 171,
                      "currentTotalPrice": 141,
                      "currentCurrencyCode": "CNY",
                      "localCurrencyCode": "CNY",
                      "marginPrice": 0,
                      "naked": true,
                      "priceType": 1,
                      "deductInfos": [
                        {
                          "totalAmount": 30,
                          "payofftype": 2
                        }
                      ]
                    },
                    "reference": { "vehicleCode": 1111 },
                    "sortNum": 0,
                    "pStoreRouteDesc": "自行前往门店取还车，距门店直线4.7公里",
                    "easyLifeInfo": {
                      "isEasyLife": false
                    },
                    "platformCode": "0",
                    "storeScore": 80,
                    "isSelect": false,
                    "stock": 0,
                    "distance": 4.6626,
                    "rDistance": 4.6626,
                    "adverts": 0,
                    "payModes": [
                      2,
                      3
                    ],
                  }
                ],
              },
              vehicleInfo: {
                "brandId": 0,
                "brandEName": "大众",
                "brandName": "大众",
                "name": "大众朗逸",
                "zhName": "大众朗逸",
                "vehicleCode": "11137",
                "groupCode": "2",
                "groupSubClassCode": "",
                "groupName": "经济轿车",
                "transmissionType": 1,
                "transmissionName": "自动挡",
                "passengerNo": 5,
                "doorNo": 4,
                "luggageNo": 3,
                "displacement": "1.6L",
                "struct": "三厢",
                "fuel": "92号",
                "driveMode": "前置前驱",
                "style": "",
                "imageList": [
                  "https://pages.c-ctrip.com/carisd/app/11137.jpg"
                ],
                "isSpecialized": true,
                "isHot": false,
                "vehicleAccessoryImages": [
                  "https://dimg04.c-ctrip.com//images/0416i120008asxfwt4E2F.jpg",
                  "https://dimg04.c-ctrip.com//images/04115120008asxg4iB5C6.jpg",
                  "https://dimg04.c-ctrip.com//images/0414u120008aszn6g73CD.jpg",
                  "https://dimg04.c-ctrip.com//images/0414s120008asz9c214CE.jpg",
                  "https://dimg04.c-ctrip.com//images/0413s120008aszt7c3700.jpg",
                  "https://dimg04.c-ctrip.com//images/0412b120008at09a8EB10.jpg",
                  "https://dimg04.c-ctrip.com//images/04124120008asyvbe198C.jpg"
                ],
                "license": "外牌",
                "licenseStyle": "6",
                "realityImageUrl": "https://dimg04.c-ctrip.com//images/04108120008asytgfE636.jpg",
                "oilType": 5
              }
            }
          ]
        },
        LocationAndDate: LocationAndData
      }
    });
    expect(pushFunc).toBeCalled();
  })

  test('提示需要重新查询', async () => {
    const pushFunc = jest.fn();
    AppContext.setPageInstance({
      push: pushFunc,
    })
    const LocationAndDate1 = {
      "rentalDate": {
        "pickUp": {
          "dateTime": "20221217100000"
        },
        "dropOff": {
          "dateTime": "20221218100000"
        }
      },
      "rentalLocation": {
        "pickUp": {
          "version": "3",
          "cid": 2,
          "cname": "北京",
          "country": "中国",
          "realcountry": "中国",
          "isDomestic": true,
          "area": {
            "id": "PVG",
            "name": "浦东国际机场",
            "lat": 31.150964,
            "lng": 121.803535,
            "type": "1"
          }
        },
        "dropOff": {
          "version": "3",
          "cid": 2,
          "cname": "上海",
          "country": "中国",
          "realcountry": "中国",
          "isDomestic": true,
          "area": {
            "id": "PVG",
            "name": "浦东国际机场",
            "lat": 31.150964,
            "lng": 121.803535,
            "type": "1"
          }
        },
        "isShowDropOff": false
      }
    };
    const LocationAndDate2 = {
      "rentalDate": {
        "pickUp": {
          "dateTime": "20221217100000"
        },
        "dropOff": {
          "dateTime": "20221218100000"
        }
      },
      "rentalLocation": {
        "pickUp": {
          "version": "3",
          "cid": 2,
          "cname": "上海",
          "country": "中国",
          "realcountry": "中国",
          "isDomestic": true,
          "area": {
            "id": "PVG",
            "name": "浦东国际机场",
            "lat": 31.150964,
            "lng": 121.803535,
            "type": "1"
          }
        },
        "dropOff": {
          "version": "3",
          "cid": 2,
          "cname": "上海",
          "country": "中国",
          "realcountry": "中国",
          "isDomestic": true,
          "area": {
            "id": "PVG",
            "name": "浦东国际机场",
            "lat": 31.150964,
            "lng": 121.803535,
            "type": "1"
          }
        },
        "isShowDropOff": false
      }
    };
    const dispatched = await recordSaga(goBooking, {
      action: {
        type: Types.GO_BOOKING,
        data: {
          vehicleIndex: 0,
        },
      },
      state: {
        RecommendVehicle: {
          brforeLocationAndDate: LocationAndDate1,
        },
        LocationAndDate: LocationAndDate2,
      }
    });
    expect(dispatched).toEqual([
      Actions.setSearchTipPopVisible({ visible: true, }),
    ])
  })
})

