import Reducer, { getInitalState } from '../../../src/pages/xcar/State/RecommendVehicle/Reducer';
import * as Actions from '../../../src/pages/xcar/State/RecommendVehicle/Types';
describe('Reducer Test', () => {
  const initState = getInitalState();

  test('Reducer Init', () => {
    expect(Reducer(undefined, {})).toEqual(initState);
  });

  const actionMap = [{
    type: Actions.FETCH_FLIGHTREC_PRODUCTS,
    data: {},
    expected: {
      ...initState,
    }
  }, {
    type: Actions.FETCH_FLIGHTREC_PRODUCTS_CALLBACK,
    data: {
      products: [{}],
      promptInfos: [],
      brforeLocationAndDate: {},
      isNetWorkError: false,
    },
    expected: {
      ...initState,
      products: [{}],
      promptInfos: [],
      brforeLocationAndDate: {},
      isLoadingFilgitRecProducts: false,
      isNetWorkError: false,
    }
  }, {
    type: Actions.FETCH_FLIGHTREC_PRODUCTS_LOADING,
    data: {},
    expected: {
      ...initState,
      saleOutList: [],
      products: [],
      isLoadingFilgitRecProducts: true,
      isNetWorkError: false,
    }
  }, {
    type: Actions.RESET,
    data: {},
    expected: initState
  }, {
    type: Actions.ADD_SALEOUT_LIST,
    data: '1212121212',
    expected: {
      ...initState,
      saleOutList: ["1212121212"]
    }
  }];

  test.each(actionMap)('%p', ({ type, data, expected }) => {
    expect(Reducer(initState, { type, data })).toEqual(expected);
  })
})

