import Utils from '../../../src/pages/xcar/Util/Utils';
import {
  getPhoneMenus,
  getOrderDetailDepositInfo,
  getCarAssitantData,
  mapChangeReminderToWaringInfo,
  getDamageProveRenderData,
  mapVioLationAndDamageDesc,
  getHomeParamFromOrder,
  getCancelReasonLists,
  setContactData,
  mapCarRentalMustReadToViolationRules,
  mapIsuranceBox,
  mapIsuranceBoxOsd,
} from './../../../src/pages/xcar/State/OrderDetail/Mappers';
import {
  isSupportCreaditMockStore,
  creditRentBackPayMockStore,
  hasAuthCreditRentMock,
  getCarAssitantDataMockMap,
  mapChangeReminderToWaringInfoMock,
  getDamageProveRenderDataMockMap,
  getMapVioLationAndDamageDescMockMap,
  getHomeParamFromOrderMockMap,
  getCarRentalMustReadToViolationRulesMock,
} from './StoreMocks';

jest.mock('../../../src/pages/xcar/State/StoreRef', () => ({
  getStore: () => ({
    getState: () => ({
      OrderDetail: {
        orderBaseInfo: {
          orderId: 1234567,
        },
        productDetails: [
          {
            insPackageId: 7,
            insuranceItems: [
              {
                productId: 18151660,
                title: '车辆碰撞险',
                description: '保障车辆碰撞损失',
                code: 'CDW',
                type: 7,
                name: '车辆碰撞险',
                isInclude: true,
                isFromCtrip: false,
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    excessShortDesc: '起赔额$100',
                    excessLongDesc: '',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                  ],
                },
                unConverageExplain: {
                  title: '不承保范围',
                  content: [
                    '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                  ],
                },
                claimProcess: {
                  title: '理赔流程',
                  subObject: [
                    {
                      title: '发生事故后报警并联系车行',
                      content: [
                        '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                      ],
                    },
                    {
                      title: '等待最终定审(45-60个工作日左右)',
                      content: [
                        '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                      ],
                    },
                  ],
                },
              },
              {
                productId: 18151660,
                title: '车辆盗抢险',
                description: '保障车辆被盗的损失',
                code: 'TP',
                type: 7,
                name: '车辆盗抢险',
                isInclude: true,
                isFromCtrip: false,
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    excessShortDesc: '起赔额$100',
                    excessLongDesc: '',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                  ],
                },
                unConverageExplain: {
                  title: '不承保范围',
                  content: [
                    '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">钥匙遗落车内导致被盗；</li><li style="margin-bottom:-25px;">钥匙丢失，损坏或被盗；</li><li style="margin-bottom:-25px;">车门忘关导致被盗；</li><li style="margin-bottom:-25px;">车内额外设备如GPS，WIFI，儿童座椅等；</li><li style="margin-bottom:-25px;">在未经授权的区域行驶；</li><li style="margin-bottom:-25px;">将车辆长时间停在不当的地方导致车辆被盗；</li><li style="margin-bottom:-25px;">因车辆被盗被抢导致的车辆停运费或维修手续费；</li><li style="margin-bottom:-25px;">违反合同或当地法律的情况下导致的损失；</li></ul>*实际赔付范围与标准以门店合同为准',
                  ],
                },
                claimProcess: {
                  title: '理赔流程',
                  subObject: [
                    {
                      title: '发生事故后报警并联系车行',
                      content: [
                        '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                      ],
                    },
                    {
                      title: '等待最终定审(45-60个工作日左右)',
                      content: [
                        '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                      ],
                    },
                  ],
                },
              },
              {
                productId: 18151660,
                title: '第三者责任险',
                description: '保障第三方车辆或人员伤害损失',
                code: 'TPL',
                type: 7,
                name: '第三者责任险',
                isInclude: true,
                isFromCtrip: false,
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    currencyCode: 'USD',
                    minExcess: 0,
                    maxExcess: 1000.0,
                    excessShortDesc: 'zh_cn：1,000USD',
                    excessLongDesc: '',
                    minCoverage: 30000.0,
                    maxCoverage: 30000.0,
                    coverageLongDesc: '',
                    coverageWithoutPlatformInsurance: '原起赔额$100可赔',
                    coverageShortDesc: '',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
                  ],
                },
                claimProcess: {
                  title: '理赔流程',
                  subObject: [
                    {
                      title: '报警并联系车行',
                      content: [
                        '发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况',
                      ],
                    },
                    {
                      title: '还车时向车行提交理赔',
                      content: [
                        '在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料',
                      ],
                    },
                    {
                      title: '等待最终定审(45-60个工作日左右)',
                      content: [
                        '车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                      ],
                    },
                  ],
                },
              },
              {
                code: 'TPL',
                name: '安心补充险',
                description: '保障玻璃轮胎底盘；补偿道路救援费用',
                isInclude: false,
                isFromCtrip: true,
                giveUp: true,
                insuranceStatus: 4,
                groupCode: '1',
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    currencyCode: 'USD',
                    minExcess: 1000.0,
                    maxExcess: 1000.0,
                    excessShortDesc: '',
                    excessLongDesc: '',
                    minCoverage: 30000.0,
                    maxCoverage: 30000.0,
                    coverageShortDesc: '保额：30,000USD',
                    coverageLongDesc: '',
                    coverageWithoutPlatformInsurance:
                      '国内保险公司提供¥50/天',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
                  ],
                },
                unConverageExplain: {
                  title: '不承保范围',
                },
              },
              {
                code: 'TPL',
                name: '安心补充险222',
                description: '保障玻璃轮胎底盘；补偿道路救援费用',
                isInclude: false,
                isFromCtrip: true,
                giveUp: false,
                insuranceStatus: 4,
                insuranceStatusDesc: '已取消',
                groupCode: '1',
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    currencyCode: 'USD',
                    minExcess: 1000.0,
                    maxExcess: 1000.0,
                    excessShortDesc: '',
                    excessLongDesc: '',
                    minCoverage: 30000.0,
                    maxCoverage: 30000.0,
                    coverageShortDesc: '保额：30,000USD',
                    coverageLongDesc: '',
                    coverageWithoutPlatformInsurance:
                      '国内保险公司提供¥50/天',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
                  ],
                },
                unConverageExplain: {
                  title: '不承保范围',
                },
              },
              {
                code: 'CTRIP_CDW',
                name: '驾乘意外险',
                description: '全车人员意外伤害及财物损失',
                isInclude: true,
                isFromCtrip: true,
                groupCode: '3',
                insuranceStatus: 3,
                insuranceStatusDesc: '已出保',
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    currencyCode: 'USD',
                    minExcess: 1000.0,
                    maxExcess: 1000.0,
                    excessShortDesc: '',
                    excessLongDesc: '',
                    minCoverage: 30000.0,
                    maxCoverage: 30000.0,
                    coverageShortDesc: '保额：30,000USD',
                    coverageLongDesc: '',
                    coverageWithoutPlatformInsurance:
                      '国内保险公司提供¥50/天',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                  ],
                },
                unConverageExplain: {
                  title: '不承保范围',
                  content: [
                    '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                  ],
                },
                itemUrl: 'www.保单详情.com',
                coverDetailList: [
                  {
                    title: '最高保额',
                    subTitle: '保障范围',
                  },
                  {
                    title: '500000元',
                    descList: [
                      '碰撞险和盗抢险(提车时租车合同中约定的起赔额以下自付)',
                      '玻璃轮胎或底盘的损坏费用',
                      '由于意外事故产生的故障救援、拖车、人工费车辆所、钥匙替换费',
                    ],
                  },
                  {
                    title: '500000元',
                    descList: [
                      '碰撞险和盗抢险(提车时租车合同中约定的起赔额以下自付)',
                      '玻璃轮胎或底盘的损坏费用',
                      '由于意外事故产生的故障救援、拖车、人工费车辆所、钥匙替换费',
                    ],
                  },
                ],
              },
            ],
            productInfoList: [
              {
                packageItems: [
                  {
                    code: '',
                    name: '',
                    desc: '1名额外驾驶员',
                    sortNum: 0,
                  },
                  {
                    code: 'CDW',
                    name: '碰撞险',
                    desc: '车辆碰撞险',
                    sortNum: 0,
                  },
                  {
                    code: '',
                    name: '',
                    desc: '满油取还',
                    sortNum: 0,
                  },
                  {
                    code: '',
                    name: '',
                    desc: '基础租车费用',
                    sortNum: 0,
                  },
                  {
                    code: '',
                    name: '',
                    desc: '限里程',
                    sortNum: 0,
                  },
                  {
                    code: 'CDW',
                    name: '碰撞险',
                    desc: '车辆盗抢险',
                    sortNum: 0,
                  },
                  {
                    code: 'TPI',
                    name: '三者险',
                    desc: '第三者责任险',
                    sortNum: 0,
                  },
                  {
                    code: '',
                    name: '',
                    desc: '税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税',
                    sortNum: 0,
                  },
                ],
                minPackageItmes: [
                  {
                    title: '车辆碰撞险',
                    type: 1,
                    code: 'CDW',
                    description: '',
                  },
                  {
                    title: '车辆盗抢险',
                    type: 1,
                    code: 'TP',
                    description: '',
                  },
                  {
                    title: '第三者责任险',
                    type: 1,
                    code: 'TPL',
                    description: '',
                  },
                  {
                    title: '限里程',
                    type: 2,
                    code: 'LM',
                    description: '租期内有公里数限制',
                  },
                  {
                    title: '1名额外驾驶员',
                    type: 3,
                    code: 'ADD1',
                    description:
                      '一名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
                  },
                  {
                    title: '满油取还',
                    type: 4,
                    code: 'FRFB',
                    description:
                      '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
                  },
                  {
                    title: '基础租车费用',
                    type: 8,
                    code: 'Fees',
                    description: '仅包含车辆租金的基础费用',
                  },
                ],
              },
            ],
            claimsProcess: [
              {
                subTitle:
                  '发生事故(本车与第三方，或本车单独发生事故)时请按下方流程处理，否则无法获得赔偿。',
                subObject: [
                  {
                    type: 1,
                    title: '立即联系门店',
                    content: [
                      '发生事故后，请立即联系门店获取处理建议。门店将向您提供必要的指导，可能包括但不限于拨打当地报警电话获取警方报告，记录事故现场照片等',
                    ],
                  },
                  {
                    type: 2,
                    title: '还车时向车行提交车行保障理赔',
                    content: [
                      '租期结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的场景有：从您信用卡预授权中扣除起赔额费用(若有)，待门店确认最终损失金额(一般需要45-60个工作日)。若最终损失金额大于起赔额，您只需支付起赔额；若小于起赔额，门店将原路退还差价。',
                    ],
                  },
                ],
              },
              {
                subTitle:
                  '超级补充全险理赔时，您需额外完成如下步骤。携程将全程协助您的理赔',
                subObject: [
                  {
                    type: 1,
                    title: '车损重要理赔材料',
                    content: [
                      '车损照片、维修费用支付凭证、维修/车损清单、警方报告等。',
                    ],
                  },
                  {
                    type: 2,
                    title: '理赔材料审核',
                    content: [
                      '材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充。',
                    ],
                  },
                  {
                    type: 3,
                    title: '理赔款到账',
                    content: [
                      '理赔款会以银行转账方式支付到被保险人提供的境内银行账户。',
                    ],
                  },
                ],
                url: 'www.查看详细理赔材料的pdf链接.com',
              },
            ],
          },
        ],
        ctripInsuranceInfos: [],
      }
    })
  })
}));

afterEach(() => {
  jest.resetAllMocks();
});

describe('getPhoneMenus', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          pickupStore: {
            storeTel: '13601777729,13681604559',
          },
          returnStore: {
            storeTel: '13601777729,13681604559',
          },
        },
      },
      expected: [
        {
          name: '门店电话',
          tels: ['13601777729,13681604559'],
        },
      ],
    },
    {
      state: {
        OrderDetail: {
          phoneModalType: 1,
          pickupStore: {
            storeTel: '13601777729',
          },
          returnStore: {
            storeTel: '13601777729,13681604559',
          },
        },
      },
      expected: [
        {
          name: '取车门店电话',
          tels: ['13601777729'],
        },
        {
          name: '还车门店电话',
          tels: ['13601777729,13681604559'],
        },
      ],
    },
  ];
  test.each(mockStateMap)('测试获取手机', ({ state, expected }) => {
    const data = getPhoneMenus(state);
    expect(data).toEqual(expected);
  });
});

describe('getOrderDetailDepositInfo', () => {
  test('信用租:芝麻', () => {
    const data = getOrderDetailDepositInfo(isSupportCreaditMockStore);
    expect(data.showCreditRent).toBeTruthy;
  });

  test('信用租:后付', () => {
    const data = getOrderDetailDepositInfo(creditRentBackPayMockStore);
    expect(data.showCreditRent).toBeTruthy;
  });

  test('信用租:已支付', () => {
    const data = getOrderDetailDepositInfo(hasAuthCreditRentMock);
    expect(data.showCreditRent).toBeFalsy;
  });
});

describe('mapChangeReminderToWaringInfo', () => {
  test.each(mapChangeReminderToWaringInfoMock)(
    '测试changeReminder 转换到 waringInfo格式',
    ({ data, expected }) => {
      const info = mapChangeReminderToWaringInfo(data);
      expect(info).toEqual(expected);
    },
  );
});

describe('getDamageProveRenderData', () => {
  test.each(getDamageProveRenderDataMockMap)(
    '%p',
    ({ damageImgList, expected }) => {
      const data = getDamageProveRenderData(damageImgList);
      expect(data).toEqual(expected);
    },
  );
});

describe('mapVioLationAndDamageDesc', () => {
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
  test.each(getMapVioLationAndDamageDescMockMap)(
    '%p',
    ({ state, expected, isCtripIsd }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(isCtripIsd);
      const data = mapVioLationAndDamageDesc(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getHomeParamFromOrder', () => {
  test.each(getHomeParamFromOrderMockMap)('%p', ({ state, expected }) => {
    const data = getHomeParamFromOrder(state);
    expect(data).toEqual(expected);
  });
});

describe('getCancelReasonLists', () => {
  test('获取国内取消原因', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    expect(
      getCancelReasonLists({
        OrderDetail: {
          orderCancelInfo: {
            cancelReasonList: [],
          },
        },
      }),
    ).toEqual([]);
  });
  test('获取国内取消原因', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    expect(
      getCancelReasonLists({
        OrderDetail: {
          orderCancelInfo: {
            cancelReasonList: [
              {
                code: 6,
                reason: '限行限号',
                sort: 1,
              },
              {
                code: 15,
                reason: '颜色/配置不满意',
                sort: 2,
              },
              {
                code: 16,
                reason: '车辆信息错误',
                sort: 3,
              },
            ],
          },
        },
      }),
    ).toEqual([
      {
        code: 6,
        reason: '限行限号',
        sort: 1,
      },
      {
        code: 15,
        reason: '颜色/配置不满意',
        sort: 2,
      },
      {
        code: 16,
        reason: '车辆信息错误',
        sort: 3,
      },
    ]);
  });

  test('获取境外取消原因', () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest.spyOn(Utils, 'isTrip').mockReturnValue(false);
    expect(
      getCancelReasonLists({
        OrderDetail: {
          cancelRuleInfo: {
            cancelTip: '2021-06-20 06:00前免费取消，逾期收费。',
            reOrderTitle: '仍需用车？',
            reOrderExplain:
              '若门店车辆不满足您对颜色、牌照、配置或出行的相关需求，您可重新预订',
            cancelReasons: ['重新下单更优惠', '其他网站更便宜'],
          },
        },
      }),
    ).toEqual([
      {
        isSelect: false,
        title: '重新下单更优惠',
      },
      {
        isSelect: false,
        title: '其他网站更便宜',
      },
    ]);
  });
});

describe('setContactData', () => {
  const phoneNumberList = [
    {
      number: '13062544144',
      type: 'virtual',
      role: 'PICKUP_STORE',
    },
    {
      number: '13044125346',
      type: 'virtual',
      role: 'RETURN_STORE',
    },
    {
      number: '15501131098',
      type: 'virtual',
      role: 'DELIVERER',
    },
    {
      number: '13062544135',
      type: 'virtual',
      role: 'COLLECTOR',
    },
  ];
  const phoneNumberList2 = [
    {
      number: '13062544144',
      type: 'origin',
      role: 'PICKUP_STORE',
    },
    {
      number: '15501131098',
      type: 'origin',
      role: 'DELIVERER',
    },
    {
      number: '13062544135',
      type: 'origin',
      role: 'COLLECTOR',
    },
  ];
  const ClickKey = {
    C_ORDER_CONTACTSTOREMODAL_VIRTUALNUMBER: {
      KEY: 'test',
    },
    C_ORDER_VIOLATIONDETAILS_VIRTUALNUMBER: {
      KEY: 'test',
    },
    C_ORDER_NOVIOLATIONDETAILS_VIRTUALNUMBER: {
      KEY: 'test',
    },
    C_ORDER_FAQCONTACTSTORE_VIRTUALNUMBER: {
      KEY: 'test',
    },
    C_HOME_ORDERCARDCONTACTSTORE_VIRTUALNUMBER: {
      KEY: 'test',
    },
    tourImJumpUrl: '',
  };
  const mockStateMap = [
    {
      params: {
        phoneNumberData: {
          phoneNumberList,
        },
        type: 1,
        storeAttendant: '',
        icon: { phone: 'phone', customerChat: 'customerChat' },
        IStoreAttendantType: '',
        PhoneNumberRole: {
          COLLECTOR: 'COLLECTOR',
        },
        vendorImUrl: '123',
        phoneModalFromWhere: 1,
        ClickKey,
      },
      expected: [
        {
          secAndPrivacyTips:
            '为保护您的隐私，对方看不到您的真实号码；为保障服务质量，您的通话可能会被录音',
          titleLineInfo: {
            title: '送/取车员',
            subTitle: '送取车进度、取/还车地址等问题',
          },
          contactLists: [
            {
              title: '取车员电话',
              virtualNumber: '13062544135',
              isRecommend: true,
              rightIcon: 'phone',
            },
          ],
        },
        {
          secAndPrivacyTips: '线上会话将被安全保护，推荐使用',
          titleLineInfo: {
            title: '门店',
            subTitle: '车辆、门店、取还车等问题',
          },
          contactLists: [
            {
              title: '门店在线客服',
              isRecommend: true,
              vendorImUrl: '123',
              rightIcon: 'customerChat',
            },
          ],
        },
        {
          titleLineInfo: { title: '携程', subTitle: '订单、支付等问题' },
          contactLists: [
            {
              title: '携程在线客服',
              rightIcon: undefined,
              vendorImUrl: undefined,
            },
          ],
        },
      ],
    },
    {
      params: {
        phoneNumberData: {
          phoneNumberList,
        },
        type: 1,
        storeAttendant: '',
        icon: { phone: 'phone', customerChat: 'customerChat' },
        IStoreAttendantType: '',
        PhoneNumberRole: {
          DELIVERER: 'DELIVERER',
        },
        vendorImUrl: '',
        phoneModalFromWhere: 1,
        ClickKey,
      },
      expected: [
        {
          secAndPrivacyTips:
            '为保护您的隐私，对方看不到您的真实号码；为保障服务质量，您的通话可能会被录音',
          titleLineInfo: {
            title: '送/取车员',
            subTitle: '送取车进度、取/还车地址等问题',
          },
          contactLists: [
            {
              title: '送车员电话',
              virtualNumber: '15501131098',
              isRecommend: true,
              rightIcon: 'phone',
            },
          ],
        },
        {
          titleLineInfo: { title: '携程', subTitle: '订单、支付等问题' },
          contactLists: [
            {
              title: '携程在线客服',
              rightIcon: undefined,
              vendorImUrl: undefined,
            },
          ],
        },
      ],
    },
    {
      params: {
        phoneNumberData: {
          phoneNumberList: [],
        },
        type: 3,
        storeAttendant: '',
        icon: { phone: 'phone', customerChat: 'customerChat' },
        IStoreAttendantType: '',
        PhoneNumberRole: 'DELIVERER',
        vendorImUrl: '',
        phoneModalFromWhere: 1,
        ClickKey,
      },
      expected: [],
    },
    {
      params: {
        phoneNumberData: undefined,
        type: 3,
        storeAttendant: '',
        icon: { phone: 'phone', customerChat: 'customerChat' },
        IStoreAttendantType: '',
        PhoneNumberRole: 'DELIVERER',
        vendorImUrl: '',
        phoneModalFromWhere: 1,
        ClickKey,
      },
      expected: [],
    },
    {
      params: {
        phoneNumberData: {
          phoneNumberList,
        },
        type: 3,
        storeAttendant: '',
        icon: { phone: 'phone', customerChat: 'customerChat' },
        IStoreAttendantType: '',
        PhoneNumberRole: {
          PICKUP_STORE: 'PICKUP_STORE',
        },
        vendorImUrl: '123',
        phoneModalFromWhere: 1,
        ClickKey,
        PhoneModalFromWhere: {},
      },
      expected: [
        {
          secAndPrivacyTips: '线上会话将被安全保护，推荐使用',
          titleLineInfo: {
            title: '门店',
            subTitle: '车辆、门店、取还车等问题',
          },
          contactLists: [
            {
              title: '门店在线客服',
              isRecommend: true,
              vendorImUrl: '123',
              rightIcon: 'customerChat',
            },
          ],
        },
        {
          secAndPrivacyTips:
            '为保护您的隐私，对方看不到您的真实号码；为保障服务质量，您的通话可能会被录音',
          contactLists: [
            {
              title: '门店电话',
              virtualNumber: '13062544144',
              rightIcon: 'phone',
              clickLog: {  "name": "点击_订单详情页-无违章详情-联系门店_手机虚拟号", info: { onPressFrom: 3 } },
            },
          ],
        },
      ],
    },
    {
      params: {
        phoneNumberData: {
          phoneNumberList,
        },
        type: 3,
        storeAttendant: '',
        icon: { phone: 'phone', customerChat: 'customerChat' },
        IStoreAttendantType: '',
        PhoneNumberRole: {
          PICKUP_STORE: 'PICKUP_STORE',
        },
        vendorImUrl: '123',
        phoneModalFromWhere: 1,
        ClickKey,
        PhoneModalFromWhere: {},
      },
      expected: [
        {
          secAndPrivacyTips: '线上会话将被安全保护，推荐使用',
          titleLineInfo: {
            title: '门店',
            subTitle: '车辆、门店、取还车等问题',
          },
          contactLists: [
            {
              title: '门店在线客服',
              isRecommend: true,
              vendorImUrl: '123',
              rightIcon: 'customerChat',
            },
          ],
        },
        {
          secAndPrivacyTips:
            '为保护您的隐私，对方看不到您的真实号码；为保障服务质量，您的通话可能会被录音',
          contactLists: [
            {
              title: '门店电话',
              virtualNumber: '13062544144',
              rightIcon: 'phone',
              clickLog: { "name": "点击_订单详情页-无违章详情-联系门店_手机虚拟号", info: { onPressFrom: 3 } },
            },
          ],
        },
      ],
    },
    {
      params: {
        phoneNumberData: {
          phoneNumberList,
        },
        type: 6,
        storeAttendant: '',
        icon: { phone: 'phone', customerChat: 'customerChat' },
        IStoreAttendantType: '',
        PhoneNumberRole: {
          PICKUP_STORE: 'PICKUP_STORE',
        },
        vendorImUrl: '123',
        phoneModalFromWhere: 1,
        ClickKey,
        PhoneModalFromWhere: {},
      },
      expected: [
        {
          secAndPrivacyTips: '线上会话将被安全保护，推荐使用',
          titleLineInfo: {
            title: '门店',
            subTitle: '车辆、门店、取还车等问题',
          },
          contactLists: [
            {
              title: '门店在线客服',
              isRecommend: true,
              vendorImUrl: '123',
              rightIcon: 'customerChat',
            },
          ],
        },
        {
          secAndPrivacyTips:
            '为保护您的隐私，对方看不到您的真实号码；为保障服务质量，您的通话可能会被录音',
          contactLists: [
            {
              title: '门店电话',
              virtualNumber: '13062544144',
              rightIcon: 'phone',
              clickLog: { "name": "点击_国内租车首页_租车订单卡片_联系门店_手机虚拟号", info: { onPressFrom: 6 } },
            },
          ],
        },
      ],
    },
    {
      params: {
        phoneNumberData: {
          phoneNumberList: phoneNumberList2,
        },
        type: 6,
        storeAttendant: '',
        icon: { phone: 'phone', customerChat: 'customerChat' },
        IStoreAttendantType: '',
        PhoneNumberRole: {
          PICKUP_STORE: 'PICKUP_STORE',
          RETURN_STORE: 'RETURN_STORE',
          COLLECTOR: 'COLLECTOR',
          DELIVERER: 'DELIVERER',
        },
        vendorImUrl: '123',
        phoneModalFromWhere: 1,
        ClickKey,
        PhoneModalFromWhere: {},
      },
      expected: [
        {
          secAndPrivacyTips: '',
          titleLineInfo: {
            title: '送/取车员',
            subTitle: '送取车进度、取/还车地址等问题',
          },
          contactLists: [
            {
              title: '送车员电话',
              virtualNumber: '15501131098',
              isRecommend: true,
              rightIcon: 'phone',
            },
            {
              title: '取车员电话',
              virtualNumber: '13062544135',
              isRecommend: true,
              rightIcon: 'phone',
            },
          ],
        },
        {
          secAndPrivacyTips: '线上会话将被安全保护，推荐使用',
          titleLineInfo: {
            title: '门店',
            subTitle: '车辆、门店、取还车等问题',
          },
          contactLists: [
            {
              title: '门店在线客服',
              isRecommend: true,
              vendorImUrl: '123',
              rightIcon: 'customerChat',
            },
          ],
        },

        {
          secAndPrivacyTips:
            '为保护您的隐私，对方看不到您的真实号码；为保障服务质量，您的通话可能会被录音',
          contactLists: [
            {
              title: '门店电话',
              virtualNumber: '13062544144',
              rightIcon: 'phone',
              clickLog: { "name": "点击_国内租车首页_租车订单卡片_联系门店_手机虚拟号", info: { onPressFrom: 6 } },
            },
          ],
        },
      ],
    },
  ];
  test.each(mockStateMap)('测试setContactData', ({ params, expected }) => {
    const paramArr = Object.values(params);
    // @ts-ignore
    const data = setContactData(...paramArr);
    expect(data).toEqual(expected);
  });
});

describe('mapCarRentalMustReadToViolationRules', () => {
  test.each(getCarRentalMustReadToViolationRulesMock)(
    '%p',
    ({ data, expected }) => {
      const content = mapCarRentalMustReadToViolationRules(data);
      expect(content).toEqual([expected]);
    },
  );
});

jest.mock('../../../src/pages/xcar/State/StoreRef', () => ({
  getStore: () => ({
    getState: () => ({
      OrderDetail: {
        response: {
          ResponseStatus: {
            Timestamp: '2023-04-20 11:16:31',
            Ack: 'Success',
            Errors: [],
            Extension: [],
          },
          naked: false,
          orderBaseInfo: {
            orderId: 23721524311,
            uId: '_WeChat2869616540',
            channelType: '17671',
            orderDate: 1681813905000,
            orderLocale: 'zh_cn',
            orderStatus: 4,
            orderStatusDesc: '已取消',
            orderStatusName: '已取消',
            orderStatusCtrip: 'CAR_CANCELLED',
            allStatuses: [],
            allOperations: [
              {
                operationId: 4,
                buttonName: '再次预订',
                enable: true,
              },
              {
                operationId: 3,
                enable: false,
                code: 4,
              },
            ],
            orderTip: {
              tipType: 5,
              tipContent:
                '您的订单已免费取消。款项将于1-5个工作天內退还至原付款账户，实际时间受您的银行影响。',
              tipContentArray: [
                '您的订单已免费取消。款项将于1-5个工作天內退还至原付款账户，实际时间受您的银行影响。',
              ],
              warnType: 0,
            },
            payMode: 2,
            isEasyLife: false,
            extOperation: [],
          },
          continuePayInfo: {
            needContinuePay: false,
          },
          orderPriceInfo: {
            packageType: 0,
            currentTotalPrice: 3639.0,
            currentCurrencyCode: 'CNY',
            localTotalPrice: 529.0,
            localCurrencyCode: 'USD',
            payMode: 2,
            payModeDesc: '在线支付',
            prepayPrice: {
              title: '',
              totalPrice: 3239.0,
              currencyCode: 'CNY',
            },
            localPrice: {
              title: '',
              totalPrice: 0.0,
              currencyCode: 'USD',
            },
            prepayPriceDetails: [
              {
                title: 'shark|common_carHireFee',
                totalPrice: 3239.0,
                currencyCode: 'CNY',
              },
            ],
            localPriceDetails: [],
            priceDetails: [
              {
                title: 'shark|common_carHireFee',
                totalPrice: 3639.0,
                currencyCode: 'CNY',
              },
            ],
            payAmount: 3239.0,
            couponAmount: 400.0,
            coupons: [
              {
                couponCode: 'fjzkpczflt',
                promotionId: 555456554,
                isNeedDebate: false,
                deductionAmount: 400,
                remark:
                  '1.优惠券仅限携程租车新用户，以预付方式（在线支付）预订国内/国际/中国港澳台的租车产品可用（部分供应商不可用，具体以列表页展示为准）；2.可享租车费满100-20/满200-40/满400-80/满700-140/满1000-200/满2000-400/满4500-888元（不含手续费、基础服务费、优享服务费、异地还车费等费用）；3.优惠券有效期7天，从发放日开始计算，过期未使用则自动失效；4.支持网页、APP、小程序用券，每张订单限用一张优惠券，每张优惠券限用一次，不可与其他优惠和活动同享。优惠券不可折现、不退差额、不可拆分使用，续租按非优惠价收费；5.退赔规则：（1）使用券的订单无损取消、超时未支付、购买失败或因订单变更导致订单金额未满足优惠门槛的，若该券尚未失效将退回原账户，若该券已失效不予退回；（2）如订单支持变更，已用券的订单因变更导致订单金额增加的，仅可享受变更前的阶梯优惠金额；已用券的订单如取消且产生取消费用的，优惠券因涉及抵扣（或部分抵扣）将不予退回。',
                displayName: ' 租车新客专享满减券',
              },
            ],
          },
          vehicleInfo: {
            vehicleName: '路虎 揽胜 运动版',
            special: false,
            passengerNum: 5,
            luggageNum: 4,
            hasAC: false,
            transmission: 'AT',
            vehicleGroupName: '豪华SUV',
            vendorVehicleCode: 'WFBV_86_LANDROVERRANGEROVERSPORT',
            imageUrl: '//ak-d.tripcdn.com/images/0AS131200096r0chk61F6.png',
            vehicleDisplacement: '',
            doorNum: 4,
          },
          vendorInfo: {
            vendorName: '星河租车',
            vendorImageUrl:
              '//pages.trip.com/cars/image/totrip/2e945a83-f3e0-4e8e-b947-13e4f37374ba.png',
            confirmRightNow: true,
            confirmDate: 1681760505000,
            confirmDateStr: '2023-04-18 03:41',
            bizVendorCode: '14120',
            commentInfo: {
              vendorGoodType: 0,
              exposedScore: 5.0,
              topScore: 5.0,
              level: '超棒',
              commentCount: 91,
              hasComment: 1,
            },
            broker: false,
          },
          fuelIsGiven: false,
          fuelInfo: {
            isGiven: false,
            code: 'FRFB',
            name: '满油取还',
            desc: '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
          },
          mileageAllowance: {
            isUnLimited: false,
            limitedDistance: 161.0,
            limitedDistanceUnit: 'km',
            limitedPeriodUnit: '每天',
            overUnitAmount: 2.0,
            overQuantity: 1.0,
            overDistanceUnit: '英里',
            mileAgeDesc: '100 Miles Free per day, additional per Miles $ 2.00',
          },
          pickupStore: {
            localDateTime: '2023-06-15 10:00:00',
            storeName: 'LAX',
            storeCode: 'LAXSR7',
            storeAddress:
              '5757 W Century Blvd Suite 110, Los Angeles, CA 90045',
            longitude: -118.382774,
            latitude: 33.946384,
            storeGuide:
              '门店位于机场外，距离机场约3分钟。到达机场出关后，请联系门店工作人员****** 371 7777或客服微信号starriverrental安排送车，请在航站楼外B7立柱旁等候，等待时间约15-25分钟。取车后，工作人员可根据需求提供驾驶指导。门店营业时间为早7点至晚12点，如因个人原因需更改取车时间，请务必提前24小时联系门店进行免费更改。如因航班晚点或其他原因取车延误错过营业时间，可在第二天营业时间内联系门店完成取车。',
            storeLocation: '机场外',
            storeWay: '可搭乘免费巴士到达',
            storeTel: '6263717777',
            storeOpenTimeDesc: '{"星期一 ～ 星期日":"07:00-23:55"}',
            cityName: '洛杉矶',
            provinceName: '加利福尼亚州',
            countryName: '美国',
            userSearchLocation: '洛杉矶国际机场',
            mapUrl: '',
            storeID: 504722,
            location: {
              locationType: 1,
              locationName: '洛杉矶国际机场',
              locationCode: 'LAX',
              continent: {
                id: 4,
                name: '美国',
              },
              country: {
                id: 66,
                name: '美国',
              },
              province: {
                id: 10125,
                name: '加利福尼亚州',
              },
              city: {
                id: 347,
                name: '洛杉矶',
              },
              poiInfo: {
                latitude: 33.94158900000000045338310883380472660064697265625,
                longitude: -118.4085299999999989495336194522678852081298828125,
                type: 1,
              },
            },
            disclaimer: 'The above information is provided by the branch',
          },
          returnStore: {
            localDateTime: '2023-06-16 10:00:00',
            storeName: 'LAX',
            storeCode: 'LAXSR7',
            storeAddress:
              '5757 W Century Blvd Suite 110, Los Angeles, CA 90045',
            longitude: -118.382774,
            latitude: 33.946384,
            storeGuide:
              '还车前，请提前联系门店工作人员****** 371 7777或微信客服号starriverrental，门店工作人员会提供详细还车指南。如因个人原因需提前还车，请务必提前48小时联系门店进行免费更改。门店营业时间为早7点至晚12点。如因航班或其他原因提前或延误还车，门店值班人员按$30美元每小时收取超时等待费用。',
            storeLocation: '机场外',
            storeWay: '',
            storeTel: '6263717777',
            storeOpenTimeDesc: '{"星期一 ～ 星期日":"07:00-23:55"}',
            cityName: '洛杉矶',
            provinceName: '加利福尼亚州',
            countryName: '美国',
            userSearchLocation: '洛杉矶国际机场',
            mapUrl: '',
            storeID: 504722,
            location: {
              locationType: 1,
              locationName: '洛杉矶国际机场',
              locationCode: 'LAX',
              continent: {
                id: 4,
                name: '美国',
              },
              country: {
                id: 66,
                name: '美国',
              },
              province: {
                id: 10125,
                name: '加利福尼亚州',
              },
              city: {
                id: 347,
                name: '洛杉矶',
              },
              poiInfo: {
                latitude: 33.94158900000000045338310883380472660064697265625,
                longitude: -118.4085299999999989495336194522678852081298828125,
                type: 1,
              },
            },
            disclaimer: 'The above information is provided by the branch',
          },
          driverInfo: {
            name: '5975b3abe2791dbf06d6ca0c511de0c97dbe400b548762351b20ed664178fd50',
            age: '29',
            email:
              '7407150e232ad11be351cf1aedf1080249a3da0c8319786db322c13f12e9348d',
            telphone:
              'd94a03e85326c1196ab8fb757a56f935a28026796008ec99cbbc0d4ee226df41',
            areaCode: '86',
            decryptTelphone: '17501640500',
            decryptMail: '<EMAIL>',
            lastName:
              'e35ca60b94885827c95d018638760d3ca96e1b3b0cbb67d2c31f1be47be7e349',
            firstName:
              '9666dee93929e456370ee51ae8474b043cf1e1e8a40bb73a197c899e210ebb32',
          },
          groupingPackageIncludes: [
            {
              name: '燃油政策',
              type: 1,
              items: ['满油取还'],
              descriptions: [
                '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
              ],
            },
            {
              name: '里程限制',
              type: 2,
              items: ['限里程'],
              descriptions: ['租期内有公里数限制'],
            },
            {
              name: '额外驾驶员',
              type: 3,
              items: ['1名额外驾驶员'],
              descriptions: [
                '一名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
              ],
            },
            {
              name: '保险',
              type: 4,
              items: ['车辆碰撞险', '车辆盗抢险', '第三者责任险'],
              descriptions: [
                '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
              ],
            },
            {
              name: '税费',
              type: 5,
              items: [
                '税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税',
              ],
              descriptions: [
                '税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。',
              ],
            },
          ],
          extraInfos: [],
          additionalDriverCount: 1,
          creditCardInfo: {
            cards: [
              '维萨',
              '万事达',
              '美国运通',
              'AirPlus',
              '大来卡',
              'Discover',
              '支付宝',
              '微信支付',
            ],
            description:
              '取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。高档车型需要两张信用卡。预计押金为US$2,500.00（约¥17,189.00）,请保证可用额度足以支付押金。押金预计会在还车后28-45天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。',
            depositCurrencyCode: 'USD',
            maxDeposit: 2500.0,
            minDeposit: 2500.0,
            payWaysNames: [null, null, null, null, null, null, null, null],
          },
          insuranceDescriptions: [
            {
              code: 'CDW',
              name: '车辆碰撞险',
              currencyCode: 'USD',
              minExcess: 2500.0,
              maxExcess: 2500.0,
              longDesc:
                '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
              coverageDesc: '保障车辆碰撞损失',
              unCoverageDesc:
                '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'TP',
              name: '车辆盗抢险',
              currencyCode: 'USD',
              minExcess: 2500.0,
              maxExcess: 2500.0,
              longDesc:
                '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
              coverageDesc: '保障车辆被盗的损失',
              unCoverageDesc:
                '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">钥匙遗落车内导致被盗；</li><li style="margin-bottom:-25px;">钥匙丢失，损坏或被盗；</li><li style="margin-bottom:-25px;">车门忘关导致被盗；</li><li style="margin-bottom:-25px;">车内额外设备如GPS，WIFI，儿童座椅等；</li><li style="margin-bottom:-25px;">在未经授权的区域行驶；</li><li style="margin-bottom:-25px;">将车辆长时间停在不当的地方导致车辆被盗；</li><li style="margin-bottom:-25px;">因车辆被盗被抢导致的车辆停运费或维修手续费；</li><li style="margin-bottom:-25px;">违反合同或当地法律的情况下导致的损失；</li></ul>*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'TPL',
              name: '第三者责任险',
              currencyCode: 'USD',
              minExcess: 1000.0,
              maxExcess: 1000.0,
              minCoverage: 30000.0,
              maxCoverage: 30000.0,
              longDesc:
                '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
              coverageDesc: '保障第三方车辆或人员伤害损失',
              unCoverageDesc: '',
            },
          ],
          cancelRuleInfo: {
            isTotalLoss: false,
            isFreeCancel: true,
            hours: 0,
            cancelDescription:
              '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
            cancelReasons: [
              '行程变更/取消',
              '修改订单',
              '重复下单',
              '车不能跨境跨岛',
              '信用卡问题',
              '驾照证件问题',
              '其他网站更便宜',
              '其他',
            ],
            modifyTip: {
              title: 'Booking cannot be modified',
              desc: 'If you need to make changes, cancel it and rebook',
            },
          },
          driverDescriptions: [
            {
              title: '中国驾照原件',
              info: [
                {
                  description: '中国驾照原件',
                  highLight: false,
                },
              ],
            },
          ],
          warmTips: [],
          ageInfo: {
            description:
              '租车公司对22-25周岁的低龄驾驶员将收取“青年驾驶费”，费用以到店支付为准',
            minDriverAge: 22,
            maxDriverAge: 75,
            youngDriverAge: 25,
            oldDriverAge: 0,
            youngDriverAgeDesc:
              '租车公司对22-25周岁的低龄驾驶员将收取“青年驾驶费”，费用以到店支付为准',
            oldDriverAgeDesc: '',
            licenceAge: 1,
            licenceAgeDesc: '驾龄至少满1年',
          },
          arrivalDetails: [],
          refundProgressList: [],
          ctripInsuranceInfos: [],
          appOrderDetailIsAddInsuranceNeeded: false,
          appOrderDetailIsSettlementOfClaimOpen: true,
          orderRenewalEntry: {},
          baseResponse: {
            isSuccess: true,
            code: 'unknown',
            returnMsg: 'success',
            requestId: '',
            cost: 348,
          },
          packageInfos: [
            {
              insPackageId: 7,
              isDefault: true,
              packageName: '强化保障套餐',
              currencyCode: 'USD',
              defaultBomCode: '',
              defaultPackageId: 18151660,
              guaranteeDegree: 2.0,
              naked: false,
              lowestDailyPrice: 0,
              gapPrice: 0,
              stepPrice: 0,
            },
          ],
          productDetails: [
            {
              insPackageId: 7,
              insuranceItems: [
                {
                  productId: 18151660,
                  title: '车辆碰撞险',
                  description: '保障车辆碰撞损失',
                  code: 'CDW',
                  type: 7,
                  name: '车辆碰撞险',
                  isInclude: true,
                  isFromCtrip: false,
                  insuranceDetail: [
                    {
                      packageId: 18151660,
                      excessShortDesc: '起赔额$100',
                      excessLongDesc: '',
                    },
                  ],
                  converageExplain: {
                    title: '承保范围',
                    content: [
                      '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                    ],
                  },
                  unConverageExplain: {
                    title: '不承保范围',
                    content: [
                      '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                    ],
                  },
                  claimProcess: {
                    title: '理赔流程',
                    subObject: [
                      {
                        title: '发生事故后报警并联系车行',
                        content: [
                          '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                        ],
                      },
                      {
                        title: '等待最终定审(45-60个工作日左右)',
                        content: [
                          '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                        ],
                      },
                    ],
                  },
                },
                {
                  productId: 18151660,
                  title: '车辆盗抢险',
                  description: '保障车辆被盗的损失',
                  code: 'TP',
                  type: 7,
                  name: '车辆盗抢险',
                  isInclude: true,
                  isFromCtrip: false,
                  insuranceDetail: [
                    {
                      packageId: 18151660,
                      excessShortDesc: '起赔额$100',
                      excessLongDesc: '',
                    },
                  ],
                  converageExplain: {
                    title: '承保范围',
                    content: [
                      '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                    ],
                  },
                  unConverageExplain: {
                    title: '不承保范围',
                    content: [
                      '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">钥匙遗落车内导致被盗；</li><li style="margin-bottom:-25px;">钥匙丢失，损坏或被盗；</li><li style="margin-bottom:-25px;">车门忘关导致被盗；</li><li style="margin-bottom:-25px;">车内额外设备如GPS，WIFI，儿童座椅等；</li><li style="margin-bottom:-25px;">在未经授权的区域行驶；</li><li style="margin-bottom:-25px;">将车辆长时间停在不当的地方导致车辆被盗；</li><li style="margin-bottom:-25px;">因车辆被盗被抢导致的车辆停运费或维修手续费；</li><li style="margin-bottom:-25px;">违反合同或当地法律的情况下导致的损失；</li></ul>*实际赔付范围与标准以门店合同为准',
                    ],
                  },
                  claimProcess: {
                    title: '理赔流程',
                    subObject: [
                      {
                        title: '发生事故后报警并联系车行',
                        content: [
                          '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                        ],
                      },
                      {
                        title: '等待最终定审(45-60个工作日左右)',
                        content: [
                          '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                        ],
                      },
                    ],
                  },
                },
                {
                  productId: 18151660,
                  title: '第三者责任险',
                  description: '保障第三方车辆或人员伤害损失',
                  code: 'TPL',
                  type: 7,
                  name: '第三者责任险',
                  isInclude: true,
                  isFromCtrip: false,
                  insuranceDetail: [
                    {
                      packageId: 18151660,
                      currencyCode: 'USD',
                      minExcess: 0,
                      maxExcess: 1000.0,
                      excessShortDesc: 'zh_cn：1,000USD',
                      excessLongDesc: '',
                      minCoverage: 30000.0,
                      maxCoverage: 30000.0,
                      coverageLongDesc: '',
                      coverageWithoutPlatformInsurance: '原起赔额$100可赔',
                      coverageShortDesc: '',
                    },
                  ],
                  converageExplain: {
                    title: '承保范围',
                    content: [
                      '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
                    ],
                  },
                  claimProcess: {
                    title: '理赔流程',
                    subObject: [
                      {
                        title: '报警并联系车行',
                        content: [
                          '发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况',
                        ],
                      },
                      {
                        title: '还车时向车行提交理赔',
                        content: [
                          '在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料',
                        ],
                      },
                      {
                        title: '等待最终定审(45-60个工作日左右)',
                        content: [
                          '车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                        ],
                      },
                    ],
                  },
                },
                {
                  code: 'TPL',
                  name: '安心补充险',
                  description: '保障玻璃轮胎底盘；补偿道路救援费用',
                  isInclude: false,
                  isFromCtrip: true,
                  giveUp: true,
                  insuranceStatus: 4,
                  groupCode: '1',
                  insuranceDetail: [
                    {
                      packageId: 18151660,
                      currencyCode: 'USD',
                      minExcess: 1000.0,
                      maxExcess: 1000.0,
                      excessShortDesc: '',
                      excessLongDesc: '',
                      minCoverage: 30000.0,
                      maxCoverage: 30000.0,
                      coverageShortDesc: '保额：30,000USD',
                      coverageLongDesc: '',
                      coverageWithoutPlatformInsurance:
                        '国内保险公司提供¥50/天',
                    },
                  ],
                  converageExplain: {
                    title: '承保范围',
                    content: [
                      '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
                    ],
                  },
                  unConverageExplain: {
                    title: '不承保范围',
                  },
                },
                {
                  code: 'TPL',
                  name: '安心补充险222',
                  description: '保障玻璃轮胎底盘；补偿道路救援费用',
                  isInclude: false,
                  isFromCtrip: true,
                  giveUp: false,
                  insuranceStatus: 4,
                  insuranceStatusDesc: '已取消',
                  groupCode: '1',
                  insuranceDetail: [
                    {
                      packageId: 18151660,
                      currencyCode: 'USD',
                      minExcess: 1000.0,
                      maxExcess: 1000.0,
                      excessShortDesc: '',
                      excessLongDesc: '',
                      minCoverage: 30000.0,
                      maxCoverage: 30000.0,
                      coverageShortDesc: '保额：30,000USD',
                      coverageLongDesc: '',
                      coverageWithoutPlatformInsurance:
                        '国内保险公司提供¥50/天',
                    },
                  ],
                  converageExplain: {
                    title: '承保范围',
                    content: [
                      '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
                    ],
                  },
                  unConverageExplain: {
                    title: '不承保范围',
                  },
                },
                {
                  code: 'CTRIP_CDW',
                  name: '驾乘意外险',
                  description: '全车人员意外伤害及财物损失',
                  isInclude: true,
                  isFromCtrip: true,
                  groupCode: '3',
                  insuranceStatus: 3,
                  insuranceStatusDesc: '已出保',
                  insuranceDetail: [
                    {
                      packageId: 18151660,
                      currencyCode: 'USD',
                      minExcess: 1000.0,
                      maxExcess: 1000.0,
                      excessShortDesc: '',
                      excessLongDesc: '',
                      minCoverage: 30000.0,
                      maxCoverage: 30000.0,
                      coverageShortDesc: '保额：30,000USD',
                      coverageLongDesc: '',
                      coverageWithoutPlatformInsurance:
                        '国内保险公司提供¥50/天',
                    },
                  ],
                  converageExplain: {
                    title: '承保范围',
                    content: [
                      '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                    ],
                  },
                  unConverageExplain: {
                    title: '不承保范围',
                    content: [
                      '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                    ],
                  },
                  itemUrl: 'www.保单详情.com',
                  coverDetailList: [
                    {
                      title: '最高保额',
                      subTitle: '保障范围',
                    },
                    {
                      title: '500000元',
                      descList: [
                        '碰撞险和盗抢险(提车时租车合同中约定的起赔额以下自付)',
                        '玻璃轮胎或底盘的损坏费用',
                        '由于意外事故产生的故障救援、拖车、人工费车辆所、钥匙替换费',
                      ],
                    },
                    {
                      title: '500000元',
                      descList: [
                        '碰撞险和盗抢险(提车时租车合同中约定的起赔额以下自付)',
                        '玻璃轮胎或底盘的损坏费用',
                        '由于意外事故产生的故障救援、拖车、人工费车辆所、钥匙替换费',
                      ],
                    },
                  ],
                },
              ],
              productInfoList: [
                {
                  packageItems: [
                    {
                      code: '',
                      name: '',
                      desc: '1名额外驾驶员',
                      sortNum: 0,
                    },
                    {
                      code: 'CDW',
                      name: '碰撞险',
                      desc: '车辆碰撞险',
                      sortNum: 0,
                    },
                    {
                      code: '',
                      name: '',
                      desc: '满油取还',
                      sortNum: 0,
                    },
                    {
                      code: '',
                      name: '',
                      desc: '基础租车费用',
                      sortNum: 0,
                    },
                    {
                      code: '',
                      name: '',
                      desc: '限里程',
                      sortNum: 0,
                    },
                    {
                      code: 'CDW',
                      name: '碰撞险',
                      desc: '车辆盗抢险',
                      sortNum: 0,
                    },
                    {
                      code: 'TPI',
                      name: '三者险',
                      desc: '第三者责任险',
                      sortNum: 0,
                    },
                    {
                      code: '',
                      name: '',
                      desc: '税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税',
                      sortNum: 0,
                    },
                  ],
                  minPackageItmes: [
                    {
                      title: '车辆碰撞险',
                      type: 1,
                      code: 'CDW',
                      description: '',
                    },
                    {
                      title: '车辆盗抢险',
                      type: 1,
                      code: 'TP',
                      description: '',
                    },
                    {
                      title: '第三者责任险',
                      type: 1,
                      code: 'TPL',
                      description: '',
                    },
                    {
                      title: '限里程',
                      type: 2,
                      code: 'LM',
                      description: '租期内有公里数限制',
                    },
                    {
                      title: '1名额外驾驶员',
                      type: 3,
                      code: 'ADD1',
                      description:
                        '一名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
                    },
                    {
                      title: '满油取还',
                      type: 4,
                      code: 'FRFB',
                      description:
                        '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
                    },
                    {
                      title: '基础租车费用',
                      type: 8,
                      code: 'Fees',
                      description: '仅包含车辆租金的基础费用',
                    },
                  ],
                },
              ],
              claimsProcess: [
                {
                  subTitle:
                    '发生事故(本车与第三方，或本车单独发生事故)时请按下方流程处理，否则无法获得赔偿。',
                  subObject: [
                    {
                      type: 1,
                      title: '立即联系门店',
                      content: [
                        '发生事故后，请立即联系门店获取处理建议。门店将向您提供必要的指导，可能包括但不限于拨打当地报警电话获取警方报告，记录事故现场照片等',
                      ],
                    },
                    {
                      type: 2,
                      title: '还车时向车行提交车行保障理赔',
                      content: [
                        '租期结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的场景有：从您信用卡预授权中扣除起赔额费用(若有)，待门店确认最终损失金额(一般需要45-60个工作日)。若最终损失金额大于起赔额，您只需支付起赔额；若小于起赔额，门店将原路退还差价。',
                      ],
                    },
                  ],
                },
                {
                  subTitle:
                    '超级补充全险理赔时，您需额外完成如下步骤。携程将全程协助您的理赔',
                  subObject: [
                    {
                      type: 1,
                      title: '车损重要理赔材料',
                      content: [
                        '车损照片、维修费用支付凭证、维修/车损清单、警方报告等。',
                      ],
                    },
                    {
                      type: 2,
                      title: '理赔材料审核',
                      content: [
                        '材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充。',
                      ],
                    },
                    {
                      type: 3,
                      title: '理赔款到账',
                      content: [
                        '理赔款会以银行转账方式支付到被保险人提供的境内银行账户。',
                      ],
                    },
                  ],
                  url: 'www.查看详细理赔材料的pdf链接.com',
                },
              ],
            },
          ],
          addPayments: [],
          faqListInfo: {},
          insuranceAndXProductDesc: [
            {
              type: 0,
              desc: [
                '取车时，店员可能会向您推销额外保险或收费项目，请根据实际需要选购。在签署合同时，请仔细核对是否有额外收费。如被强制要求购买，请您不要签字，当场联系携程客服协助解决。',
              ],
            },
            {
              type: 0,
              desc: [
                '您的套餐中已包含超级补充险及全车驾乘意外险，无须在门店重复购买。',
              ],
            },
          ],
          extendedInfo: {
            ctripInsuranceVersion: 'B',
          },
          authType: 0,
        },
        vendorInfo: {
          vendorName: '星河租车',
          vendorImageUrl:
            '//pages.trip.com/cars/image/totrip/2e945a83-f3e0-4e8e-b947-13e4f37374ba.png',
          confirmRightNow: true,
          confirmDate: 1681760505000,
          confirmDateStr: '2023-04-18 03:41',
          bizVendorCode: '14120',
          commentInfo: {
            vendorGoodType: 0,
            exposedScore: 5.0,
            topScore: 5.0,
            level: '超棒',
            commentCount: 91,
            hasComment: 1,
          },
          broker: false,
        },
        vehicleInfo: {
          vehicleName: '路虎 揽胜 运动版',
          special: false,
          passengerNum: 5,
          luggageNum: 4,
          hasAC: false,
          transmission: 'AT',
          vehicleGroupName: '豪华SUV',
          vendorVehicleCode: 'WFBV_86_LANDROVERRANGEROVERSPORT',
          imageUrl: '//ak-d.tripcdn.com/images/0AS131200096r0chk61F6.png',
          vehicleDisplacement: '',
          doorNum: 4,
        },
        packageInfos: [
          {
            insPackageId: 7,
            isDefault: true,
            packageName: '强化保障套餐',
            currencyCode: 'USD',
            defaultBomCode: '',
            defaultPackageId: 18151660,
            guaranteeDegree: 2.0,
            naked: false,
            lowestDailyPrice: 0,
            gapPrice: 0,
            stepPrice: 0,
          },
        ],
        productDetails: [
          {
            insPackageId: 7,
            insuranceItems: [
              {
                productId: 18151660,
                title: '车辆碰撞险',
                description: '保障车辆碰撞损失',
                code: 'CDW',
                type: 7,
                name: '车辆碰撞险',
                isInclude: true,
                isFromCtrip: false,
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    excessShortDesc: '起赔额$100',
                    excessLongDesc: '',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                  ],
                },
                unConverageExplain: {
                  title: '不承保范围',
                  content: [
                    '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                  ],
                },
                claimProcess: {
                  title: '理赔流程',
                  subObject: [
                    {
                      title: '发生事故后报警并联系车行',
                      content: [
                        '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                      ],
                    },
                    {
                      title: '等待最终定审(45-60个工作日左右)',
                      content: [
                        '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                      ],
                    },
                  ],
                },
              },
              {
                productId: 18151660,
                title: '车辆盗抢险',
                description: '保障车辆被盗的损失',
                code: 'TP',
                type: 7,
                name: '车辆盗抢险',
                isInclude: true,
                isFromCtrip: false,
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    excessShortDesc: '起赔额$100',
                    excessLongDesc: '',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                  ],
                },
                unConverageExplain: {
                  title: '不承保范围',
                  content: [
                    '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">钥匙遗落车内导致被盗；</li><li style="margin-bottom:-25px;">钥匙丢失，损坏或被盗；</li><li style="margin-bottom:-25px;">车门忘关导致被盗；</li><li style="margin-bottom:-25px;">车内额外设备如GPS，WIFI，儿童座椅等；</li><li style="margin-bottom:-25px;">在未经授权的区域行驶；</li><li style="margin-bottom:-25px;">将车辆长时间停在不当的地方导致车辆被盗；</li><li style="margin-bottom:-25px;">因车辆被盗被抢导致的车辆停运费或维修手续费；</li><li style="margin-bottom:-25px;">违反合同或当地法律的情况下导致的损失；</li></ul>*实际赔付范围与标准以门店合同为准',
                  ],
                },
                claimProcess: {
                  title: '理赔流程',
                  subObject: [
                    {
                      title: '发生事故后报警并联系车行',
                      content: [
                        '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                      ],
                    },
                    {
                      title: '等待最终定审(45-60个工作日左右)',
                      content: [
                        '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                      ],
                    },
                  ],
                },
              },
              {
                productId: 18151660,
                title: '第三者责任险',
                description: '保障第三方车辆或人员伤害损失',
                code: 'TPL',
                type: 7,
                name: '第三者责任险',
                isInclude: true,
                isFromCtrip: false,
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    currencyCode: 'USD',
                    minExcess: 0,
                    maxExcess: 1000.0,
                    excessShortDesc: 'zh_cn：1,000USD',
                    excessLongDesc: '',
                    minCoverage: 30000.0,
                    maxCoverage: 30000.0,
                    coverageLongDesc: '',
                    coverageWithoutPlatformInsurance: '原起赔额$100可赔',
                    coverageShortDesc: '',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
                  ],
                },
                claimProcess: {
                  title: '理赔流程',
                  subObject: [
                    {
                      title: '报警并联系车行',
                      content: [
                        '发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况',
                      ],
                    },
                    {
                      title: '还车时向车行提交理赔',
                      content: [
                        '在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料',
                      ],
                    },
                    {
                      title: '等待最终定审(45-60个工作日左右)',
                      content: [
                        '车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                      ],
                    },
                  ],
                },
              },
              {
                code: 'TPL',
                name: '安心补充险',
                description: '保障玻璃轮胎底盘；补偿道路救援费用',
                isInclude: false,
                isFromCtrip: true,
                giveUp: true,
                insuranceStatus: 4,
                groupCode: '1',
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    currencyCode: 'USD',
                    minExcess: 1000.0,
                    maxExcess: 1000.0,
                    excessShortDesc: '',
                    excessLongDesc: '',
                    minCoverage: 30000.0,
                    maxCoverage: 30000.0,
                    coverageShortDesc: '保额：30,000USD',
                    coverageLongDesc: '',
                    coverageWithoutPlatformInsurance: '国内保险公司提供¥50/天',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
                  ],
                },
                unConverageExplain: {
                  title: '不承保范围',
                },
              },
              {
                code: 'TPL',
                name: '安心补充险222',
                description: '保障玻璃轮胎底盘；补偿道路救援费用',
                isInclude: false,
                isFromCtrip: true,
                giveUp: false,
                insuranceStatus: 4,
                insuranceStatusDesc: '已取消',
                groupCode: '1',
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    currencyCode: 'USD',
                    minExcess: 1000.0,
                    maxExcess: 1000.0,
                    excessShortDesc: '',
                    excessLongDesc: '',
                    minCoverage: 30000.0,
                    maxCoverage: 30000.0,
                    coverageShortDesc: '保额：30,000USD',
                    coverageLongDesc: '',
                    coverageWithoutPlatformInsurance: '国内保险公司提供¥50/天',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准',
                  ],
                },
                unConverageExplain: {
                  title: '不承保范围',
                },
              },
              {
                code: 'CTRIP_CDW',
                name: '驾乘意外险',
                description: '全车人员意外伤害及财物损失',
                isInclude: true,
                isFromCtrip: true,
                groupCode: '3',
                insuranceStatus: 3,
                insuranceStatusDesc: '已出保',
                insuranceDetail: [
                  {
                    packageId: 18151660,
                    currencyCode: 'USD',
                    minExcess: 1000.0,
                    maxExcess: 1000.0,
                    excessShortDesc: '',
                    excessLongDesc: '',
                    minCoverage: 30000.0,
                    maxCoverage: 30000.0,
                    coverageShortDesc: '保额：30,000USD',
                    coverageLongDesc: '',
                    coverageWithoutPlatformInsurance: '国内保险公司提供¥50/天',
                  },
                ],
                converageExplain: {
                  title: '承保范围',
                  content: [
                    '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                  ],
                },
                unConverageExplain: {
                  title: '不承保范围',
                  content: [
                    '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                  ],
                },
                itemUrl: 'www.保单详情.com',
                coverDetailList: [
                  {
                    title: '最高保额',
                    subTitle: '保障范围',
                  },
                  {
                    title: '500000元',
                    descList: [
                      '碰撞险和盗抢险(提车时租车合同中约定的起赔额以下自付)',
                      '玻璃轮胎或底盘的损坏费用',
                      '由于意外事故产生的故障救援、拖车、人工费车辆所、钥匙替换费',
                    ],
                  },
                  {
                    title: '500000元',
                    descList: [
                      '碰撞险和盗抢险(提车时租车合同中约定的起赔额以下自付)',
                      '玻璃轮胎或底盘的损坏费用',
                      '由于意外事故产生的故障救援、拖车、人工费车辆所、钥匙替换费',
                    ],
                  },
                ],
              },
            ],
            productInfoList: [
              {
                packageItems: [
                  {
                    code: '',
                    name: '',
                    desc: '1名额外驾驶员',
                    sortNum: 0,
                  },
                  {
                    code: 'CDW',
                    name: '碰撞险',
                    desc: '车辆碰撞险',
                    sortNum: 0,
                  },
                  {
                    code: '',
                    name: '',
                    desc: '满油取还',
                    sortNum: 0,
                  },
                  {
                    code: '',
                    name: '',
                    desc: '基础租车费用',
                    sortNum: 0,
                  },
                  {
                    code: '',
                    name: '',
                    desc: '限里程',
                    sortNum: 0,
                  },
                  {
                    code: 'CDW',
                    name: '碰撞险',
                    desc: '车辆盗抢险',
                    sortNum: 0,
                  },
                  {
                    code: 'TPI',
                    name: '三者险',
                    desc: '第三者责任险',
                    sortNum: 0,
                  },
                  {
                    code: '',
                    name: '',
                    desc: '税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税',
                    sortNum: 0,
                  },
                ],
                minPackageItmes: [
                  {
                    title: '车辆碰撞险',
                    type: 1,
                    code: 'CDW',
                    description: '',
                  },
                  {
                    title: '车辆盗抢险',
                    type: 1,
                    code: 'TP',
                    description: '',
                  },
                  {
                    title: '第三者责任险',
                    type: 1,
                    code: 'TPL',
                    description: '',
                  },
                  {
                    title: '限里程',
                    type: 2,
                    code: 'LM',
                    description: '租期内有公里数限制',
                  },
                  {
                    title: '1名额外驾驶员',
                    type: 3,
                    code: 'ADD1',
                    description:
                      '一名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
                  },
                  {
                    title: '满油取还',
                    type: 4,
                    code: 'FRFB',
                    description:
                      '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
                  },
                  {
                    title: '基础租车费用',
                    type: 8,
                    code: 'Fees',
                    description: '仅包含车辆租金的基础费用',
                  },
                ],
              },
            ],
            claimsProcess: [
              {
                subTitle:
                  '发生事故(本车与第三方，或本车单独发生事故)时请按下方流程处理，否则无法获得赔偿。',
                subObject: [
                  {
                    type: 1,
                    title: '立即联系门店',
                    content: [
                      '发生事故后，请立即联系门店获取处理建议。门店将向您提供必要的指导，可能包括但不限于拨打当地报警电话获取警方报告，记录事故现场照片等',
                    ],
                  },
                  {
                    type: 2,
                    title: '还车时向车行提交车行保障理赔',
                    content: [
                      '租期结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的场景有：从您信用卡预授权中扣除起赔额费用(若有)，待门店确认最终损失金额(一般需要45-60个工作日)。若最终损失金额大于起赔额，您只需支付起赔额；若小于起赔额，门店将原路退还差价。',
                    ],
                  },
                ],
              },
              {
                subTitle:
                  '超级补充全险理赔时，您需额外完成如下步骤。携程将全程协助您的理赔',
                subObject: [
                  {
                    type: 1,
                    title: '车损重要理赔材料',
                    content: [
                      '车损照片、维修费用支付凭证、维修/车损清单、警方报告等。',
                    ],
                  },
                  {
                    type: 2,
                    title: '理赔材料审核',
                    content: [
                      '材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充。',
                    ],
                  },
                  {
                    type: 3,
                    title: '理赔款到账',
                    content: [
                      '理赔款会以银行转账方式支付到被保险人提供的境内银行账户。',
                    ],
                  },
                ],
                url: 'www.查看详细理赔材料的pdf链接.com',
              },
            ],
          },
        ],
        ctripInsuranceInfos: [],
      },
    }),
  }),
}));

const insuranceRes = require('../../../__mocks__/restful/18862/OSDQueryOrder/OSD/20230912.json');
const initialState = {
  OrderDetail: {
    response: insuranceRes,
    vendorInfo: insuranceRes.vendorInfo,
    vehicleInfo: insuranceRes.vehicleInfo,
    packageInfos: insuranceRes.packageInfos,
    productDetails: insuranceRes.productDetails,
    ctripInsuranceInfos: [],
  },
};
describe('mapIsuranceBox', () => {
  jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
  const mockStateMap = [
    {
      expected: {
        packageInfos: [
          {
            insPackageId: 7,
            isDefault: true,
            packageName: '优享套餐',
            currencyCode: 'NZD',
            defaultBomCode: '',
            defaultPackageId: 4417,
            guaranteeDegree: 0,
            naked: false,
            lowestDailyPrice: 0,
            gapPrice: 0,
            stepPrice: 0,
            allInsNameAndExcess: [
              {
                code: 'CDW',
                name: '车辆碰撞险',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                label: '',
                itemUrl: undefined,
                modalLabel: ['车行提供', undefined],
                isFromCtrip: false,
                description: '保障车辆碰撞损失',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                giveUp: undefined,
                groupCode: undefined,
                insuranceStatus: undefined,
                insuranceStatusName: undefined,
              },
              {
                code: 'TP',
                name: '车辆盗抢险',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                label: '',
                itemUrl: undefined,
                modalLabel: ['车行提供', undefined],
                isFromCtrip: false,
                description: '保障车辆被盗的损失',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                giveUp: undefined,
                groupCode: undefined,
                insuranceStatus: undefined,
                insuranceStatusName: undefined,
              },
              {
                code: 'TPL',
                name: '第三者责任险',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                label: '',
                itemUrl: undefined,
                modalLabel: ['车行提供', undefined],
                isFromCtrip: false,
                description: '保障第三方车辆或人员伤害损失',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                giveUp: undefined,
                groupCode: undefined,
                insuranceStatus: undefined,
                insuranceStatusName: undefined,
              },
              {
                code: 'TPL',
                name: '安心补充险',
                groupCode: '1',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                modalLabel: [false, undefined],
                isFromCtrip: true,
                giveUp: true,
                insuranceStatus: 4,
                description: '保障玻璃轮胎底盘；补偿道路救援费用',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                itemUrl: undefined,
                label: undefined,
                insuranceStatusName: undefined,
              },
              {
                code: 'TPL',
                name: '安心补充险222',
                groupCode: '1',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                modalLabel: [false, undefined],
                isFromCtrip: true,
                giveUp: false,
                insuranceStatus: 4,
                description: '保障玻璃轮胎底盘；补偿道路救援费用',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                insuranceStatusName: undefined,
                itemUrl: undefined,
                label: undefined,
              },
              {
                code: 'CTRIP_CDW',
                name: '驾乘意外险',
                groupCode: '3',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                modalLabel: [false, undefined],
                isFromCtrip: true,
                itemUrl: 'www.保单详情.com',
                insuranceStatus: 3,
                description: '全车人员意外伤害及财物损失',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                giveUp: undefined,
                insuranceStatusName: undefined,
                label: undefined,
              },
            ],
            groupInsNameAndExcess: {
              '1': [
                {
                  code: 'TPL',
                  name: '安心补充险',
                  groupCode: '1',
                  isHasExcess: false,
                  isInclude: true,
                  isZeroExcess: false,
                  modalLabel: [false, undefined],
                  isFromCtrip: true,
                  giveUp: true,
                  insuranceStatus: 4,
                  description: '保障玻璃轮胎底盘；补偿道路救援费用',
                  excessShortDesc: undefined,
                  excessShortDescNew: undefined,
                  insuranceStatusName: undefined,
                  itemUrl: undefined,
                  label: undefined,
                },
                {
                  code: 'TPL',
                  name: '安心补充险222',
                  groupCode: '1',
                  isHasExcess: false,
                  isInclude: true,
                  isZeroExcess: false,
                  modalLabel: [false, undefined],
                  isFromCtrip: true,
                  giveUp: false,
                  insuranceStatus: 4,
                  description: '保障玻璃轮胎底盘；补偿道路救援费用',
                  excessShortDesc: undefined,
                  excessShortDescNew: undefined,
                  insuranceStatusName: undefined,
                  itemUrl: undefined,
                  label: undefined,
                },
              ],
              '3': [
                {
                  code: 'CTRIP_CDW',
                  name: '驾乘意外险',
                  groupCode: '3',
                  isHasExcess: false,
                  isInclude: true,
                  isZeroExcess: false,
                  modalLabel: [false, undefined],
                  isFromCtrip: true,
                  itemUrl: 'www.保单详情.com',
                  insuranceStatus: 3,
                  description: '全车人员意外伤害及财物损失',
                  giveUp: undefined,
                  excessShortDesc: undefined,
                  excessShortDescNew: undefined,
                  insuranceStatusName: undefined,
                  label: undefined,
                },
              ],
              undefined: [
                {
                  code: 'CDW',
                  name: '车辆碰撞险',
                  isHasExcess: false,
                  isInclude: true,
                  isZeroExcess: false,
                  label: '',
                  modalLabel: ['车行提供', undefined],
                  isFromCtrip: false,
                  description: '保障车辆碰撞损失',
                  excessShortDesc: undefined,
                  excessShortDescNew: undefined,
                  insuranceStatusName: undefined,
                  itemUrl: undefined,
                  giveUp: undefined,
                  groupCode: undefined,
                  insuranceStatus: undefined,
                },
                {
                  code: 'TP',
                  name: '车辆盗抢险',
                  isHasExcess: false,
                  isInclude: true,
                  isZeroExcess: false,
                  label: '',
                  modalLabel: ['车行提供', undefined],
                  isFromCtrip: false,
                  description: '保障车辆被盗的损失',
                  excessShortDesc: undefined,
                  excessShortDescNew: undefined,
                  insuranceStatusName: undefined,
                  itemUrl: undefined,
                  giveUp: undefined,
                  groupCode: undefined,
                  insuranceStatus: undefined,
                },
                {
                  code: 'TPL',
                  name: '第三者责任险',
                  isHasExcess: false,
                  isInclude: true,
                  isZeroExcess: false,
                  label: '',
                  modalLabel: ['车行提供', undefined],
                  isFromCtrip: false,
                  description: '保障第三方车辆或人员伤害损失',
                  excessShortDesc: undefined,
                  excessShortDescNew: undefined,
                  insuranceStatusName: undefined,
                  itemUrl: undefined,
                  giveUp: undefined,
                  groupCode: undefined,
                  insuranceStatus: undefined,
                },
              ],
            },
            insNameAndExcess: [
              {
                code: 'CDW',
                name: '车辆碰撞险',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                label: '',
                modalLabel: ['车行提供', undefined],
                isFromCtrip: false,
                description: '保障车辆碰撞损失',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                insuranceStatusName: undefined,
                itemUrl: undefined,
                giveUp: undefined,
                groupCode: undefined,
                insuranceStatus: undefined,
              },
              {
                code: 'TP',
                name: '车辆盗抢险',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                label: '',
                modalLabel: ['车行提供', undefined],
                isFromCtrip: false,
                description: '保障车辆被盗的损失',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                insuranceStatusName: undefined,
                itemUrl: undefined,
                giveUp: undefined,
                groupCode: undefined,
                insuranceStatus: undefined,
              },
              {
                code: 'TPL',
                name: '第三者责任险',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                label: '',
                modalLabel: ['车行提供', undefined],
                isFromCtrip: false,
                description: '保障第三方车辆或人员伤害损失',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                insuranceStatusName: undefined,
                itemUrl: undefined,
                giveUp: undefined,
                groupCode: undefined,
                insuranceStatus: undefined,
              },
              {
                code: 'CTRIP_CDW',
                name: '驾乘意外险',
                groupCode: '3',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                modalLabel: [false, undefined],
                isFromCtrip: true,
                itemUrl: 'www.保单详情.com',
                insuranceStatus: 3,
                description: '全车人员意外伤害及财物损失',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                insuranceStatusName: undefined,
                giveUp: undefined,
              },
            ],
            unIncludeInsNameAndExcess: [
              {
                code: 'TPL',
                name: '安心补充险',
                groupCode: '1',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                modalLabel: [false, undefined],
                isFromCtrip: true,
                giveUp: true,
                insuranceStatus: 4,
                description: '保障玻璃轮胎底盘；补偿道路救援费用',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                insuranceStatusName: undefined,
                itemUrl: undefined,
                label: undefined,
              },
              {
                code: 'TPL',
                name: '安心补充险222',
                groupCode: '1',
                isHasExcess: false,
                isInclude: true,
                isZeroExcess: false,
                modalLabel: [false, undefined],
                isFromCtrip: true,
                giveUp: false,
                insuranceStatus: 4,
                description: '保障玻璃轮胎底盘；补偿道路救援费用',
                excessShortDesc: undefined,
                excessShortDescNew: undefined,
                insuranceStatusName: undefined,
                itemUrl: undefined,
                label: undefined,
              },
            ],
            logInfo: {
              guaranteePkgName: '优享套餐',
              insuranceId: ['CDW', 'TP', 'TPL', 'CTRIP_CDW'],
              vendorCode: '14000',
              groupName: '标准型SUV',
            },
          },
        ],
        insuranceAndXProductDesc: [
          {
            type: 1,
            desc: ['您已购买安心补充险及全车驾乘意外险，无需在门店重复购买：'],
          },
        ],
        platformInsuranceReminder: {
          title: '您已购安心补充险，无需重新购买',
          explain: [
            {
              title:
                '取车时，店员可能会向您推销额外保险或收费项目，请根据实际需要选购。在签署合同时，请仔细核对是否有额外收费。如被强制要求购买，请您不要签字，当场联系携程客服协助解决。',
            },
            {
              title:
                '由于车行系统无法查看国内保险公司提供的保险，如被店员推销类似保险，请明确无需购买，可出示保单或{tag}',
              link: {
                text: '英文说明',
                url: 'We would like to inform you that this customer has already purchased excess wavier and roadside service waiver through Ctrip. To prevent customers from accidentally purchasing duplicate coverage, we kindly request that you refrain from suggesting similar insurance packages at the counter. Failure to do so may result in a complaint filed with the appropriate authority.',
                linkType: 3,
                color: 'blue',
              },
            },
            {
              title:
                '如发生事故，请先垫付相关费用，还车后向保险公司申请理赔，{tag}',
              link: { text: '理赔流程', linkType: 4, color: 'blue' },
            },
          ],
        },
      },
    },
  ];
  test.each(mockStateMap)('测试mapIsuranceBox', ({ expected }) => {
    const data = mapIsuranceBox(initialState);
    expect(data).toEqual(expected);
  });
});

describe('mapIsuranceBoxOsd', () => {
  jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
  const mockStateMap = [
    {
      expected: {
        packageInfos: [
          {
            insPackageId: 7,
            isDefault: true,
            packageName: '优享套餐',
            currencyCode: 'NZD',
            defaultBomCode: '',
            defaultPackageId: 4417,
            guaranteeDegree: 0,
            naked: false,
            lowestDailyPrice: 0,
            gapPrice: 0,
            stepPrice: 0,
            allInsNameAndExcess: [
              {
                code: 'CDW',
                name: '车辆碰撞险',
                isInclude: true,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: ['车行提供：保障车辆碰撞损失'],
                isFromCtrip: false,
              },
              {
                code: 'TP',
                name: '车辆盗抢险',
                isInclude: true,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: ['车行提供：保障车辆被盗的损失'],
                isFromCtrip: false,
              },
              {
                code: 'TPL',
                name: '第三者责任险',
                isInclude: true,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: ['车行提供：保障第三方车辆或人员伤害损失'],
                isFromCtrip: false,
              },
              {
                code: 'TPL',
                name: '安心补充险',
                groupCode: '1',
                isInclude: false,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: [
                  '国内保险公司提供：保障玻璃轮胎底盘；补偿道路救援费用',
                ],
                isFromCtrip: true,
                insuranceStatus: 4,
              },
              {
                code: 'TPL',
                name: '安心补充险222',
                groupCode: '1',
                isInclude: false,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: [
                  '国内保险公司提供：保障玻璃轮胎底盘；补偿道路救援费用',
                ],
                isFromCtrip: true,
                insuranceStatus: 4,
                insuranceStatusDesc: '已取消',
              },
              {
                code: 'CTRIP_CDW',
                name: '驾乘意外险',
                groupCode: '3',
                isInclude: true,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: ['国内保险公司提供：全车人员意外伤害及财物损失'],
                isFromCtrip: true,
                itemUrl: 'www.保单详情.com',
                insuranceStatus: 3,
                insuranceStatusDesc: '已出保',
              },
            ],
            summaryInsNameAndExcess: [],
            groupInsNameAndExcess: {
              '1': [
                {
                  code: 'TPL',
                  name: '安心补充险',
                  groupCode: '1',
                  isInclude: false,
                  excessShortDescNew: '',
                  isZeroExcess: false,
                  label: '',
                  labelDescription: '',
                  modalLabel: [
                    '国内保险公司提供：保障玻璃轮胎底盘；补偿道路救援费用',
                  ],
                  isFromCtrip: true,
                  insuranceStatus: 4,
                },
                {
                  code: 'TPL',
                  name: '安心补充险222',
                  groupCode: '1',
                  isInclude: false,
                  excessShortDescNew: '',
                  isZeroExcess: false,
                  label: '',
                  labelDescription: '',
                  modalLabel: [
                    '国内保险公司提供：保障玻璃轮胎底盘；补偿道路救援费用',
                  ],
                  isFromCtrip: true,
                  insuranceStatus: 4,
                  insuranceStatusDesc: '已取消',
                },
              ],
              '3': [
                {
                  code: 'CTRIP_CDW',
                  name: '驾乘意外险',
                  groupCode: '3',
                  isInclude: true,
                  excessShortDescNew: '',
                  isZeroExcess: false,
                  label: '',
                  labelDescription: '',
                  modalLabel: ['国内保险公司提供：全车人员意外伤害及财物损失'],
                  isFromCtrip: true,
                  itemUrl: 'www.保单详情.com',
                  insuranceStatus: 3,
                  insuranceStatusDesc: '已出保',
                },
              ],
              undefined: [
                {
                  code: 'CDW',
                  name: '车辆碰撞险',
                  isInclude: true,
                  excessShortDescNew: '',
                  isZeroExcess: false,
                  label: '',
                  labelDescription: '',
                  modalLabel: ['车行提供：保障车辆碰撞损失'],
                  isFromCtrip: false,
                },
                {
                  code: 'TP',
                  name: '车辆盗抢险',
                  isInclude: true,
                  excessShortDescNew: '',
                  isZeroExcess: false,
                  label: '',
                  labelDescription: '',
                  modalLabel: ['车行提供：保障车辆被盗的损失'],
                  isFromCtrip: false,
                },
                {
                  code: 'TPL',
                  name: '第三者责任险',
                  isInclude: true,
                  excessShortDescNew: '',
                  isZeroExcess: false,
                  label: '',
                  labelDescription: '',
                  modalLabel: ['车行提供：保障第三方车辆或人员伤害损失'],
                  isFromCtrip: false,
                },
              ],
            },
            insNameAndExcess: [
              {
                code: 'CDW',
                name: '车辆碰撞险',
                isInclude: true,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: ['车行提供：保障车辆碰撞损失'],
                isFromCtrip: false,
              },
              {
                code: 'TP',
                name: '车辆盗抢险',
                isInclude: true,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: ['车行提供：保障车辆被盗的损失'],
                isFromCtrip: false,
              },
              {
                code: 'TPL',
                name: '第三者责任险',
                isInclude: true,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: ['车行提供：保障第三方车辆或人员伤害损失'],
                isFromCtrip: false,
              },
              {
                code: 'CTRIP_CDW',
                name: '驾乘意外险',
                groupCode: '3',
                isInclude: true,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: ['国内保险公司提供：全车人员意外伤害及财物损失'],
                isFromCtrip: true,
                itemUrl: 'www.保单详情.com',
                insuranceStatus: 3,
                insuranceStatusDesc: '已出保',
              },
            ],
            unIncludeInsNameAndExcess: [
              {
                code: 'TPL',
                name: '安心补充险',
                groupCode: '1',
                isInclude: false,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: [
                  '国内保险公司提供：保障玻璃轮胎底盘；补偿道路救援费用',
                ],
                isFromCtrip: true,
                insuranceStatus: 4,
              },
              {
                code: 'TPL',
                name: '安心补充险222',
                groupCode: '1',
                isInclude: false,
                excessShortDescNew: '',
                isZeroExcess: false,
                label: '',
                labelDescription: '',
                modalLabel: [
                  '国内保险公司提供：保障玻璃轮胎底盘；补偿道路救援费用',
                ],
                isFromCtrip: true,
                insuranceStatus: 4,
                insuranceStatusDesc: '已取消',
              },
            ],
            isHasExcess: 0,
            logInfo: {
              guaranteePkgName: '优享套餐',
              insuranceId: ['CDW', 'TP', 'TPL', 'CTRIP_CDW'],
              vendorCode: '14000',
              groupName: '标准型SUV',
            },
          },
        ],
        insuranceAndXProductDesc: [
          {
            type: 1,
            desc: ['您已购买安心补充险及全车驾乘意外险，无需在门店重复购买：'],
          },
        ],
        platformInsuranceReminder: {
          title: '您已购安心补充险，无需重新购买',
          explain: [
            {
              title:
                '取车时，店员可能会向您推销额外保险或收费项目，请根据实际需要选购。在签署合同时，请仔细核对是否有额外收费。如被强制要求购买，请您不要签字，当场联系携程客服协助解决。',
            },
            {
              title:
                '由于车行系统无法查看国内保险公司提供的保险，如被店员推销类似保险，请明确无需购买，可出示保单或{tag}',
              link: {
                text: '英文说明',
                url: 'We would like to inform you that this customer has already purchased excess wavier and roadside service waiver through Ctrip. To prevent customers from accidentally purchasing duplicate coverage, we kindly request that you refrain from suggesting similar insurance packages at the counter. Failure to do so may result in a complaint filed with the appropriate authority.',
                linkType: 3,
                color: 'blue',
              },
            },
            {
              title:
                '如发生事故，请先垫付相关费用，还车后向保险公司申请理赔，{tag}',
              link: { text: '理赔流程', linkType: 4, color: 'blue' },
            },
          ],
        },
      },
    },
  ];
  test.each(mockStateMap)('测试mapIsuranceBoxOsd', ({ expected }) => {
    const data = mapIsuranceBoxOsd(initialState);
    expect(data).toEqual(expected);
  });
});
