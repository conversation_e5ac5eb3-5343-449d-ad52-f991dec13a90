import Reducer, {
  getInitalState,
} from '../../../src/pages/xcar/State/OrderDetail/Reducer';
import * as Actions from '../../../src/pages/xcar/State/OrderDetail/Types';
describe('Reducer Test', () => {
  const initState = getInitalState();

  test('Reducer Init', () => {
    expect(Reducer(undefined, {})).toEqual(initState);
  });

  const OrderEnities = [
    {
      BizType: 'Translation',
      OrderID: 36510726975,
      OrderName: '香港驾照',
      OrderStatusCode: 'TRANSLATION_CANCLED',
      OrderStatusName: '已取消',
      Currency: 'RMB',
      OrderTotalPrice: 1000.0,
      BookingDate: '/Date(1687832153439+0800)/',
    },
  ];
  const OrderEnities2 = [
    {
      BizType: 'Translation',
      OrderID: 36510726910,
      OrderName: '澳门驾照',
      OrderStatusCode: 'TRANSLATION_CANCLED',
      OrderStatusName: '已取消',
      Currency: 'RMB',
      OrderTotalPrice: 200.0,
      BookingDate: '/Date(1687833333333+0800)/',
    },
  ];

  const actionMap = [
    {
      type: Actions.FETCH_ORDER_CALLBACK,
      data: {
        response: {
          orderId: '1111',
        },
        orderId: '1111',
        request: {},
        isLoading: false,
        isPolling: true,
        queryOrderApiStatus: 1,
      },
      expected: {
        ...initState,
        response: {
          orderId: '1111',
        },
        orderId: '1111',
        reqOrderParams: {},
        isLoading: false,
        isPolling: true,
        queryOrderApiStatus: 1,
        queryOrderAllDataSuccess: undefined,
      },
    },
    {
      type: Actions.FETCH_ORDER_CALLBACK,
      data: {
        response: {
          orderId: '1111',
        },
        orderId: '1111',
        request: {},
        isLoading: false,
        isPolling: true,
        queryOrderApiStatus: 1,
      },
      expected: {
        ...initState,
        response: {
          orderId: '1111',
        },
        orderId: '1111',
        reqOrderParams: {},
        isLoading: false,
        isPolling: true,
        queryOrderApiStatus: 1,
        queryOrderAllDataSuccess: undefined,
      },
    },
    {
      type: Actions.SET_ORDER_STATUS_SIGN,
      data: {
        orderStatusHashSign: 1,
      },
      expected: {
        ...initState,
        orderStatusHashSign: 1,
      },
    },
    {
      type: Actions.FETCH_TOCANCELBOOK,
      data: {
        reason: 'This is Reason',
      },
      expected: {
        ...initState,
        cancelReason: 'This is Reason',
      },
    },
    {
      type: Actions.GET_ORDER_DATA,
      data: {},
      expected: {
        ...initState,
      },
    },
    {
      type: Actions.FETCH_QUERYCANCELFEE,
      data: {},
      expected: {
        ...initState,
      },
    },
    {
      type: Actions.QUERY_SCOREANDSUGGESTIONS,
      data: {},
      expected: {
        ...initState,
      },
    },
    {
      type: Actions.FETCH_CREATECOMMENT,
      data: {},
      expected: {
        ...initState,
      },
    },
    {
      type: Actions.QUERY_CUSTOMERSERVICEUEL,
      data: {},
      expected: {
        ...initState,
      },
    },
    {
      type: Actions.FETCH_UPDATE_PAYMENT,
      data: {},
      expected: {
        ...initState,
      },
    },
    {
      type: Actions.FETCH_CREDIT_NOTIFICATION,
      data: {},
      expected: {
        ...initState,
      },
    },
    {
      type: Actions.QUERY_CAR_ASSISTANT_V2,
      data: {},
      expected: {
        ...initState,
      },
    },
    {
      type: Actions.SET_PRICEMODALVISIBLE,
      data: {
        visible: true,
      },
      expected: {
        ...initState,
        priceModalVisible: true,
      },
    },
    {
      type: Actions.SET_MODIFYFLIGHTNOMODALVISIBLE,
      data: {
        visible: true,
      },
      expected: {
        ...initState,
        modifyFlightNoModalVisible: true,
      },
    },
    {
      type: Actions.SET_FEEDEDUCTIONVISIBLE,
      data: {
        visible: true,
        feeDeductionData: {},
      },
      expected: {
        ...initState,
        feeDeductionVisible: true,
        feeDeductionData: {},
      },
    },
    {
      type: Actions.SET_CHANGEORDERMODALVISIBLE,
      data: {
        visible: true,
      },
      expected: {
        ...initState,
        orderChangeModalVisible: true,
      },
    },
    {
      type: Actions.SET_ISDCHANGEORDERMODALVISIBLE,
      data: {
        visible: true,
      },
      expected: {
        ...initState,
        orderIsdChangeModalVisible: true,
      },
    },
    {
      type: Actions.SUBMIT_SCOREANDSUGGESTIONS,
      data: {
        test: true,
      },
      expected: {
        ...initState,
        npsResponseParam: {
          test: true,
        },
      },
    },
    {
      type: Actions.SUBMIT_SCOREANDSUGGESTIONSCALLBACK,
      data: {
        res: {},
      },
      expected: {
        ...initState,
        npsResponse: {},
      },
    },
    {
      type: Actions.QUERY_SCOREANDSUGGESTIONSCALLBACK,
      data: {
        res: {
          resultCode: 200,
        },
      },
      expected: {
        ...initState,
        hasNps: 200,
      },
    },
    {
      type: Actions.SHOW_REVIEWSUCCESSMODAL,
      data: {
        visible: true,
      },
      expected: {
        ...initState,
        reviewSuccessModalVisible: true,
      },
    },
    {
      type: Actions.SHOW_REVIEWMODAL,
      data: {
        visible: true,
      },
      expected: {
        ...initState,
        reviewModalVisible: true,
      },
    },
    {
      type: Actions.FETCH_MODIFYORDERCALLBACK,
      data: {
        test: 'data',
      },
      expected: {
        ...initState,
        reqModifyOrderParam: {
          test: 'data',
        },
      },
    },
    {
      type: Actions.FETCH_QUERYCANCELFEE_CALLBACK,
      data: {
        resCancelFee: 'data',
      },
      expected: {
        ...initState,
        resCancelFee: 'data',
      },
    },
    {
      type: Actions.SETMODALSVISIBLE,
      data: {
        refundDetailModal: {
          visible: false,
        },
      },
      expected: {
        ...initState,
        modalsVisible: {
          ...initState.modalsVisible,
          refundDetailModal: {
            visible: false,
          },
        },
      },
    },
    {
      type: Actions.SET_SELECTOR_DATA,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        test: 'test',
      },
    },
    {
      type: Actions.SET_INSDETAILMODALVISIBLE,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        BbkInsuranceDetailProps: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.SET_CLMODALVISIBLE,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        labelsModalVisible: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.SET_CDMODALVISIBLE,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        carDetailModalVisible: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.SET_EASYLIFETAGMODALVISIBLE,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        easyLifeTagModalVisible: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.FETCH_EASYLIFETAG_CALLBACK,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        easyLifeTags: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.QUERY_PMSINFOCALLBACK,
      data: {
        pmsInfo: 'test',
      },
      expected: {
        ...initState,
        pmsInfo: 'test',
      },
    },
    {
      type: Actions.GET_LIMIT_CONTENT_SUCCESS,
      data: {
        limitCont: 'test',
      },
      expected: {
        ...initState,
        limitCont: 'test',
      },
    },
    {
      type: Actions.GET_CREATE_PAYMENT_SUCCESS,
      data: {
        response: 'test',
      },
      expected: {
        ...initState,
        createPaymentResponse: 'test',
      },
    },
    {
      type: Actions.SET_HISTORY_MODAL_VISIBLE,
      data: {
        visible: false,
      },
      expected: {
        ...initState,
        replenishHistoryVisible: false,
      },
    },
    {
      type: Actions.SET_DEPOSIT_DEATIL_MODAL_VISIBLE,
      data: {
        visible: false,
      },
      expected: {
        ...initState,
        depositDetailModalVisible: false,
      },
    },
    {
      type: Actions.SET_PHONE_MODAL_VISIBLE,
      data: {
        phoneModalType: undefined,
        visible: false,
      },
      expected: {
        ...initState,
        phoneModalVisible: false,
      },
    },
    {
      type: Actions.SET_PHONE_MODAL_VISIBLE,
      data: {
        phoneModalType: 1,
        visible: false,
      },
      expected: {
        ...initState,
        phoneModalVisible: false,
        phoneModalType: 1,
      },
    },
    {
      type: Actions.SET_PERSON_PHONE_MODAL_VISIBLE,
      data: {
        phoneModalType: 1,
        visible: false,
      },
      expected: {
        ...initState,
        personPhoneModalVisible: false,
        phoneModalType: 1,
      },
    },
    {
      type: Actions.QUERY_SIMILAR_VEHICLE,
      data: {
        similarVehicleInfo: {},
      },
      expected: {
        ...initState,
        similarVehicleInfo: {},
      },
    },
    {
      type: Actions.QUERY_ORDER_PRICE_SUCCESS,
      data: {
        orderDetailPrice: {},
      },
      expected: {
        ...initState,
        orderDetailPrice: {},
      },
    },
    {
      type: Actions.QUERY_CUSTOMERSERVICEUEL_SUCCESS,
      data: {
        customerServiceUrl: 'customerServiceUrl',
      },
      expected: {
        ...initState,
        customerServiceUrl: 'customerServiceUrl',
      },
    },
    {
      type: Actions.SET_FETCH_DONE,
      data: {
        fetchDone: true,
      },
      expected: {
        ...initState,
        fetchDone: true,
      },
    },
    {
      type: Actions.RERENT_RETRY,
      expected: {
        ...initState,
        renewalOrders: [{}],
      },
    },
    {
      type: Actions.RESET,
      expected: initState,
    },
    {
      type: Actions.GET_SUPPLEMENT_LIST_CALLBACK,
      data: {
        data: [''],
      },
      expected: {
        ...initState,
        data: [''],
      },
    },
    {
      type: Actions.SET_VEHICLE_DAMAGE_ID,
      data: 1,
      expected: {
        ...initState,
        vehicleDamageId: 1,
      },
    },
    {
      type: Actions.SET_SUPPLEMENT_LIST_NEW,
      data: {
        visible: false,
      },
      expected: {
        ...initState,
        isShowSupplementRedIcon: false,
      },
    },
    {
      type: Actions.PAYCOUNTDOWNTIMEOUT,
      data: {
        timeOut: false,
      },
      expected: {
        ...initState,
        payCountDownTimeOut: false,
      },
    },
    {
      type: Actions.SET_TIPPOP_DATA,
      data: {
        data: {
          tip: 'test',
          style: {
            fontSize: 20,
          },
        },
        visible: true,
      },
      expected: {
        ...initState,
        tipPopData: {
          visible: true,
          data: {
            tip: 'test',
            style: {
              fontSize: 20,
            },
          },
        },
      },
    },
    {
      type: Actions.SET_TIPPOP_DATA,
      data: {
        data: {
          tip: 'test',
          style: {
            fontSize: 20,
          },
        },
        visible: undefined,
      },
      expected: {
        ...initState,
        tipPopData: {
          visible: false,
          data: {
            tip: 'test',
            style: {
              fontSize: 20,
            },
          },
        },
      },
    },
    {
      type: Actions.QUERY_CAR_ASSISTANT_V2_CALLBACK,
      data: {
        test: false,
      },
      expected: {
        ...initState,
        queryCarAssistantV2Response: {
          test: false,
        },
      },
    },
    {
      type: Actions.SET_STORAGE_CARDS_TITLE,
      data: {
        test: false,
      },
      expected: {
        ...initState,
        storageCardsTitle: {
          test: false,
        },
      },
    },
    {
      type: Actions.SET_PRICE_DETAIL_MODAL_VISIBLE,
      data: {
        test: false,
      },
      expected: {
        ...initState,
        priceDetailModalVisible: {
          test: false,
        },
      },
    },
    {
      type: Actions.QUERY_ORDERINSUANDXPRODUCT_CALLBACK,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        newOrderInsAndXRes: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.SET_SCANNED_IMAGES,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        scannedImages: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.QUERY_ORDERCANCELINFO_CALLBACK,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        orderCancelInfo: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.QUERYORDERCOUPON_CALLBACK,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        couponLists: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.FETCH_CASHBACK_CALLBACK,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        cashBackInfo: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.SET_CONTINUE_PAY_FAIL_DIALOG_INFO,
      data: {
        test: 'test',
      },
      expected: {
        ...initState,
        continuePayFailDialogInfo: {
          test: 'test',
        },
      },
    },
    {
      type: Actions.LIMITRULE_POP_VISIBLE,
      data: {
        visible: false,
      },
      expected: {
        ...initState,
        limitPopVisible: false,
      },
    },
    {
      type: Actions.CREATE_ORDER_PAYMENT_PARAMS,
      data: {
        params: {},
      },
      expected: {
        ...initState,
        paymentParam: {},
      },
    },
    {
      type: Actions.SET_REFUNDMODALVISIBLE,
      data: {
        visible: false,
      },
      expected: {
        ...initState,
        refundModalVisible: false,
      },
    },
    {
      type: '',
      data: {
        test: 'test',
      },
      expected: initState,
    },
    {
      type: Actions.QUERY_DRIVER_LICENSE_ORDERS,
      data: {
        PageIndex: 1,
        nextPageOffset: '1234',
        isLastPage: false,
      },
      expected: {
        ...initState,
        driverLicenseOrdersEnities: [],
        driverLicensePageNo: 1,
      },
    },
    {
      type: Actions.QUERY_DRIVER_LICENSE_ORDERS,
      data: {
        PageIndex: 2,
        nextPageOffset: '1234',
        isLastPage: true,
      },
      state: {
        driverLicenseOrdersEnities: OrderEnities,
        driverLicensePageNo: 2,
      },
      expected: {
        driverLicenseOrdersEnities: OrderEnities,
        driverLicensePageNo: 2,
      },
    },
    {
      type: Actions.QUERY_DRIVER_LICENSE_ORDERS_CALLBACK,
      data: {
        PageIndex: 1,
        orderEnities: OrderEnities2,
        nextPageOffset: '1234',
        isLastPage: false,
      },
      state: {
        driverLicenseOrdersEnities: OrderEnities,
        driverLicensePageNo: 2,
      },
      expected: {
        driverLicenseOrdersEnities: [...OrderEnities, ...OrderEnities2],
        driverLicensePageNo: 3,
        nextPageOffset: '1234',
        isLastPage: false,
      },
    },
    {
      type: Actions.QUERY_DRIVER_LICENSE_ORDERS_CALLBACK,
      data: {
        PageIndex: 1,
        orderEnities: [],
        nextPageOffset: '1234',
        isLastPage: true,
      },
      state: {
        driverLicenseOrdersEnities: OrderEnities,
        driverLicensePageNo: 2,
      },
      expected: {
        driverLicenseOrdersEnities: OrderEnities,
        driverLicensePageNo: 2,
        nextPageOffset: '1234',
        isLastPage: true,
      },
    },
    {
      type: Actions.QUERY_DRIVER_LICENSE_ORDERS_CALLBACK,
      data: {
        PageIndex: 1,
        orderEnities: OrderEnities2,
        nextPageOffset: '1234',
        isLastPage: true,
      },
      state: {
        driverLicenseOrdersEnities: OrderEnities,
        driverLicensePageNo: 1,
      },
      expected: {
        driverLicenseOrdersEnities: OrderEnities2,
        driverLicensePageNo: 2,
        nextPageOffset: '1234',
        isLastPage: true,
      },
    },
    {
      type: Actions.QUERY_DRIVER_LICENSE_ORDERS_CALLBACK,
      data: null,
      state: {
        driverLicenseOrdersEnities: OrderEnities,
        driverLicensePageNo: 1,
      },
      expected: {
        driverLicenseOrdersEnities: undefined,
        driverLicensePageNo: 1,
      },
    },
    {
      type: Actions.TO_CANCEL_CALLBACK,
      data: {
        isPenaltyChange: true,
        penaltyChangeTip: {},
        penaltyChangeCancelTip:{}
      },
      expected: {
        ...initState,
        isPenaltyChange: true,
        penaltyChangeTip: {},
        penaltyChangeCancelTip:{}
      }
    },
    {
      type: Actions.FETCH_QUERY_CANCEL_INFO,
      data: {},
      expected: {
        ...initState,
        isFetchCancelInfoLoading: true,
         isPenaltyChange: false,
         penaltyChangeTip: null,
         penaltyChangeCancelTip: null,
      }
    }
  ];

  test.each(actionMap)('%p', ({ type, data, state, expected }) => {
    const curState: any = state || initState;
    expect(Reducer(curState, { type, data })).toEqual(expected);
  });
});
