export const getFeeDetailDataMockMap = [
  {
    state: {
      OrderDetail: {
        orderDetailPrice: {
          cashBackInfoV2: {
            title: '还车后可获',
            subTitle: '平台补贴返现',
            description: '还车完成后X个工作日内自动返至「我的钱包」',
            code: 'Summary',
            type: 1,
            currencyCode: 'CNY',
            currentTotalPrice: 261,
            payMode: 2,
            items: [],
            notices: ['还车后返现¥20'],
            labels: [
              {
                code: '1',
                title: '还车后可获',
                subTitle: '平台补贴返现',
              },
            ],
          },
          chargesInfos: [
            {
              title: '车辆租金',
              code: 'CAR_RENTAL_FEE',
              currencyCode: 'CNY',
              currentDailyPrice: 70,
              currentTotalPrice: 693,
              items: [
                {
                  title: '租车费',
                  subTitle: '',
                  code: '1001',
                  size: '¥99 x9天 + ¥99',
                  count: 10,
                  unit: '',
                  currencyCode: 'CNY',
                  currentDailyPrice: 99,
                  currentTotalPrice: 990,
                  priceDailys: [
                    {
                      date: '8月11日 周四',
                      priceStr: '¥99',
                      showType: 0,
                    },
                    {
                      date: '8月12日 周五',
                      priceStr: '¥99',
                      showType: 0,
                    },
                    {
                      date: '8月13日 周六',
                      priceStr: '¥99',
                      showType: 0,
                    },
                    {
                      date: '8月14日 周日',
                      priceStr: '¥99',
                      showType: 0,
                    },
                  ],
                },
                {
                  title: '夏日特惠',
                  code: 'PMS_SAT18140545821',
                  type: 1,
                  currencyCode: 'CNY',
                  currentTotalPrice: 297,
                  payMode: 1,
                },
              ],
              originDailyPrice: 99,
            },
            {
              title: '车行服务费',
              code: 'CAR_GUARANTEE_FEE',
              currencyCode: 'CNY',
              currentTotalPrice: 635,
              items: [
                {
                  title: '优享服务',
                  description:
                    '含车辆整备，车损险赔付全部损失，含玻璃、不含车轮，三者险保额100万，折旧费和停运费正常收取',
                  code: '2001',
                  count: 5,
                  unit: '天',
                  currencyCode: 'CNY',
                  currentDailyPrice: 127,
                  currentTotalPrice: 635,
                },
              ],
            },
          ],
          equipmentInfos: [],
          discountList: [],
          depositInfo: {
            items: [],
          },
          rentalTerm: 5,
        },
      },
    },
    expected: [],
  },
  {
    state: {
      OrderDetail: {
        orderDetailPrice: {
          chargesInfos: [
            {
              title: '车辆取还费',
              code: 'CAR_SERVICE_FEE',
              currencyCode: 'CNY',
              currentTotalPrice: 280,
              items: [
                {
                  title: '送车上门服务费',
                  description: '',
                  code: '11026',
                  count: 1,
                  unit: '',
                  currencyCode: 'CNY',
                  currentDailyPrice: 140,
                  currentTotalPrice: 140,
                },
                {
                  title: '上门取车服务费',
                  description: '',
                  code: '11027',
                  count: 1,
                  unit: '',
                  currencyCode: 'CNY',
                  currentDailyPrice: 140,
                  currentTotalPrice: 140,
                },
              ],
            },
            {
              title: '车行服务费',
              code: 'CAR_GUARANTEE_FEE',
              currencyCode: 'CNY',
              currentTotalPrice: 635,
              items: [
                {
                  title: '优享服务',
                  description:
                    '含车辆整备，车损险赔付全部损失，含玻璃、不含车轮，三者险保额100万，折旧费和停运费正常收取',
                  code: '2001',
                  count: 5,
                  unit: '天',
                  currencyCode: 'CNY',
                  currentDailyPrice: 127,
                  currentTotalPrice: 635,
                },
              ],
            },
          ],
          equipmentInfos: [],
          discountList: [],
          couponInfos: [
            {
              title: '优惠券',
              subTitle: '国内租车满减券',
              description:
                '本优惠券通过携程租车频道、接送机·包车频道，以预付方式（在线支付）预订国内租车可享租车费立减优惠（满100-12/满200-30/满400-60/满800-100/满2000-218/满5000-588）；优惠券自领取日起7天内使用有效，过期则自动失效；优惠仅限租车费、用车费，不含手续费、基础服务费、增值服务费、异地还车费等费用；领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；对于恶意下单、利用程序漏洞等行为，我司有权取消优惠资格；使用优惠券的订单取消、超时未支付或购买失败，若该券尚未失效将退回原账户，若该券已失效不予退回。',
              code: 'fhywfhdxsk',
              type: 2,
              currencyCode: 'CNY',
              currentTotalPrice: 100,
              payMode: 2,
            },
          ],
          depositInfo: {
            items: [],
          },
          chargesSummary: {
            title: '订单总额',
            code: 'Summary',
            type: 103,
            currencyCode: 'CNY',
            currentTotalPrice: 2095,
            payMode: 2,
            items: [
              {
                title: '在线支付',
                code: '1',
                currencyCode: 'CNY',
              },
              {
                title: '招商银行¥2095',
                code: '6',
                currencyCode: 'CNY',
              },
            ],
            notices: [],
          },
          rentalTerm: 5,
        },
      },
    },
    expected: [
      {
        currency: 'CNY',
        desc: '国内租车满减券',
        items: undefined,
        price: 100,
        title: '优惠券',
      },
    ],
  },
  {
    state: {
      OrderDetail: {
        orderDetailPrice: null,
      },
    },
    expected: [],
  },
];

export const getIsShowMessageAssistantBtnMockMap = [
  {
    state: {
      OrderDetail: {
        refundPenaltyInfo: {
          status: 1,
          title: '已提交¥30违约金退还申请',
          lastRefundTime: '2021-08-31 17:23:18',
          refundAmount: 30,
          penaltyAmount: 40,
        },
        queryCarAssistantV2Response: {
          summary: [
            {
              content: '取车必备驾驶员本人身份证+中国大陆驾照',
              style: 'background:pink',
            },
            {
              content: '了解取还车流程',
            },
          ],
          attrDto: {
            sort: 5,
            history: 'true',
          },
          isAddWarningTitle: true,
        },
        storageCardsTitle: '测试',
        orderBaseInfo: {
          allOperations: [
            {
              operationId: 1,
              buttonName: '继续支付',
              enable: false,
              display: null,
            },
            {
              operationId: 3,
              buttonName: '去点评',
              enable: false,
              display: null,
            },
            {
              operationId: 4,
              buttonName: '再次预订',
              enable: true,
              display: null,
            },
            {
              operationId: 2,
              buttonName: '取消订单',
              enable: true,
              display: 'none',
            },
            {
              operationId: 7,
              buttonName: '修改订单',
              enable: true,
              display: null,
            },
          ],
        },
      },
      Service: {
        serviceProgressList: [],
        serviceStatus: '0',
        serviceTitle: '',
        serviceDesc: '',
        serviceLatestTime: '',
        urgeServiceIds: [],
        serviceIds: '',
        serviceCardHistory: false,
      },
      OnlineAuth: {
        retryingCardFace: {
          '1': false,
          '2': false,
          '3': false,
          '4': false,
        },
        orderAuthInfo: {
          supportInfo: {
            authStatus: 0,
            certificationStatus: 200,
            defaultAuthStatus: false,
            isShow: true,
            showTitle: '身份证及驾照在线认证',
            buttonText: '去授权',
            guideText:
              '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
          },
          certificateV3List: [
            {
              isActive: true,
              isShow: true,
            },
          ],
        },
      },
      Common: {
        orderWaringInfo: {},
      },
    },
    expected: false,
  },
  {
    state: {
      OrderDetail: {
        refundPenaltyInfo: {
          status: 1,
          title: '已提交¥30违约金退还申请',
          lastRefundTime: '2021-08-31 17:23:18',
          refundAmount: 30,
          penaltyAmount: 40,
        },
        queryCarAssistantV2Response: null,
        storageCardsTitle: '测试',
        orderBaseInfo: {
          allOperations: [
            {
              operationId: 1,
              buttonName: '继续支付',
              enable: false,
              display: null,
            },
            {
              operationId: 3,
              buttonName: '去点评',
              enable: false,
              display: null,
            },
            {
              operationId: 4,
              buttonName: '再次预订',
              enable: true,
              display: null,
            },
            {
              operationId: 2,
              buttonName: '取消订单',
              enable: true,
              display: 'none',
            },
            {
              operationId: 7,
              buttonName: '修改订单',
              enable: true,
              display: null,
            },
          ],
        },
      },
      Service: {
        serviceProgressList: [],
        serviceStatus: '0',
        serviceTitle: '',
        serviceDesc: '',
        serviceLatestTime: '',
        urgeServiceIds: [],
        serviceIds: '',
        serviceCardHistory: false,
      },
      OnlineAuth: {
        retryingCardFace: {
          '1': false,
          '2': false,
          '3': false,
          '4': false,
        },
        orderAuthInfo: {
          supportInfo: {
            authStatus: 0,
            certificationStatus: 200,
            defaultAuthStatus: false,
            isShow: false,
            showTitle: '身份证及驾照在线认证',
            buttonText: '去授权',
            guideText:
              '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
          },
          certificateV3List: [
            {
              isActive: true,
              isShow: true,
            },
          ],
        },
      },
      Common: {
        orderWaringInfo: {},
      },
    },
    expected: false,
  },
];

export const getDiffInsuranceMockMap = [
  {
    state: {
      OrderDetail: {
        insuranceAndXProductDesc: [],
        insuranceAndXProduct: [
          {
            name: '人身及财物险',
            code: '2000896',
            title: '人身及财物险',
            specificName: 0,
            sourceFrom: 2,
            productId: 2000896,
            insuranceOrderId: '1111111',
            requestId: '3082488749-2000896',
            orderTitle: '保险加购',
            price: 156,
            localCurrencyCode: 'CNY',
            localTotalPrice: 156,
            localDailyPrice: 156,
            currentTotalPrice: 156,
            currentDailyPrice: 156,
            currentCurrencyCode: 'CNY',
            quantity: 1,
            quantityName: '',
            maxQuantity: 1,
            group: 1,
            status: 3,
            canUpgrade: false,
            extDesc: '',
            toDetailStatus: 0,
          },
          {
            name: '优享服务',
            code: '2001',
            title: '优享服务',
            specificName: 0,
            sourceFrom: 1,
            price: 100,
            localCurrencyCode: 'CNY',
            localTotalPrice: 100,
            localDailyPrice: 100,
            currentTotalPrice: 100,
            currentDailyPrice: 100,
            currentCurrencyCode: 'CNY',
            quantity: 1,
            quantityName: '',
            maxQuantity: 1,
            group: 1,
            status: 0,
            canUpgrade: true,
            extDesc: '',
            toDetailStatus: 0,
          },
          {
            name: '优享服务',
            code: '2001',
            title: '优享服务',
            specificName: 0,
            sourceFrom: 1,
            price: 100,
            localCurrencyCode: 'CNY',
            localTotalPrice: 100,
            localDailyPrice: 100,
            currentTotalPrice: 100,
            currentDailyPrice: 100,
            currentCurrencyCode: 'CNY',
            quantity: 1,
            quantityName: '',
            maxQuantity: 1,
            group: 2,
            status: 0,
            canUpgrade: true,
            extDesc: '',
            toDetailStatus: 0,
          },
        ],
      },
    },
    expected: {
      insuranceAndXProductDesc: [],
      plusInsurance: [],
      purchasedInsurance: [
        {
          name: '人身及财物险',
          code: '2000896',
          title: '人身及财物险',
          specificName: 0,
          sourceFrom: 2,
          productId: 2000896,
          insuranceOrderId: '1111111',
          requestId: '3082488749-2000896',
          orderTitle: '保险加购',
          price: 156,
          localCurrencyCode: 'CNY',
          localTotalPrice: 156,
          localDailyPrice: 156,
          currentTotalPrice: 156,
          currentDailyPrice: 156,
          currentCurrencyCode: 'CNY',
          quantity: 1,
          quantityName: '',
          maxQuantity: 1,
          group: 1,
          status: 3,
          canUpgrade: false,
          extDesc: '',
          toDetailStatus: 0,
        },
      ],
      upgradeInsurance: [
        {
          name: '优享服务',
          code: '2001',
          title: '优享服务',
          specificName: 0,
          sourceFrom: 1,
          price: 100,
          localCurrencyCode: 'CNY',
          localTotalPrice: 100,
          localDailyPrice: 100,
          currentTotalPrice: 100,
          currentDailyPrice: 100,
          currentCurrencyCode: 'CNY',
          quantity: 1,
          quantityName: '',
          maxQuantity: 1,
          group: 1,
          status: 0,
          canUpgrade: true,
          extDesc: '',
          toDetailStatus: 0,
        },
      ],
    },
  },
  {
    state: {
      OrderDetail: {
        insuranceAndXProductDesc: [],
        insuranceAndXProduct: [],
      },
    },
    expected: {
      insuranceAndXProductDesc: [],
      plusInsurance: [],
      purchasedInsurance: [],
      upgradeInsurance: [],
    },
  },
];

export const queryContinuePayParamsMockMap = [
  {
    insData: {
      status: 0,
      token: 'xxxxxxxx',
      data: {
        selectedInsuranceList: [
          {
            name: '人身及财物险',
            insuranceId: 2000896,
            productId: 2000896,
            code: 2000896,
            price: '56',
            insuranceOrderId: 'xxxxx',
          },
        ],
      },
    },
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        driverInfo: {
          name: '陈XX',
        },
        freeDeposit: {
          preAmountForCar: 3000,
        },
        vendorInfo: {
          vendorID: '13014',
          vendorName: '大方租车',
          vendorLogo: '',
        },
        ctripInsuranceInfos: null,
        insuranceAndXProductDesc: [],
        insuranceAndXProduct: [
          {
            name: '人身及财物险',
            code: '2000896',
            title: '人身及财物险',
            specificName: 0,
            sourceFrom: 2,
            productId: 2000896,
            insuranceOrderId: '1111111',
            requestId: '3082488749-2000896',
            orderTitle: '保险加购',
            price: 156,
            localCurrencyCode: 'CNY',
            localTotalPrice: 156,
            localDailyPrice: 156,
            currentTotalPrice: 156,
            currentDailyPrice: 156,
            currentCurrencyCode: 'CNY',
            quantity: 1,
            quantityName: '',
            maxQuantity: 1,
            group: 1,
            status: 3,
            canUpgrade: false,
            extDesc: '',
            toDetailStatus: 0,
          },
        ],
      },
    },
    expected: {
      additionalServicesByRemove: [],
      orderid: '11111',
      type: 2,
      preAmountForCar: 3000,
      vendorId: '13014',
      preToken: 'xxxxxxxx',
      ctripOrderIds: [1111111],
    },
  },
  {
    insData: {},
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        driverInfo: {
          name: '陈XX',
        },
        freeDeposit: {
          preAmountForCar: 3000,
        },
        vendorInfo: {
          vendorID: '13014',
          vendorName: '大方租车',
          vendorLogo: '',
        },
        ctripInsuranceInfos: null,
      },
    },
    expected: {
      orderid: '11111',
      type: 2,
      preAmountForCar: 3000,
      vendorId: '13014',
    },
  },
  {
    insData: null,
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        vendorInfo: {
          vendorID: '13014',
          vendorName: '大方租车',
          vendorLogo: '',
        },
        freeDeposit: {
          preAmountForCar: 3000,
        },
      },
    },
    expected: {
      orderid: '11111',
      type: 2,
      preAmountForCar: 3000,
      vendorId: '13014',
    },
  },
];

export const getDepositPaymentParamsMockMap = [
  {
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        orderBaseInfo: {
          payMode: 2,
          preAuthStatus: 0,
        },
        driverInfo: {
          name: '陈XX',
        },
        freeDeposit: {
          preAmountForCar: 3000,
        },
        vendorInfo: {
          vendorID: '13014',
          vendorName: '大方租车',
          vendorLogo: '',
        },
        ctripInsuranceInfos: null,
        insuranceAndXProductDesc: [],
        insuranceAndXProduct: [
          {
            name: '人身及财物险',
            code: '2000896',
            title: '人身及财物险',
            specificName: 0,
            sourceFrom: 2,
            productId: 2000896,
            insuranceOrderId: '1111111',
            requestId: '3082488749-2000896',
            orderTitle: '保险加购',
            price: 156,
            localCurrencyCode: 'CNY',
            localTotalPrice: 156,
            localDailyPrice: 156,
            currentTotalPrice: 156,
            currentDailyPrice: 156,
            currentCurrencyCode: 'CNY',
            quantity: 1,
            quantityName: '',
            maxQuantity: 1,
            group: 1,
            status: 3,
            canUpgrade: false,
            extDesc: '',
            toDetailStatus: 0,
          },
        ],
        cancelRuleInfo: {
          cancelTip: '2021-06-20 06:00前免费取消，逾期收费。',
          reOrderTitle: '仍需用车？',
          reOrderExplain:
            '若门店车辆不满足您对颜色、牌照、配置或出行的相关需求，您可重新预订',
          cancelReasons: [
            '重新下单更优惠',
            '其他网站更便宜',
            '修改取还车信息',
            '订单未免押',
            '修改驾驶员信息',
            '行程取消无需用车',
            '门店无法提供车辆',
            '航班延误',
            '其他',
          ],
          cancelReasonsV2: [
            {
              reason: '行程变更/取消',
              code: 1,
            },
            {
              reason: '其他网站更便宜',
              code: 2,
            },
            {
              reason: '修改订单',
              code: 3,
            },
            {
              reason: '车辆有问题',
              code: 4,
            },
            {
              reason: '未带取车证件',
              code: 5,
            },
            {
              reason: '车辆限行限号',
              code: 6,
            },
            {
              reason: '门店无法提供车辆',
              code: 7,
            },
            {
              reason: '航班延误',
              code: 8,
            },
            {
              reason: '其他',
              code: 9,
            },
          ],
          cancelRules: [
            {
              freeStatus: 1,
              free: 1,
              title: '免费取消',
              context: '取车前4小时',
              time: '2021-06-20 06:00前',
              hit: true,
            },
            {
              freeStatus: 0,
              free: 0,
              title: '扣订单总金额50%',
              context: '取车前4小时-取车时间',
              time: '2021-06-20 06:00至10:00',
              hit: false,
            },
            {
              freeStatus: 0,
              free: 0,
              title: '扣订单全额',
              context: '取车时间后',
              time: '2021-06-20 10:00后',
              hit: false,
            },
          ],
          cancelTipColor: 1,
          bottomDesc: '因修改订单产生的“订单修改费”不退费',
        },
      },
    },
    expected: {
      vendorId: '13014',
    },
  },
];

export const getPaymentParamsMockMap = [
  {
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        orderBaseInfo: {
          payMode: 2,
          preAuthStatus: 0,
        },
        pickupStore: {},
        returnStore: {},
        driverInfo: {
          name: '陈XX',
        },
        freeDeposit: {
          preAmountForCar: 3000,
        },
        vendorInfo: {
          vendorID: '13014',
          vendorName: '大方租车',
          vendorLogo: '',
        },
        ctripInsuranceInfos: null,
        insuranceAndXProductDesc: [],
        insuranceAndXProduct: [
          {
            name: '人身及财物险',
            code: '2000896',
            title: '人身及财物险',
            specificName: 0,
            sourceFrom: 2,
            productId: 2000896,
            insuranceOrderId: '1111111',
            requestId: '3082488749-2000896',
            orderTitle: '保险加购',
            price: 156,
            localCurrencyCode: 'CNY',
            localTotalPrice: 156,
            localDailyPrice: 156,
            currentTotalPrice: 156,
            currentDailyPrice: 156,
            currentCurrencyCode: 'CNY',
            quantity: 1,
            quantityName: '',
            maxQuantity: 1,
            group: 1,
            status: 3,
            canUpgrade: false,
            extDesc: '',
            toDetailStatus: 0,
          },
        ],
        cancelRuleInfo: {
          cancelTip: '2021-06-20 06:00前免费取消，逾期收费。',
          reOrderTitle: '仍需用车？',
          reOrderExplain:
            '若门店车辆不满足您对颜色、牌照、配置或出行的相关需求，您可重新预订',
          cancelReasons: [
            '重新下单更优惠',
            '其他网站更便宜',
            '修改取还车信息',
            '订单未免押',
            '修改驾驶员信息',
            '行程取消无需用车',
            '门店无法提供车辆',
            '航班延误',
            '其他',
          ],
          cancelReasonsV2: [
            {
              reason: '行程变更/取消',
              code: 1,
            },
            {
              reason: '其他网站更便宜',
              code: 2,
            },
            {
              reason: '修改订单',
              code: 3,
            },
            {
              reason: '车辆有问题',
              code: 4,
            },
            {
              reason: '未带取车证件',
              code: 5,
            },
            {
              reason: '车辆限行限号',
              code: 6,
            },
            {
              reason: '门店无法提供车辆',
              code: 7,
            },
            {
              reason: '航班延误',
              code: 8,
            },
            {
              reason: '其他',
              code: 9,
            },
          ],
          cancelRules: [
            {
              freeStatus: 1,
              free: 1,
              title: '免费取消',
              context: '取车前4小时',
              time: '2021-06-20 06:00前',
              hit: true,
            },
            {
              freeStatus: 0,
              free: 0,
              title: '扣订单总金额50%',
              context: '取车前4小时-取车时间',
              time: '2021-06-20 06:00至10:00',
              hit: false,
            },
            {
              freeStatus: 0,
              free: 0,
              title: '扣订单全额',
              context: '取车时间后',
              time: '2021-06-20 10:00后',
              hit: false,
            },
          ],
          cancelTipColor: 1,
          bottomDesc: '因修改订单产生的“订单修改费”不退费',
        },
      },
    },
    expected: {
      vendorId: '13014',
    },
  },
];

export const licenseNoAndColorMockMap = [
  {
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        orderBaseInfo: {
          payMode: 2,
          preAuthStatus: 0,
          orderStatusCtrip: 'CAR_COMPLETED',
        },
        carAssistant: {
          safeRentList: [
            {
              commonItem: {
                code: '',
                title: '2021-11-17 21:30 还车',
                subTitles: [
                  {
                    title: '导航回还车门店还车地址:尚美科技大厦',
                    url: '',
                  },
                ],
                button: {
                  title: '地图 · 指引',
                  statusType: 1,
                  type: 21,
                },
              },
            },
            {
              commonItem: {
                code: '',
                title: '门店实时还车服务进度',
                subTitles: [],
                button: {
                  title: '用车指导',
                  actionUrl: '',
                  type: 101,
                },
              },
              safeRentProcess: true,
              safeInfo: {
                licenseNo: '粤A88669',
                color: '白色',
                sendPersonName: 'lixubin',
                sendPersonPhone: '15999999999',
                storePhone: '13489881541',
                processList: [
                  {
                    code: '',
                    title: '已分配取车员',
                    subTitles: [
                      {
                        title: 'lixubin,15999999999',
                        url: '',
                      },
                    ],
                    button: {
                      title: '联系取车员',
                      actionUrl: '15999999999',
                      type: 100,
                    },
                    status: 2,
                  },
                  {
                    code: '',
                    title: '上门取车',
                    subTitles: [],
                    status: 1,
                  },
                ],
              },
            },
            {
              commonItem: {
                code: '',
                title: '提前还车',
                subTitles: [
                  {
                    title:
                      '提前还车请联系门店，若门店同意退款，款项将原路退还，预计5个工作日内到账。',
                    url: '',
                  },
                ],
                button: {
                  title: '费用标准',
                  actionUrl: '',
                  type: 36,
                },
              },
              safeRentProcess: false,
            },
            {
              commonItem: {
                code: '',
                title: '燃油说明：等量取还',
                subTitles: [
                  {
                    title:
                      '还车时咨询门店，若可退费，则款项预计还车后5个工作日内退回携程钱包',
                    url: '',
                  },
                ],
                button: {
                  title: '费用标准',
                  actionUrl: '',
                  type: 37,
                },
              },
              safeRentProcess: false,
            },
          ],
        },
      },
    },
    expected: '粤A88669',
  },
  {
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        orderBaseInfo: {
          payMode: 2,
          preAuthStatus: 0,
          orderStatusCtrip: 'CAR_COMPLETED',
        },
        carAssistant: {
          safeRentList: [
            {
              commonItem: {
                code: '',
                title: '2021-11-17 21:30 还车',
                subTitles: [
                  {
                    title: '导航回还车门店还车地址:尚美科技大厦',
                    url: '',
                  },
                ],
                button: {
                  title: '地图 · 指引',
                  statusType: 1,
                  type: 21,
                },
              },
            },
            {
              commonItem: {
                code: '',
                title: '提前还车',
                subTitles: [
                  {
                    title:
                      '提前还车请联系门店，若门店同意退款，款项将原路退还，预计5个工作日内到账。',
                    url: '',
                  },
                ],
                button: {
                  title: '费用标准',
                  actionUrl: '',
                  type: 36,
                },
              },
              safeRentProcess: false,
            },
            {
              commonItem: {
                code: '',
                title: '燃油说明：等量取还',
                subTitles: [
                  {
                    title:
                      '还车时咨询门店，若可退费，则款项预计还车后5个工作日内退回携程钱包',
                    url: '',
                  },
                ],
                button: {
                  title: '费用标准',
                  actionUrl: '',
                  type: 37,
                },
              },
              safeRentProcess: false,
            },
          ],
        },
      },
    },
    expected: '',
  },
  {
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        orderBaseInfo: null,
        carAssistant: null,
      },
    },
    expected: '',
  },
];

export const getRentalPolicyParamsMockMap = [
  {
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        orderBaseInfo: {
          payMode: 2,
          preAuthStatus: 0,
          orderStatusCtrip: 'CAR_COMPLETED',
          safeRent: true,
        },
        pickupStore: {
          storeID: 'storeID',
        },
        vendorInfo: {
          vendorID: 'vendorID',
        },
      },
    },
    expected: {
      orderId: '11111',
      storeCode: 'storeID',
      vendorCode: 'vendorID',
      isEasyLife: true,
    },
  },
];

export const getBookingNoticeParamsMockMap = [
  {
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        orderBaseInfo: {
          payMode: 2,
          preAuthStatus: 0,
          orderStatusCtrip: 'CAR_COMPLETED',
          safeRent: true,
        },
        pickupStore: {
          storeID: 'storeID',
        },
        vendorInfo: {
          vendorID: 'vendorID',
        },
      },
    },
    expected: {
      orderId: '11111',
      storeCode: 'storeID',
      vendorCode: 'vendorID',
      isEasyLife: true,
    },
  },
];

export const updateFreeDepositInfoParamsMockMap = [
  {
    state: {
      OrderDetail: {
        reqOrderParams: {
          orderId: '11111',
        },
        orderBaseInfo: {
          payMode: 2,
          preAuthStatus: 0,
          orderStatusCtrip: 'CAR_COMPLETED',
          safeRent: true,
        },
        pickupStore: {
          storeID: 'storeID',
        },
        vendorInfo: {
          vendorID: 'vendorID',
        },
        freeDeposit: {
          preAmountForCar: 3000,
          freeDepositType: 1,
        },
      },
    },
    expected: {
      preAmountForCar: 3000,
      freeDepositType: 1,
      vendorId: 'vendorID',
      freeDepositWay: 2,
    },
  },
];

export const packagePriceDetailModalDataMockMap = [
  {
    state: {
      OrderDetail: {
        orderDetailPrice: {
          cashBackInfoV2: {
            title: '还车后可获',
            subTitle: '平台补贴返现',
            description: '还车完成后X个工作日内自动返至「我的钱包」',
            code: 'Summary',
            type: 1,
            currencyCode: 'CNY',
            currentTotalPrice: 261,
            payMode: 2,
            items: [],
            notices: ['还车后返现¥20'],
            labels: [
              {
                code: '1',
                title: '还车后可获',
                subTitle: '平台补贴返现',
              },
            ],
          },
          chargesSummary: {},
          userPoints: [],
          chargesInfos: [
            {
              title: '车辆租金',
              code: 'CAR_RENTAL_FEE',
              currencyCode: 'CNY',
              currentDailyPrice: 70,
              currentTotalPrice: 693,
              items: [
                {
                  title: '租车费',
                  subTitle: '',
                  code: '1001',
                  size: '¥99 x9天 + ¥99',
                  count: 10,
                  unit: '',
                  currencyCode: 'CNY',
                  currentDailyPrice: 99,
                  currentTotalPrice: 990,
                  priceDailys: [
                    {
                      date: '8月11日 周四',
                      priceStr: '¥99',
                      showType: 0,
                    },
                    {
                      date: '8月12日 周五',
                      priceStr: '¥99',
                      showType: 0,
                    },
                    {
                      date: '8月13日 周六',
                      priceStr: '¥99',
                      showType: 0,
                    },
                    {
                      date: '8月14日 周日',
                      priceStr: '¥99',
                      showType: 0,
                    },
                  ],
                },
                {
                  title: '夏日特惠',
                  code: 'PMS_SAT18140545821',
                  type: 1,
                  currencyCode: 'CNY',
                  currentTotalPrice: 297,
                  payMode: 1,
                },
              ],
              originDailyPrice: 99,
            },
          ],
        },
      },
    },
    isNewInsurance: true,
    expected: {
      chargesInfos: [
        {
          title: '车辆租金',
          code: 'CAR_RENTAL_FEE',
          currencyCode: 'CNY',
          currentDailyPrice: 70,
          currentTotalPrice: 693,
          items: [
            {
              title: '租车费',
              subTitle: '',
              code: '1001',
              size: '¥99 x9天 + ¥99',
              count: 10,
              unit: '',
              currencyCode: 'CNY',
              currentDailyPrice: 99,
              currentTotalPrice: 990,
              isPromotion: false,
              priceDailys: [
                {
                  date: '8月11日 周四',
                  priceStr: '¥99',
                  showType: 0,
                },
                {
                  date: '8月12日 周五',
                  priceStr: '¥99',
                  showType: 0,
                },
                {
                  date: '8月13日 周六',
                  priceStr: '¥99',
                  showType: 0,
                },
                {
                  date: '8月14日 周日',
                  priceStr: '¥99',
                  showType: 0,
                },
              ],
            },
            {
              title: '夏日特惠',
              code: 'PMS_SAT18140545821',
              type: 1,
              currencyCode: 'CNY',
              currentTotalPrice: 297,
              payMode: 1,
              isPromotion: true,
              size: undefined,
            },
          ],
          originDailyPrice: 99,
        },
      ],
      chargesSummary: {
        isPriceDesc: false,
      },
      cashBackInfo: {
        title: '还车后可获',
        subTitle: '平台补贴返现',
        description: '还车完成后X个工作日内自动返至「我的钱包」',
        code: 'Summary',
        type: 1,
        currencyCode: 'CNY',
        currentTotalPrice: 261,
        payMode: 2,
        items: [],
        notices: [],
        labels: [
          {
            code: '1',
            title: '还车后可获',
            subTitle: '平台补贴返现',
          },
        ],
      },
      points: [],
    },
  },
];

export const getCarLabelsInfoMockMap = [
  {
    state: {
      OrderDetail: {
        labelsInfo: [
          {
            code: '3547',
            name: '两年内新车',
            desc: '行驶证注册年限小于两年。',
            marketGroupCode: 'MarketGroup1201',
            colorCode: '8',
          },
          {
            code: '3495',
            name: '手机支架',
            desc: '车内配备有手机支架。',
            marketGroupCode: 'MarketGroup1201',
            colorCode: '1',
          },
        ],
      },
    },
    expected: [
      {
        code: '3547',
        name: '两年内新车',
        desc: '行驶证注册年限小于两年。',
        marketGroupCode: 'MarketGroup1201',
        colorCode: '8',
        title: '两年内新车',
        category: '1',
        description: '行驶证注册年限小于两年。',
        isShowQuestion: false,
      },
      {
        code: '3495',
        name: '手机支架',
        desc: '车内配备有手机支架。',
        marketGroupCode: 'MarketGroup1201',
        colorCode: '1',
        title: '手机支架',
        category: '1',
        description: '车内配备有手机支架。',
        isShowQuestion: false,
      },
    ],
  },
  {
    state: {
      OrderDetail: {
        labelsInfo: [
          {
            code: '3547',
            name: '两年内新车',
            desc: '行驶证注册年限小于两年。',
            marketGroupCode: 'MarketGroup1200',
            colorCode: '8',
          },
        ],
      },
    },
    expected: [],
  },
  {
    state: {
      OrderDetail: {},
    },
    expected: [],
  },
];

export const getXProductDatasMap = [
  {
    state: {
      OrderDetail: {
        insuranceAndXProduct: [
          {
            name: '人身及财物险',
            code: '2000896',
            title: '人身及财物险',
            specificName: 0,
            sourceFrom: 2,
            productId: 2000896,
            insuranceOrderId: '1111111',
            requestId: '3082488749-2000896',
            orderTitle: '保险加购',
            price: 156,
            localCurrencyCode: 'CNY',
            localTotalPrice: 156,
            localDailyPrice: 156,
            currentTotalPrice: 156,
            currentDailyPrice: 156,
            currentCurrencyCode: 'CNY',
            quantity: 1,
            quantityName: '',
            maxQuantity: 1,
            group: 2,
            status: 3,
            canUpgrade: false,
            extDesc: '',
            toDetailStatus: 0,
          },
        ],
        extraInfos: {},
      },
    },
    expected: 1,
  },
];

export const cancelReasonTipAboutDeposit = [
  {
    state: {
      OrderDetail: {
        insuranceAndXProduct: [
          {
            name: '人身及财物险',
            code: '2000896',
            title: '人身及财物险',
            specificName: 0,
            sourceFrom: 2,
            productId: 2000896,
            insuranceOrderId: '1111111',
            requestId: '3082488749-2000896',
            orderTitle: '保险加购',
            price: 156,
            localCurrencyCode: 'CNY',
            localTotalPrice: 156,
            localDailyPrice: 156,
            currentTotalPrice: 156,
            currentDailyPrice: 156,
            currentCurrencyCode: 'CNY',
            quantity: 1,
            quantityName: '',
            maxQuantity: 1,
            group: 2,
            status: 3,
            canUpgrade: false,
            extDesc: '',
            toDetailStatus: 0,
          },
        ],
        extraInfos: {},
      },
    },
    expected: 1,
  },
];

export const cancelReasonTipAboutDepositMockMap = [
  {
    state: {
      OrderDetail: {
        freeDeposit: {
          preAmountForCar: 6000,
          preAmountForPeccancy: 3000,
          freeDepositType: 10,
          depositStatus: 1,
        },
      },
    },
    expected: {
      preAmountForCarTxt: '免租车押金',
      preAmountForCar: 6000,
      preAmountForPeccancy: 3000,
      preAmountForPeccancyTxt: '免违章押金',
    },
  },
  {
    state: {
      OrderDetail: {
        freeDeposit: {
          preAmountForCar: 6000,
          preAmountForPeccancy: 3000,
          freeDepositType: 10,
          depositStatus: 0,
        },
      },
    },
    expected: false,
  },
];

export const getInstructionsAnchorMockMap = [
  {
    state: {
      OrderDetail: {
        queryCarAssistantV2Response: {
          guidInfoGroupList: [
            {
              groupTitle: 'before',
              guidItemList: [
                {
                  code: 'necessaryMaterials',
                  title: '取车必备材料',
                  desc: '均需驾驶员王良军本人证件',
                  items: [
                    {
                      code: 'necessaryMaterialsSon',
                      linkContent: [
                        {
                          id: 1,
                          title: '身份证原件',
                          desc: '有效期1个月以上',
                          type: 1,
                        },
                        {
                          id: 2,
                          title:
                            '中国大陆驾驶证原件\n或 “交管12123”APP发放的电子驾驶证',
                          desc: '有效期2个月以上',
                          type: 2,
                        },
                      ],
                    },
                  ],
                },
                {
                  code: 'pickUpProcess',
                  title: '取车流程',
                  items: [
                    {
                      code: 'pickUpProcessPickWay',
                      title: '店员免费送车上门',
                      desc: '翠屏·凤凰水城-F4栋',
                      underButton: {
                        title: '地图及指引',
                      },
                    },
                    {
                      code: 'clerkVerification',
                      title: '店员核验证件',
                      desc: '取车前，店员需要对您的身份证件及驾驶证件进行拍照核验，确认有效并为本人持有。核验通过才可取车。',
                    },
                    {
                      code: 'testVehicle',
                      title: '店员检验车辆',
                      desc: '拍照记录仪表盘油量及里程，检验车身外部、座椅、玻璃、轮胎是否有划痕或凹陷。如有请拍照记录，并要求店员在验车单上记录',
                    },
                    {
                      code: 'pickUpSignContract',
                      title: '签署合同',
                      button: {
                        title: '预览合同',
                      },
                      desc: '合同签署后，取车完成',
                    },
                  ],
                },
              ],
            },
            {
              groupTitle: 'driving',
              guidItemList: [
                {
                  code: 'driving',
                  title: '驾驶注意事项',
                  items: [
                    {
                      code: 'mileageLimit',
                      title: '里程限制',
                      contentObject: [
                        {
                          stringObjs: [
                            {
                              content: '租期内没有里程数限制',
                            },
                          ],
                        },
                      ],
                    },
                    {
                      code: 'noGoAreas',
                      title: '禁行区域',
                      contentObject: [
                        {
                          stringObjs: [
                            {
                              content: '车辆不允许驶出海南',
                            },
                          ],
                        },
                        {
                          stringObjs: [
                            {
                              content:
                                '*若违反上述禁行规则，门店可拒绝提供或强制收回车辆；强制收回车辆时，产生的相关费用由用户承担，剩余租期费用全部退还给用户。',
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
                {
                  code: 'accidentHandling',
                  title: '发生事故怎么办',
                  items: [
                    {
                      code: 'accidentHandlingSon',
                      title: '事故处理流程',
                      button: {
                        title: '查看详情',
                        description: '',
                        statusType: 0,
                        actionUrl: '',
                        icon: '',
                        type: 38,
                      },
                      linkContent: [
                        {
                          title: '立即联系门店及交通警察',
                          desc: '若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店已获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。',
                        },
                        {
                          title: '拍照并留存记录信息',
                          desc: '请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。',
                        },
                        {
                          title:
                            '请您明确涉事方事故责任，或等待交警抵达现场进行处理',
                        },
                        {
                          title: '请您配合交警处理完事故后，方可离开事故现场',
                        },
                        {
                          title: '准备材料，进行保险理赔流程',
                        },
                      ],
                      desc: '发生事故时，请及时联系门店及交通警察',
                    },
                  ],
                },
              ],
            },
            {
              isDefault: true,
              groupTitle: 'after',
              guidItemList: [
                {
                  code: 'returnEarly',
                  title: '还车准备',
                  items: [
                    {
                      code: 'fuelOil',
                      title: '确认油量与取车时一致',
                      contentObject: [
                        {
                          contentStyle: '13',
                          stringObjs: [
                            {
                              content: '还车时需保持与取车时油量一致',
                            },
                          ],
                        },
                        {
                          stringObjs: [
                            {
                              content:
                                '若还车油量少于取车油量，门店将收取油费。',
                            },
                          ],
                        },
                        {
                          stringObjs: [
                            {
                              content:
                                '若还车油量多于取车油量，门店将返还多余油费。',
                            },
                          ],
                        },
                        {
                          contentStyle: '10',
                          stringObjs: [
                            {
                              content:
                                '油费=市场油价×油箱容量×(缺失格数/油表总格数)',
                            },
                          ],
                        },
                        {
                          stringObjs: [
                            {
                              content: '*建议您自行保留一份油量记录。',
                            },
                          ],
                        },
                      ],
                    },
                    {
                      code: 'returnEarly',
                      title: '若提前或延迟还车，请及时与门店联系',
                      button: {
                        title: '门店政策',
                      },
                      desc: '相关收费标准可查看门店政策',
                      underButton: {
                        description: '订单支持在线续租，取车后可操作',
                      },
                    },
                  ],
                },
                {
                  code: 'returnProcess',
                  title: '还车流程',
                  items: [
                    {
                      code: 'returnWay',
                      title: '店员免费上门取车',
                      desc: '翠屏·凤凰水城-F4栋',
                      underButton: {
                        title: '地图及指引',
                      },
                    },
                    {
                      code: 'returnTestVehicle',
                      title: '店员检验车辆',
                      desc: '车辆停靠指定位置后，等待工作人员检验车辆',
                    },
                    {
                      code: 'returnSignContract',
                      title: '签署还车验车单',
                      desc: '若对车辆核验结果和费用结算无异议，您需在还车验车单上签字，完成还车',
                    },
                  ],
                },
              ],
            },
          ],
        },
      },
    },
    expected: 'DropOff',
  },
  {
    state: {
      OrderDetail: {
        queryCarAssistantV2Response: {
          guidInfoGroupList: [
            {
              isDefault: true,
              groupTitle: 'before',
              guidItemList: [
                {
                  code: 'necessaryMaterials',
                  title: '取车必备材料',
                  desc: '均需驾驶员王良军本人证件',
                  items: [
                    {
                      code: 'necessaryMaterialsSon',
                      linkContent: [
                        {
                          id: 1,
                          title: '身份证原件',
                          desc: '有效期1个月以上',
                          type: 1,
                        },
                        {
                          id: 2,
                          title:
                            '中国大陆驾驶证原件\n或 “交管12123”APP发放的电子驾驶证',
                          desc: '有效期2个月以上',
                          type: 2,
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      },
    },
    expected: 'PickUp',
  },
  {
    state: {
      OrderDetail: {
        queryCarAssistantV2Response: {
          guidInfoGroupList: [
            {
              isDefault: true,
              groupTitle: 'driving',
              guidItemList: [
                {
                  code: 'driving',
                  title: '驾驶注意事项',
                  items: [
                    {
                      code: 'mileageLimit',
                      title: '里程限制',
                      contentObject: [
                        {
                          stringObjs: [
                            {
                              content: '租期内没有里程数限制',
                            },
                          ],
                        },
                      ],
                    },
                    {
                      code: 'noGoAreas',
                      title: '禁行区域',
                      contentObject: [
                        {
                          stringObjs: [
                            {
                              content: '车辆不允许驶出海南',
                            },
                          ],
                        },
                        {
                          stringObjs: [
                            {
                              content:
                                '*若违反上述禁行规则，门店可拒绝提供或强制收回车辆；强制收回车辆时，产生的相关费用由用户承担，剩余租期费用全部退还给用户。',
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
                {
                  code: 'accidentHandling',
                  title: '发生事故怎么办',
                  items: [
                    {
                      code: 'accidentHandlingSon',
                      title: '事故处理流程',
                      button: {
                        title: '查看详情',
                        description: '',
                        statusType: 0,
                        actionUrl: '',
                        icon: '',
                        type: 38,
                      },
                      linkContent: [
                        {
                          title: '立即联系门店及交通警察',
                          desc: '若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店已获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。',
                        },
                        {
                          title: '拍照并留存记录信息',
                          desc: '请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。',
                        },
                        {
                          title:
                            '请您明确涉事方事故责任，或等待交警抵达现场进行处理',
                        },
                        {
                          title: '请您配合交警处理完事故后，方可离开事故现场',
                        },
                        {
                          title: '准备材料，进行保险理赔流程',
                        },
                      ],
                      desc: '发生事故时，请及时联系门店及交通警察',
                    },
                  ],
                },
              ],
            },
          ],
        },
      },
    },
    expected: 'InUse',
  },
];

export const isSupportCreaditMockStore = {
  Sesame: {
    authStatus: 0,
    userName: '',
  },
  OrderDetail: {
    freeDeposit: {
      depositStatus: 0,
      showDepositType: 2,
      depositExplain: '取车时冻结租车押金¥20000，还车时冻结违章押金¥2000',
      freeDepositType: 10,
      freeDepositWay: 1,
      preAmountForCar: 20000,
      preAmountForPeccancy: 2000,
      depositItems: [
        {
          depositTitle: '租车押金',
          deposit: 20000,
          depositStatus: 2,
          explain: '取车时冻结20000元租车押金，若无车损，还车时解冻',
        },
        {
          depositTitle: '违章押金',
          deposit: 2000,
          depositStatus: 2,
          explain:
            '还车时冻结2000元违章押金，若无违章，银行会在还车后30天左右退还押金',
        },
        {
          depositTitle: '支付方式',
          deposit: 0,
          depositStatus: 2,
          explain: '信用卡',
        },
      ],
      deductionTime: '11月18日 17:00',
      isBeforeNow: true,
      tip: {
        texts: [
          {
            title: '芝麻信用≥650分有机会可享',
          },
        ],
        btnType: 2,
      },
    },
    orderBaseInfo: {
      orderId: 3178103378,
      uId: 'M00111825',
      orderDate: 1599047971137,
      orderStatus: 2,
      orderStatusDesc: '已确认',
      allOperations: [
        {
          operationId: 1,
          buttonName: '继续支付',
          enable: false,
        },
        {
          operationId: 3,
          buttonName: '去点评',
          enable: false,
        },
        {
          operationId: 4,
          buttonName: '再次预订',
          enable: true,
        },
        {
          operationId: 2,
          buttonName: '取消订单',
          enable: true,
        },
        {
          operationId: 7,
          buttonName: '修改订单',
          enable: false,
        },
      ],
      orderTip: {
        tipContentArray: ['已为您预留车辆，取车时请携带必要证件'],
      },
      useDate: 1599616800000,
      returnDate: 1599789600000,
      duration: 48,
      ftype: 0,
      useCityID: 2,
      useCity: '上海',
      selfName: '携程优选',
      vendorOrderCode: '3178103387',
      useQuantity: 2,
      processStatus: 5,
      lastEnablePayTime: 1599049771137,
      orderType: 0,
      payMode: 2,
      payModeDesc: '在线支付',
      distributionChannelId: 7,
      quickPayNo: '',
      remark: '',
      rateCode: '',
      rateCategory: '',
      grantedCode: '',
      preAmountForCar: 3000,
      preAmountForPeccancy: 3000,
      preAmountType: 3,
      preAuthStatus: 0,
      vendorPreAuthInfo: {
        preAuthDisplay: 0,
        preWay: 0,
        authdesc: '还车3天内退回',
        authMartket: 200,
        authLabel: '在线支付租车押金￥3000',
        quickPayNo: '8e07db42-37cb-4f43-a874-8f360add16d0',
      },
      preAmountDesc: [
        {
          name: '1',
          description: '取车时冻结3000元作为租车押金，还车时解冻',
        },
        {
          name: '2',
          description:
            '还车时冻结3000元违章押金，还车后30天内无违章，由银行自动解冻该笔押金',
        },
      ],
      freeCancelTime: 1599616800000,
      cancelRuleDesc: '',
      alipay: false,
      safeRent: true,
      successSafeRentAuth: true,
      orderContact: false,
      continueBackPay: false,
      creditRiskResult: 'N',
      creditRiskRequestId: '60d6d959-a741-4453-95ae-d1cac9a6e285',
      foreInsurance: 0,
    },
  },
};

export const creditRentBackPayMockStore = {
  Sesame: {
    authStatus: 0,
    userName: '',
  },
  OrderDetail: {
    freeDeposit: {
      depositStatus: 0,
      showDepositType: 1,
      depositExplain: '取车时冻结租车押金¥20000，还车时冻结违章押金¥2000',
      freeDepositType: 10,
      freeDepositWay: 1,
      preAmountForCar: 20000,
      preAmountForPeccancy: 2000,
      depositItems: [
        {
          depositTitle: '租车押金',
          deposit: 20000,
          depositStatus: 2,
          explain: '取车时冻结20000元租车押金，若无车损，还车时解冻',
        },
        {
          depositTitle: '违章押金',
          deposit: 2000,
          depositStatus: 2,
          explain:
            '还车时冻结2000元违章押金，若无违章，银行会在还车后30天左右退还押金',
        },
        {
          depositTitle: '支付方式',
          deposit: 0,
          depositStatus: 2,
          explain: '信用卡',
        },
      ],
      deductionTime: '11月18日 17:00',
      isBeforeNow: true,
      tip: {
        texts: [
          {
            title: '芝麻信用≥650分有机会可享',
          },
        ],
        btnType: 2,
      },
    },
    orderBaseInfo: {
      orderId: 3178103378,
      uId: 'M00111825',
      orderDate: 1599047971137,
      orderStatus: 2,
      orderStatusDesc: '已确认',
      allOperations: [
        {
          operationId: 1,
          buttonName: '继续支付',
          enable: false,
        },
        {
          operationId: 3,
          buttonName: '去点评',
          enable: false,
        },
        {
          operationId: 4,
          buttonName: '再次预订',
          enable: true,
        },
        {
          operationId: 2,
          buttonName: '取消订单',
          enable: true,
        },
        {
          operationId: 7,
          buttonName: '修改订单',
          enable: false,
        },
      ],
      orderTip: {
        tipContentArray: ['已为您预留车辆，取车时请携带必要证件'],
      },
      useDate: 1599616800000,
      returnDate: 1599789600000,
      duration: 48,
      ftype: 0,
      useCityID: 2,
      useCity: '上海',
      selfName: '携程优选',
      vendorOrderCode: '3178103387',
      useQuantity: 2,
      processStatus: 5,
      lastEnablePayTime: 1599049771137,
      orderType: 0,
      payMode: 2,
      payModeDesc: '在线支付',
      distributionChannelId: 7,
      quickPayNo: '',
      remark: '',
      rateCode: '',
      rateCategory: '',
      grantedCode: '',
      preAmountForCar: 3000,
      preAmountForPeccancy: 3000,
      preAmountType: 3,
      preAuthStatus: 0,
      vendorPreAuthInfo: {
        preAuthDisplay: 0,
        preWay: 0,
        authdesc: '还车3天内退回',
        authMartket: 200,
        authLabel: '在线支付租车押金￥3000',
        quickPayNo: '8e07db42-37cb-4f43-a874-8f360add16d0',
      },
      preAmountDesc: [
        {
          name: '1',
          description: '取车时冻结3000元作为租车押金，还车时解冻',
        },
        {
          name: '2',
          description:
            '还车时冻结3000元违章押金，还车后30天内无违章，由银行自动解冻该笔押金',
        },
      ],
      freeCancelTime: 1599616800000,
      cancelRuleDesc: '',
      alipay: false,
      safeRent: true,
      successSafeRentAuth: true,
      orderContact: false,
      continueBackPay: false,
      creditRiskResult: 'N',
      creditRiskRequestId: '60d6d959-a741-4453-95ae-d1cac9a6e285',
      foreInsurance: 0,
    },
  },
};

export const hasAuthCreditRentMock = {
  Sesame: {
    authStatus: 0,
    userName: '',
  },
  OrderDetail: {
    freeDeposit: {
      depositStatus: 1,
      showDepositType: 1,
      depositExplain: '取车时冻结租车押金¥20000，还车时冻结违章押金¥2000',
      freeDepositType: 10,
      freeDepositWay: 1,
      preAmountForCar: 20000,
      preAmountForPeccancy: 2000,
      depositItems: [
        {
          depositTitle: '租车押金',
          deposit: 20000,
          depositStatus: 2,
          explain: '取车时冻结20000元租车押金，若无车损，还车时解冻',
        },
        {
          depositTitle: '违章押金',
          deposit: 2000,
          depositStatus: 2,
          explain:
            '还车时冻结2000元违章押金，若无违章，银行会在还车后30天左右退还押金',
        },
        {
          depositTitle: '支付方式',
          deposit: 0,
          depositStatus: 2,
          explain: '信用卡',
        },
      ],
      deductionTime: '11月18日 17:00',
      isBeforeNow: true,
      tip: {
        texts: [
          {
            title: '芝麻信用≥650分有机会可享',
          },
        ],
        btnType: 2,
      },
    },
    orderBaseInfo: {
      orderId: 3178103378,
      uId: 'M00111825',
      orderDate: 1599047971137,
      orderStatus: 2,
      orderStatusDesc: '已确认',
      allOperations: [
        {
          operationId: 1,
          buttonName: '继续支付',
          enable: false,
        },
        {
          operationId: 3,
          buttonName: '去点评',
          enable: false,
        },
        {
          operationId: 4,
          buttonName: '再次预订',
          enable: true,
        },
        {
          operationId: 2,
          buttonName: '取消订单',
          enable: true,
        },
        {
          operationId: 7,
          buttonName: '修改订单',
          enable: false,
        },
      ],
      orderTip: {
        tipContentArray: ['已为您预留车辆，取车时请携带必要证件'],
      },
      useDate: 1599616800000,
      returnDate: 1599789600000,
      duration: 48,
      ftype: 0,
      useCityID: 2,
      useCity: '上海',
      selfName: '携程优选',
      vendorOrderCode: '3178103387',
      useQuantity: 2,
      processStatus: 5,
      lastEnablePayTime: 1599049771137,
      orderType: 0,
      payMode: 2,
      payModeDesc: '在线支付',
      distributionChannelId: 7,
      quickPayNo: '',
      remark: '',
      rateCode: '',
      rateCategory: '',
      grantedCode: '',
      preAmountForCar: 3000,
      preAmountForPeccancy: 3000,
      preAmountType: 3,
      preAuthStatus: 0,
      vendorPreAuthInfo: {
        preAuthDisplay: 0,
        preWay: 0,
        authdesc: '还车3天内退回',
        authMartket: 200,
        authLabel: '在线支付租车押金￥3000',
        quickPayNo: '8e07db42-37cb-4f43-a874-8f360add16d0',
      },
      preAmountDesc: [
        {
          name: '1',
          description: '取车时冻结3000元作为租车押金，还车时解冻',
        },
        {
          name: '2',
          description:
            '还车时冻结3000元违章押金，还车后30天内无违章，由银行自动解冻该笔押金',
        },
      ],
      freeCancelTime: 1599616800000,
      cancelRuleDesc: '',
      alipay: false,
      safeRent: true,
      successSafeRentAuth: true,
      orderContact: false,
      continueBackPay: false,
      creditRiskResult: 'N',
      creditRiskRequestId: '60d6d959-a741-4453-95ae-d1cac9a6e285',
      foreInsurance: 0,
    },
  },
};

export const getCarAssitantDataMockMap = [
  {
    state: {
      OrderDetail: {
        carAssistant: {
          safeRentList: [
            {
              commonItem: {
                code: '',
                title: '2021-11-17 21:30 还车',
                subTitles: [
                  {
                    title: '导航回还车门店还车地址:尚美科技大厦',
                    url: '',
                  },
                ],
                button: {
                  title: '地图 · 指引',
                  statusType: 1,
                  type: 21,
                },
              },
            },
            {
              commonItem: {
                code: '',
                title: '门店实时还车服务进度',
                subTitles: [],
                button: {
                  title: '用车指导',
                  actionUrl: '',
                  type: 101,
                },
              },
              safeRentProcess: true,
              safeInfo: {
                licenseNo: '粤A88669',
                color: '白色',
                sendPersonName: 'lixubin',
                sendPersonPhone: '15999999999',
                storePhone: '13489881541',
                processList: [
                  {
                    code: '',
                    title: '已分配取车员',
                    subTitles: [
                      {
                        title: 'lixubin,15999999999',
                        url: '',
                      },
                    ],
                    button: {
                      title: '联系取车员',
                      actionUrl: '15999999999',
                      type: 100,
                    },
                    status: 2,
                  },
                  {
                    code: '',
                    title: '上门取车',
                    subTitles: [],
                    status: 1,
                  },
                ],
              },
            },
            {
              commonItem: {
                code: '',
                title: '提前还车',
                subTitles: [
                  {
                    title:
                      '提前还车请联系门店，若门店同意退款，款项将原路退还，预计5个工作日内到账。',
                    url: '',
                  },
                ],
                button: {
                  title: '费用标准',
                  actionUrl: '',
                  type: 36,
                },
              },
              safeRentProcess: false,
            },
            {
              commonItem: {
                code: '',
                title: '燃油说明：等量取还',
                subTitles: [
                  {
                    title:
                      '还车时咨询门店，若可退费，则款项预计还车后5个工作日内退回携程钱包',
                    url: '',
                  },
                ],
                button: {
                  title: '费用标准',
                  actionUrl: '',
                  type: 37,
                },
              },
              safeRentProcess: false,
            },
            {
              commonItem: {
                code: '',
                title: '事故处理',
                subTitles: [
                  {
                    title: '行程中如遇问题，请及时致电车行或门店，并拍照记录。',
                    url: '',
                  },
                ],
                linkContent: [
                  {
                    title: '立即联系门店及交通警察',
                    desc: '若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店已获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。',
                  },
                  {
                    title: '拍照并留存记录信息',
                    desc: '请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。',
                  },
                  {
                    title: '请您明确涉事方事故责任，或等待交警抵达现场进行处理',
                  },
                  {
                    title: '请您配合交警处理完事故后，方可离开事故现场',
                  },
                  {
                    title: '准备材料，进行保险理赔流程',
                  },
                ],
                button: {
                  title: '处理流程',
                  description: '',
                  statusType: 0,
                  actionUrl: '',
                  icon: '',
                  type: 38,
                },
              },
            },
          ],
        },
      },
    },
    expected: 5,
  },
  {
    state: {
      OrderDetail: {
        carAssistant: {
          guidInfoGroupList: [
            {
              isDefault: false,
              groupTitle: '出发前准备',
              groupSubTitle: '证件一览，取车指引',
              guidItemList: [
                {
                  title: '2021-11-02 10:00:00 取车',
                  subTitle:
                    '免费站内取车\n取车地址:上海市华翔路2819号(新家弄新区54号)',
                  button: { title: '地图 · 指引', statusType: 1, type: 11 },
                },
                {
                  title: '用车必备材料',
                  subTitle:
                    '身份证+中国大陆驾照+支付宝/微信/驾驶员本人信用卡\n',
                  button: {
                    title: '材料要求',
                    description: '',
                    statusType: 1,
                    actionUrl: '',
                    icon: '',
                    type: 12,
                  },
                  linkContent: [
                    {
                      id: 1,
                      title: '驾驶员本人身份证原件',
                      desc: '有效期1个月以上',
                      subItem: [],
                    },
                    {
                      id: 2,
                      title:
                        '驾驶员本人中国大陆驾照原件驾驶员本人\n中国大陆驾照原件驾驶员本人中国大陆驾照原件',
                      desc: '有效期2个月以上\n有效期2个月以上',
                      subItem: [],
                    },
                    {
                      id: 3,
                      title: '支付宝/微信/驾驶员本人国内信用卡',
                      desc: '用于到店支付押金',
                      subItem: [
                        {
                          id: 4,
                          desc: '如通过支付宝/微信支付押金，请在门店扫描二维码进行押金支付',
                          type: 3,
                        },
                        {
                          id: 5,
                          desc: '如通过信用卡刷支付押金，信用卡有效期需1个月以上',
                        },
                      ],
                    },
                  ],
                },
                {
                  title: '取车时请验车',
                  button: {
                    title: '',
                    description: '',
                    statusType: 0,
                    actionUrl: '',
                    icon: '',
                    type: 0,
                  },
                  linkContent: [],
                  desc: '若车辆外部存在划痕、破损，要求工作人员在验车单上标注清楚。若存在影响驾驶的问题，与现场工作人员沟通，协调换车。',
                },
                {
                  title: '限行规则',
                  button: {
                    title: '限行说明',
                    description: '',
                    statusType: 0,
                    actionUrl: '',
                    icon: '',
                    type: 13,
                  },
                  desc: '上海工作日部分时段外牌车辆限行政策',
                },
              ],
            },
            {
              isDefault: true,
              groupTitle: '行中帮助',
              groupSubTitle: '快速缴费，保险理赔',
              guidItemList: [
                {
                  title: '2021-11-04 10:00 还车',
                  subTitle:
                    '免费站内还车\n还车地址:上海市华翔路2819号(新家弄新区54号)',
                  button: { title: '地图 · 指引', statusType: 1, type: 21 },
                },
              ],
            },
            {
              isDefault: true,
              groupTitle: '还车帮助',
              groupSubTitle: '还车指引，我要点评',
              guidItemList: [
                {
                  title: '提前还车',
                  subTitle:
                    '还车时和门店沟通，若存在提前还车需要退费的情况，款项原路退还，预计5个工作日内到账。',
                  button: {
                    title: '费用标准',
                    description: '',
                    statusType: 1,
                    actionUrl: '',
                    icon: '',
                    type: 36,
                  },
                },
                {
                  title: '燃油说明：等量取还',
                  subTitle:
                    '还车时和门店沟通，若存在多加油需要退费的情况，款项退到携程钱包，预计5个工作日内到账。',
                  button: {
                    title: '费用标准',
                    description: '',
                    statusType: 1,
                    actionUrl: '',
                    icon: '',
                    type: 37,
                  },
                },
                {
                  title: '事故处理',
                  button: {
                    title: '处理流程',
                    description: '',
                    statusType: 0,
                    actionUrl: '',
                    icon: '',
                    type: 38,
                  },
                  linkContent: [
                    {
                      title: '立即联系门店及交通警察',
                      desc: '若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店已获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。',
                    },
                    {
                      title: '拍照并留存记录信息',
                      desc: '请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。',
                    },
                    {
                      title:
                        '请您明确涉事方事故责任，或等待交警抵达现场进行处理',
                    },
                    { title: '请您配合交警处理完事故后，方可离开事故现场' },
                    { title: '准备材料，进行保险理赔流程' },
                  ],
                  desc: '行程中如遇问题，请及时致电车行或门店，并拍照记录。',
                },
              ],
            },
          ],
        },
      },
    },
    expected: 4,
  },
];

export const getSupportModifyOrderMockMap = [
  {
    state: {
      OrderDetail: {
        orderBaseInfo: {
          allOperations: [
            {
              operationId: 7,
              buttonName: '修改订单',
              enable: true,
              display: null,
              code: 1,
            },
          ],
        },
      },
    },
    expected: true,
  },
  {
    state: {
      OrderDetail: {
        orderBaseInfo: {
          allOperations: [
            {
              operationId: 7,
              buttonName: '修改订单',
              enable: true,
              display: null,
              code: 0,
            },
          ],
        },
      },
    },
    expected: false,
  },
  {
    state: {
      OrderDetail: {
        orderBaseInfo: {
          allOperations: [
            {
              operationId: 7,
              buttonName: '修改订单',
              enable: false,
              display: null,
              code: 0,
            },
          ],
        },
      },
    },
    expected: false,
  },
];

export const mapChangeReminderToWaringInfoMock = [
  {
    data: {
      changeTipTime: 1670322136536,
      tipText: {
        title: '取消政策即将变化',
        description: '10-21 19:00后取消将收取违约金¥30',
      },
    },
    expected: {
      prefixTitle: '取消政策即将变化',
      warningDtos: [
        {
          warningTitle: '10-21 19:00后取消将收取违约金¥30',
        },
      ],
    },
  },
  {
    data: null,
    expected: {
      prefixTitle: undefined,
      warningDtos: [
        {
          warningTitle: undefined,
        },
      ],
    },
  },
];

export const getDamageProveRenderDataMockMap = [
  {
    damageImgList: [
      {
        imgUrl: [
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
        ],
        commitTime: '2022-02-15 20:12:27',
        firstCommit: true,
        vedioUrl: [
          {
            videoUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            coverUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          },
        ],
      },
      {
        imgUrl: [
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
        ],
        commitTime: '2022-02-15 20:12:27',
        firstCommit: true,
        vedioUrl: [
          {
            videoUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            coverUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          },
        ],
      },
    ],
    expected: {
      combineUrls: [
        {
          title: '2022年02月15日 20:12 记录',
          urlItems: [
            {
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
            {
              imageUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
        },
        {
          title: '2022年02月15日 20:12 记录',
          urlItems: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
            {
              imageUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
        },
      ],
      pureImageList: [
        {
          imageUrl:
            'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
        },
        {
          imageUrl:
            'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
        },
      ],
    },
  },
  {
    damageImgList: [
      {
        imgUrl: [
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
        ],
        commitTime: '2022-02-15 20:12:27',
        firstCommit: false,
        vedioUrl: [
          {
            videoUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            coverUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          },
        ],
      },
      {
        imgUrl: [
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
        ],
        commitTime: '2022-02-15 20:12:27',
        firstCommit: true,
        vedioUrl: [
          {
            videoUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            coverUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          },
        ],
      },
    ],
    expected: {
      combineUrls: [
        {
          title: '2022年02月15日 20:12 记录',
          urlItems: [
            {
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
            {
              imageUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
        },
        {
          title: '2022年02月15日 20:12 补充',
          urlItems: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
            {
              imageUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
        },
      ],
      pureImageList: [
        {
          imageUrl:
            'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
        },
        {
          imageUrl:
            'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
        },
      ],
    },
  },
  {
    damageImgList: [],
    expected: {
      combineUrls: [],
      pureImageList: [],
    },
  },
];

export const getMapVioLationAndDamageDescMockMap = [
  {
    state: {
      OrderDetail: {
        violationList: [],
        vehicleDamageList: [],
      },
    },
    isCtripIsd: true,
    expected:
      '若有违章，车行一般会在还车后的15个工作日左右收到未处理的违章信息，请持续关注；',
  },
  {
    state: {
      OrderDetail: {
        violationList: [
          {
            penaltyPoint: 2,
          },
        ],
        vehicleDamageList: [
          {
            penaltyPoint: 2,
          },
        ],
      },
    },
    isCtripIsd: true,
    expected: null,
  },
  {
    state: {
      OrderDetail: {
        violationList: [
          {
            penaltyPoint: 2,
          },
        ],
        vehicleDamageList: [
          {
            penaltyPoint: 2,
          },
        ],
      },
    },
    isCtripIsd: false,
    expected: '已查询到1条违章记录。\n已查询到1条车损记录。',
  },
  {
    state: {
      OrderDetail: {
        violationList: [],
        vehicleDamageList: [],
      },
    },
    isCtripIsd: false,
    expected:
      '若有违章，车行一般会在还车后的15个工作日左右收到未处理的违章信息，请持续关注；暂无车损记录。',
  },
  {
    state: {
      OrderDetail: {
        violationList: [
          {
            penaltyPoint: 2,
            penaltyAmount: 200,
            carModelName: '别克凯越',
            carNo: '沪ASK293',
            occurrenceTime: '2018-01-25 10:18',
            location: '苏虹路申虹路西约100米',
            behavior:
              '机动车违反规定停放、临时停车且驾驶人虽在现场但是拒绝驶离',
            payStatus: 1,
            imgLst: [
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
            ],
          },
        ],
        vehicleDamageList: [],
      },
    },
    isCtripIsd: false,
    expected: '已查询到1条违章记录。\n暂无车损记录。',
  },
];

export const getHomeParamFromOrderMockMap = [
  {
    state: {
      OrderDetail: {
        vendorInfo: {
          vendorName: '鹏琛租车',
          vendorImageUrl: 'http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg',
          vendorID: 62494,
          vendorConfirmCode: '3082488752',
          isSelf: false,
          selfName: '',
          vendorMobileImageUrl: '',
          commentInfo: {
            vendorGoodType: 0,
            exposedScore: 0,
            topScore: 5,
            level: '',
            commentCount: 2,
            hasComment: 0,
          },
        },
        pickupStore: {
          localDateTime: '2021-06-20 10:00:00',
          storeName: '乌鲁木齐机场店',
          storeCode: '61973',
          storeAddress:
            '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
          longitude: 87.578292,
          latitude: 43.82205,
          storeTel: '13601777729,13681604559',
          storeOpenTimeDesc: '{"":"24小时营业"}',
          cityName: '乌鲁木齐',
          provinceName: '新疆',
          countryName: 'China',
          fromTime: '00:00',
          toTime: '23:59',
          cityId: 39,
          storeSerivceName: '取车门店',
          userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
          userLongitude: 87.615364,
          userLatitude: 43.798958,
          serviceType: '0',
          serviceDetails: ['自行前往门店取车'],
          addrTypeName: '取车门店',
          storeID: 128348,
          commentCount: 0,
          pickUpOffLevel: 0,
          sendTypeForPickUpOffCar: 0,
        },
        returnStore: {
          localDateTime: '2021-06-22 10:00:00',
          storeName: '乌鲁木齐机场店',
          storeCode: '61973',
          storeAddress:
            '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
          longitude: 87.578292,
          latitude: 43.82205,
          storeTel: '13601777729,13681604559',
          storeOpenTimeDesc: '{"":"24小时营业"}',
          cityName: '乌鲁木齐',
          provinceName: '新疆',
          countryName: 'China',
          fromTime: '00:00',
          toTime: '23:59',
          cityId: 39,
          storeSerivceName: '还车门店',
          userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
          userLongitude: 87.615364,
          userLatitude: 43.798958,
          serviceType: '0',
          serviceDetails: ['自行前往门店还车'],
          addrTypeName: '还车门店',
          storeID: 128348,
          commentCount: 0,
          pickUpOffLevel: 0,
          sendTypeForPickUpOffCar: 0,
        },
      },
    },
    expected: {
      issendcar: 2,
      ispickupcar: 2,
      pcid: '39',
      rcid: '39',
      pcname: '乌鲁木齐',
      rcname: '乌鲁木齐',
      poiinfo: {
        addr: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
        lat: '43.798958',
        lng: '87.615364',
      },
      rpoiinfo: {
        addr: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
        lat: '43.798958',
        lng: '87.615364',
      },
      ptime: '2021/06/20 10:00:00',
      rtime: '2021/06/22 10:00:00',
    },
  },
  {
    state: {
      OrderDetail: {
        vendorInfo: {
          vendorName: '鹏琛租车',
          vendorImageUrl: 'http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg',
          vendorID: 9787,
          vendorConfirmCode: '3082488752',
          isSelf: false,
          selfName: '',
          vendorMobileImageUrl: '',
          commentInfo: {
            vendorGoodType: 0,
            exposedScore: 0,
            topScore: 5,
            level: '',
            commentCount: 2,
            hasComment: 0,
          },
        },
        pickupStore: {
          localDateTime: '2021-06-20 10:00:00',
          storeName: '乌鲁木齐机场店',
          storeCode: '61973',
          storeAddress:
            '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
          longitude: 87.578292,
          latitude: 43.82205,
          storeTel: '13601777729,13681604559',
          storeOpenTimeDesc: '{"":"24小时营业"}',
          cityName: '乌鲁木齐',
          provinceName: '新疆',
          countryName: 'China',
          fromTime: '00:00',
          toTime: '23:59',
          cityId: 39,
          storeSerivceName: '取车门店',
          userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
          userLongitude: 87.615364,
          userLatitude: 43.798958,
          serviceType: 2,
          serviceDetails: ['自行前往门店取车'],
          addrTypeName: '取车门店',
          storeID: 128348,
          commentCount: 0,
          pickUpOffLevel: 0,
          sendTypeForPickUpOffCar: 0,
        },
        returnStore: {
          localDateTime: '2021-06-22 10:00:00',
          storeName: '乌鲁木齐机场店',
          storeCode: '61973',
          storeAddress:
            '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
          longitude: 87.578292,
          latitude: 43.82205,
          storeTel: '13601777729,13681604559',
          storeOpenTimeDesc: '{"":"24小时营业"}',
          cityName: '乌鲁木齐',
          provinceName: '新疆',
          countryName: 'China',
          fromTime: '00:00',
          toTime: '23:59',
          cityId: 39,
          storeSerivceName: '还车门店',
          userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
          userLongitude: 87.615364,
          userLatitude: 43.798958,
          serviceType: 2,
          serviceDetails: ['自行前往门店还车'],
          addrTypeName: '还车门店',
          storeID: 128348,
          commentCount: 0,
          pickUpOffLevel: 0,
          sendTypeForPickUpOffCar: 0,
        },
      },
    },
    expected: {
      issendcar: 1,
      ispickupcar: 1,
      pcid: '39',
      rcid: '39',
      pcname: '乌鲁木齐',
      rcname: '乌鲁木齐',
      poiinfo: {
        addr: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
        lat: '43.798958',
        lng: '87.615364',
      },
      rpoiinfo: {
        addr: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
        lat: '43.798958',
        lng: '87.615364',
      },
      ptime: '2021/06/20 10:00:00',
      rtime: '2021/06/22 10:00:00',
    },
  },
];

export const getCancelReasonListsMockMap = [
  {
    state: {
      OrderDetail: {
        orderCancelInfo: {
          cancelReasonList: [],
        },
      },
    },
    expected: [],
  },
];

export const getCarRentalMustReadToViolationRulesMock = [
  {
    data: [
      {
        title: '租车及违章押金相关',
        type: -3,
        code: '2',
        subObject: [
          {
            title: '车损外观损伤（修复或更换）费用标准',
            content: [
              '车辆外观损伤修复费用和车辆外观件更换费均需设置为一口价费用标准=维修/更换费用 +拆装费用 + 工时费用车辆外观损伤修复费用标准（满足下列任一条件就收费）1.漆面划伤触摸有凹槽且损伤长度在5cm以上2.漆面划伤触摸有凹槽且宽度0.5cm以上3.外观凹陷且面积4cm²以上4.外观隆起且面积4cm²以上车辆外观损伤修复费用标准：',
              '',
              '<table>CarDamage1',
              '车辆外观件更换费用标准（满足下列任一条件就收费）1.外观出现穿孔 2.外观出现开裂 3.外观件脱落 4.外观件遗失 车辆外观件更换费用标准：',
              '',
              '<table>CarDamage3',
              '1. 一项以上的外观或部件损伤，需叠加计算的总费用为车辆维修费。2. 如部件缺失或不可修复，除按上表支付修复费用外，还需按4S店价格另外支付备件费用。3. 钥匙仅在出现金属钥匙主体断裂时并且客户提供了完整的损坏钥匙的情况下，进行补配机械钥匙，如果钥匙丢失等情况，按照更换全车锁价格收取。',
            ],
            type: 52,
            code: '2',
            contentObject: [
              {
                stringObjs: [
                  {
                    content:
                      '车辆外观损伤修复费用和车辆外观件更换费均需设置为一口价费用标准=维修/更换费用 +拆装费用 + 工时费用车辆外观损伤修复费用标准（满足下列任一条件就收费）1.漆面划伤触摸有凹槽且损伤长度在5cm以上2.漆面划伤触摸有凹槽且宽度0.5cm以上3.外观凹陷且面积4cm²以上4.外观隆起且面积4cm²以上车辆外观损伤修复费用标准：',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content: '',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content: '<table>CarDamage1',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content:
                      '车辆外观件更换费用标准（满足下列任一条件就收费）1.外观出现穿孔 2.外观出现开裂 3.外观件脱落 4.外观件遗失 车辆外观件更换费用标准：',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content: '',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content: '<table>CarDamage3',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content:
                      '1. 一项以上的外观或部件损伤，需叠加计算的总费用为车辆维修费。2. 如部件缺失或不可修复，除按上表支付修复费用外，还需按4S店价格另外支付备件费用。3. 钥匙仅在出现金属钥匙主体断裂时并且客户提供了完整的损坏钥匙的情况下，进行补配机械钥匙，如果钥匙丢失等情况，按照更换全车锁价格收取。',
                  },
                ],
              },
            ],
          },
          {
            title: '随车物品损失',
            content: [
              '车内随车设备和物品不在保险公司赔付范围内，车辆租期如内有遗失或损坏，需要照价赔偿，\n请参照《随车物品清单及价格表》或当地 4S 店价格进行赔偿。（豪华进口车的随车物品按门\n店或 4s 店实际报价为准）\n随车物品清单及价格表:',
              '',
              '<table>BelongingsCost1',
              '若用车中发生车辆钥匙丢失，租车公司需更换全车锁，请立即联系客服或门店，实际费用按 \n具体车型及市场价浮动。\n其他损失，按租车合同及相关法规协商处理。',
            ],
            type: 53,
            code: '2',
            contentObject: [
              {
                stringObjs: [
                  {
                    content:
                      '车内随车设备和物品不在保险公司赔付范围内，车辆租期如内有遗失或损坏，需要照价赔偿，\n请参照《随车物品清单及价格表》或当地 4S 店价格进行赔偿。（豪华进口车的随车物品按门\n店或 4s 店实际报价为准）\n随车物品清单及价格表:',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content: '',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content: '<table>BelongingsCost1',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content:
                      '若用车中发生车辆钥匙丢失，租车公司需更换全车锁，请立即联系客服或门店，实际费用按 \n具体车型及市场价浮动。\n其他损失，按租车合同及相关法规协商处理。',
                  },
                ],
              },
            ],
          },
          {
            title: '违章处理违约金',
            content: ['违章处理违约金内容'],
            type: 54,
            code: '2',
            contentObject: [
              {
                stringObjs: [
                  {
                    content: '违章处理违约金内容',
                  },
                ],
              },
            ],
          },
          {
            title: '车辆停运损失费',
            content: [
              '是否收车辆停运损失费：是\n对于因车辆事故，或因驾车过程中操作不当造成的车辆故障等影响车辆正常营运，将收取停\n运损失费。对已购“优享服务”客人正常收取停运费。\n根据车辆所需维修天数，每天按车辆当日订单金额（包含基本保险，但不包含手续费及附加\n服务费）收取 100%停运费。\n因第三方全责导致的车损，不收取客人的停运费及折旧费。\n车损维修-停运天数参照表',
              '',
              '<table>StopLossFee1',
              '1、若车辆维修时间超过 30 天，则损失核定由门店工作人员、4S 店专业人士、保险公司定损员共同界定事故级\n别，核定最终停运损失\n2、上述条款不适用于生产工艺以及车辆零配件罕有或需进口后才能维修的车辆，其停运损失根据实际情况而定。',
            ],
            type: 43,
            code: '2',
            contentObject: [
              {
                stringObjs: [
                  {
                    content:
                      '是否收车辆停运损失费：是\n对于因车辆事故，或因驾车过程中操作不当造成的车辆故障等影响车辆正常营运，将收取停\n运损失费。对已购“优享服务”客人正常收取停运费。\n根据车辆所需维修天数，每天按车辆当日订单金额（包含基本保险，但不包含手续费及附加\n服务费）收取 100%停运费。\n因第三方全责导致的车损，不收取客人的停运费及折旧费。\n车损维修-停运天数参照表',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content: '',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content: '<table>StopLossFee1',
                  },
                ],
              },
              {
                stringObjs: [
                  {
                    content:
                      '1、若车辆维修时间超过 30 天，则损失核定由门店工作人员、4S 店专业人士、保险公司定损员共同界定事故级\n别，核定最终停运损失\n2、上述条款不适用于生产工艺以及车辆零配件罕有或需进口后才能维修的车辆，其停运损失根据实际情况而定。',
                  },
                ],
              },
            ],
          },
          {
            title: '车辆折旧费标准',
            content: [
              '根据车辆受损情况及保险公司定损金额，维修费在0-5000元（含）间，不收取折旧费；维修费在5000元以上，收取承租车辆维修费总额的20%作为折旧费。因第三方全责导致的车损，不收取用户/承租人的停运费及折旧费（因用户/承租人原因导致无法收取的除外）。',
            ],
            type: 14,
            code: '2',
            contentObject: [
              {
                stringObjs: [
                  {
                    content:
                      '根据车辆受损情况及保险公司定损金额，维修费在0-5000元（含）间，不收取折旧费；维修费在5000元以上，收取承租车辆维修费总额的20%作为折旧费。因第三方全责导致的车损，不收取用户/承租人的停运费及折旧费（因用户/承租人原因导致无法收取的除外）。',
                  },
                ],
              },
            ],
          },
          {
            title: '租车押金退还标准',
            content: [''],
            type: 56,
            code: '2',
            contentObject: [
              {
                stringObjs: [
                  {
                    content: '',
                  },
                ],
              },
            ],
          },
        ],
        sortNum: 4,
      },
    ],
    expected: '违章处理违约金内容',
  },
];
