import { getVehicleLabels } from './../../../src/pages/xcar/State/OrderDetail/Method';
describe('getPhoneMenus', () => {
  const mockStateMap = [
    {
      vehicle: {
        hasAC: true,
        transmission: 'MT',
        luggageNum: 2,
      },
      expected: [
        { text: '手动挡', icon: { iconContent: '&#xee84;' } },
        { text: 'A/C', icon: { iconContent: '&#xee7c;' } },
        {
          text: '2个24寸行李箱',
          rightIcon: { iconContent: '&#xe010;' },
          type: 'luggage',
        },
      ],
    },
    {
      vehicle: {
        hasAC: false,
        transmission: 'MT',
        luggageNum: 2,
      },
      expected: [
        { text: '手动挡', icon: { iconContent: '&#xee84;' } },
        {
          text: '2个24寸行李箱',
          rightIcon: { iconContent: '&#xe010;' },
          type: 'luggage',
        },
      ],
    },
    {
      vehicle: {
        hasAC: false,
        transmission: 'AT',
        luggageNum: 2,
      },
      expected: [
        { text: '自动挡', icon: { iconContent: '&#xee7e;' } },
        {
          text: '2个24寸行李箱',
          rightIcon: { iconContent: '&#xe010;' },
          type: 'luggage',
        },
      ],
    },
  ];
  test.each(mockStateMap)('车型标签', ({ vehicle, expected }) => {
    const data = getVehicleLabels(vehicle);

    expect(data).toEqual(expected);
  });
});
