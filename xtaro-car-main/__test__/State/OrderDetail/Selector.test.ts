import dayjs from '../../../src/pages/xcar/Common/src/Dayjs/src';
import {
  getOperationButtons,
  getISDRenewButton,
  getContinuePayButton,
  getContinuePayTick,
  getCarServiceData,
  getOrderMessageCardStatus,
  getNextStorageCardsTitle,
  getIsShowMessageAssistantBtn,
  getGuaranteeData,
  getModifyCancelRule,
  getModifyTip,
  getCarLabelsInfo,
  getMiddlePayVendorId,
  getInsExtend,
  getFeeDetailData,
  getOrderCashBackInfo,
  getOrderBuriedPointData,
  isOnlinePreAuth,
  getServiceTags,
  getNationalChainTag,
  getRestAssuredTag,
  getRenewalSuccess,
  getIsModifyOrderAddPayment,
  getModifyOrderAddPayment,
  getDepositPaymentModalAutoShow,
  filterCouponsStatus,
  getDamageInfoRenderData,
  getOnlyVehicleDamageId,
  hadBuyInsurances,
  getDiffInsurance,
  getisdInsData,
  queryContinuePayParams,
  getDepositPaymentParams,
  getPaymentParams,
  getElsePaymentParams,
  getRentalPolicyParams,
  getBookingNoticeParams,
  updateFreeDepositInfoParams,
  packagePriceDetailModalData,
  getXProductDatas,
  cancelReasonTipAboutDeposit,
  getInstructionsAnchor,
  getLocationType,
  getStoreAttendant,
  supportModifyOrder,
  getCommentButtonInfo,
  getDateLocation,
  getProductComfrimLocation,
  getFreeDeposit,
  selectOsdDeductionList,
  getCurrentOsdDeductionDetail,
  getCurrentDeductionFee,
  getCurrentDepositDetailFeeProcess,
  getCurrentVehicleDamageImgList,
  getOrderDetailResponse,
  getCrossPolicy,
  getIsShowTravelLimit,
  getTravelLimitSelectedResult,
  getIsNewCancelRule,
  getCancelRules,
  getCancelTip,
  getDriverLicenseOrdersEnities,
  getDriverLicensePageNo,
  getNextPageOffset,
  getIsLastPage,
  getPenaltyChangeCancelTip,
} from '../../../src/pages/xcar/State/OrderDetail/Selectors';
import { Utils } from '../../../src/pages/xcar/Util/Index';
import {
  queryContinuePayParamsMockMap,
  getDepositPaymentParamsMockMap,
  getFeeDetailDataMockMap,
  getDiffInsuranceMockMap,
  getIsShowMessageAssistantBtnMockMap,
  getPaymentParamsMockMap,
  licenseNoAndColorMockMap,
  getRentalPolicyParamsMockMap,
  getBookingNoticeParamsMockMap,
  updateFreeDepositInfoParamsMockMap,
  packagePriceDetailModalDataMockMap,
  getCarLabelsInfoMockMap,
  getXProductDatasMap,
  cancelReasonTipAboutDepositMockMap,
  getInstructionsAnchorMockMap,
  getSupportModifyOrderMockMap,
} from './StoreMocks';

jest.mock('../../../src/pages/xcar/State/ModifyOrder/Selector', () => jest.fn());
jest.mock('../../../src/pages/xcar/State/OnlineAuth/CommonSelectors', () => {
  return {
    selectSupportInfo: () => ({
      authStatus: 0,
      certificationStatus: 200,
      defaultAuthStatus: false,
      isShow: true,
      showTitle: '身份证及驾照在线认证',
      buttonText: '去授权',
      guideText: '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
    }), // 解决循环引用
  };
});

jest.mock('../../../src/pages/xcar/State/OrderDetail/Mappers', () => {
  return {
    getCancelRuleData: jest.fn(),
    getEasyLifeData: jest.fn(),
    hasCreditBothFree: jest.fn(),
    hasCreditOrZhimaFree: jest.fn(),
  };
});

test('Simple Selector Check', () => {
  const state = {
    OrderDetail: {
      extendedInfo: {
        locationType: 1,
        storeAttendant: {},
      },
    },
  };
  const needCheckFunc = [getLocationType, getStoreAttendant];
  needCheckFunc.forEach(selector => {
    expect(selector(state)).toBeTruthy();
  });
});

describe('测试获取订详按钮正确获取', () => {
  // state mock 配置数据，用于遍历执行 case
  const mockISDStateMap = [
    {
      state: {
        OrderDetail: {
          orderBaseInfo: {
            allOperations: [
              {
                operationId: 1,
                buttonName: '继续支付',
                enable: false,
                display: null,
              },
              {
                operationId: 3,
                buttonName: '去点评',
                enable: false,
                display: null,
              },
              {
                operationId: 4,
                buttonName: '再次预订',
                enable: true,
                display: null,
              },
            ],
          },
        },
      },
      expected: 1,
    },
    {
      state: {
        OrderDetail: {
          orderBaseInfo: {
            allOperations: [
              {
                operationId: 1,
                buttonName: '继续支付',
                enable: false,
                display: null,
              },
              {
                operationId: 3,
                buttonName: '去点评',
                enable: false,
                display: null,
              },
              {
                operationId: 4,
                buttonName: '再次预订',
                enable: true,
                display: null,
              },
              {
                operationId: 2,
                buttonName: '取消订单',
                enable: true,
                display: 'none',
              },
              {
                operationId: 7,
                buttonName: '修改订单',
                enable: true,
                display: null,
              },
            ],
          },
        },
      },
      expected: 2,
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: 0,
    },
  ];

  const mockOSDStateMap = [
    {
      state: {
        OrderDetail: {
          orderBaseInfo: {
            allOperations: [
              {
                operationId: 1,
                buttonName: '继续支付',
                enable: false,
                display: null,
              },
              {
                operationId: 3,
                buttonName: '去点评',
                enable: false,
                display: null,
              },
              {
                operationId: 4,
                buttonName: '再次预订',
                enable: true,
                display: null,
              },
            ],
          },
        },
      },
      expected: 1,
    },
    {
      state: {
        OrderDetail: {
          orderBaseInfo: {
            allOperations: [
              {
                operationId: 1,
                buttonName: '继续支付',
                enable: false,
                display: null,
              },
              {
                operationId: 3,
                buttonName: '去点评',
                enable: false,
                display: null,
              },
              {
                operationId: 4,
                buttonName: '再次预订',
                enable: true,
                display: null,
              },
              {
                operationId: 2,
                buttonName: '取消订单',
                enable: true,
                display: 'none',
              },
              {
                operationId: 7,
                buttonName: '修改订单',
                enable: true,
                display: null,
              },
            ],
          },
        },
      },
      expected: 3,
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: 0,
    },
  ];
  test.each(mockISDStateMap)(
    '国内订详按钮返回数量正常',
    ({ state, expected }) => {
      // mock Utils.isCtripIsd 返回
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
      const buttons = getOperationButtons(state);
      expect(buttons.length).toEqual(expected);
    },
  );

  test.each(mockOSDStateMap)(
    '境外订详按钮返回数量正常',
    ({ state, expected }) => {
      // mock Utils.isCtripIsd 返回
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => false);
      const buttons = getOperationButtons(state);
      expect(buttons.length).toEqual(expected);
    },
  );
});

describe('getISDRenewButton', () => {
  const stateMock1 = {
    OrderDetail: {
      orderBaseInfo: {
        allOperations: [
          {
            operationId: 1,
            buttonName: '继续支付',
            enable: false,
            display: null,
          },
          {
            operationId: 3,
            buttonName: '去点评',
            enable: false,
            display: null,
          },
          {
            operationId: 11,
            buttonName: '续租',
            enable: true,
          },
        ],
      },
    },
  };
  test('getISDRenewButton 获取续租按钮', () => {
    const btn = getISDRenewButton(stateMock1);
    expect(btn?.operationId).toEqual(11);
  });

  const stateMock2 = {
    OrderDetail: {
      orderBaseInfo: {
        allOperations: [
          {
            buttonName: '继续支付',
            enable: false,
            display: null,
          },
        ],
      },
    },
  };
  test('getISDRenewButton 异常状态', () => {
    const btn = getISDRenewButton(stateMock2);
    expect(btn?.operationId).toEqual(undefined);
  });
});

describe('getContinuePayButton', () => {
  const stateMock1 = {
    OrderDetail: {
      orderBaseInfo: {
        allOperations: [
          {
            operationId: 1,
            buttonName: '继续支付',
            enable: true,
            display: null,
          },
          {
            operationId: 3,
            buttonName: '去点评',
            enable: false,
            display: null,
          },
          {
            operationId: 11,
            buttonName: '续租',
            enable: true,
          },
        ],
      },
    },
  };
  test('getContinuePayButton 获取继续支付按钮', () => {
    const btn = getContinuePayButton(stateMock1);
    expect(btn?.operationId).toEqual(1);
  });

  const stateMock2 = {
    OrderDetail: {
      orderBaseInfo: {
        allOperations: [
          {
            buttonName: '继续支付',
            enable: false,
            display: null,
          },
        ],
      },
    },
  };
  test('getContinuePayButton 异常状态', () => {
    const btn = getContinuePayButton(stateMock2);
    expect(btn?.operationId).toEqual(undefined);
  });
});

describe('getContinuePayTick', () => {
  const stateMock1 = {
    OrderDetail: {
      orderBaseInfo: {
        // 当前时间减去10s
        lastEnablePayTime: dayjs().subtract(10, 'second').valueOf(),
        orderStatus: 0,
      },
    },
  };
  test('getContinuePayTick 测试已过最晚支付时间', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const timeData = getContinuePayTick(stateMock1);
    expect(timeData.visible).toEqual(false);
    expect(timeData.minute).toEqual(0);
    expect(timeData.second).toEqual(0);
  });

  const stateMock2 = {
    OrderDetail: {
      orderBaseInfo: {
        lastEnablePayTime: dayjs().add(10, 'second').valueOf(),
        orderStatus: 0,
      },
    },
  };

  test('getContinuePayTick 测试支付时间还剩10s', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const timeData = getContinuePayTick(stateMock2);
    expect(timeData.visible).toEqual(true);
    expect(timeData.minute).toEqual(0);
    expect(timeData.second >= 9).toEqual(true);
  });

  const stateMock3 = {
    OrderDetail: {
      continuePayInfo: {
        leftMinutes: 29,
        leftSeconds: 59,
        needContinuePay: true,
      },
    },
  };

  test('getContinuePayTick 测试境外支付时间', () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
    const timeData = getContinuePayTick(stateMock3);
    expect(timeData.visible).toEqual(true);
    expect(timeData.minute).toEqual(29);
    expect(timeData.second).toEqual(59);
  });
});

describe('getCarServiceData', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          newOrderInsAndXRes: {
            purchased: {
              name: '尊享服务',
              currentCurrencyCode: 'CNY',
              currentDailyPrice: 175,
              gapPrice: 100,
              description: [],
              allTags: [],
              insuranceDetailDescription: [],
              uniqueCode: '2011',
              status: 3,
              type: 1,
              price: 175,
            },
            purchasedSub: [
              {
                name: '尊享服务',
                currentCurrencyCode: 'CNY',
                currentDailyPrice: 175,
                gapPrice: 100,
                description: [],
                allTags: [],
                insuranceDetailDescription: [],
                uniqueCode: '2011',
                status: 3,
                type: 1,
                price: 175,
              },
            ],
            upgradeGuarantee: {
              rentalGuaranteeTitle: [],
              vendorServiceDesc:
                '车行服务是指车行为客人安全用车提供的服务，包含车辆整备（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。',
              vendorServiceSubDesc:
                '上述车损险和三者险仅覆盖保险理赔范围内的损失，理赔范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求进行理赔导致保险公司拒赔的情况，您需承担全额损失。',
              purchasingNotice: {},
              vendorServiceDetail: [],
              claimSettlementVersion: '',
            },
          },
        },
      },
      expected: {
        rentalGuaranteeV2: {
          packageDetailList: [
            {
              name: '尊享服务',
              currentCurrencyCode: 'CNY',
              currentDailyPrice: 175,
              gapPrice: 100,
              description: [],
              allTags: [],
              insuranceDetailDescription: [],
              uniqueCode: '2011',
              status: 3,
              type: 1,
              price: 175,
            },
          ],
          purchased: {
            name: '尊享服务',
            currentCurrencyCode: 'CNY',
            currentDailyPrice: 175,
            gapPrice: 100,
            description: [],
            allTags: [],
            insuranceDetailDescription: [],
            uniqueCode: '2011',
            status: 3,
            type: 1,
            price: 175,
          },
          purchasingNotice: {},
        },
        isEasyLife2024: false,
        insuranceProductTips:
          '上述车损险和三者险仅覆盖保险理赔范围内的损失，理赔范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求进行理赔导致保险公司拒赔的情况，您需承担全额损失。',
      },
    },
    {
      state: {
        OrderDetail: {
          newOrderInsAndXRes: {
            purchased: null,
            purchasedSub: null,
            upgradeGuarantee: {
              rentalGuaranteeTitle: [],
              vendorServiceDesc:
                '车行服务是指车行为客人安全用车提供的服务，包含车辆整备（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。',
              vendorServiceSubDesc:
                '上述车损险和三者险仅覆盖保险理赔范围内的损失，理赔范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求进行理赔导致保险公司拒赔的情况，您需承担全额损失。',
              purchasingNotice: {},
              vendorServiceDetail: [],
              claimSettlementVersion: '',
            },
          },
        },
      },
      expected: {
        rentalGuaranteeV2: null,
        isEasyLife2024: false,
        insuranceProductTips:
          '上述车损险和三者险仅覆盖保险理赔范围内的损失，理赔范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求进行理赔导致保险公司拒赔的情况，您需承担全额损失。',
      },
    },
    {
      state: {
        OrderDetail: {
          newOrderInsAndXRes: null,
        },
      },
      expected: {
        rentalGuaranteeV2: null,
        insuranceProductTips: undefined,
        isEasyLife2024: false,
      },
    },
  ];
  test.each(mockStateMap)('getCarServiceData check', ({ state, expected }) => {
    const data = getCarServiceData(state);
    expect(data).toEqual(expected);
  });
});

describe('getOrderMessageCardStatus', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          refundPenaltyInfo: {
            status: 1,
            title: '已提交¥30违约金退还申请',
            lastRefundTime: '2021-08-31 17:23:18',
            refundAmount: 30,
            penaltyAmount: 40,
          },
          queryCarAssistantV2Response: {
            summary: [
              {
                content: '取车必备驾驶员本人身份证+中国大陆驾照',
                style: 'background:pink',
              },
              {
                content: '了解取还车流程',
              },
            ],
            attrDto: {
              sort: 5,
              history: 'true',
            },
            isAddWarningTitle: true,
          },
          storageCardsTitle: '测试',
          orderBaseInfo: {
            allOperations: [
              {
                operationId: 1,
                buttonName: '继续支付',
                enable: false,
                display: null,
              },
              {
                operationId: 3,
                buttonName: '去点评',
                enable: false,
                display: null,
              },
              {
                operationId: 4,
                buttonName: '再次预订',
                enable: true,
                display: null,
              },
              {
                operationId: 2,
                buttonName: '取消订单',
                enable: true,
                display: 'none',
              },
              {
                operationId: 7,
                buttonName: '修改订单',
                enable: true,
                display: null,
              },
            ],
          },
        },
        Service: {
          serviceProgressList: [],
          serviceStatus: '0',
          serviceTitle: '',
          serviceDesc: '',
          serviceLatestTime: '',
          urgeServiceIds: [],
          serviceIds: '',
          serviceCardHistory: false,
        },
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
          },
          orderAuthInfo: {
            supportInfo: {
              authStatus: 0,
              certificationStatus: 200,
              defaultAuthStatus: false,
              isShow: true,
              showTitle: '身份证及驾照在线认证',
              buttonText: '去授权',
              guideText:
                '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
            },
            certificateV3List: [
              {
                isActive: true,
                isShow: true,
              },
            ],
          },
        },
        Common: {
          orderWaringInfo: {},
        },
      },
      expected: {
        currentCardsMap: [
          {
            isHistory: false,
            isShow: true,
            title: '已提交¥30违约金退还申请',
            type: 'Refund',
          },
        ],
        historyCardsMap: [],
      },
    },
    {
      state: {
        OrderDetail: {
          storageCardsTitle: '测试',
        },
        Service: null,
        OnlineAuth: null,
        Common: {
          orderWaringInfo: {},
        },
      },
      expected: {
        currentCardsMap: [],
        historyCardsMap: [],
      },
    },
  ];
  test.each(mockStateMap)(
    '测试返回当前卡片和历史卡片',
    ({ state, expected }) => {
      const data = getOrderMessageCardStatus(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getNextStorageCardsTitle', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          refundPenaltyInfo: {
            status: 1,
            title: '已提交¥30违约金退还申请',
            lastRefundTime: '2021-08-31 17:23:18',
            refundAmount: 30,
            penaltyAmount: 40,
            attrDto: {
              sort: 5,
              history: 'unknown',
            },
          },
          queryCarAssistantV2Response: {
            summary: [
              {
                content: '取车必备驾驶员本人身份证+中国大陆驾照',
                style: 'background:pink',
              },
              {
                content: '了解取还车流程',
              },
            ],
            attrDto: {
              sort: 5,
              history: 'true',
            },
            isAddWarningTitle: true,
          },
          storageCardsTitle: [],
          orderBaseInfo: {
            allOperations: [
              {
                operationId: 1,
                buttonName: '继续支付',
                enable: false,
                display: null,
              },
              {
                operationId: 3,
                buttonName: '去点评',
                enable: false,
                display: null,
              },
              {
                operationId: 4,
                buttonName: '再次预订',
                enable: true,
                display: null,
              },
              {
                operationId: 2,
                buttonName: '取消订单',
                enable: true,
                display: 'none',
              },
              {
                operationId: 7,
                buttonName: '修改订单',
                enable: true,
                display: null,
              },
            ],
          },
        },
        Service: {
          serviceProgressList: [],
          serviceStatus: '0',
          serviceTitle: '',
          serviceDesc: '',
          serviceLatestTime: '',
          urgeServiceIds: [],
          serviceIds: '',
          serviceCardHistory: false,
        },
        OnlineAuth: {
          retryingCardFace: {
            '1': false,
            '2': false,
            '3': false,
            '4': false,
          },
          orderAuthInfo: {
            supportInfo: {
              authStatus: 0,
              certificationStatus: 200,
              defaultAuthStatus: false,
              isShow: true,
              showTitle: '身份证及驾照在线认证',
              buttonText: '去授权',
              guideText:
                '您已上传过证件，可直接授权门店完成认证，到店可免部分取车手续',
              authCardHistory: 'unknown',
            },
            certificateV3List: [
              {
                isActive: true,
                isShow: true,
              },
            ],
          },
        },
        Common: {
          orderWaringInfo: {},
        },
      },
      expected: ['退款卡片'],
    },
    {
      state: {
        OrderDetail: {
          storageCardsTitle: ['标题1', '标题2'],
        },
        Service: null,
        OnlineAuth: null,
        Common: {
          orderWaringInfo: {},
        },
      },
      expected: ['标题1', '标题2'],
    },
  ];
  test.each(mockStateMap)('getNextStorageCardsTitle', ({ state, expected }) => {
    const data = getNextStorageCardsTitle(state);
    expect(data).toEqual(expected);
  });
});

describe('getIsShowMessageAssistantBtn', () => {
  test.each(getIsShowMessageAssistantBtnMockMap)(
    '测试是否展示消息助手按钮',
    ({ state, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
      const data = getIsShowMessageAssistantBtn(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getGuaranteeData', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {},
        Common: {},
      },
      expected: {
        ctripInsurances: [],
        isShowBuyButton: false,
        insuranceDesc: false,
        isOnlyShowTotalPrice: false,
      },
    },
    {
      state: {
        OrderDetail: {
          newOrderInsAndXRes: {
            insuranceAndXProductList: [
              {
                name: '人身及财物险',
                code: '2000896',
                title: '人身及财物险',
                status: 0,
                sourceFrom: 2,
              },
            ],
          },
        },
        Common: {
          qConfigResponse: {
            insuranceFlag: false,
          },
        },
      },
      expected: {
        ctripInsurances: [
          {
            name: '人身及财物险',
            code: '2000896',
            title: '人身及财物险',
            sourceFrom: 2,
            status: 0,
          },
        ],
        isShowBuyButton: true,
        insuranceDesc:
          '本模块为投保页面，由携程保险代理有限公司管理并运营。请仔细阅读投保须知等内容，并知晓承保保险公司和产品条款内容。为确保您的投保权益，您的投保信息轨迹将被记录。',
        isOnlyShowTotalPrice: true,
      },
    },
  ];
  test.each(mockStateMap)('getGuaranteeData', ({ state, expected }) => {
    const data = getGuaranteeData(state);
    expect(data).toEqual(expected);
  });
});

describe('getModifyCancelRule', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          modifyInfoDto: {
            modifyCancelRules: [
              {
                code: '1',
                content:
                  '当前订单取消预估扣除违约金¥40，可在填写新订单时申请退还',
                contentAlert: [
                  '当前订单取消预估扣除违约金¥40',
                  '可在填写新订单时申请退还，实际扣除费用以最终结果为准',
                ],
                highLight: '扣除违约金¥40',
                color: 0,
              },
              {
                code: '1',
                content: '当前订单2021-06-20 06:00前可免费取消。',
                color: 1,
              },
            ],
          },
        },
      },
      expected: {
        code: '1',
        content: '当前订单取消预估扣除违约金¥40，可在填写新订单时申请退还',
        contentAlert: [
          '当前订单取消预估扣除违约金¥40',
          '可在填写新订单时申请退还，实际扣除费用以最终结果为准',
        ],
        highLight: '扣除违约金¥40',
        color: 0,
      },
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: null,
    },
  ];
  test.each(mockStateMap)('getModifyCancelRule', ({ state, expected }) => {
    const data = getModifyCancelRule(state);
    expect(data).toEqual(expected);
  });
});

describe('getModifyTip', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          modifyInfoDto: {
            tipInfo: [
              {
                code: '4',
                content: '修改成功，用车信息及费用已更新',
              },
            ],
          },
        },
      },
      expected: '修改成功，用车信息及费用已更新',
    },
    {
      state: {
        OrderDetail: {
          modifyInfoDto: {
            tipInfo: [],
          },
        },
      },
      expected: undefined,
    },
    {
      state: {
        OrderDetail: {
          modifyInfoDto: {},
        },
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getModifyTip', ({ state, expected }) => {
    const data = getModifyTip(state);
    expect(data).toEqual(expected);
  });
});

describe('getMiddlePayVendorId', () => {
  const spy = jest.spyOn(Utils, 'isCtripOsd');
  afterEach(() => {
    jest.clearAllMocks();
  });

  const mockState1 = {
    OrderDetail: {
      vendorInfo: {
        vendorID: 30009,
        bizVendorCode: 11111,
      },
    },
  };

  test('OSD', () => {
    spy.mockImplementation(() => true);
    const data = getMiddlePayVendorId(mockState1);
    expect(data).toEqual(11111);
  });

  const mockState2 = {
    OrderDetail: {
      vendorInfo: {
        vendorID: 30009,
        bizVendorCode: 22222,
      },
    },
  };

  test('ISD', () => {
    spy.mockImplementation(() => false);
    const data = getMiddlePayVendorId(mockState2);
    expect(data).toEqual(30009);
  });
});

describe('getInsExtend', () => {
  const mockStateMap = [
    {
      apptype: 'ISD',
      state: {
        OrderDetail: {
          isdFeeInfo: {
            feeList: [
              {
                priceCode: '1001',
                priceName: '租车费',
                amount: 866,
                quantity: 2,
                descdetail: [],
              },
              {
                priceCode: '1002',
                priceName: '基础服务费',
                amount: 120,
                quantity: 2,
                descdetail: [],
              },
              {
                priceCode: '2000896',
                priceName: '人身及财物险',
                amount: 52.0,
                quantity: 1,
                descdetail: [],
              },
            ],
          },
        },
      },
      expected: {
        insuranceinfos: [{ amount: 52, currency: 'CNY', provider: 1 }],
      },
    },
    {
      apptype: 'ISD',
      state: {
        OrderDetail: {
          isdFeeInfo: {},
        },
      },
      expected: null,
    },
    {
      apptype: 'OSD',
      state: {
        OrderDetail: {},
      },
      expected: null,
    },
    {
      apptype: 'ISD',
      state: {
        OrderDetail: {},
      },
      expected: null,
    },
  ];
  test.each(mockStateMap)('getInsExtend', ({ apptype, state, expected }) => {
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => apptype == 'OSD');
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => apptype == 'ISD');
    const data = getInsExtend(state);
    expect(data).toEqual(expected);
  });
});

describe('getFeeDetailData', () => {
  test.each(getFeeDetailDataMockMap)(
    'getFeeDetailData',
    ({ state, expected }) => {
      const data = getFeeDetailData(state);
      expect(data).toHaveProperty('feeDetail.promotion', expected);
    },
  );
});

describe('getOrderCashBackInfo', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          orderDetailPrice: {
            cashBackInfoV2: {
              title: '还车后可获',
              subTitle: '平台补贴返现',
              description: '还车完成后X个工作日内自动返至「我的钱包」',
              code: 'Summary',
              type: 1,
              currencyCode: 'CNY',
              currentTotalPrice: 261,
              payMode: 2,
              items: [],
              notices: ['还车后返现¥20'],
              labels: [
                {
                  code: '1',
                  title: '还车后可获',
                  subTitle: '平台补贴返现',
                },
              ],
            },
          },
        },
      },
      expected: {
        labels: [{ code: '1', subTitle: '平台补贴返现', title: '还车后可获' }],
        notices: ['还车后返现¥20'],
        type: 1,
      },
    },
    {
      state: {
        OrderDetail: {
          orderDetailPrice: null,
        },
      },
      expected: { labels: [], notices: [], type: undefined },
    },
  ];
  test.each(mockStateMap)('getOrderCashBackInfo', ({ state, expected }) => {
    const data = getOrderCashBackInfo(state);
    expect(data).toEqual(expected);
  });
});

describe('getOrderBuriedPointData', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: '111111',
          },
          orderBaseInfo: {
            orderStatus: '1',
          },
        },
      },
      expected: {
        orderId: '111111',
        orderStatus: '1',
      },
    },
  ];
  test.each(mockStateMap)('getOrderBuriedPointData', ({ state, expected }) => {
    const data = getOrderBuriedPointData(state);
    expect(data).toEqual(expected);
  });
});

describe('isOnlinePreAuth', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          orderBaseInfo: {
            payMode: 2,
            preAuthStatus: 1,
          },
        },
      },
      expected: true,
    },
    {
      state: {
        OrderDetail: {
          orderBaseInfo: {
            payMode: 2,
            preAuthStatus: 0,
          },
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)('isOnlinePreAuth', ({ state, expected }) => {
    const data = isOnlinePreAuth(state);
    expect(data).toEqual(expected);
  });
});

describe('getCarLabelsInfo', () => {
  test.each(getCarLabelsInfoMockMap)(
    'getCarLabelsInfo',
    ({ state, expected }) => {
      const data = getCarLabelsInfo(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getServiceTags', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          labelsInfo: [
            {
              code: '3547',
              name: '两年内新车',
              desc: '行驶证注册年限小于两年。',
              marketGroupCode: 'MarketGroup1201',
              colorCode: '8',
              serviceType: true,
            },
            {
              code: '3495',
              name: '手机支架',
              desc: '车内配备有手机支架。',
              marketGroupCode: 'MarketGroup1201',
              colorCode: '1',
            },
          ],
        },
      },
      expected: [
        {
          code: '3547',
          name: '两年内新车',
          desc: '行驶证注册年限小于两年。',
          marketGroupCode: 'MarketGroup1201',
          colorCode: '8',
          serviceType: true,
          title: '两年内新车',
          category: '1',
          description: '行驶证注册年限小于两年。',
        },
      ],
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: [],
    },
  ];
  test.each(mockStateMap)('getServiceTags', ({ state, expected }) => {
    const data = getServiceTags(state);
    expect(data).toEqual(expected);
  });
});

describe('getNationalChainTag', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          labelsInfo: [
            { code: '3757', name: '全国连锁' },
            { code: '3760', name: 'APP专享价返¥10/天' },
          ],
        },
      },
      expected: { code: '3757', name: '全国连锁', title: '全国连锁' },
    },
    {
      state: {
        OrderDetail: {
          labelsInfo: [
            { code: '20', name: '全国连锁' },
            { code: '3760', name: 'APP专享价返¥10/天' },
          ],
        },
      },
      expected: { code: '20', name: '全国连锁', title: '全国连锁' },
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: { title: undefined },
    },
  ];
  test.each(mockStateMap)('getNationalChainTag', ({ state, expected }) => {
    const data = getNationalChainTag(state);
    expect(data).toEqual(expected);
  });
});

describe('getRestAssuredTag', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          labelsInfo: [
            { code: '3757', name: '全国连锁' },
            { code: '3760', name: 'APP专享价返¥10/天' },
            { code: '3764', name: '安心行' },
          ],
        },
      },
      expected: { code: '3764', name: '安心行' },
    },
  ];
  test.each(mockStateMap)('getRestAssuredTag', ({ state, expected }) => {
    const data = getRestAssuredTag(state);
    expect(data).toEqual(expected);
  });
});

describe('getRenewalSuccess', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          renewalOrders: [
            {
              renewalStatus: 'SUCCESS',
            },
          ],
        },
      },
      expected: true,
    },
    {
      state: {
        OrderDetail: {
          renewalOrders: [
            {
              renewalStatus: 'FAIL_TIMOUT',
            },
          ],
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)('getRenewalSuccess', ({ state, expected }) => {
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    const data = getRenewalSuccess(state);
    expect(data).toEqual(expected);
  });
});

describe('getIsModifyOrderAddPayment', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          additionPaymentInfo: {
            additionalPaymentList: [
              {
                orderId: 123456789,
                amount: 300,
                reasonCode: 0,
                remark: 'String',
                payStatus: 0,
                additionalPaymentId: 123566,
                payTime: 215646,
                bizScene: 4,
                createTime: 23465464,
              },
            ],
          },
        },
      },
      expected: true,
    },
    {
      state: {
        OrderDetail: {
          additionPaymentInfo: {
            additionalPaymentList: [
              {
                orderId: 123456789,
                amount: 300,
                reasonCode: 0,
                remark: 'String',
                payStatus: 1,
                additionalPaymentId: 123566,
                payTime: 215646,
                bizScene: 4,
                createTime: 23465464,
              },
            ],
          },
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getIsModifyOrderAddPayment',
    ({ state, expected }) => {
      const data = getIsModifyOrderAddPayment(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getModifyOrderAddPayment', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          additionPaymentInfo: {
            additionalPaymentList: [
              {
                orderId: 123456789,
                amount: 300,
                reasonCode: 0,
                remark: 'String',
                payStatus: 0,
                additionalPaymentId: 123566,
                payTime: 215646,
                bizScene: 4,
                createTime: 23465464,
              },
            ],
          },
        },
      },
      expected: {
        orderId: 123456789,
        amount: 300,
        reasonCode: 0,
        remark: 'String',
        payStatus: 0,
        additionalPaymentId: 123566,
        payTime: 215646,
        bizScene: 4,
        createTime: 23465464,
      },
    },
    {
      state: {
        OrderDetail: {
          additionPaymentInfo: {},
        },
      },
      expected: null,
    },
  ];
  test.each(mockStateMap)('getModifyOrderAddPayment', ({ state, expected }) => {
    const data = getModifyOrderAddPayment(state);
    expect(data).toEqual(expected);
  });
});

describe('getDepositPaymentModalAutoShow', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          freeDeposit: {
            freeDepositBtn: {
              title: '去免押',
              description: '您信用极佳，授权后可享免押',
              statusType: 1,
              auto: true,
              type: 1,
            },
          },
        },
      },
      expected: true,
    },
    {
      state: {
        OrderDetail: {
          freeDeposit: {
            freeDepositBtn: {
              title: '去免押',
              description: '您信用极佳，授权后可享免押',
              statusType: 1,
            },
          },
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getDepositPaymentModalAutoShow',
    ({ state, expected }) => {
      const data = getDepositPaymentModalAutoShow(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('filterCouponsStatus', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          couponLists: [
            {
              promoid: 991833678,
              couponNo: 'eikjahqmwd',
              title: '国内租车满减券',
              desc: '有效期至：2022-08-19',
              type: 3,
              amount: 200,
              received: true,
              startDate: '2022-08-12 00:00:00',
              endDate: '2022-08-19 23:59:59',
            },
            {
              promoid: 991833678,
              couponNo: 'esibdikvpg',
              title: '国内租车满减券',
              desc: '有效期至：2022-08-19',
              type: 3,
              amount: 200,
              received: true,
              startDate: '2022-08-12 00:00:00',
              endDate: '2022-08-19 23:59:59',
            },
          ],
        },
      },
      expected: {
        receiveds: [991833678, 991833678],
        unReceiveds: [],
      },
    },
    {
      state: {
        OrderDetail: {
          couponLists: [],
        },
      },
      expected: {
        receiveds: [],
        unReceiveds: [],
      },
    },
  ];
  test.each(mockStateMap)('filterCouponsStatus', ({ state, expected }) => {
    const data = filterCouponsStatus(state);
    expect(data).toEqual(expected);
  });
});

describe('getDamageInfoRenderData', () => {
  const osdDeductionList = [
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
        actualTitle: '实扣',
        actualTotalAmountStr: '$100,约￥10',
        feeContrast: {
          desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
          title: '费用汇率说明',
        },
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 0,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45603,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
        feeContrast: {
          desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
          title: '费用汇率说明',
        },
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45604,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45605,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
    },
  ];
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          vehicleDamageId: 1,
          vehicleDamageList: [
            {
              occurrenceTime: '2020-2-2',
              totalAmount: 2000,
              imgLstV2: [
                {
                  imgUrl: [
                    'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                  ],
                  commitTime: '2022-02-15 20:12:27',
                  firstCommit: true,
                  vedioUrl: [
                    {
                      videoUrl:
                        'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                      coverUrl:
                        'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                    },
                  ],
                },
                {
                  imgUrl: [
                    'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                  ],
                  commitTime: '2022-02-15 20:12:27',
                  firstCommit: true,
                  vedioUrl: [
                    {
                      videoUrl:
                        'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                      coverUrl:
                        'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                    },
                  ],
                },
              ],
              id: 1,
            },
          ],
        },
      },
      isCtripIsd: true,
      expected: {
        occurrenceTime: '2020-2-2',
        combineUrls: [
          {
            coverUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            isNewAdd: true,
            videoUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          },
          {
            imageUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            isNewAdd: true,
          },
          {
            coverUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            videoUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          },
          {
            imageUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          },
        ],
        pureImageList: [
          {
            imageUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          },
          {
            imageUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          },
        ],
        showArr: [
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
        ],
      },
    },
    {
      state: {
        OrderDetail: {
          vehicleDamageId: 1,
          vehicleDamageList: [
            {
              occurrenceTime: '2020-2-2',
              totalAmount: 2000,
              imgLstV2: [
                {
                  commitTime: '2022-02-15 20:12:27',
                  firstCommit: true,
                },
              ],
              id: 1,
            },
          ],
        },
      },
      isCtripIsd: true,
      expected: {
        occurrenceTime: '2020-2-2',
        combineUrls: [],
        pureImageList: [],
        showArr: [],
      },
    },
    {
      state: {
        OrderDetail: {
          vehicleDamageId: 1,
          vehicleDamageList: [
            {
              occurrenceTime: '2020-2-2',
              totalAmount: 2000,
              imgLstV2: [],
              id: 1,
            },
          ],
        },
      },
      isCtripIsd: true,
      expected: {
        occurrenceTime: '2020-2-2',
        combineUrls: [],
        pureImageList: [],
        showArr: [],
      },
    },
    {
      state: {
        OrderDetail: {
          vehicleDamageId: 0,
          vehicleDamageList: [
            {
              occurrenceTime: '2020-2-2',
              totalAmount: 2000,
              imgLstV2: [],
              id: 1,
            },
          ],
        },
      },
      isCtripIsd: true,
      expected: {
        combineUrls: [],
        pureImageList: [],
        showArr: [],
      },
    },
    {
      state: {
        OrderDetail: {
          vehicleDamageId: 45603,
          osdDeductionList,
        },
      },
      isCtripIsd: false,
      expected: {
        occurrenceTime: '2023-02-28 21:39:29',
        combineUrls: [
          {
            videoUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            coverUrl:
              'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          },
          {
            imageUrl:
              'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          },
          {
            imageUrl:
              'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          },
        ],
        pureImageList: [
          {
            imageUrl:
              'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          },
          {
            imageUrl:
              'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          },
        ],
        feeContrast: {
          desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
          title: '费用汇率说明',
        },
        deductionTypeDesc: '车损',
        showArr: [
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
        ],
      },
    },
  ];
  test.each(mockStateMap)(
    'getDamageInfoRenderData',
    ({ state, isCtripIsd, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => isCtripIsd);
      const data = getDamageInfoRenderData(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getOnlyVehicleDamageId', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          vehicleDamageId: 1,
          vehicleDamageList: [
            {
              occurrenceTime: '2020-2-2',
              totalAmount: 2000,
              imgLstV2: [
                {
                  imgUrl: [
                    'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                  ],
                  commitTime: '2022-02-15 20:12:27',
                  firstCommit: true,
                  vedioUrl: [
                    {
                      videoUrl:
                        'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                      coverUrl:
                        'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                    },
                  ],
                },
              ],
              id: 1,
            },
          ],
        },
      },
      expected: 1,
    },
    {
      state: {
        OrderDetail: {
          vehicleDamageId: 0,
          vehicleDamageList: [],
        },
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getOnlyVehicleDamageId', ({ state, expected }) => {
    const data = getOnlyVehicleDamageId(state);
    expect(data).toEqual(expected);
  });
});

describe('hadBuyInsurances', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          insuranceAndXProduct: [
            {
              name: '人身及财物险',
              code: '2000896',
              title: '人身及财物险',
              specificName: 0,
              sourceFrom: 2,
              productId: 2000896,
              insuranceOrderId: '1111111',
              requestId: '3082488749-2000896',
              orderTitle: '保险加购',
              price: 156,
              localCurrencyCode: 'CNY',
              localTotalPrice: 156,
              localDailyPrice: 156,
              currentTotalPrice: 156,
              currentDailyPrice: 156,
              currentCurrencyCode: 'CNY',
              quantity: 1,
              quantityName: '',
              maxQuantity: 1,
              group: 1,
              status: 0,
              canUpgrade: false,
              extDesc: '',
              toDetailStatus: 0,
            },
          ],
        },
      },
      expected: [
        {
          name: '人身及财物险',
          code: '2000896',
          title: '人身及财物险',
          specificName: 0,
          sourceFrom: 2,
          productId: 2000896,
          insuranceOrderId: '1111111',
          requestId: '3082488749-2000896',
          orderTitle: '保险加购',
          price: 156,
          localCurrencyCode: 'CNY',
          localTotalPrice: 156,
          localDailyPrice: 156,
          currentTotalPrice: 156,
          currentDailyPrice: 156,
          currentCurrencyCode: 'CNY',
          quantity: 1,
          quantityName: '',
          maxQuantity: 1,
          group: 1,
          status: 0,
          canUpgrade: false,
          extDesc: '',
          toDetailStatus: 0,
        },
      ],
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: [],
    },
  ];
  test.each(mockStateMap)('hadBuyInsurances', ({ state, expected }) => {
    const data = hadBuyInsurances(state);
    expect(data).toEqual(expected);
  });
});

describe('getDiffInsurance', () => {
  test.each(getDiffInsuranceMockMap)(
    'getDiffInsurance',
    ({ state, expected }) => {
      const data = getDiffInsurance(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getisdInsData', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          insuranceAndXProductDesc: [],
          insuranceAndXProduct: [
            {
              name: '人身及财物险',
              code: '2000896',
              title: '人身及财物险',
              specificName: 0,
              sourceFrom: 2,
              productId: 2000896,
              insuranceOrderId: '1111111',
              requestId: '3082488749-2000896',
              orderTitle: '保险加购',
              price: 156,
              localCurrencyCode: 'CNY',
              localTotalPrice: 156,
              localDailyPrice: 156,
              currentTotalPrice: 156,
              currentDailyPrice: 156,
              currentCurrencyCode: 'CNY',
              quantity: 1,
              quantityName: '',
              maxQuantity: 1,
              group: 1,
              status: 0,
              canUpgrade: false,
              extDesc: '',
              toDetailStatus: 0,
            },
          ],
        },
      },
      expected: [
        {
          name: '人身及财物险',
          code: '2000896',
          title: '人身及财物险',
          specificName: 0,
          sourceFrom: 2,
          productId: 2000896,
          insuranceOrderId: '1111111',
          requestId: '3082488749-2000896',
          orderTitle: '保险加购',
          price: 156,
          localCurrencyCode: 'CNY',
          localTotalPrice: 156,
          localDailyPrice: 156,
          currentTotalPrice: 156,
          currentDailyPrice: 156,
          currentCurrencyCode: 'CNY',
          quantity: 1,
          quantityName: '',
          maxQuantity: 1,
          group: 1,
          status: 0,
          canUpgrade: false,
          extDesc: '',
          toDetailStatus: 0,
        },
      ],
    },
    {
      state: {
        OrderDetail: {
          insuranceAndXProductDesc: [],
          insuranceAndXProduct: [],
        },
      },
      expected: [],
    },
  ];
  test.each(mockStateMap)('getisdInsData', ({ state, expected }) => {
    const data = getisdInsData(state);
    expect(data).toEqual(expected);
  });
});

describe('queryContinuePayParams', () => {
  test.each(queryContinuePayParamsMockMap)(
    'queryContinuePayParams',
    ({ insData, state, expected }) => {
      const data = queryContinuePayParams(state, insData);
      expect(data).toEqual(expected);
    },
  );
});

describe('getDepositPaymentParams', () => {
  test.each(getDepositPaymentParamsMockMap)(
    '获取vendorId',
    ({ state, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
      const data = getDepositPaymentParams(state);
      expect(data).toMatchObject(expected);
    },
  );
});

describe('getPaymentParams', () => {
  test.each(getPaymentParamsMockMap)('获取vendorId', ({ state, expected }) => {
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    const data = getPaymentParams(state);
    expect(data).toMatchObject(expected);
  });
});

describe('getElsePaymentParams', () => {
  test.each(getPaymentParamsMockMap)('获取vendorId', ({ state, expected }) => {
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    const data = getElsePaymentParams(
      {
        orderId: '111111',
        amount: '90',
        isInsOrder: true,
        insuranceToken: '',
        titletype: '',
        ordertitle: '',
        hideOrderPaySummary: true,
      },
      state,
    );
    expect(data).toMatchObject(expected);
  });
});

describe('getRentalPolicyParams', () => {
  test.each(getRentalPolicyParamsMockMap)(
    'getRentalPolicyParams',
    ({ state, expected }) => {
      const data = getRentalPolicyParams(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getBookingNoticeParams', () => {
  test.each(getBookingNoticeParamsMockMap)(
    'getBookingNoticeParams',
    ({ state, expected }) => {
      const data = getBookingNoticeParams(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('updateFreeDepositInfoParams', () => {
  test.each(updateFreeDepositInfoParamsMockMap)(
    'updateFreeDepositInfoParams',
    ({ state, expected }) => {
      const data = updateFreeDepositInfoParams(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('packagePriceDetailModalData', () => {
  test.each(packagePriceDetailModalDataMockMap)(
    'packagePriceDetailModalData',
    ({ state, isNewInsurance, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(isNewInsurance);
      const data = packagePriceDetailModalData(state);
      expect(data.data).toEqual(expected);
    },
  );
});

describe('getXProductDatas', () => {
  jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
  test.each(getXProductDatasMap)('getXProductDatas', ({ state, expected }) => {
    const data = getXProductDatas(state);
    expect(data[0].count).toEqual(expected);
  });
});

describe('cancelReasonTipAboutDeposit', () => {
  test.each(cancelReasonTipAboutDepositMockMap)(
    '取消原因 bothFreeDeposithInfo是否正常',
    ({ state, expected }) => {
      const data = cancelReasonTipAboutDeposit(state);
      expect(data.bothFreeDeposithInfo).toEqual(expected);
    },
  );
});

describe('getInstructionsAnchor', () => {
  test.each(getInstructionsAnchorMockMap)(
    'getInstructionsAnchor',
    ({ state, expected }) => {
      const data = getInstructionsAnchor(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('supportModifyOrder', () => {
  test.each(getSupportModifyOrderMockMap)(
    'supportModifyOrder',
    ({ state, expected }) => {
      const result = supportModifyOrder(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('getCommentButtonInfo', () => {
  const stateMock1 = {
    OrderDetail: {
      orderBaseInfo: {
        allOperations: [
          {
            operationId: 1,
            buttonName: '继续支付',
            enable: false,
            display: null,
          },
          {
            operationId: 3,
            buttonName: '去点评',
            enable: false,
            display: null,
          },
          {
            operationId: 11,
            buttonName: '续租',
            enable: true,
          },
        ],
      },
    },
  };
  test('getCommentButtonInfo 获取点评按钮', () => {
    const btn = getCommentButtonInfo(stateMock1);
    expect(btn?.operationId).toEqual(3);
  });

  const stateMock2 = {
    OrderDetail: {
      orderBaseInfo: {
        allOperations: [
          {
            buttonName: '继续支付',
            enable: false,
            display: null,
          },
        ],
      },
    },
  };
  test('getCommentButtonInfo 异常状态', () => {
    const btn = getCommentButtonInfo(stateMock2);
    expect(btn?.operationId).toEqual(undefined);
  });
});
describe('getDateLocation', () => {
  const pickupStore = {
    localDateTime: '2022-09-21 19:30:00',
    storeName: '三亚凤凰机场店',
    storeCode: '17344',
    storeAddress: '三亚凤凰国际机场停车楼4楼',
    longitude: 109.410294,
    latitude: 18.306058,
    storeTel: '0898-88639055;17336962092;17786981002',
    storeOpenTimeDesc: '{"":"24小时营业"}',
    cityName: '三亚',
    provinceName: '海南',
    countryName: 'China',
    fromTime: '00:00',
    toTime: '23:59',
    cityId: 43,
    storeSerivceName: '送车上门：',
    userAddress: '凤凰国际机场T1航站楼',
    userLongitude: 109.41201,
    userLatitude: 18.30747,
    serviceType: '16',
    serviceDetails: ['免费站内取车'],
    addrTypeName: '',
    storeID: 39450,
    commentCount: 0,
    pickUpOffLevel: 0,
    sendTypeForPickUpOffCar: 0,
    showLocation: {
      serviceTypeDesc: '免费站内取车',
      realAddress: '三亚凤凰国际机场停车楼4楼',
      longitude: '109.413683',
      latitude: '18.305902',
      oldAddressTitle: '取车地址',
    },
  };
  const returnStore = {
    localDateTime: '2022-09-23 19:30:00',
    storeName: '三亚凤凰机场店',
    storeCode: '17344',
    storeAddress: '三亚凤凰国际机场停车楼4楼',
    longitude: 109.410294,
    latitude: 18.306058,
    storeTel: '0898-88639055;17336962092;17786981002',
    storeOpenTimeDesc: '{"":"24小时营业"}',
    cityName: '三亚',
    provinceName: '海南',
    countryName: 'China',
    fromTime: '00:00',
    toTime: '23:59',
    cityId: 43,
    storeSerivceName: '免费接送',
    userAddress: '凤凰国际机场T1航站楼',
    userLongitude: 109.41201,
    userLatitude: 18.30747,
    serviceType: '16',
    serviceDetails: ['免费站内还车'],
    addrTypeName: '',
    storeID: 39450,
    commentCount: 0,
    pickUpOffLevel: 0,
    sendTypeForPickUpOffCar: 0,
    showLocation: {
      serviceTypeDesc: '免费站内还车',
      realAddress: '三亚凤凰国际机场停车楼4楼',
      longitude: '109.413683',
      latitude: '18.305902',
      oldAddressTitle: '还车地址',
    },
  };
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          pickupStore,
          returnStore,
        },
      },
      expected: {
        dropOffAddress: '三亚凤凰国际机场停车楼4楼',
        dropOffWay: '免费站内还车',
        pickupAddress: '三亚凤凰国际机场停车楼4楼',
        pickupWay: '免费站内取车',
        ptime: '2022-09-21 19:30:00',
        rtime: '2022-09-23 19:30:00',
      },
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: {
        dropOffAddress: '',
        dropOffWay: undefined,
        pickupAddress: '',
        pickupWay: undefined,
        ptime: undefined,
        rtime: undefined,
      },
    },
  ];
  test.each(mockStateMap)('getDateLocation', ({ state, expected }) => {
    const data = getDateLocation(state);
    expect(data).toEqual(expected);
  });
});

describe('getProductComfrimLocation', () => {
  const pickupStore = {
    localDateTime: '2022-09-21 19:30:00',
    storeName: '三亚凤凰机场店',
    storeCode: '17344',
    storeAddress: '三亚凤凰国际机场停车楼4楼',
    longitude: 109.410294,
    latitude: 18.306058,
    storeTel: '0898-88639055;17336962092;17786981002',
    storeOpenTimeDesc: '{"":"24小时营业"}',
    cityName: '三亚',
    provinceName: '海南',
    countryName: 'China',
    fromTime: '00:00',
    toTime: '23:59',
    cityId: 43,
    storeSerivceName: '送车上门：',
    userAddress: '凤凰国际机场T1航站楼',
    userLongitude: 109.41201,
    userLatitude: 18.30747,
    serviceType: '16',
    serviceDetails: ['免费站内取车'],
    addrTypeName: '',
    storeID: 39450,
    commentCount: 0,
    pickUpOffLevel: 0,
    sendTypeForPickUpOffCar: 0,
    showLocation: {
      serviceTypeDesc: '免费站内取车',
      realAddress: '三亚凤凰国际机场停车楼4楼',
      longitude: '109.413683',
      latitude: '18.305902',
      oldAddressTitle: '取车地址',
    },
  };
  const returnStore = {
    localDateTime: '2022-09-23 19:30:00',
    storeName: '三亚凤凰机场店',
    storeCode: '17344',
    storeAddress: '三亚凤凰国际机场停车楼4楼',
    longitude: 109.410294,
    latitude: 18.306058,
    storeTel: '0898-88639055;17336962092;17786981002',
    storeOpenTimeDesc: '{"":"24小时营业"}',
    cityName: '三亚',
    provinceName: '海南',
    countryName: 'China',
    fromTime: '00:00',
    toTime: '23:59',
    cityId: 43,
    storeSerivceName: '免费接送',
    userAddress: '凤凰国际机场T1航站楼',
    userLongitude: 109.41201,
    userLatitude: 18.30747,
    serviceType: '16',
    serviceDetails: ['免费站内还车'],
    addrTypeName: '',
    storeID: 39450,
    commentCount: 0,
    pickUpOffLevel: 0,
    sendTypeForPickUpOffCar: 0,
    showLocation: {
      serviceTypeDesc: '免费站内还车',
      realAddress: '三亚凤凰国际机场停车楼4楼',
      longitude: '109.413683',
      latitude: '18.305902',
      oldAddressTitle: '还车地址',
    },
  };
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          pickupStore,
          returnStore,
        },
      },
      expected: {
        pickup: {
          way: '免费站内取车',
          address: '三亚凤凰国际机场停车楼4楼',
          rentCenterName: '',
          rentCenterId: '',
        },
        return: {
          way: '免费站内还车',
          address: '三亚凤凰国际机场停车楼4楼',
          rentCenterName: '',
          rentCenterId: '',
        },
      },
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: {
        pickup: { address: '', rentCenterName: '', rentCenterId: '' },
        return: { address: '', rentCenterName: '', rentCenterId: '' },
      },
    },
  ];
  test.each(mockStateMap)(
    'getProductComfrimLocation',
    ({ state, expected }) => {
      const data = getProductComfrimLocation(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getFreeDeposit', () => {
  const freeDeposit = {
    depositStatus: 2,
    showDepositType: 0,
    depositExplainV2: [],
    realPayType: 0,
    freeDepositType: 10,
    preAmountForCar: 2000.0,
    preAmountForPeccancy: 1000.0,
    depositItems: [
      {
        depositTitle: '租车押金',
        deposit: 2000.0,
        depositStatus: 1,
        explain: '',
      },
      {
        depositTitle: '违章押金',
        deposit: 1000.0,
        depositStatus: 1,
        explain: '',
      },
    ],
    deductionTime: '2023-02-26 10:00',
    isBeforeNow: true,
    verifyTexts: [],
    oldAlipayCredit: 0,
    depositPayType: 5,
    tips: [],
    showFreezeDeposit: true,
    depositItemName: '押金明细',
    freeDepositProgress: {
      title: '授权及解冻进度',
      subTitle: '已扣费$300，约¥1800',
      progressInfos: [
        {
          mainText: '授权信用免押',
          subText: [
            {
              subDesc: '2023-02-28 21:26',
            },
          ],
          type: '2',
          name: '授权',
          level: '10',
          color: '1',
        },
        {
          mainText: '车损扣款预扣 $300，约¥1800',
          subText: [],
          links: [
            {
              type: '2',
              desc: '查看扣费详情',
            },
          ],
          type: '6',
          level: '20',
          color: '1',
        },
        {
          mainText: '解除信用授权',
          subText: [
            {
              subDesc: '含真实资金¥2000',
            },
          ],
          type: '8',
          name: '解冻',
          level: '10',
          color: '2',
        },
      ],
    },
    depositItemTitle: {
      title: '此单已享免押金2',
      subTitle: '$300,约￥1000',
    },
    showFreeLabel: true,
    freePreccancy: false,
  };
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          freeDeposit,
        },
      },
      expected: freeDeposit,
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getFreeDeposit', ({ state, expected }) => {
    const data = getFreeDeposit(state);
    expect(data).toEqual(expected);
  });
});

describe('selectOsdDeductionList', () => {
  const osdDeductionList = [
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
        actualTitle: '实扣',
        actualTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 0,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45603,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
      feeContrast: {
        desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
        title: '费用汇率说明',
      },
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45604,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
      feeContrast: {
        desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
        title: '费用汇率说明',
      },
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45605,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
    },
  ];
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          osdDeductionList,
        },
      },
      expected: osdDeductionList,
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('selectOsdDeductionList', ({ state, expected }) => {
    const data = selectOsdDeductionList(state);
    expect(data).toEqual(expected);
  });
});

describe('getCurrentOsdDeductionDetail', () => {
  const osdDeductionList = [
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
        actualTitle: '实扣',
        actualTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 0,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45603,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
      feeContrast: {
        desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
        title: '费用汇率说明',
      },
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45604,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
      feeContrast: {
        desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
        title: '费用汇率说明',
      },
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45605,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
    },
  ];
  const vehicleDamageId = 45603;
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          osdDeductionList,
          vehicleDamageId,
        },
      },
      expected: osdDeductionList[0],
    },
    {
      state: {
        OrderDetail: {
          osdDeductionList,
          vehicleDamageId: 0,
        },
      },
      expected: {},
    },
  ];
  test.each(mockStateMap)(
    'getCurrentOsdDeductionDetail',
    ({ state, expected }) => {
      const data = getCurrentOsdDeductionDetail(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getCurrentDeductionFee', () => {
  const osdDeductionList = [
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
        actualTitle: '实扣',
        actualTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 0,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45603,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
      feeContrast: {
        desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
        title: '费用汇率说明',
      },
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45604,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
      feeContrast: {
        desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
        title: '费用汇率说明',
      },
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45605,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
    },
  ];
  const vehicleDamageId = 45603;
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          osdDeductionList,
          vehicleDamageId,
        },
      },
      expected: osdDeductionList[0].feeInfo,
    },
    {
      state: {
        OrderDetail: {
          osdDeductionList,
          vehicleDamageId: 0,
        },
      },
      expected: [],
    },
  ];
  test.each(mockStateMap)('getCurrentDeductionFee', ({ state, expected }) => {
    const data = getCurrentDeductionFee(state);
    expect(data).toEqual(expected);
  });
});

describe('getCurrentDepositDetailFeeProcess', () => {
  const osdDeductionList = [
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
        actualTitle: '实扣',
        actualTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 0,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45603,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
      feeContrast: {
        desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
        title: '费用汇率说明',
      },
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45604,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
      feeContrast: {
        desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
        title: '费用汇率说明',
      },
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45605,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
    },
  ];
  const vehicleDamageId = 45603;
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          osdDeductionList,
          vehicleDamageId,
        },
      },
      expected: osdDeductionList[0].auditProgress,
    },
    {
      state: {
        OrderDetail: {
          osdDeductionList,
          vehicleDamageId: 0,
        },
      },
      expected: [],
    },
  ];
  test.each(mockStateMap)(
    'getCurrentDepositDetailFeeProcess',
    ({ state, expected }) => {
      const data = getCurrentDepositDetailFeeProcess(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getCurrentVehicleDamageImgList', () => {
  const osdDeductionList = [
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
        actualTitle: '实扣',
        actualTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 0,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45603,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
      feeContrast: {
        desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
        title: '费用汇率说明',
      },
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45604,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
      feeContrast: {
        desc: '费用所需的人民币金额根据当前汇率计算，实际所需人民币金额以扣款生成时汇率为准。',
        title: '费用汇率说明',
      },
    },
    {
      occurrenceTime: '2023-02-28 21:39:29',
      feeInfo: {
        estimateTitle: '预扣',
        estimateTotalAmountStr: '$100,约￥10',
      },
      auditProgress: [
        {
          name: '门店预扣费用',
          desc: '2023-10-08 10:00 扣费¥600',
          status: 2,
        },
        {
          name: '携程核实情况，确认实际损失',
          desc: '携程将对车辆情况进行跟踪核实，预计2023-10-08 10:00前完成',
          status: 2,
        },
        {
          name: '完成',
          desc: '若预扣金额大于实际损失，将自动退还超额部分',
          status: 2,
        },
      ],
      imgLstV2: [
        {
          imgUrl: [
            'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
          ],
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
          commitTime: '2023-02-28 21:39:29',
        },
      ],
      id: 45605,
      expenseTitle: '实扣$100,约￥10',
      deductionTypeDesc: '车损',
    },
  ];
  const vehicleDamageId = 45603;
  const mockStateMap = [
    // {
    //   state: {
    //     OrderDetail: {
    //       osdDeductionList,
    //       vehicleDamageId,
    //     },
    //   },
    //   isCtripIsd: false,
    //   expected: [
    //     {
    //       imgUrl: [
    //         'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
    //         'https://dimg04.c-ctrip.com/images/0yc1p12000aq1x9y71E8D.jpg',
    //       ],
    //       vedioUrl: [
    //         {
    //           videoUrl:
    //             'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
    //           coverUrl:
    //             'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
    //         },
    //       ],
    //       commitTime: '2023-02-28 21:39:29',
    //     },
    //   ],
    // },
    {
      state: {
        OrderDetail: {
          vehicleDamageId: 1,
          vehicleDamageList: [
            {
              occurrenceTime: '2020-2-2',
              totalAmount: 2000,
              imgLstV2: [
                {
                  imgUrl: [
                    'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                  ],
                  commitTime: '2022-02-15 20:12:27',
                  firstCommit: true,
                  vedioUrl: [
                    {
                      videoUrl:
                        'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                      coverUrl:
                        'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                    },
                  ],
                },
                {
                  imgUrl: [
                    'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                  ],
                  commitTime: '2022-02-15 20:12:27',
                  firstCommit: true,
                  vedioUrl: [
                    {
                      videoUrl:
                        'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                      coverUrl:
                        'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
                    },
                  ],
                },
              ],
              id: 1,
            },
          ],
        },
      },
      isCtripIsd: true,
      expected: [
        {
          imgUrl: [
            'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          ],
          commitTime: '2022-02-15 20:12:27',
          firstCommit: true,
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
        },
        {
          imgUrl: [
            'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
          ],
          commitTime: '2022-02-15 20:12:27',
          firstCommit: true,
          vedioUrl: [
            {
              videoUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
              coverUrl:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0yc101200000duhnk5172.jpg',
            },
          ],
        },
      ],
    },
  ];
  test.each(mockStateMap)(
    'getCurrentVehicleDamageImgList',
    ({ state, isCtripIsd, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => isCtripIsd);
      const data = getCurrentVehicleDamageImgList(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getOrderDetailResponse', () => {
  const response = {
    test: 1,
  };
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          response,
        },
      },
      expected: response,
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getOrderDetailResponse', ({ state, expected }) => {
    const data = getOrderDetailResponse(state);
    expect(data).toEqual(expected);
  });
});

describe('getFreeDeposit', () => {
  const freeDeposit = {
    test: 1,
  };
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          freeDeposit,
        },
      },
      expected: freeDeposit,
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getFreeDeposit', ({ state, expected }) => {
    const data = getFreeDeposit(state);
    expect(data).toEqual(expected);
  });
});

describe('getDriverLicenseOrdersEnities', () => {
  const OrderEnities = [
    {
      BizType: 'Translation',
      OrderID: 36510726975,
      OrderName: '香港驾照',
      OrderStatusCode: 'TRANSLATION_CANCLED',
      OrderStatusName: '已取消',
      Currency: 'RMB',
      OrderTotalPrice: 1000.0,
      BookingDate: '/Date(1687832153439+0800)/',
    },
  ];
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          driverLicenseOrdersEnities: OrderEnities,
        },
      },
      expected: OrderEnities,
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getDriverLicenseOrdersEnities',
    ({ state, expected }) => {
      const data = getDriverLicenseOrdersEnities(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getDriverLicensePageNo', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          driverLicensePageNo: 3,
        },
      },
      expected: 3,
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getDriverLicensePageNo', ({ state, expected }) => {
    const data = getDriverLicensePageNo(state);
    expect(data).toEqual(expected);
  });
});

describe('getNextPageOffset', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          nextPageOffset: 'XgG1IKk8S0DnzTevVczpVPUOPMJI6i7g8VThxxEEIc0=',
        },
      },
      expected: 'XgG1IKk8S0DnzTevVczpVPUOPMJI6i7g8VThxxEEIc0=',
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getNextPageOffset', ({ state, expected }) => {
    const data = getNextPageOffset(state);
    expect(data).toEqual(expected);
  });
});

describe('getIsLastPage', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          isLastPage: true,
        },
      },
      expected: true,
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getIsLastPage', ({ state, expected }) => {
    const data = getIsLastPage(state);
    expect(data).toEqual(expected);
  });
});

describe('OrderDetail Selectors getCrossPolicy', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          extendedInfo: {
            crossLocationsPolicy: {
              title: '旅行限制',
              notes: [
                '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。',
              ],
              crossLocationsInfos: [
                {
                  crossType: 1,
                  crossTypeName: '跨岛政策',
                  summaryTitle: '允许跨岛',
                  summaryPolicies: ['我是允许跨岛政策描述'],
                },
                {
                  crossType: 2,
                  crossTypeName: '跨州政策',
                  summaryTitle: '条件跨州',
                  summaryPolicies: ['我是条件跨州政策描述'],
                },
                {
                  title: '选择计划前往的国家',
                  subTitle: '门店支持在以下区域跨境使用车辆:',
                  crossType: 3,
                  crossTypeName: '跨境政策',
                  summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
                  locations: [
                    {
                      name: '日本',
                      status: 1,
                      statusName: '允许跨境',
                      firstChar: 'R',
                      regionId: '1111',
                      isSelected: false,
                    },
                    {
                      name: '俄罗斯',
                      status: 1,
                      statusName: '允许跨境',
                      firstChar: 'E',
                      regionId: '11112222',
                      isSelected: false,
                    },
                    {
                      name: '美国',
                      status: 2,
                      statusName: '条件跨境',
                      firstChar: 'M',
                      regionId: '1111222244',
                      isSelected: false,
                    },
                    {
                      name: '新加坡',
                      status: 3,
                      statusName: '不允许跨境',
                      firstChar: 'X',
                      regionId: '11112222555',
                      isSelected: false,
                      policy: '条件跨境政策1',
                    },
                    {
                      name: '越南',
                      status: 2,
                      statusName: '条件跨境',
                      firstChar: 'Y',
                      regionId: '11112222555',
                      isSelected: false,
                      policy: '条件跨境政策2',
                    },
                    {
                      name: '缅甸',
                      status: 2,
                      statusName: '条件跨境',
                      firstChar: 'M',
                      regionId: '11112222555',
                      isSelected: false,
                      policy: '条件跨境政策2',
                    },
                  ],
                },
              ],
            },
          },
        },
      },
      expected: {
        title: '旅行限制',
        notes: [
          '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。',
        ],
        crossLocationsInfos: [
          {
            crossType: 1,
            crossTypeName: '跨岛政策',
            summaryTitle: '允许跨岛',
            summaryPolicies: ['我是允许跨岛政策描述'],
          },
          {
            crossType: 2,
            crossTypeName: '跨州政策',
            summaryTitle: '条件跨州',
            summaryPolicies: ['我是条件跨州政策描述'],
          },
          {
            title: '选择计划前往的国家',
            subTitle: '门店支持在以下区域跨境使用车辆:',
            crossType: 3,
            crossTypeName: '跨境政策',
            summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
            locations: [
              {
                name: '日本',
                status: 1,
                statusName: '允许跨境',
                firstChar: 'R',
                regionId: '1111',
                isSelected: false,
              },
              {
                name: '俄罗斯',
                status: 1,
                statusName: '允许跨境',
                firstChar: 'E',
                regionId: '11112222',
                isSelected: false,
              },
              {
                name: '美国',
                status: 2,
                statusName: '条件跨境',
                firstChar: 'M',
                regionId: '1111222244',
                isSelected: false,
              },
              {
                name: '新加坡',
                status: 3,
                statusName: '不允许跨境',
                firstChar: 'X',
                regionId: '11112222555',
                isSelected: false,
                policy: '条件跨境政策1',
              },
              {
                name: '越南',
                status: 2,
                statusName: '条件跨境',
                firstChar: 'Y',
                regionId: '11112222555',
                isSelected: false,
                policy: '条件跨境政策2',
              },
              {
                name: '缅甸',
                status: 2,
                statusName: '条件跨境',
                firstChar: 'M',
                regionId: '11112222555',
                isSelected: false,
                policy: '条件跨境政策2',
              },
            ],
          },
        ],
      },
    },
  ];
  test.each(mockStateMap)('getCrossPolicy check', ({ state, expected }) => {
    const data = getCrossPolicy(state);
    expect(data).toEqual(expected);
  });
});

describe('OrderDetail Selectors getIsShowTravelLimit', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          extendedInfo: {
            crossLocationsPolicy: {
              title: '旅行限制',
              notes: [
                '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。',
              ],
              crossLocationsInfos: [
                {
                  crossType: 1,
                  crossTypeName: '跨岛政策',
                  summaryTitle: '允许跨岛',
                  summaryPolicies: ['我是允许跨岛政策描述'],
                },
                {
                  crossType: 2,
                  crossTypeName: '跨州政策',
                  summaryTitle: '条件跨州',
                  summaryPolicies: ['我是条件跨州政策描述'],
                },
                {
                  title: '选择计划前往的国家',
                  subTitle: '门店支持在以下区域跨境使用车辆:',
                  crossType: 3,
                  crossTypeName: '跨境政策',
                  summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
                  locations: [
                    {
                      name: '日本',
                      status: 1,
                      statusName: '允许跨境',
                      firstChar: 'R',
                      regionId: '1111',
                      isSelected: false,
                    },
                    {
                      name: '俄罗斯',
                      status: 1,
                      statusName: '允许跨境',
                      firstChar: 'E',
                      regionId: '11112222',
                      isSelected: false,
                    },
                    {
                      name: '美国',
                      status: 2,
                      statusName: '条件跨境',
                      firstChar: 'M',
                      regionId: '1111222244',
                      isSelected: false,
                    },
                    {
                      name: '新加坡',
                      status: 3,
                      statusName: '不允许跨境',
                      firstChar: 'X',
                      regionId: '11112222555',
                      isSelected: false,
                      policy: '条件跨境政策1',
                    },
                    {
                      name: '越南',
                      status: 2,
                      statusName: '条件跨境',
                      firstChar: 'Y',
                      regionId: '11112222555',
                      isSelected: false,
                      policy: '条件跨境政策2',
                    },
                    {
                      name: '缅甸',
                      status: 2,
                      statusName: '条件跨境',
                      firstChar: 'M',
                      regionId: '11112222555',
                      isSelected: false,
                      policy: '条件跨境政策2',
                    },
                  ],
                },
              ],
            },
          },
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)(
    'getIsShowTravelLimit check',
    ({ state, expected }) => {
      const data = getIsShowTravelLimit(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('OrderDetail Selectors getTravelLimitSelectedResult', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          travelLimitSelectedResult: [
            {
              code: '12345',
              groupCode: 'CrossPlace',
              isSelected: true,
              name: '美国',
            },
            {
              code: '123456',
              groupCode: 'CrossPlace',
              isSelected: false,
              name: '法国',
            },
          ],
        },
      },
      expected: [
        {
          code: '12345',
          groupCode: 'CrossPlace',
          isSelected: true,
          name: '美国',
        },
        {
          code: '123456',
          groupCode: 'CrossPlace',
          isSelected: false,
          name: '法国',
        },
      ],
    },
  ];
  test.each(mockStateMap)(
    'getTravelLimitSelectedResult check',
    ({ state, expected }) => {
      const data = getTravelLimitSelectedResult(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('getIsNewCancelRule', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          cancelRuleInfo: {
            cancelTip: '2021-06-20 06:00前免费取消，逾期收费。',
            reOrderTitle: '仍需用车？',
            reOrderExplain:
              '若门店车辆不满足您对颜色、牌照、配置或出行的相关需求，您可重新预订',
            cancelReasons: [
              '重新下单更优惠',
              '其他网站更便宜',
              '修改取还车信息',
              '订单未免押',
              '修改驾驶员信息',
              '行程取消无需用车',
              '门店无法提供车辆',
              '航班延误',
              '其他',
            ],
            cancelReasonsV2: [
              {
                reason: '行程变更/取消',
                code: 1,
              },
              {
                reason: '其他网站更便宜',
                code: 2,
              },
              {
                reason: '修改订单',
                code: 3,
              },
              {
                reason: '车辆有问题',
                code: 4,
              },
              {
                reason: '未带取车证件',
                code: 5,
              },
              {
                reason: '车辆限行限号',
                code: 6,
              },
              {
                reason: '门店无法提供车辆',
                code: 7,
              },
              {
                reason: '航班延误',
                code: 8,
              },
              {
                reason: '其他',
                code: 9,
              },
            ],
            cancelRules: [
              {
                freeStatus: 1,
                free: 1,
                title: '免费取消',
                context: '取车前4小时',
                time: '2021-06-20 06:00前',
                hit: true,
              },
              {
                freeStatus: 0,
                free: 0,
                title: '扣订单总金额50%',
                context: '取车前4小时-取车时间',
                time: '2021-06-20 06:00至10:00',
                hit: false,
                lossFee: '$100',
              },
              {
                freeStatus: 0,
                free: 0,
                title: '扣订单全额',
                context: '取车时间后',
                time: '2021-06-20 10:00后',
                hit: false,
                lossFee: '$200',
              },
            ],
            cancelTipColor: 1,
            bottomDesc: '因修改订单产生的“订单修改费”不退费',
            customerCurrency: 'CNY',
            osdCancelRuleInfo: {
              code: 'FreeCancel',
              complexSubTitle: {
                contentStyle: '1',
              },
              items: [
                {
                  description: '可免费取消',
                  key: '56913',
                  lossFee: 0,
                  showFree: true,
                  subTitle: '支付完成至取车前72小时',
                  title: '支付完成至2024年1月13日10:00',
                },
                {
                  description:
                    '取消将收取全部租金作为违约金，最多收取¥999,999.00',
                  key: '56914',
                  lossFee: 0,
                  showFree: false,
                  subTitle: '取车前72小时后',
                  title: '2024年1月13日10:00后',
                },
              ],
              subTitle: '支付完成至2024年1月13日10:00可免费取消',
            },
          },
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)('getIsNewCancelRule', ({ state, expected }) => {
    const data = getIsNewCancelRule(state);
    expect(data).toEqual(expected);
  });
});

describe('getCancelRules', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          cancelRuleInfo: {
            cancelTip: '2021-06-20 06:00前免费取消，逾期收费。',
            reOrderTitle: '仍需用车？',
            reOrderExplain:
              '若门店车辆不满足您对颜色、牌照、配置或出行的相关需求，您可重新预订',
            cancelReasons: [
              '重新下单更优惠',
              '其他网站更便宜',
              '修改取还车信息',
              '订单未免押',
              '修改驾驶员信息',
              '行程取消无需用车',
              '门店无法提供车辆',
              '航班延误',
              '其他',
            ],
            cancelReasonsV2: [
              {
                reason: '行程变更/取消',
                code: 1,
              },
              {
                reason: '其他网站更便宜',
                code: 2,
              },
              {
                reason: '修改订单',
                code: 3,
              },
              {
                reason: '车辆有问题',
                code: 4,
              },
              {
                reason: '未带取车证件',
                code: 5,
              },
              {
                reason: '车辆限行限号',
                code: 6,
              },
              {
                reason: '门店无法提供车辆',
                code: 7,
              },
              {
                reason: '航班延误',
                code: 8,
              },
              {
                reason: '其他',
                code: 9,
              },
            ],
            cancelRules: [
              {
                freeStatus: 1,
                free: 1,
                title: '免费取消',
                context: '取车前4小时',
                time: '2021-06-20 06:00前',
                hit: true,
              },
              {
                freeStatus: 0,
                free: 0,
                title: '扣订单总金额50%',
                context: '取车前4小时-取车时间',
                time: '2021-06-20 06:00至10:00',
                hit: false,
                lossFee: '$100',
              },
              {
                freeStatus: 0,
                free: 0,
                title: '扣订单全额',
                context: '取车时间后',
                time: '2021-06-20 10:00后',
                hit: false,
                lossFee: '$200',
              },
            ],
            cancelTipColor: 1,
            bottomDesc: '因修改订单产生的“订单修改费”不退费',
            customerCurrency: 'CNY',
            osdCancelRuleInfo: {
              code: 'FreeCancel',
              complexSubTitle: {
                contentStyle: '1',
              },
              items: [
                {
                  description: '可免费取消',
                  key: '56913',
                  lossFee: 0,
                  showFree: true,
                  subTitle: '支付完成至取车前72小时',
                  title: '支付完成至2024年1月13日10:00',
                },
                {
                  description:
                    '取消将收取全部租金作为违约金，最多收取¥999,999.00',
                  key: '56914',
                  lossFee: 0,
                  showFree: false,
                  subTitle: '取车前72小时后',
                  title: '2024年1月13日10:00后',
                },
              ],
              subTitle: '支付完成至2024年1月13日10:00可免费取消',
            },
          },
        },
      },
      expected: [
        {
          description: '可免费取消',
          key: '56913',
          lossFee: 0,
          showFree: true,
          subTitle: '支付完成至取车前72小时',
          title: '支付完成至2024年1月13日10:00',
          customerCurrency: 'CNY',
        },
        {
          description: '取消将收取全部租金作为违约金，最多收取¥999,999.00',
          key: '56914',
          lossFee: 0,
          showFree: false,
          subTitle: '取车前72小时后',
          title: '2024年1月13日10:00后',
          customerCurrency: 'CNY',
        },
      ],
    },
  ];
  test.each(mockStateMap)('getCancelRules', ({ state, expected }) => {
    const data = getCancelRules(state);
    expect(data).toEqual(expected);
  });
});

describe('getCancelTip', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          cancelRuleInfo: {
            cancelTip: '2021-06-20 06:00前免费取消，逾期收费。',
            reOrderTitle: '仍需用车？',
            reOrderExplain:
              '若门店车辆不满足您对颜色、牌照、配置或出行的相关需求，您可重新预订',
            cancelReasons: [
              '重新下单更优惠',
              '其他网站更便宜',
              '修改取还车信息',
              '订单未免押',
              '修改驾驶员信息',
              '行程取消无需用车',
              '门店无法提供车辆',
              '航班延误',
              '其他',
            ],
            cancelReasonsV2: [
              {
                reason: '行程变更/取消',
                code: 1,
              },
              {
                reason: '其他网站更便宜',
                code: 2,
              },
              {
                reason: '修改订单',
                code: 3,
              },
              {
                reason: '车辆有问题',
                code: 4,
              },
              {
                reason: '未带取车证件',
                code: 5,
              },
              {
                reason: '车辆限行限号',
                code: 6,
              },
              {
                reason: '门店无法提供车辆',
                code: 7,
              },
              {
                reason: '航班延误',
                code: 8,
              },
              {
                reason: '其他',
                code: 9,
              },
            ],
            cancelRules: [
              {
                freeStatus: 1,
                free: 1,
                title: '免费取消',
                context: '取车前4小时',
                time: '2021-06-20 06:00前',
                hit: true,
              },
              {
                freeStatus: 0,
                free: 0,
                title: '扣订单总金额50%',
                context: '取车前4小时-取车时间',
                time: '2021-06-20 06:00至10:00',
                hit: false,
                lossFee: '$100',
              },
              {
                freeStatus: 0,
                free: 0,
                title: '扣订单全额',
                context: '取车时间后',
                time: '2021-06-20 10:00后',
                hit: false,
                lossFee: '$200',
              },
            ],
            cancelTipColor: 1,
            bottomDesc: '因修改订单产生的“订单修改费”不退费',
            customerCurrency: 'CNY',
            osdCancelRuleInfo: {
              code: 'FreeCancel',
              complexSubTitle: {
                contentStyle: '1',
              },
              items: [
                {
                  description: '可免费取消',
                  key: '56913',
                  lossFee: 0,
                  showFree: true,
                  subTitle: '支付完成至取车前72小时',
                  title: '支付完成至2024年1月13日10:00',
                },
                {
                  description:
                    '取消将收取全部租金作为违约金，最多收取¥999,999.00',
                  key: '56914',
                  lossFee: 0,
                  showFree: false,
                  subTitle: '取车前72小时后',
                  title: '2024年1月13日10:00后',
                },
              ],
              subTitle: '支付完成至2024年1月13日10:00可免费取消',
            },
          },
        },
      },
      expected: '支付完成至2024年1月13日10:00可免费取消',
    },
  ];
  test.each(mockStateMap)('getCancelTip', ({ state, expected }) => {
    const data = getCancelTip(state);
    expect(data).toEqual(expected);
  });
});

describe('getNextPageOffset', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          penaltyChangeCancelTip: {
            title: '取消后不可恢复{tag}，确定取消？',
            titleSuppl: { color: 'red', text: '￥300' },
          },
        },
      },
      expected: {
        title: '取消后不可恢复{tag}，确定取消？',
        titleSuppl: { color: 'red', text: '￥300' },
      },
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getPenaltyChangeCancelTip',
    ({ state, expected }) => {
      const data = getPenaltyChangeCancelTip(state);
      expect(data).toEqual(expected);
    },
  );
});
