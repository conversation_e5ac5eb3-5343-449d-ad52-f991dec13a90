import { runSaga } from 'redux-saga';
import { xShowToast } from '@ctrip/xtaro';
import {
  CarABTesting,
  GetAB,
  CarFetch,
  CarStorage,
} from '../../../src/pages/xcar/Util/Index';
import * as Payment from '../../../src/pages/xcar/Util/Payment/Index';
import * as CommonSelectors from '../../../src/pages/xcar/State/Common/Selectors';
import {
  queryCtripContinuePay,
  apiQueryOrderStatus,
  apiOrderQueryData,
  queryOrderDetail,
  apiFetchQueryCancelFee,
  apiFetchEasyLifeTagInfo,
  apiToCancelBook,
  apiFetchQueryScoreAndSuggestions,
  apiToSubmitScoreAndSuggestions,
  queryOrderPriceInfo,
  apiBuyInsOrder,
  isdInsurancePayment,
  apiUpdateFreeDepositInfo,
  depositPayOnlineSaga,
  createOrderAdditionalPay,
  createContinuePayment,
  creditRentAuth,
  getSupplementListLogic,
  apiModifyOrder,
  handleContinuePayFail,
  getRentalMustRead,
  queryAdditionPayment,
  setRenewStatusByStorage,
  saveRenewalOrderStatus,
  queryCarAssistantV2Logic,
  apiGueryOrderInsAndXProduct,
  apiqueryOrderCoupon,
  apiOrderCashBack,
  apiFetchOrderDataByGraphql,
  queryCancelInfo,
  apiModifyCrossLocation,
  queryDriverLicenseOrders,
  queryExtraInsurance,
} from '../../../src/pages/xcar/State/OrderDetail/Logic';
import * as OrderDetailSelector from '../../../src/pages/xcar/State/OrderDetail/Selectors';
import * as GraphqlApi from '../../../src/pages/xcar/Util/CarFetch/GraphqlFetch';
import {
  QUERYCCTRIPCONTINUEPAY,
  QUERY_EXTRA_INSRUANCE,
} from '../../../src/pages/xcar/State/OrderDetail/Types';
import { recordSaga, takeEveryGeneratorFunction } from '../../testHelpers';
import Toast from '../../../src/pages/xcar/Common/src/Components/Basic/Toast/src';
import * as BbkUtils from '../../../src/pages/xcar/Common/src/Utils/src/Utils';
import * as Actions from '../../../src/pages/xcar/State/OrderDetail/Actions';
import { setInitialDataCallback } from '../../../src/pages/xcar/State/ModifyOrder/Actions';
import * as Types from '../../../src/pages/xcar/State/OrderDetail/Types';
import AppContext from '../../../src/pages/xcar/Util/AppContext';
import Utils from '../../../src/pages/xcar/Util/Utils';
import Channel from '../../../src/pages/xcar/Util/Channel';
import { put } from '@redux-saga/core/effects';
import { FETCH_LISTWARNINGINFO_CALLBACK_TASK } from '../../../src/pages/xcar/State/Common/Types';
import { fetchListWarningInfoCallBackTask } from '../../../src/pages/xcar/State/Common/Actions';
import { setOrderAuthInfo } from '../../../src/pages/xcar/State/OnlineAuth/Actions';
import { setSupplierData } from '../../../src/pages/xcar/State/SupplierData/Actions';
import { fetchMetricLog } from '../../../src/pages/xcar/Util/CarFetchHelper';
import GraphqlData from '../../../__mocks__/graphql/index.json';

jest.mock('../../../src/pages/xcar/Util/Payment/Index', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/Util/Payment/Index'),
}));

jest.mock('../../../src/pages/xcar/State/Common/Selectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/State/Common/Selectors'),
}));

jest.mock('../../../src/pages/xcar/Common/src/Utils/src/Utils', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/Common/src/Utils/src/Utils'),
}));

afterEach(() => {
  jest.clearAllMocks();
});

jest.mock('../../../src/pages/xcar/State/OrderDetail/Selectors', () => {
  const origin = jest.requireActual('../../../src/pages/xcar/State/OrderDetail/Selectors');
  return {
    ...origin,
    getAuthOrderCount: jest.fn(),
    getInsConfirmReqParam: () => false,
    getOrderModalsVisible: () => null,
    queryContinuePayParams: () => ({}),
  };
});
jest.mock('../../../src/pages/xcar/State/OrderDetail/Mappers', () => {
  const origin = jest.requireActual('../../../src/pages/xcar/State/OrderDetail/Mappers');
  return {
    ...origin,
    getRenewTipLogData: jest.fn(),
    getDepositPayOnlineParams: () => ({}),
  };
});
jest.mock('../../../src/pages/xcar/Util/CarFetchHelper', () => {
  return {
    fetchMetricLog: jest.fn(),
    parameterBuilder: jest.fn(),
  };
});
describe('queryCtripContinuePay ', () => {
  test('queryCtripContinuePay 正常运行', async () => {
    const dispatched = []; // 保存 put 返回
    const actionMock = {
      type: QUERYCCTRIPCONTINUEPAY,
      data: {},
    };

    // 获取 Generator 函数
    const logicFunc = takeEveryGeneratorFunction(queryCtripContinuePay);

    jest
      .spyOn(OrderDetailSelector, 'getInsConfirmReqParam')
      .mockImplementation(() => false);
    // @ts-ignore
    jest
      .spyOn(CommonSelectors, 'getQConfig')
      .mockReturnValue({ insuranceFlag: false });

    // mock接口返回数据
    jest.spyOn(CarFetch, 'ctripContinuePay').mockReturnValue(
      Promise.resolve({
        resultCode: '-1',
      }),
    );

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[1].data).toEqual({
      sesameRepeatOrderModal: {
        visible: true,
        data: {
          title: '如果要继续支付这笔信用免押订单，您需要去授权芝麻分',
          submitTxt: '去授权',
          cancelTxt: '再看看',
        },
      },
    });
  });
});

describe('apiQueryOrderStatus', () => {
  test('当没有缓存第一次主接口请求时间firstLoadSucTime时，直接请求订单接口', async () => {
    // 获取saga执行记录
    const queryOrderStatusAPI = jest
      .spyOn(CarFetch, 'queryOrderStatus')
      .mockReturnValue(
        Promise.resolve({
          sign: '123456',
          baseResponse: {
            isSuccess: true,
          },
        }),
      );
    const dispatched = await recordSaga(apiQueryOrderStatus, {
      action: {
        type: Types.QUERY_ORDER_STATUS,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
        },
      },
    });

    expect(queryOrderStatusAPI).toBeCalledWith({ orderId: 'actionOrderId' });
    expect(dispatched).toEqual([
      Actions.fetchOrder2({ orderId: 'actionOrderId' }),
      Actions.setOrderStatusSign({ orderStatusHashSign: '123456' }),
    ]);
  });

  test('当有第一次主接口请求时间firstLoadSucTime，请求 queryOrderStatus 接口, sign相等', async () => {
    const queryOrderStatusAPI = jest
      .spyOn(CarFetch, 'queryOrderStatus')
      .mockReturnValue(
        Promise.resolve({
          sign: '123456',
          baseResponse: {
            isSuccess: true,
          },
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiQueryOrderStatus, {
      action: {
        type: Types.QUERY_ORDER_STATUS,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          firstLoadSucTime: 'firstLoadSucTime',
          orderStatusHashSign: '123456',
          orderBaseInfo: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    expect(queryOrderStatusAPI).toBeCalledWith({ orderId: 'actionOrderId' });
  });

  test('当有订单号时，请求 queryOrderStatus 接口, sign不相等', async () => {
    const queryOrderStatusAPI = jest
      .spyOn(CarFetch, 'queryOrderStatus')
      .mockReturnValue(
        Promise.resolve({
          sign: '654321',
          baseResponse: {
            isSuccess: true,
          },
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiQueryOrderStatus, {
      action: {
        type: Types.QUERY_ORDER_STATUS,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderStatusHashSign: '123456',
          firstLoadSucTime: 'firstLoadSucTime',
          orderBaseInfo: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    expect(queryOrderStatusAPI).toBeCalledWith({ orderId: 'actionOrderId' });
    expect(dispatched).toEqual([
      Actions.setOrderStatusSign({ orderStatusHashSign: '654321' }),
      Actions.fetchOrder2({ orderId: 'actionOrderId' }),
    ]);
  });
});

describe('订单详情初始化', () => {
  test('没有OrderId', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');
    // 获取saga执行记录
    await recordSaga(apiOrderQueryData, {
      action: {
        type: Types.QUERY_ORDER_STATUS,
        data: {},
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
        },
      },
    });

    expect(ToastShow).toBeCalledWith(
      'The order number or account number is wrong',
    );
  });

  test('测试actionOrderId', async () => {
    const queryOrderFetch = jest.spyOn(CarFetch, 'queryOrder').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
      }),
    );

    // 获取saga执行记录
    await recordSaga(apiOrderQueryData, {
      action: {
        type: Types.QUERY_ORDER_STATUS,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
        },
      },
    });

    expect(queryOrderFetch).toBeCalledWith(
      expect.objectContaining({
        orderId: 'actionOrderId',
      }),
    );
  });

  test('测试urlOrderId', async () => {
    AppContext.setUrlQuery({
      orderId: 'urlOrderId',
    });

    const queryOrderFetch = jest.spyOn(CarFetch, 'queryOrder').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
      }),
    );

    // 获取saga执行记录
    await recordSaga(apiOrderQueryData, {
      action: {
        type: Types.QUERY_ORDER_STATUS,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
        },
      },
    });

    expect(queryOrderFetch).toBeCalledWith(
      expect.objectContaining({
        orderId: 'urlOrderId',
      }),
    );

    AppContext.setUrlQuery(null);
  });

  test('测试StoreOrderId', async () => {
    const queryOrderFetch = jest.spyOn(CarFetch, 'queryOrder').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
      }),
    );

    // 获取saga执行记录
    await recordSaga(apiOrderQueryData, {
      action: {
        type: Types.QUERY_ORDER_STATUS,
        data: {},
      },
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });

    expect(queryOrderFetch).toBeCalledWith(
      expect.objectContaining({
        orderId: 'storeOrderId',
      }),
    );
  });

  test('测试订单请求成功', async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest.spyOn(CarFetch, 'queryOrder').mockReturnValue(
      Promise.resolve({
        orderBaseInfo: {
          orderId: 'resOrderId',
        },
        baseResponse: {
          isSuccess: true,
        },
      }),
    );

    const callbackFunc = jest.fn();

    // 获取saga执行记录
    const dispatched = await recordSaga(apiOrderQueryData, {
      action: {
        type: Types.QUERY_ORDER_STATUS,
        data: {
          callback: callbackFunc,
        },
      },
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    const fetchOrderSuccess = dispatched.find(
      item => item.type === 'ORDER/FETCH_DATA_CALLBACK',
    );

    // expect(callbackFunc).toBeCalled();

    expect(fetchOrderSuccess).toEqual(
      Actions.fetchOrderSuccess(
        expect.objectContaining({
          queryOrderApiStatus: 1,
        }),
      ),
    );
  });

  test('测试订单请求失败', async () => {
    jest.spyOn(CarFetch, 'queryOrder').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: false,
        },
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiOrderQueryData, {
      action: {
        type: Types.QUERY_ORDER_STATUS,
        data: {},
      },
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    const fetchOrderSuccess = dispatched.find(
      item => item.type === 'ORDER/FETCH_DATA_CALLBACK',
    );
    expect(fetchOrderSuccess).toEqual(
      Actions.fetchOrderSuccess(
        expect.objectContaining({
          queryOrderApiStatus: 2,
        }),
      ),
    );
  });
});

describe('订单详情请求', () => {
  test('queryOrderDetail', async () => {
    jest.spyOn(CarFetch, 'queryOrder').mockReturnValue(
      Promise.resolve({
        orderBaseInfo: {
          orderId: 'resOrderId',
        },
        baseResponse: {
          isSuccess: true,
        },
      }),
    );

    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(BbkUtils, 'uuid').mockReturnValue('uuid');
    const callbackFunc = jest.fn();

    // 获取saga执行记录
    const dispatched = await recordSaga(queryOrderDetail, {
      action: {
        type: Types.QUERY_ORDER_DETAIL,
        data: {
          orderId: 'actionOrderId',
          callback: callbackFunc,
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
        },
      },
    });
    expect(callbackFunc).toBeCalledWith(true);
    expect(dispatched).toEqual([
      Actions.fetchOrderSuccess(
        expect.objectContaining({
          response: {
            orderBaseInfo: {
              orderId: 'resOrderId',
            },
            baseResponse: {
              isSuccess: true,
            },
          },
          request: {
            orderId: 'actionOrderId',
            channelType: Number(AppContext.MarketInfo.channelId),
            pageId: 1111,
            requestId: 'uuid',
          },
        }),
      ),
    ]);
  });

  test('queryOrderDetail', async () => {
    jest.spyOn(CarFetch, 'queryOrder').mockReturnValue(
      Promise.resolve({
        orderBaseInfo: {
          orderId: 'resOrderId',
        },
        baseResponse: {
          isSuccess: true,
        },
      }),
    );

    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(BbkUtils, 'uuid').mockReturnValue('uuid');
    const callbackFunc = jest.fn();

    // 获取saga执行记录
    const dispatched = await recordSaga(queryOrderDetail, {
      action: {
        type: Types.QUERY_ORDER_DETAIL,
        data: {
          orderId: 'actionOrderId',
          notifyModifyOrderCallback: callbackFunc,
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
        },
      },
    });
    expect(dispatched).toEqual([
      Actions.fetchOrderSuccess(
        expect.objectContaining({
          response: {
            orderBaseInfo: {
              orderId: 'resOrderId',
            },
            baseResponse: {
              isSuccess: true,
            },
          },
          request: {
            orderId: 'actionOrderId',
            channelType: Number(AppContext.MarketInfo.channelId),
            pageId: 1111,
            requestId: 'uuid',
          },
        }),
      ),
      setInitialDataCallback({
        isSuccess: true,
      }),
    ]);
  });
});

describe('apiFetchQueryCancelFee', () => {
  test('国内请求取消违约金', async () => {
    const pushFunc = jest.fn();
    AppContext.setPageInstance({
      push: pushFunc,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const queryCancelFeeFetch = jest
      .spyOn(CarFetch, 'queryCancelFee')
      .mockReturnValue(
        Promise.resolve({
          isSuccessful: true,
        }),
      );

    const cancelInfoFetch = jest.spyOn(CarFetch, 'cancelInfo').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiFetchQueryCancelFee, {
      action: {
        type: Types.FETCH_QUERYCANCELFEE,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });

    expect(queryCancelFeeFetch).toBeCalledWith({
      orderId: 'storeOrderId',
      timeout: 120,
    });

    expect(dispatched).toEqual([
      Actions.fetchQueryCancelFeeCallBack({
        resCancelFee: {
          isSuccessful: true,
        },
      }),
      Actions.queryOrderCancelInfo({
        callback: expect.anything(),
      }),
    ]);
  });
});

describe('apiFetchQueryCancelFee', () => {
  test('境外请求取消违约金', async () => {
    const pushFunc = jest.fn();
    AppContext.setPageInstance({
      push: pushFunc,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

    const queryCancelFeeFetch = jest
      .spyOn(CarFetch, 'queryCancelFee')
      .mockReturnValue(
        Promise.resolve({
          isSuccessful: true,
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiFetchQueryCancelFee, {
      action: {
        type: Types.FETCH_QUERYCANCELFEE,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });

    expect(queryCancelFeeFetch).toBeCalledWith({
      orderId: 'storeOrderId',
      timeout: 120,
    });

    expect(dispatched).toEqual([
      Actions.fetchQueryCancelFeeCallBack({
        resCancelFee: {
          isSuccessful: true,
        },
      }),
    ]);
  });
});

describe('queryCancelInfo', () => {
  test('国内取消信息接口请求', async () => {
    const pushFunc = jest.fn();
    AppContext.setPageInstance({
      push: pushFunc,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const cancelInfoFetch = jest.spyOn(CarFetch, 'cancelInfo').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(queryCancelInfo, {
      action: {
        type: Types.FETCH_QUERY_CANCEL_INFO,
        data: {
          orderId: 'actionOrderId',
          callback: () => {
            AppContext.PageInstance.push(Channel.getPageId().OrderCancel.EN, {
              visible: true,
            });
          },
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });

    expect(cancelInfoFetch).toBeCalledWith({
      orderId: 'storeOrderId',
      extraMaps: {
        osdModifyOrderVersion: '',
      },
    });

    expect(pushFunc).toBeCalledWith(Channel.getPageId().OrderCancel.EN, {
      visible: true,
    });

    expect(dispatched).toEqual([
      Actions.queryOrderCancelInfoCallBack({
        baseResponse: {
          isSuccess: true,
        },
      }),
    ]);
  });
});

describe('apiFetchEasyLifeTagInfo', () => {
  test('无忧组标签', async () => {
    const getEasyLifeTagInfoFetch = jest
      .spyOn(CarFetch, 'getEasyLifeTagInfo')
      .mockReturnValue(
        Promise.resolve({
          baseResponse: {
            isSuccess: true,
          },
          easyLifeTag: [],
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiFetchEasyLifeTagInfo, {
      action: {
        type: Types.FETCH_EASYLIFETAG,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });

    expect(getEasyLifeTagInfoFetch).toBeCalledWith({
      orderId: 'storeOrderId',
      timeout: 120,
    });

    expect(dispatched).toEqual([Actions.fetchEasyLifeTagInfoCallBack([])]);
  });

  test('空判断', async () => {
    const getEasyLifeTagInfoFetch = jest
      .spyOn(CarFetch, 'getEasyLifeTagInfo')
      .mockReturnValue(Promise.resolve(null));

    // 获取saga执行记录
    const dispatched = await recordSaga(apiFetchEasyLifeTagInfo, {
      action: {
        type: Types.FETCH_EASYLIFETAG,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });

    expect(getEasyLifeTagInfoFetch).toBeCalledWith({
      orderId: 'storeOrderId',
      timeout: 120,
    });

    expect(dispatched).toEqual([]);
  });
});

describe('apiToCancelBook', () => {
  test('取消订单成功', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');

    const cancelOSDOrderFetch = jest
      .spyOn(CarFetch, 'cancelOSDOrder')
      .mockReturnValue(
        Promise.resolve({
          baseResponse: {
            isSuccess: true,
          },
          easyLifeTag: [],
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiToCancelBook, {
      action: {
        type: Types.FETCH_EASYLIFETAG,
        data: {
          refundPenaltyAmount: '200',
          reasonCode: '0',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          cancelReason: '测试',
        },
      },
    });

    expect(cancelOSDOrderFetch).toBeCalledWith({
      orderId: 'storeOrderId',
      reason: '测试',
      refundPenaltyAmount: '200',
      reasonCode: '0',
      penaltyAmount: undefined,
      strongSubmit: {
        type: 3,
      },
    });

    expect(ToastShow).toBeCalledWith('取消成功', 2);

    expect(dispatched).toEqual([
      Actions.setCancelOrderSubmitId(''),
      Actions.fetchOrder2({}),
    ]);
  });

  test('取消订单失败', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');

    const cancelOSDOrderFetch = jest
      .spyOn(CarFetch, 'cancelOSDOrder')
      .mockReturnValue(
        Promise.resolve({
          baseResponse: {
            isSuccess: false,
          },
          easyLifeTag: [],
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiToCancelBook, {
      action: {
        type: Types.FETCH_EASYLIFETAG,
        data: {
          refundPenaltyAmount: '200',
          reasonCode: '0',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          cancelReason: '测试',
        },
      },
    });

    expect(cancelOSDOrderFetch).toBeCalledWith({
      orderId: 'storeOrderId',
      reason: '测试',
      refundPenaltyAmount: '200',
      reasonCode: '0',
      penaltyAmount: undefined,
      strongSubmit: {
        type: 3,
      },
    });

    expect(ToastShow).toBeCalledWith('取消失败，请稍后重试', 2);

    expect(dispatched).toEqual([Actions.fetchOrder2({})]);
  });
});

describe('apiFetchQueryScoreAndSuggestions', () => {
  test('apiFetchQueryScoreAndSuggestions', async () => {
    const fetchRequest = jest
      .spyOn(CarFetch, 'queryScoreAndSuggestions')
      .mockReturnValue(
        Promise.resolve({
          baseResponse: {
            isSuccess: true,
          },
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiFetchQueryScoreAndSuggestions, {
      action: {
        type: Types.QUERY_SCOREANDSUGGESTIONS,
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          cancelReason: '测试',
        },
      },
    });

    expect(fetchRequest).toBeCalledWith({
      orderId: 'storeOrderId',
      channelType: Number(AppContext.MarketInfo.channelId),
    });

    expect(dispatched).toEqual([
      Actions.queryScoreAndSuggestionsCallback({
        res: {
          baseResponse: {
            isSuccess: true,
          },
        },
      }),
    ]);
  });
});

describe('apiToSubmitScoreAndSuggestions', () => {
  test('提交成功', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');

    jest.spyOn(CarFetch, 'submitScoreAndSuggestions').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        resultCode: 1,
      }),
    );

    // 获取saga执行记录
    await recordSaga(apiToSubmitScoreAndSuggestions, {
      action: {
        type: Types.SUBMIT_SCOREANDSUGGESTIONS,
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          npsResponseParam: {
            score: '1',
            suggestions: [],
          },
        },
      },
    });
    expect(ToastShow).toBeCalledWith(
      '提交成功，感谢您的反馈',
      4,
      expect.anything(),
    );
  });

  test('提交成功', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');

    jest
      .spyOn(CarFetch, 'submitScoreAndSuggestions')
      .mockReturnValue(Promise.resolve(0));

    // 获取saga执行记录
    await recordSaga(apiToSubmitScoreAndSuggestions, {
      action: {
        type: Types.SUBMIT_SCOREANDSUGGESTIONS,
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          npsResponseParam: {
            score: '1',
            suggestions: [],
          },
        },
      },
    });
    expect(ToastShow).toBeCalledWith('提交失败，请稍后重试', 2);
  });
});

describe('queryOrderPriceInfo', () => {
  test('请求成功', async () => {
    const callbackFunc = jest.fn();
    jest.spyOn(CarFetch, 'queryOrderPriceInfo').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        feeDetailInfo: {
          test: 1,
        },
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(queryOrderPriceInfo, {
      action: {
        type: Types.QUERY_ORDER_PRICE_INFO,
        data: {
          callBack: callbackFunc,
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    expect(callbackFunc).toBeCalled();
    expect(dispatched).toEqual([
      Actions.queryOrderPriceInfoCallBack({
        orderDetailPrice: {
          test: 1,
          queryOrderDetailPriceLoaded: true,
        },
      }),
    ]);
  });
});

describe('apiBuyInsOrder', () => {
  test('手机查单直接返回', async () => {
    jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        insuranceFlag: true,
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiBuyInsOrder, {
      action: {
        type: Types.FETCH_ISDBUYINSURANCE,
        data: {
          code: '2000891',
        },
      },
      state: {
        OrderDetail: {
          authType: 1,
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    expect(dispatched).toEqual([]);
  });

  test('非人身财务险，直接跳转支付', async () => {
    jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        insuranceFlag: true,
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiBuyInsOrder, {
      action: {
        type: Types.FETCH_ISDBUYINSURANCE,
        data: {
          code: '2000891',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    expect(dispatched).toEqual([
      Actions.goIsdInsurancePayment({
        code: '2000891',
      }),
    ]);
  });

  test('人身财务险，未选择保险', async () => {
    jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        insuranceFlag: true,
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiBuyInsOrder, {
      action: {
        type: Types.FETCH_ISDBUYINSURANCE,
        data: {
          uniqueCode: '2000896',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    expect(dispatched).toEqual([]);
  });

  test('无忧尊享', async () => {
    jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        insuranceFlag: true,
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiBuyInsOrder, {
      action: {
        type: Types.FETCH_ISDBUYINSURANCE,
        data: {
          uniqueCode: '2011',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          insuranceAndXProduct: [
            {
              name: '无忧尊享',
              code: '2011',
              title: '无忧尊享',
              status: 0,
              sourceFrom: 2,
            },
          ],
        },
      },
    });
    expect(dispatched).toEqual([
      Actions.goIsdInsurancePayment({
        code: '2011',
        uniqueCode: '2011',
      }),
    ]);
  });
});

describe('isdInsurancePayment', () => {
  test('支付成功', async () => {
    jest.spyOn(CarFetch, 'ISDBuyInsOrder').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
          code: 1,
        },
        referenceNo: 'referenceNo',
        businessType: 'businessType',
        additionalId: 'additionalId',
      }),
    );
    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: true,
        showError: true,
        status: null,
        result: null,
      }),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(isdInsurancePayment, {
      action: {
        type: Types.ISDINSURANCEPAYMENT,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          driverInfo: {
            name: '测试',
            email: '',
            telphone:
              'd492e309ad4397fe9768cf2eea0e92905f1c2faca90ce24ac59052e5dbb7214d',
            areaCode: '+86',
            flightNo: '',
            iDCardType: 1,
            iDCardNo: '2103********24',
            encrypIDCardNo: '210381Ns6SnIxGrH24#',
            distributionMobile: '1561TV71116',
            distributionEmail: '',
            decryptIDCardNo: '210381199405065924',
            decryptTelphone: '15618081116',
          },
          vendorInfo: {
            vendorName: '鹏琛租车',
            vendorImageUrl:
              'http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg',
            vendorID: 62494,
            vendorConfirmCode: '3082488752',
            isSelf: false,
            selfName: '',
            vendorMobileImageUrl: '',
            commentInfo: {
              vendorGoodType: 0,
              exposedScore: 0,
              topScore: 5,
              level: '',
              commentCount: 2,
              hasComment: 0,
            },
          },
          pickupStore: {
            localDateTime: '2021-06-20 10:00:00',
            storeName: '乌鲁木齐机场店',
            storeCode: '61973',
            storeAddress:
              '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
            longitude: 87.578292,
            latitude: 43.82205,
            storeTel: '13601777729,13681604559',
            storeOpenTimeDesc: '{"":"24小时营业"}',
            cityName: '乌鲁木齐',
            provinceName: '新疆',
            countryName: 'China',
            fromTime: '00:00',
            toTime: '23:59',
            cityId: 39,
            storeSerivceName: '取车门店',
            userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
            userLongitude: 87.615364,
            userLatitude: 43.798958,
            serviceType: '0',
            serviceDetails: ['自行前往门店取车'],
            addrTypeName: '取车门店',
            storeID: 128348,
            commentCount: 0,
            pickUpOffLevel: 0,
            sendTypeForPickUpOffCar: 0,
          },
          returnStore: {
            localDateTime: '2021-06-22 10:00:00',
            storeName: '乌鲁木齐机场店',
            storeCode: '61973',
            storeAddress:
              '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
            longitude: 87.578292,
            latitude: 43.82205,
            storeTel: '13601777729,13681604559',
            storeOpenTimeDesc: '{"":"24小时营业"}',
            cityName: '乌鲁木齐',
            provinceName: '新疆',
            countryName: 'China',
            fromTime: '00:00',
            toTime: '23:59',
            cityId: 39,
            storeSerivceName: '还车门店',
            userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
            userLongitude: 87.615364,
            userLatitude: 43.798958,
            serviceType: '0',
            serviceDetails: ['自行前往门店还车'],
            addrTypeName: '还车门店',
            storeID: 128348,
            commentCount: 0,
            pickUpOffLevel: 0,
            sendTypeForPickUpOffCar: 0,
          },
        },
      },
    });
    expect(dispatched).toEqual([Actions.fetchOrder2({})]);
  });

  test('支付失败', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');
    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: false,
        showError: true,
        status: null,
        result: null,
      }),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(isdInsurancePayment, {
      action: {
        type: Types.ISDINSURANCEPAYMENT,
        data: {
          orderId: 'actionOrderId',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          driverInfo: {
            name: '测试',
            email: '',
            telphone:
              'd492e309ad4397fe9768cf2eea0e92905f1c2faca90ce24ac59052e5dbb7214d',
            areaCode: '+86',
            flightNo: '',
            iDCardType: 1,
            iDCardNo: '2103********24',
            encrypIDCardNo: '210381Ns6SnIxGrH24#',
            distributionMobile: '1561TV71116',
            distributionEmail: '',
            decryptIDCardNo: '210381199405065924',
            decryptTelphone: '15618081116',
          },
          vendorInfo: {
            vendorName: '鹏琛租车',
            vendorImageUrl:
              'http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg',
            vendorID: 62494,
            vendorConfirmCode: '3082488752',
            isSelf: false,
            selfName: '',
            vendorMobileImageUrl: '',
            commentInfo: {
              vendorGoodType: 0,
              exposedScore: 0,
              topScore: 5,
              level: '',
              commentCount: 2,
              hasComment: 0,
            },
          },
          pickupStore: {
            localDateTime: '2021-06-20 10:00:00',
            storeName: '乌鲁木齐机场店',
            storeCode: '61973',
            storeAddress:
              '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
            longitude: 87.578292,
            latitude: 43.82205,
            storeTel: '13601777729,13681604559',
            storeOpenTimeDesc: '{"":"24小时营业"}',
            cityName: '乌鲁木齐',
            provinceName: '新疆',
            countryName: 'China',
            fromTime: '00:00',
            toTime: '23:59',
            cityId: 39,
            storeSerivceName: '取车门店',
            userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
            userLongitude: 87.615364,
            userLatitude: 43.798958,
            serviceType: '0',
            serviceDetails: ['自行前往门店取车'],
            addrTypeName: '取车门店',
            storeID: 128348,
            commentCount: 0,
            pickUpOffLevel: 0,
            sendTypeForPickUpOffCar: 0,
          },
          returnStore: {
            localDateTime: '2021-06-22 10:00:00',
            storeName: '乌鲁木齐机场店',
            storeCode: '61973',
            storeAddress:
              '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
            longitude: 87.578292,
            latitude: 43.82205,
            storeTel: '13601777729,13681604559',
            storeOpenTimeDesc: '{"":"24小时营业"}',
            cityName: '乌鲁木齐',
            provinceName: '新疆',
            countryName: 'China',
            fromTime: '00:00',
            toTime: '23:59',
            cityId: 39,
            storeSerivceName: '还车门店',
            userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
            userLongitude: 87.615364,
            userLatitude: 43.798958,
            serviceType: '0',
            serviceDetails: ['自行前往门店还车'],
            addrTypeName: '还车门店',
            storeID: 128348,
            commentCount: 0,
            pickUpOffLevel: 0,
            sendTypeForPickUpOffCar: 0,
          },
        },
      },
    });
    expect(ToastShow).toBeCalledWith('支付失败');
  });
});

describe('apiUpdateFreeDepositInfo', () => {
  test('更新成功', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');

    jest.spyOn(CarFetch, 'iSDUpdateFreeDepositInfo').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        resultMsg: '成功',
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiUpdateFreeDepositInfo, {
      action: {
        type: Types.UPDATA_FREE_DEPOSITINFO,
        data: {
          freeDepositWay: 1,
          freeDepositType: 2,
          preAmountForCar: 2,
          vendorId: '11111',
          quickPayNo: '1111111',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    expect(ToastShow).toBeCalledWith('成功');
    expect(dispatched[0]).toMatchObject(
      Actions.setOrderModalsVisible({
        depositPaymentModal: {
          visible: false,
        },
      }),
    );
  });

  test('更新失败', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');

    jest.spyOn(CarFetch, 'iSDUpdateFreeDepositInfo').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: false,
        },
        resultMsg: '成功',
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiUpdateFreeDepositInfo, {
      action: {
        type: Types.UPDATA_FREE_DEPOSITINFO,
        data: {
          freeDepositWay: 1,
          freeDepositType: 2,
          preAmountForCar: 2,
          vendorId: '11111',
          quickPayNo: '1111111',
        },
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    expect(ToastShow).toBeCalledWith('出错了，请稍后重试');
  });
});

describe('depositPayOnlineSaga', () => {
  test('押金支付成功', async () => {
    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: true,
        showError: true,
        status: null,
        result: null,
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(depositPayOnlineSaga, {
      action: {
        type: Types.DEPOSIT_PAY_ONLINE,
        data: {},
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });

    expect(dispatched).toEqual([Actions.fetchOrder2()]);
  });

  test('押金支付失败', async () => {

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: false,
        showError: true,
        status: null,
        result: null,
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(depositPayOnlineSaga, {
      action: {
        type: Types.DEPOSIT_PAY_ONLINE,
        data: {},
      },
      state: {
        OrderDetail: {
          orderBaseInfo: {},
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });

    expect(xShowToast).toBeCalledWith({
      title: '支付失败',
      duration: 3000,
    });
  });
});

describe('createOrderAdditionalPay', () => {
  const storeMock = {
    OrderDetail: {
      orderBaseInfo: {},
      reqOrderParams: {
        orderId: 'storeOrderId',
      },
      driverInfo: {
        name: '测试',
        email: '',
        telphone:
          'd492e309ad4397fe9768cf2eea0e92905f1c2faca90ce24ac59052e5dbb7214d',
        areaCode: '+86',
        flightNo: '',
        iDCardType: 1,
        iDCardNo: '2103********24',
        encrypIDCardNo: '210381Ns6SnIxGrH24#',
        distributionMobile: '1561TV71116',
        distributionEmail: '',
        decryptIDCardNo: '210381199405065924',
        decryptTelphone: '15618081116',
      },
      vendorInfo: {
        vendorName: '鹏琛租车',
        vendorImageUrl: 'http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg',
        vendorID: 62494,
        vendorConfirmCode: '3082488752',
        isSelf: false,
        selfName: '',
        vendorMobileImageUrl: '',
        commentInfo: {
          vendorGoodType: 0,
          exposedScore: 0,
          topScore: 5,
          level: '',
          commentCount: 2,
          hasComment: 0,
        },
      },
      pickupStore: {
        localDateTime: '2021-06-20 10:00:00',
        storeName: '乌鲁木齐机场店',
        storeCode: '61973',
        storeAddress:
          '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
        longitude: 87.578292,
        latitude: 43.82205,
        storeTel: '13601777729,13681604559',
        storeOpenTimeDesc: '{"":"24小时营业"}',
        cityName: '乌鲁木齐',
        provinceName: '新疆',
        countryName: 'China',
        fromTime: '00:00',
        toTime: '23:59',
        cityId: 39,
        storeSerivceName: '取车门店',
        userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
        userLongitude: 87.615364,
        userLatitude: 43.798958,
        serviceType: '0',
        serviceDetails: ['自行前往门店取车'],
        addrTypeName: '取车门店',
        storeID: 128348,
        commentCount: 0,
        pickUpOffLevel: 0,
        sendTypeForPickUpOffCar: 0,
      },
      returnStore: {
        localDateTime: '2021-06-22 10:00:00',
        storeName: '乌鲁木齐机场店',
        storeCode: '61973',
        storeAddress:
          '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
        longitude: 87.578292,
        latitude: 43.82205,
        storeTel: '13601777729,13681604559',
        storeOpenTimeDesc: '{"":"24小时营业"}',
        cityName: '乌鲁木齐',
        provinceName: '新疆',
        countryName: 'China',
        fromTime: '00:00',
        toTime: '23:59',
        cityId: 39,
        storeSerivceName: '还车门店',
        userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
        userLongitude: 87.615364,
        userLatitude: 43.798958,
        serviceType: '0',
        serviceDetails: ['自行前往门店还车'],
        addrTypeName: '还车门店',
        storeID: 128348,
        commentCount: 0,
        pickUpOffLevel: 0,
        sendTypeForPickUpOffCar: 0,
      },
    },
  };
  test('补款支付成功：更新补款状态', async () => {
    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: true,
        showError: true,
        status: null,
        result: null,
      }),
    );

    jest.spyOn(CarFetch, 'createOrderAdditionalPay').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        resultMsg: '成功',
        referenceNo: 'referenceNo',
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(createOrderAdditionalPay, {
      action: {
        type: Types.ISDINSURANCEPAYMENT,
        data: {
          orderId: 'actionOrderId',
          amount: 100,
          payDeadline: '2022/09/02 10:00:00',
          bizScene: 'test',
          additionalPaymentId: 'additionalPaymentId',
          isModifyToPay: false,
        },
      },
      state: storeMock,
    });

    expect(dispatched).toEqual([
      Actions.fetchOrder2({}),
      Actions.getAdditionPayment({ orderId: 'actionOrderId' }),
      Actions.fetchOrder2({}),
    ]);
  });

  test('补款支付失败', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: false,
        showError: true,
        status: null,
        result: null,
      }),
    );

    jest.spyOn(CarFetch, 'createOrderAdditionalPay').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: false,
        },
        resultMsg: '成功',
        referenceNo: 'referenceNo',
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(createOrderAdditionalPay, {
      action: {
        type: Types.ISDINSURANCEPAYMENT,
        data: {
          orderId: 'actionOrderId',
          amount: 100,
          payDeadline: '2022/09/02 10:00:00',
          bizScene: 'test',
          additionalPaymentId: 'additionalPaymentId',
          isModifyToPay: false,
        },
      },
      state: storeMock,
    });

    expect(ToastShow).toBeCalledWith('支付失败');
  });
});

describe('createContinuePayment', () => {
  const storeMock = {
    OrderDetail: {
      orderBaseInfo: {
        continueBackPay: true,
        creditRiskResult: 'T',
      },
      reqOrderParams: {
        orderId: 'storeOrderId',
      },
      driverInfo: {
        name: '测试',
        email: '',
        telphone:
          'd492e309ad4397fe9768cf2eea0e92905f1c2faca90ce24ac59052e5dbb7214d',
        areaCode: '+86',
        flightNo: '',
        iDCardType: 1,
        iDCardNo: '2103********24',
        encrypIDCardNo: '210381Ns6SnIxGrH24#',
        distributionMobile: '1561TV71116',
        distributionEmail: '',
        decryptIDCardNo: '210381199405065924',
        decryptTelphone: '15618081116',
      },
      vendorInfo: {
        vendorName: '鹏琛租车',
        vendorImageUrl: 'http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg',
        vendorID: 62494,
        vendorConfirmCode: '3082488752',
        isSelf: false,
        selfName: '',
        vendorMobileImageUrl: '',
        commentInfo: {
          vendorGoodType: 0,
          exposedScore: 0,
          topScore: 5,
          level: '',
          commentCount: 2,
          hasComment: 0,
        },
      },
      pickupStore: {
        localDateTime: '2021-06-20 10:00:00',
        storeName: '乌鲁木齐机场店',
        storeCode: '61973',
        storeAddress:
          '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
        longitude: 87.578292,
        latitude: 43.82205,
        storeTel: '13601777729,13681604559',
        storeOpenTimeDesc: '{"":"24小时营业"}',
        cityName: '乌鲁木齐',
        provinceName: '新疆',
        countryName: 'China',
        fromTime: '00:00',
        toTime: '23:59',
        cityId: 39,
        storeSerivceName: '取车门店',
        userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
        userLongitude: 87.615364,
        userLatitude: 43.798958,
        serviceType: '0',
        serviceDetails: ['自行前往门店取车'],
        addrTypeName: '取车门店',
        storeID: 128348,
        commentCount: 0,
        pickUpOffLevel: 0,
        sendTypeForPickUpOffCar: 0,
      },
      returnStore: {
        localDateTime: '2021-06-22 10:00:00',
        storeName: '乌鲁木齐机场店',
        storeCode: '61973',
        storeAddress:
          '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
        longitude: 87.578292,
        latitude: 43.82205,
        storeTel: '13601777729,13681604559',
        storeOpenTimeDesc: '{"":"24小时营业"}',
        cityName: '乌鲁木齐',
        provinceName: '新疆',
        countryName: 'China',
        fromTime: '00:00',
        toTime: '23:59',
        cityId: 39,
        storeSerivceName: '还车门店',
        userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
        userLongitude: 87.615364,
        userLatitude: 43.798958,
        serviceType: '0',
        serviceDetails: ['自行前往门店还车'],
        addrTypeName: '还车门店',
        storeID: 128348,
        commentCount: 0,
        pickUpOffLevel: 0,
        sendTypeForPickUpOffCar: 0,
      },
      cancelRuleInfo: {},
    },
  };

  test('继续支付后付程信分T的场景成功', async () => {
    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: true,
        showError: true,
        status: null,
        result: null,
      }),
    );

    const iSDUpdateFreeDepositInfoFetch = jest
      .spyOn(CarFetch, 'iSDUpdateFreeDepositInfo')
      .mockReturnValue(
        Promise.resolve({
          baseResponse: {
            isSuccess: true,
          },
          resultMsg: '成功',
          referenceNo: 'referenceNo',
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePayment, {
      action: {
        type: Types.CONTINUEPAYMENT,
        data: {},
      },
      state: storeMock,
    });

    expect(iSDUpdateFreeDepositInfoFetch).toBeCalled();

    expect(dispatched).toEqual([Actions.fetchOrder2({})]);
  });

  test('继续支付后付程信分T，用户主动取消支付', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');
    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: false,
        showError: true,
        status: {
          status: -3,
        },
        result: 'cancel pay',
      }),
    );

    const iSDUpdateFreeDepositInfoFetch = jest
      .spyOn(CarFetch, 'iSDUpdateFreeDepositInfo')
      .mockReturnValue(
        Promise.resolve({
          baseResponse: {
            isSuccess: true,
          },
          resultMsg: '成功',
          referenceNo: 'referenceNo',
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePayment, {
      action: {
        type: Types.CONTINUEPAYMENT,
        data: {},
      },
      state: storeMock,
    });

    expect(ToastShow).toBeCalledWith('支付失败，请稍后重试');
  });

  test('继续支付后付程信分T的场景失败', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');

    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: false,
        showError: true,
        status: null,
        result: null,
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePayment, {
      action: {
        type: Types.CONTINUEPAYMENT,
        data: {},
      },
      state: storeMock,
    });

    expect(ToastShow).toBeCalledWith('支付失败，请稍后重试');
  });

  test('继续支付常规支付成功，更新订单详情', async () => {
    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: true,
        showError: true,
        status: null,
        result: null,
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePayment, {
      action: {
        type: Types.CONTINUEPAYMENT,
        data: {},
      },
      state: {
        OrderDetail: {
          ...storeMock.OrderDetail,
          orderBaseInfo: {
            continueBackPay: false,
          },
        },
      },
    });

    expect(dispatched).toEqual([Actions.fetchOrder2({})]);
  });

  test('继续支付常规支付，主动取消', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');
    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: false,
        showError: true,
        status: {
          status: -3,
        },
        result: null,
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePayment, {
      action: {
        type: Types.CONTINUEPAYMENT,
        data: {},
      },
      state: {
        OrderDetail: {
          ...storeMock.OrderDetail,
          orderBaseInfo: {
            continueBackPay: false,
          },
        },
      },
    });

    expect(ToastShow).toBeCalledWith('支付失败');
  });

  test('境外继续支付常规支付成功，更新订单详情', async () => {
    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: true,
        showError: true,
        status: null,
        result: null,
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePayment, {
      action: {
        type: Types.CONTINUEPAYMENT,
        data: {},
      },
      state: {
        OrderDetail: {
          ...storeMock.OrderDetail,
          orderBaseInfo: {
            continueBackPay: false,
          },
        },
      },
    });

    expect(dispatched).toEqual([Actions.fetchOrder2({})]);
  });

  test('境外继续支付常规支付, 取消支付', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');

    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: false,
        showError: true,
        status: {
          status: -3,
        },
        result: null,
      }),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePayment, {
      action: {
        type: Types.CONTINUEPAYMENT,
        data: {},
      },
      state: {
        OrderDetail: {
          ...storeMock.OrderDetail,
          orderBaseInfo: {
            continueBackPay: false,
          },
        },
      },
    });

    expect(ToastShow).toBeCalledWith('支付失败');
  });

  test('境外继续支付常规支付, 支付失败', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');

    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: false,
        showError: true,
        status: {
          status: -1,
        },
        result: null,
      }),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePayment, {
      action: {
        type: Types.CONTINUEPAYMENT,
        data: {},
      },
      state: {
        OrderDetail: {
          ...storeMock.OrderDetail,
          orderBaseInfo: {
            continueBackPay: false,
          },
        },
      },
    });

    expect(ToastShow).toBeCalledWith('支付失败');
  });
});

describe('creditRentAuth', () => {
  const storeMock = {
    OrderDetail: {
      orderBaseInfo: {
        continueBackPay: true,
        creditRiskResult: 'T',
      },
      reqOrderParams: {
        orderId: 'storeOrderId',
      },
      driverInfo: {
        name: '测试',
        email: '',
        telphone:
          'd492e309ad4397fe9768cf2eea0e92905f1c2faca90ce24ac59052e5dbb7214d',
        areaCode: '+86',
        flightNo: '',
        iDCardType: 1,
        iDCardNo: '2103********24',
        encrypIDCardNo: '210381Ns6SnIxGrH24#',
        distributionMobile: '1561TV71116',
        distributionEmail: '',
        decryptIDCardNo: '210381199405065924',
        decryptTelphone: '15618081116',
      },
      vendorInfo: {
        vendorName: '鹏琛租车',
        vendorImageUrl: 'http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg',
        vendorID: 62494,
        vendorConfirmCode: '3082488752',
        isSelf: false,
        selfName: '',
        vendorMobileImageUrl: '',
        commentInfo: {
          vendorGoodType: 0,
          exposedScore: 0,
          topScore: 5,
          level: '',
          commentCount: 2,
          hasComment: 0,
        },
      },
      pickupStore: {
        localDateTime: '2021-06-20 10:00:00',
        storeName: '乌鲁木齐机场店',
        storeCode: '61973',
        storeAddress:
          '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
        longitude: 87.578292,
        latitude: 43.82205,
        storeTel: '13601777729,13681604559',
        storeOpenTimeDesc: '{"":"24小时营业"}',
        cityName: '乌鲁木齐',
        provinceName: '新疆',
        countryName: 'China',
        fromTime: '00:00',
        toTime: '23:59',
        cityId: 39,
        storeSerivceName: '取车门店',
        userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
        userLongitude: 87.615364,
        userLatitude: 43.798958,
        serviceType: '0',
        serviceDetails: ['自行前往门店取车'],
        addrTypeName: '取车门店',
        storeID: 128348,
        commentCount: 0,
        pickUpOffLevel: 0,
        sendTypeForPickUpOffCar: 0,
      },
      returnStore: {
        localDateTime: '2021-06-22 10:00:00',
        storeName: '乌鲁木齐机场店',
        storeCode: '61973',
        storeAddress:
          '新疆省乌鲁木齐市乌鲁木齐市沙依巴克区红庙子街道克拉玛依西街135号新疆维吾尔自治区残疾人联合会',
        longitude: 87.578292,
        latitude: 43.82205,
        storeTel: '13601777729,13681604559',
        storeOpenTimeDesc: '{"":"24小时营业"}',
        cityName: '乌鲁木齐',
        provinceName: '新疆',
        countryName: 'China',
        fromTime: '00:00',
        toTime: '23:59',
        cityId: 39,
        storeSerivceName: '还车门店',
        userAddress: '乌鲁木齐锦尚酒店(原锦福徕快捷酒店)',
        userLongitude: 87.615364,
        userLatitude: 43.798958,
        serviceType: '0',
        serviceDetails: ['自行前往门店还车'],
        addrTypeName: '还车门店',
        storeID: 128348,
        commentCount: 0,
        pickUpOffLevel: 0,
        sendTypeForPickUpOffCar: 0,
      },
      cancelRuleInfo: {},
      freeDeposit: {
        freeDepositWay: 1,
        freeDepositType: 2,
      },
    },
  };

  test('调用程信分授权成功', async () => {
    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() =>
      Promise.resolve({
        success: true,
        showError: false,
        status: {
          status: 0,
        },
        result: null,
        isNew: true,
        payRequestId: 'payRequestId',
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(creditRentAuth, {
      action: {
        type: Types.CONTINUEPAYMENT,
        data: {},
      },
      state: storeMock,
    });

    expect(dispatched).toEqual([
      Actions.updateFreeDepositInfo({
        quickPayNo: 'payRequestId',
        freeDepositType: 2,
        freeDepositWay: 1,
      }),
    ]);
  });
});

describe('getSupplementListLogic', () => {
  test('获取违章和车损列表: 有更新违章', async () => {
    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    const storeString =
      '{"123456":{"violationListLength": 0,"vehicleDamageList": 1}}';
    jest
      .spyOn(CarStorage, 'load')
      .mockImplementation(() => Promise.resolve(storeString));
    jest
      .spyOn(CarStorage, 'save')
      .mockImplementation(() => Promise.resolve(null));

    const getSuplementListApi = jest
      .spyOn(CarFetch, 'getSuplementListApi')
      .mockReturnValue(
        Promise.resolve({
          baseResponse: {
            isSuccess: true,
            code: 0,
          },
          resultMsg: '成功',
          removeDetail: 'removeDetail',
          violationDesc: 'violationDesc',
          violationLst: [
            {
              penaltyPoint: 2,
              penaltyAmount: 200,
              carModelName: '别克凯越',
              carNo: '沪ASK293',
              occurrenceTime: '2018-01-25 10:18',
              location: '苏虹路申虹路西约100米',
              behavior:
                '机动车违反规定停放、临时停车且驾驶人虽在现场但是拒绝驶离',
              payStatus: 1,
              imgLst: [
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              ],
            },
          ],
          vehicleDamageLst: [
            {
              carModelName: '别克凯越',
              carNo: '沪ASK293',
              occurrenceTime: '2018-01-25 20:12',
              totalAmount: 200,
              expenseDetailLst: [
                {
                  expenseType: 1,
                  expenseName: '维修费',
                  expenseAmount: '¥500',
                },
                {
                  expenseType: 1,
                  expenseName: '停运费',
                  expenseAmount: '¥100',
                },
                {
                  expenseType: 1,
                  expenseName: '维修费',
                  expenseAmount: '¥500',
                },
                {
                  expenseType: 1,
                  expenseName: '维修费',
                  expenseAmount: '¥500',
                },
              ],
              imgLst: [
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              ],
            },
          ],
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(getSupplementListLogic, {
      action: {
        type: Types.GET_SUPPLEMENT_LIST,
        data: {
          orderId: '123456',
        },
      },
      state: {},
    });

    expect(getSuplementListApi).toBeCalled();

    expect(dispatched).toEqual([
      Actions.getSupplementListCallBack({
        violationList: [
          {
            penaltyPoint: 2,
            penaltyAmount: 200,
            carModelName: '别克凯越',
            carNo: '沪ASK293',
            occurrenceTime: '2018-01-25 10:18',
            location: '苏虹路申虹路西约100米',
            behavior:
              '机动车违反规定停放、临时停车且驾驶人虽在现场但是拒绝驶离',
            payStatus: 1,
            imgLst: [
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
            ],
          },
        ],
        vehicleDamageList: [
          {
            carModelName: '别克凯越',
            carNo: '沪ASK293',
            occurrenceTime: '2018-01-25 20:12',
            totalAmount: 200,
            expenseDetailLst: [
              {
                expenseType: 1,
                expenseName: '维修费',
                expenseAmount: '¥500',
              },
              {
                expenseType: 1,
                expenseName: '停运费',
                expenseAmount: '¥100',
              },
              {
                expenseType: 1,
                expenseName: '维修费',
                expenseAmount: '¥500',
              },
              {
                expenseType: 1,
                expenseName: '维修费',
                expenseAmount: '¥500',
              },
            ],
            imgLst: [
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
            ],
          },
        ],
        isShowViolationDamageEntry: true,
        removeDetail: 'removeDetail',
        violationDesc: 'violationDesc',
      }),
      Actions.setSupplementListNew({
        visible: true,
      }),
    ]);
  });

  test('获取违章和车损列表: 有更新车损', async () => {
    AppContext.setPageInstance({
      getPageId: () => 1111,
    });

    const storeString =
      '{"123456":{"violationListLength": 1,"vehicleDamageList": 0}}';
    jest
      .spyOn(CarStorage, 'load')
      .mockImplementation(() => Promise.resolve(storeString));
    jest
      .spyOn(CarStorage, 'save')
      .mockImplementation(() => Promise.resolve(null));

    const getSuplementListApi = jest
      .spyOn(CarFetch, 'getSuplementListApi')
      .mockReturnValue(
        Promise.resolve({
          baseResponse: {
            isSuccess: true,
            code: 0,
          },
          resultMsg: '成功',
          removeDetail: 'removeDetail',
          violationDesc: 'violationDesc',
          violationLst: [
            {
              penaltyPoint: 2,
              penaltyAmount: 200,
              carModelName: '别克凯越',
              carNo: '沪ASK293',
              occurrenceTime: '2018-01-25 10:18',
              location: '苏虹路申虹路西约100米',
              behavior:
                '机动车违反规定停放、临时停车且驾驶人虽在现场但是拒绝驶离',
              payStatus: 1,
              imgLst: [
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              ],
            },
          ],
          vehicleDamageLst: [
            {
              carModelName: '别克凯越',
              carNo: '沪ASK293',
              occurrenceTime: '2018-01-25 20:12',
              totalAmount: 200,
              expenseDetailLst: [
                {
                  expenseType: 1,
                  expenseName: '维修费',
                  expenseAmount: '¥500',
                },
                {
                  expenseType: 1,
                  expenseName: '停运费',
                  expenseAmount: '¥100',
                },
                {
                  expenseType: 1,
                  expenseName: '维修费',
                  expenseAmount: '¥500',
                },
                {
                  expenseType: 1,
                  expenseName: '维修费',
                  expenseAmount: '¥500',
                },
              ],
              imgLst: [
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
                'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              ],
            },
          ],
        }),
      );

    // 获取saga执行记录
    const dispatched = await recordSaga(getSupplementListLogic, {
      action: {
        type: Types.GET_SUPPLEMENT_LIST,
        data: {
          orderId: '123456',
        },
      },
      state: {},
    });

    expect(getSuplementListApi).toBeCalled();

    expect(dispatched).toEqual([
      Actions.getSupplementListCallBack({
        violationList: [
          {
            penaltyPoint: 2,
            penaltyAmount: 200,
            carModelName: '别克凯越',
            carNo: '沪ASK293',
            occurrenceTime: '2018-01-25 10:18',
            location: '苏虹路申虹路西约100米',
            behavior:
              '机动车违反规定停放、临时停车且驾驶人虽在现场但是拒绝驶离',
            payStatus: 1,
            imgLst: [
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
            ],
          },
        ],
        vehicleDamageList: [
          {
            carModelName: '别克凯越',
            carNo: '沪ASK293',
            occurrenceTime: '2018-01-25 20:12',
            totalAmount: 200,
            expenseDetailLst: [
              {
                expenseType: 1,
                expenseName: '维修费',
                expenseAmount: '¥500',
              },
              {
                expenseType: 1,
                expenseName: '停运费',
                expenseAmount: '¥100',
              },
              {
                expenseType: 1,
                expenseName: '维修费',
                expenseAmount: '¥500',
              },
              {
                expenseType: 1,
                expenseName: '维修费',
                expenseAmount: '¥500',
              },
            ],
            imgLst: [
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
              'https://pages.c-ctrip.com/carisd/app/easylife/paper_default_bg.png',
            ],
          },
        ],
        isShowViolationDamageEntry: true,
        removeDetail: 'removeDetail',
        violationDesc: 'violationDesc',
      }),
      Actions.setSupplementListNew({
        visible: true,
      }),
    ]);
  });
});

describe('apiModifyOrder', () => {
  test('修改订单成功', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');
    jest.spyOn(CarFetch, 'modifyOrder').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        success: true,
      }),
    );

    const popFn = jest.fn();

    AppContext.setPageInstance({
      getPageId: () => 1111,
      pop: popFn,
    });

    // 获取saga执行记录
    const dispatched = await recordSaga(apiModifyOrder, {
      action: {
        type: Types.FETCH_MODIFYORDERCALLBACK,
        data: {},
      },
      state: {
        OrderDetail: {
          reqModifyOrderParam: {},
        },
      },
    });

    expect(ToastShow).toBeCalledWith('信息修改成功', 2);
    expect(popFn).toBeCalled();

    expect(dispatched).toEqual([
      Actions.setOrderChangeModalVisible({ visible: false }),
      Actions.fetchOrder2({}),
    ]);
  });

  test('修改订单失败', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');
    jest.spyOn(CarFetch, 'modifyOrder').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: false,
        },
        success: false,
        errorMessage: 'errorMessage',
      }),
    );

    const popFn = jest.fn();

    AppContext.setPageInstance({
      getPageId: () => 1111,
      pop: popFn,
    });

    // 获取saga执行记录
    const dispatched = await recordSaga(apiModifyOrder, {
      action: {
        type: Types.FETCH_MODIFYORDERCALLBACK,
        data: {},
      },
      state: {
        OrderDetail: {
          reqModifyOrderParam: {},
        },
      },
    });

    expect(ToastShow).toBeCalledWith('errorMessage', 2);
    expect(popFn).toBeCalled();

    expect(dispatched).toEqual([
      Actions.setOrderChangeModalVisible({ visible: false }),
      Actions.fetchOrder2({}),
    ]);
  });
});

describe('handleContinuePayFail', () => {
  test('tipType 4', async () => {
    expect(
      handleContinuePayFail({
        resultMessage: 'resultMessage',
        tipType: 4,
        resultCode: '40013',
      }).next().value,
    ).toEqual(
      put(
        Actions.setContinuePayFailDiaLogInfo({
          visible: true,
          type: 'alert',
          content: 'resultMessage',
        }),
      ),
    );
  });

  test('tipType 不是 4', async () => {
    const ToastShow = jest.spyOn(Toast, 'show');
    handleContinuePayFail({
      resultMessage: 'resultMessage',
      tipType: 0,
      resultCode: '40013',
    }).next();
    expect(ToastShow).toBeCalledWith('resultMessage', 2);
  });
});

describe('getRentalMustRead', () => {
  test('国内获取门店政策', async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        isGetBookingNotice: true,
      }),
    );
    jest.spyOn(CarFetch, 'getBookingNotice').mockReturnValue(
      Promise.resolve({
        mustReads: [],
        rentalMustReadTable: [],
        rentalMustReadPicture: [],
      }),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(getRentalMustRead, {
      action: {
        type: Types.GETRENTALMUSTREAD,
        data: {},
      },
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          vendorInfo: {
            vendorName: '鹏琛租车',
            vendorImageUrl:
              'http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg',
            vendorID: 62494,
            vendorConfirmCode: '3082488752',
            isSelf: false,
            selfName: '',
            vendorMobileImageUrl: '',
            commentInfo: {
              vendorGoodType: 0,
              exposedScore: 0,
              topScore: 5,
              level: '',
              commentCount: 2,
              hasComment: 0,
            },
          },
        },
      },
    });
    expect(dispatched).toEqual([
      Actions.fetchCarAssistantCallBack({
        carRentalMustRead: [],
        rentalMustReadTable: [],
        rentalMustReadPicture: [],
      }),
    ]);
  });

  test('国内获取门店政策，字段兜底', async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
        isGetBookingNotice: true,
      }),
    );
    jest
      .spyOn(CarFetch, 'getBookingNotice')
      .mockReturnValue(Promise.resolve(null));
    // 获取saga执行记录
    const dispatched = await recordSaga(getRentalMustRead, {
      action: {
        type: Types.GETRENTALMUSTREAD,
        data: {},
      },
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          vendorInfo: {
            vendorName: '鹏琛租车',
            vendorImageUrl:
              'http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg',
            vendorID: 62494,
            vendorConfirmCode: '3082488752',
            isSelf: false,
            selfName: '',
            vendorMobileImageUrl: '',
            commentInfo: {
              vendorGoodType: 0,
              exposedScore: 0,
              topScore: 5,
              level: '',
              commentCount: 2,
              hasComment: 0,
            },
          },
        },
      },
    });
    expect(dispatched).toEqual([
      Actions.fetchCarAssistantCallBack({
        carRentalMustRead: [],
        rentalMustReadTable: [],
        rentalMustReadPicture: [],
      }),
    ]);
  });
});

describe('queryAdditionPayment', () => {
  test('获取补款', async () => {
    jest.spyOn(CarFetch, 'queryAdditionPayment').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
      }),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(queryAdditionPayment, {
      action: {
        type: Types.QUERYADDITIONPAYMENT,
        data: {},
      },
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          vendorInfo: {
            vendorName: '鹏琛租车',
            vendorImageUrl:
              'http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg',
            vendorID: 62494,
            vendorConfirmCode: '3082488752',
            isSelf: false,
            selfName: '',
            vendorMobileImageUrl: '',
            commentInfo: {
              vendorGoodType: 0,
              exposedScore: 0,
              topScore: 5,
              level: '',
              commentCount: 2,
              hasComment: 0,
            },
          },
        },
      },
    });
    expect(dispatched).toEqual([
      Actions.fetchCarAssistantCallBack({
        additionPaymentInfo: {
          baseResponse: {
            isSuccess: true,
          },
        },
      }),
    ]);
  });
});

describe('setRenewStatusByStorage', () => {
  test('续租状态标签更新', async () => {
    const storageLoad = jest.spyOn(CarStorage, 'load').mockImplementation(() =>
      Promise.resolve(
        JSON.stringify({
          renewalStatusName: '续租成功',
        }),
      ),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(setRenewStatusByStorage, {
      action: {
        type: Types.SET_RENEW_STATUS_BYSTORAGE,
        data: {},
      },
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          renewalOrders: [
            {
              renewalOrderId: 'renewalOrderId',
              renewalStatusName: '续租成功',
              chargeInfoList: [
                {
                  chargeName: 'chargeName',
                  currencyCode: 'CNY',
                  currentTotalPrice: '100',
                },
              ],
            },
          ],
        },
      },
    });
    expect(storageLoad).toBeCalledWith(
      'car_renewal_status_visible_renewalOrderId',
      true,
    );
    expect(dispatched).toEqual([
      Actions.setSelectorData({
        orderRenewStatusVisible: false,
      }),
    ]);
  });
});

describe('saveRenewalOrderStatus', () => {
  test('保存续租状态', async () => {
    const storageSave = jest.spyOn(CarStorage, 'save');
    // 获取saga执行记录
    const dispatched = await recordSaga(saveRenewalOrderStatus, {
      action: {
        type: Types.SAVE_RENEWALORDERSTATUS,
        data: {},
      },
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
          renewalOrders: [
            {
              renewalOrderId: 'renewalOrderId',
              renewalStatusName: '续租成功',
              chargeInfoList: [
                {
                  chargeName: 'chargeName',
                  currencyCode: 'CNY',
                  currentTotalPrice: '100',
                },
              ],
            },
          ],
        },
      },
    });
    expect(storageSave).toBeCalledWith(
      'car_renewal_status_visible_renewalOrderId',
      JSON.stringify({
        renewalStatusName: '续租成功',
      }),
      undefined,
      true,
    );
  });
});

describe('queryCarAssistantV2Logic', () => {
  test('用车助手V2信息查询', async () => {
    jest.spyOn(CarFetch, 'queryAdditionPayment').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
      }),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(queryCarAssistantV2Logic, {
      action: {
        type: Types.QUERY_CAR_ASSISTANT_V2,
        data: {},
      },
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    expect(dispatched).toEqual([
      Actions.queryCarAssistantV2CallBack({
        baseResponse: {
          isSuccess: true,
        },
      }),
    ]);
  });
});

describe('apiGueryOrderInsAndXProduct', () => {
  test('用车助手V2信息查询', async () => {
    jest.spyOn(CarFetch, 'queryOrderInsAndXProduct').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
      }),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(apiGueryOrderInsAndXProduct, {
      action: {
        type: Types.QUERY_ORDERINSUANDXPRODUCT,
        data: {},
      },
      state: {
        OrderDetail: {
          reqOrderParams: {
            orderId: 'storeOrderId',
          },
        },
      },
    });
    expect(dispatched).toEqual([
      Actions.queryOrderInsAndXProductCallBack({
        baseResponse: {
          isSuccess: true,
        },
      }),
    ]);
  });
});

describe('apiModifyCrossLocation', () => {
  test('修改旅行限制', async () => {
    jest.spyOn(CarFetch, 'modifyCrossLocation').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
      }),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(apiModifyCrossLocation, {
      action: {
        type: Types.FETCH_MODIFY_CROSS_LOCATION,
        data: {
          selectedResult: [
            {
              code: '1111',
              isSelected: true,
              groupCode: 'CrossPlace',
              name: 'XXXX',
            },
          ],
          callback: () => {},
        },
      },
      state: {},
    });
    expect(dispatched).toEqual([
      Actions.setTravelLimitSelectedResult([
        {
          code: '1111',
          isSelected: true,
          groupCode: 'CrossPlace',
          name: 'XXXX',
        },
      ]),
    ]);
  });
});

jest.mock('../../../src/pages/xcar/Graphql/Schemas/Index.ts', () => ({
  __esModule: true,
  default: () => jest.fn(),
}));
jest.mock('../../../src/pages/xcar/State/Common/Requests', () => ({
  __esModule: true,
  default: () => jest.fn(),
}));
jest
  .spyOn(Utils, 'getRequestUrl')
  .mockImplementation(() =>
    Promise.resolve('https://gateway.m.fws.qa.nt.ctripcorp.com'),
  );
describe('apiFetchOrderDataByGraphql Logic', () => {
  jest
    .spyOn(OrderDetailSelector, 'getCustomerServiceUrl')
    .mockImplementation(() => '');
  jest.spyOn(OrderDetailSelector, 'getPickUpTime').mockImplementation(() => {});
  jest
    .spyOn(OrderDetailSelector, 'getDropOffTime')
    .mockImplementation(() => {});
  jest
    .spyOn(OrderDetailSelector, 'getPickupStore')
    .mockImplementation(() => {});
  jest
    .spyOn(OrderDetailSelector, 'getReturnStore')
    .mockImplementation(() => {});
  jest
    .spyOn(OrderDetailSelector, 'getReqOrderParams')
    .mockImplementation(() => {});
  jest
    .spyOn(GraphqlApi, 'getGraphqlServerUrl')
    .mockImplementation(() =>
      Promise.resolve(
        'https://gateway.m.fws.qa.nt.ctripcorp.com/restapi/soa2/25840/graphql',
      ),
    );
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValueOnce(true);
  jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
  const orderId = '123456';
  const vendorId = '1312324';
  const testFn = async result => {
    jest
      .spyOn(GraphqlApi, 'graphqlFetch')
      .mockImplementation(() => Promise.resolve(result));
    const dispatched = await recordSaga(apiFetchOrderDataByGraphql, {
      action: {
        type: Types.FETCH_ORDER_DATA_BY_GRAPHQL,
        data: {
          orderId,
          vendorId,
        },
      },
      state: {},
    });
    return dispatched;
  };
  test('apiFetchOrderDataByGraphql 测试正常调用, 有接口返回数据undefined', async () => {
    const mockGraphqlResult = {
      data: {
        queryAdditionPayment: {},
        queryPriceInfo: { feeDetailInfo: {} },
        queryWarningList: {},
        getEasyLifeTagInfo: { easyLifeTag: [] },
        getLicense: {},
        getcustomerservice: {},
        querySimilarVehicle: {},
        queryCertificateV3: undefined,
        queryOrderInsAndXProduct: {},
      },
    };
    const dispatched = await testFn(mockGraphqlResult);
    const {
      data: {
        queryAdditionPayment,
        queryPriceInfo,
        queryWarningList,
        getEasyLifeTagInfo,
        getLicense,
        getcustomerservice,
        querySimilarVehicle,
        queryCertificateV3,
        queryOrderInsAndXProduct,
      },
    } = mockGraphqlResult;
    expect(dispatched[0]).toEqual(
      Actions.setSelectorData({
        orderDetailPrice: {
          ...queryPriceInfo?.feeDetailInfo,
          queryOrderDetailPriceLoaded: true,
        },
        easyLifeTags: getEasyLifeTagInfo?.easyLifeTag || [],
      }),
    );
    expect(dispatched[1]).toEqual(
      fetchListWarningInfoCallBackTask({
        storageRes: undefined,
        parameter: undefined,
        data: undefined,
        // res: GraphqlData.data.queryWarningList,
      }),
    );
    expect(dispatched[2]).toEqual(
      Actions.fetchCustomerServiceUrlSuccess({
        customerServiceUrl: 'https://gateway.m.fws.qa.nt.ctripcorp.com',
      }),
    );
    expect(dispatched[3]).toEqual(
      setSupplierData({
        licenseDesc: undefined,
        companyName: undefined,
        licenseImgUrl: undefined,
      }),
    );
  });
  test('apiFetchOrderDataByGraphql 测试正常调用, 接口正常返回数据', async () => {
    const mockGraphqlResult = {
      data: {
        queryAdditionPayment: {},
        queryPriceInfo: { feeDetailInfo: {} },
        queryWarningList: {},
        getEasyLifeTagInfo: { easyLifeTag: [] },
        getLicense: {},
        getcustomerservice: {},
        querySimilarVehicle: {},
        queryCertificateV3: {},
        queryOrderInsAndXProduct: {},
      },
    };
    const dispatched = await testFn(mockGraphqlResult);
    const {
      data: {
        queryAdditionPayment,
        queryPriceInfo,
        queryWarningList,
        getEasyLifeTagInfo,
        getLicense,
        getcustomerservice,
        querySimilarVehicle,
        queryCertificateV3,
        queryOrderInsAndXProduct,
      },
    } = mockGraphqlResult;
    expect(dispatched[0]).toEqual(
      Actions.setSelectorData({
        orderDetailPrice: {
          ...queryPriceInfo?.feeDetailInfo,
          queryOrderDetailPriceLoaded: true,
        },
        easyLifeTags: getEasyLifeTagInfo?.easyLifeTag || [],
      }),
    );
    expect(dispatched[1]).toEqual(
      fetchListWarningInfoCallBackTask({
        storageRes: undefined,
        parameter: undefined,
        data: undefined,
        // res: GraphqlData.data.queryWarningList,
      }),
    );
    expect(dispatched[2]).toEqual(
      Actions.fetchCustomerServiceUrlSuccess({
        customerServiceUrl: 'https://gateway.m.fws.qa.nt.ctripcorp.com',
      }),
    );
    expect(dispatched[3]).toEqual(
      setSupplierData({
        licenseDesc: undefined,
        companyName: undefined,
        licenseImgUrl: undefined,
      }),
    );
    // expect(dispatched[4]).toEqual(
    //   setOrderAuthInfo({
    //     supportInfo: { authCardHistory: undefined },
    //     policy: undefined,
    //     certificateV3List: undefined,
    //   }),
    // );
  });
  test('apiFetchOrderDataByGraphql 异常调用', async () => {
    const exceptionError = new Error('apiFetchOrderDataByGraphql exception');
    const mockGraphqlResult = {
      data: {
        queryOrderCoupon: { couponList: [] },
        queryAdditionPayment: {},
        queryPriceInfo: { feeDetailInfo: {} },
        queryWarningList: {},
        getEasyLifeTagInfo: { easyLifeTag: [] },
        getLicense: {},
        getcustomerservice: {},
        querySimilarVehicle: {},
        queryCertificateV3: {},
        queryOrderInsAndXProduct: {},
      },
    };
    const dispatched = await testFn(mockGraphqlResult);
    expect(fetchMetricLog).toHaveBeenCalled();
  });
});

describe('queryDriverLicenseOrders', () => {
  const OrderEnities = [
    {
      BizType: 'Translation',
      OrderID: 36510726975,
      OrderName: '香港驾照',
      OrderStatusCode: 'TRANSLATION_CANCLED',
      OrderStatusName: '已取消',
      Currency: 'RMB',
      OrderTotalPrice: 1000.0,
      BookingDate: '/Date(1687832153439+0800)/',
      Passagers: [null],
      ContactName: '熊',
      ContactMobile: '131==D55156',
      Longitude: 0.0,
      Latitude: 0.0,
      OrderType: '40',
      IsFromCurrentUser: true,
      IsNeedBind: false,
      HotelOrderItems: [],
      TrainOrderItems: [],
      VacationOrderItems: [],
      PiaoOrderItems: [],
      FlightOrderItems: [],
      CarOrderItems: [],
      TuanOrderItems: [],
      TaxiOrderItems: [],
      DIYOrderItems: [],
      LipinOrderItems: [],
      OrderActions: [
        {
          ActionCode: 'Detail',
          ActionName: '订单详情',
          ActionURLH5:
            'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/hkdriverlicensebookdetail?fromurl=paywaiting&orderid=36510726975&isHideNavBar=YES',
          ActionType: 'Link',
          ActionRank: 0,
          ActionStyle: 'Style1',
          StandardCode: 'Detail',
          LinkType: 'H5',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
        {
          ActionCode: 'Remove',
          ActionName: '删除',
          ActionURLH5: '',
          ActionType: 'Button',
          ActionRank: 10,
          ActionStyle: 'Style1',
          StandardCode: 'Delete',
          LinkType: 'Default',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
      ],
      CruiseOrderItems: [],
      QicheOrderItems: [],
      TopShopOrderItems: [],
      VisaOrderItems: [],
      ActivityOrderItems: [],
      VacationInsuranceOrderItems: [],
      AirportBusOrderItems: [],
      HHTravelOrderItems: [],
      DinnerOrderItems: [],
      GolfOrderItems: [],
      GlobalBuyOrderItems: [],
      MallOrderItems: [],
      DepositOrderItems: [],
      CustomizedOrderItems: [],
      ShipOrderItems: [],
      ExchangeOrderItem: [],
      MeetingOrderItem: [],
      LoungeOrderItem: [],
      FishTripOrderItem: [],
      GlobalBuyVoucherItems: [],
      FinInsOrderItems: [],
      MiceOrderItems: [],
      CrowdFundingOrderItems: [],
      GuiderOrderItems: [],
      PreSaleOrderItems: [],
      TranslationOrderItems: [{}],
      LipinSurpriseOrderItems: [],
      DriverOrderItems: [],
      FlightXOrderItems: [],
      ParentOrderID: 0,
      OrderRecommendations: [],
      BagOrderItems: [],
      ViewType: 'Translation#40',
      HealthOrderItems: [],
      CreditOrderItems: [],
      SportOrderItems: [],
      OneOrderItems: [],
      TotalQuantity: 0,
      FlightXOrderItemInfos: [],
      UsedTime: '/Date(1687832153000+0800)/',
      OrderAssitants: [],
      TravelATOrderItems: [],
      YachtOrderItems: [],
      RailsIntlOrderItems: [],
      GsFoodOrderItems: [],
      CashPassportOrderItems: [],
      IsShared: false,
      CountryCode: '86',
      IsNotTravel: false,
      IsTraveled: false,
      IsOthers: false,
      MktAosOrderItems: [],
      GsFoodQueueOrderItems: [],
      FlightCorOrderItems: [],
      IsOnlineOnlyShowOrder: false,
      IsBasedQueryLocaleOnlyShowOrder: false,
      OiOrderType: '40',
      LocalGuideOrderItems: [],
      IsSupportCurrentChannel: true,
      IsMultiResource: false,
      CommonOrderItems: [],
      OrderNoteInfo: [],
      TotalAmount: 0,
    },
    {
      BizType: 'Translation',
      OrderID: 36510521016,
      OrderName: '香港驾照',
      OrderStatusCode: 'TRANSLATION_PAID',
      OrderStatusName: '已支付',
      Currency: 'RMB',
      OrderTotalPrice: 1000.0,
      BookingDate: '/Date(1685525996927+0800)/',
      Passagers: [null],
      ContactName: '熊',
      ContactMobile: '131==D55156',
      Longitude: 0.0,
      Latitude: 0.0,
      OrderType: '40',
      IsFromCurrentUser: true,
      IsNeedBind: false,
      HotelOrderItems: [],
      TrainOrderItems: [],
      VacationOrderItems: [],
      PiaoOrderItems: [],
      FlightOrderItems: [],
      CarOrderItems: [],
      TuanOrderItems: [],
      TaxiOrderItems: [],
      DIYOrderItems: [],
      LipinOrderItems: [],
      OrderActions: [
        {
          ActionCode: 'Detail',
          ActionName: '订单详情',
          ActionURLH5:
            'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/hkdriverlicensebookdetail?fromurl=paywaiting&orderid=36510521016&isHideNavBar=YES',
          ActionType: 'Link',
          ActionRank: 0,
          ActionStyle: 'Style1',
          StandardCode: 'Detail',
          LinkType: 'H5',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
      ],
      CruiseOrderItems: [],
      QicheOrderItems: [],
      TopShopOrderItems: [],
      VisaOrderItems: [],
      ActivityOrderItems: [],
      VacationInsuranceOrderItems: [],
      AirportBusOrderItems: [],
      HHTravelOrderItems: [],
      DinnerOrderItems: [],
      GolfOrderItems: [],
      GlobalBuyOrderItems: [],
      MallOrderItems: [],
      DepositOrderItems: [],
      CustomizedOrderItems: [],
      ShipOrderItems: [],
      ExchangeOrderItem: [],
      MeetingOrderItem: [],
      LoungeOrderItem: [],
      FishTripOrderItem: [],
      GlobalBuyVoucherItems: [],
      FinInsOrderItems: [],
      MiceOrderItems: [],
      CrowdFundingOrderItems: [],
      GuiderOrderItems: [],
      PreSaleOrderItems: [],
      TranslationOrderItems: [{}],
      LipinSurpriseOrderItems: [],
      DriverOrderItems: [],
      FlightXOrderItems: [],
      ParentOrderID: 0,
      OrderRecommendations: [],
      BagOrderItems: [],
      ViewType: 'Translation#40',
      HealthOrderItems: [],
      CreditOrderItems: [],
      SportOrderItems: [],
      OneOrderItems: [],
      TotalQuantity: 0,
      FlightXOrderItemInfos: [],
      UsedTime: '/Date(1685525997000+0800)/',
      OrderAssitants: [],
      TravelATOrderItems: [],
      YachtOrderItems: [],
      RailsIntlOrderItems: [],
      GsFoodOrderItems: [],
      CashPassportOrderItems: [],
      IsShared: false,
      CountryCode: '86',
      IsNotTravel: false,
      IsTraveled: false,
      IsOthers: false,
      MktAosOrderItems: [],
      GsFoodQueueOrderItems: [],
      FlightCorOrderItems: [],
      IsOnlineOnlyShowOrder: false,
      IsBasedQueryLocaleOnlyShowOrder: false,
      OiOrderType: '40',
      LocalGuideOrderItems: [],
      IsSupportCurrentChannel: true,
      IsMultiResource: false,
      CommonOrderItems: [],
      OrderNoteInfo: [],
      TotalAmount: 0,
    },
    {
      BizType: 'Translation',
      OrderID: 36510520969,
      OrderName: '香港驾照',
      OrderStatusCode: 'TRANSLATION_CANCLED',
      OrderStatusName: '已取消',
      Currency: 'RMB',
      OrderTotalPrice: 1000.0,
      BookingDate: '/Date(1685525980366+0800)/',
      Passagers: [null],
      ContactName: '熊',
      ContactMobile: '131==D55156',
      Longitude: 0.0,
      Latitude: 0.0,
      OrderType: '40',
      IsFromCurrentUser: true,
      IsNeedBind: false,
      HotelOrderItems: [],
      TrainOrderItems: [],
      VacationOrderItems: [],
      PiaoOrderItems: [],
      FlightOrderItems: [],
      CarOrderItems: [],
      TuanOrderItems: [],
      TaxiOrderItems: [],
      DIYOrderItems: [],
      LipinOrderItems: [],
      OrderActions: [
        {
          ActionCode: 'Detail',
          ActionName: '订单详情',
          ActionURLH5:
            'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/hkdriverlicensebookdetail?fromurl=paywaiting&orderid=36510520969&isHideNavBar=YES',
          ActionType: 'Link',
          ActionRank: 0,
          ActionStyle: 'Style1',
          StandardCode: 'Detail',
          LinkType: 'H5',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
        {
          ActionCode: 'Remove',
          ActionName: '删除',
          ActionURLH5: '',
          ActionType: 'Button',
          ActionRank: 10,
          ActionStyle: 'Style1',
          StandardCode: 'Delete',
          LinkType: 'Default',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
      ],
      CruiseOrderItems: [],
      QicheOrderItems: [],
      TopShopOrderItems: [],
      VisaOrderItems: [],
      ActivityOrderItems: [],
      VacationInsuranceOrderItems: [],
      AirportBusOrderItems: [],
      HHTravelOrderItems: [],
      DinnerOrderItems: [],
      GolfOrderItems: [],
      GlobalBuyOrderItems: [],
      MallOrderItems: [],
      DepositOrderItems: [],
      CustomizedOrderItems: [],
      ShipOrderItems: [],
      ExchangeOrderItem: [],
      MeetingOrderItem: [],
      LoungeOrderItem: [],
      FishTripOrderItem: [],
      GlobalBuyVoucherItems: [],
      FinInsOrderItems: [],
      MiceOrderItems: [],
      CrowdFundingOrderItems: [],
      GuiderOrderItems: [],
      PreSaleOrderItems: [],
      TranslationOrderItems: [{}],
      LipinSurpriseOrderItems: [],
      DriverOrderItems: [],
      FlightXOrderItems: [],
      ParentOrderID: 0,
      OrderRecommendations: [],
      BagOrderItems: [],
      ViewType: 'Translation#40',
      HealthOrderItems: [],
      CreditOrderItems: [],
      SportOrderItems: [],
      OneOrderItems: [],
      TotalQuantity: 0,
      FlightXOrderItemInfos: [],
      UsedTime: '/Date(1685525980000+0800)/',
      OrderAssitants: [],
      TravelATOrderItems: [],
      YachtOrderItems: [],
      RailsIntlOrderItems: [],
      GsFoodOrderItems: [],
      CashPassportOrderItems: [],
      IsShared: false,
      CountryCode: '86',
      IsNotTravel: false,
      IsTraveled: false,
      IsOthers: false,
      MktAosOrderItems: [],
      GsFoodQueueOrderItems: [],
      FlightCorOrderItems: [],
      IsOnlineOnlyShowOrder: false,
      IsBasedQueryLocaleOnlyShowOrder: false,
      OiOrderType: '40',
      LocalGuideOrderItems: [],
      IsSupportCurrentChannel: true,
      IsMultiResource: false,
      CommonOrderItems: [],
      OrderNoteInfo: [],
      TotalAmount: 0,
    },
    {
      BizType: 'Translation',
      OrderID: 36510520958,
      OrderName: '香港驾照',
      OrderStatusCode: 'TRANSLATION_CANCLED',
      OrderStatusName: '已取消',
      Currency: 'RMB',
      OrderTotalPrice: 1000.0,
      BookingDate: '/Date(1685525607625+0800)/',
      Passagers: [null],
      ContactName: '熊',
      ContactMobile: '131==D55156',
      Longitude: 0.0,
      Latitude: 0.0,
      OrderType: '40',
      IsFromCurrentUser: true,
      IsNeedBind: false,
      HotelOrderItems: [],
      TrainOrderItems: [],
      VacationOrderItems: [],
      PiaoOrderItems: [],
      FlightOrderItems: [],
      CarOrderItems: [],
      TuanOrderItems: [],
      TaxiOrderItems: [],
      DIYOrderItems: [],
      LipinOrderItems: [],
      OrderActions: [
        {
          ActionCode: 'Detail',
          ActionName: '订单详情',
          ActionURLH5:
            'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/hkdriverlicensebookdetail?fromurl=paywaiting&orderid=36510520958&isHideNavBar=YES',
          ActionType: 'Link',
          ActionRank: 0,
          ActionStyle: 'Style1',
          StandardCode: 'Detail',
          LinkType: 'H5',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
        {
          ActionCode: 'Remove',
          ActionName: '删除',
          ActionURLH5: '',
          ActionType: 'Button',
          ActionRank: 10,
          ActionStyle: 'Style1',
          StandardCode: 'Delete',
          LinkType: 'Default',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
      ],
      CruiseOrderItems: [],
      QicheOrderItems: [],
      TopShopOrderItems: [],
      VisaOrderItems: [],
      ActivityOrderItems: [],
      VacationInsuranceOrderItems: [],
      AirportBusOrderItems: [],
      HHTravelOrderItems: [],
      DinnerOrderItems: [],
      GolfOrderItems: [],
      GlobalBuyOrderItems: [],
      MallOrderItems: [],
      DepositOrderItems: [],
      CustomizedOrderItems: [],
      ShipOrderItems: [],
      ExchangeOrderItem: [],
      MeetingOrderItem: [],
      LoungeOrderItem: [],
      FishTripOrderItem: [],
      GlobalBuyVoucherItems: [],
      FinInsOrderItems: [],
      MiceOrderItems: [],
      CrowdFundingOrderItems: [],
      GuiderOrderItems: [],
      PreSaleOrderItems: [],
      TranslationOrderItems: [{}],
      LipinSurpriseOrderItems: [],
      DriverOrderItems: [],
      FlightXOrderItems: [],
      ParentOrderID: 0,
      OrderRecommendations: [],
      BagOrderItems: [],
      ViewType: 'Translation#40',
      HealthOrderItems: [],
      CreditOrderItems: [],
      SportOrderItems: [],
      OneOrderItems: [],
      TotalQuantity: 0,
      FlightXOrderItemInfos: [],
      UsedTime: '/Date(1685525608000+0800)/',
      OrderAssitants: [],
      TravelATOrderItems: [],
      YachtOrderItems: [],
      RailsIntlOrderItems: [],
      GsFoodOrderItems: [],
      CashPassportOrderItems: [],
      IsShared: false,
      CountryCode: '86',
      IsNotTravel: false,
      IsTraveled: false,
      IsOthers: false,
      MktAosOrderItems: [],
      GsFoodQueueOrderItems: [],
      FlightCorOrderItems: [],
      IsOnlineOnlyShowOrder: false,
      IsBasedQueryLocaleOnlyShowOrder: false,
      OiOrderType: '40',
      LocalGuideOrderItems: [],
      IsSupportCurrentChannel: true,
      IsMultiResource: false,
      CommonOrderItems: [],
      OrderNoteInfo: [],
      TotalAmount: 0,
    },
    {
      BizType: 'Translation',
      OrderID: 36510520549,
      OrderName: '香港驾照',
      OrderStatusCode: 'TRANSLATION_PAID',
      OrderStatusName: '已支付',
      Currency: 'RMB',
      OrderTotalPrice: 1000.0,
      BookingDate: '/Date(1685525274508+0800)/',
      Passagers: [null],
      ContactName: '熊',
      ContactMobile: '131==D55156',
      Longitude: 0.0,
      Latitude: 0.0,
      OrderType: '40',
      IsFromCurrentUser: true,
      IsNeedBind: false,
      HotelOrderItems: [],
      TrainOrderItems: [],
      VacationOrderItems: [],
      PiaoOrderItems: [],
      FlightOrderItems: [],
      CarOrderItems: [],
      TuanOrderItems: [],
      TaxiOrderItems: [],
      DIYOrderItems: [],
      LipinOrderItems: [],
      OrderActions: [
        {
          ActionCode: 'Detail',
          ActionName: '订单详情',
          ActionURLH5:
            'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/hkdriverlicensebookdetail?fromurl=paywaiting&orderid=36510520549&isHideNavBar=YES',
          ActionType: 'Link',
          ActionRank: 0,
          ActionStyle: 'Style1',
          StandardCode: 'Detail',
          LinkType: 'H5',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
      ],
      CruiseOrderItems: [],
      QicheOrderItems: [],
      TopShopOrderItems: [],
      VisaOrderItems: [],
      ActivityOrderItems: [],
      VacationInsuranceOrderItems: [],
      AirportBusOrderItems: [],
      HHTravelOrderItems: [],
      DinnerOrderItems: [],
      GolfOrderItems: [],
      GlobalBuyOrderItems: [],
      MallOrderItems: [],
      DepositOrderItems: [],
      CustomizedOrderItems: [],
      ShipOrderItems: [],
      ExchangeOrderItem: [],
      MeetingOrderItem: [],
      LoungeOrderItem: [],
      FishTripOrderItem: [],
      GlobalBuyVoucherItems: [],
      FinInsOrderItems: [],
      MiceOrderItems: [],
      CrowdFundingOrderItems: [],
      GuiderOrderItems: [],
      PreSaleOrderItems: [],
      TranslationOrderItems: [{}],
      LipinSurpriseOrderItems: [],
      DriverOrderItems: [],
      FlightXOrderItems: [],
      ParentOrderID: 0,
      OrderRecommendations: [],
      BagOrderItems: [],
      ViewType: 'Translation#40',
      HealthOrderItems: [],
      CreditOrderItems: [],
      SportOrderItems: [],
      OneOrderItems: [],
      TotalQuantity: 0,
      FlightXOrderItemInfos: [],
      UsedTime: '/Date(1685525275000+0800)/',
      OrderAssitants: [],
      TravelATOrderItems: [],
      YachtOrderItems: [],
      RailsIntlOrderItems: [],
      GsFoodOrderItems: [],
      CashPassportOrderItems: [],
      IsShared: false,
      CountryCode: '86',
      IsNotTravel: false,
      IsTraveled: false,
      IsOthers: false,
      MktAosOrderItems: [],
      GsFoodQueueOrderItems: [],
      FlightCorOrderItems: [],
      IsOnlineOnlyShowOrder: false,
      IsBasedQueryLocaleOnlyShowOrder: false,
      OiOrderType: '40',
      LocalGuideOrderItems: [],
      IsSupportCurrentChannel: true,
      IsMultiResource: false,
      CommonOrderItems: [],
      OrderNoteInfo: [],
      TotalAmount: 0,
    },
    {
      BizType: 'Translation',
      OrderID: 36510024108,
      OrderName: '香港驾照',
      OrderStatusCode: 'TRANSLATION_PAID',
      OrderStatusName: '已支付',
      Currency: 'RMB',
      OrderTotalPrice: 1300.0,
      BookingDate: '/Date(1682427446446+0800)/',
      Passagers: [null],
      ContactName: '熊',
      ContactMobile: '131==D55156',
      Longitude: 0.0,
      Latitude: 0.0,
      OrderType: '40',
      IsFromCurrentUser: true,
      IsNeedBind: false,
      HotelOrderItems: [],
      TrainOrderItems: [],
      VacationOrderItems: [],
      PiaoOrderItems: [],
      FlightOrderItems: [],
      CarOrderItems: [],
      TuanOrderItems: [],
      TaxiOrderItems: [],
      DIYOrderItems: [],
      LipinOrderItems: [],
      OrderActions: [
        {
          ActionCode: 'Detail',
          ActionName: '订单详情',
          ActionURLH5:
            'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/hkdriverlicensebookdetail?fromurl=paywaiting&orderid=36510024108&isHideNavBar=YES',
          ActionType: 'Link',
          ActionRank: 0,
          ActionStyle: 'Style1',
          StandardCode: 'Detail',
          LinkType: 'H5',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
      ],
      CruiseOrderItems: [],
      QicheOrderItems: [],
      TopShopOrderItems: [],
      VisaOrderItems: [],
      ActivityOrderItems: [],
      VacationInsuranceOrderItems: [],
      AirportBusOrderItems: [],
      HHTravelOrderItems: [],
      DinnerOrderItems: [],
      GolfOrderItems: [],
      GlobalBuyOrderItems: [],
      MallOrderItems: [],
      DepositOrderItems: [],
      CustomizedOrderItems: [],
      ShipOrderItems: [],
      ExchangeOrderItem: [],
      MeetingOrderItem: [],
      LoungeOrderItem: [],
      FishTripOrderItem: [],
      GlobalBuyVoucherItems: [],
      FinInsOrderItems: [],
      MiceOrderItems: [],
      CrowdFundingOrderItems: [],
      GuiderOrderItems: [],
      PreSaleOrderItems: [],
      TranslationOrderItems: [{}],
      LipinSurpriseOrderItems: [],
      DriverOrderItems: [],
      FlightXOrderItems: [],
      ParentOrderID: 0,
      OrderRecommendations: [],
      BagOrderItems: [],
      ViewType: 'Translation#40',
      HealthOrderItems: [],
      CreditOrderItems: [],
      SportOrderItems: [],
      OneOrderItems: [],
      TotalQuantity: 0,
      FlightXOrderItemInfos: [],
      UsedTime: '/Date(1682427446000+0800)/',
      OrderAssitants: [],
      TravelATOrderItems: [],
      YachtOrderItems: [],
      RailsIntlOrderItems: [],
      GsFoodOrderItems: [],
      CashPassportOrderItems: [],
      IsShared: false,
      CountryCode: '86',
      IsNotTravel: false,
      IsTraveled: false,
      IsOthers: false,
      MktAosOrderItems: [],
      GsFoodQueueOrderItems: [],
      FlightCorOrderItems: [],
      IsOnlineOnlyShowOrder: false,
      IsBasedQueryLocaleOnlyShowOrder: false,
      OiOrderType: '40',
      LocalGuideOrderItems: [],
      IsSupportCurrentChannel: true,
      IsMultiResource: false,
      CommonOrderItems: [],
      OrderNoteInfo: [],
      TotalAmount: 0,
    },
    {
      BizType: 'Translation',
      OrderID: 36510023894,
      OrderName: 'NZTA翻译认证件',
      OrderStatusCode: 'TRANSLATION_CANCLED',
      OrderStatusName: '已取消',
      Currency: 'RMB',
      OrderTotalPrice: 35.0,
      BookingDate: '/Date(1682426368930+0800)/',
      Passagers: [null],
      ContactName: '孙金婷',
      ContactMobile: '176SYaS0822',
      Longitude: 0.0,
      Latitude: 0.0,
      OrderType: '40',
      IsFromCurrentUser: true,
      IsNeedBind: false,
      HotelOrderItems: [],
      TrainOrderItems: [],
      VacationOrderItems: [],
      PiaoOrderItems: [],
      FlightOrderItems: [],
      CarOrderItems: [],
      TuanOrderItems: [],
      TaxiOrderItems: [],
      DIYOrderItems: [],
      LipinOrderItems: [],
      OrderActions: [
        {
          ActionCode: 'Detail',
          ActionName: '订单详情',
          ActionURLH5:
            'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/nztalicbookingdetail?fromurl=paywaiting&orderid=36510023894&isHideNavBar=YES',
          ActionType: 'Link',
          ActionRank: 0,
          ActionStyle: 'Style1',
          StandardCode: 'Detail',
          LinkType: 'H5',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
        {
          ActionCode: 'Remove',
          ActionName: '删除',
          ActionURLH5: '',
          ActionType: 'Button',
          ActionRank: 10,
          ActionStyle: 'Style1',
          StandardCode: 'Delete',
          LinkType: 'Default',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
      ],
      CruiseOrderItems: [],
      QicheOrderItems: [],
      TopShopOrderItems: [],
      VisaOrderItems: [],
      ActivityOrderItems: [],
      VacationInsuranceOrderItems: [],
      AirportBusOrderItems: [],
      HHTravelOrderItems: [],
      DinnerOrderItems: [],
      GolfOrderItems: [],
      GlobalBuyOrderItems: [],
      MallOrderItems: [],
      DepositOrderItems: [],
      CustomizedOrderItems: [],
      ShipOrderItems: [],
      ExchangeOrderItem: [],
      MeetingOrderItem: [],
      LoungeOrderItem: [],
      FishTripOrderItem: [],
      GlobalBuyVoucherItems: [],
      FinInsOrderItems: [],
      MiceOrderItems: [],
      CrowdFundingOrderItems: [],
      GuiderOrderItems: [],
      PreSaleOrderItems: [],
      TranslationOrderItems: [{}],
      LipinSurpriseOrderItems: [],
      DriverOrderItems: [],
      FlightXOrderItems: [],
      ParentOrderID: 0,
      OrderRecommendations: [],
      BagOrderItems: [],
      ViewType: 'Translation#40',
      HealthOrderItems: [],
      CreditOrderItems: [],
      SportOrderItems: [],
      OneOrderItems: [],
      TotalQuantity: 0,
      FlightXOrderItemInfos: [],
      UsedTime: '/Date(1682426369000+0800)/',
      OrderAssitants: [],
      TravelATOrderItems: [],
      YachtOrderItems: [],
      RailsIntlOrderItems: [],
      GsFoodOrderItems: [],
      CashPassportOrderItems: [],
      IsShared: false,
      CountryCode: '86',
      IsNotTravel: false,
      IsTraveled: false,
      IsOthers: false,
      MktAosOrderItems: [],
      GsFoodQueueOrderItems: [],
      FlightCorOrderItems: [],
      IsOnlineOnlyShowOrder: false,
      IsBasedQueryLocaleOnlyShowOrder: false,
      OiOrderType: '40',
      LocalGuideOrderItems: [],
      IsSupportCurrentChannel: true,
      IsMultiResource: false,
      CommonOrderItems: [],
      OrderNoteInfo: [],
      TotalAmount: 0,
    },
    {
      BizType: 'Translation',
      OrderID: 36510023805,
      OrderName: '当地语言公证件',
      OrderStatusCode: 'TRANSLATION_PAID',
      OrderStatusName: '已支付',
      Currency: 'RMB',
      OrderTotalPrice: 150.0,
      BookingDate: '/Date(1682426035550+0800)/',
      Passagers: [null],
      ContactName: '熊',
      ContactMobile: '131==D55156',
      Longitude: 0.0,
      Latitude: 0.0,
      OrderType: '40',
      IsFromCurrentUser: true,
      IsNeedBind: false,
      HotelOrderItems: [],
      TrainOrderItems: [],
      VacationOrderItems: [],
      PiaoOrderItems: [],
      FlightOrderItems: [],
      CarOrderItems: [],
      TuanOrderItems: [],
      TaxiOrderItems: [],
      DIYOrderItems: [],
      LipinOrderItems: [],
      OrderActions: [
        {
          ActionCode: 'Detail',
          ActionName: '订单详情',
          ActionURLH5:
            'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/langlicbookingdetail?fromurl=paywaiting&orderid=36510023805&isHideNavBar=YES',
          ActionType: 'Link',
          ActionRank: 0,
          ActionStyle: 'Style1',
          StandardCode: 'Detail',
          LinkType: 'H5',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
      ],
      CruiseOrderItems: [],
      QicheOrderItems: [],
      TopShopOrderItems: [],
      VisaOrderItems: [],
      ActivityOrderItems: [],
      VacationInsuranceOrderItems: [],
      AirportBusOrderItems: [],
      HHTravelOrderItems: [],
      DinnerOrderItems: [],
      GolfOrderItems: [],
      GlobalBuyOrderItems: [],
      MallOrderItems: [],
      DepositOrderItems: [],
      CustomizedOrderItems: [],
      ShipOrderItems: [],
      ExchangeOrderItem: [],
      MeetingOrderItem: [],
      LoungeOrderItem: [],
      FishTripOrderItem: [],
      GlobalBuyVoucherItems: [],
      FinInsOrderItems: [],
      MiceOrderItems: [],
      CrowdFundingOrderItems: [],
      GuiderOrderItems: [],
      PreSaleOrderItems: [],
      TranslationOrderItems: [{}],
      LipinSurpriseOrderItems: [],
      DriverOrderItems: [],
      FlightXOrderItems: [],
      ParentOrderID: 0,
      OrderRecommendations: [],
      BagOrderItems: [],
      ViewType: 'Translation#40',
      HealthOrderItems: [],
      CreditOrderItems: [],
      SportOrderItems: [],
      OneOrderItems: [],
      TotalQuantity: 0,
      FlightXOrderItemInfos: [],
      UsedTime: '/Date(1682426036000+0800)/',
      OrderAssitants: [],
      TravelATOrderItems: [],
      YachtOrderItems: [],
      RailsIntlOrderItems: [],
      GsFoodOrderItems: [],
      CashPassportOrderItems: [],
      IsShared: false,
      CountryCode: '86',
      IsNotTravel: false,
      IsTraveled: false,
      IsOthers: false,
      MktAosOrderItems: [],
      GsFoodQueueOrderItems: [],
      FlightCorOrderItems: [],
      IsOnlineOnlyShowOrder: false,
      IsBasedQueryLocaleOnlyShowOrder: false,
      OiOrderType: '40',
      LocalGuideOrderItems: [],
      IsSupportCurrentChannel: true,
      IsMultiResource: false,
      CommonOrderItems: [],
      OrderNoteInfo: [],
      TotalAmount: 0,
    },
    {
      BizType: 'Translation',
      OrderID: 36510023670,
      OrderName: '当地语言公证件',
      OrderStatusCode: 'TRANSLATION_CANCLED',
      OrderStatusName: '已取消',
      Currency: 'RMB',
      OrderTotalPrice: 150.0,
      BookingDate: '/Date(1682425770721+0800)/',
      Passagers: [null],
      ContactName: '熊',
      ContactMobile: '131==D55156',
      Longitude: 0.0,
      Latitude: 0.0,
      OrderType: '40',
      IsFromCurrentUser: true,
      IsNeedBind: false,
      HotelOrderItems: [],
      TrainOrderItems: [],
      VacationOrderItems: [],
      PiaoOrderItems: [],
      FlightOrderItems: [],
      CarOrderItems: [],
      TuanOrderItems: [],
      TaxiOrderItems: [],
      DIYOrderItems: [],
      LipinOrderItems: [],
      OrderActions: [
        {
          ActionCode: 'Detail',
          ActionName: '订单详情',
          ActionURLH5:
            'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/langlicbookingdetail?fromurl=paywaiting&orderid=36510023670&isHideNavBar=YES',
          ActionType: 'Link',
          ActionRank: 0,
          ActionStyle: 'Style1',
          StandardCode: 'Detail',
          LinkType: 'H5',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
        {
          ActionCode: 'Remove',
          ActionName: '删除',
          ActionURLH5: '',
          ActionType: 'Button',
          ActionRank: 10,
          ActionStyle: 'Style1',
          StandardCode: 'Delete',
          LinkType: 'Default',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
      ],
      CruiseOrderItems: [],
      QicheOrderItems: [],
      TopShopOrderItems: [],
      VisaOrderItems: [],
      ActivityOrderItems: [],
      VacationInsuranceOrderItems: [],
      AirportBusOrderItems: [],
      HHTravelOrderItems: [],
      DinnerOrderItems: [],
      GolfOrderItems: [],
      GlobalBuyOrderItems: [],
      MallOrderItems: [],
      DepositOrderItems: [],
      CustomizedOrderItems: [],
      ShipOrderItems: [],
      ExchangeOrderItem: [],
      MeetingOrderItem: [],
      LoungeOrderItem: [],
      FishTripOrderItem: [],
      GlobalBuyVoucherItems: [],
      FinInsOrderItems: [],
      MiceOrderItems: [],
      CrowdFundingOrderItems: [],
      GuiderOrderItems: [],
      PreSaleOrderItems: [],
      TranslationOrderItems: [{}],
      LipinSurpriseOrderItems: [],
      DriverOrderItems: [],
      FlightXOrderItems: [],
      ParentOrderID: 0,
      OrderRecommendations: [],
      BagOrderItems: [],
      ViewType: 'Translation#40',
      HealthOrderItems: [],
      CreditOrderItems: [],
      SportOrderItems: [],
      OneOrderItems: [],
      TotalQuantity: 0,
      FlightXOrderItemInfos: [],
      UsedTime: '/Date(1682425771000+0800)/',
      OrderAssitants: [],
      TravelATOrderItems: [],
      YachtOrderItems: [],
      RailsIntlOrderItems: [],
      GsFoodOrderItems: [],
      CashPassportOrderItems: [],
      IsShared: false,
      CountryCode: '86',
      IsNotTravel: false,
      IsTraveled: false,
      IsOthers: false,
      MktAosOrderItems: [],
      GsFoodQueueOrderItems: [],
      FlightCorOrderItems: [],
      IsOnlineOnlyShowOrder: false,
      IsBasedQueryLocaleOnlyShowOrder: false,
      OiOrderType: '40',
      LocalGuideOrderItems: [],
      IsSupportCurrentChannel: true,
      IsMultiResource: false,
      CommonOrderItems: [],
      OrderNoteInfo: [],
      TotalAmount: 0,
    },
    {
      BizType: 'Translation',
      OrderID: 36510023119,
      OrderName: '驾照国际翻译认证件',
      OrderStatusCode: 'TRANSLATION_CANCLED',
      OrderStatusName: '已取消',
      Currency: 'RMB',
      OrderTotalPrice: 18.0,
      BookingDate: '/Date(1682424491515+0800)/',
      Passagers: ['测试'],
      ContactName: '熊',
      ContactMobile: '131==D55156',
      Longitude: 0.0,
      Latitude: 0.0,
      OrderType: '40',
      IsFromCurrentUser: true,
      IsNeedBind: false,
      HotelOrderItems: [],
      TrainOrderItems: [],
      VacationOrderItems: [],
      PiaoOrderItems: [],
      FlightOrderItems: [],
      CarOrderItems: [],
      TuanOrderItems: [],
      TaxiOrderItems: [],
      DIYOrderItems: [],
      LipinOrderItems: [],
      OrderActions: [
        {
          ActionCode: 'Detail',
          ActionName: '订单详情',
          ActionURLH5:
            'https://gateway.m.fws.qa.nt.ctripcorp.com/webapp/carhire/xsd/idlBookingdetail?fromurl=paywaiting&orderid=36510023119&isHideNavBar=YES',
          ActionType: 'Link',
          ActionRank: 0,
          ActionStyle: 'Style1',
          StandardCode: 'Detail',
          LinkType: 'H5',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
        {
          ActionCode: 'Remove',
          ActionName: '删除',
          ActionURLH5: '',
          ActionType: 'Button',
          ActionRank: 10,
          ActionStyle: 'Style1',
          StandardCode: 'Delete',
          LinkType: 'Default',
          H5Title: '',
          Extension: '',
          UrlSource: '',
        },
      ],
      CruiseOrderItems: [],
      QicheOrderItems: [],
      TopShopOrderItems: [],
      VisaOrderItems: [],
      ActivityOrderItems: [],
      VacationInsuranceOrderItems: [],
      AirportBusOrderItems: [],
      HHTravelOrderItems: [],
      DinnerOrderItems: [],
      GolfOrderItems: [],
      GlobalBuyOrderItems: [],
      MallOrderItems: [],
      DepositOrderItems: [],
      CustomizedOrderItems: [],
      ShipOrderItems: [],
      ExchangeOrderItem: [],
      MeetingOrderItem: [],
      LoungeOrderItem: [],
      FishTripOrderItem: [],
      GlobalBuyVoucherItems: [],
      FinInsOrderItems: [],
      MiceOrderItems: [],
      CrowdFundingOrderItems: [],
      GuiderOrderItems: [],
      PreSaleOrderItems: [],
      TranslationOrderItems: [{}],
      LipinSurpriseOrderItems: [],
      DriverOrderItems: [],
      FlightXOrderItems: [],
      ParentOrderID: 0,
      OrderRecommendations: [],
      BagOrderItems: [],
      ViewType: 'Translation#40',
      HealthOrderItems: [],
      CreditOrderItems: [],
      SportOrderItems: [],
      OneOrderItems: [],
      TotalQuantity: 0,
      FlightXOrderItemInfos: [],
      UsedTime: '/Date(1682424492000+0800)/',
      OrderAssitants: [],
      TravelATOrderItems: [],
      YachtOrderItems: [],
      RailsIntlOrderItems: [],
      GsFoodOrderItems: [],
      CashPassportOrderItems: [],
      IsShared: false,
      CountryCode: '86',
      IsNotTravel: false,
      IsTraveled: false,
      IsOthers: false,
      MktAosOrderItems: [],
      GsFoodQueueOrderItems: [],
      FlightCorOrderItems: [],
      IsOnlineOnlyShowOrder: false,
      IsBasedQueryLocaleOnlyShowOrder: false,
      OiOrderType: '40',
      LocalGuideOrderItems: [],
      IsSupportCurrentChannel: true,
      IsMultiResource: false,
      CommonOrderItems: [],
      OrderNoteInfo: [],
      TotalAmount: 0,
    },
  ];
  test('获取驾照证件订单列表', async () => {
    jest.spyOn(CarFetch, 'queryDriverLicenseOrders').mockReturnValue(
      Promise.resolve({
        OrderEnities,
        Offset: '12345',
      }),
    );
    // 获取saga执行记录
    const dispatched = await recordSaga(queryDriverLicenseOrders, {
      action: {
        type: Types.QUERY_DRIVER_LICENSE_ORDERS,
        data: {},
      },
      state: {
        OrderDetail: {},
      },
    });
    expect(dispatched).toEqual([
      Actions.queryDriverLicenseOrdersCallback({
        orderEnities: OrderEnities,
        PageIndex: 1,
        isLastPage: false,
        nextPageOffset: '12345',
      }),
    ]);
  });
});

const queryExtraInsuranceRes = require('../../../__mocks__/restful/18862/queryExtraInsurance/OSD/20230913.json');
describe('queryExtraInsurance', () => {
  test('测试正常调用', async () => {
    jest
      .spyOn(CarFetch, 'queryExtraInsurance')
      .mockReturnValue(Promise.resolve(queryExtraInsuranceRes));

    let callBackParam = null;
    const dispatched = await recordSaga(queryExtraInsurance, {
      action: {
        type: QUERY_EXTRA_INSRUANCE,
        data: {
          callback: data => {
            callBackParam = data;
          },
        },
      },
      state: {},
    });

    expect(callBackParam).toEqual([
      {
        orderStatus: 'Insurance issued',
        showUrl: true,
        url: 'https://www.rentalcover.com/pds/TH69-624X-INS',
        statusCode: 63008,
        code: 'MP18021531PK00079837',
      },
    ]);
  });

  test('测试数据异常调用', async () => {
    const res: any = {};
    jest
      .spyOn(CarFetch, 'queryExtraInsurance')
      .mockReturnValue(Promise.resolve(res));
    let callBackParam = null;
    const dispatched = await recordSaga(queryExtraInsurance, {
      action: {
        type: QUERY_EXTRA_INSRUANCE,
        data: {
          callback: data => {
            callBackParam = data;
          },
        },
      },
      state: {},
    });
    expect(callBackParam).toBeNull();
  });
});
