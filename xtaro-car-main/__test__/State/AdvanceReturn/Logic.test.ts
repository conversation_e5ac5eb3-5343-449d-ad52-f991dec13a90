import {
  queryPrice,
  apply,
  cancel,
} from '../../../src/pages/xcar/State/AdvanceReturn/Logic';
import Toast from '../../../src/pages/xcar/Common/src/Components/Basic/Toast/src';
import * as Actions from '../../../src/pages/xcar/State/AdvanceReturn/Actions';
import { getLimitContentData } from '../../../src/pages/xcar/State/OrderDetail/Actions';
import * as Types from '../../../src/pages/xcar/State/AdvanceReturn/Types';
import { recordSaga } from '../../testHelpers';
import CarFetch from '../../../src/pages/xcar/Util/CarFetch';

describe('queryPrice ', () => {
  test('queryPrice 接口请求 isSuccess:true', async () => {
    // mock接口返回数据
    const queryEarlyReturnPriceAPI = jest.spyOn(CarFetch, 'queryEarlyReturnPrice').mockResolvedValue({
      baseResponse: {
        isSuccess: true,
      },
      earlyReturnRecord: {},
      policyText: {},
    });

    // 获取saga执行记录
    const dispatched = await recordSaga(queryPrice, {
      action: {
        type: Types.QUERY_PRICE,
        data: {},
      },
      state: {
        AdvanceReturn: {
          returnTime: '2023/03/21 00:00:00',
        }
      },
    });

    expect(queryEarlyReturnPriceAPI).toBeCalled();
    expect(dispatched).toEqual([
      Actions.queryPriceCallback({
        isSuccess: true,
        earlyReturnRecord: {},
        policyText: {},
      }),
      getLimitContentData({})
    ]);
  })

  test('queryPrice 接口请求 isSuccess:false', async () => {
    // mock接口返回数据
    const queryEarlyReturnPriceAPI = jest.spyOn(CarFetch, 'queryEarlyReturnPrice').mockResolvedValue({
      baseResponse: {
        isSuccess: false,
        code: '500',
        returnMsg: 'returnMsg'
      },
      earlyReturnRecord: {},
      policyText: {},
    });

    // 获取saga执行记录
    const dispatched = await recordSaga(queryPrice, {
      action: {
        type: Types.QUERY_PRICE,
        data: {},
      },
      state: {
        AdvanceReturn: {
          returnTime: '2023/03/21 00:00:00',
        }
      },
    });

    expect(queryEarlyReturnPriceAPI).toBeCalled();
    expect(dispatched).toEqual([
      Actions.queryPriceCallback({
        isSuccess: false,
        queryPriceTip: 'returnMsg',
      }),
    ]);
  })
})


describe('apply ', () => {
  test('apply 接口请求 isSuccess:true', async () => {
    // mock接口返回数据
    const createEarlyReturnRecordAPI = jest.spyOn(CarFetch, 'createEarlyReturnRecord').mockResolvedValue({
      baseResponse: {
        isSuccess: true,
        returnMsg: 'returnMsg'
      },
    });

    // 获取saga执行记录
    const dispatched = await recordSaga(apply, {
      action: {
        type: Types.APPLY,
        data: {},
      },
      state: {
        AdvanceReturn: {
          returnTime: '2023/03/21 00:00:00',
        }
      },
    });

    expect(createEarlyReturnRecordAPI).toBeCalled();
    expect(dispatched).toEqual([
      Actions.applyCallback({
        isSuccess: true,
      }),
    ]);
  })

  test('apply 接口请求 Toast', async () => {
    // mock接口返回数据
    const createEarlyReturnRecordAPI = jest.spyOn(CarFetch, 'createEarlyReturnRecord').mockResolvedValue({
      baseResponse: {
        isSuccess: false,
        returnMsg: 'returnMsg'
      },
      resultInfo: {
        groupCode: '3',
        msg: 'msg',
      }
    });

    const MockToast = jest.spyOn(Toast, 'show');

    // 获取saga执行记录
    const dispatched = await recordSaga(apply, {
      action: {
        type: Types.APPLY,
        data: {},
      },
      state: {
        AdvanceReturn: {
          returnTime: '2023/03/21 00:00:00',
        }
      },
    });

    expect(createEarlyReturnRecordAPI).toBeCalled();
    expect(MockToast).toBeCalledWith('msg');
  })

  test('apply 接口请求 Confirm', async () => {
    // mock接口返回数据
    const createEarlyReturnRecordAPI = jest.spyOn(CarFetch, 'createEarlyReturnRecord').mockResolvedValue({
      baseResponse: {
        isSuccess: false,
        returnMsg: 'returnMsg'
      },
      resultInfo: {
        groupCode: '1',
        msg: 'msg',
      }
    });

    // 获取saga执行记录
    const dispatched = await recordSaga(apply, {
      action: {
        type: Types.APPLY,
        data: {},
      },
      state: {
        AdvanceReturn: {
          returnTime: '2023/03/21 00:00:00',
        }
      },
    });

    expect(createEarlyReturnRecordAPI).toBeCalled();
    expect(dispatched).toEqual([
      Actions.applyCallback({
        applyTip: 'msg',
        isSuccess: false,
      }),
      Actions.setModalVisible({
        name: 'Confirm',
        visible: true,
        content: 'msg',
        code: '1',
      })
    ])
  })
})


describe('cancel ', () => {
  test('cancel 接口请求 isSuccess:true', async () => {
    // mock接口返回数据
    const cancelEarlyReturnAPI = jest.spyOn(CarFetch, 'cancelEarlyReturn').mockResolvedValue({
      baseResponse: {
        isSuccess: true,
        returnMsg: 'returnMsg'
      },
    });

    const MockToast = jest.spyOn(Toast, 'show');
    // 获取saga执行记录
    const dispatched = await recordSaga(cancel, {
      action: {
        type: Types.CANCEL,
        data: {},
      },
      state: {
        AdvanceReturn: {
          returnTime: '2023/03/21 00:00:00',
        }
      },
    });

    expect(cancelEarlyReturnAPI).toBeCalled();
    expect(MockToast).toBeCalledWith('提前还车已撤销');
  })
})
