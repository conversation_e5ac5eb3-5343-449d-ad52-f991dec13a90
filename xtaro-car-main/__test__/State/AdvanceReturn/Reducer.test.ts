import Reducer, { getInitialState } from '../../../src/pages/xcar/State/AdvanceReturn/Reducer';
import * as Actions from '../../../src/pages/xcar/State/AdvanceReturn/Types';
describe('Reducer Test', () => {
  const initState = getInitialState();

  test('测试 initial state', () => {
    const data = Reducer(undefined, {
      type: Actions.QUERY_PRICE,
    });
    expect(data).toEqual(initState);
  });


  const actionMap = [{
    type: Actions.QUERY_PRICE_CALLBACK,
    data: {
      queryPriceTip: 'queryPriceTip',
    },
    expected: {
      ...initState,
      queryPriceTip: 'queryPriceTip',
      priceResult: {
        queryPriceTip: 'queryPriceTip',
      },
    },
  },
  {
    type: Actions.APPLY_CALLBACK,
    data: {
      isSuccess: true,
      applyTip: 'applyTip',
    },
    expected: {
      ...initState,
      applyTip: 'applyTip',
      isApplySuccess: true,
      orderStatusLabel: 'SUCCESS',
    },
  }, {
    type: Actions.APPLY_CALLBACK,
    data: {
      isSuccess: false,
      applyTip: 'applyTip',
    },
    expected: {
      ...initState,
      isApplySuccess: false,
      applyTip: 'applyTip',
      orderStatusLabel: null,
    },
  }, {
    type: Actions.SET_MODAL_VISIBLE,
    data: {
      name: 'FeeDetail',
      visible: true,
    },
    expected: {
      ...initState,
      modalVisible: {
        ...initState.modalVisible,
        FeeDetail: {
          name: 'FeeDetail',
          visible: true,
        }
      },
    },
  }, {
    type: Actions.CANCEL_CALLBACK,
    data: {
      baseResponse: {
        isSuccess: true,
      },
    },
    expected: {
      ...initState,
      orderStatusLabel: 'CANCEL',
    },
  }, {
    type: Actions.CLEAR_CACHE,
    data: {},
    expected: {
      ...initState,
      orderStatusLabel: '',
    },
  }];

  test.each(actionMap)('%p', ({ type, data, expected }) => {
    expect(Reducer(initState, { type, data })).toEqual(expected);
  })
})

