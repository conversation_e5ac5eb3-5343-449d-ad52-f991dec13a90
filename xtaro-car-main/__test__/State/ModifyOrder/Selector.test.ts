import {
  getPassengerLoaded,
  getMobileString,
  getDepositStatusLabel,
  getModifyStatus,
  getModifyOrderResponse,
  getModifyOrderRequest,
  getIsLoading,
  getModifyOrderSuccess,
  getIsFail,
  getIsMaskLoading,
  getModifyOrderWarnModalVisible,
  getModifyOrderWarnModalProps,
  getModifyOrderRequestInfo,
  getRebookPassenger,
} from '../../../src/pages/xcar/State/ModifyOrder/Selector';
import { ModifyOrderTexts as Texts } from '../../../src/pages/xcar/Constants/TextIndex';
import { DepositStatus } from '../../../src/pages/xcar/Constants/OrderDetail';
import { IModifyStatus } from '../../../src/pages/xcar/State/ModifyOrder/Types';

describe('ModifyOrder Selectors getPassengerLoaded', () => {
  const isPassengerLoaded = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          isPassengerLoaded,
        },
      },
      expected: isPassengerLoaded,
    },
  ];
  test.each(mockStateMap)('getPassengerLoaded check', ({ state, expected }) => {
    const data = getPassengerLoaded(state);
    expect(data).toEqual(expected);
  });
});

describe('ModifyOrder Selectors getMobileString', () => {
  const mockStateMap = [
    {
      state: {
        Booking: {
          driverInfo: [
            {
              type: 'areaCode',
              value: '',
              error: false,
            },
          ],
        },
      },
      expected: '',
    },
    {
      state: {
        Booking: {
          driverInfo: [
            { type: 'mobilePhone', value: '15800000000', error: false },
            { type: 'flightNumber', value: '', error: false },
            { type: 'areaCode', value: '86', error: false },
          ],
        },
      },
      expected: '15800000000',
    },
  ];
  test.each(mockStateMap)('getMobileString check', ({ state, expected }) => {
    const data = getMobileString(state);
    expect(data).toEqual(expected);
  });
});

describe('ModifyOrder Selectors getDepositStatusLabel', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          freeDeposit: {
            depositStatus: DepositStatus.Unsupport,
          },
        },
      },
      expected: '',
    },
    {
      state: {
        OrderDetail: {
          freeDeposit: {
            depositStatus: DepositStatus.PreAuth,
          },
        },
      },
      expected: '',
    },
    {
      state: {
        OrderDetail: {
          freeDeposit: {
            depositStatus: DepositStatus.PayOnline,
          },
        },
      },
      expected: '',
    },
    {
      state: {
        OrderDetail: {
          freeDeposit: {
            depositStatus: DepositStatus.CreditRent,
          },
        },
      },
      expected: Texts.freeDiposit,
    },
    {
      state: {
        OrderDetail: {
          freeDeposit: {
            depositStatus: DepositStatus.Zhima,
          },
        },
      },
      expected: Texts.freeDiposit,
    },
    {
      state: {
        OrderDetail: {
          freeDeposit: {
            depositStatus: DepositStatus.Vendor,
          },
        },
      },
      expected: Texts.freeDiposit,
    },
  ];
  test.each(mockStateMap)(
    'getDepositStatusLabel check',
    ({ state, expected }) => {
      const data = getDepositStatusLabel(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('ModifyOrder Selectors getModifyStatus', () => {
  const mockState = {
    OrderDetail: {
      driverInfo: {
        areaCode: '+86',
        decryptIDCardNo: '310111199208080000',
        decryptTelphone: '15800000000',
        distributionEmail: '',
        distributionMobile: '1583V7=0000',
        email: '',
        encrypIDCardNo: '310111iD7Hi08ObJ30#',
        flightNo: '',
        iDCardNo: '3101********00',
        iDCardType: 1,
        name: '张三',
        telphone: '158****0000',
      },
      pickupStore: {
        addrTypeName: '',
        cityId: 43,
        cityName: '三亚',
        commentCount: 0,
        countryName: 'China',
        fromTime: '00:00',
        latitude: 18.305902,
        localDateTime: '2022-10-25 20:00:00',
        longitude: 109.413683,
        pickUpOffLevel: 0,
        provinceName: '海南',
        sendTypeForPickUpOffCar: 0,
        serviceDetails: ['免费站内取车'],
        serviceType: '16',
        showLocation: {
          latitude: '18.305902',
          longitude: '109.413683',
          oldAddressTitle: '取车地址',
          realAddress: '三亚凤凰国际机场停车楼4楼',
          serviceTypeDesc: '免费站内取车',
        },
        storeAddress: '三亚凤凰国际机场停车楼4楼',
        storeCode: '1966',
        storeID: 11006,
        storeName: '三亚凤凰机场店',
        storeOpenTimeDesc: '{"":"24小时营业"}',
        storeSerivceName: '送车上门：',
        storeTel: '17330803456;17784642345;0898-88667777',
        toTime: '23:59',
        userAddress: '凤凰国际机场',
        userLatitude: 18.303395,
        userLongitude: 109.414693,
      },
      returnStore: {
        addrTypeName: '',
        cityId: 43,
        cityName: '三亚',
        commentCount: 0,
        countryName: 'China',
        fromTime: '00:00',
        latitude: 18.305902,
        localDateTime: '2022-10-26 20:00:00',
        longitude: 109.413683,
        pickUpOffLevel: 0,
        provinceName: '海南',
        sendTypeForPickUpOffCar: 0,
        serviceDetails: ['免费站内还车'],
        showLocation: {
          latitude: '18.305902',
          longitude: '109.413683',
          oldAddressTitle: '还车地址',
          realAddress: '三亚凤凰国际机场停车楼4楼',
          serviceTypeDesc: '免费站内还车',
        },
        storeAddress: '三亚凤凰国际机场停车楼4楼',
        storeCode: '1966',
        storeID: 11006,
        storeName: '三亚凤凰机场店',
        storeOpenTimeDesc: '{"":"24小时营业"}',
        storeSerivceName: '上门取车：',
        storeTel: '17330803456;17784642345;0898-88667777',
        toTime: '23:59',
        userAddress: '凤凰国际机场',
        userLatitude: 18.303395,
        userLongitude: 109.414693,
      },
    },
    DriverList: {
      passenger: {
        age: 30,
        birthday: '1992-08-08',
        certificateList: [
          { certificateNo: '310111199208080000', certificateType: '1' },
        ],
        countryCode: '86',
        email: '',
        firstName: 'SAN',
        fullName: '张三',
        isCreditQualified: false,
        isDefault: true,
        isRecommend: true,
        isSelf: false,
        lastName: 'ZHANG',
        mobile: '15800000000',
        nationality: 'CN',
        nationalityName: '中国',
        passengerId: '114790932',
        sortExt:
          'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:-1661850087000|orderSort:-1661841236924|selfSort:0|requestPassenger:0',
      },
      curCertificates: {},
      availableCertificates: ['1'],
    },
    Booking: {
      driverInfo: [
        { type: 'mobilePhone', value: '15800000000', error: false },
        { type: 'flightNumber', value: '', error: false },
        { type: 'areaCode', value: '86', error: false },
      ],
    },
    LocationAndDate: {
      rentalLocation: {
        pickUp: {
          cid: 43,
          cname: '三亚',
          country: '中国',
          realcountry: '中国',
          isDomestic: true,
          area: {
            id: '',
            name: '凤凰国际机场T1航站楼',
            lat: 18.30747,
            lng: 109.41201,
            type: '1',
          },
          isFromPosition: false,
        },
        dropOff: {
          cid: 43,
          cname: '三亚',
          country: '中国',
          realcountry: '中国',
          isDomestic: true,
          area: {
            id: '',
            name: '凤凰国际机场T1航站楼',
            lat: 18.30747,
            lng: 109.41201,
            type: '1',
          },
          isFromPosition: false,
        },
        isShowDropOff: false,
      },
      rentalDate: {
        pickUp: {
          dateTime: '2022-08-31T02:00:00.000Z',
        },
        dropOff: {
          dateTime: '2022-09-02T02:00:00.000Z',
        },
        productDropOff: {
          dateTime: '2022-09-02T02:00:00.000Z',
        },
      },
    },
    ModifyOrder: {
      isPassengerLoaded: true,
    },
  };
  test('getModifyStatus check', () => {
    const result = getModifyStatus(mockState);
    const expected = IModifyStatus.eitherchange;
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrder Selectors getModifyOrderResponse', () => {
  const modifyOrderResponse = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: null,
        },
      },
      expected: null,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse,
        },
      },
      expected: modifyOrderResponse,
    },
  ];
  test.each(mockStateMap)(
    'getModifyOrderResponse check',
    ({ state, expected }) => {
      const data = getModifyOrderResponse(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('ModifyOrder Selectors getModifyOrderRequest', () => {
  const modifyOrderReqeust = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderReqeust: null,
        },
      },
      expected: null,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderReqeust,
        },
      },
      expected: modifyOrderReqeust,
    },
  ];
  test.each(mockStateMap)(
    'getModifyOrderRequest check',
    ({ state, expected }) => {
      const data = getModifyOrderRequest(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('ModifyOrder Selectors getIsLoading', () => {
  const isLoading = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          isLoading,
        },
      },
      expected: isLoading,
    },
  ];
  test.each(mockStateMap)('getIsLoading check', ({ state, expected }) => {
    const data = getIsLoading(state);
    expect(data).toEqual(expected);
  });
});

describe('ModifyOrder Selectors getModifyOrderSuccess', () => {
  const modifyOrderSuccess = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderSuccess,
        },
      },
      expected: modifyOrderSuccess,
    },
  ];
  test.each(mockStateMap)(
    'getModifyOrderSuccess check',
    ({ state, expected }) => {
      const data = getModifyOrderSuccess(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('ModifyOrder Selectors getIsFail', () => {
  const isFail = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          isFail,
        },
      },
      expected: isFail,
    },
  ];
  test.each(mockStateMap)('getIsFail check', ({ state, expected }) => {
    const data = getIsFail(state);
    expect(data).toEqual(expected);
  });
});

describe('ModifyOrder Selectors getIsMaskLoading', () => {
  const isMaskLoading = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          isMaskLoading,
        },
      },
      expected: isMaskLoading,
    },
  ];
  test.each(mockStateMap)('getIsMaskLoading check', ({ state, expected }) => {
    const data = getIsMaskLoading(state);
    expect(data).toEqual(expected);
  });
});

describe('ModifyOrder Selectors getModifyOrderWarnModalVisible', () => {
  const modifyOrderWarnModalVisible = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderWarnModalVisible,
        },
      },
      expected: modifyOrderWarnModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getModifyOrderWarnModalVisible check',
    ({ state, expected }) => {
      const data = getModifyOrderWarnModalVisible(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('ModifyOrder Selectors getModifyOrderWarnModalProps', () => {
  const modifyOrderWarnModalProps = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderWarnModalProps,
        },
      },
      expected: modifyOrderWarnModalProps,
    },
  ];
  test.each(mockStateMap)(
    'getModifyOrderWarnModalProps check',
    ({ state, expected }) => {
      const data = getModifyOrderWarnModalProps(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('ModifyOrder Selectors getModifyOrderRequestInfo', () => {
  const mockState = {
    DriverList: {
      passenger: {
        age: 30,
        birthday: '1992-08-08',
        certificateList: [
          { certificateNo: '310111199208080000', certificateType: '1' },
        ],
        countryCode: '86',
        email: '',
        firstName: 'SAN',
        fullName: '张三',
        isCreditQualified: false,
        isDefault: true,
        isRecommend: true,
        isSelf: false,
        lastName: 'ZHANG',
        mobile: '15800000000',
        nationality: 'CN',
        nationalityName: '中国',
        passengerId: '114790932',
        sortExt:
          'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:-1661850087000|orderSort:-1661841236924|selfSort:0|requestPassenger:0',
      },
      curCertificates: {},
      availableCertificates: ['1'],
    },
    Booking: {
      driverInfo: [
        { type: 'mobilePhone', value: '15800000000', error: false },
        { type: 'flightNumber', value: '', error: false },
        { type: 'areaCode', value: '86', error: false },
      ],
    },
    LocationAndDate: {
      rentalLocation: {
        pickUp: {
          cid: 43,
          cname: '三亚',
          country: '中国',
          realcountry: '中国',
          isDomestic: true,
          area: {
            id: '',
            name: '凤凰国际机场T1航站楼',
            lat: 18.30747,
            lng: 109.41201,
            type: '1',
            meetingPointId: 148,
          },
          isFromPosition: false,
        },
        dropOff: {
          cid: 43,
          cname: '三亚',
          country: '中国',
          realcountry: '中国',
          isDomestic: true,
          area: {
            id: '',
            name: '凤凰国际机场T1航站楼',
            lat: 18.30747,
            lng: 109.41201,
            type: '1',
            meetingPointId: 148,
          },
          isFromPosition: false,
        },
        isShowDropOff: false,
      },
      rentalDate: {
        pickUp: {
          dateTime: '2022-08-31T02:00:00.000Z',
        },
        dropOff: {
          dateTime: '2022-09-02T02:00:00.000Z',
        },
        productDropOff: {
          dateTime: '2022-09-02T02:00:00.000Z',
        },
      },
    },
  };
  test('getModifyOrderRequestInfo check', () => {
    const result = getModifyOrderRequestInfo(mockState);
    const expected = {
      driver: {
        certificateNumber: '310111199208080000',
        certificateType: 1,
        mobile: '15800000000',
        name: '张三',
      },
      pickUpLocation: {
        areaType: '1',
        bjTime: '2022-08-31 10:00:00',
        cityId: 43,
        cityName: '三亚',
        latitude: '18.30747',
        longitude: '109.41201',
        name: '凤凰国际机场T1航站楼',
        time: '2022-08-31 10:00:00',
        meetingPointId: 148,
      },
      returnLocation: {
        areaType: '1',
        bjTime: '2022-09-02 10:00:00',
        cityId: 43,
        cityName: '三亚',
        latitude: '18.30747',
        longitude: '109.41201',
        name: '凤凰国际机场T1航站楼',
        time: '2022-09-02 10:00:00',
        meetingPointId: 148,
      },
    };
    // 包含
    expect(result).toEqual(
      expect.objectContaining({
        driver: {
          certificateNumber: '310111199208080000',
          certificateType: 1,
          mobile: '15800000000',
          name: '张三',
        },
      }),
    );
    // 相等
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrder Selectors getRebookPassenger', () => {
  const mockState = {
    DriverList: {
      passenger: {
        age: 30,
        birthday: '1992-08-08',
        certificateList: [
          { certificateNo: '310111199208080000', certificateType: '1' },
        ],
        countryCode: '86',
        email: '',
        firstName: 'SAN',
        fullName: '张三',
        isCreditQualified: false,
        isDefault: true,
        isRecommend: true,
        isSelf: false,
        lastName: 'ZHANG',
        mobile: '15800000000',
        nationality: 'CN',
        nationalityName: '中国',
        passengerId: '114790932',
        sortExt:
          'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:-1661850087000|orderSort:-1661841236924|selfSort:0|requestPassenger:0',
      },
      curCertificates: {},
      availableCertificates: ['1'],
    },
    Booking: {
      driverInfo: [
        { type: 'mobilePhone', value: '15800000000', error: false },
        { type: 'flightNumber', value: '', error: false },
        { type: 'areaCode', value: '86', error: false },
      ],
    },
  };
  test('getRebookPassenger check', () => {
    const result = getRebookPassenger(mockState);
    const expected = {
      fullName: '张三',
      mobile: '15800000000',
      certificateList: [
        {
          certificateType: '1',
          certificateNo: '310111199208080000',
        },
      ],
    };
    // 包含
    expect(result).toEqual(
      expect.objectContaining({
        fullName: '张三',
      }),
    );
    // 相等
    expect(result).toEqual(expected);
  });
});
