import ModifyOrderReducer, {
  initalState,
} from '../../../src/pages/xcar/State/ModifyOrder/Reducer';
import {
  MODIFY_SET_LOADING,
  MODIFY_SET_FAIL,
  MODIFY_SET_MASKLOADING,
  MODIFY_SET_ORDER_WARN,
  SET_MODIFY_RESPONSE,
  SET_MODIFY_REQUEST,
  SET_REBOOK_PARAMS,
  MODIFY_SET_LOADPASSENGER,
  MODIFY_CLEAR,
} from '../../../src/pages/xcar/State/ModifyOrder/Types';

describe('ModifyOrder Reducer Test', () => {
  const initState = initalState;

  const store = JSON.stringify(initalState);

  test('Init', () => {
    expect(ModifyOrderReducer(undefined, {})).toEqual(initState);
  });

  test('MODIFY_SET_LOADING', () => {
    expect(
      ModifyOrderReducer(initState, {
        type: MODIFY_SET_LOADING,
        data: true,
      }),
    ).toEqual({
      ...initState,
      isLoading: true,
    });
  });

  test('MODIFY_CLEAR', () => {
    expect(
      ModifyOrderReducer(initState, {
        type: MODIFY_CLEAR,
      }),
    ).toEqual({
      ...JSON.parse(store),
    });
  });

  test('MODIFY_SET_FAIL', () => {
    expect(
      ModifyOrderReducer(initState, {
        type: MODIFY_SET_FAIL,
        data: true,
      }),
    ).toEqual({
      ...initState,
      isFail: true,
    });
  });

  test('MODIFY_SET_MASKLOADING', () => {
    expect(
      ModifyOrderReducer(initState, {
        type: MODIFY_SET_MASKLOADING,
        data: true,
      }),
    ).toEqual({
      ...initState,
      isMaskLoading: true,
    });
  });

  test('MODIFY_SET_LOADPASSENGER', () => {
    expect(
      ModifyOrderReducer(initState, {
        type: MODIFY_SET_LOADPASSENGER,
        data: true,
      }),
    ).toEqual({
      ...initState,
      isPassengerLoaded: true,
    });
  });

  test('MODIFY_SET_ORDER_WARN', () => {
    const content = {};
    expect(
      ModifyOrderReducer(initState, {
        type: MODIFY_SET_ORDER_WARN,
        data: {
          visible: false,
          content: content,
        },
      }),
    ).toEqual({
      ...initState,
      modifyOrderWarnModalVisible: false,
      modifyOrderWarnModalProps: content,
    });
  });

  test('SET_MODIFY_REQUEST', () => {
    const req = {};
    expect(
      ModifyOrderReducer(initState, {
        type: SET_MODIFY_REQUEST,
        data: req,
      }),
    ).toEqual({
      ...initState,
      modifyOrderReqeust: req,
      modifyOrderSuccess: false,
    });
  });

  test('SET_MODIFY_RESPONSE', () => {
    const res = {};
    expect(
      ModifyOrderReducer(initState, {
        type: SET_MODIFY_RESPONSE,
        data: res,
      }),
    ).toEqual({
      ...initState,
      modifyOrderResponse: res,
      modifyOrderSuccess: true,
    });
  });

  test('SET_REBOOK_PARAMS', () => {
    const rebookParams = {};
    expect(
      ModifyOrderReducer(initState, {
        type: SET_REBOOK_PARAMS,
        data: rebookParams,
      }),
    ).toEqual({
      ...initState,
      rebookParams: rebookParams,
    });
  });
});
