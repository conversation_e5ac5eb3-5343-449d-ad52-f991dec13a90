import { recordSaga } from '../../testHelpers';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Log } from '../../../src/pages/xcar/Util/Index';
import {
  create,
  cancel,
  selectModifyDriver,
  setInitalData,
  setInitialDataCallback,
  getRebookUrl,
  rebook,
  success,
} from '../../../src/pages/xcar/State/ModifyOrder/Logic';
import {
  setLoading,
  setIsFail,
  setMaskLoading,
  setModifyOrderWarnModalVisible,
  setModifyOrderResponse,
  setModifyOrderRequest,
  setPassengerLoaded,
} from '../../../src/pages/xcar/State/ModifyOrder/Actions';
import {
  resetConfirmData,
  setModifyDiscountInfo,
  changeCoupon,
} from '../../../src/pages/xcar/State/ModifyOrderConfirm/Actions';
import { Utils } from '../../../src/pages/xcar/Util/Index';
import {
  SELECT_MODIFY_DRIVER,
  SET_INITIAL_DATA,
  SET_INITIAL_DATA_CALLBACK,
  CREATE,
  REBOOK,
  MODIFY_SUCCESS,
  MODIFY_CANCEL,
} from '../../../src/pages/xcar/State/ModifyOrder/Types';
import { xRouter, xShowToast } from '@ctrip/xtaro';
import {
  queryOrderDetail,
  setOrderModalsVisible,
} from '../../../src/pages/xcar/State/OrderDetail/Actions';
import * as DriverListActions from '../../../src/pages/xcar/State/DriverList/Actions';
import { changeFormData } from '../../../src/pages/xcar/State/Booking/Actions';
import * as OrderDetailSelector from '../../../src/pages/xcar/State/OrderDetail/Selectors';
import {
  setDateInfo,
  setLocationInfo,
} from '../../../src/pages/xcar/State/LocationAndDate/Actions';
import * as LocationAndDateSelectors from '../../../src/pages/xcar/State/LocationAndDate/Selectors';
import BbkChannel from '../../../src/pages/xcar/Common/src/Utils';
import { ModifiedPopStorage as ModifiedStorage } from '../../../src/pages/xcar/State/ModifyOrder/ModifiedStorage';
import { Platform, LogKeyDev, ApiResCode } from '../../../src/pages/xcar/Constants/Index';
import { SesameGuiderType } from '../../../src/pages/xcar/ComponentBusiness/SesameCard';

jest.mock('../../../src/pages/xcar/State/OrderDetail/Selectors', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/State/OrderDetail/Selectors') }));

jest.mock('../../../src/pages/xcar/State/LocationAndDate/Selectors', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/State/LocationAndDate/Selectors') }));

jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
jest.mock('../../../src/pages/xcar/State/ModifyOrder/Selector', () => {
  return {
    getModifyOrderRequestInfo: jest.fn(() => {}),
    getRebookPassenger: jest.fn(() => {}),
  };
});
describe('create Logic', () => {
  jest
    .spyOn(OrderDetailSelector, 'getModifyCancelRule')
    .mockImplementation(() => {});
  const testFn = async result => {
    jest
      .spyOn(CarFetch, 'modifyOrderV2')
      .mockReturnValueOnce(Promise.resolve(result));
    const dispatched = await recordSaga(create, {
      action: {
        type: CREATE,
        data: 1234,
      },
      state: {},
    });
    return dispatched;
  };

  test('modifyOrderV2 接口返回异常', async () => {
    const mockData = {};
    const dispatched = await testFn(mockData);
    expect(dispatched).toEqual([
      setModifyOrderRequest({ orderId: 1234, info: undefined }),
      setMaskLoading(true),
      setMaskLoading(false),
      setMaskLoading(false),
    ]);
  });
  test('modifyOrderV2 接口返回正常 code=5', async () => {
    const mockData = {
      resultType: 5,
      resultInfo: {
        title: '',
        msg: '',
      },
      discountInfo: {
        couponList: {
          selectedCoupon: {
            code: 1,
          },
        },
      },
      baseResponse: {
        isSuccess: true,
      },
    };
    const dispatched = await testFn(mockData);
    expect(dispatched).toEqual([
      setModifyOrderRequest({ orderId: 1234, info: undefined }),
      setMaskLoading(true),
      setMaskLoading(false),
      setModifyOrderWarnModalVisible(true, {
        header: '',
        title: '',
        isWarnTip: true,
        desc: undefined,
      }),
    ]);
  });
  test('modifyOrderV2 接口返回正常 discountInfo是null', async () => {
    const mockData = {
      resultType: 0,
      resultInfo: {
        title: '',
        msg: '',
      },
      discountInfo: null,
      baseResponse: {
        isSuccess: true,
      },
    };
    const dispatched = await testFn(mockData);
    expect(dispatched).toEqual([
      setModifyOrderRequest({ orderId: 1234, info: undefined }),
      setMaskLoading(true),
      setMaskLoading(false),
      resetConfirmData(),
      setModifyOrderResponse(mockData),
    ]);
  });
  test('modifyOrderV2 接口返回正常 discountInfo有值', async () => {
    const mockData = {
      resultType: 0,
      resultInfo: {
        title: '',
        msg: '',
      },
      discountInfo: {
        couponList: {
          selectedCoupon: {
            code: '1',
          },
        },
      },
      baseResponse: {
        isSuccess: true,
      },
    };
    const dispatched = await testFn(mockData);
    expect(dispatched).toEqual([
      setModifyOrderRequest({ orderId: 1234, info: undefined }),
      setMaskLoading(true),
      setMaskLoading(false),
      resetConfirmData(),
      setModifyOrderResponse(mockData),
      setModifyDiscountInfo({
        discountInfo: {
          couponList: {
            selectedCoupon: {
              code: '1',
            },
          },
        },
        enablePressCoupon: true,
      }),
      changeCoupon(['1'], true),
    ]);
  });
});
describe('cancel Logic', () => {
  jest
    .spyOn(OrderDetailSelector, 'getOrderId')
    .mockImplementation(() => 123456);
  const testFn = async result => {
    jest
      .spyOn(CarFetch, 'cancelModify')
      .mockReturnValueOnce(Promise.resolve(result));
    const dispatched = await recordSaga(cancel, {
      action: {
        type: MODIFY_CANCEL,
        data: {},
      },
      state: {},
    });
    return dispatched;
  };

  test('cancelModify 接口返回异常', async () => {
    const errorMsg = '系统异常';
    const mockData = {
      errorMsg,
    };
    const dispatched = await testFn(mockData);
    expect(xShowToast).toBeCalledWith({
      "duration": 3000,
      "title": "系统异常",
    });
    expect(dispatched).toEqual([queryOrderDetail({ orderId: 123456 })]);
  });
  test('cancelModify 接口报错', async () => {
    const exceptionError = new Error('error');
    const dispatched = await testFn(
      new Promise(() => {
        throw exceptionError;
      }),
    );
    expect(xShowToast).toBeCalledWith({
      "duration": 3000,
      "title": "系统异常",
    });
  });
});
describe('selectModifyDriver Logic', () => {
  jest.spyOn(OrderDetailSelector, 'getCustomerInfo').mockImplementation(() => {
    return { name: '142343', decryptIDCardNo: 1 };
  });

  const testFn = async result => {
    jest
      .spyOn(OrderDetailSelector, 'getVendorInfo')
      .mockImplementation(() => {});
    jest
      .spyOn(OrderDetailSelector, 'getPickupStore')
      .mockImplementation(() => {});
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockReturnValueOnce(Promise.resolve(result));
    const dispatched = await recordSaga(selectModifyDriver, {
      action: {
        type: SELECT_MODIFY_DRIVER,
        data: {},
      },
      state: {},
    });
    return dispatched;
  };
  test('queryDriverList 接口返回异常', async () => {
    const mockData = {
      passengerList: [],
    };
    const dispatched = await testFn(mockData);
    expect(dispatched).toEqual([
      DriverListActions.fetchApiIdCardList({ vendorId: undefined }),
      DriverListActions.updateCurCertificates({ temp: 'undefined' }),
      DriverListActions.selectDriver({
        passengerId: 'temp',
        fullName: '142343',
        age: undefined,
        countryCode: undefined,
        mobile: undefined,
        email: undefined,
        certificateList: [
          {
            certificateNo: 1,
            certificateType: 'undefined',
          },
        ],
      }),
      setPassengerLoaded(true),
    ]);
  });
  test('queryDriverList 接口正常返回', async () => {
    const mockData = {
      passengerList: [
        {
          fullName: '142343',
          certificateList: [
            {
              certificateNo: 1,
            },
          ],
        },
      ],
    };
    const dispatched = await testFn(mockData);
    expect(dispatched).toEqual([
      DriverListActions.fetchApiIdCardList({ vendorId: undefined }),
      DriverListActions.updateCurCertificates({ temp: 'undefined' }),
      DriverListActions.selectDriver({
        passengerId: 'temp',
        fullName: '142343',
        age: undefined,
        countryCode: undefined,
        mobile: undefined,
        email: undefined,
        certificateList: [
          {
            certificateNo: 1,
            certificateType: 'undefined',
          },
        ],
      }),
      DriverListActions.selectDriver({
        fullName: '142343',
        certificateList: [
          {
            certificateNo: 1,
          },
        ],
      }),
      changeFormData([{ type: 'mobilePhone', value: undefined }]),
      setPassengerLoaded(true),
    ]);
  });
});
describe('setInitalData Logic', () => {
  const testFn = async () => {
    const dispatched = await recordSaga(setInitalData, {
      action: {
        type: MODIFY_CANCEL,
        data: {
          orderId: 123456,
        },
      },
      state: {},
    });
    return dispatched;
  };
  test('isOrderDataExsit没值', async () => {
    jest
      .spyOn(OrderDetailSelector, 'getVehicleInfo')
      .mockImplementation(() => '');
    const dispatched = await testFn();

    expect(dispatched).toEqual([
      setLoading(true),
      queryOrderDetail({ orderId: 123456, notifyModifyOrderCallback: true }),
    ]);
  });
  test('isOrderDataExsit有值', async () => {
    jest
      .spyOn(OrderDetailSelector, 'getVehicleInfo')
      .mockImplementation(() => '12323234');
    jest
      .spyOn(OrderDetailSelector, 'getNewHomeParamFromOrder')
      .mockImplementation(() => {
        return {
          rentalLocation: {},
          rentalDate: {},
        };
      });
    const dispatched = await testFn();
    expect(dispatched).toEqual([setLocationInfo({}), setDateInfo({})]);
  });
});

describe('setInitialDataCallback Logic', () => {
  const testFn = async () => {
    const dispatched = await recordSaga(setInitialDataCallback, {
      action: {
        type: SET_INITIAL_DATA_CALLBACK,
        data: {
          isSuccess: true,
        },
      },
      state: {},
    });
    return dispatched;
  };
  test('setInitialDataCallback 正常调用', async () => {
    const dispatched = await testFn();
    expect(dispatched).toEqual([setLoading(false), setIsFail(false)]);
  });
});

describe('getRebookUrl function', () => {
  const params = {
    rentalLocation: {},
    rentalDate: {
      pickUp: {
        dateTime: '2022-11-20',
      },
      dropOff: {
        dateTime: '2022-11-22',
      },
    },
    orderId: '123456',
    vendorInfo: {},
    vehicleInfo: {},
    pickupStore: {},
    landingToPage: 'rebookhome',
    passenger: {},
    priceInfo: {},
  };
  test('getRebookUrl 正常调用', async () => {
    expect(getRebookUrl(params)).toEqual(
      '/rn_xtaro_car_main/_crn_config?CRNModuleName=rn_xtaro_car_main&CRNType=1&initialPage=Market&st=client&fromurl=common&apptype=ISD_C_APP&data=%7B%22rentalDate%22%3A%7B%22pickUp%22%3A%7B%22dateTime%22%3A%2220221120000000%22%7D%2C%22dropOff%22%3A%7B%22dateTime%22%3A%2220221122000000%22%7D%7D%2C%22rentalLocation%22%3A%7B%7D%7D&landingto=rebookhome&ctripOrderId=123456&encryptUid=&fromType=&originOrderId=123456&channelId=&eid=&originalCouponCode=&originalActivityId=&originalActivityName=&passenger=%7B%7D',
    );
  });
});

describe('rebook Logic', () => {
  const testFn = async isInfoFromOrder => {
    jest
      .spyOn(OrderDetailSelector, 'getOrderId')
      .mockImplementation(() => 123456);
    jest.spyOn(OrderDetailSelector, 'getVendorInfo').mockImplementation(() => {
      return {
        vendorID: '123',
      };
    });
    jest.spyOn(OrderDetailSelector, 'getVehicleInfo').mockImplementation(() => {
      return {
        vendorVehicleID: '222',
        ctripVehicleID: '333',
      };
    });
    jest.spyOn(OrderDetailSelector, 'getPickupStore').mockImplementation(() => {
      return {
        storeID: '123',
      };
    });
    jest
      .spyOn(OrderDetailSelector, 'getOrderPriceInfoFee')
      .mockImplementation(() => {});
    jest
      .spyOn(OrderDetailSelector, 'getNewHomeParamFromOrder')
      .mockImplementation(() => {
        return {
          rentalDate: {
            pickUp: {
              dateTime: '2022-11-20 10:00:00',
            },
            dropOff: {
              dateTime: '2022-11-22 10:00:00',
            },
          },
        };
      });
    jest
      .spyOn(LocationAndDateSelectors, 'getIsDifferentLocation')
      .mockImplementation(() => true);
    jest
      .spyOn(LocationAndDateSelectors, 'getRentalLocation')
      .mockImplementation(() => {});
    jest
      .spyOn(LocationAndDateSelectors, 'getRentalDate')
      .mockImplementation(() => {});
    const dispatched = await recordSaga(rebook, {
      action: {
        type: REBOOK,
        data: {
          isInfoFromOrder,
        },
      },
      state: {},
    });
    return dispatched;
  };
  test('rebook isInfoFromOrder是true', async () => {
    const dispatched = await testFn(true);
    expect(xRouter.navigateTo).toBeCalled();
  });
  test('rebook isInfoFromOrder是false', async () => {
    const dispatched = await testFn(false);
    expect(xRouter.navigateTo).toBeCalled();
  });
});

describe('success Logic', () => {
  const testFn = async result => {
    jest
      .spyOn(OrderDetailSelector, 'getOrderId')
      .mockImplementation(() => '123456');
    jest
      .spyOn(ModifiedStorage, 'loadOne')
        .mockReturnValueOnce(Promise.resolve(result));
        const CarLogFunc = jest.spyOn(CarLog, 'LogTraceDev');
    const dispatched = await recordSaga(success, {
      action: {
        type: MODIFY_SUCCESS,
        data: {},
      },
      state: {},
    });
      return { dispatched,CarLogFunc };
  };

  test('ModifiedStorage 返回true', async () => {
    jest
      .spyOn(OrderDetailSelector, 'getModifyInfoDto')
      .mockImplementation(() => {});
    const dispatched = await testFn(true);
  });
    test('ModifiedStorage 返回false', async () => {

    jest
      .spyOn(OrderDetailSelector, 'getModifyInfoDto')
      .mockImplementation(() => {});
        const { dispatched,CarLogFunc } = await testFn(false);
    // console.log(dispatched);
    // expect(CarLogFunc).toBeCalledWith({
    //   key: LogKeyDev.c_car_trace_modify_order_pop,
    //   info: {
    //     modifyInfoDto: undefined,
    //     orderId: '123456',
    //   },
    // });
  });

  test('ModifiedStorage getModifyInfoDto有值', async () => {
    jest
      .spyOn(OrderDetailSelector, 'getModifyInfoDto')
        .mockImplementation(() => {
            return {
                goDeposit:true,
                    tipInfo: [{
                        code: '6',
                        title: '标题',
                        content:'描述'
                }]
          }
      });
    const {dispatched,CarLogFunc} = await testFn(false);
      expect(dispatched).toEqual([
        setOrderModalsVisible({ ehiModifyOrderModal: { visible: true, data: {
            get btnText() {
              return "去信用免押";
            },
            tips: [],
            type: SesameGuiderType.VERIFY_SUCCESS_ISD,
            verifyRes: {
              boldTitle: '标题',
              subTitle: '描述',
            },
          } } })
      ])
  });
});
