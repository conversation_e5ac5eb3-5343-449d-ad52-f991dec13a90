import {
  getAge,
  getAdultNumbers,
  getChildNumbers,
  getDriverAgeAndNumber,
} from '../../../src/pages/xcar/State/DriverAgeAndNumber/Selectors';

describe('DriverAgeAndNumber Selectors getAge', () => {
  const mockStateMap = [
    {
      state: {
        DriverAgeAndNumber: {
          age: 15,
        },
      },
      expected: 15,
    },
    {
      state: {
        DriverAgeAndNumber: {
        },
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getAge check',
    ({ state, expected }) => {
      const data = getAge(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverAgeAndNumber Selectors getAdultNumbers', () => {
  const mockStateMap = [
    {
      state: {
        DriverAgeAndNumber: {
          adultNumbers: 16,
        },
      },
      expected: 16,
    },
    {
      state: {
        DriverAgeAndNumber: {
        },
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getAdultNumbers check',
    ({ state, expected }) => {
      const data = getAdultNumbers(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverAgeAndNumber Selectors getChildNumbers', () => {
  const mockStateMap = [
    {
      state: {
        DriverAgeAndNumber: {
          childNumbers: 17,
        },
      },
      expected: 17,
    },
    {
      state: {
        DriverAgeAndNumber: {
        },
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getChildNumbers check',
    ({ state, expected }) => {
      const data = getChildNumbers(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverAgeAndNumber Selectors getDriverAgeAndNumber', () => {
  const mockStateMap = [
    {
      state: {
        DriverAgeAndNumber: {
          age: 14,
          adultNumbers: 2,
          childNumbers: 0,
        },
      },
      expected: {
        age: 14,
        adultNumbers: 2,
        childNumbers: 0
      },
    },
    {
      state: {
        DriverAgeAndNumber: {
        },
      },
      expected: {},
    },
  ];
  test.each(mockStateMap)(
    'getDriverAgeAndNumber check',
    ({ state, expected }) => {
      const data = getDriverAgeAndNumber(state);
      expect(data).toEqual(expected);
    },
  );
});
