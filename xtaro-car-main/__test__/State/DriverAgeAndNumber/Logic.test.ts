import { runSaga } from 'redux-saga';
import {
  setCommonAge
} from '../../../src/pages/xcar/State/DriverAgeAndNumber/Logic';
import { takeEveryGeneratorFunction } from '../../testHelpers';
import { AppContext } from '../../../src/pages/xcar/Util/Index';
import { FrontEndConfig } from '../../../src/pages/xcar/Constants/Index';
import {
  GET_AGE,
  SET_AGE,
} from '../../../src/pages/xcar/State/DriverAgeAndNumber/Types';

const { AgeConfig } = FrontEndConfig;

describe('DriverAgeAndNumber Logic setCommonAge', () => {
  test('测试正常调用 url年龄<AgeConfig.MIN_AGE', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: GET_AGE,
      data: {},
    };
    const urlQuery: any = {
      age: '10',
    };
    AppContext.setUrlQuery(urlQuery);
    const logicFunc = takeEveryGeneratorFunction(setCommonAge);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverAgeAndNumber: {
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: SET_AGE,
      data: AgeConfig.MIN_AGE.toString(),
    });
  });

  test('测试正常调用 url年龄>AgeConfig.MAX_AGE', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: GET_AGE,
      data: {},
    };
    const urlQuery: any = {
      age: '100',
    };
    AppContext.setUrlQuery(urlQuery);
    const logicFunc = takeEveryGeneratorFunction(setCommonAge);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverAgeAndNumber: {
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: SET_AGE,
      data: AgeConfig.MAX_AGE.toString(),
    });
  });

  test('测试正常调用 url年龄>=AgeConfig.DEFAULT_AGE.min且<=AgeConfig.DEFAULT_AGE.max', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: GET_AGE,
      data: {},
    };
    const urlQuery: any = {
      age: '50',
    };
    AppContext.setUrlQuery(urlQuery);
    const logicFunc = takeEveryGeneratorFunction(setCommonAge);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverAgeAndNumber: {
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: SET_AGE,
      data: AgeConfig.DEFAULT_AGE.getVal(),
    });
  });

  test('测试正常调用 url年龄24', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: GET_AGE,
      data: {},
    };
    const urlQuery: any = {
      age: '24',
    };
    AppContext.setUrlQuery(urlQuery);
    const logicFunc = takeEveryGeneratorFunction(setCommonAge);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverAgeAndNumber: {
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: SET_AGE,
      data: '24',
    });
  });

  test('测试异常调用', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: GET_AGE,
      data: {},
    };
    const urlQuery: any = {};
    AppContext.setUrlQuery(urlQuery);
    const logicFunc = takeEveryGeneratorFunction(setCommonAge);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverAgeAndNumber: {
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched).toEqual([]);
  });
});