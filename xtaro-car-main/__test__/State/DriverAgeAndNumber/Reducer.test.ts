import DriverAgeAndNumberReducer, { getInitalState } from '../../../src/pages/xcar/State/DriverAgeAndNumber/Reducers';
import {
  SET_AGE, GET_AGE, SET_AGE_ADULT_CHILD_NUM
} from '../../../src/pages/xcar/State/DriverAgeAndNumber/Types';

describe('DriverAgeAndNumber Reducer Test', () => {
  const initState = getInitalState();

  test('DriverAgeAndNumber Reducer Init', () => {
    expect(DriverAgeAndNumberReducer(undefined, {})).toEqual(initState);
  });

  describe('DriverAgeAndNumber Reducer SET_AGE', () => {
    const mockActionMap = [
      {
        data: {
          age: 12,
        },
        expected: {
          ...initState,
          age: 12,
        },
      },
      {
        data: {
        },
        expected: {
          ...initState,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_AGE check',
      ({ data, expected }) => {
        expect(DriverAgeAndNumberReducer(initState, {
          type: SET_AGE,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('DriverAgeAndNumber Reducer SET_AGE_ADULT_CHILD_NUM', () => {
    const mockActionMap = [
      {
        data: {
          age: 1,
          adultSelectNum: 3,
          childSelectNum: 6,
        },
        expected: {
          ...initState,
          age: 1,
          adultNumbers: 3,
          childNumbers: 6,
        },
      },
      {
        data: {
        },
        expected: {
          ...initState,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_AGE_ADULT_CHILD_NUM check',
      ({ data, expected }) => {
        expect(DriverAgeAndNumberReducer(initState, {
          type: SET_AGE_ADULT_CHILD_NUM,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('DriverAgeAndNumber Reducer GET_AGE', () => {
    const mockActionMap = [
      {
        expected: {
          ...initState,
        },
      },
    ];
    test.each(mockActionMap)(
      'GET_AGE check',
      ({ expected }) => {
        expect(DriverAgeAndNumberReducer(initState, {
          type: GET_AGE,
        })).toEqual(expected);
      },
    );
  });
})