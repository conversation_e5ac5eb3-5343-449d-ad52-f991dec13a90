import { queryMemberShipRights, setMemberShipIsShow } from '../../../src/pages/xcar/State/Member/Actions';
import {
  FETCH_QUERY_MEMBERSHIPRIGHTS,
  SET_MEMBERSHIP_VISIBLE,
} from '../../../src/pages/xcar/State/Member/Types';

describe('Member Actions queryMemberShipRights', () => {
  const data = {
    name: 134,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: FETCH_QUERY_MEMBERSHIPRIGHTS,
        data,
      },
    },
    {
      data: undefined,
      expected: {
        type: FETCH_QUERY_MEMBERSHIPRIGHTS,
        data: {},
      },
    },
  ];
  test.each(mockStateMap)(
    'queryMemberShipRights check',
    ({ data, expected }) => {
      const result = queryMemberShipRights(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('Member Actions setMemberShipIsShow', () => {
  const data = {
    name: 134,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: SET_MEMBERSHIP_VISIBLE,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'setMemberShipIsShow check',
    ({ data, expected }) => {
      const result = setMemberShipIsShow(data);
      expect(result).toEqual(expected);
    },
  );
});
