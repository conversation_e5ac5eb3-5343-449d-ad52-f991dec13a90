import {
  getCurLevelCode,
  getCurLevelName,
  getCurBasicPoints,
  getMembershipCard,
  getGrowUpUrl,
  getMembershipCardUrl,
  getRights,
  getWelfare,
  getRightsCode,
  getMembershipVisible,
} from '../../../src/pages/xcar/State/Member/Selectors';

describe('Member Selectors getCurLevelCode', () => {
  const curLevelCode = "35";
  const mockStateMap = [
    {
      state: {
        Member: {},
      },
      expected: undefined,
    },
    {
      state: {
        Member: null,
      },
      expected: undefined,
    },
    {
      state: {
        Member: {
          curLevelCode,
        },
      },
      expected: curLevelCode,
    },
  ];
  test.each(mockStateMap)(
    'getCurLevelCode check',
    ({ state, expected }) => {
      const data = getCurLevelCode(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Member Selectors getCurLevelName', () => {
  const curLevelName = "普通会员";
  const mockStateMap = [
    {
      state: {
        Member: {},
      },
      expected: undefined,
    },
    {
      state: {
        Member: null,
      },
      expected: undefined,
    },
    {
      state: {
        Member: {
          curLevelName,
        },
      },
      expected: curLevelName,
    },
  ];
  test.each(mockStateMap)(
    'getCurLevelName check',
    ({ state, expected }) => {
      const data = getCurLevelName(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Member Selectors getCurBasicPoints', () => {
  const curBasicPoints = 848;
  const mockStateMap = [
    {
      state: {
        Member: {},
      },
      expected: undefined,
    },
    {
      state: {
        Member: null,
      },
      expected: undefined,
    },
    {
      state: {
        Member: {
          curBasicPoints,
        },
      },
      expected: curBasicPoints,
    },
  ];
  test.each(mockStateMap)(
    'getCurBasicPoints check',
    ({ state, expected }) => {
      const data = getCurBasicPoints(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Member Selectors getMembershipCard', () => {
  const membershipCard = 0;
  const mockStateMap = [
    {
      state: {
        Member: {},
      },
      expected: undefined,
    },
    {
      state: {
        Member: null,
      },
      expected: undefined,
    },
    {
      state: {
        Member: {
          membershipCard,
        },
      },
      expected: membershipCard,
    },
  ];
  test.each(mockStateMap)(
    'getMembershipCard check',
    ({ state, expected }) => {
      const data = getMembershipCard(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Member Selectors getGrowUpUrl', () => {
  const growUpUrl = "https://m.fat30.qa.nt.ctripcorp.com/webapp/member/growup?isHideNavBar=YES&popup=close";
  const mockStateMap = [
    {
      state: {
        Member: {},
      },
      expected: undefined,
    },
    {
      state: {
        Member: null,
      },
      expected: undefined,
    },
    {
      state: {
        Member: {
          growUpUrl,
        },
      },
      expected: growUpUrl,
    },
  ];
  test.each(mockStateMap)(
    'getGrowUpUrl check',
    ({ state, expected }) => {
      const data = getGrowUpUrl(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Member Selectors getMembershipCardUrl', () => {
  const membershipCardUrl = "https://m.fat222.qa.nt.ctripcorp.com/webapp/supermember/landing";
  const mockStateMap = [
    {
      state: {
        Member: {},
      },
      expected: undefined,
    },
    {
      state: {
        Member: null,
      },
      expected: undefined,
    },
    {
      state: {
        Member: {
          membershipCardUrl,
        },
      },
      expected: membershipCardUrl,
    },
  ];
  test.each(mockStateMap)(
    'getMembershipCardUrl check',
    ({ state, expected }) => {
      const data = getMembershipCardUrl(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Member Selectors getRights', () => {
  const rights = {
    "title": {
        "stringObjs": [
            {
                "content": "尊敬的普通会员"
            }
        ]
    },
  };
  const mockStateMap = [
    {
      state: {
        Member: {},
      },
      expected: undefined,
    },
    {
      state: {
        Member: null,
      },
      expected: undefined,
    },
    {
      state: {
        Member: {
          rights,
        },
      },
      expected: rights,
    },
  ];
  test.each(mockStateMap)(
    'getRights check',
    ({ state, expected }) => {
      const data = getRights(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Member Selectors getWelfare', () => {
  const welfare = {
    "title": "会员福利",
  };
  const mockStateMap = [
    {
      state: {
        Member: {},
      },
      expected: undefined,
    },
    {
      state: {
        Member: null,
      },
      expected: undefined,
    },
    {
      state: {
        Member: {
          welfare,
        },
      },
      expected: welfare,
    },
  ];
  test.each(mockStateMap)(
    'getWelfare check',
    ({ state, expected }) => {
      const data = getWelfare(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Member Selectors getRightsCode', () => {
  const rightsCode = "PointsAcceleration,PointsRedemption,VipDiscount,XbuDiscount";
  const mockStateMap = [
    {
      state: {
        Member: {},
      },
      expected: undefined,
    },
    {
      state: {
        Member: null,
      },
      expected: undefined,
    },
    {
      state: {
        Member: {
          rightsCode,
        },
      },
      expected: rightsCode,
    },
  ];
  test.each(mockStateMap)(
    'getRightsCode check',
    ({ state, expected }) => {
      const data = getRightsCode(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Member Selectors getMembershipVisible', () => {
  const membershipVisible = true;
  const notMembershipVisible = false;
  const mockStateMap = [
    {
      state: {
        Member: {},
      },
      expected: undefined,
    },
    {
      state: {
        Member: null,
      },
      expected: undefined,
    },
    {
      state: {
        Member: {
          membershipVisible,
        },
      },
      expected: membershipVisible,
    },
    {
      state: {
        Member: {
          membershipVisible: notMembershipVisible,
        },
      },
      expected: notMembershipVisible,
    },
  ];
  test.each(mockStateMap)(
    'getMembershipVisible check',
    ({ state, expected }) => {
      const data = getMembershipVisible(state);
      expect(data).toEqual(expected);
    },
  );
});