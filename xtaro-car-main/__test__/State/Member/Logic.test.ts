import { runSaga } from 'redux-saga';
import {
  queryMembershipRights,
} from '../../../src/pages/xcar/State/Member/Logic';
import { takeEveryGeneratorFunction } from '../../testHelpers';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';
import {
  FETCH_QUERY_MEMBERSHIPRIGHTS,
  FETCH_QUERY_MEMBERSHIPRIGHTS_CALLBACK,
} from '../../../src/pages/xcar/State/Member/Types';

describe('Member Logic queryMembershipRights', () => {
  test('测试正常调用', async () => {
    const dispatched : any = [];
    const showModal = true;
    const fetchParams : any = {
      showModal,
    };
    const res = {
      "baseResponse": {
          "isSuccess": true,
          "code": "200",
          "requestId": "70a608f7-fed0-4f54-9f25-e401cac905f3",
          "extMap": { },
          "apiResCodes": [ ],
          "hasResult": false,
          "errorCode": "200",
          "message": ""
      },
      "ResponseStatus": {
          "Timestamp": "/Date(1640229185349+0800)/",
          "Ack": "Success",
          "Errors": [ ],
          "Extension": [
              {
                  "Id": "CLOGGING_TRACE_ID",
                  "Value": "6921794846881355987"
              },
              {
                  "Id": "RootMessageId",
                  "Value": "100025527-0a063db3-455619-59115"
              }
          ]
      },
      "curLevelCode": "35",
      "curLevelName": "普通会员",
      "curBasicPoints": 848,
      "membershipCard": 0,
      "growUpUrl": "https://m.fat30.qa.nt.ctripcorp.com/webapp/member/growup?isHideNavBar=YES&popup=close",
      "membershipCardUrl": "https://m.fat222.qa.nt.ctripcorp.com/webapp/supermember/landing",
      "rights": {
          "title": {
              "stringObjs": [
                  {
                      "content": "尊敬的普通会员"
                  }
              ]
          },
          "subTitle": {
              "stringObjs": [
                  {
                      "content": "您已解锁",
                      "style": "0"
                  },
                  {
                      "content": "2",
                      "style": "1"
                  },
                  {
                      "content": "项租车权益！",
                      "style": "0"
                  }
              ]
          },
          "items": [
              {
                  "code": "PointsAcceleration",
                  "title": "积分奖励",
                  "subTitle": "普通会员",
                  "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*ifN6S7AB1o8AAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png",
                  "status": 2,
              },
              {
                  "code": "PointsRedemption",
                  "title": "积分兑换",
                  "subTitle": "普通会员",
                  "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*5nJQTKcSbIgAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png",
                  "status": 2,
              },
              {
                  "code": "VipDiscount",
                  "title": "租车费95折",
                  "subTitle": "黄金贵宾解锁",
                  "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*S-QQQaz147QAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png",
                  "status": 2,
              },
              {
                  "code": "XbuDiscount",
                  "title": "租车费9折",
                  "subTitle": "预定机/酒解锁",
                  "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*S-QQQaz147QAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png",
                  "status": 2,
              },
              {
                  "code": "SvipCoupon",
                  "title": "免一日租金",
                  "subTitle": "超级会员",
                  "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*S-QQQaz147QAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png",
                  "status": 0,
              }
          ],
          "desc": {
              "stringObjs": [
                  {
                      "content": "优惠特权不可叠加使用，预订时将默认帮您使用最大优惠"
                  }
              ]
          }
      },
      "welfare": {
          "title": "会员福利",
      }
    };
    const actionMock = {
      type: FETCH_QUERY_MEMBERSHIPRIGHTS,
      data: fetchParams,
    };
    const logicFunc = takeEveryGeneratorFunction(queryMembershipRights);
    jest.spyOn(CarFetch, 'queryMembershipRights').mockReturnValue(Promise.resolve(res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: FETCH_QUERY_MEMBERSHIPRIGHTS_CALLBACK,
      data: {
        curLevelCode: res.curLevelCode,
        curLevelName: res.curLevelName,
        curBasicPoints: res.curBasicPoints,
        membershipCard: res.membershipCard,
        growUpUrl: res.growUpUrl,
        membershipCardUrl: res.membershipCardUrl,
        rights: res.rights,
        welfare: res.welfare,
        showModal,
      },
    });
  });

  test('测试数据异常调用', async () => {
    const dispatched : any = [];
    const fetchParams : any = {
    };
    const actionMock = {
      type: FETCH_QUERY_MEMBERSHIPRIGHTS,
      data: fetchParams,
    };
    const logicFunc = takeEveryGeneratorFunction(queryMembershipRights);
    jest.spyOn(CarFetch, 'queryMembershipRights').mockReturnValue(Promise.resolve(undefined));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });

  test('接口异常调用', async () => {
    const dispatched : any = [];
    const showModal = true;
    const fetchParams : any = {
      showModal,
    };
    const actionMock = {
      type: FETCH_QUERY_MEMBERSHIPRIGHTS,
      data: fetchParams,
    };
    const exceptionError = new Error('queryMembershipRights exception');
    const logicFunc = takeEveryGeneratorFunction(queryMembershipRights);
    jest.spyOn(CarFetch, 'queryMembershipRights').mockReturnValue(new Promise(() => {
      throw exceptionError;
    }));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: FETCH_QUERY_MEMBERSHIPRIGHTS_CALLBACK,
      data: {},
    });
  });
});
