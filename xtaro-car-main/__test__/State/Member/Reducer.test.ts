import {
  CurLevelCode,
  MemberRightsStatus,
} from '../../../src/pages/xcar/Common/src/Logic/src/Member/Types/MemberShipRights';
import MemberReducer, 
{ 
  getInitalState,
  formatCurLevelCode,
  isShowModal,
  formatRightsCode,
} from '../../../src/pages/xcar/State/Member/Reducer';
import {
  FETCH_QUERY_MEMBERSHIPRIGHTS_CALLBACK,
  SET_MEMBERSHIP_VISIBLE,
} from '../../../src/pages/xcar/State/Member/Types';

describe('Member Reducer Test', () => {
  const initState = getInitalState();

  test('Member Reducer Init', () => {
    expect(MemberReducer(undefined, {})).toEqual(initState);
  });

  describe('Member Reducer formatCurLevelCode', () => {
    const mockActionMap = [
      {
        curLevelCode: CurLevelCode.Normal,
        expected: CurLevelCode.Normal,
      },
      {
        curLevelCode: CurLevelCode.Silver,
        expected: CurLevelCode.Silver,
      },
      {
        curLevelCode: CurLevelCode.Platinum,
        expected: CurLevelCode.Platinum,
      },
      {
        curLevelCode: CurLevelCode.Gold,
        expected: CurLevelCode.Gold,
      },
      {
        curLevelCode: CurLevelCode.Diamond,
        expected: CurLevelCode.Diamond,
      },
      {
        curLevelCode: CurLevelCode.GoldDiamond,
        expected: CurLevelCode.GoldDiamond,
      },
      {
        curLevelCode: CurLevelCode.BlackDiamond,
        expected: CurLevelCode.BlackDiamond,
      },
      {
        curLevelCode: "999999",
        expected: CurLevelCode.Normal,
      },
    ];
    test.each(mockActionMap)(
      'formatCurLevelCode check',
      ({ curLevelCode, expected }) => {
        const data = formatCurLevelCode(curLevelCode);
        expect(data).toEqual(expected);
      },
    );
  });

  describe('Member Reducer isShowModal', () => {
    const rights = {
      "items": [
          {
              "code": "PointsAcceleration", 
              "title": "积分奖励", 
          }, 
          {
              "code": "PointsRedemption", 
              "title": "积分兑换", 
          },
      ], 
    };
    const mockActionMap = [
      {
        rights: null,
        curLevelCode: CurLevelCode.Platinum,
        expected: false,
      },
      {
        rights,
        curLevelCode: CurLevelCode.Platinum,
        expected: rights.items.length > 0,
      },
      {
        rights,
        curLevelCode: CurLevelCode.Gold,
        expected: rights.items.length > 0,
      },
      {
        rights,
        curLevelCode: CurLevelCode.Diamond,
        expected: rights.items.length > 0,
      },
      {
        rights,
        curLevelCode: CurLevelCode.GoldDiamond,
        expected: rights.items.length > 0,
      },
      {
        rights,
        curLevelCode: CurLevelCode.BlackDiamond,
        expected: rights.items.length > 0,
      },
      {
        rights,
        curLevelCode: CurLevelCode.Normal,
        expected: false,
      },
      {
        rights,
        curLevelCode: CurLevelCode.Silver,
        expected: false,
      },
      {
        rights,
        curLevelCode: '999999',
        expected: false,
      },
    ];
    test.each(mockActionMap)(
      'isShowModal check',
      ({ rights, curLevelCode, expected }) => {
        const data = isShowModal(rights, curLevelCode);
        expect(data).toEqual(expected);
      },
    );
  });

  describe('Member Reducer formatRightsCode', () => {
    const rights1 = {
      "items": [
          {
              "code": "PointsAcceleration",
              "title": "积分奖励",
              "status": MemberRightsStatus.UnLock,
          }, 
          {
              "code": "PointsRedemption", 
              "title": "积分兑换", 
              "status": MemberRightsStatus.Locked,
          },
          {
            "code": "VipDiscount", 
            "title": "租车费95折", 
            "status": MemberRightsStatus.Locked,
        },
      ], 
    };
    const rights2 = {
      "items": [
          {
              "code": "PointsAcceleration",
              "title": "积分奖励",
              "status": MemberRightsStatus.UnLock,
          }, 
          {
              "code": "PointsRedemption", 
              "title": "积分兑换", 
              "status": MemberRightsStatus.UnLock,
          },
          {
            "code": "VipDiscount", 
            "title": "租车费95折", 
            "status": MemberRightsStatus.UnLock,
        },
      ], 
    };
    const rights3 = {
      "items": [
          {
              "code": "PointsAcceleration",
              "title": "积分奖励",
              "status": MemberRightsStatus.UnLock,
          }, 
          {
              "code": "PointsRedemption", 
              "title": "积分兑换", 
              "status": MemberRightsStatus.Locked,
          },
          {
            "code": "VipDiscount", 
            "title": "租车费95折", 
            "status": MemberRightsStatus.UnLock,
        },
      ], 
    };
    const mockActionMap = [
      {
        rights: null,
        expected: '',
      },
      {
        rights: rights1,
        expected: 'PointsRedemption,VipDiscount',
      },
      {
        rights: rights2,
        expected: '',
      },
      {
        rights: rights3,
        expected: 'PointsRedemption',
      },
    ];
    test.each(mockActionMap)(
      'formatRightsCode check',
      ({ rights, expected }) => {
        const data = formatRightsCode(rights);
        expect(data).toEqual(expected);
      },
    );
  });

  describe('Member Reducer FETCH_QUERY_MEMBERSHIPRIGHTS_CALLBACK', () => {
    const res : any = {
      "baseResponse": {
          "isSuccess": true, 
          "code": "200", 
          "requestId": "70a608f7-fed0-4f54-9f25-e401cac905f3", 
          "extMap": { }, 
          "apiResCodes": [ ], 
          "hasResult": false, 
          "errorCode": "200", 
          "message": ""
      }, 
      "ResponseStatus": {
          "Timestamp": "/Date(1640229185349+0800)/", 
          "Ack": "Success", 
          "Errors": [ ], 
          "Extension": [
              {
                  "Id": "CLOGGING_TRACE_ID", 
                  "Value": "6921794846881355987"
              }, 
              {
                  "Id": "RootMessageId", 
                  "Value": "100025527-0a063db3-455619-59115"
              }
          ]
      }, 
      "curLevelCode": "35", 
      "curLevelName": "普通会员", 
      "curBasicPoints": 848, 
      "membershipCard": 0, 
      "growUpUrl": "https://m.fat30.qa.nt.ctripcorp.com/webapp/member/growup?isHideNavBar=YES&popup=close", 
      "membershipCardUrl": "https://m.fat222.qa.nt.ctripcorp.com/webapp/supermember/landing", 
      "rights": {
          "title": {
              "stringObjs": [
                  {
                      "content": "尊敬的普通会员"
                  }
              ]
          }, 
          "subTitle": {
              "stringObjs": [
                  {
                      "content": "您已解锁", 
                      "style": "0"
                  }, 
                  {
                      "content": "2", 
                      "style": "1"
                  }, 
                  {
                      "content": "项租车权益！", 
                      "style": "0"
                  }
              ]
          },
          "items": [
              {
                  "code": "PointsAcceleration", 
                  "title": "积分奖励", 
                  "subTitle": "普通会员", 
                  "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*ifN6S7AB1o8AAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", 
                  "status": 2, 
              }, 
              {
                  "code": "PointsRedemption", 
                  "title": "积分兑换", 
                  "subTitle": "普通会员", 
                  "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*5nJQTKcSbIgAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", 
                  "status": 2, 
              }, 
              {
                  "code": "VipDiscount", 
                  "title": "租车费95折", 
                  "subTitle": "黄金贵宾解锁", 
                  "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*S-QQQaz147QAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", 
                  "status": 2, 
              }, 
              {
                  "code": "XbuDiscount", 
                  "title": "租车费9折", 
                  "subTitle": "预定机/酒解锁", 
                  "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*S-QQQaz147QAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", 
                  "status": 2, 
              }, 
              {
                  "code": "SvipCoupon", 
                  "title": "免一日租金", 
                  "subTitle": "超级会员", 
                  "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*S-QQQaz147QAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", 
                  "status": 0, 
              }
          ], 
          "desc": {
              "stringObjs": [
                  {
                      "content": "优惠特权不可叠加使用，预订时将默认帮您使用最大优惠"
                  }
              ]
          }
      }, 
      "welfare": {
          "title": "会员福利",
      }
    };
    const res2 : any = {};
    const res3 : any = { showModal: true };
    const mockActionMap = [
      {
        data: res,
        expected: {
          ...initState,
          curLevelCode: formatCurLevelCode(res.curLevelCode),
          rightsCode: formatRightsCode(res.rights),
          curLevelName: res.curLevelName,
          curBasicPoints: res.curBasicPoints,
          membershipCard: res.membershipCard,
          growUpUrl: res.growUpUrl,
          membershipCardUrl: res.membershipCardUrl,
          rights: res.rights,
          welfare: res.welfare,
          membershipVisible: res.showModal && isShowModal(res.rights, res.curLevelCode),
        },
      },
      {
        data: res2,
        expected: {
          ...initState,
          curLevelCode: formatCurLevelCode(res2.curLevelCode),
          rightsCode: formatRightsCode(res2.rights),
          curLevelName: res2.curLevelName,
          curBasicPoints: res2.curBasicPoints,
          membershipCard: res2.membershipCard,
          growUpUrl: res2.growUpUrl,
          membershipCardUrl: res2.membershipCardUrl,
          rights: res2.rights,
          welfare: res2.welfare,
          membershipVisible: res2.showModal && isShowModal(res2.rights, res2.curLevelCode),
        },
      },
      {
        data: res3,
        expected: {
          ...initState,
          curLevelCode: formatCurLevelCode(res3.curLevelCode),
          rightsCode: formatRightsCode(res3.rights),
          curLevelName: res3.curLevelName,
          curBasicPoints: res3.curBasicPoints,
          membershipCard: res3.membershipCard,
          growUpUrl: res3.growUpUrl,
          membershipCardUrl: res3.membershipCardUrl,
          rights: res3.rights,
          welfare: res3.welfare,
          membershipVisible: isShowModal(res3.rights, res3.curLevelCode),
        },
      },
    ];
    test.each(mockActionMap)(
      'FETCH_QUERY_MEMBERSHIPRIGHTS_CALLBACK check',
      ({ data, expected }) => {
        expect(MemberReducer(initState, {
          type: FETCH_QUERY_MEMBERSHIPRIGHTS_CALLBACK,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('Member Reducer SET_MEMBERSHIP_VISIBLE', () => {
    const mockActionMap = [
      {
        data: {
          visible: true,
        },
        expected: {
          ...initState,
          membershipVisible: true,
        },
      },
      {
        data: {
          visible: false,
        },
        expected: {
          ...initState,
          membershipVisible: false,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_MEMBERSHIP_VISIBLE check',
      ({ data, expected }) => {
        expect(MemberReducer(initState, {
          type: SET_MEMBERSHIP_VISIBLE,
          data,
        })).toEqual(expected);
      },
    );
  });
})