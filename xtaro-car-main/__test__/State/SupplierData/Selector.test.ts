import { getSupplierData } from '../../../src/pages/xcar/State/SupplierData/Selector';

const mockStateMap = [
  {
    state: {
      SupplierData: {
        supplierData: {},
      },
    },
    expected: {},
  },
  {
    state: {
      SupplierData: {
      },
    },
    expected: undefined,
  },
];


describe('SupplierData Selectors getSupplierData', () => {
  test.each(mockStateMap)(
    'getSupplierData check',
    ({ state, expected }) => {
      const data = getSupplierData(state);
      expect(data).toEqual(expected);
    },
  );
});

