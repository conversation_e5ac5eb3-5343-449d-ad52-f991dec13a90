import SupplierDataReducer, { getInitalState } from '../../../src/pages/xcar/State/SupplierData/Reducer';
import {
  SET_SUPPLIER_DATA,
  QUERY_ORDERDETAIL_SUPPLIER_DATA,
} from '../../../src/pages/xcar/State/SupplierData/Types';

describe('SupplierData Reducer Test', () => {
  const initState = getInitalState();

  test('SupplierData Reducer Init', () => {
    expect(SupplierDataReducer(undefined, {})).toEqual(initState);
  });

  describe('SupplierData Reducer SET_SUPPLIER_DATA', () => {
    test('SET_SUPPLIER_DATA', () => {
      expect(SupplierDataReducer(initState, {
        type: SET_SUPPLIER_DATA,
        data: {},
      })).toEqual({
        ...initState,
        supplierData: {},
      });
    });
  });

  describe('SupplierData Reducer QUERY_ORDERDETAIL_SUPPLIER_DATA', () => {
    test('QUERY_ORDERDETAIL_SUPPLIER_DATA', () => {
      expect(SupplierDataReducer(initState, {
        type: QUERY_ORDERDETAIL_SUPPLIER_DATA,
        data: {},
      })).toEqual({
        ...initState,
        supplierData: {},
      });
    });
  });
})