import {
  getIsLoading,
  getIsFail,
  getPickType,
  getTempLocation,
  getCityId,
  getCityName,
  getApiQueryAreaFetchParam,
} from '../../../src/pages/xcar/State/Area/Selectors';

describe('Area Selectors getIsLoading', () => {
  const mockStateMap = [
    {
      state: {
        Area: {
          isLoading: false,
        },
      },
      expected: false,
    },
    {
      state: {
        Area: {
          isLoading: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)('getIsLoading check', ({ state, expected }) => {
    const data = getIsLoading(state);
    expect(data).toEqual(expected);
  });
});

describe('Area Selectors getIsFail', () => {
  const mockStateMap = [
    {
      state: {
        Area: {
          isFail: false,
        },
      },
      expected: false,
    },
    {
      state: {
        Area: {
          isFail: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)('getIsFail check', ({ state, expected }) => {
    const data = getIsFail(state);
    expect(data).toEqual(expected);
  });
});

describe('Area Selectors getPickType', () => {
  const mockStateMap = [
    {
      state: {
        Area: {
          pickType: 'pickup',
        },
      },
      expected: 'pickup',
    },
    {
      state: {
        Area: {
          pickType: 'dropoff',
        },
      },
      expected: 'dropoff',
    },
  ];
  test.each(mockStateMap)('getPickType check', ({ state, expected }) => {
    const data = getPickType(state);
    expect(data).toEqual(expected);
  });
});

describe('Area Selectors getTempLocation', () => {
  const mockStateMap = [
    {
      state: {
        Area: {
          tempLocation: {
            cid: 43,
            cname: '三亚',
          },
        },
      },
      expected: {
        cid: 43,
        cname: '三亚',
      },
    },
  ];
  test.each(mockStateMap)('getTempLocation check', ({ state, expected }) => {
    const data = getTempLocation(state);
    expect(data).toEqual(expected);
  });
});

describe('Area Selectors getCityId', () => {
  const mockStateMap = [
    {
      state: {
        Area: {
          tempLocation: {
            cid: 43,
            cname: '三亚',
          },
        },
      },
      expected: 43,
    },
  ];
  test.each(mockStateMap)('getCityId check', ({ state, expected }) => {
    const data = getCityId(state);
    expect(data).toEqual(expected);
  });
});

describe('Area Selectors getCityName', () => {
  const mockStateMap = [
    {
      state: {
        Area: {
          tempLocation: {
            cid: 43,
            cname: '三亚',
          },
        },
      },
      expected: '三亚',
    },
  ];
  test.each(mockStateMap)('getCityName check', ({ state, expected }) => {
    const data = getCityName(state);
    expect(data).toEqual(expected);
  });
});

describe('Area Selectors getApiQueryAreaFetchParam', () => {
  const mockStateMap = [
    {
      state: {
        Area: {
          tempLocation: {
            cid: 43,
            cname: '三亚',
          },
        },
      },
      expected: {
        cid: 43,
      },
    },
  ];
  test.each(mockStateMap)(
    'getApiQueryAreaFetchParam check',
    ({ state, expected }) => {
      const data = getApiQueryAreaFetchParam(state);
      expect(data).toEqual(expected);
    },
  );
});
