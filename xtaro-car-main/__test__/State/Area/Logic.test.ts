import { recordSaga } from '../../testHelpers';
import {
  getIsSuccess,
  queryAreaList,
  queryAreaListCallback,
  setTempLocation,
} from '../../../src/pages/xcar/State/Area/Logic';
import * as Types from '../../../src/pages/xcar/State/Area/Types';
import * as Actions from '../../../src/pages/xcar/State/Area/Actions';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';

const state = {
  Area: {
    tempLocation: {
      cid: 43,
      cname: '三亚',
      country: '中国',
    },
  },
};

const mockResData = {
  version: 3,
  ResponseStatus: {
    Extension: [
      { Value: '2698641476914568979', Id: 'CLOGGING_TRACE_ID' },
      { Value: '921822-0a050d7d-463856-70111', Id: 'RootMessageId' },
    ],
    Ack: 'Success',
    Errors: [],
    Timestamp: '/Date(1669884442122+0800)/',
  },
  areaList: [
    {
      areaInfoList: [
        {
          cen: '',
          aname: '凤凰国际机场',
          aid: '9035610',
          lon: '109.414693',
          cname: '三亚',
          lat: '18.303395',
          isDomestic: true,
          cid: '43',
        },
      ],
      showNum: 2,
      type: 1,
      name: '机场',
    },
    {
      areaInfoList: [
        {
          cen: '',
          aname: '凤凰机场站',
          aid: '245558',
          lon: '109.408787',
          cname: '三亚',
          lat: '18.308986',
          isDomestic: true,
          cid: '43',
        },
        {
          cen: '',
          aname: '三亚站',
          aid: '60508',
          lon: '109.492988',
          cname: '三亚',
          lat: '18.296274',
          isDomestic: true,
          cid: '43',
        },
        {
          cen: '',
          aname: '亚龙湾站',
          aid: '68150',
          lon: '109.602469',
          cname: '三亚',
          lat: '18.302156',
          isDomestic: true,
          cid: '43',
        },
        {
          cen: '',
          aname: '崖州站',
          aid: '83482',
          lon: '109.157536',
          cname: '三亚',
          lat: '18.376506',
          isDomestic: true,
          cid: '43',
        },
        {
          cen: '',
          aname: '南山北站',
          aid: '1293550',
          lon: '109.209641',
          cname: '三亚',
          lat: '18.317686',
          isDomestic: true,
          cid: '43',
        },
        {
          cen: '',
          aname: '天涯站',
          aid: '3118035',
          lon: '109.443691',
          cname: '三亚',
          lat: '18.313696',
          isDomestic: true,
          cid: '43',
        },
      ],
      showNum: 2,
      type: 2,
      name: '火车站',
    },
    {
      areaInfoList: [
        {
          cen: '',
          aname: '三亚汽车站',
          aid: '60513',
          lon: '109.50395',
          cname: '三亚',
          lat: '18.248582',
          isDomestic: true,
          cid: '43',
        },
        {
          cen: '',
          aname: '三亚客运西站',
          aid: '265977',
          lon: '109.434368',
          cname: '三亚',
          lat: '18.300029',
          isDomestic: true,
          cid: '43',
        },
        {
          cen: '',
          aname: '亚龙湾客运东站',
          aid: '2755112',
          lon: '109.603911',
          cname: '三亚',
          lat: '18.302277',
          isDomestic: true,
          cid: '43',
        },
        {
          cen: '',
          aname: '万科湖畔公交首末站',
          aid: '1480106',
          lon: '109.555483',
          cname: '三亚',
          lat: '18.278446',
          isDomestic: true,
          cid: '43',
        },
      ],
      showNum: 2,
      type: 9,
      name: '汽车站',
    },
  ],
  BaseResponse: {
    IsSuccess: true,
    ReturnMsg: 'success',
    Cost: 8,
    Code: 'unknown',
    RequestId: '195e4c0c-979f-45e7-ad4b-478d1daeedcc',
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    cacheKey:
      '13609%2FgetAreaList_M2252328224_%7B%22baseRequest%22%3A%7B%22sourceFrom%22%3A%22ISD_C_APP%22%2C%22channelType%22%3A7%2C%22invokeFrom%22%3A%22%22%2C%22platform%22%3A%22app_ctrip%22%2C%22site%22%3A%22cn%22%2C%22language%22%3A%22cn%22%2C%22locale%22%3A%22zh_cn%22%2C%22currencyCode%22%3A%22CNY%22%2C%22sourceCountryId%22%3A1%2C%22channelId%22%3A14277%2C%22clientVersion%22%3A%2220221201164005%22%2C%22clientid%22%3A%2212001076310000200437%22%2C%22vid%22%3A%221669884440704.li36vk%22%2C%22patternType%22%3A%2235%22%2C%22mobileInfo%22%3A%7B%22customerGPSLat%22%3A37.78583526611328%2C%22customerGPSLng%22%3A-122.**************%2C%22mobileModel%22%3A%22iPhone%20X_Simulator%22%2C%22wirelessVersion%22%3A%228.53.0%22%7D%2C%22allianceInfo%22%3A%7B%22allianceId%22%3A0%2C%22ouid%22%3A%221%22%2C%22sid%22%3A0%2C%22distributorUID%22%3A%221%22%7D%2C%22extraMaps%22%3A%7B%22batchVersion%22%3A%22%22%2C%22pageVersion%22%3A%22true%22%2C%22depositVersion%22%3A%221.0%22%2C%22creditVersion%22%3A%22%22%2C%22abVersion%22%3A%22220922_DSJT_nctr%7CA%22%2C%22partialVersion%22%3A%2220221201164005%22%2C%22queryProductsVersion%22%3A%22cashbackDemand%22%2C%22crnVersion%22%3A%2233%22%2C%22queryVid%22%3A%22c7461277-5ac0-4b95-968c-3b5b24205b56%22%2C%22sourceFrom%22%3A%22ISD_C_APP%22%2C%22platform%22%3A%22app_ctrip%22%2C%22onePost%22%3A%221%22%2C%22ctripVersion%22%3A1%2C%22insVersion%22%3A%221%22%2C%22encryptUid%22%3A%22%22%2C%22fromType%22%3A%22%22%2C%22originOrderId%22%3A%22%22%2C%22channelId%22%3A%22%22%2C%22eid%22%3A%22%22%2C%22commentVersion%22%3A1%2C%22ehaiDepositVersion%22%3A%221.0%22%2C%22priceUnitedVersion%22%3A%222%22%2C%22directRenewalVersion%22%3A%221%22%2C%22tangramAbt%22%3A%22B%22%2C%22snapshotVersion%22%3A%22v4%22%2C%22rentCenter%22%3A%221%22%2C%22detailPageVersion%22%3A%222%22%2C%22poiProject%22%3A%22B%22%2C%22goodsShelves%22%3A%222%22%2C%22membershipRightsV%22%3A%221%22%2C%22feeGroup%22%3A%221%22%2C%22isFilterPrice%22%3A%221%22%2C%22payOnlineVersion%22%3A%221%22%2C%22aboutToTravelVersion%22%3A%221%22%2C%22karabiVersion%22%3A%221%22%2C%22isNewRecommend%22%3A%221%22%2C%22orderDetailRestStruct%22%3A%221%22%2C%22insuranceDetail%22%3A0%2C%22vehicleDamageAuditVersion%22%3A%221%22%2C%22receiveCoupon%22%3A%221%22%2C%22filterProject%22%3A%22B%22%2C%22poiNewVersion%22%3A%222%22%2C%22rankingVersion%22%3A%222%22%2C%22groupNameVersion%22%3A%221%22%2C%22lateDepositVer%22%3A%223%22%2C%22correctStatus%22%3A%22%22%2C%22streamVersion%22%3Afalse%2C%22telVersion%22%3A%221%22%2C%22orderDetailCallBack%22%3A1%2C%22newPayment%22%3A1%2C%22labelOptimizeVer%22%3A%22%22%2C%22openAwardVersion%22%3A0%2C%22trip-tech-code%22%3A2%2C%22trip-os-code%22%3A0%2C%22trip-app-code%22%3A1%2C%22trip-business-code%22%3A1%2C%22trip-subBusiness-code%22%3A0%2C%22orderId%22%3A%22%22%2C%22isNewSearchNoResult%22%3A%221%22%2C%22isNewEnergy%22%3A%221%22%2C%22filterPoi%22%3A%220%22%7D%2C%22extraTags%22%3A%7B%22abVersion%22%3A%22220922_DSJT_nctr%7CA%22%2C%22ctripVersion%22%3A1%2C%22commentVersion%22%3A1%2C%22poiProject%22%3A%22B%22%2C%22aboutToTravelVersion%22%3A%221%22%2C%22poiNewVersion%22%3A%221%22%2C%22openAwardVersion%22%3A0%2C%22filterPoi%22%3A%220%22%7D%2C%22extMap%22%3A%7B%7D%2C%22pageId%22%3A%22%22%7D%2C%22cid%22%3A%2243%22%7D',
    groupId: '13609/getAreaList',
    networkCost: 169,
    environmentCost: 88,
    cacheFetchCost: 1,
    fetchCost: 169,
    setCacheCost: 2,
    cacheFrom: '',
    beforeFetch: 1669884442100,
    afterFetch: 1669884442269,
  },
};

describe('Test Area logic queryAreaList', () => {
  const data = {};
  const testLogicFn = async (result, data?) => {
    const api = jest
      .spyOn(CarFetch, 'queryAreaList')
      .mockReturnValueOnce(Promise.resolve(result));
    const dispatched = await recordSaga(queryAreaList, {
      action: {
        type: Types.FETCH_AREA_LIST,
        data,
      },
      state,
    });
    return { api, dispatched };
  };
  test('测试 queryAreaList', async () => {
    const { api, dispatched } = await testLogicFn(mockResData, data);
    expect(api).toBeCalled();
    expect(dispatched[0]).toEqual(Actions.setStatus({ isLoading: true }));
    expect(dispatched[1]).toEqual(
      Actions.fetchAreaListCallback({
        isError: false,
        param: { cid: 43 },
        res: mockResData,
      }),
    );
  });
});

describe('Test Area logic queryAreaListCallback', () => {
  const data = {
    res: mockResData,
    isError: false,
  };
  const testLogicFn = async data => {
    const dispatched = await recordSaga(queryAreaListCallback, {
      action: {
        type: Types.FETCH_AREA_LIST_CALLBACK,
        data,
      },
      state,
    });
    return dispatched;
  };
  test('测试 queryAreaListCallback', async () => {
    const dispatched = await testLogicFn(data);
    expect(dispatched[0]).toEqual(
      Actions.setStatus({ isLoading: false, isFail: false }),
    );
  });
});

describe('Test Area logic setTempLocation', () => {
  const data = {};
  const testLogicFn = async data => {
    const dispatched = await recordSaga(setTempLocation, {
      action: {
        type: Types.SET_TEMPLOCATION,
        data,
      },
      state,
    });
    return dispatched;
  };
  test('测试 setTempLocation', async () => {
    const dispatched = await testLogicFn(data);
    expect(dispatched[0]).toEqual(Actions.fetchAreaCache());
  });
});

describe('Test Area logic getIsSuccess', () => {
  test('test function getIsSuccess', () => {
    const result = getIsSuccess(mockResData);
    expect(result).toEqual(true);
  });
});
