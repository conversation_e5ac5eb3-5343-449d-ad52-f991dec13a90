import {
  setStatus,
  fetchAreaList,
  fetchAreaListCallback,
  setPickType,
  setTempLocation,
} from '../../../src/pages/xcar/State/Area/Actions';
import {
  SET_STATUS,
  FETCH_AREA_LIST,
  FETCH_AREA_LIST_CALLBACK,
  SET_PICKTYPE,
  SET_TEMPLOCATION,
} from '../../../src/pages/xcar/State/Area/Types';

describe('Area Actions setStatus', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: SET_STATUS,
        data,
      },
    },
  ];
  test.each(mockMap)('setStatus check', ({ data, expected }) => {
    const result = setStatus(data);
    expect(result).toEqual(expected);
  });
});

describe('Area Actions fetchAreaList', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: FETCH_AREA_LIST,
        data,
      },
    },
  ];
  test.each(mockMap)('fetchAreaList check', ({ data, expected }) => {
    const result = fetchAreaList(data);
    expect(result).toEqual(expected);
  });
});

describe('Area Actions fetchAreaListCallback', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: FETCH_AREA_LIST_CALLBACK,
        data,
      },
    },
  ];
  test.each(mockMap)('fetchAreaListCallback check', ({ data, expected }) => {
    const result = fetchAreaListCallback(data);
    expect(result).toEqual(expected);
  });
});

describe('Area Actions setPickType', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: SET_PICKTYPE,
        data,
      },
    },
  ];
  test.each(mockMap)('setPickType check', ({ data, expected }) => {
    const result = setPickType(data);
    expect(result).toEqual(expected);
  });
});

describe('Area Actions setTempLocation', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: SET_TEMPLOCATION,
        data,
      },
    },
  ];
  test.each(mockMap)('setTempLocation check', ({ data, expected }) => {
    const result = setTempLocation(data);
    expect(result).toEqual(expected);
  });
});
