import {
  getMetroStationList,
  getAreaList,
} from '../../../src/pages/xcar/State/Area/BbkMapper';

jest.mock('../../../src/pages/xcar/Global/Cache/AreaSelectors', () => ({
  getMetroStationList: jest.fn(() => []),
  getAreaList: jest.fn(() => {
    return [
      {
        type: 1,
        name: '机场',
        areaInfoList: [
          {
            cen: '',
            aname: '凤凰国际机场T1航站楼',
            aid: '1968',
            lon: '109.412010',
            lat: '18.307470',
            cid: '43',
            cname: '三亚',
            isDomestic: true,
          },
          {
            cen: '',
            aname: '凤凰国际机场T2航站楼',
            aid: '1969',
            lon: '109.413780',
            lat: '18.308030',
            cid: '43',
            cname: '三亚',
            isDomestic: true,
          },
        ],
      },
    ];
  }),
  getAreaListVersion: jest.fn(() => 2),
}));

describe('getMetroStationList', () => {
  test('getMetroStationList is empty', () => {
    const result = getMetroStationList();
    // 判断返回为空
    expect(result).toEqual([]);
  });
});

describe('getAreaList', () => {
  test('getAreaList mapper', () => {
    const result = getAreaList();
    // 判断返回为空
    expect(result).toEqual([
      {
        type: 1,
        name: '机场',
        areaInfoList: [
          {
            cen: '',
            aname: '凤凰国际机场T1航站楼',
            aid: '1968',
            lon: '109.412010',
            lat: '18.307470',
            cid: '43',
            cname: '三亚',
            isDomestic: true,
            label: '机场',
            text: '凤凰国际机场T1航站楼',
            enName: '',
            cityId: 43,
            version: 2,
            type: 1,
          },
          {
            cen: '',
            aname: '凤凰国际机场T2航站楼',
            aid: '1969',
            lon: '109.413780',
            lat: '18.308030',
            cid: '43',
            cname: '三亚',
            isDomestic: true,
            label: '机场',
            text: '凤凰国际机场T2航站楼',
            enName: '',
            cityId: 43,
            version: 2,
            type: 1,
          },
        ],
      },
    ]);
  });
});
