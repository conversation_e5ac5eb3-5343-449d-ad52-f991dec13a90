import AreaReducer, { getInitalState } from '../../../src/pages/xcar/State/Area/Reducer';

import {
  SET_STATUS,
  SET_PICKTYPE,
  SET_TEMPLOCATION,
  FETCH_AREA_LIST,
  FETCH_AREA_LIST_CALLBACK,
  FETCH_AREA_CACHE,
} from '../../../src/pages/xcar/State/Area/Types';

describe('AreaReducer Reducer Test', () => {
  const initState = getInitalState();

  // 验证初始化
  test('AreaReducer Reducer Init', () => {
    expect(AreaReducer(undefined, {})).toEqual(initState);
  });

  describe('DriverList Reducer SET_STATUS', () => {
    test('SET_STATUS set isLoading', () => {
      expect(
        AreaReducer(initState, {
          type: SET_STATUS,
          data: {
            isLoading: true,
          },
        }),
      ).toEqual({
        ...initState,
        isLoading: true,
      });
    });

    test('SET_STATUS set isFail', () => {
      expect(
        AreaReducer(initState, {
          type: SET_STATUS,
          data: {
            isFail: true,
          },
        }),
      ).toEqual({
        ...initState,
        isFail: true,
      });
    });
  });

  describe('DriverList Reducer SET_PICKTYPE', () => {
    test('SET_PICKTYPE', () => {
      expect(
        AreaReducer(initState, {
          type: SET_PICKTYPE,
          data: {
            pickType: 'dropOff',
          },
        }),
      ).toEqual({
        ...initState,
        pickType: 'dropOff',
      });
    });
  });

  describe('DriverList Reducer SET_TEMPLOCATION', () => {
    test('SET_TEMPLOCATION', () => {
      const tempLocation = {
        cid: 43,
        cname: '三亚',
      };
      expect(
        AreaReducer(initState, {
          type: SET_TEMPLOCATION,
          data: {
            tempLocation,
          },
        }),
      ).toEqual({
        ...initState,
        tempLocation,
      });
    });
  });

  describe('DriverList Reducer FETCH_AREA_LIST', () => {
    test('FETCH_AREA_LIST', () => {
      expect(
        AreaReducer(initState, {
          type: FETCH_AREA_LIST,
        }),
      ).toEqual({
        ...initState,
      });
    });
  });

  describe('DriverList Reducer FETCH_AREA_LIST_CALLBACK', () => {
    test('FETCH_AREA_LIST_CALLBACK', () => {
      expect(
        AreaReducer(initState, {
          type: FETCH_AREA_LIST_CALLBACK,
        }),
      ).toEqual({
        ...initState,
      });
    });
  });

  describe('DriverList Reducer FETCH_AREA_CACHE', () => {
    test('FETCH_AREA_CACHE', () => {
      expect(
        AreaReducer(initState, {
          type: FETCH_AREA_CACHE,
        }),
      ).toEqual({
        ...initState,
      });
    });
  });
});
