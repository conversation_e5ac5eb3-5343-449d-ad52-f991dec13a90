import MarketReducer from '../../../src/pages/xcar/State/Market/Reducers';
import { LOAD_COMPLETED, LOAD_FAILED } from '../../../src/pages/xcar/State/Market/Types';

describe('Market Reducer Test', () => {
  const initState = {
    replacePageData: null,
    loadData: null,
    gotoPageName: '',
  };

  test('Init', () => {
    expect(MarketReducer(undefined, {})).toEqual(initState);
  });

  test('LOAD_COMPLETED', () => {
    expect(
      MarketReducer(initState, {
        type: LOAD_COMPLETED,
        data: { gotoPageName: 'list', replacePageData: {} },
      }),
    ).toEqual({ ...initState, gotoPageName: 'list', replacePageData: {} });
  });

  test('LOAD_FAILED', () => {
    expect(
      MarketReducer(initState, {
        type: LOAD_FAILED,
        data: {
          err: 'err',
          landingto: 'list',
        },
      }),
    ).toEqual({
      ...initState,
      err: 'err',
      landingto: 'list',
    });
  });
});
