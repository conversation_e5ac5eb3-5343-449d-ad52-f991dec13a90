import {
  getPageName,
  getDefaultPageName,
  checkParams,
} from '../../../src/pages/xcar/State/Market/Helpers';
import { LANDING_PAGE } from '../../../src/pages/xcar/State/Market/Types';
import Channel from '../../../src/pages/xcar/Util/Channel';

describe('Market Helpers getPageName', () => {
  test('getPageName', () => {
    const landingTo = 'list';
    expect(getPageName(landingTo)).toEqual(Channel.getPageId().List.EN);
  });
  test('getPageName wrongPage', () => {
    const landingTo = 'wrongPage';
    expect(getPageName(landingTo)).toEqual(Channel.getPageId().Home.EN);
  });
});

describe('Market Helpers getDefaultPageName', () => {
  test('getDefaultPageName', () => {
    expect(getDefaultPageName()).toEqual(LANDING_PAGE.DEFAULT);
  });
});

describe('Market Helpers checkParams', () => {
  const mockData = {
    rentalDate: {
      pickUp: {
        dateTime: '20200612100000',
      },
      dropOff: {
        dateTime: '20200614100000',
      },
    },
    rentalLocation: {
      pickUp: {
        country: '中国',
        area: {
          lng: 110.343315,
          lat: 19.984078,
          id: '',
          name: '海口东站',
          type: '2',
        },

        cname: '海口',
        cid: '42',
      },
      dropOff: {
        country: '中国',
        area: {
          lng: 110.343315,
          lat: 19.984078,
          id: '',
          name: '海口东站',
          type: '2',
        },

        cname: '海口',
        cid: '42',
      },
      isShowDropOff: false,
    },
  };
  test('checkParams', () => {
    expect(checkParams(mockData)).toEqual(mockData.rentalLocation);
  });
});
