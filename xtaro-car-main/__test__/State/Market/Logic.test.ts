import { BbkChannel } from '../../../src/pages/xcar/Common/src/Utils';
import { recordSaga } from '../../testHelpers';
import { AppContext, CarLog, User, Utils } from '../../../src/pages/xcar/Util/Index';
import { LOAD } from '../../../src/pages/xcar/State/Market/Types';
import { LogKeyDev } from '../../../src/pages/xcar/Constants/Index';
import qs from 'qs';
import { load } from '../../../src/pages/xcar/State/Market/Logic';

jest.mock('../../../src/pages/xcar/Util/User', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Util/User') }));

jest.mock('../../../src/pages/xcar/Helpers/Index', () => ({
  postParentMessage: () => {},
}));

describe('Market Logic composeQsParse', () => {
  const map = [
    {
      input: 'channelId=1&aid=2&sid=3&channelId=1&aid=2&sid=4',
      output: { channelId: '1', aid: '2', sid: '4' },
    },
    {
      input: 'filters=[]&data={a:1}',
      output: { filters: '[]', data: '{a:1}' },
    },
  ];
  test.each(map)('composeQsParse', async ({ input, output }) => {
    expect(Utils.composeQsParse(input)).toEqual(output);
  });
});

describe('Market Logic load', () => {
  const fakeQuery = {
    CRNModuleName: 'rn_car_main',
    CRNType: '1',
    initialPage: 'Market',
    isHomeCombine: 'true',
    filters: '[]',
    fromurl: 'common',
    landingto: 'list',
    channelId: '17671',
    sid: '1',
    st: 'client',
    encryptUid: '123',
    vehgroupid: '1',
    fromType: '',
    pageChannel: 'ctrip_home_page',
    originOrderId: '',
    eid: '123',
    env: 'ctrip',
    modifyVendorOrderCode: '87980',
    originVendorId: '123',
    originalCouponCode: 'testCouponCode',
    originalActivityId: 'testId',
    originalActivityName: 'testName',
    klbVersion: '2',
    orignScenes: '3',
    isMarketing: 1,
    isHomeRanking: false,
  };
  const mockDataMap = [
    {
      data: {
        fakeUrl: qs.stringify(fakeQuery),
      },
      expected: {
        ...fakeQuery,
      },
    },
    {
      data: {
        fakeUrl: qs.stringify({ ...fakeQuery, landingto: 'list' }),
      },
      expected: {
        ...fakeQuery,
      },
    },
    {
      data: {
        fakeUrl: qs.stringify({ ...fakeQuery, st: 'ser' }),
      },
      expected: {
        ...fakeQuery,
      },
    },
  ];

  test.each(mockDataMap)('load', async ({ data, expected }) => {
    jest
      .spyOn(User, 'toLogin')
      .mockReturnValue(new Promise(resolve => resolve(true)));
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(false)));
    jest.spyOn(Utils, 'getUBT').mockImplementation(() => 'test');
    jest.spyOn(Utils, 'isNewHomeTab').mockImplementation(() => false);
    const replace = jest.fn();
    AppContext.setPageInstance({
      replace,
    });
    const dispatched = await recordSaga(load, {
      action: {
        type: LOAD,
        data: data,
      },
      state: {},
    });

    expect(AppContext.encryptUid).toEqual(expected.encryptUid);
    expect(AppContext.fromType).toEqual(expected.fromType);
    expect(AppContext.originOrderId).toEqual(expected.originOrderId);
    expect(AppContext.channelId).toEqual(expected.channelId);
    expect(AppContext.eid).toEqual(expected.eid);
    expect(AppContext.fatEnv).toEqual(expected.env);
    expect(AppContext.modifyVendorOrderCode).toEqual(
      expected.modifyVendorOrderCode,
    );
    expect(AppContext.originVendorId).toEqual(expected.originVendorId);
    expect(AppContext.originalCouponCode).toEqual(expected.originalCouponCode);
    expect(AppContext.originalActivityId).toEqual(expected.originalActivityId);
    expect(AppContext.originalActivityName).toEqual(
      expected.originalActivityName,
    );
    expect(AppContext.klbVersion).toEqual(Number(expected.klbVersion));
    expect(AppContext.orignScenes).toEqual(Number(expected.orignScenes));
    expect(AppContext.isMarketing).toEqual(Number(expected.isMarketing));
    const LogTraceDev = jest.spyOn(CarLog, 'LogTraceDev');
    // expect(LogTraceDev).toHaveBeenCalledWith({
    //   key: LogKeyDev.c_car_dev_trace_market_landingto,
    //   info: {
    //     landingto: expected.landingto,
    //   },
    // });
  });
});
