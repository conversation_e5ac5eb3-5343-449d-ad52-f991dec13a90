import { fetchDepositInfo } from '../../../src/pages/xcar/State/DepositFree/Actions';
import { FETCH_DEPOSIT_INFO } from '../../../src/pages/xcar/State/DepositFree/Types';

describe('DepositFree Actions fetchDepositInfo', () => {
  const data = {};
  const mockStateMap = [
    {
      data,
      expected: {
        type: FETCH_DEPOSIT_INFO,
        data,
      },
    },
  ];
  test.each(mockStateMap)('fetchDepositInfo check', ({ data, expected }) => {
    const result = fetchDepositInfo(data);
    expect(result).toEqual(expected);
  });
});
