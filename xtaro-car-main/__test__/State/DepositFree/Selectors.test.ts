import {
  getDepositeRule,
  getDepositeProcedures,
} from '../../../src/pages/xcar/State/DepositFree/Selectors';

describe('DepositFree Selectors getDepositeRule', () => {
  const mockStateMap = [
    {
      state: {
        DepositFree: {
          depositeRule: {
            content: [
              {
                title: '授权芝麻信用',
                desc: '最高享￥10000押金全免',
              },
            ],
            items: [
              {
                rowIndex: '1',
                columnIndex: '1',
                content: '芝麻信用分',
              },
              {
                rowIndex: '1',
                columnIndex: '2',
                content: '可享减免额度',
              },
              {
                rowIndex: '2',
                columnIndex: '1',
                content: '800分及以上',
              },
              {
                rowIndex: '2',
                columnIndex: '2',
                content: '减免¥10000',
              },
              {
                rowIndex: '2',
                columnIndex: '1',
                content: '700分-799分',
              },
              {
                rowIndex: '2',
                columnIndex: '2',
                content: '减免¥8000',
              },
              {
                rowIndex: '2',
                columnIndex: '1',
                content: '650分-699分',
              },
              {
                rowIndex: '2',
                columnIndex: '2',
                content: '减免¥6000',
              },
              {
                rowIndex: '3',
                columnIndex: '1',
                content: '600分-649分',
              },
              {
                rowIndex: '3',
                columnIndex: '2',
                content: '减免¥2000',
              },
              {
                rowIndex: '4',
                columnIndex: '1',
                content: '550分-599分',
              },
              {
                rowIndex: '4',
                columnIndex: '2',
                content: '减免¥1000',
              },
              {
                rowIndex: '5',
                columnIndex: '1',
                content: '550以下',
              },
              {
                rowIndex: '5',
                columnIndex: '2',
                content: '无法减免，需支付全额押金',
              },
            ],
            explainExt: {
              title: '免押服务说明',
              items: [
                {
                  title: {
                    stringObjs: [
                      {
                        content: '1.减免额度小于押金金额时，无法支持免押',
                      },
                    ],
                  },
                  desc: [
                    {
                      stringObjs: [
                        {
                          content:
                            '若您当前所选车型押金为￥2000元，而您的芝麻信用分对应的减免额度小于押金金额时，您将无法享受免押金服务，需要支付全额押金。',
                        },
                      ],
                    },
                    {
                      stringObjs: [
                        {
                          content:
                            '若您的芝麻信用分低于550分或综合评估不通过，您将无法享受免押金服务，需要支付全额押金。',
                        },
                      ],
                    },
                    {
                      stringObjs: [
                        {
                          content:
                            '芝麻信用为您减免的额度（如有），需要您授权相应金额的信用扣款额度。',
                        },
                      ],
                    },
                  ],
                },
                {
                  title: {
                    stringObjs: [
                      {
                        content: '2.信用扣款额度授权解除',
                      },
                    ],
                  },
                  desc: [
                    {
                      stringObjs: [
                        {
                          content:
                            '若您预订成功，预计将在您实际还车x天后解除您的信用额度授权（如有）。',
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          },
          depositeProcedures: [
            {
              title: '选择支持免押服务的车辆',
              items: [
                {
                  title: {
                    stringObjs: {
                      content: '选择免押车型',
                    },
                  },
                  desc: {
                    stringObjs: {
                      url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_1.png',
                    },
                  },
                },
                {
                  title: {
                    stringObjs: {
                      content: '选择免押金门店',
                    },
                  },
                  desc: {
                    stringObjs: {
                      url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_2.png',
                    },
                  },
                },
              ],
            },
            {
              title: '填写页授权享受芝麻免押',
              items: [
                {
                  title: {
                    stringObjs: {
                      content: '下单页进行芝麻预授权',
                    },
                  },
                  desc: {
                    stringObjs: {
                      url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_3.png',
                    },
                  },
                },
                {
                  title: {
                    stringObjs: {
                      content: '授权成功后可享免押服务',
                    },
                  },
                  desc: {
                    stringObjs: {
                      url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_4.png',
                    },
                  },
                },
              ],
            },
            {
              title: '下单时确认押金政策为免押',
              items: [
                {
                  desc: {
                    stringObjs: {
                      url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_5.png',
                    },
                  },
                },
              ],
            },
          ],
        },
      },
      expected: {
        content: [
          {
            title: '授权芝麻信用',
            desc: '最高享￥10000押金全免',
          },
        ],
        items: [
          {
            rowIndex: '1',
            columnIndex: '1',
            content: '芝麻信用分',
          },
          {
            rowIndex: '1',
            columnIndex: '2',
            content: '可享减免额度',
          },
          {
            rowIndex: '2',
            columnIndex: '1',
            content: '800分及以上',
          },
          {
            rowIndex: '2',
            columnIndex: '2',
            content: '减免¥10000',
          },
          {
            rowIndex: '2',
            columnIndex: '1',
            content: '700分-799分',
          },
          {
            rowIndex: '2',
            columnIndex: '2',
            content: '减免¥8000',
          },
          {
            rowIndex: '2',
            columnIndex: '1',
            content: '650分-699分',
          },
          {
            rowIndex: '2',
            columnIndex: '2',
            content: '减免¥6000',
          },
          {
            rowIndex: '3',
            columnIndex: '1',
            content: '600分-649分',
          },
          {
            rowIndex: '3',
            columnIndex: '2',
            content: '减免¥2000',
          },
          {
            rowIndex: '4',
            columnIndex: '1',
            content: '550分-599分',
          },
          {
            rowIndex: '4',
            columnIndex: '2',
            content: '减免¥1000',
          },
          {
            rowIndex: '5',
            columnIndex: '1',
            content: '550以下',
          },
          {
            rowIndex: '5',
            columnIndex: '2',
            content: '无法减免，需支付全额押金',
          },
        ],
        explainExt: {
          title: '免押服务说明',
          items: [
            {
              title: {
                stringObjs: [
                  {
                    content: '1.减免额度小于押金金额时，无法支持免押',
                  },
                ],
              },
              desc: [
                {
                  stringObjs: [
                    {
                      content:
                        '若您当前所选车型押金为￥2000元，而您的芝麻信用分对应的减免额度小于押金金额时，您将无法享受免押金服务，需要支付全额押金。',
                    },
                  ],
                },
                {
                  stringObjs: [
                    {
                      content:
                        '若您的芝麻信用分低于550分或综合评估不通过，您将无法享受免押金服务，需要支付全额押金。',
                    },
                  ],
                },
                {
                  stringObjs: [
                    {
                      content:
                        '芝麻信用为您减免的额度（如有），需要您授权相应金额的信用扣款额度。',
                    },
                  ],
                },
              ],
            },
            {
              title: {
                stringObjs: [
                  {
                    content: '2.信用扣款额度授权解除',
                  },
                ],
              },
              desc: [
                {
                  stringObjs: [
                    {
                      content:
                        '若您预订成功，预计将在您实际还车x天后解除您的信用额度授权（如有）。',
                    },
                  ],
                },
              ],
            },
          ],
        },
      },
    },
  ];
  test.each(mockStateMap)('getDepositeRule check', ({ state, expected }) => {
    const data = getDepositeRule(state);
    expect(data).toEqual(expected);
  });
});

describe('DepositFree Selectors getDepositeProcedures', () => {
  const mockStateMap = [
    {
      state: {
        DepositFree: {
          depositeRule: {
            content: [
              {
                title: '授权芝麻信用',
                desc: '最高享￥10000押金全免',
              },
            ],
            items: [
              {
                rowIndex: '1',
                columnIndex: '1',
                content: '芝麻信用分',
              },
              {
                rowIndex: '1',
                columnIndex: '2',
                content: '可享减免额度',
              },
              {
                rowIndex: '2',
                columnIndex: '1',
                content: '800分及以上',
              },
              {
                rowIndex: '2',
                columnIndex: '2',
                content: '减免¥10000',
              },
              {
                rowIndex: '2',
                columnIndex: '1',
                content: '700分-799分',
              },
              {
                rowIndex: '2',
                columnIndex: '2',
                content: '减免¥8000',
              },
              {
                rowIndex: '2',
                columnIndex: '1',
                content: '650分-699分',
              },
              {
                rowIndex: '2',
                columnIndex: '2',
                content: '减免¥6000',
              },
              {
                rowIndex: '3',
                columnIndex: '1',
                content: '600分-649分',
              },
              {
                rowIndex: '3',
                columnIndex: '2',
                content: '减免¥2000',
              },
              {
                rowIndex: '4',
                columnIndex: '1',
                content: '550分-599分',
              },
              {
                rowIndex: '4',
                columnIndex: '2',
                content: '减免¥1000',
              },
              {
                rowIndex: '5',
                columnIndex: '1',
                content: '550以下',
              },
              {
                rowIndex: '5',
                columnIndex: '2',
                content: '无法减免，需支付全额押金',
              },
            ],
            explainExt: {
              title: '免押服务说明',
              items: [
                {
                  title: {
                    stringObjs: [
                      {
                        content: '1.减免额度小于押金金额时，无法支持免押',
                      },
                    ],
                  },
                  desc: [
                    {
                      stringObjs: [
                        {
                          content:
                            '若您当前所选车型押金为￥2000元，而您的芝麻信用分对应的减免额度小于押金金额时，您将无法享受免押金服务，需要支付全额押金。',
                        },
                      ],
                    },
                    {
                      stringObjs: [
                        {
                          content:
                            '若您的芝麻信用分低于550分或综合评估不通过，您将无法享受免押金服务，需要支付全额押金。',
                        },
                      ],
                    },
                    {
                      stringObjs: [
                        {
                          content:
                            '芝麻信用为您减免的额度（如有），需要您授权相应金额的信用扣款额度。',
                        },
                      ],
                    },
                  ],
                },
                {
                  title: {
                    stringObjs: [
                      {
                        content: '2.信用扣款额度授权解除',
                      },
                    ],
                  },
                  desc: [
                    {
                      stringObjs: [
                        {
                          content:
                            '若您预订成功，预计将在您实际还车x天后解除您的信用额度授权（如有）。',
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          },
          depositeProcedures: [
            {
              title: '选择支持免押服务的车辆',
              items: [
                {
                  title: {
                    stringObjs: {
                      content: '选择免押车型',
                    },
                  },
                  desc: {
                    stringObjs: {
                      url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_1.png',
                    },
                  },
                },
                {
                  title: {
                    stringObjs: {
                      content: '选择免押金门店',
                    },
                  },
                  desc: {
                    stringObjs: {
                      url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_2.png',
                    },
                  },
                },
              ],
            },
            {
              title: '填写页授权享受芝麻免押',
              items: [
                {
                  title: {
                    stringObjs: {
                      content: '下单页进行芝麻预授权',
                    },
                  },
                  desc: {
                    stringObjs: {
                      url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_3.png',
                    },
                  },
                },
                {
                  title: {
                    stringObjs: {
                      content: '授权成功后可享免押服务',
                    },
                  },
                  desc: {
                    stringObjs: {
                      url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_4.png',
                    },
                  },
                },
              ],
            },
            {
              title: '下单时确认押金政策为免押',
              items: [
                {
                  desc: {
                    stringObjs: {
                      url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_5.png',
                    },
                  },
                },
              ],
            },
          ],
        },
      },
      expected: [
        {
          title: '选择支持免押服务的车辆',
          items: [
            {
              title: {
                stringObjs: {
                  content: '选择免押车型',
                },
              },
              desc: {
                stringObjs: {
                  url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_1.png',
                },
              },
            },
            {
              title: {
                stringObjs: {
                  content: '选择免押金门店',
                },
              },
              desc: {
                stringObjs: {
                  url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_2.png',
                },
              },
            },
          ],
        },
        {
          title: '填写页授权享受芝麻免押',
          items: [
            {
              title: {
                stringObjs: {
                  content: '下单页进行芝麻预授权',
                },
              },
              desc: {
                stringObjs: {
                  url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_3.png',
                },
              },
            },
            {
              title: {
                stringObjs: {
                  content: '授权成功后可享免押服务',
                },
              },
              desc: {
                stringObjs: {
                  url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_4.png',
                },
              },
            },
          ],
        },
        {
          title: '下单时确认押金政策为免押',
          items: [
            {
              desc: {
                stringObjs: {
                  url: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/osd_deposit_free_procedure_5.png',
                },
              },
            },
          ],
        },
      ],
    },
  ];
  test.each(mockStateMap)(
    'getDepositeProcedures check',
    ({ state, expected }) => {
      const data = getDepositeProcedures(state);
      expect(data).toEqual(expected);
    },
  );
});
