import uuid from 'uuid';
import {
  getAddPayments,
  getSupplementModalVisible
} from '../../../src/pages/xcar/State/Supplement/Selectors';

const mockState : any = {
  Supplement: null,
};

describe('Supplement Selectors getSupplementModalVisible', () => {
  const mockStateMap = [
    {
      state: {
        Supplement: {
          supplementModalVisible: true,
        },
      },
      expected: true,
    },
    {
      state: mockState,
      expected: undefined,
    },
    {
      state: {
        Supplement: {
          supplementModalVisible: false,
        },
      },
      expected: false,
    },
    {
      state: {
        Supplement: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getSupplementModalVisible check',
    ({ state, expected }) => {
      const data = getSupplementModalVisible(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Supplement Selectors getAddPayments', () => {
  const mockStateMap = [
    {
      state: {
        Supplement: {
          additionPaymentInfo: {},
        },
      },
      expected: {},
    },
    {
      state: mockState,
      expected: undefined,
    },
    {
      state: {
        OrderDetail: {
          additionPaymentInfo: {},
        },
      },
      expected: {},
    },
    {
      state: {
        OrderDetail: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getIsUseCancel check',
    ({ state, expected }) => {
      const data = getAddPayments(state);
      expect(data).toEqual(expected);
    },
  );
});
