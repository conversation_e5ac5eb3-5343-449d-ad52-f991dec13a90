import { fetchApISupplement, setSupplementModalVisible } from '../../../src/pages/xcar/State/Supplement/Actions';
import {
  SETSUPPLEMENTMODALVISIBLE,
} from '../../../src/pages/xcar/State/Supplement/Types';
import {
  QUERYADDITIONPAYMENT,
} from '../../../src/pages/xcar/State/OrderDetail/Types';
describe('Supplement Actions fetchApISupplement', () => {
  const data = {
    name: 134,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: QUERYADDITIONPAYMENT,
        data,
      },
    },
    {
      data: undefined,
      expected: {
        type: QUERYADDITIONPAYMENT,
        data: undefined,
      },
    },
  ];
  test.each(mockStateMap)(
    'fetchApISupplement check',
    ({ data, expected }) => {
      const result = fetchApISupplement(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('Supplement Actions setSupplementModalVisible', () => {
  const data = {
    name: 134,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: SETSUPPLEMENTMODALVISIBLE,
        data,
      },
    },
    {
      data: undefined,
      expected: {
        type: SETSUPPLEMENTMODALVISIBLE,
        data: undefined,
      },
    },
  ];
  test.each(mockStateMap)(
    'setMemberShipIsShow check',
    ({ data, expected }) => {
      const result = setSupplementModalVisible(data);
      expect(result).toEqual(expected);
    },
  );
});
