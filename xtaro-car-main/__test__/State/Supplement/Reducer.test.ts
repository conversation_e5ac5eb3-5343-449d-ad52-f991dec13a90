import Reducer, {initialState} from '../../../src/pages/xcar/State/Supplement/Reducer';
import * as Actions from '../../../src/pages/xcar/State/Supplement/Types';
describe('Reducer Test', () => {
  const initState = initialState;

  test('测试 initial state', () => {
    const data = Reducer(undefined, {
      type: Actions.INIT_SUPPLEMENT_STATE,
    });
    expect(data).toEqual(initialState);
  });

  test('测试 initial state', () => {
    const data = Reducer(undefined, {
      type: Actions.INIT_SUPPLEMENT_STATE,
    });
    expect(data).toEqual(initialState);
  });


  const actionMap = [{
    type: Actions.SETSUPPLEMENTMODALVISIBLE,
    data: {visible: true},
    expected: {
      ...initState,
      supplementModalVisible: true,
    }
  }];

  test.each(actionMap)('%p', ({ type, data, expected }) => {
    expect(Reducer(initState, { type, data })).toEqual(expected);
  })
})

