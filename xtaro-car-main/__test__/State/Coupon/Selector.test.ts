import {
  getAvailablePromotionData,
  getReceivePromotionTitle,
  getReceivePromotionData,
  getBirthPromotion,
  getAnchorPromotionId,
  getIsListReceiveSuccess,
  getHomeEntryReceivePromotionData,
  getListCouponEntryIsShow,
  getListCouponEntryData,
  getListCouponLogs,
  getListEntryIsReceiveAble,
  getCouponPreValidationModalVisible,
  getCouponPreValidationModalProps,
  getCouponPreValidationModalCode,
  getCouponPreValidationModalGroupCode,
  getCouponPreValidationModalTitle,
  getCouponPreValidationModalMsg,
} from '../../../src/pages/xcar/State/Coupon/Selectors';

describe('Coupon Selectors getReceivePromotionTitle', () => {
  const mockStateMap = [
    {
      state: {
        Coupon: {
          title: '展示标题',
        },
      },
      expected: '展示标题',
    },
    {
      state: {
        Coupon: {},
      },
      expected: '',
    },
    {
      state: {},
      expected: '',
    },
  ];
  test.each(mockStateMap)(
    'getReceivePromotionTitle check',
    ({ state, expected }) => {
      const data = getReceivePromotionTitle(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getReceivePromotionData', () => {
  const promotionFirst = { promotionId: '11111', name: '云南券' };
  const promotionSecond = { promotionId: '22222', name: '北京券' };
  const promotions = [promotionFirst, promotionSecond];
  const mockStateMap = [
    {
      state: {
        Coupon: {
          promotions,
        },
      },
      expected: promotions,
    },
    {
      state: {
        Coupon: {},
      },
      expected: [],
    },
    {
      state: {},
      expected: [],
    },
  ];
  test.each(mockStateMap)(
    'getReceivePromotionData check',
    ({ state, expected }) => {
      const data = getReceivePromotionData(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getBirthPromotion', () => {
  const promotionFirst = { promotionId: '11111', name: '云南券' };
  const promotionSecond = { promotionId: '22222', name: '北京券' };
  const birthPromotions = [promotionFirst, promotionSecond];
  const mockStateMap = [
    {
      state: {
        Coupon: {
          birthPromotions,
        },
      },
      expected: birthPromotions,
    },
    {
      state: {
        Coupon: {},
      },
      expected: [],
    },
    {
      state: {},
      expected: [],
    },
  ];
  test.each(mockStateMap)('getBirthPromotion check', ({ state, expected }) => {
    const data = getBirthPromotion(state);
    expect(data).toEqual(expected);
  });
});

describe('Coupon Selectors getAnchorPromotionId', () => {
  const mockStateMap = [
    {
      state: {
        Coupon: {
          anchorPromotionId: '999999',
        },
      },
      expected: '999999',
    },
    {
      state: {
        Coupon: {},
      },
      expected: '',
    },
    {
      state: {},
      expected: '',
    },
  ];
  test.each(mockStateMap)(
    'getAnchorPromotionId check',
    ({ state, expected }) => {
      const data = getAnchorPromotionId(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getIsListReceiveSuccess', () => {
  const mockStateMap = [
    {
      state: {
        Coupon: {
          isListReceiveSuccess: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Coupon: {},
      },
      expected: false,
    },
    {
      state: {},
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getIsListReceiveSuccess check',
    ({ state, expected }) => {
      const data = getIsListReceiveSuccess(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getHomeEntryReceivePromotionData', () => {
  const promotionFirst = { promotionId: '11111', name: '云南券', scenes: 2 };
  const promotionSecond = { promotionId: '22222', name: '北京券', scenes: 1 };
  const promotions = [promotionFirst, promotionSecond];
  const mockStateMap = [
    {
      state: {
        Coupon: {
          promotions,
        },
      },
      expected: [promotionSecond],
    },
    {
      state: {
        Coupon: {},
      },
      expected: [],
    },
    {
      state: {},
      expected: [],
    },
  ];
  test.each(mockStateMap)(
    'getHomeEntryReceivePromotionData check',
    ({ state, expected }) => {
      const data = getHomeEntryReceivePromotionData(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getListCouponEntryIsShow', () => {
  const promotionFirst = { promotionId: '11111', name: '云南券', scenes: 2 };
  const promotionSecond = { promotionId: '22222', name: '北京券', scenes: 1 };
  const promotions = [promotionFirst, promotionSecond];
  const mockStateMap = [
    {
      state: {
        Coupon: {
          promotions,
        },
      },
      expected: true,
    },
    {
      state: {
        Coupon: {},
      },
      expected: false,
    },
    {
      state: {},
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getListCouponEntryIsShow check',
    ({ state, expected }) => {
      const data = getListCouponEntryIsShow(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getListCouponEntryData', () => {
  const promotionFirst = {
    promotionId: '11111',
    name: '云南券',
    scenes: 2,
    briefName: '第一',
  };
  const promotionSecond = {
    promotionId: '22222',
    name: '北京券',
    scenes: 1,
    briefName: '第二',
  };
  const promotionThird = {
    promotionId: '33333',
    name: '北京券',
    scenes: 1,
    briefName: '第三',
  };
  const promotions1 = [promotionFirst, promotionSecond, promotionThird];
  const promotions2 = [
    promotionFirst,
    promotionFirst,
    promotionSecond,
    promotionThird,
  ];
  const mockStateMap = [
    {
      state: {
        Coupon: {
          promotions: promotions1,
        },
      },
      expected: ['第一', '第二'],
    },
    {
      state: {
        Coupon: {
          promotions: promotions2,
        },
      },
      expected: ['第一'],
    },
    {
      state: {
        Coupon: {},
      },
      expected: [],
    },
    {
      state: {},
      expected: [],
    },
  ];
  test.each(mockStateMap)(
    'getListCouponEntryData check',
    ({ state, expected }) => {
      const data = getListCouponEntryData(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getListCouponLogs', () => {
  const promotionFirst = {
    promotionId: '11111',
    name: '云南券',
    scenes: 2,
    briefName: '第一',
    status: 1,
  };
  const promotionSecond = {
    promotionId: '22222',
    name: '北京券',
    scenes: 1,
    briefName: '第二',
    status: 2,
  };
  const promotionThird = {
    promotionId: '33333',
    name: '北京券',
    scenes: 1,
    briefName: '第三',
    status: 3,
  };
  const promotions = [promotionFirst, promotionSecond, promotionThird];
  const mockStateMap = [
    {
      state: {
        Coupon: {
          promotions,
        },
      },
      expected: [
        {
          couponId: '11111',
          couponStatus: 1,
        },
        {
          couponId: '22222',
          couponStatus: 2,
        },
        {
          couponId: '33333',
          couponStatus: 3,
        },
      ],
    },
    {
      state: {
        Coupon: {},
      },
      expected: [],
    },
    {
      state: {},
      expected: [],
    },
  ];
  test.each(mockStateMap)('getListCouponLogs check', ({ state, expected }) => {
    const data = getListCouponLogs(state);
    expect(data).toEqual(expected);
  });
});

describe('Coupon Selectors getListEntryIsReceiveAble', () => {
  const promotionFirst = {
    promotionId: '11111',
    name: '云南券',
    scenes: 2,
    briefName: '第一',
    status: 1,
  };
  const promotionSecond = {
    promotionId: '22222',
    name: '北京券',
    scenes: 1,
    briefName: '第二',
    status: 2,
  };
  const promotionThird = {
    promotionId: '33333',
    name: '北京券',
    scenes: 1,
    briefName: '第三',
    status: 3,
  };
  const promotions = [promotionFirst, promotionSecond, promotionThird];
  const mockStateMap = [
    {
      state: {
        Coupon: {
          promotions,
        },
      },
      expected: true,
    },
    {
      state: {
        Coupon: {
          promotions: [promotionSecond, promotionThird],
        },
      },
      expected: false,
    },
    {
      state: {},
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getListEntryIsReceiveAble check',
    ({ state, expected }) => {
      const data = getListEntryIsReceiveAble(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getCouponPreValidationModalVisible', () => {
  const mockStateMap = [
    {
      state: {
        Coupon: {
          couponPreValidationModalVisible: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Coupon: {},
      },
      expected: undefined,
    },
    {
      state: {},
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getCouponPreValidationModalVisible check',
    ({ state, expected }) => {
      const data = getCouponPreValidationModalVisible(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getCouponPreValidationModalProps', () => {
  const content = {
    title: '展示标题',
    code: '1234',
    groupCode: '14444',
    msg: '展示消息',
  };
  const mockStateMap = [
    {
      state: {
        Coupon: {
          couponPreValidationModalProps: content,
        },
      },
      expected: content,
    },
    {
      state: {
        Coupon: {},
      },
      expected: undefined,
    },
    {
      state: {},
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getCouponPreValidationModalProps check',
    ({ state, expected }) => {
      const data = getCouponPreValidationModalProps(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getCouponPreValidationModalCode', () => {
  const content = {
    title: '展示标题',
    code: '1234',
    groupCode: '14444',
    msg: '展示消息',
  };
  const mockStateMap = [
    {
      state: {
        Coupon: {
          couponPreValidationModalProps: content,
        },
      },
      expected: '1234',
    },
    {
      state: {
        Coupon: {},
      },
      expected: undefined,
    },
    {
      state: {},
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getCouponPreValidationModalCode check',
    ({ state, expected }) => {
      const data = getCouponPreValidationModalCode(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getCouponPreValidationModalGroupCode', () => {
  const content = {
    title: '展示标题',
    code: '1234',
    groupCode: '14444',
    msg: '展示消息',
  };
  const mockStateMap = [
    {
      state: {
        Coupon: {
          couponPreValidationModalProps: content,
        },
      },
      expected: '14444',
    },
    {
      state: {
        Coupon: {},
      },
      expected: undefined,
    },
    {
      state: {},
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getCouponPreValidationModalGroupCode check',
    ({ state, expected }) => {
      const data = getCouponPreValidationModalGroupCode(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getCouponPreValidationModalTitle', () => {
  const content = {
    title: '展示标题',
    code: '1234',
    groupCode: '14444',
    msg: '展示消息',
  };
  const mockStateMap = [
    {
      state: {
        Coupon: {
          couponPreValidationModalProps: content,
        },
      },
      expected: '展示标题',
    },
    {
      state: {
        Coupon: {},
      },
      expected: undefined,
    },
    {
      state: {},
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getCouponPreValidationModalTitle check',
    ({ state, expected }) => {
      const data = getCouponPreValidationModalTitle(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selectors getCouponPreValidationModalMsg', () => {
  const content = {
    title: '展示标题',
    code: '1234',
    groupCode: '14444',
    msg: '展示消息',
  };
  const mockStateMap = [
    {
      state: {
        Coupon: {
          couponPreValidationModalProps: content,
        },
      },
      expected: '展示消息',
    },
    {
      state: {
        Coupon: {},
      },
      expected: undefined,
    },
    {
      state: {},
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getCouponPreValidationModalMsg check',
    ({ state, expected }) => {
      const data = getCouponPreValidationModalMsg(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Coupon Selector getAvailablePromotionData', () => {
  test('getAvailablePromotionData', () => {
    const state = {
      Coupon: {
        promotions: [
          {
            promotionSecretId:
              '61271bd2bd873deebc5eff1de686b5dc2b63e081236ee74add7032ccaeae7c06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
            currencyCode: '¥',
            value: '30',
            valueType: 0,
            discountText: '',
            limitText: '',
            name: '周三租车携程优选满减券',
            validPeriodDesc: '2022-11-30生效',
            cornerText: '',
            shortDesc: '【周三福利日】',
            longDesc:
              '（1）使用范围：通过携程国内自驾租车频道，以预付方式（在线支付）预订带有“携程优选”标签的国内租车产品可享优惠；\\\\（2）券有效期：优惠券仅限2022年11月30日当天下单可用，无取还车时间限制；\\\\（3）优惠金额：享车辆租金满31元减30元的优惠，优惠不含手续费、基础服务费、租车保障费、异地还车费等其他费用，且不与其他平台优惠券同享；\\\\（4）使用限制：优惠券每位用户仅限领取1张，同一设备号，手机号，uid均视为同一用户，每个订单限用一张优惠券，仅限本人使用，领取的优惠券不可累加，不可找零，不可折现，不退差额，不可拆分使用，续租按非优惠价收费；\\\\（5）退改说明：使用优惠券的订单取消、超时未支付或购买失败，若该券尚未失效将退回原账户，若该券已失效不予退回。',
            status: 1,
            statusName: '领取',
            invaildReason: '',
            deductionList: ['满¥31减¥30'],
            isActive: true,
            briefName: '满¥31减¥30',
            scenes: 1,
            promotionId: 754775878,
            couponCode: '',
          },
          {
            promotionSecretId:
              '20e8901c7d8c0aa74c65b29e3fbb841e65ca505cd236e8ac2487ad0f9c1b3e06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
            currencyCode: '',
            value: '7.9',
            valueType: 0,
            discountText: '折',
            limitText: '',
            name: '国内租车79折立减券',
            validPeriodDesc: '领取后3天内有效',
            cornerText: '',
            shortDesc: '携程租车中心专享',
            longDesc:
              '1、使用范围：适用于携程租车频道，以在线预付方式预订携程租车中心门店车型时可用（海南不可用），目前适用城市：北京、武汉、贵阳、福州、郑州、哈尔滨、兰州。\n\\\\2、券有效期：领取后3天内使用有效，取、还车时间在节假日用车高峰期不可用（国庆9月30日~10月7日、元旦12月31日~1月2日、春节1月20日~1月27日），过期自动失效。\n\\\\3、优惠金额：租车费享79折优惠，优惠金额上限188元（不含手续费、基础服务费、优享服务费、异地还车费等费用）。\n\\\\4、使用限制：每张优惠券仅限使用一次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券。\n\\\\5、使用方式：在填写订单页勾选优惠券，下单时即可扣减相应优惠金额，已完成支付的订单不能使用此券。\n\\\\6、退赔说明：\n\\\\1）使用优惠券的订单取消、超时未支付或购买失败的，若该券尚未失效将退回原账户，若该券已失效不予退回；\n\\\\2）使用优惠券的订单部分退改，导致订单金额减少的，按退改订单优惠前的金额享受相应优惠，扣除优惠差额后退款，优惠券不退；\n\\\\3）使用优惠券的订单变更，导致订单金额增加的，仅可享受变更前的优惠金额。',
            status: 1,
            statusName: '领取',
            invaildReason: '',
            deductionList: [],
            isActive: true,
            briefName: '立享7.9折',
            scenes: 1,
            promotionId: 243518831,
            couponCode: '',
          },
          {
            promotionSecretId: '',
            currencyCode: '¥',
            value: '668',
            valueType: 0,
            discountText: '',
            limitText: '最高减',
            name: '租车新客专享满减券',
            validPeriodDesc: '有效期至2022-12-02',
            cornerText: '新客券',
            shortDesc: '国内租车·最高减668元',
            longDesc:
              '1.优惠券仅限携程租车新用户以预付方式（在线支付）预订国内租车产品，可享租车费立减优惠（满100-12/满200-24/满400-48/满700-84/满1000-120/满5000-668）；\\\\2.优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\3.优惠券有效期7天，从发放日开始计算，过期未使用则自动失效；\\\\4.支持网页、APP、小程序用券，每张订单限用一张优惠券，每张优惠券限用一次，不可与其他优惠和活动同享。优惠券不可折现、不退差额、不可拆分使用，续租按非优惠价收费。',
            status: 2,
            statusName: '已领取',
            invaildReason: '',
            deductionList: [
              '满¥100减¥12',
              '满¥200减¥24',
              '满¥400减¥48',
              '满¥1000减¥120',
              '满¥5000减¥668',
            ],
            isActive: true,
            briefName: '最高减¥668',
            scenes: 1,
            promotionId: 585623006,
            couponCode: 'lddtjrbzwp',
          },
        ],
      },
    };
    const expected = [
      {
        promotionSecretId:
          '61271bd2bd873deebc5eff1de686b5dc2b63e081236ee74add7032ccaeae7c06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
        currencyCode: '¥',
        value: '30',
        valueType: 0,
        discountText: '',
        limitText: '',
        name: '周三租车携程优选满减券',
        validPeriodDesc: '2022-11-30生效',
        cornerText: '',
        shortDesc: '【周三福利日】',
        longDesc:
          '（1）使用范围：通过携程国内自驾租车频道，以预付方式（在线支付）预订带有“携程优选”标签的国内租车产品可享优惠；\\\\（2）券有效期：优惠券仅限2022年11月30日当天下单可用，无取还车时间限制；\\\\（3）优惠金额：享车辆租金满31元减30元的优惠，优惠不含手续费、基础服务费、租车保障费、异地还车费等其他费用，且不与其他平台优惠券同享；\\\\（4）使用限制：优惠券每位用户仅限领取1张，同一设备号，手机号，uid均视为同一用户，每个订单限用一张优惠券，仅限本人使用，领取的优惠券不可累加，不可找零，不可折现，不退差额，不可拆分使用，续租按非优惠价收费；\\\\（5）退改说明：使用优惠券的订单取消、超时未支付或购买失败，若该券尚未失效将退回原账户，若该券已失效不予退回。',
        status: 1,
        statusName: '领取',
        invaildReason: '',
        deductionList: ['满¥31减¥30'],
        isActive: true,
        briefName: '满¥31减¥30',
        scenes: 1,
        promotionId: 754775878,
        couponCode: '',
      },
      {
        promotionSecretId:
          '20e8901c7d8c0aa74c65b29e3fbb841e65ca505cd236e8ac2487ad0f9c1b3e06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
        currencyCode: '',
        value: '7.9',
        valueType: 0,
        discountText: '折',
        limitText: '',
        name: '国内租车79折立减券',
        validPeriodDesc: '领取后3天内有效',
        cornerText: '',
        shortDesc: '携程租车中心专享',
        longDesc:
          '1、使用范围：适用于携程租车频道，以在线预付方式预订携程租车中心门店车型时可用（海南不可用），目前适用城市：北京、武汉、贵阳、福州、郑州、哈尔滨、兰州。\n\\\\2、券有效期：领取后3天内使用有效，取、还车时间在节假日用车高峰期不可用（国庆9月30日~10月7日、元旦12月31日~1月2日、春节1月20日~1月27日），过期自动失效。\n\\\\3、优惠金额：租车费享79折优惠，优惠金额上限188元（不含手续费、基础服务费、优享服务费、异地还车费等费用）。\n\\\\4、使用限制：每张优惠券仅限使用一次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券。\n\\\\5、使用方式：在填写订单页勾选优惠券，下单时即可扣减相应优惠金额，已完成支付的订单不能使用此券。\n\\\\6、退赔说明：\n\\\\1）使用优惠券的订单取消、超时未支付或购买失败的，若该券尚未失效将退回原账户，若该券已失效不予退回；\n\\\\2）使用优惠券的订单部分退改，导致订单金额减少的，按退改订单优惠前的金额享受相应优惠，扣除优惠差额后退款，优惠券不退；\n\\\\3）使用优惠券的订单变更，导致订单金额增加的，仅可享受变更前的优惠金额。',
        status: 1,
        statusName: '领取',
        invaildReason: '',
        deductionList: [],
        isActive: true,
        briefName: '立享7.9折',
        scenes: 1,
        promotionId: 243518831,
        couponCode: '',
      },
    ];
    // jest.mock();
    expect(getAvailablePromotionData(state)).toEqual(expected);
  });
});
