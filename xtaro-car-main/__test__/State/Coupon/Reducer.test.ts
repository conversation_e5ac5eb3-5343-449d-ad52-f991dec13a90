import CouponReducer, {
  getInitalState,
} from '../../../src/pages/xcar/State/Coupon/Reducer';
import {
  FETCH_RECEIVE_PROMOTION_CALLBACK,
  SET_IS_LIST_RECEIVE_SUCCESS,
  RECEIVE_PROMOTION_CALLBACK,
  SET_ANCHOR_PROMOTION_ID,
  COUPON_RESET,
  SET_COUPON_PREVALIDATION_MODAL_VISIBLE,
  RECEIVE_ALL_PROMOTION_CALLBACK,
} from '../../../src/pages/xcar/State/Coupon/Types';

describe('Coupon Reducer Test', () => {
  const initState = getInitalState();

  // 验证初始化
  test('Coupon Reducer Init', () => {
    expect(CouponReducer(undefined, { type: '' })).toEqual(initState);
  });

  describe('Coupon Reducer FETCH_RECEIVE_PROMOTION_CALLBACK', () => {
    test('FETCH_RECEIVE_PROMOTION_CALLBACK', () => {
      const mockData = [
        {
          shortDesc: '满200减100元·部分产品可用',
          status: 1,
          limitText: '',
          deductionList: ['满¥200减¥100'],
          valueType: 0,
          isActive: true,
          briefName: '满¥200减¥100',
          promotionSecretId:
            '5fb45816116e602c793779a8b790f5ac1e546dc460fdd35cddd0d6b961e5e8aa45bdbc7bdc5e42297fc430ab9bb4e777',
          name: '贵州文旅消费自驾租车满减券',
          validPeriodDesc: '领取后4天内有效',
          cornerText: '',
          currencyCode: '¥',
          couponCode: '',
          invaildReason: '',
          discountText: '',
          value: '100',
          scenes: 1,
          statusName: '领取',
          promotionId: 972862202,
          longDesc:
            '适用范围：适用于携程租车频道，以预付方式（在线支付）预订贵州省内部分国内租车产可用，具体以列表页展示为准；\\\\券有效期：优惠券领取后5天内使用有效，预订和取车时间在2022年8月31日（含）前的产品可用，领取未使用过期则自动失效；\\\\门槛阶梯：租车费满200元立减100元优惠（不含手续费、基础服务费、优享服务费、异地还车费等费用）； \\\\使用限制：有效期内同一用户仅限领取和使用1张优惠券，每张优惠券仅限使用一次，仅限本人使用，不拆分、不找零、不转赠、不可叠加其他优惠券； \\\\退赔说明：订单发生全额退款时，尚在有效期内的消费券可在指定活动商家再次使用，退款后的消费券有效期不变；如退款时消费券超出有效期，消费券无法再次使用。',
        },
        {
          shortDesc: '携程租车中心专享',
          status: 1,
          limitText: '',
          deductionList: [],
          valueType: 0,
          isActive: true,
          briefName: '立享7.9折',
          promotionSecretId:
            '20e8901c7d8c0aa74c65b29e3fbb841e7e28faa22cdc91e159e247680dc8298c45bdbc7bdc5e42297fc430ab9bb4e777',
          name: '国内租车79折立减券',
          validPeriodDesc: '领取后3天内有效',
          cornerText: '',
          currencyCode: '',
          couponCode: '',
          invaildReason: '',
          discountText: '折',
          value: '7.9',
          scenes: 1,
          statusName: '领取',
          promotionId: 243518831,
          longDesc:
            '1、使用范围：适用于携程租车频道，以在线预付方式预订携程租车中心门店车型时可用（海南不可用），目前适用城市：北京、武汉、贵阳、福州、郑州、哈尔滨、兰州。\n\\\\2、券有效期：领取后3天内使用有效，取、还车时间在节假日用车高峰期不可用（中秋9月10日~9月12日、国庆9月30日~10月7日、元旦12月31日~1月2日、春节1月20日~1月27日），过期自动失效。\n\\\\3、优惠金额：租车费享79折优惠，优惠金额上限188元（不含手续费、基础服务费、优享服务费、异地还车费等费用）。\n\\\\4、使用限制：每张优惠券仅限使用一次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券。\n\\\\5、使用方式：在填写订单页勾选优惠券，下单时即可扣减相应优惠金额，已完成支付的订单不能使用此券。\n\\\\6、退赔说明：\n\\\\1）使用优惠券的订单取消、超时未支付或购买失败的，若该券尚未失效将退回原账户，若该券已失效不予退回；\n\\\\2）使用优惠券的订单部分退改，导致订单金额减少的，按退改订单优惠前的金额享受相应优惠，扣除优惠差额后退款，优惠券不退；\n\\\\3）使用优惠券的订单变更，导致订单金额增加的，仅可享受变更前的优惠金额。',
        },
        {
          shortDesc: '新用户专享',
          status: 7,
          limitText: '最高减',
          deductionList: [
            '满¥100减¥12',
            '满¥200减¥24',
            '满¥400减¥48',
            '满¥1000减¥120',
            '满¥5000减¥668',
          ],
          valueType: 0,
          isActive: false,
          briefName: '最高减¥668',
          promotionSecretId: '',
          name: '国内租车满减券',
          validPeriodDesc: '领取后7天内有效',
          cornerText: '',
          currencyCode: '¥',
          couponCode: '',
          invaildReason: '限新客可领',
          discountText: '',
          value: '668',
          scenes: 0,
          statusName: '',
          promotionId: 585623006,
          longDesc:
            '1.优惠券仅限携程租车新用户以预付方式（在线支付）预订国内租车产品，可享租车费立减优惠（满100-12/满200-24/满400-48/满700-84/满1000-120/满5000-668）；\\\\2.优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\3.优惠券有效期7天，从发放日开始计算，过期未使用则自动失效；\\\\4.支持网页、APP、小程序用券，每张订单限用一张优惠券，每张优惠券限用一次，不可与其他优惠和活动同享。优惠券不可折现、不退差额、不可拆分使用，续租按非优惠价收费。',
        },
      ];

      expect(
        CouponReducer(initState, {
          type: FETCH_RECEIVE_PROMOTION_CALLBACK,
          data: {
            promotions: mockData,
          },
        }),
      ).toEqual({
        ...initState,
        promotions: mockData,
      });
    });
  });

  describe('Coupon Reducer SET_IS_LIST_RECEIVE_SUCCESS', () => {
    test('SET_IS_LIST_RECEIVE_SUCCESS', () => {
      expect(
        CouponReducer(initState, {
          type: SET_IS_LIST_RECEIVE_SUCCESS,
          data: true,
        }),
      ).toEqual({
        ...initState,
        isListReceiveSuccess: true,
      });
    });
  });

  describe('Coupon Reducer RECEIVE_PROMOTION_CALLBACK', () => {
    const defaultState: any = {};
    const promotionFirst = { promotionId: '11111', name: '云南券' };
    const promotionSecond = { promotionId: '22222', name: '北京券' };
    const promotions = [promotionFirst, promotionSecond];
    const mockState = { promotions };
    const receiveFirst = { promotionId: '22222', name: '天津券' };
    const receiveSecond = { promotionId: '33333', name: '天津券' };
    const mockActionMap = [
      {
        mockState: getInitalState(),
        data: receiveFirst,
        expected: getInitalState(),
      },
      {
        mockState,
        data: receiveFirst,
        expected: {
          ...mockState,
          promotions: [promotionFirst, receiveFirst],
        },
      },
      {
        mockState,
        data: receiveSecond,
        expected: {
          ...mockState,
          promotions,
        },
      },
    ];
    test.each(mockActionMap)(
      'RECEIVE_PROMOTION_CALLBACK check',
      ({ mockState = defaultState, data, expected }) => {
        const result = CouponReducer(mockState, {
          type: RECEIVE_PROMOTION_CALLBACK,
          data,
        });
        expect(result).toEqual(expected);
      },
    );
  });

  describe('Coupon Reducer SET_ANCHOR_PROMOTION_ID', () => {
    const mockActionMap = [
      {
        data: '0000000',
        expected: {
          ...initState,
          anchorPromotionId: '0000000',
        },
      },
      {
        expected: {
          ...initState,
          anchorPromotionId: undefined,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_ANCHOR_PROMOTION_ID check',
      ({ data, expected }) => {
        expect(
          CouponReducer(initState, {
            type: SET_ANCHOR_PROMOTION_ID,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('Coupon Reducer COUPON_RESET', () => {
    const mockActionMap = [
      {
        data: '0000000',
        expected: {
          ...initState,
          anchorPromotionId: '',
        },
      },
      {
        expected: {
          ...initState,
          anchorPromotionId: '',
        },
      },
    ];
    test.each(mockActionMap)('COUPON_RESET check', ({ data, expected }) => {
      expect(
        CouponReducer(initState, {
          type: COUPON_RESET,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('Coupon Reducer SET_COUPON_PREVALIDATION_MODAL_VISIBLE', () => {
    const content = {
      title: '展示标题',
      code: '',
      groupCode: '',
      msg: '',
    };
    const mockActionMap = [
      {
        data: {
          visible: true,
          content,
        },
        expected: {
          ...initState,
          couponPreValidationModalVisible: true,
          couponPreValidationModalProps: content,
        },
      },
      {
        data: {},
        expected: {
          ...initState,
          couponPreValidationModalVisible: undefined,
          couponPreValidationModalProps: undefined,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_COUPON_PREVALIDATION_MODAL_VISIBLE check',
      ({ data, expected }) => {
        expect(
          CouponReducer(initState, {
            type: SET_COUPON_PREVALIDATION_MODAL_VISIBLE,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('Coupon Reducer RECEIVE_ALL_PROMOTION_CALLBACK', () => {
    const init: any = {
      ...initState,
      promotions: [
        {
          promotionSecretId:
            '61271bd2bd873deebc5eff1de686b5dc2b63e081236ee74add7032ccaeae7c06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
          currencyCode: '¥',
          value: '30',
          valueType: 0,
          discountText: '',
          limitText: '',
          name: '周三租车携程优选满减券',
          validPeriodDesc: '2022-11-30生效',
          cornerText: '',
          shortDesc: '【周三福利日】',
          longDesc:
            '（1）使用范围：通过携程国内自驾租车频道，以预付方式（在线支付）预订带有“携程优选”标签的国内租车产品可享优惠；\\\\（2）券有效期：优惠券仅限2022年11月30日当天下单可用，无取还车时间限制；\\\\（3）优惠金额：享车辆租金满31元减30元的优惠，优惠不含手续费、基础服务费、租车保障费、异地还车费等其他费用，且不与其他平台优惠券同享；\\\\（4）使用限制：优惠券每位用户仅限领取1张，同一设备号，手机号，uid均视为同一用户，每个订单限用一张优惠券，仅限本人使用，领取的优惠券不可累加，不可找零，不可折现，不退差额，不可拆分使用，续租按非优惠价收费；\\\\（5）退改说明：使用优惠券的订单取消、超时未支付或购买失败，若该券尚未失效将退回原账户，若该券已失效不予退回。',
          status: 1,
          statusName: '领取',
          invaildReason: '',
          deductionList: ['满¥31减¥30'],
          isActive: true,
          briefName: '满¥31减¥30',
          scenes: 1,
          promotionId: 754775878,
          couponCode: '',
        },
        {
          promotionSecretId:
            '20e8901c7d8c0aa74c65b29e3fbb841e65ca505cd236e8ac2487ad0f9c1b3e06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
          currencyCode: '',
          value: '7.9',
          valueType: 0,
          discountText: '折',
          limitText: '',
          name: '国内租车79折立减券',
          validPeriodDesc: '领取后3天内有效',
          cornerText: '',
          shortDesc: '携程租车中心专享',
          longDesc:
            '1、使用范围：适用于携程租车频道，以在线预付方式预订携程租车中心门店车型时可用（海南不可用），目前适用城市：北京、武汉、贵阳、福州、郑州、哈尔滨、兰州。\n\\\\2、券有效期：领取后3天内使用有效，取、还车时间在节假日用车高峰期不可用（国庆9月30日~10月7日、元旦12月31日~1月2日、春节1月20日~1月27日），过期自动失效。\n\\\\3、优惠金额：租车费享79折优惠，优惠金额上限188元（不含手续费、基础服务费、优享服务费、异地还车费等费用）。\n\\\\4、使用限制：每张优惠券仅限使用一次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券。\n\\\\5、使用方式：在填写订单页勾选优惠券，下单时即可扣减相应优惠金额，已完成支付的订单不能使用此券。\n\\\\6、退赔说明：\n\\\\1）使用优惠券的订单取消、超时未支付或购买失败的，若该券尚未失效将退回原账户，若该券已失效不予退回；\n\\\\2）使用优惠券的订单部分退改，导致订单金额减少的，按退改订单优惠前的金额享受相应优惠，扣除优惠差额后退款，优惠券不退；\n\\\\3）使用优惠券的订单变更，导致订单金额增加的，仅可享受变更前的优惠金额。',
          status: 1,
          statusName: '领取',
          invaildReason: '',
          deductionList: [],
          isActive: true,
          briefName: '立享7.9折',
          scenes: 1,
          promotionId: 243518831,
          couponCode: '',
        },
        {
          promotionSecretId: '',
          currencyCode: '¥',
          value: '668',
          valueType: 0,
          discountText: '',
          limitText: '最高减',
          name: '租车新客专享满减券',
          validPeriodDesc: '有效期至2022-12-02',
          cornerText: '新客券',
          shortDesc: '国内租车·最高减668元',
          longDesc:
            '1.优惠券仅限携程租车新用户以预付方式（在线支付）预订国内租车产品，可享租车费立减优惠（满100-12/满200-24/满400-48/满700-84/满1000-120/满5000-668）；\\\\2.优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\3.优惠券有效期7天，从发放日开始计算，过期未使用则自动失效；\\\\4.支持网页、APP、小程序用券，每张订单限用一张优惠券，每张优惠券限用一次，不可与其他优惠和活动同享。优惠券不可折现、不退差额、不可拆分使用，续租按非优惠价收费。',
          status: 2,
          statusName: '已领取',
          invaildReason: '',
          deductionList: [
            '满¥100减¥12',
            '满¥200减¥24',
            '满¥400减¥48',
            '满¥1000减¥120',
            '满¥5000减¥668',
          ],
          isActive: true,
          briefName: '最高减¥668',
          scenes: 1,
          promotionId: 585623006,
          couponCode: 'lddtjrbzwp',
        },
      ],
    };
    const data = [
      {
        promotionSecretId:
          '61271bd2bd873deebc5eff1de686b5dc2b63e081236ee74add7032ccaeae7c06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
        currencyCode: '¥',
        value: '30',
        valueType: 0,
        discountText: '',
        limitText: '',
        name: '周三租车携程优选满减券',
        validPeriodDesc: '2022-11-30生效',
        cornerText: '',
        shortDesc: '【周三福利日】',
        longDesc:
          '（1）使用范围：通过携程国内自驾租车频道，以预付方式（在线支付）预订带有“携程优选”标签的国内租车产品可享优惠；\\\\（2）券有效期：优惠券仅限2022年11月30日当天下单可用，无取还车时间限制；\\\\（3）优惠金额：享车辆租金满31元减30元的优惠，优惠不含手续费、基础服务费、租车保障费、异地还车费等其他费用，且不与其他平台优惠券同享；\\\\（4）使用限制：优惠券每位用户仅限领取1张，同一设备号，手机号，uid均视为同一用户，每个订单限用一张优惠券，仅限本人使用，领取的优惠券不可累加，不可找零，不可折现，不退差额，不可拆分使用，续租按非优惠价收费；\\\\（5）退改说明：使用优惠券的订单取消、超时未支付或购买失败，若该券尚未失效将退回原账户，若该券已失效不予退回。',
        status: 3,
        statusName: '已领取',
        invaildReason: '',
        deductionList: ['满¥31减¥30'],
        isActive: false,
        briefName: '满¥31减¥30',
        scenes: 1,
        promotionId: 754775878,
        couponCode: '',
      },
      {
        promotionSecretId:
          '20e8901c7d8c0aa74c65b29e3fbb841e65ca505cd236e8ac2487ad0f9c1b3e06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
        currencyCode: '',
        value: '7.9',
        valueType: 0,
        discountText: '折',
        limitText: '',
        name: '国内租车79折立减券',
        validPeriodDesc: '领取后3天内有效',
        cornerText: '',
        shortDesc: '携程租车中心专享',
        longDesc:
          '1、使用范围：适用于携程租车频道，以在线预付方式预订携程租车中心门店车型时可用（海南不可用），目前适用城市：北京、武汉、贵阳、福州、郑州、哈尔滨、兰州。\n\\\\2、券有效期：领取后3天内使用有效，取、还车时间在节假日用车高峰期不可用（国庆9月30日~10月7日、元旦12月31日~1月2日、春节1月20日~1月27日），过期自动失效。\n\\\\3、优惠金额：租车费享79折优惠，优惠金额上限188元（不含手续费、基础服务费、优享服务费、异地还车费等费用）。\n\\\\4、使用限制：每张优惠券仅限使用一次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券。\n\\\\5、使用方式：在填写订单页勾选优惠券，下单时即可扣减相应优惠金额，已完成支付的订单不能使用此券。\n\\\\6、退赔说明：\n\\\\1）使用优惠券的订单取消、超时未支付或购买失败的，若该券尚未失效将退回原账户，若该券已失效不予退回；\n\\\\2）使用优惠券的订单部分退改，导致订单金额减少的，按退改订单优惠前的金额享受相应优惠，扣除优惠差额后退款，优惠券不退；\n\\\\3）使用优惠券的订单变更，导致订单金额增加的，仅可享受变更前的优惠金额。',
        status: 3,
        statusName: '已领取',
        invaildReason: '',
        deductionList: [],
        isActive: false,
        briefName: '立享7.9折',
        scenes: 1,
        promotionId: 243518831,
        couponCode: '',
      },
    ];
    const promotions = [
      {
        promotionSecretId:
          '61271bd2bd873deebc5eff1de686b5dc2b63e081236ee74add7032ccaeae7c06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
        currencyCode: '¥',
        value: '30',
        valueType: 0,
        discountText: '',
        limitText: '',
        name: '周三租车携程优选满减券',
        validPeriodDesc: '2022-11-30生效',
        cornerText: '',
        shortDesc: '【周三福利日】',
        longDesc:
          '（1）使用范围：通过携程国内自驾租车频道，以预付方式（在线支付）预订带有“携程优选”标签的国内租车产品可享优惠；\\\\（2）券有效期：优惠券仅限2022年11月30日当天下单可用，无取还车时间限制；\\\\（3）优惠金额：享车辆租金满31元减30元的优惠，优惠不含手续费、基础服务费、租车保障费、异地还车费等其他费用，且不与其他平台优惠券同享；\\\\（4）使用限制：优惠券每位用户仅限领取1张，同一设备号，手机号，uid均视为同一用户，每个订单限用一张优惠券，仅限本人使用，领取的优惠券不可累加，不可找零，不可折现，不退差额，不可拆分使用，续租按非优惠价收费；\\\\（5）退改说明：使用优惠券的订单取消、超时未支付或购买失败，若该券尚未失效将退回原账户，若该券已失效不予退回。',
        status: 3,
        statusName: '已领取',
        invaildReason: '',
        deductionList: ['满¥31减¥30'],
        isActive: false,
        briefName: '满¥31减¥30',
        scenes: 1,
        promotionId: 754775878,
        couponCode: '',
      },
      {
        promotionSecretId:
          '20e8901c7d8c0aa74c65b29e3fbb841e65ca505cd236e8ac2487ad0f9c1b3e06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
        currencyCode: '',
        value: '7.9',
        valueType: 0,
        discountText: '折',
        limitText: '',
        name: '国内租车79折立减券',
        validPeriodDesc: '领取后3天内有效',
        cornerText: '',
        shortDesc: '携程租车中心专享',
        longDesc:
          '1、使用范围：适用于携程租车频道，以在线预付方式预订携程租车中心门店车型时可用（海南不可用），目前适用城市：北京、武汉、贵阳、福州、郑州、哈尔滨、兰州。\n\\\\2、券有效期：领取后3天内使用有效，取、还车时间在节假日用车高峰期不可用（国庆9月30日~10月7日、元旦12月31日~1月2日、春节1月20日~1月27日），过期自动失效。\n\\\\3、优惠金额：租车费享79折优惠，优惠金额上限188元（不含手续费、基础服务费、优享服务费、异地还车费等费用）。\n\\\\4、使用限制：每张优惠券仅限使用一次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券。\n\\\\5、使用方式：在填写订单页勾选优惠券，下单时即可扣减相应优惠金额，已完成支付的订单不能使用此券。\n\\\\6、退赔说明：\n\\\\1）使用优惠券的订单取消、超时未支付或购买失败的，若该券尚未失效将退回原账户，若该券已失效不予退回；\n\\\\2）使用优惠券的订单部分退改，导致订单金额减少的，按退改订单优惠前的金额享受相应优惠，扣除优惠差额后退款，优惠券不退；\n\\\\3）使用优惠券的订单变更，导致订单金额增加的，仅可享受变更前的优惠金额。',
        status: 3,
        statusName: '已领取',
        invaildReason: '',
        deductionList: [],
        isActive: false,
        briefName: '立享7.9折',
        scenes: 1,
        promotionId: 243518831,
        couponCode: '',
      },
      {
        promotionSecretId: '',
        currencyCode: '¥',
        value: '668',
        valueType: 0,
        discountText: '',
        limitText: '最高减',
        name: '租车新客专享满减券',
        validPeriodDesc: '有效期至2022-12-02',
        cornerText: '新客券',
        shortDesc: '国内租车·最高减668元',
        longDesc:
          '1.优惠券仅限携程租车新用户以预付方式（在线支付）预订国内租车产品，可享租车费立减优惠（满100-12/满200-24/满400-48/满700-84/满1000-120/满5000-668）；\\\\2.优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\3.优惠券有效期7天，从发放日开始计算，过期未使用则自动失效；\\\\4.支持网页、APP、小程序用券，每张订单限用一张优惠券，每张优惠券限用一次，不可与其他优惠和活动同享。优惠券不可折现、不退差额、不可拆分使用，续租按非优惠价收费。',
        status: 2,
        statusName: '已领取',
        invaildReason: '',
        deductionList: [
          '满¥100减¥12',
          '满¥200减¥24',
          '满¥400减¥48',
          '满¥1000减¥120',
          '满¥5000减¥668',
        ],
        isActive: true,
        briefName: '最高减¥668',
        scenes: 1,
        promotionId: 585623006,
        couponCode: 'lddtjrbzwp',
      },
    ];
    const state1: any = {
      ...initState,
      promotions: [],
    };
    const mockActionMap = [
      {
        data,
        expected: {
          ...initState,
          promotions,
        },
        mockState: init,
      },
      {
        data: undefined,
        expected: {
          ...initState,
          promotions,
        },
        mockState: {
          ...initState,
          promotions,
        },
      },
    ];
    test.each(mockActionMap)(
      'RECEIVE_ALL_PROMOTION_CALLBACK',
      ({ data, expected, mockState = state1 }) => {
        const result = CouponReducer(mockState, {
          type: RECEIVE_ALL_PROMOTION_CALLBACK,
          data,
        });
        expect(result).toEqual(expected);
      },
    );
  });
});
