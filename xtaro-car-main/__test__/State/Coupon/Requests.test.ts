import {
  getApiFetchReceivePromotionParams
} from '../../../src/pages/xcar/State/Coupon/Requests';
import { Enums } from '../../../src/pages/xcar/ComponentBusiness/Common';

const { ReceivePromotionScenes } = Enums;

describe('Coupon Requests getApiFetchReceivePromotionParams', () => {
  const mockStateMap = [
    {
      action: {
        data: {
          scenes: 15,
        },
      },
      expected: { scenes: 15 },
    },
    {
      action: {
        data: {},
      },
      expected: { scenes: ReceivePromotionScenes.All },
    },
    {
      expected: { scenes: ReceivePromotionScenes.All },
    },
  ];
  test.each(mockStateMap)(
    'getApiFetchReceivePromotionParams check',
    ({ action, expected }) => {
      const result = getApiFetchReceivePromotionParams(action);
      expect(result).toEqual(expected);
    },
  );
});

