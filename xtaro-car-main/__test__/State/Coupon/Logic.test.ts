import { run<PERSON><PERSON> } from 'redux-saga';
import { xShowToast } from '@ctrip/xtaro';
import {
  apiFetchReceivePromotionCallBackTask,
  apiFetchReceivePromotion,
  apiReceivePromotion,
  apiReceiveAllPromotion,
} from '../../../src/pages/xcar/State/Coupon/Logic';
import { takeEveryGeneratorFunction, recordSaga } from '../../testHelpers';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';
import {
  RECEIVE_ALL_PROMOTION,
  RECEIVE_ALL_PROMOTION_CALLBACK,
  FETCH_RECEIVE_PROMOTION,
  FETCH_RECEIVE_PROMOTION_CALLBACK,
  FETCH_RECEIVE_PROMOTION_CALLBACK_TASK,
  RECEIVE_PROMOTION,
  RECEIVE_PROMOTION_CALLBACK,
  SET_IS_LIST_RECEIVE_SUCCESS,
} from '../../../src/pages/xcar/State/Coupon/Types';
import { QUERY_TRIP_RECOMMEND } from '../../../src/pages/xcar/State/Home/Types';


describe('Coupon Logic apiFetchReceivePromotionCallBackTask', () => {
  test('测试正常调用', async () => {
    const res = {
      baseResponse: {
        extMap: {},
        errorCode: '0',
        message: '操作成功',
        isSuccess: true,
        showMessage: '操作成功',
      },
      title: '新客特惠',
      promotions: [
        {
          shortDesc: '租车费满100可用',
          status: 94,
          limitText: '',
          deductionList: [],
          valueType: 0,
          isActive: true,
          briefName: '立享7.5折',
          promotionSecretId:
            'af30992f653b86840a24c3447f4a0db160668721f7d0746aa640ae7b0d680f72025956be70a2b346ab1aacc1886f14841580cccbfa4a17d625e07e74cfce86dc',
          name: '国内租车满减券',
          validPeriodDesc: '有效期至 2021-05-29',
          cornerText: '',
          currencyCode: '¥',
          couponCode: '',
          invaildReason: '',
          discountText: '折',
          value: '70',
          scenes: 1,
          statusName: '生日月可领',
          promotionId: 976501635,
          longDesc:
            '本优惠券通过携程租车频道，以预付方式（在线支付）预订“枫叶租车”产品，可享受租车费7.5折优惠；\\\\优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\ 优惠券自领取日起后7天内使用有效，过期则自动失效；\\\\ 优惠券限预订取、还车时间均在2021年12月20日（含）前的订单可用；\\\\优惠券可与带有“超级品牌周”标签的产品优惠叠加适用，不可与其它优惠券与优惠活动同享；\\\\优惠券可用城市：深圳，成都，上海，杭州，北京，重庆，南京，武汉，昆明，贵阳，长沙，广州，三亚，合肥，厦门，西安，宁波，郑州，青岛，太原，南宁，珠海，沈阳，乌鲁木齐，伊宁；具体以列表页实际展示为准；\\\\ 每个订单限用一张优惠券，每张优惠券限用一次；领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；',
        },
        {
          shortDesc: '枫叶租车产品专享',
          status: 1,
          limitText: '',
          deductionList: [],
          valueType: 0,
          isActive: true,
          briefName: '立享7.5折',
          promotionSecretId:
            '5fb45816116e602c793779a8b790f5ac324192dbf3a5dcabbac9d05af894a36466d465e617295a7645f70f0a2c5e109b',
          name: '国内租车7.5折立减券',
          validPeriodDesc: '领取后7天内有效',
          cornerText: '',
          currencyCode: '',
          couponCode: '',
          invaildReason: '',
          discountText: '折',
          value: '7.5',
          scenes: 1,
          statusName: '领取',
          promotionId: 976501637,
          longDesc:
            '本优惠券通过携程租车频道，以预付方式（在线支付）预订“枫叶租车”产品，可享受租车费7.5折优惠；\\\\优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\ 优惠券自领取日起后7天内使用有效，过期则自动失效；\\\\ 优惠券限预订取、还车时间均在2021年12月20日（含）前的订单可用；\\\\优惠券可与带有“超级品牌周”标签的产品优惠叠加适用，不可与其它优惠券与优惠活动同享；\\\\优惠券可用城市：深圳，成都，上海，杭州，北京，重庆，南京，武汉，昆明，贵阳，长沙，广州，三亚，合肥，厦门，西安，宁波，郑州，青岛，太原，南宁，珠海，沈阳，乌鲁木齐，伊宁；具体以列表页实际展示为准；\\\\ 每个订单限用一张优惠券，每张优惠券限用一次；领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；',
        },
        {
          shortDesc: '新用户专享',
          status: 7,
          limitText: '最高减',
          deductionList: [
            '满¥200减¥40',
            '满¥400减¥60',
            '满¥800减¥108',
            '满¥2000减¥218',
            '满¥5000减¥588',
          ],
          valueType: 0,
          isActive: false,
          briefName: '最高减¥588',
          promotionSecretId: '',
          name: '国内租车满减券',
          validPeriodDesc: '领取后7天内有效',
          cornerText: '',
          currencyCode: '¥',
          couponCode: '',
          invaildReason: '限新客可领',
          discountText: '',
          value: '588',
          scenes: 0,
          statusName: '',
          promotionId: 449584215,
          longDesc:
            '1.优惠券仅限携程租车新用户以预付方式（在线支付）预订国内租车产品，可享租车费立减优惠（满200-40/满400-60/满800-108/满2000-218/满3000-328/满5000-588）；\\\\2.优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\3.优惠券有效期7天，从发放日开始计算，过期未使用则自动失效；\\\\4.支持网页、APP、小程序用券，每张订单限用一张优惠券，每张优惠券限用一次，不可与其他优惠同享。优惠券不可折现、不退差额、不可折分使用，续租按非优惠价收费。',
        },
        {
          shortDesc: '【超值星期三】新客专享',
          status: 7,
          limitText: '',
          deductionList: ['满¥51减¥50'],
          valueType: 0,
          isActive: false,
          briefName: '满¥51减¥50',
          promotionSecretId: '',
          name: '国内租车50元满减券',
          validPeriodDesc: '2021-12-08生效',
          cornerText: '',
          currencyCode: '¥',
          couponCode: '',
          invaildReason: '限新客可领',
          discountText: '',
          value: '50',
          scenes: 0,
          statusName: '',
          promotionId: 184395262,
          longDesc:
            '本优惠券通过携程租车频道，以预付方式（在线支付）预订国内租车产品可享受租车费满51元立减50元优惠；\\\\ 优惠券仅限携程租车新客领取，且在2021年12月8日当天下单可用，无取还车时间限制；\\\\优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用，若租车费不满可优惠金额，则减免全部租车费后不继续减免；\\\\ 该优惠券可与带有“超值星期三”标签的产品优惠叠加使用，不可与其他优惠叠加使用；\\\\ 有效期内每个用户仅限领取一张优惠券，同一设备号，手机号，uid均视为同一用户，每张优惠券限用一次；\\\\领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用，续租按非优惠价格收取；',
        },
      ],
      ResponseStatus: {
        Extension: [
          {
            Value: '4048014632245765939',
            Id: 'CLOGGING_TRACE_ID',
          },
          {
            Value: '921822-0a0eff68-455216-3950535',
            Id: 'RootMessageId',
          },
        ],
        Ack: 'Success',
        Errors: [],
        Timestamp: '/Date(1638779459632+0800)/',
      },
      appResponseMap: {
        isFromCache: false,
        isCacheValid: true,
        networkCost: 792,
        environmentCost: 0,
        cacheFetchCost: 0,
        fetchCost: 792,
        setCacheCost: 0,
        cacheFrom: '',
        beforeFetch: 1638779459546,
        afterFetch: 1638779460338,
      },
    };
    const scenes = 1;
    const dispatched: any = [];
    const actionMock = {
      type: FETCH_RECEIVE_PROMOTION_CALLBACK_TASK,
      data: {
        res,
        scenes,
      },
    };
    const logicFunc = takeEveryGeneratorFunction(
      apiFetchReceivePromotionCallBackTask,
    );
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: FETCH_RECEIVE_PROMOTION_CALLBACK,
      data: {
        title: res.title,
        promotions: res.promotions,
        scenes,
      },
    });
  });

  test('测试数据异常调用', async () => {
    const scenes = 1;
    const dispatched: any = [];
    const actionMock = {
      type: FETCH_RECEIVE_PROMOTION_CALLBACK_TASK,
    };
    const logicFunc = takeEveryGeneratorFunction(
      apiFetchReceivePromotionCallBackTask,
    );
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: FETCH_RECEIVE_PROMOTION_CALLBACK,
      data: {
        title: '',
        promotions: [],
        scenes: undefined,
      },
    });
  });
});

describe('Coupon Logic apiFetchReceivePromotion', () => {
  test('测试正常调用', async () => {
    const res = {
      baseResponse: {
        extMap: {},
        errorCode: '0',
        message: '操作成功',
        isSuccess: true,
        showMessage: '操作成功',
      },
      title: '新客特惠',
      promotions: [
        {
          shortDesc: '租车费满100可用',
          status: 94,
          limitText: '',
          deductionList: [],
          valueType: 0,
          isActive: true,
          briefName: '立享7.5折',
          promotionSecretId:
            'af30992f653b86840a24c3447f4a0db160668721f7d0746aa640ae7b0d680f72025956be70a2b346ab1aacc1886f14841580cccbfa4a17d625e07e74cfce86dc',
          name: '国内租车满减券',
          validPeriodDesc: '有效期至 2021-05-29',
          cornerText: '',
          currencyCode: '¥',
          couponCode: '',
          invaildReason: '',
          discountText: '折',
          value: '70',
          scenes: 1,
          statusName: '生日月可领',
          promotionId: 976501635,
          longDesc:
            '本优惠券通过携程租车频道，以预付方式（在线支付）预订“枫叶租车”产品，可享受租车费7.5折优惠；\\\\优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\ 优惠券自领取日起后7天内使用有效，过期则自动失效；\\\\ 优惠券限预订取、还车时间均在2021年12月20日（含）前的订单可用；\\\\优惠券可与带有“超级品牌周”标签的产品优惠叠加适用，不可与其它优惠券与优惠活动同享；\\\\优惠券可用城市：深圳，成都，上海，杭州，北京，重庆，南京，武汉，昆明，贵阳，长沙，广州，三亚，合肥，厦门，西安，宁波，郑州，青岛，太原，南宁，珠海，沈阳，乌鲁木齐，伊宁；具体以列表页实际展示为准；\\\\ 每个订单限用一张优惠券，每张优惠券限用一次；领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；',
        },
        {
          shortDesc: '枫叶租车产品专享',
          status: 1,
          limitText: '',
          deductionList: [],
          valueType: 0,
          isActive: true,
          briefName: '立享7.5折',
          promotionSecretId:
            '5fb45816116e602c793779a8b790f5ac324192dbf3a5dcabbac9d05af894a36466d465e617295a7645f70f0a2c5e109b',
          name: '国内租车7.5折立减券',
          validPeriodDesc: '领取后7天内有效',
          cornerText: '',
          currencyCode: '',
          couponCode: '',
          invaildReason: '',
          discountText: '折',
          value: '7.5',
          scenes: 1,
          statusName: '领取',
          promotionId: 976501637,
          longDesc:
            '本优惠券通过携程租车频道，以预付方式（在线支付）预订“枫叶租车”产品，可享受租车费7.5折优惠；\\\\优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\ 优惠券自领取日起后7天内使用有效，过期则自动失效；\\\\ 优惠券限预订取、还车时间均在2021年12月20日（含）前的订单可用；\\\\优惠券可与带有“超级品牌周”标签的产品优惠叠加适用，不可与其它优惠券与优惠活动同享；\\\\优惠券可用城市：深圳，成都，上海，杭州，北京，重庆，南京，武汉，昆明，贵阳，长沙，广州，三亚，合肥，厦门，西安，宁波，郑州，青岛，太原，南宁，珠海，沈阳，乌鲁木齐，伊宁；具体以列表页实际展示为准；\\\\ 每个订单限用一张优惠券，每张优惠券限用一次；领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；',
        },
        {
          shortDesc: '新用户专享',
          status: 7,
          limitText: '最高减',
          deductionList: [
            '满¥200减¥40',
            '满¥400减¥60',
            '满¥800减¥108',
            '满¥2000减¥218',
            '满¥5000减¥588',
          ],
          valueType: 0,
          isActive: false,
          briefName: '最高减¥588',
          promotionSecretId: '',
          name: '国内租车满减券',
          validPeriodDesc: '领取后7天内有效',
          cornerText: '',
          currencyCode: '¥',
          couponCode: '',
          invaildReason: '限新客可领',
          discountText: '',
          value: '588',
          scenes: 0,
          statusName: '',
          promotionId: 449584215,
          longDesc:
            '1.优惠券仅限携程租车新用户以预付方式（在线支付）预订国内租车产品，可享租车费立减优惠（满200-40/满400-60/满800-108/满2000-218/满3000-328/满5000-588）；\\\\2.优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\3.优惠券有效期7天，从发放日开始计算，过期未使用则自动失效；\\\\4.支持网页、APP、小程序用券，每张订单限用一张优惠券，每张优惠券限用一次，不可与其他优惠同享。优惠券不可折现、不退差额、不可折分使用，续租按非优惠价收费。',
        },
        {
          shortDesc: '【超值星期三】新客专享',
          status: 7,
          limitText: '',
          deductionList: ['满¥51减¥50'],
          valueType: 0,
          isActive: false,
          briefName: '满¥51减¥50',
          promotionSecretId: '',
          name: '国内租车50元满减券',
          validPeriodDesc: '2021-12-08生效',
          cornerText: '',
          currencyCode: '¥',
          couponCode: '',
          invaildReason: '限新客可领',
          discountText: '',
          value: '50',
          scenes: 0,
          statusName: '',
          promotionId: 184395262,
          longDesc:
            '本优惠券通过携程租车频道，以预付方式（在线支付）预订国内租车产品可享受租车费满51元立减50元优惠；\\\\ 优惠券仅限携程租车新客领取，且在2021年12月8日当天下单可用，无取还车时间限制；\\\\优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用，若租车费不满可优惠金额，则减免全部租车费后不继续减免；\\\\ 该优惠券可与带有“超值星期三”标签的产品优惠叠加使用，不可与其他优惠叠加使用；\\\\ 有效期内每个用户仅限领取一张优惠券，同一设备号，手机号，uid均视为同一用户，每张优惠券限用一次；\\\\领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用，续租按非优惠价格收取；',
        },
      ],
      ResponseStatus: {
        Extension: [
          {
            Value: '4048014632245765939',
            Id: 'CLOGGING_TRACE_ID',
          },
          {
            Value: '921822-0a0eff68-455216-3950535',
            Id: 'RootMessageId',
          },
        ],
        Ack: 'Success',
        Errors: [],
        Timestamp: '/Date(1638779459632+0800)/',
      },
      appResponseMap: {
        isFromCache: false,
        isCacheValid: true,
        networkCost: 792,
        environmentCost: 0,
        cacheFetchCost: 0,
        fetchCost: 792,
        setCacheCost: 0,
        cacheFrom: '',
        beforeFetch: 1638779459546,
        afterFetch: 1638779460338,
      },
    };
    jest
      .spyOn(CarFetch, 'getReceivePromotion')
      .mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(apiFetchReceivePromotion, {
      action: {
        type: FETCH_RECEIVE_PROMOTION,
        data: {
          scenes: 2,
        },
      },
      state: {},
    });

    expect(dispatched[0]).toEqual({
      type: FETCH_RECEIVE_PROMOTION_CALLBACK_TASK,
      data: {
        res,
        scenes: 2,
      },
    });
  });

  test('测试数据异常调用', async () => {
    const res = {
      baseResponse: {
        extMap: {},
        errorCode: '0',
        message: '操作成功',
        isSuccess: false,
        showMessage: '操作成功',
      },
    };
    jest
      .spyOn(CarFetch, 'getReceivePromotion')
      .mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(apiFetchReceivePromotion, {
      action: {
        type: FETCH_RECEIVE_PROMOTION,
        data: {
          scenes: 1,
        },
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: FETCH_RECEIVE_PROMOTION_CALLBACK_TASK,
      data: {
        res,
        scenes: 1,
      },
    });
  });

  test('接口异常调用', async () => {
    const exceptionError = new Error('apiFetchReceivePromotion exception');
    jest.spyOn(CarFetch, 'getReceivePromotion').mockReturnValue(
      new Promise(() => {
        throw exceptionError;
      }),
    );
    const dispatched = await recordSaga(apiFetchReceivePromotion, {
      action: {
        type: FETCH_RECEIVE_PROMOTION,
        data: {
          scenes: 1,
        },
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: FETCH_RECEIVE_PROMOTION_CALLBACK_TASK,
      data: {
        res: undefined,
        scenes: 1,
      },
    });
  });
});

describe('Coupon Logic apiReceivePromotion', () => {
  test('测试正常调用', async () => {
    const res = {
      baseResponse: {
        extMap: {},
        errorCode: '0',
        code: '0',
        showMessage: '操作成功',
        apiResCodes: [],
        returnMsg: '操作成功',
        message: '操作成功',
        requestId: 'ea28c33a-b2cc-447a-a6aa-e1f6958a927e',
        isSuccess: true,
      },
      receivePromotionInfo: {
        shortDesc: '首页券包指定城市可用zj',
        status: 2,
        limitText: '',
        deductionList: [],
        valueType: 0,
        isActive: true,
        briefName: '立享5.0折',
        promotionSecretId: '',
        name: '券包指定城市可用zj立减券',
        validPeriodDesc: '有效期至2023-06-05',
        cornerText: '',
        currencyCode: '',
        couponCode: 'yqmvlcpsnm',
        invaildReason: '',
        discountText: '折',
        value: '5.0',
        scenes: 1,
        statusName: '已领取',
        promotionId: 394279873,
        longDesc: '首页券包指定城市可用zj',
      },
      ResponseStatus: {
        Extension: [
          { Value: '7199301105414592365', Id: 'CLOGGING_TRACE_ID' },
          { Value: '921822-0a056b21-463995-30277', Id: 'RootMessageId' },
        ],
        Ack: 'Success',
        Errors: [],
        Timestamp: '/Date(1670382358857+0800)/',
      },
      appResponseMap: {
        isFromCache: false,
        isCacheValid: true,
        networkCost: 251,
        environmentCost: 0,
        cacheFetchCost: 0,
        fetchCost: 251,
        setCacheCost: 0,
        cacheFrom: '',
        beforeFetch: 1670382358559,
        afterFetch: 1670382358810,
      },
    };
    jest
      .spyOn(CarFetch, 'receivePromotion')
      .mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(apiReceivePromotion, {
      action: {
        type: RECEIVE_PROMOTION,
        data: {
          isList: true,
        },
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: RECEIVE_PROMOTION_CALLBACK,
      data: res.receivePromotionInfo,
    });
    expect(dispatched[1]).toEqual({
      type: QUERY_TRIP_RECOMMEND,
      data: {
        isInit: true
      },
    });
    expect(dispatched[2]).toEqual({
      type: SET_IS_LIST_RECEIVE_SUCCESS,
      data: true,
    });
  });

  test('测试数据异常调用', async () => {
    const res: any = {
      baseResponse: {
        extMap: {},
        errorCode: '0',
        code: '0',
        showMessage: '操作失败',
        apiResCodes: [],
        returnMsg: '操作成功',
        message: '操作成功',
        requestId: 'ea28c33a-b2cc-447a-a6aa-e1f6958a927e',
        isSuccess: false,
      },
    };

    jest
      .spyOn(CarFetch, 'receivePromotion')
      .mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(apiReceivePromotion, {
      action: {
        type: RECEIVE_PROMOTION,
        data: {
          isList: true,
        },
      },
      state: {},
    });
    expect(dispatched).toEqual([]);
    expect(xShowToast).toBeCalledWith({ "duration": 3000, "title": "操作失败" });
  });

  test('接口异常调用', async () => {
    const res: any = {
      baseResponse: {
        extMap: {},
        errorCode: '0',
        code: '0',
        showMessage: '操作失败',
        apiResCodes: [],
        returnMsg: '操作成功',
        message: '操作成功',
        requestId: 'ea28c33a-b2cc-447a-a6aa-e1f6958a927e',
        isSuccess: false,
      },
    };
    const exceptionError = new Error('receivePromotion exception');
    jest.spyOn(CarFetch, 'receivePromotion').mockReturnValue(
      new Promise(() => {
        throw exceptionError;
      }),
    );
    const dispatched = await recordSaga(apiReceivePromotion, {
      action: {
        type: RECEIVE_PROMOTION,
        data: {
          isList: true,
        },
      },
      state: {},
    });
    expect(dispatched).toEqual([]);
    expect(xShowToast).toBeCalledWith({ "duration": 3000, "title": "系统异常，请刷新页面后重试" }); // API 被调用
  });
});

describe('LocationAndDate Logic apiReceiveAllPromotion', () => {
  const receivePromotionInfos = [
    {
      promotionSecretId:
        '20e8901c7d8c0aa74c65b29e3fbb841e65ca505cd236e8ac2487ad0f9c1b3e06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
      currencyCode: '',
      value: '7.9',
      valueType: 0,
      discountText: '折',
      limitText: '',
      name: '国内租车79折立减券',
      validPeriodDesc: '领取后3天内有效',
      cornerText: '',
      shortDesc: '携程租车中心专享',
      longDesc:
        '1、使用范围：适用于携程租车频道，以在线预付方式预订携程租车中心门店车型时可用（海南不可用），目前适用城市：北京、武汉、贵阳、福州、郑州、哈尔滨、兰州。\n\\\\2、券有效期：领取后3天内使用有效，取、还车时间在节假日用车高峰期不可用（国庆9月30日~10月7日、元旦12月31日~1月2日、春节1月20日~1月27日），过期自动失效。\n\\\\3、优惠金额：租车费享79折优惠，优惠金额上限188元（不含手续费、基础服务费、优享服务费、异地还车费等费用）。\n\\\\4、使用限制：每张优惠券仅限使用一次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券。\n\\\\5、使用方式：在填写订单页勾选优惠券，下单时即可扣减相应优惠金额，已完成支付的订单不能使用此券。\n\\\\6、退赔说明：\n\\\\1）使用优惠券的订单取消、超时未支付或购买失败的，若该券尚未失效将退回原账户，若该券已失效不予退回；\n\\\\2）使用优惠券的订单部分退改，导致订单金额减少的，按退改订单优惠前的金额享受相应优惠，扣除优惠差额后退款，优惠券不退；\n\\\\3）使用优惠券的订单变更，导致订单金额增加的，仅可享受变更前的优惠金额。',
      status: 3,
      statusName: '已领取',
      invaildReason: '',
      deductionList: [],
      isActive: false,
      briefName: '立享7.9折',
      scenes: 1,
      promotionId: *********,
      couponCode: '',
    },
    {
      promotionSecretId:
        '61271bd2bd873deebc5eff1de686b5dc2b63e081236ee74add7032ccaeae7c06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
      currencyCode: '¥',
      value: '30',
      valueType: 0,
      discountText: '',
      limitText: '',
      name: '周三租车携程优选满减券',
      validPeriodDesc: '2022-11-30生效',
      cornerText: '',
      shortDesc: '【周三福利日】',
      longDesc:
        '（1）使用范围：通过携程国内自驾租车频道，以预付方式（在线支付）预订带有“携程优选”标签的国内租车产品可享优惠；\\\\（2）券有效期：优惠券仅限2022年11月30日当天下单可用，无取还车时间限制；\\\\（3）优惠金额：享车辆租金满31元减30元的优惠，优惠不含手续费、基础服务费、租车保障费、异地还车费等其他费用，且不与其他平台优惠券同享；\\\\（4）使用限制：优惠券每位用户仅限领取1张，同一设备号，手机号，uid均视为同一用户，每个订单限用一张优惠券，仅限本人使用，领取的优惠券不可累加，不可找零，不可折现，不退差额，不可拆分使用，续租按非优惠价收费；\\\\（5）退改说明：使用优惠券的订单取消、超时未支付或购买失败，若该券尚未失效将退回原账户，若该券已失效不予退回。',
      status: 3,
      statusName: '已领取',
      invaildReason: '',
      deductionList: ['满¥31减¥30'],
      isActive: false,
      briefName: '满¥31减¥30',
      scenes: 1,
      promotionId: *********,
      couponCode: '',
    },
  ];
  const promotionIds = [*********, *********];
  const promotionSecretIds = [
    '61271bd2bd873deebc5eff1de686b5dc2b63e081236ee74add7032ccaeae7c06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
    '20e8901c7d8c0aa74c65b29e3fbb841e65ca505cd236e8ac2487ad0f9c1b3e06b608a127eb9eb90ae89c360e9285104b1580cccbfa4a17d625e07e74cfce86dc',
  ];
  const testFn = async () => {
    const dispatched = await recordSaga(apiReceiveAllPromotion, {
      action: {
        type: RECEIVE_ALL_PROMOTION,
        data: { promotionIds, promotionSecretIds, isList: true },
      },
      state: {},
    });
    return dispatched;
  };

  test('apiReceiveAllPromotion 测试正常调用', async () => {
    const res = { baseResponse: { isSuccess: true }, receivePromotionInfos };
    jest
      .spyOn(CarFetch, 'receivePromotion')
      .mockReturnValue(Promise.resolve(res));
    const dispatched = await testFn();
    expect(dispatched[0]).toEqual({
      type: RECEIVE_ALL_PROMOTION_CALLBACK,
      data: receivePromotionInfos,
    });
    expect(dispatched[1]).toEqual({
      type: SET_IS_LIST_RECEIVE_SUCCESS,
      data: true,
    });
  });
  test('apiReceiveAllPromotion 测试接口异常调用', async () => {
    const exceptionError = new Error('receivePromotion exception');
    jest.spyOn(CarFetch, 'receivePromotion').mockReturnValue(
      new Promise(() => {
        throw exceptionError;
      }),
    );
    const dispatched = await testFn();
    expect(dispatched[0]).toEqual({
      type: RECEIVE_ALL_PROMOTION_CALLBACK,
      data: [],
    });
  });
  test('apiReceiveAllPromotion 测试数据异常调用', async () => {
    const res = {
      baseResponse: { isSuccess: false, showMessage: 'error' },
      receivePromotionInfos: [],
    };
    jest
      .spyOn(CarFetch, 'receivePromotion')
      .mockReturnValue(Promise.resolve(res));
    const dispatched = await testFn();
    expect(dispatched).toEqual([]);
  });
});
