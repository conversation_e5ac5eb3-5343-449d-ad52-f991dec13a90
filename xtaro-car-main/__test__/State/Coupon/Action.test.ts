import {
  receiveAllPromotion,
  receiveAllPromotionCallback,
} from '../../../src/pages/xcar/State/Coupon/Actions';
import {
  RECEIVE_ALL_PROMOTION,
  RECEIVE_ALL_PROMOTION_CALLBACK,
} from '../../../src/pages/xcar/State/Coupon/Types';

describe('Coupon Actions receiveAllPromotion', () => {
  const data = {};
  const expected = {
    type: RECEIVE_ALL_PROMOTION,
    data,
  };
  test('receiveAllPromotion check', () => {
    const result = receiveAllPromotion(data);
    expect(result).toEqual(expected);
  });
});

describe('Coupon Actions receiveAllPromotionCallback', () => {
  const data = {};
  const expected = {
    type: RECEIVE_ALL_PROMOTION_CALLBACK,
    data,
  };
  test('fetchApiDriverList check', () => {
    const result = receiveAllPromotionCallback(data);
    expect(result).toEqual(expected);
  });
});
