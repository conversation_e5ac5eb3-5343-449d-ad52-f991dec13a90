import _ from 'lodash';
import {
  packageGuideRes,
  packageOrderDetailGuideRes,
  getPickDropGuideInfo,
} from '../../../src/pages/xcar/State/Guide/Mapper';
import { Utils } from '../../../src/pages/xcar/Util/Index';

describe('Guide Mappers packageGuideRes', () => {
  const pickupStart = {
    lat: 25.070579,
    lng: 102.931459,
    addr: '昆明市某个地方',
    desc: '',
    type: 0,
  };
  const dropoffStart = {
    lat: 25.070579,
    lng: 102.941459,
    addr: '合肥机场',
    desc: '',
    type: 0,
  };
  const pickUpGuideInfo = {
    wayInfo: 1,
    storeAddress: '凤凰机场TI航站楼入口',
    storeWay: '店员免费接您至门店取车',
    longitude: '109.411996',
    latitude: '18.308310',
    storeName: '凤凰机场T1送车点',
    storePhone: '***********;************',
    businessTime: '10:00-20:00',
  };
  const dropOffGuideInfo = {
    wayInfo: 1,
    storeAddress: '凤凰机场TI航站楼入口',
    storeWay: '还车后店员免费送您至凤凰国际机场T1航站楼',
    longitude: '109.411996',
    latitude: '18.308310',
    storeName: '凤凰机场T1送车点',
    storePhone: '***********;************',
    businessTime: '10:00-20:00',
  };
  const selectedId = 'pickup';
  const transportType = 1;
  const urlParams = {
    pickupStart,
    dropoffStart,
    selectedId,
    transportType,
  };
  const data = {
    pickUpGuideInfo,
    dropOffGuideInfo,
  };
  const reqParam = {
    pickupPointInfo: {
      locationName: '凤凰机场TI航站楼',
    },
    returnPointInfo: {
      locationName: '凤凰机场TI航站楼',
    },
  };
  const mockData = [
    {
      urlParams,
      data,
      reqParam,
      expected: {
        pickUpGuideInfo: {
          ...pickUpGuideInfo,
          start: pickupStart,
          serviceType: pickUpGuideInfo.wayInfo,
          dest: {
            lat: pickUpGuideInfo.latitude,
            lng: pickUpGuideInfo.longitude,
            addr: pickUpGuideInfo.storeAddress,
          },
          addressTitle: '取车地址',
          goAddress: '凤凰机场TI航站楼',
        },
        dropOffGuideInfo: {
          ...dropOffGuideInfo,
          start: dropoffStart,
          serviceType: dropOffGuideInfo.wayInfo,
          dest: {
            lat: dropOffGuideInfo.latitude,
            lng: dropOffGuideInfo.longitude,
            addr: dropOffGuideInfo.storeAddress,
          },
          addressTitle: '还车地址',
          goAddress: '凤凰机场TI航站楼',
        },
        selectedId,
        transportType,
      },
    },
    {
      urlParams: {},
      data: {},
      reqParam: {},
      expected: {
        pickUpGuideInfo: {
          start: undefined,
          serviceType: undefined,
          dest: {
            lat: undefined,
            lng: undefined,
            addr: undefined,
          },
          goAddress: undefined,
        },
        dropOffGuideInfo: {
          start: undefined,
          serviceType: undefined,
          dest: {
            lat: undefined,
            lng: undefined,
            addr: undefined,
          },
          goAddress: undefined,
        },
        selectedId: undefined,
        transportType: undefined,
      },
    },
  ];

  test.each(mockData)(
    'Guide Mappers packageGuideRes',
    ({ urlParams, data, reqParam, expected }) => {
      const result = packageGuideRes(urlParams, data, reqParam);
      expect(result).toEqual(expected);
    },
  );
});

describe('Guide Mappers packageOrderDetailGuideRes', () => {
  const pickUpGuideInfo = {
    wayInfo: 0,
    storeAddress: '凤凰机场TI航站楼入口',
    storeWay: '店员免费接您至门店取车',
    longitude: '109.411996',
    latitude: '18.308310',
    storeName: '凤凰机场T1送车点',
    storePhone: '***********;************',
    businessTime: '10:00-20:00',
    serviceTypeCode: 1,
  };
  const dropOffGuideInfo = {
    wayInfo: 1,
    storeAddress: '凤凰机场TI航站楼入口',
    storeWay: '还车后店员免费送您至凤凰国际机场T1航站楼',
    longitude: '109.411996',
    latitude: '18.308310',
    storeName: '凤凰机场T1送车点',
    storePhone: '***********;************',
    businessTime: '10:00-20:00',
    serviceTypeCode: 2,
  };
  const selectedId = 'pickup';
  const data = {
    pickUpGuideInfo,
    dropOffGuideInfo,
  };
  const mockData = [
    {
      selectedId,
      data,
      expected: {
        pickUpGuideInfo: {
          ...pickUpGuideInfo,
          dest: {
            lat: pickUpGuideInfo?.latitude,
            lng: pickUpGuideInfo?.longitude,
            addr: pickUpGuideInfo?.storeAddress,
          },
          serviceType: pickUpGuideInfo.serviceTypeCode,
        },
        dropOffGuideInfo: {
          ...dropOffGuideInfo,
          dest: {
            lat: dropOffGuideInfo?.latitude,
            lng: dropOffGuideInfo?.longitude,
            addr: dropOffGuideInfo?.storeAddress,
          },
          serviceType: dropOffGuideInfo.serviceTypeCode,
        },
        selectedId,
      },
    },
    {
      selectedId: null,
      data: {},
      expected: {
        pickUpGuideInfo: {
          dest: {
            lat: undefined,
            lng: undefined,
            addr: undefined,
          },
          serviceType: undefined,
        },
        dropOffGuideInfo: {
          dest: {
            at: undefined,
            lng: undefined,
            addr: undefined,
          },
          serviceType: undefined,
        },
        selectedId: null,
      },
    },
  ];
  test.each(mockData)(
    'Guide Mappers packageOrderDetailGuideRes',
    ({ data, selectedId, expected }) => {
      const result = packageOrderDetailGuideRes(data, selectedId);
      expect(result).toEqual(expected);
    },
  );
});

describe('Guide Mappers getPickDropGuideInfo', () => {
  const guideStep = [
    {
      strDate: '2017-12-18',
      userAvatar:
        '//uploadimg.uat.qa.nt.ctripcorp.com/upload/target/t1/headphoto/136/299/437/649eb217e3e54746b7a53b8665e14b00_C_180_180.jpg',
      uid: 'M00014355',
      name: 'M***5',
      content: '测试还车指引',
      type: 2,
    },
    {
      strDate: '2017-10-25',
      userAvatar:
        '//uploadimg.uat.qa.nt.ctripcorp.com/upload/target/t1/headphoto/308/781/676/5415377ef6af49aba9d028013530b873_C_180_180.jpg',
      uid: 'M00111825',
      name: 'M***5',
      content: 'dahgera',
      type: 2,
    },
  ];
  const expectedStep = guideStep.map(item => ({
    iconImg: item.userAvatar,
    userName: item.name,
    content: item.content,
  }));
  const guideInfo = {
    guideStep,
    temp: 12346,
  };
  const storeGuide = 'storeGuide测试文案';
  const mockOsdData = [
    {
      guideInfo,
      storeGuide,
      expected: {
        ...guideInfo,
        officialGuide: storeGuide,
      },
    },
    {
      guideInfo: undefined,
      expected: {
        officialGuide: undefined
      },
    },
  ];

  test.each(mockOsdData)(
    'Guide Mappers getPickDropGuideInfo OSD',
    ({ guideInfo, storeGuide, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => false);
      jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
      const result = getPickDropGuideInfo(guideInfo, storeGuide);
      
      expect(result).toEqual(expected);
    },
  );
});
