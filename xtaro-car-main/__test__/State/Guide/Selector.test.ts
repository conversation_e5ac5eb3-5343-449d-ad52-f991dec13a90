import { 
  getIsLoading,
  getIsFail,
  getPickUpGuideInfo,
  getDropOffGuideInfo,
  getSelectedId,
  getTransportType,
  getRes,
} from '../../../src/pages/xcar/State/Guide/Selectors';

describe('Guide Selectors getIsLoading', () => {
  const mockStateMap = [
    {
      state: {
        Guide: {
          isLoading: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Guide: {
          isLoading: false,
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getIsLoading check',
    ({ state, expected }) => {
      const data = getIsLoading(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Guide Selectors getIsFail', () => {
  const mockStateMap = [
    {
      state: {
        Guide: {
          isFail: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Guide: {
          isFail: false,
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getIsFail check',
    ({ state, expected }) => {
      const data = getIsFail(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Guide Selectors getPickUpGuideInfo', () => {
  const pickUpGuideInfo = {
    "storeAddress": "凤凰机场TI航站楼入口",
    "storeWay": "店员免费接您至门店取车",
    "longitude": "109.411996",
    "latitude": "18.308310",
    "storeName": "凤凰机场T1送车点",
    "storePhone": "***********;************",
    "businessTime": "10:00-20:00"
  };
  const mockStateMap = [
    {
      state: {
        Guide: {
          pickUpGuideInfo: null,
        },
      },
      expected: null,
    },
    {
      state: {
        Guide: {
          pickUpGuideInfo,
        },
      },
      expected: pickUpGuideInfo,
    },
  ];
  test.each(mockStateMap)(
    'getPickUpGuideInfo check',
    ({ state, expected }) => {
      const data = getPickUpGuideInfo(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Guide Selectors getDropOffGuideInfo', () => {
  const dropOffGuideInfo = {
    "storeAddress": "凤凰机场TI航站楼入口",
    "storeWay": "还车后店员免费送您至凤凰国际机场T1航站楼",
    "longitude": "109.411996",
    "latitude": "18.308310",
    "storeName": "凤凰机场T1送车点",
    "storePhone": "***********;************",
    "businessTime": "10:00-20:00"
  };
  const mockStateMap = [
    {
      state: {
        Guide: {
          dropOffGuideInfo: null,
        },
      },
      expected: null,
    },
    {
      state: {
        Guide: {
          dropOffGuideInfo,
        },
      },
      expected: dropOffGuideInfo,
    },
  ];
  test.each(mockStateMap)(
    'getDropOffGuideInfo check',
    ({ state, expected }) => {
      const data = getDropOffGuideInfo(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Guide Selectors getSelectedId', () => {
  const mockStateMap = [
    {
      state: {
        Guide: {
          selectedId: null,
        },
      },
      expected: null,
    },
    {
      state: {
        Guide: {
          selectedId: 'pickup',
        },
      },
      expected: 'pickup',
    },
  ];
  test.each(mockStateMap)(
    'getSelectedId check',
    ({ state, expected }) => {
      const data = getSelectedId(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Guide Selectors getTransportType', () => {
  const mockStateMap = [
    {
      state: {
        Guide: {
          transportType: null,
        },
      },
      expected: null,
    },
    {
      state: {
        Guide: {
          transportType: 1,
        },
      },
      expected: 1,
    },
  ];
  test.each(mockStateMap)(
    'getTransportType check',
    ({ state, expected }) => {
      const data = getTransportType(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Guide Selectors getRes', () => {
  const guideRes = {
    "pickUpGuideInfo": {
        "storeAddress": "8820 Bellanca Ave",
        "outOfHourDescription": "如果您的取車還車時間不在營業時間內，請提前聯繫租車櫃台詢問是否支持營業時間外取還車。請注意，營業時間外取車或還車可能收取額外費用。",
        "longitude": "-118.408530000000000",
        "storeCode": "LAXACLAXO02",
        "latitude": "33.941589000000000",
        "storeName": "Los Angeles Intl Airport",
        "storePhone": "************",
        "experiences": [
            {
                "iconImg": "https: //pic.c-ctrip.com/car_isd/app/long/service.png",
                "userName": "1***7",
                "content": "t was a dream. I knew it was going to be beautiful, but I didn&#39;t realize we&#39;d be staying where all the photos are being taken. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented 8 cars from the airport. You have everything .",
                "originContent": "1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店到店面。2.出示提车凭证。3.店面店面制单打印凭证。 4. 取门店申请打印凭证，然后去取车"
            },
            {
                "iconImg": "https: //pic.c-ctrip.com/car_isd/app/long/service.png",
                "userName": "1***7",
                "content": "t was a dream. I knew it was going to be beautiful, but I didn&#39;t realize we&#39;d be staying where all the photos are being taken. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented 8 cars from the airport. You have everything .",
                "originContent": "1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店到店面。2.出示提车凭证。3.店面店面制单打印凭证。 4. 取门店申请打印凭证，然后去取车"
            },
            {
                "iconImg": "https: //pic.c-ctrip.com/car_isd/app/long/service.png",
                "userName": "1***7",
                "content": "t was a dream. I knew it was going to be beautiful, but I didn&#39;t realize we&#39;d be staying where all the photos are being taken. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented 8 cars from the airport. You have everything .",
                "originContent": "1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店到店面。2.出示提车凭证。3.店面店面制单打印凭证。 4. 取门店申请打印凭证，然后去取车"
            },
            {
                "iconImg": "https: //pic.c-ctrip.com/car_isd/app/long/service.png",
                "userName": "Ali***on",
                "content": "t was a dream. I knew it was going to be beautiful, but I didn&#39;t realize we&#39;d be staying where all the photos are being taken. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented 8 cars from the airport. You have everything .",
                "originContent": "1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店到店面。2.出示提车凭证。3.店面店面制单打印凭证。 4. 取门店申请打印凭证，然后去取车"
            },
            {
                "iconImg": "https: //pic.c-ctrip.com/car_isd/app/long/service.png",
                "userName": "Ali***on",
                "content": "t was a dream. I knew it was going to be beautiful, but I didn&#39;t realize we&#39;d be staying where all the photos are being taken. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented 8 cars from the airport. You have everything .",
                "originContent": "1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店到店面。2.出示提车凭证。3.店面店面制单打印凭证。 4. 取门店申请打印凭证，然后去取车"
            }
        ],
        "storeIcon": "//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/ace.png",
        "businessTime": "{\"星期一 ～ 星期日\":\"07:00-22:00\"}",
        "start": {
            "lat": 33.941589,
            "lng": -118.40853,
            "addr": "洛杉矶国际机场"
        },
        "serviceType": 0,
        "dest": {
            "lat": "33.941589000000000",
            "lng": "-118.408530000000000",
            "addr": "8820 Bellanca Ave"
        }
    },
    "dropOffGuideInfo": {
        "storeAddress": "8820 Bellanca Ave",
        "outOfHourDescription": "如果您的取車還車時間不在營業時間內，請提前聯繫租車櫃台詢問是否支持營業時間外取還車。請注意，營業時間外取車或還車可能收取額外費用。",
        "longitude": "-118.408530000000000",
        "storeCode": "LAXACLAXO02",
        "latitude": "33.941589000000000",
        "storeName": "Los Angeles Intl Airport",
        "storePhone": "************",
        "experiences": [
            {
                "iconImg": "https: //pic.c-ctrip.com/car_isd/app/long/service.png",
                "userName": "2***7",
                "content": "t was a dream. I knew it was going to be beautiful, but I didn&#39;t realize we&#39;d be staying where all the photos are being taken. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented 8 cars from the airport. You have everything .",
                "originContent": "1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店到店面。2.出示提车凭证。3.店面店面制单打印凭证。 4. 取门店申请打印凭证，然后去取车"
            },
            {
                "iconImg": "https: //pic.c-ctrip.com/car_isd/app/long/service.png",
                "userName": "2***7",
                "content": "t was a dream. I knew it was going to be beautiful, but I didn&#39;t realize we&#39;d be staying where all the photos are being taken. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented 8 cars from the airport. You have everything .",
                "originContent": "1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店到店面。2.出示提车凭证。3.店面店面制单打印凭证。 4. 取门店申请打印凭证，然后去取车"
            },
            {
                "iconImg": "https: //pic.c-ctrip.com/car_isd/app/long/service.png",
                "userName": "2***7",
                "content": "t was a dream. I knew it was going to be beautiful, but I didn&#39;t realize we&#39;d be staying where all the photos are being taken. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented 8 cars from the airport. You have everything .",
                "originContent": "1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店到店面。2.出示提车凭证。3.店面店面制单打印凭证。 4. 取门店申请打印凭证，然后去取车"
            },
            {
                "iconImg": "https: //pic.c-ctrip.com/car_isd/app/long/service.png",
                "userName": "Ali***on",
                "content": "t was a dream. I knew it was going to be beautiful, but I didn&#39;t realize we&#39;d be staying where all the photos are being taken. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented 8 cars from the airport. You have everything .",
                "originContent": "1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店到店面。2.出示提车凭证。3.店面店面制单打印凭证。 4. 取门店申请打印凭证，然后去取车"
            },
            {
                "iconImg": "https: //pic.c-ctrip.com/car_isd/app/long/service.png",
                "userName": "Ali***on",
                "content": "t was a dream. I knew it was going to be beautiful, but I didn&#39;t realize we&#39;d be staying where all the photos are being taken. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented a car from the airport. You have everything at your finger tips. We rented 8 cars from the airport. You have everything .",
                "originContent": "1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店面制单打印凭证。4.选车。5.检查车况1.在机场做免费大巴到店面。2.出示提车凭证。3.店到店面。2.出示提车凭证。3.店面店面制单打印凭证。 4. 取门店申请打印凭证，然后去取车"
            }
        ],
        "storeIcon": "//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/ace.png",
        "businessTime": "{\"星期一 ～ 星期日\":\"07:00-22:00\"}",
        "start": {
            "lat": 33.941589,
            "lng": -118.40853,
            "addr": "洛杉矶国际机场"
        },
        "serviceType": 0,
        "dest": {
            "lat": "33.941589000000000",
            "lng": "-118.408530000000000",
            "addr": "8820 Bellanca Ave"
        }
    },
    "selectedId": "pickup"
  };
  const mockStateMap = [
    {
      state: {
        Guide: {
          res: null,
        },
      },
      expected: null,
    },
    {
      state: {
        Guide: {
          res: guideRes,
        },
      },
      expected: guideRes,
    },
  ];
  test.each(mockStateMap)(
    'getRes check',
    ({ state, expected }) => {
      const data = getRes(state);
      expect(data).toEqual(expected);
    },
  );
});