import { 
  getParams,
  checkParams,
} from '../../../src/pages/xcar/State/Guide/Helpers';
  
describe('Guide Helper getParams', () => {
  const data1 = '{"result":{"resultCode":0}}';
  const mockStateMap = [
    {
      state: data1,
      expected: data1 && JSON.parse(decodeURIComponent(data1)),
    },
  ];
  test.each(mockStateMap)(
    'getParams check',
    ({ state, expected }) => {
      const data = getParams(state);
      expect(data).toEqual(expected);
    },
  );
});
  
describe('Guide Helper checkParams', () => {
  const mockStateMap = [
    {
      state: {
        pickupStoreId: 12345,
        dropoffStoreId: 56789,
      },
      expected: 56789,
    },
    {
      state: {
        pickupStoreId: 12345,
      },
      expected: undefined,
    },
    {
      state: {
        dropoffStoreId: 56789,
      },
      expected: undefined,
    },
    {
      state: {
      },
      expected: undefined,
    },
    {
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'checkParams check',
    ({ state, expected }) => {
      const data = checkParams(state);
      expect(data).toEqual(expected);
    },
  );
});