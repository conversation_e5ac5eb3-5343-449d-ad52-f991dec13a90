import { fetchApiGuide } from '../../../src/pages/xcar/State/Guide/Actions';
import {
  FETCH_GUIDE
} from '../../../src/pages/xcar/State/Guide/Types';

describe('Guide Actions fetchApiGuide', () => {
  const data = {};
  const mockStateMap = [
    {
      data,
      expected: {
        type: FETCH_GUIDE,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'fetchApiGuide check',
    ({ data, expected }) => {
      const result = fetchApiGuide(data);
      expect(result).toEqual(expected);
    },
  );
});
