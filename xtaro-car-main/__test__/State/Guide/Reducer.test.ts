import GuideReducer, { getInitalState } from '../../../src/pages/xcar/State/Guide/Reducer';
import {
  FETCH_GUIDE,
  FETCH_GUIDE_CALLBACK,
  SET_STATUS,
} from '../../../src/pages/xcar/State/Guide/Types';

describe('Guide Reducer Test', () => {
  const initState = getInitalState();

  test('Guide Reducer Init', () => {
    expect(GuideReducer(undefined, {})).toEqual(initState);
  });

  describe('Guide Reducer FETCH_GUIDE', () => {
    test('FETCH_GUIDE', () => {
        expect(GuideReducer(initState, {
            type: FETCH_GUIDE,
        })).toEqual({
            ...initState,
            isLoading: true,
        });
    });
  });

  describe('Guide Reducer FETCH_GUIDE_CALLBACK', () => {
    const pickUpGuideInfo = {
      "storeAddress": "凤凰机场TI航站楼入口",
      "storeWay": "店员免费接您至门店取车",
      "longitude": "109.411996",
      "latitude": "18.308310",
      "storeName": "凤凰机场T1送车点",
      "storePhone": "***********;************",
      "businessTime": "10:00-20:00"
    };
    const dropOffGuideInfo = {
      "storeAddress": "凤凰机场TI航站楼入口",
      "storeWay": "还车后店员免费送您至凤凰国际机场T1航站楼",
      "longitude": "109.411996",
      "latitude": "18.308310",
      "storeName": "凤凰机场T1送车点",
      "storePhone": "***********;************",
      "businessTime": "10:00-20:00"
    };
    const selectedId = "pickup";
    const transportType = 1;
    const isError = false;
    const res = {
      pickUpGuideInfo,
      dropOffGuideInfo,
      selectedId,
      transportType,
    };
    const mockActionMap = [
      {
        data: {
          res,
          isLoading: false,
          isError,
        },
        expected: {
          ...initState,
          res,
          pickUpGuideInfo,
          dropOffGuideInfo,
          isLoading: false,
          selectedId,
          transportType,
          isFail: isError,
        },
      },
      {
        data: {
          res: {},
          isLoading: true,
          isError: true,
        },
        expected: {
          ...initState,
          res: {},
          pickUpGuideInfo: undefined,
          dropOffGuideInfo: undefined,
          isLoading: true,
          selectedId: undefined,
          transportType: undefined,
          isFail: true,
        },
      },
    ];
    test.each(mockActionMap)(
      'FETCH_DRIVERLIST_CALLBACK check',
      ({ data, expected }) => {
        expect(GuideReducer(initState, {
          type: FETCH_GUIDE_CALLBACK,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('Guide Reducer SET_STATUS', () => {
    const mockActionMap = [
      {
        data: {
          isLoading: false,
          isFail: false,
          progress: false,
        },
        expected: {
          ...initState,
          isLoading: false,
          isFail: false,
          progress: false,
        },
      },
      {
        data: {
          isLoading: true,
          isFail: true,
          progress: true,
        },
        expected: {
          ...initState,
          isLoading: true,
          isFail: true,
          progress: true,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_STATUS check',
      ({ data, expected }) => {
        expect(GuideReducer(initState, {
          type: SET_STATUS,
          data,
        })).toEqual(expected);
      },
    );
  });
})