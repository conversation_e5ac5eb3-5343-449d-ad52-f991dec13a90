import { runSaga } from 'redux-saga';
import {
  getApiQueryParam,
  getFetchHelperParam,
  getApiQueryGuideFetchParam,
  apiQueryGuide,
} from '../../../src/pages/xcar/State/Guide/Logic';
import { takeEveryGeneratorFunction } from '../../testHelpers';
import {
  CarFetch,
  AppContext,
  CarFetchHelper,
  Utils,
} from '../../../src/pages/xcar/Util/Index';
import {
  FETCH_GUIDE,
  SET_STATUS,
  FETCH_GUIDE_CALLBACK,
} from '../../../src/pages/xcar/State/Guide/Types';
import {
  packageGuideRes,
  packageOrderDetailGuideRes,
} from '../../../src/pages/xcar/State/Guide/Mapper';
import { FETCH_ODERNUMBER } from '../../../src/pages/xcar/State/Common/Types';

jest.mock('../../../src/pages/xcar/State/Guide/Helpers', () => {
  return {
    getParams: jest.fn(data => JSON.parse(decodeURIComponent(data))),
    checkParams: jest.fn(state => state.OrderDetail.pickupStore),
  };
});
jest.mock('../../../src/pages/xcar/State/VendorList/Selectors', () => {
  return {
    getUniqueReference: jest.fn(state => state.reference),
  };
});
describe('Guide Logic getApiQueryParam', () => {
  const actionData = {
    pickupStart: {
      lat: 18.30747,
      lng: 109.41201,
      addr: '凤凰国际机场T1航站楼',
    },
    dropoffStart: {
      lat: 18.30747,
      lng: 109.41201,
      addr: '凤凰国际机场T1航站楼',
    },
    pickupStoreId: '194723',
    dropoffStoreId: '194723',
    selectedId: 'pickup',
    pickupServiceType: 0,
    dropoffServiceType: 0,
    pStoreWay: '自行前往门店取还车',
    rStoreWay: '自行前往门店取还车',
    pickupPointInfo: {
      cityId: 43,
      date: '2022-07-30 10:00:00',
      locationCode: '',
      locationName: '凤凰国际机场T1航站楼',
      locationType: '1',
      poi: {
        latitude: 18.30747,
        longitude: 109.41201,
        radius: 0,
      },
      pickupOnDoor: 0,
      dropOffOnDoor: 0,
    },
    returnPointInfo: {
      cityId: 43,
      date: '2022-08-01 10:00:00',
      locationCode: '',
      locationName: '凤凰国际机场T1航站楼',
      locationType: '1',
      poi: {
        latitude: 18.30747,
        longitude: 109.41201,
        radius: 0,
      },
      pickupOnDoor: 0,
      dropOffOnDoor: 0,
    },
    fixMap: true,
    isHidePhone: false,
    pRc: 0,
    rRc: 0,
    pickWayInfo: 0,
    returnWayInfo: 0,
    rentCenterId: 1,
  };
  const urlQuery1: any = {
    data: '{"pickupStoreId":99999}',
  };
  const urlQuery2: any = {
    data: '{"drop":99999}',
  };
  const queryObj1 = JSON.parse(decodeURIComponent(urlQuery1.data));
  const mockStateMap = [
    {
      state: actionData,
      urlQuery: urlQuery1,
      expected: {
        fixParams: queryObj1,
        param: {
          pickupStoreId: queryObj1.pickupStoreId,
          dropoffStoreId: queryObj1.dropoffStoreId,
          rentCenterId: queryObj1.rentCenterId,
          pStoreWay: queryObj1.pStoreWay,
          rStoreWay: queryObj1.rStoreWay,
          pickupPointInfo: queryObj1.pickupPointInfo,
          returnPointInfo: queryObj1.returnPointInfo,
          pRc: queryObj1.pRc,
          rRc: queryObj1.rRc,
          pickWayInfo: queryObj1.pickWayInfo,
          returnWayInfo: queryObj1.returnWayInfo,
          reference: null,
        },
      },
    },
    {
      state: actionData,
      urlQuery: urlQuery2,
      expected: {
        fixParams: actionData,
        param: {
          pickupStoreId: actionData.pickupStoreId,
          dropoffStoreId: actionData.dropoffStoreId,
          rentCenterId: actionData.rentCenterId,
          pStoreWay: actionData.pStoreWay,
          rStoreWay: actionData.rStoreWay,
          pickupPointInfo: actionData.pickupPointInfo,
          returnPointInfo: actionData.returnPointInfo,
          pRc: actionData.pRc,
          rRc: actionData.rRc,
          pickWayInfo: actionData.pickWayInfo,
          returnWayInfo: actionData.returnWayInfo,
          reference: null,
        },
      },
    },
  ];
  test.each(mockStateMap)(
    'getOrderDetailParams check',
    ({ state, urlQuery, expected }) => {
      AppContext.setUrlQuery(urlQuery);
      const data = getApiQueryParam(state, null);
      expect(data).toEqual(expected);
    },
  );
});

describe('Guide Logic getFetchHelperParam', () => {
  const param = {
    fromurl: 'www.baidu.com',
  };
  const mockStateMap = [
    {
      state: param,
      expected: CarFetchHelper.parameterBuilder({
        param,
        cachePolicy: { enableCache: true },
      }),
    },
  ];
  test.each(mockStateMap)(
    'getFetchHelperParam check',
    ({ state, expected }) => {
      const data = getFetchHelperParam(state);
      expect(data).toEqual(expected);
    },
  );
});

jest.mock('../../../src/pages/xcar/State/Product/BbkMapper', () => {
  return {
    getGuidePageParam: jest.fn((param1, param2, data) => ({ ...data })),
  };
});
jest.mock('../../../src/pages/xcar/State/Guide/Helpers', () => {
  return {
    getParams: jest.fn(data => JSON.parse(decodeURIComponent(data))),
    checkParams: jest.fn(
      data => data && data.pickupStoreId && data.dropoffStoreId,
    ),
  };
});
describe('Guide Logic getApiQueryGuideFetchParam', () => {
  const actionData = {
    pickupStart: {
      lat: 18.30747,
      lng: 109.41201,
      addr: '凤凰国际机场T1航站楼',
    },
    dropoffStart: {
      lat: 18.30747,
      lng: 109.41201,
      addr: '凤凰国际机场T1航站楼',
    },
    pickupStoreId: '194723',
    dropoffStoreId: '194723',
    selectedId: 'pickup',
    pickupServiceType: 0,
    dropoffServiceType: 0,
    pStoreWay: '自行前往门店取还车',
    rStoreWay: '自行前往门店取还车',
    reference: null,
    pickupPointInfo: {
      cityId: 43,
      date: '2022-07-30 10:00:00',
      locationCode: '',
      locationName: '凤凰国际机场T1航站楼',
      locationType: '1',
      poi: {
        latitude: 18.30747,
        longitude: 109.41201,
        radius: 0,
      },
      pickupOnDoor: 0,
      dropOffOnDoor: 0,
    },
    returnPointInfo: {
      cityId: 43,
      date: '2022-08-01 10:00:00',
      locationCode: '',
      locationName: '凤凰国际机场T1航站楼',
      locationType: '1',
      poi: {
        latitude: 18.30747,
        longitude: 109.41201,
        radius: 0,
      },
      pickupOnDoor: 0,
      dropOffOnDoor: 0,
    },
    fixMap: true,
    isHidePhone: false,
    pRc: 0,
    rRc: 0,
    pickWayInfo: 0,
    returnWayInfo: 0,
    rentCenterId: 1,
  };
  const exceptParam = {
    pickupStoreId: actionData.pickupStoreId,
    dropoffStoreId: actionData.dropoffStoreId,
    rentCenterId: actionData.rentCenterId,
    pStoreWay: actionData.pStoreWay,
    rStoreWay: actionData.rStoreWay,
    pickupPointInfo: actionData.pickupPointInfo,
    returnPointInfo: actionData.returnPointInfo,
    pRc: actionData.pRc,
    rRc: actionData.rRc,
    pickWayInfo: actionData.pickWayInfo,
    returnWayInfo: actionData.returnWayInfo,
    reference: null,
  };
  const mockStateMap = [
    {
      state: actionData,
      expected: getFetchHelperParam(exceptParam),
    },
    {
      state: { fromurl: 'www.baidu.com' },
      expected: null,
    },
  ];

  test.each(mockStateMap)(
    'getApiQueryGuideFetchParam check',
    ({ state, expected }) => {
      const data = getApiQueryGuideFetchParam(state, null);
      expect(data).toEqual(expected);
    },
  );
});

describe('Guide Logic apiQueryGuide', () => {
  test('测试正常调用 有orderId', async () => {
    const dispatched: any = [];
    const orderId = 123556767;
    const selectedId = 'pickup';
    const fetchParams: any = {
      pickupStoreId: 99999,
      rentCenterId: 1,
      selectedId,
      orderId,
    };
    const res = {
      baseResponse: { isSuccess: true },
      pickUpGuideInfo: {
        storeAddress: '凤凰机场TI航站楼入口',
        storeWay: '店员免费接您至门店取车',
        longitude: '109.411996',
        latitude: '18.308310',
        storeName: '凤凰机场T1送车点',
        storePhone: '***********;************',
        businessTime: '10:00-20:00',
      },
      dropOffGuideInfo: {
        storeAddress: '凤凰机场TI航站楼入口',
        storeWay: '还车后店员免费送您至凤凰国际机场T1航站楼',
        longitude: '109.411996',
        latitude: '18.308310',
        storeName: '凤凰机场T1送车点',
        storePhone: '***********;************',
        businessTime: '10:00-20:00',
      },
    };
    const actionMock = {
      type: FETCH_GUIDE,
      data: fetchParams,
    };
    const logicFunc = takeEveryGeneratorFunction(apiQueryGuide);
    jest
      .spyOn(CarFetch, 'queryOrderDetailStoreGuide')
      .mockReturnValue(Promise.resolve(res));
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValueOnce(true);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    );
    const { param } = getApiQueryParam(fetchParams);
    expect(dispatched[0]).toEqual({
      type: SET_STATUS,
      data: {
        isLoading: true,
      },
    });
    expect(dispatched[1]).toEqual({
      type: FETCH_ODERNUMBER,
      data: { orderId, showLoading: false },
    });
    expect(dispatched[2]).toEqual({
      type: FETCH_GUIDE_CALLBACK,
      data: {
        isError: !res?.baseResponse?.isSuccess,
        param,
        res: packageOrderDetailGuideRes(res, selectedId),
        isLoading: false,
        errorMessage: "",
      },
    });
  });

  test('测试正常调用 有pickupStoreId&dropoffStoreId', async () => {
    const dispatched: any = [];
    const selectedId = 'pickup';
    const fetchParams: any = {
      pickupStoreId: 99999,
      dropoffStoreId: 11111,
      rentCenterId: 1,
      selectedId,
    };
    const res = {
      baseResponse: { isSuccess: true },
      pickUpGuideInfo: {
        storeAddress: '凤凰机场TI航站楼入口',
        storeWay: '店员免费接您至门店取车',
        longitude: '109.411996',
        latitude: '18.308310',
        storeName: '凤凰机场T1送车点',
        storePhone: '***********;************',
        businessTime: '10:00-20:00',
      },
      dropOffGuideInfo: {
        storeAddress: '凤凰机场TI航站楼入口',
        storeWay: '还车后店员免费送您至凤凰国际机场T1航站楼',
        longitude: '109.411996',
        latitude: '18.308310',
        storeName: '凤凰机场T1送车点',
        storePhone: '***********;************',
        businessTime: '10:00-20:00',
      },
    };
    const actionMock = {
      type: FETCH_GUIDE,
      data: fetchParams,
    };
    const logicFunc = takeEveryGeneratorFunction(apiQueryGuide);
    jest
      .spyOn(CarFetch, 'queryStoreGuide')
      .mockReturnValue(Promise.resolve(res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    );
    const { param, fixParams } = getApiQueryParam(fetchParams);
    expect(dispatched[0]).toEqual({
      type: SET_STATUS,
      data: {
        isLoading: true,
      },
    });
    expect(dispatched[1]).toEqual({
      type: FETCH_GUIDE_CALLBACK,
      data: {
        isError: !res?.baseResponse?.isSuccess,
        param,
        res: packageGuideRes(fixParams, res),
        isLoading: false,
        errorMessage: "",
      },
    });
  });

  test('测试数据异常调用 有orderId', async () => {
    const dispatched: any = [];
    const orderId = 123556767;
    const selectedId = 'pickup';
    const fetchParams: any = {
      pickupStoreId: 99999,
      rentCenterId: 1,
      selectedId,
      orderId,
    };
    const res: any = {};
    const actionMock = {
      type: FETCH_GUIDE,
      data: fetchParams,
    };
    const logicFunc = takeEveryGeneratorFunction(apiQueryGuide);
    jest
      .spyOn(CarFetch, 'queryOrderDetailStoreGuide')
      .mockReturnValue(Promise.resolve(res));
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValueOnce(true);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    );
    const { param } = getApiQueryParam(fetchParams);
    expect(dispatched[0]).toEqual({
      type: SET_STATUS,
      data: {
        isLoading: true,
      },
    });
    expect(dispatched[1]).toEqual({
      type: FETCH_ODERNUMBER,
      data: { orderId, showLoading: false },
    });

    expect(dispatched[2]).toEqual({
      type: FETCH_GUIDE_CALLBACK,
      data: {
        isError: !res?.baseResponse?.isSuccess,
        param,
        res: packageOrderDetailGuideRes(res, selectedId),
        isLoading: false,
        errorMessage: "",
      },
    });
  });

  test('接口异常调用 有pickupStoreId&dropoffStoreId', async () => {
    const dispatched: any = [];
    const selectedId = 'pickup';
    const fetchParams: any = {
      pickupStoreId: 99999,
      dropoffStoreId: 11111,
      rentCenterId: 1,
      selectedId,
    };
    const actionMock = {
      type: FETCH_GUIDE,
      data: fetchParams,
    };
    const exceptionError = new Error('queryStoreGuide exception');
    const logicFunc = takeEveryGeneratorFunction(apiQueryGuide);
    jest.spyOn(CarFetch, 'queryStoreGuide').mockReturnValue(
      new Promise(() => {
        throw exceptionError;
      }),
    );
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: SET_STATUS,
      data: {
        isLoading: true,
      },
    });
    expect(dispatched[1]).toEqual({
      type: FETCH_GUIDE_CALLBACK,
      data: {
        isError: true,
        res: exceptionError,
        isLoading: false,
      },
    });
  });
});
