import ModifyOrderConfirmReducer, {
  initalState,
  updateSelectedInsurance,
  undoChangePrice,
} from '../../../src/pages/xcar/State/ModifyOrderConfirm/Reducer';
import {
  CHANGE_SELECT_INSURANCE,
  UPDATE_FEE_INFO,
  UNDO_CHAGE_PRICE,
  SET_STATUS,
  RESET_CONFIRM_DATA,
  SET_CONFIRM_MODAL_VISIBLE,
  SET_CONFIRM_MODAL_PROPS,
  SET_CREATE_INS_MODAL_VISIBLE,
  SET_INS_FAILED_MODAL_VISIBLE,
  SET_MODIFY_ORDER_MODAL_PROPS,
  CHANGE_COUPON,
  MODIFY_TO_PAY_SET_LOADING,
  SET_PAY_TIME_OUT_MODAL_VISIBLE,
  SET_MODIFY_DISCOUNTINFO,
} from '../../../src/pages/xcar/State/ModifyOrderConfirm/Types';

describe('ModifyOrderConfirm Reducer Test', () => {
  test('Init', () => {
    expect(ModifyOrderConfirmReducer(undefined, {})).toEqual(initalState);
  });

  test('MODIFY_TO_PAY_SET_LOADING', () => {
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: MODIFY_TO_PAY_SET_LOADING,
        data: true,
      }),
    ).toEqual({
      ...initalState,
      isLoading: true,
    });
  });

  test('CHANGE_SELECT_INSURANCE', () => {
    const mockData = {
      checked: true,
      index: 0,
      localTotalPrice: 50,
      name: '人身及财物险',
      uniqueCode: '2000896',
      vendorServiceCode: '2000896',
    };
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: CHANGE_SELECT_INSURANCE,
        data: mockData,
      }),
    ).toEqual(updateSelectedInsurance(initalState, mockData));
  });

  test('UPDATE_FEE_INFO', () => {
    const feeInfo = {};
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: UPDATE_FEE_INFO,
        data: feeInfo,
      }),
    ).toEqual({
      ...initalState,
      feeInfo: feeInfo,
    });
  });

  test('UNDO_CHAGE_PRICE', () => {
    const mockData = {};
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: UNDO_CHAGE_PRICE,
        data: mockData,
      }),
    ).toEqual(undoChangePrice(initalState, mockData));
  });

  test('SET_STATUS', () => {
    const mockData = { isPriceLoading: false };
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: SET_STATUS,
        data: mockData,
      }),
    ).toEqual({
      ...initalState,
      ...mockData,
    });
  });

  test('RESET_CONFIRM_DATA', () => {
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: RESET_CONFIRM_DATA,
      }),
    ).toEqual({
      ...initalState,
    });
  });

  test('SET_CONFIRM_MODAL_VISIBLE', () => {
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: SET_CONFIRM_MODAL_VISIBLE,
        data: true,
      }),
    ).toEqual({
      ...initalState,
      confirmModalVisible: true,
    });
  });

  test('SET_CONFIRM_MODAL_PROPS', () => {
    const confirmModalProps = {};
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: SET_CONFIRM_MODAL_PROPS,
        data: confirmModalProps,
      }),
    ).toEqual({
      ...initalState,
      confirmModalProps: confirmModalProps,
    });
  });

  test('SET_CREATE_INS_MODAL_VISIBLE', () => {
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: SET_CREATE_INS_MODAL_VISIBLE,
        data: true,
      }),
    ).toEqual({
      ...initalState,
      createInsModalVisible: true,
    });
  });

  test('SET_INS_FAILED_MODAL_VISIBLE', () => {
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: SET_INS_FAILED_MODAL_VISIBLE,
        data: true,
      }),
    ).toEqual({
      ...initalState,
      insFailedModalVisible: true,
    });
  });

  test('CHANGE_COUPON', () => {
    const selectedCouponsCodes = '';
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: CHANGE_COUPON,
        data: { couponCode: selectedCouponsCodes },
      }),
    ).toEqual({
      ...initalState,
      selectedCouponsCodes: selectedCouponsCodes,
    });
  });

  test('SET_MODIFY_DISCOUNTINFO', () => {
    const mockData = {};
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: SET_MODIFY_DISCOUNTINFO,
        data: mockData,
      }),
    ).toEqual({
      ...initalState,
      ...mockData,
    });
  });

  test('SET_MODIFY_ORDER_MODAL_PROPS', () => {
    const mockData = {};
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: SET_MODIFY_ORDER_MODAL_PROPS,
        data: mockData,
      }),
    ).toEqual({
      ...initalState,
      modifyOrderModalProps: {
        ...initalState.modifyOrderModalProps,
        ...mockData,
      },
    });
  });

  test('SET_PAY_TIME_OUT_MODAL_VISIBLE', () => {
    expect(
      ModifyOrderConfirmReducer(initalState, {
        type: SET_PAY_TIME_OUT_MODAL_VISIBLE,
        data: true,
      }),
    ).toEqual({
      ...initalState,
      payTimeOutModalVisible: true,
    });
  });
});
