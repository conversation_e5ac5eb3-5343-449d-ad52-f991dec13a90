import { couponFailedCode } from './../../../src/pages/xcar/Components/CouponPreValidationModals/Index';
import { IModifyConfirmErrorMsgType } from './../../../src/pages/xcar/State/ModifyOrderConfirm/Types';
import * as Payment from '../../../src/pages/xcar/Util/Payment/Index';
import CRNToast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import { recordSaga } from '../../testHelpers';
import {
  getPriceInfo,
  onPressSubmit,
  modifyToPay,
} from '../../../src/pages/xcar/State/ModifyOrderConfirm/Logic';
import * as Types from '../../../src/pages/xcar/State/ModifyOrderConfirm/Types';
import * as Actions from '../../../src/pages/xcar/State/ModifyOrderConfirm/Actions';
import * as ModifyOrderActions from '../../../src/pages/xcar/State/ModifyOrder/Actions';
import * as CouponActions from '../../../src/pages/xcar/State/Coupon/Actions';
import * as OrderActions from '../../../src/pages/xcar/State/OrderDetail/Actions';
import { AppContext, CarFetch } from '../../../src/pages/xcar/Util/Index';
import PREVIOUS_STATE from '../../../src/pages/xcar/State/__Global/Types';

jest.mock('../../../src/pages/xcar/Util/Payment/Index', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Util/Payment/Index') }));

describe('queryRenewalProductLogic', () => {
  test('getPriceInfo', async () => {
    // mock接口返回数据
    const modifyPriceAPI = jest.spyOn(CarFetch, 'modifyPrice').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      feeInfo: {},
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(getPriceInfo, {
      action: {
        type: Types.CHANGE_SELECT_INSURANCE,
        data: {},
        [PREVIOUS_STATE]: {
          ModifyOrderConfirm: {},
        },
      },
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
        ModifyOrderConfirm: {
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: '111111',
          },
        }
      },
    });

    expect(modifyPriceAPI).toBeCalled();
    expect(dispatched).toEqual([
      Actions.setStatus({
        isPriceLoading: true,
      }),
      Actions.upDateFeeInfo({}),
      Actions.setStatus({
        isPriceLoading: false,
      }),
    ]);
  })

  test('onPressSubmit 接口失败', async () => {
    const popFn = jest.fn();

    AppContext.setPageInstance({
      getPageId: () => 222009,
      pop: popFn,
    });

    const ToastShow = jest.spyOn(CRNToast, 'show');

    // mock接口返回数据
    const modifyConfirmAPI = jest.spyOn(CarFetch, 'modifyConfirm').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: false,
      },
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(onPressSubmit, {
      action: {
        type: Types.ON_PRESS_SUBMIT,
        data: {},
        [PREVIOUS_STATE]: {
          ModifyOrderConfirm: {},
        },
      },
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
        ModifyOrderConfirm: {
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: '111111',
          },
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
        }
      },
    });

    expect(modifyConfirmAPI).toBeCalled();
    // expect(ToastShow).toBeCalledWith('网络失败');
    expect(dispatched).toEqual([
      Actions.setStatus({
        isSubmitting: true,
      }),
      Actions.setStatus({
        isSubmitting: false,
      }),
    ]);
  })

  test('onPressSubmit 异常信息提示 priceChanged', async () => {
    const popFn = jest.fn();

    AppContext.setPageInstance({
      getPageId: () => 222009,
      pop: popFn,
    });

    const ToastShow = jest.spyOn(CRNToast, 'show');

    // mock接口返回数据
    const modifyConfirmAPI = jest.spyOn(CarFetch, 'modifyConfirm').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      errorMsg: [{
        title: '',
        type: IModifyConfirmErrorMsgType.priceChanged,
        description: '价格已失效',
        groupCode: '',
      }]
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(onPressSubmit, {
      action: {
        type: Types.ON_PRESS_SUBMIT,
        data: {},
        [PREVIOUS_STATE]: {
          ModifyOrderConfirm: {},
        },
      },
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
        ModifyOrderConfirm: {
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: '111111',
          },
        }
      },
    });

    expect(modifyConfirmAPI).toBeCalled();
    expect(dispatched).toEqual([
      Actions.setStatus({
        isSubmitting: true,
      }),
      Actions.setConfirmModalProps(expect.objectContaining({
        title: '价格已失效',
        isReSearch: true,
      })),
      Actions.setConfirmModalVisible(true),
      Actions.setStatus({
        isSubmitting: false,
      }),
    ]);
  })

  test('onPressSubmit 异常信息提示 noInventory', async () => {
    const popFn = jest.fn();

    AppContext.setPageInstance({
      getPageId: () => 222009,
      pop: popFn,
    });

    const ToastShow = jest.spyOn(CRNToast, 'show');

    // mock接口返回数据
    const modifyConfirmAPI = jest.spyOn(CarFetch, 'modifyConfirm').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      errorMsg: [{
        title: '提示',
        type: IModifyConfirmErrorMsgType.noInventory,
        description: '无库存',
        groupCode: '',
      }]
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(onPressSubmit, {
      action: {
        type: Types.ON_PRESS_SUBMIT,
        data: {},
        [PREVIOUS_STATE]: {
          ModifyOrderConfirm: {},
        },
      },
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
        ModifyOrderConfirm: {
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: '111111',
          },
          modifyInfoDto: {
            "modifyCancelRules": [{
              "code": "1",
              "content": "当前订单取消预估扣除违约金¥40，可在填写新订单时申请退还",
              "contentAlert": [
                "当前订单取消预估扣除违约金¥40",
                "可在填写新订单时申请退还，实际扣除费用以最终结果为准"
              ],
              "highLight": "扣除违约金¥40",
              "color": 0
            }, {
              "code": "1",
              "content": "当前订单2021-06-20 06:00前可免费取消。",
              "color": 1
            }],
          }
        }
      },
    });

    expect(modifyConfirmAPI).toBeCalled();
    expect(dispatched).toEqual([
      Actions.setStatus({
        isSubmitting: true,
      }),
      ModifyOrderActions.setModifyOrderWarnModalVisible(true, {
        header: '提示',
        title: '无库存',
        isWarnTip: true,
        tip: '当前订单取消预估扣除违约金¥40，可在填写新订单时申请退还',
      }),
      Actions.setStatus({
        isSubmitting: false,
      }),
    ]);
  })


  test('onPressSubmit 异常信息提示 couponFailedCode.BeenUsed', async () => {
    const popFn = jest.fn();

    AppContext.setPageInstance({
      getPageId: () => 222009,
      pop: popFn,
    });

    const ToastShow = jest.spyOn(CRNToast, 'show');

    // mock接口返回数据
    const modifyConfirmAPI = jest.spyOn(CarFetch, 'modifyConfirm').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      errorGroup: '11',
      errorMsg: [{
        title: '提示',
        type: couponFailedCode.BeenUsed,
        description: 'description',
        groupCode: '',
      }]
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(onPressSubmit, {
      action: {
        type: Types.ON_PRESS_SUBMIT,
        data: {},
        [PREVIOUS_STATE]: {
          ModifyOrderConfirm: {},
        },
      },
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
        ModifyOrderConfirm: {
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: '111111',
          },
          modifyInfoDto: {
            "modifyCancelRules": [{
              "code": "1",
              "content": "当前订单取消预估扣除违约金¥40，可在填写新订单时申请退还",
              "contentAlert": [
                "当前订单取消预估扣除违约金¥40",
                "可在填写新订单时申请退还，实际扣除费用以最终结果为准"
              ],
              "highLight": "扣除违约金¥40",
              "color": 0
            }, {
              "code": "1",
              "content": "当前订单2021-06-20 06:00前可免费取消。",
              "color": 1
            }],
          }
        }
      },
    });

    expect(modifyConfirmAPI).toBeCalled();
    expect(dispatched).toEqual([
      Actions.setStatus({
        isSubmitting: true,
      }),
      CouponActions.setCouponPreValidationModalVisible(true, {
        code: '51',
        groupCode: '11',
        title: '提示',
        msg: 'description',
      }),
      Actions.setStatus({
        isSubmitting: false,
      }),
    ]);
  })

  test('onPressSubmit 调用支付', async () => {
    const popFn = jest.fn();

    AppContext.setPageInstance({
      getPageId: () => 222009,
      pop: popFn,
    });

    const MiddlePayApi =  jest.spyOn(Payment, 'MiddlePay').mockImplementation(() => Promise.resolve({
      success: true,
      showError: true,
      status: null,
      result: null
    }))

    const ToastShow = jest.spyOn(CRNToast, 'show');

    // mock接口返回数据
    const modifyConfirmAPI = jest.spyOn(CarFetch, 'modifyConfirm').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(onPressSubmit, {
      action: {
        type: Types.ON_PRESS_SUBMIT,
        data: {},
        [PREVIOUS_STATE]: {
          ModifyOrderConfirm: {},
        },
      },
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
        ModifyOrderConfirm: {
          feeInfo: {
            payAmount: 100,
          }
        },
        Booking: {
          driverInfo: [
            { type: 'mobilePhone', value: '15800000000', error: false },
            { type: 'flightNumber', value: '', error: false },
            { type: 'areaCode', value: '86', error: false },
          ],
        },
        DriverList: {
          passenger: {
            "userName": "zhangsan",
          },
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: '111111',
          },
          vehicleInfo: {
            "brandId": 0,
            "brandEName": "大众",
            "brandName": "大众",
            "name": "大众朗逸",
          },
          vendorInfo: {
            "vendorName": "鹏琛租车",
            "vendorImageUrl": "http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg",
            "vendorID": 62494,
            "vendorConfirmCode": "3082488752",
            "isSelf": false,
            "selfName": "",
            "vendorMobileImageUrl": "",
            "commentInfo": {
              "vendorGoodType": 0,
              "exposedScore": 0,
              "topScore": 5,
              "level": "",
              "commentCount": 2,
              "hasComment": 0
            }
          },
          modifyInfoDto: {
            "modifyCancelRules": [{
              "code": "1",
              "content": "当前订单取消预估扣除违约金¥40，可在填写新订单时申请退还",
              "contentAlert": [
                "当前订单取消预估扣除违约金¥40",
                "可在填写新订单时申请退还，实际扣除费用以最终结果为准"
              ],
              "highLight": "扣除违约金¥40",
              "color": 0
            }, {
              "code": "1",
              "content": "当前订单2021-06-20 06:00前可免费取消。",
              "color": 1
            }],
          }
        }
      },
    });

    expect(modifyConfirmAPI).toBeCalled();
    expect(MiddlePayApi).toBeCalled();
    expect(dispatched).toEqual([
      Actions.setStatus({
        isSubmitting: true,
      }),
      Actions.setStatus({
        isSubmitting: false,
      }),
      OrderActions.fetchOrder2({
        orderId: '111111',
      }),
      Actions.setStatus({
        isSubmitting: false,
      }),
    ]);
  })


  test('modifyToPay 获取补款修改订单信息', async () => {
    const popFn = jest.fn();
    const pushFn = jest.fn();

    AppContext.setPageInstance({
      getPageId: () => 222009,
      pop: popFn,
      push: pushFn,
    });

    const ToastShow = jest.spyOn(CRNToast, 'show');

    // mock接口返回数据
    const modifyToPayApi = jest.spyOn(CarFetch, 'modifyToPay').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      discountInfo: {}
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(modifyToPay, {
      action: {
        type: Types.MODIFY_TO_PAY,
        data: {
          orderId: '1111111'
        },
      },
      state: {}
    });

    expect(modifyToPayApi).toBeCalled();
    expect(dispatched).toEqual([
      ModifyOrderActions.setModifyOrderResponse({
        baseResponse: {
          isSuccess: true,
        },
        discountInfo: {}
      }),
      Actions.setModifyDiscountInfo({
        discountInfo: {},
        enablePressCoupon: false,
      }),
    ]);
    expect(pushFn).toBeCalled();
  })

  test('modifyToPay 获取补款修改信息失败', async () => {
    const popFn = jest.fn();

    AppContext.setPageInstance({
      getPageId: () => 222009,
      pop: popFn,
    });

    const ToastShow = jest.spyOn(CRNToast, 'show');

    // mock接口返回数据
    const modifyToPayApi = jest.spyOn(CarFetch, 'modifyToPay').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: false,
        returnMsg: 'returnMsg',
      },
      discountInfo: {}
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(modifyToPay, {
      action: {
        type: Types.MODIFY_TO_PAY,
        data: {
          orderId: '1111111'
        },
      },
      state: {}
    });

    expect(modifyToPayApi).toBeCalled();
    // expect(ToastShow).toBeCalledWith('returnMsg');
  })
});


