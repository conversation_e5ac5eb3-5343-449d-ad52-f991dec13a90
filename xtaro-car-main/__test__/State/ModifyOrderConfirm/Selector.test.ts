import {
  getPageInfo,
  getComPriceCode,
  getPriceCode,
  getInfo,
  getModifyOrderFeeInfo,
  getProductInfo,
  getCancelRules,
  getActivity,
  getPayDeadline,
  getModifyOrderConfirmFeeInfo,
  getSelectedInsuranceId,
  getSelectedCouponsCodes,
  getFeeInfo,
  getIsPriceLoading,
  getIsSubmitting,
  getModifyOrderConfirmState,
  getConfirmModalVisible,
  getConfirmModalProps,
  getCreateInsModalVisible,
  getInsFailedModalVisible,
  getModifyOrderModalProps,
  getExplain,
  getActivityDetail,
  getCouponList,
  getIsLoading,
  getPayTimeOutModalVisible,
  getEnablePressCoupon,
  getPaymentParams,
  getInsConfirmReqParam,
  getModifyDeposit,
  getDepositDescTable,
  getDepositDesc,
} from '../../../src/pages/xcar/State/ModifyOrderConfirm/Selector';

describe('ModifyOrderConfirm Selectors getPageInfo', () => {
  const pageInfo = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            pageInfo,
          },
        },
      },
      expected: pageInfo,
    },
  ];
  test.each(mockStateMap)('getPageInfo check', ({ state, expected }) => {
    const result = getPageInfo(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getComPriceCode', () => {
  const comPriceCode = 'x';
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            comPriceCode,
          },
        },
      },
      expected: comPriceCode,
    },
  ];
  test.each(mockStateMap)('getComPriceCode check', ({ state, expected }) => {
    const result = getComPriceCode(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getPriceCode', () => {
  const priceCode = 'x';
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            priceCode,
          },
        },
      },
      expected: priceCode,
    },
  ];
  test.each(mockStateMap)('getPriceCode check', ({ state, expected }) => {
    const result = getPriceCode(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getInfo', () => {
  const info = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            info,
          },
        },
      },
      expected: info,
    },
  ];
  test.each(mockStateMap)('getInfo check', ({ state, expected }) => {
    const result = getInfo(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getModifyOrderFeeInfo', () => {
  const feeInfo = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            feeInfo,
          },
        },
      },
      expected: feeInfo,
    },
  ];
  test.each(mockStateMap)(
    'getModifyOrderFeeInfo check',
    ({ state, expected }) => {
      const result = getModifyOrderFeeInfo(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getProductInfo', () => {
  const productInfo = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            productInfo,
          },
        },
      },
      expected: productInfo,
    },
  ];
  test.each(mockStateMap)('getProductInfo check', ({ state, expected }) => {
    const result = getProductInfo(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getCancelRules', () => {
  const cancelRules = [];
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            cancelRules,
          },
        },
      },
      expected: cancelRules,
    },
  ];
  test.each(mockStateMap)('getCancelRules check', ({ state, expected }) => {
    const result = getCancelRules(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getActivity', () => {
  const activity = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            activity,
          },
        },
      },
      expected: activity,
    },
  ];
  test.each(mockStateMap)('getActivity check', ({ state, expected }) => {
    const result = getActivity(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getPayDeadline', () => {
  const payDeadline = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            payDeadline,
          },
        },
      },
      expected: payDeadline,
    },
  ];
  test.each(mockStateMap)('getPayDeadline check', ({ state, expected }) => {
    const result = getPayDeadline(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getModifyOrderConfirmFeeInfo', () => {
  const feeInfo = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          feeInfo,
        },
      },
      expected: feeInfo,
    },
  ];
  test.each(mockStateMap)(
    'getModifyOrderConfirmFeeInfo check',
    ({ state, expected }) => {
      const result = getModifyOrderConfirmFeeInfo(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getSelectedInsuranceId', () => {
  const selectedInsuranceId = [];
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          selectedInsuranceId,
        },
      },
      expected: selectedInsuranceId,
    },
  ];
  test.each(mockStateMap)(
    'getSelectedInsuranceId check',
    ({ state, expected }) => {
      const result = getSelectedInsuranceId(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getSelectedCouponsCodes', () => {
  const selectedCouponsCodes = [];
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          selectedCouponsCodes,
        },
      },
      expected: selectedCouponsCodes,
    },
  ];
  test.each(mockStateMap)(
    'getSelectedCouponsCodes check',
    ({ state, expected }) => {
      const result = getSelectedCouponsCodes(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getFeeInfo', () => {
  const feeInfo = {};
  const modifyOrderFeeInfo = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            feeInfo: modifyOrderFeeInfo,
          },
        },
        ModifyOrderConfirm: {
          feeInfo,
        },
      },
      expected: feeInfo,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            feeInfo: modifyOrderFeeInfo,
          },
        },
        ModifyOrderConfirm: {},
      },
      expected: modifyOrderFeeInfo,
    },
  ];
  test.each(mockStateMap)('getFeeInfo check', ({ state, expected }) => {
    const result = getFeeInfo(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getIsPriceLoading', () => {
  const isPriceLoading = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          isPriceLoading,
        },
      },
      expected: isPriceLoading,
    },
  ];
  test.each(mockStateMap)('getIsPriceLoading check', ({ state, expected }) => {
    const result = getIsPriceLoading(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getIsSubmitting', () => {
  const isSubmitting = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          isSubmitting,
        },
      },
      expected: isSubmitting,
    },
  ];
  test.each(mockStateMap)('getIsSubmitting check', ({ state, expected }) => {
    const result = getIsSubmitting(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getModifyOrderConfirmState', () => {
  const ModifyOrderConfirm = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: null,
      },
      expected: null,
    },
    {
      state: {
        ModifyOrderConfirm,
      },
      expected: ModifyOrderConfirm,
    },
  ];
  test.each(mockStateMap)(
    'getModifyOrderConfirmState check',
    ({ state, expected }) => {
      const result = getModifyOrderConfirmState(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getConfirmModalVisible', () => {
  const confirmModalVisible = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          confirmModalVisible,
        },
      },
      expected: confirmModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getConfirmModalVisible check',
    ({ state, expected }) => {
      const result = getConfirmModalVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getConfirmModalProps', () => {
  const confirmModalProps = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          confirmModalProps,
        },
      },
      expected: confirmModalProps,
    },
  ];
  test.each(mockStateMap)(
    'getConfirmModalProps check',
    ({ state, expected }) => {
      const result = getConfirmModalProps(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getCreateInsModalVisible', () => {
  const createInsModalVisible = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          createInsModalVisible,
        },
      },
      expected: createInsModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getCreateInsModalVisible check',
    ({ state, expected }) => {
      const result = getCreateInsModalVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getInsFailedModalVisible', () => {
  const insFailedModalVisible = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          insFailedModalVisible,
        },
      },
      expected: insFailedModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getInsFailedModalVisible check',
    ({ state, expected }) => {
      const result = getInsFailedModalVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getModifyOrderModalProps', () => {
  const modifyOrderModalProps = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          modifyOrderModalProps,
        },
      },
      expected: modifyOrderModalProps,
    },
  ];
  test.each(mockStateMap)(
    'getModifyOrderModalProps check',
    ({ state, expected }) => {
      const result = getModifyOrderModalProps(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getExplain', () => {
  const explain = 'x';
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {
          discountInfo: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          discountInfo: {
            explain,
          },
        },
      },
      expected: explain,
    },
  ];
  test.each(mockStateMap)('getExplain check', ({ state, expected }) => {
    const result = getExplain(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getActivityDetail', () => {
  const activityDetail = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {
          discountInfo: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          discountInfo: {
            activityDetail,
          },
        },
      },
      expected: activityDetail,
    },
  ];
  test.each(mockStateMap)('getActivityDetail check', ({ state, expected }) => {
    const result = getActivityDetail(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getCouponList', () => {
  const couponList = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {
          discountInfo: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          discountInfo: {
            couponList,
          },
        },
      },
      expected: couponList,
    },
  ];
  test.each(mockStateMap)('getCouponList check', ({ state, expected }) => {
    const result = getCouponList(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getIsLoading', () => {
  const isLoading = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          isLoading,
        },
      },
      expected: isLoading,
    },
  ];
  test.each(mockStateMap)('getIsLoading check', ({ state, expected }) => {
    const result = getIsLoading(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getPayTimeOutModalVisible', () => {
  const payTimeOutModalVisible = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          payTimeOutModalVisible,
        },
      },
      expected: payTimeOutModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getPayTimeOutModalVisible check',
    ({ state, expected }) => {
      const result = getPayTimeOutModalVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getEnablePressCoupon', () => {
  const enablePressCoupon = true;
  const mockStateMap = [
    {
      state: {
        ModifyOrderConfirm: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrderConfirm: {
          enablePressCoupon,
        },
      },
      expected: enablePressCoupon,
    },
  ];
  test.each(mockStateMap)(
    'getEnablePressCoupon check',
    ({ state, expected }) => {
      const result = getEnablePressCoupon(state);
      expect(result).toEqual(expected);
    },
  );
});

jest.mock('uuid', () => {
  return jest.fn().mockImplementation(() => {
    return "1234567890";
  });
});
describe('ModifyOrderConfirm Selectors getPaymentParams', () => {
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          reqOrderParams: {
            channelType: 14277,
            orderId: '18780975900',
            pageId: '222025',
            requestId: 'b8e42fc5-24fb-4928-a87b-25da986ec2ea',
          },
          vendorInfo: {
            commentInfo: {
              commentCount: 30454,
              commentLabel: '店员热情',
              exposedScore: 5,
              hasComment: 1,
              level: '超棒',
              link: '/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=STORE_QUERY&storeId=11006&vehicleId=76338&vehicleName=雪佛兰科沃兹&productCategoryId=35&isHideNavBar=YES',
              topScore: 5,
              vendorGoodType: 1,
            },
            isSelf: true,
            selfName: '携程优选',
            vendorConfirmCode: '18780999377',
            vendorID: 30164,
            vendorImageUrl: '//pic.c-ctrip.com/car_isd/vendorlogo/30164.jpg',
            vendorMobileImageUrl: '',
            vendorName: '懒人行租车(携程优选)',
          },
          vehicleInfo: {
            name: '懒人行租车(携程优选)',
            ctripVehicleID: '20150',
            displacement: '1.0T',
            doorNum: 4,
            driveMode: '前置前驱',
            fuel: '汽油92号',
            gearbox: '双离合变速箱(DCT)',
            grantCode: '',
            granted: false,
            imageUrl:
              'https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0scn143fai2ah4w9B69.jpg',
            labels: [],
            license: '',
            licenseStyle: '2',
            oilType: 5,
            passengerNum: 5,
            similarImageUrls: [
              'https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0scn143fai2ah4w9B69.jpg',
            ],
            struct: '三厢',
            style: '',
            transmission: 'AT',
            vehicleAccessoryImages: [
              'https://dimg04.c-ctrip.com//images/0414k120008at15l04B7B.jpg',
              'https://dimg04.c-ctrip.com//images/04139120008at3x402339.jpg',
              'https://dimg04.c-ctrip.com//images/0416n120008at1b3t7DE9.jpg',
              'https://dimg04.c-ctrip.com//images/04135120008at4it05F01.jpg',
              'https://dimg04.c-ctrip.com//images/04142120008at10cy3FB8.jpg',
              'https://dimg04.c-ctrip.com//images/0413g120008at239eCF3C.jpg',
              'https://dimg04.c-ctrip.com//images/04171120008at46pr9E1E.jpg',
              'https://dimg04.c-ctrip.com//images/0415b120008at1hgaA2A9.jpg',
            ],
            vehicleDegree: '0',
            vehicleGroupName: '经济轿车',
            vehicleName: '雪佛兰科沃兹',
            vendorVehicleCode: '20037374',
            vendorVehicleID: 76338,
          },
        },
        ModifyOrder: {
          modifyOrderResponse: {
            cancelExplain: '修改取还车信息后，请留意取消政策的更新。',
            baseResponse: {
              code: 'unknown',
              requestId: '',
              cost: 547,
              isSuccess: true,
              returnMsg: 'success',
            },
            resultType: 3,
            responseStatus: {
              ack: 'Success',
              errors: [],
              timestamp: '/Date(1661924223895+0800)/',
              extension: [
                {
                  id: 'CLOGGING_TRACE_ID',
                  value: '1369464516399727479',
                },
                {
                  id: 'RootMessageId',
                  value: '921822-0a715c50-461645-2557122',
                },
              ],
            },
            comPriceCode:
              'eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiUFJJQ0VfQ0hBTk5FTF8ifQ==',
            feeInfo: {
              totalAmount: 164,
              payAmount: 69,
              contents: [
                {
                  title: '在线补款后，修改即可生效。',
                  type: 0,
                  code: 'gray',
                },
                {
                  code: 'bold',
                  title: '在线补款',
                  type: 1,
                  typeDesc: '¥69',
                },
              ],
              feeInfoList: [
                {
                  fixed: true,
                  oldPrice: {
                    amount: 48,
                    priceDailys: [
                      {
                        date: '10月25日 周二',
                        priceStr: '¥48',
                        showType: 0,
                      },
                    ],
                    subTitle: '¥48×1天',
                    count: 1,
                    price: 48,
                    unit: '',
                  },
                  newPrice: {
                    amount: 96,
                    priceDailys: [
                      {
                        priceStr: '¥48',
                        showType: 0,
                        oDprice: '',
                        date: '10月24日 周一',
                      },
                      {
                        priceStr: '¥48',
                        showType: 0,
                        oDprice: '',
                        date: '10月25日 周二',
                      },
                    ],
                    subTitle: '¥48×2天',
                    count: 2,
                    price: 48,
                    unit: '',
                  },
                  title: '租车费',
                  serviceCode: '1001',
                  type: 0,
                  changeType: 1,
                },
                {
                  fixed: true,
                  oldPrice: {
                    amount: 30,
                    count: 1,
                    price: 30,
                    unit: '',
                  },
                  newPrice: {
                    amount: 60,
                    priceDailys: [],
                    count: 2,
                    price: 30,
                    unit: '',
                  },
                  title: '基础服务费',
                  serviceCode: '1002',
                  type: 0,
                  changeType: 1,
                },
                {
                  fixed: true,
                  oldPrice: {
                    amount: 20,
                    count: 1,
                    price: 20,
                    unit: '',
                  },
                  newPrice: {
                    amount: 20,
                    priceDailys: [],
                    count: 1,
                    price: 20,
                    unit: '',
                  },
                  title: '车行手续费',
                  serviceCode: '1003',
                  type: 0,
                  changeType: 0,
                },
                {
                  fixed: true,
                  oldPrice: {
                    amount: 3,
                  },
                  newPrice: {
                    subTitle: '不可享',
                  },
                  title: '活动',
                  serviceCode: 'act_104540',
                  type: 1,
                  subTitle: '黄金贵宾',
                  changeType: 1,
                },
                {
                  fixed: true,
                  oldPrice: {
                    subTitle: '无',
                  },
                  newPrice: {
                    amount: 12,
                  },
                  title: '活动',
                  serviceCode: 'act_121129',
                  type: 1,
                  subTitle: '周三福利日',
                  changeType: 2,
                },
                {
                  fixed: true,
                  oldPrice: {
                    amount: 95,
                  },
                  newPrice: {
                    amount: 164,
                  },
                  title: '订单总额',
                  fieldStyle: 'b',
                  serviceCode: 'total',
                  type: 4,
                  subTitle: '在线支付',
                  changeType: 1,
                },
              ],
            },
            discountInfo: {
              explain: '请留意可使用的优惠更新。',
              activityDetail: {
                title: '周三福利日',
                status: 1,
                promotion: {
                  promotionId: 121129,
                  code: 'PMS_SAT18620602129',
                  title: '周三福利日',
                  selected: true,
                  description: '减免12%',
                  isFromCtrip: false,
                  deductionAmount: 12,
                  payofftype: 2,
                },
              },
              couponList: {
                status: 0,
                title: '暂无可用优惠券',
              },
            },
            pageInfo: {
              type: 2,
              title: '请确认已修改的取还车信息',
              freeDeposit: true,
            },
            cancelRules: [
              {
                free: 1,
                title: '免费取消',
                context: '取车时间前',
                time: '2022-10-24 20:00前',
              },
              {
                free: 0,
                title: '扣订单全额',
                context: '取车时间后',
                time: '2022-10-24 20:00后',
              },
            ],
            activity: {
              explain: '',
              title: '活动',
              activities: [
                {
                  prefixText: 'modify.after.text',
                  suffixType: '1',
                  name: '活动',
                  suffixText: '3.00',
                  originText: '3.00',
                },
              ],
            },
            info: {
              pickUpLocation: {
                cityId: 43,
                bjTime: '/Date(1666612800000+0800)/',
                longitude: 109.414693,
                time: '/Date(1666612800000+0800)/',
                areaType: 1,
                latitude: 18.303395,
                name: '凤凰国际机场',
                cityName: '三亚',
              },
              driver: {
                mobile: '15800000000',
                certificateType: 1,
                name: '张三',
                certificateNumber: '310111199208080000',
              },
              returnLocation: {
                cityId: 43,
                bjTime: '/Date(1666785600000+0800)/',
                longitude: 109.414693,
                time: '/Date(1666785600000+0800)/',
                areaType: 1,
                latitude: 18.303395,
                name: '凤凰国际机场',
                cityName: '三亚',
              },
            },
            priceCode: 'e2555a15679044ae88914db92365edc0',
            appResponseMap: {
              isFromCache: false,
              isCacheValid: true,
              networkCost: 599,
              environmentCost: 0,
              cacheFetchCost: 0,
              fetchCost: 599,
              setCacheCost: 0,
              cacheFrom: '',
              beforeFetch: 1661924223310,
              afterFetch: 1661924223909,
            },
          },
        },
        Booking: {
          driverInfo: [
            { type: 'mobilePhone', value: '15800000000', error: false },
            { type: 'flightNumber', value: '', error: false },
            { type: 'areaCode', value: '86', error: false },
          ],
          isNameReverse: false,
        },
        DriverList: {
          passenger: {
            age: 30,
            birthday: '1992-08-08',
            certificateList: [
              { certificateNo: '310111199208080000', certificateType: '1' },
            ],
            countryCode: '86',
            email: '',
            firstName: 'SAN',
            fullName: '张三',
            isCreditQualified: false,
            isDefault: true,
            isRecommend: true,
            isSelf: false,
            lastName: 'ZHANG',
            mobile: '15800000000',
            nationality: 'CN',
            nationalityName: '中国',
            passengerId: '114790932',
            sortExt:
              'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:-1661850087000|orderSort:-1661841236924|selfSort:0|requestPassenger:0',
          },
          curCertificates: {},
          availableCertificates: ['1'],
        },
        ModifyOrderConfirm: {
          selectedInsuranceId: [],
        },
      },
      expected: {
        amount: 69,
        chargesInfos: [],
        currency: 'CNY',
        driver: {
          age: 30,
          areaCode: '86',
          birthday: '1992-08-08',
          cellPhone: '15800000000',
          countryCode: '',
          driverCountryCode: 'CN',
          driverCountryName: '中国',
          firstName: 'ZHANG',
          flightNo: '',
          name: 'ZHANG SAN',
          secondName: 'SAN',
        },
        freeCancel: '',
        insExtend: {},
        isHertzPrepay: false,
        orderId: '18780975900',
        payAmountInfo: {},
        payName: '在线预付',
        pickupLocation: '凤凰国际机场',
        ptime: '2022-10-24 20:00:00',
        requestId: '1234567890',
        returnLocation: '凤凰国际机场',
        rtime: '2022-10-26 20:00:00',
        subtitle: '懒人行租车(携程优选)',
        title: '懒人行租车(携程优选)',
        vendorId: 30164,
      },
    },
  ];
  test.each(mockStateMap)('getPaymentParams check', ({ state, expected }) => {
    const result = getPaymentParams(state);
    // 包含除requestId之外的字段
    expect(result).toEqual(expect.objectContaining(expected));
  });
});

describe('ModifyOrderConfirm Selectors getInsConfirmReqParam', () => {
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            cancelExplain: '修改取还车信息后，请留意取消政策的更新。',
            baseResponse: {
              code: 'unknown',
              requestId: '',
              cost: 547,
              isSuccess: true,
              returnMsg: 'success',
            },
            resultType: 3,
            responseStatus: {
              ack: 'Success',
              errors: [],
              timestamp: '/Date(1661924223895+0800)/',
              extension: [
                {
                  id: 'CLOGGING_TRACE_ID',
                  value: '1369464516399727479',
                },
                {
                  id: 'RootMessageId',
                  value: '921822-0a715c50-461645-2557122',
                },
              ],
            },
            comPriceCode:
              'eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiUFJJQ0VfQ0hBTk5FTF8ifQ==',
            feeInfo: {
              totalAmount: 164,
              payAmount: 69,
              contents: [
                {
                  title: '在线补款后，修改即可生效。',
                  type: 0,
                  code: 'gray',
                },
                {
                  code: 'bold',
                  title: '在线补款',
                  type: 1,
                  typeDesc: '¥69',
                },
              ],
              feeInfoList: [
                {
                  fixed: true,
                  oldPrice: {
                    amount: 48,
                    priceDailys: [
                      {
                        date: '10月25日 周二',
                        priceStr: '¥48',
                        showType: 0,
                      },
                    ],
                    subTitle: '¥48×1天',
                    count: 1,
                    price: 48,
                    unit: '',
                  },
                  newPrice: {
                    amount: 96,
                    priceDailys: [
                      {
                        priceStr: '¥48',
                        showType: 0,
                        oDprice: '',
                        date: '10月24日 周一',
                      },
                      {
                        priceStr: '¥48',
                        showType: 0,
                        oDprice: '',
                        date: '10月25日 周二',
                      },
                    ],
                    subTitle: '¥48×2天',
                    count: 2,
                    price: 48,
                    unit: '',
                  },
                  title: '租车费',
                  serviceCode: '1001',
                  type: 0,
                  changeType: 1,
                },
                {
                  fixed: true,
                  oldPrice: {
                    amount: 30,
                    count: 1,
                    price: 30,
                    unit: '',
                  },
                  newPrice: {
                    amount: 60,
                    priceDailys: [],
                    count: 2,
                    price: 30,
                    unit: '',
                  },
                  title: '基础服务费',
                  serviceCode: '1002',
                  type: 0,
                  changeType: 1,
                },
                {
                  fixed: true,
                  oldPrice: {
                    amount: 20,
                    count: 1,
                    price: 20,
                    unit: '',
                  },
                  newPrice: {
                    amount: 20,
                    priceDailys: [],
                    count: 1,
                    price: 20,
                    unit: '',
                  },
                  title: '车行手续费',
                  serviceCode: '1003',
                  type: 0,
                  changeType: 0,
                },
                {
                  fixed: true,
                  oldPrice: {
                    amount: 3,
                  },
                  newPrice: {
                    subTitle: '不可享',
                  },
                  title: '活动',
                  serviceCode: 'act_104540',
                  type: 1,
                  subTitle: '黄金贵宾',
                  changeType: 1,
                },
                {
                  fixed: true,
                  oldPrice: {
                    subTitle: '无',
                  },
                  newPrice: {
                    amount: 12,
                  },
                  title: '活动',
                  serviceCode: 'act_121129',
                  type: 1,
                  subTitle: '周三福利日',
                  changeType: 2,
                },
                {
                  fixed: true,
                  oldPrice: {
                    amount: 95,
                  },
                  newPrice: {
                    amount: 164,
                  },
                  title: '订单总额',
                  fieldStyle: 'b',
                  serviceCode: 'total',
                  type: 4,
                  subTitle: '在线支付',
                  changeType: 1,
                },
              ],
            },
            discountInfo: {
              explain: '请留意可使用的优惠更新。',
              activityDetail: {
                title: '周三福利日',
                status: 1,
                promotion: {
                  promotionId: 121129,
                  code: 'PMS_SAT18620602129',
                  title: '周三福利日',
                  selected: true,
                  description: '减免12%',
                  isFromCtrip: false,
                  deductionAmount: 12,
                  payofftype: 2,
                },
              },
              couponList: {
                status: 0,
                title: '暂无可用优惠券',
              },
            },
            pageInfo: {
              type: 2,
              title: '请确认已修改的取还车信息',
              freeDeposit: true,
            },
            cancelRules: [
              {
                free: 1,
                title: '免费取消',
                context: '取车时间前',
                time: '2022-10-24 20:00前',
              },
              {
                free: 0,
                title: '扣订单全额',
                context: '取车时间后',
                time: '2022-10-24 20:00后',
              },
            ],
            activity: {
              explain: '',
              title: '活动',
              activities: [
                {
                  prefixText: 'modify.after.text',
                  suffixType: '1',
                  name: '活动',
                  suffixText: '3.00',
                  originText: '3.00',
                },
              ],
            },
            info: {
              pickUpLocation: {
                cityId: 43,
                bjTime: '/Date(1666612800000+0800)/',
                longitude: 109.414693,
                time: '/Date(1666612800000+0800)/',
                areaType: 1,
                latitude: 18.303395,
                name: '凤凰国际机场',
                cityName: '三亚',
              },
              driver: {
                mobile: '15800000000',
                certificateType: 1,
                name: '张三',
                certificateNumber: '310111199208080000',
              },
              returnLocation: {
                cityId: 43,
                bjTime: '/Date(1666785600000+0800)/',
                longitude: 109.414693,
                time: '/Date(1666785600000+0800)/',
                areaType: 1,
                latitude: 18.303395,
                name: '凤凰国际机场',
                cityName: '三亚',
              },
            },
            priceCode: 'e2555a15679044ae88914db92365edc0',
            appResponseMap: {
              isFromCache: false,
              isCacheValid: true,
              networkCost: 599,
              environmentCost: 0,
              cacheFetchCost: 0,
              fetchCost: 599,
              setCacheCost: 0,
              cacheFrom: '',
              beforeFetch: 1661924223310,
              afterFetch: 1661924223909,
            },
          },
        },
        ModifyOrderConfirm: {
          selectedInsuranceId: [],
        },
      },
      expected: {
        callbackType: 1,
        insuranceList: [],
        selectedInsuranceList: [],
      },
    },
  ];
  test.each(mockStateMap)(
    'getInsConfirmReqParam check',
    ({ state, expected }) => {
      const result = getInsConfirmReqParam(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getModifyDeposit', () => {
  const modifyDeposit = {};
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            modifyDeposit,
          },
        },
      },
      expected: modifyDeposit,
    },
  ];
  test.each(mockStateMap)('getModifyDeposit check', ({ state, expected }) => {
    const result = getModifyDeposit(state);
    expect(result).toEqual(expected);
  });
});

describe('ModifyOrderConfirm Selectors getDepositDescTable', () => {
  const modifyDeposit = {};
  const depositDescTable = {
    items: [],
    notices: null,
  };
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: depositDescTable,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: depositDescTable,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            modifyDeposit,
          },
        },
      },
      expected: depositDescTable,
    },
  ];
  test.each(mockStateMap)(
    'getDepositDescTable check',
    ({ state, expected }) => {
      const result = getDepositDescTable(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('ModifyOrderConfirm Selectors getDepositDesc', () => {
  const desc = 'x';
  const modifyDeposit = { desc };
  const mockStateMap = [
    {
      state: {
        ModifyOrder: {},
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        ModifyOrder: {
          modifyOrderResponse: {
            modifyDeposit,
          },
        },
      },
      expected: desc,
    },
  ];
  test.each(mockStateMap)('getDepositDesc check', ({ state, expected }) => {
    const result = getDepositDesc(state);
    expect(result).toEqual(expected);
  });
});
