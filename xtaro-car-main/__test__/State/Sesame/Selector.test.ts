import uuid from 'uuid';
import {
  getVisible,
  getIsUseCancel,
  getIsCtripCredit,
  getIsOrderDetail,
  getAuthStatus,
  getAuthFlowStep,
  getIsLogin,
  getAuthenStatusTicket,
  getIsSuccess,
  getIsdAuthenStatusTicket,
  getOSdAuthenStatusTicket,
  getSesameUserName,
  getSesameComplementaryAmount,
  getIsdSesame,
  getOsdSesame,
  getCancleZhimaModalData,
} from '../../../src/pages/xcar/State/Sesame/Selectors';
import {
  SesameAuthFlowStep,
} from '../../../src/pages/xcar/State/Sesame/Types';

const mockState : any = {
  Sesame: null,
};

describe('Sesame Selectors getVisible', () => {
  const mockStateMap = [
    {
      state: {
        Sesame: {
          visible: true,
        },
      },
      expected: true,
    },
    {
      state: mockState,
      expected: undefined,
    },
    {
      state: {
        Sesame: {
          visible: false,
        },
      },
      expected: false,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getVisible check',
    ({ state, expected }) => {
      const data = getVisible(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getIsUseCancel', () => {
  const mockStateMap = [
    {
      state: {
        Sesame: {
          isUseCancel: true,
        },
      },
      expected: true,
    },
    {
      state: mockState,
      expected: undefined,
    },
    {
      state: {
        Sesame: {
          isUseCancel: false,
        },
      },
      expected: false,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getIsUseCancel check',
    ({ state, expected }) => {
      const data = getIsUseCancel(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getIsCtripCredit', () => {
  const mockStateMap = [
    {
      state: {
        Sesame: {
          isCtripCredit: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Sesame: {
          isCtripCredit: false,
        },
      },
      expected: false,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getIsCtripCredit check',
    ({ state, expected }) => {
      const data = getIsCtripCredit(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getIsOrderDetail', () => {
  const mockStateMap = [
    {
      state: {
        Sesame: {
          isOrderDetail: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Sesame: {
          isOrderDetail: false,
        },
      },
      expected: false,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getIsOrderDetail check',
    ({ state, expected }) => {
      const data = getIsOrderDetail(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getAuthStatus', () => {
  const mockStateMap = [
    {
      state: {
        Sesame: {
          authStatus: 1,
        },
      },
      expected: 1,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getAuthStatus check',
    ({ state, expected }) => {
      const data = getAuthStatus(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getAuthFlowStep', () => {
  const mockStateMap = [
    {
      state: {
        Sesame: {
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      expected: SesameAuthFlowStep.promptInfo,
    },
    {
      state: mockState,
      expected: undefined,
    },
    {
      state: {
        Sesame: {
          authFlowStep: SesameAuthFlowStep.payFailure,
        },
      },
      expected: SesameAuthFlowStep.payFailure,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getAuthFlowStep check',
    ({ state, expected }) => {
      const data = getAuthFlowStep(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getIsLogin', () => {
  const mockStateMap = [
    {
      state: {
        Sesame: {
          isLogin: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Sesame: {
          isLogin: false,
        },
      },
      expected: false,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getIsLogin check',
    ({ state, expected }) => {
      const data = getIsLogin(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getAuthenStatusTicket', () => {
  const authenStatusTicket = uuid();
  const mockStateMap = [
    {
      state: {
        Sesame: {
          authenStatusTicket,
        },
      },
      expected: authenStatusTicket,
    },
    {
      state: mockState,
      expected: undefined,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getAuthenStatusTicket check',
    ({ state, expected }) => {
      const data = getAuthenStatusTicket(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getIsSuccess', () => {
  const mockStateMap = [
    {
      state: {
        Sesame: {
          isSuccess: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Sesame: {
          isSuccess: false,
        },
      },
      expected: false,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getIsSuccess check',
    ({ state, expected }) => {
      const data = getIsSuccess(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getIsdAuthenStatusTicket', () => {
  const authenStatusTicket = uuid();
  const mockStateMap = [
    {
      state: {
        Sesame: {
          isdSesame: {
            authenStatusTicket,
          }
        },
      },
      expected: authenStatusTicket,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getIsdAuthenStatusTicket check',
    ({ state, expected }) => {
      const data = getIsdAuthenStatusTicket(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getOSdAuthenStatusTicket', () => {
  const authenStatusTicket = uuid();
  const mockStateMap = [
    {
      state: {
        Sesame: {
          osdSesame: {
            authenStatusTicket,
          }
        },
      },
      expected: authenStatusTicket,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getOSdAuthenStatusTicket check',
    ({ state, expected }) => {
      const data = getOSdAuthenStatusTicket(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getSesameUserName', () => {
  const userName = "zhangsan";
  const mockStateMap = [
    {
      state: {
        Sesame: {
          userName,
        },
      },
      expected: userName,
    },
    {
      state: mockState,
      expected: undefined,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getSesameUserName check',
    ({ state, expected }) => {
      const data = getSesameUserName(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getSesameComplementaryAmount', () => {
  const complementaryAmount = 1234;
  const mockStateMap = [
    {
      state: {
        Sesame: {
          complementaryAmount,
        },
      },
      expected: complementaryAmount,
    },
    {
      state: mockState,
      expected: undefined,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getSesameComplementaryAmount check',
    ({ state, expected }) => {
      const data = getSesameComplementaryAmount(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getIsdSesame', () => {
  const isdSesame = {
    userName: 'zhangsan',
  };
  const mockStateMap = [
    {
      state: {
        Sesame: {
          isdSesame,
        },
      },
      expected: isdSesame,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getIsdSesame check',
    ({ state, expected }) => {
      const data = getIsdSesame(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getOsdSesame', () => {
  const osdSesame = {
    userName: 'zhangsan',
  };
  const mockStateMap = [
    {
      state: {
        Sesame: {
          osdSesame,
        },
      },
      expected: osdSesame,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getOsdSesame check',
    ({ state, expected }) => {
      const data = getOsdSesame(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Sesame Selectors getCancleZhimaModalData', () => {
  const cancelModalData = {
    "visible": false,
    "data": {}
  };
  const mockStateMap = [
    {
      state: {
        Sesame: {
          cancelModalData,
        },
      },
      expected: cancelModalData,
    },
    {
      state: {
        Sesame: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getCancleZhimaModalData check',
    ({ state, expected }) => {
      const data = getCancleZhimaModalData(state);
      expect(data).toEqual(expected);
    },
  );
});
