import { 
  initSesameAuthState,
  getSesameState,
  onCtripAuthentication,
  onAuthenticationWelcome,
  selectedCheckBox,
  onAuthConfirmPress,
  onAuthCancelPress,
  onAuthRealNameRegister,
  fetchCancelAuth,
} from '../../../src/pages/xcar/State/Sesame/Actions';
import {
  INIT_SESAME_STATE,
  GET_SESAME_STATE,
  ON_CTRIP_AUTHENTICATION,
  ON_AUTHENTICATION_WELCOME,
  SELECTED_CHECK_BOX,
  ON_AUTH_CONFIRM_PRESS,
  ON_AUTH_CANCEL_PRESS,
  ON_AUTH_REALNAME_REGISTER,
  FETCH_CANCEL_AUTH,
} from '../../../src/pages/xcar/State/Sesame/Types';

describe('Sesame Actions initSesameAuthState', () => {
  const props : any = {};
  const mockStateMap = [
    {
      props,
      expected: {
        type: INIT_SESAME_STATE,
        data: {
          isInitial: props.isInitial,
          appType: props.appType,
          isOrderDetail: props.isOrderDetail,
          isBooking: props.isBooking,
          newHomePage: props.newHomePage,
        },
      },
    },
    {
      props: null,
      expected: {
        type: INIT_SESAME_STATE,
        data: {
          isInitial: null,
          appType: null,
          isOrderDetail: null,
          isBooking: null,
          newHomePage: undefined,
        },
      },
    },
  ];
  test.each(mockStateMap)(
    'initSesameAuthState check',
    ({ props, expected }) => {
      const result = initSesameAuthState(props);
      expect(result).toEqual(expected);
    },
  );
});

describe('Sesame Actions getSesameState', () => {
  const mockStateMap = [
    {
      expected: {
        type: GET_SESAME_STATE,
      },
    },
  ];
  test.each(mockStateMap)(
    'getSesameState check',
    ({ expected }) => {
      const result = getSesameState();
      expect(result).toEqual(expected);
    },
  );
});

describe('Sesame Actions onCtripAuthentication', () => {
  const mockStateMap = [
    {
      expected: {
        type: ON_CTRIP_AUTHENTICATION,
      },
    },
  ];
  test.each(mockStateMap)(
    'onCtripAuthentication check',
    ({ expected }) => {
      const result = onCtripAuthentication();
      expect(result).toEqual(expected);
    },
  );
});

describe('Sesame Actions onAuthenticationWelcome', () => {
  const mockStateMap = [
    {
      expected: {
        type: ON_AUTHENTICATION_WELCOME,
      },
    },
  ];
  test.each(mockStateMap)(
    'onAuthenticationWelcome check',
    ({ expected }) => {
      const result = onAuthenticationWelcome();
      expect(result).toEqual(expected);
    },
  );
});

describe('Sesame Actions selectedCheckBox', () => {
  const data : any = {
    isSelected: true,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: SELECTED_CHECK_BOX,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'selectedCheckBox check',
    ({ expected }) => {
      const result = selectedCheckBox(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('Sesame Actions onAuthConfirmPress', () => {
  const data : any = {
    isVisible: true,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: ON_AUTH_CONFIRM_PRESS,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'onAuthConfirmPress check',
    ({ expected }) => {
      const result = onAuthConfirmPress(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('Sesame Actions onAuthCancelPress', () => {
  const data : any = {
    isVisible: true,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: ON_AUTH_CANCEL_PRESS,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'onAuthCancelPress check',
    ({ expected }) => {
      const result = onAuthCancelPress(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('Sesame Actions onAuthRealNameRegister', () => {
  const data : any = {
    authCode: 1234,
    showToast: true,
    test: "zhangsan",
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: ON_AUTH_REALNAME_REGISTER,
        data: {
          authCode: data.authCode,
          showToast: data.showToast,
        },
      },
    },
  ];
  test.each(mockStateMap)(
    'onAuthRealNameRegister check',
    ({ expected }) => {
      const result = onAuthRealNameRegister(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('Sesame Actions fetchCancelAuth', () => {
  const data : any = {
    isVisible: true,
  };
  const mockStateMap = [
    {
      data,
      expected: {
        type: FETCH_CANCEL_AUTH,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'fetchCancelAuth check',
    ({ expected }) => {
      const result = fetchCancelAuth(data);
      expect(result).toEqual(expected);
    },
  );
});