import { CHANGE_ENVIRONMENT } from '../../../src/pages/xcar/State/__Environment/Types';
import SesameReducer, 
{ 
  initialState,
} from '../../../src/pages/xcar/State/Sesame/Reducer';
import {
  SET_VISIBLE_DONE,
  SET_SESAME_STATE,
  SELECTED_CHECK_BOX,
  SET_AUTH_TICKET,
  TOGGLE_CANCEL_AUTHENTICATION_MODAL,
} from '../../../src/pages/xcar/State/Sesame/Types';

jest.mock('uuid', () => {
  return jest.fn().mockImplementation(() => {
    return "1234567890";
  });
});
describe('Sesame Reducer Test', () => {
  const initState = initialState();
  const action : any = {};
  test('Sesame Reducer Init', () => {
    expect(SesameReducer(undefined, action)).toEqual(initState);
  });

  describe('Sesame Reducer SET_VISIBLE_DONE', () => {
    const data = {
      isVisible: true,
      isUseCancel: true,
      isCtripCredit: true,
      isOrderDetail: true,
    };
    const mockActionMap = [
      {
        data,
        expected: {
          ...initState,
          visible: data.isVisible,
          isUseCancel: data.isUseCancel,
          isCtripCredit: data.isCtripCredit,
          isOrderDetail: data.isOrderDetail,
        },
      },
      {
        data: {
        },
        expected: {
          ...initState,
          visible: undefined,
          isUseCancel: undefined,
          isCtripCredit: false,
          isOrderDetail: undefined,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_VISIBLE_DONE check',
      ({ data, expected }) => {
        expect(SesameReducer(initState, {
          type: SET_VISIBLE_DONE,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('Sesame Reducer SET_SESAME_STATE', () => {
    const data = {
      authStatus: 1,
      isLogin: true,
      authFlowStep: 1,
      userName: 'zhangsan',
      identifyCard: true,
      authOrderCount: 2,
      isSuccess: true,
    };
    const mockActionMap = [
      {
        data,
        expected: {
          ...initState,
          authStatus: data.authStatus,
          isLogin: data.isLogin,
          authFlowStep: data.authFlowStep,
          userName: data.userName,
          identifyCard: data.identifyCard,
          authOrderCount: data.authOrderCount,
          isSuccess: data.isSuccess,
        },
      },
      {
        data: {
        },
        expected: {
          ...initState,
          authStatus: undefined,
          isLogin: undefined,
          authFlowStep: 0,
          userName: undefined,
          identifyCard: undefined,
          authOrderCount: undefined,
          isSuccess: undefined,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_SESAME_STATE check',
      ({ data, expected }) => {
        expect(SesameReducer(initState, {
          type: SET_SESAME_STATE,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('Sesame Reducer SELECTED_CHECK_BOX', () => {
    const data = {
      isSelected: true,
    };
    const mockActionMap = [
      {
        data,
        expected: {
          ...initState,
          selectedCheckbox: data.isSelected,
        },
      },
      {
        data: {
        },
        expected: {
          ...initState,
          selectedCheckbox: undefined,
        },
      },
    ];
    test.each(mockActionMap)(
      'SELECTED_CHECK_BOX check',
      ({ data, expected }) => {
        expect(SesameReducer(initState, {
          type: SELECTED_CHECK_BOX,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('Sesame Reducer SET_AUTH_TICKET', () => {
    const mockActionMap = [
      {
        expected: {
          ...initState,
          authenStatusTicket: '1234567890',
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_AUTH_TICKET check',
      ({ expected }) => {
        expect(SesameReducer(initState, {
          type: SET_AUTH_TICKET,
        })).toEqual(expected);
      },
    );
  });

  describe('Sesame Reducer CHANGE_ENVIRONMENT', () => {
    const Sesame = {
      userName: "lisi",
      authFlowStep: 1,
    };
    const mockActionMap = [
      {
        Sesame,
        expected: {
          ...initState,
          ...Sesame,
        },
      },
    ];
    test.each(mockActionMap)(
      'CHANGE_ENVIRONMENT check',
      ({ Sesame, expected }) => {
        const result = SesameReducer(initState, {
          type: CHANGE_ENVIRONMENT,
          Sesame,
        });
        expect(result).toEqual(expected);
      },
    );
  });

  describe('Sesame Reducer TOGGLE_CANCEL_AUTHENTICATION_MODAL', () => {
    const cancelModalData = {
      "visible": false,
      "data": {}
    };
    const mockActionMap = [
      {
        data: cancelModalData,
        expected: {
          ...initState,
          cancelModalData,
        },
      },
      {
        data: null,
        expected: {
          ...initState,
          cancelModalData: null,
        },
      },
    ];
    test.each(mockActionMap)(
      'TOGGLE_CANCEL_AUTHENTICATION_MODAL check',
      ({ data, expected }) => {
        const result = SesameReducer(initState, {
          type: TOGGLE_CANCEL_AUTHENTICATION_MODAL,
          data,
        });
        expect(result).toEqual(expected);
      },
    );
  });
})