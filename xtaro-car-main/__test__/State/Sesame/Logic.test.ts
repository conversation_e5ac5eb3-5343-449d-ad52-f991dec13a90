import { runSaga } from 'redux-saga';
import {
  fetchSesameAuthentication,
  traceLog,
  depoistFreeAuthResultTraceLog,
  initSesameAuthStateLogic,
  cancelAuthDone,
  onAuthenticationLogic,
  onCtripAuthenticationLogic,
  onAuthenticationWelcomeLogic,
  onAuthenticationOrderConfirmLogic,
  onAuthConfirmPress,
  onAuthCancelPress,
  onAuthRealNameRegister,
  fetchCancelAuthLogic,
  cancelAuthenticationLogic,
  cancelAuthenticationDoneLogic,
} from '../../../src/pages/xcar/State/Sesame/Logic';
import { takeEveryGeneratorFunction, testSagaGeneratorFunction } from '../../testHelpers';
import { CarFetch, CarLog, Utils, User, CarABTesting } from '../../../src/pages/xcar/Util/Index';
import * as SesameHelper from '../../../src/pages/xcar/Util/SesameHelper';
import {
  INIT_SESAME_STATE,
  SET_SESAME_STATE,
  SET_AUTH_TICKET,
  SesameAuthFlowStep,
  ON_AUTHENTICATION,
  SET_VISIBLE_DONE,
  SesameState,
  CANCEL_AUTHENTICATION,
  ON_AUTHENTICATION_ORDERCONFRIM,
  ON_AUTHENTICATION_WELCOME,
  ON_AUTH_CONFIRM_PRESS,
  ON_AUTH_CANCEL_PRESS,
  ON_AUTH_REALNAME_REGISTER,
  FETCH_CANCEL_AUTH,
  CANCEL_AUTHENTICATION_DONE,
  TOGGLE_CANCEL_AUTHENTICATION_MODAL,
} from '../../../src/pages/xcar/State/Sesame/Types';
import { cancelZhima } from '../../../src/pages/xcar/State/Sesame/Texts';
import {
  SETMODALSVISIBLE,
  UPDATA_FREE_DEPOSITINFO,
} from '../../../src/pages/xcar/State/OrderDetail/Types';
import {
  FETCH_DRIVERLIST,
} from '../../../src/pages/xcar/State/DriverList/Types';
import {
  ProductSelectors,
  SesameResponse,
} from '../../../src/pages/xcar/Global/Cache/Index';
import { LogKey } from '../../../src/pages/xcar/Constants/Index';


jest.mock('../../../src/pages/xcar/Util/SesameHelper', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Util/SesameHelper') }));

jest.mock('../../../src/pages/xcar/Util/User', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Util/User') }));

jest.mock('../../../src/pages/xcar/Util/ABTesting', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Util/ABTesting') }));

jest.mock('../../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Global/Cache/ProductSelectors') }));

jest.mock('../../../src/pages/xcar/State/StoreRef', () => ({
  getStore: () => ({
    getState: () => ({
      OrderDetail: {
        orderBaseInfo: {
          orderId: 1234567,
        },
      }
    })
  })
}));
describe('Sesame Logic fetchSesameAuthentication', () => {
  test('测试正常调用', async () => {
    const params1 = {
      cityId: 43,
      authCode: 1,
      userName: '张三',
      sesameAmount: 100,
      sesameVendorId: '1000',
      appType: "ISD_C_APP",
      newHomePage: true,
      isBooking: true,
      isOrderDetail: true,
      sesameAuthenticationInit: true,
    };
    const params2 = {
      cityId: 43,
      authCode: 1,
      userName: '张三',
      sesameAmount: 0,
      sesameVendorId: '',
      appType: "ISD_C_APP",
      newHomePage: true,
      isBooking: false,
      isOrderDetail: false,
      sesameAuthenticationInit: true,
    };
    const res = {
      "alipayAuthInfo": {
        "authCount": 0,
        "authInfos": [
          {
            "button": {
              "title": "立即验证"
            },
            "contents": [
              {
                "contentStyle": "1",
                "stringObjs": [
                  {
                    "content": "芝麻分满650",
                    "style": "1"
                  },
                  {
                    "content": "有机会享免押金租车",
                    "style": "2"
                  }
                ]
              }
            ],
            "title": "",
            "type": 1
          }
        ],
        "authOrderCount": 0,
        "authStatus": 0,
        "authUrl": "",
        "authedCountEqOne": false,
        "defaultInfo": {
          "button": {
            "title": "知道了"
          },
          "contents": [
            {
              "stringObjs": [
                {
                  "content": "可能原因：",
                  "style": "3"
                }
              ]
            },
            {
              "stringObjs": [
                {
                  "content": "1.芝麻信用未达650分",
                  "style": "3"
                }
              ]
            },
            {
              "stringObjs": [
                {
                  "content": "2.未通过支付宝安全认证",
                  "style": "3"
                }
              ]
            },
            {
              "stringObjs": [
                {
                  "content": "详情请咨询支付宝客服",
                  "style": "7"
                }
              ]
            }
          ],
          "title": "抱歉，验证未通过"
        },
        "idNo": "",
        "orderId": "12988053426",
        "promptInfo": {
          "button": {
            "title": "立即认证"
          },
          "contents": [
            {
              "stringObjs": [
                {
                  "content": "芝麻信用满650分",
                  "style": "1"
                }
              ]
            },
            {
              "stringObjs": [
                {
                  "content": "享免押金租车",
                  "style": "2"
                }
              ]
            }
          ],
          "title": ""
        },
        "requestId": "alipay200806025654991uuh6",
        "userName": ""
      },
      "baseResponse": {
        "isSuccess": false
      },
      "responseStatus": {
        "ack": "Success",
        "errors": [],
        "extension": [
          {
            "id": "CLOGGING_TRACE_ID",
            "value": "8484612565890017861"
          },
          {
            "id": "RootMessageId",
            "value": "921822-0a3d94aa-443522-6597566"
          }
        ],
        "timestamp": "2020-08-06 10:56:54"
      }
    };
    const expected = {
      request: { "authCode": "", "cityId": { "cityId": 43, "authCode": 1, "userName": "张三", "sesameAmount": 100, "sesameVendorId": "1000", "appType": "ISD_C_APP", "newHomePage": true, "isBooking": true, "isOrderDetail": true, "sesameAuthenticationInit": true }, "userName": "", "vendorId": "13039", "amount": 5000, "newHomePage": true, "parentRequestId": "", "orderId": "", "extraMaps": {} },
      response: res,
    };
    const expected2 = {
      request: { "authCode": "", "cityId": { "cityId": 43, "authCode": 1, "userName": "张三", "sesameAmount": 0, "sesameVendorId": "", "appType": "ISD_C_APP", "newHomePage": true, "isBooking": false, "isOrderDetail": false, "sesameAuthenticationInit": true }, "userName": "", "vendorId": "13039", "amount": 5000, "newHomePage": true, "parentRequestId": "", "orderId": "", "extraMaps": {} },
      response: res,
    };
    jest.spyOn(ProductSelectors, "getCreditRentDepositInfo").mockImplementation(() => ({ "carRentalDepositFee": 5000 }));
    jest.spyOn(ProductSelectors, "getVendorInfo").mockImplementation(() => ({ "bizVendorCode": "13039" }));
    jest.spyOn(ProductSelectors, "getProductRequestId").mockImplementation(() => ({ "requestId": "1f56883c-ab20-4b6e-9803-ae4a7bcbec4f" }));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((request) => (Promise.resolve({ request, response: res })));
    const fetchRes: any = await fetchSesameAuthentication(params1);
    expect(fetchRes).toEqual(expected);
    const fetchRes2: any = await fetchSesameAuthentication(params2);
    expect(fetchRes2).toEqual(expected2);
  });

  test('测试异常调用', async () => {
    const params1 = {
      cityId: 43,
      authCode: 1,
      userName: '张三',
      sesameAmount: 100,
      sesameVendorId: '1000',
      appType: "ISD_C_APP",
      newHomePage: true,
      isBooking: true,
      isOrderDetail: true,
      sesameAuthenticationInit: true,
    };
    const params2 = {
      cityId: 43,
      authCode: 1,
      userName: '张三',
      sesameAmount: 0,
      sesameVendorId: '',
      appType: "ISD_C_APP",
      newHomePage: true,
      isBooking: false,
      isOrderDetail: false,
      sesameAuthenticationInit: true,
    };
    const res = {
      "alipayAuthInfo": {
        "authCount": 0,
        "authInfos": [
          {
            "button": {
              "title": "立即验证"
            },
            "contents": [
              {
                "contentStyle": "1",
                "stringObjs": [
                  {
                    "content": "芝麻分满650",
                    "style": "1"
                  },
                  {
                    "content": "有机会享免押金租车",
                    "style": "2"
                  }
                ]
              }
            ],
            "title": "",
            "type": 1
          }
        ],
        "authOrderCount": 0,
        "authStatus": 0,
        "authUrl": "",
        "authedCountEqOne": false,
        "defaultInfo": {
          "button": {
            "title": "知道了"
          },
          "contents": [
            {
              "stringObjs": [
                {
                  "content": "可能原因：",
                  "style": "3"
                }
              ]
            },
            {
              "stringObjs": [
                {
                  "content": "1.芝麻信用未达650分",
                  "style": "3"
                }
              ]
            },
            {
              "stringObjs": [
                {
                  "content": "2.未通过支付宝安全认证",
                  "style": "3"
                }
              ]
            },
            {
              "stringObjs": [
                {
                  "content": "详情请咨询支付宝客服",
                  "style": "7"
                }
              ]
            }
          ],
          "title": "抱歉，验证未通过"
        },
        "idNo": "",
        "orderId": "12988053426",
        "promptInfo": {
          "button": {
            "title": "立即认证"
          },
          "contents": [
            {
              "stringObjs": [
                {
                  "content": "芝麻信用满650分",
                  "style": "1"
                }
              ]
            },
            {
              "stringObjs": [
                {
                  "content": "享免押金租车",
                  "style": "2"
                }
              ]
            }
          ],
          "title": ""
        },
        "requestId": "alipay200806025654991uuh6",
        "userName": ""
      },
      "baseResponse": {
        "isSuccess": false
      },
      "responseStatus": {
        "ack": "Success",
        "errors": [],
        "extension": [
          {
            "id": "CLOGGING_TRACE_ID",
            "value": "8484612565890017861"
          },
          {
            "id": "RootMessageId",
            "value": "921822-0a3d94aa-443522-6597566"
          }
        ],
        "timestamp": "2020-08-06 10:56:54"
      }
    };
    const expected = {
      request: { "authCode": "", "cityId": { "cityId": 43, "authCode": 1, "userName": "张三", "sesameAmount": 100, "sesameVendorId": "1000", "appType": "ISD_C_APP", "newHomePage": true, "isBooking": true, "isOrderDetail": true, "sesameAuthenticationInit": true }, "userName": "", "vendorId": "13039", "amount": 5000, "newHomePage": true, "parentRequestId": "", "orderId": "", "extraMaps": {} },
      response: res,
    };
    const expected2 = {
      request: { "authCode": "", "cityId": { "cityId": 43, "authCode": 1, "userName": "张三", "sesameAmount": 0, "sesameVendorId": "", "appType": "ISD_C_APP", "newHomePage": true, "isBooking": false, "isOrderDetail": false, "sesameAuthenticationInit": true }, "userName": "", "vendorId": "13039", "amount": 5000, "newHomePage": true, "parentRequestId": "", "orderId": "", "extraMaps": {} },
      response: res,
    };
    jest.spyOn(ProductSelectors, "getCreditRentDepositInfo").mockImplementation(() => ({ "carRentalDepositFee": 5000 }));
    jest.spyOn(ProductSelectors, "getVendorInfo").mockImplementation(() => ({ "bizVendorCode": "13039" }));
    jest.spyOn(ProductSelectors, "getProductRequestId").mockImplementation(() => ({ "requestId": "1f56883c-ab20-4b6e-9803-ae4a7bcbec4f" }));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((request) => (Promise.resolve({ request, response: res })));
    const fetchRes: any = await fetchSesameAuthentication(params1);
    expect(fetchRes).toEqual(expected);
    const fetchRes2: any = await fetchSesameAuthentication(params2);
    expect(fetchRes2).toEqual(expected2);
  });
});

describe('Sesame Logic traceLog', () => {
  const data1 = {
    type: "type1",
    authRes: {},
    authFlowStep: "authFlowStep1",
  }
  const data2 = {
    type: "",
    authRes: {},
    authFlowStep: "authFlowStep1",
  }
  const mockStateMap = [
    {
      data: data1,
      expected: {
        key: LogKey.c_car_trace_sesame_credit,
        info: data1
      },
    },
    {
      data: data2,
      expected: {
        key: LogKey.c_car_trace_sesame_credit,
        info: {
          type: 'Sesame/ON_AUTHENTICATION',
          authRes: data2.authRes,
          authFlowStep: data2.authFlowStep,
        }
      },
    }
  ];
  let logResult = {};
  test.each(mockStateMap)(
    'traceLog check',
    ({ data, expected }) => {
      jest.spyOn(CarLog, "LogTrace").mockImplementation((data) => {
        logResult = data;
      });
      traceLog(data);
      expect(logResult).toEqual(expected);
    },
  );
});

describe('Sesame Logic depoistFreeAuthResultTraceLog', () => {
  const data = {
    name: "123456",
  }
  const mockStateMap = [
    {
      data,
      expected: {
        key: LogKey.vac_car_trace_orderdetail_depoistfree_result,
        info: data
      },
    },
  ];
  let logResult = {};
  test.each(mockStateMap)(
    'depoistFreeAuthResultTraceLog check',
    ({ data, expected }) => {
      jest.spyOn(CarLog, "LogTrace").mockImplementation((data) => {
        logResult = data;
      });
      depoistFreeAuthResultTraceLog(data);
      expect(logResult).toEqual(expected);
    },
  );
});

jest.mock('../../../src/pages/xcar/State/SelectorIndex', () => ({
  DriverListSelector: {
    getUnMaskPassenger: jest.fn((state) => state.DriverList.unMaskPassenger),
  }
}));
jest.mock('../../../src/pages/xcar/State/OrderDetail/Selectors', () => ({
  getFreeDeposit: jest.fn((state) => state.OrderDetail.freeDeposit),
  getOrderDetailVendorInfo: jest.fn(state => state.OrderDetail.vendorInfo),
  getReqOrderParams: jest.fn(state => ({ orderId: '11111' })),
  getCustomerInfo: jest.fn((state) => state.OrderDetail.driverInfo),
  getVendorInfo: jest.fn(state => state.OrderDetail.vendorInfo),
}));
describe('Sesame Logic initSesameAuthStateLogic', () => {
  const res = {
    "alipayAuthInfo": {
      "authCount": 0,
      "authInfos": [
        {
          "button": {
            "title": "立即验证"
          },
          "contents": [
            {
              "contentStyle": "1",
              "stringObjs": [
                {
                  "content": "芝麻分满650",
                  "style": "1"
                },
                {
                  "content": "有机会享免押金租车",
                  "style": "2"
                }
              ]
            }
          ],
          "title": "",
          "type": 1
        }
      ],
      "authOrderCount": 0,
      "authStatus": 0,
      "authUrl": "",
      "authedCountEqOne": false,
      "defaultInfo": {
        "button": {
          "title": "知道了"
        },
        "contents": [
          {
            "stringObjs": [
              {
                "content": "可能原因：",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "1.芝麻信用未达650分",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "2.未通过支付宝安全认证",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "详情请咨询支付宝客服",
                "style": "7"
              }
            ]
          }
        ],
        "title": "抱歉，验证未通过"
      },
      "idNo": "",
      "orderId": "12988053426",
      "promptInfo": {
        "button": {
          "title": "立即认证"
        },
        "contents": [
          {
            "stringObjs": [
              {
                "content": "芝麻信用满650分",
                "style": "1"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "享免押金租车",
                "style": "2"
              }
            ]
          }
        ],
        "title": ""
      },
      "requestId": "alipay200806025654991uuh6",
      "userName": ""
    },
    "baseResponse": {
      "isSuccess": false
    },
  };

  test('测试正常调用', async () => {
    const dispatched: any = [];
    const fetchParams: any = {
      data: {
        isInitial: false,
      },
    };
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(res)));
    jest.spyOn(SesameResponse, 'setData').mockImplementation(jest.fn);
    jest.spyOn(User, 'isLogin').mockImplementation(() => (Promise.resolve(true)));
    const logicFunc = takeEveryGeneratorFunction(initSesameAuthStateLogic);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          LocationAndDate: {
            rentalLocation: {
              pickUp: {
                cid: 43,
              },
            },
          },
          DriverList: {
            unMaskPassenger: {
              fullName: "张三"
            }
          },
          OrderDetail: {
            freeDeposit: {
              preAmountForCar: 300,
            },
          },
        }),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        isLogin: true,
        authStatus: res.alipayAuthInfo.authStatus,
        authFlowStep: SesameAuthFlowStep.promptInfo,
        userName: res.alipayAuthInfo.userName,
        identifyCard: res.alipayAuthInfo.idNo,
        authOrderCount: res.alipayAuthInfo.authOrderCount,
        isSuccess: res.baseResponse.isSuccess,
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试数据异常调用', async () => {
    const dispatched: any = [];
    const fetchParams: any = {
      data: {
        isInitial: false,
      },
    };
    const res = {};
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(res)));
    jest.spyOn(SesameResponse, 'setData').mockImplementation(jest.fn);
    jest.spyOn(User, 'isLogin').mockImplementation(() => (Promise.resolve(true)));
    const logicFunc = takeEveryGeneratorFunction(initSesameAuthStateLogic);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          LocationAndDate: {
            rentalLocation: {
              pickUp: {
                cid: 43,
              },
            },
          },
          DriverList: {
            unMaskPassenger: {
              fullName: "张三"
            }
          },
          OrderDetail: {
            freeDeposit: {
              preAmountForCar: 300,
            },
          },
        }),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });

  test('接口异常调用', async () => {
    const dispatched: any = [];
    const fetchParams: any = {
      data: {
        isInitial: false,
      },
    };
    const exceptionError = new Error('queryMembershipRights exception');
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockReturnValue(new Promise(() => {
      throw exceptionError;
    }));
    jest.spyOn(SesameResponse, 'setData').mockImplementation(jest.fn);
    jest.spyOn(User, 'isLogin').mockImplementation(() => (Promise.resolve(true)));
    const logicFunc = takeEveryGeneratorFunction(initSesameAuthStateLogic);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          LocationAndDate: {
            rentalLocation: {
              pickUp: {
                cid: 43,
              },
            },
          },
          DriverList: {
            unMaskPassenger: {
              fullName: "张三"
            }
          },
          OrderDetail: {
            freeDeposit: {
              preAmountForCar: 300,
            },
          },
        }),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });
});

describe('Sesame Logic cancelAuthDone', () => {
  test('测试正常调用', async () => {
    const data = {
      isSuccess: true,
      newHomePage: {},
    };
    const excepts = [
      {
        action: {
          type: ON_AUTHENTICATION,
          data
        },
      },
      {
        action: {
          type: INIT_SESAME_STATE,
          data: {
            isInitial: undefined,
            appType: undefined,
            isOrderDetail: undefined,
            isBooking: undefined,
            newHomePage: data.newHomePage,
          },
        },
      },
      {
        action: {
          type: SET_AUTH_TICKET,
        },
      }
    ];
    testSagaGeneratorFunction(cancelAuthDone, data, excepts);
  });

  test('测试参数异常调用', async () => {
    const data: any = {};
    const excepts = [
      {
        action: {
          type: SET_AUTH_TICKET,
        },
      }
    ];
    testSagaGeneratorFunction(cancelAuthDone, data, excepts);
  });
});
jest.mock('../../../src/pages/xcar/Util/Payment/Index', () => ({
  MiddlePay: jest.fn(() => ({
    status: 2
  })),
}));
jest.mock('../../../src/pages/xcar/State/OrderDetail/Mappers', () => ({
  getRenewTipLogData: jest.fn(),
}));
describe('Sesame Logic onAuthenticationLogic', () => {
  const res = {
    "alipayAuthInfo": {
      "authCount": 0,
      "authInfos": [
        {
          "button": {
            "title": "立即验证"
          },
          "contents": [
            {
              "contentStyle": "1",
              "stringObjs": [
                {
                  "content": "芝麻分满650",
                  "style": "1"
                },
                {
                  "content": "有机会享免押金租车",
                  "style": "2"
                }
              ]
            }
          ],
          "title": "",
          "type": 1
        }
      ],
      "authOrderCount": 0,
      "authStatus": 0,
      "authUrl": "",
      "authedCountEqOne": false,
      "defaultInfo": {
        "button": {
          "title": "知道了"
        },
        "contents": [
          {
            "stringObjs": [
              {
                "content": "可能原因：",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "1.芝麻信用未达650分",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "2.未通过支付宝安全认证",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "详情请咨询支付宝客服",
                "style": "7"
              }
            ]
          }
        ],
        "title": "抱歉，验证未通过"
      },
      "idNo": "",
      "orderId": "12988053426",
      "promptInfo": {
        "button": {
          "title": "立即认证"
        },
        "contents": [
          {
            "stringObjs": [
              {
                "content": "芝麻信用满650分",
                "style": "1"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "享免押金租车",
                "style": "2"
              }
            ]
          }
        ],
        "title": ""
      },
      "requestId": "alipay200806025654991uuh6",
      "userName": ""
    },
    "baseResponse": {
      "isSuccess": false
    },
  };
  const initialParams: any = {
    isShowSuccessModal: true,
    isShowSuccessToast: true,
    isShowFailureModal: true,
    isShowFundSuccessModal: true,
    isCanCancel: true,
    isOrderDetail: true,
    newHomePage: true,
    isBooking: true,
    sesameAuthenticationInit: true,
    successCb: () => { },
  };
  const initialState = {
    LocationAndDate: {
      rentalLocation: {
        pickUp: {
          cid: 43,
        },
      },
    },
    DriverList: {
      unMaskPassenger: {
        fullName: "张三"
      }
    },
    OrderDetail: {
      freeDeposit: {
        preAmountForCar: 300,
      },
    },
  };

  test('测试正常调用第二次请求授权认证成功状态且需要展示弹窗', async () => {
    const dispatched: any = [];
    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.authorized,
      },
    };
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: { isVisible: true },
    });
    expect(dispatched[2]).toEqual({
      type: FETCH_DRIVERLIST,
      data: {},
    });
  });

  test('测试正常调用第二次请求授权认证成功状态且需要展示弹窗', async () => {
    const dispatched: any = [];

    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.authorized,
      },
    };
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: { isVisible: true },
    });
  });

  test('测试正常调用第二次请求授权认证成功状态且不需要展示弹窗但需要展示资金补足授权成功弹层', async () => {
    const dispatched: any = [];

    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.authorized,
        complementaryAmount: 1000,
      },
    };
    const fetchParams: any = {
      ...initialParams,
      isShowSuccessModal: false,
    };
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: { isVisible: true },
    });
  });

  test('测试正常调用第二次请求授权认证成功状态且不需要展示弹窗和资金补足授权成功弹层，但需展示驾驶员不同Toast且是订单详情页', async () => {
    const dispatched: any = [];

    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.authorized,
        sameDriver: false,
      },
    };
    const fetchParams: any = {
      ...initialParams,
      isShowSuccessModal: false,
    };

    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: { isVisible: true, isOrderDetail: true },
    });
  });

  test('测试正常调用第二次请求授权认证成功状态且不需要展示弹窗和资金补足授权成功弹层，但需展示驾驶员不同Toast且不是订单详情页', async () => {
    const dispatched: any = [];

    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.authorized,
        sameDriver: false,
      },
    };
    const fetchParams: any = {
      ...initialParams,
      isOrderDetail: false,
      isShowSuccessModal: false,
    };

    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: { isVisible: true },
    });
  });

  test('测试正常调用第二次请求授权认证成功状态且不需要展示弹窗和资金补足授权成功弹层，但需展示驾驶员相同Toast且是订单详情页', async () => {
    const dispatched: any = [];
    const freeDepositWay = {};
    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.authorized,
        sameDriver: true,
      },
    };
    const fetchParams: any = {
      ...initialParams,
      isOrderDetail: true,
      freeDepositWay,
      isShowSuccessModal: false,
    };

    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: ON_AUTHENTICATION_ORDERCONFRIM,
      data: { isVisible: undefined, driverUserName: undefined, freeDepositWay },
    });
    expect(dispatched[2]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试正常调用第二次请求授权认证成功状态且不需要展示弹窗和资金补足授权成功弹层，但需展示驾驶员相同Toast且不是订单详情页', async () => {
    const dispatched: any = [];
    const freeDepositWay = {};
    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.authorized,
        sameDriver: true,
      },
    };
    const fetchParams: any = {
      ...initialParams,
      freeDepositWay,
      isShowSuccessModal: false,
      isOrderDetail: false,
    };

    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试正常调用第二次请求授权认证成功状态且不需要展示弹窗和资金补足授权成功弹层且不需展示驾驶员相同Toast且是订单详情页', async () => {
    const dispatched: any = [];
    const freeDepositWay = {};
    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.authorized,
        sameDriver: true,
      },
    };
    const fetchParams: any = {
      ...initialParams,
      isShowSuccessModal: false,
      isShowSuccessToast: false,
      freeDepositWay,
    };

    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SETMODALSVISIBLE,
      data: {
        depositPaymentModal: {
          visible: false,
        }
      }
    });
    expect(dispatched[2]).toEqual({
      type: ON_AUTHENTICATION_ORDERCONFRIM,
      data: {
        isVisible: undefined,
        driverUserName: undefined,
        freeDepositWay
      },
    });
    expect(dispatched[3]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试正常调用第二次请求授权认证成功状态且不需要展示弹窗和资金补足授权成功弹层且不需展示驾驶员相同Toast且不是订单详情页', async () => {
    const dispatched: any = [];
    const freeDepositWay = {};
    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.authorized,
        sameDriver: true,
      },
    };
    const fetchParams: any = {
      ...initialParams,
      isShowSuccessModal: false,
      isShowSuccessToast: false,
      isOrderDetail: false,
      freeDepositWay,
    };

    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试正常调用第二次请求授权认证非成功状态且需要展示失败弹窗', async () => {
    const dispatched: any = [];
    const freeDepositWay = {};
    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.failAuthorized,
        sameDriver: true,
      },
    };
    const fetchParams: any = {
      ...initialParams,
      isShowSuccessModal: false,
      isShowSuccessToast: false,
      isOrderDetail: false,
      freeDepositWay,
    };

    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: { isVisible: true }
    });
  });

  test('测试正常调用第二次请求授权未认证状态', async () => {
    const dispatched: any = [];

    const secondRes: any = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.unAuthorized,
      },
    };
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: secondRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.payFailure,
        userName: secondRes.alipayAuthInfo.userName,
        identifyCard: secondRes.alipayAuthInfo.idNo,
        authOrderCount: secondRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
  });

  test('测试正常调用第二次请求授权状态无结果', async () => {
    const dispatched: any = [];
    const secondRes: any = {};
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: { ...initialParams, isShowFailureModal: true },
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => {
      return Promise.resolve(params.extraMaps.init ? res : secondRes);
    });
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: SesameState.failAuthorized,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.netWorkFailure,
        userName: undefined,
        identifyCard: undefined,
        authOrderCount: undefined,
        appType: undefined,
        isSuccess: undefined
      },
    });
  });

  test('测试正常调用未安装支付宝', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(false)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched).toEqual([]);
  });

  test('测试正常调用接口返回验证不通过且需资金补足(一嗨)&取消授权再发起申请接口成功', async () => {
    const dispatched: any = [];
    const mockRes = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.depositLack,
      },
    };
    const cancelRes = {
      baseResponse: {
        isSuccess: true,
      },
      newHomePage: {},
    };
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(mockRes)));
    jest.spyOn(CarFetch, 'cancelSesameAuthentication').mockImplementation(() => (Promise.resolve(cancelRes)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: mockRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.promptInfo,
        userName: mockRes.alipayAuthInfo.userName,
        identifyCard: mockRes.alipayAuthInfo.idNo,
        authOrderCount: mockRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: ON_AUTHENTICATION,
      data: {
        isSuccess: cancelRes.baseResponse.isSuccess,
        ...initialParams,
        sesameAuthenticationInit: 1,
      },
    });
    expect(dispatched[2]).toEqual({
      type: INIT_SESAME_STATE,
      data: {
        isInitial: undefined,
        appType: undefined,
        isOrderDetail: undefined,
        isBooking: undefined,
        newHomePage: initialParams.newHomePage,
      },
    });
    expect(dispatched[3]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试正常调用接口返回验证不通过且需资金补足(一嗨)&取消授权再发起申请接口无结果', async () => {
    const dispatched: any = [];
    const mockRes = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.depositLack,
      },
    };
    const cancelRes = {};
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(mockRes)));
    jest.spyOn(CarFetch, 'cancelSesameAuthentication').mockImplementation(() => (Promise.resolve(cancelRes)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: mockRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.promptInfo,
        userName: mockRes.alipayAuthInfo.userName,
        identifyCard: mockRes.alipayAuthInfo.idNo,
        authOrderCount: mockRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试正常调用接口返回验证不通过且需资金补足(一嗨)&取消授权再发起申请接口异常', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    const mockRes = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.depositLack,
      },
    };
    const exceptionError = new Error('cancelSesameAuthentication exception');
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(mockRes)));
    jest.spyOn(CarFetch, 'cancelSesameAuthentication').mockReturnValue(new Promise(() => {
      throw exceptionError;
    }));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: mockRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.promptInfo,
        userName: mockRes.alipayAuthInfo.userName,
        identifyCard: mockRes.alipayAuthInfo.idNo,
        authOrderCount: mockRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试正常调用接口返回验证不通过但诚信用满足', async () => {
    const dispatched: any = [];
    const mockRes = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.failAuthorizedSuccessTrip,
      },
    };
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(mockRes)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: mockRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: mockRes.alipayAuthInfo.authStatus === SesameState.under18 ? SesameAuthFlowStep.promptInfo : SesameAuthFlowStep.authFailure,
        userName: mockRes.alipayAuthInfo.userName,
        identifyCard: mockRes.alipayAuthInfo.idNo,
        authOrderCount: mockRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: { isVisible: initialParams.isShowFailureModal },
    });
  });

  test('测试正常调用接口返回验证不通过但诚信用满足或者实名信息不满足18周岁', async () => {
    const dispatched: any = [];
    const mockRes = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.under18,
      },
    };
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: { ...initialParams, isBooking: false },
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(mockRes)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: mockRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: mockRes.alipayAuthInfo.authStatus === SesameState.under18 ? SesameAuthFlowStep.promptInfo : SesameAuthFlowStep.authFailure,
        userName: mockRes.alipayAuthInfo.userName,
        identifyCard: mockRes.alipayAuthInfo.idNo,
        authOrderCount: mockRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: { isVisible: initialParams.isShowFailureModal },
    });
  });

  test('测试正常调用接口返回未实名', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    const mockRes = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.unRealName,
      },
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(mockRes)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });

  test('测试正常调用接口返回未实名有实名认证url', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    const mockRes = {
      ...res,
      authUrl: "www.baidu.com",
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.unRealName,
      },
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(mockRes)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });

  test('测试正常调用接口返回已授权或者超时', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    const mockRes = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.overmuchLimitedNums,
      },
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(mockRes)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: mockRes.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.promptInfo,
        userName: mockRes.alipayAuthInfo.userName,
        identifyCard: mockRes.alipayAuthInfo.idNo,
        authOrderCount: mockRes.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
    expect(dispatched[2]).toEqual({
      type: CANCEL_AUTHENTICATION,
      data: initialParams,
    });
  });

  test('测试已登入但接口调用失败或无结果', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched).toEqual([]);
  });

  test('测试未登入调用', async () => {
    const dispatched: any = [];
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: initialParams,
    };
    jest.spyOn(User, 'isLogin').mockImplementation(() => (Promise.resolve(false)));
    jest.spyOn(User, 'toLogin').mockImplementation(() => (Promise.resolve(false)));
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(res)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: res.alipayAuthInfo.authStatus,
        isLogin: false,
        authFlowStep: SesameAuthFlowStep.unLogin,
        userName: undefined,
        identifyCard: undefined,
        authOrderCount: undefined,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: true
      },
    });
  });

  test('测试debug调用', async () => {
    const dispatched: any = [];

    const fetchParams: any = {
      ...initialParams,
      mockData: res,
    };

    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationLogic);

    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          OrderDetail: {
            driverInfo: {
              name: '测试',
              email: '',
              telphone:
                'd492e309ad4397fe9768cf2eea0e92905f1c2faca90ce24ac59052e5dbb7214d',
              areaCode: '+86',
              flightNo: '',
              iDCardType: 1,
              iDCardNo: '2103********24',
              encrypIDCardNo: '210381Ns6SnIxGrH24#',
              distributionMobile: '1561TV71116',
              distributionEmail: '',
              decryptIDCardNo: '210381199405065924',
              decryptTelphone: '15618081116',
            },
          },
          LocationAndDate: {
            rentalDate: {
              pickUp: {
                dateTime: '20200612100000',
              },
              dropOff: {
                dateTime: '20200614100000',
              },
            },
            rentalLocation: {
              pickUp: {
                country: '中国',
                area: {
                  lng: 110.343315,
                  lat: 19.984078,
                  id: '',
                  name: '海口东站',
                  type: '2',
                },

                cname: '海口',
                cid: '42',
              },
              dropOff: {
                country: '中国',
                area: {
                  lng: 110.343315,
                  lat: 19.984078,
                  id: '',
                  name: '海口东站',
                  type: '2',
                },

                cname: '海口',
                cid: '42',
              },
              isShowDropOff: false,
            },
          },
        }),
      },
      logicFunc,
      actionMock,
    ).toPromise();

    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: res.alipayAuthInfo.authStatus,
        isLogin: false,
        authFlowStep: -2,
        userName: undefined,
        identifyCard: undefined,
        authOrderCount: undefined,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: true
      },
    });
  });

  test('测试数据fetchSesameAuthentication接口异常调用', async () => {
    const dispatched: any = [];
    const fetchParams: any = {
      data: {
        isInitial: false,
      },
    };
    const res = {};
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(res)));
    jest.spyOn(SesameResponse, 'setData').mockImplementation(jest.fn);
    jest.spyOn(User, 'isLogin').mockImplementation(() => (Promise.resolve(true)));
    const logicFunc = takeEveryGeneratorFunction(initSesameAuthStateLogic);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });

  test('fetchSesameAuthentication接口异常调用', async () => {
    const dispatched: any = [];
    const fetchParams: any = {
      data: {
        isInitial: false,
      },
    };
    const exceptionError = new Error('queryMembershipRights exception');
    const actionMock = {
      type: INIT_SESAME_STATE,
      data: fetchParams,
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockReturnValue(new Promise(() => {
      throw exceptionError;
    }));
    jest.spyOn(SesameResponse, 'setData').mockImplementation(jest.fn);
    jest.spyOn(User, 'isLogin').mockImplementation(() => (Promise.resolve(true)));
    const logicFunc = takeEveryGeneratorFunction(initSesameAuthStateLogic);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => (initialState),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });
});

describe('Sesame Logic onCtripAuthenticationLogic', () => {
  test('测试正常调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onCtripAuthenticationLogic);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: true,
        isCtripCredit: true,
      },
    });
  });
});

describe('Sesame Logic onAuthenticationWelcomeLogic', () => {
  const initialState = {
    LocationAndDate: {
      rentalLocation: {
        pickUp: {
          cid: 43,
        },
      },
    },
    DriverList: {
      unMaskPassenger: {
        fullName: "张三"
      }
    },
  };
  test('测试正常调用', async () => {
    const dispatched: any = [];
    const res = {
      alipayAuthInfo: {
        authStatus: SesameState.authorized,
      },
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(res)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationWelcomeLogic);
    const actionMock = {
      type: ON_AUTHENTICATION_WELCOME,
    };
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        userName: undefined,
        identifyCard: undefined,
        authOrderCount: undefined,
        appType: undefined,
        isSuccess: undefined,
        authStatus: res.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: 0,
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: true,
      },
    });
  });

  test('测试数据异常调用', async () => {
    const dispatched: any = [];
    const res = {
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation(() => (Promise.resolve(res)));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationWelcomeLogic);
    const actionMock = {
      type: ON_AUTHENTICATION_WELCOME,
    };
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });

  test('接口异常调用', async () => {
    const dispatched: any = [];
    const exceptionError = new Error('onAuthenticationWelcomeLogic exception');
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockReturnValue(new Promise(() => {
      throw exceptionError;
    }));
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationWelcomeLogic);
    const actionMock = {
      type: ON_AUTHENTICATION_WELCOME,
    };
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });
});

describe('Sesame Logic onAuthenticationOrderConfirmLogic', () => {
  const initialState = {
    OrderDetail: {
      freeDeposit: {
        preAmountForCar: 300,
        freeDepositType: 10,
      },
      vendorInfo: {
        vendorID: 12345,
      },
    },
  };
  test('测试正常调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationOrderConfirmLogic);
    const actionMock = {
      type: ON_AUTHENTICATION_ORDERCONFRIM,
      data: {
        freeDepositWay: {},
      },
    };
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: UPDATA_FREE_DEPOSITINFO,
      data: {
        freeDepositType: initialState.OrderDetail.freeDeposit.freeDepositType,
        freeDepositWay: actionMock.data.freeDepositWay,
        preAmountForCar: initialState.OrderDetail.freeDeposit.preAmountForCar,
        vendorId: initialState.OrderDetail.vendorInfo.vendorID,
      },
    });
  });

  test('测试数据异常调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthenticationOrderConfirmLogic);
    const actionMock = {
      type: ON_AUTHENTICATION_ORDERCONFRIM,
    };
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: UPDATA_FREE_DEPOSITINFO,
      data: {
        freeDepositType: initialState.OrderDetail.freeDeposit.freeDepositType,
        freeDepositWay: undefined,
        preAmountForCar: initialState.OrderDetail.freeDeposit.preAmountForCar,
        vendorId: initialState.OrderDetail.vendorInfo.vendorID,
      },
    });
  });
});

describe('Sesame Logic onAuthConfirmPress', () => {
  const initialState = {
    Sesame: {
      authStatus: SesameState.unRealName,
      isCtripCredit: false,
    },
    DriverList: {
      unMaskPassenger: {
        fullName: "张三"
      }
    },
  };
  const res: any = {
    "alipayAuthInfo": {
      "authCount": 0,
      "authInfos": [
        {
          "button": {
            "title": "立即验证"
          },
          "contents": [
            {
              "contentStyle": "1",
              "stringObjs": [
                {
                  "content": "芝麻分满650",
                  "style": "1"
                },
                {
                  "content": "有机会享免押金租车",
                  "style": "2"
                }
              ]
            }
          ],
          "title": "",
          "type": 1
        }
      ],
      "authOrderCount": 0,
      "authStatus": 0,
      "authUrl": "www.baidu.com",
      "authedCountEqOne": false,
      "defaultInfo": {
        "button": {
          "title": "知道了"
        },
        "contents": [
          {
            "stringObjs": [
              {
                "content": "可能原因：",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "1.芝麻信用未达650分",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "2.未通过支付宝安全认证",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "详情请咨询支付宝客服",
                "style": "7"
              }
            ]
          }
        ],
        "title": "抱歉，验证未通过"
      },
      "idNo": "",
      "orderId": "12988053426",
      "promptInfo": {
        "button": {
          "title": "立即认证"
        },
        "contents": [
          {
            "stringObjs": [
              {
                "content": "芝麻信用满650分",
                "style": "1"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "享免押金租车",
                "style": "2"
              }
            ]
          }
        ],
        "title": ""
      },
      "requestId": "alipay200806025654991uuh6",
      "userName": ""
    },
    "baseResponse": {
      "isSuccess": false
    },
  };
  test('测试正常调用 非诚信分未失实名认证 且无芝麻认证url', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthConfirmPress);
    const actionMock = {
      type: ON_AUTH_CONFIRM_PRESS,
      data: {
        isVisible: true,
      },
    };
    jest.spyOn(SesameResponse, "getData").mockImplementation(() => (res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_AUTH_TICKET,
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: actionMock.data.isVisible,
      },
    });
  });

  test('测试正常调用 非诚信分未失实名认证 且有芝麻认证url&安装了支付宝', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthConfirmPress);
    const actionMock = {
      type: ON_AUTH_CONFIRM_PRESS,
      data: {
        isVisible: true,
      },
    };
    jest.spyOn(SesameResponse, "getData").mockImplementation(() => (res));
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({ ...initialState, authUrl: "www.baidu.com" }),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_AUTH_TICKET,
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: actionMock.data.isVisible,
      },
    });
  });

  test('测试正常调用 非诚信验证不通过但诚信用满足', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthConfirmPress);
    const actionMock = {
      type: ON_AUTH_CONFIRM_PRESS,
      data: {
        isVisible: true,
      },
    };
    jest.spyOn(SesameResponse, "getData").mockImplementation(() => (res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          ...initialState,
          Sesame: {
            ...initialState.Sesame,
            authStatus: SesameState.failAuthorizedSuccessTrip,
          }
        }),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_AUTH_TICKET,
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: actionMock.data.isVisible,
      },
    });
  });

  test('测试正常调用 非诚信验证认证成功', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthConfirmPress);
    const actionMock = {
      type: ON_AUTH_CONFIRM_PRESS,
      data: {
        isVisible: true,
      },
    };
    jest.spyOn(CarABTesting, "isCreditRent").mockReturnValue(true);
    jest.spyOn(SesameResponse, "getData").mockImplementation(() => (res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          ...initialState,
          Sesame: {
            ...initialState.Sesame,
            authStatus: SesameState.authorized,
          }
        }),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_AUTH_TICKET,
    });
    expect(dispatched[1]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: actionMock.data.isVisible,
      },
    });
  });


  test('测试正常调用 诚信分', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthConfirmPress);
    const mockState = {
      ...initialState,
      Sesame: {
        ...initialState,
        isCtripCredit: true,
        authStatus: SesameState.unRealName,
      },
    };
    const actionMock = {
      type: ON_AUTH_CONFIRM_PRESS,
      data: {
        isVisible: true,
      },
    };
    jest.spyOn(SesameResponse, "getData").mockImplementation(() => (res));
    jest.spyOn(SesameHelper, 'isInstallAlipay').mockImplementation(() => (Promise.resolve(true)));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => mockState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: actionMock.data.isVisible,
      },
    });
  });
});

describe('Sesame Logic onAuthCancelPress', () => {
  test('测试正常调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthCancelPress);
    const actionMock = {
      type: ON_AUTH_CANCEL_PRESS,
      data: {
        isVisible: true,
      },
    };
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => { },
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: actionMock.data.isVisible,
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });
});

describe('Sesame Logic onAuthRealNameRegister', () => {
  const initialState = {
    LocationAndDate: {
      rentalLocation: {
        pickUp: {
          cid: 43,
        },
      },
    },
    DriverList: {
      unMaskPassenger: {
        fullName: "张三"
      }
    },
    OrderDetail: {
      freeDeposit: {
        preAmountForCar: 300,
      },
    },
  };
  const res = {
    "alipayAuthInfo": {
      "authCount": 0,
      "authInfos": [
        {
          "button": {
            "title": "立即验证"
          },
          "contents": [
            {
              "contentStyle": "1",
              "stringObjs": [
                {
                  "content": "芝麻分满650",
                  "style": "1"
                },
                {
                  "content": "有机会享免押金租车",
                  "style": "2"
                }
              ]
            }
          ],
          "title": "",
          "type": 1
        }
      ],
      "authOrderCount": 0,
      "authStatus": 0,
      "authUrl": "",
      "authedCountEqOne": false,
      "defaultInfo": {
        "button": {
          "title": "知道了"
        },
        "contents": [
          {
            "stringObjs": [
              {
                "content": "可能原因：",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "1.芝麻信用未达650分",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "2.未通过支付宝安全认证",
                "style": "3"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "详情请咨询支付宝客服",
                "style": "7"
              }
            ]
          }
        ],
        "title": "抱歉，验证未通过"
      },
      "idNo": "",
      "orderId": "12988053426",
      "promptInfo": {
        "button": {
          "title": "立即认证"
        },
        "contents": [
          {
            "stringObjs": [
              {
                "content": "芝麻信用满650分",
                "style": "1"
              }
            ]
          },
          {
            "stringObjs": [
              {
                "content": "享免押金租车",
                "style": "2"
              }
            ]
          }
        ],
        "title": ""
      },
      "requestId": "alipay200806025654991uuh6",
      "userName": ""
    },
    "baseResponse": {
      "isSuccess": false
    },
  };
  test('测试正常调用 不展示Toast', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthRealNameRegister);
    const actionMock = {
      type: ON_AUTH_REALNAME_REGISTER,
      data: {
        showToast: false,
      },
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => (Promise.resolve(res)));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: res.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.realNameRegisterCallback,
        userName: res.alipayAuthInfo.userName,
        identifyCard: res.alipayAuthInfo.idNo,
        authOrderCount: res.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
    expect(dispatched[2]).toEqual({
      type: SET_VISIBLE_DONE,
      data: {
        isVisible: true,
      },
    });

  });

  test('测试正常调用 展示Toast 实名状态', async () => {
    const res1 = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.unRealName,
      },
    };
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthRealNameRegister);
    const actionMock = {
      type: ON_AUTH_REALNAME_REGISTER,
      data: {
        showToast: true,
      },
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => (Promise.resolve(res1)));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: res1.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.realNameRegisterCallback,
        userName: res1.alipayAuthInfo.userName,
        identifyCard: res1.alipayAuthInfo.idNo,
        authOrderCount: res1.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试正常调用 展示Toast 已认证状态', async () => {
    const res1 = {
      ...res,
      alipayAuthInfo: {
        ...res.alipayAuthInfo,
        authStatus: SesameState.authorized,
      },
    };
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthRealNameRegister);
    const actionMock = {
      type: ON_AUTH_REALNAME_REGISTER,
      data: {
        showToast: true,
      },
    };
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => (Promise.resolve(res1)));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: SET_SESAME_STATE,
      data: {
        authStatus: res1.alipayAuthInfo.authStatus,
        isLogin: true,
        authFlowStep: SesameAuthFlowStep.realNameRegisterCallback,
        userName: res1.alipayAuthInfo.userName,
        identifyCard: res1.alipayAuthInfo.idNo,
        authOrderCount: res1.alipayAuthInfo.authOrderCount,
        appType: undefined,
        isSuccess: undefined
      },
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试数据异常调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(onAuthRealNameRegister);
    const actionMock = {
      type: ON_AUTH_REALNAME_REGISTER,
      data: {
        showToast: true,
      },
    };
    const mockRes = {};
    jest.spyOn(CarFetch, 'getSesameAuthentication').mockImplementation((params) => (Promise.resolve(mockRes)));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched).toEqual([]);
  });

  // TODO 异常catch
  // test('接口异常调用', async () => {
  //   const dispatched: any = [];
  //   const logicFunc = takeEveryGeneratorFunction(onAuthRealNameRegister);
  //   const actionMock = {
  //     type: ON_AUTH_REALNAME_REGISTER,
  //     data: {
  //       showToast: true,
  //     },
  //   };
  //   const exceptionError = new Error('queryMembershipRights exception');
  //   jest.spyOn(CarFetch, 'getSesameAuthentication').mockReturnValue(new Promise(() => {
  //     throw exceptionError;
  //   }));
  //   await runSaga(
  //     {
  //       dispatch: action => dispatched.push(action),
  //       getState: () => initialState,
  //     },
  //     logicFunc,
  //     actionMock,
  //   ).toPromise();
  //   expect(dispatched).toEqual([]);
  // });
});

describe('Sesame Logic fetchCancelAuthLogic', () => {
  const initialState = {
    LocationAndDate: {
      rentalLocation: {
        pickUp: {
          cid: 43,
        },
      },
    },
    DriverList: {
      unMaskPassenger: {
        fullName: "张三"
      }
    },
  };
  const res: any = {
    baseResponse: {
      isSuccess: true,
    },
  };
  test('测试正常调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(fetchCancelAuthLogic);
    const actionMock = {
      type: FETCH_CANCEL_AUTH,
      data: {
        textConfig: {},
        successCb: {},
        failedCb: {},
        newHomePage: {},
      },
    };
    jest.spyOn(CarFetch, 'cancelSesameAuthentication').mockImplementation(() => (Promise.resolve(res)));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: CANCEL_AUTHENTICATION_DONE,
      data: {
        isSuccess: res.baseResponse.isSuccess,
        textConfig: actionMock.data.textConfig,
        successCb: actionMock.data.successCb,
        failedCb: actionMock.data.failedCb,
        newHomePage: actionMock.data.newHomePage,
      },
    });
  });

  test('测试数据异常调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(fetchCancelAuthLogic);
    const actionMock = {
      type: FETCH_CANCEL_AUTH,
      data: {
        textConfig: {},
        successCb: {},
        failedCb: {},
        newHomePage: {},
      },
    };
    const mockRes: any = {};
    jest.spyOn(CarFetch, 'cancelSesameAuthentication').mockImplementation(() => (Promise.resolve(mockRes)));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: CANCEL_AUTHENTICATION_DONE,
      data: {
        isSuccess: mockRes?.baseResponse?.isSuccess,
        textConfig: actionMock.data.textConfig,
        successCb: actionMock.data.successCb,
        failedCb: actionMock.data.failedCb,
        newHomePage: actionMock.data.newHomePage,
      },
    });
  });

  test('接口异常调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(fetchCancelAuthLogic);
    const actionMock = {
      type: FETCH_CANCEL_AUTH,
      data: {
        textConfig: {},
        successCb: {},
        failedCb: {},
        newHomePage: {},
      },
    };
    const exceptionError = new Error('cancelSesameAuthentication exception');
    jest.spyOn(CarFetch, 'cancelSesameAuthentication').mockReturnValue(new Promise(() => {
      throw exceptionError;
    }));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: CANCEL_AUTHENTICATION_DONE,
      data: {
        isSuccess: undefined,
        textConfig: actionMock.data.textConfig,
        successCb: actionMock.data.successCb,
        failedCb: actionMock.data.failedCb,
        newHomePage: actionMock.data.newHomePage,
      },
    });
  });
});

describe('Sesame Logic cancelAuthenticationLogic', () => {
  const initialState = {
    LocationAndDate: {
      rentalLocation: {
        pickUp: {
          cid: 43,
        },
      },
    },
    DriverList: {
      unMaskPassenger: {
        fullName: "张三"
      }
    },
    OrderDetail: {
      vendorInfo: {
        vendorID: "12346",
      },
    }
  };
  test('测试正常调用 订单详情页', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(cancelAuthenticationLogic);
    const actionMock = {
      type: CANCEL_AUTHENTICATION,
      data: {
        textConfig: {
          cancelZhimaTile: "1",
          cancelZhimaContent: "2",
          cancelZhimaNo: "3",
          cancelZhimaYes: "4",
        },
        successCb: {},
        failedCb: {},
        newHomePage: {},
        isOrderDetail: true,
      },
    };
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: TOGGLE_CANCEL_AUTHENTICATION_MODAL,
      data: {
        visible: true,
        data: {
          title: cancelZhima.cancelZhimaTile,
          contentText: cancelZhima.cancelZhimaContent,
          leftBtnText: cancelZhima.cancelZhimaNo,
          rightBtnText: cancelZhima.cancelZhimaYes,
          vendorId: initialState.OrderDetail.vendorInfo.vendorID,
          textConfig: actionMock.data.textConfig,
          successCb: actionMock.data.successCb,
          failedCb: actionMock.data.failedCb,
          newHomePage: actionMock.data.newHomePage,
        },
      }
    });
  });

  test('测试正常调用 非订单详情页', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(cancelAuthenticationLogic);
    const actionMock = {
      type: CANCEL_AUTHENTICATION,
      data: {
        textConfig: {
          cancelZhimaTile: "1",
          cancelZhimaContent: "2",
          cancelZhimaNo: "3",
          cancelZhimaYes: "4",
        },
        successCb: {},
        failedCb: {},
        newHomePage: {},
        isOrderDetail: false,
      },
    };
    jest.spyOn(ProductSelectors, "getVendorInfo").mockImplementation(() => ({ "bizVendorCode": "13039" }));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: TOGGLE_CANCEL_AUTHENTICATION_MODAL,
      data: {
        visible: true,
        data: {
          title: cancelZhima.cancelZhimaTile,
          contentText: cancelZhima.cancelZhimaContent,
          leftBtnText: cancelZhima.cancelZhimaNo,
          rightBtnText: cancelZhima.cancelZhimaYes,
          vendorId: "13039",
          textConfig: actionMock.data.textConfig,
          successCb: actionMock.data.successCb,
          failedCb: actionMock.data.failedCb,
          newHomePage: actionMock.data.newHomePage,
        },
      }
    });
  });

  test('测试无结果调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(cancelAuthenticationLogic);
    const actionMock = {
      type: CANCEL_AUTHENTICATION,
    };
    jest.spyOn(ProductSelectors, "getVendorInfo").mockImplementation(() => ({ "bizVendorCode": "13039" }));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => initialState,
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: TOGGLE_CANCEL_AUTHENTICATION_MODAL,
      data: {
        visible: true,
        data: {
          title: cancelZhima.cancelZhimaTile,
          contentText: cancelZhima.cancelZhimaContent,
          leftBtnText: cancelZhima.cancelZhimaNo,
          rightBtnText: cancelZhima.cancelZhimaYes,
          vendorId: "13039",
          textConfig: cancelZhima,
          successCb: undefined,
          failedCb: undefined,
          newHomePage: undefined,
        },
      }
    });
  });
});

describe('Sesame Logic cancelAuthenticationDoneLogic', () => {
  test('测试正常调用 成功调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(cancelAuthenticationDoneLogic);
    const actionMock = {
      type: CANCEL_AUTHENTICATION_DONE,
      data: {
        isSuccess: true,
        successCb: () => { },
        newHomePage: {},
      },
    };
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => { },
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: INIT_SESAME_STATE,
      data: {
        isInitial: undefined,
        appType: undefined,
        isOrderDetail: undefined,
        isBooking: undefined,
        newHomePage: actionMock.data.newHomePage,
      }
    });
    expect(dispatched[1]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试正常调用 失败调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(cancelAuthenticationDoneLogic);
    const actionMock = {
      type: CANCEL_AUTHENTICATION_DONE,
      data: {
        isSuccess: false,
        failedCb: () => { },
        newHomePage: {},
      },
    };
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => { },
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });

  test('测试无结果调用 成功调用', async () => {
    const dispatched: any = [];
    const logicFunc = takeEveryGeneratorFunction(cancelAuthenticationDoneLogic);
    const actionMock = {
      type: CANCEL_AUTHENTICATION_DONE,
    };
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => { },
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: SET_AUTH_TICKET,
    });
  });
});
