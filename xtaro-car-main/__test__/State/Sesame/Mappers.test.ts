import { Utils } from '../../../src/pages/xcar/Util/Index';
import {
  getSesameInfo,
  getSesameTexts,
  replaceName,
  getCreditRentSesameTexts,
  getSesameBarTexts,
  getDetailSesameBarTexts,
  getSesameTrackInfo,
  getUserCreditStatus,
  getIsSupportSesame,
  geZhimaNoteInfo,
  getZhimaComplementaryAmount,
  getIsFreeDepositModalAvailable,
  getIsEhiDeposit,
  getSesameUserTip,
  getDepositLabelType,
  isCtripCreditRent,
  isCreditQualified,
  getSesameOrderId,
} from '../../../src/pages/xcar/State/Sesame/Mappers';
import {
  SesameAuthFlowStep,
  SesameState,
  SesamePageLocation,
} from '../../../src/pages/xcar/State/Sesame/Types';
import {
  ProductSelectors,
  ProductReqAndResData,
  SesameResponse,
} from '../../../src/pages/xcar/Global/Cache/Index';

jest.mock('../../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Global/Cache/ProductSelectors') }));

describe('Sesame Mappers getSesameInfo', () => {
  const mockData = [
    {
      expected: {
        isSupportZhima: false,
      },
    },
  ];
  jest.spyOn(ProductSelectors, "getSesameInfo").mockImplementation(() => ({isSupportZhima: false}));
  test.each(mockData)('Sesame Mappers getSesameInfo', ({ expected }) => {
    const result = getSesameInfo();
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getSesameTexts', () => {
  const res1 : any = {
    "alipayAuthInfo": {

    },
  };
  const defaultInfo = {
    "button": {
        "title": "知道了"
    },
    "contents": [
        {
            "stringObjs": [
                {
                    "content": "可能原因：",
                    "style": "3"
                }
            ]
        },
        {
            "stringObjs": [
                {
                    "content": "1.芝麻信用未达650分",
                    "style": "3"
                }
            ]
        },
        {
            "stringObjs": [
                {
                    "content": "2.未通过支付宝安全认证",
                    "style": "3"
                }
            ]
        },
        {
            "stringObjs": [
                {
                    "content": "详情请咨询支付宝客服",
                    "style": "7"
                }
            ]
        }
    ],
    "title": "抱歉，验证未通过",
  };
  const promptInfo = {
    title: '',
    contents: [{
      stringObjs: [{
          content: '芝麻信用满650分',
          style: '1',
        },
        {
          content: '可享免押金租车',
          style: '2',
        }
      ],
      contentStyle: '1',
    }, ],
    button: {
      title: '立即认证',
    },
  };
  const promptInfo1 = {
    "title": "抱歉，验证未通过",
    "subTitle": "驾驶员{0}暂时无法享用信用租",
    "contents": [{
        "stringObjs": [{
          "content": "验证结果7天有效期已到（已支付订单不受影响）",
          "style": "3"
        }]
      },
      {
        "stringObjs": [{
          "content": "重新验证芝麻信用满650分",
          "style": "3"
        }]
      },
      {
        "stringObjs": [{
          "content": "续享免押金租车",
          "style": "2"
        }]
      }
    ],
    "button": {
      "title": "重新认证"
    }
  };
  const promptInfo2 = {
    title: '验证通过',
    contents: [{
        stringObjs: [{
          content: '您的芝麻信用已验证通过。任意驾驶员通过当前账户下单，可享受押金服务，取车时无需刷取引用卡预授权。',
          style: '3',
        }]
      },
      {
        stringObjs: [{
            content: '服务适用于带有',
            style: '3',
          },
          {
            content: '免押金',
            style: '5',
          },
          {
            content: '标签的产品。',
            style: '3',
          },
        ]
      },
      {
        stringObjs: [{
          content: '芝麻验证结果10天内有效',
          style: '3',
        }]
      },
      {
        stringObjs: [{
          content: '本授权将在支付宝生成1笔服务订单，到期自动守约',
          style: '4',
        }]
      },
    ],
    button: {
      title: '立即体验免押金租车',
    },
  };
  const promptInfo3 = {
    title: '验证通过',
    contents: [{
        stringObjs: [{
            content: '宋仲基',
            style: '6',
          },
          {
            content: '的芝麻信用满足条件 可享',
            style: '3',
          },
        ]
      },
      {
        stringObjs: [{
            content: '免租车＆违章押金',
            style: '5',
          },
          {
            content: '单免租车押金',
            style: '5',
          },
        ]
      },
      {
        stringObjs: [{
          content: '芝麻验证结果7天内有效',
          style: '4',
        }]
      },
      {
        stringObjs: [{
          content: '免押金租车仅服务满足条件的驾驶员',
          style: '4',
        }]
      },
    ],
    button: {
      title: '立即体验免押金租车',
    },
  };
  const promptInfo4 = {
    title: '',
    contents: [{
        stringObjs: [{
          content: '您的芝麻分满足条件',
          style: '3',
        }]
      },
      {
        stringObjs: [{
          content: '继续完成账户实名',
          style: '2',
        }]
      },
      {
        stringObjs: [{
          content: '可享免押金租',
          style: '2',
        }]
      },
    ],
    button: {
      title: '继续完成账户实名',
    },
  };
  const promptInfo5 = {
    title: '抱歉，验证未通过',
    contents: [{
        stringObjs: [{
          content: '您当前实名信息暂不符合',
          style: '8',
        }]
      },
      {
        stringObjs: [{
          content: '您的携程信用分已超650分可享受小部分产品免租车押金服务',
          style: '3',
        }]
      },
    ],
    button: {
      title: '点击立即体验免押金租车',
    },
  };
  const promptInfo6 = {
    title: '抱歉，验证未通过',
    contents: [{
        stringObjs: [{
          content: '您当前实名信息暂不符合',
          style: '8',
        }]
      },
      {
        stringObjs: [{
          content: '驾驶员年龄(满18周岁)的要求',
          style: '8',
        }]
      },
    ],
    button: {
      title: '知道了',
    },
  };
  const promptInfo7 = {
    title: '验证通过',
    contents: [{
        contentStyle: '10',
        stringObjs: [{
          content: '宋仲基',
          style: '6',
        }, {
          content: '3425311987****3288',
          style: '3',
        }]
      },
      {
        contentStyle: '3',
        stringObjs: [{
          content: '芝麻分满足条件 可享免押金租车',
          style: '3',
        }]
      },
      {
        contentStyle: '9',
        stringObjs: [{
          content: '当前驾驶员为 ',
          style: '3',
        }, {
          content: '李明达',
          style: '6',
        }, {
          content: '是否切换驾驶员?',
          style: '3',
        }]
      },
    ],
    button: {
      title: '切换',
    },
    buttonExt: [{
      title: '再想想',
    }, ]
  };
  const res2 : any = {
    "alipayAuthInfo": {
      "defaultInfo": defaultInfo,
      "sameDriver": true,
    },
  };
  const res3 : any = {
    "alipayAuthInfo": {
      "defaultInfo": defaultInfo,
      "sameDriver": false,
    },
  };
  const res4 : any = {
    "alipayAuthInfo": {
      "promptInfo": promptInfo,
      "sameDriver": false,
    },
  };
  const res5 : any = {
    "alipayAuthInfo": {
      "promptInfo": promptInfo1,
      "sameDriver": false,
    },
  };
  const res6 : any = {
    "alipayAuthInfo": {
      "promptInfo": promptInfo2,
      "sameDriver": true,
    },
  };
  const res7 : any = {
    "alipayAuthInfo": {
      "promptInfo": promptInfo3,
      "sameDriver": true,
    },
  };
  const res8 : any = {
    "alipayAuthInfo": {
      "promptInfo": promptInfo4,
      "sameDriver": true,
    },
  };
  const res9 : any = {
    "alipayAuthInfo": {
      "promptInfo": promptInfo5,
      "sameDriver": true,
    },
  };
  const res10 : any = {
    "alipayAuthInfo": {
      "promptInfo": promptInfo6,
      "sameDriver": true,
    },
  };
  const res11 : any = {
    "alipayAuthInfo": {
      "promptInfo": promptInfo7,
      "sameDriver": false,
    },
  };
  const mockStateMap = [
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: 1,
          authFlowStep: SesameAuthFlowStep.netWorkFailure,
        },
      },
      res: res1,
      isCtripIsd: true,
      isCtripOsd: false,
      expected: null,
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.outOfDate,
          authFlowStep: SesameAuthFlowStep.netWorkFailure,
        },
      },
      res: res2,
      isCtripIsd: true,
      isCtripOsd: false,
      expected: {"btnText": "知道了", "footers": [], "tips": ["详情请咨询支付宝客服"], "type": "VERIFY_FAIL", "verifyFailure": {"boldTitle": "抱歉，验证未通过", "contents": [{"content": "可能原因：", "type": "text"}, {"content": "1.芝麻信用未达650分", "type": "text"}, {"content": "2.未通过支付宝安全认证", "type": "text"}]}},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.outOfDate,
          authFlowStep: SesameAuthFlowStep.netWorkFailure,
        },
      },
      res: res2,
      isCtripIsd: true,
      isCtripOsd: false,
      expected: {"btnText": "知道了", "footers": [], "tips": ["详情请咨询支付宝客服"], "type": "VERIFY_FAIL", "verifyFailure": {"boldTitle": "抱歉，验证未通过", "contents": [{"content": "可能原因：", "type": "text"}, {"content": "1.芝麻信用未达650分", "type": "text"}, {"content": "2.未通过支付宝安全认证", "type": "text"}]}},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.unAuthorized,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res4,
      isCtripIsd: true,
      isCtripOsd: false,
      expected:  {"btnText":"立即认证","type":"VERIFY_NOW","verifyNowContent":{"iconTitle":"芝麻信用满650分"},"tips":[],"footers":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.outOfDate,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res5,
      isCtripIsd: true,
      isCtripOsd: false,
      expected: {"btnText":"重新认证","type":"VERIFY_OUT_OF_DATE","verifyOutOfDateContent":{"contents":[{"type":"text","content":"验证结果7天有效期已到（已支付订单不受影响）"},{"type":"text","content":"重新验证芝麻信用满650分"}],"boldContent":"续享免押金租车"},"tips":[],"footers":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res7,
      isCtripIsd: true,
      isCtripOsd: false,
      expected: {"btnText":"立即体验免押金租车","type":"VERIFY_SUCCESS_ISD","verifySuccessIsd":{"contents":[{"type":"nameText","nameText":"宋仲基","content":"的芝麻信用满足条件 可享"},{"type":"labels","labels":[{"labelText":"免租车＆违章押金"},{"labelText":"单免租车押金"}]}],"boldTitle":"验证通过"},"tips":[],"footers":["芝麻验证结果7天内有效","免押金租车仅服务满足条件的驾驶员"]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.failAuthorized,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res3,
      isCtripIsd: true,
      isCtripOsd: false,
      expected: {"type":"VERIFY_FAIL","verifyFailure":{"contents":[]},"tips":[],"footers":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res11,
      isCtripIsd: true,
      isCtripOsd: false,
      expected: {"btnText":"切换","type":"VERIFY_CHANE_DRIVER","verifyChangeDriver":{"contents":[{"type":"nameIdCard","nameText":"宋仲基","content":"3425311987****3288"},{"type":"text","content":"芝麻分满足条件 可享免押金租车"},{"type":"textNameText","leftText":"当前驾驶员为 ","nameText":"李明达","rightText":"是否切换驾驶员?"}],"boldTitle":"验证通过"},"secondBtnText":"再想想","tips":[],"footers":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.unRealName,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res8,
      isCtripIsd: true,
      isCtripOsd: false,
      expected: {"btnText":"继续完成账户实名","type":"VERIFY_REAL_NAME","verifyRealName":{"contents":[{"type":"text","content":"您的芝麻分满足条件"}],"boldContent":"继续完成账户实名","boldContent2":"可享免押金租"},"tips":[],"footers":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.failAuthorizedSuccessTrip,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res9,
      isCtripIsd: true,
      isCtripOsd: false,
      expected: {"btnText":"点击立即体验免押金租车","type":"VERIFY_FAIL_SESAME_SUCCESS_TRIP","verifyFailSesameSuccessTrip":{"contents":[{"type":"textDecline","content":"您当前实名信息暂不符合"},{"type":"text","content":"您的携程信用分已超650分可享受小部分产品免租车押金服务"}],"boldTitle":"抱歉，验证未通过"},"tips":[],"footers":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.under18,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res10,
      isCtripIsd: true,
      isCtripOsd: false,
      expected: {"btnText":"知道了","type":"VERIFY_UNDER_18","verifyUnder18":{"contents":[{"type":"textDecline","content":"您当前实名信息暂不符合"},{"type":"textDecline","content":"驾驶员年龄(满18周岁)的要求"}],"boldTitle":"抱歉，验证未通过"},"tips":[],"footers":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res6,
      isCtripIsd: false,
      isCtripOsd: true,
      expected: {"btnText":"立即体验免押金租车","type":"VERIFY_SUCCESS","verifySuccess":{"contents":[{"type":"text","content":"您的芝麻信用已验证通过。任意驾驶员通过当前账户下单，可享受押金服务，取车时无需刷取引用卡预授权。"},{"type":"textLabelText","leftText":"服务适用于带有","labelText":"免押金","rightText":"标签的产品。"},{"type":"text","content":"芝麻验证结果10天内有效"}],"boldTitle":"验证通过"},"tips":[],"footers":["本授权将在支付宝生成1笔服务订单，到期自动守约"]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.netWorkFailure,
        },
      },
      res: res3,
      isCtripIsd: false,
      isCtripOsd: true,
      expected: {"btnText":"知道了","type":"VERIFY_FAIL","verifyFailure":{"contents":[{"type":"text","content":"可能原因："},{"type":"text","content":"1.芝麻信用未达650分"},{"type":"text","content":"2.未通过支付宝安全认证"}],"boldTitle":"抱歉，验证未通过"},"tips":["详情请咨询支付宝客服"],"footers":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res3,
      isCtripIsd: false,
      isCtripOsd: true,
      expected: {"type":"VERIFY_CHANE_DRIVER","verifyChangeDriver":{"contents":[]},"tips":[],"footers":[]},
    },
    {
      state: {
        Sesame: {
          visible: false,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.promptInfo,
        },
      },
      res: res3,
      isCtripIsd: false,
      isCtripOsd: true,
      expected: null,
    },
    {
      state: {
        Sesame: {
          visible: true,
          authStatus: 1,
          authFlowStep: SesameAuthFlowStep.netWorkFailure,
        },
      },
      res: {
        "alipayAuthInfo": null,
      },
      isCtripIsd: true,
      isCtripOsd: false,
      expected: null,
    },
  ];
  test.each(mockStateMap)(
    'getSesameTexts check',
    ({ state, res, isCtripIsd, isCtripOsd, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => isCtripIsd);
      jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => isCtripOsd);
      jest.spyOn(SesameResponse, 'getData').mockImplementation(() => res);
      const result = getSesameTexts(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('Sesame Mappers replaceName', () => {
  const text1 = 'may name is {0}';
  const name1 = 'zhang san';
  const mockData = [
    {
      text: text1,
      name: name1,
      expected: text1.replace('{0}', name1),
    },
    {
      expected: '',
    },
    {
      text: '',
      name: '',
      expected: '',
    },
  ];
  test.each(mockData)('Sesame Mappers replaceName', ({ text, name, expected }) => {
    const result = replaceName(text, name);
    expect(result).toEqual(expected);
  })
});

jest.mock('../../../src/pages/xcar/State/SelectorIndex', () => ({
  DriverListSelector: {
    getPriceReqPassenger: jest.fn((state) => state.DriverList.passenger),
    getPassengerMasker: jest.fn((state) => state.DriverList.passenger),
  }
}));
describe('Sesame Mappers getCreditRentSesameTexts', () => {
  const res1 : any = {
    "alipayAuthInfo": {

    },
  };
  const res2 : any = {
    "alipayAuthInfo": {
      "defaultInfo": {
        "button": {
            "title": "知道了"
        },
        "contents": [
            {
                "stringObjs": [
                    {
                        "content": "可能原因：",
                        "style": "3"
                    }
                ]
            },
            {
                "stringObjs": [
                    {
                        "content": "1.芝麻信用未达650分",
                        "style": "3"
                    }
                ]
            },
            {
                "stringObjs": [
                    {
                        "content": "2.未通过支付宝安全认证",
                        "style": "3"
                    }
                ]
            },
            {
                "stringObjs": [
                    {
                        "content": "详情请咨询支付宝客服",
                        "style": "7"
                    }
                ]
            }
        ],
        "buttonExt": [{
          "title": "取消"
        }],
        "title": "抱歉，验证未通过",
      },
      "promptInfo": {
        "title": "抱歉，验证未通过",
        "subTitle": "驾驶员{0}暂时无法享用信用租",
        "contents": [{
            "stringObjs": [{
              "content": "可能原因：",
              "style": "3"
            }]
          },
          {
            "stringObjs": [{
              "content": "1.芝麻信用未达650分",
              "style": "3"
            }]
          },
          {
            "stringObjs": [{
              "content": "2.未通过支付宝安全认证",
              "style": "3"
            }]
          },
          {
            "stringObjs": [{
              "content": "详情请咨询支付宝客服",
              "style": "7"
            }]
          }
        ],
        "button": {
          "title": "知道了"
        }
      },
      "userName": "李四",
      "sameDriver": true,
    },
  };
  const res3 : any = {
    "alipayAuthInfo": {
      "defaultInfo": {
        "button": {
            "title": "知道了"
        },
        "contents": [
            {
                "stringObjs": [
                    {
                        "content": "可能原因：",
                        "style": "3"
                    }
                ]
            },
            {
                "stringObjs": [
                    {
                        "content": "1.芝麻信用未达650分",
                        "style": "3"
                    }
                ]
            },
            {
                "stringObjs": [
                    {
                        "content": "2.未通过支付宝安全认证",
                        "style": "3"
                    }
                ]
            },
            {
                "stringObjs": [
                    {
                        "content": "详情请咨询支付宝客服",
                        "style": "7"
                    }
                ]
            }
        ],
        "title": "抱歉，验证未通过",
      },
      "promptInfo": {
        "title": "抱歉，验证未通过",
        "subTitle": "驾驶员{0}暂时无法享用信用租",
        "contents": [{
            "stringObjs": [{
              "content": "可能原因：",
              "style": "3"
            }]
          },
          {
            "stringObjs": [{
              "content": "1.芝麻信用未达650分",
              "style": "3"
            }]
          },
          {
            "stringObjs": [{
              "content": "2.未通过支付宝安全认证",
              "style": "3"
            }]
          },
          {
            "stringObjs": [{
              "content": "详情请咨询支付宝客服",
              "style": "7"
            }]
          }
        ],
        "button": {
          "title": "知道了"
        }
      },
      "userName": "王刘",
      "complementaryAmount": 10,
      "sameDriver": false,
    },
  };
  const priceRes1 = {
    "promptInfos": [
      {
          "title": "抱歉，验证未通过",
          "subTitle": "驾驶员携程测试暂不可享信用租",
          "contents": [
              {
                  "stringObjs": [
                      {
                          "content": "可能原因：",
                          "style": "3"
                      }
                  ]
              },
              {
                  "stringObjs": [
                      {
                          "content": "1.程信分未达650分",
                          "style": "3"
                      }
                  ]
              },
              {
                  "stringObjs": [
                      {
                          "content": "2.未通过携程程信分安全验证",
                          "style": "3"
                      }
                  ]
              },
              {
                  "stringObjs": [
                      {
                          "content": "详情请咨询携程金融-程信分客服",
                          "style": "7"
                      }
                  ]
              }
          ],
          "type": 10,
          "button": {
              "title": "知道了"
          }
      }
    ]
  };
  const priceRes2 = {
    "promptInfos": [
      {
          "title": "抱歉，验证未通过",
          "subTitle": "驾驶员携程测试暂不可享信用租",
          "contents": [
              {
                  "stringObjs": [
                      {
                          "content": "可能原因：",
                          "style": "3"
                      }
                  ]
              },
              {
                  "stringObjs": [
                      {
                          "content": "1.程信分未达650分",
                          "style": "3"
                      }
                  ]
              },
              {
                  "stringObjs": [
                      {
                          "content": "2.未通过携程程信分安全验证",
                          "style": "3"
                      }
                  ]
              },
              {
                  "stringObjs": [
                      {
                          "content": "详情请咨询携程金融-程信分客服",
                          "style": "7"
                      }
                  ]
              }
          ],
          "type": 10,
          "button": {
              "title": "知道了"
          },
          "buttonExt": [{
            "title": "取消"
          }]
      }
    ]
  };
  const passenger = {
    "userName": "张三",
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "340826199012231110"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false,
    "isRecommend": true,
  };
  const mockStateMap = [
    {
      state: {
        Sesame: {
          visible: false,
          isUseCancel: true,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.authFailure,
          isCtripCredit: true,
        },
        DriverList: {
          passenger,
        },
      },
      isCtripIsd: true,
      res: res1,
      priceRes: priceRes1,
      expected: null,
    },
    {
      state: {
        Sesame: {
          visible: true,
          isUseCancel: true,
          authStatus: SesameState.unAuthorized,
          authFlowStep: SesameAuthFlowStep.authFailure,
          isCtripCredit: true,
        },
        DriverList: {
          passenger,
        },
      },
      isCtripIsd: true,
      res: null,
      priceRes: {},
      expected: null,
    },
    {
      state: {
        Sesame: {
          visible: true,
          isUseCancel: false,
          authStatus: SesameState.failAuthorized,
          authFlowStep: SesameAuthFlowStep.authFailure,
          isCtripCredit: false,
        },
        DriverList: {
          passenger,
        },
      },
      isCtripIsd: true,
      res: res2,
      priceRes: priceRes1,
      expected: {"btnText":"知道了","showClose":true,"type":"VERIFY_FAIL","verifyRes":{"contents":[{"type":"text","content":"可能原因："},{"type":"textLeft","content":"1.芝麻信用未达650分"},{"type":"textLeft","content":"2.未通过支付宝安全认证"}],"boldTitle":"抱歉，验证未通过","subTitle":"驾驶员李四暂时无法享用信用租"},"tips":["详情请咨询支付宝客服"]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          isUseCancel: false,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.authFailure,
          isCtripCredit: true,
        },
        DriverList: {
          passenger,
        },
      },
      isCtripIsd: true,
      res: res2,
      priceRes: priceRes2,
      expected: {"btnText":"知道了","showClose":true,"type":"VERIFY_SUCCESS_ISD","verifyRes":{"boldTitle":"抱歉，验证未通过","subTitle":"驾驶员携程测试暂不可享信用租"},"tips":["详情请咨询携程金融-程信分客服"]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          isUseCancel: true,
          authStatus: SesameState.unAuthorized,
          authFlowStep: SesameAuthFlowStep.authFailure,
          isCtripCredit: false,
        },
        DriverList: {
          passenger,
        },
      },
      isCtripIsd: true,
      res: res2,
      priceRes: priceRes2,
      expected: {"btnText":"知道了","secondBtnText":"取消","showClose":true,"type":"VERIFY_FAIL","verifyRes":{"contents":[{"type":"text","content":"可能原因："},{"type":"textLeft","content":"1.芝麻信用未达650分"},{"type":"textLeft","content":"2.未通过支付宝安全认证"}],"boldTitle":"抱歉，验证未通过","subTitle":""},"tips":["详情请咨询支付宝客服"]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          isUseCancel: true,
          authStatus: SesameState.unRealName,
          authFlowStep: SesameAuthFlowStep.authFailure,
          isCtripCredit: false,
        },
        DriverList: {
          passenger,
        },
      },
      isCtripIsd: true,
      res: res2,
      priceRes: priceRes2,
      expected: {"btnText":"知道了","showClose":true,"type":"VERIFY_FAIL","verifyRes":{"contents":[{"type":"text","content":"可能原因："},{"type":"textLeft","content":"1.芝麻信用未达650分"},{"type":"textLeft","content":"2.未通过支付宝安全认证"}],"boldTitle":"抱歉，验证未通过","subTitle":"驾驶员李四暂时无法享用信用租"},"tips":["详情请咨询支付宝客服"]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          isUseCancel: true,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.authFailure,
          isCtripCredit: false,
        },
        DriverList: {
          passenger,
        },
      },
      isCtripIsd: true,
      res: res3,
      priceRes: priceRes2,
      expected: {"btnText":"知道了","type":"VERIFY_SUCCESS_FUND","verifyRes":{"boldTitle":"抱歉，验证未通过","subTitle":"驾驶员{0}暂时无法享用信用租"},"tips":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          isUseCancel: true,
          authStatus: SesameState.unRealName,
          authFlowStep: SesameAuthFlowStep.promptInfo,
          isCtripCredit: false,
        },
        DriverList: {
          passenger,
        },
      },
      isCtripIsd: true,
      res: res2,
      priceRes: priceRes2,
      expected: {"btnText":"知道了","type":"VERIFY_REAL_NAME","verifyRes":{"boldTitle":"抱歉，验证未通过","subTitle":"驾驶员李四暂时无法享用信用租"},"tips":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          isUseCancel: true,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.promptInfo,
          isCtripCredit: false,
        },
        DriverList: {
          passenger,
        },
      },
      isCtripIsd: true,
      res: res2,
      priceRes: priceRes2,
      expected: {"btnText":"知道了","type":"VERIFY_SUCCESS_ISD","verifyRes":{"boldTitle":"抱歉，验证未通过","subTitle":"驾驶员李四暂时无法享用信用租"},"tips":[]},
    },
    {
      state: {
        Sesame: {
          visible: true,
          isUseCancel: true,
          authStatus: SesameState.authorized,
          authFlowStep: SesameAuthFlowStep.promptInfo,
          isCtripCredit: false,
        },
        DriverList: {
          passenger,
        },
      },
      isCtripIsd: true,
      res: res2,
      priceRes: priceRes2,
      expected: {"btnText":"知道了","type":"VERIFY_SUCCESS_ISD","verifyRes":{"boldTitle":"抱歉，验证未通过","subTitle":"驾驶员李四暂时无法享用信用租"},"tips":[]},
    },
  ];
  test.each(mockStateMap)(
    'getCreditRentSesameTexts check',
    ({ state, res, priceRes, isCtripIsd, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => isCtripIsd);
      jest.spyOn(SesameResponse, 'getData').mockImplementation(() => res);
      jest.spyOn(ProductSelectors, 'getPriceResData').mockImplementation(() => priceRes);
      const result = getCreditRentSesameTexts(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('Sesame Mappers getSesameBarTexts', () => {
  const data = {
    authInfos: [
      {
          "button": {
              "title": "立即验证"
          },
          "contents": [
              {
                  "contentStyle": "1",
                  "stringObjs": [
                      {
                          "content": "芝麻分满650",
                          "style": "1"
                      },
                      {
                          "content": "有机会享免押金租车",
                          "style": "2"
                      }
                  ]
              }
          ],
          "title": "",
          "type": SesamePageLocation.detailBar
      },
      {
          "button": {
              "title": "实名认证"
          },
          "contents": [
              {
                  "contentStyle": "3",
                  "stringObjs": [
                      {
                          "content": "支付宝实名认证后可享",
                          "style": "1"
                      }
                  ]
              },
              {
                "contentStyle": "5",
                "stringObjs": [
                    {
                        "content": "重新认证",
                        "style": "1"
                    }
                ]
            }
          ],
          "title": "免押金服务",
          "type": SesamePageLocation.bookingArea
      },
      {
          "button": {
              "title": "去授权"
          },
          "contents": [
              {
                  "contentStyle": "1",
                  "stringObjs": [
                      {
                          "content": "芝麻信用≥650分有机会可享",
                          "style": "1",
                          "url": "Authentication"
                      },
                      {
                          "content": "已享一笔一嗨免押，本次需重新授权",
                          "style": "1"
                      }
                  ]
              }
          ],
          "title": "免押金服务",
          "type": SesamePageLocation.packageIncludes
      },
      {
          "button": {
              "title": ""
          },
          "contents": [
              {
                  "contentStyle": "1",
                  "stringObjs": [
                      {
                          "content": "授权免押时，有补足资金",
                          "style": "1",
                          "url": "EhiRefundIntroModal"
                      }
                  ]
              }
          ],
          "title": "免押金服务",
          "type": SesamePageLocation.creditRent
      }
    ],
    authStatus: 0,
    complementaryAmount: 100, // 补足资金
  };
  const mockData = [
    {
      type: SesamePageLocation.detailBar,
      data,
      expected: {"buttonText":"立即验证","iconTitle":[{"title":"芝麻分满650","isBold":false},{"title":"有机会享免押金租车","isBold":true}]},
    },
    {
      type: SesamePageLocation.bookingArea,
      data,
      expected: {"title":"免押金服务","buttonText":"实名认证","content":"支付宝实名认证后可享","iconHeader":{"iconTitle":[{"title":"重新认证","isBold":true,"style":[{"fontFamily":"PingFangSC-Semibold","fontSize":17.28,"lineHeight":24.48},{"color":"#00A0E9"}]}]}},
      expected: {
        title: '免押金服务',
        buttonText: '实名认证',
        content: '支付宝实名认证后可享',
        iconHeader: {
          iconTitle: [
            {
              title: '重新认证',
              isBold: true,
              style: [
                {
                  fontFamily: 'PingFangSC-Semibold',
                  fontSize: 12,
                  lineHeight: '17px',
                  fontWeight: "bold",
                },
                { color: '#00A0E9' },
              ],
            },
          ],
        },
      },
    },
    {
      type: SesamePageLocation.packageIncludes,
      data,
      expected: {"title":"免押金服务","content":"芝麻信用≥650分有机会可享","btnText":"去授权"},
    },
    {
      type: SesamePageLocation.creditRent,
      data,
      expected: {"content":[{"contentStyle":"1","stringObjs":[{"content":"授权免押时，有补足资金","style":"1","url":"EhiRefundIntroModal"}]}],"btnText":"","isEhiFund":false,"isLimit":false,"isEhiLack":false,"items":[],"complementaryAmount":100},
    },
    {
      type: SesamePageLocation.creditRent,
      data: {},
      expected: null,
    },
    {
      type: 999,
      data,
      expected: null,
    },
  ];
  test.each(mockData)('Sesame Mappers getSesameBarTexts', ({type, data, expected }) => {
    jest.spyOn(ProductSelectors, "getSesameInfo").mockImplementation(() => (data));
    const result = getSesameBarTexts(type);
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getDetailSesameBarTexts', () => {
  const res : any= {
    isSupportZhima: false,
  };
  const mockData = [
    {
      expected: {
        isSupportZhima: false,
      },
    },
  ];
  jest.spyOn(SesameResponse, "getDetailSesameBarTexts").mockImplementation(() => res);
  test.each(mockData)('Sesame Mappers getDetailSesameBarTexts', ({ expected }) => {
    const result = getDetailSesameBarTexts();
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getSesameTrackInfo', () => {
  const res : any= {
    isSupportZhima: true,
  };
  const mockData = [
    {
      expected: {
        isSupportZhima: true,
      },
    },
  ];

  test.each(mockData)('Sesame Mappers getSesameTrackInfo', ({ expected }) => {
    jest.spyOn(SesameResponse, "getTrackInfo").mockImplementation(() => res);
    const result = getSesameTrackInfo();
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getUserCreditStatus', () => {
  const mockRes : any= {
    userCreditStatus: true,
  };
  const mockData = [
    {
      mockRes,
      expected: true,
    },
    {
      mockRes: {},
      expected: undefined,
    },
    {
      mockRes: undefined,
      expected: undefined,
    },
  ];

  test.each(mockData)('Sesame Mappers getUserCreditStatus', ({ mockRes, expected }) => {
    jest.spyOn(SesameResponse, "getTrackInfo").mockImplementation(() => mockRes);
    const result = getUserCreditStatus();
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getIsSupportSesame', () => {
  const mockRes : any= {
    isSupportZhima: true,
  };
  const mockData = [
    {
      mockRes,
      expected: true,
    },
    {
      mockRes: {},
      expect: undefined,
    }
  ];

  test.each(mockData)('Sesame Mappers getIsSupportSesame', ({ mockRes, expected }) => {
    jest.spyOn(ProductSelectors, "getSesameInfo").mockImplementation(() => mockRes);
    const result = getIsSupportSesame();
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers geZhimaNoteInfo', () => {
  const mockRes : any= {
    noteInfo: 345,
    complementaryAmount: 999,
  };
  const mockData = [
    {
      mockRes,
      expected: 345,
    },
    {
      mockRes: {},
      expected: {},
    }
  ];

  test.each(mockData)('Sesame Mappers geZhimaNoteInfo', ({ mockRes, expected }) => {
    jest.spyOn(ProductSelectors, "getSesameInfo").mockImplementation(() => mockRes);
    const result = geZhimaNoteInfo();
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getZhimaComplementaryAmount', () => {
  const mockRes : any= {
    noteInfo: 345,
    complementaryAmount: 999,
  };
  const mockData = [
    {
      mockRes,
      expected: 999,
    },
    {
      mockRes: {},
      expected: undefined,
    }
  ];

  test.each(mockData)('Sesame Mappers getZhimaComplementaryAmount', ({ mockRes, expected }) => {
    jest.spyOn(ProductSelectors, "getSesameInfo").mockImplementation(() => mockRes);
    const result = getZhimaComplementaryAmount();
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getIsFreeDepositModalAvailable', () => {
  const mockData = [
    {
      authStatus: SesameState.unAuthorized,
      expected: true,
    },
    {
      authStatus: SesameState.outOfDate,
      expected: true,
    },
    {
      authStatus: SesameState.failAuthorized,
      expected: true,
    },
    {
      authStatus: SesameState.depositLack,
      expected: true,
    },
    {
      authStatus: SesameState.overmuchLimitedNums,
      expected: false,
    },
    {
      authStatus: null,
      expected: false,
    },
  ];

  test.each(mockData)('Sesame Mappers getIsFreeDepositModalAvailable', ({ authStatus, expected }) => {
    const result = getIsFreeDepositModalAvailable(authStatus);
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getIsEhiDeposit', () => {
  const state1 : any = {
    Sesame: {
      authStatus:  SesameState.unAuthorized,
    },
  };
  const state2 : any = {
    Sesame: {
      authStatus:  SesameState.outOfDate,
    },
  };
  const state3 : any = {
    Sesame: {
      authStatus:  SesameState.failAuthorized,
    },
  };
  const state4 : any = {
    Sesame: {
      authStatus:  SesameState.depositLack,
    },
  };
  const state5 : any = {
    Sesame: {
      authStatus:  SesameState.overmuchLimitedNums,
    },
  };
  const state6 : any = {
    Sesame: {
      authStatus:  SesameState.authorized,
    },
  };
  const mockRes = {
    noteInfo: [1, 2],
  };
  const mockData = [
    {
      state: state1,
      mockRes,
      expected: true,
    },
    {
      state: state2,
      mockRes,
      expected: true,
    },
    {
      state: state3,
      mockRes: {},
      expected: false,
    },
    {
      state: state4,
      mockRes: {},
      expected: false,
    },
    {
      state: state5,
      mockRes,
      expected: false,
    },
    {
      state: state6,
      mockRes,
      expected: true,
    },
  ];

  test.each(mockData)('Sesame Mappers getIsEhiDeposit', ({ state, mockRes, expected }) => {
    jest.spyOn(ProductSelectors, "getSesameInfo").mockImplementation(() => mockRes);
    const result = getIsEhiDeposit(state);
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getSesameUserTip', () => {
  const passenger = {
    "userName": "张三",
  };
  const mockRes1 = {
    isSupportZhima: true,
  };
  const mockRes2 = {
    isSupportZhima: false,
  };
  const state1 : any = {
    Sesame: {
      authStatus:  SesameState.authorized,
      userName: 'zhangsan'
    },
    DriverList: {
      passenger,
    },
  };
  const state2 : any = {
    Sesame: {
      authStatus:  SesameState.authorized,
      userName: 'zhangsan22'
    },
    DriverList: {
      passenger,
    },
  };
  const state3 : any = {
    Sesame: {
      authStatus:  SesameState.depositLack,
      userName: 'zhangsan'
    },
    DriverList: {
      passenger,
    },
  };
  const state4 : any = {
    Sesame: {
      authStatus:  SesameState.depositLack,
      userName: 'zhangsan'
    },
    DriverList: {
      passenger: {},
    },
  };
  const state5 : any = {
    Sesame: {
      authStatus:  SesameState.depositLack,
      userName: 'zhangsan'
    },
    DriverList: {
      passenger: {
        "userName": "zhangsan",
      },
    },
  };
  const mockData = [
    {
      state: state1,
      mockRes: mockRes1,
      isCtripIsd: true,
      expected: true,
    },
    {
      state: state2,
      mockRes: mockRes2,
      isCtripIsd: true,
      expected: false,
    },
    {
      state: state3,
      mockRes: mockRes1,
      isCtripIsd: true,
      expected: false,
    },
    {
      state: state4,
      mockRes: mockRes1,
      isCtripIsd: true,
      expected: false,
    },
    {
      state: state5,
      mockRes: mockRes1,
      isCtripIsd: true,
      expected: false,
    },
    {
      state: state1,
      mockRes: mockRes1,
      isCtripIsd: false,
      expected: false,
    },
  ];

  test.each(mockData)('Sesame Mappers getSesameUserTip', ({ state, mockRes, isCtripIsd, expected }) => {
    jest.spyOn(ProductSelectors, "getSesameInfo").mockImplementation(() => mockRes);
    jest.spyOn(Utils, "isCtripIsd").mockImplementation(() => isCtripIsd);
    const result = getSesameUserTip(state);
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getDepositLabelType', () => {
  const state : any = {};
  const state1 : any = { "test" : 1};
  const mockRes = {
    depositLabel: {
      type: 5,
    },
  };
  const mockData = [
    {
      state,
      mockRes,
      expected: 5,
    },
    {
      state: state1,
      mockRes: {},
      expected: undefined,
    },
  ];

  test.each(mockData)('Sesame Mappers getDepositLabelType', ({ state, mockRes, expected }) => {
    jest.spyOn(ProductReqAndResData, "getData").mockImplementation(() => mockRes);
    const result = getDepositLabelType(state);
    expect(result).toEqual(expected);
  })
});

// jest.mock('../../../src/pages/xcar/ComponentBusiness/Index', () => ({
//   Enums: {
//     DepositLabelType: {
//       CtripCreditRent: 1,
//     },
//   }
// }));
describe('Sesame Mappers isCtripCreditRent', () => {
  const state : any = {};
  const state1 : any = { "test" : 1};
  const mockRes1 = {
    depositLabel: {
      type: 1,
    },
  };
  const mockRes2 = {
    depositLabel: {
      type: 2,
    },
  };
  const mockData = [
    {
      state,
      mockRes: mockRes1,
      expected: true,
    },
    {
      state: state1,
      mockRes: mockRes2,
      expected: false,
    },
  ];

  test.each(mockData)('Sesame Mappers isCtripCreditRent', ({ state, mockRes, expected }) => {
    jest.spyOn(ProductReqAndResData, "getData").mockImplementation(() => mockRes);
    const result = isCtripCreditRent(state);
    expect(result).toEqual(expected);
  })
});


describe('Sesame Mappers isCreditQualified', () => {
  const passenger = {
    "fullName": "zhangsan",
  };
  const sesameInfo = {
    isSupportZhima: true,
  };
  const sesameInfo1 = {
    isSupportZhima: false,
  };
  const state : any = {
    Sesame: {
      authStatus: SesameState.authorized,
      userName: "zhangsan",
    },
    DriverList: {
      passenger,
    },
  };
  const state1 : any = {
    Sesame: {
      authStatus: SesameState.authorized,
      userName: "zhangsan",
      temp: 1,
    },
    DriverList: {
      passenger,
    },
  };
  const state2 : any = {
    Sesame: {
      authStatus: SesameState.depositLack,
      userName: "zhangsan",
    },
    DriverList: {
      passenger,
    },
  };
  const state3 : any = {
    Sesame: {
      authStatus: SesameState.authorized,
      userName: "wangwu",
    },
    DriverList: {
      passenger,
    },
  };
  const mockData = [
    {
      state,
      sesameInfo,
      expected: true,
    },
    {
      state: state1,
      sesameInfo: sesameInfo1,
      expected: false,
    },
    {
      state: state1,
      sesameInfo: {},
      expected: false,
    },
    {
      state: state2,
      sesameInfo,
      expected: false,
    },
    {
      state: state3,
      sesameInfo,
      expected: false,
    },
  ];

  test.each(mockData)('Sesame Mappers isCreditQualified', ({ state, sesameInfo, expected }) => {
    jest.spyOn(ProductSelectors, "getSesameInfo").mockImplementation(() => (sesameInfo));
    const result = isCreditQualified(state);
    expect(result).toEqual(expected);
  })
});

describe('Sesame Mappers getSesameOrderId', () => {
  const resData : any = {
    alipayAuthInfo: {
      orderId: "34534534645"
    },
  };
  const mockData = [
    {
      resData,
      expected: "34534534645",
    },
    {
      resData: {},
      expected: "",
    },
  ];

  test.each(mockData)('Sesame Mappers getSesameOrderId', ({ resData, expected }) => {
    jest.spyOn(SesameResponse, "getData").mockImplementation(() => (resData));
    const result = getSesameOrderId();
    expect(result).toEqual(expected);
  })
});
