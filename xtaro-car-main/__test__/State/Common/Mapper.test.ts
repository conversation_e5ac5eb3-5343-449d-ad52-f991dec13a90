import {
  getImAddressWithQconfig,
  getLimitInfo,
  getListWarningInfoParams,
  getListWarningInfoRes,
  mappingLimitInfo,
} from '../../../src/pages/xcar/State/Common/Mapper';
import {
  UngencyLevel,
  WarningTypes,
} from '../../../src/pages/xcar/ComponentBusiness/Common/src/Enums';
import AppContext from '../../../src/pages/xcar/Util/AppContext';
import { APP_TYPE } from '../../../src/pages/xcar/Constants/Platform';

jest.mock('../../../src/pages/xcar/Util/Index', () => {
  return {
    AppContext: {
      LanguageInfo: {
        standardLocale: '',
      },
    },
    Utils: {
      compatImgUrlWithWebp: data => data,
      isCtripIsd: () => true,
    },
  };
});

jest.mock('../../../src/pages/xcar/Global/Cache/ListResSelectors', () => ({
  getLimitRuleData: () => ({
    baseResponse: {
      isSuccess: true,
    },
    limitTitle: '限行标题',
    limitInfos: [
      {
        limitContents: [
          {
            module: 1,
            title: 'test',
          },
        ],
      },
    ],
  }),
}));

describe('Common Mapper getListWarningInfoParams', () => {
  test('getListWarningInfoParams', () => {
    const pageIndexId = 111;
    const pickUpTime = '2022-08-25 20:00:00';
    const dropOffTime = '2022-08-26 20:00:00';
    const pickUpCityId = 206;
    const dropOffCityId = 206;
    const targetData = {
      pageIndexId,
      businessLineId: 14,
      searchParamDto: {
        salesModel: '',
        startUsingDate: '2022-08-25',
        endUsingDate: '2022-08-26',
        productType: '',
        productCategoryList: [],
        startPlaceList: [
          {
            geocategoryid: '3',
            globalid: '206',
          },
        ],
        endPlace: [
          {
            geocategoryid: '3',
            globalid: '206',
          },
        ],
      },
      startUsingDate: pickUpTime,
      endUsingDate: dropOffTime,
      startPlace: {
        geocategoryid: 3,
        globalid: pickUpCityId,
      },
      endPlace: {
        geocategoryid: 3,
        globalid: dropOffCityId,
      },
      isOneway: true,
    };
    expect(
      getListWarningInfoParams({}, 111, {
        pickUpTime,
        dropOffTime,
        pickUpCityId,
        dropOffCityId,
      }),
    ).toEqual(targetData);
  });
});

describe('Common Mapper getListWarningInfoRes', () => {
  const mockRes = {
    data: [
      {
        urgencyLevel: UngencyLevel.High,
        warningTypeList: [WarningTypes.DisClosureOnFront],
      },
    ],
  };
  const expected = { data: [mockRes.data[0]] };
  const mockDataMap = [
    {
      mockData: mockRes,
      expected,
    },
    {
      mockData: null,
      expected: { data: [] },
    },
    {
      mockData: {
        data: [
          {
            urgencyLevel: UngencyLevel.High,
          },
        ],
      },
      expected: { data: [] },
    },
  ];
  test.each(mockDataMap)('getListWarningInfoRes', ({ mockData, expected }) => {
    expect(getListWarningInfoRes(mockData)).toEqual(expected);
  });
});

describe('Common Mapper getLimitInfo', () => {
  test('getLimitInfo', () => {
    const expected = {
      title: 'test',
      limitInfos: [
        {
          limitContents: [
            {
              module: 1,
              title: 'test',
            },
          ],
        },
      ],
      displayTitle: '限行标题',
    };
    const res = getLimitInfo();
    expect(res).toEqual(expected);
  });
});

describe('Common Mapper mappingLimitInfo', () => {
  const limitTitle = '限行标题';
  const limitInfos = [
    {
      limitContents: [
        {
          module: 1,
          title: 'test',
        },
      ],
    },
  ];
  const mockData = {
    baseResponse: {
      isSuccess: true,
    },
    limitTitle,
    limitInfos,
  };
  const mockDataMap = [
    {
      mockData,
      expected: {
        title: 'test',
        limitInfos,
        displayTitle: limitTitle,
      },
    },
    {
      mockData: {
        baseResponse: {
          isSuccess: false,
        },
      },
      expected: null,
    },
  ];
  test.each(mockDataMap)('mappingLimitInfo', ({ mockData, expected }) => {
    const res = mappingLimitInfo(mockData);
    expect(res).toEqual(expected);
  });
});

describe('Common Mapper getImAddressWithQconfig', () => {
  const mockDataMap = [
    {
      state: {
        Common: {
          isShowIm: false,
        },
      },
      expected: '',
    },
  ];
  test.each(mockDataMap)('getImAddressWithQconfig', ({ state, expected }) => {
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP })
    const res = getImAddressWithQconfig(state, {
      pageId: '222013',
      isPreSale: 1,
    });
    expect(res).toEqual(expected);
  });
});
