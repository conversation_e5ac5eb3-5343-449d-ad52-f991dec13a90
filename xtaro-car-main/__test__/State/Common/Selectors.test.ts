import {
  getUrl,
  getHomeWaringInfo,
  getHomeFetchWarningInfoLoading,
  getPhoneModalVisible,
  getQConfig,
  getLastOrderCacheId,
  getListWaringInfo,
  getProductWaringInfo,
  getOrderWaringInfo,
  getFetchWarningInfoLoading,
  getIsShowIm,
} from '../../../src/pages/xcar/State/Common/Selectors';

const testWaningInfo = {
  warningDtos: [
    {
      warningRichText:
        '<p>       当前国际新冠肺炎疫情形势复杂，国内仍存在本土疫情，为严格贯彻“外防输入、内防反弹”总策略和“动态清零”总方针，现针对所有人员流动活动做以下重要提醒，请您预订前务必关注并严格执行，谢谢~<br />\n1.请您预订及出行前务必仔细查阅出发地及目的地当时最新的疫情防控政策措施（<a href="http://www.gov.cn/zhuanti/2021yqfkgdzc/index.htm#/">中国政府网的各地疫情防控政策措施</a>）或致电当地疫情防控部门咨询电话了解最新疫情防控政策并严格遵照执行，合理规划行程。坚决不前往中、高风险地区所在地市旅游，中、高风险地区所在地及有旅居史的游客非必要不出行。坚决执行《文化和旅游部办公厅关于加强疫情防控科学精准实施跨省旅游“熔断”机制的通知》要求，确保不预订行程目的地涉及中高风险地区的县（市、区、旗）和直辖市的区（县）的团队及“机票+酒店”行程。</p>\n\n<p>2.以下人员请勿跨省流动，请务必特别注意，以免受到出行遇阻或到目的地自费隔离管控、被赋红码等影响<br />\n2.1 健康码红黄码及健康码弹窗人员。<br />\n2.2 来自或14天内具有中高风险地区及其所在县（市、区、旗）及乡镇（街道）旅居史人员。<br />\n2.3 当地出现1例溯源不清或来源不明的本土新增病例的地区及其所在县（市、区、旗）及乡镇（街道)人员或14天内有该地旅居史人员。<br />\n2.4 来自省外陆地边境口岸城市人员。<br />\n2.5 定点医院、集中隔离点、冷链食品从业人员等从事高风险岗位人员。<br />\n2.6 其他各地疫情管控政策明确禁止流动人员。<br />\n3.若您符合出发地及目的疫情防控要求可以顺利出行，出行期间请您务必做好健康防护。自觉遵守疫情防控规定，坚持“防疫三件套”（佩戴口罩、社交距离、个人卫生），“防护五还要”(口罩还要戴、社交距离还要留、咳嗽喷嚏还要遮、双手还要经常洗、窗户还要尽量开)。游览时自觉与其他游客保持间距，避免扎堆，减少触摸公共部位，不触碰口鼻眼。咳嗽、打喷嚏时，注意用肘部或纸巾遮掩，不随地吐痰。出现感冒、发热等症状时，应停止游览并及时就医。同时严格遵守文明公约。遵守《中国公民国内旅游文明行为公约》，自觉遵守社会公序良俗，爱护生态环境和文物古迹，厉行节约、制止餐饮浪费，拒食野味，减少一次性物品使用，避免使用不可降解塑料制品，树立文明、健康、绿色旅游新风尚，做文明的旅游者。</p>\n',
      urgencyType: 1,
      urgencyLevel: 2,
      isStartPlace: true,
      warningTitle: '关于疫情常态化下出行重要信息提醒',
      warningContent:
        '       当前国际新冠肺炎疫情形势复杂，国内仍存在本土疫情，为严格贯彻“外防输入、内防反弹”总策略和“动态清零”总方针，现针对所有人员流动活动做以下重要提醒，请您预订前务必关注并严格执行，谢谢~\n1.请您预订及出行前务必仔细查阅出发地及目的地当时最新的疫情防控政策措施（中国政府网的各地疫情防控政策措施：http://www.gov.cn/zhuanti/2021yqfkgdzc/index.htm#/）或致电当地疫情防控部门咨询电话了解最新疫情防控政策并严格遵照执行，合理规划行程。坚决不前往中、高风险地区所在地市旅游，中、高风险地区所在地及有旅居史的游客非必要不出行。坚决执行《文化和旅游部办公厅关于加强疫情防控科学精准实施跨省旅游“熔断”机制的通知》要求，确保不预订行程目的地涉及中高风险地区的县（市、区、旗）和直辖市的区（县）的团队及“机票+酒店”行程。\n2.以下人员请勿跨省流动，请务必特别注意，以免受到出行遇阻或到目的地自费隔离管控、被赋红码等影响\n2.1 健康码红黄码及健康码弹窗人员。\n2.2 来自或14天内具有中高风险地区及其所在县（市、区、旗）及乡镇（街道）旅居史人员。\n2.3 当地出现1例溯源不清或来源不明的本土新增病例的地区及其所在县（市、区、旗）及乡镇（街道)人员或14天内有该地旅居史人员。\n2.4 来自省外陆地边境口岸城市人员。\n2.5 定点医院、集中隔离点、冷链食品从业人员等从事高风险岗位人员。\n2.6 其他各地疫情管控政策明确禁止流动人员。\n3.若您符合出发地及目的疫情防控要求可以顺利出行，出行期间请您务必做好健康防护。自觉遵守疫情防控规定，坚持“防疫三件套”（佩戴口罩、社交距离、个人卫生），“防护五还要”(口罩还要戴、社交距离还要留、咳嗽喷嚏还要遮、双手还要经常洗、窗户还要尽量开)。游览时自觉与其他游客保持间距，避免扎堆，减少触摸公共部位，不触碰口鼻眼。咳嗽、打喷嚏时，注意用肘部或纸巾遮掩，不随地吐痰。出现感冒、发热等症状时，应停止游览并及时就医。同时严格遵守文明公约。遵守《中国公民国内旅游文明行为公约》，自觉遵守社会公序良俗，爱护生态环境和文物古迹，厉行节约、制止餐饮浪费，拒食野味，减少一次性物品使用，避免使用不可降解塑料制品，树立文明、健康、绿色旅游新风尚，做文明的旅游者。',
    },
  ],
  BaseResponse: {
    IsSuccess: true,
    ReturnMsg: 'success',
    Cost: 7,
    Code: '200',
    RequestId: '39cfb9ad-4747-4eb0-ac90-993726c72fac',
  },
};

const listWaringInfo = {
  baseResponse: {
    isSuccess: true,
    code: '200',
    returnMsg: 'success',
    requestId: '',
    cost: 0,
  },
  warningDtos: [
    {
      isStartPlace: true,
      urgencyType: 1,
      urgencyLevel: 2,
      warningTitle: '关于疫情常态化下出行重要信息提醒',
      warningContent:
        '       温馨提醒您在预订及出行前务必及时查阅您常驻地的疫情风险级别并关注自身身体情况，确保不来自、不前往高风险地区进行旅游活动；确保具备与旅游活动相关的个人健康；确保健康码绿码。出行期间请您务必做好健康防护，时刻关注好自身健康，携程旅游会尽力为您提供安全周到的服务，谢谢。',
      warningRichText:
        '<p>&nbsp; &nbsp; &nbsp; &nbsp;温馨提醒您在预订及出行前务必及时查阅您常驻地的疫情风险级别并关注自身身体情况，确保不来自、不前往高风险地区进行旅游活动；确保具备与旅游活动相关的个人健康；确保健康码绿码。出行期间请您务必做好健康防护，时刻关注好自身健康，携程旅游会尽力为您提供安全周到的服务，谢谢。</p>\n',
    },
  ],
};

const productWaringInfo = {
  warningDtos: [
    {
      isStartPlace: true,
      urgencyType: 1,
      urgencyLevel: 2,
      warningTitle: '关于疫情常态化下出行重要信息提醒',
      warningContent:
        '       温馨提醒您在预订及出行前务必及时查阅您常驻地的疫情风险级别并关注自身身体情况，确保不来自、不前往高风险地区进行旅游活动；确保具备与旅游活动相关的个人健康；确保健康码绿码。出行期间请您务必做好健康防护，时刻关注好自身健康，携程旅游会尽力为您提供安全周到的服务，谢谢。',
      warningRichText:
        '<p>&nbsp; &nbsp; &nbsp; &nbsp;温馨提醒您在预订及出行前务必及时查阅您常驻地的疫情风险级别并关注自身身体情况，确保不来自、不前往高风险地区进行旅游活动；确保具备与旅游活动相关的个人健康；确保健康码绿码。出行期间请您务必做好健康防护，时刻关注好自身健康，携程旅游会尽力为您提供安全周到的服务，谢谢。</p>\n',
    },
  ],
  baseResponse: {
    isSuccess: true,
    code: '200',
    returnMsg: 'success',
    requestId: '',
    cost: 0,
  },
};

const orderWaringInfo = {
  warningDtos: [
    {
      isStartPlace: true,
      urgencyType: 1,
      urgencyLevel: 2,
      warningTitle: '关于疫情常态化下出行重要信息提醒',
      warningContent:
        '       温馨提醒您在预订及出行前务必及时查阅您常驻地的疫情风险级别并关注自身身体情况，确保不来自、不前往高风险地区进行旅游活动；确保具备与旅游活动相关的个人健康；确保健康码绿码。出行期间请您务必做好健康防护，时刻关注好自身健康，携程旅游会尽力为您提供安全周到的服务，谢谢。',
      warningRichText:
        '<p>&nbsp; &nbsp; &nbsp; &nbsp;温馨提醒您在预订及出行前务必及时查阅您常驻地的疫情风险级别并关注自身身体情况，确保不来自、不前往高风险地区进行旅游活动；确保具备与旅游活动相关的个人健康；确保健康码绿码。出行期间请您务必做好健康防护，时刻关注好自身健康，携程旅游会尽力为您提供安全周到的服务，谢谢。</p>\n',
    },
  ],
  baseResponse: {
    isSuccess: true,
    code: '200',
    returnMsg: 'success',
    requestId: '',
    cost: 0,
  },
};

const testFetchWarningInfoLoading = {
  loading: false,
  error: false,
  showLoading: false,
};

const testMockState = {
  Common: {
    imUrl: '测试imUrl',
    homeWaringInfo: testWaningInfo,
    homefetchWarningInfoLoading: true,
    phoneModalVisible: true,
    qConfigResponse: {
      vendorListTangramEntrance: true,
      hideZhima: false,
      isShowIm: false,
    },
    lastOrderCacheId: '123456',
    listWaringInfo,
    productWaringInfo,
    orderWaringInfo,
    fetchWarningInfoLoading: testFetchWarningInfoLoading,
  },
};

describe('CommonReducer Selectors getUrl', () => {
  test('getUrl', () => {
    expect(getUrl(testMockState)).toEqual('测试imUrl');
  });
});

describe('CommonReducer Selectors getHomeWaringInfo', () => {
  test('getHomeWaringInfo', () => {
    expect(getHomeWaringInfo(testMockState)).toEqual(testWaningInfo);
  });
});

describe('CommonReducer Selectors getHomeFetchWarningInfoLoading', () => {
  test('getHomeFetchWarningInfoLoading', () => {
    expect(getHomeFetchWarningInfoLoading(testMockState)).toEqual(true);
  });
});

describe('CommonReducer Selectors getPhoneModalVisible', () => {
  test('getPhoneModalVisible', () => {
    expect(getPhoneModalVisible(testMockState)).toEqual(true);
  });
});

describe('CommonReducer Selectors getQConfig', () => {
  const expected = {
    vendorListTangramEntrance: true,
    hideZhima: false,
    isShowIm: false,
  };
  test('getQConfig', () => {
    expect(getQConfig(testMockState)).toEqual(expected);
  });
});

describe('CommonReducer Selectors getLastOrderCacheId', () => {
  test('getLastOrderCacheId', () => {
    expect(getLastOrderCacheId(testMockState)).toEqual('123456');
  });
});

describe('CommonReducer Selectors getListWaringInfo', () => {
  test('getListWaringInfo', () => {
    expect(getListWaringInfo(testMockState)).toEqual(listWaringInfo);
  });
});

describe('CommonReducer Selectors getProductWaringInfo', () => {
  test('getListWaringInfo', () => {
    expect(getProductWaringInfo(testMockState)).toEqual(productWaringInfo);
  });
});

describe('CommonReducer Selectors getOrderWaringInfo', () => {
  test('getListWaringInfo', () => {
    expect(getOrderWaringInfo(testMockState)).toEqual(orderWaringInfo);
  });
});

describe('CommonReducer Selectors getFetchWarningInfoLoading', () => {
  test('getListWaringInfo', () => {
    expect(getFetchWarningInfoLoading(testMockState)).toEqual(
      testFetchWarningInfoLoading,
    );
  });
});

describe('CommonReducer Selectors getIsShowIm', () => {
  test('getIsShowIm', () => {
    expect(getIsShowIm(testMockState)).toEqual(false);
  });
});
