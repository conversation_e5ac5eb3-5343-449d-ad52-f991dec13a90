import { WarningInfo } from '../../../src/pages/xcar/Constants/Index';
import CommonReducer, {
  getInitalState,
} from '../../../src/pages/xcar/State/Common/Reducer';

import {
  FETCH_ISD_IMURL_CALLBACK,
  FETCH_QCONFIG_CALLBACK,
  FETCH_LISTWARNINGINFO_CALLBACK,
  FETCH_WARNINGINFO_START,
  SET_PHONE_MODAL_VISIBLE,
  FETCH_LISTWARNINGINFO_CLEAR,
  FETCH_ISD_IMURL,
  FETCH_QCONFIG,
  FETCH_LASTORDER,
  FETCH_LISTWARNINGINFO,
} from '../../../src/pages/xcar/State/Common/Types';

describe('Common Reducer Test', () => {
  const initState = getInitalState();

  // 验证初始化
  test('Common Reducer Init', () => {
    expect(CommonReducer(undefined, {})).toEqual(initState);
  });

  describe('Common Reducer FETCH_ISD_IMURL_CALLBACK', () => {
    test('FETCH_ISD_IMURL_CALLBACK', () => {
      expect(
        CommonReducer(initState, {
          type: FETCH_ISD_IMURL_CALLBACK,
          data: 'imUrl',
        }),
      ).toEqual({
        ...initState,
        imUrl: 'imUrl',
      });
    });
  });

  describe('Common Reducer FETCH_QCONFIG_CALLBACK', () => {
    test('FETCH_QCONFIG_CALLBACK', () => {
      expect(
        CommonReducer(initState, {
          type: FETCH_QCONFIG_CALLBACK,
          data: {},
        }),
      ).toEqual({
        ...initState,
        qConfigResponse: {},
      });
    });
  });

  describe('Common Reducer FETCH_LISTWARNINGINFO_CALLBACK', () => {
    const res = {
      warningDtos: [
        {
          isStartPlace: true,
          urgencyType: 1,
          urgencyLevel: 2,
          warningTitle: '关于疫情常态化下出行重要信息提醒',
          warningContent:
            '       温馨提醒您在预订及出行前务必及时查阅您常驻地的疫情风险级别并关注自身身体情况，确保不来自、不前往高风险地区进行旅游活动；确保具备与旅游活动相关的个人健康；确保健康码绿码。出行期间请您务必做好健康防护，时刻关注好自身健康，携程旅游会尽力为您提供安全周到的服务，谢谢。',
          warningRichText:
            '<p>&nbsp; &nbsp; &nbsp; &nbsp;温馨提醒您在预订及出行前务必及时查阅您常驻地的疫情风险级别并关注自身身体情况，确保不来自、不前往高风险地区进行旅游活动；确保具备与旅游活动相关的个人健康；确保健康码绿码。出行期间请您务必做好健康防护，时刻关注好自身健康，携程旅游会尽力为您提供安全周到的服务，谢谢。</p>\n',
        },
      ],
      baseResponse: {
        isSuccess: true,
        code: '200',
        returnMsg: 'success',
        requestId: '',
        cost: 0,
      },
    };
    const fetchWarningInfoLoading = {
      loading: false,
      error: false,
    };
    const mockDataMap = [
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.Home,
          res,
          fetchWarningInfoLoading,
        },
        expected: {
          ...initState,
          homeWaringInfo: res,
          homefetchWarningInfoLoading: fetchWarningInfoLoading,
        },
      },
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.List,
          res,
          fetchWarningInfoLoading,
        },
        expected: {
          ...initState,
          listWaringInfo: res,
          fetchWarningInfoLoading,
        },
      },
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.Product,
          res,
          fetchWarningInfoLoading,
        },
        expected: {
          ...initState,
          productWaringInfo: res,
          fetchWarningInfoLoading,
        },
      },
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.Order,
          res,
          fetchWarningInfoLoading,
        },
        expected: {
          ...initState,
          orderWaringInfo: res,
          fetchWarningInfoLoading,
        },
      },
      {
        mockData: {
          pageIndexId: '',
        },
        expected: initState,
      },
    ];
    test.each(mockDataMap)(
      'FETCH_LISTWARNINGINFO_CALLBACK',
      ({ mockData, expected }) => {
        expect(
          CommonReducer(initState, {
            type: FETCH_LISTWARNINGINFO_CALLBACK,
            data: mockData,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('Common Reducer FETCH_WARNINGINFO_START', () => {
    const mockFetchWarningInfoLoading = {
      loading: true,
      error: false,
      showLoading: true,
    };
    const mockDataMap = [
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.Home,
          fetchWarningInfoLoading: mockFetchWarningInfoLoading,
        },
        expected: {
          ...initState,
          homefetchWarningInfoLoading: mockFetchWarningInfoLoading,
        },
      },
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.List,
          fetchWarningInfoLoading: mockFetchWarningInfoLoading,
        },
        expected: {
          ...initState,
          fetchWarningInfoLoading: mockFetchWarningInfoLoading,
        },
      },
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.Product,
          fetchWarningInfoLoading: mockFetchWarningInfoLoading,
        },
        expected: {
          ...initState,
          fetchWarningInfoLoading: mockFetchWarningInfoLoading,
        },
      },
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.Order,
          fetchWarningInfoLoading: mockFetchWarningInfoLoading,
        },
        expected: {
          ...initState,
          fetchWarningInfoLoading: mockFetchWarningInfoLoading,
        },
      },
      {
        mockData: {
          pageIndexId: '',
        },
        expected: initState,
      },
    ];
    test.each(mockDataMap)(
      'FETCH_WARNINGINFO_START',
      ({ mockData, expected }) => {
        expect(
          CommonReducer(initState, {
            type: FETCH_WARNINGINFO_START,
            data: mockData,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('Common Reducer SET_PHONE_MODAL_VISIBLE', () => {
    test('SET_PHONE_MODAL_VISIBLE', () => {
      expect(
        CommonReducer(initState, {
          type: SET_PHONE_MODAL_VISIBLE,
          data: {
            visible: true,
          },
        }),
      ).toEqual({
        ...initState,
        phoneModalVisible: true,
      });
    });
  });

  // describe('Common Reducer FETCH_LASTORDER_CALLBACK', () => {
  //   test('FETCH_LASTORDER_CALLBACK', () => {
  //     expect(
  //       CommonReducer(initState, {
  //         type: FETCH_LASTORDER_CALLBACK,
  //         data: {},
  //       }),
  //     ).toEqual({
  //       ...initState,
  //       qConfigResponse: {},
  //     });
  //   });
  // });

  describe('Common Reducer FETCH_LISTWARNINGINFO_CLEAR', () => {
    const mockDataMap = [
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.Home,
        },
        expected: {
          ...initState,
          homeWaringInfo: {},
        },
      },
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.List,
        },
        expected: {
          ...initState,
          listWaringInfo: {},
        },
      },
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.Product,
        },
        expected: {
          ...initState,
          productWaringInfo: {},
        },
      },
      {
        mockData: {
          pageIndexId: WarningInfo.PageIndexId.Order,
        },
        expected: {
          ...initState,
          orderWaringInfo: {},
        },
      },
      {
        mockData: {
          pageIndexId: '',
        },
        expected: {
          ...initState,
        },
      },
    ];
    test.each(mockDataMap)(
      'FETCH_LISTWARNINGINFO_CLEAR',
      ({ mockData, expected }) => {
        expect(
          CommonReducer(initState, {
            type: FETCH_LISTWARNINGINFO_CLEAR,
            data: mockData,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('Common Reducer FETCH_ISD_IMURL', () => {
    test('FETCH_ISD_IMURL', () => {
      expect(
        CommonReducer(initState, {
          type: FETCH_ISD_IMURL,
        }),
      ).toEqual({
        ...initState,
      });
    });
  });

  describe('Common Reducer FETCH_QCONFIG', () => {
    test('FETCH_QCONFIG', () => {
      expect(
        CommonReducer(initState, {
          type: FETCH_QCONFIG,
        }),
      ).toEqual({
        ...initState,
      });
    });
  });

  describe('Common Reducer FETCH_LASTORDER', () => {
    test('FETCH_LASTORDER', () => {
      expect(
        CommonReducer(initState, {
          type: FETCH_LASTORDER,
        }),
      ).toEqual({
        ...initState,
      });
    });
  });

  describe('Common Reducer FETCH_LISTWARNINGINFO', () => {
    test('FETCH_LISTWARNINGINFO', () => {
      expect(
        CommonReducer(initState, {
          type: FETCH_LISTWARNINGINFO,
        }),
      ).toEqual({
        ...initState,
      });
    });
  });

  describe('Common Reducer Default', () => {
    test('Default', () => {
      expect(
        CommonReducer(initState, {
          type: null,
        }),
      ).toEqual({
        ...initState,
      });
    });
  });
});
