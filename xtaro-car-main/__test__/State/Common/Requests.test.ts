import { WarningInfo } from '../../../src/pages/xcar/Constants/Index';
import { CommonReqAndResData } from '../../../src/pages/xcar/Global/Cache/Index';
import getWarningListParams from '../../../src/pages/xcar/State/Common/Requests';

describe('Common Requests', () => {
  const mockStorageData = { test: 'test' };
  const locationAndDate = {
    pickUpTime: '2022-12-28 14:00:00',
    dropOffTime: '2022-12-29 14:00:00',
    pickUpCityId: 1,
    dropOffCityId: 1,
  };
  const state = {
    LocationAndDate: {
      rentalLocation: {
        pickUp: {
          cid: 1,
        },
        dropOff: {
          cid: 1,
        },
      },
      rentalDate: {
        pickUp: {
          dateTime: '2022-12-28 14:00:00',
        },
        dropOff: {
          dateTime: '2022-12-29 14:00:00',
        },
      },
    },
  };
  const mockData = {
    pageIndexId: WarningInfo.PageIndexId.Order,
    locationAndDate,
  };
  const parameterRes = {
    locale: undefined,
    pageIndexId: 12004,
    businessLineId: 14,
    searchParamDto: {
      salesModel: '',
      startUsingDate: '2022-12-28',
      endUsingDate: '2022-12-29',
      productType: '',
      productCategoryList: [],
      startPlaceList: [{ geocategoryid: '3', globalid: '1' }],
      endPlace: [{ geocategoryid: '3', globalid: '1' }],
    },
    startUsingDate: '2022-12-28 14:00:00',
    endUsingDate: '2022-12-29 14:00:00',
    startPlace: { geocategoryid: 3, globalid: 1 },
    endPlace: { geocategoryid: 3, globalid: 1 },
    isOneway: true,
  };
  const mockDataMap = [
    {
      param: { mockData, state: {} },
      expected: {
        mockStorageData,
        parameterRes,
        mockData,
      },
    },
    {
      param: { mockData: undefined, state },
      expected: {
        mockStorageData,
        parameterRes: { ...parameterRes, pageIndexId: undefined },
        mockData: {},
      },
    },
    {
      param: {
        mockData: { pageIndexId: WarningInfo.PageIndexId.Order },
        state,
      },
      expected: {
        mockStorageData,
        parameterRes,
        mockData: { pageIndexId: WarningInfo.PageIndexId.Order },
      },
    },
  ];
  test.each(mockDataMap)('getWarningListParams', ({ param, expected }) => {
    CommonReqAndResData.setData(
      JSON.stringify(expected.parameterRes),
      mockStorageData,
    );
    const { storageRes, parameter, data } = getWarningListParams(
      { data: param.mockData },
      param.state,
    );
    expect(storageRes).toEqual(expected.mockStorageData);
    expect(parameter).toEqual(expected.parameterRes);
    expect(data).toEqual(expected.mockData);
  });
});
