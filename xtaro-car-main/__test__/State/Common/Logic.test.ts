import { recordSaga } from '../../testHelpers';
import { queryOrderNumber } from '../../../src/pages/xcar/State/Common/Logic';
import { FETCH_ODERNUMBER, FETCH_ODERNUMBER_CALLBACK } from '../../../src/pages/xcar/State/Common/Types';
import { AppContext, CarLog, User, EventHelper } from '../../../src/pages/xcar/Util/Index';
import {
  FETCH_ISD_IMURL,
  FETCH_ISD_IMURL_CALLBACK,
  FETCH_LASTORDER,
  FETCH_LASTORDER_CALLBACK,
  FETCH_LASTORDER_CALLBACK_TASK,
  FETCH_LISTWARNINGINFO,
  FETCH_LISTWARNINGINFO_CALLBACK,
  FETCH_LISTWARNINGINFO_CALLBACK_TASK,
  FETCH_LISTWARNINGINFO_CLEAR,
  FETCH_QCONFIG_CALL<PERSON>CK,
  FETCH_WARNINGINFO_START,
} from '../../../src/pages/xcar/State/Common/Types';
import {
  getIsdImURL,
  getLastOrder,
  getLastOrderCallBackTask,
  getListWarningInfo,
  getListWarningInfoCallBackTask,
  getQconfig,
} from '../../../src/pages/xcar/State/Common/Logic';
import CarFetch from '../../../src/pages/xcar/Util/CarFetch';
import { resolve } from 'path';
import { CommonReqAndResData } from '../../../src/pages/xcar/Global/Cache/Index';
import {
  EventName,
  LogKeyDev,
  WarningInfo,
} from '../../../src/pages/xcar/Constants/Index';
import { PhoneNumberType } from '../../../src/pages/xcar/Common/src/Logic/src/Order/Types/QueryOrderNumberType';

jest.mock('../../../src/pages/xcar/Util/User', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Util/User') }));

jest.mock('../../../src/pages/xcar/Util/EventHelper', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Util/EventHelper') }));

const mockRes =
{
    "baseResponse":{
            "isSuccess":false,
            "code":"string",
            "returnMsg":"string",
            "requestId":"string",
            "cost":0
        },
        "phoneNumberList":[
            {
                "number":"123456",
                "type": PhoneNumberType.origin,
                "role":1,
                "nonBusinessHoursTips":"string"
            },
            {
                "number":"123456",
                "type": PhoneNumberType.virtual,
                "role":2,
                "nonBusinessHoursTips":"string"
            },
            {
                "number":"123456",
                "type": PhoneNumberType.virtual,
                "role":3,
                "nonBusinessHoursTips":"string"
            },
            {
                "number":"123456",
                "type": PhoneNumberType.virtual,
                "role":4,
                "nonBusinessHoursTips":"string"
            }
        ]
    }
  ;
const mockRes2 =
  {
      "baseResponse":{
              "isSuccess":false,
              "code":"string",
              "returnMsg":"string",
              "requestId":"string",
              "cost":0
          },
          "phoneNumberList":[
              {
                  "number":"123456",
                  "type":'origin',
                  "role":1,
                  "nonBusinessHoursTips":"string"
              },
              {
                  "number":"123456",
                  "type":'origin',
                  "role":2,
                  "nonBusinessHoursTips":"string"
              },
              {
                  "number":"123456",
                  "type":'origin',
                  "role":3,
                  "nonBusinessHoursTips":"string"
              },
              {
                  "number":"123456",
                  "type":'origin',
                  "role":4,
                  "nonBusinessHoursTips":"string"
              }
          ]
      }
  ;
  const mockRes3 = undefined;
describe('Common logic queryOrderNumber', () => {
  const orderId = 123456;
  const testFn = async res => {
    const api = jest
      .spyOn(CarFetch, 'queryOrderNumber').mockReturnValueOnce(res);
    const dispatched = await recordSaga(queryOrderNumber, {
      action: {
        type: FETCH_ODERNUMBER,
        data: {
          orderId
        }
      },
      state:{},
    });
    return {api, dispatched};
  };

  test('测试正常调用', async () => {
    const {api, dispatched} = await testFn(mockRes);
    expect(api).toBeCalled();
    expect(dispatched[0]).toEqual({
      type: FETCH_ODERNUMBER_CALLBACK,
      data: {
        phoneNumberList: mockRes.phoneNumberList,
        fetchCompleted: true,
        orderId,
      },
    });
  });

  test('测试正常调用 接口返回都是真实号码则不用接口数据', async () => {
    const {api, dispatched} = await testFn(mockRes2);
    expect(api).toBeCalled();
    expect(dispatched[0]).toEqual({
      type: FETCH_ODERNUMBER_CALLBACK,
      data: {
        phoneNumberList: [],
        fetchCompleted: true,
        orderId,
      },
    });
  });

  test('测试调用异常', async () => {
    const exceptionError = new Error('modifyDriver exception');
    const { dispatched } = await testFn(new Promise(() => {
      throw exceptionError;
    }));
    expect(dispatched[0]).toEqual({
      type: FETCH_ODERNUMBER_CALLBACK,
      data: {
        phoneNumberList: [],
        fetchCompleted: true,
        orderId,
      },
    });
  });
  test('测试调用正常 无数据', async () => {
    const exceptionError = new Error('modifyDriver exception');
    const { dispatched } = await testFn(mockRes3);
    expect(dispatched[0]).toEqual({
      type: FETCH_ODERNUMBER_CALLBACK,
      data: {
        phoneNumberList: [],
        fetchCompleted: true,
        orderId,
      },
    });
  });
});

describe('Common Logic getIsdImURL', () => {
  test('getIsdImURL 正常调用', async () => {
    const res = {
      appUrl: 'testAppUrl',
    };
    jest.spyOn(CarFetch, 'getImUrl').mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(getIsdImURL, {
      action: {
        type: FETCH_ISD_IMURL,
      },
      state: {},
    });
    const resData = CommonReqAndResData.getData(
      CommonReqAndResData.keyList.commonIMUrl,
    );
    expect(resData).toEqual(res.appUrl);
    expect(dispatched[0]).toEqual({
      type: FETCH_ISD_IMURL_CALLBACK,
      data: res.appUrl,
    });
  });
  test('getIsdImURL 接口失败', async () => {
    const err = new Error('Error');
    jest.spyOn(CarFetch, 'getImUrl').mockReturnValue(
      new Promise(reject => {
        throw err;
        reject(err);
      }),
    );
    const dispatched = await recordSaga(getIsdImURL, {
      action: {
        type: FETCH_ISD_IMURL,
      },
      state: {},
    });
    const resData = CommonReqAndResData.getData(
      CommonReqAndResData.keyList.commonIMUrl,
    );
    expect(resData).toEqual(undefined);
    expect(dispatched[0]).toEqual({
      type: FETCH_ISD_IMURL_CALLBACK,
      data: undefined,
    });
  });
});

describe('Common Logic getQconfig', () => {
  test('getQconfig res为空', async () => {
    const res = null;
    jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(getQconfig, {
      action: {
        type: FETCH_ISD_IMURL,
      },
      state: {},
    });
    const resData = CommonReqAndResData.getData(
      CommonReqAndResData.keyList.configRes,
    );
    expect(resData).toEqual(null);
    expect(dispatched).toEqual([]);
  });
  test('getQconfig', async () => {
    const res = 'test';
    jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(getQconfig, {
      action: {
        type: FETCH_ISD_IMURL,
      },
      state: {},
    });
    const resData = CommonReqAndResData.getData(
      CommonReqAndResData.keyList.configRes,
    );
    expect(resData).toEqual(res);
    expect(dispatched[0]).toEqual({
      type: FETCH_QCONFIG_CALLBACK,
      data: res,
    });
  });
});

describe('Common Logic getLastOrderCallBackTask', () => {
  const lastOrderRes = {
    orderList: [
      {
        orderId: '1234',
      },
    ],
  };
  const mockDataMap = [
    {
      mockParams: { res: lastOrderRes },
      expected: lastOrderRes.orderList,
    },
    {
      mockParams: { res: undefined },
      expected: [],
    },
  ];
  beforeEach(() => {
    jest.spyOn(EventHelper, 'sendEvent').mockImplementation(() => {});
  });
  test.each(mockDataMap)(
    'getLastOrderCallBackTask',
    async ({ mockParams, expected }) => {
      AppContext.setIsHomeCombine(true);
      const dispatched = await recordSaga(getLastOrderCallBackTask, {
        action: {
          type: FETCH_LASTORDER_CALLBACK_TASK,
          data: mockParams,
        },
        state: {},
      });
      const resData = CommonReqAndResData.getData(
        CommonReqAndResData.keyList.lastOrderRes,
      );
      expect(resData).toEqual(expected);
      expect(dispatched[0]).toEqual({
        type: FETCH_LASTORDER_CALLBACK,
      });
      expect(EventHelper.sendEvent).toHaveBeenCalledWith(EventName.LastOrderChange, {
        orderInfo: expected,
      });
    },
  );
});

describe('Common Logic getLastOrder', () => {
  beforeEach(() => {
    jest.spyOn(User, 'isLoginSync').mockReturnValue(true);
  });
  test('getLastOrder 正常调用', async () => {
    const res = 'test';
    jest.spyOn(CarFetch, 'getLastOrder').mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(getLastOrder, {
      action: {
        type: FETCH_LASTORDER,
        data: {},
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: FETCH_LASTORDER_CALLBACK_TASK,
      data: { res },
    });
  });
  test('getLastOrder 未登陆', async () => {
    jest.spyOn(User, 'isLoginSync').mockReturnValue(false);
    const dispatched = await recordSaga(getLastOrder, {
      action: {
        type: FETCH_LASTORDER,
        data: {},
      },
      state: {},
    });
    expect(dispatched).toEqual([]);
  });
  test('getLastOrder 接口失败', async () => {
    jest.spyOn(User, 'isLoginSync').mockReturnValue(true);
    const err = new Error('error');
    jest.spyOn(CarFetch, 'getLastOrder').mockReturnValue(
      new Promise(reject => {
        throw err;
        reject(err);
      }),
    );
    const dispatched = await recordSaga(getLastOrder, {
      action: {
        type: FETCH_LASTORDER,
        data: {},
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: FETCH_LASTORDER_CALLBACK_TASK,
      data: { res: undefined },
    });
  });
});

describe('Common Logic getListWarningInfoCallBackTask', () => {
  const storageRes = { storage: 'test' };
  const parameter = {};
  const pageIndexId = WarningInfo.PageIndexId.List;
  const showLoading = false;
  const data = {
    pageIndexId,
    showLoading,
  };
  const res = 'test';
  const isError = true;
  const errorInfo = new Error('Error');
  const mockData = {
    storageRes,
    parameter,
    data,
    res,
    isError,
    errorInfo,
  };
  test('getListWarningInfoCallBackTask 有storage', async () => {
    const dispatched = await recordSaga(getListWarningInfoCallBackTask, {
      action: {
        type: FETCH_LISTWARNINGINFO_CALLBACK_TASK,
        data: mockData,
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: FETCH_LISTWARNINGINFO_CALLBACK,
      data: {
        pageIndexId: data.pageIndexId,
        res: storageRes,
        fetchWarningInfoLoading: {
          loading: false,
          error: false,
        },
      },
    });
  });
  test('getListWarningInfoCallBackTask 无storage showLoading', async () => {
    const data = { pageIndexId, showLoading: true };
    const dispatched = await recordSaga(getListWarningInfoCallBackTask, {
      action: {
        type: FETCH_LISTWARNINGINFO_CALLBACK_TASK,
        data: { ...mockData, storageRes: undefined, data },
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: FETCH_WARNINGINFO_START,
      data: {
        pageIndexId,
        fetchWarningInfoLoading: {
          loading: true,
          error: false,
          showLoading: true,
        },
      },
    });
    expect(dispatched[1]).toEqual({
      type: FETCH_LISTWARNINGINFO_CLEAR,
      data: data,
    });
    const LogTraceDev = jest.spyOn(CarLog, 'LogTraceDev');
    expect(LogTraceDev).toHaveBeenCalledWith({
      key: LogKeyDev.c_car_trace_listwaringinfo,
      info: {
        pageIndexId,
        req: parameter,
        res,
        error: errorInfo,
      },
    });
    expect(dispatched[2]).toEqual({
      type: FETCH_LISTWARNINGINFO_CALLBACK,
      data: {
        pageIndexId,
        res,
        fetchWarningInfoLoading: {
          loading: false,
          error: isError,
          showLoading: true,
        },
      },
    });
  });
});

jest.mock('../../../src/pages/xcar/State/Common/Requests', () => ({
  getWarningListParams: () => ({
    storageRes: undefined,
    parameter: {
      locale: undefined,
      pageIndexId: 12002,
      businessLineId: 14,
      searchParamDto: {
        salesModel: '',
        startUsingDate: '2022-12-28',
        endUsingDate: '2022-12-29',
        productType: '',
        productCategoryList: [],
        startPlaceList: [{ geocategoryid: '3', globalid: '1' }],
        endPlace: [{ geocategoryid: '3', globalid: '1' }],
      },
      startUsingDate: '2022-12-28 14:00:00',
      endUsingDate: '2022-12-29 14:00:00',
      startPlace: { geocategoryid: 3, globalid: 1 },
      endPlace: { geocategoryid: 3, globalid: 1 },
      isOneway: true,
    },
    data: { pageIndexId: 12002 },
  }),
}));
describe('Common Logic getListWarningInfo', () => {
  const mockData = { pageIndexId: WarningInfo.PageIndexId.List };
  const mockParameter = {
    locale: undefined,
    pageIndexId: 12002,
    businessLineId: 14,
    searchParamDto: {
      salesModel: '',
      startUsingDate: '2022-12-28',
      endUsingDate: '2022-12-29',
      productType: '',
      productCategoryList: [],
      startPlaceList: [{ geocategoryid: '3', globalid: '1' }],
      endPlace: [{ geocategoryid: '3', globalid: '1' }],
    },
    startUsingDate: '2022-12-28 14:00:00',
    endUsingDate: '2022-12-29 14:00:00',
    startPlace: { geocategoryid: 3, globalid: 1 },
    endPlace: { geocategoryid: 3, globalid: 1 },
    isOneway: true,
  };
  test('getListWarningInfo 正常调用 无storage', async () => {
    const res = 'test';
    jest
      .spyOn(CarFetch, 'queryWarningList')
      .mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(getListWarningInfo, {
      action: {
        type: FETCH_LISTWARNINGINFO,
        data: mockData,
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: FETCH_LISTWARNINGINFO_CALLBACK_TASK,
      data: {
        parameter: mockParameter,
        data: mockData,
        res,
        isError: false,
        errorInfo: null,
      },
    });
  });
  test('getListWarningInfo 接口异常', async () => {
    const err = new Error('error');
    jest.spyOn(CarFetch, 'queryWarningList').mockReturnValue(
      new Promise(reject => {
        throw err;
        reject(err);
      }),
    );
    const dispatched = await recordSaga(getListWarningInfo, {
      action: {
        type: FETCH_LISTWARNINGINFO,
        data: mockData,
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: FETCH_LISTWARNINGINFO_CALLBACK_TASK,
      data: {
        parameter: mockParameter,
        data: mockData,
        res: undefined,
        isError: true,
        errorInfo: err,
      },
    });
  });
});
