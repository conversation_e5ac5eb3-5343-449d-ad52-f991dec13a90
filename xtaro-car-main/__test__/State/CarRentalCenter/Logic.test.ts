import { recordSaga } from '../../testHelpers';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';

import { apiFetchRentCenterPage } from '../../../src/pages/xcar/State/CarRentalCenter/Logic';
import { FETCH_RENT_CENTER_PAGE } from '../../../src/pages/xcar/State/CarRentalCenter/Types';
import * as Actions from '../../../src/pages/xcar/State/CarRentalCenter/Actions';

describe('CarRentalCenter Logic apiFetchRentCenterPage', () => {
  test('测试接口调用成功', async () => {
    jest.spyOn(CarFetch, 'rentCenterPage').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: true,
        },
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiFetchRentCenterPage, {
      action: {
        type: FETCH_RENT_CENTER_PAGE,
        data: {},
      },
      state: {},
    });

    expect(dispatched).toEqual([
      Actions.fetchRentCenterPageCallback({
        isLoading: false,
        data: {
          pics: [],
          contents: [],
          guides: [],
          name: '',
          address: '',
        },
      }),
    ]);
  });

  test('测试接口调用失败', async () => {
    jest.spyOn(CarFetch, 'rentCenterPage').mockReturnValue(
      Promise.resolve({
        baseResponse: {
          isSuccess: false,
        },
      }),
    );

    // 获取saga执行记录
    const dispatched = await recordSaga(apiFetchRentCenterPage, {
      action: {
        type: FETCH_RENT_CENTER_PAGE,
        data: {},
      },
      state: {},
    });

    expect(dispatched).toEqual([
      Actions.fetchRentCenterPageCallback({
        isLoading: false,
        errorMsg: '数据加载异常',
      }),
    ]);
  });
});
