import CarRentalCenterReducer, {
  getInitialState,
} from '../../../src/pages/xcar/State/CarRentalCenter/Reducer';

import {
  RESET,
  FETCH_RENT_CENTER_PAGE_CALLBACK,
} from '../../../src/pages/xcar/State/CarRentalCenter/Types';

describe('CarRentalCenterReducer Reducer Test', () => {
  const initState = getInitialState();

  // 验证初始化
  test('CarRentalCenterReducer Reducer Init', () => {
    expect(CarRentalCenterReducer(undefined, {})).toEqual(initState);
  });

  // 验证重置
  describe('CarRenterCenter Reducer RESET', () => {
    test('RESET', () => {
      expect(
        CarRentalCenterReducer(initState, {
          type: RESET,
        }),
      ).toEqual(initState);
    });
  });

  describe('CarRenterCenter Reducer FETCH_RENT_CENTER_PAGE_CALLBACK', () => {
    test('FETCH_RENT_CENTER_PAGE_CALLBACK 接口成功', () => {
      const testData = {
        pics: ['1'],
        contents: ['1'],
        guides: ['指引'],
        name: '三亚租车中心',
        address: '三亚凤凰国际机场',
      };
      expect(
        CarRentalCenterReducer(initState, {
          type: FETCH_RENT_CENTER_PAGE_CALLBACK,
          data: {
            isLoading: false,
            data: testData,
          },
        }),
      ).toEqual({
        ...initState,
        isLoading: false,
        data: testData,
        errorMsg: '',
      });
    });

    test('FETCH_RENT_CENTER_PAGE_CALLBACK 接口失败', () => {
      expect(
        CarRentalCenterReducer(initState, {
          type: FETCH_RENT_CENTER_PAGE_CALLBACK,
          data: {
            isLoading: false,
            errorMsg: 'failedToLoad',
          },
        }),
      ).toEqual({
        ...initState,
        isLoading: false,
        errorMsg: 'failedToLoad',
      });
    });
  });
});
