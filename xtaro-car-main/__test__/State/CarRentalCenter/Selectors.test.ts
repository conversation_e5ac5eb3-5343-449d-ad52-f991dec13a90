import {
  getIsLoading,
  getData,
  getErrorMsg,
} from '../../../src/pages/xcar/State/CarRentalCenter/Selectors';

describe('CarRentalCenter Selectors getIsLoading', () => {
  const mockStateMap = [
    {
      state: {
        CarRentalCenter: {
          isLoading: false,
        },
      },
      expected: false,
    },
    {
      state: {
        CarRentalCenter: {
          isLoading: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)('getIsLoading check', ({ state, expected }) => {
    const data = getIsLoading(state);
    expect(data).toEqual(expected);
  });
});

describe('CarRentalCenter Selectors getErrorMsg', () => {
  const mockStateMap = [
    {
      state: {
        CarRentalCenter: {
          errorMsg: '',
        },
      },
      expected: '',
    },
    {
      state: {
        CarRentalCenter: {
          errorMsg: '接口请求失败',
        },
      },
      expected: '接口请求失败',
    },
  ];
  test.each(mockStateMap)('getErrorMsg check', ({ state, expected }) => {
    const data = getErrorMsg(state);
    expect(data).toEqual(expected);
  });
});

describe('CarRentalCenter Selectors getData', () => {
  const mockd = {
    pics: ['1'],
    contents: ['1'],
    guides: ['指引'],
    name: '三亚租车中心',
    address: '三亚凤凰国际机场',
  };
  const mockStateMap = [
    {
      state: {
        CarRentalCenter: {
          data: mockd,
        },
      },
      expected: mockd,
    },
  ];
  test.each(mockStateMap)('getPickType check', ({ state, expected }) => {
    const data = getData(state);
    expect(data).toEqual(expected);
  });
});
