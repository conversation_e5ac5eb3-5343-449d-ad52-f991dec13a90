import {
  fetchRentCenterPage,
  fetchRentCenterPageCallback,
  reset,
} from '../../../src/pages/xcar/State/CarRentalCenter/Actions';
import {
  FETCH_RENT_CENTER_PAGE,
  FETCH_RENT_CENTER_PAGE_CALLBACK,
  RESET,
} from '../../../src/pages/xcar/State/CarRentalCenter/Types';

describe('CarRentalCenter Actions fetchRentCenterPage', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: FETCH_RENT_CENTER_PAGE,
        data,
      },
    },
  ];
  test.each(mockMap)('fetchRentCenterPage check', ({ data, expected }) => {
    const result = fetchRentCenterPage(data);
    expect(result).toEqual(expected);
  });
});

describe('CarRentalCenter Actions fetchRentCenterPageCallback', () => {
  const data = {
    isLoading: false,
    errorMsg: '数据加载异常',
  };
  const mockMap = [
    {
      data,
      expected: {
        type: FETCH_RENT_CENTER_PAGE_CALLBACK,
        data,
      },
    },
  ];
  test.each(mockMap)(
    'fetchRentCenterPageCallback check',
    ({ data, expected }) => {
      const result = fetchRentCenterPageCallback(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('CarRentalCenter Actions reset', () => {
  const mockMap = [
    {
      expected: {
        type: RESET,
      },
    },
  ];
  test.each(mockMap)('reset check', ({ expected }) => {
    const result = reset();
    expect(result).toEqual(expected);
  });
});
