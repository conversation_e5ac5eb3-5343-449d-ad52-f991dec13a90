import { CarABTesting } from '../../../src/pages/xcar/Util/Index';
import {
  getIsLoading,
  getIsError,
  getErrorInfo,
  getTimeOutPopVisible,
  getIsLoginAtBookingPage,
  getUniqueCode,
  getVehicleModalVisible,
  getLimitRulePopVisible,
  getCouponModalVisible,
  getProductConfirmModalVisible,
  getVirtualNumberDialogVisible,
  getPriceDetailModalVisible,
  getProductConfirmAnchor,
  getVehicleIndex,
  getTotalPriceModalVisible,
  getEasyLifePopVisible,
  getIsCouponBookAtBookingPage,
  getOptimizationStrengthenModalVisible,
  getVehicleDetailListResponse,
  getVehicleDetailListRequest,
  getFirstScreenParam,
  getVehicleInfo,
  getTangramEntranceInfos,
  getVehicleModalInfo,
  getMarketingAtmosphere,
  getHasLaborDayLabel,
  getVendorListMarketTheme,
  getIsShowMarketTheme,
  getAppResponseMap,
  getBbkVehicleNameProps,
  getIsShowRestAssured,
  getVehicleImageInfo,
  getVehicleImgIsLoading,
  getVehicleDescList,
  getLimitTipInfo,
  getUniquePriceInfo,
  getUniqueReference,
  getSpecificVendorList,
  getFilteredVendorList,
  getIsHasSpecificButSoldOut,
  getIsNoSpecificButHasFiltered,
  getIsSpecificSoldOutAndNoFiltered,
  getLocationDate,
  getErrorImgType,
  getSpecificSectionHeader,
  getSpecificSectionFooter,
  getIncludeFees,
  getTotalPriceModalData,
  getVendorListShowModalKey,
  getFirstScreenParamToBooking,
  getIsCouponBook,
  getVendorInfoByUniqueCode,
  getCouponBookInfoByUniqueCode,
  getFeeMap,
  getVehicleCode,
  getYunnanBannerInfo,
  getLocationDatePopVisible,
  getLocationDatePopType,
  getRecommendListPriceSummaryModalData,
  getRecommendListPriceSummaryModalVisible,
  getRecommendListTotalPriceModalData,
  getRecommendListTotalPriceModalVisible,
  getRequestQuery,
  packagePriceSummaryModalData,
  getNoMatchData,
  getVehicleVR,
  getCurrentPageParams,
  getImageListLogInfo,
  getMultimediaAlbum,
  getTotalPhotos,
  getIsLoadingAlbum,
  getBouncesImageInfo,
} from './../../../src/pages/xcar/State/VendorList/Selectors';

import { homeConfig } from '../../../src/pages/xcar/Pages/Home/Logic/Index';
import dayjs from '../../../src/pages/xcar/Common/src/Dayjs/src';

jest.mock('../../../src/pages/xcar/State/OrderDetail/Selectors', () => jest.fn());

jest.mock('../../../src/pages/xcar/Global/Cache/ListResSelectors', () => ({
  getLimitTip: jest.fn(),
  getBaseResData: jest.fn(),
  getPromptInfos: jest.fn(),
  getSecretBoxInfo: jest.fn(),
  getFavoriteInfo: jest.fn(),
  getLimitRuleData: jest.fn(() => ({
    baseResponse: {
      isSuccess: true,
    },
    limitInfos: [{ limitContents: [{ module: 1, title: 'title' }] }],
    limitTitle: 'limitTitle',
  })),
  getIsVehicle2: jest.fn(),
  getBaseProductGroups: jest.fn(),
}));

jest.mock('../../../src/pages/xcar/State/List/Mappers', () => jest.fn());

jest.mock('../../../src/pages/xcar/State/Booking/Selectors', () => jest.fn());

afterEach(() => {
  // restore the spy created with spyOn
  jest.restoreAllMocks();
});

describe('VendorList Selectors getIsLoading', () => {
  const isLoading = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          isLoading,
        },
      },
      expected: isLoading,
    },
  ];
  test.each(mockStateMap)('getIsLoading check', ({ state, expected }) => {
    const result = getIsLoading(state);
    expect(result).toEqual(expected);
  });
});

describe('VendorList Selectors getIsError', () => {
  const isError = false;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          isError,
        },
      },
      expected: isError,
    },
  ];
  test.each(mockStateMap)('getIsError check', ({ state, expected }) => {
    const result = getIsError(state);
    expect(result).toEqual(expected);
  });
});

describe('VendorList Selectors getErrorInfo', () => {
  const errorInfo = {};
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          errorInfo,
        },
      },
      expected: errorInfo,
    },
  ];
  test.each(mockStateMap)('getErrorInfo check', ({ state, expected }) => {
    const result = getErrorInfo(state);
    expect(result).toEqual(expected);
  });
});

describe('VendorList Selectors getTimeOutPopVisible', () => {
  const timeOutPopVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          timeOutPopVisible,
        },
      },
      expected: timeOutPopVisible,
    },
  ];
  test.each(mockStateMap)(
    'getTimeOutPopVisible check',
    ({ state, expected }) => {
      const result = getTimeOutPopVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getIsLoginAtBookingPage', () => {
  const isLoginAtBookingPage = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          isLoginAtBookingPage,
        },
      },
      expected: isLoginAtBookingPage,
    },
  ];
  test.each(mockStateMap)(
    'getIsLoginAtBookingPage check',
    ({ state, expected }) => {
      const result = getIsLoginAtBookingPage(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getUniqueCode', () => {
  const uniqueCode = 'x';
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          uniqueCode,
        },
      },
      expected: uniqueCode,
    },
  ];
  test.each(mockStateMap)('getUniqueCode check', ({ state, expected }) => {
    const result = getUniqueCode(state);
    expect(result).toEqual(expected);
  });
});

describe('VendorList Selectors getVehicleModalVisible', () => {
  const vehicleModalVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          vehicleModalVisible,
        },
      },
      expected: vehicleModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getVehicleModalVisible check',
    ({ state, expected }) => {
      const result = getVehicleModalVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getLimitRulePopVisible', () => {
  const limitRulePopVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          limitRulePopVisible,
        },
      },
      expected: limitRulePopVisible,
    },
  ];
  test.each(mockStateMap)(
    'getLimitRulePopVisible check',
    ({ state, expected }) => {
      const result = getLimitRulePopVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getCouponModalVisible', () => {
  const couponModalVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          couponModalVisible,
        },
      },
      expected: couponModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getCouponModalVisible check',
    ({ state, expected }) => {
      const result = getCouponModalVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getProductConfirmModalVisible', () => {
  const productConfirmModalVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          productConfirmModalVisible,
        },
      },
      expected: productConfirmModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getProductConfirmModalVisible check',
    ({ state, expected }) => {
      const result = getProductConfirmModalVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getVirtualNumberDialogVisible', () => {
  const isVirtualNumberDialogVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          isVirtualNumberDialogVisible,
        },
      },
      expected: isVirtualNumberDialogVisible,
    },
  ];
  test.each(mockStateMap)(
    'getVirtualNumberDialogVisible check',
    ({ state, expected }) => {
      const result = getVirtualNumberDialogVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getPriceDetailModalVisible', () => {
  const priceDetailModalVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          priceDetailModalVisible,
        },
      },
      expected: priceDetailModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getPriceDetailModalVisible check',
    ({ state, expected }) => {
      const result = getPriceDetailModalVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getProductConfirmAnchor', () => {
  const productConfirmAnchor = 'x';
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          productConfirmAnchor,
        },
      },
      expected: productConfirmAnchor,
    },
  ];
  test.each(mockStateMap)(
    'getProductConfirmAnchor check',
    ({ state, expected }) => {
      const result = getProductConfirmAnchor(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getVehicleIndex', () => {
  const vehicleIndex = 1;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          vehicleIndex,
        },
      },
      expected: vehicleIndex,
    },
  ];
  test.each(mockStateMap)('getVehicleIndex check', ({ state, expected }) => {
    const result = getVehicleIndex(state);
    expect(result).toEqual(expected);
  });
});

describe('VendorList Selectors getTotalPriceModalVisible', () => {
  const totalPriceModalVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          totalPriceModalVisible,
        },
      },
      expected: totalPriceModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getTotalPriceModalVisible check',
    ({ state, expected }) => {
      const result = getTotalPriceModalVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getEasyLifePopVisible', () => {
  const isEasyLifeModalVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          isEasyLifeModalVisible,
        },
      },
      expected: isEasyLifeModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getEasyLifePopVisible check',
    ({ state, expected }) => {
      const result = getEasyLifePopVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getIsCouponBookAtBookingPage', () => {
  const isCouponBookAtBookingPage = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          isCouponBookAtBookingPage,
        },
      },
      expected: isCouponBookAtBookingPage,
    },
  ];
  test.each(mockStateMap)(
    'getIsCouponBookAtBookingPage check',
    ({ state, expected }) => {
      const result = getIsCouponBookAtBookingPage(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getOptimizationStrengthenModalVisible', () => {
  const optimizationStrengthenModalVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          optimizationStrengthenModalVisible,
        },
      },
      expected: optimizationStrengthenModalVisible,
    },
  ];
  test.each(mockStateMap)(
    'getOptimizationStrengthenModalVisible check',
    ({ state, expected }) => {
      const result = getOptimizationStrengthenModalVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getVehicleDetailListResponse', () => {
  const queryVehicleDetailListRes = {};
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          queryVehicleDetailListRes,
        },
      },
      expected: queryVehicleDetailListRes,
    },
  ];
  test.each(mockStateMap)(
    'getVehicleDetailListResponse check',
    ({ state, expected }) => {
      const result = getVehicleDetailListResponse(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getVehicleDetailListRequest', () => {
  const queryVehicleDetailListReq = {};
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          queryVehicleDetailListReq,
        },
      },
      expected: queryVehicleDetailListReq,
    },
  ];
  test.each(mockStateMap)(
    'getVehicleDetailListRequest check',
    ({ state, expected }) => {
      const result = getVehicleDetailListRequest(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getFirstScreenParam', () => {
  const firstScreenParam = {};
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          firstScreenParam,
        },
      },
      expected: firstScreenParam,
    },
  ];
  test.each(mockStateMap)(
    'getFirstScreenParam check',
    ({ state, expected }) => {
      const result = getFirstScreenParam(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getVehicleInfo', () => {
  const vehicleInfo_1 = {};
  const vehicleInfo_2 = {};
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: null,
    },
    {
      state: {
        VendorList: {
          firstScreenParam: {
            vehicleInfo: vehicleInfo_1,
          },
          queryVehicleDetailListRes: {
            vehicleInfo: vehicleInfo_2,
          },
        },
      },
      expected: vehicleInfo_1,
    },
    {
      state: {
        VendorList: {
          queryVehicleDetailListRes: {
            vehicleInfo: vehicleInfo_2,
          },
        },
      },
      expected: vehicleInfo_2,
    },
  ];
  test.each(mockStateMap)('getVehicleInfo check', ({ state, expected }) => {
    const result = getVehicleInfo(state);
    expect(result).toEqual(expected);
  });
});

describe('VendorList Selectors getTangramEntranceInfos', () => {
  const promptInfo = { type: 11 };
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          queryVehicleDetailListRes: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          queryVehicleDetailListRes: {
            promptInfos: [promptInfo],
          },
        },
      },
      expected: [promptInfo],
    },
  ];
  test.each(mockStateMap)(
    'getTangramEntranceInfos check',
    ({ state, expected }) => {
      const result = getTangramEntranceInfos(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getVehicleModalInfo', () => {
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: {
        section: {
          introduce: null,
          carProtection: null,
          baseInfo: {
            headerText: '车型基本信息',
            items: [],
          },
          possibleVehicles: {
            headerText: '本次可能取到的车型',
            items: [],
          },
        },
        similarVehicleTableProps: {
          title: [],
          data: [],
        },
        vehicleNameProps: {
          vehicleCode: '',
          isSimilar: true,
        },
        vehicleBaseInfoProps: {
          imgList: [],
          vehicleLabels: [],
          allocationLables: [],
          energyBaseLabels: [],
          energyAllocationLabels: [],
          isNewEnergyVeh: false,
        },
        possibleVehicleList: [],
      },
    },
    {
      state: {
        VendorList: {
          queryVehicleDetailListRes: {
            vehicleInfo: {
              brandEName: '雪佛兰',
              brandId: 0,
              brandName: '雪佛兰',
              cover:
                'https://dimg04.c-ctrip.com/images/0AS33120009sqadenADAB.png',
              displacement: '1.0T-1.5L',
              doorNo: 4,
              fuel: '汽油92号',
              gearbox: '手自一体变速箱(AT)',
              groupCode: '2',
              groupName: '经济轿车',
              groupSubClassCode: '',
              imageList: [
                'https://dimg04.c-ctrip.com/images/0415c120009sl0u8w4A82.jpg',
              ],
              isHot: false,
              isSpecialized: true,
              licenseStyle: '2',
              luggageNo: 2,
              name: '雪佛兰科沃兹',
              oilType: 5,
              passengerNo: 5,
              realityImageUrl:
                'https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png',
              sourcePicInfos: [
                {
                  picList: [
                    {
                      imageUrl:
                        'https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png',
                      sortNum: 0,
                    },
                    {
                      imageUrl:
                        'https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg',
                      sortNum: 1,
                    },
                  ],
                  source: 3,
                  sourceName: '年款/颜色等以实物为准丨图片来源懂车帝',
                  type: 2,
                },
              ],
              style: '',
              transmissionName: '自动挡',
              transmissionType: 1,
              vedio:
                'https://video.c-ctrip.com/videos/R40f27000001i5l8m91B0.mp4',
              vehicleAccessoryImages: [
                'https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg',
                'https://dimg04.c-ctrip.com//images/0410u120008at350uDF48.jpg',
              ],
              vehicleCode: '14415',
              zhName: '雪佛兰科沃兹',
            },
          },
        },
      },
      expected: {
        section: {
          introduce: null,
          carProtection: false,
          baseInfo: { headerText: '车型基本信息', items: [] },
          possibleVehicles: { headerText: '车型图片参考', items: [] },
        },
        similarVehicleTableProps: { title: [], data: [] },
        vehicleNameProps: {
          vehicleCode: '14415',
          name: '雪佛兰科沃兹',
          isSimilar: false,
          isHotLabel: false,
          type: undefined,
        },
        vehicleBaseInfoProps: {
          imgList: [
            {
              imageUrl:
                'https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg',
            },
            {
              imageUrl:
                'https://dimg04.c-ctrip.com//images/0410u120008at350uDF48.jpg',
            },
          ],
          vehicleLabels: [
            { text: '经济轿车', icon: { iconContent: '&#xee8b;' } },
            { text: '5座', icon: { iconContent: '&#xee86;' } },
            { text: '4门', icon: { iconContent: '&#xee7f;' } },
            {
              text: '自动挡',
              icon: { iconContent: '&#xee7e;' },
              type: 'displacement',
            },
          ],
          allocationLables: [
            {
              text: '汽油92号',
              icon: {
                iconContent: '&#xe81c;',
                iconStyle: [
                  {
                    fontSize: 22,
                    lineHeight: '20px',
                    color: '#666',
                    width: 20,
                    height: 20,
                  },
                ],
              },
            },
            {
              text: '手自一体变速箱(AT)',
              icon: {
                iconContent: '&#xe819;',
                iconStyle: [
                  {
                    fontSize: 22,
                    lineHeight: '20px',
                    color: '#666',
                    width: 20,
                    height: 20,
                  },
                ],
              },
            },
          ],
          energyBaseLabels: [
            {
              code: 'groupName',
              text: '经济轿车',
              iconUrl:
                'https://pages.c-ctrip.com/rncarapp/ctrip/app/groupName.png',
            },
            {
              code: 'doorAndPassengerNo',
              text: '4门5座',
              iconUrl:
                'https://pages.c-ctrip.com/rncarapp/ctrip/app/doorAndPassengerNo.png',
            },
            {
              code: 'transmissionName',
              text: '自动挡',
              iconUrl:
                'https://pages.c-ctrip.com/rncarapp/ctrip/app/transmissionName.png',
            },
            {
              code: 'displacement',
              text: '1.0T-1.5L',
              iconUrl:
                'https://pages.c-ctrip.com/rncarapp/ctrip/app/displacement.png',
            },
            {
              code: 'fuel',
              text: '汽油92号',
              iconUrl: 'https://pages.c-ctrip.com/rncarapp/ctrip/app/fuel.png',
            },
          ],
          bootInfo: undefined,
          energyAllocationLabels: [],
          isNewEnergyVeh: false,
        },
        possibleVehicleList: [],
      },
    },
  ];
  test.each(mockStateMap)(
    'getVehicleModalInfo check',
    ({ state, expected }) => {
      const result = getVehicleModalInfo(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getMarketingAtmosphere', () => {
  const marketingAtmosphere = {};
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          queryVehicleDetailListRes: {},
        },
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          queryVehicleDetailListRes: {
            marketingAtmosphere,
          },
        },
      },
      expected: marketingAtmosphere,
    },
  ];
  test.each(mockStateMap)(
    'getMarketingAtmosphere check',
    ({ state, expected }) => {
      const result = getMarketingAtmosphere(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getHasLaborDayLabel', () => {
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: false,
    },
    {
      state: {
        VendorList: {
          queryVehicleDetailListRes: {
            specificProductGroups: {
              vendorPriceList: [
                {
                  allTags: [
                    {
                      labelCode: '3692',
                    },
                  ],
                },
              ],
            },
          },
        },
      },
      expected: !!homeConfig?.homeThemeConfig?.vendorListBg,
    },
  ];
  test.each(mockStateMap)(
    'getHasLaborDayLabel check',
    ({ state, expected }) => {
      const result = getHasLaborDayLabel(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getVendorListMarketTheme', () => {
  const mockStateMap = [
    {
      state: {
        Home: {},
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        Home: {
          themeConfigRes: {
            mergeHomePage: {
              searchUrl:
                'https://dimg04.c-ctrip.com/images/04149120009i08xxp8237.png',
              dynamicSearchUrl:
                'http://file.c-ctrip.com/files/6/car/04147120009i43o0sA44B.json',
              bottomBarBackgroundUrl:
                'https://dimg04.c-ctrip.com/images/0412e120009i08v189C08.png',
              bottomBarButtons: [
                {
                  type: 0,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/0411d120009i0965vE870.png',
                },
                {
                  type: 1,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/04109120009i0990pEE58.png',
                },
                {
                  type: 2,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/04137120009i09f5fA7AD.png',
                },
                {
                  type: 3,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/0414i120009i09k0z30EF.png',
                },
                {
                  type: 4,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/04152120009sogv7jFA64.png',
                },
              ],
              wordsColor: '#00A49D',
            },
            listPage: {
              items: [
                {
                  filterCode: 'Promotion_3803',
                  filterSkinItems: null,
                },
                {
                  filterCode: 'Promotion_3803',
                  filterSkinItems: null,
                },
                {
                  filterCode: 'Promotion_3801',
                  filterSkinItems: null,
                },
                {
                  filterCode: 'Promotion_3801',
                  filterSkinItems: null,
                },
              ],
            },
            detailPage: {
              filterCode: '3776',
              skinUrl:
                'https://dimg04.c-ctrip.com/images/0410z120009l4gdjaBE08.png',
              jumpUrl: null,
            },
            homePage: {
              searchUrl:
                'https://dimg04.c-ctrip.com/images/04149120009i08xxp8237.png',
              dynamicSearchUrl:
                'http://file.c-ctrip.com/files/6/car/0413s120009i43o0d06AA.json',
              bottomBarBackgrounds: [
                {
                  type: 0,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/04120120009i09z4261B4.png',
                },
                {
                  type: 1,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/0410t120009i09rzp320F.png',
                },
              ],
              bottomBarButtons: [
                {
                  type: 0,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/0411d120009i0965vE870.png',
                },
                {
                  type: 1,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/04109120009i0990pEE58.png',
                },
                {
                  type: 2,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/04137120009i09f5fA7AD.png',
                },
                {
                  type: 3,
                  skinUrl:
                    'https://dimg04.c-ctrip.com/images/0414i120009i09k0z30EF.png',
                },
              ],
              wordsColor: '#00A49D',
            },
          },
        },
      },
      expected: {
        filterCode: '3776',
        jumpUrl: null,
        skinUrl: 'https://dimg04.c-ctrip.com/images/0410z120009l4gdjaBE08.png',
      },
    },
  ];
  test.each(mockStateMap)(
    'getVendorListMarketTheme check',
    ({ state, expected }) => {
      const result = getVendorListMarketTheme(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('getVehicleImageInfo', () => {
  const mockStateCase = {
    VendorList: {
      queryVehicleDetailListRes: {
        vehicleInfo: {
          imageList: ['https://pages.c-ctrip.com/carisd/app/14415.jpg'],
          licenseStyle: '2',
          realityImageUrl:
            'https://dimg04.c-ctrip.com//images/0410x120008at5wkp72C0.jpg',
          vedio:
            'https://pages.c-ctrip.com/rncarapp/ctrip/app/u00c0s000000hzxjp7D7B.mp4',
          cover: 'https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg',
          oilType: 5,
        },
      },
    },
  };
});

describe('getVehicleImageInfo', () => {
  const mockStateCase = {
    VendorList: {
      queryVehicleDetailListRes: {
        vehicleInfo: {
          imageList: ['https://pages.c-ctrip.com/carisd/app/14415.jpg'],
          licenseStyle: '2',
          realityImageUrl:
            'https://dimg04.c-ctrip.com//images/0410x120008at5wkp72C0.jpg',
          vedio:
            'https://pages.c-ctrip.com/rncarapp/ctrip/app/u00c0s000000hzxjp7D7B.mp4',
          cover: 'https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg',
          oilType: 5,
        },
      },
    },
  };
});

describe('VendorList Selectors getLocationDatePopVisible', () => {
  const locationDatePopVisible = true;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          locationDatePopVisible,
        },
      },
      expected: locationDatePopVisible,
    },
  ];
  test.each(mockStateMap)(
    'getVehicleModalVisible check',
    ({ state, expected }) => {
      const result = getLocationDatePopVisible(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getLocationDatePopType', () => {
  const locationDatePopType = 1;
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          locationDatePopType,
        },
      },
      expected: locationDatePopType,
    },
  ];
  test.each(mockStateMap)(
    'getVehicleModalVisible check',
    ({ state, expected }) => {
      const result = getLocationDatePopType(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getRecommendListPriceSummaryModalData', () => {
  const mockStateMap = [
    {
      state: {
        VendorList: {
          recommendListPriceSummaryModalData: {
            data: {},
            visible: false,
          },
          recommendListTotalPriceModalData: {
            data: {},
            visible: false,
          },
        },
      },
      expected: {
        recommendListPriceSummaryModalData: {
          data: {},
          visible: false,
        },
        recommendListTotalPriceModalData: {
          data: {},
          visible: false,
        },
      },
    },
    {
      state: {
        VendorList: {
          recommendListPriceSummaryModalData: {
            data: undefined,
            visible: false,
          },
          recommendListTotalPriceModalData: {
            data: undefined,
            visible: false,
          },
        },
      },
      expected: {
        recommendListPriceSummaryModalData: {
          data: undefined,
          visible: false,
        },
        recommendListTotalPriceModalData: {
          data: undefined,
          visible: false,
        },
      },
    },
  ];
  test.each(mockStateMap)(
    'getRecommendListPriceSummaryModalData check',
    ({ state, expected }) => {
      const data = getRecommendListPriceSummaryModalData(state);
      const visible = getRecommendListPriceSummaryModalVisible(state);
      const data2 = getRecommendListTotalPriceModalData(state);
      const visible2 = getRecommendListTotalPriceModalVisible(state);
      expect(data).toEqual(expected.recommendListPriceSummaryModalData.data);
      expect(visible).toEqual(
        expected.recommendListPriceSummaryModalData.visible,
      );
      expect(data2).toEqual(expected.recommendListTotalPriceModalData.data);
      expect(visible2).toEqual(
        expected.recommendListTotalPriceModalData.visible,
      );
    },
  );
});

describe('VendorList Selectors getRequestQuery', () => {
  const dateTime = '2022-10-10 10:00:00';
  const mockStateMap = [
    {
      state: {
        VendorList: {
          recommendListPriceSummaryModalData: {
            data: {
              pickUpLevel: 1,
              pickOffLevel: 2,
              reference: undefined,
            },
          },
        },
        LocationAndDate: {
          rentalLocation: {
            pickUp: {
              area: {},
            },
            dropOff: {
              area: {},
            },
          },
          rentalDate: {
            pickUp: {
              dateTime,
            },
            dropOff: {
              dateTime,
            },
          },
        },
      },
      expected: {
        pickupPointInfo: {
          cityId: undefined,
          date: dateTime,
          dropOffOnDoor: 0,
          locationCode: undefined,
          locationName: undefined,
          locationType: undefined,
          pickUpLevel: 1,
          pickupOnDoor: 0,
          poi: { latitude: NaN, longitude: NaN, radius: 0 },
        },
        reference: undefined,
        returnPointInfo: {
          cityId: undefined,
          date: dateTime,
          dropOffOnDoor: 0,
          locationCode: undefined,
          locationName: undefined,
          locationType: undefined,
          pickOffLevel: 2,
          pickupOnDoor: 0,
          poi: { latitude: NaN, longitude: NaN, radius: 0 },
        },
      },
    },
  ];
  test.each(mockStateMap)('getRequestQuery check', ({ state, expected }) => {
    const result = getRequestQuery(state);
    expect(result).toEqual(expected);
  });
});

jest.mock('../../../src/pages/xcar/State/List/Mappers', () => ({
  mappingResponseFees2PriceModal: jest.fn(() => ({})),
}));
describe('VendorList Selectors packagePriceSummaryModalData', () => {
  const mockStateMap = [
    {
      state: {
        VendorList: {
          recommendListPriceSummaryModalData: {
            data: {},
          },
          queryVehicleDetailListRes: {
            feeMap: {},
          },
        },
      },
      expected: {
        data: {},
        footerText: undefined,
        invokeFrom: 2,
        priceListLen: undefined,
        title: undefined,
        vehicleIndex: undefined,
        vehicleList: undefined,
        vendorListPageParam: undefined,
      },
    },
  ];
  test.each(mockStateMap)(
    'getVehicleModalVisible check',
    ({ state, expected }) => {
      const result = packagePriceSummaryModalData(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('VendorList Selectors getNoMatchData', () => {
  const mockStateMap = [
    {
      state: {
        LocationAndDate: {
          rentalLocation: {
            pickUp: {
              area: {},
            },
            dropOff: {
              area: {},
            },
          },
        },
        VendorList: {
          queryVehicleDetailListRes: {
            recommendProducts: {},
            recommendInfo: {},
          },
        },
      },
      expected: {
        isNomatch: true,
        isPartNomatch: false,
        reason: undefined,
        recommend: undefined,
      },
    },
  ];
  test.each(mockStateMap)('getNoMatchData check', ({ state, expected }) => {
    const result = getNoMatchData(state);
    expect(result).toEqual(expected);
  });
});

describe('VendorList Selectors getSpecificSectionHeader', () => {
  const mockStateMap = [
    {
      state: {
        LocationAndDate: {
          rentalLocation: {
            pickUp: {
              area: {},
            },
            dropOff: {
              area: {},
            },
          },
        },
        VendorList: {
          queryVehicleDetailListReq: {
            filters: [{}],
          },
          isError: {},
          errorInfo: '',
          queryVehicleDetailListRes: {
            feeMap: {},
          },
          firstScreenParam: {
            vehicleInfo: {},
          },
          isLoading: false,
        },
      },
      expected: {
        isFit: true,
        isShowFitBottomGradientLine: true,
        isShowFitTitle: false,
        isShowGradient: false,
        locationDate: {
          pickupLocation: {
            locationCode: undefined,
            locationName: undefined,
            locationType: undefined,
          },
          ptime: dayjs().format('M月D日 HH:mm'),
          returnLocation: {
            locationCode: undefined,
            locationName: undefined,
            locationType: undefined,
          },
          rtime: dayjs().format('M月D日 HH:mm'),
        },
        vehicleCode: '',
      },
    },
  ];
  test.each(mockStateMap)(
    'getSpecificSectionHeader check',
    ({ state, expected }) => {
      const result = getSpecificSectionHeader(state);
      expect(result).toEqual(expected);
    },
  );
});
describe('getVehicleVR', () => {
  const vrUrl = 'https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364,';
  const mockStateCase = {
    VendorList: {
      queryVehicleDetailListRes: {
        vehicleInfo: {
          vr: vrUrl,
        },
      },
    },
  };

  test('getVehicleVR 有vr', () => {
    const data: any = getVehicleVR(mockStateCase);
    expect(data).toEqual(
      'https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364',
    );
  });
});

describe('getLimitTipInfo', () => {
  const mockStateCase = {
    LocationAndDate: {
      rentalLocation: {
        pickUp: {
          area: {},
          cname: '上海',
        },
        dropOff: {
          area: {},
          cname: '上海',
        },
      },
    },
    VendorList: {
      queryVehicleDetailListRes: {
        vehicleInfo: {
          licenseDescription: '测试',
        },
      },
    },
  };

  test('getLimitTipInfo 有limitDesc', () => {
    const data: any = getLimitTipInfo(mockStateCase);
    expect(data).toEqual({
      isLimit: false,
      isShowTip: 'title',
      tipText: '测试',
    });
  });
});

describe('getCurrentPageParams', () => {
  const mockStateMap = [
    {
      state: {
        VendorList: {
          vendorListCurrentPageParams: undefined,
        },
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          vendorListCurrentPageParams: {},
        },
      },
      expected: {},
    },
  ];

  test.each(mockStateMap)(
    'getCurrentPageParams check',
    ({ state, expected }) => {
      const result = getCurrentPageParams(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('getImageListLogInfo', () => {
  const reference = {
    vendorCode: '02',
    vendorVehicleCode: '03',
    pStoreCode: '04',
    rStoreCode: '05',
  };
  const uniqueCode = '0101';
  const vehicleCode = '1111';
  const mockStateCase = {
    VendorList: {
      uniqueCode,
      queryVehicleDetailListRes: {
        vehicleInfo: {
          vehicleCode,
        },
        specificProductGroups: {
          vendorPriceList: [
            {
              uniqueCode,
              reference,
            },
          ],
        },
      },
    },
  };

  test('getImageListLogInfo', () => {
    const data: any = getImageListLogInfo(mockStateCase);
    expect(data).toEqual({
      vehicleCode,
      // 供应商ID
      vendorCode: reference?.vendorCode,
      // 供应商车型ID
      vendorVehicleId: reference?.vendorVehicleCode,
      // 取车携程门店ID
      pstoreCode: reference?.pStoreCode,
      // 还车携程门店ID
      rstoreCode: reference?.rStoreCode,
    });
  });
});

describe('getMultimediaAlbum', () => {
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          multimediaAlbum: [1, 2, 3],
        },
      },
      expected: [1, 2, 3],
    },
  ];

  test.each(mockStateMap)('getMultimediaAlbum check', ({ state, expected }) => {
    const result = getMultimediaAlbum(state);
    expect(result).toEqual(expected);
  });
});

describe('getTotalPhotos', () => {
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          totalPhotos: [4, 5, 6],
        },
      },
      expected: [4, 5, 6],
    },
  ];

  test.each(mockStateMap)('getTotalPhotos check', ({ state, expected }) => {
    const result = getTotalPhotos(state);
    expect(result).toEqual(expected);
  });
});

describe('getIsLoadingAlbum', () => {
  const mockStateMap = [
    {
      state: {
        VendorList: {},
      },
      expected: undefined,
    },
    {
      state: {
        VendorList: {
          isLoadingAlbum: false,
        },
      },
      expected: false,
    },
  ];

  test.each(mockStateMap)('getIsLoadingAlbum check', ({ state, expected }) => {
    const result = getIsLoadingAlbum(state);
    expect(result).toEqual(expected);
  });
});

describe('getBouncesImageInfo', () => {
  const mockStateMap = [
    // 有视屏且详情页接口未返回图片与列表页图片相同
    {
      state: {
        VendorList: {
          firstScreenParam: {
            vehicleInfo: {
              skylight: '',
              luggageNo: 1,
              carPlay: '',
              displacement: '1.0T-1.5L',
              autoPark: false,
              endurance: '工信部续航100km',
              imageList: [
                'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
              ],
              license: '',
              isSpecialized: true,
              groupCode: '2',
              zhName: '别克英朗',
              doorNo: 4,
              driveMode: '前置前驱',
              chargeInterface: 'USB',
              mediaTypes: [2],
              vehicleCode: '834',
              name: '别克英朗',
              realityImageUrl:
                'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
              fuel: '92号',
              passengerNo: 5,
              luggageNum: '可放1个24寸行李箱',
              isHot: false,
              transmissionType: 1,
              brandName: '别克',
              oilType: 3,
              struct: '三厢车',
              groupName: '经济轿车',
              groupSubClassCode: '',
              brandEName: '别克',
              licenseStyle: '2',
              autoBackUp: false,
              vehiclesSetId: '64',
              guidSys: '',
              transmissionName: '自动挡',
            },
          },
        },
      },
      expected: {
        mediaGroup: [],
        tabNameMap: {},
        tabNameMapIndex: {},
        totalPhotos: [
          {
            isEqualListImage: false,
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
          },
        ],
        vehicleCode: '834',
      },
    },
    // 无视屏且详情页接口未返回图片与列表页图片相同
    {
      state: {
        VendorList: {
          firstScreenParam: {
            vehicleInfo: {
              skylight: '',
              luggageNo: 1,
              carPlay: '',
              displacement: '1.0T-1.5L',
              autoPark: false,
              endurance: '工信部续航100km',
              imageList: [
                'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
              ],
              license: '',
              isSpecialized: true,
              groupCode: '2',
              zhName: '别克英朗',
              doorNo: 4,
              driveMode: '前置前驱',
              chargeInterface: 'USB',
              mediaTypes: [3],
              vehicleCode: '834',
              name: '别克英朗',
              realityImageUrl:
                'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
              fuel: '92号',
              passengerNo: 5,
              luggageNum: '可放1个24寸行李箱',
              isHot: false,
              transmissionType: 1,
              brandName: '别克',
              oilType: 3,
              struct: '三厢车',
              groupName: '经济轿车',
              groupSubClassCode: '',
              brandEName: '别克',
              licenseStyle: '2',
              autoBackUp: false,
              vehiclesSetId: '64',
              guidSys: '',
              transmissionName: '自动挡',
            },
          },
        },
      },
      expected: {
        mediaGroup: [],
        tabNameMap: {},
        tabNameMapIndex: {},
        totalPhotos: [
          {
            isEqualListImage: true,
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
          },
        ],
        vehicleCode: '834',
      },
    },
    // 有视屏且详情页接口返回图片与列表页图片相同
    {
      state: {
        VendorList: {
          firstScreenParam: {
            vehicleInfo: {
              skylight: '',
              luggageNo: 1,
              carPlay: '',
              displacement: '1.0T-1.5L',
              autoPark: false,
              endurance: '工信部续航100km',
              imageList: [
                'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
              ],
              multimediaAlbums: [
                {
                  albumName: '官方相册',
                  albumType: 1,
                  mediaGroup: [
                    {
                      groupType: 2,
                      groupName: '视频',
                      groupSortNum: 2,
                      medias: [
                        {
                          sortNum: 0,
                          type: 2,
                          url: 'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
                          cover:
                            'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
                        },
                      ],
                    },
                  ],
                  note: '年款/颜色等以门店为准',
                },
              ],
              license: '',
              isSpecialized: true,
              groupCode: '2',
              zhName: '别克英朗',
              doorNo: 4,
              driveMode: '前置前驱',
              chargeInterface: 'USB',
              mediaTypes: [2],
              vehicleCode: '834',
              name: '别克英朗',
              realityImageUrl:
                'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
              fuel: '92号',
              passengerNo: 5,
              luggageNum: '可放1个24寸行李箱',
              isHot: false,
              transmissionType: 1,
              brandName: '别克',
              oilType: 3,
              struct: '三厢车',
              groupName: '经济轿车',
              groupSubClassCode: '',
              brandEName: '别克',
              licenseStyle: '2',
              autoBackUp: false,
              vehiclesSetId: '64',
              guidSys: '',
              transmissionName: '自动挡',
            },
          },
        },
      },
      expected: {
        totalPhotos: [
          {
            sortNum: 0,
            type: 2,
            url: 'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
            cover:
              'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
            groupType: 2,
            isEqualListImage: false,
          },
        ],
        vehicleCode: '834',
        tabNameMap: { 视频: 0 },
        tabNameMapIndex: { '0': '视频' },
        note: '年款/颜色等以门店为准',
        mediaGroup: [
          {
            groupType: 2,
            groupName: '视频',
            groupSortNum: 2,
            medias: [
              {
                sortNum: 0,
                type: 2,
                url: 'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
                cover:
                  'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
              },
            ],
          },
        ],
      },
    },
    // 无视屏且详情页接口返回图片与列表页图片相同
    {
      state: {
        VendorList: {
          firstScreenParam: {
            vehicleInfo: {
              skylight: '',
              luggageNo: 1,
              carPlay: '',
              displacement: '1.0T-1.5L',
              autoPark: false,
              endurance: '工信部续航100km',
              imageList: [
                'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
              ],
              multimediaAlbums: [
                {
                  albumName: '官方相册',
                  albumType: 1,
                  mediaGroup: [
                    {
                      groupType: 2,
                      groupName: 'VR',
                      groupSortNum: 2,
                      medias: [
                        {
                          sortNum: 0,
                          type: 3,
                          url: 'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
                          cover:
                            'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
                        },
                      ],
                    },
                  ],
                  note: '年款/颜色等以门店为准',
                },
              ],
              license: '',
              isSpecialized: true,
              groupCode: '2',
              zhName: '别克英朗',
              doorNo: 4,
              driveMode: '前置前驱',
              chargeInterface: 'USB',
              mediaTypes: [2],
              vehicleCode: '834',
              name: '别克英朗',
              realityImageUrl:
                'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
              fuel: '92号',
              passengerNo: 5,
              luggageNum: '可放1个24寸行李箱',
              isHot: false,
              transmissionType: 1,
              brandName: '别克',
              oilType: 3,
              struct: '三厢车',
              groupName: '经济轿车',
              groupSubClassCode: '',
              brandEName: '别克',
              licenseStyle: '2',
              autoBackUp: false,
              vehiclesSetId: '64',
              guidSys: '',
              transmissionName: '自动挡',
            },
          },
        },
      },
      expected: {
        totalPhotos: [
          {
            sortNum: 0,
            type: 3,
            url: 'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
            cover:
              'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
            groupType: 2,
            isEqualListImage: true,
          },
        ],
        vehicleCode: '834',
        tabNameMap: { VR: 0 },
        tabNameMapIndex: { '0': 'VR' },
        note: '年款/颜色等以门店为准',
        mediaGroup: [
          {
            groupType: 2,
            groupName: 'VR',
            groupSortNum: 2,
            medias: [
              {
                sortNum: 0,
                type: 3,
                url: 'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
                cover:
                  'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
              },
            ],
          },
        ],
      },
    },
  ];

  test.each(mockStateMap)(
    'getBouncesImageInfo check',
    ({ state, expected }) => {
      const result = getBouncesImageInfo(state);
      expect(result).toEqual(expected);
    },
  );
});
