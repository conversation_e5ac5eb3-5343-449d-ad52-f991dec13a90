import { recordSaga, takeEveryGeneratorFunction } from '../../testHelpers';
import { CarFetch, CarABTesting } from '../../../src/pages/xcar/Util/Index';

import {
  queryVehicleDetailList,
  handleGoToBooking,
  queryMultimediaAlbum,
} from '../../../src/pages/xcar/State/VendorList/Logic';
import {
  QUERY_VEHICLE_DETAIL_LIST,
  GO_TO_BOOKING,
  QUERY_MULTIMEDIA_ALBUM,
  QUERY_MULTIMEDIA_ALBUM_CALLBACK,
} from '../../../src/pages/xcar/State/VendorList/Types';
import { formatMultimediaAlbum } from '../../../src/pages/xcar/State/VendorList/Mappers';
import * as Actions from '../../../src/pages/xcar/State/VendorList/Actions';
import * as ProductConfirmActions from '../../../src/pages/xcar/State/ProductConfirm/Actions';
import { verifyPointInfo } from '../../../src/pages/xcar/State/List/Method';
import { Utils } from '../../../src/pages/xcar/Util/Index';
import { AlbumType } from '../../../src/pages/xcar/Types/Dto/QueryVehicleDetailListResponseType';

const multimediaAlbumRes = require('../../../__mocks__/restful/18631/queryMultimediaAlbum/20231106.json');

// describe('VendorList Logic queryVehicleDetailList', () => {
//   test('测试正常调用', async () => {
//     const state = {
//       LocationAndDate: {
//         rentalDate: {
//           pickUp: {
//             dateTime: '20221226180000',
//           },
//           dropOff: {
//             dateTime: '20221228180000',
//           },
//         },
//         rentalLocation: {
//           pickUp: {
//             country: '中国',
//             area: {
//               lng: 109.41235,
//               lat: 18.30767,
//               id: '',
//               name: '凤凰国际机场T1航站楼',
//               type: 1,
//             },

//             cname: '三亚',
//             cid: 43,
//           },
//           dropOff: {
//             country: '中国',
//             area: {
//               lng: 109.41235,
//               lat: 18.30767,
//               id: '',
//               name: '凤凰国际机场T1航站楼',
//               type: 1,
//             },

//             cname: '三亚',
//             cid: 43,
//           },
//           isShowDropOff: false,
//         },
//       },
//     };

//     const reqParam = {
//       ctripVehicleId: '5241',
//       groupCode: '2',
//       tops: [],
//       filters: [],
//       sortType: '1',
//       uniqSign: '12001093210000201232WEhN024PRtM3P1qfiM91',
//       extraMaps: {
//         recommendType: '',
//       },
//     };

//     const testRes = {
//       baseResponse: {
//         isSuccess: true,
//       },
//       specificProductGroups: {
//         vendorPriceList: [
//           {
//             vendorName: '懒人行租车',
//           },
//         ],
//       },
//     };

//     jest
//       .spyOn(CarFetch, 'queryVehicleDetailList')
//       .mockReturnValue(Promise.resolve(testRes));

//     // 获取saga执行记录
//     const dispatched = await recordSaga(queryVehicleDetailList, {
//       action: {
//         type: QUERY_VEHICLE_DETAIL_LIST,
//         data: {
//           reqParam,
//         },
//       },
//       state: state,
//     });

//     const pickupPointInfo = {
//       cityId: 43,
//       date: '2022-12-26 18:00:00',
//       locationCode: '',
//       locationName: '凤凰国际机场T1航站楼',
//       locationType: 1,
//       poi: {
//         latitude: 18.30767,
//         longitude: 109.41235,
//         radius: 0,
//       },
//       pickupOnDoor: 0,
//       dropOffOnDoor: 0,
//     };

//     const returnPointInfo = {
//       cityId: 43,
//       date: '2022-12-28 18:00:00',
//       locationCode: '',
//       locationName: '凤凰国际机场T1航站楼',
//       locationType: 1,
//       poi: {
//         latitude: 18.30767,
//         longitude: 109.41235,
//         radius: 0,
//       },
//       pickupOnDoor: 0,
//       dropOffOnDoor: 0,
//     };

//     const request = {
//       pickupPointInfo,
//       returnPointInfo,
//       ...reqParam,
//       productRef: undefined,
//       appRequestMap: {
//         verifyRequestParameters: () => {
//           return (
//             verifyPointInfo(pickupPointInfo) &&
//             verifyPointInfo(returnPointInfo) &&
//             Utils.isValid(reqParam.ctripVehicleId)
//           );
//         },
//       },
//     };

//     expect(dispatched).toEqual([
//       Actions.setStatus({
//         isLoading: true,
//         isError: false,
//       }),
//       Actions.setVehicleListRequestAndResponse({ request }),
//       Actions.setVehicleListRequestAndResponse({
//         response: testRes,
//       }),
//       Actions.setStatus({
//         isLoading: false,
//         isError: false,
//         errorInfo: null,
//       }),
//     ]);
//   });
// });

describe('VendorList Logic  handleGoToBooking', () => {
  test('测试正常调用', async () => {
    // 获取saga执行记录
    const dispatched = await recordSaga(handleGoToBooking, {
      action: {
        type: GO_TO_BOOKING,
        data: {
          pTime: '2022-7-31 18:00:00',
          uniqueCode: '111222',
        },
      },
      state: {
        VendorList: {
          uniqueCode: '111222',
        },
      },
    });

    expect(dispatched).toEqual([
      Actions.setModalStatus({ uniqueCode: '111222' }),
      Actions.setTimeOutPopData({ visible: true }),
    ]);
  });
});

describe('VendorList Logic  queryVehicleDetailList', () => {
  const params = {
    reqParam: {},
    callbackFun: () => {},
  };
  const mockResult = {
    baseResponse: {
      isSuccess: false,
      code: '1000002',
    },
  };
  const testLogicFn = async (params, result) => {
    const api = jest
      .spyOn(CarFetch, 'queryVehicleDetailList')
      .mockReturnValueOnce(Promise.resolve(result));
    // 获取saga执行记录
    const dispatched = await recordSaga(queryVehicleDetailList, {
      action: {
        type: QUERY_VEHICLE_DETAIL_LIST,
        data: params,
      },
      state: {
        LocationAndDate: {
          rentalLocation: {
            pickUp: {
              area: {},
            },
            dropOff: {
              area: {},
            },
          },
        },
        VendorList: {
          firstScreenParam: {},
        },
      },
    });
    return { api, dispatched };
  };

  //@ts-ignore
  test('测试正常无结果没返回提示语句', async () => {
    const { api, dispatched } = await testLogicFn(params, mockResult);
    // @ts-ignore
    const mockRecommendInfo = {
      reason: '暂无符合要求的报价哦',
      recommend: '建议您修改取还车条件',
    };
    expect(dispatched[2]).toEqual(
      Actions.setVehicleListRequestAndResponse({
        response: {
          baseResponse: { isSuccess: false, code: '1000002' },
          recommendInfo: mockRecommendInfo,
        },
      }),
    );
  });
});

describe('VendorList Logic queryMultimediaAlbum', () => {
  test('测试正常调用 非预请求', async () => {
    jest
      .spyOn(CarFetch, 'queryMultimediaAlbum')
      .mockReturnValue(Promise.resolve(multimediaAlbumRes));

    const dispatched = await recordSaga(queryMultimediaAlbum, {
      action: {
        type: QUERY_MULTIMEDIA_ALBUM,
        data: {
          vehicleId: '123456',
          albumType: AlbumType.Official,
        },
      },
      state: {},
    });
    const { multimediaAlbum, totalPhotos } = formatMultimediaAlbum(
      multimediaAlbumRes?.multimediaAlbum,
    );
    expect(dispatched[0]).toEqual({
      type: QUERY_MULTIMEDIA_ALBUM_CALLBACK,
      data: {
        multimediaAlbum,
        totalPhotos,
      },
    });
  });

  test('测试正常调用 预请求', async () => {
    jest
      .spyOn(CarFetch, 'queryMultimediaAlbum')
      .mockReturnValue(Promise.resolve(multimediaAlbumRes));

    const dispatched = await recordSaga(queryMultimediaAlbum, {
      action: {
        type: QUERY_MULTIMEDIA_ALBUM,
        data: {
          vehicleId: '123456',
          albumType: AlbumType.Official,
          isPreFetch: true,
        },
      },
      state: {},
    });
    expect(dispatched.length).toEqual(0);
  });

  test('测试数据异常调用', async () => {
    const res: any = {};
    jest
      .spyOn(CarFetch, 'queryMultimediaAlbum')
      .mockReturnValue(Promise.resolve(res));
    let callBackParam = null;
    const dispatched = await recordSaga(queryMultimediaAlbum, {
      action: {
        type: QUERY_MULTIMEDIA_ALBUM,
        data: {
          vehicleId: '123456',
          albumType: AlbumType.Official,
        },
      },
      state: {},
    });
    expect(dispatched.length).toEqual(0);
  });
});
