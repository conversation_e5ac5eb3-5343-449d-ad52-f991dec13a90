import VendorListReducer, {
  getInitialState,
} from '../../../src/pages/xcar/State/VendorList/Reducer';
import {
  RESET,
  SET_STATUS,
  SET_TIMEOUT_POP_DATA,
  VEHICLE_MODAL_VISIBLE,
  LIMITRULE_POP_VISIBLE,
  SET_COUPON_MODAL_VISIBLE,
  SET_IS_LOGIN_AT_BOOKING_PAGE,
  SET_VEHICLE_DETAIL_LIST_REQ_RES,
  SET_VEHICLE_INDEX,
  SET_UNIQUECODE,
  SET_PRODUCT_COMFIRM_MODAL_VISIBLE,
  SET_PRICE_DETAIL_MODAL_VISIBLE,
  SET_MODAL_STATUS,
  SET_FRIST_SCREEN_PARAM,
  SET_TOTAL_PRICE_MODAL_VISIBLE,
  CLOSE_VENDORLIST_MODAL,
  SET_VIRTUAL_NUMBER_VISIBLE,
  SET_IS_COUPON_BOOK_AT_BOOKING_PAGE,
  SET_OPTIMIZATION_STRENGTHEN_MODAL_VISIBLE,
  SET_VENDORLISTPAGE_LOCATIONDATEPOP_VISIBLE,
  SET_PRICE_SUMMARY_MODAL,
  SET_CURRENTPAGEPARAMS,
  QUERY_MULTIMEDIA_ALBUM,
  QUERY_MULTIMEDIA_ALBUM_CALLBACK,
} from '../../../src/pages/xcar/State/VendorList/Types';

describe('VendorList Reducer Test', () => {
  const initState = getInitialState();

  test('Init', () => {
    expect(VendorListReducer(undefined, {})).toEqual(initState);
  });

  test('RESET', () => {
    expect(
      VendorListReducer(initState, {
        type: RESET,
      }),
    ).toEqual({
      ...initState,
    });
  });

  test('SET_STATUS', () => {
    const data = {
      isLoading: true,
      isError: false,
      errorInfo: null,
    };
    expect(
      VendorListReducer(initState, {
        type: SET_STATUS,
        data,
      }),
    ).toEqual({
      ...initState,
      ...data,
    });
  });

  test('SET_TIMEOUT_POP_DATA', () => {
    const data = {
      visible: true,
    };
    expect(
      VendorListReducer(initState, {
        type: SET_TIMEOUT_POP_DATA,
        data,
      }),
    ).toEqual({
      ...initState,
      timeOutPopVisible: data.visible,
    });
  });

  test('VEHICLE_MODAL_VISIBLE', () => {
    const visible = true;
    expect(
      VendorListReducer(initState, {
        type: VEHICLE_MODAL_VISIBLE,
        visible,
      }),
    ).toEqual({
      ...initState,
      vehicleModalVisible: visible,
    });
  });

  test('LIMITRULE_POP_VISIBLE', () => {
    const data = {
      visible: true,
    };
    expect(
      VendorListReducer(initState, {
        type: LIMITRULE_POP_VISIBLE,
        data,
      }),
    ).toEqual({
      ...initState,
      limitRulePopVisible: data.visible,
    });
  });

  test('SET_COUPON_MODAL_VISIBLE', () => {
    const visible = true;
    expect(
      VendorListReducer(initState, {
        type: SET_COUPON_MODAL_VISIBLE,
        visible,
      }),
    ).toEqual({
      ...initState,
      couponModalVisible: visible,
    });
  });

  test('SET_IS_LOGIN_AT_BOOKING_PAGE', () => {
    const data = true;
    expect(
      VendorListReducer(initState, {
        type: SET_IS_LOGIN_AT_BOOKING_PAGE,
        data,
      }),
    ).toEqual({
      ...initState,
      isLoginAtBookingPage: data,
    });
  });

  describe('VendorList Reducer SET_VEHICLE_DETAIL_LIST_REQ_RES', () => {
    const data_1 = {
      request: {},
      response: {
        baseResponse: {
          isSuccess: true,
        },
      },
    };
    const data_2 = {
      request: {},
      response: {},
    };
    const mockMap = [
      {
        data: data_1,
        expected: {
          ...initState,
          queryVehicleDetailListReq: data_1.request,
          queryVehicleDetailListRes: data_1.response,
          firstScreenParam: null,
        },
      },
      {
        data: data_2,
        expected: {
          ...initState,
          queryVehicleDetailListReq: data_2.request,
          queryVehicleDetailListRes: data_2.response,
        },
      },
    ];
    test.each(mockMap)(
      'SET_VEHICLE_DETAIL_LIST_REQ_RES check',
      ({ data, expected }) => {
        expect(
          VendorListReducer(initState, {
            type: SET_VEHICLE_DETAIL_LIST_REQ_RES,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  test('SET_VEHICLE_INDEX', () => {
    const vehicleIndex = 1;
    expect(
      VendorListReducer(initState, {
        type: SET_VEHICLE_INDEX,
        vehicleIndex,
      }),
    ).toEqual({
      ...initState,
      vehicleIndex,
    });
  });

  test('SET_UNIQUECODE', () => {
    const uniqueCode = 'x';
    expect(
      VendorListReducer(initState, {
        type: SET_UNIQUECODE,
        uniqueCode,
      }),
    ).toEqual({
      ...initState,
      uniqueCode,
    });
  });

  test('SET_PRODUCT_COMFIRM_MODAL_VISIBLE', () => {
    const visible = true;
    expect(
      VendorListReducer(initState, {
        type: SET_PRODUCT_COMFIRM_MODAL_VISIBLE,
        visible,
      }),
    ).toEqual({
      ...initState,
      productConfirmModalVisible: visible,
    });
  });

  describe('VendorList Reducer SET_PRICE_DETAIL_MODAL_VISIBLE', () => {
    const visible_1 = true;
    const visible_2 = undefined;
    const mockMap = [
      {
        visible: visible_1,
        expected: {
          ...initState,
          priceDetailModalVisible: visible_1,
        },
      },
      {
        visible: visible_2,
        expected: {
          ...initState,
          priceDetailModalVisible: !initState.priceDetailModalVisible,
        },
      },
    ];
    test.each(mockMap)(
      'SET_PRICE_DETAIL_MODAL_VISIBLE check',
      ({ visible, expected }) => {
        expect(
          VendorListReducer(initState, {
            type: SET_PRICE_DETAIL_MODAL_VISIBLE,
            visible,
          }),
        ).toEqual(expected);
      },
    );
  });

  test('SET_MODAL_STATUS', () => {
    const data = {
      productConfirmModalVisible: true,
      priceDetailModalVisible: true,
      totalPriceModalVisible: true,
      isEasyLifeModalVisible: true,
      uniqueCode: 'x',
      productConfirmAnchor: 'y',
    };
    expect(
      VendorListReducer(initState, {
        type: SET_MODAL_STATUS,
        data,
      }),
    ).toEqual({
      ...initState,
      productConfirmModalVisible: data.productConfirmModalVisible,
      priceDetailModalVisible: data.priceDetailModalVisible,
      totalPriceModalVisible: data.totalPriceModalVisible,
      isEasyLifeModalVisible: data.isEasyLifeModalVisible,
      uniqueCode: data.uniqueCode,
      productConfirmAnchor: data.productConfirmAnchor,
    });
  });

  test('SET_FRIST_SCREEN_PARAM', () => {
    const data = {};
    expect(
      VendorListReducer(initState, {
        type: SET_FRIST_SCREEN_PARAM,
        data,
      }),
    ).toEqual({
      ...initState,
      firstScreenParam: data,
    });
  });

  test('SET_TOTAL_PRICE_MODAL_VISIBLE', () => {
    const visible = true;
    expect(
      VendorListReducer(initState, {
        type: SET_TOTAL_PRICE_MODAL_VISIBLE,
        visible,
      }),
    ).toEqual({
      ...initState,
      totalPriceModalVisible: visible,
    });
  });

  test('CLOSE_VENDORLIST_MODAL', () => {
    const closeModalKey = 'totalPriceModalVisible';
    expect(
      VendorListReducer(initState, {
        type: CLOSE_VENDORLIST_MODAL,
        closeModalKey,
      }),
    ).toEqual({
      ...initState,
      totalPriceModalVisible: false,
    });
  });

  test('SET_VIRTUAL_NUMBER_VISIBLE', () => {
    const data = true;
    expect(
      VendorListReducer(initState, {
        type: SET_VIRTUAL_NUMBER_VISIBLE,
        data,
      }),
    ).toEqual({
      ...initState,
      isVirtualNumberDialogVisible: data,
    });
  });

  test('SET_IS_COUPON_BOOK_AT_BOOKING_PAGE', () => {
    const data = true;
    expect(
      VendorListReducer(initState, {
        type: SET_IS_COUPON_BOOK_AT_BOOKING_PAGE,
        data,
      }),
    ).toEqual({
      ...initState,
      isCouponBookAtBookingPage: data,
    });
  });

  test('SET_OPTIMIZATION_STRENGTHEN_MODAL_VISIBLE', () => {
    const data = true;
    expect(
      VendorListReducer(initState, {
        type: SET_OPTIMIZATION_STRENGTHEN_MODAL_VISIBLE,
        data,
      }),
    ).toEqual({
      ...initState,
      optimizationStrengthenModalVisible: data,
    });
  });

  describe('VendorList Reducer SET_VENDORLISTPAGE_LOCATIONDATEPOP_VISIBLE', () => {
    const visible_1 = true;
    const locationDatePopType_1 = 1;
    const mockMap = [
      {
        visible: visible_1,
        locationDatePopType: locationDatePopType_1,
        enterPosition: 1,
        expected: {
          ...initState,
          locationDatePopVisible: visible_1,
          locationDatePopType: locationDatePopType_1,
          locationDatePopEnterPosition: 1,
        },
      },
    ];
    test.each(mockMap)(
      'SET_VENDORLISTPAGE_LOCATIONDATEPOP_VISIBLE check',
      ({ visible, locationDatePopType, enterPosition, expected }) => {
        expect(
          VendorListReducer(initState, {
            type: SET_VENDORLISTPAGE_LOCATIONDATEPOP_VISIBLE,
            data: {
              visible,
              locationDatePopType,
              enterPosition,
            },
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('VendorList Reducer SET_PRICE_SUMMARY_MODAL', () => {
    const mockMap = [
      {
        visible: true,
        data: {},
        expected: {
          ...initState,
          recommendListPriceSummaryModalData: {
            visible: true,
            data: {},
          },
        },
      },
      {
        visible: true,
        data: undefined,
        expected: {
          ...initState,
          recommendListPriceSummaryModalData: {
            visible: true,
            data: {},
          },
        },
      },
    ];
    test.each(mockMap)(
      'SET_PRICE_SUMMARY_MODAL check',
      ({ visible, data, expected }) => {
        expect(
          VendorListReducer(initState, {
            type: SET_PRICE_SUMMARY_MODAL,
            data: {
              visible,
              data,
            },
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('VendorList Reducer SET_CURRENTPAGEPARAMS', () => {
    const mockMap = [
      {
        data: {},
        expected: {
          ...initState,
          vendorListCurrentPageParams: {},
        },
      },
      {
        data: undefined,
        expected: {
          ...initState,
          vendorListCurrentPageParams: undefined,
        },
      },
    ];
    test.each(mockMap)('SET_CURRENTPAGEPARAMS check', ({ data, expected }) => {
      expect(
        VendorListReducer(initState, {
          type: SET_CURRENTPAGEPARAMS,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('VendorList Reducer QUERY_MULTIMEDIA_ALBUM', () => {
    const mockMap = [
      {
        expected: {
          ...initState,
        },
      },
    ];
    test.each(mockMap)('QUERY_MULTIMEDIA_ALBUM check', ({ expected }) => {
      expect(
        VendorListReducer(initState, {
          type: QUERY_MULTIMEDIA_ALBUM,
        }),
      ).toEqual(expected);
    });
  });

  describe('VendorList Reducer QUERY_MULTIMEDIA_ALBUM_CALLBACK', () => {
    const mockMap = [
      {
        data: {
          multimediaAlbum: [1, 2, 3],
          totalPhotos: [4, 5, 6],
        },
        expected: {
          ...initState,
          isLoadingAlbum: false,
          multimediaAlbum: [1, 2, 3],
          totalPhotos: [4, 5, 6],
        },
      },
    ];
    test.each(mockMap)(
      'QUERY_MULTIMEDIA_ALBUM_CALLBACK check',
      ({ data, expected }) => {
        expect(
          VendorListReducer(initState, {
            type: QUERY_MULTIMEDIA_ALBUM_CALLBACK,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });
});
