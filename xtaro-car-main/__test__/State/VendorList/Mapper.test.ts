import * as Utils from '../../../src/pages/xcar/Common/src/Utils/src/Utils';
import { xRouter } from '@ctrip/xtaro';
import {
  getRenderVendorList,
  openUrlVendorList,
  parseVRUrlString,
  formatMultimediaAlbum,
} from '../../../src/pages/xcar/State/VendorList/Mappers';
import { AppContext } from '../../../src/pages/xcar/Util/Index';
import { APP_TYPE } from '../../../src/pages/xcar/Constants/Platform';

const multimediaAlbumRes = require('../../../__mocks__/restful/18631/queryMultimediaAlbum/20231106.json');

jest.mock('../../../src/pages/xcar/Common/src/Utils/src/Utils', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/Common/src/Utils/src/Utils'),
}));

jest.mock('../../../src/pages/xcar/State/List/VehicleListMappers.ts', () => ({
  packageVendorParam: jest.fn(() => {
    return {
      isEasyLife: false,
    };
  }),
}));
jest.mock('../../../src/pages/xcar/State/StoreRef', () => ({
  getStore: () => ({
    getState: () => ({
      List: {},
      LocationAndDate: {},
      Coupon: {},
      ModifyOrder: {},
    }),
  }),
}));

describe('VendorList Mappers Test', () => {
  describe('Mappers getRenderVendorList', () => {
    test('getRenderVendorList resVendorList为空', () => {
      const result = getRenderVendorList(
        [],
        true,
        '123',
        [''],
        {},
        2,
        false,
        '',
        1,
      );
      // 判断返回包含特定值
      expect(result).toEqual([]);
    });

    test('getRenderVendorList resVendorList不为空', () => {
      const result = getRenderVendorList(
        [{ pStoreSortDesc: 'test' }],
        true,
        '123',
        [''],
        {},
        2,
        false,
        '',
        1,
      );
      // 判断返回包含特定值
      expect(result).toEqual([
        {
          isEasyLife: false,
        },
      ]);
    });
  });
});
jest.mock('../../../src/pages/xcar/State/StoreRef', () => ({
  getStore: () => ({
    getState: () => ({
      List: {},
      LocationAndDate: {},
    }),
  }),
}));
describe('Mappers openUrlVendorList', () => {
  test('openUrlVendorList ', () => {
    jest.spyOn(Utils, 'uuid').mockImplementation(() => '1234');
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    const url = `/rn_xtaro_car_main/_crn_config?CRNModuleName=rn_xtaro_car_main&CRNType=1&apptype=${AppContext.CarEnv.appType}&initialPage=VendorList&pageUniqueId=1234&channelId=&aid=&sid=`;
    openUrlVendorList({});
    expect(xRouter.navigateTo).toBeCalledWith({
      url
    });
  });
});

describe('parseVRUrlString', () => {
  it('should return empty strings when input is empty', () => {
    const expected = { newUrl: '', oldUrl: '' };
    const result = parseVRUrlString('');
    expect(result).toEqual(expected);
  });

  it('should parse string correctly when input has values', () => {
    const expected = {
      newUrl: 'https://new-url.com',
      oldUrl: 'https://old-url.com',
    };
    const result = parseVRUrlString('https://new-url.com, https://old-url.com');
    expect(result).toEqual(expected);
  });

  it('should ignore extra commas in input', () => {
    const expected = { newUrl: 'https://new-url.com', oldUrl: '' };
    const result = parseVRUrlString('https://new-url.com,,');
    expect(result).toEqual(expected);
  });

  it('should trim whitespace around URLs', () => {
    const expected = {
      newUrl: 'https://new-url.com',
      oldUrl: 'https://old-url.com',
    };
    const result = parseVRUrlString(
      '   https://new-url.com  ,  https://old-url.com   ',
    );
    expect(result).toEqual(expected);
  });
});

describe('VendorList Mappers formatMultimediaAlbum', () => {
  const mockData = [
    {
      data: multimediaAlbumRes.multimediaAlbum,
      expected: {
        multimediaAlbum: {
          albumName: '官方相册',
          note: '以下为参考样图 年款/颜色等以实物为准',
          albumType: 1,
          albumMenus: [
            { menuName: '全部(31)', sortNum: 1, mediaGroups: [-1] },
            { menuName: '视频(2)', sortNum: 2, mediaGroups: [2] },
            { menuName: 'VR(1)', sortNum: 3, mediaGroups: [3] },
            { menuName: '外观(11)', sortNum: 4, mediaGroups: [4] },
            { menuName: '前排(2)', sortNum: 5, mediaGroups: [5] },
            { menuName: '后排(15)', sortNum: 6, mediaGroups: [6] },
          ],
          mediaGroup: [
            {
              groupType: 2,
              groupName: '视频(2)',
              groupSortNum: 1,
              medias: [
                {
                  type: 2,
                  url: 'https://video-preview.ctrip.com/videos/RV0lcn15372j7p8lsDCAF.mp4?auth=Nephele%20nrasnbqocpx3ndetlcovpaxttgwf8c2a%2F202304171549%2Fcar_standard_product%2FdG9rZW46YmQyOTk1OTVmYzI2MTIzMDdkMmEzNDViMjllNDZiZGQ0N2JlM2VhOTc3ODRjNzE5OTliYWMwNmQ%3D',
                  cover:
                    'https://dimg04.c-ctrip.com/images/0RV1p12000b64jce93BF7.jpg',
                  sortNum: 1,
                  index: 0,
                  groupName: '视频',
                  groupId: 2,
                  itemCountInGroup: 2,
                  itemIdInGroup: 0,
                },
                {
                  type: 2,
                  url: 'https://video-preview.ctrip.com/videos/RV0lcn15372j7p8lsDCAF.mp4?auth=Nephele%20nrasnbqocpx3ndetlcovpaxttgwf8c2a%2F202304171549%2Fcar_standard_product%2FdG9rZW46YmQyOTk1OTVmYzI2MTIzMDdkMmEzNDViMjllNDZiZGQ0N2JlM2VhOTc3ODRjNzE5OTliYWMwNmQ%3D',
                  cover:
                    'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
                  sortNum: 2,
                  index: 1,
                  groupName: '视频',
                  groupId: 2,
                  itemCountInGroup: 2,
                  itemIdInGroup: 1,
                },
              ],
            },
            {
              groupType: 3,
              groupName: 'VR(1)',
              groupSortNum: 2,
              medias: [
                {
                  type: 3,
                  url: 'https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4851',
                  cover:
                    'https://dimg04.c-ctrip.com/images/0413a120008n36k4711D4.jpg',
                  sortNum: 1,
                  index: 2,
                  groupName: 'VR',
                  groupId: 3,
                  itemCountInGroup: 1,
                  itemIdInGroup: 0,
                },
              ],
            },
            {
              groupType: 4,
              groupName: '外观(11)',
              groupSortNum: 3,
              medias: [
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV5f12000b9qtnul9A10.jpg',
                  sortNum: 1,
                  index: 3,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 0,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 2,
                  index: 4,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 1,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 2,
                  index: 5,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 2,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 2,
                  index: 6,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 3,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 2,
                  index: 7,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 4,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 2,
                  index: 8,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 5,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 2,
                  index: 9,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 6,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 2,
                  index: 10,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 7,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 2,
                  index: 11,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 8,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 2,
                  index: 12,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 9,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 2,
                  index: 13,
                  groupName: '外观',
                  groupId: 4,
                  itemCountInGroup: 11,
                  itemIdInGroup: 10,
                },
              ],
            },
            {
              groupType: 5,
              groupName: '前排(2)',
              groupSortNum: 4,
              medias: [
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
                  sortNum: 1,
                  index: 14,
                  groupName: '前排',
                  groupId: 5,
                  itemCountInGroup: 2,
                  itemIdInGroup: 0,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV0r12000b0aujvo9602.jpg',
                  sortNum: 2,
                  index: 15,
                  groupName: '前排',
                  groupId: 5,
                  itemCountInGroup: 2,
                  itemIdInGroup: 1,
                },
              ],
            },
            {
              groupType: 6,
              groupName: '后排(15)',
              groupSortNum: 5,
              medias: [
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV2r12000b0auww3CA35.jpg',
                  sortNum: 1,
                  index: 16,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 0,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
                  sortNum: 2,
                  index: 17,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 1,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
                  sortNum: 2,
                  index: 18,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 2,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
                  sortNum: 2,
                  index: 19,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 3,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
                  sortNum: 2,
                  index: 20,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 4,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
                  sortNum: 3,
                  index: 21,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 5,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
                  sortNum: 4,
                  index: 22,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 6,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
                  sortNum: 4,
                  index: 23,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 7,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
                  sortNum: 4,
                  index: 24,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 8,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
                  sortNum: 5,
                  index: 25,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 9,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
                  sortNum: 5,
                  index: 26,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 10,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
                  sortNum: 5,
                  index: 27,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 11,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
                  sortNum: 6,
                  index: 28,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 12,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
                  sortNum: 6,
                  index: 29,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 13,
                },
                {
                  type: 1,
                  url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
                  sortNum: 6,
                  index: 30,
                  groupName: '后排',
                  groupId: 6,
                  itemCountInGroup: 15,
                  itemIdInGroup: 14,
                },
              ],
            },
          ],
        },
        totalPhotos: [
          {
            type: 2,
            url: 'https://video-preview.ctrip.com/videos/RV0lcn15372j7p8lsDCAF.mp4?auth=Nephele%20nrasnbqocpx3ndetlcovpaxttgwf8c2a%2F202304171549%2Fcar_standard_product%2FdG9rZW46YmQyOTk1OTVmYzI2MTIzMDdkMmEzNDViMjllNDZiZGQ0N2JlM2VhOTc3ODRjNzE5OTliYWMwNmQ%3D',
            cover:
              'https://dimg04.c-ctrip.com/images/0RV1p12000b64jce93BF7.jpg',
            sortNum: 1,
            index: 0,
            groupName: '视频',
            groupId: 2,
            itemCountInGroup: 2,
            itemIdInGroup: 0,
          },
          {
            type: 2,
            url: 'https://video-preview.ctrip.com/videos/RV0lcn15372j7p8lsDCAF.mp4?auth=Nephele%20nrasnbqocpx3ndetlcovpaxttgwf8c2a%2F202304171549%2Fcar_standard_product%2FdG9rZW46YmQyOTk1OTVmYzI2MTIzMDdkMmEzNDViMjllNDZiZGQ0N2JlM2VhOTc3ODRjNzE5OTliYWMwNmQ%3D',
            cover:
              'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
            sortNum: 2,
            index: 1,
            groupName: '视频',
            groupId: 2,
            itemCountInGroup: 2,
            itemIdInGroup: 1,
          },
          {
            type: 3,
            url: 'https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4851',
            cover:
              'https://dimg04.c-ctrip.com/images/0413a120008n36k4711D4.jpg',
            sortNum: 1,
            index: 2,
            groupName: 'VR',
            groupId: 3,
            itemCountInGroup: 1,
            itemIdInGroup: 0,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV5f12000b9qtnul9A10.jpg',
            sortNum: 1,
            index: 3,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 0,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 2,
            index: 4,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 1,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 2,
            index: 5,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 2,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 2,
            index: 6,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 3,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 2,
            index: 7,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 4,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 2,
            index: 8,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 5,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 2,
            index: 9,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 6,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 2,
            index: 10,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 7,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 2,
            index: 11,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 8,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 2,
            index: 12,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 9,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 2,
            index: 13,
            groupName: '外观',
            groupId: 4,
            itemCountInGroup: 11,
            itemIdInGroup: 10,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg',
            sortNum: 1,
            index: 14,
            groupName: '前排',
            groupId: 5,
            itemCountInGroup: 2,
            itemIdInGroup: 0,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV0r12000b0aujvo9602.jpg',
            sortNum: 2,
            index: 15,
            groupName: '前排',
            groupId: 5,
            itemCountInGroup: 2,
            itemIdInGroup: 1,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV2r12000b0auww3CA35.jpg',
            sortNum: 1,
            index: 16,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 0,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
            sortNum: 2,
            index: 17,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 1,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
            sortNum: 2,
            index: 18,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 2,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
            sortNum: 2,
            index: 19,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 3,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
            sortNum: 2,
            index: 20,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 4,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
            sortNum: 3,
            index: 21,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 5,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
            sortNum: 4,
            index: 22,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 6,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
            sortNum: 4,
            index: 23,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 7,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg',
            sortNum: 4,
            index: 24,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 8,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
            sortNum: 5,
            index: 25,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 9,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
            sortNum: 5,
            index: 26,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 10,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
            sortNum: 5,
            index: 27,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 11,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
            sortNum: 6,
            index: 28,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 12,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
            sortNum: 6,
            index: 29,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 13,
          },
          {
            type: 1,
            url: 'https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg',
            sortNum: 6,
            index: 30,
            groupName: '后排',
            groupId: 6,
            itemCountInGroup: 15,
            itemIdInGroup: 14,
          },
        ],
      },
    },
    {
      data: {},
      expected: { multimediaAlbum: {}, totalPhotos: [] },
    },
  ];
  test.each(mockData)(
    'VendorList Mappers formatMultimediaAlbum',
    ({ data, expected }) => {
      const result = formatMultimediaAlbum(data);
      expect(result).toEqual(expected);
    },
  );
});
