import {
  getHistoryAreaInfo,
  getHistoryCityInfo,
} from '../../../src/pages/xcar/State/City/BbkMapper';

// 验证获取历史搜索地点
describe('City Reducer BbkMapper getHistoryAreaInfo', () => {
  const testHistoryAreaList = [
    {
      cid: 43,
      cname: '三亚',
      country: '中国',
      realcountry: '中国',
      isDomestic: true,
      version: 3,
      area: {
        id: '646861',
        name: '凤凰国际机场',
        ename: '',
        lat: '18.303395',
        lng: '109.414693',
        type: 1,
        typename: '机场',
      },
      sortIndex: 34,
      isFromPosition: false,
    },
  ];

  const mockData = [
    {
      data: testHistoryAreaList,
      expected: [
        {
          cityArea: {
            cid: 43,
            cname: '三亚',
            country: '中国',
            realcountry: '中国',
            isDomestic: true,
            version: 3,
            area: {
              id: '646861',
              name: '凤凰国际机场',
              ename: '',
              lat: '18.303395',
              lng: '109.414693',
              type: 1,
              typename: '机场',
            },
            sortIndex: 34,
            isFromPosition: false,
          },
          text: '凤凰国际机场',
          type: 1,
          name: '凤凰国际机场',
          subTextArr: [],
        },
      ],
    },
  ];

  test.each(mockData)('getHistoryAreaInfo', ({ data, expected }) => {
    const result = getHistoryAreaInfo(data);
    expect(result).toEqual(expected);
  });
});

// 验证获取历史搜索城市
describe('City Reducer BbkMapper getHistoryCityInfo', () => {
  const testHistoryCityList = [
    {
      cid: 206,
      cname: '长沙',
      country: '中国',
      realcountry: '中国',
      isDomestic: true,
      version: 3,
      area: {
        id: '9233',
        name: '长沙站',
        ename: '',
        lat: '28.194052',
        lng: '113.012974',
        type: 2,
        typename: '火车',
      },
      sortIndex: 28,
      isFromPosition: false,
    },
  ];
  const mockData = [
    {
      data: testHistoryCityList,
      expected: [
        {
          name: '长沙',
          geoCategoryId: 3,
          globalId: '206',
          id: '206',
          needExtraAction: true,
          extraShowType: 1,
          extension:
            '{"id":"206","name":"长沙","countryName":"中国","isDomestic":true,"rentalLocation":{"cid":"206","cname":"长沙","country":"中国","realcountry":"中国","isDomestic":true,"version":3,"area":{"id":"9233","name":"长沙站","ename":"","lat":"28.194052","lng":"113.012974","type":2,"typename":"火车"},"sortIndex":28,"isFromPosition":false,"name":"长沙","geoCategoryId":3,"globalId":"206","id":"206","needExtraAction":true,"extraShowType":1}}',
          ...testHistoryCityList[0],
          cid: '206',
        },
      ],
    },
  ];

  test.each(mockData)('getHistoryCityInfo', ({ data, expected }) => {
    expect(getHistoryCityInfo(data)).toEqual(expected);
  });
});
