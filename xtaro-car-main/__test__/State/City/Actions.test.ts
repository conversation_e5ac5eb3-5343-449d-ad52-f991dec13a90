import {
  setStatus,
  fetchCityList,
  fetchCityListCallback,
  fetchCityCache,
} from '../../../src/pages/xcar/State/City/Actions';
import {
  SET_STATUS,
  FETCH_CITY_LIST,
  FETCH_CITY_LIST_CALLBACK,
  FETCH_CITY_CACHE,
} from '../../../src/pages/xcar/State/City/Types';

describe('City Actions setStatus', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: SET_STATUS,
        data,
      },
    },
  ];
  test.each(mockMap)('setStatus check', ({ data, expected }) => {
    const result = setStatus(data);
    expect(result).toEqual(expected);
  });
});

describe('City Actions fetchCityList', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: FETCH_CITY_LIST,
        data,
      },
    },
  ];
  test.each(mockMap)('fetchCityList check', ({ data, expected }) => {
    const result = fetchCityList(data);
    expect(result).toEqual(expected);
  });
});

describe('City Actions fetchCityListCallback', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: FETCH_CITY_LIST_CALLBACK,
        data,
      },
    },
  ];
  test.each(mockMap)('fetchCityListCallback check', ({ data, expected }) => {
    const result = fetchCityListCallback(data);
    expect(result).toEqual(expected);
  });
});

describe('City Actions fetchCityCache', () => {
  const mockMap = [
    {
      expected: {
        type: FETCH_CITY_CACHE,
      },
    },
  ];
  test.each(mockMap)('fetchCityCache check', ({ expected }) => {
    const result = fetchCityCache();
    expect(result).toEqual(expected);
  });
});
