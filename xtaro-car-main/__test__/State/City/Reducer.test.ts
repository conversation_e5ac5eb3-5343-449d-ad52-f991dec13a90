import CityReducer, { getInitalState } from '../../../src/pages/xcar/State/City/Reducer';

import {
  SET_STATUS,
  FETCH_CITY_LIST,
  FETCH_CITY_LIST_CALLBACK,
} from '../../../src/pages/xcar/State/City/Types';

describe('City Reducer Test', () => {
  const initState = getInitalState();

  // 验证初始化
  test('City Reducer Init', () => {
    expect(CityReducer(undefined, {})).toEqual(initState);
  });

  // 验证重置
  describe('City Reducer SET_STATUS', () => {
    test('SET_STATUS', () => {
      expect(
        CityReducer(initState, {
          type: SET_STATUS,
          data: {
            isLoading: true,
            isFail: false,
          },
        }),
      ).toEqual({
        ...initState,
        isLoading: true,
        isFail: false,
      });
    });
  });

  describe('City Reducer FETCH_CITY_LIST TEST', () => {
    test('FETCH_CITY_LIST', () => {
      expect(
        CityReducer(initState, {
          type: FETCH_CITY_LIST,
        }),
      ).toEqual({
        ...initState,
      });
    });
  });

  describe('City Reducer FETCH_CITY_LIST_CALLBACK TEST', () => {
    test('FETCH_CITY_LIST_CALLBACK', () => {
      expect(
        CityReducer(initState, {
          type: FETCH_CITY_LIST_CALLBACK,
        }),
      ).toEqual({
        ...initState,
      });
    });
  });
});
