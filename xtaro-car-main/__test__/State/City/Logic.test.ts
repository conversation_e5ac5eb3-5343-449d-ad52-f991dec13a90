import { recordSaga } from '../../testHelpers';
import {
  getIsSuccess,
  queryCityList,
  queryCityListCallback,
  getApiQueryIsdCityFetchParam,
  getApiQueryOsdCityFetchParam,
} from '../../../src/pages/xcar/State/City/Logic';
import * as Types from '../../../src/pages/xcar/State/City/Types';
import * as Actions from '../../../src/pages/xcar/State/City/Actions';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';

const state = {
  City: {},
};

const mockResData = [
  {
    BaseResponse: {
      IsSuccess: true,
    },
    cityList: [{}, {}],
  },
  {
    BaseResponse: {
      IsSuccess: true,
    },
    cityList: [{}, {}],
  },
];

describe('Test City logic queryCityList', () => {
  const data = {};
  const testLogicFn = async (result, data?) => {
    const api = jest
      .spyOn(CarFetch, 'queryCityList')
      .mockReturnValueOnce(Promise.resolve(result));
    const dispatched = await recordSaga(queryCityList, {
      action: {
        type: Types.FETCH_CITY_LIST,
        data,
      },
      state,
    });
    return { api, dispatched };
  };
  test('测试 queryCityList', async () => {
    const { api, dispatched } = await testLogicFn(mockResData, data);
    expect(api).toBeCalled();
    expect(dispatched[0]).toEqual(Actions.setStatus({ isLoading: true }));
    expect(dispatched[1]).toEqual(
      Actions.fetchCityListCallback({
        isError: false,
        param: {
          isdParam: {
            appType: 'ISD_C_APP',
          },
          osdParam: {
            appType: 'OSD_C_APP',
          },
        },
        res: [
          mockResData,
          {
            baseResponse: {
              isSuccess: true,
            },
          },
        ],
      }),
    );
  });
});

describe('Test City logic queryCityListCallback', () => {
  const data = {
    res: mockResData,
    isError: false,
  };
  const testLogicFn = async data => {
    const dispatched = await recordSaga(queryCityListCallback, {
      action: {
        type: Types.FETCH_CITY_LIST_CALLBACK,
        data,
      },
      state,
    });
    return dispatched;
  };
  test('测试 queryCityListCallback', async () => {
    const dispatched = await testLogicFn(data);
    expect(dispatched[0]).toEqual(
      Actions.setStatus({ isLoading: false, isFail: false }),
    );
  });
});

describe('Test City logic getIsSuccess', () => {
  test('test function getIsSuccess', () => {
    const result = getIsSuccess(mockResData[0]);
    expect(result).toEqual(true);
  });
});

describe('Test City logic getApiQueryIsdCityFetchParam', () => {
  test('test function getApiQueryIsdCityFetchParam', () => {
    const result = getApiQueryIsdCityFetchParam();
    expect(result).toEqual({ appType: 'ISD_C_APP' });
  });
});

describe('Test City logic getApiQueryOsdCityFetchParam', () => {
  test('test function getApiQueryOsdCityFetchParam', () => {
    const result = getApiQueryOsdCityFetchParam();
    expect(result).toEqual({ appType: 'OSD_C_APP' });
  });
});
