import { getIsLoading, getIsFail } from '../../../src/pages/xcar/State/City/Selectors';

describe('CityCenter Selectors getIsLoading', () => {
  const mockStateMap = [
    {
      state: {
        City: {
          isLoading: false,
        },
      },
      expected: false,
    },
    {
      state: {
        City: {
          isLoading: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)('getIsLoading check', ({ state, expected }) => {
    const data = getIsLoading(state);
    expect(data).toEqual(expected);
  });
});

describe('CityCenter Selectors getIsFail', () => {
  const mockStateMap = [
    {
      state: {
        City: {
          isFail: true,
        },
      },
      expected: true,
    },
    {
      state: {
        City: {
          isFail: false,
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)('getIsFail check', ({ state, expected }) => {
    const data = getIsFail(state);
    expect(data).toEqual(expected);
  });
});
