import MessageAssistantReducer, {
  getInitialState,
} from '../../../src/pages/xcar/State/MessageAssistant/Reducer';
import {
  SET_PHONE_MODAL_VISIBLE,
  SET_MODALS_VISIBLE,
} from '../../../src/pages/xcar/State/MessageAssistant/Types';
import { CustomerPhoneModalType } from '../../../src/pages/xcar/Constants/OrderDetail';

describe('MessageAssistant Reducer Test', () => {
  const initState = getInitialState();

  test('MessageAssistant Reducer Init', () => {
    expect(MessageAssistantReducer(undefined, {})).toEqual(initState);
  });

  describe('MessageAssistant Reducer SET_PHONE_MODAL_VISIBLE', () => {
    test('SET_PHONE_MODAL_VISIBLE without phoneModalType', () => {
      expect(
        MessageAssistantReducer(initState, {
          type: SET_PHONE_MODAL_VISIBLE,
          data: { visible: true },
        }),
      ).toEqual({ ...initState, phoneModalVisible: true });
    });
    test('SET_PHONE_MODAL_VISIBLE with phoneModalType', () => {
      expect(
        MessageAssistantReducer(initState, {
          type: SET_PHONE_MODAL_VISIBLE,
          data: {
            phoneModalType: CustomerPhoneModalType.Customer,
            visible: true,
          },
        }),
      ).toEqual({
        ...initState,
        phoneModalVisible: true,
        phoneModalType: CustomerPhoneModalType.Customer,
      });
    });
  });

  test('SET_MODALS_VISIBLE', () => {
    expect(
      MessageAssistantReducer(initState, {
        type: SET_MODALS_VISIBLE,
        data: {
          refundDetailModal: {
            visible: true,
          },
        },
      }),
    ).toEqual({
      ...initState,
      modalsVisible: {
        refundDetailModal: {
          visible: true,
        },
      },
    });
  });
});
