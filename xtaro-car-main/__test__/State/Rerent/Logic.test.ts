import { recordSaga } from '../../testHelpers';
import { AppContext, CarFetch, CarStorage } from '../../../src/pages/xcar/Util/Index';
import {
  queryRenewalProductLogic,
  queryRenewalPenaltyLogic,
  saveRenewalPreCheckLogic,
  saveRenewalOrderLogic,
  createContinuePaymentReferenceNoLogic,
  goRerentPayLogic,
  cancelRenewalOrderLogic,
  setRenewalOrderIdLogic,
  queryLimitContent,
} from '../../../src/pages/xcar/State/Rerent/Logic';
import * as Payment from '../../../src/pages/xcar/Util/Payment/Index';
import * as Types from '../../../src/pages/xcar/State/Rerent/Types';
import * as Actions from '../../../src/pages/xcar/State/Rerent/Actions';
import { ModalNames } from '../../../src/pages/xcar/Constants/Rerent';
import Toast from '../../../src/pages/xcar/Common/src/Components/Basic/Toast/src';

jest.mock('../../../src/pages/xcar/Util/Payment/Index', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Util/Payment/Index') }));

afterEach(() => {
  jest.clearAllMocks();
})

describe('queryRenewalProductLogic', () => {
  test('queryRenewalProductLogic 正常运行', async () => {
    // mock接口返回数据
    const queryRenewalProductAPI = jest.spyOn(CarFetch, 'queryRenewalProduct').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      continueRateList: [{
        stock: 1,
      }]
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(queryRenewalProductLogic, {
      action: {
        type: Types.QUERY_RENEWAL_PRODUCT,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33'
        },
        OrderDetail: {
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
        }
      },
    });

    expect(queryRenewalProductAPI).toBeCalled();
    expect(dispatched).toEqual([Actions.queryRenewalProductCallBack({
      productInfo: {
        stock: 1,
      },
    })]);
  })

  test('queryRenewalProductLogic接口返回异常', async () => {
    // mock接口返回数据
    const queryRenewalProductAPI = jest.spyOn(CarFetch, 'queryRenewalProduct').mockReturnValue(Promise.resolve(null));
    // 获取saga执行记录
    const dispatched = await recordSaga(queryRenewalProductLogic, {
      action: {
        type: Types.QUERY_RENEWAL_PRODUCT,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33'
        },
        OrderDetail: {
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
        }
      },
    });

    expect(queryRenewalProductAPI).toBeCalled();
    expect(dispatched).toEqual([Actions.queryRenewalProductCallBack({
      productInfo: {},
    })]);
  })
})


describe('queryRenewalPenaltyLogic', () => {
  test('queryRenewalPenaltyLogic 正常运行', async () => {
    // mock接口返回数据
    const queryRenewalPenaltyAPI = jest.spyOn(CarFetch, 'queryRenewalPenalty').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(queryRenewalPenaltyLogic, {
      action: {
        type: Types.QUERY_RENEWAL_PENALTY,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: '11111',
        },
        OrderDetail: {
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
        }
      },
    });

    expect(queryRenewalPenaltyAPI).toBeCalled();
    expect(dispatched).toEqual([Actions.queryRenewalPenaltyCallback({
      penaltyDetail: {
        baseResponse: {
          isSuccess: true,
        },
      },
    }), Actions.setModalVisible({
      name: ModalNames.RefundCheck,
      visible: true,
    })]);
  })

  test('queryRenewalPenaltyLogic 服务异常', async () => {
    // mock接口返回数据
    const queryRenewalPenaltyAPI = jest.spyOn(CarFetch, 'queryRenewalPenalty').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: false,
      },
    }));
    const ToastFunc = jest.spyOn(Toast, 'show');
    // 获取saga执行记录
    const dispatched = await recordSaga(queryRenewalPenaltyLogic, {
      action: {
        type: Types.QUERY_RENEWAL_PENALTY,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: '11111',
        },
        OrderDetail: {
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
        }
      },
    });

    expect(queryRenewalPenaltyAPI).toBeCalled();
    expect(ToastFunc).toBeCalledWith('系统异常');
  })
})

describe('saveRenewalPreCheckLogic', () => {
  test('下单预检查：支持保险，未加购保险，出提醒弹窗', async () => {
    // mock接口返回数据
    const queryRenewalPenaltyAPI = jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(Promise.resolve({
      insuranceFlag: true,
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(saveRenewalPreCheckLogic, {
      action: {
        type: Types.SAVE_RENEWAL_PRE_CHECK,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: '11111',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: false,
        },
        OrderDetail: {
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
        }
      },
    });

    expect(queryRenewalPenaltyAPI).toBeCalled();
    expect(dispatched).toEqual([Actions.setModalVisible({
      name: ModalNames.INSTIP,
      visible: true,
    })]);
  })

  test('下单预检查：不支持保险，直接下单', async () => {
    // mock接口返回数据
    const queryRenewalPenaltyAPI = jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(Promise.resolve({
      insuranceFlag: false,
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(saveRenewalPreCheckLogic, {
      action: {
        type: Types.SAVE_RENEWAL_PRE_CHECK,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: '11111',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
          },
          addInsurance: false,
        },
        OrderDetail: {
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
        }
      },
    });

    expect(queryRenewalPenaltyAPI).toBeCalled();
    expect(dispatched).toEqual([Actions.saveRerentOrder({})]);
  })

  test('免费续租保险，直接下单', async () => {
    // mock接口返回数据
    const queryRenewalPenaltyAPI = jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(Promise.resolve({
      insuranceFlag: false,
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(saveRenewalPreCheckLogic, {
      action: {
        type: Types.SAVE_RENEWAL_PRE_CHECK,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: '11111',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            freeRenewalInsurance: true,
          },
          addInsurance: false,
        },
        OrderDetail: {
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
        }
      },
    });

    expect(queryRenewalPenaltyAPI).toBeCalled();
    expect(dispatched).toEqual([Actions.saveRerentOrder({})]);
  })

})



describe('saveRenewalOrderLogic', () => {
  test('下续租单成功', async () => {

    AppContext.setPageInstance({
      getPageId: () => 10650047205,
    })

    // mock接口返回数据
    const FetchApi = jest.spyOn(CarFetch, 'saveRenewalOrder').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      result: {},
      renewalOrderId: '11111',
      prepayAmount: 100,
      referenceNo: 'referenceNo',
      payDeadline: '2022/10/20 10:30:00'
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(saveRenewalOrderLogic, {
      action: {
        type: Types.SAVE_RENEWAL_ORDER,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: '11111',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: false,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
        }
      },
    });

    expect(FetchApi).toBeCalled();
    expect(dispatched).toContainEqual(Actions.goRerentPay({
      refNo: 'referenceNo',
      resAmount: 100,
      payDeadline: '2022/10/20 10:30:00',
    }));
  })

  test('下单失败，弹Toast', async () => {

    AppContext.setPageInstance({
      getPageId: () => 10650047205,
    })

    // mock接口返回数据
    const FetchApi = jest.spyOn(CarFetch, 'saveRenewalOrder').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: false,
      },
      resultMsgGroup: 'toast',
      resultMsg: '续租失败'
    }));

    const ToastFunc = jest.spyOn(Toast, 'show');

    // 获取saga执行记录
    const dispatched = await recordSaga(saveRenewalOrderLogic, {
      action: {
        type: Types.SAVE_RENEWAL_ORDER,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: '11111',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: false,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
        }
      },
    });

    expect(FetchApi).toBeCalled();
    expect(ToastFunc).toBeCalledWith('续租失败');
  })
})


describe('createContinuePaymentReferenceNoLogic', () => {
  test('续租加购保险，跳转保险代理', async () => {

    AppContext.setPageInstance({
      getPageId: () => 10650047205,
    })

    const goToInsFun = jest.fn();

    // mock接口返回数据
    const FetchApi = jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(Promise.resolve({
      insuranceFlag: true,
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePaymentReferenceNoLogic, {
      action: {
        type: Types.CREATE_CONTINUE_PAYMENT_REFERENCE_NO,
        data: {
          goToInsFun,
        },
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: '',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [],
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
          driverInfo: {
            areaCode: '+86',
            decryptIDCardNo: '310111199208080000',
            decryptTelphone: '15800000000',
            distributionEmail: '',
            distributionMobile: '1583V7=0000',
            email: '',
            encrypIDCardNo: '310111iD7Hi08ObJ30#',
            flightNo: '',
            iDCardNo: '3101********00',
            iDCardType: 1,
            name: '张三',
            telphone: '158****0000',
          },
        }
      },
    });

    expect(FetchApi).toBeCalled();
    expect(goToInsFun).toBeCalled();
  })

  test('未添加保险，请求继续支付接口成功', async () => {

    AppContext.setPageInstance({
      getPageId: () => 10650047205,
    })

    const goToInsFun = jest.fn();

    // mock接口返回数据
    const FetchApi = jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(Promise.resolve({
      insuranceFlag: false,
    }));

    const fetchCtripContinuePayApi = jest.spyOn(CarFetch, 'ctripContinuePay').mockReturnValue(Promise.resolve({
      rst: true,
      referenceNo: 'referenceNo',
      amt: 100,
      payDeadline: '2022/10/20 10:30:00',
    }));

    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePaymentReferenceNoLogic, {
      action: {
        type: Types.CREATE_CONTINUE_PAYMENT_REFERENCE_NO,
        data: {
          goToInsFun,
        },
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: '',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [],
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
          driverInfo: {
            areaCode: '+86',
            decryptIDCardNo: '310111199208080000',
            decryptTelphone: '15800000000',
            distributionEmail: '',
            distributionMobile: '1583V7=0000',
            email: '',
            encrypIDCardNo: '310111iD7Hi08ObJ30#',
            flightNo: '',
            iDCardNo: '3101********00',
            iDCardType: 1,
            name: '张三',
            telphone: '158****0000',
          },
        }
      },
    });

    expect(FetchApi).toBeCalled();
    expect(fetchCtripContinuePayApi).toBeCalled();
    expect(dispatched).toContainEqual(Actions.goRerentPay({
      refNo: 'referenceNo',
      resAmount: 100,
      payDeadline: '2022/10/20 10:30:00',
    }));
  });


  test('未添加保险，请求继续支付接口失败', async () => {
    AppContext.setPageInstance({
      getPageId: () => 10650047205,
    })

    const goToInsFun = jest.fn();


    const ToastFunc = jest.spyOn(Toast, 'show');

    // mock接口返回数据
    const FetchApi = jest.spyOn(CarFetch, 'getQConfig').mockReturnValue(Promise.resolve({
      insuranceFlag: false,
    }));

    const fetchCtripContinuePayApi = jest.spyOn(CarFetch, 'ctripContinuePay').mockReturnValue(Promise.resolve({
      rst: false,
      resultMsgGroup: 'toast',
      resultMessage: 'resultMessage',
    }));

    // 获取saga执行记录
    const dispatched = await recordSaga(createContinuePaymentReferenceNoLogic, {
      action: {
        type: Types.CREATE_CONTINUE_PAYMENT_REFERENCE_NO,
        data: {
          goToInsFun,
        },
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: '',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [],
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
          driverInfo: {
            areaCode: '+86',
            decryptIDCardNo: '310111199208080000',
            decryptTelphone: '15800000000',
            distributionEmail: '',
            distributionMobile: '1583V7=0000',
            email: '',
            encrypIDCardNo: '310111iD7Hi08ObJ30#',
            flightNo: '',
            iDCardNo: '3101********00',
            iDCardType: 1,
            name: '张三',
            telphone: '158****0000',
          },
        }
      },
    });

    expect(FetchApi).toBeCalled();
    expect(fetchCtripContinuePayApi).toBeCalled();
    expect(ToastFunc).toBeCalledWith('resultMessage', 3);
  });
})



describe('goRerentPayLogic', () => {
  test('续租支付成功', async () => {
    const ToastFunc = jest.spyOn(Toast, 'show');
    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() => Promise.resolve({
      success: true,
      showError: true,
      status: null,
      result: null
    }))
    // 获取saga执行记录
    const dispatched = await recordSaga(goRerentPayLogic, {
      action: {
        type: Types.GO_RENEWAL_PAY,
        data: {
          resAmount: 100,
          refNo: 'refNo',
          payDeadline: '2022/09/22 09:10:33',
        },
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: 'renewalOrderId',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [{
            renewalOrderId: 'renewalOrderId',
            chargeInfoList: [{
              chargeName: 'chargeName',
              currencyCode: 'CNY',
              currentTotalPrice: '100',
            }]
          }],
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
          vendorInfo: {
            "vendorName": "鹏琛租车",
            "vendorImageUrl": "http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg",
            "vendorID": 62494,
            "vendorConfirmCode": "3082488752",
            "isSelf": false,
            "selfName": "",
            "vendorMobileImageUrl": "",
            "commentInfo": {
                "vendorGoodType": 0,
                "exposedScore": 0,
                "topScore": 5,
                "level": "",
                "commentCount": 2,
                "hasComment": 0
            }
        },
          driverInfo: {
            areaCode: '+86',
            decryptIDCardNo: '310111199208080000',
            decryptTelphone: '15800000000',
            distributionEmail: '',
            distributionMobile: '1583V7=0000',
            email: '',
            encrypIDCardNo: '310111iD7Hi08ObJ30#',
            flightNo: '',
            iDCardNo: '3101********00',
            iDCardType: 1,
            name: '张三',
            telphone: '158****0000',
          },
        }
      },
    });
    expect(ToastFunc).toBeCalledWith('支付成功')
  })

  test('续租支付失败', async () => {
    const ToastFunc = jest.spyOn(Toast, 'show');
    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() => Promise.resolve({
      success: false,
      showError: true,
      status: null,
      result: null
    }))
    // 获取saga执行记录
    const dispatched = await recordSaga(goRerentPayLogic, {
      action: {
        type: Types.GO_RENEWAL_PAY,
        data: {
          resAmount: 100,
          refNo: 'refNo',
          payDeadline: '2022/09/22 09:10:33',
        },
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: 'renewalOrderId',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [{
            renewalOrderId: 'renewalOrderId',
            chargeInfoList: [{
              chargeName: 'chargeName',
              currencyCode: 'CNY',
              currentTotalPrice: '100',
            }]
          }],
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
          vendorInfo: {
            "vendorName": "鹏琛租车",
            "vendorImageUrl": "http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg",
            "vendorID": 62494,
            "vendorConfirmCode": "3082488752",
            "isSelf": false,
            "selfName": "",
            "vendorMobileImageUrl": "",
            "commentInfo": {
                "vendorGoodType": 0,
                "exposedScore": 0,
                "topScore": 5,
                "level": "",
                "commentCount": 2,
                "hasComment": 0
            }
        },
          driverInfo: {
            areaCode: '+86',
            decryptIDCardNo: '310111199208080000',
            decryptTelphone: '15800000000',
            distributionEmail: '',
            distributionMobile: '1583V7=0000',
            email: '',
            encrypIDCardNo: '310111iD7Hi08ObJ30#',
            flightNo: '',
            iDCardNo: '3101********00',
            iDCardType: 1,
            name: '张三',
            telphone: '158****0000',
          },
        }
      },
    });
    expect(ToastFunc).toBeCalledWith('支付失败')
  })


  test('续租支付异常', async () => {
    const ToastFunc = jest.spyOn(Toast, 'show');
    jest.spyOn(Payment, 'MiddlePay').mockImplementation(() => {
      throw new Error("test");
    })
    // 获取saga执行记录
    const dispatched = await recordSaga(goRerentPayLogic, {
      action: {
        type: Types.GO_RENEWAL_PAY,
        data: {
          resAmount: 100,
          refNo: 'refNo',
          payDeadline: '2022/09/22 09:10:33',
        },
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: 'renewalOrderId',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [{
            renewalOrderId: 'renewalOrderId',
            chargeInfoList: [{
              chargeName: 'chargeName',
              currencyCode: 'CNY',
              currentTotalPrice: '100',
            }]
          }],
          pickupStore: {
            "localDateTime": "2021-06-20 10:00:00",
          },
          returnStore: {
            "localDateTime": "2021-06-22 10:00:00",
          },
          vendorInfo: {
            "vendorName": "鹏琛租车",
            "vendorImageUrl": "http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg",
            "vendorID": 62494,
            "vendorConfirmCode": "3082488752",
            "isSelf": false,
            "selfName": "",
            "vendorMobileImageUrl": "",
            "commentInfo": {
                "vendorGoodType": 0,
                "exposedScore": 0,
                "topScore": 5,
                "level": "",
                "commentCount": 2,
                "hasComment": 0
            }
        },
          driverInfo: {
            areaCode: '+86',
            decryptIDCardNo: '310111199208080000',
            decryptTelphone: '15800000000',
            distributionEmail: '',
            distributionMobile: '1583V7=0000',
            email: '',
            encrypIDCardNo: '310111iD7Hi08ObJ30#',
            flightNo: '',
            iDCardNo: '3101********00',
            iDCardType: 1,
            name: '张三',
            telphone: '158****0000',
          },
        }
      },
    });
    expect(dispatched).toEqual([
      Actions.setModalVisible({
        name: ModalNames.NormalTip,
        title: '系统异常，如需继续续租请联系门店',
        visible: true,
        isNeedPop: true,
      })
    ])
  })
})



describe('cancelRenewalOrderLogic', () => {
  test('取消续租成功', async () => {
    const ToastFunc = jest.spyOn(Toast, 'show');
    // mock接口返回数据
    const FetchApi = jest.spyOn(CarFetch, 'cancelRenewalOrder').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(cancelRenewalOrderLogic, {
      action: {
        type: Types.CANCLE_RENEWAL_ORDER,
        data: {
          resAmount: 100,
          refNo: 'refNo',
          payDeadline: '2022/09/22 09:10:33',
        },
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: 'renewalOrderId',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [{
            renewalOrderId: 'renewalOrderId',
            chargeInfoList: [{
              chargeName: 'chargeName',
              currencyCode: 'CNY',
              currentTotalPrice: '100',
            }]
          }],
        }
      },
    });
    expect(ToastFunc).toBeCalledWith('续租已取消')
    expect(FetchApi).toBeCalled();
  })

  test('状态不一致，导致无法取消', async () => {
    const ToastFunc = jest.spyOn(Toast, 'show');
    // mock接口返回数据
    const FetchApi = jest.spyOn(CarFetch, 'cancelRenewalOrder').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: false,
        code: '-3',
      },
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(cancelRenewalOrderLogic, {
      action: {
        type: Types.CANCLE_RENEWAL_ORDER,
        data: {
          resAmount: 100,
          refNo: 'refNo',
          payDeadline: '2022/09/22 09:10:33',
        },
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: 'renewalOrderId',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [{
            renewalOrderId: 'renewalOrderId',
            chargeInfoList: [{
              chargeName: 'chargeName',
              currencyCode: 'CNY',
              currentTotalPrice: '100',
            }]
          }],
        }
      },
    });
    expect(ToastFunc).toBeCalledWith('订单状态更新');
    expect(FetchApi).toBeCalled();
    expect(dispatched).toEqual([
      Actions.queryRenewalProduct(),
    ])
  })

  test('取消失败，其他异常', async () => {
    const ToastFunc = jest.spyOn(Toast, 'show');
    // mock接口返回数据
    const FetchApi = jest.spyOn(CarFetch, 'cancelRenewalOrder').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: false,
        returnMsg: 'returnMsg',
      },
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(cancelRenewalOrderLogic, {
      action: {
        type: Types.CANCLE_RENEWAL_ORDER,
        data: {
          resAmount: 100,
          refNo: 'refNo',
          payDeadline: '2022/09/22 09:10:33',
        },
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: 'renewalOrderId',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [{
            renewalOrderId: 'renewalOrderId',
            chargeInfoList: [{
              chargeName: 'chargeName',
              currencyCode: 'CNY',
              currentTotalPrice: '100',
            }]
          }],
        }
      },
    });
    expect(FetchApi).toBeCalled();
    expect(dispatched).toEqual([
      Actions.setModalVisible({
        name: 'NormalTip',
        title: 'returnMsg',
        visible: true,
      }),
    ])
  })
});


describe('setRenewalOrderIdLogic', () => {
  test('设置当前续租订单', async () => {
    // 获取saga执行记录
    const dispatched = await recordSaga(setRenewalOrderIdLogic, {
      action: {
        type: Types.SET_RENEWAL_ORDERID,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: 'renewalOrderId',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [{
            renewalOrderId: 'renewalOrderId',
            chargeInfoList: [{
              chargeName: 'chargeName',
              currencyCode: 'CNY',
              currentTotalPrice: '100',
            }],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          }],
        }
      },
    });

    expect(dispatched).toEqual([
      Actions.setAddInsurance({
        addInsurance: true,
      })
    ])
  })
});



describe('queryLimitContent', () => {
  test('获取限行数据', async () => {
    const FetchApi = jest.spyOn(CarFetch, 'getLimitContent').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      result: {
        isSuccess: true,
      },
      test: 1111,
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(queryLimitContent, {
      action: {
        type: Types.QUERY_LIMIT_CONTENT_DATA,
        data: {},
      },
      state: {
        Rerent: {
          rerentRTime: '2022/09/22 09:10:33',
          renewalOrderId: 'renewalOrderId',
          productInfo: {
            totalAmount: 100,
            continueRateFreeList: [{}],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          },
          addInsurance: true,
        },
        OrderDetail: {
          reqOrderParams: {
            orderId: 11111,
          },
          renewalOrders: [{
            renewalOrderId: 'renewalOrderId',
            chargeInfoList: [{
              chargeName: 'chargeName',
              currencyCode: 'CNY',
              currentTotalPrice: '100',
            }],
            ctripInsurance: {
              productId: '11111',
              name: '人身财务险',
              sellingPrice: '58',
            }
          }],
        }
      },
    });

    expect(dispatched).toEqual([
      Actions.queryLimitContentSuccess({
        limitCont: {
          baseResponse: {
            isSuccess: true,
          },
          result: {
            isSuccess: true,
          },
          test: 1111,
        },
      })
    ])
  })
});
