import Reducer, { getInitalState, getServiceIdsStr } from '../../../src/pages/xcar/State/Service/Reducer';

import * as Types from '../../../src/pages/xcar/State/Service/Types';
import * as Actions from '../../../src/pages/xcar/State/Service/Actions';

test('getServiceIdsStr', () => {
  expect(getServiceIdsStr([
    {
      serviceId: "47191567",
      createTime: "2022-07-20 17:29:51",
      progressList: [
        {
          title: "已收到您的咨询，预计最晚7月20日20:29:00前处理完成",
          description: "2022-07-20 17:29:51",
          type: 0
        },
        {
          title: "已分配客服处理",
          description: "2022-07-20 17:30:18",
          type: 0
        },
        {
          title: "客服处理中，请耐心等待",
          description: "2022-07-20 17:30:35",
          type: 0
        }
      ],
      status: "3"
    }
  ])).toEqual('47191567');

  expect(getServiceIdsStr()).toEqual('');
})



describe('测试直接赋值的reducer', () => {
  const initialState = getInitalState();
  const map = [
    {
      type: Types.FETCH_QUERY_SERVICEPROGRESS_CALLBACK,
      data: {
        status: "3",
        title: "您的咨询已在处理中",
        desc: "",
        latestTime: "07-20 17:30",
        serviceProgressDTO: [
          {
            serviceId: "47191567",
            createTime: "2022-07-20 17:29:51",
            progressList: [
              {
                title: "已收到您的咨询，预计最晚7月20日20:29:00前处理完成",
                description: "2022-07-20 17:29:51",
                type: 0
              },
              {
                title: "已分配客服处理",
                description: "2022-07-20 17:30:18",
                type: 0
              },
              {
                title: "客服处理中，请耐心等待",
                description: "2022-07-20 17:30:35",
                type: 0
              }
            ],
            status: "3"
          }
        ],
        attrDto: {
          sort: 3,
          history: "false"
        }
      },
      expected: {
        serviceProgressList: [
          {
            serviceId: "47191567",
            createTime: "2022-07-20 17:29:51",
            progressList: [
              {
                title: "已收到您的咨询，预计最晚7月20日20:29:00前处理完成",
                description: "2022-07-20 17:29:51",
                type: 0
              },
              {
                title: "已分配客服处理",
                description: "2022-07-20 17:30:18",
                type: 0
              },
              {
                title: "客服处理中，请耐心等待",
                description: "2022-07-20 17:30:35",
                type: 0
              }
            ],
            status: "3"
          }
        ],
        serviceStatus: "3",
        serviceTitle: "您的咨询已在处理中",
        serviceDesc: "",
        serviceLatestTime: "07-20 17:30",
        serviceIds: '47191567',
        urgeServiceIds: undefined,
        serviceCardHistory: 'false',
      },
    },
    {
      type: Types.URGE_SERVICEPROGRESS_CALLBACK,
      data: {
        urgeServiceIds: []
      },
      expected: {
        urgeServiceIds: []
      },
    },
  ];
  test.each(map)('%p', ({ type, data, expected }) => {
    const newState = Reducer(initialState, {
      type,
      data,
    });
    expect(newState).toEqual({
      ...initialState,
      ...expected,
    });
  });
});
