import { xShowToast } from '@ctrip/xtaro';
import { recordSaga } from '../../testHelpers';
import { fetchQueryServiceProgress, urgeServiceProgress } from '../../../src/pages/xcar/State/Service/Logic';
import { AppContext, CarFetch, CarStorage } from '../../../src/pages/xcar/Util/Index';
import * as Types from '../../../src/pages/xcar/State/Service/Types';
import * as Actions from '../../../src/pages/xcar/State/Service/Actions';

describe('fetchQueryServiceProgress', () => {
  test('fetchQueryServiceProgress 正常运行', async () => {
    // mock接口返回数据
    const queryServiceProgressApi = jest.spyOn(CarFetch, 'queryServiceProgress').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
        code: "unknown",
        returnMsg: "success",
        requestId: "",
        cost: 119
      },
      status: "3",
      title: "您的咨询已在处理中",
      desc: "",
      latestTime: "07-20 17:30",
      serviceProgressDTO: [
        {
          serviceId: "47191567",
          createTime: "2022-07-20 17:29:51",
          progressList: [
            {
              title: "已收到您的咨询，预计最晚7月20日20:29:00前处理完成",
              description: "2022-07-20 17:29:51",
              type: 0
            },
            {
              title: "已分配客服处理",
              description: "2022-07-20 17:30:18",
              type: 0
            },
            {
              title: "客服处理中，请耐心等待",
              description: "2022-07-20 17:30:35",
              type: 0
            }
          ],
          status: "3"
        }
      ],
      attrDto: {
        sort: 3,
        history: "false"
      }
    }));

    jest.spyOn(CarStorage, 'loadAsync').mockResolvedValueOnce('[11111]');
    // 获取saga执行记录
    const dispatched = await recordSaga(fetchQueryServiceProgress, {
      action: {
        type: Types.FETCH_QUERY_SERVICEPROGRESS,
        data: {},
      },
      state: {},
    });

    expect(queryServiceProgressApi).toBeCalled();
    expect(dispatched).toEqual([Actions.queryServiceProgressCallBack({
      baseResponse: {
        isSuccess: true,
        code: "unknown",
        returnMsg: "success",
        requestId: "",
        cost: 119
      },
      status: "3",
      title: "您的咨询已在处理中",
      desc: "",
      latestTime: "07-20 17:30",
      serviceProgressDTO: [
        {
          serviceId: "47191567",
          createTime: "2022-07-20 17:29:51",
          progressList: [
            {
              title: "已收到您的咨询，预计最晚7月20日20:29:00前处理完成",
              description: "2022-07-20 17:29:51",
              type: 0
            },
            {
              title: "已分配客服处理",
              description: "2022-07-20 17:30:18",
              type: 0
            },
            {
              title: "客服处理中，请耐心等待",
              description: "2022-07-20 17:30:35",
              type: 0
            }
          ],
          status: "3"
        }
      ],
      attrDto: {
        sort: 3,
        history: "false"
      },
      urgeServiceIds: [11111],
    })]);
  })
})



describe('urgeServiceProgress', () => {
  test('urgeServiceProgress 正常运行', async () => {
    // mock接口返回数据
    const urgeServiceProgressApi = jest.spyOn(CarFetch, 'urgeServiceProgress').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
        code: "unknown",
        returnMsg: "success",
        requestId: "",
        cost: 119
      },
    }));

    // 获取saga执行记录
    const dispatched = await recordSaga(urgeServiceProgress, {
      action: {
        type: Types.URGE_SERVICEPROGRESS,
        data: {
          eventId: 222222,
        },
      },
      state: {
        Service: {
          urgeServiceIds: [111111]
        }
      },
    });

    expect(urgeServiceProgressApi).toBeCalled();
    expect(dispatched).toEqual([Actions.urgeServiceProgressCallBack({
      urgeServiceIds: [111111, 222222],
    })]);
  })

  test('urgeServiceProgress 接口失败', async () => {
    // mock接口返回数据
    const urgeServiceProgressApi = jest.spyOn(CarFetch, 'urgeServiceProgress').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: false,
        code: "unknown",
        returnMsg: "success",
        requestId: "",
        cost: 119
      },
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(urgeServiceProgress, {
      action: {
        type: Types.URGE_SERVICEPROGRESS,
        data: {
          eventId: 222222,
        },
      },
      state: {
        Service: {
          urgeServiceIds: [111111]
        }
      },
    });

    expect(urgeServiceProgressApi).toBeCalled();
    expect(xShowToast).toBeCalledWith({"duration": 3000, "title": "出错了，请稍后重试"});
  })
})
