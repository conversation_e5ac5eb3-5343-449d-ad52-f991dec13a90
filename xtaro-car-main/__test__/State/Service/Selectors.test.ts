import {
  getServiceProgressList,
  getServiceStatus,
  getServiceTitle,
  getServiceDesc,
  getServiceLatestTime,
  getUrgeServiceIds,
  getServiceIds,
  getService,
} from '../../../src/pages/xcar/State/Service/Selectors';

import { getInitalState } from '../../../src/pages/xcar/State/Service/Reducer';

test('Simple Selector Check', () => {
  const initalState = getInitalState();
  const state = {
    Service: initalState,
  };
  const needCheckFunc = [
    getServiceProgressList,
    getServiceStatus,
    getServiceTitle,
    getServiceDesc,
    getServiceLatestTime,
    getUrgeServiceIds,
    getServiceIds,
    getService,
  ];
  needCheckFunc.forEach((selector) => {
    expect(selector(state)).not.toBeUndefined();
  })
})
