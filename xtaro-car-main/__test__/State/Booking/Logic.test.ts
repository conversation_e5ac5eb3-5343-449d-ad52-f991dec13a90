import {
  closeInsuranceLoading,
  create,
  insConfirmCallback,
  fetchRebookCancelFee,
  snapshot,
  validateFlightNo,
  refresh,
} from '../../../src/pages/xcar/State/Booking/Logic';
import * as Actions from '../../../src/pages/xcar/State/Booking/Actions';
import * as Types from '../../../src/pages/xcar/State/Booking/Types';
import {
  CarFetch,
  CarABTesting,
  AppContext,
  Utils,
} from '../../../src/pages/xcar/Util/Index';
import { recordSaga } from '../../testHelpers';
import { setCouponPreValidationModalVisible } from '../../../src/pages/xcar/State/Coupon/Actions';
import ProductReqAndResData from '../../../src/pages/xcar/Global/Cache/ProductReqAndResData';

jest.mock('../../../src/pages/xcar/State/OrderDetail/Selectors', () => {
  const origins = jest.requireActual(
    '../../../src/pages/xcar/State/OrderDetail/Selectors',
  );
  return {
    ...origins,
    getCustomerInfo: jest.fn(), // 解决循环引用
  };
});
jest.mock('../../../src/pages/xcar/State/List/Mappers', () => {
  const origins = jest.requireActual('../../../src/pages/xcar/State/List/Mappers');
  return {
    ...origins,
    getLimitInfo: jest.fn(), // 解决循环引用
  };
});
jest.mock('../../../src/pages/xcar/State/Product/Mappers', () => {
  return {
    // 解决循环引用
    getCurPriceInfo: jest.fn(),
    getCurProductInfo: jest.fn(),
    getProduct: () => ({}),
    getCurPackageIsEasyLife: jest.fn(),
    getCrossIslandInfo: () => ({}),
    getCrossPlacesMap: () => ({}),
    getCurPackage: () => ({}),
    getCurCtripInsuranceIds: () => [],
    getCurPayModeInfo: () => ({}),
    getCurDepositPayInfo: () => ({}),
    getIsKlb: () => ({}),
  };
});

jest.mock('../../../src/pages/xcar/State/List/Selectors', () => {
  const origins = jest.requireActual('../../../src/pages/xcar/State/List/Selectors');
  return {
    ...origins,
    getSelectedfilterLabels: jest.fn(),
    getBitsFilter: jest.fn(), // 解决循环引用
    getSaleOutList: jest.fn(), // 解决循环引用
  };
});
const state = {
  LocationAndDate: {
    rentalLocation: {
      pickUp: {
        area: { name: 'name' },
      },
      dropOff: {
        area: { name: 'name' },
      },
    },
  },
  DriverAgeAndNumber: { age: '30' },
  Product: { curPackageId: 'curPackageId' },
  Booking: { driverInfo: [] },
  DriverList: {
    passenger: {
      age: 30,
    },
    curCertificates: {},
    availableCertificates: [],
  },
  ProductConfirm: { request: {} },
};

describe('测试 create Logic ', () => {
  const testOrderFunc = async (result, data?) => {
    jest
      .spyOn(CarFetch, 'createOrder')
      .mockReturnValueOnce(Promise.resolve(result));
    const dispatched = await recordSaga(create, {
      action: {
        type: Types.CREATEORDER,
        data,
      },
      state,
    });
    return dispatched;
  };
  test('测试 create 下单成功 ', async () => {
    const successResult = {
      baseResponse: { isSuccess: true },
      resInfo: { orderId: 12345 },
    };
    const dispatched = await testOrderFunc(successResult);
    expect(dispatched[4]).toEqual(Actions.setOrderInfo(successResult.resInfo));
  });
  test('测试 create 重复订单 ', async () => {
    const successResult = {
      baseResponse: { isSuccess: true },
      resInfo: { uniqueOrderId: 12345 },
    };
    const dispatched = await testOrderFunc(successResult);
    expect(dispatched[3]).toEqual(Actions.setUniqueOrder(12345));
  });
  test('测试 create 验单成功 ', async () => {
    const successResult = {
      baseResponse: { isSuccess: true },
      resInfo: { payAmountInfo: '' },
    };
    const dispatched = await testOrderFunc(successResult, {
      isCheckOrder: true,
    });
    expect(dispatched[2]).toEqual(
      Actions.setPreLicensing(successResult.resInfo),
    );
  });
  test('测试 create 验单失败 ', async () => {
    const successResult = {
      baseResponse: { isSuccess: true },
      resInfo: {},
    };
    const dispatched = await testOrderFunc(successResult, {
      isCheckOrder: true,
    });
    expect(dispatched[3]).toEqual(Actions.setPreLicensing(null));
  });
  test('测试 create 航班号失败Code: 2002 ', async () => {
    const successResult = {
      baseResponse: {
        code: '2002',
      },
      resInfo: {},
    };
    const dispatched = await testOrderFunc(successResult);
    expect(dispatched[4]).toEqual(
      Actions.setFlightErrorTip(
        '您的航班号信息并不存在，请确认您所输入的航班号信息',
      ),
    );
    expect(dispatched[5]).toEqual(
      Actions.changeFormData([{ type: 'flightNumber', error: true }]),
    );
  });
  test('测试 create 航班号失败Code: 2003 ', async () => {
    const successResult = {
      baseResponse: {
        code: '2003',
      },
      resInfo: {},
    };
    const dispatched = await testOrderFunc(successResult);
    expect(dispatched[4]).toEqual(
      Actions.setFlightErrorTip(
        '航班号到达机场和取车机场不符，请再次确认您的航班号信息, 或更换非必填航班号门店进行下单',
      ),
    );
    expect(dispatched[5]).toEqual(
      Actions.changeFormData([{ type: 'flightNumber', error: true }]),
    );
  });
  test('测试 create 下单失败打底 ', async () => {
    const successResult = {
      baseResponse: {
        extMap: {
          scode: 'S1',
        },
      },
      resInfo: {},
    };
    const dispatched = await testOrderFunc(successResult);
    expect(dispatched[4]).toEqual(
      Actions.setBaseResInfo(successResult.baseResponse),
    );
  });
  test('测试 create CouponPreValidation ', async () => {
    const successResult = {
      baseResponse: {
        isSuccess: true,
      },
      resInfo: {
        resultInfo: { code: '51' },
      },
    };
    const dispatched = await testOrderFunc(successResult);
    expect(dispatched[4]).toEqual(
      setCouponPreValidationModalVisible(
        true,
        successResult.resInfo.resultInfo,
      ),
    );
  });

  test('测试 create 接口异常 ', async () => {
    jest
      .spyOn(CarFetch, 'createOrder')
      .mockReturnValueOnce(Promise.reject(null));
    const dispatched = await recordSaga(create, {
      action: {
        type: Types.CREATEORDER,
      },
      state,
    });
    expect(dispatched[1]).toEqual(Actions.createOrderFail());
  });
});
describe('测试 snapshot Logic ', () => {
  const testFn = async promise => {
    const api = jest
      .spyOn(CarFetch, 'saveSnapshot')
      .mockReturnValueOnce(promise);
    await recordSaga(snapshot, {
      action: {
        type: Types.SNAPSHOT,
      },
      state,
    });
    return api;
  };
  test('测试 snapshot ', async () => {
    const saveSnapshotApi = await testFn(Promise.resolve({}));
    expect(saveSnapshotApi).toBeCalled();
  });
  test('测试 snapshot 异常', async () => {
    await testFn(Promise.reject({}));
  });
});

describe('测试 closeInsuranceLoading Logic ', () => {
  test('测试 closeInsuranceLoading', async () => {
    const dispatched = await recordSaga(closeInsuranceLoading, {
      action: {
        type: Types.CLOSE_INSURANCE_LOADING_VIEW,
        data: true,
      },
      state,
    });
    expect(dispatched[0]).toEqual(Actions.setCreateInsLoadingPopIsShow(true));
  });
  test('测试 closeInsuranceLoading 异常', async () => {
    const dispatched = await recordSaga(closeInsuranceLoading, {
      action: {
        type: Types.CLOSE_INSURANCE_LOADING_VIEW,
      },
      state,
    });
  });
});

describe('测试 insConfirmCallback Logic ', () => {
  AppContext.setPageInstance({
    getPageId: () => '222017',
  });
  test('测试 insConfirmCallback', async () => {
    const result = {
      baseResponse: {
        isSuccess: true,
      },
      priceChangeInfo: {},
    };
    jest
      .spyOn(CarFetch, 'queryPriceInfo')
      .mockReturnValueOnce(Promise.resolve(result));
    const dispatched = await recordSaga(insConfirmCallback, {
      action: {
        type: Types.INSCONFIRM_CALLBACK,
        data: {
          insConfirmData: {},
          callbackFun: () => {},
          continueCreateOrder: () => {},
          logInsurancePageActiveTime: () => {},
        },
      },
      state,
    });
    expect(dispatched[2]).toEqual({
      type: 'Booking/SET_PRICECHANGE_POP_VISIBLE',
      data: true,
    });
  });
  test('测试 insConfirmCallback 国内逻辑', async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValueOnce(true);
    const dispatched = await recordSaga(insConfirmCallback, {
      action: {
        type: Types.INSCONFIRM_CALLBACK,
        data: {
          insConfirmData: {},
          callbackFun: () => {},
          continueCreateOrder: () => {},
          logInsurancePageActiveTime: () => {},
        },
      },
      state,
    });
  });
  test('测试 insConfirmCallback 价格接口返回失败', async () => {
    const result = {
      baseResponse: {
        isSuccess: false,
      },
      isSoldOut: false,
    };
    jest
      .spyOn(CarFetch, 'queryPriceInfo')
      .mockReturnValueOnce(Promise.resolve(result));
    const dispatched = await recordSaga(insConfirmCallback, {
      action: {
        type: Types.INSCONFIRM_CALLBACK,
        data: {
          insConfirmData: {},
          callbackFun: () => {},
          continueCreateOrder: () => {},
          logInsurancePageActiveTime: () => {},
        },
      },
      state,
    });
    expect(dispatched[1]).toEqual({
      type: 'Product/SET_STATUS',
      data: { isPriceFail: false, isPriceLoading: false },
    });
  });
  test('测试 insConfirmCallback 价格接口进入catch', async () => {
    jest
      .spyOn(CarFetch, 'queryPriceInfo')
      .mockReturnValueOnce(Promise.reject({}));
    const dispatched = await recordSaga(insConfirmCallback, {
      action: {
        type: Types.INSCONFIRM_CALLBACK,
        data: {
          insConfirmData: {},
          callbackFun: () => {},
          continueCreateOrder: () => {},
          logInsurancePageActiveTime: () => {},
        },
      },
      state,
    });
  });
});

describe('测试 fetchRebookCancelFee Logic ', () => {
  test('测试 fetchRebookCancelFee', async () => {
    jest.spyOn(CarFetch, 'queryCancelFee').mockReturnValueOnce(
      Promise.resolve({
        isSuccessful: true,
      }),
    );
    const dispatched = await recordSaga(fetchRebookCancelFee, {
      action: {
        type: Types.FETCH_CANCELFEE_REBOOK,
        data: true,
      },
      state,
    });
    expect(dispatched[0]).toEqual({
      data: {
        resCancelFee: {
          isSuccessful: true,
        },
      },
      type: 'Booking/FETCH_CANCELFEE_REBOOK_CALLBACK',
    });
  });
});

describe('测试 validateFlightNo Logic ', () => {
  test('测试 validateFlightNo', async () => {
    jest.spyOn(CarFetch, 'checkFlightNo').mockReturnValueOnce(
      Promise.resolve({
        isValid: true,
      }),
    );
    const dispatched = await recordSaga(validateFlightNo, {
      action: {
        type: Types.VALIDATE_FLIGHT_NO,
        data: {
          flightNo: 'AA2485',
          type: 'flightNumber',
        },
      },
      state,
    });
    expect(dispatched[0]).toEqual({
      data: true,
      type: 'Booking/SET_CHECK_FLIGHT_NO_LOADING',
    });
    expect(dispatched[1]).toEqual({
      data: false,
      type: 'Booking/SET_CHECK_FLIGHT_NO_LOADING',
    });
  });
});

describe('测试 refresh Logic ', () => {
  test('测试 refresh', async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValueOnce(true);
    const dispatched = await recordSaga(refresh, {
      action: { type: Types.REFRESH },
      state,
    });
    expect(dispatched[0]).toEqual({
      type: 'Product/RESET',
    });
    expect(dispatched[1]).toEqual({
      type: 'Booking/CLEAR_WITHOUT_VENDOR_INFO',
    });
    expect(dispatched[2]).toEqual({
      data: {
        reset: true,
        isStartPriceTimer: true,
        isBooking: true,
        clearVcCacheKey: true,
      },
      type: 'Product/QUERY_PRODUCT',
    });
  });
});

describe('测试 境外 refresh Logic ', () => {
  test('测试 境外 refresh', async () => {
    ProductReqAndResData.setData(ProductReqAndResData.keyList.productReq, {
      reference: {
        age: 30,
        bizVendorCode: '14001',
        bomCode: 'LAXZE01_10387_FRFB_Fees_SLDW_ULM_0_0',
        decoratorVendorType: 0,
        isEasyLife: false,
        pCityId: 347,
        pHub: undefined,
        pStoreCode: 'LAXZE01',
        packageId: '18861723',
        packageType: 0,
        payMode: 1,
        priceType: undefined,
        priceVersion: 'ARoAsoxiXjQiBtiquHuXvFA83cBBNMCG69SUqWOoLpZkNEU=',
        productCode: '10434814',
        rCityId: 347,
        rHub: undefined,
        rStoreCode: 'LAXZE01',
        rateCode: 'ABC',
        sippCode: 'ICAR',
        skuId: 4965062,
        vehicleCode: '10387',
        vendorCode: 'SD0003',
        vendorVehicleCode: 'ICAR_MAZDA34DOOR',
        klbVersion: 1,
      },
    });
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValueOnce(false);

    const dispatched = await recordSaga(refresh, {
      action: { type: Types.REFRESH },
      state,
    });
    expect(dispatched[0]).toEqual({
      data: {},
      type: 'Booking/ERRORINFO',
    });
    expect(dispatched[1]).toEqual({
      data: {
        reference: {
          age: 30,
          bizVendorCode: '14001',
          bomCode: 'LAXZE01_10387_FRFB_Fees_SLDW_ULM_0_0',
          decoratorVendorType: 0,
          isEasyLife: false,
          pCityId: 347,
          pHub: undefined,
          pStoreCode: 'LAXZE01',
          packageId: '18861723',
          packageType: 0,
          payMode: 1,
          priceType: undefined,
          priceVersion: 'ARoAsoxiXjQiBtiquHuXvFA83cBBNMCG69SUqWOoLpZkNEU=',
          productCode: '10434814',
          rCityId: 347,
          rHub: undefined,
          rStoreCode: 'LAXZE01',
          rateCode: 'ABC',
          sippCode: 'ICAR',
          skuId: 4965062,
          vehicleCode: '10387',
          vendorCode: 'SD0003',
          vendorVehicleCode: 'ICAR_MAZDA34DOOR',
          klbVersion: 1,
        },
      },
      type: 'Product/QUERY_PRODUCT',
    });
  });
});
