import {
  getBookingStoreGuidInfos,
  getCheckFlightNoLoading,
  getFlightErrorTip,
} from '../../../src/pages/xcar/State/Booking/Selectors';

jest.mock('../../../src/pages/xcar/State/ProductConfirm/Selectors', () => {
  const originalModule = jest.requireActual(
    '../../../src/pages/xcar/State/ProductConfirm/Selectors',
  );
  return {
    __esModule: true,
    ...originalModule,
    getQueryVehicleDetailInfoStoreGuidInfos: jest.fn(() => {
      return [
        {
          storeGuid: '免费站内取还车 携程租车中心内',
          address: '地址：三亚凤凰国际机场停车楼4楼',
          type: 1,
        },
      ];
    }),
  };
});

jest.mock('../../../src/pages/xcar/Global/Cache/ProductSelectors', () => {
  const originalModule = jest.requireActual(
    '../../../src/pages/xcar/Global/Cache/ProductSelectors',
  );
  return {
    __esModule: true,
    ...originalModule,
    getQueryProductInfoStoreGuidInfos: jest.fn(() => {
      return [
        {
          storeGuid: '免费站内取还车 携程租车中心内',
          address: '地址：三亚凤凰国际机场停车楼4楼',
          type: 2,
        },
      ];
    }),
    getBookingFirstScreenParam: jest.fn(() => {
      return {};
    }),
  };
});

describe('Booking Selectors getBookingStoreGuidInfos', () => {
  const mockStateMap = [
    {
      state: {},
      expected: [
        {
          storeGuid: '免费站内取还车 携程租车中心内',
          address: '地址：三亚凤凰国际机场停车楼4楼',
          type: 1,
        },
      ],
    },
  ];
  test.each(mockStateMap)(
    'getBookingStoreGuidInfos check',
    ({ state, expected }) => {
      const data = getBookingStoreGuidInfos(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Booking Selectors getCheckFlightNoLoading', () => {
  const mockStateMap = [
    {
      state: {
        Booking: {
          checkFlightNoLoading: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Booking: {
          checkFlightNoLoading: false,
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getCheckFlightNoLoading check',
    ({ state, expected }) => {
      const data = getCheckFlightNoLoading(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Booking Selectors getFlightErrorTip', () => {
  const mockStateMap = [
    {
      state: {
        Booking: {
          flightErrorTip: '',
        },
      },
      expected: '',
    },
    {
      state: {
        Booking: {
          flightErrorTip: '航班不存在',
        },
      },
      expected: '航班不存在',
    },
  ];
  test.each(mockStateMap)('getFlightErrorTip check', ({ state, expected }) => {
    const data = getFlightErrorTip(state);
    expect(data).toEqual(expected);
  });
});
