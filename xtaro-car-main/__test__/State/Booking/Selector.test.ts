import {
  getNewCouponTip,
  getNewDepositData,
  validateIsMergeDeposit,
  getAuthInfo,
  getDepositMethodData,
  getDepositDescriptionSectionData,
  getDriversMap,
  getBookingLogParams,
} from '../../../src/pages/xcar/State/Booking/Selectors';

import { Utils } from '../../../src/pages/xcar/Util/Index';
import * as ProductSelectors from '../../../src/pages/xcar/Global/Cache/ProductSelectors';
import ProductReqAndResData from '../../../src/pages/xcar/Global/Cache/ProductReqAndResData';

jest.mock('../../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));

ProductReqAndResData.setData(ProductReqAndResData.keyList.productRes, {
  promptInfos: [
    {
      title:
        '洛杉矶国际机场 is a popular location on your selected dates. Rates are likely to rise.',
      subTitle: "Book now! Don't miss out on low prices.",
      type: 29,
    },
  ],
  commentInfo: {
    overallRating: '4.5',
    vendorDesc: '0',
    level: '满意',
    maximumRating: 5,
    commentLabel: '',
    link: '/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=STORE_QUERY&storeId=LAXZE01&vehicleId=10177&vehicleName=雪佛兰 Spark 4门版&productCategoryId=34&isHideNavBar=YES',
    hasComment: 1,
    commentCount: 581,
  },
  baseResponse: {
    extMap: {},
    hasResult: true,
    code: '200',
    apiResCodes: [],
    returnMsg: 'OK',
    message: 'OK',
    requestId: '834f2656-8863-4374-b276-2d3530dd2227',
    isSuccess: true,
  },
  isSoldOut: false,
  pickupStoreInfo: {
    workTime: { openTimeDesc: '{"":"24小时营业"}' },
    isAirportStore: true,
    storeGuild:
      '从机场到达大厅出来后就能明显看到紫红色的“rental car shuttles”标示，这里是各个租车公司的巴士停靠位置，搭乘接驳巴士前往租车柜台办理取车手续即可（约10分钟一班）。车程约5分钟。',
    countryName: '美国',
    bizVendorCode: '14001',
    longitude: -118.384484,
    cityName: '洛杉矶',
    latitude: 33.95427,
    storeServiceList: [
      {
        title: '电子提车凭证',
        typeCode: 'F',
        description:
          '使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦',
      },
      {
        title: '中文表单',
        typeCode: 'F',
        description: '门店为携程客户提供额外的中文对照表单服务',
      },
    ],
    countryId: 66,
    storeWay: '机场外，可搭乘免费巴士到达',
    address: '9000 AIRPORT BLVD,LOS ANGELES',
    provinceName: '加利福尼亚州',
    storeCode: 'LAXZE01',
    storeName: 'LOS ANGELES AIRPORT',
    telephone: '001-3105685100',
    provinceId: 10125,
    cityId: 347,
    mapUrl: '',
  },
  designatedVehicleIntroduce: {
    title: '指定车型',
    description:
      '您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）',
  },
  returnStoreInfo: {
    workTime: { openTimeDesc: '{"":"24小时营业"}' },
    isAirportStore: true,
    storeGuild:
      '在门店停车场附近的道路旁通常能看到“Rental Car Return”的指示牌，按照指引将车辆驶入到对应的停车场办理还车手续，随后搭乘租车公司免费接驳巴士返回机场即可。',
    countryName: '美国',
    bizVendorCode: '14001',
    longitude: -118.384484,
    cityName: '洛杉矶',
    latitude: 33.95427,
    storeServiceList: [
      {
        title: '电子提车凭证',
        typeCode: 'F',
        description:
          '使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦',
      },
      {
        title: '中文表单',
        typeCode: 'F',
        description: '门店为携程客户提供额外的中文对照表单服务',
      },
    ],
    countryId: 66,
    storeWay: '机场外',
    address: '9000 AIRPORT BLVD,LOS ANGELES',
    provinceName: '加利福尼亚州',
    storeCode: 'LAXZE01',
    storeName: 'LOS ANGELES AIRPORT',
    telephone: '001-3105685100',
    provinceId: 10125,
    cityId: 347,
    mapUrl: '',
  },
  requestInfo: {
    rLongitude: -118.40853,
    pDate: '20230619110000',
    rDate: '20230622100000',
    pickupDate: '/Date(1687143600000+0800)/',
    sourceCountryId: 1,
    rLatitude: 33.941589,
    pLongitude: -118.40853,
    returnDate: '/Date(1687399200000+0800)/',
    pickupLocationName: '洛杉矶国际机场',
    pLatitude: 33.941589,
    returnLocationName: '洛杉矶国际机场',
  },
  vehicleInfo: {
    transmissionName: '自动挡',
    realityImageUrl: '//dimg04.c-ctrip.com/images/0410b12000atrxu0u51CA.jpg',
    isHot: false,
    imageList: ['//dimg04.c-ctrip.com/images/0410b12000atrxu0u51CA.jpg'],
    vehicleCode: '10177',
    conditionerDesc: 'A/C',
    recommendDesc: '小巧灵活，适合都市代步',
    similarCommentDesc: '空间类似菲亚特500',
    vendorSimilarVehicleInfos: [
      {
        vendorLogo: '//dimg04.c-ctrip.com/images/0AS5x120009e9t4h6F8D4.png',
        similarVehicleInfos: [
          {
            vehicleImageUrl:
              '//pic.c-ctrip.com/car/osd/online/vehicle_new/Chevrolet_Spark_4_Doors.png',
            vehicleCode: '10177',
            vehicleName: '雪佛兰 Spark 4门版',
          },
        ],
        vendorName: 'Hertz',
        bizVendorCode: '14001',
      },
    ],
    name: '雪佛兰 Spark 4门版',
    zhName: '雪佛兰 Spark 4门版',
    imageUrl:
      '//pic.c-ctrip.com/car/osd/online/vehicle_new/Chevrolet_Spark_4_Doors.png',
    brandId: 8,
    passengerNo: 4,
    hasConditioner: true,
    doorNo: 4,
    isSpecialized: false,
    groupCode: 'G02',
    userRealImageCount: 5,
    luggageNo: 3,
    brandEName: 'Chevrolet',
    groupSubClassCode: 'C',
    groupName: '小型轿车',
    transmissionType: 1,
    spaceDesc: '建议乘坐2人+1行李箱',
    groupSubName: '紧凑型轿车',
    userRealImageList: [
      'https://dimg04.c-ctrip.com/images/410r190000017wf5hF932.jpg',
      'https://dimg04.c-ctrip.com/images/410hsa114yx0va0kw4729.jpg',
      'https://dimg04.c-ctrip.com/images/410bsa114yx0vag0g026A.jpg',
      'https://dimg04.c-ctrip.com/images/410nsa133v3r7gdrk80E1.jpg',
      'https://dimg04.c-ctrip.com/images/410isa133v3r7glhc014A.jpg',
    ],
  },
  packageInfos: [
    {
      description:
        '不推荐选择。本套餐不含第三者保障，国外三者伤亡的赔付金额非常高，门店也会强烈推荐甚至强制搭售。',
      gapPrice: 0,
      packageType: 0,
      isDefault: true,
      insPackageId: 3,
      packageName: '基础保障套餐',
      lowestDailyPrice: 404,
      insuranceNames: ['车损盗抢险'],
      defaultBomCode: 'LAXZE01_10177_FRFB_Fees_LDW_Taxes_ULM_0_0',
      stepPrice: 0,
      defaultPackageId: 3685,
      naked: false,
      guaranteeDegree: 1,
      currencyCode: 'CNY',
    },
    {
      gapPrice: 35,
      packageType: 0,
      isDefault: false,
      insPackageId: 11,
      packageName: '高级保障套餐',
      lowestDailyPrice: 439,
      insuranceNames: ['百万三者险', '车辆碰撞险', '车辆盗抢险'],
      defaultBomCode: 'LAXZE01_10177_ALI_CDW_FRFB_Fees_TP_Taxes_ULM_0_0',
      stepPrice: 35,
      defaultPackageId: 3687,
      naked: false,
      guaranteeDegree: 2.5,
      currencyCode: 'CNY',
    },
    {
      gapPrice: 63,
      packageType: 0,
      isDefault: false,
      insPackageId: 59,
      packageName: '全面保障套餐',
      lowestDailyPrice: 467,
      insuranceNames: [
        '百万三者险',
        '车损盗抢险',
        '人身意外险',
        '个人财物险',
        '个人意外保险',
      ],
      defaultBomCode:
        'LAXZE01_10177_ALI_FRFB_Fees_LDW_PAI_PEI_PPI_Taxes_ULM_0_0',
      stepPrice: 28,
      defaultPackageId: 4223,
      naked: false,
      guaranteeDegree: 3.5,
      currencyCode: 'CNY',
    },
  ],
  similarVehicleIntroduce: {
    cases: [
      {
        vehicleGroupName: '车型组',
        representativeVehicleName: '代表车型',
        vehicleGroupItems: '车组包含',
        vehicleGroupCode: 'default',
      },
      {
        vehicleGroupName: '紧凑型轿车',
        representativeVehicleName: '福特嘉年华',
        vehicleGroupItems: '丰田卡罗拉、现代雅绅特',
        vehicleGroupCode: 'D',
      },
      {
        vehicleGroupName: '中大型轿车',
        representativeVehicleName: '丰田凯美瑞',
        vehicleGroupItems: '雪佛兰迈锐宝、大众帕萨特',
        vehicleGroupCode: 'S',
      },
      {
        vehicleGroupName: '中大型SUV',
        representativeVehicleName: '大众途观',
        vehicleGroupItems: '丰田RAV4、吉普指南者',
        vehicleGroupCode: 'R',
      },
    ],
    carProtection: {
      title: '取车保障',
      description:
        '有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用',
    },
    introduce: {
      title: '同组车型',
      description:
        '在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如',
    },
  },
  isHotCity: true,
  ResponseStatus: {
    Extension: [
      { Value: '6577345053819480583', Id: 'CLOGGING_TRACE_ID' },
      { Value: '921822-0a6e9256-467883-1811710', Id: 'RootMessageId' },
    ],
    Ack: 'Success',
    Errors: [],
    Timestamp: '/Date(1684381850851+0800)/',
  },
  productDetails: [
    {
      productInfoList: [
        {
          needFlightNo: false,
          naked: false,
          productCode: 'REMATCHEAVD177RUAWQ4BNYE4',
          priceInfoList: [
            {
              localCarPrice: 173.1,
              cancelRule: {
                isTotalLoss: false,
                isFreeCancel: true,
                isFreeCancelNow: true,
                hours: 0,
                cancelDescription:
                  '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              },
              chargeList: [
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'ACF',
                  payMode: 2,
                  name: '机场建设费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'CFC',
                  payMode: 2,
                  name: '客户设施费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'VLRF',
                  payMode: 2,
                  name: '车辆登记费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'RTS',
                  payMode: 2,
                  name: '租赁附加费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'TS',
                  payMode: 2,
                  name: '旅游附加费',
                  netAmount: 0,
                },
                {
                  dueAmount: 1212,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: '1',
                  payMode: 2,
                  name: 'Vehicle rental',
                  netAmount: 0,
                },
                {
                  netAmount: 0,
                  dueAmount: 1400,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'minDeposit',
                  desc: '',
                },
                {
                  netAmount: 0,
                  dueAmount: 245,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'youngDriverExtraFee',
                  desc: '',
                },
              ],
              isContainOnewayFee: false,
              packageId: 3685,
              exchangeRate: 7.00005,
              promotionInfo: {},
              currentPoaPrice: 0,
              currentDailyPrice: 404,
              currentTotalPrice: 1212,
              vendorPromotionList: [],
              localTotalPrice: 173.1,
              currentOnewayfee: 0,
              payMode: 2,
              productId: 'REMATCHEAVD177RUAWQ4BNYE43685',
              mileInfo: {
                name: '不限里程',
                desc: '租期内没有公里数限制',
                isLimited: false,
              },
              currentCurrencyCode: 'CNY',
              ageRestriction: {
                maxDriverAge: 80,
                licenceAge: 1,
                licenceAgeDesc: '驾龄至少满1年',
                youngDriverExtraFee: {
                  currentPrice: 245,
                  localCurrencyCode: 'USD',
                  localPrice: 35,
                  feeType: 0,
                },
                oldDriverExtraFee: {
                  currentPrice: 0,
                  localCurrencyCode: 'USD',
                  localPrice: 0,
                  feeType: 0,
                },
                minDriverAge: 20,
                youngDriverAge: 24,
                description:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAge: 0,
                youngDriverAgeDesc:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAgeDesc: '',
              },
              confirmInfo: {
                confirmTitle: '1小时内确认',
                confirmDesc:
                  '预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.',
                confirmTime: 1,
                confirmRightNow: false,
              },
              localCurrencyCode: 'USD',
              localPrepaidPrice: 173.1,
              currentPrepaidPrice: 1212,
              allTags: [
                {
                  category: 1,
                  sortNum: 10,
                  code: '2',
                  title: '1小时内确认',
                  colorCode: '1',
                  type: 1,
                  description: '预订此产品后供应商将在1小时内确认订单',
                },
                {
                  category: 1,
                  sortNum: 25,
                  code: '2',
                  title: '免费取消',
                  colorCode: '8',
                  type: 1,
                  description:
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                },
                {
                  category: 2,
                  sortNum: 49,
                  code: '2',
                  title: '中文表单',
                  colorCode: '2',
                  type: 1,
                  description:
                    '为提高服务质量，该门店为用户提供中文额外服务表单，避免因语言沟通问题产生强制推销的现象。',
                },
                {
                  category: 2,
                  sortNum: 56,
                  code: '2',
                  title: '随时可订',
                  colorCode: '2',
                  type: 1,
                  description: '该车型随时可预订。',
                },
                {
                  category: 2,
                  sortNum: 60,
                  code: '2',
                  title: '24h营业',
                  colorCode: '2',
                  type: 1,
                  description: '门店24小时营业。',
                },
                {
                  category: 2,
                  sortNum: 80,
                  code: '2',
                  title: '满油取还',
                  colorCode: '2',
                  type: 1,
                  description:
                    '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
                },
                {
                  category: 2,
                  sortNum: 85,
                  code: '2',
                  title: '不限里程',
                  colorCode: '2',
                  type: 1,
                  description: '租期内没有公里数限制。',
                },
                {
                  category: 2,
                  sortNum: 95,
                  code: '2',
                  title: '电子提车凭证',
                  colorCode: '2',
                  type: 1,
                  description:
                    '使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。',
                },
              ],
              vcExtendRequest: {},
              packageType: 0,
              currentCarPrice: 1212,
              creditCardInfo: {
                maxDeposit: 0,
                minDeposit: 200,
                description:
                  '取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为US$200.00（约¥1,400.00）,请保证可用额度足以支付押金。押金预计会在还车后28-45天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。',
                cardList: [
                  { name: '美国运通', type: 'AE' },
                  { name: '万事达', type: 'MC' },
                  { name: '大来卡', type: 'DC' },
                  { name: '维萨', type: 'VI' },
                  { name: 'Japanese Credit Bureau Credit Card', type: 'JC' },
                  { name: 'Discover', type: 'DV' },
                  { name: '银联单币信用卡', type: 'UNS' },
                ],
                depositCurrencyCode: 'USD',
              },
              localDailyPrice: 57.7,
              insuranceDetails: [],
              localPoaPrice: 0,
              localOnewayfee: 0,
            },
          ],
          equipments: [
            {
              equipmentCode: '7',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '婴儿座椅',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 0,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '个月',
              equipmentType: 1,
              equipmentDesc: '通常适用于一周岁以下，体重约0-10公斤的婴儿',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '8',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '儿童座椅',
              ageTo: 6,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 9,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 2,
              equipmentDesc: '通常适用于9个月-6周岁，体重约9-18.2公斤的儿童',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '9',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '岁',
              equipmentName: '儿童增高座垫',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 6,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 3,
              equipmentDesc: '通常适用于6-12周岁，体重约18.2-45.5公斤的儿童',
              currentDailyPrice: 98,
            },
          ],
          carRentalMustRead: [
            {
              code: '1',
              title: '确认政策',
              content: ['预订此产品后供应商将在1小时内确认订单'],
              type: 0,
            },
            {
              code: '1',
              title: '取消政策',
              content: [
                '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              ],
              type: 1,
            },
            {
              code: '1',
              title: '押金说明',
              content: [
                '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
              ],
              type: 2,
            },
            { code: '1', title: '里程政策', content: ['不限里程'], type: 3 },
            {
              code: '1',
              title: '燃油政策',
              content: [
                '满油取还',
                '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
              ],
              type: 4,
            },
            {
              code: '1',
              title: '年龄要求',
              content: [
                '驾驶员年龄要求：20-80周岁',
                '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
              ],
              type: 5,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '里程政策',
                  content: ['不限里程'],
                  type: 3,
                },
                {
                  code: '2',
                  title: '燃油政策',
                  content: [
                    '满油取还',
                    '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
                  ],
                  type: 4,
                },
                {
                  code: '2',
                  title: '年龄要求',
                  content: [
                    '驾驶员年龄要求：20-80周岁',
                    '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                    '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
                  ],
                  type: 5,
                },
                {
                  code: '2',
                  title: '额外驾驶员',
                  content: [
                    '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
                  ],
                  type: 6,
                },
                {
                  code: '2',
                  title: '营业时间外取还车',
                  content: [
                    '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
                  ],
                  type: 7,
                },
                {
                  code: '2',
                  title: '当地费用',
                  content: [
                    '订单费用不包括在租车过程中产生的额外收费和罚款，包括过路过桥费、交通拥堵费、停车费、超速罚单或任何其他交通罚款。如果您产生任何费用，例如超速罚单或未付的交通拥堵费，当地管理部门会找到租车公司，让租车公司联系您。一般是还车后几个月内 ，您需要支付租车公司的管理费用以及您需要补交的违章费。',
                  ],
                  type: 16,
                },
              ],
              title: '费用须知',
              sortNum: 4,
              type: 40,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '提前取车',
                  content: [
                    '若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。',
                  ],
                  type: 17,
                },
                {
                  code: '2',
                  title: '延迟取车',
                  content: [
                    '若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。',
                  ],
                  type: 18,
                },
                {
                  code: '2',
                  title: '提前还车',
                  content: [
                    '提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。',
                  ],
                  type: 10,
                },
                {
                  code: '2',
                  title: '延迟还车',
                  content: [
                    '若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。',
                  ],
                  type: 11,
                },
              ],
              title: '提前/延后取还车',
              sortNum: 3,
              type: 41,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '门店提示',
                  content: [
                    '在门店取车时提供的信用卡必须与预付时使用的卡片一致才能取车',
                    '仅接受纸质版实体驾照。（不支持电子驾照）',
                  ],
                  type: 47,
                },
              ],
              title: '温馨提示',
              type: 19,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  subObject: [
                    {
                      title: '车损盗抢险',
                      content: [
                        '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                        '保障车辆碰撞、被盗的损失',
                        '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">单车事故；</li><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                      ],
                    },
                  ],
                  title: '保险，保障范围，豁免',
                  type: 21,
                },
                {
                  code: '2',
                  title: '保险服务提示',
                  content: [
                    '租车公司规定，在租用车辆期间，若发生任何车辆损坏或事故时，请第一时间报警并获得警方报告，否则车辆保险将会失效，车损费用将由您承担。',
                    '租车公司将根据产生车损的次数扣除对应次数的起赔额费用。若最终定损后单次车损的维修费用低于起赔额，会退回该次车损起赔额多收的部分。需赔付单次起赔额的情况：一次事故中造成了多处车损（客人能够提供证明）；一次事故中造成单处车损；需赔付多次起赔额的情况：一次事故中造成了多处车损（客人无法提供证明）；租车期间发生多次车损。',
                  ],
                  type: 45,
                },
                {
                  code: '2',
                  title: '管理费',
                  content: [
                    '还车后租车公司会对车辆损坏进行索赔，并从超额部分扣除金额上增加损坏管理费。',
                    '发生事故时会支付一定费用。这是从超额扣除的金额，是强制性的。这个费用是根据车型组和维修天数计算的。',
                  ],
                  type: 22,
                },
              ],
              title: '租车保障',
              type: 20,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '额外设备',
                  content: [
                    '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
                  ],
                  type: 24,
                },
                {
                  code: '2',
                  title: '税费',
                  content: ['所有额外服务将会收取销售税费和当地费用。'],
                  type: 25,
                },
              ],
              title: '附加服务',
              type: 23,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '跨境政策',
                  content: [
                    '只有以下 Hertz 车组可以开往墨西哥，但不允许在墨西哥还车。请在租车时查询前往墨西哥的授权许可，并购买强制性墨西哥保险：参考价格38美元/天，适用车组(A) Chevrolet Spark 或类似,  (B) Ford Focus 或类似,  (C) Mazda 3 4-door 或类似, (D) VW Jetta 或类似, (E6) Ford Fusion Hybrid 或类似, (F) Chevrolet Malibu 或类似, (F6) Fullsize with Leather 4 Door 或类似, (Q4) Nissan Rogue 或类似。 参考价格48 美元/天，适用车组(F4) Hyundai Santa Fe 或类似, (G) Buick Regal 或类似, (K6) Dodge Journey 或类似, (L) Chevrolet Equinox 或类似, (L4) Nissan Pathfinder 或类似, (O6) Nissan Frontier Crew Cab 或类似, (R) Chrysler Pacifica 或类似。 墨西哥法律不允许墨西哥公民将美国车辆带入墨西哥，或在墨西哥境内驾驶美国车辆。 如果需要开车进入加拿大，承租人需要在租车时告知柜台工作人员。您必须拥有有效的许可证和租赁记录。从这个城市租车不需要在海关出示责任保护信、证书或贴纸即可驾车进入加拿大。某些州可能适用额外和/或不同的限制。请在租赁时查看您的租赁协议以获取更多信息。',
                  ],
                  type: 27,
                },
              ],
              title: '旅行限制',
              type: 26,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '取消政策',
                  content: [
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                  ],
                  type: 30,
                },
                {
                  code: '2',
                  title: '修改',
                  content: [
                    '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
                  ],
                  type: 31,
                },
                {
                  code: '2',
                  title: 'No show（未取车）',
                  content: [
                    'noshow 是指当你：',
                    '计划提前取消订单但未告知我们，或者',
                    '未在规定的时间内取车，或者',
                    '无法提供取车时要出示的文件，或者',
                    '无法提供主驾驶员名下有足够额度的信用卡',
                    '以上情形，你之前预定的订单金额不会退还给你。',
                  ],
                  type: 32,
                },
              ],
              title: '取消，未取车和修改',
              type: 42,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '意外或故障',
                  content: [
                    '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
                  ],
                  type: 34,
                },
                {
                  code: '2',
                  title: '道路救援',
                  content: [
                    '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
                  ],
                  type: 35,
                },
                {
                  code: '2',
                  title: '遗失钥匙',
                  content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
                  type: 36,
                },
                {
                  code: '2',
                  title: '安全带',
                  content: [
                    '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
                  ],
                  type: 37,
                },
                {
                  code: '2',
                  title: '禁止吸烟',
                  content: [
                    '注意所有的车都禁止车内抽烟，如果不遵守被发现，会有罚款。',
                  ],
                  type: 38,
                },
                {
                  code: '2',
                  title: '价格计算',
                  content: [
                    '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
                    '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
                    '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
                    '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
                  ],
                  type: 39,
                },
              ],
              title: '租车公司重要信息',
              type: 33,
            },
            {
              code: '4',
              sortNum: 1,
              title: '额外驾驶员',
              content: [
                '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
              ],
              type: 6,
            },
            {
              code: '4',
              sortNum: 2,
              title: '营业时间外取还车',
              content: [
                '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
              ],
              type: 7,
            },
          ],
          searchUnionPay: false,
          searchCreditCard: false,
          bomGroupCode: 'LAXZE01_10177_FRFB_Fees_LDW_Taxes_ULM_0_0',
          packageItems: [
            {
              code: 'Fees',
              sortNum: 1,
              name: '基础租车费用',
              desc: '仅包含车辆租金的基础费用',
            },
            {
              code: 'Taxes',
              sortNum: 2,
              name: '税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税',
              desc: '税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。',
            },
            {
              code: 'LDW',
              sortNum: 3,
              name: '车损盗抢险',
              desc: '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'ULM',
              sortNum: 4,
              name: '不限里程',
              desc: '租期内没有公里数限制',
            },
            {
              code: 'FRFB',
              sortNum: 4,
              name: '满油取还',
              desc: '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
            },
          ],
          pickUpMaterials: [
            {
              subObject: [
                { title: '中国大陆护照原件', code: 'CN' },
                { title: '中国香港护照原件', code: 'HK' },
                { title: '中国澳门护照原件', code: 'MO' },
                { title: '中国台湾护照原件', code: 'TW' },
                { title: '其他地区护照原件', code: 'OH' },
              ],
              title: '身份证明文件',
              content: ['护照、驾照发证国家/地区须一致方可成功取车'],
              type: 0,
            },
            {
              subObject: [
                {
                  content: [
                    '当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。',
                    '所有驾驶员驾龄必须至少满1年',
                  ],
                  type: 4,
                },
                {
                  title: '门店支持以下驾照组合(任选其一)',
                  type: 5,
                  subObject: [
                    {
                      code: 'CN',
                      subObject: [
                        {
                          sortNum: 2,
                          title: '中国驾照原件+驾照国际翻译认证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 3,
                          title: '中国驾照原件+车行翻译件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 ',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 4,
                          title: '中国驾照原件+当地语言公证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合(任选其一)',
                      sortNum: 0,
                    },
                    {
                      code: 'HK',
                      subObject: [
                        {
                          sortNum: 37,
                          title: '香港驾照+国际驾照',
                          content: [
                            '香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 1,
                    },
                    {
                      code: 'MO',
                      subObject: [
                        {
                          sortNum: 29,
                          title: '澳门驾照+国际驾照',
                          content: [
                            '澳门驾照：由中国澳门特别行政区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 2,
                    },
                    {
                      code: 'TW',
                      subObject: [
                        {
                          sortNum: 31,
                          title: '台湾驾照+国际驾照',
                          content: [
                            '台湾驾照：由中国台湾地区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 3,
                    },
                    {
                      code: 'OH',
                      subObject: [
                        {
                          sortNum: 11,
                          title: '驾驶员本国驾照+国际驾照',
                          content: [
                            '驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 4,
                    },
                  ],
                },
                {
                  title: '注意：',
                  content: [
                    '因商业条款限制，美国驾照不适用此价格租车。若您使用美国驾照，请移步trip平台预定。',
                  ],
                  type: 10,
                },
              ],
              title: '驾照要求',
              summaryContent: ['所有驾驶员驾龄必须至少满1年'],
              type: 1,
            },
            {
              subObject: [
                {
                  title: '信用卡要求',
                  content: [
                    '请保证可用金额足以支付押金',
                    '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
                    '卡面所示姓名与主驾驶员护照姓名一致',
                  ],
                  type: 6,
                },
                {
                  title: '接受的信用卡',
                  content: [
                    '美国运通，万事达，大来卡，维萨，Japanese Credit Bureau Credit Card，Discover，银联单币信用卡',
                  ],
                  type: 7,
                  urlList: [
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/unionpay.png',
                  ],
                },
                {
                  title: '押金说明',
                  content: [
                    '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
                  ],
                  type: 8,
                },
              ],
              title: '主驾驶员名下国际信用卡',
              content: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              summaryContent: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              type: 2,
            },
            {
              title: '提车凭证',
              content: [
                '取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。',
              ],
              summaryContent: ['订单确认后，携程为您提供'],
              type: 3,
            },
          ],
        },
      ],
      combinations: [
        {
          codes: [],
          dayPrice: 404,
          gapPrice: 0,
          title: '',
          totalPrice: 1212,
          bomCode: 'LAXZE01_10177_FRFB_Fees_LDW_Taxes_ULM_0_0',
          stepPrice: 0,
          hike: true,
          currency: 'CNY',
          payMode: 2,
          packageId: 3685,
        },
      ],
      insPackageId: 3,
      insuranceItems: [
        {
          code: 'LDW',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">单车事故；</li><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
            ],
          },
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: true,
          description: '保障车辆碰撞、被盗的损失',
          isFromCtrip: false,
          name: '车损盗抢险',
          insuranceDetail: [],
        },
        {
          code: 'CDW',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
            ],
          },
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          isInclude: false,
          description: '保障车辆碰撞损失',
          isFromCtrip: false,
          name: '车辆碰撞险',
        },
        {
          code: 'TP',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">钥匙遗落车内导致被盗；</li><li style="margin-bottom:-25px;">钥匙丢失，损坏或被盗；</li><li style="margin-bottom:-25px;">车门忘关导致被盗；</li><li style="margin-bottom:-25px;">车内额外设备如GPS，WIFI，儿童座椅等；</li><li style="margin-bottom:-25px;">在未经授权的区域行驶；</li><li style="margin-bottom:-25px;">将车辆长时间停在不当的地方导致车辆被盗；</li><li style="margin-bottom:-25px;">因车辆被盗被抢导致的车辆停运费或维修手续费；</li><li style="margin-bottom:-25px;">违反合同或当地法律的情况下导致的损失；</li></ul>*实际赔付范围与标准以门店合同为准',
            ],
          },
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          isInclude: false,
          description: '保障车辆被盗的损失',
          isFromCtrip: false,
          name: '车辆盗抢险',
        },
        {
          code: 'ALI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {
            title: '承保范围',
            content: [
              '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: false,
          description: '保障第三方车辆或人员伤害损失',
          isFromCtrip: false,
          name: '百万三者险',
        },
        {
          code: 'PAI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {
            title: '承保范围',
            content: [
              '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: false,
          description: '保障全车人员意外伤害',
          isFromCtrip: false,
          name: '人身意外险',
        },
        {
          code: 'PEI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {
            title: '承保范围',
            content: [
              '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: false,
          description: '保障随车行李丢失',
          isFromCtrip: false,
          name: '个人财物险',
        },
        {
          code: 'PPI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {},
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: false,
          description: '',
          isFromCtrip: false,
          name: '个人意外保险',
        },
      ],
    },
    {
      insuranceDesc: [
        '租车公司规定，在租用车辆期间，若发生任何车辆损坏或事故时，请第一时间报警并获得警方报告，否则车辆保险将会失效，车损费用将由您承担。',
        '租车公司将根据产生车损的次数扣除对应次数的起赔额费用。若最终定损后单次车损的维修费用低于起赔额，会退回该次车损起赔额多收的部分。需赔付单次起赔额的情况：一次事故中造成了多处车损（客人能够提供证明）；一次事故中造成单处车损；需赔付多次起赔额的情况：一次事故中造成了多处车损（客人无法提供证明）；租车期间发生多次车损。',
      ],
      productInfoList: [
        {
          needFlightNo: false,
          naked: false,
          productCode: 'REMATCHEAVD177RUAWQ4BNYE4',
          priceInfoList: [
            {
              localCarPrice: 188.1,
              cancelRule: {
                isTotalLoss: false,
                isFreeCancel: true,
                isFreeCancelNow: true,
                hours: 0,
                cancelDescription:
                  '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              },
              chargeList: [
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'ACF',
                  payMode: 2,
                  name: '机场建设费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'CFC',
                  payMode: 2,
                  name: '客户设施费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'VLRF',
                  payMode: 2,
                  name: '车辆登记费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'RTS',
                  payMode: 2,
                  name: '租赁附加费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'TS',
                  payMode: 2,
                  name: '旅游附加费',
                  netAmount: 0,
                },
                {
                  dueAmount: 1317,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: '1',
                  payMode: 2,
                  name: 'Vehicle rental',
                  netAmount: 0,
                },
                {
                  netAmount: 0,
                  dueAmount: 1400,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'minDeposit',
                  desc: '',
                },
                {
                  netAmount: 0,
                  dueAmount: 245,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'youngDriverExtraFee',
                  desc: '',
                },
              ],
              isContainOnewayFee: false,
              packageId: 3687,
              exchangeRate: 7.00005,
              promotionInfo: {},
              currentPoaPrice: 0,
              currentDailyPrice: 439,
              currentTotalPrice: 1317,
              vendorPromotionList: [],
              localTotalPrice: 188.1,
              currentOnewayfee: 0,
              payMode: 2,
              productId: 'REMATCHEAVD177RUAWQ4BNYE43687',
              mileInfo: {
                name: '不限里程',
                desc: '租期内没有公里数限制',
                isLimited: false,
              },
              currentCurrencyCode: 'CNY',
              ageRestriction: {
                maxDriverAge: 80,
                licenceAge: 1,
                licenceAgeDesc: '驾龄至少满1年',
                youngDriverExtraFee: {
                  currentPrice: 245,
                  localCurrencyCode: 'USD',
                  localPrice: 35,
                  feeType: 0,
                },
                oldDriverExtraFee: {
                  currentPrice: 0,
                  localCurrencyCode: 'USD',
                  localPrice: 0,
                  feeType: 0,
                },
                minDriverAge: 20,
                youngDriverAge: 24,
                description:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAge: 0,
                youngDriverAgeDesc:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAgeDesc: '',
              },
              confirmInfo: {
                confirmTitle: '1小时内确认',
                confirmDesc:
                  '预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.',
                confirmTime: 1,
                confirmRightNow: false,
              },
              localCurrencyCode: 'USD',
              localPrepaidPrice: 188.1,
              currentPrepaidPrice: 1317,
              allTags: [
                {
                  category: 1,
                  sortNum: 10,
                  code: '2',
                  title: '1小时内确认',
                  colorCode: '1',
                  type: 1,
                  description: '预订此产品后供应商将在1小时内确认订单',
                },
                {
                  category: 1,
                  sortNum: 25,
                  code: '2',
                  title: '免费取消',
                  colorCode: '8',
                  type: 1,
                  description:
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                },
                {
                  category: 2,
                  sortNum: 49,
                  code: '2',
                  title: '中文表单',
                  colorCode: '2',
                  type: 1,
                  description:
                    '为提高服务质量，该门店为用户提供中文额外服务表单，避免因语言沟通问题产生强制推销的现象。',
                },
                {
                  category: 2,
                  sortNum: 56,
                  code: '2',
                  title: '随时可订',
                  colorCode: '2',
                  type: 1,
                  description: '该车型随时可预订。',
                },
                {
                  category: 2,
                  sortNum: 60,
                  code: '2',
                  title: '24h营业',
                  colorCode: '2',
                  type: 1,
                  description: '门店24小时营业。',
                },
                {
                  category: 2,
                  sortNum: 66,
                  code: '2',
                  title: '送百万三者险',
                  colorCode: '2',
                  type: 1,
                  description: '该产品送百万三者险。',
                },
                {
                  category: 2,
                  sortNum: 70,
                  code: '2',
                  title: '0起赔额',
                  colorCode: '2',
                  type: 1,
                  description:
                    '该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。',
                },
                {
                  category: 2,
                  sortNum: 80,
                  code: '2',
                  title: '满油取还',
                  colorCode: '2',
                  type: 1,
                  description:
                    '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
                },
                {
                  category: 2,
                  sortNum: 85,
                  code: '2',
                  title: '不限里程',
                  colorCode: '2',
                  type: 1,
                  description: '租期内没有公里数限制。',
                },
                {
                  category: 2,
                  sortNum: 95,
                  code: '2',
                  title: '电子提车凭证',
                  colorCode: '2',
                  type: 1,
                  description:
                    '使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。',
                },
              ],
              vcExtendRequest: {},
              packageType: 0,
              currentCarPrice: 1317,
              creditCardInfo: {
                maxDeposit: 0,
                minDeposit: 200,
                description:
                  '取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为US$200.00（约¥1,400.00）,请保证可用额度足以支付押金。押金预计会在还车后28-45天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。',
                cardList: [
                  { name: '美国运通', type: 'AE' },
                  { name: '万事达', type: 'MC' },
                  { name: '大来卡', type: 'DC' },
                  { name: '维萨', type: 'VI' },
                  { name: 'Japanese Credit Bureau Credit Card', type: 'JC' },
                  { name: 'Discover', type: 'DV' },
                  { name: '银联单币信用卡', type: 'UNS' },
                ],
                depositCurrencyCode: 'USD',
              },
              localDailyPrice: 62.7,
              insuranceDetails: [
                {
                  code: 'CDW',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '车辆碰撞险',
                  maxExcess: 0,
                },
                {
                  code: 'TP',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '车辆盗抢险',
                  maxExcess: 0,
                },
                {
                  code: 'ALI',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '百万三者险',
                  maxExcess: 0,
                },
              ],
              localPoaPrice: 0,
              localOnewayfee: 0,
            },
            {
              localCarPrice: 198,
              cancelRule: {
                isTotalLoss: false,
                isFreeCancel: true,
                isFreeCancelNow: true,
                hours: 0,
                cancelDescription:
                  '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              },
              chargeList: [
                {
                  dueAmount: 0,
                  currency: 'USD',
                  isIncludedInRate: true,
                  code: 'ACF',
                  payMode: 1,
                  name: '机场建设费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'USD',
                  isIncludedInRate: true,
                  code: 'CFC',
                  payMode: 1,
                  name: '客户设施费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'USD',
                  isIncludedInRate: true,
                  code: 'VLRF',
                  payMode: 1,
                  name: '车辆登记费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'USD',
                  isIncludedInRate: true,
                  code: 'RTS',
                  payMode: 1,
                  name: '租赁附加费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'USD',
                  isIncludedInRate: true,
                  code: 'TS',
                  payMode: 1,
                  name: '旅游附加费',
                  netAmount: 0,
                },
                {
                  dueAmount: 198,
                  currency: 'USD',
                  isIncludedInRate: true,
                  code: '1',
                  payMode: 1,
                  name: 'Vehicle rental',
                  netAmount: 0,
                },
                {
                  netAmount: 0,
                  dueAmount: 1400,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'minDeposit',
                  desc: '',
                },
                {
                  netAmount: 0,
                  dueAmount: 245,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'youngDriverExtraFee',
                  desc: '',
                },
              ],
              isContainOnewayFee: true,
              packageId: 3659,
              exchangeRate: 7.00005,
              promotionInfo: {},
              currentPoaPrice: 1386,
              currentDailyPrice: 462,
              currentTotalPrice: 1386,
              vendorPromotionList: [],
              localTotalPrice: 198,
              currentOnewayfee: 0,
              payMode: 1,
              productId: 'REMATCHEAVD177RUAWQ4BNYE43659',
              mileInfo: {
                name: '不限里程',
                desc: '租期内没有公里数限制',
                isLimited: false,
              },
              currentCurrencyCode: 'CNY',
              ageRestriction: {
                maxDriverAge: 80,
                licenceAge: 1,
                licenceAgeDesc: '驾龄至少满1年',
                youngDriverExtraFee: {
                  currentPrice: 245,
                  localCurrencyCode: 'USD',
                  localPrice: 35,
                  feeType: 0,
                },
                oldDriverExtraFee: {
                  currentPrice: 0,
                  localCurrencyCode: 'USD',
                  localPrice: 0,
                  feeType: 0,
                },
                minDriverAge: 20,
                youngDriverAge: 24,
                description:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAge: 0,
                youngDriverAgeDesc:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAgeDesc: '',
              },
              confirmInfo: {
                confirmTitle: '1小时内确认',
                confirmDesc:
                  '预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.',
                confirmTime: 1,
                confirmRightNow: false,
              },
              localCurrencyCode: 'USD',
              localPrepaidPrice: 0,
              currentPrepaidPrice: 0,
              allTags: [
                {
                  category: 1,
                  sortNum: 10,
                  code: '2',
                  title: '1小时内确认',
                  colorCode: '1',
                  type: 1,
                  description: '预订此产品后供应商将在1小时内确认订单',
                },
                {
                  category: 1,
                  sortNum: 25,
                  code: '2',
                  title: '免费取消',
                  colorCode: '8',
                  type: 1,
                  description:
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                },
                {
                  category: 2,
                  sortNum: 49,
                  code: '2',
                  title: '中文表单',
                  colorCode: '2',
                  type: 1,
                  description:
                    '为提高服务质量，该门店为用户提供中文额外服务表单，避免因语言沟通问题产生强制推销的现象。',
                },
                {
                  category: 2,
                  sortNum: 56,
                  code: '2',
                  title: '随时可订',
                  colorCode: '2',
                  type: 1,
                  description: '该车型随时可预订。',
                },
                {
                  category: 2,
                  sortNum: 60,
                  code: '2',
                  title: '24h营业',
                  colorCode: '2',
                  type: 1,
                  description: '门店24小时营业。',
                },
                {
                  category: 2,
                  sortNum: 66,
                  code: '2',
                  title: '送百万三者险',
                  colorCode: '2',
                  type: 1,
                  description: '该产品送百万三者险。',
                },
                {
                  category: 2,
                  sortNum: 70,
                  code: '2',
                  title: '0起赔额',
                  colorCode: '2',
                  type: 1,
                  description:
                    '该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。',
                },
                {
                  category: 2,
                  sortNum: 80,
                  code: '2',
                  title: '满油取还',
                  colorCode: '2',
                  type: 1,
                  description:
                    '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
                },
                {
                  category: 2,
                  sortNum: 85,
                  code: '2',
                  title: '不限里程',
                  colorCode: '2',
                  type: 1,
                  description: '租期内没有公里数限制。',
                },
                {
                  category: 2,
                  sortNum: 95,
                  code: '2',
                  title: '电子提车凭证',
                  colorCode: '2',
                  type: 1,
                  description:
                    '使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。',
                },
              ],
              vcExtendRequest: {},
              packageType: 0,
              currentCarPrice: 1386,
              creditCardInfo: {
                maxDeposit: 0,
                minDeposit: 200,
                description:
                  '取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为US$200.00（约¥1,400.00）,请保证可用额度足以支付押金。押金预计会在还车后28-45天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。',
                cardList: [
                  { name: '美国运通', type: 'AE' },
                  { name: '万事达', type: 'MC' },
                  { name: '大来卡', type: 'DC' },
                  { name: '维萨', type: 'VI' },
                  { name: 'Japanese Credit Bureau Credit Card', type: 'JC' },
                  { name: 'Discover', type: 'DV' },
                  { name: '银联单币信用卡', type: 'UNS' },
                ],
                depositCurrencyCode: 'USD',
              },
              localDailyPrice: 66,
              insuranceDetails: [
                {
                  code: 'CDW',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '车辆碰撞险',
                  maxExcess: 0,
                },
                {
                  code: 'TP',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '车辆盗抢险',
                  maxExcess: 0,
                },
                {
                  code: 'ALI',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '百万三者险',
                  maxExcess: 0,
                },
              ],
              localPoaPrice: 198,
              localOnewayfee: 0,
            },
          ],
          equipments: [
            {
              equipmentCode: '7',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '婴儿座椅',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 0,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '个月',
              equipmentType: 1,
              equipmentDesc: '通常适用于一周岁以下，体重约0-10公斤的婴儿',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '8',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '儿童座椅',
              ageTo: 6,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 9,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 2,
              equipmentDesc: '通常适用于9个月-6周岁，体重约9-18.2公斤的儿童',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '9',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '岁',
              equipmentName: '儿童增高座垫',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 6,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 3,
              equipmentDesc: '通常适用于6-12周岁，体重约18.2-45.5公斤的儿童',
              currentDailyPrice: 98,
            },
          ],
          carRentalMustRead: [
            {
              code: '1',
              title: '确认政策',
              content: ['预订此产品后供应商将在1小时内确认订单'],
              type: 0,
            },
            {
              code: '1',
              title: '取消政策',
              content: [
                '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              ],
              type: 1,
            },
            {
              code: '1',
              title: '押金说明',
              content: [
                '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
              ],
              type: 2,
            },
            { code: '1', title: '里程政策', content: ['不限里程'], type: 3 },
            {
              code: '1',
              title: '燃油政策',
              content: [
                '满油取还',
                '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
              ],
              type: 4,
            },
            {
              code: '1',
              title: '年龄要求',
              content: [
                '驾驶员年龄要求：20-80周岁',
                '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
              ],
              type: 5,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '里程政策',
                  content: ['不限里程'],
                  type: 3,
                },
                {
                  code: '2',
                  title: '燃油政策',
                  content: [
                    '满油取还',
                    '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
                  ],
                  type: 4,
                },
                {
                  code: '2',
                  title: '年龄要求',
                  content: [
                    '驾驶员年龄要求：20-80周岁',
                    '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                    '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
                  ],
                  type: 5,
                },
                {
                  code: '2',
                  title: '额外驾驶员',
                  content: [
                    '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
                  ],
                  type: 6,
                },
                {
                  code: '2',
                  title: '营业时间外取还车',
                  content: [
                    '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
                  ],
                  type: 7,
                },
                {
                  code: '2',
                  title: '当地费用',
                  content: [
                    '订单费用不包括在租车过程中产生的额外收费和罚款，包括过路过桥费、交通拥堵费、停车费、超速罚单或任何其他交通罚款。如果您产生任何费用，例如超速罚单或未付的交通拥堵费，当地管理部门会找到租车公司，让租车公司联系您。一般是还车后几个月内 ，您需要支付租车公司的管理费用以及您需要补交的违章费。',
                  ],
                  type: 16,
                },
              ],
              title: '费用须知',
              sortNum: 4,
              type: 40,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '提前取车',
                  content: [
                    '若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。',
                  ],
                  type: 17,
                },
                {
                  code: '2',
                  title: '延迟取车',
                  content: [
                    '若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。',
                  ],
                  type: 18,
                },
                {
                  code: '2',
                  title: '提前还车',
                  content: [
                    '提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。',
                  ],
                  type: 10,
                },
                {
                  code: '2',
                  title: '延迟还车',
                  content: [
                    '若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。',
                  ],
                  type: 11,
                },
              ],
              title: '提前/延后取还车',
              sortNum: 3,
              type: 41,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '门店提示',
                  content: [
                    '在门店取车时提供的信用卡必须与预付时使用的卡片一致才能取车',
                    '仅接受纸质版实体驾照。（不支持电子驾照）',
                  ],
                  type: 47,
                },
              ],
              title: '温馨提示',
              type: 19,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  subObject: [
                    {
                      title: '百万三者险',
                      content: [
                        '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
                        '保障第三方车辆或人员伤害损失',
                      ],
                    },
                    {
                      title: '车辆碰撞险',
                      content: [
                        '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                        '保障车辆碰撞损失',
                        '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                      ],
                    },
                    {
                      title: '车辆盗抢险',
                      content: [
                        '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                        '保障车辆被盗的损失',
                        '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">钥匙遗落车内导致被盗；</li><li style="margin-bottom:-25px;">钥匙丢失，损坏或被盗；</li><li style="margin-bottom:-25px;">车门忘关导致被盗；</li><li style="margin-bottom:-25px;">车内额外设备如GPS，WIFI，儿童座椅等；</li><li style="margin-bottom:-25px;">在未经授权的区域行驶；</li><li style="margin-bottom:-25px;">将车辆长时间停在不当的地方导致车辆被盗；</li><li style="margin-bottom:-25px;">因车辆被盗被抢导致的车辆停运费或维修手续费；</li><li style="margin-bottom:-25px;">违反合同或当地法律的情况下导致的损失；</li></ul>*实际赔付范围与标准以门店合同为准',
                      ],
                    },
                  ],
                  title: '保险，保障范围，豁免',
                  type: 21,
                },
                {
                  code: '2',
                  title: '保险服务提示',
                  content: [
                    '租车公司规定，在租用车辆期间，若发生任何车辆损坏或事故时，请第一时间报警并获得警方报告，否则车辆保险将会失效，车损费用将由您承担。',
                    '租车公司将根据产生车损的次数扣除对应次数的起赔额费用。若最终定损后单次车损的维修费用低于起赔额，会退回该次车损起赔额多收的部分。需赔付单次起赔额的情况：一次事故中造成了多处车损（客人能够提供证明）；一次事故中造成单处车损；需赔付多次起赔额的情况：一次事故中造成了多处车损（客人无法提供证明）；租车期间发生多次车损。',
                  ],
                  type: 45,
                },
                {
                  code: '2',
                  title: '管理费',
                  content: [
                    '还车后租车公司会对车辆损坏进行索赔，并从超额部分扣除金额上增加损坏管理费。',
                    '发生事故时会支付一定费用。这是从超额扣除的金额，是强制性的。这个费用是根据车型组和维修天数计算的。',
                  ],
                  type: 22,
                },
              ],
              title: '租车保障',
              type: 20,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '额外设备',
                  content: [
                    '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
                  ],
                  type: 24,
                },
                {
                  code: '2',
                  title: '税费',
                  content: ['所有额外服务将会收取销售税费和当地费用。'],
                  type: 25,
                },
              ],
              title: '附加服务',
              type: 23,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '跨境政策',
                  content: [
                    '只有以下 Hertz 车组可以开往墨西哥，但不允许在墨西哥还车。请在租车时查询前往墨西哥的授权许可，并购买强制性墨西哥保险：参考价格38美元/天，适用车组(A) Chevrolet Spark 或类似,  (B) Ford Focus 或类似,  (C) Mazda 3 4-door 或类似, (D) VW Jetta 或类似, (E6) Ford Fusion Hybrid 或类似, (F) Chevrolet Malibu 或类似, (F6) Fullsize with Leather 4 Door 或类似, (Q4) Nissan Rogue 或类似。 参考价格48 美元/天，适用车组(F4) Hyundai Santa Fe 或类似, (G) Buick Regal 或类似, (K6) Dodge Journey 或类似, (L) Chevrolet Equinox 或类似, (L4) Nissan Pathfinder 或类似, (O6) Nissan Frontier Crew Cab 或类似, (R) Chrysler Pacifica 或类似。 墨西哥法律不允许墨西哥公民将美国车辆带入墨西哥，或在墨西哥境内驾驶美国车辆。 如果需要开车进入加拿大，承租人需要在租车时告知柜台工作人员。您必须拥有有效的许可证和租赁记录。从这个城市租车不需要在海关出示责任保护信、证书或贴纸即可驾车进入加拿大。某些州可能适用额外和/或不同的限制。请在租赁时查看您的租赁协议以获取更多信息。',
                  ],
                  type: 27,
                },
              ],
              title: '旅行限制',
              type: 26,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '取消政策',
                  content: [
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                  ],
                  type: 30,
                },
                {
                  code: '2',
                  title: '修改',
                  content: [
                    '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
                  ],
                  type: 31,
                },
                {
                  code: '2',
                  title: 'No show（未取车）',
                  content: [
                    'noshow 是指当你：',
                    '计划提前取消订单但未告知我们，或者',
                    '未在规定的时间内取车，或者',
                    '无法提供取车时要出示的文件，或者',
                    '无法提供主驾驶员名下有足够额度的信用卡',
                    '以上情形，你之前预定的订单金额不会退还给你。',
                  ],
                  type: 32,
                },
              ],
              title: '取消，未取车和修改',
              type: 42,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '意外或故障',
                  content: [
                    '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
                  ],
                  type: 34,
                },
                {
                  code: '2',
                  title: '道路救援',
                  content: [
                    '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
                  ],
                  type: 35,
                },
                {
                  code: '2',
                  title: '遗失钥匙',
                  content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
                  type: 36,
                },
                {
                  code: '2',
                  title: '安全带',
                  content: [
                    '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
                  ],
                  type: 37,
                },
                {
                  code: '2',
                  title: '禁止吸烟',
                  content: [
                    '注意所有的车都禁止车内抽烟，如果不遵守被发现，会有罚款。',
                  ],
                  type: 38,
                },
                {
                  code: '2',
                  title: '价格计算',
                  content: [
                    '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
                    '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
                    '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
                    '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
                  ],
                  type: 39,
                },
              ],
              title: '租车公司重要信息',
              type: 33,
            },
            {
              code: '4',
              sortNum: 1,
              title: '额外驾驶员',
              content: [
                '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
              ],
              type: 6,
            },
            {
              code: '4',
              sortNum: 2,
              title: '营业时间外取还车',
              content: [
                '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
              ],
              type: 7,
            },
          ],
          searchUnionPay: false,
          searchCreditCard: false,
          bomGroupCode: 'LAXZE01_10177_ALI_CDW_FRFB_Fees_TP_Taxes_ULM_0_0',
          packageItems: [
            {
              code: 'Fees',
              sortNum: 1,
              name: '基础租车费用',
              desc: '仅包含车辆租金的基础费用',
            },
            {
              code: 'Taxes',
              sortNum: 2,
              name: '税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税',
              desc: '税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。',
            },
            {
              code: 'ALI',
              sortNum: 3,
              name: '百万三者险',
              desc: '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'CDW',
              sortNum: 3,
              name: '车辆碰撞险',
              desc: '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'TP',
              sortNum: 3,
              name: '车辆盗抢险',
              desc: '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'ULM',
              sortNum: 4,
              name: '不限里程',
              desc: '租期内没有公里数限制',
            },
            {
              code: 'FRFB',
              sortNum: 4,
              name: '满油取还',
              desc: '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
            },
          ],
          pickUpMaterials: [
            {
              subObject: [
                { title: '中国大陆护照原件', code: 'CN' },
                { title: '中国香港护照原件', code: 'HK' },
                { title: '中国澳门护照原件', code: 'MO' },
                { title: '中国台湾护照原件', code: 'TW' },
                { title: '其他地区护照原件', code: 'OH' },
              ],
              title: '身份证明文件',
              content: ['护照、驾照发证国家/地区须一致方可成功取车'],
              type: 0,
            },
            {
              subObject: [
                {
                  content: [
                    '当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。',
                    '所有驾驶员驾龄必须至少满1年',
                  ],
                  type: 4,
                },
                {
                  title: '门店支持以下驾照组合(任选其一)',
                  type: 5,
                  subObject: [
                    {
                      code: 'CN',
                      subObject: [
                        {
                          sortNum: 2,
                          title: '中国驾照原件+驾照国际翻译认证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 3,
                          title: '中国驾照原件+车行翻译件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 ',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 4,
                          title: '中国驾照原件+当地语言公证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合(任选其一)',
                      sortNum: 0,
                    },
                    {
                      code: 'HK',
                      subObject: [
                        {
                          sortNum: 37,
                          title: '香港驾照+国际驾照',
                          content: [
                            '香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 1,
                    },
                    {
                      code: 'MO',
                      subObject: [
                        {
                          sortNum: 29,
                          title: '澳门驾照+国际驾照',
                          content: [
                            '澳门驾照：由中国澳门特别行政区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 2,
                    },
                    {
                      code: 'TW',
                      subObject: [
                        {
                          sortNum: 31,
                          title: '台湾驾照+国际驾照',
                          content: [
                            '台湾驾照：由中国台湾地区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 3,
                    },
                    {
                      code: 'OH',
                      subObject: [
                        {
                          sortNum: 11,
                          title: '驾驶员本国驾照+国际驾照',
                          content: [
                            '驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 4,
                    },
                  ],
                },
                {
                  title: '注意：',
                  content: [
                    '因商业条款限制，美国驾照不适用此价格租车。若您使用美国驾照，请移步trip平台预定。',
                  ],
                  type: 10,
                },
              ],
              title: '驾照要求',
              summaryContent: ['所有驾驶员驾龄必须至少满1年'],
              type: 1,
            },
            {
              subObject: [
                {
                  title: '信用卡要求',
                  content: [
                    '请保证可用金额足以支付押金',
                    '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
                    '卡面所示姓名与主驾驶员护照姓名一致',
                  ],
                  type: 6,
                },
                {
                  title: '接受的信用卡',
                  content: [
                    '美国运通，万事达，大来卡，维萨，Japanese Credit Bureau Credit Card，Discover，银联单币信用卡',
                  ],
                  type: 7,
                  urlList: [
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/unionpay.png',
                  ],
                },
                {
                  title: '押金说明',
                  content: [
                    '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
                  ],
                  type: 8,
                },
              ],
              title: '主驾驶员名下国际信用卡',
              content: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              summaryContent: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              type: 2,
            },
            {
              title: '提车凭证',
              content: [
                '取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。',
              ],
              summaryContent: ['订单确认后，携程为您提供'],
              type: 3,
            },
          ],
        },
      ],
      combinations: [
        {
          codes: [],
          dayPrice: 439,
          gapPrice: 0,
          title: '',
          totalPrice: 1317,
          bomCode: 'LAXZE01_10177_ALI_CDW_FRFB_Fees_TP_Taxes_ULM_0_0',
          stepPrice: 0,
          hike: true,
          currency: 'CNY',
          payMode: 2,
          packageId: 3687,
        },
      ],
      insPackageId: 11,
      insuranceItems: [
        {
          code: 'CDW',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
            ],
          },
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          isInclude: true,
          description: '保障车辆碰撞损失',
          isFromCtrip: false,
          name: '车辆碰撞险',
          insuranceDetail: [
            {
              coverageLongDesc: '',
              currencyCode: 'USD',
              coverageShortDesc: '',
              packageId: 3659,
              maxExcess: 0,
              excessShortDesc: '起赔额：0USD',
              minExcess: 0,
              excessLongDesc: '',
            },
            {
              coverageLongDesc: '',
              currencyCode: 'USD',
              coverageShortDesc: '',
              packageId: 3687,
              maxExcess: 0,
              excessShortDesc: '起赔额：0USD',
              minExcess: 0,
              excessLongDesc: '',
            },
          ],
        },
        {
          code: 'LDW',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">单车事故；</li><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
            ],
          },
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: false,
          description: '保障车辆碰撞、被盗的损失',
          isFromCtrip: false,
          name: '车损盗抢险',
        },
        {
          code: 'TP',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">钥匙遗落车内导致被盗；</li><li style="margin-bottom:-25px;">钥匙丢失，损坏或被盗；</li><li style="margin-bottom:-25px;">车门忘关导致被盗；</li><li style="margin-bottom:-25px;">车内额外设备如GPS，WIFI，儿童座椅等；</li><li style="margin-bottom:-25px;">在未经授权的区域行驶；</li><li style="margin-bottom:-25px;">将车辆长时间停在不当的地方导致车辆被盗；</li><li style="margin-bottom:-25px;">因车辆被盗被抢导致的车辆停运费或维修手续费；</li><li style="margin-bottom:-25px;">违反合同或当地法律的情况下导致的损失；</li></ul>*实际赔付范围与标准以门店合同为准',
            ],
          },
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          isInclude: true,
          description: '保障车辆被盗的损失',
          isFromCtrip: false,
          name: '车辆盗抢险',
          insuranceDetail: [
            {
              coverageLongDesc: '',
              currencyCode: 'USD',
              coverageShortDesc: '',
              packageId: 3659,
              maxExcess: 0,
              excessShortDesc: '起赔额：0USD',
              minExcess: 0,
              excessLongDesc: '',
            },
            {
              coverageLongDesc: '',
              currencyCode: 'USD',
              coverageShortDesc: '',
              packageId: 3687,
              maxExcess: 0,
              excessShortDesc: '起赔额：0USD',
              minExcess: 0,
              excessLongDesc: '',
            },
          ],
        },
        {
          code: 'ALI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {
            title: '承保范围',
            content: [
              '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: true,
          description: '保障第三方车辆或人员伤害损失',
          isFromCtrip: false,
          name: '百万三者险',
          insuranceDetail: [
            {
              coverageLongDesc: '',
              currencyCode: 'USD',
              coverageShortDesc: '',
              packageId: 3659,
              maxExcess: 0,
              excessShortDesc: '起赔额：0USD',
              minExcess: 0,
              excessLongDesc: '',
            },
            {
              coverageLongDesc: '',
              currencyCode: 'USD',
              coverageShortDesc: '',
              packageId: 3687,
              maxExcess: 0,
              excessShortDesc: '起赔额：0USD',
              minExcess: 0,
              excessLongDesc: '',
            },
          ],
        },
        {
          code: 'PAI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {
            title: '承保范围',
            content: [
              '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: false,
          description: '保障全车人员意外伤害',
          isFromCtrip: false,
          name: '人身意外险',
        },
        {
          code: 'PEI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {
            title: '承保范围',
            content: [
              '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: false,
          description: '保障随车行李丢失',
          isFromCtrip: false,
          name: '个人财物险',
        },
        {
          code: 'PPI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {},
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: false,
          description: '',
          isFromCtrip: false,
          name: '个人意外保险',
        },
      ],
    },
    {
      productInfoList: [
        {
          needFlightNo: false,
          naked: false,
          productCode: 'REMATCHEAVD177RUAWQ4BNYE4',
          priceInfoList: [
            {
              localCarPrice: 257.73,
              cancelRule: {
                isTotalLoss: false,
                isFreeCancel: true,
                isFreeCancelNow: true,
                hours: 0,
                cancelDescription:
                  '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              },
              chargeList: [
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'ACF',
                  payMode: 2,
                  name: '机场建设费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'CFC',
                  payMode: 2,
                  name: '客户设施费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'VLRF',
                  payMode: 2,
                  name: '车辆登记费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'TS',
                  payMode: 2,
                  name: '旅游附加费',
                  netAmount: 0,
                },
                {
                  dueAmount: 1804,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: '1',
                  payMode: 2,
                  name: 'Vehicle rental',
                  netAmount: 0,
                },
                {
                  netAmount: 0,
                  dueAmount: 1400,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'minDeposit',
                  desc: '',
                },
                {
                  netAmount: 0,
                  dueAmount: 245,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'youngDriverExtraFee',
                  desc: '',
                },
              ],
              isContainOnewayFee: false,
              packageId: 2921,
              exchangeRate: 7.00005,
              promotionInfo: {},
              currentPoaPrice: 0,
              currentDailyPrice: 601,
              currentTotalPrice: 1804,
              vendorPromotionList: [],
              localTotalPrice: 257.73,
              currentOnewayfee: 0,
              payMode: 2,
              productId: 'REMATCHEAVD177RUAWQ4BNYE42921',
              mileInfo: {
                name: '不限里程',
                desc: '租期内没有公里数限制',
                isLimited: false,
              },
              currentCurrencyCode: 'CNY',
              ageRestriction: {
                maxDriverAge: 80,
                licenceAge: 1,
                licenceAgeDesc: '驾龄至少满1年',
                youngDriverExtraFee: {
                  currentPrice: 245,
                  localCurrencyCode: 'USD',
                  localPrice: 35,
                  feeType: 0,
                },
                oldDriverExtraFee: {
                  currentPrice: 0,
                  localCurrencyCode: 'USD',
                  localPrice: 0,
                  feeType: 0,
                },
                minDriverAge: 20,
                youngDriverAge: 24,
                description:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAge: 0,
                youngDriverAgeDesc:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAgeDesc: '',
              },
              confirmInfo: {
                confirmTitle: '1小时内确认',
                confirmDesc:
                  '预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.',
                confirmTime: 1,
                confirmRightNow: false,
              },
              localCurrencyCode: 'USD',
              localPrepaidPrice: 257.73,
              currentPrepaidPrice: 1804,
              allTags: [
                {
                  category: 1,
                  sortNum: 10,
                  code: '2',
                  title: '1小时内确认',
                  colorCode: '1',
                  type: 1,
                  description: '预订此产品后供应商将在1小时内确认订单',
                },
                {
                  category: 1,
                  sortNum: 25,
                  code: '2',
                  title: '免费取消',
                  colorCode: '8',
                  type: 1,
                  description:
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                },
                {
                  category: 2,
                  sortNum: 49,
                  code: '2',
                  title: '中文表单',
                  colorCode: '2',
                  type: 1,
                  description:
                    '为提高服务质量，该门店为用户提供中文额外服务表单，避免因语言沟通问题产生强制推销的现象。',
                },
                {
                  category: 2,
                  sortNum: 56,
                  code: '2',
                  title: '随时可订',
                  colorCode: '2',
                  type: 1,
                  description: '该车型随时可预订。',
                },
                {
                  category: 2,
                  sortNum: 60,
                  code: '2',
                  title: '24h营业',
                  colorCode: '2',
                  type: 1,
                  description: '门店24小时营业。',
                },
                {
                  category: 2,
                  sortNum: 66,
                  code: '2',
                  title: '送百万三者险',
                  colorCode: '2',
                  type: 1,
                  description: '该产品送百万三者险。',
                },
                {
                  category: 2,
                  sortNum: 70,
                  code: '2',
                  title: '0起赔额',
                  colorCode: '2',
                  type: 1,
                  description:
                    '该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。',
                },
                {
                  category: 2,
                  sortNum: 80,
                  code: '2',
                  title: '满油取还',
                  colorCode: '2',
                  type: 1,
                  description:
                    '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
                },
                {
                  category: 2,
                  sortNum: 85,
                  code: '2',
                  title: '不限里程',
                  colorCode: '2',
                  type: 1,
                  description: '租期内没有公里数限制。',
                },
                {
                  category: 2,
                  sortNum: 95,
                  code: '2',
                  title: '电子提车凭证',
                  colorCode: '2',
                  type: 1,
                  description:
                    '使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。',
                },
                {
                  category: 2,
                  sortNum: 105,
                  code: '2',
                  title: '送GPS',
                  colorCode: '2',
                  type: 1,
                  description: 'GPS设备，可用于车辆导航。',
                },
              ],
              vcExtendRequest: {},
              packageType: 0,
              currentCarPrice: 1804,
              creditCardInfo: {
                maxDeposit: 0,
                minDeposit: 200,
                description:
                  '取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为US$200.00（约¥1,400.00）,请保证可用额度足以支付押金。押金预计会在还车后28-45天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。',
                cardList: [
                  { name: '美国运通', type: 'AE' },
                  { name: '万事达', type: 'MC' },
                  { name: '大来卡', type: 'DC' },
                  { name: '维萨', type: 'VI' },
                  { name: 'Japanese Credit Bureau Credit Card', type: 'JC' },
                  { name: 'Discover', type: 'DV' },
                  { name: '银联单币信用卡', type: 'UNS' },
                ],
                depositCurrencyCode: 'USD',
              },
              localDailyPrice: 85.91,
              insuranceDetails: [
                {
                  code: 'LDW',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '车损盗抢险',
                  maxExcess: 0,
                },
                {
                  code: 'ALI',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '百万三者险',
                  maxExcess: 0,
                },
              ],
              localPoaPrice: 0,
              localOnewayfee: 0,
            },
          ],
          equipments: [
            {
              equipmentCode: '7',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '婴儿座椅',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 0,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '个月',
              equipmentType: 1,
              equipmentDesc: '通常适用于一周岁以下，体重约0-10公斤的婴儿',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '8',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '儿童座椅',
              ageTo: 6,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 9,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 2,
              equipmentDesc: '通常适用于9个月-6周岁，体重约9-18.2公斤的儿童',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '9',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '岁',
              equipmentName: '儿童增高座垫',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 6,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 3,
              equipmentDesc: '通常适用于6-12周岁，体重约18.2-45.5公斤的儿童',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '13',
              localCurrencyCode: 'USD',
              localDailyPrice: 0,
              ageFromUnit: '0',
              equipmentName: 'GPS',
              ageTo: 0,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 0,
              ageFrom: 0,
              localTotalPrice: 0,
              payMode: 1,
              ageToUnit: '0',
              equipmentType: 4,
              equipmentDesc:
                'GPS（由租车公司门店提供的当地语言的导航仪，价格和库存取决于门店）',
              currentDailyPrice: 0,
            },
          ],
          carRentalMustRead: [
            {
              code: '1',
              title: '确认政策',
              content: ['预订此产品后供应商将在1小时内确认订单'],
              type: 0,
            },
            {
              code: '1',
              title: '取消政策',
              content: [
                '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              ],
              type: 1,
            },
            {
              code: '1',
              title: '押金说明',
              content: [
                '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
              ],
              type: 2,
            },
            { code: '1', title: '里程政策', content: ['不限里程'], type: 3 },
            {
              code: '1',
              title: '燃油政策',
              content: [
                '满油取还',
                '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
              ],
              type: 4,
            },
            {
              code: '1',
              title: '年龄要求',
              content: [
                '驾驶员年龄要求：20-80周岁',
                '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
              ],
              type: 5,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '里程政策',
                  content: ['不限里程'],
                  type: 3,
                },
                {
                  code: '2',
                  title: '燃油政策',
                  content: [
                    '满油取还',
                    '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
                  ],
                  type: 4,
                },
                {
                  code: '2',
                  title: '年龄要求',
                  content: [
                    '驾驶员年龄要求：20-80周岁',
                    '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                    '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
                  ],
                  type: 5,
                },
                {
                  code: '2',
                  title: '额外驾驶员',
                  content: [
                    '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
                  ],
                  type: 6,
                },
                {
                  code: '2',
                  title: '营业时间外取还车',
                  content: [
                    '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
                  ],
                  type: 7,
                },
                {
                  code: '2',
                  title: '当地费用',
                  content: [
                    '订单费用不包括在租车过程中产生的额外收费和罚款，包括过路过桥费、交通拥堵费、停车费、超速罚单或任何其他交通罚款。如果您产生任何费用，例如超速罚单或未付的交通拥堵费，当地管理部门会找到租车公司，让租车公司联系您。一般是还车后几个月内 ，您需要支付租车公司的管理费用以及您需要补交的违章费。',
                  ],
                  type: 16,
                },
              ],
              title: '费用须知',
              sortNum: 4,
              type: 40,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '提前取车',
                  content: [
                    '若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。',
                  ],
                  type: 17,
                },
                {
                  code: '2',
                  title: '延迟取车',
                  content: [
                    '若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。',
                  ],
                  type: 18,
                },
                {
                  code: '2',
                  title: '提前还车',
                  content: [
                    '提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。',
                  ],
                  type: 10,
                },
                {
                  code: '2',
                  title: '延迟还车',
                  content: [
                    '若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。',
                  ],
                  type: 11,
                },
              ],
              title: '提前/延后取还车',
              sortNum: 3,
              type: 41,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '门店提示',
                  content: [
                    '在门店取车时提供的信用卡必须与预付时使用的卡片一致才能取车',
                    '仅接受纸质版实体驾照。（不支持电子驾照）',
                  ],
                  type: 47,
                },
              ],
              title: '温馨提示',
              type: 19,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  subObject: [
                    {
                      title: '百万三者险',
                      content: [
                        '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
                        '保障第三方车辆或人员伤害损失',
                      ],
                    },
                    {
                      title: '车损盗抢险',
                      content: [
                        '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                        '保障车辆碰撞、被盗的损失',
                        '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">单车事故；</li><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                      ],
                    },
                    {
                      title: '人身意外险',
                      content: [
                        '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
                        '保障全车人员意外伤害',
                      ],
                    },
                    {
                      title: '个人财物险',
                      content: [
                        '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
                        '保障随车行李丢失',
                      ],
                    },
                    { title: '个人意外保险', content: ['个人意外保险'] },
                  ],
                  title: '保险，保障范围，豁免',
                  type: 21,
                },
                {
                  code: '2',
                  title: '保险服务提示',
                  content: [
                    '租车公司规定，在租用车辆期间，若发生任何车辆损坏或事故时，请第一时间报警并获得警方报告，否则车辆保险将会失效，车损费用将由您承担。',
                    '租车公司将根据产生车损的次数扣除对应次数的起赔额费用。若最终定损后单次车损的维修费用低于起赔额，会退回该次车损起赔额多收的部分。需赔付单次起赔额的情况：一次事故中造成了多处车损（客人能够提供证明）；一次事故中造成单处车损；需赔付多次起赔额的情况：一次事故中造成了多处车损（客人无法提供证明）；租车期间发生多次车损。',
                  ],
                  type: 45,
                },
                {
                  code: '2',
                  title: '管理费',
                  content: [
                    '还车后租车公司会对车辆损坏进行索赔，并从超额部分扣除金额上增加损坏管理费。',
                    '发生事故时会支付一定费用。这是从超额扣除的金额，是强制性的。这个费用是根据车型组和维修天数计算的。',
                  ],
                  type: 22,
                },
              ],
              title: '租车保障',
              type: 20,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '额外设备',
                  content: [
                    '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
                  ],
                  type: 24,
                },
                {
                  code: '2',
                  title: '税费',
                  content: ['所有额外服务将会收取销售税费和当地费用。'],
                  type: 25,
                },
              ],
              title: '附加服务',
              type: 23,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '跨境政策',
                  content: [
                    '只有以下 Hertz 车组可以开往墨西哥，但不允许在墨西哥还车。请在租车时查询前往墨西哥的授权许可，并购买强制性墨西哥保险：参考价格38美元/天，适用车组(A) Chevrolet Spark 或类似,  (B) Ford Focus 或类似,  (C) Mazda 3 4-door 或类似, (D) VW Jetta 或类似, (E6) Ford Fusion Hybrid 或类似, (F) Chevrolet Malibu 或类似, (F6) Fullsize with Leather 4 Door 或类似, (Q4) Nissan Rogue 或类似。 参考价格48 美元/天，适用车组(F4) Hyundai Santa Fe 或类似, (G) Buick Regal 或类似, (K6) Dodge Journey 或类似, (L) Chevrolet Equinox 或类似, (L4) Nissan Pathfinder 或类似, (O6) Nissan Frontier Crew Cab 或类似, (R) Chrysler Pacifica 或类似。 墨西哥法律不允许墨西哥公民将美国车辆带入墨西哥，或在墨西哥境内驾驶美国车辆。 如果需要开车进入加拿大，承租人需要在租车时告知柜台工作人员。您必须拥有有效的许可证和租赁记录。从这个城市租车不需要在海关出示责任保护信、证书或贴纸即可驾车进入加拿大。某些州可能适用额外和/或不同的限制。请在租赁时查看您的租赁协议以获取更多信息。',
                  ],
                  type: 27,
                },
              ],
              title: '旅行限制',
              type: 26,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '取消政策',
                  content: [
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                  ],
                  type: 30,
                },
                {
                  code: '2',
                  title: '修改',
                  content: [
                    '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
                  ],
                  type: 31,
                },
                {
                  code: '2',
                  title: 'No show（未取车）',
                  content: [
                    'noshow 是指当你：',
                    '计划提前取消订单但未告知我们，或者',
                    '未在规定的时间内取车，或者',
                    '无法提供取车时要出示的文件，或者',
                    '无法提供主驾驶员名下有足够额度的信用卡',
                    '以上情形，你之前预定的订单金额不会退还给你。',
                  ],
                  type: 32,
                },
              ],
              title: '取消，未取车和修改',
              type: 42,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '意外或故障',
                  content: [
                    '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
                  ],
                  type: 34,
                },
                {
                  code: '2',
                  title: '道路救援',
                  content: [
                    '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
                  ],
                  type: 35,
                },
                {
                  code: '2',
                  title: '遗失钥匙',
                  content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
                  type: 36,
                },
                {
                  code: '2',
                  title: '安全带',
                  content: [
                    '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
                  ],
                  type: 37,
                },
                {
                  code: '2',
                  title: '禁止吸烟',
                  content: [
                    '注意所有的车都禁止车内抽烟，如果不遵守被发现，会有罚款。',
                  ],
                  type: 38,
                },
                {
                  code: '2',
                  title: '价格计算',
                  content: [
                    '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
                    '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
                    '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
                    '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
                  ],
                  type: 39,
                },
              ],
              title: '租车公司重要信息',
              type: 33,
            },
            {
              code: '4',
              sortNum: 1,
              title: '额外驾驶员',
              content: [
                '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
              ],
              type: 6,
            },
            {
              code: '4',
              sortNum: 2,
              title: '营业时间外取还车',
              content: [
                '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
              ],
              type: 7,
            },
          ],
          searchUnionPay: false,
          searchCreditCard: false,
          bomGroupCode:
            'LAXZE01_10177_ALI_FRFB_Fees_GPS_LDW_PAI_PEI_PPI_Taxes_ULM_0_0',
          packageItems: [
            {
              code: 'Fees',
              sortNum: 1,
              name: '基础租车费用',
              desc: '仅包含车辆租金的基础费用',
            },
            {
              code: 'Taxes',
              sortNum: 2,
              name: '税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税',
              desc: '税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。',
            },
            {
              code: 'ALI',
              sortNum: 3,
              name: '百万三者险',
              desc: '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'LDW',
              sortNum: 3,
              name: '车损盗抢险',
              desc: '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PAI',
              sortNum: 3,
              name: '人身意外险',
              desc: '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PEI',
              sortNum: 3,
              name: '个人财物险',
              desc: '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PPI',
              sortNum: 3,
              name: '个人意外保险',
              desc: '个人意外保险',
            },
            {
              code: 'ULM',
              sortNum: 4,
              name: '不限里程',
              desc: '租期内没有公里数限制',
            },
            {
              code: 'FRFB',
              sortNum: 4,
              name: '满油取还',
              desc: '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
            },
            {
              code: 'GPS',
              sortNum: 4,
              name: 'GPS设备',
              desc: ' GPS设备，可用于车辆导航。',
            },
          ],
          pickUpMaterials: [
            {
              subObject: [
                { title: '中国大陆护照原件', code: 'CN' },
                { title: '中国香港护照原件', code: 'HK' },
                { title: '中国澳门护照原件', code: 'MO' },
                { title: '中国台湾护照原件', code: 'TW' },
                { title: '其他地区护照原件', code: 'OH' },
              ],
              title: '身份证明文件',
              content: ['护照、驾照发证国家/地区须一致方可成功取车'],
              type: 0,
            },
            {
              subObject: [
                {
                  content: [
                    '当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。',
                    '所有驾驶员驾龄必须至少满1年',
                  ],
                  type: 4,
                },
                {
                  title: '门店支持以下驾照组合(任选其一)',
                  type: 5,
                  subObject: [
                    {
                      code: 'CN',
                      subObject: [
                        {
                          sortNum: 2,
                          title: '中国驾照原件+驾照国际翻译认证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 3,
                          title: '中国驾照原件+车行翻译件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 ',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 4,
                          title: '中国驾照原件+当地语言公证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合(任选其一)',
                      sortNum: 0,
                    },
                    {
                      code: 'HK',
                      subObject: [
                        {
                          sortNum: 37,
                          title: '香港驾照+国际驾照',
                          content: [
                            '香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 1,
                    },
                    {
                      code: 'MO',
                      subObject: [
                        {
                          sortNum: 29,
                          title: '澳门驾照+国际驾照',
                          content: [
                            '澳门驾照：由中国澳门特别行政区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 2,
                    },
                    {
                      code: 'TW',
                      subObject: [
                        {
                          sortNum: 31,
                          title: '台湾驾照+国际驾照',
                          content: [
                            '台湾驾照：由中国台湾地区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 3,
                    },
                    {
                      code: 'OH',
                      subObject: [
                        {
                          sortNum: 11,
                          title: '驾驶员本国驾照+国际驾照',
                          content: [
                            '驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 4,
                    },
                  ],
                },
                {
                  title: '注意：',
                  content: [
                    '因商业条款限制，美国驾照不适用此价格租车。若您使用美国驾照，请移步trip平台预定。',
                  ],
                  type: 10,
                },
              ],
              title: '驾照要求',
              summaryContent: ['所有驾驶员驾龄必须至少满1年'],
              type: 1,
            },
            {
              subObject: [
                {
                  title: '信用卡要求',
                  content: [
                    '请保证可用金额足以支付押金',
                    '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
                    '卡面所示姓名与主驾驶员护照姓名一致',
                  ],
                  type: 6,
                },
                {
                  title: '接受的信用卡',
                  content: [
                    '美国运通，万事达，大来卡，维萨，Japanese Credit Bureau Credit Card，Discover，银联单币信用卡',
                  ],
                  type: 7,
                  urlList: [
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/unionpay.png',
                  ],
                },
                {
                  title: '押金说明',
                  content: [
                    '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
                  ],
                  type: 8,
                },
              ],
              title: '主驾驶员名下国际信用卡',
              content: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              summaryContent: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              type: 2,
            },
            {
              title: '提车凭证',
              content: [
                '取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。',
              ],
              summaryContent: ['订单确认后，携程为您提供'],
              type: 3,
            },
          ],
        },
        {
          needFlightNo: false,
          naked: false,
          productCode: 'REMATCHEAVD177RUAWQ4BNYE4',
          priceInfoList: [
            {
              localCarPrice: 383.1,
              cancelRule: {
                isTotalLoss: false,
                isFreeCancel: true,
                isFreeCancelNow: true,
                hours: 0,
                cancelDescription:
                  '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              },
              chargeList: [
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'ACF',
                  payMode: 2,
                  name: '机场建设费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'CFC',
                  payMode: 2,
                  name: '客户设施费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'VLRF',
                  payMode: 2,
                  name: '车辆登记费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'RTS',
                  payMode: 2,
                  name: '租赁附加费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'TS',
                  payMode: 2,
                  name: '旅游附加费',
                  netAmount: 0,
                },
                {
                  dueAmount: 2682,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: '1',
                  payMode: 2,
                  name: 'Vehicle rental',
                  netAmount: 0,
                },
                {
                  netAmount: 0,
                  dueAmount: 1400,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'minDeposit',
                  desc: '',
                },
                {
                  netAmount: 0,
                  dueAmount: 245,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'youngDriverExtraFee',
                  desc: '',
                },
              ],
              isContainOnewayFee: false,
              packageId: 32055,
              exchangeRate: 7.00005,
              promotionInfo: {},
              currentPoaPrice: 0,
              currentDailyPrice: 894,
              currentTotalPrice: 2682,
              vendorPromotionList: [],
              localTotalPrice: 383.1,
              currentOnewayfee: 0,
              payMode: 2,
              productId: 'REMATCHEAVD177RUAWQ4BNYE432055',
              mileInfo: {
                name: '不限里程',
                desc: '租期内没有公里数限制',
                isLimited: false,
              },
              currentCurrencyCode: 'CNY',
              ageRestriction: {
                maxDriverAge: 80,
                licenceAge: 1,
                licenceAgeDesc: '驾龄至少满1年',
                youngDriverExtraFee: {
                  currentPrice: 245,
                  localCurrencyCode: 'USD',
                  localPrice: 35,
                  feeType: 0,
                },
                oldDriverExtraFee: {
                  currentPrice: 0,
                  localCurrencyCode: 'USD',
                  localPrice: 0,
                  feeType: 0,
                },
                minDriverAge: 20,
                youngDriverAge: 24,
                description:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAge: 0,
                youngDriverAgeDesc:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAgeDesc: '',
              },
              confirmInfo: {
                confirmTitle: '1小时内确认',
                confirmDesc:
                  '预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.',
                confirmTime: 1,
                confirmRightNow: false,
              },
              localCurrencyCode: 'USD',
              localPrepaidPrice: 383.1,
              currentPrepaidPrice: 2682,
              allTags: [
                {
                  category: 1,
                  sortNum: 10,
                  code: '2',
                  title: '1小时内确认',
                  colorCode: '1',
                  type: 1,
                  description: '预订此产品后供应商将在1小时内确认订单',
                },
                {
                  category: 1,
                  sortNum: 25,
                  code: '2',
                  title: '免费取消',
                  colorCode: '8',
                  type: 1,
                  description:
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                },
                {
                  category: 2,
                  sortNum: 49,
                  code: '2',
                  title: '中文表单',
                  colorCode: '2',
                  type: 1,
                  description:
                    '为提高服务质量，该门店为用户提供中文额外服务表单，避免因语言沟通问题产生强制推销的现象。',
                },
                {
                  category: 2,
                  sortNum: 56,
                  code: '2',
                  title: '随时可订',
                  colorCode: '2',
                  type: 1,
                  description: '该车型随时可预订。',
                },
                {
                  category: 2,
                  sortNum: 60,
                  code: '2',
                  title: '24h营业',
                  colorCode: '2',
                  type: 1,
                  description: '门店24小时营业。',
                },
                {
                  category: 2,
                  sortNum: 66,
                  code: '2',
                  title: '送百万三者险',
                  colorCode: '2',
                  type: 1,
                  description: '该产品送百万三者险。',
                },
                {
                  category: 2,
                  sortNum: 70,
                  code: '2',
                  title: '0起赔额',
                  colorCode: '2',
                  type: 1,
                  description:
                    '该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。',
                },
                {
                  category: 2,
                  sortNum: 85,
                  code: '2',
                  title: '不限里程',
                  colorCode: '2',
                  type: 1,
                  description: '租期内没有公里数限制。',
                },
                {
                  category: 2,
                  sortNum: 95,
                  code: '2',
                  title: '电子提车凭证',
                  colorCode: '2',
                  type: 1,
                  description:
                    '使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。',
                },
              ],
              vcExtendRequest: {},
              packageType: 0,
              currentCarPrice: 2682,
              creditCardInfo: {
                maxDeposit: 0,
                minDeposit: 200,
                description:
                  '取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为US$200.00（约¥1,400.00）,请保证可用额度足以支付押金。押金预计会在还车后28-45天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。',
                cardList: [
                  { name: '美国运通', type: 'AE' },
                  { name: '万事达', type: 'MC' },
                  { name: '大来卡', type: 'DC' },
                  { name: '维萨', type: 'VI' },
                  { name: 'Japanese Credit Bureau Credit Card', type: 'JC' },
                  { name: 'Discover', type: 'DV' },
                  { name: '银联单币信用卡', type: 'UNS' },
                ],
                depositCurrencyCode: 'USD',
              },
              localDailyPrice: 127.7,
              insuranceDetails: [
                {
                  code: 'LDW',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '车损盗抢险',
                  maxExcess: 0,
                },
                {
                  code: 'ALI',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '百万三者险',
                  maxExcess: 0,
                },
              ],
              localPoaPrice: 0,
              localOnewayfee: 0,
            },
          ],
          equipments: [
            {
              equipmentCode: '7',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '婴儿座椅',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 0,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '个月',
              equipmentType: 1,
              equipmentDesc: '通常适用于一周岁以下，体重约0-10公斤的婴儿',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '8',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '儿童座椅',
              ageTo: 6,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 9,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 2,
              equipmentDesc: '通常适用于9个月-6周岁，体重约9-18.2公斤的儿童',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '9',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '岁',
              equipmentName: '儿童增高座垫',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 6,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 3,
              equipmentDesc: '通常适用于6-12周岁，体重约18.2-45.5公斤的儿童',
              currentDailyPrice: 98,
            },
          ],
          carRentalMustRead: [
            {
              code: '1',
              title: '确认政策',
              content: ['预订此产品后供应商将在1小时内确认订单'],
              type: 0,
            },
            {
              code: '1',
              title: '取消政策',
              content: [
                '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              ],
              type: 1,
            },
            {
              code: '1',
              title: '押金说明',
              content: [
                '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
              ],
              type: 2,
            },
            { code: '1', title: '里程政策', content: ['不限里程'], type: 3 },
            {
              code: '1',
              title: '燃油政策',
              content: ['一箱油', '取车时送一箱燃油，还车时可任意油量还车。'],
              type: 4,
            },
            {
              code: '1',
              title: '年龄要求',
              content: [
                '驾驶员年龄要求：20-80周岁',
                '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
              ],
              type: 5,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '里程政策',
                  content: ['不限里程'],
                  type: 3,
                },
                {
                  code: '2',
                  title: '燃油政策',
                  content: [
                    '一箱油',
                    '取车时送一箱燃油，还车时可任意油量还车。',
                  ],
                  type: 4,
                },
                {
                  code: '2',
                  title: '年龄要求',
                  content: [
                    '驾驶员年龄要求：20-80周岁',
                    '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                    '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
                  ],
                  type: 5,
                },
                {
                  code: '2',
                  title: '额外驾驶员',
                  content: [
                    '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
                  ],
                  type: 6,
                },
                {
                  code: '2',
                  title: '营业时间外取还车',
                  content: [
                    '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
                  ],
                  type: 7,
                },
                {
                  code: '2',
                  title: '当地费用',
                  content: [
                    '订单费用不包括在租车过程中产生的额外收费和罚款，包括过路过桥费、交通拥堵费、停车费、超速罚单或任何其他交通罚款。如果您产生任何费用，例如超速罚单或未付的交通拥堵费，当地管理部门会找到租车公司，让租车公司联系您。一般是还车后几个月内 ，您需要支付租车公司的管理费用以及您需要补交的违章费。',
                  ],
                  type: 16,
                },
              ],
              title: '费用须知',
              sortNum: 4,
              type: 40,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '提前取车',
                  content: [
                    '若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。',
                  ],
                  type: 17,
                },
                {
                  code: '2',
                  title: '延迟取车',
                  content: [
                    '若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。',
                  ],
                  type: 18,
                },
                {
                  code: '2',
                  title: '提前还车',
                  content: [
                    '提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。',
                  ],
                  type: 10,
                },
                {
                  code: '2',
                  title: '延迟还车',
                  content: [
                    '若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。',
                  ],
                  type: 11,
                },
              ],
              title: '提前/延后取还车',
              sortNum: 3,
              type: 41,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '门店提示',
                  content: [
                    '在门店取车时提供的信用卡必须与预付时使用的卡片一致才能取车',
                    '仅接受纸质版实体驾照。（不支持电子驾照）',
                  ],
                  type: 47,
                },
              ],
              title: '温馨提示',
              type: 19,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  subObject: [
                    {
                      title: '百万三者险',
                      content: [
                        '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
                        '保障第三方车辆或人员伤害损失',
                      ],
                    },
                    {
                      title: '车损盗抢险',
                      content: [
                        '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                        '保障车辆碰撞、被盗的损失',
                        '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">单车事故；</li><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                      ],
                    },
                    {
                      title: '人身意外险',
                      content: [
                        '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
                        '保障全车人员意外伤害',
                      ],
                    },
                    {
                      title: '个人财物险',
                      content: [
                        '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
                        '保障随车行李丢失',
                      ],
                    },
                    { title: '个人意外保险', content: ['个人意外保险'] },
                  ],
                  title: '保险，保障范围，豁免',
                  type: 21,
                },
                {
                  code: '2',
                  title: '保险服务提示',
                  content: [
                    '租车公司规定，在租用车辆期间，若发生任何车辆损坏或事故时，请第一时间报警并获得警方报告，否则车辆保险将会失效，车损费用将由您承担。',
                    '租车公司将根据产生车损的次数扣除对应次数的起赔额费用。若最终定损后单次车损的维修费用低于起赔额，会退回该次车损起赔额多收的部分。需赔付单次起赔额的情况：一次事故中造成了多处车损（客人能够提供证明）；一次事故中造成单处车损；需赔付多次起赔额的情况：一次事故中造成了多处车损（客人无法提供证明）；租车期间发生多次车损。',
                  ],
                  type: 45,
                },
                {
                  code: '2',
                  title: '管理费',
                  content: [
                    '还车后租车公司会对车辆损坏进行索赔，并从超额部分扣除金额上增加损坏管理费。',
                    '发生事故时会支付一定费用。这是从超额扣除的金额，是强制性的。这个费用是根据车型组和维修天数计算的。',
                  ],
                  type: 22,
                },
              ],
              title: '租车保障',
              type: 20,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '额外设备',
                  content: [
                    '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
                  ],
                  type: 24,
                },
                {
                  code: '2',
                  title: '税费',
                  content: ['所有额外服务将会收取销售税费和当地费用。'],
                  type: 25,
                },
              ],
              title: '附加服务',
              type: 23,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '跨境政策',
                  content: [
                    '只有以下 Hertz 车组可以开往墨西哥，但不允许在墨西哥还车。请在租车时查询前往墨西哥的授权许可，并购买强制性墨西哥保险：参考价格38美元/天，适用车组(A) Chevrolet Spark 或类似,  (B) Ford Focus 或类似,  (C) Mazda 3 4-door 或类似, (D) VW Jetta 或类似, (E6) Ford Fusion Hybrid 或类似, (F) Chevrolet Malibu 或类似, (F6) Fullsize with Leather 4 Door 或类似, (Q4) Nissan Rogue 或类似。 参考价格48 美元/天，适用车组(F4) Hyundai Santa Fe 或类似, (G) Buick Regal 或类似, (K6) Dodge Journey 或类似, (L) Chevrolet Equinox 或类似, (L4) Nissan Pathfinder 或类似, (O6) Nissan Frontier Crew Cab 或类似, (R) Chrysler Pacifica 或类似。 墨西哥法律不允许墨西哥公民将美国车辆带入墨西哥，或在墨西哥境内驾驶美国车辆。 如果需要开车进入加拿大，承租人需要在租车时告知柜台工作人员。您必须拥有有效的许可证和租赁记录。从这个城市租车不需要在海关出示责任保护信、证书或贴纸即可驾车进入加拿大。某些州可能适用额外和/或不同的限制。请在租赁时查看您的租赁协议以获取更多信息。',
                  ],
                  type: 27,
                },
              ],
              title: '旅行限制',
              type: 26,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '取消政策',
                  content: [
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                  ],
                  type: 30,
                },
                {
                  code: '2',
                  title: '修改',
                  content: [
                    '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
                  ],
                  type: 31,
                },
                {
                  code: '2',
                  title: 'No show（未取车）',
                  content: [
                    'noshow 是指当你：',
                    '计划提前取消订单但未告知我们，或者',
                    '未在规定的时间内取车，或者',
                    '无法提供取车时要出示的文件，或者',
                    '无法提供主驾驶员名下有足够额度的信用卡',
                    '以上情形，你之前预定的订单金额不会退还给你。',
                  ],
                  type: 32,
                },
              ],
              title: '取消，未取车和修改',
              type: 42,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '意外或故障',
                  content: [
                    '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
                  ],
                  type: 34,
                },
                {
                  code: '2',
                  title: '道路救援',
                  content: [
                    '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
                  ],
                  type: 35,
                },
                {
                  code: '2',
                  title: '遗失钥匙',
                  content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
                  type: 36,
                },
                {
                  code: '2',
                  title: '安全带',
                  content: [
                    '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
                  ],
                  type: 37,
                },
                {
                  code: '2',
                  title: '禁止吸烟',
                  content: [
                    '注意所有的车都禁止车内抽烟，如果不遵守被发现，会有罚款。',
                  ],
                  type: 38,
                },
                {
                  code: '2',
                  title: '价格计算',
                  content: [
                    '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
                    '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
                    '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
                    '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
                  ],
                  type: 39,
                },
              ],
              title: '租车公司重要信息',
              type: 33,
            },
            {
              code: '4',
              sortNum: 1,
              title: '额外驾驶员',
              content: [
                '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
              ],
              type: 6,
            },
            {
              code: '4',
              sortNum: 2,
              title: '营业时间外取还车',
              content: [
                '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
              ],
              type: 7,
            },
          ],
          searchUnionPay: false,
          searchCreditCard: false,
          bomGroupCode:
            'LAXZE01_10177_ALI_FPO_Fees_LDW_PAI_PEI_PPI_Taxes_ULM_0_0',
          packageItems: [
            {
              code: 'Fees',
              sortNum: 1,
              name: '基础租车费用',
              desc: '仅包含车辆租金的基础费用',
            },
            {
              code: 'Taxes',
              sortNum: 2,
              name: '税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税',
              desc: '税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。',
            },
            {
              code: 'ALI',
              sortNum: 3,
              name: '百万三者险',
              desc: '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'LDW',
              sortNum: 3,
              name: '车损盗抢险',
              desc: '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PAI',
              sortNum: 3,
              name: '人身意外险',
              desc: '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PEI',
              sortNum: 3,
              name: '个人财物险',
              desc: '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PPI',
              sortNum: 3,
              name: '个人意外保险',
              desc: '个人意外保险',
            },
            {
              code: 'ULM',
              sortNum: 4,
              name: '不限里程',
              desc: '租期内没有公里数限制',
            },
            {
              code: 'FPO',
              sortNum: 4,
              name: '一箱油',
              desc: '取车时送一箱燃油，还车时可任意油量还车。',
            },
          ],
          pickUpMaterials: [
            {
              subObject: [
                { title: '中国大陆护照原件', code: 'CN' },
                { title: '中国香港护照原件', code: 'HK' },
                { title: '中国澳门护照原件', code: 'MO' },
                { title: '中国台湾护照原件', code: 'TW' },
                { title: '其他地区护照原件', code: 'OH' },
              ],
              title: '身份证明文件',
              content: ['护照、驾照发证国家/地区须一致方可成功取车'],
              type: 0,
            },
            {
              subObject: [
                {
                  content: [
                    '当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。',
                    '所有驾驶员驾龄必须至少满1年',
                  ],
                  type: 4,
                },
                {
                  title: '门店支持以下驾照组合(任选其一)',
                  type: 5,
                  subObject: [
                    {
                      code: 'CN',
                      subObject: [
                        {
                          sortNum: 2,
                          title: '中国驾照原件+驾照国际翻译认证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 3,
                          title: '中国驾照原件+车行翻译件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 ',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 4,
                          title: '中国驾照原件+当地语言公证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合(任选其一)',
                      sortNum: 0,
                    },
                    {
                      code: 'HK',
                      subObject: [
                        {
                          sortNum: 37,
                          title: '香港驾照+国际驾照',
                          content: [
                            '香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 1,
                    },
                    {
                      code: 'MO',
                      subObject: [
                        {
                          sortNum: 29,
                          title: '澳门驾照+国际驾照',
                          content: [
                            '澳门驾照：由中国澳门特别行政区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 2,
                    },
                    {
                      code: 'TW',
                      subObject: [
                        {
                          sortNum: 31,
                          title: '台湾驾照+国际驾照',
                          content: [
                            '台湾驾照：由中国台湾地区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 3,
                    },
                    {
                      code: 'OH',
                      subObject: [
                        {
                          sortNum: 11,
                          title: '驾驶员本国驾照+国际驾照',
                          content: [
                            '驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 4,
                    },
                  ],
                },
                {
                  title: '注意：',
                  content: [
                    '因商业条款限制，美国驾照不适用此价格租车。若您使用美国驾照，请移步trip平台预定。',
                  ],
                  type: 10,
                },
              ],
              title: '驾照要求',
              summaryContent: ['所有驾驶员驾龄必须至少满1年'],
              type: 1,
            },
            {
              subObject: [
                {
                  title: '信用卡要求',
                  content: [
                    '请保证可用金额足以支付押金',
                    '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
                    '卡面所示姓名与主驾驶员护照姓名一致',
                  ],
                  type: 6,
                },
                {
                  title: '接受的信用卡',
                  content: [
                    '美国运通，万事达，大来卡，维萨，Japanese Credit Bureau Credit Card，Discover，银联单币信用卡',
                  ],
                  type: 7,
                  urlList: [
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/unionpay.png',
                  ],
                },
                {
                  title: '押金说明',
                  content: [
                    '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
                  ],
                  type: 8,
                },
              ],
              title: '主驾驶员名下国际信用卡',
              content: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              summaryContent: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              type: 2,
            },
            {
              title: '提车凭证',
              content: [
                '取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。',
              ],
              summaryContent: ['订单确认后，携程为您提供'],
              type: 3,
            },
          ],
        },
        {
          needFlightNo: false,
          naked: false,
          productCode: 'REMATCHEAVD177RUAWQ4BNYE4',
          priceInfoList: [
            {
              localCarPrice: 200.1,
              cancelRule: {
                isTotalLoss: false,
                isFreeCancel: true,
                isFreeCancelNow: true,
                hours: 0,
                cancelDescription:
                  '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              },
              chargeList: [
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'ACF',
                  payMode: 2,
                  name: '机场建设费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'CFC',
                  payMode: 2,
                  name: '客户设施费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'VLRF',
                  payMode: 2,
                  name: '车辆登记费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'TS',
                  payMode: 2,
                  name: '旅游附加费',
                  netAmount: 0,
                },
                {
                  dueAmount: 1401,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: '1',
                  payMode: 2,
                  name: 'Vehicle rental',
                  netAmount: 0,
                },
                {
                  netAmount: 0,
                  dueAmount: 1400,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'minDeposit',
                  desc: '',
                },
                {
                  netAmount: 0,
                  dueAmount: 245,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'youngDriverExtraFee',
                  desc: '',
                },
              ],
              isContainOnewayFee: false,
              packageId: 4223,
              exchangeRate: 7.00005,
              promotionInfo: {},
              currentPoaPrice: 0,
              currentDailyPrice: 467,
              currentTotalPrice: 1401,
              vendorPromotionList: [],
              localTotalPrice: 200.1,
              currentOnewayfee: 0,
              payMode: 2,
              productId: 'REMATCHEAVD177RUAWQ4BNYE44223',
              mileInfo: {
                name: '不限里程',
                desc: '租期内没有公里数限制',
                isLimited: false,
              },
              currentCurrencyCode: 'CNY',
              ageRestriction: {
                maxDriverAge: 80,
                licenceAge: 1,
                licenceAgeDesc: '驾龄至少满1年',
                youngDriverExtraFee: {
                  currentPrice: 245,
                  localCurrencyCode: 'USD',
                  localPrice: 35,
                  feeType: 0,
                },
                oldDriverExtraFee: {
                  currentPrice: 0,
                  localCurrencyCode: 'USD',
                  localPrice: 0,
                  feeType: 0,
                },
                minDriverAge: 20,
                youngDriverAge: 24,
                description:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAge: 0,
                youngDriverAgeDesc:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAgeDesc: '',
              },
              confirmInfo: {
                confirmTitle: '1小时内确认',
                confirmDesc:
                  '预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.',
                confirmTime: 1,
                confirmRightNow: false,
              },
              localCurrencyCode: 'USD',
              localPrepaidPrice: 200.1,
              currentPrepaidPrice: 1401,
              allTags: [
                {
                  category: 1,
                  sortNum: 10,
                  code: '2',
                  title: '1小时内确认',
                  colorCode: '1',
                  type: 1,
                  description: '预订此产品后供应商将在1小时内确认订单',
                },
                {
                  category: 1,
                  sortNum: 25,
                  code: '2',
                  title: '免费取消',
                  colorCode: '8',
                  type: 1,
                  description:
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                },
                {
                  category: 2,
                  sortNum: 49,
                  code: '2',
                  title: '中文表单',
                  colorCode: '2',
                  type: 1,
                  description:
                    '为提高服务质量，该门店为用户提供中文额外服务表单，避免因语言沟通问题产生强制推销的现象。',
                },
                {
                  category: 2,
                  sortNum: 56,
                  code: '2',
                  title: '随时可订',
                  colorCode: '2',
                  type: 1,
                  description: '该车型随时可预订。',
                },
                {
                  category: 2,
                  sortNum: 60,
                  code: '2',
                  title: '24h营业',
                  colorCode: '2',
                  type: 1,
                  description: '门店24小时营业。',
                },
                {
                  category: 2,
                  sortNum: 66,
                  code: '2',
                  title: '送百万三者险',
                  colorCode: '2',
                  type: 1,
                  description: '该产品送百万三者险。',
                },
                {
                  category: 2,
                  sortNum: 70,
                  code: '2',
                  title: '0起赔额',
                  colorCode: '2',
                  type: 1,
                  description:
                    '该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。',
                },
                {
                  category: 2,
                  sortNum: 80,
                  code: '2',
                  title: '满油取还',
                  colorCode: '2',
                  type: 1,
                  description:
                    '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
                },
                {
                  category: 2,
                  sortNum: 85,
                  code: '2',
                  title: '不限里程',
                  colorCode: '2',
                  type: 1,
                  description: '租期内没有公里数限制。',
                },
                {
                  category: 2,
                  sortNum: 95,
                  code: '2',
                  title: '电子提车凭证',
                  colorCode: '2',
                  type: 1,
                  description:
                    '使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。',
                },
              ],
              vcExtendRequest: {},
              packageType: 0,
              currentCarPrice: 1401,
              creditCardInfo: {
                maxDeposit: 0,
                minDeposit: 200,
                description:
                  '取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为US$200.00（约¥1,400.00）,请保证可用额度足以支付押金。押金预计会在还车后28-45天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。',
                cardList: [
                  { name: '美国运通', type: 'AE' },
                  { name: '万事达', type: 'MC' },
                  { name: '大来卡', type: 'DC' },
                  { name: '维萨', type: 'VI' },
                  { name: 'Japanese Credit Bureau Credit Card', type: 'JC' },
                  { name: 'Discover', type: 'DV' },
                  { name: '银联单币信用卡', type: 'UNS' },
                ],
                depositCurrencyCode: 'USD',
              },
              localDailyPrice: 66.7,
              insuranceDetails: [
                {
                  code: 'LDW',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '车损盗抢险',
                  maxExcess: 0,
                },
                {
                  code: 'ALI',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '百万三者险',
                  maxExcess: 0,
                },
              ],
              localPoaPrice: 0,
              localOnewayfee: 0,
            },
          ],
          equipments: [
            {
              equipmentCode: '7',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '婴儿座椅',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 0,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '个月',
              equipmentType: 1,
              equipmentDesc: '通常适用于一周岁以下，体重约0-10公斤的婴儿',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '8',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '儿童座椅',
              ageTo: 6,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 9,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 2,
              equipmentDesc: '通常适用于9个月-6周岁，体重约9-18.2公斤的儿童',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '9',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '岁',
              equipmentName: '儿童增高座垫',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 6,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 3,
              equipmentDesc: '通常适用于6-12周岁，体重约18.2-45.5公斤的儿童',
              currentDailyPrice: 98,
            },
          ],
          carRentalMustRead: [
            {
              code: '1',
              title: '确认政策',
              content: ['预订此产品后供应商将在1小时内确认订单'],
              type: 0,
            },
            {
              code: '1',
              title: '取消政策',
              content: [
                '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              ],
              type: 1,
            },
            {
              code: '1',
              title: '押金说明',
              content: [
                '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
              ],
              type: 2,
            },
            { code: '1', title: '里程政策', content: ['不限里程'], type: 3 },
            {
              code: '1',
              title: '燃油政策',
              content: [
                '满油取还',
                '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
              ],
              type: 4,
            },
            {
              code: '1',
              title: '年龄要求',
              content: [
                '驾驶员年龄要求：20-80周岁',
                '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
              ],
              type: 5,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '里程政策',
                  content: ['不限里程'],
                  type: 3,
                },
                {
                  code: '2',
                  title: '燃油政策',
                  content: [
                    '满油取还',
                    '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
                  ],
                  type: 4,
                },
                {
                  code: '2',
                  title: '年龄要求',
                  content: [
                    '驾驶员年龄要求：20-80周岁',
                    '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                    '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
                  ],
                  type: 5,
                },
                {
                  code: '2',
                  title: '额外驾驶员',
                  content: [
                    '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
                  ],
                  type: 6,
                },
                {
                  code: '2',
                  title: '营业时间外取还车',
                  content: [
                    '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
                  ],
                  type: 7,
                },
                {
                  code: '2',
                  title: '当地费用',
                  content: [
                    '订单费用不包括在租车过程中产生的额外收费和罚款，包括过路过桥费、交通拥堵费、停车费、超速罚单或任何其他交通罚款。如果您产生任何费用，例如超速罚单或未付的交通拥堵费，当地管理部门会找到租车公司，让租车公司联系您。一般是还车后几个月内 ，您需要支付租车公司的管理费用以及您需要补交的违章费。',
                  ],
                  type: 16,
                },
              ],
              title: '费用须知',
              sortNum: 4,
              type: 40,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '提前取车',
                  content: [
                    '若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。',
                  ],
                  type: 17,
                },
                {
                  code: '2',
                  title: '延迟取车',
                  content: [
                    '若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。',
                  ],
                  type: 18,
                },
                {
                  code: '2',
                  title: '提前还车',
                  content: [
                    '提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。',
                  ],
                  type: 10,
                },
                {
                  code: '2',
                  title: '延迟还车',
                  content: [
                    '若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。',
                  ],
                  type: 11,
                },
              ],
              title: '提前/延后取还车',
              sortNum: 3,
              type: 41,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '门店提示',
                  content: [
                    '在门店取车时提供的信用卡必须与预付时使用的卡片一致才能取车',
                    '仅接受纸质版实体驾照。（不支持电子驾照）',
                  ],
                  type: 47,
                },
              ],
              title: '温馨提示',
              type: 19,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  subObject: [
                    {
                      title: '百万三者险',
                      content: [
                        '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
                        '保障第三方车辆或人员伤害损失',
                      ],
                    },
                    {
                      title: '车损盗抢险',
                      content: [
                        '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                        '保障车辆碰撞、被盗的损失',
                        '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">单车事故；</li><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                      ],
                    },
                    {
                      title: '人身意外险',
                      content: [
                        '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
                        '保障全车人员意外伤害',
                      ],
                    },
                    {
                      title: '个人财物险',
                      content: [
                        '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
                        '保障随车行李丢失',
                      ],
                    },
                    { title: '个人意外保险', content: ['个人意外保险'] },
                  ],
                  title: '保险，保障范围，豁免',
                  type: 21,
                },
                {
                  code: '2',
                  title: '保险服务提示',
                  content: [
                    '租车公司规定，在租用车辆期间，若发生任何车辆损坏或事故时，请第一时间报警并获得警方报告，否则车辆保险将会失效，车损费用将由您承担。',
                    '租车公司将根据产生车损的次数扣除对应次数的起赔额费用。若最终定损后单次车损的维修费用低于起赔额，会退回该次车损起赔额多收的部分。需赔付单次起赔额的情况：一次事故中造成了多处车损（客人能够提供证明）；一次事故中造成单处车损；需赔付多次起赔额的情况：一次事故中造成了多处车损（客人无法提供证明）；租车期间发生多次车损。',
                  ],
                  type: 45,
                },
                {
                  code: '2',
                  title: '管理费',
                  content: [
                    '还车后租车公司会对车辆损坏进行索赔，并从超额部分扣除金额上增加损坏管理费。',
                    '发生事故时会支付一定费用。这是从超额扣除的金额，是强制性的。这个费用是根据车型组和维修天数计算的。',
                  ],
                  type: 22,
                },
              ],
              title: '租车保障',
              type: 20,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '额外设备',
                  content: [
                    '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
                  ],
                  type: 24,
                },
                {
                  code: '2',
                  title: '税费',
                  content: ['所有额外服务将会收取销售税费和当地费用。'],
                  type: 25,
                },
              ],
              title: '附加服务',
              type: 23,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '跨境政策',
                  content: [
                    '只有以下 Hertz 车组可以开往墨西哥，但不允许在墨西哥还车。请在租车时查询前往墨西哥的授权许可，并购买强制性墨西哥保险：参考价格38美元/天，适用车组(A) Chevrolet Spark 或类似,  (B) Ford Focus 或类似,  (C) Mazda 3 4-door 或类似, (D) VW Jetta 或类似, (E6) Ford Fusion Hybrid 或类似, (F) Chevrolet Malibu 或类似, (F6) Fullsize with Leather 4 Door 或类似, (Q4) Nissan Rogue 或类似。 参考价格48 美元/天，适用车组(F4) Hyundai Santa Fe 或类似, (G) Buick Regal 或类似, (K6) Dodge Journey 或类似, (L) Chevrolet Equinox 或类似, (L4) Nissan Pathfinder 或类似, (O6) Nissan Frontier Crew Cab 或类似, (R) Chrysler Pacifica 或类似。 墨西哥法律不允许墨西哥公民将美国车辆带入墨西哥，或在墨西哥境内驾驶美国车辆。 如果需要开车进入加拿大，承租人需要在租车时告知柜台工作人员。您必须拥有有效的许可证和租赁记录。从这个城市租车不需要在海关出示责任保护信、证书或贴纸即可驾车进入加拿大。某些州可能适用额外和/或不同的限制。请在租赁时查看您的租赁协议以获取更多信息。',
                  ],
                  type: 27,
                },
              ],
              title: '旅行限制',
              type: 26,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '取消政策',
                  content: [
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                  ],
                  type: 30,
                },
                {
                  code: '2',
                  title: '修改',
                  content: [
                    '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
                  ],
                  type: 31,
                },
                {
                  code: '2',
                  title: 'No show（未取车）',
                  content: [
                    'noshow 是指当你：',
                    '计划提前取消订单但未告知我们，或者',
                    '未在规定的时间内取车，或者',
                    '无法提供取车时要出示的文件，或者',
                    '无法提供主驾驶员名下有足够额度的信用卡',
                    '以上情形，你之前预定的订单金额不会退还给你。',
                  ],
                  type: 32,
                },
              ],
              title: '取消，未取车和修改',
              type: 42,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '意外或故障',
                  content: [
                    '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
                  ],
                  type: 34,
                },
                {
                  code: '2',
                  title: '道路救援',
                  content: [
                    '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
                  ],
                  type: 35,
                },
                {
                  code: '2',
                  title: '遗失钥匙',
                  content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
                  type: 36,
                },
                {
                  code: '2',
                  title: '安全带',
                  content: [
                    '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
                  ],
                  type: 37,
                },
                {
                  code: '2',
                  title: '禁止吸烟',
                  content: [
                    '注意所有的车都禁止车内抽烟，如果不遵守被发现，会有罚款。',
                  ],
                  type: 38,
                },
                {
                  code: '2',
                  title: '价格计算',
                  content: [
                    '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
                    '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
                    '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
                    '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
                  ],
                  type: 39,
                },
              ],
              title: '租车公司重要信息',
              type: 33,
            },
            {
              code: '4',
              sortNum: 1,
              title: '额外驾驶员',
              content: [
                '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
              ],
              type: 6,
            },
            {
              code: '4',
              sortNum: 2,
              title: '营业时间外取还车',
              content: [
                '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
              ],
              type: 7,
            },
          ],
          searchUnionPay: false,
          searchCreditCard: false,
          bomGroupCode:
            'LAXZE01_10177_ALI_FRFB_Fees_LDW_PAI_PEI_PPI_Taxes_ULM_0_0',
          packageItems: [
            {
              code: 'Fees',
              sortNum: 1,
              name: '基础租车费用',
              desc: '仅包含车辆租金的基础费用',
            },
            {
              code: 'Taxes',
              sortNum: 2,
              name: '税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税',
              desc: '税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。',
            },
            {
              code: 'ALI',
              sortNum: 3,
              name: '百万三者险',
              desc: '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'LDW',
              sortNum: 3,
              name: '车损盗抢险',
              desc: '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PAI',
              sortNum: 3,
              name: '人身意外险',
              desc: '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PEI',
              sortNum: 3,
              name: '个人财物险',
              desc: '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PPI',
              sortNum: 3,
              name: '个人意外保险',
              desc: '个人意外保险',
            },
            {
              code: 'ULM',
              sortNum: 4,
              name: '不限里程',
              desc: '租期内没有公里数限制',
            },
            {
              code: 'FRFB',
              sortNum: 4,
              name: '满油取还',
              desc: '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。',
            },
          ],
          pickUpMaterials: [
            {
              subObject: [
                { title: '中国大陆护照原件', code: 'CN' },
                { title: '中国香港护照原件', code: 'HK' },
                { title: '中国澳门护照原件', code: 'MO' },
                { title: '中国台湾护照原件', code: 'TW' },
                { title: '其他地区护照原件', code: 'OH' },
              ],
              title: '身份证明文件',
              content: ['护照、驾照发证国家/地区须一致方可成功取车'],
              type: 0,
            },
            {
              subObject: [
                {
                  content: [
                    '当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。',
                    '所有驾驶员驾龄必须至少满1年',
                  ],
                  type: 4,
                },
                {
                  title: '门店支持以下驾照组合(任选其一)',
                  type: 5,
                  subObject: [
                    {
                      code: 'CN',
                      subObject: [
                        {
                          sortNum: 2,
                          title: '中国驾照原件+驾照国际翻译认证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 3,
                          title: '中国驾照原件+车行翻译件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 ',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 4,
                          title: '中国驾照原件+当地语言公证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合(任选其一)',
                      sortNum: 0,
                    },
                    {
                      code: 'HK',
                      subObject: [
                        {
                          sortNum: 37,
                          title: '香港驾照+国际驾照',
                          content: [
                            '香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 1,
                    },
                    {
                      code: 'MO',
                      subObject: [
                        {
                          sortNum: 29,
                          title: '澳门驾照+国际驾照',
                          content: [
                            '澳门驾照：由中国澳门特别行政区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 2,
                    },
                    {
                      code: 'TW',
                      subObject: [
                        {
                          sortNum: 31,
                          title: '台湾驾照+国际驾照',
                          content: [
                            '台湾驾照：由中国台湾地区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 3,
                    },
                    {
                      code: 'OH',
                      subObject: [
                        {
                          sortNum: 11,
                          title: '驾驶员本国驾照+国际驾照',
                          content: [
                            '驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 4,
                    },
                  ],
                },
                {
                  title: '注意：',
                  content: [
                    '因商业条款限制，美国驾照不适用此价格租车。若您使用美国驾照，请移步trip平台预定。',
                  ],
                  type: 10,
                },
              ],
              title: '驾照要求',
              summaryContent: ['所有驾驶员驾龄必须至少满1年'],
              type: 1,
            },
            {
              subObject: [
                {
                  title: '信用卡要求',
                  content: [
                    '请保证可用金额足以支付押金',
                    '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
                    '卡面所示姓名与主驾驶员护照姓名一致',
                  ],
                  type: 6,
                },
                {
                  title: '接受的信用卡',
                  content: [
                    '美国运通，万事达，大来卡，维萨，Japanese Credit Bureau Credit Card，Discover，银联单币信用卡',
                  ],
                  type: 7,
                  urlList: [
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/unionpay.png',
                  ],
                },
                {
                  title: '押金说明',
                  content: [
                    '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
                  ],
                  type: 8,
                },
              ],
              title: '主驾驶员名下国际信用卡',
              content: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              summaryContent: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              type: 2,
            },
            {
              title: '提车凭证',
              content: [
                '取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。',
              ],
              summaryContent: ['订单确认后，携程为您提供'],
              type: 3,
            },
          ],
        },
        {
          needFlightNo: false,
          naked: false,
          productCode: 'REMATCHEAVD177RUAWQ4BNYE4',
          priceInfoList: [
            {
              localCarPrice: 468.18,
              cancelRule: {
                isTotalLoss: false,
                isFreeCancel: true,
                isFreeCancelNow: true,
                hours: 0,
                cancelDescription:
                  '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              },
              chargeList: [
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'ACF',
                  payMode: 2,
                  name: '机场建设费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'CFC',
                  payMode: 2,
                  name: '客户设施费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'VLRF',
                  payMode: 2,
                  name: '车辆登记费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'RTS',
                  payMode: 2,
                  name: '租赁附加费用',
                  netAmount: 0,
                },
                {
                  dueAmount: 0,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: 'TS',
                  payMode: 2,
                  name: '旅游附加费',
                  netAmount: 0,
                },
                {
                  dueAmount: 3277,
                  currency: 'CNY',
                  isIncludedInRate: true,
                  code: '1',
                  payMode: 2,
                  name: 'Vehicle rental',
                  netAmount: 0,
                },
                {
                  netAmount: 0,
                  dueAmount: 1400,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'minDeposit',
                  desc: '',
                },
                {
                  netAmount: 0,
                  dueAmount: 245,
                  currency: 'CNY',
                  isIncludedInRate: false,
                  code: '',
                  payMode: 2,
                  name: 'youngDriverExtraFee',
                  desc: '',
                },
              ],
              isContainOnewayFee: false,
              packageId: 32056,
              exchangeRate: 7.00005,
              promotionInfo: {},
              currentPoaPrice: 0,
              currentDailyPrice: 1092,
              currentTotalPrice: 3277,
              vendorPromotionList: [],
              localTotalPrice: 468.18,
              currentOnewayfee: 0,
              payMode: 2,
              productId: 'REMATCHEAVD177RUAWQ4BNYE432056',
              mileInfo: {
                name: '不限里程',
                desc: '租期内没有公里数限制',
                isLimited: false,
              },
              currentCurrencyCode: 'CNY',
              ageRestriction: {
                maxDriverAge: 80,
                licenceAge: 1,
                licenceAgeDesc: '驾龄至少满1年',
                youngDriverExtraFee: {
                  currentPrice: 245,
                  localCurrencyCode: 'USD',
                  localPrice: 35,
                  feeType: 0,
                },
                oldDriverExtraFee: {
                  currentPrice: 0,
                  localCurrencyCode: 'USD',
                  localPrice: 0,
                  feeType: 0,
                },
                minDriverAge: 20,
                youngDriverAge: 24,
                description:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAge: 0,
                youngDriverAgeDesc:
                  '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                oldDriverAgeDesc: '',
              },
              confirmInfo: {
                confirmTitle: '1小时内确认',
                confirmDesc:
                  '预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.',
                confirmTime: 1,
                confirmRightNow: false,
              },
              localCurrencyCode: 'USD',
              localPrepaidPrice: 468.18,
              currentPrepaidPrice: 3277,
              allTags: [
                {
                  category: 1,
                  sortNum: 10,
                  code: '2',
                  title: '1小时内确认',
                  colorCode: '1',
                  type: 1,
                  description: '预订此产品后供应商将在1小时内确认订单',
                },
                {
                  category: 1,
                  sortNum: 25,
                  code: '2',
                  title: '免费取消',
                  colorCode: '8',
                  type: 1,
                  description:
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                },
                {
                  category: 2,
                  sortNum: 49,
                  code: '2',
                  title: '中文表单',
                  colorCode: '2',
                  type: 1,
                  description:
                    '为提高服务质量，该门店为用户提供中文额外服务表单，避免因语言沟通问题产生强制推销的现象。',
                },
                {
                  category: 2,
                  sortNum: 56,
                  code: '2',
                  title: '随时可订',
                  colorCode: '2',
                  type: 1,
                  description: '该车型随时可预订。',
                },
                {
                  category: 2,
                  sortNum: 60,
                  code: '2',
                  title: '24h营业',
                  colorCode: '2',
                  type: 1,
                  description: '门店24小时营业。',
                },
                {
                  category: 2,
                  sortNum: 66,
                  code: '2',
                  title: '送百万三者险',
                  colorCode: '2',
                  type: 1,
                  description: '该产品送百万三者险。',
                },
                {
                  category: 2,
                  sortNum: 70,
                  code: '2',
                  title: '0起赔额',
                  colorCode: '2',
                  type: 1,
                  description:
                    '该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。',
                },
                {
                  category: 2,
                  sortNum: 85,
                  code: '2',
                  title: '不限里程',
                  colorCode: '2',
                  type: 1,
                  description: '租期内没有公里数限制。',
                },
                {
                  category: 2,
                  sortNum: 95,
                  code: '2',
                  title: '电子提车凭证',
                  colorCode: '2',
                  type: 1,
                  description:
                    '使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。',
                },
                {
                  category: 2,
                  sortNum: 105,
                  code: '2',
                  title: '送GPS',
                  colorCode: '2',
                  type: 1,
                  description: 'GPS设备，可用于车辆导航。',
                },
              ],
              vcExtendRequest: {},
              packageType: 0,
              currentCarPrice: 3277,
              creditCardInfo: {
                maxDeposit: 0,
                minDeposit: 200,
                description:
                  '取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为US$200.00（约¥1,400.00）,请保证可用额度足以支付押金。押金预计会在还车后28-45天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。',
                cardList: [
                  { name: '美国运通', type: 'AE' },
                  { name: '万事达', type: 'MC' },
                  { name: '大来卡', type: 'DC' },
                  { name: '维萨', type: 'VI' },
                  { name: 'Japanese Credit Bureau Credit Card', type: 'JC' },
                  { name: 'Discover', type: 'DV' },
                  { name: '银联单币信用卡', type: 'UNS' },
                ],
                depositCurrencyCode: 'USD',
              },
              localDailyPrice: 156.06,
              insuranceDetails: [
                {
                  code: 'CDW',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '车辆碰撞险',
                  maxExcess: 0,
                },
                {
                  code: 'TP',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '车辆盗抢险',
                  maxExcess: 0,
                },
                {
                  code: 'ALI',
                  currencyCode: 'USD',
                  minExcess: 0,
                  name: '百万三者险',
                  maxExcess: 0,
                },
              ],
              localPoaPrice: 0,
              localOnewayfee: 0,
            },
          ],
          equipments: [
            {
              equipmentCode: '7',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '婴儿座椅',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 0,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '个月',
              equipmentType: 1,
              equipmentDesc: '通常适用于一周岁以下，体重约0-10公斤的婴儿',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '8',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '个月',
              equipmentName: '儿童座椅',
              ageTo: 6,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 9,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 2,
              equipmentDesc: '通常适用于9个月-6周岁，体重约9-18.2公斤的儿童',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '9',
              localCurrencyCode: 'USD',
              localDailyPrice: 13.99,
              ageFromUnit: '岁',
              equipmentName: '儿童增高座垫',
              ageTo: 12,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 294,
              ageFrom: 6,
              localTotalPrice: 41.97,
              payMode: 1,
              ageToUnit: '岁',
              equipmentType: 3,
              equipmentDesc: '通常适用于6-12周岁，体重约18.2-45.5公斤的儿童',
              currentDailyPrice: 98,
            },
            {
              equipmentCode: '13',
              localCurrencyCode: 'USD',
              localDailyPrice: 0,
              ageFromUnit: '0',
              equipmentName: 'GPS',
              ageTo: 0,
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 0,
              ageFrom: 0,
              localTotalPrice: 0,
              payMode: 1,
              ageToUnit: '0',
              equipmentType: 4,
              equipmentDesc:
                'GPS（由租车公司门店提供的当地语言的导航仪，价格和库存取决于门店）',
              currentDailyPrice: 0,
            },
          ],
          carRentalMustRead: [
            {
              code: '1',
              title: '确认政策',
              content: ['预订此产品后供应商将在1小时内确认订单'],
              type: 0,
            },
            {
              code: '1',
              title: '取消政策',
              content: [
                '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
              ],
              type: 1,
            },
            {
              code: '1',
              title: '押金说明',
              content: [
                '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
              ],
              type: 2,
            },
            { code: '1', title: '里程政策', content: ['不限里程'], type: 3 },
            {
              code: '1',
              title: '燃油政策',
              content: ['一箱油', '取车时送一箱燃油，还车时可任意油量还车。'],
              type: 4,
            },
            {
              code: '1',
              title: '年龄要求',
              content: [
                '驾驶员年龄要求：20-80周岁',
                '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
              ],
              type: 5,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '里程政策',
                  content: ['不限里程'],
                  type: 3,
                },
                {
                  code: '2',
                  title: '燃油政策',
                  content: [
                    '一箱油',
                    '取车时送一箱燃油，还车时可任意油量还车。',
                  ],
                  type: 4,
                },
                {
                  code: '2',
                  title: '年龄要求',
                  content: [
                    '驾驶员年龄要求：20-80周岁',
                    '租车公司对20-24周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：US$35.00（约¥245.00）/天；需门店支付，不含税',
                    '额外驾驶员的年龄限制和收费标准，实际请以门店为准。',
                  ],
                  type: 5,
                },
                {
                  code: '2',
                  title: '额外驾驶员',
                  content: [
                    '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
                  ],
                  type: 6,
                },
                {
                  code: '2',
                  title: '营业时间外取还车',
                  content: [
                    '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
                  ],
                  type: 7,
                },
                {
                  code: '2',
                  title: '当地费用',
                  content: [
                    '订单费用不包括在租车过程中产生的额外收费和罚款，包括过路过桥费、交通拥堵费、停车费、超速罚单或任何其他交通罚款。如果您产生任何费用，例如超速罚单或未付的交通拥堵费，当地管理部门会找到租车公司，让租车公司联系您。一般是还车后几个月内 ，您需要支付租车公司的管理费用以及您需要补交的违章费。',
                  ],
                  type: 16,
                },
              ],
              title: '费用须知',
              sortNum: 4,
              type: 40,
            },
            {
              code: '3',
              subObject: [
                {
                  code: '2',
                  title: '提前取车',
                  content: [
                    '若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。',
                  ],
                  type: 17,
                },
                {
                  code: '2',
                  title: '延迟取车',
                  content: [
                    '若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。',
                  ],
                  type: 18,
                },
                {
                  code: '2',
                  title: '提前还车',
                  content: [
                    '提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。',
                  ],
                  type: 10,
                },
                {
                  code: '2',
                  title: '延迟还车',
                  content: [
                    '若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。',
                  ],
                  type: 11,
                },
              ],
              title: '提前/延后取还车',
              sortNum: 3,
              type: 41,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '门店提示',
                  content: [
                    '在门店取车时提供的信用卡必须与预付时使用的卡片一致才能取车',
                    '仅接受纸质版实体驾照。（不支持电子驾照）',
                  ],
                  type: 47,
                },
              ],
              title: '温馨提示',
              type: 19,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  subObject: [
                    {
                      title: '百万三者险',
                      content: [
                        '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
                        '保障第三方车辆或人员伤害损失',
                      ],
                    },
                    {
                      title: '车辆碰撞险',
                      content: [
                        '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                        '保障车辆碰撞损失',
                        '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
                      ],
                    },
                    {
                      title: '人身意外险',
                      content: [
                        '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
                        '保障全车人员意外伤害',
                      ],
                    },
                    {
                      title: '个人财物险',
                      content: [
                        '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
                        '保障随车行李丢失',
                      ],
                    },
                    {
                      title: '车辆盗抢险',
                      content: [
                        '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
                        '保障车辆被盗的损失',
                        '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">钥匙遗落车内导致被盗；</li><li style="margin-bottom:-25px;">钥匙丢失，损坏或被盗；</li><li style="margin-bottom:-25px;">车门忘关导致被盗；</li><li style="margin-bottom:-25px;">车内额外设备如GPS，WIFI，儿童座椅等；</li><li style="margin-bottom:-25px;">在未经授权的区域行驶；</li><li style="margin-bottom:-25px;">将车辆长时间停在不当的地方导致车辆被盗；</li><li style="margin-bottom:-25px;">因车辆被盗被抢导致的车辆停运费或维修手续费；</li><li style="margin-bottom:-25px;">违反合同或当地法律的情况下导致的损失；</li></ul>*实际赔付范围与标准以门店合同为准',
                      ],
                    },
                  ],
                  title: '保险，保障范围，豁免',
                  type: 21,
                },
                {
                  code: '2',
                  title: '保险服务提示',
                  content: [
                    '租车公司规定，在租用车辆期间，若发生任何车辆损坏或事故时，请第一时间报警并获得警方报告，否则车辆保险将会失效，车损费用将由您承担。',
                    '租车公司将根据产生车损的次数扣除对应次数的起赔额费用。若最终定损后单次车损的维修费用低于起赔额，会退回该次车损起赔额多收的部分。需赔付单次起赔额的情况：一次事故中造成了多处车损（客人能够提供证明）；一次事故中造成单处车损；需赔付多次起赔额的情况：一次事故中造成了多处车损（客人无法提供证明）；租车期间发生多次车损。',
                  ],
                  type: 45,
                },
                {
                  code: '2',
                  title: '管理费',
                  content: [
                    '还车后租车公司会对车辆损坏进行索赔，并从超额部分扣除金额上增加损坏管理费。',
                    '发生事故时会支付一定费用。这是从超额扣除的金额，是强制性的。这个费用是根据车型组和维修天数计算的。',
                  ],
                  type: 22,
                },
              ],
              title: '租车保障',
              type: 20,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '额外设备',
                  content: [
                    '额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。',
                  ],
                  type: 24,
                },
                {
                  code: '2',
                  title: '税费',
                  content: ['所有额外服务将会收取销售税费和当地费用。'],
                  type: 25,
                },
              ],
              title: '附加服务',
              type: 23,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '跨境政策',
                  content: [
                    '只有以下 Hertz 车组可以开往墨西哥，但不允许在墨西哥还车。请在租车时查询前往墨西哥的授权许可，并购买强制性墨西哥保险：参考价格38美元/天，适用车组(A) Chevrolet Spark 或类似,  (B) Ford Focus 或类似,  (C) Mazda 3 4-door 或类似, (D) VW Jetta 或类似, (E6) Ford Fusion Hybrid 或类似, (F) Chevrolet Malibu 或类似, (F6) Fullsize with Leather 4 Door 或类似, (Q4) Nissan Rogue 或类似。 参考价格48 美元/天，适用车组(F4) Hyundai Santa Fe 或类似, (G) Buick Regal 或类似, (K6) Dodge Journey 或类似, (L) Chevrolet Equinox 或类似, (L4) Nissan Pathfinder 或类似, (O6) Nissan Frontier Crew Cab 或类似, (R) Chrysler Pacifica 或类似。 墨西哥法律不允许墨西哥公民将美国车辆带入墨西哥，或在墨西哥境内驾驶美国车辆。 如果需要开车进入加拿大，承租人需要在租车时告知柜台工作人员。您必须拥有有效的许可证和租赁记录。从这个城市租车不需要在海关出示责任保护信、证书或贴纸即可驾车进入加拿大。某些州可能适用额外和/或不同的限制。请在租赁时查看您的租赁协议以获取更多信息。',
                  ],
                  type: 27,
                },
              ],
              title: '旅行限制',
              type: 26,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '取消政策',
                  content: [
                    '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
                  ],
                  type: 30,
                },
                {
                  code: '2',
                  title: '修改',
                  content: [
                    '如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。',
                  ],
                  type: 31,
                },
                {
                  code: '2',
                  title: 'No show（未取车）',
                  content: [
                    'noshow 是指当你：',
                    '计划提前取消订单但未告知我们，或者',
                    '未在规定的时间内取车，或者',
                    '无法提供取车时要出示的文件，或者',
                    '无法提供主驾驶员名下有足够额度的信用卡',
                    '以上情形，你之前预定的订单金额不会退还给你。',
                  ],
                  type: 32,
                },
              ],
              title: '取消，未取车和修改',
              type: 42,
            },
            {
              code: '2',
              subObject: [
                {
                  code: '2',
                  title: '意外或故障',
                  content: [
                    '事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。',
                  ],
                  type: 34,
                },
                {
                  code: '2',
                  title: '道路救援',
                  content: [
                    '道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。',
                  ],
                  type: 35,
                },
                {
                  code: '2',
                  title: '遗失钥匙',
                  content: ['遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用'],
                  type: 36,
                },
                {
                  code: '2',
                  title: '安全带',
                  content: [
                    '安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。',
                  ],
                  type: 37,
                },
                {
                  code: '2',
                  title: '禁止吸烟',
                  content: [
                    '注意所有的车都禁止车内抽烟，如果不遵守被发现，会有罚款。',
                  ],
                  type: 38,
                },
                {
                  code: '2',
                  title: '价格计算',
                  content: [
                    '价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。',
                    '取车的时候，客人可能会选择不同的车，而不是预订时候的车。',
                    '如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。',
                    '如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。',
                  ],
                  type: 39,
                },
              ],
              title: '租车公司重要信息',
              type: 33,
            },
            {
              code: '4',
              sortNum: 1,
              title: '额外驾驶员',
              content: [
                '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
              ],
              type: 6,
            },
            {
              code: '4',
              sortNum: 2,
              title: '营业时间外取还车',
              content: [
                '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用，或者是否支持营业时间外取还车。',
              ],
              type: 7,
            },
          ],
          searchUnionPay: false,
          searchCreditCard: false,
          bomGroupCode:
            'LAXZE01_10177_ALI_CDW_FPO_Fees_GPS_PAI_PEI_TP_Taxes_ULM_0_0',
          packageItems: [
            {
              code: 'Fees',
              sortNum: 1,
              name: '基础租车费用',
              desc: '仅包含车辆租金的基础费用',
            },
            {
              code: 'Taxes',
              sortNum: 2,
              name: '税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税',
              desc: '税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。',
            },
            {
              code: 'ALI',
              sortNum: 3,
              name: '百万三者险',
              desc: '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'CDW',
              sortNum: 3,
              name: '车辆碰撞险',
              desc: '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PAI',
              sortNum: 3,
              name: '人身意外险',
              desc: '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'PEI',
              sortNum: 3,
              name: '个人财物险',
              desc: '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'TP',
              sortNum: 3,
              name: '车辆盗抢险',
              desc: '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            },
            {
              code: 'ULM',
              sortNum: 4,
              name: '不限里程',
              desc: '租期内没有公里数限制',
            },
            {
              code: 'FPO',
              sortNum: 4,
              name: '一箱油',
              desc: '取车时送一箱燃油，还车时可任意油量还车。',
            },
            {
              code: 'GPS',
              sortNum: 4,
              name: 'GPS设备',
              desc: ' GPS设备，可用于车辆导航。',
            },
          ],
          pickUpMaterials: [
            {
              subObject: [
                { title: '中国大陆护照原件', code: 'CN' },
                { title: '中国香港护照原件', code: 'HK' },
                { title: '中国澳门护照原件', code: 'MO' },
                { title: '中国台湾护照原件', code: 'TW' },
                { title: '其他地区护照原件', code: 'OH' },
              ],
              title: '身份证明文件',
              content: ['护照、驾照发证国家/地区须一致方可成功取车'],
              type: 0,
            },
            {
              subObject: [
                {
                  content: [
                    '当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。',
                    '所有驾驶员驾龄必须至少满1年',
                  ],
                  type: 4,
                },
                {
                  title: '门店支持以下驾照组合(任选其一)',
                  type: 5,
                  subObject: [
                    {
                      code: 'CN',
                      subObject: [
                        {
                          sortNum: 2,
                          title: '中国驾照原件+驾照国际翻译认证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 3,
                          title: '中国驾照原件+车行翻译件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 ',
                          ],
                          type: 0,
                        },
                        {
                          sortNum: 4,
                          title: '中国驾照原件+当地语言公证件',
                          content: [
                            '中国驾照原件：中国驾照原件',
                            '当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合(任选其一)',
                      sortNum: 0,
                    },
                    {
                      code: 'HK',
                      subObject: [
                        {
                          sortNum: 37,
                          title: '香港驾照+国际驾照',
                          content: [
                            '香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 1,
                    },
                    {
                      code: 'MO',
                      subObject: [
                        {
                          sortNum: 29,
                          title: '澳门驾照+国际驾照',
                          content: [
                            '澳门驾照：由中国澳门特别行政区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 2,
                    },
                    {
                      code: 'TW',
                      subObject: [
                        {
                          sortNum: 31,
                          title: '台湾驾照+国际驾照',
                          content: [
                            '台湾驾照：由中国台湾地区颁发的驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 3,
                    },
                    {
                      code: 'OH',
                      subObject: [
                        {
                          sortNum: 11,
                          title: '驾驶员本国驾照+国际驾照',
                          content: [
                            '驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照',
                            '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
                          ],
                          type: 0,
                        },
                      ],
                      title: '门店支持以下驾照组合',
                      sortNum: 4,
                    },
                  ],
                },
                {
                  title: '注意：',
                  content: [
                    '因商业条款限制，美国驾照不适用此价格租车。若您使用美国驾照，请移步trip平台预定。',
                  ],
                  type: 10,
                },
              ],
              title: '驾照要求',
              summaryContent: ['所有驾驶员驾龄必须至少满1年'],
              type: 1,
            },
            {
              subObject: [
                {
                  title: '信用卡要求',
                  content: [
                    '请保证可用金额足以支付押金',
                    '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
                    '卡面所示姓名与主驾驶员护照姓名一致',
                  ],
                  type: 6,
                },
                {
                  title: '接受的信用卡',
                  content: [
                    '美国运通，万事达，大来卡，维萨，Japanese Credit Bureau Credit Card，Discover，银联单币信用卡',
                  ],
                  type: 7,
                  urlList: [
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png',
                    'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/unionpay.png',
                  ],
                },
                {
                  title: '押金说明',
                  content: [
                    '取车时刷取押金预授权，预计押金至少为US$200.00（约¥1,400.00） 。还车后28-45天内退还。',
                  ],
                  type: 8,
                },
              ],
              title: '主驾驶员名下国际信用卡',
              content: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              summaryContent: [
                '带芯片，卡号为凸字（摸起来有凹凸感），支持银联双币卡（卡面可带银联标志）',
              ],
              type: 2,
            },
            {
              title: '提车凭证',
              content: [
                '取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。',
              ],
              summaryContent: ['订单确认后，携程为您提供'],
              type: 3,
            },
          ],
        },
      ],
      combinations: [
        {
          codes: ['Fuel'],
          dayPrice: 467,
          gapPrice: 0,
          title: '满油取还',
          totalPrice: 1401,
          bomCode: 'LAXZE01_10177_ALI_FRFB_Fees_LDW_PAI_PEI_PPI_Taxes_ULM_0_0',
          stepPrice: 0,
          hike: false,
          currency: 'CNY',
          payMode: 2,
          packageId: 4223,
        },
        {
          codes: ['Fuel', 'GPS'],
          dayPrice: 601,
          gapPrice: 134,
          title: '满油取还 + GPS设备',
          totalPrice: 1804,
          bomCode:
            'LAXZE01_10177_ALI_FRFB_Fees_GPS_LDW_PAI_PEI_PPI_Taxes_ULM_0_0',
          stepPrice: 134,
          hike: false,
          currency: 'CNY',
          payMode: 2,
          packageId: 2921,
        },
        {
          codes: ['Fuel'],
          dayPrice: 894,
          gapPrice: 427,
          title: '一箱油',
          totalPrice: 2682,
          bomCode: 'LAXZE01_10177_ALI_FPO_Fees_LDW_PAI_PEI_PPI_Taxes_ULM_0_0',
          stepPrice: 293,
          hike: false,
          currency: 'CNY',
          payMode: 2,
          packageId: 32055,
        },
        {
          codes: ['Fuel', 'GPS'],
          dayPrice: 1092,
          gapPrice: 625,
          title: '一箱油 + GPS设备',
          totalPrice: 3277,
          bomCode:
            'LAXZE01_10177_ALI_CDW_FPO_Fees_GPS_PAI_PEI_TP_Taxes_ULM_0_0',
          stepPrice: 198,
          hike: false,
          currency: 'CNY',
          payMode: 2,
          packageId: 32056,
        },
      ],
      insPackageId: 59,
      insuranceItems: [
        {
          code: 'LDW',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">单车事故；</li><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
            ],
          },
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: true,
          description: '保障车辆碰撞、被盗的损失',
          isFromCtrip: false,
          name: '车损盗抢险',
          insuranceDetail: [
            {
              coverageLongDesc: '',
              currencyCode: 'USD',
              coverageShortDesc: '',
              packageId: 4223,
              maxExcess: 0,
              excessShortDesc: '起赔额：0USD',
              minExcess: 0,
              excessLongDesc: '',
            },
          ],
        },
        {
          code: 'CDW',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">车辆涉水造成的车辆损坏；</li><li style="margin-bottom:-25px;">因加错燃油类型导致车辆损坏；</li><li style="margin-bottom:-25px;">碰撞到野生动物造成的车辆损失；</li><li style="margin-bottom:-25px;">不爱惜车辆，在非正常公路行驶；</li><li style="margin-bottom:-25px;">在正常道路行驶期间，对于国家公共设施的损坏；</li><li style="margin-bottom:-25px;">对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；</li> <li style="margin-bottom:-25px;">由未登记“额外驾驶员”的其他人驾驶车辆；</li><li style="margin-bottom:-25px;">驾驶车辆跨境未告知工作人员，出境后发生车损；</li><li style="margin-bottom:-25px;">未经租车公司授权的修车，拖车产生的费用或损失；</li><li style="margin-bottom:-25px;">超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；</li><li style="margin-bottom:-25px;">车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；</li><li style="margin-bottom:-25px;">货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；</li><listyle="margin-bottom:-25px;">由于自然灾害，或不可抗力因素导致的损失。</li><li  style="margin-bottom:-25px;"></ul>*实际赔付范围与标准以门店合同为准',
            ],
          },
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          isInclude: false,
          description: '保障车辆碰撞损失',
          isFromCtrip: false,
          name: '车辆碰撞险',
        },
        {
          code: 'TP',
          unConverageExplain: {
            title: '不承保范围',
            content: [
              '不承保范围：<br/><ul style="list-style-type:disc;margin-left:15px;"><li style="margin-bottom:-25px;">钥匙遗落车内导致被盗；</li><li style="margin-bottom:-25px;">钥匙丢失，损坏或被盗；</li><li style="margin-bottom:-25px;">车门忘关导致被盗；</li><li style="margin-bottom:-25px;">车内额外设备如GPS，WIFI，儿童座椅等；</li><li style="margin-bottom:-25px;">在未经授权的区域行驶；</li><li style="margin-bottom:-25px;">将车辆长时间停在不当的地方导致车辆被盗；</li><li style="margin-bottom:-25px;">因车辆被盗被抢导致的车辆停运费或维修手续费；</li><li style="margin-bottom:-25px;">违反合同或当地法律的情况下导致的损失；</li></ul>*实际赔付范围与标准以门店合同为准',
            ],
          },
          converageExplain: {
            title: '承保范围',
            content: [
              '车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            subObject: [
              {
                title: '发生事故后报警并联系车行',
                content: [
                  '发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况',
                ],
              },
              {
                title: '等待最终定审(45-60个工作日左右)',
                content: [
                  '在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价',
                ],
              },
            ],
          },
          isInclude: false,
          description: '保障车辆被盗的损失',
          isFromCtrip: false,
          name: '车辆盗抢险',
        },
        {
          code: 'ALI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {
            title: '承保范围',
            content: [
              '若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: true,
          description: '保障第三方车辆或人员伤害损失',
          isFromCtrip: false,
          name: '百万三者险',
          insuranceDetail: [
            {
              coverageLongDesc: '',
              currencyCode: 'USD',
              coverageShortDesc: '',
              packageId: 4223,
              maxExcess: 0,
              excessShortDesc: '起赔额：0USD',
              minExcess: 0,
              excessLongDesc: '',
            },
          ],
        },
        {
          code: 'PAI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {
            title: '承保范围',
            content: [
              '保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: true,
          description: '保障全车人员意外伤害',
          isFromCtrip: false,
          name: '人身意外险',
          insuranceDetail: [],
        },
        {
          code: 'PEI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {
            title: '承保范围',
            content: [
              '在正常租赁驾驶期间随车行李及财物丢失导致的损失，将由保险公司承担保额内的费用。*实际赔付范围与标准以门店合同为准',
            ],
          },
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: true,
          description: '保障随车行李丢失',
          isFromCtrip: false,
          name: '个人财物险',
          insuranceDetail: [],
        },
        {
          code: 'PPI',
          unConverageExplain: { title: '不承保范围' },
          converageExplain: {},
          claimProcess: {
            title: '理赔流程',
            content: ['以车行合同披露为准'],
          },
          isInclude: true,
          description: '',
          isFromCtrip: false,
          name: '个人意外保险',
          insuranceDetail: [],
        },
      ],
    },
  ],
  flightDelayRule: {
    rules: [
      { title: '航班延误车辆保留规则', descs: ['门店将为您保留车辆1小时。'] },
      {
        title: '航班延误取消规则',
        descs: ['若航班延误无法免费取消订单，门店将按照取消规则处理。'],
      },
    ],
    title: '航班延误车辆保留规则',
    description: '门店将为您保留车辆至当天11:00。',
    subDesc: '门店将为您保留车辆至当天11:00。',
  },
  trunkInfo: {
    content: '行李箱说明',
    url: 'https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png',
  },
  vendorInfo: {
    vendorImageUrl: '//dimg04.c-ctrip.com/images/0AS5x120009e9t4h6F8D4.png',
    vendorCode: 'SD0003',
    vendorTag: { title: '国际知名', sortNum: 0 },
    vendorName: 'Hertz',
    haveCoupon: false,
    bizVendorCode: '14001',
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 2437,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 2437,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1684381848334,
    afterFetch: 1684381850771,
  },
});
ProductReqAndResData.setData(ProductReqAndResData.keyList.priceRes, {
  zhimaInfo: {
    isSupportZhima: true,
    idNo: '',
    userName: '',
    authStatus: 7,
    orderId: '1111111111',
    requestId: 'alipay230427031441202b6ye',
    authedCountEqOne: false,
    authCount: 1,
    authOrderCount: 0,
    authUrl: '',
    sameDriver: true,
    authInfos: [
      {
        contents: [
          {
            contentStyle: '1',
            stringObjs: [
              {
                content:
                  '免AU$200（约￥925）押金，您当前授权￥500.00免押额度，不足本单要求的￥925.00额度，需重新授权',
                style: '1',
              },
            ],
          },
        ],
        note: '重新授权后上笔授权将自动接解除',
        type: 4,
        button: { title: '重新授权', url: 'ReAuthentication' },
        style: '1',
      },
    ],
  },
  depositInfo: {
    title: '押金说明',
    items: [
      {
        title: '押金',
        description: '取车时刷取押金预授权，还车后7-30天内退还。',
        contents: [
          {
            stringObjs: [
              {
                content: 'AU$200.00（约￥925.00）',
              },
            ],
          },
        ],
        code: 'RentalDeposit',
        type: 305,
        currencyCode: 'USD',
        currentTotalPrice: 0.0,
        showFree: false,
        retractable: true,
        positiveDesc: '可退',
        sortNum: 1,
      },
      {
        title: '支持卡种',
        description: '需要芯片卡，卡号为凸字（摸起来有凹凸感）',
        code: 'RentalDeposit',
        type: 305,
        showFree: false,
        retractable: false,
        showCreditCard: true,
        sortNum: 2,
      },
    ],
    notices: [
      '若您还车时有涉及车损、违章等费用，建议在还车时与供应商核对后现场支付，以免发生费用争议。若还车时未涉及车损、违章等费用，押金预计在还车后60天左右解冻。',
    ],
  },
});

const mockDepositPayInfos = [
  {
    depositTypeInfo: {
      title: {
        contentStyle: '2',
        stringObjs: [
          {
            content: '授权芝麻信用免押',
            style: '2',
          },
        ],
      },
      desc: [
        {
          contentStyle: '2',
          stringObjs: [
            {
              content: '芝麻信用≥650分，支付后在订单页认证可免押金',
              style: '2',
            },
          ],
        },
      ],
    },
    sortNum: 2,
    depositPayType: 1,
    isClickable: false,
    isEnable: false,
    isCheck: true,
  },
];

const mockPreferentialTips = {
  type: 1,
  description: '最大优惠￥7',
};

describe('Booking Selectors getNewCouponTip', () => {
  const mockStateMap = [
    {
      state: {},
      priceRes: {
        preferentialTips: mockPreferentialTips,
      },
      expected: '已享最大优惠￥7',
    },
  ];
  test.each(mockStateMap)(
    'getNewCouponTip',
    ({ state, priceRes, expected }) => {
      jest.spyOn(ProductSelectors, 'getPriceResData').mockReturnValue(priceRes);

      const data = getNewCouponTip(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Booking Selectors getNewDepositData', () => {
  const mockStateMap = [
    {
      state: {},
      priceRes: {
        depositPayInfos: mockDepositPayInfos,
      },
      expected: {
        depositTitle: '授权芝麻信用免押',
        depositDescs: [
          {
            contentStyle: '2',
            stringObjs: [
              {
                content: '芝麻信用≥650分，支付后在订单页认证可免押金',
                style: '2',
              },
            ],
          },
        ],
        opeButtonInfo: undefined,
        fundStringObjs: null,
        isZero: false,
        selectPassengerObj: undefined,
        depositRuleObj: null,
      },
    },
  ];
  test.each(mockStateMap)(
    'getNewDepositData',
    ({ state, priceRes, expected }) => {
      jest.spyOn(ProductSelectors, 'getPriceResData').mockReturnValue(priceRes);
      const data = getNewDepositData(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Booking Selectors validateIsMergeDeposit', () => {
  const mockStateMap = [
    {
      state: {},
      isCtripIsd: false,
      expected: false,
    },
    {
      state: {},
      isCtripIsd: true,
      priceRes: {
        trackInfo: {
          vendorCode: '58344',
          vendorPlatFrom: 10,
          depositFreeType: 2,
          depositType: 10,
          riskOriginal: '0',
          riskFinal: '0',
          zhimaResult: '0',
          zhimaResultDesc: 'UNAUTHORIZED',
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)(
    'validateIsMergeDeposit',
    ({ state, isCtripIsd, priceRes, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(isCtripIsd);
      jest.spyOn(ProductSelectors, 'getPriceResData').mockReturnValue(priceRes);
      const data = validateIsMergeDeposit(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Booking Selectors getAuthInfo', () => {
  const mockStateMap = [
    {
      state: {},
      expected: {
        authStatus: 7,
        type: 4,
        content: [
          '免AU$200（约￥925）押金，您当前授权￥500.00免押额度，不足本单要求的￥925.00额度，需重新授权',
        ],
        note: '重新授权后上笔授权将自动接解除',
        button: { title: '重新授权', url: 'ReAuthentication' },
      },
    },
  ];
  test.each(mockStateMap)('getAuthInfo', ({ state, expected }) => {
    const data = getAuthInfo(state);
    expect(data).toEqual(expected);
  });
});

describe('Booking Selectors getDepositMethodData', () => {
  const mockStateMap = [
    {
      state: {},
      priceRes: {
        depositPayInfos: [
          {
            depositPayType: 5,
            isEnable: false,
            isCheck: false,
            isClickable: true,
            depositTypeInfo: {
              title: {
                contentStyle: '2',
                stringObjs: [
                  {
                    content: '免押金',
                    style: '2',
                  },
                ],
              },
              desc: [
                {
                  contentStyle: '3',
                  stringObjs: [
                    {
                      content: '免AU$200.00（约￥925.00）车辆押金',
                      style: '1',
                    },
                  ],
                },
              ],
            },
            sortNum: 1,
          },
          {
            depositPayType: 1,
            isEnable: true,
            isCheck: true,
            isClickable: true,
            depositTypeInfo: {
              title: {
                contentStyle: '1',
                stringObjs: [
                  {
                    content: '到店支付押金',
                    style: '1',
                  },
                ],
              },
              desc: [
                {
                  contentStyle: '1',
                  stringObjs: [
                    {
                      content: '取车时在门店使用信用卡刷取如下押金',
                      style: '1',
                    },
                  ],
                },
              ],
            },
            sortNum: 2,
          },
        ],
      },
      expected: {
        depositPays: [
          {
            depositPayType: 5,
            desc: '免AU$200.00（约￥925.00）车辆押金',
            isCheck: false,
            isEnable: false,
            title: '免押金',
          },
          {
            depositPayType: 1,
            desc: '取车时在门店使用信用卡刷取如下押金',
            isCheck: true,
            isEnable: true,
            title: '到店支付押金',
          },
        ],
        isShowAuthInfo: true,
      },
    },
  ];
  test.each(mockStateMap)(
    'getDepositMethodData',
    ({ state, priceRes, expected }) => {
      jest.spyOn(ProductSelectors, 'getPriceResData').mockReturnValue(priceRes);
      const data = getDepositMethodData(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Booking Selectors getDepositDescriptionSectionData', () => {
  const mockStateMap = [
    {
      state: {
        Product: {
          curInsPackageId: 3,
        },
      },
      expected: {
        title: '押金说明',
        items: [
          {
            content: 'AU$200.00（约￥925.00）',
            creditCardImgList: undefined,
            description: '取车时刷取押金预授权，还车后7-30天内退还。',
            isShowFree: false,
            isShowQuestion: false,
            positiveDesc: '可退',
            title: '押金',
          },
          {
            content: undefined,
            creditCardImgList: [
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png',
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png',
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png',
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png',
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png',
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png',
              'https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/unionpay.png',
            ],
            description: '需要芯片卡，卡号为凸字（摸起来有凹凸感）',
            isShowFree: false,
            isShowQuestion: false,
            positiveDesc: undefined,
            title: '支持\n卡种',
          },
        ],
        notices: [
          '若您还车时有涉及车损、违章等费用，建议在还车时与供应商核对后现场支付，以免发生费用争议。若还车时未涉及车损、违章等费用，押金预计在还车后60天左右解冻。',
        ],
      },
    },
  ];
  test.each(mockStateMap)(
    'getDepositDescriptionSectionData',
    ({ state, expected }) => {
      const data = getDepositDescriptionSectionData(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Booking Selectors getDriversMap', () => {
  const mockStateMap = [
    {
      state: {
        Booking: {
          driverInfo: [
            { type: 'firstName', value: 'test', error: false },
            { type: 'lastName', value: 'ctrip', error: false },
            { type: 'mobilePhone', value: '15500010002', error: false },
            { type: 'email', value: '', error: false },
            { type: 'wechat', value: '', error: false },
            { type: 'flightNumber', value: '', error: false },
            { type: 'driverLicense', value: 'CN', error: false },
            { type: 'driverLicenseName', value: '中国大陆驾照', error: false },
            { type: 'areaCode', value: '86', error: false },
          ],
          isNameReverse: false,
        },
        DriverList: {
          passenger: {
            nationalityName: '中国',
            lastName: 'ctrip',
            firstName: 'test',
            age: 29,
            nationality: 'CN',
            mobile: '15500010002',
            countryCode: '86',
            isDefault: true,
            isSelf: false,
            sortExt:
              'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:-1689066029749|selfSort:0|requestPassenger:-1',
            isRecommend: true,
            fullName: '张来明',
            birthday: '1994-02-28',
            passengerId: '269614',
            certificateList: [
              { certificateType: '1', certificateNo: '******************' },
              { certificateType: '7', certificateNo: 'W22334D2221' },
            ],
            email: '',
            isCreditQualified: false,
          },
        },
        Product: {
          selectedIdType: { typename: '中国大陆护照原件', idtype: 'CN' },
        },
      },
      isCtripIsd: false,
      expected: {
        firstName: 'ctrip',
        secondName: 'test',
        cellPhone: '15500010002',
        email: '',
        weChat: '',
        flightNo: '',
        driverLicense: 'CN',
        driverLicenseName: '中国大陆驾照',
        areaCode: '86',
        name: 'ctrip test',
        birthday: '1994-02-28',
        age: 29,
        countryCode: 'CN',
        driverCountryCode: 'CN',
        driverCountryName: '中国',
      },
    },
    {
      state: {
        Booking: {},
        DriverList: {},
        Product: {},
      },
      isCtripIsd: false,
      expected: {},
    },
    {
      state: {
        Booking: {
          driverInfo: [
            { type: 'mobilePhone', value: '15500010002', error: false },
            { type: 'flightNumber', value: '', error: false },
            { type: 'areaCode', value: '86', error: false },
          ],
        },
        DriverList: {
          passenger: {
            nationalityName: '中国',
            lastName: 'ctrip',
            firstName: 'test',
            age: 29,
            nationality: 'CN',
            mobile: '15500010002',
            countryCode: '86',
            isDefault: true,
            isSelf: false,
            sortExt:
              'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:-1689144108000|orderSort:0|selfSort:0|requestPassenger:0',
            isRecommend: true,
            fullName: '张来明',
            birthday: '1994-02-28',
            passengerId: '269614',
            certificateList: [
              { certificateType: '1', certificateNo: '******************' },
              { certificateType: '7', certificateNo: 'W22334D2221' },
            ],
            email: '',
            isCreditQualified: false,
          },
        },
        Product: {},
      },
      isCtripIsd: true,
      expected: {
        cellPhone: '15500010002',
        flightNo: '',
        areaCode: '86',
        secondName: 'test',
        firstName: 'ctrip',
        name: '张来明',
        birthday: '',
        age: 29,
        countryCode: '',
        driverCountryCode: 'CN',
        driverCountryName: '中国',
      },
    },
  ];
  test.each(mockStateMap)(
    'getDriversMap',
    ({ state, isCtripIsd, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(isCtripIsd);
      const data = getDriversMap(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Booking Selectors getBookingLogParams', () => {
  const mockStateMap = [
    {
      state: {
        Booking: {
          vendorPriceInfo: {
            ctripVehicleCode: 1234,
            reference: {
              pStoreCode: 4444,
              rStoreCode: 5555,
            },
          },
        },
      },
      isCtripIsd: false,
      expected: { vehicleCode: 1234, storeCode: 4444, rStoreCode: 5555 },
    },
    {
      state: {
        Booking: {},
      },
      isCtripIsd: false,
      expected: {},
    },
  ];
  test.each(mockStateMap)(
    'getBookingLogParams',
    ({ state, isCtripIsd, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(isCtripIsd);
      const data = getBookingLogParams(state);
      expect(data).toEqual(expected);
    },
  );
});
