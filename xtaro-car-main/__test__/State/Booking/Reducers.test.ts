import reducer, {
  initalState as initialState,
} from '../../../src/pages/xcar/State/Booking/Reducers';
import * as TYPES from '../../../src/pages/xcar/State/Booking/Types';
import { Utils } from '../../../src/pages/xcar/Util/Index';

describe('测试直接赋值的reducer', () => {
  const map = [
    {
      type: TYPES.CREATEFAIL,
      expected: {
        createOrderFailModalVisible: true,
      },
    },
    {
      type: TYPES.NAMEREVERSE,
      expected: {
        isNameReverse: true,
      },
    },
    {
      type: TYPES.CHANGE_COUPON,
      expected: {
        selectedCouponsCodes: true,
      },
    },
    {
      type: TYPES.ERRORINFO,
      expected: {
        baseResponse: true,
      },
    },
    {
      type: TYPES.SET_PRELICENDING,
      expected: {
        preLicensData: true,
      },
    },
    {
      type: TYPES.SET_SNAPSHOT_FINISH,
      expected: {
        isSnapShotFinish: true,
      },
    },
    {
      type: TYPES.SET_PRICECHANGE_POP_VISIBLE,
      expected: {
        priceChangePopVisible: true,
      },
    },
    {
      type: TYPES.SET_INSREMIND_POP_VISIBLE,
      expected: {
        insRemindPopVisible: true,
      },
    },
    {
      type: TYPES.SET_CREATEINS_LOADING_POP_VISIBLE,
      expected: {
        createInsLoadingPopVisible: true,
      },
    },
    {
      type: TYPES.SET_CREATEINS_FAIL_POP_VISIBLE,
      expected: {
        createInsFailPopVisible: true,
      },
    },
    {
      type: TYPES.SET_PASSENGER_ERROR,
      expected: {
        passengerError: true,
      },
    },
    {
      type: TYPES.SET_EHI_FREEDEPOSIT_MODAL_VISIBLE,
      expected: {
        ehiFreeDepositModalVisible: true,
      },
    },
    {
      type: TYPES.SET_LOG_INFO,
      expected: {
        logInfo: true,
      },
    },
    {
      type: TYPES.SET_CHECK_FLIGHT_NO_LOADING,
      expected: {
        checkFlightNoLoading: true,
      },
    },
  ];
  test.each(map)('%p', ({ type, expected }) => {
    const data = reducer(initialState, {
      type,
      data: true,
    });
    expect(data).toEqual({
      ...initialState,
      ...expected,
    });
  });
});
describe('测试 reducer 状态是否正常', () => {
  test('测试 initial state', () => {
    const data = reducer(undefined, {
      type: TYPES.CLEAR,
    });
    expect(data).toEqual(initialState);
  });

  test('测试 default action', () => {
    const data = reducer(initialState, {
      type: 'default',
    });
    expect(data).toEqual(initialState);
  });

  test('测试 CHANGE_FORM', () => {
    const driverInfoOSD = [
      {
        error: false,
        type: 'firstName',
        value: '',
      },
      {
        error: false,
        type: 'lastName',
        value: '',
      },
      {
        error: false,
        type: 'mobilePhone',
        value: '',
      },
      {
        error: false,
        type: 'email',
        value: '',
      },
      {
        error: false,
        type: 'wechat',
        value: '',
      },
      {
        error: false,
        type: 'flightNumber',
        value: '',
      },
      {
        error: false,
        type: 'driverLicense',
        value: '',
      },
      {
        error: false,
        type: 'driverLicenseName',
        value: '',
      },
      {
        error: false,
        type: 'areaCode',
        value: '',
      },
    ];
    const driverInfoISD = [
      {
        type: 'mobilePhone',
        value: '',
        error: false,
      },
      {
        type: 'flightNumber',
        value: '',
        error: false,
      },
      {
        type: 'areaCode',
        value: '',
        error: false,
      },
    ];
    // ISD传参
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);

    const expectedISD = driverInfoISD.map(v => {
      if (v.type === 'areaCode') {
        return {
          ...v,
          value: '1',
          error: true,
        };
      }
      return v;
    });
    const dataISD = reducer(initialState, {
      type: TYPES.CHANGE_FORM,
      data: [
        {
          type: 'areaCode',
          error: true,
          value: '1',
        },
      ],
    });
    expect(dataISD).toEqual({ ...initialState, driverInfo: expectedISD });
    // ISD不传参
    expect(
      reducer(initialState, {
        type: TYPES.CHANGE_FORM,
      }),
    ).toEqual({ ...initialState });
    // OSD传参
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
    const expectedOSD = driverInfoOSD.map(v => {
      if (v.type === 'areaCode') {
        return {
          ...v,
          value: '1',
          error: true,
        };
      }
      return v;
    });
    const dataOSD = reducer(initialState, {
      type: TYPES.CHANGE_FORM,
      data: [
        {
          type: 'areaCode',
          error: true,
          value: '1',
        },
      ],
    });
    expect(dataOSD).toEqual({ ...initialState, driverInfo: expectedOSD });
    // Trip传参
    // TODO Jenny Trip代码移除
  });

  test('测试 LOADING', () => {
    expect(
      reducer(initialState, {
        type: TYPES.LOADING,
        data: true,
      }),
    ).toEqual({
      ...initialState,
      isMaskLoading: true,
    });
    const data = reducer(initialState, {
      type: TYPES.LOADING,
      option: { clearOrder: true },
      data: true,
    });
    expect(data).toEqual({
      ...initialState,
      uniqueOrderId: 0,
      flightErrorCode: '',
      orderId: 0,
      orderData: {},
      isMaskLoading: true,
      createOrderFailModalVisible: false,
      uniqueOrderModalVisible: false,
      flightErrorModalVisible: false,
      ehiFreeDepositModalVisible: false,
      preLicensData: null,
    });
  });

  test('测试 UNIQUEORDER', () => {
    const data = reducer(initialState, {
      type: TYPES.UNIQUEORDER,
      data: true,
    });
    expect(data).toEqual({
      ...initialState,
      uniqueOrderId: true,
      uniqueOrderModalVisible: true,
    });
  });

  test('测试 CREATEORDERSUCCESS', () => {
    const data = reducer(initialState, {
      type: TYPES.CREATEORDERSUCCESS,
      data: {
        orderId: '123',
      },
    });
    expect(data).toEqual({
      ...initialState,
      orderId: '123',
      orderData: {
        orderId: '123',
      },
    });
  });

  test('测试 FLIGHTNO', () => {
    expect(
      reducer(initialState, {
        type: TYPES.FLIGHTNO,
        data: 'S123',
      }),
    ).toEqual({
      ...initialState,
      flightErrorCode: 'S123',
      flightErrorModalVisible: false,
      flightErrorTimes: 1,
    });
    expect(
      reducer(
        { ...initialState, flightErrorTimes: 1 },
        {
          type: TYPES.FLIGHTNO,
          data: 'S123',
        },
      ),
    ).toEqual({
      ...initialState,
      flightErrorCode: 'S123',
      flightErrorModalVisible: true,
      flightErrorTimes: 2,
    });
  });

  test('测试 CHANGE_MODALE', () => {
    expect(
      reducer(initialState, {
        type: TYPES.CHANGE_MODALE,
        data: {
          createOrderFailModalVisible: true,
        },
      }),
    ).toEqual({
      ...initialState,
      createOrderFailModalVisible: true,
    });
  });

  test('测试 SET_EASYLIFE_POP_VISIBLE', () => {
    expect(
      reducer(initialState, {
        type: TYPES.SET_EASYLIFE_POP_VISIBLE,
        visible: true,
      }),
    ).toEqual({
      ...initialState,
      isEasyLifeModalVisible: true,
    });
  });

  test('测试 SET_REBOOK_PENALTY', () => {
    expect(
      reducer(initialState, {
        type: TYPES.SET_REBOOK_PENALTY,
      }),
    ).toEqual({
      ...initialState,
      rebookPenalty: undefined,
    });
    expect(
      reducer(initialState, {
        type: TYPES.SET_REBOOK_PENALTY,
        data: {
          rebookPenalty: 'rebookPenalty',
        },
      }),
    ).toEqual({
      ...initialState,
      rebookPenalty: 'rebookPenalty',
    });
  });

  test('测试 RESET_CANCELFEE_REBOOK', () => {
    const defaultCancelFee = {
      amount: 0,
      currencyCode: 0,
      canRefund: false,
      auditTime: '',
      orderAmount: 0,
    };
    expect(
      reducer(initialState, {
        type: TYPES.RESET_CANCELFEE_REBOOK,
      }),
    ).toEqual({
      ...initialState,
      resCancelFeeRebook: defaultCancelFee,
    });
  });

  test('测试 FETCH_CANCELFEE_REBOOK_CALLBACK', () => {
    const defaultCancelFee = {
      amount: 0,
      currencyCode: 0,
      canRefund: false,
      auditTime: '',
      orderAmount: 0,
    };
    expect(
      reducer(initialState, {
        type: TYPES.FETCH_CANCELFEE_REBOOK_CALLBACK,
        data: { resCancelFee: defaultCancelFee },
      }),
    ).toEqual({
      ...initialState,
      resCancelFeeRebook: defaultCancelFee,
      rebookPenalty: '',
    });
  });

  test('测试 SET_CHECK_FLIGHT_NO_LOADING', () => {
    expect(
      reducer(initialState, {
        type: TYPES.SET_CHECK_FLIGHT_NO_LOADING,
        data: true,
      }),
    ).toEqual({
      ...initialState,
      checkFlightNoLoading: true,
    });

    expect(
      reducer(initialState, {
        type: TYPES.SET_CHECK_FLIGHT_NO_LOADING,
        data: false,
      }),
    ).toEqual({
      ...initialState,
      checkFlightNoLoading: false,
    });
  });

  test('测试 SET_FLIGHT_ERROR_TIP', () => {
    expect(
      reducer(initialState, {
        type: TYPES.SET_FLIGHT_ERROR_TIP,
        data: '',
      }),
    ).toEqual({
      ...initialState,
      flightErrorTip: '',
    });

    expect(
      reducer(initialState, {
        type: TYPES.SET_FLIGHT_ERROR_TIP,
        data: '航班不存在',
      }),
    ).toEqual({
      ...initialState,
      flightErrorTip: '航班不存在',
    });
  });

  test('测试 SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE', () => {
    expect(
      reducer(initialState, {
        type: TYPES.SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
        data: true,
      }),
    ).toEqual({
      ...initialState,
      depositRateDescriptionModalVisible: true,
    });

    expect(
      reducer(initialState, {
        type: TYPES.SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
        data: false,
      }),
    ).toEqual({
      ...initialState,
      depositRateDescriptionModalVisible: false,
    });
  });
});
