import { StoreWayInfoType } from '../../../src/pages/xcar/ComponentBusiness/Common/src/Enums';
import {
  verifyCreateOrderPointInfo,
  verifyDriverInfo,
  getDropOffShowAddress,
  getPickUpShowAddress,
  getStoreShowWay,
} from '../../../src/pages/xcar/State/Booking/Method';
import { WayInfoType } from '../../../src/pages/xcar/State/Product/Enums';
describe('Test getDropOffShowAddress', () => {
  const mock_1 = {
    cityId: 43,
    date: '2022-11-25 10:00:00',
    locationCode: '',
    locationName: '凤凰国际机场T1航站楼',
    locationType: '1',
    storeCode: '32038',
    address: '凤凰国际机场T1航站楼',
    poi: { latitude: 18.30747, longitude: 109.41201, radius: 0 },
    pickupOnDoor: 0,
    dropOffOnDoor: 0,
  };
  test('无 referenceTemp ｜ 无入参', () => {
    expect(getDropOffShowAddress(mock_1, 'dropOffLocationName', null)).toEqual(
      '',
    );
    expect(getDropOffShowAddress(null, 'dropOffLocationName', null)).toEqual(
      '',
    );
  });
  test('有 referenceTemp', () => {
    const res1 = getDropOffShowAddress(
      { mock_1, wayInfo: StoreWayInfoType.PickupInStation, address: 'address' },
      'dropOffLocationName',
      {
        returnWayInfo: WayInfoType.SELF_PICK,
      },
    );
    expect(res1).toEqual('address');
    const res2 = getDropOffShowAddress(
      { mock_1, address: 'address' },
      'dropOffLocationName',
      {
        returnWayInfo: WayInfoType.SELF_PICK,
      },
    );
    expect(res2).toEqual('address');
    const res3 = getDropOffShowAddress(
      { mock_1, address: 'address' },
      'dropOffLocationName',
      {
        returnWayInfo: WayInfoType.SHUTTLE_POINT,
      },
    );
    expect(res3).toEqual('还车点：address');
    const res4 = getDropOffShowAddress(
      { mock_1, address: 'address' },
      'dropOffLocationName',
      {
        returnWayInfo: 'WayInfoType.SHUTTLE_POINT',
      },
    );
    expect(res4).toEqual('dropOffLocationName');
  });
});
describe('Test getStoreShowWay', () => {
  expect(
    getStoreShowWay({
      storeGuildSplit: 'A，B',
      pickUpOnDoor: false,
      returnOnDoor: false,
    }),
  ).toEqual('A');
  expect(
    getStoreShowWay({
      storeGuildSplit: 'B，A',
      pickUpOnDoor: false,
      returnOnDoor: true,
    }),
  ).toEqual('B');
  expect(getStoreShowWay({})).toEqual('');
});
describe('Test getPickUpShowAddress', () => {
  const mock_1 = {
    cityId: 43,
    date: '2022-11-25 10:00:00',
    locationCode: '',
    locationName: '凤凰国际机场T1航站楼',
    locationType: '1',
    storeCode: '32038',
    address: '凤凰国际机场T1航站楼',
    poi: { latitude: 18.30747, longitude: 109.41201, radius: 0 },
    pickupOnDoor: 0,
    dropOffOnDoor: 0,
  };
  test('无 referenceTemp ｜ 无入参', () => {
    expect(getPickUpShowAddress(mock_1, 'pickUpLocationName', null)).toEqual(
      '',
    );
    expect(getPickUpShowAddress(null, 'pickUpLocationName', null)).toEqual('');
  });
  test('有 referenceTemp', () => {
    const res1 = getPickUpShowAddress(
      { mock_1, wayInfo: StoreWayInfoType.PickupInStation, address: 'address' },
      'pickUpLocationName',
      {
        pickWayInfo: WayInfoType.SELF_PICK,
      },
    );
    expect(res1).toEqual('address');
    const res2 = getPickUpShowAddress(
      { mock_1, address: 'address' },
      'pickUpLocationName',
      {
        pickWayInfo: WayInfoType.SELF_PICK,
      },
    );
    expect(res2).toEqual('address');
    const res3 = getPickUpShowAddress(
      { mock_1, address: 'address' },
      'pickUpLocationName',
      {
        pickWayInfo: WayInfoType.FREE_SHUTTLE,
      },
    );
    expect(res3).toEqual('汇合点：pickUpLocationName');
    const res4 = getPickUpShowAddress(
      {
        mock_1,
        storeType: 4,
        shuttlePointAddress: '汇合点',
        address: 'address',
      },
      'pickUpLocationName',
      {
        pickWayInfo: 'WayInfoType.SHUTTLE_POINT',
      },
    );
    expect(res4).toEqual('pickUpLocationName');
    const res5 = getPickUpShowAddress(mock_1, 'pickUpLocationName', {
      pickWayInfo: 'WayInfoType.SHUTTLE_POINT',
    });
    expect(res5).toEqual('pickUpLocationName');
  });
});
describe('Booking Method Test', () => {
  describe('Booking Method verifyCreateOrderPointInfo', () => {
    const createOrderPointInfoMock_1 = {
      cityId: 43,
      date: '2022-11-25 10:00:00',
      locationCode: '',
      locationName: '凤凰国际机场T1航站楼',
      locationType: '1',
      storeCode: '32038',
      address: '凤凰国际机场T1航站楼',
      poi: { latitude: 18.30747, longitude: 109.41201, radius: 0 },
      pickupOnDoor: 0,
      dropOffOnDoor: 0,
    };

    const createOrderPointInfoMock_2 = {
      cityId: 43,
      date: '2022-11-25 10:00:00',
      locationCode: '',
      locationName: '凤凰国际机场T1航站楼',
      locationType: '',
      storeCode: '32038',
      address: '凤凰国际机场T1航站楼',
      poi: { latitude: 18.30747, longitude: 109.41201, radius: 0 },
      pickupOnDoor: 0,
      dropOffOnDoor: 0,
    };

    test('create order pointInfo validate true', () => {
      const result = verifyCreateOrderPointInfo(createOrderPointInfoMock_1);
      expect(result).toEqual(true);
    });

    test('create order pointInfo validate false', () => {
      const result = verifyCreateOrderPointInfo(createOrderPointInfoMock_2);
      expect(result).toEqual(true);
    });
  });

  describe('Booking Method verifyDriverInfo', () => {
    const driverInfoMock_1 = {
      name: 'ZHANG SAN',
      age: 32,
      email: '<EMAIL>',
      areaCode: '86',
      firstName: 'ZHANG',
      secondName: 'SAN',
      cellPhone: '15888888888',
      idnumber: '12121212121212121',
      idtype: '1',
    };

    const driverInfoMock_2 = {
      name: 'ZHANG SAN',
      age: 32,
      email: '',
      areaCode: '86',
      firstName: 'ZHANG',
      secondName: 'SAN',
      cellPhone: '15888888888',
      idnumber: '12121212121212121',
      idtype: '1',
    };

    test('OSD driverInfo validate true', () => {
      const result = verifyDriverInfo(driverInfoMock_1);
      expect(result).toEqual(true);
    });

    test('OSD driverInfo validate false', () => {
      const result = verifyDriverInfo(driverInfoMock_2);
      expect(result).toEqual(false);
    });
  });
});
