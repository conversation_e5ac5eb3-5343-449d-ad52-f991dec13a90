import { run<PERSON><PERSON> } from 'redux-saga';
import { modifyDriver, getWordPinyin } from '../../../src/pages/xcar/State/DriverEdit/Logic';
import { takeEveryGeneratorFunction } from '../../testHelpers';
import { <PERSON>Fetch } from '../../../src/pages/xcar/Util/Index';
import {
  MODIFY_DRIVER,
  MODIFY_DRIVER_CALLBACK,
  GET_WORDPINGYIN_CALLBACK,
} from '../../../src/pages/xcar/State/DriverEdit/Types';
import {
  SELECT_DRIVER,
  FETCH_DRIVERLIST,
  UPDATE_DRIVERLIST_FOR_MODIFY,
} from '../../../src/pages/xcar/State/DriverList/Types';

describe('DriverEdit Logic modifyDriver', () => {
  const driver = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "340826199012231110"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false,
  };
  const resultDriver = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试后",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "340826199012231110"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false,
  };
  const yongAge = 20;
  const oldAge = 60;
  const confirmedCodes = ['aaaa01'];
  const fromurl = 'www.baidu.com';
  test('测试正常调用', async () => {
    const dispatched : any = [];
    const res = {
      baseResponse: { isSuccess: true },
      passengerInfo: resultDriver,
    };
    let callBackFlag = null;
    const callback = (data) => {
      callBackFlag = data;
    };
    const actionMock = {
      type: MODIFY_DRIVER,
      data: {
        driver,
        yongAge,
        oldAge,
        confirmedCodes,
        fromurl,
      },
      callback,
    };
    const logicFunc = takeEveryGeneratorFunction(modifyDriver);
    jest.spyOn(CarFetch, 'modifyDriver').mockReturnValue(Promise.resolve(res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: MODIFY_DRIVER_CALLBACK,
      data: {
        isError: false,
        data: actionMock.data,
        res,
        isLoading: false,
      },
    });
    expect(dispatched[1]).toEqual({
      type: SELECT_DRIVER,
      data: {
        ...resultDriver,
        queryPriceInfo: true,
      },
    });
    expect(dispatched[2]).toEqual({
      type: FETCH_DRIVERLIST,
      data: {
        fromurl,
        isForceUpdate: true,
      },
    });
    expect(dispatched[3]).toEqual({
      type: UPDATE_DRIVERLIST_FOR_MODIFY,
      data: {
        passenger: resultDriver,
        isdelete: false,
        certificateType: driver.certificateList[0].certificateType,
      },
    });
    expect(callBackFlag).toEqual(res);
  });

  test('测试数据异常调用', async () => {
    const dispatched : any = [];
    const res = {};
    let callBackFlag = null;
    const mockDriver = {
      ...driver,
      certificateList: undefined,
    };
    const actionMock = {
      type: MODIFY_DRIVER,
      data: {
        driver: mockDriver,
        yongAge,
        oldAge,
        confirmedCodes,
        fromurl,
      },
    };
    const logicFunc = takeEveryGeneratorFunction(modifyDriver);
    jest.spyOn(CarFetch, 'modifyDriver').mockReturnValue(Promise.resolve(res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: MODIFY_DRIVER_CALLBACK,
      data: {
        isError: false,
        data: actionMock.data,
        res,
        isLoading: false,
      },
    });
    expect(dispatched[1]).toEqual({
      type: SELECT_DRIVER,
      data: {
        queryPriceInfo: true,
      },
    });
    expect(dispatched[2]).toEqual({
      type: FETCH_DRIVERLIST,
      data: {
        fromurl,
        isForceUpdate: true,
      },
    });
    expect(dispatched[3]).toEqual({
      type: UPDATE_DRIVERLIST_FOR_MODIFY,
      data: {
        passenger: {},
        isdelete: false,
        certificateType: undefined,
      },
    });
    expect(callBackFlag).toEqual(null);
  });

  test('接口异常调用', async () => {
    const dispatched : any = [];
    let callBackFlag = null;
    const callback = (data) => {
      callBackFlag = data;
    };
    const actionMock = {
      type: MODIFY_DRIVER,
      data: {
        driver,
        yongAge,
        oldAge,
        confirmedCodes,
        fromurl,
      },
      callback,
    };
    const exceptionError = new Error('modifyDriver exception');
    const logicFunc = takeEveryGeneratorFunction(modifyDriver);
    jest.spyOn(CarFetch, 'modifyDriver').mockReturnValue(new Promise(() => {
      throw exceptionError;
    }));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: MODIFY_DRIVER_CALLBACK,
      data: {
        isError: true,
        data: actionMock.data,
        res: exceptionError,
        isLoading: false,
      },
    });
    expect(callBackFlag).toEqual(exceptionError);
  });
});

describe('DriverEdit Logic getWordPinyin', () => {
  test('测试正常调用', async () => {
    const dispatched : any = [];
    const res = {
      "result": {
          "resultCode": 0
      },
      "words": [
          {
              "pinyins": [
                  "CE"
              ],
              "word": "测"
          },
          {
              "pinyins": [
                  "SHI"
              ],
              "word": "试"
          }
      ],
      "surnames": [ ],
      "ResponseStatus": {
          "Extension": [
              {
                  "Value": "3052102117517311628",
                  "Id": "CLOGGING_TRACE_ID"
              },
              {
                  "Value": "921822-0a056b21-460851-24426",
                  "Id": "RootMessageId"
              }
          ],
          "Ack": "Success",
          "Errors": [ ],
          "Timestamp": "/Date(1659064041499+0800)/"
      },
      "appResponseMap": {
          "isFromCache": false,
          "isCacheValid": true,
          "networkCost": 252,
          "environmentCost": 0,
          "cacheFetchCost": 0,
          "fetchCost": 252,
          "setCacheCost": 0,
          "cacheFrom": "",
          "beforeFetch": 1659064041266,
          "afterFetch": 1659064041518
      }
    };
    let callBackFlag = null;
    const callback = (data) => {
      callBackFlag = data;
    };
    const actionMock = {
      type: MODIFY_DRIVER,
      data: "测试",
      callback,
    };
    const logicFunc = takeEveryGeneratorFunction(getWordPinyin);
    jest.spyOn(CarFetch, 'getWordPinyin').mockReturnValue(Promise.resolve(res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: GET_WORDPINGYIN_CALLBACK,
      data: {
        isError: false,
        data: actionMock.data,
        res,
        isLoading: false,
      },
    });
    expect(callBackFlag).toEqual(res);
  });

  test('测试数据异常调用', async () => {
    const dispatched : any = [];
    const res = {};
    let callBackFlag = null;
    const actionMock = {
      type: MODIFY_DRIVER,
      data: "测试",
    };
    const logicFunc = takeEveryGeneratorFunction(getWordPinyin);
    jest.spyOn(CarFetch, 'getWordPinyin').mockReturnValue(Promise.resolve(res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: GET_WORDPINGYIN_CALLBACK,
      data: {
        isError: false,
        data: actionMock.data,
        res: {},
        isLoading: false,
      },
    });
    expect(callBackFlag).toEqual(null);
  });

  test('接口异常调用', async () => {
    const dispatched : any = [];
    let callBackFlag = null;
    const callback = (data) => {
      callBackFlag = data;
    };
    const actionMock = {
      type: MODIFY_DRIVER,
      data: "测试",
      callback,
    };
    const exceptionError = new Error('getWordPinyin exception');
    const logicFunc = takeEveryGeneratorFunction(getWordPinyin);
    jest.spyOn(CarFetch, 'getWordPinyin').mockReturnValue(new Promise(() => {
      throw exceptionError;
    }));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      logicFunc,
      actionMock,
    ).toPromise();
    expect(dispatched[0]).toEqual({
      type: GET_WORDPINGYIN_CALLBACK,
      data: {
        isError: true,
        data: actionMock.data,
        res: exceptionError,
        isLoading: false,
      },
    });
    expect(callBackFlag).toEqual(exceptionError);
  });
});
