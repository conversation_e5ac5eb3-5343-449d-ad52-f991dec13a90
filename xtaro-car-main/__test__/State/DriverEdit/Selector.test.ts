import { getIsLoading, getIsFail } from '../../../src/pages/xcar/State/DriverEdit/Selectors';

const mockStateMap = [
  {
    state: {
      DriverEdit: {
        isLoading: true,
        isFail: true,
      },
    },
    expected: true,
  },
  {
    state: {
      DriverEdit: {
        isLoading: false,
        isFail: false,
      },
    },
    expected: false,
  },
];


describe('DeiverEdit Selectors getIsLoading', () => {
  test.each(mockStateMap)(
    'getIsLoading check',
    ({ state, expected }) => {
      const data = getIsLoading(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DeiverEdit Selectors getIsFail', () => {
  test.each(mockStateMap)(
    'getIsFail check',
    ({ state, expected }) => {
      const data = getIsFail(state);
      expect(data).toEqual(expected);
    },
  );
});

