import DriverEditReducer, { getInitalState } from '../../../src/pages/xcar/State/DriverEdit/Reducer';
import {
    MODIFY_DRIVER,
    MODIFY_DRIVER_CALLBACK,
    UPDATE_ISFORCEUPDATE,
    GET_WORDPINGYIN,
    GET_WORDPINGYIN_CALLBACK,
  } from '../../../src/pages/xcar/State/DriverEdit/Types';

describe('DriverEdit Reducer Test', () => {
  const initState = getInitalState();

  test('DriverEdit Reducer Init', () => {
    expect(DriverEditReducer(undefined, {})).toEqual(initState);
  });

  describe('DriverEdit Reducer MODIFY_DRIVER', () => {
    test('MODIFY_DRIVER', () => {
        expect(DriverEditReducer(initState, {
            type: MODIFY_DRIVER,
        })).toEqual({
            ...initState,
            isLoading: true,
        });
    });
  });

  describe('DriverEdit Reducer MODIFY_DRIVER_CALLBACK', () => {
    test('MODIFY_DRIVER_CALLBACK', () => {
        expect(DriverEditReducer(initState, {
            type: MODIFY_DRIVER_CALLBACK,
        })).toEqual({
            ...initState,
            isForceUpdate: true,
            isLoading: false,
        });
    });
  });

  describe('DriverEdit Reducer UPDATE_ISFORCEUPDATE', () => {
    test('UPDATE_ISFORCEUPDATE', () => {
        expect(DriverEditReducer(initState, {
            type: UPDATE_ISFORCEUPDATE,
        })).toEqual({
            ...initState,
            isForceUpdate: false,
        });
    });
  });

  describe('DriverEdit Reducer GET_WORDPINGYIN', () => {
    test('GET_WORDPINGYIN', () => {
        expect(DriverEditReducer(initState, {
            type: GET_WORDPINGYIN,
        })).toEqual({
            ...initState,
            isLoading: true,
        });
    });
  });

  describe('DriverEdit Reducer GET_WORDPINGYIN_CALLBACK', () => {
    test('GET_WORDPINGYIN_CALLBACK', () => {
        expect(DriverEditReducer(initState, {
            type: GET_WORDPINGYIN_CALLBACK,
        })).toEqual({
            ...initState,
            isLoading: false,
        });
    });
  });
})