import { modifyDriver, getWordPinyin } from '../../../src/pages/xcar/State/DriverEdit/Actions';
import {
  MODIFY_DRIVER,
  GET_WORDPINGYIN,
} from '../../../src/pages/xcar/State/DriverEdit/Types';

describe('DeiverEdit Actions modifyDriver', () => {
  const data = {};
  const callback = () => {};
  const mockStateMap = [
    {
      data,
      callback,
      expected: {
        type: MODIFY_DRIVER,
        data,
        callback,
      },
    },
  ];
  test.each(mockStateMap)(
    'modifyDriver check',
    ({ data, callback, expected }) => {
      const result = modifyDriver(data, callback);
      expect(result).toEqual(expected);
    },
  );
});

describe('DeiverEdit Actions getWordPinyin', () => {
  const data = {};
  const callback = () => {};
  const mockStateMap = [
    {
      data,
      callback,
      expected: {
        type: GET_WORDPINGYIN,
        data,
        callback,
      },
    },
  ];
  test.each(mockStateMap)(
    'getWordPinyin check',
    ({ data, callback, expected }) => {
      const result = getWordPinyin(data, callback);
      expect(result).toEqual(expected);
    },
  );
});
