import { recordSaga } from '../../testHelpers';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';
import { setCommonDateInfo, getCityTimeZone, setLatestDateLogic, apiFetchRentCenterInfo, onLocationChange } from '../../../src/pages/xcar/State/LocationAndDate/Logic';
import { GET_LOCATION_DATE_INFO, SET_LOCATION_INFO, GET_CITY_TIMEZONE, SET_LATEST_DATE_INFO, FETCH_RENT_CENTER_INFO, SET_DATE_INFO, FETCH_RENT_CENTER_INFO_CALLBACK, GET_CITY_TIMEZONE_CALLBACK } from '../../../src/pages/xcar/State/LocationAndDate/Types';
import { SET_FILTER_ITEMS } from '../../../src/pages/xcar/State/Home/Types'
import { Utils } from '../../../src/pages/xcar/Util/Index';
import dayjs from '../../../src/pages/xcar/Common/src/Dayjs/src';

const mockRentalLocation = {
  "pickUp": {
    "cid": 43,
    "cname": "三亚",
    "country": "中国",
    "realcountry": "中国",
    "isDomestic": true,
    "area": {
      "id": "",
      "name": "凤凰国际机场T1航站楼",
      "lat": 18.30747,
      "lng": 109.41201,
      "type": "1"
    },
    "isFromPosition": false
  },
  "dropOff": {
    "cid": 43,
    "cname": "三亚",
    "country": "中国",
    "realcountry": "中国",
    "isDomestic": true,
    "area": {
      "id": "",
      "name": "凤凰国际机场T1航站楼",
      "lat": 18.30747,
      "lng": 109.41201,
      "type": "1"
    },
    "isFromPosition": false
  },
  "isShowDropOff": false
};
const mockFilterItems = [{
  name: '',
  code: 'PickReturn_PickupOnDoor',
  isSelected: false,
}];

jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
jest.mock('../../../src/pages/xcar/State/LocationAndDate/Mappers', () => {
  return {
    getInitLocationInfo: jest.fn(() => mockRentalLocation),
    getInitPTime: jest.fn(),
    getInitRTime: jest.fn(),
  }
});
jest.mock('../../../src/pages/xcar/State/LocationAndDate/Selectors', () => {
  return {
    getRentalLocation: jest.fn(() => mockRentalLocation),
    getRentalLocationAndDate: jest.fn()
  }
});
jest.mock('../../../src/pages/xcar/State/Home/Selectors', () => {
  return {
    getFilterItems: jest.fn(() => mockFilterItems),
  }
});
describe('LocationAndDate Logic setCommonDateInfo', () => {
  const testFn = async () => {
    const dispatched = await recordSaga(setCommonDateInfo, {
      action: {
        type: GET_LOCATION_DATE_INFO,
        data: {}
      },
      state: {}
    })
    return dispatched;
  }

  test('setCommonDateInfo 测试正常调用', async () => {
    const dispatched = await testFn()
    expect(dispatched[0]).toEqual({
      type: SET_LOCATION_INFO,
      data: mockRentalLocation
    })
  });
});

jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);

describe('LocationAndDate Logic getCityTimeZone', () => {
    const testFn = async () => {
        const now = dayjs();
        const api =jest.spyOn(CarFetch,'queryTime').mockReturnValueOnce(Promise.resolve({
            targetDate: dayjs().add(-7, 'hours')
        }))
        const dispatched = await recordSaga(getCityTimeZone, {
            action: {
                type: SET_LOCATION_INFO,
                data:{}
            },
            state:{}
        })
        return dispatched;
    }
    
    test('getCityTimeZone 测试正常调用', async () => {
        const dispatched = await testFn()
    });

    const testFnOsd = async () => {
        const api =jest.spyOn(CarFetch,'queryTime').mockReturnValueOnce(Promise.resolve({
            targetDate: dayjs().add(-7, 'hours')
        }))
        jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
        const dispatched = await recordSaga(getCityTimeZone, {
            action: {
                type: SET_LOCATION_INFO,
                data:{}
            },
            state:{}
        })
        return dispatched;
    }
    
    test('getCityTimeZone 测试正常调用', async () => {
        const dispatched = await testFnOsd()
        expect(dispatched[0]).toEqual({
            type: GET_CITY_TIMEZONE_CALLBACK,
            data: {
              cityId: 43,
              timeZone: 1,
            },
        })
    });
});

describe('LocationAndDate Logic setLatestDateLogic', () => {
    const testFn = async () => {
        const api =jest.spyOn(CarFetch,'queryTime').mockReturnValueOnce(Promise.resolve({
            targetDate: dayjs().add(-7, 'hours')
        }))
        const dispatched = await recordSaga(getCityTimeZone, {
            action: {
                type: SET_LOCATION_INFO,
                data:{}
            },
            state:{}
        })
        return dispatched;
    }
    
    test('getCityTimeZone 测试正常调用', async () => {
        const dispatched = await testFn()
    });
});

describe('LocationAndDate Logic setLatestDateLogic', () => {
  const testFn = async () => {
    const dispatched = await recordSaga(setLatestDateLogic, {
      action: {
        type: SET_LATEST_DATE_INFO,
        data: {
          time: dayjs('2021/08/09 00:00:00')
        }
      },
      state: {}
    })
    return dispatched;
  }

  test('setLatestDateLogic 测试正常调用', async () => {
    const dispatched = await testFn();
    expect(dispatched[0]).toEqual({
      type: SET_DATE_INFO,
      data: {
        pickup: undefined,
        dropoff: undefined
      }
    })
  });
});
jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => false);

const mockHomeController = {
  "baseResponse": {
    "isSuccess": true
  },
  "rentcenterId": 1,
  "filter": [
    { "type": 1, "code": "PickReturn_StationPR", "text": "只看站内取还" }
  ],
  "pickupOnDoorCode": "StoreService_PickupOnDoor",
  "pHub": 1,
  "rHub": 1
};

describe('LocationAndDate Logic apiFetchRentCenterInfo', () => {
  const testFn = async () => {
    const api = jest.spyOn(CarFetch, 'homeController').mockReturnValueOnce(Promise.resolve(mockHomeController))
    const dispatched = await recordSaga(apiFetchRentCenterInfo, {
      action: {
        type: FETCH_RENT_CENTER_INFO,
        data: {}
      },
      state: {}
    })
    return { api, dispatched };
  }

  test('apiFetchRentCenterInfo 测试正常调用', async () => {
    const { api, dispatched } = await testFn();
    expect(api).toBeCalled();
    expect(dispatched[0]).toEqual({
      type: FETCH_RENT_CENTER_INFO_CALLBACK,
      data: {
        isPickupStation: '1',
        isDropOffStation: '1',
      }
    });
    expect(dispatched[1]).toEqual({
      type: SET_FILTER_ITEMS,
      data: {
        filterItems: [
          {
            name: "只看站内取还",
            code: "PickReturn_StationPR",
            isSelected: false,
            isPickupOnDoor: false,
          },
        ]
      }
    })
  });
});

describe('LocationAndDate Logic onLocationChange', () => {
  test('apiFetchRentCenterInfo 测试正常调用', async () => {
    const dispatched = await recordSaga(onLocationChange, {
      action: {
        type: SET_LOCATION_INFO,
        data: {}
      },
      state: {}
    })
    expect(dispatched).toEqual([{ "data": { "isChangeLocation": true, "isInit": true }, "type": "Home/QUERY_TRIP_RECOMMEND" }]);
  });
});

describe('LocationAndDate Logic onLocationChange', () => {
  test('apiFetchRentCenterInfo 测试正常调用', async () => {
    const dispatched = await recordSaga(onLocationChange, {
      action: {
        type: SET_LOCATION_INFO,
        data: {}
      },
      state: {}
    })
    expect(dispatched).toEqual([{ "data": { "isChangeLocation": true, "isInit": true }, "type": "Home/QUERY_TRIP_RECOMMEND" }]);
  });
});
