import { recordSaga } from '../../testHelpers';
import { CarFetch} from '../../../src/pages/xcar/Util/Index';
import { checkAirPortTransfer, showAirPortTransferTip  } from '../../../src/pages/xcar/State/LocationAndDate/LogicSaga';
import { SHOW_AIRPORT_TRANSFER_TIP, SET_AIRPORT_TRANSFER_TIP } from '../../../src/pages/xcar/State/LocationAndDate/Types';
import dayjs from '../../../src/pages/xcar/Common/src/Dayjs/src';
import { xShowToast } from '@ctrip/xtaro';
import { HomeTexts as Texts } from '../../../src/pages/xcar/Constants/TextIndex';
const mockAirPortTransferTip = {
    deadlineTime: '2022-09-01 00 00 00',
    poiName:'上海'
};

const mockConfig={
    "nationalDayWarning":{
        "end":"2021/2/17 23:59",
        "start":"2021/2/11 00:00",
        "title":"春节为租车高峰期，部分门店需要至少3天起租"
    },
    "preFetchListChannelIds":[
        "17671"
    ],
    "selfDrivingEntryConfig":[

    ],
    "airportTransfer":{
        "deadlineTime":"2022/3/24 00:00:00",
        "pois":[
            "湛江机场",
            "湛江机场航站楼",
            "湛江机场-P1停车场",
            "湛江机场-国内到达口",
            "湛江机场-网约车停车场",
            "湛江机场-国际到达",
            "湛江机场-P2停车场",
            "湛江机场-国内出发口"
        ]
    },
    "isGetBookingNotice":true,
    "modalsSort":[
        "carForcePush",
        "latitude",
        "memberPrivilege"
    ],
    "addInstructData":{
        "title":"增加多名驾驶员",
        "content":"默认仅支持一名驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每名驾驶员都需要出示与主驾驶员相同的取车证件。如果未经登记的人驾驶车辆，保险可能失效，请在取车时与店员确认。"
    },
    "hideZhima":true,
    "goodsShelfCityList":[
        "12",
        "34",
        "278",
        "17",
        "380",
        "43"
    ],
    "moreServiceTip":"80%的人选择升级租车保障，保障出行安全",
    "ensureTipJumpUrl":"https://m.ctrip.com/webapp/cars/marketing/easycar?isHideNavBar=YES&from_native_page=1",
    "vendorListTangramEntrance":false,
    "flightCalabiSwitch":1,
    "flightDiscountContent":"机票租车同时下单专享",
    "flightDiscout":85,
    "slogan":[
        "海量车型",
        "押金双免",
        "有车保障"
    ],
    "flightDiscountTitle":"租车立减活动",
    "popCouponSceneId":540,
    "flightDiscountText":"折起",
    "ensureTipImageUrl":"https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/ensureTipBgNew.png",
    "insuranceFlag":true
}
const mockRentalLocation = {
    "pickUp":{
        "cid":43,
        "cname":"三亚",
        "country":"中国",
        "realcountry":"中国",
        "isDomestic":true,
        "area":{
            "id":"",
            "name":"凤凰国际机场T1航站楼",
            "lat":18.30747,
            "lng":109.41201,
            "type":"1"
        },
        "isFromPosition":false
    },
    "dropOff":{
        "cid":43,
        "cname":"三亚",
        "country":"中国",
        "realcountry":"中国",
        "isDomestic":true,
        "area":{
            "id":"",
            "name":"凤凰国际机场T1航站楼",
            "lat":18.30747,
            "lng":109.41201,
            "type":"1"
        },
        "isFromPosition":false
    },
    "isShowDropOff":false
};

jest.mock('../../../src/pages/xcar/State/LocationAndDate/Selectors', () => {
  return {
    ...jest.requireActual('../../../src/pages/xcar/State/LocationAndDate/Selectors'),   
      getAirPortTransferTip: jest.fn(() => mockAirPortTransferTip),
      getRentalLocation: jest.fn(() => mockRentalLocation),
      getPickUpTime: jest.fn(),
      getDropOffTime: jest.fn()
  }
});

describe('LocationAndDate LogicSaga showAirPortTransferTip', () => {
    const testFn = async () => {
        const dispatched = await recordSaga(showAirPortTransferTip, {
            action: {
                type: SHOW_AIRPORT_TRANSFER_TIP
            },
            state:{}
        })
        return dispatched
    }
    test('showAirPortTransferTip 测试正常调用', async() => {
        const dispatched = await testFn();
        const time = dayjs(mockAirPortTransferTip.deadlineTime).format(
            Texts.monthDayFomat,
          );
        expect(xShowToast).toBeCalledWith({
            title: `${time}起，您选择的上海将停止使用，请确认后再预订`,
            duration: 3000,
        }); // API 被调用
        expect(dispatched[0]).toEqual({
            type: SET_AIRPORT_TRANSFER_TIP,
            data: { isShow: false }
        })
    })
})

describe('LocationAndDate LogicSaga checkAirPortTransfer', () => {
    const testFn = async (promise) => {
        const api = jest
      .spyOn(CarFetch, 'getQConfig').mockReturnValueOnce(promise);
        const dispatched = await recordSaga(checkAirPortTransfer, {
            action: {
                type: SHOW_AIRPORT_TRANSFER_TIP,
                data:{}
            },
            state:{}
        })
        return dispatched;
    }
    test('checkAirPortTransfer 测试正常调用', async () => {
        const dispatched = await testFn(Promise.resolve(mockConfig));
        expect(dispatched[0]).toEqual(
            {
                type: 'LocationAndDate/SET_AIRPORT_TRANSFER_TIP',
                data: { isShow: false }
              }
        )
    });
});

