import dayjs from '../../../src/pages/xcar/Common/src/Dayjs/src';
import {
  getPolicyResponse,
  getLoading,
  getFail,
  getPackagedData,
  getParams,
} from '../../../src/pages/xcar/State/Policy/Selectors';
import { getBbkStorePolicyProps } from '../../../src/pages/xcar/State/Product/BbkMapper';
import { PolicyPressType } from '../../../src/pages/xcar/State/Product/Enums';
import { initialState } from '../../../src/pages/xcar/State/LocationAndDate/Reducers';
import { Utils } from '../../../src/pages/xcar/Util/Index';
import { getPointInfoParamsV2 } from '../../../src/pages/xcar/State/LocationAndDate/Selectors';

const skuId = '12345';
const pickUpTime = '2023-01-22 19:30:00';
const dropOffTime = '2023-01-27 19:30:00';
const pointInfoParams = data => ({
  pickupPointInfo: {
    cityId: 1,
  },
  returnPointInfo: {
    cityId: 1,
  },
});

describe('Policy Selectors getPolicyResponse', () => {
  const mockStateMap = [
    {
      state: {
        Policy: {
          response: {
            baseResponse: {
              extMap: {
                iSDReserveNotice: 33,
                queryRentalMustRead: 0,
              },
            },
            resInfo: {
              mustReads: [],
            },
          },
        },
      },
      expected: {
        baseResponse: {
          extMap: {
            iSDReserveNotice: 33,
            queryRentalMustRead: 0,
          },
        },
        resInfo: {
          mustReads: [],
        },
      },
    },
  ];
  test.each(mockStateMap)('getPolicyResponse', ({ state, expected }) => {
    const data = getPolicyResponse(state);
    expect(data).toEqual(expected);
  });
});

describe('Policy Selectors getLoading', () => {
  const mockStateMap = [
    {
      state: {
        Policy: {
          isLoading: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)('getLoading', ({ state, expected }) => {
    const data = getLoading(state);
    expect(data).toEqual(expected);
  });
});

describe('Policy Selectors getFail', () => {
  const mockStateMap = [
    {
      state: {
        Policy: {
          isFail: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)('getFail', ({ state, expected }) => {
    const data = getFail(state);
    expect(data).toEqual(expected);
  });
});

describe('Policy Selectors getPackagedData', () => {
  const response = {
    baseResponse: {
      extMap: {
        iSDReserveNotice: 33,
        queryRentalMustRead: 0,
      },
    },
    resInfo: {
      mustReads: [],
      rentalMustReadPicture: [],
      rentalMustReadTable: [],
    },
  };
  const policyType: any = [PolicyPressType.All];
  const { mustReads, rentalMustReadPicture, rentalMustReadTable }: any =
    response.resInfo;
  const expectedData = getBbkStorePolicyProps(policyType, 0, {
    carRentalMustRead: mustReads,
    rentalMustReadPicture: rentalMustReadPicture,
    rentalMustReadTable: rentalMustReadTable,
  });
  const mockStateMap = [
    {
      state: {
        Policy: {
          response,
        },
      },
      expected: expectedData,
    },
    {
      state: {
        Policy: {
          response: {
            baseResponse: {
              extMap: {
                iSDReserveNotice: 33,
                queryRentalMustRead: 0,
              },
            },
          },
        },
      },
      expected: null,
    },
    {
      state: {
        Policy: {},
      },
      expected: null,
    },
  ];
  test.each(mockStateMap)('getPackagedData', ({ state, expected }) => {
    const data = getPackagedData(state);
    expect(data).toEqual(expected);
  });
});

describe('Policy Selectors getParams', () => {
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValueOnce(true);
  const priceInfo = {
    uniqueCode: '12345',
    reference: {
      pStoreCode: '1234',
      rStoreCode: '5678',
      vendorCode: '1111',
      vehicleCode: '0000',
      isEasyLife: true,
    },
    ctripVehicleCode: '1888',
  };
  const skuId = '12345';
  const pickUpTime = '2023-12-19 14:00:00';
  const dropOffTime = '2023-12-19 14:30:00';
  const pointInfoParams = initialState();
  const state = {
    Policy: {
      isLoading: true,
    },
    VendorList: {
      skuId,
      uniqueCode: '12345',
      queryVehicleDetailListRes: {
        specificProductGroups: {
          vendorPriceList: [priceInfo],
        },
      },
    },
    LocationAndDate: {
      ...pointInfoParams,
      rentalDate: {
        pickUp: { dateTime: pickUpTime },
        dropOff: { dateTime: dropOffTime },
      },
    },
  };
  const pointInfoParamsData = getPointInfoParamsV2(state);
  const expected = {
    storeCode: priceInfo?.reference.pStoreCode,
    rStoreCode: priceInfo?.reference.rStoreCode,
    vendorCode: priceInfo?.reference.vendorCode,
    isEasyLife: priceInfo?.reference.isEasyLife,
    pickUpDate: dayjs(pickUpTime).format('YYYY-MM-DD HH:mm:ss'),
    returnDate: dayjs(dropOffTime).format('YYYY-MM-DD HH:mm:ss'),
    ctripVehicleId: priceInfo?.ctripVehicleCode,
    vehicleId: priceInfo?.reference?.vehicleCode,
    ...pointInfoParamsData(false),
    skuId,
  };

  test('getParams', () => {
    const data = getParams(state);
    expect(data).toEqual(expected);
  });
});
