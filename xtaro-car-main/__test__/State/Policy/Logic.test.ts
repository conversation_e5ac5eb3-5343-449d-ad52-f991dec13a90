import { runSaga } from 'redux-saga';
import { queryPolicyLogic } from '../../../src/pages/xcar/State/Policy/Logic';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';
import * as Types from '../../../src/pages/xcar/State/Policy/Types';
import { DEFAULT_CAR_FETCH_RES } from '../../../__mocks__/mockConstants';

const mockParams = {
  storeCode: '123456',
};

jest.mock('../../../src/pages/xcar/State/Policy/Selectors', () => ({
  getParams: jest.fn(() => mockParams),
}));

const actionMock = {
  type: Types.QUERY,
  data: {
    vendorCode: '123456',
  },
};

jest.mock('../../../src/pages/xcar/State/VendorList/Selectors', () => {
  return {
    getUniqueReference: jest.fn(state => state.reference),
    getUniqueCode: jest.fn(state => state.uniqueCode),
  }
});
describe('Policy Logic queryPolicyLogic', () => {
  test('测试正常调用', async () => {
    const dispatched = []; // 保存 put 返回
    // 组装入参
    const request = {
      reqInfo: {
        ...mockParams,
        ...actionMock.data,
      },
    };
    /**
     * runSaga https://redux-saga.js.org/docs/api/#runsagaoptions-saga-args
     * 运行 saga 函数，并记录 put action 副作用，用作后续验证
     */
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      queryPolicyLogic,
      actionMock,
    ).toPromise();

    // 筛选需要校验的 put action
    const resAction = dispatched.find(
      item => item.type === 'Policy/SET_REQUEST_RESPONSE',
    );

    // 校验
    expect(resAction.data).toEqual({
      request,
      response: DEFAULT_CAR_FETCH_RES,
    });
  });

  test('测试异常调用', async () => {
    jest.spyOn(CarFetch, 'rentalMustRead').mockImplementation(
      () =>
        new Promise(() => {
          throw new Error();
        }),
    );

    const dispatched = []; // 保存 put 返回
    // 组装入参
    const request = {
      reqInfo: {
        ...mockParams,
        ...actionMock.data,
      },
    };
    /**
     * runSaga https://redux-saga.js.org/docs/api/#runsagaoptions-saga-args
     * 运行 saga 函数，并记录 put action 副作用，用作后续验证
     */
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({}),
      },
      queryPolicyLogic,
      actionMock,
    ).toPromise();

    // 筛选需要校验的 put action
    const resAction = dispatched.find(
      item => item.type === 'Policy/SET_REQUEST_RESPONSE',
    );

    // 校验
    expect(resAction.data).toEqual({
      request,
      response: null,
    });
  });
});
