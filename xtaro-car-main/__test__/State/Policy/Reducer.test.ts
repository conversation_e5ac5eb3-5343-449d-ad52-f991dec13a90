import PolicyReducer from '../../../src/pages/xcar/State/Policy/Reducer';
import {
  SET_REQUEST_RESPONSE,
  SET_LOADING,
  SET_FAIL,
} from '../../../src/pages/xcar/State/Policy/Types';

const initialState = {
  isLoading: true,
  isFail: false,
  response: null,
  request: null,
};

describe('Policy Reducer SET_REQUEST_RESPONSE', () => {
  const request = {
    storeCode: '632633',
    vendorCode: '12345',
    isEasyLife: false,
  };
  const response = {
    baseResponse: {
      extMap: {
        iSDReserveNotice: 33,
        queryRentalMustRead: 0,
      },
    },
    resInfo: {
      mustReads: [],
    },
  };
  const mockActionMap = [
    {
      actionData: {
        request,
        response,
      },
      expected: {
        ...initialState,
        request,
        response,
      },
    },
    {
      actionData: null,
      expected: {
        ...initialState,
        request: null,
        response: null,
      },
    },
  ];
  test.each(mockActionMap)(
    'SET_REQUEST_RESPONSE',
    ({ actionData, expected }) => {
      expect(
        PolicyReducer(initialState, {
          type: SET_REQUEST_RESPONSE,
          data: actionData,
        }),
      ).toEqual(expected);
    },
  );
});

describe('Policy Reducer SET_LOADING', () => {
  test('SET_LOADING', () => {
    expect(
      PolicyReducer(initialState, {
        type: SET_LOADING,
        data: false,
      }),
    ).toEqual({ ...initialState, isFail: false, isLoading: false });
  });
});

describe('Policy Reducer SET_FAIL', () => {
  test('SET_FAIL', () => {
    expect(
      PolicyReducer(initialState, {
        type: SET_FAIL,
        data: true,
      }),
    ).toEqual({ ...initialState, isFail: true });
  });
});

describe('Policy Reducer Default', () => {
  test('Default', () => {
    expect(
      PolicyReducer(undefined, {
        type: '',
        data: true,
      }),
    ).toEqual(initialState);
  });
});
