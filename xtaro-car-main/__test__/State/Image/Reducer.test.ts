import Reducer, { getInitalState } from '../../../src/pages/xcar/State/Image/Reducer';
import { SET_TAG_ID, FETCH_IMAGE_CALLBACK, SET_SCROLLY, SET_LEFT_RENDER_DATA, SET_RIGHT_RENDER_DATA, RESET } from '../../../src/pages/xcar/State/Image/Types';
import { ImagePageNavId } from '../../../src/pages/xcar/ComponentBusiness/Common/src/Enums';

describe('Reducer Test', () => {
  const initState = getInitalState();

  test('Reducer Init', () => {
    expect(Reducer(undefined, {})).toEqual(initState);
  });

  test('SET_TAG_ID', () => {
    expect(
      Reducer(initState, {
        type: SET_TAG_ID,
        data: ImagePageNavId.Left,
      }),
    ).toEqual({
      ...initState,
      tagId: ImagePageNavId.Left,
    });
  });

  test('FETCH_IMAGE_CALLBACK', () => {
    expect(
      Reducer(initState, {
        type: FETCH_IMAGE_CALLBACK,
        data: {
          isError: true,
          param:{},
          res: {},
          isLoading: false,
        },
      }),
    ).toEqual({
      ...initState,
      isError: true,
      param:{},
      res: {},
      isLoading: false,
    });
  });

  test('SET_SCROLLY', () => {
    expect(
      Reducer(initState, {
        type: SET_SCROLLY,
        data: 10,
      }),
    ).toEqual({
      ...initState,
      scrollY: 10,
    });
  });

  test('SET_LEFT_RENDER_DATA', () => {
    expect(
      Reducer(initState, {
        type: SET_LEFT_RENDER_DATA,
        data: {
          title: "标题",
          tips: '提示文字',
          imageList: [],
          emptyTips:'提示文字2'
        },
      }),
    ).toEqual({
      ...initState,
      leftRenderData: {
        title: "标题",
          tips: '提示文字',
          imageList: [],
          emptyTips:'提示文字2'
      },
    });
  });

  test('SET_RIGHT_RENDER_DATA', () => {
    expect(
      Reducer(initState, {
        type: SET_RIGHT_RENDER_DATA,
        data: {
          title: "标题",
          tips: '提示文字',
          imageList: [],
          emptyTips:'提示文字2'
        },
      }),
    ).toEqual({
      ...initState,
      rightRenderData: {
        title: "标题",
          tips: '提示文字',
          imageList: [],
          emptyTips:'提示文字2'
      },
    });
  });

  test('RESET', () => {
    expect(
      Reducer(initState, {
        type: RESET,
        data: {},
      }),
    ).toEqual({
      ...getInitalState()
    });
  });

})
