import { recordSaga } from '../../testHelpers';
import { CarFetch} from '../../../src/pages/xcar/Util/Index';
import { apiFetchImageList, getLeftRenderData, getRightRenderData } from '../../../src/pages/xcar/State/Image/Logic';
import { FETCH_IMAGE_LIST, SET_LEFT_RENDER_DATA, SET_RIGHT_RENDER_DATA, SET_TAG_ID, FETCH_IMAGE_CALLBACK, RESET } from '../../../src/pages/xcar/State/Image/Types';

const mockRes = {
  "baseResponse":{
      "code":"0",
      "returnMsg":"success"
  },
  "defaultType":2,
  "userPictureInfo":{
      "title":"用户实拍",
      "tips":"以下图片来自携程租车用户实际拍摄，具体请以门店实物为准",
      "type":3,
      "userPicList":[
          {
              "picid":16712202,
              "commentId":23741748,
              "picUrl":"https://dimg04.c-ctrip.com/images/0301c120009v3olmaDADC.png",
              "smallPicUrl":"https://dimg04.c-ctrip.com/images/0301c120009v3olmaDADC_C_360_360.png",
              "isRealShot":true,
              "remark":"很好的租车体验、小哥服务超好、免费升级的新车超新"
          },
          {
              "picid":16698352,
              "commentId":23719338,
              "picUrl":"https://dimg04.c-ctrip.com/images/0304s120009uu2emk65FA.jpg",
              "smallPicUrl":"https://dimg04.c-ctrip.com/images/0304s120009uu2emk65FA_C_360_360.jpg",
              "isRealShot":true,
              "remark":"租车体验很好，车子很新，早上8点到机场商家送车也没有迟到，很好的一次体验，还车也非常方便，提前跟商家联系到目的地就可以还，很棒"
          }
      ]
  },
  "storePictureInfo":{
      "title":"参考样图",
      "tips":"以下图片仅供参考，具体请以门店实物为准",
      "noResultPrompt":"暂无数据",
      "type":2,
      "vehiclePicList":[
          {
              "imageUrl":"https://dimg04.c-ctrip.com//images/0415g120008at13i22922.jpg",
              "sortNum":0
          },
          {
              "imageUrl":"https://dimg04.c-ctrip.com//images/0410j120008at1cpx1D8D.jpg",
              "sortNum":1
          }
      ]
  }
};

describe('Image function getLeftRenderData', () => {
  const mockResMap = [
    {
      state: mockRes,
      expected: {
        "title": "参考样图",
        "tips": "以下图片仅供参考，具体请以门店实物为准",
        "imageList": [
          {
            "imageUrl": "https://dimg04.c-ctrip.com//images/0415g120008at13i22922.jpg",
            "sortNum": 0
          },
          {
            "imageUrl": "https://dimg04.c-ctrip.com//images/0410j120008at1cpx1D8D.jpg",
            "sortNum": 1
          }
        ],
        "emptyTips": "暂无数据"
      },
    },
    {
      state: {},
      expected: { "title": "", "tips": "", "imageList": [], "emptyTips": "" },
    },
  ];
  test.each(mockResMap)(
    'getLeftRenderData check',
    ({ state, expected }) => {
      const data = getLeftRenderData(state);
      expect(data).toEqual(expected);
    },
  );
})

jest.mock('../../../src/pages/xcar/State/VendorList/Selectors', () => {
  return {
    getUniqueReference: jest.fn(state => state.reference),
  }
});
describe('Image Logic apiFetchImageList', () => {
  const testFn = async res => {
    const api = jest
      .spyOn(CarFetch, 'getImageList').mockReturnValueOnce(res);
    const dispatched = await recordSaga(apiFetchImageList, {
      action: {
        type: FETCH_IMAGE_LIST,
        data: {}
      },
      state:{},
    });
    return {api, dispatched};
  };

  test('测试正常调用', async () => {
    const {api, dispatched} = await testFn(mockRes);
    expect(api).toBeCalled();
    const leftRenderData = getLeftRenderData(mockRes);
    const rightRenderData = getRightRenderData(mockRes);
    expect(dispatched[1]).toEqual({
      type: SET_LEFT_RENDER_DATA,
      data: leftRenderData,
    });
    expect(dispatched[2]).toEqual({
      type: SET_RIGHT_RENDER_DATA,
      data: rightRenderData,
    });
    expect(dispatched[3]).toEqual({
      type: SET_TAG_ID,
      data: 'store-real-shot',
    });
    expect(dispatched[4]).toEqual({
      type: FETCH_IMAGE_CALLBACK,
      data: {
        isError: false,
        res: mockRes,
        isLoading: false,
        param:{},
      },
    });
  });

  test('测试调用异常', async () => {
    const exceptionError = new Error('modifyDriver exception');
    const { dispatched } = await testFn(new Promise(() => {
      throw exceptionError;
    }));
    expect(dispatched[1]).toEqual({
      type: FETCH_IMAGE_CALLBACK,
      data: {
        isError: true,
        res: exceptionError,
        isLoading: false,
        param:{},
      },
    });
  });
});