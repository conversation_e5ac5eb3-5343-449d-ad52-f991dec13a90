import { recordSaga } from '../../testHelpers';
import {
  getCredentialsListLogic,
} from '../../../src/pages/xcar/State/Credentials/Logic';
import * as Types from '../../../src/pages/xcar/State/Credentials/Types';
import * as Actions from '../../../src/pages/xcar/State/Credentials/Actions';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';

describe('Credentials', () => {
  test('getCredentialsListLogic', async () => {
    // mock接口返回数据
    const getCredentialsListApi = jest.spyOn(CarFetch, 'getCredentialsListApi').mockReturnValue(Promise.resolve({
      baseResponse: {
        isSuccess: true,
      },
      contacts: [
        {
          "contactSubTitle": "门店提供的租车合同文件",
          "contactTitle": "电子合同",
          "contactType": 1,
          "url": "http://ws.downloadfile.fx.ctripcorp.com/files/98/carosdfile/image/uz0tcn134057c511c8FEB.jpg"
        }
      ]
    }));
    // 获取saga执行记录
    const dispatched = await recordSaga(getCredentialsListLogic, {
      action: {
        type: Types.GET_CREDENTIALS_LIST,
        data: {},
      },
      state: {
        OrderDetail: {
          vendorInfo: {
            "vendorName": "鹏琛租车",
            "vendorImageUrl": "http://pic.c-ctrip.com/car_isd/vendorlogo/62494.jpg",
            "vendorID": 62494,
            "vendorConfirmCode": "3082488752",
            "isSelf": false,
            "selfName": "",
            "vendorMobileImageUrl": "",
            "commentInfo": {
              "vendorGoodType": 0,
              "exposedScore": 0,
              "topScore": 5,
              "level": "",
              "commentCount": 2,
              "hasComment": 0
            }
          },
        },
      },
    });
    expect(getCredentialsListApi).toBeCalled();
    expect(dispatched).toEqual([
      Actions.getCredentialsListCallBack({
        contacts: [
          {
            "contactSubTitle": "门店提供的租车合同文件",
            "contactTitle": "电子合同",
            "contactType": 1,
            "url": "http://ws.downloadfile.fx.ctripcorp.com/files/98/carosdfile/image/uz0tcn134057c511c8FEB.jpg"
          }
        ],
      })
    ])
  })
})
