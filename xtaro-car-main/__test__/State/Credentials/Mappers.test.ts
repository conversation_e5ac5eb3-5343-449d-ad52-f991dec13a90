import {
  mapContactsToCredentials,
  mapCredentialsToPhotoList,
} from '../../../src/pages/xcar/State/Credentials/Mappers';

describe('Credentials mapContactsToCredentials', () => {
  const mockData = [
    {
      data: [
        {
          "contactSubTitle": "门店提供的租车合同文件",
          "contactTitle": "电子合同",
          "contactType": 1,
          "url": "http://ws.downloadfile.fx.ctripcorp.com/files/98/carosdfile/image/uz0tcn134057c511c8FEB.jpg",
          contactUrls: ['http://ws.downloadfile.fx.ctripcorp.com/files/98/carosdfile/image/uz0tcn134057c511c8FEB.jpg'],
        },
        {
          "contactSubTitle": "结算支付明细及还车验车单",
          "contactTitle": "结算单及验车单",
          "contactType": 3,
          "time": "还车后提供"
        }
      ],
      expected: [{
        title: "电子合同",
        desc: '门店提供的租车合同文件',
        contactUrls: ['http://ws.downloadfile.fx.ctripcorp.com/files/98/carosdfile/image/uz0tcn134057c511c8FEB.jpg'],
        cover: 'http://ws.downloadfile.fx.ctripcorp.com/files/98/carosdfile/image/uz0tcn134057c511c8FEB.jpg',
        disable: false,
        photoIndex: 0,
        button: undefined,
        contactType: 1,
        time: undefined,
        sign: undefined,
        signingDesc: undefined,
        isThumbnail: 'http://ws.downloadfile.fx.ctripcorp.com/files/98/carosdfile/image/uz0tcn134057c511c8FEB.jpg',
      }, {
        title: "结算单及验车单",
        desc: '结算支付明细及还车验车单',
        cover: 'https://pages.c-ctrip.com/carisd/app/easylife/not_upload_bg.png',
        disable: true,
        photoIndex: 0,
        button: undefined,
        contactType: 3,
        time: '还车后提供',
        sign: undefined,
        signingDesc: undefined,
        isThumbnail: '',
        contactUrls: undefined,
      }],
    }];
  test.each(mockData)('mapContactsToCredentials', ({ data, expected }) => {
    expect(mapContactsToCredentials(data)).toEqual(expected);
  })
});


describe('Credentials mapCredentialsToPhotoList', () => {
  const mockData = [
    {
      data: {
        disable: false,
        title: '结算单及验车单',
        contactUrls: ['http://ws.downloadfile.fx.ctripcorp.com/files/98/carosdfile/image/uz0tcn134057c511c8FEB.jpg']
      },
      expected: [{
        imageUrl: 'http://ws.downloadfile.fx.ctripcorp.com/files/98/carosdfile/image/uz0tcn134057c511c8FEB.jpg?size=large',
        imageTitle: '结算单及验车单',
      }],
    }, {
      data: {
        disable: true,
        title: '结算单及验车单',
        contactUrls: ['http://ws.downloadfile.fx.ctripcorp.com/files/98/carosdfile/image/uz0tcn134057c511c8FEB.jpg']
      },
      expected: [],
    }];
  test.each(mockData)('mapCredentialsToPhotoList', ({ data, expected }) => {
    expect(mapCredentialsToPhotoList(data)).toEqual(expected);
  })
});