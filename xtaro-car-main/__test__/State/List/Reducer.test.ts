import ListReducer, {
  addSaleOutList,
  getInitalState,
} from '../../../src/pages/xcar/State/List/Reducer';
import {
  SET_PRICE_SUMMARY_MODAL,
  SET_TIMEOUT_POP_DATA,
  FETCH_LIST_RECOMMEND,
  FETCH_LIST_RECOMMEND_CALLBACK,
  SET_PAGE_STATUS,
  SET_STATUS,
  INIT_SET_GROUPID,
  SET_GROUPID,
  SET_ACTIVE_FILTER_BAR_CODE,
  UPDATE_SELECTED_FILTER,
  SET_LOCATIONDATEPOP_VISIBLE,
  C<PERSON>AR_SELECTED_FILTER,
  SET_AGEPICKER_VISIBLE,
  SET_AGETIPPOP_VISIBLE,
  SET_PROGRESS_ISFINISH,
  SET_SORTANDFILTER_VISIBLE,
  SET_QUICKFILTERBAR_ISSHOW,
  SET_DRIVERLICENSE_POP_DATA,
  SET_SALEOUT_LIST,
  ADD_SALEOUT_LIST,
  ADD_VEHICLE_SALEOUT_LIST,
  LIMITRULE_POP_VISIBLE,
  SET_VEHPOP_DATA,
  SET_LIST_RENTCENTER,
  SET_VENDORLISTMODAL_DATA,
  SET_SHOW_FILTERED_PROGRESS,
  SET_IS_MORE_AGE,
  SET_SECRET_BOX_MODAL_DATA,
  SET_LICENSE_MODAL_DATA,
} from '../../../src/pages/xcar/State/List/Types';

jest.mock('../../../src/pages/xcar/State/List/Mappers', () => {
  return {
    getAllCarsConfig: jest.fn(() => ({
      groupCode: '1111',
    })),
  };
});

describe('List Reducer Test', () => {
  const initState = getInitalState();

  test('List Reducer Init', () => {
    expect(ListReducer(undefined, {})).toEqual(initState);
  });

  describe('List Reducer SET_PRICE_SUMMARY_MODAL', () => {
    test('SET_PRICE_SUMMARY_MODAL: 不传 action data.data', () => {
      expect(
        ListReducer(initState, {
          type: SET_PRICE_SUMMARY_MODAL,
          data: {
            visible: true,
          },
        }),
      ).toEqual({
        ...initState,
        priceSummaryModalData: {
          visible: true,
          data: {},
          secretBoxPriceModalVisible: false,
        },
      });
    });

    test('SET_PRICE_SUMMARY_MODAL: action data 空值', () => {
      expect(
        ListReducer(initState, {
          type: SET_PRICE_SUMMARY_MODAL,
          data: {
            data: null,
            visible: true,
          },
        }),
      ).toEqual({
        ...initState,
        priceSummaryModalData: {
          data: null,
          visible: true,
          secretBoxPriceModalVisible: false,
        },
      });
    });

    test('SET_PRICE_SUMMARY_MODAL: action data 正常值', () => {
      expect(
        ListReducer(initState, {
          type: SET_PRICE_SUMMARY_MODAL,
          data: {
            data: {
              foo: 1,
            },
            visible: true,
          },
        }),
      ).toEqual({
        ...initState,
        priceSummaryModalData: {
          data: {
            foo: 1,
          },
          visible: true,
          secretBoxPriceModalVisible: false,
        },
      });
    });
  });

  describe('List Reducer SET_PRICE_SUMMARY_MODAL', () => {
    test('SET_TIMEOUT_POP_DATA: action data 不传', () => {
      expect(
        ListReducer(initState, {
          type: SET_TIMEOUT_POP_DATA,
          data: {
            visible: null,
            title: null,
            content: null,
            rightText: null,
          },
        }),
      ).toEqual({
        ...initState,
        timeOutPopData: {
          visible: false,
          title: '',
          content: '',
          rightText: '',
        },
      });
    });

    test('SET_TIMEOUT_POP_DATA: action data 正常值', () => {
      expect(
        ListReducer(initState, {
          type: SET_TIMEOUT_POP_DATA,
          data: {
            visible: true,
            title: '超时请重试',
            content: null,
            rightText: null,
          },
        }),
      ).toEqual({
        ...initState,
        timeOutPopData: {
          visible: true,
          title: '超时请重试',
          content: '',
          rightText: '',
        },
      });
    });
  });

  describe('List Reducer FETCH_LIST_RECOMMEND', () => {
    const mockActionMap = [
      {
        expected: {
          ...initState,
          isRecommendLoading: true,
          isRecommendNoResult: false,
          recommendTip: '',
          recommendDesc: '',
          recommendTitle: '',
        },
      },
    ];
    test.each(mockActionMap)('FETCH_LIST_RECOMMEND check', ({ expected }) => {
      expect(
        ListReducer(initState, {
          type: FETCH_LIST_RECOMMEND,
        }),
      ).toEqual(expected);
    });
  });

  describe('List Reducer FETCH_LIST_RECOMMEND_CALLBACK', () => {
    const mockActionMap = [
      {
        data: {
          isRecommendNoResult: true,
          recommendAllVehicleCount: 5,
          recommendAllVendorPriceCount: 30,
          recommendTip: '以下为满足您部分要求的车辆',
          recommendDesc: '建议您修改取还车条件',
          recommendTitle: '暂无符合要求的车辆哦',
          recommendButtonTitle: '以下为满足您部分要求的车辆',
          recommendType: 'r1',
          subStrategyType: 1,
          recUniqsign: '123456',
          cid: 1212,
          availableLocation: '',
          returnAvailableTime: '',
          pickUpAvailableTime: '',
          availableLocationCode: '2222',
          longitude: 2121.2121,
          latitude: 2121.2121,
        },
        expected: {
          ...initState,
          isRecommendLoading: false,
          isRecommendNoResult: true,
          recommendAllVehicleCount: 5,
          recommendAllVendorPriceCount: 30,
          recommendTip: '以下为满足您部分要求的车辆',
          recommendDesc: '建议您修改取还车条件',
          recommendTitle: '暂无符合要求的车辆哦',
          recommendButtonTitle: '以下为满足您部分要求的车辆',
          recommendType: 'r1',
          subStrategyType: 1,
          recUniqsign: '123456',
          recommendCid: 1212,
          availableLocation: '',
          returnAvailableTime: '',
          pickUpAvailableTime: '',
          availableLocationCode: '2222',
          longitude: 2121.2121,
          latitude: 2121.2121,
          noResultRecTip: undefined,
        },
      },
      {
        data: {
          isRecommendNoResult: false,
        },
        expected: {
          ...initState,
          isRecommendLoading: false,
          isRecommendNoResult: false,
          recommendAllVehicleCount: undefined,
          recommendAllVendorPriceCount: undefined,
          recommendTip: undefined,
          recommendDesc: undefined,
          recommendTitle: undefined,
          recommendType: undefined,
          recommendButtonTitle: undefined,
          subStrategyType: undefined,
          recUniqsign: undefined,
          availableLocation: undefined,
          availableLocationCode: undefined,
          latitude: undefined,
          longitude: undefined,
          recommendCid: undefined,
          returnAvailableTime: undefined,
          pickUpAvailableTime: undefined,
          noResultRecTip: undefined,
        },
      },
      {
        data: {},
        expected: {
          ...initState,
          isRecommendLoading: false,
          isRecommendNoResult: undefined,
          recommendAllVehicleCount: undefined,
          recommendAllVendorPriceCount: undefined,
          recommendTip: undefined,
          recommendDesc: undefined,
          recommendTitle: undefined,
          recommendType: undefined,
          recommendButtonTitle: undefined,
          subStrategyType: undefined,
          recUniqsign: undefined,
          availableLocation: undefined,
          availableLocationCode: undefined,
          latitude: undefined,
          longitude: undefined,
          recommendCid: undefined,
          returnAvailableTime: undefined,
          pickUpAvailableTime: undefined,
          noResultRecTip: undefined,
        },
      },
    ];
    test.each(mockActionMap)(
      'FETCH_LIST_RECOMMEND_CALLBACK check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: FETCH_LIST_RECOMMEND_CALLBACK,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_PAGE_STATUS', () => {
    const mockActionMap = [
      {
        data: {
          activeGroupId: 'newenergy',
          isLoading: false,
          isFail: false,
          progress: 1,
          progressIsFinish: true,
          isShowToast: false,
        },
        expected: {
          ...initState,
          activeGroupId: 'newenergy',
          isLoading: false,
          isFail: false,
          progress: 1,
          progressIsFinish: true,
          isShowToast: false,
        },
      },
    ];
    test.each(mockActionMap)('SET_PAGE_STATUS check', ({ data, expected }) => {
      expect(
        ListReducer(initState, {
          type: SET_PAGE_STATUS,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('List Reducer SET_STATUS', () => {
    const mockActionMap = [
      {
        data: {
          isLoading: false,
          isFail: false,
          isError: false,
          progress: 1,
        },
        expected: {
          ...initState,
          isLoading: false,
          isFail: false,
          isError: false,
          progress: 1,
        },
      },
    ];
    test.each(mockActionMap)('SET_STATUS check', ({ data, expected }) => {
      expect(
        ListReducer(initState, {
          type: SET_STATUS,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('List Reducer INIT_SET_GROUPID', () => {
    const mockActionMap = [
      {
        data: {
          activeGroupId: '',
        },
        expected: {
          ...initState,
        },
      },
    ];
    test.each(mockActionMap)('INIT_SET_GROUPID check', ({ data, expected }) => {
      expect(
        ListReducer(initState, {
          type: INIT_SET_GROUPID,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('List Reducer INIT_SET_GROUPID', () => {
    const mockActionMap = [
      {
        data: {
          activeGroupId: '',
        },
        expected: {
          ...initState,
        },
      },
    ];
    test.each(mockActionMap)('INIT_SET_GROUPID check', ({ data, expected }) => {
      expect(
        ListReducer(initState, {
          type: INIT_SET_GROUPID,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('List Reducer SET_GROUPID', () => {
    const mockActionMap = [
      {
        data: {
          activeGroupId: 'easyLife',
        },
        expected: {
          ...initState,
          activeGroupId: 'easyLife',
        },
      },
    ];
    test.each(mockActionMap)('SET_GROUPID check', ({ data, expected }) => {
      expect(
        ListReducer(initState, {
          type: SET_GROUPID,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('List Reducer SET_ACTIVE_FILTER_BAR_CODE', () => {
    const mockActionMap = [
      {
        data: {
          activeFilterBarCode: 'easyLife',
          activeFilterBarName: '无忧租',
        },
        expected: {
          ...initState,
          activeFilterBarCode: 'easyLife',
          activeFilterBarName: '无忧租',
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_ACTIVE_FILTER_BAR_CODE check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_ACTIVE_FILTER_BAR_CODE,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer UPDATE_SELECTED_FILTER', () => {
    const mockActionMap = [
      {
        data: {},
        expected: {
          ...initState,
          showFilteredProgress: true,
        },
      },
    ];
    test.each(mockActionMap)(
      'UPDATE_SELECTED_FILTER check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: UPDATE_SELECTED_FILTER,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_LOCATIONDATEPOP_VISIBLE', () => {
    const mockActionMap = [
      {
        data: {
          visible: true,
          recommendType: 1,
          availableLocation: '',
          longitude: '',
          latitude: '',
        },
        expected: {
          ...initState,
          locationDatePopVisible: true,
          recommendType: '',
          availableLocation: '',
          longitude: '',
          latitude: '',
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_LOCATIONDATEPOP_VISIBLE check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_LOCATIONDATEPOP_VISIBLE,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer CLEAR_SELECTED_FILTER', () => {
    const mockActionMap = [
      {
        data: {},
        expected: {
          ...initState,
        },
      },
    ];
    test.each(mockActionMap)(
      'CLEAR_SELECTED_FILTER check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: CLEAR_SELECTED_FILTER,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_AGEPICKER_VISIBLE', () => {
    const mockActionMap = [
      {
        data: {
          visible: true,
        },
        expected: {
          ...initState,
          agePickerVisible: true,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_AGEPICKER_VISIBLE check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_AGEPICKER_VISIBLE,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_AGETIPPOP_VISIBLE', () => {
    const mockActionMap = [
      {
        data: {
          visible: true,
        },
        expected: {
          ...initState,
          ageTipPopVisible: true,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_AGETIPPOP_VISIBLE check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_AGETIPPOP_VISIBLE,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_PROGRESS_ISFINISH', () => {
    const mockActionMap = [
      {
        data: true,
        expected: {
          ...initState,
          progressIsFinish: true,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_PROGRESS_ISFINISH check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_PROGRESS_ISFINISH,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_SORTANDFILTER_VISIBLE', () => {
    const mockActionMap = [
      {
        data: {
          visible: true,
        },
        expected: {
          ...initState,
          sortAndFilterVisible: true,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_SORTANDFILTER_VISIBLE check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_SORTANDFILTER_VISIBLE,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_QUICKFILTERBAR_ISSHOW', () => {
    const mockActionMap = [
      {
        data: true,
        expected: {
          ...initState,
          quickFilterBarIsShow: true,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_QUICKFILTERBAR_ISSHOW check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_QUICKFILTERBAR_ISSHOW,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_DRIVERLICENSE_POP_DATA', () => {
    const mockActionMap = [
      {
        data: {
          visible: true,
          content: '证件弹层标题',
          leftText: '取消',
        },
        expected: {
          ...initState,
          driverlicensePopData: {
            visible: true,
            content: '证件弹层标题',
            leftText: '取消',
          },
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_DRIVERLICENSE_POP_DATA check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_DRIVERLICENSE_POP_DATA,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_SALEOUT_LIST', () => {
    const mockActionMap = [
      {
        data: {
          saleOutList: [],
        },
        expected: {
          ...initState,
          saleOutList: [],
        },
      },
    ];
    test.each(mockActionMap)('SET_SALEOUT_LIST check', ({ data, expected }) => {
      expect(
        ListReducer(initState, {
          type: SET_SALEOUT_LIST,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('List Reducer ADD_SALEOUT_LIST', () => {
    const mockActionMap = [
      {
        data: 'vehicle1_vendor1_saleout',
        expected: {
          ...initState,
          saleOutList: ['vehicle1_vendor1_saleout'],
        },
      },
    ];
    test.each(mockActionMap)('ADD_SALEOUT_LIST check', ({ data, expected }) => {
      expect(
        ListReducer(initState, {
          type: ADD_SALEOUT_LIST,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('List Reducer ADD_VEHICLE_SALEOUT_LIST', () => {
    const mockActionMap = [
      {
        data: 'vehicle1_saleout',
        expected: {
          ...initState,
          vehicleSoldOutList: ['vehicle1_saleout'],
        },
      },
    ];
    test.each(mockActionMap)(
      'ADD_VEHICLE_SALEOUT_LIST check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: ADD_VEHICLE_SALEOUT_LIST,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer LIMITRULE_POP_VISIBLE', () => {
    const mockActionMap = [
      {
        data: {
          visible: true,
        },
        expected: {
          ...initState,
          limitRulePopVisible: true,
        },
      },
    ];
    test.each(mockActionMap)(
      'LIMITRULE_POP_VISIBLE check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: LIMITRULE_POP_VISIBLE,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_VEHPOP_DATA', () => {
    const mockActionMap = [
      {
        data: {
          visible: true,
          vehicleCode: '111222',
        },
        expected: {
          ...initState,
          vehPopData: {
            visible: true,
            vehicleCode: '111222',
          },
        },
      },
    ];
    test.each(mockActionMap)('SET_VEHPOP_DATA check', ({ data, expected }) => {
      expect(
        ListReducer(initState, {
          type: SET_VEHPOP_DATA,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('List Reducer SET_LIST_RENTCENTER', () => {
    const mockActionMap = [
      {
        data: {
          rentCenter: null,
        },
        expected: {
          ...initState,
          rentCenter: null,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_LIST_RENTCENTER check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_LIST_RENTCENTER,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_VENDORLISTMODAL_DATA', () => {
    const mockActionMap = [
      {
        data: {
          visible: false,
          data: null,
        },
        expected: {
          ...initState,
          vendorModalData: {
            visible: false,
            data: null,
          },
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_VENDORLISTMODAL_DATA check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_VENDORLISTMODAL_DATA,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_SHOW_FILTERED_PROGRESS', () => {
    const mockActionMap = [
      {
        data: false,
        expected: {
          ...initState,
          showFilteredProgress: false,
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_SHOW_FILTERED_PROGRESS check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_SHOW_FILTERED_PROGRESS,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('List Reducer SET_IS_MORE_AGE', () => {
    const mockActionMap = [
      {
        data: false,
        expected: {
          ...initState,
          isMoreAge: false,
        },
      },
      {
        data: true,
        expected: {
          ...initState,
          isMoreAge: true,
        },
      },
    ];
    test.each(mockActionMap)('SET_IS_MORE_AGE check', ({ data, expected }) => {
      expect(
        ListReducer(initState, {
          type: SET_IS_MORE_AGE,
          data,
        }),
      ).toEqual(expected);
    });
  });

  describe('List Reducer SET_SECRET_BOX_MODAL_DATA', () => {
    test('SET_SECRET_BOX_MODAL_DATA: action data 不传', () => {
      expect(
        ListReducer(initState, {
          type: SET_SECRET_BOX_MODAL_DATA,
          data: {
            visible: false,
          },
        }),
      ).toEqual({
        ...initState,
        secretBoxModalVisible: false,
        secretBoxModalData: undefined,
      });
    });

    test('SET_SECRET_BOX_MODAL_DATA: action data 正常值', () => {
      const data = {
        vehicleCode: 1011,
        minTPrice: 128,
        minDPrice: 78,
        isOptim: true,
        isEasy: true,
        pTag: [],
        outTags: [],
        priceSize: 2,
        minDOrinPrice: 98,
        isCredit: false,
        productRef: {
          license: '沪牌',
        },
        type: 1,
      };
      expect(
        ListReducer(initState, {
          type: SET_SECRET_BOX_MODAL_DATA,
          data: {
            visible: true,
            data,
          },
        }),
      ).toEqual({
        ...initState,
        secretBoxModalVisible: true,
        secretBoxModalData: data,
      });
    });
  });

  describe('List Reducer SET_LICENSE_MODAL_DATA', () => {
    const mockActionMap = [
      {
        data: {
          visible: true,
          data: '因商业条款限制，持非中国（含港澳台）驾照无法预订。建议您改为持有中国驾照的同伴进行取车或前往Trip下单',
          title: '驾照政策',
        },
        expected: {
          ...initState,
          licenseModalData: {
            visible: true,
            data: '因商业条款限制，持非中国（含港澳台）驾照无法预订。建议您改为持有中国驾照的同伴进行取车或前往Trip下单',
            title: '驾照政策',
          },
        },
      },
      {
        data: {},
        expected: {
          ...initState,
          licenseModalData: {
            visible: false,
            data: undefined,
            title: undefined,
          },
        },
      },
    ];
    test.each(mockActionMap)(
      'SET_LICENSE_MODAL_DATA check',
      ({ data, expected }) => {
        expect(
          ListReducer(initState, {
            type: SET_LICENSE_MODAL_DATA,
            data,
          }),
        ).toEqual(expected);
      },
    );
  });

  describe('addSaleOutList', () => {
    test('should add saleOutProductKey to the saleOutList', () => {
      const state = {
        saleOutList: ['productA', 'productB'],
        priceSoldOutTextMap: {},
      };
      const data = 'productC';
      const expected = {
        saleOutList: ['productA', 'productB', 'productC'],
        priceSoldOutTextMap: {},
      };

      const result = addSaleOutList(state, data);
      expect(result).toEqual(expected);
    });

    test('should update priceSoldOutTextMap when data is an object', () => {
      const state = {
        saleOutList: [],
        priceSoldOutTextMap: {},
      };
      const data = {
        saleOutProductKey: 'productD',
        priceSoldOutText: 'Sold out',
      };
      const expected = {
        saleOutList: ['productD'],
        priceSoldOutTextMap: {
          productD: 'Sold out',
        },
      };

      const result = addSaleOutList(state, data);
      expect(result).toEqual(expected);
    });
  });
});
