import dayjs from '../../../src/pages/xcar/Common/src/Dayjs/src';
import {
  getSelectedfilterLabels,
  getIsRecommendLoading,
  getRecommendType,
  getPickUpAvailableTime,
  getReturnAvailableTime,
  getAvailableLocation,
  getLongitude,
  getLatitude,
  getIsRecommendNoResult,
  getRecommendAllVehicleCount,
  getRecommendAllVendorPriceCount,
  getRecommendTitle,
  getRecommendDesc,
  getRecommendTip,
  getIsMoreAge,
  getSecretBoxPriceModalVisible,
  getSecretBoxModalVisible,
  getSecretBoxModalData,
  getLicenseModalData,
} from '../../../src/pages/xcar/State/List/Selectors';

// Mock 被测试方法所在文件的外部依赖方法
jest.mock('../../../src/pages/xcar/Global/Cache/ListResSelectors', () => {
  return {
    getVehGroupList: jest.fn(() => []),
    getSortList: jest.fn(),
    getBaseProductGroups: jest.fn(),
  };
});

// state mock 配置数据，用于遍历执行 case
const mockStateMap = [
  {
    state: {
      List: {
        selectedFilters: {
          priceFilter: [],
          filterLabels: null,
        },
      },
    },
    expected: '',
  },
  {
    state: {
      List: {
        selectedFilters: {
          priceFilter: [{ min: 50, max: 100 }],
          filterLabels: null,
        },
      },
    },
    expected: '¥50-100',
  },
  {
    state: {
      List: {
        selectedFilters: {
          priceFilter: [{ min: 50, max: 100 }],
          filterLabels: [
            {
              code: 'Transmission_1',
              name: '自动挡',
              groupCode: 'Transmission',
              type: 'QuickChoose',
              isSingleChoice: true,
            },
            {
              code: 'Vendor_-1',
              name: '携程优选',
              groupCode: 'Vendor_0',
            },
          ],
        },
      },
    },
    expected: '自动挡,携程优选,¥50-100',
  },
];

describe('List Selectors Test', () => {
  test.each(mockStateMap)(
    'getSelectedfilterLabels check',
    ({ state, expected }) => {
      const labels: any = getSelectedfilterLabels(state);
      expect(labels).toEqual(expected);
    },
  );
});

describe('List Selectors getIsRecommendLoading', () => {
  const mockStateMap = [
    {
      state: {
        List: {
          isRecommendLoading: true,
        },
      },
      expected: true,
    },
    {
      state: {
        List: {
          isRecommendLoading: false,
        },
      },
      expected: false,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getIsRecommendLoading check',
    ({ state, expected }) => {
      const data = getIsRecommendLoading(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('List Selectors getRecommendType', () => {
  const recommendType = 'r1';
  const mockStateMap = [
    {
      state: {
        List: {
          recommendType,
        },
      },
      expected: recommendType,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getRecommendType check', ({ state, expected }) => {
    const data = getRecommendType(state);
    expect(data).toEqual(expected);
  });
});

describe('List Selectors getPickUpAvailableTime', () => {
  const pickUpAvailableTime = dayjs('2022-09-13 10:00:00', 'YYYYMMDDHHmmss');
  const mockStateMap = [
    {
      state: {
        List: {
          pickUpAvailableTime,
        },
      },
      expected: pickUpAvailableTime,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getPickUpAvailableTime check',
    ({ state, expected }) => {
      const data = getPickUpAvailableTime(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('List Selectors getReturnAvailableTime', () => {
  const returnAvailableTime = dayjs('2022-09-15 10:00:00', 'YYYYMMDDHHmmss');
  const mockStateMap = [
    {
      state: {
        List: {
          returnAvailableTime,
        },
      },
      expected: returnAvailableTime,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getReturnAvailableTime check',
    ({ state, expected }) => {
      const data = getReturnAvailableTime(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('List Selectors getAvailableLocation', () => {
  const availableLocation = '亚龙湾客运东站';
  const mockStateMap = [
    {
      state: {
        List: {
          availableLocation,
        },
      },
      expected: availableLocation,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getAvailableLocation check',
    ({ state, expected }) => {
      const data = getAvailableLocation(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('List Selectors getLongitude', () => {
  const longitude = '18.302277';
  const mockStateMap = [
    {
      state: {
        List: {
          longitude,
        },
      },
      expected: longitude,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getLongitude check', ({ state, expected }) => {
    const data = getLongitude(state);
    expect(data).toEqual(expected);
  });
});

describe('List Selectors getLatitude', () => {
  const latitude = '109.603911';
  const mockStateMap = [
    {
      state: {
        List: {
          latitude,
        },
      },
      expected: latitude,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getLatitude check', ({ state, expected }) => {
    const data = getLatitude(state);
    expect(data).toEqual(expected);
  });
});

describe('List Selectors getIsRecommendNoResult', () => {
  const mockStateMap = [
    {
      state: {
        List: {
          isRecommendNoResult: true,
        },
      },
      expected: true,
    },
    {
      state: {
        List: {
          isRecommendNoResult: false,
        },
      },
      expected: false,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getIsRecommendNoResult check',
    ({ state, expected }) => {
      const data = getIsRecommendNoResult(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('List Selectors getRecommendAllVehicleCount', () => {
  const mockStateMap = [
    {
      state: {
        List: {
          recommendAllVehicleCount: 5,
        },
      },
      expected: 5,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getRecommendAllVehicleCount check',
    ({ state, expected }) => {
      const data = getRecommendAllVehicleCount(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('List Selectors getRecommendAllVendorPriceCount', () => {
  const mockStateMap = [
    {
      state: {
        List: {
          recommendAllVendorPriceCount: 30,
        },
      },
      expected: 30,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getRecommendAllVendorPriceCount check',
    ({ state, expected }) => {
      const data = getRecommendAllVendorPriceCount(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('List Selectors getRecommendTitle', () => {
  const recommendTitle = '附近暂时没有门店哦';
  const mockStateMap = [
    {
      state: {
        List: {
          recommendTitle,
        },
      },
      expected: recommendTitle,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getRecommendTitle check', ({ state, expected }) => {
    const data = getRecommendTitle(state);
    expect(data).toEqual(expected);
  });
});

describe('List Selectors getRecommendDesc', () => {
  const recommendDesc = '建议您修改取还车条件，或选择距取车地较远的门店';
  const mockStateMap = [
    {
      state: {
        List: {
          recommendDesc,
        },
      },
      expected: recommendDesc,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getRecommendDesc check', ({ state, expected }) => {
    const data = getRecommendDesc(state);
    expect(data).toEqual(expected);
  });
});

describe('List Selectors getRecommendTip', () => {
  const recommendTip = '修改取还车条件';
  const mockStateMap = [
    {
      state: {
        List: {
          recommendTip,
        },
      },
      expected: recommendTip,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getRecommendBtn check', ({ state, expected }) => {
    const data = getRecommendTip(state);
    expect(data).toEqual(expected);
  });
});

describe('List Selectors getIsMoreAge', () => {
  const mockStateMap = [
    {
      state: {
        List: {
          isMoreAge: true,
        },
      },
      expected: true,
    },
    {
      state: {
        List: {
          isMoreAge: false,
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)('getIsMoreAge check', ({ state, expected }) => {
    const data = getIsMoreAge(state);
    expect(data).toEqual(expected);
  });
});

describe('List Selectors getSecretBoxPriceModalVisible', () => {
  const mockStateMap = [
    {
      state: {
        List: {
          priceSummaryModalData: {
            secretBoxPriceModalVisible: true,
          },
        },
      },
      expected: true,
    },
    {
      state: {
        List: {
          priceSummaryModalData: {
            secretBoxPriceModalVisible: false,
          },
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getSecretBoxPriceModalVisible check',
    ({ state, expected }) => {
      const data = getSecretBoxPriceModalVisible(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('List Selectors getSecretBoxModalVisible', () => {
  const mockStateMap = [
    {
      state: {
        List: {
          secretBoxModalVisible: true,
        },
      },
      expected: true,
    },
    {
      state: {
        List: {
          secretBoxModalVisible: false,
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getSecretBoxModalVisible check',
    ({ state, expected }) => {
      const data = getSecretBoxModalVisible(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('List Selectors getSecretBoxModalData', () => {
  const secretBoxModalData = {
    vehicleCode: 1011,
    minTPrice: 128,
    minDPrice: 78,
    isOptim: true,
    isEasy: true,
    pTag: [],
    outTags: [],
    priceSize: 2,
    minDOrinPrice: 98,
    isCredit: false,
    productRef: {
      license: '沪牌',
    },
    type: 1,
  };
  const mockStateMap = [
    {
      state: {
        List: {
          secretBoxModalData,
        },
      },
      expected: secretBoxModalData,
    },
    {
      state: {
        List: {
          secretBoxModalData: {},
        },
      },
      expected: {},
    },
  ];
  test.each(mockStateMap)(
    'getSecretBoxModalData check',
    ({ state, expected }) => {
      const data = getSecretBoxModalData(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('List Selectors getLicenseModalData', () => {
  const licenseModalData = {
    visible: true,
    data: '因商业条款限制，持非中国（含港澳台）驾照无法预订。建议您改为持有中国驾照的同伴进行取车或前往Trip下单',
    title: '驾照政策',
  };
  const mockStateMap = [
    {
      state: {
        List: {
          licenseModalData,
        },
      },
      expected: licenseModalData,
    },
    {
      state: {
        List: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getLicenseModalData check',
    ({ state, expected }) => {
      const data = getLicenseModalData(state);
      expect(data).toEqual(expected);
    },
  );
});
