import LogKeyDev from '../../../src/pages/xcar/Constants/LogKeyDev';
import {
  LogPriceExposureOfMarketing,
  getLogInfo,
  getMarketingPriceInfo,
} from '../../../src/pages/xcar/State/List/UBTLog';
import AppContext from '../../../src/pages/xcar/Util/AppContext';
import CarLog from '../../../src/pages/xcar/Util/CarLog';

describe('List/UBTLog', () => {
  test('LogPriceExposureOfMarketing', () => {
    AppContext.setRouterListLoader({ vehicleDayPrice: '199' });
    LogPriceExposureOfMarketing(null);
    expect(CarLog.LogTraceDev).toHaveBeenCalledTimes(0);

    const res = { productGroups: [{ productList: [{ productTopInfo: 0 }] }] };
    LogPriceExposureOfMarketing(res);
    expect(CarLog.LogTraceDev).toHaveBeenCalledTimes(0);

    const res2 = {
      productGroups: [{ productList: [{ productTopInfo: 1, minDPrice: 100 }] }],
    };
    LogPriceExposureOfMarketing(res2);
    const logInfo = getMarketingPriceInfo(res2);
    expect(CarLog.LogTraceDev).toHaveBeenCalledTimes(1);
    expect(logInfo).toEqual({
      key: LogKeyDev.c_car_dev_market_price,
      info: {
        eventResult: Number(199) === Number(100),
        extraData: {
          marketVehicleId: undefined,
          marketVehicleDayPrice: Number(199),
          listMinDPrice: Number(100),
        },
      },
    });

    const res3 = {
      productGroups: [{ productList: [{ productTopInfo: 1, minDPrice: 199 }] }],
    };
    LogPriceExposureOfMarketing(res2);
    const logInfo3 = getMarketingPriceInfo(res3);
    expect(CarLog.LogTraceDev).toHaveBeenCalledTimes(2);
    expect(logInfo3).toEqual({
      key: LogKeyDev.c_car_dev_market_price,
      info: {
        eventResult: Number(199) === Number(199),
        extraData: {
          marketVehicleId: undefined,
          marketVehicleDayPrice: Number(199),
          listMinDPrice: Number(199),
        },
      },
    });
  });
});

describe('List/getLogInfo', () => {
  const param = {
    adultNumbers: 2,
    age: 30,
    childrenNumbers: 1,
    modify: null,
    requestId: 'b26bc65c-961f-4c64-be0c-ee21952149e6',
    searchType: 1,
    vendorGroup: 1,
  };
  const recommendInfo = {
    promptTitle: '暂无符合要求的车辆',
    buttonTitle: '修改取还车时间或地点',
    errorCode: 'unknown',
    type: 3,
    promptSubTitle: '建议您修改取还车条件',
  };
  const res = {
    baseResponse: {
      extMap: {
        end: '2023-05-19 17:47:20',
        allCost: '684.0',
        start: '2023-05-19 17:47:20',
        dropoffCityId: '59',
        dataConvertResCost: '0.0',
        apiCost: '684.0',
        pageName: 'List',
        restCost: '1',
        pickupCityId: '59',
        shennongType: '[18192, 18154]',
      },
      errorCode: '20011',
      hasResult: false,
      code: '200',
      apiResCodes: [],
      returnMsg: 'No available stores in pickup area ; ',
      message: 'No available stores in pickup area ; ',
      requestId: 'b26bc65c-961f-4c64-be0c-ee21952149e6',
      isSuccess: false,
    },
    recommendInfo,
  };
  const isFromCache = false;
  const vehCodeList = [];
  const groupCodeList = [];
  const vehicleGroupList = [];
  const vehCount = 0;
  const pPricecount = 0;
  const pNormalcount = 0;
  const pEasylifecount = 0;
  const pMaskvehiclecount = 0;
  const pZhimaCount = 0;
  const vendorNames = '';
  const vendorCount = 0;
  const mockData = [
    {
      data: {
        param,
        res,
        isFromCache,
      },
      expected: {
        eventResult: false,
        request: JSON.stringify(param),
        serverErrorCode:
          (res && res.baseResponse && res.baseResponse.errorCode) || '',
        serverErrorMsg:
          (res && res.baseResponse && res.baseResponse.message) || '',
        vehicleList: vehCodeList.toString(),
        vehicleCount: `${vehCount}`,
        groupList: groupCodeList.toString(),
        priceCount: `${pPricecount}`,
        normalCount: `${pNormalcount}`,
        easyLifeCount: `${pEasylifecount}`,
        maskVehicleCount: `${pMaskvehiclecount}`,
        zhimaCount: `${pZhimaCount}`,
        vendorNames: vendorNames.toString(),
        vendorCount: `${vendorCount}`,
        vehicleGroupList: vehicleGroupList.toString(),
        recommendInfo: JSON.stringify(recommendInfo),
        isFromCache,
        clientCode: '',
        clientLog: JSON.stringify(res.baseResponse),
        resCode: res?.baseResponse?.code,
        userType: '[18192, 18154]',
        networkType: 'WIFI',
        isNetworkError: false
      },
    },
  ];

  test.each(mockData)('getLogInfo check', ({ data, expected }) => {
    const res: any = getLogInfo(data.param, data.res, data.isFromCache);
    expect(res).toEqual(expected);
  });
});