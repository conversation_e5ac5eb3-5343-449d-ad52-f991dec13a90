import { verifyPointInfo } from '../../../src/pages/xcar/State/List/Method';

describe('List Method Test', () => {
  describe('List Method verifyPointInfo', () => {
    const pointInfoMock_1 = {
      cityId: 43,
      date: '2022-11-25 10:00:00',
      locationCode: '',
      locationName: '凤凰国际机场T1航站楼',
      locationType: '1',
      poi: { latitude: 18.30747, longitude: 109.41201, radius: 0 },
      pickupOnDoor: 0,
      dropOffOnDoor: 0,
    };

    const pointInfoMock_2 = {
      cityId: 43,
      date: '2022-11-25 10:00:00',
      locationCode: '',
      locationName: '凤凰国际机场T1航站楼',
      locationType: '',
      poi: { latitude: 18.30747, longitude: 109.41201, radius: 0 },
      pickupOnDoor: 0,
      dropOffOnDoor: 0,
    };

    test('pointInfo validate true', () => {
      const result = verifyPointInfo(pointInfoMock_1);
      expect(result).toEqual(true);
    });

    test('pointInfo validate false', () => {
      const result = verifyPointInfo(pointInfoMock_2);
      expect(result).toEqual(true);
    });
  });
});
