import { icon } from '../../../src/pages/xcar/Common/src/Tokens';
import {
  getVendorPriceListByVehicleCode,
  getVehicleInfoByCode,
  getSecretBoxGroupLabel,
  getSecretBoxSecondLabels,
  getSecretBoxItemTagsAndPrice,
  getSecretBoxFirstScreenParamToBooking,
  getNationalChainTag,
  getVendorListEnterData,
  getMergeVehicleExtendInfo,
  getNewPriceGroup,
} from '../../../src/pages/xcar/State/List/VehicleListMappers';
import { ListReqAndResData } from '../../../src/pages/xcar/Global/Cache/Index';
import { LabelsType } from '../../../src/pages/xcar/Types/Dto/QueryVehicleDetailListResponseType';
import { initializeStore } from '../../../src/pages/xcar/State/Store';
import Utils from '../../../src/pages/xcar/Util/Utils';

jest.mock('../../../src/pages/xcar/Global/Cache/ListResSelectors', () => {
  const originalModule = jest.requireActual(
    '../../../src/pages/xcar/Global/Cache/ListResSelectors',
  );
  return {
    __esModule: true,
    ...originalModule,
    getBaseResData: jest.fn(() => {}),
    getBaseProductGroups: jest.fn(() => [
      {
        groupCode: '123',
        productList: [
          {
            vehicleCode: '123',
            productTopInfo: 'test',
            vendorPriceList: [],
          },
        ],
      },
    ]),
    getCurProductGroupId: jest.fn(() => '123'),
  };
});

describe('List Selectors Test', () => {
  const vehicleCode = '123';
  const productTopInfo = 'test';
  const activeGroupId = '1111';
  const curProductList = [
    {
      vehicleCode,
      productTopInfo,
      productList: [
        {
          vehicleCode,
          productTopInfo,
        },
      ],
      vendorPriceList: [],
    },
  ];
  const mockStateMap = [
    {
      data: {
        vehicleCode,
        productTopInfo,
        activeGroupId,
        curProductList,
      },
      expected: [],
    },
    {
      data: {
        vehicleCode,
        productTopInfo,
        activeGroupId,
        curProductList: false,
      },
      expected: undefined,
    },
    {
      data: {
        vehicleCode,
        productTopInfo,
        activeGroupId: false,
        curProductList: false,
      },
      expected: [],
    },
    {
      data: {
        vehicleCode,
        productTopInfo,
        activeGroupId: false,
      },
      expected: [],
    },
  ];
  test.each(mockStateMap)(
    'getVendorPriceListByVehicleCode 参数 check',
    ({ data, expected }) => {
      const res: any = getVendorPriceListByVehicleCode(
        data.vehicleCode,
        data.productTopInfo,
        data.activeGroupId,
        data.curProductList,
      );
      expect(res).toEqual(expected);
    },
  );
});

describe('getVehicleInfoByCode', () => {
  const mockStateMap = [
    {
      data: {
        vehicleCode: '5283',
        vehicleList: [
          {
            brandEName: '别克',
            brandName: '别克',
            name: '别克GL8',
            zhName: '别克GL8',
            vehicleCode: '5283',
            groupCode: '4',
            groupSubClassCode: '',
            groupName: '商务车',
            transmissionType: 1,
            transmissionName: '自动挡',
            passengerNo: 7,
            doorNo: 5,
            luggageNo: 3,
            displacement: '2.0T',
            fuel: '95号',
            driveMode: '前置前驱',
            style: '2021款',
            imageList: [
              'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
            ],
            isSpecialized: true,
            isHot: false,
            vehicleAccessoryImages: [
              'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
              'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
              'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
            ],
            license: '外牌',
            licenseStyle: '6',
            realityImageUrl:
              'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
            oilType: 3,
            luggageNum: '可放3个24寸行李箱',
            endurance: '续航100km',
            autoPark: false,
            carPhone: true,
            autoStart: true,
            vr: '',
            vehiclesSetId: '44',
          },
        ],
        productRef: {
          license: '沪牌',
          licenseStyle: '2',
          licenseTag: '沪牌',
        },
      },
      expected: {
        brandEName: '别克',
        brandName: '别克',
        name: '别克GL8',
        zhName: '别克GL8',
        vehicleCode: '5283',
        groupCode: '4',
        groupSubClassCode: '',
        groupName: '商务车',
        transmissionType: 1,
        transmissionName: '自动挡',
        passengerNo: 7,
        doorNo: 5,
        luggageNo: 3,
        displacement: '2.0T',
        fuel: '95号',
        driveMode: '前置前驱',
        style: '2021款',
        imageList: [
          'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
        ],
        isSpecialized: true,
        isHot: false,
        vehicleAccessoryImages: [
          'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
          'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
          'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
        ],
        license: '沪牌',
        licenseStyle: '2',
        realityImageUrl:
          'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
        oilType: 3,
        luggageNum: '可放3个24寸行李箱',
        endurance: '续航100km',
        autoPark: false,
        carPhone: true,
        autoStart: true,
        vr: '',
        vehiclesSetId: '44',
      },
    },
    {
      data: {
        vehicleCode: '5282',
        vehicleList: [
          {
            brandEName: '别克',
            brandName: '别克',
            name: '别克GL8',
            zhName: '别克GL8',
            vehicleCode: '5283',
            groupCode: '4',
            groupSubClassCode: '',
            groupName: '商务车',
            transmissionType: 1,
            transmissionName: '自动挡',
            passengerNo: 7,
            doorNo: 5,
            luggageNo: 3,
            displacement: '2.0T',
            fuel: '95号',
            driveMode: '前置前驱',
            style: '2021款',
            imageList: [
              'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
            ],
            isSpecialized: true,
            isHot: false,
            vehicleAccessoryImages: [
              'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
              'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
              'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
            ],
            license: '外牌',
            licenseStyle: '6',
            realityImageUrl:
              'https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg',
            oilType: 3,
            luggageNum: '可放3个24寸行李箱',
            endurance: '续航100km',
            autoPark: false,
            carPhone: true,
            autoStart: true,
            vr: '',
            vehiclesSetId: '44',
          },
        ],
        productRef: {
          license: '沪牌',
          licenseStyle: '2',
          licenseTag: '沪牌',
        },
      },
      expected: null,
    },
  ];
  test.each(mockStateMap)(
    'getVehicleInfoByCode 参数 check',
    ({ data, expected }) => {
      const res: any = getVehicleInfoByCode(
        data.vehicleCode,
        data.vehicleList,
        data.productRef,
      );
      expect(res).toEqual(expected);
    },
  );
});

describe('getSecretBoxGroupLabel', () => {
  const mockStateMap = [
    {
      data: ['经济轿车', '舒适轿车', 'SUV'],
      expected: [
        {
          text: '可选',
          icon: {
            iconContent: '',
          },
        },
        {
          text: '经济轿车 舒适轿车 SUV',
          icon: {
            iconContent: icon.car,
          },
        },
      ],
    },
    {
      data: ['经济轿车'],
      expected: [
        {
          text: '经济轿车',
          icon: {
            iconContent: icon.car,
          },
        },
      ],
    },
    {
      data: undefined,
      expected: [],
    },
  ];
  test.each(mockStateMap)('getSecretBoxGroupLabel', ({ data, expected }) => {
    const res: any = getSecretBoxGroupLabel(data);
    expect(res).toEqual(expected);
  });
});

describe('getSecretBoxSecondLabels', () => {
  const mockStateMap = [
    {
      data: {
        vehicle: {
          transmissionName: '自动挡',
          transmissionType: 1,
        },
        passengerNoRange: '5-7',
      },
      expected: [
        {
          text: '5-7座',
          icon: {
            iconContent: icon.seat,
          },
        },
        {
          text: '自动挡',
          icon: {
            iconContent: icon.circleAFilled,
          },
          type: 'displacement',
        },
      ],
    },
    {
      data: {
        vehicle: {
          transmissionName: '自动挡',
          transmissionType: 1,
        },
        passengerNoRange: undefined,
      },
      expected: [],
    },
  ];
  test.each(mockStateMap)('getSecretBoxSecondLabels', ({ data, expected }) => {
    const res: any = getSecretBoxSecondLabels(
      data.vehicle,
      data.passengerNoRange,
    );
    expect(res).toEqual(expected);
  });
});

describe('getSecretBoxItemTagsAndPrice', () => {
  const vendorPrice = {
    easyLifeInfo: {
      isEasyLife: true,
    },
    allTags: [
      {
        category: 2,
        sortNum: 10,
        mergeId: 0,
        groupCode: 'MarketGroup1347',
        code: '7',
        title: '免押金',
        colorCode: '2',
        type: 1,
        groupId: 3,
        labelCode: '3746',
      },
      {
        category: 1,
        sortNum: 25,
        mergeId: 1,
        groupCode: 'MarketGroup1336',
        code: '8',
        title: '免费取消',
        colorCode: '2',
        type: 1,
        groupId: 3,
        labelCode: '3563',
      },
      {
        category: 2,
        sortNum: 10000,
        mergeId: 1,
        groupCode: 'MarketGroup1325',
        code: '6',
        title: '满油取车',
        colorCode: '1',
        type: 1,
        groupId: 3,
        labelCode: '3788',
      },
      {
        category: 3,
        sortNum: 10000,
        amountTitle: '共减65',
        groupCode: 'MarketGroup1312',
        code: '30',
        title: '盲盒测试+券',
        colorCode: '15',
        mergeId: 0,
        type: 3,
        groupId: 1,
        labelCode: '3836',
      },
    ],
    pStoreRouteDesc: '店员免费上门送取车',
    decorateVehicleName: '盲盒·SUV',
    uniqueCode: '61fd1e6a752746cdb5ef6f56aea077b2',
    vehicleGroup: 6,
    vehicleScopeDesc:
      '随机分配SUV 车型（别克昂科拉、雪佛兰沃兰多、福特锐界等）',
    priceInfo: {
      oTPrice: 353,
      naked: true,
      priceVersion:
        'SH-PRICEVERSION_MTA2ODcxXzUwNDZfMV8xNzhfMTc4XzE3OF8zNTMuMDBfMTU4LjBfMzMzLjBfMV8xXzAuMF8wLjBfNDAuMDBfMzUuMDBfNTAuMDBfNTAuMDBfMTg1ODg3OQ==',
      deductInfos: [
        {
          totalAmount: 54,
          payofftype: 2,
        },
      ],
      currentOriginalDailyPrice: 178,
      priceType: 1,
      currentDailyPrice: 124,
      currentTotalPrice: 299,
      localCurrencyCode: 'CNY',
      marginPrice: 0,
      currentCurrencyCode: 'CNY',
    },
  };
  const mockStateMap = [
    {
      data: undefined,
      expected: null,
    },
    {
      data: vendorPrice,
      expected: {
        serviceTags: [
          {
            category: 1,
            sortNum: 25,
            mergeId: 1,
            groupCode: 'MarketGroup1336',
            code: '8',
            title: '免费取消·满油取车',
            colorCode: '14',
            type: 1,
            groupId: 3,
            labelCode: '3563',
          },
          {
            category: 2,
            sortNum: 10,
            mergeId: 0,
            groupCode: 'MarketGroup1347',
            code: '7',
            title: '免押金',
            colorCode: '2',
            type: 1,
            groupId: 3,
            labelCode: '3746',
          },
        ],
        marketTags: [
          {
            category: 3,
            sortNum: 10000,
            amountTitle: '共减65',
            groupCode: 'MarketGroup1312',
            code: '30',
            title: '盲盒测试+券',
            colorCode: '15',
            mergeId: 0,
            type: 3,
            groupId: 1,
            labelCode: '3836',
          },
        ],
        currentDailyPrice: vendorPrice?.priceInfo?.currentDailyPrice,
        currentTotalPrice: vendorPrice?.priceInfo?.currentTotalPrice,
        originPrice: {
          price: vendorPrice?.priceInfo?.currentOriginalDailyPrice,
        },
      },
    },
  ];
  initializeStore();
  test.each(mockStateMap)(
    'getSecretBoxItemTagsAndPrice',
    ({ data, expected }) => {
      const res: any = getSecretBoxItemTagsAndPrice(data);
      expect(res).toEqual(expected);
    },
  );
});

describe('getSecretBoxFirstScreenParamToBooking', () => {
  const allTags: LabelsType[] | [] = [
    {
      category: 2,
      sortNum: 10,
      mergeId: 0,
      groupCode: 'MarketGroup1347',
      code: '7',
      title: '免押金',
      colorCode: '2',
      type: 1,
      groupId: 3,
      labelCode: '3746',
    },
    {
      category: 1,
      sortNum: 25,
      mergeId: 1,
      groupCode: 'MarketGroup1336',
      code: '8',
      title: '免费取消',
      colorCode: '2',
      type: 1,
      groupId: 3,
      labelCode: '3563',
    },
    {
      category: 2,
      sortNum: 10000,
      mergeId: 1,
      groupCode: 'MarketGroup1325',
      code: '6',
      title: '满油取车',
      colorCode: '1',
      type: 1,
      groupId: 3,
      labelCode: '3788',
    },
    {
      category: 3,
      sortNum: 10000,
      amountTitle: '共减65',
      groupCode: 'MarketGroup1312',
      code: '30',
      title: '盲盒测试+券',
      colorCode: '15',
      mergeId: 0,
      type: 3,
      groupId: 1,
      labelCode: '3836',
    },
  ];
  const priceInfo = {
    allTags,
    vendorName: '不指定供应商',
    easyLifeInfo: {
      isEasyLife: true,
    },
    isSelect: true,
  };

  const mockStateMap = [
    {
      data: undefined,
      expected: {
        vendorInfo: {
          vendorName: undefined,
        },
        isSelect: undefined,
        isEasyLife: undefined,
        nationalChainTagTitle: undefined,
        allTags: {
          vehicleTagList: undefined,
        },
      },
    },
    {
      data: priceInfo,
      expected: {
        vendorInfo: {
          vendorName: priceInfo?.vendorName,
        },
        isSelect: priceInfo?.isSelect,
        isEasyLife: priceInfo?.easyLifeInfo?.isEasyLife,
        nationalChainTagTitle: getNationalChainTag(priceInfo?.allTags)?.title,
        allTags: {
          vehicleTagList: priceInfo?.allTags?.filter(
            flex => flex?.groupId === 2,
          ),
        },
      },
    },
  ];

  test.each(mockStateMap)(
    'getSecretBoxFirstScreenParamToBooking',
    ({ data, expected }) => {
      const res: any = getSecretBoxFirstScreenParamToBooking(data);
      expect(res).toEqual(expected);
    },
  );
});

describe('getVendorListEnterData', () => {
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
  jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
  const product = {
    vehicleCode: '10285',
    isSpecialized: false,
    vendorPriceList: [
      {
        vendorName: 'Avis',
        vendorLogo:
          '//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png',
        evaluation: {
          title: '',
          type: 1,
        },
        reference: {},
        isRStoreSupportCdl: true,
        rStoreRouteDesc: '',
        qualityScore: 0,
        pStoreSortDesc: '门店1',
        storeScore: 3.5,
        isPStoreSupportCdl: true,
        rStoreSortDesc: '门店1',
        sortNum: 71.7441,
        allTags: [
          {
            category: 1,
            sortNum: 10,
            code: '2',
            title: '立即确认',
            colorCode: '8',
            type: 1,
            description:
              '预订此产品后将立即确认订单并在取车时间为您安排好车辆。',
          },
          {
            category: 1,
            sortNum: 25,
            code: '2',
            title: '免费取消',
            colorCode: '8',
            type: 1,
            description:
              '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
          },
          {
            category: 2,
            sortNum: 56,
            code: '2',
            title: '随时可订',
            colorCode: '2',
            type: 1,
            description: '该车型随时可预订。',
          },
          {
            category: 2,
            sortNum: 60,
            code: '2',
            title: '24h营业',
            colorCode: '2',
            type: 1,
            description: '门店24小时营业。',
          },
          {
            category: 2,
            sortNum: 80,
            code: '2',
            title: '满油取还',
            colorCode: '2',
            type: 1,
            description:
              '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
          },
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '精选特惠',
            colorCode: '3',
            type: 3,
            description:
              '预订带有“精选特惠”的标签产品，享受车辆租金优惠，显示价格为优惠后价格。',
          },
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '券减¥76',
            colorCode: '3',
            type: 3,
            description: '券减¥76,可享立减￥76/天（1天共减￥76）的优惠。',
          },
        ],
        isSpecialized: false,
        easyLifeInfo: {
          isEasyLife: false,
        },
        commentInfo: {
          commentCount: 512,
          maximumRating: 5,
          commentLabel: '',
          level: '',
          hasComment: 1,
          overallRating: '3.5',
        },
        distance: 2.2874,
        pStoreRouteDesc: '机场内，可搭乘免费巴士到达',
        platformCode: '0',
        reactId: '174252606211',
        priceInfo: {
          currentTotalPrice: 691,
          localCurrencyCode: 'USD',
          deductInfos: [
            {
              totalAmount: 76,
              dayAmount: 76,
              payofftype: 2,
            },
          ],
          currentOriginalDailyPrice: 767,
          currentDailyPrice: 691,
          currentCurrencyCode: 'CNY',
        },
        isBroker: false,
        isSelect: false,
        vendorTag: {
          title: '国际知名',
          sortNum: 0,
        },
        orignalPriceStyle: 'WithOutStrikethrough',
      },
      {
        vendorName: 'Budget',
        vendorLogo:
          '//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/budget_2020.png',
        evaluation: {
          title: '',
          type: 1,
        },
        isRStoreSupportCdl: true,
        rStoreRouteDesc: '',
        qualityScore: 0,
        isPStoreSupportCdl: true,
        sortNum: 68.401,
        allTags: [
          {
            category: 1,
            sortNum: 10,
            code: '2',
            title: '1小时内确认',
            colorCode: '1',
            type: 1,
            description: '预订此产品后供应商将在1小时内确认订单',
          },
          {
            category: 1,
            sortNum: 25,
            code: '2',
            title: '免费取消',
            colorCode: '8',
            type: 1,
            description:
              '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
          },
          {
            category: 2,
            sortNum: 60,
            code: '2',
            title: '24h营业',
            colorCode: '2',
            type: 1,
            description: '门店24小时营业。',
          },
          {
            category: 2,
            sortNum: 80,
            code: '2',
            title: '满油取还',
            colorCode: '2',
            type: 1,
            description:
              '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
          },
          {
            category: 2,
            sortNum: 85,
            code: '2',
            title: '不限里程',
            colorCode: '2',
            type: 1,
            description: '租期内没有公里数限制。',
          },
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '精选特惠',
            colorCode: '3',
            type: 3,
            description:
              '预订带有“精选特惠”的标签产品，享受车辆租金优惠，显示价格为优惠后价格。',
          },
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '券减¥76',
            colorCode: '3',
            type: 3,
            description: '券减¥76,可享立减￥76/天（1天共减￥76）的优惠。',
          },
        ],
        reference: {},
        isSpecialized: false,
        easyLifeInfo: {
          isEasyLife: false,
        },
        commentInfo: {
          commentCount: 0,
          maximumRating: 5,
          commentLabel: '',
          level: '',
          hasComment: 0,
          overallRating: '0.0',
        },
        distance: 2.1317,
        pStoreRouteDesc: '机场内，可搭乘免费巴士到达',
        platformCode: '0',
        reactId: '174252687260',
        priceInfo: {
          currentTotalPrice: 687,
          localCurrencyCode: 'USD',
          deductInfos: [
            {
              totalAmount: 76,
              dayAmount: 76,
              payofftype: 2,
            },
          ],
          currentOriginalDailyPrice: 763,
          currentDailyPrice: 687,
          currentCurrencyCode: 'CNY',
        },
        isBroker: false,
        isSelect: false,
        vendorTag: {
          title: '全球连锁',
          sortNum: 0,
        },
        orignalPriceStyle: 'WithOutStrikethrough',
      },
    ],
    minTPrice: 687,
    lowestPrice: 687,
  };
  const vehicleInfo = {
    vehicleCode: '10285',
    vehicleIndex: 23,
  };
  const expected = {
    hasOptimize: false,
    hasEasyLife: false,
    price: 687,
    minTotalPriceDesc: '总价',
    minTotalPriceOtherDesc: '起',
    currency: 'CNY',
    prefixDescText: '',
    depositFreeLabels: [],
    descText: '/天起',
    logoUrlList: [
      '//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png',
    ],
    vendorListDesc: '2个报价',
    tagInfo: null,
    hasMore: true,
    totalPriceLen: 2,
    vehicleCode: '10285',
    productRef: undefined,
    type: undefined,
    vendorList: [
      {
        diffFreeTag: undefined,
        reference: {},
        soldOutLabel: undefined,
        priceDescProps: {
          dayText: '/天',
          originPrice: {
            price: 767,
            currency: '¥',
          },
          dayPrice: {
            price: 691,
            currency: '¥',
          },
          undefined: undefined,
        },
        vendor: {
          vendorName: 'Avis',
          vendorLogo:
            '//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png',
          evaluation: {
            title: '',
            type: 1,
          },
          isRStoreSupportCdl: true,
          rStoreRouteDesc: '',
          qualityScore: 0,
          pStoreSortDesc: '门店1',
          storeScore: 3.5,
          isPStoreSupportCdl: true,
          rStoreSortDesc: '门店1',
          sortNum: 71.7441,
          allTags: [
            {
              category: 1,
              sortNum: 10,
              code: '2',
              title: '立即确认',
              colorCode: '8',
              type: 1,
              description:
                '预订此产品后将立即确认订单并在取车时间为您安排好车辆。',
            },
            {
              category: 1,
              sortNum: 25,
              code: '2',
              title: '免费取消',
              colorCode: '8',
              type: 1,
              description:
                '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
            },
            {
              category: 2,
              sortNum: 56,
              code: '2',
              title: '随时可订',
              colorCode: '2',
              type: 1,
              description: '该车型随时可预订。',
            },
            {
              category: 2,
              sortNum: 60,
              code: '2',
              title: '24h营业',
              colorCode: '2',
              type: 1,
              description: '门店24小时营业。',
            },
            {
              category: 2,
              sortNum: 80,
              code: '2',
              title: '满油取还',
              colorCode: '2',
              type: 1,
              description:
                '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
            },
            {
              category: 3,
              sortNum: 0,
              code: '3',
              title: '精选特惠',
              colorCode: '3',
              type: 3,
              description:
                '预订带有“精选特惠”的标签产品，享受车辆租金优惠，显示价格为优惠后价格。',
            },
            {
              category: 3,
              sortNum: 0,
              code: '3',
              title: '券减¥76',
              colorCode: '3',
              type: 3,
              description: '券减¥76,可享立减￥76/天（1天共减￥76）的优惠。',
            },
          ],
          isSpecialized: false,
          easyLifeInfo: {
            isEasyLife: false,
          },
          commentInfo: {
            commentCount: 512,
            maximumRating: 5,
            commentLabel: '',
            level: '',
            hasComment: 1,
            overallRating: '3.5',
          },
          distance: 2.2874,
          pStoreRouteDesc: '机场内，可搭乘免费巴士到达',
          platformCode: '0',
          reactId: '174252606211',
          reference: {},
          priceInfo: {
            currentTotalPrice: 691,
            localCurrencyCode: 'USD',
            deductInfos: [
              {
                totalAmount: 76,
                dayAmount: 76,
                payofftype: 2,
              },
            ],
            currentOriginalDailyPrice: 767,
            currentDailyPrice: 691,
            currentCurrencyCode: 'CNY',
          },
          isBroker: false,
          isSelect: false,
          vendorTag: {
            title: '国际知名',
            sortNum: 0,
          },
          orignalPriceStyle: 'WithOutStrikethrough',
        },
        vendorHeaderProps: {
          vendorLogo:
            '//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png',
          vendorName: 'Avis',
          title: '国际知名',
          scoreDesc: '',
          commentDesc: '512条评价',
          hasComment: 1,
          commentLabel: '',
          score: '3.5',
          totalScore: 5,
          scoreLow: false,
          isEasyLife: false,
          isSelect: false,
          isRentCenter: false,
          isFlagShip: false,
          isOptimize: false,
          nationalChainTag: undefined,
        },
        vehicleIndex: 23,
        vendorIndex: 0,
        vehicleCode: '10285',
        isCreditRent: false,
        tagsWithoutMarket: [
          {
            category: 1,
            sortNum: 10,
            code: '2',
            title: '立即确认',
            colorCode: '8',
            type: 1,
            description:
              '预订此产品后将立即确认订单并在取车时间为您安排好车辆。',
          },
          {
            category: 1,
            sortNum: 25,
            code: '2',
            title: '免费取消',
            colorCode: '8',
            type: 1,
            description:
              '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
          },
          {
            category: 2,
            sortNum: 56,
            code: '2',
            title: '随时可订',
            colorCode: '2',
            type: 1,
            description: '该车型随时可预订。',
          },
          {
            category: 2,
            sortNum: 60,
            code: '2',
            title: '24h营业',
            colorCode: '2',
            type: 1,
            description: '门店24小时营业。',
          },
          {
            category: 2,
            sortNum: 80,
            code: '2',
            title: '满油取还',
            colorCode: '2',
            type: 1,
            description:
              '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
          },
        ],
        marketLabels: [
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '精选特惠',
            colorCode: '3',
            type: 3,
            description:
              '预订带有“精选特惠”的标签产品，享受车辆租金优惠，显示价格为优惠后价格。',
          },
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '券减¥76',
            colorCode: '3',
            type: 3,
            description: '券减¥76,可享立减￥76/天（1天共减￥76）的优惠。',
          },
        ],
      },
      {
        priceDescProps: {
          dayText: '/天',
          originPrice: {
            price: 763,
            currency: '¥',
          },
          undefined: undefined,
          dayPrice: {
            price: 687,
            currency: '¥',
          },
        },
        reference: {},
        soldOutLabel: undefined,
        vendor: {
          vendorName: 'Budget',
          vendorLogo:
            '//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/budget_2020.png',
          evaluation: {
            title: '',
            type: 1,
          },
          isRStoreSupportCdl: true,
          rStoreRouteDesc: '',
          qualityScore: 0,
          isPStoreSupportCdl: true,
          sortNum: 68.401,
          allTags: [
            {
              category: 1,
              sortNum: 10,
              code: '2',
              title: '1小时内确认',
              colorCode: '1',
              type: 1,
              description: '预订此产品后供应商将在1小时内确认订单',
            },
            {
              category: 1,
              sortNum: 25,
              code: '2',
              title: '免费取消',
              colorCode: '8',
              type: 1,
              description:
                '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
            },
            {
              category: 2,
              sortNum: 60,
              code: '2',
              title: '24h营业',
              colorCode: '2',
              type: 1,
              description: '门店24小时营业。',
            },
            {
              category: 2,
              sortNum: 80,
              code: '2',
              title: '满油取还',
              colorCode: '2',
              type: 1,
              description:
                '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
            },
            {
              category: 2,
              sortNum: 85,
              code: '2',
              title: '不限里程',
              colorCode: '2',
              type: 1,
              description: '租期内没有公里数限制。',
            },
            {
              category: 3,
              sortNum: 0,
              code: '3',
              title: '精选特惠',
              colorCode: '3',
              type: 3,
              description:
                '预订带有“精选特惠”的标签产品，享受车辆租金优惠，显示价格为优惠后价格。',
            },
            {
              category: 3,
              sortNum: 0,
              code: '3',
              title: '券减¥76',
              colorCode: '3',
              type: 3,
              description: '券减¥76,可享立减￥76/天（1天共减￥76）的优惠。',
            },
          ],
          isSpecialized: false,
          easyLifeInfo: {
            isEasyLife: false,
          },
          commentInfo: {
            commentCount: 0,
            maximumRating: 5,
            commentLabel: '',
            level: '',
            hasComment: 0,
            overallRating: '0.0',
          },
          distance: 2.1317,
          pStoreRouteDesc: '机场内，可搭乘免费巴士到达',
          platformCode: '0',
          reactId: '174252687260',
          reference: {},
          priceInfo: {
            currentTotalPrice: 687,
            localCurrencyCode: 'USD',
            deductInfos: [
              {
                totalAmount: 76,
                dayAmount: 76,
                payofftype: 2,
              },
            ],
            currentOriginalDailyPrice: 763,
            currentDailyPrice: 687,
            currentCurrencyCode: 'CNY',
          },
          isBroker: false,
          isSelect: false,
          vendorTag: {
            title: '全球连锁',
            sortNum: 0,
          },
          orignalPriceStyle: 'WithOutStrikethrough',
        },
        vendorHeaderProps: {
          vendorLogo:
            '//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/budget_2020.png',
          vendorName: 'Budget',
          title: '全球连锁',
          scoreDesc: '',
          commentDesc: '',
          hasComment: 0,
          commentLabel: '',
          score: '0.0',
          totalScore: 5,
          scoreLow: false,
          isEasyLife: false,
          isSelect: false,
          isRentCenter: false,
          nationalChainTag: undefined,
          isFlagShip: false,
          isOptimize: false,
        },
        vehicleIndex: 23,
        vendorIndex: 1,
        vehicleCode: '10285',
        isCreditRent: false,
        diffFreeTag: undefined,
        tagsWithoutMarket: [
          {
            category: 1,
            sortNum: 10,
            code: '2',
            title: '1小时内确认',
            colorCode: '1',
            type: 1,
            description: '预订此产品后供应商将在1小时内确认订单',
          },
          {
            category: 1,
            sortNum: 25,
            code: '2',
            title: '免费取消',
            colorCode: '8',
            type: 1,
            description:
              '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
          },
          {
            category: 2,
            sortNum: 60,
            code: '2',
            title: '24h营业',
            colorCode: '2',
            type: 1,
            description: '门店24小时营业。',
          },
          {
            category: 2,
            sortNum: 80,
            code: '2',
            title: '满油取还',
            colorCode: '2',
            type: 1,
            description:
              '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
          },
          {
            category: 2,
            sortNum: 85,
            code: '2',
            title: '不限里程',
            colorCode: '2',
            type: 1,
            description: '租期内没有公里数限制。',
          },
        ],
        marketLabels: [
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '精选特惠',
            colorCode: '3',
            type: 3,
            description:
              '预订带有“精选特惠”的标签产品，享受车辆租金优惠，显示价格为优惠后价格。',
          },
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '券减¥76',
            colorCode: '3',
            type: 3,
            description: '券减¥76,可享立减￥76/天（1天共减￥76）的优惠。',
          },
        ],
      },
    ],
    allVendorPriceList: [
      {
        vendorName: 'Avis',
        vendorLogo:
          '//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png',
        evaluation: {
          title: '',
          type: 1,
        },
        isRStoreSupportCdl: true,
        rStoreRouteDesc: '',
        qualityScore: 0,
        pStoreSortDesc: '门店1',
        storeScore: 3.5,
        isPStoreSupportCdl: true,
        rStoreSortDesc: '门店1',
        sortNum: 71.7441,
        allTags: [
          {
            category: 1,
            sortNum: 10,
            code: '2',
            title: '立即确认',
            colorCode: '8',
            type: 1,
            description:
              '预订此产品后将立即确认订单并在取车时间为您安排好车辆。',
          },
          {
            category: 1,
            sortNum: 25,
            code: '2',
            title: '免费取消',
            colorCode: '8',
            type: 1,
            description:
              '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
          },
          {
            category: 2,
            sortNum: 56,
            code: '2',
            title: '随时可订',
            colorCode: '2',
            type: 1,
            description: '该车型随时可预订。',
          },
          {
            category: 2,
            sortNum: 60,
            code: '2',
            title: '24h营业',
            colorCode: '2',
            type: 1,
            description: '门店24小时营业。',
          },
          {
            category: 2,
            sortNum: 80,
            code: '2',
            title: '满油取还',
            colorCode: '2',
            type: 1,
            description:
              '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
          },
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '精选特惠',
            colorCode: '3',
            type: 3,
            description:
              '预订带有“精选特惠”的标签产品，享受车辆租金优惠，显示价格为优惠后价格。',
          },
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '券减¥76',
            colorCode: '3',
            type: 3,
            description: '券减¥76,可享立减￥76/天（1天共减￥76）的优惠。',
          },
        ],
        isSpecialized: false,
        easyLifeInfo: {
          isEasyLife: false,
        },
        commentInfo: {
          commentCount: 512,
          maximumRating: 5,
          commentLabel: '',
          level: '',
          hasComment: 1,
          overallRating: '3.5',
        },
        distance: 2.2874,
        pStoreRouteDesc: '机场内，可搭乘免费巴士到达',
        platformCode: '0',
        reactId: '174252606211',
        reference: {},
        priceInfo: {
          currentTotalPrice: 691,
          localCurrencyCode: 'USD',
          deductInfos: [
            {
              totalAmount: 76,
              dayAmount: 76,
              payofftype: 2,
            },
          ],
          currentOriginalDailyPrice: 767,
          currentDailyPrice: 691,
          currentCurrencyCode: 'CNY',
        },
        isBroker: false,
        isSelect: false,
        vendorTag: {
          title: '国际知名',
          sortNum: 0,
        },
        orignalPriceStyle: 'WithOutStrikethrough',
      },
      {
        vendorName: 'Budget',
        vendorLogo:
          '//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/budget_2020.png',
        evaluation: {
          title: '',
          type: 1,
        },
        isRStoreSupportCdl: true,
        rStoreRouteDesc: '',
        qualityScore: 0,
        isPStoreSupportCdl: true,
        sortNum: 68.401,
        allTags: [
          {
            category: 1,
            sortNum: 10,
            code: '2',
            title: '1小时内确认',
            colorCode: '1',
            type: 1,
            description: '预订此产品后供应商将在1小时内确认订单',
          },
          {
            category: 1,
            sortNum: 25,
            code: '2',
            title: '免费取消',
            colorCode: '8',
            type: 1,
            description:
              '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
          },
          {
            category: 2,
            sortNum: 60,
            code: '2',
            title: '24h营业',
            colorCode: '2',
            type: 1,
            description: '门店24小时营业。',
          },
          {
            category: 2,
            sortNum: 80,
            code: '2',
            title: '满油取还',
            colorCode: '2',
            type: 1,
            description:
              '取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。',
          },
          {
            category: 2,
            sortNum: 85,
            code: '2',
            title: '不限里程',
            colorCode: '2',
            type: 1,
            description: '租期内没有公里数限制。',
          },
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '精选特惠',
            colorCode: '3',
            type: 3,
            description:
              '预订带有“精选特惠”的标签产品，享受车辆租金优惠，显示价格为优惠后价格。',
          },
          {
            category: 3,
            sortNum: 0,
            code: '3',
            title: '券减¥76',
            colorCode: '3',
            type: 3,
            description: '券减¥76,可享立减￥76/天（1天共减￥76）的优惠。',
          },
        ],
        isSpecialized: false,
        easyLifeInfo: {
          isEasyLife: false,
        },
        commentInfo: {
          commentCount: 0,
          maximumRating: 5,
          commentLabel: '',
          level: '',
          hasComment: 0,
          overallRating: '0.0',
        },
        distance: 2.1317,
        pStoreRouteDesc: '机场内，可搭乘免费巴士到达',
        platformCode: '0',
        reactId: '174252687260',
        reference: {},
        priceInfo: {
          currentTotalPrice: 687,
          localCurrencyCode: 'USD',
          deductInfos: [
            {
              totalAmount: 76,
              dayAmount: 76,
              payofftype: 2,
            },
          ],
          currentOriginalDailyPrice: 763,
          currentDailyPrice: 687,
          currentCurrencyCode: 'CNY',
        },
        isBroker: false,
        isSelect: false,
        vendorTag: {
          title: '全球连锁',
          sortNum: 0,
        },
        orignalPriceStyle: 'WithOutStrikethrough',
      },
    ],
    restAssuredTag: null,
    firstVendor: null,
    discountLabels: [
      {
        category: 3,
        sortNum: 0,
        code: '3',
        title: '精选特惠',
        colorCode: '3',
        type: 3,
        description:
          '预订带有“精选特惠”的标签产品，享受车辆租金优惠，显示价格为优惠后价格。',
      },
      {
        category: 3,
        sortNum: 0,
        code: '3',
        title: '券减¥76',
        colorCode: '3',
        type: 3,
        description: '券减¥76,可享立减￥76/天（1天共减￥76）的优惠。',
      },
    ],
    newOriginPrice: {
      price: 763,
      currency: '¥',
    },
    pickUpDesc: '',
  };
  const mockStateMap = [
    {
      data: {
        product,
        vehicleInfo,
      },
      expected,
    },
  ];

  test.each(mockStateMap)('getVendorListEnterData', ({ data, expected }) => {
    const res: any = getVendorListEnterData(data.product, data.vehicleInfo);
    expect(res).toEqual(expected);
  });
});

describe('getMergeVehicleExtendInfo', () => {
  const listRes = {
    mergeVehicleExtendInfos: [
      {
        group: 104883,
        extendType: 1,
        contents: [
          {
            stringObjs: [
              {
                content: '切克闹切克闹--隐藏特使文案',
              },
            ],
          },
        ],
      },
    ],
  };
  const mockStateMap1 = [
    {
      listRes,
      expected: {
        group: 104883,
        extendType: 1,
        contents: [
          {
            stringObjs: [
              {
                content: '切克闹切克闹--隐藏特使文案',
              },
            ],
          },
        ],
      },
    },
    {
      listRes: {},
      expected: {},
    },
  ];
  test.each(mockStateMap1)(
    'getMergeVehicleExtendInfo',
    ({ listRes, expected }) => {
      ListReqAndResData.setData(
        ListReqAndResData.keyList.listProductRes,
        listRes,
      );
      const result = getMergeVehicleExtendInfo(104883);
      expect(result).toMatchObject(expected);
    },
  );
  // getNewPriceGroup
  const listRes2 = {
    productGroups: [
      {
        productList: [
          {
            vehicleCode: '4883',
            sortNum: 9,
            lowestPrice: 441,
            highestPrice: 844,
            maximumRating: 5,
            maximumCommentCount: 3444,
            lowestDistance: 0,
            vendorPriceList: [
              {
                isMinTPriceVendor: true,
                reference: {
                  bizVendorCode: 'SD3864',
                  vendorCode: '30116',
                  pStoreCode: '106861',
                  rStoreCode: '106861',
                  vehicleCode: '0',
                  packageId: '',
                  packageType: 1,
                  comPriceCode:
                    '[c]MjIxfDE4NTd8MjAyMC4wMC0wMiAzLTEwMDowMDAwOjAmJjEkJjQ1MC0xMC0yMDIzMDowMDAzIDA0NTAmOjAwJjAyMy0mMSQyNCAwMDEwLTAwMCY0OjAwOjEkMjA1MCYmMC0wNTIzLTEwMDowIDAwOjAmJjEwJjQ1MDEmNCR8MTAmMTgwJjQ1MDAzJjEwJDEwMDAmMiYyMC4kMTAwMC4wMDg1LjAyJjQmMC4wMDAmMzQyMy0wJHwyMCAxODo5LTIyNwAAADMzOjE=',
                  priceVersion:
                    'SH-PRICEVERSION_MTA2ODYxXzQ4ODNfMV80NTBfMTgwMF80NTBfMjE2MC4wMF80NDEuMF8yMTI0LjBfMF8wXzAuMF8wLjBfMzQwLjAwXzIwLjAwXzAuMDBfMC4wMF8xODU3MjIx',
                  vendorVehicleCode: '20076508',
                  sendTypeForPickUpCar: 0,
                  sendTypeForPickOffCar: 0,
                  skuId: 1857221,
                  pLev: 22255,
                  rLev: 22255,
                  klbVersion: 1,
                },
                fees: [
                  {
                    code: 'CAR_RENTAL_FEE',
                    name: '车辆租金',
                    amount: 1764,
                    amountStr: '¥1764',
                    subAmount: 441,
                    subAmountStr: '日均¥441',
                    originalDailyPrice: 450,
                    detail: [
                      {
                        code: '1001',
                        name: '租车费',
                        amount: 1800,
                        amountDesc: '¥1800',
                      },
                      {
                        code: '3852',
                        name: '长租特惠',
                        amount: 36,
                        amountDesc: '¥36',
                      },
                    ],
                  },
                  {
                    code: 'CAR_SERVICE_FEE',
                    name: '服务/手续费',
                    amount: 20,
                    amountStr: '¥20',
                    detail: [
                      {
                        code: '1003',
                        name: '车行手续费',
                        amount: 20,
                        amountDesc: '¥20',
                        desc: '用于车辆清洁，车辆保养，单据制作，人员服务等',
                        showFree: false,
                      },
                    ],
                    currencyCode: '¥',
                  },
                  {
                    code: 'CAR_GUARANTEE_FEE',
                    name: '车行保障服务费',
                    amount: 340,
                    amountStr: '¥340',
                    detail: [
                      {
                        code: '1002',
                        name: '基础服务费',
                        amount: 340,
                        amountDesc: '¥340',
                        showFree: false,
                      },
                    ],
                    currencyCode: '¥',
                  },
                  {
                    code: '10001',
                    name: '总价',
                    amount: 2124,
                    amountStr: '¥2124',
                    subAmount: 2160,
                    subAmountStr: '¥2160',
                    currencyCode: '¥',
                  },
                ],
              },
              {
                reference: {
                  bizVendorCode: 'SD8812',
                  vendorCode: '71523',
                  pStoreCode: '107401',
                  vehicleCode: '0',
                  packageType: 1,
                  skuId: 1861829,
                },
              },
              {
                reference: {
                  bizVendorCode: 'SD7281',
                  vendorCode: '15000539',
                  pStoreCode: '153013',
                  vehicleCode: '0',
                  packageType: 1,
                  skuId: 3021067,
                },
              },
              {
                reference: {
                  bizVendorCode: 'SD7172',
                  vendorCode: '15000430',
                  pStoreCode: '137302',
                  vehicleCode: '0',
                  packageType: 1,
                  skuId: 2707862,
                },
              },
            ],
            reactId: '**********',
            group: 104883,
            groupSort: 1,
            scoreSort: 0,
            hot: 0,
            hotType: 0,
            hotScore: 0,
            vehicleRecommendProduct: {
              introduce: '当前车型最低价',
              productCodes: ['SD3864_0_106861_106861'],
            },
            minTPrice: 2124,
            minDPrice: 441,
            modifySameVehicle: false,
            minDOrinPrice: 450,
            outTags: [
              {
                title: '免押金',
                category: 2,
                type: 1,
                code: '7',
                sortNum: 10,
                colorCode: '2',
                labelCode: '3746',
                groupCode: 'MarketGroup1347',
                groupId: 3,
                mergeId: 0,
              },
            ],
            pTag: {
              title: '长租特惠',
              category: 3,
              type: 3,
              code: '30',
              sortNum: 10000,
              colorCode: '15',
              labelCode: '3852',
              groupCode: 'MarketGroup1384',
              amountTitle: '已减36',
              groupId: 1,
              mergeId: 0,
            },
            priceSize: 4,
            isEasy: true,
            isCredit: true,
            rCoup: 0,
            pWay: '可选：店员免费上门送取车',
            productRef: {
              license: '沪牌',
              licenseStyle: '2',
              licenseTag: '沪牌',
            },
          },
          {
            vehicleCode: '4883',
            sortNum: 9,
            lowestPrice: 488,
            highestPrice: 488,
            maximumRating: 4.8,
            maximumCommentCount: 2102,
            lowestDistance: 0,
            vendorPriceList: [
              {
                isMinTPriceVendor: true,
                reference: {
                  bizVendorCode: 'SD7089',
                  vendorCode: '13072',
                  pStoreCode: '137706',
                  rStoreCode: '137706',
                  vehicleCode: '0',
                  packageId: 'sec',
                  packageType: 1,
                  priceVersion:
                    'SH-PRICEVERSION_MTM3NzA2XzQ4ODNfMV80ODguMF8xOTUyLjBfMC4wXzIzNDcuMF80ODguMF8yMzQ3LjBfMF8wXzAuMF8wLjBfMzYwLjBfMzUuMF8wXzBfNTcyMDU2Mw==',
                  vendorVehicleCode: '2107_49537_pupai',
                  sendTypeForPickUpCar: 0,
                  sendTypeForPickOffCar: 0,
                  skuId: 5720563,
                  pLev: 83230,
                  rLev: 83230,
                  klbVersion: 1,
                },
                fees: [
                  {
                    code: 'CAR_RENTAL_FEE',
                    name: '车辆租金',
                    amount: 1952,
                    amountStr: '¥1952',
                    subAmount: 488,
                    subAmountStr: '日均¥488',
                    detail: [
                      {
                        code: '1001',
                        name: '租车费',
                        amount: 1952,
                        amountDesc: '¥1952',
                      },
                    ],
                  },
                  {
                    code: 'CAR_SERVICE_FEE',
                    name: '服务/手续费',
                    amount: 35,
                    amountStr: '¥35',
                    detail: [
                      {
                        code: '1003',
                        name: '车行手续费',
                        amount: 35,
                        amountDesc: '¥35',
                        desc: '用于车辆清洁，车辆保养，单据制作，人员服务等',
                        showFree: false,
                      },
                    ],
                    currencyCode: '¥',
                  },
                  {
                    code: 'CAR_GUARANTEE_FEE',
                    name: '车行保障服务费',
                    amount: 360,
                    amountStr: '¥360',
                    detail: [
                      {
                        code: '1002',
                        name: '基础服务费',
                        amount: 360,
                        amountDesc: '¥360',
                        showFree: false,
                      },
                    ],
                    currencyCode: '¥',
                  },
                  {
                    code: '10001',
                    name: '总价',
                    amount: 2347,
                    amountStr: '¥2347',
                    subAmount: 2347,
                    subAmountStr: '¥2347',
                    currencyCode: '¥',
                  },
                ],
              },
            ],
            reactId: '**********',
            group: 104883,
            groupSort: 2,
            scoreSort: 0,
            hot: 0,
            hotType: 0,
            hotScore: 0,
            vehicleRecommendProduct: {
              introduce: '当前车型最低价',
              productCodes: ['SD7089_0_137706_137706'],
            },
            minTPrice: 2347,
            minDPrice: 488,
            modifySameVehicle: false,
            minDOrinPrice: 0,
            outTags: [
              {
                title: '免押金',
                category: 2,
                type: 1,
                code: '7',
                sortNum: 10,
                colorCode: '2',
                labelCode: '3746',
                groupCode: 'MarketGroup1347',
                groupId: 3,
                mergeId: 0,
              },
            ],
            priceSize: 1,
            isEasy: true,
            isCredit: true,
            rCoup: 0,
            pWay: '可选：店员免费上门送取车',
            productRef: {
              license: '外牌',
              licenseStyle: '6',
              licenseTag: '',
            },
            showType: 1,
          },
        ],
      },
    ],
  };
  const mockStateMap2 = [
    {
      listRes,
      expected: {
        foldBtnContent: '切克闹切克闹--隐藏特使文案',
        needFold: true,
        priceGroup: [
          {
            group: 104883,
            groupSort: 1,
            highestPrice: 844,
            hot: 0,
            hotScore: 0,
            hotType: 0,
            isCredit: true,
            isEasy: true,
            lowestDistance: 0,
            lowestPrice: 441,
            maximumCommentCount: 3444,
            maximumRating: 5,
            minDOrinPrice: 450,
            minDPrice: 441,
            minTPrice: 2124,
            modifySameVehicle: false,
            outTags: [
              {
                category: 2,
                code: '7',
                colorCode: '2',
                groupCode: 'MarketGroup1347',
                groupId: 3,
                labelCode: '3746',
                mergeId: 0,
                sortNum: 10,
                title: '免押金',
                type: 1,
              },
            ],
            pTag: {
              amountTitle: '已减36',
              category: 3,
              code: '30',
              colorCode: '15',
              groupCode: 'MarketGroup1384',
              groupId: 1,
              labelCode: '3852',
              mergeId: 0,
              sortNum: 10000,
              title: '长租特惠',
              type: 3,
            },
            pWay: '可选：店员免费上门送取车',
            priceSize: 4,
            productRef: {
              license: '沪牌',
              licenseStyle: '2',
              licenseTag: '沪牌',
            },
            rCoup: 0,
            reactId: '**********',
            scoreSort: 0,
            sortNum: 9,
            vehicleCode: '4883',
            vehicleRecommendProduct: {
              introduce: '当前车型最低价',
              productCodes: ['SD3864_0_106861_106861'],
            },
            vendorPriceList: [
              {
                fees: [
                  {
                    amount: 1764,
                    amountStr: '¥1764',
                    code: 'CAR_RENTAL_FEE',
                    detail: [
                      {
                        amount: 1800,
                        amountDesc: '¥1800',
                        code: '1001',
                        name: '租车费',
                      },
                      {
                        amount: 36,
                        amountDesc: '¥36',
                        code: '3852',
                        name: '长租特惠',
                      },
                    ],
                    name: '车辆租金',
                    originalDailyPrice: 450,
                    subAmount: 441,
                    subAmountStr: '日均¥441',
                  },
                  {
                    amount: 20,
                    amountStr: '¥20',
                    code: 'CAR_SERVICE_FEE',
                    currencyCode: '¥',
                    detail: [
                      {
                        amount: 20,
                        amountDesc: '¥20',
                        code: '1003',
                        desc: '用于车辆清洁，车辆保养，单据制作，人员服务等',
                        name: '车行手续费',
                        showFree: false,
                      },
                    ],
                    name: '服务/手续费',
                  },
                  {
                    amount: 340,
                    amountStr: '¥340',
                    code: 'CAR_GUARANTEE_FEE',
                    currencyCode: '¥',
                    detail: [
                      {
                        amount: 340,
                        amountDesc: '¥340',
                        code: '1002',
                        name: '基础服务费',
                        showFree: false,
                      },
                    ],
                    name: '车行保障服务费',
                  },
                  {
                    amount: 2124,
                    amountStr: '¥2124',
                    code: '10001',
                    currencyCode: '¥',
                    name: '总价',
                    subAmount: 2160,
                    subAmountStr: '¥2160',
                  },
                ],
                isMinTPriceVendor: true,
                reference: {
                  bizVendorCode: 'SD3864',
                  comPriceCode:
                    '[c]MjIxfDE4NTd8MjAyMC4wMC0wMiAzLTEwMDowMDAwOjAmJjEkJjQ1MC0xMC0yMDIzMDowMDAzIDA0NTAmOjAwJjAyMy0mMSQyNCAwMDEwLTAwMCY0OjAwOjEkMjA1MCYmMC0wNTIzLTEwMDowIDAwOjAmJjEwJjQ1MDEmNCR8MTAmMTgwJjQ1MDAzJjEwJDEwMDAmMiYyMC4kMTAwMC4wMDg1LjAyJjQmMC4wMDAmMzQyMy0wJHwyMCAxODo5LTIyNwAAADMzOjE=',
                  klbVersion: 1,
                  pLev: 22255,
                  pStoreCode: '106861',
                  packageId: '',
                  packageType: 1,
                  priceVersion:
                    'SH-PRICEVERSION_MTA2ODYxXzQ4ODNfMV80NTBfMTgwMF80NTBfMjE2MC4wMF80NDEuMF8yMTI0LjBfMF8wXzAuMF8wLjBfMzQwLjAwXzIwLjAwXzAuMDBfMC4wMF8xODU3MjIx',
                  rLev: 22255,
                  rStoreCode: '106861',
                  sendTypeForPickOffCar: 0,
                  sendTypeForPickUpCar: 0,
                  skuId: 1857221,
                  vehicleCode: '0',
                  vendorCode: '30116',
                  vendorVehicleCode: '20076508',
                },
              },
              {
                reference: {
                  bizVendorCode: 'SD8812',
                  pStoreCode: '107401',
                  packageType: 1,
                  skuId: 1861829,
                  vehicleCode: '0',
                  vendorCode: '71523',
                },
              },
              {
                reference: {
                  bizVendorCode: 'SD7281',
                  pStoreCode: '153013',
                  packageType: 1,
                  skuId: 3021067,
                  vehicleCode: '0',
                  vendorCode: '15000539',
                },
              },
              {
                reference: {
                  bizVendorCode: 'SD7172',
                  pStoreCode: '137302',
                  packageType: 1,
                  skuId: 2707862,
                  vehicleCode: '0',
                  vendorCode: '15000430',
                },
              },
            ],
          },
          {
            group: 104883,
            groupSort: 2,
            highestPrice: 488,
            hot: 0,
            hotScore: 0,
            hotType: 0,
            isCredit: true,
            isEasy: true,
            lowestDistance: 0,
            lowestPrice: 488,
            maximumCommentCount: 2102,
            maximumRating: 4.8,
            minDOrinPrice: 0,
            minDPrice: 488,
            minTPrice: 2347,
            modifySameVehicle: false,
            outTags: [
              {
                category: 2,
                code: '7',
                colorCode: '2',
                groupCode: 'MarketGroup1347',
                groupId: 3,
                labelCode: '3746',
                mergeId: 0,
                sortNum: 10,
                title: '免押金',
                type: 1,
              },
            ],
            pWay: '可选：店员免费上门送取车',
            priceSize: 1,
            productRef: { license: '外牌', licenseStyle: '6', licenseTag: '' },
            rCoup: 0,
            reactId: '**********',
            scoreSort: 0,
            showType: 1,
            sortNum: 9,
            vehicleCode: '4883',
            vehicleRecommendProduct: {
              introduce: '当前车型最低价',
              productCodes: ['SD7089_0_137706_137706'],
            },
            vendorPriceList: [
              {
                fees: [
                  {
                    amount: 1952,
                    amountStr: '¥1952',
                    code: 'CAR_RENTAL_FEE',
                    detail: [
                      {
                        amount: 1952,
                        amountDesc: '¥1952',
                        code: '1001',
                        name: '租车费',
                      },
                    ],
                    name: '车辆租金',
                    subAmount: 488,
                    subAmountStr: '日均¥488',
                  },
                  {
                    amount: 35,
                    amountStr: '¥35',
                    code: 'CAR_SERVICE_FEE',
                    currencyCode: '¥',
                    detail: [
                      {
                        amount: 35,
                        amountDesc: '¥35',
                        code: '1003',
                        desc: '用于车辆清洁，车辆保养，单据制作，人员服务等',
                        name: '车行手续费',
                        showFree: false,
                      },
                    ],
                    name: '服务/手续费',
                  },
                  {
                    amount: 360,
                    amountStr: '¥360',
                    code: 'CAR_GUARANTEE_FEE',
                    currencyCode: '¥',
                    detail: [
                      {
                        amount: 360,
                        amountDesc: '¥360',
                        code: '1002',
                        name: '基础服务费',
                        showFree: false,
                      },
                    ],
                    name: '车行保障服务费',
                  },
                  {
                    amount: 2347,
                    amountStr: '¥2347',
                    code: '10001',
                    currencyCode: '¥',
                    name: '总价',
                    subAmount: 2347,
                    subAmountStr: '¥2347',
                  },
                ],
                isMinTPriceVendor: true,
                reference: {
                  bizVendorCode: 'SD7089',
                  klbVersion: 1,
                  pLev: 83230,
                  pStoreCode: '137706',
                  packageId: 'sec',
                  packageType: 1,
                  priceVersion:
                    'SH-PRICEVERSION_MTM3NzA2XzQ4ODNfMV80ODguMF8xOTUyLjBfMC4wXzIzNDcuMF80ODguMF8yMzQ3LjBfMF8wXzAuMF8wLjBfMzYwLjBfMzUuMF8wXzBfNTcyMDU2Mw==',
                  rLev: 83230,
                  rStoreCode: '137706',
                  sendTypeForPickOffCar: 0,
                  sendTypeForPickUpCar: 0,
                  skuId: 5720563,
                  vehicleCode: '0',
                  vendorCode: '13072',
                  vendorVehicleCode: '2107_49537_pupai',
                },
              },
            ],
          },
        ],
        showNumber: 1,
      },
    },
    {
      listRes: {},
      expected: {
        priceGroup: [
          {
            group: 104883,
            groupSort: 1,
            highestPrice: 844,
            hot: 0,
            hotScore: 0,
            hotType: 0,
            isCredit: true,
            isEasy: true,
            lowestDistance: 0,
            lowestPrice: 441,
            maximumCommentCount: 3444,
            maximumRating: 5,
            minDOrinPrice: 450,
            minDPrice: 441,
            minTPrice: 2124,
            modifySameVehicle: false,
            outTags: [
              {
                category: 2,
                code: '7',
                colorCode: '2',
                groupCode: 'MarketGroup1347',
                groupId: 3,
                labelCode: '3746',
                mergeId: 0,
                sortNum: 10,
                title: '免押金',
                type: 1,
              },
            ],
            pTag: {
              amountTitle: '已减36',
              category: 3,
              code: '30',
              colorCode: '15',
              groupCode: 'MarketGroup1384',
              groupId: 1,
              labelCode: '3852',
              mergeId: 0,
              sortNum: 10000,
              title: '长租特惠',
              type: 3,
            },
            pWay: '可选：店员免费上门送取车',
            priceSize: 4,
            productRef: {
              license: '沪牌',
              licenseStyle: '2',
              licenseTag: '沪牌',
            },
            rCoup: 0,
            reactId: '**********',
            scoreSort: 0,
            sortNum: 9,
            vehicleCode: '4883',
            vehicleRecommendProduct: {
              introduce: '当前车型最低价',
              productCodes: ['SD3864_0_106861_106861'],
            },
            vendorPriceList: [
              {
                fees: [
                  {
                    amount: 1764,
                    amountStr: '¥1764',
                    code: 'CAR_RENTAL_FEE',
                    detail: [
                      {
                        amount: 1800,
                        amountDesc: '¥1800',
                        code: '1001',
                        name: '租车费',
                      },
                      {
                        amount: 36,
                        amountDesc: '¥36',
                        code: '3852',
                        name: '长租特惠',
                      },
                    ],
                    name: '车辆租金',
                    originalDailyPrice: 450,
                    subAmount: 441,
                    subAmountStr: '日均¥441',
                  },
                  {
                    amount: 20,
                    amountStr: '¥20',
                    code: 'CAR_SERVICE_FEE',
                    currencyCode: '¥',
                    detail: [
                      {
                        amount: 20,
                        amountDesc: '¥20',
                        code: '1003',
                        desc: '用于车辆清洁，车辆保养，单据制作，人员服务等',
                        name: '车行手续费',
                        showFree: false,
                      },
                    ],
                    name: '服务/手续费',
                  },
                  {
                    amount: 340,
                    amountStr: '¥340',
                    code: 'CAR_GUARANTEE_FEE',
                    currencyCode: '¥',
                    detail: [
                      {
                        amount: 340,
                        amountDesc: '¥340',
                        code: '1002',
                        name: '基础服务费',
                        showFree: false,
                      },
                    ],
                    name: '车行保障服务费',
                  },
                  {
                    amount: 2124,
                    amountStr: '¥2124',
                    code: '10001',
                    currencyCode: '¥',
                    name: '总价',
                    subAmount: 2160,
                    subAmountStr: '¥2160',
                  },
                ],
                isMinTPriceVendor: true,
                reference: {
                  bizVendorCode: 'SD3864',
                  comPriceCode:
                    '[c]MjIxfDE4NTd8MjAyMC4wMC0wMiAzLTEwMDowMDAwOjAmJjEkJjQ1MC0xMC0yMDIzMDowMDAzIDA0NTAmOjAwJjAyMy0mMSQyNCAwMDEwLTAwMCY0OjAwOjEkMjA1MCYmMC0wNTIzLTEwMDowIDAwOjAmJjEwJjQ1MDEmNCR8MTAmMTgwJjQ1MDAzJjEwJDEwMDAmMiYyMC4kMTAwMC4wMDg1LjAyJjQmMC4wMDAmMzQyMy0wJHwyMCAxODo5LTIyNwAAADMzOjE=',
                  klbVersion: 1,
                  pLev: 22255,
                  pStoreCode: '106861',
                  packageId: '',
                  packageType: 1,
                  priceVersion:
                    'SH-PRICEVERSION_MTA2ODYxXzQ4ODNfMV80NTBfMTgwMF80NTBfMjE2MC4wMF80NDEuMF8yMTI0LjBfMF8wXzAuMF8wLjBfMzQwLjAwXzIwLjAwXzAuMDBfMC4wMF8xODU3MjIx',
                  rLev: 22255,
                  rStoreCode: '106861',
                  sendTypeForPickOffCar: 0,
                  sendTypeForPickUpCar: 0,
                  skuId: 1857221,
                  vehicleCode: '0',
                  vendorCode: '30116',
                  vendorVehicleCode: '20076508',
                },
              },
              {
                reference: {
                  bizVendorCode: 'SD8812',
                  pStoreCode: '107401',
                  packageType: 1,
                  skuId: 1861829,
                  vehicleCode: '0',
                  vendorCode: '71523',
                },
              },
              {
                reference: {
                  bizVendorCode: 'SD7281',
                  pStoreCode: '153013',
                  packageType: 1,
                  skuId: 3021067,
                  vehicleCode: '0',
                  vendorCode: '15000539',
                },
              },
              {
                reference: {
                  bizVendorCode: 'SD7172',
                  pStoreCode: '137302',
                  packageType: 1,
                  skuId: 2707862,
                  vehicleCode: '0',
                  vendorCode: '15000430',
                },
              },
            ],
          },
          {
            group: 104883,
            groupSort: 2,
            highestPrice: 488,
            hot: 0,
            hotScore: 0,
            hotType: 0,
            isCredit: true,
            isEasy: true,
            lowestDistance: 0,
            lowestPrice: 488,
            maximumCommentCount: 2102,
            maximumRating: 4.8,
            minDOrinPrice: 0,
            minDPrice: 488,
            minTPrice: 2347,
            modifySameVehicle: false,
            outTags: [
              {
                category: 2,
                code: '7',
                colorCode: '2',
                groupCode: 'MarketGroup1347',
                groupId: 3,
                labelCode: '3746',
                mergeId: 0,
                sortNum: 10,
                title: '免押金',
                type: 1,
              },
            ],
            pWay: '可选：店员免费上门送取车',
            priceSize: 1,
            productRef: { license: '外牌', licenseStyle: '6', licenseTag: '' },
            rCoup: 0,
            reactId: '**********',
            scoreSort: 0,
            showType: 1,
            sortNum: 9,
            vehicleCode: '4883',
            vehicleRecommendProduct: {
              introduce: '当前车型最低价',
              productCodes: ['SD7089_0_137706_137706'],
            },
            vendorPriceList: [
              {
                fees: [
                  {
                    amount: 1952,
                    amountStr: '¥1952',
                    code: 'CAR_RENTAL_FEE',
                    detail: [
                      {
                        amount: 1952,
                        amountDesc: '¥1952',
                        code: '1001',
                        name: '租车费',
                      },
                    ],
                    name: '车辆租金',
                    subAmount: 488,
                    subAmountStr: '日均¥488',
                  },
                  {
                    amount: 35,
                    amountStr: '¥35',
                    code: 'CAR_SERVICE_FEE',
                    currencyCode: '¥',
                    detail: [
                      {
                        amount: 35,
                        amountDesc: '¥35',
                        code: '1003',
                        desc: '用于车辆清洁，车辆保养，单据制作，人员服务等',
                        name: '车行手续费',
                        showFree: false,
                      },
                    ],
                    name: '服务/手续费',
                  },
                  {
                    amount: 360,
                    amountStr: '¥360',
                    code: 'CAR_GUARANTEE_FEE',
                    currencyCode: '¥',
                    detail: [
                      {
                        amount: 360,
                        amountDesc: '¥360',
                        code: '1002',
                        name: '基础服务费',
                        showFree: false,
                      },
                    ],
                    name: '车行保障服务费',
                  },
                  {
                    amount: 2347,
                    amountStr: '¥2347',
                    code: '10001',
                    currencyCode: '¥',
                    name: '总价',
                    subAmount: 2347,
                    subAmountStr: '¥2347',
                  },
                ],
                isMinTPriceVendor: true,
                reference: {
                  bizVendorCode: 'SD7089',
                  klbVersion: 1,
                  pLev: 83230,
                  pStoreCode: '137706',
                  packageId: 'sec',
                  packageType: 1,
                  priceVersion:
                    'SH-PRICEVERSION_MTM3NzA2XzQ4ODNfMV80ODguMF8xOTUyLjBfMC4wXzIzNDcuMF80ODguMF8yMzQ3LjBfMF8wXzAuMF8wLjBfMzYwLjBfMzUuMF8wXzBfNTcyMDU2Mw==',
                  rLev: 83230,
                  rStoreCode: '137706',
                  sendTypeForPickOffCar: 0,
                  sendTypeForPickUpCar: 0,
                  skuId: 5720563,
                  vehicleCode: '0',
                  vendorCode: '13072',
                  vendorVehicleCode: '2107_49537_pupai',
                },
              },
            ],
          },
        ],
      },
    },
  ];
  test.each(mockStateMap2)('getNewPriceGroup', ({ listRes, expected }) => {
    ListReqAndResData.setData(
      ListReqAndResData.keyList.listProductRes,
      listRes,
    );
    const result = getNewPriceGroup(
      104883,
      listRes2.productGroups[0].productList,
    );
    expect(result).toMatchObject(expected);
  });
});
