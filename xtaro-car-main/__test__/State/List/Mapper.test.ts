import { INVOKE_FROM } from '../../../src/pages/xcar/Constants/ServerMapping';
import {
  packageListBaseReqParam,
  getListBatchGroups,
  getListReqCount,
  getShowLikeLabel,
  getIsShowAgeModify,
  packagePriceSummaryModalData,
  combineGroupVendorPriceList,
  setResVehicleAndPriceBatch,
  getPromotionFilterText,
  getPromotionFilterCode,
  getCurPromotionFilter,
  getDisplaySelectedFiltersByType,
  getPriceStep,
  getPriceRange,
  mappingResponseFees2PriceModal,
  getVendorStore,
  concatVendorPrice,
  appendResPatch,
  upDateMergeVehicleExtendInfos,
} from '../../../src/pages/xcar/State/List/Mappers';

import Utils from '../../../src/pages/xcar/Util/Utils';

import * as ListResSelectors from '../../../src/pages/xcar/Global/Cache/ListResSelectors';

jest.mock('../../../src/pages/xcar/Global/Cache/ListResSelectors', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/Global/Cache/ListResSelectors') }));

jest.mock('../../../src/pages/xcar/State/ModifyOrder/CommonSelector', () => ({
  getRebookParams: jest.fn(() => {
    return {
      test: 1,
    };
  }),
}));

// jest.mock('../../../src/pages/xcar/Global/Cache/ListResSelectors', () => ({
//   getFeeMap: jest.fn(),
//   getPromotionFilterItems: () => ({
//     filterGroups: [
//       {
//         filterItems: [
//           {
//             name: 'First Filter Item',
//           },
//           {
//             name: 'Second Filter Item',
//           },
//         ],
//       },
//     ],
//   }),
// }));

describe('List Mappers Test', () => {
  describe('List Mappers packageListBaseReqParam', () => {
    const stateMock = {
      DriverAgeAndNumber: {
        age: '30~60',
        adultNumbers: 2,
        childNumbers: 0,
      },
      ModifyOrder: {
        isLoading: false,
        isMaskLoading: false,
        isFail: false,
        modifyOrderWarnModalVisible: false,
        modifyOrderWarnModalProps: null,
        modifyOrderReqeust: null,
        modifyOrderResponse: null,
        modifyOrderSuccess: false,
        rebookParams: null,
        isPassengerLoaded: false,
      },
      LocationAndDate: {
        envMeta: {
          isMultiEnvironment: true,
        },
        rentalLocation: {
          pickUp: {
            cid: 43,
            cname: '三亚',
            country: '中国',
            realcountry: '中国',
            isDomestic: true,
            area: {
              id: '',
              name: '凤凰国际机场T1航站楼',
              lat: 18.30747,
              lng: 109.41201,
              type: '1',
            },
            isFromPosition: false,
          },
          dropOff: {
            cid: 43,
            cname: '三亚',
            country: '中国',
            realcountry: '中国',
            isDomestic: true,
            area: {
              id: '',
              name: '凤凰国际机场T1航站楼',
              lat: 18.30747,
              lng: 109.41201,
              type: '1',
            },
            isFromPosition: false,
          },
          isShowDropOff: false,
        },
        rentalDate: {
          pickUp: {
            dateTime: '2022-06-13T02:00:00.000Z',
          },
          dropOff: {
            dateTime: '2022-06-15T02:00:00.000Z',
          },
          productDropOff: {
            dateTime: '2022-06-15T02:00:00.000Z',
          },
        },
        position: {
          positionInfo: {},
          positionLocation: {},
          positionStatus: {
            locationOn: false,
            locationStatus: 'failure',
          },
        },
        filterItems: [],
        isPickupStation: '1',
        isDropOffStation: '1',
        airPortTransferTip: {
          isShow: false,
        },
      },
    };
    test('packageListBaseReqParam state null', () => {
      const result = packageListBaseReqParam(stateMock);

      // 判断返回包含特定值
      expect(result).toEqual(
        expect.objectContaining({
          age: 30,
        }),
      );

      // 判断返回等于
      expect(result).toEqual({
        age: 30,
        adultNumbers: 2,
        childrenNumbers: 0,
        pickupPointInfo: {
          cityId: 43,
          date: '2022-06-13 10:00:00',
          locationCode: '',
          locationName: '凤凰国际机场T1航站楼',
          locationType: '1',
          poi: { latitude: 18.30747, longitude: 109.41201, radius: 0 },
          pickupOnDoor: 0,
          dropOffOnDoor: 0,
        },
        returnPointInfo: {
          cityId: 43,
          date: '2022-06-15 10:00:00',
          locationCode: '',
          locationName: '凤凰国际机场T1航站楼',
          locationType: '1',
          poi: { latitude: 18.30747, longitude: 109.41201, radius: 0 },
          pickupOnDoor: 0,
          dropOffOnDoor: 0,
        },
        searchType: 1,
        modify: { test: 1 },
      });
    });

    test('packageListBaseReqParam age 非默认', () => {
      const result = packageListBaseReqParam({
        ...stateMock,
        DriverAgeAndNumber: {
          age: '25',
          adultNumbers: 2,
          childNumbers: 0,
        },
      });

      // 判断返回包含特定值
      expect(result).toEqual(
        expect.objectContaining({
          age: 25,
        }),
      );

      // 判断返回等于
      expect(result).toEqual({
        age: 25,
        adultNumbers: 2,
        childrenNumbers: 0,
        pickupPointInfo: {
          cityId: 43,
          date: '2022-06-13 10:00:00',
          locationCode: '',
          locationName: '凤凰国际机场T1航站楼',
          locationType: '1',
          poi: { latitude: 18.30747, longitude: 109.41201, radius: 0 },
          pickupOnDoor: 0,
          dropOffOnDoor: 0,
        },
        returnPointInfo: {
          cityId: 43,
          date: '2022-06-15 10:00:00',
          locationCode: '',
          locationName: '凤凰国际机场T1航站楼',
          locationType: '1',
          poi: { latitude: 18.30747, longitude: 109.41201, radius: 0 },
          pickupOnDoor: 0,
          dropOffOnDoor: 0,
        },
        searchType: 1,
        modify: { test: 1 },
      });
    });

    test('getListBatchGroups', () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const result = getListBatchGroups();
      expect(result).toEqual([0]);
    });

    test('getListBatchGroups', () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      const result = getListBatchGroups();
      expect(result).toEqual([0, 1]);
    });

    test('getListReqCount', () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const result = getListReqCount();
      expect(result).toEqual(1);
    });

    test('getListReqCount', () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      const result = getListReqCount();
      expect(result).toEqual(2);
    });
  });
});

describe('getShowLikeLabel', () => {
  test.each([
    {
      params: { activeGroupId: 'all', vehicleIndex: 0, hotType: 4 },
      expected: true,
    },
    {
      params: { activeGroupId: 'all', vehicleIndex: 0, hotType: 0 },
      expected: false,
    },
  ])('测试 getShowLikeLabel', ({ params, expected }) => {
    expect(getShowLikeLabel(params)).toEqual(expected);
  });
});

describe('getIsShowAgeModify', () => {
  const stateMock1 = {
    DriverAgeAndNumber: {
      age: '30~60',
      adultNumbers: 2,
      childNumbers: 0,
    },
    List: {
      isMoreAge: false,
    },
  };
  const stateMock2 = {
    DriverAgeAndNumber: {
      age: '29',
      adultNumbers: 2,
      childNumbers: 0,
    },
    List: {
      isMoreAge: true,
    },
  };
  test.each([
    {
      params: stateMock1,
      expected: false,
    },
    {
      params: stateMock2,
      expected: true,
    },
  ])('测试 getIsShowAgeModify', ({ params, expected }) => {
    expect(getIsShowAgeModify(params)).toEqual(expected);
  });
});

describe('packagePriceSummaryModalData', () => {
  jest.mock('../../../src/pages/xcar/Global/Cache/ListResSelectors', () => {
    return {
      getFeeMap: jest.fn(() => ({})),
    };
  });
  const data = {
    fees: [],
    footer: '下一步，选择门店',
    pickOffLevel: 0,
    pickUpLevel: 0,
    priceListLen: 4,
    reference: {},
    section: {},
    title: '总价说明',
    totalPriceName: '总价',
    type: 'list',
    uniqueCode: undefined,
    vehicleIndex: 2,
    vehicleList: undefined,
    vehiclesSetId: '64',
    vendorListPageParam: {},
  };
  const expected = {
    data: {
      chargesInfos: [],
      chargesSummary: null,
    },
    title: data?.title,
    footerText: data?.footer,
    invokeFrom: INVOKE_FROM.LIST,
    vendorListPageParam: data?.vendorListPageParam,
    vehicleIndex: data?.vehicleIndex,
    priceListLen: data?.priceListLen,
    type: data?.type,
    uniqueCode: data?.uniqueCode,
    vehicleCode: undefined,
    isEasyLife2024: undefined,
    isShowEasyLifeCompare: false,
    packageCompareRequest: undefined,
    vendorPriceList: undefined,
    section: data?.section,
    vendorPriceInfo: undefined,
  };
  const mockState = {
    List: {
      priceSummaryModalData: {
        data,
      },
    },
  };
  test('packagePriceSummaryModalData', () => {
    expect(packagePriceSummaryModalData(mockState)).toEqual(expected);
  });
});


describe('combineGroupVendorPriceList', () => {
  const mockMap = [{
    prevGroup: [
      {
        groupCode: 'G1',
        productList: [
          {
            vehicleCode: '11111',
            vendorPriceList: [
              {
                vendorName: 'Group Thrifty1',
                sortNum: 1,
              },
            ],
          },
        ],
      },
      {
        groupCode: 'G2',
        productList: [
          {
            vehicleCode: '10177',
            vendorPriceList: [
              {
                vendorName: 'Thrifty2',
                sortNum: 2,
              },
            ],
          },
        ],
      },
    ],
    nextGroup: [
      {
        groupCode: 'G2',
        productList: [
          {
            vehicleCode: '10177',
            vendorPriceList: [
              {
                vendorName: 'Thrifty1',
                sortNum: 1,
              },
            ],
          },
          {
            vehicleCode: '22222',
            vendorPriceList: [
              {
                vendorName: 'Thrifty3',
                sortNum: 3,
              },
            ],
          },
        ],
      },
      {
        groupCode: 'G3',
        productList: [
          {
            vehicleCode: '33333',
            vendorPriceList: [
              {
                vendorName: 'Group Thrifty3',
                sortNum: 1,
              },
            ],
          },
        ],
      },
    ],
    expected: [
      {
        groupCode: 'G1',
        productList: [
          {
            vehicleCode: '11111',
            vendorPriceList: [
              {
                vendorName: 'Group Thrifty1',
                sortNum: 1,
              },
            ],
          },
        ],
      },
      {
        groupCode: 'G2',
        productList: [
          {
            batchNo: undefined,
            sortNum: undefined,
            vehicleCode: '10177',
            vendorPriceList: [
              {
                filterAggregations: undefined,
                vendorName: 'Thrifty2',
                sortNum: 2,
              },
            ],
          },
          {
            vehicleCode: '22222',
            vendorPriceList: [
              {
                vendorName: 'Thrifty3',
                sortNum: 3,
              },
            ],
          },
        ],
      },
      {
        groupCode: 'G3',
        productList: [
          {
            vehicleCode: '33333',
            vendorPriceList: [
              {
                vendorName: 'Group Thrifty3',
                sortNum: 1,
              },
            ],
          },
        ],
      },
    ],
  }];
  test.each(mockMap)('combineGroupVendorPriceList', ({
    prevGroup,
    nextGroup,
    expected
  }) => {
    const result = combineGroupVendorPriceList(prevGroup, nextGroup);
    expect(result).toEqual(expected);
  });
});


describe('setResVehicleAndPriceBatch', () => {
  test('should set batchNo correctly for C200 baseResponse code', () => {
    const res = {
      baseResponse: {
        code: '200',
      },
      vehicleList: [{ id: 1, name: 'Vehicle 1' }],
      productGroups: [
        {
          id: 1,
          name: 'Group 1',
          productList: [
            {
              id: 1,
              name: 'Product 1',
              vendorPriceList: [
                { vendorId: 1, price: 10 },
                { vendorId: 2, price: 10 },
                { vendorId: 3, price: 10 },
              ],
            },
          ],
        },
      ],
    };

    const expected = {
      baseResponse: {
        code: '200',
      },
      vehicleList: [{ id: 1, name: 'Vehicle 1'}],
      productGroups: [
        {
          id: 1,
          name: 'Group 1',
          productList: [
            {
              id: 1,
              name: 'Product 1',
              batchNo: 2,
              vendorPriceList: [{
                vendorId: 1,
                price: 10,
                reference: {
                  priceBatchNo: 2,
                }
              }, {
                vendorId: 2,
                price: 10,
                reference: {
                  priceBatchNo: 2,
                }
              }, {
                vendorId: 3,
                price: 10,
                reference: {
                  priceBatchNo: 2,
                }
              }],
            },
          ],
        },
      ],
    };

    const updatedRes = setResVehicleAndPriceBatch(res);

    expect(updatedRes).toEqual(expected);
  });
});



describe('getCurPromotionFilter', () => {
  it('should return null if promotionFilterItems is falsy', () => {
    // Calling the function to be tested
    const result = getCurPromotionFilter();

    // Assertion
    expect(result).toBeNull();
  });

  it('should return the first item of promotionFilterItems if it is truthy', () => {
    // Mocking the response from getPromotionFilterItems
    const mockPromotionFilterItems = [
      {
        filterGroups: [
          {
            filterItems: [
              {
                name: 'First Filter Item',
                itemCode: 'ITEM001',
              },
              {
                name: 'Second Filter Item',
                itemCode: 'ITEM002',
              },
            ],
          },
        ],
      },
    ];
    jest.spyOn(ListResSelectors, 'getPromotionFilterItems').mockReturnValue(mockPromotionFilterItems);

    // Calling the function to be tested
    const result = getCurPromotionFilter();

    // Assertion
    expect(result).toEqual({
      filterGroups: [
        {
          filterItems: [
            {
              name: 'First Filter Item',
              itemCode: 'ITEM001',
            },
            {
              name: 'Second Filter Item',
              itemCode: 'ITEM002',
            },
          ],
        },
      ],
    });
  });
});


describe('getPromotionFilterText', () => {
  it('should return an empty string if curPromotionFilter is falsy', () => {
    // Mocking the getCurPromotionFilter function to return falsy value
    jest.spyOn(ListResSelectors, 'getPromotionFilterItems').mockReturnValue(null);
    // Calling the function to be tested
    const result = getPromotionFilterText();

    // Assertion
    expect(result).toBe('');
  });

  it('should return the name of the first filter item if curPromotionFilter is truthy', () => {
    // Mocking the response from getCurPromotionFilter
    const mockPromotionFilterItems = [
      {
        filterGroups: [
          {
            filterItems: [
              {
                name: 'First Filter Item',
                itemCode: 'ITEM001',
              },
              {
                name: 'Second Filter Item',
                itemCode: 'ITEM002',
              },
            ],
          },
        ],
      },
    ];
    jest.spyOn(ListResSelectors, 'getPromotionFilterItems').mockReturnValue(mockPromotionFilterItems);
    // Calling the function to be tested

    // Calling the function to be tested
    const result = getPromotionFilterText();

    // Assertion
    expect(result).toBe('First Filter Item');
  });
});

describe('getPromotionFilterCode', () => {
  it('should return an empty string if curPromotionFilter is falsy', () => {
    // Mocking the getCurPromotionFilter function to return falsy value
    // Mocking the response from getCurPromotionFilter
    const mockPromotionFilterItems = [];
    jest.spyOn(ListResSelectors, 'getPromotionFilterItems').mockReturnValue(mockPromotionFilterItems);

    // Calling the function to be tested
    const result = getPromotionFilterCode();

    // Assertion
    expect(result).toBe('');
  });

  it('should return the itemCode of the first filter item if curPromotionFilter is truthy', () => {
    // Mocking the response from getCurPromotionFilter
    const mockPromotionFilterItems = [
      {
        filterGroups: [
          {
            filterItems: [
              {
                name: 'First Filter Item',
                itemCode: 'ITEM001',
              },
              {
                name: 'Second Filter Item',
                itemCode: 'ITEM002',
              },
            ],
          },
        ],
      },
    ];
    jest.spyOn(ListResSelectors, 'getPromotionFilterItems').mockReturnValue(mockPromotionFilterItems);

    // Calling the function to be tested
    const result = getPromotionFilterCode();

    // Assertion
    expect(result).toBe('ITEM001');
  });
});



describe('getDisplaySelectedFiltersByType', () => {
  it('should return displaySelectedFilters with only sortFilter when filterBarType is FilterBarType.Sort', () => {
    // Mocking the selectedFilters and filterBarType
    const selectedFilters = {
      sortFilter: 'Sort Filter',
      bitsFilter: ['Bits Filter 1', 'Bits Filter 2'],
      commentFilter: ['Comment Filter'],
      priceFilter: ['Price Filter'],
    };
    const filterBarType = 'Sort'; // Assuming FilterBarType.Sort is defined correctly

    // Calling the function to be tested
    const result = getDisplaySelectedFiltersByType(selectedFilters, filterBarType);

    // Assertion
    expect(result).toEqual({
      codeList: ['Sort Filter'],
      priceList: [],
    });
  });

  it('should return displaySelectedFilters with combined bitsFilter, commentFilter, and priceFilter when filterBarType is not FilterBarType.Sort', () => {
    // Mocking the selectedFilters and filterBarType
    const selectedFilters = {
      sortFilter: 'Sort Filter',
      bitsFilter: ['Bits Filter 1', 'Bits Filter 2'],
      commentFilter: ['Comment Filter'],
      priceFilter: ['Price Filter'],
    };
    const filterBarType = 'OtherType'; // Assuming FilterBarType.Sort is defined correctly

    // Calling the function to be tested
    const result = getDisplaySelectedFiltersByType(selectedFilters, filterBarType);

    // Assertion
    expect(result).toEqual({
      codeList: ['Bits Filter 1', 'Bits Filter 2', 'Comment Filter'],
      priceList: ['Price Filter'],
    });
  });
});


describe('getPriceStep', () => {
  it('should return 1000 when currency is JPY', () => {
    // Mocking the currency
    const currency = 'JPY';

    // Calling the function to be tested
    const result = getPriceStep(currency);

    // Assertion
    expect(result).toBe(1000);
  });

  it('should return 10000 when currency is KRW', () => {
    // Mocking the currency
    const currency = 'KRW';

    // Calling the function to be tested
    const result = getPriceStep(currency);

    // Assertion
    expect(result).toBe(10000);
  });

  it('should return 50 when currency is not JPY or KRW', () => {
    // Mocking the currency
    const currency = 'USD';

    // Calling the function to be tested
    const result = getPriceStep(currency);

    // Assertion
    expect(result).toBe(50);
  });
});


describe('getPriceRange', () => {
  it('should return the correct price range object when filterItem has valid values', () => {
    // Mocking the input parameters
    const filterItem = [{ code: '100-200' }, { code: '300-400' }];
    const priceStep = 50;

    // Calling the function to be tested
    const result = getPriceRange(filterItem, priceStep);

    // Assertion
    expect(result).toEqual({ minRange: 100, maxRange: 400 });
  });
});


describe('mappingResponseFees2PriceModal', () => {
  it('should return the expected result when fees has valid values', () => {
    // Mocking the input parameters
    const fees = [
      {
        code: 'TOTAL_FEE',
        name: 'Total Fee',
        amount: 100,
        subAmount: 90,
        detail: [],
      },
      {
        code: 'CAR_RENTAL_FEE',
        name: 'Car Rental Fee',
        amount: 50,
        subAmount: 40,
        originalDailyPrice: 30,
        detail: [
          {
            name: 'Fee 1',
            amount: 10,
            code: 'FEE_1',
            showFree: false,
            size: 'small',
          },
          {
            name: 'Fee 2',
            amount: 20,
            code: 'FEE_2',
            showFree: true,
            size: 'large',
          },
        ],
      },
    ];
    const totalPriceName = 'Total Price';
    const feeMap = [
      {
        code: 'FEE_1',
        subName: 'Sub Fee 1',
      },
    ];

    // Calling the function to be tested
    const result = mappingResponseFees2PriceModal(fees, totalPriceName, feeMap);

    // Assertion
    expect(result).toEqual({
      chargesInfos: [
        {
          currentDailyPrice: 0,
          currentTotalPrice: 100,
          hourDesc: undefined,
          items: [],
          originDailyPrice: 0,
          title: "Total Fee",
        },
        {
          title: 'Car Rental Fee',
          currentTotalPrice: 50,
          currentDailyPrice: 40,
          originDailyPrice: 30,
          items: [
            {
              title: 'Fee 1',
              currentTotalPrice: 10,
              isPromotion: true,
              showFree: false,
              subTitle: 'Sub Fee 1',
              size: 'small',
            },
            {
              title: 'Fee 2',
              currentTotalPrice: 20,
              isPromotion: true,
              showFree: true,
              subTitle: undefined,
              size: 'large',
            },
          ],
          hourDesc: undefined,
        },
      ],
      chargesSummary: null,
    });
  });

  it('should return an empty result when fees is empty', () => {
    // Mocking the input parameters
    const fees = [];
    const totalPriceName = 'Total Price';
    const feeMap = [];

    // Calling the function to be tested
    const result = mappingResponseFees2PriceModal(fees, totalPriceName, feeMap);

    // Assertion
    expect(result).toEqual({
      chargesInfos: [],
      chargesSummary: null,
    });
  });

  it('should handle missing properties gracefully', () => {
    // Mocking the input parameters
    const fees = [
      {
        code: 'TOTAL_FEE',
        amount: 100,
        detail: [
          {
            name: 'Fee 1',
            amount: 10,
            code: 'FEE_1',
          },
        ],
      },
    ];
    const totalPriceName = undefined;
    const feeMap = undefined;

    // Calling the function to be tested
    const result = mappingResponseFees2PriceModal(fees, totalPriceName, feeMap);

    // Assertion
    expect(result).toEqual({
      chargesInfos: [
        {
          title: undefined,
          currentTotalPrice: 100,
          currentDailyPrice: 0,
          originDailyPrice: 0,
          items: [
            {
              title: 'Fee 1',
              currentTotalPrice: 10,
              isPromotion: false,
              showFree: undefined,
              subTitle: '',
              size: undefined,
            },
          ],
          hourDesc: undefined,
        },
      ],
      chargesSummary: null,
    });
  });
});


describe('getVendorStore', () => {
  it('should return the store that matches the reference pStoreCode', () => {
    // Mocking the input parameters
    const storeList = [
      { storeCode: 'A', name: 'Store A' },
      { storeCode: 'B', name: 'Store B' },
    ];
    const reference = { pStoreCode: 'B' };

    // Calling the function to be tested
    const result = getVendorStore(storeList, reference);

    // Assertion
    expect(result).toEqual({ storeCode: 'B', name: 'Store B' });
  });

  it('should return undefined when no store matches the reference pStoreCode', () => {
    // Mocking the input parameters
    const storeList = [
      { storeCode: 'A', name: 'Store A' },
      { storeCode: 'B', name: 'Store B' },
    ];
    const reference = { pStoreCode: 'C' };

    // Calling the function to be tested
    const result = getVendorStore(storeList, reference);

    // Assertion
    expect(result).toBeUndefined();
  });

  it('should return undefined when storeList is not provided and getStoreList is not available', () => {
    // Mocking the input parameters
    const storeList = undefined;
    const reference = { pStoreCode: 'B' };

    // Mocking the getStoreList function
    jest.spyOn(ListResSelectors, 'getStoreList').mockReturnValue(null);

    // Calling the function to be tested
    const result = getVendorStore(storeList, reference);

    // Assertion
    expect(result).toBeUndefined();
  });
});

describe('concatVendorPrice', () => {

  test.each([
    {
      preVendorPriceList: [
        {
          priceInfo: {
            currentDailyPrice: 9,
          },
          reference: {
            "bizVendorCode": "14085",
            "vendorCode": "SD0125",
            "vehicleCode": "10630",
            "packageId": "18847102",
            "packageType": 0,
            "skuId": 5366512,
          },
          sortNum: 1,
          extMap: {
            freeCancel: false,
          },
          filterAggregations: { price: 10 },
        },
        {
          priceInfo: {
            currentDailyPrice: 10,
          },
          reference: {
            "bizVendorCode": "14085",
            "vendorCode": "SD0125",
            "vehicleCode": "10630",
            "packageId": "18847102",
            "packageType": 0,
            "pStoreCode": "FRAKYFRAT01",
            "rStoreCode": "FRAKYFRAT01",
          },
          sortNum: 0,
          extMap: {
            freeCancel: false,
          },
          filterAggregations: { price: 10 },
        },
      ],
      nextVendorPriceList: [],
      expected: [{
        priceInfo: {
          currentDailyPrice: 10,
        },
        reference: {
          "bizVendorCode": "14085",
          "vendorCode": "SD0125",
          "vehicleCode": "10630",
          "packageId": "18847102",
          "packageType": 0,
          "pStoreCode": "FRAKYFRAT01",
          "rStoreCode": "FRAKYFRAT01",
        },
        sortNum: 0,
        extMap: {
          freeCancel: false,
        },
        filterAggregations: { price: 10 },
      }, {
        priceInfo: {
          currentDailyPrice: 9,
        },
        reference: {
          "bizVendorCode": "14085",
          "vendorCode": "SD0125",
          "vehicleCode": "10630",
          "packageId": "18847102",
          "packageType": 0,
          "skuId": 5366512,
        },
        sortNum: 1,
        extMap: {
          freeCancel: false,
        },
        filterAggregations: { price: 10 },
      }],
    },
    {
      preVendorPriceList: [
        {
          priceInfo: {
            currentDailyPrice: 10,
          },
          reference: {
            "bizVendorCode": "14085",
            "vendorCode": "SD0125",
            "vehicleCode": "10630",
            "packageId": "18847102",
            "packageType": 0,
            "pStoreCode": "FRAKYFRAT01",
            "rStoreCode": "FRAKYFRAT01",
          },
          sortNum: 0,
          extMap: {
            freeCancel: false,
          },
          filterAggregations: { price: 10 },
        },
        {
          priceInfo: {
            currentDailyPrice: 9,
          },
          reference: {
            "bizVendorCode": "14085",
            "vendorCode": "SD0125",
            "vehicleCode": "10630",
            "packageId": "18847102",
            "packageType": 0,
            "skuId": 5366512,
          },
          sortNum: 1,
          extMap: {
            freeCancel: false,
          },
          filterAggregations: { price: 10 },
        },
      ],
      nextVendorPriceList: [],
      expected: [{
        priceInfo: {
          currentDailyPrice: 10,
        },
        reference: {
          "bizVendorCode": "14085",
          "vendorCode": "SD0125",
          "vehicleCode": "10630",
          "packageId": "18847102",
          "packageType": 0,
          "pStoreCode": "FRAKYFRAT01",
          "rStoreCode": "FRAKYFRAT01",
        },
        sortNum: 0,
        extMap: {
          freeCancel: false,
        },
        filterAggregations: { price: 10 },
      }, {
        priceInfo: {
          currentDailyPrice: 9,
        },
        reference: {
          "bizVendorCode": "14085",
          "vendorCode": "SD0125",
          "vehicleCode": "10630",
          "packageId": "18847102",
          "packageType": 0,
          "skuId": 5366512,
        },
        sortNum: 1,
        extMap: {
          freeCancel: false,
        },
        filterAggregations: { price: 10 },
      }],
    },
  ])('测试 concatVendorPrice', ({ preVendorPriceList, nextVendorPriceList, expected }) => {
    expect(concatVendorPrice(preVendorPriceList, nextVendorPriceList)).toEqual(expected);
  });
});



describe('appendResPatch function', () => {
  it('should return nextResponse if preResponse is an empty object', () => {
    const preResponse = {};
    const nextResponse = {
      baseResponse: {
        code: 'C201',
      },
      vehicleList: [],
      productGroups: [],
    };

    const result = appendResPatch(preResponse, nextResponse);

    expect(result).toEqual(nextResponse);
  });

  it('should swap preResponse and nextResponse if their codes match specific conditions', () => {
    const preResponse = {
      baseResponse: {
        code: 'C200',
      },
      vehicleList: [],
      productGroups: [],
    };
    const nextResponse = {
      baseResponse: {
        code: 'C201',
      },
      vehicleList: [],
      productGroups: [],
    };

    const result = appendResPatch(preResponse, nextResponse);

    expect(result).toEqual({
      ...nextResponse,
      productGroups: [], // Expected combined product groups based on your business logic
    });
  });

});


describe('upDateMergeVehicleExtendInfos', () => {
  test('should return undefined when res.mergeVehicleExtendInfos is not defined', () => {
    const lastRes = {};
    const res = {};

    const result = upDateMergeVehicleExtendInfos(lastRes, res);

    expect(result).toBeUndefined();
  });

  test('should return res.mergeVehicleExtendInfos when lastRes.mergeVehicleExtendInfos is not defined', () => {
    const lastRes = {};
    const res = {
      mergeVehicleExtendInfos: [{ group: 'A' }, { group: 'B' }],
    };

    const result = upDateMergeVehicleExtendInfos(lastRes, res);

    expect(result).toEqual([{ group: 'A' }, { group: 'B' }]);
  });

  test('should merge lastRes.mergeVehicleExtendInfos and res.mergeVehicleExtendInfos', () => {
    const lastRes = {
      mergeVehicleExtendInfos: [{ group: 'A', value: 1 }, { group: 'B', value: 2 }],
    };
    const res = {
      mergeVehicleExtendInfos: [{ group: 'A', value: 3 }, { group: 'C', value: 4 }],
    };

    const result = upDateMergeVehicleExtendInfos(lastRes, res);

    expect(result).toEqual([
      { group: 'A', value: 3 },
      { group: 'B', value: 2 },
      { group: 'C', value: 4 },
    ]);
  });

  test('should merge and update existing items in lastRes.mergeVehicleExtendInfos', () => {
    const lastRes = {
      mergeVehicleExtendInfos: [{ group: 'A', value: 1 }, { group: 'B', value: 2 }],
    };
    const res = {
      mergeVehicleExtendInfos: [{ group: 'A', value: 3 }, { group: 'B', value: 4 }],
    };

    const result = upDateMergeVehicleExtendInfos(lastRes, res);

    expect(result).toEqual([
      { group: 'A', value: 3 },
      { group: 'B', value: 4 },
    ]);
  });
});
