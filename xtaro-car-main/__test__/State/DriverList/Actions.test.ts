import {
  deleteDriver,
  fetchApiDriverList,
  fetchApiIdCardList,
  updateCurCertificates,
  maskerDriver,
  maskerDriverDone,
  unMaskerDriver,
  clear,
} from '../../../src/pages/xcar/State/DriverList/Actions';
import {
  DELETE_DRIVER,
  FETCH_DRIVERLIST,
  FETCH_IDCARDLIST,
  UPDATE_CUR_CERTIFICATES,
  MASK_DRIVER,
  MASK_DRIVER_DONE,
  UNMASK_DRIVER,
  CLEAR,
} from '../../../src/pages/xcar/State/DriverList/Types';

describe('DriverList Actions deleteDriver', () => {
  const data = {};
  const callback = () => { };
  const mockStateMap = [
    {
      data,
      callback,
      expected: {
        type: DELETE_DRIVER,
        data,
        callback,
      },
    },
  ];
  test.each(mockStateMap)(
    'deleteDriver check',
    ({ data, callback, expected }) => {
      const result = deleteDriver(data, callback);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Actions fetchApiDriverList', () => {
  const data = {};
  const mockStateMap = [
    {
      data,
      expected: {
        type: FETCH_DRIVERLIST,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'fetchApiDriverList check',
    ({ data, expected }) => {
      const result = fetchApiDriverList(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Actions fetchApiIdCardList', () => {
  const data = {};
  const mockStateMap = [
    {
      data,
      expected: {
        type: FETCH_IDCARDLIST,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'fetchApiIdCardList check',
    ({ data, expected }) => {
      const result = fetchApiIdCardList(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Actions updateCurCertificates', () => {
  const data = {};
  const mockStateMap = [
    {
      data,
      expected: {
        type: UPDATE_CUR_CERTIFICATES,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'updateCurCertificates check',
    ({ data, expected }) => {
      const result = updateCurCertificates(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Actions maskerDriver', () => {
  const data = {};
  const mockStateMap = [
    {
      data,
      expected: {
        type: MASK_DRIVER,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'maskerDriver check',
    ({ data, expected }) => {
      const result = maskerDriver(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Actions maskerDriverDone', () => {
  const data = {};
  const mockStateMap = [
    {
      data,
      expected: {
        type: MASK_DRIVER_DONE,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'maskerDriverDone check',
    ({ data, expected }) => {
      const result = maskerDriverDone(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Actions unMaskerDriver', () => {
  const data = {};
  const mockStateMap = [
    {
      data,
      expected: {
        type: UNMASK_DRIVER,
        data,
      },
    },
  ];
  test.each(mockStateMap)(
    'unMaskerDriver check',
    ({ data, expected }) => {
      const result = unMaskerDriver(data);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Actions clear', () => {
  const mockStateMap = [
    {
      expected: {
        type: CLEAR,
      },
    },
  ];
  test.each(mockStateMap)(
    'clear check',
    ({ expected }) => {
      const result = clear();
      expect(result).toEqual(expected);
    },
  );
});
