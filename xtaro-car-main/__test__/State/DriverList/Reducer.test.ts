import DriverListReducer, { getInitalState } from '../../../src/pages/xcar/State/DriverList/Reducer';
import {
  MASK_DRIVER,
  UNMASK_DRIVER,
  FETCH_DRIVERLIST,
  FETCH_DRIVERLIST_CALLBACK,
  DELETE_DRIVER,
  DELETE_DRIVER_CALLBACK,
  SET_STATUS,
  SELECT_DRIVER,
  FETCH_IDCARDLIST,
  FETCH_IDCARDLIST_CALLBACK,
  UPDATE_CUR_CERTIFICATES,
  UPDATE_DRIVERLIST_FOR_MODIFY,
  CLEAR,
} from '../../../src/pages/xcar/State/DriverList/Types';

describe('DriverList Reducer Test', () => {
  const initState = getInitalState();

  test('DriverList Reducer Init', () => {
    expect(DriverListReducer(undefined, {})).toEqual(initState);
  });

  describe('DriverList Reducer MASK_DRIVER', () => {
    test('MASK_DRIVER', () => {
        expect(DriverListReducer(initState, {
          type: MASK_DRIVER,
        })).toEqual({
          ...initState,
          isOnMask: true,
        });
    });
  });

  describe('DriverList Reducer UNMASK_DRIVER', () => {
    test('UNMASK_DRIVER', () => {
        expect(DriverListReducer(initState, {
          type: UNMASK_DRIVER,
        })).toEqual({
          ...initState,
          isOnMask: false,
        });
    });
  });

  describe('DriverList Reducer FETCH_DRIVERLIST', () => {
    test('FETCH_DRIVERLIST', () => {
        expect(DriverListReducer(initState, {
          type: FETCH_DRIVERLIST,
        })).toEqual({
          ...initState,
          isLoading: true,
        });
    });
  });

  describe('DriverList Reducer FETCH_DRIVERLIST_CALLBACK', () => {
    const passengerList = [
      {
        "nationalityName": "中国",
        "lastName": "CE",
        "firstName": "SHICHU",
        "age": 41,
        "nationality": "CN",
        "mobile": "13521002100",
        "countryCode": "86",
        "fullName": "测试出",
        "birthday": "1978-09-16",
        "passengerId": "13478",
        "certificateList": [
          {
            "certificateType": "1",
            "certificateNo": "340826199012231110"
          }
        ],
        "email": "<EMAIL>",
        "isCreditQualified": false
      },
      {
        "nationalityName": "中国",
        "lastName": "CTRIP",
        "firstName": "TEST",
        "age": 48,
        "nationality": "CN",
        "mobile": "13012345678",
        "countryCode": "86",
        "fullName": "测试",
        "birthday": "1971-05-26",
        "passengerId": "13455",
        "certificateList": [
          {
            "certificateType": "1",
            "certificateNo": "340826199012231110"
          }
        ],
        "email": "",
        "isCreditQualified": false
      }
    ];
    const tips = [
      {
        "style": "title",
        "content": "点击保存表示您已阅读并同意以下内容"
      }
    ];
    const maxAge = 10;
    const minAge = 20;
    const mockActionMap = [
      {
        data: {
          res: {
            passengerList: null,
            tips: null,
            maxAge: undefined,
            minAge: undefined,
          },
        },
        expected: {
          ...initState,
          isLoading: false,
          isLoadingSuccess: true,
          passengerList: [],
          tips: [],
          maxAge: 0,
          minAge: 0,
        },
      },
      {
        data: {
          res: {
            passengerList,
            tips,
            maxAge,
            minAge,
          },
        },
        expected: {
          ...initState,
          isLoading: false,
          isLoadingSuccess: true,
          passengerList: passengerList,
          tips: tips,
          maxAge: maxAge,
          minAge: minAge,
        },
      },
      {
        data: {
          res: {
            passengerList: undefined,
            tips: undefined,
            maxAge: undefined,
            minAge: undefined,
          },
        },
        expected: {
          ...initState,
          isLoading: false,
          isLoadingSuccess: true,
          passengerList: [],
          tips: [],
          maxAge: 0,
          minAge: 0,
        },
      },
      {
        data: {
          res: undefined,
        },
        expected: {
          ...initState,
          isLoading: false,
          isLoadingSuccess: true,
          passengerList: [],
          tips: [],
          maxAge: 0,
          minAge: 0,
        },
      }
    ];
    test.each(mockActionMap)(
      'FETCH_DRIVERLIST_CALLBACK check',
      ({ data, expected }) => {
        expect(DriverListReducer(initState, {
          type: FETCH_DRIVERLIST_CALLBACK,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('DriverList Reducer DELETE_DRIVER', () => {
    test('DELETE_DRIVER', () => {
        expect(DriverListReducer(initState, {
          type: DELETE_DRIVER,
        })).toEqual({
          ...initState,
          isLoading: false,
        });
    });
  });

  describe('DriverList Reducer DELETE_DRIVER_CALLBACK', () => {
    test('DELETE_DRIVER_CALLBACK', () => {
        expect(DriverListReducer(initState, {
          type: DELETE_DRIVER_CALLBACK,
        })).toEqual({
          ...initState,
          isLoading: false,
        });
    });
  });

  describe('DriverList Reducer SET_STATUS', () => {
    const mockActionMap = [
      {
        data: {
          isLoading: null,
          isFail: null,
          progress: null,
        },
        expected: {
          ...initState,
          isLoading: null,
          isFail: null,
          progress: null,
        },
      },
      {
        data: {
          isLoading: true,
          isFail: true,
          progress: true,
        },
        expected: {
          ...initState,
          isLoading: true,
          isFail: true,
          progress: true,
        },
      }
    ];
    test.each(mockActionMap)(
      'SET_STATUS check',
      ({ data, expected }) => {
        expect(DriverListReducer(initState, {
          type: SET_STATUS,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('DriverList Reducer SELECT_DRIVER', () => {
    const passenger = {
      "nationalityName": "中国",
      "lastName": "CE",
      "firstName": "SHICHU",
      "age": 41,
      "nationality": "CN",
      "mobile": "13521002100",
      "countryCode": "86",
      "fullName": "测试出",
      "birthday": "1978-09-16",
      "passengerId": "13478",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "<EMAIL>",
      "isCreditQualified": false
    };
    const mockActionMap = [
      {
        data: null,
        expected: {
          ...initState,
          passenger: null,
          unMaskPassenger: null,
        },
      },
      {
        data: passenger,
        expected: {
          ...initState,
          passenger: passenger,
          unMaskPassenger: passenger,
        },
      }
    ];
    test.each(mockActionMap)(
      'SELECT_DRIVER check',
      ({ data, expected }) => {
        expect(DriverListReducer(initState, {
          type: SELECT_DRIVER,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('DriverList Reducer FETCH_IDCARDLIST', () => {
    test('FETCH_IDCARDLIST', () => {
        expect(DriverListReducer(initState, {
          type: FETCH_IDCARDLIST,
        })).toEqual({
          ...initState,
          availableCertificates: [],
          errorInfo: {},
        });
    });
  });

  describe('DriverList Reducer FETCH_IDCARDLIST_CALLBACK', () => {
    const availableCertificates = ['1', '2', '7', '8'];
    const error = { message: '错误信息'};
    const mockActionMap = [
      {
        data: {
        },
        expected: {
          ...initState,
          availableCertificates: undefined,
          errorInfo: undefined,
        },
      },
      {
        data: {
          res: availableCertificates,
          error,
        },
        expected: {
          ...initState,
          availableCertificates,
          errorInfo: error,
        },
      }
    ];
    test.each(mockActionMap)(
      'FETCH_IDCARDLIST_CALLBACK check',
      ({ data, expected }) => {
        expect(DriverListReducer(initState, {
          type: FETCH_IDCARDLIST_CALLBACK,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('DriverList Reducer UPDATE_CUR_CERTIFICATES', () => {
    const curCertificates = {"13478" : '1'};
    const mockActionMap = [
      {
        data: null,
        expected: {
          ...initState,
          curCertificates: null,
        },
      },
      {
        data: curCertificates,
        expected: {
          ...initState,
          curCertificates,
        },
      }
    ];
    test.each(mockActionMap)(
      'UPDATE_CUR_CERTIFICATES check',
      ({ data, expected }) => {
        expect(DriverListReducer(initState, {
          type: UPDATE_CUR_CERTIFICATES,
          data,
        })).toEqual(expected);
      },
    );
  });

  describe('DriverList Reducer UPDATE_DRIVERLIST_FOR_MODIFY', () => {
    const passenger0 = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "10001",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const passenger1 = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "10002",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const returnPassenger = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "10010",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const passengerList = [
      passenger0,
      passenger1
    ];
    const curCertificates = {
      "200000": "1"
    };
    const certificateType1 = null;
    const certificateType2 = '7';
    const state1 : any = {
      ...initState,
      passengerList,
      curCertificates,
    }; 
    const state2 : any= {
      ...initState,
      passengerList: undefined,
      curCertificates: undefined,
    };
    const mockActionMap = [
      {
        data: {
          isdelete: true,
        },
        expected: {
          ...initState,
          curCertificates: {
            ...curCertificates,
          },
          passengerList,
        },
      },
      {
        data: {
          isdelete: false,
          certificateType: certificateType1,
          passenger: passenger1,
        },
        expected: {
          ...initState,
          curCertificates: {
            ...curCertificates,
            [passenger1.passengerId]: certificateType1,
          },
          passengerList: [
            passenger1,
            passenger0,
          ],
        },
      },
      {
        data: {
          isdelete: false,
          certificateType: certificateType2,
          passenger: returnPassenger,
        },
        expected: {
          ...initState,
          curCertificates: {
            ...curCertificates,
            [returnPassenger.passengerId]: certificateType2,
          },
          passengerList: [
            returnPassenger,
            passenger1,
            passenger0,
          ],
        },
      },
      {
        data: {
          isdelete: true,
          certificateType: '7',
          passenger: returnPassenger,
        },
        expected: {
          ...initState,
          curCertificates: {
            ...curCertificates,
          },
          passengerList,
        },
      },
      {
        data: {
          isdelete: true,
          certificateType: '7',
          passenger: passenger1,
        },
        expected: {
          ...initState,
          curCertificates: {
            ...curCertificates,
          },
          passengerList: [passenger0],
        },
      },
      {
        data: {
          
        },
        expected: {
          ...initState,
          curCertificates: {
            ...curCertificates,
            "undefined": ""
          },
          passengerList: [{}, passenger0],
        },
      },
      {
        data: {
          
        },
        mockState: state2,
        expected: {
          ...initState,
          curCertificates: {
            "undefined": ""
          },
          passengerList: [{}],
        },
      },
    ];
    test.each(mockActionMap)(
      'UPDATE_DRIVERLIST_FOR_MODIFY check',
      ({ data, expected, mockState = state1 }) => {
        const result = DriverListReducer(mockState, {
          type: UPDATE_DRIVERLIST_FOR_MODIFY,
          data,
        });
        expect(result).toEqual(expected);
      },
    );
  });

  describe('DriverList Reducer CLEAR', () => {
    const mockInitState = {
      ...initState,
      isLoading: true,
    };
    test('CLEAR check',() => {
        expect(DriverListReducer(initState, {
          type: CLEAR,
        })).toEqual(initState);
      },
    );

    test('CLEAR check',() => {
      expect(DriverListReducer(mockInitState, {
        type: CLEAR,
      })).toEqual(initState);
    },
  );
  });
})