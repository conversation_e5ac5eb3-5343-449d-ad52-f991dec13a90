import { runSaga } from 'redux-saga';
import {
  getOrderDetailParams,
  getProductParams,
  getDriverListReqParams,
  apiQueryDriverList,
  apiDeleteDriver,
  apiQueryIdCardList,
  selectDriver,
} from '../../../src/pages/xcar/State/DriverList/Logic';
import { takeEveryGeneratorFunction, recordSaga } from '../../testHelpers';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';
import { packageDriverListRes, packageIdCardListRes } from '../../../src/pages/xcar/State/DriverList/Mapper';
import {
  FETCH_DRIVERLIST,
  SET_STATUS,
  FETCH_DRIVERLIST_CALLBACK,
  DELETE_DRIVER,
  DELETE_DRIVER_CALLBACK,
  UPDATE_DRIVERLIST_FOR_MODIFY,
  SELECT_DRIVER,
  FETCH_IDCARDLIST,
  FETCH_IDCARDLIST_CALLBACK,
} from '../../../src/pages/xcar/State/DriverList/Types';
import { changeAllSelectInsurance } from '../../../src/pages/xcar/State/Product/Actions';
import {
  CHANGE_FORM,
  SET_PASSENGER_ERROR,
} from '../../../src/pages/xcar/State/Booking/Types';
import { SET_AUTH_TICKET } from '../../../src/pages/xcar/State/Sesame/Types';
import { UPDATE_ISFORCEUPDATE } from '../../../src/pages/xcar/State/DriverEdit/Types';
import { QUERY_PRICE_INFO } from '../../../src/pages/xcar/State/Product/Types';
import { Utils } from '../../../src/pages/xcar/Util/Index';
import * as CommonSelectors from '../../../src/pages/xcar/State/Common/Selectors';
import { queryDriverListExtraMapsParam } from '../../../src/pages/xcar/State/DriverList/Logic';

jest.mock('../../../src/pages/xcar/State/Common/Selectors', () => ({ __esModule: true, ...jest.requireActual('../../../src/pages/xcar/State/Common/Selectors') }));

const reference = {
  "fType": 0,
  "isSelect": true,
  "noDepositFilter": {},
  "rCityId": 43,
  "rCType": 1,
  "vendorVehicleCode": "20051804",
  "pickWayInfo": 4,
  "pCityId": 43,
  "vcExtendRequest": { "vendorVehicleId": "0" },
  "pStoreNav": "店员免费接您至门店取还车",
  "vehicleCode": "0",
  "isdShareInfo": {
    "orginaltotal": 1299,
    "recommendOrder": 0,
    "mergeId": 0,
    "vdegree": "0",
    "rectype": 1,
    "totalDailyPrice": 387,
    "grantedcode": "",
    "isrec": false,
    "cvid": 67,
    "rentalamount": 773
  },
  "klbVersion": 1,
  "platform": 10,
  "bizVendorCode": "SD30147",
  "elct": 0,
  "pLevel": 2139,
  "gsDesc": "精选好货",
  "vehicleDegree": "0",
  "gsId": 0,
  "pCType": 1,
  "priceType": 1,
  "isEasyLife": true,
  "klbPId": 636,
  "rStoreNav": "店员免费接您至门店取还车",
  "priceVersion": "AUgB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXBRvbxjnVabj01m5jD62sttFmbBm4xa2DJ+xX55zm/rwqYHSrtxo8WipuP2JeL44j5yUlngGsblyvygFlWfV4YDaCD1/BAQu9rZUn/WD1rx8tUNyhK/kruOyrxLKONSxbcWZsGbjFrYMhG28pZk/axFBmHZHdw/kOgWrarH9jF7xS297j3F0oCzUcmBVv062x334xDsoPmvW8zbhSSEAVqxN1e/eaMzUua05rVZEj792glh7lK9p/iqLXCYweCHcML1fUqDW5yThTs7sx5Ik0exRDVUqf5d65vBxoSLkNe2Hn71LojuJBTTV0pzVE68Y9i11kRLC/9owZl/YyLdyDJ+y5f/LHQBDG8zgVa2AusUOg==",
  "alipay": false,
  "vendorCode": "30147",
  "productCode": "SD30147_0_100026430_100026430",
  "pLev": 2139,
  "adjustVersion": "",
  "sendTypeForPickOffCar": 0,
  "payMode": 2,
  "pRc": 0,
  "packageType": 1,
  "kPSId": 100026430,
  "age": 30,
  "rCoup": 0,
  "kRSId": 100026430,
  "packageId": "",
  "freeIllegalDeposit": false,
  "rLev": 2139,
  "pStoreCode": "100026430",
  "pickUpOnDoor": false,
  "aType": 0,
  "kVId": 30147,
  "sortInfo": { "p": "4", "s": "100.0", "c": "43", "v": "2083" },
  "kVehicleId": 67,
  "comPriceCode": "6380317e0a0c571a11f1072b",
  "dropOffOnDoor": false,
  "returnWayInfo": 4,
  "creditFreeCarDeposit": false,
  "hot": 0,
  "labels": [
    {
      "category": 3,
      "sortNum": 10000,
      "amountTitle": "已减331",
      "groupCode": "MarketGroup1353",
      "code": "30",
      "title": "平台补贴",
      "colorCode": "15",
      "mergeId": 0,
      "type": 3,
      "groupId": 1,
      "labelCode": "3759"
    }
  ],
  "hotType": 0,
  "rRc": 0,
  "klb": 1,
  "promtId": 0,
  "rStoreCode": "100026430",
  "vendorSupportZhima": false,
  "sendTypeForPickUpCar": 0,
  "skuId": 434883,
  "rLevel": 2139,
  "newEnergy": 0
};

jest.mock('../../../src/pages/xcar/State/OrderDetail/Selectors', () => {
  return {
    getVendorInfo: jest.fn(state => state.OrderDetail.vendorInfo),
    getPickupStore: jest.fn(state => state.OrderDetail.pickupStore),
  }
});
jest.mock('../../../src/pages/xcar/State/VendorList/Selectors', () => {
  return {
    getUniqueReference: jest.fn(state => state.OrderDetail.reference),
  }
});
describe('DriverList Logic getOrderDetailParams', () => {
  const vendorInfo = {
    "vendorID": 62494,
  };
  const pickupStore = {
    "storeID": 128348,
  };
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          vendorInfo,
          pickupStore,
        },
      },
      expected: {
        vendorCode: vendorInfo?.vendorID || '',
        storeId: pickupStore?.storeID || '',
      },
    },
    {
      state: {
        OrderDetail: {
        },
      },
      expected: {
        vendorCode: '',
        storeId: '',
      },
    },
  ];
  test.each(mockStateMap)(
    'getOrderDetailParams check',
    ({ state, expected }) => {
      const data = getOrderDetailParams(state);
      expect(data).toEqual(expected);
    },
  );
});


// jest.mock('../../../src/pages/xcar/Global/Cache/ProductSelectors', () => {
//   let isNullGetPickupStoreInfo = true;
//   let isNullGetVendorInfo = true;
//   return {
//     getProductReq: jest.fn(),
//     getPickupStoreInfo: jest.fn(() => {
//       const result = isNullGetPickupStoreInfo ? {} : {
//         "storeCode": 225861,
//       };
//       isNullGetPickupStoreInfo = false;
//       return result;
//     }),
//     getVendorInfo: jest.fn(() => {
//       const result = isNullGetVendorInfo ? {} : {
//         "vendorCode": 13039,
//       };
//       isNullGetVendorInfo = false;
//       return result;
//     }),
//   }
// });
jest.mock('../../../src/pages/xcar/State/Product/Selectors', () => {
  return {
    getCurInsPackageId: jest.fn(state => state.Product.curInsPackageId),
    getSelectedInsuranceId: jest.fn(state => state.Product.selectedInsuranceId),
  };
});
jest.mock('../../../src/pages/xcar/State/Product/Mappers', () => {
  return {
    getCurPriceInfo: jest.fn((curInsPackageId) => (curInsPackageId ? {
      "ageRestriction": {
        "minDriverAge": 10,
        "maxDriverAge": 20,
      }
    } : null)),
  }
});
describe('DriverList Logic getProductParams', () => {
  const curInsPackageId = 1792;
  const mockStateMap = [
    {
      state: {
        Product: {
          curInsPackageId,
        },
      },
      expected: {
        "storeId": "",
        "vendorCode": "",
        "minAge": 10,
        "maxAge": 20,
      },
    },
    {
      state: {
        Product: {
          curInsPackageId: '',
        },
      },
      expected: {
        "storeId": "",
        "vendorCode": "",
        "minAge": undefined,
        "maxAge": undefined,
      },
    },
  ];
  test.each(mockStateMap)(
    'getProductParams check',
    ({ state, expected }) => {
      const data = getProductParams(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Logic getDriverListReqParams', () => {
  const vendorInfo = {
    "vendorID": 62494,
  };
  const pickupStore = {
    "storeID": 128348,
  };
  const curInsPackageId = 1792;
  const mockStateMap = [
    {
      state: {
        OrderDetail: {
          vendorInfo,
          pickupStore,
        },
        Product: {
          curInsPackageId,
        },
      },
      from: 'ModifyOrder',
      expected: {
        vendorCode: vendorInfo?.vendorID || '',
        storeId: pickupStore?.storeID || '',
      },
    },
    {
      state: {
        OrderDetail: {
          vendorInfo,
          pickupStore,
        },
        Product: {
          curInsPackageId,
        },
      },
      from: '',
      expected: {
        "storeId": "",
        "vendorCode": "",
        "minAge": 10,
        "maxAge": 20,
      },
    },
  ];
  test.each(mockStateMap)(
    'getDriverListReqParams check',
    ({ state, from, expected }) => {
      const data = getDriverListReqParams(state, from);
      expect(data).toEqual(expected);
    },
  );
});

jest.mock('../../../src/pages/xcar/State/DriverList/Selectors', () => {
  return {
    getUnMaskPassenger: jest.fn(state => state.DriverList.unMaskPassenger),
    getPassenger: jest.fn(state => state.DriverList.passenger),
  }
});
jest.mock('../../../src/pages/xcar/State/VendorList/Selectors', () => {
  return {
    getUniqueReference: jest.fn(state => state.DriverList.reference),
  }
});
describe('DriverList Logic apiQueryDriverList', () => {
  test('测试正常调用', async () => {
    jest.spyOn(CommonSelectors, 'getQConfig').mockReturnValue({ personalInfoAuthCheck: true });
    const curInsPackageId = 1792;
    const unMaskPassenger = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "13455",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const res = {
      "baseResponse": {},
      "passengerList": [
        {
          "nationalityName": "中国",
          "lastName": "CE",
          "firstName": "SHICHU",
          "age": 41,
          "nationality": "CN",
          "mobile": "13521002100",
          "countryCode": "86",
          "fullName": "测试出",
          "birthday": "1978-09-16",
          "passengerId": "13478",
          "certificateList": [
            {
              "certificateType": "1",
              "certificateNo": "340826199012231110"
            }
          ],
          "email": "<EMAIL>",
          "isCreditQualified": false
        },
        {
          "nationalityName": "中国",
          "lastName": "CTRIP",
          "firstName": "TEST",
          "age": 48,
          "nationality": "CN",
          "mobile": "13012345678",
          "countryCode": "86",
          "fullName": "测试",
          "birthday": "1971-05-26",
          "passengerId": "13455",
          "certificateList": [
            {
              "certificateType": "1",
              "certificateNo": "340826199012231110"
            }
          ],
          "email": "",
          "isCreditQualified": false
        }
      ],
      "tips": [
        {
          "style": "title",
          "content": "点击保存表示您已阅读并同意以下内容"
        },
        {
          "style": "content",
          "content": "您已知晓您录入的驾驶员身份证件信息，将用于您预订的租车产品，并在使用时根据有关法律规定进行查验或留存…录入信息真实有效。携程将通过加密等方式保护此信息，并在预订过程中提供给为您实际提供服务的租车公司。"
        }
      ],
      "ResponseStatus": {
        "Extension": [
          {
            "Value": "2858981890528872490",
            "Id": "CLOGGING_TRACE_ID"
          },
          {
            "Value": "921822-0a023027-441609-32971",
            "Id": "RootMessageId"
          }
        ],
        "Ack": "Success",
        "Errors": [],
        "Timestamp": "/Date(1589794969191+0800)/"
      },
      "appResponseMap": {
        "networkCost": 167
      }
    };
    const dispatched : any = [];
    const fromurl = 'www.baidu.com';
    const isForceUpdate = false;
    const actionMock = {
      type: FETCH_DRIVERLIST,
      data: {
        isForceUpdate,
        fromurl,
      },
    };
    const logicFunc = takeEveryGeneratorFunction(apiQueryDriverList);
    jest.spyOn(CarFetch, 'queryDriverList').mockReturnValue(Promise.resolve(res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverList: {
            unMaskPassenger,
          },
          Product: {
            curInsPackageId,
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: UPDATE_ISFORCEUPDATE,
      data: {
        isForceUpdate: false,
      },
    });
    expect(dispatched[1]).toEqual({
      type: FETCH_DRIVERLIST_CALLBACK,
      data: {
        isError: false,
        res: packageDriverListRes(res),
        isLoading: false,
      },
    });
  });

  test('测试数据异常调用', async () => {
    const fromurl = 'www.baidu.com';
    const dispatched : any = [];
    const res = {};
    const actionMock = {
      type: FETCH_DRIVERLIST,
      data: {
        fromurl,
      },
    };
    jest.spyOn(CommonSelectors, 'getQConfig').mockReturnValue({ personalInfoAuthCheck: true });
    const logicFunc = takeEveryGeneratorFunction(apiQueryDriverList);
    jest.spyOn(CarFetch, 'queryDriverList').mockReturnValue(Promise.resolve(res));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverList: {
            unMaskPassenger: null,
          },
          Product: {
            curInsPackageId: 0,
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: UPDATE_ISFORCEUPDATE,
      data: {
        isForceUpdate: false,
      },
    });
    expect(dispatched[1]).toEqual({
      type: FETCH_DRIVERLIST_CALLBACK,
      data: {
        isError: false,
        res: packageDriverListRes(res),
        isLoading: false,
      },
    });
  });

  test('接口异常调用', async () => {
    const fromurl = 'www.baidu.com';
    jest.spyOn(CommonSelectors, 'getQConfig').mockReturnValue({ personalInfoAuthCheck: true });
    const exceptionError = new Error('apiQueryDriverList exception');
    const dispatched : any = [];
    const isForceUpdate = true;
    const actionMock = {
      type: FETCH_DRIVERLIST,
      data: {
        isForceUpdate,
        fromurl,
      },
    };
    const logicFunc = takeEveryGeneratorFunction(apiQueryDriverList);
    jest.spyOn(CarFetch, 'queryDriverList').mockReturnValue(new Promise(() => {
      throw exceptionError;
    }));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverList: {
            unMaskPassenger: {},
          },
          Product: {
            curInsPackageId: 0,
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: UPDATE_ISFORCEUPDATE,
      data: {
        isForceUpdate: false,
      },
    });
    expect(dispatched[1]).toEqual({
      type: FETCH_DRIVERLIST_CALLBACK,
      data: {
        isError: true,
        res: exceptionError,
        isLoading: false,
      },
    });
  });
});

describe('DriverList Logic apiDeleteDriver', () => {
  test('测试正常调用 删除的是选中的驾驶员', async () => {
    const passenger0 = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "10001",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const resPassenger = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "已删除",
      "birthday": "1971-05-26",
      "passengerId": "10009",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const dispatched : any = [];
    let callBackFlag = null;
    const callback = (data) => {
      callBackFlag = data;
    };
    const actionMock = {
      type: DELETE_DRIVER,
      data: passenger0,
      callback,
    };
    const logicFunc = takeEveryGeneratorFunction(apiDeleteDriver);
    jest.spyOn(CarFetch, 'deleteDriver').mockReturnValue(Promise.resolve(resPassenger));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverList: {
            passenger: passenger0,
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: DELETE_DRIVER_CALLBACK,
      data: {
        isError: false,
        res: resPassenger,
        isLoading: false,
      },
    });
    expect(dispatched[1]).toEqual({
      type: UPDATE_DRIVERLIST_FOR_MODIFY,
      data: {
        passenger: passenger0,
        isdelete: true,
      },
    });
    expect(dispatched[2]).toEqual({
      type: SET_AUTH_TICKET,
    });
    expect(dispatched[3]).toEqual({
      type: SELECT_DRIVER,
      data: {},
    });
    expect(callBackFlag).toEqual(resPassenger);
  });

  test('测试正常调用 删除的不是选中的驾驶员', async () => {
    const passenger0 = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "10001",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const passenger1 = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "10002",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const resPassenger = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "已删除",
      "birthday": "1971-05-26",
      "passengerId": "10009",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const dispatched : any = [];
    let callBackFlag = null;
    const callback = (data) => {
      callBackFlag = data;
    };
    const actionMock = {
      type: DELETE_DRIVER,
      data: passenger0,
      callback,
    };
    const logicFunc = takeEveryGeneratorFunction(apiDeleteDriver);
    jest.spyOn(CarFetch, 'deleteDriver').mockReturnValue(Promise.resolve(resPassenger));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverList: {
            passenger: passenger1,
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: DELETE_DRIVER_CALLBACK,
      data: {
        isError: false,
        res: resPassenger,
        isLoading: false,
      },
    });
    expect(dispatched[1]).toEqual({
      type: UPDATE_DRIVERLIST_FOR_MODIFY,
      data: {
        passenger: passenger0,
        isdelete: true,
      },
    });
    expect(dispatched[2]).toEqual({
      type: SET_AUTH_TICKET,
    });
    expect(callBackFlag).toEqual(resPassenger);
  });

  test('测试数据异常调用', async () => {
    const passenger0 = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "10001",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const resPassenger = {};
    const dispatched : any = [];
    let callBackFlag = null;
    const actionMock = {
      type: DELETE_DRIVER,
    };
    const logicFunc = takeEveryGeneratorFunction(apiDeleteDriver);
    jest.spyOn(CarFetch, 'deleteDriver').mockReturnValue(Promise.resolve(resPassenger));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverList: {
            passenger: null,
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: DELETE_DRIVER_CALLBACK,
      data: {
        isError: false,
        res: resPassenger,
        isLoading: false,
      },
    });
    expect(dispatched[1]).toEqual({
      type: UPDATE_DRIVERLIST_FOR_MODIFY,
      data: {
        passenger: undefined,
        isdelete: true,
      },
    });
    expect(dispatched[2]).toEqual({
      type: SET_AUTH_TICKET,
    });
    expect(callBackFlag).toEqual(null);
  });

  test('接口异常调用', async () => {
    const passenger0 = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "10001",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const exceptionError = new Error('deleteDriver exception');
    const dispatched : any = [];
    let callBackFlag = null;
    const callback = (data) => {
      callBackFlag = data;
    };
    const actionMock = {
      type: DELETE_DRIVER,
      data: passenger0,
      callback,
    };
    const logicFunc = takeEveryGeneratorFunction(apiDeleteDriver);
    jest.spyOn(CarFetch, 'deleteDriver').mockReturnValue(new Promise(() => {
      throw exceptionError;
    }));
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
        getState: () => ({
          DriverList: {
            passenger: passenger0,
          },
        }),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: DELETE_DRIVER_CALLBACK,
      data: {
        isError: true,
        res: exceptionError,
        isLoading: false,
      },
    });
    expect(callBackFlag).toEqual(exceptionError);
  });
});

describe('DriverList Logic apiQueryIdCardList', () => {
  test('测试正常调用', async () => {
    const res = {
      "iclist": [
        {
            "typename": "二代身份证",
            "idtype": "1"
        },
        {
            "typename": "护照",
            "idtype": "2"
        },
        {
            "typename": "回乡证",
            "idtype": "7"
        },
        {
            "typename": "台胞证",
            "idtype": "8"
        }
      ]
    };

    jest.spyOn(CarFetch, 'getIdCardList').mockReturnValue(Promise.resolve(res));

    const dispatched = await recordSaga(apiQueryIdCardList, {
      action: {
        type: FETCH_IDCARDLIST,
        data: {
          vendorCode: 12346,
          vendorId: 56789,
        },
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: FETCH_IDCARDLIST_CALLBACK,
      data: {
        isError: false,
        res: packageIdCardListRes(res),
        error: {},
        isLoading: false
      },
    });
  });

  test('测试数据异常调用', async () => {
    const res : any = {};
    jest.spyOn(CarFetch, 'getIdCardList').mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(apiQueryIdCardList, {
      action: {
        type: FETCH_DRIVERLIST,
        data: null,
      },
      state: {},
    });
    expect(dispatched[0]).toEqual({
      type: FETCH_IDCARDLIST_CALLBACK,
      data: {
        isError: false,
        res: packageIdCardListRes(res),
        error: {},
        isLoading: false
      },
    });
  });
});

describe('DriverList Logic selectDriver', () => {
  test('测试正常调用 不需要查询价格', async () => {
    const queryPriceInfo = false;
    const passenger = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "10001",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const dispatched : any = [];
    const actionMock = {
      type: SELECT_DRIVER,
      data: {
        ...passenger,
        queryPriceInfo,
      },
    };
    const logicFunc = takeEveryGeneratorFunction(selectDriver);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: CHANGE_FORM,
      data: [
        { type: 'mobilePhone', value: passenger.mobile || '', error: false },
        { type: 'email', value: passenger.email || '', error: false },
        { type: 'firstName', value: passenger.firstName || '', error: false },
        { type: 'lastName', value: passenger.lastName || '', error: false },
        { type: 'areaCode', value: passenger.countryCode || '86', error: false }
      ],
    });
    expect(dispatched[1]).toEqual({
      type: SET_PASSENGER_ERROR,
      data: false,
    });
  });

  test('测试正常调用 需要查询价格', async () => {
    const queryPriceInfo = true;
    const passenger = {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "10001",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    };
    const dispatched : any = [];
    const actionMock = {
      type: SELECT_DRIVER,
      data: {
        ...passenger,
        queryPriceInfo,
      },
    };
    const logicFunc = takeEveryGeneratorFunction(selectDriver);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: CHANGE_FORM,
      data: [
        { type: 'mobilePhone', value: passenger.mobile || '', error: false },
        { type: 'email', value: passenger.email || '', error: false },
        { type: 'firstName', value: passenger.firstName || '', error: false },
        { type: 'lastName', value: passenger.lastName || '', error: false },
        { type: 'areaCode', value: passenger.countryCode || '86', error: false }
      ],
    });
    expect(dispatched[1]).toEqual({
      type: SET_PASSENGER_ERROR,
      data: false,
    });
    expect(dispatched[2]).toEqual({
      type: QUERY_PRICE_INFO,
    });
  });

  test('测试正常调用 有自营险', async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValueOnce(true);
    const passenger = {
      nationalityName: '中国',
      lastName: 'CTRIP',
      firstName: 'TEST',
      age: 48,
      nationality: 'CN',
      mobile: '13012345678',
      countryCode: '86',
      fullName: '测试',
      birthday: '1971-05-26',
      passengerId: '10001',
      certificateList: [
        {
          certificateType: '1',
          certificateNo: '340826199012231110',
        },
      ],
      email: '',
      isCreditQualified: false,
    };
    const dispatched = await recordSaga(selectDriver, {
      action: {
        type: SELECT_DRIVER,
        data: {
          ...passenger,
        },
      },
      state: {
        Product: {
          selectedInsuranceId: ['123'],
        },
      },
    });
    expect(dispatched[2]).toEqual(changeAllSelectInsurance([]));
  });

  test('测试数据异常调用', async () => {
    const passenger : any = {};
    const dispatched : any = [];
    const actionMock = {
      type: SELECT_DRIVER,
    };
    const logicFunc = takeEveryGeneratorFunction(selectDriver);
    await runSaga(
      {
        dispatch: action => dispatched.push(action),
      },
      logicFunc,
      actionMock,
    );
    expect(dispatched[0]).toEqual({
      type: CHANGE_FORM,
      data: [
        { type: 'mobilePhone', value: passenger.mobile || '', error: false },
        { type: 'email', value: passenger.email || '', error: false },
        { type: 'firstName', value: passenger.firstName || '', error: false },
        { type: 'lastName', value: passenger.lastName || '', error: false },
        { type: 'areaCode', value: passenger.countryCode || '86', error: false }
      ],
    });
    expect(dispatched[1]).toEqual({
      type: SET_PASSENGER_ERROR,
      data: false,
    });
  });
});

describe('DriverList Logic queryDriverListExtraMapsParam', () => {
  test('qconfig personalInfoAuthCheck true', () => {
    jest.spyOn(CommonSelectors, 'getQConfig').mockReturnValue({ personalInfoAuthCheck: true });
    const data = queryDriverListExtraMapsParam({});
    expect(data).toEqual({
      extraMaps: { PIPA: 1 }
    });
  });
  test('qconfig personalInfoAuthCheck false', () => {
    jest.spyOn(CommonSelectors, 'getQConfig').mockReturnValue({ personalInfoAuthCheck: false });
    const data = queryDriverListExtraMapsParam({});
    expect(data).toEqual({});
  });
});
