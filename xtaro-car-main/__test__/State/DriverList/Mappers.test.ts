import {
  packageDriverListRes,
  packageIdCardListRes,
} from '../../../src/pages/xcar/State/DriverList/Mapper';

describe('DriverList Mappers packageDriverListRes', () => {
  const tips = [
    {
      "style": "title",
      "content": "点击保存表示您已阅读并同意以下内容"
    }
  ];
  const passengerList = [
    {
      "nationalityName": "中国",
      "lastName": "CE",
      "firstName": "SHICHU",
      "age": 41,
      "nationality": "CN",
      "mobile": "13521002100",
      "countryCode": "86",
      "fullName": "测试出",
      "birthday": "1978-09-16",
      "passengerId": "13478",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "<EMAIL>",
      "isCreditQualified": false
    },
    {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "13455",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    }
  ];
  const maxAge = 60;
  const minAge = 18;
  const mockData = [
    {
      data: {
      },
      expected: {
        passengerList: [],
        maxAge: 0,
        minAge: 0,
        tips: undefined,
      },
    },
    {
    data: {
      passengerList,
      maxAge,
      minAge,
      tips
    },
    expected: {
      passengerList,
      maxAge,
      minAge,
      tips
    },
  }];
  test.each(mockData)('DriverList Mappers packageDriverListRes', ({ data, expected }) => {
    expect(packageDriverListRes(data)).toEqual(expected);
  })
});

describe('DriverList Mappers packageIdCardListRes', () => {
  const iclist =  [
    {
        "typename": "二代身份证", 
        "idtype": "1"
    }, 
    {
        "typename": "护照", 
        "idtype": "2"
    }, 
    {
        "typename": "回乡证", 
        "idtype": "7"
    }, 
    {
        "typename": "台胞证", 
        "idtype": "8"
    }
  ];
  const mockData = [{
      data: {},
      expected: [],
    },
    {
      data: {
        iclist,
      },
      expected: ["1", "2", "7", "8"],
    }
  ];
  test.each(mockData)('DriverList Mappers packageIdCardListRes', ({ data, expected }) => {
    expect(packageIdCardListRes(data)).toEqual(expected);
  })
});