import { 
  getDriverInfo,
  getIsLoading,
  getIsLoadingSuccess,
  getIsFail,
  getPassengerList,
  getTips,
  getMaxAge,
  getMinAge,
  getPassenger,
  getUnMaskPassenger,
  getIsOnMask,
  getIsForceUpdate,
  getAvailableCertificates,
  getCurCertificates,
  getRecommendedPassengers,
  getPriceReqPassengerData,
  getPriceReqDefaultPassengerData,
  getPassengerIDCard,
  getPriceReqPassenger,
  getPassengerMasker,
  getDriverMaskInfo,
 } from '../../../src/pages/xcar/State/DriverList/Selectors';
 import { Utils } from '../../../src/pages/xcar/Util/Index';

describe('DriverList Selectors getDriverInfo', () => {
  const mockStateMap = [
    {
      state: {
        Booking: {
          driverInfo: {},
        },
      },
      expected: {},
    },
    {
      state: {
        Booking: {
          driverInfo: null,
        },
      },
      expected: null,
    },
    {
      state: {
        Booking: {
          driverInfo: {
            "type": "areaCode",
            "value": "",
            "error": false
          },
        },
      },
      expected: {
        "type": "areaCode",
        "value": "",
        "error": false
      },
    },
  ];
  test.each(mockStateMap)(
    'getDriverInfo check',
    ({ state, expected }) => {
      const data = getDriverInfo(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getIsLoading', () => {
  const mockStateMap = [
    {
      state: {
        DriverList: {
          isLoading: false,
        },
      },
      expected: false,
    },
    {
      state: {
        DriverList: {
          isLoading: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)(
    'getIsLoading check',
    ({ state, expected }) => {
      const data = getIsLoading(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getIsLoadingSuccess', () => {
  const mockStateMap = [
    {
      state: {
        DriverList: {
          isLoadingSuccess: false,
        },
      },
      expected: false,
    },
    {
      state: {
        DriverList: {
          isLoadingSuccess: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)(
    'getIsLoadingSuccess check',
    ({ state, expected }) => {
      const data = getIsLoadingSuccess(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getIsFail', () => {
  const mockStateMap = [
    {
      state: {
        DriverList: {
          isFail: false,
        },
      },
      expected: false,
    },
    {
      state: {
        DriverList: {
          isFail: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)(
    'getIsFail check',
    ({ state, expected }) => {
      const data = getIsFail(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getPassengerList', () => {
  const passengerList = [
    {
      "nationalityName": "中国",
      "lastName": "CE",
      "firstName": "SHICHU",
      "age": 41,
      "nationality": "CN",
      "mobile": "13521002100",
      "countryCode": "86",
      "fullName": "测试出",
      "birthday": "1978-09-16",
      "passengerId": "13478",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "<EMAIL>",
      "isCreditQualified": false
    },
    {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "13455",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false
    }
  ];
  const mockStateMap = [
    {
      state: {
        DriverList: {
          passengerList: [],
        },
      },
      expected: [],
    },
    {
      state: {
        DriverList: {
          passengerList,
        },
      },
      expected: passengerList,
    },
  ];
  test.each(mockStateMap)(
    'getPassengerList check',
    ({ state, expected }) => {
      const result = getPassengerList(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getTips', () => {
  const tips = [
    {
      "style": "title",
      "content": "点击保存表示您已阅读并同意以下内容"
    },
    {
      "style": "content",
      "content": "您已知晓您录入的驾驶员身份证件信息，将用于您预订的租车产品，并在使用时根据有关法律规定进行查验或留存…录入信息真实有效。携程将通过加密等方式保护此信息，并在预订过程中提供给为您实际提供服务的租车公司。"
    }
  ];
  const mockStateMap = [
    {
      state: {
        DriverList: {
          tips: [],
        },
      },
      expected: [],
    },
    {
      state: {
        DriverList: {
          tips,
        },
      },
      expected: tips,
    },
  ];
  test.each(mockStateMap)(
    'getTips check',
    ({ state, expected }) => {
      const result = getTips(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getMaxAge', () => {
  const mockStateMap = [
    {
      state: {
        DriverList: {
          maxAge: 0,
        },
      },
      expected: 0,
    },
    {
      state: {
        DriverList: {
          maxAge: 999999999,
        },
      },
      expected: 999999999,
    },
    {
      state: {
        DriverList: {
          maxAge: -999999999,
        },
      },
      expected: -999999999,
    },
  ];
  test.each(mockStateMap)(
    'getMaxAge check',
    ({ state, expected }) => {
      const data = getMaxAge(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getMinAge', () => {
  const mockStateMap = [
    {
      state: {
        DriverList: {
          minAge: 0,
        },
      },
      expected: 0,
    },
    {
      state: {
        DriverList: {
          minAge: 999999999,
        },
      },
      expected: 999999999,
    },
    {
      state: {
        DriverList: {
          minAge: -999999999,
        },
      },
      expected: -999999999,
    },
  ];
  test.each(mockStateMap)(
    'getMinAge check',
    ({ state, expected }) => {
      const data = getMinAge(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getPassenger', () => {
  const passenger = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "340826199012231110"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false
  };
  const mockStateMap = [
    {
      state: {
        DriverList: {
          passenger: {},
        },
      },
      expected: {},
    },
    {
      state: {
        DriverList: {
          passenger,
        },
      },
      expected: passenger,
    },
  ];
  test.each(mockStateMap)(
    'getPassenger check',
    ({ state, expected }) => {
      const result = getPassenger(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getUnMaskPassenger', () => {
  const unMaskPassenger = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "340826199012231110"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false
  };
  const mockStateMap = [
    {
      state: {
        DriverList: {
          unMaskPassenger: {},
        },
      },
      expected: {},
    },
    {
      state: {
        DriverList: {
          unMaskPassenger,
        },
      },
      expected: unMaskPassenger,
    },
  ];
  test.each(mockStateMap)(
    'getUnMaskPassenger check',
    ({ state, expected }) => {
      const result = getUnMaskPassenger(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getIsOnMask', () => {
  const mockStateMap = [
    {
      state: {
        DriverList: {
          isOnMask: false,
        },
      },
      expected: false,
    },
    {
      state: {
        DriverList: {
          isOnMask: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)(
    'getIsOnMask check',
    ({ state, expected }) => {
      const data = getIsOnMask(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getIsForceUpdate', () => {
  const mockStateMap = [
    {
      state: {
        DriverEdit: {
          isForceUpdate: false,
        },
      },
      expected: false,
    },
    {
      state: {
        DriverEdit: {
          isForceUpdate: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)(
    'getIsForceUpdate check',
    ({ state, expected }) => {
      const data = getIsForceUpdate(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getAvailableCertificates', () => {
  const availableCertificates = ['1', '2', '7', '8'];
  const mockStateMap = [
    {
      state: {
        DriverList: {
          availableCertificates: [],
        },
      },
      expected: [],
    },
    {
      state: {
        DriverList: {
          availableCertificates,
        },
      },
      expected: availableCertificates,
    },
  ];
  test.each(mockStateMap)(
    'getAvailableCertificates check',
    ({ state, expected }) => {
      const data = getAvailableCertificates(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getCurCertificates', () => {
  const curCertificates = {"13478" : '1'};
  const mockStateMap = [
    {
      state: {
        DriverList: {
          curCertificates: {},
        },
      },
      expected: {},
    },
    {
      state: {
        DriverList: {
          curCertificates,
        },
      },
      expected: curCertificates,
    },
  ];
  test.each(mockStateMap)(
    'getCurCertificates check',
    ({ state, expected }) => {
      const data = getCurCertificates(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getRecommendedPassengers', () => {
  const passengerList = [
    {
      "nationalityName": "中国",
      "lastName": "CE",
      "firstName": "SHICHU",
      "age": 41,
      "nationality": "CN",
      "mobile": "13521002100",
      "countryCode": "86",
      "fullName": "测试出",
      "birthday": "1978-09-16",
      "passengerId": "13478",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "<EMAIL>",
      "isCreditQualified": false,
      "isRecommend": true,
    },
    {
      "nationalityName": "中国",
      "lastName": "CTRIP",
      "firstName": "TEST",
      "age": 48,
      "nationality": "CN",
      "mobile": "13012345678",
      "countryCode": "86",
      "fullName": "测试",
      "birthday": "1971-05-26",
      "passengerId": "13455",
      "certificateList": [
        {
          "certificateType": "1",
          "certificateNo": "340826199012231110"
        }
      ],
      "email": "",
      "isCreditQualified": false,
      "isRecommend": true,
    }
  ];
  const mockStateMap = [
    {
      state: {
        DriverList: {
          passengerList: [],
        },
      },
      expected: [],
    },
    {
      state: {
        DriverList: {
          passengerList,
        },
      },
      expected: passengerList,
    },
  ];
  test.each(mockStateMap)(
    'getRecommendedPassengers check',
    ({ state, expected }) => {
      const result = getRecommendedPassengers(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getPriceReqPassengerData', () => {
  const certificate = {
    certificateType: '1', 
    certificateNo: '610112199506300525',
  };
  const passenger = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "340826199012231110"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false,
    "isRecommend": true,
  };
  const passenger2 : any = undefined;
  const mockStateMap = [
    {
      state: {
        passenger: {},
        certificate: undefined,
      },
      expected: null,
    },
    {
      state: {
        passenger,
        certificate,
      },
      expected: {
        userName: passenger.fullName,
        ...certificate,
        ...passenger,
      },
    },
    {
      state: {
        passenger,
        certificate: undefined,
      },
      expected: {
        userName: passenger.fullName,
        ...passenger,
      },
    },
    {
      state: {
        passenger,
        certificate: {},
      },
      expected: {
        userName: passenger.fullName,
        ...passenger,
      },
    },
    {
      state: {
        passenger: passenger2,
        certificate: {},
      },
      expected: null,
    },
  ];

  test.each(mockStateMap)(
    'getPriceReqPassengerData check',
    ({ state, expected }) => {
      const { certificate, passenger} = state; 
      const result = getPriceReqPassengerData(passenger, certificate);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getPriceReqDefaultPassengerData', () => {
  const idCardTypes = [
    {idCardType: 1, idCardName: '身份证'},
    {idCardType: 2, idCardName: '护照'},
    {idCardType: 7, idCardName: '回乡证'},
    {idCardType: 8, idCardName: '台胞证'}
  ];
  const passenger = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "340826199012231110"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false,
    "isRecommend": true,
  };
  const mockStateMapOSD = [
    {
      state: {
        passenger: {},
        idCardTypes,
      },
      expected: null,
    },
    {
      state: {
        passenger,
        idCardTypes,
      },
      expected: {
        userName: passenger.fullName,
        ...passenger,
      },
    }
  ];
  test.each(mockStateMapOSD)(
    'getPriceReqDefaultPassengerData check OSD',
    ({ state, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => false);
      const { idCardTypes, passenger} = state; 
      const result = getPriceReqDefaultPassengerData(passenger, idCardTypes);
      expect(result).toEqual(expected);
    },
  );
  const mockStateMapISD = [
    {
      state: {
        passenger: {},
        idCardTypes,
      },
      expected: null,
    },
    {
      state: {
        passenger,
        idCardTypes,
      },
      expected: {
        userName: passenger.fullName,
        ...passenger.certificateList[0],
        ...passenger,
      },
    },
    {
      state: {
        passenger,
        idCardTypes: [],
      },
      expected: {
        userName: passenger.fullName,
        ...passenger,
      },
    }
  ];
  test.each(mockStateMapISD)(
    'getPriceReqDefaultPassengerData check ISD',
    ({ state, expected }) => {
      jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
      const { idCardTypes, passenger} = state; 
      const result = getPriceReqDefaultPassengerData(passenger, idCardTypes);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getPassengerIDCard', () => {
  const passenger = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "340826199012231110"
      },
      {
        "certificateType": "2",
        "certificateNo": "4353463466"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false,
    "isRecommend": true,
  };
  const passenger2 : any = undefined;
  const availableCertificates = ['1', '2', '7', '8'];
  const curCertificates = {"13478" : '1'};
  const mockStateMap = [
    {
      state: {
        DriverList: {
          passenger: {},
          curCertificates,
          availableCertificates,
        },
      },
      expected: undefined,
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates: {},
          availableCertificates: [],
        },
      },
      expected: undefined,
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates: {},
          availableCertificates,
        },
      },
      expected: passenger.certificateList[0],
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates,
          availableCertificates : [],
        },
      },
      expected: undefined,
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates,
          availableCertificates,
        },
      },
      expected: passenger.certificateList[0],
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates: {"13478" : '7'},
          availableCertificates,
        },
      },
      expected: undefined,
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates: {"13478" : '2'},
          availableCertificates,
        },
      },
      expected: passenger.certificateList[1],
    },
    {
      state: {
        DriverList: {
          passenger: passenger2,
          curCertificates: {"13478" : '2'},
          availableCertificates,
        },
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getPassengerIDCard check',
    ({ state, expected }) => {
      const result = getPassengerIDCard(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getPriceReqPassenger', () => {
  const availableCertificates = ['1', '2', '7', '8'];
  const curCertificates = {"13478" : '1'};
  const passenger = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "340826199012231110"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false,
    "isRecommend": true,
  };
  const mockStateMap = [
    {
      state: {
        DriverList: {
          passenger: {},
          curCertificates,
          availableCertificates,
        },
      },
      expected: null,
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates: {},
          availableCertificates: [],
        },
      },
      expected: {
        userName: passenger.fullName,
        ...passenger,
      },
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates: {},
          availableCertificates,
        },
      },
      expected: {
        userName: passenger.fullName,
        ...passenger.certificateList[0],
        ...passenger,
      },
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates,
          availableCertificates : [],
        },
      },
      expected: {
        userName: passenger.fullName,
        ...passenger,
      },
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates,
          availableCertificates,
        },
      },
      expected: {
        userName: passenger.fullName,
        ...passenger.certificateList[0],
        ...passenger,
      },
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates: {"13478" : '7'},
          availableCertificates,
        },
      },
      expected: {
        userName: passenger.fullName,
        ...passenger,
      },
    },
    {
      state: {
        DriverList: {
          passenger,
          curCertificates: {"13478" : '2'},
          availableCertificates,
        },
      },
      expected: {
        userName: passenger.fullName,
        ...passenger.certificateList[1],
        ...passenger,
      },
    },
  ];

  test.each(mockStateMap)(
    'getPriceReqPassenger check',
    ({ state, expected }) => {
      const result = getPriceReqPassenger(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getPassengerMasker', () => {
  const passenger = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "340826199012231110"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false,
    "isRecommend": true,
  };
  const passenger2 = {
    ...passenger,
    certificateList: undefined,
  };
  const noIdCardPassenger = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "2",
        "certificateNo": "44545454545"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false,
    "isRecommend": true,
  };
  const exceptMaskPassenger = {
    "nationalityName": "中国",
    "lastName": "CE",
    "firstName": "SHICHU",
    "age": 41,
    "nationality": "CN",
    "mobile": "13521002100",
    "countryCode": "86",
    "fullName": "测试出",
    "birthday": "1978-09-16",
    "passengerId": "13478",
    "certificateList": [
      {
        "certificateType": "1",
        "certificateNo": "34082619****231110"
      }
    ],
    "email": "<EMAIL>",
    "isCreditQualified": false,
    "isRecommend": true,
  };
  const mockStateMap = [
    {
      state: {
        DriverList: {
          isOnMask: false,
          passenger: null,
        },
      },
      expected: null,
    },
    {
      state: {
        DriverList: {
          isOnMask: true,
          passenger: {},
        },
      },
      expected: {},
    },
    {
      state: {
        DriverList: {
          isOnMask: true,
          passenger: passenger,
        },
      },
      expected: exceptMaskPassenger,
    },
    {
      state: {
        DriverList: {
          isOnMask: true,
          passenger: noIdCardPassenger,
        },
      },
      expected: noIdCardPassenger,
    },
    {
      state: {
        DriverList: {
          isOnMask: true,
          passenger: passenger2,
        },
      },
      expected: passenger2,
    },
  ];

  test.each(mockStateMap)(
    'getPassengerMasker check',
    ({ state, expected }) => {
      const result = getPassengerMasker(state);
      expect(result).toEqual(expected);
    },
  );
});

describe('DriverList Selectors getDriverMaskInfo', () => {
  const driverInfo = [
    {type: 'mobilePhone', value: '15319935821', error: false},
    {type: 'flightNumber', value: '', error: false},
    {type: 'areaCode', value: '86', error: false},
  ];
  const noPhoneDriverInfo = [
    {type: 'flightNumber', value: '', error: false},
    {type: 'areaCode', value: '86', error: false},
  ];
  const exceptMaskDriverInfo = [
    {type: 'mobilePhone', value: '153****5821', error: false},
    {type: 'flightNumber', value: '', error: false},
    {type: 'areaCode', value: '86', error: false},
  ];
  const mockStateMap = [
    {
      state: {
        DriverList: {
          isOnMask: false,
        },
        Booking: {
          driverInfo: null,
        },
      },
      expected: null,
    },
    {
      state: {
        DriverList: {
          isOnMask: true,
        },
        Booking: {
          driverInfo: [],
        },
      },
      expected: [],
    },
    {
      state: {
        DriverList: {
          isOnMask: true,
        },
        Booking: {
          driverInfo,
        },
      },
      expected: exceptMaskDriverInfo,
    },
    {
      state: {
        DriverList: {
          isOnMask: true,
        },
        Booking: {
          driverInfo: noPhoneDriverInfo,
        },
      },
      expected: noPhoneDriverInfo,
    },
  ];

  test.each(mockStateMap)(
    'getDriverMaskInfo check',
    ({ state, expected }) => {
      const result = getDriverMaskInfo(state);
      expect(result).toEqual(expected);
    },
  );
});
