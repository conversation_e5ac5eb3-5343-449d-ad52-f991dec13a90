import { getCountryId } from '../../../src/pages/xcar/State/CountryInfo/Selectors';

describe('CountryInfo Selectors getCountryId', () => {
  const mockStateMap = [
    {
      state: {
        CountryInfo: {
          countryId: 20,
        },
      },
      expected: 20,
    },
  ];
  test.each(mockStateMap)('getCountryId check', ({ state, expected }) => {
    const data = getCountryId(state);
    expect(data).toEqual(expected);
  });
});
