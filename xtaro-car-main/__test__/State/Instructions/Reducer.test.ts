import InstructionsReducer, {
  getInitialState,
} from '../../../src/pages/xcar/State/Instructions/Reducer';
import {
  SET_MODALS_VISIBLE,
  LIMIT_RULE_POP_VISIBLE,
} from '../../../src/pages/xcar/State/Instructions/Types';

describe('Instructions Reducer Test', () => {
  const initState = getInitialState();

  test('Instructions Reducer Init', () => {
    expect(InstructionsReducer(undefined, {})).toEqual(initState);
  });

  describe('Instructions Reducer SET_MODALS_VISIBLE', () => {
    test('SET_MODALS_VISIBLE: renewTipModal: { visible: true }', () => {
      expect(
        InstructionsReducer(initState, {
          type: SET_MODALS_VISIBLE,
          data: {
            renewTipModal: { visible: true },
          },
        }),
      ).toEqual({
        ...initState,
        modalsVisible: {
          renewTipModal: { visible: true },
        },
      });
    });

    test('SET_MODALS_VISIBLE: renewTipModal: { visible: false }', () => {
      expect(
        InstructionsReducer(initState, {
          type: SET_MODALS_VISIBLE,
          data: {
            renewTipModal: { visible: false },
          },
        }),
      ).toEqual({
        ...initState,
        modalsVisible: {
          renewTipModal: { visible: false },
        },
      });
    });
  });

  describe('Instructions Reducer LIMIT_RULE_POP_VISIBLE', () => {
    test('LIMIT_RULE_POP_VISIBLE: visible: true', () => {
      expect(
        InstructionsReducer(initState, {
          type: LIMIT_RULE_POP_VISIBLE,
          data: {
            visible: true,
          },
        }),
      ).toEqual({
        ...initState,
        limitPopVisible: true,
      });
    });

    test('LIMIT_RULE_POP_VISIBLE: visible: false', () => {
      expect(
        InstructionsReducer(initState, {
          type: LIMIT_RULE_POP_VISIBLE,
          data: {
            visible: false,
          },
        }),
      ).toEqual({
        ...initState,
        limitPopVisible: false,
      });
    });
  });
});
