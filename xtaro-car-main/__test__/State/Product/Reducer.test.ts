import reducer, {
  getInitalState,
  selectPackage,
} from '../../../src/pages/xcar/State/Product/Reducer';
import * as Types from '../../../src/pages/xcar/State/Product/Types';
import * as ProductMapper from '../../../src/pages/xcar/State/Product/Mappers';
import { GetAB } from '../../../src/pages/xcar/Util/Index';

jest.mock('../../../src/pages/xcar/State/Product/Mappers', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/State/Product/Mappers'),
}));

jest.mock('../../../src/pages/xcar/Util/CarABTesting/GetAB', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/Util/CarABTesting/GetAB'),
}));

describe('test reducer', () => {
  test('测试 SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE', () => {
    const data = reducer(getInitalState(), {
      type: Types.SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
      data: true,
    });
    expect(data.depositRateDescriptionModalVisible).toEqual(true);
  });
  test('测试 SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE', () => {
    const data = reducer(getInitalState(), {
      type: Types.SET_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
      data: false,
    });
    expect(data.depositRateDescriptionModalVisible).toEqual(false);
  });
  test('测试 SET_MATERIALS_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE', () => {
    const data = reducer(getInitalState(), {
      type: Types.SET_MATERIALS_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
      data: true,
    });
    expect(data.materialsDepositRateDescriptionModalVisible).toEqual(true);
  });
  test('测试 SET_MATERIALS_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE', () => {
    const data = reducer(getInitalState(), {
      type: Types.SET_MATERIALS_DEPOSIT_RATE_DESCRIPTION_MODAL_VISIBLE,
      data: false,
    });
    expect(data.materialsDepositRateDescriptionModalVisible).toEqual(false);
  });
});

describe('selectPackage', () => {
  const initialState = getInitalState();
  const mockSelectPackageData = {
    curBomCode: 'LAXZE01_10619_FRFB_SLDW_Taxes_Taxes_ULM_0_0',
    payMode: 2,
    curInsPackageId: '192',
  };

  test('走主接口获取额外设备', () => {
    jest.spyOn(ProductMapper, 'getCurProductInfo').mockReturnValue({
      equipments: [
        {
          equipmentType: 1,
          equipmentCode: '7',
          equipmentName: '婴儿座椅',
          equipmentDesc: '通常适用于一周岁以下，体重约0-10公斤的婴儿',
          ageFrom: 0,
          ageFromUnit: '个月',
          ageTo: 12,
          ageToUnit: '个月',
          localTotalPrice: 97.93,
          localDailyPrice: 13.99,
          localCurrencyCode: 'USD',
          currentCurrencyCode: 'CNY',
          currentTotalPrice: 710,
          currentDailyPrice: 101,
          payMode: 1,
        },
      ],
    });
    const result = selectPackage(initialState, mockSelectPackageData);

    expect(result).toEqual(
      expect.objectContaining({
        curEquipments: [
          {
            equipmentType: 1,
            equipmentCode: '7',
            equipmentName: '婴儿座椅',
            equipmentDesc: '通常适用于一周岁以下，体重约0-10公斤的婴儿',
            ageFrom: 0,
            ageFromUnit: '个月',
            ageTo: 12,
            ageToUnit: '个月',
            localTotalPrice: 97.93,
            localDailyPrice: 13.99,
            localCurrencyCode: 'USD',
            currentCurrencyCode: 'CNY',
            currentTotalPrice: 710,
            currentDailyPrice: 101,
            payMode: 1,
          },
        ],
      }),
    );
  });

  test('走单独接口获取额外设备', () => {
    const initialState = {
      ...getInitalState(),
      equipmentInfos: [
        {
          equipments: [
            {
              equipmentType: 1,
              equipmentCode: '7',
              equipmentName: '婴儿座椅',
              equipmentDesc: '通常适用于一周岁以下，体重约0-10公斤的婴儿',
              ageFrom: 0.0,
              ageFromUnit: '个月',
              ageTo: 12.0,
              ageToUnit: '个月',
              localTotalPrice: 97.93,
              localDailyPrice: 13.99,
              localCurrencyCode: 'USD',
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 710.0,
              currentDailyPrice: 101.0,
              payMode: 1,
            },
            {
              equipmentType: 2,
              equipmentCode: '8',
              equipmentName: '儿童座椅',
              equipmentDesc: '通常适用于9个月-6周岁，体重约9-18.2公斤的儿童',
              ageFrom: 9.0,
              ageFromUnit: '个月',
              ageTo: 6.0,
              ageToUnit: '岁',
              localTotalPrice: 97.93,
              localDailyPrice: 13.99,
              localCurrencyCode: 'USD',
              currentCurrencyCode: 'CNY',
              currentTotalPrice: 710.0,
              currentDailyPrice: 101.0,
              payMode: 1,
            },
          ],
          bomGroupCode: 'LAXZE01_10619_ALI_FRFB_SLDW_Taxes_Taxes_ULM_0_0',
        },
      ],
    };
    jest.spyOn(ProductMapper, 'getCurProductInfo').mockReturnValue({
      equipments: [],
      bomGroupCode: 'LAXZE01_10619_ALI_FRFB_SLDW_Taxes_Taxes_ULM_0_0',
    });
    const result = selectPackage(initialState, mockSelectPackageData);

    expect(result).toEqual(
      expect.objectContaining({
        curEquipments: [
          {
            equipmentType: 1,
            equipmentCode: '7',
            equipmentName: '婴儿座椅',
            equipmentDesc: '通常适用于一周岁以下，体重约0-10公斤的婴儿',
            ageFrom: 0.0,
            ageFromUnit: '个月',
            ageTo: 12.0,
            ageToUnit: '个月',
            localTotalPrice: 97.93,
            localDailyPrice: 13.99,
            localCurrencyCode: 'USD',
            currentCurrencyCode: 'CNY',
            currentTotalPrice: 710.0,
            currentDailyPrice: 101.0,
            payMode: 1,
          },
          {
            equipmentType: 2,
            equipmentCode: '8',
            equipmentName: '儿童座椅',
            equipmentDesc: '通常适用于9个月-6周岁，体重约9-18.2公斤的儿童',
            ageFrom: 9.0,
            ageFromUnit: '个月',
            ageTo: 6.0,
            ageToUnit: '岁',
            localTotalPrice: 97.93,
            localDailyPrice: 13.99,
            localCurrencyCode: 'USD',
            currentCurrencyCode: 'CNY',
            currentTotalPrice: 710.0,
            currentDailyPrice: 101.0,
            payMode: 1,
          },
        ],
      }),
    );
  });
});

describe('Product Reducer QUERY_LICENSE_POLICY', () => {
  const initalState = {
    ...getInitalState(),
    driverLicenseItems: [1, 2, 3],
    curDriverLicense: {},
    placeHoldTips: '这是提示',
  };
  const mockActionMap = [
    {
      data: {},
      expected: {
        ...initalState,
        driverLicenseItems: [],
        curDriverLicense: null,
        placeHoldTips: '',
      },
    },
  ];
  test.each(mockActionMap)(
    'QUERY_LICENSE_POLICY check',
    ({ data, expected }) => {
      expect(
        reducer(initalState, {
          type: Types.QUERY_LICENSE_POLICY,
          data,
        }),
      ).toEqual(expected);
    },
  );
});

describe('Product Reducer QUERY_LICENSE_POLICY_CALLBACK', () => {
  const initalState = {
    ...getInitalState(),
    driverLicenseItems: [1, 2, 3],
    curDriverLicense: {},
    placeHoldTips: '这是提示',
  };
  const mockActionMap = [
    {
      data: {
        driverLicenseItems: [2, 3],
        curDriverLicense: {
          title: '中国大陆驾照',
          tips: '',
          statuses: 1,
          selected: false,
          code: 'CDL',
          requirements: [
            '中国大陆驾照 + ',
            '驾照国际翻译认证件/当地语言公证件',
          ],
          areaCode: 'CN',
        },
        placeHoldTips: '这是提示',
      },
      expected: {
        ...initalState,
        driverLicenseItems: [2, 3],
        curDriverLicense: {
          title: '中国大陆驾照',
          tips: '',
          statuses: 1,
          selected: false,
          code: 'CDL',
          requirements: [
            '中国大陆驾照 + ',
            '驾照国际翻译认证件/当地语言公证件',
          ],
          areaCode: 'CN',
        },
        placeHoldTips: '这是提示',
      },
    },
    {
      data: {},
      expected: {
        ...initalState,
        driverLicenseItems: undefined,
        curDriverLicense: undefined,
        placeHoldTips: undefined,
      },
    },
  ];
  test.each(mockActionMap)(
    'QUERY_LICENSE_POLICY_CALLBACK check',
    ({ data, expected }) => {
      expect(
        reducer(initalState, {
          type: Types.QUERY_LICENSE_POLICY_CALLBACK,
          data,
        }),
      ).toEqual(expected);
    },
  );
});

describe('Product Reducer SELECT_CUR_DRIVER_LICENSE', () => {
  const initalState = {
    ...getInitalState(),
    curDriverLicense: { name: '站三' },
  };
  const mockActionMap = [
    {
      data: {
        title: '中国大陆驾照',
        tips: '',
        statuses: 1,
        selected: false,
        code: 'CDL',
        requirements: ['中国大陆驾照 + ', '驾照国际翻译认证件/当地语言公证件'],
        areaCode: 'CN',
      },
      expected: {
        ...initalState,
        curDriverLicense: {
          title: '中国大陆驾照',
          tips: '',
          statuses: 1,
          selected: false,
          code: 'CDL',
          requirements: [
            '中国大陆驾照 + ',
            '驾照国际翻译认证件/当地语言公证件',
          ],
          areaCode: 'CN',
        },
      },
    },
    {
      expected: {
        ...initalState,
        curDriverLicense: undefined,
      },
    },
  ];
  test.each(mockActionMap)(
    'SELECT_CUR_DRIVER_LICENSE check',
    ({ data, expected }) => {
      expect(
        reducer(initalState, {
          type: Types.SELECT_CUR_DRIVER_LICENSE,
          data,
        }),
      ).toEqual(expected);
    },
  );
});
