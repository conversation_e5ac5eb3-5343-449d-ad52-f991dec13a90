import { getProductTraceData } from '../../../src/pages/xcar/State/Product/Method';
import ProductReqAndResData from '../../../src/pages/xcar/Global/Cache/ProductReqAndResData';

describe('Product Method getProductTraceData', () => {
  const mockData = [
    {
      productReq: {
        reference: {
          skuId: 1234,
          bizVendorCode: 1400023,
          pStoreCode: 23424,
          rStoreCode: 3434,
        },
      },
      productRes: {
        vendorInfo: {
          vendorCode: 'SD222222',
          vendorName: '富君租车',
        },
        vehicleInfo: {
          vehicleCode: '343434',
        },
      },
      expected: {
        ifCalabi: true,
        pStoreCode: 23424,
        pStoreId: 23424,
        rStoreCode: 3434,
        rStoreId: 3434,
        skuId: 1234,
        packageSellingRuleId: undefined,
        vehicleCode: '343434',
        vendorId: 1400023,
        vendorCode: 'SD222222',
        vendorName: '富君租车',
      },
    },
    {
      expected: {
        ifCalabi: false,
        pStoreCode: undefined,
        rStoreCode: undefined,
        vehicleCode: undefined,
        vendorCode: undefined,
        vendorName: undefined,
      },
    },
  ];
  test.each(mockData)(
    'Product Method getProductTraceData',
    ({ productReq, productRes, expected }) => {
      ProductReqAndResData.setData(
        ProductReqAndResData.keyList.productReq,
        productReq,
      );
      ProductReqAndResData.setData(
        ProductReqAndResData.keyList.productRes,
        productRes,
      );
      expect(getProductTraceData()).toEqual(expected);
    },
  );
});
