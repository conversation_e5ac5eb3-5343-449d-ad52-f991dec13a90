import { Log<PERSON><PERSON> } from '../../../src/pages/xcar/Constants/Index';
import { LogProductInfo } from '../../../src/pages/xcar/State/Product/UBTLog';
import { CarLog } from '../../../src/pages/xcar/Util/Index';
const mockBaseResData = {
  vendorInfo: {
    vendorName: 'Vendor A',
    bizVendorCode: '123',
    vendorCode: 'V001',
  },
  vehicleInfo: {
    name: 'Vehicle A',
    vehicleCode: 'V001',
    groupSubClassCode: 'G001',
    groupSubName: 'Subgroup A',
    groupName: 'Group A',
    groupCode: 'G001',
    isSpecialized: true,
  },
  baseResponse: {
    extMap: {
      isKarabi: '1',
      rentalDays: 5,
    },
  },
  commentInfo: {
    overallRating: 4.5,
    commentCount: 10,
  },
  productDetails: [
    {
      insPackageId: 'P001',
      productInfoList: [
        {
          priceInfoList: [
            { pkgSellingRuleId: 'S001' },
            { pkgSellingRuleId: 'S002' },
          ],
          packageItems: [{ code: 'I001' }, { code: 'I002' }],
        },
      ],
    },
  ],
  packageInfos: [
    {
      insPackageId: 'P001',
      defaultBomCode: 'B001',
      packageName: 'Package A',
    },
  ],
};
jest.mock('../../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  getBaseResData: jest.fn(() => mockBaseResData),
}));
describe('LogProductInfo', () => {
  it('should log product information', () => {
    const mockLogTrace = jest.spyOn(CarLog, 'LogTrace');

    LogProductInfo();

    expect(mockLogTrace).toHaveBeenCalledWith({
      key: LogKey.car_trip_detail_package_expo,
      info: {
        info: {
          vendorName: 'Vendor A',
          vendorId: '123',
          vendorcode: 'V001',
          vehicleName: 'Vehicle A',
          vehicleCode: 'V001',
          subGroupId: 'G001',
          subGroupName: 'Subgroup A',
          groupName: 'Group A',
          groupId: 'G001',
          ifCalabi: true,
          useDays: 5,
          commentScore: 4.5,
          commentCnt: 10,
          package: [
            {
              insPackageId: 'P001',
              packageId: ['S001', 'S002'],
              packageItem: ['I001', 'I002'],
              defaultBomCode: 'B001',
              packageName: 'Package A',
            },
          ],
          isSpecialized: true,
        },
      },
    });
  });
});
