import * as Types from '../../../src/pages/xcar/State/Product/Types';
import { SELECT_DRIVER } from '../../../src/pages/xcar/State/DriverList/Types';
import { CarFetch, CarLog, GetAB, Utils } from '../../../src/pages/xcar/Util/Index';
import { recordSaga } from '../../testHelpers';
import * as ProductSelectors from '../../../src/pages/xcar/Global/Cache/ProductSelectors';
import { ApiResCode, LogKey } from '../../../src/pages/xcar/Constants/Index';
import {
  queryEquipmentInfo,
  queryEquipmentInfoCallBack,
} from '../../../src/pages/xcar/State/Product/Actions';
import {
  queryProductInfo,
  logPriceLoad,
  queryEquipmentInfoLogic,
  queryLicencePolicyLogic,
} from '../../../src/pages/xcar/State/Product/Logic';

const queryLicencePolicyRes = require('../../../__mocks__/restful/18631/queryLicencePolicy/OSD/20231204.json');

jest.mock('../../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));

jest.mock('../../../src/pages/xcar/Util/CarABTesting/GetAB', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/Util/CarABTesting/GetAB'),
}));

afterEach(() => {
  jest.clearAllMocks();
});

const stateRentalLocation = {
  pickUp: {
    cid: 2,
    cname: '上海',
    country: '中国',
    realcountry: '中国',
    isDomestic: true,
    version: 3,
    area: {
      id: '47650',
      name: '上海南站',
      ename: '',
      lat: '31.153133',
      lng: '121.429457',
      type: 2,
      typename: '火车',
    },
    sortIndex: 5,
    isFromPosition: true,
  },
  dropOff: {
    cid: 2,
    cname: '上海',
    country: '中国',
    realcountry: '中国',
    isDomestic: true,
    version: 3,
    area: {
      id: '47650',
      name: '上海南站',
      ename: '',
      lat: '31.153133',
      lng: '121.429457',
      type: 2,
      typename: '火车',
    },
    sortIndex: 5,
    isFromPosition: true,
  },
  isShowDropOff: false,
};
const stateRentalDate = {
  pickUp: {
    dateTime: '2023-02-15 18:00',
  },
  dropOff: {
    dateTime: '2023-02-19 18:00',
  },
};

const state = {
  LocationAndDate: {
    rentalLocation: stateRentalLocation,
    rentalDate: stateRentalDate,
  },
  Booking: {
    driverInfo: {
      name: '测试',
      email: '',
      telphone:
        'd492e309ad4397fe9768cf2eea0e92905f1c2faca90ce24ac59052e5dbb7214d',
      areaCode: '+86',
      flightNo: '',
      iDCardType: 1,
      iDCardNo: '2103********24',
      encrypIDCardNo: '210381Ns6SnIxGrH24#',
      distributionMobile: '1561TV71116',
      distributionEmail: '',
      decryptIDCardNo: '210381199405065924',
      decryptTelphone: '15618081116',
    },
  },
  DriverList: {
    passenger: {
      nationalityName: '中国',
      lastName: 'CTRIP',
      firstName: 'TEST',
      age: 48,
      nationality: 'CN',
      mobile: '13012345678',
      countryCode: '86',
      fullName: '测试',
      birthday: '1971-05-26',
      passengerId: '10002',
      certificateList: [
        {
          certificateType: '1',
          certificateNo: '340826199012231110',
        },
      ],
      email: '',
      isCreditQualified: false,
    },
  },
  Product: {
    isFail: false,
  },
};
const response = {
  baseResponse: {
    isSuccess: true,
  },
};

describe('测试 queryProductInfo Logic ', () => {
  const testFn = async (response, data?) => {
    const queryProductInfoAPI = jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockReturnValueOnce(Promise.resolve(response));
    const dispatched = await recordSaga(queryProductInfo, {
      action: {
        type: Types.QUERY_PRODUCT,
        data,
      },
      state,
    });
    jest
      .spyOn(ProductSelectors, 'getProductResSuccess')
      .mockImplementation(() => true);
    expect(queryProductInfoAPI).toBeCalled();
    return dispatched;
  };
  test('测试 queryProductInfo Logic 正常调用', async () => {
    await testFn(response, { reset: true });
  });

  test('测试境外额外设备调用', async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
    jest
      .spyOn(ProductSelectors, 'getProductResSuccess')
      .mockImplementation(() => true);
    const queryProductInfoAPI = jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockReturnValueOnce(Promise.resolve(response));
    const dispatched = await recordSaga(queryProductInfo, {
      action: {
        type: Types.QUERY_PRODUCT,
        data: { reset: true },
      },
      state,
    });
    expect(queryProductInfoAPI).toBeCalled();
    expect(dispatched[3]).toEqual(
      queryEquipmentInfo({
        productReqParams: expect.anything(),
      }),
    );
  });
});

describe('测试 queryEquipmentInfo Logic ', () => {
  test('测试境外额外设备调用', async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
    const queryEquipmentInfoAPI = jest
      .spyOn(CarFetch, 'queryEquipmentInfo')
      .mockReturnValueOnce(
        Promise.resolve({
          response: {
            baseResponse: {
              isSuccess: true,
            },
            equipmentInfos: [],
          },
        }),
      );
    const dispatched = await recordSaga(queryEquipmentInfoLogic, {
      action: {
        type: Types.QUERY_EQUIPMENT_INFO,
        data: {},
      },
      state,
    });
    expect(queryEquipmentInfoAPI).toBeCalled();
    expect(dispatched).toEqual([
      queryEquipmentInfoCallBack({
        equipmentInfos: [],
      }),
    ]);
  });
});

describe('logPriceLoad', () => {
  test('should not call CarLog.LogTrace and CarLog.LogTraceDev if res is falsy', () => {
    // Create mocks for CarLog.LogTrace and CarLog.LogTraceDev functions
    const LogTrace = jest.spyOn(CarLog, 'LogTrace');
    const LogTraceDev = jest.spyOn(CarLog, 'LogTraceDev');

    // Call the logPriceLoad function with res as falsy
    logPriceLoad({
      isError: false,
      success: true,
      param: {},
      res: null,
    });

    // Assert that neither CarLog.LogTrace nor CarLog.LogTraceDev were called
    expect(LogTrace).toHaveBeenCalled();
    expect(LogTraceDev).toHaveBeenCalled();
  });

  it('should set errorCode to ApiResCode.TraceCode.E1004 when isError is true and res is undefined', () => {
    const mockCarLog = jest.spyOn(CarLog, 'LogTrace');

    logPriceLoad({ isError: true, success: false, param: {}, res: undefined });

    expect(mockCarLog).toBeCalledWith({
      key: LogKey.c_car_trace_product_price_load,
      info: expect.objectContaining({
        errorCode: ApiResCode.TraceCode.E1004,
      }),
    });
  });

  it('should set errorCode to ApiResCode.TraceCode.E1001 when isError is true and res is undefined', () => {
    const mockCarLog = jest.spyOn(CarLog, 'LogTrace');

    logPriceLoad({ isError: true, success: false, param: {}, res: {} });

    expect(mockCarLog).toBeCalledWith({
      key: LogKey.c_car_trace_product_price_load,
      info: expect.objectContaining({
        errorCode: ApiResCode.TraceCode.E1001,
      }),
    });
  });
});

describe('测试 queryLicencePolicyLogic Logic ', () => {
  test('测试正常调用', async () => {
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockReturnValue(Promise.resolve(queryLicencePolicyRes));

    const dispatched = await recordSaga(queryLicencePolicyLogic, {
      action: {
        type: Types.QUERY_LICENSE_POLICY,
        data: {
          requestId: '100025527-0a068a24-472928-155880',
          nationalityCode: 'CN',
        },
      },
      state: {
        DriverList: {
          passenger: {
            nationalityName: '中国',
            lastName: 'CTRIP',
            firstName: 'TEST',
            age: 48,
            nationality: 'CN',
            mobile: '13012345678',
            countryCode: '86',
            fullName: '测试',
            birthday: '1971-05-26',
            passengerId: '10002',
            certificateList: [
              {
                certificateType: '1',
                certificateNo: '340826199012231110',
              },
            ],
            email: '',
            isCreditQualified: false,
          },
        },
      },
    });
    expect(dispatched[0]).toEqual({
      type: Types.QUERY_LICENSE_POLICY_CALLBACK,
      data: {
        driverLicenseItems: [
          {
            title: '美国驾照',
            tips: '不支持该驾照租车',
            statuses: 0,
            selected: false,
            code: 'ODL',
            requirements: ['美国驾照 + ', '国际驾照'],
            areaCode: 'US',
          },
          {
            title: '中国大陆驾照',
            tips: '',
            statuses: 1,
            selected: false,
            code: 'CDL',
            requirements: [
              '中国大陆驾照 + ',
              '驾照国际翻译认证件/当地语言公证件',
            ],
            areaCode: 'CN',
          },
          {
            title: '中国香港驾照',
            tips: '',
            statuses: 1,
            selected: true,
            code: 'HKDL',
            requirements: ['仅需中国香港驾照'],
            areaCode: 'HK',
          },
          {
            title: '中国澳门驾照',
            tips: '',
            statuses: 1,
            selected: false,
            code: 'MCDL',
            requirements: ['仅需中国澳门驾照'],
            areaCode: 'OM',
          },
          {
            title: '中国台湾驾照',
            tips: '',
            statuses: 1,
            selected: false,
            code: 'TWDL',
            requirements: ['仅需中国台湾驾照'],
            areaCode: 'TW',
          },
        ],
        placeHoldTips: '请选择驾照',
        curDriverLicense: {
          title: '中国香港驾照',
          tips: '',
          statuses: 1,
          selected: true,
          code: 'HKDL',
          requirements: ['仅需中国香港驾照'],
          areaCode: 'HK',
        },
      },
    });
    expect(dispatched[1]).toEqual({
      type: SELECT_DRIVER,
      data: {
        nationalityName: '中国',
        lastName: 'CTRIP',
        firstName: 'TEST',
        age: 48,
        nationality: 'CN',
        mobile: '13012345678',
        countryCode: '86',
        fullName: '测试',
        birthday: '1971-05-26',
        passengerId: '10002',
        certificateList: [
          { certificateType: '1', certificateNo: '340826199012231110' },
        ],
        email: '',
        isCreditQualified: false,
      },
    });
  });

  test('测试数据异常调用', async () => {
    const res : any = {};
    jest.spyOn(CarFetch, 'queryLicencePolicy').mockReturnValue(Promise.resolve(res));
    const dispatched = await recordSaga(queryLicencePolicyLogic, {
      action: {
        type: Types.QUERY_LICENSE_POLICY,
        data: {
          requestId: '100025527-0a068a24-472928-155880',
          nationalityCode: 'CN',
        },
      },
      state: {
        DriverList: {
          passenger: {
            nationalityName: '中国',
            lastName: 'CTRIP',
            firstName: 'TEST',
            age: 48,
            nationality: 'CN',
            mobile: '13012345678',
            countryCode: '86',
            fullName: '测试',
            birthday: '1971-05-26',
            passengerId: '10002',
            certificateList: [
              {
                certificateType: '1',
                certificateNo: '340826199012231110',
              },
            ],
            email: '',
            isCreditQualified: false,
          },
        },
      },
    });
    expect(dispatched).toEqual([]);
  });
});
