import { getRentalMustReadCancelRuleInfo } from '../../../src/pages/xcar/State/Product/Mappers';
import * as ProductSelectors from '../../../src/pages/xcar/Global/Cache/ProductSelectors';
import { Utils } from '../../../src/pages/xcar/Util/Index';

jest.mock('../../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../../src/pages/xcar/Global/Cache/ProductSelectors')
}));

describe('Product Mappers', () => {
  test('getRentalMustReadCancelRuleInfo', () => {
    const state = {
      Product: {
        curInsPackageId: 11,
      },
    };
    jest.spyOn(ProductSelectors, 'getPriceResData').mockImplementation(() => {
      return {
        "cancelRuleInfo": {
          "title": "取消政策",
          "subTitle": "支付完成至2024-08-11 12:00可免费取消",
          "description": "注意：均为当地时间",
          "complexSubTitle": {
            "contentStyle": "1",
            "stringObjs": [{
              "content": "支付完成至2024-08-11 12:00可免费取消",
              "style": "5"
            }, {
              "content": "，",
              "style": "6"
            }, {
              "content": "支付完成至2024-08-11 12:00可免费取消",
              "style": "6"
            }]
          },
          "code": "FreeCancel",
          "type": 300,
          "showFree": true,
          "items": [{
            "title": "支付完成至2024-08-11 12:00",
            "subTitle": "支付完成至取车时间",
            "description": "可免费取消",
            "showFree": true
          }, {
            "title": "2024-08-11 12:00后",
            "subTitle": "取车时间后",
            "description": "取消将收取全部租金作为违约金",
            "showFree": false
          }]
        },
        "confirmInfo": {
          "title": "立即确认",
          "subTitle": "立即确认",
          "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。",
          "code": "ConfirmImmediately",
          "type": 301,
          "isInstantConfirm": true
        },
      };
    });
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => true);
    const rentalMustReadCancelRuleInfo = getRentalMustReadCancelRuleInfo(state);
    expect(rentalMustReadCancelRuleInfo).toEqual({
      subTitle: '支付完成至2024-08-11 12:00可免费取消',
      description: '注意：均为当地时间',
      items: [
        {
          showFree: true,
          title: '支付完成至2024-08-11 12:00',
          subTitle: '支付完成至取车时间',
          description: '可免费取消',
        },
        {
          showFree: false,
          title: '2024-08-11 12:00后',
          subTitle: '取车时间后',
          description: '取消将收取全部租金作为违约金',
        },
      ],
    });
  });
});
