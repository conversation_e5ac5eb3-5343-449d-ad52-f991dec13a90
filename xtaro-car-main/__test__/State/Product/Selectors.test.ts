import {
  getCrossPolicy,
  getIsShowTravelLimit,
  getTravelLimitCrossCountryData,
  getTravelLimitSelectedResult,
  getInsuranceCompareModalVisible,
  getDriverLicenseItems,
  getCurDriverLicense,
  getPlaceHoldTips,
} from '../../../src/pages/xcar/State/Product/Selectors';

import ProductReqAndResData from '../../../src/pages/xcar/Global/Cache/ProductReqAndResData';

describe('Product Selectors getCrossPolicy', () => {
  ProductReqAndResData.setData(ProductReqAndResData.keyList.productRes, {
    crossPolicy: {
      title: '旅行限制',
      notes: [
        '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。',
      ],
      crossLocationsInfos: [
        {
          crossType: 1,
          crossTypeName: '跨岛政策',
          summaryTitle: '允许跨岛',
          summaryPolicies: ['我是允许跨岛政策描述'],
        },
        {
          crossType: 2,
          crossTypeName: '跨州政策',
          summaryTitle: '条件跨州',
          summaryPolicies: ['我是条件跨州政策描述'],
        },
        {
          title: '选择计划前往的国家',
          subTitle: '门店支持在以下区域跨境使用车辆:',
          crossType: 3,
          crossTypeName: '跨境政策',
          summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
          locations: [
            {
              name: '日本',
              status: 1,
              statusName: '允许跨境',
              firstChar: 'R',
              regionId: '1111',
              isSelected: false,
            },
            {
              name: '俄罗斯',
              status: 1,
              statusName: '允许跨境',
              firstChar: 'E',
              regionId: '11112222',
              isSelected: false,
            },
            {
              name: '美国',
              status: 2,
              statusName: '条件跨境',
              firstChar: 'M',
              regionId: '1111222244',
              isSelected: false,
            },
            {
              name: '新加坡',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'X',
              regionId: '11112222555',
              isSelected: false,
              policy: '条件跨境政策1',
            },
            {
              name: '越南',
              status: 2,
              statusName: '条件跨境',
              firstChar: 'Y',
              regionId: '11112222555',
              isSelected: false,
              policy: '条件跨境政策2',
            },
            {
              name: '缅甸',
              status: 2,
              statusName: '条件跨境',
              firstChar: 'M',
              regionId: '11112222555',
              isSelected: false,
              policy: '条件跨境政策2',
            },
          ],
        },
      ],
    },
  });
  const data = getCrossPolicy();
  const expected = {
    title: '旅行限制',
    notes: [
      '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。',
    ],
    crossLocationsInfos: [
      {
        crossType: 1,
        crossTypeName: '跨岛政策',
        summaryTitle: '允许跨岛',
        summaryPolicies: ['我是允许跨岛政策描述'],
      },
      {
        crossType: 2,
        crossTypeName: '跨州政策',
        summaryTitle: '条件跨州',
        summaryPolicies: ['我是条件跨州政策描述'],
      },
      {
        title: '选择计划前往的国家',
        subTitle: '门店支持在以下区域跨境使用车辆:',
        crossType: 3,
        crossTypeName: '跨境政策',
        summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
        locations: [
          {
            name: '日本',
            status: 1,
            statusName: '允许跨境',
            firstChar: 'R',
            regionId: '1111',
            isSelected: false,
          },
          {
            name: '俄罗斯',
            status: 1,
            statusName: '允许跨境',
            firstChar: 'E',
            regionId: '11112222',
            isSelected: false,
          },
          {
            name: '美国',
            status: 2,
            statusName: '条件跨境',
            firstChar: 'M',
            regionId: '1111222244',
            isSelected: false,
          },
          {
            name: '新加坡',
            status: 3,
            statusName: '不允许跨境',
            firstChar: 'X',
            regionId: '11112222555',
            isSelected: false,
            policy: '条件跨境政策1',
          },
          {
            name: '越南',
            status: 2,
            statusName: '条件跨境',
            firstChar: 'Y',
            regionId: '11112222555',
            isSelected: false,
            policy: '条件跨境政策2',
          },
          {
            name: '缅甸',
            status: 2,
            statusName: '条件跨境',
            firstChar: 'M',
            regionId: '11112222555',
            isSelected: false,
            policy: '条件跨境政策2',
          },
        ],
      },
    ],
  };
  expect(data).toEqual(expected);
});

describe('Product Selectors getIsShowTravelLimit', () => {
  ProductReqAndResData.setData(ProductReqAndResData.keyList.productRes, {
    crossPolicy: {
      title: '旅行限制',
      notes: [
        '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。',
      ],
      crossLocationsInfos: [
        {
          crossType: 1,
          crossTypeName: '跨岛政策',
          summaryTitle: '允许跨岛',
          summaryPolicies: ['我是允许跨岛政策描述'],
        },
        {
          crossType: 2,
          crossTypeName: '跨州政策',
          summaryTitle: '条件跨州',
          summaryPolicies: ['我是条件跨州政策描述'],
        },
        {
          title: '选择计划前往的国家',
          subTitle: '门店支持在以下区域跨境使用车辆:',
          crossType: 3,
          crossTypeName: '跨境政策',
          summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
          locations: [
            {
              name: '日本',
              status: 1,
              statusName: '允许跨境',
              firstChar: 'R',
              regionId: '1111',
              isSelected: false,
            },
            {
              name: '俄罗斯',
              status: 1,
              statusName: '允许跨境',
              firstChar: 'E',
              regionId: '11112222',
              isSelected: false,
            },
            {
              name: '美国',
              status: 2,
              statusName: '条件跨境',
              firstChar: 'M',
              regionId: '1111222244',
              isSelected: false,
            },
            {
              name: '新加坡',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'X',
              regionId: '11112222555',
              isSelected: false,
              policy: '条件跨境政策1',
            },
            {
              name: '越南',
              status: 2,
              statusName: '条件跨境',
              firstChar: 'Y',
              regionId: '11112222555',
              isSelected: false,
              policy: '条件跨境政策2',
            },
            {
              name: '缅甸',
              status: 2,
              statusName: '条件跨境',
              firstChar: 'M',
              regionId: '11112222555',
              isSelected: false,
              policy: '条件跨境政策2',
            },
          ],
        },
      ],
    },
  });
  const data = getIsShowTravelLimit();
  const expected = true;
  expect(data).toEqual(expected);
});

describe('Product Selectors getTravelLimitCrossCountryData', () => {
  ProductReqAndResData.setData(ProductReqAndResData.keyList.productRes, {
    crossPolicy: {
      title: '旅行限制',
      notes: [
        '若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。',
      ],
      crossLocationsInfos: [
        {
          crossType: 1,
          crossTypeName: '跨岛政策',
          summaryTitle: '允许跨岛',
          summaryPolicies: ['我是允许跨岛政策描述'],
        },
        {
          crossType: 2,
          crossTypeName: '跨州政策',
          summaryTitle: '条件跨州',
          summaryPolicies: ['我是条件跨州政策描述'],
        },
        {
          title: '选择计划前往的国家',
          subTitle: '门店支持在以下区域跨境使用车辆:',
          crossType: 3,
          crossTypeName: '跨境政策',
          summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
          locations: [
            {
              name: '日本',
              status: 1,
              statusName: '允许跨境',
              firstChar: 'R',
              regionId: '1111',
              isSelected: false,
            },
            {
              name: '俄罗斯',
              status: 1,
              statusName: '允许跨境',
              firstChar: 'E',
              regionId: '11112222',
              isSelected: false,
            },
            {
              name: '美国',
              status: 2,
              statusName: '条件跨境',
              firstChar: 'M',
              regionId: '1111222244',
              isSelected: false,
            },
            {
              name: '新加坡',
              status: 3,
              statusName: '不允许跨境',
              firstChar: 'X',
              regionId: '11112222555',
              isSelected: false,
              policy: '条件跨境政策1',
            },
            {
              name: '越南',
              status: 2,
              statusName: '条件跨境',
              firstChar: 'Y',
              regionId: '11112222555',
              isSelected: false,
              policy: '条件跨境政策2',
            },
            {
              name: '缅甸',
              status: 2,
              statusName: '条件跨境',
              firstChar: 'M',
              regionId: '11112222555',
              isSelected: false,
              policy: '条件跨境政策2',
            },
          ],
        },
      ],
    },
  });

  const data = getTravelLimitCrossCountryData();
  const expected = {
    title: '选择计划前往的国家',
    subTitle: '门店支持在以下区域跨境使用车辆:',
    crossType: 3,
    crossTypeName: '跨境政策',
    summaryPolicies: ['若您的行程中涉及跨境，请提前选择'],
    locations: [
      {
        name: '日本',
        status: 1,
        statusName: '允许跨境',
        firstChar: 'R',
        regionId: '1111',
        isSelected: false,
      },
      {
        name: '俄罗斯',
        status: 1,
        statusName: '允许跨境',
        firstChar: 'E',
        regionId: '11112222',
        isSelected: false,
      },
      {
        name: '美国',
        status: 2,
        statusName: '条件跨境',
        firstChar: 'M',
        regionId: '1111222244',
        isSelected: false,
      },
      {
        name: '新加坡',
        status: 3,
        statusName: '不允许跨境',
        firstChar: 'X',
        regionId: '11112222555',
        isSelected: false,
        policy: '条件跨境政策1',
      },
      {
        name: '越南',
        status: 2,
        statusName: '条件跨境',
        firstChar: 'Y',
        regionId: '11112222555',
        isSelected: false,
        policy: '条件跨境政策2',
      },
      {
        name: '缅甸',
        status: 2,
        statusName: '条件跨境',
        firstChar: 'M',
        regionId: '11112222555',
        isSelected: false,
        policy: '条件跨境政策2',
      },
    ],
  };
  expect(data).toEqual(expected);
});

describe('Product Selectors getTravelLimitSelectedResult', () => {
  const mockStateMap = [
    {
      state: {
        Product: {
          travelLimitSelectedResult: [
            {
              code: '12345',
              groupCode: 'CrossPlace',
              isSelected: true,
              name: '美国',
            },
            {
              code: '123456',
              groupCode: 'CrossPlace',
              isSelected: false,
              name: '法国',
            },
          ],
        },
      },
      expected: [
        {
          code: '12345',
          groupCode: 'CrossPlace',
          isSelected: true,
          name: '美国',
        },
        {
          code: '123456',
          groupCode: 'CrossPlace',
          isSelected: false,
          name: '法国',
        },
      ],
    },
  ];
  test.each(mockStateMap)(
    'getTravelLimitSelectedResult check',
    ({ state, expected }) => {
      const data = getTravelLimitSelectedResult(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Product Selectors getInsuranceCompareModalVisible', () => {
  const mockStateMap = [
    {
      state: {
        Product: {
          insuranceCompareModalVisible: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Product: {
          insuranceCompareModalVisible: false,
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)(
    'getInsuranceCompareModalVisible check',
    ({ state, expected }) => {
      const data = getInsuranceCompareModalVisible(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Product Selectors getDriverLicenseItems', () => {
  const mockStateMap = [
    {
      state: {
        Product: {
          driverLicenseItems: [1, 2, 3],
        },
      },
      expected: [1, 2, 3],
    },
    {
      state: {
        Product: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getDriverLicenseItems check',
    ({ state, expected }) => {
      const data = getDriverLicenseItems(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Product Selectors getCurDriverLicense', () => {
  const mockStateMap = [
    {
      state: {
        Product: {
          curDriverLicense: {
            title: '中国大陆驾照',
            tips: '',
            statuses: 1,
            selected: false,
            code: 'CDL',
            requirements: [
              '中国大陆驾照 + ',
              '驾照国际翻译认证件/当地语言公证件',
            ],
            areaCode: 'CN',
          },
        },
      },
      expected: {
        title: '中国大陆驾照',
        tips: '',
        statuses: 1,
        selected: false,
        code: 'CDL',
        requirements: ['中国大陆驾照 + ', '驾照国际翻译认证件/当地语言公证件'],
        areaCode: 'CN',
      },
    },
    {
      state: {
        Product: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)(
    'getCurDriverLicense check',
    ({ state, expected }) => {
      const data = getCurDriverLicense(state);
      expect(data).toEqual(expected);
    },
  );
});

describe('Product Selectors getPlaceHoldTips', () => {
  const mockStateMap = [
    {
      state: {
        Product: {
          placeHoldTips: '这是提示内容',
        },
      },
      expected: '这是提示内容',
    },
    {
      state: {
        Product: {},
      },
      expected: undefined,
    },
  ];
  test.each(mockStateMap)('getPlaceHoldTips check', ({ state, expected }) => {
    const data = getPlaceHoldTips(state);
    expect(data).toEqual(expected);
  });
});
