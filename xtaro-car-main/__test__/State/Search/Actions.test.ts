import {
  setStatus,
  fetchSearchList,
  fetchSearchListCallback,
  updateKeyword,
} from '../../../src/pages/xcar/State/Search/Actions';
import {
  SET_STATUS,
  FETCH_SEARCH_LIST,
  FETCH_SEARCH_LIST_CALLBACK,
  UPDATE_KEYWORD,
} from '../../../src/pages/xcar/State/Search/Types';

describe('Search Actions setStatus', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: SET_STATUS,
        data,
      },
    },
  ];
  test.each(mockMap)('setStatus check', ({ data, expected }) => {
    const result = setStatus(data);
    expect(result).toEqual(expected);
  });
});

describe('Search Actions fetchSearchList', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: FETCH_SEARCH_LIST,
        data,
      },
    },
  ];
  test.each(mockMap)('fetchSearchList check', ({ data, expected }) => {
    const result = fetchSearchList(data);
    expect(result).toEqual(expected);
  });
});

describe('Search Actions fetchSearchListCallback', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: FETCH_SEARCH_LIST_CALLBACK,
        data,
      },
    },
  ];
  test.each(mockMap)('fetchSearchListCallback check', ({ data, expected }) => {
    const result = fetchSearchListCallback(data);
    expect(result).toEqual(expected);
  });
});

describe('Search Actions updateKeyword', () => {
  const data = {};
  const mockMap = [
    {
      data,
      expected: {
        type: UPDATE_KEYWORD,
        data,
      },
    },
  ];
  test.each(mockMap)('updateKeyword check', ({ data, expected }) => {
    const result = updateKeyword(data);
    expect(result).toEqual(expected);
  });
});
