import {
  getIsLoading,
  getIsFail,
  getKeyword,
  getSearchCount,
} from '../../../src/pages/xcar/State/Search/Selectors';

describe('Search Selectors getIsLoading', () => {
  const mockStateMap = [
    {
      state: {
        Search: {
          isLoading: false,
        },
      },
      expected: false,
    },
    {
      state: {
        Search: {
          isLoading: true,
        },
      },
      expected: true,
    },
  ];
  test.each(mockStateMap)('getIsLoading check', ({ state, expected }) => {
    const data = getIsLoading(state);
    expect(data).toEqual(expected);
  });
});

describe('Search Selectors getIsFail', () => {
  const mockStateMap = [
    {
      state: {
        Search: {
          isFail: true,
        },
      },
      expected: true,
    },
    {
      state: {
        Search: {
          isFail: false,
        },
      },
      expected: false,
    },
  ];
  test.each(mockStateMap)('getIsFail check', ({ state, expected }) => {
    const data = getIsFail(state);
    expect(data).toEqual(expected);
  });
});

describe('Search Selectors getKeyword', () => {
  const mockStateMap = [
    {
      state: {
        Search: {
          keyword: 'keyword',
        },
      },
      expected: 'keyword',
    },
    {
      state: {
        Search: {
          keyword: 'XXXX',
        },
      },
      expected: 'XXXX',
    },
  ];
  test.each(mockStateMap)('getKeyword check', ({ state, expected }) => {
    const data = getKeyword(state);
    expect(data).toEqual(expected);
  });
});

describe('Search Selectors getSearchCount', () => {
  const mockStateMap = [
    {
      state: {
        Search: {
          searchCount: 1,
        },
      },
      expected: 1,
    },
    {
      state: {
        Search: {
          searchCount: 3,
        },
      },
      expected: 3,
    },
  ];
  test.each(mockStateMap)('getSearchCount check', ({ state, expected }) => {
    const data = getSearchCount(state);
    expect(data).toEqual(expected);
  });
});
