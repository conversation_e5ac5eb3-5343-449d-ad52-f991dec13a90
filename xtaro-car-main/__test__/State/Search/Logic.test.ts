import { recordSaga } from '../../testHelpers';
import {
  querySearchList,
  querySearchListCallback,
} from '../../../src/pages/xcar/State/Search/Logic';
import * as Types from '../../../src/pages/xcar/State/Search/Types';
import * as Actions from '../../../src/pages/xcar/State/Search/Actions';
import { CarFetch } from '../../../src/pages/xcar/Util/Index';

const state = {
  Search: {
    searchCount: 1,
  },
};

const mockResData = {
  version: 3,
  ResponseStatus: {
    Extension: [
      {
        Value: '5736832743977879318',
        Id: 'CLOGGING_TRACE_ID',
      },
      {
        Value: '921822-0a050d7d-463855-28960',
        Id: 'RootMessageId',
      },
    ],
    Ack: 'Success',
    Errors: [],
    Timestamp: '/Date(1669879769863+0800)/',
  },
  locations: [
    {
      id: '2',
      label: '城市',
      provinceid: 2,
      business: true,
      cityName: '上海',
      word: '上海（彩虹桥）',
      channel: 1,
      countryname: '中国',
      highlight: [],
      type: '0',
      countryid: 1,
      isDomestic: true,
      address: '中国',
      ename: '',
      sortNum: 0,
      provinceName: '上海',
      timezone: 8,
      cityId: 2,
      name: '上海（彩虹桥）',
    },
    {
      id: '15',
      label: '城市',
      provinceid: 15,
      business: true,
      cityName: '扬州',
      word: '扬州（大虹桥）',
      channel: 1,
      countryname: '中国',
      highlight: [],
      type: '0',
      countryid: 1,
      isDomestic: true,
      address: '江苏，中国',
      ename: '',
      sortNum: 1,
      provinceName: '江苏',
      timezone: 8,
      cityId: 15,
      name: '扬州（大虹桥）',
    },
    {
      id: '1333',
      label: '城市',
      provinceid: 25,
      business: true,
      cityName: '开远',
      word: '开远（长虹桥）',
      channel: 1,
      countryname: '中国',
      highlight: [],
      type: '0',
      countryid: 1,
      isDomestic: true,
      address: '云南，中国',
      ename: '',
      sortNum: 2,
      provinceName: '云南',
      timezone: 8,
      cityId: 1333,
      name: '开远（长虹桥）',
    },
    {
      id: '7',
      label: '城市',
      provinceid: 10,
      business: true,
      cityName: '青岛',
      word: '青岛（彩虹桥）',
      channel: 1,
      countryname: '中国',
      highlight: [],
      type: '0',
      countryid: 1,
      isDomestic: true,
      address: '山东，中国',
      ename: '',
      sortNum: 3,
      provinceName: '山东',
      timezone: 8,
      cityId: 7,
      name: '青岛（彩虹桥）',
    },
    {
      id: '3',
      label: '城市',
      provinceid: 3,
      business: true,
      cityName: '天津',
      word: '天津（新红桥）',
      channel: 1,
      countryname: '中国',
      highlight: [],
      type: '0',
      countryid: 1,
      isDomestic: true,
      address: '中国',
      ename: '',
      sortNum: 4,
      provinceName: '天津',
      timezone: 8,
      cityId: 3,
      name: '天津（新红桥）',
    },
    {
      id: '17',
      label: '城市',
      provinceid: 16,
      business: true,
      cityName: '杭州',
      word: '杭州（霁虹桥）',
      channel: 1,
      countryname: '中国',
      highlight: [],
      type: '0',
      countryid: 1,
      isDomestic: true,
      address: '浙江，中国',
      ename: '',
      sortNum: 5,
      provinceName: '浙江',
      timezone: 8,
      cityId: 17,
      name: '杭州（霁虹桥）',
    },
    {
      id: '7619',
      label: '城市',
      provinceid: 26,
      business: true,
      cityName: '仁怀',
      word: '仁怀（彩虹桥）',
      channel: 1,
      countryname: '中国',
      highlight: [],
      type: '0',
      countryid: 1,
      isDomestic: true,
      address: '贵州，中国',
      ename: '',
      sortNum: 6,
      provinceName: '贵州',
      timezone: 8,
      cityId: 7619,
      name: '仁怀（彩虹桥）',
    },
    {
      id: '489',
      label: '城市',
      provinceid: 18,
      business: true,
      cityName: '婺源',
      word: '婺源（彩虹桥）',
      channel: 1,
      countryname: '中国',
      highlight: [],
      type: '0',
      countryid: 1,
      isDomestic: true,
      address: '江西，中国',
      ename: '',
      sortNum: 7,
      provinceName: '江西',
      timezone: 8,
      cityId: 489,
      name: '婺源（彩虹桥）',
    },
    {
      id: '5',
      label: '城市',
      provinceid: 5,
      business: true,
      cityName: '哈尔滨',
      word: '哈尔滨（霁虹桥）',
      channel: 1,
      countryname: '中国',
      highlight: [],
      type: '0',
      countryid: 1,
      isDomestic: true,
      address: '黑龙江，中国',
      ename: '',
      sortNum: 8,
      provinceName: '黑龙江',
      timezone: 8,
      cityId: 5,
      name: '哈尔滨（霁虹桥）',
    },
  ],
  BaseResponse: {
    IsSuccess: true,
    ReturnMsg: 'success',
    Cost: 310,
    Code: '200',
    RequestId: '3c3fe63c-3fe4-4632-b34c-3b6ca830581e',
  },
  appResponseMap: {
    isFromCache: false,
    isCacheValid: true,
    networkCost: 349,
    environmentCost: 0,
    cacheFetchCost: 0,
    fetchCost: 349,
    setCacheCost: 0,
    cacheFrom: '',
    beforeFetch: 1669879769531,
    afterFetch: 1669879769880,
  },
};

describe('Test Search logic querySearchList', () => {
  const keyword = 'hongqiao';
  const data = { keyword: keyword, isArea: false };
  const testLogicFn = async (result, data?) => {
    const api = jest
      .spyOn(CarFetch, 'querySearchList')
      .mockReturnValueOnce(Promise.resolve(result));
    const dispatched = await recordSaga(querySearchList, {
      action: {
        type: Types.FETCH_SEARCH_LIST,
        data,
      },
      state,
    });
    return { api, dispatched };
  };
  test('测试 querySearchList', async () => {
    const { api, dispatched } = await testLogicFn(mockResData, data);
    expect(api).toBeCalled();
    expect(dispatched[0]).toEqual(Actions.setStatus({ isLoading: true }));
    expect(dispatched[1]).toEqual(
      Actions.fetchSearchListCallback({
        isError: false,
        param: { keyWord: keyword },
        res: mockResData,
        type: 'city',
      }),
    );
  });
});

describe('Test Search logic querySearchListCallback', () => {
  const data = {
    param: {},
    res: mockResData,
    isError: false,
    type: 'city',
  };
  const testLogicFn = async data => {
    const dispatched = await recordSaga(querySearchListCallback, {
      action: {
        type: Types.FETCH_SEARCH_LIST_CALLBACK,
        data,
      },
      state,
    });
    return dispatched;
  };

  test('测试 querySearchListCallback', async () => {
    const dispatched = await testLogicFn(data);
    expect(dispatched[0]).toEqual(
      Actions.setStatus({ isLoading: false, isFail: false, searchCount: 2 }),
    );
  });
});
