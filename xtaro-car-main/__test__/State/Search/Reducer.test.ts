import SearchReducer, {
  getInitalState,
} from '../../../src/pages/xcar/State/Search/Reducer';

import { SET_STATUS, UPDATE_KEYWORD } from '../../../src/pages/xcar/State/Search/Types';

describe('Search Reducer Test', () => {
  const initState = getInitalState();

  // 验证初始化
  test('Search Reducer Init', () => {
    expect(SearchReducer(undefined, {})).toEqual(initState);
  });

  // 验证状态设置
  test('Search Reducer SET_STATUS', () => {
    expect(
      SearchReducer(initState, {
        type: SET_STATUS,
        data: {
          isLoading: true,
          isFail: false,
        },
      }),
    ).toEqual({
      ...initState,
      isLoading: true,
      isFail: false,
    });
  });

  // 验证更新keyword
  test('Search Reducer UPDATE_KEYWORD', () => {
    expect(
      SearchReducer(initState, {
        type: UPDATE_KEYWORD,
        data: {
          keyword: 'keyword',
        },
      }),
    ).toEqual({
      ...initState,
      keyword: 'keyword',
    });
  });
});
