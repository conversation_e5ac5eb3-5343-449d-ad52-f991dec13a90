import { fireEvent, act } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import { Utils, AppContext, CarFetch, User } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import OrderDetail from '../../src/pages/xcar/Containers/OrderDetailContainer';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { renderWithAction, waitRender } from '../testHelpers';
// @ts-ignore 禁止Redux Log
__DEV__ = false;
const resOSDQueryOrder408010 = require('../../__mocks__/restful/18862/OSDQueryOrder/408010.json');

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

// 页面初始化
const renderPage = async (initialState = {}) => {
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
  jest.spyOn(User, 'isLoginSync').mockReturnValue(true);
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'OrderDetail',
  });

  return createPage(OrderDetail, {}, initialState);
};
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));

test(
  createInterTestName({
    testId: [
      407969, 407972, 407974, 407971, 407976, 407977, 407978, 407975, 407980,
      407981, 407982, 407984, 411018, 407973, 407979, 407983,
    ],
    name: '费用明细',
  }),
  async () => {
    const state = {
      OrderDetail: {
        reqOrderParams: {
          orderId: 1234567,
        },
        storageCardsTitle: [],
      },
    };
    jest
      .spyOn(CarFetch, 'queryOrder')
      .mockImplementation(() => Promise.resolve(resOSDQueryOrder408010));
    const Page = await renderPage(state);
    const priceDetailBtn = await Page.findByTestId(
      UITestID.car_testid_page_order_price_detail,
    );
    //	407969 点击【费用明细】 预期：弹出“费用明细弹窗”
    await act(async () => {
      fireEvent(priceDetailBtn, 'click');
    });
    const priceDetailModal = await Page.findByTestId(
      UITestID.car_testid_comp_price_modal,
    );
    await waitRender(async () => {
      expect(priceDetailModal).toBeTruthy();
      // 407972 订单有使用优惠券
      expect(priceDetailModal).toHaveTextContent('国内立减折扣9折立减券');
      // 411018 407973 预期：右侧优惠券价格前展示“-”
      expect(priceDetailModal).toHaveTextContent('- ¥42');
      // 407974
      expect(priceDetailModal).toHaveTextContent('租车费');
      expect(priceDetailModal).toHaveTextContent('¥428');
      // 407976
      expect(priceDetailModal).toHaveTextContent('夜间取车费');
      expect(priceDetailModal).toHaveTextContent('¥50');
      // 407977
      expect(priceDetailModal).toHaveTextContent('夜间还车费');
      expect(priceDetailModal).toHaveTextContent('¥50');
      // 407978
      expect(priceDetailModal).toHaveTextContent('车行手续费');
      expect(priceDetailModal).toHaveTextContent('含车辆清洁、单据打印等');
      // 407975
      expect(priceDetailModal).toHaveTextContent(
        '若租期不足1天，按1天价格收取',
      );
      // 407980
      expect(priceDetailModal).toHaveTextContent('尊享服务费');
      expect(priceDetailModal).toHaveTextContent('¥80 x1天');
      expect(priceDetailModal).toHaveTextContent(
        '车损保障赔付全部损失，含玻璃、轮胎，三者保障保额100万，含无需垫付，车损3万元以下免收折旧费，免收1万元以下停运费',
      );
      // 407981
      expect(priceDetailModal).toHaveTextContent('人身及财物险');
      expect(priceDetailModal).toHaveTextContent('¥25');
      // 407982
      expect(priceDetailModal).toHaveTextContent('订单总额');
      expect(priceDetailModal).toHaveTextContent('¥245');
      expect(priceDetailModal).toHaveTextContent('在线支付');
      // 407971 有零散小时费
      expect(priceDetailModal).toHaveTextContent('小时费收费规则');
    });
    const tipBtn = await Page.findByTestId(
      UITestID.car_testid_comp_order_priceDetail_tip,
    );
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
    await renderWithAction({
      action: async () => {
        await act(async () => {
          fireEvent(tipBtn, 'click');
        });
      },
      expect: () => {
        expect(pagePushFunc).toBeCalled();
        // 407984 积分
        expect(priceDetailModal).toHaveTextContent('已享1.5倍加速');
      },
    });

    await renderWithAction({
      action: async () => {
        const pointBtn = await Page.findByTestId(
          UITestID.car_testid_comp_order_priceDetail_point,
        );
        await act(async () => {
          fireEvent(pointBtn, 'click');
        });
      },
      expect: () => {
        expect(Page.container).toHaveTextContent('积分说明');
        // 407979
        expect(Page.container).toHaveTextContent('优享服务费');
        // 407983售前未加购保险跳转保代页面
        expect(Page.container).toHaveTextContent('异地还车费¥50');
      },
    });
  },
);
