import { Image, TouchableOpacity, Text } from 'react-native';
import { fireEvent, act, waitFor } from '@testing-library/react-native';
import { BbkConstants } from '../../src/pages/xcar/Common/src/Utils';
import { Utils, User, AppContext, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { defaultRecommendDate } from '../../src/pages/xcar/State/LocationAndDate/Mappers';
import { renderWithAction, waitRender } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

const productRes = require('../../__mocks__/restful/18631/queryProductInfo/OSD/457880.json');
const priceRes = require('../../__mocks__/restful/18631/querypriceinfo/OSD/451558.json');

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};

test(
  createInterTestName({
    testId: [451552, 451553],
    name: '返回按钮',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes));
    const Page = await renderPage();

    const container = await Page.findByTestId(UITestID.car_testid_page_booking);
    expect(container).toHaveTextContent('订单填写');

    await renderWithAction({
      action: async () => {
        const btns = await container.findAllByType(TouchableOpacity);
        fireEvent.press(btns[0]);
      },
      expect: async () => {
        // @ts-ignore
        expect(global.popCount).toEqual(1);
      },
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [451554, 451560, 455582, 3246384, 3246475, 3246482],
    name: '车型',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes));
    const Page = await renderPage();

    const container = await Page.findByTestId(UITestID.car_testid_page_booking);
    expect(container).toHaveTextContent('雪佛兰 Spark 4门版或同组车型');
    expect(container).toHaveTextContent('4');
    expect(container).toHaveTextContent('3');
    expect(container).toHaveTextContent('A/C');
    expect(container).toHaveTextContent('4');
    expect(container).toHaveTextContent('自动挡');
    expect(container).toHaveTextContent('参考车型');
    // expect(container).toHaveTextContent('驾ddd龄');
    expect(container).toHaveTextContent('2个24寸行李箱');
    const suitCaseBtn = await Page.queryByText('2个24寸行李箱');
    await act(() => {
      fireEvent.press(suitCaseBtn);
    });
    expect(container).toHaveTextContent(
      '行李个数计算· 行李大小计算标准为24寸行李箱；· 超过24寸的大行李箱，记为2件；· 一个空座可增加1件24寸行李（豪华车型不适用）。',
    );
    const bootModal = await Page.findByTestId(
      UITestID.car_testid_vehicleBootModal,
    );
    const allImages = bootModal.findAllByType(Image);
    expect(allImages[0].props.source.uri).toEqual(
      'https://dimg04.c-ctrip.com/images/1tg4l12000cmig1tdC0BE.png_.webp',
    );
    allImages.forEach(item => {
      console.log(item.props.source.uri);
    });
    // 弹窗关闭
    const closeBtn = await bootModal.findAllByType(Text);
    fireEvent.press(closeBtn[0]);
    await waitFor(() => {
      expect(Page.queryByText('行李箱说明')).toBeFalsy();
    });

    const vehicleLocation = await Page.findByTestId(
      UITestID.car_testid_page_booking_vehicleLocation,
    );
    const images = await vehicleLocation.findAllByType(Image);
    expect(images[0].props.source.uri).toEqual(
      `https://pic.c-ctrip.com/car/osd/online/vehicle_new/Chevrolet_Spark_4_Doors.png?timestamps=${BbkConstants.timestamps}`,
    );
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [451555, 451556, 455583],
    name: '租期',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes));
    const Page = await renderPage();
    const { pickUp, dropOff } = defaultRecommendDate();
    const formatString =
      pickUp.dateTime.year() === dropOff.dateTime.year()
        ? 'M月D日 HH:mm'
        : 'YYYY-MM-DD HH:mm';
    const pStr = pickUp.dateTime.format(formatString);
    const rStr = dropOff.dateTime.format(formatString);
    const container = await Page.findByTestId(UITestID.car_testid_page_booking);
    expect(container).toHaveTextContent(`取车${pStr} (当地时间)`);
    expect(container).toHaveTextContent('洛杉矶国际机场');
    expect(container).toHaveTextContent(`还车${rStr} (当地时间)`);
    expect(container).toHaveTextContent('洛杉矶国际机场');
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [451557, 455584],
    name: '供应商logo',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes));
    const Page = await renderPage();

    const vehicleLocation = await Page.findByTestId(
      UITestID.car_testid_page_booking_vehicleLocation,
    );
    expect(vehicleLocation).toHaveTextContent('提供服务');

    const images = await vehicleLocation.findAllByType(Image);
    expect(images[1].props.source.uri).toEqual(
      `https://dimg04.c-ctrip.com/images/0yc4912000bf4bxa4DB93.png?timestamps=${BbkConstants.timestamps}`,
    );
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [451558, 451559, 451563, 455586],
    name: '激励政策',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes));
    jest
      .spyOn(CarFetch, 'queryPriceInfo')
      .mockImplementation(() => Promise.resolve(priceRes));

    const Page = await renderPage();

    const vehicleLocation = await Page.findByTestId(
      UITestID.car_testid_page_booking,
    );

    await waitRender(() => {
      expect(vehicleLocation).toHaveTextContent('立即确认，取车前免费取消');
      expect(vehicleLocation).toHaveTextContent('此订单已减¥50');
    });
  },
  TEST_TIMEOUT,
);
