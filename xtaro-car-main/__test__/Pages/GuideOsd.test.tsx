import { fireEvent, act } from '@testing-library/react-native';
import Image from '@c2x/components/Image';;
import CRNPage from '@c2x/components/Page';
import { waitRender, renderWithAction } from '../testHelpers';
import { Utils, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';

const resStoreGuide458062 = require('../../__mocks__/restful/18631/queryStoreGuide/OSD/458062.json');

// @ts-ignore 禁止Redux Log
__DEV__ = false;

jest.mock(
  '../../src/pages/xcar/ComponentBusiness/PickdropMap/src/CalculateManager',
  () => {
    return {
      calculateRouteDistanceForProxyViewPromise: jest.fn(() => ({
        distance: 1600,
      })),
      calculateRouteETAForProxyViewPromise: jest.fn(() => ({ etaTime: 1265 })),
      drawRouteForProxyViewV2Promise: jest.fn(),
    };
  },
);

// 页面初始化
const renderPage = (initialState = undefined, orderId?) => {
  // eslint-disable-next-line global-require
  const Page = require('../../src/pages/xcar/Containers/GuidePageContainer').default;
  return createPage(
    Page,
    {
      orderId,
      pageParam: {
        orderId,
        pickupStart: {
          lat: '33.941589',
          lng: '-118.40853',
          addr: '洛杉矶国际机场',
        },
        dropoffStart: {
          lat: '33.941589',
          lng: '-118.40853',
          addr: '洛杉矶国际机场',
        },
        pickupStoreId: 'LAXEPLAXO01',
        dropoffStoreId: 'LAXEPLAXO01',
        selectedId: 'pickup',
        pickupServiceType: 0,
        dropoffServiceType: 0,
        pickupPointInfo: {
          cityId: 347,
          date: '2023-09-07 10:00:00',
          locationCode: 'LAX',
          locationName: '洛杉矶国际机场',
          locationType: 1,
          poi: { latitude: 33.941589, longitude: -118.40853, radius: 0 },
          pickupOnDoor: 0,
          dropOffOnDoor: 0,
        },
        returnPointInfo: {
          cityId: 347,
          date: '2023-09-08 10:00:00',
          locationCode: 'LAX',
          locationName: '洛杉矶国际机场',
          locationType: 1,
          poi: { latitude: 33.941589, longitude: -118.40853, radius: 0 },
          pickupOnDoor: 0,
          dropOffOnDoor: 0,
        },
        fixMap: true,
        isHidePhone: false,
        pickupStoreGuide:
          '您的Europcar订单将由Fox门店为您提供服务，请前往Fox门店取车。',
        returnStoreGuide:
          '请提前致电联系门店，确认还车位置。通常在停车场附近的道路旁会有“Car Return”的指示牌，可按照指示牌前往。',
      },
    },
    initialState,
  );
};

test(
  createInterTestName({
    testId: [458062, 458064, 4068478],
    name: '大气泡',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(CarFetch, 'queryStoreGuide')
      .mockImplementationOnce(() => Promise.resolve(resStoreGuide458062));
    const Page = await renderPage();
    await waitRender(async () => {
      // 4068478 驾车文案 驾车距离*KM，约*分钟到达
      // @ts-ignore
      expect(global.mapCard.title).toEqual('驾车距离2KM，约22分钟到达');

      const walkbtn = await Page.findByTestId(
        UITestID.car_testid_page_guide_map_walk_tab,
      );

      await act(async () => {
        fireEvent(walkbtn, 'onPress');
      });

      // 4068478 步行文案 步行距离*KM，约*分钟到达
      // @ts-ignore
      expect(global.mapCard.title).toEqual('步行距离2KM，约22分钟到达');
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [458069, 458070, 458072],
    name: '定位',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(CarFetch, 'queryStoreGuide')
      .mockImplementationOnce(() => Promise.resolve(resStoreGuide458062));
    const Page = await renderPage();

    const locationBtn = await Page.findByTestId(
      UITestID.car_testid_page_guide_map_location,
    );

    await act(async () => {
      fireEvent(locationBtn, 'onPress');
    });

    // @ts-ignore
    expect(global.mapLocationCount).toEqual(1);

    await act(async () => {
      fireEvent(locationBtn, 'onPress');
    });
    // @ts-ignore
    expect(global.mapLocationCount).toEqual(2);

    await act(async () => {
      fireEvent(locationBtn, 'onPress');
    });
    // @ts-ignore
    expect(global.mapLocationCount).toEqual(3);
  },
  TEST_TIMEOUT,
);
test(
  createInterTestName({
    testId: [3840467, 4068492, 4068506],
    name: '底部浮层-内容展示',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(CarFetch, 'queryStoreGuide')
      .mockImplementationOnce(() => Promise.resolve(resStoreGuide458062));
    const Page = await renderPage();

    await waitRender(async () => {
      // 底部浮层-内容展示 4068492
      const item1 = await Page.queryByTestId('c_testid_guide_content_item_1');
      expect(item1).toHaveTextContent('取车方式机场外');
      const item2 = await Page.queryByTestId('c_testid_guide_content_item_2');
      expect(item2).toHaveTextContent('门店名称LOS ANGELES AIRPORT');
      const item3 = await Page.queryByTestId('c_testid_guide_content_item_3');
      expect(item3).toHaveTextContent(
        '门店地址PLEASE GO TO FOX COUNTER,5500 W CENTURY BLVD',
      );
      const item4 = await Page.queryByTestId('c_testid_guide_content_item_4');
      expect(item4).toHaveTextContent('门店电话+1 323 6739084');
      const item5 = await Page.queryByTestId('c_testid_guide_content_item_5');
      expect(item5).toHaveTextContent(
        '取车指引您的Europcar订单将由Fox门店为您提供服务，请前往Fox门店取车。',
      );
      const item6 = await Page.queryByTestId('c_testid_guide_content_item_6');
      expect(item6).toHaveTextContent(
        '营业时间0:01 - 0:30, 5:00 - 23:59如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
      );
      // 取还车指引底部免责文案 仅trip展示 4068506
      expect(Page.container).not.toHaveTextContent('此信息由门店提供');
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [3840474],
    name: '取还车指引图片',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const Page = await renderPage();

    await waitRender(async () => {
      const item5 = await Page.queryByTestId('c_testid_guide_content_item_5');
      expect(item5).toHaveTextContent(
        '取车指引取车指引测试取车指引测试取车指引测试取车指引测试取车指引测试取车指引测试取车指引测试取车指引测试取车指引测试',
      );
      const allImages = item5.findAllByType(Image);
      expect(allImages[1].props.source.uri).toEqual(
        'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R43v1200000yopea14A1.png?timestamps=20230329',
      );
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [3840320, 3840306],
    name: '订详-取还车指引图片,底部浮层-内容展示',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const Page = await renderPage(undefined, 'orderId');
    await waitRender(async () => {
      const item1 = await Page.queryByTestId('c_testid_guide_content_item_1');
      expect(item1).toHaveTextContent('取车方式步行可达');
      const item2 = await Page.queryByTestId('c_testid_guide_content_item_2');
      expect(item2).toHaveTextContent(
        '门店名称Bangkok Suvarnabhumi International Airport',
      );
      const item3 = await Page.queryByTestId('c_testid_guide_content_item_3');
      expect(item3).toHaveTextContent(
        '门店地址999 Thanon Bang Na-Trat, Rachathewa, Samut Prakan 10540.',
      );
      const item4 = await Page.queryByTestId('c_testid_guide_content_item_4');
      expect(item4).toHaveTextContent('门店电话86-1820-000000');
      const item5 = await Page.queryByTestId('c_testid_guide_content_item_5');
      const item6 = await Page.queryByTestId('c_testid_guide_content_item_6');
      expect(item6).toHaveTextContent('营业时间00:00 - 14:00,21:00 - 23:00');

      expect(item5).toHaveTextContent('订详取车指引测试');
      const allImages = item5.findAllByType(Image);
      expect(allImages[1].props.source.uri).toEqual(
        'https://dimg04.fws.qa.nt.ctripcorp.com/images/0R43v1200000yopea14A1.png?timestamps=20230329',
      );
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [4068520, 3840488, 3840453],
    name: '取还指引页',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(CarFetch, 'queryStoreGuide')
      .mockImplementationOnce(() => Promise.resolve(resStoreGuide458062));
    const Page = await renderPage();
    const pagePopFunc = jest.spyOn(CRNPage.prototype, 'pop');
    await waitRender(async () => {
      const content = await Page.queryByTestId(
        UITestID.c_testid_pickDropModal_content,
      );
      // 标题栏 tab 4068520
      expect(content).toHaveTextContent('取车还车');
      const pickDropStore = await Page.queryByTestId(
        UITestID.c_testid_pickDropStore,
      );
      expect(pickDropStore).toBeTruthy();
    });
    await renderWithAction({
      action: async () => {
        const backIcon = await Page.queryByTestId(
          UITestID.c_testid_pickDropModal_backIcon,
        );
        fireEvent.press(backIcon);
      },
      expect: () => {
        // 标题栏【返回icon】4068520
        expect(pagePopFunc).toBeCalled();
      },
    });
  },
  TEST_TIMEOUT,
);
