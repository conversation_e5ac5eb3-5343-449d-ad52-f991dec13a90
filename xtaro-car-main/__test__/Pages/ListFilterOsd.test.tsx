/* eslint-disable @typescript-eslint/naming-convention */
import { TouchableOpacity, Image, Text } from 'react-native';
import { fireEvent, within, waitFor, act } from '@testing-library/react-native';
import { color } from '../../src/pages/xcar/Common/src/Tokens';
import { Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';
import { renderWithAction, waitRender, delayPromise } from '../testHelpers';

const res440391 = require('../../__mocks__/restful/18631/queryProducts/OSD/440391.json');

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

const SelectedImageIcon =
  'https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/filterSelected.png';
const unSelectStyle = {
  color: '#111111',
};
const selectStyle = {
  color: '#0086f6',
};

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: false,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

describe('列表页-筛选栏', () => {
  test(
    createInterTestName({
      testId: [440406, 440407, 440408, 440410, 440411, 440414, 440409],
      name: '点击筛选栏门店/服务',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(Utils, 'getUniqRequestKeyWithEnv')
        .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );

      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[2];
      // 点击：门店/服务
      fireEvent.press(filterMoreTouchable);

      // 440406 取车地点非机场-左侧导航 预期：【门店位置 】；【门店距离 】；【点评】；【门店服务】；【特色服务】；
      const filterListNavBarDom = await Page.findByTestId(
        'car_testid_filter_nav_list_item',
      );
      const filterListNavBar = within(filterListNavBarDom);

      const filterListNavBarText = filterListNavBarDom.findAllByType(Text);
      expect(filterListNavBarText.length).toBe(5);
      expect(filterListNavBar.getByText('门店位置')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('门店距离')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('点评')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('门店服务')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('特色服务')).toBeOnTheScreen();
      // 440407 门店距离 预期：不支持多选
      const filterModal = within(
        Page.getByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      const filterBtn_1 = await filterModal.findByText('500米内');
      const filterBtn_2 = await filterModal.findByText('1公里内');
      fireEvent.press(filterBtn_1);
      fireEvent.press(filterBtn_2);
      // @ts-ignore
      expect(filterBtn_1.parent).toHaveStyle(unSelectStyle);
      // @ts-ignore
      expect(filterBtn_2.parent).toHaveStyle(selectStyle);

      const images = await Page.container.findAllByType(Image);
      const selectedFilters = images.filter(
        image => image.props.source.uri === SelectedImageIcon,
      );

      // 440408 点评 预期：不支持多选
      const filterBtn_3 = await filterModal.findByText('4.5分以上');
      const filterBtn_4 = await filterModal.findByText('4.0分以上');
      fireEvent.press(filterBtn_3);
      fireEvent.press(filterBtn_4);
      expect(filterBtn_3.parent).toHaveStyle(unSelectStyle);
      // @ts-ignore
      expect(filterBtn_4.parent).toHaveStyle(selectStyle);

      // 440410 门店位置不支持多选
      const filterBtn_7 = await filterModal.findByText('机场外');
      const filterBtn_8 = await filterModal.findByText('机场内');
      fireEvent.press(filterBtn_7);
      fireEvent.press(filterBtn_8);
      expect(filterBtn_7.parent).toHaveStyle(unSelectStyle);
      // @ts-ignore
      expect(filterBtn_8.parent).toHaveStyle(selectStyle);

      // 440411 跨类别多选 预期：交集筛选
      waitFor(() => {
        expect(selectedFilters.length).toBe(3);
      });

      // 440414 特色服务 预期：支持多选，并集筛选
      const filterBtn_5 = await filterModal.findByText('满油取还');
      const filterBtn_6 = await filterModal.findByText('送第三者责任险');
      fireEvent.press(filterBtn_5);
      fireEvent.press(filterBtn_6);
      expect(filterBtn_5.parent).toHaveStyle(selectStyle);
      // @ts-ignore
      expect(filterBtn_6.parent).toHaveStyle(selectStyle);
      waitFor(() => {
        expect(selectedFilters.length).toBe(5);
      });

      // 440409 门店服务 预期：支持多选，交集筛选
      const filterBtn_9 = await filterModal.findByText('立即确认');
      const filterBtn_10 = await filterModal.findByText('免费取消');
      fireEvent.press(filterBtn_9);
      fireEvent.press(filterBtn_10);
      expect(filterBtn_9.parent).toHaveStyle(selectStyle);
      // @ts-ignore
      expect(filterBtn_10.parent).toHaveStyle(selectStyle);
      waitFor(() => {
        expect(selectedFilters.length).toBe(7);
      });
    },
  );
});

describe('列表页-筛选栏', () => {
  test(
    createInterTestName({
      testId: [440441, 531459, 531454, 531458],
      name: '筛选功能',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );
      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[3];
      // 点击：筛选
      fireEvent.press(filterMoreTouchable);
      const filterModal = within(
        await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      // 440441 选择不限里程 预期：筛选支持不限里程的车辆
      const filterBtn_1 = await filterModal.findByText('不限里程');
      const submitBtn = await Page.findByText('完成');
      const carVehicleList = await Page.getByTestId(
        UITestID.car_testid_list_osd_carVehicleList,
      );
      const vehicleListItem = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );
      await renderWithAction({
        action: () => {
          fireEvent.press(filterBtn_1);
          fireEvent.press(submitBtn);
        },
        expect: async () => {
          expect(carVehicleList).toHaveTextContent('起亚 Soul或同组车型');
          expect(carVehicleList).toHaveTextContent('大众 捷达 Jetta或同组车型');
          expect(carVehicleList).toHaveTextContent(
            '丰田 凯美瑞 Camry或同组车型',
          );
          expect(carVehicleList).toHaveTextContent('丰田 RAV4或同组车型');
          expect(carVehicleList).toHaveTextContent(
            '克莱斯勒 Pacifica或同组车型',
          );
          expect(vehicleListItem.length).toBe(6);
        },
      });
      const ImageUrls = [
        'https://pic.c-ctrip.com/car/osd/online/vehicle_new/Kia_Soul.png?timestamps=20230329',
        'https://pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png?timestamps=20230329',
        'https://pages.c-ctrip.com/cars/image/components/moreRightIcon.png',
      ];
      // 531459 531454 531458  供应商Logo 车型图 正常展示
      const allImages = vehicleListItem[0].findAllByType(Image);
      allImages?.forEach((image, idx) => {
        expect(image.props.source.uri).toBe(ImageUrls[idx]);
      });
    },
  );
  test(
    createInterTestName({
      testId: [440441, 440440],
      name: '筛选功能',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );
      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[3];
      // 点击：筛选
      fireEvent.press(filterMoreTouchable);
      const filterModal = within(
        await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      // 440441 选择不限里程 预期：筛选支持不限里程的车辆
      const filterBtn_1 = await filterModal.findByText('不限里程');
      const submitBtn = await Page.findByText('完成');
      const carVehicleList = await Page.getByTestId(
        UITestID.car_testid_list_osd_carVehicleList,
      );
      const vehicleListItem = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );
      // 440440 选择支持银联 预期：并集筛选车辆
      const filterBtn_2 = await filterModal.findByText('支持银联');
      await renderWithAction({
        action: () => {
          fireEvent.press(filterBtn_1);
          fireEvent.press(filterBtn_2);
          fireEvent.press(submitBtn);
        },
        expect: async () => {
          expect(vehicleListItem.length).toBe(6);
        },
      });
    },
  );
  test(
    createInterTestName({
      testId: [440441, 440442],
      name: '筛选功能',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );
      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[3];
      // 点击：筛选
      fireEvent.press(filterMoreTouchable);
      const filterModal = within(
        await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      const submitBtn = await Page.findByText('完成');
      const carVehicleList = await Page.getByTestId(
        UITestID.car_testid_list_osd_carVehicleList,
      );
      const vehicleListItem = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );

      // 440442 选择在线支付 预期：筛选支持在线支付的车辆
      const filterBtn_3 = await filterModal.findByText('在线支付');
      await renderWithAction({
        action: () => {
          fireEvent.press(filterBtn_3);
          fireEvent.press(submitBtn);
        },
        expect: async () => {
          expect(carVehicleList).toHaveTextContent('起亚 Soul或同组车型');
          expect(carVehicleList).toHaveTextContent('捷达 Jetta或同组车型');
          expect(carVehicleList).toHaveTextContent(
            '丰田 凯美瑞 Camry或同组车型',
          );
          expect(carVehicleList).toHaveTextContent('丰田 RAV4或同组车型');
          expect(carVehicleList).toHaveTextContent(
            '克莱斯勒 Pacifica或同组车型',
          );
          expect(vehicleListItem.length).toBe(6);
        },
      });
    },
  );
  test(
    createInterTestName({
      testId: [440441, 440439],
      name: '筛选功能',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );
      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[3];
      // 点击：筛选
      fireEvent.press(filterMoreTouchable);
      const filterModal = within(
        await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      const submitBtn = await Page.findByText('完成');
      const vehicleListItem = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );
      // 440439 选择支持香港驾照 预期：筛选支持香港驾照的车辆
      const filterBtn_4 = await filterModal.findByText('支持香港驾照');
      await renderWithAction({
        action: () => {
          fireEvent.press(filterBtn_4);
          // fireEvent.press(submitBtn);
        },
        expect: () => {
          expect(vehicleListItem.length).toBe(6);
        },
      });
    },
  );
  // @todo-lxj不稳定
  // test(
  //   createInterTestName({
  //     testId: [440441, 440438],
  //     name: '筛选功能-门店/服务',
  //   }),
  //   async () => {
  //     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
  //     const Page = await renderPage();
  //     const filterBar = await Page.findByTestId(
  //       UITestID.car_testid_page_list_filter_bar,
  //     );
  //     const filterMoreTouchable = await filterBar.findAllByType(
  //       TouchableOpacity,
  //     )[2];
  //     // 点击：门店/服务
  //     fireEvent.press(filterMoreTouchable);
  //     const filterModal = within(
  //       await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
  //     );
  //     const vehicleListItem = await Page.findAllByTestId(
  //       UITestID.car_testid_page_list_vehicle_osd,
  //     );
  //     // 440438 选择4.5分以上 预期：筛选4.5分以上的车辆
  //     const filterBtn_1 = await filterModal.findByText('4.5分以上');
  //     const submitBtn = await Page.findByText('完成');
  //     await renderWithAction({
  //       action: () => {
  //         fireEvent.press(filterBtn_1);
  //         fireEvent.press(submitBtn);
  //       },
  //       expect: async () => {
  //         expect(submitBtn.parent).toHaveStyle({
  //           color: '#fff',
  //         });
  //       },
  //     });
  //   },
  // );
  test(
    createInterTestName({
      testId: [440441, 440436],
      name: '筛选功能-门店/服务',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );
      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[2];
      // 点击：门店/服务
      fireEvent.press(filterMoreTouchable);
      const filterModal = within(
        await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      const vehicleListItem = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );
      // 440436 选择满油取还，24h营业 预期：筛选满油取还和24h营业车辆、
      const filterBtn_2 = await filterModal.findByText('满油取还');
      const filterBtn_3 = await filterModal.findByText('24h营业');
      const submitBtn = await Page.findByText('完成');
      await renderWithAction({
        action: () => {
          fireEvent.press(filterBtn_2);
          fireEvent.press(filterBtn_3);
          fireEvent.press(submitBtn);
        },
        expect: async () => {
          expect(vehicleListItem.length).toBe(6);
        },
      });
    },
  );
  test(
    createInterTestName({
      testId: [440441, 440433],
      name: '筛选功能-门店/服务',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );
      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[2];
      // 点击：门店/服务
      fireEvent.press(filterMoreTouchable);
      const filterModal = within(
        await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      const vehicleListItem = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );
      const submitBtn = await Page.findByText('完成');
      const carVehicleList = await Page.getByTestId(
        UITestID.car_testid_list_osd_carVehicleList,
      );

      // 440433 选择500米内 预期：筛选500米以内的门店车辆
      const filterBtn_4 = await filterModal.findByText('500米内');
      await renderWithAction({
        action: () => {
          fireEvent.press(filterBtn_4);
          fireEvent.press(submitBtn);
        },
        expect: async () => {
          expect(vehicleListItem.length).toBe(6);
        },
      });
    },
  );
  test(
    createInterTestName({
      testId: [440441, 440434],
      name: '筛选功能-品牌/配置',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );
      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[1];
      // 点击：品牌/配置
      fireEvent.press(filterMoreTouchable);
      const filterModal = within(
        await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      const vehicleListItem = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );
      const submitBtn = await Page.findByText('完成');
      // 440434 自动挡 预期：筛选自动挡的车
      const filterBtn_1 = await filterModal.findByText('自动挡');
      await renderWithAction({
        action: () => {
          fireEvent.press(filterBtn_1);
          fireEvent.press(submitBtn);
        },
        expect: async () => {
          expect(vehicleListItem.length).toBe(6);
        },
      });
    },
  );
  test(
    createInterTestName({
      testId: [440441, 440429],
      name: '筛选功能-品牌/配置',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );
      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[1];
      // 点击：品牌/配置
      fireEvent.press(filterMoreTouchable);
      const filterModal = within(
        await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      const vehicleListItem = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );
      const submitBtn = await Page.findByText('完成');
      const carVehicleList = await Page.getByTestId(
        UITestID.car_testid_list_osd_carVehicleList,
      );
      // 440429 选择座位数2座 预期：筛选2座位或者4座的车
      const filterBtn_2 = await filterModal.findByText('2座');

      await renderWithAction({
        action: () => {
          fireEvent.press(filterBtn_2);
          fireEvent.press(submitBtn);
        },
        expect: () => {
          expect(vehicleListItem.length).toBe(6);
        },
      });
    },
  );
  test(
    createInterTestName({
      testId: [440403, 440404, 440405],
      name: '点击筛选栏门店/服务',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );

      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[2];
      // 点击：门店/服务
      fireEvent.press(filterMoreTouchable);
      // 440403 取车地点非机场-左侧导航 预期：【门店距离 】；【点评】；【门店服务】；【特色服务】；
      const filterListNavBarDom = await Page.findByTestId(
        'car_testid_filter_nav_list_item',
      );
      const filterListNavBar = within(filterListNavBarDom);
      const filterListNavBarText = filterListNavBarDom.findAllByType(Text);
      expect(filterListNavBarText.length).toBe(4);
      expect(filterListNavBar.getByText('门店距离')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('点评')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('门店服务')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('特色服务')).toBeOnTheScreen();

      // 440404
      const sideBar = await Page.findAllByText('门店服务');
      fireEvent.press(sideBar[0]);
      // @ts-ignore
      expect(sideBar[1].parent).toHaveStyle(selectStyle);
      expect(sideBar[0]).toBeOnTheScreen();

      const filterModal = within(
        await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      const images = await Page.container.findAllByType(Image);
      const selectedFilters = images.filter(
        image => image.props.source.uri === SelectedImageIcon,
      );

      const filterBtn_1 = await filterModal.findByText('立即确认');
      const filterBtn_2 = await filterModal.findByText('免费取消');
      fireEvent.press(filterBtn_1);
      fireEvent.press(filterBtn_2);
      // 440405 点击清除
      setTimeout(async () => {
        const clearBtn = await Page.findByText('清除');
        fireEvent.press(clearBtn);
        waitFor(() => {
          expect(selectedFilters.length).toBe(0);
          expect(clearBtn).toBeFalsy();
        });
      }, 1000);
    },
  );
  test(
    createInterTestName({
      testId: [440441, 440459, 440462, 440456, 440457, 440460],
      name: '侧边导航',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      // 440457 起价逻辑 预期：确认导航栏起价为该分类内最低总价车型的日价

      await renderWithAction({
        action: async () => {
          const quickFilterButton2 = await Page.findByText('免费取消');
          fireEvent.press(quickFilterButton2);
        },
        expect: async () => {
          const vehicleGroupBar = await Page.findByTestId(
            'car_testid_list_VehicleGroupBar_中大型车',
          );
          // 440460
          expect(vehicleGroupBar).toHaveTextContent('¥581起');
        },
      });

      // 440459 联动筛选或快筛预期：确认筛选后无库存的分类置灰
      const quickFilterButton = await Page.findByText('仅需中国大陆驾照');
      await renderWithAction({
        action: () => {
          fireEvent.press(quickFilterButton);
        },
        expect: async () => {
          const sideBar01 = await Page.findByText('小型轿车');
          expect(sideBar01.parent).toHaveStyle({
            color: '#ccc',
          });
          // 440462 预期：确认筛选后侧边导航起价发生变化
          expect(Page.container).toHaveTextContent('暂无产品');

          // 440456 预期：确认无资源时tab置灰
          const sideBar02 = await Page.findByText('中大型车');
          expect(sideBar02.parent).toHaveStyle({
            color: '#ccc',
          });
          const sideBar03 = await Page.findByText('SUV');
          expect(sideBar03.parent).toHaveStyle({
            color: '#ccc',
          });
          const sideBar04 = await Page.findByText('MPV');
          expect(sideBar04.parent).toHaveStyle({
            color: '#ccc',
          });
        },
      });
    },
  );
  test(
    createInterTestName({
      testId: [440441, 440455],
      name: '侧边导航',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();

      // 440455 车型分组 预期：确认一个车型只会出现在一个分组中
      const vehicleGroupBars = await Page.findAllByTestId(
        /car_testid_list_VehicleGroupBar_/i,
      );
      vehicleGroupBars.forEach(async item => {
        await act(() => {
          fireEvent.press(item);
        });
        const vehicleNames = await Page.findAllByTestId(
          UITestID.car_testid_comp_vehicle_name,
        );
        const arr = [];
        vehicleNames.forEach(name => {
          arr.push(name.findAllByType(Text)[0].props.children[0]);
        });
        // 断言数组中的每个值都不相同
        expect(arr.filter((e, index) => arr.indexOf(e) === index)).toEqual(arr);
      });
    },
  );
  test(
    createInterTestName({
      testId: [
        440420, 440412, 440419, 440417, 440421, 440415, 440416, 440418, 440387,
        440390, 440446, 440422, 440413, 2863554, 455521,
      ],
      name: '点击筛选栏筛选按钮',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();
      const filterBar = await Page.getByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );
      // 440387 默认显示筛选项 预期：推荐排序；品牌/配置；门店/服务；筛选
      expect(filterBar).toHaveTextContent('推荐排序');
      expect(filterBar).toHaveTextContent('品牌/配置');
      expect(filterBar).toHaveTextContent('门店/服务');
      expect(filterBar).toHaveTextContent('筛选');
      // 440390 默认高亮 预期：“推荐排序”
      const filterBarDom = within(
        Page.getByTestId(UITestID.car_testid_page_list_filter_bar),
      );
      const recommendedSort = await filterBarDom.findByText('推荐排序');
      expect(recommendedSort.parent).toHaveStyle(selectStyle);

      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[3];
      // 点击：筛选
      fireEvent.press(filterMoreTouchable);

      // 440412 左侧导航 预期：【价格】；【支付方式】；【驾照要求】；【押金方式】；【里程限制】；【租车公司】
      const filterListNavBar = within(
        await Page.findByTestId('car_testid_filter_nav_list_item'),
      );
      const filterListNavBarDom = await Page.findByTestId(
        'car_testid_filter_nav_list_item',
      );
      const filterListNavBarText = filterListNavBarDom.findAllByType(Text);
      expect(filterListNavBarText.length).toBe(6);
      expect(filterListNavBar.getByText('价格')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('支付方式')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('驾照要求')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('押金方式')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('里程限制')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('租车公司')).toBeOnTheScreen();

      const filterModal = within(
        Page.getByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      const images = await Page.container.findAllByType(Image);
      const selectedFilters = images.filter(
        image => image.props.source.uri === SelectedImageIcon,
      );
      // 440420 租车公司 预期：支持多选，并集筛选
      const filterBarBtn = await filterListNavBar.findByText('租车公司');
      fireEvent.press(filterBarBtn);
      const filterBtn_1 = await filterModal.findByText('Hertz');
      const filterBtn_2 = await filterModal.findByText('Avis');
      fireEvent.press(filterBtn_1);
      fireEvent.press(filterBtn_2);
      // @ts-ignore
      expect(filterBtn_1.parent).toHaveStyle(selectStyle);
      // @ts-ignore
      expect(filterBtn_2.parent).toHaveStyle(selectStyle);

      // 440419 里程限制 预期：不支持多选
      const filterBtn_3 = await filterModal.findByText('不限里程');
      const filterBtn_4 = await filterModal.findByText('限里程');
      fireEvent.press(filterBtn_3);
      fireEvent.press(filterBtn_4);
      // @ts-ignore
      expect(filterBtn_3.parent).toHaveStyle(unSelectStyle);
      // @ts-ignore
      expect(filterBtn_4.parent).toHaveStyle(selectStyle);

      // 440417 驾照要求 预期：支持多选，并集筛选
      const filterBtn_5 = await filterModal.findByText('仅需中国大陆驾照');
      const filterBtn_6 = await filterModal.findByText(
        '支持中国驾照原件+驾照国际翻译认证件',
      );
      expect(
        filterModal.getByText('支持中国驾照原件+车行翻译件'),
      ).toBeOnTheScreen();
      expect(
        filterModal.getByText('支持中国驾照原件+当地语言公证件'),
      ).toBeOnTheScreen();
      expect(
        filterModal.getByText('支持国际驾照IDP+签发国当地驾照'),
      ).toBeOnTheScreen();
      expect(filterModal.getByText('支持香港驾照')).toBeOnTheScreen();
      expect(
        filterModal.getByText('支持国际驾照IDP+签发国当地驾照'),
      ).toBeOnTheScreen();
      fireEvent.press(filterBtn_5);
      fireEvent.press(filterBtn_6);
      // @ts-ignore
      expect(filterBtn_5.parent).toHaveStyle(selectStyle);
      // @ts-ignore
      expect(filterBtn_6.parent).toHaveStyle(selectStyle);

      // 440421 跨类别多选 预期：交集筛选
      waitFor(() => {
        expect(selectedFilters.length).toBe(5);
      });

      // 440415 价格 预期：不支持多选
      const filterBtn_7 = await filterModal.findByText('¥100以下');
      const filterBtn_8 = await filterModal.findByText('¥100-200');
      fireEvent.press(filterBtn_7);
      fireEvent.press(filterBtn_8);
      // @ts-ignore
      expect(filterBtn_7.parent).toHaveStyle(unSelectStyle);
      // @ts-ignore
      expect(filterBtn_8.parent).toHaveStyle(selectStyle);

      // 440416 支付方式 预期：不支持多选
      const filterBtn_9 = await filterModal.findByText('在线支付');
      const filterBtn_10 = await filterModal.findByText('到店支付');
      fireEvent.press(filterBtn_9);
      fireEvent.press(filterBtn_10);
      // @ts-ignore
      expect(filterBtn_9.parent).toHaveStyle(unSelectStyle);
      // @ts-ignore
      expect(filterBtn_10.parent).toHaveStyle(selectStyle);

      // 440422 押金方式 信用卡 支持多选
      const filterBtn_11 = await filterModal.findByText('支持银联');
      const filterBtn_12 = await filterModal.findByText('支持非凸字信用卡');
      fireEvent.press(filterBtn_11);
      fireEvent.press(filterBtn_12);
      // @ts-ignore
      expect(filterBtn_11.parent).toHaveStyle(selectStyle);
      // @ts-ignore
      expect(filterBtn_12.parent).toHaveStyle(selectStyle);

      // 440418 点击清除 预期：1、弹层收起 2、该弹层所有筛选清除
      setTimeout(async () => {
        const clearBtn = await Page.findByText('清除');
        fireEvent.press(clearBtn);
        waitFor(() => {
          expect(selectedFilters.length).toBe(0);
          expect(clearBtn).toBeFalsy();
        });

        // 440446 取车地点为美国，列表页驾照提示
        expect(Page.container).toHaveTextContent('持中国大陆驾照可在美国租车');
      }, 1000);
    },
  );
  test(
    createInterTestName({
      testId: [440458],
      name: '侧边导航- 全量枚举',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      const Page = await renderPage();

      // 440458 预期：确认全量枚举值为[全部车型,小型轿车，中大型车，SUV，MPV，高级轿车，跑车/轿跑,皮卡]
      const vehicleGroupBars = await Page.findAllByTestId(
        /car_testid_list_VehicleGroupBar_/i,
      );
      const arr = [
        '全部车型',
        '小型轿车',
        '中大型车',
        'SUV',
        'MPV',
        '高级轿车',
        '跑车/轿跑',
        '皮卡',
      ];
      await waitRender(() => {
        vehicleGroupBars.forEach((item, index) => {
          expect(item.findAllByType(Text)[0].props.children).toEqual(
            arr[index],
          );
        });
      });
    },
    1000,
  );
  test(
    createInterTestName({
      testId: [
        440395, 440397, 440396, 440398, 440399, 440400, 440401, 440402, 2860467,
        2863050, 2863064, 2863085, 2863092, 455522, 2863554,
      ],
      name: '点击筛选栏品牌/配置',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(Utils, 'getUniqRequestKeyWithEnv')
        .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );

      const filterMoreTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[1];
      // 点击：品牌/配置
      fireEvent.press(filterMoreTouchable);
      // 440398 左侧导航 内容 【座位数】 ；【车辆排挡】；【车辆配置】；【车辆品牌】
      const filterListNavBar = within(
        await Page.findByTestId('car_testid_filter_nav_list_item'),
      );
      expect(filterListNavBar.getByText('座位数')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('车辆排挡')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('能源驱动')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('车辆配置')).toBeOnTheScreen();
      expect(filterListNavBar.getByText('车辆品牌')).toBeOnTheScreen();

      // 440395 点击左侧导航
      const sideBar = await Page.findAllByText('车辆排挡');
      fireEvent.press(sideBar[0]);
      // @ts-ignore
      expect(sideBar[1].parent).toHaveStyle(selectStyle);
      expect(sideBar[0]).toBeOnTheScreen();

      const sideBar1 = await Page.findAllByText('能源驱动');
      fireEvent.press(sideBar1[0]);
      // @ts-ignore
      expect(sideBar1[1].parent).toHaveStyle(selectStyle);
      expect(sideBar1[0]).toBeOnTheScreen();

      const sideBar2 = await Page.findAllByText('车辆配置');
      fireEvent.press(sideBar2[0]);
      // @ts-ignore
      expect(sideBar2[1].parent).toHaveStyle(selectStyle);
      expect(sideBar2[0]).toBeOnTheScreen();


      const sideBar3 = await Page.findAllByText('车辆品牌');
      fireEvent.press(sideBar3[0]);
      // @ts-ignore
      expect(sideBar3[1].parent).toHaveStyle(selectStyle);
      expect(sideBar3[0]).toBeOnTheScreen();

      const filterModal = within(
        await Page.findByTestId(UITestID.car_testid_list_filterListWithNav),
      );
      // 440397 座位数 支持多选
      const filterBtn_1 = await Page.findByText('6座');
      const filterBtn_2 = await Page.findByText('7座');
      fireEvent.press(filterBtn_1);
      fireEvent.press(filterBtn_2);
      // @ts-ignore
      expect(filterBtn_1.parent).toHaveStyle(selectStyle);
      // @ts-ignore
      expect(filterBtn_2.parent).toHaveStyle(selectStyle);

      // 440400 车辆品牌 支持多选
      const filterBtn_3 = await Page.findByText('现代');
      const filterBtn_4 = await Page.findByText('日产');
      fireEvent.press(filterBtn_3);
      fireEvent.press(filterBtn_4);
      // @ts-ignore
      expect(filterBtn_3.parent).toHaveStyle(selectStyle);
      // @ts-ignore
      expect(filterBtn_4.parent).toHaveStyle(selectStyle);

      // 440399 440401 440402 车辆排档
      const filterBtn_5 = await filterModal.findAllByText('自动挡');
      const filterBtn_6 = await filterModal.findAllByText('手动挡');
      fireEvent.press(filterBtn_5[0]);
      fireEvent.press(filterBtn_6[0]);
      const images = await Page.container.findAllByType(Image);
      const selectedFilters = images.filter(
        image => image.props.source.uri === SelectedImageIcon,
      );
      expect(selectedFilters.length).toBe(5);

      setTimeout(async () => {
        // 440396 点击清除
        const clearBtn = await Page.findByText('清除');
        fireEvent.press(clearBtn);
        waitFor(() => {
          expect(selectedFilters.length).toBe(0);
          expect(clearBtn).toBeFalsy();
        });
      }, 1000);
    },
  );
});

test(
  createInterTestName({
    testId: [440392],
    name: '推荐排序',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(res440391));
    const Page = await renderPage();

    // 440392 默认推荐排序
    await waitRender(async () => {
      const vehicleList = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );

      expect(vehicleList[0]).toHaveTextContent('福特嘉年华Fiesta或同组车型');
      expect(vehicleList[1]).toHaveTextContent('日产 Versa或同组车型');
      expect(vehicleList[2]).toHaveTextContent('雪佛兰Spark柴油版');
      expect(vehicleList[3]).toHaveTextContent('特 Focus 2门版');
      expect(vehicleList[4]).toHaveTextContent('雪佛兰 Spark 4门版');
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [440391],
    name: '推荐排序',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(res440391));
    const Page = await renderPage();

    await waitRender(async () => {
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );

      const filterTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[0];

      // 点击 推荐排序
      await fireEvent.press(filterTouchable);

      const sortModal = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_sort_modal,
      );

      const priceSortBtn = await sortModal.findAllByType(TouchableOpacity)[1];

      // 点击 推荐价格排序
      await fireEvent.press(priceSortBtn);

      await delayPromise();

      // 页面车型按价格进行排序
      const vehicleList = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );

      expect(vehicleList[0]).toHaveTextContent('雪佛兰 Spark 4门版');
      expect(vehicleList[1]).toHaveTextContent('福特 Focus 2门版');
      expect(vehicleList[2]).toHaveTextContent('福特嘉年华Fiest');
      expect(vehicleList[3]).toHaveTextContent('雪佛兰Spark柴油版');
      expect(vehicleList[4]).toHaveTextContent('大众 捷达 Jetta');

      const sortModal2 = await Page.queryByTestId(
        UITestID.car_testid_page_list_filter_sort_modal,
      );

      expect(sortModal2).not.toBeTruthy();
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [440393],
    name: '推荐排序',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(res440391));
    const Page = await renderPage();

    await waitRender(async () => {
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );

      const filterTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[0];

      // 点击 推荐排序
      await fireEvent.press(filterTouchable);

      await delayPromise();

      const sortModal = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_sort_modal,
      );

      const allTexts = await sortModal.findAllByType(Text);
      expect(allTexts[1]).toHaveStyle({
        color: color.blueBase,
      });
    });
  },
  TEST_TIMEOUT,
);
