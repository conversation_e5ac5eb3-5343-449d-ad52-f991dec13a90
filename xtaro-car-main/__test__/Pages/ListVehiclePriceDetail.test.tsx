import { fireEvent, act } from '@testing-library/react-native';
import { Utils, CarStorage } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';

// const res411022 = require('../../__mocks__/restful/18631/queryProducts/411022.json');

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: true,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

test(
  createInterTestName({
    testId: [411019, 411016],
    name: '费用明细',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    // jest
    //   .spyOn(CarFetch, 'getListProduct')
    //   .mockImplementation(() => Promise.resolve(res411022));

    const Page = await renderPage();

    const priceHelp = await Page.findAllByTestId(
      UITestID.car_testid_comp_vehicle_price_help,
    );
    // 点击总价说明弹窗
    await act(() => {
      fireEvent(priceHelp[0], 'onPress');
    });

    const priceModal1 = await Page.findByTestId(
      UITestID.car_testid_comp_price_modal,
    );

    expect(priceModal1).toHaveTextContent('- ¥7');

    await act(() => {
      fireEvent(priceHelp[1], 'onPress');
    });
    const priceModal2 = await Page.findByTestId(
      UITestID.car_testid_comp_price_modal,
    );
    expect(priceModal2).toHaveTextContent('- ¥41');
  },
);
