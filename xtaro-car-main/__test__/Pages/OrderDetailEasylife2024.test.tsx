import { fireEvent, act } from '@testing-library/react-native';
import { Utils, AppContext, User } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import OrderDetail from '../../src/pages/xcar/Containers/OrderDetailContainer';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { renderWithAction, findPageImage } from '../testHelpers';
// @ts-ignore 禁止Redux Log
__DEV__ = false;

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

// 页面初始化
const renderPage = async (initialState = {}) => {
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
  jest.spyOn(User, 'isLoginSync').mockReturnValue(true);
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'OrderDetail',
    orderId: '12321',
  });

  return createPage(OrderDetail, {}, initialState);
};
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));

test(
  createInterTestName({
    testId: [
      3532789, 3532754, 3532775, 3532782, 3532796, 3532803, 3532831, 3532768,
      3532747,
    ],
    name: '无忧租2024订详',
  }),
  async () => {
    const state = {
      OrderDetail: {
        reqOrderParams: {
          orderId: 1234567,
        },
        storageCardsTitle: [],
      },
    };
    const Page = await renderPage(state);
    const priceDetailBtn = await Page.findByTestId(
      UITestID.car_testid_page_order_price_detail,
    );
    // 3532754 车型信息上面增加无忧租2024banner
    const banner = await findPageImage(
      Page,
      'https://dimg04.c-ctrip.com/images/1tg0i12000e015fmi22A9.png',
    );
    expect(banner).toBeOnTheScreen();
    // 3532789,3532775,3532782,3532796,3532803,3532831,3532768 费用明细
    await renderWithAction({
      action: async () => {
        await act(async () => {
          fireEvent(priceDetailBtn, 'click');
        });
      },
      expect: async () => {
        const priceDetailModal = await Page.findByTestId(
          UITestID.car_testid_comp_price_modal,
        );
        expect(priceDetailModal).toBeTruthy();
        expect(priceDetailModal).toHaveTextContent('费用明细');
        expect(priceDetailModal).toHaveTextContent(
          '车辆租赁费无忧租+车损全免保障¥138',
        );

        expect(priceDetailModal).toHaveTextContent(
          '国内立减折扣9折立减券- ¥42',
        );
        expect(priceDetailModal).toHaveTextContent('6月25日 周二¥ 138');
        expect(priceDetailModal).toHaveTextContent('附加产品儿童座椅¥ 50 x1');
        expect(priceDetailModal).toHaveTextContent('订单总额¥188');
        expect(priceDetailModal).toHaveTextContent('在线支付');
        expect(priceDetailModal).toHaveTextContent(
          '还车后可获积分53已享1.8倍加',
        );
      },
    });
    // 3532747 增加多名驾驶员
    await renderWithAction({
      action: async () => {
        const driverItem = await Page.findByText('增加多名驾驶员');
        fireEvent.press(driverItem);
      },
      expect: () => {
        expect(Page.container).toHaveTextContent(
          '默认仅支持一名驾驶员。如需多人开车，请提前与门店联系，无额外收费。每名驾驶员都需要出示与主驾驶员相同的取车证件。',
        );
      },
    });
  },
);
