import { fireEvent, act } from '@testing-library/react-native';
import Image from '@c2x/components/Image';;
import Toast from '../../src/pages/xcar/Common/src/Components/Basic/Toast/src';
import { Utils, GetAB } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { pressWithTestId, waitRender, renderWithAction } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined, orderId, pageParam = {}) => {
  // eslint-disable-next-line global-require
  const Page = require('../../src/pages/xcar/Containers/GuidePageContainer').default;
  return createPage(
    Page,
    {
      orderId,
      ...pageParam,
    },
    initialState,
  );
};

test(
  createInterTestName({
    testId: [408051, 408052, 408054, 579716, 579704],
    name: '地图指引页二级页面',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const Page = await renderPage(undefined, 'orderId');
    await pressWithTestId(Page, UITestID.car_testid_page_address_copy);
    const ToastFunc = jest.spyOn(Toast, 'show');
    expect(ToastFunc).toBeCalledWith('复制成功', 1);

    await pressWithTestId(Page, UITestID.car_testid_page_guide_store_call);
    expect(Page.container).toHaveTextContent('拨打13867279511拨打15868243731');
    // 579716 579704 POI为非汇合点-指引信息
    expect(Page.container).toHaveTextContent('取车方式店员收费送车上门');
  },
  10000,
);
const mockPageParamsFromOrderDetail = {
  pickupStart: {
    lat: 30.56,
    lng: 103.95,
    addr: '双流国际机场T1航站楼-行中汇合点测试名称',
    meetingPointAddress: '双流国际机场T1航站楼-行中汇合点测试名称',
  },
  dropoffStart: {
    lat: 30.56,
    lng: 103.95,
    addr: '双流国际机场T1航站楼-行中汇合点测试名称',
    meetingPointAddress: '双流国际机场T1航站楼-行中汇合点测试名称',
  },
  pickupStoreId: 107072,
  dropoffStoreId: 107072,
  rentCenterId: -1,
  pickupServiceType: '2',
  dropoffServiceType: '2',
  pStoreWay: '',
  rStoreWay: '',
  fixMap: true,
  isHidePhone: false,
  orderId: '36515627907',
  orderStatus: 6,
  pickupStoreGuide: '',
  returnStoreGuide: '',
  isFromOrderDetail: true,
  scene: '2',
  selectedId: 'pickup',
};
test(
  createInterTestName({
    testId: [579681],
    name: '汇合点项目订详跳地图指引页页',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    mockPageParamsFromOrderDetail.selectedId = 'dropoff';
    const Page = await renderPage(
      undefined,
      'orderId',
      mockPageParamsFromOrderDetail,
    );
    expect(Page.container).toHaveTextContent(
      '取车地址双流国际机场T1航站楼-全家汇合点',
    );
    expect(Page.container).toHaveTextContent(
      '第一步取车，先锁定门店!02第二步，航站楼1号出口直行100米03第三步：机场路右拐50米04第一步还车，先把邮箱加满!05第二步还车：把车洗干净！06第三步还车：把车开到店里',
    );
  },
  10000,
);
const mockPageParams = {
  pickupStart: {
    lat: 30.56,
    lng: 103.95,
    addr: '双流国际机场T1航站楼-行中汇合点测试名称',
    meetingPointAddress: '双流国际机场T1航站楼-行中汇合点测试名称',
  },
  dropoffStart: {
    lat: 30.56,
    lng: 103.95,
    addr: '双流国际机场T1航站楼-行中汇合点测试名称',
    meetingPointAddress: '双流国际机场T1航站楼-行中汇合点测试名称',
  },
  pickupStoreId: 107072,
  dropoffStoreId: 107072,
  // selectedId: 'dropoff',
  rentCenterId: -1,
  pickupServiceType: '2',
  dropoffServiceType: '2',
  pStoreWay: '',
  rStoreWay: '',
  fixMap: true,
  isHidePhone: false,
  pickupStoreGuide: '',
  returnStoreGuide: '',
};
test(
  createInterTestName({
    testId: [579698, 579699, 579700, 579697, 579705],
    name: '地图指引页-汇合点项目-免费送车上门',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const Page = await renderPage(undefined, undefined, {
      pageParam: mockPageParams,
    });
    await waitRender(() => {
      // 579698 579697
      expect(Page.container).toHaveTextContent('取车方式店员免费送车上门');
      // 579705
      expect(Page.container).toHaveTextContent(
        '第一步取车，先锁定门店!02第二步，航站楼1号出口直行100米03第三步：机场路右拐50米',
      );
      // 579700
      expect(Page.container).toHaveTextContent('取车地址成都双流机场到达层2楼');
    });
  },
  10000,
);

test(
  createInterTestName({
    testId: [579706, 579702, 579705, 579703, 579708],
    name: '地图指引页-汇合点项目-收费送车上门',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const Page = await renderPage(undefined, undefined, {
      pageParam: mockPageParams,
    });
    await waitRender(() => {
      // 579706 579703
      expect(Page.container).toHaveTextContent('取车方式店员收费送车上门');
      // 579705
      expect(Page.container).toHaveTextContent(
        '第一步取车，先锁定门店!02第二步，航站楼1号出口直行100米03第三步：机场路右拐50米',
      );
      // 579702
      expect(Page.container).toHaveTextContent('取车地址成都双流机场到达层2楼');
    });
    const dropoffBtn = await Page.findByText('还车');
    await renderWithAction({
      action: async () => {
        fireEvent.press(dropoffBtn);
      },
      expect: () => {
        // 579708
        expect(Page.container).toHaveTextContent('店员收费上门取车');
      },
    });
  },
  10000,
);

test(
  createInterTestName({
    testId: [579712, 579709, 579710, 579711],
    name: '地图指引页-汇合点项目-poi为汇合点-门店接送',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const Page = await renderPage(undefined, undefined, {
      pageParam: mockPageParams,
    });
    const pageDom = Page.container;
    const imagesUrl = [
      'https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/path.png',
      'https://dimg04.c-ctrip.com/images/0yc2812000ckrvx0o1953.png?timestamps=20230329',
      'https://dimg04.c-ctrip.com/images/0yc2512000ckrrvkvB127.png?timestamps=20230329',
      'https://dimg04.c-ctrip.com/images/0yc6w12000ckrvxfp47C8.jpg?timestamps=20230329',
      'https://dimg04.c-ctrip.com/images/0yc2912000ckrp2jnE0A9.jpg?timestamps=20230329',
      'https://dimg04.c-ctrip.com/images/0yc0i12000ckrp2jzBBB4.jpg?timestamps=20230329',
      'https://dimg04.c-ctrip.com/images/0yc3d12000ckro008D8A5.jpg?timestamps=20230329',
      'https://dimg04.c-ctrip.com/images/0yc4l12000ckrp2eqF3E1.jpg?timestamps=20230329',
      'https://dimg04.c-ctrip.com/images/0AS4r12000a2mie29BF26.png_.webp',
    ];
    await waitRender(() => {
      // 579709
      expect(pageDom).toHaveTextContent('取车方式店员免费接您至门店取车');
      expect(pageDom).toHaveTextContent(
        '店名称杭州机场店门店地址萧山区云丰汽车充电站南50米(城红线西) 萧山区城红线',
      );
      expect(pageDom).toHaveTextContent('联系电话17706433362');
      expect(pageDom).toHaveTextContent('营业时间24小时营业');
      expect(pageDom).toHaveTextContent(
        '如何到达取车点01T2航站楼出口直行过马路到达【交通换乘中心】，右转02步行约20米，到达扶梯，乘坐扶梯下行03扶梯下行，到达负一层，左转04步行约50米，到达交通中心停车库4号电梯厅（位于您左手边），直行进入电梯厅，右转05步行约30米，左转（可跟随地面【无障碍电梯】标识）06到达电梯口，乘坐电梯下行，到达B3层07您已到达目的地，请在此处等候您的号码将被加密后呼出，为保障服务质量，您的通话可能会被录音。立即呼叫（加密）',
      );
      const allImages = pageDom.findAllByType(Image);
      allImages.forEach((image, index) => {
        expect(image.props.source.uri).toEqual(imagesUrl[index]);
      });
    });
    await pressWithTestId(Page, UITestID.car_testid_page_doorAddress_copy);
    const ToastFunc = jest.spyOn(Toast, 'show');
    expect(ToastFunc).toBeCalledWith('复制成功', 1);

    // 579709
    const returnBtn = await Page.findByText('还车');
    await act(() => {
      fireEvent.press(returnBtn);
    });
    expect(pageDom).toHaveTextContent(
      '还车后店员免费送您至萧山国际机场T2航站楼-停车库4号电梯厅',
    );
    const allImages = pageDom.findAllByType(Image);
    allImages.forEach((image, index) => {
      expect(image.props.source.uri).not.toEqual(imagesUrl[index]);
    });
  },
  10000,
);

test(
  createInterTestName({
    testId: [579717],
    name: '地图指引页-汇合点项目-非poi为汇合点-门店接送',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const Page = await renderPage(undefined, undefined, {
      pageParam: mockPageParams,
    });
    const pageDom = Page.container;
    await waitRender(() => {
      expect(pageDom).toHaveTextContent(
        '取车方式店员免费接您至门店取车门店名称EVCARD杭州东站店门店地址杭州东站P5地下停车场C5区C5-0577车位󰇖联系电话19518301050营业时间08:00 - 20:00您的号码将被加密后呼出，为保障服务质量，您的通话可能会被录音。立即呼叫（加密）',
      );
    });
    await pressWithTestId(Page, UITestID.car_testid_page_doorAddress_copy);
    const ToastFunc = jest.spyOn(Toast, 'show');
    expect(ToastFunc).toBeCalledWith('复制成功', 1);

    const returnBtn = await Page.findByText('还车');
    await act(() => {
      fireEvent.press(returnBtn);
    });
    expect(pageDom).toHaveTextContent(
      '还车方式还车后店员免费送您至杭州东火车站门店名称EVCARD杭州东站店门店地址杭州东站P5地下停车场C5区C5-0577车位󰇖联系电话19518301050营业时间08:00 - 20:00您的号码将被加密后呼出，为保障服务质量，您的通话可能会被录音。立即呼叫（加密）',
    );
  },
  10000,
);
test(
  createInterTestName({
    testId: [579713],
    name: '地图指引页-汇合点项目-poi为汇合点-门店自取',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const Page = await renderPage(undefined, undefined, {
      pageParam: mockPageParams,
    });
    const pageDom = Page.container;
    await waitRender(() => {
      expect(pageDom).toHaveTextContent(
        '取车方式自行前往门店取车门店名称荣望轩送车点门店地址浙江省杭州市萧山区知行路857号',
      );
    });
  },
  10000,
);

test(
  createInterTestName({
    testId: [579714],
    name: '地图指引页-汇合点项目-poi为非汇合点-门店自取',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const Page = await renderPage(undefined, undefined, {
      pageParam: mockPageParams,
    });
    const pageDom = Page.container;
    await waitRender(() => {
      expect(pageDom).toHaveTextContent(
        '取车方式自行前往门店取车门店名称笕桥送车点门店地址杭州市上城区丁桥镇丁兰路92-94号󰇖联系电话0571-87802632营业时间10:00 - 16:00如何到达取车点',
      );
    });
  },
  10000,
);

test(
  createInterTestName({
    testId: [579715],
    name: '地图指引页-汇合点项目-poi为汇合点-站内取车',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const Page = await renderPage(undefined, undefined, {
      pageParam: mockPageParams,
    });
    const pageDom = Page.container;
    await waitRender(() => {
      expect(pageDom).toHaveTextContent(
        '取车方式免费站内取车门店地址门店位于携程租车中心·三亚凤凰机场店三亚凤凰国际机场停车楼4楼󰇖联系电话17784642345;0898-88667777',
      );
      expect(pageDom).toHaveTextContent(
        '营业时间24小时营业如何到达取车点01到达厅出7号门直行02直行30米左拐03直行50米04右拐进入电梯05左右两边电梯皆可上四楼06出电梯右拐07到达携程租车中心',
      );
    });
  },
  10000,
);
