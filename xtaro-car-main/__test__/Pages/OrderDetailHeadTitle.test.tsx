import { waitFor } from '@testing-library/react-native';
import { Utils, User, AppContext } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import OrderDetail from '../../src/pages/xcar/Containers/OrderDetailContainer';
import UITestId from '../../src/pages/xcar/Constants/UITestID';
// @ts-ignore 禁止Redux Log
__DEV__ = false;

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));

// 页面初始化
const renderPage = async (initialState = {}) => {
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
  jest.spyOn(User, 'isLoginSync').mockReturnValue(true);
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'OrderDetail',
    orderId: '15372819832',
  });

  return createPage(OrderDetail, {}, initialState);
};

test(
  createInterTestName({
    testId: [942194],
    name: '非自助取还订单',
  }),
  async () => {
    const Page = await renderPage();
    await waitFor(() => {
      const pageHeader = Page.getByTestId(
        UITestId.car_testid_page_order_detail_page_header,
      );
      // 942194
      expect(pageHeader).toHaveTextContent('订单号 15372819832');
    });
  },
);

test(
  createInterTestName({
    testId: [942208],
    name: '门店扫码订单',
  }),
  async () => {
    const Page = await renderPage();
    await waitFor(() => {
      const pageHeader = Page.getByTestId(
        UITestId.car_testid_page_order_detail_page_header,
      );
      // 942194
      expect(pageHeader).toHaveTextContent('门店扫码 15372819832');
    });
  },
);

test(
  createInterTestName({
    testId: [942215],
    name: '门店扫码下的自助取还订单',
  }),
  async () => {
    const Page = await renderPage();
    await waitFor(() => {
      const pageHeader = Page.getByTestId(
        UITestId.car_testid_page_order_detail_page_header,
      );
      // 942194
      expect(pageHeader).toHaveTextContent('自助取还 15372819832');
    });
  },
);
