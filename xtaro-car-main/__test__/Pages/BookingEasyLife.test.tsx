import { fireEvent } from '@testing-library/react-native';
import { Utils, User, AppContext } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';
import { renderWithAction, waitRender } from '../testHelpers';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(
    Booking,
    {
      vendorPriceInfo: {
        reference: {
          packageLevel: 'PREP',
        },
      },
    },
    initialState,
  );
};

test(
  createInterTestName({
    testId: [3532628, 489353, 3532691, 3532691, 3532712, 3532719],
    name: '填写页无忧租改动 - banner 驾驶员',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    // 3532628 顶部增加无忧租banner
    const banner = await Page.findByTestId('booking_easylife_2024_banner');
    expect(banner).toBeOnTheScreen();
    await waitRender(async () => {
      // 3532691 3532712 3532719
      expect(Page.container).toHaveTextContent('暂无可享优惠');
    });
    // 489353 增加多名驾驶员
    await renderWithAction({
      action: async () => {
        const driverItem = await Page.findByText('增加多名驾驶员');
        fireEvent.press(driverItem);
      },
      expect: () => {
        expect(Page.container).toHaveTextContent(
          '默认仅支持一名驾驶员。如需多人开车，请提前与门店联系，无额外收费。每名驾驶员都需要出示与主驾驶员相同的取车证件。',
        );
      },
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [3532628, 3532663, 3532670, 3532649, 3532698, 3532705, 3532684],
    name: '填写页无忧租改动 - 费用明细',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    await waitRender(async () => {
      // 3532691 3532712 3532719
      expect(Page.container).toHaveTextContent('暂无可享优惠');
    });
    // 3532663, 3532670,3532649,3532698,3532705,3532684
    await renderWithAction({
      action: async () => {
        const priceDetail = await Page.findByText('订单总额');
        fireEvent.press(priceDetail);
      },
      expect: async () => {
        expect(Page.container).toHaveTextContent(
          '车辆租赁费无忧租+车损全免保障¥60',
        );
        expect(Page.container).toHaveTextContent('6月26日 周三¥60');
        expect(Page.container).toHaveTextContent('国内立减折扣9折立减券- ¥15');
        expect(Page.container).toHaveTextContent('一口价¥60');
        expect(Page.container).toHaveTextContent('在线支付');
        expect(Page.container).toHaveTextContent('还车后可获积分14已享1.5倍加');
      },
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [3532628, 489333],
    name: '填写页无忧租改动 - 点击无忧租banner',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    await waitRender(async () => {
      // 3532691 3532712 3532719
      expect(Page.container).toHaveTextContent('暂无可享优惠');
    });
    // 489333 无忧租banner
    let vehicleBanner;
    await renderWithAction({
      action: async () => {
        const vehicle = await Page.findByTestId('booking_vehicle_info');
        fireEvent.press(vehicle);
      },
      expect: async () => {
        vehicleBanner = await Page.findByTestId(
          'product_confirm_easylife_2024_banner',
        );
        expect(vehicleBanner).toBeOnTheScreen();
      },
    });
    await renderWithAction({
      action: async () => {
        fireEvent.press(vehicleBanner);
      },
      expect: async () => {
        const vehicleEasyLifeListModal = await Page.findByTestId(
          'easylife_tag_list_modal',
        );
        expect(vehicleEasyLifeListModal).toBeOnTheScreen();
      },
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [3532628, 3532740],
    name: '填写页无忧租改动 - 保险弹层底部差价利益点',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    await waitRender(async () => {
      // 3532691 3532712 3532719
      expect(Page.container).toHaveTextContent('暂无可享优惠');
    });
    // 3532740 底部差价利益点
    await renderWithAction({
      action: async () => {
        const serviceDetail = await Page.findByTestId(
          'booking_service_detail_button',
        );
        fireEvent.press(serviceDetail);
      },
      expect: async () => {
        const insDetailModal = await Page.findByTestId(
          'car_testid_comp_booking_ins_detail_modal',
        );
        expect(insDetailModal).not.toHaveTextContent('已含');
      },
    });
  },
  TEST_TIMEOUT,
);
