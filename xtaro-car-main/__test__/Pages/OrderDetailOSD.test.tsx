import { waitFor, fireEvent } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import Call from '@c2x/components/Call';
import URL from '@c2x/apis/URL';
import { AppContext, Utils, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import UITestId from '../../src/pages/xcar/Constants/UITestID';
import { findPageImage, renderWithAction, waitRender } from '../testHelpers';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { name: 1 }, initialState);
};
const res1 = {
  vendorInfo: {
    vendorName: 'Avis',
    vendorImageUrl:
      '//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png',
    confirmRightNow: false,
    confirmDate: 1693642536000,
    confirmDateStr: '2023-09-02 16:15',
    platformCode: 'P10',
    platformName: 'Qunar.com',
    bizVendorCode: '14060003',
    commentInfo: {
      vendorGoodType: 0,
      exposedScore: 4.5,
      topScore: 5.0,
      level: '满意',
      commentCount: 1,
      hasComment: 0,
    },
    broker: true,
  },
  orderBaseInfo: {
    orderId: 26094177511,
    uId: 'M2331903879',
    channelType: '17671',
    orderDate: 1693638936000,
    orderLocale: 'zh_cn',
    orderStatus: 4,
    orderStatusDesc: '已取消',
    orderStatusName: '已取消',
    orderStatusCtrip: 'CAR_CANCELLED',
    allStatuses: [],
    allOperations: [
      { operationId: 4, buttonName: '再次预订', enable: true },
      { operationId: 3, enable: false, code: 4 },
    ],
    orderTip: {
      tipType: 5,
      tipContent:
        '您的订单已免费取消。款项将于1-5个工作天內退还至原付款账户，实际时间受您的银行影响。',
      tipContentArray: [
        '您的订单已免费取消。款项将于1-5个工作天內退还至原付款账户，实际时间受您的银行影响。',
      ],
      warnType: 0,
    },
    payMode: 2,
    isEasyLife: false,
    extOperation: [],
  },
  baseResponse: {
    isSuccess: true,
    code: 'unknown',
    returnMsg: 'success',
    requestId: '',
    cost: 369,
  },
};

const res2 = {
  orderBaseInfo: {
    orderId: 26094177511,
    uId: 'M2331903879',
    channelType: '17671',
    orderDate: 1693638936000,
    orderLocale: 'zh_cn',
    orderStatus: 4,
    orderStatusDesc: '已取消',
    orderStatusName: '已取消',
    orderStatusCtrip: 'CAR_CANCELLED',
    allStatuses: [],
    allOperations: [
      { operationId: 4, buttonName: '再次预订', enable: true },
      { operationId: 3, enable: false, code: 4 },
    ],
    orderTip: {
      tipType: 5,
      tipContent: '很抱歉，由于价格/库存变动，您的订单已取消',
      tipContentArray: ['很抱歉，由于价格/库存变动，您的订单已取消'],
      warnType: 0,
    },
    payMode: 2,
    isEasyLife: false,
    extOperation: [],
  },
  baseResponse: {
    isSuccess: true,
    code: 'unknown',
    returnMsg: 'success',
    requestId: '',
    cost: 369,
  },
};

const res4 = {
  orderBaseInfo: {
    orderId: 26094177511,
    uId: 'M2331903879',
    channelType: '17671',
    orderDate: 1693638936000,
    orderLocale: 'zh_cn',
    orderStatus: 4,
    orderStatusDesc: '已取消',
    orderStatusName: '已取消',
    orderStatusCtrip: 'CAR_CANCELLED',
    allStatuses: [],
    allOperations: [
      { operationId: 4, buttonName: '再次预订', enable: true },
      { operationId: 3, enable: false, code: 4 },
    ],
    orderTip: {
      tipType: 5,
      tipContent: '您的订单已取消，感谢您使用携程租车',
      tipContentArray: [
        '您的订单已取消，感谢您使用携程租车',
        '请注意行车安全，如遇事故请及时联系携程和租车公司',
      ],
      warnType: 0,
    },
    payMode: 2,
    isEasyLife: false,
    extOperation: [],
  },
  baseResponse: {
    isSuccess: true,
    code: 'unknown',
    returnMsg: 'success',
    requestId: '',
    cost: 369,
  },
};
describe('境外订详', () => {
  test(
    createInterTestName({
      testId: [
        3147922, 458379, 40806, 458380, 458403, 408069, 458402, 458540, 458537,
        458536, 458535, 458562, 458564, 458569, 458565, 458568, 458574, 458576,
        458529, 458528, 458538, 458539, 458567, 458572, 458573, 458570, 458571,
        458473, 4067589, 4067617,
      ],
      name: '境外订详 - 正常取消',
    }),

    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      const callApi = jest.spyOn(Call, 'makeCall');
      const openURL = jest.spyOn(URL, 'openURL');
      const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

      const Page = await renderPage(state);
      await waitRender(async () => {
        // 458380
        expect(Page.container).toHaveTextContent('订单号 12345678');
        // 458538
        // expect(Page.container).toHaveTextContent('还车(当地时间)');
        // expect(Page.container).toHaveTextContent('取车(当地时间)');
        // expect(Page.container).toHaveTextContent('10月4日 周三 11:00');
        // expect(Page.container).toHaveTextContent('10月5日 周四 11:00');
        // 458402
        // expect(Page.container).toHaveTextContent(
        //   '很抱歉，由于超时未支付，您的订单已取消',
        // );
        // // 458537
        // const vehicleDescs = await Page.findAllByTestId(
        //   UITestId.car_testid_comp_vehicle_desc,
        // );
        // expect(vehicleDescs[0]).toHaveTextContent('标准型商务车');
        // expect(vehicleDescs[1]).toHaveTextContent(
        //   '7座4门自动挡A/C2个24寸行李箱',
        // );
        // // 458536
        // const vehicleLabel = await Page.findByText('参考车型');
        // expect(vehicleLabel).toBeTruthy();
        // // 458535
        // const vehicleImage = await findPageImage(
        //   Page,
        //   'https://pages.trip.com/cars/image/totrip/e8cbcb9a-e85b-4293-868e-e5a524f5a612.png?timestamps=20230329',
        // );
        // expect(vehicleImage).toBeTruthy();
        // // 458562
        // const vendorImage = await findPageImage(
        //   Page,
        //   'https://pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png?timestamps=20230329',
        // );
        // expect(vendorImage).toBeTruthy();
        // // 458564
        // const comment = await Page.findByTestId(
        //   UITestId.car_testid_page_product_vendor_comment_osd,
        // );
        // expect(comment).toHaveTextContent('4');
        // expect(comment).toHaveTextContent('5');
        // expect(comment).toHaveTextContent('满意');
        // // 458569
        // const pickReturnContent = await Page.findByTestId(
        //   UITestId.car_osd_pick_return_content,
        // );
        // expect(pickReturnContent).toHaveTextContent('08:00–22:00');
        // expect(pickReturnContent).toHaveTextContent('星期一至星期日');

        // expect(pickReturnContent).toHaveTextContent(
        //   'Jeju International Airport',
        // );
        // // 458568
        // await renderWithAction({
        //   action: async () => {
        //     const callBtn = await Page.findByText('拨打');
        //     fireEvent.press(callBtn);
        //   },
        //   expect: async () => {
        //     const knowBtn = await Page.findByTestId(
        //       `${UITestId.car_testid_page_order_confirm_modal_button}_拨打`,
        //     );
        //     expect(Page.container).toHaveTextContent('82647266460');
        //     expect(knowBtn).toBeOnTheScreen();
        //   },
        // });
        // // 458574
        // await renderWithAction({
        //   action: async () => {
        //     const callBtn = await Page.findByTestId(
        //       `${UITestId.car_testid_page_order_confirm_modal_button}_拨打`,
        //     );
        //     fireEvent.press(callBtn);
        //   },
        //   expect: () => {
        //     expect(callApi).toBeCalledWith('82647266460');
        //   },
        // });
        // // 458529，458528
        // expect(Page.container).toHaveTextContent('起亚 Carnival');

        // await renderWithAction({
        //   action: async () => {
        //     const similarBtn = await Page.findByText('或同组车型');
        //     fireEvent.press(similarBtn);
        //   },
        //   expect: () => {
        //     expect(pagePushFunc).toBeCalled();
        //   },
        // });
        // await renderWithAction({
        //   action: async () => {
        //     const callBtn = await Page.findByTestId(
        //       `car_osd_pick_return_item_门店地址`,
        //     );
        //     fireEvent.press(callBtn);
        //   },
        //   expect: () => {
        //     expect(pagePushFunc).toBeCalled();
        //   },
        // });
        // await renderWithAction({
        //   action: async () => {
        //     const callBtn = await Page.findByText(`还车`);
        //     fireEvent.press(callBtn);
        //   },
        //   expect: () => {
        //     expect(pickReturnContent).toHaveTextContent('07:00–22:00');
        //     expect(pickReturnContent).toHaveTextContent('星期一至星期六');
        //     expect(pickReturnContent).toHaveTextContent('还车地址 aaaa');
        //   },
        // });

        // // expect(Page.toJSON()).toMatchSnapshot();
        // await renderWithAction({
        //   action: async () => {
        //     const callBtn = await Page.findByText(`查看详情`);
        //     fireEvent.press(callBtn);
        //   },
        //   expect: () => {
        //     expect(openURL).toBeCalledWith(
        //       '/rn_car_osd/_crn_config?CRNModuleName=rn_car_osd&CRNType=1&initialPage=OsdRentalCarMaterial&CRNType=1&orderid=12345678',
        //     );
        //   },
        // });
      });
    },
  );

  test(
    createInterTestName({
      testId: [458403, 458404, 458563, 4067603, 4068394],
      name: '境外订详 - 取消有退款',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockResolvedValue(Promise.resolve(res1));
      const Page = await renderPage(state);
      // 458403您的订单已免费取消。款项将于1-5个工作天內退还至原付款账户，实际时间受您的银行影响。458404 已取消订单 预期：您的订单已免费取消。（到店付订单）
      expect(Page.container).toHaveTextContent('您的订单已免费取消');
      await waitFor(() => {
        expect(Page.container).toHaveTextContent(
          '您的订单已免费取消。款项将于1-5个工作天內退还至原付款账户，实际时间受您的银行影响。',
        );
      });
      // 458563
      const comment = await Page.findByTestId(
        UITestId.car_testid_page_product_vendor_comment_osd,
      );
      expect(comment).toHaveTextContent('暂无评分');
    },
  );
  test(
    createInterTestName({
      testId: [458401, 458395, 458397, 458406, 458405, 458398],
      name: '境外订详 - 供应商无库存取消',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockResolvedValue(Promise.resolve(res2));
      const Page = await renderPage(state);
      // 458401 很抱歉，由于价格/库存变动，您的订单已取消
      await waitFor(() => {
        expect(Page.container).toHaveTextContent(
          '很抱歉，由于价格/库存变动，您的订单已取消',
        );
      });
    },
  );
  test(
    createInterTestName({
      testId: [458400, 458402, 458396],
      name: '境外订详 - 取消退优惠券',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockResolvedValue(Promise.resolve(res4));
      const Page = await renderPage(state);
      await waitFor(
        () => {
          expect(Page.container).toHaveTextContent(
            '您的订单已取消，感谢您使用携程租车',
          );
        },
        {
          timeout: 2000,
        },
      );
    },
  );
  test(
    createInterTestName({
      testId: [4670716, 4670723],
      name: '境外订详 - DID门店消息',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockResolvedValue(Promise.resolve(res4));
      const Page = await renderPage(state);
      await waitFor(
        () => {
          expect(Page.container).toHaveTextContent('门店消息1条车损');
        },
        {
          timeout: 2000,
        },
      );
      const damageEntry = Page.getByText('门店消息');
      await fireEvent.press(damageEntry);
      const openURL = jest.spyOn(URL, 'openURL');
      expect(openURL).toBeCalledWith(
        '/rn_xtaro_car_osd/main.js?CRNType=1&CRNModuleName=rn_xtaro_car_osd&initialPage=storeMessages&orderId=12345678&orderStatus=4&title=%E9%97%A8%E5%BA%97%E6%B6%88%E6%81%AF&pageType=storeMessage',
      );
    },
  );
});
