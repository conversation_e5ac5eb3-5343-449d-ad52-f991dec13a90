import { Utils, User, AppContext, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import OrderDetail from '../../src/pages/xcar/Containers/OrderDetailContainer';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { renderWithAction, waitRender } from '../testHelpers';
// @ts-ignore 禁止Redux Log
__DEV__ = false;

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));
const resQueryCarAssistant408244 = require('../../__mocks__/restful/18862/queryCarAssistantV2/408244.json');
const resQueryCarAssistant407960 = require('../../__mocks__/restful/18862/queryCarAssistantV2/407960.json');
const resQueryCarAssistant407961 = require('../../__mocks__/restful/18862/queryCarAssistantV2/407961.json');

// 页面初始化
const renderPage = async (initialState = {}) => {
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
  jest.spyOn(User, 'isLoginSync').mockReturnValue(true);
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'OrderDetail',
  });

  return createPage(OrderDetail, {}, initialState);
};

const renderInstructionsPage = (initialState = undefined) => {
  const InstructionsPage =
    require('../../src/pages/xcar/Containers/InstructionsContainer').default;
  return createPage(InstructionsPage, {}, initialState);
};
const renderMessageAssistantPage = (initialState = undefined) => {
  const page =
    require('../../src/pages/xcar/Containers/MessageAssistantContainer').default;
  return createPage(page, {}, initialState);
};

jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));

// TODO-@zyr 履约卡片版本下 先注释
// test(
//   createInterTestName({
//     testId: [
//       407955, 407962, 407964, 407968, 407970, 407967, 407959, 407965, 407963,
//       407966,
//     ],
//     name: '当前消息场景数量<=3',
//   }),
//   async () => {
//     const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
//     const state = {
//       OrderDetail: {
//         reqOrderParams: {
//           orderId: 888888,
//         },
//         storageCardsTitle: [],
//       },
//     };
//     const Page = await renderPage(state);
//     const ToastFunc = jest.spyOn(Toast, 'show');
//     // 407955 当前消息场景数量<=3 预期：1、展示所有场景卡片 2、点击卡片，跳转对应得二级页面
//     const tipsCardDom = await Page.findAllByTestId(
//       /car_testid_comp_orderDetail_message_card_TipsCardDom/,
//     );
//     expect(tipsCardDom).toBeTruthy();
//     // 407968 提车号展示
//     expect(Page.container).toHaveTextContent('提车号36510621513');
//     // 407970 金额模块展示
//     expect(Page.container).toHaveTextContent('在线支付 ¥73');
//     // 407967 订单号复制
//     const orderIdCopyBtn = await Page.findAllByTestId(
//       UITestID.car_testid_order_detail_orderIdCopyBtn,
//     );
//     await act(() => {
//       fireEvent.press(orderIdCopyBtn[0]);
//     });
//     waitFor(() => {
//       expect(ToastFunc).toBeCalledWith('已复制 订单号');
//     });

//     const serviceCardDom = await Page.findAllByTestId(
//       /car_testid_comp_orderDetail_message_card_ServiceCardDom/,
//     );
//     expect(serviceCardDom).toBeTruthy();
//     fireEvent(serviceCardDom[0], 'click');

//     const ServiceProgressModal = await Page.findAllByTestId(
//       UITestID.car_testid_comp_order_ServiceProgressModal,
//     );
//     expect(ServiceProgressModal).toBeTruthy();
//     fireEvent(tipsCardDom[0], 'click');
//     expect(pagePushFunc).toHaveBeenNthCalledWith(1, 'Instructions', {});
//     // 407962 407959 407963 407966 订单状态“已确认\取消中\已取消\处理中修改中” 预期：点击卡片，进入“用车助手”-取车tab 都是根据接口返回定位tab锚点
//     const InstructionsPage = await renderInstructionsPage();
//     // await waitFor(async () => {
//     const detailTab = await InstructionsPage.findAllByTestId(
//       'car_testid_comp_vendor_modal_tab_取车',
//     );
//     expect(detailTab[0].findAllByType(View)[1]).toHaveProp(
//       'elementName',
//       'LinearGradient',
//     );
//     // });
//     // 407964 用车小贴士二级页”用车指南“ 预期：tab展示: 取车 用车中 还车
//     expect(InstructionsPage.container).toHaveTextContent('取车');
//     expect(InstructionsPage.container).toHaveTextContent('用车中');
//     expect(InstructionsPage.container).toHaveTextContent('还车');
//     // 407965
//     expect(InstructionsPage.container).toHaveTextContent(
//       '取车必备材料身份证原件有效期3个月以上中国大陆驾驶证原件 或 “交管12123”APP发放的电子驾驶证有效期1个月以上均需驾驶员测试人朱杰本人证件取车流程店员收费送车上门静安寺-地铁站地图及指引店员核验证件取车前，店员需要对您的身份证件及驾驶证件进行拍照核验，确认有效并为本人持有。核验通过才可取车。在线认证驾驶员，取车免核验继续认证店员检验车辆拍照记录仪表盘油量及里程，检验车身外部、座椅、玻璃、轮胎是否有划痕或凹陷。如有请拍照记录，并要求店员在验车单上记录签署合同合同签署后，取车完成驾驶注意事项城市限行政策上海工作日外牌车辆部分道路分时段限行详情里程限制租期内里程数限制信息，请以门店告知为准。发生事故怎么办事故处理流程发生事故时，请及时联系门店及交通警察查看详情还车准备确认油量与取车时一致还车时需保持与取车时油量一致若还车油量少于取车油量，门店将收取油费。若还车油量多于取车油量，门店将返还多余油费。油费=格数差/总格数*油箱容量*油价若提前或延迟还车，请及时与门店联系相关收费标准可查看门店政策门店政策订单支持在线续租，取车后可操作还车流程店员收费上门取车静安寺-地铁站地图及指引店员检验车辆车辆停靠指定位置后，等待工作人员检验车辆签署还车验车单若对车辆核验结果和费用结算无异议，您需在还车验车单上签字，完成还车',
//     );
//   },
//   TEST_TIMEOUT,
// );

// test(
//   createInterTestName({
//     testId: [407956, 408243, 408245, 407957],
//     name: '当前消息场景数量 > 3',
//   }),
//   async () => {
//     const state = {
//       OrderDetail: {
//         reqOrderParams: {
//           orderId: 888888,
//         },
//         storageCardsTitle: [],
//       },
//     };
//     const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
//     const Page = await renderPage(state);
//     await waitRender(() => {
//       const doms = Page.getAllByTestId(
//         /car_testid_comp_orderDetail_message_card_.*/,
//       );
//       // 407956 预期：最多展示三张卡片
//       expect(doms.length).toEqual(3);
//       expect(Page.container).toHaveTextContent('2条消息');

//       // 407957 预期：第三张文案展示“xx条消息”，点击进入消息助手页
//       fireEvent.press(doms[2]);
//       expect(pagePushFunc).toHaveBeenNthCalledWith(2, 'OnlineAuth');
//     });

//     const messageAssistantPage = await renderMessageAssistantPage();
//     // 408243 属于当前消息 预期：展示在标题为“当前消息”下
//     expect(messageAssistantPage.container).toHaveTextContent(
//       '当前消息（4）限行政策 里程限制与禁行 车辆事故处理 门店政策 已提交¥30违约金退还申请该款项将原路退回，您可在退款进度中查看已收到您的咨询09-12 10:00预计最晚9月24日 10:00处理完成并给您回复用车小贴士取车必备驾驶员本人身份证+中国大陆驾照了解取还车流程',
//     );
//     const pagePopFunc = jest.spyOn(CRNPage.prototype, 'pop');
//     // 408245 点击悬浮按钮【订单详情】
//     await renderWithAction({
//       action: async () => {
//         const btn = await messageAssistantPage.findByText('订单详情');
//         fireEvent.press(btn);
//       },
//       expect: () => {
//         expect(pagePopFunc).toBeCalled();
//       },
//     });
//   },
// );

// test(
//   createInterTestName({
//     testId: [407956, 408244],
//     name: '历史消息',
//   }),
//   async () => {
//     const state = {
//       OrderDetail: {
//         reqOrderParams: {
//           orderId: 888888,
//         },
//         storageCardsTitle: [],
//       },
//     };
//     jest
//       .spyOn(CarFetch, 'queryCarAssistantV2')
//       .mockImplementation(() => Promise.resolve(resQueryCarAssistant408244));
//     const Page = await renderPage(state);

//     await renderWithAction({
//       action: async () => {
//         const btn = await Page.findByText('查看消息助手');
//         fireEvent.press(btn);
//       },
//       expect: () => {},
//     });

//     const messageAssistantPage = await renderMessageAssistantPage();
//     // 408244 属于历史消息 预期：展示在标题为“历史消息”下
//     expect(messageAssistantPage.container).toHaveTextContent(
//       '历史消息（1）用车小贴士取车必备驾驶员本人身份证+中国大陆驾照了解取还车流程订单详情申请退违约金订单违约金共¥40，您申请退还¥30咨询记录咨询时间: 2021-04-08 19:20:03催处理已收到您的咨询，预计最晚10月28日14已收到您的咨询，预计最晚10月28日14:16:00处理完成',
//     );
//   },
// );

test(
  createInterTestName({
    testId: [407955, 407960],
    name: '用车中-用车助手tab定位',
  }),
  async () => {
    const state = {
      OrderDetail: {
        reqOrderParams: {
          orderId: 888888,
        },
        storageCardsTitle: [],
      },
    };
    jest
      .spyOn(CarFetch, 'queryCarAssistantV2')
      .mockImplementation(() => Promise.resolve(resQueryCarAssistant407960));
    const Page = await renderPage(state);
    const InstructionsPage = await renderInstructionsPage();

    await waitRender(async () => {
      const tabInUse = await InstructionsPage.findByTestId(
        'car_testid_instructions_tab_InUse',
      );
      expect(tabInUse).toBeTruthy();
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [407955, 407961],
    name: '已完成-用车助手tab定位',
  }),
  async () => {
    const state = {
      OrderDetail: {
        reqOrderParams: {
          orderId: 888888,
        },
        storageCardsTitle: [],
      },
    };
    jest
      .spyOn(CarFetch, 'queryCarAssistantV2')
      .mockImplementation(() => Promise.resolve(resQueryCarAssistant407961));
    const Page = await renderPage(state);
    const InstructionsPage = await renderInstructionsPage();

    await waitRender(async () => {
      const tabInUse = await InstructionsPage.findByTestId(
        'car_testid_instructions_tab_DropOff',
      );
      expect(tabInUse).toBeTruthy();
    });
  },
  TEST_TIMEOUT,
);
