import { waitFor, fireEvent } from '@testing-library/react-native';
import URL from '@c2x/apis/URL';
import { Utils, User, AppContext } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';

import { findPageImage, renderWithAction } from '../testHelpers';

import Booking from '../../src/pages/xcar/Containers/BookingContainer';

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};
const selfServiceInstruction =
  'https://docs.c-ctrip.com/files/6/carpic_no_mark/1tg6112000dgszjhy250C.pdf';
test(
  createInterTestName({
    testId: [860469, 860483, 860476, 860504, 860518, 860511],
    name: '填写页-自助取还',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    waitFor(async () => {
      // expect(Page.toJSON()).toMatchSnapshot();
      const selfServiceLabel = await findPageImage(
        Page,
        'https://dimg04.c-ctrip.com/images/1tg4j12000e31mpw01F95.png',
      );
      expect(selfServiceLabel).toBeTruthy();
      expect(Page.container).toHaveTextContent(
        '取还车需预订人现场操作否则需联系门店员工处理',
      );
      const selfServiceText = await Page.findByText(
        '个人身份信息授权声明以及自助取还说明',
      );
      expect(selfServiceText).toBeTruthy();
      await renderWithAction({
        action: async () => {
          const selfServicePdf = await Page.findByText('自助取还说明');
          expect(selfServicePdf).toBeTruthy();
          fireEvent.press(selfServicePdf);
        },
        expect: () => {
          const openURL = jest.spyOn(URL, 'openURL');
          expect(openURL).toBeCalledWith(selfServiceInstruction);
        },
      });
    });
  },
  TEST_TIMEOUT,
);
