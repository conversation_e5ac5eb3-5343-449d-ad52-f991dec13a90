import { TouchableOpacity } from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { fireEvent, act, waitFor } from '@testing-library/react-native';
import { Utils, User } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { waitRender } from '../testHelpers';

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/BookingContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: false,
      isFail: false,
      isPriceLoading: false,
    },
    initialState,
  );
};

test(
  createInterTestName({
    testId: [296438, 296441, 296443, 296440, 296446, 296448, 296445],
    name: '优惠券与活动-已享最大优惠, 展示 活动名称+活动说明入口+优惠金额',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));

    const Page = await renderPage();
    await waitRender(async () => {
      const couponAndDeposit = Page.queryByTestId(
        UITestID.car_testid_comp_booking_NewCouponAndDeposit,
      );
      // 296438
      expect(couponAndDeposit).toHaveTextContent(/已享最大优惠￥/);
      const couponAndDepositTouchable = await couponAndDeposit.findAllByType(
        TouchableOpacity,
      )[0];
      await act(() => {
        fireEvent.press(couponAndDepositTouchable);
      });
      const activity = Page.queryByTestId(
        UITestID.car_testid_comp_booking_NewCouponAndDeposit_activity,
      );
      // 296440
      expect(activity).toBeTruthy();
      // 296441
      expect(activity).toHaveTextContent(/周边游特惠/);
      expect(activity).toHaveTextContent(/¥24/);

      // 296443
      expect(activity).toHaveTextContent('周三租车30元优选满减券-¥30');

      const couponEntry = Page.queryByTestId(
        UITestID.car_testid_comp_booking_NewCouponAndDeposit_coupon,
      );
      await act(() => {
        fireEvent.press(couponEntry);
      });
      const couponModal = Page.queryByTestId(
        UITestID.car_testid_comp_booking_couponModal,
      );
      // 296446
      expect(couponModal).toBeTruthy();

      // 296448
      expect(couponModal).toHaveTextContent(/周三租车30元优选满减券/);

      // 296445
      const couponModalUsableItem = Page.queryByTestId(
        UITestID.car_testid_comp_booking_couponModal_usable_item,
      );
      await act(() => {
        fireEvent.press(couponModalUsableItem);
      });
      waitFor(() => {
        expect(couponEntry).toHaveTextContent('1张可用');
      });
    });
    // TODO-lxj 296449、296447、296439
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [296444],
    name: '展示打底文案：暂无可用优惠券',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));

    const Page = await renderPage();
    await waitRender(() => {
      expect(Page.container).toHaveTextContent('暂无可享优惠');
    });
  },
);
