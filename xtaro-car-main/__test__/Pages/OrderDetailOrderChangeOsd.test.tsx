import { fireEvent } from '@testing-library/react-native';
import { TouchableOpacity } from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import CRNPage from '@c2x/components/Page';
import { AppContext, Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { renderWithAction } from '../testHelpers';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderChangeContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  jest
    .spyOn(CarStorage, 'load')
    .mockImplementation(() => Promise.resolve('[]'));
  jest
    .spyOn(CarStorage, 'loadAsync')
    .mockImplementation(() => Promise.resolve('[]'));
  return createPage(container, {}, initialState);
};
const res = {
  orderBaseInfo: {
    orderId: 26094177511,
    uId: 'M2331903879',
    channelType: '17671',
    orderDate: 1693638936000,
    orderLocale: 'zh_cn',
    orderStatus: 4,
    orderStatusDesc: '已取消',
    orderStatusName: '已取消',
    orderStatusCtrip: 'CAR_CANCELLED',
    allStatuses: [],
    allOperations: [
      { operationId: 4, buttonName: '再次预订', enable: true },
      { operationId: 3, enable: false, code: 4 },
    ],
    orderTip: {
      tipType: 5,
      tipContent: '很抱歉，由于超时未支付，您的订单已取消',
      tipContentArray: ['很抱歉，由于超时未支付，您的订单已取消'],
      warnType: 0,
    },
    payMode: 2,
    isEasyLife: false,
    extOperation: [],
  },
  pickupStore: {
    localDateTime: '2023-10-04 11:00:00',
    storeName: 'Jeju International Airport',
    storeCode: 'CJUCT329404',
    storeAddress: '735-22 Yongdam 2 Dong  Jeju Do,Jeju,63164,0,',
    longitude: 126.50222,
    latitude: 33.50417,
    storeGuide:
      '到达后，请致电联系门店，确认取车位置。Tips：为了还车更便捷，取车时请提前向店员咨询如何还车!',
    storeLocation: '机场外',
    storeWay: '',
    storeTel: '82647266460',
    storeOpenTimeDesc: '{"星期一至星期日":"08:00–22:00"}',
    cityName: '济州市',
    provinceName: '济州特别自治道',
    countryName: '韩国',
    userSearchLocation: '济州国际机场 (CJU)',
    mapUrl: '',
    storeID: 418321,
    location: {
      locationType: 1,
      locationName: '济州国际机场 (CJU)',
      locationCode: 'CJU',
      continent: { id: 1, name: '韩国' },
      country: { id: 42, name: '韩国' },
      province: { id: 11176, name: '济州特别自治道' },
      city: { id: 737, name: '济州市' },
      poiInfo: {
        latitude: 33.510413999999997258782968856394290924072265625,
        longitude: 126.4913530000000037034624256193637847900390625,
        type: 1,
      },
    },
    disclaimer: 'The above information is provided by the branch',
  },
  returnStore: {
    localDateTime: '2023-10-05 11:00:00',
    storeName: 'Jeju International Airport',
    storeCode: 'CJUCT329404',
    storeAddress: '还车地址 aaaa',
    longitude: 126.50222,
    latitude: 33.50417,
    storeGuide:
      '请提前致电联系门店，确认还车位置。通常在停车场附近的道路旁会有“Car Return”的指示牌，可按照指示牌前往。',
    storeLocation: '机场外',
    storeWay: '',
    storeTel: '82647266460',
    storeOpenTimeDesc: '{"星期一至星期六":"07:00–22:00"}',
    cityName: '济州市',
    provinceName: '济州特别自治道',
    countryName: '韩国',
    userSearchLocation: '济州国际机场 (CJU)',
    mapUrl: '',
    storeID: 418321,
    location: {
      locationType: 1,
      locationName: '济州国际机场 (CJU)',
      locationCode: 'CJU',
      continent: { id: 1, name: '韩国' },
      country: { id: 42, name: '韩国' },
      province: { id: 11176, name: '济州特别自治道' },
      city: { id: 737, name: '济州市' },
      poiInfo: {
        latitude: 33.510413999999997258782968856394290924072265625,
        longitude: 126.4913530000000037034624256193637847900390625,
        type: 1,
      },
    },
    disclaimer: 'The above information is provided by the branch',
  },
  driverInfo: {
    name: '5811a194e341c48f47f21d40bc11dc4c894fa051c1b113c94ea90a60ba1adc3a',
    age: '31',
    email: '18a4a08bb346e481f8ecc40f61719c21d673a6a9ebb4568db8692809c8f6c22a',
    telphone:
      '38d3191e2a394e04cf415e94bfcf0eaae62be5d741c6169c4e7310683e25fbcb',
    areaCode: '86',
    decryptTelphone: '13564504574',
    decryptMail: '<EMAIL>',
    lastName:
      'b804f21aa596239ef6705813ecb0a31f8b642f154c8571bae62aa263e9bf1d69',
    firstName:
      'c91ce47863e905e5bae68976e46b49edd2c01188e80442724e21c296e0630102',
  },
  cancelRuleInfo: {
    isTotalLoss: false,
    isFreeCancel: false,
    hours: 48,
    cancelDescription:
      '取车（当地时间）前48小时以上可免费取消；取车（当地时间）前48小时内取消将收取€ 95.00（约¥756.00）作为违约金；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款',
    cancelReasons: [
      '行程变更/取消',
      '修改订单',
      '重复下单',
      '车不能跨境跨岛',
      '信用卡问题',
      '驾照证件问题',
      '其他网站更便宜',
      '其他',
    ],
    modifyTip: {
      title: 'Booking cannot be modified',
      desc: 'If you need to make changes, cancel it and rebook',
    },
  },
  baseResponse: {
    isSuccess: true,
    code: 'unknown',
    returnMsg: 'success',
    requestId: '',
    cost: 369,
  },
};

describe('境外订详 - 修改订单', () => {
  test(
    createInterTestName({
      testId: [458456, 458453, 458452, 458451, 458449, 458450],
      name: '境外订详 - 修改订单',
    }),
    async () => {
      const state = {
        OrderDetail: {
          ...res,
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      const modifyOrder = jest
        .spyOn(CarFetch, 'modifyOrder')
        .mockResolvedValue(Promise.resolve({}));
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockResolvedValue(Promise.resolve(res));
      const pagePopFunc = jest.spyOn(CRNPage.prototype, 'pop');
      const Page = await renderPage(state);
      expect(Page.container).toHaveTextContent('修改订单');
      expect(Page.container).toHaveTextContent(
        '以下信息暂不支持直接修改，您可取消订单后重新预订',
      );
      expect(Page.container).toHaveTextContent('取车地点济州国际机场 (CJU)');
      expect(Page.container).toHaveTextContent('取车时间2023-10-04 11:00:00');
      expect(Page.container).toHaveTextContent('还车地点济州国际机场 (CJU)');
      expect(Page.container).toHaveTextContent('还车时间2023-10-05 11:00:00');
      expect(Page.container).toHaveTextContent(
        '取消取车（当地时间）前48小时以上可免费取消；取车（当地时间）前48小时内取消将收取€ 95.00（约¥756.00）作为违约金；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款',
      );
      await renderWithAction({
        action: async () => {
          const bootTouchable = await Page.container.findAllByType(
            TouchableOpacity,
          );
          fireEvent(bootTouchable[0], 'onPress');
        },
        expect: () => {
          expect(pagePopFunc).toBeCalled();
        },
      });
      await renderWithAction({
        action: async () => {
          const btn = await Page.findAllByTestId('order_change_cancel');
          fireEvent(btn[0], 'onPress');
        },
        expect: () => {
          expect(Page.container).toHaveTextContent('取消订单');
        },
      });
      await renderWithAction({
        action: async () => {
          const btn = await Page.findAllByTestId('order_change_confirm');
          fireEvent(btn[0], 'onPress');
        },
        expect: () => {
          expect(modifyOrder).toBeCalled();
        },
      });
    },
  );
});
