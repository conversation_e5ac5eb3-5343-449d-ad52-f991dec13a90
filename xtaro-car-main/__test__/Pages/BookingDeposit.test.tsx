import { fireEvent, waitFor, act } from '@testing-library/react-native';
import { Utils, GetAB, User, AppContext } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};

test(
  createInterTestName({
    testId: 296451,
    name: '填写页-押金方式及说明-产品支持免押-提示文案',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    waitFor(() => {
      const depositTitle = Page.queryByTestId(
        UITestID.car_testid_page_booking_deposit_title,
      );
      expect(depositTitle).toHaveTextContent('您信用良好，可免押金');
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: 296452,
    name: '填写页-押金方式及说明-产品支付免押-勾选放弃',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    waitFor(() => {
      const giveUpCheckBox = Page.queryByTestId(
        UITestID.car_testid_page_booking_deposit_check_give_up,
      );
      fireEvent.press(giveUpCheckBox);
      const depositTitle = Page.queryByTestId(
        UITestID.car_testid_page_booking_deposit_title,
      );
      const depositSubTitle = Page.queryByTestId(
        UITestID.car_testid_page_booking_deposit_subtitle,
      );
      expect(depositTitle).toHaveTextContent('您已选择暂不免押');
      expect(depositSubTitle).toHaveTextContent(
        '下单后你可在订单页重新免押，或取车时支付押金',
      );
    });
  },
  TEST_TIMEOUT,
);
