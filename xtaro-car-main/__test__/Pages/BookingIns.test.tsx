import { fireEvent, within, act } from '@testing-library/react-native';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';
import { AppContext, Utils, User } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { waitRender } from '../testHelpers';
// @ts-ignore 禁止Redux Log
__DEV__ = false;

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

// 页面初始化
const renderPage = async (initialState = {}) => {
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};

test(
  createInterTestName({
    testId: [
      296453, 296455, 296457, 296458, 296459, 296456, 296461, 296462, 296463,
      296460, 296469, 296471, 296470, 296472, 296468, 296474, 296473, 296475,
      296476, 296478, 296477, 296479, 296404, 296480,
    ],
    name: '填写页保险模块',
  }),
  async () => {
    const Page = await renderPage();
    const PageDom = Page.container;
    await waitRender(async () => {
      // 296404 标题 预期：订单填写，右侧展示【门店政策】
      const [headerRightText] = await within(PageDom).getAllByText('门店政策');
      const policyBtn = headerRightText.parent;
      // @ts-ignore
      expect(policyBtn).toHaveStyle('right', 17.28);
      // 296480 发票文案展示: 如需发票，请在还车时联系门店索取
      expect(PageDom).toHaveTextContent('如需发票，请在还车时联系门店索取');
      // 296453 车行险模块标题
      const insTitle = await Page.queryByTestId(
        UITestID.car_testid_comp_booking_ins_title,
      );
      expect(insTitle).toHaveTextContent('车行保障服务');
      const insContent = await Page.queryByTestId(
        UITestID.car_testid_comp_booking_ins_content,
      );
      // 296455 服务内容；车损保障、三者保障；免折旧费；免停运费；无须垫付
      expect(insContent).toHaveTextContent(
        '服务内容车损保障三者保障免折旧费免停运费无需垫付',
      );
      const [base, better, best] = await Page.findAllByTestId(
        UITestID.car_testid_comp_booking_ins_content_item,
      );
      // 296457 基础服务展示  1500元以内自付(车轮损失自付)
      expect(base).toHaveTextContent('1500元以内自付(车轮损失自付)');
      // 296458 优享服务展示 全额赔付(车轮损失自付)
      expect(better).toHaveTextContent('全额赔付(车轮损失自付)');

      // 296459 尊享服务展示 全额赔付
      expect(best).toHaveTextContent('全额赔付');
      // 296456 基础服务，优享服务，尊享服务
      expect(insContent).toHaveTextContent('基础服务');
      expect(insContent).toHaveTextContent('优享服务');
      expect(insContent).toHaveTextContent('尊享服务');
      // 296461 基础服务展示 车损5000元以下免收
      expect(base).toHaveTextContent('车损5000元以下免收');
      // 296462 优享服务展示 车损5000元以下免收
      expect(better).toHaveTextContent('车损5000元以下免收');
      // 296463 车损3万元以下免收
      expect(best).toHaveTextContent('车损3万元以下免收');
      //  296460 基础，优享，尊享服务 都展示 “100万”
      expect(base).toHaveTextContent('100万');
      expect(better).toHaveTextContent('100万');
      expect(best).toHaveTextContent('100万');
      // 296469 尊享服务免，展示"√"
      expect(base).toHaveTextContent('已含');
      // 296471 优享服务下方展示”城市地区多剐蹭，建议您升级服务“
      expect(better).toHaveTextContent('城市地区多剐蹭，建议您升级服务');

      // 296470 点击优享”选择“ 预期：优享服务下方展示”已选择“
      const [baseChooseBtn, betterChooseBtn] = await Page.findAllByTestId(
        UITestID.car_testid_comp_booking_ins_content_item_btn,
      );
      await act(async () => {
        fireEvent.press(betterChooseBtn);
      });
      await waitRender(async () => {
        expect(better).toHaveTextContent('已选择');
        // 296472 已选择优享 预期：优享服务下方展示”经济实用 超值之选“
        expect(better).toHaveTextContent('经济实用 超值之选');
        await act(() => {
          fireEvent.press(baseChooseBtn);
        });
      });
      const [baseContentBtn] = await Page.findAllByTestId(
        UITestID.car_testid_comp_booking_ins_content_btn,
      );
      // 296468 点击基础 详情箭头 预期：跳转服务二级页面
      await act(() => {
        fireEvent.press(baseContentBtn);
      });
      await waitRender(async () => {
        // 296474 标题展示 预期：1.页面标题”车行保障服务详情“ 2.tab展示基础服务、优享服务，尊享服务
        expect(PageDom).toHaveTextContent('车行保障服务详情');
        const [tabs] = await Page.findAllByTestId(
          UITestID.car_testid_comp_booking_ins_detail_tab,
        );
        expect(tabs).toHaveTextContent('基础服务优享服务尊享服务');
        // 296473,296475 点击表格基础服务”详情" 预期：跳转弹出二级页面，tab默认展示“基础服务”;基础服务默认展示“当前已含‘
        expect(PageDom).toHaveTextContent('当前已含');
        // 296476 进入二级页面，点击”优享服务“ 预期：tab切换；右下角展示”选择此服务“
        const betterTab = within(tabs).getByText('优享服务');
        await act(() => {
          fireEvent.press(betterTab);
        });
        expect(PageDom).toHaveTextContent('选择此服务');
        // 296477 进入二级页面-”优享服务“，点击右下角【选择此服务】 预期：关闭二级页面，表格切换至优享服务
        const betterTabBtn = within(PageDom).getByText('选择此服务');
        await act(() => {
          fireEvent.press(betterTabBtn);
        });
        await waitRender(() => {
          expect(better).toHaveTextContent('已选择');
        });
        // 296479 点击“查看理赔相关要求及须知” 预期：弹出弹框“理赔相关要求及须知”
        const [tipBtn] = await Page.findAllByTestId(
          UITestID.car_testid_comp_booking_ins_tip_btn,
        );
        await act(() => {
          fireEvent.press(tipBtn);
        });
        expect(PageDom).toHaveTextContent('理赔相关要求及须知');
      });
      // 296478 套餐表格下方文案展示 预期：文案“上述车损险和三者险仅覆盖保险理赔范围内得损失，理赔范围内见服务详情。若发生事故，请按照要求进行处理；如未按照要求进行理赔导致保险公司拒赔的情况，您需承担全额损失”
      expect(PageDom).toHaveTextContent(
        '上述车损险和三者险仅覆盖保险理赔范围内的损失，理赔范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求进行理赔导致保险公司拒赔的情况，您需承担全额损失',
      );
    });
  },
);
