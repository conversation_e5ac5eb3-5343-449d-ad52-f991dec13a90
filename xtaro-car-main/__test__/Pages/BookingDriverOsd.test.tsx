import { fireEvent, waitFor, within, act } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';
import { AppContext, Utils, GetAB, User } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { renderWithAction, waitRender } from '../testHelpers';
// @ts-ignore 禁止Redux Log
__DEV__ = false;

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Util/CarABTesting/GetAB', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/CarABTesting/GetAB'),
}));
// Apptype OSD
jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
jest
  .spyOn(User, 'isLogin')
  .mockImplementation(() => new Promise(resolve => resolve(true)));

// 页面初始化
const renderPage = async (initialState = {}) => {
  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};

const renderDriverListPage = async (initialState = {}) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/DriverListContainer').default;
  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(container, {}, initialState);
};

test(
  createInterTestName({
    testId: [451571, 451572, 451570, 451573, 451567],
    name: '常旅列表页',
  }),
  async () => {
    const BookingPage = await renderPage();
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
    // 451570 跳转"选择驾驶员"的常旅页面
    await renderWithAction({
      action: async () => {
        const addDriverBtn = await BookingPage.findByTestId(
          UITestID.car_testid_book_osd_driver,
        );
        fireEvent.press(addDriverBtn);
      },
      expect: () => {
        // 296422 todo-lxj
        expect(pagePushFunc).toBeCalledWith('DriverEdit', {
          fromurl: 'Booking',
          isAdd: true,
          passenger: undefined,
        });
      },
    });

    const DriverListPage = await renderDriverListPage();
    await waitRender(() => {
      expect(DriverListPage.container).toHaveTextContent(
        '请补充国籍、联系手机',
      );
      expect(DriverListPage.container).toHaveTextContent('新增驾驶员');
    });
    await renderWithAction({
      action: async () => {
        const driverItem = await DriverListPage.findByText('秋大幅 QOIU/DAFU');
        fireEvent.press(driverItem);
      },
      expect: async () => {
        expect(BookingPage.container).toHaveTextContent('QOIU/DAFU');
      },
    });

    await renderWithAction({
      action: async () => {
        const payBtn = await BookingPage.findAllByText('去支付');
        fireEvent.press(payBtn[0]);
      },
      expect: async () => {
        expect(BookingPage.container).toHaveTextContent(
          '请输入正确的驾驶员邮箱',
        );
      },
    });
    // 451567 唤起键盘，可直接编辑手机号
    await renderWithAction({
      action: async () => {
        const input = await BookingPage.container.findByProps({
          placeholder: '联系手机',
        });
        fireEvent.changeText(input, '17500000035');
      },
      expect: async () => {
        const input = await BookingPage.container.findByProps({
          placeholder: '联系手机',
        });
        expect(input.props.value).toEqual('17500000035');
      },
    });
    // 451582
    await renderWithAction({
      action: async () => {
        expect(DriverListPage.container).toHaveTextContent('秋大幅 QOIU/DAFU');
        const driverDeleteItems = await DriverListPage.findAllByText('删除');
        fireEvent.press(driverDeleteItems[0]);
        // 二次点击确认删除
        fireEvent.press(driverDeleteItems[0]);
      },
      expect: async () => {
        expect(DriverListPage.container).not.toHaveTextContent(
          '秋大幅 QOIU/DAFU',
        );
      },
    });
  },
);
