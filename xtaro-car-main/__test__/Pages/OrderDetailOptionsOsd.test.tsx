import { fireEvent } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import URL from '@c2x/apis/URL';
import dayjs from '../../src/pages/xcar/Common/src/Dayjs/src';
import { AppContext, Utils, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { renderWithAction, waitRender } from '../testHelpers';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
    privateLoadSync: jest.fn(() => '[]'),
  },
}));
// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, {}, initialState);
};

const commentURL =
  'https://m.ctrip.com/webapp/cars/osd/osd/osdcomment?oid=12345678&isHideNavBar=YES&isFromCRNWebDetail=false&clienttype=Ctrip_App';
const res1 = {
  orderBaseInfo: {
    orderId: 26094177511,
    uId: 'M2331903879',
    channelType: '17671',
    orderDate: 1693638936000,
    orderLocale: 'zh_cn',
    orderStatus: 4,
    orderStatusDesc: '已完成',
    orderStatusName: '已完成',
    orderStatusCtrip: 'CAR_COMPLETED',
    allStatuses: [],
    allOperations: [
      {
        operationId: 3,
        buttonName: '我要点评',
        url: commentURL,
        enable: true,
      },
      {
        operationId: 4,
        buttonName: '再次预定',
        enable: true,
      },
      {
        operationId: 7,
        buttonName: '修改订单',
        enable: true,
      },
      {
        operationId: 2,
        buttonName: '取消订单',
        enable: true,
      },
      {
        operationId: 1,
        buttonName: '去支付',
        enable: true,
      },
      {
        operationId: 5,
        buttonName: '打印提车凭证',
        enable: true,
      },
    ],
    orderTip: {
      tipType: 5,
      tipContent: '行程已结束，感谢您使用携程租车',
      tipContentArray: ['行程已结束，感谢您使用携程租车'],
      warnType: 0,
    },
    payMode: 2,
    isEasyLife: false,
    extOperation: [],
  },
  baseResponse: {
    isSuccess: true,
    code: 'unknown',
    returnMsg: 'success',
    requestId: '',
    cost: 369,
  },
};
const res2 = {
  orderBaseInfo: {
    orderId: 35072884767,
    uId: '18666011284',
    channelType: '17671',
    orderDate: 1729404182000,
    orderLocale: 'zh-CN',
    orderStatus: 9,
    orderStatusDesc: '用车中',
    orderStatusName: '用车中',
    orderStatusCtrip: 'CAR_IN_SERVICE',
    allStatuses: [],
    allOperations: [
      {
        operationId: 4,
        buttonName: '再次预订',
        enable: true,
      },
      {
        operationId: 3,
        buttonName: '我要点评',
        enable: true,
        code: 3,
        contents: [
          {
            title: '还车后方可点评',
            description: '还车后方可点评',
            type: 1,
          },
        ],
      },
      {
        operationId: 6,
        buttonName: '报销凭证',
        enable: true,
        url: 'https://m.ctrip.com/webapp/carhire/xsd/osdinvoice?id=35072884767&hideHeader=true',
        contents: [
          {
            description:
              '在线支付的费用可以在订单完成后，进入订详页面自助下载相关报销凭证；到店支付的费用，请在门店向供应商索要相关Receipt凭证。',
          },
        ],
      },
    ],
    payMode: 2,
    successSafeRentAuth: false,
    extOperation: [],
  },
  baseResponse: {
    isSuccess: true,
    code: '200',
    returnMsg: 'success',
    requestId: '8bb5a04f-2741-4cf3-bff6-9f7cb73953dd',
    cost: 697,
  },
};
describe('境外订详 - 订单操作', () => {
  test(
    createInterTestName({
      testId: [
        458423, 458422, 458418, 458419, 458421, 458416, 458415, 458410, 458413,
        458414, 458411, 458417, 458412, 3898343, 3898357, 3898364, 3898371, 3898385,
        3898392, 3898399, 3898406, 3898413, 3898420, 3898427, 3898434, 3898378, 3898525
      ],
      name: '境外订详 - 点评露出',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      const openURL = jest.spyOn(URL, 'openURL');
      const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockResolvedValue(Promise.resolve(res1));
      const ctripContinuePayAction = jest
        .spyOn(CarFetch, 'ctripContinuePay')
        .mockResolvedValue(Promise.resolve({}));
      const Page = await renderPage(state);
      await waitRender(() => {
        expect(Page.container).toHaveTextContent('我要点评');
        expect(Page.container).toHaveTextContent('再次预定');
        expect(Page.container).toHaveTextContent('修改订单');
        expect(Page.container).toHaveTextContent('取消订单');
        expect(Page.container).toHaveTextContent('去支付');
        expect(Page.container).toHaveTextContent('打印提车凭证');
      });

      await renderWithAction({
        action: async () => {
          const btn = await Page.findAllByTestId(
            'car_testid_page_order_op_btn_我要点评',
          );
          fireEvent.press(btn[0]);
        },
        expect: async () => {
          expect(openURL).toHaveBeenNthCalledWith(1, commentURL);
        },
      });
      await renderWithAction({
        action: async () => {
          const btn = await Page.findAllByTestId(
            'car_testid_page_order_op_btn_再次预定',
          );
          fireEvent.press(btn[0]);
        },
        expect: async () => {
          const startDate = dayjs().add(7, 'd').startOf('hour').hour(10);
          const pickDateTime = startDate.format('YYYYMMDDHH');
          const returnDateTime = startDate.add(7, 'day').format('YYYYMMDDHH');
          expect(openURL).toHaveBeenNthCalledWith(
            2,
            `/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&st=client&fromurl=common&landingto=Home&apptype=OSD_C_APP&data=%7B%22rentalLocation%22%3A%7B%22pickUp%22%3A%7B%22cid%22%3A347%2C%22cname%22%3A%22%E6%B4%9B%E6%9D%89%E7%9F%B6%22%2C%22country%22%3A%22%E7%BE%8E%E5%9B%BD%22%2C%22realcountry%22%3A%22%E7%BE%8E%E5%9B%BD%22%2C%22isDomestic%22%3Afalse%2C%22area%22%3A%7B%22id%22%3A%22LAX%22%2C%22name%22%3A%22%E6%B4%9B%E6%9D%89%E7%9F%B6%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BA%22%2C%22lat%22%3A33.941589%2C%22lng%22%3A-118.40853%2C%22type%22%3A%221%22%7D%2C%22isFromPosition%22%3Afalse%2C%22isFromDefault%22%3Atrue%7D%2C%22dropOff%22%3A%7B%22cid%22%3A347%2C%22cname%22%3A%22%E6%B4%9B%E6%9D%89%E7%9F%B6%22%2C%22country%22%3A%22%E7%BE%8E%E5%9B%BD%22%2C%22realcountry%22%3A%22%E7%BE%8E%E5%9B%BD%22%2C%22isDomestic%22%3Afalse%2C%22area%22%3A%7B%22id%22%3A%22LAX%22%2C%22name%22%3A%22%E6%B4%9B%E6%9D%89%E7%9F%B6%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BA%22%2C%22lat%22%3A33.941589%2C%22lng%22%3A-118.40853%2C%22type%22%3A%221%22%7D%2C%22isFromPosition%22%3Afalse%2C%22isFromDefault%22%3Atrue%7D%2C%22isShowDropOff%22%3Afalse%7D%2C%22rentalDate%22%3A%7B%22pickUp%22%3A%7B%22dateTime%22%3A%22${pickDateTime}0000%22%7D%2C%22dropOff%22%3A%7B%22dateTime%22%3A%22${returnDateTime}0000%22%7D%7D%7D`,
          );
        },
      });
      await renderWithAction({
        action: async () => {
          const btn = await Page.findAllByTestId(
            'car_testid_page_order_op_btn_修改订单',
          );
          fireEvent.press(btn[0]);
        },
        expect: async () => {
          expect(pagePushFunc).toHaveBeenNthCalledWith(1, 'OrderChange', {
            visible: true,
          });
        },
      });

      const appContextPushFunc = jest.fn();
      AppContext.setPageInstance({
        push: appContextPushFunc,
      });

      await renderWithAction({
        action: async () => {
          const btn = await Page.findAllByTestId(
            'car_testid_page_order_op_btn_取消订单',
          );
          fireEvent.press(btn[0]);
        },
        expect: async () => {
          expect(appContextPushFunc).toBeCalledWith('OrderCancel', {
            visible: true,
          });
        },
      });

      await renderWithAction({
        action: async () => {
          const btn = await Page.findAllByTestId(
            'car_testid_page_order_op_btn_打印提车凭证',
          );
          fireEvent.press(btn[0]);
        },
        expect: async () => {
          expect(openURL).toHaveBeenCalledWith(
            '/rn_car_osd/_crn_config?CRNModuleName=rn_car_osd&CRNType=1&initialPage=OsdRentalCarMaterial&orderid=26094177511',
          );
        },
      });
      await renderWithAction({
        action: async () => {
          const btn = await Page.findAllByTestId(
            'car_testid_page_order_continue_pay',
          );
          fireEvent.press(btn[0]);
        },
        expect: async () => {
          expect(ctripContinuePayAction).toBeCalled();
        },
      });
    },
  );
});

describe('境外订详 - 订单操作', () => {
  test(
    createInterTestName({
      testId: [3898490],
      name: '境外订详 - 点评露出',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockResolvedValue(Promise.resolve(res2));
      const Page = await renderPage(state);
      await waitRender(() => {
        // 我要点评 3898490
        expect(Page.container).toHaveTextContent('我要点评');
      });
    },
  );
});
