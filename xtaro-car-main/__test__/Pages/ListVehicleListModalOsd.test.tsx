/* eslint-disable @typescript-eslint/naming-convention */
import { TouchableOpacity, Text } from 'react-native';
import { fireEvent, act, within, waitFor } from '@testing-library/react-native';
import { Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { waitRender, renderWithAction } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';

const res440463 = require('../../__mocks__/restful/18631/queryProducts/OSD/440463.json');
const res440463_201 = require('../../__mocks__/restful/18631/queryProducts/OSD/440463-201.json');
const res440424 = require('../../__mocks__/restful/18631/queryProducts/OSD/440424.json');

afterEach(() => {
  jest.clearAllMocks();
});

// @ts-ignore 禁止Redux Log
__DEV__ = false;
jest.mock('uuid', () => {
  return jest.fn().mockImplementation(() => {
    return '123456789';
  });
});
// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: false,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

test(
  createInterTestName({
    testId: [699028, 699042, 455734],
    name: '报价弹窗-信息展示',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementationOnce(() => Promise.resolve(res440463_201));
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementationOnce(() => Promise.resolve(res440463));
    const Page = await renderPage();
    await waitRender(async () => {
      const vehicleList = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );

      const firstVehicle = vehicleList[4];
      const firstVehicleTouchableOpacity = await firstVehicle.findAllByType(
        TouchableOpacity,
      )[1];

      await act(async () => {
        fireEvent(firstVehicleTouchableOpacity, 'onPress');
      });

      const vendorListModal = await Page.findByTestId(
        UITestID.car_testid_page_list_vendor_list_modal_osd,
      );
      expect(vendorListModal).toBeTruthy();
      const bootDescBtn = await within(vendorListModal).findByText(
        '3个24寸行李箱',
      );
      fireEvent.press(bootDescBtn);
      const bootModal = await Page.findByTestId(
        UITestID.car_testid_vehicleBootModal,
      );
      expect(bootModal).toBeTruthy();
      expect(bootModal).toHaveTextContent(
        '行李箱说明24寸行李箱行李个数计算· 行李大小计算标准为24寸行李箱；· 超过24寸的大行李箱，记为2件；· 一个空座可增加1件24寸行李（豪华车型不适用）',
      );
      // 弹窗关闭
      const closeBtn = await bootModal.findAllByType(Text);
      fireEvent.press(closeBtn[0]);
      await waitFor(() => {
        expect(Page.queryByText('行李箱说明')).toBeFalsy();
      });
      // 列表页报价弹窗打开或同组车型弹窗
      // const similarCarBtn = await within(vendorListModal).findByText(
      //   '或同组车型',
      // );
      // await renderWithAction({
      //   action: async () => {
      //     fireEvent.press(similarCarBtn);
      //   },
      //   expect: async () => {
      //     const similarModal = await Page.findByTestId(
      //       UITestID.car_testid_page_vendorList_carInfoModal,
      //     );
      //     expect(similarModal).toHaveTextContent('什么是同组车型');
      //   },
      // });
      // 455734 报价弹窗
      expect(vendorListModal).toHaveTextContent(
        '大众 捷达 Jetta或同组车型󰝤标准型轿车5座4门自动挡A/C3个24寸行李箱全球连锁直销4.9/5很好 1条评价门店1，机场外1小时内确认免费取消24h营业满油取还不限里程¥328/天全球连锁直销4.8/5很好 1条评价机场内1小时内确认免费取消24h营业满油取还不限里程¥437/天精选特惠全球连锁直销4.8/5很好 1条评价机场内1小时内确认免费取消24h营业满油取还不限里程¥447/天全球连锁直销 暂无评分 门店1，机场外立即确认免费取消24h营业满油取还不限里程¥399/天国际知名代理5.0/5超棒 “服务周到” 3条评价机场外，可搭乘接驳巴士到达70小时内确认限时免费取消送第三者责任险满油取还不限里程¥397/天全球连锁代理4.6/5满意 3条评价门店2，机场外，可搭乘接驳巴士到达70小时内确认限时免费取消送第三者责任险满油取还不限里程¥383/天全球连锁代理4.3/5不错 2条评价门店2，机场外，可搭乘接驳巴士到达70小时内确认限时免费取消送第三者责任险满油取还不限里程¥383/天国际知名代理 暂无评分 机场外，可搭乘接驳巴士到达70小时内确认限时免费取消送第三者责任险满油取还不限里程¥568/天国际知名直销4.8/5很好 2条评价机场外，可搭乘免费巴士到达1小时内确认免费取消24h营业满油取还不限里程¥1377/天全球连锁直销3.6/53条评价机场外，可搭乘免费巴士到达1小时内确认免费取消24h营业满油取还不限里程¥1063/天已展示全部供应商报价',
      );
      // 跳转详情页
      const priceItem = await within(vendorListModal).findByText('¥437');
      await renderWithAction({
        action: async () => {
          fireEvent.press(priceItem);
        },
        expect: async () => {
          // @ts-ignore
          expect(global.pushPageName).toEqual('Product');
        },
      });
    });
  },
  TEST_TIMEOUT,
);
