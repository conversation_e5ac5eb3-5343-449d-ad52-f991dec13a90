import URL from '@c2x/apis/URL';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { Utils, User, AppContext, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';
import UITestID from '../../src/pages/xcar/Constants/UITestID';

const productRes = require('../../__mocks__/restful/18631/queryProductInfo/OSD/457880.json');
const priceRes = require('../../__mocks__/restful/18631/querypriceinfo/OSD/451558.json');

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};

test(
  createInterTestName({
    testId: [451593, 451592],
    name: '境外-填写页-押金说明',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes));
    jest
      .spyOn(CarFetch, 'queryPriceInfo')
      .mockImplementation(() => Promise.resolve(priceRes));
    const Page = await renderPage();

    waitFor(() => {
      const container = Page.findByTestId(UITestID.car_testid_page_booking);
      // 451593 国际信用卡
      expect(container).toHaveTextContent('国际信用卡');
      // 451592 押金说明 押金信息
      expect(container).toHaveTextContent(
        '取车时刷取押金预授权，还车后30-60天内退换',
      );
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [451594, 455607],
    name: '境外-填写页-取消政策',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes));
    jest
      .spyOn(CarFetch, 'queryPriceInfo')
      .mockImplementation(() => Promise.resolve(priceRes));
    const Page = await renderPage();

    waitFor(() => {
      const container = Page.findByTestId(UITestID.car_testid_page_booking);
      // 451594 取消政策
      expect(container).toHaveTextContent(
        '取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；',
      );
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: 451598,
    name: '境外-填写页-报销凭证',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryPriceInfo')
      .mockImplementation(() => Promise.resolve(priceRes));
    const Page = await renderPage();

    waitFor(() => {
      const container = Page.findByTestId(UITestID.car_testid_page_booking);
      // 451598 报销凭证
      expect(container).toHaveTextContent(
        '在线支付的费用，订单完成后将提供电子itinerary作为报销凭证，无国内发票',
      );
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [451595, 455613],
    name: '境外-填写页-预定条款',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryPriceInfo')
      .mockImplementation(() => Promise.resolve(priceRes));
    const Page = await renderPage();
    waitFor(() => {
      const osdAgreementTitle = Page.queryByTestId(
        UITestID.car_testid_page_booking_isd_agreement_title,
      );
      fireEvent.press(osdAgreementTitle);
      const openURL = jest.spyOn(URL, 'openURL');
      expect(openURL).toBeCalledWith(
        'https://m.ctrip.com/webapp/carhire/xsd/osdrentalprovision?fromurl=osdfill&title=%E9%A2%84%E8%AE%A2%E6%9D%A1%E6%AC%BE&hideHeader=true&from_native_page=1',
      );
    });
  },
  TEST_TIMEOUT,
);
