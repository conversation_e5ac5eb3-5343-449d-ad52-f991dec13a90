/* eslint-disable @typescript-eslint/naming-convention */
import Image from '@c2x/components/Image';;
import Toast from '@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src';
import { fireEvent, within, act, waitFor } from '@testing-library/react-native';
import { Utils, CarStorage, User, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { UITestID, ImageUrl } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';
import { renderWithAction } from '../testHelpers';

const receivePromotionData = require('../../__mocks__/restful/18631/receivePromotion/osd.json');
const receivePromotion429856 = require('../../__mocks__/restful/18631/receivePromotion/429856.json');

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));
jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));
// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: false,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

describe('列表页-券包', () => {
  test(
    createInterTestName({
      testId: [440443, 440444, 440445, 440447, 429853, 440452, 440450],
      name: '境外列表页租车券包展示',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(Utils, 'getUniqRequestKeyWithEnv')
        .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
      const toLogin = jest.spyOn(User, 'toLogin');
      const Page = await renderPage();
      const PageDom = Page.container;
      const tipListDom = await Page.findByTestId(
        UITestID.car_testid_page_list_tiplist,
      );
      const tipList = within(tipListDom);
      // 440443 预期：超值特惠
      const receiveBtn = await tipList.findByText('领取');
      const couponTitle = await tipListDom.findAllByType(Image);
      expect(couponTitle[0].props.source.uri).toEqual(ImageUrl.couponTitle);

      // 440444 点击 领取 预期：弹出领券弹窗
      await renderWithAction({
        action: () => {
          fireEvent.press(receiveBtn);
        },
        expect: async () => {
          const CouponModal = await Page.findByTestId(
            UITestID.car_testid_page_vendorList_coupon_modal,
          );
          // 打开券包弹层
          expect(CouponModal).toBeTruthy();
          // 429853 未登录跳登录
          const CouponModalDom = within(
            Page.getByTestId(UITestID.car_testid_page_vendorList_coupon_modal),
          );
          const CouponModalReceiveBtn = await CouponModalDom.findAllByText(
            '领取',
          );
          await act(() => {
            fireEvent.press(CouponModalReceiveBtn[0]);
          });
          expect(toLogin).toBeCalled();
        },
      });

      // 440445 折扣说明 预期：阶梯券展示“最高” 限额券展示“限额” 立减及无限额折扣券不需展示文案
      expect(PageDom).toHaveTextContent('最高');
      expect(PageDom).toHaveTextContent('立减50');

      // 440447
      // 预期：2张平铺
      const couponItem = await tipListDom.findAllByType(Image);
      const couponItemCircle = couponItem.filter(
        image =>
          image.props.source.uri ===
          'https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/coupon_left_half_circle.png',
      );
      expect(couponItemCircle.length).toBe(2);
      // 440452
      jest
        .spyOn(User, 'isLogin')
        .mockImplementation(() => new Promise(resolve => resolve(true)));
      const couponModalFooterBtn = await Page.findByTestId(
        UITestID.car_testid_page_vendorList_coupon_modal_footer_button,
      );
      jest
        .spyOn(CarFetch, 'receivePromotion')
        .mockImplementation(() => Promise.resolve(receivePromotionData));
      await act(() => {
        fireEvent.press(couponModalFooterBtn);
      });
      expect(couponModalFooterBtn).toHaveTextContent('完成');

      // 成功领取后显示查看 440450
      expect(tipListDom).toHaveTextContent('查看');
    },
  );
  test(
    createInterTestName({
      testId: [440453],
      name: '境外列表页没有配置租车券包',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(Utils, 'getUniqRequestKeyWithEnv')
        .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
      const Page = await renderPage();
      const PageDom = Page.container;
      expect(PageDom).not.toHaveTextContent('领取');
    },
  );
  test(
    createInterTestName({
      testId: [440451],
      name: '已登录',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(Utils, 'getUniqRequestKeyWithEnv')
        .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
      jest
        .spyOn(CarFetch, 'receivePromotion')
        .mockImplementationOnce(() => Promise.resolve(receivePromotion429856));
      const Page = await renderPage();
      const PageDom = Page.container;
      jest
        .spyOn(User, 'isLogin')
        .mockImplementation(() => new Promise(resolve => resolve(true)));
      // 预期：领取成功更新按钮状态 领取失败toast提示失败原因（服务端返）
      const tipListDom = await Page.findByTestId(
        UITestID.car_testid_page_list_tiplist,
      );
      const tipList = within(tipListDom);
      const receiveBtn = await tipList.findByText('领取');
      await act(() => {
        fireEvent.press(receiveBtn);
      });
      const CouponModal = await Page.findByTestId(
        UITestID.car_testid_page_vendorList_coupon_modal,
      );
      const CouponModalDom = within(CouponModal);
      const CouponModalReceiveBtn = await CouponModalDom.findAllByText('领取');

      await act(() => {
        fireEvent.press(CouponModalReceiveBtn[0]);
      });
      const ToastFunc = jest.spyOn(Toast, 'show');
      // 领取成功toast提示
      expect(ToastFunc).toBeCalledWith('领取失败');
    },
  );
});
