import { TouchableOpacity } from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { fireEvent, act, waitFor } from '@testing-library/react-native';
import dayjs from '../../src/pages/xcar/Common/src/Dayjs/src';
import { Utils, CarStorage } from '../../src/pages/xcar/Util/Index';
import {
  createPage,
  createInterTestName,
  TEST_TIMEOUT,
  updateStore,
} from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';
import { renderWithAction, waitRender } from '../testHelpers';

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: true,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

test(
  createInterTestName({
    testId: [230508, 230510],
    name: '查询模块弹窗',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();
    await waitRender(async () => {
      const header = await Page.getByTestId(
        UITestID.car_testid_page_list_header,
      );
      const contentTouchable = await header.findAllByType(TouchableOpacity)[0];

      await act(() => {
        fireEvent(contentTouchable, 'onPress');
      });

      const modal = await Page.getByTestId(
        UITestID.car_testid_page_list_search_pannel_modal,
      );
      expect(modal).toBeTruthy();

      const rightTouchable = await modal.findAllByType(TouchableOpacity)[0];

      await act(() => {
        fireEvent(rightTouchable, 'onPress');
      });

      const canceledModal = await Page.queryByTestId(
        UITestID.car_testid_page_list_search_pannel_modal,
      );

      expect(canceledModal).toBeNull();
    });
  },
);

test(
  createInterTestName({
    testId: [230518, 230519, 489202],
    name: '时间显示',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const now = dayjs();
    const currTime = now.set('hour', 10).set('minute', 0).set('second', 0);
    let statePTime = currTime.add(6, 'days');
    let stateRTime = currTime.add(10, 'days');
    if (stateRTime.year() > statePTime.year()) {
      statePTime = statePTime.add(10, 'days');
      stateRTime = statePTime.add(10, 'days');
    }
    const ptimeStr = statePTime.format('M月D日 HH:mm');
    const rtimeStr = stateRTime.format('M月D日 HH:mm');
    const initialState = {
      LocationAndDate: {
        rentalDate: {
          pickUp: {
            dateTime: dayjs(statePTime),
          },
          dropOff: {
            dateTime: dayjs(stateRTime),
          },
        },
      },
    };
    const Page = await renderPage(initialState);
    await waitFor(() => {
      const pTimeText = Page.findByText(ptimeStr);
      expect(pTimeText).toBeTruthy();
      const rTimeText = Page.findByText(rtimeStr);
      expect(rTimeText).toBeTruthy();
    });

    const statePTime2 = currTime.add(6, 'days');
    const stateRTime2 = currTime.add(10, 'days').add(1, 'year');
    const ptimeStr2 = statePTime2.format('YYYY-MM-DD HH:mm');
    const rtimeStr2 = stateRTime2.format('YYYY-MM-DD HH:mm');
    const updateState = {
      LocationAndDate: {
        rentalDate: {
          pickUp: {
            dateTime: dayjs(statePTime2),
          },
          dropOff: {
            dateTime: dayjs(stateRTime2),
          },
        },
      },
    };
    updateStore(updateState);
    await waitFor(() => {
      const pTimeText2 = Page.queryByText(ptimeStr2);
      expect(pTimeText2).toBeTruthy();
      const rTimeText2 = Page.queryByText(rtimeStr2);
      expect(rTimeText2).toBeTruthy();
    });
  },
);

test(
  createInterTestName({
    testId: [230515, 230516, 489201],
    name: '异地按钮展示',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();

    const header = await Page.getByTestId(UITestID.car_testid_page_list_header);
    const contentTouchable = await header.findAllByType(TouchableOpacity)[0];

    await act(() => {
      fireEvent(contentTouchable, 'onPress');
    });

    // await waitRender(async () => {
    //   const switchBtn = await Page.findByTestId(
    //     UITestID.car_testid_page_home_offsite_switch,
    //   );

    //   await act(() => {
    //     fireEvent(switchBtn, 'click');
    //   });

    //   await waitRender(() => {
    //     const dropOff = Page.queryByTestId(
    //       UITestID.car_testid_page_list_search_pannel_locationItem_dropoff,
    //     );
    //     expect(dropOff).toBeTruthy();
    //   });

    //   await act(() => {
    //     fireEvent(switchBtn, 'click');
    //   });

    //   await waitRender(() => {
    //     const dropOff = Page.queryByTestId(
    //       UITestID.car_testid_page_list_search_pannel_locationItem_dropoff,
    //     );
    //     expect(!dropOff).toBeTruthy();
    //   });
    // });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [230511, 230513, 230514, 489204],
    name: '信息修改',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();
    await waitRender(async () => {
      await waitFor(() => {
        const pAddressText = Page.queryAllByText('凤凰国际机场T1航站楼');
        expect(pAddressText[0]).toBeTruthy();
      });
      const currTime = dayjs()
        .set('hour', 10)
        .set('minute', 0)
        .set('second', 0);
      const statePTimeStr = currTime
        .add(1, 'days')
        .format('YYYY-MM-DD HH:mm:ss');
      const stateRTimeStr = currTime
        .add(3, 'days')
        .add(0.5, 'hour')
        .format('YYYY-MM-DD HH:mm:ss');
      const rentalLocation = {
        pickUp: {
          cid: 2,
          cname: '上海',
          country: '中国',
          realcountry: '中国',
          isDomestic: true,
          version: 3,
          area: {
            id: '47650',
            name: '上海虹桥机场',
            ename: '',
            lat: '31.153133',
            lng: '121.429457',
            type: 2,
            typename: '机场',
          },
          sortIndex: 5,
          isFromPosition: true,
        },
        dropOff: {
          cid: 2,
          cname: '上海',
          country: '中国',
          realcountry: '中国',
          isDomestic: true,
          version: 3,
          area: {
            id: '47650',
            name: '上海虹桥机场',
            ename: '',
            lat: '31.153133',
            lng: '121.429457',
            type: 2,
            typename: '机场',
          },
          sortIndex: 5,
          isFromPosition: true,
        },
        isShowDropOff: false,
      };
      const rentalDate = {
        pickUp: {
          dateTime: dayjs(statePTimeStr),
        },
        dropOff: {
          dateTime: dayjs(stateRTimeStr),
        },
      };
      const age = '30~60';
      const adultSelectNum = 2;
      const childSelectNum = 0;

      const initialState = {
        LocationAndDate: {
          rentalDate,
          rentalLocation,
          age,
          adultSelectNum,
          childSelectNum,
        },
      };

      await renderWithAction({
        action: () => {
          updateStore(initialState);
        },
        expect: async () => {
          const pAddressText = await Page.findAllByText('上海虹桥机场');
          expect(pAddressText[0]).toBeTruthy();
        },
      });

      const header = await Page.getByTestId(
        UITestID.car_testid_page_list_header,
      );
      const contentTouchable = await header.findAllByType(TouchableOpacity)[0];

      // 点击头部修改取还车信息
      await act(() => {
        fireEvent(contentTouchable, 'onPress');
      });

      const timeLine = await Page.findByTestId(
        UITestID.car_testid_page_list_search_pannel_timeline,
      );
      const ptimeTouchable = await timeLine.findAllByType(TouchableOpacity)[0];

      // 点击取车时间
      await act(() => {
        fireEvent(ptimeTouchable, 'onPress');
      });

      // @ts-ignore
      expect(global.showCalendar).toBeTruthy();

      const locatoionItem = await Page.getByTestId(
        UITestID.car_testid_page_list_search_pannel_locationItem_pickup,
      );
      const pCityTouchable = await locatoionItem.findAllByType(
        TouchableOpacity,
      )[0];

      // 点击取车城市
      await act(() => {
        fireEvent(pCityTouchable, 'onPress');
      });

      // @ts-ignore
      expect(global.pushPageName).toEqual('Location');
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [230524, 230525, 489208],
    name: '同地逻辑',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();

    const currTime = dayjs().set('hour', 10).set('minute', 0).set('second', 0);
    const statePTimeStr = currTime.add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
    const stateRTimeStr = currTime
      .add(3, 'days')
      .add(0.5, 'hour')
      .format('YYYY-MM-DD HH:mm:ss');
    const rentalLocation = {
      pickUp: {
        cid: 2,
        cname: '上海',
        country: '中国',
        realcountry: '中国',
        isDomestic: true,
        version: 3,
        area: {
          id: '47650',
          name: '浦东国际机场T2航站楼-地下负一层-靠近检票口-国际/港澳台到达',
          ename: '',
          lat: '31.153133',
          lng: '121.429457',
          type: 2,
          typename: '机场',
        },
        sortIndex: 5,
        isFromPosition: true,
      },
      dropOff: {
        cid: 2,
        cname: '上海',
        country: '中国',
        realcountry: '中国',
        isDomestic: true,
        version: 3,
        area: {
          id: '47650',
          name: '浦东国际机场T2航站楼-地下负一层-靠近检票口-国际/港澳台到达',
          ename: '',
          lat: '31.153133',
          lng: '121.429457',
          type: 2,
          typename: '机场',
        },
        sortIndex: 5,
        isFromPosition: true,
      },
      isShowDropOff: false,
    };
    const rentalDate = {
      pickUp: {
        dateTime: dayjs(statePTimeStr),
      },
      dropOff: {
        dateTime: dayjs(stateRTimeStr),
      },
    };
    const age = '30~60';
    const adultSelectNum = 2;
    const childSelectNum = 0;

    const initialState = {
      LocationAndDate: {
        rentalDate,
        rentalLocation,
        age,
        adultSelectNum,
        childSelectNum,
      },
    };
    updateStore(initialState);
    await waitFor(() => {
      const pAddressText = Page.queryByText(
        '浦东国际机场T2航站楼-地下负一层-靠近检票口-国际/港澳台到达',
      );
      expect(pAddressText).toBeTruthy();
      const rAddressText = Page.queryByText(
        '浦东国际机场T2航站楼-地下负一层-靠近检票口-国际/港澳台到达',
      );
      expect(rAddressText.parent.props.numberOfLines).toEqual(1);
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [230521, 230522, 489203],
    name: '异地逻辑',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();

    const currTime = dayjs().set('hour', 10).set('minute', 0).set('second', 0);
    const statePTimeStr = currTime.add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
    const stateRTimeStr = currTime
      .add(3, 'days')
      .add(0.5, 'hour')
      .format('YYYY-MM-DD HH:mm:ss');
    const rentalLocation = {
      pickUp: {
        cid: 2,
        cname: '上海',
        country: '中国',
        realcountry: '中国',
        isDomestic: true,
        version: 3,
        area: {
          id: '47650',
          name: '上海虹桥机场',
          ename: '',
          lat: '31.153133',
          lng: '121.429457',
          type: 2,
          typename: '机场',
        },
        sortIndex: 5,
        isFromPosition: true,
      },
      dropOff: {
        cid: 2,
        cname: '上海',
        country: '中国',
        realcountry: '中国',
        isDomestic: true,
        version: 3,
        area: {
          id: '47650',
          name: '浦东国际机场T2航站楼-地下负一层-靠近检票口-国际/港澳台到达',
          ename: '',
          lat: '31.153133',
          lng: '121.429457',
          type: 2,
          typename: '机场',
        },
        sortIndex: 5,
        isFromPosition: true,
      },
      isShowDropOff: false,
    };
    const rentalDate = {
      pickUp: {
        dateTime: dayjs(statePTimeStr),
      },
      dropOff: {
        dateTime: dayjs(stateRTimeStr),
      },
    };
    const age = '30~60';
    const adultSelectNum = 2;
    const childSelectNum = 0;

    const initialState = {
      LocationAndDate: {
        rentalDate,
        rentalLocation,
        age,
        adultSelectNum,
        childSelectNum,
      },
    };
    updateStore(initialState);
    await waitFor(() => {
      const pAddressText = Page.queryByText('上海虹桥机场');
      expect(pAddressText).toBeTruthy();
      const rAddressText = Page.queryByText(
        '浦东国际机场T2航站楼-地下负一层-靠近检票口-国际/港澳台到达',
      );
      expect(rAddressText.parent.props.numberOfLines).toEqual(1);
    });
  },
  TEST_TIMEOUT,
);
