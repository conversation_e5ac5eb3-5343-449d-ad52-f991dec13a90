import { act, waitFor, fireEvent } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import URL from '@c2x/apis/URL';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { AppContext, Utils, CarFetch } from '../../src/pages/xcar/Util/Index';
import {
  createPage,
  createInterTestName,
  pressWithTestId,
} from '../PageProvider';
import UITestId from '../../src/pages/xcar/Constants/UITestID';
import { renderWithAction } from '../testHelpers';
// @ts-ignore 禁止Redux Log
// __DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));

const orderRes3898581 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3898581.json');
const orderRes3898553 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3898553.json');
const orderRes3898532 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3898532.json');
const orderRes3898546 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3898546.json');
const orderRes4067806 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/4067806.json');
const orderRes4067813 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/4067813.json');
const orderRes4067869 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/4067869.json');
const orderRes3898518 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3898518.json');
const orderRes3898840 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3898840.json');
const orderRes4067911 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/4067911.json');
const orderRes4068205 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/4068205.json');

// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '36510',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { name: 1, app: { urlQuery } }, initialState);
};

describe('订详页-押金', () => {
  test(
    createInterTestName({
      testId: [3898581, 3898574, 3898560, 3898595, 3898609, 3898637, 3898539],
      name: '免押',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898581));
        const Page = await renderPage(state);
        // 押金展示内容 3898581
        await waitFor(() => {
          expect(Page.container).toHaveTextContent('免收');
          expect(Page.container).toHaveTextContent(
            '预计2025-02-10 11:00解除信用授权',
          );
          expect(Page.container).toHaveTextContent(
            '取消将收取全部租金作为违约金',
          );
          expect(Page.container).toHaveTextContent('提前/延后取还');
        });
        // 点击"押金明细" 3898581
        await pressWithTestId(
          Page,
          UITestId.car_testid_page_order_detail_deposit_block,
        );
        await waitFor(() => {
          const depositDetailModal = Page.getByTestId(
            UITestId.car_testid_page_order_detail_deposit_detail_modal,
          );
          const depositDetailModalHeader = Page.getByTestId(
            UITestId.car_testid_page_order_detail_deposit_detail_modal_header,
          );
          // 点击【押金明细】底部弹出“押金明细”浮层 3898581
          expect(depositDetailModal).toBeTruthy();
          // 免押信息-标题 3898574
          expect(depositDetailModalHeader).toHaveTextContent('押金明细');
          // 底部说明文案 3898560
          expect(depositDetailModal).toHaveTextContent(
            '如产生车损和违章，建议与门店现场结算，并保留相关凭证，后续将不会扣除您的押金',
          );
          // 授权及解冻进度-授权免押时间 3898595
          expect(depositDetailModal).toHaveTextContent('授权芝麻信用免押');
          // 免押信息-订单免押金额披露 3898609
          expect(depositDetailModal).toHaveTextContent(
            '此单已享免押金信用免押THB 10000.00，约¥2125.00',
          );
          // 授权及解冻进度-解除授权-未解冻 3898637 3898539
          expect(depositDetailModal).toHaveTextContent(
            '预计解冻2025-02-10 11:00',
          );
        });
      });
    },
  );
});

describe('订详页-押金', () => {
  test(
    createInterTestName({
      testId: [3898553, 3898616, 3898567, 3898588],
      name: '已解冻',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898553));
        const Page = await renderPage(state);
        // 已解冻，无扣费 3898553
        await waitFor(() => {
          expect(Page.container).toHaveTextContent('已解除信用授权');
        });
        await pressWithTestId(
          Page,
          UITestId.car_testid_page_order_detail_deposit_block,
        );
        await waitFor(() => {
          const depositDetailModal = Page.getByTestId(
            UITestId.car_testid_page_order_detail_deposit_detail_modal,
          );
          // 授权及解冻进度-解除授权-已解冻 3898616 3898567
          expect(depositDetailModal).toHaveTextContent(
            '2024-10-22 14:49额度已解冻',
          );
        });
        await renderWithAction({
          action: async () => {
            const helpIcon = await Page.findByTestId(
              UITestId.c_testid_orderDetail_deposit_detail_modal_help_icon,
            );
            fireEvent.press(helpIcon);
          },
          expect: async () => {
            const depositRateIntroduceModal = await Page.findByTestId(
              UITestId.c_testid_depositRateIntroduceModal,
            );
            // 3898588 弹出“押金汇率说明”弹层
            expect(depositRateIntroduceModal).toHaveTextContent(
              '押金所需人民币按照授权免押时的汇率计算，押金实际所需人民币以授权免押时的金额为准',
            );
          },
        });
      });
    },
  );
});

describe('订详页-押金', () => {
  test(
    createInterTestName({
      testId: [3898532],
      name: '已解冻，有扣费',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898532,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898532));
        const Page = await renderPage(state);
        await pressWithTestId(
          Page,
          UITestId.car_testid_page_order_detail_deposit_block,
        );
        await waitFor(() => {
          const depositDetailModal = Page.getByTestId(
            UITestId.car_testid_page_order_detail_deposit_detail_modal,
          );
          // 已解冻，有扣费 3898532
          expect(depositDetailModal).toHaveTextContent('已扣费：¥ 330.04');
          expect(depositDetailModal).toHaveTextContent('解除信用授权');
        });
      });
    },
  );
});

describe('订详页-押金', () => {
  test(
    createInterTestName({
      testId: [3898518, 3898602, 3898861],
      name: '扣费失败-提示',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898518,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898518));
        const Page = await renderPage(state);
        const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
        await pressWithTestId(
          Page,
          UITestId.car_testid_page_order_detail_deposit_block,
        );
        await waitFor(() => {
          const depositDetailModal = Page.getByTestId(
            UITestId.car_testid_page_order_detail_deposit_detail_modal,
          );
          // 扣费失败-提示 3898518
          expect(depositDetailModal).toHaveTextContent(
            '暂无法解冻您尚未支付车损费用，请保证账户中有足够金额',
          );
        });
        await waitFor(() => {
          const depositDetailModal = Page.getByTestId(
            UITestId.car_testid_page_order_detail_deposit_detail_modal,
          );
          // 押金明细的节点中露出【每项扣费明细的入口】 3898861
          expect(depositDetailModal).toHaveTextContent('查看扣费详情');
        });
        await renderWithAction({
          action: async () => {
            const depositProgressContent = Page.getByTestId(
              UITestId.c_testid_orderDetail_depositProgressContent,
            );
            const viewDamageDetailButton =
              depositProgressContent.findByType(BbkTouchable);
            fireEvent.press(viewDamageDetailButton);
          },
          expect: () => {
            // 3898602 授权及解冻进度-授权扣费时间 查看扣费详情 进入到扣费明细页面
            expect(pagePushFunc).toBeCalledWith('DamageDetail');
          },
        });
      });
    },
  );
});

describe('订详页-押金', () => {
  test(
    createInterTestName({
      testId: [3898546, 3898511],
      name: '未解冻，有扣费',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898546,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898546));
        const Page = await renderPage(state);
        await pressWithTestId(
          Page,
          UITestId.car_testid_page_order_detail_deposit_block,
        );
        await waitFor(() => {
          const depositDetailModal = Page.getByTestId(
            UITestId.car_testid_page_order_detail_deposit_detail_modal,
          );
          // 未解冻，有扣费 3898546 3898511
          expect(depositDetailModal).toHaveTextContent('已扣费：¥ 835.56');
          expect(depositDetailModal).toHaveTextContent(
            '预计解冻2024-10-11 15:00',
          );
        });
      });
    },
  );
});

describe('订详页-保障服务', () => {
  test(
    createInterTestName({
      testId: [4068233, 4068128, 4068191, 4068212, 4068226],
      name: '保障服务',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898581));
        const Page = await renderPage(state);
        await waitFor(() => {
          // 模块名称 4068233
          expect(Page.container).toHaveTextContent('保障服务');
          // 保险内容展示 4068226
          expect(Page.container).toHaveTextContent('基础套餐保险');
          // 商品侧套餐包含项需去重展示 如SLDW和CDW仅返回一个 4068191
          // 详情页1级页面套餐包含项合并展示 4068212
          expect(Page.container).toHaveTextContent(
            '碰撞盗抢保障 起赔额THB 5000',
          );
          expect(Page.container).toHaveTextContent('第三者保障 起赔额THB 5000');
          // 未加购平台险提示文案 4068128
          expect(Page.container).toHaveTextContent(
            '取车时，店员可能会向您推销额外保险或收费项目，请根据实际需要选购。在签署合同时，请仔细核对是否有额外收费。如被强制要求购买，请您不要签字，当场联系携程客服协助解决。',
          );
        });
      });
    },
  );
});

describe('订详页-保障服务', () => {
  test(
    createInterTestName({
      testId: [4068205, 4068156, 4068177, 4068121],
      name: '保障服务',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes4068205));
        const Page = await renderPage(state);
        const openURL = jest.spyOn(URL, 'openURL');
        await waitFor(() => {
          // 保险订单状态 4068205
          expect(Page.container).toHaveTextContent('安心补充险已出保');
          expect(Page.container).toHaveTextContent('驾乘意外险已出保');
          // 当车辆碰撞险和车辆盗抢险起赔额一致时 4068177
          expect(Page.container).toHaveTextContent('0起赔额');
        });
        await renderWithAction({
          action: async () => {
            const button = await Page.findAllByTestId(
              UITestId.car_testid_page_order_detail_insurancebox_item_electronicdetail,
            );
            fireEvent.press(button[0]);
          },
          expect: () => {
            // 安心补充险加保障链接 4068156
            // 点击保险条款 打开对应pdf内容（数据原读取后台保险条款）4068121
            expect(openURL).toBeCalledWith(
              'https://baoxian.pingan.com/pa18shopnst/do/era/PasCommon/downloadPolicyStream?policyNo=JU7AvSK%2BA5li0kHNwBVfik1k7QE%2F9%2BNYVsYT29zoXQPTbhpcB4GO%2BRtFG7ATcJbEFd7sL5e774DRi0WXgAckUm9eiUCkGObszFq3ZHkUxc4YG8%2FvoQ%2BxCEXpG%2FVjlFcIddvlE%2F%2FSoYrXW5mx3O1WMWBlQG%2BVpRi1qSDiaLVwiQ0%3D/&partnerCode=P_CTRIP_GA',
            );
          },
        });
        await renderWithAction({
          action: async () => {
            const button = await Page.findAllByTestId(
              UITestId.car_testid_page_order_detail_insurancebox_item_electronicdetail,
            );
            fireEvent.press(button[1]);
          },
          expect: () => {
            // 驾乘意外险增加保障链接 4068156
            expect(openURL).toBeCalledWith(
              'https://baoxian.pingan.com/pa18shopnst/do/era/PasCommon/downloadPolicyStream?policyNo=Dv4Y2kcqroUj2I5WoYi8Fo%2BQ4kc7P2E19NXBORULAshVTUNXnPt366fP6A%2BCRvCmM1AO1uBLx2rXjpf1MWSZk48GQ86FpJJzaQeybJTaZb6rOq7n9G%2FgtBDwRiGgJRaLiC2QuKiK8v0Pbl3KR5Vj9spfY3jcD%2Fv6peuYmz2%2B%2B4g%3D/&partnerCode=P_CTRIP_GA',
            );
          },
        });
      });
    },
  );
});

describe('订详页-取消政策', () => {
  test(
    createInterTestName({
      testId: [4068100, 4067974],
      name: '取消政策',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898581));
        const Page = await renderPage(state);
        await waitFor(() => {
          // 取消政策 4068100
          expect(Page.container).toHaveTextContent('免费取消');
          // 取消政策展示 4067974
          expect(Page.container).toHaveTextContent(
            '取消政策当地时间2024-12-10 10:00前可免费取消取车时间前2024-12-10 10:00前可免费取消取车时间后2024-12-10 10:00后取消将收取全部租金作为违约金注意：均为当地时',
          );
        });
      });
    },
  );
});

describe('订详页-旅行限制', () => {
  test(
    createInterTestName({
      testId: [4068268],
      name: '旅行限制',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898581));
        const Page = await renderPage(state);
        await waitFor(() => {
          // 旅行限制 4068100
          expect(Page.container).toHaveTextContent('旅行限制');
        });
      });
    },
  );
});

// 履约可视化AB全量开启，用车助手模块移除
describe.skip('订详页-用车助手', () => {
  test(
    createInterTestName({
      testId: [4068576],
      name: '用车助手',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898581));
        const Page = await renderPage(state);
        await waitFor(() => {
          // 用车助手 4068576
          // 履约可视化AB全量开启，取车材料展示到二级页面
          // expect(Page.container).toHaveTextContent(
          //   '取车时记得携带取车材料和提车凭证免费办理驾照翻译件取车材料&租车指南提车凭证自驾政策',
          // );
        });
      });
    },
  );
});

// 履约可视化AB全量开启，用车助手模块移除
describe.skip('订详页-用车助手', () => {
  test(
    createInterTestName({
      testId: [4067827, 4067834],
      name: '用车助手',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898553));
        const Page = await renderPage(state);
        await waitFor(() => {
          // 已取消订单用车小贴士不展示操作 4067827
          expect(Page.container).not.toHaveTextContent('自驾政策');
          expect(Page.container).not.toHaveTextContent('提车凭证');
          // expect(Page.container).not.toHaveTextContent('合同');
          expect(Page.container).not.toHaveTextContent('企业微信');
          // 已取消订单用车小贴士-取车材料&租车指南 4067834
          // expect(Page.container).toHaveTextContent('期待您的下一次预订');
          // expect(Page.container).toHaveTextContent('取车材料&租车指南');
        });
      });
    },
  );
});

// 履约可视化AB全量开启，用车助手模块移除
describe.skip('订详页-用车助手', () => {
  test(
    createInterTestName({
      testId: [4067820, 4067841, 4067792],
      name: '用车助手',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898532));
        const Page = await renderPage(state);
        await waitFor(() => {
          // 已完成订单用车小贴士-企业微信-确认不展示企业微信入口 4067820
          expect(Page.container).not.toHaveTextContent('企业微信');
          // 已完成订单用车小贴士-提车凭证-确认展示提车凭证按钮 4067841
          // expect(Page.container).toHaveTextContent('提车凭证');
          // 已完成订单用车小贴士-取车材料&租车指南  4067792
          // expect(Page.container).toHaveTextContent(
          //   '取车时记得携带取车材料和提车凭证',
          // );
          // expect(Page.container).toHaveTextContent('取车材料&租车指南');
        });
      });
    },
  );
});

describe('订详页-用车助手', () => {
  test(
    createInterTestName({
      testId: [4067813, 4067869],
      name: '用车助手',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes4067813));
        const Page = await renderPage(state);
        await waitFor(() => {
          // 用车中订单用车小贴士-企业微信 4067813
          // 出境四期订单 露出用车小贴士入口 4067869
          // 履约可视化AB全量开启，取车材料展示到二级页面
          // expect(Page.container).toHaveTextContent('企业微信');
        });
      });
    },
  );
});

describe('订详页-用车助手', () => {
  test(
    createInterTestName({
      testId: [4067869],
      name: '非出境四期订单',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes4067869));
        const Page = await renderPage(state);
        await waitFor(() => {
          // 出境非四期订单 不露出用车小贴士入口 4067869
          expect(Page.container).not.toHaveTextContent('企业微信');
        });
      });
    },
  );
});

describe('订详页-用车助手', () => {
  test(
    createInterTestName({
      testId: [4067806, 4067799],
      name: '用车助手',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 4067806,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes4067806));
        const Page = await renderPage(state);
        const openURL = jest.spyOn(URL, 'openURL');
        await waitFor(() => {
          // 履约可视化AB全量开启，取车材料展示到二级页面
          // // 已完成订单用车小贴士-合同参照 4067806
          // expect(Page.container).toHaveTextContent('中英合同参照');
          // // 已完成订单用车小贴士-自驾政策 4067799
          // expect(Page.container).toHaveTextContent('自驾政策');
        });
        // await renderWithAction({
        //   action: async () => {
        //     const button = await Page.findByText('自驾政策');
        //     fireEvent.press(button);
        //   },
        //   expect: () => {
        //     // 已完成订单用车小贴士-自驾政策 4067799 点击自驾政策按钮 根据配置链接跳转自驾政策页面
        //     expect(openURL).toBeCalledWith(
        //       'https://m.ctrip.com/tangram/ODAxNTg=?ctm_ref=vactang_page_80158&isHideNavBar=YES&apppgid=10650039393&statusBarStyle=1&channelid=237770',
        //     );
        //   },
        // });
      });
    },
  );
});

describe('订详页', () => {
  test(
    createInterTestName({
      testId: [4067575],
      name: '回退按钮',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898581,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898532));
        const Page = await renderPage(state);
        const pagePopFunc = jest.spyOn(CRNPage.prototype, 'pop');
        await renderWithAction({
          action: async () => {
            const backBtn = await Page.findByTestId(
              UITestId.car_testid_page_order_detail_page_header_left_icon,
            );
            fireEvent.press(backBtn);
          },
          // 回退按钮 4067575
          expect: () => {
            expect(pagePopFunc).toBeCalled();
          },
        });
      });
    },
  );
});

describe('订详页-押金', () => {
  test(
    createInterTestName({
      testId: [3898840, 3898854],
      name: '押金扣款',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898840,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes3898840));
        const Page = await renderPage(state);
        const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
        await waitFor(() => {
          // 3898840 押金扣款{xx笔} 【查看详情】
          expect(Page.container).toHaveTextContent('押金扣款（3笔）查看详情');
        });
        await renderWithAction({
          action: async () => {
            const damageEntry = Page.getByText('查看详情');
            fireEvent.press(damageEntry);
          },
          expect: () => {
            // 3898854 扣费明细入口-入口2：一级页面露出扣费列表入口-点击【查看详情】进入到“扣费列表”
            expect(pagePushFunc).toBeCalledWith('SupplementList', { type: 4 });
          },
        });
      });
    },
  );
});

describe('订详页', () => {
  test(
    createInterTestName({
      testId: [4067911],
      name: '车辆信息',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 3898840,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(orderRes4067911));
        const Page = await renderPage(state);
        await waitFor(() => {
          // 4067911 推销、强销保险强提示
          expect(Page.container).toHaveTextContent(
            '境外车行大都存在推销及强销售保险的风险，请注意不需要保险的情况下合理拒绝门店推销，签署租车合同时注意保险类的条款。',
          );
        });
      });
    },
  );
});
