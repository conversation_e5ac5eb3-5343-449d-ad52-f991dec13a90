import { fireEvent } from '@testing-library/react-native';
import { TouchableOpacity, TextInput } from 'react-native';
import CRNPage from '@c2x/components/Page';
import { AppContext, Utils, CarStorage } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { renderWithAction } from '../testHelpers';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailCancelContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  jest
    .spyOn(CarStorage, 'load')
    .mockImplementation(() => Promise.resolve('[]'));
  jest
    .spyOn(CarStorage, 'loadAsync')
    .mockImplementation(() => Promise.resolve('[]'));
  return createPage(container, {}, initialState);
};
describe('境外订详 - 取消订单', () => {
  test(
    createInterTestName({
      testId: [
        458433, 458432, 458431, 458428, 458427, 458426, 458424, 458425, 458429, 4068387,
      ],
      name: '境外订详 - 取消订单页',
    }),
    async () => {
      const state = {
        OrderDetail: {
          resCancelFee: {
            amount: 10,
            currencyCode: 'CNY',
          },
          orderBaseInfo: {
            orderId: 123213,
          },
          cancelRuleInfo: {
            bottomDesc: '因修改订单产生的“订单修改费”不退费。',
            cancelReasonsV2: [
              {
                code: 3,
                cancelReasonList: [],
                reason: '修改订单/行程',
                sort: 1,
              },
              {
                code: 1,
                cancelReasonList: [],
                reason: '行程取消',
                sort: 2,
              },
              {
                code: 2,
                cancelReasonList: [
                  {
                    reason: '携程降价',
                    sort: 1,
                    code: 12,
                  },
                  {
                    reason: '门店',
                    sort: 2,
                    code: 13,
                  },
                  {
                    reason: '其他平台',
                    sort: 3,
                    code: 14,
                  },
                ],
                reason: '有更低价',
                sort: 3,
              },
              {
                code: 10,
                cancelReasonList: [],
                reason: '订单未免押',
                sort: 4,
              },
              {
                code: 5,
                cancelReasonList: [],
                reason: '未带取车证件',
                sort: 5,
              },
              {
                code: 4,
                cancelReasonList: [
                  {
                    reason: '限行限号',
                    sort: 1,
                    code: 6,
                  },
                  {
                    reason: '颜色/配置不满意',
                    sort: 2,
                    code: 15,
                  },
                  {
                    reason: '车辆信息错误',
                    sort: 3,
                    code: 16,
                  },
                ],
                reason: '车辆问题',
                sort: 6,
              },
              {
                code: 7,
                cancelReasonList: [],
                reason: '门店告知无车',
                sort: 7,
              },
              {
                code: 11,
                cancelReasonList: [],
                reason: '疫情管控',
                sort: 8,
              },
              {
                code: 9,
                cancelReasonList: [
                  {
                    reason: '航班延误/取消',
                    sort: 1,
                    code: 8,
                  },
                  {
                    reason: '对车行服务不满意',
                    sort: 2,
                    code: 17,
                  },
                  {
                    reason: '其他缘由',
                    sort: 3,
                    code: 18,
                  },
                ],
                reason: '其他',
                sort: 9,
              },
            ],
            reOrderTitle: '仍需用车？',
            cancelReasons: [
              '修改订单/行程',
              '行程取消',
              '有更低价',
              '订单未免押',
              '未带取车证件',
              '车辆问题',
              '门店告知无车',
              '疫情管控',
              '其他',
            ],
            cancelRules: [
              {
                freeStatus: 1,
                free: 1,
                title: '免费取消',
                hit: false,
                time: '支付完成至2023-05-23 06:00',
              },
              {
                freeStatus: 0,
                free: 0,
                title: '扣订单总金额30%',
                hit: false,
                time: '2023-05-23 06:00至10:00',
              },
              {
                freeStatus: 0,
                free: 0,
                title: '扣订单全额',
                hit: true,
                time: '2023-05-23 10:00后',
              },
            ],
            reOrderExplain:
              '若门店车辆不满足您对颜色、牌照、配置或出行的相关需求，您可重新预订',
          },
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      const pagePopFunc = jest.spyOn(CRNPage.prototype, 'pop');
      const Page = await renderPage(state);
      expect(Page.container).toHaveTextContent('取消订单');
      expect(Page.container).toHaveTextContent(
        '您将要取消该订单，请留意可能存在的违约费用',
      );
      expect(Page.container).toHaveTextContent('取消费用¥10');
      expect(Page.container).toHaveTextContent(
        '取消原因修改订单/行程行程取消有更低价订单未免押未带取车证件车辆问题门店告知无车疫情管控其他',
      );
      await renderWithAction({
        action: async () => {
          const bootTouchable = await Page.container.findAllByType(
            TouchableOpacity,
          );
          fireEvent(bootTouchable[0], 'onPress');
        },
        expect: () => {
          expect(pagePopFunc).toBeCalled();
        },
      });
      const input = await Page.container.findAllByType(TextInput);
      expect(input[0].props.placeholder).toBe('其他意见和建议，最多输入100字');

      await renderWithAction({
        action: async () => {
          const cancelBtn = await Page.findAllByTestId(
            'car_osd_order_cancel_btn',
          );
          fireEvent(cancelBtn[0], 'onPress');
        },
        expect: async () => {
          expect(Page.container).toHaveTextContent('请填写取消原因');
        },
      });
    },
  );
});
