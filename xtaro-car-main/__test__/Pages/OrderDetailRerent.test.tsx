/* eslint-disable no-underscore-dangle */
import { act, waitFor, fireEvent } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import { AppContext, Utils, CarStorage } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import UITestId from '../../src/pages/xcar/Constants/UITestID';
import { renderWithAction } from '../testHelpers';

// @ts-ignore 禁止Redux Log
// __DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '36510',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { name: 1 }, initialState);
};

describe('订详页-续租', () => {
  // 用车中，可续租
  test(
    createInterTestName({
      testId: [
        408160, 408173, 408168, 408180, 408177, 408165, 408166, 408170, 408171,
        408172,
      ],
      name: '订详续租卡片，露出逻辑',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408160,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);

        // 履约可视化上线后，卡片露出位置变更
        return;
        const renewCard = await Page.findByTestId(
          `${UITestId.car_testid_comp_orderDetail_message_card}_RenewCardDom`,
        );
        await waitFor(() => {
          // 续租卡片露出
          expect(renewCard).toBeTruthy();
        });

        // 408165 408172 点击-直到 0天 预期：-号按钮不可点击，置灰显示
        await renderWithAction({
          action: async () => {
            expect(renewCard).toHaveTextContent('1天');
            const minusBtn = await Page.findByTestId(
              UITestId.car_testid_renew_card_minus,
            );
            fireEvent.press(minusBtn);
            fireEvent.press(minusBtn);
          },
          expect: async () => {
            expect(renewCard).toHaveTextContent('0天');
          },
        });

        // 408166 订详续租卡片，天数展示 天数增加 1
        await renderWithAction({
          action: async () => {
            expect(renewCard).toHaveTextContent('0天');
            const plusBtn = await Page.findByTestId(
              UITestId.car_testid_renew_card_plus,
            );
            fireEvent.press(plusBtn);
          },
          expect: async () => {
            expect(renewCard).toHaveTextContent('1天');
          },
        });

        // 408170 天数为2天，点击【去续租】预期：跳转续租申请页，续租时间带入2天
        const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
        await renderWithAction({
          action: async () => {
            const plusBtn = await Page.findByTestId(
              UITestId.car_testid_renew_card_plus,
            );
            fireEvent.press(plusBtn);
            expect(renewCard).toHaveTextContent('2天');
            fireEvent.press(renewCard);
          },
          expect: async () => {
            expect(pagePushFunc).toBeCalledWith('Rerent', {
              visible: true,
              renewDay: 2,
            });
          },
        });

        await waitFor(() => {
          const reRent = Page.getByTestId(
            `${UITestId.car_testid_page_order_op_btn}_续租`,
          );
          // 按钮高亮
          expect(reRent?.props?.style?.backgroundColor).toEqual('#006ff6');
          // 点击续租按钮

          fireEvent.press(reRent);
          expect(pagePushFunc).toBeCalledWith('Rerent', {
            visible: true,
            renewDay: 2,
          });
        });
        // 文案展示 408168
        await waitFor(() => {
          expect(Page.container).toHaveTextContent('还车前');
        });
      });
    },
  );
  // 用车中，可续租
  test(
    createInterTestName({
      testId: [408164],
      name: '默认展示',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408160,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);
        waitFor(() => {
          // 默认展示1天
          const reNewDays = Page.getByTestId(
            UITestId.car_testid_page_order_detail_renew_days,
          );
          expect(
            // @ts-ignore
            reNewDays?._fiber?.pendingProps?.children?.props?.children?.[0],
          ).toEqual(1);
        });
      });
    },
  );
  // 不可续租
  test(
    createInterTestName({
      testId: [408174, 408175, 408176, 408161, 408162, 408167],
      name: '订详续租按钮，高亮逻辑',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408174,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);
        await waitFor(() => {
          const reRent = Page.getByTestId(
            `${UITestId.car_testid_page_order_op_btn}_续租`,
          );
          // 按钮置灰
          expect(reRent?.props?.style?.backgroundColor).toEqual('#eee');
        });
      });
    },
  );
});
