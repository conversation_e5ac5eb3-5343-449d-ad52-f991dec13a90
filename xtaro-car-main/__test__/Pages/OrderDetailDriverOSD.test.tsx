/**
 * 订单详情页 驾驶员信息
 */
import { fireEvent } from '@testing-library/react-native';
import { act } from 'react-test-renderer';
import { AppContext, Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { waitRender } from '../testHelpers';
import exp from 'constants';

const orderRes20240913 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/20240913.json');

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    saveIsd: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  jest
    .spyOn(CarStorage, 'load')
    .mockImplementation(() => Promise.resolve('[]'));
  jest
    .spyOn(CarStorage, 'loadAsync')
    .mockImplementation(() => Promise.resolve('[]'));
  return createPage(container, { name: 1 }, initialState);
};

test(
  createInterTestName({
    testId: [
      455677, 455678, 455684, 455680, 4068331, 4068338, 4068317, 4068359,
      4068317, 4068331, 4068359, 4068338,
    ],
    name: '境外订详 - 驾驶员信息',
  }),
  async () => {
    // TODO: @ZXY snapshot生成2次，提示未找到对应元素
    return;
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
    jest
      .spyOn(CarFetch, 'queryOrder')
      .mockImplementation(() => Promise.resolve(orderRes20240913));
    const Page = await renderPage();
    const PageDom = Page.container;

    await waitRender(async () => {
      // 455677 驾驶员信息
      // expect(Page).toMatchSnapshot();
      expect(PageDom).toHaveTextContent('zuo/yanru');
      expect(PageDom).toHaveTextContent('25');
      expect(PageDom).toHaveTextContent('12***<EMAIL>');
      expect(PageDom).toHaveTextContent('86-132****2196');

      // 455678 航班号
      expect(PageDom).toHaveTextContent('MF8693');

      // 455684 航班延误
      expect(PageDom).toHaveTextContent('航班延误保留政策');

      // 455680 航班延误车辆保留规则页
      const flightDelayBtn = await Page.findByText('航班延误保留政策');
      await act(() => {
        fireEvent.press(flightDelayBtn);
      });
      expect(PageDom).toHaveTextContent(
        '建议您填写航班号，如遇延误请携带机票前往取车',
      );
      expect(PageDom).toHaveTextContent(
        '如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。',
      );
    });
  },
);
