import { act, waitFor, fireEvent, within } from '@testing-library/react-native';
import { AppContext, Utils, CarStorage } from '../../src/pages/xcar/Util/Index';
import {
  createPage,
  createInterTestName,
  pressWithTestId,
} from '../PageProvider';
import UITestId from '../../src/pages/xcar/Constants/UITestID';
import { renderWithAction, waitRender } from '../testHelpers';
// @ts-ignore 禁止Redux Log
// __DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '36510',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { name: 1 }, initialState);
};

describe('订详页-押金', () => {
  test(
    createInterTestName({
      testId: [407985, 407986],
      name: '免押',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 407985,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);
        // 页面展示 407985
        await waitFor(() => {
          expect(Page.container).toHaveTextContent('免收');
        });
        // 点击"押金明细" 407986
        await pressWithTestId(
          Page,
          UITestId.car_testid_page_order_detail_deposit_block,
        );
        await waitFor(() => {
          const depositDetailModal = Page.getByTestId(
            UITestId.car_testid_page_order_detail_deposit_detail_modal,
          );
          expect(depositDetailModal).toBeTruthy();
        });
      });
    },
  );
  test(
    createInterTestName({
      testId: [407987, 407991],
      name: '非免押',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 407987,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);
        // 页面展示 407987
        await waitFor(() => {
          expect(Page.container).toHaveTextContent(
            '取车时支付租车押金¥3000，还车时支付违章押金¥2000',
          );
        });
        await pressWithTestId(
          Page,
          UITestId.car_testid_page_order_detail_deposit_block,
        );
        const depositDetailModal = within(
          await Page.findByTestId(
            UITestId.car_testid_page_order_detail_deposit_detail_modal,
          ),
        );
        // 407991 点击冻结押金说明 预期：展示弹层 冻结押金
        await renderWithAction({
          action: async () => {
            const addDriverBtn = await depositDetailModal.findByText(
              '什么是冻结押金',
            );
            fireEvent.press(addDriverBtn);
          },
          expect: () => {
            expect(Page.container).toHaveTextContent(
              '冻结押金是指当您使用信用卡作为支付方式时，冻结1笔押金金额作为押金预授权，这笔冻结的金额将占用掉您的信用卡额度，但实际不产生扣款形式的交易信息。',
            );
          },
        });
      });
    },
  );
});
