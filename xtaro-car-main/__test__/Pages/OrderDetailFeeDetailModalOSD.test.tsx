/* eslint-disable @ctrip/ctrip/ascii-only */
import { fireEvent } from '@testing-library/react-native';
import { AppContext, Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { renderWithAction } from '../testHelpers';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, {}, initialState);
};

describe('境外订详 - 费用明细弹层', () => {
  test(
    createInterTestName({
      testId: [
        458409, 3898238, 3898301, 3898259, 3898280, 3898315, 3898273, 3898336,
      ],
      name: '境外订详 - 常规',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

      const Page = await renderPage(state);

      await renderWithAction({
        action: async () => {
          const priceDetailBtn = await Page.findAllByTestId(
            UITestID.car_testid_page_order_price_detail,
          );
          fireEvent.press(priceDetailBtn[0]);
        },
        expect: async () => {
          const priceModal = await Page.findByTestId(
            UITestID.car_osd_orderdetail_price_modal,
          );
          expect(priceModal).toBeOnTheScreen();
          // 3898238
          expect(priceModal).toHaveTextContent('费用明细');
          // 3898301 优惠券
          expect(priceModal).toHaveTextContent('优惠券');
          expect(priceModal).toHaveTextContent('- ¥50');
          expect(priceModal).toHaveTextContent('国际租车满减券');
          // 3898259 3898280
          expect(priceModal).toHaveTextContent(
            '车辆租金+基础套餐基础租车费用¥546约¥182×3天基础租车费用等油取还、基本租车费用、税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税、不限里程',
          );
          expect(priceModal).toHaveTextContent(
            '*上述金额是按当前汇率换算的，实际到店支付费用以门店当天汇率为准',
          );
          // 3898315 全额展示-在线支付
          // 3898336 全额展示-中途放弃自营险-此处为实际支付金额
          expect(priceModal).toHaveTextContent('在线预付 ¥496');
          // 3898273 下单过程中放弃自营险 不展示自营险条目
          expect(priceModal).not.toHaveTextContent('安心补充险');
          expect(priceModal).not.toHaveTextContent('驾乘意外险');
        },
      });
    },
  );
});
