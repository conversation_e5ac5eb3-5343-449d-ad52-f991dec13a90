import { Image, TouchableOpacity } from 'react-native';
import { fireEvent } from '@testing-library/react-native';
import { BbkConstants } from '../../src/pages/xcar/Common/src/Utils';
import { Utils, User, AppContext, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { defaultRecommendDate } from '../../src/pages/xcar/State/LocationAndDate/Mappers';
import { renderWithAction, waitRender } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

const productRes = require('../../__mocks__/restful/18631/queryProductInfo/296410.json');
const priceRes = require('../../__mocks__/restful/18631/querypriceinfo/296412.json');

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};

test(
  createInterTestName({
    testId: [489320],
    name: '顶部',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes));
    const Page = await renderPage();

    const container = await Page.findByTestId(UITestID.car_testid_page_booking);
    expect(container).toHaveTextContent('订单填写');

    await renderWithAction({
      action: async () => {
        const btns = await container.findAllByType(TouchableOpacity);
        fireEvent.press(btns[0]);
      },
      expect: async () => {
        // @ts-ignore
        expect(global.popCount).toEqual(1);
      },
    });
  },
  TEST_TIMEOUT,
);
