import { fireEvent } from '@testing-library/react-native';
import { TouchableOpacity } from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import DriverList from '../../src/pages/xcar/Containers/DriverListContainer';
import { AppContext, Utils, User, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { waitRender } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';

const res451574 = require('../../__mocks__/restful/18631/queryCommonPassenger/OSD/451574.json');

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'DriverList',
  });

  return createPage(DriverList, {}, initialState);
};

test(
  createInterTestName({
    testId: [451582],
    name: '删除驾驶员',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const Page = await renderPage();
    // const PageDom = Page.container;

    await waitRender(async () => {
      const driverItems = await Page.findAllByTestId(
        UITestID.car_testid_page_driverlist_item,
      );

      const firstDeleteBtn = driverItems[0].findAllByType(TouchableOpacity)[3];

      const deleteApi = jest.spyOn(CarFetch, 'deleteDriver');
      // 点击删除
      await fireEvent.press(firstDeleteBtn);

      expect(deleteApi).toBeCalledWith({ passengerId: '13478' });
    });
  },
);

test(
  createInterTestName({
    testId: [451574, 451575, 451576],
    name: '添加驾驶员',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(res451574));
    const Page = await renderPage();

    const content = await Page.findByTestId(
      UITestID.car_testid_page_driverlist_content,
    );
    // 451574
    expect(content).toHaveTextContent('Sdf/LISIdff');
    expect(content).toHaveTextContent('SDFSDF/DSFD');
    expect(content).toHaveTextContent('柴金涛');
    expect(content).toHaveTextContent('Test/ZHANGSAN');
    expect(content).toHaveTextContent('San/ZHANGSNA');
    expect(content).toHaveTextContent('Tu/JVIN');
    expect(content).toHaveTextContent('新增驾驶员');

    const pageDom = await Page.findByTestId(
      UITestID.car_testid_page_driverlist,
    );
    const backBtn = await pageDom.findAllByType(TouchableOpacity);
    await fireEvent.press(backBtn[0]);

    // @ts-ignore 451575
    expect(global.popCount).toEqual(1);
  },
);
