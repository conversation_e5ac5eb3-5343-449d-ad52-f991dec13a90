import { fireEvent } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import { TouchableOpacity } from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { AppContext, Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { renderWithAction } from '../testHelpers';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    saveIsd: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, {}, initialState);
};

const renderRefundPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderRefundDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { visible: true }, initialState);
};
describe('境外订详 - 退款', () => {
  test(
    createInterTestName({
      testId: [458443, 458445, 4067862],
      name: '境外订详 - 退款进度',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
      const Page = await renderPage(state);
      //  4067862 退款记录
      expect(Page.container).toHaveTextContent(
        '退款记录（3笔，退款总额¥1652）退款进度',
      );
      // expect(Page.toJSON()).toMatchSnapshot();
      await renderWithAction({
        action: async () => {
          const refundEntry = await Page.findByTestId(
            UITestID.car_testid_comp_orderDetail_refund_entry,
          );
          fireEvent.press(refundEntry);
        },
        expect: async () => {
          expect(pagePushFunc).toBeCalledWith('OrderRefundDetail', {
            visible: true,
          });
        },
      });
    },
  );
  test(
    createInterTestName({
      testId: [458443, 458448, 458447, 458445, 458444, 458443, 458446, 4067876, 4067897],
      name: '境外订详 - 退款进度页面',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
          refundProgressList: [
            {
              billNo: 2825046000530201,
              billItemNo: 2825046000530468,
              currency: 'CNY',
              refundAmount: 302,
              billStatus: 'SUCCESS',
              remark: '退款',
              dealStatus: 1,
              createTime: '/Date(1695712145322+0800)/',
              dealTime: '/Date(1695712175000+0800)/',
              refundCycle: '预计1个工作日退款到账',
              refundPeriodKey: 'key.payment.paymentserv.refundperiod.oneday',
              paymentWayID: 'EB_MobileAlipay',
              paymentWayName: '移动版支付宝',
            },
          ],
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      const pagePopFunc = jest.spyOn(CRNPage.prototype, 'pop');
      const Page = await renderRefundPage(state);
      expect(Page.container).toHaveTextContent('金额 ¥302');
      expect(Page.container).toHaveTextContent('退款已受理2023-09-26 15:09:05');
      expect(Page.container).toHaveTextContent(
        '处理退款申请2023-09-26 15:09:35',
      );
      expect(Page.container).toHaveTextContent(
        '处理退款申请退款成功2023-09-26 15:09:35 退至移动版支付宝,预计1个工作日退款到账',
      );
      expect(Page.container).toHaveTextContent(
        '处理退款申请退款成功2023-09-26 15:09:35 退至移动版支付宝,预计1个工作日退款到账',
      );
      await renderWithAction({
        action: async () => {
          const bootTouchable = await Page.container.findAllByType(
            TouchableOpacity,
          );
          fireEvent(bootTouchable[0], 'onPress');
        },
        expect: () => {
          expect(pagePopFunc).toBeCalled();
        },
      });
      // expect(Page.toJSON()).toMatchSnapshot();
    },
  );
});
