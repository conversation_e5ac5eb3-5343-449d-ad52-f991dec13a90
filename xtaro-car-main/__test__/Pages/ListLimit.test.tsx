/* eslint-disable @typescript-eslint/naming-convention */
import { fireEvent } from '@testing-library/react-native';
import { Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';
import { renderWithAction } from '../testHelpers';
import * as LocationAndDateSelectors from '../../src/pages/xcar/State/LocationAndDate/Selectors';

const queryProductsData = require('../../__mocks__/restful/18631/queryProducts/230495.json');

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));
jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));
jest.mock('../../src/pages/xcar/State/LocationAndDate/Selectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/LocationAndDate/Selectors'),
}));
const resTranslimit285039 = require('../../__mocks__/restful/18631/translimit/285039.json');

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: false,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

describe('列表页-限行', () => {
  test(
    createInterTestName({
      testId: [230530],
      name: '点击限行政策',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'getLimitContent')
        .mockImplementation(() => Promise.resolve(resTranslimit285039));
      jest
        .spyOn(CarFetch, 'getListProduct')
        .mockImplementationOnce(() => Promise.resolve(queryProductsData));

      jest
        .spyOn(LocationAndDateSelectors, 'getPickUpCityId')
        .mockReturnValue(2);
      jest
        .spyOn(LocationAndDateSelectors, 'getPickUpCityName')
        .mockReturnValue('上海');
      const Page = await renderPage();
      const PageDom = Page.container;
      const limitTip = await Page.findByText(
        '上海外牌车辆工作日部分道路分时段限行（沪牌车辆在市内不限行）',
      );
      await renderWithAction({
        action: async () => {
          fireEvent.press(limitTip);
        },
        expect: async () => {
          const limitModal = await Page.findByTestId(
            UITestID.car_testid_page_list_limitTip,
          );
          expect(limitModal).toHaveTextContent(
            '上海·城市限行规则上海外牌车辆工作日部分道路分时段限行（沪牌车辆在市内不限行）【外地牌照】外牌限行限行时间工作日:07:00-20:00限行范围限行说明1、高架限行时间： 07:00-20:00 (周六、周日、国定假日除外) 高架限行区域： (1)延安高架路（S20外环高速以东段） (2)南北高架路（呼玛路至鲁班立交段） (3)逸仙高架路（全线） (4)沪闵高架路（全线） (5)中环路（全线） (6)华夏高架路（全线） (7)罗山高架路（全线） (8)度假区高架路（中环路至秀浦路段） (9)内环高架路（除内圈中山北二路入口至锦绣路出口、外圈锦绣路入口至黄兴路出口以外的路段） (10)龙东高架路（罗山路立交至华东路） (11)虹梅高架路 （中环路至S20外环高速段） (12)北翟路地道 (13)南浦大桥 (14)卢浦大桥 (15)延安东路隧道 2、内环限行时间： 07:00-09:00和17:00-19:00 (周六、周日、国定假日除外) 内环地面限行区域：沿杨浦大桥地面投影—宁国路—黄兴路—中山北二路—中山北一路—中山北路—中山西路—中山南二路—中山南一路—中山南路—南浦大桥地面投影—龙阳路—罗山路合围区域内的道路(不含上述道路)。 包括“从限行区域外驶入限行区域”和“在限行区域内通行”两种情况。即：在内环外的外省市号牌小客车不得在交通早晚高峰时段驶入内环内，位于内环内的外省市号牌小客车交通早晚高峰时段不得在道路上行驶。 备注： 1、悬挂沪牌新能源号牌的机动车不受上述措施限制。 2、禁止外省市机动车号牌的小客车、使用临时行驶车号牌的小客车、未载客的出租小客车及实习期驾驶员驾驶的小客车在限行时间内在指定的高架区域道路通行。 3、禁止悬挂外省市机动车号牌的小客车在限行时间内在指定内环地面道路通行。 4、如需前往周边公路及景区，建议通过官方渠道查询其限行政策。 *以上信息仅供参考，更多详细信息敬请关注当地城市限行政策，按照官方通知为准。知道了',
          );
        },
      });
    },
  );
});
