import { fireEvent } from '@testing-library/react-native';
import {
  U<PERSON>s,
  User,
  AppContext,
  CarFetch,
  GetAB,
  GetABCache,
} from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { waitRender } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

const productRes = require('../../__mocks__/restful/18631/queryProductInfo/OSD/490330.json');
const productRes490289 = require('../../__mocks__/restful/18631/queryProductInfo/OSD/490289.json');
const productRes490290 = require('../../__mocks__/restful/18631/queryProductInfo/OSD/490290.json');

const productRes20231204 = require('../../__mocks__/restful/18631/queryProductInfo/OSD/20231204.json');
const priceRes20231204 = require('../../__mocks__/restful/18631/querypriceinfo/OSD/20231204.json');
const driverListRes20231204 = require('../../__mocks__/restful/18631/queryCommonPassenger/OSD/20231204.json');
const licensePolicy20231204 = require('../../__mocks__/restful/18631/queryLicencePolicy/OSD/20231204.json');
const licensePolicy20231205 = require('../../__mocks__/restful/18631/queryLicencePolicy/OSD/20231205.json');
const licensePolicy20231206 = require('../../__mocks__/restful/18631/queryLicencePolicy/OSD/20231206.json');
// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};

describe('不带入默认常旅', () => {
  const mockStateMap = [
    {
      passengerList: [
        {
          nationalityName: '中国',
          lastName: '',
          firstName: '',
          age: 38,
          nationality: 'CN',
          mobile: '19900066666',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0',
          isRecommend: true,
          fullName: '王宗国',
          birthday: '1985-08-29',
          passengerId: '109637998',
          certificateList: [
            { certificateType: '1', certificateNo: '220283198508297351' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    },
    {
      passengerList: [
        {
          nationalityName: '中国',
          lastName: 'WANG',
          firstName: 'ZONGGUO',
          age: 0,
          nationality: 'CN',
          mobile: '19900066666',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0',
          isRecommend: true,
          fullName: '王宗国',
          birthday: '1985-08-29',
          passengerId: '109637998',
          certificateList: [
            { certificateType: '1', certificateNo: '220283198508297351' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    },
    {
      passengerList: [
        {
          nationalityName: '中国',
          lastName: 'WANG',
          firstName: 'ZONGGUO',
          age: 13,
          nationality: 'CN',
          mobile: '19900066666',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0',
          isRecommend: true,
          fullName: '王宗国',
          birthday: '1985-08-29',
          passengerId: '109637998',
          certificateList: [
            { certificateType: '1', certificateNo: '220283198508297351' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    },
    {
      passengerList: [
        {
          nationalityName: '中国',
          lastName: 'WANG',
          firstName: 'ZONGGUO',
          age: 38,
          nationality: '',
          mobile: '19900066666',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0',
          isRecommend: true,
          fullName: '王宗国',
          birthday: '1985-08-29',
          passengerId: '109637998',
          certificateList: [
            { certificateType: '1', certificateNo: '220283198508297351' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    },
  ];
  mockStateMap.forEach((data: any) => {
    test(
      createInterTestName({
        testId: [490284],
        name: '不带入默认常旅',
      }),
      async () => {
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(User, 'isLogin')
          .mockImplementation(() => new Promise(resolve => resolve(true)));
        jest
          .spyOn(CarFetch, 'queryProductInfo')
          .mockImplementation(() => Promise.resolve(productRes));
        jest.spyOn(GetAB, 'isLicenseApprove').mockImplementation(() => true);
        jest
          .spyOn(CarFetch, 'queryDriverList')
          .mockImplementation(() => Promise.resolve(data));
        const Page = await renderPage();

        const container = await Page.findByTestId(
          UITestID.car_testid_page_booking,
        );
        expect(container).not.toHaveTextContent('王宗国');
      },
    );
  });
});

test(
  createInterTestName({
    testId: [986903],
    name: '驾驶员信息',
  }),
  async () => {
    const driverList = {
      passengerList: [
        {
          nationalityName: '中国',
          lastName: 'WANG',
          firstName: 'ZONGGUO',
          age: 24,
          nationality: 'CA',
          mobile: '19900066666',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0',
          isRecommend: true,
          fullName: '王宗国',
          birthday: '2000-08-29',
          passengerId: '109637998',
          certificateList: [
            { certificateType: '1', certificateNo: '220283198508297351' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    };
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes20231204));
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockImplementation(() => Promise.resolve(licensePolicy20231204));
    AppContext.setABTesting({
      // @ts-ignore
      '231218_DSJT_qccl': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231218_DSJT_qccl',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(driverList));
    const Page = await renderPage();

    await waitRender(async () => {
      const container = await Page.findByTestId(
        UITestID.car_testid_page_booking,
      );
      expect(container).toHaveTextContent('主驾驶人');
      expect(container).toHaveTextContent('WANG/ZONGGUO');
      expect(container).toHaveTextContent('（24 周岁，中国）');
      expect(container).toHaveTextContent('额外收费');
      expect(container).toHaveTextContent(
        '租车公司对21-24周岁将收取“青年驾驶费”；参考价格：USD27（约¥192.94）/天，不包含税，需在线上或门店支付。',
      );
      expect(container).toHaveTextContent('驾照');
      expect(container).toHaveTextContent('中国香港驾照');
      expect(container).toHaveTextContent('驾照组合');
      expect(container).toHaveTextContent('仅需中国香港驾照');
      expect(container).toHaveTextContent('驾龄');
      expect(container).toHaveTextContent('≥12周岁');
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [986896],
    name: '联系信息',
  }),
  async () => {
    const driverList = {
      passengerList: [
        {
          nationalityName: '中国',
          lastName: 'WANG',
          firstName: 'ZONGGUO',
          age: 24,
          nationality: 'CA',
          mobile: '19900066666',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0',
          isRecommend: true,
          fullName: '王宗国',
          birthday: '2000-08-29',
          passengerId: '109637998',
          certificateList: [
            { certificateType: '1', certificateNo: '220283198508297351' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    };
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes20231204));
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockImplementation(() => Promise.resolve(licensePolicy20231204));
    AppContext.setABTesting({
      // @ts-ignore
      '231218_DSJT_qccl': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231218_DSJT_qccl',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(driverList));
    const Page = await renderPage();

    await waitRender(async () => {
      const container = await Page.findByTestId(
        UITestID.car_testid_page_booking,
      );
      expect(container).toHaveTextContent('联系信息');
      expect(container).toHaveTextContent('驾驶员手机');
      expect(container).toHaveTextContent('+86');
      expect(container).toHaveTextContent('驾驶员邮箱');
      expect(container).toHaveTextContent('航班号 (可选)');
      expect(container).toHaveTextContent('如中转，请填写末班航班的信息');
      expect(container).toHaveTextContent('航班延误保留政策');
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [986910],
    name: '进入填写页',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes20231204));
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockImplementation(() => Promise.resolve(licensePolicy20231204));
    AppContext.setABTesting({
      // @ts-ignore
      '231218_DSJT_qccl': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231218_DSJT_qccl',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(driverListRes20231204));
    const Page = await renderPage();

    await waitRender(async () => {
      const container = await Page.findByTestId(
        UITestID.car_testid_page_booking,
      );
      expect(container).toHaveTextContent('CAO/CAO');
      expect(container).toHaveTextContent('（39 周岁，中国）');
      expect(container).toHaveTextContent('中国香港驾照');
    });
  },
  TEST_TIMEOUT,
);

describe('详情页优化二期-不带入默认常旅', () => {
  const mockStateMap = [
    {
      passengerList: [
        {
          nationalityName: '中国',
          lastName: '',
          firstName: '',
          age: 39,
          nationality: 'CN',
          mobile: '13817320202',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:-1',
          isRecommend: true,
          fullName: '曹操',
          birthday: '1984-11-01',
          passengerId: '986',
          certificateList: [
            { certificateType: '1', certificateNo: '******************' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    },
    {
      passengerList: [
        {
          nationalityName: '中国',
          lastName: 'CAO',
          firstName: 'CAO',
          age: 0,
          nationality: 'CN',
          mobile: '13817320202',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:-1',
          isRecommend: true,
          fullName: '曹操',
          birthday: '1984-11-01',
          passengerId: '986',
          certificateList: [
            { certificateType: '1', certificateNo: '******************' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    },
    {
      passengerList: [
        {
          nationalityName: '中国',
          lastName: 'CAO',
          firstName: 'CAO',
          age: 39,
          nationality: '',
          mobile: '13817320202',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:-1',
          isRecommend: true,
          fullName: '曹操',
          birthday: '1984-11-01',
          passengerId: '986',
          certificateList: [
            { certificateType: '1', certificateNo: '******************' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    },
  ];
  mockStateMap.forEach((data: any) => {
    test(
      createInterTestName({
        testId: [986917],
        name: '默认常旅姓名/年龄/国籍为空',
      }),
      async () => {
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(User, 'isLogin')
          .mockImplementation(() => new Promise(resolve => resolve(true)));
        jest
          .spyOn(CarFetch, 'queryProductInfo')
          .mockImplementation(() => Promise.resolve(productRes20231204));
        jest
          .spyOn(CarFetch, 'queryLicencePolicy')
          .mockImplementation(() => Promise.resolve(licensePolicy20231204));
        AppContext.setABTesting({
          // @ts-ignore
          '231218_DSJT_qccl': {
            State: true,
            ExpVersion: 'C',
            ExpCode: '231218_DSJT_qccl',
            ExpDefaultVersion: '',
            BeginTime: '',
            ExpResult:
              'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
            End: '',
            EndTime: '',
            Attrs: {},
          },
        });
        jest
          .spyOn(CarFetch, 'queryDriverList')
          .mockImplementation(() => Promise.resolve(data));
        const Page = await renderPage();

        await waitRender(async () => {
          const container = await Page.findByTestId(
            UITestID.car_testid_page_booking,
          );
          expect(container).not.toHaveTextContent('CAO/CAO');
        });
      },
    );
  });
});

test(
  createInterTestName({
    testId: [986924],
    name: '默认常旅年龄不符合供应商要求',
  }),
  async () => {
    const data = {
      passengerList: [
        {
          nationalityName: '中国',
          lastName: 'CAO',
          firstName: 'CAO',
          age: 16,
          nationality: 'CN',
          mobile: '13817320202',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:-1',
          isRecommend: true,
          fullName: '曹操',
          birthday: '1984-11-01',
          passengerId: '986',
          certificateList: [
            { certificateType: '1', certificateNo: '******************' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    };
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes20231204));
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockImplementation(() => Promise.resolve(licensePolicy20231204));
    AppContext.setABTesting({
      // @ts-ignore
      '231218_DSJT_qccl': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231218_DSJT_qccl',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(data));
    const Page = await renderPage();

    await waitRender(async () => {
      const container = await Page.findByTestId(
        UITestID.car_testid_page_booking,
      );
      expect(container).not.toHaveTextContent('CAO/CAO');
    });
  },
);

test(
  createInterTestName({
    testId: [986931],
    name: '填写页仅做回显，不能修改',
  }),
  async () => {
    const data = {
      passengerList: [
        {
          nationalityName: '中国澳门',
          lastName: 'CAO',
          firstName: 'CAO',
          age: 38,
          nationality: 'CN',
          mobile: '13817320202',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:-1',
          isRecommend: true,
          fullName: '曹操',
          birthday: '1984-11-01',
          passengerId: '986',
          certificateList: [
            { certificateType: '1', certificateNo: '******************' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    };
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes20231204));
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockImplementation(() => Promise.resolve(licensePolicy20231204));
    AppContext.setABTesting({
      // @ts-ignore
      '231218_DSJT_qccl': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231218_DSJT_qccl',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(data));
    const Page = await renderPage();

    await waitRender(async () => {
      const container = await Page.findByTestId(
        UITestID.car_testid_page_booking,
      );
      expect(container).toHaveTextContent('CAO/CAO');
      expect(container).toHaveTextContent('中国澳门');
    });
  },
);

describe('默认回填', () => {
  const mockStateMap = [
    {
      licensePolicyRes: licensePolicy20231204,
      expected: '中国香港驾照',
    },
    {
      licensePolicyRes: licensePolicy20231205,
      expected: '驾照供应商不支持中国大陆驾照租车，建议更换驾驶员',
    },
    {
      licensePolicyRes: licensePolicy20231206,
      expected: '选择驾照',
    },
  ];
  mockStateMap.forEach((data: any) => {
    test(
      createInterTestName({
        testId: [986938],
        name: '默认回填',
      }),
      async () => {
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
        jest
          .spyOn(User, 'isLogin')
          .mockImplementation(() => new Promise(resolve => resolve(true)));
        jest
          .spyOn(CarFetch, 'queryProductInfo')
          .mockImplementation(() => Promise.resolve(productRes20231204));
        jest
          .spyOn(CarFetch, 'queryLicencePolicy')
          .mockImplementation(() => Promise.resolve(data.licensePolicyRes));
        AppContext.setABTesting({
          // @ts-ignore
          '231218_DSJT_qccl': {
            State: true,
            ExpVersion: 'C',
            ExpCode: '231218_DSJT_qccl',
            ExpDefaultVersion: '',
            BeginTime: '',
            ExpResult:
              'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
            End: '',
            EndTime: '',
            Attrs: {},
          },
        });
        jest
          .spyOn(CarFetch, 'queryDriverList')
          .mockImplementation(() => Promise.resolve(driverListRes20231204));
        const Page = await renderPage();

        const container = await Page.findByTestId(
          UITestID.car_testid_page_booking,
        );
        expect(container).toHaveTextContent(data.expected);
      },
    );
  });
});

test(
  createInterTestName({
    testId: [986973],
    name: '供应商不支持的驾照（特殊配置）',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes20231204));
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockImplementation(() => Promise.resolve(licensePolicy20231205));
    AppContext.setABTesting({
      // @ts-ignore
      '231218_DSJT_qccl': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231218_DSJT_qccl',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(driverListRes20231204));
    const Page = await renderPage();

    const container = await Page.findByTestId(UITestID.car_testid_page_booking);
    expect(container).toHaveTextContent(
      '照供应商不支持中国大陆驾照租车，建议更换驾驶员',
    );
  },
);

test(
  createInterTestName({
    testId: [986980],
    name: '低/高龄驾驶员收费标准',
  }),
  async () => {
    const driverList = {
      passengerList: [
        {
          nationalityName: '中国澳门',
          lastName: 'CAO',
          firstName: 'CAO',
          age: 16,
          nationality: 'CN',
          mobile: '13817320202',
          countryCode: '86',
          isDefault: true,
          isSelf: false,
          sortExt:
            'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:-1',
          isRecommend: true,
          fullName: '曹操',
          birthday: '1984-11-01',
          passengerId: '986',
          certificateList: [
            { certificateType: '1', certificateNo: '******************' },
          ],
          email: '',
          isCreditQualified: false,
        },
      ],
    };
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes20231204));
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockImplementation(() => Promise.resolve(licensePolicy20231205));
    AppContext.setABTesting({
      // @ts-ignore
      '231218_DSJT_qccl': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231218_DSJT_qccl',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(driverList));
    const Page = await renderPage();
    await waitRender(async () => {
      const container = await Page.findByTestId(
        UITestID.car_testid_page_booking,
      );
      expect(container).toHaveTextContent('取车要求');
      expect(container).toHaveTextContent(
        '18-65周岁驾驶员驾龄要求≥12周岁21-24周岁将收取“青年驾驶费”；参考价格：USD27(约196.71)/天，不包含税，需在线上或门店支付',
      );
      expect(container).toHaveTextContent(
        '65-75周岁将收取“老年驾驶费”；参考价格：USD27(约¥196.71)/天，不包含税，需在线上或门店支付。',
      );
    });
  },
);

test(
  createInterTestName({
    testId: [986959],
    name: '驾照要求',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes20231204));
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockImplementation(() => Promise.resolve(licensePolicy20231204));
    AppContext.setABTesting({
      // @ts-ignore
      '231218_DSJT_qccl': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231218_DSJT_qccl',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(driverListRes20231204));
    const Page = await renderPage();

    const container = await Page.findByTestId(UITestID.car_testid_page_booking);
    expect(container).toHaveTextContent('驾照组合');
    expect(container).toHaveTextContent('仅需中国香港驾照');
  },
);

test(
  createInterTestName({
    testId: [986966],
    name: '驾龄要求',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes20231204));
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockImplementation(() => Promise.resolve(licensePolicy20231204));
    AppContext.setABTesting({
      // @ts-ignore
      '231218_DSJT_qccl': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231218_DSJT_qccl',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(driverListRes20231204));
    const Page = await renderPage();

    const container = await Page.findByTestId(UITestID.car_testid_page_booking);
    expect(container).toHaveTextContent('驾龄');
    expect(container).toHaveTextContent('≥12周岁');
  },
);

test(
  createInterTestName({
    testId: [986987],
    name: '展示顺序 收费标准>驾照要求>驾龄要求',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryProductInfo')
      .mockImplementation(() => Promise.resolve(productRes20231204));
    jest
      .spyOn(CarFetch, 'queryLicencePolicy')
      .mockImplementation(() => Promise.resolve(licensePolicy20231204));
    AppContext.setABTesting({
      // @ts-ignore
      '231218_DSJT_qccl': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231218_DSJT_qccl',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231218_DSJT_qccl:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryDriverList')
      .mockImplementation(() => Promise.resolve(driverListRes20231204));
    const Page = await renderPage();

    const container = await Page.findByTestId(UITestID.car_testid_page_booking);
    expect(container).toHaveTextContent('驾照组合仅需中国香港驾照驾龄≥12周岁');
  },
);
