/**
 * 订单详情页新增供应商多语言合同模板
 */
import { AppContext, Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { waitRender } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';

const orderRes3232944 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3232944.json'); // 已完成
const orderRes3232951 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3232951.json'); // 已完成
const orderRes3232860 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3232860.json'); // 已确认
const orderRes3232958 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3232958.json'); // 用车中
const orderRes3232776 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3232776.json'); // 已取消

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    saveIsd: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  jest
    .spyOn(CarStorage, 'load')
    .mockImplementation(() => Promise.resolve('[]'));
  jest
    .spyOn(CarStorage, 'loadAsync')
    .mockImplementation(() => Promise.resolve('[]'));
  return createPage(container, { name: 1 }, initialState);
};
// 履约卡版不露出
describe('供应商多语言合同模板-模板展示入口', () => {
  test(
    createInterTestName({
      testId: [3232776],
      name: '已取消',
    }),
    async () => {
      // jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      // jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      // jest
      //   .spyOn(CarFetch, 'queryOrder')
      //   .mockImplementation(() => Promise.resolve(orderRes3232776));
      // const Page = await renderPage();
      // const tipsCardOsdWrap = await Page.findByTestId(
      //   UITestID.c_testid_orderDetail_tipsCardOsd,
      // );
      // const tipsCardOsdBtns = await Page.findByTestId(
      //   UITestID.c_testid_orderDetail_tipsCardOsd_btns,
      // );
      // await waitRender(async () => {
      //   expect(tipsCardOsdWrap).toBeTruthy();
      //   expect(tipsCardOsdBtns).not.toHaveTextContent('中英合同/验车单参照');
      // });
    },
    TEST_TIMEOUT,
  );

  //   test(
  //     createInterTestName({
  //       testId: [
  //         3232860, 3232874, 3232853, 3232867, 3232937, 3232923, 3232930, 3232769,
  //       ],
  //       name: '已确认',
  //     }),
  //     async () => {
  //       jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
  //       jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
  //       jest
  //         .spyOn(CarFetch, 'queryOrder')
  //         .mockImplementation(() => Promise.resolve(orderRes3232860));
  //       const Page = await renderPage();
  //       const tipsCardOsdWrap = await Page.findByTestId(
  //         UITestID.c_testid_orderDetail_tipsCardOsd,
  //       );
  //       await waitRender(async () => {
  //         expect(tipsCardOsdWrap).toBeTruthy();
  //         expect(tipsCardOsdWrap).toHaveTextContent('中英合同/验车单参照');
  //       });
  //     },
  //     TEST_TIMEOUT,
  //   );

  //   test(
  //     createInterTestName({
  //       testId: [3232958, 3232769],
  //       name: '用车中',
  //     }),
  //     async () => {
  //       jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
  //       jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
  //       jest
  //         .spyOn(CarFetch, 'queryOrder')
  //         .mockImplementation(() => Promise.resolve(orderRes3232958));
  //       const Page = await renderPage();
  //       const tipsCardOsdWrap = await Page.findByTestId(
  //         UITestID.c_testid_orderDetail_tipsCardOsd,
  //       );
  //       // 3232958 仅有取车合同-中英合同参照
  //       // 3232769 用车中露出
  //       await waitRender(async () => {
  //         expect(tipsCardOsdWrap).toBeTruthy();
  //         expect(tipsCardOsdWrap).toHaveTextContent('中英合同参照');
  //       });
  //     },
  //     TEST_TIMEOUT,
  //   );

  //   test(
  //     createInterTestName({
  //       testId: [3232944, 3232769],
  //       name: '已完成',
  //     }),
  //     async () => {
  //       jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
  //       jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
  //       jest
  //         .spyOn(CarFetch, 'queryOrder')
  //         .mockImplementation(() => Promise.resolve(orderRes3232944));
  //       const Page = await renderPage();
  //       const tipsCardOsdWrap = await Page.findByTestId(
  //         UITestID.c_testid_orderDetail_tipsCardOsd,
  //       );
  //       const tipsCardOsdBtns = await Page.findByTestId(
  //         UITestID.c_testid_orderDetail_tipsCardOsd_btns,
  //       );

  //       await waitRender(async () => {
  //         expect(tipsCardOsdWrap).toBeTruthy();
  //         // 仅有验车单-中英验车单参照
  //         expect(tipsCardOsdBtns).toHaveTextContent('中英验车单参照');
  //       });
  //     },
  //     TEST_TIMEOUT,
  //   );
  //   test(
  //     createInterTestName({
  //       testId: [3232951],
  //       name: '两者都没有-不展示',
  //     }),
  //     async () => {
  //       jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
  //       jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
  //       jest
  //         .spyOn(CarFetch, 'queryOrder')
  //         .mockImplementation(() => Promise.resolve(orderRes3232951));
  //       const Page = await renderPage();
  //       const tipsCardOsdWrap = await Page.findByTestId(
  //         UITestID.c_testid_orderDetail_tipsCardOsd,
  //       );
  //       const tipsCardOsdBtns = await Page.findByTestId(
  //         UITestID.c_testid_orderDetail_tipsCardOsd_btns,
  //       );

  //       await waitRender(async () => {
  //         expect(tipsCardOsdWrap).toBeTruthy();
  //         expect(tipsCardOsdBtns).not.toHaveTextContent('中英验车单参照');
  //       });
  //     },
  //     TEST_TIMEOUT,
  //   );
});
