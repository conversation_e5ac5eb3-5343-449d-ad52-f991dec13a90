import { act, fireEvent } from '@testing-library/react-native';
import { TouchableOpacity } from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { AppContext, Utils, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { delayPromise } from '../testHelpers';

const res20240926 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/20240926.json');

// @ts-ignore 禁止Redux Log
// __DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '36510166399',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { name: 1 }, initialState);
};

// 已下线
describe.skip('取车要求及材料模块', () => {
  test(
    createInterTestName({
      testId: [986322, 4068534],
      name: '护照',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(res20240926));
        const Page = await renderPage(state);

        // const tipsCard = await Page.findByTestId(
        //   UITestID.c_testid_orderDetail_tipsCardOsd,
        // );
        // const tipsCardBtns = await tipsCard.findAllByType(TouchableOpacity);

        // await delayPromise();

        // // 点击取车材料
        // await act(() => {
        //   fireEvent.press(tipsCardBtns[0]);
        // });

        // const materialsModal = await Page.findByTestId(
        //   UITestID.car_testid_page_materials,
        // );

        // expect(materialsModal).toBeTruthy();
        // expect(materialsModal).toHaveTextContent('中国澳门护照原件');
        // expect(materialsModal).toHaveTextContent(
        //   '主驾驶员和其他额外驾驶员需提供他们名下的护照原件',
        // );
        // expect(materialsModal).toHaveTextContent(
        //   '照签发地需与驾照发证国家/地区一致',
        // );
      });
    },
  );

  test(
    createInterTestName({
      testId: [986343, 986350, 986357, 986378, 986399, 4068555],
      name: '驾照组合',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(res20240926));
        const Page = await renderPage(state);

        // const tipsCard = await Page.findByTestId(
        //   UITestID.c_testid_orderDetail_tipsCardOsd,
        // );
        // const tipsCardBtns = await tipsCard.findAllByType(TouchableOpacity);

        // await delayPromise();

        // // 点击取车材料
        // await act(() => {
        //   fireEvent.press(tipsCardBtns[0]);
        // });

        // const materialsModal = await Page.findByTestId(
        //   UITestID.car_testid_page_materials,
        // );

        // expect(materialsModal).toBeTruthy();
        // expect(materialsModal).toHaveTextContent('驾照组合');
        // expect(materialsModal).toHaveTextContent('门店支持以下驾照组合');
        // expect(materialsModal).toHaveTextContent('澳门驾照 + 国际驾照');
        // expect(materialsModal).toHaveTextContent(
        //   '澳门驾照：由中国澳门特别行政区颁发的驾照',
        // );
        // expect(materialsModal).toHaveTextContent(
        //   '国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）',
        // );
      });
    },
  );

  test(
    createInterTestName({
      testId: [986490],
      name: '信用卡模块 支付方式仅现金',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(res20240926));
        const Page = await renderPage(state);

        // const tipsCard = await Page.findByTestId(
        //   UITestID.c_testid_orderDetail_tipsCardOsd,
        // );
        // const tipsCardBtns = await tipsCard.findAllByType(TouchableOpacity);

        // await delayPromise();

        // // 点击取车材料
        // await act(() => {
        //   fireEvent.press(tipsCardBtns[0]);
        // });

        // const materialsModal = await Page.findByTestId(
        //   UITestID.car_testid_page_materials,
        // );

        // expect(materialsModal).toBeTruthy();
        // expect(materialsModal).not.toHaveTextContent('不支持展示 支付宝/微信');
      });
    },
  );

  test(
    createInterTestName({
      testId: [
        986504, 986511, 986539, 986525, 986532, 986560, 986546, 986546, 986567,
        986574,
      ],
      name: '押金模块',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(res20240926));
        const Page = await renderPage(state);

        // const tipsCard = await Page.findByTestId(
        //   UITestID.c_testid_orderDetail_tipsCardOsd,
        // );
        // const tipsCardBtns = await tipsCard.findAllByType(TouchableOpacity);

        // await delayPromise();

        // // 点击取车材料
        // await act(() => {
        //   fireEvent.press(tipsCardBtns[0]);
        // });

        // const materialsModal = await Page.findByTestId(
        //   UITestID.car_testid_page_materials,
        // );

        // expect(materialsModal).toBeTruthy();
        // expect(materialsModal).toHaveTextContent('押金');
        // expect(materialsModal).toHaveTextContent('到店刷取押金');
        // expect(materialsModal).toHaveTextContent('该门店不收取押金');
        // expect(materialsModal).toHaveTextContent(
        //   '预估押金：{最小押金金额}-{最大押金金额}（约￥xxx-￥xxx）',
        // );
        // expect(materialsModal).toHaveTextContent('到店刷取押金预授权');
        // expect(materialsModal).toHaveTextContent(
        //   '到店刷取押金预授权，押金约USD500-USD1,000（约¥3,512-¥7,025），还车后30-60天内退还。',
        // );
      });
    },
  );

  test(
    createInterTestName({
      testId: [986609],
      name: '（支付方式为信用卡+现金）',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(res20240926));
        const Page = await renderPage(state);

        // const tipsCard = await Page.findByTestId(
        //   UITestID.c_testid_orderDetail_tipsCardOsd,
        // );
        // const tipsCardBtns = await tipsCard.findAllByType(TouchableOpacity);

        // await delayPromise();

        // // 点击取车材料
        // await act(() => {
        //   fireEvent.press(tipsCardBtns[0]);
        // });

        // const materialsModal = await Page.findByTestId(
        //   UITestID.car_testid_page_materials,
        // );

        // expect(materialsModal).toBeTruthy();
        // expect(materialsModal).toHaveTextContent(
        //   '如您无法提供信用卡，该门店也支持现金支付押金',
        // );
      });
    },
  );

  test(
    createInterTestName({
      testId: [986623],
      name: '其它材料',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(res20240926));
        const Page = await renderPage(state);

        // const tipsCard = await Page.findByTestId(
        //   UITestID.c_testid_orderDetail_tipsCardOsd,
        // );
        // const tipsCardBtns = await tipsCard.findAllByType(TouchableOpacity);

        // await delayPromise();

        // // 点击取车材料
        // await act(() => {
        //   fireEvent.press(tipsCardBtns[0]);
        // });

        // const materialsModal = await Page.findByTestId(
        //   UITestID.car_testid_page_materials,
        // );

        // expect(materialsModal).toBeTruthy();
        // expect(materialsModal).toHaveTextContent(
        //   '按门店要求，取车时需提供机票。',
        // );
      });
    },
  );

  test(
    createInterTestName({
      testId: [4068541],
      name: '标题栏',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(res20240926));
        const Page = await renderPage(state);

        // const tipsCard = await Page.findByTestId(
        //   UITestID.c_testid_orderDetail_tipsCardOsd,
        // );
        // const tipsCardBtns = await tipsCard.findAllByType(TouchableOpacity);

        // await delayPromise();

        // // 点击取车材料
        // await act(() => {
        //   fireEvent.press(tipsCardBtns[0]);
        // });

        // const materialsModal = await Page.findByTestId(
        //   UITestID.car_testid_page_materials,
        // );

        // expect(materialsModal).toBeTruthy();
        // expect(materialsModal).toHaveTextContent('取车要求及材料');

        // const btns = await materialsModal.findAllByType(TouchableOpacity);
        // await fireEvent(btns[0], 'onPress');
      });
    },
  );

  test(
    createInterTestName({
      testId: [4068625],
      name: '取车材料页面',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

        jest
          .spyOn(CarFetch, 'queryOrder')
          .mockImplementation(() => Promise.resolve(res20240926));
        const Page = await renderPage(state);

        // const tipsCard = await Page.findByTestId(
        //   UITestID.c_testid_orderDetail_tipsCardOsd,
        // );
        // const tipsCardBtns = await tipsCard.findAllByType(TouchableOpacity);

        // await delayPromise();

        // // 点击取车材料
        // await act(() => {
        //   fireEvent.press(tipsCardBtns[0]);
        // });

        // const materialsModal = await Page.findByTestId(
        //   UITestID.car_testid_page_materials,
        // );

        // expect(materialsModal).toBeTruthy();
        // expect(materialsModal).toHaveTextContent('取车要求及材料');
      });
    },
  );
});
