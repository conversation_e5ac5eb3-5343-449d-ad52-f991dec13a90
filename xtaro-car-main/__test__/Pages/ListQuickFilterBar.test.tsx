import { TouchableOpacity, StyleSheet } from 'react-native';
import { fireEvent, act } from '@testing-library/react-native';
import { Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));

const res230506 = require('../../__mocks__/restful/18631/queryProducts/230506.json');
// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: true,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

test(
  createInterTestName({
    testId: [230506, 230507],
    name: '列表页快筛规则',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();
    const quickFilterBar = await Page.findByTestId(
      UITestID.car_testid_comp_quick_filter_bar,
    );

    const filterItems = await quickFilterBar.findAllByType(TouchableOpacity);

    // 点击送车上门筛选
    await act(() => {
      fireEvent(filterItems[4], 'onPress');
    });

    const btnSelectStyle = StyleSheet.flatten(filterItems[4].props.style);
    expect(btnSelectStyle.backgroundColor).toEqual('#E6F3FE');

    // 取消送车上门筛选
    await act(() => {
      fireEvent(filterItems[4], 'onPress');
    });
    const btnUnSelectStyle = StyleSheet.flatten(filterItems[4].props.style);
    expect(btnUnSelectStyle.backgroundColor).toEqual('#f4f4f4');
  },
);
test(
  createInterTestName({
    testId: [769973, 769966],
    name: '自助取还快筛项出现逻辑',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(res230506));
    const Page = await renderPage();
    const quickFilterBar = await Page.findByTestId(
      UITestID.car_testid_comp_quick_filter_bar,
    );
    expect(quickFilterBar).toHaveTextContent(
      '送车上门押金双免半年内车龄自助取还倒车影像自动挡4.8分以上7座携程优选',
    );
  },
);
