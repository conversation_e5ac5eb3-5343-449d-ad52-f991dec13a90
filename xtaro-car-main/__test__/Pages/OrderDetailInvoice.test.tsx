import { act, waitFor, fireEvent } from '@testing-library/react-native';
import URL from '@c2x/apis/URL';
import { AppContext, Utils, CarStorage } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import UITestId from '../../src/pages/xcar/Constants/UITestID';

// @ts-ignore 禁止Redux Log
// __DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '36510',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { name: 1 }, initialState);
};

describe('订详页-发票', () => {
  test(
    createInterTestName({
      testId: [408028, 408023],
      name: '发票模块',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 407985,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);
        // 页面展示 408028
        await waitFor(() => {
          expect(Page.container).toHaveTextContent(
            '如需发票，请在还车时联系门店索取',
          );
        });
        // 发票模块露出逻辑 408023
        await waitFor(() => {
          const invoiceModule = Page.getByTestId(
            UITestId.car_testid_page_order_detail_invoice,
          );
          expect(invoiceModule).toBeTruthy();
        });
      });
    },
    TEST_TIMEOUT,
  );
});

// 国内开票线上化项目
describe('订详页-发票', () => {
  test(
    createInterTestName({
      testId: [
        3234036, 3234043, 3234071, 3234050, 3234092, 3234099, 3234085, 3234113,
        3234078, 3234064, 3234120, 3234106, 3234155,
      ],
      name: '订详一级页发票模块',
    }),
    async () => {
      await act(async () => {
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage();
        // 不露出开票按钮
        await waitFor(() => {
          expect(Page.container).not.toHaveTextContent('我要发票');
        });
        await waitFor(() => {
          expect(Page.container).not.toHaveTextContent(
            '订单已超过还车后1年，无法申请开票',
          );
        });
      });
    },
    TEST_TIMEOUT,
  );
});

describe('订详页-发票', () => {
  test(
    createInterTestName({
      testId: [3234148],
      name: '订详一级页发票模块',
    }),
    async () => {
      await act(async () => {
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage();
        await waitFor(() => {
          expect(Page.container).toHaveTextContent('我要发票');
        });
        await waitFor(() => {
          const invoiceButton = Page.queryByTestId(
            UITestId.c_testid_orderDetail_invoice_button,
          );
          fireEvent.press(invoiceButton);
          const openURL = jest.spyOn(URL, 'openURL');
          expect(openURL).toBeCalledWith(
            '/rn_xtaro_car_order/main.js?CRNType=1&CRNModuleName=rn_xtaro_car_order&initialPage=editInvoice&orderId=36510',
          );
        });
      });
    },
    TEST_TIMEOUT,
  );
});

describe('订详页-发票', () => {
  test(
    createInterTestName({
      testId: [3234127],
      name: '订详一级页发票模块',
    }),
    async () => {
      await act(async () => {
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage();
        await waitFor(() => {
          expect(Page.container).toHaveTextContent('修改发票');
        });
        await waitFor(() => {
          const invoiceButton = Page.queryByTestId(
            UITestId.c_testid_orderDetail_invoice_button,
          );
          fireEvent.press(invoiceButton);
          const openURL = jest.spyOn(URL, 'openURL');
          expect(openURL).toBeCalledWith(
            '/rn_xtaro_car_order/main.js?CRNType=1&CRNModuleName=rn_xtaro_car_order&initialPage=editInvoice&orderId=36510',
          );
        });
      });
    },
    TEST_TIMEOUT,
  );
});

describe('订详页-发票', () => {
  test(
    createInterTestName({
      testId: [3234134, 3234141, 3234162],
      name: '订详一级页发票模块',
    }),
    async () => {
      await act(async () => {
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage();
        await waitFor(() => {
          expect(Page.container).toHaveTextContent('查看发票');
        });
        await waitFor(() => {
          const invoiceButton = Page.queryByTestId(
            UITestId.c_testid_orderDetail_invoice_button,
          );
          fireEvent.press(invoiceButton);
          const openURL = jest.spyOn(URL, 'openURL');
          expect(openURL).toBeCalledWith(
            '/rn_xtaro_car_order/main.js?CRNType=1&CRNModuleName=rn_xtaro_car_order&initialPage=invoiceDetail&orderId=36510',
          );
        });
      });
    },
    TEST_TIMEOUT,
  );
});

describe('订详页-发票', () => {
  test(
    createInterTestName({
      testId: [3234176, 3234169],
      name: '订详一级页发票模块',
    }),
    async () => {
      await act(async () => {
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage();
        await waitFor(() => {
          expect(Page.container).toHaveTextContent(
            '如有租车相关费用发票的需求，请在还车后联系门店。超级会员发票请联系携程客服。',
          );
        });
      });
    },
    TEST_TIMEOUT,
  );
});
