import { fireEvent, waitFor, within, act } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';
import { AppContext, Utils, GetAB, User } from '../../src/pages/xcar/Util/Index';
import * as Payment from '../../src/pages/xcar/Util/Payment/Index';
import {
  createPage,
  createInterTestName,
  updateReduxState,
} from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { renderWithAction, waitRender } from '../testHelpers';
import CarFetch from '../../src/pages/xcar/Util/CarFetch';
// @ts-ignore 禁止Redux Log
__DEV__ = false;

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Util/CarABTesting/GetAB', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/CarABTesting/GetAB'),
}));
// Apptype OSD
jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
jest
  .spyOn(User, 'isLogin')
  .mockImplementation(() => new Promise(resolve => resolve(true)));

// 页面初始化
const renderPage = async (initialState = {}, props = {}) => {
  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, props, initialState);
};

const renderDriverEditPage = async (initialState = {}) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/DriverEditContainer').default;
  return createPage(container, {}, initialState);
};

test(
  createInterTestName({
    testId: [
      451616, 451614, 451613, 451612, 451611, 451615, 451607, 451609, 451608,
      451602, 451604, 451610, 451605, 451601, 451603, 451599, 451597, 451596,
      490310, 490313, 490309,
    ],
    name: '个人信息隐私政策 - 授权开关 - 开',
  }),
  async () => {
    let BookingPage;
    await act(async () => {
      BookingPage = await renderPage({
        curDepositPayInfo: {
          depositPayType: 5,
          isEnable: false,
          isCheck: true,
          isClickable: false,
          depositTypeInfo: {
            title: {
              contentStyle: '2',
              stringObjs: [
                {
                  content: '您信用良好，可免押金',
                  style: '2',
                },
              ],
            },
          },
          sortNum: 1,
        },
      });
    });

    const DriverEdit = await renderDriverEditPage();

    //
    await waitRender(() => {
      expect(BookingPage.container).toHaveTextContent('我已阅读并同意');
      expect(BookingPage.container).toHaveTextContent('个人信息授权声明');
      expect(DriverEdit.container).toHaveTextContent(
        '请您仔细阅读并理解以下内容',
      );
    });
    await renderWithAction({
      action: async () => {
        updateReduxState(draft => {
          draft.DriverList.passenger = {
            nationalityName: '中国',
            lastName: 'ctrip',
            firstName: 'test',
            age: 29,
            nationality: 'CN',
            mobile: '15500010002',
            countryCode: '86',
            isDefault: true,
            isSelf: false,
            sortExt:
              'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:-1689066029749|selfSort:0|requestPassenger:-1',
            isRecommend: true,
            fullName: '张三',
            birthday: '1994-02-28',
            passengerId: '269614',
            certificateList: [
              { certificateType: '1', certificateNo: '******************' },
              { certificateType: '7', certificateNo: 'W22334D2221' },
            ],
            email: '',
            isCreditQualified: false,
          };

          draft.Booking.driverInfo = [
            { type: 'firstName', value: 'test', error: false },
            { type: 'lastName', value: 'ctrip', error: false },
            { type: 'mobilePhone', value: '15500010002', error: false },
            { type: 'email', value: '<EMAIL>', error: false },
            { type: 'wechat', value: '', error: false },
            { type: 'flightNumber', value: '**********', error: false },
            { type: 'driverLicense', value: 'CN', error: false },
            { type: 'driverLicenseName', value: '中国大陆驾照', error: false },
            { type: 'areaCode', value: '86', error: false },
          ];
        });
        const payBtns = await BookingPage.findAllByTestId(
          UITestID.car_testid_page_booking_footer_button,
        );
        fireEvent.press(payBtns[0]);
      },
      expect: () => {
        expect(BookingPage.container).toHaveTextContent('请确认以下信息');
      },
    });
    await renderWithAction({
      action: async () => {
        const noticeBtns = await BookingPage.findAllByText('个人信息授权声明');
        fireEvent.press(noticeBtns[1]);
      },
      expect: () => {
        expect(BookingPage.container).toHaveTextContent('同意并预订');
      },
    });

    const createOrder = jest.spyOn(CarFetch, 'createOrder');

    await renderWithAction({
      action: async () => {
        const noticeBtns = await BookingPage.findAllByText('同意并预订');
        fireEvent.press(noticeBtns[1]);
      },
      expect: () => {
        expect(createOrder).toBeCalled();
      },
    });
    // 451602 填写页底部无政策说明
    expect(BookingPage.container).not.toHaveTextContent('政策说明');
  },
);

test(
  createInterTestName({
    testId: [451617],
    name: '个人信息隐私政策 - 授权开关 - 关',
  }),
  async () => {
    const BookingPage = await renderPage();
    const DriverEdit = await renderDriverEditPage();
    await waitRender(() => {
      expect(BookingPage.container).toHaveTextContent('个人信息授权声明');
      expect(DriverEdit.container).toHaveTextContent('已阅读并同意以下内容');
    });
  },
);
