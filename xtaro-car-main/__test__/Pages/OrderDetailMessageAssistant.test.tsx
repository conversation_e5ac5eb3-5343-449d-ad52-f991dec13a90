import { fireEvent, waitFor, act } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import { Utils, AppContext, CarStorage, User } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import OrderDetail from '../../src/pages/xcar/Containers/OrderDetailContainer';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
  jest.spyOn(User, 'isLoginSync').mockReturnValue(true);
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'OrderDetail',
  });

  return createPage(OrderDetail, {}, initialState);
};
const renderMessageAssistantPage = (initialState = undefined) => {
  const MessageAssistantPage =
    require('../../src/pages/xcar/Containers/MessageAssistantContainer').default;
  return createPage(MessageAssistantPage, {}, initialState);
};
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    saveIsd: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
test(
  createInterTestName({
    testId: [407952, 407958],
    name: '消息助手',
  }),
  async () => {
    const state = {
      OrderDetail: {
        reqOrderParams: {
          orderId: 1234567,
        },
        storageCardsTitle: [],
      },
    };
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
    const Page = await renderPage(state);
    // const messageAssistantText = await Page.findByText('查看消息助手');
    // expect(messageAssistantText).toBeTruthy();
    // const messageAssistant = await Page.findByTestId(
    //   UITestID.car_testid_page_order_status_message_assistant,
    // );
    //	点击【消息助手】 预期：弹出“消息助手页”
    // await act(async () => {
    //   fireEvent(messageAssistant, 'click');
    //   expect(pagePushFunc).toHaveBeenNthCalledWith(1, 'MessageAssistant');
    // });
    // const MessageAssistantPage = await renderMessageAssistantPage();
    // const messageCardsTitle = await MessageAssistantPage.findByText('历史消息');
    // expect(messageCardsTitle).toBeTruthy();
  },
  TEST_TIMEOUT,
);
