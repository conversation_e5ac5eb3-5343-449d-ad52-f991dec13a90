/* eslint-disable @typescript-eslint/naming-convention */
import { TouchableOpacity, Image } from 'react-native';
import { fireEvent, act } from '@testing-library/react-native';
import { BbkConstants } from '../../src/pages/xcar/Common/src/Utils';
import { Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { waitRender } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';

const res440463 = require('../../__mocks__/restful/18631/queryProducts/OSD/440463.json');
const res440463_201 = require('../../__mocks__/restful/18631/queryProducts/OSD/440463-201.json');
const res440424 = require('../../__mocks__/restful/18631/queryProducts/OSD/440424.json');

afterEach(() => {
  jest.clearAllMocks();
})

// @ts-ignore 禁止Redux Log
__DEV__ = false;
jest.mock('uuid', () => {
  return jest.fn().mockImplementation(() => {
    return '123456789';
  });
});
// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: false,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

test(
  createInterTestName({
    testId: [440463, 440464, 440465, 440467, 440470, 455732],
    name: '车型信息',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementationOnce(() => Promise.resolve(res440463_201));
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementationOnce(() => Promise.resolve(res440463));
    const Page = await renderPage();

    await waitRender(async () => {
      const vehicleList = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );

      const firstVehicle = vehicleList[0];
      const firstVehicleImage = await firstVehicle.findAllByType(Image);
      // 展示车图
      expect(firstVehicleImage[0].props.source.uri).toEqual(
        `https://pic.c-ctrip.com/car/osd/online/vehicle_new/Chevrolet_Spark_4_Doors.png?timestamps=${BbkConstants.timestamps}`,
      );
      expect(firstVehicle).toHaveTextContent('雪佛兰 Spark 4门版');
      expect(firstVehicle).toHaveTextContent('紧凑型轿车');
      expect(firstVehicle).toHaveTextContent('4');
      expect(firstVehicle).toHaveTextContent('3');
      expect(firstVehicle).toHaveTextContent('4');
      expect(firstVehicle).toHaveTextContent('自动挡');
      // 455732 车型信息
      expect(firstVehicle).toHaveTextContent(
        '雪佛兰 Spark 4门版或同组车型紧凑型轿车4座4门自动挡A/C3个24寸行李箱3个报价¥272/天起',
      );

      const vendorListEnterList = await Page.queryAllByTestId(
        UITestID.car_testid_page_list_vendor_list_enter_osd,
      );
      const firstVendorListEnter = vendorListEnterList[0];
      expect(firstVendorListEnter).toHaveTextContent('3个报价');

      const moreVendorListEnter = vendorListEnterList[4];
      expect(moreVendorListEnter).toHaveTextContent('等10个报价');

      const moreVendorListEnterImage = await moreVendorListEnter.findAllByType(
        Image,
      );
      expect(moreVendorListEnterImage.length).toEqual(3);
    })
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [440468],
    name: '报价数量',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
    const Page = await renderPage();

    await waitRender(async () => {
      const vehicleList = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_osd,
      );

      const moreVehicle = vehicleList[4];
      const moreVehicleTouchableOpacity = await moreVehicle.findAllByType(
        TouchableOpacity,
      )[1];

      await act(async () => {
        fireEvent(moreVehicleTouchableOpacity, 'onPress');
      });

      const vendorListModalItems = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vendor_list_modal_item_osd,
      );
      expect(vendorListModalItems.length).toEqual(10);
    });
  },
  TEST_TIMEOUT,
);

// test(
//   createInterTestName({
//     testId: [440471, 440474, 440478, 440480, 440481, 440482, 440483],
//     name: '报价弹窗',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementationOnce(() => Promise.resolve(res440463_201));
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementationOnce(() => Promise.resolve(res440463));
//     const Page = await renderPage();
//     const vehicleList = await Page.findAllByTestId(
//       UITestID.car_testid_page_list_vehicle_osd,
//     );

//     const firstVehicle = vehicleList[4];
//     const firstVehicleTouchableOpacity = await firstVehicle.findAllByType(
//       TouchableOpacity,
//     )[1];

//     await act(async () => {
//       fireEvent(firstVehicleTouchableOpacity, 'onPress');
//     });

//     const vendorListModal = await Page.findByTestId(
//       UITestID.car_testid_page_list_vendor_list_modal_osd,
//     );
//     expect(vendorListModal).toBeTruthy();
//     expect(vendorListModal).toHaveTextContent('或同组车型');

//     const vendorListModalItems = await Page.findAllByTestId(
//       UITestID.car_testid_page_list_vendor_list_modal_item_osd,
//     );

//     const firstVendorItem = vendorListModalItems[0];
//     expect(firstVendorItem).toHaveTextContent('¥328/天');
//     expect(firstVendorItem).toHaveTextContent('4.9/5');
//     expect(firstVendorItem).toHaveTextContent('1条评价');

//     const vendorItemImages = await firstVendorItem.findAllByType(Image);
//     expect(vendorItemImages[0].props.source.uri).toEqual(
//       `https://pic.c-ctrip.com/car/osd/ctrip/vendor_logo/dollar_2020.png?timestamps=${BbkConstants.timestamps}`,
//     );

//     const vendorTouchable = await firstVendorItem.findAllByType(
//       TouchableOpacity,
//     );
//     await act(async () => {
//       fireEvent(vendorTouchable[0], 'onPress');
//     });

//     // @ts-ignore
//     expect(global.pushPageName).toEqual('Product');
//   },
//   TEST_TIMEOUT,
// );

// test(
//   createInterTestName({
//     testId: [440472, 440473, 440475, 440476, 440477, 440479, 531446],
//     name: '同组车型二级弹层',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementationOnce(() => Promise.resolve(res440463_201));
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementationOnce(() => Promise.resolve(res440463));
//     const Page = await renderPage();
//     const vehicleList = await Page.findAllByTestId(
//       UITestID.car_testid_page_list_vehicle_osd,
//     );

//     const moreVehicle = vehicleList[4];
//     const moreVehicleTouchableOpacity = await moreVehicle.findAllByType(
//       TouchableOpacity,
//     )[1];

//     await act(async () => {
//       fireEvent(moreVehicleTouchableOpacity, 'onPress');
//     });

//     const vendorListModal = await Page.findByTestId(
//       UITestID.car_testid_page_list_vendor_list_modal_osd,
//     );

//     const vendorListModalTouchable = await vendorListModal.findAllByType(
//       TouchableOpacity,
//     );
//     await act(async () => {
//       fireEvent(vendorListModalTouchable[2], 'onPress');
//     });

//     const similarModal = await Page.findByTestId(
//       UITestID.car_testid_page_vendorList_carInfoModal,
//     );

//     expect(similarModal).toBeTruthy();
//     expect(similarModal).toHaveTextContent('车型组');
//     expect(similarModal).toHaveTextContent('紧凑型轿车');
//     expect(similarModal).toHaveTextContent('中大型轿车');
//     expect(similarModal).toHaveTextContent('中大型SUV');

//     // expect(similarModal).toHaveTextContent('车型基本信息');
//     // expect(similarModal).toHaveTextContent('5');
//     // expect(similarModal).toHaveTextContent('3');
//     // expect(similarModal).toHaveTextContent('4');

//     // expect(similarModal).toHaveTextContent(
//     //   '*以上车型信息及图片仅供参考。个别配置可能仅部分年款的车型才有，具体以门店提供车辆的实际配置为准',
//     // );
//     // 531446 同组车型/指定车型弹窗中车图展示正常
//     const similarModalImages = await vendorListModal.findAllByType(Image);
//     expect(similarModalImages[0].props.source.uri).toEqual(
//       'https://pic.c-ctrip.com/car/osd/online/vehicle_new/VW_Jetta.png',
//     );

//     // const similarModalBoot = await Page.findByTestId(
//     //   UITestID.car_testid_page_list_vendor_list_modal_boot_osd,
//     // );
//     // const bootTouchable = await similarModalBoot.findAllByType(
//     //   TouchableOpacity,
//     // );
//     // await act(async () => {
//     //   fireEvent(bootTouchable[0], 'onPress');
//     // });
//     // const packageImage = await similarModalBoot.findAllByType(Image);
//     // expect(packageImage[0].props.source.uri).toEqual(
//     //   `https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png?timestamps=${BbkConstants.timestamps}`,
//     // );

//     // await act(async () => {
//     //   fireEvent(bootTouchable[0], 'onPress');
//     // });
//     // const packageImage2 = await similarModalBoot.findAllByType(Image);
//     // expect(packageImage2.length).toEqual(0);
//   },
//   TEST_TIMEOUT,
// );

// test(
//   createInterTestName({
//     testId: [440488, 440491],
//     name: '列表底部已展示全部车型',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(res440424));
//     const Page = await renderPage();

//     await waitRender(async () => {
//       const vehicleListContain = await Page.findByTestId(
//         UITestID.car_testid_list_osd_carVehicleList,
//       );

//       expect(vehicleListContain).toHaveTextContent('已展示全部车型');

//       const quickFilter = await Page.queryByTestId(
//         UITestID.car_testid_comp_quick_filter_bar,
//       );

//       const inAirPortQuickFilter = await quickFilter.findAllByType(
//         TouchableOpacity,
//       )[0];

//       await act(() => {
//         fireEvent(inAirPortQuickFilter, 'onPress');
//       });

//       expect(vehicleListContain).not.toHaveTextContent('已展示全部车型');
//     });
//   },
//   TEST_TIMEOUT,
// );

// test(
//   createInterTestName({
//     testId: [440489, 440490],
//     name: '列表页底部筛选条件回显',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(res440424));
//     const Page = await renderPage();

//     await waitRender(async () => {
//       const quickFilter = await Page.queryByTestId(
//         UITestID.car_testid_comp_quick_filter_bar,
//       );

//       const allQuickFilterBtn = await quickFilter.findAllByType(
//         TouchableOpacity,
//       );
//       const inAirPortQuickFilter = allQuickFilterBtn[0];

//       const selfPickupQuickFilter = allQuickFilterBtn[1];

//       await act(() => {
//         // 点击机场内筛选
//         fireEvent(inAirPortQuickFilter, 'onPress');
//       });

//       await act(() => {
//         // 点击自助取车筛选
//         fireEvent(selfPickupQuickFilter, 'onPress');
//       });

//       const vehicleListContain = await Page.findByTestId(
//         UITestID.car_testid_list_osd_carVehicleList,
//       );

//       expect(vehicleListContain).toHaveTextContent(
//         '没有更多符合条件的车型了减少筛选条件试试',
//       );
//       expect(vehicleListContain).toHaveTextContent('机场内');
//       expect(vehicleListContain).toHaveTextContent('自助取车');

//       const clearFilterBtn = await Page.findByText('清除');
//       await act(() => {
//         // 点击自助取车筛选
//         fireEvent(clearFilterBtn, 'onPress');
//       });

//       const vehicleListContainAfterClear = await Page.findByTestId(
//         UITestID.car_testid_list_osd_carVehicleList,
//       );

//       expect(vehicleListContainAfterClear).toHaveTextContent('已展示全部车型');
//       expect(vehicleListContainAfterClear).not.toHaveTextContent(
//         '没有更多符合条件的车型了减少筛选条件试试',
//       );
//       expect(vehicleListContainAfterClear).not.toHaveTextContent('机场内');
//       expect(vehicleListContainAfterClear).not.toHaveTextContent('自助取车');
//     });
//   },
//   TEST_TIMEOUT,
// );
