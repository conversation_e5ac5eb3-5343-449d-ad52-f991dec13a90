import { fireEvent } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import { Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { waitRender, renderWithAction } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: true,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

test(
  createInterTestName({
    testId: [
      3531137, 3531151, 3531158, 3531165, 3531179, 3531186, 3531172, 3531207,
      3531221, 3531214, 3531249, 3531263, 3531270, 3531256, 3531291, 3531298,
      3531277, 3531284, 3531319, 3531305, 3531340, 3531326, 3531361, 3531347,
      3531375, 3531242, 3531193, 3531200, 3531235, 3531354,
    ],
    name: '无忧租列表页',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
    const Page = await renderPage();
    await waitRender(async () => {
      await Page.findAllByTestId(UITestID.car_testid_page_list_vehicle_sku);
    });
    // 3531151,3531158,3531165,3531179,3531186,3531172,3531207,3531221,3531214,3531249,3531263,3531270
    expect(Page.container).toHaveTextContent('经济轿车/5座/自动挡/1.5L');
    expect(Page.container).toHaveTextContent('倒车影像/雷达/行车记录');
    expect(Page.container).toHaveTextContent('一口价¥60');
    expect(Page.container).toHaveTextContent('比单独加购保障省¥95');
    expect(Page.container).toHaveTextContent('保障全面');
    // 3531200 有优惠时
    expect(Page.container).toHaveTextContent('券减¥25');
    // ,3531256,3531291,3531298,3531277,3531284,3531319,3531305,3531340,3531326,3531361,3531347,3531375
    await renderWithAction({
      action: async () => {
        const label = await Page.findByText('比单独加购保障省¥505');
        fireEvent.press(label);
      },
      expect: async () => {
        expect(Page.container).toHaveTextContent(
          '一口价说明非一口价产品加购保障车损保障全额赔付三者保障200万免收停运费免收折旧费车上人员保障5万/座免费道路救援·车损保障全额赔付·三者保障200万·1万及以内免收停运费·3万元及以内免收折旧费·车上人员保障1万/座·收费道路救援一口价¥60总价¥565车辆租赁费无忧租+车损全免保障¥6012月27日 周二¥15812月28日 周三¥158一口价¥60立即预订',
        );
      },
    });

    // 3531354 button立即预订
    await renderWithAction({
      action: async () => {
        const label = await Page.findAllByText('立即预订');
        fireEvent.press(label[0]);
      },
      expect: () => {
        expect(pagePushFunc).toHaveBeenCalled();
      },
    });

    // 3531235 关闭
    await renderWithAction({
      action: async () => {
        const close = await Page.findByTestId(
          'car_testid_page_insurance_modal_close_icon',
        );
        fireEvent.press(close);
      },
      expect: () => {
        expect(Page.container).not.toHaveTextContent('一口价说明');
      },
    });

    // 3531242 订
    await renderWithAction({
      action: async () => {
        const label = await Page.findAllByText('订');
        fireEvent.press(label[0]);
      },
      expect: () => {
        expect(pagePushFunc).toHaveBeenCalled();
      },
    });
    // 3531193 没有找到对应的其它资源比价
    await renderWithAction({
      action: async () => {
        const vehicleList = await Page.findAllByTestId(
          UITestID.car_testid_page_list_vehicle_sku,
        );
        fireEvent.press(vehicleList[1]);
      },
      expect: () => {
        const queryPackageComparisonFunc = jest
          .spyOn(CarFetch, 'queryPackageComparison')
          .mockImplementation(() => Promise.resolve(null));
        expect(queryPackageComparisonFunc).not.toHaveBeenCalled();
      },
    });
  },
);
