/* eslint-disable global-require */
/**
 * 海外订单详情页自营险详情弹窗模块
 */
import { TouchableOpacity, Text } from 'react-native';
import URL from '@c2x/apis/URL';
import { fireEvent } from '@testing-library/react-native';
import { AppContext, Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { renderWithAction } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';

const orderRes20230912 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/20230912.json');
const orderRes20230913 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/20230913.json');

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    saveIsd: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  jest
    .spyOn(CarStorage, 'load')
    .mockImplementation(() => Promise.resolve('[]'));
  jest
    .spyOn(CarStorage, 'loadAsync')
    .mockImplementation(() => Promise.resolve('[]'));
  return createPage(container, { name: 1 }, initialState);
};

describe('保险详情页面原起赔额标签和文案优化', () => {
  test(
    createInterTestName({
      testId: [565853],
      name: '二级页CDW/SCDW/LDW/TP/STP 保障详情增加增加起赔额承担方说明表格',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      AppContext.setABTesting({
        // @ts-ignore
        '231008_DSJT_cjptx': {
          State: true,
          ExpVersion: 'C',
          ExpCode: '231008_DSJT_cjptx',
          ExpDefaultVersion: '',
          BeginTime: '',
          ExpResult:
            'L:L148639,D:D,Mod:65,231008_DSJT_cjptx:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
          End: '',
          EndTime: '',
          Attrs: {},
        },
      });
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230912));
      const Page = await renderPage();

      await renderWithAction({
        action: async () => {
          const insuranceBox = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancebox,
          );
          const btns = await insuranceBox.findAllByType(TouchableOpacity);
          // 点击保险套餐详情
          fireEvent(btns[0], 'onPress');
        },
        expect: () => {},
      });

      await renderWithAction({
        action: async () => {
          // 点击车损盗抢险
          const insuranceDetailBtns = await Page.findAllByTestId(
            UITestID.car_testid_page_product_insurancesuits_modal_detail_horizontal,
          );
          fireEvent(insuranceDetailBtns[1], 'onPress');
        },
        expect: () => {},
      });

      const insuranceModal = await Page.findByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal,
      );

      // 查看起赔额弹窗说明表格
      expect(insuranceModal).toHaveTextContent('起赔额$10-$300');
      expect(insuranceModal).toHaveTextContent('$10-$300以上部分');
      expect(insuranceModal).toHaveTextContent('车行承担');
      expect(insuranceModal).toHaveTextContent('$10-$300以下部分（据实承担）');
      expect(insuranceModal).toHaveTextContent('客户或承租方承担');
    },
    TEST_TIMEOUT,
  );

  test(
    createInterTestName({
      testId: [565856],
      name: '搭售了补充险时，二级页车行险CDW/SCDW/LDW/TP/STP的保险短描述后拼接起赔额描述',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      AppContext.setABTesting({
        // @ts-ignore
        '231008_DSJT_cjptx': {
          State: true,
          ExpVersion: 'C',
          ExpCode: '231008_DSJT_cjptx',
          ExpDefaultVersion: '',
          BeginTime: '',
          ExpResult:
            'L:L148639,D:D,Mod:65,231008_DSJT_cjptx:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
          End: '',
          EndTime: '',
          Attrs: {},
        },
      });
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230913));
      const Page = await renderPage();

      await renderWithAction({
        action: async () => {
          const insuranceBox = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancebox,
          );
          const btns = await insuranceBox.findAllByType(TouchableOpacity);
          // 点击保险套餐详情
          fireEvent(btns[0], 'onPress');
        },
        expect: () => {},
      });

      const insuranceModal = await Page.findByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal,
      );

      // 搭售自营险展示
      expect(insuranceModal).toHaveTextContent(
        '*起赔额$100-$400及以下部分由国内保险公司承担',
      );
    },
    TEST_TIMEOUT,
  );

  test(
    createInterTestName({
      testId: [565851],
      name: '详情页二级页面 现有【车行提供】、【国内保险公司提供】展示位置',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      AppContext.setABTesting({
        // @ts-ignore
        '231008_DSJT_cjptx': {
          State: true,
          ExpVersion: 'C',
          ExpCode: '231008_DSJT_cjptx',
          ExpDefaultVersion: '',
          BeginTime: '',
          ExpResult:
            'L:L148639,D:D,Mod:65,231008_DSJT_cjptx:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
          End: '',
          EndTime: '',
          Attrs: {},
        },
      });
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230912));
      const Page = await renderPage();

      await renderWithAction({
        action: async () => {
          const insuranceBox = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancebox,
          );
          const btns = await insuranceBox.findAllByType(TouchableOpacity);
          // 点击保险套餐详情
          fireEvent(btns[0], 'onPress');
        },
        expect: async () => {},
      });

      const insurancesuitsModal = await Page.findByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal,
      );
      expect(insurancesuitsModal).toBeTruthy();

      expect(insurancesuitsModal).toHaveTextContent(
        '车行提供：保障车辆碰撞损失车辆盗抢保障',
      );
      expect(insurancesuitsModal).toHaveTextContent(
        '国内保险公司提供：保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额',
      );
    },
    TEST_TIMEOUT,
  );
});

describe('保险详情页增加 投保须知 及 保险条款', () => {
  test(
    createInterTestName({
      testId: [565855, 590716],
      name: '安心补充险及驾乘意外险增加保障链接',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      AppContext.setABTesting({
        // @ts-ignore
        '231008_DSJT_cjptx': {
          State: true,
          ExpVersion: 'C',
          ExpCode: '231008_DSJT_cjptx',
          ExpDefaultVersion: '',
          BeginTime: '',
          ExpResult:
            'L:L148639,D:D,Mod:65,231008_DSJT_cjptx:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
          End: '',
          EndTime: '',
          Attrs: {},
        },
      });
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230912));
      const Page = await renderPage();

      await renderWithAction({
        action: async () => {
          const insuranceBox = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancebox,
          );
          const btns = await insuranceBox.findAllByType(TouchableOpacity);
          // 点击保险套餐详情
          fireEvent(btns[0], 'onPress');
        },
        expect: () => {},
      });

      await renderWithAction({
        action: async () => {
          // 点击查看保障详情
          const insuranceDetailLook = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancesuits_modal_anchordetail,
          );
          fireEvent(insuranceDetailLook, 'onPress');
        },
        expect: () => {},
      });

      const insurancedetailBtns = await Page.findAllByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal_detail_horizontal,
      );

      const tabTexts = await insurancedetailBtns[3].findAllByType(Text);

      const selectedStyleJson = JSON.stringify(tabTexts[0].props.style);
      // 选中样式
      expect(
        selectedStyleJson.indexOf('{"color":"#0086f6"}') > -1,
      ).toBeTruthy();
    },
    TEST_TIMEOUT,
  );

  test(
    createInterTestName({
      testId: [565860],
      name: '保障详情下增加 投保须知 及 保险条款',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      AppContext.setABTesting({
        // @ts-ignore
        '231008_DSJT_cjptx': {
          State: true,
          ExpVersion: 'C',
          ExpCode: '231008_DSJT_cjptx',
          ExpDefaultVersion: '',
          BeginTime: '',
          ExpResult:
            'L:L148639,D:D,Mod:65,231008_DSJT_cjptx:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
          End: '',
          EndTime: '',
          Attrs: {},
        },
      });
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230912));
      const Page = await renderPage();

      await renderWithAction({
        action: async () => {
          const insuranceBox = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancebox,
          );
          const btns = await insuranceBox.findAllByType(TouchableOpacity);
          // 点击保险套餐详情
          fireEvent(btns[0], 'onPress');
        },
        expect: () => {},
      });

      await renderWithAction({
        action: async () => {
          const insurancedetailBtns = await Page.findAllByTestId(
            UITestID.car_testid_page_product_insurancesuits_modal_detail_horizontal,
          );
          // 点击保险详情水平滚动条的安心补充险Item
          fireEvent(insurancedetailBtns[3], 'onPress');
        },
        expect: () => {},
      });

      await renderWithAction({
        action: async () => {
          const noticeruleBtn = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancesuits_modal_noticerule,
          );
          // 点击保险条款 跳转url
          fireEvent(noticeruleBtn, 'onPress');
        },
        expect: () => {
          const openURL = jest.spyOn(URL, 'openURL');
          expect(openURL).toBeCalledWith(
            'https://pages.c-ctrip.com/tour/pdf1903/1.pdf',
            '保险条款',
          );
        },
      });

      await renderWithAction({
        action: async () => {
          const mustreadBtn = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancesuits_modal_mustread,
          );
          // 点击投保须知 展示弹窗
          fireEvent(mustreadBtn, 'onPress');
        },
        expect: () => {
          const openURL = jest.spyOn(URL, 'openURL');
          expect(openURL).toBeCalledWith(
            'https://pages.c-ctrip.com/tour/pdf1903/1.pdf',
            '保险条款',
          );
        },
      });

      await renderWithAction({
        action: async () => {
          const mustreadBtn = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancesuits_modal_mustread,
          );
          // 点击投保须知 展示弹窗
          fireEvent(mustreadBtn, 'onPress');
        },
        expect: () => {
          const openURL = jest.spyOn(URL, 'openURL');
          expect(openURL).toBeCalledWith(
            'https://pages.c-ctrip.com/tour/pdf1903/1.pdf',
            '保险条款',
          );
        },
      });

      const mustreadmodal = await Page.findByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal_mustread_modal,
      );
      expect(mustreadmodal).toBeTruthy();
    },
    TEST_TIMEOUT,
  );

  test(
    createInterTestName({
      testId: [565857],
      name: 'AB',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230912));
      const Page = await renderPage();

      // 565855 安心补充险及驾乘意外险增加保障链接 A版
      await renderWithAction({
        action: async () => {
          const insuranceBox = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancebox,
          );
          const btns = await insuranceBox.findAllByType(TouchableOpacity);
          // 点击保险套餐详情
          fireEvent(btns[0], 'onPress');
        },
        expect: () => {},
      });

      await renderWithAction({
        action: async () => {
          // 点击查看保障详情
          const insuranceDetailLook = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancesuits_modal_anchordetail,
          );
          fireEvent(insuranceDetailLook, 'onPress');
        },
        expect: () => {},
      });

      const insurancedetailBtns = await Page.findAllByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal_detail_horizontal,
      );

      const tabTexts = await insurancedetailBtns[3].findAllByType(Text);

      const selectedStyleJson = JSON.stringify(tabTexts[0].props.style);
      // 选中样式
      expect(
        selectedStyleJson.indexOf('{"color":"#0086f6"}') > -1,
      ).toBeTruthy();

      // 565860 保障详情下增加 投保须知 及 保险条款 A版
      await renderWithAction({
        action: async () => {
          // 点击保险详情水平滚动条的安心补充险Item
          fireEvent(insurancedetailBtns[3], 'onPress');
        },
        expect: () => {},
      });

      await renderWithAction({
        action: async () => {
          const noticeruleBtn = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancesuits_modal_noticerule,
          );
          // 点击保险条款 跳转url
          fireEvent(noticeruleBtn, 'onPress');
        },
        expect: () => {
          const openURL = jest.spyOn(URL, 'openURL');
          expect(openURL).toBeCalledWith(
            'https://pages.c-ctrip.com/tour/pdf1903/1.pdf',
            '保险条款',
          );
        },
      });

      await renderWithAction({
        action: async () => {
          const mustreadBtn = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancesuits_modal_mustread,
          );
          // 点击投保须知 展示弹窗
          fireEvent(mustreadBtn, 'onPress');
        },
        expect: () => {
          const openURL = jest.spyOn(URL, 'openURL');
          expect(openURL).toBeCalledWith(
            'https://pages.c-ctrip.com/tour/pdf1903/1.pdf',
            '保险条款',
          );
        },
      });

      await renderWithAction({
        action: async () => {
          const mustreadBtn = await Page.findByTestId(
            UITestID.car_testid_page_product_insurancesuits_modal_mustread,
          );
          // 点击投保须知 展示弹窗
          fireEvent(mustreadBtn, 'onPress');
        },
        expect: () => {
          const openURL = jest.spyOn(URL, 'openURL');
          expect(openURL).toBeCalledWith(
            'https://pages.c-ctrip.com/tour/pdf1903/1.pdf',
            '保险条款',
          );
        },
      });

      const mustreadmodal = await Page.findByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal_mustread_modal,
      );
      expect(mustreadmodal).toBeTruthy();
    },
    TEST_TIMEOUT,
  );
});
