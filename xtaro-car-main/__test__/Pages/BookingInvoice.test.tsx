import { Utils, User, AppContext, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';
import { waitRender } from '../testHelpers';

const priceRes489391 = require('../../__mocks__/restful/27140/querypriceinfo/489391.json');

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, initialState);
};

test(
  createInterTestName({
    testId: [489390],
    name: '填写页发票信息',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    // 489390 发票信息
    // 非VBK资源进入填写页 预期：取消政策下面展示发票“如有租车相关费用发票的需求，请在还车后联系门店”
    await waitRender(async () => {
      expect(Page.container).toHaveTextContent(
        '如有租车相关费用发票的需求，请在还车后联系门店',
      );
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [489391],
    name: '填写页发票信息',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    jest
      .spyOn(CarFetch, 'queryPriceInfo')
      .mockImplementation(() => Promise.resolve(priceRes489391));

    const Page = await renderPage();
    // VBK资源进入填写页 预期：如需发票，请在订单详情页填写信息开票
    await waitRender(async () => {
      expect(Page.container).toHaveTextContent(
        '如需发票，请在订单详情页填写信息开票',
      );
    });
  },
  TEST_TIMEOUT,
);
