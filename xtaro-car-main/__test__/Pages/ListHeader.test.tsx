import { fireEvent, act } from '@testing-library/react-native';
import { Utils, CarStorage } from '../../src/pages/xcar/Util/Index';
import {
  createPage,
  createInterTestName,
} from '../PageProvider';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';
import { waitRender } from '../testHelpers';

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: true,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

test(
  createInterTestName({
    testId: [489205],
    name: '右上角侧边栏',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();
    await waitRender(async () => {
      const sideBox = await Page.getByTestId(
        'SideToolBox',
      );
      
      expect(sideBox).toBeTruthy();
      await act(() => {
        fireEvent(sideBox, 'onPress');
      });

      // 489205 我的收藏
      expect(Page.container).toHaveTextContent('我的收藏');
      // 489206 浏览历史
      expect(Page.container).toHaveTextContent('浏览历史');
      // 489207 联系客服
      expect(Page.container).toHaveTextContent('联系客服');
      expect(Page.container).toHaveTextContent('carrental');
      expect(Page.container).toHaveTextContent('1');
      // 489209 订单
      expect(Page.container).toHaveTextContent('订单');
      // 489210 消息
      expect(Page.container).toHaveTextContent('消息');
      // 489211 首页
      expect(Page.container).toHaveTextContent('首页');
      // 489212 反馈
      expect(Page.container).toHaveTextContent('反馈');
      // 489216 浮层
      await act(() => {
        fireEvent(sideBox, 'onPress');
      });
      expect(Page.container).not.toHaveTextContent('首页');

    });
  },
);
