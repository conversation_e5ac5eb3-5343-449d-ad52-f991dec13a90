/**
 * 订单详情页出境自营险模块
 */
import { TouchableOpacity, Text } from 'react-native';
import { waitFor, fireEvent } from '@testing-library/react-native';
import { act } from 'react-test-renderer';
import UIToast from '../../src/pages/xcar/Common/src/Components/Basic/Toast/src';
import { AppContext, Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { waitRender, renderWithAction, delayPromise } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';

const orderRes20230912 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/20230912.json');
const orderRes20230913 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/20230913.json');
const extraInsuranceRes20230913 = require('../../__mocks__/restful/18862/queryExtraInsurance/OSD/20230913.json');

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    saveIsd: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  jest
    .spyOn(CarStorage, 'load')
    .mockImplementation(() => Promise.resolve('[]'));
  jest
    .spyOn(CarStorage, 'loadAsync')
    .mockImplementation(() => Promise.resolve('[]'));
  return createPage(container, { name: 1 }, initialState);
};

test(
  createInterTestName({
    testId: [458554, 458556, 458555, 458553, 4068163],
    name: '境外订详 - 保障服务',
  }),

  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

    const Page = await renderPage();
    const PageDom = Page.container;
    const insDetail = await Page.findByText('保险详情及理赔流程');
    await act(async () => {
      fireEvent.press(insDetail);
    });
    const insCompose = await waitFor(() =>
      Page.queryByTestId(UITestID.car_testid_page_insurance_compose_osd),
    );
    await waitRender(async () => {
      // 458553 点击跳出 保障服务弹层
      expect(PageDom).toHaveTextContent('保障服务');
      // 458555
      // 4068163 详情页二级页面 现有【车行提供】、【国内保险公司提供】展示位置 展示规则：{保险提供方}：{保险短描述}
      expect(insCompose).toHaveTextContent('车行提供');
      expect(insCompose).toHaveTextContent('超级碰撞盗抢保障');
      // 458556 保障内容 不保障内容 事故处理流程
      expect(PageDom).toHaveTextContent('保障内容');
      expect(PageDom).toHaveTextContent('不保障内容');
      expect(PageDom).toHaveTextContent('事故处理流程');
    });
    //
    const closebtn = await Page.findByTestId(
      UITestID.car_testid_page_product_insurancesuits_modal_header_lefticon,
    );
    await act(async () => {
      fireEvent.press(closebtn);
    });
    const baozhang = await Page.queryAllByText('保障内容');
    await waitRender(async () => {
      expect(baozhang.length).toBe(1);
    });
  },
);

test(
  createInterTestName({
    testId: [4068114, 4068135, 4068170, 4068149, 4068170, 4068184, 4068142],
    name: '保障服务',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    AppContext.setABTesting({
      // @ts-ignore
      '231008_DSJT_cjptx': {
        State: true,
        ExpVersion: 'C',
        ExpCode: '231008_DSJT_cjptx',
        ExpDefaultVersion: '',
        BeginTime: '',
        ExpResult:
          'L:L148639,D:D,Mod:65,231008_DSJT_cjptx:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
        End: '',
        EndTime: '',
        Attrs: {},
      },
    });
    jest
      .spyOn(CarFetch, 'queryOrder')
      .mockImplementation(() => Promise.resolve(orderRes20230913));
    jest
      .spyOn(CarFetch, 'queryExtraInsurance')
      .mockImplementation(() => Promise.resolve(extraInsuranceRes20230913));
    const Page = await renderPage();

    await waitRender(async () => {
      // 4068114 提车凭证套餐包含项目下文案
      expect(Page.container).toHaveTextContent(
        '由于车行系统无法查看国内保险公司提供的保险，如被店员推销类似保险，请明确无需购买，可出示保单或英文说明',
      );
      // 4068135 提车凭证更新起赔额描述
      expect(Page.container).toHaveTextContent('强三者险0起赔额车');
      // 4068170 当车辆碰撞险和车辆盗抢险起赔额不一致时
      expect(Page.container).toHaveTextContent('损盗抢险0起赔额');
      expect(Page.container).toHaveTextContent('驾乘意外险已出保');
      expect(Page.container).toHaveTextContent(
        '已退保，¥ 50金额退款将于1-5个工作日退还',
      );
    });

    const insDetail = await Page.findByText('保险详情及理赔流程');
    await act(async () => {
      fireEvent.press(insDetail);
    });

    const insCompose = await waitFor(() =>
      Page.queryByTestId(UITestID.car_testid_page_insurance_compose_osd),
    );
    // 4068184 搭售了安心补充险时，二级页车行险CDW/SCDW/TP/STP/LDW/SLDW的保险短描述后拼接起赔额描述
    // 4068170 当车辆碰撞险和车辆盗抢险起赔额不一致时
    // 4068149 二级页CDW/SCDW/TP/STP/LDW/SLDW 保障详情增加增加起赔额展示，与【套餐构成】模块展示一致
    await waitRender(async () => {
      expect(insCompose).toHaveTextContent('加强三者险0起赔额');
      expect(insCompose).toHaveTextContent(
        '车行提供：保障第三方车辆或人员伤害损失',
      );
      expect(insCompose).toHaveTextContent('车损盗抢险0起赔额');
      expect(insCompose).toHaveTextContent(
        '车行提供：保障车辆碰撞、被盗的损失',
      );
      expect(insCompose).toHaveTextContent('驾乘意外险¥75/天已出保');
      expect(insCompose).toHaveTextContent(
        '国内保险公司提供：保障全车人员意外伤害及财物损失',
      );
      expect(insCompose).toHaveTextContent('安心补充险¥25/天已取消');
      expect(insCompose).toHaveTextContent(
        '内保险公司提供：保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额*起赔额$100-$400及以下部分由国内保险公司承担',
      );
    });

    const tabList = await Page.findAllByTestId(
      UITestID.car_testid_page_product_insurancesuits_modal_detail_horizontal,
    );
    await fireEvent(tabList[1], 'onPress');

    const modalWrap = await Page.findByTestId(
      UITestID.car_testid_page_product_insurancesuits_modal,
    );
    // 4068142 二级页CDW/SCDW/TP/STP/LDW/SLDW 保障详情起赔额承担方说明表格
    await waitRender(async () => {
      expect(modalWrap).toHaveTextContent('车行承担');
      expect(modalWrap).toHaveTextContent('全部损失');
      expect(modalWrap).toHaveTextContent('客户或承租方承担');
      expect(modalWrap).toHaveTextContent('US$ 0');
    });
  },
  TEST_TIMEOUT,
);

describe('出境自营险订单状态展示优化', () => {
  test(
    createInterTestName({
      testId: [565846, 4068198],
      name: '报错优化',
    }),
    async () => {
      const ToastFunc = jest.spyOn(UIToast, 'show');
      const queryOrderFunc = jest.spyOn(CarFetch, 'queryOrder');
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      AppContext.setABTesting({
        // @ts-ignore
        '231008_DSJT_cjptx': {
          State: true,
          ExpVersion: 'C',
          ExpCode: '231008_DSJT_cjptx',
          ExpDefaultVersion: '',
          BeginTime: '',
          ExpResult:
            'L:L148639,D:D,Mod:65,231008_DSJT_cjptx:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
          End: '',
          EndTime: '',
          Attrs: {},
        },
      });
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230912));
      jest
        .spyOn(CarFetch, 'queryExtraInsurance')
        .mockImplementation(() => Promise.resolve(extraInsuranceRes20230913));
      const Page = await renderPage();
      await renderWithAction({
        action: async () => {
          const eleDetailBtn = await Page.findByTestId(
            UITestID.car_testid_page_order_detail_insurancebox_item_electronicdetail,
          );
          // 点击查看电子保单
          fireEvent(eleDetailBtn, 'onPress');
        },
        expect: async () => {
          // // 电子保单已取消提示
          // 4068198 报错优化 报错弹窗提示【您的保险订单已取消，为您刷新订单】。并自动刷新订单详情页。
          expect(ToastFunc).toBeCalledWith('您的保险订单已取消，为您刷新订单');
          expect(queryOrderFunc).toBeCalled();
        },
      });
    },
    TEST_TIMEOUT,
  );

  test(
    createInterTestName({
      testId: [565847],
      name: 'AB',
    }),
    async () => {
      const ToastFunc = jest.spyOn(UIToast, 'show');
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230912));
      jest
        .spyOn(CarFetch, 'queryExtraInsurance')
        .mockImplementation(() => Promise.resolve(extraInsuranceRes20230913));
      const Page = await renderPage();
      await renderWithAction({
        action: async () => {
          const eleDetailBtn = await Page.findByTestId(
            UITestID.car_testid_page_order_detail_insurancebox_item_electronicdetail,
          );
          // 点击查看电子保单
          fireEvent(eleDetailBtn, 'onPress');
        },
        expect: async () => {
          // // 电子保单已取消提示
          expect(ToastFunc).toBeCalledWith('您的保险订单已取消，为您刷新订单');
        },
      });
    },
    TEST_TIMEOUT,
  );

  test(
    createInterTestName({
      testId: [4068219],
      name: '提示文案',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      AppContext.setABTesting({
        // @ts-ignore
        '231008_DSJT_cjptx': {
          State: true,
          ExpVersion: 'C',
          ExpCode: '231008_DSJT_cjptx',
          ExpDefaultVersion: '',
          BeginTime: '',
          ExpResult:
            'L:L148639,D:D,Mod:65,231008_DSJT_cjptx:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
          End: '',
          EndTime: '',
          Attrs: {},
        },
      });
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230912));
      jest
        .spyOn(CarFetch, 'queryExtraInsurance')
        .mockImplementation(() => Promise.resolve(extraInsuranceRes20230913));
      const Page = await renderPage();
      await waitRender(async () => {
        expect(Page.container).toHaveTextContent(
          '您已购买安心补充险及全车驾乘意外险，无需在门店重复购买',
        );
      });
    },
    TEST_TIMEOUT,
  );
});

describe('自营险提示文案调整', () => {
  test(
    createInterTestName({
      testId: [565859, 4068107],
      name: '订详页面，平台险提示文案更新',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      AppContext.setABTesting({
        // @ts-ignore
        '231008_DSJT_cjptx': {
          State: true,
          ExpVersion: 'C',
          ExpCode: '231008_DSJT_cjptx',
          ExpDefaultVersion: '',
          BeginTime: '',
          ExpResult:
            'L:L148639,D:D,Mod:65,231008_DSJT_cjptx:A,ClientCode:12001139890321479536,EffectTime:2023-10-08 19:50:16,IterationId:1',
          End: '',
          EndTime: '',
          Attrs: {},
        },
      });
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230913));
      jest
        .spyOn(CarFetch, 'queryExtraInsurance')
        .mockImplementation(() => Promise.resolve(extraInsuranceRes20230913));
      const Page = await renderPage();

      const insuranceBox = await Page.findByTestId(
        UITestID.car_testid_page_product_insurancebox,
      );
      // 加购平台险 4068107
      expect(insuranceBox).toHaveTextContent(
        '由于车行系统无法查看国内保险公司提供的保险，如被店员推销类似保险，请明确无需购买，可出示保单或',
      );
      expect(insuranceBox).toHaveTextContent('英文说明 >');
      expect(insuranceBox).toHaveTextContent(
        '如签署合同时，发现额外收费或强制购买，请您不要签字，当场联系携程客服协助解决或请店员用英文在纸上写下哪些项目是需要强制购买（These items are mandatory），并加上店员签名和日期，回国后提交给携程为您申诉处理。',
      );
      expect(insuranceBox).toHaveTextContent(
        '如发生事故，请先垫付相关费用，还车后向保险公司申请理赔，',
      );
      expect(insuranceBox).toHaveTextContent('理赔流程 >');

      await renderWithAction({
        action: async () => {
          const englishBtn = await Page.findByText('英文说明 >');
          // 点击英文说明
          fireEvent(englishBtn, 'onPress');
        },
        expect: async () => {},
      });

      const englishModal = await Page.findByTestId(
        UITestID.car_testid_page_order_detail_insurance_reminder_english_modal,
      );
      // 英文说明点击后弹窗展示 4068107
      expect(englishModal).toHaveTextContent(
        'We would like to inform you that this customer has already purchased excess wavier and roadside service waiver through Ctrip. To prevent customers from accidentally purchasing duplicate coverage, we kindly request that you refrain from suggesting similar insurance packages at the counter. Failure to do so may result in a complaint filed with the appropriate authority.',
      );

      await renderWithAction({
        action: async () => {
          const englishCloseBtns = await englishModal.findAllByType(
            TouchableOpacity,
          );
          // 关闭英文说明弹窗
          fireEvent(englishCloseBtns[0], 'onPress');
        },
        expect: async () => {},
      });

      await renderWithAction({
        action: async () => {
          const englishBtn = await Page.findByText('理赔流程 >');
          // 点击理赔流程
          // 理赔流程点击后进入保险详情页面，锚定至理赔流程 4068107
          fireEvent(englishBtn, 'onPress');
        },
        expect: async () => {},
      });

      const insuranceModal = await Page.findByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal,
      );

      expect(insuranceModal).toBeTruthy();

      await delayPromise();

      const categoryItems = await Page.findAllByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal_category_horizontal,
      );
      const categoryText = await categoryItems[2].findByType(Text);

      const selectedStyleJson = JSON.stringify(categoryText.props.style);
      // 选中样式
      expect(
        selectedStyleJson.indexOf('{"color":"#0086f6"}') > -1,
      ).toBeTruthy();
    },
    TEST_TIMEOUT,
  );

  test(
    createInterTestName({
      testId: [565864],
      name: 'AB',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230913));
      jest
        .spyOn(CarFetch, 'queryExtraInsurance')
        .mockImplementation(() => Promise.resolve(extraInsuranceRes20230913));
      const Page = await renderPage();

      const insuranceBox = await Page.findByTestId(
        UITestID.car_testid_page_product_insurancebox,
      );

      expect(insuranceBox).toHaveTextContent(
        '由于车行系统无法查看国内保险公司提供的保险，如被店员推销类似保险，请明确无需购买，可出示保单或',
      );
      expect(insuranceBox).toHaveTextContent('英文说明 >');
      expect(insuranceBox).toHaveTextContent(
        '如签署合同时，发现额外收费或强制购买，请您不要签字，当场联系携程客服协助解决或请店员用英文在纸上写下哪些项目是需要强制购买（These items are mandatory），并加上店员签名和日期，回国后提交给携程为您申诉处理。',
      );
      expect(insuranceBox).toHaveTextContent(
        '如发生事故，请先垫付相关费用，还车后向保险公司申请理赔，',
      );
      expect(insuranceBox).toHaveTextContent('理赔流程 >');

      await renderWithAction({
        action: async () => {
          const englishBtn = await Page.findByText('英文说明 >');
          // 点击英文说明
          fireEvent(englishBtn, 'onPress');
        },
        expect: async () => {},
      });

      const englishModal = await Page.findByTestId(
        UITestID.car_testid_page_order_detail_insurance_reminder_english_modal,
      );
      expect(englishModal).toHaveTextContent(
        'We would like to inform you that this customer has already purchased excess wavier and roadside service waiver through Ctrip. To prevent customers from accidentally purchasing duplicate coverage, we kindly request that you refrain from suggesting similar insurance packages at the counter. Failure to do so may result in a complaint filed with the appropriate authority.',
      );

      await renderWithAction({
        action: async () => {
          const englishCloseBtns = await englishModal.findAllByType(
            TouchableOpacity,
          );
          // 关闭英文说明弹窗
          fireEvent(englishCloseBtns[0], 'onPress');
        },
        expect: async () => {},
      });

      await renderWithAction({
        action: async () => {
          const englishBtn = await Page.findByText('理赔流程 >');
          // 点击理赔流程
          fireEvent(englishBtn, 'onPress');
        },
        expect: async () => {},
      });

      const insuranceModal = await Page.findByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal,
      );

      expect(insuranceModal).toBeTruthy();

      await delayPromise();

      const categoryItems = await Page.findAllByTestId(
        UITestID.car_testid_page_product_insurancesuits_modal_category_horizontal,
      );
      const categoryText = await categoryItems[2].findByType(Text);

      const selectedStyleJson = JSON.stringify(categoryText.props.style);
      // 选中样式
      expect(
        selectedStyleJson.indexOf('{"color":"#0086f6"}') > -1,
      ).toBeTruthy();
    },
    TEST_TIMEOUT,
  );
});

describe('AB实验', () => {
  test(
    createInterTestName({
      testId: [565843],
      name: '其他',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes20230913));
      jest
        .spyOn(CarFetch, 'queryExtraInsurance')
        .mockImplementation(() => Promise.resolve(extraInsuranceRes20230913));
      const Page = await renderPage();

      const insuranceBox = await Page.findByTestId(
        UITestID.car_testid_page_product_insurancebox,
      );

      expect(insuranceBox).toHaveTextContent('加强三者');
      expect(insuranceBox).toHaveTextContent('0起赔额');
      expect(insuranceBox).toHaveTextContent('车损盗抢险');
      expect(insuranceBox).toHaveTextContent('驾乘意外险');
      expect(insuranceBox).toHaveTextContent('已出保');
      expect(insuranceBox).toHaveTextContent('查看电子保单');
      expect(insuranceBox).toHaveTextContent('¥75/天');
      expect(insuranceBox).toHaveTextContent('（国内保险公司提供）');
      expect(insuranceBox).toHaveTextContent('保障全车人员意外伤害及财物损失');
      expect(insuranceBox).toHaveTextContent('安心补充险');
      expect(insuranceBox).toHaveTextContent('已取消');
      expect(insuranceBox).toHaveTextContent('¥25/天');
      expect(insuranceBox).toHaveTextContent('已退保');
      expect(insuranceBox).toHaveTextContent('¥ 50金额退款将于1-5个工作日退还');
      expect(insuranceBox).toHaveTextContent(
        '由于车行系统无法查看国内保险公司提供的保险，如被店员推销类似保险，请明确无需购买，可出示保单或',
      );
      expect(insuranceBox).toHaveTextContent('英文说明 >');
      expect(insuranceBox).toHaveTextContent(
        '如签署合同时，发现额外收费或强制购买，请您不要签字，当场联系携程客服协助解决或请店员用英文在纸上写下哪些项目是需要强制购买（These items are mandatory），并加上店员签名和日期，回国后提交给携程为您申诉处理。',
      );
      expect(insuranceBox).toHaveTextContent(
        '如发生事故，请先垫付相关费用，还车后向保险公司申请理赔，',
      );
      expect(insuranceBox).toHaveTextContent('理赔流程 >');
    },
    TEST_TIMEOUT,
  );
});
