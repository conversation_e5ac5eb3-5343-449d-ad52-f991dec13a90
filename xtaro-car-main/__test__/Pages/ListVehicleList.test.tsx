import { Image, Text, TouchableOpacity } from 'react-native';
import CRNPage from '@c2x/components/Page';
import URL from '@c2x/apis/URL';
import { fireEvent, act, waitFor } from '@testing-library/react-native';
import { timestamps } from '../../src/pages/xcar/Common/src/Utils/src/Constants';
import {
  Utils,
  CarStorage,
  CarFetch,
  listPagingAgent,
} from '../../src/pages/xcar/Util/Index';
import { ListReqAndResData } from '../../src/pages/xcar/Global/Cache/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { waitRender } from '../testHelpers';
import { UITestID, ImageUrl } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';

const dmt2Res = require('../../__mocks__/restful/18631/queryProducts/20231106.json');
const Res20240914 = require('../../__mocks__/restful/18631/queryProducts/20240914.json');
const Res********** = require('../../__mocks__/restful/18631/queryProducts/**********.json');
const Res********** = require('../../__mocks__/restful/18631/queryProducts/**********.json');

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));
jest.mock('../../src/pages/xcar/Global/Cache/ListResSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ListResSelectors'),
  getIsFromSearch: () => false,
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: true,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

test(
  createInterTestName({
    testId: [230553, 230554, 230555, 489717],
    name: '车型信息',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const xflkwzPicUrl = `https://pages.c-ctrip.com/carisd/app/14415.jpg?timestamps=${timestamps}`;
    const xflkwzConfigGroupName = '经济轿车';
    const xflkwzConfigPassengerNo = '5座';
    const xflkwzConfigTransmission = '自动挡';
    const xflkwzConfigDisplacement = '1.0T-1.5L';
    const Page = await renderPage();

    const vehicleSkuList = await Page.findAllByTestId(
      UITestID.car_testid_page_list_vehicle_sku,
    );

    await waitFor(async () => {
      // 雪佛兰科沃兹车图
      let xflkwzPic = null;
      let xflkwzIndex = null;
      // 雪佛兰科沃兹车型组文案
      let xflkwzConfigGroupNameText = null;
      // 雪佛兰科沃兹座位数文案
      let xflkwzConfigPassengerNoText = null;
      // 雪佛兰科沃兹自动挡文案
      let xflkwzConfigTransmissionText = null;
      // 雪佛兰科沃兹排量文案
      let xflkwzConfigDisplacementText = null;

      vehicleSkuList.forEach((skuItem, index) => {
        const allImages = skuItem.findAllByType(Image);
        const allTexts = skuItem.findAllByType(Text);
        allImages.forEach(image => {
          switch (image.props.source.uri) {
            case xflkwzPicUrl:
              xflkwzIndex = index;
              xflkwzPic = image;
              break;
            default:
              break;
          }
        });
        if (xflkwzIndex === index) {
          allTexts.forEach(text => {
            switch (text.props.children) {
              case xflkwzConfigGroupName:
                xflkwzConfigGroupNameText = text;
                break;
              case xflkwzConfigPassengerNo:
                xflkwzConfigPassengerNoText = text;
                break;
              case xflkwzConfigTransmission:
                xflkwzConfigTransmissionText = text;
                break;
              case xflkwzConfigDisplacement:
                xflkwzConfigDisplacementText = text;
                break;
              default:
                break;
            }
          });
        }
      });

      // 雪佛兰科沃兹skuItem展示图片
      expect(xflkwzPic).toBeTruthy();
      // 雪佛兰科沃兹展示无忧组标签图片
      // expect(xflkwzEasylifePic).toBeTruthy();
      // 雪佛兰科沃兹车型组文案
      expect(xflkwzConfigGroupNameText).toBeTruthy();
      // 雪佛兰科沃兹座位数文案
      expect(xflkwzConfigPassengerNoText).toBeTruthy();
      // 雪佛兰科沃兹自动挡文案
      expect(xflkwzConfigTransmissionText).toBeTruthy();
      // // 雪佛兰科沃兹排量文案
      expect(xflkwzConfigDisplacementText).toBeTruthy();
    });
  },
);

test(
  createInterTestName({
    testId: [230550, 230551, 230552, 489713],
    name: '多年款',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();
    await waitRender(async () => {
      const vehicleGroupItemList = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicleList_group_vendoritem,
      );

      // 合并车型组展示2个供应商报价
      expect(vehicleGroupItemList.length).toBe(4);

      const moreVendorBtnTouchable = await Page.findByText('查看更多2个年款');

      expect(moreVendorBtnTouchable).toBeTruthy();

      // 点击”查看更多2个年款“ 按钮
      await act(() => {
        fireEvent.press(moreVendorBtnTouchable);
      });

      const moreVehicleGroupItemList = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicleList_group_vendoritem,
      );
      // 合并车型组展示4个供应商报价
      expect(moreVehicleGroupItemList.length).toBe(6);

      const torrgleVendorBtnText = await Page.findByText('收起');

      expect(torrgleVendorBtnText).toBeTruthy();

      // 点击”收起“ 按钮
      await act(() => {
        fireEvent.press(torrgleVendorBtnText);
      });

      const torrgleVehicleGroupItemList = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicleList_group_vendoritem,
      );
      expect(torrgleVehicleGroupItemList.length).toBe(6);
    });
  },
);

test(
  createInterTestName({
    testId: [230549],
    name: '划线价展示',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();
    const originText = await Page.findByText('¥88');

    expect(originText).toBeTruthy();
  },
);

test(
  createInterTestName({
    testId: [230538, 230539],
    name: '价格展示',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();
    const totalPrice = await Page.findAllByText('¥236');
    const totalPriceText = await Page.findAllByText('起');
    const discount = await Page.findAllByText('券');
    const discountText = await Page.findAllByText('已减40');

    const activity = await Page.findAllByText('春节特惠+券');
    const activityText = await Page.findAllByText('共减106');

    expect(totalPrice).toBeTruthy();
    expect(totalPriceText).toBeTruthy();

    expect(discount).toBeTruthy();
    expect(discountText).toBeTruthy();

    expect(activity).toBeTruthy();
    expect(activityText).toBeTruthy();
  },
);

test(
  createInterTestName({
    testId: [230540],
    name: '总价说明入口',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();
    const priceHelp = await Page.findAllByTestId(
      UITestID.car_testid_comp_vehicle_price_help,
    );

    expect(priceHelp).toBeTruthy();
  },
);

test(
  createInterTestName({
    testId: [230541, 230542, 230543, 230544, 230545, 230546, 230547, 230548],
    name: '总价价格弹层',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

    const Page = await renderPage();

    const priceHelp = await Page.findAllByTestId(
      UITestID.car_testid_comp_vehicle_price_help,
    );
    // 点击总价说明弹窗
    await act(() => {
      fireEvent(priceHelp[0], 'onPress');
    });

    const priceModal = await Page.findByTestId(
      UITestID.car_testid_comp_price_modal,
    );

    const nextButton = await Page.findByTestId(
      UITestID.car_testid_comp_price_modal_next,
    );

    expect(priceModal).toHaveTextContent('总价说明');

    expect(priceModal).toHaveTextContent('车辆租金');
    expect(priceModal).toHaveTextContent('日均¥78');

    expect(priceModal).toHaveTextContent('租车费');
    expect(priceModal).toHaveTextContent('¥176');

    expect(priceModal).toHaveTextContent('优惠券');
    expect(priceModal).toHaveTextContent('- ¥20');

    expect(priceModal).toHaveTextContent('服务/手续费');
    expect(priceModal).toHaveTextContent(
      '车行手续费（含车辆清洁、单据打印等）',
    );
    expect(priceModal).toHaveTextContent('¥20');

    expect(priceModal).toHaveTextContent('车行保障服务费');
    expect(priceModal).toHaveTextContent(
      '基础服务费（含车损、第三者责任等保障）',
    );
    expect(priceModal).toHaveTextContent('¥60');

    expect(priceModal).toHaveTextContent('¥256');
    expect(priceModal).toHaveTextContent('总价');
    expect(priceModal).toHaveTextContent('236');
    // 点击下一步
    await act(() => {
      fireEvent(nextButton, 'onPress');
    });

    // @ts-ignore
    expect(global.pushPageName).toEqual('VendorList');
  },
);

test(
  createInterTestName({
    testId: [595973],
    name: '车型视频信息前置',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, null);
    listPagingAgent.cacheManager.clear();
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(dmt2Res));
    const Page = await renderPage();

    await waitRender(async () => {
      const vehicleList = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_sku,
      );
      const vrVideoImageList = await vehicleList[0].findAllByType(Image);
      const vrVideoIcon = vrVideoImageList.find(
        image => image.props.source.uri === ImageUrl.CarouseVrVideoIcon,
      );
      expect(vrVideoIcon).toBeTruthy();

      const playImageList = await vehicleList[1].findAllByType(Image);
      const playIcon = playImageList.find(
        image => image.props.source.uri === ImageUrl.CarousePlayIcon,
      );
      expect(playIcon).toBeTruthy();

      const vrImageList = await vehicleList[2].findAllByType(Image);
      const vrIcon = vrImageList.find(
        image => image.props.source.uri === ImageUrl.CarouseVrIcon,
      );
      expect(vrIcon).toBeTruthy();
    });
  },
);

test(
  createInterTestName({
    testId: [489712],
    name: '价格',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, null);
    listPagingAgent.cacheManager.clear();
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(dmt2Res));
    const Page = await renderPage();

    await waitRender(async () => {
      const priceHelp = await Page.findAllByTestId(
        UITestID.car_testid_comp_vehicle_price_help,
      );
      await act(() => {
        fireEvent(priceHelp[0], 'onPress');
      });
      expect(Page.container).toHaveTextContent('下一步，选择门店');
    });
  },
);

test(
  createInterTestName({
    testId: [489718],
    name: '单年款',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, null);
    listPagingAgent.cacheManager.clear();
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(dmt2Res));
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
    const Page = await renderPage();

    await waitRender(async () => {
      const vehicleSkuList = await Page.findAllByTestId(
        UITestID.car_testid_page_list_vehicle_sku,
      );
      const vehicleCards = await vehicleSkuList[0].findAllByType(
        TouchableOpacity,
      );
      await act(() => {
        fireEvent(vehicleCards[0], 'onPress');
      });
      expect(pagePushFunc).toBeCalledWith('VendorList', {
        fromPage: undefined,
        isSetFirstScreenBefore: true,
        pageParam: {
          browVendorCode: undefined,
          ctripVehicleId: '5145',
          extraMaps: { recommendType: '' },
          filterLabelsStr: "  ",
          filters: [],
          groupCode: '6',
          productRef: { license: '', licenseStyle: '2', licenseTag: '' },
          sortType: 1,
          tops: [],
          uniqSign: '1200113989032147953643I72320Yi3e2701LNS6',
          vehiclesSetId: '10',
        },
        priceListLen: 1,
        vehicleIndex: 0,
        vendorListFirstScreenParam: {
          vehicleInfo: {
            autoBackUp: true,
            autoStart: true,
            brandEName: '奔驰',
            brandName: '奔驰',
            carPhone: true,
            carPlay: '',
            chargeInterface: 'Type-C',
            displacement: '1.3T',
            doorNo: 5,
            driveMode: '前置前驱',
            endurance: '工信部续航100km',
            fuel: '95号',
            groupCode: '6',
            groupName: 'SUV',
            groupSubClassCode: '',
            guidSys: '定速巡航',
            imageList: [
              'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
            ],
            isHot: false,
            isSpecialized: true,
            license: '',
            licenseStyle: '2',
            luggageNo: 1,
            luggageNum: '可放1个24寸行李箱',
            mediaTypes: [2, 3],
            name: '奔驰GLB',
            oilType: 3,
            passengerNo: 5,
            realityImageUrl:
              'https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg',
            skylight: '分段式电动天窗',
            struct: 'SUV',
            style: '',
            transmissionName: '自动挡',
            transmissionType: 1,
            vehicleCode: '5145',
            vehiclesSetId: '10',
            zhName: '奔驰GLB',
          },
        },
      });
    });
  },
);

test(
  createInterTestName({
    testId: [489716],
    name: '报价数量',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, null);
    listPagingAgent.cacheManager.clear();
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(dmt2Res));
    const Page = await renderPage();

    await waitRender(async () => {
      expect(Page.container).toHaveTextContent('216个车型，682个报价');
    });
  },
);

test(
  createInterTestName({
    testId: [489719, 770029],
    name: '唐图模块展示',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, null);
    listPagingAgent.cacheManager.clear();
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(dmt2Res));
    const openURL = jest.spyOn(URL, 'openURL');
    const Page = await renderPage();

    await waitRender(async () => {
      const tangramEntry = await Page.findByTestId(
        UITestID.car_testid_page_list_tangram_entry,
      );
      expect(tangramEntry).toBeTruthy();
      await act(() => {
        fireEvent(tangramEntry, 'onPress');
      });
      expect(openURL).toBeCalledWith(
        '/rn_car_main/_crn_config?CRNModuleName=rn_car_main&CRNType=1&initialPage=Member&apptype=ISD_C_APP',
      );
    });
  },
);

test(
  createInterTestName({
    testId: [770001],
    name: '自助取还中插banner优先级',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, null);
    listPagingAgent.cacheManager.clear();
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(Res20240914));
    const openURL = jest.spyOn(URL, 'openURL');
    const Page = await renderPage();

    await waitRender(async () => {
      const selfService = await Page.findByTestId(
        UITestID.car_testid_page_list_self_service_entry,
      );
      expect(selfService).toBeTruthy();
      await act(() => {
        fireEvent(selfService, 'onPress');
      });
      expect(openURL).toBeCalledWith(
        'https://m.ctrip.com/tangram/MTMyNDQ4?ctm_ref=vactang_page_132448&isHideNavBar=YES&apppgid=10650039395&statusBarStyle=1&channelid=236720',
      );
    });
  },
);

test(
  createInterTestName({
    testId: [770008],
    name: '自助取还中插banner默认位置',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, null);
    listPagingAgent.cacheManager.clear();
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(Res20240914));
    const openURL = jest.spyOn(URL, 'openURL');
    const Page = await renderPage();

    await waitRender(async () => {
      const selfService = await Page.findByTestId(
        UITestID.car_testid_page_list_self_service_entry,
      );
      expect(selfService).toBeTruthy();
      await act(() => {
        fireEvent(selfService, 'onPress');
      });
      expect(openURL).toBeCalledWith(
        'https://m.ctrip.com/tangram/MTMyNDQ4?ctm_ref=vactang_page_132448&isHideNavBar=YES&apppgid=10650039395&statusBarStyle=1&channelid=236720',
      );
    });
  },
);

test(
  createInterTestName({
    testId: [3531144],
    name: '新无忧租tab下顶部静态品宣',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, null);
    listPagingAgent.cacheManager.clear();
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(Res20240914));
    const openURL = jest.spyOn(URL, 'openURL');
    const Page = await renderPage();

    await waitRender(async () => {
      const easyLife = await Page.findByTestId(
        UITestID.car_testid_page_list_easylife_entry,
      );
      expect(easyLife).toBeTruthy();
      await act(() => {
        fireEvent(easyLife, 'onPress');
      });
      expect(openURL).toBeCalledWith(
        'https://m.ctrip.com/tangram/OTI2MjU=?ctm_ref=vactang_page_92625&isHideNavBar=YES&apppgid=10650039395&statusBarStyle=1&channelid=238407',
      );
    });
  },
);

test(
  createInterTestName({
    testId: [489725],
    name: '列表底部已展示全部车型',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, null);
    listPagingAgent.cacheManager.clear();
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(Res**********));
    const Page = await renderPage();

    await waitRender(async () => {
      expect(Page.container).toHaveTextContent('已展示全部车型');
    });
  },
);

test(
  createInterTestName({
    testId: [489724],
    name: '列表页底部筛选条件回显',
  }),
  async () => {
    // Apptype ISD
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    ListReqAndResData.setData(ListReqAndResData.keyList.listProductRes, null);
    listPagingAgent.cacheManager.clear();
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(Res**********));
    const Page = await renderPage();

    await waitRender(async () => {
      const filterItemsModule = await Page.findByTestId(
        UITestID.car_testid_page_list_selected_filter_items,
      );
      expect(filterItemsModule).toBeTruthy();
      expect(filterItemsModule).toHaveTextContent('自助取还');
      expect(filterItemsModule).toHaveTextContent('免费取消');
      expect(filterItemsModule).toHaveTextContent('清除');
    });
  },
);
