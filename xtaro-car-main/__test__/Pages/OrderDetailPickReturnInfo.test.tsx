import { act, waitFor } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';

import { AppContext, Utils, CarStorage } from '../../src/pages/xcar/Util/Index';
import {
  createPage,
  createInterTestName,
  pressWithTestId,
} from '../PageProvider';
import UITestId from '../../src/pages/xcar/Constants/UITestID';
import { waitRender } from '../testHelpers';
// import * as HomeSelectors from '../../src/pages/xcar/Global/Cache/HomeSelectors';

// @ts-ignore 禁止Redux Log
// __DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '36510166399',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { name: 1 }, initialState);
};

describe('取还车信息', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test(
    createInterTestName({
      testId: [408040, 408042, 408043, 408058, 408053, 408045],
      name: '取还车信息',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        // Apptype ISD
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);

        await waitFor(async () => {
          // 408040 408042 408043 下单时取车时间 日期 星期 时间； 跨年加年份
          expect(Page.container).toHaveTextContent(
            '2022年12月30日 15:30 - 2023年1月1日 15:30',
          );
          expect(Page.container).toHaveTextContent('共2天');
          expect(Page.container).toHaveTextContent(
            '取店员免费送车上门凤凰国际机场T1航站楼',
          );
          expect(Page.container).toHaveTextContent(
            '还店员免费上门取车凤凰国际机场T1航站楼',
          );
          //
        });
        const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
        await pressWithTestId(
          Page,
          UITestId.car_testid_page_order_detail_pick_return_map,
        );
        await waitFor(() => {
          expect(pagePushFunc).toBeCalledWith('Guide', {
            pageParam: expect.objectContaining({
              selectedId: 'dropoff',
            }),
          });
        });
      });
    },
  );

  test(
    createInterTestName({
      testId: [408046],
      name: '零散小时展示',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        // Apptype ISD
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);

        await waitFor(() => {
          // 408040 408042 408043 下单时取车时间 日期 星期 时间； 跨年加年份
          expect(Page.container).toHaveTextContent(
            '2022年12月30日 15:30 - 2023年1月1日 16:30',
          );
          expect(Page.container).toHaveTextContent('共2天1小时');
        });
      });
    },
  );

  test(
    createInterTestName({
      testId: [408047],
      name: '跳转地图指引页 - 待支付',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        // Apptype ISD
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);

        await waitFor(async () => {
          await pressWithTestId(
            Page,
            UITestId.car_testid_page_order_detail_pick_return_map,
          );
          const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
          expect(pagePushFunc).toHaveBeenLastCalledWith('Guide', {
            pageParam: expect.objectContaining({
              selectedId: 'pickup',
            }),
          });
        });
      });
    },
  );

  test(
    createInterTestName({
      testId: [408048],
      name: '跳转地图指引页 - 已确认',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        // Apptype ISD
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);

        await waitFor(async () => {
          await pressWithTestId(
            Page,
            UITestId.car_testid_page_order_detail_pick_return_map,
          );
          const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
          expect(pagePushFunc).toHaveBeenLastCalledWith('Guide', {
            pageParam: expect.objectContaining({
              selectedId: 'pickup',
            }),
          });
        });
      });
    },
  );

  test(
    createInterTestName({
      testId: [408049],
      name: '跳转地图指引页 - 用车中',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408077,
            },
            storageCardsTitle: [],
          },
        };
        // Apptype ISD
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);

        await waitRender(async () => {
          await pressWithTestId(
            Page,
            UITestId.car_testid_page_order_detail_pick_return_map,
          );
          const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
          expect(pagePushFunc).toHaveBeenLastCalledWith('Guide', {
            pageParam: expect.objectContaining({
              selectedId: 'dropoff',
            }),
          });
        });
      });
    },
  );

  test(
    createInterTestName({
      testId: [408050],
      name: '跳转地图指引页 - 已完成',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 408077,
          },
          storageCardsTitle: [],
        },
      };
      // Apptype ISD
      const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const Page = await renderPage(state);
      await waitRender(async () => {
        await pressWithTestId(
          Page,
          UITestId.car_testid_page_order_detail_pick_return_map,
        );
        expect(pagePushFunc).toHaveBeenLastCalledWith('Guide', {
          pageParam: expect.objectContaining({
            selectedId: 'dropoff',
          }),
        });
      });
    },
  );
});
