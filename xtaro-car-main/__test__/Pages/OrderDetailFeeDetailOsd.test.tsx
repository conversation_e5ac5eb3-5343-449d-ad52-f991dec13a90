import { fireEvent } from '@testing-library/react-native';
import { AppContext, Utils, CarFetch } from '../../src/pages/xcar/Util/Index';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { renderWithAction } from '../testHelpers';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, {}, initialState);
};

const extraData = {
  feeDetailInfo: {
    chargesInfos: [
      {
        title: '车辆租金+高级套餐',
        extraDescription: {
          title: '说明弹窗',
          desc: '您在购买过程中放弃了{项目xxx}，不再享有该保障，已为您更新套餐。',
        },
      },
      {
        title: '基础租车费用',
        description: '',
        code: 'Car',
        type: 8,
        size: '约¥456×1天',
        currencyCode: 'CNY',
        currentTotalPrice: 456,
        payMode: 1,
        items: [
          {
            title: '基础租车费用',
            description:
              '满油取还、基本租车费用、税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税、不限里程',
            descList: [
              '满油取还、基本租车费用、税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税、不限里程',
            ],
            code: 'Car',
            type: 8,
          },
          {
            title: '超级碰撞盗抢保障',
            code: 'Insurance',
            type: 1,
          },
        ],
      },
      {
        title: '异地还车费',
        code: 'OnewayFee',
        type: 15,
        include: true,
        currencyCode: 'CNY',
        currentTotalPrice: 633,
        showFree: false,
        showPrice: '¥633',
        payMode: 2,
      },
      {
        title: '安心补充险',
        code: 'MP18022460PK00131278',
        size: '¥50×1天',
        currencyCode: 'CNY',
        currentTotalPrice: 50,
      },
      {
        title: '驾乘意外险',
        code: 'MP18021541PK00080231',
        size: '¥30×1天',
        currencyCode: 'CNY',
        currentTotalPrice: 30,
      },
    ],
    equipmentInfos: [],
    activityInfo: {},
    couponInfos: [],
    notIncludeCharges: {
      title: '以下费用不包含在全额中，需要到店支付相应费用及税费：',
      subTitle: '收费项目|数量|每日单价',
      description: '总价',
      code: 'NotInclude',
      type: 106,
      currencyCode: 'CNY',
      currentTotalPrice: 204,
      localCurrencyCode: 'USD',
      localTotalPrice: 27.98,
      payMode: 1,
      sortNum: 1,
      items: [
        {
          title: '婴儿座椅',
          size: '1',
          currencyCode: 'CNY',
          currentDailyPrice: 102,
          localCurrencyCode: 'USD',
          localDailyPrice: 13.99,
        },
        {
          title: '儿童座椅',
          size: '1',
          currencyCode: 'CNY',
          currentDailyPrice: 102,
          localCurrencyCode: 'USD',
          localDailyPrice: 13.99,
        },
      ],
      notices: [
        '所列价格仅供参考，以门店的实际价格为准。',
        '附加产品仅预约，不保障实际库存。',
      ],
    },
    chargesSummary: {
      title: '全额',
      code: 'Summary',
      type: 103,
      currencyCode: 'CNY',
      currentTotalPrice: 456,
      items: [
        {
          title: '到店支付 约',
          code: '1',
          currencyCode: 'CNY',
          currentTotalPrice: 456,
        },
        {
          title: '当地货币',
          code: '6',
          currencyCode: 'USD',
          currentTotalPrice: 62.45,
        },
      ],
      notices: [
        '* 上述金额是按当前汇率换算的，实际到店支付费用以门店当天汇率为准。',
      ],
    },
    rentalTerm: 1,
  },
  responseStatus: {
    timestamp: '/Date(1695707542498+0800)/',
    ack: 'Success',
    errors: [],
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '8928471412685501501',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a63ce33-471029-1911274',
      },
    ],
  },
  baseResponse: {
    isSuccess: true,
    code: 'unknown',
    returnMsg: 'success',
    requestId: '',
    cost: 81,
  },
  priceDesc: {
    aboutDeposit: {
      desc: '取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为US$ 200.00（约¥1,462.00）,请保证可用额度足以支付押金。押金预计会在还车后30-60天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。',
    },
    possibleChangeList: [
      {
        title: '里程政策',
        description: '不限里程',
      },
      {
        title: '燃油政策',
        description: '满油取还',
      },
      {
        title: '年龄要求',
        description: '驾驶员年龄要求：20-75周岁',
      },
      {
        title: '额外驾驶员',
        description:
          '不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。',
      },
      {
        title: '营业时间外取还车',
        description:
          '如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。',
      },
      {
        title: '当地费用',
        description:
          '订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。',
      },
    ],
  },
};
describe('境外订详 - 费用明细弹层', () => {
  test(
    createInterTestName({
      testId: [
        458409, 458438, 458434, 458439, 458440, 458441, 458442, 458436, 3898315,
        3898301, 3898308, 3898287, 3898294, 3898322,
      ],
      name: '境外订详 - 常规',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

      const Page = await renderPage(state);

      await renderWithAction({
        action: async () => {
          const priceDetailBtn = await Page.findAllByTestId(
            UITestID.car_testid_page_order_price_detail,
          );
          fireEvent.press(priceDetailBtn[0]);
        },
        expect: async () => {
          const priceModal = await Page.findByTestId(
            UITestID.car_osd_orderdetail_price_modal,
          );
          expect(priceModal).toBeOnTheScreen();
          expect(priceModal).toHaveTextContent('优惠券');
          expect(priceModal).toHaveTextContent('- ¥50');
          expect(priceModal).toHaveTextContent('国际租车满减券');
          expect(priceModal).toHaveTextContent(
            '车辆租金+基础套餐基础租车费用¥546约¥182×3天基础租车费用等油取还、基本租车费用、税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税、不限里程',
          );
          expect(priceModal).toHaveTextContent(
            '*上述金额是按当前汇率换算的，实际到店支付费用以门店当天汇率为准',
          );
          expect(priceModal).toHaveTextContent('在线预付 ¥496');
        },
      });
    },
  );
  test(
    createInterTestName({
      testId: [458409, 458437, 458435, 3898245, 3898252],
      name: '境外订详 - 儿童座椅',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
      jest
        .spyOn(CarFetch, 'queryOrderPriceInfo')
        .mockResolvedValue(Promise.resolve(extraData));
      const Page = await renderPage(state);

      await renderWithAction({
        action: async () => {
          const priceDetailBtn = await Page.findAllByTestId(
            UITestID.car_testid_page_order_price_detail,
          );
          fireEvent.press(priceDetailBtn[0]);

          const descBtn = await Page.findByTestId(
            `${
              UITestID.car_testid_feedetail_modal_fee_item_help
            }_${'车辆租金+高级套餐'}`,
          );
          fireEvent.press(descBtn);
        },
        expect: async () => {
          const priceModal = await Page.findByTestId(
            UITestID.car_osd_orderdetail_price_modal,
          );
          expect(priceModal).toBeOnTheScreen();
          expect(priceModal).toHaveTextContent('异地还车费¥633');
          expect(priceModal).toHaveTextContent(
            '安心补充险¥50¥50×1天驾乘意外险¥30¥30×1天',
          );
          expect(priceModal).toHaveTextContent(
            '上述金额是按当前汇率换算的，实际到店支付费用以门店当天汇率为准。',
          );

          const descModal = await Page.findByTestId(
            UITestID.car_testid_feetitleexplain_modal_view,
          );
          expect(descModal).toHaveTextContent(
            '您在购买过程中放弃了{项目xxx}，不再享有该保障，已为您更新套餐。',
          );
        },
      });
    },
  );
});
