import { Image, TouchableOpacity } from 'react-native';
import PhotoBrowser from '@c2x/apis/PhotoBrowser';
import URL from '@c2x/apis/URL';
import { fireEvent } from '@testing-library/react-native';
import { AppContext, CarFetch, Utils } from '../../src/pages/xcar/Util/Index';
import { color } from '../../src/pages/xcar/Common/src/Tokens';
import { timestamps } from '../../src/pages/xcar/Common/src/Utils/src/Constants';
import Album from '../../src/pages/xcar/Containers/Album/AlbumContainer';
import { createPage, createInterTestName } from '../PageProvider';
import { waitRender } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';

const dmt2Res = require('../../__mocks__/restful/18631/queryMultimediaAlbum/20231106.json');

// 页面初始化
const renderPage = async (initialState = undefined, props = {}) => {
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Album',
  });

  return createPage(Album, { ...props, vehicleCode: '12345' }, initialState);
};

test(
  createInterTestName({
    testId: [595991],
    name: 'VR',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    jest
      .spyOn(CarFetch, 'queryMultimediaAlbum')
      .mockImplementation(() => Promise.resolve(dmt2Res));
    const Page = await renderPage();

    await waitRender(async () => {
      const groupItems = await Page.findAllByTestId(
        UITestID.car_testid_page_album_media_group_item,
      );

      const vrGroupItem = await groupItems[1].findAllByType(TouchableOpacity);

      await fireEvent.press(vrGroupItem[0]);

      const openURL = jest.spyOn(URL, 'openURL');
      expect(openURL).toBeCalledWith(
        'https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4851',
      );
    });
  },
);

test(
  createInterTestName({
    testId: [595990],
    name: '图片',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    jest
      .spyOn(CarFetch, 'queryMultimediaAlbum')
      .mockImplementation(() => Promise.resolve(dmt2Res));
    const Page = await renderPage();

    await waitRender(async () => {
      const groupItems = await Page.findAllByTestId(
        UITestID.car_testid_page_album_media_group_item,
      );

      const appearanceImages = await groupItems[2].findAllByType(Image);

      expect(appearanceImages.length).toEqual(10);

      const moreBtn = await groupItems[2].findAllByType(TouchableOpacity);

      // 点击更多按钮
      await fireEvent.press(moreBtn[10]);

      const allAppearanceImages = await groupItems[2].findAllByType(Image);

      expect(allAppearanceImages.length).toEqual(11);
    });

    await waitRender(async () => {
      const groupItems = await Page.findAllByTestId(
        UITestID.car_testid_page_album_media_group_item,
      );

      const imageBtns = await groupItems[2].findAllByType(TouchableOpacity);

      // 点击第一张图片
      await fireEvent.press(imageBtns[0]);

      const showWithScrollCallback = jest.spyOn(
        PhotoBrowser,
        'showWithScrollCallback',
      );

      expect(showWithScrollCallback).toBeCalled();
    });
  },
);

test(
  createInterTestName({
    testId: [595988],
    name: '底部bar',
  }),
  async () => {
    const lookPrice = jest.fn();
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    jest
      .spyOn(CarFetch, 'queryMultimediaAlbum')
      .mockImplementation(() => Promise.resolve(dmt2Res));
    const Page = await renderPage(
      {},
      {
        price: 193,
        minTotalPrice: 333,
        minTotalPriceDesc: '总价',
        minTotalPriceOtherDesc: '起',
        lookPrice,
      },
    );

    await waitRender(async () => {
      const footer = await Page.findByTestId(
        UITestID.car_testid_page_album_footer,
      );

      expect(footer).toHaveTextContent('日均¥193总价¥333起');

      const footerBtn = await Page.findByTestId(
        UITestID.car_testid_comp_bookbar_button,
      );

      await fireEvent.press(footerBtn);

      // @ts-ignore
      expect(global.popCount).toEqual(1);
      expect(lookPrice).toBeCalled();
    });
  },
);

test(
  createInterTestName({
    testId: [595985],
    name: '分类',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockImplementation(() => false);
    jest.spyOn(Utils, 'isCtripIsd').mockImplementation(() => true);
    jest
      .spyOn(CarFetch, 'queryMultimediaAlbum')
      .mockImplementation(() => Promise.resolve(dmt2Res));
    const Page = await renderPage();

    await waitRender(async () => {
      const menuView = await Page.findByTestId(
        UITestID.car_testid_page_album_menu,
      );

      expect(menuView).toHaveTextContent(
        '全部(31)视频(2)VR(1)外观(11)前排(2)后排(15)',
      );

      const allMenu = await menuView.findAllByType(TouchableOpacity);
      const fontMenu = allMenu[4];

      // 全部menu选中， 前排Menu不选中
      expect(allMenu[0]).toHaveStyle({
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: color.C_006ff6,
      });
      expect(fontMenu).not.toHaveStyle({
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: color.C_006ff6,
      });

      // 点击前排
      await fireEvent.press(fontMenu);

      // 全部menu不选中， 前排Menu选中
      expect(allMenu[0]).not.toHaveStyle({
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: color.C_006ff6,
      });
      expect(fontMenu).toHaveStyle({
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: color.C_006ff6,
      });

      const groupItems = await Page.findAllByTestId(
        UITestID.car_testid_page_album_media_group_item,
      );

      expect(groupItems.length).toEqual(1);

      const images = await groupItems[0].findAllByType(Image);

      expect(fontMenu).toHaveStyle({
        borderStyle: 'solid',
        borderWidth: 1,
        borderColor: color.C_006ff6,
      });

      expect(images[0].props.source.uri).toEqual(
        `https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg?timestamps=${timestamps}&proc=resize/m_w,w_1500,h_0,1696`,
      );
      expect(images[1].props.source.uri).toEqual(
        `https://dimg04.c-ctrip.com/images/0RV0r12000b0aujvo9602.jpg?timestamps=${timestamps}&proc=resize/m_w,w_1500,h_0,1696`,
      );
    });
  },
);
