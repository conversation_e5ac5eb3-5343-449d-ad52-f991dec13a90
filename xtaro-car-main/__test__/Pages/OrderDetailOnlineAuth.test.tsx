import { fireEvent } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import { AppContext, Utils } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { renderWithAction, waitRender } from '../testHelpers';

// @ts-ignore 禁止Redux Log
// __DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    saveIsd: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));

const renderOrderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '36510166399',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { name: 1 }, initialState);
};

describe('上传证件模块展示', () => {
  test(
    createInterTestName({
      testId: [17723, 17726],
      name: '确认入口正确, 未验证证件',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 408077,
          },
          storageCardsTitle: [],
        },
      };
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
      const Page = await renderOrderPage(state);
      await waitRender(() => {
        expect(Page.container).toHaveTextContent('认证驾驶员，免部分取车手续');
        expect(Page.container).toHaveTextContent('去认证');
      });
      await renderWithAction({
        action: async () => {
          const authButton = await Page.findAllByText('去认证');
          fireEvent.press(authButton[0]);
        },
        expect: () => {
          expect(pagePushFunc).toBeCalledWith('OnlineAuth');
        },
      });
    },
  );

  test(
    createInterTestName({
      testId: [17725],
      name: '已验证部分证件',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 408077,
          },
          storageCardsTitle: [],
        },
      };
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const Page = await renderOrderPage(state);
      await waitRender(() => {
        expect(Page.container).toHaveTextContent('认证驾驶员，免部分取车手续');
        expect(Page.container).toHaveTextContent('继续认证');
      });
    },
  );

  test(
    createInterTestName({
      testId: [17724, 17722], // 服务返回文案，复用case
      name: '已验证两证未授权',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 408077,
          },
          storageCardsTitle: [],
        },
      };
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const Page = await renderOrderPage(state);
      await waitRender(() => {
        expect(Page.container).toHaveTextContent('认证驾驶员，免部分取车手续');
        expect(Page.container).toHaveTextContent('去授权');
      });
    },
  );
});
