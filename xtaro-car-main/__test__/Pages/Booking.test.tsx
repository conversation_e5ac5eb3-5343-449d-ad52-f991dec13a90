import { Image, TouchableOpacity } from 'react-native';
import CRNPage from '@c2x/components/Page';
import URL from '@c2x/apis/URL';
import { fireEvent, waitFor, act } from '@testing-library/react-native';
import {
  Utils,
  GetAB,
  User,
  AppContext,
  CarABTesting,
} from '../../src/pages/xcar/Util/Index';
import {
  createPage,
  createInterTestName,
  TEST_TIMEOUT,
  pressWithTestId,
} from '../PageProvider';
import { UITestID, ImageUrl } from '../../src/pages/xcar/Constants/Index';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}) => {
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);

  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};

test(
  createInterTestName({
    testId: [
      296410, 296408, 296405, 296411, 296407, 296406, 296437, 296442, 296409,
    ],
    name: '车辆信息等',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));

    const Page = await renderPage();
    // 296410 预期两年内车龄 倒车影像 倒车雷达
    const vehicleInfo = await Page.findByTestId(
      UITestID.car_testid_comp_booking_vehicle_info,
    );

    expect(vehicleInfo).toHaveTextContent(/两年内车龄/);
    expect(vehicleInfo).toHaveTextContent(/倒车影像/);
    expect(vehicleInfo).toHaveTextContent(/倒车雷达/);
    // 296408 供应商展示
    expect(vehicleInfo).toHaveTextContent(/申恺租车/);
    // 296409
    const vendorPressBtn = await vehicleInfo.findAllByType(TouchableOpacity)[0];
    await act(() => {
      fireEvent.press(vendorPressBtn);
    });
    const vehicleModal = await Page.findAllByTestId(
      UITestID.car_testid_comp_booking_vehicle_modal,
    );
    expect(vehicleModal).toBeTruthy();

    // 296405 展示新能源标识
    const allImages = vehicleInfo.findAllByType(Image);
    const lessLogo = allImages?.find(
      image => image.props.source.uri === ImageUrl.lessLowCarbon,
    );
    expect(lessLogo).toBeTruthy();
    // 296411 牌照信息展示
    expect(vehicleInfo).toHaveTextContent(/沪牌/);

    // 296407
    expect(vehicleInfo).toHaveTextContent(/荣威RX5新能源/);
    // 296406 车辆参考样图
    const referenceImage = allImages?.find(
      image =>
        image.props.source?.uri ===
        'https://dimg04.c-ctrip.com/images/0RV6y12000au0o8az9D1B.png?timestamps=20230329&proc=resize/m_w,w_250,h_0,7CBD',
    );
    expect(referenceImage).toBeTruthy();

    // 296437
    const couponAndDeposit = Page.queryByTestId(
      UITestID.car_testid_comp_booking_NewCouponAndDeposit,
    );
    expect(couponAndDeposit).toHaveTextContent(/优惠暂无可享优惠/);
    const couponAndDepositTouchable = await couponAndDeposit.findAllByType(
      TouchableOpacity,
    )[0];
    await act(() => {
      fireEvent.press(couponAndDepositTouchable);
    });
    // 296444
    expect(couponAndDeposit).toHaveTextContent(/暂无可用优惠券/);
    // 296442
    expect(couponAndDeposit).toHaveTextContent(/活动无可享活动/);
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: 296412,
    name: '激励信息模块',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    waitFor(() => {
      const advantageDom = Page.queryByTestId(
        UITestID.car_testid_comp_booking_advantageDom,
      );
      expect(advantageDom).toHaveTextContent(/取车前可免费取消/);
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: 296481,
    name: '填写页-门店政策跳转-点击【门店政策】',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
    const Page = await renderPage();
    waitFor(() => {
      const storePolicy = Page.queryByTestId(
        UITestID.car_testid_page_booking_store_policy,
      );
      fireEvent.press(storePolicy);
      expect(pagePushFunc).toBeCalledWith('Policy');
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: 296482,
    name: '填写页-预定条款跳转-点击【预定条款】',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
    const Page = await renderPage();
    waitFor(() => {
      const isdAgreementTitle = Page.queryByTestId(
        UITestID.car_testid_page_booking_isd_agreement_title,
      );
      fireEvent.press(isdAgreementTitle);
      expect(pagePushFunc).toBeCalledWith('IsdAgreement');
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: 296483,
    name: '填写页-个人信息说明跳转-点击【个人信息说明】',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    waitFor(() => {
      const approveExplain = Page.queryByTestId(
        UITestID.car_testid_page_booking_approve_explain,
      );
      fireEvent.press(approveExplain);
      const approveExplainModal = Page.queryByTestId(
        UITestID.car_testid_page_booking_approve_explain_modal,
      );
      expect(approveExplainModal).toBeTruthy();
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: 296485,
    name: '填写页-程信分免押服务协议跳转-点击【程信分免押服务协议】',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    waitFor(() => {
      const bookChengXinFenAgreement = Page.queryByTestId(
        UITestID.car_testid_page_booking_cheng_xin_fen_agreement,
      );
      fireEvent.press(bookChengXinFenAgreement);
      const openURL = jest.spyOn(URL, 'openURL');
      expect(openURL).toBeCalledWith(
        'https://pages.trip.com/cars/doc/CreditRentAggrement.doc',
      );
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [489817, 489819, 489823],
    name: '填写页-优惠券与活动',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));
    const Page = await renderPage();
    // 489817 489819
    waitFor(() => {
      const couponAndActivity = Page.queryByTestId(
        UITestID.car_testid_comp_booking_new_couponAndActivity,
      );
      expect(couponAndActivity).toHaveTextContent(/优惠/);
      fireEvent.press(couponAndActivity);
      expect(couponAndActivity).toHaveTextContent(/优惠券/);
      expect(couponAndActivity).toHaveTextContent(/活动/);
      // 489819
      expect(couponAndActivity).toHaveTextContent(/境内外租车立减券/);
      // 489823
      expect(couponAndActivity).toHaveTextContent(/周边游特惠/);
    });
  },
  TEST_TIMEOUT,
);
