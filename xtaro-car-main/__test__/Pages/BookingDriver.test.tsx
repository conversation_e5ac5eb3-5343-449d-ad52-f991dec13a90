import { act, fireEvent } from '@testing-library/react-native';
import CRNPage from '@c2x/components/Page';
import Toast from '../../src/pages/xcar/Common/src/Components/Basic/Toast/src';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';
import { AppContext, Utils, GetAB, User } from '../../src/pages/xcar/Util/Index';
import {
  createPage,
  createInterTestName,
  updateReduxState,
} from '../PageProvider';
import { renderWithAction, waitRender } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
// @ts-ignore 禁止Redux Log
__DEV__ = false;

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Util/CarABTesting/GetAB', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/CarABTesting/GetAB'),
}));
// Apptype ISD
jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
jest
  .spyOn(User, 'isLogin')
  .mockImplementation(() => new Promise(resolve => resolve(true)));

// 页面初始化
const renderPage = async (initialState = {}) => {
  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};

const renderDriverListPage = async (initialState = {}) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/DriverListContainer').default;
  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(container, {}, initialState);
};

const renderDriverEditPage = async (initialState = {}) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/DriverEditContainer').default;
  return createPage(container, {}, initialState);
};

afterEach(() => {
  jest.clearAllMocks();
});

test(
  createInterTestName({
    testId: [296417, 296422, 296424, 296416, 296436, 296421, 296428, 296423],
    name: '填写页驾驶员信息',
  }),
  async () => {
    const BookingPage = await renderPage();
    const DriverListPage = await renderDriverListPage();
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
    // {296417} 展示 身份证+身份证号18位
    await renderWithAction({
      action: async () => {
        const addDriverBtn = await BookingPage.findByText('选择驾驶员');
        fireEvent.press(addDriverBtn);
      },
      expect: () => {
        // 296422
        expect(pagePushFunc).toBeCalledWith('DriverList');
      },
    });

    await waitRender(() => {
      // {296424} 身份证及联系方式回显结构
      expect(DriverListPage.container).toHaveTextContent(
        '340826 1990 1223 1110',
      );
      expect(DriverListPage.container).toHaveTextContent('135 2100 2100');
    });
    await renderWithAction({
      action: async () => {
        const driverItem = await DriverListPage.findByText('郭玉庆');
        fireEvent.press(driverItem);
      },
      expect: () => {
        // 296417 预期：展示 身份证+身份证号18位
        expect(BookingPage.container).toHaveTextContent(
          '身份证430723 1973 0609 501X',
        );
        // 296416 预期：驾驶员姓名后面展示“可享免押”标签
        expect(BookingPage.container).toHaveTextContent('可享免押');

        // 296436 预期：手机号下方展示文案
        expect(BookingPage.container).toHaveTextContent(
          '凭驾驶员本人有效身份证+有效驾驶证取车，驾龄需1年以上',
        );

        // 296423 则返回填写页后该选项顺序变为第1个，选中状态
        expect(BookingPage.container).toHaveTextContent('郭玉庆陈安阳汪超二');
      },
    });
    // 296421 点击【增加多名驾驶员】
    await renderWithAction({
      action: async () => {
        const driverItem = await BookingPage.findByText('增加多名驾驶员');
        fireEvent.press(driverItem);
      },
      expect: () => {
        expect(BookingPage.container).toHaveTextContent(
          '默认仅支持一名驾驶员。',
        );
      },
    });

    // 296428
    await renderWithAction({
      action: async () => {
        expect(DriverListPage.container).toHaveTextContent('郭玉庆');
        const driverDeleteItems = await DriverListPage.findAllByText('删除');
        fireEvent.press(driverDeleteItems[0]);
        // 二次点击确认删除
        fireEvent.press(driverDeleteItems[0]);
      },
      expect: async () => {
        const ToastFunc = jest.spyOn(Toast, 'show');
        expect(ToastFunc).toBeCalledWith('删除成功', 1);
      },
    });
  },
);

test(
  createInterTestName({
    testId: [296429, 296430],
    name: '填写页驾驶员信息',
  }),
  async () => {
    const DriverListPage = await renderDriverListPage();

    // 296429 进入"选择驾驶员"页面，点击【新增驾驶员】按钮
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');
    const pagePopFunc = jest.spyOn(CRNPage.prototype, 'pop');
    await renderWithAction({
      action: async () => {
        const addDriverButton = await DriverListPage.findByText('新增驾驶员');
        fireEvent.press(addDriverButton);
      },
      expect: () => {
        expect(pagePushFunc).toBeCalledWith('DriverEdit', {
          fromurl: 'Booking',
          isAdd: true,
        });
      },
    });
    // 296430 进入"新增驾驶员"页面，点击【关闭】按钮
    await renderWithAction({
      action: async () => {
        const EditPage = await renderDriverEditPage();
        const driverEditClose = await EditPage.findByTestId(
          UITestID.car_testid_comp_driver_edit_close,
        );
        fireEvent.press(driverEditClose);
      },
      expect: () => {
        expect(pagePopFunc).toBeCalled();
      },
    });
  },
);

test(
  createInterTestName({
    testId: [296414, 296413],
    name: '填写页驾驶员信息',
  }),
  async () => {
    let BookingPage;
    // 296414 若推荐选中的驾驶员证件类型非身份证时
    await renderWithAction({
      action: async () => {
        BookingPage = await renderPage();
        updateReduxState(darft => {
          darft.DriverList.passenger = {
            nationalityName: '中国',
            lastName: 'ctrip',
            firstName: 'test',
            age: 29,
            nationality: 'CN',
            mobile: '15500010002',
            countryCode: '86',
            isDefault: true,
            isSelf: false,
            sortExt:
              'isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:-1689066029749|selfSort:0|requestPassenger:-1',
            isRecommend: true,
            fullName: '测试',
            birthday: '1994-02-28',
            passengerId: '269614',
            certificateList: [
              { certificateType: '7', certificateNo: 'W22334D2221' },
            ],
            email: '',
            isCreditQualified: false,
          };
        });
      },
      expect: async () => {
        expect(BookingPage.container).toHaveTextContent('点击补充证件信息');
      },
    });
  },
);
