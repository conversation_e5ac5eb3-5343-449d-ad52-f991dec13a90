import { fireEvent, act, waitFor } from '@testing-library/react-native';
import URL from '@c2x/apis/URL';
import { Utils, User, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { Platform, UITestID } from '../../src/pages/xcar/Constants/Index';
import { waitRender } from '../testHelpers';
// @ts-ignore 禁止Redux Log
__DEV__ = false;
const resOSDQueryOrder408010 = require('../../__mocks__/restful/18862/OSDQueryOrder/407956.json');
// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const OrderDetailPage =
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  return createPage(
    OrderDetailPage,
    {
      isLoading: true,
    },
    initialState,
  );
};
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    saveIsd: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

describe('售前未加购保险', () => {
  const openURL = jest.spyOn(Utils, 'openUrlWithTicket');
  test(
    createInterTestName({
      testId: [408131, 408132, 408133],
      name: '售前未加购保险',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 1234567,
          },
          storageCardsTitle: [],
        },
      };
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(false);
      jest.spyOn(User, 'isLoginSync').mockReturnValue(true);

      const Page = await renderPage(state);

      const insuranceDesc = await Page.findByTestId(
        UITestID.car_testid_order_detail_insurance_desc,
      );
      expect(insuranceDesc).toHaveTextContent('专业道路救援服务');
      expect(insuranceDesc).toHaveTextContent('随车财务损失');
      await act(() => {
        fireEvent.press(insuranceDesc);
      });
      const url = `${
        Platform.CAR_CROSS_URL.TravelInsuranceAgreement.ISD
      }&sparam=${encodeURIComponent(JSON.stringify({ type: 1 }))}`;
      expect(openURL).toBeCalledWith(url);
    },
    TEST_TIMEOUT,
  );
});

describe('售前未加购保险跳转保代页面', () => {
  const openURL = jest.spyOn(URL, 'openURL');
  test(
    createInterTestName({
      testId: [408139, 408128, 408129, 408134],
      name: '售前未加购保险跳转保代页面',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 1234567,
          },
          storageCardsTitle: [],
        },
      };
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(false);
      jest.spyOn(User, 'isLoginSync').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(resOSDQueryOrder408010));
      const Page = await renderPage(state);

      await waitRender(() => {
        expect(Page.container).toHaveTextContent('人身及财物险');

        expect(Page.container).toHaveTextContent('人身意外保额');

        expect(Page.container).toHaveTextContent('集中隔离津贴');
      });

      const upgradeButton = await Page.findByText('去购买');
      await act(() => {
        fireEvent.press(upgradeButton);
      });
      const url =
        'https://m.fws.qa.nt.ctripcorp.com/webapp/flight/insurance/insurance_confirm.html?high=1&isHideNavBar=YES&bizCode=504&token=c64bb60c8a624f2a8c7c49834d4cdd95&callbackType=1&eventName=orderInsurancePayCallback';
      await waitFor(
        () => {
          expect(openURL).toBeCalledWith(url);
        },
        { timeout: 3500 },
      );
    },
    TEST_TIMEOUT,
  );
});

describe('售前已加购保险', () => {
  test(
    createInterTestName({
      testId: [
        408143, 408147, 408144, 408145, 408146, 408151, 408148, 408149, 408150,
        408127,
      ],
      name: '售前已加购保险',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 1234567,
          },
          storageCardsTitle: [],
        },
      };
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(false);
      jest.spyOn(User, 'isLoginSync').mockReturnValue(true);
      const openURL = jest.spyOn(Utils, 'openUrlWithTicket');
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(resOSDQueryOrder408010));
      const Page = await renderPage(state);
      await waitRender(async () => {
        const title = await Page.findByText('人身及财物险');
        const pingAnTag = await Page.findByText('平安财险承保');
        expect(title).toBeTruthy();
        expect(pingAnTag).toBeTruthy();
      });

      const upgradeButton = await Page.queryByText('去购买');
      expect(upgradeButton).toBeFalsy();

      const insuranceDesc = await Page.findByTestId(
        UITestID.car_testid_order_detail_insurance_desc,
      );
      expect(insuranceDesc).toHaveTextContent('人身意外保额');
      expect(insuranceDesc).toHaveTextContent('集中隔离津贴');
      expect(insuranceDesc).toHaveTextContent('随车财务损失');
      expect(insuranceDesc).toHaveTextContent('专业道路救援服务');

      await act(() => {
        fireEvent.press(insuranceDesc);
      });

      const url = `${
        Platform.CAR_CROSS_URL.TravelInsuranceAgreement.ISD
      }&sparam=${encodeURIComponent(JSON.stringify({ type: 1 }))}`;
      expect(openURL).toBeCalledWith(url);
    },
    TEST_TIMEOUT,
  );
});
