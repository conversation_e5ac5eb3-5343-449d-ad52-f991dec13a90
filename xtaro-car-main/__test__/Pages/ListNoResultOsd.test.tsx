/* eslint-disable @typescript-eslint/naming-convention */
import { TouchableOpacity, Image } from 'react-native';
import { fireEvent, act } from '@testing-library/react-native';
import { BbkConstants } from '@ctrip/rn_com_car/dist/src/Utils';
import { Utils, CarStorage, CarFetch, GetAB } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';
import { ListReqAndResData } from '../../src/pages/xcar/Global/Cache/Index';
import { renderWithAction, waitRender } from '../testHelpers';

const resProduct903785 = require('../../__mocks__/restful/27140/queryProducts/OSD/903785.json');
const resRecommend903785 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903785.json');
const resRecommend903792 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903792.json');
const resRecommend903757 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903757.json');
const resRecommend903764 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903764.json');
const resRecommend903813 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903813.json');
const resRecommend903778 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903778.json');
const resRecommend903799 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903799.json');
const resRecommend903806 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903806.json');
const resRecommend903827 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903827.json');
const resRecommend903841 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903841.json');
const resRecommend903834 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903834.json');
const resRecommend903820 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903820.json');
const resRecommend903869 = require('../../__mocks__/restful/18631/queryRecommendProducts/OSD/903869.json');

// @ts-ignore 禁止Redux Log
__DEV__ = false;

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: false,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

afterEach(() => {
  jest.clearAllMocks();
});

test(
  createInterTestName({
    testId: [903694],
    name: '无结果-AB实验',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');

    const Page = await renderPage();
    const PageDom = Page.container;

    // 903694 C出境无结果补偿 A版 列表页
    expect(PageDom).toHaveTextContent('网络不给力');
    expect(PageDom).toHaveTextContent('请检查网络设置后再试');
    expect(PageDom).toHaveTextContent('再试一次');
  },
);

// test(
//   createInterTestName({
//     testId: [903785, 903883, 903946, 904002, 903701],
//     name: '同地因驾驶员年龄无结果',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903785));

//     const Page = await renderPage();
//     const PageDom = Page.container;
//     await waitRender(async () => {
//       // 903757 无结果提示模块
//       expect(PageDom).toHaveTextContent(
//         '您选择的驾驶员年龄18岁，暂无可预订车辆',
//       );
//       expect(PageDom).toHaveTextContent(
//         '建议您更换驾驶员，修改驾驶员年龄重新查询',
//       );
//       expect(PageDom).toHaveTextContent('修改驾驶员年龄');

//       // 903883 补偿标题
//       expect(PageDom).toHaveTextContent('以下为您推荐其他年龄要求的车辆');

//       // 903946 车型提示卡片
//       // expect(PageDom).toHaveTextContent('25岁起可租');

//       // 904002 同地-驾驶员年龄-更新到门店方式 903701列表页增加补偿逻辑
//       const vehicle = await Page.findByText(/雪佛兰 Spark/i);
//       renderWithAction({
//         action: async () => {
//           fireEvent.press(vehicle);
//         },
//         expect: async () => {
//           expect(PageDom).toHaveTextContent('年龄要求：25-80周岁');
//         },
//       });
//     });
//   },
// );

// test(
//   createInterTestName({
//     testId: [903792, 903862, 903953, 904009],
//     name: '异地因驾驶员年龄无结果',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903792));

//     const Page = await renderPage();
//     const PageDom = Page.container;
//     await waitRender(async () => {
//       // 903792 无结果提示模块
//       expect(PageDom).toHaveTextContent(
//         '您选择的驾驶员年龄99岁，暂无可预订车辆',
//       );
//       expect(PageDom).toHaveTextContent(
//         '建议您更换驾驶员，修改驾驶员年龄重新查询',
//       );
//       expect(PageDom).toHaveTextContent('修改驾驶员年龄');

//       // 903862 补偿标题
//       expect(PageDom).toHaveTextContent('以下为您推荐其他年龄要求的车辆');

//       // 903953 车型提示卡片
//       expect(PageDom).toHaveTextContent('80岁以下可租');

//       // 904009 异地-驾驶员年龄-更新到门店方式
//       const vehicle = await Page.findByText(/雪佛兰 Spark/i);
//       renderWithAction({
//         action: async () => {
//           fireEvent.press(vehicle);
//         },
//         expect: async () => {
//           expect(PageDom).toHaveTextContent('年龄要求：25-80周岁');
//         },
//       });
//     });
//   },
// );

// test(
//   createInterTestName({
//     testId: [903757, 903855],
//     name: '同地因成人儿童数无结果',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903757));

//     const Page = await renderPage();
//     const PageDom = Page.container;
//     await waitRender(async () => {
//       // 903757 无结果提示模块
//       expect(PageDom).toHaveTextContent('暂无符合人数要求的车辆');
//       expect(PageDom).toHaveTextContent('建议您修改人数要求，或预订多辆车');
//       expect(PageDom).toHaveTextContent('修改人数');

//       // 903855 补偿标题
//       expect(PageDom).toHaveTextContent('可预订以下多辆车出行');
//     });
//   },
// );

// test(
//   createInterTestName({
//     testId: [903764, 903876],
//     name: '异地因成人儿童数无结果',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903764));

//     const Page = await renderPage();
//     const PageDom = Page.container;
//     await waitRender(async () => {
//       // 903757 无结果提示模块
//       expect(PageDom).toHaveTextContent('暂无符合人数要求的车辆');
//       expect(PageDom).toHaveTextContent('建议您修改人数要求，或预订多辆车');
//       expect(PageDom).toHaveTextContent('修改人数');

//       // 903855 补偿标题
//       expect(PageDom).toHaveTextContent('可预订以下多辆车出行');
//     });
//   },
// );
// 不稳定case
// test(
//   createInterTestName({
//     testId: [903813, 903890],
//     name: '同地因取车门店营业时间无结果',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903813));

//     const Page = await renderPage();
//     const PageDom = Page.container;
//     await waitRender(async () => {
//       // 903813 无结果提示模块
//       expect(PageDom).toHaveTextContent('您选择的取车时间暂无门店营业');
//       expect(PageDom).toHaveTextContent('建议您修改取车时间');
//       expect(PageDom).toHaveTextContent('修改取车时间');

//       // 903890 补偿标题
//       expect(PageDom).toHaveTextContent('2024-01-30 10:00可取车');
//     });
//   },
// );

// test(
//   createInterTestName({
//     testId: [903778, 903904],
//     name: '同地因还车门店营业时间无结果',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903778));

//     const Page = await renderPage();
//     await waitRender(async () => {
//       const PageDom = Page.container;
//       // 903778 无结果提示模块
//       expect(PageDom).toHaveTextContent('您选择的还车时间暂无门店营业');
//       expect(PageDom).toHaveTextContent('建议您修改还车时间');
//       expect(PageDom).toHaveTextContent('修改还车时间');

//       // 903904 补偿标题
//       expect(PageDom).toHaveTextContent('2024-02-02 10:00可还车');
//     }, 5000);
//   },
// );

// test(
//   createInterTestName({
//     testId: [903799, 903897],
//     name: '异地因取车门店营业时间无结果',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903799));

//     const Page = await renderPage();
//     await waitRender(async () => {
//       const PageDom = Page.container;
//       // 903799 无结果提示模块
//       expect(PageDom).toHaveTextContent('您选择的取车时间暂无门店营业');
//       expect(PageDom).toHaveTextContent('建议您修改取车时间');
//       expect(PageDom).toHaveTextContent('修改取车时间');

//       // 903897 补偿标题
//       expect(PageDom).toHaveTextContent('2024-01-30 9:30可取车');
//     }, 5000);
//   },
// );

// test(
//   createInterTestName({
//     testId: [903806, 903911],
//     name: '异地因还车门店营业时间无结果',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903806));

//     const Page = await renderPage();
//     const PageDom = Page.container;
//     await waitRender(async () => {
//       // 903806 无结果提示模块
//       expect(PageDom).toHaveTextContent('您选择的还车时间暂无门店营业');
//       expect(PageDom).toHaveTextContent('建议您修改还车时间');
//       expect(PageDom).toHaveTextContent('修改还车时间');

//       // 903911 补偿标题
//       expect(PageDom).toHaveTextContent('2024-02-02 9:30可还车');
//     });
//   },
// );

// test(
//   createInterTestName({
//     testId: [903827, 903925, 903974, 903995],
//     name: '推荐同地更远的门店',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903827));

//     const Page = await renderPage();
//     const PageDom = Page.container;
//     await waitRender(async () => {
//       // 903827 无结果提示模块
//       expect(PageDom).toHaveTextContent('该地点附近暂无门店');
//       expect(PageDom).toHaveTextContent('建议您修改取还车地点或选择较远的门店');
//       expect(PageDom).toHaveTextContent('修改取还车地点');

//       // 903925 补偿标题
//       expect(PageDom).toHaveTextContent('以下取车门店距取车地点15.1公里起');

//       // 903974 车型卡片提示
//       expect(PageDom).toHaveTextContent(
//         '距Abu Dhabi Al Dhafra AB Apt机场23.2公里起',
//       );

//       // 903995 报价提示文案
//       const vehicle = await Page.findByText(/三菱 Attrage/i);
//       renderWithAction({
//         action: async () => {
//           fireEvent.press(vehicle);
//         },
//         expect: async () => {
//           expect(PageDom).toHaveTextContent(
//             '距Abu Dhabi Al Dhafra AB Apt机场23.2公里',
//           );
//         },
//       });
//     });
//   },
// );

// test(
//   createInterTestName({
//     testId: [903841, 903918],
//     name: '异地因提前预定期无结果',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903841));

//     const Page = await renderPage();
//     const PageDom = Page.container;
//     await waitRender(async () => {
//       // 903841 无结果提示模块
//       expect(PageDom).toHaveTextContent('您选择的取车时间暂无门店营业');
//       expect(PageDom).toHaveTextContent('建议您修改取车时间');
//       expect(PageDom).toHaveTextContent('修改取车时间');

//       // 903918 补偿标题
//       expect(PageDom).toHaveTextContent('2024-02-27 10:00可取车');
//     });
//   },
// );

// 不稳case
// test(
//   createInterTestName({
//     testId: [903834, 903932],
//     name: '同地因提前预定期无结果',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903834));

//     const Page = await renderPage();
//     const PageDom = Page.container;
//     await waitRender(async () => {
//       // 903834 无结果提示模块
//       expect(PageDom).toHaveTextContent('您选择的取车时间暂无门店营业');
//       expect(PageDom).toHaveTextContent('建议您修改取车时间');
//       expect(PageDom).toHaveTextContent('修改取车时间');

//       // 903932 补偿标题
//       expect(PageDom).toHaveTextContent('2024-01-31 10:00可取车');
//     });
//   },
// );

// test(
//   createInterTestName({
//     testId: [903820, 903960],
//     name: '推荐同地取还',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903820));

//     const Page = await renderPage();
//     const PageDom = Page.container;
//     await waitRender(async () => {
//       // 903820 无结果提示模块
//       expect(PageDom).toHaveTextContent('暂无支持异地取还的门店哦');
//       expect(PageDom).toHaveTextContent('建议您修改取还车条件，或选择同地取还');
//       expect(PageDom).toHaveTextContent('修改取还车地点');

//       // 903960 补偿标题
//       expect(PageDom).toHaveTextContent('以下车辆支持同门店取还');
//     });
//   },
// );

// test(
//   createInterTestName({
//     testId: [903869, 903967, 903981, 904023],
//     name: '同地-兜底推荐取还车地点',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     jest
//       .spyOn(Utils, 'getUniqRequestKeyWithEnv')
//       .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
//     jest
//       .spyOn(CarFetch, 'getListProduct')
//       .mockImplementation(() => Promise.resolve(resProduct903785));
//     jest
//       .spyOn(CarFetch, 'getRecommendProducts')
//       .mockImplementation(() => Promise.resolve(resRecommend903869));

//     const Page = await renderPage();
//     const PageDom = Page.container;

//     await waitRender(async () => {
//       // 903869 无结果提示模块
//       expect(PageDom).toHaveTextContent('暂无符合要求的车辆哦');
//       expect(PageDom).toHaveTextContent('建议您修改取还车地点');
//       expect(PageDom).toHaveTextContent('修改取还车条件');

//       // 903967 补偿标题
//       expect(PageDom).toHaveTextContent('以下取车门店距取车地点12.9公里起');

//       // 903974 车型卡片提示
//       expect(PageDom).toHaveTextContent('距新加坡动物园22.4公里起');

//       // 903995 报价提示文案
//       const vehicle = await Page.findByText(/宝马 3系/i);
//       renderWithAction({
//         action: async () => {
//           fireEvent.press(vehicle);
//         },
//         expect: async () => {
//           expect(PageDom).toHaveTextContent('距离新加坡动物园 12.9公里');
//         },
//       });
//     });
//   },
// );
