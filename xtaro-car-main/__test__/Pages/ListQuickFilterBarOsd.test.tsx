/* eslint-disable @typescript-eslint/naming-convention */
import { TouchableOpacity, Text } from 'react-native';
import { fireEvent, act } from '@testing-library/react-native';
import { color } from '../../src/pages/xcar/Common/src/Tokens';
import { Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));

const res440423 = require('../../__mocks__/restful/18631/queryProducts/OSD/440423.json');
const res440424 = require('../../__mocks__/restful/18631/queryProducts/OSD/440424.json');
const res440426 = require('../../__mocks__/restful/18631/queryProducts/OSD/440426.json');

// @ts-ignore 禁止Redux Log
__DEV__ = false;
jest.mock('uuid', () => {
  return jest.fn().mockImplementation(() => {
    return '123456789';
  });
});
// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: false,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

test(
  createInterTestName({
    testId: [440423],
    name: '配置项',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(res440423));
    const Page = await renderPage();

    const quickFilter = Page.queryByTestId(
      UITestID.car_testid_comp_quick_filter_bar,
    );

    expect(quickFilter).toBeNull();
  },
);

test(
  createInterTestName({
    testId: [440424],
    name: '配置项',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(res440424));
    const Page = await renderPage();

    const quickFilter = await Page.queryByTestId(
      UITestID.car_testid_comp_quick_filter_bar,
    );

    expect(quickFilter).toBeTruthy();
    expect(quickFilter).toHaveTextContent('机场内');
    expect(quickFilter).toHaveTextContent('自助取车');
    expect(quickFilter).toHaveTextContent('仅需中国大陆驾照');
    expect(quickFilter).toHaveTextContent('送加强三者险');
    expect(quickFilter).toHaveTextContent('免费取消');
    expect(quickFilter).toHaveTextContent('满油取还');
    expect(quickFilter).toHaveTextContent('不限里程');
    expect(quickFilter).toHaveTextContent('立即确认');
  },
);

test(
  createInterTestName({
    testId: [440426], // 此case的上线10个，前端没有限制?，是否做在服务端
    name: '配置项',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(res440426));
    const Page = await renderPage();

    const quickFilter = await Page.queryByTestId(
      UITestID.car_testid_comp_quick_filter_bar,
    );

    expect(quickFilter).toBeTruthy();
    expect(quickFilter).toHaveTextContent('机场内');
    expect(quickFilter).toHaveTextContent('自助取车');
    expect(quickFilter).toHaveTextContent('仅需中国大陆驾照');
    expect(quickFilter).toHaveTextContent('送加强三者险');
    expect(quickFilter).toHaveTextContent('免费取消');
    expect(quickFilter).toHaveTextContent('满油取还');
    expect(quickFilter).toHaveTextContent('不限里程');
    expect(quickFilter).toHaveTextContent('立即确认');
    expect(quickFilter).toHaveTextContent('高速缴费盒');
    expect(quickFilter).toHaveTextContent('免押金');
    expect(quickFilter).toHaveTextContent('中文店员');
  },
);

test(
  createInterTestName({
    testId: [440425, 440427, 440428, 440430],
    name: '快筛规则',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(Utils, 'getUniqRequestKeyWithEnv')
      .mockReturnValue('OSD_C_APP_e01e003b-e0b8-4d27-b8f4-6ea981319cc3');
    jest
      .spyOn(CarFetch, 'getListProduct')
      .mockImplementation(() => Promise.resolve(res440424));
    const Page = await renderPage();

    const quickFilter = await Page.queryByTestId(
      UITestID.car_testid_comp_quick_filter_bar,
    );

    const inAirPortQuickFilter = await quickFilter.findAllByType(
      TouchableOpacity,
    )[0];
    await act(() => {
      fireEvent(inAirPortQuickFilter, 'onPress');
    });
    const selectedButtonStyle = {
      backgroundColor: color.blueBgSecondary,
    };
    expect(inAirPortQuickFilter).toHaveStyle(selectedButtonStyle);

    const filterBar = await Page.getByTestId(
      UITestID.car_testid_page_list_filter_bar,
    );
    const filterStoreService = await filterBar.findAllByType(
      TouchableOpacity,
    )[2];

    const filterStoreServiceText = await filterStoreService.findAllByType(Text);
    expect(filterStoreServiceText.length).toEqual(3);
    const selectedFilterBarTextStyle = {
      color: '#0086f6',
    };
    expect(filterStoreServiceText[1]).toHaveStyle(selectedFilterBarTextStyle);

    // 点击门店/服务
    await act(() => {
      fireEvent(filterStoreService, 'onPress');
    });

    const clearBtn = await Page.findByTestId(
      UITestID.car_testid_page_list_filter_modal_clear_btn,
    );

    // 点击筛选弹窗中的清除
    await act(() => {
      fireEvent(clearBtn, 'onPress');
    });

    setTimeout(async () => {
      const clearfilterStoreServiceText =
        await filterStoreService.findAllByType(Text);
      expect(clearfilterStoreServiceText.length).toEqual(2);
      const unSelectedFilterBarTextStyle = {
        color: '#333333',
      };
      expect(clearfilterStoreServiceText[0]).toHaveStyle(
        unSelectedFilterBarTextStyle,
      );
      const unSelectedButtonStyle = {
        backgroundColor: color.grayBg,
      };
      expect(inAirPortQuickFilter).toHaveStyle(unSelectedButtonStyle);
    }, 1000);
  },
);
