import CRNPage from '@c2x/components/Page';
import { AppContext, Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Global/Cache/ProductSelectors', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Global/Cache/ProductSelectors'),
}));
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '12345678',
    apptype: 'OSD_C_APP',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, {}, initialState);
};
const res1 = {
  orderBaseInfo: {
    orderId: 26094177511,
    uId: 'M2331903879',
    channelType: '17671',
    orderDate: 1693638936000,
    orderLocale: 'zh_cn',
    orderStatus: 4,
    orderStatusDesc: '已取消',
    orderStatusName: '已取消',
    orderStatusCtrip: 'CAR_CANCELLED',
    allStatuses: [],
    allOperations: [
      { operationId: 4, buttonName: '再次预订', enable: true },
      { operationId: 3, enable: false, code: 4 },
    ],
    orderTip: {
      tipType: 5,
      tipContent: '很抱歉，由于超时未支付，您的订单已取消',
      tipContentArray: ['很抱歉，由于超时未支付，您的订单已取消'],
      warnType: 0,
    },
    payMode: 2,
    isEasyLife: false,
    extOperation: [],
  },
  orderPriceInfo: {
    packageType: 0,
    currentTotalPrice: 2039.0,
    currentCurrencyCode: 'CNY',
    localTotalPrice: 370737.27,
    localCurrencyCode: 'KRW',
    payMode: 1,
    payModeDesc: '到店支付',
    prepayPrice: { title: '', totalPrice: 1839.0, currencyCode: 'CNY' },
    localPrice: { title: '', totalPrice: 30, currencyCode: 'KRW' },
    prepayPriceDetails: [
      {
        title: 'shark|common_carHireFee',
        totalPrice: 1839.0,
        currencyCode: 'CNY',
      },
    ],
    localPriceDetails: [],
    priceDetails: [
      {
        title: 'shark|common_carHireFee',
        totalPrice: 2039.0,
        currencyCode: 'CNY',
      },
    ],
    payAmount: 1839.0,
    couponAmount: 200.0,
    coupons: [
      {
        couponCode: 'enxjbfhuac',
        promotionId: 974834787,
        isNeedDebate: false,
        deductionAmount: 200,
        remark:
          '（1）使用范围：优惠券仅限携程租车新用户领取并适用于携程租车-国际租车（含中国港澳台）频道，以全额预付方式（限在线支付，不含预付订金和到店支付）预订国际租车（含中国港澳台）产品（部分套餐不适用，以订单情况为准）； （2）券有效期：优惠券领取后30天内下单可用，过期自动失效； （3）门槛阶梯：基础租车费享受满500减50元，满1000减100元，满2000减200元，满4000减400元，满8000减888元优惠； （4）使用限制：有效期内每个用户仅限领取一张，同一设备号、手机号、uid均视为同一用户；每张优惠券仅限使用一次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券、不与活动同享；已完成支付的订单不能使用此券； （5）退赔说明：使用优惠券的订单无损取消、超时未支付、购买失败或因订单变更导致订单金额未满足优惠门槛的，若该券尚未失效将退回原账户，若该券已失效不予退回；使用优惠券的订单变更，导致订单金额增加的，仅可享受变更前的阶梯优惠金额。已用券的订单如取消且产生取消费用的，优惠券因涉及抵扣（或部分抵扣）将不予退回。',
        displayName: '国际租车满减券',
      },
    ],
  },
  baseResponse: {
    isSuccess: true,
    code: 'unknown',
    returnMsg: 'success',
    requestId: '',
    cost: 369,
  },
};
const res2 = {
  orderBaseInfo: {
    orderId: 26094177511,
    uId: 'M2331903879',
    channelType: '17671',
    orderDate: 1693638936000,
    orderLocale: 'zh_cn',
    orderStatus: 4,
    orderStatusDesc: '已取消',
    orderStatusName: '已取消',
    orderStatusCtrip: 'CAR_CANCELLED',
    allStatuses: [],
    allOperations: [
      { operationId: 4, buttonName: '再次预订', enable: true },
      { operationId: 3, enable: false, code: 4 },
    ],
    orderTip: {
      tipType: 5,
      tipContent: '很抱歉，由于超时未支付，您的订单已取消',
      tipContentArray: ['很抱歉，由于超时未支付，您的订单已取消'],
      warnType: 0,
    },
    payMode: 2,
    isEasyLife: false,
    extOperation: [],
  },
  orderPriceInfo: {
    packageType: 0,
    currentTotalPrice: 2039.0,
    currentCurrencyCode: 'CNY',
    localTotalPrice: 370737.27,
    localCurrencyCode: 'KRW',
    payMode: 2,
    payModeDesc: '在线支付',
    prepayPrice: { title: '', totalPrice: 1839.0, currencyCode: 'CNY' },
    localPrice: { title: '', totalPrice: 30, currencyCode: 'KRW' },
    prepayPriceDetails: [
      {
        title: 'shark|common_carHireFee',
        totalPrice: 1839.0,
        currencyCode: 'CNY',
      },
    ],
    localPriceDetails: [],
    priceDetails: [
      {
        title: 'shark|common_carHireFee',
        totalPrice: 2039.0,
        currencyCode: 'CNY',
      },
    ],
    payAmount: 1839.0,
    couponAmount: 200.0,
    coupons: [
      {
        couponCode: 'enxjbfhuac',
        promotionId: 974834787,
        isNeedDebate: false,
        deductionAmount: 200,
        remark:
          '（1）使用范围：优惠券仅限携程租车新用户领取并适用于携程租车-国际租车（含中国港澳台）频道，以全额预付方式（限在线支付，不含预付订金和到店支付）预订国际租车（含中国港澳台）产品（部分套餐不适用，以订单情况为准）； （2）券有效期：优惠券领取后30天内下单可用，过期自动失效； （3）门槛阶梯：基础租车费享受满500减50元，满1000减100元，满2000减200元，满4000减400元，满8000减888元优惠； （4）使用限制：有效期内每个用户仅限领取一张，同一设备号、手机号、uid均视为同一用户；每张优惠券仅限使用一次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券、不与活动同享；已完成支付的订单不能使用此券； （5）退赔说明：使用优惠券的订单无损取消、超时未支付、购买失败或因订单变更导致订单金额未满足优惠门槛的，若该券尚未失效将退回原账户，若该券已失效不予退回；使用优惠券的订单变更，导致订单金额增加的，仅可享受变更前的阶梯优惠金额。已用券的订单如取消且产生取消费用的，优惠券因涉及抵扣（或部分抵扣）将不予退回。',
        displayName: '国际租车满减券',
      },
    ],
  },
  baseResponse: {
    isSuccess: true,
    code: 'unknown',
    returnMsg: 'success',
    requestId: '',
    cost: 369,
  },
};
describe('境外订详 - 费用明细', () => {
  test(
    createInterTestName({
      testId: [458409, 3898350],
      name: '境外订详 - 预付定金',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      const pagePopFunc = jest.spyOn(CRNPage.prototype, 'pop');
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

      const Page = await renderPage(state);
      expect(Page.container).toHaveTextContent('预付定金¥1839');
      expect(Page.container).toHaveTextContent('到店还需支付KRW30');
      // expect(Page.toJSON()).toMatchSnapshot();
    },
  );
  test(
    createInterTestName({
      testId: [458407],
      name: '境外订详 - 到付',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockResolvedValue(Promise.resolve(res1));
      const Page = await renderPage(state);
      expect(Page.container).toHaveTextContent('到店支付KRW30');
      expect(Page.container).toHaveTextContent('订单总额 ¥1839');
      // expect(Page.toJSON()).toMatchSnapshot();
    },
  );
  test(
    createInterTestName({
      testId: [458407, 458408, 458391],
      name: '境外订详 - 到付',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 12345678,
          },
        },
      };
      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);

      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockResolvedValue(Promise.resolve(res2));
      const Page = await renderPage(state);
      expect(Page.container).toHaveTextContent('订单总额 ¥1839');
      // expect(Page.toJSON()).toMatchSnapshot();
    },
  );
});
