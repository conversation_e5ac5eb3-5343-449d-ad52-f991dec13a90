import CRNPage from '@c2x/components/Page';
import Booking from '../../src/pages/xcar/Containers/BookingContainer';
import {
  AppContext,
  Utils,
  GetAB,
  User,
  GetABCache,
} from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import { renderWithAction, waitRender } from '../testHelpers';
// @ts-ignore 禁止Redux Log
__DEV__ = false;

jest.mock('../../src/pages/xcar/Util/User', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/User'),
}));

jest.mock('../../src/pages/xcar/Util/CarABTesting/GetAB', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/CarABTesting/GetAB'),
}));
jest.mock('../../src/pages/xcar/Util/CarABTesting/GetABCache', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/Util/CarABTesting/GetABCache'),
}));
// Apptype OSD
jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
jest
  .spyOn(User, 'isLogin')
  .mockImplementation(() => new Promise(resolve => resolve(true)));

// 页面初始化
const renderPage = async (initialState = {}) => {
  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'Booking',
  });

  return createPage(Booking, {}, initialState);
};

test(
  createInterTestName({
    testId: [451672, 451649],
    name: '境外优惠券与活动展示',
  }),
  async () => {
    const BookingPage = await renderPage();
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');

    await waitRender(() => {
      // 451672
      expect(BookingPage.container).toHaveTextContent('周边游特惠');
    });
  },
);

test(
  createInterTestName({
    testId: [451653, 451648],
    name: '无活动',
  }),
  async () => {
    const BookingPage = await renderPage();
    const pagePushFunc = jest.spyOn(CRNPage.prototype, 'push');

    await waitRender(() => {
      // 451653 预期：预期：展示【暂无促销活动】
      expect(BookingPage.container).toHaveTextContent('暂无促销活动');
      // 451648 预期：展示【暂无可用优惠券】
      expect(BookingPage.container).toHaveTextContent('暂无可用优惠券');
    });
  },
);

test(
  createInterTestName({
    testId: [685469, 685483, 685490],
    name: '机票打包项目二期-无优惠券和有活动',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(User, 'isLogin')
      .mockImplementation(() => new Promise(resolve => resolve(true)));

    const Page = await renderPage();
    await waitRender(async () => {
      // 685490 合并展示 预期：优惠券模块和活动模块合并展示为优惠模块
      const newCouponAndAcitity = await Page.findByTestId(
        UITestID.car_testid_comp_booking_new_couponAndActivity,
      );
      expect(newCouponAndAcitity).toHaveTextContent('优惠暂无可用优惠券');
      // 685483 预期：机票用户专享>精选特惠>供应商活动(夏日，限时)
      expect(newCouponAndAcitity).toHaveTextContent(
        '活动机票用户专享,周末特惠,周末特惠,国庆特惠',
      );
    });
  },
);

test(
  createInterTestName({
    testId: [685476],
    name: '无优惠券&无活动',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const Page = await renderPage();
    await waitRender(async () => {
      const newCouponAndAcitity = await Page.findByTestId(
        UITestID.car_testid_comp_booking_new_couponAndActivity,
      );
      expect(newCouponAndAcitity).toHaveTextContent('暂无促销活动');
      expect(newCouponAndAcitity).toHaveTextContent('暂无可用优惠券');
    });
  },
);

test(
  createInterTestName({
    testId: [685462],
    name: '有优惠券&有活动',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const Page = await renderPage();
    await waitRender(async () => {
      const newCouponAndAcitity = await Page.findByTestId(
        UITestID.car_testid_comp_booking_new_couponAndActivity,
      );
      expect(newCouponAndAcitity).toHaveTextContent(
        '优惠出境无门槛立减50立减券-¥50',
      );
      expect(newCouponAndAcitity).toHaveTextContent(
        '活动周末特惠,周末特惠,国庆特惠',
      );
    });
  },
);

test(
  createInterTestName({
    testId: [685455],
    name: '有优惠券&无活动',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const Page = await renderPage();
    await waitRender(async () => {
      const newCouponAndAcitity = await Page.findByTestId(
        UITestID.car_testid_comp_booking_new_couponAndActivity,
      );
      expect(newCouponAndAcitity).toHaveTextContent(
        '优惠出境无门槛立减50立减券-¥50',
      );
      expect(newCouponAndAcitity).toHaveTextContent('活动暂无促销活动');
    });
  },
);
