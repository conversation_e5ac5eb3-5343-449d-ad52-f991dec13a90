import { fireEvent } from '@testing-library/react-native';
import { TouchableOpacity } from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import DriverEdit from '../../src/pages/xcar/Containers/DriverEditContainer';
import { AppContext, Utils, User } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import { waitRender } from '../testHelpers';
import ProductReqAndResData from '../../src/pages/xcar/Global/Cache/ProductReqAndResData';

const resProductInfo457865 = require('../../__mocks__/restful/18631/queryProductInfo/OSD/457865.json');

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = async (initialState = {}, props = {}) => {
  jest
    .spyOn(User, 'isLogin')
    .mockImplementation(() => new Promise(resolve => resolve(true)));

  // 配置页面标识
  AppContext.setUrlQuery({
    initialPage: 'DriverList',
  });

  return createPage(DriverEdit, props, initialState);
};

test(
  createInterTestName({
    testId: [451579, 451581],
    name: '添加驾驶员',
  }),
  async () => {
    ProductReqAndResData.setData(
      ProductReqAndResData.keyList.productRes,
      resProductInfo457865,
    );
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const Page = await renderPage(
      {},
      {
        passenger: {
          lastName: '',
          firstName: '',
          age: 30,
          nationality: '',
          mobile: '',
          countryCode: '',
          fullName: '',
          birthday: '2020-01-01',
          passengerId: '260698',
          certificateList: [],
          email: '',
          isCreditQualified: false,
        },
      },
    );

    await waitRender(async () => {
      const allBtn = await Page.container.findAllByType(TouchableOpacity);
      const saveBtn = allBtn[1];
      await fireEvent.press(saveBtn);

      // 451579
      expect(Page.container).toHaveTextContent(
        '驾驶员最低年龄为20，请核对或填写其他符合要求的同伴作为驾驶员',
      );

      // 451581
      expect(Page.container).toHaveTextContent('请输入中文姓名');
      expect(Page.container).toHaveTextContent('请输入英文姓');
      expect(Page.container).toHaveTextContent('请输入正确的驾驶员联系手机');
    });
  },
);

test(
  createInterTestName({
    testId: [451585],
    name: '添加驾驶员',
  }),
  async () => {
    ProductReqAndResData.setData(
      ProductReqAndResData.keyList.productRes,
      resProductInfo457865,
    );
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const Page = await renderPage(
      {},
      {
        passenger: {
          lastName: '',
          firstName: '',
          age: 30,
          nationality: '',
          mobile: '1sdfsdf',
          countryCode: '',
          fullName: '',
          birthday: '2020-01-01',
          passengerId: '260698',
          certificateList: [],
          email: '',
          isCreditQualified: false,
        },
      },
    );

    await waitRender(async () => {
      const allBtn = await Page.container.findAllByType(TouchableOpacity);
      const saveBtn = allBtn[1];
      await fireEvent.press(saveBtn);

      expect(Page.container).toHaveTextContent('请输入正确的驾驶员联系手机');
    });
  },
);
