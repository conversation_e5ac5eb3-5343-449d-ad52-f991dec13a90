import { TouchableOpacity } from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { fireEvent, act, waitFor } from '@testing-library/react-native';
import dayjs from '../../src/pages/xcar/Common/src/Dayjs/src';
import { Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import {
  createPage,
  createInterTestName,
  TEST_TIMEOUT,
  updateStore,
} from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';
import { delayPromise, waitRender } from '../testHelpers';

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: true,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

test(
  createInterTestName({
    testId: [440376, 440377, 440378, 455494],
    name: '时间显示',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

    const now = dayjs();
    const currTime = now.set('hour', 10).set('minute', 0).set('second', 0);
    let statePTime = currTime.add(6, 'days');
    let stateRTime = currTime.add(10, 'days');
    if (stateRTime.year() > statePTime.year()) {
      statePTime = statePTime.add(10, 'days');
      stateRTime = statePTime.add(10, 'days');
    }
    const ptimeStr = statePTime.format('M月D日 HH:mm');
    const rtimeStr = stateRTime.format('M月D日 HH:mm');
    const initialState = {
      LocationAndDate: {
        rentalDate: {
          pickUp: {
            dateTime: dayjs(statePTime),
          },
          dropOff: {
            dateTime: dayjs(stateRTime),
          },
        },
      },
    };
    const Page = await renderPage(initialState);

    // 440376
    await waitFor(() => {
      const pTimeText = Page.findByText(ptimeStr);
      expect(pTimeText).toBeTruthy();
      const rTimeText = Page.findByText(rtimeStr);
      expect(rTimeText).toBeTruthy();
    });

    const statePTime2 = currTime.add(6, 'days');
    const stateRTime2 = currTime.add(10, 'days').add(1, 'year');
    const ptimeStr2 = statePTime2.format('YYYY-MM-DD HH:mm');
    const rtimeStr2 = stateRTime2.format('YYYY-MM-DD HH:mm');
    const updateState = {
      LocationAndDate: {
        rentalDate: {
          pickUp: {
            dateTime: dayjs(statePTime2),
          },
          dropOff: {
            dateTime: dayjs(stateRTime2),
          },
        },
      },
    };
    updateStore(updateState);
    await waitFor(() => {
      const pTimeText2 = Page.queryByText(ptimeStr2);
      expect(pTimeText2).toBeTruthy();
      const rTimeText2 = Page.queryByText(rtimeStr2);
      expect(rTimeText2).toBeTruthy();
    });
  },
);

test(
  createInterTestName({
    testId: [440379],
    name: '同地按钮展示',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const Page = await renderPage();

    const header = await Page.findByTestId(
      UITestID.car_testid_page_list_header,
    );
    const contentTouchable = await header.findAllByType(TouchableOpacity)[0];

    await act(() => {
      fireEvent(contentTouchable, 'onPress');
    });

    await waitFor(async () => {
      const switchBtn = await Page.findByTestId(
        UITestID.car_testid_page_home_offsite_switch,
      );
      expect(switchBtn.props.value).toBeFalsy();
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [440383, 440381, 440382],
    name: '信息修改',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

    const Page = await renderPage();

    const header = await Page.findByTestId(
      UITestID.car_testid_page_list_header,
    );
    const contentTouchable = await header.findAllByType(TouchableOpacity)[0];

    await act(() => {
      fireEvent(contentTouchable, 'onPress');
    });

    // 点击时间信息 成功唤起时间组件
    const timeLine = await Page.findByTestId(
      UITestID.car_testid_page_list_search_pannel_timeline,
    );
    const ptimeTouchable = await timeLine.findAllByType(TouchableOpacity)[0];
    await act(() => {
      fireEvent(ptimeTouchable, 'onPress');
    });

    // @ts-ignore
    expect(global.showCalendar).toBeTruthy();

    // 点击取车城市
    const locatoionItem = await Page.findByTestId(
      UITestID.car_testid_page_list_search_pannel_locationItem_pickup,
    );
    const pCityTouchable = await locatoionItem.findAllByType(
      TouchableOpacity,
    )[0];
    const pAreaTouchable = await locatoionItem.findAllByType(
      TouchableOpacity,
    )[0];

    await act(() => {
      fireEvent(pCityTouchable, 'onPress');
    });
    // @ts-ignore
    expect(global.pushPageName).toEqual('Location');
    await act(() => {
      fireEvent(pAreaTouchable, 'onPress');
    });
    // @ts-ignore
    expect(global.pushPageName).toEqual('Location');
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [440370, 440371, 440372],
    name: '同地',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);

    const now = dayjs();
    const currTime = now.set('hour', 10).set('minute', 0).set('second', 0);
    const statePTime = currTime.add(6, 'days');
    const stateRTime = currTime.add(10, 'days');

    const initialStateCross = {
      LocationAndDate: {
        rentalDate: {
          pickUp: {
            dateTime: dayjs(statePTime),
          },
          dropOff: {
            dateTime: dayjs(stateRTime),
          },
        },
        rentalLocation: {
          pickUp: {
            cid: 633,
            cname: '纽约',
            country: '美国',
            realcountry: '美国',
            isDomestic: false,
            area: {
              name: '时代金融广场',
              lat: 33.941589,
              lng: -118.40853,
              type: '1',
            },
            isFromPosition: false, // 数据是否来自于定位
            isFromDefault: true, // 是否来源于默认数据
          },
          dropOff: {
            cid: 633,
            cname: '纽约',
            country: '美国',
            realcountry: '美国',
            isDomestic: false,
            area: {
              name: '时代金融广场',
              lat: 33.941589,
              lng: -118.40853,
              type: '1',
            },
            isFromPosition: false, // 数据是否来自于定位
            isFromDefault: true, // 是否来源于默认数据
          },
          isShowDropOff: true,
        },
      },
    };

    const Page = await renderPage(initialStateCross);

    // 440372
    await waitRender(async () => {
      const pAddressText = Page.queryByText('时代金融广场');
      expect(pAddressText).toBeTruthy();
      const rAddressText = Page.queryByText('时代金融广场');
      expect(rAddressText).toBeTruthy();
    });

    const statePTimeStr = currTime.add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
    const stateRTimeStr = currTime
      .add(3, 'days')
      .add(0.5, 'hour')
      .format('YYYY-MM-DD HH:mm:ss');
    const rentalLocation = {
      pickUp: {
        cid: 347,
        cname: '洛杉矶',
        country: '美国',
        realcountry: '美国',
        isDomestic: false,
        area: {
          id: 'LAX',
          name: '洛杉矶国际机场',
          lat: 33.941589,
          lng: -118.40853,
          type: '1',
        },
        isFromPosition: false, // 数据是否来自于定位
        isFromDefault: true, // 是否来源于默认数据
      },
      dropOff: {
        cid: 347,
        cname: '洛杉矶',
        country: '美国',
        realcountry: '美国',
        isDomestic: false,
        area: {
          id: 'LAX',
          name: '洛杉矶国际机场',
          lat: 33.941589,
          lng: -118.40853,
          type: '1',
        },
        isFromPosition: false, // 数据是否来自于定位
        isFromDefault: true, // 是否来源于默认数据
      },
      isShowDropOff: false,
    };
    const rentalDate = {
      pickUp: {
        dateTime: dayjs(statePTimeStr),
      },
      dropOff: {
        dateTime: dayjs(stateRTimeStr),
      },
    };
    const age = '30~60';
    const adultSelectNum = 2;
    const childSelectNum = 0;

    const initialState = {
      LocationAndDate: {
        rentalDate,
        rentalLocation,
        age,
        adultSelectNum,
        childSelectNum,
      },
    };
    updateStore(initialState);
    await waitFor(() => {
      const pAddressText = Page.queryByText('洛杉矶国际机场');
      expect(pAddressText).toBeTruthy();
      const rAddressText = Page.queryByText('洛杉矶国际机场');
      expect(rAddressText.parent.props.numberOfLines).toEqual(1);
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [440373, 440374, 440375, 440380, 455498],
    name: '异地',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const now = dayjs();
    const currTime = now.set('hour', 10).set('minute', 0).set('second', 0);
    const statePTime = currTime.add(6, 'days');
    const stateRTime = currTime.add(10, 'days');

    const initialStateCross = {
      LocationAndDate: {
        rentalDate: {
          pickUp: {
            dateTime: dayjs(statePTime),
          },
          dropOff: {
            dateTime: dayjs(stateRTime),
          },
        },
        rentalLocation: {
          pickUp: {
            cid: 347,
            cname: '洛杉矶',
            country: '美国',
            realcountry: '美国',
            isDomestic: false,
            area: {
              id: 'LAX',
              name: '洛杉矶国际机场',
              lat: 33.941589,
              lng: -118.40853,
              type: '1',
            },
            isFromPosition: false, // 数据是否来自于定位
            isFromDefault: true, // 是否来源于默认数据
          },
          dropOff: {
            cid: 633,
            cname: '纽约',
            country: '美国',
            realcountry: '美国',
            isDomestic: false,
            area: {
              name: '时代金融广场',
              lat: 33.941589,
              lng: -118.40853,
              type: '1',
            },
            isFromPosition: false, // 数据是否来自于定位
            isFromDefault: true, // 是否来源于默认数据
          },
          isShowDropOff: true,
        },
      },
    };

    const Page = await renderPage(initialStateCross);

    await waitRender(async () => {
      const pAddressText = Page.queryByText('洛杉矶国际机场');
      expect(pAddressText).toBeTruthy();
      const rAddressText = Page.queryByText('时代金融广场');
      expect(rAddressText).toBeTruthy();
    });

    const statePTimeStr = currTime.add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
    const stateRTimeStr = currTime
      .add(3, 'days')
      .add(0.5, 'hour')
      .format('YYYY-MM-DD HH:mm:ss');
    const rentalLocation = {
      pickUp: {
        cid: 347,
        cname: '洛杉矶',
        country: '美国',
        realcountry: '美国',
        isDomestic: false,
        area: {
          id: 'LAX',
          name: '洛杉矶国际机场',
          lat: 33.941589,
          lng: -118.40853,
          type: '1',
        },
        isFromPosition: false, // 数据是否来自于定位
        isFromDefault: true, // 是否来源于默认数据
      },
      dropOff: {
        cid: 633,
        cname: '纽约',
        country: '美国',
        realcountry: '美国',
        isDomestic: false,
        area: {
          name: '时代金融广场',
          lat: 33.941589,
          lng: -118.40853,
          type: '1',
        },
        isFromPosition: false, // 数据是否来自于定位
        isFromDefault: true, // 是否来源于默认数据
      },
      isShowDropOff: true,
    };
    const rentalDate = {
      pickUp: {
        dateTime: dayjs(statePTimeStr),
      },
      dropOff: {
        dateTime: dayjs(stateRTimeStr),
      },
    };
    const age = '30~60';
    const adultSelectNum = 2;
    const childSelectNum = 0;

    const initialState = {
      LocationAndDate: {
        rentalDate,
        rentalLocation,
        age,
        adultSelectNum,
        childSelectNum,
      },
    };
    updateStore(initialState);
    await waitFor(() => {
      const pAddressText = Page.queryByText('洛杉矶国际机场');
      expect(pAddressText).toBeTruthy();
      const rAddressText = Page.queryByText('时代金融广场');
      expect(rAddressText.parent.props.numberOfLines).toEqual(1);
    });

    // 440380 【异地还车】开启
    const header = await Page.findByTestId(
      UITestID.car_testid_page_list_header,
    );
    const contentTouchable = await header.findAllByType(TouchableOpacity)[0];

    await act(() => {
      fireEvent(contentTouchable, 'onPress');
    });
    await waitFor(async () => {
      const switchBtn = await Page.findByTestId(
        `${UITestID.car_testid_page_home_offsite_switch}`,
      );
      expect(switchBtn.props.accessibilityState.checked).toBeTruthy();
    });
  },
  TEST_TIMEOUT,
);

test(
  createInterTestName({
    testId: [440384, 455499],
    name: '信息修改',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    const now = dayjs();
    const currTime = now.set('hour', 10).set('minute', 0).set('second', 0);
    const statePTime = currTime.add(6, 'days');
    const stateRTime = currTime.add(10, 'days');

    const initialStateCross = {
      LocationAndDate: {
        rentalDate: {
          pickUp: {
            dateTime: dayjs(statePTime),
          },
          dropOff: {
            dateTime: dayjs(stateRTime),
          },
        },
        rentalLocation: {
          pickUp: {
            cid: 347,
            cname: '洛杉矶',
            country: '美国',
            realcountry: '美国',
            isDomestic: false,
            area: {
              id: 'LAX',
              name: '洛杉矶国际机场',
              lat: 33.941589,
              lng: -118.40853,
              type: '1',
            },
            isFromPosition: false, // 数据是否来自于定位
            isFromDefault: true, // 是否来源于默认数据
          },
          dropOff: {
            cid: 633,
            cname: '纽约',
            country: '美国',
            realcountry: '美国',
            isDomestic: false,
            area: {
              name: '时代金融广场',
              lat: 33.941589,
              lng: -118.40853,
              type: '1',
            },
            isFromPosition: false, // 数据是否来自于定位
            isFromDefault: true, // 是否来源于默认数据
          },
          isShowDropOff: true,
        },
      },
    };

    const Page = await renderPage(initialStateCross);

    await waitRender(async () => {
      const pAddressText = Page.queryByText('洛杉矶国际机场');
      expect(pAddressText).toBeTruthy();
      const rAddressText = Page.queryByText('时代金融广场');
      expect(rAddressText).toBeTruthy();

      const header = await Page.findByTestId(
        UITestID.car_testid_page_list_header,
      );
      const contentTouchable = await header.findAllByType(TouchableOpacity)[0];

      // 点击头部搜索框
      await act(() => {
        fireEvent(contentTouchable, 'onPress');
      });

      // 点击异地取还
      const switchBtn = await Page.findByTestId(
        UITestID.car_testid_page_home_offsite_switch,
      );
      await act(() => {
        fireEvent(switchBtn, 'onPress');
      });

      // 异步取还操作按钮有动画延迟
      await delayPromise();

      const searchBtn = await Page.findByTestId(
        UITestID.car_testid_page_home_search_btn,
      );

      // 点击搜索按钮
      await act(() => {
        fireEvent(searchBtn, 'click');
      });

      const headerAfter = await Page.findByTestId(
        UITestID.car_testid_page_list_header,
      );

      expect(headerAfter).toHaveTextContent('洛杉矶国际机场');
      expect(headerAfter).not.toHaveTextContent('时代金融广场');
    });
  },
  TEST_TIMEOUT,
);

// TODO tjw 不稳定case
// test(
//   createInterTestName({
//     testId: [440385, 440386, 440389],
//     name: '查询功能',
//   }),
//   async () => {
//     jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
//     const now = dayjs();
//     const currTime = now.set('hour', 10).set('minute', 0).set('second', 0);
//     const statePTime = currTime.add(6, 'days');
//     const stateRTime = currTime.add(10, 'days');

//     const initialStateCross = {
//       LocationAndDate: {
//         rentalDate: {
//           pickUp: {
//             dateTime: dayjs(statePTime),
//           },
//           dropOff: {
//             dateTime: dayjs(stateRTime),
//           },
//         },
//         rentalLocation: {
//           pickUp: {
//             cid: 347,
//             cname: '洛杉矶',
//             country: '美国',
//             realcountry: '美国',
//             isDomestic: false,
//             area: {
//               id: 'LAX',
//               name: '洛杉矶国际机场',
//               lat: 33.941589,
//               lng: -118.40853,
//               type: '1',
//             },
//             isFromPosition: false, // 数据是否来自于定位
//             isFromDefault: true, // 是否来源于默认数据
//           },
//           dropOff: {
//             cid: 633,
//             cname: '纽约',
//             country: '美国',
//             realcountry: '美国',
//             isDomestic: false,
//             area: {
//               name: '时代金融广场',
//               lat: 33.941589,
//               lng: -118.40853,
//               type: '1',
//             },
//             isFromPosition: false, // 数据是否来自于定位
//             isFromDefault: true, // 是否来源于默认数据
//           },
//           isShowDropOff: true,
//         },
//       },
//     };

//     const Page = await renderPage(initialStateCross);

//     await waitRender(async () => {
//       const headerBefore = await Page.findByTestId(
//         UITestID.car_testid_page_list_header,
//       );
//       expect(headerBefore).toHaveTextContent('洛杉矶国际机场');
//       expect(headerBefore).toHaveTextContent('时代金融广场');
//     });

//     await waitRender(async () => {
//       const header = await Page.findByTestId(
//         UITestID.car_testid_page_list_header,
//       );
//       const contentTouchable = await header.findAllByType(TouchableOpacity)[0];

//       // 点击头部搜索框
//       await act(() => {
//         fireEvent(contentTouchable, 'onPress');
//       });

//       const searchPannel = await Page.queryByTestId(
//         UITestID.car_testid_page_list_search_pannel_modal,
//       );

//       // 搜索弹窗显示
//       expect(searchPannel).toBeTruthy();

//       // 点击异地取还
//       const switchBtn = await Page.findByTestId(
//         UITestID.car_testid_page_home_offsite_switch,
//       );
//       await act(() => {
//         fireEvent(switchBtn, 'onPress');
//       });

//       // 异步取还操作按钮有动画延迟
//       await delayPromise();

//       expect(searchPannel).toHaveTextContent('洛杉矶国际机场');
//       expect(searchPannel).not.toHaveTextContent('时代金融广场');

//       const searchPannelMast = await Page.findByTestId(
//         UITestID.car_testid_page_list_search_pannel_close_mask,
//       );

//       // 点击搜索弹窗蒙层关闭弹窗
//       await act(() => {
//         fireEvent(searchPannelMast, 'click');
//       });

//       // // 关闭搜索弹窗有动画延迟
//       await delayPromise();

//       // 搜索弹窗
//       const closeSearchPannel = await Page.queryByTestId(
//         UITestID.car_testid_page_list_search_pannel_modal,
//       );

//       // 440386 搜索弹窗隐藏
//       expect(closeSearchPannel).not.toBeTruthy();
//     });

//     await waitRender(async () => {
//       await delayPromise();

//       const header = await Page.findByTestId(
//         UITestID.car_testid_page_list_header,
//       );
//       const contentTouchable = await header.findAllByType(TouchableOpacity)[0];

//       // 点击头部搜索框
//       await act(() => {
//         fireEvent(contentTouchable, 'onPress');
//       });

//       const searchPannel = await Page.queryByTestId(
//         UITestID.car_testid_page_list_search_pannel_modal,
//       );

//       // 440389 搜索条件还原未修改之前
//       expect(searchPannel).toHaveTextContent('洛杉矶国际机场');
//       expect(searchPannel).toHaveTextContent('时代金融广场');

//       // 点击异地取还
//       const switchBtn = await Page.findByTestId(
//         UITestID.car_testid_page_home_offsite_switch,
//       );
//       await act(() => {
//         fireEvent(switchBtn, 'onPress');
//       });

//       // 异步取还操作按钮有动画延迟
//       await delayPromise();

//       const getListProduct = jest.spyOn(CarFetch, 'getListProduct');

//       const searchBtn = await Page.findByTestId(
//         UITestID.car_testid_page_home_search_btn,
//       );

//       // 点击搜索按钮
//       await act(() => {
//         fireEvent(searchBtn, 'click');
//       });

//       expect(getListProduct).toBeCalled();

//       const headerAfter = await Page.findByTestId(
//         UITestID.car_testid_page_list_header,
//       );
//       expect(headerAfter).toHaveTextContent('洛杉矶国际机场');
//       expect(headerAfter).not.toHaveTextContent('时代金融广场');
//     });
//   },
//   TEST_TIMEOUT,
// );
