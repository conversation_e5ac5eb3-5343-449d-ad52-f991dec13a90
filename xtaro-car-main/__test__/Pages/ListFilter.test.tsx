import { TouchableOpacity, Image, Text } from 'react-native';
import CRNPage from '@c2x/components/Page';
import { fireEvent, within, act, waitFor } from '@testing-library/react-native';
import { delay } from 'redux-saga/effects';
import { Utils, CarStorage, CarFetch } from '../../src/pages/xcar/Util/Index';
import {
  createPage,
  createInterTestName,
  pressWithTestId,
} from '../PageProvider';
import { UITestID } from '../../src/pages/xcar/Constants/Index';
import * as UBTLog from '../../src/pages/xcar/State/List/UBTLog';
import { getStore } from '../../src/pages/xcar/State/Store';
import { waitRender, renderWithAction, delayPromise } from '../testHelpers';

const resProducts579679 = require('../../__mocks__/restful/18631/queryProducts/230502.json');
const res489213 = require('../../__mocks__/restful/18631/queryProducts/489213.json');
const res489688 = require('../../__mocks__/restful/18631/queryProducts/489688.json');

jest.mock('../../src/pages/xcar/State/List/UBTLog', () => ({
  __esModule: true,
  ...jest.requireActual('../../src/pages/xcar/State/List/UBTLog'),
}));

// @ts-ignore 禁止Redux Log
__DEV__ = false;

const SelectedImageIcon =
  'https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/filterSelected.png';

// 页面初始化
const renderPage = (initialState = undefined) => {
  // eslint-disable-next-line global-require
  const ListPage = require('../../src/pages/xcar/Containers/ListPageContainer').default;
  return createPage(
    ListPage,
    {
      isLoading: true,
    },
    initialState,
  );
};
jest.spyOn(CarStorage, 'remove').mockImplementation(() => {});
jest.spyOn(UBTLog, 'LogPriceExposureOfMarketing').mockImplementation(() => {});

describe('列表页-筛选排序', () => {
  test(
    createInterTestName({
      testId: [230502, 230501, 230500, 230498, 230497, 230496, 769959, 769952],
      name: '更多筛选',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const Page = await renderPage();

      await waitRender(async () => {
        const filterBar = await Page.getByTestId(
          UITestID.car_testid_page_list_filter_bar,
        );

        const filterMoreTouchable = await filterBar.findAllByType(
          TouchableOpacity,
        )[2];

        // 点击：更多筛选
        fireEvent.press(filterMoreTouchable);
        // 769959 自助取还筛选项筛选
        const btnSelfService = await Page.getByTestId(
          'car_testid_comp_filter_modal_item_SelfService_Support',
        );
        expect(btnSelfService).toBeTruthy();
        const filterListNavBarDom = await Page.getByTestId(
          'car_testid_filter_nav_list_item',
        );
        // 769952 自助取还筛选项位置
        expect(filterListNavBarDom).toHaveTextContent(
          '价格取车方式门店服务优惠活动自助取还取车证件驾驶员驾龄门店评分租车公司',
        );
        // 筛选
        // 50以下
        // car_testid_comp_filter_modal_item_Price_0-50
        const btnPrice = await Page.getByTestId(
          'car_testid_comp_filter_modal_item_Price_0-50',
        );
        fireEvent.press(btnPrice);
        // 送车上门
        // car_testid_comp_filter_modal_item_PickReturn_PickupOnDoor
        const btnPickType = await Page.getByTestId(
          'car_testid_comp_filter_modal_item_PickReturn_PickupOnDoor',
        );
        fireEvent.press(btnPickType);
        // 回乡证
        // car_testid_comp_filter_modal_item_Ceritificate_7
        const btnIdentityType = await Page.getByTestId(
          'car_testid_comp_filter_modal_item_Ceritificate_7',
        );
        fireEvent.press(btnIdentityType);

        const images = Page.container.findAllByType(Image);
        const selectedFilters = images.filter(
          image => image.props.source.uri === SelectedImageIcon,
        );

        // 230502
        // 1.快速选车Tab上面的数字显示为：N
        // TODO: 2.列表页底部回显对应筛选项的名称
        expect(selectedFilters.length).toBe(3);

        // 230501 选中取车方式“站内取车”
        const btnPickInStation = await Page.getByTestId(
          'car_testid_comp_filter_modal_item_PickReturn_StationPR',
        );
        fireEvent.press(btnPickInStation);
        const pickInStationImages = btnPickInStation.findAllByType(Image);
        expect(pickInStationImages[0].props.source.uri).toBe(SelectedImageIcon);

        // 230500 点击左侧导航栏中的类别,页面定位到对应筛选条件
        fireEvent.press(Page.getAllByText('租车公司')[0]);
        const filterItemCTRIP = Page.getAllByText('携程租车中心')[0];
        expect(filterItemCTRIP).toBeOnTheScreen();

        delay(1000);

        // 230498 左侧导航展示“价格，取车方式，门店服务，优惠活动，取车证件，驾驶员驾龄，门店评分，租车公司”
        const filterListNavBar = within(
          Page.getByTestId('car_testid_filter_nav_list_item'),
        );
        expect(filterListNavBar.getByText('价格')).toBeOnTheScreen();
        expect(filterListNavBar.getByText('取车方式')).toBeOnTheScreen();
        expect(filterListNavBar.getByText('门店服务')).toBeOnTheScreen();
        expect(filterListNavBar.getByText('优惠活动')).toBeOnTheScreen();
        expect(filterListNavBar.getByText('取车证件')).toBeOnTheScreen();
        expect(filterListNavBar.getByText('驾驶员驾龄')).toBeOnTheScreen();
        expect(filterListNavBar.getByText('门店评分')).toBeOnTheScreen();
        expect(filterListNavBar.getByText('租车公司')).toBeOnTheScreen();

        // 230496 1、弹出浮层，箭头方向变为向上 2、点击阴影区域，收起浮层
        expect(Page.getByText('')).toBeOnTheScreen();

        // 230497 选择筛选条件，点击【清除】，弹窗关闭，取消所有筛选条件
        // TODO: 关闭弹窗未实现
        // fireEvent.press(Page.getByText('清除'));
      });
    },
  );
  test(
    createInterTestName({
      testId: [230495, 230499],
      name: '快速选车',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const Page = await renderPage();

      const filterBar = await Page.getByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );

      const quickFilterTouchable = await filterBar.findAllByType(
        TouchableOpacity,
      )[1];

      // 点击：快速选车
      await act(async () => {
        fireEvent.press(quickFilterTouchable);
      });
      const filterModal = within(
        Page.getByTestId('car_testid_comp_filter_horizontal_list'),
      );

      // 230499
      // 选中车龄“半年内车龄”,选中项高亮展示
      await act(async () => {
        fireEvent.press(filterModal.getByText('半年内车龄'));
      });
      const images = await Page.container.findAllByType(Image);
      const selectedFilters = images.filter(
        image => image.props.source.uri === SelectedImageIcon,
      );
      // TODO: 数值校验不稳定，改为模糊校验
      expect(selectedFilters.length).toBeGreaterThan(0);

      // 230495
      // 同时选中不同类别的筛选项，筛选个数为N
      // 1.快速选车Tab上面的数字显示为：N
      // TODO: 2.列表页底部回显对应筛选项的名称
      await act(async () => {
        fireEvent.press(filterModal.getByText('自动挡'));
        fireEvent.press(filterModal.getByText('携程优选'));
        fireEvent.press(filterModal.getByText('倒车影像'));
      });
      const images2 = await Page.container.findAllByType(Image);
      const selectedFilters2 = images2.filter(
        image => image.props.source.uri === SelectedImageIcon,
      );
      // TODO: 数值校验不稳定，改为模糊校验
      expect(selectedFilters2.length).toBeGreaterThan(0);
    },
  );
  test(
    createInterTestName({
      testId: [230485, 230486, 230487],
      name: '默认排序项',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const Page = await renderPage();
      await waitRender(async () => {
        // 无法检测选中样式，判断sortFilter数据是否正确
        // 230485, 230487 首次进入列表页，查看默认选中的排序项
        const sortFilter =
          getStore().getState()?.List?.selectedFilters?.sortFilter;
        expect(sortFilter).toBe('1');

        // 230486 推荐排序；快速选车；更多筛选
        const filterBar = await Page.findByTestId(
          UITestID.car_testid_page_list_filter_bar,
        );
        expect(filterBar).toHaveTextContent('推荐排序');
        expect(filterBar).toHaveTextContent('快速选车');
        expect(filterBar).toHaveTextContent('更多筛选');
      });
    },
  );

  test(
    createInterTestName({
      testId: [230531, 230532, 230533, 230534, 230536, 230537, 489233],
      name: '租车中心、侧边导航',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      const Page = await renderPage();
      await waitRender(async () => {
        // 230531 230532

        // 230533 点击【筛选站内车型】预期：按钮变为【取消筛选】筛选项【携程租车中心】高亮展示

        // 230534 点击模块 预期：跳转取车指引落地页

        // 230536 侧边导航 默认tab 预期：默认为”全部“tab
        const vehicleGroupBar = within(
          Page.getByTestId(UITestID.car_testid_page_list_vehicle_bar),
        );
        const vehicleGroupBarDom = await Page.findByTestId(
          UITestID.car_testid_page_list_vehicle_bar,
        );
        await waitFor(async () => {
          const groupBarAll = await vehicleGroupBar.findByText('全部');
          expect(groupBarAll).toHaveStyle({
            color: '#006ff6',
          });
        });
        // 489233
        expect(vehicleGroupBarDom).toHaveTextContent(
          '全部¥62起经济轿车¥62起新能源¥70起舒适轿车¥78起SUV¥78起商务车¥145起豪华轿车¥148起跑车¥288起房车¥1200起小巴士¥488起皮卡¥168起',
        );
        // 230537 切换”经济轿车“ 预期：左侧tab切换为”经济轿车“
        await waitFor(async () => {
          const sideBar01 = await vehicleGroupBar.findByText('经济轿车');
          fireEvent.press(sideBar01);
          expect(sideBar01.parent).toHaveStyle({
            color: '#006ff6',
          });
        });
      });
    },
  );

  test(
    createInterTestName({
      testId: [489213, 489214, 489215, 489220],
      name: '切换排序项',
    }),
    async () => {
      let paramSort = null;
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      jest.spyOn(CarFetch, 'getListProduct').mockImplementation(param => {
        paramSort = param;
        return res489213;
      });
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );

      await waitRender(async () => {
        // 默认排序项 489215
        const sortFilter =
          getStore().getState()?.List?.selectedFilters?.sortFilter;
        expect(sortFilter).toBe('1');

        // 点击推荐排序
        const sortMenuBtn = await filterBar.findAllByType(TouchableOpacity)[0];
        fireEvent.press(sortMenuBtn);

        // 点击 总价 低-高 排序
        const filterSortModal = await Page.findByTestId(
          UITestID.car_testid_page_list_filter_sort_modal,
        );
        const priceSortBtn = await filterSortModal.findAllByType(
          TouchableOpacity,
        )[1];
        fireEvent.press(priceSortBtn);

        // 切换排序项 489213 判断点击价格筛选后的请求参数的sortType为2
        expect(paramSort.sortType).toEqual(2);
      });

      // 489214 推荐排序
      const filterSortModal = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_sort_modal,
      );
      const priceSortBtn = await filterSortModal.findAllByType(
        TouchableOpacity,
      )[1];
      const sortTexts = priceSortBtn.findAllByType(Text);
      expect(sortTexts[0]).toHaveStyle({ color: '#006ff6' });
      expect(sortTexts[1]).toHaveStyle({ color: '#0086f6' });

      // 489220 排序筛选样式
      expect(filterBar).toHaveTextContent('总价 低→高');
      expect(filterBar).toHaveTextContent('快速选车');
      expect(filterBar).toHaveTextContent('更多筛选');
    },
  );
});

describe('列表页-筛选排序', () => {
  test(
    createInterTestName({
      testId: [489688],
      name: '组合筛选',
    }),
    async () => {
      jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'getListProduct')
        .mockImplementation(() => Promise.resolve(res489688));
      const Page = await renderPage();
      const filterBar = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_bar,
      );

      await waitRender(async () => {
        // 点击推荐排序
        const filterMenu = await filterBar.findAllByType(TouchableOpacity)[1];
        fireEvent.press(filterMenu);
      });

      const filterModal = within(
        Page.getByTestId('car_testid_comp_filter_horizontal_list'),
      );
      // 选中“半年内车龄”
      await act(async () => {
        fireEvent.press(filterModal.getByText('半年内车龄'));
      });

      // 选中“自动挡”
      await act(async () => {
        fireEvent.press(filterModal.getByText('自动挡'));
      });

      // 点击完成筛选按钮
      const confirmFilter = await Page.findByTestId(
        UITestID.car_testid_page_list_filter_modal_confirm_btn,
      );
      await act(async () => {
        fireEvent.press(confirmFilter);
      });

      expect(filterBar).toHaveTextContent('推荐排序');
      expect(filterBar).toHaveTextContent('快速选车');
      // expect(filterBar).toHaveTextContent('2');
      expect(filterBar).toHaveTextContent('更多筛选');
    },
  );
});
