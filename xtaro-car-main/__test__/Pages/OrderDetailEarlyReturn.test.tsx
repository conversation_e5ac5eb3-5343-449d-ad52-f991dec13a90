/* eslint-disable no-underscore-dangle */
import { act, waitFor } from '@testing-library/react-native';
import { AppContext, Utils, CarStorage } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName } from '../PageProvider';
import UITestId from '../../src/pages/xcar/Constants/UITestID';

// @ts-ignore 禁止Redux Log
// __DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    saveIsd: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));
// 页面初始化
const renderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '36510',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { name: 1 }, initialState);
};

describe('订详页-提前还车', () => {
  // 用车中
  test(
    createInterTestName({
      testId: [408182, 408181, 408183],
      name: '用车中',
    }),
    async () => {
      await act(async () => {
        const state = {
          OrderDetail: {
            reqOrderParams: {
              orderId: 408160,
            },
          },
        };
        jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
        const Page = await renderPage(state);
        await waitFor(() => {
          const earlyReturnButton = Page.getByTestId(
            `${UITestId.car_testid_page_order_op_btn}_提前还车`,
          );
          // 用车中订单状态-展示按钮
          expect(earlyReturnButton).toBeTruthy();
          // 按钮高亮
          expect(earlyReturnButton?.props?.style?.backgroundColor).toEqual(
            '#006ff6',
          );
        });
      });
    },
  );
});
