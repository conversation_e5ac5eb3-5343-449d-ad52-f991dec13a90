import { within, fireEvent } from '@testing-library/react-native';
import { AppContext, Utils, CarFetch } from '../../src/pages/xcar/Util/Index';
import { createPage, createInterTestName, TEST_TIMEOUT } from '../PageProvider';
import { waitRender, renderWithAction } from '../testHelpers';
import { UITestID } from '../../src/pages/xcar/Constants/Index';

const orderRes3147880 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3147894.json');
const orderRes3232860 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3232860.json');
const orderRes3147922 = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/3147922.json');
const orderRes********** = require('../../__mocks__/restful/18862/OSDQueryOrder/OSD/**********.json');

// @ts-ignore 禁止Redux Log
// __DEV__ = false;
jest.mock('../../src/pages/xcar/Util/CarStorage.ts', () => ({
  __esModule: true,
  default: {
    remove: jest.fn(),
    save: jest.fn(),
    saveIsd: jest.fn(),
    loadAsync: jest.fn(() => '[]'),
    load: jest.fn(() => '[]'),
    loadSyncV2: jest.fn(() => '[]'),
  },
}));

const renderOrderPage = (initialState = undefined) => {
  const container =
    // eslint-disable-next-line global-require
    require('../../src/pages/xcar/Containers/OrderDetailContainer').default;
  const urlQuery: any = {
    initialPage: 'OrderDetail',
    orderId: '36510166399',
  };
  AppContext.setUrlQuery(urlQuery);
  return createPage(container, { name: 1 }, initialState);
};

describe('订详旅行限制', () => {
  test(
    createInterTestName({
      testId: [3840173, 4068247, 4068268],
      name: '跨境政策-支持跨境',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 408077,
          },
          storageCardsTitle: [],
        },
      };

      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes3147880));

      const page = await renderOrderPage(state);

      await waitRender(() => {
        expect(page.container).toHaveTextContent('旅行限制跨境政策选择');
        expect(page.container).toHaveTextContent(
          '若您的行程中涉及跨境，请提前选择了解相关政策及费用',
        );
      });
    },
  );
  test(
    createInterTestName({
      testId: [3840152, 4068254],
      name: '跨境政策-不支持跨境',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 408077,
          },
          storageCardsTitle: [],
        },
      };

      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes3147922));

      const page = await renderOrderPage(state);

      await waitRender(() => {
        expect(page.container).toHaveTextContent(
          '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。',
        );
      });
    },
  );
  test(
    createInterTestName({
      testId: [3840166, 3840187, 4068261, 4068275],
      name: '跨境政策-支持跨岛，支持跨州',
    }),
    async () => {
      const state = {
        OrderDetail: {
          reqOrderParams: {
            orderId: 408077,
          },
          storageCardsTitle: [],
        },
      };

      jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
      jest
        .spyOn(CarFetch, 'queryOrder')
        .mockImplementation(() => Promise.resolve(orderRes**********));

      const page = await renderOrderPage(state);

      await waitRender(() => {
        expect(page.container).toHaveTextContent('允许跨岛：条件跨岛');
        expect(page.container).toHaveTextContent('允许跨州：跨州政策允许');
      });
    },
  );
});
test(
  createInterTestName({
    testId: [4068282, 4068289],
    name: '跨州政策-不支持跨州',
  }),
  async () => {
    jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(false);
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(CarFetch, 'queryOrder')
      .mockImplementation(() => Promise.resolve(orderRes3147922));
    const Page = await renderOrderPage();
    await waitRender(async () => {
      const countryItem = await Page.queryByTestId(
        'c_testid_travelLimitItem_跨州政策',
      );
      expect(countryItem).toHaveTextContent(
        '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨州/省，建议您更换其它租车公司或车型组。',
      );
      expect(Page.container).toHaveTextContent(
        '注意：若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。除特别说明的情况外，“跨境费用”为前往单个国家所需的费用。',
      );
    });
  },
);
test(
  createInterTestName({
    testId: [4068296],
    name: '跨岛政策-不支持跨岛',
  }),
  async () => {
    const state = {
      OrderDetail: {
        reqOrderParams: {
          orderId: 408077,
        },
        storageCardsTitle: [],
      },
    };

    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(CarFetch, 'queryOrder')
      .mockImplementation(() => Promise.resolve(orderRes3232860));

    const page = await renderOrderPage(state);

    await waitRender(() => {
      expect(page.container).toHaveTextContent(
        '由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨岛，建议您更换其它租车公司或车型组。',
      );
    });
  },
);

test(
  createInterTestName({
    testId: [4068324, 4068303, 4068310],
    name: '跨境国家选择,确认政策, 修改政策',
  }),
  async () => {
    const state = {
      OrderDetail: {
        reqOrderParams: {
          orderId: 408077,
        },
        storageCardsTitle: [],
      },
    };
    jest.spyOn(Utils, 'isCtripOsd').mockReturnValue(true);
    jest
      .spyOn(CarFetch, 'queryOrder')
      .mockImplementation(() => Promise.resolve(orderRes3147880));

    const Page = await renderOrderPage(state);
    await waitRender(async () => {
      const countryItem = await Page.queryByTestId(
        'c_testid_travelLimitItem_跨境政策',
      );
      const selectBtn = await within(countryItem).queryByText('选择');
      expect(selectBtn).toBeTruthy();
      let countriesModal;
      await renderWithAction({
        action: async () => {
          fireEvent.press(selectBtn);
        },
        expect: async () => {
          countriesModal = await Page.queryByTestId(
            UITestID.c_testid_countriesListModal,
          );
          expect(countriesModal).toBeTruthy();
        },
      });
      const btn1 = await within(countriesModal).queryByText('加拿大');
      [btn1].forEach(async item => {
        await fireEvent.press(item);
      });
      const countriesListFooter = await Page.queryByTestId(
        UITestID.c_testid_countriesListFooter,
      );
      const submitBtn = await within(countriesListFooter).queryByText('确定');
      await renderWithAction({
        action: async () => {
          fireEvent.press(submitBtn);
        },
        expect: async () => {
          expect(countryItem).toHaveTextContent('修改');
          expect(countryItem).toHaveTextContent('加拿大');
        },
      });
      const modifyBtn = await within(countryItem).queryByText('修改');
      expect(modifyBtn).toBeTruthy();
    });
  },
  TEST_TIMEOUT,
);
