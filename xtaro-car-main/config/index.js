const path = require('path');
const config = {
  alias: {
    '@ctrip/rn_com_car/dist': path.resolve(
      __dirname,
      '..',
      'src/pages/xcar/Common',
    ),
    '@c2x': path.resolve(__dirname, '..', 'src/pages/xcar/c2x'),
  },
  transformSassName: true,
  // 设计稿尺寸
  designWidth: 750,
  // 设计稿尺寸换算规则
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
  },
  routes: [
    {
      path: 'pages/xcar/entry/home/<USER>',
      subRoot: 'pages/xcar',
      crnRouterName: 'Home',
      h5RouterName: 'Home',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/homemini/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Home',
      h5RouterName: 'Home',
      platform: ['mini'],
    },
    {
      path: 'pages/xcar/entry/list/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'List',
      h5RouterName: 'List',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/location/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Location',
      h5RouterName: 'Location',
      platform: ['crn', 'mini', 'h5'],
      jumpAnimation: 'bottom',
    },
    {
      path: 'pages/xcar/entry/homeheader/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'HomeHeader',
      h5RouterName: 'HomeHeader',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/vendorlist/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'VendorList',
      h5RouterName: 'VendorList',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/guide/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Guide',
      h5RouterName: 'Guide',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/coupon/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Coupon',
      h5RouterName: 'Coupon',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/booking/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Booking',
      h5RouterName: 'Booking',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/product/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Product',
      h5RouterName: 'Product',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/market/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Market',
      h5RouterName: 'Market',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/license/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'License',
      h5RouterName: 'License',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/debug/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Debug',
      h5RouterName: 'Debug',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/apidetail/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'ApiDetail',
      h5RouterName: 'ApiDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/driverlist/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'DriverList',
      h5RouterName: 'DriverList',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/driveredit/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'DriverEdit',
      h5RouterName: 'DriverEdit',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/vehmodal/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'VehModal',
      h5RouterName: 'VehModal',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/orderdetail/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'OrderDetail',
      h5RouterName: 'OrderDetail',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/onlineauth/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'OnlineAuth',
      h5RouterName: 'OnlineAuth',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/materials/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Materials',
      h5RouterName: 'Materials',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/policy/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Policy',
      h5RouterName: 'Policy',
      platform: ['crn', 'h5'],
      jumpAnimation: 'bottom',
    },
    {
      path: 'pages/xcar/entry/extras/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Extras',
      h5RouterName: 'Extras',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/packageincludes/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'PackageIncludes',
      h5RouterName: 'PackageIncludes',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/credentials/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Credentials',
      h5RouterName: 'Credentials',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/ordercancel/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'OrderCancel',
      h5RouterName: 'OrderCancel',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/orderrefunddetail/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'OrderRefundDetail',
      h5RouterName: 'OrderRefundDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/orderchange/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'OrderChange',
      h5RouterName: 'OrderChange',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/supplementlist/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'SupplementList',
      h5RouterName: 'SupplementList',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/renewlist/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'RenewList',
      h5RouterName: 'RenewList',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/violationdetail/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'ViolationDetail',
      h5RouterName: 'ViolationDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/damagedetail/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'DamageDetail',
      h5RouterName: 'DamageDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/supplement/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Supplement',
      h5RouterName: 'Supplement',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/rerent/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Rerent',
      h5RouterName: 'Rerent',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/advancereturn/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'AdvanceReturn',
      h5RouterName: 'AdvanceReturn',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/orderlimitrulespage/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'OrderLimitRulesPage',
      h5RouterName: 'OrderLimitRulesPage',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/insuranceorderdetail/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'InsuranceOrderDetail',
      h5RouterName: 'InsuranceOrderDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/ordervialationrule/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'OrderVialationRule',
      h5RouterName: 'OrderVialationRule',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/modifyorder/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'ModifyOrder',
      h5RouterName: 'ModifyOrder',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/modifyorderconfirm/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'ModifyOrderConfirm',
      h5RouterName: 'ModifyOrderConfirm',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/modifycoupon/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'ModifyCoupon',
      h5RouterName: 'ModifyCoupon',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/rebookhome/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'RebookHome',
      h5RouterName: 'RebookHome',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/carrentalcenter/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'CarRentalCenter',
      h5RouterName: 'CarRentalCenter',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/isdagreement/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'IsdAgreement',
      h5RouterName: 'IsdAgreement',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/messageassistant/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'MessageAssistant',
      h5RouterName: 'MessageAssistant',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/instructions/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Instructions',
      h5RouterName: 'Instructions',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/member/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Member',
      h5RouterName: 'Member',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/memberdetail/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'MemberDetail',
      h5RouterName: 'MemberDetail',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/memberbirth/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'MemberBirth',
      h5RouterName: 'MemberBirth',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/memberpoints/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'MemberPoints',
      h5RouterName: 'MemberPoints',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/vehicledamageprove/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'VehicleDamageProve',
      h5RouterName: 'VehicleDamageProve',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/recommendvehicle/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'RecommendVehicle',
      h5RouterName: 'RecommendVehicle',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/limitmap/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'LimitMap',
      h5RouterName: 'LimitMap',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/depositfree/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'DepositFree',
      h5RouterName: 'DepositFree',
      platform: ['crn', 'h5'],
    },
    {
      path: 'pages/xcar/entry/album/index',
      subRoot: 'pages/xcar',
      crnRouterName: 'Album',
      h5RouterName: 'Album',
      platform: ['crn', 'h5'],
    },
  ],
  copyFolders: [
    {
      sourcePath: './scripts',
    },
  ],
  plugins: ['@ctrip/babel-plugin-replace-mini/conditionalCompile'],
};

//XTARO_ENV在各端build时会自动设置
module.exports = function (merge) {
  if (process.env.XTARO_ENV === 'crn') {
    return merge({}, config, require('./crn.config.js'));
  }
  if (process.env.XTARO_ENV === 'h5') {
    return merge({}, config, require('./h5.config.js'));
  }
  if (process.env.XTARO_ENV === 'mini') {
    return merge({}, config, require('./mini.config.js'));
  }
  return config;
};
