const routeConfig = require('./miniRouteConfig');
module.exports = {
  routes: [...routeConfig.routes],
  pipeline: {
    weapp: 'xtaro-weapp-release-standalone',
  },
  gitConfig: {
    baseConfig: {
      git: '****************************:car/xtaro-car-main.git',
      branch: 'mini_optimize_mainpackage',
    },
    miniConfig: {
      appId: 'wxe4bc565cbbf5e289',
      mcdAppId: '5240',
      weapp: '****************************:tinyapp/taro-car-auto-weapp.git',
    },
  },
}