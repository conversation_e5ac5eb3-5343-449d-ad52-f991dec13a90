// const t = require('@babel/types');
const config_800017 = require('./800017.config');
const config_800018 = require('./800018.config');
const config_5240 = require('./5240.config');
const config_5288 = require('./5288.config');
const config_5227 = require('./5227.config');
module.exports = {
  routes: [],
  babelConfig: {
    plugins: [
      ['@ctrip/babel-plugin-replace-mini/replaceMiniChannel'],
      ['@ctrip/babel-plugin-replace-mini/replaceMiniStyle'],
      ['@ctrip/babel-plugin-replace-mini/replaceFn',  {
        "replacements": {
          'CarABTesting.isListInPage': true,
          'GetABCache.isISDShelves': false,
          'GetABCache.isISDShelvesB': false,
          'Utils.isCtripOsd': false,
          'Utils.isCtripIsd': true,
        }
      }],
    ],
  },
  // 待xTaro支持
  // defineConstants: {
  //   __QALI__: JSON.stringify(process.env.XTARO_APPID === '800017'),
  //   __MINI__: JSON.stringify(process.env.XTARO_ENV === 'mini'),
  // },

  // lodash-es/debounce异常解决方案二：替换lodash-es/debounce至lodash/debounce
  // preHandleImport: (node, path) => {
  //   const moduleName = (node.source && node.source.value) || '';
  //   //  const { node } = path;

  //   // 仅针对 lodash-es 的导入路径进行替换
  //   if (moduleName === 'lodash-es') {
  //     // 查找是否有 debounce 导入
  //     const debounceSpecifier = node.specifiers.find(
  //       specifier =>
  //         specifier.type === 'ImportSpecifier' &&
  //         specifier.imported.name === 'debounce',
  //     );

  //     // 如果找到了 debounce 导入
  //     if (debounceSpecifier) {
  //       // 移除 debounce 导入
  //       node.specifiers = node.specifiers.filter(
  //         specifier =>
  //           specifier.type !== 'ImportSpecifier' ||
  //           specifier.imported.name !== 'debounce',
  //       );

  //       // 插入新的 import lodashDebounce from 'lodash/debounce';
  //       const newImport = t.importDeclaration(
  //         [t.importDefaultSpecifier(t.identifier('lodashDebounce'))],
  //         t.stringLiteral('lodash/debounce'),
  //       );

  //       // 将新的导入插入到文件的头部
  //       path.insertBefore(newImport);

  //       // 如果 lodash-es 没有其他导入，删除这个导入语句
  //       if (node.specifiers.length === 0) {
  //         path.remove();
  //       }
  //     }
  //   }
  // },
  pipeline: {
    // weapp: "weapp-release"
  },
  commonModules: [], // 暂时为数组类型，待 cli 发布后，再改成 对象（从而支持更复杂的配置需求）
  5227: config_5227,
  5240: config_5240,
  5288: config_5288,
  // 去哪儿支付宝小程序
  800017: config_800017,
  // 去哪儿微信小程序
  800018: config_800018,
};
