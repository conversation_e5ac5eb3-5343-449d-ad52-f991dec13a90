module.exports = {
  100055438: {
    // T端示例，100055438是captain appId。修改appid时候，package中的appid也需要修改。运行dev:trip-h5 和 build:trip-h5 时候，这里的配置会覆盖外层的配置
    sharkOptions: {
      //sharkOptions 当使用XShark模块使需要配置此配置项
      init: {}, //Refer to shark-sdk http://shark.ibu.ctripcorp.com/docs/sdk/shark-node-sdk，initial content。The log level is as follows：DEBUG:0,INFO:1,WARNING:2,ERROR:3,NONE:4
      preCache: {
        //Refer to http://npm.release.ctripcorp.com/package/@ctrip/shark-js-sdk， warm-up
        locales: ['en-US'], //language type that need to be warmed up
        appIds: [6001, 6002], //appid that need warmed up， 6002是如需使用L10N必须要加的公共库
      },
      openSharkI18n: true,
    },
    extraPackageJson: {
      //合并NFES产物中的package.json
      dependencies: {
        '@ctrip/shark-js-sdk': '^5.0.2',
        '@ctrip/shark-sdk': '^5.5.1',
        '@ctrip/shark-l10n': '^4.2.2',
        '@ctrip/cargo-nodejs': '^3.6.0',
        'lodash-es': '4.17.21',
      },
    },
  },
  customExtraPlatform: process.env.XTARO_EXTRA || '', // 直接使用process.env.XTARO_EXTRA 作为配置,由package.json 里面命令行--extra trip（优先级更高） 或者 -T 传入
  appId: '1000012345',
  pipeline: {
    Build: {
      appId: '1000012345',
    },
    BuildTrip: {
      appId: '100055438',
    },
  },
  baseWidth: 750,
};
