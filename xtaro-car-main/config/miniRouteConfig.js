const subRootPrefix = 'pages/xcar/entry'
// 需要打大分包的渠道：去哪儿微信、微信主板、微信独立版
const ONEPACKAGEAPPIDS = [5240,800018,5227]
module.exports = {
  routes: [
    {
      path: 'pages/xcar/entry/homemini/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) && subRootPrefix,
      crnRouterName: 'Home',
      h5RouterName: 'Home',
      platform: ['mini'],
    },
    {
      path: 'pages/xcar/entry/debugmini/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ? subRootPrefix :  `${subRootPrefix}/debugmini`,
      crnRouterName: 'DebugMini',
      h5RouterName: 'DebugMini',
      platform: ['mini'],
    },
    {
      path: 'pages/xcar/entry/list/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ? subRootPrefix :  `${subRootPrefix}/list`,
      crnRouterName: 'List',
      h5RouterName: 'List',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/location/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ? subRootPrefix :  `${subRootPrefix}/location`,
      crnRouterName: 'Location',
      h5RouterName: 'Location',
      platform: ['crn', 'mini', 'h5'],
      jumpAnimation: 'bottom',
    },
    {
      path: 'pages/xcar/entry/vendorlist/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ? subRootPrefix :  `${subRootPrefix}/vendorlist`,
      crnRouterName: 'VendorList',
      h5RouterName: 'VendorList',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/booking/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ? subRootPrefix :  `${subRootPrefix}/booking`,
      crnRouterName: 'Booking',
      h5RouterName: 'Booking',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/driverlist/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ? subRootPrefix :  `${subRootPrefix}/driverlist`,
      crnRouterName: 'DriverList',
      h5RouterName: 'DriverList',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/driveredit/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ? subRootPrefix :  `${subRootPrefix}/driveredit`,
      crnRouterName: 'DriverEdit',
      h5RouterName: 'DriverEdit',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/carNew/isd/subPages/list/index',
      subRoot: 'pages/carNew/isd/subPages/list',
      crnRouterName: 'ListTrans',
      h5RouterName: 'ListTrans',
      platform: [ 'mini'],
    },
    {
      path: 'pages/carNew/isd/indexNew/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ?'' : 'pages/carNew/isd/indexNew',
      crnRouterName: 'HomeTrans',
      h5RouterName: 'HomeTrans',
      platform: [ 'mini'],
    },
    {
      path: 'pages/xcar/entry/policy/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ? subRootPrefix :  `${subRootPrefix}/policy`,
      crnRouterName: 'Policy',
      h5RouterName: 'Policy',
      platform: ['crn', 'mini', 'h5'],
      jumpAnimation: 'bottom',
    },
    {
      path: 'pages/xcar/entry/extras/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ? subRootPrefix :  `${subRootPrefix}/extras`,
      crnRouterName: 'Extras',
      h5RouterName: 'Extras',
      platform: ['crn', 'mini', 'h5'],
    },
    {
      path: 'pages/xcar/entry/carrentalcenter/index',
      subRoot: ONEPACKAGEAPPIDS.includes(+process.env.XTARO_APPID) ? subRootPrefix :  `${subRootPrefix}/carrentalcenter`,
      crnRouterName: 'CarRentalCenter',
      h5RouterName: 'CarRentalCenter',
      platform: ['crn', 'mini', 'h5'],
    },
  ],
}