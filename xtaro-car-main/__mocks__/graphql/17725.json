{"data": {"queryAdditionPayment": {"responseStatus": {"timestamp": "/Date(1699588205937+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "", "cost": 120}, "additionalPaymentList": []}, "queryPriceInfo": {"feeDetailInfo": {"chargesInfos": [{"title": "车辆租金", "code": "CAR_RENTAL_FEE", "currencyCode": "CNY", "currentTotalPrice": 125, "items": [{"title": "租车费", "subTitle": "", "code": "1001", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 125, "currentTotalPrice": 125, "priceDailys": [{"date": "11月21日 周二", "priceStr": "¥ 125", "showType": 0}]}]}, {"title": "车辆取还费", "code": "CAR_SERVICE_FEE", "currencyCode": "CNY", "currentTotalPrice": 200, "items": [{"title": "送车上门服务费", "subTitle": "", "code": "11026", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 100, "currentTotalPrice": 100}, {"title": "上门取车服务费", "subTitle": "", "code": "11027", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 100, "currentTotalPrice": 100}]}, {"title": "车行手续费", "code": "SERVICE_CHARGE", "currencyCode": "CNY", "currentTotalPrice": 30, "items": [{"title": "车行手续费", "description": "（含车辆清洁、单据打印等）", "code": "1003", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 30, "currentTotalPrice": 30}]}, {"title": "车行保障服务费", "code": "CAR_GUARANTEE_FEE", "currencyCode": "CNY", "currentTotalPrice": 70, "items": [{"title": "基础服务费", "description": "车损保障赔付1500元以上损失，含玻璃、不含轮胎，三者保障保额200万元，车损5000元以下免收折旧费，停运费正常收取", "code": "1002", "size": "¥ 70 x1天", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 70, "currentTotalPrice": 70}]}], "equipmentInfos": [], "discountList": [], "couponInfos": [], "depositInfo": {"items": []}, "chargesSummary": {"title": "订单总额", "code": "Summary", "type": 103, "currencyCode": "CNY", "currentTotalPrice": 425, "payMode": 2, "items": [{"title": "在线支付", "code": "1", "currencyCode": "CNY", "currentTotalPrice": 425}], "notices": []}, "rentalTerm": 1, "userPoints": {"title": "还车后可获", "subTitle": "积分", "currencyPrice": 63, "pointsTip": "已享1.0倍加速", "pointsNotice": [{"title": {"stringObjs": [{"content": "本单可获积分", "style": "1"}]}, "desc": [{"stringObjs": [{"content": "订单成交金额 x 15% x 1.0 "}]}]}, {"title": {"stringObjs": [{"content": "规则说明", "style": "1"}]}, "desc": [{"stringObjs": [{"content": "1.使用返现、优惠券等活动订单，优惠金额不参与携程积分计算；"}]}, {"stringObjs": [{"content": "2.使用积分抵现功能支付部分不参与携程积分计算；"}]}, {"stringObjs": [{"content": "3.积分将在行程完成且订单状态更新为完成状态后发放"}]}]}]}}, "responseStatus": {"timestamp": "/Date(1699588206178+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 492}}, "queryWarningList": {"baseResponse": {"isSuccess": true, "code": "200", "extMap": {}}, "ResponseStatus": {"Timestamp": "/Date(1699588205828+0800)/", "Ack": "Success", "Errors": []}, "warningDtos": []}, "getEasyLifeTagInfo": {"baseResponse": {"isSuccess": true}, "ResponseStatus": {"Timestamp": "/Date(1699588205829+0800)/", "Ack": "Success", "Errors": []}, "easyLifeTag": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "优质车况", "type": 1, "description": "3年内车龄", "sortNum": 3, "subTitle": "", "showLayer": 0}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满", "sortNum": 10, "subTitle": "", "showLayer": 0}, {"title": "车辆守护升级", "type": 0, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 99, "subTitle": "*覆盖损失范围以预订页面内披露为准"}]}, "getLicense": {"status": 1, "companyName": "昆山宁盟汽车科技服务有限公司", "licenseImgUrl": "http://images4.c-ctrip.com/target/0414e120008kz219nDA46.png?proc=watermark/t_6JCl5Lia5omn54Wn5L-h5oGv5YWs56S65LiT55So,s_70,l_center,r_315,d_40,c_696969,F732", "licenseDesc": "租车服务提供方", "ResponseStatus": {"Timestamp": "/Date(1699588205839+0800)/", "Ack": "Success", "Errors": [], "Build": null, "Version": null, "Extension": []}, "defaultCompanyName": "上海华程西南国际旅行社有限公司", "defaultLicenseImgUrl": "http://pic.c-ctrip.com/car_isd/app/h5/lisences.png", "defaultlLicenseDesc": "预订服务提供方"}, "queryCertificateV3": {"responseStatus": {"timestamp": "/Date(1699588206290+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 471}, "certificateV3List": [{"isActive": true, "isShow": true, "certificateV3": {"certificateType": "1", "userName": "张三", "certificateNo": "23232323232323232", "sex": "男", "birthday": "1993-11-30", "address": "xxxxxxxxxxxxx", "notionality": "汉", "validPeriodBegin": "2020-10-13", "validPeriodEnd": "2040-10-13", "issueAuthority": "xxxxxxxxxxxxx", "mainImageUrl": "url", "subImageUrl": "url", "firstIssueDate": "1111-01-01", "fileNumber": "", "licenceClass": ""}}, {"isActive": false, "certificateV3": {"certificateType": "2", "certificateNo": "23232323232323232"}}], "supportInfo": {"authStatus": 0, "certificationStatus": 300, "defaultAuthStatus": false, "isShow": true, "showTitle": "认证驾驶员，免部分取车手续", "buttonText": "继续认证", "guideText": "您的认证尚未完成，完成后可免部分取车手续"}, "policy": {"iDCardShowType": 0, "licenceShowType": 0, "additionTips": ["该门店支持认证完成后，无需出示证件", "如您无法在线认证或认证异常，可在取车时现场办理", "用车过程中请务必随身携带证件原件"], "isChange": false}, "attrDto": {"sort": 6, "history": "false"}}, "queryOrderInsAndXProduct": {"responseStatus": {"timestamp": "/Date(1699588206308+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "", "returnMsg": "success", "requestId": "", "cost": 530}, "insuranceAndXProductList": [{"name": "人身及财物险", "code": "2000896", "title": "人身及财物险", "description": ["人身意外保额30.0万元/人，医疗费用保额3万元/人", "集中隔离津贴100元/天，最高1400元/人", "随车财务损失2000元/车", "专业道路救援服务"], "targetTag": [{"title": "仅支持线上购买"}, {"title": "87%用户选择购买"}], "specificName": 0, "sourceFrom": 2, "productId": 2000896, "requestId": "36515168115-2000896", "orderTitle": "保险加购", "price": 25, "localCurrencyCode": "CNY", "localTotalPrice": 25, "currentTotalPrice": 25, "currentCurrencyCode": "CNY", "quantity": 1, "group": 1, "status": 0, "canUpgrade": false, "toDetailStatus": 0, "accident": [{"title": "立即联系门店及交通警察", "desc": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店已获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"title": "拍照并留存记录信息", "desc": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"title": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"title": "请您配合交警处理完事故后，方可离开事故现场"}, {"title": "准备材料，进行保险理赔流程"}], "insBottomDesc": {"title": "以下情况无法为您提供保障服务：", "desc": ["发生事故时未及时通知租车公司或未申报保险", "无事故证明材料或无保险理赔材料", "无证驾驶、酒驾、超速等其他保险公司不予理赔或责任免除的场景"]}, "insAndXProductLabelInfos": [{"sort": "1", "name": "平安财险承保", "color": "0"}]}], "purchased": {"name": "基础服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 70, "gapPrice": 0, "description": [{"description": "车损保障赔付1500元以上损失，含玻璃、不含轮胎", "type": "CDWcn", "attr": "{\"carCoverage\":\"1500元\",\"vendorId\":80377,\"isBasic\":true,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "RED"}, {"contains": true, "description": "三者保障保额200万元", "type": "TPLcn", "attr": "200万元"}, {"contains": true, "description": "车损5000元以下免收折旧费", "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}"}, {"contains": false, "description": "停运费正常收取", "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}], "allTags": [{"title": "车轮损失自付", "code": "tag0", "colorCode": "<PERSON>"}], "insuranceDetailDescription": [{"title": "车辆损失险", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "1500元以上部分"}}, {"key": "用户或承租方需承担", "value": {"content": "1500元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "保险赔付的100%"}}, {"key": "用户或承租方需承担", "value": {"content": "保险赔付以外的100%"}}]}], "type": "CDWcn"}, {"title": "三者保障", "contains": true, "content": ["保险车辆发生意外事故，导致第三者承受的损失。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "200万元元"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "200万元"}}, {"key": "用户或承租方需承担", "value": {"content": "200万元以上的部分"}}]}], "type": "TPLcn"}, {"title": "折旧费", "contains": true, "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "desc": "", "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "5000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "5000元以上", "value": {"content": "维修费总额的20%"}}], "styleType": 1}], "type": "DPN", "subDesc": ["第三方全责的车损不收折旧费"]}, {"title": "停运费", "contains": false, "content": ["因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为："], "subContent": [{"table": [{"key": "", "value": {"great": false, "content": "停运费 = 车辆维修期间每日订单金额之和 × 100%"}}]}], "type": "OTG", "subDesc": ["每日订单金额包含基本保险，不含手续费及附加服务费", "因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）"]}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：轮胎（含轮毂）损坏、底盘损坏；以及车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "1002", "status": 3, "type": 1, "price": 70}, "upgradeGuarantee": {"rentalGuaranteeTitle": ["车损保障", "三者保障", "免折旧费", "免停运费", "无需垫付"], "vendorServiceDesc": "车行服务是指车行为客人安全用车提供的服务，包含车辆整备（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。", "vendorServiceSubDesc": "1.以上服务与保障由车行基于车辆自身车险所提供，故车损与三者保障仅覆盖车行车险理赔范围内的损失，理赔范围见服务详情。\n2.发生事故后请严格按照理赔要求操作，若未按要求处理导致车险无法理赔，上述保障将会失效，您需承担全额损失。", "packageDetailList": [{"name": "基础服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 70, "gapPrice": 0, "description": [{"description": "1500元以内自付 <tag>tag0", "type": "CDWcn", "attr": "{\"carCoverage\":\"1500元\",\"vendorId\":80377,\"isBasic\":true,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "RED"}, {"contains": true, "description": "200万元", "type": "TPLcn", "attr": "200万元"}, {"contains": true, "description": "车损5000元以下免收", "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}"}, {"contains": false, "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "allTags": [{"title": "车轮损失自付", "code": "tag0", "colorCode": "<PERSON>"}, {"title": "车轮损失自付", "code": "tag0", "colorCode": "<PERSON>"}], "insuranceDetailDescription": [{"title": "车辆损失险", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "1500元以上部分"}}, {"key": "用户或承租方需承担", "value": {"content": "1500元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "保险赔付的100%"}}, {"key": "用户或承租方需承担", "value": {"content": "保险赔付以外的100%"}}]}], "type": "CDWcn"}, {"title": "三者保障", "contains": true, "content": ["保险车辆发生意外事故，导致第三者承受的损失。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "200万元元"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "200万元"}}, {"key": "用户或承租方需承担", "value": {"content": "200万元以上的部分"}}]}], "type": "TPLcn"}, {"title": "折旧费", "contains": true, "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "desc": "", "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "5000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "5000元以上", "value": {"content": "维修费总额的20%"}}], "styleType": 1}], "type": "DPN", "subDesc": ["第三方全责的车损不收折旧费"]}, {"title": "停运费", "contains": false, "content": ["因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为："], "subContent": [{"table": [{"key": "", "value": {"great": false, "content": "停运费 = 车辆维修期间每日订单金额之和 × 100%"}}]}], "type": "OTG", "subDesc": ["每日订单金额包含基本保险，不含手续费及附加服务费", "因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）"]}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：轮胎（含轮毂）损坏、底盘损坏；以及车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "1002", "status": 3, "type": 1, "price": 70}, {"name": "尊享服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 60, "gapPrice": 60, "description": [{"description": "车损保障赔付全部损失，含玻璃、轮胎，车损3万元元以下免收折旧费，免停运费，含无需垫付", "type": "summary", "show": true}, {"description": "全额赔付", "type": "CDWcn", "attr": "{\"carCoverage\":\"0元\",\"vendorId\":80377,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}", "show": false, "descriptionColorCode": "GREEN"}, {"contains": true, "description": "200万元", "type": "TPLcn", "attr": "200万元", "show": false}, {"contains": true, "description": "车损3万元元以下免收", "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"3万元\",\"desc\":\"车损3万元元以下免收折旧费\",\"summary\":\"车损3万元元以下免收折旧费\",\"containsDescription\":\"车损3万元元以下免收\"}", "show": false}, {"contains": true, "type": "OTG", "attr": "{\"cover\":true,\"desc\":\"您无需承担车辆停运费\",\"summary\":\"免停运费\"}", "show": false}, {"contains": true, "type": "Disbursements", "attr": "含无需垫付", "show": false}], "allTags": [], "insuranceDetailDescription": [{"title": "车辆损失险", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失。", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": true, "content": "全部损失"}}, {"key": "用户或承租方需承担", "value": {"content": "0元"}}]}], "type": "CDWcn"}, {"title": "折旧费", "contains": true, "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "desc": "", "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "30000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "30000元以上", "value": {"content": "维修费总额的20%"}}], "styleType": 1}], "type": "DPN", "subDesc": ["第三方全责的车损不收折旧费"]}, {"title": "免停运费", "contains": true, "great": true, "content": ["免收因车损产生的车辆停运费"], "type": "OTG"}, {"title": "无须垫付", "contains": true, "great": true, "content": ["无需垫付定金或相关费用，车辆维修出险后，根据费用单等，补缴给租车公司您所需赔付的部分。"]}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "2011", "status": 0, "type": 0, "price": 60}], "purchasingNotice": {"title": "购买须知", "content": ["1.车行服务由租车公司提供，仅支持取车前购买", "2.若发生续租，已购买的车行服务的订单续租时必须购买相同服务"]}, "vendorServiceDetail": [{"title": "车辆事故处理流程", "type": 1, "description": "车辆事故处理流程的描述", "contents": [{"title": "车辆事故处理流程的内容1", "type": 1, "content": ["车辆事故处理流程的内容2"]}]}, {"title": "车行服务理赔说明", "type": 2, "description": "发生车损（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得理赔", "contents": [{"title": "不予理赔说明", "type": 2, "content": ["1、发生事故时未及时通知租车公司或未申报保险", "2、无事故证明材料或无保险理赔材料", "3、无证驾驶、酒驾、超速等其他保险不予理赔或责任免除场景"]}, {"title": "保险理赔说明", "type": 4, "content": ["1. 保险内容"]}, {"title": "赔付方式", "type": 3, "content": ["1. 赔付方式的内容1", "2. 赔付方式的内容2"]}]}]}, "upgradeSummary": {"type": "one"}, "purchasedSub": [{"name": "基础服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 70, "gapPrice": 0, "description": [{"description": "车损保障赔付1500元以上损失，含玻璃、不含轮胎", "type": "CDWcn", "attr": "{\"carCoverage\":\"1500元\",\"vendorId\":80377,\"isBasic\":true,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "RED"}, {"contains": true, "description": "三者保障保额200万元", "type": "TPLcn", "attr": "200万元"}, {"contains": true, "description": "车损5000元以下免收折旧费", "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}"}, {"contains": false, "description": "停运费正常收取", "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "allTags": [{"title": "车轮损失自付", "code": "tag0", "colorCode": "<PERSON>"}, {"title": "车轮损失自付", "code": "tag0", "colorCode": "<PERSON>"}], "insuranceDetailDescription": [{"title": "车辆损失险", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "1500元以上部分"}}, {"key": "用户或承租方需承担", "value": {"content": "1500元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "保险赔付的100%"}}, {"key": "用户或承租方需承担", "value": {"content": "保险赔付以外的100%"}}]}], "type": "CDWcn"}, {"title": "三者保障", "contains": true, "content": ["保险车辆发生意外事故，导致第三者承受的损失。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "200万元元"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "200万元"}}, {"key": "用户或承租方需承担", "value": {"content": "200万元以上的部分"}}]}], "type": "TPLcn"}, {"title": "折旧费", "contains": true, "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "desc": "", "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "5000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "5000元以上", "value": {"content": "维修费总额的20%"}}], "styleType": 1}], "type": "DPN", "subDesc": ["第三方全责的车损不收折旧费"]}, {"title": "停运费", "contains": false, "content": ["因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为："], "subContent": [{"table": [{"key": "", "value": {"great": false, "content": "停运费 = 车辆维修期间每日订单金额之和 × 100%"}}]}], "type": "OTG", "subDesc": ["每日订单金额包含基本保险，不含手续费及附加服务费", "因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）"]}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：轮胎（含轮毂）损坏、底盘损坏；以及车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "1002", "status": 3, "type": 1, "price": 70}], "tenancyTerm": 1}}}