{"data": {"queryHomePageCard": {"baseResponse": {"code": "200", "requestId": "", "cost": 528, "isSuccess": true, "returnMsg": "success"}, "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1682068821972+0800)/"}, "orderList": [{"isPick": true, "useDay": "173天后", "channel": "ISD", "pickupInfo": {"localDateTime": "2023-10-11 10:00:00", "storeTel": "18117159935;15800651881;15800651881", "title1": "取车方式", "title2": "取车地址", "address": "上海市闵行区高泊飞虹桥天山西路停车场-出入口", "storeID": 153209, "serviceType": "0", "userLongitude": 121.3521744411732, "pickReturnWay": "自行前往门店取车", "userLatitude": 31.22188427709729, "userAddress": "SkyBridge HQ天会"}, "lastEnablePayTime": 1681961459000, "returnDate": 1697162400000, "vehiclePic": "https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0pcn152vdz1tkq982C2.jpg", "ctripOrderStatusDesc": "已确认", "klbVersion": 1, "earlyReturn": false, "ctripOrderStatus": "CAR_CONFIRMED", "orderId": 23761729688, "allOperations": [{"operationId": 13, "enable": true, "icon": "&#xf08f9;", "buttonName": "订单详情"}, {"operationId": 14, "enable": true, "icon": "&#xf08fc;", "buttonName": "联系门店"}, {"operationId": 15, "enable": true, "icon": "&#xf08fa;", "buttonName": "用车必备材料"}], "returnInfo": {"localDateTime": "2023-10-13 10:00:00", "storeTel": "18117159935;15800651881;15800651881", "title1": "还车方式", "title2": "还车地址", "address": "上海市闵行区高泊飞虹桥天山西路停车场-出入口", "storeID": 153209, "serviceType": "0", "userLongitude": 121.3521744411732, "pickReturnWay": "自行前往门店还车", "userLatitude": 31.22188427709729, "userAddress": "SkyBridge HQ天会"}, "pickUpDate": 1696989600000, "locationType": 1, "vehicleName": "福特福睿斯", "useDayDesc": "上海取车"}]}, "getCityTimeZone": {"isSuccessful": true, "cityTimeZone": -8, "ResponseStatus": {"Timestamp": "/Date(1682068821443+0800)/", "Ack": "Success", "Errors": []}}, "queryWarningList": {"baseResponse": {"code": "200", "extMap": {}, "isSuccess": true}, "warningDtos": [], "ResponseStatus": {"Extension": [], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1682068821111+0800)/"}}, "queryMembershipRights4Index": {"baseResponse": {"extMap": {}, "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "OK", "requestId": "79fb58f5-3e01-457d-ae75-b45c5ada93c4", "isSuccess": true}, "rights": {"title": {"stringObjs": [{"content": "钻石贵宾，您可享受以下权益"}]}, "items": [{"code": "VipDiscount", "status": 1, "title": "租车费9折", "subTitle": "钻石贵宾专享", "icon": "https://dimg04.c-ctrip.com/images/0AS3h120009hi6gue7CFE.png", "url": ""}, {"code": "LuxuryCarsDiscount", "status": 1, "title": "豪华车8折", "subTitle": "钻石贵宾专享", "icon": "https://pages.trip.com/cars/components-image/ctrip-app/Member/homeLuxuryCarsDiscount.png"}, {"code": "PointsAcceleration", "status": 1, "title": "2.0倍积分加速", "subTitle": "钻石贵宾", "icon": "https://dimg04.c-ctrip.com/images/0AS1e120009hi6ghz0C65.png", "url": ""}]}, "curLevelName": "钻石贵宾", "curLevelCode": "30", "ResponseStatus": {"Timestamp": "/Date(1664173527841+0800)/", "Ack": "Success", "Errors": []}}, "getReceivePromotion": {"baseResponse": {"extMap": {}, "errorCode": "0", "code": "0", "showMessage": "操作成功", "apiResCodes": [], "returnMsg": "操作成功", "message": "操作成功", "requestId": "3139944c-8400-412b-b2f7-65c0decc5c52", "isSuccess": true}, "title": "限时特惠", "promotions": [{"shortDesc": "腾讯出行领券测试腾讯出行领券测试腾讯出行领券测试", "status": 1, "limitText": "", "deductionList": [], "valueType": 0, "isActive": true, "briefName": "立减¥50", "promotionSecretId": "3ecb6a9cb33b247b31e63570057e079b95d2ba7cc1808e4e1973b6f88b0e79904331d1259f9b8cb3e72fdd9819a39513", "name": "腾讯出行领券测试立减券", "validPeriodDesc": "有效期至2022-11-18", "cornerText": "", "currencyCode": "¥", "couponCode": "", "invaildReason": "", "discountText": "", "value": "50", "scenes": 1, "statusName": "领取", "promotionId": 411935327, "longDesc": "腾讯出行领券测试腾讯出行领券测试"}, {"shortDesc": "qunar国内立减券", "status": 2, "limitText": "", "deductionList": [], "valueType": 1, "isActive": true, "briefName": "免租券", "promotionSecretId": "", "name": "qunar国内立减券立减券", "validPeriodDesc": "有效期至2023-06-01", "cornerText": "", "currencyCode": "", "couponCode": "l<PERSON><PERSON><PERSON><PERSON>", "invaildReason": "", "discountText": "", "value": "超值优惠", "scenes": 1, "statusName": "已领取", "promotionId": 236802966, "longDesc": "qunar国内立减券"}, {"shortDesc": "hzw_回归_test1", "status": 2, "limitText": "", "deductionList": [], "valueType": 0, "isActive": true, "briefName": "立减¥1", "promotionSecretId": "", "name": "hzw_test1立减券", "validPeriodDesc": "有效期至2022-10-13", "cornerText": "新客券", "currencyCode": "¥", "couponCode": "jckjw<PERSON><PERSON><PERSON>", "invaildReason": "", "discountText": "", "value": "1", "scenes": 1, "statusName": "已领取", "promotionId": 203739886, "longDesc": "hzw单次券2"}], "ResponseStatus": {"Timestamp": "/Date(1664173527934+0800)/", "Ack": "Success", "Errors": []}}, "queryYunnanWelfare": {"amount": "￥100", "show": false, "ResponseStatus": {"Extension": [], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1664173527718+0800)/"}, "desc": "云南自驾租车，赠#加油券", "BaseResponse": {"IsSuccess": true, "ReturnMsg": "success", "Cost": 13, "Code": "0", "RequestId": "b80ffaa7-a71c-4a4b-a78e-3400bc3c2d4d"}}, "getStreamRecCityId": {"baseResponse": {"extMap": {}, "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "OK", "requestId": "83dd08ed-727d-45c8-a2d6-ac7d20bb98da", "isSuccess": true}, "recCityId": 17, "ResponseStatus": {"Timestamp": "/Date(1664173527756+0800)/", "Ack": "Success", "Errors": []}, "cityIdType": "IntentCityId"}, "getIndexSuperVip": {"baseResponse": {"extMap": {}, "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "OK", "requestId": "388c0af3-5ef2-4c51-9ce9-4c7f01fc6445", "isSuccess": true}, "ResponseStatus": {"Timestamp": "/Date(1664173527765+0800)/", "Ack": "Success", "Errors": []}}, "homeController": {"filter": [{"type": 1, "text": "只看站内取还", "code": "PickReturn_StationPR"}], "rentcenterId": 2, "pickupOnDoorCode": "PickReturn_PickupOnDoor", "rHub": 1, "baseResponse": {"code": "200", "returnMsg": "success", "isSuccess": true}, "ResponseStatus": {"Timestamp": "/Date(1664173527743+0800)/", "Ack": "Success", "Errors": []}, "pHub": 1}, "queryAdditionPayment": {}, "queryOrderInsAndXProduct": {}, "querySimilarVehicle": {}}}