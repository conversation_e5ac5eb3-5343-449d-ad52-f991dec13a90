{"data": {"queryWarningList": {"baseResponse": {"code": "200", "extMap": {}, "isSuccess": true}, "warningDtos": [], "ResponseStatus": {"Timestamp": "/Date(1688111139778+0800)/", "Ack": "Success", "Errors": []}}, "getEasyLifeTagInfo": {"baseResponse": {"isSuccess": true}, "easyLifeTag": [{"sortNum": 1, "showLayer": 1, "title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "subTitle": "*国定节假日以页面披露为准"}, {"sortNum": 3, "title": "优质车况", "subTitle": "", "type": 1, "description": "3年内车龄", "showLayer": 0}, {"sortNum": 10, "title": "满油取车", "subTitle": "", "type": 0, "description": "保障取车时油量满", "showLayer": 0}, {"sortNum": 99, "title": "车辆守护升级", "subTitle": "*覆盖损失范围以预订页面内披露为准", "type": 0, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧"}], "ResponseStatus": {"Timestamp": "/Date(1688111139777+0800)/", "Ack": "Success", "Errors": []}}, "queryOrderInsAndXProduct": {"upgradeSummary": {"type": "one"}, "purchasedSub": [{"status": 3, "type": 1, "gapPrice": 0, "price": 200, "allTags": [{"title": "车轮损失自付", "colorCode": "<PERSON>", "code": "tag0"}, {"title": "车轮损失自付", "colorCode": "<PERSON>", "code": "tag0"}], "currentDailyPrice": 100, "insuranceDetailDescription": [{"title": "车辆损失险", "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "1500元以上部分"}}, {"key": "用户或承租方需承担", "value": {"content": "1500元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "保险赔付的100%"}}, {"key": "用户或承租方需承担", "value": {"content": "保险赔付以外的100%"}}]}], "contains": true, "type": "CDWcn"}, {"title": "三者保障", "content": [], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "200万元"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "200万元"}}, {"key": "用户或承租方需承担", "value": {"content": "200万元以上的部分"}}]}], "contains": true, "type": "TPLcn"}, {"subDesc": ["第三方全责的车损不收折旧费"], "title": "折旧费", "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "styleType": 1, "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "5000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "5000元以上", "value": {"content": "维修费总额的20%"}}], "desc": ""}], "contains": true, "type": "DPN"}, {"subDesc": ["每日订单金额包含基本保险，不含手续费及附加服务费", "因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）"], "title": "停运费", "content": ["因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为："], "subContent": [{"table": [{"key": "", "value": {"great": false, "content": "停运费 = 车辆维修期间每日订单金额之和 × 100%"}}]}], "contains": false, "type": "OTG"}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：轮胎（含轮毂）损坏、底盘损坏；以及车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "description": [{"attr": "{\"carCoverage\":\"1500元\",\"vendorId\":76661,\"isBasic\":true,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "RED", "description": "车损保障赔付1500元以上损失，含玻璃、不含轮胎", "type": "CDWcn"}, {"attr": "200万", "description": "三者保障保额200万", "contains": true, "type": "TPLcn"}, {"attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}", "description": "车损5000元以下免收折旧费", "contains": true, "type": "DPN"}, {"attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}", "description": "停运费正常收取", "contains": false, "type": "OTG"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "uniqueCode": "1002", "name": "基础服务", "currentCurrencyCode": "CNY"}], "tenancyTerm": 2, "purchased": {"status": 3, "type": 1, "gapPrice": 0, "price": 200, "allTags": [{"title": "车轮损失自付", "colorCode": "<PERSON>", "code": "tag0"}], "currentDailyPrice": 100, "insuranceDetailDescription": [{"title": "车辆损失险", "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "1500元以上部分"}}, {"key": "用户或承租方需承担", "value": {"content": "1500元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "保险赔付的100%"}}, {"key": "用户或承租方需承担", "value": {"content": "保险赔付以外的100%"}}]}], "contains": true, "type": "CDWcn"}, {"title": "三者保障", "content": [], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "200万元"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "200万元"}}, {"key": "用户或承租方需承担", "value": {"content": "200万元以上的部分"}}]}], "contains": true, "type": "TPLcn"}, {"subDesc": ["第三方全责的车损不收折旧费"], "title": "折旧费", "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "styleType": 1, "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "5000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "5000元以上", "value": {"content": "维修费总额的20%"}}], "desc": ""}], "contains": true, "type": "DPN"}, {"subDesc": ["每日订单金额包含基本保险，不含手续费及附加服务费", "因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）"], "title": "停运费", "content": ["因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为："], "subContent": [{"table": [{"key": "", "value": {"great": false, "content": "停运费 = 车辆维修期间每日订单金额之和 × 100%"}}]}], "contains": false, "type": "OTG"}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：轮胎（含轮毂）损坏、底盘损坏；以及车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "description": [{"attr": "{\"carCoverage\":\"1500元\",\"vendorId\":76661,\"isBasic\":true,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "RED", "description": "车损保障赔付1500元以上损失，含玻璃、不含轮胎", "type": "CDWcn"}, {"attr": "200万", "description": "三者保障保额200万", "contains": true, "type": "TPLcn"}, {"attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}", "description": "车损5000元以下免收折旧费", "contains": true, "type": "DPN"}, {"attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}", "description": "停运费正常收取", "contains": false, "type": "OTG"}], "uniqueCode": "1002", "name": "基础服务", "currentCurrencyCode": "CNY"}, "baseResponse": {"code": "", "requestId": "", "cost": 473, "isSuccess": true, "returnMsg": "success"}, "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1688111140218+0800)/", "extension": []}, "insuranceAndXProductList": [{"orderTitle": "保险加购", "status": 0, "title": "人身及财物险", "code": "2000896", "currentTotalPrice": 50, "localTotalPrice": 50, "accident": [{"title": "立即联系门店及交通警察", "desc": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店已获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"title": "拍照并留存记录信息", "desc": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"title": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"title": "请您配合交警处理完事故后，方可离开事故现场"}, {"title": "准备材料，进行保险理赔流程"}], "targetTag": [{"title": "仅支持线上购买"}, {"title": "87%用户选择购买"}], "name": "人身及财物险", "specificName": 0, "sourceFrom": 2, "productId": 2000896, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "quantity": 1, "group": 1, "requestId": "36510794018-2000896", "toDetailStatus": 0, "insAndXProductLabelInfos": [{"name": "平安财险承保", "color": "0", "sort": "1"}], "price": 50, "insBottomDesc": {"title": "以下情况无法为您提供保障服务：", "desc": ["发生事故时未及时通知租车公司或未申报保险", "无事故证明材料或无保险理赔材料", "无证驾驶、酒驾、超速等其他保险公司不予理赔或责任免除的场景"]}, "canUpgrade": false, "description": ["人身意外保额30.0万元/人，医疗费用保额3万元/人", "集中隔离津贴100元/天，最高1400元/人", "随车财务损失2000元/车", "专业道路救援服务"]}], "upgradeGuarantee": {"packageDetailList": [{"status": 3, "type": 1, "gapPrice": 0, "price": 200, "allTags": [{"title": "车轮损失自付", "colorCode": "<PERSON>", "code": "tag0"}, {"title": "车轮损失自付", "colorCode": "<PERSON>", "code": "tag0"}], "currentDailyPrice": 100, "insuranceDetailDescription": [{"title": "车辆损失险", "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "1500元以上部分"}}, {"key": "用户或承租方需承担", "value": {"content": "1500元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "保险赔付的100%"}}, {"key": "用户或承租方需承担", "value": {"content": "保险赔付以外的100%"}}]}], "contains": true, "type": "CDWcn"}, {"title": "三者保障", "content": [], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "200万元"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "200万元"}}, {"key": "用户或承租方需承担", "value": {"content": "200万元以上的部分"}}]}], "contains": true, "type": "TPLcn"}, {"subDesc": ["第三方全责的车损不收折旧费"], "title": "折旧费", "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "styleType": 1, "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "5000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "5000元以上", "value": {"content": "维修费总额的20%"}}], "desc": ""}], "contains": true, "type": "DPN"}, {"subDesc": ["每日订单金额包含基本保险，不含手续费及附加服务费", "因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）"], "title": "停运费", "content": ["因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为："], "subContent": [{"table": [{"key": "", "value": {"great": false, "content": "停运费 = 车辆维修期间每日订单金额之和 × 100%"}}]}], "contains": false, "type": "OTG"}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：轮胎（含轮毂）损坏、底盘损坏；以及车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "description": [{"attr": "{\"carCoverage\":\"1500元\",\"vendorId\":76661,\"isBasic\":true,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "RED", "description": "1500元以内自付 <tag>tag0", "type": "CDWcn"}, {"attr": "200万", "description": "200万", "contains": true, "type": "TPLcn"}, {"attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}", "description": "车损5000元以下免收", "contains": true, "type": "DPN"}, {"contains": false, "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "uniqueCode": "1002", "name": "基础服务", "currentCurrencyCode": "CNY"}, {"status": 0, "type": 0, "gapPrice": 120, "price": 240, "allTags": [], "currentDailyPrice": 120, "insuranceDetailDescription": [{"title": "车辆损失险", "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失。", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": true, "content": "全部损失"}}, {"key": "用户或承租方需承担", "value": {"content": "0元"}}]}], "contains": true, "type": "CDWcn"}, {"subDesc": ["第三方全责的车损不收折旧费"], "title": "折旧费", "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "styleType": 1, "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "30000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "30000元以上", "value": {"content": "维修费总额的20%"}}], "desc": ""}], "contains": true, "type": "DPN"}, {"title": "免停运费", "content": ["免收因车损产生的车辆停运费"], "type": "OTG", "contains": true, "great": true}, {"title": "无须垫付", "content": ["无需垫付定金或相关费用，车辆维修出险后，根据费用单等，补缴给租车公司您所需赔付的部分。"], "contains": true, "great": true}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "description": [{"type": "summary", "show": true, "description": "车损保障赔付全部损失，含玻璃、轮胎，车损3万元以下免收折旧费，免停运费，含无需垫付"}, {"attr": "{\"carCoverage\":\"0元\",\"vendorId\":76661,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}", "show": false, "description": "全额赔付", "type": "CDWcn", "descriptionColorCode": "GREEN"}, {"attr": "200万", "show": false, "description": "200万", "contains": true, "type": "TPLcn"}, {"descriptionColorCode": "GREEN", "attr": "{\"cover\":true,\"excess\":\"3万\",\"desc\":\"车损3万元以下免收折旧费\",\"summary\":\"车损3万元以下免收折旧费\",\"containsDescription\":\"车损3万元以下免收\",\"descriptionColorCode\":\"GREEN\"}", "show": false, "description": "车损3万元以下免收", "contains": true, "type": "DPN"}, {"attr": "{\"cover\":true,\"desc\":\"您无需承担车辆停运费\",\"summary\":\"免停运费\"}", "show": false, "type": "OTG", "contains": true}, {"attr": "含无需垫付", "show": false, "type": "Disbursements", "contains": true}], "uniqueCode": "2011", "name": "尊享服务", "currentCurrencyCode": "CNY"}], "rentalGuaranteeTitle": ["车损保障", "三者保障", "免折旧费", "免停运费", "无需垫付"], "vendorServiceDesc": "车行服务是指车行为客人安全用车提供的服务，包含车辆整备（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。", "purchasingNotice": {"title": "购买须知", "content": ["1.车行服务由租车公司提供，仅支持取车前购买", "2.若发生续租，已购买的车行服务的订单续租时必须购买相同服务"]}, "vendorServiceDetail": [{"title": "车辆事故处理流程", "type": 1, "description": "1", "contents": [{"title": "1", "type": 1, "content": ["1"]}]}, {"title": "车行服务理赔说明", "type": 2, "description": "发生车损（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得理赔", "contents": [{"title": "不予理赔说明", "type": 2, "content": ["1、发生事故时未及时通知租车公司或未申报保险", "2、无事故证明材料或无保险理赔材料", "3、无证驾驶、酒驾、超速等其他保险不予理赔或责任免除场景"]}, {"title": "保险理赔说明", "type": 4, "content": ["1. 保险内容"]}]}], "vendorServiceSubDesc": "1.以上服务与保障由车行基于车辆自身车险所提供，故车损与三者保障仅覆盖车行车险理赔范围内的损失，理赔范围见服务详情。\n2.发生事故后请严格按照理赔要求操作，若未按要求处理导致车险无法理赔，上述保障将会失效，您需承担全额损失。"}}, "queryPriceInfo": {"baseResponse": {"code": "unknown", "requestId": "", "cost": 1004, "isSuccess": true, "returnMsg": "success"}, "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1688111140241+0800)/", "extension": []}, "feeDetailInfo": {"depositInfo": {"items": []}, "discountList": [], "equipmentInfos": [], "couponInfos": [], "chargesInfos": [{"code": "CAR_RENTAL_FEE", "currentTotalPrice": 596, "title": "车辆租金", "currencyCode": "CNY", "items": [{"unit": "", "priceDailys": [{"date": "7月9日 周日", "priceStr": "¥298", "showType": 0}, {"date": "7月10日 周一", "priceStr": "¥298", "showType": 0}], "code": "1001", "title": "租车费", "count": 2, "currencyCode": "CNY", "currentDailyPrice": 298, "currentTotalPrice": 596, "subTitle": ""}], "currentDailyPrice": 298}, {"code": "SERVICE_CHARGE", "currentTotalPrice": 20, "title": "车行手续费", "currencyCode": "CNY", "items": [{"unit": "", "code": "1003", "title": "车行手续费", "count": 1, "currencyCode": "CNY", "currentDailyPrice": 20, "description": "（含车辆清洁、单据打印等）", "currentTotalPrice": 20}]}, {"code": "CAR_GUARANTEE_FEE", "currentTotalPrice": 200, "title": "车行保障服务费", "currencyCode": "CNY", "items": [{"unit": "", "code": "1002", "title": "基础服务费", "size": "¥100 x2天", "count": 2, "currencyCode": "CNY", "description": "车损保障赔付1500元以上损失，含玻璃、不含轮胎，三者保障保额200万，车损5000元以下免收折旧费，停运费正常收取", "currentDailyPrice": 100, "currentTotalPrice": 200}]}], "rentalTerm": 2, "userPoints": {"pointsNotice": [{"title": {"stringObjs": [{"content": "本单可获积分", "style": "1"}]}, "desc": [{"stringObjs": [{"content": "订单成交金额 x 15% x 1.0 "}]}]}, {"title": {"stringObjs": [{"content": "规则说明", "style": "1"}]}, "desc": [{"stringObjs": [{"content": "1.使用返现、优惠券等活动订单，优惠金额不参与携程积分计算；"}]}, {"stringObjs": [{"content": "2.使用积分抵现功能支付部分不参与携程积分计算；"}]}, {"stringObjs": [{"content": "3.积分将在行程完成且订单状态更新为完成状态后发放"}]}]}], "pointsTip": "已享1.0倍加速"}, "chargesSummary": {"code": "Summary", "title": "订单总额", "currencyCode": "CNY", "notices": [], "currentTotalPrice": 816, "type": 103, "payMode": 2, "items": [{"title": "在线支付", "currencyCode": "CNY", "code": "1"}, {"title": "现金余额¥816", "currencyCode": "CNY", "code": "6"}]}}}, "getLicense": {"status": 1, "defaultLicenseImgUrl": "http://pic.c-ctrip.com/car_isd/app/h5/lisences.png", "licenseDesc": "租车服务提供方", "ResponseStatus": {"Build": null, "Version": null, "Ack": "Success", "Errors": [], "Timestamp": "/Date(1688111139783+0800)/", "Extension": []}, "licenseImgUrl": "http://images4.c-ctrip.com/target/0411h120008e1uy0s2FD6.jpg?proc=watermark/t_6JCl5Lia5omn54Wn5L-h5oGv5YWs56S65LiT55So,s_70,l_center,r_315,d_40,c_696969,F732", "companyName": "三亚振亚汽车租赁有限公司", "defaultlLicenseDesc": "预订服务提供方", "defaultCompanyName": "上海华程西南国际旅行社有限公司"}, "queryAdditionPayment": {"baseResponse": {"code": "200", "requestId": "", "cost": 77, "isSuccess": true, "returnMsg": "success"}, "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1688111139849+0800)/"}, "additionalPaymentList": []}, "queryCertificateV3": {"baseResponse": {"code": "unknown", "requestId": "", "cost": 539, "isSuccess": true, "returnMsg": "success"}, "supportInfo": {"isShow": false, "showTitle": "身份证及驾照在线认证"}, "certificateV3List": [{"certificateV3": {"certificateType": "1"}, "isActive": false}, {"certificateV3": {"certificateType": "2"}, "isActive": false}], "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1688111140302+0800)/", "extension": []}, "policy": {"isChange": false}}}}