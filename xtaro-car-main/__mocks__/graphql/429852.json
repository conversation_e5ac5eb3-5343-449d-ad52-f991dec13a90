{"data": {"getReceivePromotion": {"baseResponse": {"isSuccess": true, "code": "0", "returnMsg": "操作成功", "requestId": "4b8ee0bd-4b11-4282-a2d3-362adcc983cb", "showMessage": "操作成功", "extMap": {}, "apiResCodes": [], "errorCode": "0", "message": "操作成功"}, "ResponseStatus": {"Timestamp": "/Date(1682482402687+0800)/", "Ack": "Success", "Errors": [], "Extension": []}, "title": "每日好券", "promotions": [{"shortDesc": "无门槛立减50", "status": 1, "limitText": "", "meetCouponStartDate": true, "deductionList": [], "valueType": 0, "isActive": true, "briefName": "立减¥50", "promotionSecretId": "c7292efccb8be2ca7803fd028b820334df0987b5f1e469f6fb19847f28cbe6b0be0f997295bb62407155da4d797ab162", "name": "无门槛立减50立减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "¥", "couponCode": "", "invaildReason": "", "discountText": "", "value": "50", "scenes": 1, "statusName": "领取", "promotionId": 765745525, "longDesc": "无门槛立减50"}, {"shortDesc": "阶梯折扣满减", "status": 1, "limitText": "最低", "meetCouponStartDate": true, "deductionList": ["满¥100享受9.0折", "满¥200享受7.0折"], "valueType": 0, "isActive": true, "briefName": "最低7.0折", "promotionSecretId": "cf1db2568f4731a6501e088c23a296f46a17840b41043c11564281d10c8ea1ddbe0f997295bb62407155da4d797ab162", "name": "阶梯折扣满减满减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "", "couponCode": "", "invaildReason": "", "discountText": "折", "value": "7.0", "scenes": 1, "statusName": "领取", "promotionId": 633909833, "longDesc": "阶梯折扣满减"}, {"shortDesc": "立减折扣9折", "status": 1, "limitText": "", "meetCouponStartDate": true, "deductionList": [], "valueType": 0, "isActive": true, "briefName": "立享9.0折", "promotionSecretId": "84f448d36180dc34c836084966f0264afeee84bb2840ae63a22429390ae83d56be0f997295bb62407155da4d797ab162", "name": "立减折扣9折立减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "", "couponCode": "", "invaildReason": "", "discountText": "折", "value": "9.0", "scenes": 1, "statusName": "领取", "promotionId": 288845477, "longDesc": "立减折扣9折"}, {"shortDesc": "阶梯满减", "status": 2, "limitText": "最高减", "meetCouponStartDate": true, "deductionList": ["满¥100减¥20", "满¥200减¥40"], "valueType": 0, "isActive": true, "briefName": "最高减¥40", "promotionSecretId": "", "name": "阶梯满减满减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "¥", "couponCode": "zwgjgxuzye", "invaildReason": "", "discountText": "", "value": "40", "scenes": 1, "statusName": "已领取", "promotionId": 667234322, "longDesc": "阶梯满减"}, {"shortDesc": "阶梯满减", "status": 2, "limitText": "最高减", "meetCouponStartDate": true, "deductionList": ["满¥100减¥20", "满¥200减¥40"], "valueType": 0, "isActive": true, "briefName": "最高减¥40", "promotionSecretId": "", "name": "阶梯满减满减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "¥", "couponCode": "zwgjgxuzye", "invaildReason": "", "discountText": "", "value": "40", "scenes": 1, "statusName": "已领取", "promotionId": 667234322, "longDesc": "阶梯满减"}, {"shortDesc": "阶梯满减", "status": 2, "limitText": "最高减", "meetCouponStartDate": true, "deductionList": ["满¥100减¥20", "满¥200减¥40"], "valueType": 0, "isActive": true, "briefName": "最高减¥40", "promotionSecretId": "", "name": "阶梯满减满减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "¥", "couponCode": "zwgjgxuzye", "invaildReason": "", "discountText": "", "value": "40", "scenes": 1, "statusName": "已领取", "promotionId": 667234322, "longDesc": "阶梯满减"}, {"shortDesc": "阶梯满减", "status": 2, "limitText": "最高减", "meetCouponStartDate": true, "deductionList": ["满¥100减¥20", "满¥200减¥40"], "valueType": 0, "isActive": true, "briefName": "最高减¥40", "promotionSecretId": "", "name": "阶梯满减满减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "¥", "couponCode": "zwgjgxuzye", "invaildReason": "", "discountText": "", "value": "40", "scenes": 1, "statusName": "已领取", "promotionId": 667234322, "longDesc": "阶梯满减"}]}, "queryWarningList": {"baseResponse": {"isSuccess": true, "code": "200", "extMap": {}}, "ResponseStatus": {"Timestamp": "/Date(1682482402621+0800)/", "Ack": "Success", "Errors": [], "Extension": []}, "warningDtos": []}, "queryHomePageCard": {"responseStatus": {"timestamp": "/Date(1682482403572+0800)/", "ack": "Success", "errors": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "", "cost": 957}, "orderList": [{"orderId": 36510014534, "channel": "ISD", "ctripOrderStatus": "CAR_IN_SERVICE", "useDay": "2天后", "ctripOrderStatusDesc": "用车中", "lastEnablePayTime": 1682406153000, "pickUpDate": 1682470800000, "returnDate": 1682649000000, "vehicleName": "雪佛兰科沃兹雪佛兰科沃兹雪佛兰科沃兹", "vehiclePic": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "pickupInfo": {"localDateTime": "2023-04-26 09:00:00", "storeTel": "15566065515;15566065513;15338990710", "title1": "取车方式", "pickReturnWay": "店员免费送车上门", "title2": "取车地址", "address": "凤凰国际机场T1航站楼", "storeID": 106883, "serviceType": "2", "userLongitude": 109.41235, "userAddress": "凤凰国际机场T1航站楼", "userLatitude": 18.30767}, "returnInfo": {"localDateTime": "2023-04-28 10:30:00", "storeTel": "15566065515;15566065513;15338990710", "title1": "还车方式", "pickReturnWay": "店员免费上门取车", "title2": "还车地址", "address": "凤凰国际机场T1航站楼", "storeID": 106883, "serviceType": "2", "userLongitude": 109.41235, "userAddress": "凤凰国际机场T1航站楼", "userLatitude": 18.30767}, "allOperations": [{"operationId": 13, "buttonName": "订单详情", "enable": true, "icon": "&#xf08f9;"}, {"operationId": 14, "buttonName": "联系门店", "enable": true, "icon": "&#xf08fc;"}, {"operationId": 11, "buttonName": "续租", "enable": true, "label": "车辆想要多用一会儿？请点击续租", "contents": [], "icon": "&#xf08fb;"}], "isPick": false, "locationType": 1, "useDayDesc": "三亚还车", "klbVersion": 1, "earlyReturn": true}]}, "queryMembershipRights4Index": {"ResponseStatus": {"Timestamp": "/Date(1682482402682+0800)/", "Ack": "Success", "Errors": [], "Extension": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "5ccaf553-a694-4deb-8b06-67528b2d9b97", "extMap": {}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "rights": {"title": {"stringObjs": [{"content": "普通会员，您可享受以下权益"}]}, "items": [{"code": "PointsAcceleration", "title": "1.0倍积分加速", "subTitle": "普通会员", "icon": "https://dimg04.c-ctrip.com/images/0AS1e120009hi6ghz0C65.png", "status": 1, "url": ""}]}, "curLevelName": "普通会员", "curLevelCode": "0"}, "getStreamRecCityId": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "764f3038-4262-4acc-bc59-94659c61e53e", "extMap": {}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1682482402663+0800)/", "Ack": "Success", "Errors": []}, "recCityId": 2, "cityIdType": "IntentCityId"}, "getIndexSuperVip": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "8853bdc3-7d31-472d-8438-254658ab255e", "extMap": {}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1682482402683+0800)/", "Ack": "Success", "Errors": []}}, "queryYunnanWelfare": {"BaseResponse": {"IsSuccess": true, "Code": "0", "ReturnMsg": "success", "RequestId": "39982da6-edd3-4cd2-9a9c-971c3bbc0baf", "Cost": 9}, "ResponseStatus": {"Timestamp": "/Date(1682482402623+0800)/", "Ack": "Success", "Errors": [], "Extension": []}, "show": false, "amount": "￥100", "desc": "云南自驾租车，赠#加油券"}, "homeController": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success"}, "ResponseStatus": {"Timestamp": "/Date(1682482402631+0800)/", "Ack": "Success", "Errors": []}, "rentcenterId": 2, "filter": [{"type": 1, "code": "PickReturn_StationPR", "text": "只看站内取还"}], "pickupOnDoorCode": "PickReturn_PickupOnDoor", "pHub": 1, "rHub": 1}}}