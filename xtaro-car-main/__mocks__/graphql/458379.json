{"data": {"queryAdditionPayment": {"responseStatus": {"timestamp": "/Date(1694057850164+0800)/", "ack": "Success", "errors": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "", "cost": 256}, "additionalPaymentList": []}, "queryPriceInfo": {"feeDetailInfo": {"chargesInfos": [{"title": "车辆租金+基础保障套餐"}, {"title": "基础租车费用", "description": "", "code": "Car", "type": 8, "size": "约¥713×1天", "currencyCode": "CNY", "currentTotalPrice": 713, "payMode": 1, "items": [{"title": "基础租车费用", "description": "满油取还、基本租车费用、税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税、不限里程", "descList": ["满油取还、基本租车费用、税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税、不限里程"], "code": "Car", "type": 8}, {"title": "车辆碰撞保障", "code": "Insurance", "type": 1}, {"title": "车辆盗抢险", "code": "Insurance", "type": 1}]}], "equipmentInfos": [], "activityInfo": {}, "couponInfos": [], "notIncludeCharges": {}, "chargesSummary": {"title": "全额", "code": "Summary", "type": 103, "currencyCode": "CNY", "currentTotalPrice": 713, "items": [{"title": "到店支付 约", "code": "1", "currencyCode": "CNY", "currentTotalPrice": 713}, {"title": "当地货币", "code": "6", "currencyCode": "USD", "currentTotalPrice": 98.55}], "notices": ["* 上述金额是按当前汇率换算的，实际到店支付费用以门店当天汇率为准。"]}, "rentalTerm": 1}, "responseStatus": {"timestamp": "/Date(1694057850468+0800)/", "ack": "Success", "errors": []}, "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 577}, "priceDesc": {"aboutDeposit": {"desc": "取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为US$200.00（约¥1,448.00）,请保证可用额度足以支付押金。押金预计会在还车后28-45天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。"}, "possibleChangeList": [{"title": "里程政策", "description": "不限里程"}, {"title": "燃油政策", "description": "满油取还"}, {"title": "年龄要求", "description": "驾驶员年龄要求：20-80周岁"}, {"title": "额外驾驶员", "description": "不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"}, {"title": "营业时间外取还车", "description": "如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"}, {"title": "当地费用", "description": "订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"}]}}, "queryWarningList": {"baseResponse": {"isSuccess": true, "code": "200", "extMap": {}}, "ResponseStatus": {"Timestamp": "/Date(1694057849899+0800)/", "Ack": "Success", "Errors": []}, "warningDtos": []}, "getEasyLifeTagInfo": {"baseResponse": {"isSuccess": true}, "ResponseStatus": {"Timestamp": "/Date(1694057849907+0800)/", "Ack": "Success", "Errors": []}, "easyLifeTag": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "优质车况", "type": 1, "description": "3年内车龄", "sortNum": 3, "subTitle": "", "showLayer": 0}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满", "sortNum": 10, "subTitle": "", "showLayer": 0}, {"title": "车辆守护升级", "type": 0, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 99, "subTitle": "*覆盖损失范围以预订页面内披露为准"}]}, "getcustomerservice": {"baseResponse": {"isSuccess": true, "code": "200", "message": "查询成功", "requestId": "", "type": 0, "showMessage": ""}, "ResponseStatus": {"Timestamp": "2023-09-07 11:37:30", "Ack": "Success", "Errors": [], "Extension": []}, "url": "ctrip://wireless/chat_customerServiceChat?isPreSale=0&sceneCode=0&bizType=1339&pageId=222053&ext=eyJhaVBhcmFtIjp7Im9yZGVyaWQiOiIzNjUxMTY1NzYxMiJ9LCJvcmRlckluZm8iOnsiY3R5cGUiOiJPUkQiLCJjaWQiOiIzNjUxMTY1NzYxMiIsImRlc2MiOiLmtbflpJboh6rpqb4iLCJ0aXRsZSI6IuWig+Wkluenn+i9pueuoeWutiIsImFtb3VudCI6IjAuMDAiLCJidSI6InJlbnRhbCJ9fQ=="}, "querySimilarVehicle": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}], "responseStatus": {"timestamp": "/Date(1694057850068+0800)/", "ack": "Success", "errors": []}, "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 155}}}}