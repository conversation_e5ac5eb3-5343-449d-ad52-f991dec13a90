{"data": {"queryAdditionPayment": {"responseStatus": {"timestamp": "/Date(1718764142233+0800)/", "ack": "Success", "errors": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "ba1049ed-a997-4c55-915e-88d5dbb8a296", "cost": 89}, "additionalPaymentList": []}, "queryPriceInfo": {"feeDetailInfo": {"chargesInfos": [{"title": "车辆租赁费", "code": "CAR_RENTAL_FEE", "currencyCode": "CNY", "currentTotalPrice": 138, "items": [{"title": "无忧租+车损全免保障", "code": "20000001", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 138, "currentTotalPrice": 138, "priceDailys": [{"date": "6月25日 周二", "priceStr": "¥ 138", "showType": 0}]}, {"title": "国内立减折扣9折立减券", "currencyCode": "CNY", "currentTotalPrice": 42.0}]}, {"title": "附加产品", "code": "CAR_ADDITIONAL_FEE", "currencyCode": "CNY", "currentTotalPrice": 50, "items": [{"title": "儿童座椅", "subTitle": "", "code": "2003", "size": "¥ 50 x1", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 50, "currentTotalPrice": 50}]}], "equipmentInfos": [{"title": "儿童座椅", "subTitle": "", "code": "2003", "size": "¥ 50 x1", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 50, "currentTotalPrice": 50}], "discountList": [], "couponInfos": [], "depositInfo": {"items": []}, "chargesSummary": {"title": "订单总额", "code": "Summary", "type": 103, "currencyCode": "CNY", "currentTotalPrice": 188, "payMode": 2, "items": [{"title": "在线支付", "code": "1", "currencyCode": "CNY", "currentTotalPrice": 188}], "notices": []}, "rentalTerm": 1, "userPoints": {"title": "还车后可获", "subTitle": "积分", "currencyPrice": 53, "pointsTip": "已享1.8倍加速", "pointsNotice": [{"title": {"stringObjs": [{"content": "本单可获积分", "style": "1"}]}, "desc": [{"stringObjs": [{"content": "订单成交金额 x 15% x 1.8 "}]}]}, {"title": {"stringObjs": [{"content": "规则说明", "style": "1"}]}, "desc": [{"stringObjs": [{"content": "1.使用返现、优惠券等活动订单，优惠金额不参与携程积分计算；"}]}, {"stringObjs": [{"content": "2.使用积分抵现功能支付部分不参与携程积分计算；"}]}, {"stringObjs": [{"content": "3.积分将在行程完成且订单状态更新为完成状态后发放"}]}]}]}}, "responseStatus": {"timestamp": "/Date(1718764142491+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "ba1049ed-a997-4c55-915e-88d5dbb8a296", "cost": 435}}, "queryWarningList": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "3c118383-0666-473e-a356-350492e37f4b", "cost": 5, "extMap": {}}, "ResponseStatus": {"Timestamp": "/Date(1718764142149+0800)/", "Ack": "Success", "Errors": []}, "warningDtos": []}, "getEasyLifeTagInfo": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "a0c2f9c2-dfab-4d6f-bca7-c26b22a4d40b", "cost": 8}, "ResponseStatus": {"Timestamp": "/Date(1718764142152+0800)/", "Ack": "Success", "Errors": []}, "easyLifeTag": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "安心保障", "titleExtra": "(需加购尊享服务)", "type": 1, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 2, "subTitle": "*覆盖损失范围以预订页面内披露为准", "showLayer": 0}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满", "sortNum": 10, "subTitle": "", "showLayer": 0}, {"title": "免加油/充电服务费", "type": 0, "description": "还车时，若油量/电量少于取车油量/电量，无需支付加油/充电服务费", "sortNum": 99, "subTitle": "*油量/电量差价仍需支付"}], "titleAbstract": [{"title": "免费取消", "sortNum": 1}, {"title": "满油取车", "sortNum": 2}, {"title": "免加油服务费", "sortNum": 3}]}, "getLicense": {"ResponseStatus": {"Timestamp": "/Date(1718764142160+0800)/", "Ack": "Success", "Errors": []}, "fullName": "三亚世纪联合汽车租赁有限公司", "licenseUrl": "https://dimg04.c-ctrip.com/images/0307112000bncswv01B43.jpg?proc=watermark/t_6JCl5Lia5omn54Wn5L-h5oGv5YWs56S65LiT55So,s_70,l_center,r_315,d_40,c_696969,F732", "BaseResponse": {"IsSuccess": true, "Code": "200", "ReturnMsg": "success", "RequestId": "4f048dbe-0247-440e-be7c-dd9834a74d03", "Cost": 16}, "companyName": "三亚世纪联合汽车租赁有限公司", "licenseImgUrl": "https://dimg04.c-ctrip.com/images/0307112000bncswv01B43.jpg?proc=watermark/t_6JCl5Lia5omn54Wn5L-h5oGv5YWs56S65LiT55So,s_70,l_center,r_315,d_40,c_696969,F732", "licenseDesc": "预订服务提供方"}, "queryCertificateV3": {"responseStatus": {"timestamp": "/Date(1718764142214+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "1be05408-ceb9-4424-8bfb-476f7f55e8e9", "cost": 72}, "certificateV3List": [{"isActive": false, "certificateV3": {"certificateType": "1"}}, {"isActive": false, "certificateV3": {"certificateType": "2"}}], "supportInfo": {"isShow": false, "showTitle": "身份证及驾照在线认证"}, "policy": {"isChange": false}}, "queryOrderInsAndXProduct": {"responseStatus": {"timestamp": "/Date(1718764142489+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "ba1049ed-a997-4c55-915e-88d5dbb8a296", "cost": 359}, "insuranceAndXProductList": [{"name": "儿童座椅", "code": "2003", "title": "儿童座椅", "specificName": 0, "price": 50, "localCurrencyCode": "CNY", "localTotalPrice": 50, "currentTotalPrice": 50, "currentCurrencyCode": "CNY", "quantity": 1, "group": 2, "status": 3, "canUpgrade": false, "toDetailStatus": 0, "accident": [{"title": "立即联系门店及交通警察", "desc": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店已获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"title": "拍照并留存记录信息", "desc": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"title": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"title": "请您配合交警处理完事故后，方可离开事故现场"}, {"title": "准备材料，进行保险理赔流程"}]}], "purchased": {"name": "车行保障服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 138, "gapPrice": 0, "description": [{"description": "车损保障赔付全部损失，含玻璃、车轮", "type": "CDWcn", "attr": "{\"carCoverage\":\"0元\",\"vendorId\":53893,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}"}, {"contains": true, "description": "三者保障保额200万", "type": "TPLcn", "attr": "200万"}, {"contains": true, "description": "您无需承担车辆停运费", "type": "OTG", "attr": "{\"cover\":true,\"desc\":\"您无需承担车辆停运费\",\"summary\":\"免停运费\"}"}, {"contains": true, "description": "您无需承担车辆折旧费", "type": "DPN", "attr": "{\"cover\":true,\"desc\":\"您无需承担车辆折旧费\",\"summary\":\"免折旧费\"}"}, {"contains": true, "description": "车上人员（乘客/司机）保障5万元/座", "type": "PAIcn", "attr": "车上人员（乘客/司机）保障5万元/座"}, {"contains": true, "description": "免费道路救援", "type": "ABCcn", "attr": "免费道路救援"}], "allTags": [], "insuranceDetailDescription": [{"title": "三者保障", "contains": true, "content": ["保障车辆发生意外事故、导致第三者承受的损失。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "200万元"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "200万元"}}, {"key": "客户承担", "value": {"content": "200万元以上的部分"}}]}], "type": "TPLcn"}, {"title": "车上人员（乘客/司机）保障", "contains": true, "content": ["保障车辆发生意外事故，导致乘客/司机人身伤亡。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "50000元/座"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "5万元/座"}}, {"key": "客户承担", "value": {"content": "5万元/座以上的部分"}}]}], "type": "PAIcn"}, {"title": "车辆损失保障", "contains": true, "content": ["发生车辆损失或车辆报废时，用户或承租方可通过赔付减损。若同一订单中发生多次车损，每次车损单独计算承担额"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}], "type": "CDWcn"}, {"title": "免折旧费", "contains": true, "great": true, "content": ["免收因车损产生的车辆折旧费"], "type": "DPN"}, {"title": "免停运费", "contains": true, "great": true, "content": ["免收因车损产生的车辆停运费"], "type": "OTG"}, {"title": "免费道路救援", "contains": true, "content": ["提供全国道路救援保障"], "type": "ABCcn"}, {"title": "不予理赔", "contains": false, "content": ["包括但不限于如下情况：肇事逃逸、酒/毒驾、无证驾驶等（此场景下一般客人存在明显法律过失，包括但不限于严重违法犯罪行为）"]}], "uniqueCode": "20000001", "status": 3, "type": 1, "price": 138}, "upgradeGuarantee": {"rentalGuaranteeTitle": ["车损保障", "三者保障", "免折旧费", "免停运费", "无需垫付"], "vendorServiceDesc": "车行服务是指车行为客人安全用车提供的服务，包含车行手续（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。", "vendorServiceSubDesc": "上述保障仅覆盖非违法违规场景范围内的损失，详见服务详情。若发生事故，请按照要求进行处理。", "packageDetailList": [], "vendorServiceDetail": [{"title": "车辆事故处理流程", "type": 1, "description": "发生车损（本车与第三方，或本车单独发生事故）时，请按下方流程处理，否则将无法享受门店服务保障权益。", "contents": [{"title": "报警并联系门店", "type": 1, "content": ["拨打122报警（上海地区拨打110），并立即联系门店获取后续操作指引（如保险报案等）。车辆需由门店知晓并同意后再修理。擅自修理造成的额外损失将由用户或承租方承担。"]}, {"title": "拍照并留存记录信息", "type": 1, "content": ["包括但不限于涉事各方车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况。"]}, {"title": "明确事故责任，配合交警完成事故处理", "type": 1, "content": ["等待交警明确涉事各方事故责任，交警处理完成后方可离开现场。"]}, {"title": "提交材料进行理赔（如需）", "type": 1, "content": ["如门店判断需进行保险报案，用户或承租方须配合准备材料、联系保险公司进行保险理赔报案。如因用户或承租方未配合保险报案导致的额外损失将由用户或承租方承担。"]}]}, {"title": "保险理赔（如需）说明", "type": 2, "description": "发生车损后，如需保险报案获取理赔，请阅读并知晓如下说明", "contents": [{"title": "保险理赔说明", "type": 4, "content": ["1. 如果车辆不慎发生事故，请注意人身安全，并立即致电门店，按门店指引报案、收集理赔材料。齐全的理赔材料可以节省理赔等待时间。\n1. 如用户或承租方已还车并提交了齐全的理赔材料，则不需要垫付本车维修等保险公司应付费用，仅需：\na)支付服务保障详情中列明的的由用户或承租方承担的损失，以及服务保障范围外的所有责任和损失；\nb)垫付第三方损失，该费用将在保险公司完成理赔后返还。", "2. 如用户或承租方还车时不能提供齐全的理赔材料，一般有以下2种方式：\na.)无需先行支付。在保险公司完成定损或车辆维修完成后，根据实际费用计算用户或承租方需承担的金额支付给门店；\nb.)需用户或承租方先行交付部分金额（金额与门店现场沟通）。在保险公司完成定损或车辆实际维修完成后，根据实际费用金额多退少补。 "]}, {"title": "不予理赔说明", "type": 2, "content": ["1、发生事故时未及时通知租车公司或未申报保险", "2、无事故证明材料或无保险理赔材料", "3、无证驾驶、酒驾、超速等其他保险不予理赔或责任免除场景"]}]}]}, "upgradeSummary": {"type": "zero"}, "purchasedSub": [{"name": "车行保障服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 138, "gapPrice": 0, "description": [{"description": "车损保障赔付全部损失，含玻璃、车轮", "type": "CDWcn", "attr": "{\"carCoverage\":\"0元\",\"vendorId\":53893,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}"}, {"contains": true, "description": "三者保障保额200万", "type": "TPLcn", "attr": "200万"}, {"contains": true, "description": "您无需承担车辆停运费", "type": "OTG", "attr": "{\"cover\":true,\"desc\":\"您无需承担车辆停运费\",\"summary\":\"免停运费\"}"}, {"contains": true, "description": "您无需承担车辆折旧费", "type": "DPN", "attr": "{\"cover\":true,\"desc\":\"您无需承担车辆折旧费\",\"summary\":\"免折旧费\"}"}, {"contains": true, "description": "车上人员（乘客/司机）保障5万元/座", "type": "PAIcn", "attr": "车上人员（乘客/司机）保障5万元/座"}, {"contains": true, "description": "免费道路救援", "type": "ABCcn", "attr": "免费道路救援"}], "allTags": [], "insuranceDetailDescription": [{"title": "三者保障", "contains": true, "content": ["保障车辆发生意外事故、导致第三者承受的损失。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "200万元"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "200万元"}}, {"key": "客户承担", "value": {"content": "200万元以上的部分"}}]}], "type": "TPLcn"}, {"title": "车上人员（乘客/司机）保障", "contains": true, "content": ["保障车辆发生意外事故，导致乘客/司机人身伤亡。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "50000元/座"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "5万元/座"}}, {"key": "客户承担", "value": {"content": "5万元/座以上的部分"}}]}], "type": "PAIcn"}, {"title": "车辆损失保障", "contains": true, "content": ["发生车辆损失或车辆报废时，用户或承租方可通过赔付减损。若同一订单中发生多次车损，每次车损单独计算承担额"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}], "type": "CDWcn"}, {"title": "免折旧费", "contains": true, "great": true, "content": ["免收因车损产生的车辆折旧费"], "type": "DPN"}, {"title": "免停运费", "contains": true, "great": true, "content": ["免收因车损产生的车辆停运费"], "type": "OTG"}, {"title": "免费道路救援", "contains": true, "content": ["提供全国道路救援保障"], "type": "ABCcn"}, {"title": "不予理赔", "contains": false, "content": ["包括但不限于如下情况：肇事逃逸、酒/毒驾、无证驾驶等（此场景下一般客人存在明显法律过失，包括但不限于严重违法犯罪行为）"]}], "uniqueCode": "20000001", "status": 3, "type": 1, "price": 138}], "tenancyTerm": 1}}}