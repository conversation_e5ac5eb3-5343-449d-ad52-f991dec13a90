{"data": {"queryHomePageCard": {"baseResponse": {"code": "200", "requestId": "", "cost": 528, "isSuccess": true, "returnMsg": "success"}, "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1682068821972+0800)/"}, "orderList": [{"isPick": true, "useDay": "173天后", "channel": "ISD", "pickupInfo": {"localDateTime": "2023-10-11 10:00:00", "storeTel": "18117159935;15800651881;15800651881", "title1": "取车方式", "title2": "取车地址", "address": "上海市闵行区高泊飞虹桥天山西路停车场-出入口", "storeID": 153209, "serviceType": "0", "userLongitude": 121.3521744411732, "pickReturnWay": "自行前往门店取车", "userLatitude": 31.22188427709729, "userAddress": "SkyBridge HQ天会"}, "lastEnablePayTime": 1681961459000, "returnDate": 1697162400000, "vehiclePic": "https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0pcn152vdz1tkq982C2.jpg", "ctripOrderStatusDesc": "已确认", "klbVersion": 1, "earlyReturn": false, "ctripOrderStatus": "CAR_CONFIRMED", "orderId": 23761729688, "allOperations": [{"operationId": 13, "enable": true, "icon": "&#xf08f9;", "buttonName": "订单详情"}, {"operationId": 14, "enable": true, "icon": "&#xf08fc;", "buttonName": "联系门店"}, {"operationId": 15, "enable": true, "icon": "&#xf08fa;", "buttonName": "用车必备材料"}], "returnInfo": {"localDateTime": "2023-10-13 10:00:00", "storeTel": "18117159935;15800651881;15800651881", "title1": "还车方式", "title2": "还车地址", "address": "上海市闵行区高泊飞虹桥天山西路停车场-出入口", "storeID": 153209, "serviceType": "0", "userLongitude": 121.3521744411732, "pickReturnWay": "自行前往门店还车", "userLatitude": 31.22188427709729, "userAddress": "SkyBridge HQ天会"}, "pickUpDate": 1696989600000, "locationType": 1, "vehicleName": "福特福睿斯", "useDayDesc": "上海取车"}]}, "getCityTimeZone": {"isSuccessful": true, "cityTimeZone": -8, "ResponseStatus": {"Timestamp": "/Date(1682068821443+0800)/", "Ack": "Success", "Errors": []}}, "queryWarningList": {"baseResponse": {"code": "200", "extMap": {}, "isSuccess": true}, "warningDtos": [], "ResponseStatus": {"Extension": [], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1682068821111+0800)/"}}, "queryMembershipRights4Index": {"baseResponse": {"extMap": {}, "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "OK", "requestId": "79fb58f5-3e01-457d-ae75-b45c5ada93c4", "isSuccess": true}, "rights": {"title": {"stringObjs": [{"content": "钻石贵宾，您可享受以下权益"}]}, "items": [{"code": "VipDiscount", "status": 1, "title": "租车费9折", "subTitle": "钻石贵宾专享", "icon": "https://dimg04.c-ctrip.com/images/0AS3h120009hi6gue7CFE.png", "url": ""}, {"code": "LuxuryCarsDiscount", "status": 1, "title": "豪华车8折", "subTitle": "钻石贵宾专享", "icon": "https://pages.trip.com/cars/components-image/ctrip-app/Member/homeLuxuryCarsDiscount.png"}, {"code": "PointsAcceleration", "status": 1, "title": "2.0倍积分加速", "subTitle": "钻石贵宾", "icon": "https://dimg04.c-ctrip.com/images/0AS1e120009hi6ghz0C65.png", "url": ""}]}, "curLevelName": "钻石贵宾", "curLevelCode": "30", "ResponseStatus": {"Timestamp": "/Date(1664173527841+0800)/", "Ack": "Success", "Errors": []}}, "getReceivePromotion": {"baseResponse": {"isSuccess": true, "code": "0", "returnMsg": "操作成功", "requestId": "e4ee4e7c-a85c-4868-b778-767f4f09b539", "showMessage": "操作成功", "extMap": {}, "apiResCodes": [], "errorCode": "0", "message": "操作成功"}, "title": "每日好券", "promotions": [{"promotionSecretId": "5cad143e8c92ad99225c8d7e7bcae0e57f29b6cf199b32982f23ececbd3977254e0f4dc74159614a890c7cc438342079", "currencyCode": "¥", "value": "30", "valueType": 0, "discountText": "", "limitText": "", "name": "租车优选30元满减券", "validPeriodDesc": "2023-07-19生效", "cornerText": "", "shortDesc": "【周三福利日】", "longDesc": "1.使用范围：适用于携程租车频道，以在线预付方式预订国内租车“携程优选”产品可用（部分供应商不可用，具体以列表页展示为准）；\\\\2.券有效期：仅限周三当天下单，预订用车时段不含法定节假日及其前一天的订单可用；\\\\3.优惠金额：租车费享受满100减30元优惠抵扣（优惠抵扣金额仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用 ）；\\\\4.使用限制：每个用户仅限领取1张，同一设备号、手机号、uid均视为同一用户；每张优惠券仅限使用1次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券；已完成支付的订单不能使用此券；续租按非优惠价收费；\\\\5.退赔说明：（1）使用券的订单无损取消、超时未支付、购买失败或因订单变更导致订单金额未满足优惠门槛的，若该券尚未失效将退回原账户，若该券已失效不予退回；（2）如订单支持变更，已用券的订单因变更导致订单金额增加的，仅可享受变更前的阶梯优惠金额；已用券的订单如取消且产生取消费用的，优惠券因涉及抵扣（或部分抵扣）将不予退回。", "status": 1, "statusName": "领取", "invaildReason": "", "deductionList": ["满¥100减¥30"], "isActive": true, "briefName": "满¥100减¥30", "scenes": 1, "promotionId": 475246906, "couponCode": "", "meetCouponStartDate": true}, {"promotionSecretId": "", "currencyCode": "¥", "value": "788", "valueType": 0, "discountText": "", "limitText": "最高减", "name": "租车新客首单满减券", "validPeriodDesc": "领取后30天内有效", "cornerText": "", "shortDesc": "国内租车·最高减788元", "longDesc": "1.优惠券仅限携程租车新用户领取，领取后以预付方式（在线支付）预订国内租车产品可用（部分供应商不可用，具体以列表页展示为准）；\\\\2.可享租车费满100-12/满200-24/满400-48/满800-96/满1000-120/满6000-788元（不含手续费、基础服务费、优享服务费、异地还车费等费用）；\\\\3.优惠券有效期30天，从发放日开始计算，过期未使用则自动失效；\\\\4.支持网页、APP、小程序用券，每张订单限用一张优惠券，每张优惠券限用一次，不可与其他优惠和活动同享。优惠券不可折现、不退差额、不可拆分使用，续租按非优惠价收费；\\\\5.退赔规则：（1）使用券的订单无损取消、超时未支付、购买失败或因订单变更导致订单金额未满足优惠门槛的，若该券尚未失效将退回原账户，若该券已失效不予退回；（2）如订单支持变更，已用券的订单因变更导致订单金额增加的，仅可享受变更前的阶梯优惠金额；已用券的订单如取消且产生取消费用的，优惠券因涉及抵扣（或部分抵扣）将不予退回。", "status": 1, "statusName": "领取", "invaildReason": "限新客可领", "deductionList": ["满¥100减¥12", "满¥200减¥24", "满¥400减¥48", "满¥800减¥96", "满¥6000减¥788"], "isActive": true, "briefName": "最高减¥788", "scenes": 0, "promotionId": 923972074, "couponCode": "", "meetCouponStartDate": true}]}, "queryYunnanWelfare": {"amount": "￥100", "show": false, "ResponseStatus": {"Extension": [], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1664173527718+0800)/"}, "desc": "云南自驾租车，赠#加油券", "BaseResponse": {"IsSuccess": true, "ReturnMsg": "success", "Cost": 13, "Code": "0", "RequestId": "b80ffaa7-a71c-4a4b-a78e-3400bc3c2d4d"}}, "getStreamRecCityId": {"baseResponse": {"extMap": {}, "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "OK", "requestId": "83dd08ed-727d-45c8-a2d6-ac7d20bb98da", "isSuccess": true}, "recCityId": 17, "ResponseStatus": {"Timestamp": "/Date(1664173527756+0800)/", "Ack": "Success", "Errors": []}, "cityIdType": "IntentCityId"}, "getIndexSuperVip": {"baseResponse": {"extMap": {}, "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "OK", "requestId": "388c0af3-5ef2-4c51-9ce9-4c7f01fc6445", "isSuccess": true}, "ResponseStatus": {"Timestamp": "/Date(1664173527765+0800)/", "Ack": "Success", "Errors": []}}, "homeController": {"filter": [{"type": 1, "text": "只看站内取还", "code": "PickReturn_StationPR"}], "rentcenterId": 2, "pickupOnDoorCode": "PickReturn_PickupOnDoor", "rHub": 1, "baseResponse": {"code": "200", "returnMsg": "success", "isSuccess": true}, "ResponseStatus": {"Timestamp": "/Date(1664173527743+0800)/", "Ack": "Success", "Errors": []}, "pHub": 1}, "queryAdditionPayment": {}, "queryOrderInsAndXProduct": {}, "querySimilarVehicle": {}}}