{"data": {"getReceivePromotion": {"baseResponse": {"isSuccess": true, "code": "0", "returnMsg": "操作成功", "requestId": "76ecbd52-cb00-4cfa-a1d6-13e3a948a833", "showMessage": "操作成功", "extMap": {}, "apiResCodes": [], "errorCode": "0", "message": "操作成功"}, "ResponseStatus": {"Timestamp": "/Date(1696760350442+0800)/", "Ack": "Success", "Errors": []}, "title": "每日好券", "promotions": [{"promotionSecretId": "6df18ac1bda65e35d65cddc889fda1088a05d9f77db5c5d29442fa3bd4085115448e180f475456656289b49f31d572870a2216b5f8d847588b79bb77d96c7bfe", "currencyCode": "¥", "value": "30", "valueType": 0, "discountText": "", "limitText": "", "name": "租车优选30元满减券", "validPeriodDesc": "2023-10-11生效", "cornerText": "", "shortDesc": "【周三福利日】", "longDesc": "1.使用范围：适用于携程租车频道，以在线预付方式预订国内租车“携程优选”产品可用（部分供应商不可用，具体以列表页展示为准）；\\\\2.券有效期：仅限周三当天下单，预订用车时段不含法定节假日及其前一天的订单可用；\\\\3.优惠金额：租车费享受满100减30元优惠抵扣（优惠抵扣金额仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用 ）；\\\\4.使用限制：每个用户仅限领取1张，同一设备号、手机号、uid均视为同一用户；每张优惠券仅限使用1次，仅限本用户下单使用，不拆分、不找零、不转赠、不可叠加其他优惠券；已完成支付的订单不能使用此券；续租按非优惠价收费；\\\\5.退赔说明：（1）使用券的订单无损取消、超时未支付、购买失败或因订单变更导致订单金额未满足优惠门槛的，若该券尚未失效将退回原账户，若该券已失效不予退回；（2）如订单支持变更，已用券的订单因变更导致订单金额增加的，仅可享受变更前的阶梯优惠金额；已用券的订单如取消且产生取消费用的，优惠券因涉及抵扣（或部分抵扣）将不予退回。", "status": 1, "statusName": "领取", "invaildReason": "", "deductionList": ["满¥100减¥30"], "isActive": true, "briefName": "满¥100减¥30", "scenes": 1, "promotionId": 792296524, "couponCode": "", "meetCouponStartDate": true}, {"promotionSecretId": "", "currencyCode": "¥", "value": "788", "valueType": 0, "discountText": "", "limitText": "最高减", "name": "租车新客首单满减券", "validPeriodDesc": "领取后30天内有效", "cornerText": "", "shortDesc": "国内租车·最高减788元", "longDesc": "1.优惠券仅限携程租车新用户领取，领取后以预付方式（在线支付）预订国内租车产品可用（部分供应商不可用，具体以列表页展示为准）；\\\\2.可享租车费满100-12/满200-24/满400-48/满800-96/满1000-120/满6000-788元（不含手续费、基础服务费、优享服务费、异地还车费等费用）；\\\\3.优惠券有效期30天，从发放日开始计算，过期未使用则自动失效；\\\\4.支持网页、APP、小程序用券，每张订单限用一张优惠券，每张优惠券限用一次，不可与其他优惠和活动同享。优惠券不可折现、不退差额、不可拆分使用，续租按非优惠价收费；\\\\5.退赔规则：（1）使用券的订单无损取消、超时未支付、购买失败或因订单变更导致订单金额未满足优惠门槛的，若该券尚未失效将退回原账户，若该券已失效不予退回；（2）如订单支持变更，已用券的订单因变更导致订单金额增加的，仅可享受变更前的阶梯优惠金额；已用券的订单如取消且产生取消费用的，优惠券因涉及抵扣（或部分抵扣）将不予退回。", "status": 7, "statusName": "", "invaildReason": "限新客可领", "deductionList": ["满¥100减¥12", "满¥200减¥24", "满¥400减¥48", "满¥800减¥96", "满¥6000减¥788"], "isActive": false, "briefName": "最高减¥788", "scenes": 0, "promotionId": 923972074, "couponCode": "", "meetCouponStartDate": true}], "marketCouponStatus": 1}, "queryWarningList": {"baseResponse": {"isSuccess": true, "code": "200", "extMap": {}}, "ResponseStatus": {"Timestamp": "/Date(1696760350423+0800)/", "Ack": "Success", "Errors": []}, "warningDtos": []}, "queryHomePageCard": {"responseStatus": {"timestamp": "/Date(1696760350890+0800)/", "ack": "Success", "errors": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "", "cost": 470}, "orderList": [{"orderId": 26652585446, "channel": "ISD", "ctripOrderStatus": "CAR_CONFIRMED", "useDay": "59天后", "ctripOrderStatusDesc": "已确认", "lastEnablePayTime": 1696762080000, "pickUpDate": 1701801000000, "returnDate": 1701887400000, "vehicleName": "大众宝来", "vehiclePic": "https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0qcn143fagmgoxd04D4.jpg", "pickupInfo": {"localDateTime": "2023-12-06 02:30:00", "storeTel": "15738815516", "title1": "取车方式", "pickReturnWay": "店员免费送车上门", "title2": "取车地址", "address": "中原站", "storeID": 49010, "serviceType": "2", "userLongitude": 113.621767, "userAddress": "中原站", "userLatitude": 34.763997}, "returnInfo": {"localDateTime": "2023-12-07 02:30:00", "storeTel": "15738815516", "title1": "还车方式", "pickReturnWay": "店员免费上门取车", "title2": "还车地址", "address": "中原站", "storeID": 49010, "serviceType": "2", "userLongitude": 113.621767, "userAddress": "中原站", "userLatitude": 34.763997}, "allOperations": [{"operationId": 13, "buttonName": "订单详情", "enable": true, "icon": "&#xf08f9;"}, {"operationId": 14, "buttonName": "联系门店", "enable": true, "icon": "&#xf08fc;"}, {"operationId": 15, "buttonName": "用车必备材料", "enable": true, "icon": "&#xf08fa;"}], "isPick": true, "locationType": 1, "useDayDesc": "郑州取车", "klbVersion": 1, "earlyReturn": false}]}, "queryMembershipRights4Index": {"ResponseStatus": {"Timestamp": "/Date(1696760350441+0800)/", "Ack": "Success", "Errors": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "04853fc2-ac8d-4fa2-bb31-8b238a7e5caa", "extMap": {}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "rights": {"title": {"stringObjs": [{"content": "黄金贵宾，您可享受以下权益"}]}, "items": [{"code": "VipDiscount", "title": "租车费95折", "subTitle": "黄金贵宾专享", "icon": "https://dimg04.c-ctrip.com/images/0AS3h120009hi6gue7CFE.png", "status": 1}, {"code": "PointsAcceleration", "title": "1.5倍积分加速", "subTitle": "黄金贵宾", "icon": "https://dimg04.c-ctrip.com/images/0AS1e120009hi6ghz0C65.png", "status": 1}, {"code": "PointsEqualsToCash", "title": "积分可抵￥10", "subTitle": "黄金贵宾", "icon": "https://dimg04.c-ctrip.com/images/0AS03120009hi6fn339A5.png", "status": 1}]}, "curLevelName": "黄金贵宾", "curLevelCode": "10"}, "getStreamRecCityId": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "fe15f9fc-a9d7-4850-b22e-a22e71818148", "extMap": {}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1696760350441+0800)/", "Ack": "Success", "Errors": []}, "recCityId": 350, "cityIdType": "IntentCityId"}, "getIndexSuperVip": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "325920c6-7dec-4694-9792-aa302e838edb", "extMap": {}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1696760350434+0800)/", "Ack": "Success", "Errors": []}}, "queryYunnanWelfare": {"BaseResponse": {"IsSuccess": true, "Code": "0", "ReturnMsg": "success", "RequestId": "7621e7d9-b754-490d-b0e7-39202084535b", "Cost": 4}, "ResponseStatus": {"Timestamp": "/Date(1696760350424+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "cat log link", "Value": "http://cat.ctripcorp.com/cat/r/m/100025005-0a39afa7-471322-253690"}]}, "show": false, "amount": "￥100", "desc": "云南自驾租车，赠#加油券"}, "homeController": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success"}, "ResponseStatus": {"Timestamp": "/Date(1696760350424+0800)/", "Ack": "Success", "Errors": []}, "filter": [{"type": 1, "code": "PickReturn_PickupOnDoor", "text": "只看送车上门"}], "pickupOnDoorCode": "PickReturn_PickupOnDoor"}}}