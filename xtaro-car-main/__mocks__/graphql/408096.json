{"data": {"queryAdditionPayment": {"responseStatus": {"timestamp": "/Date(1685673545041+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "", "cost": 93}, "additionalPaymentList": []}, "queryPriceInfo": {"feeDetailInfo": {"chargesInfos": [{"title": "车辆租金", "code": "CAR_RENTAL_FEE", "currencyCode": "CNY", "currentDailyPrice": 177, "currentTotalPrice": 177, "items": [{"title": "租车费", "subTitle": "", "code": "1001", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 217, "currentTotalPrice": 217, "priceDailys": [{"date": "6月22日 周四", "priceStr": "¥217", "showType": 0}]}, {"title": "国内阶梯满减满减券", "currencyCode": "CNY", "currentTotalPrice": 40}], "originDailyPrice": 217}, {"title": "车行手续费", "code": "SERVICE_CHARGE", "currencyCode": "CNY", "currentTotalPrice": 35, "items": [{"title": "车行手续费", "description": "（含车辆清洁、单据打印等）", "code": "1003", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 35, "currentTotalPrice": 35}]}, {"title": "车行保障服务费", "code": "CAR_GUARANTEE_FEE", "currencyCode": "CNY", "currentTotalPrice": 70, "items": [{"title": "基础服务费", "code": "1002", "size": "¥40 x1天", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 40, "currentTotalPrice": 40}, {"title": "优享服务费", "subTitle": "", "description": "车损保障赔付全部损失，含玻璃、轮胎，三者保障保额150万，车损5000元以下免收折旧费，停运费正常收取", "code": "2001", "size": "¥30 x1天", "count": 1, "unit": "", "currencyCode": "CNY", "currentDailyPrice": 30, "currentTotalPrice": 30}]}], "equipmentInfos": [], "discountList": [], "couponInfos": [{"title": "优惠券", "subTitle": "国内阶梯满减满减券", "code": "enyfwfiqnb", "type": 2, "currencyCode": "CNY", "currentTotalPrice": 40, "payMode": 2}], "depositInfo": {"items": []}, "chargesSummary": {"title": "订单总额", "code": "Summary", "type": 103, "currencyCode": "CNY", "currentTotalPrice": 282, "payMode": 2, "items": [{"title": "在线支付", "code": "1", "currencyCode": "CNY"}], "notices": []}, "rentalTerm": 1, "userPoints": {"title": "还车后可获", "subTitle": "积分", "currencyPrice": 42, "pointsTip": "已享1.0倍加速", "pointsNotice": [{"title": {"stringObjs": [{"content": "本单可获积分", "style": "1"}]}, "desc": [{"stringObjs": [{"content": "订单成交金额 x 15% x 1.0 "}]}]}, {"title": {"stringObjs": [{"content": "规则说明", "style": "1"}]}, "desc": [{"stringObjs": [{"content": "1.使用返现、优惠券等活动订单，优惠金额不参与携程积分计算；"}]}, {"stringObjs": [{"content": "2.使用积分抵现功能支付部分不参与携程积分计算；"}]}, {"stringObjs": [{"content": "3.积分将在行程完成且订单状态更新为完成状态后发放"}]}]}]}}, "responseStatus": {"timestamp": "/Date(1685673545794+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 987}}, "queryWarningList": {"baseResponse": {"isSuccess": true, "code": "200", "extMap": {}}, "ResponseStatus": {"Timestamp": "/Date(1685673544956+0800)/", "Ack": "Success", "Errors": []}, "warningDtos": []}, "getEasyLifeTagInfo": {"baseResponse": {"isSuccess": true}, "ResponseStatus": {"Timestamp": "/Date(1685673544957+0800)/", "Ack": "Success", "Errors": []}, "easyLifeTag": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "优质车况", "type": 1, "description": "3年内车龄", "sortNum": 3, "subTitle": "", "showLayer": 0}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满", "sortNum": 10, "subTitle": "", "showLayer": 0}, {"title": "车辆守护升级", "type": 0, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 99, "subTitle": "*覆盖损失范围以预订页面内披露为准"}]}, "getLicense": {"status": 1, "companyName": "上海林其商务咨询有限公司", "licenseImgUrl": "http://images4.c-ctrip.com/target/0413o120008ev9abu9049.png?proc=watermark/t_6JCl5Lia5omn54Wn5L-h5oGv5YWs56S65LiT55So,s_70,l_center,r_315,d_40,c_696969,F732", "licenseDesc": "租车服务提供方", "ResponseStatus": {"Timestamp": "/Date(1685673544956+0800)/", "Ack": "Success", "Errors": [], "Build": null, "Version": null, "Extension": []}, "defaultCompanyName": "上海华程西南国际旅行社有限公司", "defaultLicenseImgUrl": "http://pic.c-ctrip.com/car_isd/app/h5/lisences.png", "defaultlLicenseDesc": "预订服务提供方"}, "queryCertificateV3": {"responseStatus": {"timestamp": "/Date(1685673545366+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 423}, "certificateV3List": [{"isActive": false, "certificateV3": {"certificateType": "1"}}, {"isActive": false, "certificateV3": {"certificateType": "2"}}], "supportInfo": {"isShow": false, "showTitle": "身份证及驾照在线认证"}, "policy": {"isChange": false}}, "queryOrderInsAndXProduct": {"responseStatus": {"timestamp": "/Date(1685673545404+0800)/", "ack": "Success", "errors": [], "extension": []}, "baseResponse": {"isSuccess": true, "code": "", "returnMsg": "success", "requestId": "", "cost": 1416}, "insuranceAndXProductList": [], "purchased": {"name": "基础服务+优享服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 30, "gapPrice": 30, "description": [{"description": "车损保障赔付全部损失，含玻璃、轮胎", "type": "CDWcn", "attr": "{\"carCoverage\":\"0元\",\"vendorId\":77757,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}", "descriptionColorCode": "GREEN"}, {"contains": true, "description": "三者保障保额150万", "type": "TPLcn", "attr": "150万"}, {"contains": true, "description": "车损5000元以下免收折旧费", "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}"}, {"contains": false, "description": "停运费正常收取", "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}], "allTags": [], "insuranceDetailDescription": [{"title": "车辆损失险", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失。", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": true, "content": "全部损失"}}, {"key": "用户或承租方需承担", "value": {"content": "0元"}}]}], "type": "CDWcn"}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "2001", "status": 3, "type": 1, "price": 30, "selectedEncourage": "经济实用 超值之选"}, "upgradeGuarantee": {"rentalGuaranteeTitle": ["车损保障", "三者保障", "免折旧费", "免停运费", "无需垫付"], "vendorServiceDesc": "车行服务是指车行为客人安全用车提供的服务，包含车辆整备（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。", "vendorServiceSubDesc": "1.以上服务与保障由车行基于车辆自身车险所提供，故车损与三者保障仅覆盖车行车险理赔范围内的损失，理赔范围见服务详情。\n2.发生事故后请严格按照理赔要求操作，若未按要求处理导致车险无法理赔，上述保障将会失效，您需承担全额损失。", "packageDetailList": [], "purchasingNotice": {"title": "购买须知", "content": ["1.车行服务由租车公司提供，仅支持取车前购买", "2.若发生续租，已购买的车行服务的订单续租时必须购买相同服务"]}, "vendorServiceDetail": [{"title": "车辆事故处理流程", "type": 1, "description": "1", "contents": [{"title": "1", "type": 1, "content": ["1"]}]}, {"title": "车行服务理赔说明", "type": 2, "description": "发生车损（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得理赔", "contents": [{"title": "不予理赔说明", "type": 2, "content": ["1、发生事故时未及时通知租车公司或未申报保险", "2、无事故证明材料或无保险理赔材料", "3、无证驾驶、酒驾、超速等其他保险不予理赔或责任免除场景"]}, {"title": "保险理赔说明", "type": 4, "content": ["1. 保险内容"]}]}]}, "upgradeSummary": {"type": "zero"}, "purchasedSub": [{"name": "基础服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 40, "gapPrice": 0, "description": [{"description": "1500元以内自付 <tag>tag0", "type": "CDWcn", "attr": "{\"carCoverage\":\"1500元\",\"vendorId\":77757,\"isBasic\":true,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "RED"}, {"contains": true, "description": "150万", "type": "TPLcn", "attr": "150万"}, {"contains": true, "description": "车损5000元以下免收", "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}"}, {"contains": false, "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "allTags": [{"title": "车轮损失自付", "code": "tag0", "colorCode": "<PERSON>"}], "insuranceDetailDescription": [{"title": "车辆损失险", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "1500元以上部分"}}, {"key": "用户或承租方需承担", "value": {"content": "1500元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "保险赔付的100%"}}, {"key": "用户或承租方需承担", "value": {"content": "保险赔付以外的100%"}}]}], "type": "CDWcn"}, {"title": "三者保障", "contains": true, "content": [], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "150万元"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "150万元"}}, {"key": "用户或承租方需承担", "value": {"content": "150万元以上的部分"}}]}], "type": "TPLcn"}, {"title": "折旧费", "contains": true, "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "desc": "", "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "5000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "5000元以上", "value": {"content": "维修费总额的20%"}}], "styleType": 1}], "type": "DPN", "subDesc": ["第三方全责的车损不收折旧费"]}, {"title": "停运费", "contains": false, "content": ["因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为："], "subContent": [{"table": [{"key": "", "value": {"great": false, "content": "停运费 = 车辆维修期间每日订单金额之和 × 100%"}}]}], "type": "OTG", "subDesc": ["每日订单金额包含基本保险，不含手续费及附加服务费", "因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）"]}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：轮胎（含轮毂）损坏、底盘损坏；以及车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "1002", "status": 3, "type": 1, "price": 40}, {"name": "优享服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 30, "gapPrice": 30, "description": [{"description": "车损保障赔付全部损失，含玻璃、轮胎", "type": "CDWcn", "attr": "{\"carCoverage\":\"0元\",\"vendorId\":77757,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}", "descriptionColorCode": "GREEN"}, {"contains": true, "description": "三者保障保额150万", "type": "TPLcn", "attr": "150万"}, {"contains": true, "description": "车损5000元以下免收折旧费", "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}"}, {"contains": false, "description": "停运费正常收取", "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "allTags": [], "insuranceDetailDescription": [{"title": "车辆损失险", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失。", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": true, "content": "全部损失"}}, {"key": "用户或承租方需承担", "value": {"content": "0元"}}]}], "type": "CDWcn"}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "2001", "status": 3, "type": 1, "price": 30, "selectedEncourage": "经济实用 超值之选"}], "tenancyTerm": 1}}}