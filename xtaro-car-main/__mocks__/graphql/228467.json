{"data": {"getReceivePromotion": {"baseResponse": {"isSuccess": true, "code": "0", "returnMsg": "操作成功", "requestId": "4b8ee0bd-4b11-4282-a2d3-362adcc983cb", "showMessage": "操作成功", "extMap": {}, "apiResCodes": [], "errorCode": "0", "message": "操作成功"}, "ResponseStatus": {"Timestamp": "/Date(1682482402687+0800)/", "Ack": "Success", "Errors": [], "Extension": []}, "title": "每日好券", "promotions": [{"promotionSecretId": "c7292efccb8be2ca7803fd028b820334aaab8f1734399665ca52ae02dbdafc378b7d9e926379a5525eb9dabf0a611d001580cccbfa4a17d625e07e74cfce86dc", "currencyCode": "¥", "value": "50", "valueType": 0, "discountText": "", "limitText": "", "name": "国内无门槛立减50立减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "shortDesc": "国内无门槛立减50", "longDesc": "国内无门槛立减50", "status": 1, "statusName": "领取", "invaildReason": "", "deductionList": [], "isActive": true, "briefName": "立减¥50", "scenes": 1, "promotionId": 765745525, "couponCode": "", "meetCouponStartDate": true}, {"promotionSecretId": "3ff4c03bcaaa7af391cb85b5fa496fb4abefbdb1b183b608145040c3fc9ac67c8b7d9e926379a5525eb9dabf0a611d001580cccbfa4a17d625e07e74cfce86dc", "currencyCode": "¥", "value": "40", "valueType": 0, "discountText": "", "limitText": "最高减", "name": "国内阶梯满减满减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "shortDesc": "国内阶梯满减", "longDesc": "国内阶梯满减", "status": 1, "statusName": "领取", "invaildReason": "", "deductionList": ["满¥100减¥20", "满¥200减¥40"], "isActive": true, "briefName": "最高减¥40", "scenes": 1, "promotionId": 667234322, "couponCode": "", "meetCouponStartDate": true}, {"promotionSecretId": "cf1db2568f4731a6501e088c23a296f459f2d53e369e9e11a447b1c5f7583a268b7d9e926379a5525eb9dabf0a611d001580cccbfa4a17d625e07e74cfce86dc", "currencyCode": "", "value": "7.0", "valueType": 0, "discountText": "折", "limitText": "最低", "name": "国内阶梯折扣满减满减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "shortDesc": "国内阶梯折扣满减", "longDesc": "国内阶梯折扣满减", "status": 1, "statusName": "领取", "invaildReason": "", "deductionList": ["满¥100享受9.0折", "满¥200享受7.0折"], "isActive": true, "briefName": "最低7.0折", "scenes": 1, "promotionId": 633909833, "couponCode": "", "meetCouponStartDate": true}, {"promotionSecretId": "84f448d36180dc34c836084966f0264a7b7d96a7e31174c6b1bd3a876caaa52a8b7d9e926379a5525eb9dabf0a611d001580cccbfa4a17d625e07e74cfce86dc", "currencyCode": "", "value": "9.0", "valueType": 0, "discountText": "折", "limitText": "", "name": "国内立减折扣9折立减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "shortDesc": "国内立减折扣9折", "longDesc": "国内立减折扣9折", "status": 1, "statusName": "领取", "invaildReason": "", "deductionList": [], "isActive": true, "briefName": "立享9.0折", "scenes": 1, "promotionId": 288845477, "couponCode": "", "meetCouponStartDate": true}]}, "queryWarningList": {"baseResponse": {"isSuccess": true, "code": "200", "extMap": {}}, "ResponseStatus": {"Timestamp": "/Date(1682482402621+0800)/", "Ack": "Success", "Errors": [], "Extension": []}, "warningDtos": []}, "queryHomePageCard": {"responseStatus": {"timestamp": "/Date(1682482403572+0800)/", "ack": "Success", "errors": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "", "cost": 957}, "orderList": [{"orderId": 36510014534, "channel": "ISD", "ctripOrderStatus": "CAR_IN_SERVICE", "useDay": "2天后", "ctripOrderStatusDesc": "用车中", "lastEnablePayTime": 1682406153000, "pickUpDate": 1682470800000, "returnDate": 1682649000000, "vehicleName": "雪佛兰科沃兹雪佛兰科沃兹雪佛兰科沃兹", "vehiclePic": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "pickupInfo": {"localDateTime": "2023-04-26 09:00:00", "storeTel": "15566065515;15566065513;15338990710", "title1": "取车方式", "pickReturnWay": "店员免费送车上门", "title2": "取车地址", "address": "凤凰国际机场T1航站楼", "storeID": 106883, "serviceType": "2", "userLongitude": 109.41235, "userAddress": "凤凰国际机场T1航站楼", "userLatitude": 18.30767}, "returnInfo": {"localDateTime": "2023-04-28 10:30:00", "storeTel": "15566065515;15566065513;15338990710", "title1": "还车方式", "pickReturnWay": "店员免费上门取车", "title2": "还车地址", "address": "凤凰国际机场T1航站楼", "storeID": 106883, "serviceType": "2", "userLongitude": 109.41235, "userAddress": "凤凰国际机场T1航站楼", "userLatitude": 18.30767}, "allOperations": [{"operationId": 13, "buttonName": "订单详情", "enable": true, "icon": "&#xf08f9;"}, {"operationId": 14, "buttonName": "联系门店", "enable": true, "icon": "&#xf08fc;"}, {"operationId": 11, "buttonName": "续租", "enable": true, "label": "车辆想要多用一会儿？请点击续租", "contents": [], "icon": "&#xf08fb;"}], "isPick": false, "locationType": 1, "useDayDesc": "三亚还车", "klbVersion": 1, "earlyReturn": true}]}, "queryMembershipRights4Index": {"ResponseStatus": {"Timestamp": "/Date(1682482402682+0800)/", "Ack": "Success", "Errors": [], "Extension": []}, "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "5ccaf553-a694-4deb-8b06-67528b2d9b97", "extMap": {}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "rights": {"title": {"stringObjs": [{"content": "普通会员，您可享受以下权益"}]}, "items": [{"code": "PointsAcceleration", "title": "1.0倍积分加速", "subTitle": "普通会员", "icon": "https://dimg04.c-ctrip.com/images/0AS1e120009hi6ghz0C65.png", "status": 1, "url": ""}]}, "curLevelName": "普通会员", "curLevelCode": "0"}, "getStreamRecCityId": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "764f3038-4262-4acc-bc59-94659c61e53e", "extMap": {}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1682482402663+0800)/", "Ack": "Success", "Errors": []}, "recCityId": 2, "cityIdType": "IntentCityId"}, "getIndexSuperVip": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "8853bdc3-7d31-472d-8438-254658ab255e", "extMap": {}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1682482402683+0800)/", "Ack": "Success", "Errors": []}}, "queryYunnanWelfare": {"BaseResponse": {"IsSuccess": true, "Code": "0", "ReturnMsg": "success", "RequestId": "39982da6-edd3-4cd2-9a9c-971c3bbc0baf", "Cost": 9}, "ResponseStatus": {"Timestamp": "/Date(1682482402623+0800)/", "Ack": "Success", "Errors": [], "Extension": []}, "show": false, "amount": "￥100", "desc": "云南自驾租车，赠#加油券"}, "homeController": {"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success"}, "ResponseStatus": {"Timestamp": "/Date(1682482402631+0800)/", "Ack": "Success", "Errors": []}, "rentcenterId": 2, "filter": [{"type": 1, "code": "PickReturn_StationPR", "text": "只看站内取还"}], "pickupOnDoorCode": "PickReturn_PickupOnDoor", "pHub": 1, "rHub": 1}}}