{"data": {"queryHomePageCard": {"baseResponse": {"code": "200", "requestId": "", "cost": 528, "isSuccess": true, "returnMsg": "success"}, "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1682068821972+0800)/"}, "orderList": [{"isPick": true, "useDay": "173天后", "channel": "ISD", "pickupInfo": {"localDateTime": "2023-10-11 10:00:00", "storeTel": "18117159935;15800651881;15800651881", "title1": "取车方式", "title2": "取车地址", "address": "上海市闵行区高泊飞虹桥天山西路停车场-出入口", "storeID": 153209, "serviceType": "0", "userLongitude": 121.3521744411732, "pickReturnWay": "自行前往门店取车", "userLatitude": 31.22188427709729, "userAddress": "SkyBridge HQ天会"}, "lastEnablePayTime": 1681961459000, "returnDate": 1697162400000, "vehiclePic": "https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0pcn152vdz1tkq982C2.jpg", "ctripOrderStatusDesc": "已确认", "klbVersion": 1, "earlyReturn": false, "ctripOrderStatus": "CAR_CONFIRMED", "orderId": 23761729688, "allOperations": [{"operationId": 13, "enable": true, "icon": "&#xf08f9;", "buttonName": "订单详情"}, {"operationId": 14, "enable": true, "icon": "&#xf08fc;", "buttonName": "联系门店"}, {"operationId": 15, "enable": true, "icon": "&#xf08fa;", "buttonName": "用车必备材料"}], "returnInfo": {"localDateTime": "2023-10-13 10:00:00", "storeTel": "18117159935;15800651881;15800651881", "title1": "还车方式", "title2": "还车地址", "address": "上海市闵行区高泊飞虹桥天山西路停车场-出入口", "storeID": 153209, "serviceType": "0", "userLongitude": 121.3521744411732, "pickReturnWay": "自行前往门店还车", "userLatitude": 31.22188427709729, "userAddress": "SkyBridge HQ天会"}, "pickUpDate": 1696989600000, "locationType": 1, "vehicleName": "福特福睿斯", "useDayDesc": "上海取车"}]}, "getCityTimeZone": {"isSuccessful": true, "cityTimeZone": -8, "ResponseStatus": {"Timestamp": "/Date(1682068821443+0800)/", "Ack": "Success", "Errors": []}}, "queryWarningList": {"baseResponse": {"code": "200", "extMap": {}, "isSuccess": true}, "warningDtos": [], "ResponseStatus": {"Extension": [], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1682068821443+0800)/"}}, "queryMembershipRights4Index": {"baseResponse": {"extMap": {}, "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "OK", "requestId": "5fdb42fa-3bae-41fc-96f3-af9c682d8144", "isSuccess": true}, "curLevelName": "铂金贵宾", "curLevelCode": "20", "ResponseStatus": {"Timestamp": "/Date(1682068821476+0800)/", "Ack": "Success", "Errors": []}}}}