{"data": {"queryWarningList": {"baseResponse": {"code": "200", "extMap": {}, "isSuccess": true}, "warningDtos": [], "ResponseStatus": {"Timestamp": "/Date(1688119347649+0800)/", "Ack": "Success", "Errors": []}}, "getEasyLifeTagInfo": {"baseResponse": {"isSuccess": true}, "easyLifeTag": [{"sortNum": 1, "showLayer": 1, "title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "subTitle": "*国定节假日以页面披露为准"}, {"sortNum": 3, "title": "优质车况", "subTitle": "", "type": 1, "description": "3年内车龄", "showLayer": 0}, {"sortNum": 10, "title": "满油取车", "subTitle": "", "type": 0, "description": "保障取车时油量满", "showLayer": 0}, {"sortNum": 99, "title": "车辆守护升级", "subTitle": "*覆盖损失范围以预订页面内披露为准", "type": 0, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧"}], "ResponseStatus": {"Timestamp": "/Date(1688119347644+0800)/", "Ack": "Success", "Errors": []}}, "queryOrderInsAndXProduct": {"upgradeSummary": {"type": "zero"}, "purchasedSub": [{"status": 3, "type": 1, "gapPrice": 0, "price": 156, "allTags": [{"title": "车轮损失自付", "colorCode": "<PERSON>", "code": "tag0"}], "currentDailyPrice": 39, "insuranceDetailDescription": [{"title": "车损保障", "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "1500元以上的部分"}}, {"key": "客户承担", "value": {"content": "≤1500元（据实承担）"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "保司核定理赔金额"}}, {"key": "客户承担", "value": {"content": "保司核定车辆价值－理赔金额"}}]}], "contains": true, "type": "CDWcn"}, {"title": "车上人员（乘客）保障", "content": ["保障车辆发生意外事故，导致乘客人身伤亡。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "10000元/座"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "1万元/座"}}, {"key": "客户承担", "value": {"content": "1万元/座以上的部分"}}]}], "contains": true, "type": "PAIcn"}, {"title": "三者保障", "content": [], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "200万元"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "200万元"}}, {"key": "客户承担", "value": {"content": "200万元以上的部分"}}]}], "contains": true, "type": "TPLcn"}, {"subDesc": ["因第三方全责导致的车损，不收取用户/承租人的折旧费（因用户/承租人原因导致无法收取的除外）"], "title": "折旧费", "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "styleType": 1, "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "5000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "5000元以上", "value": {"content": "维修费总额的20%"}}], "desc": ""}], "contains": true, "type": "DPN"}, {"subDesc": ["每日订单金额包含基本保险，不含手续费及附加服务费", "因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）"], "title": "停运费", "content": ["因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为："], "subContent": [{"table": [{"key": "", "value": {"great": false, "content": "停运费 = 车辆维修期间每日订单金额之和 × 100%"}}]}], "contains": false, "type": "OTG"}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车轮（包括轮胎、轮毂、轮毂罩）损坏；以及车牌等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "description": [{"attr": "{\"carCoverage\":\"1500元\",\"vendorId\":62534,\"isBasic\":true,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "RED", "description": "1500元以内自付 <tag>tag0", "type": "CDWcn"}, {"attr": "200万", "description": "200万", "contains": true, "type": "TPLcn"}, {"attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}", "description": "车损5000元以下免收", "contains": true, "type": "DPN"}, {"contains": false, "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "uniqueCode": "1002", "name": "基础服务", "currentCurrencyCode": "CNY"}, {"description": [{"attr": "{\"carCoverage\":\"0元\",\"vendorId\":62534,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}", "descriptionColorCode": "GREEN", "description": "全额赔付", "type": "CDWcn"}, {"attr": "200万", "description": "200万", "contains": true, "type": "TPLcn"}, {"attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}", "description": "车损5000元以下免收", "contains": true, "type": "DPN"}, {"contains": false, "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "gapPrice": 53, "insuranceDetailDescription": [{"title": "车损保障", "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}], "contains": true, "type": "CDWcn"}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车牌等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "currentCurrencyCode": "CNY", "type": 1, "uniqueCode": "2001", "price": 210, "selectedEncourage": "经济实用 超值之选", "allTags": [], "currentDailyPrice": 53, "name": "优享服务", "status": 3}, {"description": [{"attr": "{\"carCoverage\":\"0元\",\"vendorId\":62534,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}", "show": false, "description": "全额赔付", "type": "CDWcn", "descriptionColorCode": "GREEN"}, {"attr": "200万", "show": false, "description": "200万", "contains": true, "type": "TPLcn"}, {"descriptionColorCode": "GREEN", "attr": "{\"cover\":true,\"excess\":\"3万\",\"desc\":\"车损3万元以下免收折旧费\",\"summary\":\"车损3万元以下免收折旧费\",\"containsDescription\":\"车损3万元以下免收\",\"descriptionColorCode\":\"GREEN\"}", "show": false, "description": "车损3万元以下免收", "contains": true, "type": "DPN"}, {"descriptionColorCode": "GREEN", "attr": "{\"cover\":true,\"excess\":\"1万\",\"desc\":\"免收1万元以下停运费\",\"summary\":\"免收1万元以下停运费\",\"containsDescription\":\"1万\",\"descriptionColorCode\":\"GREEN\"}", "show": false, "description": "1万", "contains": true, "type": "OTG"}, {"attr": "含无需垫付", "show": false, "type": "Disbursements", "contains": true}], "gapPrice": 15, "insuranceDetailDescription": [{"title": "车损保障", "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}], "contains": true, "type": "CDWcn"}, {"subDesc": ["因第三方全责导致的车损，不收取用户/承租人的折旧费（因用户/承租人原因导致无法收取的除外）"], "title": "折旧费", "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "styleType": 1, "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "30000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "30000元以上", "value": {"content": "维修费总额的20%"}}], "desc": ""}], "contains": true, "type": "DPN"}, {"subDesc": ["每日订单金额包含基本保险，不含手续费及附加服务费", "因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）"], "title": "停运费", "content": ["因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为："], "subContent": [{"title": "尊享服务为您提供停运费保障：", "table": [{"key": "保额", "value": {"great": true, "content": "1万"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "保额以内保司定额的100%"}}, {"key": "您需承担", "value": {"great": false, "content": "保险赔付以外的100%"}}], "styleType": 0}], "contains": true, "type": "OTG"}, {"title": "无须垫付", "content": ["无需垫付定金或相关费用，车辆维修出险后，根据费用单等，补缴给租车公司您所需赔付的部分。"], "contains": true, "great": true}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车牌等非车辆本身的设备或物料的遗失与损坏。", "3.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "currentCurrencyCode": "CNY", "type": 1, "uniqueCode": "20111", "price": 60, "selectedEncourage": "全面保障 无忧之选", "allTags": [], "currentDailyPrice": 15, "name": "尊享服务", "status": 3}, {"status": 3, "type": 1, "gapPrice": 133, "price": 530, "allTags": [{"title": "含玻璃、车轮", "colorCode": "ORANGE", "code": "tag0"}], "currentDailyPrice": 133, "insuranceDetailDescription": [{"title": "车损保障", "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}], "contains": true, "type": "CDWcn"}, {"subDesc": ["因第三方全责导致的车损，不收取用户/承租人的折旧费（因用户/承租人原因导致无法收取的除外）"], "title": "折旧费", "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "styleType": 1, "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "30000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "30000元以上", "value": {"content": "维修费总额的10%"}}], "desc": ""}], "contains": true, "type": "DPN"}, {"title": "免停运费", "content": ["免收因车损产生的车辆停运费"], "type": "OTG", "contains": true, "great": true}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车牌等非车辆本身的设备或物料的遗失与损坏。", "3.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "description": [{"attr": "{\"carCoverage\":\"0元\",\"vendorId\":62534,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}", "show": false, "description": "车损保障赔付全部损失，含玻璃、车轮", "type": "CDWcn", "descriptionColorCode": "GREEN"}, {"attr": "200万", "show": false, "description": "三者保障保额200万", "contains": true, "type": "TPLcn"}, {"descriptionColorCode": "GREEN", "attr": "{\"cover\":true,\"excess\":\"3万\",\"desc\":\"车损3万元以下免收折旧费\",\"summary\":\"车损3万元以下免收折旧费\",\"containsDescription\":\"车损3万元以下免收\",\"descriptionColorCode\":\"GREEN\"}", "show": false, "description": "车损3万元以下免收折旧费", "contains": true, "type": "DPN"}, {"attr": "{\"cover\":true,\"desc\":\"您无需承担车辆停运费\",\"summary\":\"免停运费\"}", "show": false, "description": "您无需承担车辆停运费", "contains": true, "type": "OTG"}, {"attr": "含无需垫付", "show": false, "type": "Disbursements", "contains": false}], "uniqueCode": "60003", "name": "门店服务", "currentCurrencyCode": "CNY"}], "tenancyTerm": 4, "purchased": {"status": 3, "type": 1, "gapPrice": 133, "price": 530, "allTags": [{"title": "含玻璃、车轮", "colorCode": "ORANGE", "code": "tag0"}], "currentDailyPrice": 133, "insuranceDetailDescription": [{"title": "车损保障", "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}], "contains": true, "type": "CDWcn"}, {"subDesc": ["因第三方全责导致的车损，不收取用户/承租人的折旧费（因用户/承租人原因导致无法收取的除外）"], "title": "折旧费", "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "styleType": 1, "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "30000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "30000元以上", "value": {"content": "维修费总额的10%"}}], "desc": ""}], "contains": true, "type": "DPN"}, {"title": "免停运费", "content": ["免收因车损产生的车辆停运费"], "type": "OTG", "contains": true, "great": true}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车牌等非车辆本身的设备或物料的遗失与损坏。", "3.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "description": [{"attr": "{\"carCoverage\":\"0元\",\"vendorId\":62534,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}", "show": false, "description": "车损保障赔付全部损失，含玻璃、车轮", "type": "CDWcn", "descriptionColorCode": "GREEN"}, {"attr": "200万", "show": false, "description": "三者保障保额200万", "contains": true, "type": "TPLcn"}, {"descriptionColorCode": "GREEN", "attr": "{\"cover\":true,\"excess\":\"3万\",\"desc\":\"车损3万元以下免收折旧费\",\"summary\":\"车损3万元以下免收折旧费\",\"containsDescription\":\"车损3万元以下免收\",\"descriptionColorCode\":\"GREEN\"}", "show": false, "description": "车损3万元以下免收折旧费", "contains": true, "type": "DPN"}, {"attr": "{\"cover\":true,\"desc\":\"您无需承担车辆停运费\",\"summary\":\"免停运费\"}", "show": false, "description": "您无需承担车辆停运费", "contains": true, "type": "OTG"}], "uniqueCode": "60003", "name": "基础服务+优享服务+优享升级尊享+尊享升级门店服务", "currentCurrencyCode": "CNY"}, "baseResponse": {"code": "", "requestId": "", "cost": 346, "isSuccess": true, "returnMsg": "success"}, "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1688119347986+0800)/", "extension": []}, "insuranceAndXProductList": [], "upgradeGuarantee": {"packageDetailList": [], "rentalGuaranteeTitle": ["车损保障", "三者保障", "免折旧费", "免停运费", "无需垫付"], "vendorServiceDesc": "车行服务是指车行为客人安全用车提供的服务，包含车行手续（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。", "purchasingNotice": {"title": "购买须知", "content": ["1.车行服务由门店提供，仅支持取车前购买。", "2.订单续租时只能购买与原订单相同的服务。例如，如果原订单未加购升级服务，在续租时也无法加购升级服务；如果原订单加购了某升级服务，在续租时须继续加购相同的升级服务，无法取消或替换为其他升级服务。"]}, "vendorServiceDetail": [{"title": "车辆事故处理流程", "type": 1, "description": "发生车损（本车与第三方，或本车单独发生事故）时，请按下方流程处理，否则将无法享受门店服务保障权益。", "contents": [{"title": "报警并联系门店", "type": 1, "content": ["拨打122报警（上海地区拨打110），并立即联系门店获取后续操作指引（如保险报案等）。车辆需由门店知晓并同意后再修理。擅自修理造成的额外损失将由用户或承租方承担。"]}, {"title": "拍照并留存记录信息", "type": 1, "content": ["包括但不限于涉事各方车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况。"]}, {"title": "明确事故责任，配合交警完成事故处理", "type": 1, "content": ["等待交警明确涉事各方事故责任，交警处理完成后方可离开现场。"]}, {"title": "提交材料进行理赔（如需）", "type": 1, "content": ["如门店判断需进行保险报案，用户或承租方须配合准备材料、联系保险公司进行保险理赔报案。如因用户或承租方未配合保险报案导致的额外损失将由用户或承租方承担。"]}]}, {"title": "保险理赔（如需）说明", "type": 2, "description": "发生车损后，如需保险报案获取理赔，请阅读并知晓如下说明", "contents": [{"title": "不予理赔说明", "type": 2, "content": ["1、发生事故时未及时通知租车公司或未申报保险", "2、无事故证明材料或无保险理赔材料", "3、无证驾驶、酒驾、超速等其他保险不予理赔或责任免除场景"]}, {"title": "保险理赔说明", "type": 4, "content": ["1. 如果车辆不慎发生事故，请注意人身安全，并立即致电门店，按门店指引报案、收集理赔材料。齐全的理赔材料可以节省理赔等待时间。\n1. 如用户或承租方已还车并提交了齐全的理赔材料，则不需要垫付本车维修等保险公司应付费用，仅需：\na)支付服务保障详情中列明的的由用户或承租方承担的损失，以及服务保障范围外的所有责任和损失；\nb)垫付第三方损失，该费用将在保险公司完成理赔后返还。", "2. 如用户或承租方还车时不能提供齐全的理赔材料，一般有以下2种方式：\na.)无需先行支付。在保险公司完成定损或车辆维修完成后，根据实际费用计算用户或承租方需承担的金额支付给门店；\nb.)需用户或承租方先行交付部分金额（金额与门店现场沟通）。在保险公司完成定损或车辆实际维修完成后，根据实际费用金额多退少补。 "]}]}], "vendorServiceSubDesc": "上述车损险和三者险仅覆盖保险理赔范围内的损失，理赔范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求进行理赔导致保险公司拒赔的情况，您需承担全额损失。"}}, "queryPriceInfo": {"baseResponse": {"code": "unknown", "requestId": "", "cost": 306, "isSuccess": true, "returnMsg": "success"}, "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1688119347897+0800)/", "extension": []}, "feeDetailInfo": {"depositInfo": {"items": []}, "discountList": [], "equipmentInfos": [], "couponInfos": [], "chargesInfos": [{"code": "CAR_RENTAL_FEE", "currentTotalPrice": 495, "title": "车辆租金", "currencyCode": "CNY", "items": [{"unit": "", "priceDailys": [{"date": "5月23日 周二", "priceStr": "¥159", "showType": 0}, {"date": "5月24日 周三", "priceStr": "¥159", "showType": 0}, {"date": "5月25日 周四", "priceStr": "¥159", "showType": 0}, {"priceStr": "¥18(7小时)", "showType": 0, "oDprice": "全日价¥159", "date": "5月26日 周五"}], "code": "1001", "title": "租车费", "count": 4, "currencyCode": "CNY", "currentDailyPrice": 159, "currentTotalPrice": 495, "subTitle": "", "hourDesc": "小时费收费规则"}], "currentDailyPrice": 159}, {"code": "SERVICE_CHARGE", "currentTotalPrice": 20, "title": "车行手续费", "currencyCode": "CNY", "items": [{"unit": "", "code": "1003", "title": "车行手续费", "count": 1, "currencyCode": "CNY", "currentDailyPrice": 20, "description": "（含车辆清洁、单据打印等）", "currentTotalPrice": 20}]}, {"code": "CAR_GUARANTEE_FEE", "currentTotalPrice": 426, "title": "车行保障服务费", "currencyCode": "CNY", "items": [{"unit": "", "code": "1002", "title": "基础服务费", "size": "¥39 x4天", "count": 4, "currencyCode": "CNY", "currentDailyPrice": 50, "currentTotalPrice": 156}, {"unit": "", "code": "2001", "title": "优享服务费", "size": "¥53 x4天", "count": 4, "currencyCode": "CNY", "currentDailyPrice": 70, "currentTotalPrice": 210, "subTitle": ""}, {"unit": "", "code": "20111", "title": "优享升级尊享", "size": "¥15 x4天", "count": 4, "currencyCode": "CNY", "description": "车损保障赔付全部损失，含玻璃、车轮，三者保障保额200万，车损3万元以下免收折旧费，免停运费", "currentDailyPrice": 15, "subTitle": "", "currentTotalPrice": 60}], "hourDesc": "若租期不足1天，按1天价格收取"}], "rentalTerm": 4, "userPoints": {"pointsNotice": [{"title": {"stringObjs": [{"content": "本单可获积分", "style": "1"}]}, "desc": [{"stringObjs": [{"content": "订单成交金额 x 15% x 2.5 "}]}]}, {"title": {"stringObjs": [{"content": "规则说明", "style": "1"}]}, "desc": [{"stringObjs": [{"content": "1.使用返现、优惠券等活动订单，优惠金额不参与携程积分计算；"}]}, {"stringObjs": [{"content": "2.使用积分抵现功能支付部分不参与携程积分计算；"}]}, {"stringObjs": [{"content": "3.积分将在行程完成且订单状态更新为完成状态后发放"}]}]}], "title": "还车后可获", "subTitle": "积分", "pointsTip": "已享2.5倍加速", "currencyPrice": 352}, "offlineFee": {"title": "线下购买项", "items": [{"title": "门店服务", "currentTotalPrice": 530}], "hourDesc": "所列价格仅供参考，以购买时实际价格为准"}, "chargesSummary": {"code": "Summary", "title": "订单总额", "currencyCode": "CNY", "notices": [], "currentTotalPrice": 941, "type": 103, "payMode": 2, "items": [{"title": "在线支付", "currencyCode": "CNY", "code": "1"}, {"title": "招商银行¥940.9", "currencyCode": "CNY", "code": "6"}, {"title": "支付立减¥0.1", "currencyCode": "CNY", "code": "6"}]}}}, "getLicense": {"status": 1, "defaultLicenseImgUrl": "http://pic.c-ctrip.com/car_isd/app/h5/lisences.png", "licenseDesc": "租车服务提供方", "ResponseStatus": {"Build": null, "Version": null, "Ack": "Success", "Errors": [], "Timestamp": "/Date(1688119347657+0800)/", "Extension": []}, "licenseImgUrl": "http://images4.c-ctrip.com/target/410u19000001814onDA18.jpg?proc=watermark/t_6JCl5Lia5omn54Wn5L-h5oGv5YWs56S65LiT55So,s_70,l_center,r_315,d_40,c_696969,F732", "companyName": "租车宝测试供应商", "defaultlLicenseDesc": "预订服务提供方", "defaultCompanyName": "上海华程西南国际旅行社有限公司"}, "queryAdditionPayment": {"baseResponse": {"code": "200", "requestId": "", "cost": 48, "isSuccess": true, "returnMsg": "success"}, "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1688119347688+0800)/", "extension": []}, "additionalPaymentList": []}, "queryCertificateV3": {"baseResponse": {"code": "unknown", "requestId": "", "cost": 301, "isSuccess": true, "returnMsg": "success"}, "supportInfo": {"isShow": false, "showTitle": "身份证及驾照在线认证"}, "certificateV3List": [{"certificateV3": {"certificateType": "1"}, "isActive": false}, {"certificateV3": {"certificateType": "2"}, "isActive": false}], "responseStatus": {"ack": "Success", "errors": [], "timestamp": "/Date(1688119347942+0800)/", "extension": []}, "policy": {"isChange": false}}}}