{"resultCode": 200, "privileges": [{"status": 1, "code": 100, "appSkipLink": "", "describe": "超级会员首日0元", "openStatus": 1, "type": 0, "name": "超级会员", "index": null}, {"status": 1, "code": 0, "appSkipLink": "", "describe": "敬请期待", "openStatus": 1, "type": 0, "name": "普通会员", "index": null}, {"status": 1, "code": 101, "appSkipLink": "ctrip://wireless/flight_inland_inquire", "describe": "解锁租车9折", "openStatus": 1, "type": 1, "name": "机票用户", "index": null}, {"status": 1, "code": 102, "appSkipLink": "ctrip://wireless/hotel_inquire", "describe": "解锁租车9折", "openStatus": 1, "type": 1, "name": "酒店用户", "index": null}, {"status": 0, "code": 103, "appSkipLink": "", "describe": "敬请期待", "openStatus": 0, "type": 1, "name": "机酒用户", "index": null}, {"status": 0, "code": 105, "appSkipLink": "", "describe": "敬请期待", "openStatus": 0, "type": 1, "name": "火车票用户", "index": null}, {"status": 0, "code": 104, "appSkipLink": "", "describe": "敬请期待", "openStatus": 0, "type": 1, "name": "门票用户", "index": null}], "resultMessage": "成功", "ResponseStatus": {"Build": null, "Version": null, "Ack": "Success", "Errors": [], "Timestamp": "/Date(1627960620516+0800)/", "Extension": [{"Version": null, "ContentType": null, "Value": "http://cat.fws.qa.nt.ctripcorp.com/cat/r/m/921822-0a056b5d-452211-29750", "Id": "cat log link"}, {"Version": null, "ContentType": null, "Value": "2531831239253130182", "Id": "CLOGGING_TRACE_ID"}, {"Version": null, "ContentType": null, "Value": "921822-0a056b5d-452211-29749", "Id": "RootMessageId"}]}, "havePrivilegeAmount": 4, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 796, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 796, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1627960620351, "afterFetch": 1627960621147}}