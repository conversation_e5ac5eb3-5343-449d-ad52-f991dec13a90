{"crossType": 2, "locations": [{"name": "安提瓜和巴布达", "id": "201", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "安圭拉", "id": "275", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "巴哈马", "id": "185", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "巴巴多斯", "id": "202", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "百慕大", "id": "207", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "伯利兹", "id": "210", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "哥斯达黎加", "id": "38", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "古巴", "id": "39", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "加拿大", "id": "47", "status": 2, "statusName": "条件跨境", "firstChar": "C", "continent": null, "country": null, "province": null, "city": null, "policy": "多数情况下，车辆可以跨境至加拿大。跨境需携带租车协议和有效护照。跨州跨境会产生一定费用，具体费用请详询门店。"}, {"name": "开曼群岛", "id": "223", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "科摩罗", "id": "224", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "荷兰加勒比区", "id": "259", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "库拉索岛", "id": "294", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "多米尼克", "id": "217", "status": 3, "statusName": "不允许跨境", "firstChar": "D", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "多米尼加共和国", "id": "276", "status": 3, "statusName": "不允许跨境", "firstChar": "D", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "萨尔瓦多", "id": "238", "status": 3, "statusName": "不允许跨境", "firstChar": "E", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "法属圣马丁", "id": "291", "status": 3, "statusName": "不允许跨境", "firstChar": "F", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "危地马拉", "id": "90", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "格林纳达", "id": "220", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "瓜德罗普", "id": "278", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "海地", "id": "41", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "牙买加", "id": "101", "status": 3, "statusName": "不允许跨境", "firstChar": "J", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "墨西哥", "id": "72", "status": 2, "statusName": "条件跨境", "firstChar": "M", "continent": null, "country": null, "province": null, "city": null, "policy": "仅允许从德克萨斯州，新墨西哥州，亚利桑那州，加利福尼亚州跨境至墨西哥。 跨境需要提前申请并获得授权，车辆可以从该州的任何一处跨境至墨西哥。但跨境至墨西哥必须在当地购买墨西哥保险，费用为每日38-48美元（含税费）。跨境至墨西哥期间，高级道路救援服务（PERS）不生效。墨西哥法律规定，墨西哥公民不得在墨西哥驾驶美国所有的车辆，若违反该规定将处以罚款与处罚。"}, {"name": "马提尼克", "id": "279", "status": 3, "statusName": "不允许跨境", "firstChar": "M", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "尼加拉瓜", "id": "198", "status": 3, "statusName": "不允许跨境", "firstChar": "N", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "巴拿马", "id": "18", "status": 3, "statusName": "不允许跨境", "firstChar": "P", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "波多黎各", "id": "208", "status": 3, "statusName": "不允许跨境", "firstChar": "P", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "圣基茨和尼维斯", "id": "243", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "圣卢西亚", "id": "244", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "圣文森特和格林纳丁斯", "id": "246", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "荷属圣马丁", "id": "295", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "圣巴泰勒米", "id": "297", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "特立尼达和多巴哥", "id": "252", "status": 3, "statusName": "不允许跨境", "firstChar": "T", "continent": null, "country": null, "province": null, "city": null, "policy": ""}, {"name": "特克斯和凯科斯群岛", "id": "265", "status": 3, "statusName": "不允许跨境", "firstChar": "T", "continent": null, "country": null, "province": null, "city": null, "policy": ""}], "policies": ["政策可能发生变动，取车时请主动向工作人员说明您要前往的国家并确认相关政策。", "跨境相关实际收费和条件以具体门店要求为准。", " 若违反跨境政策，车辆保险将失效，甚至需缴纳罚金。"], "baseResponse": {"isSuccess": true, "code": 200, "returnMsg": "成功"}, "ResponseStatus": {"Timestamp": "/Date(1695381533766+0800)/", "Ack": "Success", "Errors": [], "Build": null, "Version": null, "Extension": [{"Id": "CLOGGING_TRACE_ID", "Version": null, "ContentType": null, "Value": "8487981413000770079"}, {"Id": "RootMessageId", "Version": null, "ContentType": null, "Value": "921822-0a305e15-470939-1201844"}]}}