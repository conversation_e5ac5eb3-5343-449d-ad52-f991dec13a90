{"policies": ["政策可能发生变动，取车时请主动向工作人员说明您要前往的国家并确认相关政策。", "跨境相关实际收费和条件以具体门店要求为准。", " 若违反跨境政策，车辆保险将失效，甚至需缴纳罚金。"], "baseResponse": {"code": 200, "returnMsg": "成功", "isSuccess": true}, "ResponseStatus": {"Build": null, "Version": null, "Ack": "Success", "Errors": [], "Timestamp": "/Date(1692181926222+0800)/", "Extension": [{"Version": null, "ContentType": null, "Value": "403191212307525871", "Id": "CLOGGING_TRACE_ID"}, {"Version": null, "ContentType": null, "Value": "921822-0a71e205-470050-1167333", "Id": "RootMessageId"}]}, "crossType": 2, "locations": [{"status": 3, "province": null, "country": null, "id": "201", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "安提瓜和巴布达", "firstChar": "A"}, {"status": 3, "province": null, "country": null, "id": "275", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "安圭拉", "firstChar": "A"}, {"status": 3, "province": null, "country": null, "id": "185", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "巴哈马", "firstChar": "B"}, {"status": 3, "province": null, "country": null, "id": "202", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "巴巴多斯", "firstChar": "B"}, {"status": 3, "province": null, "country": null, "id": "207", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "百慕大", "firstChar": "B"}, {"status": 3, "province": null, "country": null, "id": "210", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "伯利兹", "firstChar": "B"}, {"status": 3, "province": null, "country": null, "id": "38", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "哥斯达黎加", "firstChar": "C"}, {"status": 3, "province": null, "country": null, "id": "39", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "古巴", "firstChar": "C"}, {"status": 2, "province": null, "country": null, "id": "47", "city": null, "statusName": "条件跨境", "continent": null, "policy": "多数情况下，车辆可以跨境至加拿大。跨境需携带租车协议和有效护照。跨州跨境会产生一定费用，具体费用请详询门店。", "name": "加拿大", "firstChar": "C"}, {"status": 3, "province": null, "country": null, "id": "223", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "开曼群岛", "firstChar": "C"}, {"status": 3, "province": null, "country": null, "id": "224", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "科摩罗", "firstChar": "C"}, {"status": 3, "province": null, "country": null, "id": "259", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "荷兰加勒比区", "firstChar": "C"}, {"status": 3, "province": null, "country": null, "id": "294", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "库拉索岛", "firstChar": "C"}, {"status": 3, "province": null, "country": null, "id": "217", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "多米尼克", "firstChar": "D"}, {"status": 3, "province": null, "country": null, "id": "276", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "多米尼加共和国", "firstChar": "D"}, {"status": 3, "province": null, "country": null, "id": "238", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "萨尔瓦多", "firstChar": "E"}, {"status": 3, "province": null, "country": null, "id": "291", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "法属圣马丁", "firstChar": "F"}, {"status": 3, "province": null, "country": null, "id": "90", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "危地马拉", "firstChar": "G"}, {"status": 3, "province": null, "country": null, "id": "220", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "格林纳达", "firstChar": "G"}, {"status": 3, "province": null, "country": null, "id": "278", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "瓜德罗普", "firstChar": "G"}, {"status": 3, "province": null, "country": null, "id": "41", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "海地", "firstChar": "H"}, {"status": 3, "province": null, "country": null, "id": "101", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "牙买加", "firstChar": "J"}, {"status": 2, "province": null, "country": null, "id": "72", "city": null, "statusName": "条件跨境", "continent": null, "policy": "仅允许从德克萨斯州，新墨西哥州，亚利桑那州，加利福尼亚州跨境至墨西哥。 跨境需要提前申请并获得授权，车辆可以从该州的任何一处跨境至墨西哥。但跨境至墨西哥必须在当地购买墨西哥保险，费用为每日38-48美元（含税费）。跨境至墨西哥期间，高级道路救援服务（PERS）不生效。墨西哥法律规定，墨西哥公民不得在墨西哥驾驶美国所有的车辆，若违反该规定将处以罚款与处罚。", "name": "墨西哥", "firstChar": "M"}, {"status": 3, "province": null, "country": null, "id": "279", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "马提尼克", "firstChar": "M"}, {"status": 3, "province": null, "country": null, "id": "198", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "尼加拉瓜", "firstChar": "N"}, {"status": 3, "province": null, "country": null, "id": "18", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "巴拿马", "firstChar": "P"}, {"status": 3, "province": null, "country": null, "id": "208", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "波多黎各", "firstChar": "P"}, {"status": 3, "province": null, "country": null, "id": "243", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "圣基茨和尼维斯", "firstChar": "S"}, {"status": 3, "province": null, "country": null, "id": "244", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "圣卢西亚", "firstChar": "S"}, {"status": 3, "province": null, "country": null, "id": "246", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "圣文森特和格林纳丁斯", "firstChar": "S"}, {"status": 3, "province": null, "country": null, "id": "295", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "荷属圣马丁", "firstChar": "S"}, {"status": 3, "province": null, "country": null, "id": "297", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "圣巴泰勒米", "firstChar": "S"}, {"status": 3, "province": null, "country": null, "id": "252", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "特立尼达和多巴哥", "firstChar": "T"}, {"status": 3, "province": null, "country": null, "id": "265", "city": null, "statusName": "不允许跨境", "continent": null, "policy": "", "name": "特克斯和凯科斯群岛", "firstChar": "T"}], "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "13589%2Fgetcrosslocations_M180970538_%7B%22baseRequest%22%3A%7B%22sourceFrom%22%3A%22OSD_C_APP%22%2C%22channelType%22%3A7%2C%22invokeFrom%22%3A%22%22%2C%22platform%22%3A%22app_ctrip%22%2C%22site%22%3A%22cn%22%2C%22language%22%3A%22cn%22%2C%22locale%22%3A%22zh_cn%22%2C%22currencyCode%22%3A%22CNY%22%2C%22sourceCountryId%22%3A1%2C%22channelId%22%3A17671%2C%22clientVersion%22%3A%2220230815100733%22%2C%22clientid%22%3A%2212001145310000201410%22%2C%22vid%22%3A%221692180323364.xtmrc3%22%2C%22patternType%22%3A%2234%22%2C%22mobileInfo%22%3A%7B%22customerGPSLat%22%3A22.308046340942383%2C%22customerGPSLng%22%3A113.91629028320312%2C%22mobileModel%22%3A%22iPhone%2014%20Plus_Simulator%22%2C%22wirelessVersion%22%3A%228.52.6%22%7D%2C%22allianceInfo%22%3A%7B%22allianceId%22%3A0%2C%22ouid%22%3A%221%22%2C%22sid%22%3A1%2C%22distributorUID%22%3A%221%22%7D%2C%22extraMaps%22%3A%7B%22batchVersion%22%3A%22%22%2C%22pageVersion%22%3A%22%22%2C%22depositVersion%22%3A%22%22%2C%22creditVersion%22%3A%22%22%2C%22abVersion%22%3A%22230524_DSJT_syfc%7CA%2C230814_DSJT_syqy%7CB%2C231218_DSJT_qccl%7CB%2C230628_DSJT_dksc%7CA%22%2C%22partialVersion%22%3A%2220230815100733%22%2C%22queryProductsVersion%22%3A%22cashbackDemand%22%2C%22crnVersion%22%3A%2237%22%2C%22queryVid%22%3A%22e881d32a-9277-4788-9aa9-d06c49d7e14f%22%2C%22sourceFrom%22%3A%22OSD_C_APP%22%2C%22platform%22%3A%22app_ctrip%22%2C%22onePost%22%3A%221%22%2C%22ctripVersion%22%3A1%2C%22insVersion%22%3A%221%22%2C%22encryptUid%22%3A%22%22%2C%22fromType%22%3A%22%22%2C%22originOrderId%22%3A%22%22%2C%22channelId%22%3A%22%22%2C%22eid%22%3A%22%22%2C%22commentVersion%22%3A1%2C%22ehaiDepositVersion%22%3A%221.0%22%2C%22priceUnitedVersion%22%3A%22%22%2C%22directRenewalVersion%22%3A%22%22%2C%22tangramAbt%22%3A%22B%22%2C%22snapshotVersion%22%3A%22v4%22%2C%22rentCenter%22%3A%221%22%2C%22detailPageVersion%22%3A%222%22%2C%22poiProject%22%3A%22B%22%2C%22goodsShelves%22%3A%222%22%2C%22membershipRightsV%22%3A%221%22%2C%22feeGroup%22%3A%22%22%2C%22isFilterPrice%22%3A%22%22%2C%22payOnlineVersion%22%3A%221%22%2C%22aboutToTravelVersion%22%3A%221%22%2C%22karabiVersion%22%3A%221%22%2C%22isNewRecommend%22%3A%221%22%2C%22orderDetailRestStruct%22%3A%22%22%2C%22insuranceDetail%22%3A0%2C%22vehicleDamageAuditVersion%22%3A%221%22%2C%22receiveCoupon%22%3A%221%22%2C%22filterProject%22%3A%22B%22%2C%22poiNewVersion%22%3A%222%22%2C%22rankingVersion%22%3A%222%22%2C%22groupNameVersion%22%3A%22%22%2C%22lateDepositVer%22%3A%221%22%2C%22serverRequestId%22%3A%223d58344a-0047-4ea5-b00f-64b10638f4f2%22%2C%22streamVersion%22%3Afalse%2C%22telVersion%22%3A%221%22%2C%22orderDetailCallBack%22%3A1%2C%22newPayment%22%3A1%2C%22labelOptimizeVer%22%3A%22%22%2C%22openAwardVersion%22%3A0%2C%22trip-tech-code%22%3A2%2C%22trip-os-code%22%3A0%2C%22trip-app-code%22%3A1%2C%22trip-business-code%22%3A1%2C%22trip-subBusiness-code%22%3A1%2C%22orderId%22%3A%22%22%2C%22isNewSearchNoResult%22%3A%221%22%2C%22isFilterRecommend%22%3A%221%22%2C%22isNewEnergy%22%3A%221%22%2C%22filterPoi%22%3A%220%22%2C%22klbDataFlag%22%3A1%2C%22mdtcy%22%3A1%2C%22easyLifeVersion%22%3A%221%22%2C%22earlyReturnVersion%22%3A1%2C%22licensePlateVersion%22%3A1%2C%22osdDepositFree%22%3A%221%22%2C%22secretBoxVersion%22%3A1%2C%22osdCtripIns%22%3A%221%22%2C%22vendorListVrAB2%22%3A%221%22%2C%22klbDriverLicense%22%3A%22B%22%2C%22osdProductFailGuide%22%3A%221%22%7D%2C%22extraTags%22%3A%7B%22abVersion%22%3A%22230524_DSJT_syfc%7CA%2C230814_DSJT_syqy%7CB%2C231218_DSJT_qccl%7CB%2C230628_DSJT_dksc%7CA%22%2C%22ctripVersion%22%3A1%2C%22commentVersion%22%3A1%2C%22poiProject%22%3A%22B%22%2C%22aboutToTravelVersion%22%3A%221%22%2C%22poiNewVersion%22%3A%222%22%2C%22openAwardVersion%22%3A0%2C%22filterPoi%22%3A%220%22%7D%2C%22extMap%22%3A%7B%7D%2C%22pageId%22%3A%22%22%7D%2C%22pickupCityId%22%3A347%2C%22pickupDate%22%3A%222023-08-22%2010%3A00%3A00%22%2C%22returnDate%22%3A%222023-08-29%2010%3A00%3A00%22%2C%22vehicleCode%22%3A%2210177%22%2C%22vendorCode%22%3A%22SD0004%22%2C%22crossType%22%3A2%7D", "groupId": "13589/getcrosslocations", "networkCost": 1014, "environmentCost": 1, "cacheFetchCost": 0, "fetchCost": 1014, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1692181925772, "afterFetch": 1692181926786}}