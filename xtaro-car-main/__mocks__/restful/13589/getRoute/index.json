{"baseResponse": {"isSuccess": true, "code": 200, "returnMsg": "success"}, "ResponseStatus": {"Timestamp": "/Date(1680249940965+0800)/", "Ack": "Success", "Errors": [], "Build": null, "Version": null, "Extension": [{"Id": "CLOGGING_TRACE_ID", "Version": null, "ContentType": null, "Value": "8322330113332596503"}, {"Id": "RootMessageId", "Version": null, "ContentType": null, "Value": "100025527-0a068a23-466736-20592"}]}, "params": "{\"rentalLocation\":{\"pickUp\":{\"version\":\"1\",\"cid\":43,\"cname\":\"三亚\",\"country\":\"中国\",\"realcountry\":\"中国\",\"isDomestic\":true,\"area\":{\"id\":\"SYX\",\"name\":\"凤凰国际机场\",\"lat\":18.306675,\"lng\":109.426847,\"type\":\"5\"}},\"dropOff\":{\"version\":\"1\",\"cid\":43,\"cname\":\"三亚\",\"country\":\"中国\",\"realcountry\":\"中国\",\"isDomestic\":true,\"area\":{\"id\":\"SYX\",\"name\":\"凤凰国际机场\",\"lat\":18.306675,\"lng\":109.426847,\"type\":\"5\"}},\"isOneWay\":false},\"rentalDate\":{\"pickUp\":{\"dateTime\":\"20230401100000\"},\"dropOff\":{\"dateTime\":\"20230403100000\"}}}", "toast": null}