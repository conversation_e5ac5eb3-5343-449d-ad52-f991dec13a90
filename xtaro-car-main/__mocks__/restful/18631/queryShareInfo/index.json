{"baseResponse": {"isSuccess": true, "code": "200"}, "ResponseStatus": {"Timestamp": "/Date(1632914071814+0800)/", "Ack": "Success", "Errors": []}, "shareInfo": [{"shareType": 1, "shortUrl": "https://t.ctrip.cn.fws.qa.nt.ctripcorp.com/CWYWid9", "title": "携程租车-{车型名称}", "content": "<{城市名称}租车>我在携程租车发现了一款不错的车型。携程租车，无忧每一程！"}, {"shareType": 2, "shortUrl": "https://t.ctrip.cn.fws.qa.nt.ctripcorp.com/CWYWid9", "title": "携程租车-{车型名称}", "content": ""}, {"shareType": 3, "shortUrl": "https://t.ctrip.cn.fws.qa.nt.ctripcorp.com/CWYWid9", "title": "携程租车-{车型名称}", "content": "<{城市名称}租车>我在携程租车发现了一款不错的车型。携程租车，无忧每一程！"}, {"shareType": 4, "shortUrl": "https://t.ctrip.cn.fws.qa.nt.ctripcorp.com/CWYWid9", "title": "携程租车-{车型名称}", "content": "<{城市名称}租车>我在携程租车发现了一款不错的车型。携程租车，无忧每一程！"}, {"shareType": 0, "shortUrl": "https://t.ctrip.cn.fws.qa.nt.ctripcorp.com/CWYWid9", "title": "", "content": "#携程租车# <{城市名称}租车>我在携程租车发现了一款不错的车型-{车型名称}。携程租车，无忧每一程！@携程租车，https://t.ctrip.cn.fws.qa.nt.ctripcorp.com/CWYWid9"}]}