{"baseResponse": {"isSuccess": true, "extMap": {}}, "ResponseStatus": {"Timestamp": "/Date(1686732428703+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "7284275935124345355"}, {"Id": "RootMessageId", "Value": "100025527-0a063db3-468536-291050"}]}, "roadTripRecAreas": [{"areaName": "海南", "code": "hainan", "title": "海南", "desc": "被上帝宠坏的地方。宜人的气候，清新的空气，湛蓝的海水，柔软的沙滩，热情的少数民族，美味的海鲜。", "recCityId": 43, "color": "#72CBE5,#F1FCFF", "headImage": "https://pages.c-ctrip.com/cars/components-image/ctrip-app/FlowWithVehicle/pos-hainan.png", "sortNum": 0, "recCityName": "三亚"}, {"areaName": "新疆", "code": "xinjiang", "title": "新疆", "desc": "彩绘的图景，奶白的晨雾，蓝紫的暮霭，赭红的山峦，深绿的草原，抹黑的沙漠。边界蜿蜒曲折，与八国接壤。", "recCityId": 39, "color": "#E5AC69,#FFF8F2", "headImage": "https://pages.c-ctrip.com/cars/components-image/ctrip-app/FlowWithVehicle/pos-xinjiang.png", "sortNum": 2, "recCityName": "乌鲁木齐"}, {"areaName": "内蒙", "code": "neimeng<PERSON>", "title": "内蒙", "desc": "天苍苍，野茫茫，风吹草低现牛羊，说的就是内蒙的草原。", "recCityId": 103, "color": "#71C689,#F2F7F2", "headImage": "https://pages.c-ctrip.com/cars/components-image/ctrip-app/FlowWithVehicle/pos-neimeng.png", "sortNum": 3, "recCityName": "呼和浩特"}, {"areaName": "西藏", "code": "zizang", "title": "西藏", "desc": "地球第三极的主题，高原秘境，巍峨的雪山、纯净的圣湖、神秘的寺庙、西游的珍物特产、特色的民俗文化。", "recCityId": 41, "color": "#5DA6<PERSON>,#EFF3F7", "headImage": "https://pages.c-ctrip.com/cars/components-image/ctrip-app/FlowWithVehicle/pos-xizang.png", "sortNum": 4, "recCityName": "拉萨"}, {"areaName": "青海", "code": "qinghai", "title": "青海", "desc": "高原湖泊、碧绿草原、茫茫戈壁、雅丹地貌、沙漠绿洲、芳香花海，体验西北自然风光与丝路人文历史。", "recCityId": 124, "color": "#5FCBCD,#F4FAF9", "headImage": "https://pages.c-ctrip.com/cars/components-image/ctrip-app/FlowWithVehicle/pos-qinghai.png", "sortNum": 5, "recCityName": "西宁"}], "topProductGroups": [{"listType": 1, "categoryName": "低价之选", "topProductInfos": [{"imageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "4982", "vehicleName": "哪吒N01", "vendorCode": "79485", "currentDailyPrice": 38, "url": "/rn_car_main/_crn_config?CRNModuleName=rn_car_main&CRNType=1&initialPage=Market&st=ser&fromurl=common&landingto=List&apptype=ISD_C_APP&pcid=43&plat=18.303395&plng=109.414693&plname=凤凰国际机场&ptime=20230615100000&rcid=43&rlat=18.303395&rlng=109.414693&rlname=凤凰国际机场&rtime=20230617100000&channelId=17671&locale=zh_cn&currencyCode=CNY&sourceCountryId=1&site=cn&language=cn&vehicleid=4982&filters=%5B%22%E5%93%AA%E5%90%92%E6%B1%BD%E8%BD%A6%22%5D&currentDailyPrice=38&orignScenes=1&topProductsVersion=2&klbVersion=1", "pickupPointInfo": {"locationType": 1, "cityId": 43, "locationCode": "SYX", "locationName": "凤凰国际机场", "poi": {"longitude": 109.414693, "latitude": 18.303395}, "date": "/Date(1686794400000+0800)/"}, "returnPointInfo": {"locationType": 1, "cityId": 43, "locationCode": "SYX", "locationName": "凤凰国际机场", "poi": {"longitude": 109.414693, "latitude": 18.303395}, "date": "/Date(1686967200000+0800)/"}, "dailyPriceShow": "38", "klbVersion": 1}]}, {"listType": 2, "categoryName": "城市热销", "topProductInfos": [{"imageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "4139", "vehicleName": "福特Mustang MacDOnsadasdas", "vendorCode": "77147", "currentDailyPrice": 45, "url": "/rn_car_main/_crn_config?CRNModuleName=rn_car_main&CRNType=1&initialPage=Market&st=ser&fromurl=common&landingto=List&apptype=ISD_C_APP&pcid=43&plat=18.303395&plng=109.414693&plname=凤凰国际机场&ptime=20230615100000&rcid=43&rlat=18.303395&rlng=109.414693&rlname=凤凰国际机场&rtime=20230617100000&channelId=17671&locale=zh_cn&currencyCode=CNY&sourceCountryId=1&site=cn&language=cn&vehicleid=4139&filters=%5B%22%E9%9B%AA%E4%BD%9B%E5%85%B0%22%5D&currentDailyPrice=45&orignScenes=1&topProductsVersion=2&klbVersion=1", "pickupPointInfo": {"locationType": 1, "cityId": 43, "locationCode": "SYX", "locationName": "上海浦东国际机场-T2航站楼", "poi": {"longitude": 109.414693, "latitude": 18.303395}, "date": "/Date(1686794400000+0800)/"}, "returnPointInfo": {"locationType": 1, "cityId": 43, "locationCode": "SYX", "locationName": "凤凰国际机场", "poi": {"longitude": 109.414693, "latitude": 18.303395}, "date": "/Date(1686967200000+0800)/"}, "dailyPriceShow": "45", "klbVersion": 1}]}], "streamArticles": [{"id": "2083327808", "headImage": "https://dimg04.uat.qa.nt.ctripcorp.com/images/1me4l12000007h8fwBE8A.jpg?proc=source/tripcommunity", "url": "ctrip://wireless/h5?type=5&url=L3JuX2Rlc3RpbmF0aW9uX3ZpZGVvL21haW4uanM/Q1JOTW9kdWxlTmFtZT1kZXN0aW5hdGlvbmxpdmUmQ1JOVHlwZT0xJmluaXRpYWxQYWdlPXRyYXZlbFJlY29yZF9kZXRhaWwmaWQ9MjA4MzMyNzgwOA==", "title": "测试美食poi挂货", "nickName": "啦啦啦来了22", "avatar": "https://dimg04.uat.qa.nt.ctripcorp.com/images/0Z811120000073dieE5B1_C_180_180.jpg"}, {"id": "2083327807", "headImage": "https://dimg04.uat.qa.nt.ctripcorp.com/images/1me3p12000007h8fu470D.jpg?proc=source/tripcommunity", "url": "ctrip://wireless/h5?type=5&url=L3JuX2Rlc3RpbmF0aW9uX3ZpZGVvL21haW4uanM/Q1JOTW9kdWxlTmFtZT1kZXN0aW5hdGlvbmxpdmUmQ1JOVHlwZT0xJmluaXRpYWxQYWdlPXRyYXZlbFJlY29yZF9kZXRhaWwmaWQ9MjA4MzMyNzgwNw==", "title": "笔记笔记", "nickName": "啦啦啦来了22", "avatar": "https://dimg04.uat.qa.nt.ctripcorp.com/images/0Z811120000073dieE5B1_C_180_180.jpg"}, {"id": "2083327806", "headImage": "https://dimg04.uat.qa.nt.ctripcorp.com/images/1me2t12000007h8fs4A0B.jpg?proc=source/tripcommunity", "url": "ctrip://wireless/h5?type=5&url=L3JuX2Rlc3RpbmF0aW9uX3ZpZGVvL21haW4uanM/Q1JOTW9kdWxlTmFtZT1kZXN0aW5hdGlvbmxpdmUmQ1JOVHlwZT0xJmluaXRpYWxQYWdlPXRyYXZlbFJlY29yZF9kZXRhaWwmaWQ9MjA4MzMyNzgwNg==", "title": "test", "nickName": "啦啦啦来了22", "avatar": "https://dimg04.uat.qa.nt.ctripcorp.com/images/0Z811120000073dieE5B1_C_180_180.jpg"}, {"id": "2083327804", "headImage": "https://dimg04.uat.qa.nt.ctripcorp.com/images/1me6b12000007h8l6426E.jpg?proc=source/tripcommunity", "url": "ctrip://wireless/h5?type=5&url=L3JuX2Rlc3RpbmF0aW9uX3ZpZGVvL21haW4uanM/Q1JOTW9kdWxlTmFtZT1kZXN0aW5hdGlvbmxpdmUmQ1JOVHlwZT0xJmluaXRpYWxQYWdlPXRyYXZlbFJlY29yZF9kZXRhaWwmaWQ9MjA4MzMyNzgwNA==", "title": "测试poi挂货", "nickName": "啦啦啦来了22", "avatar": "https://dimg04.uat.qa.nt.ctripcorp.com/images/0Z811120000073dieE5B1_C_180_180.jpg"}, {"id": "2083327803", "headImage": "https://dimg04.uat.qa.nt.ctripcorp.com/images/1me0512000007h8fm7589.jpg?proc=source/tripcommunity", "url": "ctrip://wireless/videoGoods?requestListType=ailist&source=lvPai&articleId=2083327803&bizType=tripshoot&productType=LVPAI", "title": "喵喵", "nickName": "啦啦啦来了22", "avatar": "https://dimg04.uat.qa.nt.ctripcorp.com/images/0Z811120000073dieE5B1_C_180_180.jpg"}, {"id": "2083327799", "headImage": "https://dimg04.uat.qa.nt.ctripcorp.com/images/1me4j12000007guht7BC0.jpg?proc=source/tripcommunity", "url": "ctrip://wireless/h5?type=5&url=L3JuX2Rlc3RpbmF0aW9uX3ZpZGVvL21haW4uanM/Q1JOTW9kdWxlTmFtZT1kZXN0aW5hdGlvbmxpdmUmQ1JOVHlwZT0xJmluaXRpYWxQYWdlPXRyYXZlbFJlY29yZF9kZXRhaWwmaWQ9MjA4MzMyNzc5OQ==", "nickName": "帅帅的烟花", "avatar": "https://dimg04.uat.qa.nt.ctripcorp.com/images/zc05170000011gffv6AD5_C_180_180.jpg"}], "currentCode": "hainan", "recCityId": 43, "recCity": "三亚"}