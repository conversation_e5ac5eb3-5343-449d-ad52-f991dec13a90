{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "extMap": {"allCost": "5561.0", "pageName": "Book", "key": "129582"}, "extraIndexTags": {"requestId": "13df5f1f-4621-4d73-94ea-07a769e1b370"}}, "ResponseStatus": {"Timestamp": "/Date(1629112214956+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "7863009463424513150"}, {"Id": "RootMessageId", "Value": "100025527-0a068a23-452531-45054"}]}, "resInfo": {"orderId": 3082561436, "carAmount": 1151.0, "payAmount": 1151, "resultInfo": {"code": "51", "groupCode": "#1", "title": "当前优惠券已被使用", "msg": "如您有其他可使用的优惠券，可尝试更换"}, "paySign": {"token": "{\"oid\":3082561436,\"bustype\":82,\"requestid\":\"e4c89cb3-fa87-4234-8b8d-398fe6768c5b\",\"islogin\":1,\"auth\":\"B343089879D688D2A1E94652A6BFBEBC3BF732B89C9147D45B3A8A8316AA1549\",\"title\":\"Use Car Pay\",\"amount\":1151}", "extend": "{\"useEType\":1,\"subPayType\":0,\"payTypeList\":0,\"subPayTypeList\":0,\"IsNeedPreAuth\":false,\"isRealTimePay\":1,\"insuranceinfos\":[],\"needPreAuth\":false}", "sign": "2086a79b12b73fdb47be6c68cb3a1027"}, "payAmountInfo": {"ctripInsuranceAmountInfos": []}, "ctripInsuranceOrderList": [], "lastEnablePayTime": 1629114014008, "unpaidMessage": "订单已提交，订单号3082561436，请客户29min内支付，最晚支付时间2021-08-16 19:40，未支付订单将自动取消"}}