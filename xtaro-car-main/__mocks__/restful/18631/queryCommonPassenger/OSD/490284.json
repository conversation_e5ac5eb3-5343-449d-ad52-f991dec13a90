{"passengerList": [{"nationalityName": "中国", "lastName": "WANG", "firstName": "ZONGGUO", "age": 13, "nationality": "", "mobile": "19900066666", "countryCode": "86", "isDefault": true, "isSelf": false, "sortExt": "isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0", "isRecommend": true, "fullName": "", "birthday": "1985-08-29", "passengerId": "109637998", "certificateList": [{"certificateType": "1", "certificateNo": "220283198508297351"}], "email": "", "isCreditQualified": false}, {"nationalityName": "台湾", "lastName": "CTRIP", "firstName": "TEST", "age": 33, "nationality": "TW", "mobile": "15444444444", "countryCode": "86", "isDefault": false, "isSelf": false, "sortExt": "isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0", "isRecommend": true, "fullName": "测试", "birthday": "1990-01-01", "passengerId": "109639258", "certificateList": [], "email": "", "isCreditQualified": false}, {"nationalityName": "中国", "lastName": "CUI", "firstName": "XIUZHI", "age": 59, "nationality": "CN", "mobile": "13699999999", "countryCode": "86", "isDefault": false, "isSelf": false, "sortExt": "isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0", "isRecommend": true, "fullName": "崔秀芝", "birthday": "1964-03-02", "passengerId": "109637999", "certificateList": [{"certificateType": "1", "certificateNo": "110225196403026127"}], "email": "", "isCreditQualified": false}, {"nationalityName": "", "lastName": "FANG", "firstName": "HANJIA", "nationality": "", "mobile": "15000000009", "countryCode": "86", "isDefault": false, "isSelf": false, "sortExt": "isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:0|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0", "fullName": "放寒假", "birthday": "", "passengerId": "109638604", "certificateList": [{"certificateType": "8", "certificateNo": "IU374"}], "email": "", "isCreditQualified": false}, {"nationalityName": "", "lastName": "TE", "firstName": "SITEGEN", "nationality": "", "mobile": "15000000000", "countryCode": "86", "isDefault": false, "isSelf": false, "sortExt": "isCompleteSort:-1|vendorCarTypeSort:-1|zhimaSort:0|ageSort:0|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0", "fullName": "特斯特根", "birthday": "", "passengerId": "109630213", "certificateList": [{"certificateType": "2", "certificateNo": "1D5EE"}, {"certificateType": "8", "certificateNo": "WDHH274"}], "email": "", "isCreditQualified": false}, {"nationalityName": "中国", "lastName": "", "firstName": "", "age": 59, "nationality": "CN", "mobile": "", "countryCode": "86", "isDefault": false, "isSelf": false, "sortExt": "isCompleteSort:0|vendorCarTypeSort:-1|zhimaSort:0|ageSort:-1|selectedSort:0|orderSort:0|selfSort:0|requestPassenger:0", "isRecommend": true, "fullName": "说的话", "birthday": "1964-03-02", "passengerId": "109639721", "certificateList": [{"certificateType": "1", "certificateNo": "110225196403026127"}], "email": "", "isCreditQualified": false}], "maxAge": 90, "minAge": 18, "tips": [{"content": "请您仔细阅读并理解以下内容", "style": "title"}, {"content": "您已知晓您录入的驾驶员身份证件信息，将用于您预订的租车产品，并在使用时根据有关法律规定进行查验或留存，请确保录入信息真实有效。携程将通过加密等方式保护此信息，并在预订过程中提供给为您实际提供服务的租车公司。\n如您未开通实名认证，但选择免押服务，您的驾驶员身份证件将被用于实名认证。", "style": "content"}]}