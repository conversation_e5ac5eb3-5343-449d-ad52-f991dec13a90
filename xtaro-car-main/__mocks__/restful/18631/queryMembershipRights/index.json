{"baseResponse": {"isSuccess": true, "code": "200", "requestId": "70a608f7-fed0-4f54-9f25-e401cac905f3", "extMap": {}, "apiResCodes": [], "hasResult": false, "errorCode": "200", "message": ""}, "ResponseStatus": {"Timestamp": "/Date(1640229185349+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "6921794846881355987"}, {"Id": "RootMessageId", "Value": "100025527-0a063db3-455619-59115"}]}, "curLevelCode": "35", "curLevelName": "普通会员", "curBasicPoints": 848, "membershipCard": 0, "growUpUrl": "https://m.fat30.qa.nt.ctripcorp.com/webapp/member/growup?isHideNavBar=YES&popup=close", "membershipCardUrl": "https://m.fat222.qa.nt.ctripcorp.com/webapp/supermember/landing", "rights": {"title": {"stringObjs": [{"content": "尊敬的普通会员"}]}, "subTitle": {"stringObjs": [{"content": "您已解锁", "style": "0"}, {"content": "2", "style": "1"}, {"content": "项租车权益！", "style": "0"}]}, "items": [{"code": "PointsAcceleration", "title": "积分奖励", "subTitle": "普通会员", "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*ifN6S7AB1o8AAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", "status": 2, "url": "", "rightsInfo": {"title": {"stringObjs": [{"content": "积分奖励"}]}, "subTitle": [{"stringObjs": [{"content": "预订租车可享积分奖励，积分可用于支付页面积分抵现，或在携程-我的-我的积分页面兑换商品。", "style": "0"}]}, {"stringObjs": [{"content": "每单奖励积分=订单成交金额 x 15% x 会员等级系数", "style": "0"}]}, {"stringObjs": [{"content": "携程白银贵宾及以上贵宾可享积分1.2倍加速起", "style": "0"}]}], "progress": [{"title": "普通", "desc": "1.0倍", "code": "0", "status": 1, "sortNum": 1, "note": [{"stringObjs": [{"content": "您的会员等级是普通会员，尚不可享积分加速。"}]}, {"stringObjs": [{"content": "升级至白银贵宾可享1.2倍加速 "}, {"content": "升级攻略>", "url": "https://m.ctrip.com/webapp/member/growup?isHideNavBar=YES&popup=close"}]}]}, {"title": "白银", "desc": "1.2倍", "code": "5", "status": 0, "sortNum": 2}, {"title": "黄金", "desc": "1.5倍", "code": "10", "status": 0, "sortNum": 3}, {"title": "铂金", "desc": "1.8倍", "code": "20", "status": 0, "sortNum": 4}, {"title": "钻石", "desc": "2.0倍", "code": "30", "status": 0, "sortNum": 5}, {"title": "金钻", "desc": "2.5倍", "code": "35", "status": 0, "sortNum": 5}, {"title": "黑钻", "desc": "3.0倍", "code": "40", "status": 0, "sortNum": 6}], "note": [{"stringObjs": [{"content": "您尚未解锁该权益。"}, {"content": "开通超级会员解锁>", "url": "https://m.fat222.qa.nt.ctripcorp.com/webapp/supermember/landing"}]}], "content": [{"title": {"stringObjs": [{"content": "积分规则"}]}, "desc": [{"stringObjs": [{"content": "1.使用返现、优惠券等活动订单，优惠金额不参与携程积分计算；"}]}, {"stringObjs": [{"content": "2.使用积分抵现功能支付部分不参与携程积分计算；"}]}, {"stringObjs": [{"content": "3.积分将在行程完成且订单状态更新为完成状态后发放。"}]}]}]}}, {"code": "PointsRedemption", "title": "积分兑换", "subTitle": "普通会员", "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*5nJQTKcSbIgAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", "status": 2, "rightsInfo": {"title": {"stringObjs": [{"content": "积分兑换"}]}, "subTitle": [{"stringObjs": [{"content": "携程会员可使用积分兑换权益、商品等。", "style": "0"}]}], "content": [{"title": {"stringObjs": [{"content": "如何使用"}]}, "desc": [{"stringObjs": [{"content": "在携程-我的-我的积分页面可进行积分超值抵、积分兑好物、积分兑权益等操作。"}]}]}, {"title": {"stringObjs": [{"content": "兑换规则"}]}, "desc": [{"stringObjs": [{"content": "1.积分可兑商品请以我的积分页面为准；"}]}, {"stringObjs": [{"content": "2.每一笔携程积分的有效期为两年，未进行使用的积分将于两年后的当月月底过期。"}]}]}]}}, {"code": "VipDiscount", "title": "租车费95折", "subTitle": "黄金贵宾解锁", "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*S-QQQaz147QAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", "status": 2, "url": "", "rightsInfo": {"title": {"stringObjs": [{"content": "租车费95折起"}]}, "subTitle": [{"stringObjs": [{"content": "携程黄金及以上贵宾可享租车费95折起，会员等级越高折扣力度越大。", "style": "0"}]}], "progress": [{"title": "普通", "desc": "1.2倍", "code": "0", "status": 1, "sortNum": 1, "note": [{"stringObjs": [{"content": "您的会员等级是普通会员，尚未解锁该权益。"}]}, {"stringObjs": [{"content": "升级至黄金贵宾解锁 "}, {"content": "升级攻略>", "url": "https://m.ctrip.com/webapp/member/growup?isHideNavBar=YES&popup=close"}]}]}, {"title": "白银", "desc": "95折", "code": "5", "status": 0, "sortNum": 2}, {"title": "黄金", "desc": "95折", "code": "10", "status": 0, "sortNum": 3}, {"title": "铂金", "desc": "92折", "code": "20", "status": 0, "sortNum": 4}, {"title": "钻石", "desc": "9折", "code": "30", "status": 0, "sortNum": 5}, {"title": "金钻", "desc": "88折", "code": "35", "status": 0, "sortNum": 5}, {"title": "黑钻", "desc": "88折", "code": "40", "status": 0, "sortNum": 6}], "content": [{"title": {"stringObjs": [{"content": "如何使用"}]}, "desc": [{"stringObjs": [{"content": "预订租车时，选择带有等级会员标签的产品，例如“黄金贵宾95折”。"}]}, {"stringObjs": [{"url": "https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/member/useTip1.png"}]}]}, {"title": {"stringObjs": [{"content": "兑换规则"}]}, "desc": [{"stringObjs": [{"content": "1.以上优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；"}]}, {"stringObjs": [{"content": "2.优惠特权不可叠加使用，默认帮你使用力度最大的优惠。"}]}]}]}}, {"code": "XbuDiscount", "title": "租车费9折", "subTitle": "预定机/酒解锁", "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*S-QQQaz147QAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", "status": 2, "url": "", "rightsInfo": {"title": {"stringObjs": [{"content": "租车费9折"}]}, "subTitle": [{"stringObjs": [{"content": "有机票、酒店待出行订单的用户，预定租车时可享出行特惠租车费9折。", "style": "0"}]}], "content": [{"title": {"stringObjs": [{"content": "如何使用"}]}, "desc": [{"stringObjs": [{"content": "预订租车时，选择带有“机票用户专享”、“酒店用户专享”标签的产品。"}]}, {"stringObjs": [{"url": "https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/member/useTip2.png"}]}]}, {"title": {"stringObjs": [{"content": "兑换规则"}]}, "desc": [{"stringObjs": [{"content": "1.以上折扣优惠仅针对租车费，不含手续费等；"}]}, {"stringObjs": [{"content": "2.优惠特权不可叠加使用，默认帮你使用力度最大的优惠。"}]}]}], "note": [{"stringObjs": [{"content": "您尚未解锁该权益，预订机票、酒店之后即可解锁"}]}, {"stringObjs": [{"content": "您尚未解锁该权益，预订机票、酒店之后即可解锁"}]}]}}, {"code": "SvipCoupon", "title": "免一日租金", "subTitle": "超级会员", "icon": "https://mdn.alipay.com/wsdk/img?fileid=A*S-QQQaz147QAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", "status": 0, "rightsInfo": {"title": {"stringObjs": [{"content": "免一日租金"}]}, "subTitle": [{"stringObjs": [{"content": "超级会员经典年卡用户在有效期内享有1次境内租车减免一日租金（最高优惠100元）权益，租期满48小时可用。", "style": "0"}]}], "content": [{"title": {"stringObjs": [{"content": "如何使用"}]}, "desc": [{"stringObjs": [{"content": "通过携程-租车-国内租车频道，预订带有“超级会员专享立减”标签的与付租车产品（及在线支付租车产品，可通过筛选“超级会员专享”查看），且租期满48小时，选择“超级会员”标签的优惠券，可享境内租车减免一日的日平均租车费用（最高优惠100元）权益（优惠通过立减优惠券实现，日平均租车费用见商品列表页面展示）。"}]}]}, {"title": {"stringObjs": [{"content": "兑换规则"}]}, "desc": [{"stringObjs": [{"content": "1.该权益仅限预订境内租车产品时使用（不含中国港澳台及境外租车产品）；"}]}, {"stringObjs": [{"content": "2.该权益可减免一日的日平均租车费用，且只适用于租车费减免，不包含保险、手续费、异地还车费等其他费用；"}]}, {"stringObjs": [{"content": "3.有效期内同一超级会员用户每单仅限使用一张优惠券，不可与其他优惠叠加；"}]}, {"stringObjs": [{"content": "4.部分城市租车产品不适用，具体以租车列表页“超级会员”标签展示产品为准。"}]}]}], "note": [{"stringObjs": [{"content": "您尚未解锁该权益。"}, {"content": "开通超级会员解锁>", "url": "https://m.fat222.qa.nt.ctripcorp.com/webapp/supermember/landing"}]}]}}], "desc": {"stringObjs": [{"content": "优惠特权不可叠加使用，预订时将默认帮您使用最大优惠"}]}}, "welfare": {"title": "会员福利", "items": [{"code": "PointsEqualsToCash", "title": "积分抵现金", "subTitle": "普通会员", "status": 1, "background": "https://mdn.alipay.com/wsdk/img?fileid=A*cbWpSYBf9uIAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", "url": "", "welfareInfoInfo": {"title": {"stringObjs": [{"content": "下单支付使用积分抵现"}]}, "subTitle": [{"stringObjs": [{"content": "您当前的会员等级为", "style": "0"}, {"content": "普通会员", "style": "1"}, {"content": "，每单最高可用积分抵扣10元。", "style": "0"}]}], "progress": [{"title": "普通", "desc": "￥10", "code": "0", "status": 2, "sortNum": 1}, {"title": "白银", "desc": "￥10", "code": "5", "status": 2, "sortNum": 2}, {"title": "黄金", "desc": "￥10", "code": "10", "status": 1, "sortNum": 3}, {"title": "铂金", "desc": "￥15", "code": "20", "status": 0, "sortNum": 4}, {"title": "钻石", "desc": "￥15", "code": "30", "status": 0, "sortNum": 5}, {"title": "金钻", "desc": "￥20", "code": "35", "status": 0, "sortNum": 5}, {"title": "黑钻", "desc": "￥20", "code": "40", "status": 0, "sortNum": 6}], "content": [{"title": {"stringObjs": [{"content": "如何使用"}]}, "desc": [{"stringObjs": [{"content": "支付租车订单时，选择“积分抵现”支付方式进行积分抵扣，250积分 = 1元。"}]}, {"stringObjs": [{"url": "https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/member/pointsBg.png"}]}]}, {"title": {"stringObjs": [{"content": "规则说明"}]}, "desc": [{"stringObjs": [{"content": "1. 订单不足100元或积分不足250时，不可使用积分抵现；"}]}, {"stringObjs": [{"content": "2. 实际抵扣金额不得超过订单金额的10%；"}]}, {"stringObjs": [{"content": "3. 携程积分不可与第三方支付进行混合支付；"}]}, {"stringObjs": [{"content": "4. 订单中如含积分部分，取消时将返还至携程积分账户；"}]}, {"stringObjs": [{"content": "5. 携程积分抵扣部分不开具发票。"}]}]}]}}, {"code": "VipBirthdayCoupon", "title": "生日月领租车券", "subTitle": "钻石贵宾解锁", "status": 0, "background": "https://mdn.alipay.com/wsdk/img?fileid=A*6m9iS7e8rdwAAAAAAAAAAAAADsN1AQ&bz=alsc_crmhome&zoom=.png", "url": "", "welfareInfoInfo": {"title": {"stringObjs": [{"content": "生日当月免费领租车券"}]}, "subTitle": [{"stringObjs": [{"content": "钻石及以上贵宾可领租车券，¥50起", "style": "0"}]}], "progress": [{"title": "普通", "desc": "", "code": "0", "status": 1, "sortNum": 1, "note": [{"stringObjs": [{"content": "您的会员等级是普通会员，尚未解锁该福利。"}]}, {"stringObjs": [{"content": "升级至钻石贵宾解锁 "}, {"content": "升级攻略>", "url": "https://m.ctrip.com/webapp/member/growup?isHideNavBar=YES&popup=close"}]}]}, {"title": "白银", "desc": "", "code": "5", "status": 0, "sortNum": 2}, {"title": "黄金", "desc": "", "code": "10", "status": 0, "sortNum": 3}, {"title": "铂金", "desc": "", "code": "20", "status": 0, "sortNum": 4}, {"title": "钻石", "desc": "¥50", "code": "30", "status": 0, "sortNum": 5}, {"title": "金钻", "desc": "¥70", "code": "35", "status": 0, "sortNum": 5}, {"title": "黑钻", "desc": "¥100", "code": "40", "status": 0, "sortNum": 6}], "content": [{"title": {"stringObjs": [{"content": "规则说明"}]}, "desc": [{"stringObjs": [{"content": "1.该权益仅限预订境内租车产品时使用（不含中国港澳台及境外租车产品）；"}]}, {"stringObjs": [{"content": "2.该权益可减免一日的日平均租车费用，且只适用于租车费减免，不包含保险、手续费、异地还车费等其他费用；"}]}, {"stringObjs": [{"content": "3.有效期内同一超级会员用户每单仅限使用一张优惠券，不可与其他优惠叠加；"}]}, {"stringObjs": [{"content": "4.部分城市租车产品不适用，具体以租车列表页“超级会员”标签展示产品为准；"}]}, {"stringObjs": [{"content": "5.该活动仅限携程APP可领取。"}]}]}]}}]}}