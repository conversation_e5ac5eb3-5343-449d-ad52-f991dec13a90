{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success"}, "ResponseStatus": {"Timestamp": "/Date(1675759605501+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "2741921902018749474"}, {"Id": "RootMessageId", "Value": "100025527-0a068a23-465488-255579"}]}, "allVehicleCount": 7, "allVendorPriceCount": 7, "vehicleList": [{"brandEName": "捷达", "brandName": "捷达", "name": "捷达VS5", "zhName": "捷达VS5", "vehicleCode": "5068", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.4T", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "vehiclesSetId": "10"}, {"brandEName": "大众", "brandName": "大众", "name": "大众途铠", "zhName": "大众途铠", "vehicleCode": "4861", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoBackUp": false, "vehiclesSetId": "10"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "4663", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 1, "displacement": "2.4L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "2015款及以前", "imageList": ["0"], "isSpecialized": true, "isHot": true, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "44"}, {"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰迈锐宝XL", "zhName": "雪佛兰迈锐宝XL", "vehicleCode": "4912", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.3T", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": false, "vehiclesSetId": "81"}, {"brandEName": "奥迪", "brandName": "奥迪", "name": "奥迪Q2L", "zhName": "奥迪Q2L", "vehicleCode": "4883", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "displacement": "1.4T", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": true, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "carPhone": true, "autoBackUp": true, "vehiclesSetId": "10"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田埃尔法", "zhName": "丰田埃尔法", "vehicleCode": "4805", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 1, "displacement": "2.5L", "struct": "", "fuel": "92号", "driveMode": "前置四驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 4, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "46"}, {"brandEName": "宝马", "brandName": "宝马", "name": "宝马X6", "zhName": "宝马X6", "vehicleCode": "280", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "3.0T", "struct": "", "fuel": "95号", "driveMode": "前置四驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "10"}], "productGroups": [{"groupCode": "all", "groupName": "全部车型", "sortNum": -4, "productList": [{"vehicleCode": "5068", "sortNum": 0, "lowestPrice": 300, "highestPrice": 300, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 2.533, "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30466_0_133340_116805"]}, "minTPrice": 1715.0, "minDPrice": 300, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 1, "isEasy": true, "isCredit": true, "pWay": "", "recommendType": "r1", "recommendInfo": "03月20日 08:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4861", "sortNum": 1, "lowestPrice": 520, "highestPrice": 520, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 2.533, "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30466_0_133340_116805"]}, "minTPrice": 2275.0, "minDPrice": 520, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 1, "isOptim": false, "isEasy": false, "isCredit": true, "pWay": "", "recommendType": "r1", "recommendInfo": "03月20日 08:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4663", "sortNum": 2, "lowestPrice": 600, "highestPrice": 600, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 2.533, "reactId": "**********", "group": 870, "groupSort": 1, "scoreSort": 0.0, "hot": 1, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30466_0_133340_116805"]}, "minTPrice": 2550.0, "minDPrice": 600, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 1, "isOptim": false, "isEasy": false, "isCredit": true, "pWay": "", "recommendType": "r1", "recommendInfo": "03月20日 08:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4912", "sortNum": 3, "lowestPrice": 600, "highestPrice": 600, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 2.533, "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30466_0_133340_116805"]}, "minTPrice": 2725.0, "minDPrice": 600, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 1, "isOptim": false, "isEasy": false, "isCredit": true, "pWay": "", "recommendType": "r1", "recommendInfo": "03月20日 08:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4883", "sortNum": 4, "lowestPrice": 620, "highestPrice": 620, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 2.533, "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 1, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30466_0_133340_116805"]}, "minTPrice": 2775.0, "minDPrice": 620, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 1, "isOptim": false, "isEasy": false, "isCredit": true, "pWay": "", "recommendType": "r1", "recommendInfo": "03月20日 08:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4805", "sortNum": 5, "lowestPrice": 880, "highestPrice": 880, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 2.533, "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30466_0_133340_116805"]}, "minTPrice": 3780.0, "minDPrice": 880, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 1, "isOptim": false, "isEasy": false, "isCredit": true, "pWay": "", "recommendType": "r1", "recommendInfo": "03月20日 08:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "280", "sortNum": 6, "lowestPrice": 1100, "highestPrice": 1100, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 2.533, "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30466_0_133340_116805"]}, "minTPrice": 4505.0, "minDPrice": 1100, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 1, "isOptim": false, "isEasy": false, "isCredit": true, "pWay": "", "recommendType": "r1", "recommendInfo": "03月20日 08:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}], "dailyPrice": 300, "minTotalPrice": 1715.0, "hasResult": true}, {"groupCode": "3", "groupName": "舒适轿车", "sortNum": 3, "allowMerge": true, "dailyPrice": 600, "minTotalPrice": 2725.0, "hasResult": true}, {"groupCode": "6", "groupName": "SUV", "sortNum": 4, "allowMerge": true, "dailyPrice": 300, "minTotalPrice": 1715.0, "hasResult": true}, {"groupCode": "4", "groupName": "商务车", "sortNum": 5, "allowMerge": true, "dailyPrice": 600, "minTotalPrice": 2550.0, "hasResult": true}], "recommendProductInfo": [{"recommendType": "r1", "recommendVehicles": [{"vehicleCode": "5068"}, {"vehicleCode": "4861"}, {"vehicleCode": "4663"}, {"vehicleCode": "4912"}, {"vehicleCode": "4883"}, {"vehicleCode": "4805"}, {"vehicleCode": "280"}], "recommendation": {"tilte": "暂无符合要求的车辆哦", "subTitle": "建议您修改取还车条件", "recTitle": "以下为满足您部分要求的车辆", "recMessage": "建议您修改取还车时间，有车型可预订", "pickUpAvailableTime": "2023-03-17 08:00:00", "returnAvailableTime": "2023-03-20 08:00:00"}, "subStrategyType": 2}], "storeList": [{"storeCode": "133340", "pickUpLevel": -1, "pickOffLevel": 55175}]}