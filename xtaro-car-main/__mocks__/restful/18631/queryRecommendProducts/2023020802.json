{"allVehicleCount": 10, "allVendorPriceCount": 11, "baseResponse": {"code": "200", "returnMsg": "success", "isSuccess": true}, "ResponseStatus": {"Extension": [{"Value": "7291460570665824870", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a04347c-465511-44584", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1675842669910+0800)/"}, "uniqSign": "1212121212121212", "vehicleList": [{"transmissionName": "自动挡", "displacement": "1.4T", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "1081", "carPhone": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "大众帕萨特", "zhName": "大众帕萨特", "brandName": "大众", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "3", "vehiclesSetId": "81", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "大众", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.5T", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "4764", "carPhone": true, "autoStart": true, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": true, "name": "上汽大通MAXUS G50", "zhName": "上汽大通MAXUS G50", "brandName": "上汽大通MAXUS", "passengerNo": 7, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0"], "groupCode": "4", "vehiclesSetId": "44", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "上汽大通MAXUS", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.8T", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "1082", "fuel": "95号、92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "大众帕萨特", "zhName": "大众帕萨特", "brandName": "大众", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "3", "vehiclesSetId": "81", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "大众", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "license": ""}, {"transmissionName": "自动挡", "displacement": "1.5L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "4870", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "大众桑塔纳", "zhName": "大众桑塔纳", "brandName": "大众", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "大众", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.5L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "3601", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "雪佛兰乐风RV", "zhName": "雪佛兰乐风RV", "brandName": "雪佛兰", "passengerNo": 5, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放2个24寸行李箱", "luggageNo": 2, "brandEName": "雪佛兰", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.0T-1.5L", "realityImageUrl": "0", "isHot": false, "imageList": ["0"], "carPhone": true, "vehicleCode": "4139", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "brandName": "雪佛兰", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放2个24寸行李箱", "luggageNo": 2, "brandEName": "雪佛兰", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.5L-1.6L", "realityImageUrl": "0", "isHot": false, "imageList": ["0"], "autoStart": false, "vehicleCode": "5579", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "大众捷达", "zhName": "大众捷达", "brandName": "大众", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "大众", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.4T", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "5141", "carPhone": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": true, "name": "捷达VS7", "zhName": "捷达VS7", "brandName": "捷达", "passengerNo": 5, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "6", "vehiclesSetId": "10", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "捷达", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.4T-2.0T", "realityImageUrl": "0", "isHot": false, "imageList": ["0"], "carPhone": true, "vehicleCode": "4781", "fuel": "95号、98号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置四驱、前置前驱", "autoBackUp": true, "name": "大众探岳", "zhName": "大众探岳", "brandName": "大众", "passengerNo": 5, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "groupCode": "6", "vehiclesSetId": "10", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "大众", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "license": ""}, {"transmissionName": "自动挡", "displacement": "1.5T", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "4758", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": true, "name": "广汽传祺传祺GM6", "zhName": "广汽传祺传祺GM6", "brandName": "广汽传祺", "passengerNo": 7, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0"], "groupCode": "4", "vehiclesSetId": "44", "luggageNum": "可放2个24寸行李箱", "luggageNo": 2, "brandEName": "广汽传祺", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "license": "", "autoPark": false}], "storeList": [{"pickOffLevel": 51011, "storeCode": "116720", "pickUpLevel": 51011}, {"pickOffLevel": 10240, "storeCode": "114197", "pickUpLevel": 10240}], "recommendProductInfo": [{"recommendation": {"tilte": "暂无更多符合要求的车辆哦", "recTitle": "以下为满足您部分要求的车辆", "subTitle": "建议您修改取还车条件", "pickUpAvailableTime": "2023-02-24 10:00:00", "returnAvailableTime": "2023-02-27 10:00:00", "recMessage": "建议您修改取还车时间，有车型可预订"}, "recommendType": "r1", "subStrategyType": 1, "recommendVehicles": [{"vehicleCode": "1081"}, {"vehicleCode": "4764"}, {"vehicleCode": "1082"}, {"vehicleCode": "4870"}, {"vehicleCode": "3601"}, {"vehicleCode": "4139"}, {"vehicleCode": "5579"}, {"vehicleCode": "5141"}, {"vehicleCode": "4781"}, {"vehicleCode": "4758"}]}], "productGroups": [{"sortNum": -4, "productList": [{"groupSort": 0, "lowestPrice": 150, "modifySameVehicle": false, "pWay": "", "vehicleCode": "1081", "highestPrice": 349, "minDPrice": 150, "hot": 0, "minTPrice": 1005, "lowestDistance": 34.2781, "group": 0, "recommendType": "r1", "sortNum": 0, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD71043_0_116720_116720"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "recommendInfo": "可租时间 ：02月24日 10:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 2}, {"groupSort": 0, "lowestPrice": 158, "modifySameVehicle": false, "pWay": "", "vehicleCode": "4764", "highestPrice": 158, "minDPrice": 158, "hot": 0, "minTPrice": 1029, "lowestDistance": 34.2781, "group": 0, "recommendType": "r1", "sortNum": 1, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD71043_0_116720_116720"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "recommendInfo": "可租时间 ：02月24日 10:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1}, {"groupSort": 0, "lowestPrice": 150, "modifySameVehicle": false, "pWay": "", "vehicleCode": "1082", "highestPrice": 150, "minDPrice": 150, "hot": 0, "minTPrice": 1035, "lowestDistance": 34.2781, "group": 0, "recommendType": "r1", "sortNum": 2, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD71043_0_116720_116720"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "recommendInfo": "可租时间 ：02月24日 10:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 199, "modifySameVehicle": false, "pWay": "", "vehicleCode": "4870", "highestPrice": 199, "minDPrice": 199, "hot": 0, "minTPrice": 1072, "lowestDistance": 41.3776, "group": 0, "recommendType": "r1", "sortNum": 3, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD33993_0_114197_114197"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "recommendInfo": "可租时间 ：02月24日 10:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 199, "modifySameVehicle": false, "pWay": "", "vehicleCode": "3601", "highestPrice": 199, "minDPrice": 199, "hot": 0, "minTPrice": 1072, "lowestDistance": 41.3776, "group": 0, "recommendType": "r1", "sortNum": 4, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD33993_0_114197_114197"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "recommendInfo": "可租时间 ：02月24日 10:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 199, "modifySameVehicle": false, "pWay": "", "vehicleCode": "4139", "highestPrice": 199, "minDPrice": 199, "hot": 0, "minTPrice": 1072, "lowestDistance": 41.3776, "group": 0, "recommendType": "r1", "sortNum": 5, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD33993_0_114197_114197"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "recommendInfo": "可租时间 ：02月24日 10:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 199, "modifySameVehicle": false, "pWay": "", "vehicleCode": "5579", "highestPrice": 199, "minDPrice": 199, "hot": 0, "minTPrice": 1072, "lowestDistance": 41.3776, "group": 0, "recommendType": "r1", "sortNum": 6, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD33993_0_114197_114197"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "recommendInfo": "可租时间 ：02月24日 10:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 249, "modifySameVehicle": false, "pWay": "", "vehicleCode": "5141", "highestPrice": 249, "minDPrice": 249, "hot": 0, "minTPrice": 1232, "lowestDistance": 41.3776, "group": 0, "recommendType": "r1", "sortNum": 7, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD33993_0_114197_114197"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "recommendInfo": "可租时间 ：02月24日 10:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 206, "modifySameVehicle": false, "pWay": "", "vehicleCode": "4781", "highestPrice": 206, "minDPrice": 206, "hot": 0, "minTPrice": 1293, "lowestDistance": 34.2781, "group": 0, "recommendType": "r1", "sortNum": 8, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD71043_0_116720_116720"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "recommendInfo": "可租时间 ：02月24日 10:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 349, "modifySameVehicle": false, "pWay": "", "vehicleCode": "4758", "highestPrice": 349, "minDPrice": 349, "hot": 0, "minTPrice": 1592, "lowestDistance": 41.3776, "group": 0, "recommendType": "r1", "sortNum": 9, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD33993_0_114197_114197"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "recommendInfo": "可租时间 ：02月24日 10:00", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}], "hasResult": true, "groupCode": "all", "groupName": "全部车型", "dailyPrice": 150, "minTotalPrice": 1005}, {"sortNum": 0, "hasResult": true, "groupCode": "2", "groupName": "经济轿车", "dailyPrice": 199, "allowMerge": true, "minTotalPrice": 1072}, {"sortNum": 3, "hasResult": true, "groupCode": "3", "groupName": "舒适轿车", "dailyPrice": 150, "allowMerge": true, "minTotalPrice": 1005}, {"sortNum": 4, "hasResult": true, "groupCode": "6", "groupName": "SUV", "dailyPrice": 249, "allowMerge": true, "minTotalPrice": 1232}, {"sortNum": 5, "hasResult": true, "groupCode": "4", "groupName": "商务车", "dailyPrice": 158, "allowMerge": true, "minTotalPrice": 1029}], "appResponseMap": {"isFromCache": false, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&modify=&orderId=&pickupPointInfo=2023-02-23 22:00:00|清徐火车站|21786|37.557692|112.313174|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=all&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2023-02-26 10:00:00|清徐火车站|21786|37.557692|112.313174|||&sortType=1&uid=@@PAGENUM@@1@@RECOMMEND@@", "groupId": "18631/queryRecommendProducts", "networkCost": 218, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 218, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1675842669775, "afterFetch": 1675842669993}}