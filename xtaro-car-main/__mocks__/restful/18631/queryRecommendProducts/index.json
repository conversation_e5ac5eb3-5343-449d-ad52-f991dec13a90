{"baseResponse": {"isSuccess": true, "code": "200", "requestId": "523bd630-6ebb-85c5-c562-157140810392"}, "ResponseStatus": {"Timestamp": "/Date(1661760093108+0800)/", "Ack": "Success", "Errors": []}, "allVehicleCount": 5, "allVendorPriceCount": 30, "vehicleList": [{"brandId": 0, "brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "14415", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济型", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.0T-1.5L", "fuel": "汽油92号", "gearbox": "手自一体变速箱(AT)", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0415c120009sl0u8w4A82.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg", "https://dimg04.c-ctrip.com//images/0410u120008at350uDF48.jpg", "https://dimg04.c-ctrip.com//images/0414c120008at2ypz0D5C.jpg", "https://dimg04.c-ctrip.com//images/0412t120008at3ett66A0.jpg", "https://dimg04.c-ctrip.com//images/0414w120008at5huo1DE9.jpg"], "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png", "oilType": 5}, {"brandId": 0, "brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "115373", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 3, "displacement": "2.0T", "struct": "MPV", "style": "2020款", "imageList": ["https://dimg04.c-ctrip.com/images/0AS5m120005bsqnn1203E.png"], "isSpecialized": true, "isHot": false, "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0412s120008n38gqgE28F.png", "oilType": 5}, {"brandId": 0, "brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "115678", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 3, "displacement": "2.0T", "struct": "MPV", "gearbox": "手自一体", "driveMode": "前置前驱", "style": "2021款", "imageList": ["//dimg04.c-ctrip.com/images/0AS0b120008crt1ow183F.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0414b120008n38vaiECF9.jpg", "https://dimg04.c-ctrip.com/images/0412p120009sj2ncyFB6F.jpg", "https://dimg04.c-ctrip.com/images/0414b120009sj2nl9E013.jpg"], "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04170120009sl188f1B79.png", "oilType": 5}, {"brandId": 0, "brandEName": "宝马", "brandName": "宝马", "name": "宝马2系", "zhName": "宝马2系", "vehicleCode": "10306", "groupCode": "9", "groupSubClassCode": "", "groupName": "跑车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 4, "doorNo": 2, "luggageNo": 1, "displacement": "1.5T-2.0T", "style": "", "imageList": ["//dimg04.c-ctrip.com/images/0AS011200081znf9c35EF.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/04150120008ascen5A830.jpg", "https://dimg04.c-ctrip.com//images/0414d120008as84c85BD7.jpg", "https://dimg04.c-ctrip.com//images/04128120008asb9ag7294.jpg"], "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0416n120009soq8623E12.png", "oilType": 5}, {"brandId": 0, "brandEName": "大众", "brandName": "大众", "name": "大众朗逸", "zhName": "大众朗逸", "vehicleCode": "23084", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.2T-1.6L", "fuel": "92号汽油", "gearbox": "六挡手自一体", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz07cn162bh8durkg9FD9.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0412c120008at0a8i1635.jpg", "https://dimg04.c-ctrip.com//images/0415z120008at0prs5FA0.jpg", "https://dimg04.c-ctrip.com//images/0414c120008at0f4bBE30.jpg", "https://dimg04.c-ctrip.com//images/0415s120008aszpue57EF.jpg", "https://dimg04.c-ctrip.com//images/04146120008asy2d38073.jpg", "https://dimg04.c-ctrip.com//images/0411x120008asy5knE2C1.jpg", "https://dimg04.c-ctrip.com//images/0411t120008at0xcoA878.jpg", "https://dimg04.c-ctrip.com//images/0416y120008asxf6u5D56.jpg"], "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04169120009sl1081CCDC.png", "oilType": 5}], "recommendProductInfo": [{"recommendType": "r1", "recommendVehicles": [{"vehicleCode": "14415"}, {"vehicleCode": "115678"}, {"vehicleCode": "115373"}], "recommendation": {"tilte": "暂无符合要求的车辆哦", "subTitle": "建议您修改取还车条件", "recTitle": "以下为满足您部分要求的车辆", "recMessage": "以下为距取车地较远的门店，可能需要到店取车", "pickUpAvailableTime": "2022-09-13 10:00:00", "returnAvailableTime": "2022-09-15 10:00:00"}}, {"recommendType": "r2", "recommendVehicles": [{"vehicleCode": "10306", "availableLocation": "亚龙湾客运东站", "longitude": "18.302277", "latitude": "109.603911"}]}], "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"recommendType": "r1", "recommendInfo": "8月31日 12:00", "vehicleCode": "14415", "sortNum": 0, "group": 0, "minTPrice": 176, "minDPrice": 48, "outTags": [{"title": "押金双免", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "防疫安心行", "category": 2, "type": 1, "code": "10", "sortNum": 35, "colorCode": "2", "labelCode": "3764", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "priceSize": 25, "isEasy": true, "isCredit": true}, {"recommendType": "r3", "recommendInfo": "8月31日 12:00", "vehicleCode": "115678", "sortNum": 1, "group": 10002, "minTPrice": 176, "minDPrice": 48, "outTags": [{"title": "押金双免", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "防疫安心行", "category": 2, "type": 1, "code": "10", "sortNum": 35, "colorCode": "2", "labelCode": "3764", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "priceSize": 36, "isEasy": true, "isCredit": false, "vendorPriceList": [{"fees": [{"amount": 548, "detail": [{"code": "1001", "amount": 598, "amountDesc": "¥598", "name": "租车费"}, {"code": "3776", "amount": 50, "amountDesc": "¥50", "name": "夏日特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥548", "originalDailyPrice": 598, "subAmount": 548, "name": "车辆租金", "amountStr": "¥548"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 628, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥678", "subAmount": 678, "name": "总价", "amountStr": "¥628"}], "reference": {"vendorCode": "62073", "bizVendorCode": "32073", "rStoreCode": "128311", "comPriceCode": "eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiMDgyMjIwMjIxMTEzMzU2NWYyYjE4Ny03OGE2LTQ0OWItYWE1ZC02ZWYzNzA3MWE3YzYifQ==", "priceVersion": "AWYB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXB0f416KgGc/DKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlUkLATQm+UuHfGn4PGmJgBeT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cFdI3+HGquFjlmD7Fjn1+IP2ulGW6xAydPgjkz2OrAq5QdTVBe/z8CZdysrPA+i/kN7gUtHiwtEIu6xi41ENDX9tTu24C4IkU7T331tWsN4YaMqGAFcV3TTs7sx5Ik0exxlzlC4wQDBFc6mqeALM80MHBRuJlQ0JB1jBK+HXLpuwX+dMipsLRduphaKLH4X+6", "packageType": 0, "pStoreCode": "128311", "vehicleCode": "107763", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0}, "isMinTPriceVendor": true}]}, {"recommendType": "r3", "recommendInfo": "8月31日 12:00", "vehicleCode": "115373", "sortNum": 2, "group": 10002, "minTPrice": 136, "minDPrice": 58, "outTags": [{"title": "押金双免", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 16, "isEasy": false, "isCredit": true, "vendorPriceList": [{"fees": [{"amount": 548, "detail": [{"code": "1001", "amount": 598, "amountDesc": "¥598", "name": "租车费"}, {"code": "3776", "amount": 50, "amountDesc": "¥50", "name": "夏日特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥548", "originalDailyPrice": 598, "subAmount": 548, "name": "车辆租金", "amountStr": "¥548"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 628, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥678", "subAmount": 678, "name": "总价", "amountStr": "¥628"}], "reference": {"vendorCode": "62073", "bizVendorCode": "32073", "rStoreCode": "128311", "comPriceCode": "eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiMDgyMjIwMjIxMTEzMzU2NWYyYjE4Ny03OGE2LTQ0OWItYWE1ZC02ZWYzNzA3MWE3YzYifQ==", "priceVersion": "AWYB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXB0f416KgGc/DKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlUkLATQm+UuHfGn4PGmJgBeT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cFdI3+HGquFjlmD7Fjn1+IP2ulGW6xAydPgjkz2OrAq5QdTVBe/z8CZdysrPA+i/kN7gUtHiwtEIu6xi41ENDX9tTu24C4IkU7T331tWsN4YaMqGAFcV3TTs7sx5Ik0exxlzlC4wQDBFc6mqeALM80MHBRuJlQ0JB1jBK+HXLpuwX+dMipsLRduphaKLH4X+6", "packageType": 0, "pStoreCode": "128311", "vehicleCode": "107763", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0}, "isMinTPriceVendor": true}]}, {"recommendType": "r2", "recommendInfo": "亚龙湾客运东站", "vehicleCode": "10306", "sortNum": 0, "group": 0, "minTPrice": 736, "minDPrice": 600, "outTags": [{"title": "押金双免", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "防疫安心行", "category": 2, "type": 1, "code": "10", "sortNum": 35, "colorCode": "2", "labelCode": "3764", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "priceSize": 16, "isEasy": false, "isCredit": true}, {"recommendType": "r3", "recommendInfo": "3.2公里", "vehicleCode": "23084", "sortNum": 2, "group": 0, "minTPrice": 436, "minDPrice": 78, "outTags": [{"title": "押金双免", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 16, "isEasy": true, "isCredit": true, "vendorPriceList": [{"fees": [{"amount": 548, "detail": [{"code": "1001", "amount": 598, "amountDesc": "¥598", "name": "租车费"}, {"code": "3776", "amount": 50, "amountDesc": "¥50", "name": "夏日特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥548", "originalDailyPrice": 598, "subAmount": 548, "name": "车辆租金", "amountStr": "¥548"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 628, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥678", "subAmount": 678, "name": "总价", "amountStr": "¥628"}], "reference": {"vendorCode": "62073", "bizVendorCode": "32073", "rStoreCode": "128311", "comPriceCode": "eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiMDgyMjIwMjIxMTEzMzU2NWYyYjE4Ny03OGE2LTQ0OWItYWE1ZC02ZWYzNzA3MWE3YzYifQ==", "priceVersion": "AWYB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXB0f416KgGc/DKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlUkLATQm+UuHfGn4PGmJgBeT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cFdI3+HGquFjlmD7Fjn1+IP2ulGW6xAydPgjkz2OrAq5QdTVBe/z8CZdysrPA+i/kN7gUtHiwtEIu6xi41ENDX9tTu24C4IkU7T331tWsN4YaMqGAFcV3TTs7sx5Ik0exxlzlC4wQDBFc6mqeALM80MHBRuJlQ0JB1jBK+HXLpuwX+dMipsLRduphaKLH4X+6", "packageType": 0, "pStoreCode": "128311", "vehicleCode": "107763", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0}, "isMinTPriceVendor": true}]}], "groupCode": "all", "dailyPrice": 48, "hasResult": true}, {"sortNum": 0, "groupName": "经济型", "hasResult": true, "groupCode": "2", "dailyPrice": 48}, {"sortNum": 0, "groupName": "商务车", "hasResult": true, "groupCode": "4", "dailyPrice": 48}, {"sortNum": 3, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 78}, {"sortNum": 5, "groupName": "跑车", "hasResult": true, "groupCode": "9", "dailyPrice": 600}], "storeList": [{"pickOffLevel": 0, "storeCode": "128311", "pickUpLevel": 0}]}