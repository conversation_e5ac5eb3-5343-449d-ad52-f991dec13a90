{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "780e2f8c-661e-4141-bc56-8b12bdda3602", "extraIndexTags": {"recType": "rec4", "isRec": "1"}}, "ResponseStatus": {"Timestamp": "/Date(1706168185779+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "1619396139600414126"}, {"Id": "RootMessageId", "Value": "921822-0a1951e8-473935-2234868"}]}, "allVehicleCount": 2, "allVendorPriceCount": 2, "vehicleList": [{"brandId": 98, "brandEName": "VW", "name": "大众 捷达 Jetta", "zhName": "大众 捷达 Jetta", "vehicleCode": "7218", "imageUrl": "https://dimg04.c-ctrip.com/images/0RV7012000c6s19eo1C03.png", "groupCode": "2", "groupSubClassCode": "14", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "imageList": ["https://dimg04.c-ctrip.com/images/0RV7012000c6s19eo1C03.png"], "userRealImageCount": 0, "isSpecialized": false, "hasConditioner": true, "conditionerDesc": "A/C", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2n12000c6s17ix2D81.jpg", "groupSubName": "标准型轿车"}, {"brandId": 43, "brandEName": "BMW", "name": "宝马 3系", "zhName": "宝马 3系", "vehicleCode": "6046", "imageUrl": "https://dimg04.c-ctrip.com/images/0RV4t12000c6ry7y6D6BB.png", "groupCode": "3", "groupSubClassCode": "33", "groupName": "豪华轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "imageList": ["https://dimg04.c-ctrip.com/images/0RV4t12000c6ry7y6D6BB.png"], "userRealImageCount": 0, "isSpecialized": false, "hasConditioner": true, "conditionerDesc": "A/C", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6r12000c6ryapj1A5F.jpg", "groupSubName": "高级轿车"}], "productGroups": [{"groupCode": "2", "groupName": "舒适轿车", "sortNum": 2, "productList": [{"vehicleCode": "7218", "sortNum": 0, "lowestPrice": 1089, "highestPrice": 1089, "maximumRating": 0, "maximumCommentCount": 0, "lowestDistance": 22.4287, "isSpecialized": false, "vendorPriceList": [{"vendorName": "SIXT", "vendorLogo": "https://dimg04.c-ctrip.com/images/0yc2v12000ba70fun1FE2.png", "commentInfo": {"level": "", "commentCount": 0, "overallRating": "0.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 1089, "currentOriginalDailyPrice": 0, "currentTotalPrice": 2177, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "deductInfos": []}, "reference": {"bizVendorCode": "14060007", "vendorCode": "SD0005", "pStoreCode": "170857", "rStoreCode": "170857", "vehicleCode": "7218", "packageId": "106284", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "1542906901|2024-01-25T07:36:18.422Z|e53bd1f0-e2a9-4d3e-b3a9-dc6f8dbc9332.315"}, "decoratorVendorType": 0, "isEasyLife": false, "payMode": 2, "bomCode": "170857_7218_CDW_FRFB_TP_TPL_ULM_0_0", "productCode": "4222244", "priceVersion": "ARgA8yDYGmjsSYABsuRj06g4YANAXX+pxuXx", "pCityId": 73, "rCityId": 73, "vendorVehicleCode": "IDAR_VOLKSWAGENJETTA", "age": 30, "unionCardFilter": {}, "noDepositFilter": {}, "skuId": 4222244, "sippCode": "IDAR", "kPSId": 170857, "kRSId": 170857, "kVId": 14060007, "klbVersion": 1, "kVehicleId": 7218, "pkgRuleId": 106284}, "promotions": [], "isSpecialized": false, "sortNum": 0, "vendorTag": {"title": "全球连锁", "sortNum": 0}, "pStoreRouteDesc": "机场内", "rStoreRouteDesc": "", "easyLifeInfo": {"isEasyLife": false}, "isBroker": true, "platformName": "Qunar.com", "platformCode": "P10", "allTags": [{"title": "24小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在24小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前%1$s小时可免费取消；%2$s小时内取消将扣除%3$s作为违约金；延迟取车或者提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款。", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含第三者保障", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}], "evaluation": {"title": "", "type": 1}, "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 512, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 1, "checkType": 0}], "reactId": "1536257400", "isPStoreSupportCdl": true, "isRStoreSupportCdl": true, "qualityScore": 0, "isSelect": false, "orignalPriceStyle": "WithStrikethrough", "distance": 22.4287, "extMap": {"freeCancel": "false", "isULM": "true", "isNoOnewayFee": "false", "distance": "22.4287", "confirmRightNow": "false", "isConfirmTimeGth12": "false", "isVendorActive": "0", "isThirdInsurance": "false", "isAsiaPickup": "true", "isSupportZhima": "false"}, "ctripVehicleCode": "7218", "priceTip": "请确认到达门店方式", "minAge": 26, "maxAge": 70}], "reactId": "1536257430", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14060007", "vendorName": "SIXT", "vendorLogo": "https://dimg04.c-ctrip.com/images/0yc2v12000ba70fun1FE2.png", "similarVehicleInfos": [{"vehicleCode": "7218", "vehicleName": "大众 捷达 Jetta", "vehicleImageUrl": "https://dimg04.c-ctrip.com/images/0RV7012000c6s19eo1C03.png"}]}], "vehicleRecommendProduct": {"introduce": "同组车型最低价", "productCodes": ["4222244"]}, "minTPrice": 2177, "recommendInfo": "距新加坡动物园22.4公里起"}], "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/standard.png", "allowMerge": true, "dailyPrice": 1089}, {"groupCode": "3", "groupName": "豪华轿车", "sortNum": 3, "productList": [{"vehicleCode": "6046", "sortNum": 1, "lowestPrice": 1165, "highestPrice": 1165, "maximumRating": 3.3, "maximumCommentCount": 1, "lowestDistance": 12.8893, "isSpecialized": false, "vendorPriceList": [{"vendorName": "<PERSON><PERSON>", "vendorLogo": "https://dimg04.c-ctrip.com/images/0411a12000bg4eqkg4784.png", "commentInfo": {"level": "", "commentCount": 1, "overallRating": "3.3", "maximumRating": 5, "commentLabel": "", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 1165, "currentOriginalDailyPrice": 0, "currentTotalPrice": 2330, "currentCurrencyCode": "CNY", "localCurrencyCode": "SGD", "deductInfos": []}, "reference": {"bizVendorCode": "14001", "vendorCode": "SD0003", "pStoreCode": "70319", "rStoreCode": "70319", "vehicleCode": "6046", "packageId": "84369", "packageType": 0, "vcExtendRequest": {}, "decoratorVendorType": 0, "isEasyLife": false, "payMode": 1, "bomCode": "70319_6046_CDW_FRFB_TP_TPL_0_0", "productCode": "2911631", "rateCode": "BEST", "priceVersion": "ARcAFauMWIOZqin/mbirZ5jQJADin3RH0BL5", "pCityId": 73, "rCityId": 73, "vendorVehicleCode": "PDAR_QBMW3SERIES", "age": 30, "unionCardFilter": {}, "noDepositFilter": {}, "skuId": 2911631, "sippCode": "PDAR", "kPSId": 70319, "kRSId": 70319, "kVId": 14001, "klbVersion": 1, "kVehicleId": 6046, "pkgRuleId": 84369}, "promotions": [], "isSpecialized": false, "sortNum": 0, "vendorTag": {"title": "国际知名", "sortNum": 0}, "pStoreRouteDesc": "距离新加坡动物园 12.9公里", "rStoreRouteDesc": "", "easyLifeInfo": {"isEasyLife": false}, "isBroker": false, "platformCode": "", "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车时间前可免费取消。", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "免费添加1人驾驶", "category": 2, "type": 1, "code": "2", "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "sortNum": 20, "colorCode": "2", "labelCode": "3556"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含第三者保障", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}], "evaluation": {"title": "", "type": 1}, "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 512, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 514, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 12, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}], "reactId": "1536257411", "isPStoreSupportCdl": true, "isRStoreSupportCdl": true, "qualityScore": 0, "storeScore": 3.3, "isSelect": false, "orignalPriceStyle": "WithStrikethrough", "distance": 12.8893, "extMap": {"freeCancel": "true", "isULM": "true", "isNoOnewayFee": "false", "distance": "12.8893", "confirmRightNow": "false", "isConfirmTimeGth12": "false", "isVendorActive": "0", "isThirdInsurance": "false", "isAsiaPickup": "false", "isSupportZhima": "false"}, "ctripVehicleCode": "6046", "priceTip": "请确认到达门店方式", "minAge": 25, "maxAge": 80}], "reactId": "1536257430", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14001", "vendorName": "<PERSON><PERSON>", "vendorLogo": "https://dimg04.c-ctrip.com/images/0411a12000bg4eqkg4784.png", "similarVehicleInfos": [{"vehicleCode": "6046", "vehicleName": "宝马 3系", "vehicleImageUrl": "https://dimg04.c-ctrip.com/images/0RV4t12000c6ry7y6D6BB.png"}]}], "vehicleRecommendProduct": {"introduce": "同组车型最低价", "productCodes": ["2911631"]}, "minTPrice": 2330, "recommendInfo": "距新加坡动物园12.9公里起"}], "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/luxury.png", "allowMerge": true, "dailyPrice": 1165}], "recommendProductInfo": [{"recommendType": "rec6", "recommendation": {"tilte": "暂无符合要求的车辆哦", "subTitle": "建议您修改取还车地点", "recTitle": "修改取还车条件", "recMessage": "以下取车门店距取车地点12.9公里起"}}], "licenseInfo": {"pickupLicenseDesc": "持中国大陆驾照可在美国租车", "noticeMsg": "", "pickupSupportCDLType": 1, "pickupCountryName": "美国", "returnCountryName": "美国", "returnSupportCDLType": 1, "returnLicenseDesc": "持中国大陆驾照可在美国租车"}, "productGroupsHashCode": "32daccd4b095c929aa04d8110250abd9ab160b10c49cd03bdb2a20e9b495f9676d3f6067", "filterMenuItems": [{"name": "品牌/配置", "code": "BrandAndAccessory", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"name": "座位数", "sortNum": 1, "groupCode": "SeatGroup", "bitwiseType": 2, "filterItems": [{"itemCode": "Seat_1", "name": "2座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Seat_2", "name": "4座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Seat_3", "name": "5座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Seat_4", "name": "6座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Seat_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Seat_6", "name": "8座及以上", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 1, "isQuickItem": false}], "shortName": "座位", "isSupportMulti": true}, {"name": "车辆排挡", "sortNum": 2, "groupCode": "Transmission", "bitwiseType": 2, "filterItems": [{"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Transmission_2", "name": "手动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}], "shortName": "排挡"}, {"name": "车辆配置", "sortNum": 5, "groupCode": "VehicleAccessory", "bitwiseType": 1, "filterItems": [{"itemCode": "VehicleAccessory_AirConditioner", "name": "空调", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 512, "sortNum": 5, "isQuickItem": false}], "shortName": "车辆配置", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 6, "groupCode": "Brand", "bitwiseType": 2, "filterItems": [{"itemCode": "Brand_120", "name": "丰田", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "Brand_176", "name": "日产", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"itemCode": "Brand_168", "name": "Jeep", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"itemCode": "Brand_98", "name": "大众", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"itemCode": "Brand_88", "name": "福特", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}, {"itemCode": "Brand_7", "name": "起亚", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}, {"itemCode": "Brand_203", "name": "雪佛兰", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}, {"itemCode": "Brand_154", "name": "道奇", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/daoqi.png"}, {"itemCode": "Brand_148", "name": "现代", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"itemCode": "Brand_93", "name": "马自达", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mazida.png"}, {"itemCode": "Brand_55", "name": "别克", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "Brand_207", "name": "克莱斯勒", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/kelaisile.png"}, {"itemCode": "Brand_14", "name": "GMC", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/gmc.png"}, {"itemCode": "Brand_54", "name": "特斯拉", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}, {"itemCode": "Brand_118", "name": "三菱", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sanling.png"}, {"itemCode": "Brand_223", "name": "凯迪拉克", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}, {"itemCode": "Brand_197", "name": "英菲尼迪", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/yingfeinidi.png"}, {"itemCode": "Brand_144", "name": "奥迪", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"itemCode": "Brand_43", "name": "宝马", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "Brand_198", "name": "奔驰", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "Brand_232", "name": "捷豹", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiebao.png"}, {"itemCode": "Brand_22", "name": "路虎", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"itemCode": "Brand_231", "name": "玛莎拉蒂", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}], "shortName": "车辆品牌", "isSupportMulti": true}]}, {"name": "门店/服务", "code": "StoreAndPR", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"name": "门店位置", "sortNum": 1, "groupCode": "Location", "bitwiseType": 2, "filterItems": [{"itemCode": "Location_OusideOfAirport", "name": "机场外", "groupCode": "Location", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Location_InsideOfAirport", "name": "机场内", "groupCode": "Location", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 1, "isQuickItem": false}], "shortName": "门店位置"}, {"name": "门店距离", "sortNum": 2, "groupCode": "DistanceGroup", "bitwiseType": 2, "filterItems": [{"itemCode": "Distance_M500", "name": "500米内", "groupCode": "DistanceGroup", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Distance_K1", "name": "1公里内", "groupCode": "DistanceGroup", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Distance_K2", "name": "2公里内", "groupCode": "DistanceGroup", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Distance_K5", "name": "5公里内", "groupCode": "DistanceGroup", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 2, "isQuickItem": false}], "shortName": "距离"}, {"name": "点评", "sortNum": 4, "groupCode": "Comment", "bitwiseType": 2, "filterItems": [{"itemCode": "Comment_4.5", "name": "4.5分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false}, {"itemCode": "Comment_4.0", "name": "4.0分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}, {"itemCode": "Comment_3.5", "name": "3.5分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false}], "shortName": "点评"}, {"name": "门店服务", "sortNum": 10, "groupCode": "StoreService", "bitwiseType": 1, "filterItems": [{"itemCode": "StoreService_3599", "name": "自助取车", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 1, "sortNum": 10, "isQuickItem": false}, {"itemCode": "StoreService_3564", "name": "立即确认", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 2, "sortNum": 10, "isQuickItem": true, "quickSortNum": 120001}, {"itemCode": "StoreService_3563", "name": "免费取消", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 4, "sortNum": 10, "isQuickItem": true, "quickSortNum": 70001}], "shortName": "门店服务", "isSupportMulti": true}, {"name": "特色服务", "sortNum": 11, "groupCode": "SpecialService", "bitwiseType": 2, "filterItems": [{"itemCode": "SpecialService_3603", "name": "支持跨州", "groupCode": "SpecialService", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 11, "isQuickItem": false}, {"itemCode": "SpecialService_3602", "name": "支持跨境", "groupCode": "SpecialService", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 11, "isQuickItem": false}, {"itemCode": "SpecialService_3555", "name": "含加强三者险", "groupCode": "SpecialService", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 11, "isQuickItem": true, "quickSortNum": 60001}, {"itemCode": "SpecialService_3554", "name": "含第三者保障", "groupCode": "SpecialService", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 11, "isQuickItem": true, "quickSortNum": 60002}, {"itemCode": "SpecialService_3553", "name": "0起赔额", "groupCode": "SpecialService", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 11, "isQuickItem": false}, {"itemCode": "SpecialService_3552", "name": "电子提车凭证", "groupCode": "SpecialService", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 11, "isQuickItem": false}], "shortName": "特色服务", "isSupportMulti": true}]}, {"name": "筛选", "code": "filter", "sortNum": 3, "hierarchy": 1, "filterGroups": [{"name": "价格", "sortNum": 1, "groupCode": "Price", "filterItems": [{"itemCode": "Price_0-100", "name": "¥100以下", "code": "0-100", "groupCode": "Price", "sortNum": 0}, {"itemCode": "Price_100-200", "name": "¥100-200", "code": "100-200", "groupCode": "Price", "sortNum": 0}, {"itemCode": "Price_200-300", "name": "¥200-300", "code": "200-300", "groupCode": "Price", "sortNum": 0}, {"itemCode": "Price_300-400", "name": "¥300-400", "code": "300-400", "groupCode": "Price", "sortNum": 0}, {"itemCode": "Price_400-99999", "name": "¥400以上", "code": "400-99999", "groupCode": "Price", "sortNum": 0}], "shortName": "价格"}, {"name": "支付方式", "sortNum": 2, "groupCode": "PayMode", "bitwiseType": 2, "filterItems": [{"itemCode": "PayMode_Prepaid", "name": "在线支付", "groupCode": "PayMode", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 2, "isQuickItem": false}, {"itemCode": "PayMode_PayOnarrived", "name": "到店支付", "groupCode": "PayMode", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}], "shortName": "支付方式"}, {"name": "优惠活动", "sortNum": 3, "groupCode": "Activity", "bitwiseType": 2, "filterItems": [{"itemCode": "Activity_3600", "name": "精选特惠", "groupCode": "Activity", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}], "shortName": "优惠活动", "isSupportMulti": true}, {"name": "驾照要求", "sortNum": 4, "groupCode": "DriverLience", "bitwiseType": 2, "filterItems": [{"itemCode": "DriverLience_lt1001", "name": "仅需中国驾照原件", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false}, {"itemCode": "DriverLience_lt1002", "name": "支持中国驾照原件+驾照国际翻译认证件", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}, {"itemCode": "DriverLience_lt1003", "name": "支持中国驾照原件+车行翻译件", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false}, {"itemCode": "DriverLience_lt1004", "name": "支持中国驾照原件+当地语言公证件", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}, {"itemCode": "DriverLience_lt1006", "name": "支持香港驾照", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 4, "isQuickItem": false}, {"itemCode": "DriverLience_lt1014", "name": "支持国际驾照IDP+签发国当地驾照", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 4, "isQuickItem": false}], "shortName": "驾照要求", "isSupportMulti": true}, {"name": "押金方式", "sortNum": 5, "groupCode": "CreditCard", "bitwiseType": 2, "filterItems": [{"itemCode": "CreditCard_SupportUnionLogoNew", "name": "支持银联", "groupCode": "CreditCard", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}, {"itemCode": "CreditCard_SupportNoEmbossed", "name": "支持非凸字信用卡", "groupCode": "CreditCard", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 5, "isQuickItem": false}, {"itemCode": "CreditCard_SupportNoChip", "name": "支持非芯片信用卡", "groupCode": "CreditCard", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 5, "isQuickItem": false}], "shortName": "押金方式"}, {"name": "里程限制", "sortNum": 6, "groupCode": "Limit", "bitwiseType": 2, "filterItems": [{"itemCode": "Mileage_Unlimited", "name": "不限里程", "groupCode": "Limit", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 6, "isQuickItem": false}, {"itemCode": "Mileage_Limited", "name": "限里程", "groupCode": "Limit", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 6, "isQuickItem": false}], "shortName": "里程限制"}, {"name": "租车公司", "sortNum": 6, "groupCode": "Vendor_0", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_SD0007", "name": "<PERSON><PERSON>", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_SD0009", "name": "ALAMO", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_SD0003", "name": "<PERSON><PERSON>", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_SD0004", "name": "Thrifty", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_SD0008", "name": "Enterprise", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_SD0002", "name": "Dollar", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_SD0010", "name": "National", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_SD0006", "name": "Europcar", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 7, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}]}]}