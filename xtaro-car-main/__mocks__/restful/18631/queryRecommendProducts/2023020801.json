{"allVehicleCount": 10, "allVendorPriceCount": 21, "baseResponse": {"code": "200", "returnMsg": "success", "isSuccess": true}, "ResponseStatus": {"Extension": [{"Value": "8099638796050102875", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a04ad05-465511-29168", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1675841705834+0800)/"}, "uniqSign": "1212121212121212", "vehicleList": [{"transmissionName": "自动挡", "displacement": "1.5L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "3164", "carPhone": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "日产阳光", "zhName": "日产阳光", "brandName": "日产", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "日产", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.5L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "5579", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "大众捷达", "zhName": "大众捷达", "brandName": "大众", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "大众", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.4L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "3059", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "起亚K2", "zhName": "起亚K2", "brandName": "起亚", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "起亚", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.6L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "3060", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "起亚K2", "zhName": "起亚K2", "brandName": "起亚", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "起亚", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.0T-1.5L", "realityImageUrl": "0", "isHot": true, "imageList": ["0"], "carPhone": true, "vehicleCode": "4139", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "brandName": "雪佛兰", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放2个24寸行李箱", "luggageNo": 2, "brandEName": "雪佛兰", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.6L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "1058", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "大众桑塔纳", "zhName": "大众桑塔纳", "brandName": "大众", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "大众", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.0T", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "4899", "carPhone": true, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "雪佛兰创酷", "zhName": "雪佛兰创酷", "brandName": "雪佛兰", "passengerNo": 5, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0"], "groupCode": "6", "vehiclesSetId": "10", "luggageNum": "可放2个24寸行李箱", "luggageNo": 2, "brandEName": "雪佛兰", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.5L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "819", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "别克凯越", "zhName": "别克凯越", "brandName": "别克", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放2个24寸行李箱", "luggageNo": 2, "brandEName": "别克", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.3L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "4498", "carPhone": true, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "别克凯越", "zhName": "别克凯越", "brandName": "别克", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "别克", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.0T", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "5025", "carPhone": true, "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "别克威朗", "zhName": "别克威朗", "brandName": "别克", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "别克", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}], "storeList": [{"pickOffLevel": 22342, "storeCode": "106883", "pickUpLevel": 22342}, {"pickOffLevel": 55049, "storeCode": "117740", "pickUpLevel": 78409}, {"pickOffLevel": 23665, "storeCode": "106896", "pickUpLevel": 23665}, {"pickOffLevel": 43319, "storeCode": "114315", "pickUpLevel": 43319}, {"pickOffLevel": 77639, "storeCode": "107033", "pickUpLevel": 67561}, {"pickOffLevel": 6947, "storeCode": "114043", "pickUpLevel": 6947}, {"pickOffLevel": 59419, "storeCode": "116761", "pickUpLevel": 59419}, {"pickOffLevel": 22341, "storeCode": "106878", "pickUpLevel": 22341}, {"pickOffLevel": 46866, "storeCode": "114529", "pickUpLevel": 46866}, {"pickOffLevel": 44669, "storeCode": "114731", "pickUpLevel": 44669}], "recommendProductInfo": [{"availableLocation": "凤凰国际机场T1航站楼", "recommendation": {"tilte": "附近暂时没有门店哦", "recTitle": "以下为推荐取车枢纽站的部分车辆", "subTitle": "建议您修改取还车地点", "recMessage": ""}, "recommendType": "r5", "latitude": 18.30747, "longitude": 109.41201, "recommendVehicles": [{"vehicleCode": "3164"}, {"vehicleCode": "5579"}, {"vehicleCode": "3059"}, {"vehicleCode": "3060"}, {"vehicleCode": "4139"}, {"vehicleCode": "1058"}, {"vehicleCode": "4899"}, {"vehicleCode": "819"}, {"vehicleCode": "4498"}, {"vehicleCode": "5025"}], "cName": "三亚", "cid": 43}], "productGroups": [{"sortNum": -4, "productList": [{"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 0, "amountStr": "¥0", "detail": [{"code": "1001", "amount": 35, "amountDesc": "¥35", "name": "租车费"}, {"code": "3487", "amount": 35, "amountDesc": "¥35", "name": "元旦特惠"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 11, "amountStr": "¥11", "detail": [{"code": "1002", "amount": 11, "amountDesc": "¥11", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 31, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥66", "subAmount": 66, "name": "总价", "amountStr": "¥31"}], "reference": {"vehicleCode": "0", "rStoreCode": "106883", "pLev": 22342, "comPriceCode": "[c]AegBz/7iwrqttzzxP9KXKtwpZR9L6ED4X0i2YIs41/fY0haM1L35lDIl9cdHkv4EgY1t5rebXa7ixXP7ny26Vy27YJVIri69QPuSzHrS+Nsx/ilH4ALjgsgoGOHztpLep82RkQ6sE1gc07DgiG/WyGYEImu9vfg+3+ngCa+FUOgeZoxFEc58QkUMiPjjadNyWfRCP1Irfn6JznANvFLCFiGQkjHYIhuMehoLLpgo0UFQtieZSJG+/zgja4gcgIQajMX+pIlhqsGkUog3TVTB+ySyNC9bpwbaJ27c/cyurB66nAnLGlB7QVRcPtVOgKwUMCMpU6B06Jb4JkPic2oQd9XB2xdCE72KDA9ZY7uvVPCPqSYqDnac8/OKs2aiEY186q0OQYkgUk9d8WvSBc2qJH40e9EyCuMZhv1OURs9ORNSJ7ay9iy7OHbXSGV8AVkXdxN3M4/vhnalo1cp9tgmItOb2cZCEQUzglYMQN9RE2q61wxHfrqCsWr+skfuTdUPGXDbOjsKsJFyooHDYirmuVzJkzpjgnAXlEVO1GnkW/roC+tVo3xNMIRv1MNcQyJEdRNd0JzPjL9KzokdpC8jKdq6+bo0fkMGaC9GrY881U6QeN8sGLh2pzbl93LM9FaQswb22Qjx8prEFXE=", "bizVendorCode": "SD30147", "pStoreCode": "106883", "packageType": 0, "priceVersion": "AVkB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAleMVaOxCS+IxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOeU00FWYkI9ZeFogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThEh3qML5+OVUvQQPJOpjpURj4CrOI1G42EXvlkfMlUOGF2SPhmVwUZslaoMDyloqiN7gUtHiwtEIpACIBrfcKb2qX95ytHOvotGffMLMUJ4DakUrkySh5k+1CRElJTKzywcaEi5DXth5+9S6I7iQU03vFAI/nSE2lwjZ3ItK2kbqm+s6t+fpflUBIwGgyung/sSEman3g6Ky7EE4jvpwdBA==", "sendTypeForPickUpCar": 0, "skuId": 1855282, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22342, "vendorCode": "30147", "vendorVehicleCode": "13362"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减35", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "3164", "highestPrice": 0, "pWay": "可选:店员免费上门送取车", "minDPrice": 0, "hot": 0, "minTPrice": 31, "lowestDistance": 0, "group": 0, "recommendType": "r5", "recommendInfo": "可租地点：三亚 凤凰国际机场T1航站楼", "sortNum": 0, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106883_106883"], "introduce": "当前车型最低价"}, "minDOrinPrice": 35, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1855953, "bizVendorCode": "SD30147"}}, {"fees": [{"code": "CAR_RENTAL_FEE", "amount": 0, "amountStr": "¥0", "detail": [{"code": "1001", "amount": 31, "amountDesc": "¥31", "name": "租车费"}, {"code": "3487", "amount": 31, "amountDesc": "¥31", "name": "元旦特惠"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 11, "amountStr": "¥11", "detail": [{"code": "1002", "amount": 11, "amountDesc": "¥11", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 31, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥62", "subAmount": 62, "name": "总价", "amountStr": "¥31"}], "reference": {"vehicleCode": "0", "rStoreCode": "106883", "pLev": 22342, "comPriceCode": "[c]AeQBz/7iwrqttzyPNj7Jw8ptQn8BvfmkpCyaGTtHYio+LiTAx+lsL1GJTrwWcZku94t3Jw1k5feS5mAQrK/8zpQTCpGewYzTMV2ZMdvyc3arHlhpEdh/OOPFnhyEWHNaLqjE8WbS88c/xuEvG6u3PRWiRA83OXEitBsPt2nuS4GJUXQzEFyFn1kiAjELA/+DoxqwLm60bcwJCMhWzKn1gZsauo2UJGVwwgPB11GjoqegSIhAY/YJL1CmnF/zdunCcwr0SVyHQEaLU5oSJu6rTlzHWGGjwUX7GMIQJmlrbs6aXcSc7GAQYw1koXuabn5ozRm97HKbUmexuYPZAz1AdhCJvEgMeMHzDDxcxToMrWPy9kqmUQZTsh/mZLj2uxkxbQWJ8jgQEHQ4XX56B9fr/61fJ31HIhmnRNuYnJ8nFD3hXrvaxuNaKh84TYeo8mR6ojArtXYXpKRJ4yjyXS/U1Ijrgv9z/hbMpdDM0CV6H0m3/CeNeVCFwWvYMFhGmtIkkn4+YJQs7xcc6vv6TIdcdTx0hNKGpDlJuDtu+rhgiAbki+G1xbA+Keza4PeZd29rVO9Fo378FvUrafEg+TwAPYVaWA0GeLJbxMUI/LzHaS1/ypenkXV1m6/h8TN7Hiefw9PHNe64SrmS/aY=", "bizVendorCode": "SD30147", "pStoreCode": "106883", "packageType": 0, "priceVersion": "AVkB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAwGJRUDFaQAYxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOeU00FWYkI9ZeFogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThkZROc17UIqcvrF7KBiOXpBj4CrOI1G42EXvlkfMlUOGF2SPhmVwUZjBDHqMoNj/dN7gUtHiwtEL1pHPb2qZyKWqX95ytHOvo0YdDmC0bAl3akUrkySh5kxO273SlHcDiwcaEi5DXth5+9S6I7iQU03vFAI/nSE2lwjZ3ItK2kbqixxrszpHZLEBIwGgyung/sSEman3g6Ky7EE4jvpwdBA==", "sendTypeForPickUpCar": 0, "skuId": 1859842, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22342, "vendorCode": "30147", "vendorVehicleCode": "20007237"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减31", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "5579", "highestPrice": 0, "pWay": "可选:免费站内取还车", "minDPrice": 0, "hot": 0, "minTPrice": 31, "lowestDistance": 0, "group": 0, "recommendType": "r5", "recommendInfo": "可租地点：三亚 凤凰国际机场T1航站楼", "sortNum": 1, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106878_106878"], "introduce": "当前车型最低价"}, "minDOrinPrice": 31, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 2}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 0, "amountStr": "¥0", "detail": [{"code": "1001", "amount": 28, "amountDesc": "¥28", "name": "租车费"}, {"code": "3487", "amount": 28, "amountDesc": "¥28", "name": "元旦特惠"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 11, "amountStr": "¥11", "detail": [{"code": "1002", "amount": 11, "amountDesc": "¥11", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 31, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥59", "subAmount": 59, "name": "总价", "amountStr": "¥31"}], "reference": {"vehicleCode": "0", "rStoreCode": "106883", "pLev": 22342, "comPriceCode": "[c]AegBz/7iwrqttzxu1+plEcXse3qBnuJ1gE2B4LxMsYWP4dKpi0jpH8GdrRNjjLztZQ5c+8FyOiIAdGTgusmSU5kswx3CEpabmgs0wCPysiALXm4YLqj8N4mNJ3DD5M0zsindCRDwcYt+UJPZQHUMKn2c+7Xd6T+eW5WOOC6OgZOH8fJhM58Kcpg4riIluUFqi/tevKiibt3pvAdLacrgWvglqamhevXripSym5t4RAQW6BoNlWJhkxIcL3ZHckDofmU5kZzm5BYqffr6o9S6Mo9uv64gRBrpLrJsw4BvzUj5I8y4aw/Jbu1rbDAECCFUwOrCWf/QbnrO6FP/z53+NfvdmdlOsLa7riVtatMfeYKY5FCIJ36y1d8+uYnHSYpWK+CceorXrR1XXGgzlwcZu1DDUzElqy6e3LzG53gaADmJZ/cbRhkSWR9/7o81DQCW6mPb4X4tVachPv9FPelkNPaSapqEFiRYbgONMYq4VzT3bRCr99+T24nbmM7yOvU2NL9ruj4D6QyuMOJFVvZTujxkJloW4A0TmAKQlA/bkfytVYqijJWq8UTXwG8KfpX3o/pTRwGZef7TAmB7kYt1aMNXDEwDVOhgN60HINahAWq18pFysJXJ0keTDZt28GTz+9N1yXGTa6rAgMI=", "bizVendorCode": "SD30147", "pStoreCode": "106883", "packageType": 0, "priceVersion": "AVkB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOeU00FWYkI9ZeFogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThkZROc17UIqdtBei9Gj1VfBj4CrOI1G42EXvlkfMlUOGF2SPhmVwUZkZ0VEpWBKDON7gUtHiwtEKQiMyxR0iHDmqX95ytHOvoC6fTS9Q1fj/akUrkySh5kz3DDaAI9Ui8wcaEi5DXth5+9S6I7iQU03vFAI/nSE2lwjZ3ItK2kbpk2Me1aRaKBUBIwGgyung/sSEman3g6Ky7EE4jvpwdBA==", "sendTypeForPickUpCar": 0, "skuId": 1855386, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22342, "vendorCode": "30147", "vendorVehicleCode": "108"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1909929, "bizVendorCode": "SD66324"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1972713, "bizVendorCode": "SD70698"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减28", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "3059", "highestPrice": 210, "pWay": "可选:店员免费上门送取车", "minDPrice": 0, "hot": 0, "minTPrice": 31, "lowestDistance": 0, "group": 0, "recommendType": "r5", "recommendInfo": "可租地点：三亚 凤凰国际机场T1航站楼", "sortNum": 2, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106883_106883"], "introduce": "当前车型最低价"}, "minDOrinPrice": 28, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 3}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 0, "amountStr": "¥0", "detail": [{"code": "1001", "amount": 27, "amountDesc": "¥27", "name": "租车费"}, {"code": "3487", "amount": 27, "amountDesc": "¥27", "name": "元旦特惠"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 11, "amountStr": "¥11", "detail": [{"code": "1002", "amount": 11, "amountDesc": "¥11", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 31, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥58", "subAmount": 58, "name": "总价", "amountStr": "¥31"}], "reference": {"vehicleCode": "0", "rStoreCode": "106878", "pLev": 22341, "comPriceCode": "[c]AegBz/7iwrqttzzwxH7NynlwDvsaMA1Rq7T+umtcAjKjOqcW/XAy5VkCUlT+SKjmk5l1xmcy7KAnByjt1779ePVTcgX2n8nTfJiazzvaGiupS4VcWa1nFsaV3SaFErSm3eijNHYdBY3Zzbas5jhnl01ez/tX135+kXqEYg+kv7I1vDfC6SBy432gHxoo9HrkSdGVB3//misJabWBKrH1PxbD80hRKAU/lfV8iOv6YszqrmOb+8KYrkVpKu0iip06H7zdytxZek+d1vWw9gPfngU893dKVIXP158h6JtsWGDK3LZJ0nMJm5eeBAqSRgwdNRfvzjyg0gJX/5uloBPq7WM1B+GfN8YwznVtzP0QHKYu0VnV3LiJCPCXv8YKtyPRddsYVG735Ze04Vn8cug0EdoUAdlobyNiqeygsrz45ufhT3zqGryGmLbcV7zlYrJ7Sd0CnfEfJE5ib1YljmwgIpFln2dtpzDTVP63DzPpm52P0w5mcse1/qNJTjrsMht1fTxKdvMStcnYlMttUi7hMUDtphifiv5fgLhiVilOlT/u3P+ZjUNpqM2CF4WlGN5AM7JZjmfzoQ0NXgaio/1b8z1jfT87Qre4NMBE2vcG6v9i5RdPBrjERjQxnrV3TwMzcSonnOJfZRurK7w=", "bizVendorCode": "SD30147", "pStoreCode": "106878", "packageType": 1, "priceVersion": "AVkB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBSQV1KzLIn4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOeU00FWYkI9ZeFogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThhtNJdHK4NaxtdmYH2LzUSRj4CrOI1G42EXvlkfMlUOGF2SPhmVwUZg/S5apfnnklN7gUtHiwtEJVFkMYAIwbdWqX95ytHOvoIOrwETsxjjXakUrkySh5k4DnqTAds2VFwcaEi5DXth5+9S6I7iQU03vFAI/nSE2lwjZ3ItK2kbp268o/KHXnvkBIwGgyung/CLb/Hkync7e7EE4jvpwdBA==", "sendTypeForPickUpCar": 0, "skuId": 1857847, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22341, "vendorCode": "30147", "vendorVehicleCode": "20059442"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1858311, "bizVendorCode": "SD79695"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减27", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "3060", "highestPrice": 113, "pWay": "可选:免费站内取还车", "minDPrice": 0, "hot": 0, "minTPrice": 31, "lowestDistance": 0.1, "group": 0, "recommendType": "r5", "recommendInfo": "可租地点：三亚 凤凰国际机场T1航站楼", "sortNum": 3, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106878_106878"], "introduce": "当前车型最低价"}, "minDOrinPrice": 27, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 2}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 0, "amountStr": "¥0", "detail": [{"code": "1001", "amount": 28, "amountDesc": "¥28", "name": "租车费"}, {"code": "3487", "amount": 28, "amountDesc": "¥28", "name": "元旦特惠"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 11, "amountStr": "¥11", "detail": [{"code": "1002", "amount": 11, "amountDesc": "¥11", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 31, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥59", "subAmount": 59, "name": "总价", "amountStr": "¥31"}], "reference": {"vehicleCode": "0", "rStoreCode": "106878", "pLev": 22341, "comPriceCode": "[c]AegBz/7iwrqttzyDWr8a6SUlf67wfJJfLie0AsEKTntoaBvfWPc08w4ezi1U6sQmAt/Wy8XTsBZwYhEHn8mhqsqM3ow9ZCG14HLzF7uHugf9oX+yBp3WqvNIhLicyn0nWAHct3Q4qJ0MPgKGElkAqNwIQhyjBJoNFqUxmEmjM0mvEj0YL/FD1L7clCSYT4SMNNXJeqJhxJeYIfxzuqRg4E7ZiAw1qq1EVqmR4VDegN8e8/welvEBUbT6anmCXfQLBgH5eIj0uhBdXWDTpzz5yKG1ftRhuQTmR9pCAbzgxKHrY2kKOVlvh6oxPMEVxsLjrsYa0xmfK+3QegAd//uWYcnbPuib1DaXZJWbdBo44D4Q61n4300NlVnaGbLWpcFaYcAx8yYWfDLeVBfVii35LsrTDj20TxvkYTEIValZgi8SOFrxt0DIvatrIJM3gc3Vlt45fswht9kbLBbanCSFrE2254VZNx91KUBFjyBk0czlhUc9/F9rrmpiS9T1g5+C7mnc/zsrvQH/54/c440Rh9k45OXid9PJOJk+Difh62Otd6cnLfaBmg5bUl7FaxgZIkxfCnU/irOtoczh8+7SgUtslusGqktPumpAABiJIdm0stg6s1jh+84hD38o7mXMl1pDl7FDldvzsZs=", "bizVendorCode": "SD30147", "pStoreCode": "106878", "packageType": 1, "priceVersion": "AVkB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDz0wFHwmfn6oxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOeU00FWYkI9ZeFogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThkZROc17UIqdtBei9Gj1VfBj4CrOI1G42EXvlkfMlUOGF2SPhmVwUZkZ0VEpWBKDON7gUtHiwtEKQiMyxR0iHDmqX95ytHOvoC6fTS9Q1fj/akUrkySh5kz3DDaAI9Ui8wcaEi5DXth5+9S6I7iQU03vFAI/nSE2lwjZ3ItK2kbo92XsUtKW1x0BIwGgyung/CLb/Hkync7e7EE4jvpwdBA==", "sendTypeForPickUpCar": 0, "skuId": 1854917, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22341, "vendorCode": "30147", "vendorVehicleCode": "14415"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1858655, "bizVendorCode": "SD30147"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1911622, "bizVendorCode": "SD74573"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1911940, "bizVendorCode": "SD61831"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1912422, "bizVendorCode": "44444"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1912422, "bizVendorCode": "44444"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1863132, "bizVendorCode": "SD37573"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1972046, "bizVendorCode": "SD30234"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减28", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "4139", "highestPrice": 218, "pWay": "可选:免费站内取还车", "minDPrice": 0, "hot": 1, "minTPrice": 31, "lowestDistance": 0.1, "group": 0, "recommendType": "r5", "recommendInfo": "可租地点：三亚 凤凰国际机场T1航站楼", "sortNum": 4, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106878_106878"], "introduce": "当前车型最低价"}, "minDOrinPrice": 28, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 8}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 0, "amountStr": "¥0", "detail": [{"code": "1001", "amount": 35, "amountDesc": "¥35", "name": "租车费"}, {"code": "3487", "amount": 35, "amountDesc": "¥35", "name": "元旦特惠"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 14, "amountStr": "¥14", "detail": [{"code": "1002", "amount": 14, "amountDesc": "¥14", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 34, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥69", "subAmount": 69, "name": "总价", "amountStr": "¥34"}], "reference": {"vehicleCode": "0", "rStoreCode": "106878", "pLev": 22341, "comPriceCode": "[c]AegBz/7iwrqttzyFBFbbVMOhDa7wfJJfLie0lQDf+itd69rfWPc08w4ezkMbAK0C3/E4CS4T02MOINNy9fXmOWIZUVyvwuyshPp0Pw/quFhMX1xdlzBu4nCs9Dx1QkFewBngruXWUNBClxH1QXe8HRPvUylY5Yi/8i0Gkg9Qa6yGwFYskOJ1uOqRGu2+C4xl7aDCAQZqcH5aULW0nmJZgv5jIDkl7DHiJuCABrAJnTp/ah+NAwcV3vxopCaFKKtOKNvN7Fq2YKM6Ofkyitf8JJ5G4Nyj2iJSoTTpeBxabzMeDbOASii0ZXPt5LfKjSnKee7XUe4g7S1PCwDjujhYCPddnSPh0QLLxFJGpWVRx3oeQCQ3TPkNp+hwqYRRFcw8ZJqwmpUlOR3UxWadxC0df5vqrms9fMrT6TS+vWKRb5Nrv6d1tRUM9PXTyJsEp6CGNX/EsojUmci/HuTE2DXSKajhhaf+Z+hoQHCICAEdSRK3/r7yKXo0gNDf4d1KCLe0NWrA4JrT6qDtaZDspxlUGEWIeYxZeBzG1LGp6h5dYW/EqU+Xavi+usUJvjpNiRQAIKw43VsA/1WA+kMDkWPdcaS7rqtj/D66fpZvsZ0JD5tKXhRu+zivUa35YceXWyiZtvwu/QQXzPCfMsY=", "bizVendorCode": "SD30147", "pStoreCode": "106878", "packageType": 1, "priceVersion": "AVkB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAzdR9BmTnI94xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOfDFvk7pO/EqeFogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThEh3qML5+OVUvQQPJOpjpURj4CrOI1G42c+Z4c6ujLEaF2SPhmVwUZslaoMDyloqiN7gUtHiwtEIpACIBrfcKb2qX95ytHOvotGffMLMUJ4DakUrkySh5k+SN3VqA/B0SwcaEi5DXth5+9S6I7iQU03vFAI/nSE2lwjZ3ItK2kbpPmGKTtWBRiEBIwGgyung/CLb/Hkync7e7EE4jvpwdBA==", "sendTypeForPickUpCar": 0, "skuId": 1864119, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22341, "vendorCode": "30147", "vendorVehicleCode": "169"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减35", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "1058", "highestPrice": 0, "pWay": "可选:免费站内取还车", "minDPrice": 0, "hot": 0, "minTPrice": 34, "lowestDistance": 0, "group": 0, "recommendType": "r5", "recommendInfo": "可租地点：三亚 凤凰国际机场T1航站楼", "sortNum": 5, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106878_106878"], "introduce": "当前车型最低价"}, "minDOrinPrice": 35, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 0, "amountStr": "¥0", "detail": [{"code": "1001", "amount": 35, "amountDesc": "¥35", "name": "租车费"}, {"code": "3487", "amount": 35, "amountDesc": "¥35", "name": "元旦特惠"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 14, "amountStr": "¥14", "detail": [{"code": "1002", "amount": 14, "amountDesc": "¥14", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 34, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥69", "subAmount": 69, "name": "总价", "amountStr": "¥34"}], "reference": {"vehicleCode": "0", "rStoreCode": "106878", "pLev": 22341, "comPriceCode": "[c]AegBz/7iwrqttzyzfwU4z4c8lZwYWlAJZuzflfesWmdxnT+xBIK9VOIaXsOt3R1OpgLFq6mWmlx9JUJla8DlQQUaW8T10ym2V2HlxfNR95nn73lTw2ZaAo0A9kYXK4EFx7p7WEx4HafhISMos+gxl5zgCdJRiL63AYBENrNHaD5cXc4y7AI+LT4Tm0p57rQsSiVRBYLtsRxnJXseQ3oJIOcUp29W4MwWmwz8hYM1ysBzxfBihk5HWSKn050DM534a+yIEUC2Dt2h5ZD5hI2rcwF3xmrGpprKp3LHRTNJ3eNRjfQOjd/UEzo1cvNUUdFrjnh6E+LMaYORWZCLk65BMHLDtu5ABZOySfxIDJmlBxVSMlWOw1/pflJroIEHFO0DIFtH83NLpdMhNqVlCeq1UyRNL8dr6PK4Ahg67lsRnClhJNz/PBld3FuRSKuumF2pHyEm7oypSKI8RfPZavvL32FIq1Y7yORiwzzeMOz90w5cyIbiISgQTiFCHXOKeZERzGEHYli2oPypJuI0v3lov6LSNGfzk9Cq+QpC0xlq1mPDPGet5Yu0kcx30aSJpVRZmD72CzBWTFHmRuWu/2RpbsdlRnH1OJUvlSatl5B/Ci2y+3E/B9UrFgp4g37gSBQ+wWk1FwU7Ra96Pmg=", "bizVendorCode": "SD30147", "pStoreCode": "106878", "packageType": 1, "priceVersion": "AVgB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXBIEq0jvqgWIrzvUZ2EqjhHWBoQN/MtsD3oqpDSw5xzJQHO2Upf/0pR9xc9PfPJDcVV8RrJfIBqGgz8xTYLcsSe4rVnlj/hm0/ZUn/WD1rx8tUNyhK/kruOyrxLKONSxbcWZsGbjFrYMhG28pZk/axFf94B3ddWUDtlG5cmxSG/+TAZ33xPHXivYWrWTpB3xMYHXUFaoT3dT0Yr0UA14RpGgT5kA/+es/hQdTVBe/z8CUqUGe4pANiq6leq55zxkzHBS3DCu3Hfld8pNsBNrK7X9O8cqeWYrFCMKV7jjfxCam93/8Y1d98gcCMFBToyFtbIrmeVE7ktq3VUHnv1IL9/5+RGPbx6S7rgYkQ1WKby8nllq6zTQKkPD2Vj+pz87A4=", "sendTypeForPickUpCar": 0, "skuId": 1856731, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22341, "vendorCode": "30147", "vendorVehicleCode": "20003308"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减35", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "819", "highestPrice": 0, "pWay": "可选:免费站内取还车", "minDPrice": 0, "hot": 0, "minTPrice": 34, "lowestDistance": 0, "group": 0, "recommendType": "r5", "recommendInfo": "可租地点：三亚 凤凰国际机场T1航站楼", "sortNum": 7, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106878_106878"], "introduce": "当前车型最低价"}, "minDOrinPrice": 35, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 0, "amountStr": "¥0", "detail": [{"code": "1001", "amount": 31, "amountDesc": "¥31", "name": "租车费"}, {"code": "3487", "amount": 31, "amountDesc": "¥31", "name": "元旦特惠"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 14, "amountStr": "¥14", "detail": [{"code": "1002", "amount": 14, "amountDesc": "¥14", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 34, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥65", "subAmount": 65, "name": "总价", "amountStr": "¥34"}], "reference": {"vehicleCode": "0", "rStoreCode": "106878", "pLev": 22341, "comPriceCode": "[c]AegBz/7iwrqttzyFBFbbVMOhDcWeMyjEMPnGG8qdjfc0kdhADUqUQZwOpqwvf3ekilQVd0Z/G5OQA94hoD3K682qaZeT+0beUwf7s7tWuO9LjPPq4spFSRfCshhBxqVPY1mu/zbG1RdKfRajzsYSU0lLAK3nrNh9lncHHaSg69HA7FKQd5VPfOGqegT22vQHWGauLqupElHPTyoRH1BI4T15c4aiSs6isjk090sfvS/fRNh5uPUSEsCHZxqZ5/4CR2DdFqcI8Nd03qMLDFyZ2qekAqinufUBOrfVU7+nNLZAuPXjL7BD0HKeKpKqSfkEEvZGNyP5UU4he1CcCunz1zMnC25GDKRt1nK4wtO4aaztZ5pLxLqM+p9TcKVpsbdBYlXb3YgrDHQau28gA9RQQurDuIfTagTP+jpIqFPrLT/U4hhKFOqs9WzRdZsEp6CGNX/EsojUmci/HuTE2DXSKajhhaf+Z+hoQHCICAEdSRK3/r7yKXo0gNDf4d1KCLe0NWrA4JrT6qDtaZDspxlUGEWIeYxZeBzG1LGp6h5dYW/EqU+Xavi+usUJvjpNiRQAIKw43VsA/1WA+kMDkWPdcaS7rqtj/D66fpZvsZ0JD5tKXhRu+zivUa35YceXWyiZtvwu/QQXzPCfMsY=", "bizVendorCode": "SD30147", "pStoreCode": "106878", "packageType": 1, "priceVersion": "AVkB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDKsBv+g1LpkYxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOfDFvk7pO/EqeFogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThkZROc17UIqcvrF7KBiOXpBj4CrOI1G42c+Z4c6ujLEaF2SPhmVwUZjBDHqMoNj/dN7gUtHiwtEL1pHPb2qZyKWqX95ytHOvo0YdDmC0bAl3akUrkySh5k/LGJ16sHHWmwcaEi5DXth5+9S6I7iQU03vFAI/nSE2lwjZ3ItK2kbr89OdYMPmfFEBIwGgyung/CLb/Hkync7e7EE4jvpwdBA==", "sendTypeForPickUpCar": 0, "skuId": 1856692, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22341, "vendorCode": "30147", "vendorVehicleCode": "20042460"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减31", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "4498", "highestPrice": 0, "pWay": "可选:免费站内取还车", "minDPrice": 0, "hot": 0, "minTPrice": 34, "lowestDistance": 0, "group": 0, "recommendType": "r5", "recommendInfo": "可租地点：三亚 凤凰国际机场T1航站楼", "sortNum": 8, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106878_106878"], "introduce": "当前车型最低价"}, "minDOrinPrice": 31, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 0, "amountStr": "¥0", "detail": [{"code": "1001", "amount": 42, "amountDesc": "¥42", "name": "租车费"}, {"code": "3487", "amount": 42, "amountDesc": "¥42", "name": "元旦特惠"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 14, "amountStr": "¥14", "detail": [{"code": "1002", "amount": 14, "amountDesc": "¥14", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 34, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥76", "subAmount": 76, "name": "总价", "amountStr": "¥34"}], "reference": {"vehicleCode": "0", "rStoreCode": "106878", "pLev": 22341, "comPriceCode": "[c]AegBz/7iwrqttzzTAjMjIB7gQKm9qgJ+MeorSRFTg9xe5bqM1L35lDIl9XuRe4acJcignm96Calx9xbvX7wDL61Rhb44QmhPTCyKFIz2pyI54bfk6gKoHXYMijx8IhGWllEBUEMKSfIHTslc+HjBsT1v9wngUHpSWN/iztGq3r3wq7pzgYespq6qtaKF+DISfEe05rdhpxDyJvThEC2K/80zSIvhvoXP3JnS58CccpHdmaUC+5+4/wMPESOfzrzjMSW011wIDTLPGBXf84w1m2zdLIDf0OnusqoE0uc5FuQhkFxIPMGz7WojKRdkjxnHlf6F6QLv3MJlyugcyLo1whdfEGkQN3P3jUKz8P4z6nb817ccFW0CmGwTnfB5Eq9qJ0AuYhq81Eylz3CsXOB1kTaAQsF2U2wQ4Xhe3qgbipcsUsf+uVf4w/I6YI6NU/10R6m0HvtpMPmNs6dL/Le37M1NyGBcrjIGoJNEW6I4L2jfFvIz0ELo0dM01No38/jNkve1soBXMS73KKmiBHwCwoVlk6EkEUMmkNAFt8OdkfMgSt2erha1kqybFURJ+DqUJjXYQsNBh6P1HY9nfMbzp/gkx7XOkVI4/szs6xflD4Fn4+82qDYzBmuApiqSTwtBW1qEusCJQC+gPqM=", "bizVendorCode": "SD30147", "pStoreCode": "106878", "packageType": 1, "priceVersion": "AVoB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAg+6hhuPwfeoxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOfDFvk7pO/EqeFogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbTh486G0PWfOP8MuLlhdyiywhj4CrOI1G42c+Z4c6ujLEaF2SPhmVwUZqEmBt5JE2HhpZpEoTnxeD4V49wtq2fogSX4u/eBFfWNggQ/APP14YcomfwiPv572uZddA1PqN6633zlwu8WNB+lD6gOOR8DP5EvoZ9Ehk/Bem3AF7czA2mtjPwS8HzjJQK2rx95bcUCF7qaj10/lnZWz1KxuIuasQ==", "sendTypeForPickUpCar": 0, "skuId": 1860062, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22341, "vendorCode": "30147", "vendorVehicleCode": "20041868"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减42", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "5025", "highestPrice": 0, "pWay": "可选:免费站内取还车", "minDPrice": 0, "hot": 0, "minTPrice": 34, "lowestDistance": 0, "group": 0, "recommendType": "r5", "recommendInfo": "可租地点：三亚 凤凰国际机场T1航站楼", "sortNum": 9, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106878_106878"], "introduce": "当前车型最低价"}, "minDOrinPrice": 42, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 0, "amountStr": "¥0", "detail": [{"code": "1001", "amount": 70, "amountDesc": "¥70", "name": "租车费"}, {"code": "3487", "amount": 70, "amountDesc": "¥70", "name": "元旦特惠"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 14, "amountStr": "¥14", "detail": [{"code": "1002", "amount": 14, "amountDesc": "¥14", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 34, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥104", "subAmount": 104, "name": "总价", "amountStr": "¥34"}], "reference": {"vehicleCode": "0", "rStoreCode": "106878", "pLev": 22341, "comPriceCode": "[c]AegBz/7iwrqttzy7jaGOBFuxqpwYWlAJZuzfJMgzgjWsixyxBIK9VOIaXsOt3R1OpgLF+mn5lr20ooqTjCsTwYaDiaS3yhfWxZGXJxb1GtQjiCqQVUpmdatROpHi5JzPxHQWLEy1Rj3aF9GgzJJSveA3hBMONh6p0jsmEShobpIw5NQIytvyT6Kh1jaf6fbdMm8rCxqoYBvRd+AWi7WEOmqECvkppW3GNzH/dmKTIpmKseZMlLCb+chICWGYrSM3ea7OoULe98AuxwC1IT+a47HkAhblRUONul36SZpfWjKD/1v0GlEEfwqRNYe3D23iMwIgjc5B9anuUguTuax9HR+dYADnGGuQFZXqkde1JgAQv6btBrPY+NXQNCnmKe/1QxqJ2URI3nfHxa8MODOzXXIsuNAmSxW32OvLQDsi1P8t7IjbG1suqwTEbC6pPEZyNra8aJGye/iI+8vZavvL32FIq1Y7yORiwzzeMOz90w5cyIYuafYcljLlXHOKeZERzGEHruJcJYSXaJ+H3hKnB6nI+hQwJ07y9SdgNAIeYyhZHn2b7hMl1xYBtfKmOPB+OO6hBWbQFXalqCDwuhQBbjxTjSeNHTNxpIVHbMPqhS5BCxpyos6nSxoRBdQ9qtsP51x8hiOyqYflcTI=", "bizVendorCode": "SD30147", "pStoreCode": "106878", "packageType": 1, "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXB+SM9LEnoniIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOfDFvk7pO/EqeFogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw+PFtnSU59Xoc/nXkuHmYCalxpzjZ4XdZgtve49xdKAs3Z4aDlXo/56wsbWidGqqaTjzobQ9Z84/6ZvsCNf3ch8gmTzkg7piu1g4rLTXqm8m/DuYSISBmmQy2JyuUNW0fXBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZMT3dy9j+zOBBUwjHPTf+MwmVShtdZkZv2QvI+WI+mP2Q==", "sendTypeForPickUpCar": 0, "skuId": 1863572, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22341, "vendorCode": "30147", "vendorVehicleCode": "20037826"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减70", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "4899", "highestPrice": 0, "pWay": "可选:免费站内取还车", "minDPrice": 0, "hot": 0, "minTPrice": 34, "lowestDistance": 0, "group": 0, "recommendType": "r5", "recommendInfo": "可租地点：三亚 凤凰国际机场T1航站楼", "sortNum": 6, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106878_106878"], "introduce": "当前车型最低价"}, "minDOrinPrice": 70, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1}], "hasResult": true, "groupCode": "all", "groupName": "全部车型", "dailyPrice": 0, "minTotalPrice": 31}, {"sortNum": 0, "hasResult": true, "groupCode": "2", "groupName": "经济轿车", "dailyPrice": 0, "allowMerge": true, "minTotalPrice": 31}, {"sortNum": 4, "hasResult": true, "groupCode": "6", "groupName": "SUV", "dailyPrice": 0, "allowMerge": true, "minTotalPrice": 34}], "appResponseMap": {"isFromCache": false, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&modify=&orderId=&pickupPointInfo=2023-02-08 16:00:00|富蕴客运站|175|46.989905|89.526772|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=all&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2023-02-08 16:30:00|富蕴客运站|175|46.989905|89.526772|||&sortType=1&uid=@@PAGENUM@@1@@RECOMMEND@@", "groupId": "18631/queryRecommendProducts", "networkCost": 2196, "environmentCost": 2, "cacheFetchCost": 0, "fetchCost": 2196, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1675841703574, "afterFetch": 1675841705770}}