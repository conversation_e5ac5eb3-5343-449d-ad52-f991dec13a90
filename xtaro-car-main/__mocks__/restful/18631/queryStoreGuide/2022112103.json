{"baseResponse": {"code": "200", "cost": 16, "isSuccess": true, "returnMsg": "Success"}, "pickUpGuideInfo": {"storeAddress": "三亚凤凰机场D区停车楼3层（从到达层6号门出口过马路至对面停车场,乘电梯到3楼一嗨机场门店)咨询电话***********", "storeWay": "店员免费接您至门店取车", "longitude": "109.413670", "wayInfo": 4, "storeType": 1, "latitude": "18.308470", "storeName": "凤凰机场店", "guideStep": [{"id": 1, "image": "https://dimg04.c-ctrip.com/images/0412m1200092puh94252D.png"}, {"id": 2, "image": "https://dimg04.c-ctrip.com/images/0415f1200092puhunCA36.png"}, {"id": 3, "image": "https://dimg04.c-ctrip.com/images/0414u1200092pun3cA565.png"}, {"id": 4, "image": "https://dimg04.c-ctrip.com/images/041671200092pubp90211.png"}, {"id": 5, "image": "https://dimg04.c-ctrip.com/images/0410u1200092puey57D61.png"}, {"id": 6, "image": "https://dimg04.c-ctrip.com/images/0414b1200092puhojCBF3.png"}], "storePhone": "***********;4008886608", "businessTime": "24小时营业"}, "ResponseStatus": {"Extension": [{"Value": "2955184339279844177", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a056b5d-463614-31620", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1669011659984+0800)/"}, "dropOffGuideInfo": {"storeAddress": "三亚凤凰机场D区停车楼3层（从到达层6号门出口过马路至对面停车场,乘电梯到3楼一嗨机场门店)咨询电话***********", "storeWay": "还车后店员免费送您至凤凰国际机场T1航站楼", "longitude": "109.413670", "wayInfo": 4, "storeType": 1, "latitude": "18.308470", "storeName": "凤凰机场店", "guideStep": [{"id": 1, "image": "https://dimg04.c-ctrip.com/images/0414y1200092pufws93C9.png"}, {"id": 2, "image": "https://dimg04.c-ctrip.com/images/0411z1200092pui1z53E5.png"}, {"id": 3, "image": "https://dimg04.c-ctrip.com/images/0415k1200092puedp5DA2.png"}, {"id": 4, "image": "https://dimg04.c-ctrip.com/images/0412u1200092puejkF207.png"}], "storePhone": "***********;4008886608", "businessTime": "24小时营业"}, "priceDetailIMUrl": "", "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "18631%2FqueryStoreGuide_M00", "groupId": "18631/queryStoreGuide", "networkCost": 249, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 249, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1669011659808, "afterFetch": 1669011660057}}