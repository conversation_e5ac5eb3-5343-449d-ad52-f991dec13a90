{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "Success", "cost": 87}, "ResponseStatus": {"Timestamp": "2023-11-08 14:15:28", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "6715156299693085519"}, {"Id": "RootMessageId", "Value": "921822-0a90108b-472062-28624"}]}, "pickUpGuideInfo": {"guideStep": [{"id": 1, "image": "https://auto.youth.cn/xw/202310/W020231012516240701625.jpg", "content": "第一步取车，先锁定门店!", "type": 1}, {"id": 2, "image": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc411200000kzvd87CCB.webp", "content": "第二步，航站楼1号出口直行100米", "type": 1}, {"id": 3, "image": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc4x1200000kzvd92D47.webp", "content": "第三步：机场路右拐50米", "type": 1}], "storeType": 2, "storeName": "两江国际门店", "storeAddress": "四川省成都市武侯区桂溪街道吉庆一路411号航天·城上城", "storePhone": "***********", "storeWay": "店员免费送车上门", "businessTime": "09:00 - 22:00", "latitude": "30.556390", "longitude": "104.058274", "wayInfo": 1, "poiAddress": "成都双流机场到达层2楼"}, "dropOffGuideInfo": {"guideStep": [{"id": 1, "image": "https://hiphotos.baidu.com/lbsugc/pic/item/ac6eddc451da81cbd0d75b845166d01609243142.jpg", "content": "第一步还车，先把邮箱加满!", "type": 2}, {"id": 2, "image": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc071200000kzuhu8915.webp", "content": "第二步还车：把车洗干净！", "type": 2}, {"id": 3, "image": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc0i1200000kzvddA5B2.webp", "content": "第三步还车：把车开到店里", "type": 2}], "storeType": 2, "storeName": "两江国际门店", "storeAddress": "四川省成都市武侯区桂溪街道吉庆一路411号航天·城上城", "storePhone": "***********", "storeWay": "店员免费上门取车", "businessTime": "09:00 - 22:00", "latitude": "30.556390", "longitude": "104.058274", "wayInfo": 1, "poiAddress": "成都双流机场到达层2楼"}, "priceDetailIMUrl": ""}