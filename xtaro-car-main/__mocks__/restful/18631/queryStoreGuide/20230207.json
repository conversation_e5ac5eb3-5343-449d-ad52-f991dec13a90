{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "Success", "cost": 92}, "ResponseStatus": {"Timestamp": "/Date(1675751832218+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "1238902973505572931"}, {"Id": "RootMessageId", "Value": "100025527-0a063db3-465486-187365"}]}, "pickUpGuideInfo": {"storeType": 1, "storeName": "祥成租车凤凰机场店", "storeAddress": "海南省三亚市天涯区凤凰镇格之林美宿酒店(三亚机场旗舰店)三亚海航城", "storePhone": "***********", "storeWay": "自行前往门店取车", "businessTime": "08:00 - 20:00", "latitude": "18.306839", "longitude": "109.436301", "wayInfo": 0}, "dropOffGuideInfo": {"storeType": 1, "storeName": "双流国际机场店", "storeAddress": "四川省成都市青羊区东升街道成都天鹅湖公寓天鹅湖别墅区", "storePhone": "***********;***********", "storeWay": "店员免费上门取车", "businessTime": "08:00 - 21:00", "latitude": "30.576015", "longitude": "103.965253", "wayInfo": 1}, "priceDetailIMUrl": ""}