{"__isFromCache": true, "pickUpGuideInfo": {"storeAddress": "凤凰机场TI航站楼入口", "storeWay": "店员免费接您至门店取车", "longitude": "109.411996", "latitude": "18.308310", "storeName": "凤凰机场T1送车点", "storePhone": "***********;************", "businessTime": "10:00-20:00"}, "baseResponse": {"code": "200", "cost": 0, "isSuccess": true, "returnMsg": "Success"}, "ResponseStatus": {"Extension": [{"Value": "6084142804623348403", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a023047-441631-3823", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1589871383704+0800)/"}, "__cachedTime": 66.28903985023499, "dropOffGuideInfo": {"storeAddress": "凤凰机场TI航站楼入口", "storeWay": "还车后店员免费送您至凤凰国际机场T1航站楼", "longitude": "109.411996", "latitude": "18.308310", "storeName": "凤凰机场T1送车点", "storePhone": "***********;************", "businessTime": "10:00-20:00"}, "__saveCacheTimestamp": 1589871383.714895, "appResponseMap": {"networkCost": 228, "isFromCache": true, "isCacheValid": true}}