{"BaseResponse": {"IsSuccess": true, "Code": "200", "ReturnMsg": "success", "RequestId": "b04dd593-8e79-4781-91eb-8a46012d962d", "Cost": 280}, "ResponseStatus": {"Timestamp": "/Date(1713322650737+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "3439942805873241716"}, {"Id": "RootMessageId", "Value": "*********-0a9061a4-475922-339140"}]}, "equipmentInfos": [{"bomGroupCode": "216664_12678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "equipments": [{"equipmentType": 2, "maxCount": 1, "equipmentCode": "1983", "equipmentName": "儿童座椅", "equipmentDesc": "通常适用于年龄4个月~3岁, 体重4千克~8千克的儿童\n", "ageFromUnit": "/个", "ageToUnit": "/个", "localTotalPrice": 800.0, "localDailyPrice": 800.0, "localCurrencyCode": "THB", "currentCurrencyCode": "CNY", "currentTotalPrice": 158.0, "currentDailyPrice": 158.0, "payMode": 1}, {"equipmentType": 2, "maxCount": 2, "equipmentCode": "1941", "equipmentName": "儿童增高坐垫", "equipmentDesc": "通常适用于年龄4个月~3岁, 体重4千克~10千克的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 15.0, "localDailyPrice": 15.0, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 109.0, "currentDailyPrice": 109.0, "payMode": 1}, {"equipmentType": 2, "maxCount": 2, "equipmentCode": "2472", "equipmentName": "婴儿座椅", "equipmentDesc": "通常适用于年龄0个月~6岁, 体重1千克~25千克的儿童\n当有儿童随行时，需装置儿童座椅，以保障儿童安全，遵循当地法规。", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 10.0, "localDailyPrice": 10.0, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 72.0, "currentDailyPrice": 72.0, "payMode": 1}, {"equipmentType": 5, "maxCount": 1, "equipmentCode": "1943", "equipmentName": "额外驾驶员", "equipmentDesc": "2名额外驾驶员\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 50.0, "localDailyPrice": 50.0, "localCurrencyCode": "THB", "currentCurrencyCode": "CNY", "currentTotalPrice": 10.0, "currentDailyPrice": 10.0, "payMode": 1}, {"equipmentType": 5, "maxCount": 1, "equipmentCode": "2387", "equipmentName": "额外驾驶员", "equipmentDesc": "1名额外驾驶员\n额外驾驶员（需提供与主驾相同的证件）", "ageFromUnit": "/个", "ageToUnit": "/个", "localTotalPrice": 0.0, "localDailyPrice": 0.0, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 0.0, "currentDailyPrice": 0.0, "payMode": 1}, {"equipmentType": 5, "maxCount": 1, "equipmentCode": "2388", "equipmentName": "额外驾驶员", "equipmentDesc": "4名额外驾驶员\n额外驾驶员，4名，描述，中文", "ageFromUnit": "/次", "ageToUnit": "/次", "localTotalPrice": 0.0, "localDailyPrice": 0.0, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 0.0, "currentDailyPrice": 0.0, "payMode": 1}], "packageId": 342997}], "areaDesc": "泰国法律规定，年龄不满7岁的儿童必须在车辆上使用儿童安全座椅。"}