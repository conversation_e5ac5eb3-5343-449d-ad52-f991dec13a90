{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "REQUEST PARAM [%s] ERROR", "requestId": "25e99144-7664-4c74-9721-e7f3505244a7", "extMap": {"initBaseData_1": "43.0", "checkRentCenter_2": "0.0", "calculatePreAuth_3": "1.0", "buildInfoCost_2": "402.0", "buildInfoCost_3": "1.0", "buildInfoCost_4": "0.0", "buildInfoCost_1": "0.0", "orginCacheKey": "FlightRecProducts_uid=M00588838&pcid=43&plat=18.306675&plng=109.426847&plname=凤凰国际机场&ptime=2023-03-31 20:00:00&rcid=43&rlat=18.306675&rlng=109.426847&rlname=凤凰国际机场&rtime=2023-04-02 20:00:00&channelId=17522", "cacheKey": "2c0c1af6f62ab1b1c79a6974fd8aebb5"}, "apiResCodes": [], "hasResult": true, "errorCode": "0", "message": ""}, "ResponseStatus": {"Timestamp": "/Date(1680249455070+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "743710277664117385"}, {"Id": "RootMessageId", "Value": "100025527-0a063db3-466735-238809"}]}, "products": [{"product": {"vehicleCode": "4139", "sortNum": 0, "lowestPrice": 18, "highestPrice": 66, "maximumRating": 4.4, "maximumCommentCount": 13, "lowestDistance": 0, "vendorPriceList": [{"vendorName": "懒人行卡拉比", "isMinTPriceVendor": true, "commentInfo": {"level": "", "vendorDesc": "三亚—机场店", "commentCount": 1, "qCommentCount": 1, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "hasComment": 0}, "priceInfo": {"currentDailyPrice": 18, "currentOriginalDailyPrice": 179, "oTPrice": 438, "currentTotalPrice": 115, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AV0B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDv9YRZGhKVmIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw9tdmYH2LzUSRj4CrOI1G42JvqLpLfByOQtve49xdKAs3sbAlU7WCZTwsbWidGqqaSRlE5zXtQip0zvi7+/0Zo6gmTzkg7piu0LdVz1ZP6MJH4dA2vV5Sv+3NjPUZ57GzUoWgX+u0SHxiXhyIqn1eR4pQ7fuev/+jX6B96kIX6UbRu4k0ITj6mVojv8fX8gmVToiEfcecQPqQ==", "priceType": 1, "deductInfos": [{"totalAmount": 323, "payofftype": 2}]}, "reference": {"bizVendorCode": "44444", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "44444_0_114043_114043", "comPriceCode": "[c]NDIyfDE5MTIzLTAzfDIwMjAwOjAtMzEgJjU4JjA6MDAwMjMtJjEkMjEgMDAwNC0wMDAmMzowMDoxJHwxMDAmJjImMTcwMDEmOCQxMDkmMzUmMjAuMDMmMTAuMDAwMCYyMiYyJiQxMDAwJHwyMzAmNjAzLTMwMjMtOjU3OjEgMTUAAAAAMzIAAA==", "priceVersion": "AV0B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDv9YRZGhKVmIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw9tdmYH2LzUSRj4CrOI1G42JvqLpLfByOQtve49xdKAs3sbAlU7WCZTwsbWidGqqaSRlE5zXtQip0zvi7+/0Zo6gmTzkg7piu0LdVz1ZP6MJH4dA2vV5Sv+3NjPUZ57GzUoWgX+u0SHxiXhyIqn1eR4pQ7fuev/+jX6B96kIX6UbRu4k0ITj6mVojv8fX8gmVToiEfcecQPqQ==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20038826", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "618旅游节+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3744", "groupCode": "MarketGroup1346", "amountTitle": "共减323", "groupId": 1, "mergeId": 0}, {"title": "618旅游节+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减323", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 438, "isrec": false, "recommendOrder": 0, "mergeId": 1216, "rectype": 1, "cvid": 4139, "rentalamount": 35, "totalDailyPrice": 18, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20038826", "storeId": "114043"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1912422, "klbPId": 4156, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 40859, "rLevel": 40859, "promtId": 783151896, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "1966"}, "newEnergy": 0, "platform": 10, "kPSId": 114043, "kRSId": 114043, "kVId": 30164, "pLev": 40859, "rLev": 40859, "klbVersion": 1, "kVehicleId": 4139}, "sortNum": 0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "618旅游节+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3744", "groupCode": "MarketGroup1346", "amountTitle": "共减323", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Brand_雪佛兰_雪佛兰科沃兹", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 8448, "checkType": 0}], "storeScore": 100, "isSelect": true, "distance": 0, "rDistance": 1.6184, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3744", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "懒人行卡拉比", "card": 0, "ctripVehicleCode": "4139", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 35, "amountStr": "¥35", "subAmount": 18, "subAmountStr": "日均¥18", "originalDailyPrice": 179, "detail": [{"code": "1001", "name": "租车费", "amount": 358, "amountDesc": "¥358"}, {"code": "11037", "name": "优惠券", "amount": 108, "amountDesc": "¥108"}, {"code": "3744", "name": "618旅游节", "amount": 215, "amountDesc": "¥215"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 115, "amountStr": "¥115", "subAmount": 438, "subAmountStr": "¥438", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["44444_0_114043_114043"]}, "minTPrice": 115, "minDPrice": 18, "modifySameVehicle": false, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, "vehicleInfo": {"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "4139", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济型", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.0T-1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "endurance": "续航100km", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}}, {"product": {"vehicleCode": "5068", "sortNum": 2, "lowestPrice": 43, "highestPrice": 420, "maximumRating": 4, "maximumCommentCount": 13, "lowestDistance": 0, "vendorPriceList": [{"vendorName": "乐天租车", "isMinTPriceVendor": true, "vendorLogo": "http://pic.ctrip.com/car_isd/vendorlogo/31024.jpg", "commentInfo": {"level": "", "vendorDesc": "乐天三亚店", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 43, "currentOriginalDailyPrice": 78, "oTPrice": 236, "currentTotalPrice": 165, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXByU1H60qg/BYxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThhtNJdHK4NawvQQPJOpjpURj4CrOI1G42OK0TQTP3KhQtve49xdKAs9sFeXkr4HYEpZpEoTnxeD7ynuDgNGWrRCX4u/eBFfWNaA3FdkEq28QS18foxW6zTynWS920DkwxiF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZNZulgYaSudQL7+PCUHzY5cDD67mlSidVS7oT9ii7Zh7w==", "priceType": 1, "deductInfos": [{"totalAmount": 71, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD31025", "vendorCode": "31025", "pStoreCode": "107852", "rStoreCode": "107852", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD31025_0_107852_107852", "comPriceCode": "[c]ODE1fDE4ODczLTAzfDIwMjAwOjAtMzEgJjc4JjA6MDAwMjMtJjEkMjEgMDAwNC0wMDAmNzowMDokfDEwOCYmMSY3OCYwMSYyMTAwMzE1NiQwLjAwJjEmMjAwJDEmMjAuMiYzMDAwMiZ8MjAyJjYwJC0zMSAzLTAzNzozMjE1OjU=", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXByU1H60qg/BYxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThhtNJdHK4NawvQQPJOpjpURj4CrOI1G42OK0TQTP3KhQtve49xdKAs9sFeXkr4HYEpZpEoTnxeD7ynuDgNGWrRCX4u/eBFfWNaA3FdkEq28QS18foxW6zTynWS920DkwxiF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZNZulgYaSudQL7+PCUHzY5cDD67mlSidVS7oT9ii7Zh7w==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20080277", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "机票专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3795", "groupCode": "MarketGroup501", "amountTitle": "共减71", "groupId": 1, "mergeId": 0}, {"title": "机票专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减71", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 236, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 5068, "rentalamount": 85, "totalDailyPrice": 43, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1887815, "klbPId": 3459, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 39513, "rLevel": 39513, "promtId": 783151896, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "78533"}, "newEnergy": 0, "platform": 10, "kPSId": 107852, "kRSId": 107852, "kVId": 31025, "pLev": 39513, "rLev": 39513, "klbVersion": 1, "kVehicleId": 5068}, "sortNum": 0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "机票专享+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3795", "groupCode": "MarketGroup501", "amountTitle": "共减71", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Brand_捷达_捷达VS5", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 65536, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}], "storeScore": 100, "isSelect": false, "distance": 0, "rDistance": 0.1732, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3795", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "乐天租车", "card": 0, "ctripVehicleCode": "5068", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 85, "amountStr": "¥85", "subAmount": 43, "subAmountStr": "日均¥43", "originalDailyPrice": 78, "detail": [{"code": "1001", "name": "租车费", "amount": 156, "amountDesc": "¥156"}, {"code": "11037", "name": "优惠券", "amount": 47, "amountDesc": "¥47"}, {"code": "3795", "name": "机票专享", "amount": 24, "amountDesc": "¥24"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 165, "amountStr": "¥165", "subAmount": 236, "subAmountStr": "¥236", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD31025_0_107852_107852"]}, "minTPrice": 165, "minDPrice": 43, "modifySameVehicle": false, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, "vehicleInfo": {"brandEName": "捷达", "brandName": "捷达", "name": "捷达VS5", "zhName": "捷达VS5", "vehicleCode": "5068", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.4T", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "endurance": "续航100km", "autoPark": false, "carPhone": true, "vehiclesSetId": "10"}}, {"product": {"vehicleCode": "5177", "sortNum": 4, "lowestPrice": 48, "highestPrice": 379, "maximumRating": 4.4, "maximumCommentCount": 2, "lowestDistance": 0, "vendorPriceList": [{"vendorName": "器车出行", "isMinTPriceVendor": true, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/74573.jpg", "commentInfo": {"level": "", "vendorDesc": "三亚仝和", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 48, "currentOriginalDailyPrice": 68, "oTPrice": 236, "currentTotalPrice": 195, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAsJWpDLUgrqIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZCJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThhtNJdHK4NaxtdmYH2LzUSRj4CrOI1G42HDehb5ECKbItve49xdKAsxs/jSozjzZVpZpEoTnxeD7dQ1BT/3BX9CX4u/eBFfWNaA3FdkEq28ReTDJ5VBvpeynWS920DkwxiF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZPUN7baZrA6iH0rKQNJ+Bx6N8reTen9/Q343ATlVROaow==", "priceType": 1, "deductInfos": [{"totalAmount": 41, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD74573", "vendorCode": "74573", "pStoreCode": "114529", "rStoreCode": "114529", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD74573_0_114529_114529", "comPriceCode": "[c]NTQ0fDIzMzEzLTAzfDIwMjAwOjAtMzEgJjY4JjA6MDAwMjMtJjEkMjEgMDAwNC0wMDAmNjowMDokfDEwOCYmMSY2OCYwMSYyMTAwMzEzNiQwLjAwJjEmMjAwJDEmMjAuMiY0MDAwMiZ8MjAyJjgwJC0zMSAzLTAzNzozMjE1OjU=", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAsJWpDLUgrqIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZCJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThhtNJdHK4NaxtdmYH2LzUSRj4CrOI1G42HDehb5ECKbItve49xdKAsxs/jSozjzZVpZpEoTnxeD7dQ1BT/3BX9CX4u/eBFfWNaA3FdkEq28ReTDJ5VBvpeynWS920DkwxiF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZPUN7baZrA6iH0rKQNJ+Bx6N8reTen9/Q343ATlVROaow==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20067637", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减41", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 236, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 5177, "rentalamount": 95, "totalDailyPrice": 48, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 2331544, "klbPId": 4997, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 46866, "rLevel": 46866, "promtId": 783151896, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "76099"}, "newEnergy": 1, "platform": 10, "kPSId": 114529, "kRSId": 114529, "kVId": 74573, "pLev": 46866, "rLev": 46866, "klbVersion": 1, "kVehicleId": 5177}, "sortNum": 0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减41", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Brand_别克_别克微蓝6", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}], "storeScore": 100, "isSelect": false, "distance": 0, "rDistance": 1.5305, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "器车出行", "card": 0, "ctripVehicleCode": "5177", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 95, "amountStr": "¥95", "subAmount": 48, "subAmountStr": "日均¥48", "originalDailyPrice": 68, "detail": [{"code": "1001", "name": "租车费", "amount": 136, "amountDesc": "¥136"}, {"code": "11037", "name": "优惠券", "amount": 41, "amountDesc": "¥41"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 195, "amountStr": "¥195", "subAmount": 236, "subAmountStr": "¥236", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD74573_0_114529_114529"]}, "minTPrice": 195, "minDPrice": 48, "modifySameVehicle": false, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, "vehicleInfo": {"brandEName": "别克", "brandName": "别克", "name": "别克微蓝6", "zhName": "别克微蓝6", "vehicleCode": "5177", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适型", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "struct": "", "fuel": "", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 5, "fuelType": "纯电动", "luggageNum": "可放2个24寸行李箱", "endurance": "续航100km", "charge": "快充0.67小时,慢充8小时", "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vehiclesSetId": "83"}}, {"product": {"vehicleCode": "5291", "sortNum": 9, "lowestPrice": 90, "highestPrice": 132, "maximumRating": 0, "maximumCommentCount": 0, "lowestDistance": 0, "vendorPriceList": [{"vendorName": "炜晨租车", "isMinTPriceVendor": true, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/79695.jpg", "commentInfo": {"level": "", "vendorDesc": "炜晨租车", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 102, "currentOriginalDailyPrice": 145, "oTPrice": 370, "currentTotalPrice": 283, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXBwLWddjAH9r4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw8uGsR6h849S8/nXkuHmYCaci6IpT4sGTL7iAF2kxaBTBKWlmuTL1ANLfJLo8tJEm+05rVZEj792mzKjoQFV3zALXCYweCHcMIZbFSyW645Vc/nXkuHmYCaO8PrAIdTtvdc6mqeALM80MHBRuJlQ0JBI5Vg/dNzVr8z4Oi4SXH6QKRQT8yTHUGls2DBNghjgLo7qYtqp3Tksg==", "priceType": 1, "deductInfos": [{"totalAmount": 87, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD79695", "vendorCode": "79695", "pStoreCode": "107033", "rStoreCode": "107033", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD79695_0_107033_107033", "comPriceCode": "[c]", "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXBwLWddjAH9r4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw8uGsR6h849S8/nXkuHmYCaci6IpT4sGTL7iAF2kxaBTBKWlmuTL1ANLfJLo8tJEm+05rVZEj792mzKjoQFV3zALXCYweCHcMIZbFSyW645Vc/nXkuHmYCaO8PrAIdTtvdc6mqeALM80MHBRuJlQ0JBI5Vg/dNzVr8z4Oi4SXH6QKRQT8yTHUGls2DBNghjgLo7qYtqp3Tksg==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20069463", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减87", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 370, "isrec": false, "recommendOrder": 0, "mergeId": 871, "rectype": 1, "cvid": 5291, "rentalamount": 203, "totalDailyPrice": 102, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20069463", "storeId": "107033"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1858503, "klbPId": 1904, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 77639, "rLevel": 77639, "promtId": 783151896, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "82323"}, "newEnergy": 0, "platform": 10, "kPSId": 107033, "kRSId": 107033, "kVId": 79695, "pLev": 77639, "rLev": 77639, "klbVersion": 1, "kVehicleId": 5291}, "sortNum": 1, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减87", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Brand_海马_海马7X", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 65536, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 8448, "checkType": 0}], "storeScore": 100, "isSelect": false, "distance": 0, "rDistance": 0.3033, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "炜晨租车", "card": 0, "ctripVehicleCode": "5291", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 203, "amountStr": "¥203", "subAmount": 102, "subAmountStr": "日均¥102", "originalDailyPrice": 145, "detail": [{"code": "1001", "name": "租车费", "amount": 290, "amountDesc": "¥290"}, {"code": "11037", "name": "优惠券", "amount": 87, "amountDesc": "¥87"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 283, "amountStr": "¥283", "subAmount": 370, "subAmountStr": "¥370", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD81931_0_117220_117220"]}, "minTPrice": 283, "minDPrice": 102, "modifySameVehicle": false, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, "vehicleInfo": {"brandEName": "海马", "brandName": "海马", "name": "海马7X", "zhName": "海马7X", "vehicleCode": "5291", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 1, "displacement": "1.5T-1.6T", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "endurance": "续航100km", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "44"}}, {"product": {"vehicleCode": "17380", "sortNum": 113, "lowestPrice": 70, "highestPrice": 489, "maximumRating": 0, "maximumCommentCount": 1, "lowestDistance": 0, "vendorPriceList": [{"vendorName": "懒人行卡拉比", "isMinTPriceVendor": true, "commentInfo": {"level": "", "vendorDesc": "三亚—机场店", "commentCount": 1, "qCommentCount": 1, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "hasComment": 0}, "priceInfo": {"currentDailyPrice": 70, "currentOriginalDailyPrice": 699, "oTPrice": 1638, "currentTotalPrice": 379, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AWIB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDNkWCRUP3hyjKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0zoLE3I1MH/BpD5XCJO4OtUET0W6PBA/0pygpFDL5W4SLwbsmBELwBye8UAj+dITaWyhNBk2niSwCr5jR6xgwagUHU1QXv8/An3vJv4vo9LqoViTVf+KyZD/7FiEcVVNFTRrGj9xfIHE2mZ+VB5tA5R1sUaQ936ig1DQfPbwnHXoEnSe3IG/GMbMCe5LaKnV14JJXqEaL/OV4wpXuON/EJqqg6mQNYS1AfffOXC7xY0H6UPqA45HwM/kS+hn0SGT8F6bcAXtzMDaQn4sRIxNclSAravH3ltxQLyztKnntAvjjtqtF9OQCEG", "priceType": 1, "deductInfos": [{"totalAmount": 1259, "payofftype": 2}]}, "reference": {"bizVendorCode": "44444", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "44444_0_114043_114043", "comPriceCode": "[c]", "priceVersion": "AWIB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDNkWCRUP3hyjKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0zoLE3I1MH/BpD5XCJO4OtUET0W6PBA/0pygpFDL5W4SLwbsmBELwBye8UAj+dITaWyhNBk2niSwCr5jR6xgwagUHU1QXv8/An3vJv4vo9LqoViTVf+KyZD/7FiEcVVNFTRrGj9xfIHE2mZ+VB5tA5R1sUaQ936ig1DQfPbwnHXoEnSe3IG/GMbMCe5LaKnV14JJXqEaL/OV4wpXuON/EJqqg6mQNYS1AfffOXC7xY0H6UPqA45HwM/kS+hn0SGT8F6bcAXtzMDaQn4sRIxNclSAravH3ltxQLyztKnntAvjjtqtF9OQCEG", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20655947", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "618旅游节+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3744", "groupCode": "MarketGroup1346", "amountTitle": "共减1259", "groupId": 1, "mergeId": 0}, {"title": "618旅游节+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减1259", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 1638, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 17380, "rentalamount": 139, "totalDailyPrice": 70, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 2036881, "klbPId": 4156, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 40859, "rLevel": 40859, "promtId": 783151896, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "1966"}, "newEnergy": 0, "platform": 10, "kPSId": 114043, "kRSId": 114043, "kVId": 30164, "pLev": 40859, "rLev": 40859, "klbVersion": 1, "kVehicleId": 17380}, "sortNum": 0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "618旅游节+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3744", "groupCode": "MarketGroup1346", "amountTitle": "共减1259", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_1", "binaryDigit": 1024, "checkType": 0}, {"groupCode": "Brand_福特_福特野马", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}], "storeScore": 100, "isSelect": true, "distance": 0, "rDistance": 1.6184, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3744", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "懒人行卡拉比", "card": 0, "ctripVehicleCode": "17380", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 139, "amountStr": "¥139", "subAmount": 70, "subAmountStr": "日均¥70", "originalDailyPrice": 699, "detail": [{"code": "1001", "name": "租车费", "amount": 1398, "amountDesc": "¥1398"}, {"code": "11037", "name": "优惠券", "amount": 420, "amountDesc": "¥420"}, {"code": "3744", "name": "618旅游节", "amount": 839, "amountDesc": "¥839"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 220, "amountStr": "¥220", "detail": [{"code": "1002", "name": "基础服务费", "amount": 220, "amountDesc": "¥220", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 379, "amountStr": "¥379", "subAmount": 1638, "subAmountStr": "¥1638", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["44444_0_114043_114043"]}, "minTPrice": 379, "minDPrice": 70, "modifySameVehicle": false, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, "vehicleInfo": {"brandEName": "福特", "brandName": "福特", "name": "福特野马", "zhName": "福特野马", "vehicleCode": "17380", "groupCode": "9", "groupSubClassCode": "", "groupName": "跑车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 4, "doorNo": 2, "luggageNo": 0, "displacement": "2.3T", "struct": "", "fuel": "98号", "driveMode": "", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 3, "endurance": "续航100km", "autoPark": false, "carPhone": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "113"}}], "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"content": "与机票同时下单，限时专享优惠8折起-测试", "style": "0"}]}], "type": 11, "icon": "https://dimg.fws.qa.nt.ctripcorp.com/images/0306i1200000dycdk2E0B.png"}]}