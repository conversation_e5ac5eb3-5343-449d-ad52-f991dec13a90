{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1671010402767+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "3084238499590000439"}, {"Id": "RootMessageId", "Value": "921822-0a715c4c-464169-8234169"}]}, "vehicleInfo": {"brandId": 0, "brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "14415", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.0T-1.5L", "struct": "三厢", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0415c120009sl0u8w4A82.jpg"], "vedio": "https://video.c-ctrip.com/videos/R40f27000001i5l8m91B0.mp4", "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg", "https://dimg04.c-ctrip.com//images/0410u120008at350uDF48.jpg", "https://dimg04.c-ctrip.com//images/0414c120008at2ypz0D5C.jpg", "https://dimg04.c-ctrip.com//images/0412t120008at3ett66A0.jpg", "https://dimg04.c-ctrip.com//images/0414w120008at5huo1DE9.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png", "sourcePicInfos": [{"source": 3, "type": 2, "sourceName": "年款/颜色等以实物为准丨图片来源懂车帝", "picList": [{"imageUrl": "https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png", "sortNum": 0}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0410u120008at350uDF48.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0412t120008at3ett66A0.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0414c120008at2ypz0D5C.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0414w120008at5huo1DE9.jpg", "sortNum": 1}]}], "oilType": 5, "cover": "https://dimg04.c-ctrip.com/images/0AS33120009sqadenADAB.png", "luggageNum": "可放2个24寸行李箱", "autoPark": false, "autoStart": false, "autoBackUp": false, "vr": ""}, "specificProductGroups": {"vendorPriceList": [{"vendorName": "桐叶租车", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "凤凰机场店", "commentCount": 24006, "qCommentCount": 24006, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "commentLabel": "车辆很新", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 42, "currentOriginalDailyPrice": 48, "oTPrice": 196, "currentTotalPrice": 184, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 12, "payofftype": 2}]}, "reference": {"bizVendorCode": "13031", "vendorCode": "13031", "pStoreCode": "150693", "rStoreCode": "150693", "vehicleCode": "127517", "packageId": "Secure", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "127517"}, "isEasyLife": true, "payMode": 2, "productCode": "13031_127517_150693_150693", "comPriceCode": "eyJwYWNrYWdlaWQiOiJTZWN1cmUiLCJzdG9yZWlkIjoyNzYxLCJjYXJ0eXBlaWQiOjE4MjAwLCJ0b3RhbCI6MTk2fQ==", "priceVersion": "AVkB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbcCJO9j9gLhryT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7Uixlv3FOddkNUoAtIy+cOdA4XZI+GZXBRm+yzQN581aXnJ0Q58bLxSDuLPazPh/bwkhflRQ2sfuxgMT7yIu9nO5ttTu24C4IkU7T331tWsN4ZfAiUaBhj2tM/nXkuHmYCaj0IQ1ZF8jowmexv4KTleLKIP0Fbh1/0lLy8p67UobGU9RMEIFScL4EBIwGgyung/yDDjqh/3B8W7EE4jvpwdBA==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "18200", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减12", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 196, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 84, "totalDailyPrice": 42, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "18200", "storeId": "150693"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 2, "rRc": 2, "skuId": 58955, "klbPId": 1, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 2761, "rLevel": 2761, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "16", "s": "69.62", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 78, "kRSId": 78, "kVId": 13031, "pLev": 129, "rLev": 129, "klbVersion": 0}, "sortNum": 0, "pStoreRouteDesc": "免费站内取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 70, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减12", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 124, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}], "sortScore": 0, "storeScore": 98.26, "isSelect": true, "stock": 1, "distance": 0, "rDistance": 0.5747, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3743", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "桐叶租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 84, "amountStr": "¥84", "subAmount": 42, "subAmountStr": "日均¥42", "originalDailyPrice": 48, "detail": [{"code": "1001", "name": "租车费", "amount": 96, "amountDesc": "¥96"}, {"code": "3743", "name": "周三福利日", "amount": 12, "amountDesc": "¥12"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 184, "amountStr": "¥184", "subAmount": 196, "subAmountStr": "¥196", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "35cfc26cdfb44b7193a9c72f678c98f5"}, {"vendorName": "懒人行租车", "isMinTPriceVendor": false, "vendorLogo": "https://dimg04.c-ctrip.com/images/0yc3w12000a7k3pvyF9CF.jpg", "commentInfo": {"level": "超棒", "vendorDesc": "三亚--机场店", "commentCount": 31886, "qCommentCount": 31886, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "commentLabel": "店员热情", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 62, "currentOriginalDailyPrice": 70, "oTPrice": 240, "currentTotalPrice": 223, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 17, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4201", "vendorCode": "30164", "pStoreCode": "11006", "rStoreCode": "11006", "vehicleCode": "76338", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "76338"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4201_76338_11006_11006", "comPriceCode": "639997a8d852893f13ea4749", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0yFB1O+qAq7LsChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dq4pyc1rRHsps/nXkuHmYCa7IlBXax1dZb7iAF2kxaBTJFJVSnKAnW2wsbWidGqqaTPwE2vw2qFnUzvi7+/0Zo6gmTzkg7piu1W6HI1OpNCXeaBD74/86GB6oJbfI+/MX8oWgX+u0SHxiXhyIqn1eR4pQ7fuev/+jX6B96kIX6UbRu4k0ITj6mVojv8fX8gmVRkeEqRvIaKdg==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20037374", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减17", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 240, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 123, "totalDailyPrice": 62, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20037374", "storeId": "11006"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "精选好货", "pRc": 2, "rRc": 2, "skuId": 1912422, "klbPId": 4156, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 6947, "rLevel": 6947, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "16", "s": "45.67", "c": "43", "v": "1966"}, "newEnergy": 0, "platform": 10, "kPSId": 114043, "kRSId": 114043, "kVId": 30164, "pLev": 40845, "rLev": 40845, "klbVersion": 0}, "sortNum": 1, "pStoreRouteDesc": "免费站内取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 70, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减17", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 124, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}], "sortScore": 1, "storeScore": 98.99, "isSelect": true, "distance": 0, "rDistance": 0.5832, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3743", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "懒人行租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 123, "amountStr": "¥123", "subAmount": 62, "subAmountStr": "日均¥62", "originalDailyPrice": 70, "detail": [{"code": "1001", "name": "租车费", "amount": 140, "amountDesc": "¥140"}, {"code": "3743", "name": "周三福利日", "amount": 17, "amountDesc": "¥17"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 223, "amountStr": "¥223", "subAmount": 240, "subAmountStr": "¥240", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "e2a58ebdd27b4e04a63158087e2dc52c"}, {"vendorName": "三亚世纪联合租车", "isMinTPriceVendor": true, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "世纪联合租车", "commentCount": 8087, "qCommentCount": 8087, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 38, "currentOriginalDailyPrice": 0, "oTPrice": 156, "currentTotalPrice": 156, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD3942", "vendorCode": "53893", "pStoreCode": "98485", "rStoreCode": "98485", "vehicleCode": "76338", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "76338"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3942_76338_98485_98485", "comPriceCode": "639997a8a67e477d0ec90331", "priceVersion": "AVkB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0y9q7V3/9BXyMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/drX8s9LgliBS7OotsHaLJ/+Iq52C1SRCtKF2SPhmVwUZqir4oUW+FhWN7gUtHiwtEJ52CJNsXqFIGqX95ytHOvotqkEi+pwYKHakUrkySh5kzgZzrBKgh/l33zlwu8WNB+lD6gOOR8DP5EvoZ9Ehk/Bem3AF7czA2n3kTo4jlLHVgK2rx95bcUCCsTlaGyOqCK7EE4jvpwdBA==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20037374", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 156, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 76, "totalDailyPrice": 38, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20037374", "storeId": "98485"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "低价省钱", "pRc": 0, "rRc": 0, "skuId": 1860920, "klbPId": 2110, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 23998, "rLevel": 23998, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "50500"}, "newEnergy": 0, "platform": 10, "kPSId": 107119, "kRSId": 107119, "kVId": 53893, "pLev": 23998, "rLev": 23998, "klbVersion": 0}, "sortNum": 2, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 1024, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 124, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 160, "checkType": 0}], "sortScore": 2, "storeScore": 99.01, "isSelect": false, "distance": 0, "rDistance": 1.8315, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "三亚世纪联合租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 76, "amountStr": "¥76", "subAmount": 38, "subAmountStr": "日均¥38", "detail": [{"code": "1001", "name": "租车费", "amount": 76, "amountDesc": "¥76"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 156, "amountStr": "¥156", "subAmount": 156, "subAmountStr": "¥156", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "d8583d2dd9ce44a58a9c561632a3855e"}, {"vendorName": "明昊租车", "isMinTPriceVendor": false, "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/minghao.png", "commentInfo": {"level": "超棒", "vendorDesc": "明昊凤凰机场店", "commentCount": 2902, "qCommentCount": 2902, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "commentLabel": "店员热情", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 42, "currentOriginalDailyPrice": 48, "oTPrice": 196, "currentTotalPrice": 184, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 12, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3012", "vendorCode": "13032", "pStoreCode": "150743", "rStoreCode": "150743", "vehicleCode": "128368", "packageId": "Secure", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "128368"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3012_128368_150743_150743", "comPriceCode": "eyJwYWNrYWdlaWQiOiJTZWN1cmUiLCJzdG9yZWlkIjo4NTM2LCJjYXJ0eXBlaWQiOjE4MjAwLCJ0b3RhbCI6MTk2fQ==", "priceVersion": "AVoB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbcCJO9j9gLhryT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7Uixlv3FOddkNUoAtIy+cOdA4XZI+GZXBRm+yzQN581aXnJ0Q58bLxSDuLPazPh/bwkhflRQ2sfuxgMT7yIu9nO5ttTu24C4IkU7T331tWsN4ZfAiUaBhj2tM/nXkuHmYCaj0IQ1ZF8jowmexv4KTleLKIP0Fbh1/0lLy8p67UobGUZzUmJU6lqMgK2rx95bcUCnBZ2TzqIpaM7arRfTkAhBg==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "18200", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减12", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 196, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 84, "totalDailyPrice": 42, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "18200", "storeId": "150743"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 226822, "klbPId": 6, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8536, "rLevel": 8536, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "69.62", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 2961, "kRSId": 2961, "kVId": 13032, "pLev": 1032, "rLev": 1032, "klbVersion": 0}, "sortNum": 3, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减12", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 64, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 124, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}], "sortScore": 3, "storeScore": 95.52, "isSelect": false, "stock": 1, "distance": 0, "rDistance": 0.5747, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3743", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "明昊租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 84, "amountStr": "¥84", "subAmount": 42, "subAmountStr": "日均¥42", "originalDailyPrice": 48, "detail": [{"code": "1001", "name": "租车费", "amount": 96, "amountDesc": "¥96"}, {"code": "3743", "name": "周三福利日", "amount": 12, "amountDesc": "¥12"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 184, "amountStr": "¥184", "subAmount": 196, "subAmountStr": "¥196", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "56d75b77c6a043fc85ff145d4dc66043"}, {"vendorName": "凯美租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "三亚店", "commentCount": 2689, "qCommentCount": 2689, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 48, "currentOriginalDailyPrice": 0, "oTPrice": 176, "currentTotalPrice": 176, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD3926", "vendorCode": "37573", "pStoreCode": "179195", "rStoreCode": "179195", "vehicleCode": "76338", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "76338"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3926_76338_179195_179195", "comPriceCode": "639997a8d852893f13ea4884", "priceVersion": "AVoB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0y9q7V3/9BXyMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dqLc/WPOP05hLOotsHaLJ/+3T6wLmzUN0eF2SPhmVwUZnPkoBnJoUu4N7gUtHiwtEKQiMyxR0iHDmqX95ytHOvolfLYev903n/akUrkySh5k8vo3BD40PnK33zlwu8WNB+lD6gOOR8DP5EvoZ9Ehk/Bem3AF7czA2m+yEk4dOP/VwK2rx95bcUCBt8O0AYPYbm151wxnovGUQ==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20037374", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 176, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 96, "totalDailyPrice": 48, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20037374", "storeId": "179195"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1863132, "klbPId": 1615, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 23665, "rLevel": 23665, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "76.76", "c": "43", "v": "74399"}, "newEnergy": 0, "platform": 10, "kPSId": 106896, "kRSId": 106896, "kVId": 37573, "pLev": 23665, "rLev": 23665, "klbVersion": 0}, "sortNum": 4, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "sortScore": 4, "storeScore": 97.97, "isSelect": false, "distance": 0, "rDistance": 1.1075, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "凯美租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 96, "amountStr": "¥96", "subAmount": 48, "subAmountStr": "日均¥48", "detail": [{"code": "1001", "name": "租车费", "amount": 96, "amountDesc": "¥96"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 176, "amountStr": "¥176", "subAmount": 176, "subAmountStr": "¥176", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "860b88713e44422abe482014b19e2390"}, {"vendorName": "海南中进租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "中进汽车租赁三亚店", "commentCount": 72, "qCommentCount": 72, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "commentLabel": "车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 41, "currentOriginalDailyPrice": 45, "oTPrice": 170, "currentTotalPrice": 162, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 8, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD6573", "vendorCode": "74373", "pStoreCode": "181329", "rStoreCode": "181329", "vehicleCode": "76338", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "76338"}, "isEasyLife": true, "payMode": 2, "productCode": "SD6573_76338_181329_181329", "comPriceCode": "639997a8a67e477d0ec901cb", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0y9q7V3/9BXyMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dqQvZLnnf1MaM/nXkuHmYCacdx6t2BdhJz7iAF2kxaBTMXEJGYXAEAJwsbWidGqqaSG00l0crg1rD20hRuk6nMsgmTzkg7piu2Rqsevq3WAeB6pkdUJuAhHQ6q3jYfmb0TBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZMDMlGyXy8I644jLwN2TNIGeFGo7GuVePiILGFpTY6Alg==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20037374", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减8", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 170, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 82, "totalDailyPrice": 41, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20037374", "storeId": "181329"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1965861, "klbPId": 9036, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 67912, "rLevel": 67912, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "92.05", "c": "43", "v": "75869"}, "newEnergy": 0, "platform": 10, "kPSId": 116523, "kRSId": 116523, "kVId": 74373, "pLev": 67912, "rLev": 67912, "klbVersion": 0}, "sortNum": 5, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减8", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 32, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 160, "checkType": 0}], "sortScore": 5, "storeScore": 98.7, "isSelect": false, "distance": 0, "rDistance": 1.1568, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "海南中进租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 82, "amountStr": "¥82", "subAmount": 41, "subAmountStr": "日均¥41", "originalDailyPrice": 45, "detail": [{"code": "1001", "name": "租车费", "amount": 90, "amountDesc": "¥90"}, {"code": "3814", "name": "周边游特惠", "amount": 8, "amountDesc": "¥8"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 162, "amountStr": "¥162", "subAmount": 170, "subAmountStr": "¥170", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "e5bf987c5f2041ae962584c03e1327c9"}, {"vendorName": "恒驰租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "景顺恒驰", "commentCount": 383, "qCommentCount": 383, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "commentLabel": "车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 78, "currentOriginalDailyPrice": 88, "oTPrice": 256, "currentTotalPrice": 236, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 20, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4739", "vendorCode": "77287", "pStoreCode": "185172", "rStoreCode": "185172", "vehicleCode": "76338", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "76338"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4739_76338_185172_185172", "comPriceCode": "639997a8d852893f13ea489f", "priceVersion": "AV0B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0y9q7V3/9BXyMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/drxQp8cvC+1zc/nXkuHmYCazsCt9f2Ot6n7iAF2kxaBTJYcoEi62T5dwsbWidGqqaSKPWjJh7q4JEzvi7+/0Zo6gmTzkg7piu0VdR5r8mAyAuaBD74/86GBga2KL5ujdqEoWgX+u0SHxiXhyIqn1eR4pQ7fuev/+jUqTi3bolDYicZv9rN79EgT16OJIUjhMXJ15P9CsGy96Q==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20037374", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减20", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 256, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 156, "totalDailyPrice": 78, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20037374", "storeId": "185172"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1915995, "klbPId": 5710, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 47171, "rLevel": 47171, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "40.34", "c": "43", "v": "79263"}, "newEnergy": 0, "platform": 10, "kPSId": 114580, "kRSId": 114580, "kVId": 77287, "pLev": 47171, "rLev": 47171, "klbVersion": 0}, "sortNum": 6, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减20", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 131072, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 11, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}], "sortScore": 6, "storeScore": 99.08, "isSelect": false, "distance": 0, "rDistance": 2.7305, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "恒驰租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 156, "amountStr": "¥156", "subAmount": 78, "subAmountStr": "日均¥78", "originalDailyPrice": 88, "detail": [{"code": "1001", "name": "租车费", "amount": 176, "amountDesc": "¥176"}, {"code": "3814", "name": "周边游特惠", "amount": 20, "amountDesc": "¥20"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 236, "amountStr": "¥236", "subAmount": 256, "subAmountStr": "¥256", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "4710cceb56f941fb82dfbd4f087f961e"}, {"vendorName": "卢米租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "凤凰机场店", "commentCount": 6209, "qCommentCount": 6209, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 176, "currentOriginalDailyPrice": 198, "oTPrice": 476, "currentTotalPrice": 432, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 44, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD5349", "vendorCode": "30912", "pStoreCode": "23379", "rStoreCode": "23379", "vehicleCode": "18456", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "18456"}, "isEasyLife": true, "payMode": 2, "productCode": "SD5349_18456_23379_23379", "comPriceCode": "639997a8d852893f13ea4946", "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0y9q7V3/9BXyMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dpuDnFYORCtPIViTVf+KyZDAUeDJid5xejRrGj9xfIHE+0PoLFrDxhc1sUaQ936ig1DQfPbwnHXoGIGB4P7zMJ4MCe5LaKnV15ES7fIBFy0Nzs7sx5Ik0expkczb9KnBrVwIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLut25+n0nUKSXeWWrrNNAqQ8sSSRTV8XY+g==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "14415", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减44", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 476, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 352, "totalDailyPrice": 176, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "14415", "storeId": "23379"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1980093, "klbPId": 7736, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 7591, "rLevel": 7591, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "10.71", "c": "43", "v": "9651"}, "newEnergy": 0, "platform": 10, "kPSId": 115986, "kRSId": 115986, "kVId": 30912, "pLev": 55308, "rLev": 55308, "klbVersion": 0}, "sortNum": 7, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减44", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8388608, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 416, "checkType": 0}], "sortScore": 7, "storeScore": 97.32, "isSelect": false, "distance": 0, "rDistance": 2.7484, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "卢米租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 352, "amountStr": "¥352", "subAmount": 176, "subAmountStr": "日均¥176", "originalDailyPrice": 198, "detail": [{"code": "1001", "name": "租车费", "amount": 396, "amountDesc": "¥396"}, {"code": "3814", "name": "周边游特惠", "amount": 44, "amountDesc": "¥44"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 432, "amountStr": "¥432", "subAmount": 476, "subAmountStr": "¥476", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a9477878ab4d460fbe1adf2d4b236b66"}, {"vendorName": "立强租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "三亚店", "commentCount": 20, "qCommentCount": 20, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "commentLabel": "免费升级车", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 158, "currentOriginalDailyPrice": 0, "oTPrice": 456, "currentTotalPrice": 456, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD5486", "vendorCode": "79485", "pStoreCode": "218013", "rStoreCode": "218013", "vehicleCode": "188136", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "188136"}, "isEasyLife": true, "payMode": 2, "productCode": "SD5486_188136_218013_218013", "comPriceCode": "639997a84ba20a12fddc9842", "priceVersion": "AV4B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0wfv7TTkF9qZZD5XCJO4OtUET0W6PBA/0pygpFDL5W4SLwbsmBELwBye8UAj+dITaWyhNBk2niSwCr5jR6xgwagUHU1QXv8/Akhmtno8mtmMs/nXkuHmYCaCtpGEEaMSX77iAF2kxaBTLuFm3TdeMweLfJLo8tJEm+05rVZEj792nNykuh4p1ja7T331tWsN4ZPp0J/Gc1jSxj4CrOI1G42fdoWNkKV8Tcmexv4KTleLKIP0Fbh1/0l4sZ3LyTPzti4KtTmHcyeY5GKiNfySTvbZpX94Tdv96mL+a9C1HS7QA==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20097414", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 456, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 316, "totalDailyPrice": 158, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20097414", "storeId": "218013"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1976197, "klbPId": 9995, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 60758, "rLevel": 60758, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "9.51", "c": "43", "v": "81929"}, "newEnergy": 0, "platform": 10, "kPSId": 116918, "kRSId": 116918, "kVId": 79485, "pLev": 60758, "rLev": 60758, "klbVersion": 0}, "sortNum": 8, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 8, "colorCode": "8", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 2}, {"title": "一年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3510", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 2}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 67108864, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 11, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 124, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}], "sortScore": 8, "storeScore": 73.86, "isSelect": false, "distance": 0, "rDistance": 3.3083, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "立强租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 316, "amountStr": "¥316", "subAmount": 158, "subAmountStr": "日均¥158", "detail": [{"code": "1001", "name": "租车费", "amount": 316, "amountDesc": "¥316"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 456, "amountStr": "¥456", "subAmount": 456, "subAmountStr": "¥456", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "c0853e153612476b9df65888c61be2dc"}, {"vendorName": "文东租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "海南文东汽车服务有限公司", "commentCount": 31, "qCommentCount": 31, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "commentLabel": "店员热情", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 178, "currentOriginalDailyPrice": 200, "oTPrice": 480, "currentTotalPrice": 436, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 44, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4445", "vendorCode": "62863", "pStoreCode": "128087", "rStoreCode": "128087", "vehicleCode": "18456", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "18456"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4445_18456_128087_128087", "comPriceCode": "639997a8a67e477d0ec902ba", "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0y9q7V3/9BXyMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dpXdA7FRI228IViTVf+KyZDFKkHdSD+RXnRrGj9xfIHEzKJ2NTODxrH1sUaQ936ig1DQfPbwnHXoI1yi341QSYMMCe5LaKnV16bImkNBqFnHTs7sx5Ik0ex0tq6PfnzLpZwIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLukGzuhmZhIaFeWWrrNNAqQ8zjlzRthU2XA==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "14415", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减44", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 480, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 356, "totalDailyPrice": 178, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "14415", "storeId": "128087"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1919961, "klbPId": 4822, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 43934, "rLevel": 43934, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "10.5", "c": "43", "v": "62395"}, "newEnergy": 0, "platform": 10, "kPSId": 114447, "kRSId": 114447, "kVId": 62863, "pLev": 43934, "rLev": 43934, "klbVersion": 0}, "sortNum": 9, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减44", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 124, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 32, "checkType": 0}], "sortScore": 9, "storeScore": 99.57, "isSelect": false, "distance": 0, "rDistance": 16.6226, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "文东租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 356, "amountStr": "¥356", "subAmount": 178, "subAmountStr": "日均¥178", "originalDailyPrice": 200, "detail": [{"code": "1001", "name": "租车费", "amount": 400, "amountDesc": "¥400"}, {"code": "3814", "name": "周边游特惠", "amount": 44, "amountDesc": "¥44"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 436, "amountStr": "¥436", "subAmount": 480, "subAmountStr": "¥480", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "11bb343e1c71451eb2c93cb6171eaeed"}, {"vendorName": "鑫路达租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "鑫路达租车", "commentCount": 47, "qCommentCount": 47, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "commentLabel": "车辆很新", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 193, "currentOriginalDailyPrice": 217, "oTPrice": 579, "currentTotalPrice": 531, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 48, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD6669", "vendorCode": "80145", "pStoreCode": "218134", "rStoreCode": "218134", "vehicleCode": "188135", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "188135"}, "isEasyLife": true, "payMode": 2, "productCode": "SD6669_188135_218134_218134", "comPriceCode": "639997a8ca58db2f9d2d619b", "priceVersion": "AWEB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlR2j4tcqWeLy+hKrlD37X0wfv7TTkF9qZZD5XCJO4OtUET0W6PBA/0pygpFDL5W4SLwbsmBELwBye8UAj+dITaWyhNBk2niSwCr5jR6xgwagUHU1QXv8/AkNcwoc/Fen4cnRDnxsvFIO3RyR+AiTvpqF+VFDax+7GAk+WdFESK7yIuOOUF9sw85AI7DGBPDsCNI88z+35mbFwDBBa/JUOEjrA228uFEyKYwpXuON/EJqUeOvMESS0uvBxoSLkNe2Hn71LojuJBTTe8UAj+dITaXCNnci0raRuqozKONuzTAtQEjAaDK6eD9BLEfPPleoM7sQTiO+nB0E", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20097416", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减48", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 579, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 386, "totalDailyPrice": 193, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20097416", "storeId": "218134"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1980186, "klbPId": 10026, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 61167, "rLevel": 61167, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "6.81", "c": "43", "v": "83073"}, "newEnergy": 0, "platform": 10, "kPSId": 116930, "kRSId": 116930, "kVId": 80145, "pLev": 61167, "rLev": 61167, "klbVersion": 0}, "sortNum": 10, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 8, "colorCode": "8", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 2}, {"title": "一年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3510", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 2}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减48", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_4", "binaryDigit": 8, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 124, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}], "sortScore": 10, "storeScore": 0, "isSelect": false, "distance": 0, "rDistance": 3.5231, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "鑫路达租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 386, "amountStr": "¥386", "subAmount": 193, "subAmountStr": "日均¥193", "originalDailyPrice": 217, "detail": [{"code": "1001", "name": "租车费", "amount": 434, "amountDesc": "¥434"}, {"code": "3814", "name": "周边游特惠", "amount": 48, "amountDesc": "¥48"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 25, "amountStr": "¥25", "detail": [{"code": "1003", "name": "车行手续费", "amount": 25, "amountDesc": "¥25", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 531, "amountStr": "¥531", "subAmount": 579, "subAmountStr": "¥579", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "5bdb3dd5fcbd474e89512272d5a7d5b7"}, {"vendorName": "盛豪会租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "盛豪会租车", "commentCount": 26, "qCommentCount": 26, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "commentLabel": "车辆很新", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 98, "currentOriginalDailyPrice": 0, "oTPrice": 306, "currentTotalPrice": 306, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD4790", "vendorCode": "79723", "pStoreCode": "216211", "rStoreCode": "216211", "vehicleCode": "188134", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "188134"}, "isEasyLife": false, "payMode": 2, "productCode": "SD4790_188134_216211_216211", "comPriceCode": "639997a84ba20a12fddc9949", "priceVersion": "AVsB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlUkLATQm+UuH+hKrlD37X0yFB1O+qAq7LsChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/doktBWk9loPREYr0UA14RpGwGjJnbNvsFOF2SPhmVwUZslaoMDyloqiN7gUtHiwtEIpPhSLak6plGqX95ytHOvol/tZIDliDZW7EB0f552oBi6be+kpHiWDZ/dbM+tK/AWHORFPZ+cN9yYrDcq1PDL3Ly8p67UobGUVY3swZwHTd5qhccdJxj0R+nXYb0BtbH/zrY58bbULlw==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20097418", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 306, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 196, "totalDailyPrice": 98, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20097418", "storeId": "216211"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1924514, "klbPId": 6132, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 62251, "rLevel": 62251, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "22.82", "c": "43", "v": "82359"}, "newEnergy": 0, "platform": 10, "kPSId": 114993, "kRSId": 114993, "kVId": 79723, "pLev": 47598, "rLev": 47598, "klbVersion": 0}, "sortNum": 11, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 268435456, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 7, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 88, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 176, "checkType": 0}], "sortScore": 11, "storeScore": 94.45, "isSelect": false, "distance": 0, "rDistance": 2.9659, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "盛豪会租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 196, "amountStr": "¥196", "subAmount": 98, "subAmountStr": "日均¥98", "detail": [{"code": "1001", "name": "租车费", "amount": 196, "amountDesc": "¥196"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "name": "车行手续费", "amount": 30, "amountDesc": "¥30", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 306, "amountStr": "¥306", "subAmount": 306, "subAmountStr": "¥306", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "09f341e3277241e8a20205f27d27432a"}, {"vendorName": "金晟利租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "金晟利", "commentCount": 150, "qCommentCount": 150, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "commentLabel": "车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 228, "currentOriginalDailyPrice": 0, "oTPrice": 556, "currentTotalPrice": 556, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD4737", "vendorCode": "77151", "pStoreCode": "185588", "rStoreCode": "185588", "vehicleCode": "76338", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "76338"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4737_76338_185588_185588", "comPriceCode": "639997a84ba20a12fddc97a5", "priceVersion": "AV4B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbc+hKrlD37X0yFB1O+qAq7LsChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dq9d2jt0+7bCRj4CrOI1G42Go55HyQQFAMtve49xdKAs6YfTWHKUJYDwsbWidGqqaQDLBGiwc/SYUPhW00PFblF7T331tWsN4ZQvEaGSybv2Rj4CrOI1G42v7c+ZBfylb0mexv4KTleLKIP0Fbh1/0l4sZ3LyTPztgEkfcwRd7BorgMAa30fFw6F/nTIqbC0Xb542YBaj6a0Q==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20037374", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 556, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 456, "totalDailyPrice": 228, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20037374", "storeId": "185588"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1914962, "klbPId": 5719, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 47158, "rLevel": 47158, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "6.16", "c": "43", "v": "79103"}, "newEnergy": 0, "platform": 10, "kPSId": 114590, "kRSId": 114590, "kVId": 77151, "pLev": 47158, "rLev": 47158, "klbVersion": 0}, "sortNum": 12, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 65536, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 124, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "sortScore": 12, "storeScore": 93.79, "isSelect": false, "distance": 0, "rDistance": 3.5861, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "金晟利租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 456, "amountStr": "¥456", "subAmount": 228, "subAmountStr": "日均¥228", "detail": [{"code": "1001", "name": "租车费", "amount": 456, "amountDesc": "¥456"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 556, "amountStr": "¥556", "subAmount": 556, "subAmountStr": "¥556", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "5ce044193bc8495fa5bfc20f2491766a"}, {"vendorName": "漫自由租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "漫自由三亚店", "commentCount": 2350, "qCommentCount": 2350, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "commentLabel": "车辆很新", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 439, "currentOriginalDailyPrice": 499, "oTPrice": 1093, "currentTotalPrice": 973, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 120, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3933", "vendorCode": "46492", "pStoreCode": "111846", "rStoreCode": "111846", "vehicleCode": "35425", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "35425"}, "isEasyLife": false, "payMode": 2, "productCode": "SD3933_35425_111846_111846", "comPriceCode": "639997a8a67e477d0ec9036b", "priceVersion": "AWEB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZX+hKrlD37X0y9q7V3/9BXyMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dqSGM9YxlC2CIViTVf+KyZDAwj6kMeqUa/RrGj9xfIHE+ADmSCiDwF51sUaQ936ig1DQfPbwnHXoE7v+triVn9iMCe5LaKnV164NbQR4jEzDzs7sx5Ik0exkjmi/Ih9yBPBxoSLkNe2Hn71LojuJBTTe8UAj+dITaXCNnci0raRumbdVY6aA/ErQEjAaDK6eD+tr4j+7XaQlbsQTiO+nB0E", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20024622", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减120", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 1093, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 878, "totalDailyPrice": 439, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20024622", "storeId": "111846"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1863431, "klbPId": 1715, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 23868, "rLevel": 23868, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.81", "c": "43", "v": "55251"}, "newEnergy": 0, "platform": 10, "kPSId": 106965, "kRSId": 106965, "kVId": 46492, "pLev": 23868, "rLev": 23868, "klbVersion": 0}, "sortNum": 13, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减120", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 256, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 11, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 88, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}], "sortScore": 13, "storeScore": 94.83, "isSelect": false, "distance": 0, "rDistance": 3.0761, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3743", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "漫自由租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 878, "amountStr": "¥878", "subAmount": 439, "subAmountStr": "日均¥439", "originalDailyPrice": 499, "detail": [{"code": "1001", "name": "租车费", "amount": 998, "amountDesc": "¥998"}, {"code": "3743", "name": "周三福利日", "amount": 120, "amountDesc": "¥120"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 973, "amountStr": "¥973", "subAmount": 1093, "subAmountStr": "¥1093", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "f6066b7d600c46c4966a297aacf481ad"}, {"vendorName": "顺利出行", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "顺利出行", "commentCount": 38, "qCommentCount": 38, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 99, "currentOriginalDailyPrice": 0, "oTPrice": 353, "currentTotalPrice": 353, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD5100", "vendorCode": "83315", "pStoreCode": "620619", "rStoreCode": "620619", "vehicleCode": "35422", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "35422"}, "isEasyLife": false, "payMode": 2, "productCode": "SD5100_35422_620619_620619", "comPriceCode": "639997a84ba20a12fddc9938", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZX+hKrlD37X0wfv7TTkF9qZZD5XCJO4OtUET0W6PBA/0pygpFDL5W4SLwbsmBELwBye8UAj+dITaWyhNBk2niSwCr5jR6xgwagUHU1QXv8/AlpRNuDtiDF4Rj4CrOI1G423CcL6F11hKctve49xdKAs4clPZZ/WxvxpZpEoTnxeD6ULMw9QMWpSCX4u/eBFfWNaA3FdkEq28RzcpLoeKdY2mka9A+bvex/kZUXkJ8NkYvBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZODKfkwiY3J9ZTM4MachP5q8ulSKegXyFHQzePXnNcQ8A==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20024619", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 353, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 198, "totalDailyPrice": 99, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20024619", "storeId": "620619"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1911226, "klbPId": 6962, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 50275, "rLevel": 50275, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "16.68", "c": "43", "v": "89279"}, "newEnergy": 0, "platform": 10, "kPSId": 115358, "kRSId": 115358, "kVId": 83315, "pLev": 50275, "rLev": 50275, "klbVersion": 0}, "sortNum": 14, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_6", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 88, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 416, "checkType": 0}], "sortScore": 14, "storeScore": 98.3, "isSelect": false, "distance": 0, "rDistance": 2.9742, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "顺利出行", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 198, "amountStr": "¥198", "subAmount": 99, "subAmountStr": "日均¥99", "detail": [{"code": "1001", "name": "租车费", "amount": 198, "amountDesc": "¥198"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 353, "amountStr": "¥353", "subAmount": 353, "subAmountStr": "¥353", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "0df576a1ff134465a7f24c78e2e31ffb"}, {"vendorName": "大方租车", "isMinTPriceVendor": false, "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/dfzc.jpg", "commentInfo": {"level": "超棒", "vendorDesc": "回新路店", "commentCount": 1, "qCommentCount": 1, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 47, "currentOriginalDailyPrice": 53, "oTPrice": 216, "currentTotalPrice": 203, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 13, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3013", "vendorCode": "13036", "pStoreCode": "230047", "rStoreCode": "230047", "vehicleCode": "146056", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "146056"}, "isEasyLife": false, "payMode": 2, "productCode": "SD3013_146056_230047_230047", "priceVersion": "AVcB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlUkLATQm+UuHCJO9j9gLhryT0694D6+4cNS0Tb1hyQbzqrmv+HNaKpEnY6qhxh7qlRu+IG/z4VxBk5Z7BpMJCnJaKrkDQzTK3AZh2R3cP5DoFq2qx/Yxe8Utve49xdKAs/a0q5stSiRiC/qKYJnH/GmK82PzBRG2bjDxQ+hWIh3+kZROc17UIqfNgOKrBma5py1wmMHgh3DCqgbe0vph90KMKV7jjfxCahwrz7xocOgOcCMFBToyFtbIrmeVE7ktq3VUHnv1IL9/S4SjvuTJlmoxDb5GeMt+V7NgwTYIY4C6QRAT7GEjO7M=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "29570", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减13", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 216, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 93, "totalDailyPrice": 47, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "29570", "storeId": "230047"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 217321, "klbPId": 9, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 6436, "rLevel": 6436, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "56.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 38424, "kRSId": 38424, "kVId": 13036, "pLev": 34642, "rLev": 34642, "klbVersion": 0}, "sortNum": 15, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减13", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 7, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "sortScore": 15, "storeScore": 0, "isSelect": false, "stock": 1, "distance": 0, "rDistance": 6.4396, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3743", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "大方租车", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 93, "amountStr": "¥93", "subAmount": 47, "subAmountStr": "日均¥47", "originalDailyPrice": 53, "detail": [{"code": "1001", "name": "租车费", "amount": 106, "amountDesc": "¥106"}, {"code": "3743", "name": "周三福利日", "amount": 13, "amountDesc": "¥13"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "name": "车行手续费", "amount": 30, "amountDesc": "¥30", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 203, "amountStr": "¥203", "subAmount": 216, "subAmountStr": "¥216", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "3f7cff4718b6468bb50757e1cc34a43f"}, {"vendorName": "凹凸出行", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "", "vendorDesc": "三亚-机场店", "commentCount": 1, "qCommentCount": 1, "qExposed": "3.3", "overallRating": "3.3", "maximumRating": 5, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 74, "currentOriginalDailyPrice": 0, "oTPrice": 263, "currentTotalPrice": 263, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD3010", "vendorCode": "13027", "pStoreCode": "386971", "rStoreCode": "386971", "vehicleCode": "140411", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "140411"}, "isEasyLife": false, "payMode": 2, "productCode": "SD3010_140411_386971_386971", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZXCJO9j9gLhryT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7Uixlv3FOddkNUoAtIy+cOdA4XZI+GZXBRmWfwq5Rlue1XJ0Q58bLxSDi0/aX5chXywhflRQ2sfuxgL1phId+7JddtTu24C4IkU7T331tWsN4Ylstov0F+eiDs7sx5Ik0exsJErYUbDuN9c6mqeALM80MHBRuJlQ0JBVl0JptPjK9dCi62hKwD5orvissa2lIfo6KiXkRv7LYS6PPutcO617w==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "428689_PMS", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 263, "isrec": false, "recommendOrder": 0, "mergeId": 1582, "rectype": 1, "cvid": 14415, "rentalamount": 148, "totalDailyPrice": 74, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "428689_PMS", "storeId": "386971"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1201341, "klbPId": 20, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 1, "rLevel": 1, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "31.81", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 48587, "kRSId": 48587, "kVId": 13027, "pLev": 7535, "rLev": 7535, "klbVersion": 0}, "sortNum": 16, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 16, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "sortScore": 16, "storeScore": 0, "isSelect": false, "stock": 1, "distance": 0, "rDistance": 2.2768, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "凹凸出行", "card": 0, "ctripVehicleCode": "14415", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 148, "amountStr": "¥148", "subAmount": 74, "subAmountStr": "日均¥74", "detail": [{"code": "1001", "name": "租车费", "amount": 148, "amountDesc": "¥148"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 263, "amountStr": "¥263", "subAmount": 263, "subAmountStr": "¥263", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "ee1d96ed67cc4ca2828b6ec720f9df98"}]}, "recommendProducts": [{"vehicleCode": "3060", "sortNum": 1, "lowestPrice": 25.0, "highestPrice": 239.0, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 0.0, "vendorPriceList": [{"vendorName": "calabi-骑仕租车A", "isMinTPriceVendor": true, "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/30147.jpg", "commentInfo": {"level": "", "vendorDesc": "机场店", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 25.0, "currentOriginalDailyPrice": 75, "oTPrice": 230.0, "currentTotalPrice": 130.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBSQV1KzLIn4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThAywRosHP0mFZmyXbdtjrYRj4CrOI1G42XthjezdN5Z8tve49xdKAs+ZMc9eDcalapZpEoTnxeD5zr9EuGKS1LiX4u/eBFfWNaA3FdkEq28QcKYzK02BzXSnWS920DkwxQ6q3jYfmb0TBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZP5ezvp11gpRbl9c5XVw4ammVShtdZkZv2QvI+WI+mP2Q==", "priceType": 1, "deductInfos": [{"totalAmount": 100.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD30147", "vendorCode": "30147", "pStoreCode": "106878", "rStoreCode": "106878", "vehicleCode": "0", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD30147_0_106878_106878", "comPriceCode": "63dba621667fb177dd188767", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBSQV1KzLIn4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThAywRosHP0mFZmyXbdtjrYRj4CrOI1G42XthjezdN5Z8tve49xdKAs+ZMc9eDcalapZpEoTnxeD5zr9EuGKS1LiX4u/eBFfWNaA3FdkEq28QcKYzK02BzXSnWS920DkwxQ6q3jYfmb0TBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZP5ezvp11gpRbl9c5XVw4ammVShtdZkZv2QvI+WI+mP2Q==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20059442", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 230.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3060, "rentalamount": 50.0, "totalDailyPrice": 25.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 1857847, "klbPId": 1609, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 22341, "rLevel": 22341, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43", "v": "1937"}, "newEnergy": 0, "platform": 10, "kPSId": 106878, "kRSId": 106878, "kVId": 30147, "pLev": 22341, "rLev": 22341, "klbVersion": 1, "kVehicleId": 3060}, "sortNum": 0.0, "pStoreRouteDesc": "免费站内取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 70, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 8, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 28, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}], "storeScore": 100.0, "isSelect": true, "distance": 0.0, "rDistance": 0.2884, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3487", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "calabi-骑仕租车A", "card": 0, "ctripVehicleCode": "3060", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 50.0, "amountStr": "¥50", "subAmount": 25.0, "subAmountStr": "日均¥25", "originalDailyPrice": 75, "detail": [{"code": "1001", "name": "租车费", "amount": 150, "amountDesc": "¥150"}, {"code": "3487", "name": "元旦特惠", "amount": 100, "amountDesc": "¥100"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 130.0, "amountStr": "¥130", "subAmount": 230.0, "subAmountStr": "¥230", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "鹏顺通租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "", "vendorDesc": "鹏顺通租车", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 155.0, "currentOriginalDailyPrice": 158, "oTPrice": 431.0, "currentTotalPrice": 425.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBSQV1KzLIn4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/CJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw9wHK37Wj7nB8/nXkuHmYCahCcIcj4OwpP7iAF2kxaBTLuFm3TdeMweLfJLo8tJEm+05rVZEj792qlwi+oD3X8jLXCYweCHcMIt9U7bzraSIs/nXkuHmYCaW8NbGZZfNf9c6mqeALM80MHBRuJlQ0JBI5Vg/dNzVr+xwwmT/aGtJuHQgmDLtLmws2DBNghjgLqsAKRUOR+Lmw==", "priceType": 1, "deductInfos": [{"totalAmount": 6.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD79747", "vendorCode": "79747", "pStoreCode": "116842", "rStoreCode": "116842", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD79747_0_116842_116842", "comPriceCode": "63dba621a73e3b7a6792acfc", "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBSQV1KzLIn4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/CJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw9wHK37Wj7nB8/nXkuHmYCahCcIcj4OwpP7iAF2kxaBTLuFm3TdeMweLfJLo8tJEm+05rVZEj792qlwi+oD3X8jLXCYweCHcMIt9U7bzraSIs/nXkuHmYCaW8NbGZZfNf9c6mqeALM80MHBRuJlQ0JBI5Vg/dNzVr+xwwmT/aGtJuHQgmDLtLmws2DBNghjgLqsAKRUOR+Lmw==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20059443", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 431.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3060, "rentalamount": 310.0, "totalDailyPrice": 155.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1976589, "klbPId": 9797, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 60966, "rLevel": 60966, "promtId": 606353347, "rCoup": 0, "sortInfo": {"p": "1", "s": "8.18", "c": "43", "v": "82397"}, "newEnergy": 0, "platform": 10, "kPSId": 116842, "kRSId": 116842, "kVId": 79747, "pLev": 60966, "rLev": 60966, "klbVersion": 1, "kVehicleId": 3060}, "sortNum": 1.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 536870912, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 3.6196, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "鹏顺通租车", "card": 0, "ctripVehicleCode": "3060", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 310.0, "amountStr": "¥310", "subAmount": 155.0, "subAmountStr": "日均¥155", "originalDailyPrice": 158, "detail": [{"code": "1001", "name": "租车费", "amount": 316, "amountDesc": "¥316"}, {"code": "11037", "name": "优惠券", "amount": 6, "amountDesc": "¥6"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 425.0, "amountStr": "¥425", "subAmount": 431.0, "subAmountStr": "¥431", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "炜晨租车", "isMinTPriceVendor": false, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/79695.jpg", "commentInfo": {"level": "", "vendorDesc": "炜晨租车", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 167.0, "currentOriginalDailyPrice": 188, "oTPrice": 506.0, "currentTotalPrice": 464.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBSQV1KzLIn4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOcS/Sadeigi1sChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dr242sGN6b6YoViTVf+KyZDg7rXpHBDH3bRrGj9xfIHEypuNwVMrKqR1sUaQ936ig1DQfPbwnHXoFWfng+6T9xaMCe5LaKnV17R1+Qz6UxT5Ds7sx5Ik0exN12OhgBSYttwIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLuughzWkmNqXceWWrrNNAqQ9nt7O5hA9aXA==", "priceType": 1, "deductInfos": [{"totalAmount": 42.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD79695", "vendorCode": "79695", "pStoreCode": "107033", "rStoreCode": "107033", "vehicleCode": "0", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD79695_0_107033_107033", "comPriceCode": "63dba621a73e3b7a6792aaf3", "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBSQV1KzLIn4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOcS/Sadeigi1sChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dr242sGN6b6YoViTVf+KyZDg7rXpHBDH3bRrGj9xfIHEypuNwVMrKqR1sUaQ936ig1DQfPbwnHXoFWfng+6T9xaMCe5LaKnV17R1+Qz6UxT5Ds7sx5Ik0exN12OhgBSYttwIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLuughzWkmNqXceWWrrNNAqQ9nt7O5hA9aXA==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20059442", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减42", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 506.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3060, "rentalamount": 334.0, "totalDailyPrice": 167.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1858311, "klbPId": 1904, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 77639, "rLevel": 77639, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "6.79", "c": "43", "v": "82323"}, "newEnergy": 0, "platform": 10, "kPSId": 107033, "kRSId": 107033, "kVId": 79695, "pLev": 77639, "rLev": 77639, "klbVersion": 1, "kVehicleId": 3060}, "sortNum": 2.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减42", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 134217728, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 28, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 1.273, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "炜晨租车", "card": 0, "ctripVehicleCode": "3060", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 334.0, "amountStr": "¥334", "subAmount": 167.0, "subAmountStr": "日均¥167", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 376, "amountDesc": "¥376"}, {"code": "3814", "name": "周边游特惠", "amount": 42, "amountDesc": "¥42"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 110, "amountStr": "¥110", "detail": [{"code": "1002", "name": "基础服务费", "amount": 110, "amountDesc": "¥110", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 464.0, "amountStr": "¥464", "subAmount": 506.0, "subAmountStr": "¥506", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "轩琪租车", "isMinTPriceVendor": false, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/78963.jpg", "commentInfo": {"level": "", "vendorDesc": "轩琪租车", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 239.0, "currentOriginalDailyPrice": 268, "oTPrice": 696.0, "currentTotalPrice": 637.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBSQV1KzLIn4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOfHYojF7kfmy8ChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dpb6cXG/SxQSoViTVf+KyZDdPkrMHzcnxHRrGj9xfIHE0Ot2pi8rzDQ1sUaQ936ig1DQfPbwnHXoMKuu8plWWQRMCe5LaKnV15lOKL0RQixujs7sx5Ik0exO/XmUBgPQW9wIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLumsP4ZLuLZcLeWWrrNNAqQ+1R5pb8EWbmQ==", "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD78963", "vendorCode": "78963", "pStoreCode": "114946", "rStoreCode": "114946", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD78963_0_114946_114946", "comPriceCode": "63dba621a73e3b7a6792a9fd", "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBSQV1KzLIn4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOfHYojF7kfmy8ChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dpb6cXG/SxQSoViTVf+KyZDdPkrMHzcnxHRrGj9xfIHE0Ot2pi8rzDQ1sUaQ936ig1DQfPbwnHXoMKuu8plWWQRMCe5LaKnV15lOKL0RQixujs7sx5Ik0exO/XmUBgPQW9wIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLumsP4ZLuLZcLeWWrrNNAqQ+1R5pb8EWbmQ==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20059443", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 696.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3060, "rentalamount": 477.0, "totalDailyPrice": 239.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1923518, "klbPId": 6035, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 47336, "rLevel": 47336, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "3.48", "c": "43", "v": "81163"}, "newEnergy": 0, "platform": 10, "kPSId": 114946, "kRSId": 114946, "kVId": 78963, "pLev": 47336, "rLev": 47336, "klbVersion": 1, "kVehicleId": 3060}, "sortNum": 3.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减59", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 16777216, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 11.8226, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "轩琪租车", "card": 0, "ctripVehicleCode": "3060", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 477.0, "amountStr": "¥477", "subAmount": 239.0, "subAmountStr": "日均¥239", "originalDailyPrice": 268, "detail": [{"code": "1001", "name": "租车费", "amount": 536, "amountDesc": "¥536"}, {"code": "3814", "name": "周边游特惠", "amount": 59, "amountDesc": "¥59"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 140, "amountStr": "¥140", "detail": [{"code": "1002", "name": "基础服务费", "amount": 140, "amountDesc": "¥140", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 637.0, "amountStr": "¥637", "subAmount": 696.0, "subAmountStr": "¥696", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30147_0_106878_106878"]}, "minTPrice": 130.0, "minDPrice": 25.0, "modifySameVehicle": false, "priceSize": 4, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "3059", "sortNum": 35, "lowestPrice": 28.0, "highestPrice": 1780.0, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 0.0, "vendorPriceList": [{"vendorName": "calabi-骑仕租车A", "isMinTPriceVendor": true, "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/30147.jpg", "commentInfo": {"level": "", "vendorDesc": "三亚湾店", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 28.0, "currentOriginalDailyPrice": 78, "oTPrice": 236.0, "currentTotalPrice": 136.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThAywRosHP0mFtdmYH2LzUSRj4CrOI1G42/40PYttw3Sktve49xdKAs9sFeXkr4HYEpZpEoTnxeD7ynuDgNGWrRCX4u/eBFfWNaA3FdkEq28QS18foxW6zTynWS920DkwxiF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZM32fm7S54qyXZ9EeAvY1hhmVShtdZkZv0lYE0J7Fi0GA==", "priceType": 1, "deductInfos": [{"totalAmount": 100.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD30147", "vendorCode": "30147", "pStoreCode": "106883", "rStoreCode": "106883", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD30147_0_106883_106883", "comPriceCode": "63dba621667fb177dd1886eb", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThAywRosHP0mFtdmYH2LzUSRj4CrOI1G42/40PYttw3Sktve49xdKAs9sFeXkr4HYEpZpEoTnxeD7ynuDgNGWrRCX4u/eBFfWNaA3FdkEq28QS18foxW6zTynWS920DkwxiF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZM32fm7S54qyXZ9EeAvY1hhmVShtdZkZv0lYE0J7Fi0GA==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "108", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 236.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3059, "rentalamount": 56.0, "totalDailyPrice": 28.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1855386, "klbPId": 1609, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 22342, "rLevel": 22342, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "2083"}, "newEnergy": 0, "platform": 10, "kPSId": 106883, "kRSId": 106883, "kVId": 30147, "pLev": 22342, "rLev": 22342, "klbVersion": 1, "kVehicleId": 3059}, "sortNum": 0.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 8, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 0.76, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3487", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "calabi-骑仕租车A", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 56.0, "amountStr": "¥56", "subAmount": 28.0, "subAmountStr": "日均¥28", "originalDailyPrice": 78, "detail": [{"code": "1001", "name": "租车费", "amount": 156, "amountDesc": "¥156"}, {"code": "3487", "name": "元旦特惠", "amount": 100, "amountDesc": "¥100"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 136.0, "amountStr": "¥136", "subAmount": 236.0, "subAmountStr": "¥236", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "易云租车", "isMinTPriceVendor": false, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/61953.jpg", "commentInfo": {"level": "", "vendorDesc": "三亚易云租车", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 115.0, "currentOriginalDailyPrice": 118, "oTPrice": 341.0, "currentTotalPrice": 335.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2Csq2PzvOWjqCJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw/0ILNGKDb7aM/nXkuHmYCaY+KprFhOJPr7iAF2kxaBTKtC5Ij9WJgrLfJLo8tJEm+05rVZEj792u2hjbr6qXMXLXCYweCHcMJsnXwX1iy1yM/nXkuHmYCarnMKR36/c+xc6mqeALM80MHBRuJlQ0JBI5Vg/dNzVr+xwwmT/aGtJiBd7FoRVERHs2DBNghjgLqYlKMbEVdlUg==", "priceType": 1, "deductInfos": [{"totalAmount": 6.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD61953", "vendorCode": "61953", "pStoreCode": "114317", "rStoreCode": "114317", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD61953_0_114317_114317", "comPriceCode": "63dba621667fb177dd18857e", "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2Csq2PzvOWjqCJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw/0ILNGKDb7aM/nXkuHmYCaY+KprFhOJPr7iAF2kxaBTKtC5Ij9WJgrLfJLo8tJEm+05rVZEj792u2hjbr6qXMXLXCYweCHcMJsnXwX1iy1yM/nXkuHmYCarnMKR36/c+xc6mqeALM80MHBRuJlQ0JBI5Vg/dNzVr+xwwmT/aGtJiBd7FoRVERHs2DBNghjgLqYlKMbEVdlUg==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20019531", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 341.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3059, "rentalamount": 230.0, "totalDailyPrice": 115.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1915939, "klbPId": 4328, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 43387, "rLevel": 43387, "promtId": 606353347, "rCoup": 0, "sortInfo": {"p": "1", "s": "14.61", "c": "43", "v": "61230"}, "newEnergy": 0, "platform": 10, "kPSId": 114317, "kRSId": 114317, "kVId": 61953, "pLev": 43387, "rLev": 43387, "klbVersion": 1, "kVehicleId": 3059}, "sortNum": 1.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 524288, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 1.1391, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "易云租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 230.0, "amountStr": "¥230", "subAmount": 115.0, "subAmountStr": "日均¥115", "originalDailyPrice": 118, "detail": [{"code": "1001", "name": "租车费", "amount": 236, "amountDesc": "¥236"}, {"code": "11037", "name": "优惠券", "amount": 6, "amountDesc": "¥6"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 25.0, "amountStr": "¥25", "detail": [{"code": "1003", "name": "车行手续费", "amount": 25.0, "amountDesc": "¥25", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 335.0, "amountStr": "¥335", "subAmount": 341.0, "subAmountStr": "¥341", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "椰林情租车", "isMinTPriceVendor": false, "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/30777.jpg", "commentInfo": {"level": "", "vendorDesc": "华庭天下店", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 141.0, "currentOriginalDailyPrice": 158, "oTPrice": 416.0, "currentTotalPrice": 381.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZCJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw+QvZLnnf1MaM/nXkuHmYCaT6HEKccml8H7iAF2kxaBTLuFm3TdeMweLfJLo8tJEm+05rVZEj792qlwi+oD3X8jLXCYweCHcMIt9U7bzraSIs/nXkuHmYCaHkYgctmjGEVc6mqeALM80MHBRuJlQ0JBI5Vg/dNzVr+xwwmT/aGtJjuMoEcTx3Lgs2DBNghjgLr66ESzD2OFCw==", "priceType": 1, "deductInfos": [{"totalAmount": 35.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD30777", "vendorCode": "30777", "pStoreCode": "114080", "rStoreCode": "114080", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD30777_0_114080_114080", "comPriceCode": "63dba621667fb177dd188588", "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZCJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw+QvZLnnf1MaM/nXkuHmYCaT6HEKccml8H7iAF2kxaBTLuFm3TdeMweLfJLo8tJEm+05rVZEj792qlwi+oD3X8jLXCYweCHcMIt9U7bzraSIs/nXkuHmYCaHkYgctmjGEVc6mqeALM80MHBRuJlQ0JBI5Vg/dNzVr+xwwmT/aGtJjuMoEcTx3Lgs2DBNghjgLr66ESzD2OFCw==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "108", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减35", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 416.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3059, "rentalamount": 281.0, "totalDailyPrice": 141.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1908285, "klbPId": 3738, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 41291, "rLevel": 41291, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "11.1", "c": "43", "v": "11034"}, "newEnergy": 0, "platform": 10, "kPSId": 114080, "kRSId": 114080, "kVId": 30777, "pLev": 41291, "rLev": 41291, "klbVersion": 1, "kVehicleId": 3059}, "sortNum": 2.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减35", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 131072, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 0.8674, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "椰林情租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 281.0, "amountStr": "¥281", "subAmount": 141.0, "subAmountStr": "日均¥141", "originalDailyPrice": 158, "detail": [{"code": "1001", "name": "租车费", "amount": 316, "amountDesc": "¥316"}, {"code": "3814", "name": "周边游特惠", "amount": 35, "amountDesc": "¥35"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 381.0, "amountStr": "¥381", "subAmount": 416.0, "subAmountStr": "¥416", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "环岛租车", "isMinTPriceVendor": false, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/66324.jpg", "commentInfo": {"level": "", "vendorDesc": "环岛租车机场店", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 167.0, "currentOriginalDailyPrice": 188, "oTPrice": 516.0, "currentTotalPrice": 474.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dr242sGN6b6YoViTVf+KyZDlLSuq6m0kqrRrGj9xfIHEypuNwVMrKqR1sUaQ936ig1DQfPbwnHXoFWfng+6T9xaMCe5LaKnV17R1+Qz6UxT5Ds7sx5Ik0ex0PfBPiDU2z1wIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLujvpa/2F+r3DeWWrrNNAqQ/sdwEKwmn8dg==", "priceType": 1, "deductInfos": [{"totalAmount": 42.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD66324", "vendorCode": "66324", "pStoreCode": "114731", "rStoreCode": "114731", "vehicleCode": "0", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD66324_0_114731_114731", "comPriceCode": "63dba621667fb177dd188581", "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dr242sGN6b6YoViTVf+KyZDlLSuq6m0kqrRrGj9xfIHEypuNwVMrKqR1sUaQ936ig1DQfPbwnHXoFWfng+6T9xaMCe5LaKnV17R1+Qz6UxT5Ds7sx5Ik0ex0PfBPiDU2z1wIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLujvpa/2F+r3DeWWrrNNAqQ/sdwEKwmn8dg==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20063249", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减42", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 516.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3059, "rentalamount": 334.0, "totalDailyPrice": 167.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1909929, "klbPId": 5544, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 44669, "rLevel": 44669, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "6.97", "c": "43", "v": "68196"}, "newEnergy": 0, "platform": 10, "kPSId": 114731, "kRSId": 114731, "kVId": 66324, "pLev": 44669, "rLev": 44669, "klbVersion": 1, "kVehicleId": 3059}, "sortNum": 3.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减42", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 8192, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 28, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 3.2185, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "环岛租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 334.0, "amountStr": "¥334", "subAmount": 167.0, "subAmountStr": "日均¥167", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 376, "amountDesc": "¥376"}, {"code": "3814", "name": "周边游特惠", "amount": 42, "amountDesc": "¥42"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 474.0, "amountStr": "¥474", "subAmount": 516.0, "subAmountStr": "¥516", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "玖捌陆租车", "isMinTPriceVendor": false, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/62167jpg", "commentInfo": {"level": "", "vendorDesc": "986汽车租赁", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 215.0, "currentOriginalDailyPrice": 218, "oTPrice": 536.0, "currentTotalPrice": 530.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZCJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThAywRosHP0mH0ILNGKDb7aM/nXkuHmYCa29MuEzoJODf7iAF2kxaBTBZeKcPlNiTvLfJLo8tJEm+05rVZEj792mYrUmQcLUFkLXCYweCHcMLW0sRpQwlTs8/nXkuHmYCabeIGCr6jpEpc6mqeALM80MHBRuJlQ0JBI5Vg/dNzVr+xwwmT/aGtJlYjTeKf1Q0Cs2DBNghjgLrqMorL/21obw==", "priceType": 1, "deductInfos": [{"totalAmount": 6.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD62167", "vendorCode": "62167", "pStoreCode": "114325", "rStoreCode": "114325", "vehicleCode": "0", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD62167_0_114325_114325", "comPriceCode": "63dba621a73e3b7a6792a734", "priceVersion": "AV8B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZCJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThAywRosHP0mH0ILNGKDb7aM/nXkuHmYCa29MuEzoJODf7iAF2kxaBTBZeKcPlNiTvLfJLo8tJEm+05rVZEj792mYrUmQcLUFkLXCYweCHcMLW0sRpQwlTs8/nXkuHmYCabeIGCr6jpEpc6mqeALM80MHBRuJlQ0JBI5Vg/dNzVr+xwwmT/aGtJlYjTeKf1Q0Cs2DBNghjgLrqMorL/21obw==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20063250", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 536.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3059, "rentalamount": 430.0, "totalDailyPrice": 215.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1920916, "klbPId": 4349, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 43591, "rLevel": 43591, "promtId": 606353347, "rCoup": 0, "sortInfo": {"p": "1", "s": "5.49", "c": "43", "v": "61497"}, "newEnergy": 0, "platform": 10, "kPSId": 114325, "kRSId": 114325, "kVId": 62167, "pLev": 43591, "rLev": 43591, "klbVersion": 1, "kVehicleId": 3059}, "sortNum": 4.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 536870912, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 11, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 28, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 2.3924, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "玖捌陆租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 430.0, "amountStr": "¥430", "subAmount": 215.0, "subAmountStr": "日均¥215", "originalDailyPrice": 218, "detail": [{"code": "1001", "name": "租车费", "amount": 436, "amountDesc": "¥436"}, {"code": "11037", "name": "优惠券", "amount": 6, "amountDesc": "¥6"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 530.0, "amountStr": "¥530", "subAmount": 536.0, "subAmountStr": "¥536", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "宝驰租车", "isMinTPriceVendor": false, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/61966.jpg", "commentInfo": {"level": "", "vendorDesc": "宝驰租车", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 218.0, "currentOriginalDailyPrice": 268, "oTPrice": 691.0, "currentTotalPrice": 591.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/aWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dqKuIbRYjnEioViTVf+KyZDEY9Dgov09BjRrGj9xfIHE0Ot2pi8rzDQ1sUaQ936ig1DQfPbwnHXoMKuu8plWWQRMCe5LaKnV15lOKL0RQixujs7sx5Ik0exgtJFS9jBN/VwIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLuua89+RWan/aeWWrrNNAqQ9Ll7TJFf7/+A==", "priceType": 1, "deductInfos": [{"totalAmount": 100.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD61966", "vendorCode": "61966", "pStoreCode": "114322", "rStoreCode": "114322", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD61966_0_114322_114322", "comPriceCode": "63dba621667fb177dd18830c", "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/aWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dqKuIbRYjnEioViTVf+KyZDEY9Dgov09BjRrGj9xfIHE0Ot2pi8rzDQ1sUaQ936ig1DQfPbwnHXoMKuu8plWWQRMCe5LaKnV15lOKL0RQixujs7sx5Ik0exgtJFS9jBN/VwIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLuua89+RWan/aeWWrrNNAqQ9Ll7TJFf7/+A==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20019532", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 691.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3059, "rentalamount": 436.0, "totalDailyPrice": 218.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1912609, "klbPId": 4343, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 43416, "rLevel": 43416, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "4.35", "c": "43", "v": "61242"}, "newEnergy": 0, "platform": 10, "kPSId": 114322, "kRSId": 114322, "kVId": 61966, "pLev": 43416, "rLev": 43416, "klbVersion": 1, "kVehicleId": 3059}, "sortNum": 5.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 1048576, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 8, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 4.7399, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3487", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "宝驰租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 436.0, "amountStr": "¥436", "subAmount": 218.0, "subAmountStr": "日均¥218", "originalDailyPrice": 268, "detail": [{"code": "1001", "name": "租车费", "amount": 536, "amountDesc": "¥536"}, {"code": "3487", "name": "元旦特惠", "amount": 100, "amountDesc": "¥100"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 591.0, "amountStr": "¥591", "subAmount": 691.0, "subAmountStr": "¥691", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "铭途租车", "isMinTPriceVendor": false, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/70698.jpg", "commentInfo": {"level": "", "vendorDesc": "铭途租车", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 532.0, "currentOriginalDailyPrice": 598, "oTPrice": 1336.0, "currentTotalPrice": 1204.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AWMB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dpmexFQm8sz0YViTVf+KyZDrwGmbTcMFjyF+VFDax+7GNpR+imxQqHHtkD5P+8dttpAI7DGBPDsCLB5qKNmuo7uwDBBa/JUOEhqjucawnvPBNqRSuTJKHmTK4k+1LNyd4Jn91sz60r8BYc5EU9n5w33JisNyrU8MvcvLynrtShsZT3rejx2oz6BOT7qM0OufJbRnGBHY/0mWwqdtQR4hiPS", "priceType": 1, "deductInfos": [{"totalAmount": 132.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD70698", "vendorCode": "70698", "pStoreCode": "116761", "rStoreCode": "116761", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD70698_0_116761_116761", "comPriceCode": "63dba621667fb177dd1882b7", "priceVersion": "AWMB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dpmexFQm8sz0YViTVf+KyZDrwGmbTcMFjyF+VFDax+7GNpR+imxQqHHtkD5P+8dttpAI7DGBPDsCLB5qKNmuo7uwDBBa/JUOEhqjucawnvPBNqRSuTJKHmTK4k+1LNyd4Jn91sz60r8BYc5EU9n5w33JisNyrU8MvcvLynrtShsZT3rejx2oz6BOT7qM0OufJbRnGBHY/0mWwqdtQR4hiPS", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20019532", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减132", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 1336.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3059, "rentalamount": 1064.0, "totalDailyPrice": 532.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1972713, "klbPId": 9600, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 59419, "rLevel": 59419, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "0.95", "c": "43", "v": "71504"}, "newEnergy": 0, "platform": 10, "kPSId": 116761, "kRSId": 116761, "kVId": 70698, "pLev": 59419, "rLev": 59419, "klbVersion": 1, "kVehicleId": 3059}, "sortNum": 6.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减132", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 536870912, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 3.2776, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "铭途租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1064.0, "amountStr": "¥1064", "subAmount": 532.0, "subAmountStr": "日均¥532", "originalDailyPrice": 598, "detail": [{"code": "1001", "name": "租车费", "amount": 1196, "amountDesc": "¥1196"}, {"code": "3814", "name": "周边游特惠", "amount": 132, "amountDesc": "¥132"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1204.0, "amountStr": "¥1204", "subAmount": 1336.0, "subAmountStr": "¥1336", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "车之美租车", "isMinTPriceVendor": false, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/73265.jpg", "commentInfo": {"level": "", "vendorDesc": "车之美租车", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 615.0, "currentOriginalDailyPrice": 618, "oTPrice": 1361.0, "currentTotalPrice": 1355.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AWMB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2Csq2PzvOWjqaWPVGfDQaOcxPsJyD7vyOMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dph/9mI6cMcloViTVf+KyZDPoD7ODz6BUKF+VFDax+7GCu7DsnS2YWNtkD5P+8dttpAI7DGBPDsCHwU118RFsj8wDBBa/JUOEh5aIBhEgMaRdqRSuTJKHmTvDm1OHS/y19n91sz60r8BYc5EU9n5w33JisNyrU8MvcvLynrtShsZRTLpirutzXvOT7qM0OufJaXjPFlUqs5+q/gLWfr1umC", "priceType": 1, "deductInfos": [{"totalAmount": 6.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD73265", "vendorCode": "73265", "pStoreCode": "114681", "rStoreCode": "114681", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD73265_0_114681_114681", "comPriceCode": "63dba621667fb177dd1882c9", "priceVersion": "AWMB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2Csq2PzvOWjqaWPVGfDQaOcxPsJyD7vyOMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dph/9mI6cMcloViTVf+KyZDPoD7ODz6BUKF+VFDax+7GCu7DsnS2YWNtkD5P+8dttpAI7DGBPDsCHwU118RFsj8wDBBa/JUOEh5aIBhEgMaRdqRSuTJKHmTvDm1OHS/y19n91sz60r8BYc5EU9n5w33JisNyrU8MvcvLynrtShsZRTLpirutzXvOT7qM0OufJaXjPFlUqs5+q/gLWfr1umC", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20019531", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 1361.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3059, "rentalamount": 1230.0, "totalDailyPrice": 615.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1922293, "klbPId": 5163, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 46578, "rLevel": 46578, "promtId": 606353347, "rCoup": 0, "sortInfo": {"p": "1", "s": "0.74", "c": "43", "v": "74523"}, "newEnergy": 0, "platform": 10, "kPSId": 114681, "kRSId": 114681, "kVId": 73265, "pLev": 46578, "rLev": 46578, "klbVersion": 1, "kVehicleId": 3059}, "sortNum": 7.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 1.7865, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "车之美租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1230.0, "amountStr": "¥1230", "subAmount": 615.0, "subAmountStr": "日均¥615", "originalDailyPrice": 618, "detail": [{"code": "1001", "name": "租车费", "amount": 1236, "amountDesc": "¥1236"}, {"code": "11037", "name": "优惠券", "amount": 6, "amountDesc": "¥6"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 25.0, "amountStr": "¥25", "detail": [{"code": "1003", "name": "车行手续费", "amount": 25.0, "amountDesc": "¥25", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "name": "基础服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1355.0, "amountStr": "¥1355", "subAmount": 1361.0, "subAmountStr": "¥1361", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "漫自由租车", "isMinTPriceVendor": false, "vendorLogo": "http://pic.ctrip.com/car_isd/vendorlogo/46492.jpg", "commentInfo": {"level": "", "vendorDesc": "漫自由三亚店", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 1423.0, "currentOriginalDailyPrice": 1599, "oTPrice": 3278.0, "currentTotalPrice": 2926.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AWUB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw+YgRUR73UH4oViTVf+KyZDXEn4dqZfD9iF+VFDax+7GJYVd4OW/3/VrxKAzK6kwOKuXfu8EgC8V+gJkesgmhYwapf3nK0c6+jRh0OYLRsCXXNykuh4p1ja/Q/vG+NfxZ8vdFo74KKXpChaBf67RIfGJeHIiqfV5HilDt+56//6NaL9pg6Jd5gh4SUqk/jQ5P/Ll/8sdAEMbzFXCpz7RD9P", "priceType": 1, "deductInfos": [{"totalAmount": 352.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD46492", "vendorCode": "46492", "pStoreCode": "106965", "rStoreCode": "106965", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD46492_0_106965_106965", "comPriceCode": "63dba621667fb177dd188b2d", "priceVersion": "AWUB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw+YgRUR73UH4oViTVf+KyZDXEn4dqZfD9iF+VFDax+7GJYVd4OW/3/VrxKAzK6kwOKuXfu8EgC8V+gJkesgmhYwapf3nK0c6+jRh0OYLRsCXXNykuh4p1ja/Q/vG+NfxZ8vdFo74KKXpChaBf67RIfGJeHIiqfV5HilDt+56//6NaL9pg6Jd5gh4SUqk/jQ5P/Ll/8sdAEMbzFXCpz7RD9P", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "108", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减352", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 3278.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3059, "rentalamount": 2846.0, "totalDailyPrice": 1423.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1854733, "klbPId": 1715, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 23868, "rLevel": 23868, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "0.14", "c": "43", "v": "55251"}, "newEnergy": 0, "platform": 10, "kPSId": 106965, "kRSId": 106965, "kVId": 46492, "pLev": 23868, "rLev": 23868, "klbVersion": 1, "kVehicleId": 3059}, "sortNum": 8.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减352", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 11, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 3.2972, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "漫自由租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2846.0, "amountStr": "¥2846", "subAmount": 1423.0, "subAmountStr": "日均¥1423", "originalDailyPrice": 1599, "detail": [{"code": "1001", "name": "租车费", "amount": 3198, "amountDesc": "¥3198"}, {"code": "3814", "name": "周边游特惠", "amount": 352, "amountDesc": "¥352"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2926.0, "amountStr": "¥2926", "subAmount": 3278.0, "subAmountStr": "¥3278", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}, {"vendorName": "文东租车", "isMinTPriceVendor": false, "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/62863.jpg", "commentInfo": {"level": "", "vendorDesc": "海南文东汽车服务有限公司", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 1780.0, "currentOriginalDailyPrice": 2000, "oTPrice": 4090.0, "currentTotalPrice": 3650.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AWUB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2AoLI78oMerFLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw9iI/nyo3XkaoViTVf+KyZDBUhZMbohH1KF+VFDax+7GFwkaxugjbzYxHQJ6juIo/2uXfu8EgC8Vxgtn0YBnk8Hapf3nK0c6+i9StnoCi2M+uFOPrXz96/m4/FGO7pDdEa93P1vCt6FKihaBf67RIfGJeHIiqfV5HilDt+56//6NVgE6wUBh9xX00D5xf/HFfmiO/x9fyCZVPlHV0SX97FL", "priceType": 1, "deductInfos": [{"totalAmount": 440.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD62863", "vendorCode": "62863", "pStoreCode": "114447", "rStoreCode": "114447", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD62863_0_114447_114447", "comPriceCode": "63dba621667fb177dd188853", "priceVersion": "AWUB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDeSyW05BMwPIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2AoLI78oMerFLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw9iI/nyo3XkaoViTVf+KyZDBUhZMbohH1KF+VFDax+7GFwkaxugjbzYxHQJ6juIo/2uXfu8EgC8Vxgtn0YBnk8Hapf3nK0c6+i9StnoCi2M+uFOPrXz96/m4/FGO7pDdEa93P1vCt6FKihaBf67RIfGJeHIiqfV5HilDt+56//6NVgE6wUBh9xX00D5xf/HFfmiO/x9fyCZVPlHV0SX97FL", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20019531", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减440", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 4090.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 3059, "rentalamount": 3560.0, "totalDailyPrice": 1780.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 2441505, "klbPId": 4822, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 43934, "rLevel": 43934, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "0.09", "c": "43", "v": "62395"}, "newEnergy": 0, "platform": 10, "kPSId": 114447, "kRSId": 114447, "kVId": 62863, "pLev": 43934, "rLev": 43934, "klbVersion": 1, "kVehicleId": 3059}, "sortNum": 9.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减440", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 16.929, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3814", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "文东租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 3560.0, "amountStr": "¥3560", "subAmount": 1780.0, "subAmountStr": "日均¥1780", "originalDailyPrice": 2000, "detail": [{"code": "1001", "name": "租车费", "amount": 4000, "amountDesc": "¥4000"}, {"code": "3814", "name": "周边游特惠", "amount": 440, "amountDesc": "¥440"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 30.0, "amountStr": "¥30", "detail": [{"code": "1003", "name": "车行手续费", "amount": 30.0, "amountDesc": "¥30", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 3650.0, "amountStr": "¥3650", "subAmount": 4090.0, "subAmountStr": "¥4090", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30147_0_106883_106883"]}, "minTPrice": 136.0, "minDPrice": 28.0, "modifySameVehicle": false, "priceSize": 10, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "3164", "sortNum": 36, "lowestPrice": 48.0, "highestPrice": 48.0, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 0.0, "vendorPriceList": [{"vendorName": "calabi-骑仕租车A", "isMinTPriceVendor": true, "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/30147.jpg", "commentInfo": {"level": "", "vendorDesc": "三亚湾店", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 48.0, "currentOriginalDailyPrice": 98, "oTPrice": 276.0, "currentTotalPrice": 176.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAleMVaOxCS+IxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThhtNJdHK4NaxtdmYH2LzUSRj4CrOI1G42bg5xWDkQrTwtve49xdKAs2XExqToPEDwpZpEoTnxeD71EAGZgRkRqiX4u/eBFfWNaA3FdkEq28QttnixjB73+/QX5rSrUWB0iF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZNoNCZRJrHew2iYSwnulaP3mVShtdZkZv0lYE0J7Fi0GA==", "priceType": 1, "deductInfos": [{"totalAmount": 100.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD30147", "vendorCode": "30147", "pStoreCode": "106883", "rStoreCode": "106883", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD30147_0_106883_106883", "comPriceCode": "63dba621667fb177dd18869b", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAleMVaOxCS+IxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThhtNJdHK4NaxtdmYH2LzUSRj4CrOI1G42bg5xWDkQrTwtve49xdKAs2XExqToPEDwpZpEoTnxeD71EAGZgRkRqiX4u/eBFfWNaA3FdkEq28QttnixjB73+/QX5rSrUWB0iF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZNoNCZRJrHew2iYSwnulaP3mVShtdZkZv0lYE0J7Fi0GA==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13362", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 276.0, "isrec": false, "recommendOrder": 0, "mergeId": 738, "rectype": 1, "cvid": 3164, "rentalamount": 96.0, "totalDailyPrice": 48.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13362", "storeId": "106883"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1855282, "klbPId": 1609, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 22342, "rLevel": 22342, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "2083"}, "newEnergy": 0, "platform": 10, "kPSId": 106883, "kRSId": 106883, "kVId": 30147, "pLev": 22342, "rLev": 22342, "klbVersion": 1, "kVehicleId": 3164}, "sortNum": 0.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 8, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}], "storeScore": 100.0, "isSelect": false, "distance": 0.0, "rDistance": 0.76, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3487", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "calabi-骑仕租车A", "card": 0, "ctripVehicleCode": "3164", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 96.0, "amountStr": "¥96", "subAmount": 48.0, "subAmountStr": "日均¥48", "originalDailyPrice": 98, "detail": [{"code": "1001", "name": "租车费", "amount": 196, "amountDesc": "¥196"}, {"code": "3487", "name": "元旦特惠", "amount": 100, "amountDesc": "¥100"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 176.0, "amountStr": "¥176", "subAmount": 276.0, "subAmountStr": "¥276", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}], "reactId": "**********0", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30147_0_106883_106883"]}, "minTPrice": 176.0, "minDPrice": 48.0, "modifySameVehicle": false, "priceSize": 1, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4498", "sortNum": 37, "lowestPrice": 38.0, "highestPrice": 38.0, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 0.0, "vendorPriceList": [{"vendorName": "calabi-骑仕租车A", "isMinTPriceVendor": true, "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/30147.jpg", "commentInfo": {"level": "", "vendorDesc": "机场店", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 38.0, "currentOriginalDailyPrice": 88, "oTPrice": 276.0, "currentTotalPrice": 176.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDKsBv+g1LpkYxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZCJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThAeQgoyOMqkNtdmYH2LzUSRj4CrOI1G42bg5xWDkQrTwtve49xdKAs2c+Pk+kSvP7pZpEoTnxeD4n6COLPwRpTiX4u/eBFfWNaA3FdkEq28SQ+i4rco/ZRPQX5rSrUWB0iF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZMZrbWY8nniUjTdjYUaCW/rmVShtdZkZv2QvI+WI+mP2Q==", "priceType": 1, "deductInfos": [{"totalAmount": 100.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD30147", "vendorCode": "30147", "pStoreCode": "106878", "rStoreCode": "106878", "vehicleCode": "0", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD30147_0_106878_106878", "comPriceCode": "63dba621667fb177dd188aff", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDKsBv+g1LpkYxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZCJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThAeQgoyOMqkNtdmYH2LzUSRj4CrOI1G42bg5xWDkQrTwtve49xdKAs2c+Pk+kSvP7pZpEoTnxeD4n6COLPwRpTiX4u/eBFfWNaA3FdkEq28SQ+i4rco/ZRPQX5rSrUWB0iF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZMZrbWY8nniUjTdjYUaCW/rmVShtdZkZv2QvI+WI+mP2Q==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20042460", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 276.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 4498, "rentalamount": 76.0, "totalDailyPrice": 38.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 1856692, "klbPId": 12011, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 22341, "rLevel": 22341, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43", "v": "1937"}, "newEnergy": 0, "platform": 10, "kPSId": 106878, "kRSId": 106878, "kVId": 30147, "pLev": 22341, "rLev": 22341, "klbVersion": 1, "kVehicleId": 4498}, "sortNum": 0.0, "pStoreRouteDesc": "免费站内取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 70, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_b0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 8, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 28, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}], "storeScore": 100.0, "isSelect": true, "distance": 0.0, "rDistance": 0.2884, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3487", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "calabi-骑仕租车A", "card": 0, "ctripVehicleCode": "4498", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 76.0, "amountStr": "¥76", "subAmount": 38.0, "subAmountStr": "日均¥38", "originalDailyPrice": 88, "detail": [{"code": "1001", "name": "租车费", "amount": 176, "amountDesc": "¥176"}, {"code": "3487", "name": "元旦特惠", "amount": 100, "amountDesc": "¥100"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 176.0, "amountStr": "¥176", "subAmount": 276.0, "subAmountStr": "¥276", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": ""}], "reactId": "**********1", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD30147_0_106878_106878"]}, "minTPrice": 176.0, "minDPrice": 38.0, "modifySameVehicle": false, "priceSize": 1, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}], "recommendVehicleList": [{"brandEName": "大众", "brandName": "大众", "name": "大众宝来", "zhName": "大众宝来", "vehicleCode": "1126", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.4T-1.5L", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "vehiclesSetId": "64"}, {"brandEName": "起亚", "brandName": "起亚", "name": "起亚K2", "zhName": "起亚K2", "vehicleCode": "3060", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "别克", "brandName": "别克", "name": "别克英朗", "zhName": "别克英朗", "vehicleCode": "4101", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.0T-1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众朗逸", "zhName": "大众朗逸", "vehicleCode": "1069", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.4T-1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田 卡罗拉", "zhName": "丰田 卡罗拉", "vehicleCode": "5407", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.2T-1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田雷凌", "zhName": "丰田雷凌", "vehicleCode": "5349", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.2T-1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "起亚", "brandName": "起亚", "name": "起亚K2", "zhName": "起亚K2", "vehicleCode": "3059", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.4L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "日产", "brandName": "日产", "name": "日产阳光", "zhName": "日产阳光", "vehicleCode": "3164", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "carPhone": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "别克", "brandName": "别克", "name": "别克凯越", "zhName": "别克凯越", "vehicleCode": "4498", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.3L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "carPhone": true, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众捷达", "zhName": "大众捷达", "vehicleCode": "1121", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.4T-1.5L", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众桑塔纳", "zhName": "大众桑塔纳", "vehicleCode": "1058", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "别克", "brandName": "别克", "name": "别克凯越", "zhName": "别克凯越", "vehicleCode": "819", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "捷达", "brandName": "捷达", "name": "捷达VA3", "zhName": "捷达VA3", "vehicleCode": "5104", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "别克", "brandName": "别克", "name": "别克威朗", "zhName": "别克威朗", "vehicleCode": "829", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.0T-1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众桑塔纳", "zhName": "大众桑塔纳", "vehicleCode": "4870", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "现代", "brandName": "现代", "name": "现代伊兰特", "zhName": "现代伊兰特", "vehicleCode": "5306", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田YARiS L 致享", "zhName": "丰田YARiS L 致享", "vehicleCode": "5574", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "本田", "brandName": "本田", "name": "本田飞度", "zhName": "本田飞度", "vehicleCode": "658", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "起亚", "brandName": "起亚", "name": "起亚焕驰", "zhName": "起亚焕驰", "vehicleCode": "5572", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.4L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众速腾", "zhName": "大众速腾", "vehicleCode": "4103", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.2T", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "vehiclesSetId": "64"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田凌尚", "zhName": "丰田凌尚", "vehicleCode": "5465", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "2.0L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "本田", "brandName": "本田", "name": "本田思域", "zhName": "本田思域", "vehicleCode": "5278", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.5T", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "本田", "brandName": "本田", "name": "本田享域", "zhName": "本田享域", "vehicleCode": "4743", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.0T", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田雷凌", "zhName": "丰田雷凌", "vehicleCode": "1441", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.8L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众C-TREK蔚领", "zhName": "大众C-TREK蔚领", "vehicleCode": "4091", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 3, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田亚洲狮", "zhName": "丰田亚洲狮", "vehicleCode": "5427", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "2.0L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "现代", "brandName": "现代", "name": "现代悦动", "zhName": "现代悦动", "vehicleCode": "3469", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田威驰", "zhName": "丰田威驰", "vehicleCode": "1464", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.3L-1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田致炫", "zhName": "丰田致炫", "vehicleCode": "1436", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "displacement": "1.3L-1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "carPhone": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "名爵", "brandName": "名爵", "name": "名爵6", "zhName": "名爵6", "vehicleCode": "4303", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 3, "displacement": "1.5T", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "本田", "brandName": "本田", "name": "本田凌派", "zhName": "本田凌派", "vehicleCode": "668", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 4, "displacement": "1.0T-1.8L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放4个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众Polo", "zhName": "大众Polo", "vehicleCode": "4484", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "日产", "brandName": "日产", "name": "日产轩逸", "zhName": "日产轩逸", "vehicleCode": "3159", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.6L-1.8L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "vehiclesSetId": "64"}, {"brandEName": "现代", "brandName": "现代", "name": "现代领动", "zhName": "现代领动", "vehicleCode": "5060", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "carPhone": true, "vehiclesSetId": "64"}, {"brandEName": "名爵", "brandName": "名爵", "name": "MG锐行", "zhName": "MG锐行", "vehicleCode": "2779", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.5T", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "荣威", "brandName": "荣威", "name": "荣威i6", "zhName": "荣威i6", "vehicleCode": "4841", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "领克", "brandName": "领克", "name": "领克03", "zhName": "领克03", "vehicleCode": "4737", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5T-2.0T", "struct": "", "fuel": "95号", "driveMode": "前置四驱、前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "vehiclesSetId": "64"}, {"brandEName": "起亚", "brandName": "起亚", "name": "起亚KX CROSS", "zhName": "起亚KX CROSS", "vehicleCode": "4909", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "本田", "brandName": "本田", "name": "本田思域", "zhName": "本田思域", "vehicleCode": "635", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.0T-1.5T", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科鲁泽", "zhName": "雪佛兰科鲁泽", "vehicleCode": "4820", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.0T", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "马自达", "brandName": "马自达", "name": "马自达3 昂克赛拉", "zhName": "马自达3 昂克赛拉", "vehicleCode": "2831", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "autoStart": true, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众高尔夫", "zhName": "大众高尔夫", "vehicleCode": "1129", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.2T", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "起亚", "brandName": "起亚", "name": "起亚K3", "zhName": "起亚K3", "vehicleCode": "4920", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "MINI", "brandName": "MINI", "name": "MINI", "zhName": "MINI", "vehicleCode": "2799", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.5T", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoStart": true, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "吉利汽车", "brandName": "吉利汽车", "name": "吉利帝豪", "zhName": "吉利帝豪", "vehicleCode": "2050", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoStart": true, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众速腾", "zhName": "大众速腾", "vehicleCode": "1140", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.4T", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "vehiclesSetId": "64"}, {"brandEName": "吉利汽车", "brandName": "吉利汽车", "name": "吉利远景", "zhName": "吉利远景", "vehicleCode": "2066", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoStart": true, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "本田", "brandName": "本田", "name": "本田型格", "zhName": "本田型格", "vehicleCode": "5719", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5T", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "64"}, {"brandEName": "奇瑞", "brandName": "奇瑞", "name": "奇瑞艾瑞泽5", "zhName": "奇瑞艾瑞泽5", "vehicleCode": "2961", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众朗行", "zhName": "大众朗行", "vehicleCode": "4318", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "displacement": "1.2T", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "斯柯达", "brandName": "斯柯达", "name": "斯柯达明锐", "zhName": "斯柯达明锐", "vehicleCode": "3419", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.4T", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "vehiclesSetId": "64"}, {"brandEName": "日产", "brandName": "日产", "name": "日产骐达TIIDA", "zhName": "日产骐达TIIDA", "vehicleCode": "3155", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "displacement": "1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "现代", "brandName": "现代", "name": "现代瑞纳", "zhName": "现代瑞纳", "vehicleCode": "3460", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.4L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "东风风神", "brandName": "东风风神", "name": "东风风神A60", "zhName": "东风风神A60", "vehicleCode": "1333", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoStart": true, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "斯柯达", "brandName": "斯柯达", "name": "斯柯达昕锐", "zhName": "斯柯达昕锐", "vehicleCode": "4769", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "宝骏", "brandName": "宝骏", "name": "宝骏630", "zhName": "宝骏630", "vehicleCode": "154", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoStart": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "现代", "brandName": "现代", "name": "现代领动", "zhName": "现代领动", "vehicleCode": "3477", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.6L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "福特", "brandName": "福特", "name": "福特福睿斯", "zhName": "福特福睿斯", "vehicleCode": "1577", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.5L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "现代", "brandName": "现代", "name": "现代悦纳", "zhName": "现代悦纳", "vehicleCode": "4020", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.4L", "struct": "", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众高尔夫", "zhName": "大众高尔夫", "vehicleCode": "1133", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.4T", "struct": "", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["0"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "realityImageUrl": "0", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "vehiclesSetId": "64"}], "filteredProductGroups": {"vendorPriceList": []}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "isFromSearch": false, "promotMap": {}, "extras": {"serverRequestId": "gHFuS4P0ty09Q5Lh1c6h", "abVersion": "220323_DSJT_rank2|B,220624_DSJT_spfj1|B,221207_DSJT_cxvr|A", "rSelect": "1"}, "imStatus": 1, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 321, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 321, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1671010402293, "afterFetch": 1671010402614}}