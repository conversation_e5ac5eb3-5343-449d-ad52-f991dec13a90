{"promotMap": {}, "extras": {"packageLevelAB": "B", "abVersion": "241008_DSJT_ykjpx|B,230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B", "isLicensePlateHideShow": "0", "serverRequestId": "1W9410q6Bx4435qdl9qL", "packageLevelSwitch": "1", "rSelect": "1", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "0"}, "resBodySize": 215211, "baseResponse": {"code": "200", "requestId": "25cd1248-40f0-4da1-9b22-1af5d69f718a", "cost": 96, "isSuccess": true, "returnMsg": "OK"}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "productGroupCodeUesd": "2", "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "recommendVehicleList": [{"skylight": "部分车辆支持", "vehicleKey": "0_3159_", "luggageNo": 3, "carPlay": "不支持", "displacement": "1.6L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6c12000eodv4th22F2.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "日产轩逸(23及以前款)", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=912&app_ver=10.5", "vehicleCode": "3159", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "日产轩逸(23及以前款)", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2l12000befacgb63B5.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放3个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV7112000c5j6fs7D2EA.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3m12000c5j6qwk4321.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1r12000c5j6lb87547.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2p12000c5j714mD617.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0u12000c5j6fs96046.jpg?mark=yiche"], "transmissionType": 1, "brandName": "日产", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "日产", "licenseStyle": "2", "vehiclesSetId": "69", "guidSys": "部分车辆支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_4493_", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife/原厂互联/映射", "displacement": "1.4T-1.6L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4k12000etgmqamCFE7.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "大众朗逸", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=1234&app_ver=10.5", "vehicleCode": "4493", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "大众朗逸", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4k12000etgmqamCFE7.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1u12000c5lzks4ED27.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5n12000c5lzkwd97A1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0u12000c5lznhw8EB8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6012000c5lzrknF4D8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6x12000c5lzfpg9EF4.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "vehiclesSetId": "69", "guidSys": "部分车辆支持定速巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "2_4493_", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife/原厂互联/映射", "displacement": "1.4T-1.6L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4k12000etgmqamCFE7.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "大众朗逸", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=1234&app_ver=10.5", "vehicleCode": "4493", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "大众朗逸", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4k12000etgmqamCFE7.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1u12000c5lzks4ED27.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5n12000c5lzkwd97A1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0u12000c5lznhw8EB8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6012000c5lzrknF4D8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6x12000c5lzfpg9EF4.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "vehiclesSetId": "69", "guidSys": "部分车辆支持定速巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5407_", "luggageNo": 2, "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "displacement": "1.2T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "丰田卡罗拉（23款及以前）", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "carPhone": true, "vehicleCode": "5407", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田卡罗拉（23款及以前）", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0e12000c5s92ry5659.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000c5s9frq4F26.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0k12000c5s94goBD02.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1p12000c5s9fry9DF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6o12000c5s97iu8E9C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "69", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "2_5407_", "luggageNo": 2, "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "displacement": "1.2T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "丰田卡罗拉（23款及以前）", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "carPhone": true, "vehicleCode": "5407", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田卡罗拉（23款及以前）", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0e12000c5s92ry5659.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000c5s9frq4F26.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0k12000c5s94goBD02.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1p12000c5s9fry9DF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6o12000c5s97iu8E9C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "69", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_4498_", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife", "displacement": "1.3L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0p12000c7kml63CFE9.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "别克凯越", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=1486&app_ver=10.5", "carPhone": true, "vehicleCode": "4498", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克凯越", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0p12000c7kml63CFE9.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3t12000c7n51ky9AE3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3212000c7n5nxd0CC9.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1512000c7n55tk0FB5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1812000c7n57533381.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1z12000c7n5h4cDDF1.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "69", "guidSys": "不支持", "transmissionName": "自动挡"}], "filteredProductGroups": {"vendorPriceList": []}, "vehicleInfo": {"skylight": "部分车辆支持", "vehicleKey": "0_4139_", "luggageNo": 5, "carPlay": "部分车辆支持原厂互联/映射/CarLife/CarPlay", "displacement": "1.0T-1.5L", "autoPark": false, "charge": "", "fuelType": "汽油", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5i12000c7kmh93066A.png?mark=yiche"], "license": "", "isSpecialized": true, "multimediaAlbums": [{"albumName": "官方相册", "albumType": 1, "mediaGroup": [{"groupType": 2, "groupName": "视频", "groupSortNum": 2, "medias": [{"sortNum": 0, "type": 2, "url": "https://video.c-ctrip.com/videos/R40f27000001i5l8m91B0.mp4?mark=yiche", "cover": "https://dimg04.c-ctrip.com/images/0RV0612000d5pibur948E.png"}]}, {"groupType": 3, "groupName": "VR", "groupSortNum": 3, "medias": [{"sortNum": 0, "type": 3, "url": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "cover": "https://dimg04.c-ctrip.com/images/0RV5i12000c7kmh93066A.png?mark=yiche"}]}, {"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2212000c7mwn2zE222.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV7212000c7mwoic1D7B.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1512000c7mwn2h32E0.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6312000c7mwo5d6170.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4a12000c7mwln30AA4.jpg?mark=yiche", "cover": ""}]}, {"groupType": 5, "groupName": "前排", "groupSortNum": 5, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5212000c7mwubp7B39.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1212000c7mwvvc7936.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3012000c7mwv0sCA00.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4u12000c7mww2dBA58.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5z12000c7mwuun4E9B.jpg?mark=yiche", "cover": ""}]}, {"groupType": 6, "groupName": "后排", "groupSortNum": 6, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0l12000c7mwy55EE30.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3212000c7mx6u22402.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1812000c7mx63oDDA7.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6r12000c7mx4z63FA4.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6s12000c7mx0gu6818.jpg?mark=yiche", "cover": ""}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}], "note": "年款/颜色等以门店为准"}], "groupCode": "2", "zhName": "雪佛兰科沃兹", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/Type-C/SD", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "carPhone": true, "vehicleCode": "4139", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "雪佛兰科沃兹", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0612000d5pibur948E.png", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5212000c7mwubp7B39.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0l12000c7mwy55EE30.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000c7mwvvc7936.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3212000c7mx6u22402.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3012000c7mwv0sCA00.jpg?mark=yiche"], "transmissionType": 1, "brandName": "雪佛兰", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "雪佛兰", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "69", "guidSys": "不支持", "transmissionName": "自动挡"}, "isFromSearch": false, "timeInterval": 167.************, "specificProductGroups": {"vendorPriceList": [{"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 52282, "vendorDesc": "三亚-机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 52282}, "storeScore": 90.95, "uniqueCode": "9cd8bf1055274913910588215d25053d", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 98, "naked": true, "deductInfos": [{"totalAmount": 48, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 0, "currentTotalPrice": 50, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 0, "vendorName": "懒人行租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8192, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2288, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.535, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "一年内车龄", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3510"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"category": 2, "sortNum": 47, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "延误时免费留车", "colorCode": "2", "prefix": "租车中心", "type": 1, "groupId": 3, "labelCode": "3779"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减48", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "11037", "amount": 45, "amountDesc": "¥45", "name": "优惠券"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 48, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥50"}], "vendorLogo": "https://dimg04.c-ctrip.com/images/0yc3w12000a7k3pvyF9CF.jpg", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "免费站内取还车", "ctripVehicleCode": "4139", "newCar": true, "secretBox": false, "sortScore": 0, "cyVendorName": "懒人行租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "2398_8888_pupai", "pickWayInfo": 16, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费站内取还车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 98, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "193947", "vehicleId": "2398_8888_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 0}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD4201", "elct": 0, "pLevel": 1658, "gsDesc": "站内取还", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 38809, "rStoreNav": "免费站内取还车", "priceVersion": "SH-PRICEVERSION_MTkzOTQ3XzQxMzlfMV80OF80OF80OF85OC4wMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzMwNTQxMjExXzM1MTB8MzgxMHwzNTA0fDM1NjN8Mzc0NnwzNzg4fDQyMjl8Mzc3OV8xMDAxOjQ4fDEwMDM6MjAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "13119", "productCode": "SD4201_0_193947_193947", "pLev": 1139587, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 2, "packageType": 1, "kPSId": 193947, "age": 30, "rCoup": 0, "kRSId": 193947, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 1139587, "pStoreCode": "193947", "pickUpOnDoor": true, "aType": 0, "kVId": 13119, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]MTIxMTMwNTQwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCY0OGUmNDhmYWxzMDAxJiYkfDEmNDgkMSY0OCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTQ6NSAxOAAAAAAyNQAA", "dropOffOnDoor": true, "returnWayInfo": 16, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减48", "groupId": 1}, {"category": 3, "sortNum": 10000, "amountTitle": "共减48", "groupCode": "MarketGroup1317", "code": "30", "title": "双11特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 2, "klb": 1, "promtId": 605988495, "rStoreCode": "193947", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 30541211, "rLevel": 1658, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 12643, "vendorDesc": "凯美租车三亚店", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆干净整洁", "qExposed": "4.9", "hasComment": 1, "commentCount": 12643}, "storeScore": 82.5, "uniqueCode": "4fdf065725d843b993442a22e86c9c57", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 38, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 0, "currentTotalPrice": 50, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 1, "vendorName": "凯美租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.0396, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "一年内车龄", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3510"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "11037", "amount": 36, "amountDesc": "¥36", "name": "优惠券"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 38, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥50"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": true, "secretBox": false, "sortScore": 1, "cyVendorName": "凯美租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "699_53432_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "183936", "vehicleId": "699_53432_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 0}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD3926", "elct": 0, "pLevel": 292, "gsDesc": "精选好货", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 19034, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTgzOTM2XzQxMzlfMV8zOF8zOF8zOF84OC4wMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzY5MDEwNDRfMzUxMHwzODEwfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjM4fDEwMDM6MjAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "13082", "productCode": "SD3926_0_183936_183936", "pLev": 141409, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 183936, "age": 30, "rCoup": 0, "kRSId": 183936, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 141409, "pStoreCode": "183936", "pickUpOnDoor": true, "aType": 0, "kVId": 13082, "sortInfo": {"p": "1", "s": "100.0", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]MDQ0fDY5MDF8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}, {"category": 3, "sortNum": 10000, "amountTitle": "共减38", "groupCode": "MarketGroup1317", "code": "30", "title": "双11特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 605988495, "rStoreCode": "183936", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 6901044, "rLevel": 292, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 13114, "vendorDesc": "世纪联合租车", "level": "超棒", "maximumRating": 5, "commentLabel": "取还方便", "qExposed": "5.0", "hasComment": 1, "commentCount": 13114}, "storeScore": 83.75, "uniqueCode": "421dff72181e420a86bc67545c4dca5b", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 38, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 0, "currentTotalPrice": 50, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 2, "vendorName": "三亚世纪联合租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 33554432, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2480, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.2394, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "11037", "amount": 36, "amountDesc": "¥36", "name": "优惠券"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 38, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥50"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 2, "cyVendorName": "三亚世纪联合租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1860920", "pickWayInfo": 1, "stockLevel": "E", "pCityId": 43, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "107119", "vehicleId": "1860920"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 0}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3942", "elct": 0, "pLevel": 30154, "gsDesc": "低价省钱", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 2110, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTA3MTE5XzQxMzlfMV8zOF8zOF8zOF84OC4wMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzE4NjA5MjBfMzgxMHwzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6Mzh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "53893", "productCode": "SD3942_0_107119_107119", "pLev": 23994, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 107119, "age": 30, "rCoup": 0, "kRSId": 107119, "packageId": "", "freeIllegalDeposit": false, "rLev": 23994, "pStoreCode": "107119", "pickUpOnDoor": true, "aType": 0, "kVId": 53893, "sortInfo": {"p": "1", "s": "100.0", "c": "43", "v": "50500"}, "kVehicleId": 4139, "comPriceCode": "[c]OTIwfDE4NjB8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}, {"category": 3, "sortNum": 10000, "amountTitle": "共减38", "groupCode": "MarketGroup1317", "code": "30", "title": "双11特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 605988495, "rStoreCode": "107119", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1860920, "rLevel": 30154, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 51270, "vendorDesc": "三亚凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "取还方便", "qExposed": "5.0", "hasComment": 1, "commentCount": 51270}, "storeScore": 91.03, "uniqueCode": "3b9b43699cbf4a408c8354ca1802d685", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 38, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 0, "currentTotalPrice": 50, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 3, "vendorName": "骑仕租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 112, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2347, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "两年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3547"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"category": 2, "sortNum": 47, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "延误时免费留车", "colorCode": "2", "prefix": "租车中心", "type": 1, "groupId": 3, "labelCode": "3779"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "11037", "amount": 36, "amountDesc": "¥36", "name": "优惠券"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 38, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥50"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "免费站内取还车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 3, "cyVendorName": "骑仕租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "445_13470_pupai", "pickWayInfo": 16, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费站内取还车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "188084", "vehicleId": "445_13470_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 0}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD3866", "elct": 0, "pLevel": 872, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 17694, "rStoreNav": "免费站内取还车", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzQxMzlfMV8zOF8zOF8zOF84OC4wMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzY5MDAyODBfMzU0N3wzODEwfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNzc5XzEwMDE6Mzh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "13094", "productCode": "SD3866_0_188084_188084", "pLev": 169518, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 2, "packageType": 1, "kPSId": 188084, "age": 30, "rCoup": 0, "kRSId": 188084, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 169518, "pStoreCode": "188084", "pickUpOnDoor": true, "aType": 0, "kVId": 13094, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]MjgwfDY5MDB8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 16, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}, {"category": 3, "sortNum": 10000, "amountTitle": "共减38", "groupCode": "MarketGroup1317", "code": "30", "title": "双11特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 2, "klb": 1, "promtId": 605988495, "rStoreCode": "188084", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 6900280, "rLevel": 872, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 1599, "vendorDesc": "三亚机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "服务周到", "qExposed": "5.0", "hasComment": 1, "commentCount": 1599}, "storeScore": 90, "uniqueCode": "44dc633bcaa64074a0b55a9cc26a4078", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 38, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 0, "currentTotalPrice": 50, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 4, "vendorName": "器车出行", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 524288, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 160, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 1.4147, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "两年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3547"}, {"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "11037", "amount": 36, "amountDesc": "¥36", "name": "优惠券"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 38, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥50"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 4, "cyVendorName": "器车出行", "vehicleGroup": 2, "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "8651_8888_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "114529", "vehicleId": "8651_8888_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 0}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD4701", "elct": 0, "pLevel": 120135, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 4997, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTE0NTI5XzQxMzlfMV8zOF8zOF8zOF84OC4wMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzQwNTY1MzQ0XzM1NDd8MzUwM3wzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6Mzh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "74573", "productCode": "SD4701_0_114529_114529", "pLev": 1590240, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 114529, "age": 30, "rCoup": 0, "kRSId": 114529, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 1590240, "pStoreCode": "114529", "pickUpOnDoor": true, "aType": 0, "kVId": 74573, "sortInfo": {"p": "1", "s": "100.0", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]NTM0NDQwNTYwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCYzOGUmMzhmYWxzMDAxJiYkfDEmMzgkMSYzOCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTQ6NSAxOAAAAAAyNQAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}, {"category": 3, "sortNum": 10000, "amountTitle": "共减38", "groupCode": "MarketGroup1317", "code": "30", "title": "双11特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 605988495, "rStoreCode": "114529", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 40565344, "rLevel": 120135, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 45511, "vendorDesc": "桐叶凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 45511}, "storeScore": 100, "uniqueCode": "83edee9d600848a381107c6e54f978fa", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 38, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 0, "currentTotalPrice": 50, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 5, "vendorName": "桐叶租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2888, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"category": 2, "sortNum": 47, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "延误时免费留车", "colorCode": "2", "prefix": "租车中心", "type": 1, "groupId": 3, "labelCode": "3779"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "11037", "amount": 36, "amountDesc": "¥36", "name": "优惠券"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 38, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥50"}], "vendorLogo": "https://dimg04.c-ctrip.com/images/20p4212000fiowgprC5C9.jpg", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "免费站内取还车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 5, "cyVendorName": "桐叶租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "stock": 99999, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "18536", "pickWayInfo": 16, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费站内取还车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "78", "vehicleId": "18536"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 0}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD14450", "elct": 0, "pLevel": 2761, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 1, "rStoreNav": "免费站内取还车", "priceVersion": "SH-PRICEVERSION_NzhfNDEzOV8xXzM4LjBfMzguMF8wLjBfODguMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMF8yMC4wXzBfMF8xNTI1MzlfMzgxMHwzNTYzfDM3NDZ8Mzc4OHw0MjI5fDM3NzlfMTAwMTozOC4wfDEwMDM6MjAuMHwxMDAyOjMwLjA=", "alipay": false, "vendorCode": "13031", "productCode": "SD14450_0_78_78", "pLev": 129, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 2, "packageType": 1, "kPSId": 78, "age": 30, "rCoup": 0, "kRSId": 78, "packageId": "Secure", "freeIllegalDeposit": false, "rLev": 129, "pStoreCode": "78", "pickUpOnDoor": true, "aType": 0, "kVId": 13031, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "eyJzZWxsZXJpZCI6MTEwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6Mjc2MSwiYWN0Z2V0aWQiOjI3NjEsImFjdG9mZmlkIjoyNzYxLCJjYXJ0eXBlaWQiOjE4NTM2LCJ0b3RhbCI6ODgsInRpbWUiOjE3Mjk4NTA5NzJ9", "dropOffOnDoor": true, "returnWayInfo": 16, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}, {"category": 3, "sortNum": 10000, "amountTitle": "共减38", "groupCode": "MarketGroup1317", "code": "30", "title": "双11特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 2, "klb": 1, "promtId": 605988495, "rStoreCode": "78", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 152539, "rLevel": 2761, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": true, "isOrderVehicle": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 3740, "vendorDesc": "三亚凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "服务周到", "qExposed": "4.9", "hasComment": 1, "commentCount": 3740}, "storeScore": 87.03, "uniqueCode": "5dfbf25581f94b23bd6164ddc710bab8", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 6, "vendorName": "车游天下租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2288, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.79, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "一年内车龄", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3510"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": true, "secretBox": false, "sortScore": 6, "cyVendorName": "车游天下租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "stock": 99999, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "18536", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "67462", "vehicleId": "18536"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD7763", "elct": 0, "pLevel": 20220801, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 119, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_Njc0NjJfNDEzOV8xXzM4LjBfMzguMF8wLjBfODguMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMF8yMC4wXzAuMF8wLjBfNzg3MDEzXzM1MTB8MzgxMHwzNTA0fDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTozOC4wfDEwMDM6MjAuMHwxMDAyOjMwLjA=", "alipay": false, "vendorCode": "13067", "productCode": "SD7763_0_67462_67462", "pLev": 984012, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 67462, "age": 30, "rCoup": 0, "kRSId": 67462, "packageId": "XC2024102518093277575", "freeIllegalDeposit": false, "rLev": 984012, "pStoreCode": "67462", "pickUpOnDoor": true, "aType": 0, "kVId": 13067, "sortInfo": {"p": "1", "s": "39.8", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "a53a5ecc-9574-47d4-88fb-e557dd7f7a1e", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "67462", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 787013, "rLevel": 20220801, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "isOrderVehicle": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 6948, "vendorDesc": "漫自由三亚店", "level": "超棒", "maximumRating": 5, "commentLabel": "服务周到", "qExposed": "5.0", "hasComment": 1, "commentCount": 6948}, "storeScore": 79.72, "uniqueCode": "b6b3303e7319438c93f2a0caad13e379", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 7, "vendorName": "漫自由租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8388608, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 11, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2272, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.3325, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "两年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3547"}, {"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 7, "cyVendorName": "漫自由租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "6745_13472_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "106965", "vehicleId": "6745_13472_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD3933", "elct": 0, "pLevel": 88368, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 1715, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTA2OTY1XzQwMTRfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzI4MDk3NzE0XzM1NDd8MzUwM3wzNTA0fDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTozOHwxMDAzOjIwLjAwfDEwMDI6MzAuMDA=", "alipay": false, "vendorCode": "46492", "productCode": "SD3933_0_106965_106965", "pLev": 88368, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 106965, "age": 30, "rCoup": 0, "kRSId": 106965, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 88368, "pStoreCode": "106965", "pickUpOnDoor": true, "aType": 0, "kVId": 46492, "sortInfo": {"p": "1", "s": "39.8", "c": "43"}, "kVehicleId": 4014, "comPriceCode": "[c]NzcxNDI4MDkwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCYzOGUmMzhmYWxzMDAxJiYkfDEmMzgkMSYzOCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTQ6NSAxOAAAAAAyNQAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "106965", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 28097714, "rLevel": 88368, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 679, "vendorDesc": "机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "服务周到", "qExposed": "5.0", "hasComment": 1, "commentCount": 679}, "storeScore": 78.75, "uniqueCode": "8a07a4a3b7bb45bb9f7e2419856bb7f7", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 8, "vendorName": "琼城租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_4", "binaryDigit": 128, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2208, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2559, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "两年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3547"}, {"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"category": 2, "sortNum": 48, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "可升级免停运折旧费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3872"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 8, "cyVendorName": "琼城租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "3367_8888_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "184270", "vehicleId": "3367_8888_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD8841", "elct": 0, "pLevel": 145906, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 19357, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTg0MjcwXzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzczMzQwODNfMzU0N3wzNTAzfDM1NjN8Mzc0NnwzNzg4fDQyMjl8Mzg3Ml8xMDAxOjM4fDEwMDM6MjAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "15001863", "productCode": "SD8841_0_184270_184270", "pLev": 1230776, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 184270, "age": 30, "rCoup": 0, "kRSId": 184270, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 1230776, "pStoreCode": "184270", "pickUpOnDoor": true, "aType": 0, "kVId": 15001863, "sortInfo": {"p": "1", "s": "39.8", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]MDgzfDczMzR8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "184270", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 7334083, "rLevel": 145906, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 2300, "vendorDesc": "三亚凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "取还方便", "qExposed": "5.0", "hasComment": 1, "commentCount": 2300}, "storeScore": 91.25, "uniqueCode": "ac57d1d27379427b9ed18c5c8e537b08", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 9, "vendorName": "金晟租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 65536, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2208, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 1.6958, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "三年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3548"}, {"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 9, "cyVendorName": "金晟租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1972046", "pickWayInfo": 1, "stockLevel": "D", "pCityId": 43, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "117740", "vehicleId": "1972046"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD5727", "elct": 0, "pLevel": 55049, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 12020, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTE3NzQwXzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzE5NzIwNDZfMzU0OHwzNTAzfDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTozOHwxMDAzOjIwLjAwfDEwMDI6MzAuMDA=", "alipay": false, "vendorCode": "30234", "productCode": "SD5727_0_117740_117740", "pLev": 55049, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 117740, "age": 30, "rCoup": 0, "kRSId": 117740, "packageId": "", "freeIllegalDeposit": false, "rLev": 55049, "pStoreCode": "117740", "pickUpOnDoor": true, "aType": 0, "kVId": 30234, "sortInfo": {"p": "1", "s": "39.8", "c": "43", "v": "94015"}, "kVehicleId": 4139, "comPriceCode": "[c]MDQ2fDE5NzJ8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "117740", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1972046, "rLevel": 55049, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 1085, "vendorDesc": "三亚威途租车", "level": "超棒", "maximumRating": 5, "commentLabel": "办理手续便捷", "qExposed": "5.0", "hasComment": 1, "commentCount": 1085}, "storeScore": 85.75, "uniqueCode": "d55ea395377c46bf99a4138217b82b6c", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 10, "vendorName": "三亚威途租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 33554432, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2272, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.7932, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 10, "cyVendorName": "三亚威途租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "4929070", "pickWayInfo": 1, "stockLevel": "E", "pCityId": 43, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "182401", "vehicleId": "4929070"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD8506", "elct": 0, "pLevel": 129059, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 16821, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTgyNDAxXzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzQ5MjkwNzBfMzUwM3wzNTA0fDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTozOHwxMDAzOjIwLjAwfDEwMDI6MzAuMDA=", "alipay": false, "vendorCode": "15001538", "productCode": "SD8506_0_182401_182401", "pLev": 129059, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 182401, "age": 30, "rCoup": 0, "kRSId": 182401, "packageId": "", "freeIllegalDeposit": false, "rLev": 129059, "pStoreCode": "182401", "pickUpOnDoor": true, "aType": 0, "kVId": 15001538, "sortInfo": {"p": "1", "s": "39.8", "c": "43", "v": "182401"}, "kVehicleId": 4139, "comPriceCode": "[c]MDcwfDQ5Mjl8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "182401", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 4929070, "rLevel": 129059, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 1657, "vendorDesc": "启航租车海棠湾店", "level": "超棒", "maximumRating": 5, "commentLabel": "店员专业", "qExposed": "5.0", "hasComment": 1, "commentCount": 1657}, "storeScore": 90, "uniqueCode": "88e62ff1e6b9400590038af6aaf1993a", "pickOffFee": 1, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 90, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 88, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 11, "vendorName": "三亚启航租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 32, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 66, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2080, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 30.6006, "actId": "3758", "pickUpFee": 1, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 22, "amountStr": "¥22", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11026", "amount": 1, "amountDesc": "¥1", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 1, "amountDesc": "¥1", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 88, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥90", "subAmount": 90, "name": "总价", "amountStr": "¥88"}], "vendorLogo": "", "distance": 30.6006, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员收费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 11, "cyVendorName": "三亚启航租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1908499", "pickWayInfo": 2, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员收费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 90, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "115473", "vehicleId": "1908499"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD4407", "elct": 0, "pLevel": 132053, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 4543, "rStoreNav": "店员收费上门送取车", "priceVersion": "SH-PRICEVERSION_MTE1NDczXzQxMzlfMV8zOF8zOF8zOF85MC4wMF8zNi4wXzg4LjBfMV8xXzAuMF8wLjBfMzAuMDBfMjAuMDBfMS4wMF8xLjAwXzE5MDg0OTlfMzUwM3wzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6Mzh8MTAwMzoyMC4wMHwxMTAyNjoxLjAwfDExMDI3OjEuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "62115", "productCode": "SD4407_0_115473_115473", "pLev": 43571, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 115473, "age": 30, "rCoup": 0, "kRSId": 115473, "packageId": "", "freeIllegalDeposit": false, "rLev": 43571, "pStoreCode": "115473", "pickUpOnDoor": true, "aType": 0, "kVId": 62115, "sortInfo": {"p": "2", "s": "38.27", "c": "43", "v": "92569"}, "kVehicleId": 4139, "comPriceCode": "[c]NDk5fDE5MDh8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 2, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "115473", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1908499, "rLevel": 132053, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 6, "vendorDesc": "三亚凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "服务周到", "qExposed": "5.0", "hasComment": 1, "commentCount": 6}, "storeScore": 74.86, "uniqueCode": "639bbb56b545461cb53055e42f8acb24", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 12, "vendorName": "西十出行", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_6", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 112, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2397, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "两年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3547"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 12, "cyVendorName": "西十出行", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "13301_8888_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "797672", "vehicleId": "13301_8888_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD12615", "elct": 0, "pLevel": 8603, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 112911, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzQ2MDEwNTQ2XzM1NDd8MzgxMHwzNTA0fDM1NjN8Mzc0Nl8xMDAxOjM4fDEwMDM6MjAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "15005425", "productCode": "SD12615_0_797672_797672", "pLev": 1368452, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 797672, "age": 30, "rCoup": 0, "kRSId": 797672, "freeIllegalDeposit": false, "rLev": 1368452, "pStoreCode": "797672", "pickUpOnDoor": true, "aType": 0, "kVId": 15005425, "sortInfo": {"p": "1", "s": "39.8", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]MDU0NjQ2MDEyNC0xfHwyMCAwMDowLTMwMCYzODAwOjBmYWxzJiYxJiYkfDFlJjM4MSYzODAwMSYxMDAzJjM4JDAuMDAmMSYyMDAkMSYyMC4xJjMwMDAyJjMwLjAuMDAmMDI0LTAkfDIwIDE5MTAtMzAwJjI6MzA6MTAtMzAyNC06MzA6MSAxOTAyNC0wMHwyNSAxODEwLTIyNQAAOjE0Og==", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "797672", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 46010546, "rLevel": 8603, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 59, "vendorDesc": "腾新出行", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 59}, "storeScore": 89.88, "uniqueCode": "c0c9328d85704edab0c3218094cded24", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 13, "vendorName": "腾新出行", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_6", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2288, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2521, "actId": "3783", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减2", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3783", "amount": 2, "amountDesc": "¥2", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 13, "cyVendorName": "腾新出行", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "85499_8888_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "834184", "vehicleId": "85499_8888_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD13636", "elct": 0, "pLevel": 1434224, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 257069, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_ODM0MTg0XzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzU1MDY2NjI4XzM4MTB8MzUwNHwzNjc5fDM3NDZfMTAwMTozOHwxMDAzOjIwLjAwfDEwMDI6MzAuMDA=", "alipay": false, "vendorCode": "15006407", "productCode": "SD13636_0_834184_834184", "pLev": 1434224, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 834184, "age": 30, "rCoup": 0, "kRSId": 834184, "freeIllegalDeposit": false, "rLev": 1434224, "pStoreCode": "834184", "pickUpOnDoor": true, "aType": 0, "kVId": 15006407, "sortInfo": {"p": "1", "s": "39.8", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]NjYyODU1MDYyNC0xfHwyMCAwMDowLTMwMCYzODAwOjBmYWxzJiYxJiYkfDFlJjM4MSYzODAwMSYxMDAzJjM4JDAuMDAmMSYyMDAkMSYyMC4xJjMwMDAyJjMwLjAuMDAmMDI0LTAkfDIwIDE5MTAtMzAwJjI6MzA6MTAtMzAyNC06MzA6MSAxOTAyNC0wMHwyNSAxODEwLTIyNQAAOjE0Og==", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减2", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "834184", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 55066628, "rLevel": 1434224, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.6", "qCommentCount": 15, "vendorDesc": "租呗租车", "level": "满意", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "4.6", "hasComment": 1, "commentCount": 15}, "storeScore": 44.69, "uniqueCode": "83642710983b47b6aca87a260b711b8a", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 14, "vendorName": "三亚租呗租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_5", "binaryDigit": 8388608, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2288, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.5484, "actId": "3783", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "两年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3547"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减2", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3783", "amount": 2, "amountDesc": "¥2", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 14, "cyVendorName": "三亚租呗租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "95523_8930_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "407345", "vehicleId": "95523_8930_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD11974", "elct": 0, "pLevel": 1566587, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 281940, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_NDA3MzQ1XzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzY0Njg2Njg2XzM1NDd8MzgxMHwzNTA0fDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTozOHwxMDAzOjIwLjAwfDEwMDI6MzAuMDA=", "alipay": false, "vendorCode": "15004807", "productCode": "SD11974_0_407345_407345", "pLev": 1566587, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 407345, "age": 30, "rCoup": 0, "kRSId": 407345, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 1566587, "pStoreCode": "407345", "pickUpOnDoor": true, "aType": 0, "kVId": 15004807, "sortInfo": {"p": "1", "s": "39.8", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]NjY4NjY0NjgwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCYzOCYzMCZ0cnVlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减2", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "407345", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 64686686, "rLevel": 1566587, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 25, "vendorDesc": "琼琚租车", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 25}, "storeScore": 84.73, "uniqueCode": "1f70a70fd65e46b28cfc1d0850e7e07e", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 15, "vendorName": "琼琚租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_6", "binaryDigit": 16777216, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 1.4613, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "三年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3548"}, {"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 15, "cyVendorName": "琼琚租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "91323_8888_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "1633255", "vehicleId": "91323_8888_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD14493", "elct": 0, "pLevel": 1571368, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 274254, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTYzMzI1NV80MTM5XzFfMzhfMzhfMzhfODguMDBfMzYuMF84Ni4wXzBfMF8wLjBfMC4wXzMwLjAwXzIwLjAwXzAuMDBfMC4wMF82MDgyMDYwN18zNTQ4fDM1MDN8MzUwNHwzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6Mzh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "15006961", "productCode": "SD14493_0_1633255_1633255", "pLev": 1620487, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 1633255, "age": 30, "rCoup": 0, "kRSId": 1633255, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 1620487, "pStoreCode": "1633255", "pickUpOnDoor": true, "aType": 0, "kVId": 15006961, "sortInfo": {"p": "1", "s": "39.8", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]MDYwNzYwODIwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCYzOGUmMzhmYWxzMDAxJiYkfDEmMzgkMSYzOCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTQ6NSAxOAAAAAAyNQAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1633255", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 60820607, "rLevel": 1571368, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 11, "vendorDesc": "鑫和润", "level": "超棒", "maximumRating": 5, "commentLabel": "取还方便", "qExposed": "5.0", "hasComment": 1, "commentCount": 11}, "storeScore": 74.25, "uniqueCode": "f76298e7a4fc4438822e3f7cd900a269", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 16, "vendorName": "鑫和润租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_6", "binaryDigit": 256, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2288, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.9778, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "三年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3548"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 16, "cyVendorName": "鑫和润租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "21649_8888_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "740013", "vehicleId": "21649_8888_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD13098", "elct": 0, "pLevel": 1299138, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 247864, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_NzQwMDEzXzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzQ3NzI2ODQxXzM1NDh8MzgxMHwzNTA0fDM2Nzl8Mzc0Nl8xMDAxOjM4fDEwMDM6MjAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "15005890", "productCode": "SD13098_0_740013_740013", "pLev": 1374283, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 740013, "age": 30, "rCoup": 0, "kRSId": 740013, "freeIllegalDeposit": false, "rLev": 1374283, "pStoreCode": "740013", "pickUpOnDoor": true, "aType": 0, "kVId": 15005890, "sortInfo": {"p": "1", "s": "39.8", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]Njg0MTQ3NzIyNC0xfHwyMCAwMDowLTMwMCYzODAwOjBmYWxzJiYxJiYkfDFlJjM4MSYzODAwMSYxMDAzJjM4JDAuMDAmMSYyMDAkMSYyMC4xJjMwMDAyJjMwLjAuMDAmMDI0LTAkfDIwIDE5MTAtMzAwJjI6MzA6MTAtMzAyNC06MzA6MSAxOTAyNC0wMHwyNSAxODEwLTIyNQAAOjE0Og==", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "740013", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 47726841, "rLevel": 1299138, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 112, "vendorDesc": "墨源租车", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 112}, "storeScore": 67.27, "uniqueCode": "b853854299bc42bf915083aab8f24c20", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 17, "vendorName": "墨源租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_5", "binaryDigit": 67108864, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.9563, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 17, "cyVendorName": "墨源租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "29681870", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "421268", "vehicleId": "29681870"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD12256", "elct": 0, "pLevel": 1107541, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 61286, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_NDIxMjY4XzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzI5NjgxODcwXzM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjM4fDEwMDM6MjAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "15005072", "productCode": "SD12256_0_421268_421268", "pLev": 1107541, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 421268, "age": 30, "rCoup": 0, "kRSId": 421268, "packageId": "", "freeIllegalDeposit": false, "rLev": 1107541, "pStoreCode": "421268", "pickUpOnDoor": true, "aType": 0, "kVId": 15005072, "sortInfo": {"p": "1", "s": "39.8", "c": "43", "v": "421268"}, "kVehicleId": 4139, "comPriceCode": "[c]MTg3MDI5NjgwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCYzOGUmMzhmYWxzMDAxJiYkfDEmMzgkMSYzOCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTQ6NSAxOAAAAAAyNQAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "421268", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 29681870, "rLevel": 1107541, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 5948, "vendorDesc": "明昊凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "服务周到", "qExposed": "4.9", "hasComment": 1, "commentCount": 5948}, "storeScore": 91.11, "uniqueCode": "d9e94fe43d324214ba72bb892625e3f9", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 18, "vendorName": "明昊租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 64, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2888, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "vendorLogo": "https://ak-d.tripcdn.com/images/0yc2l12000bzfo23oCC4C.png", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 18, "cyVendorName": "明昊租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "stock": 99999, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "18536", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "2961", "vehicleId": "18536"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD3012", "elct": 0, "pLevel": 8536, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 6, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_Mjk2MV80MTM5XzFfMzguMF8zOC4wXzAuMF84OC4wXzM2LjBfODYuMF8wXzBfMC4wXzAuMF8zMC4wXzIwLjBfMF8wXzIyNzk3N18zODEwfDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTozOC4wfDEwMDM6MjAuMHwxMDAyOjMwLjA=", "alipay": false, "vendorCode": "13032", "productCode": "SD3012_0_2961_2961", "pLev": 918184, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 2961, "age": 30, "rCoup": 0, "kRSId": 2961, "packageId": "Secure", "freeIllegalDeposit": false, "rLev": 918184, "pStoreCode": "2961", "pickUpOnDoor": true, "aType": 0, "kVId": 13032, "sortInfo": {"p": "1", "s": "39.8", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "eyJzZWxsZXJpZCI6MTIwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6ODUzNiwiYWN0Z2V0aWQiOjg1MzYsImFjdG9mZmlkIjo4NTM2LCJjYXJ0eXBlaWQiOjE4NTM2LCJ0b3RhbCI6ODgsInRpbWUiOjE3Mjk4NTA5NzJ9", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "2961", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 227977, "rLevel": 8536, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "isOrderVehicle": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 191, "vendorDesc": "三亚海程汽车管理服务有限公司", "level": "超棒", "maximumRating": 5, "commentLabel": "取还方便", "qExposed": "4.9", "hasComment": 1, "commentCount": 191}, "storeScore": 43.27, "uniqueCode": "b333399fed0d4570a0558c8caaeea68c", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 98, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 45, "currentTotalPrice": 95, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 19, "vendorName": "海程租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 2097152, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 176, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.2422, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "两年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3547"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "originalDailyPrice": 48, "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 95, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥95"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 19, "cyVendorName": "海程租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "4125_53432_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 98, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 45, "mergeInfo": [{"storeId": "107759", "vehicleId": "4125_53432_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 45}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD3850", "elct": 0, "pLevel": 28549, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 7203, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTA3NzU5XzQxMzlfMV80OF80OF80OF85OC4wMF80NS4wXzk1LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzI0NjU2NDA5XzM1NDd8MzgxMHwzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6NDh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "82987", "productCode": "SD3850_0_107759_107759", "pLev": 28549, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 107759, "age": 30, "rCoup": 0, "kRSId": 107759, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 28549, "pStoreCode": "107759", "pickUpOnDoor": true, "aType": 0, "kVId": 82987, "sortInfo": {"p": "1", "s": "33.61", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]NjQwOTI0NjUwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCY0OGUmNDhmYWxzMDAxJiYkfDEmNDgkMSY0OCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTQ6NSAxOAAAAAAyNQAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "107759", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 24656409, "rLevel": 28549, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 34, "vendorDesc": "启航租车", "level": "超棒", "maximumRating": 5, "commentLabel": "取还方便", "qExposed": "5.0", "hasComment": 1, "commentCount": 34}, "storeScore": 66.96, "uniqueCode": "5d135856b5214626823c6e3c422276c8", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 20, "vendorName": "启航商旅租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_5", "binaryDigit": 262144, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 496, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.399, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 20, "cyVendorName": "启航商旅租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "25267446", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "326481", "vehicleId": "25267446"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD11817", "elct": 0, "pLevel": 1216433, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 50401, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MzI2NDgxXzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzI1MjY3NDQ2XzM4MTB8MzUwNHwzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6Mzh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "15004677", "productCode": "SD11817_0_326481_326481", "pLev": 1216433, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 326481, "age": 30, "rCoup": 0, "kRSId": 326481, "packageId": "", "freeIllegalDeposit": false, "rLev": 1216433, "pStoreCode": "326481", "pickUpOnDoor": true, "aType": 0, "kVId": 15004677, "sortInfo": {"p": "1", "s": "39.8", "c": "43", "v": "326481"}, "kVehicleId": 4139, "comPriceCode": "[c]NzQ0NjI1MjYwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCYzOGUmMzhmYWxzMDAxJiYkfDEmMzgkMSYzOCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTQ6NSAxOAAAAAAyNQAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "326481", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 25267446, "rLevel": 1216433, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 854, "vendorDesc": "旺亚租车三亚店", "level": "超棒", "maximumRating": 5, "commentLabel": "免费接送", "qExposed": "5.0", "hasComment": 1, "commentCount": 854}, "storeScore": 89.58, "uniqueCode": "bf2e9e06faa3499ab38943e6af1c8dbd", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 21, "vendorName": "旺亚租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 536870912, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 176, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 4.1858, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 21, "cyVendorName": "旺亚租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "52104347", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "114307", "vehicleId": "52104347"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD4382", "elct": 0, "pLevel": 43275, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 4302, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTE0MzA3XzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzUyMTA0MzQ3XzM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjM4fDEwMDM6MjAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "61659", "productCode": "SD4382_0_114307_114307", "pLev": 43275, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 114307, "age": 30, "rCoup": 0, "kRSId": 114307, "packageId": "", "freeIllegalDeposit": false, "rLev": 43275, "pStoreCode": "114307", "pickUpOnDoor": true, "aType": 0, "kVId": 61659, "sortInfo": {"p": "1", "s": "39.8", "c": "43", "v": "60907"}, "kVehicleId": 4139, "comPriceCode": "[c]NDM0NzUyMTAwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCYzOGUmMzhmYWxzMDAxJiYkfDEmMzgkMSYzOCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTQ6NSAxOAAAAAAyNQAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "114307", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 52104347, "rLevel": 43275, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.8", "qCommentCount": 7, "vendorDesc": "橘子租车", "level": "很好", "maximumRating": 5, "commentLabel": "取还方便", "qExposed": "4.8", "hasComment": 1, "commentCount": 7}, "storeScore": 65.32, "uniqueCode": "18c136907247448383c6fddfe9cd1074", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 58, "oTPrice": 108, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 58, "priceType": 1, "currentDailyPrice": 55, "currentTotalPrice": 105, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 22, "vendorName": "三亚橘子租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_4", "binaryDigit": 524288, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.4065, "actId": "3783", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减3", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "licenseTag": "", "fees": [{"amount": 55, "detail": [{"code": "1001", "amount": 58, "amountDesc": "¥58", "name": "租车费"}, {"code": "3783", "amount": 3, "amountDesc": "¥3", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥55", "originalDailyPrice": 58, "subAmount": 55, "name": "车辆租金", "amountStr": "¥55"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 105, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥108", "subAmount": 108, "name": "总价", "amountStr": "¥105"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 22, "cyVendorName": "三亚橘子租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "94774_13473_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 108, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 55, "mergeInfo": [{"storeId": "228910", "vehicleId": "94774_13473_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 55}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD10288", "elct": 0, "pLevel": 1187957, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 65500, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MjI4OTEwXzQwMTRfMV81OF81OF81OF8xMDguMDBfNTUuMF8xMDUuMF8wXzBfMC4wXzAuMF8zMC4wMF8yMC4wMF8wLjAwXzAuMDBfNjMwNzcwODVfMzgxMHwzNTA0fDM1NjN8Mzc0Nl8xMDAxOjU4fDEwMDM6MjAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "15003255", "productCode": "SD10288_0_228910_228910", "pLev": 1187957, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 228910, "age": 30, "rCoup": 0, "kRSId": 228910, "freeIllegalDeposit": false, "rLev": 1187957, "pStoreCode": "228910", "pickUpOnDoor": true, "aType": 0, "kVId": 15003255, "sortInfo": {"p": "1", "s": "28.35", "c": "43"}, "kVehicleId": 4014, "comPriceCode": "[c]NzA4NTYzMDcyNC0xfHwyMCAwMDowLTMwMCY1ODAwOjBmYWxzJiYxJiYkfDFlJjU4MSY1ODAwMSYxMDAzJjU4JDAuMDAmMSYyMDAkMSYyMC4xJjMwMDAyJjMwLjAuMDAmMDI0LTAkfDIwIDE5MTAtMzAwJjI6MzA6MTAtMzAyNC06MzA6MSAxOTAyNC0wMHwyNSAxODEwLTIyNQAAOjE0Og==", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减3", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "228910", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 63077085, "rLevel": 1187957, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.7", "qCommentCount": 96, "vendorDesc": "海南文东汽车服务有限公司", "level": "很好", "maximumRating": 5, "commentLabel": "店员专业", "qExposed": "4.7", "hasComment": 1, "commentCount": 96}, "storeScore": 36.11, "uniqueCode": "806e23d88f3749438b97a608d8e21023", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 103, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 101, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 23, "vendorName": "文东租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 256, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 160, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 4.5477, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 101, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥103", "subAmount": 103, "name": "总价", "amountStr": "¥101"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 23, "cyVendorName": "文东租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1919961", "pickWayInfo": 1, "stockLevel": "E", "pCityId": 43, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 103, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "114447", "vehicleId": "1919961"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD4445", "elct": 0, "pLevel": 182454, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 4822, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTE0NDQ3XzQwMTRfMV8zOF8zOF8zOF8xMDMuMDBfMzYuMF8xMDEuMF8wXzBfMC4wXzAuMF8zMC4wMF8zNS4wMF8wLjAwXzAuMDBfMTkxOTk2MV8zNTAzfDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTozOHwxMDAzOjM1LjAwfDEwMDI6MzAuMDA=", "alipay": false, "vendorCode": "62863", "productCode": "SD4445_0_114447_114447", "pLev": 182454, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 114447, "age": 30, "rCoup": 0, "kRSId": 114447, "packageId": "", "freeIllegalDeposit": false, "rLev": 182454, "pStoreCode": "114447", "pickUpOnDoor": true, "aType": 0, "kVId": 62863, "sortInfo": {"p": "1", "s": "30.28", "c": "43", "v": "62395"}, "kVehicleId": 4014, "comPriceCode": "[c]OTYxfDE5MTl8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMzUwMDMmMzUuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "114447", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1919961, "rLevel": 182454, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 1222, "vendorDesc": "三亚常晟租车", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 1222}, "storeScore": 33.75, "uniqueCode": "36751d9d83b7450abdb60564de065312", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 24, "vendorName": "常晟租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 4194304, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 432, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.239, "actId": "3783", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减2", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3783", "amount": 2, "amountDesc": "¥2", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 24, "cyVendorName": "常晟租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1916013", "pickWayInfo": 1, "stockLevel": "E", "pCityId": 43, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "114575", "vehicleId": "1916013"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD4734", "elct": 0, "pLevel": 47132, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 5701, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTE0NTc1XzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzE5MTYwMTNfMzgxMHwzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6Mzh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "77081", "productCode": "SD4734_0_114575_114575", "pLev": 47132, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 114575, "age": 30, "rCoup": 0, "kRSId": 114575, "packageId": "", "freeIllegalDeposit": false, "rLev": 47132, "pStoreCode": "114575", "pickUpOnDoor": true, "aType": 0, "kVId": 77081, "sortInfo": {"p": "1", "s": "39.8", "c": "43", "v": "79023"}, "kVehicleId": 4139, "comPriceCode": "[c]MDEzfDE5MTZ8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减2", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "114575", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1916013, "rLevel": 47132, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 1601, "vendorDesc": "小飞侠租车", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 1601}, "storeScore": 66.76, "uniqueCode": "751fa611266f4a8ba85b9743bc1f814b", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 38, "oTPrice": 88, "naked": true, "deductInfos": [{"totalAmount": 2, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 36, "currentTotalPrice": 86, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 25, "vendorName": "小飞侠租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 432, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.2418, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥36", "originalDailyPrice": 38, "subAmount": 36, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 86, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥86"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 25, "cyVendorName": "小飞侠租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1913476", "pickWayInfo": 1, "stockLevel": "E", "pCityId": 43, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 88, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 36, "mergeInfo": [{"storeId": "114886", "vehicleId": "1913476"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 36}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD4849", "elct": 0, "pLevel": 68967, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 5887, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTE0ODg2XzQxMzlfMV8zOF8zOF8zOF84OC4wMF8zNi4wXzg2LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzE5MTM0NzZfMzgxMHwzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6Mzh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "80431", "productCode": "SD4849_0_114886_114886", "pLev": 48018, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 114886, "age": 30, "rCoup": 0, "kRSId": 114886, "packageId": "", "freeIllegalDeposit": false, "rLev": 48018, "pStoreCode": "114886", "pickUpOnDoor": true, "aType": 0, "kVId": 80431, "sortInfo": {"p": "1", "s": "39.8", "c": "43", "v": "83647"}, "kVehicleId": 4139, "comPriceCode": "[c]NDc2fDE5MTN8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减2", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "114886", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1913476, "rLevel": 68967, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 153, "vendorDesc": "中进汽车租赁三亚店", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "4.9", "hasComment": 1, "commentCount": 153}, "storeScore": 73.56, "uniqueCode": "901e5ece16024dcbbb03bac9c4512fc6", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 98, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 45, "currentTotalPrice": 95, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 26, "vendorName": "海南中进租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 131072, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 160, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 1.3064, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "originalDailyPrice": 48, "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 95, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥95"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 26, "cyVendorName": "海南中进租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1965861", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 98, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 45, "mergeInfo": [{"storeId": "116523", "vehicleId": "1965861"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 45}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD6573", "elct": 0, "pLevel": 67912, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 9036, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTE2NTIzXzQxMzlfMV80OF80OF80OF85OC4wMF80NS4wXzk1LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzE5NjU4NjFfMzUwM3wzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6NDh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "74373", "productCode": "SD6573_0_116523_116523", "pLev": 67912, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 116523, "age": 30, "rCoup": 0, "kRSId": 116523, "packageId": "", "freeIllegalDeposit": false, "rLev": 67912, "pStoreCode": "116523", "pickUpOnDoor": true, "aType": 0, "kVId": 74373, "sortInfo": {"p": "1", "s": "33.61", "c": "43", "v": "75869"}, "kVehicleId": 4139, "comPriceCode": "[c]ODYxfDE5NjV8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjQ4JiY0OCZhbHNlMDEmMSR8MTA0OCQxJjQ4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "116523", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1965861, "rLevel": 67912, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 115, "vendorDesc": "机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆干净整洁", "qExposed": "5.0", "hasComment": 1, "commentCount": 115}, "storeScore": 63.24, "uniqueCode": "5db51c518a584d3cbb894fb602fa39ff", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 58, "oTPrice": 108, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 58, "priceType": 1, "currentDailyPrice": 55, "currentTotalPrice": 105, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 27, "vendorName": "麒麟恒泰租车", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_4", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.2911, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 55, "detail": [{"code": "1001", "amount": 58, "amountDesc": "¥58", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥55", "originalDailyPrice": 58, "subAmount": 55, "name": "车辆租金", "amountStr": "¥55"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 105, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥108", "subAmount": 108, "name": "总价", "amountStr": "¥105"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 27, "cyVendorName": "麒麟恒泰租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "93018_53432_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 108, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 55, "mergeInfo": [{"storeId": "183156", "vehicleId": "93018_53432_pupai"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 55}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD8606", "elct": 0, "pLevel": 133456, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 18273, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTgzMTU2XzQxMzlfMV81OF81OF81OF8xMDguMDBfNTUuMF8xMDUuMF8wXzBfMC4wXzAuMF8zMC4wMF8yMC4wMF8wLjAwXzAuMDBfNjI1NzkzNjRfMzY3OXwzNzQ2XzEwMDE6NTh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "15001635", "productCode": "SD8606_0_183156_183156", "pLev": 133456, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 183156, "age": 30, "rCoup": 0, "kRSId": 183156, "freeIllegalDeposit": false, "rLev": 133456, "pStoreCode": "183156", "pickUpOnDoor": true, "aType": 0, "kVId": 15001635, "sortInfo": {"p": "1", "s": "28.35", "c": "43"}, "kVehicleId": 4139, "comPriceCode": "[c]OTM2NDYyNTcyNC0xfHwyMCAwMDowLTMwMCY1ODAwOjBmYWxzJiYxJiYkfDFlJjU4MSY1ODAwMSYxMDAzJjU4JDAuMDAmMSYyMDAkMSYyMC4xJjMwMDAyJjMwLjAuMDAmMDI0LTAkfDIwIDE5MTAtMzAwJjI6MzA6MTAtMzAyNC06MzA6MSAxOTAyNC0wMHwyNSAxODEwLTIyNQAAOjE0Og==", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "183156", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 62579364, "rLevel": 133456, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_4139_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "0.0", "qCommentCount": 0, "vendorDesc": "盛楠出行", "level": "", "maximumRating": 5, "commentLabel": "", "qExposed": "0.0", "hasComment": 0, "commentCount": 0}, "storeScore": 25, "uniqueCode": "58cecc5e10734516b7d883ef85b31513", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 99, "oTPrice": 159, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 99, "currentTotalPrice": 159, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 28, "vendorName": "盛楠出行", "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_6", "binaryDigit": 33554432, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 48, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.4155, "actId": "", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 48, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "可升级免停运折旧费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3872"}], "licenseTag": "", "fees": [{"amount": 99, "detail": [{"code": "1001", "amount": 99, "amountDesc": "¥99", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥99", "subAmount": 99, "name": "车辆租金", "amountStr": "¥99"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 159, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥159", "subAmount": 159, "name": "总价", "amountStr": "¥159"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "4139", "newCar": false, "secretBox": false, "sortScore": 28, "cyVendorName": "盛楠出行", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "57197960", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 159, "recommendOrder": 0, "mergeId": 1216, "vdegree": "0", "rectype": 1, "totalDailyPrice": 99, "mergeInfo": [{"storeId": "1633290", "vehicleId": "57197960"}], "grantedcode": "", "isrec": false, "cvid": 4139, "rentalamount": 99}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD14279", "elct": 0, "pLevel": 1570591, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 270446, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTYzMzI5MF80MTM5XzFfOTlfOTlfOTlfMTU5LjAwXzk5XzE1OS4wXzBfMF8wLjBfMC4wXzMwLjAwXzMwLjAwXzAuMDBfMC4wMF81NzE5Nzk2MF8zODEwfDM2Nzl8Mzc0NnwzODcyXzEwMDE6OTl8MTAwMzozMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "15006997", "productCode": "SD14279_0_1633290_1633290", "pLev": 1570591, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 1633290, "age": 30, "rCoup": 0, "kRSId": 1633290, "freeIllegalDeposit": false, "rLev": 1570591, "pStoreCode": "1633290", "pickUpOnDoor": true, "aType": 0, "kVId": 15006997, "sortInfo": {"p": "1", "s": "14.01", "c": "43", "v": "1633290"}, "kVehicleId": 4139, "comPriceCode": "[c]Nzk2MDU3MTkyNC0xfHwyMCAwMDowLTMwMCY5OTAwOjBmYWxzJiYxJiYkfDFlJjk5MSY5OTAwMSYxMDAzJjk5JDAuMDAmMSYzMDAkMSYzMC4xJjMwMDAyJjMwLjAuMDAmMDI0LTAkfDIwIDE5MTAtMzAwJjI6MzA6MTAtMzAyNC06MzA6MSAxOTAyNC0wMHwyNSAxODEwLTIyNQAAOjE0Og==", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "1633290", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 57197960, "rLevel": 1570591, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}]}, "ResponseStatus": {"Extension": [{"Value": "7151353417314608140", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a60124b-480514-619509", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1729851322896+0800)/"}, "imStatus": 1, "recommendProducts": [{"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "11037", "amount": 45, "amountDesc": "¥45", "name": "优惠券"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 48, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥50"}], "reference": {"vehicleCode": "0", "rStoreCode": "78", "packageId": "Secure", "pLev": 129, "comPriceCode": "eyJzZWxsZXJpZCI6MTEwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6Mjc2MSwiYWN0Z2V0aWQiOjI3NjEsImFjdG9mZmlkIjoyNzYxLCJjYXJ0eXBlaWQiOjE5NDM0LCJ0b3RhbCI6OTgsInRpbWUiOjE3Mjk4NTA5NzJ9", "bizVendorCode": "SD14450", "pStoreCode": "78", "packageType": 1, "priceVersion": "SH-PRICEVERSION_NzhfMzE1OV8xXzQ4LjBfNDguMF8wLjBfOTguMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMF8yMC4wXzBfMF8zNTU0OA==", "sendTypeForPickUpCar": 0, "skuId": 35548, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 129, "vendorCode": "13031", "vendorVehicleCode": "19434"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 226396, "bizVendorCode": "SD3012"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4634728, "bizVendorCode": "SD8142"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 51447012, "bizVendorCode": "SD12256"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 28097980, "bizVendorCode": "SD3933"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 3119746, "bizVendorCode": "SD3746"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 62531344, "bizVendorCode": "SD8606"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 24652041, "bizVendorCode": "SD14476"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 55808236, "bizVendorCode": "SD11466"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 21320580, "bizVendorCode": "SD6991"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 18331475, "bizVendorCode": "SD6991"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 55019805, "bizVendorCode": "SD12290"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 56695598, "bizVendorCode": "SD5538"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 24985423, "bizVendorCode": "SD11828"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 57002072, "bizVendorCode": "SD13636"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减48", "groupId": 1}, "vehicleCode": "3159", "highestPrice": 379, "pWay": "可选：免费站内取还车", "minDPrice": 0, "vehicleKey": "0_3159_", "hot": 0, "minTPrice": 50, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 8, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD14450_0_78_78"], "introduce": "当前车型最低价"}, "minDOrinPrice": 48, "isEasy": true, "isCredit": true, "maximumCommentCount": 45511, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 15}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "11037", "amount": 45, "amountDesc": "¥45", "name": "优惠券"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 48, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥50"}], "reference": {"vehicleCode": "0", "rStoreCode": "193947", "packageId": "sec", "pLev": 1139587, "comPriceCode": "[c]MDk1MjMwNTQwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCY0OGUmNDhmYWxzMDAxJiYkfDEmNDgkMSY0OCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTQ6NSAxOAAAAAAyNQAA", "bizVendorCode": "SD4201", "pStoreCode": "193947", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTkzOTQ3XzQ0OTNfMV80OF80OF80OF85OC4wMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzMwNTQwOTUy", "sendTypeForPickUpCar": 0, "skuId": 30540952, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1139587, "vendorCode": "13119", "vendorVehicleCode": "2148_10905_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 60352531, "bizVendorCode": "SD3926"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6900277, "bizVendorCode": "SD3866"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 63100339, "bizVendorCode": "SD4102"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 24656423, "bizVendorCode": "SD3850"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860588, "bizVendorCode": "SD4113"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 3883530, "bizVendorCode": "SD5349"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 46121671, "bizVendorCode": "SD12615"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44835274, "bizVendorCode": "SD7678"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 55009515, "bizVendorCode": "SD13636"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 62763667, "bizVendorCode": "SD13921"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 43673680, "bizVendorCode": "SD8155"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 54183865, "bizVendorCode": "SD8506"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 61502442, "bizVendorCode": "SD14543"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 28097896, "bizVendorCode": "SD3933"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 60056298, "bizVendorCode": "SD14476"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 46197698, "bizVendorCode": "SD13482"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2235814, "bizVendorCode": "SD6573"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 55924702, "bizVendorCode": "SD13696"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 57143409, "bizVendorCode": "SD6370"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 25635982, "bizVendorCode": "SD6991"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 62755876, "bizVendorCode": "SD8539"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 54437342, "bizVendorCode": "SD5538"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 7971944, "bizVendorCode": "SD8638"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减48", "groupId": 1}, "vehicleCode": "4493", "highestPrice": 378, "pWay": "可选：免费站内取还车", "minDPrice": 0, "vehicleKey": "0_4493_", "hot": 0, "minTPrice": 50, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 30, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD4201_0_193947_193947"], "introduce": "当前车型最低价"}, "minDOrinPrice": 48, "isEasy": true, "isCredit": true, "maximumCommentCount": 52282, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 24}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "11037", "amount": 45, "amountDesc": "¥45", "name": "优惠券"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 48, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥50"}], "reference": {"vehicleCode": "0", "rStoreCode": "183936", "packageId": "sec", "pLev": 141409, "comPriceCode": "[c]NzQwODI0NjkwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCY0OGUmNDhmYWxzMDAxJiYkfDEmNDgkMSY0OCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTQ6NSAxOAAAAAAyNQAA", "bizVendorCode": "SD3926", "pStoreCode": "183936", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgzOTM2XzU0MDdfMV80OF80OF80OF85OC4wMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzI0Njk3NDA4", "sendTypeForPickUpCar": 0, "skuId": 24697408, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 141409, "vendorCode": "13082", "vendorVehicleCode": "5596_55117_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6900273, "bizVendorCode": "SD3866"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 24587431, "bizVendorCode": "SD4102"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 45464161, "bizVendorCode": "SD8893"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 64674345, "bizVendorCode": "SD14035"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 46121699, "bizVendorCode": "SD12615"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 58528170, "bizVendorCode": "SD11232"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44835820, "bizVendorCode": "SD7678"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4696845, "bizVendorCode": "SD4518"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 59118228, "bizVendorCode": "SD4755"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44682611, "bizVendorCode": "SD8486"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 54247537, "bizVendorCode": "SD4723"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 47765537, "bizVendorCode": "SD12983"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 3388020, "bizVendorCode": "SD3987"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2428339, "bizVendorCode": "SD5727"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 24656157, "bizVendorCode": "SD3850"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 62556145, "bizVendorCode": "SD7103"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 30001532, "bizVendorCode": "SD11544"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 25177384, "bizVendorCode": "SD11817"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 51441167, "bizVendorCode": "SD10763"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 56560414, "bizVendorCode": "SD10983"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 41250091, "bizVendorCode": "SD14476"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 62450557, "bizVendorCode": "SD12877"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 25022215, "bizVendorCode": "SD10542"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4867132, "bizVendorCode": "SD8383"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 24916235, "bizVendorCode": "SD8142"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 45105103, "bizVendorCode": "SD12256"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1910574, "bizVendorCode": "SD4374"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 24662247, "bizVendorCode": "SD3127"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 28097938, "bizVendorCode": "SD3933"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 54542328, "bizVendorCode": "SD8423"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 29317212, "bizVendorCode": "SD6519"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 52808953, "bizVendorCode": "SD13636"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44060367, "bizVendorCode": "SD8032"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 62763590, "bizVendorCode": "SD13921"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 7679939, "bizVendorCode": "SD6835"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 58068074, "bizVendorCode": "SD12471"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44384894, "bizVendorCode": "SD4552"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 43673666, "bizVendorCode": "SD8155"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 54511794, "bizVendorCode": "SD5809"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 63082769, "bizVendorCode": "SD4700"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 61502498, "bizVendorCode": "SD14543"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6753838, "bizVendorCode": "SD3746"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 39487596, "bizVendorCode": "SD8606"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44658461, "bizVendorCode": "SD8841"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 45531004, "bizVendorCode": "SD11844"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 51447621, "bizVendorCode": "SD8506"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44383130, "bizVendorCode": "SD8338"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1862480, "bizVendorCode": "SD4074"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 39505782, "bizVendorCode": "SD10070"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 52358699, "bizVendorCode": "SD13340"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 62970713, "bizVendorCode": "SD14509"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2361441, "bizVendorCode": "SD6707"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44336335, "bizVendorCode": "SD7724"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 62804043, "bizVendorCode": "SD11658"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 46127572, "bizVendorCode": "SD13098"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 40471964, "bizVendorCode": "SD12389"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 55924849, "bizVendorCode": "SD13696"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 39472168, "bizVendorCode": "SD8656"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 57146531, "bizVendorCode": "SD6370"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 52242058, "bizVendorCode": "SD5100"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44576764, "bizVendorCode": "SD12899"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 28329841, "bizVendorCode": "SD7857"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 60810793, "bizVendorCode": "SD11516"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 40472839, "bizVendorCode": "SD7000"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4605815, "bizVendorCode": "SD4406"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 63077015, "bizVendorCode": "SD10288"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 11240664, "bizVendorCode": "SD6991"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 16622677, "bizVendorCode": "SD6991"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 58561476, "bizVendorCode": "SD14280"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 59879408, "bizVendorCode": "SD8568"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5194505, "bizVendorCode": "SD5048"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 30564878, "bizVendorCode": "SD9781"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4885138, "bizVendorCode": "SD5591"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 57105098, "bizVendorCode": "SD9809"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 25016083, "bizVendorCode": "SD11828"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5738546, "bizVendorCode": "SD9625"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减48", "groupId": 1}, "vehicleCode": "5407", "highestPrice": 368, "pWay": "可选：免费站内取还车", "minDPrice": 0, "vehicleKey": "0_5407_", "hot": 0, "minTPrice": 50, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 32, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3926_0_183936_183936"], "introduce": "当前车型最低价"}, "minDOrinPrice": 48, "isEasy": true, "isCredit": true, "maximumCommentCount": 51270, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 77}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 38, "amountDesc": "¥38", "name": "租车费"}, {"code": "11037", "amount": 36, "amountDesc": "¥36", "name": "优惠券"}, {"code": "3758", "amount": 2, "amountDesc": "¥2", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 38, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥88", "subAmount": 88, "name": "总价", "amountStr": "¥50"}], "reference": {"vehicleCode": "0", "rStoreCode": "183936", "packageId": "sec", "pLev": 141409, "comPriceCode": "[c]MDA4fDY5MDF8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjM4JiYzOCZhbHNlMDEmMSR8MTAzOCQxJjM4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExNDoyIDE4OgAAAAA1AAAA", "bizVendorCode": "SD3926", "pStoreCode": "183936", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgzOTM2XzQ0OThfMV8zOF8zOF8zOF84OC4wMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzY5MDEwMDg=", "sendTypeForPickUpCar": 0, "skuId": 6901008, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 141409, "vendorCode": "13082", "vendorVehicleCode": "661_27573_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6900274, "bizVendorCode": "SD3866"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 46121713, "bizVendorCode": "SD12615"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 40460274, "bizVendorCode": "SD11010"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减38", "groupId": 1}, "vehicleCode": "4498", "highestPrice": 245, "pWay": "可选：免费站内取还车", "minDPrice": 0, "vehicleKey": "0_4498_", "hot": 0, "minTPrice": 50, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 55, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3926_0_183936_183936"], "introduce": "当前车型最低价"}, "minDOrinPrice": 38, "isEasy": true, "isCredit": true, "maximumCommentCount": 51270, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 4}], "checkResponseTime": 1729851322816.428, "checkRequestTime": 1729851322649.0679, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 447, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 447, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1729851322647, "afterFetch": 1729851323094}}