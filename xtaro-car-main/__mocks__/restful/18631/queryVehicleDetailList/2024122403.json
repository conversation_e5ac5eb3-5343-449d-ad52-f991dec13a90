{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "bef83406-f446-4f7f-8151-da835ea9a496", "cost": 61}, "vehicleInfo": {"brandEName": "丰田", "brandName": "丰田", "name": "丰田亚洲龙", "zhName": "丰田亚洲龙", "vehicleCode": "5193", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "2.0L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6g1200000mggv9402F.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": [], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "oilType": 3, "fuelType": "汽油", "luggageNum": "可放2个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "81", "multimediaAlbums": [{"albumName": "官方相册", "note": "年款/颜色等以门店为准", "albumType": 1, "mediaGroup": [{"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "cover": "", "sortNum": 0}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}]}], "mediaTypes": [], "vehicleKey": "0_5193_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "snowTyre": {"type": 0, "typeDesc": "不支持"}, "reverseImage": {"type": 1, "typeDesc": "支持"}, "reverseSensor": {"type": 1, "typeDesc": "支持"}}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "detailPageTags": [{"title": "机场内取车", "specificProductGroups": {}, "filteredProductGroups": {"vendorPriceList": []}}, {"title": "机场外取车", "specificProductGroups": {}, "filteredProductGroups": {"vendorPriceList": []}}], "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "isFromSearch": false, "promptInfos": [{"type": 19, "locations": [{"groupCode": "all", "index": 1}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg3112000e8l73mt525E.png", "jumpUrl": "https://m.ctrip.com/tangram/OTI2MjU=?ctm_ref=vactang_page_92625&isHideNavBar=YES&apppgid=10650039395&statusBarStyle=1&channelid=238407"}, {"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}, {"type": 18, "locations": [{"groupCode": "all", "index": 2}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg5t12000cixdsaaBF66.png"}], "promotMap": {}, "extras": {"packageLevelAB": "B", "selfServiceSwitch": "1", "isLicensePlateHideShow": "0", "packageLevelSwitch": "1", "commodityClass2Version": "1", "abVersion": "241008_DSJT_ykjpx|A,240419_DSJT_wyz24|B,230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,231218_DSJT_zzqh|B,231218_DSJT_zzqh|B", "isNewLicensePlate": "0", "serverRequestId": "57457R3Z6w6B3C3XK950", "prepProductGroupTopSwitch": "0"}, "imStatus": 0, "recommendProducts": [{"vehicleCode": "5193", "sortNum": 8, "lowestPrice": 0, "highestPrice": 0, "maximumRating": 4.4, "maximumCommentCount": 10, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3926", "vendorCode": "37573", "pStoreCode": "106896", "rStoreCode": "106896", "vehicleCode": "0", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzUxOTNfMV8xMDAuMF8yMDAuMF8wLjBfMzAwLjBfMF8xMDAuMF8wXzBfMC4wXzAuMF84MC4wXzIwLjBfMF8wXzEwOTI0Nzc1XzM1MDl8MzgxMHwzNjc5fDM3NDZ8Mzc3OXw0MjAxXzEwMDE6MjAwLjB8MTAwMzoyMC4wfDEwMDI6ODAuMA==", "vendorVehicleCode": "1448_48644_pupai", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 10924775, "pLev": 100619, "rLev": 100619, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 0, "amountStr": "¥0", "subAmount": 0, "subAmountStr": "日均¥0", "originalDailyPrice": 100.0, "detail": [{"code": "1001", "name": "租车费", "amount": 200.0, "amountDesc": "¥200"}, {"code": "11037", "name": "优惠券", "amount": 200, "amountDesc": "¥200"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80.0, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80.0, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 100.0, "amountStr": "¥100", "subAmount": 300.0, "subAmountStr": "¥300", "currencyCode": "¥"}]}], "reactId": "1841493320", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3926_0_106896_106896"]}, "minTPrice": 100.0, "minDPrice": 0, "modifySameVehicle": false, "minDOrinPrice": 100.0, "outTags": [], "pTag": {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减200", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isOptim": true, "isEasy": false, "isCredit": false, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_5193_"}, {"vehicleCode": "4039", "sortNum": 25, "lowestPrice": 77.0, "highestPrice": 77.0, "maximumRating": 4.4, "maximumCommentCount": 10, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3926", "vendorCode": "37573", "pStoreCode": "106896", "rStoreCode": "106896", "vehicleCode": "0", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzQwMzlfMV8xODguMF8zNzYuMF8wLjBfNDc2LjBfNzcuMF8yNTQuMF8wXzBfMC4wXzAuMF84MC4wXzIwLjBfMF8wXzY4ODQ1OTc0", "vendorVehicleCode": "1495_55056_pupai", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 68845974, "pLev": 100619, "rLev": 100619, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 154.0, "amountStr": "¥154", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 188.0, "detail": [{"code": "1001", "name": "租车费", "amount": 376.0, "amountDesc": "¥376"}, {"code": "11037", "name": "优惠券", "amount": 222, "amountDesc": "¥222"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80.0, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80.0, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 254.0, "amountStr": "¥254", "subAmount": 476.0, "subAmountStr": "¥476", "currencyCode": "¥"}]}], "reactId": "1841493321", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3926_0_106896_106896"]}, "minTPrice": 254.0, "minDPrice": 77.0, "modifySameVehicle": false, "minDOrinPrice": 188.0, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减222", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isOptim": true, "isEasy": false, "isCredit": true, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_4039_"}, {"vehicleCode": "1149", "sortNum": 37, "lowestPrice": 3.0, "highestPrice": 3.0, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD8256", "vendorCode": "32231", "pStoreCode": "107102", "rStoreCode": "107102", "vehicleCode": "0", "packageType": 1, "comPriceCode": "[c]NjY1fDE4NTV8MjAyMC4wMC0wOCA1LTAxMDowMDAwOjAmJjEmJjIwMGUmMjBmYWxzMDI1LTAmJDI5IDAwMDEtMDAwJjI6MDA6JjIyLjgmMjgmZmFsMDAwMDgmMjhzZSYyMDEmMiR8MTAmMjI4JjExNDMmMSYkMTAwMCYzMDMwLjAxMDAyLjAwJDAuMDAmMiY2LjAwJCYxMjA1LTAxfDIwMjE1OjAtMDggJjIwMjA6MDAtMTAgNS0wMTA6MDAxMzowNS0wMXwyMDIxODo0LTAyIAAAAAAxOjQ3", "priceVersion": "SH-PRICEVERSION_MTA3MTAyXzExNDlfMV8xMTRfMjI4XzIwMF8zNzguMDBfMy4wXzE1Ni4wXzBfMF8wLjBfMC4wXzEyMC4wMF8zMC4wMF8wLjAwXzAuMDBfMTg1NTY2NQ==", "vendorVehicleCode": "1855665", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1855665, "pLev": 23028, "rLev": 23028, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 6.0, "amountStr": "¥6", "subAmount": 3.0, "subAmountStr": "日均¥3", "originalDailyPrice": 114, "detail": [{"code": "1001", "name": "租车费", "amount": 228, "amountDesc": "¥228"}, {"code": "11037", "name": "优惠券", "amount": 222, "amountDesc": "¥222"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 30.0, "amountStr": "¥30", "detail": [{"code": "1003", "name": "车行手续费", "amount": 30.0, "amountDesc": "¥30", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 156.0, "amountStr": "¥156", "subAmount": 378.0, "subAmountStr": "¥378", "currencyCode": "¥"}]}], "reactId": "1841493322", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD8256_0_107102_107102"]}, "minTPrice": 156.0, "minDPrice": 3.0, "modifySameVehicle": false, "minDOrinPrice": 114, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减222", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isEasy": true, "isCredit": true, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_1149_"}, {"vehicleCode": "1444", "sortNum": 69, "lowestPrice": 127.0, "highestPrice": 127.0, "maximumRating": 0.0, "maximumCommentCount": 0, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4374", "vendorCode": "58487", "pStoreCode": "114289", "rStoreCode": "114289", "vehicleCode": "0", "packageType": 1, "comPriceCode": "[c]NjgxfDE5MTN8MjAyMC4wMC0wOCA1LTAxMDowMDAwOjAmJjEmJjIzOGUmMjNmYWxzMDI1LTgmJDI5IDAwMDEtMDAwJjI6MDA6MzgmMjM4JjIwMCZmMi4wMCYyMzhhbHNlJHwxMCYyMzgmMjM4MDEmMiQxMDAmNDc2MjAuMDMmMSYuMDAkMCYyMCYyJjYxMDAyJjEyMDAuMDB8MjAyLjAwJC0wOCA1LTAxMDowMDE1OjA1LTAxJjIwMjEzOjAtMTAgfDIwMjA6MDAtMDIgNS0wMTE6NDgxODo0", "priceVersion": "SH-PRICEVERSION_MTE0Mjg5XzE0NDRfMV8yMzhfNDc2XzIzOF82MTYuMDBfMTI3LjBfMzk0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF8xOTEzNjgx", "vendorVehicleCode": "1913681", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1913681, "pLev": 43184, "rLev": 43184, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 254.0, "amountStr": "¥254", "subAmount": 127.0, "subAmountStr": "日均¥127", "originalDailyPrice": 238, "detail": [{"code": "1001", "name": "租车费", "amount": 476, "amountDesc": "¥476"}, {"code": "11037", "name": "优惠券", "amount": 222, "amountDesc": "¥222"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 394.0, "amountStr": "¥394", "subAmount": 616.0, "subAmountStr": "¥616", "currencyCode": "¥"}]}], "reactId": "1841493326", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4374_0_114289_114289"]}, "minTPrice": 394.0, "minDPrice": 127.0, "modifySameVehicle": false, "minDOrinPrice": 238, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减222", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isEasy": true, "isCredit": true, "pWay": "可选：店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_1444_"}], "recommendVehicleList": [{"brandEName": "宝马", "brandName": "宝马", "name": "宝马1系", "zhName": "宝马1系", "vehicleCode": "4039", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.5T-2.0T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6a1200000mgg5yAC2F.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": false, "vehiclesSetId": "81", "mediaTypes": [], "vehicleKey": "2_4039_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田亚洲龙", "zhName": "丰田亚洲龙", "vehicleCode": "5193", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "2.0L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6g1200000mggv9402F.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": [], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "oilType": 3, "fuelType": "汽油", "luggageNum": "可放2个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "81", "multimediaAlbums": [{"albumName": "官方相册", "note": "年款/颜色等以门店为准", "albumType": 1, "mediaGroup": [{"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "cover": "", "sortNum": 0}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}]}], "mediaTypes": [], "vehicleKey": "0_5193_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "snowTyre": {"type": 0, "typeDesc": "不支持"}, "reverseImage": {"type": 1, "typeDesc": "支持"}, "reverseSensor": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田亚洲龙", "zhName": "丰田亚洲龙", "vehicleCode": "5193", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "2.0L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6g1200000mggv9402F.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": [], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "81", "mediaTypes": [], "vehicleKey": "2_5193_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "宝马", "brandName": "宝马", "name": "宝马1系", "zhName": "宝马1系", "vehicleCode": "4039", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.5T-2.0T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6a1200000mgg5yAC2F.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": false, "vehiclesSetId": "81", "mediaTypes": [], "vehicleKey": "0_4039_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "大众", "brandName": "大众", "name": "大众迈腾", "zhName": "大众迈腾", "vehicleCode": "1149", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.4T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6x1200000mj4ns5FF5.jpg?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV2712000cfuv0mgB512.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2612000cfuuxieF585.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2812000cfuv5e21D14.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1j12000cfuuvhn4FE2.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5412000cfuuzpcDF30.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV171200000mj2xp187C.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": false, "vehiclesSetId": "81", "mediaTypes": [2], "vehicleKey": "0_1149_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田凯美瑞", "zhName": "丰田凯美瑞", "vehicleCode": "1444", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "2.0L", "struct": "三厢车", "fuel": "92号或95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV2r1200000mgf984CF0.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0AS2u120009rj6ayhA1B3.png", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": false, "vehiclesSetId": "81", "mediaTypes": [2], "vehicleKey": "0_1444_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}}], "productGroupCodeUesd": "3", "priceExplain": {"title": "价格说明", "content": ["划线价格为参考价，该价格指商品或服务的门市价、服务提供商的指导价、零售价或该商品或服务曾经展示过的销售价等并非原价；由于产品信息实时更新、市场价格波动等可能会与您预订时展示的不一致，该价格仅供您参考。", "未划线价格指商品或服务的实时标价，为划线价基础上计算出的优惠金额。具体成交价格根据商品或服务参加活动，或会员使用优惠券等发生变化，最终以订单结算页价格为准。", "如有疑问，您可在预订前联系客服进行咨询。", "此说明仅当出现价格比较时有效。若服务提供商单独对划线价格进行说明的，以服务提供商的表述为准。 "]}, "shareVehicleInfo": {"vehicleName": "丰田亚洲龙", "groupName": "丰田亚洲龙", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "displacement": "2.0L", "style": "", "currentOriginalDailyPrice": 1000, "vehicleImage": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "license": ""}}