{"promotMap": {}, "extras": {"packageLevelAB": "B", "abVersion": "241008_DSJT_ykjpx|B,230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B", "isLicensePlateHideShow": "0", "serverRequestId": "LjcFJ11t4W9a5Y0LbC8N", "packageLevelSwitch": "1", "rSelect": "1", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "0"}, "resBodySize": 107729, "baseResponse": {"code": "200", "requestId": "61b2ce2b-7965-463d-8a42-ade198953b83", "cost": 152, "isSuccess": true, "returnMsg": "OK"}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "productGroupCodeUesd": "2", "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "recommendVehicleList": [{"skylight": "部分车辆支持", "vehicleKey": "0_5407_", "luggageNo": 2, "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "displacement": "1.2T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "丰田卡罗拉（23款及以前）", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "carPhone": true, "vehicleCode": "5407", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田卡罗拉（23款及以前）", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0e12000c5s92ry5659.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000c5s9frq4F26.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0k12000c5s94goBD02.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1p12000c5s9fry9DF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6o12000c5s97iu8E9C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "69", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "2_5407_", "luggageNo": 2, "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "displacement": "1.2T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "丰田卡罗拉（23款及以前）", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "carPhone": true, "vehicleCode": "5407", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田卡罗拉（23款及以前）", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0e12000c5s92ry5659.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000c5s9frq4F26.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0k12000c5s94goBD02.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1p12000c5s9fry9DF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6o12000c5s97iu8E9C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "69", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_4101_", "luggageNo": 2, "carPlay": "部分车辆支持CarPlay/CarLife", "displacement": "1.0T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4812000c55hlic84B7.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "别克英朗", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/AUX", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=200&app_ver=10.5", "vehicleCode": "4101", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "别克英朗", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6h12000d5p7p8r923E.png", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5u12000cea0kpu1B49.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5m12000cea0mjs6841.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4312000cea0kq6354D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5p12000cea0m0r7E89.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6c12000cea0iew2E5A.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "69", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_1437_", "luggageNo": 2, "carPlay": "部分车辆支持原厂互联/映射", "displacement": "1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV2f12000c55lfy6B13D.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "丰田致炫", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=6532&app_ver=10.5", "vehicleCode": "1437", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "丰田致炫", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2f12000c55lfy6B13D.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV4t12000cg0vg4yE8DA.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2112000cg0vgauF3DF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5u12000cg0vh7097BB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2x12000cg0vgaw83BF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6q12000cg0vh7373CA.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "两厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "69", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_658_", "luggageNo": 1, "carPlay": "部分车辆支持CarLife", "displacement": "1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4112000etfg9h2DF2F.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "本田飞度", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/HDMI", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3686&app_ver=10.5", "vehicleCode": "658", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "本田飞度", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV5712000bdpz7mlB131.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放1个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1p12000cfa48158FE0.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000cfa4ay5A013.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3912000cfa4cza1898.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5312000cfa4dilC852.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3q12000cfa443u3D27.jpg?mark=yiche"], "transmissionType": 1, "brandName": "本田", "oilType": 3, "struct": "两厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "本田", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "69", "guidSys": "部分车辆支持自适应巡航", "transmissionName": "自动挡"}], "filteredProductGroups": {"title": "", "vendorPriceList": [{"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 45510, "vendorDesc": "桐叶凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 45510}, "storeScore": 100, "uniqueCode": "0a21b18ea4e0455496eee01af71f311a", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 98, "naked": true, "deductInfos": [{"totalAmount": 48, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 0, "currentTotalPrice": 50, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 0, "vendorName": "桐叶租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2888, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "一年内车龄", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3510"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"category": 2, "sortNum": 47, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "延误时免费留车", "colorCode": "2", "prefix": "租车中心", "type": 1, "groupId": 3, "labelCode": "3779"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减48", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "11037", "amount": 45, "amountDesc": "¥45", "name": "优惠券"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 48, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 50, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥50"}], "vendorLogo": "https://dimg04.c-ctrip.com/images/20p4212000fiowgprC5C9.jpg", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "免费站内取还车", "ctripVehicleCode": "3159", "newCar": true, "secretBox": false, "sortScore": 0, "cyVendorName": "桐叶租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "stock": 99999, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "19434", "pickWayInfo": 16, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费站内取还车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 98, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "mergeInfo": [{"storeId": "78", "vehicleId": "19434"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 0}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD14450", "elct": 0, "pLevel": 2761, "gsDesc": "站内取还", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 1, "rStoreNav": "免费站内取还车", "priceVersion": "SH-PRICEVERSION_NzhfMzE1OV8xXzQ4LjBfNDguMF8wLjBfOTguMF8wXzUwLjBfMF8wXzAuMF8wLjBfMzAuMF8yMC4wXzBfMF8zNTU0OF8zNTEwfDM1NjN8Mzc0NnwzNzg4fDQyMjl8Mzc3OV8xMDAxOjQ4LjB8MTAwMzoyMC4wfDEwMDI6MzAuMA==", "alipay": false, "vendorCode": "13031", "productCode": "SD14450_0_78_78", "pLev": 129, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 2, "packageType": 1, "kPSId": 78, "age": 30, "rCoup": 0, "kRSId": 78, "packageId": "Secure", "freeIllegalDeposit": false, "rLev": 129, "pStoreCode": "78", "pickUpOnDoor": true, "aType": 0, "kVId": 13031, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "kVehicleId": 3159, "comPriceCode": "eyJzZWxsZXJpZCI6MTEwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6Mjc2MSwiYWN0Z2V0aWQiOjI3NjEsImFjdG9mZmlkIjoyNzYxLCJjYXJ0eXBlaWQiOjE5NDM0LCJ0b3RhbCI6OTgsInRpbWUiOjE3Mjk4NDQxMTZ9", "dropOffOnDoor": true, "returnWayInfo": 16, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠+券", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "共减48", "groupId": 1}, {"category": 3, "sortNum": 10000, "amountTitle": "共减48", "groupCode": "MarketGroup1317", "code": "30", "title": "双11特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 2, "klb": 1, "promtId": 605988495, "rStoreCode": "78", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 35548, "rLevel": 2761, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": true, "isOrderVehicle": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 6948, "vendorDesc": "漫自由三亚店", "level": "超棒", "maximumRating": 5, "commentLabel": "服务周到", "qExposed": "5.0", "hasComment": 1, "commentCount": 6948}, "storeScore": 79.72, "uniqueCode": "eb559623dbdb4fcfbb229e3c5541abe4", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 98, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 45, "currentTotalPrice": 95, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 1, "vendorName": "漫自由租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8388608, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 11, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2288, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.3325, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "一年内车龄", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3510"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "originalDailyPrice": 48, "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 95, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥95"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "3159", "newCar": true, "secretBox": false, "sortScore": 1, "cyVendorName": "漫自由租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "6783_55105_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 98, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 45, "mergeInfo": [{"storeId": "106965", "vehicleId": "6783_55105_pupai"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 45}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD3933", "elct": 0, "pLevel": 88368, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 1715, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTA2OTY1XzMxNTlfMV80OF80OF80OF85OC4wMF80NS4wXzk1LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzI4MDk3OTgwXzM1MTB8MzgxMHwzNTA0fDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTo0OHwxMDAzOjIwLjAwfDEwMDI6MzAuMDA=", "alipay": false, "vendorCode": "46492", "productCode": "SD3933_0_106965_106965", "pLev": 88368, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 106965, "age": 30, "rCoup": 0, "kRSId": 106965, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 88368, "pStoreCode": "106965", "pickUpOnDoor": true, "aType": 0, "kVId": 46492, "sortInfo": {"p": "1", "s": "33.61", "c": "43"}, "kVehicleId": 3159, "comPriceCode": "[c]Nzk4MDI4MDkwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCY0OGUmNDhmYWxzMDAxJiYkfDEmNDgkMSY0OCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTk6NSAxNgAAAAAxMwAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "106965", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 28097980, "rLevel": 88368, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 112, "vendorDesc": "墨源租车", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 112}, "storeScore": 67.27, "uniqueCode": "5ac21c4540894adda149e130def465c7", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 98, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 45, "currentTotalPrice": 95, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 2, "vendorName": "墨源租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_5", "binaryDigit": 8388608, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 176, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.9563, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "两年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3547"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "originalDailyPrice": 48, "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 95, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥95"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "3159", "newCar": false, "secretBox": false, "sortScore": 2, "cyVendorName": "墨源租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "51447012", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 98, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 45, "mergeInfo": [{"storeId": "421268", "vehicleId": "51447012"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 45}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD12256", "elct": 0, "pLevel": 1107541, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 61286, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_NDIxMjY4XzMxNTlfMV80OF80OF80OF85OC4wMF80NS4wXzk1LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzUxNDQ3MDEyXzM1NDd8MzgxMHwzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6NDh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "15005072", "productCode": "SD12256_0_421268_421268", "pLev": 1107541, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 421268, "age": 30, "rCoup": 0, "kRSId": 421268, "packageId": "", "freeIllegalDeposit": false, "rLev": 1107541, "pStoreCode": "421268", "pickUpOnDoor": true, "aType": 0, "kVId": 15005072, "sortInfo": {"p": "1", "s": "33.61", "c": "43", "v": "421268"}, "kVehicleId": 3159, "comPriceCode": "[c]NzAxMjUxNDQwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCY0OGUmNDhmYWxzMDAxJiYkfDEmNDgkMSY0OCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTk6NSAxNgAAAAAxMwAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "421268", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 51447012, "rLevel": 1107541, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 175, "vendorDesc": "京海租车凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "上门送取准时", "qExposed": "5.0", "hasComment": 1, "commentCount": 175}, "storeScore": 93.75, "uniqueCode": "5412297537d44555a0e1855c415dbe44", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 198, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 45, "currentTotalPrice": 195, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 3, "vendorName": "京海租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 256, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 7, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2288, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 1.3851, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 8, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "新款", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3789"}, {"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "半年内车龄", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3509"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "originalDailyPrice": 48, "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11006", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "夜间取车费"}, {"code": "11007", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "夜间还车费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 195, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥198", "subAmount": 198, "name": "总价", "amountStr": "¥195"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "3159", "newCar": true, "secretBox": false, "sortScore": 3, "cyVendorName": "京海租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "87964_85730_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 198, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 45, "mergeInfo": [{"storeId": "117944", "vehicleId": "87964_85730_pupai"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 45}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD5538", "elct": 0, "pLevel": 90305, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 257496, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTE3OTQ0XzMxNTlfMV80OF80OF80OF8xOTguMDBfNDUuMF8xOTUuMF8wXzBfMC4wXzAuMF8zMC4wMF8yMC4wMF8wLjAwXzAuMDBfNTY2OTU1OThfMzc4OXwzNTA5fDM4MTB8MzUwNHwzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6NDh8MTAwMzoyMC4wMHwxMTAwNjo1MC4wMHwxMTAwNzo1MC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "81827", "productCode": "SD5538_0_117944_117944", "pLev": 90305, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 117944, "age": 30, "rCoup": 0, "kRSId": 117944, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 90305, "pStoreCode": "117944", "pickUpOnDoor": true, "aType": 0, "kVId": 81827, "sortInfo": {"p": "1", "s": "9.9", "c": "43"}, "kVehicleId": 3159, "comPriceCode": "[c]NTU5ODU2NjkwfDIwfDAuMDAtMzAyNC0xMDA6MCAwMDomJjEmMCY0OGUmNDhmYWxzMDAxJiYkfDEmNDgkMSY0OCYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTk6NSAxNgAAAAAxMwAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "117944", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 56695598, "rLevel": 90305, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 5948, "vendorDesc": "明昊凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "服务周到", "qExposed": "4.9", "hasComment": 1, "commentCount": 5948}, "storeScore": 91.11, "uniqueCode": "dd67838489344fc5b3a8ee83f9a2ac66", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 98, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 45, "currentTotalPrice": 95, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 4, "vendorName": "明昊租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 64, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2888, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "一年内车龄", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3510"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "originalDailyPrice": 48, "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 95, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥95"}], "vendorLogo": "https://ak-d.tripcdn.com/images/0yc2l12000bzfo23oCC4C.png", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "3159", "newCar": true, "secretBox": false, "sortScore": 4, "cyVendorName": "明昊租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "stock": 99999, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "19434", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 98, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 45, "mergeInfo": [{"storeId": "2961", "vehicleId": "19434"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 45}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD3012", "elct": 0, "pLevel": 8536, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 6, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_Mjk2MV8zMTU5XzFfNDguMF80OC4wXzAuMF85OC4wXzQ1LjBfOTUuMF8wXzBfMC4wXzAuMF8zMC4wXzIwLjBfMF8wXzIyNjM5Nl8zNTEwfDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTo0OC4wfDEwMDM6MjAuMHwxMDAyOjMwLjA=", "alipay": false, "vendorCode": "13032", "productCode": "SD3012_0_2961_2961", "pLev": 918184, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 2961, "age": 30, "rCoup": 0, "kRSId": 2961, "packageId": "Secure", "freeIllegalDeposit": false, "rLev": 918184, "pStoreCode": "2961", "pickUpOnDoor": true, "aType": 0, "kVId": 13032, "sortInfo": {"p": "1", "s": "33.61", "c": "43"}, "kVehicleId": 3159, "comPriceCode": "eyJzZWxsZXJpZCI6MTIwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6ODUzNiwiYWN0Z2V0aWQiOjg1MzYsImFjdG9mZmlkIjo4NTM2LCJjYXJ0eXBlaWQiOjE5NDM0LCJ0b3RhbCI6OTgsInRpbWUiOjE3Mjk4NDQxMTZ9", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "2961", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 226396, "rLevel": 8536, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "isOrderVehicle": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 59, "vendorDesc": "腾新出行", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 59}, "storeScore": 89.88, "uniqueCode": "a4259fdc48474c058cfd8a94df2a45cf", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 399, "oTPrice": 449, "naked": true, "deductInfos": [{"totalAmount": 20, "payofftype": 2}], "currentOriginalDailyPrice": 399, "priceType": 1, "currentDailyPrice": 379, "currentTotalPrice": 429, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 5, "vendorName": "腾新出行", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_6", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2521, "actId": "3783", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "一年内车龄", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3510"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减20", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "licenseTag": "", "fees": [{"amount": 379, "detail": [{"code": "1001", "amount": 399, "amountDesc": "¥399", "name": "租车费"}, {"code": "3783", "amount": 20, "amountDesc": "¥20", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥379", "originalDailyPrice": 399, "subAmount": 379, "name": "车辆租金", "amountStr": "¥379"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 429, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥449", "subAmount": 449, "name": "总价", "amountStr": "¥429"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "3159", "newCar": true, "secretBox": false, "sortScore": 5, "cyVendorName": "腾新出行", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "88347_67660_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 449, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 379, "mergeInfo": [{"storeId": "834184", "vehicleId": "88347_67660_pupai"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 379}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD13636", "elct": 0, "pLevel": 1434224, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 257069, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_ODM0MTg0XzMxNTlfMV8zOTlfMzk5XzM5OV80NDkuMDBfMzc5LjBfNDI5LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzU3MDAyMDcyXzM1MTB8MzgxMHwzNjc5fDM3NDZfMTAwMTozOTl8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "15006407", "productCode": "SD13636_0_834184_834184", "pLev": 1434224, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 834184, "age": 30, "rCoup": 0, "kRSId": 834184, "freeIllegalDeposit": false, "rLev": 1434224, "pStoreCode": "834184", "pickUpOnDoor": true, "aType": 0, "kVId": 15006407, "sortInfo": {"p": "1", "s": "2.59", "c": "43"}, "kVehicleId": 3159, "comPriceCode": "[c]MjA3MjU3MDAyNC0xfHwyMCAwMDowLTMwMCYzOTAwOjAmZmFsOSYmMTk5JiRzZSYzMSYxJnwxMDAzOTkkMzk5JiYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTk6NSAxNgAAAAAxMwAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减20", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "834184", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 57002072, "rLevel": 1434224, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 111, "vendorDesc": "北蒙租车", "level": "超棒", "maximumRating": 5, "commentLabel": "服务周到", "qExposed": "5.0", "hasComment": 1, "commentCount": 111}, "storeScore": 99.7, "uniqueCode": "e8efdf91aa8e4b5aad450fa62eee6dcf", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 98, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 45, "currentTotalPrice": 95, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 6, "vendorName": "北蒙租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 128, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2544, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.7335, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "两年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3547"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "originalDailyPrice": 48, "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 95, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥95"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "3159", "newCar": false, "secretBox": false, "sortScore": 6, "cyVendorName": "北蒙租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "4634728", "pickWayInfo": 1, "stockLevel": "C", "pCityId": 43, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 98, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 45, "mergeInfo": [{"storeId": "175685", "vehicleId": "4634728"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 45}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD8142", "elct": 0, "pLevel": 116453, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 15320, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTc1Njg1XzMxNTlfMV80OF80OF80OF85OC4wMF80NS4wXzk1LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzQ2MzQ3MjhfMzU0N3wzODEwfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjQ4fDEwMDM6MjAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "15001186", "productCode": "SD8142_0_175685_175685", "pLev": 116453, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 175685, "age": 30, "rCoup": 0, "kRSId": 175685, "packageId": "", "freeIllegalDeposit": false, "rLev": 116453, "pStoreCode": "175685", "pickUpOnDoor": true, "aType": 0, "kVId": 15001186, "sortInfo": {"p": "1", "s": "33.61", "c": "43", "v": "175685"}, "kVehicleId": 3159, "comPriceCode": "[c]NzI4fDQ2MzR8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjQ4JiY0OCZhbHNlMDEmMSR8MTA0OCQxJjQ4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExOToxIDE2OgAAAAAzAAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "175685", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 4634728, "rLevel": 116453, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 872, "vendorDesc": "么么达租车", "level": "超棒", "maximumRating": 5, "commentLabel": "取还方便", "qExposed": "4.9", "hasComment": 1, "commentCount": 872}, "storeScore": 71.04, "uniqueCode": "deebf22d7317428fa70a1ac5ed86d475", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 98, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 48, "currentTotalPrice": 98, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 7, "vendorName": "么么达租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2097152, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 176, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.7736, "actId": "", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}], "licenseTag": "", "fees": [{"amount": 48, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥48", "subAmount": 48, "name": "车辆租金", "amountStr": "¥48"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 98, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥98", "subAmount": 98, "name": "总价", "amountStr": "¥98"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "3159", "newCar": false, "secretBox": false, "sortScore": 7, "cyVendorName": "么么达租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "3119746", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 98, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 48, "mergeInfo": [{"storeId": "107196", "vehicleId": "3119746"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 48}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3746", "elct": 0, "pLevel": 23111, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 2300, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTA3MTk2XzMxNTlfMV80OF80OF80OF85OC4wMF80OF85OC4wXzBfMF8wLjBfMC4wXzMwLjAwXzIwLjAwXzAuMDBfMC4wMF8zMTE5NzQ2XzM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjQ4fDEwMDM6MjAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "32498", "productCode": "SD3746_0_107196_107196", "pLev": 23111, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 107196, "age": 30, "rCoup": 0, "kRSId": 107196, "packageId": "", "freeIllegalDeposit": false, "rLev": 23111, "pStoreCode": "107196", "pickUpOnDoor": true, "aType": 0, "kVId": 32498, "sortInfo": {"p": "1", "s": "31.88", "c": "43", "v": "19331"}, "kVehicleId": 3159, "comPriceCode": "[c]NzQ2fDMxMTl8MjAyMC4wMC0zMCA0LTEwMDowMDAwOjAmMSZmJjQ4JiY0OCZhbHNlMDEmMSR8MTA0OCQxJjQ4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAwLTMwMjQtMTMwOjAgMTk6MjQtMTAmMjAgMTk6MC0zMTB8MjAzMDowMC0yNTI0LTExOToxIDE2OgAAAAAzAAAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "107196", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 3119746, "rLevel": 23111, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 18115, "vendorDesc": "凤凰机场店(站内取还)", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "4.9", "hasComment": 1, "commentCount": 18115}, "storeScore": 58.75, "uniqueCode": "c884057db44146539881390eb520a781", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 59, "oTPrice": 144, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 59, "priceType": 1, "currentDailyPrice": 56, "currentTotalPrice": 141, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 9, "vendorName": "一嗨租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2078, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 9, "sortNum": 10000, "mergeId": 0, "groupCode": "MarketGroup1335", "code": "20", "title": "全国连锁", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3757"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 56, "detail": [{"code": "1001", "amount": 59, "amountDesc": "¥59", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥56", "originalDailyPrice": 59, "subAmount": 56, "name": "车辆租金", "amountStr": "¥56"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 141, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥144", "subAmount": 144, "name": "总价", "amountStr": "¥141"}], "vendorLogo": "https://dimg04.c-ctrip.com/images/0yc4s12000cewdel8F3E3.jpg", "distance": 0.2078, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "免费站内取还车", "ctripVehicleCode": "3159", "newCar": false, "secretBox": false, "sortScore": 9, "cyVendorName": "一嗨租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "stock": 99999, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": -1, "vendorVehicleCode": "191", "pickWayInfo": 16, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费站内取还车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 144, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 56, "mergeInfo": [{"storeId": "138334", "vehicleId": "191"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 56}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD6991", "elct": 0, "pLevel": -1, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": -1, "priceType": 1, "isEasyLife": false, "klbPId": 13158, "rStoreNav": "免费站内取还车", "priceVersion": "SH-PRICEVERSION_MTM4MzM0XzMxNTlfMV81OS4wXzU5LjBfMC4wXzE0NC4wXzU2LjBfMTQxLjBfMF8wXzAuMF8wLjBfNTAuMF8zNS4wXzBfMF8yMTMyMDU4MF8zNTYzfDM3NDZ8Mzc1N18xMDAxOjU5LjB8MTAwMzozNS4wfDEwMDI6NTAuMA==", "alipay": false, "vendorCode": "13088", "productCode": "SD6991_0_138334_138334", "pLev": -1, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 138334, "age": 30, "rCoup": 0, "kRSId": 138334, "freeIllegalDeposit": false, "rLev": -1, "pStoreCode": "138334", "pickUpOnDoor": false, "aType": 0, "kVId": 13088, "sortInfo": {"p": "16", "s": "17.18", "c": "43"}, "kVehicleId": 3159, "returnWayInfo": 16, "dropOffOnDoor": false, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "138334", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 21320580, "rLevel": -1, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "isOrderVehicle": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.7", "qCommentCount": 41, "vendorDesc": "山与海", "level": "很好", "maximumRating": 5, "commentLabel": "取还方便", "qExposed": "4.7", "hasComment": 1, "commentCount": 41}, "storeScore": 49.3, "uniqueCode": "b9572ff83a5e410992bcdc6197d85a7b", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 56, "oTPrice": 306, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 56, "priceType": 1, "currentDailyPrice": 53, "currentTotalPrice": 303, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 10, "vendorName": "山与海出行", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_5", "binaryDigit": 65536, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 48, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 1.5858, "actId": "3783", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "两年内车龄", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3547"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减3", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "licenseTag": "", "fees": [{"amount": 53, "detail": [{"code": "1001", "amount": 56, "amountDesc": "¥56", "name": "租车费"}, {"code": "3783", "amount": 3, "amountDesc": "¥3", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥53", "originalDailyPrice": 56, "subAmount": 53, "name": "车辆租金", "amountStr": "¥53"}, {"code": "CAR_SERVICE_FEE", "amount": 220, "amountStr": "¥220", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11006", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "夜间取车费"}, {"code": "11007", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "夜间还车费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 303, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥306", "subAmount": 306, "name": "总价", "amountStr": "¥303"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "3159", "newCar": false, "secretBox": false, "sortScore": 10, "cyVendorName": "山与海出行", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "24985423", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 306, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 53, "mergeInfo": [{"storeId": "326432", "vehicleId": "24985423"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 53}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD11828", "elct": 0, "pLevel": 799688, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 49967, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MzI2NDMyXzMxNTlfMV81Nl81Nl81Nl8zMDYuMDBfNTMuMF8zMDMuMF8wXzBfMC4wXzAuMF8zMC4wMF8yMC4wMF8wLjAwXzAuMDBfMjQ5ODU0MjNfMzU0N3wzODEwfDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTo1NnwxMDAzOjIwLjAwfDExMDA2OjEwMC4wMHwxMTAwNzoxMDAuMDB8MTAwMjozMC4wMA==", "alipay": false, "vendorCode": "15004686", "productCode": "SD11828_0_326432_326432", "pLev": 799688, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 326432, "age": 30, "rCoup": 0, "kRSId": 326432, "packageId": "", "freeIllegalDeposit": false, "rLev": 799688, "pStoreCode": "326432", "pickUpOnDoor": true, "aType": 0, "kVId": 15004686, "sortInfo": {"p": "1", "s": "4.68", "c": "43", "v": "326432"}, "kVehicleId": 3159, "comPriceCode": "[c]NTQyMzI0OTg1fDIwfDAuMTAtMzAyNC0xMDA6MCAwMDomJjEmMCY1NmUmNTZmYWxzMDAxJiYkfDEmNTYkMSY1NiYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjMwLjAxMC0zMDI0LTozMDowIDE5MDI0LTAwJjIxIDE5MTAtMzAwfDI6MzA6MTAtMjAyNC06MTk6NSAxNgAAAAAxMwAA", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减3", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "326432", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 24985423, "rLevel": 799688, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 115, "vendorDesc": "机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆干净整洁", "qExposed": "5.0", "hasComment": 1, "commentCount": 115}, "storeScore": 63.24, "uniqueCode": "024e458281a7443f8549cf2280da120a", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 108, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 45, "currentTotalPrice": 105, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 11, "vendorName": "麒麟恒泰租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 536870912, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 3.2911, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "originalDailyPrice": 48, "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 105, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥108", "subAmount": 108, "name": "总价", "amountStr": "¥105"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "3159", "newCar": false, "secretBox": false, "sortScore": 11, "cyVendorName": "麒麟恒泰租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "92952_28856_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 108, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 45, "mergeInfo": [{"storeId": "183156", "vehicleId": "92952_28856_pupai"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 45}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD8606", "elct": 0, "pLevel": 133456, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 18273, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MTgzMTU2XzMxNTlfMV80OF80OF80OF8xMDguMDBfNDUuMF8xMDUuMF8wXzBfMC4wXzAuMF80MC4wMF8yMC4wMF8wLjAwXzAuMDBfNjI1MzEzNDRfMzY3OXwzNzQ2XzEwMDE6NDh8MTAwMzoyMC4wMHwxMDAyOjQwLjAw", "alipay": false, "vendorCode": "15001635", "productCode": "SD8606_0_183156_183156", "pLev": 133456, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 183156, "age": 30, "rCoup": 0, "kRSId": 183156, "freeIllegalDeposit": false, "rLev": 133456, "pStoreCode": "183156", "pickUpOnDoor": true, "aType": 0, "kVId": 15001635, "sortInfo": {"p": "1", "s": "28.35", "c": "43"}, "kVehicleId": 3159, "comPriceCode": "[c]MTM0NDYyNTMyNC0xfHwyMCAwMDowLTMwMCY0ODAwOjBmYWxzJiYxJiYkfDFlJjQ4MSY0ODAwMSYxMDAzJjQ4JDAuMDAmMSYyMDAkMSYyMC4xJjQwMDAyJjQwLjAuMDAmMDI0LTAkfDIwIDE5MTAtMzAwJjI6MzA6MTAtMzAyNC06MzA6MSAxOTAyNC0wMHwyNSAxNjEwLTIxMwAAOjE5Og==", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "183156", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 62531344, "rLevel": 133456, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 59, "vendorDesc": "仙雅租车", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 59}, "storeScore": 99.2, "uniqueCode": "3955e6f4c1b5472d8720ffbd615ffc97", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 88, "oTPrice": 138, "naked": true, "deductInfos": [{"totalAmount": 5, "payofftype": 2}], "currentOriginalDailyPrice": 88, "priceType": 1, "currentDailyPrice": 83, "currentTotalPrice": 133, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 12, "vendorName": "仙雅租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_5", "binaryDigit": 16777216, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.8554, "actId": "3783", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减5", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "licenseTag": "", "fees": [{"amount": 83, "detail": [{"code": "1001", "amount": 88, "amountDesc": "¥88", "name": "租车费"}, {"code": "3783", "amount": 5, "amountDesc": "¥5", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥83", "originalDailyPrice": 88, "subAmount": 83, "name": "车辆租金", "amountStr": "¥83"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 133, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥138", "subAmount": 138, "name": "总价", "amountStr": "¥133"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "3159", "newCar": false, "secretBox": false, "sortScore": 12, "cyVendorName": "仙雅租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "55019805", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 138, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 83, "mergeInfo": [{"storeId": "421835", "vehicleId": "55019805"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 83}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD12290", "elct": 0, "pLevel": 1152180, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 62014, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_NDIxODM1XzMxNTlfMV84OF84OF84OF8xMzguMDBfODMuMF8xMzMuMF8wXzBfMC4wXzAuMF8zMC4wMF8yMC4wMF8wLjAwXzAuMDBfNTUwMTk4MDVfMzY3OXwzNzQ2XzEwMDE6ODh8MTAwMzoyMC4wMHwxMDAyOjMwLjAw", "alipay": false, "vendorCode": "15005104", "productCode": "SD12290_0_421835_421835", "pLev": 1152180, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 421835, "age": 30, "rCoup": 0, "kRSId": 421835, "freeIllegalDeposit": false, "rLev": 1152180, "pStoreCode": "421835", "pickUpOnDoor": true, "aType": 0, "kVId": 15005104, "sortInfo": {"p": "1", "s": "18.97", "c": "43", "v": "421835"}, "kVehicleId": 3159, "comPriceCode": "[c]OTgwNTU1MDEyNC0xfHwyMCAwMDowLTMwMCY4ODAwOjBmYWxzJiYxJiYkfDFlJjg4MSY4ODAwMSYxMDAzJjg4JDAuMDAmMSYyMDAkMSYyMC4xJjMwMDAyJjMwLjAuMDAmMDI0LTAkfDIwIDE5MTAtMzAwJjI6MzA6MTAtMzAyNC06MzA6MSAxOTAyNC0wMHwyNSAxNjEwLTIxMwAAOjE5Og==", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减5", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "421835", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 55019805, "rLevel": 1152180, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "addProducts": [], "platformCode": "10"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 5, "vendorDesc": "耀东方凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆干净整洁", "qExposed": "5.0", "hasComment": 1, "commentCount": 5}, "storeScore": 67.23, "uniqueCode": "7b20cd06b5704e518b4316fe2546861b", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 48, "oTPrice": 118, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 48, "priceType": 1, "currentDailyPrice": 45, "currentTotalPrice": 115, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 13, "vendorName": "耀东方租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_5", "binaryDigit": 32, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 0.2553, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "originalDailyPrice": 48, "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 115, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥118", "subAmount": 118, "name": "总价", "amountStr": "¥115"}], "vendorLogo": "https://dimg04.c-ctrip.com/images/0yc2r12000cr7378c9AA0.jpg", "distance": 0.2553, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "自行前往门店取还车，距门店直线255米", "ctripVehicleCode": "3159", "newCar": false, "secretBox": false, "sortScore": 13, "cyVendorName": "耀东方租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "stock": 99999, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": -1, "vendorVehicleCode": "3554", "pickWayInfo": 0, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "自行前往门店取还车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 118, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 45, "mergeInfo": [{"storeId": "425573", "vehicleId": "3554"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 45}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD11466", "elct": 0, "pLevel": -1, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": -1, "priceType": 1, "isEasyLife": false, "klbPId": 64744, "rStoreNav": "自行前往门店取还车", "priceVersion": "SH-PRICEVERSION_NDI1NTczXzMxNTlfMV80OC4wXzQ4LjBfMC4wXzExOC4wXzQ1LjBfMTE1LjBfMF8wXzAuMF8wLjBfNTAuMF8yMC4wXzBfMF81NTgwODIzNl8zNTYzfDM3NDZfMTAwMTo0OC4wfDEwMDM6MjAuMHwxMDAyOjUwLjA=", "alipay": false, "vendorCode": "15004351", "productCode": "SD11466_0_425573_425573", "pLev": -1, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 425573, "age": 30, "rCoup": 0, "kRSId": 425573, "freeIllegalDeposit": false, "rLev": -1, "pStoreCode": "425573", "pickUpOnDoor": false, "aType": 0, "kVId": 15004351, "sortInfo": {"p": "0", "s": "24.29", "c": "43"}, "kVehicleId": 3159, "returnWayInfo": 0, "dropOffOnDoor": false, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "425573", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 55808236, "rLevel": -1, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "isOrderVehicle": false, "addProducts": [], "platformCode": "0"}]}, "vehicleInfo": {"skylight": "部分车辆支持", "vehicleKey": "0_3159_", "luggageNo": 3, "carPlay": "不支持", "displacement": "1.6L", "autoPark": false, "charge": "", "fuelType": "汽油", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6c12000eodv4th22F2.png?mark=yiche"], "license": "", "isSpecialized": true, "multimediaAlbums": [{"albumName": "官方相册", "albumType": 1, "mediaGroup": [{"groupType": 2, "groupName": "视频", "groupSortNum": 2, "medias": [{"sortNum": 0, "type": 2, "url": "https://video-preview.ctrip.com/videos/RV0vcn15370vcmahs37B6.mp4?auth=Nephele%20oo488fbixl16uoyqkxvsnnqhngqn9p89%2F202304131116%2Fcar_standard_product%2FdG9rZW46YmQyOTk4MWJmNzI2MTIzMDdkMmEzMzBlMmRlNjNmOGE4ZWFhY2MyMTg1N2NkODc0OWM1YzRlYmM%3D&mark=yiche", "cover": "https://dimg04.c-ctrip.com/images/0RV2l12000befacgb63B5.jpg"}]}, {"groupType": 3, "groupName": "VR", "groupSortNum": 3, "medias": [{"sortNum": 0, "type": 3, "url": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=912&app_ver=10.5", "cover": "https://dimg04.c-ctrip.com/images/0RV6c12000eodv4th22F2.png?mark=yiche"}]}, {"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4812000c5j6d58E9A9.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5512000c5j6jy9993C.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4c12000c5j6frk809C.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1212000c5j6gbd2D32.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2312000c5j6r199B97.jpg?mark=yiche", "cover": ""}]}, {"groupType": 5, "groupName": "前排", "groupSortNum": 5, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV7112000c5j6fs7D2EA.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1r12000c5j6lb87547.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0u12000c5j6fs96046.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2n12000c5j6lba5747.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0x12000c5j6qvwF2FB.jpg?mark=yiche", "cover": ""}]}, {"groupType": 6, "groupName": "后排", "groupSortNum": 6, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3m12000c5j6qwk4321.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2p12000c5j714mD617.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5812000c5j71cp6981.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5j12000c5j6gc61113.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6412000c5j71cr554C.jpg?mark=yiche", "cover": ""}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}], "note": "年款/颜色等以门店为准"}], "groupCode": "2", "zhName": "日产轩逸(23及以前款)", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=912&app_ver=10.5", "vehicleCode": "3159", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "日产轩逸(23及以前款)", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2l12000befacgb63B5.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放3个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV7112000c5j6fs7D2EA.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3m12000c5j6qwk4321.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1r12000c5j6lb87547.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2p12000c5j714mD617.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0u12000c5j6fs96046.jpg?mark=yiche"], "transmissionType": 1, "brandName": "日产", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "日产", "licenseStyle": "2", "vehiclesSetId": "69", "guidSys": "部分车辆支持全速自适应巡航", "transmissionName": "自动挡"}, "isFromSearch": false, "timeInterval": 206.**********, "specificProductGroups": {"title": "", "vendorPriceList": [{"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 18115, "vendorDesc": "凤凰机场店(站内取还)", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "4.9", "hasComment": 1, "commentCount": 18115}, "storeScore": 58.75, "uniqueCode": "693f7bfd61534f73929b43bc2483854c", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 59, "oTPrice": 144, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 59, "priceType": 1, "currentDailyPrice": 56, "currentTotalPrice": 141, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 8, "vendorName": "一嗨租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 1, "checkType": 0}], "rDistance": 0.2078, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 9, "sortNum": 10000, "mergeId": 0, "groupCode": "MarketGroup1335", "code": "20", "title": "全国连锁", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3757"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 56, "detail": [{"code": "1001", "amount": 59, "amountDesc": "¥59", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥56", "originalDailyPrice": 59, "subAmount": 56, "name": "车辆租金", "amountStr": "¥56"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 141, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥144", "subAmount": 144, "name": "总价", "amountStr": "¥141"}], "vendorLogo": "https://dimg04.c-ctrip.com/images/0yc4s12000cewdel8F3E3.jpg", "distance": 0.2078, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "免费站内取还车", "ctripVehicleCode": "3159", "newCar": false, "secretBox": false, "sortScore": 8, "cyVendorName": "一嗨租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "stock": 99999, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": -1, "vendorVehicleCode": "3554", "pickWayInfo": 16, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费站内取还车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 144, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 56, "mergeInfo": [{"storeId": "138334", "vehicleId": "3554"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 56}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD6991", "elct": 0, "pLevel": -1, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": -1, "priceType": 1, "isEasyLife": false, "klbPId": 13158, "rStoreNav": "免费站内取还车", "priceVersion": "SH-PRICEVERSION_MTM4MzM0XzMxNTlfMV81OS4wXzU5LjBfMC4wXzE0NC4wXzU2LjBfMTQxLjBfMF8wXzAuMF8wLjBfNTAuMF8zNS4wXzBfMF8xODMzMTQ3NV8zODY2fDM1NjN8Mzc0NnwzNzU3XzEwMDE6NTkuMHwxMDAzOjM1LjB8MTAwMjo1MC4w", "alipay": false, "vendorCode": "13088", "productCode": "SD6991_0_138334_138334", "pLev": -1, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 138334, "age": 30, "rCoup": 0, "kRSId": 138334, "freeIllegalDeposit": false, "rLev": -1, "pStoreCode": "138334", "pickUpOnDoor": false, "aType": 0, "kVId": 13088, "sortInfo": {"p": "16", "s": "17.18", "c": "43"}, "kVehicleId": 3159, "returnWayInfo": 16, "dropOffOnDoor": false, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "138334", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 18331475, "rLevel": -1, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "isOrderVehicle": false, "addProducts": [], "platformCode": "0"}, {"vehicleKey": "0_3159_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 18115, "vendorDesc": "凤凰机场店(站内取还)", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "4.9", "hasComment": 1, "commentCount": 18115}, "storeScore": 58.75, "uniqueCode": "693f7bfd61534f73929b43bc2483854c", "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 59, "oTPrice": 144, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 59, "priceType": 1, "currentDailyPrice": 56, "currentTotalPrice": 141, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 8, "vendorName": "一嗨租车", "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 128, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 1, "checkType": 0}], "rDistance": 0.2078, "actId": "3758", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 9, "sortNum": 10000, "mergeId": 0, "groupCode": "MarketGroup1335", "code": "20", "title": "全国连锁", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3757"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "licenseTag": "", "fees": [{"amount": 56, "detail": [{"code": "1001", "amount": 59, "amountDesc": "¥59", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥56", "originalDailyPrice": 59, "subAmount": 56, "name": "车辆租金", "amountStr": "¥56"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 141, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥144", "subAmount": 144, "name": "总价", "amountStr": "¥141"}], "vendorLogo": "https://dimg04.c-ctrip.com/images/0yc4s12000cewdel8F3E3.jpg", "distance": 0.2078, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "免费站内取还车", "ctripVehicleCode": "3159", "newCar": false, "secretBox": false, "sortScore": 8, "cyVendorName": "一嗨租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "stock": 99999, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": -1, "vendorVehicleCode": "3554", "pickWayInfo": 16, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "免费站内取还车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 144, "recommendOrder": 0, "mergeId": 735, "vdegree": "0", "rectype": 1, "totalDailyPrice": 56, "mergeInfo": [{"storeId": "138334", "vehicleId": "3554"}], "grantedcode": "", "isrec": false, "cvid": 3159, "rentalamount": 56}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD6991", "elct": 0, "pLevel": -1, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": -1, "priceType": 1, "isEasyLife": false, "klbPId": 13158, "rStoreNav": "免费站内取还车", "priceVersion": "SH-PRICEVERSION_MTM4MzM0XzMxNTlfMV81OS4wXzU5LjBfMC4wXzE0NC4wXzU2LjBfMTQxLjBfMF8wXzAuMF8wLjBfNTAuMF8zNS4wXzBfMF8xODMzMTQ3NV8zODY2fDM1NjN8Mzc0NnwzNzU3XzEwMDE6NTkuMHwxMDAzOjM1LjB8MTAwMjo1MC4w", "alipay": false, "vendorCode": "13088", "productCode": "SD6991_0_138334_138334", "pLev": -1, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 138334, "age": 30, "rCoup": 0, "kRSId": 138334, "freeIllegalDeposit": false, "rLev": -1, "pStoreCode": "138334", "pickUpOnDoor": false, "aType": 0, "kVId": 13088, "sortInfo": {"p": "16", "s": "17.18", "c": "43"}, "kVehicleId": 3159, "returnWayInfo": 16, "dropOffOnDoor": false, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "138334", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 18331475, "rLevel": -1, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "isOrderVehicle": false, "addProducts": [], "platformCode": "0"}]}, "ResponseStatus": {"Extension": [{"Value": "3738269419882751298", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a38da7a-480512-949451", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1729844490570+0800)/"}, "imStatus": 1, "recommendProducts": [{"groupSort": 0, "lowestPrice": 84, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 84, "detail": [{"code": "1001", "amount": 89, "amountDesc": "¥89", "name": "租车费"}, {"code": "3758", "amount": 5, "amountDesc": "¥5", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥84", "originalDailyPrice": 89, "subAmount": 84, "name": "车辆租金", "amountStr": "¥84"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 169, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥174", "subAmount": 174, "name": "总价", "amountStr": "¥169"}], "reference": {"vehicleCode": "0", "rStoreCode": "138334", "pLev": -1, "bizVendorCode": "SD6991", "pStoreCode": "138334", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTM4MzM0XzU0MDdfMV84OS4wXzg5LjBfMC4wXzE3NC4wXzg0LjBfMTY5LjBfMF8wXzAuMF8wLjBfNTAuMF8zNS4wXzBfMF8xNjYyMjY3Nw==", "sendTypeForPickUpCar": 0, "skuId": 16622677, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "13088", "vendorVehicleCode": "3966"}, "isMinTPriceVendor": true}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减5", "groupId": 1}, "vehicleCode": "5407", "highestPrice": 84, "rCoup": 0, "minDPrice": 84, "pWay": "可选：免费站内取还车", "vehicleKey": "0_5407_", "hot": 0, "minTPrice": 169, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 32, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3926_0_183936_183936"], "introduce": "当前车型最低价"}, "minDOrinPrice": 89, "isEasy": false, "isCredit": true, "maximumCommentCount": 51269, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 84, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 84, "detail": [{"code": "1001", "amount": 89, "amountDesc": "¥89", "name": "租车费"}, {"code": "3758", "amount": 5, "amountDesc": "¥5", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥84", "originalDailyPrice": 89, "subAmount": 84, "name": "车辆租金", "amountStr": "¥84"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 169, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥174", "subAmount": 174, "name": "总价", "amountStr": "¥169"}], "reference": {"vehicleCode": "0", "rStoreCode": "138334", "pLev": -1, "bizVendorCode": "SD6991", "pStoreCode": "138334", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTM4MzM0XzQxMDFfMV84OS4wXzg5LjBfMC4wXzE3NC4wXzg0LjBfMTY5LjBfMF8wXzAuMF8wLjBfNTAuMF8zNS4wXzBfMF8xNTUxODQ2OQ==", "sendTypeForPickUpCar": 0, "skuId": 15518469, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "13088", "vendorVehicleCode": "3923"}, "isMinTPriceVendor": true}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减5", "groupId": 1}, "vehicleCode": "4101", "highestPrice": 84, "rCoup": 0, "minDPrice": 84, "pWay": "可选：免费站内取还车", "vehicleKey": "0_4101_", "hot": 0, "minTPrice": 169, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 94, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD4701_0_114529_114529"], "introduce": "当前车型最低价"}, "minDOrinPrice": 89, "isEasy": false, "isCredit": true, "maximumCommentCount": 51269, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "16191496323", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 56, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 56, "detail": [{"code": "1001", "amount": 59, "amountDesc": "¥59", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥56", "originalDailyPrice": 59, "subAmount": 56, "name": "车辆租金", "amountStr": "¥56"}, {"code": "CAR_SERVICE_FEE", "amount": 25, "amountStr": "¥25", "detail": [{"code": "1003", "amount": 25, "amountDesc": "¥25", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 131, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥134", "subAmount": 134, "name": "总价", "amountStr": "¥131"}], "reference": {"vehicleCode": "0", "rStoreCode": "140547", "pLev": -1, "bizVendorCode": "SD6991", "pStoreCode": "140547", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTQwNTQ3XzE0MzdfMV81OS4wXzU5LjBfMC4wXzEzNC4wXzU2LjBfMTMxLjBfMF8wXzAuMF8wLjBfNTAuMF8yNS4wXzBfMF8xNTI1Njk5OA==", "sendTypeForPickUpCar": 0, "skuId": 15256998, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "13088", "vendorVehicleCode": "3924"}, "isMinTPriceVendor": true}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}, "vehicleCode": "1437", "highestPrice": 56, "rCoup": 0, "minDPrice": 56, "pWay": "", "vehicleKey": "0_1437_", "hot": 0, "minTPrice": 131, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 253, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11974_0_407345_407345"], "introduce": "当前车型最低价"}, "minDOrinPrice": 59, "isEasy": false, "isCredit": true, "maximumCommentCount": 957, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********2", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 45, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 48, "amountDesc": "¥48", "name": "租车费"}, {"code": "3758", "amount": 3, "amountDesc": "¥3", "name": "双11特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "originalDailyPrice": 48, "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 130, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥133", "subAmount": 133, "name": "总价", "amountStr": "¥130"}], "reference": {"vehicleCode": "0", "rStoreCode": "138334", "pLev": -1, "bizVendorCode": "SD6991", "pStoreCode": "138334", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTM4MzM0XzY1OF8xXzQ4LjBfNDguMF8wLjBfMTMzLjBfNDUuMF8xMzAuMF8wXzBfMC4wXzAuMF81MC4wXzM1LjBfMF8wXzE4NjEzNTgy", "sendTypeForPickUpCar": 0, "skuId": 18613582, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "13088", "vendorVehicleCode": "3573"}, "isMinTPriceVendor": true}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "双11特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1354", "labelCode": "3758", "amountTitle": "已减3", "groupId": 1}, "vehicleCode": "658", "highestPrice": 45, "rCoup": 0, "minDPrice": 45, "pWay": "可选：免费站内取还车", "vehicleKey": "0_658_", "hot": 0, "minTPrice": 130, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 257, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD13705_0_181671_181671"], "introduce": "当前车型最低价"}, "minDOrinPrice": 48, "isEasy": false, "isCredit": true, "maximumCommentCount": 18115, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0.2268414, "hotType": 0, "reactId": "**********6", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}], "checkResponseTime": 1729844490489.564, "checkRequestTime": 1729844490282.774, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 415, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 415, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1729844490280, "afterFetch": 1729844490695}}