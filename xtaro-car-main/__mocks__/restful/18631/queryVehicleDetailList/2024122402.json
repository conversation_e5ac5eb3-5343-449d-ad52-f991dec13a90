{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "ab822fab-b48b-4b51-be20-6c4064e69f86", "cost": 1359}, "ResponseStatus": {"Timestamp": "2024-12-23 20:45:25", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "8901809450752373903"}, {"Id": "RootMessageId", "Value": "921822-0a30871f-481932-2408945"}]}, "vehicleInfo": {"brandEName": "起亚", "brandName": "起亚", "name": "起亚 K2", "zhName": "起亚 K2", "vehicleCode": "3059", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.4L-1.6L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4u12000c7kmc96BC60.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0o12000cfag9geA72D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4q12000cfagcew2A19.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5112000cfag1881D75.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6h12000cfag95j74A7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1912000cfag3vrE24F.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4312000d5p3loaA404.png", "oilType": 3, "fuelType": "汽油", "luggageNum": "可放2个24寸行李箱", "guidSys": "不支持", "carPlay": "部分车辆支持CarPlay/CarLife", "chargeInterface": "部分车辆支持USB/AUX", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "autoBackUp": false, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=1629", "vehiclesSetId": "69", "multimediaAlbums": [{"albumName": "官方相册", "note": "年款/颜色等以门店为准", "albumType": 1, "mediaGroup": [{"groupType": 2, "groupName": "视频", "groupSortNum": 2, "medias": [{"type": 2, "url": "https://video.c-ctrip.com/videos/R40u27000001i5jk0590E.mp4?mark=yiche", "cover": "https://dimg04.c-ctrip.com/images/0RV4312000d5p3loaA404.png", "sortNum": 0}]}, {"groupType": 3, "groupName": "VR", "groupSortNum": 3, "medias": [{"type": 3, "url": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=1629", "cover": "https://dimg04.c-ctrip.com/images/0RV4u12000c7kmc96BC60.png?mark=yiche", "sortNum": 0}]}, {"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6g12000cfag6qgEF79.jpg?mark=yiche", "cover": "", "sortNum": 1}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2d12000cfag182C995.jpg?mark=yiche", "cover": "", "sortNum": 2}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3c12000cfag63m0B0E.jpg?mark=yiche", "cover": "", "sortNum": 3}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5g12000cfafzhsE2CB.jpg?mark=yiche", "cover": "", "sortNum": 4}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6z12000cfag51s15EA.jpg?mark=yiche", "cover": "", "sortNum": 5}]}, {"groupType": 5, "groupName": "前排", "groupSortNum": 5, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0o12000cfag9geA72D.jpg?mark=yiche", "cover": "", "sortNum": 1}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5112000cfag1881D75.jpg?mark=yiche", "cover": "", "sortNum": 2}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1912000cfag3vrE24F.jpg?mark=yiche", "cover": "", "sortNum": 3}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6u12000cfag4ig2ED7.jpg?mark=yiche", "cover": "", "sortNum": 4}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0812000cfag28k2A0B.jpg?mark=yiche", "cover": "", "sortNum": 5}]}, {"groupType": 6, "groupName": "后排", "groupSortNum": 6, "medias": [{"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4q12000cfagcew2A19.jpg?mark=yiche", "cover": "", "sortNum": 1}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6h12000cfag95j74A7.jpg?mark=yiche", "cover": "", "sortNum": 2}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0712000cfagaerC7B8.jpg?mark=yiche", "cover": "", "sortNum": 3}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5312000cfag9av1EAE.jpg?mark=yiche", "cover": "", "sortNum": 4}, {"type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1a12000cfag41n60DC.jpg?mark=yiche", "cover": "", "sortNum": 5}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}]}], "mediaTypes": [3, 2], "vehicleKey": "0_3059_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}}, "detailPageTags": [{"title": "送车上门", "lowestDailyPrice": 323.21, "specificProductGroups": {"vendorPriceList": []}, "filteredProductGroups": {"vendorPriceList": [{"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}]}}, {"title": "到店取车", "lowestDailyPrice": 123.21, "specificProductGroups": {"vendorPriceList": [{"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}]}, "filteredProductGroups": {"vendorPriceList": [{"vendorName": "小龙人租车", "isMinTPriceVendor": false, "commentInfo": {"level": "很好", "vendorDesc": "小龙人机场路店", "commentCount": 10, "qCommentCount": 10, "qExposed": "4.8", "overallRating": "4.8", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 288.0, "currentOriginalDailyPrice": 295, "curOriginDPrice": 295, "oTPrice": 1318.0, "currentTotalPrice": 1288.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 30.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD8888", "vendorCode": "15001908", "pStoreCode": "184116", "rStoreCode": "184116", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD8888_0_184116_184116", "comPriceCode": "[c]MzczMjQ0MzgwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDowJiYxMCYyMHNlJjImZmFsMjAyNTAwJiQwMiAwLTAxLTowMCYwOjAwJjEmZjIwMCYmMjAwYWxzZTI1LTAmJDIwIDAwOjEtMDMwJjM4MDA6MCZmYWw5JiYxODkmJHNlJjMtMDEtMjAyNTA6MDAwNCAwMzg5JjowMCZhbHNlJjEmZiYkfDEmMzg5NCYyOTAwMSY3OCQxNSYxMTEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTg0MTE2XzMwNTlfMV8yOTVfMTE3OF8yMDBfMTMxOC4wMF8yODguMF8xMjg4LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF80NDM4MzczMl8zODEwfDM1NjN8Mzc0NnwzNzg4fDQyMjl8Mzg3Ml8xMDAxOjExNzh8MTAwMzoyMC4wMHwxMDAyOjEyMC4wMA==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "10901_29437_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减30", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 1318.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 1148.0, "totalDailyPrice": 288.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "10901_29437_pupai", "storeId": "184116"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 44383732, "klbPId": 60229, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 1101731, "rLevel": 1101731, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "5.42", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 184116, "kRSId": 184116, "kVId": 15001908, "pLev": 1101731, "rLev": 1101731, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 3.0, "pStoreRouteDesc": "门店取车，距门店直线2.0公里", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 128, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2096, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 3.0, "storeScore": 65.52, "isSelect": false, "distance": 0.0, "rDistance": 2.7023, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "小龙人租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1148.0, "amountStr": "¥1148", "subAmount": 288.0, "subAmountStr": "日均¥288", "originalDailyPrice": 295, "detail": [{"code": "1001", "name": "租车费", "amount": 1178, "amountDesc": "¥1178"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1288.0, "amountStr": "¥1288", "subAmount": 1318.0, "subAmountStr": "¥1318", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "7c5b94688f3242bfa4c6b1a4ab785bd3", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}]}}], "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "uniqSign": "32001137690359151993c4L5I9v606ipdfRCCM4f", "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "isFromSearch": true, "promotMap": {}, "extras": {"packageLevelAB": "B", "selfServiceSwitch": "1", "rSelect": "1", "isLicensePlateHideShow": "0", "packageLevelSwitch": "1", "commodityClass2Version": "1", "abVersion": "241008_DSJT_ykjpx|B,230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,220624_DSJT_spfj1|B", "isNewLicensePlate": "0", "serverRequestId": "Om17wT1R3xBN57EtwVQ8", "prepProductGroupTopSwitch": "1"}, "imStatus": 1, "recommendProducts": [{"vehicleCode": "4139", "sortNum": 0, "lowestPrice": 59.0, "highestPrice": 215.0, "maximumRating": 5.0, "maximumCommentCount": 51841, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4382", "vendorCode": "61659", "pStoreCode": "114307", "rStoreCode": "114307", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "comPriceCode": "[c]MDEzNDczNzgwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDomJjEmMCY5OWUmOTlmYWxzMjUtMCYkMjAgMDA6MS0wMjAmNDgwMDowZmFscyYmMSYmJDIwZSY0ODEtMDMyNS0wMDA6MCAwMDomJjEmMCY0OGUmNDhmYWxzMjUtMCYkMjAgMDA6MS0wNDAmNDgwMDowZmFscyYmMSYmJHwxZSY0ODQmNjEwMDEmJDEwMCYyNDMyMC4wMyYxJi4wMCQwJjIwJjQmMzEwMDImMTIwMC4wMHwyMDIuMDAkLTAxIDUtMDEwOjAwMTA6MDUtMDEmMjAyMTA6MC0wNSB8MjAyMDowMC0yMyA0LTEyNToyNDIwOjQ=", "priceVersion": "SH-PRICEVERSION_MTE0MzA3XzQxMzlfMV82MV8yNDNfOTlfMzgzLjAwXzU5LjBfMzczLjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF83Mzc4MDEzNA==", "vendorVehicleCode": "100479_8888_pupai", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 73780134, "pLev": 43275, "rLev": 43275, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 233.0, "amountStr": "¥233", "subAmount": 59.0, "subAmountStr": "日均¥59", "originalDailyPrice": 61, "detail": [{"code": "1001", "name": "租车费", "amount": 243, "amountDesc": "¥243"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 373.0, "amountStr": "¥373", "subAmount": 383.0, "subAmountStr": "¥383", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD15262", "vehicleCode": "0", "packageType": 0, "skuId": 76069855}}, {"reference": {"bizVendorCode": "SD7763", "vehicleCode": "0", "packageType": 1, "skuId": 787013}}, {"reference": {"bizVendorCode": "SD7763", "vehicleCode": "0", "packageType": 1, "skuId": 72913632}}, {"reference": {"bizVendorCode": "SD4701", "vehicleCode": "0", "packageType": 1, "skuId": 40565344}}, {"reference": {"bizVendorCode": "SD3933", "vehicleCode": "0", "packageType": 1, "skuId": 28097714}}, {"reference": {"bizVendorCode": "SD6573", "vehicleCode": "0", "packageType": 1, "skuId": 1965861}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 6900280}}, {"reference": {"bizVendorCode": "SD3012", "vehicleCode": "0", "packageType": 1, "skuId": 227977}}, {"reference": {"bizVendorCode": "SD14450", "vehicleCode": "0", "packageType": 1, "skuId": 152539}}, {"reference": {"bizVendorCode": "SD12615", "vehicleCode": "0", "packageType": 0, "skuId": 46010546}}, {"reference": {"bizVendorCode": "SD3942", "vehicleCode": "0", "packageType": 1, "skuId": 1860920}}, {"reference": {"bizVendorCode": "SD4445", "vehicleCode": "0", "packageType": 0, "skuId": 56981863}}, {"reference": {"bizVendorCode": "SD11239", "vehicleCode": "0", "packageType": 1, "skuId": 44611645}}, {"reference": {"bizVendorCode": "SD4849", "vehicleCode": "0", "packageType": 1, "skuId": 1913476}}, {"reference": {"bizVendorCode": "SD8506", "vehicleCode": "0", "packageType": 1, "skuId": 69928139}}, {"reference": {"bizVendorCode": "SD8119", "vehicleCode": "0", "packageType": 1, "skuId": 54499593}}, {"reference": {"bizVendorCode": "SD5523", "vehicleCode": "0", "packageType": 1, "skuId": 61517758}}, {"reference": {"bizVendorCode": "SD3926", "vehicleCode": "0", "packageType": 1, "skuId": 6901044}}], "reactId": "2045256160", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4382_0_114307_114307"]}, "minTPrice": 373.0, "minDPrice": 59.0, "modifySameVehicle": false, "minDOrinPrice": 61, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}, "priceSize": 19, "isEasy": true, "isCredit": true, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_4139_"}, {"vehicleCode": "5407", "sortNum": 11, "lowestPrice": 73.0, "highestPrice": 551.0, "maximumRating": 5.0, "maximumCommentCount": 51841, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD7678", "vendorCode": "15000935", "pStoreCode": "265301", "rStoreCode": "265301", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]NTgyMDQ0ODMwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDomJjEmMCY4OCY4NyZ0cnVlNS0wMSQyMDIwMDowLTAyICY4NiYwOjAwYWxzZSYxJmYkMjAyJjg2Ji0wMyA1LTAxMDowMDAwOjAmMSZmJjg2JiY4NiZhbHNlNS0wMSQyMDIwMDowLTA0ICY4NiYwOjAwYWxzZSYxJmYkfDEwJjg2JiY4NyYwMSY0MTAwMzM0NiQwLjAwJjEmMjAwJDEmMjAuNCYzMDAwMiYxMjAuLjAwJjIwMjUwMCR8MDEgMS0wMS06MDAmMDowMC0wMS0yMDI1MDowMDA1IDEyMDI0OjAwfDIzIDItMTItOjI0ADA6NDU=", "priceVersion": "SH-PRICEVERSION_MjY1MzAxXzU0MDdfMV84N18zNDZfODhfNDg2LjAwXzczLjBfNDI4LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF80NDgzNTgyMA==", "vendorVehicleCode": "44835820", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 44835820, "pLev": 1217371, "rLev": 1217371, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 288.0, "amountStr": "¥288", "subAmount": 73.0, "subAmountStr": "日均¥73", "originalDailyPrice": 87, "detail": [{"code": "1001", "name": "租车费", "amount": 346, "amountDesc": "¥346"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 28, "amountDesc": "¥28"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 428.0, "amountStr": "¥428", "subAmount": 486.0, "subAmountStr": "¥486", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3933", "vehicleCode": "0", "packageType": 1, "skuId": 28097938}}, {"reference": {"bizVendorCode": "SD3127", "vehicleCode": "0", "packageType": 1, "skuId": 24662247}}, {"reference": {"bizVendorCode": "SD7763", "vehicleCode": "0", "packageType": 1, "skuId": 76160981}}, {"reference": {"bizVendorCode": "SD5613", "vehicleCode": "0", "packageType": 1, "skuId": 68647265}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 0, "skuId": 59118228}}, {"reference": {"bizVendorCode": "SD8841", "vehicleCode": "0", "packageType": 1, "skuId": 44658461}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 6900273}}, {"reference": {"bizVendorCode": "SD12615", "vehicleCode": "0", "packageType": 0, "skuId": 46121699}}, {"reference": {"bizVendorCode": "SD3987", "vehicleCode": "0", "packageType": 1, "skuId": 3388049}}, {"reference": {"bizVendorCode": "SD4410", "vehicleCode": "0", "packageType": 1, "skuId": 57104797}}, {"reference": {"bizVendorCode": "SD14035", "vehicleCode": "0", "packageType": 1, "skuId": 64674345}}, {"reference": {"bizVendorCode": "SD15275", "vehicleCode": "0", "packageType": 1, "skuId": 73919819}}, {"reference": {"bizVendorCode": "SD10763", "vehicleCode": "0", "packageType": 1, "skuId": 51441167}}, {"reference": {"bizVendorCode": "SD11239", "vehicleCode": "0", "packageType": 1, "skuId": 72832950}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 21166839}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 16625344}}, {"reference": {"bizVendorCode": "SD7103", "vehicleCode": "0", "packageType": 1, "skuId": 62556145}}, {"reference": {"bizVendorCode": "SD4552", "vehicleCode": "0", "packageType": 1, "skuId": 44384894}}, {"reference": {"bizVendorCode": "SD8155", "vehicleCode": "0", "packageType": 1, "skuId": 43673666}}, {"reference": {"bizVendorCode": "SD10744", "vehicleCode": "0", "packageType": 1, "skuId": 76178390}}, {"reference": {"bizVendorCode": "SD5523", "vehicleCode": "0", "packageType": 1, "skuId": 62119317}}, {"reference": {"bizVendorCode": "SD5118", "vehicleCode": "0", "packageType": 1, "skuId": 1914745}}, {"reference": {"bizVendorCode": "SD5809", "vehicleCode": "0", "packageType": 1, "skuId": 54503457}}, {"reference": {"bizVendorCode": "SD6673", "vehicleCode": "0", "packageType": 1, "skuId": 55333188}}, {"reference": {"bizVendorCode": "SD3926", "vehicleCode": "0", "packageType": 1, "skuId": 24697408}}, {"reference": {"bizVendorCode": "SD4518", "vehicleCode": "0", "packageType": 1, "skuId": 4696845}}, {"reference": {"bizVendorCode": "SD14617", "vehicleCode": "0", "packageType": 0, "skuId": 66216501}}], "reactId": "2045256162", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD7678_0_265301_265301"]}, "minTPrice": 428.0, "minDPrice": 73.0, "modifySameVehicle": false, "minDOrinPrice": 87, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "自助取还", "category": 2, "type": 1, "code": "6", "sortNum": 1, "showLayer": 1, "colorCode": "1", "labelCode": "3866", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "pTag": {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减58", "groupId": 1, "mergeId": 0}, "priceSize": 28, "isEasy": true, "isCredit": true, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_5407_"}, {"vehicleCode": "1069", "sortNum": 33, "lowestPrice": 73.0, "highestPrice": 460.0, "maximumRating": 5.0, "maximumCommentCount": 52651, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD7678", "vendorCode": "15000935", "pStoreCode": "265301", "rStoreCode": "265301", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]NTI3NDQ0ODMwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDomJjEmMCY4OCY4NyZ0cnVlNS0wMSQyMDIwMDowLTAyICY4NiYwOjAwYWxzZSYxJmYkMjAyJjg2Ji0wMyA1LTAxMDowMDAwOjAmMSZmJjg2JiY4NiZhbHNlNS0wMSQyMDIwMDowLTA0ICY4NiYwOjAwYWxzZSYxJmYkfDEwJjg2JiY4NyYwMSY0MTAwMzM0NiQwLjAwJjEmMjAwJDEmMjAuNCYzMDAwMiYxMjAuLjAwJjIwMjUwMCR8MDEgMS0wMS06MDAmMDowMC0wMS0yMDI1MDowMDA1IDEyMDI0OjAwfDIzIDItMTItOjI0ADA6NDU=", "priceVersion": "SH-PRICEVERSION_MjY1MzAxXzQ0OTNfMV84N18zNDZfODhfNDg2LjAwXzczLjBfNDI4LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF80NDgzNTI3NA==", "vendorVehicleCode": "44835274", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 44835274, "pLev": 1217371, "rLev": 1217371, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 288.0, "amountStr": "¥288", "subAmount": 73.0, "subAmountStr": "日均¥73", "originalDailyPrice": 87, "detail": [{"code": "1001", "name": "租车费", "amount": 346, "amountDesc": "¥346"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 28, "amountDesc": "¥28"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 428.0, "amountStr": "¥428", "subAmount": 486.0, "subAmountStr": "¥486", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD7763", "vehicleCode": "0", "packageType": 1, "skuId": 787063}}, {"reference": {"bizVendorCode": "SD6573", "vehicleCode": "0", "packageType": 1, "skuId": 2235814}}, {"reference": {"bizVendorCode": "SD7763", "vehicleCode": "0", "packageType": 1, "skuId": 72903993}}, {"reference": {"bizVendorCode": "SD3933", "vehicleCode": "0", "packageType": 1, "skuId": 28097896}}, {"reference": {"bizVendorCode": "SD6709", "vehicleCode": "0", "packageType": 1, "skuId": 51716988}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 30540952}}, {"reference": {"bizVendorCode": "SD3047", "vehicleCode": "0", "packageType": 1, "skuId": 51724450}}, {"reference": {"bizVendorCode": "SD12615", "vehicleCode": "0", "packageType": 0, "skuId": 46121671}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 6900277}}, {"reference": {"bizVendorCode": "SD3012", "vehicleCode": "0", "packageType": 1, "skuId": 228229}}, {"reference": {"bizVendorCode": "SD14450", "vehicleCode": "0", "packageType": 1, "skuId": 156068}}, {"reference": {"bizVendorCode": "SD14035", "vehicleCode": "0", "packageType": 1, "skuId": 65051106}}, {"reference": {"bizVendorCode": "SD8506", "vehicleCode": "0", "packageType": 1, "skuId": 69928384}}, {"reference": {"bizVendorCode": "SD11239", "vehicleCode": "0", "packageType": 1, "skuId": 68938626}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 25635982}}, {"reference": {"bizVendorCode": "SD4552", "vehicleCode": "0", "packageType": 0, "skuId": 73773897}}, {"reference": {"bizVendorCode": "SD8155", "vehicleCode": "0", "packageType": 1, "skuId": 43673680}}, {"reference": {"bizVendorCode": "SD3926", "vehicleCode": "0", "packageType": 1, "skuId": 60352531}}, {"reference": {"bizVendorCode": "SD4113", "vehicleCode": "0", "packageType": 1, "skuId": 65943851}}], "reactId": "2045256163", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.19054455, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD7678_0_265301_265301"]}, "minTPrice": 428.0, "minDPrice": 73.0, "modifySameVehicle": false, "minDOrinPrice": 87, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减58", "groupId": 1, "mergeId": 0}, "priceSize": 20, "isEasy": true, "isCredit": true, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_1069_"}, {"vehicleCode": "658", "sortNum": 62, "lowestPrice": 49.0, "highestPrice": 106.0, "maximumRating": 5.0, "maximumCommentCount": 1681, "lowestDistance": 1.562, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD11466", "vendorCode": "15004351", "pStoreCode": "425573", "rStoreCode": "425573", "vehicleCode": "0", "packageType": 0, "priceVersion": "SH-PRICEVERSION_NDI1NTczXzY1OF8xXzUzLjBfMjEyLjBfMC4wXzQzMi4wXzQ5LjBfNDEzLjBfMF8wXzAuMF8wLjBfMjAwLjBfMjAuMF8wXzBfNTQ0NDYwOTI=", "vendorVehicleCode": "3573", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 54446092, "pLev": -1, "rLev": -1, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 193.0, "amountStr": "¥193", "subAmount": 49.0, "subAmountStr": "日均¥49", "originalDailyPrice": 53.0, "detail": [{"code": "1001", "name": "租车费", "amount": 212.0, "amountDesc": "¥212"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}, {"code": "3852", "name": "长租特惠", "amount": 9, "amountDesc": "¥9"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 200.0, "amountStr": "¥200", "detail": [{"code": "1002", "name": "基础服务费", "amount": 200.0, "amountDesc": "¥200", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 413.0, "amountStr": "¥413", "subAmount": 432.0, "subAmountStr": "¥432", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4552", "vehicleCode": "0", "packageType": 1, "skuId": 44384873}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 18605518}}], "reactId": "20452561610", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.20657748, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD11466_0_425573_425573"]}, "minTPrice": 413.0, "minDPrice": 49.0, "modifySameVehicle": false, "minDOrinPrice": 53.0, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "自助取还", "category": 2, "type": 1, "code": "6", "sortNum": 1, "showLayer": 1, "colorCode": "1", "labelCode": "3866", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "pTag": {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减19", "groupId": 1, "mergeId": 0}, "priceSize": 3, "isEasy": true, "isCredit": true, "pWay": "可选：店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_658_"}, {"vehicleCode": "658", "sortNum": 62, "lowestPrice": 49.0, "highestPrice": 106.0, "maximumRating": 5.0, "maximumCommentCount": 1681, "lowestDistance": 1.562, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD11466", "vendorCode": "15004351", "pStoreCode": "425573", "rStoreCode": "425573", "vehicleCode": "0", "packageType": 0, "priceVersion": "SH-PRICEVERSION_NDI1NTczXzY1OF8xXzUzLjBfMjEyLjBfMC4wXzQzMi4wXzQ5LjBfNDEzLjBfMF8wXzAuMF8wLjBfMjAwLjBfMjAuMF8wXzBfNTQ0NDYwOTI=", "vendorVehicleCode": "3573", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 54446092, "pLev": -1, "rLev": -1, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 193.0, "amountStr": "¥193", "subAmount": 49.0, "subAmountStr": "日均¥49", "originalDailyPrice": 53.0, "detail": [{"code": "1001", "name": "租车费", "amount": 212.0, "amountDesc": "¥212"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}, {"code": "3852", "name": "长租特惠", "amount": 9, "amountDesc": "¥9"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 200.0, "amountStr": "¥200", "detail": [{"code": "1002", "name": "基础服务费", "amount": 200.0, "amountDesc": "¥200", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 413.0, "amountStr": "¥413", "subAmount": 432.0, "subAmountStr": "¥432", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4552", "vehicleCode": "0", "packageType": 1, "skuId": 44384873}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 18605518}}], "reactId": "20452561610", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.20657748, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD11466_0_425573_425573"]}, "minTPrice": 413.0, "minDPrice": 49.0, "modifySameVehicle": false, "minDOrinPrice": 53.0, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "自助取还", "category": 2, "type": 1, "code": "6", "sortNum": 1, "showLayer": 1, "colorCode": "1", "labelCode": "3866", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "pTag": {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减19", "groupId": 1, "mergeId": 0}, "priceSize": 3, "isEasy": true, "isCredit": true, "pWay": "可选：店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_658_"}, {"vehicleCode": "658", "sortNum": 62, "lowestPrice": 49.0, "highestPrice": 106.0, "maximumRating": 5.0, "maximumCommentCount": 1681, "lowestDistance": 1.562, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD11466", "vendorCode": "15004351", "pStoreCode": "425573", "rStoreCode": "425573", "vehicleCode": "0", "packageType": 0, "priceVersion": "SH-PRICEVERSION_NDI1NTczXzY1OF8xXzUzLjBfMjEyLjBfMC4wXzQzMi4wXzQ5LjBfNDEzLjBfMF8wXzAuMF8wLjBfMjAwLjBfMjAuMF8wXzBfNTQ0NDYwOTI=", "vendorVehicleCode": "3573", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 54446092, "pLev": -1, "rLev": -1, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 193.0, "amountStr": "¥193", "subAmount": 49.0, "subAmountStr": "日均¥49", "originalDailyPrice": 53.0, "detail": [{"code": "1001", "name": "租车费", "amount": 212.0, "amountDesc": "¥212"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}, {"code": "3852", "name": "长租特惠", "amount": 9, "amountDesc": "¥9"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 200.0, "amountStr": "¥200", "detail": [{"code": "1002", "name": "基础服务费", "amount": 200.0, "amountDesc": "¥200", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 413.0, "amountStr": "¥413", "subAmount": 432.0, "subAmountStr": "¥432", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4552", "vehicleCode": "0", "packageType": 1, "skuId": 44384873}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 18605518}}], "reactId": "20452561610", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.20657748, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD11466_0_425573_425573"]}, "minTPrice": 413.0, "minDPrice": 49.0, "modifySameVehicle": false, "minDOrinPrice": 53.0, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "自助取还", "category": 2, "type": 1, "code": "6", "sortNum": 1, "showLayer": 1, "colorCode": "1", "labelCode": "3866", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "pTag": {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减19", "groupId": 1, "mergeId": 0}, "priceSize": 3, "isEasy": true, "isCredit": true, "pWay": "可选：店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_658_"}, {"vehicleCode": "658", "sortNum": 62, "lowestPrice": 49.0, "highestPrice": 106.0, "maximumRating": 5.0, "maximumCommentCount": 1681, "lowestDistance": 1.562, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD11466", "vendorCode": "15004351", "pStoreCode": "425573", "rStoreCode": "425573", "vehicleCode": "0", "packageType": 0, "priceVersion": "SH-PRICEVERSION_NDI1NTczXzY1OF8xXzUzLjBfMjEyLjBfMC4wXzQzMi4wXzQ5LjBfNDEzLjBfMF8wXzAuMF8wLjBfMjAwLjBfMjAuMF8wXzBfNTQ0NDYwOTI=", "vendorVehicleCode": "3573", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 54446092, "pLev": -1, "rLev": -1, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 193.0, "amountStr": "¥193", "subAmount": 49.0, "subAmountStr": "日均¥49", "originalDailyPrice": 53.0, "detail": [{"code": "1001", "name": "租车费", "amount": 212.0, "amountDesc": "¥212"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}, {"code": "3852", "name": "长租特惠", "amount": 9, "amountDesc": "¥9"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 200.0, "amountStr": "¥200", "detail": [{"code": "1002", "name": "基础服务费", "amount": 200.0, "amountDesc": "¥200", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 413.0, "amountStr": "¥413", "subAmount": 432.0, "subAmountStr": "¥432", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4552", "vehicleCode": "0", "packageType": 1, "skuId": 44384873}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 18605518}}], "reactId": "20452561610", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.20657748, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD11466_0_425573_425573"]}, "minTPrice": 413.0, "minDPrice": 49.0, "modifySameVehicle": false, "minDOrinPrice": 53.0, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "自助取还", "category": 2, "type": 1, "code": "6", "sortNum": 1, "showLayer": 1, "colorCode": "1", "labelCode": "3866", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "pTag": {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减19", "groupId": 1, "mergeId": 0}, "priceSize": 3, "isEasy": true, "isCredit": true, "pWay": "可选：店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_658_"}, {"vehicleCode": "658", "sortNum": 62, "lowestPrice": 49.0, "highestPrice": 106.0, "maximumRating": 5.0, "maximumCommentCount": 1681, "lowestDistance": 1.562, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD11466", "vendorCode": "15004351", "pStoreCode": "425573", "rStoreCode": "425573", "vehicleCode": "0", "packageType": 0, "priceVersion": "SH-PRICEVERSION_NDI1NTczXzY1OF8xXzUzLjBfMjEyLjBfMC4wXzQzMi4wXzQ5LjBfNDEzLjBfMF8wXzAuMF8wLjBfMjAwLjBfMjAuMF8wXzBfNTQ0NDYwOTI=", "vendorVehicleCode": "3573", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 54446092, "pLev": -1, "rLev": -1, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 193.0, "amountStr": "¥193", "subAmount": 49.0, "subAmountStr": "日均¥49", "originalDailyPrice": 53.0, "detail": [{"code": "1001", "name": "租车费", "amount": 212.0, "amountDesc": "¥212"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}, {"code": "3852", "name": "长租特惠", "amount": 9, "amountDesc": "¥9"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 200.0, "amountStr": "¥200", "detail": [{"code": "1002", "name": "基础服务费", "amount": 200.0, "amountDesc": "¥200", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 413.0, "amountStr": "¥413", "subAmount": 432.0, "subAmountStr": "¥432", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4552", "vehicleCode": "0", "packageType": 1, "skuId": 44384873}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 18605518}}], "reactId": "20452561610", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.20657748, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD11466_0_425573_425573"]}, "minTPrice": 413.0, "minDPrice": 49.0, "modifySameVehicle": false, "minDOrinPrice": 53.0, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "自助取还", "category": 2, "type": 1, "code": "6", "sortNum": 1, "showLayer": 1, "colorCode": "1", "labelCode": "3866", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "pTag": {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减19", "groupId": 1, "mergeId": 0}, "priceSize": 3, "isEasy": true, "isCredit": true, "pWay": "可选：店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_658_"}, {"vehicleCode": "658", "sortNum": 62, "lowestPrice": 49.0, "highestPrice": 106.0, "maximumRating": 5.0, "maximumCommentCount": 1681, "lowestDistance": 1.562, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD11466", "vendorCode": "15004351", "pStoreCode": "425573", "rStoreCode": "425573", "vehicleCode": "0", "packageType": 0, "priceVersion": "SH-PRICEVERSION_NDI1NTczXzY1OF8xXzUzLjBfMjEyLjBfMC4wXzQzMi4wXzQ5LjBfNDEzLjBfMF8wXzAuMF8wLjBfMjAwLjBfMjAuMF8wXzBfNTQ0NDYwOTI=", "vendorVehicleCode": "3573", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 54446092, "pLev": -1, "rLev": -1, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 193.0, "amountStr": "¥193", "subAmount": 49.0, "subAmountStr": "日均¥49", "originalDailyPrice": 53.0, "detail": [{"code": "1001", "name": "租车费", "amount": 212.0, "amountDesc": "¥212"}, {"code": "11037", "name": "优惠券", "amount": 10, "amountDesc": "¥10"}, {"code": "3852", "name": "长租特惠", "amount": 9, "amountDesc": "¥9"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 200.0, "amountStr": "¥200", "detail": [{"code": "1002", "name": "基础服务费", "amount": 200.0, "amountDesc": "¥200", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 413.0, "amountStr": "¥413", "subAmount": 432.0, "subAmountStr": "¥432", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4552", "vehicleCode": "0", "packageType": 1, "skuId": 44384873}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 18605518}}], "reactId": "20452561610", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.20657748, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD11466_0_425573_425573"]}, "minTPrice": 413.0, "minDPrice": 49.0, "modifySameVehicle": false, "minDOrinPrice": 53.0, "outTags": [{"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "自助取还", "category": 2, "type": 1, "code": "6", "sortNum": 1, "showLayer": 1, "colorCode": "1", "labelCode": "3866", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}], "pTag": {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减19", "groupId": 1, "mergeId": 0}, "priceSize": 3, "isEasy": true, "isCredit": true, "pWay": "可选：店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 0, "vehicleKey": "0_658_"}], "recommendVehicleList": [{"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "4139", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.0T-1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5i12000c7kmh93066A.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5212000c7mwubp7B39.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0l12000c7mwy55EE30.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000c7mwvvc7936.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3212000c7mx6u22402.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3012000c7mwv0sCA00.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0612000d5pibur948E.png", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "guidSys": "不支持", "carPlay": "部分车辆支持原厂互联/映射/CarLife/CarPlay", "chargeInterface": "部分车辆支持USB/AUX/Type-C/SD", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "vehiclesSetId": "69", "mediaTypes": [3, 2], "vehicleKey": "0_4139_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田卡罗拉（23款及以前）", "zhName": "丰田卡罗拉（23款及以前）", "vehicleCode": "5407", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.2T-1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0e12000c5s92ry5659.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000c5s9frq4F26.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0k12000c5s94goBD02.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1p12000c5s9fry9DF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6o12000c5s97iu8E9C.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "guidSys": "支持全速自适应巡航", "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "chargeInterface": "支持USB/Type-C", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "carPhone": true, "autoBackUp": true, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "vehiclesSetId": "69", "mediaTypes": [3], "vehicleKey": "0_5407_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田卡罗拉（23款及以前）", "zhName": "丰田卡罗拉（23款及以前）", "vehicleCode": "5407", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.2T-1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche"], "isSpecialized": true, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0e12000c5s92ry5659.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000c5s9frq4F26.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0k12000c5s94goBD02.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1p12000c5s9fry9DF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6o12000c5s97iu8E9C.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "guidSys": "支持全速自适应巡航", "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "chargeInterface": "支持USB/Type-C", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "carPhone": true, "autoBackUp": true, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "vehiclesSetId": "69", "mediaTypes": [3], "vehicleKey": "2_5407_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}}, {"brandEName": "大众", "brandName": "大众", "name": "大众朗逸", "zhName": "大众朗逸", "vehicleCode": "1069", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.5L-1.6L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0a12000c7kmcp84670.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0312000chlvgr2CE91.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1u12000chlvgyp97DC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000chlv9fkFF40.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000chlv9ft6840.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2612000chlvh5q2C37.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0m12000chlv9o2C40D.jpg?mark=yiche", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "guidSys": "部分车辆支持定速巡航", "carPlay": "不支持", "chargeInterface": "部分车辆支持USB/AUX/SD", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "69", "mediaTypes": [], "vehicleKey": "0_1069_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}}, {"brandEName": "大众", "brandName": "大众", "name": "大众朗逸", "zhName": "大众朗逸", "vehicleCode": "1069", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.5L-1.6L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0a12000c7kmcp84670.png?mark=yiche"], "isSpecialized": true, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0312000chlvgr2CE91.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1u12000chlvgyp97DC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000chlv9fkFF40.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000chlv9ft6840.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2612000chlvh5q2C37.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0m12000chlv9o2C40D.jpg?mark=yiche", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "guidSys": "部分车辆支持定速巡航", "carPlay": "不支持", "chargeInterface": "部分车辆支持USB/AUX/SD", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "autoBackUp": false, "vehiclesSetId": "69", "mediaTypes": [], "vehicleKey": "2_1069_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}}, {"brandEName": "本田", "brandName": "本田", "name": "本田飞度", "zhName": "本田飞度", "vehicleCode": "658", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "displacement": "1.5L", "struct": "两厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4112000etfg9h2DF2F.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1p12000cfa48158FE0.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000cfa4ay5A013.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3912000cfa4cza1898.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5312000cfa4dilC852.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3q12000cfa443u3D27.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV5712000bdpz7mlB131.jpg", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "guidSys": "部分车辆支持自适应巡航", "carPlay": "部分车辆支持CarLife", "chargeInterface": "部分车辆支持USB/AUX/HDMI", "skylight": "部分车辆支持", "charge": "", "autoPark": false, "autoBackUp": false, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3686&app_ver=10.5", "vehiclesSetId": "69", "mediaTypes": [3, 2], "vehicleKey": "0_658_", "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}}], "productGroupCodeUesd": "2", "priceExplain": {"title": "价格说明", "content": ["划线价格为参考价，该价格指商品或服务的门市价、服务提供商的指导价、零售价或该商品或服务曾经展示过的销售价等并非原价；由于产品信息实时更新、市场价格波动等可能会与您预订时展示的不一致，该价格仅供您参考。", "未划线价格指商品或服务的实时标价，为划线价基础上计算出的优惠金额。具体成交价格根据商品或服务参加活动，或会员使用优惠券等发生变化，最终以订单结算页价格为准。", "如有疑问，您可在预订前联系客服进行咨询。", "此说明仅当出现价格比较时有效。若服务提供商单独对划线价格进行说明的，以服务提供商的表述为准。 "]}, "shareVehicleInfo": {"vehicleName": "起亚 K2", "groupName": "起亚 K2", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "displacement": "1.4L-1.6L", "style": "", "currentOriginalDailyPrice": 83, "vehicleImage": "https://dimg04.c-ctrip.com/images/0RV4312000d5p3loaA404.png", "license": ""}}