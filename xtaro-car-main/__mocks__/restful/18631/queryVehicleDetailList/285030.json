{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1682659471401+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "8725936919823322892"}, {"Id": "RootMessageId", "Value": "100025527-0a71b621-467405-1621696"}]}, "vehicleInfo": {"brandEName": "荣威", "brandName": "荣威", "name": "荣威RX5新能源", "zhName": "荣威RX5新能源", "vehicleCode": "4092", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 5, "displacement": "1.5T", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6y12000au0o8az9D1B.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-waiguan1.jpg", "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-xijie-2.jpg", "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-zuoyi2.jpg"], "license": "沪牌", "licenseStyle": "2", "licenseDescription": "沪牌车辆可在上海市内各高架、隧道行驶", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6z12000au0ocysDF52.jpg", "sourcePicInfos": [{"source": 0, "type": 2, "sourceName": "年款/颜色等以实物为准", "picList": [{"imageUrl": "https://dimg04.c-ctrip.com/images/0RV6z12000au0ocysDF52.jpg", "sortNum": 0}, {"imageUrl": "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-waiguan1.jpg", "sortNum": 1}, {"imageUrl": "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-xijie-2.jpg", "sortNum": 2}, {"imageUrl": "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-zuoyi2.jpg", "sortNum": 3}]}], "oilType": 4, "fuelType": "插电式", "luggageNum": "可放5个24寸行李箱", "endurance": "续航50km-60km", "charge": "慢充4小时", "autoPark": false, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "carPhone": true, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "autoBackUp": true, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "subGroupCode": "newenergy", "vr": "", "vehiclesSetId": "15"}, "specificProductGroups": {"vendorPriceList": [{"vendorName": "安卡尔租车", "isMinTPriceVendor": true, "vendorLogo": "", "commentInfo": {"level": "", "vendorDesc": "安卡尔青浦", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 1530.0, "currentOriginalDailyPrice": 1800, "oTPrice": 3875.0, "currentTotalPrice": 3335.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 540.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3402", "vendorCode": "85585", "pStoreCode": "107744", "rStoreCode": "107744", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD3402_0_107744_107744", "comPriceCode": "[c]NTA1fDQzOTczLTA0fDIwMjAwOjAtMjggJjE4MDA6MDAkMjAyMCYmMS0yOSAzLTA0MDowMDAwOjAwJiYxJjE4MDAxJjIkfDEwMCYzNiYxODAwMDMmMDAkMS4wMCYxJjM1MCQxMDM1LjAmNDAmMDImMjIwMjM4MCR8MjggMS0wNC06MTQAMzoyNA==", "priceVersion": "SH-PRICEVERSION_MTA3NzQ0XzQwOTJfMV8xODAwXzM2MDBfMTgwMF8zODc1LjAwXzE1MzAuMF8zMzM1LjBfMV8xXzAuMF8wLjBfODBfMzUuMDBfODAuMDBfODAuMDBfNDM5NzUwNQ==", "pCityId": 2, "rCityId": 2, "vendorVehicleCode": "0", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "五一特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3692", "groupCode": "MarketGroup1338", "amountTitle": "已减540", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员收费上门送取车", "rStoreNav": "店员收费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 2, "returnWayInfo": 2, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 3875.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 4092, "rentalamount": 3060.0, "totalDailyPrice": 1530.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "低价省钱", "pRc": 0, "rRc": 0, "skuId": 4397505, "klbPId": 1288, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 97762, "rLevel": 97762, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "2", "s": "100.0", "c": "2", "v": "94287"}, "newEnergy": 1, "platform": 10, "kPSId": 107744, "kRSId": 107744, "kVId": 85585, "pLev": 97762, "rLev": 97762, "klbVersion": 1, "kVehicleId": 4092}, "sortNum": 0.0, "pStoreRouteDesc": "店员收费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "有损取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "4", "labelCode": "3683", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "五一特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3692", "groupCode": "MarketGroup1338", "amountTitle": "已减540", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 8388608, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 2, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 2, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 306, "checkType": 0}], "sortScore": 0.0, "storeScore": 98.9, "isSelect": false, "distance": 17.6116, "rDistance": 17.6116, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3692", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 80.0, "pickOffFee": 80.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "安卡尔租车", "card": 0, "ctripVehicleCode": "4092", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 3060.0, "amountStr": "¥3060", "subAmount": 1530.0, "subAmountStr": "日均¥1530", "originalDailyPrice": 1800, "detail": [{"code": "1001", "name": "租车费", "amount": 3600, "amountDesc": "¥3600"}, {"code": "3692", "name": "五一特惠", "amount": 540, "amountDesc": "¥540"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 195.0, "amountStr": "¥195", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}, {"code": "11026", "name": "送车上门服务费", "amount": 80.0, "amountDesc": "¥80", "showFree": false}, {"code": "11027", "name": "上门取车服务费", "amount": 80.0, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 3335.0, "amountStr": "¥3335", "subAmount": 3875.0, "subAmountStr": "¥3875", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "f564f135c9d44ad7b7550403afdce2e8", "licenseTag": "沪牌"}, {"vendorName": "延瑞租车", "isMinTPriceVendor": false, "commentInfo": {"level": "", "vendorDesc": "延瑞租车", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 1725, "currentOriginalDailyPrice": 0, "oTPrice": 3565.0, "currentTotalPrice": 3565.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD7283", "vendorCode": "15000541", "pStoreCode": "153109", "rStoreCode": "153109", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD7283_0_153109_153109", "comPriceCode": "[c]MzMxfDQwMTF8MjAyMC4xNS0yOCAzLTA0MDowMDAwOjA1JiYxJjE3MjMtMDQkMjAyMDA6MC0yOSAmMTcyMDowMCR8MTA1JiYxJjE3MjAxJjI1MCQxNSYzNDEmMzUwMDMmMzUuMC4wMCYwMiYyMCQxMDgwJHwmNDAmLTA0LTIwMjMzOjI0MjggMQAAAAA6MTQA", "priceVersion": "SH-PRICEVERSION_MTUzMTA5XzQwOTJfMV8xNzI1XzM0NTBfMTcyNV8zNTY1LjAwXzE3MjVfMzU2NS4wXzBfMF8wLjBfMC4wXzgwXzM1LjAwXzAuMDBfMC4wMF80MDExMzMx", "pCityId": 2, "rCityId": 2, "vendorVehicleCode": "20038665", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 3565.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 4092, "rentalamount": 3450, "totalDailyPrice": 1725, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 4011331, "klbPId": 14445, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 88926, "rLevel": 88926, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "79.05", "c": "2", "v": "153109"}, "newEnergy": 1, "platform": 10, "kPSId": 153109, "kRSId": 153109, "kVId": 15000541, "pLev": 88926, "rLev": 88926, "klbVersion": 1, "kVehicleId": 4092}, "sortNum": 1.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}], "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 2, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 498, "checkType": 0}], "sortScore": 1.0, "storeScore": 98.6, "isSelect": false, "distance": 0.0, "rDistance": 19.8205, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "延瑞租车", "card": 0, "ctripVehicleCode": "4092", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 3450, "amountStr": "¥3450", "subAmount": 1725, "subAmountStr": "日均¥1725", "detail": [{"code": "1001", "name": "租车费", "amount": 3450, "amountDesc": "¥3450"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 3565.0, "amountStr": "¥3565", "subAmount": 3565.0, "subAmountStr": "¥3565", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "c0d9bd619e6d4c4481e41bdd24c8d9cb", "licenseTag": "沪牌"}]}, "filteredProductGroups": {"vendorPriceList": []}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "isFromSearch": false, "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": []}, {"stringObjs": []}], "type": 11, "icon": "", "locations": [{"groupCode": "all", "index": 1}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/0304w12000avji5x91C0A.jpg", "textColor": {"r": 255, "g": 255, "b": 255, "a": 1}, "tangChoiceTypes": "[]", "jumpUrl": "https://m.ctrip.com/tangram/MzM1Nzc=?ctm_ref=vactang_page_33577&isHideNavBar=YES&apppgid=10650039393"}], "promotMap": {}, "extras": {"abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,230403_DSJT_cxtjy|B", "isNewLicensePlate": "0", "serverRequestId": "oJF5YdO7713O6k7yi7d9"}, "imStatus": 1}