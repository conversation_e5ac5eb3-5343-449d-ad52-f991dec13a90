{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1683603539319+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "7684763711075617508"}, {"Id": "RootMessageId", "Value": "100025527-0a3d52dd-467667-1399718"}]}, "vehicleInfo": {"brandEName": "荣威", "brandName": "荣威", "name": "荣威ei6 MAX", "zhName": "荣威ei6 MAX", "vehicleCode": "5287", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "1.5T", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0411u120008n39iyw70D8.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0411v120008as8d6q01F8.jpg", "https://dimg04.c-ctrip.com//images/0416x120008as8vftFC39.jpg", "https://dimg04.c-ctrip.com//images/0413n120008as66t9C996.jpg", "https://dimg04.c-ctrip.com//images/04104120008as77l6E6E6.jpg", "https://dimg04.c-ctrip.com//images/0413h120008as61kt5769.jpg", "https://dimg04.c-ctrip.com//images/04160120008as81me005A.jpg", "https://dimg04.c-ctrip.com//images/0416v120008as6qe0EC71.jpg"], "license": "沪牌", "licenseStyle": "2", "licenseDescription": "沪牌车辆可在上海市内各高架、隧道行驶", "realityImageUrl": "https://dimg04.c-ctrip.com//images/04150120008as7nly6411.jpg", "sourcePicInfos": [{"source": 0, "type": 2, "sourceName": "年款/颜色等以实物为准", "picList": [{"imageUrl": "https://dimg04.c-ctrip.com//images/04150120008as7nly6411.jpg", "sortNum": 0}]}, {"source": 3, "type": 2, "sourceName": "年款/颜色等以实物为准丨图片来源懂车帝", "picList": [{"imageUrl": "https://dimg04.c-ctrip.com//images/04104120008as77l6E6E6.jpg", "sortNum": 4}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0411v120008as8d6q01F8.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0413h120008as61kt5769.jpg", "sortNum": 5}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0413n120008as66t9C996.jpg", "sortNum": 3}, {"imageUrl": "https://dimg04.c-ctrip.com//images/04160120008as81me005A.jpg", "sortNum": 6}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0416v120008as6qe0EC71.jpg", "sortNum": 7}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0416x120008as8vftFC39.jpg", "sortNum": 2}]}], "oilType": 4, "fuelType": "插电式", "luggageNum": "可放3个24寸行李箱", "endurance": "续航51km", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vr": "", "vehiclesSetId": "74"}, "specificProductGroups": {"title": "", "vendorPriceList": [{"vendorName": "申恺租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "水产路店", "commentCount": 1381, "qCommentCount": 1381, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 94.0, "currentOriginalDailyPrice": 99, "oTPrice": 324.0, "currentTotalPrice": 319.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 5.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4034", "vendorCode": "71523", "pStoreCode": "107408", "rStoreCode": "107408", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4034_0_107408_107408", "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTA3NDA4XzUyODdfMV85OV85OV85OV8zMjQuMDBfOTQuMF8zMTkuMF8xXzFfMC4wXzAuMF83MF8zNS4wMF82MC4wMF82MC4wMF8xODU5NjM3", "pCityId": 2, "rCityId": 2, "vendorVehicleCode": "20082442", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减5", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员收费上门送取车", "rStoreNav": "店员收费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 2, "returnWayInfo": 2, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 324.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 5287, "rentalamount": 94.0, "totalDailyPrice": 94.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "精选好货", "pRc": 0, "rRc": 0, "skuId": 1859637, "klbPId": 2780, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 73097, "rLevel": 73097, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "2", "s": "57.29", "c": "2", "v": "72483"}, "newEnergy": 1, "platform": 10, "kPSId": 107408, "kRSId": 107408, "kVId": 71523, "pLev": 25681, "rLev": 25681, "klbVersion": 1, "kVehicleId": 5287}, "sortNum": 0.0, "pStoreRouteDesc": "店员收费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减5", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 2, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 99.27, "isSelect": true, "distance": 21.4211, "rDistance": 21.4211, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3783", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 60.0, "pickOffFee": 60.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "申恺租车", "card": 0, "ctripVehicleCode": "5287", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 94.0, "amountStr": "¥94", "subAmount": 94.0, "subAmountStr": "日均¥94", "originalDailyPrice": 99, "detail": [{"code": "1001", "name": "租车费", "amount": 99, "amountDesc": "¥99"}, {"code": "3783", "name": "黄金贵宾", "amount": 5, "amountDesc": "¥5"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 155.0, "amountStr": "¥155", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}, {"code": "11026", "name": "送车上门服务费", "amount": 60.0, "amountDesc": "¥60", "showFree": false}, {"code": "11027", "name": "上门取车服务费", "amount": 60.0, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 70, "amountStr": "¥70", "detail": [{"code": "1002", "name": "基础服务费", "amount": 70, "amountDesc": "¥70", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 319.0, "amountStr": "¥319", "subAmount": 324.0, "subAmountStr": "¥324", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "6d10f06bb1ce42e498c0fa2027e80140", "licenseTag": "沪牌"}, {"vendorName": "骏适出行", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "水产路店", "commentCount": 1381, "qCommentCount": 1381, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 94.0, "currentOriginalDailyPrice": 99, "oTPrice": 324.0, "currentTotalPrice": 319.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 5.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4034", "vendorCode": "71523", "pStoreCode": "107408", "rStoreCode": "107408", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4034_0_107408_107408", "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTA3NDA4XzUyODdfMV85OV85OV85OV8zMjQuMDBfOTQuMF8zMTkuMF8xXzFfMC4wXzAuMF83MF8zNS4wMF82MC4wMF82MC4wMF8xODU5NjM3", "pCityId": 2, "rCityId": 2, "vendorVehicleCode": "20082442", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减5", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员收费上门送取车", "rStoreNav": "店员收费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 2, "returnWayInfo": 2, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 324.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 5287, "rentalamount": 94.0, "totalDailyPrice": 94.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "低价省钱", "pRc": 0, "rRc": 0, "skuId": 1859637, "klbPId": 2780, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 73097, "rLevel": 73097, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "2", "s": "57.29", "c": "2", "v": "72483"}, "newEnergy": 1, "platform": 10, "kPSId": 107408, "kRSId": 107408, "kVId": 71523, "pLev": 25681, "rLev": 25681, "klbVersion": 1, "kVehicleId": 5287}, "sortNum": 0.0, "pStoreRouteDesc": "店员收费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减5", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 2, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 99.27, "isSelect": true, "distance": 21.4211, "rDistance": 21.4211, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3783", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 60.0, "pickOffFee": 60.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骏适出行", "card": 0, "ctripVehicleCode": "5287", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 94.0, "amountStr": "¥94", "subAmount": 94.0, "subAmountStr": "日均¥94", "originalDailyPrice": 99, "detail": [{"code": "1001", "name": "租车费", "amount": 99, "amountDesc": "¥99"}, {"code": "3783", "name": "黄金贵宾", "amount": 5, "amountDesc": "¥5"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 155.0, "amountStr": "¥155", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}, {"code": "11026", "name": "送车上门服务费", "amount": 60.0, "amountDesc": "¥60", "showFree": false}, {"code": "11027", "name": "上门取车服务费", "amount": 60.0, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 70, "amountStr": "¥70", "detail": [{"code": "1002", "name": "基础服务费", "amount": 70, "amountDesc": "¥70", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 319.0, "amountStr": "¥319", "subAmount": 324.0, "subAmountStr": "¥324", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "6d10f06bb1ce42e498c0fa2027e80140", "licenseTag": "沪牌"}]}, "filteredProductGroups": {"title": "", "vendorPriceList": [{"vendorName": "莱蓉租车", "isMinTPriceVendor": true, "vendorLogo": "", "commentInfo": {"level": "", "vendorDesc": "虹桥机场店", "commentCount": 5, "qCommentCount": 5, "qExposed": "3.7", "overallRating": "3.7", "maximumRating": 5.0, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 158, "currentOriginalDailyPrice": 0, "oTPrice": 253.0, "currentTotalPrice": 253.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD4915", "vendorCode": "81409", "pStoreCode": "115092", "rStoreCode": "115092", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD4915_0_115092_115092", "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTE1MDkyXzUyODdfMV8xNThfMTU4XzE1OF8yNTMuMDBfMTU4XzI1My4wXzBfMF8wLjBfMC4wXzYwXzM1LjAwXzAuMDBfMC4wMF8xOTA5MDE3", "pCityId": 2, "rCityId": 2, "vendorVehicleCode": "20079016", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 253.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 5287, "rentalamount": 158, "totalDailyPrice": 158, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "低价省钱", "pRc": 0, "rRc": 0, "skuId": 1909017, "klbPId": 6380, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 48681, "rLevel": 48681, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "2", "v": "86091"}, "newEnergy": 1, "platform": 10, "kPSId": 115092, "kRSId": 115092, "kVId": 81409, "pLev": 48681, "rLev": 48681, "klbVersion": 1, "kVehicleId": 5287}, "sortNum": 1.0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "10", "allTags": [{"title": "一年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3510", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 536870912, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 2, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 370, "checkType": 0}], "sortScore": 1.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 4.5199, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "莱蓉租车", "card": 0, "ctripVehicleCode": "5287", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 158, "amountStr": "¥158", "subAmount": 158, "subAmountStr": "日均¥158", "detail": [{"code": "1001", "name": "租车费", "amount": 158, "amountDesc": "¥158"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 253.0, "amountStr": "¥253", "subAmount": 253.0, "subAmountStr": "¥253", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "0b886dcedb8c414fa2d67e3563a8d1a3", "licenseTag": "沪牌"}, {"vendorName": "嘉澳出行", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "嘉澳真南路店", "commentCount": 66, "qCommentCount": 66, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 148.0, "currentOriginalDailyPrice": 156, "oTPrice": 361.0, "currentTotalPrice": 353.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 8.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD5673", "vendorCode": "85251", "pStoreCode": "117737", "rStoreCode": "117737", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD5673_0_117737_117737", "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTE3NzM3XzUyODdfMV8xNTZfMTU2XzE1Nl8zNjEuMDBfMTQ4LjBfMzUzLjBfMV8xXzAuMF8wLjBfNTBfMzUuMDBfNjAuMDBfNjAuMDBfMjMzMDYwMg==", "pCityId": 2, "rCityId": 2, "vendorVehicleCode": "20094780", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减8", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员收费上门送取车", "rStoreNav": "店员收费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 2, "returnWayInfo": 2, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 361.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 5287, "rentalamount": 148.0, "totalDailyPrice": 148.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 2330602, "klbPId": 11881, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 132567, "rLevel": 132567, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "2", "s": "44.91", "c": "2", "v": "92633"}, "newEnergy": 1, "platform": 10, "kPSId": 117737, "kRSId": 117737, "kVId": 85251, "pLev": 63589, "rLev": 63589, "klbVersion": 1, "kVehicleId": 5287}, "sortNum": 2.0, "pStoreRouteDesc": "店员收费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减8", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_4", "binaryDigit": 64, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 2, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 434, "checkType": 0}], "sortScore": 2.0, "storeScore": 99.69, "isSelect": false, "distance": 7.4428, "rDistance": 7.4428, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3783", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 60.0, "pickOffFee": 60.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "嘉澳出行", "card": 0, "ctripVehicleCode": "5287", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 148.0, "amountStr": "¥148", "subAmount": 148.0, "subAmountStr": "日均¥148", "originalDailyPrice": 156, "detail": [{"code": "1001", "name": "租车费", "amount": 156, "amountDesc": "¥156"}, {"code": "3783", "name": "黄金贵宾", "amount": 8, "amountDesc": "¥8"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 155.0, "amountStr": "¥155", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}, {"code": "11026", "name": "送车上门服务费", "amount": 60.0, "amountDesc": "¥60", "showFree": false}, {"code": "11027", "name": "上门取车服务费", "amount": 60.0, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "name": "基础服务费", "amount": 50, "amountDesc": "¥50", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 353.0, "amountStr": "¥353", "subAmount": 361.0, "subAmountStr": "¥361", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "9c9c607efecc44c8b4587a1ae73a0a31", "licenseTag": "沪牌"}, {"vendorName": "众妤出行", "isMinTPriceVendor": false, "commentInfo": {"level": "很好", "vendorDesc": "虹桥机场店", "commentCount": 18, "qCommentCount": 18, "qExposed": "4.8", "overallRating": "4.8", "maximumRating": 5.0, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 238.0, "currentOriginalDailyPrice": 251, "oTPrice": 446.0, "currentTotalPrice": 433.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 13.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD7100", "vendorCode": "15000358", "pStoreCode": "136705", "rStoreCode": "136705", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD7100_0_136705_136705", "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTM2NzA1XzUyODdfMV8yNTFfMjUxXzI1MV80NDYuMDBfMjM4LjBfNDMzLjBfMV8xXzAuMF8wLjBfNjBfMzUuMDBfNTAuMDBfNTAuMDBfMjQ4ODMzNA==", "pCityId": 2, "rCityId": 2, "vendorVehicleCode": "20379644", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减13", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员收费上门送取车", "rStoreNav": "店员收费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 2, "returnWayInfo": 2, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 446.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 5287, "rentalamount": 238.0, "totalDailyPrice": 238.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 2488334, "klbPId": 12737, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 78926, "rLevel": 78926, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "2", "s": "27.49", "c": "2", "v": "136705"}, "newEnergy": 1, "platform": 10, "kPSId": 136705, "kRSId": 136705, "kVId": 15000358, "pLev": 78926, "rLev": 78926, "klbVersion": 1, "kVehicleId": 5287}, "sortNum": 3.0, "pStoreRouteDesc": "店员收费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减13", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_5", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 2, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 498, "checkType": 0}], "sortScore": 3.0, "storeScore": 97.81, "isSelect": false, "distance": 5.9254, "rDistance": 5.9254, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3783", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 50.0, "pickOffFee": 50.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "众妤出行", "card": 0, "ctripVehicleCode": "5287", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 238.0, "amountStr": "¥238", "subAmount": 238.0, "subAmountStr": "日均¥238", "originalDailyPrice": 251, "detail": [{"code": "1001", "name": "租车费", "amount": 251, "amountDesc": "¥251"}, {"code": "3783", "name": "黄金贵宾", "amount": 13, "amountDesc": "¥13"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 135.0, "amountStr": "¥135", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}, {"code": "11026", "name": "送车上门服务费", "amount": 50.0, "amountDesc": "¥50", "showFree": false}, {"code": "11027", "name": "上门取车服务费", "amount": 50.0, "amountDesc": "¥50", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 433.0, "amountStr": "¥433", "subAmount": 446.0, "subAmountStr": "¥446", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "fa70d21a31514cf58a239bd1671ba6ba", "licenseTag": "沪牌"}, {"vendorName": "一嗨租车", "isMinTPriceVendor": false, "vendorLogo": "https://dimg04.c-ctrip.com/images/0AS6812000a92yn68EA59.jpg", "commentInfo": {"level": "超棒", "vendorDesc": "江桥店", "commentCount": 582, "qCommentCount": 582, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 215.0, "currentOriginalDailyPrice": 225.0, "oTPrice": 295.0, "currentTotalPrice": 285.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 10.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD6991", "vendorCode": "13088", "pStoreCode": "139883", "rStoreCode": "139883", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD6991_0_139883_139883", "priceVersion": "SH-PRICEVERSION_MTM5ODgzXzUyODdfMV8yMjUuMF8yMjUuMF8wLjBfMjk1LjBfMjE1LjBfMjg1LjBfMF8wXzAuMF8wLjBfNTAuMF8yMC4wXzBfMF8zNDI3NjYx", "pCityId": 2, "rCityId": 2, "vendorVehicleCode": "1940", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "一嗨特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3778", "groupCode": "MarketGroup1365", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "pStoreNav": "自行前往门店取还车", "rStoreNav": "自行前往门店取还车", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 0, "returnWayInfo": 0, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 295.0, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 5287, "rentalamount": 215.0, "totalDailyPrice": 215.0, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 3427661, "klbPId": 13158, "klb": 1, "pCType": -1, "rCType": -1, "pLevel": -1, "rLevel": -1, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "0", "s": "75.11", "c": "2"}, "newEnergy": 1, "platform": 0, "kPSId": 139883, "kRSId": 139883, "kVId": 13088, "pLev": -1, "rLev": -1, "klbVersion": 1, "kVehicleId": 5287}, "sortNum": 4.0, "pStoreRouteDesc": "自行前往门店取还车，距门店直线2.7公里", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "全国连锁", "category": 9, "type": 1, "code": "20", "sortNum": 10000, "colorCode": "1", "labelCode": "3757", "groupCode": "MarketGroup1335", "groupId": 2, "mergeId": 0}, {"title": "一嗨特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3778", "groupCode": "MarketGroup1365", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_r0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 2, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 2, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 8, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2, "checkType": 0}], "sortScore": 4.0, "storeScore": 98.9, "isSelect": false, "stock": 1, "distance": 2.6592, "rDistance": 2.6592, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3778", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "一嗨租车", "card": 0, "ctripVehicleCode": "5287", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 215.0, "amountStr": "¥215", "subAmount": 215.0, "subAmountStr": "日均¥215", "originalDailyPrice": 225.0, "detail": [{"code": "1001", "name": "租车费", "amount": 225.0, "amountDesc": "¥225"}, {"code": "3778", "name": "一嗨特惠", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 50.0, "amountStr": "¥50", "detail": [{"code": "1002", "name": "基础服务费", "amount": 50.0, "amountDesc": "¥50", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 285.0, "amountStr": "¥285", "subAmount": 295.0, "subAmountStr": "¥295", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "befec987e61945cd9a2952a97da0bac2", "licenseTag": "沪牌"}]}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "isFromSearch": false, "promotMap": {}, "extras": {"abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B,230403_DSJT_cxtjy|B", "isNewLicensePlate": "0", "serverRequestId": "eJl597c40083p5530314", "rSelect": "1"}, "imStatus": 1}