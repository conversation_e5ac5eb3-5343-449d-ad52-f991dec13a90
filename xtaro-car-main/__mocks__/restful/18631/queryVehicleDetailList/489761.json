{"promotMap": {}, "extras": {"packageLevelAB": "B", "abVersion": "240419_DSJT_wyz24|B,230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B,231218_DSJT_zzqh|B", "isLicensePlateHideShow": "0", "serverRequestId": "K15N1p014o6U71n6704X", "packageLevelSwitch": "1", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "1"}, "resBodySize": 48576, "baseResponse": {"code": "200", "requestId": "807d1c26-44e9-4a22-a7c6-9971565d3d86", "cost": 344, "isSuccess": true, "returnMsg": "OK"}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "productGroupCodeUesd": "4", "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "recommendVehicleList": [{"skylight": "部分车辆支持", "vehicleKey": "0_5283_沪牌", "luggageNo": 5, "carPlay": "支持支持CarPlay/支持CarLife", "displacement": "2.0T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV1x12000cf2440k87D7.jpg?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=4037&app_ver=10.5", "carPhone": true, "vehicleCode": "5283", "style": "2021款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1x12000cf2440k87D7.jpg?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5v12000c5rmv0xFB9E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5s12000c5rmrii54A5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5912000c5rmwv3E006.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4c12000c5rn4791853.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3i12000c5rmwvf2837.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5591_", "luggageNo": 2, "carPlay": "部分车辆支持原厂互联/映射", "displacement": "1.5T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6q12000ciat9yc2BCB.png?mark=yiche"], "license": "外牌", "isSpecialized": true, "groupCode": "4", "zhName": "上汽大通MAXUS G50", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7257&app_ver=10.5", "vehicleCode": "5591", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "上汽大通MAXUS G50", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6i12000b63x6xsA6CE.jpg", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0912000ciarxw8111F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5h12000ciarzw68DE7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000ciarrfv603F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2y12000ciarw7b53D3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5w12000ciarxjeD9B0.jpg?mark=yiche"], "transmissionType": 1, "brandName": "上汽大通MAXUS", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "上汽大通MAXUS", "licenseStyle": "6", "autoBackUp": true, "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5591_沪牌", "luggageNo": 2, "carPlay": "部分车辆支持原厂互联/映射", "displacement": "1.5T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6q12000ciat9yc2BCB.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "4", "zhName": "上汽大通MAXUS G50", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7257&app_ver=10.5", "vehicleCode": "5591", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "上汽大通MAXUS G50", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6i12000b63x6xsA6CE.jpg", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0912000ciarxw8111F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5h12000ciarzw68DE7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000ciarrfv603F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2y12000ciarw7b53D3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5w12000ciarxjeD9B0.jpg?mark=yiche"], "transmissionType": 1, "brandName": "上汽大通MAXUS", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "上汽大通MAXUS", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_4659_沪牌", "luggageNo": 5, "carPlay": "支持支持CarPlay", "displacement": "2.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0t12000cf9hq3zB508.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX", "mediaTypes": [], "carPhone": true, "vehicleCode": "4659", "style": "2017/18款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3c12000c6eb44lAB7C.jpg?mark=yiche", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3g12000c6eb2tfDCA3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3k12000c6eawz3F33E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0y12000c6eb0ghC333.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6w12000c6eb3df5EB1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3h12000c6eb26y9568.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "49", "guidSys": "支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "2_5591_沪牌", "luggageNo": 2, "carPlay": "部分车辆支持原厂互联/映射", "displacement": "1.5T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6q12000ciat9yc2BCB.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "4", "zhName": "上汽大通MAXUS G50", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7257&app_ver=10.5", "vehicleCode": "5591", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "上汽大通MAXUS G50", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6i12000b63x6xsA6CE.jpg", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0912000ciarxw8111F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5h12000ciarzw68DE7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000ciarrfv603F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2y12000ciarw7b53D3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5w12000ciarxjeD9B0.jpg?mark=yiche"], "transmissionType": 1, "brandName": "上汽大通MAXUS", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "上汽大通MAXUS", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航/自适应巡航", "transmissionName": "自动挡"}], "filteredProductGroups": {"vendorPriceList": []}, "vehicleInfo": {"skylight": "部分车辆支持", "vehicleKey": "0_17561_", "luggageNo": 5, "carPlay": "支持支持CarPlay", "displacement": "2.0T-2.5L", "autoPark": false, "charge": "", "fuelType": "汽油", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4512000elvdljb183C.png?mark=yiche"], "license": "外牌", "isSpecialized": true, "multimediaAlbums": [{"albumName": "官方相册", "albumType": 1, "mediaGroup": [{"groupType": 3, "groupName": "VR", "groupSortNum": 3, "medias": [{"sortNum": 0, "type": 3, "url": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=675&app_ver=10.5", "cover": "https://dimg04.c-ctrip.com/images/0RV4512000elvdljb183C.png?mark=yiche"}]}, {"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1v12000c6eah9uCDBF.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0i12000c6ea8k1C487.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2212000c6eahvv2D5E.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6i12000c6eahw57E3A.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2212000c6eacmoD1AB.jpg?mark=yiche", "cover": ""}]}, {"groupType": 5, "groupName": "前排", "groupSortNum": 5, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0o12000c6eaivoD4D0.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2f12000c6eakzuA728.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0f12000c6eacfx9063.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6q12000c6ea8kgDEFA.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5412000c6eaj2l595A.jpg?mark=yiche", "cover": ""}]}, {"groupType": 6, "groupName": "后排", "groupSortNum": 6, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2412000c6eahws4567.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1n12000c6eaj9265BC.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0q12000c6eaiwkB390.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1g12000c6ealo80B74.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3z12000c6eaiq8658A.jpg?mark=yiche", "cover": ""}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}], "note": "年款/颜色等以门店为准"}], "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=675&app_ver=10.5", "carPhone": true, "vehicleCode": "17561", "style": "2017/18款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4512000elvdljb183C.png?mark=yiche", "fuel": "95号或92号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0o12000c6eaivoD4D0.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2412000c6eahws4567.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2f12000c6eakzuA728.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1n12000c6eaj9265BC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0f12000c6eacfx9063.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "6", "vehiclesSetId": "49", "guidSys": "支持定速巡航", "transmissionName": "自动挡"}, "isFromSearch": false, "timeInterval": 402.86083984375, "specificProductGroups": {"vendorPriceList": [{"vehicleKey": "0_17561_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 463, "vendorDesc": "金义方租车（虹桥店）", "level": "超棒", "maximumRating": 5, "commentLabel": "服务周到", "qExposed": "4.9", "hasComment": 1, "commentCount": 463}, "storeScore": 35.34, "uniqueCode": "aa3e1d4470d340969551156ccafcc8e4", "pickOffFee": 0, "priceInfo": {"oTPrice": 386, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 118, "currentTotalPrice": 386, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 0, "vendorName": "金义方租车", "filterAggregations": [{"groupCode": "BrandGroup_b0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 128, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 11, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 176, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 2.4822, "actId": "", "pickUpFee": 0, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}], "licenseTag": "", "fees": [{"amount": 236, "detail": [{"code": "1001", "amount": 236, "amountDesc": "¥236", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥118", "subAmount": 118, "name": "车辆租金", "amountStr": "¥236"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 386, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥386", "subAmount": 386, "name": "总价", "amountStr": "¥386"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "17561", "newCar": false, "secretBox": false, "sortScore": 0, "cyVendorName": "金义方租车", "vehicleGroup": 4, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "stock": 99999, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 2, "rCType": 2, "vendorVehicleCode": "4876_32543_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 2, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员免费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 386, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 118, "grantedcode": "", "isrec": false, "cvid": 17561, "rentalamount": 236}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD3200", "elct": 0, "pLevel": 2549, "gsDesc": "低价省钱", "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 20096, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_MjU2MTAzXzE3NTYxXzFfMTE4LjBfMjM2LjBfMC4wXzM4Ni4wXzExOC4wXzM4Ni4wXzBfMF8wLjBfMC4wXzEyMC4wXzMwLjBfMF8wXzI0NjUwODcyXzM4MTB8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjIzNi4wfDEwMDM6MzAuMHwxMDAyOjEyMC4w", "alipay": false, "vendorCode": "13079", "productCode": "SD3200_0_256103_256103", "pLev": 358345, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 256103, "age": 30, "rCoup": 0, "kRSId": 256103, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 358345, "pStoreCode": "256103", "pickUpOnDoor": true, "aType": 0, "kVId": 13079, "sortInfo": {"p": "1", "s": "100.0", "c": "2"}, "kVehicleId": 17561, "returnWayInfo": 1, "dropOffOnDoor": true, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "256103", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 24650872, "rLevel": 2549, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": true, "isOrderVehicle": false, "platformCode": "0"}, {"vehicleKey": "0_17561_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 274, "vendorDesc": "怡彤租车", "level": "超棒", "maximumRating": 5, "commentLabel": "取还方便", "qExposed": "5.0", "hasComment": 1, "commentCount": 274}, "storeScore": 63.69, "uniqueCode": "df460d5708764fae9eceb808ead1b1fc", "pickOffFee": 300, "priceInfo": {"oTPrice": 1380, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 340, "currentTotalPrice": 1380, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 1, "vendorName": "怡彤租车", "filterAggregations": [{"groupCode": "BrandGroup_b0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 66, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 496, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 72.2106, "actId": "", "pickUpFee": 300, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}], "licenseTag": "", "fees": [{"amount": 680, "detail": [{"code": "1001", "amount": 680, "amountDesc": "¥680", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥340", "subAmount": 340, "name": "车辆租金", "amountStr": "¥680"}, {"code": "CAR_SERVICE_FEE", "amount": 620, "amountStr": "¥620", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11026", "amount": 300, "amountDesc": "¥300", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 300, "amountDesc": "¥300", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 1380, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥1380", "subAmount": 1380, "name": "总价", "amountStr": "¥1380"}], "vendorLogo": "", "distance": 72.2106, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员收费上门送取车", "ctripVehicleCode": "17561", "newCar": false, "secretBox": false, "sortScore": 1, "cyVendorName": "怡彤租车", "vehicleGroup": 4, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 2, "rCType": 2, "vendorVehicleCode": "20068013", "pickWayInfo": 2, "stockLevel": "E", "pCityId": 2, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员收费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 1380, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 340, "grantedcode": "", "isrec": false, "cvid": 17561, "rentalamount": 680}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD4117", "elct": 0, "pLevel": 1267575, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 2136, "rStoreNav": "店员收费上门送取车", "priceVersion": "SH-PRICEVERSION_MTA3MTI4XzE3NTYxXzFfMzQwXzY4MF8zNDBfMTM4MC4wMF8zNDBfMTM4MC4wXzFfMV8wLjBfMC4wXzgwLjAwXzIwLjAwXzMwMC4wMF8zMDAuMDBfNDY3OTg2Ml8zODEwfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOV8xMDAxOjY4MHwxMDAzOjIwLjAwfDExMDI2OjMwMC4wMHwxMTAyNzozMDAuMDB8MTAwMjo4MC4wMA==", "alipay": false, "vendorCode": "82013", "productCode": "SD4117_0_107128_107128", "pLev": 1267575, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 107128, "age": 30, "rCoup": 0, "kRSId": 107128, "packageId": "", "freeIllegalDeposit": false, "rLev": 1267575, "pStoreCode": "107128", "pickUpOnDoor": true, "aType": 0, "kVId": 82013, "sortInfo": {"p": "2", "s": "3.71", "c": "14", "v": "87057"}, "kVehicleId": 17561, "comPriceCode": "[c]", "dropOffOnDoor": true, "returnWayInfo": 2, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "107128", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 4679862, "rLevel": 1267575, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "platformCode": "10"}, {"vehicleKey": "0_17561_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 131, "vendorDesc": "宝铺租车", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 131}, "storeScore": 78.38, "uniqueCode": "6e7376b65a7747259a4474100c257d62", "pickOffFee": 500, "priceInfo": {"oTPrice": 1701, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 228, "currentTotalPrice": 1701, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 2, "vendorName": "宝铺租车", "filterAggregations": [{"groupCode": "BrandGroup_b0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Vendor_3", "binaryDigit": 33554432, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 66, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 3056, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 149.225, "actId": "", "pickUpFee": 500, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}], "licenseTag": "", "fees": [{"amount": 456, "detail": [{"code": "1001", "amount": 456, "amountDesc": "¥456", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥228", "subAmount": 228, "name": "车辆租金", "amountStr": "¥456"}, {"code": "CAR_SERVICE_FEE", "amount": 1035, "amountStr": "¥1035", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11026", "amount": 500, "amountDesc": "¥500", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 500, "amountDesc": "¥500", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 210, "amountStr": "¥210", "detail": [{"code": "1002", "amount": 210, "amountDesc": "¥210", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 1701, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥1701", "subAmount": 1701, "name": "总价", "amountStr": "¥1701"}], "vendorLogo": "", "distance": 149.225, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员收费上门送取车", "ctripVehicleCode": "17561", "newCar": false, "secretBox": false, "sortScore": 2, "cyVendorName": "宝铺租车", "vehicleGroup": 4, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 2, "rCType": 2, "vendorVehicleCode": "20067812", "pickWayInfo": 2, "stockLevel": "E", "pCityId": 2, "packageLevel": "BAS", "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员收费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 1701, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 228, "grantedcode": "", "isrec": false, "cvid": 17561, "rentalamount": 456}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3472", "elct": 0, "pLevel": 747174, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 1397, "rStoreNav": "店员收费上门送取车", "priceVersion": "SH-PRICEVERSION_MTA2NzA0XzE3NTYxXzFfMjI4XzQ1Nl8yMjhfMTcwMS4wMF8yMjhfMTcwMS4wXzFfMV8wLjBfMC4wXzIxMC4wMF8zNS4wMF81MDAuMDBfNTAwLjAwXzQ2NzY3NTJfMzgxMHwzNTA0fDM1NjN8Mzc0NnwzNzg4fDQyMjlfMTAwMTo0NTZ8MTAwMzozNS4wMHwxMTAyNjo1MDAuMDB8MTEwMjc6NTAwLjAwfDEwMDI6MjEwLjAw", "alipay": false, "vendorCode": "84953", "productCode": "SD3472_0_106704_106704", "pLev": 747174, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 106704, "age": 30, "rCoup": 0, "kRSId": 106704, "packageId": "", "freeIllegalDeposit": false, "rLev": 747174, "pStoreCode": "106704", "pickUpOnDoor": true, "aType": 0, "kVId": 84953, "sortInfo": {"p": "2", "s": "2.16", "c": "213", "v": "92073"}, "kVehicleId": 17561, "comPriceCode": "[c]", "dropOffOnDoor": true, "returnWayInfo": 2, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "106704", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 4676752, "rLevel": 747174, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "platformCode": "10"}, {"vehicleKey": "0_17561_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 71, "vendorDesc": "雪源莱租车", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆干净整洁", "qExposed": "5.0", "hasComment": 1, "commentCount": 71}, "storeScore": 51.77, "uniqueCode": "dd8c962bedaf495c9795a6328c561bd7", "pickOffFee": 400, "priceInfo": {"oTPrice": 1351, "naked": true, "deductInfos": [], "currentOriginalDailyPrice": 0, "priceType": 1, "currentDailyPrice": 208, "currentTotalPrice": 1351, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 3, "vendorName": "雪源莱租车", "filterAggregations": [{"groupCode": "BrandGroup_b0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "HotBrand", "binaryDigit": 32, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 4096, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 66, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2480, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 70.9336, "actId": "", "pickUpFee": 400, "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 1, "sortNum": 40, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "groupId": 3, "labelCode": "3679"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "licenseTag": "", "fees": [{"amount": 416, "detail": [{"code": "1001", "amount": 416, "amountDesc": "¥416", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥208", "subAmount": 208, "name": "车辆租金", "amountStr": "¥416"}, {"code": "CAR_SERVICE_FEE", "amount": 835, "amountStr": "¥835", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11026", "amount": 400, "amountDesc": "¥400", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 400, "amountDesc": "¥400", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 1351, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥1351", "subAmount": 1351, "name": "总价", "amountStr": "¥1351"}], "vendorLogo": "", "distance": 70.9336, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员收费上门送取车", "ctripVehicleCode": "17561", "newCar": false, "secretBox": false, "sortScore": 3, "cyVendorName": "雪源莱租车", "vehicleGroup": 4, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 2, "rCType": 2, "vendorVehicleCode": "20068014", "pickWayInfo": 2, "packageLevel": "BAS", "pCityId": 2, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "店员收费上门送取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 1351, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 208, "grantedcode": "", "isrec": false, "cvid": 17561, "rentalamount": 416}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD6613", "elct": 0, "pLevel": 60703, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": false, "klbPId": 9413, "rStoreNav": "店员收费上门送取车", "priceVersion": "SH-PRICEVERSION_MTE2Njc5XzE3NTYxXzFfMjA4XzQxNl8yMDhfMTM1MS4wMF8yMDhfMTM1MS4wXzFfMV8wLjBfMC4wXzEwMC4wMF8zNS4wMF80MDAuMDBfNDAwLjAwXzUwNTA0ODFfMzgxMHwzNjc5fDM3NDZfMTAwMTo0MTZ8MTAwMzozNS4wMHwxMTAyNjo0MDAuMDB8MTEwMjc6NDAwLjAwfDEwMDI6MTAwLjAw", "alipay": false, "vendorCode": "79391", "productCode": "SD6613_0_116679_116679", "pLev": 60675, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 0, "kPSId": 116679, "age": 30, "rCoup": 0, "kRSId": 116679, "freeIllegalDeposit": false, "rLev": 60675, "pStoreCode": "116679", "pickUpOnDoor": true, "aType": 0, "kVId": 79391, "sortInfo": {"p": "2", "s": "3.91", "c": "14", "v": "81765"}, "kVehicleId": 17561, "comPriceCode": "[c]", "dropOffOnDoor": true, "returnWayInfo": 2, "creditFreeCarDeposit": false, "hot": 0, "labels": [], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "116679", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 5050481, "rLevel": 60703, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "platformCode": "10"}]}, "ResponseStatus": {"Extension": [{"Value": "2498502600187600235", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a6e0e7b-477824-1256030", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1720168279583+0800)/"}, "imStatus": 1, "recommendProducts": [{"groupSort": 4, "lowestPrice": 160, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 320, "detail": [{"code": "1001", "amount": 320, "amountDesc": "¥320", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥160", "subAmount": 160, "name": "车辆租金", "amountStr": "¥320"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 440, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥440", "subAmount": 440, "name": "总价", "amountStr": "¥440"}], "reference": {"vehicleCode": "0", "rStoreCode": "248964", "packageId": "", "pLev": 252871, "comPriceCode": "[c]MzI2fDY4NjZ8MjAyMC4wMC0yMyA0LTA3MDowMDAwOjAmJjEkJjE2MC0wNy0yMDI0MDowMDI0IDAxNjAmOjAwJjEwMDEmMSR8NjAmMyYyJjEwMDMmMjAkMS4wMCYxJjIwMCQxMDIwLjAmNTAuMDImMjAwLjAwMCYxMDI0LTAkfDIzIDEwMDctMjAwJjI6MDA6MDctMjAyNC06MDA6NSAxMDAyNC0wMHwyNSAxNjA3LTA1NgAAOjI4Og==", "bizVendorCode": "SD10666", "pStoreCode": "248964", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MjQ4OTY0XzUyODNfMV8xNjBfMzIwXzE2MF80NDAuMDBfMTYwXzQ0MC4wXzBfMF8wLjBfMC4wXzEwMC4wMF8yMC4wMF8wLjAwXzAuMDBfNjg2NjMyNg==", "sendTypeForPickUpCar": 0, "skuId": 6866326, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 252871, "vendorCode": "15003615", "vendorVehicleCode": "20073341"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1960836, "bizVendorCode": "SD5482"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1856518, "bizVendorCode": "SD4042"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6870203, "bizVendorCode": "SD9741"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1964328, "bizVendorCode": "SD6554"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1904886, "bizVendorCode": "SD3149"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1904854, "bizVendorCode": "SD3167"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 6741320, "bizVendorCode": "SD4247"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1925258, "bizVendorCode": "SD4444"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1923831, "bizVendorCode": "SD4431"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1839066, "bizVendorCode": "SD3347"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44193759, "bizVendorCode": "SD4894"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5417086, "bizVendorCode": "SD9356"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4015420, "bizVendorCode": "SD7283"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1918321, "bizVendorCode": "SD4400"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5113019, "bizVendorCode": "SD8832"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1977193, "bizVendorCode": "SD6788"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6397831, "bizVendorCode": "SD8630"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 43827358, "bizVendorCode": "SD12840"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 23778203, "bizVendorCode": "SD11569"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1918931, "bizVendorCode": "SD4937"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 42993371, "bizVendorCode": "SD12545"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1837980, "bizVendorCode": "SD3412"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4390295, "bizVendorCode": "SD8013"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1859747, "bizVendorCode": "SD3703"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 40574619, "bizVendorCode": "SD9559"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2694569, "bizVendorCode": "SD4599"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1863723, "bizVendorCode": "SD3616"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1976605, "bizVendorCode": "SD6498"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2526028, "bizVendorCode": "SD4938"}}], "pWay": "可选：免费站内取还车", "vehicleCode": "5283", "highestPrice": 999, "vehicleKey": "0_5283_沪牌", "minDPrice": 160, "hot": 0, "minTPrice": 440, "lowestDistance": 0, "group": 87, "type": 0, "groupTypeBits": 2, "sortNum": 5, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD10666_0_248964_248964"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 2551, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "162858393", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 30}, {"groupSort": 1, "lowestPrice": 110, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 220, "detail": [{"code": "1001", "amount": 220, "amountDesc": "¥220", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥110", "subAmount": 110, "name": "车辆租金", "amountStr": "¥220"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 160, "amountStr": "¥160", "detail": [{"code": "1002", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 415, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥415", "subAmount": 415, "name": "总价", "amountStr": "¥415"}], "reference": {"vehicleCode": "0", "rStoreCode": "117737", "packageId": "", "pLev": 63590, "comPriceCode": "[c]NTkyfDI3MDZ8MjAyMC4wMC0yMyA0LTA3MDowMDAwOjAmJjEkJjExMC0wNy0yMDI0MDowMDI0IDAxMTAmOjAwJjEwMDEmMSR8MTAmMiYyJjEwMDMmMjAkMS4wMCYxJjM1MCQxMDM1LjAmODAuMDImMjYwLjAwMCYxMDI0LTAkfDIzIDEwMDctMjAwJjI6MDA6MDctMjAyNC06MDA6NSAxMDAyNC0wMHwyNSAxNjA3LTA1NgAAOjI4Og==", "bizVendorCode": "SD5673", "pStoreCode": "117737", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTE3NzM3XzU1OTFfMV8xMTBfMjIwXzExMF80MTUuMDBfMTEwXzQxNS4wXzBfMF8wLjBfMC4wXzE2MC4wMF8zNS4wMF8wLjAwXzAuMDBfMjcwNjU5Mg==", "sendTypeForPickUpCar": 0, "skuId": 2706592, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 63590, "vendorCode": "85251", "vendorVehicleCode": "20096478"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5670828, "bizVendorCode": "SD8718"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5190262, "bizVendorCode": "SD4845"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 5589337, "bizVendorCode": "SD4446"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6396032, "bizVendorCode": "SD10391"}}], "pWay": "可选：店员免费上门送取车", "vehicleCode": "5591", "highestPrice": 220, "vehicleKey": "0_5591_", "minDPrice": 110, "hot": 0, "minTPrice": 415, "lowestDistance": 40.0619, "group": 105591, "type": 0, "groupTypeBits": 1, "sortNum": 18, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD5673_0_117737_117737"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 369, "productRef": {"license": "外牌", "licenseType": 1, "licenseTag": "", "licenseStyle": "6"}, "hotScore": 0, "hotType": 0, "reactId": "162858396", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 5}, {"groupSort": 2, "lowestPrice": 58, "modifySameVehicle": false, "vendorPriceList": [{"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1970976, "bizVendorCode": "SD5673"}}, {"fees": [{"amount": 276, "detail": [{"code": "1001", "amount": 276, "amountDesc": "¥276", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥138", "subAmount": 138, "name": "车辆租金", "amountStr": "¥276"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 431, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥431", "subAmount": 431, "name": "总价", "amountStr": "¥431"}], "reference": {"vehicleCode": "0", "rStoreCode": "410999", "pLev": 1247331, "comPriceCode": "[c]ODM4NTM0NDgyNC0wfHwyMCAwMDo3LTIzMCYxMzAwOjAkMjAyOCYmMS0yNCA0LTA3MDowMDAwOjAmJjEkJjEzODEmMiZ8MTAwMjc2JDEzOCYmMSYzMTAwMyYzNS41LjAwMDAyJjAwJDEuMDAmMiY2MDAwJHwxMjAuLTA3LTIwMjQwOjAwMjMgMTIwMjQ6MDAmMjUgMS0wNy06MDB8MDowMC0wNy0yMDI0NjoyODA1IDEAAAAAOjU2AA==", "bizVendorCode": "SD12066", "pStoreCode": "410999", "packageType": 0, "priceVersion": "SH-PRICEVERSION_NDEwOTk5XzU1OTFfMV8xMzhfMjc2XzEzOF80MzEuMDBfMTM4XzQzMS4wXzBfMF8wLjBfMC4wXzEyMC4wMF8zNS4wMF8wLjAwXzAuMDBfMzQ0ODgzODU=", "sendTypeForPickUpCar": 0, "skuId": 34488385, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1247331, "vendorCode": "15004890", "vendorVehicleCode": "0"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 8152250, "bizVendorCode": "SD11150"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 45612183, "bizVendorCode": "SD13367"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 5643675, "bizVendorCode": "SD9690"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2503563, "bizVendorCode": "SD7112"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 6934566, "bizVendorCode": "SD10583"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 6433878, "bizVendorCode": "SD9438"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6902109, "bizVendorCode": "SD7089"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2707753, "bizVendorCode": "SD4454"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6862887, "bizVendorCode": "SD10388"}}], "pWay": "可选：店员免费上门送取车", "vehicleCode": "5591", "highestPrice": 335, "vehicleKey": "0_5591_沪牌", "minDPrice": 138, "hot": 0, "minTPrice": 431, "lowestDistance": 0, "group": 105591, "type": 0, "groupTypeBits": 1, "sortNum": 19, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD9690_0_192890_192890"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 3419, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "162858397", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 11}, {"groupSort": 1, "lowestPrice": 140, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 280, "detail": [{"code": "1001", "amount": 280, "amountDesc": "¥280", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥140", "subAmount": 140, "name": "车辆租金", "amountStr": "¥280"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 415, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥415", "subAmount": 415, "name": "总价", "amountStr": "¥415"}], "reference": {"vehicleCode": "0", "rStoreCode": "256103", "packageId": "sec", "pLev": 358345, "bizVendorCode": "SD3200", "pStoreCode": "256103", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MjU2MTAzXzQ2NTlfMV8xNDAuMF8yODAuMF8wLjBfNDE1LjBfMTQwLjBfNDE1LjBfMF8wXzAuMF8wLjBfMTAwLjBfMzUuMF8wXzBfMjkzOTY3MTE=", "sendTypeForPickUpCar": 0, "skuId": 29396711, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 358345, "vendorCode": "13079", "vendorVehicleCode": "7124_32542_hupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 51448328, "bizVendorCode": "SD4101"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 23250865, "bizVendorCode": "SD7256"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 4877456, "bizVendorCode": "SD4206"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1910585, "bizVendorCode": "SD4392"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1915335, "bizVendorCode": "SD4399"}}], "pWay": "可选：店员免费上门送取车", "vehicleCode": "4659", "highestPrice": 600, "vehicleKey": "0_4659_沪牌", "minDPrice": 140, "hot": 0, "minTPrice": 415, "lowestDistance": 13.1156, "group": 82, "type": 0, "groupTypeBits": 2, "sortNum": 34, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD3200_0_256103_256103"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 467, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "162858399", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 6}], "checkResponseTime": 1720168279462.156, "checkRequestTime": 1720168279059.2952, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 580, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 580, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1720168279057, "afterFetch": 1720168279637}}