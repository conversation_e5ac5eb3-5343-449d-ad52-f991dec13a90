{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1697177631451+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "8811546527332758386"}, {"Id": "RootMessageId", "Value": "921822-0a3abbaa-471438-275605"}]}, "vehicleInfo": {"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "4139", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.0T-1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0scn143fai2ah4w9B69.jpg"], "vedio": "https://video.c-ctrip.com/videos/R40f27000001i5l8m91B0.mp4", "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0414k120008at15l04B7B.jpg", "https://dimg04.c-ctrip.com//images/04139120008at3x402339.jpg", "https://dimg04.c-ctrip.com//images/0416n120008at1b3t7DE9.jpg", "https://dimg04.c-ctrip.com//images/04135120008at4it05F01.jpg", "https://dimg04.c-ctrip.com//images/04142120008at10cy3FB8.jpg", "https://dimg04.c-ctrip.com//images/0413g120008at239eCF3C.jpg", "https://dimg04.c-ctrip.com//images/04171120008at46pr9E1E.jpg", "https://dimg04.c-ctrip.com//images/0415b120008at1hgaA2A9.jpg", "https://dimg04.c-ctrip.com//images/0414k120008at2f9v4B12.jpg", "https://dimg04.c-ctrip.com//images/0414w120008at1b3z48A6.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png", "sourcePicInfos": [{"source": 0, "type": 2, "sourceName": "年款/颜色等以实物为准", "picList": [{"imageUrl": "https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png", "sortNum": 0}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0414k120008at15l04B7B.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com//images/04139120008at3x402339.jpg", "sortNum": 2}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0416n120008at1b3t7DE9.jpg", "sortNum": 3}, {"imageUrl": "https://dimg04.c-ctrip.com//images/04135120008at4it05F01.jpg", "sortNum": 4}, {"imageUrl": "https://dimg04.c-ctrip.com//images/04142120008at10cy3FB8.jpg", "sortNum": 5}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0413g120008at239eCF3C.jpg", "sortNum": 6}, {"imageUrl": "https://dimg04.c-ctrip.com//images/04171120008at46pr9E1E.jpg", "sortNum": 7}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0415b120008at1hgaA2A9.jpg", "sortNum": 8}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0414k120008at2f9v4B12.jpg", "sortNum": 9}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0414w120008at1b3z48A6.jpg", "sortNum": 10}]}], "oilType": 3, "cover": "https://dimg04.c-ctrip.com/images/0AS33120009sqadenADAB.png", "fuelType": "汽油", "luggageNum": "可放5个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "vehiclesSetId": "69"}, "specificProductGroups": {"vendorPriceList": [{"vendorName": "桐叶租车", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "凤凰机场店", "commentCount": 38094, "qCommentCount": 38094, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 33, "currentOriginalDailyPrice": 38, "oTPrice": 360, "currentTotalPrice": 331, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 29, "payofftype": 2}]}, "reference": {"bizVendorCode": "13031", "vendorCode": "13031", "pStoreCode": "78", "rStoreCode": "78", "vehicleCode": "0", "packageId": "Secure", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "13031_0_78_78", "comPriceCode": "eyJzZWxsZXJpZCI6MTEwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6Mjc2MSwiY2FydHlwZWlkIjoxODUzNiwidG90YWwiOjM2MCwidGltZSI6MTY5NzE3NzYyN30=", "priceVersion": "SH-PRICEVERSION_NzhfNDEzOV8xXzM4LjBfMTkwLjBfMC4wXzM2MC4wXzMzLjBfMzMxLjBfMF8wXzAuMF8wLjBfMTUwLjBfMjAuMF8wXzBfMTUyNTM5", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "18536", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "已减29", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 360, "isrec": false, "recommendOrder": 0, "mergeId": 1216, "rectype": 1, "cvid": 4139, "rentalamount": 161, "totalDailyPrice": 33, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "18536", "storeId": "78"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 2, "rRc": 2, "skuId": 152539, "klbPId": 1, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 2761, "rLevel": 2761, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 78, "kRSId": 78, "kVId": 13031, "pLev": 129, "rLev": 129, "klbVersion": 1, "kVehicleId": 4139, "adjustRuleId": ""}, "sortNum": 0, "pStoreRouteDesc": "免费站内取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 70, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "长租特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "已减29", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}], "sortScore": 0, "storeScore": 98.96, "isSelect": true, "stock": 99999, "distance": 0, "rDistance": 0.2888, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "桐叶租车", "card": 0, "ctripVehicleCode": "4139", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 161, "amountStr": "¥161", "subAmount": 33, "subAmountStr": "日均¥33", "originalDailyPrice": 38, "detail": [{"code": "1001", "name": "租车费", "amount": 190, "amountDesc": "¥190"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 150, "amountStr": "¥150", "detail": [{"code": "1002", "name": "基础服务费", "amount": 150, "amountDesc": "¥150", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 331, "amountStr": "¥331", "subAmount": 360, "subAmountStr": "¥360", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "c898d940aa88444db0f90acda30dbd84", "licenseTag": "", "newCar": false, "vehicleGroup": 2, "secretBox": false}, {"vendorName": "懒人行租车", "isMinTPriceVendor": true, "vendorLogo": "https://dimg04.c-ctrip.com/images/0yc3w12000a7k3pvyF9CF.jpg", "commentInfo": {"level": "超棒", "vendorDesc": "三亚—机场店", "commentCount": 43456, "qCommentCount": 43456, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 33, "currentOriginalDailyPrice": 38, "oTPrice": 360, "currentTotalPrice": 331, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 29, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4201", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4201_0_114043_114043", "comPriceCode": "[c]NDIyfDE5MTJ8MjAyMC4wMC0xMyAzLTEwMDowMDAwOjAmMSQyJjM4JjEwLTEwMjMtOjAwOjQgMDA4JiYxMDAmMzMtMTAkMjAyMDA6MC0xNSAmMzgmMDowMDAyMy0mMSQyNiAwMDEwLTEwMCYzOjAwOiQyMDI4JiYxLTE3IDMtMTAwOjAwMDA6MCYxJHwmMzgmJjUmMzEwMDEwJDEwOCYxOSYyMC4wMyYxMC4wMDAwJjIyJjUmJDEwMDAmMTUzMC4wJHwyMDAuMDAwLTEzMjMtMTEzOjQgMTQ6AAAAADcAAAA=", "priceVersion": "SH-PRICEVERSION_MTE0MDQzXzQxMzlfMV8zOF8xOTBfMzhfMzYwLjAwXzMzLjBfMzMxLjBfMF8wXzAuMF8wLjBfMTUwLjAwXzIwLjAwXzBfMC4wMF8xOTEyNDIy", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20038826", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "已减29", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": false, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 360, "isrec": false, "recommendOrder": 0, "mergeId": 1216, "rectype": 1, "cvid": 4139, "rentalamount": 161, "totalDailyPrice": 33, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20038826", "storeId": "114043"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "精选好货", "pRc": 2, "rRc": 2, "skuId": 1912422, "klbPId": 4156, "klb": 1, "pCType": -1, "rCType": 2, "pLevel": -1, "rLevel": 6947, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43", "v": "1966"}, "newEnergy": 0, "platform": 10, "kPSId": 114043, "kRSId": 114043, "kVId": 30164, "pLev": -1, "rLev": 40845, "klbVersion": 1, "kVehicleId": 4139, "stockLevel": "D", "adjustRuleId": ""}, "sortNum": 1, "pStoreRouteDesc": "免费站内取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 70, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "长租特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "已减29", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 112, "checkType": 0}], "sortScore": 1, "storeScore": 98.9, "isSelect": true, "distance": 0.0576, "rDistance": 0.0576, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "懒人行租车", "card": 0, "ctripVehicleCode": "4139", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 161, "amountStr": "¥161", "subAmount": 33, "subAmountStr": "日均¥33", "originalDailyPrice": 38, "detail": [{"code": "1001", "name": "租车费", "amount": 190, "amountDesc": "¥190"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 150, "amountStr": "¥150", "detail": [{"code": "1002", "name": "基础服务费", "amount": 150, "amountDesc": "¥150", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 331, "amountStr": "¥331", "subAmount": 360, "subAmountStr": "¥360", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "00e4ae1a75474727a934b04d1442df1f", "licenseTag": "", "newCar": false, "vehicleGroup": 2, "secretBox": false}, {"vendorName": "骑仕租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 37823, "qCommentCount": 37823, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 33, "currentOriginalDailyPrice": 38, "oTPrice": 360, "currentTotalPrice": 331, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 29, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "rStoreCode": "188084", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_188084_188084", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzQwMTRfMV8zOC4wXzE5MC4wXzAuMF8zNjAuMF8zMy4wXzMzMS4wXzBfMF8wLjBfMC4wXzE1MC4wXzIwLjBfMF8wXzUzNTQwMDk=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "445_13470_pupai", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "已减29", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 360, "isrec": false, "recommendOrder": 0, "mergeId": 1216, "rectype": 1, "cvid": 4139, "rentalamount": 161, "totalDailyPrice": 33, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "445_13470_pupai", "storeId": "188084"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "低价省钱", "pRc": 2, "rRc": 2, "skuId": 5354009, "klbPId": 17694, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 872, "rLevel": 872, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 188084, "kRSId": 188084, "kVId": 13094, "pLev": 169518, "rLev": 169518, "klbVersion": 1, "kVehicleId": 4014, "adjustRuleId": ""}, "sortNum": 2, "pStoreRouteDesc": "免费站内取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3547", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 70, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "长租特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "已减29", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "CarAge", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 112, "checkType": 0}], "sortScore": 2, "storeScore": 96.77, "isSelect": true, "stock": 99999, "distance": 0, "rDistance": 0.2347, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "4139", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 161, "amountStr": "¥161", "subAmount": 33, "subAmountStr": "日均¥33", "originalDailyPrice": 38, "detail": [{"code": "1001", "name": "租车费", "amount": 190, "amountDesc": "¥190"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 150, "amountStr": "¥150", "detail": [{"code": "1002", "name": "基础服务费", "amount": 150, "amountDesc": "¥150", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 331, "amountStr": "¥331", "subAmount": 360, "subAmountStr": "¥360", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "8ca5d831a68448288ce84dae8eb6963e", "licenseTag": "", "newCar": false, "vehicleGroup": 2, "secretBox": false}, {"vendorName": "EVCARD租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "EVCARD三亚凤凰机场店", "commentCount": 2436, "qCommentCount": 2436, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 35, "currentOriginalDailyPrice": 38, "oTPrice": 360, "currentTotalPrice": 344, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 16, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3047", "vendorCode": "13071", "pStoreCode": "101002", "rStoreCode": "101002", "vehicleCode": "0", "packageId": "Secure", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3047_0_101002_101002", "comPriceCode": "eyJzZWxsZXJpZCI6MjA2OTYsInBhY2thZ2VpZCI6IlNlY3VyZSIsInN0b3JlaWQiOjEwMjEzLCJjYXJ0eXBlaWQiOjE4OTY2LCJ0b3RhbCI6MzYwLCJ0aW1lIjoxNjk3MTc3NjI3fQ==", "priceVersion": "SH-PRICEVERSION_MTAxMDAyXzQxMzlfMV8zOC4wXzE5MC4wXzAuMF8zNjAuMF8zNS4wXzM0NC4wXzBfMF8wLjBfMC4wXzE1MC4wXzIwLjBfMF8wXzEyMDkyNTc=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "18966", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "铂金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3641", "groupCode": "MarketGroup1320", "amountTitle": "已减16", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 360, "isrec": false, "recommendOrder": 0, "mergeId": 1216, "rectype": 1, "cvid": 4139, "rentalamount": 174, "totalDailyPrice": 35, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "18966", "storeId": "101002"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 1209257, "klbPId": 313, "klb": 1, "pCType": 1, "rCType": 1, "pLevel": 10213, "rLevel": 10213, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "16", "s": "90.74", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 101002, "kRSId": 101002, "kVId": 13071, "pLev": 13224, "rLev": 13224, "klbVersion": 1, "kVehicleId": 4139, "adjustRuleId": ""}, "sortNum": 3, "pStoreRouteDesc": "免费站内取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 70, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "铂金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3641", "groupCode": "MarketGroup1320", "amountTitle": "已减16", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 64, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 5, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 48, "checkType": 0}], "sortScore": 3, "storeScore": 98.39, "isSelect": true, "stock": 99999, "distance": 0.1, "rDistance": 0.1826, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3641", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "EVCARD租车", "card": 0, "ctripVehicleCode": "4139", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 174, "amountStr": "¥174", "subAmount": 35, "subAmountStr": "日均¥35", "originalDailyPrice": 38, "detail": [{"code": "1001", "name": "租车费", "amount": 190, "amountDesc": "¥190"}, {"code": "3641", "name": "铂金贵宾", "amount": 16, "amountDesc": "¥16"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 150, "amountStr": "¥150", "detail": [{"code": "1002", "name": "基础服务费", "amount": 150, "amountDesc": "¥150", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 344, "amountStr": "¥344", "subAmount": 360, "subAmountStr": "¥360", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "99e67bfef45941b99b94486986624c97", "licenseTag": "", "newCar": false, "vehicleGroup": 2, "secretBox": false}, {"vendorName": "器车出行", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "三亚仝和", "commentCount": 574, "qCommentCount": 574, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 35, "currentOriginalDailyPrice": 38, "oTPrice": 360, "currentTotalPrice": 344, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 16, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4701", "vendorCode": "74573", "pStoreCode": "114529", "rStoreCode": "114529", "vehicleCode": "0", "packageId": "", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4701_0_114529_114529", "comPriceCode": "[c]NjIyfDE5MTF8MjAyMC4wMC0xMyAzLTEwMDowMDAwOjAmMSQyJjM4JjEwLTEwMjMtOjAwOjQgMDA4JiYxMDAmMzMtMTAkMjAyMDA6MC0xNSAmMzgmMDowMDAyMy0mMSQyNiAwMDEwLTEwMCYzOjAwOiQyMDI4JiYxLTE3IDMtMTAwOjAwMDA6MCYxJHwmMzgmJjUmMzEwMDEwJDEwOCYxOSYyMC4wMyYxMC4wMDAwJjIyJjUmJDEwMDAmMTUzMC4wJHwyMDAuMDAwLTEzMjMtMTEzOjQgMTQ6AAAAADcAAAA=", "priceVersion": "SH-PRICEVERSION_MTE0NTI5XzQxMzlfMV8zOF8xOTBfMzhfMzYwLjAwXzM1LjBfMzQ0LjBfMF8wXzAuMF8wLjBfMTUwLjAwXzIwLjAwXzBfMF8xOTExNjIy", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20038826", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "铂金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3641", "groupCode": "MarketGroup1320", "amountTitle": "已减16", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费接您至门店取还车", "rStoreNav": "店员免费接您至门店取还车", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 4, "returnWayInfo": 4, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 360, "isrec": false, "recommendOrder": 0, "mergeId": 1216, "rectype": 1, "cvid": 4139, "rentalamount": 174, "totalDailyPrice": 35, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20038826", "storeId": "114529"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 1911622, "klbPId": 4997, "klb": 1, "pCType": 1, "rCType": 1, "pLevel": 97105, "rLevel": 97105, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "4", "s": "90.74", "c": "43", "v": "76099"}, "newEnergy": 0, "platform": 10, "kPSId": 114529, "kRSId": 114529, "kVId": 74573, "pLev": 97105, "rLev": 97105, "klbVersion": 1, "kVehicleId": 4139, "stockLevel": "D", "adjustRuleId": ""}, "sortNum": 4, "pStoreRouteDesc": "店员免费接您至门店取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "铂金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3641", "groupCode": "MarketGroup1320", "amountTitle": "已减16", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 64, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 32, "checkType": 0}], "sortScore": 4, "storeScore": 98.78, "isSelect": false, "distance": 0.1, "rDistance": 3.1007, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3641", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "器车出行", "card": 0, "ctripVehicleCode": "4139", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 174, "amountStr": "¥174", "subAmount": 35, "subAmountStr": "日均¥35", "originalDailyPrice": 38, "detail": [{"code": "1001", "name": "租车费", "amount": 190, "amountDesc": "¥190"}, {"code": "3641", "name": "铂金贵宾", "amount": 16, "amountDesc": "¥16"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 150, "amountStr": "¥150", "detail": [{"code": "1002", "name": "基础服务费", "amount": 150, "amountDesc": "¥150", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 344, "amountStr": "¥344", "subAmount": 360, "subAmountStr": "¥360", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "38e3d88c13cd42a789c650c79999cd83", "licenseTag": "", "newCar": false, "vehicleGroup": 2, "secretBox": false}, {"vendorName": "明昊租车", "isMinTPriceVendor": false, "vendorLogo": "https://ak-d.tripcdn.com/images/0yc2l12000bzfo23oCC4C.png", "commentInfo": {"level": "超棒", "vendorDesc": "明昊凤凰机场店", "commentCount": 5354, "qCommentCount": 5354, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 33, "currentOriginalDailyPrice": 38, "oTPrice": 360, "currentTotalPrice": 331, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 29, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3012", "vendorCode": "13032", "pStoreCode": "2961", "rStoreCode": "2961", "vehicleCode": "0", "packageId": "Secure", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3012_0_2961_2961", "comPriceCode": "eyJzZWxsZXJpZCI6MTIwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6ODUzNiwiY2FydHlwZWlkIjoxODUzNiwidG90YWwiOjM2MCwidGltZSI6MTY5NzE3NzYyN30=", "priceVersion": "SH-PRICEVERSION_Mjk2MV80MTM5XzFfMzguMF8xOTAuMF8wLjBfMzYwLjBfMzMuMF8zMzEuMF8wXzBfMC4wXzAuMF8xNTAuMF8yMC4wXzBfMF8yMjc5Nzc=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "18536", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "已减29", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 360, "isrec": false, "recommendOrder": 0, "mergeId": 1216, "rectype": 1, "cvid": 4139, "rentalamount": 161, "totalDailyPrice": 33, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "18536", "storeId": "2961"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 227977, "klbPId": 6, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8536, "rLevel": 8536, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 2961, "kRSId": 2961, "kVId": 13032, "pLev": 1032, "rLev": 1032, "klbVersion": 1, "kVehicleId": 4139, "adjustRuleId": ""}, "sortNum": 5, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "长租特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "已减29", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 16, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 4, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}], "sortScore": 5, "storeScore": 99.27, "isSelect": false, "stock": 99999, "distance": 0, "rDistance": 0.2888, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "明昊租车", "card": 0, "ctripVehicleCode": "4139", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 161, "amountStr": "¥161", "subAmount": 33, "subAmountStr": "日均¥33", "originalDailyPrice": 38, "detail": [{"code": "1001", "name": "租车费", "amount": 190, "amountDesc": "¥190"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 150, "amountStr": "¥150", "detail": [{"code": "1002", "name": "基础服务费", "amount": 150, "amountDesc": "¥150", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 331, "amountStr": "¥331", "subAmount": 360, "subAmountStr": "¥360", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "e38d2d18f9e54b02a2be8e082f3bc7a0", "licenseTag": "", "newCar": false, "vehicleGroup": 2, "secretBox": false}, {"vendorName": "车游天下租车", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 2490, "qCommentCount": 2490, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5, "hasComment": 1}, "priceInfo": {"currentDailyPrice": 35, "currentOriginalDailyPrice": 38, "oTPrice": 360, "currentTotalPrice": 344, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 16, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD7763", "vendorCode": "13067", "pStoreCode": "67462", "rStoreCode": "67462", "vehicleCode": "0", "packageId": "XC2023101314134769263", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD7763_0_67462_67462", "comPriceCode": "023acf3d-c081-42c3-8a80-702761a9eed1", "priceVersion": "SH-PRICEVERSION_Njc0NjJfNDEzOV8xXzM4LjBfMTkwLjBfMC4wXzM2MC4wXzM1LjBfMzQ0LjBfMF8wXzAuMF8wLjBfMTUwLjBfMjAuMF8wLjBfMC4wXzc4NzAxMw==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "18536", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "铂金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3641", "groupCode": "MarketGroup1320", "amountTitle": "已减16", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 360, "isrec": false, "recommendOrder": 0, "mergeId": 1216, "rectype": 1, "cvid": 4139, "rentalamount": 174, "totalDailyPrice": 35, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "18536", "storeId": "67462"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 787013, "klbPId": 119, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 20220801, "rLevel": 20220801, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "90.74", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 67462, "kRSId": 67462, "kVId": 13067, "pLev": 81381, "rLev": 81381, "klbVersion": 1, "kVehicleId": 4139, "adjustRuleId": ""}, "sortNum": 6, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "铂金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3641", "groupCode": "MarketGroup1320", "amountTitle": "已减16", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_x0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 32, "checkType": 0}, {"groupCode": "DriveAge", "binaryDigit": 6, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 60, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 240, "checkType": 0}], "sortScore": 6, "storeScore": 95.6, "isSelect": false, "stock": 99999, "distance": 0, "rDistance": 2.79, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3641", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "车游天下租车", "card": 0, "ctripVehicleCode": "4139", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 174, "amountStr": "¥174", "subAmount": 35, "subAmountStr": "日均¥35", "originalDailyPrice": 38, "detail": [{"code": "1001", "name": "租车费", "amount": 190, "amountDesc": "¥190"}, {"code": "3641", "name": "铂金贵宾", "amount": 16, "amountDesc": "¥16"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 150, "amountStr": "¥150", "detail": [{"code": "1002", "name": "基础服务费", "amount": 150, "amountDesc": "¥150", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 344, "amountStr": "¥344", "subAmount": 360, "subAmountStr": "¥360", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "4125bbbac1fc4840880c73f0d9a327ac", "licenseTag": "", "newCar": false, "vehicleGroup": 2, "secretBox": false}]}, "filteredProductGroups": {"vendorPriceList": []}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "isFromSearch": false, "promotMap": {}, "extras": {"abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B", "isNewLicensePlate": "0", "serverRequestId": "950asxFzD9gE6w347vZ5", "commodityClass2Version": "1", "rSelect": "1"}, "imStatus": 1}