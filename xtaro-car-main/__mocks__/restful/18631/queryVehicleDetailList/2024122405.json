{"includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "productGroupCodeUesd": "2", "shareVehicleInfo": {"doorNo": 4, "displacement": "1.0T-1.5L", "style": "", "license": "", "minCurrentDailyPrice": 51, "vehicleImage": "https://dimg04.c-ctrip.com/images/0RV5i12000c7kmh93066A.png?mark=yiche", "groupName": "雪佛兰科沃兹", "transmissionType": 1, "passengerNo": 5, "vehicleName": "雪佛兰科沃兹", "transmissionName": "自动挡"}, "priceExplain": {"title": "价格说明", "content": ["划线价格为参考价，该价格指商品或服务的门市价、服务提供商的指导价、零售价或该商品或服务曾经展示过的销售价等并非原价；由于产品信息实时更新、市场价格波动等可能会与您预订时展示的不一致，该价格仅供您参考。", "未划线价格指商品或服务的实时标价，为划线价基础上计算出的优惠金额。具体成交价格根据商品或服务参加活动，或会员使用优惠券等发生变化，最终以订单结算页价格为准。", "如有疑问，您可在预订前联系客服进行咨询。", "此说明仅当出现价格比较时有效。若服务提供商单独对划线价格进行说明的，以服务提供商的表述为准。 "]}, "recommendVehicleList": [{"skylight": "部分车辆支持", "vehicleKey": "0_5407_", "luggageNo": 2, "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "displacement": "1.2T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "丰田卡罗拉（23款及以前）", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "carPhone": true, "vehicleCode": "5407", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田卡罗拉（23款及以前）", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0e12000c5s92ry5659.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000c5s9frq4F26.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0k12000c5s94goBD02.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1p12000c5s9fry9DF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6o12000c5s97iu8E9C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "70", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "2_5407_", "luggageNo": 2, "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "displacement": "1.2T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "丰田卡罗拉（23款及以前）", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "carPhone": true, "vehicleCode": "5407", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田卡罗拉（23款及以前）", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4m12000c55lln0436B.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0e12000c5s92ry5659.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000c5s9frq4F26.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0k12000c5s94goBD02.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1p12000c5s9fry9DF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6o12000c5s97iu8E9C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "70", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_1069_", "luggageNo": 5, "carPlay": "不支持", "displacement": "1.4T-1.6L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0a12000c7kmcp84670.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "大众朗逸", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD", "mediaTypes": [], "vehicleCode": "1069", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "大众朗逸", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0m12000chlv9o2C40D.jpg?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0312000chlvgr2CE91.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1u12000chlvgyp97DC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000chlv9fkFF40.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000chlv9ft6840.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2612000chlvh5q2C37.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "70", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "2_1069_", "luggageNo": 5, "carPlay": "不支持", "displacement": "1.4T-1.6L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0a12000c7kmcp84670.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "大众朗逸", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD", "mediaTypes": [], "vehicleCode": "1069", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "大众朗逸", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0m12000chlv9o2C40D.jpg?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0312000chlvgr2CE91.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1u12000chlvgyp97DC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000chlv9fkFF40.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000chlv9ft6840.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2612000chlvh5q2C37.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "70", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_2050_", "luggageNo": 2, "carPlay": "部分车辆支持原厂互联/映射/CarPlay/CarLife/HUAWEIHiCar", "displacement": "1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4c12000c8kfbvjE999.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "吉利帝豪", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX/HDMI", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5359&app_ver=10.5", "vehicleCode": "2050", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "吉利帝豪", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4c12000c8kfbvjE999.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV2r12000cgh5qtz2592.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3p12000cgh5n8fE51B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0f12000cgh5rd2EC32.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5h12000cgh5n8j3D50.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2912000cgh5hxn6A27.jpg?mark=yiche"], "transmissionType": 1, "brandName": "吉利汽车", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "吉利汽车", "licenseStyle": "2", "vehiclesSetId": "70", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_1466_", "luggageNo": 2, "carPlay": "部分车辆支持CarLife/原厂互联/映射", "displacement": "1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0s12000c55lx6v372E.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "丰田威驰", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=888&app_ver=10.5", "vehicleCode": "1466", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "丰田威驰", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4s12000befw3if9F33.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1112000c5gqv5jADBF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3z12000c5gr1b217AD.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV7212000c7ly7aqD51F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6612000c5gr4jj7A1D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3512000c5gqwty7B4F.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "70", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}], "resBodySize": 130728, "promotMap": {}, "filteredProductGroups": {"vendorPriceList": []}, "vehicleInfo": {"skylight": "部分车辆支持", "vehicleKey": "0_4139_", "luggageNo": 5, "carPlay": "部分车辆支持原厂互联/映射/CarLife/CarPlay", "displacement": "1.0T-1.5L", "autoPark": false, "charge": "", "fuelType": "汽油", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5i12000c7kmh93066A.png?mark=yiche"], "license": "", "isSpecialized": true, "multimediaAlbums": [{"albumName": "官方相册", "albumType": 1, "mediaGroup": [{"groupType": 2, "groupName": "视频", "groupSortNum": 2, "medias": [{"sortNum": 0, "type": 2, "url": "https://video.c-ctrip.com/videos/R40f27000001i5l8m91B0.mp4?mark=yiche", "cover": "https://dimg04.c-ctrip.com/images/0RV0612000d5pibur948E.png"}]}, {"groupType": 3, "groupName": "VR", "groupSortNum": 3, "medias": [{"sortNum": 0, "type": 3, "url": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "cover": "https://dimg04.c-ctrip.com/images/0RV5i12000c7kmh93066A.png?mark=yiche"}]}, {"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV2212000c7mwn2zE222.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV7212000c7mwoic1D7B.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1512000c7mwn2h32E0.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6312000c7mwo5d6170.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4a12000c7mwln30AA4.jpg?mark=yiche", "cover": ""}]}, {"groupType": 5, "groupName": "前排", "groupSortNum": 5, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5212000c7mwubp7B39.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1212000c7mwvvc7936.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3012000c7mwv0sCA00.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV4u12000c7mww2dBA58.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV5z12000c7mwuun4E9B.jpg?mark=yiche", "cover": ""}]}, {"groupType": 6, "groupName": "后排", "groupSortNum": 6, "medias": [{"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV0l12000c7mwy55EE30.jpg?mark=yiche", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV3212000c7mx6u22402.jpg?mark=yiche", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV1812000c7mx63oDDA7.jpg?mark=yiche", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6r12000c7mx4z63FA4.jpg?mark=yiche", "cover": ""}, {"sortNum": 5, "type": 1, "url": "https://dimg04.c-ctrip.com/images/0RV6s12000c7mx0gu6818.jpg?mark=yiche", "cover": ""}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}], "note": "年款/颜色等以门店为准"}], "groupCode": "2", "zhName": "雪佛兰科沃兹", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/Type-C/SD", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "carPhone": true, "vehicleCode": "4139", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "雪佛兰科沃兹", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0612000d5pibur948E.png", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5212000c7mwubp7B39.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0l12000c7mwy55EE30.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000c7mwvvc7936.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3212000c7mx6u22402.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3012000c7mwv0sCA00.jpg?mark=yiche"], "transmissionType": 1, "brandName": "雪佛兰", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "雪佛兰", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "70", "guidSys": "不支持", "transmissionName": "自动挡"}, "baseResponse": {"code": "200", "requestId": "4536a7b5-394c-49d0-bbcc-474d51de1675", "cost": 85, "isSuccess": true, "returnMsg": "OK"}, "detailPageTags": [{"title": "送车上门", "lowestDailyPrice": 323.21, "specificProductGroups": {"vendorPriceList": [{"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "urge": "剩3辆", "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 51841, "qCommentCount": 51841, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 72.0, "currentOriginalDailyPrice": 83, "curOriginDPrice": 83, "oTPrice": 472.0, "currentTotalPrice": 425.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 47.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "rStoreCode": "188084", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_188084_188084", "comPriceCode": "[c]MzMzMTQ3NTcwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCYxMnNlJjEmZmFsMjAyNTI4JiQwMiAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJi0wMS0yMDI1MDowMDAzIDA2OCYmOjAwJmxzZSYxJmZhMjAyNTY4JiQwNCAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJjEmNCZ8MTAwMzIkMTgzJjMxJjIwMDAzJjIwLjAuMDAmMDImNDAkMTAwMCYxJjMwLjAkfDIyMC4wMDEtMDAyNS06MDA6MSAxMDAyNS0wMCYyNSAxMDAxLTAwMHwyOjAwOjEyLTIwMjQtOjQ1OjMgMjAAAAAAMjQAAA==", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzMwNTlfMV84M18zMzJfMTI4XzQ3Mi4wMF83Mi4wXzQyNS4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDc1NzMzMzFfMzU0OHwzNTAzfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNzc5XzEwMDE6MzMyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "831_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 472.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 285.0, "totalDailyPrice": 72.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "831_30040_pupai", "storeId": "188084"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 0, "rRc": 0, "skuId": 47573331, "klbPId": 17694, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 872, "rLevel": 872, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 188084, "kRSId": 188084, "kVId": 13094, "pLev": 1954562, "rLev": 1954562, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 0.0, "pStoreRouteDesc": "送车至机场", "rStoreRouteDesc": "门店还车，还车后店员送您至虹桥国际机场-T1航展楼-地下停车场", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "一年内车龄", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3510"}, {"category": 2, "sortNum": 14, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像/雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3810"}, {"category": 2, "sortNum": 20, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "行车记录仪", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3504"}, {"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 42, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "满油取车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3788"}, {"category": 2, "sortNum": 45, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "免加油服务费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "4229"}, {"category": 2, "sortNum": 47, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "延误时免费留车", "colorCode": "2", "prefix": "租车中心", "type": 1, "groupId": 3, "labelCode": "3779"}, {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减22", "groupId": 1}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 87.09, "isSelect": true, "distance": 0.0, "rDistance": 0.2347, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 285.0, "amountStr": "¥285", "subAmount": 72.0, "subAmountStr": "日均¥72", "originalDailyPrice": 83, "detail": [{"code": "1001", "name": "租车费", "amount": 332, "amountDesc": "¥332"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 17, "amountDesc": "¥17"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 425.0, "amountStr": "¥425", "subAmount": 472.0, "subAmountStr": "¥472", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "9951ad157ed747789f79210e056a6739", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "urge": "剩3辆", "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 51841, "qCommentCount": 51841, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 72.0, "currentOriginalDailyPrice": 83, "curOriginDPrice": 83, "oTPrice": 472.0, "currentTotalPrice": 425.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 47.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "rStoreCode": "188084", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_188084_188084", "comPriceCode": "[c]MzMzMTQ3NTcwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCYxMnNlJjEmZmFsMjAyNTI4JiQwMiAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJi0wMS0yMDI1MDowMDAzIDA2OCYmOjAwJmxzZSYxJmZhMjAyNTY4JiQwNCAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJjEmNCZ8MTAwMzIkMTgzJjMxJjIwMDAzJjIwLjAuMDAmMDImNDAkMTAwMCYxJjMwLjAkfDIyMC4wMDEtMDAyNS06MDA6MSAxMDAyNS0wMCYyNSAxMDAxLTAwMHwyOjAwOjEyLTIwMjQtOjQ1OjMgMjAAAAAAMjQAAA==", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzMwNTlfMV84M18zMzJfMTI4XzQ3Mi4wMF83Mi4wXzQyNS4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDc1NzMzMzFfMzU0OHwzNTAzfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNzc5XzEwMDE6MzMyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "831_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 472.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 285.0, "totalDailyPrice": 72.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "831_30040_pupai", "storeId": "188084"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 0, "rRc": 0, "skuId": 47573331, "klbPId": 17694, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 872, "rLevel": 872, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 188084, "kRSId": 188084, "kVId": 13094, "pLev": 1954562, "rLev": 1954562, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 0.0, "pStoreRouteDesc": "送车至机场", "rStoreRouteDesc": "门店还车，还车后店员送您至虹桥国际机场-T1航展楼-地下停车场", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"category": 2, "sortNum": 44, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 87.09, "isSelect": true, "distance": 0.0, "rDistance": 0.2347, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 285.0, "amountStr": "¥285", "subAmount": 72.0, "subAmountStr": "日均¥72", "originalDailyPrice": 83, "detail": [{"code": "1001", "name": "租车费", "amount": 332, "amountDesc": "¥332"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 17, "amountDesc": "¥17"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 425.0, "amountStr": "¥425", "subAmount": 472.0, "subAmountStr": "¥472", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "9951ad157ed747789f79210e056a6739", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "urge": "剩3辆", "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 51841, "qCommentCount": 51841, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 72.0, "currentOriginalDailyPrice": 83, "curOriginDPrice": 83, "oTPrice": 472.0, "currentTotalPrice": 425.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 47.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "rStoreCode": "188084", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_188084_188084", "comPriceCode": "[c]MzMzMTQ3NTcwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCYxMnNlJjEmZmFsMjAyNTI4JiQwMiAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJi0wMS0yMDI1MDowMDAzIDA2OCYmOjAwJmxzZSYxJmZhMjAyNTY4JiQwNCAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJjEmNCZ8MTAwMzIkMTgzJjMxJjIwMDAzJjIwLjAuMDAmMDImNDAkMTAwMCYxJjMwLjAkfDIyMC4wMDEtMDAyNS06MDA6MSAxMDAyNS0wMCYyNSAxMDAxLTAwMHwyOjAwOjEyLTIwMjQtOjQ1OjMgMjAAAAAAMjQAAA==", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzMwNTlfMV84M18zMzJfMTI4XzQ3Mi4wMF83Mi4wXzQyNS4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDc1NzMzMzFfMzU0OHwzNTAzfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNzc5XzEwMDE6MzMyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "831_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 472.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 285.0, "totalDailyPrice": 72.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "831_30040_pupai", "storeId": "188084"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 0, "rRc": 0, "skuId": 47573331, "klbPId": 17694, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 872, "rLevel": 872, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 188084, "kRSId": 188084, "kVId": 13094, "pLev": 1954562, "rLev": 1954562, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 0.0, "pStoreRouteDesc": "送车至机场", "rStoreRouteDesc": "门店还车，还车后店员送您至虹桥国际机场-T1航展楼-地下停车场", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"category": 2, "sortNum": 44, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 87.09, "isSelect": true, "distance": 0.0, "rDistance": 0.2347, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 285.0, "amountStr": "¥285", "subAmount": 72.0, "subAmountStr": "日均¥72", "originalDailyPrice": 83, "detail": [{"code": "1001", "name": "租车费", "amount": 332, "amountDesc": "¥332"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 17, "amountDesc": "¥17"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 425.0, "amountStr": "¥425", "subAmount": 472.0, "subAmountStr": "¥472", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "9951ad157ed747789f79210e056a6739", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "urge": "剩3辆", "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 51841, "qCommentCount": 51841, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 72.0, "currentOriginalDailyPrice": 83, "curOriginDPrice": 83, "oTPrice": 472.0, "currentTotalPrice": 425.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 47.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "rStoreCode": "188084", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_188084_188084", "comPriceCode": "[c]MzMzMTQ3NTcwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCYxMnNlJjEmZmFsMjAyNTI4JiQwMiAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJi0wMS0yMDI1MDowMDAzIDA2OCYmOjAwJmxzZSYxJmZhMjAyNTY4JiQwNCAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJjEmNCZ8MTAwMzIkMTgzJjMxJjIwMDAzJjIwLjAuMDAmMDImNDAkMTAwMCYxJjMwLjAkfDIyMC4wMDEtMDAyNS06MDA6MSAxMDAyNS0wMCYyNSAxMDAxLTAwMHwyOjAwOjEyLTIwMjQtOjQ1OjMgMjAAAAAAMjQAAA==", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzMwNTlfMV84M18zMzJfMTI4XzQ3Mi4wMF83Mi4wXzQyNS4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDc1NzMzMzFfMzU0OHwzNTAzfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNzc5XzEwMDE6MzMyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "831_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 472.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 285.0, "totalDailyPrice": 72.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "831_30040_pupai", "storeId": "188084"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 0, "rRc": 0, "skuId": 47573331, "klbPId": 17694, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 872, "rLevel": 872, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 188084, "kRSId": 188084, "kVId": 13094, "pLev": 1954562, "rLev": 1954562, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 0.0, "pStoreRouteDesc": "送车至机场", "rStoreRouteDesc": "门店还车，还车后店员送您至虹桥国际机场-T1航展楼-地下停车场", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"category": 2, "sortNum": 44, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 87.09, "isSelect": true, "distance": 0.0, "rDistance": 0.2347, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 285.0, "amountStr": "¥285", "subAmount": 72.0, "subAmountStr": "日均¥72", "originalDailyPrice": 83, "detail": [{"code": "1001", "name": "租车费", "amount": 332, "amountDesc": "¥332"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 17, "amountDesc": "¥17"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 425.0, "amountStr": "¥425", "subAmount": 472.0, "subAmountStr": "¥472", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "9951ad157ed747789f79210e056a6739", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "urge": "剩3辆", "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 51841, "qCommentCount": 51841, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 72.0, "currentOriginalDailyPrice": 83, "curOriginDPrice": 83, "oTPrice": 472.0, "currentTotalPrice": 425.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 47.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "rStoreCode": "188084", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_188084_188084", "comPriceCode": "[c]MzMzMTQ3NTcwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCYxMnNlJjEmZmFsMjAyNTI4JiQwMiAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJi0wMS0yMDI1MDowMDAzIDA2OCYmOjAwJmxzZSYxJmZhMjAyNTY4JiQwNCAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJjEmNCZ8MTAwMzIkMTgzJjMxJjIwMDAzJjIwLjAuMDAmMDImNDAkMTAwMCYxJjMwLjAkfDIyMC4wMDEtMDAyNS06MDA6MSAxMDAyNS0wMCYyNSAxMDAxLTAwMHwyOjAwOjEyLTIwMjQtOjQ1OjMgMjAAAAAAMjQAAA==", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzMwNTlfMV84M18zMzJfMTI4XzQ3Mi4wMF83Mi4wXzQyNS4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDc1NzMzMzFfMzU0OHwzNTAzfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNzc5XzEwMDE6MzMyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "831_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 472.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 285.0, "totalDailyPrice": 72.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "831_30040_pupai", "storeId": "188084"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 0, "rRc": 0, "skuId": 47573331, "klbPId": 17694, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 872, "rLevel": 872, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 188084, "kRSId": 188084, "kVId": 13094, "pLev": 1954562, "rLev": 1954562, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 0.0, "pStoreRouteDesc": "送车至机场", "rStoreRouteDesc": "门店还车，还车后店员送您至虹桥国际机场-T1航展楼-地下停车场", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"category": 2, "sortNum": 44, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 87.09, "isSelect": true, "distance": 0.0, "rDistance": 0.2347, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 285.0, "amountStr": "¥285", "subAmount": 72.0, "subAmountStr": "日均¥72", "originalDailyPrice": 83, "detail": [{"code": "1001", "name": "租车费", "amount": 332, "amountDesc": "¥332"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 17, "amountDesc": "¥17"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 425.0, "amountStr": "¥425", "subAmount": 472.0, "subAmountStr": "¥472", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "9951ad157ed747789f79210e056a6739", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "urge": "剩3辆", "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 51841, "qCommentCount": 51841, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 72.0, "currentOriginalDailyPrice": 83, "curOriginDPrice": 83, "oTPrice": 472.0, "currentTotalPrice": 425.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 47.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "rStoreCode": "188084", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_188084_188084", "comPriceCode": "[c]MzMzMTQ3NTcwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCYxMnNlJjEmZmFsMjAyNTI4JiQwMiAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJi0wMS0yMDI1MDowMDAzIDA2OCYmOjAwJmxzZSYxJmZhMjAyNTY4JiQwNCAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJjEmNCZ8MTAwMzIkMTgzJjMxJjIwMDAzJjIwLjAuMDAmMDImNDAkMTAwMCYxJjMwLjAkfDIyMC4wMDEtMDAyNS06MDA6MSAxMDAyNS0wMCYyNSAxMDAxLTAwMHwyOjAwOjEyLTIwMjQtOjQ1OjMgMjAAAAAAMjQAAA==", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzMwNTlfMV84M18zMzJfMTI4XzQ3Mi4wMF83Mi4wXzQyNS4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDc1NzMzMzFfMzU0OHwzNTAzfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNzc5XzEwMDE6MzMyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "831_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 472.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 285.0, "totalDailyPrice": 72.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "831_30040_pupai", "storeId": "188084"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 0, "rRc": 0, "skuId": 47573331, "klbPId": 17694, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 872, "rLevel": 872, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 188084, "kRSId": 188084, "kVId": 13094, "pLev": 1954562, "rLev": 1954562, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 0.0, "pStoreRouteDesc": "送车至机场", "rStoreRouteDesc": "门店还车，还车后店员送您至虹桥国际机场-T1航展楼-地下停车场", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"category": 2, "sortNum": 44, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 87.09, "isSelect": true, "distance": 0.0, "rDistance": 0.2347, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 285.0, "amountStr": "¥285", "subAmount": 72.0, "subAmountStr": "日均¥72", "originalDailyPrice": 83, "detail": [{"code": "1001", "name": "租车费", "amount": 332, "amountDesc": "¥332"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 17, "amountDesc": "¥17"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 425.0, "amountStr": "¥425", "subAmount": 472.0, "subAmountStr": "¥472", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "9951ad157ed747789f79210e056a6739", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "urge": "剩3辆", "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 51841, "qCommentCount": 51841, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 72.0, "currentOriginalDailyPrice": 83, "curOriginDPrice": 83, "oTPrice": 472.0, "currentTotalPrice": 425.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 47.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "rStoreCode": "188084", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_188084_188084", "comPriceCode": "[c]MzMzMTQ3NTcwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCYxMnNlJjEmZmFsMjAyNTI4JiQwMiAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJi0wMS0yMDI1MDowMDAzIDA2OCYmOjAwJmxzZSYxJmZhMjAyNTY4JiQwNCAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJjEmNCZ8MTAwMzIkMTgzJjMxJjIwMDAzJjIwLjAuMDAmMDImNDAkMTAwMCYxJjMwLjAkfDIyMC4wMDEtMDAyNS06MDA6MSAxMDAyNS0wMCYyNSAxMDAxLTAwMHwyOjAwOjEyLTIwMjQtOjQ1OjMgMjAAAAAAMjQAAA==", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzMwNTlfMV84M18zMzJfMTI4XzQ3Mi4wMF83Mi4wXzQyNS4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDc1NzMzMzFfMzU0OHwzNTAzfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNzc5XzEwMDE6MzMyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "831_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 472.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 285.0, "totalDailyPrice": 72.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "831_30040_pupai", "storeId": "188084"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 0, "rRc": 0, "skuId": 47573331, "klbPId": 17694, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 872, "rLevel": 872, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 188084, "kRSId": 188084, "kVId": 13094, "pLev": 1954562, "rLev": 1954562, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 0.0, "pStoreRouteDesc": "送车至机场", "rStoreRouteDesc": "门店还车，还车后店员送您至虹桥国际机场-T1航展楼-地下停车场", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"category": 2, "sortNum": 44, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 87.09, "isSelect": true, "distance": 0.0, "rDistance": 0.2347, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 285.0, "amountStr": "¥285", "subAmount": 72.0, "subAmountStr": "日均¥72", "originalDailyPrice": 83, "detail": [{"code": "1001", "name": "租车费", "amount": 332, "amountDesc": "¥332"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 17, "amountDesc": "¥17"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 425.0, "amountStr": "¥425", "subAmount": 472.0, "subAmountStr": "¥472", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "9951ad157ed747789f79210e056a6739", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "urge": "剩3辆", "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 51841, "qCommentCount": 51841, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 72.0, "currentOriginalDailyPrice": 83, "curOriginDPrice": 83, "oTPrice": 472.0, "currentTotalPrice": 425.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 47.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "rStoreCode": "188084", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_188084_188084", "comPriceCode": "[c]MzMzMTQ3NTcwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCYxMnNlJjEmZmFsMjAyNTI4JiQwMiAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJi0wMS0yMDI1MDowMDAzIDA2OCYmOjAwJmxzZSYxJmZhMjAyNTY4JiQwNCAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJjEmNCZ8MTAwMzIkMTgzJjMxJjIwMDAzJjIwLjAuMDAmMDImNDAkMTAwMCYxJjMwLjAkfDIyMC4wMDEtMDAyNS06MDA6MSAxMDAyNS0wMCYyNSAxMDAxLTAwMHwyOjAwOjEyLTIwMjQtOjQ1OjMgMjAAAAAAMjQAAA==", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzMwNTlfMV84M18zMzJfMTI4XzQ3Mi4wMF83Mi4wXzQyNS4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDc1NzMzMzFfMzU0OHwzNTAzfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNzc5XzEwMDE6MzMyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "831_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 472.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 285.0, "totalDailyPrice": 72.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "831_30040_pupai", "storeId": "188084"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 0, "rRc": 0, "skuId": 47573331, "klbPId": 17694, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 872, "rLevel": 872, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 188084, "kRSId": 188084, "kVId": 13094, "pLev": 1954562, "rLev": 1954562, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 0.0, "pStoreRouteDesc": "送车至机场", "rStoreRouteDesc": "门店还车，还车后店员送您至虹桥国际机场-T1航展楼-地下停车场", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"category": 2, "sortNum": 44, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 87.09, "isSelect": true, "distance": 0.0, "rDistance": 0.2347, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 285.0, "amountStr": "¥285", "subAmount": 72.0, "subAmountStr": "日均¥72", "originalDailyPrice": 83, "detail": [{"code": "1001", "name": "租车费", "amount": 332, "amountDesc": "¥332"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 17, "amountDesc": "¥17"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 425.0, "amountStr": "¥425", "subAmount": 472.0, "subAmountStr": "¥472", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "9951ad157ed747789f79210e056a6739", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "urge": "剩3辆", "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 51841, "qCommentCount": 51841, "qExposed": "4.9", "overallRating": "4.9", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 72.0, "currentOriginalDailyPrice": 83, "curOriginDPrice": 83, "oTPrice": 472.0, "currentTotalPrice": 425.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 47.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "rStoreCode": "188084", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_188084_188084", "comPriceCode": "[c]MzMzMTQ3NTcwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCYxMnNlJjEmZmFsMjAyNTI4JiQwMiAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJi0wMS0yMDI1MDowMDAzIDA2OCYmOjAwJmxzZSYxJmZhMjAyNTY4JiQwNCAwLTAxLTowMCYwOjAwMSZmYTY4JiY2OCYkbHNlJjEmNCZ8MTAwMzIkMTgzJjMxJjIwMDAzJjIwLjAuMDAmMDImNDAkMTAwMCYxJjMwLjAkfDIyMC4wMDEtMDAyNS06MDA6MSAxMDAyNS0wMCYyNSAxMDAxLTAwMHwyOjAwOjEyLTIwMjQtOjQ1OjMgMjAAAAAAMjQAAA==", "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzMwNTlfMV84M18zMzJfMTI4XzQ3Mi4wMF83Mi4wXzQyNS4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDc1NzMzMzFfMzU0OHwzNTAzfDM1MDR8MzU2M3wzNzQ2fDM3ODh8NDIyOXwzNzc5XzEwMDE6MzMyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "831_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "pStoreNav": "免费站内取还车", "rStoreNav": "免费站内取还车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 16, "returnWayInfo": 16, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 472.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 285.0, "totalDailyPrice": 72.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "831_30040_pupai", "storeId": "188084"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "gsDesc": "站内取还", "pRc": 0, "rRc": 0, "skuId": 47573331, "klbPId": 17694, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 872, "rLevel": 872, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "16", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 188084, "kRSId": 188084, "kVId": 13094, "pLev": 1954562, "rLev": 1954562, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 0.0, "pStoreRouteDesc": "送车至机场", "rStoreRouteDesc": "门店还车，还车后店员送您至虹桥国际机场-T1航展楼-地下停车场", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"category": 2, "sortNum": 44, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 512, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 0.0, "storeScore": 87.09, "isSelect": true, "distance": 0.0, "rDistance": 0.2347, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 285.0, "amountStr": "¥285", "subAmount": 72.0, "subAmountStr": "日均¥72", "originalDailyPrice": 83, "detail": [{"code": "1001", "name": "租车费", "amount": 332, "amountDesc": "¥332"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 17, "amountDesc": "¥17"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 425.0, "amountStr": "¥425", "subAmount": 472.0, "subAmountStr": "¥472", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "9951ad157ed747789f79210e056a6739", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}]}, "filteredProductGroups": {"vendorPriceList": [{"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "八骏马租车", "isMinTPriceVendor": false, "vendorLogo": "", "commentInfo": {"level": "超棒", "vendorDesc": "八骏马租车机场店", "commentCount": 701, "qCommentCount": 701, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "车辆新/车况好", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 551.0, "currentOriginalDailyPrice": 588, "curOriginDPrice": 588, "oTPrice": 2492.0, "currentTotalPrice": 2344.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 148.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD4113", "vendorCode": "81889", "pStoreCode": "107104", "rStoreCode": "107104", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD4113_0_107104_107104", "comPriceCode": "[c]MzczMjY1OTQwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDo4JiYxMCY1OHNlJjUmZmFsMjAyNTg4JiQwMiAwLTAxLTowMCYwOjAwJjEmZjU4OCYmNTg4YWxzZTI1LTAmJDIwIDAwOjEtMDMwJjU4MDA6MCZmYWw4JiYxODgmJHNlJjUtMDEtMjAyNTA6MDAwNCAwNTg4JjowMCZhbHNlJjEmZiYkfDEmNTg4NCY1ODAwMSY1MiQxOCYyMzEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTA3MTA0XzMwNTlfMV81ODhfMjM1Ml81ODhfMjQ5Mi4wMF81NTEuMF8yMzQ0LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF82NTk0MzczMl8zNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MjM1MnwxMDAzOjIwLjAwfDEwMDI6MTIwLjAw", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "96823_29439_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 2492.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 2204.0, "totalDailyPrice": 551.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "96823_29439_pupai", "storeId": "107104"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 65943732, "klbPId": 286924, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 27021, "rLevel": 27021, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "1.12", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 107104, "kRSId": 107104, "kVId": 81889, "pLev": 27021, "rLev": 27021, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 2.0, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "携程租车中心还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 9, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2048, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 2.0, "storeScore": 98.9, "isSelect": false, "distance": 0.0, "rDistance": 1.7871, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "八骏马租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 2204.0, "amountStr": "¥2204", "subAmount": 551.0, "subAmountStr": "日均¥551", "originalDailyPrice": 588, "detail": [{"code": "1001", "name": "租车费", "amount": 2352, "amountDesc": "¥2352"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2344.0, "amountStr": "¥2344", "subAmount": 2492.0, "subAmountStr": "¥2492", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "a4212ca017274769a4463b0da75c00d5", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}]}}, {"title": "到店取车", "lowestDailyPrice": 123.21, "specificProductGroups": {"vendorPriceList": [{"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}, {"vendorName": "西十出行", "isMinTPriceVendor": false, "commentInfo": {"level": "超棒", "vendorDesc": "三亚凤凰机场店", "commentCount": 15, "qCommentCount": 15, "qExposed": "5.0", "overallRating": "5.0", "maximumRating": 5.0, "commentLabel": "服务周到", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 77.0, "currentOriginalDailyPrice": 91, "curOriginDPrice": 91, "oTPrice": 502.0, "currentTotalPrice": 443.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 59.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD12615", "vendorCode": "15005425", "pStoreCode": "797672", "rStoreCode": "797672", "vehicleCode": "0", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": false, "payMode": 2, "productCode": "SD12615_0_797672_797672", "comPriceCode": "[c]ODExNzQ2MDAyNS0wfHwyMCAwMDoxLTAxMCYxMjAwOjAmZmFsOCYmMTI4JiRzZSYxLTAxLTIwMjUwOjAwMDIgMDc4JiY6MDAmbHNlJjEmZmEyMDI1NzgmJDAzIDAtMDEtOjAwJjA6MDAxJmZhNzgmJjc4JiRsc2UmLTAxLTIwMjUwOjAwMDQgMDc4JiY6MDAmbHNlJjEmZmF8MTAwNzgmJDkxJjMxJjQmMDAzJjYyJDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjQyMC4wMDAmMTAyNS0wJHwyMSAxMDAxLTAwMCYyOjAwOjAxLTAwMjUtOjAwOjUgMTAwMjQtMDB8MjMgMjAxMi0yMjQAADo0NTo=", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzMwNTlfMV85MV8zNjJfMTI4XzUwMi4wMF83Ny4wXzQ0My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDYwMDgxMTdfMzU0OHwzNTAzfDM1MDR8MzY3OXwzNzQ2XzEwMDE6MzYyfDEwMDM6MjAuMDB8MTAwMjoxMjAuMDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13299_30040_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "共减59", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 502.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 303.0, "totalDailyPrice": 77.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13299_30040_pupai", "storeId": "797672"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 2, "rRc": 2, "skuId": 46008117, "klbPId": 112911, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 8603, "rLevel": 8603, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "89.66", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 797672, "kRSId": 797672, "kVId": 15005425, "pLev": 1368452, "rLev": 1368452, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 1.0, "pStoreRouteDesc": "携程租车中心取车", "easyLifeInfo": {"isEasyLife": false}, "platformCode": "0", "allTags": [{"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 1073741824, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 24, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 224, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 1.0, "storeScore": 77.42, "isSelect": false, "distance": 0.0, "rDistance": 0.2397, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "3852", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "西十出行", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 303.0, "amountStr": "¥303", "subAmount": 77.0, "subAmountStr": "日均¥77", "originalDailyPrice": 91, "detail": [{"code": "1001", "name": "租车费", "amount": 362, "amountDesc": "¥362"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}, {"code": "3852", "name": "长租特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 443.0, "amountStr": "¥443", "subAmount": 502.0, "subAmountStr": "¥502", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "bbfb6bf9dd51498a9aa194f28a54145d", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}]}, "filteredProductGroups": {"vendorPriceList": [{"vendorName": "小龙人租车", "isMinTPriceVendor": false, "commentInfo": {"level": "很好", "vendorDesc": "小龙人机场路店", "commentCount": 10, "qCommentCount": 10, "qExposed": "4.8", "overallRating": "4.8", "maximumRating": 5.0, "commentLabel": "取还方便", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 288.0, "currentOriginalDailyPrice": 295, "curOriginDPrice": 295, "oTPrice": 1318.0, "currentTotalPrice": 1288.0, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceType": 1, "deductInfos": [{"totalAmount": 30.0, "payofftype": 2}]}, "reference": {"bizVendorCode": "SD8888", "vendorCode": "15001908", "pStoreCode": "184116", "rStoreCode": "184116", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD8888_0_184116_184116", "comPriceCode": "[c]MzczMjQ0MzgwfDIwfDAuMDEtMDEyNS0wMDA6MCAwMDowJiYxMCYyMHNlJjImZmFsMjAyNTAwJiQwMiAwLTAxLTowMCYwOjAwJjEmZjIwMCYmMjAwYWxzZTI1LTAmJDIwIDAwOjEtMDMwJjM4MDA6MCZmYWw5JiYxODkmJHNlJjMtMDEtMjAyNTA6MDAwNCAwMzg5JjowMCZhbHNlJjEmZiYkfDEmMzg5NCYyOTAwMSY3OCQxNSYxMTEmMjAwMDMmMjAuMC4wMCYwMiY0MCQxMDAwJjEmMzAuMCR8MjIwLjAwMS0wMDI1LTowMDoxIDEwMDI1LTAwJjI1IDEwMDEtMDAwfDI6MDA6MTItMjAyNC06NDU6MyAyMAAAAAAyNAAA", "priceVersion": "SH-PRICEVERSION_MTg0MTE2XzMwNTlfMV8yOTVfMTE3OF8yMDBfMTMxOC4wMF8yODguMF8xMjg4LjBfMF8wXzAuMF8wLjBfMTIwLjAwXzIwLjAwXzAuMDBfMC4wMF80NDM4MzczMl8zODEwfDM1NjN8Mzc0NnwzNzg4fDQyMjl8Mzg3Ml8xMDAxOjExNzh8MTAwMzoyMC4wMHwxMDAyOjEyMC4wMA==", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "10901_29437_pupai", "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [{"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减30", "groupId": 1, "mergeId": 0}], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 1318.0, "isrec": false, "recommendOrder": 0, "mergeId": 1319, "rectype": 1, "cvid": 3059, "rentalamount": 1148.0, "totalDailyPrice": 288.0, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "10901_29437_pupai", "storeId": "184116"}]}, "adjustVersion": "", "gsId": 0, "elct": 0, "pRc": 0, "rRc": 0, "skuId": 44383732, "klbPId": 60229, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 1101731, "rLevel": 1101731, "promtId": 921680837, "rCoup": 0, "sortInfo": {"p": "1", "s": "5.42", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 184116, "kRSId": 184116, "kVId": 15001908, "pLev": 1101731, "rLev": 1101731, "klbVersion": 1, "kVehicleId": 3059, "adjustRuleId": "", "packageLevel": "BAS"}, "sortNum": 3.0, "pStoreRouteDesc": "门店取车，距门店直线2.0公里", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "2年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减148", "groupId": 1, "mergeId": 0}], "filterAggregations": [{"groupCode": "BrandGroup_q0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_2", "binaryDigit": 128, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 56, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 34, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 2096, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "sortScore": 3.0, "storeScore": 65.52, "isSelect": false, "distance": 0.0, "rDistance": 2.7023, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0.0, "pickOffFee": 0.0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "小龙人租车", "card": 0, "ctripVehicleCode": "3059", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1148.0, "amountStr": "¥1148", "subAmount": 288.0, "subAmountStr": "日均¥288", "originalDailyPrice": 295, "detail": [{"code": "1001", "name": "租车费", "amount": 1178, "amountDesc": "¥1178"}, {"code": "11037", "name": "优惠券", "amount": 30, "amountDesc": "¥30"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120.0, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120.0, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1288.0, "amountStr": "¥1288", "subAmount": 1318.0, "subAmountStr": "¥1318", "currencyCode": "¥"}], "priceDailys": [], "uniqueCode": "7c5b94688f3242bfa4c6b1a4ab785bd3", "licenseTag": "", "newCar": false, "type": 0, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "0_3059_", "addProducts": []}]}}], "imStatus": 1, "isFromSearch": false, "ResponseStatus": {"Extension": [{"Value": "3365534906198848011", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a2d8155-482309-2688985", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1736315333490+0800)/"}, "extras": {"packageLevelAB": "B", "abVersion": "241008_DSJT_ykjpx|B,230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,220624_DSJT_spfj1|B", "isLicensePlateHideShow": "0", "serverRequestId": "3g4d9tFD6J9D56n5707O", "packageLevelSwitch": "1", "rSelect": "1", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "1"}, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "recommendProducts": [{"groupSort": 0, "lowestPrice": 69, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 137, "detail": [{"code": "1001", "amount": 156, "amountDesc": "¥156", "name": "租车费"}, {"code": "3743", "amount": 19, "amountDesc": "¥19", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥69", "originalDailyPrice": 78, "subAmount": 69, "name": "车辆租金", "amountStr": "¥137"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 217, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥236", "subAmount": 236, "name": "总价", "amountStr": "¥217"}], "reference": {"vehicleCode": "0", "rStoreCode": "107059", "packageId": "", "pLev": 24801, "comPriceCode": "[c]MDIwfDMzODh8MjAyMC4wMC0wOCA1LTAxMDowMDAwOjAmMSZmJjc4JiY3OCZhbHNlNS0wMSQyMDIwMDowLTA5ICY3OCYwOjAwYWxzZSYxJmYkfDEwJjc4JiY3OCYwMSYyMTAwMzE1NiQwLjAwJjEmMjAwJDEmMjAuMiYzMDAwMiY2MC4wLjAwJjAyNS0wJHwyOCAxODAxLTAwMCYyOjAwOjAxLTEwMjUtOjAwOjAgMTgwMjUtMDB8MjggMTMwMS0wNDYAADo0Nzo=", "bizVendorCode": "SD3987", "pStoreCode": "107059", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3MDU5XzU0MDdfMV83OF8xNTZfNzhfMjM2LjAwXzY5LjBfMjE3LjBfMF8wXzAuMF8wLjBfNjAuMDBfMjAuMDBfMC4wMF8wLjAwXzMzODgwMjA=", "sendTypeForPickUpCar": 0, "skuId": 3388020, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24801, "vendorCode": "63836", "vendorVehicleCode": "3388020"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44835820, "bizVendorCode": "SD7678"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 46121699, "bizVendorCode": "SD12615"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 69928300, "bizVendorCode": "SD8506"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6900273, "bizVendorCode": "SD3866"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1910574, "bizVendorCode": "SD4374"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 24662247, "bizVendorCode": "SD3127"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 43673666, "bizVendorCode": "SD8155"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 77221796, "bizVendorCode": "SD10907"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 28097938, "bizVendorCode": "SD3933"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 64674345, "bizVendorCode": "SD14035"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 24587431, "bizVendorCode": "SD4102"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 72832950, "bizVendorCode": "SD11239"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1914745, "bizVendorCode": "SD5118"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 51441167, "bizVendorCode": "SD10763"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 4696845, "bizVendorCode": "SD4518"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 43809907, "bizVendorCode": "SD3263"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 57104797, "bizVendorCode": "SD4410"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44336335, "bizVendorCode": "SD7724"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 55333188, "bizVendorCode": "SD6673"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44384894, "bizVendorCode": "SD4552"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 62556145, "bizVendorCode": "SD7103"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 21166839, "bizVendorCode": "SD6991"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 16622677, "bizVendorCode": "SD6991"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 77493319, "bizVendorCode": "SD15275"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 54503457, "bizVendorCode": "SD5809"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 66216501, "bizVendorCode": "SD14617"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减19", "groupId": 1}, "vehicleCode": "5407", "highestPrice": 588, "pWay": "可选：免费站内取还车", "minDPrice": 69, "vehicleKey": "0_5407_", "hot": 0, "minTPrice": 217, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 33, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3987_0_107059_107059"], "introduce": "当前车型最低价"}, "minDOrinPrice": 78, "isEasy": true, "isCredit": true, "maximumCommentCount": 52109, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 27}, {"groupSort": 0, "lowestPrice": 69, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 137, "detail": [{"code": "1001", "amount": 156, "amountDesc": "¥156", "name": "租车费"}, {"code": "3743", "amount": 19, "amountDesc": "¥19", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥69", "originalDailyPrice": 78, "subAmount": 69, "name": "车辆租金", "amountStr": "¥137"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 217, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥236", "subAmount": 236, "name": "总价", "amountStr": "¥217"}], "reference": {"vehicleCode": "0", "rStoreCode": "106965", "packageId": "sec", "pLev": 88368, "comPriceCode": "[c]Nzg5NjI4MDkwfDIwfDAuMDEtMDgyNS0wMDA6MCAwMDomJjEmMCY3OCY1OCZ0cnVlNS0wMSQyMDIwMDowLTA5ICY3OCYwOjAwcnVlJiYxJnR8MTAwNTgmJDc4JjExJjImMDAzJjU2JDEuMDAmMSYyMDAkMTAyMC4wJjMwLjAyJjIwLjAwMDAmNjI1LTAkfDIwIDE4OjEtMDgwJjIwMDA6MDEtMTAyNS0wMDA6MCAxODoyNS0wMHwyMCAxMzoxLTA4NwAAADQ3OjQ=", "bizVendorCode": "SD3933", "pStoreCode": "106965", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2OTY1XzEwNjlfMV83OF8xNTZfNzhfMjM2LjAwXzY5LjBfMjE3LjBfMF8wXzAuMF8wLjBfNjAuMDBfMjAuMDBfMC4wMF8wLjAwXzI4MDk3ODk2", "sendTypeForPickUpCar": 0, "skuId": 28097896, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 88368, "vendorCode": "46492", "vendorVehicleCode": "6771_19892_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 72903993, "bizVendorCode": "SD7763"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 51724450, "bizVendorCode": "SD3047"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 156068, "bizVendorCode": "SD14450"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 228229, "bizVendorCode": "SD3012"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 44835274, "bizVendorCode": "SD7678"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 46121671, "bizVendorCode": "SD12615"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 51716988, "bizVendorCode": "SD6709"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 43673680, "bizVendorCode": "SD8155"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 69928384, "bizVendorCode": "SD8506"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 6900277, "bizVendorCode": "SD3866"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 60352531, "bizVendorCode": "SD3926"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 30540952, "bizVendorCode": "SD4201"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 65051106, "bizVendorCode": "SD14035"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 77416956, "bizVendorCode": "SD3263"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 71216300, "bizVendorCode": "SD11257"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 25635982, "bizVendorCode": "SD6991"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 61517975, "bizVendorCode": "SD5523"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 76073516, "bizVendorCode": "SD7114"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 65943851, "bizVendorCode": "SD4113"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减19", "groupId": 1}, "vehicleCode": "1069", "highestPrice": 600, "pWay": "可选：免费站内取还车", "minDPrice": 69, "vehicleKey": "0_1069_", "hot": 0, "minTPrice": 217, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 39, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3933_0_106965_106965"], "introduce": "当前车型最低价"}, "minDOrinPrice": 78, "isEasy": true, "isCredit": true, "maximumCommentCount": 52782, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0.18571922, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 20}, {"groupSort": 0, "lowestPrice": 51, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 102, "detail": [{"code": "1001", "amount": 116, "amountDesc": "¥116", "name": "租车费"}, {"code": "3743", "amount": 14, "amountDesc": "¥14", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥51", "originalDailyPrice": 58, "subAmount": 51, "name": "车辆租金", "amountStr": "¥102"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 182, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥196", "subAmount": 196, "name": "总价", "amountStr": "¥182"}], "reference": {"vehicleCode": "0", "rStoreCode": "188084", "packageId": "sec", "pLev": 1954562, "comPriceCode": "[c]", "bizVendorCode": "SD3866", "pStoreCode": "188084", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTg4MDg0XzIwNTBfMV81OF8xMTZfNThfMTk2LjAwXzUxLjBfMTgyLjBfMF8wXzAuMF8wLjBfNjAuMDBfMjAuMDBfMC4wMF8wLjAwXzQzOTM3OTA5", "sendTypeForPickUpCar": 0, "skuId": 43937909, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1954562, "vendorCode": "13094", "vendorVehicleCode": "10112_8013_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2288518, "bizVendorCode": "SD6987"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减14", "groupId": 1}, "vehicleCode": "2050", "highestPrice": 86, "pWay": "可选：免费站内取还车", "minDPrice": 51, "vehicleKey": "0_2050_", "hot": 0, "minTPrice": 182, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 65, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3866_0_188084_188084"], "introduce": "当前车型最低价"}, "minDOrinPrice": 58, "isEasy": true, "isCredit": true, "maximumCommentCount": 52109, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 2}, {"groupSort": 0, "lowestPrice": 51, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 102, "detail": [{"code": "1001", "amount": 116, "amountDesc": "¥116", "name": "租车费"}, {"code": "3743", "amount": 14, "amountDesc": "¥14", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥51", "originalDailyPrice": 58, "subAmount": 51, "name": "车辆租金", "amountStr": "¥102"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 182, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥196", "subAmount": 196, "name": "总价", "amountStr": "¥182"}], "reference": {"vehicleCode": "0", "rStoreCode": "2285298", "packageId": "sec", "pLev": 2004549, "bizVendorCode": "SD7763", "pStoreCode": "2285298", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MjI4NTI5OF8xNDY2XzFfNTguMF8xMTYuMF8wLjBfMTk2LjBfNTEuMF8xODIuMF8wXzBfMC4wXzAuMF82MC4wXzIwLjBfMF8wXzcyOTEwNjcx", "sendTypeForPickUpCar": 0, "skuId": 72910671, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 2004549, "vendorCode": "15006533", "vendorVehicleCode": "90598_55922_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 40526452, "bizVendorCode": "SD3866"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 46121706, "bizVendorCode": "SD12615"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 76176451, "bizVendorCode": "SD7114"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减14", "groupId": 1}, "vehicleCode": "1466", "highestPrice": 253, "pWay": "可选：免费站内取还车", "minDPrice": 51, "vehicleKey": "0_1466_", "hot": 0, "minTPrice": 182, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 73, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD7763_0_2285298_2285298"], "introduce": "当前车型最低价"}, "minDOrinPrice": 58, "isEasy": true, "isCredit": true, "maximumCommentCount": 52109, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 4}], "checkResponseTime": 1736315290161.199, "checkRequestTime": 1736315289927.81, "timeInterval": 233.388916015625, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 263, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 263, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1736315289927, "afterFetch": 1736315290190}}