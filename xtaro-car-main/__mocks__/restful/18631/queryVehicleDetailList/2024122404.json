{"promotMap": {}, "extras": {"packageLevelAB": "B", "abVersion": "241008_DSJT_ykjpx|A,240419_DSJT_wyz24|B,230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,231218_DSJT_zzqh|B,231218_DSJT_zzqh|B", "isLicensePlateHideShow": "0", "serverRequestId": "4ld3506maAk4761dOaMd", "packageLevelSwitch": "1", "rSelect": "1", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "0"}, "shareVehicleInfo": {"doorNo": 4, "displacement": "1.5L", "style": "", "license": "", "currentOriginalDailyPrice": 58, "vehicleImage": "https://dimg04.c-ctrip.com/images/0RV6s12000b84wiioF6AF.jpg", "groupName": "捷达VA3", "transmissionType": 1, "passengerNo": 5, "vehicleName": "捷达VA3", "transmissionName": "自动挡"}, "baseResponse": {"code": "200", "requestId": "aa73a66a-3b7a-4681-8870-36462df9552a", "cost": 36, "isSuccess": true, "returnMsg": "OK"}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "detailPageTags": [{"title": "机场内取车", "filteredProductGroups": {"vendorPriceList": []}, "lowestDailyPrice": 0, "specificProductGroups": {"vendorPriceList": [{"vehicleKey": "0_5104_", "stationType": 3, "adverts": 0, "card": 0, "commentInfo": {"overallRating": "0.0", "qCommentCount": 0, "vendorDesc": "机场店", "level": "", "maximumRating": 5, "commentLabel": "", "qExposed": "0.0", "hasComment": 0, "commentCount": 0}, "storeScore": 100, "uniqueCode": "4deea324619640fa9026caf303fd735e", "pickOffFee": 0, "originPsType": 1, "priceInfo": {"curOriginDPrice": 58, "currentTotalPrice": 100, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "priceVersion": "SH-PRICEVERSION_MTA3MjQyXzUxMDRfMV81OF8xMTZfNThfMjE2LjAwXzBfMTAwLjBfMF8wXzAuMF8wLjBfODAuMDBfMjAuMDBfMC4wMF8wLjAwXzE4NjIyNzk=", "priceType": 1, "marginPrice": 0, "oTPrice": 216, "deductInfos": [{"totalAmount": 116, "payofftype": 2}], "currentOriginalDailyPrice": 58, "currentDailyPrice": 0, "naked": true}, "sortNum": 0, "vendorName": "捷安利达租车", "filterAggregations": [{"groupCode": "BrandGroup_j0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "NewEnergy", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 48, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 16, "checkType": 0}, {"groupCode": "SelfService", "binaryDigit": 2, "checkType": 0}], "rDistance": 1.5616, "actId": "", "pickUpFee": 0, "type": 0, "originRsType": 1, "payModes": [2], "allTags": [{"category": 1, "sortNum": 25, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减116", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "licenseTag": "", "fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 116, "amountDesc": "¥116", "name": "租车费"}, {"code": "11037", "amount": 116, "amountDesc": "¥116", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 58, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 100, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥216", "subAmount": 216, "name": "总价", "amountStr": "¥100"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "送车至机场，联系商家取车", "ctripVehicleCode": "5104", "newCar": false, "secretBox": false, "sortScore": 0, "cyVendorName": "捷安利达租车", "vehicleGroup": 2, "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": true, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "1862279", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "送车至机场，联系商家取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 216, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 0, "grantedcode": "", "isrec": false, "cvid": 5104, "rentalamount": 0}, "klbVersion": 1, "platform": 10, "bizVendorCode": "SD3949", "elct": 0, "pLevel": 24129, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 2410, "rStoreNav": "机场内还车", "priceVersion": "SH-PRICEVERSION_MTA3MjQyXzUxMDRfMV81OF8xMTZfNThfMjE2LjAwXzBfMTAwLjBfMF8wXzAuMF8wLjBfODAuMDBfMjAuMDBfMC4wMF8wLjAwXzE4NjIyNzlfMzUwMXwzNTYzfDM3NDZ8Mzc4OHw0MjI5XzEwMDE6MTE2fDEwMDM6MjAuMDB8MTAwMjo4MC4wMA==", "alipay": false, "vendorCode": "57671", "productCode": "SD3949_0_107242_107242", "pLev": 24129, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 0, "packageType": 1, "kPSId": 107242, "age": 30, "rCoup": 0, "kRSId": 107242, "freeIllegalDeposit": false, "rLev": 24129, "pStoreCode": "107242", "pickUpOnDoor": true, "aType": 0, "kVId": 57671, "sortInfo": {"p": "1", "s": "65.4", "c": "43", "v": "55528"}, "kVehicleId": 5104, "comPriceCode": "[c]Mjc5fDE4NjJ8MjAyMC4wMC0wOCA1LTAxMDowMDAwOjAmMSZmJjU4JiY1OCZhbHNlNS0wMSQyMDIwMDowLTA5ICY1OCYwOjAwMS4wMDU4JjJhbHNlMDAmZjU4JHwmNTgmJjImNTEwMDE2JDEwOCYxMSYyMC4wMyYxMC4wMDAwJjIyJjImJDEwMDAmODA0MC4wfDIwMi4wMCQtMDggNS0wMTA6MDAxNjowNS0wMSYyMDIxMzowLTEwIHwyMDIwOjAwLTA2IDUtMDEwOjU0MTc6MQ==", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减116", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": *********, "rStoreCode": "107242", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 1862279, "rLevel": 24129, "newEnergy": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": true, "addProducts": [], "platformCode": "10"}]}}, {"title": "机场外取车", "specificProductGroups": {}, "filteredProductGroups": {"vendorPriceList": []}}], "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "priceExplain": {"title": "价格说明", "content": ["划线价格为参考价，该价格指商品或服务的门市价、服务提供商的指导价、零售价或该商品或服务曾经展示过的销售价等并非原价；由于产品信息实时更新、市场价格波动等可能会与您预订时展示的不一致，该价格仅供您参考。", "未划线价格指商品或服务的实时标价，为划线价基础上计算出的优惠金额。具体成交价格根据商品或服务参加活动，或会员使用优惠券等发生变化，最终以订单结算页价格为准。", "如有疑问，您可在预订前联系客服进行咨询。", "此说明仅当出现价格比较时有效。若服务提供商单独对划线价格进行说明的，以服务提供商的表述为准。 "]}, "vehicleInfo": {"skylight": "不支持", "vehicleKey": "0_5104_", "luggageNo": 5, "carPlay": "不支持", "displacement": "1.5L", "autoPark": false, "charge": "", "fuelType": "汽油", "snowTyre": {"type": 0, "typeDesc": "不支持"}, "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5g1200000mggtf40D2.png?mark=yiche"], "license": "", "isSpecialized": true, "multimediaAlbums": [{"albumName": "官方相册", "albumType": 1, "mediaGroup": [{"groupType": 2, "groupName": "视频", "groupSortNum": 2, "medias": [{"sortNum": 0, "type": 2, "url": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg", "cover": "https://dimg04.c-ctrip.com/images/0RV6s12000b84wiioF6AF.jpg"}]}, {"groupType": 3, "groupName": "VR", "groupSortNum": 3, "medias": [{"sortNum": 0, "type": 3, "url": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5436&app_ver=10.5", "cover": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5g1200000mggtf40D2.png?mark=yiche"}]}, {"groupType": 4, "groupName": "外观", "groupSortNum": 4, "medias": [{"sortNum": 0, "type": 1, "url": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "cover": ""}]}, {"groupType": 8, "groupName": "细节", "groupSortNum": 8, "medias": [{"sortNum": 0, "type": 1, "url": "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "cover": ""}, {"sortNum": 1, "type": 1, "url": "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "cover": ""}, {"sortNum": 2, "type": 1, "url": "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "cover": ""}, {"sortNum": 3, "type": 1, "url": "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "cover": ""}, {"sortNum": 4, "type": 1, "url": "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "cover": ""}]}, {"groupType": 7, "groupName": "相册", "groupSortNum": 9999, "medias": []}], "note": "年款/颜色等以门店为准"}], "groupCode": "2", "zhName": "捷达VA3", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "不支持", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5436&app_ver=10.5", "reverseImage": {"type": 1, "typeDesc": "支持"}, "vehicleCode": "5104", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "捷达VA3", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6s12000b84wiioF6AF.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "reverseSensor": {"type": 0, "typeDesc": "不支持"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "transmissionType": 1, "brandName": "捷达", "oilType": 3, "tachograph": {"type": 0, "typeDesc": "不支持"}, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "捷达", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "64", "guidSys": "不支持", "transmissionName": "自动挡"}, "isFromSearch": false, "timeInterval": 103.************, "resBodySize": 11442, "ResponseStatus": {"Extension": [{"Value": "6322177639344925934", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a770595-482265-36913", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1736154658165+0800)/"}, "imStatus": 1, "promptInfos": [{"jumpUrl": "https://m.ctrip.com/tangram/OTI2MjU=?ctm_ref=vactang_page_92625&isHideNavBar=YES&apppgid=10650039395&statusBarStyle=1&channelid=238407", "type": 19, "locations": [{"groupCode": "all", "index": 1}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg3112000e8l73mt525E.png"}, {"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "checkResponseTime": 1736154658089.686, "checkRequestTime": 1736154657985.7532, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 162, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 162, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1736154657984, "afterFetch": 1736154658146}}