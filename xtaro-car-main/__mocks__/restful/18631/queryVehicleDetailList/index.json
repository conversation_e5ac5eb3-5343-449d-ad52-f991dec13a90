{"vehicleInfo": {"transmissionName": "自动挡", "displacement": "1.0T-1.5L", "style": "", "isHot": false, "imageList": ["https://pages.c-ctrip.com/carisd/app/14415.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com//images/0410x120008at5wkp72C0.jpg", "vehicleCode": "14415", "fuel": "汽油92号", "licenseStyle": "2", "oilType": 5, "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "brandName": "雪佛兰", "brandId": 0, "passengerNo": 5, "sourcePicInfos": [{"sourceName": "年款/颜色等以实物为准", "source": 0, "type": 2, "picList": [{"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/0410u120008at350uDF48.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/0412t120008at3ett66A0.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/0414c120008at2ypz0D5C.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/0414w120008at5huo1DE9.jpg"}]}, {"sourceName": "年款/颜色等以实物为准", "source": 1, "type": 2, "picList": [{"sortNum": 0, "imageUrl": "https://dimg04.c-ctrip.com//images/0410x120008at5wkp72C0.jpg"}]}], "doorNo": 4, "isSpecialized": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg", "https://dimg04.c-ctrip.com//images/0410u120008at350uDF48.jpg", "https://dimg04.c-ctrip.com//images/0414c120008at2ypz0D5C.jpg", "https://dimg04.c-ctrip.com//images/0412t120008at3ett66A0.jpg", "https://dimg04.c-ctrip.com//images/0414w120008at5huo1DE9.jpg"], "groupCode": "2", "gearbox": "手自一体变速箱(AT)", "luggageNo": 2, "brandEName": "雪佛兰", "groupSubClassCode": "", "groupName": "经济型", "transmissionType": 1}, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "uniqSign": "120010172100001428469giB8E12N9dw6Dt4hAb0", "baseResponse": {"code": "200", "returnMsg": "OK", "isSuccess": true}, "ResponseStatus": {"Extension": [{"Value": "2662730911767527582", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a056b21-457279-22604", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1646205537601+0800)/"}, "isFromSearch": false, "specificProductGroups": {"vendorPriceList": [{"adverts": 0, "card": 0, "commentInfo": {"overallRating": "0.0", "qCommentCount": 0, "vendorDesc": "三亚凤凰国际机场送车点", "level": "", "maximumRating": 5, "commentLabel": "", "qExposed": "0.0", "hasComment": 0, "commentCount": 0}, "storeScore": 80, "uniqueCode": "1f2352ebb7ba4f5c97727fb9b7e04bc4", "pickOffFee": 0, "priceInfo": {"oTPrice": 216, "naked": true, "priceVersion": "AWQB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlUkLATQm+UuHvWcDvCmax5eT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/c3uPeIlh033v34xDsoPmvW3hVEuaGnVDIN1e/eaMzUua05rVZEj792sPJY5lW1h72N7gUtHiwtELQJ9BVCJ5AAiX4u/eBFfWNaA3FdkEq28Tj4lTmgif2iBj4CrOI1G42KGWlXTdmZRcoWgX+u0SHxiXhyIqn1eR4UcxW3StLrwsGE4r5pG/sK1vS3drmXwH8", "deductInfos": [{"totalAmount": 80, "payofftype": 2}], "currentOriginalDailyPrice": 58, "priceType": 1, "currentDailyPrice": 18, "currentTotalPrice": 136, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 0, "vendorName": "小豆租车", "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Brand_雪佛兰_雪佛兰科沃兹", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 256, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 4096, "checkType": 0}], "rDistance": 0.5428, "actId": "3422", "pickUpFee": 0, "payModes": [2], "allTags": [{"title": "五一大促", "category": 3, "type": 3, "code": "30", "sortNum": 0, "colorCode": "15", "labelCode": "3543", "groupCode": "MarketGroup106", "amountTitle": "已减280", "groupId": 1, "mergeId": 0}, {"title": "押金双免", "category": 2, "type": 1, "code": "7", "sortNum": 5, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 8, "colorCode": "8", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 2}, {"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 2}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 15, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "全国连锁", "category": 9, "type": 1, "code": "20", "sortNum": 10000, "colorCode": "1", "labelCode": "3757", "groupCode": "MarketGroup1335", "groupId": 2, "mergeId": 0}], "fees": [{"amount": 36, "detail": [{"code": "1001", "amount": 116, "amountDesc": "¥116", "name": "租车费"}, {"code": "11037", "amount": 50, "amountDesc": "¥50", "name": "优惠券"}, {"code": "3422", "amount": 30, "amountDesc": "¥30", "name": "今日特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥18", "originalDailyPrice": 58, "subAmount": 18, "name": "车辆租金", "amountStr": "¥36"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 70, "amountStr": "¥70", "detail": [{"code": "1002", "amount": 70, "amountDesc": "¥70", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 136, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥216", "subAmount": 216, "name": "总价", "amountStr": "¥136"}], "vendorLogo": "https://dimg04.c-ctrip.com/images/04107120008pul4dm4CB7.jpg", "distance": 0.5428, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "自行前往门店取还车，距门店直线543米", "ctripVehicleCode": "14415", "cyVendorName": "小豆租车", "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {"bAdjustDailyPrice": 0, "bAdjustTotalPrice": 0}, "freeDeposit": 0, "showVendor": false, "stock": 1, "reference": {"aType": 0, "hotType": 0, "pLevel": 0, "pCityId": 43, "pickWayInfo": 0, "pickUpOnDoor": false, "freeIllegalDeposit": false, "hot": 0, "isEasyLife": false, "payMode": 2, "priceVersion": "AWQB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCDTb2K6ERpNDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlUkLATQm+UuHvWcDvCmax5eT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/c3uPeIlh033v34xDsoPmvW3hVEuaGnVDIN1e/eaMzUua05rVZEj792sPJY5lW1h72N7gUtHiwtELQJ9BVCJ5AAiX4u/eBFfWNaA3FdkEq28Tj4lTmgif2iBj4CrOI1G42KGWlXTdmZRcoWgX+u0SHxiXhyIqn1eR4UcxW3StLrwsGE4r5pG/sK1vS3drmXwH8", "elct": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup1317", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "rStoreCode": "225861", "alipay": false, "vendorCode": "13039", "productCode": "13039_169372_225861_225861", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "rCityId": 43, "pStoreNav": "自行前往门店取还车", "vehicleCode": "169372", "bizVendorCode": "13039", "vendorVehicleCode": "kewozi0715", "dropOffOnDoor": false, "gsDesc": "低价省钱", "rRc": 0, "rLevel": 0, "noDepositFilter": {}, "pCType": 0, "pStoreCode": "225861", "priceType": 1, "pRc": 0, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 216, "recommendOrder": 0, "mergeId": 108, "vdegree": "0", "rectype": 1, "totalDailyPrice": 18, "mergeInfo": [{"storeId": "225861", "vehicleId": "kewozi0715"}], "isrec": false, "cvid": 14415, "rentalamount": 36}, "vcExtendRequest": {"vendorVehicleId": "169372"}, "gsId": 0, "returnWayInfo": 0, "isSelect": false, "age": 30, "vehicleDegree": "0", "packageType": 0, "rStoreNav": "自行前往门店取还车", "adjustVersion": "", "rCType": 0, "vendorSupportZhima": true, "fType": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": true, "isOrderVehicle": false, "platformCode": "0"}, {"adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 16262, "vendorDesc": "凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "店员热情", "qExposed": "4.9", "hasComment": 1, "commentCount": 16262}, "storeScore": 80, "uniqueCode": "2b7c6375196a4cecb0db28f6fb2b02bf", "pickOffFee": 0, "priceInfo": {"oTPrice": 191, "naked": true, "priceVersion": "AV0B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZXCJO9j9gLhryT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7Uixlv3FOddkNUoAtIy+cOdA4XZI+GZXBRmovJVLJL2OrXJ0Q58bLxSDn2p8hMUUMoZhflRQ2sfuxhS0gwNORk96wplivfa+Tr5rl37vBIAvFezVboK8gIsqzAnuS2ip1deOEtP4Euc4QKMKV7jjfxCaq/8kzNAXFBZcCMFBToyFtbIrmeVE7ktqy30FqTAGJX8Qz1VxErD05i0evuvoXgBZw==", "deductInfos": [{"totalAmount": 50, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 13, "currentTotalPrice": 141, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 1, "vendorName": "tongye_local2", "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Brand_雪佛兰_雪佛兰科沃兹", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 16, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 4096, "checkType": 0}], "rDistance": 0.1826, "actId": "", "pickUpFee": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 5, "mergeId": 1, "groupCode": "MarketGroup1347", "code": "7", "title": "押金双免", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 1, "sortNum": 15, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 70, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "延误时免费留车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3779"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减50", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "fees": [{"amount": 26, "detail": [{"code": "1001", "amount": 76, "amountDesc": "¥76", "name": "租车费"}, {"code": "11037", "amount": 50, "amountDesc": "¥50", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥13", "originalDailyPrice": 38, "subAmount": 13, "name": "车辆租金", "amountStr": "¥26"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 141, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥191", "subAmount": 191, "name": "总价", "amountStr": "¥141"}], "vendorLogo": "https://dimg.uat.qa.nt.ctripcorp.com/images/0yc0m12000004sn4yDCB9.jpg", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "免费站内取还车", "ctripVehicleCode": "14415", "cyVendorName": "tongye_local2", "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {"bAdjustDailyPrice": 0, "bAdjustTotalPrice": 0}, "freeDeposit": 0, "showVendor": false, "stock": 1, "reference": {"aType": 0, "hotType": 0, "pLevel": 1, "pCityId": 43, "pickWayInfo": 16, "pickUpOnDoor": true, "freeIllegalDeposit": false, "hot": 0, "isEasyLife": true, "payMode": 2, "priceVersion": "AV0B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZXCJO9j9gLhryT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7Uixlv3FOddkNUoAtIy+cOdA4XZI+GZXBRmovJVLJL2OrXJ0Q58bLxSDn2p8hMUUMoZhflRQ2sfuxhS0gwNORk96wplivfa+Tr5rl37vBIAvFezVboK8gIsqzAnuS2ip1deOEtP4Euc4QKMKV7jjfxCaq/8kzNAXFBZcCMFBToyFtbIrmeVE7ktqy30FqTAGJX8Qz1VxErD05i0evuvoXgBZw==", "elct": 0, "packageId": "Secure", "rStoreCode": "150693", "alipay": false, "vendorCode": "13031", "productCode": "13031_127517_150693_150693", "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减50", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "sendTypeForPickUpCar": 10, "rCityId": 43, "pStoreNav": "免费站内取还车", "vehicleCode": "127517", "bizVendorCode": "13031", "vendorVehicleCode": "18200", "dropOffOnDoor": true, "sendTypeForPickOffCar": 10, "rRc": 2, "rLevel": 1, "noDepositFilter": {}, "pCType": 0, "pStoreCode": "150693", "priceType": 1, "pRc": 2, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 191, "recommendOrder": 0, "mergeId": 108, "vdegree": "0", "rectype": 1, "totalDailyPrice": 13, "mergeInfo": [{"storeId": "150693", "vehicleId": "18200"}], "isrec": false, "cvid": 14415, "rentalamount": 26}, "vcExtendRequest": {"vendorVehicleId": "127517"}, "comPriceCode": "eyJwYWNrYWdlaWQiOiJTZWN1cmUiLCJzdG9yZWlkIjoyNzYxLCJjYXJ0eXBlaWQiOjE4MjAwLCJ0b3RhbCI6MTkxfQ==", "gsId": 0, "returnWayInfo": 16, "isSelect": true, "age": 30, "vehicleDegree": "0", "packageType": 1, "rStoreNav": "免费站内取还车", "adjustVersion": "", "rCType": 0, "vendorSupportZhima": true, "fType": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "isOrderVehicle": false, "platformCode": "0"}, {"adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 25321, "vendorDesc": "机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆很新", "qExposed": "4.9", "hasComment": 1, "commentCount": 25321}, "storeScore": 80, "uniqueCode": "62c22ef1529349d6a4dd8561b7104e8c", "pickOffFee": 0, "priceInfo": {"oTPrice": 335, "naked": true, "priceVersion": "AVsB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlQ5T49U2hBb0O03DT0/0cvOEl0Onmt2oizZV+ncKvud8fo/Y72hsWDyQ+VwiTuDrVBE9FujwQP9KcoKRQy+VuEi8G7JgRC8AcuM584IseDqTZRuXJsUhv/kwGd98Tx14r3IQ7Xhfj9N/B11BWqE93U/mgQ++P/OhgQzWYV9d1mdehdkj4ZlcFGZ1IltPWQc7uaWaRKE58Xg+P2ugN/IP7MCmb7AjX93IfIJk85IO6Yrt2w+AlQT/Gy7P515Lh5mAmjKCvopmRz8MXOpqngCzPNDBwUbiZUNCQU7yyb/NdUA5sKx2/I+FtKlfxOfbeD0w7w==", "deductInfos": [{"totalAmount": 80, "payofftype": 2}], "currentOriginalDailyPrice": 100, "priceType": 1, "currentDailyPrice": 60, "currentTotalPrice": 255, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 2, "vendorName": "懒人行租车", "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Brand_雪佛兰_雪佛兰科沃兹", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4096, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 32768, "checkType": 0}], "rDistance": 0.0576, "actId": "3422", "pickUpFee": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 5, "mergeId": 1, "groupCode": "MarketGroup1347", "code": "7", "title": "押金双免", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 1, "sortNum": 15, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 2, "sortNum": 70, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "延误时免费留车", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3779"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}], "fees": [{"amount": 120, "detail": [{"code": "1001", "amount": 200, "amountDesc": "¥200", "name": "租车费"}, {"code": "11037", "amount": 50, "amountDesc": "¥50", "name": "优惠券"}, {"code": "3422", "amount": 30, "amountDesc": "¥30", "name": "今日特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥60", "originalDailyPrice": 100, "subAmount": 60, "name": "车辆租金", "amountStr": "¥120"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 255, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥335", "subAmount": 335, "name": "总价", "amountStr": "¥255"}], "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/30164.jpg", "distance": 0.0576, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "免费站内取还车", "ctripVehicleCode": "14415", "cyVendorName": "懒人行租车", "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {"bAdjustDailyPrice": 100, "bAdjustTotalPrice": 200}, "freeDeposit": 0, "showVendor": false, "stock": 39, "reference": {"aType": 0, "hotType": 0, "pLevel": 0, "pCityId": 43, "pickWayInfo": 16, "pickUpOnDoor": false, "freeIllegalDeposit": false, "hot": 0, "isEasyLife": true, "payMode": 2, "priceVersion": "AVsB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlQ5T49U2hBb0O03DT0/0cvOEl0Onmt2oizZV+ncKvud8fo/Y72hsWDyQ+VwiTuDrVBE9FujwQP9KcoKRQy+VuEi8G7JgRC8AcuM584IseDqTZRuXJsUhv/kwGd98Tx14r3IQ7Xhfj9N/B11BWqE93U/mgQ++P/OhgQzWYV9d1mdehdkj4ZlcFGZ1IltPWQc7uaWaRKE58Xg+P2ugN/IP7MCmb7AjX93IfIJk85IO6Yrt2w+AlQT/Gy7P515Lh5mAmjKCvopmRz8MXOpqngCzPNDBwUbiZUNCQU7yyb/NdUA5sKx2/I+FtKlfxOfbeD0w7w==", "elct": 0, "packageId": "vbkpkg14533af4073dd48dc9d1d9259e93bd602", "rStoreCode": "11006", "alipay": false, "vendorCode": "30164", "productCode": "164_18456_11006_11006", "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup1317", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "sendTypeForPickUpCar": 0, "rCityId": 43, "pStoreNav": "免费站内取还车", "vehicleCode": "18456", "bizVendorCode": "164", "vendorVehicleCode": "14415", "dropOffOnDoor": false, "sendTypeForPickOffCar": 0, "rRc": 2, "rLevel": 0, "noDepositFilter": {}, "pCType": 0, "pStoreCode": "11006", "priceType": 1, "pRc": 2, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 335, "recommendOrder": 0, "mergeId": 108, "vdegree": "0", "rectype": 1, "totalDailyPrice": 60, "mergeInfo": [{"storeId": "11006", "vehicleId": "14415"}], "isrec": false, "cvid": 14415, "rentalamount": 120}, "vcExtendRequest": {"vendorVehicleId": "18456"}, "comPriceCode": "eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiMTIzMTIwMjExMzMzMTliMTRlNzY0ZS0wNTg1LTQ1MDEtODBiNC1iZjRiNmJhYzdhNGQifQ==", "gsId": 0, "returnWayInfo": 16, "isSelect": true, "age": 30, "vehicleDegree": "0", "packageType": 1, "rStoreNav": "免费站内取还车", "adjustVersion": "", "rCType": 0, "vendorSupportZhima": true, "fType": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "isOrderVehicle": false, "platformCode": "10"}, {"adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 10892, "vendorDesc": "泰信吉凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "车况好", "qExposed": "4.9", "hasComment": 1, "commentCount": 10892}, "storeScore": 80, "uniqueCode": "14675fabcc5342ca915c12a41e06f857", "pickOffFee": 0, "priceInfo": {"oTPrice": 366, "naked": true, "priceVersion": "AWQB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbcHXmN+lwahh+T0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cm6ii9JJnI9X34xDsoPmvW+TNyV/BAF65N1e/eaMzUua05rVZEj792iCKjlZYWKKb6leq55zxkzG/aFBteBZ0waZvsCNf3ch8gmTzkg7piu21fFSEE+cNa8/nXkuHmYCaZydwYq3ZXSEmexv4KTleLKIP0Fbh1/0lO1d0QovCAXIAs/1INffy4WrH08ie2wEs", "deductInfos": [{"totalAmount": 80, "payofftype": 2}], "currentOriginalDailyPrice": 128, "priceType": 1, "currentDailyPrice": 88, "currentTotalPrice": 286, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 3, "vendorName": "泰信吉租车", "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Brand_雪佛兰_雪佛兰科沃兹", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 16777216, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 4, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 49152, "checkType": 0}], "rDistance": 0.24, "actId": "3422", "pickUpFee": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 5, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "押金双免", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3501"}, {"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 1, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "有损取消", "colorCode": "4", "type": 2, "groupId": 3, "labelCode": "3683"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}], "fees": [{"amount": 176, "detail": [{"code": "1001", "amount": 256, "amountDesc": "¥256", "name": "租车费"}, {"code": "11037", "amount": 50, "amountDesc": "¥50", "name": "优惠券"}, {"code": "3422", "amount": 30, "amountDesc": "¥30", "name": "今日特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥88", "originalDailyPrice": 128, "subAmount": 88, "name": "车辆租金", "amountStr": "¥176"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 90, "amountStr": "¥90", "detail": [{"code": "1002", "amount": 90, "amountDesc": "¥90", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 286, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥366", "subAmount": 366, "name": "总价", "amountStr": "¥286"}], "vendorLogo": "http://pic.ctrip.com/car_isd/vendorlogo/32231.jpg", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "14415", "cyVendorName": "泰信吉租车", "cashbackPre": "", "isSelect": true, "priceDailys": [], "adjustPriceInfo": {"bAdjustDailyPrice": 128, "bAdjustTotalPrice": 256}, "freeDeposit": 0, "showVendor": false, "stock": 21, "reference": {"aType": 0, "hotType": 0, "pLevel": 1, "pCityId": 43, "pickWayInfo": 1, "pickUpOnDoor": true, "freeIllegalDeposit": false, "hot": 0, "isEasyLife": false, "payMode": 2, "priceVersion": "AWQB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlb3uj+gbXPbcHXmN+lwahh+T0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cm6ii9JJnI9X34xDsoPmvW+TNyV/BAF65N1e/eaMzUua05rVZEj792iCKjlZYWKKb6leq55zxkzG/aFBteBZ0waZvsCNf3ch8gmTzkg7piu21fFSEE+cNa8/nXkuHmYCaZydwYq3ZXSEmexv4KTleLKIP0Fbh1/0lO1d0QovCAXIAs/1INffy4WrH08ie2wEs", "elct": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup1317", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "rStoreCode": "39450", "alipay": false, "vendorCode": "32231", "productCode": "2231_18456_39450_39450", "sendTypeForPickUpCar": 10, "sendTypeForPickOffCar": 10, "rCityId": 43, "pStoreNav": "店员免费上门送取车", "vehicleCode": "18456", "bizVendorCode": "2231", "vendorVehicleCode": "14415", "dropOffOnDoor": true, "rRc": 0, "rLevel": 1, "noDepositFilter": {}, "pCType": 0, "pStoreCode": "39450", "priceType": 1, "pRc": 0, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 366, "recommendOrder": 0, "mergeId": 108, "vdegree": "0", "rectype": 1, "totalDailyPrice": 88, "mergeInfo": [{"storeId": "39450", "vehicleId": "14415"}], "isrec": false, "cvid": 14415, "rentalamount": 176}, "vcExtendRequest": {"vendorVehicleId": "18456"}, "comPriceCode": "eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiMTIzMTIwMjExMzMzMTliMTRlNzY0ZS0wNTg1LTQ1MDEtODBiNC1iZjRiNmJhYzdhNGQifQ==", "gsId": 0, "returnWayInfo": 1, "isSelect": true, "age": 30, "vehicleDegree": "0", "packageType": 0, "rStoreNav": "店员免费上门送取车", "adjustVersion": "", "rCType": 0, "vendorSupportZhima": true, "fType": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "isOrderVehicle": false, "platformCode": "10"}, {"adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 1278, "vendorDesc": "明昊凤凰机场店", "level": "超棒", "maximumRating": 5, "commentLabel": "店员热情", "qExposed": "4.9", "hasComment": 1, "commentCount": 1278}, "storeScore": 80, "uniqueCode": "4e12ff2557ff49cba27f0920007e28f9", "pickOffFee": 0, "priceInfo": {"oTPrice": 191, "naked": true, "priceVersion": "AV0B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZXCJO9j9gLhryT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7Uixlv3FOddkNUoAtIy+cOdA4XZI+GZXBRmovJVLJL2OrXJ0Q58bLxSDn2p8hMUUMoZhflRQ2sfuxhS0gwNORk96wplivfa+Tr5rl37vBIAvFezVboK8gIsqzAnuS2ip1deOEtP4Euc4QKMKV7jjfxCaq/8kzNAXFBZcCMFBToyFtbIrmeVE7ktqy30FqTAGJX8Qz1VxErD05hhdsbKoXm7cw==", "deductInfos": [{"totalAmount": 50, "payofftype": 2}], "currentOriginalDailyPrice": 38, "priceType": 1, "currentDailyPrice": 13, "currentTotalPrice": 141, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 4, "vendorName": "明昊租车", "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Brand_雪佛兰_雪佛兰科沃兹", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 32, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 4096, "checkType": 0}], "rDistance": 0.1826, "actId": "", "pickUpFee": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 5, "mergeId": 1, "groupCode": "MarketGroup1347", "code": "7", "title": "押金双免", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 9, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "三年内车龄", "colorCode": "8", "type": 1, "groupId": 2, "labelCode": "3548"}, {"category": 1, "sortNum": 15, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减50", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "fees": [{"amount": 26, "detail": [{"code": "1001", "amount": 76, "amountDesc": "¥76", "name": "租车费"}, {"code": "11037", "amount": 50, "amountDesc": "¥50", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥13", "originalDailyPrice": 38, "subAmount": 13, "name": "车辆租金", "amountStr": "¥26"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 141, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥191", "subAmount": 191, "name": "总价", "amountStr": "¥141"}], "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/minghao.png", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "14415", "cyVendorName": "明昊租车", "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {"bAdjustDailyPrice": 0, "bAdjustTotalPrice": 0}, "freeDeposit": 0, "showVendor": false, "stock": 1, "reference": {"aType": 0, "hotType": 0, "pLevel": 1, "pCityId": 43, "pickWayInfo": 1, "pickUpOnDoor": true, "freeIllegalDeposit": false, "hot": 0, "isEasyLife": true, "payMode": 2, "priceVersion": "AV0B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZXCJO9j9gLhryT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7Uixlv3FOddkNUoAtIy+cOdA4XZI+GZXBRmovJVLJL2OrXJ0Q58bLxSDn2p8hMUUMoZhflRQ2sfuxhS0gwNORk96wplivfa+Tr5rl37vBIAvFezVboK8gIsqzAnuS2ip1deOEtP4Euc4QKMKV7jjfxCaq/8kzNAXFBZcCMFBToyFtbIrmeVE7ktqy30FqTAGJX8Qz1VxErD05hhdsbKoXm7cw==", "elct": 0, "packageId": "Secure", "rStoreCode": "150743", "alipay": false, "vendorCode": "13032", "productCode": "13032_128368_150743_150743", "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减50", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "sendTypeForPickUpCar": 10, "rCityId": 43, "pStoreNav": "店员免费上门送取车", "vehicleCode": "128368", "bizVendorCode": "13032", "vendorVehicleCode": "18200", "dropOffOnDoor": true, "sendTypeForPickOffCar": 10, "rRc": 0, "rLevel": 1, "noDepositFilter": {}, "pCType": 0, "pStoreCode": "150743", "priceType": 1, "pRc": 0, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 191, "recommendOrder": 0, "mergeId": 108, "vdegree": "0", "rectype": 1, "totalDailyPrice": 13, "mergeInfo": [{"storeId": "150743", "vehicleId": "18200"}], "isrec": false, "cvid": 14415, "rentalamount": 26}, "vcExtendRequest": {"vendorVehicleId": "128368"}, "comPriceCode": "eyJwYWNrYWdlaWQiOiJTZWN1cmUiLCJzdG9yZWlkIjo4NTM2LCJjYXJ0eXBlaWQiOjE4MjAwLCJ0b3RhbCI6MTkxfQ==", "gsId": 0, "returnWayInfo": 1, "isSelect": false, "age": 30, "vehicleDegree": "0", "packageType": 1, "rStoreNav": "店员免费上门送取车", "adjustVersion": "", "rCType": 0, "vendorSupportZhima": true, "fType": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "isOrderVehicle": false, "platformCode": "0"}, {"adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 10869, "vendorDesc": "火车站店", "level": "超棒", "maximumRating": 5, "commentLabel": "店员热情", "qExposed": "4.9", "hasComment": 1, "commentCount": 10869}, "storeScore": 80, "uniqueCode": "70110c270c234e00a759ee1e444b36fb", "pickOffFee": 0, "priceInfo": {"oTPrice": 341, "naked": true, "priceVersion": "AWQB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZXHXmN+lwahh+T0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cMKyH2xi1YeT34xDsoPmvW7Rp+0yuL7g6N1e/eaMzUua05rVZEj792keK5pEfFM+86leq55zxkzFnofIyBFVZoKZvsCNf3ch8gmTzkg7piu0oZaVdN2ZlF8/nXkuHmYCatJx4iQrf1tomexv4KTleLKIP0Fbh1/0lO1d0QovCAXLMT1ckkdNaQ+j7OCXB824W", "deductInfos": [{"totalAmount": 150, "payofftype": 2}], "currentOriginalDailyPrice": 108, "priceType": 1, "currentDailyPrice": 33, "currentTotalPrice": 191, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 5, "vendorName": "君驰租车", "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Brand_雪佛兰_雪佛兰科沃兹", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 16384, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 0, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}], "rDistance": 0.0576, "actId": "3595", "pickUpFee": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 5, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "押金双免", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 1, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "有损取消", "colorCode": "4", "type": 2, "groupId": 3, "labelCode": "3683"}, {"category": 3, "sortNum": 10000, "amountTitle": "已减50", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "fees": [{"amount": 66, "detail": [{"code": "1001", "amount": 216, "amountDesc": "¥216", "name": "租车费"}, {"code": "11037", "amount": 50, "amountDesc": "¥50", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥33", "originalDailyPrice": 108, "subAmount": 33, "name": "车辆租金", "amountStr": "¥66"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 90, "amountStr": "¥90", "detail": [{"code": "1002", "amount": 90, "amountDesc": "¥90", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 191, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥341", "subAmount": 341, "name": "总价", "amountStr": "¥191"}], "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/30190.jpg", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "14415", "cyVendorName": "君驰租车", "cashbackPre": "", "isSelect": false, "priceDailys": [], "urge": "剩3辆", "freeDeposit": 0, "adjustPriceInfo": {"bAdjustDailyPrice": 108, "bAdjustTotalPrice": 216}, "showVendor": false, "stock": 3, "reference": {"aType": 0, "hotType": 0, "pLevel": 1, "pCityId": 43, "pickWayInfo": 1, "pickUpOnDoor": true, "freeIllegalDeposit": false, "hot": 0, "isEasyLife": false, "payMode": 2, "priceVersion": "AWQB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZXHXmN+lwahh+T0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cMKyH2xi1YeT34xDsoPmvW7Rp+0yuL7g6N1e/eaMzUua05rVZEj792keK5pEfFM+86leq55zxkzFnofIyBFVZoKZvsCNf3ch8gmTzkg7piu0oZaVdN2ZlF8/nXkuHmYCatJx4iQrf1tomexv4KTleLKIP0Fbh1/0lO1d0QovCAXLMT1ckkdNaQ+j7OCXB824W", "elct": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减50", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "rStoreCode": "43938", "alipay": false, "vendorCode": "30190", "productCode": "190_18456_43938_43938", "sendTypeForPickUpCar": 10, "sendTypeForPickOffCar": 10, "rCityId": 43, "pStoreNav": "店员免费上门送取车", "vehicleCode": "18456", "bizVendorCode": "190", "vendorVehicleCode": "14415", "dropOffOnDoor": true, "rRc": 0, "rLevel": 1, "noDepositFilter": {}, "pCType": 0, "pStoreCode": "43938", "priceType": 1, "pRc": 0, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 341, "recommendOrder": 0, "mergeId": 108, "vdegree": "0", "rectype": 1, "totalDailyPrice": 33, "mergeInfo": [{"storeId": "43938", "vehicleId": "14415"}], "isrec": false, "cvid": 14415, "rentalamount": 66}, "vcExtendRequest": {"vendorVehicleId": "18456"}, "comPriceCode": "eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiMTIzMTIwMjExMzMzMTliMTRlNzY0ZS0wNTg1LTQ1MDEtODBiNC1iZjRiNmJhYzdhNGQifQ==", "gsId": 0, "returnWayInfo": 1, "isSelect": false, "age": 30, "vehicleDegree": "0", "packageType": 0, "rStoreNav": "店员免费上门送取车", "adjustVersion": "", "rCType": 0, "vendorSupportZhima": true, "fType": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "isOrderVehicle": false, "platformCode": "10"}, {"adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 66, "vendorDesc": "海南青松汽车服务有限公司", "level": "超棒", "maximumRating": 5, "commentLabel": "店员热情", "qExposed": "4.9", "hasComment": 1, "commentCount": 66}, "storeScore": 80, "uniqueCode": "a8a2a21ba2af4383968de8653789951d", "pickOffFee": 0, "priceInfo": {"oTPrice": 491, "naked": true, "priceVersion": "AWYB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlR2j4tcqWeLyHXmN+lwahh+T0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cOXwP69FwxurlmD7Fjn1+IKbW/Rj13DIwiz4+iZQE8qZQdTVBe/z8CSf/3BBYRN5rN7gUtHiwtEIqbjcFTKyqkdtTu24C4IkU7T331tWsN4bPpqQF4o/wejs7sx5Ik0exk/0iX8qrhu9c6mqeALM80MHBRuJlQ0JB1jBK+HXLpuwX+dMipsLRdqI4BMThWEHd", "deductInfos": [{"totalAmount": 80, "payofftype": 2}], "currentOriginalDailyPrice": 188, "priceType": 1, "currentDailyPrice": 148, "currentTotalPrice": 411, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 6, "vendorName": "青松租车", "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Brand_雪佛兰_雪佛兰科沃兹", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_1", "binaryDigit": 16777216, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 15, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 49152, "checkType": 0}], "rDistance": 3.1842, "actId": "3422", "pickUpFee": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 5, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "押金双免", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车影像", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3501"}, {"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 1, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "有损取消", "colorCode": "4", "type": 2, "groupId": 3, "labelCode": "3683"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}], "fees": [{"amount": 296, "detail": [{"code": "1001", "amount": 376, "amountDesc": "¥376", "name": "租车费"}, {"code": "11037", "amount": 50, "amountDesc": "¥50", "name": "优惠券"}, {"code": "3422", "amount": 30, "amountDesc": "¥30", "name": "今日特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥148", "originalDailyPrice": 188, "subAmount": 148, "name": "车辆租金", "amountStr": "¥296"}, {"code": "CAR_SERVICE_FEE", "amount": 25, "amountStr": "¥25", "detail": [{"code": "1003", "amount": 25, "amountDesc": "¥25", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 90, "amountStr": "¥90", "detail": [{"code": "1002", "amount": 90, "amountDesc": "¥90", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 411, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥491", "subAmount": 491, "name": "总价", "amountStr": "¥411"}], "vendorLogo": "http://pic.c-ctrip.com/car_isd/vendorlogo/63109.jpg", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "14415", "cyVendorName": "青松租车", "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {"bAdjustDailyPrice": 188, "bAdjustTotalPrice": 376}, "freeDeposit": 0, "showVendor": false, "stock": 1, "reference": {"aType": 0, "hotType": 0, "pLevel": 1, "pCityId": 43, "pickWayInfo": 1, "pickUpOnDoor": true, "freeIllegalDeposit": false, "hot": 0, "isEasyLife": false, "payMode": 2, "priceVersion": "AWYB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlR2j4tcqWeLyHXmN+lwahh+T0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cOXwP69FwxurlmD7Fjn1+IKbW/Rj13DIwiz4+iZQE8qZQdTVBe/z8CSf/3BBYRN5rN7gUtHiwtEIqbjcFTKyqkdtTu24C4IkU7T331tWsN4bPpqQF4o/wejs7sx5Ik0exk/0iX8qrhu9c6mqeALM80MHBRuJlQ0JB1jBK+HXLpuwX+dMipsLRdqI4BMThWEHd", "elct": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup1317", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "rStoreCode": "133416", "alipay": false, "vendorCode": "63109", "productCode": "33109_35422_133416_133416", "sendTypeForPickUpCar": 10, "sendTypeForPickOffCar": 10, "rCityId": 43, "pStoreNav": "店员免费上门送取车", "vehicleCode": "35422", "bizVendorCode": "33109", "vendorVehicleCode": "20024619", "dropOffOnDoor": true, "rRc": 0, "rLevel": 1, "noDepositFilter": {}, "pCType": 0, "pStoreCode": "133416", "priceType": 1, "pRc": 0, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 491, "recommendOrder": 0, "mergeId": 108, "vdegree": "0", "rectype": 1, "totalDailyPrice": 148, "mergeInfo": [{"storeId": "133416", "vehicleId": "20024619"}], "isrec": false, "cvid": 14415, "rentalamount": 296}, "vcExtendRequest": {"vendorVehicleId": "35422"}, "comPriceCode": "eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiMTIzMTIwMjExMzMzMTliMTRlNzY0ZS0wNTg1LTQ1MDEtODBiNC1iZjRiNmJhYzdhNGQifQ==", "gsId": 0, "returnWayInfo": 1, "isSelect": false, "age": 30, "vehicleDegree": "0", "packageType": 0, "rStoreNav": "店员免费上门送取车", "adjustVersion": "", "rCType": 0, "vendorSupportZhima": true, "fType": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "isOrderVehicle": false, "platformCode": "10"}, {"adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 3, "vendorDesc": "三亚-海航城店", "level": "超棒", "maximumRating": 5, "qExposed": "5.0", "hasComment": 1, "commentCount": 3}, "storeScore": 80, "uniqueCode": "61ace66142a546cf87ee3fe75c5b468a", "pickOffFee": 0, "priceInfo": {"oTPrice": 461, "naked": true, "priceVersion": "AWYB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZXHXmN+lwahh+T0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cv2hQbXgWdMHlmD7Fjn1+IOMBg5dM4jWBiz4+iZQE8qZQdTVBe/z8CU0xm4cmP/IfwsbWidGqqaTR8JYitSTAZdtTu24C4IkU7T331tWsN4bbM0UGRp9/DTs7sx5Ik0exvfnaGqj56kpc6mqeALM80MHBRuJlQ0JB1jBK+HXLpuwX+dMipsLRdngrJQ7idGdO", "deductInfos": [{"totalAmount": 80, "payofftype": 2}], "currentOriginalDailyPrice": 168, "priceType": 1, "currentDailyPrice": 128, "currentTotalPrice": 381, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 7, "vendorName": "凹凸出行", "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Brand_雪佛兰_雪佛兰科沃兹", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 4096, "checkType": 0}], "rDistance": 2.3461, "actId": "3422", "pickUpFee": 0, "payModes": [2], "allTags": [{"category": 1, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "有损取消", "colorCode": "4", "type": 2, "groupId": 3, "labelCode": "3683"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}], "fees": [{"amount": 256, "detail": [{"code": "1001", "amount": 336, "amountDesc": "¥336", "name": "租车费"}, {"code": "11037", "amount": 50, "amountDesc": "¥50", "name": "优惠券"}, {"code": "3422", "amount": 30, "amountDesc": "¥30", "name": "今日特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥128", "originalDailyPrice": 168, "subAmount": 128, "name": "车辆租金", "amountStr": "¥256"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 90, "amountStr": "¥90", "detail": [{"code": "1002", "amount": 90, "amountDesc": "¥90", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 381, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥461", "subAmount": 461, "name": "总价", "amountStr": "¥381"}], "vendorLogo": "", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "14415", "cyVendorName": "凹凸出行", "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {"bAdjustDailyPrice": 0, "bAdjustTotalPrice": 0}, "freeDeposit": 0, "showVendor": false, "stock": 1, "reference": {"aType": 0, "hotType": 0, "pLevel": 256, "pCityId": 43, "pickWayInfo": 1, "pickUpOnDoor": true, "freeIllegalDeposit": false, "hot": 0, "isEasyLife": false, "payMode": 2, "priceVersion": "AWYB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlZlNHkKUOxZXHXmN+lwahh+T0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cv2hQbXgWdMHlmD7Fjn1+IOMBg5dM4jWBiz4+iZQE8qZQdTVBe/z8CU0xm4cmP/IfwsbWidGqqaTR8JYitSTAZdtTu24C4IkU7T331tWsN4bbM0UGRp9/DTs7sx5Ik0exvfnaGqj56kpc6mqeALM80MHBRuJlQ0JB1jBK+HXLpuwX+dMipsLRdngrJQ7idGdO", "elct": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup1317", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "rStoreCode": "131081", "alipay": false, "vendorCode": "13027", "productCode": "13027_117249_131081_131081", "sendTypeForPickUpCar": 10, "sendTypeForPickOffCar": 10, "rCityId": 43, "pStoreNav": "店员免费上门送取车", "vehicleCode": "117249", "bizVendorCode": "13027", "vendorVehicleCode": "34033_PMS", "dropOffOnDoor": true, "rRc": 0, "rLevel": 256, "noDepositFilter": {}, "pCType": 0, "pStoreCode": "131081", "priceType": 1, "pRc": 0, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 461, "recommendOrder": 0, "mergeId": 108, "vdegree": "0", "rectype": 1, "totalDailyPrice": 128, "mergeInfo": [{"storeId": "131081", "vehicleId": "34033_PMS"}], "isrec": false, "cvid": 14415, "rentalamount": 256}, "vcExtendRequest": {"vendorVehicleId": "117249"}, "gsId": 0, "returnWayInfo": 1, "isSelect": false, "age": 30, "vehicleDegree": "0", "packageType": 0, "rStoreNav": "店员免费上门送取车", "adjustVersion": "", "rCType": 0, "vendorSupportZhima": false, "fType": 0}, "easyLifeInfo": {"isEasyLife": false}, "isMinTPriceVendor": false, "isOrderVehicle": false, "platformCode": "0"}, {"adverts": 0, "card": 0, "commentInfo": {"overallRating": "4.9", "qCommentCount": 2448, "vendorDesc": "华庭天下店", "level": "超棒", "maximumRating": 5, "commentLabel": "车辆很新", "qExposed": "4.9", "hasComment": 1, "commentCount": 2448}, "storeScore": 80, "uniqueCode": "c77969f797534ba7b37a394f924e1ead", "pickOffFee": 0, "priceInfo": {"oTPrice": 461, "naked": true, "priceVersion": "AVsB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAla55UKi3kcIDDD7iOPvxEEtyEiI7HPKFdVWkUMw0nwfjOuEJxzHY7zPAoZ4Ks8yAla62JKjAWSrVoccC+BNp4ZFjinQlUAceAbAkGIFTmvjjOXa4K8+gsQcb35DEyoWQ3aFGmnBIzao/B11BWqE93U9GK9FANeEaRvCO/V+ijX6Bhdkj4ZlcFGavYEBi31Wy2KWaRKE58Xg+4vjLN9l64Eol7GJz2m6N9oJk85IO6YrtNoK2AVgDf8HP515Lh5mAmknUvKrT+8q3XOpqngCzPNDBwUbiZUNCQU7yyb/NdUA5WxE6/Kfx7QeLImh4X5KzlQ==", "deductInfos": [{"totalAmount": 80, "payofftype": 2}], "currentOriginalDailyPrice": 173, "priceType": 1, "currentDailyPrice": 133, "currentTotalPrice": 381, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 8, "vendorName": "椰林情租车", "filterAggregations": [{"groupCode": "BrandGroup_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Brand_雪佛兰_雪佛兰科沃兹", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 524288, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "Comment", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Ceritificate", "binaryDigit": 7, "checkType": 0}, {"groupCode": "Promotion", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PickReturn", "binaryDigit": 2, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 32768, "checkType": 0}], "rDistance": 0.8548, "actId": "3422", "pickUpFee": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 5, "mergeId": 1, "groupCode": "MarketGroup1347", "code": "7", "title": "押金双免", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1201", "code": "6", "title": "倒车雷达", "colorCode": "1", "type": 1, "groupId": 2, "labelCode": "3503"}, {"category": 1, "sortNum": 15, "mergeId": 1, "groupCode": "MarketGroup1336", "code": "8", "title": "免费取消", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3563"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}], "fees": [{"amount": 266, "detail": [{"code": "1001", "amount": 346, "amountDesc": "¥346", "name": "租车费"}, {"code": "11037", "amount": 50, "amountDesc": "¥50", "name": "优惠券"}, {"code": "3422", "amount": 30, "amountDesc": "¥30", "name": "今日特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥133", "originalDailyPrice": 173, "subAmount": 133, "name": "车辆租金", "amountStr": "¥266"}, {"code": "CAR_SERVICE_FEE", "amount": 25, "amountStr": "¥25", "detail": [{"code": "1003", "amount": 25, "amountDesc": "¥25", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 90, "amountStr": "¥90", "detail": [{"code": "1002", "amount": 90, "amountDesc": "¥90", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 381, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥461", "subAmount": 461, "name": "总价", "amountStr": "¥381"}], "vendorLogo": "//pic.c-ctrip.com/car_isd/vendorlogo/30777.jpg", "distance": 0, "couId": "", "modifySameStore": false, "pStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "14415", "cyVendorName": "椰林情租车", "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {"bAdjustDailyPrice": 173, "bAdjustTotalPrice": 346}, "freeDeposit": 0, "showVendor": false, "stock": 2, "reference": {"aType": 0, "hotType": 0, "pLevel": 1, "pCityId": 43, "pickWayInfo": 1, "pickUpOnDoor": true, "freeIllegalDeposit": false, "hot": 0, "isEasyLife": true, "payMode": 2, "priceVersion": "AVsB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCBWeMPYCB+UzKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAla55UKi3kcIDDD7iOPvxEEtyEiI7HPKFdVWkUMw0nwfjOuEJxzHY7zPAoZ4Ks8yAla62JKjAWSrVoccC+BNp4ZFjinQlUAceAbAkGIFTmvjjOXa4K8+gsQcb35DEyoWQ3aFGmnBIzao/B11BWqE93U9GK9FANeEaRvCO/V+ijX6Bhdkj4ZlcFGavYEBi31Wy2KWaRKE58Xg+4vjLN9l64Eol7GJz2m6N9oJk85IO6YrtNoK2AVgDf8HP515Lh5mAmknUvKrT+8q3XOpqngCzPNDBwUbiZUNCQU7yyb/NdUA5WxE6/Kfx7QeLImh4X5KzlQ==", "elct": 0, "packageId": "vbkpkg16ef269504705412c90976b71617ac454", "rStoreCode": "25782", "alipay": false, "vendorCode": "30777", "productCode": "777_18456_25782_25782", "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup1317", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}], "sendTypeForPickUpCar": 10, "rCityId": 43, "pStoreNav": "店员免费上门送取车", "vehicleCode": "18456", "bizVendorCode": "777", "vendorVehicleCode": "14415", "dropOffOnDoor": true, "sendTypeForPickOffCar": 10, "rRc": 0, "rLevel": 1, "noDepositFilter": {}, "pCType": 0, "pStoreCode": "25782", "priceType": 1, "pRc": 0, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 461, "recommendOrder": 0, "mergeId": 108, "vdegree": "0", "rectype": 1, "totalDailyPrice": 133, "mergeInfo": [{"storeId": "25782", "vehicleId": "14415"}], "isrec": false, "cvid": 14415, "rentalamount": 266}, "vcExtendRequest": {"vendorVehicleId": "18456"}, "comPriceCode": "eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiMTIzMTIwMjExMzMzMTliMTRlNzY0ZS0wNTg1LTQ1MDEtODBiNC1iZjRiNmJhYzdhNGQifQ==", "gsId": 0, "returnWayInfo": 1, "isSelect": false, "age": 30, "vehicleDegree": "0", "packageType": 1, "rStoreNav": "店员免费上门送取车", "adjustVersion": "", "rCType": 0, "vendorSupportZhima": true, "fType": 0}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": false, "isOrderVehicle": false, "platformCode": "10"}]}, "filteredProductGroups": {"vendorPriceList": []}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 353, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 353, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1646205537430, "afterFetch": 1646205537783}}