{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "505f8ed1-8f53-41a2-baec-43bfb400b3e2", "cost": 656, "extMap": {}}, "ResponseStatus": {"Timestamp": "/Date(1718707007138+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "1671861803585460554"}, {"Id": "RootMessageId", "Value": "921822-0a06deab-477418-81533"}]}, "packageDetails": [{"totalPrice": 60, "priceTitle": "一口价", "description": [{"contents": ["车损保障全额赔付"]}, {"contents": ["三者保障200万"]}, {"contents": ["免收停运费"]}, {"contents": ["免收折旧费"]}, {"contents": ["车上人员保障5万/座"]}, {"contents": ["免费道路救援"]}], "current": true, "totalPriceStr": "¥60"}, {"title": "普通预订+尊享保障", "totalPrice": 565, "priceTitle": "总价", "description": [{"contents": ["车损保障全额赔付"]}, {"contents": ["三者保障200万"]}, {"contents": ["1万及以内免收停运费"]}, {"contents": ["3万元及以内免收折旧费"]}, {"contents": ["车上人员保障1万/座"]}, {"contents": ["收费道路救援"]}], "current": false, "totalPriceStr": "¥565"}], "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 734, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 734, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1718707006090, "afterFetch": 1718707006824}}