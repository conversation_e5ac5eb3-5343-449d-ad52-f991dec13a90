{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "ba29b470-f16a-44dd-8723-e9783ee40d54", "extMap": {"isKarabi": "1", "rentalDays": "2"}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "isSoldOut": false, "isHotCity": true, "pickupStoreInfo": {"storeCode": "87185", "bizVendorCode": "14010", "telephone": "1 323 6739084", "storeName": "LOS ANGELES AIRPORT", "address": "PLEASE GO TO FOX COUNTER,5500 W CENTURY BLVD", "longitude": -118.4083, "latitude": 33.9471, "storeGuild": "抵达洛杉矶机场后，请从行李领取处前往中岛。站在标有 \"租车接机 \"的紫色标志下，登上fox租车公司的班车。", "storeWay": "门店位于洛杉矶国际机场外", "workTime": {"openTimeDesc": "{\"\":\"00:01 - 00:30,05:00 - 23:59\"}", "description": "00:00 - 08:59取还车，请提前联系门店，门店预估收取服务费每次THB1,000，费用及税费以实际支付为准。20:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次THB1,000，费用及税费以实际支付为准。"}, "storeServiceList": [], "countryId": 66, "countryName": "美国", "provinceId": 10125, "provinceName": "加利福尼亚州", "cityId": 347, "cityName": "洛杉矶", "isAirportStore": true, "showType": 3, "vendorStoreCode": "LAXO01", "continentId": 4, "continentName": "北美洲"}, "returnStoreInfo": {"storeCode": "87185", "bizVendorCode": "14010", "telephone": "1 323 6739084", "storeName": "LOS ANGELES AIRPORT", "address": "PLEASE GO TO FOX COUNTER,5500 W CENTURY BLVD", "longitude": -118.4083, "latitude": 33.9471, "storeGuild": "请提前联系门店确认取还车位置。", "storeWay": "门店位于洛杉矶国际机场外", "workTime": {"openTimeDesc": "{\"\":\"00:01 - 00:30,05:00 - 23:59\"}", "description": "00:00 - 08:59取还车，请提前联系门店，门店预估收取服务费每次THB1,000，费用及税费以实际支付为准。20:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次THB1,000，费用及税费以实际支付为准。"}, "storeServiceList": [], "countryId": 66, "countryName": "美国", "provinceId": 10125, "provinceName": "加利福尼亚州", "cityId": 347, "cityName": "洛杉矶", "isAirportStore": true, "showType": 3, "vendorStoreCode": "LAXO01", "continentId": 4, "continentName": "北美洲"}, "vendorInfo": {"bizVendorCode": "14010", "vendorName": "Europcar", "vendorImageUrl": "https://ak-d.tripcdn.com/images/0yc2x12000bzfnvvaB9A9.png", "vendorCode": "SD0006", "isBroker": false, "platformCode": "", "platformName": "Europcar", "haveCoupon": true, "vendorTag": {"title": "当地加盟", "sortNum": 0}}, "vehicleInfo": {"brandId": 120, "brandEName": "Toyota", "name": "丰田 Yaris 2门版", "zhName": "丰田 Yaris 2门版", "vehicleCode": "7188", "imageUrl": "https://dimg04.c-ctrip.com/images/0RV5912000c6s1i8rDB37.png", "groupCode": "1", "groupSubClassCode": "1001", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 4, "doorNo": 2, "luggageNo": 1, "imageList": ["https://dimg04.c-ctrip.com/images/0RV2c12000c6s1fkh5A86.jpg"], "userRealImageCount": 0, "isSpecialized": false, "hasConditioner": true, "conditionerDesc": "A/C", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14010", "vendorName": "Europcar", "vendorLogo": "https://ak-d.tripcdn.com/images/0yc2x12000bzfnvvaB9A9.png", "similarVehicleInfos": [{"vehicleCode": "7188", "vehicleName": "丰田 Yaris 2门版", "vehicleImageUrl": "https://dimg04.c-ctrip.com/images/0RV5912000c6s1i8rDB37.png"}, {"vehicleCode": "7186", "vehicleName": "丰田 <PERSON>ris", "vehicleImageUrl": "https://dimg04.c-ctrip.com/images/0RV2j12000c6s1eriC29B.png"}]}], "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2c12000c6s1fkh5A86.jpg", "groupSubName": "小型轿车"}, "flightDelayRule": {"title": "航班延误保留政策", "description": "航班延误保留政策", "rules": [{"title": "如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。"}, {"title": "建议您填写航班号，如遇延误请携带机票前往取车"}]}, "commentInfo": {"level": "不错", "commentCount": 11, "overallRating": "4.4", "maximumRating": 5.0, "commentLabel": "", "hasComment": 1, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=87185&calabiVehicleId=7188&vehicleName=丰田 Yaris 2门版&productCategoryId=34&isHideNavBar=YES"}, "packageInfos": [{"insPackageId": 4, "isDefault": true, "packageName": "加强套餐", "currencyCode": "CNY", "defaultBomCode": "87185_7188_ALI_FRFB_RAP_SLDW_Taxes_Taxes_ULM_0_0", "defaultPackageId": 89662, "guaranteeDegree": 3.0, "naked": false, "insuranceNames": ["加强三者险", "道路救援", "超级碰撞盗抢保障"], "lowestDailyPrice": 433, "gapPrice": 0, "stepPrice": 0, "description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失", "packageType": 0, "isBasic": 1}, {"insPackageId": 260, "isDefault": false, "packageName": "优享套餐", "currencyCode": "CNY", "defaultBomCode": "87185_7188_ALI_FRFB_RAP_SLDW_Taxes_Taxes_ULM_0_0", "defaultPackageId": 89662, "guaranteeDegree": 5.0, "naked": false, "insuranceNames": ["加强三者险", "道路救援", "超级碰撞盗抢保障", "安心补充险"], "lowestDailyPrice": 478, "gapPrice": 45, "stepPrice": 45, "isYouXiang": 1, "youXiangGapPrice": 45}, {"insPackageId": 772, "isDefault": false, "packageName": "尊享套餐", "currencyCode": "CNY", "defaultBomCode": "87185_7188_ALI_FRFB_RAP_SLDW_Taxes_Taxes_ULM_0_0", "defaultPackageId": 89662, "guaranteeDegree": 5.0, "naked": false, "insuranceNames": ["加强三者险", "道路救援", "超级碰撞盗抢保障", "安心补充险", "驾乘意外险"], "lowestDailyPrice": 500, "gapPrice": 67, "stepPrice": 67, "descTitle": "全方位保障驾驶车辆、车内人员财物及第三者", "isYouXiang": 0}], "productDetails": [{"insPackageId": 4, "insuranceItems": [{"code": "SLDW", "name": "超级碰撞盗抢保障", "description": "保障车辆碰撞、被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行承担起赔额以上的费用，起赔额通常较低或为零。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}}, {"code": "ALI", "name": "加强三者险", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}], "converageExplain": {"title": "承保范围", "content": ["若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "2", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}}, {"code": "RAP", "name": "道路救援", "description": "如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}], "converageExplain": {"title": "承保范围", "content": ["如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "4", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "insuranceNotice": "1、驾车人员投保年龄必须符合国际条列，即18周岁以上、70周岁以下；乘客投保年龄范围为0--90周岁，其中被保险人“意外身故、残疾保障“的保险金额71至80周岁为保单所载金额的一半、81至90周岁为四分之一。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010。\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n平安产险交通出行意外伤害保险（互联网版）条款\t注册号：C00001732312022080414693\n平安个人随车物品损失保险条款  注册号：H00001732112017050450351\n平安附加承保地域特约条款\t注册号为：C00001732322018031302341\t\n平安附加适用医疗机构范围约定保险条款\t注册号为：C00001731922019040903801\t\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1703/33.pdf"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 3, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}], "insuranceDesc": ["由于当地法规强制要求租车需有第三者责任险，故若没有选择含第三者责任险的套餐，必定会在线下被以更高价格强制加购，建议选择含第三者责任险的套餐。"], "combinations": [{"bomCode": "87185_7188_ALI_FRFB_RAP_SLDW_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 433, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 866, "payMode": 2, "packageId": 89662}], "productInfoList": [{"productCode": "8", "bomGroupCode": "87185_7188_ALI_FRFB_RAP_SLDW_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 866, "currentDailyPrice": 433, "currentTotalPrice": 866, "localCarPrice": 120.71, "localDailyPrice": 60.36, "localTotalPrice": 120.71, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 866, "localOnewayfee": 0.0, "localPoaPrice": 0.0, "localPrepaidPrice": 120.71, "isContainOnewayFee": false, "payMode": 2, "productId": "889662", "packageId": 89662, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 7.17155, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 48, "cancelDescription": "支付完成至取车前48小时可免费取消;取车前48小时至取车时间取消将收取US$ 50.00作为违约金（约¥359.00）;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：19-75周岁", "minDriverAge": 19, "maxDriverAge": 75, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "USD", "currentPrice": 179.0, "localPrice": 25.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "USD", "maxDeposit": 200.0, "minDeposit": 200.0}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车前48小时可免费取消;取车前48小时至取车时间取消将收取US$ 50.00作为违约金（约¥359.00）;取车时间后取消将收取全部租金作为违约金", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "含加强三者险", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含加强三者险", "sortNum": 66, "colorCode": "2", "labelCode": "3555"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}, {"title": "券减¥200", "category": 3, "type": 3, "code": "30", "description": "券减¥200,可享立减￥200/天（2天共减￥400）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}], "insuranceDetails": [{"code": "ALI", "name": "加强三者险", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "RAP", "name": "道路救援", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}], "chargeList": [{"code": "", "payMode": 2, "netAmount": 168.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 698.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}], "promotionInfo": {"deductionAmount": 400}, "vendorPromotionList": [], "pkgSellingRuleId": 89662}, {"currentCarPrice": 996.0, "currentDailyPrice": 498, "currentTotalPrice": 996, "localCarPrice": 138.82, "localDailyPrice": 69.41, "localTotalPrice": 138.82, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 866, "currentPrepaidPrice": 130, "localOnewayfee": 0.0, "localPoaPrice": 120.71, "localPrepaidPrice": 18.11, "isContainOnewayFee": false, "payMode": 3, "productId": "81784", "packageId": 1784, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 7.17155, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：19-75周岁", "minDriverAge": 19, "maxDriverAge": 75, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "USD", "currentPrice": 179.0, "localPrice": 25.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "USD", "maxDeposit": 200.0, "minDeposit": 200.0}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含加强三者险", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含加强三者险", "sortNum": 66, "colorCode": "2", "labelCode": "3555"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}], "insuranceDetails": [{"code": "ALI", "name": "加强三者险", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "RAP", "name": "道路救援", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}], "chargeList": [{"code": "", "desc": "", "payMode": 3, "netAmount": 3.34, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 1784}, {"currentCarPrice": 1018.0, "currentDailyPrice": 509, "currentTotalPrice": 1018, "localCarPrice": 142.01, "localDailyPrice": 71.01, "localTotalPrice": 142.01, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 1018, "currentPrepaidPrice": 0, "localOnewayfee": 0.0, "localPoaPrice": 142.01, "localPrepaidPrice": 0.0, "isContainOnewayFee": true, "payMode": 1, "productId": "889543", "packageId": 89543, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 7.17155, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：19-75周岁", "minDriverAge": 19, "maxDriverAge": 75, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "USD", "currentPrice": 179.0, "localPrice": 25.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "USD", "maxDeposit": 200.0, "minDeposit": 200.0}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含加强三者险", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含加强三者险", "sortNum": 66, "colorCode": "2", "labelCode": "3555"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}], "insuranceDetails": [{"code": "ALI", "name": "加强三者险", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "RAP", "name": "道路救援", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}], "chargeList": [{"code": "", "desc": "", "payMode": 1, "netAmount": 3.34, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 89543}], "needFlightNo": false, "equipments": [{"equipmentType": 1, "equipmentCode": "53", "equipmentName": "婴儿座椅", "equipmentDesc": "通常适用于年龄0个月~12个月的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}, {"equipmentType": 2, "equipmentCode": "72", "equipmentName": "额外驾驶员", "equipmentDesc": "1名额外驾驶员\n每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "ageFromUnit": "/个", "ageToUnit": "/个", "localTotalPrice": 0.0, "localDailyPrice": 0.0, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 0.0, "currentDailyPrice": 0.0, "payMode": 1}, {"equipmentType": 2, "equipmentCode": "55", "equipmentName": "儿童座椅", "equipmentDesc": "通常适用于年龄4岁~7岁的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}, {"equipmentType": 2, "equipmentCode": "54", "equipmentName": "儿童座椅", "equipmentDesc": "通常适用于年龄1岁~3岁的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}, {"equipmentType": 3, "equipmentCode": "56", "equipmentName": "儿童增高座垫", "equipmentDesc": "通常适用于身高135厘米以下的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "ALI", "name": "加强三者险", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "RAP", "name": "道路救援", "desc": "如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。", "sortNum": 3}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "FRFB", "name": "满油/电取还", "desc": "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "取车要求", "content": ["驾驶员年龄19-75周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "19-75周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}], "summaryTitle": "驾驶员需持有本人名下5项材料取车"}, {"title": "护照原件", "subTitle": "", "type": 0, "subObject": [{"title": "中国大陆护照", "subTitle": "中国大陆护照原件", "code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国香港护照", "subTitle": "中国香港护照原件", "code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国澳门护照", "subTitle": "中国澳门护照原件", "code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国台湾护照", "subTitle": "中国台湾护照原件", "code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "其他地区护照", "subTitle": "其他地区护照原件", "code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}]}, {"title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1, "subObject": [{"type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "CN", "subObject": [{"title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22, "code": "CDL,IDL", "sortNum": 2}, {"title": "中国大陆驾照原件 + 英文公证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 22, "code": "CDL,OET", "sortNum": 4}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "HK", "subObject": [{"title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "HKDL,IDP", "sortNum": 37}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "MCDL,IDP", "sortNum": 29}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "TWDL,IDP", "sortNum": 31}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}]}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "暂不支持非美国加拿大护照客人使用美国、加拿大驾照取车。"}]}], "type": 10}]}, {"title": "国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["押金约租车费用+US$ 200（约¥1,436），到店刷取押金预授权，还车后2-5天内退还"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "额外驾驶员也需要出示另一张符合供应商要求的信用卡"}]}], "type": 6, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png"], "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "0"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200（约¥1,436），到店刷取押金预授权，还车后2-5天内退还"], "type": 8, "table": [{"title": "押金", "description": "到店刷取押金预授权，还车后2-5天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200（约¥1,436）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}, {"title": "其他材料", "content": ["如果您持美国驾照租车，您需要提供当前家庭住址（非邮政信箱）、当前家庭和本地联系电话号码以及第二个电话号码（手机或公司电话）。"], "summaryContent": ["如果您持美国驾照租车，您需要提供当前家庭住址（非邮政信箱）、当前家庭和本地联系电话号码以及第二个电话号码（手机或公司电话）。"], "type": 14}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在1小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前48小时可免费取消;取车前48小时至取车时间取消将收取US$ 50.00作为违约金（约¥359.00）;取车时间后取消将收取全部租金作为违约金"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2024年2月26日17:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2024年2月26日17:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2024年2月26日17:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2024年2月26日17:00", "subTitle": "支付完成至取车前48小时", "description": "可免费取消", "showFree": true}, {"title": "2024年2月26日17:00至2024年2月28日17:00", "subTitle": "取车前48小时至取车时间", "description": "取消将收取US$ 50.00作为违约金（约¥359.00）", "showFree": false}, {"title": "2024年2月28日17:00后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金", "showFree": false}]}]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200（约¥1,436），到店刷取押金预授权，还车后2-5天内退还"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "到店刷取押金预授权，还车后2-5天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200（约¥1,436）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：19-75周岁", "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：19-75周岁", "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}, {"title": "门店提示", "content": ["区域限行：在毛伊岛租用的车辆只能在柏油路上行驶。"], "type": 47, "code": "2", "sortNum": 7}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "保险服务提示", "content": ["由于当地法规强制要求租车需有第三者责任险，故若没有选择含第三者责任险的套餐，必定会在线下被以更高价格强制加购，建议选择含第三者责任险的套餐。"], "type": 45, "code": "2", "sortNum": 1}, {"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "旅行限制", "content": [], "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["来自亚利桑那州、加利福尼亚州、科罗拉多州、内华达州、犹他州和华盛顿州的租车服务只能在亚利桑那州、加利福尼亚州、科罗拉多州、爱达荷州、堪萨斯州、内布拉斯加州、内华达州、新墨西哥州、俄克拉荷马州、俄勒冈州、南达科他州、得克萨斯州、犹他州、华盛顿州和怀俄明州范围内行驶。 源于佛罗里达州的车辆租赁只能在佛罗里达州境内行驶。 源于德克萨斯州的车辆租赁只能在阿肯色州、路易斯安那州、新墨西哥州、俄克拉荷马州和德克萨斯州内行驶。 来自伊利诺伊州的车辆租赁只能在伊利诺伊州、威斯康星州、爱荷华州、印第安纳州、密苏里州和肯塔基州内行驶。 源于新泽西州的车辆租赁可以进入加拿大。进入加拿大需要有护照。加拿大海关和消费税条例禁止加拿大居民驾驶美国租赁的车辆进入加拿大并返回美国。 在茂宜岛出发的车辆租赁仅限于在公共道路上使用。 在犹他州弗纳尔市租来的车辆只能在犹他州境内行驶。在事先同意的情况下，可以在犹他州以外的地方行驶。当在犹他州以外的地方旅行时，每天的里程数限制在300英里，每英里收取0.25的超额费用。 FOX车辆可以驶入墨西哥，但前提是你已经购买了墨西哥保险。更多详情请见上面的墨西哥保险部分。 从华盛顿州租来的车辆只允许开到加拿大不列颠哥伦比亚省。所有其他省份都被禁止。 前往墨西哥 只有在购买墨西哥保险的情况下，才允许FOX车辆进入墨西哥最靠近加州和亚利桑那州边界的城市/州。请参考墨西哥保险部分，了解允许的覆盖区域。 注意：对于从毛伊岛、纽瓦克和弗纳尔出发的租赁车辆，不允许驶入墨西哥。"], "type": 27, "code": "2", "sortNum": 1}, {"title": "高速路费及ETC收费政策", "content": ["您需要支付租赁期间发生的所有通行费。 供应商提供PlatePass全包服务，这是一项可选择的电子通行费支付服务，租赁期间您在PlatePass收费道路产生的所有通行费和任何相关费用都由每日PlatePass的全包费用支付，费用为每日6.99美元至11.49美元不等。在除PlatePass收费公路以外产生相关费用（包括通行费、费用或相关的罚款、罚金），除了需要支付费用外，您还将被收取每次15.00美元的管理费（每次租赁最多90.00美元）。为避免此项管理费，您可以选择自行支付通行费。 详情请咨询门店。"], "type": 54, "code": "2", "sortNum": 2}], "sortNum": 5}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}], "sortNum": 7}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "安心补充险", "id": 3, "custumerTotalPrice": 90, "custumerDailyPrice": 45, "code": "MP18021529PK00024376", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localTotalPrice": 90, "localDailyPrice": 45.0, "localCurrencyCode": "CNY", "desc": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢自付额", "longDesc": "由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：", "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "FULL_COVERAGE", "days": 2, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "version": "OPI1175082439798685696", "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车", "description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}, {"code": "4", "title": "其他保障"}], "briefInsuranceItems": [{"code": "SLDW", "name": "超级碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}]}, {"code": "ALI", "name": "加强三者险", "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}]}, {"code": "RAP", "name": "道路救援", "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}]}], "insuranceCompareItems": [{"description": "0起赔额", "type": "LDW", "descriptionColorCode": "GREEN", "containsDescription": "（无需垫付）"}, {"contains": false, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": false, "type": "PI"}, {"contains": true, "type": "RAP"}, {"contains": false, "type": "CANCEL"}, {"contains": false, "type": "ACCIDENT"}]}, {"insPackageId": 260, "insuranceItems": [{"code": "SLDW", "name": "超级碰撞盗抢保障", "description": "保障车辆碰撞、被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行承担起赔额以上的费用，起赔额通常较低或为零。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}}, {"code": "ALI", "name": "加强三者险", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}], "converageExplain": {"title": "承保范围", "content": ["若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "2", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}}, {"code": "RAP", "name": "道路救援", "description": "如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}], "converageExplain": {"title": "承保范围", "content": ["如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "4", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥45/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥45/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 3, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "insuranceNotice": "1、驾车人员投保年龄必须符合国际条列，即18周岁以上、70周岁以下；乘客投保年龄范围为0--90周岁，其中被保险人“意外身故、残疾保障“的保险金额71至80周岁为保单所载金额的一半、81至90周岁为四分之一。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010。\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n平安产险交通出行意外伤害保险（互联网版）条款\t注册号：C00001732312022080414693\n平安个人随车物品损失保险条款  注册号：H00001732112017050450351\n平安附加承保地域特约条款\t注册号为：C00001732322018031302341\t\n平安附加适用医疗机构范围约定保险条款\t注册号为：C00001731922019040903801\t\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1703/33.pdf"}], "ctripInsuranceIds": [3], "insuranceDesc": ["由于当地法规强制要求租车需有第三者责任险，故若没有选择含第三者责任险的套餐，必定会在线下被以更高价格强制加购，建议选择含第三者责任险的套餐。"], "combinations": [{"bomCode": "87185_7188_ALI_FRFB_RAP_SLDW_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 478, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 956, "payMode": 2, "packageId": 89662}], "productInfoList": [{"productCode": "8", "bomGroupCode": "87185_7188_ALI_FRFB_RAP_SLDW_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 866, "currentDailyPrice": 478, "currentTotalPrice": 956, "localCarPrice": 120.71, "localDailyPrice": 60.36, "localTotalPrice": 120.71, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 956, "localOnewayfee": 0.0, "localPoaPrice": 0.0, "localPrepaidPrice": 120.71, "payMode": 2, "productId": "889662", "packageId": 89662, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 7.17155, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 48, "cancelDescription": "支付完成至取车前48小时可免费取消;取车前48小时至取车时间取消将收取US$ 50.00作为违约金（约¥359.00）;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：19-75周岁", "minDriverAge": 19, "maxDriverAge": 75, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "USD", "currentPrice": 179.0, "localPrice": 25.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "USD", "maxDeposit": 200.0, "minDeposit": 200.0}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车前48小时可免费取消;取车前48小时至取车时间取消将收取US$ 50.00作为违约金（约¥359.00）;取车时间后取消将收取全部租金作为违约金", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "含加强三者险", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含加强三者险", "sortNum": 66, "colorCode": "2", "labelCode": "3555"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}, {"title": "券减¥200", "category": 3, "type": 3, "code": "30", "description": "券减¥200,可享立减￥200/天（2天共减￥400）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}], "insuranceDetails": [{"code": "ALI", "name": "加强三者险", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "RAP", "name": "道路救援", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}], "chargeList": [{"code": "", "payMode": 2, "netAmount": 168.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 698.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}], "promotionInfo": {"deductionAmount": 400}, "vendorPromotionList": [], "pkgSellingRuleId": 89662}, {"currentCarPrice": 996.0, "currentDailyPrice": 543, "currentTotalPrice": 1086, "localCarPrice": 138.82, "localDailyPrice": 69.41, "localTotalPrice": 138.82, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 866, "currentPrepaidPrice": 220, "localOnewayfee": 0.0, "localPoaPrice": 120.71, "localPrepaidPrice": 18.11, "payMode": 3, "productId": "81784", "packageId": 1784, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 7.17155, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：19-75周岁", "minDriverAge": 19, "maxDriverAge": 75, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "USD", "currentPrice": 179.0, "localPrice": 25.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "USD", "maxDeposit": 200.0, "minDeposit": 200.0}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含加强三者险", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含加强三者险", "sortNum": 66, "colorCode": "2", "labelCode": "3555"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}], "insuranceDetails": [{"code": "ALI", "name": "加强三者险", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "RAP", "name": "道路救援", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}], "chargeList": [{"code": "", "desc": "", "payMode": 3, "netAmount": 3.34, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 1784}, {"currentCarPrice": 1018.0, "currentDailyPrice": 554, "currentTotalPrice": 1108, "localCarPrice": 142.01, "localDailyPrice": 71.01, "localTotalPrice": 142.01, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 1018, "currentPrepaidPrice": 90, "localOnewayfee": 0.0, "localPoaPrice": 142.01, "localPrepaidPrice": 0.0, "payMode": 1, "productId": "889543", "packageId": 89543, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 7.17155, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：19-75周岁", "minDriverAge": 19, "maxDriverAge": 75, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "USD", "currentPrice": 179.0, "localPrice": 25.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "USD", "maxDeposit": 200.0, "minDeposit": 200.0}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含加强三者险", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含加强三者险", "sortNum": 66, "colorCode": "2", "labelCode": "3555"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}], "insuranceDetails": [{"code": "ALI", "name": "加强三者险", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "RAP", "name": "道路救援", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}], "chargeList": [{"code": "", "desc": "", "payMode": 1, "netAmount": 3.34, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 89543}], "needFlightNo": false, "equipments": [{"equipmentType": 1, "equipmentCode": "53", "equipmentName": "婴儿座椅", "equipmentDesc": "通常适用于年龄0个月~12个月的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}, {"equipmentType": 2, "equipmentCode": "72", "equipmentName": "额外驾驶员", "equipmentDesc": "1名额外驾驶员\n每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "ageFromUnit": "/个", "ageToUnit": "/个", "localTotalPrice": 0.0, "localDailyPrice": 0.0, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 0.0, "currentDailyPrice": 0.0, "payMode": 1}, {"equipmentType": 2, "equipmentCode": "55", "equipmentName": "儿童座椅", "equipmentDesc": "通常适用于年龄4岁~7岁的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}, {"equipmentType": 2, "equipmentCode": "54", "equipmentName": "儿童座椅", "equipmentDesc": "通常适用于年龄1岁~3岁的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}, {"equipmentType": 3, "equipmentCode": "56", "equipmentName": "儿童增高座垫", "equipmentDesc": "通常适用于身高135厘米以下的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 45.0, "localTotalPrice": 90, "localCurrencyCode": "CNY", "currentDailyPrice": 45, "currentTotalPrice": 90, "currentCurrencyCode": "CNY", "uniqueCode": "3"}], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "ALI", "name": "加强三者险", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "RAP", "name": "道路救援", "desc": "如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。", "sortNum": 3}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "FRFB", "name": "满油/电取还", "desc": "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "取车要求", "content": ["驾驶员年龄19-75周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "19-75周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}], "summaryTitle": "驾驶员需持有本人名下5项材料取车"}, {"title": "护照原件", "subTitle": "", "type": 0, "subObject": [{"title": "中国大陆护照", "subTitle": "中国大陆护照原件", "code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国香港护照", "subTitle": "中国香港护照原件", "code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国澳门护照", "subTitle": "中国澳门护照原件", "code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国台湾护照", "subTitle": "中国台湾护照原件", "code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "其他地区护照", "subTitle": "其他地区护照原件", "code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}]}, {"title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1, "subObject": [{"type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "CN", "subObject": [{"title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22, "code": "CDL,IDL", "sortNum": 2}, {"title": "中国大陆驾照原件 + 英文公证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 22, "code": "CDL,OET", "sortNum": 4}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "HK", "subObject": [{"title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "HKDL,IDP", "sortNum": 37}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "MCDL,IDP", "sortNum": 29}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "TWDL,IDP", "sortNum": 31}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}]}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "暂不支持非美国加拿大护照客人使用美国、加拿大驾照取车。"}]}], "type": 10}]}, {"title": "国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["押金约租车费用+US$ 200（约¥1,436），到店刷取押金预授权，还车后2-5天内退还"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "额外驾驶员也需要出示另一张符合供应商要求的信用卡"}]}], "type": 6, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png"], "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "0"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200（约¥1,436），到店刷取押金预授权，还车后2-5天内退还"], "type": 8, "table": [{"title": "押金", "description": "到店刷取押金预授权，还车后2-5天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200（约¥1,436）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}, {"title": "其他材料", "content": ["如果您持美国驾照租车，您需要提供当前家庭住址（非邮政信箱）、当前家庭和本地联系电话号码以及第二个电话号码（手机或公司电话）。"], "summaryContent": ["如果您持美国驾照租车，您需要提供当前家庭住址（非邮政信箱）、当前家庭和本地联系电话号码以及第二个电话号码（手机或公司电话）。"], "type": 14}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在1小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前48小时可免费取消;取车前48小时至取车时间取消将收取US$ 50.00作为违约金（约¥359.00）;取车时间后取消将收取全部租金作为违约金"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2024年2月26日17:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2024年2月26日17:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2024年2月26日17:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2024年2月26日17:00", "subTitle": "支付完成至取车前48小时", "description": "可免费取消", "showFree": true}, {"title": "2024年2月26日17:00至2024年2月28日17:00", "subTitle": "取车前48小时至取车时间", "description": "取消将收取US$ 50.00作为违约金（约¥359.00）", "showFree": false}, {"title": "2024年2月28日17:00后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金", "showFree": false}]}]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200（约¥1,436），到店刷取押金预授权，还车后2-5天内退还"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "到店刷取押金预授权，还车后2-5天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200（约¥1,436）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：19-75周岁", "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：19-75周岁", "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}, {"title": "门店提示", "content": ["区域限行：在毛伊岛租用的车辆只能在柏油路上行驶。"], "type": 47, "code": "2", "sortNum": 7}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "保险服务提示", "content": ["由于当地法规强制要求租车需有第三者责任险，故若没有选择含第三者责任险的套餐，必定会在线下被以更高价格强制加购，建议选择含第三者责任险的套餐。"], "type": 45, "code": "2", "sortNum": 1}, {"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "旅行限制", "content": [], "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["来自亚利桑那州、加利福尼亚州、科罗拉多州、内华达州、犹他州和华盛顿州的租车服务只能在亚利桑那州、加利福尼亚州、科罗拉多州、爱达荷州、堪萨斯州、内布拉斯加州、内华达州、新墨西哥州、俄克拉荷马州、俄勒冈州、南达科他州、得克萨斯州、犹他州、华盛顿州和怀俄明州范围内行驶。 源于佛罗里达州的车辆租赁只能在佛罗里达州境内行驶。 源于德克萨斯州的车辆租赁只能在阿肯色州、路易斯安那州、新墨西哥州、俄克拉荷马州和德克萨斯州内行驶。 来自伊利诺伊州的车辆租赁只能在伊利诺伊州、威斯康星州、爱荷华州、印第安纳州、密苏里州和肯塔基州内行驶。 源于新泽西州的车辆租赁可以进入加拿大。进入加拿大需要有护照。加拿大海关和消费税条例禁止加拿大居民驾驶美国租赁的车辆进入加拿大并返回美国。 在茂宜岛出发的车辆租赁仅限于在公共道路上使用。 在犹他州弗纳尔市租来的车辆只能在犹他州境内行驶。在事先同意的情况下，可以在犹他州以外的地方行驶。当在犹他州以外的地方旅行时，每天的里程数限制在300英里，每英里收取0.25的超额费用。 FOX车辆可以驶入墨西哥，但前提是你已经购买了墨西哥保险。更多详情请见上面的墨西哥保险部分。 从华盛顿州租来的车辆只允许开到加拿大不列颠哥伦比亚省。所有其他省份都被禁止。 前往墨西哥 只有在购买墨西哥保险的情况下，才允许FOX车辆进入墨西哥最靠近加州和亚利桑那州边界的城市/州。请参考墨西哥保险部分，了解允许的覆盖区域。 注意：对于从毛伊岛、纽瓦克和弗纳尔出发的租赁车辆，不允许驶入墨西哥。"], "type": 27, "code": "2", "sortNum": 1}, {"title": "高速路费及ETC收费政策", "content": ["您需要支付租赁期间发生的所有通行费。 供应商提供PlatePass全包服务，这是一项可选择的电子通行费支付服务，租赁期间您在PlatePass收费道路产生的所有通行费和任何相关费用都由每日PlatePass的全包费用支付，费用为每日6.99美元至11.49美元不等。在除PlatePass收费公路以外产生相关费用（包括通行费、费用或相关的罚款、罚金），除了需要支付费用外，您还将被收取每次15.00美元的管理费（每次租赁最多90.00美元）。为避免此项管理费，您可以选择自行支付通行费。 详情请咨询门店。"], "type": 54, "code": "2", "sortNum": 2}], "sortNum": 5}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}], "sortNum": 7}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "安心补充险", "id": 3, "custumerTotalPrice": 90, "custumerDailyPrice": 45, "code": "MP18021529PK00024376", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localTotalPrice": 90, "localDailyPrice": 45.0, "localCurrencyCode": "CNY", "desc": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢自付额", "longDesc": "由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：", "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "FULL_COVERAGE", "days": 2, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "version": "OPI1175082439798685696", "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "提供理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核（1-3个工作日内反馈）", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款（审核通过后3个工作日内）", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}, {"code": "4", "title": "其他保障"}], "briefInsuranceItems": [{"code": "SLDW", "name": "超级碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}]}, {"code": "ALI", "name": "加强三者险", "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}]}, {"code": "RAP", "name": "道路救援", "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}]}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥45/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥45/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 3, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}], "insuranceCompareItems": [{"description": "0起赔额", "type": "LDW", "descriptionColorCode": "GREEN", "containsDescription": "（无需垫付）"}, {"contains": true, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": false, "type": "PI"}, {"contains": true, "type": "RAP"}, {"contains": true, "type": "CANCEL"}, {"contains": false, "type": "ACCIDENT"}]}, {"insPackageId": 772, "insuranceItems": [{"code": "SLDW", "name": "超级碰撞盗抢保障", "description": "保障车辆碰撞、被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行承担起赔额以上的费用，起赔额通常较低或为零。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}}, {"code": "ALI", "name": "加强三者险", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}], "converageExplain": {"title": "承保范围", "content": ["若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "2", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}}, {"code": "RAP", "name": "道路救援", "description": "如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}], "converageExplain": {"title": "承保范围", "content": ["如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "4", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥45/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥45/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 3, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥22/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥22/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "insuranceNotice": "1、驾车人员投保年龄必须符合国际条列，即18周岁以上、70周岁以下；乘客投保年龄范围为0--90周岁，其中被保险人“意外身故、残疾保障“的保险金额71至80周岁为保单所载金额的一半、81至90周岁为四分之一。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010。\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n平安产险交通出行意外伤害保险（互联网版）条款\t注册号：C00001732312022080414693\n平安个人随车物品损失保险条款  注册号：H00001732112017050450351\n平安附加承保地域特约条款\t注册号为：C00001732322018031302341\t\n平安附加适用医疗机构范围约定保险条款\t注册号为：C00001731922019040903801\t\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1703/33.pdf"}], "ctripInsuranceIds": [3, 11], "insuranceDesc": ["由于当地法规强制要求租车需有第三者责任险，故若没有选择含第三者责任险的套餐，必定会在线下被以更高价格强制加购，建议选择含第三者责任险的套餐。"], "combinations": [{"bomCode": "87185_7188_ALI_FRFB_RAP_SLDW_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 500, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 1000, "payMode": 2, "packageId": 89662}], "productInfoList": [{"productCode": "8", "bomGroupCode": "87185_7188_ALI_FRFB_RAP_SLDW_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 866, "currentDailyPrice": 500, "currentTotalPrice": 1000, "localCarPrice": 120.71, "localDailyPrice": 60.36, "localTotalPrice": 120.71, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 1000, "localOnewayfee": 0.0, "localPoaPrice": 0.0, "localPrepaidPrice": 120.71, "payMode": 2, "productId": "889662", "packageId": 89662, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 7.17155, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 48, "cancelDescription": "支付完成至取车前48小时可免费取消;取车前48小时至取车时间取消将收取US$ 50.00作为违约金（约¥359.00）;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：19-75周岁", "minDriverAge": 19, "maxDriverAge": 75, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "USD", "currentPrice": 179.0, "localPrice": 25.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "USD", "maxDeposit": 200.0, "minDeposit": 200.0}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车前48小时可免费取消;取车前48小时至取车时间取消将收取US$ 50.00作为违约金（约¥359.00）;取车时间后取消将收取全部租金作为违约金", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "含加强三者险", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含加强三者险", "sortNum": 66, "colorCode": "2", "labelCode": "3555"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}, {"title": "券减¥200", "category": 3, "type": 3, "code": "30", "description": "券减¥200,可享立减￥200/天（2天共减￥400）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}], "insuranceDetails": [{"code": "ALI", "name": "加强三者险", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "RAP", "name": "道路救援", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}], "chargeList": [{"code": "", "payMode": 2, "netAmount": 168.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 698.0, "dueAmount": 0.0, "currency": "CNY", "isIncludedInRate": true}], "promotionInfo": {"deductionAmount": 400}, "vendorPromotionList": [], "pkgSellingRuleId": 89662}, {"currentCarPrice": 996.0, "currentDailyPrice": 565, "currentTotalPrice": 1130, "localCarPrice": 138.82, "localDailyPrice": 69.41, "localTotalPrice": 138.82, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 866, "currentPrepaidPrice": 264, "localOnewayfee": 0.0, "localPoaPrice": 120.71, "localPrepaidPrice": 18.11, "payMode": 3, "productId": "81784", "packageId": 1784, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 7.17155, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：19-75周岁", "minDriverAge": 19, "maxDriverAge": 75, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "USD", "currentPrice": 179.0, "localPrice": 25.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "USD", "maxDeposit": 200.0, "minDeposit": 200.0}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含加强三者险", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含加强三者险", "sortNum": 66, "colorCode": "2", "labelCode": "3555"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}], "insuranceDetails": [{"code": "ALI", "name": "加强三者险", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "RAP", "name": "道路救援", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}], "chargeList": [{"code": "", "desc": "", "payMode": 3, "netAmount": 3.34, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 1784}, {"currentCarPrice": 1018.0, "currentDailyPrice": 576, "currentTotalPrice": 1152, "localCarPrice": 142.01, "localDailyPrice": 71.01, "localTotalPrice": 142.01, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 1018, "currentPrepaidPrice": 134, "localOnewayfee": 0.0, "localPoaPrice": 142.01, "localPrepaidPrice": 0.0, "payMode": 1, "productId": "889543", "packageId": 89543, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 7.17155, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：19-75周岁", "minDriverAge": 19, "maxDriverAge": 75, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "USD", "currentPrice": 179.0, "localPrice": 25.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "USD", "maxDeposit": 200.0, "minDeposit": 200.0}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车时间可免费取消;取车时间后取消将收取全部租金作为违约金", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含加强三者险", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含加强三者险", "sortNum": 66, "colorCode": "2", "labelCode": "3555"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}], "insuranceDetails": [{"code": "ALI", "name": "加强三者险", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "RAP", "name": "道路救援", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0}], "chargeList": [{"code": "", "desc": "", "payMode": 1, "netAmount": 3.34, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0.0, "dueAmount": 0.0, "currency": "USD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 89543}], "needFlightNo": false, "equipments": [{"equipmentType": 1, "equipmentCode": "53", "equipmentName": "婴儿座椅", "equipmentDesc": "通常适用于年龄0个月~12个月的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}, {"equipmentType": 2, "equipmentCode": "72", "equipmentName": "额外驾驶员", "equipmentDesc": "1名额外驾驶员\n每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "ageFromUnit": "/个", "ageToUnit": "/个", "localTotalPrice": 0.0, "localDailyPrice": 0.0, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 0.0, "currentDailyPrice": 0.0, "payMode": 1}, {"equipmentType": 2, "equipmentCode": "55", "equipmentName": "儿童座椅", "equipmentDesc": "通常适用于年龄4岁~7岁的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}, {"equipmentType": 2, "equipmentCode": "54", "equipmentName": "儿童座椅", "equipmentDesc": "通常适用于年龄1岁~3岁的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}, {"equipmentType": 3, "equipmentCode": "56", "equipmentName": "儿童增高座垫", "equipmentDesc": "通常适用于身高135厘米以下的儿童\n", "ageFromUnit": "/个/天", "ageToUnit": "/个/天", "localTotalPrice": 27.28, "localDailyPrice": 13.64, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentTotalPrice": 196.0, "currentDailyPrice": 98.0, "payMode": 1}], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 45.0, "localTotalPrice": 90, "localCurrencyCode": "CNY", "currentDailyPrice": 45, "currentTotalPrice": 90, "currentCurrencyCode": "CNY", "uniqueCode": "3"}, {"name": "驾乘意外险", "localDailyPrice": 22.0, "localTotalPrice": 44, "localCurrencyCode": "CNY", "currentDailyPrice": 22, "currentTotalPrice": 44, "currentCurrencyCode": "CNY", "uniqueCode": "11"}], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "ALI", "name": "加强三者险", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "RAP", "name": "道路救援", "desc": "如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。", "sortNum": 3}, {"code": "SLDW", "name": "超级碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "FRFB", "name": "满油/电取还", "desc": "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "取车要求", "content": ["驾驶员年龄19-75周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "19-75周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}], "summaryTitle": "驾驶员需持有本人名下5项材料取车"}, {"title": "护照原件", "subTitle": "", "type": 0, "subObject": [{"title": "中国大陆护照", "subTitle": "中国大陆护照原件", "code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国香港护照", "subTitle": "中国香港护照原件", "code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国澳门护照", "subTitle": "中国澳门护照原件", "code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国台湾护照", "subTitle": "中国台湾护照原件", "code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "其他地区护照", "subTitle": "其他地区护照原件", "code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}]}, {"title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1, "subObject": [{"type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "CN", "subObject": [{"title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22, "code": "CDL,IDL", "sortNum": 2}, {"title": "中国大陆驾照原件 + 英文公证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 22, "code": "CDL,OET", "sortNum": 4}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "HK", "subObject": [{"title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "HKDL,IDP", "sortNum": 37}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "MCDL,IDP", "sortNum": 29}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "TWDL,IDP", "sortNum": 31}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}]}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "暂不支持非美国加拿大护照客人使用美国、加拿大驾照取车。"}]}], "type": 10}]}, {"title": "国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["押金约租车费用+US$ 200（约¥1,436），到店刷取押金预授权，还车后2-5天内退还"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "额外驾驶员也需要出示另一张符合供应商要求的信用卡"}]}], "type": 6, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png"], "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "0"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200（约¥1,436），到店刷取押金预授权，还车后2-5天内退还"], "type": 8, "table": [{"title": "押金", "description": "到店刷取押金预授权，还车后2-5天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200（约¥1,436）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}, {"title": "其他材料", "content": ["如果您持美国驾照租车，您需要提供当前家庭住址（非邮政信箱）、当前家庭和本地联系电话号码以及第二个电话号码（手机或公司电话）。"], "summaryContent": ["如果您持美国驾照租车，您需要提供当前家庭住址（非邮政信箱）、当前家庭和本地联系电话号码以及第二个电话号码（手机或公司电话）。"], "type": 14}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在1小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前48小时可免费取消;取车前48小时至取车时间取消将收取US$ 50.00作为违约金（约¥359.00）;取车时间后取消将收取全部租金作为违约金"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2024年2月26日17:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2024年2月26日17:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2024年2月26日17:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2024年2月26日17:00", "subTitle": "支付完成至取车前48小时", "description": "可免费取消", "showFree": true}, {"title": "2024年2月26日17:00至2024年2月28日17:00", "subTitle": "取车前48小时至取车时间", "description": "取消将收取US$ 50.00作为违约金（约¥359.00）", "showFree": false}, {"title": "2024年2月28日17:00后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金", "showFree": false}]}]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200（约¥1,436），到店刷取押金预授权，还车后2-5天内退还"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "到店刷取押金预授权，还车后2-5天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200（约¥1,436）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：19-75周岁", "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：19-75周岁", "租车公司对19-25周岁将收取“青年驾驶费”；参考价格：USD25（约¥179）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}, {"title": "门店提示", "content": ["区域限行：在毛伊岛租用的车辆只能在柏油路上行驶。"], "type": 47, "code": "2", "sortNum": 7}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "保险服务提示", "content": ["由于当地法规强制要求租车需有第三者责任险，故若没有选择含第三者责任险的套餐，必定会在线下被以更高价格强制加购，建议选择含第三者责任险的套餐。"], "type": 45, "code": "2", "sortNum": 1}, {"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "旅行限制", "content": [], "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["来自亚利桑那州、加利福尼亚州、科罗拉多州、内华达州、犹他州和华盛顿州的租车服务只能在亚利桑那州、加利福尼亚州、科罗拉多州、爱达荷州、堪萨斯州、内布拉斯加州、内华达州、新墨西哥州、俄克拉荷马州、俄勒冈州、南达科他州、得克萨斯州、犹他州、华盛顿州和怀俄明州范围内行驶。 源于佛罗里达州的车辆租赁只能在佛罗里达州境内行驶。 源于德克萨斯州的车辆租赁只能在阿肯色州、路易斯安那州、新墨西哥州、俄克拉荷马州和德克萨斯州内行驶。 来自伊利诺伊州的车辆租赁只能在伊利诺伊州、威斯康星州、爱荷华州、印第安纳州、密苏里州和肯塔基州内行驶。 源于新泽西州的车辆租赁可以进入加拿大。进入加拿大需要有护照。加拿大海关和消费税条例禁止加拿大居民驾驶美国租赁的车辆进入加拿大并返回美国。 在茂宜岛出发的车辆租赁仅限于在公共道路上使用。 在犹他州弗纳尔市租来的车辆只能在犹他州境内行驶。在事先同意的情况下，可以在犹他州以外的地方行驶。当在犹他州以外的地方旅行时，每天的里程数限制在300英里，每英里收取0.25的超额费用。 FOX车辆可以驶入墨西哥，但前提是你已经购买了墨西哥保险。更多详情请见上面的墨西哥保险部分。 从华盛顿州租来的车辆只允许开到加拿大不列颠哥伦比亚省。所有其他省份都被禁止。 前往墨西哥 只有在购买墨西哥保险的情况下，才允许FOX车辆进入墨西哥最靠近加州和亚利桑那州边界的城市/州。请参考墨西哥保险部分，了解允许的覆盖区域。 注意：对于从毛伊岛、纽瓦克和弗纳尔出发的租赁车辆，不允许驶入墨西哥。"], "type": 27, "code": "2", "sortNum": 1}, {"title": "高速路费及ETC收费政策", "content": ["您需要支付租赁期间发生的所有通行费。 供应商提供PlatePass全包服务，这是一项可选择的电子通行费支付服务，租赁期间您在PlatePass收费道路产生的所有通行费和任何相关费用都由每日PlatePass的全包费用支付，费用为每日6.99美元至11.49美元不等。在除PlatePass收费公路以外产生相关费用（包括通行费、费用或相关的罚款、罚金），除了需要支付费用外，您还将被收取每次15.00美元的管理费（每次租赁最多90.00美元）。为避免此项管理费，您可以选择自行支付通行费。 详情请咨询门店。"], "type": 54, "code": "2", "sortNum": 2}], "sortNum": 5}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}], "sortNum": 7}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "安心补充险", "id": 3, "custumerTotalPrice": 90, "custumerDailyPrice": 45, "code": "MP18021529PK00024376", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localTotalPrice": 90, "localDailyPrice": 45.0, "localCurrencyCode": "CNY", "desc": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢自付额", "longDesc": "由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：", "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "FULL_COVERAGE", "days": 2, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "version": "OPI1175082439798685696", "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "提供理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核（1-3个工作日内反馈）", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款（审核通过后3个工作日内）", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}]}, {"subTitle": "您购买的套餐含国内保险公司提供的驾乘意外险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/pdf1903/2.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，请优先保障您的人身安全和健康，垫付相关费用，待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "提供理赔材料", "content": ["意外/医疗：门急诊/住院病历、医疗费用发票/收据原件、医院检查报告、事故证明等；财物损失：损失清单、购货凭证、损失证明等"], "type": 2}, {"title": "理赔审核（1-3个工作日内反馈）", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款（审核通过后3个工作日内）", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}, {"code": "4", "title": "其他保障"}], "briefInsuranceItems": [{"code": "SLDW", "name": "超级碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}]}, {"code": "ALI", "name": "加强三者险", "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000USD", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}]}, {"code": "RAP", "name": "道路救援", "isFromCtrip": false, "insuranceDetail": [{"packageId": 1784, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89543, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}, {"packageId": 89662, "currencyCode": "USD", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}]}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥45/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥45/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 3, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥22/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥22/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "insuranceNotice": "1、驾车人员投保年龄必须符合国际条列，即18周岁以上、70周岁以下；乘客投保年龄范围为0--90周岁，其中被保险人“意外身故、残疾保障“的保险金额71至80周岁为保单所载金额的一半、81至90周岁为四分之一。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010。\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n平安产险交通出行意外伤害保险（互联网版）条款\t注册号：C00001732312022080414693\n平安个人随车物品损失保险条款  注册号：H00001732112017050450351\n平安附加承保地域特约条款\t注册号为：C00001732322018031302341\t\n平安附加适用医疗机构范围约定保险条款\t注册号为：C00001731922019040903801\t\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1703/33.pdf"}], "insuranceCompareItems": [{"description": "0起赔额", "type": "LDW", "descriptionColorCode": "GREEN", "containsDescription": "（无需垫付）"}, {"contains": true, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": true, "type": "PI"}, {"contains": true, "type": "RAP"}, {"contains": true, "type": "CANCEL"}, {"contains": true, "type": "ACCIDENT"}]}], "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}, "promptInfos": [{"title": "押金汇率说明", "contents": [{"stringObjs": [{"content": "押金所需的人民币金额根据当前汇率计算，实际所需人民币金额以用户到店支付时或者授权免押时的汇率为准。"}]}], "type": 12}, {"title": "洛杉矶国际机场 is a popular location on your selected dates. Rates are likely to rise.", "subTitle": "Book now! Don't miss out on low prices.", "type": 29}], "platformInsuranceExtra": {"encourageTitle": "Extra Protection for Your Rental", "encourageDesc": "Your package does not cover you for all losses and damages. Protect yourself from unexpected damage costs with RentalCover.com's protection", "claimProcedure": [{"type": 1, "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"]}, {"type": 2, "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"]}]}, "requestInfo": {"pickupDate": "2024-02-28 17:00:00", "pickupLocationName": "洛杉矶国际机场", "returnDate": "2024-02-29 19:00:00", "returnLocationName": "洛杉矶国际机场", "sourceCountryId": 1, "pLatitude": 33.941589, "rLatitude": 33.941589, "rLongitude": -118.40853, "pLongitude": -118.40853, "pDate": "20240228170000", "rDate": "20240229190000", "pCityId": 347, "rCityId": 347}, "licenceCountryPolicy": {"title": "驾照", "type": 15, "subObject": [{"title": "中国大陆驾照", "summaryContent": ["您需携带中国大陆驾照原件和英文公证件（或驾照国际翻译认证件和中国大陆驾照原件）才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "CN", "optimalType": "1"}, {"title": "中国香港驾照", "summaryContent": ["您需携带国际驾照和香港驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "HK", "optimalType": "1"}, {"title": "中国澳门驾照", "summaryContent": ["您需携带国际驾照和澳门驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "MO", "optimalType": "1"}, {"title": "中国台湾驾照", "summaryContent": ["您需携带国际驾照和台湾驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "TW", "optimalType": "1"}], "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "持非中国（含港澳台）驾照无法预订。建议更换驾驶员或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17}]}, "crossPolicy": {"crossLocationsInfos": [{"crossType": 3, "locations": [{"name": "安提瓜和巴布达", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "regionId": "201", "isSelected": false}, {"name": "安圭拉", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "regionId": "275", "isSelected": false}, {"name": "阿鲁巴", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "regionId": "171", "isSelected": false}, {"name": "巴巴多斯", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "202", "isSelected": false}, {"name": "百慕大", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "207", "isSelected": false}, {"name": "波多黎各", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "208", "isSelected": false}, {"name": "伯利兹", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "210", "isSelected": false}, {"name": "巴拿马", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "18", "isSelected": false}, {"name": "巴哈马", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "185", "isSelected": false}, {"name": "多米尼加共和国", "status": 3, "statusName": "不允许跨境", "firstChar": "D", "regionId": "276", "isSelected": false}, {"name": "多米尼克", "status": 3, "statusName": "不允许跨境", "firstChar": "D", "regionId": "217", "isSelected": false}, {"name": "法属圣马丁", "status": 3, "statusName": "不允许跨境", "firstChar": "F", "regionId": "291", "isSelected": false}, {"name": "瓜德罗普岛", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "261", "isSelected": false}, {"name": "格林纳达", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "220", "isSelected": false}, {"name": "哥斯达黎加", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "38", "isSelected": false}, {"name": "古巴", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "39", "isSelected": false}, {"name": "荷兰加勒比区", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "regionId": "259", "isSelected": false}, {"name": "荷属圣马丁", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "regionId": "295", "isSelected": false}, {"name": "海地", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "regionId": "41", "isSelected": false}, {"name": "洪都拉斯", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "regionId": "44", "isSelected": false}, {"name": "加拿大", "status": 2, "statusName": "条件跨境", "firstChar": "J", "policy": "在新泽西州租用的车辆允许跨境至加拿大。在华盛顿州租用的车辆只允许驶入加拿大不列颠哥伦比亚省。其他省份禁止驾驶。进入加拿大需要携带护照。禁止加拿大居民驾驶美国租赁车辆进入加拿大并返回美国。", "regionId": "47", "isSelected": false}, {"name": "开曼群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "K", "regionId": "223", "isSelected": false}, {"name": "库拉索岛", "status": 3, "statusName": "不允许跨境", "firstChar": "K", "regionId": "294", "isSelected": false}, {"name": "墨西哥", "status": 2, "statusName": "条件跨境", "firstChar": "M", "policy": "仅除夏威夷州、新泽西州、犹他州以外的州出发的车辆才可驶入墨西哥。且只允许驶入加利福尼亚和亚利桑那边界的墨西哥城市/州。对接壤可跨境的城市，请咨询门店，以门店政策为准。", "regionId": "72", "isSelected": false}, {"name": "马提尼克", "status": 3, "statusName": "不允许跨境", "firstChar": "M", "regionId": "279", "isSelected": false}, {"name": "美属维尔京群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "M", "regionId": "280", "isSelected": false}, {"name": "尼加拉瓜", "status": 3, "statusName": "不允许跨境", "firstChar": "N", "regionId": "198", "isSelected": false}, {"name": "圣皮埃尔和密克隆岛", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "289", "isSelected": false}, {"name": "圣巴泰勒米", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "297", "isSelected": false}, {"name": "萨尔瓦多", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "238", "isSelected": false}, {"name": "圣基茨和尼维斯", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "243", "isSelected": false}, {"name": "圣卢西亚", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "244", "isSelected": false}, {"name": "圣文森特和格林纳丁斯", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "246", "isSelected": false}, {"name": "特克斯和凯科斯群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "T", "regionId": "265", "isSelected": false}, {"name": "特立尼达和多巴哥", "status": 3, "statusName": "不允许跨境", "firstChar": "T", "regionId": "252", "isSelected": false}, {"name": "危地马拉", "status": 3, "statusName": "不允许跨境", "firstChar": "W", "regionId": "90", "isSelected": false}, {"name": "英属维尔京群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "Y", "regionId": "282", "isSelected": false}, {"name": "牙买加", "status": 3, "statusName": "不允许跨境", "firstChar": "Y", "regionId": "101", "isSelected": false}, {"name": "英属蒙塞拉特岛", "status": 3, "statusName": "不允许跨境", "firstChar": "Y", "regionId": "299", "isSelected": false}], "crossTypeName": "跨境政策", "summaryPolicies": ["若您的行程中涉及跨境，请提前选择"], "title": "选择计划前往的国家", "subTitle": "门店支持在以下区域跨境使用车辆："}, {"crossType": 1, "crossTypeName": "跨岛政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨岛，建议您更换其它租车公司或车型组。"]}, {"crossType": 2, "crossTypeName": "跨州政策", "summaryPolicies": ["车辆只能在亚利桑那州、加利福尼亚州、科罗拉多州、爱达荷州、堪萨斯州、内布拉斯加州、内华达州、新墨西哥州、俄克拉荷马州、俄勒冈州、南达科他州、德克萨斯州、犹他州、华盛顿州和怀俄明州境内行驶。"], "summaryTitle": "条件跨州"}], "notes": ["若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。除特别说明的情况外，“跨境费用”为前往单个国家所需的费用。"], "title": "旅行限制"}, "locationPrompt": "美国地区偶发砸窗盗窃，道路救援费用较高，建议升级含安心补充险的套餐，保障范围更广，比在门店购买更便宜。", "osdCompareTitle": ["车辆碰撞及盗抢", "玻璃轮胎底盘保障", "第三者保障", "人身财物险", "道路救援补偿", "旅行取消", "意外津贴/住院津贴"]}