{"baseResponse": {"isSuccess": true, "code": "200", "extMap": {"carDamage": "38", "dropoffCityId": "2", "creditScore": "21", "queryRentalMustRead": "36", "apiCost": "76.0", "storeAndUserPic": "3", "productDetail": "63", "errorSource": "RF", "userAuth": "10", "start": "2023-05-19 16:58:49", "errorCode": "0", "pageName": "Product", "uid": "_WeChat4031051128", "priceDetail": "76", "allCost": "105.0", "easyLifeServiceLabel": "2", "end": "2023-05-19 16:58:49", "dataConvertResCost": "29.0", "pickupCityId": "2"}}, "priceChange": false, "isSelected": false, "fType": false, "needDownGrade": false, "pickupStoreInfo": {"storeCode": "107408", "bizVendorCode": "71523", "telephone": "86-13386109670", "storeName": "水产路店", "address": "上海市宝山区杨行镇水产路1439号", "longitude": 121.480803, "latitude": 31.379838, "storeGuild": "店员收费上门送取车", "storeGuildSplit": "店员收费送车上门", "storeWay": "送车上门取还车", "workTime": {"openTimeDesc": "{\"\":\"08:00 - 23:00\"}", "openTime": "08:00", "closeTime": "23:00"}, "storeType": 1, "countryId": 1, "countryName": "中国", "provinceId": 2, "cityId": 2, "cityName": "上海", "pickUpOnDoor": true, "freeShuttle": false, "wayInfo": 2, "showType": 1}, "returnStoreInfo": {"storeCode": "107408", "bizVendorCode": "71523", "telephone": "86-13386109670", "storeName": "水产路店", "address": "上海市宝山区杨行镇水产路1439号", "longitude": 121.480803, "latitude": 31.379838, "storeGuild": "店员收费上门送取车", "storeGuildSplit": "店员收费上门取车", "storeWay": "上门取车", "workTime": {"openTimeDesc": "{\"\":\"08:00 - 23:00\"}", "openTime": "08:00", "closeTime": "23:00"}, "storeType": 1, "countryId": 1, "countryName": "中国", "provinceId": 2, "cityId": 2, "cityName": "上海", "returnOnDoor": true, "freeShuttle": false, "wayInfo": 2, "showType": 1}, "vendorInfo": {"bizVendorCode": "71523", "vendorName": "申恺租车", "vendorImageUrl": "", "vendorCode": "71523", "isBroker": false, "platformCode": "", "platformName": "", "haveCoupon": false, "preAuthType": 0, "supportPreAuth": 0, "prePayType": 0, "supplierId": 1298821, "vendorFullName": "上海申恺汽车租赁有限公司", "vendorLicenseUrl": "https://dimg04.c-ctrip.com/images/0302y120008yhgbtzABAC.png?proc=watermark/t_6JCl5Lia5omn54Wn5L-h5oGv5YWs56S65LiT55So,s_70,l_center,r_315,d_40,c_696969,F732"}, "vehicleInfo": {"brandId": 109, "brandEName": "荣威", "brandName": "荣威", "name": "荣威RX5新能源", "vehicleCode": "4092", "imageUrl": "https://dimg04.c-ctrip.com/images/0RV6y12000au0o8az9D1B.png", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 5, "displacement": "1.5T", "struct": "SUV", "fuel": "92号", "gearbox": "自动变速箱(AT)、机械式自动变速箱(AMT)", "driveMode": "前置前驱", "imageList": ["http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-waiguan1.jpg", "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-xijie-2.jpg", "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-zuoyi2.jpg"], "userRealImageCount": 0, "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-waiguan1.jpg", "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-xijie-2.jpg", "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-zuoyi2.jpg"], "license": "沪牌", "licenseStyle": "2", "licenseDescription": "沪牌车辆可在上海市内各高架、隧道行驶", "sourcePicInfos": [{"source": 0, "type": 2, "sourceName": "年款/颜色等以实物为准", "picList": [{"imageUrl": "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-waiguan1.jpg", "sortNum": 1}, {"imageUrl": "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-xijie-2.jpg", "sortNum": 2}, {"imageUrl": "http://pages.ctrip.com/carisd/vd/rongwei-RX5xinnengyuan-zuoyi2.jpg", "sortNum": 3}, {"imageUrl": "https://dimg04.c-ctrip.com/images/0RV6z12000au0ocysDF52.jpg", "sortNum": 0}]}], "oilType": 4, "fuelType": "插电式"}, "flightDelayRule": {}, "commentInfo": {"level": "超棒", "commentCount": 1405, "overallRating": "4.9", "maximumRating": 5.0, "hasComment": 1, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=107408&calabiVehicleId=4092&vehicleName=荣威RX5新能源&productCategoryId=35&isHideNavBar=YES"}, "packageInfos": [{"subType": 0, "easyLifeTag": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满", "sortNum": 10, "subTitle": "", "showLayer": 0}, {"title": "车辆守护升级", "type": 0, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 99, "subTitle": "*覆盖损失范围以预订页面内披露为准"}, {"title": "免加油/充电服务费", "type": 0, "description": "还车时，若油量/电量少于取车油量/电量，无需支付加油/充电服务费", "sortNum": 99, "subTitle": "*油量/电量差价仍需支付"}]}], "productDetails": [{"insPackageId": 0, "productInfoList": [{"priceInfoList": [{"currentCarPrice": 108, "currentDailyPrice": 108, "currentTotalPrice": 233, "localCarPrice": 108, "localDailyPrice": 108, "localTotalPrice": 233.0, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 233, "localOnewayfee": 0, "localPoaPrice": 0, "localPrepaidPrice": 233.0, "isContainOnewayFee": true, "payMode": 2, "vcExtendRequest": {"vendorVehicleId": "1863522"}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": false, "isFreeCancelNow": true, "cancelEncourage": "取车前免费取消，计划有变也无需担心"}, "ageRestriction": {"licenceAge": 0}, "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "description": "行驶证注册年限小于两年。", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "tagGroups": 1, "tagSortNum": 9, "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "description": "无需支付租车押金和违章押金，取还车手续更便捷。", "sortNum": 10, "colorCode": "2", "subList": [{"description": "在下一步我们会基于您的信用情况进行综合评估，若评估通过，您可在下一步选择信用租服务。若不通过，您可选择其他方式支付押金。"}], "labelCode": "3746", "groupCode": "MarketGroup1347", "tagGroups": 2, "tagSortNum": 4, "groupId": 3, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "description": "车内配有倒车影像。", "sortNum": 14, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "tagGroups": 1, "tagSortNum": 14, "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "description": "车内配有倒车雷达。", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "tagGroups": 1, "tagSortNum": 15, "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "description": "取车时间前可免费取消。", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "description": "保障取车时油量满", "sortNum": 10000, "colorCode": "2", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "取车前可免费取消", "type": 1, "sortNum": 999, "colorCode": "2", "tagGroups": 2, "tagSortNum": 3}], "rentalGuarantee": [{"name": "基础服务", "quantity": 1, "localDailyPrice": 50, "localTotalPrice": 50, "localCurrencyCode": "CNY", "currentDailyPrice": 50, "currentTotalPrice": 50, "currentCurrencyCode": "CNY", "description": ["您无需承担车辆损失超过1500元的部分（含玻璃），轮胎及其他不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "<tag>tag0", "<tag>tag1"], "allTags": [{"title": "您需要承担车辆停运费", "type": 1, "code": "tag0", "colorCode": "BLACK"}, {"title": "车损5000元以下，您无需承担车辆折旧费", "type": 0, "code": "tag1", "colorCode": "BLACK"}, {"title": "因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为：", "type": 1, "code": "tag2"}, {"title": "停运费 = 车辆维修期间每日订单金额之和 × 100%", "type": 1, "code": "tag3"}, {"title": "·每日订单金额包含基本保险，不含手续费及附加服务费", "type": 1, "code": "tag4"}, {"title": "·因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）", "type": 1, "code": "tag5"}], "longDescription": ["基础服务费已包含在费用明细内。车辆发生意外时，租车公司承担车辆损失中超过1500元部分的费用，以及协助处理事故需要呼救中心、门店服务等服务费用（不含玻璃、轮胎、停运及折旧费，上述具体费用标准, 以租车公司确认为准）"], "type": 1, "group": 0, "uniqueCode": "1002", "insuranceDetailDescription": {"items": [{"title": "服务详情", "contents": [], "head": "", "foot": "", "pageTitle": "", "pageUrl": ""}, {"title": "车辆事故处理流程", "contents": [{"type": "3", "title": "立即联系门店及交通警察", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"type": "3", "title": "拍照并留存记录信息", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"type": "3", "title": "", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"type": "3", "title": "", "content": "请您配合交警处理完事故后，方可离开事故现场"}, {"type": "3", "title": "", "content": "准备材料，进行保险理赔流程"}], "head": "", "foot": "", "pageTitle": "查看更多事故指导及费用明细", "pageUrl": ""}], "foot": "{\"basic\":\"基础服务由车行提供\",\"advanced\":\"优享服务由车行提供\",\"easyLife\":\"无忧尊享服务由车行提供\"}"}, "descriptionV2": ["您无需承担车辆损失超过1500元的部分（含玻璃），轮胎及其他不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "<tag>tag2", "<tag>tag3", "<tag>tag4", "<tag>tag5", "车损5000元以下，您无需承担车辆折旧费", "车损在5000及以上时，收取车辆维修费总额的20%作为折旧费。"]}, {"name": "优享服务", "quantity": 1, "localDailyPrice": 50.0, "localTotalPrice": 50.0, "localCurrencyCode": "CNY", "currentDailyPrice": 50.0, "currentTotalPrice": 50.0, "currentCurrencyCode": "CNY", "description": ["您无需承担全部车辆损失（含玻璃），轮胎及其他不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "<tag>tag0", "<tag>tag1"], "allTags": [{"title": "您需要承担车辆停运费", "type": 1, "code": "tag0", "colorCode": "BLACK"}, {"title": "车损5000元以下，您无需承担车辆折旧费", "type": 0, "code": "tag1", "colorCode": "BLACK"}, {"title": "因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为：", "type": 1, "code": "tag2"}, {"title": "停运费 = 车辆维修期间每日订单金额之和 × 100%", "type": 1, "code": "tag3"}, {"title": "·每日订单金额包含基本保险，不含手续费及附加服务费", "type": 1, "code": "tag4"}, {"title": "·因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）", "type": 1, "code": "tag5"}], "longDescription": ["基础服务费的补充保护。车辆发生意外时，您无需承担1500元以内的车辆损失费用，包括协助处理事故需要呼叫中心服务、门店服务等服务费用。"], "type": 0, "group": 1, "uniqueCode": "2001", "insuranceDetailDescription": {"items": [{"title": "服务详情", "contents": [], "head": "", "foot": "", "pageTitle": "", "pageUrl": ""}, {"title": "车辆事故处理流程", "contents": [{"type": "3", "title": "立即联系门店及交通警察", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"type": "3", "title": "拍照并留存记录信息", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"type": "3", "title": "", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"type": "3", "title": "", "content": "请您配合交警处理完事故后，方可离开事故现场"}, {"type": "3", "title": "", "content": "准备材料，进行保险理赔流程"}], "head": "", "foot": "", "pageTitle": "查看更多事故指导及费用明细", "pageUrl": ""}], "foot": "{\"basic\":\"基础服务由车行提供\",\"advanced\":\"优享服务由车行提供\",\"easyLife\":\"无忧尊享服务由车行提供\"}"}, "descriptionV2": ["您无需承担全部车辆损失（含玻璃），轮胎及其他不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "<tag>tag2", "<tag>tag3", "<tag>tag4", "<tag>tag5", "车损5000元以下，您无需承担车辆折旧费", "车损在5000及以上时，收取车辆维修费总额的20%作为折旧费。"]}, {"name": "无忧尊享服务", "quantity": 1, "localDailyPrice": 100.0, "localTotalPrice": 100.0, "localCurrencyCode": "CNY", "currentDailyPrice": 100.0, "currentTotalPrice": 100.0, "currentCurrencyCode": "CNY", "description": ["您无需承担全部车辆损失（含玻璃、轮胎），不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "<tag>tag0", "<tag>tag1"], "allTags": [{"title": "无需承担车辆停运费", "type": 0, "code": "tag0", "colorCode": "BLACK"}, {"title": "车损3万元以下，您无需承担车辆折旧费", "type": 0, "code": "tag1", "colorCode": "BLACK"}], "longDescription": ["基础服务费的补充保护。车辆发生意外时，您无需承担1500元以内的车辆损失费用，包括协助处理事故需要呼叫中心服务、门店服务等服务费用。"], "type": 0, "group": 1, "uniqueCode": "2011", "insuranceDetailDescription": {"items": [{"title": "服务详情", "contents": [], "head": "", "foot": "", "pageTitle": "", "pageUrl": ""}, {"title": "车辆事故处理流程", "contents": [{"type": "3", "title": "立即联系门店及交通警察", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"type": "3", "title": "拍照并留存记录信息", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"type": "3", "title": "", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"type": "3", "title": "", "content": "请您配合交警处理完事故后，方可离开事故现场"}, {"type": "3", "title": "", "content": "准备材料，进行保险理赔流程"}], "head": "", "foot": "", "pageTitle": "查看更多事故指导及费用明细", "pageUrl": ""}], "foot": "{\"basic\":\"基础服务由车行提供\",\"advanced\":\"优享服务由车行提供\",\"easyLife\":\"无忧尊享服务由车行提供\"}"}, "descriptionV2": ["您无需承担全部车辆损失（含玻璃、轮胎），不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "您无需承担车辆停运费", "车损30000元以下，您无需承担车辆折旧费", "车损在30000及以上时，收取车辆维修费总额的20%作为折旧费。"]}], "rentalGuaranteeV2": {"rentalGuaranteeTitle": ["车损保障", "三者保障", "免折旧费", "免停运费", "无需垫付"], "vendorServiceDesc": "车行服务是指车行为客人安全用车提供的服务，包含车行手续（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。", "vendorServiceSubDesc": "上述车损险和三者险仅覆盖保险理赔范围内的损失，理赔范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求进行理赔导致保险公司拒赔的情况，您需承担全额损失。", "packageDetailList": [{"name": "基础服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 50, "gapPrice": 0, "description": [{"description": "1500元以内自付 <tag>tag0", "type": "CDWcn", "attr": "{\"carCoverage\":\"1500元\",\"vendorId\":71523,\"isBasic\":true,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "RED"}, {"contains": true, "description": "100万", "type": "TPLcn", "attr": "100万"}, {"contains": true, "description": "车损5000元以下免收", "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}"}, {"contains": false, "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "allTags": [{"title": "车轮损失自付", "code": "tag0", "colorCode": "<PERSON>"}], "insuranceDetailDescription": [{"title": "车损保障", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "1500元以上的部分"}}, {"key": "客户承担", "value": {"content": "≤1500元（据实承担）"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "保司核定理赔金额"}}, {"key": "客户承担", "value": {"content": "保司核定车辆价值－理赔金额"}}]}], "type": "CDWcn"}, {"title": "三者保障", "contains": true, "content": [null], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "100万元"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "100万元"}}, {"key": "客户承担", "value": {"content": "100万元以上的部分"}}]}], "type": "TPLcn"}, {"title": "折旧费", "contains": true, "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "desc": "", "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "5000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "5000元以上", "value": {"content": "维修费总额的20%"}}], "styleType": 1}], "type": "DPN", "subDesc": ["因第三方全责导致的车损，不收取用户/承租人的折旧费（因用户/承租人原因导致无法收取的除外）"]}, {"title": "停运费", "contains": false, "content": ["因车辆事故或驾车不当，导致车辆故障影响正常运营时，门店将收取停运费，收费标准为："], "subContent": [{"table": [{"key": "", "value": {"great": false, "content": "停运费 = 车辆维修期间每日订单金额之和 × 100%"}}]}], "type": "OTG", "subDesc": ["每日订单金额包含基本保险，不含手续费及附加服务费", "因第三方全责导致的车损，不收取用户/承租人的停运费（因用户/承租人原因导致无法收取的除外）"]}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车轮（包括轮胎、轮毂、轮毂罩）损坏；以及车牌等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "1002", "type": 1}, {"name": "优享服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 50, "gapPrice": 50, "cityEncourage": "城市地区多剐蹭，建议您升级服务", "description": [{"description": "全额赔付 <tag>tag0", "type": "CDWcn", "attr": "{\"carCoverage\":\"0元\",\"vendorId\":71523,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "GREEN"}, {"contains": true, "description": "100万", "type": "TPLcn", "attr": "100万"}, {"contains": true, "description": "车损5000元以下免收", "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"5000\",\"desc\":\"车损5000元以下免收折旧费\",\"summary\":\"车损5000元以下免收折旧费\",\"containsDescription\":\"车损5000元以下免收\"}"}, {"contains": false, "type": "OTG", "attr": "{\"cover\":false,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "allTags": [{"title": "车轮损失自付", "code": "tag0", "colorCode": "<PERSON>"}], "insuranceDetailDescription": [{"title": "车损保障", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}], "type": "CDWcn"}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车轮（包括轮胎、轮毂、轮毂罩）损坏；以及车牌等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "2001", "type": 0, "selectedEncourage": "经济实用 超值之选"}, {"name": "尊享服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 100, "gapPrice": 100, "description": [{"description": "全额赔付", "type": "CDWcn", "attr": "{\"carCoverage\":\"0元\",\"vendorId\":71523,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}", "descriptionColorCode": "GREEN"}, {"contains": true, "description": "100万", "type": "TPLcn", "attr": "100万"}, {"contains": true, "description": "车损3万元以下免收", "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"3万\",\"desc\":\"车损3万元以下免收折旧费\",\"summary\":\"车损3万元以下免收折旧费\",\"containsDescription\":\"车损3万元以下免收\",\"descriptionColorCode\":\"GREEN\"}", "descriptionColorCode": "GREEN"}, {"contains": true, "type": "OTG", "attr": "{\"cover\":true,\"desc\":\"您无需承担车辆停运费\",\"summary\":\"免停运费\"}"}, {"contains": true, "type": "Disbursements", "attr": "含无需垫付"}], "allTags": [], "insuranceDetailDescription": [{"title": "车损保障", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}], "type": "CDWcn"}, {"title": "折旧费", "contains": true, "content": ["按车辆受损情况及保险公司定损金额，折旧费收费标准为："], "subContent": [{"title": "", "desc": "", "table": [{"key": "维修费", "value": {"content": "折旧费"}}, {"key": "30000元及以内", "value": {"great": true, "content": "免折旧费"}}, {"key": "30000元以上", "value": {"content": "维修费总额的20%"}}], "styleType": 1}], "type": "DPN", "subDesc": ["因第三方全责导致的车损，不收取用户/承租人的折旧费（因用户/承租人原因导致无法收取的除外）"]}, {"title": "免停运费", "contains": true, "great": true, "content": ["免收因车损产生的车辆停运费"], "type": "OTG"}, {"title": "无须垫付", "contains": true, "great": true, "content": ["无需垫付定金或相关费用，车辆维修出险后，根据费用单等，补缴给租车公司您所需赔付的部分。"]}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车牌等非车辆本身的设备或物料的遗失与损坏。", "3.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "2011", "type": 0, "selectedEncourage": "全面保障 无忧之选"}], "purchasingNotice": {"title": "购买须知", "content": ["1.车行服务由租车公司提供，仅支持取车前购买", "2.若发生续租，已购买的车行服务的订单续租时必须购买相同服务"]}, "vendorServiceDetail": [{"title": "车辆事故处理流程", "type": 1, "description": "发生车损（本车与第三方，或本车单独发生事故）时，请按下方流程处理，否则将无法享受门店服务保障权益。", "contents": [{"title": "报警并联系门店", "type": 1, "content": ["拨打122报警（上海地区拨打110），并立即联系门店获取后续操作指引（如保险报案等）。车辆需由门店知晓并同意后再修理。擅自修理造成的额外损失将由用户或承租方承担。"]}, {"title": "拍照并留存记录信息", "type": 1, "content": ["包括但不限于涉事各方车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况。"]}, {"title": "明确事故责任，配合交警完成事故处理", "type": 1, "content": ["等待交警明确涉事各方事故责任，交警处理完成后方可离开现场。"]}, {"title": "提交材料进行理赔（如需）", "type": 1, "content": ["如门店判断需进行保险报案，用户或承租方须配合准备材料、联系保险公司进行保险理赔报案。如因用户或承租方未配合保险报案导致的额外损失将由用户或承租方承担。"]}]}, {"title": "车行服务理赔说明", "type": 2, "description": "发生车损（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得理赔", "contents": [{"title": "不予理赔说明", "type": 2, "content": ["1、发生事故时未及时通知租车公司或未申报保险", "2、无事故证明材料或无保险理赔材料", "3、无证驾驶、酒驾、超速等其他保险不予理赔或责任免除场景"]}, {"title": "保险理赔说明", "type": 4, "content": ["1. 如果车辆不慎发生事故，请注意人身安全，并立即致电门店，按门店指引报案、收集理赔材料。齐全的理赔材料可以节省理赔等待时间。\n1. 如用户或承租方已还车并提交了齐全的理赔材料，则不需要垫付本车维修等保险公司应付费用，仅需：\na)支付服务保障详情中列明的的由用户或承租方承担的损失，以及服务保障范围外的所有责任和损失；\nb)垫付第三方损失，该费用将在保险公司完成理赔后返还。", "2. 如用户或承租方还车时不能提供齐全的理赔材料，一般有以下2种方式：\na.)无需先行支付。在保险公司完成定损或车辆维修完成后，根据实际费用计算用户或承租方需承担的金额支付给门店；\nb.)需用户或承租方先行交付部分金额（金额与门店现场沟通）。在保险公司完成定损或车辆实际维修完成后，根据实际费用金额多退少补。 "]}]}], "claimSettlementVersion": ""}, "priceCode": "b7a595a4f1a643c3bc05cab857650d52"}], "equipments": [], "ctripInsurances": [{"name": "人身及财物险", "quantity": 1, "vendorServiceCode": "2000896", "localDailyPrice": 25.0, "localTotalPrice": 25.0, "localCurrencyCode": "CNY", "currentDailyPrice": 25.0, "currentTotalPrice": 25.0, "currentCurrencyCode": "CNY", "description": ["驾乘意外30万元/人，医疗费用2万元/人", "随车财务损失2000元/车", "专业道路救援服务"], "allTags": [{"title": "仅支持线上购买"}], "longDescription": ["(携程提供)保障全车人员在驾乘过程中发生的意外及租赁车辆内行李、物品损失，保障无免赔。"], "type": 0, "uniqueCode": "2000896"}], "zhiMaInfo": {"supportZhiMa": true}, "pickUpMaterials": [{"title": "驾驶员本人身份证原件", "summaryContent": ["证件需在有效期内"], "type": 0, "subObject": [{"title": "护照/回乡证/台胞证"}]}, {"title": "驾驶员本人驾照原件", "summaryContent": ["有效期2个月以上"], "type": 1}], "carRentalMustRead": [{"title": "取消政策", "type": 1, "code": "1"}, {"title": "里程限制", "contentObject": [{"stringObjs": [{"content": "租期内没有里程数限制"}]}], "type": 3, "code": "1"}, {"title": "能源费用及服务费", "content": [], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "还车时需保持与取车时能源量（油量/电量/燃料量）一致"}]}, {"stringObjs": [{"content": "若还车时能源量多于/少于发车能源量，涉及相关费用的结算标准，请以门店告知为准。"}]}], "type": 4, "code": "1"}, {"title": "常规费用收费标准", "type": -1, "code": "2", "subObject": [{"title": "能源费用及服务费", "content": [], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "还车时需保持与取车时能源量（油量/电量/燃料量）一致"}]}, {"stringObjs": [{"content": "若还车时能源量多于/少于发车能源量，涉及相关费用的结算标准，请以门店告知为准。"}]}], "type": 4, "code": "1"}, {"title": "零散小时费", "content": [], "contentObject": [{"stringObjs": [{"content": "总租期不足24小时，计费规则如下："}]}, {"stringObjs": [{"content": "<table>hourlyRatePolicyTable"}]}, {"stringObjs": [{"content": "*如您选择购买附加服务，收费标准如下"}]}, {"stringObjs": [{"content": "<table>additionServiceFeeTable"}]}], "type": 49, "code": "2"}, {"title": "提前还车", "content": [], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "1.请提前联系门店"}]}, {"stringObjs": [{"content": "平日:支持提前还车，需要提前18小时联系门店"}]}, {"stringObjs": [{"content": "节假日:支持提前还车，需要提前24小时联系门店"}]}, {"stringObjs": [{"content": "如果提前还车的实际租期总费用由于增加夜间服务费等原因导致大于订单原租期的总费用时，用户需要补足支付相应的差额后方能成功申请提前还车。"}]}, {"stringObjs": [{"content": "提前还车不改变还车方式。如需修改还车方式，请在操作提前还车后与门店联系，能否修改以门店回复为准。所涉费用另行计算。"}]}, {"contentStyle": "13", "stringObjs": [{"content": "2.退费标准"}]}, {"stringObjs": [{"content": "平日:提前还车退费，违约金比例30%"}]}, {"stringObjs": [{"content": "节假日:提前还车不退费"}]}, {"stringObjs": [{"content": "退费金额 =（原租期订单总金额-新租期订单总金额）×（1-违约金比例）"}]}, {"stringObjs": [{"content": "提前还车将收取实际租期总费用与订单中原租期总费用差额的固定比例作为违约金，剩余退还给用户。"}]}], "type": 10, "code": "2"}, {"title": "续租/延迟还车", "content": [], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "1.请提前联系门店"}]}, {"stringObjs": [{"content": "平日：需提前12小时联系门店"}]}, {"stringObjs": [{"content": "节假日：需提前24小时联系门店"}]}, {"contentStyle": "13", "stringObjs": [{"content": "2.收费标准"}]}, {"stringObjs": [{"content": "基础服务收费标准"}]}, {"stringObjs": [{"content": "续租或延迟还车申请成功后，总租期等于“已租+续租”的总时间，租金根据总租期计算。"}]}, {"stringObjs": [{"content": "若门店有零散小时收费规则，则总租期内非整日的部分，按零散小时收费政策计算。"}]}, {"stringObjs": [{"content": "续租/延迟还车费用 = 总租期金额 - 原单价格"}]}, {"stringObjs": [{"content": "附加服务收费标准"}]}, {"stringObjs": [{"content": "优享/尊享服务"}]}, {"stringObjs": [{"content": "如用户/承租人在原订单中已购买优享/尊享等服务，续租或延迟还车须继续加购已购买的服务，并收取续租时间相应的优享/尊享服务费，非整日部分（如有）计费遵循服务费零散小时收费标准。如用户/承租人在原订单中未购买优享/尊享等服务，续租或延迟还车时将无法加购。"}]}, {"stringObjs": [{"content": "人身及财务险"}]}, {"stringObjs": [{"content": "如用户/承租人在原订单中购买人身及财物险，续租或延迟还车时须继续加购该保障，加购费用根据续租时间计算，以天（24小时）为单位收费。如用户/承租人在原订单中未购买人身及财务险，续租或延迟还车时将无法加购。"}]}], "type": 12, "code": "2"}, {"title": "强行续租", "content": [], "contentObject": [{"stringObjs": [{"content": "未按规定联系门店并办理续租手续，或未经门店同意而强行延迟还车的，将被视为强行续租。强行续租除必须支付正常续租价格外，还将产生强行续租违约金。"}]}, {"stringObjs": [{"content": "强行续租费用 = 首日租金 × (300%)"}]}], "type": 48, "code": "2"}], "sortNum": 1}, {"title": "租车及违章押金相关", "type": -3, "code": "2", "subObject": [{"title": "车损外观损伤（修复或更换）费用标准", "content": [], "contentObject": [{"stringObjs": [{"content": "车辆外观损伤修复费用和车辆外观件更换费均需设置为一口价费用标准=维修/更换费用 +拆装费用 + 工时费用车辆外观损伤修复费用标准（满足下列任一条件就收费）1.漆面划伤触摸有凹槽且损伤长度在5cm以上2.漆面划伤触摸有凹槽且宽度0.5cm以上3.外观凹陷且面积4cm²以上4.外观隆起且面积4cm²以上车辆外观损伤修复费用标准："}]}, {"stringObjs": [{"content": ""}]}, {"stringObjs": [{"content": "<table>CarDamage1"}]}, {"stringObjs": [{"content": "车辆外观件更换费用标准（满足下列任一条件就收费）1.外观出现穿孔 2.外观出现开裂 3.外观件脱落 4.外观件遗失 车辆外观件更换费用标准："}]}, {"stringObjs": [{"content": ""}]}, {"stringObjs": [{"content": "<table>CarDamage3"}]}, {"stringObjs": [{"content": "1. 一项以上的外观或部件损伤，需叠加计算的总费用为车辆维修费。2. 如部件缺失或不可修复，除按上表支付修复费用外，还需按4S店价格另外支付备件费用。3. 钥匙仅在出现金属钥匙主体断裂时并且客户提供了完整的损坏钥匙的情况下，进行补配机械钥匙，如果钥匙丢失等情况，按照更换全车锁价格收取。"}]}], "type": 52, "code": "2"}, {"title": "随车物品损失", "content": [], "contentObject": [{"stringObjs": [{"content": "车内随车设备和物品不在保险公司赔付范围内，车辆租期如内有遗失或损坏，需要照价赔偿，请参照《随车物品清单及价格表》或当地 4S 店价格进行赔偿。（豪华进口车的随车物品按门店或 4s 店实际报价为准）随车物品清单及价格表:"}]}, {"stringObjs": [{"content": ""}]}, {"stringObjs": [{"content": "<table>BelongingsCost1"}]}, {"stringObjs": [{"content": "若用车中发生车辆钥匙丢失，租车公司需更换全车锁，请立即联系客服或门店，实际费用按 \n具体车型及市场价浮动。\n其他损失，按租车合同及相关法规协商处理。"}]}], "type": 53, "code": "2"}, {"title": "违章处理违约金", "content": [], "contentObject": [{"stringObjs": [{"content": "1.须知<br/>承租方须对在车辆租赁期间产生的交通违章承担责任，并予以处理。违章信息以各地交管部门的记录为准；<br/>2.如何得知租期内是否产生违章？<br/>门店将在承租方还车后10个工作日左右查询或收到承租方未处理的违章信息（具体时间以各地方交管部门通知为准），并以电话、短信等方式通知承租方。<br/>3.还车后如无违章，违章押金将如何退还？<br/>还车后如查实车辆在租赁期间内无违章，违章押金预计在30天后由实际支付渠道原路退还，退还金额到账时间以各支付渠道为准；免违章押金订单预计在30天后解除已授权的扣款通道。<br/>4.承租人自行处理违章时限是多久？<br/>承租方须在被通知之日起，7个工作日内，将违章事宜处理完毕。否则将产生200元/天 的违章逾期处理违约金。<br/>门店有权从承租方支付的违章押金内、或通过免违章押金订单的授权扣款通道，扣除上述逾期处理违约金。如押金扣除后仍不足以抵偿全部损失的，门店有权根据实际损失情况向承租方追偿。<br/>5.如何处理违章？<br/>根据交管部门要求，需携带本人驾驶证原件、身份证原件及车辆行驶证原件等，到违章当地或车牌所在地的交管部门办理；具体所需资料和违章处理相关规定各地存在差异（如部分地区要求提供租车合同，委托书等其他文件），部分城市开通了异地处理违章的政策，请事先咨询当地交管部门。<br/>部分情况下，车辆违章可以通过12123 APP或其他合法的第三方程序网上办理，具体情况请联系门店沟通。<br/>6. 如需借用行驶证等资料怎么办？<br/>承租方在自行处理违章时，如需借用行驶证原件，须提前9天联系门店预约。违章事宜处理完毕后，须及时将处理证件归还车行。<br/>在确认不耽误车辆行程的情况下，同城可免费借用6小时（门店营业时间范围内），异地可免费借用48小时（从门店将行驶证寄出至承租人将行驶证寄出）。用户须刷取500元押金预授权。在按时并完整归还证件时，门店将全额解除该预授权。若用车城市全面支持电子行驶证，则不限时免费。<br/>若实际使用车辆行驶证超过免费借用时间，则按照实际发生天数收取车辆租金（包括基础日租金+基本服务费）。 不足一天的按照一天计算。<br/>7. 违章处理完毕后，还需要做什么？<br/>承租方在违章处理完毕后，须将处罚决定书和缴费凭证给到门店。门店收到凭证后，一般会在7个工作日内审核完成，并同步更新违章状态。<br/>8. 承租人无法处理违章怎么办？<br/>如承租方无法自行处理、或无法在上述处理时限内处理违章，请及时与门店沟通解决方案。承租方须承担由此给门店带来的实际损失。门店有权从承租方支付的违章押金内、或通过免违章押金订单的授权扣款通道，扣除相应金额的费用。如押金扣除后仍不足以抵偿全部损失的，门店有权根据实际损失情况向承租方追偿。<br/>9. 注意事项<br/>承租人如需开具相关发票，建议自行前往交管部门处理违章事宜。门店仅可向承租方提供收取违章逾期处理违约金的收据和违章处理凭证。"}]}], "type": 54, "code": "2"}, {"title": "租车押金退还标准", "content": [], "contentObject": [{"stringObjs": [{"content": "租车押金退还：还车验车后，根据车况，若车辆无新车损，门店操作退还；退还金额到账时间以各支付渠道为准。 若您在还车验车时，和租车门店沟通确认在您租车期间出现新的损伤，门店将根据车辆损伤情况暂扣租车押金，经 定损后再做结算，多退少补。 车辆维修和订单金额将按以下标准执行： 1、在事故索赔所需材料不齐的情况下，您需垫付本车维修费用（即支付材料缺失保证金） 2、如有第三方损失，相关费用需由您先行垫付 3、发生事故后如需要垫付维修费、第三者费用，拖车费等费用，垫付前请您致电门店客服电话，门店客服会给出您 费用的合理性建议，及费用该由哪方来承担。由门店或保险公司承担的费用，为避免您的损失，请您在事故后 3 个 月内提出退款申请，逾期将不获受理 4、为避免保险公司拒赔或视您放弃理赔，请您务必在出险之日起 15 日内将保险理赔所需事故材料完整提供给我们 （特大人伤事故，或费用超 1 万元，可相应延缓 30 日）；感谢您的配合 5、保险公司理赔结束后，我们会将所得理赔款中由您垫付的部分返还给您（具体费用由保险公司核损，最终返还 给您的费用额度将按保险公司实际到位理赔款确定） 注：发生事故后，请勿擅自维修事故车辆，否则您将承担赔偿损失等违约责任，超过保险公司赔偿范围或保险公司 不足额理赔的部分，也将由您承担 若承租人通过验证免收押金，以上所列收费项将按照预订时签署的《代扣服务协议》中所列条款，在费用实际发生 时依据授权的扣款渠道进行扣款  "}]}], "type": 56, "code": "2"}], "sortNum": 4}, {"title": "其他收费标准", "type": -2, "code": "2", "subObject": [{"title": "道路救援", "content": [], "contentObject": [{"stringObjs": [{"content": "是否收取道路救援费：是"}]}, {"stringObjs": [{"content": ""}]}, {"stringObjs": [{"content": "<table>RoadRescue1"}]}], "type": 35, "code": "2"}, {"title": "夜间服务费", "content": [], "contentObject": [{"stringObjs": [{"content": "周一至周日"}]}, {"stringObjs": [{"content": "<table>nightServiceFeeTable_1"}]}, {"stringObjs": [{"content": "注：1）取车或还车各按1次计算；"}]}, {"stringObjs": [{"content": "      2）是否收取夜间服务费，以用户/承租人到门店或订单约定地点的时间为准，不计用户/承租人排队和等待时间。"}]}], "type": 50, "code": "2"}], "sortNum": 5}, {"title": "城市限行处理办法", "type": -7, "code": "2", "subObject": [], "sortNum": 7}, {"title": "事故指导说明", "type": -5, "code": "2", "subObject": [{"title": "事故操作流程指导说明", "content": [], "contentObject": [{"stringObjs": [{"content": "1.第一时间开启双闪,车后150米放置三脚架,撤离至安全区域保障自身人身安全后拍摄现场照片并联系门店车务主管.\n2.如实告知门店事故发生时间及事故现场情况,根据门店车务主管引导报案出险.\n3.未及时联系门店导致事故无法正常出险的,不享受任何增值保险待遇,因此一定要及时联系门店."}]}], "type": 55, "code": "2"}], "sortNum": 9999}, {"title": "其他特殊说明", "type": -6, "code": "2", "subObject": [], "sortNum": 9999}], "rentalMustReadTable": [{"tableId": "CarDamage1", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "修理项目"}, {"rowIndex": "1", "columnIndex": "2", "content": "单位"}, {"rowIndex": "1", "columnIndex": "3", "content": "10万以下车型(元)"}, {"rowIndex": "1", "columnIndex": "4", "content": "10-20万车型(元)"}, {"rowIndex": "1", "columnIndex": "5", "content": "20-30万车型(元)"}, {"rowIndex": "1", "columnIndex": "6", "content": "30万以上车型(元)"}, {"rowIndex": "2", "columnIndex": "1", "content": "保险杠(前、后)"}, {"rowIndex": "2", "columnIndex": "2", "content": "支"}, {"rowIndex": "2", "columnIndex": "3", "content": "600"}, {"rowIndex": "2", "columnIndex": "4", "content": "800"}, {"rowIndex": "2", "columnIndex": "5", "content": "1000"}, {"rowIndex": "2", "columnIndex": "6", "content": "2500"}, {"rowIndex": "3", "columnIndex": "1", "content": "前叶子板"}, {"rowIndex": "3", "columnIndex": "2", "content": "块"}, {"rowIndex": "3", "columnIndex": "3", "content": "600"}, {"rowIndex": "3", "columnIndex": "4", "content": "800"}, {"rowIndex": "3", "columnIndex": "5", "content": "1000"}, {"rowIndex": "3", "columnIndex": "6", "content": "2500"}, {"rowIndex": "4", "columnIndex": "1", "content": "前机盖"}, {"rowIndex": "4", "columnIndex": "2", "content": "个"}, {"rowIndex": "4", "columnIndex": "3", "content": "1200"}, {"rowIndex": "4", "columnIndex": "4", "content": "1800"}, {"rowIndex": "4", "columnIndex": "5", "content": "2000"}, {"rowIndex": "4", "columnIndex": "6", "content": "4000"}, {"rowIndex": "5", "columnIndex": "1", "content": "前风挡立柱"}, {"rowIndex": "5", "columnIndex": "2", "content": "个"}, {"rowIndex": "5", "columnIndex": "3", "content": "500"}, {"rowIndex": "5", "columnIndex": "4", "content": "700"}, {"rowIndex": "5", "columnIndex": "5", "content": "900"}, {"rowIndex": "5", "columnIndex": "6", "content": "1500"}, {"rowIndex": "6", "columnIndex": "1", "content": "车门总成"}, {"rowIndex": "6", "columnIndex": "2", "content": "个"}, {"rowIndex": "6", "columnIndex": "3", "content": "650"}, {"rowIndex": "6", "columnIndex": "4", "content": "800"}, {"rowIndex": "6", "columnIndex": "5", "content": "1000"}, {"rowIndex": "6", "columnIndex": "6", "content": "2500"}, {"rowIndex": "7", "columnIndex": "1", "content": "反光镜总成"}, {"rowIndex": "7", "columnIndex": "2", "content": "个"}, {"rowIndex": "7", "columnIndex": "3", "content": "350"}, {"rowIndex": "7", "columnIndex": "4", "content": "400"}, {"rowIndex": "7", "columnIndex": "5", "content": "500"}, {"rowIndex": "7", "columnIndex": "6", "content": "1100"}, {"rowIndex": "8", "columnIndex": "1", "content": "中门立柱"}, {"rowIndex": "8", "columnIndex": "2", "content": "个"}, {"rowIndex": "8", "columnIndex": "3", "content": "500"}, {"rowIndex": "8", "columnIndex": "4", "content": "700"}, {"rowIndex": "8", "columnIndex": "5", "content": "900"}, {"rowIndex": "8", "columnIndex": "6", "content": "1500"}, {"rowIndex": "9", "columnIndex": "1", "content": "车顶"}, {"rowIndex": "9", "columnIndex": "2", "content": "个"}, {"rowIndex": "9", "columnIndex": "3", "content": "1500"}, {"rowIndex": "9", "columnIndex": "4", "content": "1800"}, {"rowIndex": "9", "columnIndex": "5", "content": "3000"}, {"rowIndex": "9", "columnIndex": "6", "content": "4000"}, {"rowIndex": "10", "columnIndex": "1", "content": "底大边、后围"}, {"rowIndex": "10", "columnIndex": "2", "content": "块"}, {"rowIndex": "10", "columnIndex": "3", "content": "600"}, {"rowIndex": "10", "columnIndex": "4", "content": "800"}, {"rowIndex": "10", "columnIndex": "5", "content": "1000"}, {"rowIndex": "10", "columnIndex": "6", "content": "1500"}, {"rowIndex": "11", "columnIndex": "1", "content": "后叶子板"}, {"rowIndex": "11", "columnIndex": "2", "content": "块"}, {"rowIndex": "11", "columnIndex": "3", "content": "600"}, {"rowIndex": "11", "columnIndex": "4", "content": "800"}, {"rowIndex": "11", "columnIndex": "5", "content": "1000"}, {"rowIndex": "11", "columnIndex": "6", "content": "3000"}, {"rowIndex": "12", "columnIndex": "1", "content": "后备箱盖"}, {"rowIndex": "12", "columnIndex": "2", "content": "个"}, {"rowIndex": "12", "columnIndex": "3", "content": "1000"}, {"rowIndex": "12", "columnIndex": "4", "content": "1800"}, {"rowIndex": "12", "columnIndex": "5", "content": "2000"}, {"rowIndex": "12", "columnIndex": "6", "content": "3000"}, {"rowIndex": "13", "columnIndex": "1", "content": "轮毂(灰色铝合金质地)"}, {"rowIndex": "13", "columnIndex": "2", "content": "个"}, {"rowIndex": "13", "columnIndex": "3", "content": "300"}, {"rowIndex": "13", "columnIndex": "4", "content": "500"}, {"rowIndex": "13", "columnIndex": "5", "content": "700"}, {"rowIndex": "13", "columnIndex": "6", "content": "1000"}]}, {"tableId": "CarDamage3", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "修理项目"}, {"rowIndex": "1", "columnIndex": "2", "content": "单位"}, {"rowIndex": "1", "columnIndex": "3", "content": "10万以下车型(元)"}, {"rowIndex": "1", "columnIndex": "4", "content": "10-20万车型(元)"}, {"rowIndex": "1", "columnIndex": "5", "content": "20-30万车型(元)"}, {"rowIndex": "1", "columnIndex": "6", "content": "30万以上车型(元)"}, {"rowIndex": "2", "columnIndex": "1", "content": "保险杠(前、后)"}, {"rowIndex": "2", "columnIndex": "2", "content": "支"}, {"rowIndex": "2", "columnIndex": "3", "content": "12000"}, {"rowIndex": "2", "columnIndex": "4", "content": "2000"}, {"rowIndex": "2", "columnIndex": "5", "content": "2550"}, {"rowIndex": "2", "columnIndex": "6", "content": "5000"}, {"rowIndex": "3", "columnIndex": "1", "content": "前叶子板"}, {"rowIndex": "3", "columnIndex": "2", "content": "块"}, {"rowIndex": "3", "columnIndex": "3", "content": "1200"}, {"rowIndex": "3", "columnIndex": "4", "content": "1800"}, {"rowIndex": "3", "columnIndex": "5", "content": "2000"}, {"rowIndex": "3", "columnIndex": "6", "content": "5000"}, {"rowIndex": "4", "columnIndex": "1", "content": "前机盖"}, {"rowIndex": "4", "columnIndex": "2", "content": "个"}, {"rowIndex": "4", "columnIndex": "3", "content": "2000"}, {"rowIndex": "4", "columnIndex": "4", "content": "2800"}, {"rowIndex": "4", "columnIndex": "5", "content": "3500"}, {"rowIndex": "4", "columnIndex": "6", "content": "6500"}, {"rowIndex": "5", "columnIndex": "1", "content": "前风挡立柱"}, {"rowIndex": "5", "columnIndex": "2", "content": "个"}, {"rowIndex": "5", "columnIndex": "3", "content": "800"}, {"rowIndex": "5", "columnIndex": "4", "content": "1500"}, {"rowIndex": "5", "columnIndex": "5", "content": "2000"}, {"rowIndex": "5", "columnIndex": "6", "content": "4000"}, {"rowIndex": "6", "columnIndex": "1", "content": "车门总成"}, {"rowIndex": "6", "columnIndex": "2", "content": "个"}, {"rowIndex": "6", "columnIndex": "3", "content": "2000"}, {"rowIndex": "6", "columnIndex": "4", "content": "3000"}, {"rowIndex": "6", "columnIndex": "5", "content": "4000"}, {"rowIndex": "6", "columnIndex": "6", "content": "5000"}, {"rowIndex": "7", "columnIndex": "1", "content": "反光镜镜片"}, {"rowIndex": "7", "columnIndex": "2", "content": "个"}, {"rowIndex": "7", "columnIndex": "3", "content": "300"}, {"rowIndex": "7", "columnIndex": "4", "content": "400"}, {"rowIndex": "7", "columnIndex": "5", "content": "800"}, {"rowIndex": "7", "columnIndex": "6", "content": "1000"}, {"rowIndex": "8", "columnIndex": "1", "content": "反光镜总成"}, {"rowIndex": "8", "columnIndex": "2", "content": "个"}, {"rowIndex": "8", "columnIndex": "3", "content": "700"}, {"rowIndex": "8", "columnIndex": "4", "content": "800"}, {"rowIndex": "8", "columnIndex": "5", "content": "1500"}, {"rowIndex": "8", "columnIndex": "6", "content": "3600"}, {"rowIndex": "9", "columnIndex": "1", "content": "中门立柱"}, {"rowIndex": "9", "columnIndex": "2", "content": "个"}, {"rowIndex": "9", "columnIndex": "3", "content": "800"}, {"rowIndex": "9", "columnIndex": "4", "content": "1500"}, {"rowIndex": "9", "columnIndex": "5", "content": "2000"}, {"rowIndex": "9", "columnIndex": "6", "content": "4000"}, {"rowIndex": "10", "columnIndex": "1", "content": "车顶"}, {"rowIndex": "10", "columnIndex": "2", "content": "个"}, {"rowIndex": "10", "columnIndex": "3", "content": "2000"}, {"rowIndex": "10", "columnIndex": "4", "content": "3000"}, {"rowIndex": "10", "columnIndex": "5", "content": "8500"}, {"rowIndex": "10", "columnIndex": "6", "content": "10000"}, {"rowIndex": "11", "columnIndex": "1", "content": "底大边、后围"}, {"rowIndex": "11", "columnIndex": "2", "content": "块"}, {"rowIndex": "11", "columnIndex": "3", "content": "800"}, {"rowIndex": "11", "columnIndex": "4", "content": "1500"}, {"rowIndex": "11", "columnIndex": "5", "content": "2000"}, {"rowIndex": "11", "columnIndex": "6", "content": "4000"}, {"rowIndex": "12", "columnIndex": "1", "content": "后叶子板"}, {"rowIndex": "12", "columnIndex": "2", "content": "块"}, {"rowIndex": "12", "columnIndex": "3", "content": "1200"}, {"rowIndex": "12", "columnIndex": "4", "content": "2000"}, {"rowIndex": "12", "columnIndex": "5", "content": "3000"}, {"rowIndex": "12", "columnIndex": "6", "content": "5500"}, {"rowIndex": "13", "columnIndex": "1", "content": "后备箱盖"}, {"rowIndex": "13", "columnIndex": "2", "content": "个"}, {"rowIndex": "13", "columnIndex": "3", "content": "1500"}, {"rowIndex": "13", "columnIndex": "4", "content": "2000"}, {"rowIndex": "13", "columnIndex": "5", "content": "3500"}, {"rowIndex": "13", "columnIndex": "6", "content": "6000"}, {"rowIndex": "14", "columnIndex": "1", "content": "前大灯总成"}, {"rowIndex": "14", "columnIndex": "2", "content": "个"}, {"rowIndex": "14", "columnIndex": "3", "content": "2000"}, {"rowIndex": "14", "columnIndex": "4", "content": "3000"}, {"rowIndex": "14", "columnIndex": "5", "content": "10000"}, {"rowIndex": "14", "columnIndex": "6", "content": "10000"}, {"rowIndex": "15", "columnIndex": "1", "content": "尾灯总成"}, {"rowIndex": "15", "columnIndex": "2", "content": "个"}, {"rowIndex": "15", "columnIndex": "3", "content": "800"}, {"rowIndex": "15", "columnIndex": "4", "content": "2000"}, {"rowIndex": "15", "columnIndex": "5", "content": "4000"}, {"rowIndex": "15", "columnIndex": "6", "content": "9000"}, {"rowIndex": "16", "columnIndex": "1", "content": "侧转向灯总成"}, {"rowIndex": "16", "columnIndex": "2", "content": "个"}, {"rowIndex": "16", "columnIndex": "3", "content": "600"}, {"rowIndex": "16", "columnIndex": "4", "content": "700"}, {"rowIndex": "16", "columnIndex": "5", "content": "1000"}, {"rowIndex": "16", "columnIndex": "6", "content": "2000"}, {"rowIndex": "17", "columnIndex": "1", "content": "轮毂盖"}, {"rowIndex": "17", "columnIndex": "2", "content": "个"}, {"rowIndex": "17", "columnIndex": "3", "content": "200"}, {"rowIndex": "17", "columnIndex": "4", "content": "400"}, {"rowIndex": "17", "columnIndex": "5", "content": "600"}, {"rowIndex": "17", "columnIndex": "6", "content": "800"}, {"rowIndex": "18", "columnIndex": "1", "content": "轮毂(黑色铁质地)"}, {"rowIndex": "18", "columnIndex": "2", "content": "个"}, {"rowIndex": "18", "columnIndex": "3", "content": "500"}, {"rowIndex": "18", "columnIndex": "4", "content": "600"}, {"rowIndex": "18", "columnIndex": "5", "content": "/"}, {"rowIndex": "18", "columnIndex": "6", "content": "/"}, {"rowIndex": "19", "columnIndex": "1", "content": "轮毂(灰色铝合金质地)"}, {"rowIndex": "19", "columnIndex": "2", "content": "个"}, {"rowIndex": "19", "columnIndex": "3", "content": "800"}, {"rowIndex": "19", "columnIndex": "4", "content": "1500"}, {"rowIndex": "19", "columnIndex": "5", "content": "2000"}, {"rowIndex": "19", "columnIndex": "6", "content": "3500"}, {"rowIndex": "20", "columnIndex": "1", "content": "钥匙(机械)"}, {"rowIndex": "20", "columnIndex": "2", "content": "把"}, {"rowIndex": "20", "columnIndex": "3", "content": "500"}, {"rowIndex": "20", "columnIndex": "4", "content": "800"}, {"rowIndex": "20", "columnIndex": "5", "content": "2000"}, {"rowIndex": "20", "columnIndex": "6", "content": "3000"}, {"rowIndex": "21", "columnIndex": "1", "content": "雾灯总成"}, {"rowIndex": "21", "columnIndex": "2", "content": "个"}, {"rowIndex": "21", "columnIndex": "3", "content": "400"}, {"rowIndex": "21", "columnIndex": "4", "content": "500"}, {"rowIndex": "21", "columnIndex": "5", "content": "800"}, {"rowIndex": "21", "columnIndex": "6", "content": "1500"}, {"rowIndex": "22", "columnIndex": "1", "content": "轮胎"}, {"rowIndex": "22", "columnIndex": "2", "content": "条"}, {"rowIndex": "22", "columnIndex": "3", "content": "500"}, {"rowIndex": "22", "columnIndex": "4", "content": "800"}, {"rowIndex": "22", "columnIndex": "5", "content": "1000"}, {"rowIndex": "22", "columnIndex": "6", "content": "1500"}, {"rowIndex": "23", "columnIndex": "1", "content": "轮胎（漏气保用）"}, {"rowIndex": "23", "columnIndex": "2", "content": "条"}, {"rowIndex": "23", "columnIndex": "3", "content": "/"}, {"rowIndex": "23", "columnIndex": "4", "content": "/"}, {"rowIndex": "23", "columnIndex": "5", "content": "1500"}, {"rowIndex": "23", "columnIndex": "6", "content": "1600"}, {"rowIndex": "24", "columnIndex": "1", "content": "前档玻璃"}, {"rowIndex": "24", "columnIndex": "2", "content": "块"}, {"rowIndex": "24", "columnIndex": "3", "content": "1000"}, {"rowIndex": "24", "columnIndex": "4", "content": "1500"}, {"rowIndex": "24", "columnIndex": "5", "content": "2000"}, {"rowIndex": "24", "columnIndex": "6", "content": "3000"}]}, {"tableId": "BelongingsCost1", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "项目"}, {"rowIndex": "1", "columnIndex": "2", "content": "单位"}, {"rowIndex": "1", "columnIndex": "3", "content": "10万以下车型(元)"}, {"rowIndex": "1", "columnIndex": "4", "content": "10-20万车型(元)"}, {"rowIndex": "1", "columnIndex": "5", "content": "20-30万车型(元)"}, {"rowIndex": "1", "columnIndex": "6", "content": "30万以上车型(元)"}, {"rowIndex": "2", "columnIndex": "1", "content": "三角警示牌"}, {"rowIndex": "2", "columnIndex": "2", "content": "副"}, {"rowIndex": "2", "columnIndex": "3", "content": "60"}, {"rowIndex": "2", "columnIndex": "4", "content": "60"}, {"rowIndex": "2", "columnIndex": "5", "content": "60"}, {"rowIndex": "2", "columnIndex": "6", "content": "60"}, {"rowIndex": "3", "columnIndex": "1", "content": "千金顶"}, {"rowIndex": "3", "columnIndex": "2", "content": "个"}, {"rowIndex": "3", "columnIndex": "3", "content": "120"}, {"rowIndex": "3", "columnIndex": "4", "content": "180"}, {"rowIndex": "3", "columnIndex": "5", "content": "300"}, {"rowIndex": "3", "columnIndex": "6", "content": "300"}, {"rowIndex": "4", "columnIndex": "1", "content": "工具包"}, {"rowIndex": "4", "columnIndex": "2", "content": "个"}, {"rowIndex": "4", "columnIndex": "3", "content": "120"}, {"rowIndex": "4", "columnIndex": "4", "content": "240"}, {"rowIndex": "4", "columnIndex": "5", "content": "360"}, {"rowIndex": "4", "columnIndex": "6", "content": "240"}, {"rowIndex": "5", "columnIndex": "1", "content": "灭火器"}, {"rowIndex": "5", "columnIndex": "2", "content": "个"}, {"rowIndex": "5", "columnIndex": "3", "content": "100"}, {"rowIndex": "5", "columnIndex": "4", "content": "100"}, {"rowIndex": "5", "columnIndex": "5", "content": "100"}, {"rowIndex": "5", "columnIndex": "6", "content": "100"}, {"rowIndex": "6", "columnIndex": "1", "content": "音箱喇叭（扬声器）"}, {"rowIndex": "6", "columnIndex": "2", "content": "个"}, {"rowIndex": "6", "columnIndex": "3", "content": "300"}, {"rowIndex": "6", "columnIndex": "4", "content": "500"}, {"rowIndex": "6", "columnIndex": "5", "content": "1000"}, {"rowIndex": "6", "columnIndex": "6", "content": "2000"}, {"rowIndex": "7", "columnIndex": "1", "content": "遮阳板"}, {"rowIndex": "7", "columnIndex": "2", "content": "块"}, {"rowIndex": "7", "columnIndex": "3", "content": "120"}, {"rowIndex": "7", "columnIndex": "4", "content": "200"}, {"rowIndex": "7", "columnIndex": "5", "content": "400"}, {"rowIndex": "7", "columnIndex": "6", "content": "500"}, {"rowIndex": "8", "columnIndex": "1", "content": "车内后视镜"}, {"rowIndex": "8", "columnIndex": "2", "content": "个"}, {"rowIndex": "8", "columnIndex": "3", "content": "150"}, {"rowIndex": "8", "columnIndex": "4", "content": "500"}, {"rowIndex": "8", "columnIndex": "5", "content": "1000"}, {"rowIndex": "8", "columnIndex": "6", "content": "2000"}, {"rowIndex": "9", "columnIndex": "1", "content": "雨刷片"}, {"rowIndex": "9", "columnIndex": "2", "content": "套"}, {"rowIndex": "9", "columnIndex": "3", "content": "120"}, {"rowIndex": "9", "columnIndex": "4", "content": "160"}, {"rowIndex": "9", "columnIndex": "5", "content": "240"}, {"rowIndex": "9", "columnIndex": "6", "content": "240"}, {"rowIndex": "10", "columnIndex": "1", "content": "车辆钥匙（需换全车锁）"}, {"rowIndex": "10", "columnIndex": "2", "content": "把"}, {"rowIndex": "10", "columnIndex": "3", "content": "1200"}, {"rowIndex": "10", "columnIndex": "4", "content": "2300"}, {"rowIndex": "10", "columnIndex": "5", "content": "3000"}, {"rowIndex": "10", "columnIndex": "6", "content": "5000"}, {"rowIndex": "11", "columnIndex": "1", "content": "行驶证（含登报费）"}, {"rowIndex": "11", "columnIndex": "2", "content": "套"}, {"rowIndex": "11", "columnIndex": "3", "content": "400"}, {"rowIndex": "11", "columnIndex": "4", "content": "400"}, {"rowIndex": "11", "columnIndex": "5", "content": "400"}, {"rowIndex": "11", "columnIndex": "6", "content": "400"}, {"rowIndex": "12", "columnIndex": "1", "content": "标贴（环保，年检，交强）"}, {"rowIndex": "12", "columnIndex": "2", "content": "个"}, {"rowIndex": "12", "columnIndex": "3", "content": "200"}, {"rowIndex": "12", "columnIndex": "4", "content": "200"}, {"rowIndex": "12", "columnIndex": "5", "content": "200"}, {"rowIndex": "12", "columnIndex": "6", "content": "200"}, {"rowIndex": "13", "columnIndex": "1", "content": "附加税证/标（含登报费）"}, {"rowIndex": "13", "columnIndex": "2", "content": "个"}, {"rowIndex": "13", "columnIndex": "3", "content": "200"}, {"rowIndex": "13", "columnIndex": "4", "content": "200"}, {"rowIndex": "13", "columnIndex": "5", "content": "200"}, {"rowIndex": "13", "columnIndex": "6", "content": "200"}, {"rowIndex": "14", "columnIndex": "1", "content": "车辆牌照（含登报费）"}, {"rowIndex": "14", "columnIndex": "2", "content": "副*2"}, {"rowIndex": "14", "columnIndex": "3", "content": "500"}, {"rowIndex": "14", "columnIndex": "4", "content": "500"}, {"rowIndex": "14", "columnIndex": "5", "content": "500"}, {"rowIndex": "14", "columnIndex": "6", "content": "500"}]}, {"tableId": "RoadRescue1", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "救援项目"}, {"rowIndex": "1", "columnIndex": "2", "content": "费用收取"}, {"rowIndex": "2", "columnIndex": "1", "content": "更换轮胎"}, {"rowIndex": "2", "columnIndex": "2", "content": "根据当地市场价格和修理厂报价而定"}, {"rowIndex": "3", "columnIndex": "1", "content": "拖车牵引"}, {"rowIndex": "3", "columnIndex": "2", "content": "根据当地市场价格和修理厂报价而定"}, {"rowIndex": "4", "columnIndex": "1", "content": "紧急送油"}, {"rowIndex": "4", "columnIndex": "2", "content": "离门店 50 公里内，收取 200 元/次；超出部分与门店另议（油费另算）"}, {"rowIndex": "5", "columnIndex": "1", "content": "现场抢修"}, {"rowIndex": "5", "columnIndex": "2", "content": "离门店 50 公里内，收取 500 元/次；超出部分与门店另议"}, {"rowIndex": "6", "columnIndex": "1", "content": "搭电服务"}, {"rowIndex": "6", "columnIndex": "2", "content": "离门店 50 公里内，收取 200 元/次；超出部分与门店另议"}, {"rowIndex": "7", "columnIndex": "1", "content": "吊装救援"}, {"rowIndex": "7", "columnIndex": "2", "content": "根据当地市场价格和修理厂报价而定"}, {"rowIndex": "8", "columnIndex": "1", "content": "紧急加水"}, {"rowIndex": "8", "columnIndex": "2", "content": "离门店 50 公里内，收取 200 元/次；超出部分与门店另议"}, {"rowIndex": "9", "columnIndex": "1", "content": "送备用钥匙"}, {"rowIndex": "9", "columnIndex": "2", "content": "离门店 30 公里内，收取 200 元/次；超出部分与门店另议"}]}, {"tableId": "hourlyRatePolicyTable", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "零散小时"}, {"rowIndex": "1", "columnIndex": "2", "content": "收取当日租金比例"}, {"rowIndex": "2", "columnIndex": "1", "content": "0-1小时（含）"}, {"rowIndex": "2", "columnIndex": "2", "content": "35%"}, {"rowIndex": "3", "columnIndex": "1", "content": "1-2小时（含）"}, {"rowIndex": "3", "columnIndex": "2", "content": "45%"}, {"rowIndex": "4", "columnIndex": "1", "content": "2-3小时（含）"}, {"rowIndex": "4", "columnIndex": "2", "content": "55%"}, {"rowIndex": "5", "columnIndex": "1", "content": "3-4小时（含）"}, {"rowIndex": "5", "columnIndex": "2", "content": "65%"}, {"rowIndex": "6", "columnIndex": "1", "content": "4-5小时（含）"}, {"rowIndex": "6", "columnIndex": "2", "content": "75%"}, {"rowIndex": "7", "columnIndex": "1", "content": "5-6小时（含）"}, {"rowIndex": "7", "columnIndex": "2", "content": "85%"}, {"rowIndex": "8", "columnIndex": "1", "content": "6-7小时（含）"}, {"rowIndex": "8", "columnIndex": "2", "content": "95%"}, {"rowIndex": "9", "columnIndex": "1", "content": "7小时以上"}, {"rowIndex": "9", "columnIndex": "2", "content": "100%"}]}, {"tableId": "additionServiceFeeTable", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "附加服务"}, {"rowIndex": "1", "columnIndex": "2", "content": "收费标准"}, {"rowIndex": "2", "columnIndex": "1", "content": "优享服务费,尊享服务费,儿童座椅"}, {"rowIndex": "2", "columnIndex": "2", "content": "按24h收取"}]}, {"tableId": "nightServiceFeeTable_1", "desc": "", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "收费类别"}, {"rowIndex": "1", "columnIndex": "2", "content": "夜间服务时段"}, {"rowIndex": "1", "columnIndex": "3", "content": "收费标准"}, {"rowIndex": "2", "columnIndex": "1", "content": "取车/还车"}, {"rowIndex": "2", "columnIndex": "2", "content": "21:01 - 21:30"}, {"rowIndex": "2", "columnIndex": "3", "content": "50元/次"}, {"rowIndex": "3", "columnIndex": "1", "content": "取车/还车"}, {"rowIndex": "3", "columnIndex": "2", "content": "21:31 - 22:00"}, {"rowIndex": "3", "columnIndex": "3", "content": "80元/次"}, {"rowIndex": "4", "columnIndex": "1", "content": "取车/还车"}, {"rowIndex": "4", "columnIndex": "2", "content": "22:01 - 23:00"}, {"rowIndex": "4", "columnIndex": "3", "content": "100元/次"}]}], "rentalMustReadPicture": [], "searchCreditCard": false}]}], "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "vendorInsuranceDesc": {"insurancelist": [{"title": "车损保障", "type": 1, "desclist": [{"title": "保额", "desclist": ["车辆实际价值", "车辆实际价值"]}, {"title": "保障范围", "desclist": ["由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废"]}, {"title": "保险公司及车行承担", "desclist": ["1500元以上的部分", "保司核定理赔金额"]}, {"title": "客户承担", "desclist": ["≤1500元（据实承担）", "保司核定车辆价值－理赔金额"]}]}, {"title": "玻璃单独破碎险", "type": 1, "desclist": [{"title": "保额", "desclist": ["玻璃损失"]}, {"title": "保障范围", "desclist": ["保障因自然灾害、意外事故，导致被保险机动车未发生其他部位的损失，仅有玻璃单独的直接损失。"]}, {"title": "保险公司及车行承担", "desclist": ["100%"]}, {"title": "客户承担", "desclist": ["0%"]}]}, {"title": "免折旧费", "type": 1, "desclist": [{"title": "保额", "desclist": ["5000元"]}, {"title": "保障范围", "desclist": ["保障因车损产生的车辆加速折旧费"]}, {"title": "保险公司及车行承担", "desclist": ["维修费不超过5000元时，承租车辆维修费总额产生的折旧费"]}, {"title": "客户承担", "desclist": ["维修费超过5000元时，承租车辆维修费总额产生的折旧费"]}]}, {"title": "三者保障", "type": 1, "desclist": [{"title": "保额", "desclist": ["100万元"]}, {"title": "保障范围", "desclist": []}, {"title": "保险公司及车行承担", "desclist": ["100万元"]}, {"title": "客户承担", "desclist": ["100万元以上的部分"]}]}], "exclusionDesc": {"title": "以下情况无法为您提供保障服务：", "desclist": ["发生事故时未及时通知租车公司或未申报保险", "无事故证明材料或无保险理赔材料，", "无证驾驶、酒驾、超速等其他保险公司不予理赔或责任免除的场景"]}, "vendorInsuranceTips": "上述车辆及第三方保障仅覆盖车行服务保障范围内的损失，服务保障范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求导致保障服务公司拒绝处理或无法提供保障的情况，您需承担全额损失。"}, "priceChangeInfo": {}, "idCardTypes": [{"idCardType": 2, "idCardName": "护照"}, {"idCardType": 7, "idCardName": "回乡证"}, {"idCardType": 8, "idCardName": "台胞证"}, {"idCardType": 1, "idCardName": "身份证"}], "depositInfo": {"depositType": 10, "depositTypeName": "信用租·押金双免", "carRentalDepositFee": 6000.0, "illegalDepositFee": 3000.0, "carRentalDeposit": {"title": {"contentStyle": "1", "stringObjs": [{"content": "租车押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥", "style": "1"}, {"content": "6000.00", "style": "1"}, {"content": "", "style": "2"}]}, {"contentStyle": "2", "stringObjs": [{"content": "选择“", "style": "1"}, {"content": "信用租", "style": "3"}, {"content": "”并授权成功后免收", "style": "1"}]}]}, "illegalDeposit": {"title": {"contentStyle": "1", "stringObjs": [{"content": "违章押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥", "style": "1"}, {"content": "3000.00", "style": "1"}, {"content": "", "style": "2"}]}, {"contentStyle": "2", "stringObjs": [{"content": "选择“", "style": "1"}, {"content": "信用租", "style": "3"}, {"content": "”并授权成功后免收", "style": "1"}]}]}, "depositDesc": {"title": {"contentStyle": "1", "stringObjs": [{"content": "该车型支持免押服务", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "信用租·押金双免", "style": "1"}, {"content": "授权成功后，无需支付租车押金和违章押金", "style": "2"}]}], "note": {"contentStyle": "1", "stringObjs": [{"content": "在下一步我们会基于您的信用情况进行综合评估，若评估通过，您可在下一步选择信用租服务。若不通过，您可选择其他方式支付押金。", "style": "1"}]}}, "depositDescV2": {"title": {"contentStyle": "1", "stringObjs": [{"content": "押金双免", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "免¥6000租车押金", "style": "1"}]}, {"contentStyle": "1", "stringObjs": [{"content": "免¥3000违章押金", "style": "1"}]}], "note": {"contentStyle": "1", "stringObjs": [{"content": "下单时选择信用租并授权成功后免收", "style": "1"}]}}, "depositCase": {"title": "如何使用信用租", "type": 10, "subObject": [{"title": "进入下一步订单填写页，选择驾驶员", "url": "https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/CreditRentModalStep1.png"}, {"title": "选择驾驶员后，携程将根据订单金额、供应商支持的验证方式、与您在携程的综合信用（程信分）或芝麻信用进行综合评估，评估通过后即可尊享信用租·押金双免", "url": "https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/CreditRentModalStep22.png"}]}}, "trackInfo": {"vendorCode": "71523", "vendorPlatFrom": 10, "depositFreeType": 3, "depositType": 10, "riskOriginal": "0", "riskFinal": "0"}, "gsDesc": "", "extra": {"isPriceUnited": "1"}, "referenceTemp": {"pickWayInfo": 2, "returnWayInfo": 2, "isKarabi": 1, "klb": 1}, "imStatus": 1, "storeGuidInfos": [{"storeGuid": "店员收费上门送取车", "address": "地址：虹桥国际机场T1航站楼-国内出发", "type": 3}], "requestInfo": {"pickupDate": "2023-05-30 10:00:00", "pickupLocationName": "虹桥国际机场T1航站楼-国内出发", "returnDate": "2023-05-31 10:00:00", "returnLocationName": "虹桥国际机场T1航站楼-国内出发", "sourceCountryId": 1, "pLatitude": 31.194957, "rLatitude": 31.194957, "rLongitude": 121.347531, "pLongitude": 121.347531, "pDate": "20230530100000", "rDate": "20230531100000"}}