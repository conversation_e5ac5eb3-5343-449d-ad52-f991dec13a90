{"baseResponse": {"isSuccess": true, "code": "200", "extMap": {"carDamage": "357", "dropoffCityId": "43", "creditScore": "18", "queryRentalMustRead": "2416", "apiCost": "3900.0", "storeAndUserPic": "3", "productDetail": "529", "errorSource": "RF", "userAuth": "11", "start": "2023-02-03 14:58:30", "errorCode": "unknown", "pageName": "Product", "uid": "M00588838", "priceDetail": "3899", "allCost": "3937.0", "easyLifeServiceLabel": "201", "end": "2023-02-03 14:58:34", "dataConvertResCost": "37.0", "pickupCityId": "43"}}, "ResponseStatus": {"Timestamp": "/Date(1675407514239+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "8762467102705438040"}, {"Id": "RootMessageId", "Value": "921822-0a050d7d-465390-35337"}]}, "priceChange": false, "isSelected": true, "fType": false, "needDownGrade": false, "ctripSelected": [{"title": "优质车况", "desc": "三年内车龄（部分高级车型5年内）"}, {"title": "洗后交车", "desc": "一车一洗，干净卫生有保障"}, {"title": "油量保障", "desc": "取车保证70%以上油量"}, {"title": "不符即赔", "desc": "取车时，若以上服务未达标，每单最高赔偿300元"}], "pickupStoreInfo": {"storeCode": "106878", "bizVendorCode": "30147", "telephone": "86-15338990710", "storeName": "机场店", "address": "海南省三亚市天涯区凤凰镇鹿城大道三亚凤凰国际机场", "longitude": 109.409673, "latitude": 18.308173, "storeGuild": "自行前往门店取还车", "storeGuildSplit": "自行前往门店取车", "storeWay": "到店取还车", "workTime": {"openTimeDesc": "{\"\":\"24小时营业\"}", "openTime": "00:00", "closeTime": "23:59"}, "storeType": 2, "countryId": 1, "countryName": "中国", "provinceId": 31, "cityId": 43, "cityName": "三亚", "distance": "自行前往门店取还车，距门店直线1.8公里", "pickUpOnDoor": false, "freeShuttle": false, "wayInfo": 0, "showType": 1, "distanceDesc": "距凤凰国际机场直线1.8公里"}, "returnStoreInfo": {"storeCode": "106878", "bizVendorCode": "30147", "telephone": "86-15338990710", "storeName": "机场店", "address": "海南省三亚市天涯区凤凰镇鹿城大道三亚凤凰国际机场", "longitude": 109.409673, "latitude": 18.308173, "storeGuild": "自行前往门店取还车", "storeGuildSplit": "自行前往门店还车", "storeWay": "到店还车", "workTime": {"openTimeDesc": "{\"\":\"24小时营业\"}", "openTime": "00:00", "closeTime": "23:59"}, "storeType": 2, "countryId": 1, "countryName": "中国", "provinceId": 31, "cityId": 43, "cityName": "三亚", "distance": "自行前往门店取还车，距门店直线1.8公里", "returnOnDoor": false, "freeShuttle": false, "wayInfo": 0, "showType": 1}, "vendorInfo": {"bizVendorCode": "30147", "vendorName": "calabi-骑仕租车A", "vendorImageUrl": "//pic.c-ctrip.com/car_isd/vendorlogo/30147.jpg", "vendorCode": "30147", "isBroker": false, "platformCode": "", "platformName": "", "haveCoupon": false, "preAuthType": 0, "supportPreAuth": 0, "prePayType": 0, "supplierId": 1106232, "vendorFullName": "卡拉比骑士测试供应商", "vendorLicenseUrl": "https://dimg.fws.qa.nt.ctripcorp.com/images/0306o1200000dwi6oCD31.png?proc=watermark/t_6JCl5Lia5omn54Wn5L-h5oGv5YWs56S65LiT55So,s_70,l_center,r_315,d_40,c_696969,F732"}, "vehicleInfo": {"brandId": 98, "brandEName": "大众", "brandName": "大众", "name": "大众T-ROC探歌", "vehicleCode": "4515", "imageUrl": "0", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "displacement": "1.4T", "struct": "SUV", "fuel": "95号", "gearbox": "干式双离合变速箱(DCT)、湿式双离合变速箱(DCT)", "driveMode": "前置前驱、前置四驱", "imageList": ["//0"], "userRealImageCount": 0, "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0"], "license": "", "licenseStyle": "2", "sourcePicInfos": [{"source": 0, "picList": [{"imageUrl": "//0", "sortNum": 1}]}], "oilType": 3, "fuelType": "汽油"}, "flightDelayRule": {}, "commentInfo": {"level": "好", "commentCount": 0, "overallRating": "4.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0, "link": ""}, "packageInfos": [{}], "productDetails": [{"insPackageId": 0, "productInfoList": [{"priceInfoList": [{"currentCarPrice": 796, "currentDailyPrice": 398, "currentTotalPrice": 926, "localCarPrice": 796, "localDailyPrice": 398, "localTotalPrice": 926, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 926, "localOnewayfee": 0, "localPoaPrice": 0, "localPrepaidPrice": 926, "isContainOnewayFee": true, "payMode": 2, "vcExtendRequest": {"vendorVehicleId": "1860660"}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": false, "isFreeCancelNow": true, "cancelEncourage": ""}, "ageRestriction": {"licenceAge": 6}, "allTags": [{"title": "两年内车龄", "category": 2, "type": 1, "code": "6", "description": "行驶证注册年限小于两年。", "sortNum": 9, "colorCode": "8", "labelCode": "3547", "groupCode": "MarketGroup1201", "tagGroups": 1, "tagSortNum": 9, "groupId": 2, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "description": "无需支付租车押金和违章押金，取还车手续更便捷。", "sortNum": 10, "colorCode": "2", "subList": [{"description": "在下一步我们会基于您的信用情况进行综合评估，若评估通过，您可在下一步选择信用租服务。若不通过，您可选择其他方式支付押金。"}], "labelCode": "3746", "groupCode": "MarketGroup1347", "tagGroups": 2, "tagSortNum": 4, "groupId": 3, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "description": "车内配有倒车雷达。", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "tagGroups": 1, "tagSortNum": 15, "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "description": "车内配有行车记录仪。", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "tagGroups": 1, "tagSortNum": 20, "groupId": 2, "mergeId": 0}, {"title": "有损取消", "category": 1, "type": 2, "code": "1", "description": "该订单付款后取消，需付违约金。", "sortNum": 40, "colorCode": "4", "labelCode": "3683", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "确认后有损取消", "type": 2, "sortNum": 999, "colorCode": "4", "tagGroups": 2, "tagSortNum": 3}], "rentalGuarantee": [{"name": "基础服务", "quantity": 2, "localDailyPrice": 50, "localTotalPrice": 100, "localCurrencyCode": "CNY", "currentDailyPrice": 50, "currentTotalPrice": 100, "currentCurrencyCode": "CNY", "description": ["您无需承担车辆损失超过1500元的部分（含玻璃），轮胎及其他不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "<tag>tag0", "<tag>tag1"], "allTags": [{"title": "您需要承担车辆停运费", "type": 1, "code": "tag0", "colorCode": "BLACK"}, {"title": "您需要承担车辆折旧费", "type": 1, "code": "tag1", "colorCode": "BLACK"}], "longDescription": ["基础服务费已包含在费用明细内。车辆发生意外时，租车公司承担车辆损失中超过1500元部分的费用，以及协助处理事故需要呼救中心、门店服务等服务费用（不含玻璃、轮胎、停运及折旧费，上述具体费用标准, 以租车公司确认为准）"], "type": 1, "group": 0, "uniqueCode": "1002", "insuranceDetailDescription": {"items": [{"title": "服务详情", "contents": [], "head": "", "foot": "点击查看事故处理流程及理赔范围>", "pageTitle": "", "pageUrl": ""}, {"title": "车辆事故处理流程", "contents": [{"type": "3", "title": "立即联系门店及交通警察", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"type": "3", "title": "拍照并留存记录信息", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"type": "3", "title": "", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"type": "3", "title": "", "content": "请您配合交警处理完事故后，方可离开事故现场"}, {"type": "3", "title": "", "content": "准备材料，进行保险理赔流程"}], "head": "", "foot": "", "pageTitle": "查看更多事故指导及费用明细", "pageUrl": ""}], "foot": "{\"basic\":\"基础服务由车行提供\",\"advanced\":\"优享服务由车行提供\",\"easyLife\":\"无忧尊享服务由车行提供\"}"}}, {"name": "优享服务", "quantity": 2, "localDailyPrice": 60, "localTotalPrice": 120, "localCurrencyCode": "CNY", "currentDailyPrice": 60, "currentTotalPrice": 120, "currentCurrencyCode": "CNY", "description": ["您无需承担全部车辆损失（含玻璃），轮胎及其他不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "<tag>tag0", "<tag>tag1"], "allTags": [{"title": "您需要承担车辆停运费", "type": 1, "code": "tag0", "colorCode": "BLACK"}, {"title": "您需要承担车辆折旧费", "type": 1, "code": "tag1", "colorCode": "BLACK"}], "longDescription": ["基础服务费的补充保护。车辆发生意外时，您无需承担1500元以内的车辆损失费用，包括协助处理事故需要呼叫中心服务、门店服务等服务费用。"], "type": 0, "group": 1, "uniqueCode": "2001", "insuranceDetailDescription": {"items": [{"title": "服务详情", "contents": [], "head": "", "foot": "点击查看事故处理流程及理赔范围>", "pageTitle": "", "pageUrl": ""}, {"title": "车辆事故处理流程", "contents": [{"type": "3", "title": "立即联系门店及交通警察", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"type": "3", "title": "拍照并留存记录信息", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"type": "3", "title": "", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"type": "3", "title": "", "content": "请您配合交警处理完事故后，方可离开事故现场"}, {"type": "3", "title": "", "content": "准备材料，进行保险理赔流程"}], "head": "", "foot": "", "pageTitle": "查看更多事故指导及费用明细", "pageUrl": ""}], "foot": "{\"basic\":\"基础服务由车行提供\",\"advanced\":\"优享服务由车行提供\",\"easyLife\":\"无忧尊享服务由车行提供\"}"}}], "rentalGuaranteeV2": {"rentalGuaranteeTitle": ["车损保障", "三者保障", "免折旧费", "免停运费", "无需垫付"], "vendorServiceDesc": "车行服务是指车行为客人安全用车提供的服务，包含车辆整备（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。", "vendorServiceSubDesc": "1.以上服务与保障由车行基于车辆自身车险所提供，故车损与三者保障仅覆盖车行车险理赔范围内的损失，理赔范围见服务详情。\n2.发生事故后请严格按照理赔要求操作，若未按要求处理导致车险无法理赔，上述保障将会失效，您需承担全额损失。", "packageDetailList": [{"name": "基础服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 65, "gapPrice": 0, "description": [{"description": "1500元以内自付", "type": "CDWcn", "attr": "{\"carCoverage\":\"1500元\",\"vendorId\":30147,\"isBasic\":true,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "RED"}, {"contains": true, "description": "100万", "type": "TPLcn", "attr": "100万"}, {"contains": false, "type": "DPN", "attr": "{\"cover\":false,\"excess\":null,\"desc\":\"折旧费正常收取\",\"summary\":\"折旧费正常收取\"}"}, {"contains": false, "type": "OTG", "attr": "{\"cover\":false,\"excess\":null,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "allTags": [], "insuranceDetailDescription": [{"title": "车辆损失险", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "1500元以上部分"}}, {"key": "客户承担", "value": {"content": "1500元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "保险赔付的100%"}}, {"key": "客户承担", "value": {"content": "保险赔付以外的100%"}}]}], "type": "CDWcn"}, {"title": "第三者责任险", "contains": true, "content": ["保险车辆发生意外事故，导致第三者承受的损失。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "100万元"}}, {"key": "保险公司及车行承担", "value": {"great": false, "content": "100万"}}, {"key": "客户承担", "value": {"content": "100万以上部分"}}]}], "type": "TPLcn"}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：轮胎（含轮毂）损坏、底盘损坏；以及车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费、折旧费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "1002", "type": 1}, {"name": "优享服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 125, "gapPrice": 60, "cityEncourage": "建议您升级服务", "description": [{"description": "全额赔付 <tag>tag0 <tag>tag1", "type": "CDWcn", "attr": "{\"carCoverage\":\"0元\",\"vendorId\":30147,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":false}", "descriptionColorCode": "GREEN"}, {"contains": true, "description": "100万", "type": "TPLcn", "attr": "100万"}, {"contains": false, "type": "DPN", "attr": "{\"cover\":false,\"excess\":null,\"desc\":\"折旧费正常收取\",\"summary\":\"折旧费正常收取\"}"}, {"contains": false, "type": "OTG", "attr": "{\"cover\":false,\"excess\":null,\"desc\":\"停运费正常收取\",\"summary\":\"停运费正常收取\"}"}, {"contains": false, "type": "Disbursements", "attr": "含无需垫付"}], "allTags": [{"title": "含玻璃", "code": "tag0", "colorCode": "ORANGE"}, {"title": "、不含轮胎", "code": "tag1", "colorCode": "<PERSON>"}], "insuranceDetailDescription": [{"title": "车辆损失险", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失。", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}], "type": "CDWcn"}, {"title": "车上人员责任险（司机）", "contains": true, "content": ["车辆发生事故，致使驾驶员遭受人身伤亡或财产损失"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "10000元"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "100%"}}, {"key": "客户承担", "value": {"content": "0%"}}]}], "type": "PAIcn"}, {"title": "车上人员责任险（乘客）", "contains": true, "content": ["车辆发生事故，致使乘客遭受人身伤亡或财产损失。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "10000元/座"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "100%"}}, {"key": "客户承担", "value": {"content": "0%"}}]}], "type": "PAIcn"}, {"title": "车上人员责任险", "contains": true, "content": ["保险车辆发生保险事故，导致车上人员伤亡"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "10000元/座"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "100%"}}, {"key": "客户承担", "value": {"content": "0%"}}]}], "type": "PAIcn"}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：轮胎（含轮毂）损坏、底盘损坏；以及车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.因车损产生的停运费、折旧费。", "4.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "2001", "type": 0, "selectedEncourage": "经济实用 超值之选"}, {"name": "尊享服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 185, "gapPrice": 120, "description": [{"description": "全额赔付 <tag>tag0 ", "type": "CDWcn", "attr": "{\"carCoverage\":\"0元\",\"vendorId\":30147,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}", "descriptionColorCode": "GREEN"}, {"contains": true, "description": "100万", "type": "TPLcn", "attr": "100万"}, {"contains": true, "type": "DPN", "attr": "{\"cover\":true,\"excess\":\"3万\",\"desc\":\"车损3万元以下免收折旧费\",\"summary\":\"车损3万元以下免收折旧费\"}", "containsDescription": "（车损3万以下时）"}, {"contains": true, "type": "OTG", "attr": "{\"cover\":true,\"excess\":null,\"desc\":\"您无需承担车辆停运费\",\"summary\":\"免停运费\"}"}, {"contains": true, "type": "Disbursements", "attr": "含无需垫付"}], "allTags": [{"title": "含玻璃、轮胎", "code": "tag0", "colorCode": "ORANGE"}], "insuranceDetailDescription": [{"title": "车辆损失险", "contains": true, "content": ["发生车辆损失及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔。"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失。", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"great": true, "content": "全部损失"}}, {"key": "客户承担", "value": {"content": "0元"}}]}], "type": "CDWcn"}, {"title": "免停运费", "contains": true, "content": ["保障因车损产生的车辆停运费"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保险公司及车行承担", "value": {"great": true, "content": "100%"}}, {"key": "客户承担", "value": {"content": "0%"}}]}], "type": "OTG"}, {"title": "免折旧费", "contains": true, "content": ["保障因车损产生的车辆加速折旧费"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保险公司及车行承担", "value": {"great": false, "content": "维修费不超过30000元时，承租车辆维修费总额产生的折旧费"}}, {"key": "客户承担", "value": {"content": "维修费超过30000元时，承租车辆维修费总额产生的折旧费"}}]}], "type": "DPN"}, {"title": "无须垫付", "contains": true, "great": true, "content": ["无需垫付定金或相关费用，车辆维修出险后，根据费用单等，补缴给租车公司您所需赔付的部分。"]}, {"title": "不予理赔", "contains": false, "content": ["1.无事故证明无保险理赔材料。", "2.以下情况产生的损失由用户自行承担：车牌、车衣等非车辆本身的设备或物料的遗失与损坏。", "3.其他基本保障服务中保险公司无须承担，或责任免除的场景。"]}], "uniqueCode": "2011", "type": 0, "selectedEncourage": "全面保障 无忧之选"}], "purchasingNotice": {"title": "购买须知", "content": ["1.车行服务由租车公司提供，仅支持取车前购买", "2.若发生续租，已购买的车行服务的订单续租时必须购买相同服务"]}, "vendorServiceDetail": [{"title": "车辆事故处理流程", "type": 1, "description": "发生车损(本车与第三方,或本车单独发生事故)时请按下方流程处理，否则无法获得理赔。", "contents": [{"title": "报警并联系门店", "type": 1, "content": ["拨打122报警(上海地区拨打110),同时立即联系门店获取保险方面的咨询或建议。车辆需由保险公司定损后再修理。"]}, {"title": "拍照并留存记录信息", "type": 1, "content": ["包括不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况"]}, {"title": "明确双方责任，配合交警完成事故处理", "type": 1, "content": ["明确涉事方事故责任，或等待交通警察抵达现场处理。交警处理完成后方可离开现场"]}, {"title": "提交材料理赔", "type": 1, "content": ["根据门店给出的理赔建议准备材料,进行保险理赔"]}]}, {"title": "车行服务理赔说明", "type": 2, "description": "发生车损（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得理赔", "contents": []}]}, "priceCode": "7023269fa6274e7c9c46904e80f1ef9f"}], "equipments": [{"maxCount": 2, "equipmentName": "儿童座椅", "equipmentDesc": "通常适用于年龄1岁~6岁, 体重1千克~25千克的儿童\n", "localTotalPrice": 40, "localDailyPrice": 20, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "currentTotalPrice": 40, "currentDailyPrice": 20, "payMode": 0, "uniqueCode": "2003"}], "ctripInsurances": [], "zhiMaInfo": {"supportZhiMa": true}, "pickUpMaterials": [{"title": "驾驶员本人身份证原件", "summaryContent": ["有效期2个月以上"], "type": 0, "subObject": [{"title": "回乡证/台胞证/护照"}]}, {"title": "驾驶员本人驾照原件", "summaryContent": ["有效期2个月以上、驾龄需6个月以上"], "type": 1}, {"title": "支付宝/微信", "summaryContent": ["有效期0个月以上，用于到店刷取押金："], "type": 2, "subObject": [{"title": "驾驶员本人国内信用卡"}]}], "carRentalMustRead": [{"title": "取消政策", "type": 1, "code": "1"}, {"title": "里程限制", "content": ["租期内没有里程数限制"], "contentObject": [{"stringObjs": [{"content": "租期内没有里程数限制"}]}], "type": 3, "code": "1"}, {"title": "油费及加油服务费", "content": ["还车时需保持与取车时油量一致", "若还车油量少于取车油量，门店将收取油费。", "若还车油量多于取车油量，门店将返还多余油费。", "油费=格数差/总格数*油箱容量*油价"], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "还车时需保持与取车时油量一致"}]}, {"stringObjs": [{"content": "若还车油量少于取车油量，门店将收取油费。"}]}, {"stringObjs": [{"content": "若还车油量多于取车油量，门店将返还多余油费。"}]}, {"stringObjs": [{"content": "油费=格数差/总格数*油箱容量*油价"}]}], "type": 4, "code": "1"}, {"title": "禁行区域", "content": ["车辆不允许驶出海南", "*若违反上述禁行规则，门店可拒绝提供或强制收回车辆；强制收回车辆时，产生的相关费用由用户承担，剩余租期费用全部退还给用户。"], "contentObject": [{"stringObjs": [{"content": "车辆不允许驶出海南"}]}, {"stringObjs": [{"content": "*若违反上述禁行规则，门店可拒绝提供或强制收回车辆；强制收回车辆时，产生的相关费用由用户承担，剩余租期费用全部退还给用户。"}]}], "type": 26, "code": "1"}, {"title": "发票", "content": ["可前往租车门店开具"], "type": 9, "code": "1"}, {"title": "常规费用收费标准", "type": -1, "code": "2", "subObject": [{"title": "油费及加油服务费", "content": ["还车时需保持与取车时油量一致", "若还车油量少于取车油量，门店将收取油费。", "若还车油量多于取车油量，门店将返还多余油费。", "油费=格数差/总格数*油箱容量*油价"], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "还车时需保持与取车时油量一致"}]}, {"stringObjs": [{"content": "若还车油量少于取车油量，门店将收取油费。"}]}, {"stringObjs": [{"content": "若还车油量多于取车油量，门店将返还多余油费。"}]}, {"stringObjs": [{"content": "油费=格数差/总格数*油箱容量*油价"}]}], "type": 4, "code": "1"}, {"title": "零散小时费", "content": ["总租期内非整日的部分（零散小时）计费规则如下：", "<table>hourlyRatePolicyTable", "*如您选择购买附加服务，收费标准如下", "<table>additionServiceFeeTable"], "contentObject": [{"stringObjs": [{"content": "总租期内非整日的部分（零散小时）计费规则如下："}]}, {"stringObjs": [{"content": "<table>hourlyRatePolicyTable"}]}, {"stringObjs": [{"content": "*如您选择购买附加服务，收费标准如下"}]}, {"stringObjs": [{"content": "<table>additionServiceFeeTable"}]}], "type": 49, "code": "2"}, {"title": "提前还车", "content": ["1.请提前联系门店", "平日:支持提前还车，需要提前24小时联系门店", "节假日:不支持提前还车", "如果提前还车的实际租期总费用由于增加夜间服务费等原因导致大于订单原租期的总费用时，用户需要补足支付相应的差额后方能成功申请提前还车。", "提前还车不改变还车方式。如需修改还车方式，请在操作提前还车后与门店联系，能否修改以门店回复为准。所涉费用另行计算。", "2.退费标准", "平日:提前还车退费，退费违约金比例30%", "节假日:提前还车不退费", "退费金额 =（原租期订单总金额-新租期订单总金额）×（1-违约金比例）", "提前还车将收取实际租期总费用与订单中原租期总费用差额的固定比例作为违约金，剩余退还给用户。"], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "1.请提前联系门店"}]}, {"stringObjs": [{"content": "平日:支持提前还车，需要提前24小时联系门店"}]}, {"stringObjs": [{"content": "节假日:不支持提前还车"}]}, {"stringObjs": [{"content": "如果提前还车的实际租期总费用由于增加夜间服务费等原因导致大于订单原租期的总费用时，用户需要补足支付相应的差额后方能成功申请提前还车。"}]}, {"stringObjs": [{"content": "提前还车不改变还车方式。如需修改还车方式，请在操作提前还车后与门店联系，能否修改以门店回复为准。所涉费用另行计算。"}]}, {"contentStyle": "13", "stringObjs": [{"content": "2.退费标准"}]}, {"stringObjs": [{"content": "平日:提前还车退费，退费违约金比例30%"}]}, {"stringObjs": [{"content": "节假日:提前还车不退费"}]}, {"stringObjs": [{"content": "退费金额 =（原租期订单总金额-新租期订单总金额）×（1-违约金比例）"}]}, {"stringObjs": [{"content": "提前还车将收取实际租期总费用与订单中原租期总费用差额的固定比例作为违约金，剩余退还给用户。"}]}], "type": 10, "code": "2"}, {"title": "续租/延迟还车", "content": ["1.请提前联系门店", "平日：需提前24小时联系门店", "节假日：需提前48小时联系门店", "2.收费标准", "基础服务收费标准", "续租或延迟还车申请成功后，总租期等于“已租+续租”的总时间，租金根据总租期计算。", "若门店有零散小时收费规则，则总租期内非整日的部分，按零散小时收费政策计算。", "续租/延迟还车费用 = 总租期金额 - 原单价格", "附加服务收费标准", "优享/尊享服务", "如用户在原订单中已购买优享/尊享服务，续租或延迟还车如需延续该服务，将收取续租天数相应的优享/尊享服务费。（非整日部分遵循零散小时收费标准(如有)）。", "人身及财务险", "如用户在原订单中购买人身及财物险，续租或延迟还车时如需延续该保障，需另行加购；加购费用根据续租或延迟还车部分的租期计算，以天（计24小时）为单位收费。"], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "1.请提前联系门店"}]}, {"stringObjs": [{"content": "平日：需提前24小时联系门店"}]}, {"stringObjs": [{"content": "节假日：需提前48小时联系门店"}]}, {"contentStyle": "13", "stringObjs": [{"content": "2.收费标准"}]}, {"stringObjs": [{"content": "基础服务收费标准"}]}, {"stringObjs": [{"content": "续租或延迟还车申请成功后，总租期等于“已租+续租”的总时间，租金根据总租期计算。"}]}, {"stringObjs": [{"content": "若门店有零散小时收费规则，则总租期内非整日的部分，按零散小时收费政策计算。"}]}, {"stringObjs": [{"content": "续租/延迟还车费用 = 总租期金额 - 原单价格"}]}, {"stringObjs": [{"content": "附加服务收费标准"}]}, {"stringObjs": [{"content": "优享/尊享服务"}]}, {"stringObjs": [{"content": "如用户在原订单中已购买优享/尊享服务，续租或延迟还车如需延续该服务，将收取续租天数相应的优享/尊享服务费。（非整日部分遵循零散小时收费标准(如有)）。"}]}, {"stringObjs": [{"content": "人身及财务险"}]}, {"stringObjs": [{"content": "如用户在原订单中购买人身及财物险，续租或延迟还车时如需延续该保障，需另行加购；加购费用根据续租或延迟还车部分的租期计算，以天（计24小时）为单位收费。"}]}], "type": 12, "code": "2"}, {"title": "强行续租", "content": ["未按规定联系门店并办理续租手续，或未经门店同意而强行延迟还车的，将被视为强行续租。强行续租除必须支付正常续租价格外，还将产生强行续租违约金。", "强行续租费用 = 该车型强行续租期间总租金 × (300%)", "日租金不包含：车行手续费、附加服务费"], "contentObject": [{"stringObjs": [{"content": "未按规定联系门店并办理续租手续，或未经门店同意而强行延迟还车的，将被视为强行续租。强行续租除必须支付正常续租价格外，还将产生强行续租违约金。"}]}, {"stringObjs": [{"content": "强行续租费用 = 该车型强行续租期间总租金 × (300%)"}]}, {"stringObjs": [{"content": "日租金不包含：车行手续费、附加服务费"}]}], "type": 48, "code": "2"}], "sortNum": 1}, {"title": "租车及违章押金相关", "type": -3, "code": "2", "subObject": [], "sortNum": 4}, {"title": "其他收费标准", "type": -2, "code": "2", "subObject": [{"title": "夜间服务费", "content": ["周一至周日", "<table>nightServiceFeeTable_1", "注：1）取车或还车各按1次计算；", "      2）是否收取夜间服务费，以用户/承租人到门店或订单约定地点的时间为准，不计用户/承租人排队和等待时间。"], "contentObject": [{"stringObjs": [{"content": "周一至周日"}]}, {"stringObjs": [{"content": "<table>nightServiceFeeTable_1"}]}, {"stringObjs": [{"content": "注：1）取车或还车各按1次计算；"}]}, {"stringObjs": [{"content": "      2）是否收取夜间服务费，以用户/承租人到门店或订单约定地点的时间为准，不计用户/承租人排队和等待时间。"}]}], "type": 50, "code": "2"}], "sortNum": 5}, {"title": "事故指导说明", "type": -5, "code": "2", "subObject": [], "sortNum": 9999}, {"title": "其他特殊说明", "type": -6, "code": "2", "subObject": [], "sortNum": 9999}, {"title": "城市限行处理办法", "type": -7, "code": "2", "subObject": [], "sortNum": 9999}], "rentalMustReadTable": [{"tableId": "hourlyRatePolicyTable", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "零散小时"}, {"rowIndex": "1", "columnIndex": "2", "content": "收取当日租金比例"}, {"rowIndex": "2", "columnIndex": "1", "content": "0-1小时（含）"}, {"rowIndex": "2", "columnIndex": "2", "content": "20%"}, {"rowIndex": "3", "columnIndex": "1", "content": "1-2小时（含）"}, {"rowIndex": "3", "columnIndex": "2", "content": "25%"}, {"rowIndex": "4", "columnIndex": "1", "content": "2-3小时（含）"}, {"rowIndex": "4", "columnIndex": "2", "content": "30%"}, {"rowIndex": "5", "columnIndex": "1", "content": "3-4小时（含）"}, {"rowIndex": "5", "columnIndex": "2", "content": "45%"}, {"rowIndex": "6", "columnIndex": "1", "content": "4-5小时（含）"}, {"rowIndex": "6", "columnIndex": "2", "content": "70%"}, {"rowIndex": "7", "columnIndex": "1", "content": "5-6小时（含）"}, {"rowIndex": "7", "columnIndex": "2", "content": "80%"}, {"rowIndex": "8", "columnIndex": "1", "content": "6-7小时（含）"}, {"rowIndex": "8", "columnIndex": "2", "content": "85%"}, {"rowIndex": "9", "columnIndex": "1", "content": "7小时以上"}, {"rowIndex": "9", "columnIndex": "2", "content": "100%"}]}, {"tableId": "additionServiceFeeTable", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "附加服务"}, {"rowIndex": "1", "columnIndex": "2", "content": "收费标准"}, {"rowIndex": "2", "columnIndex": "1", "content": "优享服务费,尊享服务费,儿童座椅"}, {"rowIndex": "2", "columnIndex": "2", "content": "按24h收取"}]}, {"tableId": "nightServiceFeeTable_1", "desc": "", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "收费类别"}, {"rowIndex": "1", "columnIndex": "2", "content": "夜间服务时段"}, {"rowIndex": "1", "columnIndex": "3", "content": "收费标准"}, {"rowIndex": "2", "columnIndex": "1", "content": "取车/还车"}, {"rowIndex": "2", "columnIndex": "2", "content": "夜间21:30- 次日07:30"}, {"rowIndex": "2", "columnIndex": "3", "content": "50元/次"}]}], "rentalMustReadPicture": [], "searchCreditCard": false}]}], "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "vendorInsuranceDesc": {"insurancelist": [{"title": "车辆损失险", "type": 1, "desclist": [{"title": "保额", "desclist": ["车辆实际价值", "车辆实际价值"]}, {"title": "保障范围", "desclist": ["由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废"]}, {"title": "保险公司及车行承担", "desclist": ["1500元以上部分", "保险赔付的100%"]}, {"title": "客户承担", "desclist": ["1500元", "保险赔付以外的100%"]}]}, {"title": "第三者责任险", "type": 1, "desclist": [{"title": "保额", "desclist": ["100万元"]}, {"title": "保障范围", "desclist": ["保险车辆发生意外事故，导致第三者承受的损失。"]}, {"title": "保险公司及车行承担", "desclist": ["100万"]}, {"title": "客户承担", "desclist": ["100万以上部分"]}]}, {"title": "玻璃单独破碎险", "type": 1, "desclist": [{"title": "保额", "desclist": ["玻璃损失"]}, {"title": "保障范围", "desclist": ["保障因自然灾害、意外事故，导致被保险机动车未发生其他部位的损失，仅有玻璃单独的直接损失。"]}, {"title": "保险公司及车行承担", "desclist": ["100%"]}, {"title": "客户承担", "desclist": ["0%"]}]}], "exclusionDesc": {"title": "以下情况无法为您提供保障服务：", "desclist": ["发生事故时未及时通知租车公司或未申报保险", "无事故证明材料或无保险理赔材料，", "无证驾驶、酒驾、超速等其他保险公司不予理赔或责任免除的场景"]}, "vendorInsuranceTips": "上述车辆及第三方保障仅覆盖车行服务保障范围内的损失，服务保障范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求导致保障服务公司拒绝处理或无法提供保障的情况，您需承担全额损失。"}, "priceChangeInfo": {}, "idCardTypes": [{"idCardType": 1, "idCardName": "身份证"}, {"idCardType": 7, "idCardName": "回乡证"}, {"idCardType": 8, "idCardName": "台胞证"}, {"idCardType": 2, "idCardName": "护照"}], "depositInfo": {"depositType": 10, "depositTypeName": "信用租·押金双免", "carRentalDepositFee": 3000, "illegalDepositFee": 2000, "carRentalDeposit": {"title": {"contentStyle": "1", "stringObjs": [{"content": "租车押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥", "style": "1"}, {"content": "3000.00", "style": "1"}, {"content": "", "style": "2"}]}, {"contentStyle": "2", "stringObjs": [{"content": "选择“", "style": "1"}, {"content": "信用租", "style": "3"}, {"content": "”并授权成功后免收", "style": "1"}]}]}, "illegalDeposit": {"title": {"contentStyle": "1", "stringObjs": [{"content": "违章押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥", "style": "1"}, {"content": "2000.00", "style": "1"}, {"content": "", "style": "2"}]}, {"contentStyle": "2", "stringObjs": [{"content": "选择“", "style": "1"}, {"content": "信用租", "style": "3"}, {"content": "”并授权成功后免收", "style": "1"}]}]}, "depositDesc": {"title": {"contentStyle": "1", "stringObjs": [{"content": "该车型支持免押服务", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "信用租·押金双免", "style": "1"}, {"content": "授权成功后，无需支付租车押金和违章押金", "style": "2"}]}], "note": {"contentStyle": "1", "stringObjs": [{"content": "预定成功后，可在订单详情页申请免押；若免押授权不通过，您需要在门店线下支付押金。", "style": "1"}]}}, "depositDescV2": {"title": {"contentStyle": "1", "stringObjs": [{"content": "押金双免", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "免¥3000租车押金", "style": "1"}]}, {"contentStyle": "1", "stringObjs": [{"content": "免¥2000违章押金", "style": "1"}]}], "note": {"contentStyle": "1", "stringObjs": [{"content": "下单时选择信用租并授权成功后免收", "style": "1"}]}}, "depositCase": {"title": "如何使用信用租", "type": 10, "subObject": [{"title": "进入下一步订单填写页，选择驾驶员", "url": "https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/CreditRentModalStep1.png"}, {"title": "选择驾驶员后，携程将根据订单金额、供应商支持的验证方式、与您在携程的综合信用（程信分）或芝麻信用进行综合评估，评估通过后即可尊享信用租·押金双免", "url": "https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/CreditRentModalStep22.png"}]}, "depositPrompt": {"title": "该车型支持免押，选择信用租可享免押", "buttonExt": [{"title": "去免押", "type": 1, "tips": "98%用户选择"}, {"title": "无需免押，继续预订", "type": 2}], "items": [{"title": {"contentStyle": "1", "stringObjs": [{"content": "免租车押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥3000.00", "style": "1"}]}]}, {"title": {"contentStyle": "1", "stringObjs": [{"content": "免违章押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥2000.00", "style": "1"}]}]}]}}, "trackInfo": {"vendorCode": "30147", "vendorPlatFrom": 10, "depositFreeType": 3, "depositType": 10, "riskOriginal": "0", "riskFinal": "0"}, "gsDesc": "", "extra": {"isPriceUnited": "1"}, "referenceTemp": {"pickWayInfo": 0, "returnWayInfo": 0, "isKarabi": 1, "klb": 1}, "rentalMustReadTitle": {"title": "限制政策", "desc": "车辆不允许驶出海南若违反上述禁行规则，门店可拒绝提供或强制收回车辆；强制收回车辆时，产生的相关费用由用户承担，剩余租期费用全部退还给用户。"}, "imStatus": 1, "storeGuidInfos": [{"storeGuid": "自行前往门店取还车", "address": "地址：海南省三亚市天涯区凤凰镇鹿城大道三亚凤凰国际机场", "type": 3}]}