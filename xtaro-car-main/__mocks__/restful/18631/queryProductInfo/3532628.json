{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "3321559e-c0ed-4905-8fd3-7d3631f490d6", "cost": 2375, "extMap": {"carDamage": "152", "queryRentalMustRead": "897", "storeAndUserPic": "36", "productDetail": "395", "errorSource": "RF", "priceDetail": "2303", "easyLifeServiceLabel": "9", "errorCode": "0"}}, "ResponseStatus": {"Timestamp": "/Date(1718707080299+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "3778356660348231424"}, {"Id": "RootMessageId", "Value": "921822-0a06deab-477418-84286"}]}, "priceChange": false, "isSelected": true, "fType": false, "needDownGrade": false, "ctripSelected": [{"title": "优质车况", "desc": "三年内车龄（部分高级车型5年内）"}, {"title": "洗后交车", "desc": "一车一洗，干净卫生有保障"}, {"title": "油量保障", "desc": "取车保证70%以上油量"}, {"title": "不符即赔", "desc": "取车时，若以上服务未达标，每单最高赔偿300元"}], "pickupStoreInfo": {"storeCode": "106878", "bizVendorCode": "30147", "telephone": "86-13162770001", "storeName": "机场店343", "address": "三亚市天涯区三亚凤凰机场酒店1", "longitude": 109.410819, "latitude": 18.308337, "storeGuild": "店员免费接您至门店取还车", "storeGuildSplit": "店员免费接您至门店取车", "storeWay": "到店取还车", "workTime": {"openTimeDesc": "{\"\":\"06:30 - 22:59\"}", "openTime": "06:30", "closeTime": "22:59"}, "storeType": 1, "countryId": 1, "countryName": "中国", "provinceId": 31, "cityId": 43, "cityName": "三亚", "pickUpOnDoor": false, "freeShuttle": false, "wayInfo": 4, "showType": 1, "tags": []}, "returnStoreInfo": {"storeCode": "106878", "bizVendorCode": "30147", "telephone": "86-13162770001", "storeName": "机场店343", "address": "三亚市天涯区三亚凤凰机场酒店1", "longitude": 109.410819, "latitude": 18.308337, "storeGuild": "店员免费接您至门店取还车", "storeGuildSplit": "还车后店员免费送您至凤凰机场站", "storeWay": "到店还车", "workTime": {"openTimeDesc": "{\"\":\"06:30 - 22:59\"}", "openTime": "06:30", "closeTime": "22:59"}, "storeType": 1, "countryId": 1, "countryName": "中国", "provinceId": 31, "cityId": 43, "cityName": "三亚", "returnOnDoor": false, "freeShuttle": false, "wayInfo": 4, "showType": 1, "tags": []}, "vendorInfo": {"bizVendorCode": "30147", "vendorName": "骑仕租车", "vendorImageUrl": "https://dimg04.c-ctrip.com/images/0yc3w12000a6cep6u6C13.png", "vendorCode": "30147", "isBroker": false, "platformCode": "10", "platformName": "", "haveCoupon": false, "preAuthType": 0, "supportPreAuth": 0, "prePayType": 0, "supplierId": 1106232, "vendorFullName": "卡拉比骑士测试供应商", "vendorLicenseUrl": "https://dimg.fws.qa.nt.ctripcorp.com/images/030011200000kryty2EEC.jpg?proc=watermark/t_6JCl5Lia5omn54Wn5L-h5oGv5YWs56S65LiT55So,s_70,l_center,r_315,d_40,c_696969,F732"}, "vehicleInfo": {"brandId": 144, "brandEName": "奥迪", "brandName": "奥迪", "name": "奥迪A6L新能源", "vehicleCode": "4670", "imageUrl": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "groupCode": "5", "groupSubClassCode": "", "groupName": "豪华型", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "2.0T", "struct": "三厢车", "fuel": "95号", "gearbox": "手自一体变速箱(AT)或湿式双离合变速箱(DCT)", "driveMode": "前置前驱/前置四驱", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "oilType": 4, "fuelType": "插电式", "multimediaAlbums": [{"albumName": "门店实拍", "note": "", "albumType": 2, "mediaGroup": []}]}, "flightDelayRule": {}, "commentInfo": {"level": "超棒", "commentCount": 1611, "overallRating": "4.9", "maximumRating": 5, "commentLabel": "车辆干净整洁", "hasComment": 1, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=106878&calabiVehicleId=4670&vehicleName=奥迪A6L新能源&productCategoryId=35&isHideNavBar=YES&newAiTags=true"}, "packageInfos": [{"subType": 0, "easyLifeTag": [{"title": "一口价", "type": 0, "description": "含租车必须费用，无增项、隐性收费", "sortNum": 99, "subTitle": "*额外增加儿童座椅、车辆使用油电费、违章罚款等需自付"}, {"title": "免车损", "type": 0, "description": "车损全额保障、三者保障200万、免停运折旧费、车上人员保障5万元/座", "sortNum": 99, "subTitle": "*因客人肇事逃逸、酒/毒驾、无证驾驶等违法违规行为导致的车损，不在保障范围内"}, {"title": "随时退", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 99}, {"title": "免验车", "type": 0, "description": "无需验车，还车即走", "sortNum": 99}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满", "sortNum": 99, "subTitle": ""}, {"title": "免费道路救援", "type": 0, "description": "提供免费的全国道路救援服务", "sortNum": 99}, {"title": "免加油/充电服务费", "type": 0, "description": "还车时，若油量/电量少于取车油量/电量，无需支付加油/充电服务费", "sortNum": 99, "subTitle": "*油量/电量差价仍需支付"}]}], "productDetails": [{"insPackageId": 0, "productInfoList": [{"priceInfoList": [{"currentCarPrice": 60, "currentDailyPrice": 60, "currentTotalPrice": 60, "localCarPrice": 60, "localDailyPrice": 60, "localTotalPrice": 60, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 60, "localOnewayfee": 0, "localPoaPrice": 0, "localPrepaidPrice": 60, "isContainOnewayFee": true, "payMode": 2, "vcExtendRequest": {"vendorVehicleId": "5284924"}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": false, "isFreeCancelNow": true, "cancelEncourage": "取车前4小时可免费取消"}, "ageRestriction": {"youngDriverAge": 22, "oldDriverAge": 50, "licenceAge": 0}, "allTags": [{"title": "一年内车龄", "category": 2, "type": 1, "code": "6", "description": "行驶证注册年限小于1年。", "sortNum": 9, "colorCode": "8", "labelCode": "3510", "groupCode": "MarketGroup1201", "tagGroups": 1, "tagSortNum": 9, "groupId": 2, "mergeId": 0}, {"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "description": "车内配有倒车影像、倒车雷达。", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "tagGroups": 1, "tagSortNum": 14, "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "description": "车内配有行车记录仪。", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "tagGroups": 1, "tagSortNum": 20, "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "description": "支付完成-取车前4小时可免费取消；取车前4小时后取消取消违约金100%。", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "description": "保障取车时油量满", "sortNum": 42, "colorCode": "2", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "免加油/充电服务费", "category": 2, "type": 1, "code": "6", "description": "还车时，若油量/电量少于取车油量/电量，无需支付加油/充电服务费", "sortNum": 45, "colorCode": "2", "subList": [{"description": "*油量/电量差价仍需支付"}], "labelCode": "3828", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "支付完成至2024-06-26 06:00可免费取消", "type": 2, "sortNum": 999, "colorCode": "2", "tagGroups": 2, "tagSortNum": 3}], "rentalGuarantee": [{"name": "无忧租+车损全免保障", "quantity": 1, "localDailyPrice": 60, "localTotalPrice": 60, "localCurrencyCode": "CNY", "currentDailyPrice": 60, "currentTotalPrice": 60, "currentCurrencyCode": "CNY", "description": ["您无需承担车辆损失超过0元的部分（含玻璃、轮胎），不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失200万以内的部分", "<tag>tag0", "<tag>tag1"], "allTags": [{"title": "无需承担车辆停运费", "type": 0, "code": "tag0", "colorCode": "BLACK"}, {"title": "您无需承担车辆折旧费", "type": 0, "code": "tag1", "colorCode": "BLACK"}], "longDescription": ["含满油/电取还，送车上门，含车辆租金，车行手续费，取还费用，夜间服务费等各种服务费。"], "type": 1, "group": 0, "uniqueCode": "20000001", "insuranceDetailDescription": {"items": [{"title": "服务详情", "contents": [], "head": "", "foot": "点击查看事故处理流程及理赔范围>", "pageTitle": "", "pageUrl": ""}, {"title": "车辆事故处理流程", "contents": [{"type": "3", "title": "立即联系门店及交通警察", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"type": "3", "title": "拍照并留存记录信息", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"type": "3", "title": "", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"type": "3", "title": "", "content": "请您配合交警处理完事故后，方可离开事故现场"}, {"type": "3", "title": "", "content": "准备材料，进行保险理赔流程"}], "head": "", "foot": "", "pageTitle": "查看更多事故指导及费用明细", "pageUrl": ""}], "foot": "{\"basic\":\"基础服务由车行提供\",\"advanced\":\"优享服务由车行提供\",\"easyLife\":\"无忧尊享服务由车行提供\"}"}, "descriptionV2": ["您无需承担车辆损失超过0元的部分（含玻璃、轮胎），不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失200万以内的部分", "您无需承担车辆停运费", "您无需承担车辆折旧费"]}], "rentalGuaranteeV2": {"rentalGuaranteeTitle": ["车损保障", "三者保障", "免停运费", "免折旧费"], "vendorServiceDesc": "车行服务是指车行为客人安全用车提供的服务，包含车行手续（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。", "vendorServiceSubDesc": "上述保障仅覆盖非违法违规场景范围内的损失，详见服务详情。若发生事故，请按照要求进行处理。", "packageDetailList": [{"name": "车行保障服务", "currentCurrencyCode": "CNY", "currentDailyPrice": 60, "gapPrice": 0, "description": [{"description": "车损保障赔付全部损失，含玻璃、车轮", "type": "CDWcn", "attr": "{\"carCoverage\":\"0元\",\"vendorId\":30147,\"isBasic\":false,\"coverGlass\":true,\"coverTyre\":true}"}, {"contains": true, "description": "三者保障保额200万", "type": "TPLcn", "attr": "200万"}, {"contains": true, "description": "您无需承担车辆停运费", "type": "OTG", "attr": "{\"cover\":true,\"desc\":\"您无需承担车辆停运费\",\"summary\":\"免停运费\"}"}, {"contains": true, "description": "您无需承担车辆折旧费", "type": "DPN", "attr": "{\"cover\":true,\"desc\":\"您无需承担车辆折旧费\",\"summary\":\"免折旧费\"}"}, {"contains": true, "description": "车上人员保障5万元/座", "type": "PAIcn", "attr": "车上人员保障5万元/座"}, {"contains": true, "description": "免费道路救援", "type": "ABCcn", "attr": "免费道路救援"}], "allTags": [], "insuranceDetailDescription": [{"title": "车上人员保障", "contains": true, "content": ["保障车辆发生意外事故，导致乘客/司机人身伤亡。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "50000元/座"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "5万元/座"}}, {"key": "用户或承租方需承担", "value": {"content": "5万元/座以上的部分"}}]}], "type": "PAIcn"}, {"title": "车损保障", "contains": true, "content": ["发生车辆损失或车辆报废时，用户或承租方可通过赔付减损。若同一订单中发生多次车损，每次车损单独计算承担额"], "subContent": [{"title": "车辆损失", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": true, "content": "全部损失"}}, {"key": "用户或承租方需承担", "value": {"content": "0元"}}]}, {"title": "车辆报废", "desc": "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "车行及其保险公司承担", "value": {"great": true, "content": "全部损失"}}, {"key": "用户或承租方需承担", "value": {"content": "0元"}}]}], "type": "CDWcn"}, {"title": "三者保障", "contains": true, "content": ["保障车辆发生意外事故、导致第三者承受的损失。"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "200万元"}}, {"key": "车行及其保险公司承担", "value": {"great": false, "content": "200万元"}}, {"key": "用户或承租方需承担", "value": {"content": "200万元以上的部分"}}]}], "type": "TPLcn"}, {"title": "免折旧费", "contains": true, "great": true, "content": ["免收因车损产生的车辆折旧费"], "type": "DPN"}, {"title": "免停运费", "contains": true, "great": true, "content": ["免收因车损产生的车辆停运费"], "type": "OTG"}, {"title": "免费道路救援", "contains": true, "content": ["提供全国道路救援保障"], "type": "ABCcn"}, {"title": "不予理赔", "contains": false, "content": ["包括但不限于如下情况：肇事逃逸、酒/毒驾、无证驾驶等（此场景下一般客人存在明显法律过失，包括但不限于严重违法犯罪行为）时"]}], "uniqueCode": "20000001", "type": 1}], "vendorServiceDetail": [{"title": "车辆事故处理流程", "type": 1, "description": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "contents": [{"title": "1、报警并联系门店-卡拉比\n\n", "type": 1, "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留"]}, {"title": "2、还车时向车行提交事故材料", "type": 1, "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额和，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"]}]}, {"title": "保险理赔（如需）说明", "type": 2, "description": "发生车损（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得理赔", "contents": [{"title": "保险理赔说明", "type": 4, "content": ["1. 保险内容"]}, {"title": "赔付方式", "type": 3, "content": ["1. 保险基础数据配置-内容1", "2. 保险基础数据配置-内容2", "3. 保险基础数据配置-内容2"]}, {"title": "不予理赔说明", "type": 2, "content": ["1、发生事故时未及时通知租车公司或未申报保险", "2、无事故证明材料或无保险理赔材料", "3、无证驾驶、酒驾、超速等其他保险不予理赔或责任免除场景"]}]}], "claimSettlementVersion": ""}, "priceCode": "2a62285451a2411285a8f92deca3c706", "packageLevel": "PREP"}], "equipments": [{"maxCount": 2, "equipmentName": "儿童座椅", "equipmentDesc": "通常适用于年龄6个月~6岁, 体重1千克~25千克的儿童\n", "localTotalPrice": 5, "localDailyPrice": 5, "localCurrencyCode": "CNY", "currentCurrencyCode": "CNY", "currentTotalPrice": 5, "currentDailyPrice": 5, "payMode": 0, "uniqueCode": "2003"}], "ctripInsurances": [], "zhiMaInfo": {"supportZhiMa": false}, "pickUpMaterials": [{"title": "驾驶员本人身份证原件", "summaryContent": ["证件需在有效期内"], "type": 0, "subObject": [{"title": "护照/回乡证"}]}, {"title": "驾驶员本人驾照原件", "summaryContent": ["有效期1个月以上"], "type": 1}], "carRentalMustRead": [{"title": "取消政策", "type": 1, "code": "1"}, {"title": "里程限制", "contentObject": [{"stringObjs": [{"content": "租期内没有里程数限制"}]}], "type": 3, "code": "1"}, {"title": "能源费用及服务费", "content": [], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "还车时需保持与取车时能源量（油量/电量/燃料量）一致"}]}, {"stringObjs": [{"content": "若还车时能源量多于/少于发车能源量，涉及相关费用的结算标准，请以门店告知为准。"}]}], "type": 4, "code": "1"}, {"title": "禁行区域", "contentObject": [{"stringObjs": [{"content": "禁止驶入：海南省亚龙湾站；沙漠、戈壁、赛道、水渠、草原"}]}, {"stringObjs": [{"content": "禁止驶出：三亚市"}]}, {"contentStyle": "11", "stringObjs": [{"content": "*若违反上述禁行规则，门店可拒绝提供或强制收回车辆；强制收回车辆时，产生的相关费用由用户承担，剩余租期费用退还规则以门店为准。"}]}], "type": 26, "code": "1"}, {"title": "常规费用收费标准", "type": -1, "code": "2", "subObject": [{"title": "能源费用及服务费", "content": [], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "还车时需保持与取车时能源量（油量/电量/燃料量）一致"}]}, {"stringObjs": [{"content": "若还车时能源量多于/少于发车能源量，涉及相关费用的结算标准，请以门店告知为准。"}]}], "type": 4, "code": "1"}, {"title": "零散小时费", "content": [], "contentObject": [{"stringObjs": [{"content": "总租期不足24小时，计费规则如下："}]}, {"stringObjs": [{"content": "<table>hourlyRatePolicyTable"}]}, {"stringObjs": [{"content": "*如您选择购买附加服务，收费标准如下"}]}, {"stringObjs": [{"content": "<table>additionServiceFeeTable"}]}], "type": 49, "code": "2"}, {"title": "提前还车", "content": [], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "1.请提前联系门店"}]}, {"stringObjs": [{"content": "平日:支持提前还车，需要提前2小时在线操作或联系门店申请，与门店商议新的还车时间。提前还车是否申请成功以系统返回结果或与门店商议结果为准。"}]}, {"stringObjs": [{"content": "节假日:支持提前还车，需要提前2小时在线操作或联系门店申请，与门店商议新的还车时间。提前还车是否申请成功以系统返回结果或与门店商议结果为准。"}]}, {"stringObjs": [{"content": "如果提前还车的实际租期总费用由于增加夜间服务费等原因导致大于订单原租期的总费用时，用户需要补足支付相应的差额后方能成功申请提前还车。"}]}, {"stringObjs": [{"content": "提前还车不改变还车方式。如需修改还车方式，请在操作提前还车后与门店联系，能否修改以门店回复为准。所涉费用另行计算。"}]}, {"contentStyle": "13", "stringObjs": [{"content": "2.退费标准"}]}, {"stringObjs": [{"content": "平日:提前还车退费，违约金比例30%"}]}, {"stringObjs": [{"content": "节假日:提前还车退费，违约金比例70%"}]}, {"stringObjs": [{"content": "退费金额 =（原租期订单总金额-新租期订单总金额）×（1-违约金比例）"}]}, {"stringObjs": [{"content": "提前还车将收取实际租期总费用与订单中原租期总费用差额的固定比例作为违约金，剩余退还给用户。"}]}], "type": 10, "code": "2"}, {"title": "续租/延迟还车", "content": [], "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "1.请提前联系门店"}]}, {"stringObjs": [{"content": "平日：需提前2小时在线操作或联系门店申请，与门店商议新的还车时间。续租/延迟还车是否申请成功以系统返回结果或与门店商议结果为准。"}]}, {"stringObjs": [{"content": "节假日：需提前2小时在线操作或联系门店申请，与门店商议新的还车时间。续租/延迟还车是否申请成功以系统返回结果或与门店商议结果为准。"}]}, {"contentStyle": "13", "stringObjs": [{"content": "2.收费标准"}]}, {"stringObjs": [{"content": "基础服务收费标准"}]}, {"stringObjs": [{"content": "续租或延迟还车申请成功后，总租期等于“已租+续租”的总时间，租金根据总租期计算。"}]}, {"stringObjs": [{"content": "若门店有零散小时收费规则，则总租期内非整日的部分，按零散小时收费政策计算。"}]}, {"stringObjs": [{"content": "续租/延迟还车费用 = 总租期金额 - 原单价格"}]}], "type": 12, "code": "2"}, {"title": "强行续租", "content": [], "contentObject": [{"stringObjs": [{"content": "未按规定联系门店并办理续租手续，或未经门店同意而强行延迟还车的，将被视为强行续租。强行续租除必须支付正常续租价格外，还将产生强行续租违约金。"}]}, {"stringObjs": [{"content": "强行续租违约金= 该车型强行续租期间总租金 × (90%)"}]}, {"stringObjs": [{"content": "日租金不包含：车行手续费、附加服务费"}]}], "type": 48, "code": "2"}], "sortNum": 1}, {"title": "租车及违章押金相关", "type": -3, "code": "2", "subObject": [{"title": "违章处理违约金", "content": [], "contentObject": [{"stringObjs": [{"content": "1.须知<br/>承租方须对在车辆租赁期间产生的交通违章承担责任，并予以处理。违章信息以各地交管部门的记录为准；<br/>2.如何得知租期内是否产生违章？<br/>门店将在承租方还车后10个工作日左右查询或收到承租方未处理的违章信息（具体时间以各地方交管部门通知为准），并以电话、短信等方式通知承租方。<br/>3.还车后如无违章，违章押金将如何退还？<br/>还车后如查实车辆在租赁期间内无违章，违章押金预计在30天后由实际支付渠道原路退还，退还金额到账时间以各支付渠道为准；免违章押金订单预计在30天后解除已授权的扣款通道。<br/>4.承租人自行处理违章时限是多久？<br/>承租方须在拿到行驶证日（以物流签收时间计，不满一天按一天计）+行驶证免费借用时长起，7个工作日内，将违章事宜处理完毕。否则将产生100元/天 的违章逾期处理违约金。<br/>门店有权从承租方支付的违章押金内、或通过免违章押金订单的授权扣款通道，扣除上述逾期处理违约金。如押金扣除后仍不足以抵偿全部损失的，门店有权根据实际损失情况向承租方追偿。<br/>5.如何处理违章？<br/>根据交管部门要求，需携带本人驾驶证原件、身份证原件及车辆行驶证原件等，到违章当地或车牌所在地的交管部门办理；具体所需资料和违章处理相关规定各地存在差异（如部分地区要求提供租车合同，委托书等其他文件），部分城市开通了异地处理违章的政策，请事先咨询当地交管部门。<br/>部分情况下，车辆违章可以通过12123 APP或其他合法的第三方程序网上办理，具体情况请联系门店沟通。<br/>6. 如需借用行驶证等资料怎么办？<br/>承租方在自行处理违章时，如需借用行驶证原件，须提前9天联系门店预约。违章事宜处理完毕后，须及时将处理证件归还车行。<br/>在确认不耽误车辆行程的情况下，同城可免费借用6小时（门店营业时间范围内），异地可免费借用48小时（从门店将行驶证寄出至承租人将行驶证寄出）。用户须刷取500元押金预授权。在按时并完整归还证件时，门店将全额解除该预授权。若用车城市全面支持电子行驶证，则不限时免费。<br/>若实际使用车辆行驶证超过免费借用时间，则按照实际发生天数收取车辆租金。 不足一天的按照一天计算。<br/>7. 违章处理完毕后，还需要做什么？<br/>承租方在违章处理完毕后，须将处罚决定书和缴费凭证给到门店。门店收到凭证后，一般会在7个工作日内审核完成，并同步更新违章状态。<br/>8. 承租人无法处理违章怎么办？<br/>如承租方无法自行处理、或无法在上述处理时限内处理违章，请及时与门店沟通解决方案。承租方须承担由此给门店带来的实际损失。门店有权从承租方支付的违章押金内、或通过免违章押金订单的授权扣款通道，扣除相应金额的费用。如押金扣除后仍不足以抵偿全部损失的，门店有权根据实际损失情况向承租方追偿。<br/>9. 注意事项<br/>承租人如需开具相关发票，建议自行前往交管部门处理违章事宜。门店仅可向承租方提供收取违章逾期处理违约金的收据和违章处理凭证。"}]}], "type": 54, "code": "2"}, {"title": "车损外观损伤（修复或更换）费用标准", "content": [], "contentObject": [{"stringObjs": [{"content": "车辆外观损伤修复费用和车辆外观件更换费均需设置为一口价费用标准=维修/更换费用 +拆装费用 + 工时费用车辆外观损伤修复费用标准（满足下列任一条件就收费）1.漆面划伤触摸有凹槽且损伤长度在5cm以上2.漆面划伤触摸有凹槽且宽度0.5cm以上3.外观凹陷且面积4cm²以上4.外观隆起且面积4cm²以上车辆外观损伤修复费用标准："}]}, {"stringObjs": [{"content": ""}]}, {"stringObjs": [{"content": "<table>CarDamage1"}]}, {"stringObjs": [{"content": "车辆外观件更换费用标准（满足下列任一条件就收费）1.外观出现穿孔 2.外观出现开裂 3.外观件脱落 4.外观件遗失 车辆外观件更换费用标准："}]}, {"stringObjs": [{"content": ""}]}, {"stringObjs": [{"content": "<table>CarDamage3"}]}, {"stringObjs": [{"content": "1. 一项以上的外观或部件损伤，需叠加计算的总费用为车辆维修费。2. 如部件缺失或不可修复，除按上表支付修复费用外，还需按4S店价格另外支付备件费用。3. 钥匙仅在出现金属钥匙主体断裂时并且客户提供了完整的损坏钥匙的情况下，进行补配机械钥匙，如果钥匙丢失等情况，按照更换全车锁价格收取。"}]}], "type": 52, "code": "2"}, {"title": "随车物品损失", "content": [], "contentObject": [{"stringObjs": [{"content": "车内随车设备和物品不在保险公司赔付范围内，车辆租期如内有遗失或损坏，需要照价赔偿，\n请参照《随车物品清单及价格表》或当地 4S 店价格进行赔偿。（豪华进口车的随车物品按门\n店或 4s 店实际报价为准）\n随车物品清单及价格表:"}]}, {"stringObjs": [{"content": ""}]}, {"stringObjs": [{"content": "<table>BelongingsCost1"}]}, {"stringObjs": [{"content": "若用车中发生车辆钥匙丢失，租车公司需更换全车锁，请立即联系客服或门店，实际费用按 \n具体车型及市场价浮动。\n其他损失，按租车合同及相关法规协商处理。"}]}], "type": 53, "code": "2"}], "sortNum": 4}, {"title": "其他收费标准", "type": -2, "code": "2", "subObject": [{"title": "道路救援", "content": [], "contentObject": [{"stringObjs": [{"content": ""}]}, {"stringObjs": [{"content": ""}]}, {"stringObjs": [{"content": "<table>RoadRescue1"}]}], "type": 35, "code": "2"}, {"title": "夜间服务费", "content": [], "contentObject": [{"stringObjs": [{"content": "周一至周日"}]}, {"stringObjs": [{"content": "<table>nightServiceFeeTable_1"}]}, {"stringObjs": [{"content": "注：1）取车或还车各按1次计算；"}]}, {"stringObjs": [{"content": "      2）是否收取夜间服务费，以用户/承租人到门店或订单约定地点的时间为准，不计用户/承租人排队和等待时间。"}]}], "type": 50, "code": "2"}], "sortNum": 5}, {"title": "事故指导说明", "type": -5, "code": "2", "subObject": [], "sortNum": 9999}, {"title": "其他特殊说明", "type": -6, "code": "2", "subObject": [], "sortNum": 9999}, {"title": "城市限行处理办法", "type": -7, "code": "2", "subObject": [{"title": "城市限行供应商处理办法", "content": [], "contentObject": [{"stringObjs": [{"content": "1.门店所处的城市限行怎么办？\r\n门店会提前联系您，告知您限行相关政策并确认您的行程是否涉及限行。\r\n限行政策仅针对所属城市，如您需要前往周边城市，请关注当地的限行政策，避免违章。\r\n2.涉及限行时还需用车怎么办？门店优先为您提供不限行车辆，若您预订的车型无多余库存，门店将为您更换同等级车型或免费升级车型。若无可升级车辆库存，会尽量为您提供满足出行需求的降级车，并退还相应差价。若门店没有可调配车辆时，因限行产生的一切违章罚款及扣分均由门店承担。或联系携程客服协调改派，您无需支付取消违约金。3.不想用车了怎么办？\r\n若您因为限行政策需要取消订单时，您可与门店协商或联系携程客服协调免除取消费用。\r\n*由于限行场景的特殊性，具体情况以门店告知为准。"}]}], "type": 58, "code": "2"}], "sortNum": 9999}], "rentalMustReadTable": [{"tableId": "CarDamage1", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "修理项目"}, {"rowIndex": "1", "columnIndex": "2", "content": "单位"}, {"rowIndex": "1", "columnIndex": "3", "content": "10万以下车型(元)"}, {"rowIndex": "1", "columnIndex": "4", "content": "10-20万车型(元)"}, {"rowIndex": "1", "columnIndex": "5", "content": "20-30万车型(元)"}, {"rowIndex": "1", "columnIndex": "6", "content": "30万以上车型(元)"}, {"rowIndex": "2", "columnIndex": "1", "content": "保险杠(前、后)"}, {"rowIndex": "2", "columnIndex": "2", "content": "支"}, {"rowIndex": "2", "columnIndex": "3", "content": "600"}, {"rowIndex": "2", "columnIndex": "4", "content": "700"}, {"rowIndex": "2", "columnIndex": "5", "content": "800"}, {"rowIndex": "2", "columnIndex": "6", "content": "2500"}, {"rowIndex": "3", "columnIndex": "1", "content": "前叶子板"}, {"rowIndex": "3", "columnIndex": "2", "content": "块"}, {"rowIndex": "3", "columnIndex": "3", "content": "550"}, {"rowIndex": "3", "columnIndex": "4", "content": "650"}, {"rowIndex": "3", "columnIndex": "5", "content": "750"}, {"rowIndex": "3", "columnIndex": "6", "content": "2500"}, {"rowIndex": "4", "columnIndex": "1", "content": "前机盖"}, {"rowIndex": "4", "columnIndex": "2", "content": "个"}, {"rowIndex": "4", "columnIndex": "3", "content": "800"}, {"rowIndex": "4", "columnIndex": "4", "content": "900"}, {"rowIndex": "4", "columnIndex": "5", "content": "1000"}, {"rowIndex": "4", "columnIndex": "6", "content": "4000"}, {"rowIndex": "5", "columnIndex": "1", "content": "前风挡立柱"}, {"rowIndex": "5", "columnIndex": "2", "content": "个"}, {"rowIndex": "5", "columnIndex": "3", "content": "400"}, {"rowIndex": "5", "columnIndex": "4", "content": "500"}, {"rowIndex": "5", "columnIndex": "5", "content": "600"}, {"rowIndex": "5", "columnIndex": "6", "content": "1500"}, {"rowIndex": "6", "columnIndex": "1", "content": "车门总成"}, {"rowIndex": "6", "columnIndex": "2", "content": "个"}, {"rowIndex": "6", "columnIndex": "3", "content": "650"}, {"rowIndex": "6", "columnIndex": "4", "content": "800"}, {"rowIndex": "6", "columnIndex": "5", "content": "950"}, {"rowIndex": "6", "columnIndex": "6", "content": "2500"}, {"rowIndex": "7", "columnIndex": "1", "content": "反光镜总成"}, {"rowIndex": "7", "columnIndex": "2", "content": "个"}, {"rowIndex": "7", "columnIndex": "3", "content": "300"}, {"rowIndex": "7", "columnIndex": "4", "content": "350"}, {"rowIndex": "7", "columnIndex": "5", "content": "500"}, {"rowIndex": "7", "columnIndex": "6", "content": "1100"}, {"rowIndex": "8", "columnIndex": "1", "content": "中门立柱"}, {"rowIndex": "8", "columnIndex": "2", "content": "个"}, {"rowIndex": "8", "columnIndex": "3", "content": "400"}, {"rowIndex": "8", "columnIndex": "4", "content": "440"}, {"rowIndex": "8", "columnIndex": "5", "content": "600"}, {"rowIndex": "8", "columnIndex": "6", "content": "1500"}, {"rowIndex": "9", "columnIndex": "1", "content": "车顶"}, {"rowIndex": "9", "columnIndex": "2", "content": "个"}, {"rowIndex": "9", "columnIndex": "3", "content": "750"}, {"rowIndex": "9", "columnIndex": "4", "content": "900"}, {"rowIndex": "9", "columnIndex": "5", "content": "1100"}, {"rowIndex": "9", "columnIndex": "6", "content": "4000"}, {"rowIndex": "10", "columnIndex": "1", "content": "底大边、后围"}, {"rowIndex": "10", "columnIndex": "2", "content": "块"}, {"rowIndex": "10", "columnIndex": "3", "content": "480"}, {"rowIndex": "10", "columnIndex": "4", "content": "500"}, {"rowIndex": "10", "columnIndex": "5", "content": "600"}, {"rowIndex": "10", "columnIndex": "6", "content": "1500"}, {"rowIndex": "11", "columnIndex": "1", "content": "后叶子板"}, {"rowIndex": "11", "columnIndex": "2", "content": "块"}, {"rowIndex": "11", "columnIndex": "3", "content": "650"}, {"rowIndex": "11", "columnIndex": "4", "content": "800"}, {"rowIndex": "11", "columnIndex": "5", "content": "900"}, {"rowIndex": "11", "columnIndex": "6", "content": "3000"}, {"rowIndex": "12", "columnIndex": "1", "content": "后备箱盖"}, {"rowIndex": "12", "columnIndex": "2", "content": "个"}, {"rowIndex": "12", "columnIndex": "3", "content": "750"}, {"rowIndex": "12", "columnIndex": "4", "content": "850"}, {"rowIndex": "12", "columnIndex": "5", "content": "1000"}, {"rowIndex": "12", "columnIndex": "6", "content": "3000"}, {"rowIndex": "13", "columnIndex": "1", "content": "轮毂(灰色铝合金质地)"}, {"rowIndex": "13", "columnIndex": "2", "content": "个"}, {"rowIndex": "13", "columnIndex": "3", "content": "300"}, {"rowIndex": "13", "columnIndex": "4", "content": "400"}, {"rowIndex": "13", "columnIndex": "5", "content": "600"}, {"rowIndex": "13", "columnIndex": "6", "content": "800"}]}, {"tableId": "CarDamage3", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "修理项目"}, {"rowIndex": "1", "columnIndex": "2", "content": "单位"}, {"rowIndex": "1", "columnIndex": "3", "content": "10万以下车型(元)"}, {"rowIndex": "1", "columnIndex": "4", "content": "10-20万车型(元)"}, {"rowIndex": "1", "columnIndex": "5", "content": "20-30万车型(元)"}, {"rowIndex": "1", "columnIndex": "6", "content": "30万以上车型(元)"}, {"rowIndex": "2", "columnIndex": "1", "content": "保险杠(前、后)"}, {"rowIndex": "2", "columnIndex": "2", "content": "支"}, {"rowIndex": "2", "columnIndex": "3", "content": "900"}, {"rowIndex": "2", "columnIndex": "4", "content": "2000"}, {"rowIndex": "2", "columnIndex": "5", "content": "2550"}, {"rowIndex": "2", "columnIndex": "6", "content": "5000"}, {"rowIndex": "3", "columnIndex": "1", "content": "前叶子板"}, {"rowIndex": "3", "columnIndex": "2", "content": "块"}, {"rowIndex": "3", "columnIndex": "3", "content": "850"}, {"rowIndex": "3", "columnIndex": "4", "content": "1050"}, {"rowIndex": "3", "columnIndex": "5", "content": "1350"}, {"rowIndex": "3", "columnIndex": "6", "content": "5000"}, {"rowIndex": "4", "columnIndex": "1", "content": "前机盖"}, {"rowIndex": "4", "columnIndex": "2", "content": "个"}, {"rowIndex": "4", "columnIndex": "3", "content": "1100"}, {"rowIndex": "4", "columnIndex": "4", "content": "1360"}, {"rowIndex": "4", "columnIndex": "5", "content": "2400"}, {"rowIndex": "4", "columnIndex": "6", "content": "6500"}, {"rowIndex": "5", "columnIndex": "1", "content": "前风挡立柱"}, {"rowIndex": "5", "columnIndex": "2", "content": "个"}, {"rowIndex": "5", "columnIndex": "3", "content": "700"}, {"rowIndex": "5", "columnIndex": "4", "content": "900"}, {"rowIndex": "5", "columnIndex": "5", "content": "1200"}, {"rowIndex": "5", "columnIndex": "6", "content": "4000"}, {"rowIndex": "6", "columnIndex": "1", "content": "车门总成"}, {"rowIndex": "6", "columnIndex": "2", "content": "个"}, {"rowIndex": "6", "columnIndex": "3", "content": "950"}, {"rowIndex": "6", "columnIndex": "4", "content": "2200"}, {"rowIndex": "6", "columnIndex": "5", "content": "2800"}, {"rowIndex": "6", "columnIndex": "6", "content": "5000"}, {"rowIndex": "7", "columnIndex": "1", "content": "反光镜镜片"}, {"rowIndex": "7", "columnIndex": "2", "content": "个"}, {"rowIndex": "7", "columnIndex": "3", "content": "80"}, {"rowIndex": "7", "columnIndex": "4", "content": "150"}, {"rowIndex": "7", "columnIndex": "5", "content": "220"}, {"rowIndex": "7", "columnIndex": "6", "content": "700"}, {"rowIndex": "8", "columnIndex": "1", "content": "反光镜总成"}, {"rowIndex": "8", "columnIndex": "2", "content": "个"}, {"rowIndex": "8", "columnIndex": "3", "content": "600"}, {"rowIndex": "8", "columnIndex": "4", "content": "750"}, {"rowIndex": "8", "columnIndex": "5", "content": "1100"}, {"rowIndex": "8", "columnIndex": "6", "content": "3600"}, {"rowIndex": "9", "columnIndex": "1", "content": "中门立柱"}, {"rowIndex": "9", "columnIndex": "2", "content": "个"}, {"rowIndex": "9", "columnIndex": "3", "content": "700"}, {"rowIndex": "9", "columnIndex": "4", "content": "840"}, {"rowIndex": "9", "columnIndex": "5", "content": "1200"}, {"rowIndex": "9", "columnIndex": "6", "content": "4000"}, {"rowIndex": "10", "columnIndex": "1", "content": "车顶"}, {"rowIndex": "10", "columnIndex": "2", "content": "个"}, {"rowIndex": "10", "columnIndex": "3", "content": "1050"}, {"rowIndex": "10", "columnIndex": "4", "content": "1750"}, {"rowIndex": "10", "columnIndex": "5", "content": "8500"}, {"rowIndex": "10", "columnIndex": "6", "content": "10000"}, {"rowIndex": "11", "columnIndex": "1", "content": "底大边、后围"}, {"rowIndex": "11", "columnIndex": "2", "content": "块"}, {"rowIndex": "11", "columnIndex": "3", "content": "780"}, {"rowIndex": "11", "columnIndex": "4", "content": "1350"}, {"rowIndex": "11", "columnIndex": "5", "content": "1700"}, {"rowIndex": "11", "columnIndex": "6", "content": "4000"}, {"rowIndex": "12", "columnIndex": "1", "content": "后叶子板"}, {"rowIndex": "12", "columnIndex": "2", "content": "块"}, {"rowIndex": "12", "columnIndex": "3", "content": "950"}, {"rowIndex": "12", "columnIndex": "4", "content": "2000"}, {"rowIndex": "12", "columnIndex": "5", "content": "2100"}, {"rowIndex": "12", "columnIndex": "6", "content": "5500"}, {"rowIndex": "13", "columnIndex": "1", "content": "后备箱盖"}, {"rowIndex": "13", "columnIndex": "2", "content": "个"}, {"rowIndex": "13", "columnIndex": "3", "content": "1050"}, {"rowIndex": "13", "columnIndex": "4", "content": "1650"}, {"rowIndex": "13", "columnIndex": "5", "content": "2400"}, {"rowIndex": "13", "columnIndex": "6", "content": "6000"}, {"rowIndex": "14", "columnIndex": "1", "content": "前大灯总成"}, {"rowIndex": "14", "columnIndex": "2", "content": "个"}, {"rowIndex": "14", "columnIndex": "3", "content": "780"}, {"rowIndex": "14", "columnIndex": "4", "content": "2550"}, {"rowIndex": "14", "columnIndex": "5", "content": "4000"}, {"rowIndex": "14", "columnIndex": "6", "content": "9000"}, {"rowIndex": "15", "columnIndex": "1", "content": "尾灯总成"}, {"rowIndex": "15", "columnIndex": "2", "content": "个"}, {"rowIndex": "15", "columnIndex": "3", "content": "480"}, {"rowIndex": "15", "columnIndex": "4", "content": "1100"}, {"rowIndex": "15", "columnIndex": "5", "content": "3850"}, {"rowIndex": "15", "columnIndex": "6", "content": "9000"}, {"rowIndex": "16", "columnIndex": "1", "content": "侧转向灯总成"}, {"rowIndex": "16", "columnIndex": "2", "content": "个"}, {"rowIndex": "16", "columnIndex": "3", "content": "150"}, {"rowIndex": "16", "columnIndex": "4", "content": "150"}, {"rowIndex": "16", "columnIndex": "5", "content": "150"}, {"rowIndex": "16", "columnIndex": "6", "content": "500"}, {"rowIndex": "17", "columnIndex": "1", "content": "轮毂盖"}, {"rowIndex": "17", "columnIndex": "2", "content": "个"}, {"rowIndex": "17", "columnIndex": "3", "content": "70"}, {"rowIndex": "17", "columnIndex": "4", "content": "100"}, {"rowIndex": "17", "columnIndex": "5", "content": "150"}, {"rowIndex": "17", "columnIndex": "6", "content": "500"}, {"rowIndex": "18", "columnIndex": "1", "content": "轮毂(黑色铁质地)"}, {"rowIndex": "18", "columnIndex": "2", "content": "个"}, {"rowIndex": "18", "columnIndex": "3", "content": "180"}, {"rowIndex": "18", "columnIndex": "4", "content": "300"}, {"rowIndex": "18", "columnIndex": "5", "content": "/"}, {"rowIndex": "18", "columnIndex": "6", "content": "/"}, {"rowIndex": "19", "columnIndex": "1", "content": "轮毂(灰色铝合金质地)"}, {"rowIndex": "19", "columnIndex": "2", "content": "个"}, {"rowIndex": "19", "columnIndex": "3", "content": "800"}, {"rowIndex": "19", "columnIndex": "4", "content": "1100"}, {"rowIndex": "19", "columnIndex": "5", "content": "1600"}, {"rowIndex": "19", "columnIndex": "6", "content": "3500"}, {"rowIndex": "20", "columnIndex": "1", "content": "钥匙(机械)"}, {"rowIndex": "20", "columnIndex": "2", "content": "把"}, {"rowIndex": "20", "columnIndex": "3", "content": "300"}, {"rowIndex": "20", "columnIndex": "4", "content": "500"}, {"rowIndex": "20", "columnIndex": "5", "content": "800"}, {"rowIndex": "20", "columnIndex": "6", "content": "1500"}, {"rowIndex": "21", "columnIndex": "1", "content": "雾灯总成"}, {"rowIndex": "21", "columnIndex": "2", "content": "个"}, {"rowIndex": "21", "columnIndex": "3", "content": "200"}, {"rowIndex": "21", "columnIndex": "4", "content": "350"}, {"rowIndex": "21", "columnIndex": "5", "content": "500"}, {"rowIndex": "21", "columnIndex": "6", "content": "700"}, {"rowIndex": "22", "columnIndex": "1", "content": "轮胎"}, {"rowIndex": "22", "columnIndex": "2", "content": "条"}, {"rowIndex": "22", "columnIndex": "3", "content": "500"}, {"rowIndex": "22", "columnIndex": "4", "content": "800"}, {"rowIndex": "22", "columnIndex": "5", "content": "800"}, {"rowIndex": "22", "columnIndex": "6", "content": "900"}, {"rowIndex": "23", "columnIndex": "1", "content": "轮胎（漏气保用）"}, {"rowIndex": "23", "columnIndex": "2", "content": "条"}, {"rowIndex": "23", "columnIndex": "3", "content": "/"}, {"rowIndex": "23", "columnIndex": "4", "content": "/"}, {"rowIndex": "23", "columnIndex": "5", "content": "1500"}, {"rowIndex": "23", "columnIndex": "6", "content": "1600"}, {"rowIndex": "24", "columnIndex": "1", "content": "前档玻璃"}, {"rowIndex": "24", "columnIndex": "2", "content": "块"}, {"rowIndex": "24", "columnIndex": "3", "content": "800"}, {"rowIndex": "24", "columnIndex": "4", "content": "1000"}, {"rowIndex": "24", "columnIndex": "5", "content": "1500"}, {"rowIndex": "24", "columnIndex": "6", "content": "2000"}]}, {"tableId": "BelongingsCost1", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "项目"}, {"rowIndex": "1", "columnIndex": "2", "content": "单位"}, {"rowIndex": "1", "columnIndex": "3", "content": "10万以下车型(元)"}, {"rowIndex": "1", "columnIndex": "4", "content": "10-20万车型(元)"}, {"rowIndex": "1", "columnIndex": "5", "content": "20-30万车型(元)"}, {"rowIndex": "1", "columnIndex": "6", "content": "30万以上车型(元)"}, {"rowIndex": "2", "columnIndex": "1", "content": "三角警示牌"}, {"rowIndex": "2", "columnIndex": "2", "content": "副"}, {"rowIndex": "2", "columnIndex": "3", "content": "60"}, {"rowIndex": "2", "columnIndex": "4", "content": "60"}, {"rowIndex": "2", "columnIndex": "5", "content": "60"}, {"rowIndex": "2", "columnIndex": "6", "content": "60"}, {"rowIndex": "3", "columnIndex": "1", "content": "千金顶"}, {"rowIndex": "3", "columnIndex": "2", "content": "个"}, {"rowIndex": "3", "columnIndex": "3", "content": "120"}, {"rowIndex": "3", "columnIndex": "4", "content": "180"}, {"rowIndex": "3", "columnIndex": "5", "content": "300"}, {"rowIndex": "3", "columnIndex": "6", "content": "300"}, {"rowIndex": "4", "columnIndex": "1", "content": "工具包"}, {"rowIndex": "4", "columnIndex": "2", "content": "个"}, {"rowIndex": "4", "columnIndex": "3", "content": "120"}, {"rowIndex": "4", "columnIndex": "4", "content": "240"}, {"rowIndex": "4", "columnIndex": "5", "content": "360"}, {"rowIndex": "4", "columnIndex": "6", "content": "240"}, {"rowIndex": "5", "columnIndex": "1", "content": "灭火器"}, {"rowIndex": "5", "columnIndex": "2", "content": "个"}, {"rowIndex": "5", "columnIndex": "3", "content": "100"}, {"rowIndex": "5", "columnIndex": "4", "content": "100"}, {"rowIndex": "5", "columnIndex": "5", "content": "100"}, {"rowIndex": "5", "columnIndex": "6", "content": "100"}, {"rowIndex": "6", "columnIndex": "1", "content": "音箱喇叭（扬声器）"}, {"rowIndex": "6", "columnIndex": "2", "content": "个"}, {"rowIndex": "6", "columnIndex": "3", "content": "300"}, {"rowIndex": "6", "columnIndex": "4", "content": "500"}, {"rowIndex": "6", "columnIndex": "5", "content": "1000"}, {"rowIndex": "6", "columnIndex": "6", "content": "2000"}, {"rowIndex": "7", "columnIndex": "1", "content": "遮阳板"}, {"rowIndex": "7", "columnIndex": "2", "content": "块"}, {"rowIndex": "7", "columnIndex": "3", "content": "120"}, {"rowIndex": "7", "columnIndex": "4", "content": "200"}, {"rowIndex": "7", "columnIndex": "5", "content": "400"}, {"rowIndex": "7", "columnIndex": "6", "content": "500"}, {"rowIndex": "8", "columnIndex": "1", "content": "车内后视镜"}, {"rowIndex": "8", "columnIndex": "2", "content": "个"}, {"rowIndex": "8", "columnIndex": "3", "content": "150"}, {"rowIndex": "8", "columnIndex": "4", "content": "500"}, {"rowIndex": "8", "columnIndex": "5", "content": "1000"}, {"rowIndex": "8", "columnIndex": "6", "content": "2000"}, {"rowIndex": "9", "columnIndex": "1", "content": "雨刷片"}, {"rowIndex": "9", "columnIndex": "2", "content": "套"}, {"rowIndex": "9", "columnIndex": "3", "content": "120"}, {"rowIndex": "9", "columnIndex": "4", "content": "160"}, {"rowIndex": "9", "columnIndex": "5", "content": "240"}, {"rowIndex": "9", "columnIndex": "6", "content": "240"}, {"rowIndex": "10", "columnIndex": "1", "content": "车辆钥匙（需换全车锁）"}, {"rowIndex": "10", "columnIndex": "2", "content": "把"}, {"rowIndex": "10", "columnIndex": "3", "content": "1200"}, {"rowIndex": "10", "columnIndex": "4", "content": "2300"}, {"rowIndex": "10", "columnIndex": "5", "content": "3000"}, {"rowIndex": "10", "columnIndex": "6", "content": "5000"}, {"rowIndex": "11", "columnIndex": "1", "content": "行驶证（含登报费）"}, {"rowIndex": "11", "columnIndex": "2", "content": "套"}, {"rowIndex": "11", "columnIndex": "3", "content": "250"}, {"rowIndex": "11", "columnIndex": "4", "content": "250"}, {"rowIndex": "11", "columnIndex": "5", "content": "250"}, {"rowIndex": "11", "columnIndex": "6", "content": "250"}, {"rowIndex": "12", "columnIndex": "1", "content": "标贴（环保，年检，交强）"}, {"rowIndex": "12", "columnIndex": "2", "content": "个"}, {"rowIndex": "12", "columnIndex": "3", "content": "200"}, {"rowIndex": "12", "columnIndex": "4", "content": "200"}, {"rowIndex": "12", "columnIndex": "5", "content": "200"}, {"rowIndex": "12", "columnIndex": "6", "content": "200"}, {"rowIndex": "13", "columnIndex": "1", "content": "附加税证/标（含登报费）"}, {"rowIndex": "13", "columnIndex": "2", "content": "个"}, {"rowIndex": "13", "columnIndex": "3", "content": "200"}, {"rowIndex": "13", "columnIndex": "4", "content": "200"}, {"rowIndex": "13", "columnIndex": "5", "content": "200"}, {"rowIndex": "13", "columnIndex": "6", "content": "200"}, {"rowIndex": "14", "columnIndex": "1", "content": "车辆牌照（含登报费）"}, {"rowIndex": "14", "columnIndex": "2", "content": "副*2"}, {"rowIndex": "14", "columnIndex": "3", "content": "300"}, {"rowIndex": "14", "columnIndex": "4", "content": "300"}, {"rowIndex": "14", "columnIndex": "5", "content": "300"}, {"rowIndex": "14", "columnIndex": "6", "content": "300"}]}, {"tableId": "RoadRescue1", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "救援项目"}, {"rowIndex": "1", "columnIndex": "2", "content": "费用收取"}, {"rowIndex": "2", "columnIndex": "1", "content": "更换轮胎"}, {"rowIndex": "2", "columnIndex": "2", "content": "根据当地市场价格和修理厂报价而定"}, {"rowIndex": "3", "columnIndex": "1", "content": "拖车牵引"}, {"rowIndex": "3", "columnIndex": "2", "content": "根据当地市场价格和修理厂报价而定"}, {"rowIndex": "4", "columnIndex": "1", "content": "紧急送油"}, {"rowIndex": "4", "columnIndex": "2", "content": "离门店 50 公里内，收取 200 元/次；超出部分与门店另议（油费另算）"}, {"rowIndex": "5", "columnIndex": "1", "content": "现场抢修"}, {"rowIndex": "5", "columnIndex": "2", "content": "离门店 50 公里内，收取 500 元/次；超出部分与门店另议"}, {"rowIndex": "6", "columnIndex": "1", "content": "搭电服务"}, {"rowIndex": "6", "columnIndex": "2", "content": "离门店 50 公里内，收取 200 元/次；超出部分与门店另议"}, {"rowIndex": "7", "columnIndex": "1", "content": "吊装救援"}, {"rowIndex": "7", "columnIndex": "2", "content": "根据当地市场价格和修理厂报价而定"}, {"rowIndex": "8", "columnIndex": "1", "content": "紧急加水"}, {"rowIndex": "8", "columnIndex": "2", "content": "离门店 50 公里内，收取 200 元/次；超出部分与门店另议"}, {"rowIndex": "9", "columnIndex": "1", "content": "送备用钥匙"}, {"rowIndex": "9", "columnIndex": "2", "content": "离门店 50 公里内，收取 200 元/次；超出部分与门店另议"}]}, {"tableId": "hourlyRatePolicyTable", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "零散小时"}, {"rowIndex": "1", "columnIndex": "2", "content": "收取当日租金比例"}, {"rowIndex": "2", "columnIndex": "1", "content": "0小时以上"}, {"rowIndex": "2", "columnIndex": "2", "content": "100%"}]}, {"tableId": "additionServiceFeeTable", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "附加服务"}, {"rowIndex": "1", "columnIndex": "2", "content": "收费标准"}, {"rowIndex": "2", "columnIndex": "1", "content": "儿童座椅"}, {"rowIndex": "2", "columnIndex": "2", "content": "按24h收取"}]}, {"tableId": "nightServiceFeeTable_1", "desc": "", "items": [{"rowIndex": "1", "columnIndex": "1", "content": "收费类别"}, {"rowIndex": "1", "columnIndex": "2", "content": "夜间服务时段"}, {"rowIndex": "1", "columnIndex": "3", "content": "收费标准"}, {"rowIndex": "2", "columnIndex": "1", "content": "取车/还车"}, {"rowIndex": "2", "columnIndex": "2", "content": "06:30 - 08:30"}, {"rowIndex": "2", "columnIndex": "3", "content": "12元/次"}, {"rowIndex": "3", "columnIndex": "1", "content": "取车/还车"}, {"rowIndex": "3", "columnIndex": "2", "content": "20:00 - 22:59"}, {"rowIndex": "3", "columnIndex": "3", "content": "50元/次"}]}], "rentalMustReadPicture": [], "searchCreditCard": false, "addProductInfo": {}}]}], "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "vendorInsuranceDesc": {"insurancelist": [{"title": "玻璃单独破碎险", "type": 1, "desclist": [{"title": "保额", "desclist": ["玻璃损失"]}, {"title": "保障范围", "desclist": ["保障因自然灾害、意外事故，导致被保险机动车未发生其他部位的损失，仅有玻璃单独的直接损失。"]}, {"title": "车行及其保险公司承担", "desclist": ["100%"]}, {"title": "用户或承租方需承担", "desclist": ["0%"]}]}, {"title": "道路救援", "type": 1, "desclist": [{"title": "保额", "desclist": [""]}, {"title": "保障范围", "desclist": ["提供全国道路救援保障"]}, {"title": "车行及其保险公司承担", "desclist": ["提供全国道路救援保障"]}, {"title": "用户或承租方需承担", "desclist": ["提供全国道路救援保障"]}]}, {"title": "车上人员保障", "type": 1, "desclist": [{"title": "保额", "desclist": ["50000元/座"]}, {"title": "保障范围", "desclist": ["保障车辆发生意外事故，导致乘客/司机人身伤亡。"]}, {"title": "车行及其保险公司承担", "desclist": ["5万元/座"]}, {"title": "用户或承租方需承担", "desclist": ["5万元/座以上的部分"]}]}, {"title": "车轮单独损失险", "type": 1, "desclist": [{"title": "保额", "desclist": ["车轮损失"]}, {"title": "保障范围", "desclist": ["保障因自然灾害、意外事故，导致被保险机动车未发生其他部位的损失，仅有车轮（含轮胎、轮毂、轮毂罩）单独的直接损失。"]}, {"title": "车行及其保险公司承担", "desclist": ["100%"]}, {"title": "用户或承租方需承担", "desclist": ["0%"]}]}, {"title": "车损保障", "type": 1, "desclist": [{"title": "保额", "desclist": ["车辆实际价值", "车辆实际价值"]}, {"title": "保障范围", "desclist": ["由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失", "由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆报废"]}, {"title": "车行及其保险公司承担", "desclist": ["全部损失", "全部损失"]}, {"title": "用户或承租方需承担", "desclist": ["0元", "0元"]}]}, {"title": "三者保障", "type": 1, "desclist": [{"title": "保额", "desclist": ["200万元"]}, {"title": "保障范围", "desclist": ["保障车辆发生意外事故、导致第三者承受的损失。"]}, {"title": "车行及其保险公司承担", "desclist": ["200万元"]}, {"title": "用户或承租方需承担", "desclist": ["200万元以上的部分"]}]}], "exclusionDesc": {"title": "以下情况无法为您提供保障服务：", "desclist": ["发生事故时未及时通知租车公司或未申报保险", "无事故证明材料或无保险理赔材料，", "无证驾驶、酒驾、超速等其他保险公司不予理赔或责任免除的场景"]}, "vendorInsuranceTips": "上述车辆及第三方保障仅覆盖车行服务保障范围内的损失，服务保障范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求导致保障服务公司拒绝处理或无法提供保障的情况，您需承担全额损失。"}, "priceChangeInfo": {}, "idCardTypes": [{"idCardType": 1, "idCardName": "身份证"}, {"idCardType": 2, "idCardName": "护照"}, {"idCardType": 7, "idCardName": "回乡证"}], "depositInfo": {"depositType": 0, "carRentalDeposit": {"title": {"contentStyle": "1", "stringObjs": [{"content": "租车押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥", "style": "1"}, {"content": "3000", "style": "1"}, {"content": "可退", "style": "1"}]}, {"contentStyle": "2", "stringObjs": [{"content": "取车前冻结/支付，若无车损，还车后30天左右解冻/退还", "style": "1"}]}]}, "illegalDeposit": {"title": {"contentStyle": "1", "stringObjs": [{"content": "违章押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥", "style": "1"}, {"content": "3000", "style": "1"}, {"content": "可退", "style": "1"}]}, {"contentStyle": "2", "stringObjs": [{"content": "还车时冻结/支付，若无违章，还车后30天左右解冻/退还", "style": "1"}]}]}, "depositNote": {"contentStyle": "1", "stringObjs": [{"content": "如通过支付宝/微信支付押金，请在门店扫描二维码进行押金支付", "style": "1"}]}}, "promptInfos": [{"title": "增加多名驾驶员", "contents": [{"stringObjs": [{"content": "无忧租2024：默认仅支持一名驾驶员。如需多人开车，请提前与门店联系，无额外收费。每名驾驶员都需要出示与主驾驶员相同的取车证件。"}]}], "type": 21, "subType": 1}], "trackInfo": {"vendorCode": "30147", "vendorPlatFrom": 10, "depositFreeType": 3, "depositType": 0, "riskOriginal": "0", "riskFinal": "0"}, "gsDesc": "您选中了低价置顶lyy!", "extra": {"isPriceUnited": "1"}, "referenceTemp": {"pickWayInfo": 4, "returnWayInfo": 4, "isKarabi": 1, "klb": 1}, "rentalMustReadTitle": {"title": "限制政策", "desc": "禁止驶入：海南省亚龙湾站；沙漠、戈壁、赛道、水渠、草原"}, "imStatus": 1, "storeGuidInfos": [{"storeGuid": "店员免费接您至门店取车", "address": "汇合点：凤凰机场站", "type": 1}, {"storeGuid": "还车后店员免费送您至凤凰机场站", "address": "还车点：三亚市天涯区三亚凤凰机场酒店1", "type": 2}], "requestInfo": {"pickupDate": "/Date(1719367200000+0800)/", "pickupLocationName": "凤凰机场站", "returnDate": "/Date(1719453600000+0800)/", "returnLocationName": "凤凰机场站", "sourceCountryId": 1, "pLatitude": 18.308986, "rLatitude": 18.308986, "rLongitude": 109.408787, "pLongitude": 109.408787, "pDate": "20240626100000", "rDate": "20240627100000", "pCityId": 43, "rCityId": 43}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 2622, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 2622, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1718707077422, "afterFetch": 1718707080044}}