{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "42834df2-f923-43b3-8a8c-6a96f58e4225", "extMap": {"isKarabi": "0", "rentalDays": "1"}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "isSoldOut": false, "isHotCity": true, "pickupStoreInfo": {"storeCode": "81000014088309HKG", "bizVendorCode": "14088309", "telephone": "852-66143620", "storeName": "飞驰超跑香港Airport\t", "address": "香港国际机场", "longitude": 113.914, "latitude": 22.314, "mapUrl": "", "storeGuild": "1.每天一般服务时间为09:30——22:30，超出服务时间交收每小时额外收费HK$100\n2.送车去机场单程费用是HK$200，需取車時支付給供應商。\n3.机场规定：开车进入，首一小时35hkd，往后每小时50hkd，此费用需要客户自行线下承担支付\n4.司機會在P1停車場等待客人，實際位置請提前聯繫供應商確認\n5.若用信用卡支付，银行需要收取2.5%手续费", "storeWay": "机场外，步行可达", "workTime": {"openTimeDesc": "{\"\":\"09:30 - 22:30\"}"}, "storeServiceList": [], "countryId": 1, "countryName": "中国", "provinceId": 32, "provinceName": "香港", "cityId": 58, "cityName": "香港", "isAirportStore": true, "vendorStoreCode": "HKG", "continentId": 1}, "returnStoreInfo": {"storeCode": "81000014088309HKG", "bizVendorCode": "14088309", "telephone": "852-66143620", "storeName": "飞驰超跑香港Airport\t", "address": "香港国际机场", "longitude": 113.914, "latitude": 22.314, "mapUrl": "", "storeGuild": "1.门店营业时间：09:30——22:30，超出服务时间每小时额外收费HK$100\n2.送车去机场单程费用是HK$200，需交车时支付给供应商\n3.机场规定：开车进入，首一小时35hkd，往后每小时50hkd，此费用需要客户自行线下承担支付\n", "storeWay": "机场外", "workTime": {"openTimeDesc": "{\"\":\"09:30 - 22:30\"}"}, "storeServiceList": [], "countryId": 1, "countryName": "中国", "provinceId": 32, "provinceName": "香港", "cityId": 58, "cityName": "香港", "isAirportStore": true, "vendorStoreCode": "HKG", "continentId": 1}, "vendorInfo": {"bizVendorCode": "14088309", "vendorName": "Flying Auto", "vendorImageUrl": "https://dimg04.c-ctrip.com/images/0410a12000bkwjgv2CA35.png", "vendorCode": "SD2241", "isBroker": false, "haveCoupon": false, "vendorTag": {"title": "当地连锁", "sortNum": 0}}, "vehicleInfo": {"brandId": 2, "brandEName": "Audi", "name": "奥迪 Q3 Quattro", "zhName": "奥迪 Q3 Quattro", "vehicleCode": "22252", "imageUrl": "https://dimg04.c-ctrip.com/images/05W1b12000bp1tdk84025.png", "groupCode": "G04", "groupSubClassCode": "L", "groupName": "高级轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "imageList": ["https://dimg04.c-ctrip.com/images/05W3412000bp0xnkzAF5E.jpg"], "userRealImageCount": 0, "isSpecialized": false, "isHot": false, "recommendDesc": "", "hasConditioner": false, "spaceDesc": "", "similarCommentDesc": "", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14088309", "vendorName": "Flying Auto", "vendorLogo": "https://dimg04.c-ctrip.com/images/0410a12000bkwjgv2CA35.png", "similarVehicleInfos": [{"vehicleCode": "22252", "vehicleName": "奥迪 Q3 Quattro", "vehicleImageUrl": "https://dimg04.c-ctrip.com/images/05W1b12000bp1tdk84025.png"}]}], "realityImageUrl": "https://dimg04.c-ctrip.com/images/05W3412000bp0xnkzAF5E.jpg", "groupSubName": "豪华轿车"}, "flightDelayRule": {"title": "航班延误车辆保留规则", "description": "航班延误车辆保留规则", "subDesc": "如果您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性及供应商政策不同，可能无法为您保留车辆，或无法免费取消订单，具体条款以供应商实际政策为准，敬请谅解。", "rules": []}, "commentInfo": {"level": "", "commentCount": 1, "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=STORE_QUERY&storeId=81000014088309HKG&vehicleId=22252&vehicleName=奥迪 Q3 Quattro&productCategoryId=34&isHideNavBar=YES"}, "packageInfos": [{"insPackageId": 4, "isDefault": true, "packageName": "加强套餐", "currencyCode": "CNY", "defaultBomCode": "81000014088309HKG_22252_Fees_LM_STS_TPL_Taxes_0_0", "defaultPackageId": 18861883, "guaranteeDegree": 1.0, "naked": false, "insuranceNames": ["第三者保障"], "lowestDailyPrice": 1018, "gapPrice": 0, "stepPrice": 0, "packageType": 0}], "productDetails": [{"insPackageId": 4, "insuranceItems": [{"code": "TPL", "name": "第三者保障", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2"}], "combinations": [{"bomCode": "81000014088309HKG_22252_Fees_LM_STS_TPL_Taxes_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 1018, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 1018, "payMode": 3, "packageId": 18861883}], "productInfoList": [{"productCode": "vbk44074", "bomGroupCode": "81000014088309HKG_22252_Fees_LM_STS_TPL_Taxes_0_0", "priceInfoList": [{"currentCarPrice": 1018.0, "currentDailyPrice": 1018, "currentTotalPrice": 1018, "localCarPrice": 1088.85, "localDailyPrice": 1088.85, "localTotalPrice": 1088.85, "localCurrencyCode": "HKD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 374, "currentPrepaidPrice": 644, "localOnewayfee": 0.0, "localPoaPrice": 400.0, "localPrepaidPrice": 688.85, "isContainOnewayFee": false, "payMode": 3, "productId": "vbk4407418861883", "packageId": 18861883, "packageType": 0, "vcExtendRequest": {}, "exchangeRate": 0.93455, "mileInfo": {"name": "限里程", "isLimited": true, "distance": 250.0, "distanceUnit": "km", "periodUnit": "每天", "chargeAmount": 50.0, "chargeUnit": "公里", "quantity": 1.0, "desc": "租期内有公里数限制"}, "confirmInfo": {"confirmTitle": "72小时内确认", "confirmDesc": "预订此产品后供应商将在72小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 72.0}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"}, "ageRestriction": {"description": "最低年龄25周岁,最高年龄60周岁", "minDriverAge": 25, "maxDriverAge": 60, "youngDriverAgeDesc": "最低年龄25周岁", "oldDriverAgeDesc": "最高年龄60周岁", "licenceAge": 1, "licenceAgeDesc": "驾龄需满1年"}, "creditCardInfo": {"cardList": [{"name": "Visa", "type": "VI"}, {"name": "MasterCard", "type": "MC"}]}, "allTags": [{"title": "72小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在72小时内确认订单", "sortNum": 10, "colorCode": "1", "labelCode": "3664"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "不支持中国大陆驾照", "category": 4, "type": 2, "code": "4", "description": "供应商不支持使用中国大陆驾照租车。", "sortNum": 120, "colorCode": "4", "labelCode": "3561"}, {"title": "里程限制每天250KM", "category": 4, "type": 2, "code": "4", "description": "里程限制每天250KM，超出里程限制需额外支付每公里HKD50，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "sortNum": 125, "colorCode": "4", "labelCode": "3580"}], "insuranceDetails": [], "chargeList": [{"code": "CAR", "name": "Car rental fee", "payMode": 2, "netAmount": 560.0, "dueAmount": 560.0, "currency": "CNY", "isIncludedInRate": true}, {"code": "P_DELIVERY", "name": "Pick up delivery fee", "desc": "", "payMode": 1, "netAmount": 200.0, "dueAmount": 200.0, "currency": "HKD", "isIncludedInRate": false}, {"code": "D_DELIVERY", "name": "Drop off delivery fee", "desc": "", "payMode": 1, "netAmount": 200.0, "dueAmount": 200.0, "currency": "HKD", "isIncludedInRate": false}], "promotionInfo": {}, "vendorPromotionList": []}], "needFlightNo": true, "equipments": [], "packageItems": [{"code": "Fees", "name": "基础租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 1}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "TPL", "name": "第三者保障", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "LM", "name": "限里程", "desc": "租期内有公里数限制", "sortNum": 4}, {"code": "STS", "name": "等油取还", "desc": "取车时所提供的燃油量，在还车时应以等量返还", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "身份证明文件", "content": ["护照、驾照发证国家/地区须一致方可成功取车"], "type": 0, "subObject": [{"title": "中国大陆护照原件", "code": "CN"}, {"title": "中国香港护照原件", "code": "HK"}, {"title": "中国澳门护照原件", "code": "MO"}, {"title": "中国台湾护照原件", "code": "TW"}, {"title": "其他地区护照原件", "code": "OH"}]}, {"title": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满1年"], "type": 1, "subObject": [{"content": ["当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。", "所有驾驶员驾龄必须至少满1年"], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合", "code": "CN", "subObject": [{"title": "中国驾照原件 + 驾照国际翻译认证件", "content": ["由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 0, "sortNum": 6}], "sortNum": 0}, {"title": "门店支持以下驾照组合", "code": "HK", "subObject": [{"title": "香港驾照", "content": ["由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 0, "sortNum": 6}], "sortNum": 1}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 29}], "sortNum": 2}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 31}], "sortNum": 3}, {"title": "门店支持以下驾照组合", "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 11}], "sortNum": 4}]}]}, {"title": "主驾驶员名下国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型可能需要2张信用卡，具体请咨询门店"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型可能需要2张信用卡，具体请咨询门店", "不支持卡面带银联标志"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。该车型可能需要2张信用卡，具体请咨询门店", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6}, {"title": "接受的信用卡", "content": ["Visa，MasterCard", "不支持卡面带银联标志"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png"]}, {"title": "押金说明", "content": ["到店取车时，门店将从信用卡中冻结一笔押金预授权，最小最大押金：500-3,000USD，还车后30-60天内解冻退还"], "type": 8, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。", "contents": [{"stringObjs": [{"content": "500-3000USD"}]}], "showFree": false, "positiveDesc": "可退"}]}]}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}], "carRentalMustRead": [{"title": "确认政策", "content": ["预订此产品后供应商将在72小时内确认订单"], "type": 0, "code": "1"}, {"title": "取消政策", "content": ["取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"], "type": 1, "code": "1"}, {"title": "押金说明", "content": ["取车时，租车公司需要收取押金，还车时门店将先检查车辆，若车辆无问题，则按原支付方式退还押金。"], "type": 2, "code": "1", "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。", "contents": [{"stringObjs": [{"content": "500-3000USD"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["里程限制每天250KM，超出里程限制需额外支付每公里HKD50，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"], "type": 3, "code": "1"}, {"title": "燃油政策", "content": ["等油取还", "取车时所提供的燃油量，在还车时应以等量返还"], "type": 4, "code": "1"}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-60周岁", "最低年龄25周岁", "最高年龄60周岁", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1"}, {"title": "费用须知", "type": 40, "code": "3", "subObject": [{"title": "里程政策", "content": ["里程限制每天250KM，超出里程限制需额外支付每公里HKD50，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"], "type": 3, "code": "2"}, {"title": "燃油政策", "content": ["等油取还", "取车时所提供的燃油量，在还车时应以等量返还"], "type": 4, "code": "2"}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-60周岁", "最低年龄25周岁", "最高年龄60周岁", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2"}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2"}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2"}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2"}], "sortNum": 4}, {"title": "提前/延后取还车", "type": 41, "code": "3", "subObject": [{"title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 17, "code": "2"}, {"title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18, "code": "2"}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2"}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2"}], "sortNum": 3}, {"title": "租车保障", "type": 20, "code": "2", "subObject": [{"title": "保险，保障范围，豁免", "type": 21, "code": "2", "subObject": [{"title": "第三者保障", "content": ["保障第三方车辆或人员伤害损失", "若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。*实际赔付范围与标准以门店合同为准"]}]}, {"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2"}]}, {"title": "附加服务", "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2"}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2"}]}, {"title": "取消，未取车和修改", "type": 42, "code": "2", "subObject": [{"title": "取消政策", "content": ["取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"], "type": 30, "code": "2"}, {"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2"}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2"}]}, {"title": "租车公司重要信息", "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2"}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2"}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2"}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2"}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2"}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2"}]}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "4", "sortNum": 2}], "searchUnionPay": false, "searchCreditCard": false}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"], "type": 2}]}], "insuranceGroup": [{"code": "2", "title": "保被撞的车和人"}], "extraDesc": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准"]}], "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}, "promptInfos": [{"title": "押金汇率说明", "contents": [{"stringObjs": [{"content": "押金所需的人民币金额根据当前汇率计算，实际所需人民币金额以用户到店支付时或者授权免押时的汇率为准。"}]}], "type": 12}, {"title": "香港国际机场 is a popular location on your selected dates. Rates are likely to rise.", "subTitle": "Book now! Don't miss out on low prices.", "type": 29}], "requestInfo": {"pickupDate": "2023-12-04 10:00:00", "pickupLocationName": "香港国际机场", "returnDate": "2023-12-05 10:00:00", "returnLocationName": "香港国际机场", "sourceCountryId": 1, "pLatitude": 22.308047, "rLatitude": 22.308047, "rLongitude": 113.916292, "pLongitude": 113.916292, "pDate": "20231204100000", "rDate": "20231205100000", "pCityId": 58, "rCityId": 58}, "licenceCountryPolicy": {"title": "驾照", "type": 15, "subObject": [{"title": "中国大陆驾照", "subTitle": "香港（中国）不支持该驾照租车", "summaryContent": ["您需携带香港驾照才能成功取车", "所有驾驶员驾龄必须至少满1年"], "type": 1, "code": "CN", "optimalType": "-1"}, {"title": "中国香港驾照", "summaryContent": ["您需携带香港驾照才能成功取车", "所有驾驶员驾龄必须至少满1年"], "type": 1, "code": "HK", "optimalType": "1"}, {"title": "中国澳门驾照", "summaryContent": ["您需携带澳门驾照和国际驾照才能成功取车", "所有驾驶员驾龄必须至少满1年"], "type": 1, "code": "MO", "optimalType": "1"}, {"title": "中国台湾驾照", "summaryContent": ["您需携带台湾驾照和国际驾照才能成功取车", "所有驾驶员驾龄必须至少满1年"], "type": 1, "code": "TW", "optimalType": "1"}], "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "持非中国（含港澳台）驾照无法预订。建议更换驾驶员或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17}]}}