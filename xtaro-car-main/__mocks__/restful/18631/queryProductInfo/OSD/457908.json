{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "0566108b-ef9b-409a-b1cb-c61b99fea325", "extMap": {"isKarabi": "1", "rentalDays": "1"}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "2023-10-13 18:37:58", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "7433567132600857107"}, {"Id": "RootMessageId", "Value": "921822-0a8b4d1b-471442-23077"}]}, "isSoldOut": false, "isHotCity": true, "pickupStoreInfo": {"storeCode": "78855", "bizVendorCode": "14016", "telephone": "+86-4324342424444", "storeName": "LOS ANGELES", "address": "9775 AIRPORT BOULEVARD,LOS ANGELES INTERNATIONAL APO,90045", "longitude": -118.386772, "latitude": 33.947983, "storeGuild": "中文取车指引1", "storeWay": "机场内", "workTime": {"openTimeDesc": "{\"\":\"24小时营业\"}", "description": ""}, "storeServiceList": [], "countryId": 66, "countryName": "美国", "provinceId": 10125, "provinceName": "加利福尼亚州", "cityId": 347, "cityName": "洛杉矶", "isAirportStore": true, "vendorStoreCode": "LAXT01", "continentId": 4, "continentName": "北美洲"}, "returnStoreInfo": {"storeCode": "78855", "bizVendorCode": "14016", "telephone": "+86-4324342424444", "storeName": "LOS ANGELES", "address": "9775 AIRPORT BOULEVARD,LOS ANGELES INTERNATIONAL APO,90045", "longitude": -118.386772, "latitude": 33.947983, "storeGuild": "在门店停车场附近的道路旁通常能看到“Rental Car Return”的指示牌，按照指引将车辆驶入到对应的停车场办理还车手续，随后搭乘租车公司免费接驳巴士返回机场即可。", "storeWay": "机场内", "workTime": {"openTimeDesc": "{\"\":\"24小时营业\"}", "description": ""}, "storeServiceList": [], "countryId": 66, "countryName": "美国", "provinceId": 10125, "provinceName": "加利福尼亚州", "cityId": 347, "cityName": "洛杉矶", "isAirportStore": true, "vendorStoreCode": "LAXT01", "continentId": 4, "continentName": "北美洲"}, "vendorInfo": {"bizVendorCode": "14016", "vendorName": "Budget", "vendorImageUrl": "//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/budget_2020.png", "vendorCode": "SD0001", "isBroker": false, "platformCode": "0", "platformName": "Budget", "haveCoupon": true, "vendorTag": {"title": "全球连锁", "sortNum": 0}}, "vehicleInfo": {"brandId": 7, "brandEName": "<PERSON><PERSON>", "name": "起亚 Soul", "zhName": "起亚 Soul", "vehicleCode": "6196", "imageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg", "groupCode": "1", "groupSubClassCode": "39", "groupName": "经济型轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "imageList": ["https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "userRealImageCount": 0, "isSpecialized": false, "hasConditioner": true, "conditionerDesc": "A/C", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14016", "vendorName": "Budget", "vendorLogo": "//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/budget_2020.png", "similarVehicleInfos": [{"vehicleCode": "6196", "vehicleName": "起亚 Soul", "vehicleImageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"}]}], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "groupSubName": "中小型轿车"}, "flightDelayRule": {"title": "航班延误保留政策", "description": "若航班延误，门店将不保留车辆，请留取充足的时间取车", "rules": [{"title": "若航班延误，门店将不保留车辆，请留取充足的时间取车"}, {"title": "建议您填写航班号，如遇延误请携带机票前往取车"}]}, "commentInfo": {"level": "很好", "commentCount": 1, "overallRating": "4.8", "maximumRating": 5, "commentLabel": "", "hasComment": 1, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=78855&calabiVehicleId=6196&vehicleName=起亚 Soul&productCategoryId=34&isHideNavBar=YES"}, "packageInfos": [{"insPackageId": 2, "isDefault": true, "packageName": "基础套餐", "currencyCode": "CNY", "defaultBomCode": "78855_6196_CDW_FRFB_TP_Taxes_Taxes_ULM_0_0", "defaultPackageId": 329960, "guaranteeDegree": 1, "naked": false, "insuranceNames": ["车辆碰撞保障", "车辆盗抢险"], "lowestDailyPrice": 395, "gapPrice": 0, "stepPrice": 0, "description": "不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失", "packageType": 0}, {"insPackageId": 258, "isDefault": false, "packageName": "优享套餐", "currencyCode": "CNY", "defaultBomCode": "78855_6196_CDW_FRFB_TP_Taxes_Taxes_ULM_0_0", "defaultPackageId": 329960, "guaranteeDegree": 3, "naked": false, "insuranceNames": ["车辆碰撞保障", "车辆盗抢险", "安心补充险"], "lowestDailyPrice": 420, "gapPrice": 25, "stepPrice": 25}, {"insPackageId": 770, "isDefault": false, "packageName": "尊享套餐", "currencyCode": "CNY", "defaultBomCode": "78855_6196_CDW_FRFB_TP_Taxes_Taxes_ULM_0_0", "defaultPackageId": 329960, "guaranteeDegree": 4, "naked": false, "insuranceNames": ["车辆碰撞保障", "车辆盗抢险", "安心补充险", "驾乘意外险"], "lowestDailyPrice": 495, "gapPrice": 100, "stepPrice": 100}], "productDetails": [{"insPackageId": 2, "insuranceItems": [{"code": "CDW", "name": "车辆碰撞保障", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 329960, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1"}, {"code": "TP", "name": "车辆盗抢险", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 329960, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}]}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}]}], "insuranceDesc": ["呃呃呃"], "combinations": [{"bomCode": "78855_6196_CDW_FRFB_TP_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 395, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 395, "payMode": 1, "packageId": 329960}], "productInfoList": [{"productCode": "2", "bomGroupCode": "78855_6196_CDW_FRFB_TP_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 395, "currentDailyPrice": 395, "currentTotalPrice": 395, "localCarPrice": 54, "localDailyPrice": 54, "localTotalPrice": 54, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 395, "currentPrepaidPrice": 0, "localOnewayfee": 0, "localPoaPrice": 54, "localPrepaidPrice": 0, "isContainOnewayFee": true, "payMode": 1, "productId": "2329960", "packageId": 329960, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": "", "vendorVehicleId": "B_CCAR_GROUPB-KIASOUL"}, "exchangeRate": 7.30745, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "24小时内确认", "confirmDesc": "预订此产品后供应商将在24小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 24}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 72, "cancelDescription": "支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）"}, "ageRestriction": {"description": "驾驶员年龄要求：25-65周岁", "minDriverAge": 25, "maxDriverAge": 65, "youngDriverAgeDesc": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "oldDriverAgeDesc": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月"}, "creditCardInfo": {"cardList": [{"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "发现卡", "type": "DV"}, {"name": "虚拟信用卡", "type": "AP"}], "depositCurrencyCode": "USD", "maxDeposit": 200, "minDeposit": 100}, "allTags": [{"title": "24小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在24小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "0起赔额", "category": 2, "type": 1, "code": "2", "description": "该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。", "sortNum": 70, "colorCode": "2", "labelCode": "3553"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "四月特惠", "category": 3, "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "sortNum": 10000, "colorCode": "3", "labelCode": "3834"}, {"title": "国庆特惠", "category": 3, "type": 1, "description": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。", "sortNum": 10000, "colorCode": "3", "labelCode": "3854"}], "insuranceDetails": [{"code": "CDW", "name": "车辆碰撞保障", "currencyCode": "USD", "minExcess": 0, "maxExcess": 0}, {"code": "TP", "name": "车辆盗抢险", "currencyCode": "USD", "minExcess": 0, "maxExcess": 0}], "chargeList": [{"code": "Fees", "name": "基本租车费用", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "desc": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [{"type": 1, "title": "四月特惠", "deductionPercent": 0, "code": "3834", "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。"}, {"type": 1, "title": "国庆特惠", "deductionPercent": 0, "code": "3854", "longTag": "国庆特惠", "longDesc": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。"}], "pkgSellingRuleId": 329960}], "needFlightNo": false, "equipments": [], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "CDW", "name": "车辆碰撞保障", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "TP", "name": "车辆盗抢险", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "身份证明文件", "content": ["护照、驾照发证国家/地区须一致方可成功取车"], "type": 0, "subObject": [{"title": "中国大陆护照原件", "code": "CN"}, {"title": "中国香港护照原件", "code": "HK"}, {"title": "中国澳门护照原件", "code": "MO"}, {"title": "中国台湾护照原件", "code": "TW"}, {"title": "其他地区护照原件", "code": "OH"}]}, {"title": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满12个月", "驾驶员年龄要求：25-65周岁"], "type": 1, "subObject": [{"content": ["当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。", "所有驾驶员驾龄必须至少满12个月", "驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "code": "CN", "subObject": [{"title": "中国驾照原件 + 驾照国际翻译认证件", "content": ["中国驾照原件：中国驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 0, "code": "CDL,IDL", "sortNum": 2}, {"title": "中国驾照原件 + 车行翻译件", "content": ["中国驾照原件：中国驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 0, "code": "CDL,DLT", "sortNum": 3}, {"title": "中国驾照原件+英文公证件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OET,OLT", "sortNum": 4}, {"title": "中国驾照原件 + 当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5}], "sortNum": 0}, {"title": "门店支持以下驾照组合(任选其一)", "code": "HK", "subObject": [{"title": "香港驾照", "content": ["由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 0, "code": "HKDL", "sortNum": 3}, {"title": "国际驾照 + 香港驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）", "香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 0, "code": "IDP,HKDL", "sortNum": 37}], "sortNum": 1}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "MCDL,IDP", "sortNum": 29}], "sortNum": 2}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31}], "sortNum": 3}, {"title": "门店支持以下驾照组合", "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1}], "sortNum": 4}]}]}, {"title": "主驾驶员名下国际信用卡（2张）", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡", "支持银联双币卡（卡面可带银联标志）", "若无法提供信用卡，该门店也支持现金支付"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6, "note": "信用卡支付说明（中）"}, {"title": "接受的信用卡", "content": ["万事达，美国运通，大来卡，发现卡，虚拟信用卡", "支持银联双币卡（卡面可带银联标志）"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/airPlus.png"]}, {"title": "现金支付说明", "content": ["现金支付说明（中）"], "type": 19}, {"title": "押金说明", "content": ["到店刷取押金预授权，预计押金为租车费用+USD100-USD200（约¥730.75-¥1,461.49），还车后10-50天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后10-50天内退还。\n取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 100-US$ 200（约¥731-¥1,462）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3, "summaryTitle": "提车凭证"}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在24小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2023年11月12日10:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2023年11月12日10:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2023年11月12日10:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2023年11月12日10:00", "subTitle": "支付完成至取车前72小时", "description": "可免费取消", "showFree": true}, {"title": "2023年11月12日10:00后", "subTitle": "取车前72小时后", "description": "取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）", "showFree": false}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，预计押金为租车费用+USD100-USD200（约¥730.75-¥1,461.49），还车后10-50天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后10-50天内退还。\n取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 100-US$ 200（约¥731-¥1,462）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "保险服务提示", "content": ["呃呃呃"], "type": 45, "code": "2", "sortNum": 1}, {"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "旅行限制", "content": [], "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["前往加拿大旅行： 车辆可以不受限制地驶入加拿大。租车时必须通知柜台您打算开车进入加拿大，因此我们可以免费提供加拿大非居民保险卡。根据可用情况，异地还车仅可在某些指定加拿大城市 加拿大居民驾驶美国汽车前往加拿大： 持有加拿大驾照并计划驾驶BUDGET汽车穿越加拿大边境的客户只能租用带有加拿大牌照的汽车。 如果汽车只有美国牌照，持有加拿大驾照的客户不能把车开进加拿大。 美国租车前往加拿大：加拿大法律限制加拿大居民在加拿大驾驶美国汽车，然后返回美国。该法律也适用于在美国工作的加拿大公民，即使持有签证并拥有美国地址。 前往墨西哥：在参与的边境城市，仅允许企业客户在某些限制条件下驾驶车辆进入墨西哥。详情请咨询取车门店。 前往阿拉斯加：美国直营门店和部分加盟许可门店允许车辆在美国大陆各地无限制地行驶。视具体情况而定，某些美国城市允许异地还车。 允许车辆通过渡轮前往阿拉斯加。然而，票价是昂贵的。请注意，汽车必须返回西雅图，并且在任何情况下都不能在阿拉斯加还车，这是严格禁止的。阿拉斯加州渡轮从华盛顿贝灵汉出发，位于西雅图以北约 100 英里处。 建议选择在西雅图还车，然后作为步行乘客乘坐渡轮前往阿拉斯加。一旦客户到达阿拉斯加东南部，就有很多交通方式选择到整个地区的租车门店。 以下地点有上述驾驶限制例外情况： 宾夕法尼亚州机械堡车辆只能在以下州内行驶：康涅狄格州、哥伦比亚特区、特拉华州、伊利诺伊州、印第安纳肯塔基州、马萨诸塞州、马里兰州、缅因州、密歇根州、北卡罗来纳州、新罕布什尔州、新泽西州、纽约州、俄亥俄州、宾夕法尼亚州、罗德岛州、南卡罗来纳州、田纳西州、弗吉尼亚州、佛蒙特州、威斯康星州和西弗吉尼亚州。不允许进入加拿大。德克萨斯州布朗斯维尔的车辆只能在德克萨斯州哈林根州、拉雷多、麦卡伦行驶。华盛顿港安吉利斯的车辆只能在华盛顿州和俄勒冈州以及不列颠哥伦比亚省内行驶。 Yakima 车辆只能在以下州内行驶：爱达荷州、俄勒冈州和华盛顿州。不允许进入加拿大。 "], "type": 27, "code": "2", "sortNum": 1}], "sortNum": 5}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}, {"title": "长租政策", "content": ["请注意供应商不接受28天以上的连续租车，若需长租请联系供应商沟通。"], "type": 52, "code": "2", "sortNum": 8}], "sortNum": 7}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 16, "custumerTotalPrice": 75, "custumerDailyPrice": 75, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "localTotalPrice": 75, "localDailyPrice": 75, "localCurrencyCode": "CNY", "desc": "驾乘意外险短描述", "longDesc": "驾乘意外险长描述", "noCoverageContent": ["驾乘意外险不保障内容"], "typeCode": "ACCIDENT_COVER", "days": 1, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "version": "OPI1128778955596234752"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "1、报警并联系门店-卡拉比", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。"], "type": 1}, {"title": "2、还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额和，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"], "type": 2}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车", "description": "不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"]}, {"insPackageId": 258, "insuranceItems": [{"code": "CDW", "name": "车辆碰撞保障", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 329960, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1"}, {"code": "TP", "name": "车辆盗抢险", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 329960, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}]}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}]}], "ctripInsuranceIds": [15], "insuranceDesc": ["呃呃呃"], "combinations": [{"bomCode": "78855_6196_CDW_FRFB_TP_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 420, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 420, "payMode": 1, "packageId": 329960}], "productInfoList": [{"productCode": "2", "bomGroupCode": "78855_6196_CDW_FRFB_TP_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 395, "currentDailyPrice": 420, "currentTotalPrice": 420, "localCarPrice": 54, "localDailyPrice": 54, "localTotalPrice": 54, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 395, "currentPrepaidPrice": 25, "localOnewayfee": 0, "localPoaPrice": 54, "localPrepaidPrice": 0, "payMode": 1, "productId": "2329960", "packageId": 329960, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": "", "vendorVehicleId": "B_CCAR_GROUPB-KIASOUL"}, "exchangeRate": 7.30745, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "24小时内确认", "confirmDesc": "预订此产品后供应商将在24小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 24}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 72, "cancelDescription": "支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）"}, "ageRestriction": {"description": "驾驶员年龄要求：25-65周岁", "minDriverAge": 25, "maxDriverAge": 65, "youngDriverAgeDesc": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "oldDriverAgeDesc": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月"}, "creditCardInfo": {"cardList": [{"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "发现卡", "type": "DV"}, {"name": "虚拟信用卡", "type": "AP"}], "depositCurrencyCode": "USD", "maxDeposit": 200, "minDeposit": 100}, "allTags": [{"title": "24小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在24小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "0起赔额", "category": 2, "type": 1, "code": "2", "description": "该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。", "sortNum": 70, "colorCode": "2", "labelCode": "3553"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "四月特惠", "category": 3, "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "sortNum": 10000, "colorCode": "3", "labelCode": "3834"}, {"title": "国庆特惠", "category": 3, "type": 1, "description": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。", "sortNum": 10000, "colorCode": "3", "labelCode": "3854"}], "insuranceDetails": [{"code": "CDW", "name": "车辆碰撞保障", "currencyCode": "USD", "minExcess": 0, "maxExcess": 0}, {"code": "TP", "name": "车辆盗抢险", "currencyCode": "USD", "minExcess": 0, "maxExcess": 0}], "chargeList": [{"code": "Fees", "name": "基本租车费用", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "desc": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [{"type": 1, "title": "四月特惠", "deductionPercent": 0, "code": "3834", "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。"}, {"type": 1, "title": "国庆特惠", "deductionPercent": 0, "code": "3854", "longTag": "国庆特惠", "longDesc": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。"}], "pkgSellingRuleId": 329960}], "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 25, "localTotalPrice": 25, "localCurrencyCode": "CNY", "currentDailyPrice": 25, "currentTotalPrice": 25, "currentCurrencyCode": "CNY", "uniqueCode": "15"}], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "CDW", "name": "车辆碰撞保障", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "TP", "name": "车辆盗抢险", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}], "pickUpMaterials": [{"title": "身份证明文件", "content": ["护照、驾照发证国家/地区须一致方可成功取车"], "type": 0, "subObject": [{"title": "中国大陆护照原件", "code": "CN"}, {"title": "中国香港护照原件", "code": "HK"}, {"title": "中国澳门护照原件", "code": "MO"}, {"title": "中国台湾护照原件", "code": "TW"}, {"title": "其他地区护照原件", "code": "OH"}]}, {"title": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满12个月", "驾驶员年龄要求：25-65周岁"], "type": 1, "subObject": [{"content": ["当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。", "所有驾驶员驾龄必须至少满12个月", "驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "code": "CN", "subObject": [{"title": "中国驾照原件 + 驾照国际翻译认证件", "content": ["中国驾照原件：中国驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 0, "code": "CDL,IDL", "sortNum": 2}, {"title": "中国驾照原件 + 车行翻译件", "content": ["中国驾照原件：中国驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 0, "code": "CDL,DLT", "sortNum": 3}, {"title": "中国驾照原件+英文公证件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OET,OLT", "sortNum": 4}, {"title": "中国驾照原件 + 当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5}], "sortNum": 0}, {"title": "门店支持以下驾照组合(任选其一)", "code": "HK", "subObject": [{"title": "香港驾照", "content": ["由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 0, "code": "HKDL", "sortNum": 3}, {"title": "国际驾照 + 香港驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）", "香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 0, "code": "IDP,HKDL", "sortNum": 37}], "sortNum": 1}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "MCDL,IDP", "sortNum": 29}], "sortNum": 2}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31}], "sortNum": 3}, {"title": "门店支持以下驾照组合", "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1}], "sortNum": 4}]}]}, {"title": "主驾驶员名下国际信用卡（2张）", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡", "支持银联双币卡（卡面可带银联标志）", "若无法提供信用卡，该门店也支持现金支付"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6, "note": "信用卡支付说明（中）"}, {"title": "接受的信用卡", "content": ["万事达，美国运通，大来卡，发现卡，虚拟信用卡", "支持银联双币卡（卡面可带银联标志）"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/airPlus.png"]}, {"title": "现金支付说明", "content": ["现金支付说明（中）"], "type": 19}, {"title": "押金说明", "content": ["到店刷取押金预授权，预计押金为租车费用+USD100-USD200（约¥730.75-¥1,461.49），还车后10-50天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后10-50天内退还。\n取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 100-US$ 200（约¥731-¥1,462）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3, "summaryTitle": "提车凭证"}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在24小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2023年11月12日10:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2023年11月12日10:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2023年11月12日10:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2023年11月12日10:00", "subTitle": "支付完成至取车前72小时", "description": "可免费取消", "showFree": true}, {"title": "2023年11月12日10:00后", "subTitle": "取车前72小时后", "description": "取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）", "showFree": false}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，预计押金为租车费用+USD100-USD200（约¥730.75-¥1,461.49），还车后10-50天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后10-50天内退还。\n取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 100-US$ 200（约¥731-¥1,462）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "保险服务提示", "content": ["呃呃呃"], "type": 45, "code": "2", "sortNum": 1}, {"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "旅行限制", "content": [], "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["前往加拿大旅行： 车辆可以不受限制地驶入加拿大。租车时必须通知柜台您打算开车进入加拿大，因此我们可以免费提供加拿大非居民保险卡。根据可用情况，异地还车仅可在某些指定加拿大城市 加拿大居民驾驶美国汽车前往加拿大： 持有加拿大驾照并计划驾驶BUDGET汽车穿越加拿大边境的客户只能租用带有加拿大牌照的汽车。 如果汽车只有美国牌照，持有加拿大驾照的客户不能把车开进加拿大。 美国租车前往加拿大：加拿大法律限制加拿大居民在加拿大驾驶美国汽车，然后返回美国。该法律也适用于在美国工作的加拿大公民，即使持有签证并拥有美国地址。 前往墨西哥：在参与的边境城市，仅允许企业客户在某些限制条件下驾驶车辆进入墨西哥。详情请咨询取车门店。 前往阿拉斯加：美国直营门店和部分加盟许可门店允许车辆在美国大陆各地无限制地行驶。视具体情况而定，某些美国城市允许异地还车。 允许车辆通过渡轮前往阿拉斯加。然而，票价是昂贵的。请注意，汽车必须返回西雅图，并且在任何情况下都不能在阿拉斯加还车，这是严格禁止的。阿拉斯加州渡轮从华盛顿贝灵汉出发，位于西雅图以北约 100 英里处。 建议选择在西雅图还车，然后作为步行乘客乘坐渡轮前往阿拉斯加。一旦客户到达阿拉斯加东南部，就有很多交通方式选择到整个地区的租车门店。 以下地点有上述驾驶限制例外情况： 宾夕法尼亚州机械堡车辆只能在以下州内行驶：康涅狄格州、哥伦比亚特区、特拉华州、伊利诺伊州、印第安纳肯塔基州、马萨诸塞州、马里兰州、缅因州、密歇根州、北卡罗来纳州、新罕布什尔州、新泽西州、纽约州、俄亥俄州、宾夕法尼亚州、罗德岛州、南卡罗来纳州、田纳西州、弗吉尼亚州、佛蒙特州、威斯康星州和西弗吉尼亚州。不允许进入加拿大。德克萨斯州布朗斯维尔的车辆只能在德克萨斯州哈林根州、拉雷多、麦卡伦行驶。华盛顿港安吉利斯的车辆只能在华盛顿州和俄勒冈州以及不列颠哥伦比亚省内行驶。 Yakima 车辆只能在以下州内行驶：爱达荷州、俄勒冈州和华盛顿州。不允许进入加拿大。 "], "type": 27, "code": "2", "sortNum": 1}], "sortNum": 5}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}, {"title": "长租政策", "content": ["请注意供应商不接受28天以上的连续租车，若需长租请联系供应商沟通。"], "type": 52, "code": "2", "sortNum": 8}], "sortNum": 7}], "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 16, "custumerTotalPrice": 75, "custumerDailyPrice": 75, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "localTotalPrice": 75, "localDailyPrice": 75, "localCurrencyCode": "CNY", "desc": "驾乘意外险短描述", "longDesc": "驾乘意外险长描述", "noCoverageContent": ["驾乘意外险不保障内容"], "typeCode": "ACCIDENT_COVER", "days": 1, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "version": "OPI1128778955596234752"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "1、报警并联系门店-卡拉比", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。"], "type": 1}, {"title": "2、还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额和，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": " https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}, {"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 5}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 6}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 7}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 8}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"]}, {"insPackageId": 770, "insuranceItems": [{"code": "CDW", "name": "车辆碰撞保障", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 329960, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1"}, {"code": "TP", "name": "车辆盗抢险", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 329960, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}]}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥75/天"}], "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}]}], "ctripInsuranceIds": [15, 16], "insuranceDesc": ["呃呃呃"], "combinations": [{"bomCode": "78855_6196_CDW_FRFB_TP_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 495, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 495, "payMode": 1, "packageId": 329960}], "productInfoList": [{"productCode": "2", "bomGroupCode": "78855_6196_CDW_FRFB_TP_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 395, "currentDailyPrice": 495, "currentTotalPrice": 495, "localCarPrice": 54, "localDailyPrice": 54, "localTotalPrice": 54, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 395, "currentPrepaidPrice": 100, "localOnewayfee": 0, "localPoaPrice": 54, "localPrepaidPrice": 0, "payMode": 1, "productId": "2329960", "packageId": 329960, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": "", "vendorVehicleId": "B_CCAR_GROUPB-KIASOUL"}, "exchangeRate": 7.30745, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "24小时内确认", "confirmDesc": "预订此产品后供应商将在24小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 24}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 72, "cancelDescription": "支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）"}, "ageRestriction": {"description": "驾驶员年龄要求：25-65周岁", "minDriverAge": 25, "maxDriverAge": 65, "youngDriverAgeDesc": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "oldDriverAgeDesc": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月"}, "creditCardInfo": {"cardList": [{"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "发现卡", "type": "DV"}, {"name": "虚拟信用卡", "type": "AP"}], "depositCurrencyCode": "USD", "maxDeposit": 200, "minDeposit": 100}, "allTags": [{"title": "24小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在24小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "0起赔额", "category": 2, "type": 1, "code": "2", "description": "该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。", "sortNum": 70, "colorCode": "2", "labelCode": "3553"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "四月特惠", "category": 3, "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "sortNum": 10000, "colorCode": "3", "labelCode": "3834"}, {"title": "国庆特惠", "category": 3, "type": 1, "description": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。", "sortNum": 10000, "colorCode": "3", "labelCode": "3854"}], "insuranceDetails": [{"code": "CDW", "name": "车辆碰撞保障", "currencyCode": "USD", "minExcess": 0, "maxExcess": 0}, {"code": "TP", "name": "车辆盗抢险", "currencyCode": "USD", "minExcess": 0, "maxExcess": 0}], "chargeList": [{"code": "Fees", "name": "基本租车费用", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "desc": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": false}, {"code": "", "payMode": 1, "netAmount": 0, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [{"type": 1, "title": "四月特惠", "deductionPercent": 0, "code": "3834", "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。"}, {"type": 1, "title": "国庆特惠", "deductionPercent": 0, "code": "3854", "longTag": "国庆特惠", "longDesc": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。"}], "pkgSellingRuleId": 329960}], "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 25, "localTotalPrice": 25, "localCurrencyCode": "CNY", "currentDailyPrice": 25, "currentTotalPrice": 25, "currentCurrencyCode": "CNY", "uniqueCode": "15"}, {"name": "驾乘意外险", "localDailyPrice": 75, "localTotalPrice": 75, "localCurrencyCode": "CNY", "currentDailyPrice": 75, "currentTotalPrice": 75, "currentCurrencyCode": "CNY", "uniqueCode": "16"}], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "CDW", "name": "车辆碰撞保障", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "TP", "name": "车辆盗抢险", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}], "pickUpMaterials": [{"title": "身份证明文件", "content": ["护照、驾照发证国家/地区须一致方可成功取车"], "type": 0, "subObject": [{"title": "中国大陆护照原件", "code": "CN"}, {"title": "中国香港护照原件", "code": "HK"}, {"title": "中国澳门护照原件", "code": "MO"}, {"title": "中国台湾护照原件", "code": "TW"}, {"title": "其他地区护照原件", "code": "OH"}]}, {"title": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满12个月", "驾驶员年龄要求：25-65周岁"], "type": 1, "subObject": [{"content": ["当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。", "所有驾驶员驾龄必须至少满12个月", "驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "code": "CN", "subObject": [{"title": "中国驾照原件 + 驾照国际翻译认证件", "content": ["中国驾照原件：中国驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 0, "code": "CDL,IDL", "sortNum": 2}, {"title": "中国驾照原件 + 车行翻译件", "content": ["中国驾照原件：中国驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 0, "code": "CDL,DLT", "sortNum": 3}, {"title": "中国驾照原件+英文公证件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OET,OLT", "sortNum": 4}, {"title": "中国驾照原件 + 当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5}], "sortNum": 0}, {"title": "门店支持以下驾照组合(任选其一)", "code": "HK", "subObject": [{"title": "香港驾照", "content": ["由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 0, "code": "HKDL", "sortNum": 3}, {"title": "国际驾照 + 香港驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）", "香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 0, "code": "IDP,HKDL", "sortNum": 37}], "sortNum": 1}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "MCDL,IDP", "sortNum": 29}], "sortNum": 2}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31}], "sortNum": 3}, {"title": "门店支持以下驾照组合", "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1}], "sortNum": 4}]}]}, {"title": "主驾驶员名下国际信用卡（2张）", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡", "支持银联双币卡（卡面可带银联标志）", "若无法提供信用卡，该门店也支持现金支付"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6, "note": "信用卡支付说明（中）"}, {"title": "接受的信用卡", "content": ["万事达，美国运通，大来卡，发现卡，虚拟信用卡", "支持银联双币卡（卡面可带银联标志）"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/airPlus.png"]}, {"title": "现金支付说明", "content": ["现金支付说明（中）"], "type": 19}, {"title": "押金说明", "content": ["到店刷取押金预授权，预计押金为租车费用+USD100-USD200（约¥730.75-¥1,461.49），还车后10-50天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后10-50天内退还。\n取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 100-US$ 200（约¥731-¥1,462）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3, "summaryTitle": "提车凭证"}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在24小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2023年11月12日10:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2023年11月12日10:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2023年11月12日10:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2023年11月12日10:00", "subTitle": "支付完成至取车前72小时", "description": "可免费取消", "showFree": true}, {"title": "2023年11月12日10:00后", "subTitle": "取车前72小时后", "description": "取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥731.00）", "showFree": false}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，预计押金为租车费用+USD100-USD200（约¥730.75-¥1,461.49），还车后10-50天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后10-50天内退还。\n取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 100-US$ 200（约¥731-¥1,462）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "保险服务提示", "content": ["呃呃呃"], "type": 45, "code": "2", "sortNum": 1}, {"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "旅行限制", "content": [], "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["前往加拿大旅行： 车辆可以不受限制地驶入加拿大。租车时必须通知柜台您打算开车进入加拿大，因此我们可以免费提供加拿大非居民保险卡。根据可用情况，异地还车仅可在某些指定加拿大城市 加拿大居民驾驶美国汽车前往加拿大： 持有加拿大驾照并计划驾驶BUDGET汽车穿越加拿大边境的客户只能租用带有加拿大牌照的汽车。 如果汽车只有美国牌照，持有加拿大驾照的客户不能把车开进加拿大。 美国租车前往加拿大：加拿大法律限制加拿大居民在加拿大驾驶美国汽车，然后返回美国。该法律也适用于在美国工作的加拿大公民，即使持有签证并拥有美国地址。 前往墨西哥：在参与的边境城市，仅允许企业客户在某些限制条件下驾驶车辆进入墨西哥。详情请咨询取车门店。 前往阿拉斯加：美国直营门店和部分加盟许可门店允许车辆在美国大陆各地无限制地行驶。视具体情况而定，某些美国城市允许异地还车。 允许车辆通过渡轮前往阿拉斯加。然而，票价是昂贵的。请注意，汽车必须返回西雅图，并且在任何情况下都不能在阿拉斯加还车，这是严格禁止的。阿拉斯加州渡轮从华盛顿贝灵汉出发，位于西雅图以北约 100 英里处。 建议选择在西雅图还车，然后作为步行乘客乘坐渡轮前往阿拉斯加。一旦客户到达阿拉斯加东南部，就有很多交通方式选择到整个地区的租车门店。 以下地点有上述驾驶限制例外情况： 宾夕法尼亚州机械堡车辆只能在以下州内行驶：康涅狄格州、哥伦比亚特区、特拉华州、伊利诺伊州、印第安纳肯塔基州、马萨诸塞州、马里兰州、缅因州、密歇根州、北卡罗来纳州、新罕布什尔州、新泽西州、纽约州、俄亥俄州、宾夕法尼亚州、罗德岛州、南卡罗来纳州、田纳西州、弗吉尼亚州、佛蒙特州、威斯康星州和西弗吉尼亚州。不允许进入加拿大。德克萨斯州布朗斯维尔的车辆只能在德克萨斯州哈林根州、拉雷多、麦卡伦行驶。华盛顿港安吉利斯的车辆只能在华盛顿州和俄勒冈州以及不列颠哥伦比亚省内行驶。 Yakima 车辆只能在以下州内行驶：爱达荷州、俄勒冈州和华盛顿州。不允许进入加拿大。 "], "type": 27, "code": "2", "sortNum": 1}], "sortNum": 5}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}, {"title": "长租政策", "content": ["请注意供应商不接受28天以上的连续租车，若需长租请联系供应商沟通。"], "type": 52, "code": "2", "sortNum": 8}], "sortNum": 7}], "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 16, "custumerTotalPrice": 75, "custumerDailyPrice": 75, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "localTotalPrice": 75, "localDailyPrice": 75, "localCurrencyCode": "CNY", "desc": "驾乘意外险短描述", "longDesc": "驾乘意外险长描述", "noCoverageContent": ["驾乘意外险不保障内容"], "typeCode": "ACCIDENT_COVER", "days": 1, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "version": "OPI1128778955596234752"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "1、报警并联系门店-卡拉比", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。"], "type": 1}, {"title": "2、还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额和，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": " https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}, {"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 5}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 6}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 7}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 8}]}, {"subTitle": "您购买的套餐含国内保险公司提供的驾乘意外险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "subObject": [{"title": "驾乘意外险理赔流程步骤一名称", "content": ["驾乘意外险理赔流程步骤一描述"], "type": 1}, {"title": "驾乘意外险理赔流程步骤二名称", "content": ["驾乘意外险理赔流程步骤二描述"], "type": 2}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"]}], "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}, "promptInfos": [{"title": "押金汇率说明", "contents": [{"stringObjs": [{"content": "押金所需的人民币金额根据当前汇率计算，实际所需人民币金额以用户到店支付时或者授权免押时的汇率为准。"}]}], "type": 12}, {"title": "洛杉矶国际机场 is a popular location on your selected dates. Rates are likely to rise.", "subTitle": "Book now! Don't miss out on low prices.", "type": 29}], "platformInsuranceExtra": {"encourageTitle": "Extra Protection for Your Rental", "encourageDesc": "Your package does not cover you for all losses and damages. Protect yourself from unexpected damage costs with RentalCover.com's protection", "claimProcedure": [{"type": 1, "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。"]}, {"type": 2, "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额和，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"]}]}, "requestInfo": {"pickupDate": "2023-11-15 10:00:00", "pickupLocationName": "洛杉矶国际机场", "returnDate": "2023-11-16 10:00:00", "returnLocationName": "洛杉矶国际机场", "sourceCountryId": 1, "pLatitude": 33.941589, "rLatitude": 33.941589, "rLongitude": -118.40853, "pLongitude": -118.40853, "pDate": "20231115100000", "rDate": "20231116100000", "pCityId": 347, "rCityId": 347}, "licenceCountryPolicy": {"title": "驾照", "type": 15, "subObject": [{"title": "中国大陆驾照", "summaryContent": ["您需携带当地语言公证件和中国驾照原件和英文公证件（或驾照国际翻译认证件和中国驾照原件、当地语言公证件和中国驾照原件、中国驾照原件和车行翻译件）才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "CN", "optimalType": "1"}, {"title": "中国香港驾照", "summaryContent": ["您需携带国际驾照和香港驾照（或香港驾照）才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "HK", "optimalType": "1"}, {"title": "中国澳门驾照", "summaryContent": ["您需携带国际驾照和澳门驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "MO", "optimalType": "1"}, {"title": "中国台湾驾照", "summaryContent": ["您需携带国际驾照和台湾驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "TW", "optimalType": "1"}], "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "持非中国（含港澳台）驾照无法预订。建议更换驾驶员或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17}]}, "crossPolicy": {"crossLocationsInfos": [{"crossType": 3, "locations": [{"name": "安提瓜和巴布达", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "regionId": "201", "isSelected": false}, {"name": "安圭拉", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "regionId": "275", "isSelected": false}, {"name": "阿鲁巴", "status": 3, "statusName": "不允许跨境", "firstChar": "A", "regionId": "171", "isSelected": false}, {"name": "巴巴多斯", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "202", "isSelected": false}, {"name": "百慕大", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "207", "isSelected": false}, {"name": "伯利兹", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "210", "isSelected": false}, {"name": "英属维尔京群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "282", "isSelected": false}, {"name": "巴哈马", "status": 3, "statusName": "不允许跨境", "firstChar": "B", "regionId": "185", "isSelected": false}, {"name": "加拿大", "status": 2, "statusName": "条件跨境", "firstChar": "C", "policy": "车辆允许驶入加拿大，请在门店取车时告知跨境需求和地点以便门店工作人员为其准备跨境车辆所需要的绿卡（非居民保险卡，免费提供)", "regionId": "47", "isSelected": false}, {"name": "荷兰加勒比区", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "regionId": "259", "isSelected": false}, {"name": "开曼群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "regionId": "223", "isSelected": false}, {"name": "哥斯达黎加", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "regionId": "38", "isSelected": false}, {"name": "库拉索岛", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "regionId": "294", "isSelected": false}, {"name": "古巴", "status": 3, "statusName": "不允许跨境", "firstChar": "C", "regionId": "39", "isSelected": false}, {"name": "多米尼加共和国", "status": 3, "statusName": "不允许跨境", "firstChar": "D", "regionId": "276", "isSelected": false}, {"name": "多米尼克", "status": 3, "statusName": "不允许跨境", "firstChar": "D", "regionId": "217", "isSelected": false}, {"name": "萨尔瓦多", "status": 3, "statusName": "不允许跨境", "firstChar": "E", "regionId": "238", "isSelected": false}, {"name": "法属圣马丁", "status": 3, "statusName": "不允许跨境", "firstChar": "F", "regionId": "291", "isSelected": false}, {"name": "瓜德罗普岛", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "261", "isSelected": false}, {"name": "危地马拉", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "90", "isSelected": false}, {"name": "格林纳达", "status": 3, "statusName": "不允许跨境", "firstChar": "G", "regionId": "220", "isSelected": false}, {"name": "海地", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "regionId": "41", "isSelected": false}, {"name": "洪都拉斯", "status": 3, "statusName": "不允许跨境", "firstChar": "H", "regionId": "44", "isSelected": false}, {"name": "牙买加", "status": 3, "statusName": "不允许跨境", "firstChar": "J", "regionId": "101", "isSelected": false}, {"name": "墨西哥", "status": 3, "statusName": "不允许跨境", "firstChar": "M", "regionId": "72", "isSelected": false}, {"name": "马提尼克", "status": 3, "statusName": "不允许跨境", "firstChar": "M", "regionId": "279", "isSelected": false}, {"name": "英属蒙塞拉特岛", "status": 3, "statusName": "不允许跨境", "firstChar": "M", "regionId": "299", "isSelected": false}, {"name": "尼加拉瓜", "status": 3, "statusName": "不允许跨境", "firstChar": "N", "regionId": "198", "isSelected": false}, {"name": "波多黎各", "status": 3, "statusName": "不允许跨境", "firstChar": "P", "regionId": "208", "isSelected": false}, {"name": "巴拿马", "status": 3, "statusName": "不允许跨境", "firstChar": "P", "regionId": "18", "isSelected": false}, {"name": "圣皮埃尔和密克隆岛", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "289", "isSelected": false}, {"name": "荷属圣马丁", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "295", "isSelected": false}, {"name": "圣巴泰勒米", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "297", "isSelected": false}, {"name": "圣基茨和尼维斯", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "243", "isSelected": false}, {"name": "圣卢西亚", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "244", "isSelected": false}, {"name": "圣文森特和格林纳丁斯", "status": 3, "statusName": "不允许跨境", "firstChar": "S", "regionId": "246", "isSelected": false}, {"name": "特克斯和凯科斯群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "T", "regionId": "265", "isSelected": false}, {"name": "特立尼达和多巴哥", "status": 3, "statusName": "不允许跨境", "firstChar": "T", "regionId": "252", "isSelected": false}, {"name": "美国", "status": 3, "statusName": "不允许跨境", "firstChar": "U", "regionId": "66", "isSelected": false}, {"name": "美属维尔京群岛", "status": 3, "statusName": "不允许跨境", "firstChar": "U", "regionId": "280", "isSelected": false}], "crossTypeName": "跨境政策", "summaryPolicies": ["若您的行程中涉及跨境，请提前选择"], "title": "选择计划前往的国家", "subTitle": "门店支持在以下区域跨境使用车辆："}, {"crossType": 1, "crossTypeName": "跨岛政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨岛，建议您更换其它租车公司或车型组。"]}, {"crossType": 2, "crossTypeName": "跨州政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨州/省，建议您更换其它租车公司或车型组。"]}], "notes": ["若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。"], "title": "旅行限制"}}