{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "8dda8ec1-c9f1-4fe4-b11b-fb0b68b7d60a", "extMap": {"isKarabi": "1", "rentalDays": "7"}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1696730282823+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "6340134047281784574"}, {"Id": "RootMessageId", "Value": "921822-0a305c1c-471313-1569084"}]}, "isSoldOut": false, "isHotCity": true, "pickupStoreInfo": {"storeCode": "BNEEP08", "bizVendorCode": "14010", "telephone": "0061-7-38748150", "storeName": "BRISBANE AIRPORT", "address": "TERMINAL BUILDING,BRISBANE AIRPORT", "longitude": 153.1188, "latitude": -27.3855, "storeGuild": "机场内，步行可达，国际到达区域和国内到达区域都有柜台", "storeWay": "航站楼内，步行可达", "workTime": {"openTimeDesc": "{\"\":\"6:30 - 23:59\"}"}, "storeServiceList": [{"title": "电子提车凭证", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦", "typeCode": "F"}], "countryId": 15, "countryName": "澳大利亚", "provinceId": 10301, "provinceName": "昆士兰", "cityId": 680, "cityName": "布里斯班", "isAirportStore": true, "vendorStoreCode": "BNET01", "continentId": 3, "continentName": "大洋洲"}, "returnStoreInfo": {"storeCode": "BNEEP08", "bizVendorCode": "14010", "telephone": "0061-7-38748150", "storeName": "BRISBANE AIRPORT", "address": "TERMINAL BUILDING,BRISBANE AIRPORT", "longitude": 153.1188, "latitude": -27.3855, "storeGuild": "将车辆还至指定停车场后，请将钥匙还到所在航站楼的柜台，如需帮助请致电。", "storeWay": "航站楼内", "workTime": {"openTimeDesc": "{\"\":\"6:30 - 23:59\"}"}, "storeServiceList": [{"title": "电子提车凭证", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦", "typeCode": "F"}], "countryId": 15, "countryName": "澳大利亚", "provinceId": 10301, "provinceName": "昆士兰", "cityId": 680, "cityName": "布里斯班", "isAirportStore": true, "vendorStoreCode": "BNET01", "continentId": 3, "continentName": "大洋洲"}, "vendorInfo": {"bizVendorCode": "14010", "vendorName": "Europcar", "vendorImageUrl": "https://ak-d.tripcdn.com/images/0yc2x12000bzfnvvaB9A9.png", "vendorCode": "SD0006", "isBroker": false, "platformCode": "SD0006", "platformName": "Europcar", "haveCoupon": true, "vendorTag": {"title": "全球连锁", "sortNum": 0}}, "vehicleInfo": {"brandId": 20, "brandEName": "Hyundai", "name": "现代 i30", "zhName": "现代 i30", "vehicleCode": "10412", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Hyundai_I30.png", "groupCode": "G02", "groupSubClassCode": "C", "groupName": "小型轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "imageList": ["//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/HYUNDAI-I20.jpg"], "userRealImageCount": 0, "isSpecialized": false, "isHot": false, "recommendDesc": "常见的家用车型，三人短途首选", "hasConditioner": true, "conditionerDesc": "A/C", "spaceDesc": "建议乘坐3人+2行李箱", "similarCommentDesc": "空间类似福特福克斯", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14010", "vendorName": "Europcar", "vendorLogo": "https://ak-d.tripcdn.com/images/0yc2x12000bzfnvvaB9A9.png", "similarVehicleInfos": [{"vehicleCode": "10412", "vehicleName": "现代 i30", "vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Hyundai_I30.png"}, {"vehicleCode": "10412", "vehicleName": "现代 i30", "vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Hyundai_I30.png"}]}], "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/HYUNDAI-I20.jpg", "groupSubName": "紧凑型轿车"}, "flightDelayRule": {"title": "航班延误车辆保留规则", "description": "门店将为您保留车辆至当天23:59。", "subDesc": "门店将为您保留车辆至当天23:59。", "rules": [{"title": "航班延误车辆保留规则", "descs": ["航班延误保留至当天营业时间结束（23:59）。"]}, {"title": "航班延误取消规则", "descs": ["若航班延误无法免费取消订单，门店将按照取消规则处理。"]}]}, "commentInfo": {"level": "满意", "commentCount": 49, "overallRating": "4.6", "maximumRating": 5, "commentLabel": "", "hasComment": 1, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=STORE_QUERY&storeId=BNEEP08&vehicleId=10412&vehicleName=现代 i30&productCategoryId=34&isHideNavBar=YES"}, "packageInfos": [{"insPackageId": 2, "isDefault": true, "packageName": "基础套餐", "currencyCode": "CNY", "defaultBomCode": "BNEEP08_10412_FRFB_LDW_TPL_Taxes_Taxes_ULM_0_0", "defaultPackageId": 18237131, "guaranteeDegree": 2, "naked": false, "insuranceNames": ["碰撞盗抢保障", "第三者保障"], "lowestDailyPrice": 277, "gapPrice": 0, "stepPrice": 0, "description": "不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失", "packageType": 0}, {"insPackageId": 258, "isDefault": false, "packageName": "优享套餐", "currencyCode": "CNY", "defaultBomCode": "BNEEP08_10412_FRFB_LDW_TPL_Taxes_Taxes_ULM_0_0", "defaultPackageId": 18237131, "guaranteeDegree": 4, "naked": false, "insuranceNames": ["碰撞盗抢保障", "第三者保障", "安心补充险"], "lowestDailyPrice": 392, "gapPrice": 115, "stepPrice": 115}, {"insPackageId": 770, "isDefault": false, "packageName": "尊享套餐", "currencyCode": "CNY", "defaultBomCode": "BNEEP08_10412_FRFB_LDW_TPL_Taxes_Taxes_ULM_0_0", "defaultPackageId": 18237131, "guaranteeDegree": 5, "naked": false, "insuranceNames": ["碰撞盗抢保障", "第三者保障", "安心补充险", "驾乘意外险"], "lowestDailyPrice": 414, "gapPrice": 137, "stepPrice": 137, "descTitle": "全方位保障驾驶车辆、车内人员财物及第三者"}], "productDetails": [{"insPackageId": 2, "insuranceItems": [{"code": "LDW", "name": "碰撞盗抢保障", "description": "保障车辆碰撞、被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 18237131, "currencyCode": "AUD", "minExcess": 5000, "maxExcess": 5000, "excessShortDesc": "起赔额AU$ 5,000", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 18867665, "currencyCode": "AUD", "minExcess": 5000, "maxExcess": 5000, "excessShortDesc": "起赔额AU$ 5,000", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "1"}, {"code": "TPL", "name": "第三者保障", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 18237131, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 18867665, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}]}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 13, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}]}], "insuranceDesc": [""], "combinations": [{"bomCode": "BNEEP08_10412_FRFB_LDW_TPL_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 277, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 1941, "payMode": 3, "packageId": 18237131}], "productInfoList": [{"productCode": "2", "bomGroupCode": "BNEEP08_10412_FRFB_LDW_TPL_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 1941, "currentDailyPrice": 277, "currentTotalPrice": 1941, "localCarPrice": 415.89, "localDailyPrice": 59.41, "localTotalPrice": 415.89, "localCurrencyCode": "AUD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 1941, "localOnewayfee": 0, "localPoaPrice": 0, "localPrepaidPrice": 415.89, "isContainOnewayFee": false, "payMode": 2, "productId": "*********", "packageId": 18867665, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 4.66745, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 48, "cancelDescription": "取车（当地时间）前48小时以上可免费取消；取车（当地时间）前48小时内取消将收取€ 50.00（约¥384.00）作为违约金；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款"}, "ageRestriction": {"minDriverAge": 21, "maxDriverAge": 99, "youngDriverAge": 25, "oldDriverAge": 0, "youngDriverAgeDesc": "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "oldDriverAgeDesc": "", "licenceAge": 3, "licenceAgeDesc": "驾龄至少满3年", "youngDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 70, "localPrice": 15, "feeType": 0}, "oldDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 0, "localPrice": 0, "feeType": 0}}, "creditCardInfo": {"description": "取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为AU$ 500.00（约¥2,334.00）,请保证可用额度足以支付押金。若在门店使用信用卡消费，预估刷卡手续费为2.0%。押金预计会在还车后30-60天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。", "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "AUD", "maxDeposit": 0, "minDeposit": 500}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认订单", "sortNum": 10, "colorCode": "1", "labelCode": "3664"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前48小时以上可免费取消；取车（当地时间）前48小时内取消将收取€ 50.00（约¥384.00）作为违约金；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}, {"title": "券减¥7", "category": 3, "type": 3, "code": "3", "description": "券减¥7,可享立减￥7/天（7天共减￥50）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}], "insuranceDetails": [{"code": "LDW", "name": "碰撞盗抢保障", "currencyCode": "AUD", "minExcess": 5000, "maxExcess": 5000}, {"code": "TPL", "name": "第三者保障"}], "chargeList": [{"code": "", "payMode": 2, "netAmount": 153, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 41, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 336, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 245, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 913, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}], "promotionInfo": {"deductionAmount": 50}, "vendorPromotionList": [], "pkgSellingRuleId": 129613}, {"currentCarPrice": 1941, "currentDailyPrice": 277, "currentTotalPrice": 1941, "localCarPrice": 415.79, "localDailyPrice": 59.4, "localTotalPrice": 415.79, "localCurrencyCode": "AUD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 1688, "currentPrepaidPrice": 253, "localOnewayfee": 0, "localPoaPrice": 361.56, "localPrepaidPrice": 54.23, "isContainOnewayFee": false, "payMode": 3, "productId": "218237131", "packageId": 18237131, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 4.66745, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"}, "ageRestriction": {"minDriverAge": 21, "maxDriverAge": 99, "youngDriverAge": 25, "oldDriverAge": 0, "youngDriverAgeDesc": "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "oldDriverAgeDesc": "", "licenceAge": 3, "licenceAgeDesc": "驾龄至少满3年", "youngDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 70, "localPrice": 15, "feeType": 0}, "oldDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 0, "localPrice": 0, "feeType": 0}}, "creditCardInfo": {"description": "取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为AU$ 500.00（约¥2,334.00）,请保证可用额度足以支付押金。若在门店使用信用卡消费，预估刷卡手续费为2.0%。押金预计会在还车后30-60天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。", "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "AUD", "maxDeposit": 0, "minDeposit": 500}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认订单", "sortNum": 10, "colorCode": "1", "labelCode": "3664"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}], "insuranceDetails": [{"code": "LDW", "name": "碰撞盗抢保障", "currencyCode": "AUD", "minExcess": 5000, "maxExcess": 5000}, {"code": "TPL", "name": "第三者保障"}], "chargeList": [{"code": "", "desc": "", "payMode": 3, "netAmount": 12, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 25, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 6, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 30, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 22, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "payMode": 3, "netAmount": 0, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 7.5, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 129612}], "needFlightNo": false, "equipments": [], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "LDW", "name": "碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失", "sortNum": 3}, {"code": "TPL", "name": "第三者保障", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "身份证明文件", "content": ["护照、驾照发证国家/地区须一致方可成功取车"], "type": 0, "subObject": [{"title": "中国大陆护照原件", "code": "CN"}, {"title": "中国香港护照原件", "code": "HK"}, {"title": "中国澳门护照原件", "code": "MO"}, {"title": "中国台湾护照原件", "code": "TW"}, {"title": "其他地区护照原件", "code": "OH"}]}, {"title": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满3年"], "type": 1, "subObject": [{"content": ["当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。", "所有驾驶员驾龄必须至少满3年"], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "code": "CN", "subObject": [{"title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 0, "sortNum": 2}, {"title": "中国大陆驾照原件 + NAATI翻译件", "content": ["中国大陆驾照原件：中国大陆驾照原件", "NAATI翻译件：由澳大利亚NAATI机构颁发的驾驶员本国驾照翻译文件"], "type": 0, "sortNum": 8}], "sortNum": 0}, {"title": "门店支持以下驾照组合", "code": "HK", "subObject": [{"title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 37}], "sortNum": 1}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 29}], "sortNum": 2}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 31}], "sortNum": 3}, {"title": "门店支持以下驾照组合(任选其一)", "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 11}, {"title": "英文驾照", "content": ["由驾驶员所在国颁发的正式英文本国驾照"], "type": 0, "sortNum": 15}, {"title": "驾驶员本国驾照 + NAATI翻译件", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "NAATI翻译件：由澳大利亚NAATI机构颁发的驾驶员本国驾照翻译文件"], "type": 0, "sortNum": 21}], "sortNum": 4}]}, {"title": "注意：", "content": ["澳洲北部租车，供应商只支持国际驾照"], "type": 10}]}, {"title": "主驾驶员名下国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。", "不支持卡面带银联标志"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6}, {"title": "接受的信用卡", "content": ["维萨，万事达，美国运通，大来卡", "不支持卡面带银联标志"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png"]}, {"title": "押金说明", "content": ["取车时刷取押金预授权，预计押金至少为AU$ 500.00（约¥2,334.00） 。还车后30-60天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。", "contents": [{"stringObjs": [{"content": "AU$ 500（约¥2,334）"}]}], "showFree": false, "positiveDesc": "可退"}]}]}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}], "carRentalMustRead": [{"title": "确认政策", "content": ["预订此产品后供应商将在1小时内确认订单"], "type": 0, "code": "1"}, {"title": "取消政策", "content": ["取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"], "type": 1, "code": "1"}, {"title": "押金说明", "content": ["取车时刷取押金预授权，预计押金至少为AU$ 500.00（约¥2,334.00） 。还车后30-60天内退还。"], "type": 2, "code": "1", "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。", "contents": [{"stringObjs": [{"content": "AU$ 500（约¥2,334）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["不限里程"], "type": 3, "code": "1"}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1"}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-99周岁", "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1"}, {"title": "费用须知", "type": 40, "code": "3", "subObject": [{"title": "里程政策", "content": ["不限里程"], "type": 3, "code": "2"}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2"}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-99周岁", "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2"}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2"}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2"}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2"}], "sortNum": 4}, {"title": "提前/延后取还车", "type": 41, "code": "3", "subObject": [{"title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 17, "code": "2"}, {"title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18, "code": "2"}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2"}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2"}], "sortNum": 3}, {"title": "温馨提示", "type": 19, "code": "2", "subObject": [{"title": "门店提示", "content": ["区域限行：不允许去往昆士兰州奇拉戈以北或乔治镇以西，库克敦或劳拉以北的地区，Maggievill以北的地区、伯客发展之路、伊萨山以北和以西的非封闭道路、布卢姆菲尔德轨道、萨凡纳路。不允许去往南澳大利亚州的达雷山、斯特泽勒基小道。不允许穿越辛普森沙漠。不允许驶上西澳大利亚州和北领地的塔纳米步道和冈巴雷尔公路以及北领地通往吉姆吉姆瀑布或双子瀑布的公路。 不允许去王澳大利亚大陆与塔斯马尼亚岛之间的任一方向。不允许去往任何岛屿，但以下岛屿除外：袋鼠岛、斯特拉布鲁克岛、布里比岛、菲利普岛、布鲁尼岛。 同时，除非车辆为四驱车且已购买非密封道路保护，不然不允许在西澳大利亚州的勒韦克角公路的非封闭路段、通往温贾纳峡谷国家公园的道路、Cardabia-Ningaloo公路、大北方公路通往波奴鲁鲁国家公园的通道上行驶。不允许在南澳大利亚州乌德纳达塔步道、通往达尔豪西温泉的公路上行驶。不允许在北领地的拉拉平塔和纳马特吉拉公路（俗称Mereenie Loop）上行驶。仅有车辆为四驱车、购买非密封道路保护、向门店租用了第二个备用轮胎的情况下，才能在西澳大利亚州的吉布河路上行驶。"], "type": 47, "code": "2"}]}, {"title": "租车保障", "type": 20, "code": "2", "subObject": [{"title": "保险，保障范围，豁免", "type": 21, "code": "2", "subObject": [{"title": "碰撞盗抢保障", "content": ["保障车辆碰撞、被盗的损失", "车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准", "不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, {"title": "第三者保障", "content": ["保障第三方车辆或人员伤害损失", "若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。\n*实际赔付范围与标准以门店合同为准"]}]}, {"title": "保险服务提示", "content": [""], "type": 45, "code": "2"}, {"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2"}]}, {"title": "附加服务", "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2"}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2"}]}, {"title": "旅行限制", "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["不可跨境"], "type": 27, "code": "2"}]}, {"title": "取消，未取车和修改", "type": 42, "code": "2", "subObject": [{"title": "取消政策", "content": ["取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"], "type": 30, "code": "2"}, {"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2"}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2"}]}, {"title": "租车公司重要信息", "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2"}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2"}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2"}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2"}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2"}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2"}]}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "4", "sortNum": 2}], "searchUnionPay": false, "searchCreditCard": false, "crossIslandInfo": {"crossType": 0, "crossIslandLocationList": [{}, {}], "crossIslandPolicyList": [{"policyTitle": "允许跨岛", "policyType": 1, "policyDescription": "车辆允许上岛，但客户对车辆发生的任何损坏或车辆在渡轮上造成的损坏负全部责任。 这包括在渡轮/驳船沉没的情况下淹没。"}]}, "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 11, "custumerTotalPrice": 154, "custumerDailyPrice": 22, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "localTotalPrice": 154, "localDailyPrice": 22, "localCurrencyCode": "CNY", "desc": "保障全车人员意外伤害及财物损失", "longDesc": "由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方：", "noCoverageContent": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "ACCIDENT_COVER", "days": 7, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/2.pdf", "version": "OPI1120382914299691008"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车", "description": "不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}]}, {"insPackageId": 258, "insuranceItems": [{"code": "LDW", "name": "碰撞盗抢保障", "description": "保障车辆碰撞、被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 18237131, "currencyCode": "AUD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额AU$ 5,000可赔"}, {"packageId": 18867665, "currencyCode": "AUD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额AU$ 5,000可赔"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "1"}, {"code": "TPL", "name": "第三者保障", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 18237131, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 18867665, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥115/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 13, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}]}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}]}], "ctripInsuranceIds": [13], "insuranceDesc": [""], "combinations": [{"bomCode": "BNEEP08_10412_FRFB_LDW_TPL_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 392, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 2746, "payMode": 3, "packageId": 18237131}], "productInfoList": [{"productCode": "2", "bomGroupCode": "BNEEP08_10412_FRFB_LDW_TPL_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 1941, "currentDailyPrice": 392, "currentTotalPrice": 2746, "localCarPrice": 415.89, "localDailyPrice": 59.41, "localTotalPrice": 415.89, "localCurrencyCode": "AUD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 2746, "localOnewayfee": 0, "localPoaPrice": 0, "localPrepaidPrice": 415.89, "payMode": 2, "productId": "*********", "packageId": 18867665, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 4.66745, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 48, "cancelDescription": "取车（当地时间）前48小时以上可免费取消；取车（当地时间）前48小时内取消将收取€ 50.00（约¥384.00）作为违约金；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款"}, "ageRestriction": {"minDriverAge": 21, "maxDriverAge": 99, "youngDriverAge": 25, "oldDriverAge": 0, "youngDriverAgeDesc": "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "oldDriverAgeDesc": "", "licenceAge": 3, "licenceAgeDesc": "驾龄至少满3年", "youngDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 70, "localPrice": 15, "feeType": 0}, "oldDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 0, "localPrice": 0, "feeType": 0}}, "creditCardInfo": {"description": "取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为AU$ 500.00（约¥2,334.00）,请保证可用额度足以支付押金。若在门店使用信用卡消费，预估刷卡手续费为2.0%。押金预计会在还车后30-60天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。", "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "AUD", "maxDeposit": 0, "minDeposit": 500}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认订单", "sortNum": 10, "colorCode": "1", "labelCode": "3664"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前48小时以上可免费取消；取车（当地时间）前48小时内取消将收取€ 50.00（约¥384.00）作为违约金；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}, {"title": "券减¥7", "category": 3, "type": 3, "code": "3", "description": "券减¥7,可享立减￥7/天（7天共减￥50）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}], "insuranceDetails": [{"code": "LDW", "name": "碰撞盗抢保障", "currencyCode": "AUD", "minExcess": 5000, "maxExcess": 5000}, {"code": "TPL", "name": "第三者保障"}], "chargeList": [{"code": "", "payMode": 2, "netAmount": 153, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 41, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 336, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 245, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 913, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}], "promotionInfo": {"deductionAmount": 50}, "vendorPromotionList": [], "pkgSellingRuleId": 129613}, {"currentCarPrice": 1941, "currentDailyPrice": 392, "currentTotalPrice": 2746, "localCarPrice": 415.79, "localDailyPrice": 59.4, "localTotalPrice": 415.79, "localCurrencyCode": "AUD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 1688, "currentPrepaidPrice": 1058, "localOnewayfee": 0, "localPoaPrice": 361.56, "localPrepaidPrice": 54.23, "payMode": 3, "productId": "218237131", "packageId": 18237131, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 4.66745, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"}, "ageRestriction": {"minDriverAge": 21, "maxDriverAge": 99, "youngDriverAge": 25, "oldDriverAge": 0, "youngDriverAgeDesc": "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "oldDriverAgeDesc": "", "licenceAge": 3, "licenceAgeDesc": "驾龄至少满3年", "youngDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 70, "localPrice": 15, "feeType": 0}, "oldDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 0, "localPrice": 0, "feeType": 0}}, "creditCardInfo": {"description": "取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为AU$ 500.00（约¥2,334.00）,请保证可用额度足以支付押金。若在门店使用信用卡消费，预估刷卡手续费为2.0%。押金预计会在还车后30-60天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。", "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "AUD", "maxDeposit": 0, "minDeposit": 500}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认订单", "sortNum": 10, "colorCode": "1", "labelCode": "3664"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}], "insuranceDetails": [{"code": "LDW", "name": "碰撞盗抢保障", "currencyCode": "AUD", "minExcess": 5000, "maxExcess": 5000}, {"code": "TPL", "name": "第三者保障"}], "chargeList": [{"code": "", "desc": "", "payMode": 3, "netAmount": 12, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 25, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 6, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 30, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 22, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "payMode": 3, "netAmount": 0, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 7.5, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 129612}], "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 115, "localTotalPrice": 805, "localCurrencyCode": "CNY", "currentDailyPrice": 115, "currentTotalPrice": 805, "currentCurrencyCode": "CNY", "uniqueCode": "13"}], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "LDW", "name": "碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失", "sortNum": 3}, {"code": "TPL", "name": "第三者保障", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}], "pickUpMaterials": [{"title": "身份证明文件", "content": ["护照、驾照发证国家/地区须一致方可成功取车"], "type": 0, "subObject": [{"title": "中国大陆护照原件", "code": "CN"}, {"title": "中国香港护照原件", "code": "HK"}, {"title": "中国澳门护照原件", "code": "MO"}, {"title": "中国台湾护照原件", "code": "TW"}, {"title": "其他地区护照原件", "code": "OH"}]}, {"title": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满3年"], "type": 1, "subObject": [{"content": ["当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。", "所有驾驶员驾龄必须至少满3年"], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "code": "CN", "subObject": [{"title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 0, "sortNum": 2}, {"title": "中国大陆驾照原件 + NAATI翻译件", "content": ["中国大陆驾照原件：中国大陆驾照原件", "NAATI翻译件：由澳大利亚NAATI机构颁发的驾驶员本国驾照翻译文件"], "type": 0, "sortNum": 8}], "sortNum": 0}, {"title": "门店支持以下驾照组合", "code": "HK", "subObject": [{"title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 37}], "sortNum": 1}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 29}], "sortNum": 2}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 31}], "sortNum": 3}, {"title": "门店支持以下驾照组合(任选其一)", "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 11}, {"title": "英文驾照", "content": ["由驾驶员所在国颁发的正式英文本国驾照"], "type": 0, "sortNum": 15}, {"title": "驾驶员本国驾照 + NAATI翻译件", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "NAATI翻译件：由澳大利亚NAATI机构颁发的驾驶员本国驾照翻译文件"], "type": 0, "sortNum": 21}], "sortNum": 4}]}, {"title": "注意：", "content": ["澳洲北部租车，供应商只支持国际驾照"], "type": 10}]}, {"title": "主驾驶员名下国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。", "不支持卡面带银联标志"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6}, {"title": "接受的信用卡", "content": ["维萨，万事达，美国运通，大来卡", "不支持卡面带银联标志"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png"]}, {"title": "押金说明", "content": ["取车时刷取押金预授权，预计押金至少为AU$ 500.00（约¥2,334.00） 。还车后30-60天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。", "contents": [{"stringObjs": [{"content": "AU$ 500（约¥2,334）"}]}], "showFree": false, "positiveDesc": "可退"}]}]}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}], "carRentalMustRead": [{"title": "确认政策", "content": ["预订此产品后供应商将在1小时内确认订单"], "type": 0, "code": "1"}, {"title": "取消政策", "content": ["取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"], "type": 1, "code": "1"}, {"title": "押金说明", "content": ["取车时刷取押金预授权，预计押金至少为AU$ 500.00（约¥2,334.00） 。还车后30-60天内退还。"], "type": 2, "code": "1", "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。", "contents": [{"stringObjs": [{"content": "AU$ 500（约¥2,334）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["不限里程"], "type": 3, "code": "1"}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1"}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-99周岁", "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1"}, {"title": "费用须知", "type": 40, "code": "3", "subObject": [{"title": "里程政策", "content": ["不限里程"], "type": 3, "code": "2"}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2"}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-99周岁", "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2"}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2"}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2"}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2"}], "sortNum": 4}, {"title": "提前/延后取还车", "type": 41, "code": "3", "subObject": [{"title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 17, "code": "2"}, {"title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18, "code": "2"}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2"}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2"}], "sortNum": 3}, {"title": "温馨提示", "type": 19, "code": "2", "subObject": [{"title": "门店提示", "content": ["区域限行：不允许去往昆士兰州奇拉戈以北或乔治镇以西，库克敦或劳拉以北的地区，Maggievill以北的地区、伯客发展之路、伊萨山以北和以西的非封闭道路、布卢姆菲尔德轨道、萨凡纳路。不允许去往南澳大利亚州的达雷山、斯特泽勒基小道。不允许穿越辛普森沙漠。不允许驶上西澳大利亚州和北领地的塔纳米步道和冈巴雷尔公路以及北领地通往吉姆吉姆瀑布或双子瀑布的公路。 不允许去王澳大利亚大陆与塔斯马尼亚岛之间的任一方向。不允许去往任何岛屿，但以下岛屿除外：袋鼠岛、斯特拉布鲁克岛、布里比岛、菲利普岛、布鲁尼岛。 同时，除非车辆为四驱车且已购买非密封道路保护，不然不允许在西澳大利亚州的勒韦克角公路的非封闭路段、通往温贾纳峡谷国家公园的道路、Cardabia-Ningaloo公路、大北方公路通往波奴鲁鲁国家公园的通道上行驶。不允许在南澳大利亚州乌德纳达塔步道、通往达尔豪西温泉的公路上行驶。不允许在北领地的拉拉平塔和纳马特吉拉公路（俗称Mereenie Loop）上行驶。仅有车辆为四驱车、购买非密封道路保护、向门店租用了第二个备用轮胎的情况下，才能在西澳大利亚州的吉布河路上行驶。"], "type": 47, "code": "2"}]}, {"title": "租车保障", "type": 20, "code": "2", "subObject": [{"title": "保险，保障范围，豁免", "type": 21, "code": "2", "subObject": [{"title": "碰撞盗抢保障", "content": ["保障车辆碰撞、被盗的损失", "车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准", "不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, {"title": "第三者保障", "content": ["保障第三方车辆或人员伤害损失", "若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。\n*实际赔付范围与标准以门店合同为准"]}]}, {"title": "保险服务提示", "content": [""], "type": 45, "code": "2"}, {"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2"}]}, {"title": "附加服务", "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2"}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2"}]}, {"title": "旅行限制", "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["不可跨境"], "type": 27, "code": "2"}]}, {"title": "取消，未取车和修改", "type": 42, "code": "2", "subObject": [{"title": "取消政策", "content": ["取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"], "type": 30, "code": "2"}, {"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2"}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2"}]}, {"title": "租车公司重要信息", "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2"}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2"}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2"}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2"}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2"}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2"}]}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "4", "sortNum": 2}], "crossIslandInfo": {"crossType": 0, "crossIslandLocationList": [{}, {}], "crossIslandPolicyList": [{"policyTitle": "允许跨岛", "policyType": 1, "policyDescription": "车辆允许上岛，但客户对车辆发生的任何损坏或车辆在渡轮上造成的损坏负全部责任。 这包括在渡轮/驳船沉没的情况下淹没。"}]}, "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 11, "custumerTotalPrice": 154, "custumerDailyPrice": 22, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "localTotalPrice": 154, "localDailyPrice": 22, "localCurrencyCode": "CNY", "desc": "保障全车人员意外伤害及财物损失", "longDesc": "由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方：", "noCoverageContent": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "ACCIDENT_COVER", "days": 7, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/2.pdf", "version": "OPI1120382914299691008"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "提供理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核（1-3个工作日内反馈）", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款（审核通过后3个工作日内）", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}]}, {"insPackageId": 770, "insuranceItems": [{"code": "LDW", "name": "碰撞盗抢保障", "description": "保障车辆碰撞、被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 18237131, "currencyCode": "AUD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额AU$ 5,000可赔"}, {"packageId": 18867665, "currencyCode": "AUD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额AU$ 5,000可赔"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "1"}, {"code": "TPL", "name": "第三者保障", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 18237131, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}, {"packageId": 18867665, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥115/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 13, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}]}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥22/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}]}], "ctripInsuranceIds": [13, 11], "insuranceDesc": [""], "combinations": [{"bomCode": "BNEEP08_10412_FRFB_LDW_TPL_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 414, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 2900, "payMode": 3, "packageId": 18237131}], "productInfoList": [{"productCode": "2", "bomGroupCode": "BNEEP08_10412_FRFB_LDW_TPL_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 1941, "currentDailyPrice": 414, "currentTotalPrice": 2900, "localCarPrice": 415.89, "localDailyPrice": 59.41, "localTotalPrice": 415.89, "localCurrencyCode": "AUD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 2900, "localOnewayfee": 0, "localPoaPrice": 0, "localPrepaidPrice": 415.89, "payMode": 2, "productId": "*********", "packageId": 18867665, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 4.66745, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 48, "cancelDescription": "取车（当地时间）前48小时以上可免费取消；取车（当地时间）前48小时内取消将收取€ 50.00（约¥384.00）作为违约金；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款"}, "ageRestriction": {"minDriverAge": 21, "maxDriverAge": 99, "youngDriverAge": 25, "oldDriverAge": 0, "youngDriverAgeDesc": "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "oldDriverAgeDesc": "", "licenceAge": 3, "licenceAgeDesc": "驾龄至少满3年", "youngDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 70, "localPrice": 15, "feeType": 0}, "oldDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 0, "localPrice": 0, "feeType": 0}}, "creditCardInfo": {"description": "取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为AU$ 500.00（约¥2,334.00）,请保证可用额度足以支付押金。若在门店使用信用卡消费，预估刷卡手续费为2.0%。押金预计会在还车后30-60天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。", "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "AUD", "maxDeposit": 0, "minDeposit": 500}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认订单", "sortNum": 10, "colorCode": "1", "labelCode": "3664"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前48小时以上可免费取消；取车（当地时间）前48小时内取消将收取€ 50.00（约¥384.00）作为违约金；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}, {"title": "券减¥7", "category": 3, "type": 3, "code": "3", "description": "券减¥7,可享立减￥7/天（7天共减￥50）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}], "insuranceDetails": [{"code": "LDW", "name": "碰撞盗抢保障", "currencyCode": "AUD", "minExcess": 5000, "maxExcess": 5000}, {"code": "TPL", "name": "第三者保障"}], "chargeList": [{"code": "", "payMode": 2, "netAmount": 153, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 41, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 336, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 245, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 0, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "payMode": 2, "netAmount": 913, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}], "promotionInfo": {"deductionAmount": 50}, "vendorPromotionList": [], "pkgSellingRuleId": 129613}, {"currentCarPrice": 1941, "currentDailyPrice": 414, "currentTotalPrice": 2900, "localCarPrice": 415.79, "localDailyPrice": 59.4, "localTotalPrice": 415.79, "localCurrencyCode": "AUD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 1688, "currentPrepaidPrice": 1212, "localOnewayfee": 0, "localPoaPrice": 361.56, "localPrepaidPrice": 54.23, "payMode": 3, "productId": "218237131", "packageId": 18237131, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 4.66745, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"}, "ageRestriction": {"minDriverAge": 21, "maxDriverAge": 99, "youngDriverAge": 25, "oldDriverAge": 0, "youngDriverAgeDesc": "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "oldDriverAgeDesc": "", "licenceAge": 3, "licenceAgeDesc": "驾龄至少满3年", "youngDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 70, "localPrice": 15, "feeType": 0}, "oldDriverExtraFee": {"localCurrencyCode": "AUD", "currentPrice": 0, "localPrice": 0, "feeType": 0}}, "creditCardInfo": {"description": "取车时请携带主驾驶员名下凸字（卡号摸上去有凹凸感）的国际芯片信用卡用作车辆押金预授权，持卡人姓名需与主驾驶人护照上的姓名一致。预计押金至少为AU$ 500.00（约¥2,334.00）,请保证可用额度足以支付押金。若在门店使用信用卡消费，预估刷卡手续费为2.0%。押金预计会在还车后30-60天内退还，预授权解冻不会有短信通知，需自行查询信用卡额度是否恢复。", "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "AUD", "maxDeposit": 0, "minDeposit": 500}, "allTags": [{"title": "1小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在1小时内确认订单", "sortNum": 10, "colorCode": "1", "labelCode": "3664"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}], "insuranceDetails": [{"code": "LDW", "name": "碰撞盗抢保障", "currencyCode": "AUD", "minExcess": 5000, "maxExcess": 5000}, {"code": "TPL", "name": "第三者保障"}], "chargeList": [{"code": "", "desc": "", "payMode": 3, "netAmount": 12, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 25, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 6, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 30, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 22, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "payMode": 3, "netAmount": 0, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 7.5, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0, "dueAmount": 0, "currency": "AUD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 129612}], "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 115, "localTotalPrice": 805, "localCurrencyCode": "CNY", "currentDailyPrice": 115, "currentTotalPrice": 805, "currentCurrencyCode": "CNY", "uniqueCode": "13"}, {"name": "驾乘意外险", "localDailyPrice": 22, "localTotalPrice": 154, "localCurrencyCode": "CNY", "currentDailyPrice": 22, "currentTotalPrice": 154, "currentCurrencyCode": "CNY", "uniqueCode": "11"}], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "LDW", "name": "碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失", "sortNum": 3}, {"code": "TPL", "name": "第三者保障", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}], "pickUpMaterials": [{"title": "身份证明文件", "content": ["护照、驾照发证国家/地区须一致方可成功取车"], "type": 0, "subObject": [{"title": "中国大陆护照原件", "code": "CN"}, {"title": "中国香港护照原件", "code": "HK"}, {"title": "中国澳门护照原件", "code": "MO"}, {"title": "中国台湾护照原件", "code": "TW"}, {"title": "其他地区护照原件", "code": "OH"}]}, {"title": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满3年"], "type": 1, "subObject": [{"content": ["当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。", "所有驾驶员驾龄必须至少满3年"], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "code": "CN", "subObject": [{"title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 0, "sortNum": 2}, {"title": "中国大陆驾照原件 + NAATI翻译件", "content": ["中国大陆驾照原件：中国大陆驾照原件", "NAATI翻译件：由澳大利亚NAATI机构颁发的驾驶员本国驾照翻译文件"], "type": 0, "sortNum": 8}], "sortNum": 0}, {"title": "门店支持以下驾照组合", "code": "HK", "subObject": [{"title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 37}], "sortNum": 1}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 29}], "sortNum": 2}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 31}], "sortNum": 3}, {"title": "门店支持以下驾照组合(任选其一)", "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "sortNum": 11}, {"title": "英文驾照", "content": ["由驾驶员所在国颁发的正式英文本国驾照"], "type": 0, "sortNum": 15}, {"title": "驾驶员本国驾照 + NAATI翻译件", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "NAATI翻译件：由澳大利亚NAATI机构颁发的驾驶员本国驾照翻译文件"], "type": 0, "sortNum": 21}], "sortNum": 4}]}, {"title": "注意：", "content": ["澳洲北部租车，供应商只支持国际驾照"], "type": 10}]}, {"title": "主驾驶员名下国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。", "不支持卡面带银联标志"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6}, {"title": "接受的信用卡", "content": ["维萨，万事达，美国运通，大来卡", "不支持卡面带银联标志"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png"]}, {"title": "押金说明", "content": ["取车时刷取押金预授权，预计押金至少为AU$ 500.00（约¥2,334.00） 。还车后30-60天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。", "contents": [{"stringObjs": [{"content": "AU$ 500（约¥2,334）"}]}], "showFree": false, "positiveDesc": "可退"}]}]}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}], "carRentalMustRead": [{"title": "确认政策", "content": ["预订此产品后供应商将在1小时内确认订单"], "type": 0, "code": "1"}, {"title": "取消政策", "content": ["取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"], "type": 1, "code": "1"}, {"title": "押金说明", "content": ["取车时刷取押金预授权，预计押金至少为AU$ 500.00（约¥2,334.00） 。还车后30-60天内退还。"], "type": 2, "code": "1", "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。", "contents": [{"stringObjs": [{"content": "AU$ 500（约¥2,334）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["不限里程"], "type": 3, "code": "1"}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1"}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-99周岁", "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1"}, {"title": "费用须知", "type": 40, "code": "3", "subObject": [{"title": "里程政策", "content": ["不限里程"], "type": 3, "code": "2"}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2"}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-99周岁", "租车公司对21-25周岁的低龄驾驶员将收取“青年驾驶费”；参考价格：AU$ 15.00（约¥70.00）/天；需门店支付，不含税", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2"}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2"}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2"}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2"}], "sortNum": 4}, {"title": "提前/延后取还车", "type": 41, "code": "3", "subObject": [{"title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 17, "code": "2"}, {"title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18, "code": "2"}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2"}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2"}], "sortNum": 3}, {"title": "温馨提示", "type": 19, "code": "2", "subObject": [{"title": "门店提示", "content": ["区域限行：不允许去往昆士兰州奇拉戈以北或乔治镇以西，库克敦或劳拉以北的地区，Maggievill以北的地区、伯客发展之路、伊萨山以北和以西的非封闭道路、布卢姆菲尔德轨道、萨凡纳路。不允许去往南澳大利亚州的达雷山、斯特泽勒基小道。不允许穿越辛普森沙漠。不允许驶上西澳大利亚州和北领地的塔纳米步道和冈巴雷尔公路以及北领地通往吉姆吉姆瀑布或双子瀑布的公路。 不允许去王澳大利亚大陆与塔斯马尼亚岛之间的任一方向。不允许去往任何岛屿，但以下岛屿除外：袋鼠岛、斯特拉布鲁克岛、布里比岛、菲利普岛、布鲁尼岛。 同时，除非车辆为四驱车且已购买非密封道路保护，不然不允许在西澳大利亚州的勒韦克角公路的非封闭路段、通往温贾纳峡谷国家公园的道路、Cardabia-Ningaloo公路、大北方公路通往波奴鲁鲁国家公园的通道上行驶。不允许在南澳大利亚州乌德纳达塔步道、通往达尔豪西温泉的公路上行驶。不允许在北领地的拉拉平塔和纳马特吉拉公路（俗称Mereenie Loop）上行驶。仅有车辆为四驱车、购买非密封道路保护、向门店租用了第二个备用轮胎的情况下，才能在西澳大利亚州的吉布河路上行驶。"], "type": 47, "code": "2"}]}, {"title": "租车保障", "type": 20, "code": "2", "subObject": [{"title": "保险，保障范围，豁免", "type": 21, "code": "2", "subObject": [{"title": "碰撞盗抢保障", "content": ["保障车辆碰撞、被盗的损失", "车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准", "不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, {"title": "第三者保障", "content": ["保障第三方车辆或人员伤害损失", "若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。\n*实际赔付范围与标准以门店合同为准"]}]}, {"title": "保险服务提示", "content": [""], "type": 45, "code": "2"}, {"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2"}]}, {"title": "附加服务", "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2"}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2"}]}, {"title": "旅行限制", "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["不可跨境"], "type": 27, "code": "2"}]}, {"title": "取消，未取车和修改", "type": 42, "code": "2", "subObject": [{"title": "取消政策", "content": ["取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；"], "type": 30, "code": "2"}, {"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2"}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2"}]}, {"title": "租车公司重要信息", "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2"}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2"}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2"}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2"}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2"}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2"}]}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "4", "sortNum": 2}], "crossIslandInfo": {"crossType": 0, "crossIslandLocationList": [{}, {}], "crossIslandPolicyList": [{"policyTitle": "允许跨岛", "policyType": 1, "policyDescription": "车辆允许上岛，但客户对车辆发生的任何损坏或车辆在渡轮上造成的损坏负全部责任。 这包括在渡轮/驳船沉没的情况下淹没。"}]}, "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 11, "custumerTotalPrice": 154, "custumerDailyPrice": 22, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "localTotalPrice": 154, "localDailyPrice": 22, "localCurrencyCode": "CNY", "desc": "保障全车人员意外伤害及财物损失", "longDesc": "由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方：", "noCoverageContent": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "ACCIDENT_COVER", "days": 7, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/2.pdf", "version": "OPI1120382914299691008"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "提供理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核（1-3个工作日内反馈）", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款（审核通过后3个工作日内）", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}]}, {"subTitle": "您购买的套餐含国内保险公司提供的驾乘意外险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/pdf1903/2.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，请优先保障您的人身安全和健康，垫付相关费用，待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "提供理赔材料", "content": ["意外/医疗：门急诊/住院病历、医疗费用发票/收据原件、医院检查报告、事故证明等；财物损失：损失清单、购货凭证、损失证明等"], "type": 2}, {"title": "理赔审核（1-3个工作日内反馈）", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款（审核通过后3个工作日内）", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}]}], "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}, "promptInfos": [{"title": "押金汇率说明", "contents": [{"stringObjs": [{"content": "押金所需的人民币金额根据当前汇率计算，实际所需人民币金额以用户到店支付时或者授权免押时的汇率为准。"}]}], "type": 12}, {"title": "布里斯班机场 is a popular location on your selected dates. Rates are likely to rise.", "subTitle": "Book now! Don't miss out on low prices.", "type": 29}], "platformInsuranceExtra": {"encourageTitle": "Extra Protection for Your Rental", "encourageDesc": "Your package does not cover you for all losses and damages. Protect yourself from unexpected damage costs with RentalCover.com's protection", "claimProcedure": [{"type": 1, "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"]}, {"type": 2, "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"]}]}, "requestInfo": {"pickupDate": "/Date(1697248800000+0800)/", "pickupLocationName": "布里斯班机场", "returnDate": "/Date(1697853600000+0800)/", "returnLocationName": "布里斯班机场", "sourceCountryId": 1, "pLatitude": -27.394214, "rLatitude": -27.394214, "rLongitude": 153.12183, "pLongitude": 153.12183, "pDate": "20231014100000", "rDate": "20231021100000", "pCityId": 680, "rCityId": 680}, "licenceCountryPolicy": {"title": "驾照", "type": 15, "subObject": [{"title": "中国大陆驾照", "summaryContent": ["您需携带中国大陆驾照原件和NAATI翻译件（或中国大陆驾照原件和驾照国际翻译认证件）才能成功取车", "所有驾驶员驾龄必须至少满3年"], "type": 1, "code": "CN", "optimalType": "1"}, {"title": "中国香港驾照", "summaryContent": ["您需携带香港驾照和国际驾照才能成功取车", "所有驾驶员驾龄必须至少满3年"], "type": 1, "code": "HK", "optimalType": "1"}, {"title": "中国澳门驾照", "summaryContent": ["您需携带澳门驾照和国际驾照才能成功取车", "所有驾驶员驾龄必须至少满3年"], "type": 1, "code": "MO", "optimalType": "1"}, {"title": "中国台湾驾照", "summaryContent": ["您需携带台湾驾照和国际驾照才能成功取车", "所有驾驶员驾龄必须至少满3年"], "type": 1, "code": "TW", "optimalType": "1"}], "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "持非中国（含港澳台）驾照无法预订。建议更换驾驶员或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17}]}}