{"isHotCity": true, "productDetails": [{"insPackageId": 3, "insuranceGroup": [{"title": "保自己的车", "code": "1"}, {"title": "保被撞的车和人", "description": "不推荐。当地法律强制要求租车需有三者险，发生事故可能产生高额赔偿，门店也会要求购买保险。", "code": "2"}, {"title": "保车内人员财物", "code": "3"}], "insuranceCompareItems": [{"descriptionColorCode": "GREEN", "containsDescription": "（无需垫付）", "description": "0起赔额", "type": "LDW"}, {"contains": false, "type": "WDW"}, {"contains": false, "type": "TPL"}, {"contains": false, "type": "PI"}, {"contains": false, "type": "RAP"}, {"contains": false, "type": "CANCEL"}], "insuranceItems": [{"excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}, "groupCode": "1", "code": "SLDW", "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行承担起赔额以上的费用，起赔额通常较低或为零。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "isInclude": true, "description": "保障车辆碰撞、被盗的损失", "isFromCtrip": false, "name": "超级碰撞盗抢保障", "insuranceDetail": [{"coverageWithoutPlatformInsuranceV2": "0起赔额", "coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 129821, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}, {"coverageWithoutPlatformInsuranceV2": "0起赔额", "coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 109285, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}]}, {"groupCode": "2", "code": "ALI", "unConverageExplain": {"title": "不承保范围"}, "converageExplain": {"title": "承保范围", "content": ["若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "isInclude": false, "description": "保障第三方车辆或人员伤害损失", "isFromCtrip": false, "name": "加强三者险"}, {"groupCode": "3", "code": "PI", "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n由未经登记的驾驶人驾驶车辆造成的车辆事故\n个人物品不包括钱币、金块、股票债券等金钱类等价物以及票据合同等文件\n因超速驾驶，酒后驾驶或违反旅行国法律法规而导致的车辆事故\n*理赔时需提供报警凭证，取车时请向工作人员了解详细的保险条款。\n*实际赔付范围与标准以门店合同为准"]}, "converageExplain": {"title": "承保范围", "content": ["保障车内人员在正常使用租赁车辆期间发生的意外伤害、个人财物丢失。"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "isInclude": false, "description": "保障全车人员意外伤害及随车行李丢失", "isFromCtrip": false, "name": "人身财物险"}, {"description": "保障玻璃轮胎底盘，补偿道路救援费用", "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "productId": 3, "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE.pdf", "code": "FULL_COVERAGE", "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "groupCode": "1", "isInclude": false, "name": "安心补充险", "isFromCtrip": true}], "productInfoList": [{"needFlightNo": false, "naked": false, "productCode": "3", "priceInfoList": [{"localCarPrice": 252.7, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "取车时间前可免费取消;取车时间后取消将收取全部租金作为违约金"}, "chargeList": [{"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"dueAmount": 0, "currency": "CNY", "isIncludedInRate": false, "code": "", "payMode": 2, "desc": "", "netAmount": 0}], "isContainOnewayFee": false, "packageId": 109285, "exchangeRate": 7.26395, "promotionInfo": {"deductionAmount": 400}, "currentPoaPrice": 0, "pkgSellingRuleId": 109285, "currentDailyPrice": 262, "currentTotalPrice": 1836, "vendorPromotionList": [], "localTotalPrice": 252.7, "currentOnewayfee": 0, "payMode": 2, "productId": "3109285", "mileInfo": {"name": "不限里程", "desc": "租期内没有公里数限制。\n", "isLimited": false}, "currentCurrencyCode": "CNY", "ageRestriction": {"maxDriverAge": 80, "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "minDriverAge": 20, "youngDriverAge": 25, "description": "驾驶员年龄要求：20-80周岁", "youngDriverExtraFee": {"currentPrice": 211, "localCurrencyCode": "USD", "localPrice": 29, "feeType": 1}, "youngDriverAgeDesc": "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "oldDriverExtraFee": {"localPrice": 0, "currentPrice": 0}}, "confirmInfo": {"confirmTitle": "立即确认", "confirmDesc": "预订此车型后可快速确认订单。\n", "confirmTime": 0, "confirmRightNow": true}, "localCurrencyCode": "USD", "localPrepaidPrice": 252.7, "currentPrepaidPrice": 1836, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此车型后可快速确认订单。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "2024-07-18 10:00前可免费取消", "labelCode": "3563"}, {"category": 2, "sortNum": 56, "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 60, "code": "2", "title": "24h营业", "colorCode": "2", "type": 1, "description": "门店24小时营业。", "labelCode": "3491"}, {"category": 2, "sortNum": 70, "code": "2", "title": "0起赔额", "colorCode": "2", "type": 1, "description": "该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。", "labelCode": "3553"}, {"category": 2, "sortNum": 80, "title": "满油取还", "colorCode": "2", "type": 1, "description": "取车时油量是满的，还车时也需保证是满的。若还车时未满，租车公司将会收取差额的油费以及服务费。建议保留加油的单据以及取还车时显示的油量表照片以备用。", "labelCode": "3977"}, {"category": 2, "sortNum": 85, "code": "2", "title": "不限里程", "colorCode": "2", "type": 1, "description": "租期内没有公里数限制。", "labelCode": "3562"}, {"category": 2, "sortNum": 95, "code": "2", "title": "电子提车凭证", "colorCode": "2", "type": 1, "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "labelCode": "3552"}, {"category": 3, "sortNum": 0, "code": "30", "title": "券减¥57/天", "colorCode": "3", "type": 1, "description": "券减¥57/天,可享立减￥57/天（7天共减￥400）的优惠。", "labelCode": "3949"}], "vcExtendRequest": {"responseReturnLocationId": "", "vendorVehicleId": "LBGC3F93XQ39889-6301", "responsePickUpLocationId": ""}, "packageType": 0, "currentCarPrice": 1836, "creditCardInfo": {"maxDeposit": 300, "minDeposit": 200, "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "Discover", "type": "DV"}, {"name": "Japanese Credit Bureau Credit Card", "type": "JC"}], "depositCurrencyCode": "USD"}, "localDailyPrice": 36.1, "insuranceDetails": [{"code": "SLDW", "currencyCode": "USD", "minExcess": 0, "name": "超级碰撞盗抢保障", "maxExcess": 0}], "localPoaPrice": 0, "localOnewayfee": 0}, {"localCarPrice": 268, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": -1, "cancelDescription": "取车时间前可免费取消;取车时间后可免费取消"}, "chargeList": [{"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"dueAmount": 0, "currency": "USD", "isIncludedInRate": false, "code": "", "payMode": 1, "desc": "", "netAmount": 0}], "isContainOnewayFee": true, "packageId": 129821, "exchangeRate": 7.26395, "promotionInfo": {}, "currentPoaPrice": 1947, "pkgSellingRuleId": 129821, "currentDailyPrice": 278, "currentTotalPrice": 1947, "vendorPromotionList": [], "localTotalPrice": 268, "currentOnewayfee": 0, "payMode": 1, "productId": "3129821", "mileInfo": {"name": "不限里程", "desc": "租期内没有公里数限制。\n", "isLimited": false}, "currentCurrencyCode": "CNY", "ageRestriction": {"maxDriverAge": 80, "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "minDriverAge": 20, "youngDriverAge": 25, "description": "驾驶员年龄要求：20-80周岁", "youngDriverExtraFee": {"currentPrice": 211, "localCurrencyCode": "USD", "localPrice": 29, "feeType": 1}, "youngDriverAgeDesc": "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "oldDriverExtraFee": {"localPrice": 0, "currentPrice": 0}}, "confirmInfo": {"confirmTitle": "立即确认", "confirmDesc": "预订此车型后可快速确认订单。\n", "confirmTime": 0, "confirmRightNow": true}, "localCurrencyCode": "USD", "localPrepaidPrice": 0, "currentPrepaidPrice": 0, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此车型后可快速确认订单。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "2024-07-18 10:00前可免费取消", "labelCode": "3563"}, {"category": 2, "sortNum": 56, "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 60, "code": "2", "title": "24h营业", "colorCode": "2", "type": 1, "description": "门店24小时营业。", "labelCode": "3491"}, {"category": 2, "sortNum": 70, "code": "2", "title": "0起赔额", "colorCode": "2", "type": 1, "description": "该套餐保险起赔额为0。车辆碰撞、被盗、或发生涉及第三方的事故赔付，均有保险公司承担。", "labelCode": "3553"}, {"category": 2, "sortNum": 80, "title": "满油取还", "colorCode": "2", "type": 1, "description": "取车时油量是满的，还车时也需保证是满的。若还车时未满，租车公司将会收取差额的油费以及服务费。建议保留加油的单据以及取还车时显示的油量表照片以备用。", "labelCode": "3977"}, {"category": 2, "sortNum": 85, "code": "2", "title": "不限里程", "colorCode": "2", "type": 1, "description": "租期内没有公里数限制。", "labelCode": "3562"}, {"category": 2, "sortNum": 95, "code": "2", "title": "电子提车凭证", "colorCode": "2", "type": 1, "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "labelCode": "3552"}], "vcExtendRequest": {"responseReturnLocationId": "", "vendorVehicleId": "4RYM6FNFHQ39906-6301", "responsePickUpLocationId": ""}, "packageType": 0, "currentCarPrice": 1947, "creditCardInfo": {"maxDeposit": 300, "minDeposit": 200, "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "Discover", "type": "DV"}, {"name": "Japanese Credit Bureau Credit Card", "type": "JC"}], "depositCurrencyCode": "USD"}, "localDailyPrice": 38.29, "insuranceDetails": [{"code": "SLDW", "currencyCode": "USD", "minExcess": 0, "name": "超级碰撞盗抢保障", "maxExcess": 0}], "localPoaPrice": 268, "localOnewayfee": 0}], "equipments": [], "carRentalMustRead": [{"code": "4", "sortNum": 1, "title": "额外驾驶员", "type": 6}, {"code": "4", "sortNum": 2, "title": "营业时间外取还车", "type": 7}, {"code": "4", "sortNum": 3, "title": "提前/延后取还车", "type": 41}, {"code": "4", "sortNum": 4, "title": "费用须知", "type": 40}, {"code": "1", "sortNum": 1, "title": "确认政策", "content": ["预订此车型后可快速确认订单。\n"], "type": 0, "showFree": 1}, {"sortNum": 2, "content": ["取车时间前可免费取消;取车时间后取消将收取全部租金作为违约金"], "code": "1", "title": "取消政策", "table": [{"code": "FreeCancel", "title": "取消政策", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "当地时间2024-07-18 10:00前可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "当地时间2024-07-18 10:00前可免费取消", "style": "6"}]}, "showFree": true, "description": "注意：均为当地时间", "type": 300, "subTitle": "当地时间2024-07-18 10:00前可免费取消", "items": [{"showFree": true, "title": "2024-07-18 10:00前", "subTitle": "取车时间前", "description": "可免费取消"}, {"showFree": false, "title": "2024-07-18 10:00后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金"}]}], "showFree": 1, "type": 1}, {"code": "1", "sortNum": 3, "title": "押金说明", "content": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 2, "table": [{"showFree": false, "title": "押金", "positiveDesc": "可退", "description": "到店刷取押金预授权，还车后30-60天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180）"}]}]}]}, {"code": "1", "sortNum": 4, "title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3}, {"code": "1", "sortNum": 5, "title": "能源政策", "content": ["满油取还", "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"], "type": 4}, {"code": "1", "sortNum": 6, "title": "年龄要求", "content": ["驾驶员年龄要求：20-80周岁", "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3}, {"code": "2", "sortNum": 2, "title": "能源政策", "content": ["满油取还", "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"], "type": 4}, {"code": "2", "sortNum": 3, "title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6}, {"code": "2", "sortNum": 4, "title": "年龄要求", "content": ["驾驶员年龄要求：20-80周岁", "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5}, {"code": "2", "sortNum": 5, "title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7}, {"code": "2", "sortNum": 6, "title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16}, {"code": "2", "sortNum": 7, "title": "门店提示", "content": ["到店选购额外服务后将会收取9.5%的税费（Tax），详见租车合同费用明细。"], "type": 47}], "title": "费用须知", "content": [], "type": 40, "sortNum": 1}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17}, {"code": "2", "sortNum": 2, "title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18}, {"code": "2", "sortNum": 3, "title": "提前还车", "content": ["提前还车可能需要支付额外费用（如提前还车服务费、缩短租期里程限制改变导致的超里程费用、车行可能按照当日门市价重新计算租金等），具体费用请联系门店确认。由于您自身行为(例如违规驾驶、违反租车合同条款等)导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10}, {"code": "2", "sortNum": 4, "title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11}], "title": "提前/延后取还车", "content": [], "type": 41, "sortNum": 2}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "签合同前核对金额", "content": ["办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。"], "type": 59}, {"code": "2", "sortNum": 2, "title": "验车注意事项", "content": ["验车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身，油量里程，车损特写等），以免产生售后纠纷。"], "type": 60}], "title": "取车描述", "content": [], "type": 58, "sortNum": 3}, {"code": "2", "subObject": [{"code": "2", "sortNum": 2, "title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22}], "title": "租车保障", "content": [], "type": 20, "sortNum": 4}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24}, {"code": "2", "sortNum": 2, "title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25}], "title": "附加服务", "content": [], "type": 23, "sortNum": 5}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "跨境政策", "content": ["跨境至加拿大：多数情况允许车辆跨境至加拿大，跨境费用根据车辆类别、租用时间等情况决定，需提前联系门店确认。请注意，加拿大居民不得驾驶美国车辆进入加拿大。\n跨境至墨西哥：仅允许从德克萨斯州，新墨西哥州，亚利桑那州，加利福尼亚州跨境至墨西哥，且可驾驶范围仅限于美国与墨西哥边境以南 250 英里以内。 跨境需向门店申请并获得授权，请提前联系门店确认。跨境需购买墨西哥保险，该保险将覆盖跨境驾驶期间的第三者责任险、碰撞险以及综合保险，参考费用为每天38-48美元（含税费），具体购买方式请咨询门店。跨境至墨西哥期间，高级道路救援服务（PERS）不生效。请注意，墨西哥居民不得驾驶美国车辆进入墨西哥。"], "type": 27}], "title": "旅行限制", "content": [], "type": 26, "sortNum": 6}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31}, {"code": "2", "sortNum": 2, "title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32}], "title": "取消，未取车和修改", "content": [], "type": 42, "sortNum": 7}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34}, {"code": "2", "sortNum": 2, "title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35}, {"code": "2", "sortNum": 3, "title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36}, {"code": "2", "sortNum": 4, "title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37}, {"code": "2", "sortNum": 5, "title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38}, {"code": "2", "sortNum": 6, "title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39}, {"code": "2", "sortNum": 7, "title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13}], "title": "租车公司重要信息", "content": [], "type": 33, "sortNum": 8}], "searchUnionPay": false, "searchCreditCard": false, "bomGroupCode": "92021_66375_FRFB_SLDW_Taxes_Taxes_ULM_0_0", "packageItems": [{"code": "Taxes", "sortNum": 2, "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用"}, {"code": "Taxes", "sortNum": 2, "name": "税费（含：机场税，客户设施费，旅游税，销售税）", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。"}, {"code": "SLDW", "sortNum": 3, "name": "超级碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失"}, {"code": "ULM", "sortNum": 4, "name": "不限里程", "desc": "租期内没有公里数限制。\n"}, {"code": "FRFB", "sortNum": 4, "name": "满油取还", "desc": "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"}], "pickUpMaterials": [{"summaryTitle": "驾驶员需持有本人名下4项材料取车：", "title": "取车要求", "content": ["驾驶员年龄20-80周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "20-80周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}]}, {"subObject": [{"code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国大陆护照", "subTitle": "中国大陆护照原件"}, {"code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国香港护照", "subTitle": "中国香港护照原件"}, {"code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国澳门护照", "subTitle": "中国澳门护照原件"}, {"code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国台湾护照", "subTitle": "中国台湾护照原件"}, {"code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "其他地区护照", "subTitle": "其他地区护照原件"}], "title": "护照原件", "subTitle": "", "type": 0}, {"subObject": [{"type": 5, "subObject": [{"code": "CN", "subObject": [{"code": "CDL,IDL", "sortNum": 2, "title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22}, {"code": "CDL,DLT", "sortNum": 3, "title": "中国大陆驾照原件 + 车行翻译件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 22}, {"code": "CDL,OET", "sortNum": 4, "title": "中国大陆驾照原件 + 英文公证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 22}], "title": "门店支持以下驾照组合(任选其一)", "type": 5}, {"code": "HK", "subObject": [{"code": "HKDL,IDP", "sortNum": 37, "title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "MO", "subObject": [{"code": "MCDL,IDP", "sortNum": 29, "title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "TW", "subObject": [{"code": "TWDL,IDP", "sortNum": 31, "title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "OH", "subObject": [{"code": "ODL,IDP", "sortNum": 11, "title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "若您只能使用美加驾照租车，建议前往Trip.com下单。"}]}], "type": 10}], "title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1}, {"subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "type": 6, "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}], "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png"]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 8, "table": [{"showFree": false, "title": "押金", "positiveDesc": "可退", "description": "到店刷取押金预授权，还车后30-60天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180）"}]}]}]}], "title": "国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 2, "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，平台为您提供"], "type": 3}]}], "briefInsuranceItems": [{"code": "SLDW", "isFromCtrip": false, "name": "超级碰撞盗抢保障", "insuranceDetail": [{"coverageWithoutPlatformInsuranceV2": "0起赔额", "coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 129821, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}, {"coverageWithoutPlatformInsuranceV2": "0起赔额", "coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 109285, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}]}], "combinations": [{"codes": [], "dayPrice": 262, "gapPrice": 0, "title": "", "totalPrice": 1836, "bomCode": "92021_66375_FRFB_SLDW_Taxes_Taxes_ULM_0_0", "stepPrice": 0, "hike": true, "currency": "CNY", "payMode": 2, "packageId": 109285}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}]}, {"insPackageId": 5, "insuranceGroup": [{"title": "保自己的车", "description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失", "code": "1"}, {"title": "保被撞的车和人", "code": "2"}, {"title": "保车内人员财物", "code": "3"}], "insuranceCompareItems": [{"descriptionColorCode": "GREEN", "containsDescription": "（无需垫付）", "description": "0起赔额", "type": "LDW"}, {"contains": false, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": true, "type": "PI"}, {"contains": false, "type": "RAP"}, {"contains": false, "type": "CANCEL"}], "insuranceItems": [{"excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}, "groupCode": "1", "code": "SLDW", "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行承担起赔额以上的费用，起赔额通常较低或为零。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "isInclude": true, "description": "保障车辆碰撞、被盗的损失", "isFromCtrip": false, "name": "超级碰撞盗抢保障", "insuranceDetail": [{"coverageWithoutPlatformInsuranceV2": "0起赔额", "coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 129824, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}, {"coverageWithoutPlatformInsuranceV2": "0起赔额", "coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 109284, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}]}, {"groupCode": "2", "code": "ALI", "unConverageExplain": {"title": "不承保范围"}, "converageExplain": {"title": "承保范围", "content": ["若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "isInclude": true, "description": "保障第三方车辆或人员伤害损失", "isFromCtrip": false, "name": "加强三者险", "insuranceDetail": [{"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 129824, "coverageShortDesc": ""}, {"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 109284, "coverageShortDesc": ""}]}, {"groupCode": "3", "code": "PI", "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n由未经登记的驾驶人驾驶车辆造成的车辆事故\n个人物品不包括钱币、金块、股票债券等金钱类等价物以及票据合同等文件\n因超速驾驶，酒后驾驶或违反旅行国法律法规而导致的车辆事故\n*理赔时需提供报警凭证，取车时请向工作人员了解详细的保险条款。\n*实际赔付范围与标准以门店合同为准"]}, "converageExplain": {"title": "承保范围", "content": ["保障车内人员在正常使用租赁车辆期间发生的意外伤害、个人财物丢失。"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "isInclude": true, "description": "保障全车人员意外伤害及随车行李丢失", "isFromCtrip": false, "name": "人身财物险", "insuranceDetail": [{"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 129824, "coverageShortDesc": ""}, {"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 109284, "coverageShortDesc": ""}]}, {"description": "保障玻璃轮胎底盘，补偿道路救援费用", "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "productId": 3, "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE.pdf", "code": "FULL_COVERAGE", "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "groupCode": "1", "isInclude": false, "name": "安心补充险", "isFromCtrip": true}], "productInfoList": [{"searchCreditCard": false, "platformInsurance": {"insuranceClauses": "https://pages.c-ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE.pdf", "insurance": {"id": 3, "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localCurrencyCode": "CNY", "localDailyPrice": 45, "typeCode": "FULL_COVERAGE", "custumerTotalPrice": 315, "desc": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢自付额", "localTotalPrice": 315, "code": "MP18021529PK00024376", "days": 7, "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "longDesc": "由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：", "customerCurrencyCode": "CNY", "custumerDailyPrice": 45, "name": "安心补充险"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "version": "OPI1210602907838185472"}, "carRentalMustRead": [{"code": "4", "sortNum": 1, "title": "额外驾驶员", "type": 6}, {"code": "4", "sortNum": 2, "title": "营业时间外取还车", "type": 7}, {"code": "4", "sortNum": 3, "title": "提前/延后取还车", "type": 41}, {"code": "4", "sortNum": 4, "title": "费用须知", "type": 40}, {"code": "1", "sortNum": 1, "title": "确认政策", "content": ["预订此车型后可快速确认订单。\n"], "type": 0, "showFree": 1}, {"sortNum": 2, "content": ["取车时间前可免费取消;取车时间后取消将收取全部租金作为违约金"], "code": "1", "title": "取消政策", "table": [{"code": "FreeCancel", "title": "取消政策", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "当地时间2024-07-18 10:00前可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "当地时间2024-07-18 10:00前可免费取消", "style": "6"}]}, "showFree": true, "description": "注意：均为当地时间", "type": 300, "subTitle": "当地时间2024-07-18 10:00前可免费取消", "items": [{"showFree": true, "title": "2024-07-18 10:00前", "subTitle": "取车时间前", "description": "可免费取消"}, {"showFree": false, "title": "2024-07-18 10:00后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金"}]}], "showFree": 1, "type": 1}, {"code": "1", "sortNum": 3, "title": "押金说明", "content": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 2, "table": [{"showFree": false, "title": "押金", "positiveDesc": "可退", "description": "到店刷取押金预授权，还车后30-60天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180）"}]}]}]}, {"code": "1", "sortNum": 4, "title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3}, {"code": "1", "sortNum": 5, "title": "能源政策", "content": ["含一箱燃油", "取车时送一箱燃油，还车时可任意油量还车。"], "type": 4}, {"code": "1", "sortNum": 6, "title": "年龄要求", "content": ["驾驶员年龄要求：20-80周岁", "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3}, {"code": "2", "sortNum": 2, "title": "能源政策", "content": ["含一箱燃油", "取车时送一箱燃油，还车时可任意油量还车。"], "type": 4}, {"code": "2", "sortNum": 3, "title": "额外驾驶员", "content": ["4名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6}, {"code": "2", "sortNum": 4, "title": "年龄要求", "content": ["驾驶员年龄要求：20-80周岁", "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5}, {"code": "2", "sortNum": 5, "title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7}, {"code": "2", "sortNum": 6, "title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16}, {"code": "2", "sortNum": 7, "title": "门店提示", "content": ["到店选购额外服务后将会收取9.5%的税费（Tax），详见租车合同费用明细。"], "type": 47}], "title": "费用须知", "content": [], "type": 40, "sortNum": 1}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17}, {"code": "2", "sortNum": 2, "title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18}, {"code": "2", "sortNum": 3, "title": "提前还车", "content": ["提前还车可能需要支付额外费用（如提前还车服务费、缩短租期里程限制改变导致的超里程费用、车行可能按照当日门市价重新计算租金等），具体费用请联系门店确认。由于您自身行为(例如违规驾驶、违反租车合同条款等)导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10}, {"code": "2", "sortNum": 4, "title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11}], "title": "提前/延后取还车", "content": [], "type": 41, "sortNum": 2}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "签合同前核对金额", "content": ["办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。"], "type": 59}, {"code": "2", "sortNum": 2, "title": "验车注意事项", "content": ["验车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身，油量里程，车损特写等），以免产生售后纠纷。"], "type": 60}], "title": "取车描述", "content": [], "type": 58, "sortNum": 3}, {"code": "2", "subObject": [{"code": "2", "sortNum": 2, "title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22}], "title": "租车保障", "content": [], "type": 20, "sortNum": 4}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24}, {"code": "2", "sortNum": 2, "title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25}], "title": "附加服务", "content": [], "type": 23, "sortNum": 5}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "跨境政策", "content": ["跨境至加拿大：多数情况允许车辆跨境至加拿大，跨境费用根据车辆类别、租用时间等情况决定，需提前联系门店确认。请注意，加拿大居民不得驾驶美国车辆进入加拿大。\n跨境至墨西哥：仅允许从德克萨斯州，新墨西哥州，亚利桑那州，加利福尼亚州跨境至墨西哥，且可驾驶范围仅限于美国与墨西哥边境以南 250 英里以内。 跨境需向门店申请并获得授权，请提前联系门店确认。跨境需购买墨西哥保险，该保险将覆盖跨境驾驶期间的第三者责任险、碰撞险以及综合保险，参考费用为每天38-48美元（含税费），具体购买方式请咨询门店。跨境至墨西哥期间，高级道路救援服务（PERS）不生效。请注意，墨西哥居民不得驾驶美国车辆进入墨西哥。"], "type": 27}], "title": "旅行限制", "content": [], "type": 26, "sortNum": 6}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31}, {"code": "2", "sortNum": 2, "title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32}], "title": "取消，未取车和修改", "content": [], "type": 42, "sortNum": 7}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34}, {"code": "2", "sortNum": 2, "title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35}, {"code": "2", "sortNum": 3, "title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36}, {"code": "2", "sortNum": 4, "title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37}, {"code": "2", "sortNum": 5, "title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38}, {"code": "2", "sortNum": 6, "title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39}, {"code": "2", "sortNum": 7, "title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13}], "title": "租车公司重要信息", "content": [], "type": 33, "sortNum": 8}], "bomGroupCode": "92021_66375_ADD4_ALI_FPO_PI_SLDW_Taxes_Taxes_ULM_0_0", "needFlightNo": false, "searchUnionPay": false, "pickUpMaterials": [{"summaryTitle": "驾驶员需持有本人名下4项材料取车：", "title": "取车要求", "content": ["驾驶员年龄20-80周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "20-80周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}]}, {"subObject": [{"code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国大陆护照", "subTitle": "中国大陆护照原件"}, {"code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国香港护照", "subTitle": "中国香港护照原件"}, {"code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国澳门护照", "subTitle": "中国澳门护照原件"}, {"code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国台湾护照", "subTitle": "中国台湾护照原件"}, {"code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "其他地区护照", "subTitle": "其他地区护照原件"}], "title": "护照原件", "subTitle": "", "type": 0}, {"subObject": [{"type": 5, "subObject": [{"code": "CN", "subObject": [{"code": "CDL,IDL", "sortNum": 2, "title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22}, {"code": "CDL,DLT", "sortNum": 3, "title": "中国大陆驾照原件 + 车行翻译件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 22}, {"code": "CDL,OET", "sortNum": 4, "title": "中国大陆驾照原件 + 英文公证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 22}], "title": "门店支持以下驾照组合(任选其一)", "type": 5}, {"code": "HK", "subObject": [{"code": "HKDL,IDP", "sortNum": 37, "title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "MO", "subObject": [{"code": "MCDL,IDP", "sortNum": 29, "title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "TW", "subObject": [{"code": "TWDL,IDP", "sortNum": 31, "title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "OH", "subObject": [{"code": "ODL,IDP", "sortNum": 11, "title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "若您只能使用美加驾照租车，建议前往Trip.com下单。"}]}], "type": 10}], "title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1}, {"subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "type": 6, "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}], "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png"]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 8, "table": [{"showFree": false, "title": "押金", "positiveDesc": "可退", "description": "到店刷取押金预授权，还车后30-60天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180）"}]}]}]}], "title": "国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 2, "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，平台为您提供"], "type": 3}], "productCode": "4", "packageItems": [{"code": "Taxes", "sortNum": 2, "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用"}, {"code": "Taxes", "sortNum": 2, "name": "税费（含：机场税，客户设施费，旅游税，销售税）", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。"}, {"code": "ALI", "sortNum": 3, "name": "加强三者险", "desc": "保障第三方车辆或人员伤害损失"}, {"code": "PI", "sortNum": 3, "name": "人身财物险", "desc": "保障全车人员意外伤害及随车行李丢失"}, {"code": "SLDW", "sortNum": 3, "name": "超级碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失"}, {"code": "ULM", "sortNum": 4, "name": "不限里程", "desc": "租期内没有公里数限制。\n"}, {"code": "ADD4", "sortNum": 4, "name": "4名额外驾驶员", "desc": "4名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"}, {"code": "FPO", "sortNum": 4, "name": "含一箱燃油", "desc": "取车时送一箱燃油，还车时可任意油量还车。"}], "naked": false, "equipments": [], "priceInfoList": [{"localCarPrice": 379.7, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "取车时间前可免费取消;取车时间后取消将收取全部租金作为违约金"}, "chargeList": [{"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"dueAmount": 0, "currency": "CNY", "isIncludedInRate": false, "code": "", "payMode": 2, "desc": "", "netAmount": 0}], "isContainOnewayFee": false, "packageId": 109290, "exchangeRate": 7.26395, "promotionInfo": {"deductionAmount": 400}, "currentPoaPrice": 0, "pkgSellingRuleId": 109290, "currentDailyPrice": 394, "currentTotalPrice": 2758, "vendorPromotionList": [], "localTotalPrice": 379.7, "currentOnewayfee": 0, "payMode": 2, "productId": "4109290", "mileInfo": {"name": "不限里程", "desc": "租期内没有公里数限制。\n", "isLimited": false}, "currentCurrencyCode": "CNY", "ageRestriction": {"maxDriverAge": 80, "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "minDriverAge": 20, "youngDriverAge": 25, "description": "驾驶员年龄要求：20-80周岁", "youngDriverExtraFee": {"currentPrice": 211, "localCurrencyCode": "USD", "localPrice": 29, "feeType": 1}, "youngDriverAgeDesc": "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "oldDriverExtraFee": {"localPrice": 0, "currentPrice": 0}}, "confirmInfo": {"confirmTitle": "立即确认", "confirmDesc": "预订此车型后可快速确认订单。\n", "confirmTime": 0, "confirmRightNow": true}, "localCurrencyCode": "USD", "localPrepaidPrice": 379.7, "currentPrepaidPrice": 2758, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此车型后可快速确认订单。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "2024-07-18 10:00前可免费取消", "labelCode": "3563"}, {"category": 2, "sortNum": 56, "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 60, "code": "2", "title": "24h营业", "colorCode": "2", "type": 1, "description": "门店24小时营业。", "labelCode": "3491"}, {"category": 2, "sortNum": 66, "code": "2", "title": "含加强三者险", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含加强三者险", "labelCode": "3555"}, {"category": 2, "sortNum": 85, "code": "2", "title": "不限里程", "colorCode": "2", "type": 1, "description": "租期内没有公里数限制。", "labelCode": "3562"}, {"category": 2, "sortNum": 95, "code": "2", "title": "电子提车凭证", "colorCode": "2", "type": 1, "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "labelCode": "3552"}, {"category": 3, "sortNum": 0, "code": "30", "title": "券减¥57/天", "colorCode": "3", "type": 1, "description": "券减¥57/天,可享立减￥57/天（7天共减￥400）的优惠。", "labelCode": "3949"}], "vcExtendRequest": {"responseReturnLocationId": "", "vendorVehicleId": "VPW3FZ5OVI39944-6301", "responsePickUpLocationId": ""}, "packageType": 0, "currentCarPrice": 2758, "creditCardInfo": {"maxDeposit": 300, "minDeposit": 200, "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "Discover", "type": "DV"}, {"name": "Japanese Credit Bureau Credit Card", "type": "JC"}], "depositCurrencyCode": "USD"}, "localDailyPrice": 54.24, "insuranceDetails": [{"name": "加强三者险", "code": "ALI"}, {"name": "人身财物险", "code": "PI"}, {"code": "SLDW", "currencyCode": "USD", "minExcess": 0, "name": "超级碰撞盗抢保障", "maxExcess": 0}], "localPoaPrice": 0, "localOnewayfee": 0}]}, {"searchCreditCard": false, "platformInsurance": {"insuranceClauses": "https://pages.c-ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE.pdf", "insurance": {"id": 3, "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localCurrencyCode": "CNY", "localDailyPrice": 45, "typeCode": "FULL_COVERAGE", "custumerTotalPrice": 315, "desc": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢自付额", "localTotalPrice": 315, "code": "MP18021529PK00024376", "days": 7, "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "longDesc": "由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：", "customerCurrencyCode": "CNY", "custumerDailyPrice": 45, "name": "安心补充险"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "version": "OPI1210602907838185472"}, "carRentalMustRead": [{"code": "4", "sortNum": 1, "title": "额外驾驶员", "type": 6}, {"code": "4", "sortNum": 2, "title": "营业时间外取还车", "type": 7}, {"code": "4", "sortNum": 3, "title": "提前/延后取还车", "type": 41}, {"code": "4", "sortNum": 4, "title": "费用须知", "type": 40}, {"code": "1", "sortNum": 1, "title": "确认政策", "content": ["预订此车型后可快速确认订单。\n"], "type": 0, "showFree": 1}, {"sortNum": 2, "content": ["取车时间前可免费取消;取车时间后取消将收取全部租金作为违约金"], "code": "1", "title": "取消政策", "table": [{"code": "FreeCancel", "title": "取消政策", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "当地时间2024-07-18 10:00前可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "当地时间2024-07-18 10:00前可免费取消", "style": "6"}]}, "showFree": true, "description": "注意：均为当地时间", "type": 300, "subTitle": "当地时间2024-07-18 10:00前可免费取消", "items": [{"showFree": true, "title": "2024-07-18 10:00前", "subTitle": "取车时间前", "description": "可免费取消"}, {"showFree": false, "title": "2024-07-18 10:00后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金"}]}], "showFree": 1, "type": 1}, {"code": "1", "sortNum": 3, "title": "押金说明", "content": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 2, "table": [{"showFree": false, "title": "押金", "positiveDesc": "可退", "description": "到店刷取押金预授权，还车后30-60天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180）"}]}]}]}, {"code": "1", "sortNum": 4, "title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3}, {"code": "1", "sortNum": 5, "title": "能源政策", "content": ["满油取还", "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"], "type": 4}, {"code": "1", "sortNum": 6, "title": "年龄要求", "content": ["驾驶员年龄要求：20-80周岁", "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3}, {"code": "2", "sortNum": 2, "title": "能源政策", "content": ["满油取还", "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"], "type": 4}, {"code": "2", "sortNum": 3, "title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6}, {"code": "2", "sortNum": 4, "title": "年龄要求", "content": ["驾驶员年龄要求：20-80周岁", "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5}, {"code": "2", "sortNum": 5, "title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7}, {"code": "2", "sortNum": 6, "title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16}, {"code": "2", "sortNum": 7, "title": "门店提示", "content": ["到店选购额外服务后将会收取9.5%的税费（Tax），详见租车合同费用明细。"], "type": 47}], "title": "费用须知", "content": [], "type": 40, "sortNum": 1}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17}, {"code": "2", "sortNum": 2, "title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18}, {"code": "2", "sortNum": 3, "title": "提前还车", "content": ["提前还车可能需要支付额外费用（如提前还车服务费、缩短租期里程限制改变导致的超里程费用、车行可能按照当日门市价重新计算租金等），具体费用请联系门店确认。由于您自身行为(例如违规驾驶、违反租车合同条款等)导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10}, {"code": "2", "sortNum": 4, "title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11}], "title": "提前/延后取还车", "content": [], "type": 41, "sortNum": 2}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "签合同前核对金额", "content": ["办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。"], "type": 59}, {"code": "2", "sortNum": 2, "title": "验车注意事项", "content": ["验车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身，油量里程，车损特写等），以免产生售后纠纷。"], "type": 60}], "title": "取车描述", "content": [], "type": 58, "sortNum": 3}, {"code": "2", "subObject": [{"code": "2", "sortNum": 2, "title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22}], "title": "租车保障", "content": [], "type": 20, "sortNum": 4}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24}, {"code": "2", "sortNum": 2, "title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25}], "title": "附加服务", "content": [], "type": 23, "sortNum": 5}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "跨境政策", "content": ["跨境至加拿大：多数情况允许车辆跨境至加拿大，跨境费用根据车辆类别、租用时间等情况决定，需提前联系门店确认。请注意，加拿大居民不得驾驶美国车辆进入加拿大。\n跨境至墨西哥：仅允许从德克萨斯州，新墨西哥州，亚利桑那州，加利福尼亚州跨境至墨西哥，且可驾驶范围仅限于美国与墨西哥边境以南 250 英里以内。 跨境需向门店申请并获得授权，请提前联系门店确认。跨境需购买墨西哥保险，该保险将覆盖跨境驾驶期间的第三者责任险、碰撞险以及综合保险，参考费用为每天38-48美元（含税费），具体购买方式请咨询门店。跨境至墨西哥期间，高级道路救援服务（PERS）不生效。请注意，墨西哥居民不得驾驶美国车辆进入墨西哥。"], "type": 27}], "title": "旅行限制", "content": [], "type": 26, "sortNum": 6}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31}, {"code": "2", "sortNum": 2, "title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32}], "title": "取消，未取车和修改", "content": [], "type": 42, "sortNum": 7}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34}, {"code": "2", "sortNum": 2, "title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35}, {"code": "2", "sortNum": 3, "title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36}, {"code": "2", "sortNum": 4, "title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37}, {"code": "2", "sortNum": 5, "title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38}, {"code": "2", "sortNum": 6, "title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39}, {"code": "2", "sortNum": 7, "title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13}], "title": "租车公司重要信息", "content": [], "type": 33, "sortNum": 8}], "bomGroupCode": "92021_66375_ALI_FRFB_PI_SLDW_Taxes_Taxes_ULM_0_0", "needFlightNo": false, "searchUnionPay": false, "pickUpMaterials": [{"summaryTitle": "驾驶员需持有本人名下4项材料取车：", "title": "取车要求", "content": ["驾驶员年龄20-80周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "20-80周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}]}, {"subObject": [{"code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国大陆护照", "subTitle": "中国大陆护照原件"}, {"code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国香港护照", "subTitle": "中国香港护照原件"}, {"code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国澳门护照", "subTitle": "中国澳门护照原件"}, {"code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国台湾护照", "subTitle": "中国台湾护照原件"}, {"code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "其他地区护照", "subTitle": "其他地区护照原件"}], "title": "护照原件", "subTitle": "", "type": 0}, {"subObject": [{"type": 5, "subObject": [{"code": "CN", "subObject": [{"code": "CDL,IDL", "sortNum": 2, "title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22}, {"code": "CDL,DLT", "sortNum": 3, "title": "中国大陆驾照原件 + 车行翻译件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 22}, {"code": "CDL,OET", "sortNum": 4, "title": "中国大陆驾照原件 + 英文公证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 22}], "title": "门店支持以下驾照组合(任选其一)", "type": 5}, {"code": "HK", "subObject": [{"code": "HKDL,IDP", "sortNum": 37, "title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "MO", "subObject": [{"code": "MCDL,IDP", "sortNum": 29, "title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "TW", "subObject": [{"code": "TWDL,IDP", "sortNum": 31, "title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "OH", "subObject": [{"code": "ODL,IDP", "sortNum": 11, "title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "若您只能使用美加驾照租车，建议前往Trip.com下单。"}]}], "type": 10}], "title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1}, {"subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "type": 6, "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}], "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png"]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 8, "table": [{"showFree": false, "title": "押金", "positiveDesc": "可退", "description": "到店刷取押金预授权，还车后30-60天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180）"}]}]}]}], "title": "国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 2, "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，平台为您提供"], "type": 3}], "productCode": "4", "packageItems": [{"code": "Taxes", "sortNum": 2, "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用"}, {"code": "Taxes", "sortNum": 2, "name": "税费（含：机场税，客户设施费，旅游税，销售税）", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。"}, {"code": "ALI", "sortNum": 3, "name": "加强三者险", "desc": "保障第三方车辆或人员伤害损失"}, {"code": "PI", "sortNum": 3, "name": "人身财物险", "desc": "保障全车人员意外伤害及随车行李丢失"}, {"code": "SLDW", "sortNum": 3, "name": "超级碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失"}, {"code": "ULM", "sortNum": 4, "name": "不限里程", "desc": "租期内没有公里数限制。\n"}, {"code": "FRFB", "sortNum": 4, "name": "满油取还", "desc": "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"}], "naked": false, "equipments": [], "priceInfoList": [{"localCarPrice": 294.7, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "取车时间前可免费取消;取车时间后取消将收取全部租金作为违约金"}, "chargeList": [{"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"dueAmount": 0, "currency": "CNY", "isIncludedInRate": false, "code": "", "payMode": 2, "desc": "", "netAmount": 0}], "isContainOnewayFee": false, "packageId": 109284, "exchangeRate": 7.26395, "promotionInfo": {"deductionAmount": 400}, "currentPoaPrice": 0, "pkgSellingRuleId": 109284, "currentDailyPrice": 306, "currentTotalPrice": 2141, "vendorPromotionList": [], "localTotalPrice": 294.7, "currentOnewayfee": 0, "payMode": 2, "productId": "4109284", "mileInfo": {"name": "不限里程", "desc": "租期内没有公里数限制。\n", "isLimited": false}, "currentCurrencyCode": "CNY", "ageRestriction": {"maxDriverAge": 80, "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "minDriverAge": 20, "youngDriverAge": 25, "description": "驾驶员年龄要求：20-80周岁", "youngDriverExtraFee": {"currentPrice": 211, "localCurrencyCode": "USD", "localPrice": 29, "feeType": 1}, "youngDriverAgeDesc": "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "oldDriverExtraFee": {"localPrice": 0, "currentPrice": 0}}, "confirmInfo": {"confirmTitle": "立即确认", "confirmDesc": "预订此车型后可快速确认订单。\n", "confirmTime": 0, "confirmRightNow": true}, "localCurrencyCode": "USD", "localPrepaidPrice": 294.7, "currentPrepaidPrice": 2141, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此车型后可快速确认订单。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "2024-07-18 10:00前可免费取消", "labelCode": "3563"}, {"category": 2, "sortNum": 56, "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 60, "code": "2", "title": "24h营业", "colorCode": "2", "type": 1, "description": "门店24小时营业。", "labelCode": "3491"}, {"category": 2, "sortNum": 66, "code": "2", "title": "含加强三者险", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含加强三者险", "labelCode": "3555"}, {"category": 2, "sortNum": 80, "title": "满油取还", "colorCode": "2", "type": 1, "description": "取车时油量是满的，还车时也需保证是满的。若还车时未满，租车公司将会收取差额的油费以及服务费。建议保留加油的单据以及取还车时显示的油量表照片以备用。", "labelCode": "3977"}, {"category": 2, "sortNum": 85, "code": "2", "title": "不限里程", "colorCode": "2", "type": 1, "description": "租期内没有公里数限制。", "labelCode": "3562"}, {"category": 2, "sortNum": 95, "code": "2", "title": "电子提车凭证", "colorCode": "2", "type": 1, "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "labelCode": "3552"}, {"category": 3, "sortNum": 0, "code": "30", "title": "券减¥57/天", "colorCode": "3", "type": 1, "description": "券减¥57/天,可享立减￥57/天（7天共减￥400）的优惠。", "labelCode": "3949"}], "vcExtendRequest": {"responseReturnLocationId": "", "vendorVehicleId": "7NVUR2LD0U39952-6301", "responsePickUpLocationId": ""}, "packageType": 0, "currentCarPrice": 2141, "creditCardInfo": {"maxDeposit": 300, "minDeposit": 200, "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "Discover", "type": "DV"}, {"name": "Japanese Credit Bureau Credit Card", "type": "JC"}], "depositCurrencyCode": "USD"}, "localDailyPrice": 42.1, "insuranceDetails": [{"name": "加强三者险", "code": "ALI"}, {"name": "人身财物险", "code": "PI"}, {"code": "SLDW", "currencyCode": "USD", "minExcess": 0, "name": "超级碰撞盗抢保障", "maxExcess": 0}], "localPoaPrice": 0, "localOnewayfee": 0}, {"localCarPrice": 310, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": -1, "cancelDescription": "取车时间前可免费取消;取车时间后可免费取消"}, "chargeList": [{"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"dueAmount": 0, "currency": "USD", "isIncludedInRate": false, "code": "", "payMode": 1, "desc": "", "netAmount": 0}], "isContainOnewayFee": true, "packageId": 129824, "exchangeRate": 7.26395, "promotionInfo": {}, "currentPoaPrice": 2252, "pkgSellingRuleId": 129824, "currentDailyPrice": 322, "currentTotalPrice": 2252, "vendorPromotionList": [], "localTotalPrice": 310, "currentOnewayfee": 0, "payMode": 1, "productId": "4129824", "mileInfo": {"name": "不限里程", "desc": "租期内没有公里数限制。\n", "isLimited": false}, "currentCurrencyCode": "CNY", "ageRestriction": {"maxDriverAge": 80, "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "minDriverAge": 20, "youngDriverAge": 25, "description": "驾驶员年龄要求：20-80周岁", "youngDriverExtraFee": {"currentPrice": 211, "localCurrencyCode": "USD", "localPrice": 29, "feeType": 1}, "youngDriverAgeDesc": "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "oldDriverExtraFee": {"localPrice": 0, "currentPrice": 0}}, "confirmInfo": {"confirmTitle": "立即确认", "confirmDesc": "预订此车型后可快速确认订单。\n", "confirmTime": 0, "confirmRightNow": true}, "localCurrencyCode": "USD", "localPrepaidPrice": 0, "currentPrepaidPrice": 0, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此车型后可快速确认订单。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "2024-07-18 10:00前可免费取消", "labelCode": "3563"}, {"category": 2, "sortNum": 56, "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 60, "code": "2", "title": "24h营业", "colorCode": "2", "type": 1, "description": "门店24小时营业。", "labelCode": "3491"}, {"category": 2, "sortNum": 66, "code": "2", "title": "含加强三者险", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含加强三者险", "labelCode": "3555"}, {"category": 2, "sortNum": 80, "title": "满油取还", "colorCode": "2", "type": 1, "description": "取车时油量是满的，还车时也需保证是满的。若还车时未满，租车公司将会收取差额的油费以及服务费。建议保留加油的单据以及取还车时显示的油量表照片以备用。", "labelCode": "3977"}, {"category": 2, "sortNum": 85, "code": "2", "title": "不限里程", "colorCode": "2", "type": 1, "description": "租期内没有公里数限制。", "labelCode": "3562"}, {"category": 2, "sortNum": 95, "code": "2", "title": "电子提车凭证", "colorCode": "2", "type": 1, "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "labelCode": "3552"}], "vcExtendRequest": {"responseReturnLocationId": "", "vendorVehicleId": "Z23YR90Z7239917-6301", "responsePickUpLocationId": ""}, "packageType": 0, "currentCarPrice": 2252, "creditCardInfo": {"maxDeposit": 300, "minDeposit": 200, "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "Discover", "type": "DV"}, {"name": "Japanese Credit Bureau Credit Card", "type": "JC"}], "depositCurrencyCode": "USD"}, "localDailyPrice": 44.29, "insuranceDetails": [{"name": "加强三者险", "code": "ALI"}, {"name": "人身财物险", "code": "PI"}, {"code": "SLDW", "currencyCode": "USD", "minExcess": 0, "name": "超级碰撞盗抢保障", "maxExcess": 0}], "localPoaPrice": 310, "localOnewayfee": 0}]}], "briefInsuranceItems": [{"code": "SLDW", "isFromCtrip": false, "name": "超级碰撞盗抢保障", "insuranceDetail": [{"coverageWithoutPlatformInsuranceV2": "0起赔额", "coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 129824, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}, {"coverageWithoutPlatformInsuranceV2": "0起赔额", "coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 109284, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}]}, {"code": "ALI", "isFromCtrip": false, "name": "加强三者险", "insuranceDetail": [{"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 129824, "coverageShortDesc": ""}, {"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 109284, "coverageShortDesc": ""}]}, {"code": "PI", "isFromCtrip": false, "name": "人身财物险", "insuranceDetail": [{"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 129824, "coverageShortDesc": ""}, {"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 109284, "coverageShortDesc": ""}]}], "combinations": [{"codes": ["Fuel"], "dayPrice": 306, "gapPrice": 0, "title": "满油取还", "totalPrice": 2141, "bomCode": "92021_66375_ALI_FRFB_PI_SLDW_Taxes_Taxes_ULM_0_0", "stepPrice": 0, "hike": false, "currency": "CNY", "payMode": 2, "packageId": 109284}, {"codes": ["MultiDriver", "Fuel"], "dayPrice": 394, "gapPrice": 88, "title": "4名额外驾驶员 + 含一箱燃油", "totalPrice": 2758, "bomCode": "92021_66375_ADD4_ALI_FPO_PI_SLDW_Taxes_Taxes_ULM_0_0", "stepPrice": 88, "hike": false, "currency": "CNY", "payMode": 2, "packageId": 109290}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}]}, {"ctripInsuranceIds": [3], "insPackageId": 261, "insuranceGroup": [{"title": "保自己的车", "code": "1"}, {"title": "保被撞的车和人", "code": "2"}, {"title": "保车内人员财物", "code": "3"}], "insuranceCompareItems": [{"descriptionColorCode": "GREEN", "containsDescription": "（无需垫付）", "description": "0起赔额", "type": "LDW"}, {"contains": true, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": true, "type": "PI"}, {"contains": true, "type": "RAP"}, {"contains": true, "type": "CANCEL"}], "insuranceItems": [{"excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["US$ 0"]}], "note": "true|US$ 0(约¥0)|USD"}, "groupCode": "1", "code": "SLDW", "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由车行承担起赔额以上的费用，起赔额通常较低或为零。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "isInclude": true, "description": "保障车辆碰撞、被盗的损失", "isFromCtrip": false, "name": "超级碰撞盗抢保障", "insuranceDetail": [{"coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 129824, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}, {"coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 109284, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}]}, {"groupCode": "2", "code": "ALI", "unConverageExplain": {"title": "不承保范围"}, "converageExplain": {"title": "承保范围", "content": ["若开车时不小心撞到其它车辆或人员，在起赔额以上保额以内的部分将由保险公司赔付。*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "isInclude": true, "description": "保障第三方车辆或人员伤害损失", "isFromCtrip": false, "name": "加强三者险", "insuranceDetail": [{"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 129824, "coverageShortDesc": ""}, {"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 109284, "coverageShortDesc": ""}]}, {"groupCode": "3", "code": "PI", "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n由未经登记的驾驶人驾驶车辆造成的车辆事故\n个人物品不包括钱币、金块、股票债券等金钱类等价物以及票据合同等文件\n因超速驾驶，酒后驾驶或违反旅行国法律法规而导致的车辆事故\n*理赔时需提供报警凭证，取车时请向工作人员了解详细的保险条款。\n*实际赔付范围与标准以门店合同为准"]}, "converageExplain": {"title": "承保范围", "content": ["保障车内人员在正常使用租赁车辆期间发生的意外伤害、个人财物丢失。"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "isInclude": true, "description": "保障全车人员意外伤害及随车行李丢失", "isFromCtrip": false, "name": "人身财物险", "insuranceDetail": [{"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 129824, "coverageShortDesc": ""}, {"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 109284, "coverageShortDesc": ""}]}, {"description": "保障玻璃轮胎底盘，补偿道路救援费用", "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "productId": 3, "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE.pdf", "insuranceDetail": [{"coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥45/天", "coverageWithoutPlatformInsurance": "国内保险公司提供¥45/天"}], "code": "FULL_COVERAGE", "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "groupCode": "1", "isInclude": true, "name": "安心补充险", "isFromCtrip": true}], "productInfoList": [{"ctripInsurances": [{"localDailyPrice": 45, "localTotalPrice": 315, "currentDailyPrice": 45, "currentTotalPrice": 315, "localCurrencyCode": "CNY", "uniqueCode": "3", "name": "安心补充险", "currentCurrencyCode": "CNY"}], "searchCreditCard": false, "carRentalMustRead": [{"code": "4", "sortNum": 1, "title": "额外驾驶员", "type": 6}, {"code": "4", "sortNum": 2, "title": "营业时间外取还车", "type": 7}, {"code": "4", "sortNum": 3, "title": "提前/延后取还车", "type": 41}, {"code": "4", "sortNum": 4, "title": "费用须知", "type": 40}, {"code": "1", "sortNum": 1, "title": "确认政策", "content": ["预订此车型后可快速确认订单。\n"], "type": 0, "showFree": 1}, {"sortNum": 2, "content": ["取车时间前可免费取消;取车时间后取消将收取全部租金作为违约金"], "code": "1", "title": "取消政策", "table": [{"code": "FreeCancel", "title": "取消政策", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "当地时间2024-07-18 10:00前可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "当地时间2024-07-18 10:00前可免费取消", "style": "6"}]}, "showFree": true, "description": "注意：均为当地时间", "type": 300, "subTitle": "当地时间2024-07-18 10:00前可免费取消", "items": [{"showFree": true, "title": "2024-07-18 10:00前", "subTitle": "取车时间前", "description": "可免费取消"}, {"showFree": false, "title": "2024-07-18 10:00后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金"}]}], "showFree": 1, "type": 1}, {"code": "1", "sortNum": 3, "title": "押金说明", "content": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 2, "table": [{"showFree": false, "title": "押金", "positiveDesc": "可退", "description": "到店刷取押金预授权，还车后30-60天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180）"}]}]}]}, {"code": "1", "sortNum": 4, "title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3}, {"code": "1", "sortNum": 5, "title": "能源政策", "content": ["含一箱燃油", "取车时送一箱燃油，还车时可任意油量还车。"], "type": 4}, {"code": "1", "sortNum": 6, "title": "年龄要求", "content": ["驾驶员年龄要求：20-80周岁", "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3}, {"code": "2", "sortNum": 2, "title": "能源政策", "content": ["含一箱燃油", "取车时送一箱燃油，还车时可任意油量还车。"], "type": 4}, {"code": "2", "sortNum": 3, "title": "额外驾驶员", "content": ["4名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6}, {"code": "2", "sortNum": 4, "title": "年龄要求", "content": ["驾驶员年龄要求：20-80周岁", "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5}, {"code": "2", "sortNum": 5, "title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7}, {"code": "2", "sortNum": 6, "title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16}, {"code": "2", "sortNum": 7, "title": "门店提示", "content": ["到店选购额外服务后将会收取9.5%的税费（Tax），详见租车合同费用明细。"], "type": 47}], "title": "费用须知", "content": [], "type": 40, "sortNum": 1}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17}, {"code": "2", "sortNum": 2, "title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18}, {"code": "2", "sortNum": 3, "title": "提前还车", "content": ["提前还车可能需要支付额外费用（如提前还车服务费、缩短租期里程限制改变导致的超里程费用、车行可能按照当日门市价重新计算租金等），具体费用请联系门店确认。由于您自身行为(例如违规驾驶、违反租车合同条款等)导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10}, {"code": "2", "sortNum": 4, "title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11}], "title": "提前/延后取还车", "content": [], "type": 41, "sortNum": 2}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "签合同前核对金额", "content": ["办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。"], "type": 59}, {"code": "2", "sortNum": 2, "title": "验车注意事项", "content": ["验车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身，油量里程，车损特写等），以免产生售后纠纷。"], "type": 60}], "title": "取车描述", "content": [], "type": 58, "sortNum": 3}, {"code": "2", "subObject": [{"code": "2", "sortNum": 2, "title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22}], "title": "租车保障", "content": [], "type": 20, "sortNum": 4}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24}, {"code": "2", "sortNum": 2, "title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25}], "title": "附加服务", "content": [], "type": 23, "sortNum": 5}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "跨境政策", "content": ["跨境至加拿大：多数情况允许车辆跨境至加拿大，跨境费用根据车辆类别、租用时间等情况决定，需提前联系门店确认。请注意，加拿大居民不得驾驶美国车辆进入加拿大。\n跨境至墨西哥：仅允许从德克萨斯州，新墨西哥州，亚利桑那州，加利福尼亚州跨境至墨西哥，且可驾驶范围仅限于美国与墨西哥边境以南 250 英里以内。 跨境需向门店申请并获得授权，请提前联系门店确认。跨境需购买墨西哥保险，该保险将覆盖跨境驾驶期间的第三者责任险、碰撞险以及综合保险，参考费用为每天38-48美元（含税费），具体购买方式请咨询门店。跨境至墨西哥期间，高级道路救援服务（PERS）不生效。请注意，墨西哥居民不得驾驶美国车辆进入墨西哥。"], "type": 27}], "title": "旅行限制", "content": [], "type": 26, "sortNum": 6}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31}, {"code": "2", "sortNum": 2, "title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32}], "title": "取消，未取车和修改", "content": [], "type": 42, "sortNum": 7}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34}, {"code": "2", "sortNum": 2, "title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35}, {"code": "2", "sortNum": 3, "title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36}, {"code": "2", "sortNum": 4, "title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37}, {"code": "2", "sortNum": 5, "title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38}, {"code": "2", "sortNum": 6, "title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39}, {"code": "2", "sortNum": 7, "title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13}], "title": "租车公司重要信息", "content": [], "type": 33, "sortNum": 8}], "bomGroupCode": "92021_66375_ADD4_ALI_FPO_PI_SLDW_Taxes_Taxes_ULM_0_0", "platformInsurance": {"insuranceClauses": "https://pages.c-ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE.pdf", "insurance": {"id": 3, "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localCurrencyCode": "CNY", "localDailyPrice": 45, "typeCode": "FULL_COVERAGE", "custumerTotalPrice": 315, "desc": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢自付额", "localTotalPrice": 315, "code": "MP18021529PK00024376", "days": 7, "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "longDesc": "由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：", "customerCurrencyCode": "CNY", "custumerDailyPrice": 45, "name": "安心补充险"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "version": "OPI1210602907838185472"}, "needFlightNo": false, "searchUnionPay": false, "pickUpMaterials": [{"summaryTitle": "驾驶员需持有本人名下4项材料取车：", "title": "取车要求", "content": ["驾驶员年龄20-80周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "20-80周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}]}, {"subObject": [{"code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国大陆护照", "subTitle": "中国大陆护照原件"}, {"code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国香港护照", "subTitle": "中国香港护照原件"}, {"code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国澳门护照", "subTitle": "中国澳门护照原件"}, {"code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国台湾护照", "subTitle": "中国台湾护照原件"}, {"code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "其他地区护照", "subTitle": "其他地区护照原件"}], "title": "护照原件", "subTitle": "", "type": 0}, {"subObject": [{"type": 5, "subObject": [{"code": "CN", "subObject": [{"code": "CDL,IDL", "sortNum": 2, "title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22}, {"code": "CDL,DLT", "sortNum": 3, "title": "中国大陆驾照原件 + 车行翻译件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 22}, {"code": "CDL,OET", "sortNum": 4, "title": "中国大陆驾照原件 + 英文公证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 22}], "title": "门店支持以下驾照组合(任选其一)", "type": 5}, {"code": "HK", "subObject": [{"code": "HKDL,IDP", "sortNum": 37, "title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "MO", "subObject": [{"code": "MCDL,IDP", "sortNum": 29, "title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "TW", "subObject": [{"code": "TWDL,IDP", "sortNum": 31, "title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "OH", "subObject": [{"code": "ODL,IDP", "sortNum": 11, "title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "若您只能使用美加驾照租车，建议前往Trip.com下单。"}]}], "type": 10}], "title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1}, {"subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "type": 6, "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}], "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png"]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 8, "table": [{"showFree": false, "title": "押金", "positiveDesc": "可退", "description": "到店刷取押金预授权，还车后30-60天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180）"}]}]}]}], "title": "国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 2, "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，平台为您提供"], "type": 3}], "productCode": "4", "packageItems": [{"code": "Taxes", "sortNum": 2, "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用"}, {"code": "Taxes", "sortNum": 2, "name": "税费（含：机场税，客户设施费，旅游税，销售税）", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。"}, {"code": "ALI", "sortNum": 3, "name": "加强三者险", "desc": "保障第三方车辆或人员伤害损失"}, {"code": "PI", "sortNum": 3, "name": "人身财物险", "desc": "保障全车人员意外伤害及随车行李丢失"}, {"code": "SLDW", "sortNum": 3, "name": "超级碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失"}, {"code": "ULM", "sortNum": 4, "name": "不限里程", "desc": "租期内没有公里数限制。\n"}, {"code": "ADD4", "sortNum": 4, "name": "4名额外驾驶员", "desc": "4名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"}, {"code": "FPO", "sortNum": 4, "name": "含一箱燃油", "desc": "取车时送一箱燃油，还车时可任意油量还车。"}], "naked": false, "equipments": [], "priceInfoList": [{"localCarPrice": 379.7, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "取车时间前可免费取消;取车时间后取消将收取全部租金作为违约金"}, "chargeList": [{"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"dueAmount": 0, "currency": "CNY", "isIncludedInRate": false, "code": "", "payMode": 2, "desc": "", "netAmount": 0}], "packageId": 109290, "exchangeRate": 7.26395, "promotionInfo": {"deductionAmount": 400}, "pkgSellingRuleId": 109290, "currentPoaPrice": 0, "currentDailyPrice": 439, "currentTotalPrice": 3073, "vendorPromotionList": [], "localTotalPrice": 379.7, "currentOnewayfee": 0, "payMode": 2, "productId": "4109290", "mileInfo": {"name": "不限里程", "desc": "租期内没有公里数限制。\n", "isLimited": false}, "currentCurrencyCode": "CNY", "ageRestriction": {"maxDriverAge": 80, "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "minDriverAge": 20, "youngDriverAge": 25, "description": "驾驶员年龄要求：20-80周岁", "youngDriverExtraFee": {"currentPrice": 211, "localCurrencyCode": "USD", "localPrice": 29, "feeType": 1}, "youngDriverAgeDesc": "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "oldDriverExtraFee": {"localPrice": 0, "currentPrice": 0}}, "confirmInfo": {"confirmTitle": "立即确认", "confirmDesc": "预订此车型后可快速确认订单。\n", "confirmTime": 0, "confirmRightNow": true}, "localCurrencyCode": "USD", "localPrepaidPrice": 379.7, "currentPrepaidPrice": 3073, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此车型后可快速确认订单。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "2024-07-18 10:00前可免费取消", "labelCode": "3563"}, {"category": 2, "sortNum": 56, "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 60, "code": "2", "title": "24h营业", "colorCode": "2", "type": 1, "description": "门店24小时营业。", "labelCode": "3491"}, {"category": 2, "sortNum": 66, "code": "2", "title": "含加强三者险", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含加强三者险", "labelCode": "3555"}, {"category": 2, "sortNum": 85, "code": "2", "title": "不限里程", "colorCode": "2", "type": 1, "description": "租期内没有公里数限制。", "labelCode": "3562"}, {"category": 2, "sortNum": 95, "code": "2", "title": "电子提车凭证", "colorCode": "2", "type": 1, "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "labelCode": "3552"}, {"category": 3, "sortNum": 0, "code": "30", "title": "券减¥57/天", "colorCode": "3", "type": 1, "description": "券减¥57/天,可享立减￥57/天（7天共减￥400）的优惠。", "labelCode": "3949"}], "vcExtendRequest": {"responseReturnLocationId": "", "vendorVehicleId": "VPW3FZ5OVI39944-6301", "responsePickUpLocationId": ""}, "packageType": 0, "currentCarPrice": 2758, "creditCardInfo": {"maxDeposit": 300, "minDeposit": 200, "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "Discover", "type": "DV"}, {"name": "Japanese Credit Bureau Credit Card", "type": "JC"}], "depositCurrencyCode": "USD"}, "localDailyPrice": 54.24, "insuranceDetails": [{"name": "加强三者险", "code": "ALI"}, {"name": "人身财物险", "code": "PI"}, {"code": "SLDW", "currencyCode": "USD", "minExcess": 0, "name": "超级碰撞盗抢保障", "maxExcess": 0}], "localPoaPrice": 0, "localOnewayfee": 0}]}, {"ctripInsurances": [{"localDailyPrice": 45, "localTotalPrice": 315, "currentDailyPrice": 45, "currentTotalPrice": 315, "localCurrencyCode": "CNY", "uniqueCode": "3", "name": "安心补充险", "currentCurrencyCode": "CNY"}], "searchCreditCard": false, "carRentalMustRead": [{"code": "4", "sortNum": 1, "title": "额外驾驶员", "type": 6}, {"code": "4", "sortNum": 2, "title": "营业时间外取还车", "type": 7}, {"code": "4", "sortNum": 3, "title": "提前/延后取还车", "type": 41}, {"code": "4", "sortNum": 4, "title": "费用须知", "type": 40}, {"code": "1", "sortNum": 1, "title": "确认政策", "content": ["预订此车型后可快速确认订单。\n"], "type": 0, "showFree": 1}, {"sortNum": 2, "content": ["取车时间前可免费取消;取车时间后取消将收取全部租金作为违约金"], "code": "1", "title": "取消政策", "table": [{"code": "FreeCancel", "title": "取消政策", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "当地时间2024-07-18 10:00前可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "当地时间2024-07-18 10:00前可免费取消", "style": "6"}]}, "showFree": true, "description": "注意：均为当地时间", "type": 300, "subTitle": "当地时间2024-07-18 10:00前可免费取消", "items": [{"showFree": true, "title": "2024-07-18 10:00前", "subTitle": "取车时间前", "description": "可免费取消"}, {"showFree": false, "title": "2024-07-18 10:00后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金"}]}], "showFree": 1, "type": 1}, {"code": "1", "sortNum": 3, "title": "押金说明", "content": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 2, "table": [{"showFree": false, "title": "押金", "positiveDesc": "可退", "description": "到店刷取押金预授权，还车后30-60天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180）"}]}]}]}, {"code": "1", "sortNum": 4, "title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3}, {"code": "1", "sortNum": 5, "title": "能源政策", "content": ["满油取还", "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"], "type": 4}, {"code": "1", "sortNum": 6, "title": "年龄要求", "content": ["驾驶员年龄要求：20-80周岁", "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3}, {"code": "2", "sortNum": 2, "title": "能源政策", "content": ["满油取还", "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"], "type": 4}, {"code": "2", "sortNum": 3, "title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6}, {"code": "2", "sortNum": 4, "title": "年龄要求", "content": ["驾驶员年龄要求：20-80周岁", "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5}, {"code": "2", "sortNum": 5, "title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7}, {"code": "2", "sortNum": 6, "title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16}, {"code": "2", "sortNum": 7, "title": "门店提示", "content": ["到店选购额外服务后将会收取9.5%的税费（Tax），详见租车合同费用明细。"], "type": 47}], "title": "费用须知", "content": [], "type": 40, "sortNum": 1}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17}, {"code": "2", "sortNum": 2, "title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18}, {"code": "2", "sortNum": 3, "title": "提前还车", "content": ["提前还车可能需要支付额外费用（如提前还车服务费、缩短租期里程限制改变导致的超里程费用、车行可能按照当日门市价重新计算租金等），具体费用请联系门店确认。由于您自身行为(例如违规驾驶、违反租车合同条款等)导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10}, {"code": "2", "sortNum": 4, "title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11}], "title": "提前/延后取还车", "content": [], "type": 41, "sortNum": 2}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "签合同前核对金额", "content": ["办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。"], "type": 59}, {"code": "2", "sortNum": 2, "title": "验车注意事项", "content": ["验车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身，油量里程，车损特写等），以免产生售后纠纷。"], "type": 60}], "title": "取车描述", "content": [], "type": 58, "sortNum": 3}, {"code": "2", "subObject": [{"code": "2", "sortNum": 2, "title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22}], "title": "租车保障", "content": [], "type": 20, "sortNum": 4}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24}, {"code": "2", "sortNum": 2, "title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25}], "title": "附加服务", "content": [], "type": 23, "sortNum": 5}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "跨境政策", "content": ["跨境至加拿大：多数情况允许车辆跨境至加拿大，跨境费用根据车辆类别、租用时间等情况决定，需提前联系门店确认。请注意，加拿大居民不得驾驶美国车辆进入加拿大。\n跨境至墨西哥：仅允许从德克萨斯州，新墨西哥州，亚利桑那州，加利福尼亚州跨境至墨西哥，且可驾驶范围仅限于美国与墨西哥边境以南 250 英里以内。 跨境需向门店申请并获得授权，请提前联系门店确认。跨境需购买墨西哥保险，该保险将覆盖跨境驾驶期间的第三者责任险、碰撞险以及综合保险，参考费用为每天38-48美元（含税费），具体购买方式请咨询门店。跨境至墨西哥期间，高级道路救援服务（PERS）不生效。请注意，墨西哥居民不得驾驶美国车辆进入墨西哥。"], "type": 27}], "title": "旅行限制", "content": [], "type": 26, "sortNum": 6}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31}, {"code": "2", "sortNum": 2, "title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32}], "title": "取消，未取车和修改", "content": [], "type": 42, "sortNum": 7}, {"code": "2", "subObject": [{"code": "2", "sortNum": 1, "title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34}, {"code": "2", "sortNum": 2, "title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35}, {"code": "2", "sortNum": 3, "title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36}, {"code": "2", "sortNum": 4, "title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37}, {"code": "2", "sortNum": 5, "title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38}, {"code": "2", "sortNum": 6, "title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39}, {"code": "2", "sortNum": 7, "title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13}], "title": "租车公司重要信息", "content": [], "type": 33, "sortNum": 8}], "bomGroupCode": "92021_66375_ALI_FRFB_PI_SLDW_Taxes_Taxes_ULM_0_0", "platformInsurance": {"insuranceClauses": "https://pages.c-ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE.pdf", "insurance": {"id": 3, "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localCurrencyCode": "CNY", "localDailyPrice": 45, "typeCode": "FULL_COVERAGE", "custumerTotalPrice": 315, "desc": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢自付额", "localTotalPrice": 315, "code": "MP18021529PK00024376", "days": 7, "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "longDesc": "由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：", "customerCurrencyCode": "CNY", "custumerDailyPrice": 45, "name": "安心补充险"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "version": "OPI1210602907838185472"}, "needFlightNo": false, "searchUnionPay": false, "pickUpMaterials": [{"summaryTitle": "驾驶员需持有本人名下4项材料取车：", "title": "取车要求", "content": ["驾驶员年龄20-80周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "20-80周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}]}, {"subObject": [{"code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国大陆护照", "subTitle": "中国大陆护照原件"}, {"code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国香港护照", "subTitle": "中国香港护照原件"}, {"code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国澳门护照", "subTitle": "中国澳门护照原件"}, {"code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "中国台湾护照", "subTitle": "中国台湾护照原件"}, {"code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}], "title": "其他地区护照", "subTitle": "其他地区护照原件"}], "title": "护照原件", "subTitle": "", "type": 0}, {"subObject": [{"type": 5, "subObject": [{"code": "CN", "subObject": [{"code": "CDL,IDL", "sortNum": 2, "title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22}, {"code": "CDL,DLT", "sortNum": 3, "title": "中国大陆驾照原件 + 车行翻译件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 22}, {"code": "CDL,OET", "sortNum": 4, "title": "中国大陆驾照原件 + 英文公证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 22}], "title": "门店支持以下驾照组合(任选其一)", "type": 5}, {"code": "HK", "subObject": [{"code": "HKDL,IDP", "sortNum": 37, "title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "MO", "subObject": [{"code": "MCDL,IDP", "sortNum": 29, "title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "TW", "subObject": [{"code": "TWDL,IDP", "sortNum": 31, "title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}, {"code": "OH", "subObject": [{"code": "ODL,IDP", "sortNum": 11, "title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22}], "title": "门店支持以下驾照组合", "type": 5}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "若您只能使用美加驾照租车，建议前往Trip.com下单。"}]}], "type": 10}], "title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1}, {"subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "type": 6, "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}], "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/discover.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/jcb.png"]}, {"title": "押金说明", "content": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 8, "table": [{"showFree": false, "title": "押金", "positiveDesc": "可退", "description": "到店刷取押金预授权，还车后30-60天内退还", "contents": [{"stringObjs": [{"content": "押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180）"}]}]}]}], "title": "国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。"], "summaryContent": ["押金约租车费用+US$ 200-US$ 300（约¥1,453-¥2,180），到店刷取押金预授权，还车后30-60天内退还"], "type": 2, "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，平台为您提供"], "type": 3}], "productCode": "4", "packageItems": [{"code": "Taxes", "sortNum": 2, "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用"}, {"code": "Taxes", "sortNum": 2, "name": "税费（含：机场税，客户设施费，旅游税，销售税）", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。"}, {"code": "ALI", "sortNum": 3, "name": "加强三者险", "desc": "保障第三方车辆或人员伤害损失"}, {"code": "PI", "sortNum": 3, "name": "人身财物险", "desc": "保障全车人员意外伤害及随车行李丢失"}, {"code": "SLDW", "sortNum": 3, "name": "超级碰撞盗抢保障", "desc": "保障车辆碰撞、被盗的损失"}, {"code": "ULM", "sortNum": 4, "name": "不限里程", "desc": "租期内没有公里数限制。\n"}, {"code": "FRFB", "sortNum": 4, "name": "满油取还", "desc": "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费，具体金额以取车时门店确认为准。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。"}], "naked": false, "equipments": [], "priceInfoList": [{"localCarPrice": 294.7, "cancelRule": {"isTotalLoss": true, "isFreeCancel": true, "isFreeCancelNow": true, "hours": 0, "cancelDescription": "取车时间前可免费取消;取车时间后取消将收取全部租金作为违约金"}, "chargeList": [{"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 2, "dueAmount": 0, "currency": "CNY", "isIncludedInRate": true}, {"dueAmount": 0, "currency": "CNY", "isIncludedInRate": false, "code": "", "payMode": 2, "desc": "", "netAmount": 0}], "packageId": 109284, "exchangeRate": 7.26395, "promotionInfo": {"deductionAmount": 400}, "pkgSellingRuleId": 109284, "currentPoaPrice": 0, "currentDailyPrice": 351, "currentTotalPrice": 2456, "vendorPromotionList": [], "localTotalPrice": 294.7, "currentOnewayfee": 0, "payMode": 2, "productId": "4109284", "mileInfo": {"name": "不限里程", "desc": "租期内没有公里数限制。\n", "isLimited": false}, "currentCurrencyCode": "CNY", "ageRestriction": {"maxDriverAge": 80, "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "minDriverAge": 20, "youngDriverAge": 25, "description": "驾驶员年龄要求：20-80周岁", "youngDriverExtraFee": {"currentPrice": 211, "localCurrencyCode": "USD", "localPrice": 29, "feeType": 1}, "youngDriverAgeDesc": "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "oldDriverExtraFee": {"localPrice": 0, "currentPrice": 0}}, "confirmInfo": {"confirmTitle": "立即确认", "confirmDesc": "预订此车型后可快速确认订单。\n", "confirmTime": 0, "confirmRightNow": true}, "localCurrencyCode": "USD", "localPrepaidPrice": 294.7, "currentPrepaidPrice": 2456, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此车型后可快速确认订单。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "2024-07-18 10:00前可免费取消", "labelCode": "3563"}, {"category": 2, "sortNum": 56, "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 60, "code": "2", "title": "24h营业", "colorCode": "2", "type": 1, "description": "门店24小时营业。", "labelCode": "3491"}, {"category": 2, "sortNum": 66, "code": "2", "title": "含加强三者险", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含加强三者险", "labelCode": "3555"}, {"category": 2, "sortNum": 80, "title": "满油取还", "colorCode": "2", "type": 1, "description": "取车时油量是满的，还车时也需保证是满的。若还车时未满，租车公司将会收取差额的油费以及服务费。建议保留加油的单据以及取还车时显示的油量表照片以备用。", "labelCode": "3977"}, {"category": 2, "sortNum": 85, "code": "2", "title": "不限里程", "colorCode": "2", "type": 1, "description": "租期内没有公里数限制。", "labelCode": "3562"}, {"category": 2, "sortNum": 95, "code": "2", "title": "电子提车凭证", "colorCode": "2", "type": 1, "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "labelCode": "3552"}, {"category": 3, "sortNum": 0, "code": "30", "title": "券减¥57/天", "colorCode": "3", "type": 1, "description": "券减¥57/天,可享立减￥57/天（7天共减￥400）的优惠。", "labelCode": "3949"}], "vcExtendRequest": {"responseReturnLocationId": "", "vendorVehicleId": "7NVUR2LD0U39952-6301", "responsePickUpLocationId": ""}, "packageType": 0, "currentCarPrice": 2141, "creditCardInfo": {"maxDeposit": 300, "minDeposit": 200, "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "Discover", "type": "DV"}, {"name": "Japanese Credit Bureau Credit Card", "type": "JC"}], "depositCurrencyCode": "USD"}, "localDailyPrice": 42.1, "insuranceDetails": [{"name": "加强三者险", "code": "ALI"}, {"name": "人身财物险", "code": "PI"}, {"code": "SLDW", "currencyCode": "USD", "minExcess": 0, "name": "超级碰撞盗抢保障", "maxExcess": 0}], "localPoaPrice": 0, "localOnewayfee": 0}, {"localCarPrice": 310, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": -1, "cancelDescription": "取车时间前可免费取消;取车时间后可免费取消"}, "chargeList": [{"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"code": "", "netAmount": 0, "payMode": 1, "dueAmount": 0, "currency": "USD", "isIncludedInRate": true}, {"dueAmount": 0, "currency": "USD", "isIncludedInRate": false, "code": "", "payMode": 1, "desc": "", "netAmount": 0}], "packageId": 129824, "exchangeRate": 7.26395, "promotionInfo": {}, "pkgSellingRuleId": 129824, "currentPoaPrice": 2252, "currentDailyPrice": 367, "currentTotalPrice": 2567, "vendorPromotionList": [], "localTotalPrice": 310, "currentOnewayfee": 0, "payMode": 1, "productId": "4129824", "mileInfo": {"name": "不限里程", "desc": "租期内没有公里数限制。\n", "isLimited": false}, "currentCurrencyCode": "CNY", "ageRestriction": {"maxDriverAge": 80, "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "minDriverAge": 20, "youngDriverAge": 25, "description": "驾驶员年龄要求：20-80周岁", "youngDriverExtraFee": {"currentPrice": 211, "localCurrencyCode": "USD", "localPrice": 29, "feeType": 1}, "youngDriverAgeDesc": "租车公司对20-25周岁将收取“青年驾驶费”；参考价格：USD29（约¥211）/天，不包含税，需在线上或门店支付。", "oldDriverExtraFee": {"localPrice": 0, "currentPrice": 0}}, "confirmInfo": {"confirmTitle": "立即确认", "confirmDesc": "预订此车型后可快速确认订单。\n", "confirmTime": 0, "confirmRightNow": true}, "localCurrencyCode": "USD", "localPrepaidPrice": 0, "currentPrepaidPrice": 315, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此车型后可快速确认订单。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "2024-07-18 10:00前可免费取消", "labelCode": "3563"}, {"category": 2, "sortNum": 56, "code": "2", "title": "随时可订", "colorCode": "2", "type": 1, "description": "该车型随时可预订。", "labelCode": "3689"}, {"category": 2, "sortNum": 60, "code": "2", "title": "24h营业", "colorCode": "2", "type": 1, "description": "门店24小时营业。", "labelCode": "3491"}, {"category": 2, "sortNum": 66, "code": "2", "title": "含加强三者险", "colorCode": "2", "type": 1, "description": "该产品的保障服务套餐中含加强三者险", "labelCode": "3555"}, {"category": 2, "sortNum": 80, "title": "满油取还", "colorCode": "2", "type": 1, "description": "取车时油量是满的，还车时也需保证是满的。若还车时未满，租车公司将会收取差额的油费以及服务费。建议保留加油的单据以及取还车时显示的油量表照片以备用。", "labelCode": "3977"}, {"category": 2, "sortNum": 85, "code": "2", "title": "不限里程", "colorCode": "2", "type": 1, "description": "租期内没有公里数限制。", "labelCode": "3562"}, {"category": 2, "sortNum": 95, "code": "2", "title": "电子提车凭证", "colorCode": "2", "type": 1, "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "labelCode": "3552"}], "vcExtendRequest": {"responseReturnLocationId": "", "vendorVehicleId": "Z23YR90Z7239917-6301", "responsePickUpLocationId": ""}, "packageType": 0, "currentCarPrice": 2252, "creditCardInfo": {"maxDeposit": 300, "minDeposit": 200, "cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}, {"name": "Discover", "type": "DV"}, {"name": "Japanese Credit Bureau Credit Card", "type": "JC"}], "depositCurrencyCode": "USD"}, "localDailyPrice": 44.29, "insuranceDetails": [{"name": "加强三者险", "code": "ALI"}, {"name": "人身财物险", "code": "PI"}, {"code": "SLDW", "currencyCode": "USD", "minExcess": 0, "name": "超级碰撞盗抢保障", "maxExcess": 0}], "localPoaPrice": 310, "localOnewayfee": 0}]}], "briefInsuranceItems": [{"code": "SLDW", "isFromCtrip": false, "name": "超级碰撞盗抢保障", "insuranceDetail": [{"coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 129824, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}, {"coverageLongDesc": "", "currencyCode": "USD", "coverageShortDesc": "", "packageId": 109284, "maxExcess": 0, "excessShortDesc": "起赔额US$ 0", "minExcess": 0, "excessLongDesc": ""}]}, {"code": "ALI", "isFromCtrip": false, "name": "加强三者险", "insuranceDetail": [{"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 129824, "coverageShortDesc": ""}, {"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 109284, "coverageShortDesc": ""}]}, {"code": "PI", "isFromCtrip": false, "name": "人身财物险", "insuranceDetail": [{"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 129824, "coverageShortDesc": ""}, {"coverageLongDesc": "", "excessShortDesc": "", "excessLongDesc": "", "packageId": 109284, "coverageShortDesc": ""}]}, {"description": "保障玻璃轮胎底盘，补偿道路救援费用", "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "productId": 3, "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/carpolicy/%E4%BF%9D%E9%99%A9%E6%9D%A1%E6%AC%BE.pdf", "insuranceDetail": [{"coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥45/天", "coverageWithoutPlatformInsurance": "国内保险公司提供¥45/天"}], "code": "FULL_COVERAGE", "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "groupCode": "1", "isInclude": true, "name": "安心补充险", "isFromCtrip": true}], "combinations": [{"codes": ["Fuel"], "dayPrice": 351, "gapPrice": 0, "title": "满油取还", "totalPrice": 2456, "bomCode": "92021_66375_ALI_FRFB_PI_SLDW_Taxes_Taxes_ULM_0_0", "stepPrice": 0, "hike": false, "currency": "CNY", "payMode": 2, "packageId": 109284}, {"codes": ["MultiDriver", "Fuel"], "dayPrice": 439, "gapPrice": 88, "title": "4名额外驾驶员 + 含一箱燃油", "totalPrice": 3073, "bomCode": "92021_66375_ADD4_ALI_FPO_PI_SLDW_Taxes_Taxes_ULM_0_0", "stepPrice": 88, "hike": false, "currency": "CNY", "payMode": 2, "packageId": 109290}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "提供理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核（1-3个工作日内反馈）", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款（审核通过后3个工作日内）", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}]}]}], "locationPrompt": "美国地区偶发砸窗盗窃，道路救援费用较高，建议升级含安心补充险的套餐，保障范围更广，比在门店购买更便宜。", "vendorInfo": {"platformName": "Thrifty", "vendorCode": "SD0004", "bizVendorCode": "14403", "platformCode": "", "vendorName": "Thrifty", "haveCoupon": true, "isBroker": false, "vendorTag": {"title": "全球连锁", "sortNum": 0}, "vendorImageUrl": "https://dimg04.c-ctrip.com/images/20p5112000ejfqlt702F3.png"}, "flightDelayRule": {"title": "航班延误保留政策", "rules": [{"title": "如您遇到航班延误需要延迟取车或取消订单，请于取车时间前及时联系我们与供应商沟通。因航班延误的特殊性、且供应商政策不同，可能无法为您保留车辆或无法免费取消订单，具体条款请以供应商政策为准，敬请谅解。"}, {"title": "建议您填写航班号，如遇延误请携带机票前往取车"}], "description": "航班延误保留政策"}, "licenceCountryPolicy": {"summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "持非中国（含港澳台）驾照无法预订。建议更换驾驶员或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17}], "subObject": [{"code": "CN", "title": "中国大陆驾照", "summaryContent": ["您需携带中国大陆驾照原件和英文公证件（或驾照国际翻译认证件和中国大陆驾照原件、中国大陆驾照原件和车行翻译件）才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "optimalType": "1"}, {"code": "HK", "title": "中国香港驾照", "summaryContent": ["您需携带国际驾照和香港驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "optimalType": "1"}, {"code": "MO", "title": "中国澳门驾照", "summaryContent": ["您需携带国际驾照和澳门驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "optimalType": "1"}, {"code": "TW", "title": "中国台湾驾照", "summaryContent": ["您需携带国际驾照和台湾驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "optimalType": "1"}], "title": "驾照", "type": 15}, "osdCompareTitle": ["车辆碰撞及盗抢", "玻璃轮胎底盘保障", "第三者保障", "人身财物险", "道路救援补偿", "旅行取消"], "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}, "isSoldOut": false, "resBodySize": 233603, "platformInsuranceExtra": {"encourageTitle": "Extra Protection for Your Rental", "claimProcedure": [{"type": 1, "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"]}, {"type": 2, "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"]}], "encourageDesc": "Your package does not cover you for all losses and damages. Protect yourself from unexpected damage costs with RentalCover.com's protection"}, "packageInfos": [{"description": "不推荐。当地法律强制要求租车需有三者险，发生事故可能产生高额赔偿，门店也会要求购买保险。", "noticeDescTitle": "不推荐", "gapPrice": 0, "packageType": 0, "isDefault": true, "insPackageId": 3, "packageName": "高级套餐", "lowestDailyPrice": 262, "insuranceNames": ["超级碰撞盗抢保障"], "defaultBomCode": "92021_66375_FRFB_SLDW_Taxes_Taxes_ULM_0_0", "stepPrice": 0, "defaultPackageId": 109285, "naked": false, "guaranteeDegree": 2, "currencyCode": "CNY"}, {"description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失", "isBasic": 1, "gapPrice": 44, "packageType": 0, "isDefault": false, "insPackageId": 5, "packageName": "综合套餐", "lowestDailyPrice": 306, "insuranceNames": ["加强三者险", "人身财物险", "超级碰撞盗抢保障"], "defaultBomCode": "92021_66375_ALI_FRFB_PI_SLDW_Taxes_Taxes_ULM_0_0", "stepPrice": 44, "defaultPackageId": 109284, "naked": false, "guaranteeDegree": 4, "currencyCode": "CNY"}, {"gapPrice": 89, "isDefault": false, "insPackageId": 261, "packageName": "优享套餐", "lowestDailyPrice": 351, "youXiangGapPrice": 45, "insuranceNames": ["加强三者险", "人身财物险", "超级碰撞盗抢保障", "安心补充险"], "isYouXiang": 1, "defaultBomCode": "92021_66375_ALI_FRFB_PI_SLDW_Taxes_Taxes_ULM_0_0", "stepPrice": 89, "defaultPackageId": 109284, "naked": false, "guaranteeDegree": 5, "currencyCode": "CNY"}], "vehicleInfo": {"transmissionName": "自动挡", "realityImageUrl": "https://pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/Chevrolet-Spark-4-Doors.jpg", "imageList": ["https://pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/Chevrolet-Spark-4-Doors.jpg"], "vehicleKey": "66375_1001_0", "vehicleCode": "66375", "fuel": "汽油", "vendorSimilarVehicleInfos": [{"vendorLogo": "https://dimg04.c-ctrip.com/images/20p5112000ejfqlt702F3.png", "similarVehicleInfos": [{"vehicleImageUrl": "https://pic.c-ctrip.com/car/osd/online/vehicle_new/Chevrolet_Spark_4_Doors.png", "vehicleCode": "66375", "vehicleName": "雪佛兰 斯帕可"}], "vendorName": "Thrifty", "bizVendorCode": "14403"}], "name": "雪佛兰 斯帕可", "zhName": "雪佛兰 斯帕可", "imageUrl": "https://pic.c-ctrip.com/car/osd/online/vehicle_new/Chevrolet_Spark_4_Doors.png", "brandId": 203, "passengerNo": 4, "hasConditioner": false, "doorNo": 4, "isSpecialized": false, "fuelMode": "3", "groupCode": "1", "userRealImageCount": 0, "luggageNo": 2, "brandEName": "Chevrolet", "groupSubClassCode": "1001", "groupName": "经济轿车", "transmissionType": 1, "groupSubName": "小型轿车", "driveType": "0"}, "pickupStoreInfo": {"continentName": "北美洲", "storeServiceList": [], "bizVendorCode": "14403", "countryName": "美国", "storeCode": "92021", "continentId": 4, "telephone": "*************", "storeName": "LOS ANGELES INTL AIRPORT", "latitude": 33.954391, "vendorStoreCode": "LAXT01", "storeGuild": "领取行李后，请跟随\"Ground Transportation\"标志前往班车接客区，等待班车到来。", "countryId": 66, "isAirportStore": true, "provinceName": "加利福尼亚州", "provinceId": 10125, "cityId": 347, "longitude": -118.384257, "storeWay": "门店位于洛杉矶国际机场外，可搭乘巴士到达", "workTime": {"description": "", "fullDescription": "", "openTimeDesc": "{\"\":\"24小时营业\"}"}, "cityName": "洛杉矶", "showType": 3, "address": "9000 AIRPORT BOULEVARD,LOS ANGELES"}, "baseResponse": {"extMap": {"rentalDays": "7", "isKarabi": "1"}, "cost": 1301, "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "OK", "requestId": "3e17d12e-deb5-4358-8d02-f1b1745e2f54", "isSuccess": true}, "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "同组车型是指按照车型功能、空间大小、座位数和车门数划分的紧凑型轿车、中型轿车、小型SUV等等这些分类。您在网上预订时选择的车并不能保证到店就一定取到一模一样的，可能会得到同组车型中的其他车。"}, "cover": "https://ak-d.tripcdn.com/images/1tg1u12000dwdf1uw1DCD.png", "carProtection": {"title": "取车保障", "description": "在库存充足的情况下，车行会尽量提供您在预订时所选的车型\n如果这款车暂时没有库存，车行便会为您提供同车型组内的其他车型\n若因门店原因需要调整车型，不额外收取车型变更费用"}, "vedio": "https://ak-v.tripcdn.com/videos/9D022s000001j8a63E527.mp4", "cases": [{"vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含", "vehicleGroupCode": "default"}, {"vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特", "vehicleGroupCode": "D"}, {"vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特", "vehicleGroupCode": "S"}, {"vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者", "vehicleGroupCode": "R"}]}, "promptInfos": [{"title": "押金汇率说明", "type": 12, "contents": [{"stringObjs": [{"content": "押金所需的人民币金额根据当前汇率计算，实际所需人民币金额以用户到店支付时或者授权免押时的汇率为准。"}]}]}, {"title": "洛杉矶国际机场 is a popular location on your selected dates. Rates are likely to rise.", "subTitle": "Book now! Don't miss out on low prices.", "type": 29}], "ResponseStatus": {"Extension": [{"Value": "292616769452717425", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a621a90-477989-2877356", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1720763446352+0800)/"}, "commentInfo": {"overallRating": "3.5", "maximumRating": 5, "level": "", "commentLabel": "", "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=BRAND_CITY_QUERY&calabiStoreId=92021&calabiVehicleId=66375&vehicleName=雪佛兰 斯帕可&productCategoryId=34&isHideNavBar=YES&brandCity=SD0004-347&cityName=洛杉矶&storeName=LOS ANGELES INTL AIRPORT", "hasComment": 1, "commentCount": 38}, "requestInfo": {"rLongitude": -118.40853, "rDate": "20240725100000", "pCityId": 347, "returnDate": "/Date(1721872800000+0800)/", "sourceCountryId": 1, "pLatitude": 33.941589, "rLatitude": 33.941589, "pLongitude": -118.40853, "pDate": "20240718100000", "rCityId": 347, "pickupLocationName": "洛杉矶国际机场", "returnLocationName": "洛杉矶国际机场", "pickupDate": "/Date(1721268000000+0800)/"}, "checkResponseTime": 1720763446163.315, "returnStoreInfo": {"continentName": "北美洲", "storeServiceList": [], "bizVendorCode": "14403", "countryName": "美国", "storeCode": "92021", "continentId": 4, "telephone": "*************", "storeName": "LOS ANGELES INTL AIRPORT", "latitude": 33.954391, "vendorStoreCode": "LAXT01", "storeGuild": "请提前致电联系门店，确认还车位置。通常在停车场附近的道路旁会有“Car Return”的指示牌，可按照指示牌前往。", "countryId": 66, "isAirportStore": true, "provinceName": "加利福尼亚州", "provinceId": 10125, "cityId": 347, "longitude": -118.384257, "storeWay": "门店位于洛杉矶国际机场外", "workTime": {"description": "", "fullDescription": "", "openTimeDesc": "{\"\":\"24小时营业\"}"}, "cityName": "洛杉矶", "showType": 3, "address": "9000 AIRPORT BOULEVARD,LOS ANGELES"}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "checkRequestTime": 1720763444772.494, "crossPolicy": {"notes": ["若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。除特别说明的情况外，“跨境费用”为前往单个国家所需的费用。"], "crossLocationsInfos": [{"subTitle": "门店支持在以下区域跨境使用车辆：", "crossTypeName": "跨境政策", "title": "选择计划前往的国家", "summaryPolicies": ["若您的行程中涉及跨境，请提前选择"], "crossType": 3, "locations": [{"status": 3, "isSelected": false, "regionId": "201", "name": "安提瓜和巴布达", "statusName": "不允许跨境", "firstChar": "A"}, {"status": 3, "isSelected": false, "regionId": "275", "name": "安圭拉", "statusName": "不允许跨境", "firstChar": "A"}, {"status": 3, "isSelected": false, "regionId": "171", "name": "阿鲁巴", "statusName": "不允许跨境", "firstChar": "A"}, {"status": 3, "isSelected": false, "regionId": "202", "name": "巴巴多斯", "statusName": "不允许跨境", "firstChar": "B"}, {"status": 3, "isSelected": false, "regionId": "207", "name": "百慕大", "statusName": "不允许跨境", "firstChar": "B"}, {"status": 3, "isSelected": false, "regionId": "208", "name": "波多黎各", "statusName": "不允许跨境", "firstChar": "B"}, {"status": 3, "isSelected": false, "regionId": "210", "name": "伯利兹", "statusName": "不允许跨境", "firstChar": "B"}, {"status": 3, "isSelected": false, "regionId": "18", "name": "巴拿马", "statusName": "不允许跨境", "firstChar": "B"}, {"status": 3, "isSelected": false, "regionId": "185", "name": "巴哈马", "statusName": "不允许跨境", "firstChar": "B"}, {"status": 3, "isSelected": false, "regionId": "276", "name": "多米尼加共和国", "statusName": "不允许跨境", "firstChar": "D"}, {"status": 3, "isSelected": false, "regionId": "217", "name": "多米尼克", "statusName": "不允许跨境", "firstChar": "D"}, {"status": 3, "isSelected": false, "regionId": "291", "name": "法属圣马丁", "statusName": "不允许跨境", "firstChar": "F"}, {"status": 3, "isSelected": false, "regionId": "261", "name": "瓜德罗普岛", "statusName": "不允许跨境", "firstChar": "G"}, {"status": 3, "isSelected": false, "regionId": "220", "name": "格林纳达", "statusName": "不允许跨境", "firstChar": "G"}, {"status": 3, "isSelected": false, "regionId": "38", "name": "哥斯达黎加", "statusName": "不允许跨境", "firstChar": "G"}, {"status": 3, "isSelected": false, "regionId": "39", "name": "古巴", "statusName": "不允许跨境", "firstChar": "G"}, {"status": 3, "isSelected": false, "regionId": "259", "name": "荷兰加勒比区", "statusName": "不允许跨境", "firstChar": "H"}, {"status": 3, "isSelected": false, "regionId": "295", "name": "荷属圣马丁", "statusName": "不允许跨境", "firstChar": "H"}, {"status": 3, "isSelected": false, "regionId": "41", "name": "海地", "statusName": "不允许跨境", "firstChar": "H"}, {"status": 3, "isSelected": false, "regionId": "44", "name": "洪都拉斯", "statusName": "不允许跨境", "firstChar": "H"}, {"status": 2, "policy": "多数情况允许车辆跨境至加拿大，跨境费用根据车辆类别、租用时间等情况决定，需提前联系门店确认。请注意，加拿大居民不得驾驶美国车辆进入加拿大。", "regionId": "47", "isSelected": false, "statusName": "条件跨境", "name": "加拿大", "firstChar": "J"}, {"status": 3, "isSelected": false, "regionId": "223", "name": "开曼群岛", "statusName": "不允许跨境", "firstChar": "K"}, {"status": 3, "isSelected": false, "regionId": "294", "name": "库拉索岛", "statusName": "不允许跨境", "firstChar": "K"}, {"status": 2, "policy": "仅允许从德克萨斯州，新墨西哥州，亚利桑那州，加利福尼亚州跨境至墨西哥，且可驾驶范围仅限于美国与墨西哥边境以南 250 英里以内。 跨境需向门店申请并获得授权，请提前联系门店确认。跨境需购买墨西哥保险，该保险将覆盖跨境驾驶期间的第三者责任险、碰撞险以及综合保险，参考费用为每天38-48美元（含税费），具体购买方式请咨询门店。跨境至墨西哥期间，高级道路救援服务（PERS）不生效。请注意，墨西哥居民不得驾驶美国车辆进入墨西哥。", "regionId": "72", "isSelected": false, "statusName": "条件跨境", "name": "墨西哥", "firstChar": "M"}, {"status": 3, "isSelected": false, "regionId": "279", "name": "马提尼克", "statusName": "不允许跨境", "firstChar": "M"}, {"status": 3, "isSelected": false, "regionId": "280", "name": "美属维尔京群岛", "statusName": "不允许跨境", "firstChar": "M"}, {"status": 3, "isSelected": false, "regionId": "198", "name": "尼加拉瓜", "statusName": "不允许跨境", "firstChar": "N"}, {"status": 3, "isSelected": false, "regionId": "289", "name": "圣皮埃尔和密克隆岛", "statusName": "不允许跨境", "firstChar": "S"}, {"status": 3, "isSelected": false, "regionId": "297", "name": "圣巴泰勒米", "statusName": "不允许跨境", "firstChar": "S"}, {"status": 3, "isSelected": false, "regionId": "238", "name": "萨尔瓦多", "statusName": "不允许跨境", "firstChar": "S"}, {"status": 3, "isSelected": false, "regionId": "243", "name": "圣基茨和尼维斯", "statusName": "不允许跨境", "firstChar": "S"}, {"status": 3, "isSelected": false, "regionId": "244", "name": "圣卢西亚", "statusName": "不允许跨境", "firstChar": "S"}, {"status": 3, "isSelected": false, "regionId": "246", "name": "圣文森特和格林纳丁斯", "statusName": "不允许跨境", "firstChar": "S"}, {"status": 3, "isSelected": false, "regionId": "265", "name": "特克斯和凯科斯群岛", "statusName": "不允许跨境", "firstChar": "T"}, {"status": 3, "isSelected": false, "regionId": "252", "name": "特立尼达和多巴哥", "statusName": "不允许跨境", "firstChar": "T"}, {"status": 3, "isSelected": false, "regionId": "90", "name": "危地马拉", "statusName": "不允许跨境", "firstChar": "W"}, {"status": 3, "isSelected": false, "regionId": "282", "name": "英属维尔京群岛", "statusName": "不允许跨境", "firstChar": "Y"}, {"status": 3, "isSelected": false, "regionId": "101", "name": "牙买加", "statusName": "不允许跨境", "firstChar": "Y"}, {"status": 3, "isSelected": false, "regionId": "299", "name": "英属蒙塞拉特岛", "statusName": "不允许跨境", "firstChar": "Y"}]}, {"crossType": 1, "crossTypeName": "跨岛政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨岛，建议您更换其它租车公司或车型组。"]}, {"summaryTitle": "条件跨州", "crossTypeName": "跨州政策", "summaryPolicies": ["通常情况下允许在美国境内跨州行驶。不可以在阿拉斯加州的部分高速公路上行驶。不可以未经门店允许使用轮渡运输车辆。如您有跨州需求，请提前联系门店确认。"], "crossType": 2}], "title": "旅行限制"}, "timeInterval": 1390.821044921875, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 1481, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 1481, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1720763444771, "afterFetch": 1720763446252}}