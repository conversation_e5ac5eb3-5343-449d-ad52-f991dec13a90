{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "8358fc49-ca8b-4d65-b869-b0124168e056", "extMap": {"isKarabi": "1", "rentalDays": "3"}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "2023-10-16 19:40:45", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "2219474461855892655"}, {"Id": "RootMessageId", "Value": "100025527-0a063db3-471515-154622"}]}, "isSoldOut": false, "isHotCity": true, "pickupStoreInfo": {"storeCode": "205889", "bizVendorCode": "14027", "telephone": "001 ************", "storeName": "Los Angeles Airport", "address": "5301 W. 104th St", "longitude": -118.372344, "latitude": 33.94227, "storeGuild": "请提前联系门店确认取还车位置。", "storeWay": "机场外", "workTime": {"openTimeDesc": "{\"\":\"08:00 - 18:00\"}", "description": ""}, "storeServiceList": [], "countryId": 66, "countryName": "美国", "provinceId": 10125, "provinceName": "加利福尼亚州", "cityId": 347, "cityName": "洛杉矶", "isAirportStore": true, "vendorStoreCode": "60509", "continentId": 4, "continentName": "北美洲"}, "returnStoreInfo": {"storeCode": "205889", "bizVendorCode": "14027", "telephone": "001 ************", "storeName": "Los Angeles Airport", "address": "5301 W. 104th St", "longitude": -118.372344, "latitude": 33.94227, "storeGuild": "请提前联系门店确认取还车位置。", "storeWay": "机场外", "workTime": {"openTimeDesc": "{\"\":\"08:00 - 18:00\"}", "description": ""}, "storeServiceList": [], "countryId": 66, "countryName": "美国", "provinceId": 10125, "provinceName": "加利福尼亚州", "cityId": 347, "cityName": "洛杉矶", "isAirportStore": true, "vendorStoreCode": "60509", "continentId": 4, "continentName": "北美洲"}, "vendorInfo": {"bizVendorCode": "14027", "vendorName": "Green Motion-calabi", "vendorImageUrl": "https://dimg04.c-ctrip.com/images/0AS4j12000alw27ulE4F8.jpg", "vendorCode": "SD0131", "isBroker": false, "platformCode": "", "platformName": "GREEN MOTION LIMITED", "haveCoupon": true, "vendorTag": {"title": "当地连锁", "sortNum": 0}}, "vehicleInfo": {"brandId": 176, "brandEName": "Nissan", "name": "日产 Versa10619", "zhName": "日产 Versa10619", "vehicleCode": "6881", "imageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg", "groupCode": "1", "groupSubClassCode": "39", "groupName": "经济型轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "imageList": ["https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "userRealImageCount": 0, "isSpecialized": false, "hasConditioner": false, "vendorSimilarVehicleInfos": [{"bizVendorCode": "14027", "vendorName": "Green Motion-calabi", "vendorLogo": "https://dimg04.c-ctrip.com/images/0AS4j12000alw27ulE4F8.jpg", "similarVehicleInfos": [{"vehicleCode": "6881", "vehicleName": "日产 Versa10619", "vehicleImageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"}]}], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "groupSubName": "中小型轿车"}, "flightDelayRule": {"title": "航班延误保留政策", "description": "若航班延误，门店将不保留车辆，请留取充足的时间取车", "rules": [{"title": "若航班延误，门店将不保留车辆，请留取充足的时间取车"}, {"title": "建议您填写航班号，如遇延误请携带机票前往取车"}]}, "commentInfo": {"level": "", "commentCount": 0, "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=205889&calabiVehicleId=6881&vehicleName=日产 Versa10619&productCategoryId=34&isHideNavBar=YES"}, "packageInfos": [{"insPackageId": 2, "isDefault": true, "packageName": "基础套餐", "currencyCode": "CNY", "defaultBomCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "defaultPackageId": 318204, "guaranteeDegree": 2.0, "naked": false, "insuranceNames": ["车辆碰撞保障", "车辆盗抢险", "第三者责任险"], "lowestDailyPrice": 990, "gapPrice": 0, "stepPrice": 0, "description": "不推荐选择。除非主驾驶人在当地已购买过第三者险。", "noticeDescTitle": "不推荐选择", "packageType": 0, "excessEncourage": "起赔额¥18,270起", "isBasic": 1}, {"insPackageId": 2222, "isDefault": true, "packageName": "高级套餐", "currencyCode": "CNY", "defaultBomCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "defaultPackageId": 318204, "guaranteeDegree": 2.0, "naked": false, "insuranceNames": ["车辆碰撞保障", "车辆盗抢险", "第三者责任险"], "lowestDailyPrice": 990, "gapPrice": 0, "stepPrice": 0, "description": "不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失", "packageType": 0, "excessEncourage": "起赔额¥18,270起", "isBasic": 1}, {"insPackageId": 258, "isDefault": false, "packageName": "优享套餐", "currencyCode": "CNY", "defaultBomCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "defaultPackageId": 318204, "guaranteeDegree": 4.0, "naked": false, "insuranceNames": ["车辆碰撞保障", "车辆盗抢险", "第三者责任险", "安心补充险"], "lowestDailyPrice": 1015, "gapPrice": 25, "stepPrice": 25, "excessEncourage": "起赔额降低为0", "isYouXiang": 1, "youXiangGapPrice": 25}, {"insPackageId": 770, "isDefault": false, "packageName": "尊享套餐", "currencyCode": "CNY", "defaultBomCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "defaultPackageId": 318204, "guaranteeDegree": 5.0, "naked": false, "insuranceNames": ["车辆碰撞保障", "车辆盗抢险", "第三者责任险", "安心补充险", "驾乘意外险"], "lowestDailyPrice": 1090, "gapPrice": 100, "stepPrice": 100, "descTitle": "全方位保障驾驶车辆、车内人员财物及第三者", "isYouXiang": 0}], "productDetails": [{"insPackageId": 2, "insuranceItems": [{"code": "CDW", "name": "车辆碰撞保障", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0, "excessShortDesc": "起赔额US$ 2,500", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额US$ 2,500.00(约¥18,270)", "subObject": [{"title": "车行承担", "content": ["US$ 2,500.00(约¥18,270)以上部分"]}, {"title": "客户或承租方承担", "content": ["US$ 2,500.00(约¥18,270)及以下部分"]}], "note": "false|US$ 2,500.00(约¥18,270)"}}, {"code": "TP", "name": "车辆盗抢险", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0, "excessShortDesc": "起赔额US$ 2,500", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额US$ 2,500.00(约¥18,270)", "subObject": [{"title": "车行承担", "content": ["US$ 2,500.00(约¥18,270)以上部分"]}, {"title": "客户或承租方承担", "content": ["US$ 2,500.00(约¥18,270)及以下部分"]}], "note": "false|US$ 2,500.00(约¥18,270)"}}, {"code": "TPL", "name": "第三者责任险", "description": "保障第三方车辆或人员伤害损失", "shortDescription": "保障对第三方车辆或人员造成伤害的损失", "longDescription": "可避免由第三方事故导致的高额赔偿，强烈建议购买。", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}], "combinations": [{"bomCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 990, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 2969, "payMode": 2, "packageId": 318204}], "productInfoList": [{"productCode": "2", "bomGroupCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "priceInfoList": [{"currentCarPrice": 2969, "currentDailyPrice": 990, "currentTotalPrice": 2969, "localCarPrice": 406.3, "localDailyPrice": 135.43, "localTotalPrice": 406.3, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 2969, "localOnewayfee": 0.0, "localPoaPrice": 0.0, "localPrepaidPrice": 406.3, "isContainOnewayFee": false, "payMode": 2, "productId": "2318204", "packageId": 318204, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": "", "vendorVehicleId": "87448_63746030469"}, "exchangeRate": 7.30815, "mileInfo": {"name": "里程限制每天200mile", "isLimited": true, "distance": 200.0, "distanceUnit": "mile", "periodUnit": "每天", "chargeAmount": 0.55, "chargeUnit": "mile", "quantity": 1.0, "desc": "里程限制每天200mile，超出里程按照US$ 0.55/mile计算。\n*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"}, "confirmInfo": {"confirmTitle": "20小时内确认", "confirmDesc": "预订此产品后供应商将在20小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 20.0}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": -1, "cancelDescription": "支付完成至取车时间可免费取消;取车时间后可免费取消"}, "ageRestriction": {"description": "驾驶员年龄要求：25-65周岁", "minDriverAge": 25, "maxDriverAge": 65, "youngDriverAgeDesc": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "oldDriverAgeDesc": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月"}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}], "depositCurrencyCode": "USD", "maxDeposit": 3000.0, "minDeposit": 500.0}, "allTags": [{"title": "20小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在20小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车时间可免费取消;取车时间后可免费取消", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "券减¥16", "category": 3, "type": 3, "code": "3", "description": "券减¥16,可享立减￥16/天（3天共减￥50）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}, {"title": "四月特惠", "category": 3, "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "sortNum": 10000, "colorCode": "3", "labelCode": "3834"}, {"title": "国庆特惠", "category": 3, "type": 1, "description": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。", "sortNum": 10000, "colorCode": "3", "labelCode": "3854"}, {"title": "里程限制每天200mile", "category": 4, "type": 2, "code": "4", "description": "里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "sortNum": 125, "colorCode": "4", "labelCode": "3580"}], "insuranceDetails": [{"code": "CDW", "name": "车辆碰撞保障", "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0}, {"code": "TP", "name": "车辆盗抢险", "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0}, {"code": "TPL", "name": "第三者责任险"}], "chargeList": [], "promotionInfo": {"deductionAmount": 50}, "vendorPromotionList": [{"type": 1, "title": "四月特惠", "deductionPercent": 0.0, "code": "3834", "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。"}, {"type": 1, "title": "国庆特惠", "deductionPercent": 0.0, "code": "3854", "longTag": "国庆特惠", "longDesc": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。"}], "pkgSellingRuleId": 318204}], "needFlightNo": false, "equipments": [], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "CDW", "name": "车辆碰撞保障", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "TP", "name": "车辆盗抢险", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "TPL", "name": "第三者责任险", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "LM", "name": "里程限制每天200mile", "desc": "里程限制每天200mile，超出里程按照US$ 0.55/mile计算。\n*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "身份证明文件", "subTitle": "中国大陆护照原件", "type": 0, "subObject": [{"title": "中国大陆护照原件", "subTitle": "中国大陆公民", "code": "CN", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国香港护照原件", "subTitle": "中国香港公民", "code": "HK", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国澳门护照原件", "subTitle": "中国澳门公民", "code": "MO", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国台湾护照原件", "subTitle": "中国台湾公民", "code": "TW", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "其他地区护照原件", "subTitle": "其他国家公民", "code": "OH", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议前往Trip下单>"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}, {"contentStyle": "1", "stringObjs": [{"content": "因商业条款限制，若您持非中国（含港澳台）护照，有无法取车或者变价的风险。建议您使用与驾照发证国家/地区一致的护照进行取车或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}]}], "summaryTitle": "护照"}, {"title": "驾照要求", "subTitle": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满12个月", "驾驶员年龄要求：25-65周岁"], "type": 1, "subObject": [{"title": "驾照要求", "contentObject": [{"contentStyle": "1", "stringObjs": [{"content": "当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原件和下列驾照组合的实体件租车，电子版或照片将不被认可。"}]}, {"contentStyle": "1", "stringObjs": [{"content": "所有驾驶员驾龄必须至少满12个月"}]}, {"contentStyle": "1", "stringObjs": [{"content": "驾驶员年龄要求：25-65周岁"}]}, {"contentStyle": "1", "stringObjs": [{"content": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"}]}, {"contentStyle": "1", "stringObjs": [{"content": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"}]}], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合", "code": "CN", "subObject": [{"title": "中国驾照原件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "HK", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照+国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合(任选其一)", "code": "OH", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}, {"title": "中国驾照原件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5, "optimalType": "-1"}, {"title": "台湾驾照+国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31, "optimalType": "-1"}]}]}, {"title": "注意：", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "所有南加州客户必须提供居住证明（即带有您姓名和当前地址的公用事业账单）以及转让给租赁车辆的完整汽车保险证明。当地租客在前台购买碰撞损害豁免险不是选择项。 "}]}], "type": 10}], "summaryTitle": "驾照", "summaryObject": [{"title": "非中国驾照政策", "contentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，仅支持中国（含港澳台）护照和驾照持有者预订。若您持非中国（含港澳台）护照，则有无法取车或者价格变化的风险，但是仍然可以预订；若持非中国（含港澳台）驾照，则无法在本平台预订，建议您前往Trip下单。"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "2", "stringObjs": [{"content": "非中国驾照"}]}]}]}, {"title": "主驾驶员名下国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店", "不支持卡面带银联标志"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6}, {"title": "接受的信用卡", "content": ["维萨，万事达", "不支持卡面带银联标志"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png"]}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,654.08-¥21,924.45），还车后30-60天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,655-¥21,925）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3, "summaryTitle": "提车凭证"}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在20小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车时间可免费取消;取车时间后可免费取消"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2023年11月8日16:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2023年11月8日16:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2023年11月8日16:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2023年11月8日16:00", "subTitle": "支付完成至取车时间", "description": "可免费取消", "showFree": true}, {"title": "2023年11月8日16:00后", "subTitle": "取车时间后", "description": "可免费取消", "showFree": true}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,654.08-¥21,924.45），还车后30-60天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,655-¥21,925）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}, {"title": "长租政策", "content": ["请注意供应商不接受28天以上的连续租车，若需长租请联系供应商沟通。"], "type": 52, "code": "2", "sortNum": 8}], "sortNum": 7}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 16, "custumerTotalPrice": 225, "custumerDailyPrice": 75, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "localTotalPrice": 225, "localDailyPrice": 75.0, "localCurrencyCode": "CNY", "desc": "驾乘意外险短描述", "longDesc": "驾乘意外险长描述", "noCoverageContent": ["驾乘意外险不保障内容"], "typeCode": "ACCIDENT_COVER", "days": 3, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "version": "OPI1128778955596234752", "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "1、报警并联系门店-卡拉比", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。"], "type": 1}, {"title": "2、还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额和，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"], "type": 2}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车", "description": "不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"], "briefInsuranceItems": [{"code": "CDW", "name": "碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"currencyCode": "USD", "minExcess": 0, "maxExcess": 2500.0, "coverageWithoutPlatformInsurance": "起赔额US$ 2,500.00(约¥18,270)", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)"}]}, {"name": "第三者责任险", "insuranceDetail": [{"packageId": 318204, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}]}], "insuranceCompareItems": [{"description": "起赔额USD2,500(约¥18,270)", "type": "LDW", "descriptionColorCode": "GREEN"}, {"contains": false, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": false, "type": "PI"}, {"contains": false, "type": "RAP"}, {"contains": false, "type": "CANCEL"}, {"contains": false, "type": "ACCIDENT"}], "ctripInsuranceIds": [15, 16]}, {"insPackageId": 2222, "insuranceItems": [{"code": "CDW", "name": "车辆碰撞保障", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0, "excessShortDesc": "起赔额US$ 2,500", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额US$ 2,500.00(约¥18,270)", "subObject": [{"title": "车行承担", "content": ["US$ 2,500.00(约¥18,270)以上部分"]}, {"title": "客户或承租方承担", "content": ["US$ 2,500.00(约¥18,270)及以下部分"]}], "note": "false|US$ 2,500.00(约¥18,270)"}}, {"code": "TP", "name": "车辆盗抢险", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0, "excessShortDesc": "起赔额US$ 2,500", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额US$ 2,500.00(约¥18,270)", "subObject": [{"title": "车行承担", "content": ["US$ 2,500.00(约¥18,270)以上部分"]}, {"title": "客户或承租方承担", "content": ["US$ 2,500.00(约¥18,270)及以下部分"]}], "note": "false|US$ 2,500.00(约¥18,270)"}}, {"code": "TPL", "name": "第三者责任险", "description": "保障第三方车辆或人员伤害损失", "shortDescription": "保障对第三方车辆或人员造成伤害的损失", "longDescription": "可避免由第三方事故导致的高额赔偿，强烈建议购买。", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}], "combinations": [{"bomCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 990, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 2969, "payMode": 2, "packageId": 318204}], "productInfoList": [{"productCode": "2", "bomGroupCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "priceInfoList": [{"currentCarPrice": 2969, "currentDailyPrice": 990, "currentTotalPrice": 2969, "localCarPrice": 406.3, "localDailyPrice": 135.43, "localTotalPrice": 406.3, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 2969, "localOnewayfee": 0.0, "localPoaPrice": 0.0, "localPrepaidPrice": 406.3, "isContainOnewayFee": false, "payMode": 2, "productId": "2318204", "packageId": 318204, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": "", "vendorVehicleId": "87448_63746030469"}, "exchangeRate": 7.30815, "mileInfo": {"name": "里程限制每天200mile", "isLimited": true, "distance": 200.0, "distanceUnit": "mile", "periodUnit": "每天", "chargeAmount": 0.55, "chargeUnit": "mile", "quantity": 1.0, "desc": "里程限制每天200mile，超出里程按照US$ 0.55/mile计算。\n*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"}, "confirmInfo": {"confirmTitle": "20小时内确认", "confirmDesc": "预订此产品后供应商将在20小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 20.0}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": -1, "cancelDescription": "支付完成至取车时间可免费取消;取车时间后可免费取消"}, "ageRestriction": {"description": "驾驶员年龄要求：25-65周岁", "minDriverAge": 25, "maxDriverAge": 65, "youngDriverAgeDesc": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "oldDriverAgeDesc": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月"}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}], "depositCurrencyCode": "USD", "maxDeposit": 3000.0, "minDeposit": 500.0}, "allTags": [{"title": "20小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在20小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车时间可免费取消;取车时间后可免费取消", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "券减¥16", "category": 3, "type": 3, "code": "3", "description": "券减¥16,可享立减￥16/天（3天共减￥50）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}, {"title": "四月特惠", "category": 3, "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "sortNum": 10000, "colorCode": "3", "labelCode": "3834"}, {"title": "国庆特惠", "category": 3, "type": 1, "description": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。", "sortNum": 10000, "colorCode": "3", "labelCode": "3854"}, {"title": "里程限制每天200mile", "category": 4, "type": 2, "code": "4", "description": "里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "sortNum": 125, "colorCode": "4", "labelCode": "3580"}], "insuranceDetails": [{"code": "CDW", "name": "车辆碰撞保障", "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0}, {"code": "TP", "name": "车辆盗抢险", "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0}, {"code": "TPL", "name": "第三者责任险"}], "chargeList": [], "promotionInfo": {"deductionAmount": 50}, "vendorPromotionList": [{"type": 1, "title": "四月特惠", "deductionPercent": 0.0, "code": "3834", "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。"}, {"type": 1, "title": "国庆特惠", "deductionPercent": 0.0, "code": "3854", "longTag": "国庆特惠", "longDesc": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。"}], "pkgSellingRuleId": 318204}], "needFlightNo": false, "equipments": [], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "CDW", "name": "车辆碰撞保障", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "TP", "name": "车辆盗抢险", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "TPL", "name": "第三者责任险", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "LM", "name": "里程限制每天200mile", "desc": "里程限制每天200mile，超出里程按照US$ 0.55/mile计算。\n*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "身份证明文件", "subTitle": "中国大陆护照原件", "type": 0, "subObject": [{"title": "中国大陆护照原件", "subTitle": "中国大陆公民", "code": "CN", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国香港护照原件", "subTitle": "中国香港公民", "code": "HK", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国澳门护照原件", "subTitle": "中国澳门公民", "code": "MO", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国台湾护照原件", "subTitle": "中国台湾公民", "code": "TW", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "其他地区护照原件", "subTitle": "其他国家公民", "code": "OH", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议前往Trip下单>"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}, {"contentStyle": "1", "stringObjs": [{"content": "因商业条款限制，若您持非中国（含港澳台）护照，有无法取车或者变价的风险。建议您使用与驾照发证国家/地区一致的护照进行取车或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}]}], "summaryTitle": "护照"}, {"title": "驾照要求", "subTitle": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满12个月", "驾驶员年龄要求：25-65周岁"], "type": 1, "subObject": [{"title": "驾照要求", "contentObject": [{"contentStyle": "1", "stringObjs": [{"content": "当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原件和下列驾照组合的实体件租车，电子版或照片将不被认可。"}]}, {"contentStyle": "1", "stringObjs": [{"content": "所有驾驶员驾龄必须至少满12个月"}]}, {"contentStyle": "1", "stringObjs": [{"content": "驾驶员年龄要求：25-65周岁"}]}, {"contentStyle": "1", "stringObjs": [{"content": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"}]}, {"contentStyle": "1", "stringObjs": [{"content": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"}]}], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合", "code": "CN", "subObject": [{"title": "中国驾照原件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "HK", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照+国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合(任选其一)", "code": "OH", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}, {"title": "中国驾照原件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5, "optimalType": "-1"}, {"title": "台湾驾照+国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31, "optimalType": "-1"}]}]}, {"title": "注意：", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "所有南加州客户必须提供居住证明（即带有您姓名和当前地址的公用事业账单）以及转让给租赁车辆的完整汽车保险证明。当地租客在前台购买碰撞损害豁免险不是选择项。 "}]}], "type": 10}], "summaryTitle": "驾照", "summaryObject": [{"title": "非中国驾照政策", "contentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，仅支持中国（含港澳台）护照和驾照持有者预订。若您持非中国（含港澳台）护照，则有无法取车或者价格变化的风险，但是仍然可以预订；若持非中国（含港澳台）驾照，则无法在本平台预订，建议您前往Trip下单。"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "2", "stringObjs": [{"content": "非中国驾照"}]}]}]}, {"title": "主驾驶员名下国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店", "不支持卡面带银联标志"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6}, {"title": "接受的信用卡", "content": ["维萨，万事达", "不支持卡面带银联标志"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png"]}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,654.08-¥21,924.45），还车后30-60天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,655-¥21,925）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3, "summaryTitle": "提车凭证"}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在20小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车时间可免费取消;取车时间后可免费取消"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2023年11月8日16:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2023年11月8日16:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2023年11月8日16:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2023年11月8日16:00", "subTitle": "支付完成至取车时间", "description": "可免费取消", "showFree": true}, {"title": "2023年11月8日16:00后", "subTitle": "取车时间后", "description": "可免费取消", "showFree": true}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,654.08-¥21,924.45），还车后30-60天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,655-¥21,925）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}, {"title": "长租政策", "content": ["请注意供应商不接受28天以上的连续租车，若需长租请联系供应商沟通。"], "type": 52, "code": "2", "sortNum": 8}], "sortNum": 7}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 16, "custumerTotalPrice": 225, "custumerDailyPrice": 75, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "localTotalPrice": 225, "localDailyPrice": 75.0, "localCurrencyCode": "CNY", "desc": "驾乘意外险短描述", "longDesc": "驾乘意外险长描述", "noCoverageContent": ["驾乘意外险不保障内容"], "typeCode": "ACCIDENT_COVER", "days": 3, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "version": "OPI1128778955596234752", "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "1、报警并联系门店-卡拉比", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。"], "type": 1}, {"title": "2、还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额和，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"], "type": 2}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车", "description": "不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"], "briefInsuranceItems": [{"code": "CDW", "name": "碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0, "coverageWithoutPlatformInsurance": "起赔额US$ 2,500.00(约¥18,270)", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)"}]}, {"name": "第三者责任险", "insuranceDetail": [{"packageId": 318204, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}]}], "insuranceCompareItems": [{"description": "起赔额USD2,500(约¥18,270)", "type": "LDW", "descriptionColorCode": "GREEN"}, {"contains": false, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": false, "type": "PI"}, {"contains": false, "type": "RAP"}, {"contains": false, "type": "CANCEL"}, {"contains": false, "type": "ACCIDENT"}]}, {"insPackageId": 258, "insuranceItems": [{"code": "CDW", "name": "车辆碰撞保障", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额US$ 2,500可赔", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)", "coverageWithPlatformInsuranceV2": "*起赔额US$ 2,500.00(约¥18,270)及以下部分由国内保险公司提供"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["US$ 2,500.00(约¥18,270)以上部分"]}, {"title": "国内保险公司承担*", "content": ["US$ 2,500.00(约¥18,270)及以下部分（据实承担）"]}, {"title": "客户或承租方承担", "content": ["¥0.00"]}]}}, {"code": "TP", "name": "车辆盗抢险", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额US$ 2,500可赔", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)", "coverageWithPlatformInsuranceV2": "*起赔额US$ 2,500.00(约¥18,270)及以下部分由国内保险公司提供"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["US$ 2,500.00(约¥18,270)以上部分"]}, {"title": "国内保险公司承担*", "content": ["US$ 2,500.00(约¥18,270)及以下部分（据实承担）"]}, {"title": "客户或承租方承担", "content": ["¥0.00"]}]}}, {"code": "TPL", "name": "第三者责任险", "description": "保障第三方车辆或人员伤害损失", "shortDescription": "保障对第三方车辆或人员造成伤害的损失", "longDescription": "可避免由第三方事故导致的高额赔偿，强烈建议购买。", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}], "ctripInsuranceIds": [15], "combinations": [{"bomCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 1015, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 3044, "payMode": 2, "packageId": 318204}], "productInfoList": [{"productCode": "2", "bomGroupCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "priceInfoList": [{"currentCarPrice": 2969, "currentDailyPrice": 1015, "currentTotalPrice": 3044, "localCarPrice": 406.3, "localDailyPrice": 135.43, "localTotalPrice": 406.3, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 3044, "localOnewayfee": 0.0, "localPoaPrice": 0.0, "localPrepaidPrice": 406.3, "payMode": 2, "productId": "2318204", "packageId": 318204, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": "", "vendorVehicleId": "87448_63746030469"}, "exchangeRate": 7.30815, "mileInfo": {"name": "里程限制每天200mile", "isLimited": true, "distance": 200.0, "distanceUnit": "mile", "periodUnit": "每天", "chargeAmount": 0.55, "chargeUnit": "mile", "quantity": 1.0, "desc": "里程限制每天200mile，超出里程按照US$ 0.55/mile计算。\n*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"}, "confirmInfo": {"confirmTitle": "20小时内确认", "confirmDesc": "预订此产品后供应商将在20小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 20.0}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": -1, "cancelDescription": "支付完成至取车时间可免费取消;取车时间后可免费取消"}, "ageRestriction": {"description": "驾驶员年龄要求：25-65周岁", "minDriverAge": 25, "maxDriverAge": 65, "youngDriverAgeDesc": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "oldDriverAgeDesc": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月"}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}], "depositCurrencyCode": "USD", "maxDeposit": 3000.0, "minDeposit": 500.0}, "allTags": [{"title": "20小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在20小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车时间可免费取消;取车时间后可免费取消", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "券减¥16", "category": 3, "type": 3, "code": "3", "description": "券减¥16,可享立减￥16/天（3天共减￥50）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}, {"title": "四月特惠", "category": 3, "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "sortNum": 10000, "colorCode": "3", "labelCode": "3834"}, {"title": "国庆特惠", "category": 3, "type": 1, "description": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。", "sortNum": 10000, "colorCode": "3", "labelCode": "3854"}, {"title": "里程限制每天200mile", "category": 4, "type": 2, "code": "4", "description": "里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "sortNum": 125, "colorCode": "4", "labelCode": "3580"}], "insuranceDetails": [{"code": "CDW", "name": "车辆碰撞保障", "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0}, {"code": "TP", "name": "车辆盗抢险", "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0}, {"code": "TPL", "name": "第三者责任险"}], "chargeList": [], "promotionInfo": {"deductionAmount": 50}, "vendorPromotionList": [{"type": 1, "title": "四月特惠", "deductionPercent": 0.0, "code": "3834", "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。"}, {"type": 1, "title": "国庆特惠", "deductionPercent": 0.0, "code": "3854", "longTag": "国庆特惠", "longDesc": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。"}], "pkgSellingRuleId": 318204}], "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 25.0, "localTotalPrice": 75, "localCurrencyCode": "CNY", "currentDailyPrice": 25, "currentTotalPrice": 75, "currentCurrencyCode": "CNY", "uniqueCode": "15"}], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "CDW", "name": "车辆碰撞保障", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "TP", "name": "车辆盗抢险", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "TPL", "name": "第三者责任险", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "LM", "name": "里程限制每天200mile", "desc": "里程限制每天200mile，超出里程按照US$ 0.55/mile计算。\n*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}], "pickUpMaterials": [{"title": "身份证明文件", "subTitle": "中国大陆护照原件", "type": 0, "subObject": [{"title": "中国大陆护照原件", "subTitle": "中国大陆公民", "code": "CN", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国香港护照原件", "subTitle": "中国香港公民", "code": "HK", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国澳门护照原件", "subTitle": "中国澳门公民", "code": "MO", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国台湾护照原件", "subTitle": "中国台湾公民", "code": "TW", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "其他地区护照原件", "subTitle": "其他国家公民", "code": "OH", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议前往Trip下单>"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}, {"contentStyle": "1", "stringObjs": [{"content": "因商业条款限制，若您持非中国（含港澳台）护照，有无法取车或者变价的风险。建议您使用与驾照发证国家/地区一致的护照进行取车或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}]}], "summaryTitle": "护照"}, {"title": "驾照要求", "subTitle": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满12个月", "驾驶员年龄要求：25-65周岁"], "type": 1, "subObject": [{"title": "驾照要求", "contentObject": [{"contentStyle": "1", "stringObjs": [{"content": "当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原件和下列驾照组合的实体件租车，电子版或照片将不被认可。"}]}, {"contentStyle": "1", "stringObjs": [{"content": "所有驾驶员驾龄必须至少满12个月"}]}, {"contentStyle": "1", "stringObjs": [{"content": "驾驶员年龄要求：25-65周岁"}]}, {"contentStyle": "1", "stringObjs": [{"content": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"}]}, {"contentStyle": "1", "stringObjs": [{"content": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"}]}], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合", "code": "CN", "subObject": [{"title": "中国驾照原件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "HK", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照+国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合(任选其一)", "code": "OH", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}, {"title": "中国驾照原件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5, "optimalType": "-1"}, {"title": "台湾驾照+国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31, "optimalType": "-1"}]}]}, {"title": "注意：", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "所有南加州客户必须提供居住证明（即带有您姓名和当前地址的公用事业账单）以及转让给租赁车辆的完整汽车保险证明。当地租客在前台购买碰撞损害豁免险不是选择项。 "}]}], "type": 10}], "summaryTitle": "驾照", "summaryObject": [{"title": "非中国驾照政策", "contentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，仅支持中国（含港澳台）护照和驾照持有者预订。若您持非中国（含港澳台）护照，则有无法取车或者价格变化的风险，但是仍然可以预订；若持非中国（含港澳台）驾照，则无法在本平台预订，建议您前往Trip下单。"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "2", "stringObjs": [{"content": "非中国驾照"}]}]}]}, {"title": "主驾驶员名下国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店", "不支持卡面带银联标志"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6}, {"title": "接受的信用卡", "content": ["维萨，万事达", "不支持卡面带银联标志"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png"]}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,654.08-¥21,924.45），还车后30-60天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,655-¥21,925）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3, "summaryTitle": "提车凭证"}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在20小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车时间可免费取消;取车时间后可免费取消"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2023年11月8日16:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2023年11月8日16:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2023年11月8日16:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2023年11月8日16:00", "subTitle": "支付完成至取车时间", "description": "可免费取消", "showFree": true}, {"title": "2023年11月8日16:00后", "subTitle": "取车时间后", "description": "可免费取消", "showFree": true}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,654.08-¥21,924.45），还车后30-60天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,655-¥21,925）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}, {"title": "长租政策", "content": ["请注意供应商不接受28天以上的连续租车，若需长租请联系供应商沟通。"], "type": 52, "code": "2", "sortNum": 8}], "sortNum": 7}], "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 16, "custumerTotalPrice": 225, "custumerDailyPrice": 75, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "localTotalPrice": 225, "localDailyPrice": 75.0, "localCurrencyCode": "CNY", "desc": "驾乘意外险短描述", "longDesc": "驾乘意外险长描述", "noCoverageContent": ["驾乘意外险不保障内容"], "typeCode": "ACCIDENT_COVER", "days": 3, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "version": "OPI1128778955596234752", "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "1、报警并联系门店-卡拉比", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。"], "type": 1}, {"title": "2、还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额和，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": " https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}, {"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 5}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 6}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 7}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 8}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"], "briefInsuranceItems": [{"code": "CDW", "name": "碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "coverageWithoutPlatformInsurance": "原起赔额US$ 2,500可赔", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)", "coverageWithPlatformInsuranceV2": "*起赔额US$ 2,500.00(约¥18,270)及以下部分由国内保险公司提供"}], "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "国内保险公司承担*", "content": ["全部损失（据实承担）"]}, {"title": "客户或承租方承担", "content": ["¥0.00"]}]}}, {"name": "第三者责任险", "insuranceDetail": [{"packageId": 318204, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}]}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}], "insuranceCompareItems": [{"description": "0起赔额", "type": "LDW", "descriptionColorCode": "GREEN"}, {"contains": true, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": false, "type": "PI"}, {"contains": true, "type": "RAP"}, {"contains": true, "type": "CANCEL"}, {"contains": false, "type": "ACCIDENT"}]}, {"insPackageId": 770, "insuranceItems": [{"code": "CDW", "name": "车辆碰撞保障", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额US$ 2,500可赔", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)", "coverageWithPlatformInsuranceV2": "*起赔额US$ 2,500.00(约¥18,270)及以下部分由国内保险公司提供"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行承担自付额以上的费用。*实际赔付范围与标准以门店合同为准 -- 修改后"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["US$ 2,500.00(约¥18,270)以上部分"]}, {"title": "国内保险公司承担*", "content": ["US$ 2,500.00(约¥18,270)及以下部分（据实承担）"]}, {"title": "客户或承租方承担", "content": ["¥0.00"]}]}}, {"code": "TP", "name": "车辆盗抢险", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额US$ 2,500可赔", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)", "coverageWithPlatformInsuranceV2": "*起赔额US$ 2,500.00(约¥18,270)及以下部分由国内保险公司提供"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["US$ 2,500.00(约¥18,270)以上部分"]}, {"title": "国内保险公司承担*", "content": ["US$ 2,500.00(约¥18,270)及以下部分（据实承担）"]}, {"title": "客户或承租方承担", "content": ["¥0.00"]}]}}, {"code": "TPL", "name": "第三者责任险", "description": "保障第三方车辆或人员伤害损失", "shortDescription": "保障对第三方车辆或人员造成伤害的损失", "longDescription": "可避免由第三方事故导致的高额赔偿，强烈建议购买。", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 318204, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥75/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥75/天"}], "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}], "ctripInsuranceIds": [15, 16], "combinations": [{"bomCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 1090, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 3269, "payMode": 2, "packageId": 318204}], "productInfoList": [{"productCode": "2", "bomGroupCode": "205889_6881_CDW_FRFB_LM_TP_TPL_Taxes_Taxes_0_0", "priceInfoList": [{"currentCarPrice": 2969, "currentDailyPrice": 1090, "currentTotalPrice": 3269, "localCarPrice": 406.3, "localDailyPrice": 135.43, "localTotalPrice": 406.3, "localCurrencyCode": "USD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 3269, "localOnewayfee": 0.0, "localPoaPrice": 0.0, "localPrepaidPrice": 406.3, "payMode": 2, "productId": "2318204", "packageId": 318204, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": "", "vendorVehicleId": "87448_63746030469"}, "exchangeRate": 7.30815, "mileInfo": {"name": "里程限制每天200mile", "isLimited": true, "distance": 200.0, "distanceUnit": "mile", "periodUnit": "每天", "chargeAmount": 0.55, "chargeUnit": "mile", "quantity": 1.0, "desc": "里程限制每天200mile，超出里程按照US$ 0.55/mile计算。\n*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"}, "confirmInfo": {"confirmTitle": "20小时内确认", "confirmDesc": "预订此产品后供应商将在20小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 20.0}, "cancelRule": {"isTotalLoss": false, "isFreeCancel": true, "isFreeCancelNow": true, "hours": -1, "cancelDescription": "支付完成至取车时间可免费取消;取车时间后可免费取消"}, "ageRestriction": {"description": "驾驶员年龄要求：25-65周岁", "minDriverAge": 25, "maxDriverAge": 65, "youngDriverAgeDesc": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "oldDriverAgeDesc": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月"}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}], "depositCurrencyCode": "USD", "maxDeposit": 3000.0, "minDeposit": 500.0}, "allTags": [{"title": "20小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在20小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车时间可免费取消;取车时间后可免费取消", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "券减¥16", "category": 3, "type": 3, "code": "3", "description": "券减¥16,可享立减￥16/天（3天共减￥50）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}, {"title": "四月特惠", "category": 3, "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "sortNum": 10000, "colorCode": "3", "labelCode": "3834"}, {"title": "国庆特惠", "category": 3, "type": 1, "description": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。", "sortNum": 10000, "colorCode": "3", "labelCode": "3854"}, {"title": "里程限制每天200mile", "category": 4, "type": 2, "code": "4", "description": "里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "sortNum": 125, "colorCode": "4", "labelCode": "3580"}], "insuranceDetails": [{"code": "CDW", "name": "车辆碰撞保障", "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0}, {"code": "TP", "name": "车辆盗抢险", "currencyCode": "USD", "minExcess": 2500.0, "maxExcess": 2500.0}, {"code": "TPL", "name": "第三者责任险"}], "chargeList": [], "promotionInfo": {"deductionAmount": 50}, "vendorPromotionList": [{"type": 1, "title": "四月特惠", "deductionPercent": 0.0, "code": "3834", "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。"}, {"type": 1, "title": "国庆特惠", "deductionPercent": 0.0, "code": "3854", "longTag": "国庆特惠", "longDesc": "预订带有“国庆特惠”标签的产品可享专属优惠活动，显示价格已为直降后优惠价。具体优惠由此产品相关供应商提供，如有优惠相关问题，请按“车辆详情页 - 取车门店&还车门店 - 地图及指引”中信息 联系相关供应商咨询。"}], "pkgSellingRuleId": 318204}], "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 25.0, "localTotalPrice": 75, "localCurrencyCode": "CNY", "currentDailyPrice": 25, "currentTotalPrice": 75, "currentCurrencyCode": "CNY", "uniqueCode": "15"}, {"name": "驾乘意外险", "localDailyPrice": 75.0, "localTotalPrice": 225, "localCurrencyCode": "CNY", "currentDailyPrice": 75, "currentTotalPrice": 225, "currentCurrencyCode": "CNY", "uniqueCode": "16"}], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "CDW", "name": "车辆碰撞保障", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "TP", "name": "车辆盗抢险", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "TPL", "name": "第三者责任险", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "LM", "name": "里程限制每天200mile", "desc": "里程限制每天200mile，超出里程按照US$ 0.55/mile计算。\n*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}], "pickUpMaterials": [{"title": "身份证明文件", "subTitle": "中国大陆护照原件", "type": 0, "subObject": [{"title": "中国大陆护照原件", "subTitle": "中国大陆公民", "code": "CN", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国香港护照原件", "subTitle": "中国香港公民", "code": "HK", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国澳门护照原件", "subTitle": "中国澳门公民", "code": "MO", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "中国台湾护照原件", "subTitle": "中国台湾公民", "code": "TW", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}], "type": 17}]}, {"title": "其他地区护照原件", "subTitle": "其他国家公民", "code": "OH", "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议前往Trip下单>"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "护照、驾照发证国家/地区须一致方可成功取车"}]}, {"contentStyle": "1", "stringObjs": [{"content": "因商业条款限制，若您持非中国（含港澳台）护照，有无法取车或者变价的风险。建议您使用与驾照发证国家/地区一致的护照进行取车或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}]}], "summaryTitle": "护照"}, {"title": "驾照要求", "subTitle": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满12个月", "驾驶员年龄要求：25-65周岁"], "type": 1, "subObject": [{"title": "驾照要求", "contentObject": [{"contentStyle": "1", "stringObjs": [{"content": "当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原件和下列驾照组合的实体件租车，电子版或照片将不被认可。"}]}, {"contentStyle": "1", "stringObjs": [{"content": "所有驾驶员驾龄必须至少满12个月"}]}, {"contentStyle": "1", "stringObjs": [{"content": "驾驶员年龄要求：25-65周岁"}]}, {"contentStyle": "1", "stringObjs": [{"content": "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"}]}, {"contentStyle": "1", "stringObjs": [{"content": "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。"}]}], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合", "code": "CN", "subObject": [{"title": "中国驾照原件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "HK", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照+国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31, "optimalType": "-1"}]}, {"title": "门店支持以下驾照组合(任选其一)", "code": "OH", "subObject": [{"title": "驾驶员本国驾照+国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1, "optimalType": "-1"}, {"title": "中国驾照原件+当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 0, "code": "CDL,OLT", "sortNum": 5, "optimalType": "-1"}, {"title": "台湾驾照+国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31, "optimalType": "-1"}]}]}, {"title": "注意：", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "所有南加州客户必须提供居住证明（即带有您姓名和当前地址的公用事业账单）以及转让给租赁车辆的完整汽车保险证明。当地租客在前台购买碰撞损害豁免险不是选择项。 "}]}], "type": 10}], "summaryTitle": "驾照", "summaryObject": [{"title": "非中国驾照政策", "contentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，仅支持中国（含港澳台）护照和驾照持有者预订。若您持非中国（含港澳台）护照，则有无法取车或者价格变化的风险，但是仍然可以预订；若持非中国（含港澳台）驾照，则无法在本平台预订，建议您前往Trip下单。"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "2", "stringObjs": [{"content": "非中国驾照"}]}]}]}, {"title": "主驾驶员名下国际信用卡", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店", "不支持卡面带银联标志"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。可能需要两张信用卡，详情请咨询门店", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6}, {"title": "接受的信用卡", "content": ["维萨，万事达", "不支持卡面带银联标志"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png"]}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,654.08-¥21,924.45），还车后30-60天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,655-¥21,925）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3, "summaryTitle": "提车凭证"}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在20小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车时间可免费取消;取车时间后可免费取消"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2023年11月8日16:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2023年11月8日16:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2023年11月8日16:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2023年11月8日16:00", "subTitle": "支付完成至取车时间", "description": "可免费取消", "showFree": true}, {"title": "2023年11月8日16:00后", "subTitle": "取车时间后", "description": "可免费取消", "showFree": true}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,654.08-¥21,924.45），还车后30-60天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,655-¥21,925）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["里程限制每天200mile，超出里程限制需额外支付每mileUSD0.55，不包含税。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "燃油政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-65周岁", "18~25周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "65~85周岁驾驶员可能会被租车公司收取风险驾驶金，税费和费用需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}, {"title": "长租政策", "content": ["请注意供应商不接受28天以上的连续租车，若需长租请联系供应商沟通。"], "type": 52, "code": "2", "sortNum": 8}], "sortNum": 7}], "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 16, "custumerTotalPrice": 225, "custumerDailyPrice": 75, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "localTotalPrice": 225, "localDailyPrice": 75.0, "localCurrencyCode": "CNY", "desc": "驾乘意外险短描述", "longDesc": "驾乘意外险长描述", "noCoverageContent": ["驾乘意外险不保障内容"], "typeCode": "ACCIDENT_COVER", "days": 3, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "version": "OPI1128778955596234752", "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "1、报警并联系门店-卡拉比", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。"], "type": 1}, {"title": "2、还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额和，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": " https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}, {"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 5}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 6}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 7}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 8}]}, {"subTitle": "您购买的套餐含国内保险公司提供的驾乘意外险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "subObject": [{"title": "驾乘意外险理赔流程步骤一名称", "content": ["驾乘意外险理赔流程步骤一描述"], "type": 1}, {"title": "驾乘意外险理赔流程步骤二名称", "content": ["驾乘意外险理赔流程步骤二描述"], "type": 2}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"], "briefInsuranceItems": [{"code": "CDW", "name": "碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"currencyCode": "USD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "coverageWithoutPlatformInsurance": "原起赔额US$ 2,500可赔", "coverageWithoutPlatformInsuranceV2": "起赔额US$ 2,500.00(约¥18,270)", "coverageWithPlatformInsuranceV2": "*起赔额US$ 2,500.00(约¥18,270)及以下部分由国内保险公司提供"}], "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "国内保险公司承担*", "content": ["全部损失（据实承担）"]}, {"title": "客户或承租方承担", "content": ["¥0.00"]}]}}, {"name": "第三者责任险", "insuranceDetail": [{"packageId": 318204, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}]}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥75/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥75/天"}], "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}], "insuranceCompareItems": [{"description": "0起赔额", "type": "LDW", "descriptionColorCode": "GREEN"}, {"contains": true, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": true, "type": "PI"}, {"contains": true, "type": "RAP"}, {"contains": true, "type": "CANCEL"}, {"contains": true, "type": "ACCIDENT"}]}], "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}, "promptInfos": [{"title": "押金汇率说明", "contents": [{"stringObjs": [{"content": "押金所需的人民币金额根据当前汇率计算，实际所需人民币金额以用户到店支付时或者授权免押时的汇率为准。"}]}], "type": 12}, {"title": "洛杉矶国际机场 is a popular location on your selected dates. Rates are likely to rise.", "subTitle": "Book now! Don't miss out on low prices.", "type": 29}], "platformInsuranceExtra": {"encourageTitle": "Extra Protection for Your Rental", "encourageDesc": "Your package does not cover you for all losses and damages. Protect yourself from unexpected damage costs with RentalCover.com's protection", "claimProcedure": [{"type": 1, "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。"]}, {"type": 2, "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额和，门店将原路退还差价。请保留所有处理单据，以备后续理赔。"]}]}, "requestInfo": {"pickupDate": "2023-11-08 16:00:00", "pickupLocationName": "洛杉矶国际机场", "returnDate": "2023-11-11 16:00:00", "returnLocationName": "洛杉矶国际机场", "sourceCountryId": 1, "pLatitude": 33.941589, "rLatitude": 33.941589, "rLongitude": -118.40853, "pLongitude": -118.40853, "pDate": "20231108160000", "rDate": "20231111160000", "pCityId": 347, "rCityId": 347}, "licenceCountryPolicy": {"title": "驾照", "type": 15, "subObject": [{"title": "中国大陆驾照", "summaryContent": ["您需携带当地语言公证件和中国驾照原件才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "CN", "optimalType": "1"}, {"title": "中国香港驾照", "summaryContent": ["您需携带国际驾照和驾驶员本国驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "HK", "optimalType": "1"}, {"title": "中国澳门驾照", "summaryContent": ["您需携带国际驾照和驾驶员本国驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "MO", "optimalType": "1"}, {"title": "中国台湾驾照", "summaryContent": ["您需携带国际驾照和台湾驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "TW", "optimalType": "1"}], "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "持非中国（含港澳台）驾照无法预订。建议更换驾驶员或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17}]}, "crossPolicy": {"crossLocationsInfos": [{"crossType": 3, "crossTypeName": "跨境政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。"]}, {"crossType": 1, "crossTypeName": "跨岛政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨岛，建议您更换其它租车公司或车型组。"]}, {"crossType": 2, "crossTypeName": "跨州政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨州/省，建议您更换其它租车公司或车型组。"]}], "notes": ["若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。"], "title": "旅行限制"}, "locationPrompt": "美国地区偶发砸窗盗窃，道路救援费用较高，建议升级含安心补充险的套餐，保障范围更广，比在门店购买更便宜。", "osdCompareTitle": ["车辆碰撞及盗抢", "玻璃轮胎", "三者险", "人身财物险", "道路救援补偿", "旅行取消", "意外津贴/住院津贴"]}