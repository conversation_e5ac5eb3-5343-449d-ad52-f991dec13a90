{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "e4d15827-47fb-4238-be4d-9d1c6fcea325", "extMap": {"isKarabi": "1", "rentalDays": "1"}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1698397234960+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "1451150459431203033"}, {"Id": "RootMessageId", "Value": "100025527-0a063db3-471777-2403"}]}, "isSoldOut": false, "isHotCity": true, "pickupStoreInfo": {"storeCode": "81592", "bizVendorCode": "14010", "telephone": "61 (0) 2 92079400", "storeName": "SYDNEY AIRPORT", "address": "TERMINAL BUILDINGS,SYDNEY AIRPORT", "longitude": 151.1786, "latitude": -33.9334, "storeGuild": "门店位于机场的国内航站楼内，步行可达。在到达大厅根据Car Rental指示牌找到柜台，办理手续后到停车场取车。", "storeWay": "航站楼内，步行可达", "workTime": {"openTimeDesc": "{\"\":\"07:00 - 22:30\"}", "description": ""}, "storeServiceList": [], "countryId": 15, "countryName": "澳大利亚", "provinceId": 10285, "provinceName": "新南威尔士", "cityId": 501, "cityName": "悉尼", "isAirportStore": true, "vendorStoreCode": "SYDT01", "continentId": 3, "continentName": "大洋洲"}, "returnStoreInfo": {"storeCode": "81592", "bizVendorCode": "14010", "telephone": "61 (0) 2 92079400", "storeName": "SYDNEY AIRPORT", "address": "TERMINAL BUILDINGS,SYDNEY AIRPORT", "longitude": 151.1786, "latitude": -33.9334, "storeGuild": "请提前联系门店确认取还车位置。", "storeWay": "航站楼内", "workTime": {"openTimeDesc": "{\"\":\"07:00 - 22:30\"}", "description": ""}, "storeServiceList": [], "countryId": 15, "countryName": "澳大利亚", "provinceId": 10285, "provinceName": "新南威尔士", "cityId": 501, "cityName": "悉尼", "isAirportStore": true, "vendorStoreCode": "SYDT01", "continentId": 3, "continentName": "大洋洲"}, "vendorInfo": {"bizVendorCode": "14010", "vendorName": "Europcar", "vendorImageUrl": "//pages.trip.com/cars/image/totrip/157c4e0d-0cd0-43f4-8ba7-03ce4d5fbfb1.png", "vendorCode": "SD0006", "isBroker": false, "platformCode": "", "platformName": "Europcar", "haveCoupon": true, "vendorTag": {"title": "全球连锁", "sortNum": 0}}, "vehicleInfo": {"brandId": 215, "brandEName": "MG", "name": "名爵 312122", "zhName": "名爵 312122", "vehicleCode": "7744", "imageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg", "groupCode": "1", "groupSubClassCode": "1001", "groupName": "经济型轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 4, "doorNo": 4, "luggageNo": 1, "imageList": ["https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "userRealImageCount": 0, "isSpecialized": false, "hasConditioner": true, "conditionerDesc": "A/C", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14010", "vendorName": "Europcar", "vendorLogo": "//pages.trip.com/cars/image/totrip/157c4e0d-0cd0-43f4-8ba7-03ce4d5fbfb1.png", "similarVehicleInfos": [{"vehicleCode": "7744", "vehicleName": "名爵 312122", "vehicleImageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"}]}], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "groupSubName": "小型轿车"}, "flightDelayRule": {"title": "航班延误保留政策", "description": "航班延误保留至24:00（当天）", "rules": [{"title": "航班延误保留至24:00（当天）"}, {"title": "建议您填写航班号，如遇延误请携带机票前往取车"}, {"title": "补充政策：", "descs": ["a", "d"]}]}, "commentInfo": {"level": "", "commentCount": 0, "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=CALABI_STORE_QUERY&calabiStoreId=81592&calabiVehicleId=7744&vehicleName=名爵 312122&productCategoryId=34&isHideNavBar=YES"}, "packageInfos": [{"insPackageId": 2, "isDefault": true, "packageName": "基础套餐", "currencyCode": "CNY", "defaultBomCode": "81592_7744_LDW_PAI_STS_TPL_Taxes_Taxes_ULM_0_0", "defaultPackageId": 1773, "guaranteeDegree": 2.5, "naked": false, "insuranceNames": ["车损盗抢险", "人身意外险", "第三者责任险"], "lowestDailyPrice": 310, "gapPrice": 0, "stepPrice": 0, "description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失", "packageType": 0}, {"insPackageId": 258, "isDefault": false, "packageName": "优享套餐", "currencyCode": "CNY", "defaultBomCode": "81592_7744_LDW_PAI_STS_TPL_Taxes_Taxes_ULM_0_0", "defaultPackageId": 1773, "guaranteeDegree": 4.5, "naked": false, "insuranceNames": ["车损盗抢险", "人身意外险", "第三者责任险", "安心补充险"], "lowestDailyPrice": 335, "gapPrice": 25, "stepPrice": 25}], "productDetails": [{"insPackageId": 2, "insuranceItems": [{"code": "LDW", "name": "车损盗抢险", "description": "保障车辆碰撞、被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1773, "currencyCode": "AUD", "minExcess": 5000.0, "maxExcess": 5000.0, "excessShortDesc": "起赔额AU$ 5,000", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "1"}, {"code": "TPL", "name": "第三者责任险", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1773, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2"}, {"code": "PAI", "name": "人身意外险", "description": "保障全车人员意外伤害", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1773, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "3"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}]}], "combinations": [{"bomCode": "81592_7744_LDW_PAI_STS_TPL_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 310, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 310, "payMode": 3, "packageId": 1773}], "productInfoList": [{"productCode": "2", "bomGroupCode": "81592_7744_LDW_PAI_STS_TPL_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 310.0, "currentDailyPrice": 310, "currentTotalPrice": 310, "localCarPrice": 66.94, "localDailyPrice": 66.94, "localTotalPrice": 66.94, "localCurrencyCode": "AUD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 270, "currentPrepaidPrice": 40, "localOnewayfee": 0.0, "localPoaPrice": 58.21, "localPrepaidPrice": 8.73, "isContainOnewayFee": false, "payMode": 3, "productId": "21773", "packageId": 1773, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 4.638, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "24小时内确认", "confirmDesc": "预订此产品后供应商将在24小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 24.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 72, "cancelDescription": "支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥732.00）"}, "ageRestriction": {"description": "驾驶员年龄要求：25-70周岁", "minDriverAge": 25, "maxDriverAge": 70, "licenceAge": 3, "licenceAgeDesc": "所有驾驶员驾龄必须至少满36个月", "youngDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "USD", "maxDeposit": 3000.0, "minDeposit": 500.0}, "allTags": [{"title": "24小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在24小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥732.00）", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "四月特惠", "category": 3, "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "sortNum": 10000, "colorCode": "3", "labelCode": "3834"}], "insuranceDetails": [{"code": "LDW", "name": "车损盗抢险", "currencyCode": "AUD", "minExcess": 5000.0, "maxExcess": 5000.0}, {"code": "TPL", "name": "第三者责任险"}, {"code": "PAI", "name": "人身意外险"}], "chargeList": [{"code": "", "desc": "", "payMode": 3, "netAmount": 12.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 25.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 6.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 49.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 41.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 7.5, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [{"type": 1, "title": "四月特惠", "deductionPercent": 0.0, "code": "3834", "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。"}], "pkgSellingRuleId": 1773}], "needFlightNo": false, "equipments": [], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "LDW", "name": "车损盗抢险", "desc": "保障车辆碰撞、被盗的损失", "sortNum": 3}, {"code": "PAI", "name": "人身意外险", "desc": "保障全车人员意外伤害", "sortNum": 3}, {"code": "TPL", "name": "第三者责任险", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "STS", "name": "等油取还", "desc": "取车时与还车时油量保持一致", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "身份证明文件", "content": ["护照、驾照发证国家/地区须一致方可成功取车"], "type": 0, "subObject": [{"title": "中国大陆护照原件", "code": "CN"}, {"title": "中国香港护照原件", "code": "HK"}, {"title": "中国澳门护照原件", "code": "MO"}, {"title": "中国台湾护照原件", "code": "TW"}, {"title": "其他地区护照原件", "code": "OH"}]}, {"title": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满36个月", "驾驶员年龄要求：25-70周岁"], "type": 1, "subObject": [{"content": ["当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。", "所有驾驶员驾龄必须至少满36个月", "驾驶员年龄要求：25-70周岁"], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "code": "CN", "subObject": [{"title": "中国驾照原件 + 驾照国际翻译认证件", "content": ["中国驾照原件：中国驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 0, "code": "CDL,IDL", "sortNum": 2}, {"title": "中国驾照原件 + 英文公证件", "content": ["中国驾照原件：中国驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 0, "code": "CDL,OET", "sortNum": 4}, {"title": "中国驾照原件 + NAATI翻译件", "content": ["中国驾照原件：中国驾照原件", "NAATI翻译件：由澳大利亚NAATI机构颁发的驾驶员本国驾照翻译文件"], "type": 0, "code": "CDL,NAATI", "sortNum": 8}], "sortNum": 0}, {"title": "门店支持以下驾照组合", "code": "HK", "subObject": [{"title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "HKDL,IDP", "sortNum": 37}], "sortNum": 1}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "MCDL,IDP", "sortNum": 29}], "sortNum": 2}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31}], "sortNum": 3}, {"title": "门店支持以下驾照组合", "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1}], "sortNum": 4}]}]}, {"title": "主驾驶员名下国际信用卡（2张）", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡", "支持银联双币卡（卡面可带银联标志）", "若无法提供信用卡，该门店也支持现金支付"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6, "note": "信用卡支付说明（中）"}, {"title": "接受的信用卡", "content": ["维萨，万事达，美国运通，大来卡", "支持银联双币卡（卡面可带银联标志）"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png"]}, {"title": "现金支付说明", "content": ["现金支付说明（中）"], "type": 19}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,658.6-¥21,951.6），还车后30-60天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。\n取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,659-¥21,952）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3, "summaryTitle": "提车凭证"}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在24小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥732.00）"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2023年11月4日10:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2023年11月4日10:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2023年11月4日10:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2023年11月4日10:00", "subTitle": "支付完成至取车前72小时", "description": "可免费取消", "showFree": true}, {"title": "2023年11月4日10:00后", "subTitle": "取车前72小时后", "description": "取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥732.00）", "showFree": false}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,658.6-¥21,951.6），还车后30-60天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。\n取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,659-¥21,952）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "燃油政策", "content": ["等油取还", "取车时与还车时油量保持一致"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-70周岁", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "燃油政策", "content": ["等油取还", "取车时与还车时油量保持一致"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-70周岁", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}, {"title": "长租政策", "content": ["请注意供应商不接受28天以上的连续租车，若需长租请联系供应商沟通。"], "type": 52, "code": "2", "sortNum": 8}], "sortNum": 7}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "安心补充险", "id": 15, "custumerTotalPrice": 25, "custumerDailyPrice": 25, "code": "MP18021541PK00080231", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localTotalPrice": 25, "localDailyPrice": 25.0, "localCurrencyCode": "CNY", "desc": "赔偿车损/盗抢的自付部分，以及玻璃、轮胎、底盘破损，补偿道路救援费", "longDesc": "由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方：", "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "FULL_COVERAGE", "days": 1, "customerCurrencyCode": "CNY"}, "url": " https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf", "version": "OPI1146924666036682752"}}], "claimsProcess": [{"subTitle": "test", "subObject": [{"title": "1/erwte", "content": ["fdgdhfhfhjfsgg"], "type": 1}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车", "description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"]}, {"insPackageId": 258, "insuranceItems": [{"code": "LDW", "name": "车损盗抢险", "description": "保障车辆碰撞、被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1773, "currencyCode": "AUD", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额AU$ 5,000可赔"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭、被盗被抢产生的损失，将由保险公司承担起赔额以上的费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "1"}, {"code": "TPL", "name": "第三者责任险", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1773, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，在起赔额以上保额以内的部分将由保险公司赔付。 *实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2"}, {"code": "PAI", "name": "人身意外险", "description": "保障全车人员意外伤害", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 1773, "excessShortDesc": "", "excessLongDesc": "", "coverageShortDesc": "", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["保障车内人员，在正常使用租赁车辆期间，发生意外所造成的人身伤害或身故带来的医疗费用。*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "content": ["以车行合同披露为准"]}, "groupCode": "3"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}]}], "ctripInsuranceIds": [15], "combinations": [{"bomCode": "81592_7744_LDW_PAI_STS_TPL_Taxes_Taxes_ULM_0_0", "title": "", "codes": [], "currency": "CNY", "dayPrice": 335, "gapPrice": 0, "stepPrice": 0, "hike": true, "totalPrice": 335, "payMode": 3, "packageId": 1773}], "productInfoList": [{"productCode": "2", "bomGroupCode": "81592_7744_LDW_PAI_STS_TPL_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 310.0, "currentDailyPrice": 335, "currentTotalPrice": 335, "localCarPrice": 66.94, "localDailyPrice": 66.94, "localTotalPrice": 66.94, "localCurrencyCode": "AUD", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 270, "currentPrepaidPrice": 65, "localOnewayfee": 0.0, "localPoaPrice": 58.21, "localPrepaidPrice": 8.73, "payMode": 3, "productId": "21773", "packageId": 1773, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 4.638, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "24小时内确认", "confirmDesc": "预订此产品后供应商将在24小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 24.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 72, "cancelDescription": "支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥732.00）"}, "ageRestriction": {"description": "驾驶员年龄要求：25-70周岁", "minDriverAge": 25, "maxDriverAge": 70, "licenceAge": 3, "licenceAgeDesc": "所有驾驶员驾龄必须至少满36个月", "youngDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "USD", "maxDeposit": 3000.0, "minDeposit": 500.0}, "allTags": [{"title": "24小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在24小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "限时免费取消", "category": 1, "type": 1, "code": "2", "description": "支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥732.00）", "sortNum": 15, "colorCode": "1", "labelCode": "3581"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品含第三者保障。", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "四月特惠", "category": 3, "type": 1, "description": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。", "sortNum": 10000, "colorCode": "3", "labelCode": "3834"}], "insuranceDetails": [{"code": "LDW", "name": "车损盗抢险", "currencyCode": "AUD", "minExcess": 5000.0, "maxExcess": 5000.0}, {"code": "TPL", "name": "第三者责任险"}, {"code": "PAI", "name": "人身意外险"}], "chargeList": [{"code": "", "desc": "", "payMode": 3, "netAmount": 12.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 25.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 6.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 49.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "desc": "", "payMode": 3, "netAmount": 41.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": false}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 7.5, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": true}, {"code": "", "payMode": 3, "netAmount": 0.0, "dueAmount": 0.0, "currency": "AUD", "isIncludedInRate": true}], "promotionInfo": {}, "vendorPromotionList": [{"type": 1, "title": "四月特惠", "deductionPercent": 0.0, "code": "3834", "longTag": "四月特惠", "longDesc": "预订Chic带有“四月特惠”标签的产品可享受专属优惠活动，显示价格已为直降后优惠价。优惠预定时间为当地时间即日起至2023/02/28， 取车时间为当地时间2023/04/01至2023/04/30。"}], "pkgSellingRuleId": 1773}], "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 25.0, "localTotalPrice": 25, "localCurrencyCode": "CNY", "currentDailyPrice": 25, "currentTotalPrice": 25, "currentCurrencyCode": "CNY", "uniqueCode": "15"}], "packageItems": [{"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "LDW", "name": "车损盗抢险", "desc": "保障车辆碰撞、被盗的损失", "sortNum": 3}, {"code": "PAI", "name": "人身意外险", "desc": "保障全车人员意外伤害", "sortNum": 3}, {"code": "TPL", "name": "第三者责任险", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "STS", "name": "等油取还", "desc": "取车时与还车时油量保持一致", "sortNum": 4}], "pickUpMaterials": [{"title": "身份证明文件", "content": ["护照、驾照发证国家/地区须一致方可成功取车"], "type": 0, "subObject": [{"title": "中国大陆护照原件", "code": "CN"}, {"title": "中国香港护照原件", "code": "HK"}, {"title": "中国澳门护照原件", "code": "MO"}, {"title": "中国台湾护照原件", "code": "TW"}, {"title": "其他地区护照原件", "code": "OH"}]}, {"title": "驾照要求", "summaryContent": ["所有驾驶员驾龄必须至少满36个月", "驾驶员年龄要求：25-70周岁"], "type": 1, "subObject": [{"content": ["当你取车的时候，主驾驶员和其他额外驾驶员需要提供他们名下的驾驶证原证。", "所有驾驶员驾龄必须至少满36个月", "驾驶员年龄要求：25-70周岁"], "type": 4}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "code": "CN", "subObject": [{"title": "中国驾照原件 + 驾照国际翻译认证件", "content": ["中国驾照原件：中国驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 0, "code": "CDL,IDL", "sortNum": 2}, {"title": "中国驾照原件 + 英文公证件", "content": ["中国驾照原件：中国驾照原件", "英文公证件：由公证处颁发的驾驶员本国驾照英文翻译文件"], "type": 0, "code": "CDL,OET", "sortNum": 4}, {"title": "中国驾照原件 + NAATI翻译件", "content": ["中国驾照原件：中国驾照原件", "NAATI翻译件：由澳大利亚NAATI机构颁发的驾驶员本国驾照翻译文件"], "type": 0, "code": "CDL,NAATI", "sortNum": 8}], "sortNum": 0}, {"title": "门店支持以下驾照组合", "code": "HK", "subObject": [{"title": "香港驾照 + 国际驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "HKDL,IDP", "sortNum": 37}], "sortNum": 1}, {"title": "门店支持以下驾照组合", "code": "MO", "subObject": [{"title": "澳门驾照 + 国际驾照", "content": ["澳门驾照：由中国澳门特别行政区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "MCDL,IDP", "sortNum": 29}], "sortNum": 2}, {"title": "门店支持以下驾照组合", "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "TWDL,IDP", "sortNum": 31}], "sortNum": 3}, {"title": "门店支持以下驾照组合", "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 0, "code": "ODL,IDP", "sortNum": 1}], "sortNum": 4}]}]}, {"title": "主驾驶员名下国际信用卡（2张）", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡"], "summaryContent": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡", "支持银联双币卡（卡面可带银联标志）", "若无法提供信用卡，该门店也支持现金支付"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡", "卡面所示姓名与主驾驶员护照姓名一致"], "type": 6, "note": "信用卡支付说明（中）"}, {"title": "接受的信用卡", "content": ["维萨，万事达，美国运通，大来卡", "支持银联双币卡（卡面可带银联标志）"], "type": 7, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png"]}, {"title": "现金支付说明", "content": ["现金支付说明（中）"], "type": 19}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,658.6-¥21,951.6），还车后30-60天内退还。"], "type": 8, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。\n取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,659-¥21,952）"}]}], "showFree": false, "positiveDesc": "可退"}]}], "summaryTitle": "信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3, "summaryTitle": "提车凭证"}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在24小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前72小时可免费取消;取车前72小时后取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥732.00）"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "支付完成至2023年11月4日10:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2023年11月4日10:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2023年11月4日10:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2023年11月4日10:00", "subTitle": "支付完成至取车前72小时", "description": "可免费取消", "showFree": true}, {"title": "2023年11月4日10:00后", "subTitle": "取车前72小时后", "description": "取消将收取全部租金作为违约金，最多收取US$ 100.00（约¥732.00）", "showFree": false}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，约租车费用+USD500-USD3,000（约¥3,658.6-¥21,951.6），还车后30-60天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "取车时刷取押金预授权，还车后30-60天内退还。\n取车时用现金支付，还车时退还。", "contents": [{"stringObjs": [{"content": "US$ 500-US$ 3,000（约¥3,659-¥21,952）"}]}], "showFree": false, "positiveDesc": "可退"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "燃油政策", "content": ["等油取还", "取车时与还车时油量保持一致"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-70周岁", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "燃油政策", "content": ["等油取还", "取车时与还车时油量保持一致"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：25-70周岁", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店（或携程）咨询，否则可能会导致您无法取车，建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或携程）确认。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}, {"title": "长租政策", "content": ["请注意供应商不接受28天以上的连续租车，若需长租请联系供应商沟通。"], "type": 52, "code": "2", "sortNum": 8}], "sortNum": 7}], "platformInsurance": {"insurance": {"name": "安心补充险", "id": 15, "custumerTotalPrice": 25, "custumerDailyPrice": 25, "code": "MP18021541PK00080231", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localTotalPrice": 25, "localDailyPrice": 25.0, "localCurrencyCode": "CNY", "desc": "赔偿车损/盗抢的自付部分，以及玻璃、轮胎、底盘破损，补偿道路救援费", "longDesc": "由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方：", "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "FULL_COVERAGE", "days": 1, "customerCurrencyCode": "CNY"}, "url": " https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf", "version": "OPI1146924666036682752"}}], "claimsProcess": [{"subTitle": "test", "subObject": [{"title": "1/erwte", "content": ["fdgdhfhfhjfsgg"], "type": 1}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": " https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}, {"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 5}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 6}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 7}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 8}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"]}], "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}, "promptInfos": [{"title": "押金汇率说明", "contents": [{"stringObjs": [{"content": "押金所需的人民币金额根据当前汇率计算，实际所需人民币金额以用户到店支付时或者授权免押时的汇率为准。"}]}], "type": 12}, {"title": "金斯福德史密斯机场 is a popular location on your selected dates. Rates are likely to rise.", "subTitle": "Book now! Don't miss out on low prices.", "type": 29}], "platformInsuranceExtra": {"encourageTitle": "Extra Protection for Your Rental", "encourageDesc": "Your package does not cover you for all losses and damages. Protect yourself from unexpected damage costs with RentalCover.com's protection", "claimProcedure": [{"type": 1, "content": ["fdgdhfhfhjfsgg"]}]}, "requestInfo": {"pickupDate": "/Date(1699322400000+0800)/", "pickupLocationName": "金斯福德史密斯机场", "returnDate": "/Date(1699408800000+0800)/", "returnLocationName": "金斯福德史密斯机场", "sourceCountryId": 1, "pLatitude": -33.939923, "rLatitude": -33.939923, "rLongitude": 151.175276, "pLongitude": 151.175276, "pDate": "20231107100000", "rDate": "20231108100000", "pCityId": 501, "rCityId": 501}, "licenceCountryPolicy": {"title": "驾照", "type": 15, "subObject": [{"title": "中国大陆驾照", "summaryContent": ["您需携带中国驾照原件和英文公证件（或中国驾照原件和NAATI翻译件、驾照国际翻译认证件和中国驾照原件）才能成功取车", "所有驾驶员驾龄必须至少满36个月"], "type": 1, "code": "CN", "optimalType": "1"}, {"title": "中国香港驾照", "summaryContent": ["您需携带国际驾照和香港驾照才能成功取车", "所有驾驶员驾龄必须至少满36个月"], "type": 1, "code": "HK", "optimalType": "1"}, {"title": "中国澳门驾照", "summaryContent": ["您需携带国际驾照和澳门驾照才能成功取车", "所有驾驶员驾龄必须至少满36个月"], "type": 1, "code": "MO", "optimalType": "1"}, {"title": "中国台湾驾照", "summaryContent": ["您需携带国际驾照和台湾驾照才能成功取车", "所有驾驶员驾龄必须至少满36个月"], "type": 1, "code": "TW", "optimalType": "1"}], "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "持非中国（含港澳台）驾照无法预订。建议更换驾驶员或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17}]}, "crossPolicy": {"crossLocationsInfos": [{"crossType": 3, "crossTypeName": "跨境政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。"]}, {"crossType": 1, "crossTypeName": "跨岛政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨岛，建议您更换其它租车公司或车型组。"]}, {"crossType": 2, "crossTypeName": "跨州政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨州/省，建议您更换其它租车公司或车型组。"]}], "notes": ["若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。"], "title": "旅行限制"}}