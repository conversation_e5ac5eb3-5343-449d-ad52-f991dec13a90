{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "4c84167c-61ce-4b00-9007-fe86ed2a6fd9", "extMap": {"isKarabi": "1", "rentalDays": "2"}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "isSoldOut": false, "isHotCity": true, "pickupStoreInfo": {"storeCode": "228796", "bizVendorCode": "14088187", "telephone": "+66-0801136009", "storeName": "BKK SVP AIRPORT", "address": "999, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> 10540, Thailand", "longitude": 100.746859, "latitude": 13.681888, "storeGuild": "机场3楼8号门等待，致电门店员工，机场停车场内取车。", "storeWay": "门店位于素万那普国际机场航站楼内，步行可达", "workTime": {"openTimeDesc": "{\"\":\"24小时营业\"}", "description": ""}, "storeServiceList": [], "countryId": 4, "countryName": "泰国", "provinceId": 0, "cityId": 359, "cityName": "曼谷", "isAirportStore": true, "showType": 0, "vendorStoreCode": "BKKSVP", "continentId": 1, "continentName": "亚洲"}, "returnStoreInfo": {"storeCode": "228816", "bizVendorCode": "14088187", "telephone": "+66-0801136009", "storeName": "DMK AIRPORT POINT", "address": "222 <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> 10210, Thailand", "longitude": 100.60193, "latitude": 13.919905, "storeGuild": "请提前联系门店确认取还车位置。", "storeWay": "门店距离廊曼国际机场 779.3米", "workTime": {"openTimeDesc": "{\"\":\"24小时营业\"}", "description": "00:00 - 08:59取还车，请提前联系门店，门店预估收取服务费每次THB1,000，费用及税费以实际支付为准。20:00 - 23:59取还车，请提前联系门店，门店预估收取服务费每次THB1,000，费用及税费以实际支付为准。"}, "storeServiceList": [], "countryId": 4, "countryName": "泰国", "provinceId": 11127, "provinceName": "北榄府", "cityId": 36022, "cityName": "北榄", "isAirportStore": false, "showType": 0, "vendorStoreCode": "DMK", "continentId": 1, "continentName": "亚洲"}, "vendorInfo": {"bizVendorCode": "14088187", "vendorName": "8mile", "vendorImageUrl": "https://dimg04.c-ctrip.com/images/0412012000byrilvaE13D.jpg", "vendorCode": "SD1340", "isBroker": false, "platformCode": "", "platformName": "8mile International Group Co.Ltd", "haveCoupon": true, "vendorTag": {"title": "当地连锁", "sortNum": 0}}, "vehicleInfo": {"brandId": 120, "brandEName": "Toyota", "name": "丰田 Vios  ", "zhName": "丰田 Vios  ", "vehicleCode": "12387", "imageUrl": "https://dimg04.c-ctrip.com/images/0RV5612000c6s1er890EA.png", "groupCode": "1", "groupSubClassCode": "1001", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "imageList": ["https://dimg04.c-ctrip.com/images/0AS4012000a09ji478386.png", "https://dimg04.c-ctrip.com/images/0RV5712000c6s10pg0A3D.jpg"], "userRealImageCount": 0, "isSpecialized": false, "hasConditioner": false, "vendorSimilarVehicleInfos": [{"bizVendorCode": "14088187", "vendorName": "8mile", "vendorLogo": "https://dimg04.c-ctrip.com/images/0412012000byrilvaE13D.jpg", "similarVehicleInfos": [{"vehicleCode": "12387", "vehicleName": "丰田 Vios  ", "vehicleImageUrl": "https://dimg04.c-ctrip.com/images/0RV5612000c6s1er890EA.png"}, {"vehicleCode": "18348", "vehicleName": "丰田  yaris", "vehicleImageUrl": "https://dimg04.c-ctrip.com/images/0RV2n12000como9bp9B61.png"}]}], "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV5712000c6s10pg0A3D.jpg", "groupSubName": "小型轿车"}, "flightDelayRule": {"title": "航班延误保留政策", "description": "航班延误保留至21:00（当天）", "rules": [{"title": "航班延误保留至21:00（当天）"}, {"title": "建议您填写航班号，如遇延误请携带机票前往取车"}]}, "commentInfo": {"level": "不错", "commentCount": 28, "overallRating": "4.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 1, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=BRAND_CITY_QUERY&calabiStoreId=228796&calabiVehicleId=12387&vehicleName=丰田 Vios  &productCategoryId=34&isHideNavBar=YES&brandCity=SD1340-359&cityName=曼谷&storeName=BKK SVP AIRPORT"}, "packageInfos": [{"insPackageId": 2, "isDefault": true, "packageName": "基础套餐", "currencyCode": "CNY", "defaultBomCode": "228796_12387_ADD1_CDW_FRFB_TP_TPL_Taxes_Taxes_ULM_0_0", "defaultPackageId": 171072, "guaranteeDegree": 2.0, "naked": false, "insuranceNames": ["车辆碰撞保障", "车辆盗抢保障", "第三者保障"], "lowestDailyPrice": 600, "gapPrice": 0, "stepPrice": 0, "description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失", "packageType": 0, "excessEncourage": "起赔额¥4,034起", "isBasic": 1}, {"insPackageId": 258, "isDefault": false, "packageName": "优享套餐", "currencyCode": "CNY", "defaultBomCode": "228796_12387_ADD1_CDW_FRFB_TP_TPL_Taxes_Taxes_ULM_0_0", "defaultPackageId": 171072, "guaranteeDegree": 4.0, "naked": false, "insuranceNames": ["车辆碰撞保障", "车辆盗抢保障", "第三者保障", "安心补充险"], "lowestDailyPrice": 668, "gapPrice": 68, "stepPrice": 68, "excessEncourage": "起赔额降低为0", "isYouXiang": 1, "youXiangGapPrice": 68}, {"insPackageId": 770, "isDefault": false, "packageName": "尊享套餐", "currencyCode": "CNY", "defaultBomCode": "228796_12387_ADD1_CDW_FRFB_TP_TPL_Taxes_Taxes_ULM_0_0", "defaultPackageId": 171072, "guaranteeDegree": 5.0, "naked": false, "insuranceNames": ["车辆碰撞保障", "车辆盗抢保障", "第三者保障", "安心补充险", "驾乘意外险"], "lowestDailyPrice": 690, "gapPrice": 90, "stepPrice": 90, "descTitle": "全方位保障驾驶车辆、车内人员财物及第三者", "isYouXiang": 0}], "productDetails": [{"insPackageId": 2, "insuranceItems": [{"code": "CDW", "name": "车辆碰撞保障", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "excessShortDesc": "起赔额THB 20,000", "excessLongDesc": "", "minCoverage": 100000.0, "maxCoverage": 100000.0, "coverageShortDesc": "保额：100,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额THB 20,000(约¥4,034)", "subObject": [{"title": "车行承担", "content": ["THB 20,000(约¥4,034)以上部分"]}, {"title": "客户或承租方承担", "content": ["THB 20,000(约¥4,034)及以下部分（据实承担）"]}], "note": "false|THB 20,000(约¥4,034)|THB"}}, {"code": "TP", "name": "车辆盗抢保障", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "excessShortDesc": "起赔额THB 20,000", "excessLongDesc": "", "minCoverage": 100000.0, "maxCoverage": 100000.0, "coverageShortDesc": "保额：100,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额THB 20,000(约¥4,034)", "subObject": [{"title": "车行承担", "content": ["THB 20,000(约¥4,034)以上部分"]}, {"title": "客户或承租方承担", "content": ["THB 20,000(约¥4,034)及以下部分（据实承担）"]}], "note": "false|THB 20,000(约¥4,034)|THB"}}, {"code": "TPL", "name": "第三者保障", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "excessShortDesc": "起赔额THB 20,000", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)"}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2", "excessTable": {"title": "起赔额THB 20,000(约¥4,034)", "subObject": [{"title": "车行承担", "content": ["THB 20,000(约¥4,034)以上部分"]}, {"title": "客户或承租方承担", "content": ["THB 20,000(约¥4,034)及以下部分（据实承担）"]}], "note": "false|THB 20,000(约¥4,034)|THB"}}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "insuranceNotice": "1、驾车人员投保年龄必须符合国际条列，即18周岁以上、70周岁以下；乘客投保年龄范围为0--90周岁，其中被保险人“意外身故、残疾保障“的保险金额71至80周岁为保单所载金额的一半、81至90周岁为四分之一。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010。\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n平安产险交通出行意外伤害保险（互联网版）条款\t注册号：C00001732312022080414693\n平安个人随车物品损失保险条款  注册号：H00001732112017050450351\n平安附加承保地域特约条款\t注册号为：C00001732322018031302341\t\n平安附加适用医疗机构范围约定保险条款\t注册号为：C00001731922019040903801\t\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1703/33.pdf"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 21, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}], "combinations": [{"bomCode": "228796_12387_ADD1_CDW_FRFB_TP_TPL_Taxes_Taxes_ULM_0_0", "title": "1名额外驾驶员", "codes": ["MultiDriver"], "currency": "CNY", "dayPrice": 600, "gapPrice": 0, "stepPrice": 0, "hike": false, "totalPrice": 1200, "payMode": 2, "packageId": 171072}], "productInfoList": [{"productCode": "2", "bomGroupCode": "228796_12387_ADD1_CDW_FRFB_TP_TPL_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 1200, "currentDailyPrice": 600, "currentTotalPrice": 1200, "localCarPrice": 4450.0, "localDailyPrice": 2975.0, "localTotalPrice": 5950.0, "localCurrencyCode": "THB", "currentCurrencyCode": "CNY", "currentOnewayfee": 303, "currentPoaPrice": 0, "currentPrepaidPrice": 1200, "localOnewayfee": 1500.0, "localPoaPrice": 0.0, "localPrepaidPrice": 5950.0, "isContainOnewayFee": false, "payMode": 2, "productId": "2171072", "packageId": 171072, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 0.2017, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "24小时内确认", "confirmDesc": "预订此产品后供应商将在24小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 24.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": false, "hours": 0, "cancelDescription": "支付完成至取车前24小时取消将收取全部租金作为违约金;取车前24小时后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：21-65周岁", "minDriverAge": 21, "maxDriverAge": 65, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "THB", "currentPrice": 40.0, "localPrice": 200.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "THB", "maxDeposit": 20000.0, "minDeposit": 20000.0}, "allTags": [{"title": "24小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在24小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "有损取消", "category": 1, "type": 1, "description": "支付完成至取车前24小时取消将收取全部租金作为违约金;取车前24小时后取消将收取全部租金作为违约金", "sortNum": 40, "colorCode": "1", "labelCode": "3683"}, {"title": "免押金", "category": 2, "type": 1, "code": "2", "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "sortNum": 5, "colorCode": "2", "labelCode": "3850"}, {"title": "免费添加1人驾驶", "category": 2, "type": 1, "code": "2", "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "sortNum": 20, "colorCode": "2", "labelCode": "3556"}, {"title": "中文合同", "category": 2, "type": 1, "code": "2", "description": "可提供中文合同，减少因语言不通产生的障碍。需在取车时主动告知店员需要中文合同。", "sortNum": 50, "colorCode": "2", "labelCode": "3631"}, {"title": "中文店员", "category": 2, "type": 1, "code": "2", "description": "中文店员全程沟通，并陪同验车及熟悉车辆使用。", "sortNum": 51, "colorCode": "2", "labelCode": "3550"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含第三者保障", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}, {"title": "券减¥200", "category": 3, "type": 3, "code": "30", "description": "券减¥200,可享立减￥200/天（2天共减￥400）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}], "insuranceDetails": [{"code": "TPL", "name": "第三者保障", "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "CDW", "name": "车辆碰撞保障", "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "minCoverage": 100000.0, "maxCoverage": 100000.0}, {"code": "TP", "name": "车辆盗抢保障", "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "minCoverage": 100000.0, "maxCoverage": 100000.0}], "chargeList": [], "promotionInfo": {"deductionAmount": 400}, "vendorPromotionList": [], "pkgSellingRuleId": 171072}], "needFlightNo": false, "equipments": [], "packageItems": [{"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "CDW", "name": "车辆碰撞保障", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "TP", "name": "车辆盗抢保障", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "TPL", "name": "第三者保障", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "ADD1", "name": "1名额外驾驶员", "desc": "1名额外驾驶员\n每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "sortNum": 4}, {"code": "FRFB", "name": "满油/电取还", "desc": "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "取车要求", "content": ["驾驶员年龄21-65周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "21-65周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}], "summaryTitle": "驾驶员需持有本人名下4项材料取车"}, {"title": "护照原件", "subTitle": "", "type": 0, "subObject": [{"title": "中国大陆护照", "subTitle": "中国大陆护照原件", "code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}, {"title": "中国香港护照", "subTitle": "中国香港护照原件", "code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国澳门护照", "subTitle": "中国澳门护照原件", "code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}, {"title": "中国台湾护照", "subTitle": "中国台湾护照原件", "code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}, {"title": "其他地区护照", "subTitle": "其他地区护照原件", "code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}]}, {"title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1, "subObject": [{"type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "CN", "subObject": [{"title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22, "code": "CDL,IDL", "sortNum": 2}, {"title": "当地驾照", "content": ["当地驾照：由目的地国颁发的驾照"], "type": 22, "code": "LDL", "sortNum": 42}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "HK", "subObject": [{"title": "香港驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "HKDL", "sortNum": 6}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "MO", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "TW", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}, {"title": "当地驾照", "content": ["当地驾照：由目的地国颁发的驾照"], "type": 22, "code": "LDL", "sortNum": 42}]}]}]}, {"title": "国际信用卡", "content": ["可使用不带芯片信用卡"], "summaryContent": ["押金约THB 20,000（约¥4,034），到店刷取押金预授权，还车后1-60天内退还", "下单时可操作免押，取车时无需携带信用卡"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "type": 6, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png"], "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "0"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "0"}]}]}, {"title": "押金说明", "content": ["押金约THB 20,000（约¥4,034），到店刷取押金预授权，还车后1-60天内退还"], "summaryContent": ["下单时可操作免押，取车时无需携带信用卡"], "type": 8, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "押金约THB 20,000（约¥4,034）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}], "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在24小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前24小时取消将收取全部租金作为违约金;取车前24小时后取消将收取全部租金作为违约金"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "该订单确认后有损取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "该订单确认后有损取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "该订单确认后有损取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": false, "items": [{"title": "支付完成至2024年2月27日17:00", "subTitle": "支付完成至取车前24小时", "description": "取消将收取全部租金作为违约金", "showFree": false}, {"title": "2024年2月27日17:00后", "subTitle": "取车前24小时后", "description": "取消将收取全部租金作为违约金", "showFree": false}]}]}, {"title": "押金说明", "content": ["押金约THB 20,000（约¥4,034），到店刷取押金预授权，还车后1-60天内退还"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "押金约THB 20,000（约¥4,034）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-65周岁", "租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-65周岁", "租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["1名额外驾驶员", "1名额外驾驶员\n每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}], "sortNum": 7}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "安心补充险", "id": 21, "custumerTotalPrice": 136, "custumerDailyPrice": 68, "code": "MP18021533PK00024376", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localTotalPrice": 136, "localDailyPrice": 68.0, "localCurrencyCode": "CNY", "desc": " 保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "longDesc": "由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：", "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "FULL_COVERAGE", "days": 2, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "version": "OPI1175081858556231680", "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车", "description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}], "briefInsuranceItems": [{"code": "CDW", "name": "碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "coverageWithoutPlatformInsurance": "起赔额THB 20,000(约¥4,034)", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)"}]}, {"code": "TPL", "name": "第三者保障", "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "excessShortDesc": "起赔额THB 20,000", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)"}]}], "insuranceCompareItems": [{"description": "起赔额\nTHB 20,000", "type": "LDW", "descriptionColorCode": "GREEN"}, {"contains": false, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": false, "type": "PI"}, {"contains": false, "type": "RAP"}, {"contains": false, "type": "CANCEL"}, {"contains": false, "type": "ACCIDENT"}]}, {"insPackageId": 258, "insuranceItems": [{"code": "CDW", "name": "车辆碰撞保障", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "minCoverage": 100000.0, "maxCoverage": 100000.0, "coverageShortDesc": "保额：100,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额THB 20,000可赔", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)", "coverageWithPlatformInsuranceV2": "*起赔额及以下部分由国内保险公司提供"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "content": ["*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看"], "subObject": [{"title": "车行承担", "content": ["THB 20,000(约¥4,034)以上部分"]}, {"title": "国内保险公司承担*", "content": ["THB 20,000(约¥4,034)及以下部分（据实承担）"]}, {"title": "客户或承租方承担", "content": ["THB 0"]}]}}, {"code": "TP", "name": "车辆盗抢保障", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "minCoverage": 100000.0, "maxCoverage": 100000.0, "coverageShortDesc": "保额：100,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额THB 20,000可赔", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)", "coverageWithPlatformInsuranceV2": "*起赔额及以下部分由国内保险公司提供"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "content": ["*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看"], "subObject": [{"title": "车行承担", "content": ["THB 20,000(约¥4,034)以上部分"]}, {"title": "国内保险公司承担*", "content": ["THB 20,000(约¥4,034)及以下部分（据实承担）"]}, {"title": "客户或承租方承担", "content": ["THB 0"]}]}}, {"code": "TPL", "name": "第三者保障", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "excessShortDesc": "起赔额THB 20,000", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)"}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2", "excessTable": {"title": "起赔额THB 20,000(约¥4,034)", "subObject": [{"title": "车行承担", "content": ["THB 20,000(约¥4,034)以上部分"]}, {"title": "客户或承租方承担", "content": ["THB 20,000(约¥4,034)及以下部分（据实承担）"]}], "note": "false|THB 20,000(约¥4,034)|THB"}}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥68/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥68/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 21, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "insuranceNotice": "1、驾车人员投保年龄必须符合国际条列，即18周岁以上、70周岁以下；乘客投保年龄范围为0--90周岁，其中被保险人“意外身故、残疾保障“的保险金额71至80周岁为保单所载金额的一半、81至90周岁为四分之一。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010。\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n平安产险交通出行意外伤害保险（互联网版）条款\t注册号：C00001732312022080414693\n平安个人随车物品损失保险条款  注册号：H00001732112017050450351\n平安附加承保地域特约条款\t注册号为：C00001732322018031302341\t\n平安附加适用医疗机构范围约定保险条款\t注册号为：C00001731922019040903801\t\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1703/33.pdf"}], "ctripInsuranceIds": [21], "combinations": [{"bomCode": "228796_12387_ADD1_CDW_FRFB_TP_TPL_Taxes_Taxes_ULM_0_0", "title": "1名额外驾驶员", "codes": ["MultiDriver"], "currency": "CNY", "dayPrice": 668, "gapPrice": 0, "stepPrice": 0, "hike": false, "totalPrice": 1336, "payMode": 2, "packageId": 171072}], "productInfoList": [{"productCode": "2", "bomGroupCode": "228796_12387_ADD1_CDW_FRFB_TP_TPL_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 1200, "currentDailyPrice": 668, "currentTotalPrice": 1336, "localCarPrice": 4450.0, "localDailyPrice": 2975.0, "localTotalPrice": 5950.0, "localCurrencyCode": "THB", "currentCurrencyCode": "CNY", "currentOnewayfee": 303, "currentPoaPrice": 0, "currentPrepaidPrice": 1336, "localOnewayfee": 1500.0, "localPoaPrice": 0.0, "localPrepaidPrice": 5950.0, "payMode": 2, "productId": "2171072", "packageId": 171072, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 0.2017, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "24小时内确认", "confirmDesc": "预订此产品后供应商将在24小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 24.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": false, "hours": 0, "cancelDescription": "支付完成至取车前24小时取消将收取全部租金作为违约金;取车前24小时后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：21-65周岁", "minDriverAge": 21, "maxDriverAge": 65, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "THB", "currentPrice": 40.0, "localPrice": 200.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "THB", "maxDeposit": 20000.0, "minDeposit": 20000.0}, "allTags": [{"title": "24小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在24小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "有损取消", "category": 1, "type": 1, "description": "支付完成至取车前24小时取消将收取全部租金作为违约金;取车前24小时后取消将收取全部租金作为违约金", "sortNum": 40, "colorCode": "1", "labelCode": "3683"}, {"title": "免押金", "category": 2, "type": 1, "code": "2", "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "sortNum": 5, "colorCode": "2", "labelCode": "3850"}, {"title": "免费添加1人驾驶", "category": 2, "type": 1, "code": "2", "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "sortNum": 20, "colorCode": "2", "labelCode": "3556"}, {"title": "中文合同", "category": 2, "type": 1, "code": "2", "description": "可提供中文合同，减少因语言不通产生的障碍。需在取车时主动告知店员需要中文合同。", "sortNum": 50, "colorCode": "2", "labelCode": "3631"}, {"title": "中文店员", "category": 2, "type": 1, "code": "2", "description": "中文店员全程沟通，并陪同验车及熟悉车辆使用。", "sortNum": 51, "colorCode": "2", "labelCode": "3550"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含第三者保障", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}, {"title": "券减¥200", "category": 3, "type": 3, "code": "30", "description": "券减¥200,可享立减￥200/天（2天共减￥400）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}], "insuranceDetails": [{"code": "TPL", "name": "第三者保障", "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "CDW", "name": "车辆碰撞保障", "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "minCoverage": 100000.0, "maxCoverage": 100000.0}, {"code": "TP", "name": "车辆盗抢保障", "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "minCoverage": 100000.0, "maxCoverage": 100000.0}], "chargeList": [], "promotionInfo": {"deductionAmount": 400}, "vendorPromotionList": [], "pkgSellingRuleId": 171072}], "needFlightNo": false, "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 68.0, "localTotalPrice": 136, "localCurrencyCode": "CNY", "currentDailyPrice": 68, "currentTotalPrice": 136, "currentCurrencyCode": "CNY", "uniqueCode": "21"}], "packageItems": [{"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "CDW", "name": "车辆碰撞保障", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "TP", "name": "车辆盗抢保障", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "TPL", "name": "第三者保障", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "ADD1", "name": "1名额外驾驶员", "desc": "1名额外驾驶员\n每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "sortNum": 4}, {"code": "FRFB", "name": "满油/电取还", "desc": "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "取车要求", "content": ["驾驶员年龄21-65周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "21-65周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}], "summaryTitle": "驾驶员需持有本人名下4项材料取车"}, {"title": "护照原件", "subTitle": "", "type": 0, "subObject": [{"title": "中国大陆护照", "subTitle": "中国大陆护照原件", "code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}, {"title": "中国香港护照", "subTitle": "中国香港护照原件", "code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国澳门护照", "subTitle": "中国澳门护照原件", "code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}, {"title": "中国台湾护照", "subTitle": "中国台湾护照原件", "code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}, {"title": "其他地区护照", "subTitle": "其他地区护照原件", "code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}]}, {"title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1, "subObject": [{"type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "CN", "subObject": [{"title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22, "code": "CDL,IDL", "sortNum": 2}, {"title": "当地驾照", "content": ["当地驾照：由目的地国颁发的驾照"], "type": 22, "code": "LDL", "sortNum": 42}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "HK", "subObject": [{"title": "香港驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "HKDL", "sortNum": 6}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "MO", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "TW", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}, {"title": "当地驾照", "content": ["当地驾照：由目的地国颁发的驾照"], "type": 22, "code": "LDL", "sortNum": 42}]}]}]}, {"title": "国际信用卡", "content": ["可使用不带芯片信用卡"], "summaryContent": ["押金约THB 20,000（约¥4,034），到店刷取押金预授权，还车后1-60天内退还", "下单时可操作免押，取车时无需携带信用卡"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "type": 6, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png"], "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "0"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "0"}]}]}, {"title": "押金说明", "content": ["押金约THB 20,000（约¥4,034），到店刷取押金预授权，还车后1-60天内退还"], "summaryContent": ["下单时可操作免押，取车时无需携带信用卡"], "type": 8, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "押金约THB 20,000（约¥4,034）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}], "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在24小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前24小时取消将收取全部租金作为违约金;取车前24小时后取消将收取全部租金作为违约金"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "该订单确认后有损取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "该订单确认后有损取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "该订单确认后有损取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": false, "items": [{"title": "支付完成至2024年2月27日17:00", "subTitle": "支付完成至取车前24小时", "description": "取消将收取全部租金作为违约金", "showFree": false}, {"title": "2024年2月27日17:00后", "subTitle": "取车前24小时后", "description": "取消将收取全部租金作为违约金", "showFree": false}]}]}, {"title": "押金说明", "content": ["押金约THB 20,000（约¥4,034），到店刷取押金预授权，还车后1-60天内退还"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "押金约THB 20,000（约¥4,034）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-65周岁", "租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-65周岁", "租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["1名额外驾驶员", "1名额外驾驶员\n每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}], "sortNum": 7}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "安心补充险", "id": 21, "custumerTotalPrice": 136, "custumerDailyPrice": 68, "code": "MP18021533PK00024376", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localTotalPrice": 136, "localDailyPrice": 68.0, "localCurrencyCode": "CNY", "desc": " 保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "longDesc": "由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：", "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "FULL_COVERAGE", "days": 2, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "version": "OPI1175081858556231680", "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "提供理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核（1-3个工作日内反馈）", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款（审核通过后3个工作日内）", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}], "briefInsuranceItems": [{"code": "CDW", "name": "碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"currencyCode": "THB", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "coverageWithoutPlatformInsurance": "原起赔额THB 20,000可赔", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)", "coverageWithPlatformInsuranceV2": "*起赔额及以下部分由国内保险公司提供"}]}, {"code": "TPL", "name": "第三者保障", "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "excessShortDesc": "起赔额THB 20,000", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)"}]}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥68/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥68/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 21, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}], "insuranceCompareItems": [{"description": "0起赔额", "type": "LDW", "descriptionColorCode": "GREEN"}, {"contains": true, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": false, "type": "PI"}, {"contains": true, "type": "RAP"}, {"contains": true, "type": "CANCEL"}, {"contains": false, "type": "ACCIDENT"}]}, {"insPackageId": 770, "insuranceItems": [{"code": "CDW", "name": "车辆碰撞保障", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "minCoverage": 100000.0, "maxCoverage": 100000.0, "coverageShortDesc": "保额：100,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额THB 20,000可赔", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)", "coverageWithPlatformInsuranceV2": "*起赔额及以下部分由国内保险公司提供"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "content": ["*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看"], "subObject": [{"title": "车行承担", "content": ["THB 20,000(约¥4,034)以上部分"]}, {"title": "国内保险公司承担*", "content": ["THB 20,000(约¥4,034)及以下部分（据实承担）"]}, {"title": "客户或承租方承担", "content": ["THB 0"]}]}}, {"code": "TP", "name": "车辆盗抢保障", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "minCoverage": 100000.0, "maxCoverage": 100000.0, "coverageShortDesc": "保额：100,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额THB 20,000可赔", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)", "coverageWithPlatformInsuranceV2": "*起赔额及以下部分由国内保险公司提供"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由车行的保险承担起赔额以上的费用。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "content": ["*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看"], "subObject": [{"title": "车行承担", "content": ["THB 20,000(约¥4,034)以上部分"]}, {"title": "国内保险公司承担*", "content": ["THB 20,000(约¥4,034)及以下部分（据实承担）"]}, {"title": "客户或承租方承担", "content": ["THB 0"]}]}}, {"code": "TPL", "name": "第三者保障", "description": "保障第三方车辆或人员伤害损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "excessShortDesc": "起赔额THB 20,000", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)"}], "converageExplain": {"title": "承保范围", "content": ["若开车发生事故对第三方车辆或人员造成伤害，起赔额以上保额以内的部分将由车行的保险赔付。一般第三者保障没有起赔额（如有标注起赔额除外）但有最高保额。\n*实际赔付范围与标准以门店合同为准"]}, "unConverageExplain": {"title": "不承保范围"}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取警方报告，并联系车行告知情况"]}, {"title": "还车时向车行提交理赔", "content": ["在您租期结束时，向车行提供事故现场照片、第三者车损维修清单、第三方医疗费用清单等材料"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["车行将从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终定损（一般需要45-60个工作日）。若最终定损大于起赔额，只收起赔额；若小于起赔额，原路退还差价"]}]}, "groupCode": "2", "excessTable": {"title": "起赔额THB 20,000(约¥4,034)", "subObject": [{"title": "车行承担", "content": ["THB 20,000(约¥4,034)以上部分"]}, {"title": "客户或承租方承担", "content": ["THB 20,000(约¥4,034)及以下部分（据实承担）"]}], "note": "false|THB 20,000(约¥4,034)|THB"}}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥68/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥68/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 21, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥22/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥22/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "insuranceNotice": "1、驾车人员投保年龄必须符合国际条列，即18周岁以上、70周岁以下；乘客投保年龄范围为0--90周岁，其中被保险人“意外身故、残疾保障“的保险金额71至80周岁为保单所载金额的一半、81至90周岁为四分之一。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010。\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n平安产险交通出行意外伤害保险（互联网版）条款\t注册号：C00001732312022080414693\n平安个人随车物品损失保险条款  注册号：H00001732112017050450351\n平安附加承保地域特约条款\t注册号为：C00001732322018031302341\t\n平安附加适用医疗机构范围约定保险条款\t注册号为：C00001731922019040903801\t\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1703/33.pdf"}], "ctripInsuranceIds": [21, 11], "combinations": [{"bomCode": "228796_12387_ADD1_CDW_FRFB_TP_TPL_Taxes_Taxes_ULM_0_0", "title": "1名额外驾驶员", "codes": ["MultiDriver"], "currency": "CNY", "dayPrice": 690, "gapPrice": 0, "stepPrice": 0, "hike": false, "totalPrice": 1380, "payMode": 2, "packageId": 171072}], "productInfoList": [{"productCode": "2", "bomGroupCode": "228796_12387_ADD1_CDW_FRFB_TP_TPL_Taxes_Taxes_ULM_0_0", "priceInfoList": [{"currentCarPrice": 1200, "currentDailyPrice": 690, "currentTotalPrice": 1380, "localCarPrice": 4450.0, "localDailyPrice": 2975.0, "localTotalPrice": 5950.0, "localCurrencyCode": "THB", "currentCurrencyCode": "CNY", "currentOnewayfee": 303, "currentPoaPrice": 0, "currentPrepaidPrice": 1380, "localOnewayfee": 1500.0, "localPoaPrice": 0.0, "localPrepaidPrice": 5950.0, "payMode": 2, "productId": "2171072", "packageId": 171072, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 0.2017, "mileInfo": {"name": "不限里程", "isLimited": false, "desc": "租期内没有公里数限制。\n"}, "confirmInfo": {"confirmTitle": "24小时内确认", "confirmDesc": "预订此产品后供应商将在24小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 24.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": false, "hours": 0, "cancelDescription": "支付完成至取车前24小时取消将收取全部租金作为违约金;取车前24小时后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：21-65周岁", "minDriverAge": 21, "maxDriverAge": 65, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满12个月", "youngDriverExtraFee": {"localCurrencyCode": "THB", "currentPrice": 40.0, "localPrice": 200.0, "feeType": 1}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}], "depositCurrencyCode": "THB", "maxDeposit": 20000.0, "minDeposit": 20000.0}, "allTags": [{"title": "24小时内确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后供应商将在24小时内确认。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "有损取消", "category": 1, "type": 1, "description": "支付完成至取车前24小时取消将收取全部租金作为违约金;取车前24小时后取消将收取全部租金作为违约金", "sortNum": 40, "colorCode": "1", "labelCode": "3683"}, {"title": "免押金", "category": 2, "type": 1, "code": "2", "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "sortNum": 5, "colorCode": "2", "labelCode": "3850"}, {"title": "免费添加1人驾驶", "category": 2, "type": 1, "code": "2", "description": "1名额外驾驶员，每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "sortNum": 20, "colorCode": "2", "labelCode": "3556"}, {"title": "中文合同", "category": 2, "type": 1, "code": "2", "description": "可提供中文合同，减少因语言不通产生的障碍。需在取车时主动告知店员需要中文合同。", "sortNum": 50, "colorCode": "2", "labelCode": "3631"}, {"title": "中文店员", "category": 2, "type": 1, "code": "2", "description": "中文店员全程沟通，并陪同验车及熟悉车辆使用。", "sortNum": 51, "colorCode": "2", "labelCode": "3550"}, {"title": "含第三者保障", "category": 2, "type": 1, "code": "2", "description": "该产品的保障服务套餐中含第三者保障", "sortNum": 65, "colorCode": "2", "labelCode": "3554"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}, {"title": "电子提车凭证", "category": 2, "type": 1, "code": "2", "description": "使用电子版提车凭证取车更加方便，免去打印纸质提车凭证的麻烦。", "sortNum": 95, "colorCode": "2", "labelCode": "3552"}, {"title": "券减¥200", "category": 3, "type": 3, "code": "30", "description": "券减¥200,可享立减￥200/天（2天共减￥400）的优惠。", "sortNum": 0, "colorCode": "3", "labelCode": "3595"}], "insuranceDetails": [{"code": "TPL", "name": "第三者保障", "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "minCoverage": 1000000.0, "maxCoverage": 1000000.0}, {"code": "CDW", "name": "车辆碰撞保障", "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "minCoverage": 100000.0, "maxCoverage": 100000.0}, {"code": "TP", "name": "车辆盗抢保障", "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "minCoverage": 100000.0, "maxCoverage": 100000.0}], "chargeList": [], "promotionInfo": {"deductionAmount": 400}, "vendorPromotionList": [], "pkgSellingRuleId": 171072}], "needFlightNo": false, "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 68.0, "localTotalPrice": 136, "localCurrencyCode": "CNY", "currentDailyPrice": 68, "currentTotalPrice": 136, "currentCurrencyCode": "CNY", "uniqueCode": "21"}, {"name": "驾乘意外险", "localDailyPrice": 22.0, "localTotalPrice": 44, "localCurrencyCode": "CNY", "currentDailyPrice": 22, "currentTotalPrice": 44, "currentCurrencyCode": "CNY", "uniqueCode": "11"}], "packageItems": [{"code": "Taxes", "name": "税费，包含：机场税（如机场取车），客户设施费，旅游税，销售税", "desc": "税费，通常包含：机场税（如机场取车），客户设施费，旅游税，销售税；通常不包含：因异地还车产生的特殊地点税费以及门店的消费行为产生的其他税费，具体以门店政策为准。", "sortNum": 2}, {"code": "Taxes", "name": "基本租车费用", "desc": "仅包含车辆租金的基础费用", "sortNum": 2}, {"code": "CDW", "name": "车辆碰撞保障", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "TP", "name": "车辆盗抢保障", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "TPL", "name": "第三者保障", "desc": "保障第三方车辆或人员伤害损失", "sortNum": 3}, {"code": "ULM", "name": "不限里程", "desc": "租期内没有公里数限制。\n", "sortNum": 4}, {"code": "ADD1", "name": "1名额外驾驶员", "desc": "1名额外驾驶员\n每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。", "sortNum": 4}, {"code": "FRFB", "name": "满油/电取还", "desc": "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "取车要求", "content": ["驾驶员年龄21-65周岁，且持有驾照至少满12个月"], "summaryContent": ["租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "21-65周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥12个月"}]}], "summaryTitle": "驾驶员需持有本人名下4项材料取车"}, {"title": "护照原件", "subTitle": "", "type": 0, "subObject": [{"title": "中国大陆护照", "subTitle": "中国大陆护照原件", "code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}, {"title": "中国香港护照", "subTitle": "中国香港护照原件", "code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "type": 21}]}, {"title": "中国澳门护照", "subTitle": "中国澳门护照原件", "code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}, {"title": "中国台湾护照", "subTitle": "中国台湾护照原件", "code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}, {"title": "其他地区护照", "subTitle": "其他地区护照原件", "code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "type": 21}]}]}, {"title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1, "subObject": [{"type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "CN", "subObject": [{"title": "中国大陆驾照原件 + 驾照国际翻译认证件", "content": ["中国大陆驾照原件：中国大陆颁发的驾照原件", "驾照国际翻译认证件：全球200+个国家/地区认可，1年多次使用。在线申请，1-3个工作日寄出，活动期间可免费办理。下单后可在订单详情页面办理。"], "type": 22, "code": "CDL,IDL", "sortNum": 2}, {"title": "当地驾照", "content": ["当地驾照：由目的地国颁发的驾照"], "type": 22, "code": "LDL", "sortNum": 42}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "HK", "subObject": [{"title": "香港驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "HKDL", "sortNum": 6}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "MO", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "TW", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。请持有效IDP证件取车，1949年日内瓦道路交通公约的IDP有效期为1年，1968年维也纳道路交通公约的IDP有效期为3年。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 11}, {"title": "当地驾照", "content": ["当地驾照：由目的地国颁发的驾照"], "type": 22, "code": "LDL", "sortNum": 42}]}]}]}, {"title": "国际信用卡", "content": ["可使用不带芯片信用卡"], "summaryContent": ["押金约THB 20,000（约¥4,034），到店刷取押金预授权，还车后1-60天内退还", "下单时可操作免押，取车时无需携带信用卡"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "type": 6, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png"], "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "0"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "0"}]}]}, {"title": "押金说明", "content": ["押金约THB 20,000（约¥4,034），到店刷取押金预授权，还车后1-60天内退还"], "summaryContent": ["下单时可操作免押，取车时无需携带信用卡"], "type": 8, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "押金约THB 20,000（约¥4,034）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}], "summaryTitle": "国际信用卡"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "content": ["预订此产品后供应商将在24小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "content": ["支付完成至取车前24小时取消将收取全部租金作为违约金;取车前24小时后取消将收取全部租金作为违约金"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "该订单确认后有损取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "该订单确认后有损取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "该订单确认后有损取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": false, "items": [{"title": "支付完成至2024年2月27日17:00", "subTitle": "支付完成至取车前24小时", "description": "取消将收取全部租金作为违约金", "showFree": false}, {"title": "2024年2月27日17:00后", "subTitle": "取车前24小时后", "description": "取消将收取全部租金作为违约金", "showFree": false}]}]}, {"title": "押金说明", "content": ["押金约THB 20,000（约¥4,034），到店刷取押金预授权，还车后1-60天内退还"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "押金约THB 20,000（约¥4,034）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}, {"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-65周岁", "租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["该车辆没有里程限制。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "能源政策", "content": ["满油/电取还", "取车时油量/电量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油/充电的工时费。建议保留最后一次加油/充电时的单据以及取还车时显示的车辆油/电量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：21-65周岁", "租车公司对21-25周岁将收取“青年驾驶费”；参考价格：THB200（约¥40）/天，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["1名额外驾驶员", "1名额外驾驶员\n每位额外驾驶员都需要出示与主驾驶人相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时间不在此营业时间范围内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照门市价格收取您额外的租赁费用。您可以提前联系门店（或平台）确认。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["若您延迟到门店取车，请在预订取车时间前提前联系门店(或平台)咨询，否则可能会导致您无法取车。若供应商同意延迟取车，他们也很可能向您收取一笔“延迟取车费”。建议您务必在预订时间内准时到达。另外，对于延迟的未使用时间，门店不会退还相关费用，为避免之后出现费用纠纷，取车时请仔细核对租车合同上的取还车时间，以及预计结算费用，核对无误后再签字。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前还车，一般不会产生额外费用，租车公司也不会退还剩余租期的费用。由于您自身行为（例如违规驾驶、违反租车合同条款等）导致车行终止合同，剩余未使用天数的车辆租金将不会退还，费用仍以合同中完整的租期计算。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["若您延迟还车会产生“延迟还车费”，建议您提前与门店联系沟通，具体收费标准以门店告知为准。温馨提醒：若未经供应商允许而擅自延迟还车或逾期不还，除承担相应的经济责任，您还可能面临触犯法律的风险。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 6}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}], "sortNum": 7}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "安心补充险", "id": 21, "custumerTotalPrice": 136, "custumerDailyPrice": 68, "code": "MP18021533PK00024376", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "localTotalPrice": 136, "localDailyPrice": 68.0, "localCurrencyCode": "CNY", "desc": " 保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "longDesc": "由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方：", "noCoverageContent": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"], "typeCode": "FULL_COVERAGE", "days": 2, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "version": "OPI1175081858556231680", "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}}], "claimsProcess": [{"subTitle": "发生事故（本车与第三方，或本车单独发生事故）时请按下方流程处理，否则无法获得赔偿。", "subObject": [{"title": "报警并联系门店", "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"], "type": 1}, {"title": "还车时向车行提交事故材料", "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"], "type": 2}]}, {"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/pdf1903/1.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "提供理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核（1-3个工作日内反馈）", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款（审核通过后3个工作日内）", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}]}, {"subTitle": "您购买的套餐含国内保险公司提供的驾乘意外险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/pdf1903/2.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，请优先保障您的人身安全和健康，垫付相关费用，待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "提供理赔材料", "content": ["意外/医疗：门急诊/住院病历、医疗费用发票/收据原件、医院检查报告、事故证明等；财物损失：损失清单、购货凭证、损失证明等"], "type": 2}, {"title": "理赔审核（1-3个工作日内反馈）", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款（审核通过后3个工作日内）", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "2", "title": "保被撞的车和人"}, {"code": "3", "title": "保车内人员财物"}], "briefInsuranceItems": [{"code": "CDW", "name": "碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"currencyCode": "THB", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "coverageWithoutPlatformInsurance": "原起赔额THB 20,000可赔", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)", "coverageWithPlatformInsuranceV2": "*起赔额及以下部分由国内保险公司提供"}]}, {"code": "TPL", "name": "第三者保障", "isFromCtrip": false, "insuranceDetail": [{"packageId": 171072, "currencyCode": "THB", "minExcess": 20000.0, "maxExcess": 20000.0, "excessShortDesc": "起赔额THB 20,000", "excessLongDesc": "", "minCoverage": 1000000.0, "maxCoverage": 1000000.0, "coverageShortDesc": "保额：1,000,000THB", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额THB 20,000(约¥4,034)"}]}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用，赔付车损盗抢起赔额", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥68/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥68/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车损和盗抢保障的起赔额部分，以及车行保障不包含的玻璃、轮胎、底盘等损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 21, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "￥50,000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎（含轮毂）、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "￥50,000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "￥3,000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "￥1,000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "￥4,000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥22/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥22/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保因交通意外导致的车上人员人身伤害、医疗费用及随车财物损失。在取车时间前可无损取消。详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 非驾驶或乘坐租赁车辆期间产生的交通意外事故;\n2、 被保险人的故意行为导致的损失;\n3、 医疗费用不赔偿营养费、护理费、交通费、伙食费、误工费;\n4、 财物损失不赔偿金银、首饰、字画等珍贵财物;\n5、 车门未上锁、车窗未关闭、车辆无明显盗抢痕迹导致的车上财物损失;\n6、 租车公司未授权的驾驶人驾车导致的损失;\n7、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 11, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50万/人，每车限额150万", "descList": ["全车人员意外伤害"]}, {"title": "2万/人", "descList": ["意外医疗"]}, {"title": "100/天，最高90天", "descList": ["住院津贴"]}, {"title": "2万/车，每套物品限额3000元", "descList": ["随车行李及财物损失"]}], "insuranceNotice": "1、驾车人员投保年龄必须符合国际条列，即18周岁以上、70周岁以下；乘客投保年龄范围为0--90周岁，其中被保险人“意外身故、残疾保障“的保险金额71至80周岁为保单所载金额的一半、81至90周岁为四分之一。\n2、每人限购一份，多购无效。\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010。\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n7、请确认您已认真阅读\n平安产险交通出行意外伤害保险（互联网版）条款\t注册号：C00001732312022080414693\n平安个人随车物品损失保险条款  注册号：H00001732112017050450351\n平安附加承保地域特约条款\t注册号为：C00001732322018031302341\t\n平安附加适用医疗机构范围约定保险条款\t注册号为：C00001731922019040903801\t\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n17、本产品由中国平安财产保险股份有限公司承保。目前该公司在中华人民共和国境内（港、澳、台地区除外），包括北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省、吉林省、黑龙江省、上海市、江苏省、浙江省、安徽省、福建省、江西省、山东省、河南省、湖北省、湖南省、广东省、广西壮族自治区、海南省、 重庆市、四川省、贵州省、云南省、西藏自治区、陕西省、甘肃省、青海省、宁夏回族自治区、新疆维吾尔自治区有分支机构。\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1703/33.pdf"}], "insuranceCompareItems": [{"description": "0起赔额", "type": "LDW", "descriptionColorCode": "GREEN"}, {"contains": true, "type": "WDW"}, {"contains": true, "type": "TPL"}, {"contains": true, "type": "PI"}, {"contains": true, "type": "RAP"}, {"contains": true, "type": "CANCEL"}, {"contains": true, "type": "ACCIDENT"}]}], "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}, "promptInfos": [{"title": "押金汇率说明", "contents": [{"stringObjs": [{"content": "押金所需的人民币金额根据当前汇率计算，实际所需人民币金额以用户到店支付时或者授权免押时的汇率为准。"}]}], "type": 12}, {"title": "素万那普国际机场 is a popular location on your selected dates. Rates are likely to rise.", "subTitle": "Book now! Don't miss out on low prices.", "type": 29}], "platformInsuranceExtra": {"encourageTitle": "Extra Protection for Your Rental", "encourageDesc": "Your package does not cover you for all losses and damages. Protect yourself from unexpected damage costs with RentalCover.com's protection", "claimProcedure": [{"type": 1, "content": ["请在事故现场第一时间报警并联系门店获取后续操作指引。若涉及第三方，请留存第三方联络信息（联系电话，车牌号，姓名等），同时拍下事故现场照片，获取警方事故报告。"]}, {"type": 2, "content": ["用车结束时，您需要配合门店提供相关事故信息。门店会检查受损情况，根据合同中的保障范围，收取您相应的费用。可能会出现的情况：从您信用卡预授权中扣除起赔额费用（若有），待门店确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，您只需支付起赔额和不在理赔范围内产生的车损费用；若小于起赔额，门店将原路退还差价。"]}]}, "requestInfo": {"pickupDate": "2024-02-28 17:00:00", "pickupLocationName": "素万那普国际机场", "returnDate": "2024-02-29 23:30:00", "returnLocationName": "廊曼国际机场", "sourceCountryId": 1, "pLatitude": 13.689999, "rLatitude": 13.91326, "rLongitude": 100.604199, "pLongitude": 100.750112, "pDate": "20240228170000", "rDate": "20240229233000", "pCityId": 359, "rCityId": 359}, "licenceCountryPolicy": {"title": "驾照", "type": 15, "subObject": [{"title": "中国大陆驾照", "summaryContent": ["您需携带当地驾照（或驾照国际翻译认证件和中国大陆驾照原件）才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "CN", "optimalType": "1"}, {"title": "中国香港驾照", "summaryContent": ["您需携带香港驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "HK", "optimalType": "1"}, {"title": "中国澳门驾照", "summaryContent": ["您需携带国际驾照和驾驶员本国驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "MO", "optimalType": "1"}, {"title": "中国台湾驾照", "summaryContent": ["您需携带国际驾照和驾驶员本国驾照才能成功取车", "所有驾驶员驾龄必须至少满12个月"], "type": 1, "code": "TW", "optimalType": "1"}], "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "持非中国（含港澳台）驾照无法预订。建议更换驾驶员或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17}]}, "crossPolicy": {"crossLocationsInfos": [{"crossType": 3, "crossTypeName": "跨境政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨境，建议您更换其它租车公司或车型组。"]}, {"crossType": 1, "crossTypeName": "跨岛政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨岛，建议您更换其它租车公司或车型组。"]}, {"crossType": 2, "crossTypeName": "跨州政策", "summaryPolicies": ["由于租车公司政策限制或选择的车型受限制，如果您必须驾车跨州/省，建议您更换其它租车公司或车型组。"]}], "notes": ["若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。除特别说明的情况外，“跨境费用”为前往单个国家所需的费用。"], "title": "旅行限制"}, "locationPrompt": "泰国地区道路状况复杂，为右舵靠左行驶，易发生车损，自付起赔额较高。建议升级含安心补充险的套餐，自付起赔额降为0，保障范围更广。", "osdCompareTitle": ["车辆碰撞及盗抢", "玻璃轮胎底盘保障", "第三者保障", "人身财物险", "道路救援补偿", "旅行取消", "意外津贴/住院津贴"]}