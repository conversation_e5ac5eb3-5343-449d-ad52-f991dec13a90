{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "2857be3f-f41f-437c-91d4-5947927eea2e", "extMap": {"isKarabi": "1", "rentalDays": "2"}, "apiResCodes": [], "hasResult": true, "message": "OK"}, "ResponseStatus": {"Timestamp": "/Date(1711457593921+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "6891236303756990866"}, {"Id": "RootMessageId", "Value": "100025527-0a809ba3-475404-527712"}]}, "isSoldOut": false, "isHotCity": true, "pickupStoreInfo": {"storeCode": "216664", "bizVendorCode": "14088020", "telephone": "+43-1505264004", "storeName": "Bangkok Suvarnabhumi International Airport", "address": "999 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> 10540", "longitude": 100.750809, "latitude": 13.693233, "storeGuild": "门店位于机场到达厅二楼7号和8号出口中间，步行可达（tms翻译结果）", "storeWay": "门店位于廊曼国际机场内，步行可达", "workTime": {"openTimeDesc": "{\"\":\"24小时营业\"}", "description": ""}, "storeServiceList": [], "countryId": 4, "countryName": "泰国", "provinceId": 0, "cityId": 359, "cityName": "曼谷", "isAirportStore": true, "showType": 3, "vendorStoreCode": "BKK01", "continentId": 1, "continentName": "亚洲"}, "returnStoreInfo": {"storeCode": "216664", "bizVendorCode": "14088020", "telephone": "+43-1505264004", "storeName": "Bangkok Suvarnabhumi International Airport", "address": "999 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> 10540", "longitude": 100.750809, "latitude": 13.693233, "storeGuild": "还车与取车地点一致，将车驶到机场到达入口处，可通知工作人员办理还车手续。（tms翻译结果）", "storeWay": "门店位于廊曼国际机场内", "workTime": {"openTimeDesc": "{\"\":\"24小时营业\"}", "description": ""}, "storeServiceList": [], "countryId": 4, "countryName": "泰国", "provinceId": 0, "cityId": 359, "cityName": "曼谷", "isAirportStore": true, "showType": 3, "vendorStoreCode": "BKK01", "continentId": 1, "continentName": "亚洲"}, "vendorInfo": {"bizVendorCode": "14088020", "vendorName": "Thai Rent a car", "vendorImageUrl": "//pic.c-ctrip.com/car/osd/ctrip/vendor_logo/Thai_2017.png", "vendorCode": "SD0012", "isBroker": true, "platformCode": "", "platformName": "YES CAR RENTALS LIMITED", "haveCoupon": false, "vendorTag": {"title": "当地连锁", "sortNum": 0}}, "vehicleInfo": {"brandId": 1, "brandEName": "<PERSON><PERSON>", "name": "西雅特 伊比飒 2门版16384", "zhName": "西雅特 伊比飒 2门版16384", "vehicleCode": "12678", "imageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg", "groupCode": "1", "groupSubClassCode": "1001", "groupName": "经济型轿车", "transmissionType": 2, "transmissionName": "手动挡", "passengerNo": 4, "doorNo": 2, "luggageNo": 0, "imageList": ["https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "userRealImageCount": 0, "specializedImages": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "isSpecialized": true, "hasConditioner": false, "vendorSimilarVehicleInfos": [], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "groupSubName": "小型轿车"}, "flightDelayRule": {"title": "航班延误保留政策", "description": "航班延误保留至20:30（当天）", "rules": [{"title": "航班延误保留至20:30（当天）"}, {"title": "建议您填写航班号，如遇延误请携带机票前往取车"}, {"title": "补充政策：", "descs": ["中短啊啊啊", "中长啊啊啊啊啊啊啊啊啊"]}]}, "commentInfo": {"level": "", "commentCount": 13, "overallRating": "0.0", "maximumRating": 5.0, "commentLabel": "", "hasComment": 0, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=BRAND_CITY_QUERY&calabiStoreId=216664&calabiVehicleId=12678&vehicleName=西雅特 伊比飒 2门版16384&productCategoryId=34&isHideNavBar=YES&brandCity=SD0012-359&cityName=曼谷&storeName=Bangkok Suvarnabhumi International Airport"}, "packageInfos": [{"insPackageId": 1, "isDefault": true, "packageName": "基础套餐", "currencyCode": "CNY", "defaultBomCode": "216664_12678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "defaultPackageId": 342997, "guaranteeDegree": 2.0, "naked": false, "insuranceNames": ["超级车辆碰撞险", "超级车辆盗抢险"], "lowestDailyPrice": 30, "gapPrice": 0, "stepPrice": 0, "description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失", "packageType": 0, "isBasic": 1}, {"insPackageId": 3, "isDefault": true, "packageName": "高级套餐", "currencyCode": "CNY", "defaultBomCode": "216664_112678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "defaultPackageId": 342997, "guaranteeDegree": 2.0, "naked": false, "insuranceNames": ["超级车辆碰撞险", "超级车辆盗抢险"], "lowestDailyPrice": 30, "gapPrice": 0, "stepPrice": 0, "description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失", "packageType": 0, "isBasic": 1}, {"insPackageId": 259, "isDefault": false, "packageName": "优享套餐", "currencyCode": "CNY", "defaultBomCode": "2166642_12678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "defaultPackageId": 342997, "guaranteeDegree": 4.0, "naked": false, "insuranceNames": ["超级车辆碰撞险", "超级车辆盗抢险", "安心补充险"], "lowestDailyPrice": 55, "gapPrice": 25, "stepPrice": 25, "isYouXiang": 1, "youXiangGapPrice": 25}, {"insPackageId": 771, "isDefault": false, "packageName": "尊享套餐", "currencyCode": "CNY", "defaultBomCode": "2166634_12678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "defaultPackageId": 342997, "guaranteeDegree": 5.0, "naked": false, "insuranceNames": ["超级车辆碰撞险", "超级车辆盗抢险", "安心补充险", "驾乘意外险"], "lowestDailyPrice": 130, "gapPrice": 100, "stepPrice": 100, "isYouXiang": 0}], "productDetails": [{"insPackageId": 3, "insuranceItems": [{"code": "SCDW", "name": "超级车辆碰撞险", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 342997, "currencyCode": "THB", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额THB 0", "excessLongDesc": "", "minCoverage": 100.0, "maxCoverage": 100.0, "coverageShortDesc": "保额：100THB", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "0起赔额"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用，起赔额通常较低或为零。"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价。"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["THB 0"]}], "note": "true|THB 0(约¥0)|THB"}}, {"code": "STP", "name": "超级车辆盗抢险", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 342997, "currencyCode": "AED", "minExcess": 2.0, "maxExcess": 2.0, "excessShortDesc": "起赔额AED 2", "excessLongDesc": "", "minCoverage": 3.0, "maxCoverage": 3.0, "coverageShortDesc": "保额：3AED", "coverageLongDesc": "", "coverageWithoutPlatformInsuranceV2": "起赔额AED 2(约¥4)"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用，起赔额通常较低或为零。不承保范围：因车辆钥匙丢失、损坏，车门忘关导致车辆被盗；将车辆长时间停在不当的地方导致车辆被盗。实际赔付范围与标准以门店合同为准。"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：:\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价。"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额AED 2(约¥4)", "subObject": [{"title": "车行承担", "content": ["AED 2(约¥4)以上部分"]}, {"title": "客户或承租方承担", "content": ["AED 2(约¥4)及以下部分（据实承担）"]}], "note": "false|AED 2(约¥4)|AED"}}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}], "combinations": [{"bomCode": "216664_12678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "title": "婴儿座椅 + 儿童座椅 + 儿童增高座垫", "codes": ["ChildSeat", "ChildSeat", "ChildSeat"], "currency": "CNY", "dayPrice": 30, "gapPrice": 0, "stepPrice": 0, "hike": false, "totalPrice": 60, "payMode": 2, "packageId": 342997}], "productInfoList": [{"productCode": "3", "bomGroupCode": "216664_12678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "priceInfoList": [{"currentCarPrice": 60, "currentDailyPrice": 30, "currentTotalPrice": 60, "localCarPrice": 300.0, "localDailyPrice": 150.0, "localTotalPrice": 300.0, "localCurrencyCode": "THB", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 60, "localOnewayfee": 0.0, "localPoaPrice": 0.0, "localPrepaidPrice": 300.0, "isContainOnewayFee": false, "payMode": 2, "productId": "3342997", "packageId": 342997, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 0.1984, "mileInfo": {"name": "里程限制租车期间2km", "isLimited": true, "distance": 2.0, "distanceUnit": "km", "periodUnit": "租车期间", "chargeAmount": 1.0, "chargeUnit": "km", "quantity": 1.0, "desc": "里程限制2km。超出里程限制需额外支付费用，具体金额及税费请以门店合同为准。"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": false, "hours": 0, "cancelDescription": "支付完成至取车时间取消将收取租金30%作为违约金;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：16-90周岁", "minDriverAge": 16, "maxDriverAge": 90, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满6个月", "youngDriverExtraFee": {"localCurrencyCode": "CNY", "currentPrice": 12.0, "localPrice": 12.0, "feeType": 0}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "USD", "maxDeposit": 1.0, "minDeposit": 1.0}, "allTags": [{"title": "1小时确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后车行预计需要1小时确认是否预订成功。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "有损取消", "category": 1, "type": 1, "description": "支付完成至取车时间取消将收取租金30%作为违约金;取车时间后取消将收取全部租金作为违约金", "sortNum": 40, "colorCode": "1", "labelCode": "3683"}, {"title": "芝麻免押", "category": 2, "type": 1, "code": "2", "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "sortNum": 5, "colorCode": "2", "labelCode": "3850"}, {"title": "指定车型", "category": 2, "type": 1, "code": "2", "description": "实际提供的车型即为页面所示车型，而非同车型组的其它车型。", "sortNum": 45, "colorCode": "2", "labelCode": "3551"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "24h营业", "category": 2, "type": 1, "code": "2", "description": "门店24小时营业。", "sortNum": 60, "colorCode": "2", "labelCode": "3491"}, {"title": "自助取车", "category": 2, "type": 1, "code": "2", "description": "当您在门店办理取车手续的时候，您可以移步到柜台旁边的自助取车终端自行办理，自助取车终端提供多国语言的触屏操作，让您方便快捷地完成取车手续，节省在柜台排队等候的时间。", "sortNum": 75, "colorCode": "2", "labelCode": "3599"}, {"title": "满油取还", "category": 2, "type": 1, "description": "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3977"}, {"title": "支持跨境", "category": 2, "type": 1, "code": "2", "description": "支持跨境。", "sortNum": 90, "colorCode": "2", "labelCode": "3602"}, {"title": "支持跨岛", "category": 2, "type": 1, "code": "2", "description": "支持跨岛。", "sortNum": 91, "colorCode": "2", "labelCode": "3601"}, {"title": "支持跨州", "category": 2, "type": 1, "code": "2", "description": "支持跨州", "sortNum": 92, "colorCode": "2", "labelCode": "3603"}, {"title": "免费儿童座椅", "category": 2, "type": 1, "description": "免费儿童座椅。", "sortNum": 109, "colorCode": "2", "labelCode": "3622"}, {"title": "里程限制租车期间2km", "category": 4, "type": 2, "code": "4", "description": "里程限制租车期间2km，超出里程限制需额外支付每km¥1，不包含税。", "sortNum": 125, "colorCode": "4", "labelCode": "3580"}], "insuranceDetails": [{"code": "SCDW", "name": "超级车辆碰撞险", "currencyCode": "THB", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 100.0, "maxCoverage": 100.0}, {"code": "STP", "name": "超级车辆盗抢险", "currencyCode": "AED", "minExcess": 2.0, "maxExcess": 2.0, "minCoverage": 3.0, "maxCoverage": 3.0}], "chargeList": [], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 342997}], "needFlightNo": false, "equipments": [], "packageItems": [{"code": "Taxes", "name": "增值税", "desc": "增值税是以商品（含应税劳务）在流转过程中产生的增值额作为计税依据而征收的一种流转税。", "sortNum": 2}, {"code": "Taxes", "name": "销售税", "desc": "销售税是为销售某些商品和服务而向管理机构缴纳的一种税，租车过程中产生的任何消费行为都需要缴纳该费用。", "sortNum": 2}, {"code": "Taxes", "name": "商品税", "desc": "商品和服务税（Goods and Service Tax，GST）是以货物和服务为征收对象的间接税。", "sortNum": 2}, {"code": "SCDW", "name": "超级车辆碰撞险", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "STP", "name": "超级车辆盗抢险", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "LM", "name": "里程限制租车期间2km", "desc": "里程限制2km。超出里程限制需额外支付费用，具体金额及税费请以门店合同为准。", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}, {"code": "RAP", "name": "道路救援", "desc": "如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。", "sortNum": 4}, {"code": "WIN", "name": "雪地胎", "desc": "冬季恶劣天气，行车需配备有雪地轮胎", "sortNum": 4}, {"code": "", "name": "婴儿座椅", "desc": "通常适用于体重2千克~9千克的儿童\n通常适用于2-20磅或者 2.27-9.07千克的婴儿", "sortNum": 4}, {"code": "", "name": "儿童座椅", "desc": "通常适用于体重9千克~18千克的儿童\n通常适用于20-40磅或者9.07-18.16千克的儿童", "sortNum": 4}, {"code": "", "name": "儿童增高座垫", "desc": "通常适用于体重18千克~36千克的儿童\n通常适用于40-80磅或者18.16-36.32千克的儿童", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "取车要求", "content": ["驾驶员年龄16-90周岁，且持有驾照至少满6个月"], "summaryContent": ["租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "16-90周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥6个月"}]}], "summaryTitle": "驾驶员需持有本人名下5项材料取车"}, {"title": "护照原件", "subTitle": "", "type": 0, "subObject": [{"title": "中国大陆护照", "subTitle": "中国大陆护照原件", "code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "中国香港护照", "subTitle": "中国香港护照原件", "code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "中国澳门护照", "subTitle": "中国澳门护照原件", "code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "中国台湾护照", "subTitle": "中国台湾护照原件", "code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "其他地区护照", "subTitle": "其他地区护照原件", "code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}]}, {"title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1, "subObject": [{"type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "CN", "subObject": [{"title": "香港驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "HKDL", "sortNum": 3}, {"title": "中国驾照原件 + 车行翻译件", "content": ["中国驾照原件：中国驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 22, "code": "CDL,DLT", "sortNum": 3}, {"title": "中国驾照原件 + 当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 22, "code": "CDL,OLT", "sortNum": 5}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "HK", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 1}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "MO", "subObject": [{"title": "西班牙文公证件", "content": ["西班牙文公证件：由公证处颁发的驾驶员本国驾照西班牙文翻译文件"], "type": 22, "code": "OLTES", "sortNum": 1}, {"title": "国际驾照 + 澳门驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）", "澳门驾照：由中国澳门特别行政区颁发的驾照"], "type": 22, "code": "IDP,MCDL", "sortNum": 29}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "TWDL,IDP", "sortNum": 31}, {"title": "国际驾照 + 台湾驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）", "台湾驾照：由中国台湾地区颁发的驾照"], "type": 22, "code": "IDP,TWDL", "sortNum": 31}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 1}, {"title": "美国驾照", "content": ["美国驾照：持有驾驶员本人护照原件及美国驾照原件即可取车"], "type": 22, "code": "ADL", "sortNum": 2}, {"title": "入境纸 + 香港驾照", "content": ["入境纸：RJZ", "香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "RJZ,HKDL", "sortNum": 2}, {"title": "香港驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "HKDL", "sortNum": 3}, {"title": "国际驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "IDP", "sortNum": 5}, {"title": "英文驾照", "content": ["英文驾照：由驾驶员所在国颁发的正式英文本国驾照"], "type": 22, "code": "EODL", "sortNum": 6}]}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案"}]}], "type": 10}]}, {"title": "国际信用卡（2张）", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡"], "summaryContent": ["到店刷取押金预授权，押金约USD1（约¥7），还车后10-30天内退还。", "若无法提供信用卡，该门店也支持现金支付", "下单时可操作免押，取车时无需携带信用卡"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带2张符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "信用卡支付说明（中）"}]}], "type": 6, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png"], "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}]}, {"title": "现金支付说明", "content": ["该门店同时支持现金支付，若选择现金支付，请携带足够现金前往，详情请咨询门店"], "type": 19}, {"title": "押金说明", "content": ["到店刷取押金预授权，押金约USD1（约¥7），还车后10-30天内退还。"], "summaryContent": ["下单时可操作免押，取车时无需携带信用卡"], "type": 8, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "US$ 1（约¥8）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}], "summaryTitle": "国际信用卡（2张）"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}, {"title": "其他材料", "content": ["（cn）其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案"], "summaryContent": ["（cn）其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案"], "type": 14}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "subTitle": "立即确认", "content": ["预订此产品后供应商将在1小时内确认订单"], "type": 0, "code": "1", "sortNum": 1, "showFree": 1}, {"title": "取消政策", "subTitle": "该订单确认后有损取消", "content": ["支付完成至取车时间取消将收取租金30%作为违约金;取车时间后取消将收取全部租金作为违约金"], "type": 1, "code": "1", "sortNum": 2, "showFree": 1, "table": [{"title": "取消政策", "subTitle": "该订单确认后有损取消1", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "该订单确认后有损取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "该订单确认后有损取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": false, "items": [{"title": "支付完成至2024年4月9日14:30", "subTitle": "支付完成至取车时间", "description": "取消将收取租金30%作为违约金", "showFree": false}, {"title": "2024年4月9日14:30后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金", "showFree": false}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，押金约USD1（约¥7），还车后10-30天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "US$ 1（约¥8）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}, {"title": "里程政策", "content": ["里程限制租车期间2km，超出里程限制需额外支付每km¥1，不包含税。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "能源政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：16-90周岁", "租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["里程限制租车期间2km，超出里程限制需额外支付每km¥1，不包含税。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "能源政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：16-90周岁", "租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}, {"title": "门店提示", "content": ["中文描述提示啊啊啊啊"], "type": 47, "code": "2", "sortNum": 7}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["如您於指定時間前提前取車，請於相同時間內提前還車。假如您提前2小時取車，您須至少提前2小時還車，否則將會額外收費。如需更多資訊，請提前聯絡當地分店或 Trip.com。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["如您無法於預設指定時間內取車，請於所選時間前聯絡當地分店或 Trip.com，以免無法取車。請確保您於所選時段內抵達門市，如您延遲取車，當地分店將不會退還未使用期間的費用。為避免費用糾紛，請在取車簽約前確認還車時間，並預計租車費用。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前還車通常無需額外收費，但請注意當地分店將不會退還未使用期間的費用。若租車合約需提前終止（如車輛由未經登記的駕駛人士駕駛、違反合約條款及細則等），將不作任何退款。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["延遲還車將額外收費，請提前聯絡當地分店了解相關費用的具體資訊。請注意，延遲還車除另行收費外，您可能面臨觸犯法律的風險。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "取车描述", "content": [], "type": 58, "code": "2", "subObject": [{"title": "签合同前核对金额", "content": ["办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。"], "type": 59, "code": "2", "sortNum": 1}, {"title": "验车注意事项", "content": ["验车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身，油量里程，车损特写等），以免产生售后纠纷。"], "type": 60, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}, {"title": "雪地胎政策", "content": ["免费儿童座椅"], "type": 46, "code": "2", "sortNum": 3}], "sortNum": 5}, {"title": "旅行限制", "content": [], "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["测试啊啊 啊"], "type": 27, "code": "2", "sortNum": 1}], "sortNum": 6}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 7}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}], "sortNum": 8}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 16, "custumerTotalPrice": 150, "custumerDailyPrice": 75, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "localTotalPrice": 150, "localDailyPrice": 75.0, "localCurrencyCode": "CNY", "desc": "驾乘意外险短描述", "longDesc": "驾乘意外险长描述", "noCoverageContent": ["驾乘意外险不保障内容"], "typeCode": "ACCIDENT_COVER", "days": 2, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "version": "OPI1128778955596234752", "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}}], "claimsProcess": [], "insuranceGroup": [{"code": "1", "title": "保自己的车", "description": "注意：不保障玻璃、轮胎、底盘破损。若发生车损，需自行承担损失"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"], "briefInsuranceItems": [{"code": "CDW", "name": "超级碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"currencyCode": "CNY", "minExcess": 0, "maxExcess": 4, "coverageWithoutPlatformInsurance": "起赔额约¥0-¥4", "coverageWithoutPlatformInsuranceV2": "起赔额约¥0-¥4"}]}], "insuranceCompareItems": [{"description": "0起赔额", "type": "LDW", "descriptionColorCode": "GREEN"}, {"contains": false, "type": "WDW"}, {"contains": false, "type": "TPL"}, {"contains": false, "type": "PI"}, {"contains": false, "type": "RAP"}, {"contains": false, "type": "CANCEL"}, {"contains": false, "type": "ACCIDENT"}]}, {"insPackageId": 259, "insuranceItems": [{"code": "SCDW", "name": "超级车辆碰撞险", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 342997, "currencyCode": "THB", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额THB 0", "excessLongDesc": "", "minCoverage": 100.0, "maxCoverage": 100.0, "coverageShortDesc": "保额：100THB", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用，起赔额通常较低或为零。"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价。"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["THB 0"]}], "note": "true|THB 0(约¥0)|THB"}}, {"code": "STP", "name": "超级车辆盗抢险", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 342997, "currencyCode": "AED", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "minCoverage": 3.0, "maxCoverage": 3.0, "coverageShortDesc": "保额：3AED", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额AED 2可赔", "coverageWithoutPlatformInsuranceV2": "起赔额AED 2(约¥4)", "coverageWithPlatformInsuranceV2": "*起赔额及以下部分由国内保险公司提供"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用，起赔额通常较低或为零。不承保范围：因车辆钥匙丢失、损坏，车门忘关导致车辆被盗；将车辆长时间停在不当的地方导致车辆被盗。实际赔付范围与标准以门店合同为准。"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：:\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价。"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "content": ["*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看"], "subObject": [{"title": "车行承担", "content": ["AED 2(约¥4)以上部分"]}, {"title": "国内保险公司承担*", "content": ["AED 2(约¥4)及以下部分（据实承担）"]}, {"title": "客户或承租方承担", "content": ["AED 0"]}]}}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": false, "isFromCtrip": true, "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}], "ctripInsuranceIds": [15], "combinations": [{"bomCode": "216664_12678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "title": "婴儿座椅 + 儿童座椅 + 儿童增高座垫", "codes": ["ChildSeat", "ChildSeat", "ChildSeat"], "currency": "CNY", "dayPrice": 55, "gapPrice": 0, "stepPrice": 0, "hike": false, "totalPrice": 110, "payMode": 2, "packageId": 342997}], "productInfoList": [{"productCode": "3", "bomGroupCode": "216664_12678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "priceInfoList": [{"currentCarPrice": 60, "currentDailyPrice": 55, "currentTotalPrice": 110, "localCarPrice": 300.0, "localDailyPrice": 150.0, "localTotalPrice": 300.0, "localCurrencyCode": "THB", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 110, "localOnewayfee": 0.0, "localPoaPrice": 0.0, "localPrepaidPrice": 300.0, "payMode": 2, "productId": "3342997", "packageId": 342997, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 0.1984, "mileInfo": {"name": "里程限制租车期间2km", "isLimited": true, "distance": 2.0, "distanceUnit": "km", "periodUnit": "租车期间", "chargeAmount": 1.0, "chargeUnit": "km", "quantity": 1.0, "desc": "里程限制2km。超出里程限制需额外支付费用，具体金额及税费请以门店合同为准。"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": false, "hours": 0, "cancelDescription": "支付完成至取车时间取消将收取租金30%作为违约金;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：16-90周岁", "minDriverAge": 16, "maxDriverAge": 90, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满6个月", "youngDriverExtraFee": {"localCurrencyCode": "CNY", "currentPrice": 12.0, "localPrice": 12.0, "feeType": 0}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "USD", "maxDeposit": 1.0, "minDeposit": 1.0}, "allTags": [{"title": "1小时确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后车行预计需要1小时确认是否预订成功。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "有损取消", "category": 1, "type": 1, "description": "支付完成至取车时间取消将收取租金30%作为违约金;取车时间后取消将收取全部租金作为违约金", "sortNum": 40, "colorCode": "1", "labelCode": "3683"}, {"title": "芝麻免押", "category": 2, "type": 1, "code": "2", "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "sortNum": 5, "colorCode": "2", "labelCode": "3850"}, {"title": "指定车型", "category": 2, "type": 1, "code": "2", "description": "实际提供的车型即为页面所示车型，而非同车型组的其它车型。", "sortNum": 45, "colorCode": "2", "labelCode": "3551"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "24h营业", "category": 2, "type": 1, "code": "2", "description": "门店24小时营业。", "sortNum": 60, "colorCode": "2", "labelCode": "3491"}, {"title": "自助取车", "category": 2, "type": 1, "code": "2", "description": "当您在门店办理取车手续的时候，您可以移步到柜台旁边的自助取车终端自行办理，自助取车终端提供多国语言的触屏操作，让您方便快捷地完成取车手续，节省在柜台排队等候的时间。", "sortNum": 75, "colorCode": "2", "labelCode": "3599"}, {"title": "满油取还", "category": 2, "type": 1, "description": "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3977"}, {"title": "支持跨境", "category": 2, "type": 1, "code": "2", "description": "支持跨境。", "sortNum": 90, "colorCode": "2", "labelCode": "3602"}, {"title": "支持跨岛", "category": 2, "type": 1, "code": "2", "description": "支持跨岛。", "sortNum": 91, "colorCode": "2", "labelCode": "3601"}, {"title": "支持跨州", "category": 2, "type": 1, "code": "2", "description": "支持跨州", "sortNum": 92, "colorCode": "2", "labelCode": "3603"}, {"title": "免费儿童座椅", "category": 2, "type": 1, "description": "免费儿童座椅。", "sortNum": 109, "colorCode": "2", "labelCode": "3622"}, {"title": "里程限制租车期间2km", "category": 4, "type": 2, "code": "4", "description": "里程限制租车期间2km，超出里程限制需额外支付每km¥1，不包含税。", "sortNum": 125, "colorCode": "4", "labelCode": "3580"}], "insuranceDetails": [{"code": "SCDW", "name": "超级车辆碰撞险", "currencyCode": "THB", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 100.0, "maxCoverage": 100.0}, {"code": "STP", "name": "超级车辆盗抢险", "currencyCode": "AED", "minExcess": 2.0, "maxExcess": 2.0, "minCoverage": 3.0, "maxCoverage": 3.0}], "chargeList": [], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 342997}], "needFlightNo": false, "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 25.0, "localTotalPrice": 50, "localCurrencyCode": "CNY", "currentDailyPrice": 25, "currentTotalPrice": 50, "currentCurrencyCode": "CNY", "uniqueCode": "15"}], "packageItems": [{"code": "Taxes", "name": "增值税", "desc": "增值税是以商品（含应税劳务）在流转过程中产生的增值额作为计税依据而征收的一种流转税。", "sortNum": 2}, {"code": "Taxes", "name": "销售税", "desc": "销售税是为销售某些商品和服务而向管理机构缴纳的一种税，租车过程中产生的任何消费行为都需要缴纳该费用。", "sortNum": 2}, {"code": "Taxes", "name": "商品税", "desc": "商品和服务税（Goods and Service Tax，GST）是以货物和服务为征收对象的间接税。", "sortNum": 2}, {"code": "SCDW", "name": "超级车辆碰撞险", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "STP", "name": "超级车辆盗抢险", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "LM", "name": "里程限制租车期间2km", "desc": "里程限制2km。超出里程限制需额外支付费用，具体金额及税费请以门店合同为准。", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}, {"code": "RAP", "name": "道路救援", "desc": "如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。", "sortNum": 4}, {"code": "WIN", "name": "雪地胎", "desc": "冬季恶劣天气，行车需配备有雪地轮胎", "sortNum": 4}, {"code": "", "name": "婴儿座椅", "desc": "通常适用于体重2千克~9千克的儿童\n通常适用于2-20磅或者 2.27-9.07千克的婴儿", "sortNum": 4}, {"code": "", "name": "儿童座椅", "desc": "通常适用于体重9千克~18千克的儿童\n通常适用于20-40磅或者9.07-18.16千克的儿童", "sortNum": 4}, {"code": "", "name": "儿童增高座垫", "desc": "通常适用于体重18千克~36千克的儿童\n通常适用于40-80磅或者18.16-36.32千克的儿童", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "取车要求", "content": ["驾驶员年龄16-90周岁，且持有驾照至少满6个月"], "summaryContent": ["租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "16-90周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥6个月"}]}], "summaryTitle": "驾驶员需持有本人名下5项材料取车"}, {"title": "护照原件", "subTitle": "", "type": 0, "subObject": [{"title": "中国大陆护照", "subTitle": "中国大陆护照原件", "code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "中国香港护照", "subTitle": "中国香港护照原件", "code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "中国澳门护照", "subTitle": "中国澳门护照原件", "code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "中国台湾护照", "subTitle": "中国台湾护照原件", "code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "其他地区护照", "subTitle": "其他地区护照原件", "code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}]}, {"title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1, "subObject": [{"type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "CN", "subObject": [{"title": "香港驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "HKDL", "sortNum": 3}, {"title": "中国驾照原件 + 车行翻译件", "content": ["中国驾照原件：中国驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 22, "code": "CDL,DLT", "sortNum": 3}, {"title": "中国驾照原件 + 当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 22, "code": "CDL,OLT", "sortNum": 5}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "HK", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 1}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "MO", "subObject": [{"title": "西班牙文公证件", "content": ["西班牙文公证件：由公证处颁发的驾驶员本国驾照西班牙文翻译文件"], "type": 22, "code": "OLTES", "sortNum": 1}, {"title": "国际驾照 + 澳门驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）", "澳门驾照：由中国澳门特别行政区颁发的驾照"], "type": 22, "code": "IDP,MCDL", "sortNum": 29}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "TWDL,IDP", "sortNum": 31}, {"title": "国际驾照 + 台湾驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）", "台湾驾照：由中国台湾地区颁发的驾照"], "type": 22, "code": "IDP,TWDL", "sortNum": 31}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 1}, {"title": "美国驾照", "content": ["美国驾照：持有驾驶员本人护照原件及美国驾照原件即可取车"], "type": 22, "code": "ADL", "sortNum": 2}, {"title": "入境纸 + 香港驾照", "content": ["入境纸：RJZ", "香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "RJZ,HKDL", "sortNum": 2}, {"title": "香港驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "HKDL", "sortNum": 3}, {"title": "国际驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "IDP", "sortNum": 5}, {"title": "英文驾照", "content": ["英文驾照：由驾驶员所在国颁发的正式英文本国驾照"], "type": 22, "code": "EODL", "sortNum": 6}]}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案"}]}], "type": 10}]}, {"title": "国际信用卡（2张）", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡"], "summaryContent": ["到店刷取押金预授权，押金约USD1（约¥7），还车后10-30天内退还。", "若无法提供信用卡，该门店也支持现金支付", "下单时可操作免押，取车时无需携带信用卡"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带2张符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "信用卡支付说明（中）"}]}], "type": 6, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png"], "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}]}, {"title": "现金支付说明", "content": ["该门店同时支持现金支付，若选择现金支付，请携带足够现金前往，详情请咨询门店"], "type": 19}, {"title": "押金说明", "content": ["到店刷取押金预授权，押金约USD1（约¥7），还车后10-30天内退还。"], "summaryContent": ["下单时可操作免押，取车时无需携带信用卡"], "type": 8, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "US$ 1（约¥8）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}], "summaryTitle": "国际信用卡（2张）"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}, {"title": "其他材料", "content": ["（cn）其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案"], "summaryContent": ["（cn）其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案"], "type": 14}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "subTitle": "立即确认", "content": ["预订此产品后供应商将在1小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "subTitle": "该订单确认后有损取消", "content": ["支付完成至取车时间取消将收取租金30%作为违约金;取车时间后取消将收取全部租金作为违约金"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "该订单确认后有损取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "该订单确认后有损取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "该订单确认后有损取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": false, "items": [{"title": "支付完成至2024年4月9日14:30", "subTitle": "支付完成至取车时间", "description": "取消将收取租金30%作为违约金", "showFree": false}, {"title": "2024年4月9日14:30后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金", "showFree": false}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，押金约USD1（约¥7），还车后10-30天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "US$ 1（约¥8）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}, {"title": "里程政策", "content": ["里程限制租车期间2km，超出里程限制需额外支付每km¥1，不包含税。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "能源政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：16-90周岁", "租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["里程限制租车期间2km，超出里程限制需额外支付每km¥1，不包含税。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "能源政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：16-90周岁", "租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}, {"title": "门店提示", "content": ["中文描述提示啊啊啊啊"], "type": 47, "code": "2", "sortNum": 7}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["如您於指定時間前提前取車，請於相同時間內提前還車。假如您提前2小時取車，您須至少提前2小時還車，否則將會額外收費。如需更多資訊，請提前聯絡當地分店或 Trip.com。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["如您無法於預設指定時間內取車，請於所選時間前聯絡當地分店或 Trip.com，以免無法取車。請確保您於所選時段內抵達門市，如您延遲取車，當地分店將不會退還未使用期間的費用。為避免費用糾紛，請在取車簽約前確認還車時間，並預計租車費用。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前還車通常無需額外收費，但請注意當地分店將不會退還未使用期間的費用。若租車合約需提前終止（如車輛由未經登記的駕駛人士駕駛、違反合約條款及細則等），將不作任何退款。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["延遲還車將額外收費，請提前聯絡當地分店了解相關費用的具體資訊。請注意，延遲還車除另行收費外，您可能面臨觸犯法律的風險。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "取车描述", "content": [], "type": 58, "code": "2", "subObject": [{"title": "签合同前核对金额", "content": ["办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。"], "type": 59, "code": "2", "sortNum": 1}, {"title": "验车注意事项", "content": ["验车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身，油量里程，车损特写等），以免产生售后纠纷。"], "type": 60, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}, {"title": "雪地胎政策", "content": ["免费儿童座椅"], "type": 46, "code": "2", "sortNum": 3}], "sortNum": 5}, {"title": "旅行限制", "content": [], "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["测试啊啊 啊"], "type": 27, "code": "2", "sortNum": 1}], "sortNum": 6}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 7}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}], "sortNum": 8}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 16, "custumerTotalPrice": 150, "custumerDailyPrice": 75, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "localTotalPrice": 150, "localDailyPrice": 75.0, "localCurrencyCode": "CNY", "desc": "驾乘意外险短描述", "longDesc": "驾乘意外险长描述", "noCoverageContent": ["驾乘意外险不保障内容"], "typeCode": "ACCIDENT_COVER", "days": 2, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "version": "OPI1128778955596234752", "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}}], "claimsProcess": [{"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": " https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}, {"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 5}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 6}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 7}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 8}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"], "briefInsuranceItems": [{"code": "CDW", "name": "超级碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"currencyCode": "CNY", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "coverageWithoutPlatformInsurance": "原起赔额¥4可赔", "coverageWithoutPlatformInsuranceV2": "起赔额¥0-¥4", "coverageWithPlatformInsuranceV2": "*起赔额及以下部分由国内保险公司提供"}]}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}], "insuranceCompareItems": [{"description": "0起赔额", "type": "LDW", "descriptionColorCode": "GREEN"}, {"contains": true, "type": "WDW"}, {"contains": false, "type": "TPL"}, {"contains": false, "type": "PI"}, {"contains": true, "type": "RAP"}, {"contains": true, "type": "CANCEL"}, {"contains": false, "type": "ACCIDENT"}]}, {"insPackageId": 771, "insuranceItems": [{"code": "SCDW", "name": "超级车辆碰撞险", "description": "保障车辆碰撞损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 342997, "currencyCode": "THB", "minExcess": 0.0, "maxExcess": 0.0, "excessShortDesc": "起赔额THB 0", "excessLongDesc": "", "minCoverage": 100.0, "maxCoverage": 100.0, "coverageShortDesc": "保额：100THB", "coverageLongDesc": ""}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用，起赔额通常较低或为零。"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：\n单车事故；\n车辆涉水造成的车辆损坏；\n因加错燃油类型导致车辆损坏；\n碰撞到野生动物造成的车辆损失；\n不爱惜车辆，在非正常公路行驶；\n在正常道路行驶期间，对于国家公共设施的损坏；\n对玻璃、车灯、内饰、轮胎、底盘和车顶的损坏；\n由未登记“额外驾驶员”的其他人驾驶车辆；\n驾驶车辆跨境未告知工作人员，出境后发生车损；\n未经租车公司授权的修车，拖车产生的费用或损失；\n超速，酒驾，运输危险品，超载等违反当地法律情况发生的意外；\n车辆停运费或维修手续费、交通违章罚款、拖车费、路桥费、燃油费等非赔偿给租车公司的费用；\n货币汇率波动导致的汇率损失，和车行、银行等收款机构收取的刷卡费、信用卡费和汇款手续费；\n由于自然灾害，或不可抗力因素导致的损失。\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价。"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "subObject": [{"title": "车行承担", "content": ["全部损失"]}, {"title": "客户或承租方承担", "content": ["THB 0"]}], "note": "true|THB 0(约¥0)|THB"}}, {"code": "STP", "name": "超级车辆盗抢险", "description": "保障车辆被盗的损失", "isInclude": true, "isFromCtrip": false, "insuranceDetail": [{"packageId": 342997, "currencyCode": "AED", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "excessLongDesc": "", "minCoverage": 3.0, "maxCoverage": 3.0, "coverageShortDesc": "保额：3AED", "coverageLongDesc": "", "coverageWithoutPlatformInsurance": "原起赔额AED 2可赔", "coverageWithoutPlatformInsuranceV2": "起赔额AED 2(约¥4)", "coverageWithPlatformInsuranceV2": "*起赔额及以下部分由国内保险公司提供"}], "converageExplain": {"title": "承保范围", "content": ["车辆在正常租赁驾驶期间被盗被抢，或因被盗抢造成的车辆损坏，将由保险公司承担起赔额以上的费用，起赔额通常较低或为零。不承保范围：因车辆钥匙丢失、损坏，车门忘关导致车辆被盗；将车辆长时间停在不当的地方导致车辆被盗。实际赔付范围与标准以门店合同为准。"]}, "unConverageExplain": {"title": "不承保范围", "content": ["不承保范围：:\n钥匙遗落车内导致被盗；\n钥匙丢失，损坏或被盗；\n车门忘关导致被盗；\n车内额外设备如GPS，WIFI，儿童座椅等；\n在未经授权的区域行驶；\n将车辆长时间停在不当的地方导致车辆被盗；\n因车辆被盗被抢导致的车辆停运费或维修手续费；\n违反合同或当地法律的情况下导致的损失；\n*实际赔付范围与标准以门店合同为准"]}, "claimProcess": {"title": "理赔流程", "subObject": [{"title": "发生事故后报警并联系车行", "content": ["发生事故后，请您拨打当地报警电话获取车损报告，并联系车行告知情况"]}, {"title": "等待最终定审(45-60个工作日左右)", "content": ["在您租期结束时，车行会检查车辆受损情况，从您信用卡预授权中扣除起赔额费用（若有），待车行确认最终车损金额（一般需要45-60个工作日）。若最终车损金额大于起赔额，只收起赔额；若小于起赔额，原路退还差价。"]}]}, "groupCode": "1", "excessTable": {"title": "起赔额 0起赔额", "content": ["*套餐中包含国内保险公司提供的安心补充险，更多保障内容可点击安心补充险查看"], "subObject": [{"title": "车行承担", "content": ["AED 2(约¥4)以上部分"]}, {"title": "国内保险公司承担*", "content": ["AED 2(约¥4)及以下部分（据实承担）"]}, {"title": "客户或承租方承担", "content": ["AED 0"]}]}}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥75/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥75/天"}], "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}], "ctripInsuranceIds": [15, 16], "combinations": [{"bomCode": "216664_12678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "title": "婴儿座椅 + 儿童座椅 + 儿童增高座垫", "codes": ["ChildSeat", "ChildSeat", "ChildSeat"], "currency": "CNY", "dayPrice": 130, "gapPrice": 0, "stepPrice": 0, "hike": false, "totalPrice": 260, "payMode": 2, "packageId": 342997}], "productInfoList": [{"productCode": "3", "bomGroupCode": "216664_12678____FRFB_LM_RAP_SCDW_STP_Taxes_Taxes_Taxes_WIN_0_0", "priceInfoList": [{"currentCarPrice": 60, "currentDailyPrice": 130, "currentTotalPrice": 260, "localCarPrice": 300.0, "localDailyPrice": 150.0, "localTotalPrice": 300.0, "localCurrencyCode": "THB", "currentCurrencyCode": "CNY", "currentOnewayfee": 0, "currentPoaPrice": 0, "currentPrepaidPrice": 260, "localOnewayfee": 0.0, "localPoaPrice": 0.0, "localPrepaidPrice": 300.0, "payMode": 2, "productId": "3342997", "packageId": 342997, "packageType": 0, "vcExtendRequest": {"responsePickUpLocationId": "", "responseReturnLocationId": ""}, "exchangeRate": 0.1984, "mileInfo": {"name": "里程限制租车期间2km", "isLimited": true, "distance": 2.0, "distanceUnit": "km", "periodUnit": "租车期间", "chargeAmount": 1.0, "chargeUnit": "km", "quantity": 1.0, "desc": "里程限制2km。超出里程限制需额外支付费用，具体金额及税费请以门店合同为准。"}, "confirmInfo": {"confirmTitle": "1小时内确认", "confirmDesc": "预订此产品后供应商将在1小时内确认订单。 You can cancel at any time before your booking is confirmed. If your booking cannot be confirmed, Trip.com will provide a full refund.", "confirmRightNow": false, "confirmTime": 1.0}, "cancelRule": {"isTotalLoss": true, "isFreeCancel": false, "isFreeCancelNow": false, "hours": 0, "cancelDescription": "支付完成至取车时间取消将收取租金30%作为违约金;取车时间后取消将收取全部租金作为违约金"}, "ageRestriction": {"description": "驾驶员年龄要求：16-90周岁", "minDriverAge": 16, "maxDriverAge": 90, "youngDriverAge": 25, "youngDriverAgeDesc": "租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。", "licenceAge": 1, "licenceAgeDesc": "所有驾驶员驾龄必须至少满6个月", "youngDriverExtraFee": {"localCurrencyCode": "CNY", "currentPrice": 12.0, "localPrice": 12.0, "feeType": 0}, "oldDriverExtraFee": {"currentPrice": 0.0, "localPrice": 0.0}}, "creditCardInfo": {"cardList": [{"name": "维萨", "type": "VI"}, {"name": "万事达", "type": "MC"}, {"name": "美国运通", "type": "AE"}, {"name": "大来卡", "type": "DC"}], "depositCurrencyCode": "USD", "maxDeposit": 1.0, "minDeposit": 1.0}, "allTags": [{"title": "1小时确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后车行预计需要1小时确认是否预订成功。", "sortNum": 10, "colorCode": "1", "labelCode": "3582"}, {"title": "有损取消", "category": 1, "type": 1, "description": "支付完成至取车时间取消将收取租金30%作为违约金;取车时间后取消将收取全部租金作为违约金", "sortNum": 40, "colorCode": "1", "labelCode": "3683"}, {"title": "芝麻免押", "category": 2, "type": 1, "code": "2", "description": "芝麻信用分达550分以上，有机会享免除车辆押金服务，无需到店使用信用卡刷押金。", "sortNum": 5, "colorCode": "2", "labelCode": "3850"}, {"title": "指定车型", "category": 2, "type": 1, "code": "2", "description": "实际提供的车型即为页面所示车型，而非同车型组的其它车型。", "sortNum": 45, "colorCode": "2", "labelCode": "3551"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "24h营业", "category": 2, "type": 1, "code": "2", "description": "门店24小时营业。", "sortNum": 60, "colorCode": "2", "labelCode": "3491"}, {"title": "自助取车", "category": 2, "type": 1, "code": "2", "description": "当您在门店办理取车手续的时候，您可以移步到柜台旁边的自助取车终端自行办理，自助取车终端提供多国语言的触屏操作，让您方便快捷地完成取车手续，节省在柜台排队等候的时间。", "sortNum": 75, "colorCode": "2", "labelCode": "3599"}, {"title": "满油取还", "category": 2, "type": 1, "description": "取车时油量是满的，还车时也需保证是满的。若有差额，租车公司将会收取未加满部分的能源费用以及工作人员去加油的工时费。建议保留最后一次加油时的单据以及取还车时显示的车辆油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3977"}, {"title": "支持跨境", "category": 2, "type": 1, "code": "2", "description": "支持跨境。", "sortNum": 90, "colorCode": "2", "labelCode": "3602"}, {"title": "支持跨岛", "category": 2, "type": 1, "code": "2", "description": "支持跨岛。", "sortNum": 91, "colorCode": "2", "labelCode": "3601"}, {"title": "支持跨州", "category": 2, "type": 1, "code": "2", "description": "支持跨州", "sortNum": 92, "colorCode": "2", "labelCode": "3603"}, {"title": "免费儿童座椅", "category": 2, "type": 1, "description": "免费儿童座椅。", "sortNum": 109, "colorCode": "2", "labelCode": "3622"}, {"title": "里程限制租车期间2km", "category": 4, "type": 2, "code": "4", "description": "里程限制租车期间2km，超出里程限制需额外支付每km¥1，不包含税。", "sortNum": 125, "colorCode": "4", "labelCode": "3580"}], "insuranceDetails": [{"code": "SCDW", "name": "超级车辆碰撞险", "currencyCode": "THB", "minExcess": 0.0, "maxExcess": 0.0, "minCoverage": 100.0, "maxCoverage": 100.0}, {"code": "STP", "name": "超级车辆盗抢险", "currencyCode": "AED", "minExcess": 2.0, "maxExcess": 2.0, "minCoverage": 3.0, "maxCoverage": 3.0}], "chargeList": [], "promotionInfo": {}, "vendorPromotionList": [], "pkgSellingRuleId": 342997}], "needFlightNo": false, "equipments": [], "ctripInsurances": [{"name": "安心补充险", "localDailyPrice": 25.0, "localTotalPrice": 50, "localCurrencyCode": "CNY", "currentDailyPrice": 25, "currentTotalPrice": 50, "currentCurrencyCode": "CNY", "uniqueCode": "15"}, {"name": "驾乘意外险", "localDailyPrice": 75.0, "localTotalPrice": 150, "localCurrencyCode": "CNY", "currentDailyPrice": 75, "currentTotalPrice": 150, "currentCurrencyCode": "CNY", "uniqueCode": "16"}], "packageItems": [{"code": "Taxes", "name": "增值税", "desc": "增值税是以商品（含应税劳务）在流转过程中产生的增值额作为计税依据而征收的一种流转税。", "sortNum": 2}, {"code": "Taxes", "name": "销售税", "desc": "销售税是为销售某些商品和服务而向管理机构缴纳的一种税，租车过程中产生的任何消费行为都需要缴纳该费用。", "sortNum": 2}, {"code": "Taxes", "name": "商品税", "desc": "商品和服务税（Goods and Service Tax，GST）是以货物和服务为征收对象的间接税。", "sortNum": 2}, {"code": "SCDW", "name": "超级车辆碰撞险", "desc": "保障车辆碰撞损失", "sortNum": 3}, {"code": "STP", "name": "超级车辆盗抢险", "desc": "保障车辆被盗的损失", "sortNum": 3}, {"code": "LM", "name": "里程限制租车期间2km", "desc": "里程限制2km。超出里程限制需额外支付费用，具体金额及税费请以门店合同为准。", "sortNum": 4}, {"code": "FRFB", "name": "满油取还", "desc": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。", "sortNum": 4}, {"code": "RAP", "name": "道路救援", "desc": "如果车辆故障或损坏，可直接联系租车协议上的门店或道路救援电话，租车公司会及时给您提供相应的帮助； 遇到交通事故请第一时间报警，再跟租车公司了解如何处理。", "sortNum": 4}, {"code": "WIN", "name": "雪地胎", "desc": "冬季恶劣天气，行车需配备有雪地轮胎", "sortNum": 4}, {"code": "", "name": "婴儿座椅", "desc": "通常适用于体重2千克~9千克的儿童\n通常适用于2-20磅或者 2.27-9.07千克的婴儿", "sortNum": 4}, {"code": "", "name": "儿童座椅", "desc": "通常适用于体重9千克~18千克的儿童\n通常适用于20-40磅或者9.07-18.16千克的儿童", "sortNum": 4}, {"code": "", "name": "儿童增高座垫", "desc": "通常适用于体重18千克~36千克的儿童\n通常适用于40-80磅或者18.16-36.32千克的儿童", "sortNum": 4}], "naked": false, "pickUpMaterials": [{"title": "取车要求", "content": ["驾驶员年龄16-90周岁，且持有驾照至少满6个月"], "summaryContent": ["租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。"], "type": 20, "summaryContentObject": [{"contentStyle": "age", "stringObjs": [{"content": "16-90周岁"}]}, {"contentStyle": "licenseAge", "stringObjs": [{"content": "≥6个月"}]}], "summaryTitle": "驾驶员需持有本人名下5项材料取车"}, {"title": "护照原件", "subTitle": "", "type": 0, "subObject": [{"title": "中国大陆护照", "subTitle": "中国大陆护照原件", "code": "CN", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "中国香港护照", "subTitle": "中国香港护照原件", "code": "HK", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "中国澳门护照", "subTitle": "中国澳门护照原件", "code": "MO", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "中国台湾护照", "subTitle": "中国台湾护照原件", "code": "TW", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件", "护照签发地需与驾照发证国家/地区一致"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}, {"title": "其他地区护照", "subTitle": "其他地区护照原件", "code": "OH", "subObject": [{"title": "护照原件", "content": ["主驾驶员和其他额外驾驶员需提供他们名下的护照原件"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案身份证明说明文案"}]}], "type": 21}]}]}, {"title": "驾照组合", "content": ["你取车时，主驾驶员和其他额外驾驶员需要提供他们名下的驾照组合，电子版或照片将不被认可", "持有不同护照需要驾照组合要求可能不同"], "type": 1, "subObject": [{"type": 5, "subObject": [{"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "CN", "subObject": [{"title": "香港驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "HKDL", "sortNum": 3}, {"title": "中国驾照原件 + 车行翻译件", "content": ["中国驾照原件：中国驾照原件", "车行翻译件：车行提供官方翻译模板。在线立即生成，下载后彩色打印即可，可免费办理。可至携程APP境外租车首页办理。 "], "type": 22, "code": "CDL,DLT", "sortNum": 3}, {"title": "中国驾照原件 + 当地语言公证件", "content": ["中国驾照原件：中国驾照原件", "当地语言公证件：由公证处颁发的驾驶员本国驾照取车国语言翻译文件"], "type": 22, "code": "CDL,OLT", "sortNum": 5}]}, {"title": "门店支持以下驾照组合", "type": 5, "code": "HK", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 1}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "MO", "subObject": [{"title": "西班牙文公证件", "content": ["西班牙文公证件：由公证处颁发的驾驶员本国驾照西班牙文翻译文件"], "type": 22, "code": "OLTES", "sortNum": 1}, {"title": "国际驾照 + 澳门驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）", "澳门驾照：由中国澳门特别行政区颁发的驾照"], "type": 22, "code": "IDP,MCDL", "sortNum": 29}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "TW", "subObject": [{"title": "台湾驾照 + 国际驾照", "content": ["台湾驾照：由中国台湾地区颁发的驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "TWDL,IDP", "sortNum": 31}, {"title": "国际驾照 + 台湾驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）", "台湾驾照：由中国台湾地区颁发的驾照"], "type": 22, "code": "IDP,TWDL", "sortNum": 31}]}, {"title": "门店支持以下驾照组合(任选其一)", "type": 5, "code": "OH", "subObject": [{"title": "驾驶员本国驾照 + 国际驾照", "content": ["驾驶员本国驾照：由驾驶员所在国颁发的正式本国驾照", "国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "ODL,IDP", "sortNum": 1}, {"title": "美国驾照", "content": ["美国驾照：持有驾驶员本人护照原件及美国驾照原件即可取车"], "type": 22, "code": "ADL", "sortNum": 2}, {"title": "入境纸 + 香港驾照", "content": ["入境纸：RJZ", "香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "RJZ,HKDL", "sortNum": 2}, {"title": "香港驾照", "content": ["香港驾照：由中国香港特别行政区颁发的驾照，港澳台、53个英联邦国家/地区适用"], "type": 22, "code": "HKDL", "sortNum": 3}, {"title": "国际驾照", "content": ["国际驾照：由联合国国际道路公约成员国颁发的驾驶证明文件。适合非中国内地用户，因我国内地未加入《联合国道路交通公约》，中国内地驾照无法办理。（护照、驾照、国际驾照（IDP）发证国家需一致方可成功取车）"], "type": 22, "code": "IDP", "sortNum": 5}, {"title": "英文驾照", "content": ["英文驾照：由驾驶员所在国颁发的正式英文本国驾照"], "type": 22, "code": "EODL", "sortNum": 6}]}]}, {"contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "（cn）驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案驾照提示文案"}]}], "type": 10}]}, {"title": "国际信用卡（2张）", "content": ["带芯片，卡号为凸字（摸起来有凹凸感）。该车型需要两张信用卡"], "summaryContent": ["到店刷取押金预授权，押金约USD1（约¥7），还车后10-30天内退还。", "若无法提供信用卡，该门店也支持现金支付", "下单时可操作免押，取车时无需携带信用卡"], "type": 2, "subObject": [{"title": "信用卡要求", "content": ["请保证可用金额足以支付押金", "卡片所示姓名与主驾驶员护照姓名一致", "需带2张符合要求的国际信用卡（实体卡），不支持电子信用卡、虚拟卡、返现卡（Bonus+/Cash Rewards等）"], "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "信用卡支付说明（中）"}]}], "type": 6, "urlList": ["https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/visa.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/mastercard.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/AmericanExpress.png", "https://pic.c-ctrip.com/car/osd/mobile/crn/osdfillcreditcard/dinersclub.png"], "summaryContentObject": [{"contentStyle": "SupportCreditCard", "stringObjs": [{"content": "1"}]}, {"contentStyle": "UnionPay", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedEmbossed", "stringObjs": [{"content": "1"}]}, {"contentStyle": "NeedChip", "stringObjs": [{"content": "1"}]}]}, {"title": "现金支付说明", "content": ["该门店同时支持现金支付，若选择现金支付，请携带足够现金前往，详情请咨询门店"], "type": 19}, {"title": "押金说明", "content": ["到店刷取押金预授权，押金约USD1（约¥7），还车后10-30天内退还。"], "summaryContent": ["下单时可操作免押，取车时无需携带信用卡"], "type": 8, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "US$ 1（约¥8）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}], "summaryTitle": "国际信用卡（2张）"}, {"title": "提车凭证", "content": ["取车的时候，租车公司需要用户的提车凭证打印版或者电子提车凭证。如果不出示该凭证，租车公司可能会拒绝你取车，或者重新向你收租车费。"], "summaryContent": ["订单确认后，携程为您提供"], "type": 3}, {"title": "其他材料", "content": ["（cn）其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案"], "summaryContent": ["（cn）其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案其他材料要求文案"], "type": 14}], "carRentalMustRead": [{"title": "额外驾驶员", "type": 6, "code": "4", "sortNum": 1}, {"title": "营业时间外取还车", "type": 7, "code": "4", "sortNum": 2}, {"title": "提前/延后取还车", "type": 41, "code": "4", "sortNum": 3}, {"title": "费用须知", "type": 40, "code": "4", "sortNum": 4}, {"title": "确认政策", "subTitle": "立即确认", "content": ["预订此产品后供应商将在1小时内确认订单"], "type": 0, "code": "1", "sortNum": 1}, {"title": "取消政策", "subTitle": "该订单确认后有损取消", "content": ["支付完成至取车时间取消将收取租金30%作为违约金;取车时间后取消将收取全部租金作为违约金"], "type": 1, "code": "1", "sortNum": 2, "table": [{"title": "取消政策", "subTitle": "该订单确认后有损取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "该订单确认后有损取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "该订单确认后有损取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": false, "items": [{"title": "支付完成至2024年4月9日14:30", "subTitle": "支付完成至取车时间", "description": "取消将收取租金30%作为违约金", "showFree": false}, {"title": "2024年4月9日14:30后", "subTitle": "取车时间后", "description": "取消将收取全部租金作为违约金", "showFree": false}]}]}, {"title": "押金说明", "content": ["到店刷取押金预授权，押金约USD1（约¥7），还车后10-30天内退还。"], "type": 2, "code": "1", "sortNum": 3, "table": [{"title": "押金", "description": "芝麻信用分达550以上，下单页验证后有机会免除押金，无需到店支付押金。", "contents": [{"stringObjs": [{"content": "US$ 1（约¥8）"}]}], "showFree": true, "positiveDesc": "授权成功后减免"}]}, {"title": "里程政策", "content": ["里程限制租车期间2km，超出里程限制需额外支付每km¥1，不包含税。"], "type": 3, "code": "1", "sortNum": 4}, {"title": "能源政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "1", "sortNum": 5}, {"title": "年龄要求", "content": ["驾驶员年龄要求：16-90周岁", "租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "1", "sortNum": 6}, {"title": "费用须知", "content": [], "type": 40, "code": "2", "subObject": [{"title": "里程政策", "content": ["里程限制租车期间2km，超出里程限制需额外支付每km¥1，不包含税。"], "type": 3, "code": "2", "sortNum": 1}, {"title": "能源政策", "content": ["满油取还", "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的车辆总里程和油量表的照片以备用。"], "type": 4, "code": "2", "sortNum": 2}, {"title": "年龄要求", "content": ["驾驶员年龄要求：16-90周岁", "租车公司对16-25周岁将收取“青年驾驶费”；参考价格：¥12/次，最多收取¥15，不包含税，需在线上或门店支付。", "额外驾驶员的年龄限制和收费标准，实际请以门店为准。"], "type": 5, "code": "2", "sortNum": 3}, {"title": "额外驾驶员", "content": ["不含额外驾驶员。如需多人开车，请在取车时向店员申请，可能会有额外收费。每位驾驶员都需要出示与主驾驶员相同的取车证件，如果未经登记的人驾驶车辆，保险将会失效。"], "type": 6, "code": "2", "sortNum": 4}, {"title": "营业时间外取还车", "content": ["如果您的取还车时问不在此营业时间范国内，请务必提前联系门店了解是否需要收取额外费用及税费，或者是否支持营业时间外取还车。"], "type": 7, "code": "2", "sortNum": 5}, {"title": "当地费用", "content": ["订单费用不包括在租车过程中产生的额外费用和罚款，如过路过桥费、超速罚款等。如果您产生此类任何费用，当地管理部门会找到租车公司联系您。一般是还车后几个月内，您需要补交违章费并支付租车公司的管理费用。在租车套餐以外产生的任何需到店支付的费用，都可能有当地税费的产生，如异地还车费及税费等。"], "type": 16, "code": "2", "sortNum": 6}, {"title": "门店提示", "content": ["中文描述提示啊啊啊啊"], "type": 47, "code": "2", "sortNum": 7}], "sortNum": 1}, {"title": "提前/延后取还车", "content": [], "type": 41, "code": "2", "subObject": [{"title": "提前取车", "content": ["如您於指定時間前提前取車，請於相同時間內提前還車。假如您提前2小時取車，您須至少提前2小時還車，否則將會額外收費。如需更多資訊，請提前聯絡當地分店或 Trip.com。"], "type": 17, "code": "2", "sortNum": 1}, {"title": "延迟取车", "content": ["如您無法於預設指定時間內取車，請於所選時間前聯絡當地分店或 Trip.com，以免無法取車。請確保您於所選時段內抵達門市，如您延遲取車，當地分店將不會退還未使用期間的費用。為避免費用糾紛，請在取車簽約前確認還車時間，並預計租車費用。"], "type": 18, "code": "2", "sortNum": 2}, {"title": "提前还车", "content": ["提前還車通常無需額外收費，但請注意當地分店將不會退還未使用期間的費用。若租車合約需提前終止（如車輛由未經登記的駕駛人士駕駛、違反合約條款及細則等），將不作任何退款。"], "type": 10, "code": "2", "sortNum": 3}, {"title": "延迟还车", "content": ["延遲還車將額外收費，請提前聯絡當地分店了解相關費用的具體資訊。請注意，延遲還車除另行收費外，您可能面臨觸犯法律的風險。"], "type": 11, "code": "2", "sortNum": 4}], "sortNum": 2}, {"title": "取车描述", "content": [], "type": 58, "code": "2", "subObject": [{"title": "签合同前核对金额", "content": ["办理取车手续时，请您在签字确认前仔细查看合同上的结算金额是否和提车凭证上一致。若不一致，门店可能添加了额外保险或产品，请根据实际需求考虑是否选购；若不购买，请告知店员去掉。如果与门店协商过程有任何问题，请保留好相关凭证联系平台客服为您协商解决。"], "type": 59, "code": "2", "sortNum": 1}, {"title": "验车注意事项", "content": ["验车时，请仔细检查车辆外观是否有破损，建议对车辆实况进行留存凭证（车辆周身，油量里程，车损特写等），以免产生售后纠纷。"], "type": 60, "code": "2", "sortNum": 2}], "sortNum": 3}, {"title": "租车保障", "content": [], "type": 20, "code": "2", "subObject": [{"title": "管理费", "content": ["如果因车损（包括但不限于碰撞、盗抢等原因）或违章导致车辆进场维修或被扣押，您需要按照服务保障规则支付车损费用，并且租车公司可能会要求您支付停运或管理费。请注意，停运或管理费一般不包含在碰撞盗抢的服务保障范围内。", "此外，如果您在租车时使用信用卡进行预授权，即使租车公司已经退还了押金，他们也可以在结算完成后从您的信用卡预授权中再次扣款。因此，结算该类费用的时间可能会超出押金退还时间，请您留意。"], "type": 22, "code": "2", "sortNum": 2}], "sortNum": 4}, {"title": "附加服务", "content": [], "type": 23, "code": "2", "subObject": [{"title": "额外设备", "content": ["额外设备是指儿童座椅、GPS、额外驾驶员等，不同租车公司提供的额外设备不同。若提供服务，您可以在网上预订时选取，根据门店政策在线预付或者取车时于租车柜台支付。"], "type": 24, "code": "2", "sortNum": 1}, {"title": "税费", "content": ["所有额外服务将会收取销售税费和当地费用。"], "type": 25, "code": "2", "sortNum": 2}, {"title": "雪地胎政策", "content": ["免费儿童座椅"], "type": 46, "code": "2", "sortNum": 3}], "sortNum": 5}, {"title": "旅行限制", "content": [], "type": 26, "code": "2", "subObject": [{"title": "跨境政策", "content": ["测试啊啊 啊"], "type": 27, "code": "2", "sortNum": 1}], "sortNum": 6}, {"title": "取消，未取车和修改", "content": [], "type": 42, "code": "2", "subObject": [{"title": "修改", "content": ["如需修改驾驶员姓名和年龄、取还车时间、地点，请取消订单后重新预订。"], "type": 31, "code": "2", "sortNum": 1}, {"title": "No show（未取车）", "content": ["noshow 是指当你：", "计划提前取消订单但未告知我们，或者", "未在规定的时间内取车，或者", "无法提供取车时要出示的文件，或者", "无法提供主驾驶员名下有足够额度的信用卡", "以上情形，你之前预定的订单金额不会退还给你。"], "type": 32, "code": "2", "sortNum": 2}], "sortNum": 7}, {"title": "租车公司重要信息", "content": [], "type": 33, "code": "2", "subObject": [{"title": "意外或故障", "content": ["事故或故障-如果发生故障、事故或者机械故障，您必须立即致电租车公司。他们必须授权修理或更换车辆的权限。请保留要求完成的所有文件的副本。如果你想提车索赔，可能会需要这些文件。对于事故，警方报告和租车柜台提供的车祸报告是需要的。"], "type": 34, "code": "2", "sortNum": 1}, {"title": "道路救援", "content": ["道路救援——大多数租车公司会提供某种收费的紧急救援服务。当你取车的时候，请确保你找到保存文件的地方。绝大多时情况下，是在手套箱或附着在遮阳板上。"], "type": 35, "code": "2", "sortNum": 2}, {"title": "遗失钥匙", "content": ["遗失钥匙-如果你遗失了钥匙，你需要支付更换的费用"], "type": 36, "code": "2", "sortNum": 3}, {"title": "安全带", "content": ["安全带-无论你驾驶到哪个国家，驾驶员和乘客必须佩带安全带，无论是前排还是后排。"], "type": 37, "code": "2", "sortNum": 4}, {"title": "禁止吸烟", "content": ["禁止在车内吸烟，还车时车内外的清洁状况需要保持与提车时一致，否则会被租车公司收取清洁费用。"], "type": 38, "code": "2", "sortNum": 5}, {"title": "价格计算", "content": ["价格计算-价格是基于你取还车的日期和时间的，如果你延迟取车或者提前还车，未使用期间的费用不会退还。", "取车的时候，客人可能会选择不同的车，而不是预订时候的车。", "如果他们这么做，他们可能需要支付额外费用，即使新车比他们先前选的小。", "如果你遭遇车祸，车辆碰撞严重，你可能需要额外支付超出起赔额部分的费用。"], "type": 39, "code": "2", "sortNum": 6}, {"title": "违章条款", "content": ["请您在驾驶途中和停车时遵守当地交通法规。供应商要求租客承担一切于租车途中违章产生的罚款和费用，包括行驶、超速和停车等。若您还车时还未收到罚单，供应商收到后将会通过信用卡扣款等途径向您收取费用。"], "type": 13, "code": "2", "sortNum": 7}], "sortNum": 8}], "searchUnionPay": false, "searchCreditCard": false, "platformInsurance": {"insurance": {"name": "驾乘意外险", "id": 16, "custumerTotalPrice": 150, "custumerDailyPrice": 75, "code": "MP18021535PK00080224", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "localTotalPrice": 150, "localDailyPrice": 75.0, "localCurrencyCode": "CNY", "desc": "驾乘意外险短描述", "longDesc": "驾乘意外险长描述", "noCoverageContent": ["驾乘意外险不保障内容"], "typeCode": "ACCIDENT_COVER", "days": 2, "customerCurrencyCode": "CNY"}, "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "version": "OPI1128778955596234752", "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}}], "claimsProcess": [{"subTitle": "您购买的套餐含国内保险公司提供的安心补充险，请参照如下理赔流程申请理赔", "url": " https://pages.c-ctrip.com/tour/InsuranceClause/bcqxlpcl.pdf", "subObject": [{"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 1}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 2}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 3}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 4}, {"title": "报案", "content": ["发生事故后，您需要先行按车行保障理赔流程与车行处理车损事宜，并垫付相关费用。待结束用车后再联系保险公司申请理赔。报案及理赔邮箱：<EMAIL>"], "type": 5}, {"title": "车损重要理赔材料", "content": ["车损照片、维修费用支付凭证、维修/车损清单、警方报告等"], "type": 6}, {"title": "理赔审核", "content": ["材料提交保险公司后开始理赔审核，如有问题会联系被保险人核实补充"], "type": 7}, {"title": "支付赔款", "content": ["理赔款会以银行转账方式支付到被保险人提供的境内银行账户"], "type": 8}]}, {"subTitle": "您购买的套餐含国内保险公司提供的驾乘意外险，请参照如下理赔流程申请理赔", "url": "https://pages.c-ctrip.com/tour/InsuranceClause/jcywlpcl.pdf", "subObject": [{"title": "驾乘意外险理赔流程步骤一名称", "content": ["驾乘意外险理赔流程步骤一描述"], "type": 1}, {"title": "驾乘意外险理赔流程步骤二名称", "content": ["驾乘意外险理赔流程步骤二描述"], "type": 2}]}], "insuranceGroup": [{"code": "1", "title": "保自己的车"}, {"code": "3", "title": "保车内人员财物"}], "extraDesc": ["", "车辆在正常租赁驾驶期间发生碰撞或刮蹭产生的损失，将由保险公司承担起赔额以上的费用。 \n*实际赔付范围与标准以门店合同为准-卡拉比"], "briefInsuranceItems": [{"code": "CDW", "name": "超级碰撞盗抢保障", "isFromCtrip": false, "insuranceDetail": [{"currencyCode": "CNY", "minExcess": 0, "maxExcess": 0, "excessShortDesc": "0起赔额", "coverageWithoutPlatformInsurance": "原起赔额¥4可赔", "coverageWithoutPlatformInsuranceV2": "起赔额¥0-¥4", "coverageWithPlatformInsuranceV2": "*起赔额及以下部分由国内保险公司提供"}]}, {"code": "FULL_COVERAGE", "name": "安心补充险", "description": "保障玻璃轮胎底盘，补偿道路救援费用", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥25/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥25/天"}], "converageExplain": {"title": "承保范围", "content": ["由国内知名保险公司提供，主要承保车行保障不包含的玻璃、轮胎、底盘等损失，以及超出车行保障额度的车损或盗抢损失。比在租车行柜台单独购买便宜，且在取车时间前都可无损取消！\n详细保障请见下方："]}, "unConverageExplain": {"title": "不承保范围", "content": ["1、 对第三方造成的人身或财产损失;\n2、 租赁机动车发生意外事故时，不在车行提供的碰撞或盗抢保障的责任范围内的损失;\n3、 未经当地租车行认可的自主修车费用;\n4、 由于违反当地法律法规而产生的相关违章费用，包括但不限于交通违法违章罚款、违法违章拖车费、诉讼费等;\n5、 被保险人的故意行为导致的损失;\n6、 证件不被租车公司认可导致无法取车的损失;\n7、 租车公司未授权的驾驶人驾车导致的损失;\n8、 汇率损失、刷卡费、信用卡费、汇款手续费;\n9、 其他不属于保险合同条款范围内的损失和费用;"]}, "productId": 15, "groupCode": "1", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "50000", "descList": ["车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）", "车行车损和盗抢保障的自付部分", "玻璃、轮胎、底盘损失的自付部分", "道路救援费、拖车费、人工费、锁/钥匙替换费的自付部分", "涉及的税费及管理费用（此项以1500元为限）"]}, {"title": "50000", "descList": ["旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金", "旅行期间被保险人（主驾驶员）因意外事故导致的身故、伤残的保险金"]}, {"title": "3000", "descList": ["机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用", "机动车封闭的车厢内部及后备箱所租赁物品（如导航仪）的盗抢损失", "证件被盗抢或丟失导致无法取车的租车费用损失", "车辆停运费或被迫更换费用", "航班取消或延误导致的延迟取车费、营业时间外服务而产生的费用"]}, {"title": "1000", "descList": ["违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%", "违规用车赔偿金（如钥匙丢失或被盗抢、加错油），此项自付比例20%"]}, {"title": "4000", "descList": ["因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用", "因条款中列明原因导致的旅行取消产生的已经支付且无法追回的境外租车费用"]}], "insuranceNotice": "1、本计划的被保险人投保年龄为18周岁至70周岁，且常住地在中华人民共和国境内、临时赴境外旅行、并符合保险人承保条件的自然人。\n\n2、每人限购一份，多购无效。\n\n3、意外身故和伤残：保险期间内被保险人因意外事故导致的身故，保险公司将一次性给付保险金；保险期间内被保险人因意外事故导致的残疾，保险公司将按照《人身保险伤残评定标准》所规定的伤残等级给付相应保险金。\n\n4、退保：取车时间前，可无损退保；取车时间后，无法退保。退保请联系携程客服95010\n\n5、保险单：保险在订单确认后出保，出保成功后将短信通知您，您可以在租车订单详情页的保险信息处查看电子保单。根据《中华人民共和国合同法》第十一条规定，数据电文是合法的合同表现形式，电子保单与纸质保单具有同等法律效力。您可通过平安保险官网（www.pingan.com）对电子保单进行验真。\n\n6、发票：保险由保险公司提供报销发票作为报销凭证，支持与投保人相同抬头的普通增值税电子发票。如需要开具发票可致电携程客服95010，保险发票仅作为报销凭证，不能替代保单凭证。\n\n7、请确认您已认真阅读\n\n《平安产险境外旅行意外伤害保险（互联网版）条款》注册号：C00001732312021121316203\n\n《平安旅行附加境外租车责任保险条款》注册号：H00001730922016120902731\n\n《平安预定旅行取消费用损失保险条款》注册号：C00001731912019122407512\n\n请您了解、同意并确认上述条款内容，特别是保险责任及责任免除部分。作为投保人，您确认对于被保险人具有保险利益。\n\n8、我们严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供给我们的个人信息、数据和隐私不受到非法的泄露或披露给未获取的第三方。\n\n12、保险产品销售服务方为：携程保险代理有限公司（http://www.ctripins.com/index.html）\n\n13、支付方式说明：保险赔款、保险费及退保金支付方式：1）信用卡；2）借记卡；3）网上银行；4）第三方支付；\n\n14、告知义务及违法义务可能产生的后果：订立保险合同，保险人就保险标的或者被保险人的有关情况提出询问的，投保人应当如实告知。投保人故意或者因重大过失未履行前款规定的如实告知义务，足以影响保险人决定是否同意承保或者提高保险费率的，保险人有权解除合同。具体可详见条款\n\n15、投保交易信息安全：携程严格遵守现行的关于个人信息、数据及隐私保护的法律法规，采取充分的技术手段和制度管理，保护您提供的个人信息、数据和隐私不受到非法的泄露或披露给未获授权的第三方\n\n16、产品销售范围：全国区域内销售(不含港、澳、台地区）\n\n本页信息不构成保险合同一部分，详细信息请参见保险条款，并以其规定为准。", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1907/32.pdf"}, {"code": "ACCIDENT_COVER", "name": "驾乘意外险", "description": "保障全车人员意外伤害及财物损失", "isInclude": true, "isFromCtrip": true, "insuranceDetail": [{"coverageWithoutPlatformInsurance": "国内保险公司提供¥75/天", "coverageWithoutPlatformInsuranceV2": "（由国内保险公司提供）", "coverageWithPlatformInsuranceV2": "¥75/天"}], "converageExplain": {"title": "承保范围", "content": ["驾乘意外险长描述"]}, "unConverageExplain": {"title": "不承保范围", "content": ["驾乘意外险不保障内容"]}, "productId": 16, "groupCode": "3", "coverDetailList": [{"title": "最高保额", "subTitle": "保障范围"}, {"title": "$1000", "descList": ["驾乘意外险保障范围描述1", "驾乘意外险保障范围描述2"]}, {"title": "$2000", "descList": ["驾乘意外险保障范围描述11", "驾乘意外险保障范围描述22", "驾乘意外险保障范围描述33"]}], "insuranceNotice": "https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111https://baike.baidu.com/item/预订须知111111", "insuranceClauses": "https://pages.c-ctrip.com/tour/pdf1712/23.pdf"}], "insuranceCompareItems": [{"description": "0起赔额", "type": "LDW", "descriptionColorCode": "GREEN"}, {"contains": true, "type": "WDW"}, {"contains": false, "type": "TPL"}, {"contains": true, "type": "PI"}, {"contains": true, "type": "RAP"}, {"contains": true, "type": "CANCEL"}, {"contains": true, "type": "ACCIDENT"}]}], "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}, "promptInfos": [{"title": "押金汇率说明", "contents": [{"stringObjs": [{"content": "押金所需的人民币金额根据当前汇率计算，实际所需人民币金额以用户到店支付时或者授权免押时的汇率为准。"}]}], "type": 12}, {"title": "素万那普国际机场 is a popular location on your selected dates. Rates are likely to rise.", "subTitle": "Book now! Don't miss out on low prices.", "type": 29}], "platformInsuranceExtra": {"encourageTitle": "Extra Protection for Your Rental", "encourageDesc": "Your package does not cover you for all losses and damages. Protect yourself from unexpected damage costs with RentalCover.com's protection", "claimProcedure": [{"type": 1, "content": ["不予理赔内容内容1111111111"]}, {"type": 2, "content": ["不予理赔内容内容222222222"]}]}, "requestInfo": {"pickupDate": "/Date(1712644200000+0800)/", "pickupLocationName": "廊曼国际机场", "returnDate": "/Date(1712817000000+0800)/", "returnLocationName": "廊曼国际机场", "sourceCountryId": 1, "pLatitude": 13.91326, "rLatitude": 13.91326, "rLongitude": 100.604199, "pLongitude": 100.604199, "pDate": "20240409143000", "rDate": "20240411143000", "pCityId": 359, "rCityId": 359}, "licenceCountryPolicy": {"title": "驾照", "type": 15, "subObject": [{"title": "中国大陆驾照", "summaryContent": ["您需携带香港驾照（或当地语言公证件和中国驾照原件、中国驾照原件和车行翻译件）才能成功取车", "所有驾驶员驾龄必须至少满6个月"], "type": 1, "code": "CN", "optimalType": "1"}, {"title": "中国香港驾照", "summaryContent": ["您需携带国际驾照和驾驶员本国驾照才能成功取车", "所有驾驶员驾龄必须至少满6个月"], "type": 1, "code": "HK", "optimalType": "1"}, {"title": "中国澳门驾照", "summaryContent": ["您需携带国际驾照和澳门驾照（或西班牙文公证件）才能成功取车", "所有驾驶员驾龄必须至少满6个月"], "type": 1, "code": "MO", "optimalType": "1"}, {"title": "中国台湾驾照", "summaryContent": ["您需携带国际驾照和台湾驾照（或国际驾照和台湾驾照）才能成功取车", "所有驾驶员驾龄必须至少满6个月"], "type": 1, "code": "TW", "optimalType": "1"}], "summaryObject": [{"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "该护照类型可能无法正常取车或发生变价风险，建议"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 16, "summaryContentObject": [{"contentStyle": "1", "stringObjs": [{"content": "因商业政策限制，若您的护照为非中国（含港澳台）护照，到场后可能无法正常取车或被要求支付额外金额，建议您使用与驾照发证国家/地区一致的护照进行取车或前往Trip下单"}]}]}, {"title": "护照政策", "contentObject": [{"contentStyle": "3", "stringObjs": [{"content": "持非中国（含港澳台）驾照无法预订。建议更换驾驶员或"}, {"content": " 前往Trip下单 >", "style": "2", "url": "1"}]}], "type": 17}]}, "crossPolicy": {"crossLocationsInfos": [{"crossType": 3, "locations": [{"name": "阿曼", "status": 2, "statusName": "条件跨境", "firstChar": "A", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "150", "isSelected": false}, {"name": "阿富汗", "status": 2, "statusName": "条件跨境", "firstChar": "A", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "7", "isSelected": false}, {"name": "阿联酋", "status": 2, "statusName": "条件跨境", "firstChar": "A", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "9", "isSelected": false}, {"name": "阿塞拜疆", "status": 2, "statusName": "条件跨境", "firstChar": "A", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "145", "isSelected": false}, {"name": "不丹", "status": 2, "statusName": "条件跨境", "firstChar": "B", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "212", "isSelected": false}, {"name": "巴林", "status": 2, "statusName": "条件跨境", "firstChar": "B", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "118", "isSelected": false}, {"name": "巴基斯坦", "status": 2, "statusName": "条件跨境", "firstChar": "B", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "17", "isSelected": false}, {"name": "巴勒斯坦", "status": 2, "statusName": "条件跨境", "firstChar": "B", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "205", "isSelected": false}, {"name": "朝鲜", "status": 2, "statusName": "条件跨境", "firstChar": "C", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "26", "isSelected": false}, {"name": "东帝汶", "status": 2, "statusName": "条件跨境", "firstChar": "D", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "215", "isSelected": false}, {"name": "菲律宾", "status": 2, "statusName": "条件跨境", "firstChar": "F", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "32", "isSelected": false}, {"name": "哈萨克斯坦", "status": 2, "statusName": "条件跨境", "firstChar": "H", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "176", "isSelected": false}, {"name": "韩国", "status": 2, "statusName": "条件跨境", "firstChar": "H", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "42", "isSelected": false}, {"name": "吉尔吉斯斯坦", "status": 2, "statusName": "条件跨境", "firstChar": "J", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "143", "isSelected": false}, {"name": "柬埔寨", "status": 2, "statusName": "条件跨境", "firstChar": "J", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "50", "isSelected": false}, {"name": "科威特", "status": 2, "statusName": "条件跨境", "firstChar": "K", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "53", "isSelected": false}, {"name": "卡塔尔", "status": 2, "statusName": "条件跨境", "firstChar": "K", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "163", "isSelected": false}, {"name": "黎巴嫩", "status": 2, "statusName": "条件跨境", "firstChar": "L", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "56", "isSelected": false}, {"name": "老挝", "status": 2, "statusName": "条件跨境", "firstChar": "L", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "55", "isSelected": false}, {"name": "孟加拉国", "status": 2, "statusName": "条件跨境", "firstChar": "M", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "68", "isSelected": false}, {"name": "蒙古", "status": 2, "statusName": "条件跨境", "firstChar": "M", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "67", "isSelected": false}, {"name": "马来西亚", "status": 1, "statusName": "允许跨境", "firstChar": "M", "regionId": "2", "isSelected": false}, {"name": "缅甸", "status": 2, "statusName": "条件跨境", "firstChar": "M", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "70", "isSelected": false}, {"name": "马尔代夫", "status": 2, "statusName": "条件跨境", "firstChar": "M", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "146", "isSelected": false}, {"name": "尼泊尔", "status": 2, "statusName": "条件跨境", "firstChar": "N", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "74", "isSelected": false}, {"name": "日本", "status": 2, "statusName": "条件跨境", "firstChar": "R", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "78", "isSelected": false}, {"name": "斯里兰卡", "status": 2, "statusName": "条件跨境", "firstChar": "S", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "83", "isSelected": false}, {"name": "沙特阿拉伯", "status": 2, "statusName": "条件跨境", "firstChar": "S", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "82", "isSelected": false}, {"name": "土库曼斯坦", "status": 2, "statusName": "条件跨境", "firstChar": "T", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "195", "isSelected": false}, {"name": "土耳其", "status": 2, "statusName": "条件跨境", "firstChar": "T", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "89", "isSelected": false}, {"name": "塔吉克斯坦", "status": 2, "statusName": "条件跨境", "firstChar": "T", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "250", "isSelected": false}, {"name": "文莱达鲁萨兰国", "status": 2, "statusName": "条件跨境", "firstChar": "W", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "260", "isSelected": false}, {"name": "乌兹别克斯坦", "status": 2, "statusName": "条件跨境", "firstChar": "W", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "144", "isSelected": false}, {"name": "新加坡", "status": 2, "statusName": "条件跨境", "firstChar": "X", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "3", "isSelected": false}, {"name": "叙利亚", "status": 2, "statusName": "条件跨境", "firstChar": "X", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "100", "isSelected": false}, {"name": "亚美尼亚", "status": 2, "statusName": "条件跨境", "firstChar": "Y", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "175", "isSelected": false}, {"name": "印度", "status": 1, "statusName": "允许跨境", "firstChar": "Y", "regionId": "107", "isSelected": false}, {"name": "印度尼西亚", "status": 2, "statusName": "条件跨境", "firstChar": "Y", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "108", "isSelected": false}, {"name": "以色列", "status": 2, "statusName": "条件跨境", "firstChar": "Y", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "105", "isSelected": false}, {"name": "伊拉克", "status": 2, "statusName": "条件跨境", "firstChar": "Y", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "103", "isSelected": false}, {"name": "伊朗", "status": 2, "statusName": "条件跨境", "firstChar": "Y", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "104", "isSelected": false}, {"name": "也门", "status": 2, "statusName": "条件跨境", "firstChar": "Y", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "102", "isSelected": false}, {"name": "约旦", "status": 2, "statusName": "条件跨境", "firstChar": "Y", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "110", "isSelected": false}, {"name": "越南", "status": 1, "statusName": "允许跨境", "firstChar": "Y", "regionId": "111", "isSelected": false}, {"name": "中国", "status": 2, "statusName": "条件跨境", "firstChar": "Z", "policy": "下单后，可人需要自行联系车行，得到门店跨境许可（授权）,车辆才允许跨境使用（亚洲）", "regionId": "1", "isSelected": false}], "crossTypeName": "跨境政策", "summaryPolicies": ["若您的行程中涉及跨境，请提前选择"], "title": "选择计划前往的国家", "subTitle": "门店支持在以下区域跨境使用车辆："}, {"crossType": 1, "crossTypeName": "跨岛政策", "summaryPolicies": ["条件跨岛"], "summaryTitle": "允许跨岛"}, {"crossType": 2, "crossTypeName": "跨州政策", "summaryPolicies": ["跨州政策允许"], "summaryTitle": "允许跨州"}], "notes": ["若需跨区域行驶，请在门店主动告知工作人员，并支付相应费用，否则可能造成无法跨境和罚款。除特别说明的情况外，“跨境费用”为前往单个国家所需的费用。"], "title": "旅行限制"}, "locationPrompt": "泰国地区道路状况复杂，为右舵靠左行驶，易发生车损，自付起赔额较高。建议升级含安心补充险的套餐，自付起赔额降为0，保障范围更广。", "osdCompareTitle": ["车辆碰撞及盗抢", "玻璃轮胎底盘保障", "第三者保障", "人身财物险", "道路救援补偿", "旅行取消", "意外津贴/住院津贴"]}