{"productDetails": [{"productInfoList": [{"zhiMaInfo": {"supportZhiMa": true}, "priceInfoList": [{"localCarPrice": 116, "cancelRule": {"isTotalLoss": false, "isFreeCancel": false, "isFreeCancelNow": true, "hours": 2, "cancelDescription": "1、距离提车时间2小时（含）以外免费取消；2、距离提车时间2小时内取消，收取订单首日日租金作为违约金；3、过提车时间（含预订提车时间）未取车，收取订单总金额作为违约金。"}, "isContainOnewayFee": true, "priceCode": "c0c05bbe634546689e4e476f8405ec5f", "currentPoaPrice": 0, "rentalGuarantee": [{"quantity": 2, "description": ["您无需承担车辆损失超过1500元的部分（含玻璃），轮胎及其他不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "<tag>tag0", "<tag>tag1"], "localCurrencyCode": "CNY", "currentTotalPrice": 60, "localDailyPrice": 30, "currentCurrencyCode": "CNY", "insuranceDetailDescription": {"items": [{"pageTitle": "", "pageUrl": "", "title": "服务详情", "contents": [], "foot": "", "head": ""}, {"pageTitle": "查看更多事故指导及费用明细", "pageUrl": "", "title": "车辆事故处理流程", "contents": [{"type": "3", "title": "立即联系门店及交通警察", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"type": "3", "title": "拍照并留存记录信息", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"type": "3", "title": "", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"type": "3", "title": "", "content": "请您配合交警处理完事故后，方可离开事故现场"}, {"type": "3", "title": "", "content": "准备材料，进行保险理赔流程"}], "foot": "", "head": ""}], "foot": "{\"basic\":\"基础服务由车行提供\",\"advanced\":\"优享服务由车行提供\",\"easyLife\":\"无忧尊享服务由车行提供\"}"}, "type": 1, "localTotalPrice": 60, "uniqueCode": "1002", "group": 0, "longDescription": ["基础服务费已包含在费用明细内。车辆发生意外时，租车公司承担车辆损失中超过1500元部分的费用，以及协助处理事故需要呼救中心、门店服务等服务费用（不含玻璃、轮胎、停运及折旧费，上述具体费用标准, 以租车公司确认为准）"], "allTags": [{"code": "tag0", "colorCode": "BLACK", "title": "您需要承担车辆停运费", "type": 1}, {"code": "tag1", "colorCode": "BLACK", "title": "您需要承担车辆折旧费", "type": 1}], "currentDailyPrice": 30, "name": "基础服务", "vendorServiceCode": ""}, {"quantity": 2, "description": ["您无需承担全部车辆损失（含玻璃），轮胎及其他不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "<tag>tag0", "<tag>tag1"], "localCurrencyCode": "CNY", "currentTotalPrice": 100, "localDailyPrice": 50, "currentCurrencyCode": "CNY", "insuranceDetailDescription": {"items": [{"pageTitle": "", "pageUrl": "", "title": "服务详情", "contents": [], "foot": "", "head": ""}, {"pageTitle": "查看更多事故指导及费用明细", "pageUrl": "", "title": "车辆事故处理流程", "contents": [{"type": "3", "title": "立即联系门店及交通警察", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"type": "3", "title": "拍照并留存记录信息", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"type": "3", "title": "", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"type": "3", "title": "", "content": "请您配合交警处理完事故后，方可离开事故现场"}, {"type": "3", "title": "", "content": "准备材料，进行保险理赔流程"}], "foot": "", "head": ""}], "foot": "{\"basic\":\"基础服务由车行提供\",\"advanced\":\"优享服务由车行提供\",\"easyLife\":\"无忧尊享服务由车行提供\"}"}, "type": 0, "localTotalPrice": 100, "uniqueCode": "2001", "group": 1, "longDescription": ["基础服务费的补充保护。车辆发生意外时，您无需承担1500元以内的车辆损失费用，包括协助处理事故需要呼叫中心服务、门店服务等服务费用。含玻璃损失费，不含轮胎损失费，不含车辆停运损失费，不含车辆折旧损失费（上述具体费用标准，以租车公司确认为准）"], "allTags": [{"code": "tag0", "colorCode": "BLACK", "title": "您需要承担车辆停运费", "type": 1}, {"code": "tag1", "colorCode": "BLACK", "title": "您需要承担车辆折旧费", "type": 1}], "currentDailyPrice": 50, "name": "优享服务", "vendorServiceCode": ""}, {"quantity": 2, "description": ["您无需承担全部车辆损失（含玻璃、轮胎），不在服务范围内的损失详见详情", "您无需承担行车致使第三方人身或财产损失100万以内的部分", "<tag>tag0", "<tag>tag1"], "localCurrencyCode": "CNY", "currentTotalPrice": 200, "localDailyPrice": 100, "currentCurrencyCode": "CNY", "insuranceDetailDescription": {"items": [{"pageTitle": "", "pageUrl": "", "title": "服务详情", "contents": [], "foot": "", "head": ""}, {"pageTitle": "查看更多事故指导及费用明细", "pageUrl": "", "title": "车辆事故处理流程", "contents": [{"type": "3", "title": "立即联系门店及交通警察", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"type": "3", "title": "拍照并留存记录信息", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"type": "3", "title": "", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"type": "3", "title": "", "content": "请您配合交警处理完事故后，方可离开事故现场"}, {"type": "3", "title": "", "content": "准备材料，进行保险理赔流程"}], "foot": "", "head": ""}], "foot": "{\"basic\":\"基础服务由车行提供\",\"advanced\":\"优享服务由车行提供\",\"easyLife\":\"无忧尊享服务由车行提供\"}"}, "type": 0, "localTotalPrice": 200, "uniqueCode": "2011", "group": 1, "longDescription": ["基础服务费的补充保护。车辆发生意外时，您无需承担1500元以内的车辆损失费用，包括协助处理事故需要呼叫中心服务、门店服务等服务费用。含玻璃损失费，含轮胎损失费，含车辆停运损失费，不含车辆折旧损失费（上述具体费用标准，以租车公司确认为准）"], "allTags": [{"code": "tag0", "colorCode": "BLACK", "title": "无需承担车辆停运费", "type": 0}, {"code": "tag1", "colorCode": "BLACK", "title": "车损3万元以下，您无需承担车辆折旧费", "type": 0}], "currentDailyPrice": 100, "name": "无忧尊享服务", "vendorServiceCode": ""}], "rentalGuaranteeV2": {"rentalGuaranteeTitle": ["车辆整备", "车损险赔付", "三者险保额", "免折旧费", "免停运费", "无需垫付"], "vendorServiceDesc": "车行服务是指车行为客人安全用车提供的服务，包含车辆整备（如车辆基础清洁，车辆保养等）和车辆保险（如交强险、其他商业保险等）。", "vendorServiceSubDesc": "上述车损险和三者险仅覆盖保险理赔范围内的损失，理赔范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求进行理赔导致保险公司拒赔的情况，您需承担全额损失。", "packageDetailList": [{"name": "基础服务", "quantity": 5, "localDailyPrice": 80, "localTotalPrice": 400, "localCurrencyCode": "CNY", "currentDailyPrice": 80, "currentTotalPrice": 400, "currentCurrencyCode": "CNY", "type": 1, "vendorServiceCode": "BasicInsurance", "allTags": [{"title": "不含车轮、玻璃", "type": 1, "code": "tag0", "colorCode": "BLACK"}], "description": [{"contains": true, "description": ""}, {"contains": true, "description": "1500元以上 <tag>tag0"}, {"contains": true, "description": "20万"}, {"contains": true, "description": "车损1500元以下时免收"}, {"contains": false, "description": ""}, {"contains": false, "description": ""}], "uniqueCode": "1002", "insuranceDetailDescription": [{"title": "车辆整备", "great": false, "contains": true, "content": ["车辆整备服务说明"]}, {"title": "车损险赔付", "contains": true, "content": ["发生车辆损失以及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔"], "subContent": [{"title": "车辆损失", "desc": "由于xxxxxxxx导致车辆损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"content": "全部损失", "great": true}}, {"key": "您需承担", "value": {"content": "0元"}}]}, {"title": "车辆报废", "desc": "由于xxxxxxxx导致车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"content": "保额以内保司定额的100%"}}, {"key": "您需承担", "value": {"content": "保额赔付以外的100%"}}]}]}, {"title": "三者险", "contains": true, "content": ["保障车辆发生意外事故、导致第三者承受的损失"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "20万"}}, {"key": "保险公司及车行承担", "value": {"content": "保额以内保司定额的100%"}}, {"key": "您需承担", "value": {"content": "保额赔付以外的100%"}}]}]}, {"title": "免折旧费", "contains": true, "content": ["免折旧费文案"], "great": true}, {"title": "免停运费", "contains": false, "content": ["免停运费文案"], "great": true}, {"title": "无需垫付", "contains": true, "content": ["无需垫付说明"], "great": true}, {"title": "不予理赔", "contains": false, "content": ["以下情况不予理赔，您需要承担所以损失：", "1、无事故证明无保险理赔材料", "2、其他保险公司不予理赔，或责任免除的场景", "3、其他xxxxx"]}]}, {"name": "优享服务", "quantity": 5, "localDailyPrice": 80, "localTotalPrice": 400, "localCurrencyCode": "CNY", "currentDailyPrice": 146, "currentTotalPrice": 730, "currentCurrencyCode": "CNY", "cityEncourage": "安全放心", "gapPrice": 20, "type": 0, "vendorServiceCode": "BasicInsurance", "allTags": [{"title": "不含车轮、玻璃", "type": 1, "code": "tag0", "colorCode": "BLACK"}], "description": [{"contains": true, "description": ""}, {"contains": true, "description": "全额赔付 <tag>tag0"}, {"contains": true, "description": "20万"}, {"contains": true, "description": "车损5000元以下时免收"}, {"contains": false, "description": ""}, {"contains": false, "description": ""}], "uniqueCode": "2001", "insuranceDetailDescription": [{"title": "车辆整备", "great": false, "contains": true, "content": ["车辆整备服务说明"]}, {"title": "车损险赔付", "contains": true, "content": ["发生车辆损失以及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔"], "subContent": [{"title": "车辆损失", "desc": "由于xxxxxxxx导致车辆损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"content": "全部损失", "great": true}}, {"key": "您需承担", "value": {"content": "0元"}}]}, {"title": "车辆报废", "desc": "由于xxxxxxxx导致车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"content": "保额以内保司定额的100%"}}, {"key": "您需承担", "value": {"content": "保额赔付以外的100%"}}]}]}, {"title": "三者险", "contains": true, "content": ["保障车辆发生意外事故、导致第三者承受的损失"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "20万"}}, {"key": "保险公司及车行承担", "value": {"content": "保额以内保司定额的100%"}}, {"key": "您需承担", "value": {"content": "保额赔付以外的100%"}}]}]}, {"title": "免折旧费", "contains": true, "content": ["免折旧费文案"], "great": true}, {"title": "免停运费", "contains": false, "content": ["免停运费文案"], "great": true}, {"title": "无需垫付", "contains": true, "content": ["无需垫付说明"], "great": true}, {"title": "不予理赔", "contains": false, "content": ["以下情况不予理赔，您需要承担所以损失：", "1、无事故证明无保险理赔材料", "2、其他保险公司不予理赔，或责任免除的场景", "3、其他xxxxx"]}]}, {"name": "尊享服务", "quantity": 5, "localDailyPrice": 80, "localTotalPrice": 400, "localCurrencyCode": "CNY", "currentDailyPrice": 166, "currentTotalPrice": 830, "currentCurrencyCode": "CNY", "cityEncourage": "城市车多易剐蹭建议升级服务", "gapPrice": 40, "type": 0, "vendorServiceCode": "BasicInsurance", "allTags": [{"title": "含玻璃、不含轮胎", "type": 1, "code": "tag0", "colorCode": "ORANGE"}], "description": [{"contains": true, "description": ""}, {"contains": true, "description": "全额赔付 <tag>tag0", "descriptionColorCode": "GREEN"}, {"contains": true, "description": "20万"}, {"contains": true, "description": "车损5万元以下时免收"}, {"contains": true, "description": ""}, {"contains": true, "description": ""}], "uniqueCode": "2011", "insuranceDetailDescription": [{"title": "车辆整备", "great": false, "contains": true, "content": ["车辆整备服务说明"]}, {"title": "车损险赔付", "contains": true, "content": ["发生车辆损失以及车辆报废时可获得赔付。若发生多次车损，每次车损单独理赔"], "subContent": [{"title": "车辆损失", "desc": "由于xxxxxxxx导致车辆损失", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"content": "全部损失", "great": true}}, {"key": "您需承担", "value": {"content": "0元"}}]}, {"title": "车辆报废", "desc": "由于xxxxxxxx导致车辆报废", "table": [{"key": "保额", "value": {"content": "车辆实际价值"}}, {"key": "保险公司及车行承担", "value": {"content": "保额以内保司定额的100%"}}, {"key": "您需承担", "value": {"content": "保额赔付以外的100%"}}]}]}, {"title": "三者险", "contains": true, "content": ["保障车辆发生意外事故、导致第三者承受的损失"], "subContent": [{"title": "", "desc": "", "table": [{"key": "保额", "value": {"content": "20万"}}, {"key": "保险公司及车行承担", "value": {"content": "保额以内保司定额的100%"}}, {"key": "您需承担", "value": {"content": "保额赔付以外的100%"}}]}]}, {"title": "免折旧费", "contains": true, "content": ["免折旧费文案"], "great": true}, {"title": "免停运费", "contains": false, "content": ["免停运费文案"], "great": true}, {"title": "无需垫付", "contains": true, "content": ["无需垫付说明"], "great": true}, {"title": "不予理赔", "contains": false, "content": ["以下情况不予理赔，您需要承担所以损失：", "1、无事故证明无保险理赔材料", "2、其他保险公司不予理赔，或责任免除的场景", "3、其他xxxxx"]}]}], "purchasingNotice": {"title": "购买须知", "content": ["1.车行服务由租车公司提供，仅支持取车前购买", "2.若发生续租，已购买的车行服务的订单续租时必须购买相同服务"]}, "vendorServiceDetail": [{"title": "车辆事故处理流程", "type": 1, "description": "发生车损(本车与第三方，或本车单独发生事故)时请按下方流程处理，否则无法获得理赔。", "contents": [{"title": "立即联系门店及交通警察", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"title": "拍照并留存记录信息", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"title": "", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"title": "", "content": "请您配合交警处理完事故后，方可离开事故现场"}]}, {"title": "车行服务理赔说明", "type": 2, "description": "发生车损(本车与第三方，或本车单独发生事故)时请按下方流程处理，否则无法获得理赔。", "contents": [{"title": "保险理赔说明", "content": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店以获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"title": "赔付方式", "content": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"title": "不予理赔", "content": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}]}]}, "currentDailyPrice": 58, "currentTotalPrice": 216, "localTotalPrice": 216, "currentOnewayfee": 0, "payMode": 2, "ageRestriction": {"youngDriverAge": 0, "licenceAge": 1, "oldDriverAge": 0}, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "localPrepaidPrice": 216, "currentPrepaidPrice": 216, "allTags": [{"description": "无需支付租车押金和违章押金，取还车手续更便捷。", "category": 2, "colorCode": "2", "mergeId": 0, "subList": [{"description": "在下一步我们会基于您的信用情况进行综合评估，若评估通过，您可在下一步选择信用租服务。若不通过，您可选择其他方式支付押金。"}], "type": 1, "title": "押金双免", "code": "7", "sortNum": 5, "groupCode": "MarketGroup1347", "labelCode": "3746", "tagSortNum": 4, "groupId": 3, "tagGroups": 2}, {"description": "该车型于1年内上市。", "category": 2, "colorCode": "8", "mergeId": 2, "type": 1, "title": "三年内车龄", "code": "6", "sortNum": 9, "groupCode": "MarketGroup1201", "labelCode": "3548", "tagSortNum": 9, "groupId": 2, "tagGroups": 1}, {"description": "该车型于1年内上市。", "category": 2, "colorCode": "8", "mergeId": 2, "type": 1, "title": "新款", "code": "6", "sortNum": 8, "groupCode": "MarketGroup1201", "labelCode": "3789", "tagSortNum": 8, "groupId": 2, "tagGroups": 1}, {"category": 1, "sortNum": 15, "mergeId": 0, "groupCode": "MarketGroup1336", "code": "1", "title": "限时免费取消", "colorCode": "6", "type": 2, "description": "取车前2小时可免费取消；取车前2小时-取车时间取消扣首日价格；取车时间后取消扣订单全额。", "groupId": 3, "labelCode": "3679"}, {"description": "车内配备有手机支架。", "category": 2, "colorCode": "1", "mergeId": 0, "type": 1, "title": "手机支架", "code": "6", "sortNum": 72, "groupCode": "MarketGroup1201", "labelCode": "3495", "tagSortNum": 72, "groupId": 2, "tagGroups": 1}, {"category": 2, "sortNum": 78, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "不限里程", "colorCode": "2", "type": 1, "description": "租期内没有里程数限制", "groupId": 3, "labelCode": "3731"}, {"description": "实际产生的费用由客人承担。", "category": 2, "colorCode": "1", "mergeId": 0, "type": 1, "title": "ETC", "code": "6", "sortNum": 86, "groupCode": "MarketGroup1201", "labelCode": "3650", "tagSortNum": 86, "groupId": 2, "tagGroups": 1}, {"description": "车辆配有加湿器。", "category": 2, "colorCode": "1", "mergeId": 0, "type": 1, "title": "加湿器", "code": "6", "sortNum": 96, "groupCode": "MarketGroup1201", "labelCode": "3698", "tagSortNum": 96, "groupId": 2, "tagGroups": 1}, {"description": "车辆配有真皮座椅。", "category": 2, "colorCode": "1", "mergeId": 0, "type": 1, "title": "真皮座椅", "code": "6", "sortNum": 98, "groupCode": "MarketGroup1201", "labelCode": "3696", "tagSortNum": 98, "groupId": 2, "tagGroups": 1}, {"description": "车辆配有制氧机。", "category": 2, "colorCode": "1", "mergeId": 0, "type": 1, "title": "制氧机", "code": "6", "sortNum": 100, "groupCode": "MarketGroup1201", "labelCode": "3697", "tagSortNum": 100, "groupId": 2, "tagGroups": 1}, {"description": "车辆提供儿童座椅服务，可在产品详情页面附加产品中选择。", "category": 2, "colorCode": "1", "mergeId": 0, "type": 1, "title": "儿童座椅", "code": "6", "sortNum": 104, "groupCode": "MarketGroup1201", "labelCode": "3488", "tagSortNum": 104, "groupId": 2, "tagGroups": 1}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup7", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3422"}, {"category": 3, "sortNum": 10000, "amountTitle": "共减80", "groupCode": "MarketGroup1317", "code": "30", "title": "今日特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, {"colorCode": "2", "sortNum": 999, "title": "2022-03-02 17:30前可免费取消", "tagGroups": 2, "type": 2, "tagSortNum": 3}], "vcExtendRequest": {"vendorVehicleId": "169372"}, "currentCarPrice": 116, "localDailyPrice": 58, "localPoaPrice": 0, "localOnewayfee": 0}], "equipments": [], "carRentalMustRead": [{"title": "取消政策", "type": 1, "code": "1"}, {"code": "1", "title": "里程限制", "contentObject": [{"stringObjs": [{"content": "租期内没有里程数限制"}]}], "type": 3}, {"code": "1", "title": "油费及加油服务费", "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "还车时需保持与取车时油量一致"}]}, {"stringObjs": [{"content": "若还车油量多于/少于取车油量，涉及相关费用的结算标准，请以门店告知为准。"}]}], "type": 4}, {"code": "1", "title": "禁行区域", "contentObject": [{"stringObjs": [{"content": "车辆不允许进入新疆、西藏、昌都左贡县，拉萨当雄县，日喀则尼木县，林芝工布江达县、云南省泸沽湖、甘孜州、阿坝州、昌都左贡县"}]}, {"contentStyle": "11", "stringObjs": [{"content": "*若违反上述禁行规则，门店可拒绝提供或强制收回车辆；强制收回车辆时，产生的相关费用由用户承担，剩余租期费用退还规则以门店为准。"}]}], "type": 26}, {"code": "2", "subObject": [{"code": "1", "title": "油费及加油服务费", "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "还车时需保持与取车时油量一致"}]}, {"stringObjs": [{"content": "若还车油量多于/少于取车油量，涉及相关费用的结算标准，请以门店告知为准。"}]}], "type": 4}, {"code": "2", "title": "零散小时费", "contentObject": [{"stringObjs": [{"content": "零散小时费根据当日租金计算，由供应商提供，携程仅作展示"}]}], "type": 49}, {"code": "2", "title": "提前还车", "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "1.请提前联系门店"}]}, {"stringObjs": [{"content": "如需提前还车，请您提前24小时联系门店；如遇节假日，请提前24小时联系门店。"}]}, {"stringObjs": [{"content": "请在门店营业时间范围内办理还车手续，如未提前联系，可能导致无法及时还车。"}]}, {"stringObjs": [{"content": "如您在门店夜间服务时段办理还车手续，可能需要额外支付夜间服务费。具体夜间服务时段请在下单后咨询门店。"}]}, {"contentStyle": "13", "stringObjs": [{"content": "2.退费标准"}]}, {"stringObjs": [{"content": "1）普通订单"}]}, {"stringObjs": [{"content": "提前还车具体退费标准请咨询门店。"}]}, {"stringObjs": [{"content": "2）节假日订单"}]}, {"stringObjs": [{"content": "提前还车不退费。"}]}], "type": 10}, {"code": "2", "title": "续租/延迟还车", "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "1.请提前联系门店"}]}, {"stringObjs": [{"content": "如需续租或延迟还车，请您提前3小时联系门店。"}]}, {"stringObjs": [{"content": "请在门店营业时间范围内办理还车手续。如未提前联系，可能导致无法及时还车。"}]}, {"stringObjs": [{"content": "00:00至次日早上23:59间办理还车手续可能需要额外支付夜间服务费。"}]}, {"contentStyle": "13", "stringObjs": [{"content": "2.收费标准"}]}, {"stringObjs": [{"content": "续租或延迟还车申请成功后，租金根据总租期计算。"}]}, {"contentStyle": "10", "stringObjs": [{"content": "总租期=已租时间+续租时间"}]}, {"contentStyle": "10", "stringObjs": [{"content": "续租或延迟还车费用=总租期金额-原单价格"}]}, {"stringObjs": [{"content": "*总租期内非整日的部分，根据当日租金计算，由供应商提供，携程仅作展示。"}]}, {"contentStyle": "13", "stringObjs": [{"content": "3.延续附加服务"}]}, {"stringObjs": [{"content": "若您在原订单中已购买附加服务，续租或延迟还车时，如需延续该服务，按照以下规则收费："}]}, {"stringObjs": [{"content": "续租后，附加服务需要另行加购，加购费=总租期附加服务费用-已支付费用"}]}, {"stringObjs": [{"content": " "}]}], "type": 12}, {"code": "2", "title": "强行续租", "contentObject": [{"stringObjs": [{"content": "若您未提前3小时联系门店并办理续租手续，或未经门店同意而强行延迟还车的，将被视为强行续租。强行续租除必须支付正常续租价格外，还将产生强行续租违约金。同时，门店有权收回车辆，所需费用均由用户本人承担。"}]}, {"stringObjs": [{"content": "强行续租违约金收费标准"}]}, {"contentStyle": "10", "stringObjs": [{"content": "违约金=续租首日租金×300%"}]}, {"stringObjs": [{"content": "注：1）日租金不含基本服务费、车行手续费、附加服务费。"}]}, {"stringObjs": [{"content": "      2）原订单参与优惠和促销活动时，按参与优惠前订单价格计算。具体费用以门店反馈为准。"}]}], "type": 48}], "title": "常规费用收费标准", "sortNum": 1, "type": -1}, {"code": "2", "subObject": [{"title": "人身及财物险", "content": ["人身及财物险收费标准为26元/天"]}], "title": "附加服务收费标准", "content": [], "sortNum": 2}, {"code": "2", "subObject": [{"title": "违章处理流程及违约金", "content": ["须知<br/>", "</br>承租方须对在车辆租赁期间产生的交通违章承担责任（无论该违章是否由承租方本人造成），并应主动予以处理。违章信息以车行及门店在各地交管部门（包括但不限于各地交管部门及相关网站等）查实的记录为准。<br/>", "</br>1.如何得知租期内是否产生违章？<br/>", "</br>车行将在承租方还车后15个工作日左右查询或收到承租方未处理的违章信息（具体时间以各地方交管部门通知为准），并以电话、短信等方式通知承租方。<br/>", "</br>2.还车后如无违章，违章押金将如何退还？<br/>", "</br>还车后如经查实，车辆在租赁期间内无违章，<br/>2.1以“预授权”形式支付的违章押金预计由承租方的发卡银行在30-45天自行解冻（恢复额度）。如有问题，可咨询发卡银行核实（已知交通银行、农业银行为45天左右解冻周期）；<br/>2.2以“消费”、“预授权完成”形式支付的违章押金预计在30天后由原卡退还，并预计于3-5个工作日后到账；<br/>2.3免违章押金订单预计在30天后解除已授权的扣款通道。<br/>", "</br>3.承租人自行处理违章时限是多久？<br/>", "</br>承租方需在被通知之日起，7个工作日内予以处理。承租方收到违章处理通知后，须在该期限内，自行前往交管部门处理违章事宜。<br/>", "</br>4.如何处理违章？<br/>", "</br>根据交管部门要求，需携带本人驾驶证原件、身份证原件及车辆行驶证原件等，到违章当地或车牌所在地的交管部门办理；具体所需资料和违章处理相关规定各地存在差异（如部分地区要求提供租车合同，委托书等其他文件），请事先咨询当地交管部门。<br/>", "</br>5.如需借用行驶证等资料怎么办？<br/>", "</br>若承租方在自行处理违章时，需借用行驶证原件，则须提前7天联系车行或门店预约，联系方式，以车行指定的联系方式为主，如果不清楚联系方式请拨打小豆租车平台客服电话：400-6767-388。违章事宜处理完毕后，须及时将处理凭证原件归还车行。在确认不耽误车辆行程的情况下，可免费借用4小时（门店营业时间范围内），且须在门店现场刷取500元押金预授权。在按时并完整归还证件时，门店将全额解除该预授权。若实际使用车辆行驶证超过4小时，则按照实际发生天数收取车辆租金（包括基础日租金+基本服务费）。不足一天的按照一天计算。<br/>", "</br>6.违章处理完毕后，还需要做什么？<br/>", "</br>建议承租方在违章处理完毕后，将处罚决定书和缴费凭证给到车行。车行收到凭证后，一般会在7个工作日内审核完成，并同步更新违章状态。<br/>", "</br>7.如果无法自行按时处理怎么办？<br/>", "</br>如承租方未在收到违章通知之日起7个工作日内处理完毕，或者未提供处理凭证原件的，或者无法办理违章需要车行或门店协助处理的，车行有权在承租方违章押金内扣除相应金额的违约金。如押金扣除后仍不足以抵偿全部损失的，车行有权根据实际损失情况向承租方追偿。（免违章押金订单，供应商将通过已授权的扣款通道，按规则收取违章违约金）。<br/>", "</br>逾期未处理违章违约金收费标准：<br/>", "</br>A.无扣分：违章违约金=违章罚款金额+100元/天逾期未处理违章违约金+200元/条违章违约金；<br/>B.单条有扣分但扣分12分（不含）以内的：违章违约金=违章罚款金额+100元/天逾期未处理违章违约金+200元/条违章违约金+200元/分违章违约金；<br/>C.单条扣12分：违章违约金=违章罚款金额+200/天逾期未处理违章违约金+300元/条违章违约金+4800元/12分违章违约金；<br/>D.累计扣12分（不含）以上：违章违约金=违章罚款金额+200元/天逾期未处理违章违约金+300元/条违章违约金+400元/分违章违约金（直到双方协商处理终结）。<br/>在已冻结的承租方的违章押金中扣除以上费用后将退还剩余部分（如有）。<br/>注：违章罚款金额以违章决定书或交通网站查询到的违章罚款金额为准。其他损失按合同及相关法律法规协商处理。<br/>", "</br>8.注意事项<br/>", "</br>注：违章罚款金额以违章决定书或交通网站查询到的违章罚款金额为准。其他损失按合同及相关法律法规协商处理。<br/>承租人如需开具相关发票，建议自行前往交管部门处理违章事宜。车行及门店仅可向承租方提供收取违章违约金的收据和违章处理凭证。<br/>"], "type": 13}], "title": "违章押金所涉及的扣费项", "content": [], "sortNum": 3}, {"code": "2", "subObject": [{"title": "租车押金", "content": ["在您取车时，门店将以冻结信用卡预授权的方式支付租车押金。要求单信用卡的额度需高于租车押金金额<br /> <br /> \\n\\n押金金额：不同车型、不同租期押金金额不同，以页面披露金额为准<br /> <br /> \\n\\n押金退还方式：<br /> \\na. 有车损，门店和客人共同确认后，先行扣除一定额度，若押金有剩余则通过解冻预授权的方式退还<br /> \\nb. 无车损，还车时，门店解冻全部预授权<br /> \\n押金退还时间：还车时，门店操作解冻信用卡预授权；解冻金额到账时间以各银行为准，一般为即时解冻<br /> \\n"]}, {"title": "违章押金", "content": ["在客人还车时，门店将以冻结信用卡预授权的方式收取违章押金。要求单信用卡的额度需高于违章押金；<br/><br/>免违章押金模式下，门店不向客人收取违章押金，但客人仍需承担违章责任。<br/><br/>押金金额：根据租期、城市等因素有所不同，以页面披露金额为准。<br/><br/>押金退还方式：信用卡预授权解冻；<br/><br/>押金退还时间：<br/>    有违章，门店在告知客人之后，将依据违章情况扣除一定额度。若押金有剩余，则通过解冻预授权的方式退还；<br/>    无违章，违章押金由银行自动解冻，解冻时间以各银行操作时间为准，一般为30天左右（部分区域，如新疆，西藏，西宁，兰州，成都等，预授权解冻时间有45天的情况，以各银行实际解冻时间为准。因为进藏区或不可抗力等因素导致违章信息查询存在滞后性，如违章押金自动解冻后，门店发现客人租期内存在违章，客人仍需承担相应违章责任。）<br/>", "<pic>picture0"]}], "title": "租车押金涉及的扣费项", "content": [], "sortNum": 4}, {"code": "2", "subObject": [{"code": "2", "title": "夜间服务费", "contentObject": [{"stringObjs": [{"content": "夜间取/还车无需缴纳夜间服务费。"}]}], "type": 50}, {"title": "车辆停运损失费", "content": ["因车辆事故、车辆损伤或因遗失证件等原因造成车辆无法正常运营的，将收取停运损失费。<br /> <br /> \\n\\n计费规则：租期内租金均价（不含基本服务费）×停运天数<br /> <br /> \\n\\n停运天数按车辆维修凭证、证件补办凭证或相关部门扣押时间等确认。<br /> "], "type": 43}, {"title": "车辆折旧费", "content": ["租赁期间车辆严重损坏的，将收取车辆加速折旧费。<br /> <br /> \\n\\n收费标准为：维修费在0-3000元（不含）之间，不收加速折旧费。<br /> <br /> \\n\\n维修费在3000元（含）以上，收取维修费的30%作为折旧费。<br /> \\n\\n维修费用以定损金额或车辆维修凭证为准。<br /> \\n"], "type": 14}, {"title": "车辆维修定损", "content": ["车辆维修定损金额以当地市场价格和修理厂实际报价而定。<br /> <br /> \\n\\n如对定损金额有异议，可在得到租车门店工作人员确认后，自行到指定4S店进行维修。<br /> \\n\\n车辆维修完毕后交还租车公司并持4S店维修发票进行租车费用及其他相关费用的结算。<br /> \\n"]}, {"title": "道路救援收费标准", "content": ["1）由于车辆本身故障引起的救援费用信租出行门店承担。<br /> <br /> \\n\\n2）非车辆本身机械故障导致无法正常行驶时（包含且不限于人为操作失误、保险事故等），费用由客户承担，收费标准以第三方救援机构所示为准或客户与门店协商而定。<br /> "]}, {"title": "车辆清洁费及停车费", "content": ["清洁费：车辆租用过程中禁止在车内吸烟、吃榴莲或者装海鲜等导致车辆味道较重的行为，否则会被收取较高的清洁费。未经租车公司同意，禁止携带宠物乘车。（注：请租车人在还车时保持车辆清洁，如果在还车时车辆太脏导致无法辨认车辆是否有车损时，待车辆清洗后租车公司确认无任何问题，租车公司立刻退还租车押金，但还车到退押金超过1天时间，如果发现有车损由租车人全权负责承担并及时处理，否则租车公司有权从车辆租车押金里扣除车损，若有剩余立刻退款。）<br />\\n停车费：如预订的是送车服务，送车时如因个人原因导致车辆停留的时间超出停车场免费收费的范围，需自行承担停车费。在租车人用车途中产生的所有停车费应由租车人自行承担。<br />"]}], "title": "其他收费标准", "sortNum": 5, "type": -2}, {"code": "2", "subObject": [{"title": "城市限行处理办法", "content": ["限行期间优先提供不限行车辆，若暂时无车，门店可优先更换同等级车型，若无同等级车型，可协商免费升级车辆。若不接受上述方案，则减免客户在限行当日的租金（含保险费用及附加服务费）。<br />\\n此限行处理办法仅针对取车点所属地区，如您要前往周边城市，请关注当地的限行政策，避免违章。<br />"]}], "title": "城市限行处理办法", "sortNum": 7}, {"code": "2", "subObject": [{"title": "其他特殊要求", "content": ["1.如果您预订的是房车车型，建议您取车时在门店观看视频教程，避免出现因房车高度限制等问题对您的行程有影响。<br />\\n2.租车公司要求承租方驾龄在6个月以上，若您的驾照驾龄时间少于6个月，请选择其他租车公司。<br />\\n3.提前取车： 若您在门店许可的情况下提前取车，请提前同样的时间还车，否则门店会按照正常的收费标准收取您额外的租赁费用。<br />\\n4.延迟取车：若您延迟到门店取车，请在预订取车时间2小时前提前联系门店协调，否则可能会导致您无法取车，并且您延迟取车期间未使用时间的相关费用不予退还，建议您务必在预订时间内准时到达。<br />\\n"]}], "title": "其他特殊要求", "content": [], "sortNum": 8}], "ctripInsurances": [{"quantity": 1, "description": ["人身意外保额20万/人，医疗费用保额3万元/人。附赠新冠确诊津贴", "随车财物损失2000元", "提供专业道路救援服务"], "localCurrencyCode": "CNY", "currentTotalPrice": 156, "localDailyPrice": 78, "currentCurrencyCode": "CNY", "type": 0, "localTotalPrice": 156, "uniqueCode": "2000896", "longDescription": ["(携程提供)保障全车人员在驾乘过程中发生的意外及租赁车辆内行李、物品损失，保障无免赔。"], "allTags": [{"title": "仅支持线上购买"}], "currentDailyPrice": 78, "name": "人身及财物险", "vendorServiceCode": "2000896"}], "rentalMustReadPicture": [{"title": "picture0", "desc": "https://dimg04.c-ctrip.com/images/0AS3y1200042qpz87AC27.png"}], "rentalMustReadTable": [], "pickUpMaterials": [{"subObject": [], "title": "驾驶员本人身份证原件", "summaryContent": ["有效期1个月以上"], "type": 0}, {"title": "驾驶员本人驾照原件", "summaryContent": ["有效期1个月以上、驾龄需1个月以上"], "type": 1}], "searchCreditCard": false}], "insPackageId": 0}], "depositInfo": {"illegalDepositFee": 2000, "carRentalDeposit": {"title": {"contentStyle": "1", "stringObjs": [{"content": "租车押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥", "style": "1"}, {"content": "5000.0", "style": "1"}, {"content": "", "style": "2"}]}, {"contentStyle": "2", "stringObjs": [{"content": "选择“", "style": "1"}, {"content": "信用租", "style": "3"}, {"content": "”并授权成功后免收", "style": "1"}]}]}, "illegalDeposit": {"title": {"contentStyle": "1", "stringObjs": [{"content": "违章押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥", "style": "1"}, {"content": "2000.0", "style": "1"}, {"content": "", "style": "2"}]}, {"contentStyle": "2", "stringObjs": [{"content": "选择“", "style": "1"}, {"content": "信用租", "style": "3"}, {"content": "”并授权成功后免收", "style": "1"}]}]}, "depositDescV2": {"title": {"contentStyle": "1", "stringObjs": [{"content": "押金双免", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "免¥5000租车押金", "style": "1"}]}, {"contentStyle": "1", "stringObjs": [{"content": "免¥2000违章押金", "style": "1"}]}], "note": {"contentStyle": "1", "stringObjs": [{"content": "下单时选择信用租并授权成功后免收", "style": "1"}]}}, "depositType": 10, "depositCase": {"title": "如何使用信用租", "type": 10, "subObject": [{"title": "进入下一步订单填写页，选择驾驶员", "url": "https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/CreditRentModalStep1.png"}, {"title": "选择驾驶员后，携程将根据订单金额、供应商支持的验证方式、与您在携程的综合信用（程信分）或芝麻信用进行综合评估，评估通过后即可尊享信用租·押金双免", "url": "https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/CreditRentModalStep22.png"}]}, "depositPrompt": {"title": "该车型支持免押，选择信用租可享免押", "items": [{"title": {"contentStyle": "1", "stringObjs": [{"content": "免租车押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥5000.0", "style": "1"}]}]}, {"title": {"contentStyle": "1", "stringObjs": [{"content": "免违章押金", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "¥2000.0", "style": "1"}]}]}], "buttonExt": [{"title": "去免押", "type": 1, "tips": "98%用户选择"}, {"title": "无需免押，继续预订", "type": 2}]}, "carRentalDepositFee": 5000, "depositDesc": {"title": {"contentStyle": "1", "stringObjs": [{"content": "该车型支持免押服务", "style": "1"}]}, "desc": [{"contentStyle": "1", "stringObjs": [{"content": "信用租·押金双免", "style": "1"}, {"content": "授权成功后，无需支付租车押金和违章押金", "style": "2"}]}], "note": {"contentStyle": "1", "stringObjs": [{"content": "在下一步我们会基于您的信用情况进行综合评估，若评估通过，您可在下一步选择信用租服务。若不通过，您可选择其他方式支付押金。", "style": "1"}]}}, "depositTypeName": "信用租·押金双免"}, "vendorInsuranceDesc": {"insurancelist": [{"title": "车上人员责任险", "type": 1, "desclist": [{"title": "保额", "desclist": ["1万元/座"]}, {"title": "保障范围", "desclist": ["保险车辆发生保险事故，导致车上人员伤亡"]}, {"title": "客户承担", "desclist": ["0%"]}, {"title": "保险公司及车行承担", "desclist": ["100%"]}]}, {"title": "第三者责任险", "type": 1, "desclist": [{"title": "保额", "desclist": ["50万"]}, {"title": "保障范围", "desclist": ["保险车辆发生意外事故，导致第三者承受的损失"]}, {"title": "客户承担", "desclist": ["保险赔付以外的100%"]}, {"title": "保险公司及车行承担", "desclist": ["100%"]}]}, {"title": "车辆损失险", "type": 1, "desclist": [{"title": "保额", "desclist": ["新车购置价"]}, {"title": "保障范围", "desclist": ["由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的损失，由于发生自然灾害（地震除外）、意外事故、其他保险事故导致的车辆本身的车辆报废"]}, {"title": "客户承担", "desclist": [""]}, {"title": "保险公司及车行承担", "desclist": [""]}]}, {"title": "不在保障范围内", "type": 0, "desclist": [{"title": "", "desclist": ["1. 无事故证明无保险理赔材料", "2. 以下情况产生的损失由客户自行承担：玻璃单独破碎、轮胎损坏、底盘、车上零件遗失", "3. 因车损产生的停运费、折旧费", "4. 其他基本保障服务中保险公司无需承担，或责任免除的场景"]}]}, {"title": "以上保险理赔说明", "type": 0, "desclist": [{"title": "", "desclist": ["(1)如车辆出险，客户需立即致电加盟商电话或信租租车客服热线报案、收集理赔材料", "a.客户需要垫付本次事故相关应付费用，保险公司完成理赔后将理赔款返还给客户", "b.支付《基本险表覆盖范围》列明的保险责任内由承租方承担的损失，以及保险责任外的所有责任和损失", "c.如客户还车不能提供齐全的理赔资料，需暂时冻结客户押金,并在保险公司完成理赔后的3个工作日内退还", "d.车辆出险后需要维修的，按照维修所需时间收取车辆租金作为车辆停运损失费", "e.车辆发生重大损伤(保险定损金额不低于3000元)的，客户需承担加速折旧费(定损金额30%)", "(2)客户用车期间，如有车辆盗抢发生，客户需一直支付公安部门立案之前的车辆租赁费用", "(3)在您还车不能提供齐全的理赔资料时，请根据预估的车辆损失刷取最低1500元的维修押金，该押金将在您提交完材料后的3天内发起退款(到账时间以各银行为准)"]}]}, {"title": "若您购买了租车公司提供的优享服务，且在保障范围内，租车公司还提供以下服务：", "type": 0, "desclist": [{"title": "", "desclist": ["1. 无需承担基本保障服务理赔范围内的任何损失", "2. 无需承担1500元以内的车辆损失费用", "3. 无需承担单独反光镜损坏、天窗玻璃损坏产生的1500元以内的损失", "4. 无需承担因非人为故意造成的车辆划痕产生的1500元以内的损失"]}]}, {"title": "赔付方式", "type": 0, "desclist": [{"title": "", "desclist": ["不同租车公司方式不同，目前涵盖以下三种：", "a.无需付定金，车辆维修出险后，根据费用单等，补缴给租车公司您所需赔付的部分；", "b.需您先行交付部分定金（金额与租车公司现场沟通），待车辆维修出险后，根据费用单等，从定金扣除您所需赔付的部分，多退少补；", "c.需您垫付相关费用，车辆维修出险后，根据费用单等，租车公司报销相关款项；"]}]}], "exclusionDesc": {"title": "以下情况无法为您提供保障服务：", "desclist": ["发生事故时未及时通知租车公司或未申报保险", "无事故证明材料或无保险理赔材料，", "无证驾驶、酒驾、超速等其他保险公司不予理赔或责任免除的场景"]}, "insurancedesc": "如车辆不慎发生事故，请您注意人身安全，并致电租车公司及交通警察122（上海地区为110）进行事故沟通，租车公司会协助沟通保险公司及交通警察，具体情况以各租车公司现场情况为准； 提示：请不要随意移动车辆，及时进行拍照留存并记录信息，包括但不限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等； 租车公司已给所有提供车辆购买交强保险及商业保险，保障您的用车安全。保险事故责任承担租期内发生事故的，在符合保险理赔条件下，可由门店协助沟通报险，除了交强险外，车辆所购保险还覆盖以下内容：（具体赔偿范围及赔付金额以租车公司确认为准）", "vendorInsuranceTips": "上述车辆及第三方保障仅覆盖车行服务保障范围内的损失，服务保障范围见服务详情。若发生事故，请按照要求进行处理；如未按照要求导致保障服务公司拒绝处理或无法提供保障的情况，您需承担全额损失。"}, "vendorInfo": {"platformName": "", "vendorCode": "13039", "bizVendorCode": "13039", "platformCode": "", "vendorName": "小豆租车", "haveCoupon": false, "isBroker": false, "prePayType": 0, "vendorImageUrl": "https://dimg04.c-ctrip.com/images/04107120008pul4dm4CB7.jpg", "supportPreAuth": 0, "preAuthType": 1}, "idCardTypes": [{"idCardType": 1, "idCardName": "身份证"}], "flightDelayRule": {}, "priceChangeInfo": {}, "extra": {"isPriceUnited": "1"}, "rentalMustReadTitle": {"title": "限制政策", "desc": "车辆不允许进入新疆、西藏、昌都左贡县，拉萨当雄县，日喀则尼木县，林芝工布江达县、云南省泸沽湖、甘孜州、阿坝州、昌都左贡县若违反上述禁行规则，门店可拒绝提供或强制收回车辆；强制收回车辆时，产生的相关费用由用户承担，剩余租期费用退还规则以门店为准。"}, "fType": false, "isSelected": false, "trackInfo": {"vendorCode": "13039", "depositType": 10, "vendorPlatFrom": 0, "riskOriginal": "0", "riskFinal": "0", "depositFreeType": 3}, "gsDesc": "您选中了低价省钱!", "packageInfos": [{}], "vehicleInfo": {"transmissionName": "自动挡", "displacement": "1.0T", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com//images/0412d120008at1z4293BE.jpg", "https://dimg04.c-ctrip.com//images/0414k120008at15l04B7B.jpg", "https://dimg04.c-ctrip.com//images/04139120008at3x402339.jpg", "https://dimg04.c-ctrip.com//images/0416n120008at1b3t7DE9.jpg", "https://dimg04.c-ctrip.com//images/04135120008at4it05F01.jpg", "https://dimg04.c-ctrip.com//images/04142120008at10cy3FB8.jpg", "https://dimg04.c-ctrip.com//images/0413g120008at239eCF3C.jpg", "https://dimg04.c-ctrip.com//images/04171120008at46pr9E1E.jpg", "https://dimg04.c-ctrip.com//images/0415b120008at1hgaA2A9.jpg"], "vehicleCode": "20150", "fuel": "汽油92号", "licenseStyle": "6", "oilType": 5, "driveMode": "前置前驱", "name": "雪佛兰科沃兹", "sourcePicInfos": [{"sourceName": "年款/颜色等以实物为准", "source": 0, "type": 2, "picList": [{"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/04135120008at4it05F01.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/04139120008at3x402339.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/0413g120008at239eCF3C.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/04142120008at10cy3FB8.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/0414k120008at15l04B7B.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/0415b120008at1hgaA2A9.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/0416n120008at1b3t7DE9.jpg"}, {"sortNum": 1, "imageUrl": "https://dimg04.c-ctrip.com//images/04171120008at46pr9E1E.jpg"}]}, {"sourceName": "年款/颜色等以实物为准", "source": 1, "type": 2, "picList": [{"sortNum": 0, "imageUrl": "https://dimg04.c-ctrip.com//images/0412d120008at1z4293BE.jpg"}]}], "brandName": "雪佛兰", "brandId": 0, "imageUrl": "//dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0scn143fai2ah4w9B69.jpg", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "三厢", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0414k120008at15l04B7B.jpg", "https://dimg04.c-ctrip.com//images/04139120008at3x402339.jpg", "https://dimg04.c-ctrip.com//images/0416n120008at1b3t7DE9.jpg", "https://dimg04.c-ctrip.com//images/04135120008at4it05F01.jpg", "https://dimg04.c-ctrip.com//images/04142120008at10cy3FB8.jpg", "https://dimg04.c-ctrip.com//images/0413g120008at239eCF3C.jpg", "https://dimg04.c-ctrip.com//images/04171120008at46pr9E1E.jpg", "https://dimg04.c-ctrip.com//images/0415b120008at1hgaA2A9.jpg"], "groupCode": "2", "userRealImageCount": 0, "gearbox": "双离合变速箱(DCT)", "luggageNo": 2, "brandEName": "雪佛兰", "groupSubClassCode": "", "groupName": "经济型", "transmissionType": 1, "license": ""}, "pickupStoreInfo": {"showType": 1, "wayInfo": 0, "bizVendorCode": "13039", "countryName": "中国", "storeCode": "225861", "telephone": "17003421111;18589577527", "storeName": "三亚凤凰国际机场送车点", "latitude": 18.303421, "storeGuild": "自行前往门店取还车", "countryId": 1, "cityId": 43, "longitude": 109.414871, "storeWay": "到店取还车", "distance": "自行前往门店取还车，距门店直线543米", "storeGuildSplit": "自行前往门店取车", "workTime": {"openTime": "08:00", "closeTime": "23:59", "openTimeDesc": "{\"\":\"08: 00 - 23: 59\"}"}, "pickUpOnDoor": false, "cityName": "三亚", "storeType": 2, "address": "三亚市天涯区机场路凤凰国际机场", "freeShuttle": false}, "referenceTemp": {"klb": 0, "returnWayInfo": 0, "pickWayInfo": 0, "isKarabi": 0}, "baseResponse": {"code": "200", "extMap": {"carDamage": "19", "priceDetail": "139", "productDetail": "219", "dataConvertResCost": "40.0", "userAuth": "8", "bookingNotice": "16", "pickupCityId": "43", "pageName": "Product", "uid": "M00588838", "errorCode": "0", "apiCost": "220.0", "allCost": "260.0", "end": "2022-03-02 15:29:53", "queryRentalMustRead": "19", "creditScore": "17", "errorSource": "RF", "easyLifeServiceLabel": "200", "start": "2022-03-02 15:29:53", "dropoffCityId": "43", "storeAndUserPic": "12"}, "isSuccess": true}, "ResponseStatus": {"Extension": [{"Value": "8340535466124764593", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a056b21-457279-38281", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1646206193389+0800)/"}, "commentInfo": {"overallRating": "0.0", "maximumRating": 5, "level": "", "commentLabel": "", "link": "https://m.ctrip.fat67.qa.nt.ctripcorp.com/webapp/vacations/order/public/comment_list?channel=car-rental&scene=STORE_QUERY&storeId=225861&vehicleId=169372&vehicleName=雪佛兰科沃兹&productCategoryId=35&isHideNavBar=YES", "hasComment": 0, "commentCount": 0}, "needDownGrade": false, "vcCacheKey": "true", "returnStoreInfo": {"showType": 1, "wayInfo": 0, "bizVendorCode": "13039", "countryName": "中国", "storeCode": "225861", "telephone": "17003421111;18589577527", "storeName": "三亚凤凰国际机场送车点", "returnOnDoor": false, "latitude": 18.303421, "storeGuild": "自行前往门店取还车", "countryId": 1, "cityId": 43, "longitude": 109.414871, "storeWay": "到店还车", "distance": "自行前往门店取还车，距门店直线543米", "storeGuildSplit": "自行前往门店还车", "workTime": {"openTime": "08:00", "closeTime": "23:59", "openTimeDesc": "{\"\":\"08: 00 - 23: 59\"}"}, "cityName": "三亚", "storeType": 2, "address": "三亚市天涯区机场路凤凰国际机场", "freeShuttle": false}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "priceChange": false, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 328, "environmentCost": 1, "cacheFetchCost": 0, "fetchCost": 328, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1646206193091, "afterFetch": 1646206193419, "hasRetry": false}, "promptInfos": [{"type": 21, "title": "增加多名驾驶员", "contents": [{"stringObjs": [{"content": "默认仅支持一名驾驶员。如需多人开车，请提前咨询门店。如可增加，可能产生额外收费。每名驾驶员都需要出示与主驾驶员相同要求的取车证件。如果未经登记的人驾驶车辆，保险可能失效，请提前与门店确认（具体以门店回复为准）"}]}]}]}