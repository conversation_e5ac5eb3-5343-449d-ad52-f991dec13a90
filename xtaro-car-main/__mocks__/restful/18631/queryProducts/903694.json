{"baseResponse": {"isSuccess": false, "code": "201", "returnMsg": "No Stock", "requestId": "ecd8be54-7078-406b-98f6-e144acde28d1", "extMap": {"shennongType": "[18192]"}, "extraIndexTags": {"rCityId": "1360", "pcName": "摩洛哥", "rcId": "71", "pcId": "71", "pCityId": "1360", "pCityName": "马拉喀什", "rcName": "摩洛哥", "rCityName": "马拉喀什"}, "apiResCodes": [], "hasResult": false, "errorCode": "32101", "message": "No Stock"}, "ResponseStatus": {"Timestamp": "2024-01-24 17:03:34", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "6450899993425907665"}, {"Id": "RootMessageId", "Value": "921822-0a619639-473913-138674"}]}, "requestInfo": {"pickupDate": "2024-01-24 10:30:00", "pickupLocationName": "Marrakesh <PERSON>ara Airport(梅纳拉机场)", "returnDate": "2024-01-26 10:30:00", "returnLocationName": "Marrakesh <PERSON>ara Airport(梅纳拉机场)", "sourceCountryId": 1, "age": 30, "pLatitude": 31.602149, "rLatitude": 31.602149, "rLongitude": -8.02706, "pLongitude": -8.02706, "pDate": "20240124103000", "rDate": "20240126103000", "pCityId": 1360, "rCityId": 1360}, "productGroupsHashCode": "37a6259cc0c1dae299a7866489dff0bd2be88ca4242c76e8253ac62474851065032d6833", "needRetry": false, "commNotices": [], "basicData": {"sortItems": [{"title": "推荐排序", "type": 1, "code": "1", "sortNum": 1}, {"title": "价格 低→高", "type": 2, "code": "2", "sortNum": 2}], "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}}, "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "isAll": false, "isFromSearch": true, "uniqSign": "ecd8be54-7078-406b-98f6-e144acde28d1"}