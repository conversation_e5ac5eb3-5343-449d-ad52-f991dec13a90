{"baseResponse": {"extMap": {}, "cost": 1778, "hasResult": true, "extraIndexTags": {"pcName": "中国", "rcId": "1", "rcName": "中国", "rCityName": "上海", "rCityId": "2", "pCityId": "2", "pcId": "1", "pCityName": "上海"}, "code": "200", "errorCode": "0", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "c453371e-bc8e-462f-8b58-285f0171686a", "isSuccess": true}, "rHub": 1, "isKlbData": true, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "allVendorPriceCount": 2893, "isFromSearch": false, "rRentCenter": {"images": ["https://dimg04.c-ctrip.com/images/0yc4r12000e95lnyn6A18.jpg", "https://dimg04.c-ctrip.com/images/0yc4a12000e95lyoo0BDF.jpg"], "isNew": 1, "address": "虹桥火车站综合交通枢纽西出口1号柜台", "id": 148, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "22:59", "lat": "31.194106", "lng": "121.320666", "name": "虹桥火车站租车中心", "fromTime": "00:00"}, "extras": {"packageLevelAB": "B", "abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B", "isLicensePlateHideShow": "0", "serverRequestId": "66C2LD47EY8P23156M8g", "packageLevelSwitch": "1", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "1", "prepProductGroupTopSwitch": "0"}, "timeInterval": 1855.28076171875, "productGroupsHashCode": "l9C7G22oVnf54Vb2Uti9", "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 160, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 320, "detail": [{"code": "1001", "amount": 320, "amountDesc": "¥320", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥160", "subAmount": 160, "name": "车辆租金", "amountStr": "¥320"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 240, "amountStr": "¥240", "detail": [{"code": "1002", "amount": 240, "amountDesc": "¥240", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 590, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥590", "subAmount": 590, "name": "总价", "amountStr": "¥590"}], "reference": {"vehicleCode": "0", "rStoreCode": "115174", "packageId": "", "pLev": 1317499, "comPriceCode": "[c]OTc0fDE5MTd8MjAyMC4wMC0xOCA0LTA5MDowMDAwOjAmJjEmJjE2MGUkMjBmYWxzOS0xOTI0LTAwMDowIDAwOjAmJjEwJjE2c2UkfCZmYWwmMiYxMTAwMTIwJDE2MCYzMSYzMDAwMyYzMC4wLjAwJjAyJjIwJDEwLjAwJiYxMjAwMCR8MjQwLi0wOS0yMDI0ODowMDE4IDEyMDI0OjAwJjIwIDEtMDktOjAwfDg6MDAtMDktMjAyNDY6NTIxNCAxAAAAADoxNwA=", "bizVendorCode": "SD4972", "pStoreCode": "115174", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTE1MTc0XzE2Nl8xXzE2MF8zMjBfMTYwXzU5MC4wMF8xNjBfNTkwLjBfMF8wXzAuMF8wLjBfMjQwLjAwXzMwLjAwXzAuMDBfMC4wMF8xOTE3OTc0", "sendTypeForPickUpCar": 0, "skuId": 1917974, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1317499, "vendorCode": "82027", "vendorVehicleCode": "1917974"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15000514", "vehicleCode": "0", "packageType": 1, "pStoreCode": "153053", "skuId": 6935962, "bizVendorCode": "SD7256"}}, {"reference": {"vendorCode": "13097", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183709", "skuId": 28536859, "bizVendorCode": "SD7665"}}, {"reference": {"vendorCode": "30116", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106861", "skuId": 1861749, "bizVendorCode": "SD3864"}}, {"reference": {"vendorCode": "13033", "vehicleCode": "0", "packageType": 1, "pStoreCode": "44811", "skuId": 232509, "bizVendorCode": "SD3008"}}, {"reference": {"vendorCode": "15000430", "vehicleCode": "0", "packageType": 1, "pStoreCode": "137302", "skuId": 8284830, "bizVendorCode": "SD7172"}}, {"reference": {"vendorCode": "73521", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107547", "skuId": 45705647, "bizVendorCode": "SD4042"}}, {"reference": {"vendorCode": "15001014", "vehicleCode": "0", "packageType": 0, "pStoreCode": "174847", "skuId": 59837786, "bizVendorCode": "SD7971"}}, {"reference": {"vendorCode": "61964", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114432", "skuId": 24690282, "bizVendorCode": "SD4396"}}, {"reference": {"vendorCode": "15000959", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174254", "skuId": 4349914, "bizVendorCode": "SD7702"}}, {"reference": {"vendorCode": "15001696", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183387", "skuId": 46106621, "bizVendorCode": "SD9178"}}, {"reference": {"vendorCode": "30105", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114042", "skuId": 2440629, "bizVendorCode": "SD4198"}}, {"reference": {"vendorCode": "15002717", "vehicleCode": "0", "packageType": 0, "pStoreCode": "193043", "skuId": 6904097, "bizVendorCode": "SD9741"}}, {"reference": {"vendorCode": "15001053", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174557", "skuId": 5303481, "bizVendorCode": "SD8013"}}, {"reference": {"vendorCode": "15006919", "vehicleCode": "0", "packageType": 0, "pStoreCode": "1535381", "skuId": 57003087, "bizVendorCode": "SD14202"}}, {"reference": {"vendorCode": "15000128", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106713", "skuId": 34919487, "bizVendorCode": "SD3167"}}, {"reference": {"vendorCode": "15002708", "vehicleCode": "0", "packageType": 1, "pStoreCode": "192988", "skuId": 5640679, "bizVendorCode": "SD9732"}}, {"reference": {"vendorCode": "15000176", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106745", "skuId": 2428390, "bizVendorCode": "SD3547"}}, {"reference": {"vendorCode": "15003166", "vehicleCode": "0", "packageType": 1, "pStoreCode": "194714", "skuId": 24301488, "bizVendorCode": "SD10197"}}, {"reference": {"vendorCode": "15005385", "vehicleCode": "0", "packageType": 1, "pStoreCode": "532043", "skuId": 59162937, "bizVendorCode": "SD12574"}}, {"reference": {"vendorCode": "15005216", "vehicleCode": "0", "packageType": 1, "pStoreCode": "426518", "skuId": 31082346, "bizVendorCode": "SD12402"}}, {"reference": {"vendorCode": "81617", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115131", "skuId": 1910598, "bizVendorCode": "SD4932"}}, {"reference": {"vendorCode": "61975", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114320", "skuId": 1918645, "bizVendorCode": "SD4400"}}, {"reference": {"vendorCode": "85545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117715", "skuId": 4448995, "bizVendorCode": "SD5682"}}, {"reference": {"vendorCode": "61084", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114298", "skuId": 1917959, "bizVendorCode": "SD4380"}}, {"reference": {"vendorCode": "62853", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114463", "skuId": 41473867, "bizVendorCode": "SD4444"}}, {"reference": {"vendorCode": "13027", "vehicleCode": "0", "packageType": 1, "pStoreCode": "45784", "skuId": 243327, "bizVendorCode": "SD3010"}}, {"reference": {"vendorCode": "61920", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114323", "skuId": 2440947, "bizVendorCode": "SD4392"}}, {"reference": {"vendorCode": "30501", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114065", "skuId": 1916711, "bizVendorCode": "SD4217"}}, {"reference": {"vendorCode": "30026", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114037", "skuId": 1957628, "bizVendorCode": "SD4193"}}, {"reference": {"vendorCode": "15002528", "vehicleCode": "0", "packageType": 1, "pStoreCode": "189318", "skuId": 5629694, "bizVendorCode": "SD9544"}}, {"reference": {"vendorCode": "85585", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106650", "skuId": 1837035, "bizVendorCode": "SD3402"}}, {"reference": {"vendorCode": "82099", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117242", "skuId": 1964376, "bizVendorCode": "SD6130"}}, {"reference": {"vendorCode": "61916", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117800", "skuId": 2015465, "bizVendorCode": "SD6391"}}, {"reference": {"vendorCode": "71841", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114647", "skuId": 1922510, "bizVendorCode": "SD4641"}}], "rCoup": 0, "vehicleCode": "166", "highestPrice": 666, "pWay": "可选：免费站内取还车", "minDPrice": 160, "vehicleKey": "0_166_沪牌", "hot": 0, "minTPrice": 590, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 13, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD4972_0_115174_115174"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 4743, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0.35701016, "hotType": 0, "reactId": "165219770", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 35}, {"groupSort": 0, "lowestPrice": 98, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 196, "detail": [{"code": "1001", "amount": 196, "amountDesc": "¥196", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥98", "subAmount": 98, "name": "车辆租金", "amountStr": "¥196"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 200, "amountStr": "¥200", "detail": [{"code": "1002", "amount": 200, "amountDesc": "¥200", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 426, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥426", "subAmount": 426, "name": "总价", "amountStr": "¥426"}], "reference": {"vehicleCode": "0", "rStoreCode": "107858", "packageId": "", "pLev": 1562359, "comPriceCode": "[c]", "bizVendorCode": "SD4165", "pStoreCode": "107858", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3ODU4XzE4MDY2XzFfOThfMTk2Xzk4XzQyNi4wMF85OF80MjYuMF8wXzBfMC4wXzAuMF8yMDAuMDBfMzAuMDBfMC4wMF8wLjAwXzQxNDkwODE0", "sendTypeForPickUpCar": 0, "skuId": 41490814, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1562359, "vendorCode": "43976", "vendorVehicleCode": "41490814"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "18066", "highestPrice": 98, "pWay": "可选：免费站内取还车", "minDPrice": 98, "vehicleKey": "0_18066_沪牌", "hot": 0, "minTPrice": 426, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 14, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD4165_0_107858_107858"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 3661, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "165219781", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1}, {"groupSort": 0, "lowestPrice": 128, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 256, "detail": [{"code": "1001", "amount": 256, "amountDesc": "¥256", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥128", "subAmount": 128, "name": "车辆租金", "amountStr": "¥256"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 371, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥371", "subAmount": 371, "name": "总价", "amountStr": "¥371"}], "reference": {"vehicleCode": "0", "rStoreCode": "1511609", "packageId": "", "pLev": 1568442, "comPriceCode": "[c]MjczMTU4MDMwfDIwfDAuMDktMTgyNC0wMDA6MCAwMDo4JiYxMCYxMnNlJDImZmFsMDktMTAyNC06MDA6OSAwMDI4JiYwMCYxbHNlJDEmZmExJjImfDEwMDI1NiQxMjgmJjEmMzEwMDMmMzUuNS4wMDAwMiYwMCQxLjAwJjImNDAwJHwyODAuMDA5LTEwMjQtOjAwOjggMTgwMjQtMDAmMjAgMTgwOS0yMDB8MjowMDowOS0xMDI0LTo1Mjo0IDE2AAAAADE3AAA=", "bizVendorCode": "SD14077", "pStoreCode": "1511609", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTUxMTYwOV80NDgyXzFfMTI4XzI1Nl8xMjhfMzcxLjAwXzEyOF8zNzEuMF8wXzBfMC4wXzAuMF84MC4wMF8zNS4wMF8wLjAwXzAuMDBfNTgwMzI3MzE=", "sendTypeForPickUpCar": 0, "skuId": 58032731, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1568442, "vendorCode": "15006810", "vendorVehicleCode": "58032731"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "71523", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107401", "skuId": 1855377, "bizVendorCode": "SD8812"}}, {"reference": {"vendorCode": "15001696", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183387", "skuId": 25354323, "bizVendorCode": "SD9178"}}, {"reference": {"vendorCode": "15000430", "vehicleCode": "0", "packageType": 1, "pStoreCode": "137302", "skuId": 3385835, "bizVendorCode": "SD7172"}}, {"reference": {"vendorCode": "13099", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183715", "skuId": 24658845, "bizVendorCode": "SD7609"}}, {"reference": {"vendorCode": "43976", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107858", "skuId": 1887733, "bizVendorCode": "SD4165"}}, {"reference": {"vendorCode": "13097", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183709", "skuId": 24660518, "bizVendorCode": "SD7665"}}, {"reference": {"vendorCode": "73521", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107547", "skuId": 4915176, "bizVendorCode": "SD4042"}}, {"reference": {"vendorCode": "15003378", "vehicleCode": "0", "packageType": 1, "pStoreCode": "253457", "skuId": 9003177, "bizVendorCode": "SD10417"}}, {"reference": {"vendorCode": "13088", "vehicleCode": "0", "packageType": 0, "pStoreCode": "139933", "skuId": 9992949, "bizVendorCode": "SD6991"}}, {"reference": {"vendorCode": "15002708", "vehicleCode": "0", "packageType": 1, "pStoreCode": "192988", "skuId": 24965508, "bizVendorCode": "SD9732"}}, {"reference": {"vendorCode": "45976", "vehicleCode": "0", "packageType": 1, "pStoreCode": "314056", "skuId": 25278345, "bizVendorCode": "SD3932"}}, {"reference": {"vendorCode": "15000539", "vehicleCode": "0", "packageType": 1, "pStoreCode": "153013", "skuId": 3016487, "bizVendorCode": "SD7281"}}, {"reference": {"vendorCode": "15006151", "vehicleCode": "0", "packageType": 1, "pStoreCode": "789818", "skuId": 49479739, "bizVendorCode": "SD13367"}}, {"reference": {"vendorCode": "15005216", "vehicleCode": "0", "packageType": 1, "pStoreCode": "426518", "skuId": 47717048, "bizVendorCode": "SD12402"}}, {"reference": {"vendorCode": "15005385", "vehicleCode": "0", "packageType": 1, "pStoreCode": "532043", "skuId": 41092661, "bizVendorCode": "SD12574"}}, {"reference": {"vendorCode": "85545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117715", "skuId": 44003492, "bizVendorCode": "SD5682"}}], "rCoup": 0, "vehicleCode": "4482", "highestPrice": 440, "pWay": "可选：免费站内取还车", "minDPrice": 128, "vehicleKey": "0_4482_沪牌", "hot": 0, "minTPrice": 371, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 15, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD14077_0_1511609_1511609"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 4353, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "165219671", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 17}, {"groupSort": 1, "lowestPrice": 68, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 136, "detail": [{"code": "1001", "amount": 136, "amountDesc": "¥136", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥68", "subAmount": 68, "name": "车辆租金", "amountStr": "¥136"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 231, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥231", "subAmount": 231, "name": "总价", "amountStr": "¥231"}], "reference": {"vehicleCode": "0", "rStoreCode": "1443765", "packageId": "sec", "pLev": 1537551, "comPriceCode": "[c]NzY5MTU1OTIwfDIwfDAuMDktMTgyNC0wMDA6MCAwMDomJjEmMCY2OGUkMjBmYWxzOS0xOTI0LTAwMDowIDAwOiYmMSYwJjY4ZSR8MWZhbHMyJjY4MDAxJiQxMDAmMTM2MzUuMDMmMSYuMDAkMCYzNSYyJjMxMDAyJjYwLjAuMDAyMDI0MDAkfDE4IDEtMDktOjAwJjg6MDAtMDktMjAyNDg6MDAyMCAxMjAyNDowMHwxNCAxLTA5LToxNwA2OjUy", "bizVendorCode": "SD13858", "pStoreCode": "1443765", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTQ0Mzc2NV80MDY3XzFfNjhfMTM2XzY4XzIzMS4wMF82OF8yMzEuMF8wXzBfMC4wXzAuMF82MC4wMF8zNS4wMF8wLjAwXzAuMDBfNTU5Mjc2OTE=", "sendTypeForPickUpCar": 0, "skuId": 55927691, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1537551, "vendorCode": "15006611", "vendorVehicleCode": "86838_23157_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13104", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184419", "skuId": 55863599, "bizVendorCode": "SD8855"}}, {"reference": {"vendorCode": "15000323", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193437", "skuId": 5583813, "bizVendorCode": "SD8718"}}, {"reference": {"vendorCode": "13072", "vehicleCode": "0", "packageType": 1, "pStoreCode": "137706", "skuId": 6902140, "bizVendorCode": "SD7089"}}, {"reference": {"vendorCode": "13088", "vehicleCode": "0", "packageType": 0, "pStoreCode": "150269", "skuId": 15876694, "bizVendorCode": "SD6991"}}, {"reference": {"vendorCode": "13088", "vehicleCode": "0", "packageType": 0, "pStoreCode": "150269", "skuId": 9232147, "bizVendorCode": "SD6991"}}, {"reference": {"vendorCode": "15004163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "410355", "skuId": 51731240, "bizVendorCode": "SD3047"}}, {"reference": {"vendorCode": "30116", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106855", "skuId": 5148715, "bizVendorCode": "SD3864"}}, {"reference": {"vendorCode": "33528", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181596", "skuId": 5217043, "bizVendorCode": "SD3916"}}, {"reference": {"vendorCode": "65434", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107798", "skuId": 1957996, "bizVendorCode": "SD11803"}}, {"reference": {"vendorCode": "15002387", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188504", "skuId": 29687792, "bizVendorCode": "SD9394"}}, {"reference": {"vendorCode": "15000267", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133745", "skuId": 3275715, "bizVendorCode": "SD10647"}}, {"reference": {"vendorCode": "15002235", "vehicleCode": "0", "packageType": 1, "pStoreCode": "187445", "skuId": 5250224, "bizVendorCode": "SD9235"}}, {"reference": {"vendorCode": "80023", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107109", "skuId": 2865531, "bizVendorCode": "SD4080"}}, {"reference": {"vendorCode": "79391", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116679", "skuId": 1965557, "bizVendorCode": "SD6613"}}, {"reference": {"vendorCode": "83520", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115366", "skuId": 3910851, "bizVendorCode": "SD11032"}}, {"reference": {"vendorCode": "84953", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106704", "skuId": 41475638, "bizVendorCode": "SD3472"}}], "rCoup": 0, "vehicleCode": "1126", "highestPrice": 262, "pWay": "可选：免费站内取还车", "minDPrice": 68, "vehicleKey": "0_1126_", "hot": 0, "minTPrice": 231, "lowestDistance": 40.3667, "group": 101126, "type": 0, "groupTypeBits": 1, "sortNum": 16, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD13858_0_1443765_1443765"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 3621, "productRef": {"license": "外牌", "licenseType": 1, "licenseTag": "", "licenseStyle": "6"}, "hotScore": 0.31515738, "hotType": 0, "reactId": "165219674", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 17}, {"groupSort": 2, "lowestPrice": 99, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 198, "detail": [{"code": "1001", "amount": 198, "amountDesc": "¥198", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥99", "subAmount": 99, "name": "车辆租金", "amountStr": "¥198"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 353, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥353", "subAmount": 353, "name": "总价", "amountStr": "¥353"}], "reference": {"vehicleCode": "0", "rStoreCode": "107547", "packageId": "", "pLev": 95876, "comPriceCode": "[c]", "bizVendorCode": "SD4042", "pStoreCode": "107547", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3NTQ3XzUzNDhfMV85OV8xOThfOTlfMzUzLjAwXzk5XzM1My4wXzBfMF8wLjBfMC4wXzEyMC4wMF8zNS4wMF8wLjAwXzAuMDBfMTg2MTE0Nw==", "sendTypeForPickUpCar": 0, "skuId": 1861147, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 95876, "vendorCode": "73521", "vendorVehicleCode": "1861147"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "83287", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115339", "skuId": 2012198, "bizVendorCode": "SD5096"}}, {"reference": {"vendorCode": "15004069", "vehicleCode": "0", "packageType": 1, "pStoreCode": "264608", "skuId": 23403045, "bizVendorCode": "SD11164"}}, {"reference": {"vendorCode": "81883", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107099", "skuId": 2707140, "bizVendorCode": "SD4112"}}], "rCoup": 0, "vehicleCode": "1126", "highestPrice": 239, "pWay": "可选：免费站内取还车", "minDPrice": 99, "vehicleKey": "0_1126_沪牌", "hot": 0, "minTPrice": 353, "lowestDistance": 0, "group": 101126, "type": 0, "groupTypeBits": 1, "sortNum": 17, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD4042_0_107547_107547"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 3089, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0.31515738, "hotType": 0, "reactId": "165219675", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 4}, {"groupSort": 1, "lowestPrice": 68, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 136, "detail": [{"code": "1001", "amount": 136, "amountDesc": "¥136", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥68", "subAmount": 68, "name": "车辆租金", "amountStr": "¥136"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 216, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥216", "subAmount": 216, "name": "总价", "amountStr": "¥216"}], "reference": {"vehicleCode": "0", "rStoreCode": "410355", "packageId": "sec", "pLev": 1615545, "bizVendorCode": "SD3047", "pStoreCode": "410355", "packageType": 1, "priceVersion": "SH-PRICEVERSION_NDEwMzU1XzUzNDlfMV82OC4wXzEzNi4wXzAuMF8yMTYuMF82OC4wXzIxNi4wXzBfMF8wLjBfMC4wXzYwLjBfMjAuMF8wXzBfNTE3MjgyODY=", "sendTypeForPickUpCar": 0, "skuId": 51728286, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1615545, "vendorCode": "15004163", "vendorVehicleCode": "4649_52668_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15004635", "vehicleCode": "0", "packageType": 0, "pStoreCode": "327097", "skuId": 55158965, "bizVendorCode": "SD11767"}}, {"reference": {"vendorCode": "33528", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181596", "skuId": 54177957, "bizVendorCode": "SD3916"}}, {"reference": {"vendorCode": "15006932", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1544138", "skuId": 56970320, "bizVendorCode": "SD12823"}}, {"reference": {"vendorCode": "15002387", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188504", "skuId": 5337543, "bizVendorCode": "SD9394"}}, {"reference": {"vendorCode": "65434", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107219", "skuId": 1863408, "bizVendorCode": "SD11803"}}, {"reference": {"vendorCode": "84181", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106480", "skuId": 2426725, "bizVendorCode": "SD3387"}}, {"reference": {"vendorCode": "66757", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114366", "skuId": 30062887, "bizVendorCode": "SD4522"}}, {"reference": {"vendorCode": "80023", "vehicleCode": "0", "packageType": 0, "pStoreCode": "107109", "skuId": 55309346, "bizVendorCode": "SD4080"}}], "rCoup": 0, "vehicleCode": "5349", "highestPrice": 334, "pWay": "可选：免费站内取还车", "minDPrice": 68, "vehicleKey": "0_5349_", "hot": 0, "minTPrice": 216, "lowestDistance": 70.578, "group": 105349, "type": 0, "groupTypeBits": 1, "sortNum": 18, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3047_0_410355_410355"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 2602, "productRef": {"license": "外牌", "licenseType": 1, "licenseTag": "", "licenseStyle": "6"}, "hotScore": 0, "hotType": 0, "reactId": "165219676", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 9}, {"groupSort": 2, "lowestPrice": 68, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 138, "detail": [{"code": "1001", "amount": 138, "amountDesc": "¥138", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥69", "subAmount": 69, "name": "车辆租金", "amountStr": "¥138"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 273, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥273", "subAmount": 273, "name": "总价", "amountStr": "¥273"}], "reference": {"vehicleCode": "0", "rStoreCode": "107591", "packageId": "", "pLev": 25964, "comPriceCode": "[c]MDUxfDE4NTl8MjAyMC4wMC0xOCA0LTA5MDowMDAwOjAmMSZmJjY5JiQyMDJhbHNlLTE5IDQtMDkwOjAwMDA6MCYxJmYmNjkmJHwxMGFsc2UmNjkmMDEmMjEwMDMxMzgkNS4wMCYxJjMwMCQxJjM1LjImNTAwMDImMTAwLi4wMCYyMDI0MDAkfDE4IDEtMDktOjAwJjg6MDAtMDktMjAyNDg6MDAyMCAxMjAyNDowMHwxNCAxLTA5LToxNwA2OjUy", "bizVendorCode": "SD4050", "pStoreCode": "107591", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3NTkxXzQwMTlfMV82OV8xMzhfNjlfMjczLjAwXzY5XzI3My4wXzBfMF8wLjBfMC4wXzEwMC4wMF8zNS4wMF8wLjAwXzAuMDBfMTg1OTA1MQ==", "sendTypeForPickUpCar": 0, "skuId": 1859051, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 25964, "vendorCode": "74409", "vendorVehicleCode": "1859051"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13084", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184175", "skuId": 6893426, "bizVendorCode": "SD7100"}}, {"reference": {"vendorCode": "15001108", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175256", "skuId": 24873640, "bizVendorCode": "SD8066"}}, {"reference": {"vendorCode": "15003378", "vehicleCode": "0", "packageType": 1, "pStoreCode": "253457", "skuId": 55092766, "bizVendorCode": "SD10417"}}, {"reference": {"vendorCode": "73521", "vehicleCode": "0", "packageType": 0, "pStoreCode": "107547", "skuId": 28561821, "bizVendorCode": "SD4042"}}, {"reference": {"vendorCode": "15001696", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183387", "skuId": 39475066, "bizVendorCode": "SD9178"}}, {"reference": {"vendorCode": "85251", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117737", "skuId": 1959180, "bizVendorCode": "SD5673"}}, {"reference": {"vendorCode": "15002426", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188716", "skuId": 9025024, "bizVendorCode": "SD9437"}}, {"reference": {"vendorCode": "15000539", "vehicleCode": "0", "packageType": 1, "pStoreCode": "153013", "skuId": 5184091, "bizVendorCode": "SD7281"}}, {"reference": {"vendorCode": "15001935", "vehicleCode": "0", "packageType": 0, "pStoreCode": "186872", "skuId": 41509231, "bizVendorCode": "SD8915"}}, {"reference": {"vendorCode": "85519", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117710", "skuId": 6743703, "bizVendorCode": "SD5684"}}, {"reference": {"vendorCode": "15005385", "vehicleCode": "0", "packageType": 1, "pStoreCode": "532043", "skuId": 41111442, "bizVendorCode": "SD12574"}}, {"reference": {"vendorCode": "15005640", "vehicleCode": "0", "packageType": 1, "pStoreCode": "624849", "skuId": 44195656, "bizVendorCode": "SD12840"}}, {"reference": {"vendorCode": "81415", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117135", "skuId": 4469642, "bizVendorCode": "SD6788"}}], "rCoup": 0, "vehicleCode": "5349", "highestPrice": 286, "pWay": "可选：免费站内取还车", "minDPrice": 69, "vehicleKey": "0_5349_沪牌", "hot": 0, "minTPrice": 273, "lowestDistance": 19.1952, "group": 105349, "type": 0, "groupTypeBits": 1, "sortNum": 19, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD7100_0_184175_184175"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 3089, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "165219677", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 14}, {"groupSort": 0, "lowestPrice": 118, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 304, "detail": [{"code": "1001", "amount": 304, "amountDesc": "¥304", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥152", "subAmount": 152, "name": "车辆租金", "amountStr": "¥304"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 439, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥439", "subAmount": 439, "name": "总价", "amountStr": "¥439"}], "reference": {"vehicleCode": "0", "rStoreCode": "136750", "packageId": "", "pLev": 79532, "comPriceCode": "[c]NTYzfDI1MDN8MjAyMC4wMS0xOCA0LTA5MDowMDAwOjAmJjEmJjE1MmUkMjBmYWxzOS0xOTI0LTAwMDowIDAwOjImJjEwJjE1c2UkfCZmYWwmMiYxMTAwMTA0JDE1MiYzMSYzNTAwMyYzNS4wLjAwJjAyJjIwJDEwMDAmMSY1MC4wJHwyMDAuMDA5LTEwMjQtOjAwOjggMTgwMjQtMDAmMjAgMTgwOS0yMDB8MjowMDowOS0xMDI0LTo1Mjo0IDE2AAAAADE3AAA=", "bizVendorCode": "SD7112", "pStoreCode": "136750", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTM2NzUwXzU1OTFfMV8xNTJfMzA0XzE1Ml80MzkuMDBfMTUyXzQzOS4wXzBfMF8wLjBfMC4wXzEwMC4wMF8zNS4wMF8wLjAwXzAuMDBfMjUwMzU2Mw==", "sendTypeForPickUpCar": 0, "skuId": 2503563, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 79532, "vendorCode": "15000370", "vendorVehicleCode": "2503563"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15003378", "vehicleCode": "0", "packageType": 1, "pStoreCode": "253457", "skuId": 24405592, "bizVendorCode": "SD10417"}}, {"reference": {"vendorCode": "13072", "vehicleCode": "0", "packageType": 1, "pStoreCode": "137706", "skuId": 6902109, "bizVendorCode": "SD7089"}}, {"reference": {"vendorCode": "85251", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117737", "skuId": 1970976, "bizVendorCode": "SD5673"}}, {"reference": {"vendorCode": "13097", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183709", "skuId": 25575565, "bizVendorCode": "SD7665"}}, {"reference": {"vendorCode": "15003821", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251078", "skuId": 56492983, "bizVendorCode": "SD10876"}}, {"reference": {"vendorCode": "15002667", "vehicleCode": "0", "packageType": 0, "pStoreCode": "192890", "skuId": 5643675, "bizVendorCode": "SD9690"}}, {"reference": {"vendorCode": "83970", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106628", "skuId": 41468750, "bizVendorCode": "SD3324"}}, {"reference": {"vendorCode": "15006151", "vehicleCode": "0", "packageType": 1, "pStoreCode": "789818", "skuId": 45612183, "bizVendorCode": "SD13367"}}, {"reference": {"vendorCode": "15002427", "vehicleCode": "0", "packageType": 0, "pStoreCode": "229102", "skuId": 6433878, "bizVendorCode": "SD9438"}}, {"reference": {"vendorCode": "15004890", "vehicleCode": "0", "packageType": 0, "pStoreCode": "410999", "skuId": 34488385, "bizVendorCode": "SD12066"}}, {"reference": {"vendorCode": "15003350", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229299", "skuId": 6862887, "bizVendorCode": "SD10388"}}, {"reference": {"vendorCode": "63129", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114460", "skuId": 2707753, "bizVendorCode": "SD4454"}}], "rCoup": 0, "vehicleCode": "5591", "highestPrice": 470, "pWay": "可选：店员免费上门送取车", "minDPrice": 152, "vehicleKey": "0_5591_沪牌", "hot": 0, "minTPrice": 439, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 20, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD7089_0_137706_137706"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 3621, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "165219777", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 13}, {"groupSort": 1, "lowestPrice": 88, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 178, "detail": [{"code": "1001", "amount": 178, "amountDesc": "¥178", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥89", "subAmount": 89, "name": "车辆租金", "amountStr": "¥178"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 273, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥273", "subAmount": 273, "name": "总价", "amountStr": "¥273"}], "reference": {"vehicleCode": "0", "rStoreCode": "137706", "packageId": "sec", "pLev": 83230, "comPriceCode": "[c]MTM4fDY5MDJ8MjAyMC4wMC0xOCA0LTA5MDowMDAwOjAmMSZmJjg5JiQyMDJhbHNlLTE5IDQtMDkwOjAwMDA6MCYxJmYmODkmJHwxMGFsc2UmODkmMDEmMjEwMDMxNzgkNS4wMCYxJjMwMCQxJjM1LjImMzAwMDImNjAuMC4wMCYwMjQtMCR8MjggMTgwOS0xMDAmMjowMDowOS0yMDI0LTowMDowIDE4MDI0LTAwfDI0IDE2MDktMTE3AAA6NTI6", "bizVendorCode": "SD7089", "pStoreCode": "137706", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTM3NzA2XzQ4ODNfMV84OV8xNzhfODlfMjczLjAwXzg5XzI3My4wXzBfMF8wLjBfMC4wXzYwLjAwXzM1LjAwXzAuMDBfMC4wMF82OTAyMTM4", "sendTypeForPickUpCar": 0, "skuId": 6902138, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 83230, "vendorCode": "13072", "vendorVehicleCode": "2107_49537_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "73521", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107547", "skuId": 6958057, "bizVendorCode": "SD4042"}}, {"reference": {"vendorCode": "82013", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107128", "skuId": 5215516, "bizVendorCode": "SD4117"}}, {"reference": {"vendorCode": "53654", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107094", "skuId": 4928802, "bizVendorCode": "SD3940"}}, {"reference": {"vendorCode": "80023", "vehicleCode": "0", "packageType": 0, "pStoreCode": "107109", "skuId": 58033634, "bizVendorCode": "SD4080"}}], "rCoup": 0, "vehicleCode": "4883", "highestPrice": 185, "pWay": "可选：免费站内取还车", "minDPrice": 89, "vehicleKey": "0_4883_", "hot": 0, "minTPrice": 273, "lowestDistance": 71.5653, "group": 104883, "type": 0, "groupTypeBits": 1, "sortNum": 21, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3940_0_107094_107094"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 3621, "productRef": {"license": "外牌", "licenseType": 1, "licenseTag": "", "licenseStyle": "6"}, "hotScore": 0, "hotType": 0, "reactId": "165219782", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 5}, {"groupSort": 2, "lowestPrice": 138, "modifySameVehicle": false, "vendorPriceList": [{"reference": {"vendorCode": "73521", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107547", "skuId": 1858689, "bizVendorCode": "SD4042"}}, {"fees": [{"amount": 296, "detail": [{"code": "1001", "amount": 296, "amountDesc": "¥296", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥148", "subAmount": 148, "name": "车辆租金", "amountStr": "¥296"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 411, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥411", "subAmount": 411, "name": "总价", "amountStr": "¥411"}], "reference": {"vehicleCode": "0", "rStoreCode": "248964", "packageId": "", "pLev": 252871, "comPriceCode": "[c]NzE4fDY5MTF8MjAyMC4wMC0xOCA0LTA5MDowMDAwOjAmJjEmJjE0OGUkMjBmYWxzOS0xOTI0LTAwMDowIDAwOjgmJjEwJjE0c2UkfCZmYWwmMiYxMTAwMTk2JDE0OCYyMSYzNTAwMyYzNS4wLjAwJjAyJjIwJDEwMDAmOCY0MC4kfDIwMC4wMDktMTgyNC0wMDA6MCAxODoyNC0wMCYyMCAxODo5LTIwMHwyMDAwOjA5LTE0MjQtMDUyOjEgMTY6AAAAADcAAAA=", "bizVendorCode": "SD10666", "pStoreCode": "248964", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MjQ4OTY0XzQ4ODNfMV8xNDhfMjk2XzE0OF80MTEuMDBfMTQ4XzQxMS4wXzBfMF8wLjBfMC4wXzgwLjAwXzM1LjAwXzAuMDBfMC4wMF82OTExNzE4", "sendTypeForPickUpCar": 0, "skuId": 6911718, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 252871, "vendorCode": "15003615", "vendorVehicleCode": "6911718"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15001108", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175256", "skuId": 6870328, "bizVendorCode": "SD8066"}}, {"reference": {"vendorCode": "15000163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133589", "skuId": 2237964, "bizVendorCode": "SD3290"}}, {"reference": {"vendorCode": "43976", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107858", "skuId": 5339549, "bizVendorCode": "SD4165"}}, {"reference": {"vendorCode": "15000430", "vehicleCode": "0", "packageType": 1, "pStoreCode": "137302", "skuId": 2707862, "bizVendorCode": "SD7172"}}, {"reference": {"vendorCode": "82027", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115174", "skuId": 56490764, "bizVendorCode": "SD4972"}}, {"reference": {"vendorCode": "30116", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106861", "skuId": 1857221, "bizVendorCode": "SD3864"}}, {"reference": {"vendorCode": "13099", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183715", "skuId": 24658775, "bizVendorCode": "SD7609"}}, {"reference": {"vendorCode": "13097", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183709", "skuId": 24660462, "bizVendorCode": "SD7665"}}, {"reference": {"vendorCode": "15003378", "vehicleCode": "0", "packageType": 1, "pStoreCode": "253457", "skuId": 41989235, "bizVendorCode": "SD10417"}}, {"reference": {"vendorCode": "15002708", "vehicleCode": "0", "packageType": 1, "pStoreCode": "192988", "skuId": 6966877, "bizVendorCode": "SD9732"}}, {"reference": {"vendorCode": "15004133", "vehicleCode": "0", "packageType": 0, "pStoreCode": "274443", "skuId": 55119940, "bizVendorCode": "SD11233"}}, {"reference": {"vendorCode": "15005216", "vehicleCode": "0", "packageType": 1, "pStoreCode": "426518", "skuId": 55815334, "bizVendorCode": "SD12402"}}, {"reference": {"vendorCode": "15004082", "vehicleCode": "0", "packageType": 1, "pStoreCode": "274548", "skuId": 54505627, "bizVendorCode": "SD11178"}}, {"reference": {"vendorCode": "85519", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117710", "skuId": 1977671, "bizVendorCode": "SD5684"}}, {"reference": {"vendorCode": "15000539", "vehicleCode": "0", "packageType": 1, "pStoreCode": "153013", "skuId": 3021067, "bizVendorCode": "SD7281"}}], "rCoup": 0, "vehicleCode": "4883", "highestPrice": 348, "pWay": "可选：免费站内取还车", "minDPrice": 148, "vehicleKey": "0_4883_沪牌", "hot": 0, "minTPrice": 411, "lowestDistance": 33.1858, "group": 104883, "type": 0, "groupTypeBits": 1, "sortNum": 22, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD4042_0_107547_107547"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 4743, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "165219783", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 17}, {"groupSort": 0, "lowestPrice": 170, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 340, "detail": [{"code": "1001", "amount": 340, "amountDesc": "¥340", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥170", "subAmount": 170, "name": "车辆租金", "amountStr": "¥340"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 200, "amountStr": "¥200", "detail": [{"code": "1002", "amount": 200, "amountDesc": "¥200", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 575, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥575", "subAmount": 575, "name": "总价", "amountStr": "¥575"}], "reference": {"vehicleCode": "0", "rStoreCode": "106566", "packageId": "", "pLev": 19142, "comPriceCode": "[c]MzU0fDE4Mzd8MjAyMC4wMC0xOCA0LTA5MDowMDAwOjAmJjEmJjE3MGUkMjBmYWxzOS0xOTI0LTAwMDowIDAwOjAmJjEwJjE3c2UkfCZmYWwmMiYxMTAwMTQwJDE3MCYzMSYzNTAwMyYzNS4wLjAwJjAyJjIwJDEwLjAwJiYxMDAwMCR8MjAwLi0wOS0yMDI0ODowMDE4IDEyMDI0OjAwJjIwIDEtMDktOjAwfDg6MDAtMDktMjAyNDY6NTIxNCAxAAAAADoxNwA=", "bizVendorCode": "SD3489", "pStoreCode": "106566", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2NTY2XzQ3MzRfMV8xNzBfMzQwXzE3MF81NzUuMDBfMTcwXzU3NS4wXzBfMF8wLjBfMC4wXzIwMC4wMF8zNS4wMF8wLjAwXzAuMDBfMTgzNzM1NA==", "sendTypeForPickUpCar": 0, "skuId": 1837354, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 19142, "vendorCode": "82461", "vendorVehicleCode": "1837354"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13097", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183709", "skuId": 24660714, "bizVendorCode": "SD7665"}}, {"reference": {"vendorCode": "15001696", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183387", "skuId": 27816545, "bizVendorCode": "SD9178"}}, {"reference": {"vendorCode": "74409", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107591", "skuId": 2457350, "bizVendorCode": "SD4050"}}, {"reference": {"vendorCode": "30116", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106861", "skuId": 1863292, "bizVendorCode": "SD3864"}}, {"reference": {"vendorCode": "13033", "vehicleCode": "0", "packageType": 1, "pStoreCode": "44811", "skuId": 232247, "bizVendorCode": "SD3008"}}, {"reference": {"vendorCode": "61964", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114432", "skuId": 5358996, "bizVendorCode": "SD4396"}}, {"reference": {"vendorCode": "43976", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107858", "skuId": 1887710, "bizVendorCode": "SD4165"}}, {"reference": {"vendorCode": "15000627", "vehicleCode": "0", "packageType": 1, "pStoreCode": "156270", "skuId": 3335090, "bizVendorCode": "SD7370"}}, {"reference": {"vendorCode": "15006082", "vehicleCode": "0", "packageType": 1, "pStoreCode": "765059", "skuId": 45626001, "bizVendorCode": "SD13296"}}, {"reference": {"vendorCode": "85567", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107385", "skuId": 1857848, "bizVendorCode": "SD3740"}}, {"reference": {"vendorCode": "45976", "vehicleCode": "0", "packageType": 1, "pStoreCode": "314056", "skuId": 25278114, "bizVendorCode": "SD3932"}}, {"reference": {"vendorCode": "15000128", "vehicleCode": "0", "packageType": 0, "pStoreCode": "106713", "skuId": 43909188, "bizVendorCode": "SD3167"}}, {"reference": {"vendorCode": "15002717", "vehicleCode": "0", "packageType": 0, "pStoreCode": "193043", "skuId": 6870163, "bizVendorCode": "SD9741"}}, {"reference": {"vendorCode": "77547", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106498", "skuId": 4581945, "bizVendorCode": "SD3386"}}, {"reference": {"vendorCode": "15000161", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107845", "skuId": 5443982, "bizVendorCode": "SD3288"}}, {"reference": {"vendorCode": "15002708", "vehicleCode": "0", "packageType": 1, "pStoreCode": "192988", "skuId": 56463233, "bizVendorCode": "SD9732"}}, {"reference": {"vendorCode": "15000176", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106745", "skuId": 2705399, "bizVendorCode": "SD3547"}}, {"reference": {"vendorCode": "15006315", "vehicleCode": "0", "packageType": 1, "pStoreCode": "830418", "skuId": 49250223, "bizVendorCode": "SD13538"}}, {"reference": {"vendorCode": "81415", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117135", "skuId": 1961814, "bizVendorCode": "SD6788"}}, {"reference": {"vendorCode": "15005216", "vehicleCode": "0", "packageType": 1, "pStoreCode": "426518", "skuId": 31082031, "bizVendorCode": "SD12402"}}, {"reference": {"vendorCode": "30105", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114042", "skuId": 2427952, "bizVendorCode": "SD4198"}}, {"reference": {"vendorCode": "61975", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114320", "skuId": 1914286, "bizVendorCode": "SD4400"}}, {"reference": {"vendorCode": "13027", "vehicleCode": "0", "packageType": 0, "pStoreCode": "66177", "skuId": 8470827, "bizVendorCode": "SD3010"}}, {"reference": {"vendorCode": "85519", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117710", "skuId": 4104414, "bizVendorCode": "SD5684"}}, {"reference": {"vendorCode": "15003233", "vehicleCode": "0", "packageType": 1, "pStoreCode": "228590", "skuId": 30674085, "bizVendorCode": "SD10266"}}, {"reference": {"vendorCode": "30200", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114049", "skuId": 1915556, "bizVendorCode": "SD4205"}}, {"reference": {"vendorCode": "30026", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114037", "skuId": 1910022, "bizVendorCode": "SD4193"}}, {"reference": {"vendorCode": "15003699", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250140", "skuId": 6875976, "bizVendorCode": "SD10753"}}, {"reference": {"vendorCode": "15002528", "vehicleCode": "0", "packageType": 1, "pStoreCode": "189318", "skuId": 5629715, "bizVendorCode": "SD9544"}}, {"reference": {"vendorCode": "15005757", "vehicleCode": "0", "packageType": 0, "pStoreCode": "721421", "skuId": 44230173, "bizVendorCode": "SD12959"}}, {"reference": {"vendorCode": "15003674", "vehicleCode": "0", "packageType": 1, "pStoreCode": "249859", "skuId": 6889777, "bizVendorCode": "SD10728"}}, {"reference": {"vendorCode": "30501", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114065", "skuId": 3348547, "bizVendorCode": "SD4217"}}, {"reference": {"vendorCode": "71841", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114647", "skuId": 1912490, "bizVendorCode": "SD4641"}}, {"reference": {"vendorCode": "15006404", "vehicleCode": "0", "packageType": 0, "pStoreCode": "833498", "skuId": 51802507, "bizVendorCode": "SD13633"}}, {"reference": {"vendorCode": "85545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117715", "skuId": 4350732, "bizVendorCode": "SD5682"}}, {"reference": {"vendorCode": "83886", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115393", "skuId": 1911560, "bizVendorCode": "SD5141"}}, {"reference": {"vendorCode": "15004748", "vehicleCode": "0", "packageType": 0, "pStoreCode": "403418", "skuId": 27087985, "bizVendorCode": "SD11904"}}, {"reference": {"vendorCode": "15004890", "vehicleCode": "0", "packageType": 0, "pStoreCode": "410999", "skuId": 41450473, "bizVendorCode": "SD12066"}}, {"reference": {"vendorCode": "81617", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115131", "skuId": 1957644, "bizVendorCode": "SD4932"}}, {"reference": {"vendorCode": "83185", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107425", "skuId": 30605926, "bizVendorCode": "SD3616"}}], "rCoup": 0, "vehicleCode": "452", "highestPrice": 1500, "pWay": "可选：免费站内取还车", "minDPrice": 170, "vehicleKey": "0_452_沪牌", "hot": 0, "minTPrice": 575, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 23, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3489_0_106566_106566"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 4743, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "165219771", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 41}, {"groupSort": 1, "lowestPrice": 68, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 136, "detail": [{"code": "1001", "amount": 136, "amountDesc": "¥136", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥68", "subAmount": 68, "name": "车辆租金", "amountStr": "¥136"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 216, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥216", "subAmount": 216, "name": "总价", "amountStr": "¥216"}], "reference": {"vehicleCode": "0", "rStoreCode": "410355", "packageId": "sec", "pLev": 1615545, "bizVendorCode": "SD3047", "pStoreCode": "410355", "packageType": 1, "priceVersion": "SH-PRICEVERSION_NDEwMzU1XzQ0OTNfMV82OC4wXzEzNi4wXzAuMF8yMTYuMF82OC4wXzIxNi4wXzBfMF8wLjBfMC4wXzYwLjBfMjAuMF8wXzBfNTE3NzQwNTI=", "sendTypeForPickUpCar": 0, "skuId": 51774052, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1615545, "vendorCode": "15004163", "vendorVehicleCode": "7010_51717_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13088", "vehicleCode": "0", "packageType": 0, "pStoreCode": "150269", "skuId": 24017876, "bizVendorCode": "SD6991"}}, {"reference": {"vendorCode": "15001696", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183387", "skuId": 6408708, "bizVendorCode": "SD9178"}}, {"reference": {"vendorCode": "65434", "vehicleCode": "0", "packageType": 1, "pStoreCode": "252183", "skuId": 28189750, "bizVendorCode": "SD11803"}}, {"reference": {"vendorCode": "15004635", "vehicleCode": "0", "packageType": 1, "pStoreCode": "327097", "skuId": 41483422, "bizVendorCode": "SD11767"}}, {"reference": {"vendorCode": "15005757", "vehicleCode": "0", "packageType": 0, "pStoreCode": "721421", "skuId": 44508206, "bizVendorCode": "SD12959"}}, {"reference": {"vendorCode": "15003672", "vehicleCode": "0", "packageType": 1, "pStoreCode": "249873", "skuId": 6868961, "bizVendorCode": "SD10726"}}, {"reference": {"vendorCode": "15001935", "vehicleCode": "0", "packageType": 1, "pStoreCode": "186872", "skuId": 29783279, "bizVendorCode": "SD8915"}}, {"reference": {"vendorCode": "33528", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181596", "skuId": 45114616, "bizVendorCode": "SD3916"}}, {"reference": {"vendorCode": "15003285", "vehicleCode": "0", "packageType": 1, "pStoreCode": "228863", "skuId": 6827984, "bizVendorCode": "SD10320"}}, {"reference": {"vendorCode": "15000267", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133745", "skuId": 2427402, "bizVendorCode": "SD10647"}}], "rCoup": 0, "vehicleCode": "4493", "highestPrice": 234, "pWay": "可选：免费站内取还车", "minDPrice": 68, "vehicleKey": "0_4493_", "hot": 0, "minTPrice": 216, "lowestDistance": 71.2074, "group": 104493, "type": 0, "groupTypeBits": 1, "sortNum": 24, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3047_0_410355_410355"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 2602, "productRef": {"license": "外牌", "licenseType": 1, "licenseTag": "", "licenseStyle": "6"}, "hotScore": 0, "hotType": 0, "reactId": "165219678", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 11}, {"groupSort": 2, "lowestPrice": 78, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 156, "detail": [{"code": "1001", "amount": 156, "amountDesc": "¥156", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥78", "subAmount": 78, "name": "车辆租金", "amountStr": "¥156"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 291, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥291", "subAmount": 291, "name": "总价", "amountStr": "¥291"}], "reference": {"vehicleCode": "0", "rStoreCode": "1190239", "pLev": 1437129, "comPriceCode": "[c]Mjc2ODUyMTgyNC0wfHwyMCAwMDo5LTE4MCY3ODAwOjBmYWxzJiYxJjI0LTBlJDIwIDAwOjktMTkwJjc4MDA6MGZhbHMmJjEmMDAxJmUkfDEmMTU2MiY3ODMmMSYkMTAwMCYzNTM1LjAxMDAyLjAwJDAuMDAmMiY1LjAwJCYxMDA0LTA5fDIwMjE4OjAtMTggJjIwMjA6MDAtMjAgNC0wOTA6MDAxODowNC0wOXwyMDIxNjo1LTE0IAAAAAAyOjE3", "bizVendorCode": "SD4101", "pStoreCode": "1190239", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTE5MDIzOV80NDkzXzFfNzhfMTU2Xzc4XzI5MS4wMF83OF8yOTEuMF8wXzBfMC4wXzAuMF8xMDAuMDBfMzUuMDBfMC4wMF8wLjAwXzUyMTgyNzY4", "sendTypeForPickUpCar": 0, "skuId": 52182768, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1437129, "vendorCode": "13087", "vendorVehicleCode": "83720_10905_hupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15001696", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183387", "skuId": 6807028, "bizVendorCode": "SD9178"}}, {"reference": {"vendorCode": "13072", "vehicleCode": "0", "packageType": 1, "pStoreCode": "137706", "skuId": 29988463, "bizVendorCode": "SD7089"}}, {"reference": {"vendorCode": "15003821", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251078", "skuId": 46118780, "bizVendorCode": "SD10876"}}, {"reference": {"vendorCode": "15004069", "vehicleCode": "0", "packageType": 1, "pStoreCode": "264608", "skuId": 30036651, "bizVendorCode": "SD11164"}}, {"reference": {"vendorCode": "15003166", "vehicleCode": "0", "packageType": 1, "pStoreCode": "194714", "skuId": 5723511, "bizVendorCode": "SD10197"}}, {"reference": {"vendorCode": "15005216", "vehicleCode": "0", "packageType": 1, "pStoreCode": "426518", "skuId": 40509358, "bizVendorCode": "SD12402"}}, {"reference": {"vendorCode": "15001935", "vehicleCode": "0", "packageType": 1, "pStoreCode": "186872", "skuId": 5642440, "bizVendorCode": "SD8915"}}, {"reference": {"vendorCode": "15004040", "vehicleCode": "0", "packageType": 1, "pStoreCode": "263439", "skuId": 8803397, "bizVendorCode": "SD11133"}}, {"reference": {"vendorCode": "77755", "vehicleCode": "0", "packageType": 0, "pStoreCode": "114601", "skuId": 4014544, "bizVendorCode": "SD4749"}}], "rCoup": 0, "vehicleCode": "4493", "highestPrice": 240, "pWay": "可选：店员免费上门送取车", "minDPrice": 78, "vehicleKey": "0_4493_沪牌", "hot": 0, "minTPrice": 291, "lowestDistance": 12.4016, "group": 104493, "type": 0, "groupTypeBits": 1, "sortNum": 25, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD4101_0_1190239_1190239"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 3621, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "165219679", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 10}, {"groupSort": 0, "lowestPrice": 99, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 236, "detail": [{"code": "1001", "amount": 236, "amountDesc": "¥236", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥118", "subAmount": 118, "name": "车辆租金", "amountStr": "¥236"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 200, "amountStr": "¥200", "detail": [{"code": "1002", "amount": 200, "amountDesc": "¥200", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 466, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥466", "subAmount": 466, "name": "总价", "amountStr": "¥466"}], "reference": {"vehicleCode": "0", "rStoreCode": "228658", "packageId": "sec", "pLev": 996171, "comPriceCode": "[c]MTQzfDgyOTB8MjAyMC4wMC0xOCA0LTA5MDowMDAwOjAmJjEmJjExOGUkMjBmYWxzOS0xOTI0LTAwMDowIDAwOjgmJjEwJjExc2UkfCZmYWwmMiYxMTAwMTM2JDExOCYyMSYzMDAwMyYzMC4wLjAwJjAyJjIwJDEwLjAwJiYxMDAwMCR8MjAwLi0wOS0yMDI0ODowMDE4IDEyMDI0OjAwJjIwIDEtMDktOjAwfDg6MDAtMDktMjAyNDY6NTIxNCAxAAAAADoxNwA=", "bizVendorCode": "SD9798", "pStoreCode": "228658", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MjI4NjU4XzUyNDVfMV8xMThfMjM2XzExOF80NjYuMDBfMTE4XzQ2Ni4wXzBfMF8wLjBfMC4wXzIwMC4wMF8zMC4wMF8wLjAwXzAuMDBfODI5MDE0Mw==", "sendTypeForPickUpCar": 0, "skuId": 8290143, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 996171, "vendorCode": "15002773", "vendorVehicleCode": "4232_54901_hupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13079", "vehicleCode": "0", "packageType": 1, "pStoreCode": "256103", "skuId": 24650445, "bizVendorCode": "SD3200"}}, {"reference": {"vendorCode": "13099", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183715", "skuId": 24658978, "bizVendorCode": "SD7609"}}, {"reference": {"vendorCode": "13072", "vehicleCode": "0", "packageType": 1, "pStoreCode": "137706", "skuId": 55942937, "bizVendorCode": "SD7089"}}, {"reference": {"vendorCode": "15004163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "410355", "skuId": 51777881, "bizVendorCode": "SD3047"}}, {"reference": {"vendorCode": "13087", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1190239", "skuId": 51448433, "bizVendorCode": "SD4101"}}, {"reference": {"vendorCode": "15006082", "vehicleCode": "0", "packageType": 1, "pStoreCode": "765059", "skuId": 44843296, "bizVendorCode": "SD13296"}}, {"reference": {"vendorCode": "15006322", "vehicleCode": "0", "packageType": 1, "pStoreCode": "831601", "skuId": 49481356, "bizVendorCode": "SD13545"}}, {"reference": {"vendorCode": "32421", "vehicleCode": "0", "packageType": 1, "pStoreCode": "327951", "skuId": 29607369, "bizVendorCode": "SD5371"}}, {"reference": {"vendorCode": "83287", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115339", "skuId": 5044005, "bizVendorCode": "SD5096"}}, {"reference": {"vendorCode": "30026", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114037", "skuId": 23690472, "bizVendorCode": "SD4193"}}, {"reference": {"vendorCode": "85545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117715", "skuId": 25307605, "bizVendorCode": "SD5682"}}], "rCoup": 0, "vehicleCode": "5245", "highestPrice": 500, "pWay": "可选：免费站内取还车", "minDPrice": 118, "vehicleKey": "0_5245_沪牌", "hot": 0, "minTPrice": 466, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 26, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD7609_0_183715_183715"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 3621, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0, "hotType": 0, "reactId": "165219672", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 12}, {"groupSort": 0, "lowestPrice": 218, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 436, "detail": [{"code": "1001", "amount": 436, "amountDesc": "¥436", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥218", "subAmount": 218, "name": "车辆租金", "amountStr": "¥436"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 180, "amountStr": "¥180", "detail": [{"code": "1002", "amount": 180, "amountDesc": "¥180", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 636, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥636", "subAmount": 636, "name": "总价", "amountStr": "¥636"}], "reference": {"vehicleCode": "0", "rStoreCode": "547135", "pLev": -1, "bizVendorCode": "SD11466", "pStoreCode": "547135", "packageType": 0, "priceVersion": "SH-PRICEVERSION_NTQ3MTM1XzM3XzFfMjE4LjBfNDM2LjBfMC4wXzYzNi4wXzIxOC4wXzYzNi4wXzBfMF8wLjBfMC4wXzE4MC4wXzIwLjBfMF8wXzQxMjMzOTQ5", "sendTypeForPickUpCar": 0, "skuId": 41233949, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "15004351", "vendorVehicleCode": "1275"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13097", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183709", "skuId": 24660693, "bizVendorCode": "SD7665"}}, {"reference": {"vendorCode": "15001014", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174847", "skuId": 4646628, "bizVendorCode": "SD7971"}}, {"reference": {"vendorCode": "13099", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183715", "skuId": 54551127, "bizVendorCode": "SD7609"}}, {"reference": {"vendorCode": "30116", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106861", "skuId": 1858237, "bizVendorCode": "SD3864"}}, {"reference": {"vendorCode": "61964", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114432", "skuId": 1914019, "bizVendorCode": "SD4396"}}, {"reference": {"vendorCode": "15003636", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250958", "skuId": 41096357, "bizVendorCode": "SD10689"}}, {"reference": {"vendorCode": "15002010", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184453", "skuId": 47748156, "bizVendorCode": "SD8997"}}, {"reference": {"vendorCode": "78883", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106904", "skuId": 2777075, "bizVendorCode": "SD4066"}}, {"reference": {"vendorCode": "13088", "vehicleCode": "0", "packageType": 0, "pStoreCode": "150269", "skuId": 41222658, "bizVendorCode": "SD6991"}}, {"reference": {"vendorCode": "15005181", "vehicleCode": "0", "packageType": 0, "pStoreCode": "424579", "skuId": 30697059, "bizVendorCode": "SD12366"}}, {"reference": {"vendorCode": "85567", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107385", "skuId": 4509690, "bizVendorCode": "SD3740"}}, {"reference": {"vendorCode": "15000161", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107845", "skuId": 5461622, "bizVendorCode": "SD3288"}}, {"reference": {"vendorCode": "15003684", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250029", "skuId": 6871962, "bizVendorCode": "SD10738"}}, {"reference": {"vendorCode": "15006315", "vehicleCode": "0", "packageType": 1, "pStoreCode": "830418", "skuId": 49250076, "bizVendorCode": "SD13538"}}, {"reference": {"vendorCode": "79557", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114888", "skuId": 1920610, "bizVendorCode": "SD4781"}}, {"reference": {"vendorCode": "15003140", "vehicleCode": "0", "packageType": 0, "pStoreCode": "229054", "skuId": 7347635, "bizVendorCode": "SD10170"}}, {"reference": {"vendorCode": "62853", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114463", "skuId": 1924240, "bizVendorCode": "SD4444"}}, {"reference": {"vendorCode": "85729", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107552", "skuId": 23282904, "bizVendorCode": "SD3651"}}, {"reference": {"vendorCode": "85645", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107470", "skuId": 1859750, "bizVendorCode": "SD3703"}}, {"reference": {"vendorCode": "15004210", "vehicleCode": "0", "packageType": 1, "pStoreCode": "285587", "skuId": 35795117, "bizVendorCode": "SD11318"}}, {"reference": {"vendorCode": "61084", "vehicleCode": "0", "packageType": 0, "pStoreCode": "262620", "skuId": 29035273, "bizVendorCode": "SD4380"}}, {"reference": {"vendorCode": "15002251", "vehicleCode": "0", "packageType": 1, "pStoreCode": "187510", "skuId": 42960009, "bizVendorCode": "SD9252"}}, {"reference": {"vendorCode": "15001696", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183387", "skuId": 5443903, "bizVendorCode": "SD9178"}}, {"reference": {"vendorCode": "15006831", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1497525", "skuId": 55814039, "bizVendorCode": "SD14099"}}, {"reference": {"vendorCode": "15004601", "vehicleCode": "0", "packageType": 0, "pStoreCode": "322001", "skuId": 24979389, "bizVendorCode": "SD11733"}}, {"reference": {"vendorCode": "15001663", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183132", "skuId": 30607165, "bizVendorCode": "SD8636"}}, {"reference": {"vendorCode": "30501", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114065", "skuId": 1907628, "bizVendorCode": "SD4217"}}, {"reference": {"vendorCode": "15003201", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229318", "skuId": 42248676, "bizVendorCode": "SD10232"}}, {"reference": {"vendorCode": "70905", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116756", "skuId": 40499439, "bizVendorCode": "SD6554"}}, {"reference": {"vendorCode": "61927", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114321", "skuId": 1918784, "bizVendorCode": "SD4393"}}, {"reference": {"vendorCode": "82219", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117350", "skuId": 1980013, "bizVendorCode": "SD6145"}}, {"reference": {"vendorCode": "63129", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114460", "skuId": 1917285, "bizVendorCode": "SD4454"}}, {"reference": {"vendorCode": "15003233", "vehicleCode": "0", "packageType": 1, "pStoreCode": "228590", "skuId": 6392466, "bizVendorCode": "SD10266"}}, {"reference": {"vendorCode": "84183", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106482", "skuId": 1838065, "bizVendorCode": "SD3481"}}, {"reference": {"vendorCode": "15003350", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229299", "skuId": 6862845, "bizVendorCode": "SD10388"}}, {"reference": {"vendorCode": "83910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106612", "skuId": 28536649, "bizVendorCode": "SD3382"}}, {"reference": {"vendorCode": "15004912", "vehicleCode": "0", "packageType": 1, "pStoreCode": "411867", "skuId": 28502034, "bizVendorCode": "SD12089"}}, {"reference": {"vendorCode": "15002140", "vehicleCode": "0", "packageType": 1, "pStoreCode": "186902", "skuId": 5355869, "bizVendorCode": "SD9137"}}, {"reference": {"vendorCode": "84127", "vehicleCode": "0", "packageType": 0, "pStoreCode": "106467", "skuId": 24982189, "bizVendorCode": "SD3412"}}, {"reference": {"vendorCode": "15000627", "vehicleCode": "0", "packageType": 1, "pStoreCode": "156270", "skuId": 4671780, "bizVendorCode": "SD7370"}}, {"reference": {"vendorCode": "15003699", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250140", "skuId": 6891283, "bizVendorCode": "SD10753"}}, {"reference": {"vendorCode": "30026", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114037", "skuId": 1917209, "bizVendorCode": "SD4193"}}, {"reference": {"vendorCode": "15004921", "vehicleCode": "0", "packageType": 1, "pStoreCode": "411531", "skuId": 28536355, "bizVendorCode": "SD12099"}}, {"reference": {"vendorCode": "13027", "vehicleCode": "0", "packageType": 0, "pStoreCode": "421492", "skuId": 51442665, "bizVendorCode": "SD3010"}}, {"reference": {"vendorCode": "67288", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116394", "skuId": 1962808, "bizVendorCode": "SD6498"}}, {"reference": {"vendorCode": "15004621", "vehicleCode": "0", "packageType": 0, "pStoreCode": "322505", "skuId": 24850071, "bizVendorCode": "SD11753"}}, {"reference": {"vendorCode": "71841", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114647", "skuId": 1907625, "bizVendorCode": "SD4641"}}, {"reference": {"vendorCode": "15003173", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229252", "skuId": 6803708, "bizVendorCode": "SD10204"}}, {"reference": {"vendorCode": "15002847", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193348", "skuId": 5672530, "bizVendorCode": "SD9871"}}, {"reference": {"vendorCode": "70590", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114810", "skuId": 1924117, "bizVendorCode": "SD4620"}}, {"reference": {"vendorCode": "15002528", "vehicleCode": "0", "packageType": 1, "pStoreCode": "189318", "skuId": 24576644, "bizVendorCode": "SD9544"}}, {"reference": {"vendorCode": "15002541", "vehicleCode": "0", "packageType": 1, "pStoreCode": "189195", "skuId": 5581086, "bizVendorCode": "SD9559"}}, {"reference": {"vendorCode": "85545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117715", "skuId": 1960482, "bizVendorCode": "SD5682"}}, {"reference": {"vendorCode": "83101", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117397", "skuId": 28509237, "bizVendorCode": "SD5598"}}, {"reference": {"vendorCode": "81617", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115131", "skuId": 1911026, "bizVendorCode": "SD4932"}}], "rCoup": 0, "vehicleCode": "37", "highestPrice": 1399, "pWay": "可选：免费站内取还车", "minDPrice": 218, "vehicleKey": "0_37_沪牌", "hot": 0, "minTPrice": 636, "lowestDistance": 24.8598, "group": 0, "type": 0, "sortNum": 27, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11466_0_547135_547135"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 4743, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0.16010381, "hotType": 0, "reactId": "165219772", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 56}, {"groupSort": 0, "lowestPrice": 190, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 380, "detail": [{"code": "1001", "amount": 380, "amountDesc": "¥380", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥190", "subAmount": 190, "name": "车辆租金", "amountStr": "¥380"}, {"code": "CAR_SERVICE_FEE", "amount": 85, "amountStr": "¥85", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11026", "amount": 25, "amountDesc": "¥25", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 25, "amountDesc": "¥25", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 200, "amountStr": "¥200", "detail": [{"code": "1002", "amount": 200, "amountDesc": "¥200", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 665, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥665", "subAmount": 665, "name": "总价", "amountStr": "¥665"}], "reference": {"vehicleCode": "0", "rStoreCode": "184186", "packageId": "sec", "pLev": 158595, "comPriceCode": "[c]OTk0MTI0NjQwfDIwfDAuMDktMTgyNC0wMDA6MCAwMDowJiYxMCYxOXNlJDImZmFsMDktMTAyNC06MDA6OSAwMDkwJiYwMCYxbHNlJDEmZmExJjImfDEwMDM4MCQxOTAmJjEmMzEwMDMmMzUuNS4wMDAwMiYwMCQxMC4wMDImMTAuMDAkJjIwMDQtMDl8MjAyMTg6MC0xOCAmMjAyMDowMC0yMCA0LTA5MDowMDE4OjA0LTA5fDIwMjE2OjUtMTQgAAAAADI6MTc=", "bizVendorCode": "SD3200", "pStoreCode": "184186", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTg0MTg2XzQ1Nl8xXzE5MF8zODBfMTkwXzY2NS4wMF8xOTBfNjY1LjBfMV8xXzAuMF8wLjBfMjAwLjAwXzM1LjAwXzI1LjAwXzI1LjAwXzI0NjQ5OTQx", "sendTypeForPickUpCar": 0, "skuId": 24649941, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 158595, "vendorCode": "13079", "vendorVehicleCode": "2829_11999_hupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13088", "vehicleCode": "0", "packageType": 0, "pStoreCode": "150269", "skuId": 14240668, "bizVendorCode": "SD6991"}}, {"reference": {"vendorCode": "15001108", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175256", "skuId": 6870332, "bizVendorCode": "SD8066"}}, {"reference": {"vendorCode": "43976", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107858", "skuId": 1887688, "bizVendorCode": "SD4165"}}, {"reference": {"vendorCode": "13033", "vehicleCode": "0", "packageType": 1, "pStoreCode": "44811", "skuId": 232230, "bizVendorCode": "SD3008"}}, {"reference": {"vendorCode": "30116", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106861", "skuId": 1863316, "bizVendorCode": "SD3864"}}, {"reference": {"vendorCode": "15000893", "vehicleCode": "0", "packageType": 1, "pStoreCode": "164760", "skuId": 4022886, "bizVendorCode": "SD7636"}}, {"reference": {"vendorCode": "85565", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107379", "skuId": 1859087, "bizVendorCode": "SD3825"}}, {"reference": {"vendorCode": "15006831", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1497525", "skuId": 57001253, "bizVendorCode": "SD14099"}}, {"reference": {"vendorCode": "15000959", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174254", "skuId": 4342188, "bizVendorCode": "SD7702"}}, {"reference": {"vendorCode": "15004601", "vehicleCode": "0", "packageType": 0, "pStoreCode": "322001", "skuId": 44709379, "bizVendorCode": "SD11733"}}, {"reference": {"vendorCode": "85729", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107552", "skuId": 3775710, "bizVendorCode": "SD3651"}}, {"reference": {"vendorCode": "15003684", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250029", "skuId": 23916257, "bizVendorCode": "SD10738"}}, {"reference": {"vendorCode": "30200", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114049", "skuId": 5002523, "bizVendorCode": "SD4205"}}, {"reference": {"vendorCode": "66989", "vehicleCode": "0", "packageType": 1, "pStoreCode": "425720", "skuId": 51784062, "bizVendorCode": "SD7876"}}, {"reference": {"vendorCode": "81883", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107099", "skuId": 1861362, "bizVendorCode": "SD4112"}}, {"reference": {"vendorCode": "73085", "vehicleCode": "0", "packageType": 0, "pStoreCode": "114666", "skuId": 27771024, "bizVendorCode": "SD4668"}}, {"reference": {"vendorCode": "61920", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114323", "skuId": 1912060, "bizVendorCode": "SD4392"}}, {"reference": {"vendorCode": "30501", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114065", "skuId": 1910964, "bizVendorCode": "SD4217"}}, {"reference": {"vendorCode": "32670", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114159", "skuId": 1921146, "bizVendorCode": "SD4283"}}, {"reference": {"vendorCode": "15001113", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175168", "skuId": 4865704, "bizVendorCode": "SD8071"}}, {"reference": {"vendorCode": "15003173", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229252", "skuId": 6806427, "bizVendorCode": "SD10204"}}, {"reference": {"vendorCode": "83910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106612", "skuId": 27826114, "bizVendorCode": "SD3382"}}, {"reference": {"vendorCode": "15003350", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229299", "skuId": 6862620, "bizVendorCode": "SD10388"}}, {"reference": {"vendorCode": "31064", "vehicleCode": "0", "packageType": 0, "pStoreCode": "115877", "skuId": 3723134, "bizVendorCode": "SD5360"}}, {"reference": {"vendorCode": "13027", "vehicleCode": "0", "packageType": 0, "pStoreCode": "100775", "skuId": 1214016, "bizVendorCode": "SD3010"}}, {"reference": {"vendorCode": "30026", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114037", "skuId": 1909999, "bizVendorCode": "SD4193"}}, {"reference": {"vendorCode": "15004921", "vehicleCode": "0", "packageType": 0, "pStoreCode": "411531", "skuId": 28536404, "bizVendorCode": "SD12099"}}, {"reference": {"vendorCode": "72371", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106478", "skuId": 1838220, "bizVendorCode": "SD3380"}}, {"reference": {"vendorCode": "15001502", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182481", "skuId": 4924030, "bizVendorCode": "SD8468"}}, {"reference": {"vendorCode": "63129", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114460", "skuId": 4165720, "bizVendorCode": "SD4454"}}, {"reference": {"vendorCode": "15002251", "vehicleCode": "0", "packageType": 0, "pStoreCode": "187510", "skuId": 5313979, "bizVendorCode": "SD9252"}}, {"reference": {"vendorCode": "81617", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115131", "skuId": 1908581, "bizVendorCode": "SD4932"}}, {"reference": {"vendorCode": "15004748", "vehicleCode": "0", "packageType": 1, "pStoreCode": "403418", "skuId": 26993604, "bizVendorCode": "SD11904"}}, {"reference": {"vendorCode": "70590", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114810", "skuId": 1908000, "bizVendorCode": "SD4620"}}, {"reference": {"vendorCode": "79613", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106505", "skuId": 1837738, "bizVendorCode": "SD3457"}}, {"reference": {"vendorCode": "84127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106467", "skuId": 1838857, "bizVendorCode": "SD3412"}}, {"reference": {"vendorCode": "15002859", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193412", "skuId": 5644690, "bizVendorCode": "SD9883"}}, {"reference": {"vendorCode": "15002847", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193348", "skuId": 5672533, "bizVendorCode": "SD9871"}}, {"reference": {"vendorCode": "15000541", "vehicleCode": "0", "packageType": 1, "pStoreCode": "153109", "skuId": 4158520, "bizVendorCode": "SD7283"}}, {"reference": {"vendorCode": "85545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117715", "skuId": 1963816, "bizVendorCode": "SD5682"}}, {"reference": {"vendorCode": "66487", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114346", "skuId": 1918317, "bizVendorCode": "SD4513"}}], "rCoup": 0, "vehicleCode": "456", "highestPrice": 1588, "pWay": "可选：免费站内取还车", "minDPrice": 190, "vehicleKey": "0_456_沪牌", "hot": 0, "minTPrice": 665, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 28, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3200_0_184186_184186"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 4743, "productRef": {"license": "沪牌", "licenseType": 0, "licenseTag": "沪牌", "licenseStyle": "2"}, "hotScore": 0.22282028, "hotType": 0, "reactId": "165219773", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 42}], "groupCode": "all", "dailyPrice": 40, "hasResult": true}, {"sortNum": 0, "groupName": "无忧租一口价", "hasResult": true, "groupCode": "prep", "dailyPrice": 218, "groupAction": 1}, {"sortNum": 1, "groupName": "经济轿车", "hasResult": true, "groupCode": "2", "dailyPrice": 68, "groupAction": 0}, {"sortNum": 3, "groupName": "新能源", "hasResult": true, "groupCode": "newenergy", "dailyPrice": 68, "groupAction": 0}, {"sortNum": 4, "groupName": "舒适轿车", "hasResult": true, "groupCode": "3", "dailyPrice": 78, "groupAction": 0}, {"sortNum": 5, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 40, "groupAction": 0}, {"sortNum": 6, "groupName": "商务车", "hasResult": true, "groupCode": "4", "dailyPrice": 119, "groupAction": 0}, {"sortNum": 7, "groupName": "豪华轿车", "hasResult": true, "groupCode": "5", "dailyPrice": 178, "groupAction": 0}, {"sortNum": 8, "groupName": "跑车", "hasResult": true, "groupCode": "9", "dailyPrice": 158, "groupAction": 0}, {"sortNum": 9, "groupName": "房车", "hasResult": true, "groupCode": "11", "dailyPrice": 564, "groupAction": 0}, {"sortNum": 10, "groupName": "小巴士", "hasResult": true, "groupCode": "7", "dailyPrice": 100, "groupAction": 0}, {"sortNum": 11, "groupName": "皮卡", "hasResult": true, "groupCode": "10", "dailyPrice": 199, "groupAction": 0}], "resBodySize": 129284, "promotMap": {}, "requestInfo": {"rLongitude": 121.346814, "rDate": "20240920180000", "age": 30, "pCityId": 2, "returnDate": "/Date(1726826400000+0800)/", "sourceCountryId": 1, "pLatitude": 31.194964, "rLatitude": 31.194964, "pLongitude": 121.346814, "pDate": "20240918180000", "rCityId": 2, "pickupLocationName": "虹桥国际机场T1航站楼-P1停车库扶梯口", "returnLocationName": "虹桥国际机场T1航站楼-P1停车库扶梯口", "pickupDate": "/Date(1726653600000+0800)/"}, "productGroupCodeUesd": "all", "commNotices": [], "hasResultWithoutFilter": true, "allVehicleCount": 703, "rentCenter": {"images": ["https://dimg04.c-ctrip.com/images/0yc4r12000e95lnyn6A18.jpg", "https://dimg04.c-ctrip.com/images/0yc4a12000e95lyoo0BDF.jpg"], "isNew": 1, "address": "虹桥火车站综合交通枢纽西出口1号柜台", "id": 148, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "22:59", "lat": "31.194106", "lng": "121.320666", "name": "虹桥火车站租车中心", "fromTime": "00:00"}, "checkResponseTime": 1726304064484.436, "pHub": 1, "storeList": [{"pickOffLevel": 123822, "storeCode": "117710", "pickUpLevel": 123822}, {"pickOffLevel": 107001, "storeCode": "174254", "pickUpLevel": 107001}, {"pickOffLevel": 1268849, "storeCode": "721421", "pickUpLevel": 1268849}, {"pickOffLevel": 1, "storeCode": "421492", "pickUpLevel": 1}, {"pickOffLevel": 1336903, "storeCode": "765059", "pickUpLevel": 1336903}, {"pickOffLevel": 38591, "storeCode": "114323", "pickUpLevel": 38591}, {"pickOffLevel": 1247331, "storeCode": "410999", "pickUpLevel": 1247331}, {"pickOffLevel": 59195, "storeCode": "106904", "pickUpLevel": 59195}, {"pickOffLevel": 773200, "storeCode": "322001", "pickUpLevel": 773200}, {"pickOffLevel": 563886, "storeCode": "274548", "pickUpLevel": 563886}, {"pickOffLevel": 1030940, "storeCode": "327097", "pickUpLevel": 1030940}, {"pickOffLevel": 98222, "storeCode": "115339", "pickUpLevel": 98222}, {"pickOffLevel": 4322, "storeCode": "228658", "pickUpLevel": 4322}, {"pickOffLevel": 221447, "storeCode": "137302", "pickUpLevel": 221447}, {"pickOffLevel": 1254303, "storeCode": "624849", "pickUpLevel": 1254303}, {"pickOffLevel": 46559, "storeCode": "114666", "pickUpLevel": 46559}, {"pickOffLevel": 30557, "storeCode": "106745", "pickUpLevel": 30557}, {"pickOffLevel": 733020, "storeCode": "194714", "pickUpLevel": 733020}, {"pickOffLevel": 55544, "storeCode": "115877", "pickUpLevel": 55544}, {"pickOffLevel": 11226, "storeCode": "1190239", "pickUpLevel": 11226}, {"pickOffLevel": 219, "storeCode": "183715", "pickUpLevel": 219}, {"pickOffLevel": 193820, "storeCode": "193437", "pickUpLevel": 193820}, {"pickOffLevel": 28274, "storeCode": "107470", "pickUpLevel": 28274}, {"pickOffLevel": 19455, "storeCode": "106482", "pickUpLevel": 19455}, {"pickOffLevel": 18988, "storeCode": "106478", "pickUpLevel": 18988}, {"pickOffLevel": 1256655, "storeCode": "107219", "pickUpLevel": 1256655}, {"pickOffLevel": 1172627, "storeCode": "106713", "pickUpLevel": 1172627}, {"pickOffLevel": 1159992, "storeCode": "425720", "pickUpLevel": 1159992}, {"pickOffLevel": 2549, "storeCode": "256103", "pickUpLevel": 2549}, {"pickOffLevel": 16923, "storeCode": "1443765", "pickUpLevel": 16923}, {"pickOffLevel": 1000508, "storeCode": "44811", "pickUpLevel": 1000508}, {"pickOffLevel": 1375053, "storeCode": "187510", "pickUpLevel": 1375053}, {"pickOffLevel": 19028, "storeCode": "106505", "pickUpLevel": 19028}, {"pickOffLevel": 440532, "storeCode": "253457", "pickUpLevel": 440532}, {"pickOffLevel": 579909, "storeCode": "285587", "pickUpLevel": 579909}, {"pickOffLevel": 1573475, "storeCode": "249859", "pickUpLevel": 1573475}, {"pickOffLevel": 224109, "storeCode": "229252", "pickUpLevel": 224109}, {"pickOffLevel": 86192, "storeCode": "107099", "pickUpLevel": 86192}, {"pickOffLevel": 60703, "storeCode": "116679", "pickUpLevel": 60703}, {"pickOffLevel": 59629, "storeCode": "114366", "pickUpLevel": 59629}, {"pickOffLevel": 113546, "storeCode": "114888", "pickUpLevel": 113546}, {"pickOffLevel": 361243, "storeCode": "251078", "pickUpLevel": 361243}, {"pickOffLevel": 45301, "storeCode": "114346", "pickUpLevel": 45301}, {"pickOffLevel": 63621, "storeCode": "117715", "pickUpLevel": 63621}, {"pickOffLevel": 669236, "storeCode": "164760", "pickUpLevel": 669236}, {"pickOffLevel": 105858, "storeCode": "106628", "pickUpLevel": 105858}, {"pickOffLevel": 41176, "storeCode": "114065", "pickUpLevel": 41176}, {"pickOffLevel": 67274, "storeCode": "114049", "pickUpLevel": 67274}, {"pickOffLevel": 88926, "storeCode": "153109", "pickUpLevel": 88926}, {"pickOffLevel": 1198996, "storeCode": "532043", "pickUpLevel": 1198996}, {"pickOffLevel": 1428610, "storeCode": "833498", "pickUpLevel": 1428610}, {"pickOffLevel": 508467, "storeCode": "115366", "pickUpLevel": 508467}, {"pickOffLevel": 46381, "storeCode": "114647", "pickUpLevel": 46381}, {"pickOffLevel": 91563, "storeCode": "116394", "pickUpLevel": 91563}, {"pickOffLevel": 747174, "storeCode": "106704", "pickUpLevel": 747174}, {"pickOffLevel": 192895, "storeCode": "193412", "pickUpLevel": 192895}, {"pickOffLevel": 1395038, "storeCode": "830418", "pickUpLevel": 1395038}, {"pickOffLevel": 1030919, "storeCode": "133745", "pickUpLevel": 1030919}, {"pickOffLevel": 19392, "storeCode": "106467", "pickUpLevel": 19392}, {"pickOffLevel": 221880, "storeCode": "228863", "pickUpLevel": 221880}, {"pickOffLevel": 28331, "storeCode": "107552", "pickUpLevel": 28331}, {"pickOffLevel": 184508, "storeCode": "192890", "pickUpLevel": 184508}, {"pickOffLevel": 444249, "storeCode": "228590", "pickUpLevel": 444249}, {"pickOffLevel": 715884, "storeCode": "183132", "pickUpLevel": 715884}, {"pickOffLevel": 516447, "storeCode": "183387", "pickUpLevel": 516447}, {"pickOffLevel": 1458472, "storeCode": "411867", "pickUpLevel": 1458472}, {"pickOffLevel": 1597723, "storeCode": "250958", "pickUpLevel": 1597723}, {"pickOffLevel": 92430, "storeCode": "114463", "pickUpLevel": 92430}, {"pickOffLevel": 186593, "storeCode": "117350", "pickUpLevel": 186593}, {"pickOffLevel": 582772, "storeCode": "264608", "pickUpLevel": 582772}, {"pickOffLevel": 555, "storeCode": "184419", "pickUpLevel": 555}, {"pickOffLevel": -1, "storeCode": "150269", "pickUpLevel": -1}, {"pickOffLevel": 235434, "storeCode": "193348", "pickUpLevel": 235434}, {"pickOffLevel": -1, "storeCode": "139933", "pickUpLevel": -1}, {"pickOffLevel": 43249, "storeCode": "114298", "pickUpLevel": 43249}, {"pickOffLevel": 47459, "storeCode": "114037", "pickUpLevel": 47459}, {"pickOffLevel": 1405888, "storeCode": "831601", "pickUpLevel": 1405888}, {"pickOffLevel": 1183743, "storeCode": "252183", "pickUpLevel": 283039}, {"pickOffLevel": 226474, "storeCode": "106861", "pickUpLevel": 226474}, {"pickOffLevel": 28232, "storeCode": "107379", "pickUpLevel": 28232}, {"pickOffLevel": 19220, "storeCode": "106612", "pickUpLevel": 19220}, {"pickOffLevel": 587826, "storeCode": "262620", "pickUpLevel": 587826}, {"pickOffLevel": 77340, "storeCode": "116756", "pickUpLevel": 77340}, {"pickOffLevel": 54241, "storeCode": "107845", "pickUpLevel": 54241}, {"pickOffLevel": 64668, "storeCode": "117800", "pickUpLevel": 64668}, {"pickOffLevel": 6, "storeCode": "137706", "pickUpLevel": 6}, {"pickOffLevel": 78573, "storeCode": "107094", "pickUpLevel": 78573}, {"pickOffLevel": 50719, "storeCode": "115393", "pickUpLevel": 50719}, {"pickOffLevel": 1038185, "storeCode": "314056", "pickUpLevel": 1038185}, {"pickOffLevel": 94972, "storeCode": "156270", "pickUpLevel": 94972}, {"pickOffLevel": 1394646, "storeCode": "263439", "pickUpLevel": 1394646}, {"pickOffLevel": 766998, "storeCode": "322505", "pickUpLevel": 766998}, {"pickOffLevel": 1317499, "storeCode": "115174", "pickUpLevel": 1317499}, {"pickOffLevel": 43376, "storeCode": "114321", "pickUpLevel": 43376}, {"pickOffLevel": 62377, "storeCode": "117242", "pickUpLevel": 62377}, {"pickOffLevel": 2527, "storeCode": "175256", "pickUpLevel": 2527}, {"pickOffLevel": 79532, "storeCode": "136750", "pickUpLevel": 79532}, {"pickOffLevel": 17867, "storeCode": "1544138", "pickUpLevel": 17867}, {"pickOffLevel": 250, "storeCode": "183709", "pickUpLevel": 250}, {"pickOffLevel": 63076, "storeCode": "117397", "pickUpLevel": 63076}, {"pickOffLevel": 1, "storeCode": "45784", "pickUpLevel": 1}, {"pickOffLevel": -1, "storeCode": "547135", "pickUpLevel": -1}, {"pickOffLevel": 70156, "storeCode": "106480", "pickUpLevel": 70156}, {"pickOffLevel": 111522, "storeCode": "174557", "pickUpLevel": 111522}, {"pickOffLevel": 1543998, "storeCode": "1497525", "pickUpLevel": 1543998}, {"pickOffLevel": 502062, "storeCode": "250029", "pickUpLevel": 502062}, {"pickOffLevel": 7, "storeCode": "66177", "pickUpLevel": 7}, {"pickOffLevel": 87397, "storeCode": "153053", "pickUpLevel": 87397}, {"pickOffLevel": 1172977, "storeCode": "426518", "pickUpLevel": 1172977}, {"pickOffLevel": 570, "storeCode": "184175", "pickUpLevel": 570}, {"pickOffLevel": 85899, "storeCode": "114460", "pickUpLevel": 85899}, {"pickOffLevel": 216839, "storeCode": "107798", "pickUpLevel": 216839}, {"pickOffLevel": 1206920, "storeCode": "229318", "pickUpLevel": 1206920}, {"pickOffLevel": 1317240, "storeCode": "188504", "pickUpLevel": 1317240}, {"pickOffLevel": 598557, "storeCode": "114432", "pickUpLevel": 598557}, {"pickOffLevel": 95876, "storeCode": "107547", "pickUpLevel": 95876}, {"pickOffLevel": 1568442, "storeCode": "1511609", "pickUpLevel": 1568442}, {"pickOffLevel": 260376, "storeCode": "249873", "pickUpLevel": 253609}, {"pickOffLevel": 47457, "storeCode": "114042", "pickUpLevel": 47457}, {"pickOffLevel": 128349, "storeCode": "107109", "pickUpLevel": 128349}, {"pickOffLevel": 1433510, "storeCode": "411531", "pickUpLevel": 1433510}, {"pickOffLevel": 163372, "storeCode": "186902", "pickUpLevel": 163372}, {"pickOffLevel": 260746, "storeCode": "250140", "pickUpLevel": 260746}, {"pickOffLevel": 71665, "storeCode": "133589", "pickUpLevel": 71665}, {"pickOffLevel": 1281225, "storeCode": "153013", "pickUpLevel": 1281225}, {"pickOffLevel": 25964, "storeCode": "107591", "pickUpLevel": 25964}, {"pickOffLevel": 183926, "storeCode": "189195", "pickUpLevel": 183926}, {"pickOffLevel": 195477, "storeCode": "175168", "pickUpLevel": 195477}, {"pickOffLevel": 1562359, "storeCode": "107858", "pickUpLevel": 1562359}, {"pickOffLevel": 27375, "storeCode": "107425", "pickUpLevel": 27375}, {"pickOffLevel": 843242, "storeCode": "327951", "pickUpLevel": 843242}, {"pickOffLevel": 56351, "storeCode": "114601", "pickUpLevel": 56351}, {"pickOffLevel": 170330, "storeCode": "182481", "pickUpLevel": 170330}, {"pickOffLevel": 1011984, "storeCode": "186872", "pickUpLevel": 1011984}, {"pickOffLevel": 132583, "storeCode": "117737", "pickUpLevel": 132583}, {"pickOffLevel": 25707, "storeCode": "107401", "pickUpLevel": 25707}, {"pickOffLevel": 5, "storeCode": "100775", "pickUpLevel": 5}, {"pickOffLevel": 185740, "storeCode": "192988", "pickUpLevel": 185740}, {"pickOffLevel": 43430, "storeCode": "114320", "pickUpLevel": 43430}, {"pickOffLevel": 1058366, "storeCode": "106650", "pickUpLevel": 1058366}, {"pickOffLevel": 1606284, "storeCode": "789818", "pickUpLevel": 1606284}, {"pickOffLevel": 1570745, "storeCode": "274443", "pickUpLevel": 1570745}, {"pickOffLevel": 422934, "storeCode": "117135", "pickUpLevel": 422934}, {"pickOffLevel": 252871, "storeCode": "248964", "pickUpLevel": 252871}, {"pickOffLevel": 81289, "storeCode": "114159", "pickUpLevel": 81289}, {"pickOffLevel": 155399, "storeCode": "184453", "pickUpLevel": 155399}, {"pickOffLevel": 1210350, "storeCode": "174847", "pickUpLevel": 1210350}, {"pickOffLevel": 1042896, "storeCode": "189318", "pickUpLevel": 1042896}, {"pickOffLevel": 116649, "storeCode": "115131", "pickUpLevel": 116649}, {"pickOffLevel": 221721, "storeCode": "229102", "pickUpLevel": 221721}, {"pickOffLevel": 1447552, "storeCode": "181596", "pickUpLevel": 1447552}, {"pickOffLevel": 19013, "storeCode": "106498", "pickUpLevel": 19013}, {"pickOffLevel": 7982, "storeCode": "410355", "pickUpLevel": 7982}, {"pickOffLevel": 750, "storeCode": "184186", "pickUpLevel": 750}, {"pickOffLevel": 46179, "storeCode": "114810", "pickUpLevel": 46179}, {"pickOffLevel": 312551, "storeCode": "229054", "pickUpLevel": 312551}, {"pickOffLevel": 22258, "storeCode": "106855", "pickUpLevel": 22258}, {"pickOffLevel": 536005, "storeCode": "229299", "pickUpLevel": 536005}, {"pickOffLevel": 262804, "storeCode": "188716", "pickUpLevel": 262804}, {"pickOffLevel": 28237, "storeCode": "107385", "pickUpLevel": 28237}, {"pickOffLevel": 1267575, "storeCode": "107128", "pickUpLevel": 1267575}, {"pickOffLevel": 186591, "storeCode": "193043", "pickUpLevel": 186591}, {"pickOffLevel": 19142, "storeCode": "106566", "pickUpLevel": 19142}, {"pickOffLevel": 901734, "storeCode": "187445", "pickUpLevel": 901734}, {"pickOffLevel": 1560875, "storeCode": "1535381", "pickUpLevel": 1560875}, {"pickOffLevel": 924239, "storeCode": "403418", "pickUpLevel": 1179781}, {"pickOffLevel": 1390222, "storeCode": "424579", "pickUpLevel": 1390222}], "vehicleList": [{"skylight": "支持", "vehicleKey": "0_166_沪牌", "luggageNo": 2, "carPlay": "部分车辆支持CarPlay/CarLife", "displacement": "2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV3e12000c553dzv44F2.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "5", "zhName": "宝马3系", "doorNo": 4, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置四驱/前置后驱", "chargeInterface": "部分车辆支持USB/Type-C/AUX", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=1365&app_ver=10.5", "carPhone": true, "vehicleCode": "166", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "宝马3系", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2e12000b5jo8l95870.jpg", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV4u12000c56dykfE74B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5312000c56ehaw6002.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV7412000c56dw2qBC75.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4b12000c56eea1696D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1j12000c56e77x2797.jpg?mark=yiche"], "transmissionType": 1, "brandName": "宝马", "oilType": 3, "struct": "三厢车", "groupName": "豪华轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "宝马", "licenseStyle": "2", "vehiclesSetId": "98", "guidSys": "部分车辆支持定速巡航/自适应巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_18066_沪牌", "luggageNo": 5, "carPlay": "不支持", "displacement": "1.5T-2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5n12000e3iruz6C691.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "6", "zhName": "红旗HS3", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置四驱/前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [], "carPhone": true, "vehicleCode": "18066", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "红旗HS3", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0r12000cdl18hmDEDF.png?mark=yiche", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0612000cdl19v1C505.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5212000cdl1cl22C0D.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2k12000cdl1bgqD743.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2d12000cdl1dx6677A.png?mark=yiche"], "transmissionType": 1, "brandName": "红旗", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "红旗", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "支持全速自适应巡航/定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_4482_沪牌", "luggageNo": 2, "carPlay": "部分车辆支持CarPlay/CarLife", "displacement": "1.3T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV3212000c8kt8xb3971.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "3", "zhName": "奔驰A级", "doorNo": 4, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=1683&app_ver=10.5", "carPhone": true, "vehicleCode": "4482", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "奔驰A级", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6h12000b5teooiDE10.jpg", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV4w12000c5ltzsj3756.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3e12000c5lu9ayF037.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3l12000c5lu3dt2C9A.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5k12000c5lu2v5F5F4.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0h12000c5ltzsp21C7.jpg?mark=yiche"], "transmissionType": 1, "brandName": "奔驰", "oilType": 3, "struct": "三厢车", "groupName": "舒适轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "奔驰", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "88", "guidSys": "支持定速巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_1126_", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife/原厂互联/映射", "displacement": "1.2T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5112000etfnecb8943.png?mark=yiche"], "license": "外牌", "isSpecialized": true, "groupCode": "2", "zhName": "大众宝来", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3881&app_ver=10.5", "vehicleCode": "1126", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "大众宝来", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV5112000etfnecb8943.png?mark=yiche", "fuel": "95号或92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1112000cefyx2j97EF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3l12000cefz3k55D8E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1812000cefyyfjA95B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6q12000cefyyll3608.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3012000cefyyfn6F9F.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "6", "vehiclesSetId": "70", "guidSys": "支持定速巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_1126_沪牌", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife/原厂互联/映射", "displacement": "1.2T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5112000etfnecb8943.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "2", "zhName": "大众宝来", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3881&app_ver=10.5", "vehicleCode": "1126", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "大众宝来", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV5112000etfnecb8943.png?mark=yiche", "fuel": "95号或92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1112000cefyx2j97EF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3l12000cefz3k55D8E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1812000cefyyfjA95B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6q12000cefyyll3608.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3012000cefyyfn6F9F.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "vehiclesSetId": "70", "guidSys": "支持定速巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5349_", "luggageNo": 2, "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "displacement": "1.2T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5p12000c55ltwq3DF6.png?mark=yiche"], "license": "外牌", "isSpecialized": true, "groupCode": "2", "zhName": "丰田雷凌", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [2], "carPhone": true, "vehicleCode": "5349", "recommendLabels": [{"title": "", "subTitle": "百公里常规油耗6L"}], "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田雷凌", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV7312000b5v2nt01A20.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1p12000c6dj1p6A208.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4m12000c6dj1ih509C.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4m12000c6dj3cz87FB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0j12000c6dj7yg6A16.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5o12000c6diyzeA24C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "6", "autoBackUp": true, "vehiclesSetId": "70", "guidSys": "部分车辆支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5349_沪牌", "luggageNo": 2, "carPlay": "部分车辆支持CarLife/CarPlay/HUAWEIHiCar", "displacement": "1.2T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5p12000c55ltwq3DF6.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "2", "zhName": "丰田雷凌", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [2], "carPhone": true, "vehicleCode": "5349", "recommendLabels": [{"title": "", "subTitle": "百公里常规油耗6L"}], "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "丰田雷凌", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV7312000b5v2nt01A20.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1p12000c6dj1p6A208.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4m12000c6dj1ih509C.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4m12000c6dj3cz87FB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0j12000c6dj7yg6A16.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5o12000c6diyzeA24C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "70", "guidSys": "部分车辆支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5591_沪牌", "luggageNo": 2, "carPlay": "部分车辆支持原厂互联/映射", "displacement": "1.5T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5b12000eti2t1z5330.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "4", "zhName": "上汽大通MAXUS G50（21款-23款）", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7257&app_ver=10.5", "vehicleCode": "5591", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "上汽大通MAXUS G50（21款-23款）", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV6i12000b63x6xsA6CE.jpg", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0912000ciarxw8111F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5h12000ciarzw68DE7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5812000ciarrfv603F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2y12000ciarw7b53D3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5w12000ciarxjeD9B0.jpg?mark=yiche"], "transmissionType": 1, "brandName": "上汽大通MAXUS", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "上汽大通MAXUS", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "48", "guidSys": "部分车辆支持定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_4883_", "luggageNo": 0, "carPlay": "部分车辆支持CarPlay", "displacement": "1.4T-1.5T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5012000c5652xr0F41.png?mark=yiche"], "license": "外牌", "isSpecialized": true, "groupCode": "6", "zhName": "奥迪Q2L", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5787&app_ver=10.5", "vehicleCode": "4883", "recommendLabels": [{"title": "", "subTitle": "操控灵活"}], "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "奥迪Q2L", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2k12000d5pk830A654.png", "fuel": "95号", "passengerNo": 5, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV4j12000cduvnhb63DF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2q12000cduw3po6DE9.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3212000cduvrpp349A.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2s12000cduw24j7A43.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4312000cduvr55FA17.jpg?mark=yiche"], "transmissionType": 1, "brandName": "奥迪", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "奥迪", "licenseStyle": "6", "vehiclesSetId": "10", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_4883_沪牌", "luggageNo": 0, "carPlay": "部分车辆支持CarPlay", "displacement": "1.4T-1.5T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5012000c5652xr0F41.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "6", "zhName": "奥迪Q2L", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5787&app_ver=10.5", "vehicleCode": "4883", "recommendLabels": [{"title": "", "subTitle": "操控灵活"}], "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "奥迪Q2L", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2k12000d5pk830A654.png", "fuel": "95号", "passengerNo": 5, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV4j12000cduvnhb63DF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2q12000cduw3po6DE9.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3212000cduvrpp349A.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2s12000cduw24j7A43.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4312000cduvr55FA17.jpg?mark=yiche"], "transmissionType": 1, "brandName": "奥迪", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "奥迪", "licenseStyle": "2", "vehiclesSetId": "10", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_452_沪牌", "luggageNo": 2, "carPlay": "部分车辆支持CarPlay/CarLife", "displacement": "1.5T-1.6T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0e12000etff2hzBDBC.png?mark=yiche"], "license": "沪牌", "isSpecialized": true, "groupCode": "5", "zhName": "奔驰C级", "doorNo": 4, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置后驱", "chargeInterface": "部分车辆支持USB/SD/AUX", "mediaTypes": [2], "carPhone": true, "vehicleCode": "452", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "奔驰C级", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2z12000ap2wurk3093.jpg", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5812000cftzt044374.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6512000cftzt0l0B1B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2n12000cftzs2y016D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3y12000cftzuc925CB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6212000cftzrhbD267.jpg?mark=yiche"], "transmissionType": 1, "brandName": "奔驰", "oilType": 3, "struct": "三厢车", "groupName": "豪华轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "奔驰", "licenseStyle": "2", "vehiclesSetId": "98", "guidSys": "支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_4493_", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife/原厂互联/映射", "displacement": "1.4T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4k12000etgmqamCFE7.png?mark=yiche"], "license": "外牌", "isSpecialized": true, "groupCode": "2", "zhName": "大众朗逸", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=1234&app_ver=10.5", "vehicleCode": "4493", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "大众朗逸", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4k12000etgmqamCFE7.png?mark=yiche", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1u12000c5lzks4ED27.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5n12000c5lzkwd97A1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0u12000c5lznhw8EB8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6012000c5lzrknF4D8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6x12000c5lzfpg9EF4.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "6", "vehiclesSetId": "70", "guidSys": "部分车辆支持定速巡航/全速自适应巡航", "transmissionName": "自动挡"}], "isAll": false, "uniqSign": "12001139890321479536r9Z8967xv8Wd6l86Jp71", "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "promptInfos": [{"jumpUrl": "https://m.ctrip.com/tangram/OTI2MjU=?ctm_ref=vactang_page_92625&isHideNavBar=YES&apppgid=10650039395&statusBarStyle=1&channelid=238407", "type": 19, "locations": [{"groupCode": "all", "index": 1}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg3112000e8l73mt525E.png"}, {"type": 18, "locations": [{"groupCode": "all", "index": 5}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg5t12000cixdsaaBF66.png"}], "isLastPage": false, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "checkRequestTime": 1726304062629.1553, "ResponseStatus": {"Extension": [{"Value": "463415341610825460", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a191691-479528-5292466", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1726304064653+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&listExtraMaps[packageLevelVersion]=1&listExtraMaps[nonJumpFlow]=0&modify=&orderId=&pickupPointInfo=2024-09-18 18:00:00|虹桥国际机场T1航站楼-P1停车库扶梯口|2|31.194964|121.346814|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=all&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2024-09-20 18:00:00|虹桥国际机场T1航站楼-P1停车库扶梯口|2|31.194964|121.346814|||&sortType=1&uid=@@PAGENUM@@2", "networkCost": 1904, "environmentCost": 1, "cacheFetchCost": 0, "fetchCost": 1871, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1726304062628, "afterFetch": 1726304064532, "hasRetry": false, "loadDiffCost": 33, "originNetworkCost": 1871}}