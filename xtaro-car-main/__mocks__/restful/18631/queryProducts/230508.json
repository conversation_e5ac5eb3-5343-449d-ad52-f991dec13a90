{"baseResponse": {"extMap": {"calculatePreAuth_3": "1.0", "uid": "E2236924826", "allCost": "4856.0", "mergeGroupSize_11": "1", "restCost": "1", "mergeGroupSize_2": "102", "mergeGroup_7": "0.0", "usePage": "1", "mergeGroupSize_3": "64", "shoppingCost_1": "3621.0", "mergeGroup_11": "0.0", "mergeGroupSize_4": "74", "restOriginalCost": "4856.0", "contextBuilderCost_3": "38.0", "start": "2023-04-27 15:07:51", "dataConvertResCost": "1233.0", "mergeGroupSize_5": "51", "buildInfoCost_2": "906.0", "mergeGroup_3": "2.0", "mergeGroupSize_6": "206", "buildInfoCost_4": "908.0", "setProductGroupsHashCodeCostAffect": "0.0", "lastInfoCost_7": "250.0", "checkRentCenter_2": "0.0", "mergeGroupSize_7": "2", "runAsyncCost_2": "0.0", "mergeGroup_6": "5.0", "end": "2023-04-27 15:07:56", "pickupCityId": "43", "mergeGroupSize_9": "61", "mergeGroup_9": "2.0", "productGroupCost_6": "37.0", "pageName": "List", "IncludeFeesCost": "0.0", "originalCode": "200", "mergeGroup_2": "2.0", "totalCostTime": "5084", "mergeGroup_10": "0.0", "gsCost": "1.0", "ubtProcessCost_8": "2.0", "mergeGroup_5": "1.0", "buildInfoCost_1": "0.0", "dropoffCityId": "43", "mergeGroupSize_newenergy": "137", "buildInfoCost_3": "1.0", "mergeGroup_newenergy": "22.0", "mergeGroupSize_10": "3", "mergeGroup_4": "2.0", "apiCost": "3619.0", "initBaseData_1": "37.0"}, "errorCode": "0", "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "50413510-6410-4463-b2b1-fe28b19887b7", "isSuccess": true}, "rHub": 1, "isKlbData": true, "allVendorPriceCount": 2720, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": true, "rRentCenter": {"images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "id": 2, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "23:59", "lat": "18.305902", "lng": "109.413683", "name": "三亚凤凰机场店", "fromTime": "00:00"}, "extras": {"isNewLicensePlate": "0", "serverRequestId": "dD7s042OC3Fw4D974t54", "abVersion": "230104_DSJT_fil10|B,220323_DSJT_rank2|B,221207_DSJT_cxvr|A"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"shortName": "车龄", "sortNum": 1, "bitwiseType": 2, "name": "车龄", "groupCode": "CarAge", "filterItems": [{"sortNum": 1, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "5", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 2, "positionCode": "5", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 3, "positionCode": "5", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "手动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "NewEnergy", "itemCode": "NewEnergy_elect", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}, {"sortNum": 2, "groupCode": "NewEnergy", "itemCode": "NewEnergy_mix", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "新能源混动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 3, "groupCode": "VehicleAccessory", "quickSortNum": 7, "positionCode": "6", "itemCode": "VehicleAccessory_radar", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "倒车雷达"}, {"sortNum": 4, "groupCode": "VehicleAccessory", "quickSortNum": 8, "positionCode": "6", "itemCode": "VehicleAccessory_tachograph", "binaryDigit": 64, "isQuickItem": true, "bitwiseType": 1, "name": "行车记录仪"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}, {"sortNum": 7, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Refrigerator", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 1, "name": "车载冰箱"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "HotBrand", "itemCode": "HotBrand_奥迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_别克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_奔驰", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_宝马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotB<PERSON>_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 6, "groupCode": "HotBrand", "itemCode": "HotBrand_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 10, "groupCode": "HotBrand", "itemCode": "HotBrand_Jeep", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 12, "groupCode": "HotBrand", "itemCode": "HotBrand_路虎", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 18, "groupCode": "HotBrand", "itemCode": "HotBrand_日产", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_AC <PERSON>itzer", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "AC Schnitzer", "icon": "//pages.c-ctrip.com/carisd/brandlogo/acschnitzer.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_奥迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_阿斯顿·马丁", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "阿斯顿·马丁", "icon": "https://dimg04.c-ctrip.com/images/0R419120009gwx9cqBA77.png"}], "groupCode": "BrandGroup_a0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_BEIJING汽车", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "BEIJING汽车", "icon": "https://dimg04.c-ctrip.com/images/0R46u120009gwv7gj857B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_保时捷", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "保时捷", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_别克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_北京", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "北京", "icon": "//pages.c-ctrip.com/carisd/brandlogo/beijin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_北汽昌河", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "北汽昌河", "icon": "//pages.c-ctrip.com/carisd/brandlogo/beiqichagnhe.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔腾", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "奔腾", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benteng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔驰", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝沃", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "宝沃", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baowo.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝马", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝骏", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "宝骏", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baojun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宾利", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "宾利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/binli.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_巴博斯", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "巴博斯", "icon": "//dimg04.c-ctrip.com/images/0AS47120008bmtwth44BA.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_比亚迪", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "比亚迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_创维汽车", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "创维汽车", "icon": "https://dimg04.c-ctrip.com/images/0yc0v12000aombae248A8.png"}, {"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长城", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "长城", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changcheng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "长安", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安欧尚", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "长安欧尚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changanoushang.png"}], "groupCode": "BrandGroup_c0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风风神", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "东风风神", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengshen.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风风行", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "东风风行", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengxin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大运", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "大运", "icon": "https://dimg04.c-ctrip.com/images/0R46b120009gwviht186F.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_道奇", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "道奇", "icon": "//pages.c-ctrip.com/carisd/brandlogo/daoqi.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_法拉利", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "法拉利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_福特", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "福特", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}], "groupCode": "BrandGroup_f0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽传祺", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "广汽传祺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽埃安", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "广汽埃安", "icon": "https://dimg04.c-ctrip.com/images/0R44f120009gwvd13842B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽新能源", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "广汽新能源", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqixingnengyuan.png"}], "groupCode": "BrandGroup_g0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_华晨新日", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "华晨新日", "icon": "https://dimg04.c-ctrip.com/images/0R472120009gwv9rxB7E1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_华颂", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "华颂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/huasong.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_哈弗", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "哈弗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_海马", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "海马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_红旗", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "红旗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}], "groupCode": "BrandGroup_h0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_Jeep", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_几何汽车", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "几何汽车", "icon": "https://dimg04.c-ctrip.com/images/0R43t120009gwv73p44FD.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_吉利汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "吉利汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷豹", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "捷豹", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiebao.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷达", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "捷达", "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷途", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "捷途", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jietu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_极氪", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "极氪", "icon": "https://dimg04.c-ctrip.com/images/0yc0x12000aom51we094D.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_江淮", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "江淮", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jianghuan.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_k0", "itemCode": "BrandGroup_k0_凯迪拉克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "凯迪拉克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "groupCode": "BrandGroup_k0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_LEVC", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "LEVC", "icon": "//pages.c-ctrip.com/carisd/brandlogo/levc.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_兰博基尼", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "兰博基尼", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_凌宝汽车", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "凌宝汽车", "icon": "https://dimg04.c-ctrip.com/images/0R41o120009gwvad526A5.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_力帆汽车", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "力帆汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lifanqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_劳斯莱斯", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "劳斯莱斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_岚图汽车", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "岚图汽车", "icon": "https://dimg04.c-ctrip.com/images/0R45a120009gwvmb34A33.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_林肯", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "林肯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/linken.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_理念", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "理念", "icon": "//pages.c-ctrip.com/carisd/brandlogo/linian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_理想汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "理想汽车", "icon": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc6i1200000ieok0AE70.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_路虎", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_零跑汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "零跑汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingpaoqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_雷克萨斯", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "雷克萨斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_雷诺", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "雷诺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/leinuo.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_领克", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "领克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingke.png"}], "groupCode": "BrandGroup_l0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_MINI", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "MINI", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_名爵", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "名爵", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mingjue.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_玛莎拉蒂", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "玛莎拉蒂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_迈凯伦", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "迈凯伦", "icon": "//pages.c-ctrip.com/carisd/brandlogo/maikailun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_马自达", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "马自达", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mazida.png"}], "groupCode": "BrandGroup_m0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_n0", "itemCode": "BrandGroup_n0_哪吒汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "哪吒汽车", "icon": "https://dimg04.c-ctrip.com/images/0R46n120009gwvgrmC0A4.png"}], "groupCode": "BrandGroup_n0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_o0", "itemCode": "BrandGroup_o0_欧拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "欧拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/oula.png"}], "groupCode": "BrandGroup_o0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_p0", "itemCode": "BrandGroup_p0_Polestar极星", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "Polestar极星", "icon": "https://dimg04.c-ctrip.com/images/0R43g120009gwuo9r0192.png"}, {"sortNum": 4, "groupCode": "BrandGroup_p0", "itemCode": "BrandGroup_p0_朋克汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "朋克汽车", "icon": "https://dimg04.c-ctrip.com/images/0R45q120009gwx3tr6B92.png"}], "groupCode": "BrandGroup_p0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_前途", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "前途", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiantu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_奇瑞", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "奇瑞", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qirui.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_起亚", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "起亚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "groupCode": "BrandGroup_q0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_日产", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_荣威", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "荣威", "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "groupCode": "BrandGroup_r0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_SERES赛力斯", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "SERES赛力斯", "icon": "https://dimg04.c-ctrip.com/images/0R450120009gwvngnB95E.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_smart", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "smart", "icon": "//pages.c-ctrip.com/carisd/brandlogo/smart.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_三菱", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "三菱", "icon": "//pages.c-ctrip.com/carisd/brandlogo/sanling.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_上汽大通MAXUS", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "上汽大通MAXUS", "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_思铭", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "思铭", "icon": "//pages.c-ctrip.com/carisd/brandlogo/siming.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_斯柯达", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "斯柯达", "icon": "//pages.c-ctrip.com/carisd/brandlogo/sikeda.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_赛麟", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "赛麟", "icon": "//pages.c-ctrip.com/carisd/brandlogo/sailin.png"}], "groupCode": "BrandGroup_s0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_坦克", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "坦克", "icon": "https://dimg04.c-ctrip.com/images/0yc3612000aoma5tvACF1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_特斯拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "特斯拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_腾势", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "腾势", "icon": "//pages.c-ctrip.com/carisd/brandlogo/shiteng.png"}], "groupCode": "BrandGroup_t0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_WEY", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "WEY", "icon": "//pages.c-ctrip.com/carisd/brandlogo/wey.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_五菱汽车", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "五菱汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/wulingqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_威马汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "威马汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weimaqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_沃尔沃", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "沃尔沃", "icon": "//pages.c-ctrip.com/carisd/brandlogo/woerwo.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_蔚来", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "蔚来", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weilai.png"}], "groupCode": "BrandGroup_w0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小鹏", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "小鹏", "icon": "https://dimg04.c-ctrip.com/images/0yc5812000aom40h4060E.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小鹏汽车", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "小鹏汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiaopengqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_现代", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "现代", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪佛兰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "雪佛兰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_一汽", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "一汽", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yiqi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_依维柯", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "依维柯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yikewei.png"}, {"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_英菲尼迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "英菲尼迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yingfeinidi.png"}], "groupCode": "BrandGroup_y0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_SONGSAN MOTORS", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "SONGSAN MOTORS"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_睿蓝汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "睿蓝汽车"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_飞凡汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "飞凡汽车"}], "groupCode": "BrandGroup_z0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "0-50", "sortNum": 1, "name": "¥50以下", "groupCode": "Price", "itemCode": "Price_0-50"}, {"code": "50-100", "sortNum": 2, "name": "¥50-100", "groupCode": "Price", "itemCode": "Price_50-100"}, {"code": "100-200", "sortNum": 3, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-400", "sortNum": 4, "name": "¥200-400", "groupCode": "Price", "itemCode": "Price_200-400"}, {"code": "400-99999", "sortNum": 5, "name": "¥400以上", "groupCode": "Price", "itemCode": "Price_400-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "PickReturn", "quickSortNum": 1, "mark": "15分钟内", "itemCode": "PickReturn_StationPR", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "positionCode": "1", "name": "站内取车"}, {"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 3, "groupCode": "PickReturn", "itemCode": "PickReturn_FreeShuttle", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "免费接至门店取车"}, {"sortNum": 4, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupSelf", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "自行到店取车"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "StoreService", "quickSortNum": 4, "positionCode": "3", "itemCode": "StoreService_easyLife", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 1, "name": "无忧租"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 6, "groupCode": "StoreService", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "Promotion", "itemCode": "Promotion_3585", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "国庆特惠"}, {"sortNum": 4, "groupCode": "Promotion", "itemCode": "Promotion_3662", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "春节特惠"}, {"sortNum": 4, "groupCode": "Promotion", "itemCode": "Promotion_3836", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "盲盒测试"}], "groupCode": "Promotion", "shortName": "优惠活动", "bitwiseType": 2, "name": "优惠活动"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_8", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "台胞证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "驾驶员驾龄", "sortNum": 6, "bitwiseType": 2, "name": "驾驶员驾龄", "groupCode": "DriveAge", "filterItems": [{"sortNum": 1, "groupCode": "DriveAge", "itemCode": "DriveAge_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "不满6个月"}, {"sortNum": 2, "groupCode": "DriveAge", "itemCode": "DriveAge_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "满6个月"}, {"sortNum": 3, "groupCode": "DriveAge", "itemCode": "DriveAge_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "满一年"}]}, {"shortName": "门店评分", "sortNum": 7, "bitwiseType": 2, "name": "门店评分", "groupCode": "Comment", "filterItems": [{"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}]}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "itemCode": "Vendor_0", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "携程租车中心"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "18", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "携程优选", "icon": ""}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13037", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "calabicalabi测试lr"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30004", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "铭轩酒店租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30027", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "玛雅租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30055", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "准典出行"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30147", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "calabi-骑仕租车A"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30150", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "小木鱼租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30169", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "峰硕租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30196", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "太平洋租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30234", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "金晟租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30248", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "驰敖天天租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30284", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "旭升租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30466", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "祥成租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30777", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "椰林情租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31025", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "乐天租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31092", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "树德租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31218", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "大权租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31239", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "佳途租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31279", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "游乐美租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32231", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "泰信吉租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32498", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "么么达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32538", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "海越租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32687", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "行者天下租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32845", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "美凯租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_33419", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "欣岳美行租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_37573", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "凯美租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_38235", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "美行尚盈租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_43973", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "夜航租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_47522", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "祥驰租车"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_52811", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "蔚蓝租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_53893", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "世纪联合租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_57671", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "捷安利达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_58487", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "丰田海南出行"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61365", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "普信租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61372", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "程硕租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61659", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "旺亚租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61671", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "凤翔天涯租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61816", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "永卓租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61827", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "吉海畅行租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61831", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "金达莱租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61937", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "锋达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61951", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "鑫旺达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61966", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "宝驰租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62053", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "联谊租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62072", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "一路平安租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62099", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "宏驰智行租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62104", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "金森租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62107", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "启捷租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62115", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "启航租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62119", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "三鹤租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62166", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "小红帽租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62167", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "玖捌陆租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62267", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "琼驰租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62305", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "皖太租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62408", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "宏广东盈租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62863", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "文东租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63457", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "翊霏租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63460", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "小米租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63836", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "租租侠租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63857", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "信华租车"}], "groupCode": "Vendor_1", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_64429", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "十八度阳光租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_64662", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "潆莹租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_65413", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "哈尔滨奥朗租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_65451", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "荣树租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_65452", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "新易达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_65454", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "信和华租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_66324", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "环岛租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_66335", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "亿豪租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_66614", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "你我他租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_66708", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "旭辰租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_67661", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "乐途租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_67709", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "神风租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_68688", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "青草兔租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_68692", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "利资租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_69279", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "安米租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_69280", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "百募租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_69284", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "可爱屋租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_69287", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "华鑫海租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_70400", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "禧瑞达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_70693", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "车旺达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_70695", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "龙之祥租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_70697", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "铭车邦租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_70698", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "铭途租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_71515", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "中进通达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_72621", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "细杰租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_72983", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "吉驰租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_73265", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "车之美租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_73619", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "锦程租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_73843", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "飞浩租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_73871", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "多浦达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_74365", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "爱尚出行租车"}], "groupCode": "Vendor_2", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_74373", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "中进租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_74569", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "河北唐亚租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_74573", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "器车出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_74629", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "通源租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_76105", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "全季租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_76661", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "振亚租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_76665", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "彩车坊租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_76903", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "如亚租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_77081", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "常晟租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_77145", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "万迪租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_77147", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "美点租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_77151", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "金晟利租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_77287", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "老马出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_77523", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "博汇租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_78577", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "夏末微凉租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_78579", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "优享旅途租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_78963", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "轩琪租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79461", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "海南麻豆智行租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79485", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "立强租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79695", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "炜晨租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79723", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "盛豪会租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79797", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "麒麟火租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79799", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "华瑞租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80127", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "新概念租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80145", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "鑫路达租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80147", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "浩宇租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80427", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "名都租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80431", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "小飞侠租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80545", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "盛兴隆租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80559", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "星锐租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80617", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "道途租车"}], "groupCode": "Vendor_3", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_80771", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "豫海租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_80977", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "星月租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81003", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "琼州租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81479", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "商旅出行"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81525", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "钰鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81527", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "业扬租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81529", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "果岭出行"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81675", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "广源租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81679", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "凯信租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81687", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "易达通租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81827", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "京海租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81829", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "诚航商旅"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81831", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "潮人商旅"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81889", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "八骏马租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81919", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "福斯特租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81931", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "博利租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82105", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "旅途中租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82163", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "启瑞盛租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82231", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "云超租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82263", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "聚通达租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82301", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "梵云租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82305", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "畅行无忧租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82393", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "海鸭鸭租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82671", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82731", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82739", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82819", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82843", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82867", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": ""}], "groupCode": "Vendor_4", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "vA92AMRa8UUw0RK6Vdyg", "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 62, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 124, "detail": [{"code": "1001", "amount": 156, "amountDesc": "¥156", "name": "租车费"}, {"code": "3836", "amount": 32, "amountDesc": "¥32", "name": "盲盒测试"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥62", "originalDailyPrice": 78, "subAmount": 62, "name": "车辆租金", "amountStr": "¥124"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 204, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥236", "subAmount": 236, "name": "总价", "amountStr": "¥204"}], "reference": {"vehicleCode": "0", "rStoreCode": "106896", "packageId": "", "pLev": 23665, "comPriceCode": "[c]MjA3fDE4NjJ8MjAyMC4wMC0wOCAzLTA1MDowMDAwOjAmMSQyJjc4JjA1LTAwMjMtOjAwOjkgMDA4JiYxMDAmNzAxJjIkfDEwMTU2JCY3OCYmMSYyMTAwMyYyMC4wLjAwMDAyJjAwJDEmNjAkMiYzMDMtMDR8MjAyMTU6MC0yNyAAAAAANzo1Mg==", "bizVendorCode": "SD37573", "pStoreCode": "106896", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzQwNjdfMV83OF8xNTZfNzhfMjM2LjAwXzYyLjBfMjA0LjBfMF8wXzAuMF8wLjBfNjBfMjAuMDBfMC4wMF8wLjAwXzE4NjIyMDc=", "sendTypeForPickUpCar": 0, "skuId": 1862207, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 23665, "vendorCode": "37573", "vendorVehicleCode": "20007292"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1865573, "bizVendorCode": "SD81889"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1964054, "bizVendorCode": "SD30234"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1856767, "bizVendorCode": "SD30147"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860324, "bizVendorCode": "SD31092"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1856407, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1865075, "bizVendorCode": "SD82739"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2346203, "bizVendorCode": "SD80977"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1865486, "bizVendorCode": "SD76105"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1972358, "bizVendorCode": "SD61951"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1920948, "bizVendorCode": "SD82731"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1971788, "bizVendorCode": "SD82105"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1921776, "bizVendorCode": "SD78579"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1923000, "bizVendorCode": "SD62167"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1972662, "bizVendorCode": "SD80427"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1964008, "bizVendorCode": "SD81527"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1861422, "bizVendorCode": "SD62408"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1977341, "bizVendorCode": "SD82671"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减32", "groupCode": "MarketGroup1383", "code": "30", "title": "盲盒测试", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3836"}, "vehicleCode": "1126", "highestPrice": 2900, "rCoup": 0, "minDPrice": 62, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 204, "lowestDistance": 0, "group": 0, "sortNum": 0, "maximumRating": 4.4, "vehicleRecommendProduct": {"productCodes": ["SD37573_0_106896_106896"], "introduce": "当前车型最低价"}, "minDOrinPrice": 78, "isEasy": true, "isCredit": true, "maximumCommentCount": 13, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 18}, {"groupSort": 0, "lowestPrice": 45, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 128, "detail": [{"code": "1001", "amount": 194, "amountDesc": "¥194", "name": "租车费"}, {"code": "3662", "amount": 66, "amountDesc": "¥66", "name": "春节特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥64", "originalDailyPrice": 97, "subAmount": 64, "name": "车辆租金", "amountStr": "¥128"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 208, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥274", "subAmount": 274, "name": "总价", "amountStr": "¥208"}], "reference": {"vehicleCode": "0", "rStoreCode": "106878", "packageId": "", "pLev": 22341, "comPriceCode": "[c]OTE3fDE4NTR8MjAyMC4xMC0wOCAzLTA1MDowMDAwOjAmMSQyJjk3JjA1LTAwMjMtOjAwOjkgMDA3JiYxMDAmOTAxJjIkfDEwMTk0JCY5NyYmMSYyMTAwMyYyMC4wLjAwMDAyJjAwJDEmNjAkMiYzMDMtMDR8MjAyMTU6MC0yNyAAAAAANzo1Mg==", "bizVendorCode": "SD30147", "pStoreCode": "106878", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzQwMTRfMV85N18xOTRfOTdfMjc0LjAwXzY0LjBfMjA4LjBfMF8wXzAuMF8wLjBfNjBfMjAuMDBfMC4wMF8wLjAwXzE4NTQ5MTc=", "sendTypeForPickUpCar": 0, "skuId": 1854917, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22341, "vendorCode": "30147", "vendorVehicleCode": "14415"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1863132, "bizVendorCode": "SD37573"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1965861, "bizVendorCode": "SD74373"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2705051, "bizVendorCode": "SD77147"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1911940, "bizVendorCode": "SD61831"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860657, "bizVendorCode": "SD31092"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1972046, "bizVendorCode": "SD30234"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2705001, "bizVendorCode": "SD81827"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1861254, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1915995, "bizVendorCode": "SD77287"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1924514, "bizVendorCode": "SD79723"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2704960, "bizVendorCode": "SD33419"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1911622, "bizVendorCode": "SD74573"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1863247, "bizVendorCode": "SD76105"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1914962, "bizVendorCode": "SD77151"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1916013, "bizVendorCode": "SD77081"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1913476, "bizVendorCode": "SD80431"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860920, "bizVendorCode": "SD53893"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1919961, "bizVendorCode": "SD62863"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2362546, "bizVendorCode": "SD62119"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1980186, "bizVendorCode": "SD80145"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2237978, "bizVendorCode": "SD32538"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1908499, "bizVendorCode": "SD62115"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2330535, "bizVendorCode": "SD30466"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减66", "groupCode": "MarketGroup103", "code": "30", "title": "春节特惠", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3662"}, "vehicleCode": "4139", "highestPrice": 2000, "rCoup": 0, "minDPrice": 64, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 208, "lowestDistance": 0, "group": 0, "sortNum": 1, "maximumRating": 4.4, "vehicleRecommendProduct": {"productCodes": ["SD77147_0_116954_116954"], "introduce": "当前车型最低价"}, "minDOrinPrice": 97, "isEasy": true, "isCredit": true, "maximumCommentCount": 13, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 24}, {"groupSort": 0, "lowestPrice": 70, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 140, "detail": [{"code": "1001", "amount": 176, "amountDesc": "¥176", "name": "租车费"}, {"code": "3836", "amount": 36, "amountDesc": "¥36", "name": "盲盒测试"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥70", "originalDailyPrice": 88, "subAmount": 70, "name": "车辆租金", "amountStr": "¥140"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 220, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥256", "subAmount": 256, "name": "总价", "amountStr": "¥220"}], "reference": {"vehicleCode": "0", "rStoreCode": "106896", "packageId": "", "pLev": 23665, "comPriceCode": "[c]Mjg0fDE4NTl8MjAyMC4wMC0wOCAzLTA1MDowMDAwOjAmMSQyJjg4JjA1LTAwMjMtOjAwOjkgMDA4JiYxMDAmODAxJjIkfDEwMTc2JCY4OCYmMSYyMTAwMyYyMC4wLjAwMDAyJjAwJDEmNjAkMiYzMDMtMDR8MjAyMTU6MC0yNyAAAAAANzo1Mg==", "bizVendorCode": "SD37573", "pStoreCode": "106896", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzQ0OTNfMV84OF8xNzZfODhfMjU2LjAwXzcwLjBfMjIwLjBfMF8wXzAuMF8wLjBfNjBfMjAuMDBfMC4wMF8wLjAwXzE4NTkyODQ=", "sendTypeForPickUpCar": 0, "skuId": 1859284, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 23665, "vendorCode": "37573", "vendorVehicleCode": "20069311"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2235814, "bizVendorCode": "SD74373"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1860588, "bizVendorCode": "SD81889"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2054131, "bizVendorCode": "SD68692"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860714, "bizVendorCode": "SD31092"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1921283, "bizVendorCode": "SD79723"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1865402, "bizVendorCode": "SD30147"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1772301, "bizVendorCode": "SD30055"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1862623, "bizVendorCode": "SD76105"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1861157, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1910294, "bizVendorCode": "SD65413"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1816518, "bizVendorCode": "SD43973"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1963525, "bizVendorCode": "SD80147"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2441431, "bizVendorCode": "SD82671"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1973545, "bizVendorCode": "SD62053"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减36", "groupCode": "MarketGroup1383", "code": "30", "title": "盲盒测试", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3836"}, "vehicleCode": "1069", "highestPrice": 1998, "rCoup": 0, "minDPrice": 70, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 220, "lowestDistance": 0, "group": 0, "sortNum": 2, "maximumRating": 4.4, "vehicleRecommendProduct": {"productCodes": ["SD37573_0_106896_106896"], "introduce": "当前车型最低价"}, "minDOrinPrice": 88, "isEasy": true, "isCredit": true, "maximumCommentCount": 13, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 15}, {"groupSort": 0, "lowestPrice": 68, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 156, "detail": [{"code": "1001", "amount": 156, "amountDesc": "¥156", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥78", "subAmount": 78, "name": "车辆租金", "amountStr": "¥156"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 236, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥236", "subAmount": 236, "name": "总价", "amountStr": "¥236"}], "reference": {"vehicleCode": "0", "rStoreCode": "107852", "packageId": "", "pLev": 39513, "comPriceCode": "[c]ODE1fDE4ODd8MjAyMC4wMC0wOCAzLTA1MDowMDAwOjAmMSQyJjc4JjA1LTAwMjMtOjAwOjkgMDA4JiYxMDAmNzAxJjIkfDEwMTU2JCY3OCYmMSYyMTAwMyYyMC4wLjAwMDAyJjAwJDEmNjAkMiYzMDMtMDR8MjAyMTU6MC0yNyAAAAAANzo1MQ==", "bizVendorCode": "SD31025", "pStoreCode": "107852", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3ODUyXzUwNjhfMV83OF8xNTZfNzhfMjM2LjAwXzc4XzIzNi4wXzBfMF8wLjBfMC4wXzYwXzIwLjAwXzAuMDBfMC4wMF8xODg3ODE1", "sendTypeForPickUpCar": 0, "skuId": 1887815, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 39513, "vendorCode": "31025", "vendorVehicleCode": "20080277"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1862695, "bizVendorCode": "SD57671"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1962706, "bizVendorCode": "SD30234"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1920962, "bizVendorCode": "SD65413"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1858635, "bizVendorCode": "SD30147"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2331596, "bizVendorCode": "SD71515"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2288657, "bizVendorCode": "SD61365"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2441296, "bizVendorCode": "SD73843"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1964214, "bizVendorCode": "SD80145"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1816896, "bizVendorCode": "SD43973"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1863681, "bizVendorCode": "SD62408"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2426741, "bizVendorCode": "SD30466"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1865510, "bizVendorCode": "SD30284"}}], "rCoup": 0, "vehicleCode": "5068", "highestPrice": 2000, "pWay": "可选:店员免费上门送取车", "minDPrice": 78, "hot": 0, "minTPrice": 236, "lowestDistance": 1.3784, "group": 0, "sortNum": 3, "maximumRating": 4, "vehicleRecommendProduct": {"productCodes": ["SD57671_0_107242_107242"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 13, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 13}, {"groupSort": 0, "lowestPrice": 70, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 156, "detail": [{"code": "1001", "amount": 196, "amountDesc": "¥196", "name": "租车费"}, {"code": "3836", "amount": 40, "amountDesc": "¥40", "name": "盲盒测试"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥78", "originalDailyPrice": 98, "subAmount": 78, "name": "车辆租金", "amountStr": "¥156"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 256, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥296", "subAmount": 296, "name": "总价", "amountStr": "¥256"}], "reference": {"vehicleCode": "0", "rStoreCode": "106896", "pLev": 23665, "comPriceCode": "[c]NDYzfDE4NjUzLTA1fDIwMjAwOjAtMDggJjk4JjA6MDAwMjMtJjEkMjkgMDAwNS0wMDAmOTowMDokfDEwOCYmMSY5OCYwMSYyMTAwMzE5NiQwLjAwJjEmMjAwJDEmMjAuMiY0MDAwMiZ8MjAyJjgwJC0yNyAzLTA0Nzo1MjE1OjA=", "bizVendorCode": "SD37573", "pStoreCode": "106896", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzUxNzdfMV85OF8xOTZfOThfMjk2LjAwXzc4LjBfMjU2LjBfMF8wXzAuMF8wLjBfODBfMjAuMDBfMC4wMF8wLjAwXzE4NjU0NjM=", "sendTypeForPickUpCar": 0, "skuId": 1865463, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 23665, "vendorCode": "37573", "vendorVehicleCode": "20067637"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1914497, "bizVendorCode": "SD67709"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1966815, "bizVendorCode": "SD77147"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2331544, "bizVendorCode": "SD74573"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1910193, "bizVendorCode": "SD77081"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1920814, "bizVendorCode": "SD80431"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1914992, "bizVendorCode": "SD30027"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1857830, "bizVendorCode": "SD53893"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1383", "code": "30", "title": "盲盒测试", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3836"}, "vehicleCode": "5177", "highestPrice": 699, "rCoup": 0, "minDPrice": 78, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 256, "lowestDistance": 0, "group": 0, "sortNum": 4, "maximumRating": 4.4, "vehicleRecommendProduct": {"productCodes": ["SD77147_0_116954_116954"], "introduce": "当前车型最低价"}, "minDOrinPrice": 98, "isEasy": false, "isCredit": true, "maximumCommentCount": 2, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 8, "isOptim": true}, {"groupSort": 0, "lowestPrice": 98, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 196, "detail": [{"code": "1001", "amount": 196, "amountDesc": "¥196", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥98", "subAmount": 98, "name": "车辆租金", "amountStr": "¥196"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 276, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥276", "subAmount": 276, "name": "总价", "amountStr": "¥276"}], "reference": {"vehicleCode": "0", "rStoreCode": "106944", "packageId": "", "pLev": 24630, "comPriceCode": "[c]NDI2fDI2Nzd8MjAyMC4wMC0wOCAzLTA1MDowMDAwOjAmMSQyJjk4JjA1LTAwMjMtOjAwOjkgMDA4JiYxMDAmOTAxJjIkfDEwMTk2JCY5OCYmMSYyMTAwMyYyMC4wLjAwMDAyJjAwJDEmNjAkMiYzMDMtMDR8MjAyMTU6MC0yNyAAAAAANzo1MQ==", "bizVendorCode": "SD62408", "pStoreCode": "106944", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2OTQ0XzU0MDdfMV85OF8xOTZfOThfMjc2LjAwXzk4XzI3Ni4wXzBfMF8wLjBfMC4wXzYwXzIwLjAwXzAuMDBfMC4wMF8yNjc3NDI2", "sendTypeForPickUpCar": 0, "skuId": 2677426, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24630, "vendorCode": "62408", "vendorVehicleCode": "20103505"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1957839, "bizVendorCode": "SD37573"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1863897, "bizVendorCode": "SD30004"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860097, "bizVendorCode": "SD31092"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2428339, "bizVendorCode": "SD30234"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1921691, "bizVendorCode": "SD79723"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1910574, "bizVendorCode": "SD58487"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2413064, "bizVendorCode": "SD61365"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1920321, "bizVendorCode": "SD67709"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2302873, "bizVendorCode": "SD74365"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1772352, "bizVendorCode": "SD30055"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1862480, "bizVendorCode": "SD79695"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1974271, "bizVendorCode": "SD82105"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1971297, "bizVendorCode": "SD80127"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1978099, "bizVendorCode": "SD33419"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1858926, "bizVendorCode": "SD81525"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2704627, "bizVendorCode": "SD82731"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1958261, "bizVendorCode": "SD80145"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2398426, "bizVendorCode": "SD32538"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1972214, "bizVendorCode": "SD81679"}}], "rCoup": 0, "vehicleCode": "5407", "highestPrice": 2000, "pWay": "可选:店员免费上门送取车", "minDPrice": 98, "hot": 0, "minTPrice": 276, "lowestDistance": 0, "group": 0, "sortNum": 5, "maximumRating": 4.4, "vehicleRecommendProduct": {"productCodes": ["SD62408_0_106944_106944"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 2, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 20}], "groupCode": "all", "dailyPrice": 62, "hasResult": true}, {"sortNum": 0, "groupName": "经济轿车", "hasResult": true, "groupCode": "2", "dailyPrice": 62}, {"sortNum": 2, "groupName": "新能源", "hasResult": true, "groupCode": "newenergy", "dailyPrice": 70}, {"sortNum": 3, "groupName": "舒适轿车", "hasResult": true, "groupCode": "3", "dailyPrice": 78}, {"sortNum": 4, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 78}, {"sortNum": 5, "groupName": "商务车", "hasResult": true, "groupCode": "4", "dailyPrice": 145}, {"sortNum": 6, "groupName": "豪华轿车", "hasResult": true, "groupCode": "5", "dailyPrice": 148}, {"sortNum": 7, "groupName": "跑车", "hasResult": true, "groupCode": "9", "dailyPrice": 288}, {"sortNum": 8, "groupName": "房车", "hasResult": true, "groupCode": "11", "dailyPrice": 1200}, {"sortNum": 9, "groupName": "小巴士", "hasResult": true, "groupCode": "7", "dailyPrice": 488}, {"sortNum": 10, "groupName": "皮卡", "hasResult": true, "groupCode": "10", "dailyPrice": 168}], "frontTraceInfo": {"vehicleGroupMap": {"2": 102, "3": 64, "4": 74, "5": 51, "6": 206, "7": 2, "9": 61, "10": 3, "11": 1, "newenergy": 137}, "normalCount": 3242, "priceCount": 3242, "vehicleList": ["4971", "1577", "1333", "4720", "4842", "4980", "4982", "4861", "4740", "1", "2437", "2799", "1464", "1222", "2794", "4972", "4852", "4610", "4732", "4975", "4977", "5703", "5704", "4978", "4736", "4737", "4870", "4992", "4750", "3782", "4993", "4872", "4630", "2451", "17380", "3417", "5719", "1474", "4741", "4742", "4743", "4866", "3778", "10630", "4505", "4626", "10632", "4506", "819", "17394", "4883", "17390", "17391", "2461", "1492", "2460", "1491", "2459", "1126", "1488", "4994", "5600", "5601", "4635", "5604", "4515", "4758", "829", "17366", "1140", "10615", "1138", "2224", "1497", "1133", "1496", "712", "4643", "4764", "4644", "4526", "4769", "17378", "4781", "4660", "17373", "4662", "4421", "2484", "1152", "3450", "1151", "1150", "5508", "1149", "2477", "3203", "5500", "4653", "4895", "4774", "5502", "5623", "4897", "5503", "4535", "4899", "5504", "10622", "4659", "608", "5506", "17344", "4790", "4670", "17346", "5520", "4432", "3101", "3221", "2247", "852", "2004", "1399", "853", "2487", "5632", "4544", "4303", "5634", "4788", "5514", "859", "5517", "17357", "5651", "5410", "1176", "2385", "2384", "3469", "4796", "4433", "5402", "4676", "5524", "4798", "5525", "4799", "5526", "5407", "4318", "4319", "4571", "4330", "2155", "873", "1058", "3477", "1178", "2387", "635", "5413", "4325", "5415", "5658", "4449", "5418", "5419", "4220", "4101", "5311", "4344", "5433", "4102", "3253", "3494", "1069", "5545", "5303", "4577", "5424", "5425", "648", "5305", "5306", "5427", "5428", "1080", "4470", "4471", "4593", "5320", "4110", "5685", "4112", "5564", "5322", "5201", "5444", "5686", "4476", "4234", "3262", "1083", "2050", "1082", "1081", "410", "652", "2049", "656", "4103", "5676", "5555", "4345", "658", "5435", "4588", "5439", "4108", "4109", "5570", "4482", "5571", "5572", "4483", "4484", "5331", "5574", "4364", "5453", "4243", "5211", "5332", "5575", "4487", "2066", "3155", "2064", "540", "300", "301", "4598", "4478", "5325", "548", "4358", "4117", "5569", "4238", "5329", "5462", "4010", "4495", "5584", "4253", "5342", "4496", "5222", "4375", "5101", "5465", "5223", "4498", "5466", "2078", "3164", "672", "673", "311", "3159", "555", "315", "5214", "4488", "679", "4004", "4367", "4489", "5579", "4249", "5219", "4020", "5472", "5351", "5594", "5474", "5353", "5595", "5232", "4385", "5596", "5475", "6323", "4025", "3176", "682", "3169", "326", "5104", "5225", "5467", "5588", "5226", "4139", "5349", "6316", "5360", "3181", "5481", "5482", "4030", "3180", "5240", "4151", "5241", "4031", "6333", "5485", "5245", "5367", "4036", "3184", "5480", "452", "696", "333", "576", "456", "214", "3059", "216", "4268", "5599", "4026", "5479", "5359", "4149", "5371", "5010", "5131", "4042", "5253", "4043", "5374", "4044", "5254", "6343", "4287", "5257", "5499", "5491", "340", "220", "587", "468", "5005", "5368", "5489", "5126", "5007", "5128", "5262", "4052", "5141", "4053", "5145", "5388", "4058", "591", "4291", "471", "472", "474", "476", "5258", "4048", "5017", "5259", "5138", "5393", "4062", "5151", "5272", "5274", "5396", "5276", "5277", "5278", "5399", "5158", "5270", "3095", "5271", "120", "247", "248", "5283", "5163", "5164", "5044", "5167", "5289", "6258", "28", "5047", "250", "4191", "4071", "5281", "5282", "4193", "133", "135", "137", "5294", "5052", "4084", "33", "5173", "5295", "4086", "5175", "5176", "5297", "5177", "4088", "37", "5298", "5058", "5291", "140", "5050", "6261", "4082", "5172", "5051", "265", "267", "1927", "5063", "6276", "5066", "5187", "5068", "49", "5189", "4091", "5182", "6272", "4094", "2900", "279", "2905", "2904", "5074", "5196", "280", "6281", "5191", "5071", "5193", "5194", "286", "166", "287", "1820", "289", "65", "6297", "5089", "293", "6291", "6294", "175", "1833", "2801", "177", "178", "1951", "75", "1834", "89", "2706", "2704", "97", "98", "17455", "2713", "2831", "1500", "17457", "17421", "2721", "2842", "4907", "2720", "4909", "17428", "2608", "2729", "17425", "17427", "1404", "4916", "1644", "2732", "1400", "1407", "1406", "3945", "1405", "4925", "4804", "4805", "4806", "4928", "4808", "4929", "1531", "1651", "2860", "17529", "4920", "3952", "4921", "17411", "17412", "4936", "4816", "4932", "17413", "4814", "4935", "17416", "2889", "4826", "1436", "4948", "4827", "2888", "4943", "4823", "3617", "4946", "4960", "1450", "2779", "4837", "4958", "4716", "4959", "1446", "1445", "3502", "1444", "1442", "1441", "2893", "4952", "4832", "4834", "1449"], "easyLifeCount": 0, "zhimaCount": 3065, "vendorNames": ["丰田海南出行", "小飞侠租车", "夏末微凉租车", "浩宇租车", "哈尔滨奥朗租车", "龙之祥租车", "凯美租车", "夜航租车", "星月租车", "小米租车", "普信租车", "美点租车", "老马出行", "信华租车", "泰信吉租车", "租租侠租车", "行者天下租车", "八骏马租车", "海鸭鸭租车", "蔚蓝租车", "华鑫海租车", "梵云租车", "立强租车", "环岛租车", "琼驰租车", "你我他租车", "驰敖天天租车", "中进通达租车", "海南麻豆智行租车", "爱尚出行租车", "么么达租车", "启捷租车", "潆莹租车", "诚航商旅", "金晟租车", "美行尚盈租车", "琼州租车", "福斯特租车", "锋达租车", "星锐租车", "鑫旺达租车", "小红帽租车", "如亚租车", "商旅出行", "calabi-骑仕租车A", "祥成租车", "盛豪会租车", "佳途租车", "细杰租车", "优享旅途租车", "树德租车", "新概念租车", "锦程租车", "鑫路达租车", "青草兔租车", "百募租车", "联谊租车", "炜晨租车", "旭辰租车", "美凯租车", "业扬租车", "大权租车", "器车出行", "金晟利租车", "旭升租车", "海越租车", "吉海畅行租车", "中进租车", "金森租车", "可爱屋租车", "金达莱租车", "安米租车", "道途租车", "宏广东盈租车", "博利租车", "潮人商旅", "名都租车", "华瑞租车", "车旺达租车", "利资租车", "太平洋租车", "果岭出行", "峰硕租车", "京海租车", "云超租车", "玛雅租车", "吉驰租车", "常晟租车", "豫海租车", "河北唐亚租车", "铭途租车", "钰鑫租车", "通源租车", "广源租车", "荣树租车", "永卓租车", "一路平安租车", "启航租车", "振亚租车", "玖捌陆租车", "凯信租车", "全季租车", "聚通达租车", "文东租车", "万迪租车", "宏驰智行租车", "欣岳美行租车", "小木鱼租车", "信和华租车", "祥驰租车", "旅途中租车", "铭车邦租车", "飞浩租车", "旺亚租车", "亿豪租车", "铭轩酒店租车", "程硕租车", "博汇租车", "凤翔天涯租车", "宝驰租车", "畅行无忧租车", "新易达租车", "多浦达租车", "游乐美租车", "准典出行", "启瑞盛租车", "十八度阳光租车", "乐途租车", "神风租车", "捷安利达租车", "翊霏租车", "彩车坊租车", "车之美租车", "禧瑞达租车", "世纪联合租车", "乐天租车", "三鹤租车", "盛兴隆租车", "易达通租车", "calabicalabi测试lr", "轩琪租车", "椰林情租车", "皖太租车", "麒麟火租车"]}, "labelCodes": ["3662", "3563", "3585", "3836", "3510", "3696", "3697", "3698", "3731", "3679", "3779", "3650", "3495", "3494", "3548", "3705", "3504", "3547", "3503", "3502", "3501", "3709", "3509", "3788", "3789", "3746"], "quickFilter": [{"sortNum": 1, "groupCode": "PickReturn", "quickSortNum": 1, "mark": "15分钟内", "itemCode": "PickReturn_StationPR", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "positionCode": "1", "name": "站内取车"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 1, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "5", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "18", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "携程优选", "icon": ""}], "promotMap": {}, "requestInfo": {"rLongitude": 109.41201, "rDate": "20230510100000", "age": 30, "returnDate": "/Date(1683684000000+0800)/", "sourceCountryId": 1, "pLatitude": 18.30747, "rLatitude": 18.30747, "pLongitude": 109.41201, "pDate": "20230508100000", "pickupLocationName": "凤凰国际机场T1航站楼", "returnLocationName": "凤凰国际机场T1航站楼", "pickupDate": "/Date(1683511200000+0800)/"}, "allVehicleCount": 564, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "id": 2, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "23:59", "lat": "18.305902", "lng": "109.413683", "name": "三亚凤凰机场店", "fromTime": "00:00"}, "vehicleList": [{"transmissionName": "自动挡", "displacement": "1.4T-1.5L", "style": "", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "1126", "vehiclesSetId": "64", "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "大众宝来", "zhName": "大众宝来", "brandName": "大众", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "2", "endurance": "续航100km", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "大众", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false, "vr": ""}, {"luggageNo": 2, "displacement": "1.0T-1.5L", "autoPark": false, "endurance": "续航100km", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "雪佛兰科沃兹", "doorNo": 4, "driveMode": "前置前驱", "vr": "", "carPhone": true, "vehicleCode": "4139", "style": "", "name": "雪佛兰科沃兹", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": false, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "transmissionType": 1, "brandName": "雪佛兰", "oilType": 3, "groupName": "经济轿车", "groupSubClassCode": "", "brandEName": "雪佛兰", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "64", "transmissionName": "自动挡"}, {"transmissionName": "自动挡", "displacement": "1.4T-1.6L", "style": "", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "1069", "vehiclesSetId": "64", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "大众朗逸", "zhName": "大众朗逸", "brandName": "大众", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "2", "endurance": "续航100km", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "大众", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false, "vr": ""}, {"transmissionName": "自动挡", "displacement": "1.4T", "style": "", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "5068", "carPhone": true, "vehiclesSetId": "10", "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "捷达VS5", "zhName": "捷达VS5", "brandName": "捷达", "passengerNo": 5, "doorNo": 5, "isSpecialized": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "6", "endurance": "续航100km", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "捷达", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "license": "", "autoPark": false, "vr": ""}, {"subGroupCode": "newenergy", "luggageNo": 2, "endurance": "续航100km", "fuelType": "纯电动", "charge": "快充0.67小时,慢充8小时", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "isSpecialized": true, "groupCode": "3", "zhName": "别克微蓝6", "doorNo": 5, "driveMode": "前置前驱", "vr": "", "carPhone": true, "vehicleCode": "5177", "style": "", "name": "别克微蓝6", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "luggageNum": "可放2个24寸行李箱", "passengerNo": 5, "autoStart": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "transmissionType": 1, "brandName": "别克", "oilType": 5, "groupName": "舒适轿车", "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "83", "transmissionName": "自动挡"}, {"transmissionName": "自动挡", "displacement": "1.2T-1.5L", "style": "", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "5407", "carPhone": true, "vehiclesSetId": "64", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": true, "name": "丰田 卡罗拉", "zhName": "丰田 卡罗拉", "brandName": "丰田", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "2", "endurance": "续航100km", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "丰田", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false, "vr": ""}], "storeList": [{"pickOffLevel": 23665, "storeCode": "106896", "pickUpLevel": 23665}, {"pickOffLevel": 24630, "storeCode": "106944", "pickUpLevel": 24630}, {"pickOffLevel": 22341, "storeCode": "106878", "pickUpLevel": 22341}, {"pickOffLevel": 39513, "storeCode": "107852", "pickUpLevel": 39513}], "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": false, "isLastPage": false, "uniqSign": "120010356100002012493L4j9f7RRGEXuqH6XK4p", "pHub": 1, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"sortNum": 1, "showLayer": 1, "title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "subTitle": "*国定节假日以页面披露为准"}, {"sortNum": 2, "showLayer": 0, "title": "安心保障", "titleExtra": "(需加购优享服务)", "type": 1, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "subTitle": "*覆盖损失范围以预订页面内披露为准"}, {"sortNum": 3, "title": "优质车况", "subTitle": "", "type": 1, "description": "3年内车龄", "showLayer": 0}, {"sortNum": 10, "title": "满油取车", "subTitle": "", "type": 0, "description": "保障取车时油量满（不含纯电车）", "showLayer": 0}]}, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "5268414358288732966", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a8b56b2-467383-11077", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1682579276442+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&modify=&orderId=&pickupPointInfo=2023-05-08 10:00:00|凤凰国际机场T1航站楼|43|18.30747|109.41201|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=all&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2023-05-10 10:00:00|凤凰国际机场T1航站楼|43|18.30747|109.41201|||&sortType=1&uid=E2236924826@@PAGENUM@@1", "networkCost": 3276, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 5244, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1682579273103, "afterFetch": 1682579276379, "hasRetry": false, "loadDiffCost": 23, "originNetworkCost": 5244}}