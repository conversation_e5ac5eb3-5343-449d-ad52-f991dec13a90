{"baseResponse": {"extMap": {}, "cost": 1867, "hasResult": true, "extraIndexTags": {"pcName": "中国", "rcId": "1", "rcName": "中国", "rCityName": "三亚", "rCityId": "43", "pCityId": "43", "pcId": "1", "pCityName": "三亚"}, "code": "200", "errorCode": "0", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "52b37eb5-d9fe-4b5d-ada8-3937a48db82f", "isSuccess": true}, "rHub": 1, "isKlbData": true, "allVendorPriceCount": 1656, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": true, "rRentCenter": {"images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "id": 2, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "23:59", "lat": "18.308537", "lng": "109.413536", "name": "三亚凤凰机场店", "fromTime": "00:00"}, "extras": {"packageLevelAB": "B", "serverRequestId": "25cFUdI7E59seGr9NaK6", "isLicensePlateHideShow": "0", "abVersion": "241008_DSJT_ykjpx|B,230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,220624_DSJT_spfj1|B", "packageLevelSwitch": "1", "selfServiceSwitch": "1", "isNewLicensePlate": "0", "commodityClass2Version": "1", "prepProductGroupTopSwitch": "0"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"sortNum": 1, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "CarAge", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "8", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 2, "positionCode": "8", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}], "groupCode": "CarAge", "shortName": "车龄", "bitwiseType": 2, "name": "车龄"}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 2, "groupCode": "SeatGroup", "itemCode": "SeatGroup_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 3, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 4, "groupCode": "SeatGroup", "itemCode": "SeatGroup_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 5, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "13", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 6, "groupCode": "SeatGroup", "itemCode": "SeatGroup_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "10", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "手动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "NewEnergy", "itemCode": "NewEnergy_elect", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}, {"sortNum": 2, "groupCode": "NewEnergy", "itemCode": "NewEnergy_mix", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "新能源混动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "9", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 3, "groupCode": "VehicleAccessory", "quickSortNum": 7, "positionCode": "9", "itemCode": "VehicleAccessory_radar", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "倒车雷达"}, {"sortNum": 4, "groupCode": "VehicleAccessory", "quickSortNum": 8, "positionCode": "9", "itemCode": "VehicleAccessory_tachograph", "binaryDigit": 64, "isQuickItem": true, "bitwiseType": 1, "name": "行车记录仪"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}, {"sortNum": 7, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Refrigerator", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 1, "name": "车载冰箱"}, {"sortNum": 9, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Childseat", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 1, "name": "儿童座椅"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "HotBrand", "itemCode": "HotBrand_奥迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_别克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_奔驰", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_宝马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotB<PERSON>_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 6, "groupCode": "HotBrand", "itemCode": "HotBrand_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 10, "groupCode": "HotBrand", "itemCode": "HotBrand_Jeep", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 12, "groupCode": "HotBrand", "itemCode": "HotBrand_路虎", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 18, "groupCode": "HotBrand", "itemCode": "HotBrand_日产", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_埃安", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "埃安", "icon": "https://dimg04.c-ctrip.com/images/0R44f120009gwvd13842B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_奥迪", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_阿斯顿·马丁", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "阿斯顿·马丁", "icon": "https://dimg04.c-ctrip.com/images/0R419120009gwx9cqBA77.png"}], "groupCode": "BrandGroup_a0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_保时捷", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "保时捷", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_别克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_北京", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "北京", "icon": "//pages.c-ctrip.com/carisd/brandlogo/beijin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔腾", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "奔腾", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benteng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔驰", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝马", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝骏", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "宝骏", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baojun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宾利", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "宾利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/binli.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_本田", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_标致", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "标致", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biaozhi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_比亚迪", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "比亚迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安启源", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "长安启源", "icon": "https://dimg04.c-ctrip.com/images/0yc4c12000h4rulocBB7B.png"}], "groupCode": "BrandGroup_c0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风EV新能源", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "东风EV新能源", "icon": "https://dimg04.c-ctrip.com/images/0yc0612000h8yfr3s483E.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风风神", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "东风风神", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengshen.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_德宝", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "德宝", "icon": "https://dimg04.c-ctrip.com/images/0yc3x12000h8a806969EB.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_道奇", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "道奇", "icon": "//pages.c-ctrip.com/carisd/brandlogo/daoqi.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_法拉利", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "法拉利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_福特", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "福特", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}], "groupCode": "BrandGroup_f0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽传祺", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "广汽传祺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_高合汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "高合汽车", "icon": "https://dimg04.c-ctrip.com/images/0yc6a12000aom31vy37E4.png"}], "groupCode": "BrandGroup_g0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_华晨新日", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "华晨新日", "icon": "https://dimg04.c-ctrip.com/images/0R472120009gwv9rxB7E1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_哈弗", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "哈弗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_海马", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "海马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_红旗", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "红旗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}], "groupCode": "BrandGroup_h0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_i0", "itemCode": "BrandGroup_i0_iCAR", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "iCAR", "icon": "https://dimg04.c-ctrip.com/images/0yc2712000h4doexu1B83.png"}], "groupCode": "BrandGroup_i0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_Jeep", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_吉利汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "吉利汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷豹", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "捷豹", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiebao.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷达", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "捷达", "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷途", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "捷途", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jietu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_极氪", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "极氪", "icon": "https://dimg04.c-ctrip.com/images/0yc0x12000aom51we094D.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_k0", "itemCode": "BrandGroup_k0_凯迪拉克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "凯迪拉克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "groupCode": "BrandGroup_k0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_乐道", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "乐道", "icon": "https://dimg04.c-ctrip.com/images/0yc1m12000h1sjx5p6F8F.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_兰博基尼", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "兰博基尼", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_劳斯莱斯", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "劳斯莱斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_岚图汽车", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "岚图汽车", "icon": "https://dimg04.c-ctrip.com/images/0R45a120009gwvmb34A33.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_林肯", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "林肯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/linken.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_理想汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "理想汽车", "icon": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc6i1200000ieok0AE70.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_路特斯", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "路特斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lutesi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_路虎", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_零跑汽车", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "零跑汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingpaoqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_雷克萨斯", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "雷克萨斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}], "groupCode": "BrandGroup_l0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_MINI", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "MINI", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_玛莎拉蒂", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "玛莎拉蒂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_迈凯伦", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "迈凯伦", "icon": "//pages.c-ctrip.com/carisd/brandlogo/maikailun.png"}], "groupCode": "BrandGroup_m0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_n0", "itemCode": "BrandGroup_n0_哪吒汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "哪吒汽车", "icon": "https://dimg04.c-ctrip.com/images/0R46n120009gwvgrmC0A4.png"}], "groupCode": "BrandGroup_n0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_o0", "itemCode": "BrandGroup_o0_欧拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "欧拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/oula.png"}], "groupCode": "BrandGroup_o0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_p0", "itemCode": "BrandGroup_p0_Polestar极星", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "Polestar极星", "icon": "https://dimg04.c-ctrip.com/images/0R43g120009gwuo9r0192.png"}], "groupCode": "BrandGroup_p0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_启辰", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "启辰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qicheng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_起亚", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "起亚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "groupCode": "BrandGroup_q0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_日产", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_荣威", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "荣威", "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "groupCode": "BrandGroup_r0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_smart", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "smart", "icon": "//pages.c-ctrip.com/carisd/brandlogo/smart.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_上汽大通MAXUS", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "上汽大通MAXUS", "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}], "groupCode": "BrandGroup_s0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_坦克", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "坦克", "icon": "https://dimg04.c-ctrip.com/images/0yc3612000aoma5tvACF1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_特斯拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "特斯拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_腾势", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "腾势", "icon": "//pages.c-ctrip.com/carisd/brandlogo/shiteng.png"}], "groupCode": "BrandGroup_t0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_五菱汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "五菱汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/wulingqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_威马汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "威马汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weimaqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_蔚来", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "蔚来", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weilai.png"}], "groupCode": "BrandGroup_w0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小米汽车", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "小米汽车", "icon": "https://dimg04.c-ctrip.com/images/0yc1512000h1vkihnBA22.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小鹏", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "小鹏", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiaopengqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_现代", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "现代", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪佛兰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "雪佛兰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪铁龙", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "雪铁龙", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuetelong.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_依维柯", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "依维柯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yikewei.png"}], "groupCode": "BrandGroup_y0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_智己汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "智己汽车", "icon": "https://dimg04.c-ctrip.com/images/0yc3k12000h8zmxn19B00.png"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_魏牌", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "魏牌"}], "groupCode": "BrandGroup_z0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "0-50", "sortNum": 1, "name": "¥50以下", "groupCode": "Price", "itemCode": "Price_0-50"}, {"code": "50-100", "sortNum": 2, "name": "¥50-100", "groupCode": "Price", "itemCode": "Price_50-100"}, {"code": "100-200", "sortNum": 3, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-99999", "sortNum": 4, "name": "¥200以上", "groupCode": "Price", "itemCode": "Price_200-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "PickReturn", "quickSortNum": 1, "mark": "15分钟内", "itemCode": "PickReturn_StationPR", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "positionCode": "1", "name": "站内取车"}, {"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_FreePickupOnDoor", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 2, "name": "免费送车上门"}, {"sortNum": 2, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 3, "groupCode": "PickReturn", "itemCode": "PickReturn_CostPickupOnDoor", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "收费送车上门"}, {"sortNum": 4, "groupCode": "PickReturn", "itemCode": "PickReturn_FreeShuttle", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "免费接至门店取车"}, {"sortNum": 5, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupSelf", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "自行到店取车"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 4, "positionCode": "4", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "信用免押"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 6, "groupCode": "StoreService", "quickSortNum": 2, "positionCode": "4", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SelfService", "quickSortNum": 1, "positionCode": "3", "itemCode": "SelfService_Support", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 2, "groupCode": "SelfService", "itemCode": "SelfService_UnSupport", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "非自助取还"}], "groupCode": "SelfService", "shortName": "自助取还", "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "Promotion", "quickSortNum": 15, "positionCode": "2", "itemCode": "Promotion_3783", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "黄金贵宾"}], "groupCode": "Promotion", "shortName": "优惠活动", "bitwiseType": 2, "name": "优惠活动"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_8", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "台胞证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "门店评分", "sortNum": 8, "bitwiseType": 2, "name": "门店评分", "groupCode": "Comment", "filterItems": [{"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "12", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 2, "groupCode": "Comment", "itemCode": "Comment_4.5", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4.5分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}]}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "itemCode": "Vendor_0", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "携程租车中心"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "mark": "hot", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "positionCode": "6", "name": "携程优选", "icon": ""}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13088", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "一嗨租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13031", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "桐叶租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13032", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "明昊租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13082", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "凯美租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13092", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "易代步租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13094", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "骑仕租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13115", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "港梦超跑俱乐部租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13119", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "懒人行租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30004", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "铭轩租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30234", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "金晟租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30466", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "祥成租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30912", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "卢米租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31239", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "三亚佳途租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32231", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "泰信吉租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_46492", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "漫自由租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_47522", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "祥驰租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_53893", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "三亚世纪联合租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_58487", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "丰田海南出行"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61365", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "普信租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61924", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "龙麟租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62267", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "琼驰租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62863", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "文东租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_63836", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "租租侠租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_66708", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "旭辰租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_68692", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "利资租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_71599", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "日之星丰田租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_74373", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "海南中进租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_74573", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "器车出行"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000088", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "盛泽租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000258", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "三亚五二零租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000295", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "途新租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000361", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "海南点赞租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000372", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "田世租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000935", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "众横租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000981", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "轩宇租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15001073", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "海途租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15001137", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "毛蛋租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15001194", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "壹优租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15001199", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "逍遥租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15001351", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "海南松舍租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15001364", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "方达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15001824", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "三亚鼎豪租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15001863", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "琼城租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15002319", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "东辉租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15002466", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "鲁运昌通出行"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15002585", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "海南顺强租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15003234", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "三亚融泽租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15003280", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "东极出行"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80127", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "三亚新概念租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80431", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "小飞侠租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80535", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汇驰租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80545", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "盛兴隆租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80751", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "联动出行"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_81525", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "钰鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82105", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "三亚旅途中租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82819", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "助旅租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82843", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "名仕租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82909", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "五行租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_83528", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "钧通租车"}], "groupCode": "Vendor_1", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15003741", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "赛富德租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15003935", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "盛京出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15004136", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "礼享智行租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15004163", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "EVCARD租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15004317", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "三亚奇迹租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15004351", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "耀东方租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15004385", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "银天租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15004512", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "阿纳迪跑车俱乐部出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15004673", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "顺椰出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15004707", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "驰鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15004773", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "六六出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15004914", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "德普租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15005408", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "桐叶租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15005425", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "西十出行西十出行西十出行西十出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15005627", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "小简出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15005851", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "凯福华租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15006333", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "无忧九州租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15006533", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "车游天下租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15006884", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "宝嘉租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15006983", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "乐享好车出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15007295", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "三亚智衡租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15007296", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "鸿韵出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15007315", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "鑫路达出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15007420", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "三亚铭盛租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15007865", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "海南窝德租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15007870", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "锐冠租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15007893", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "爱信出行"}], "groupCode": "Vendor_2", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "2O94V47zrdc52838LEPw", "timeInterval": 1927.889892578125, "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 55, "modifySameVehicle": false, "vendorPriceList": [{"vehicleKey": "0_5241_", "adverts": 0, "card": 0, "commentInfo": {"overallRating": "5.0", "qCommentCount": 29, "maximumRating": 5, "level": "超棒", "commentLabel": "车辆新/车况好", "qExposed": "5.0", "hasComment": 1, "commentCount": 29}, "storeScore": 86.25, "pickOffFee": 0, "priceInfo": {"curOriginDPrice": 58, "oTPrice": 108, "naked": true, "deductInfos": [{"totalAmount": 3, "payofftype": 2}], "currentOriginalDailyPrice": 58, "priceType": 1, "currentDailyPrice": 55, "currentTotalPrice": 105, "localCurrencyCode": "CNY", "marginPrice": 0, "currentCurrencyCode": "CNY"}, "sortNum": 0, "vendorName": "西十出行西十出行西十出行西西", "rDistance": 0.6405, "pickUpFee": 0, "actId": "3783", "type": 0, "payModes": [2], "allTags": [{"category": 2, "sortNum": 44, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}, {"category": 2, "sortNum": 48, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "可升级免停运折旧费", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3872"}, {"title": "新款", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3789", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "3年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "免费取消", "category": 1, "type": 1, "code": "8", "sortNum": 25, "colorCode": "2", "labelCode": "3563", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 0}, {"title": "延误时免费留车", "category": 2, "type": 1, "code": "6", "sortNum": 47, "colorCode": "2", "labelCode": "3779", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0, "prefix": "租车中心"}, {"title": "长租特惠+券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3852", "groupCode": "MarketGroup1384", "amountTitle": "共减47", "groupId": 1, "mergeId": 0}, {"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 10000, "colorCode": "1", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 1}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"category": 3, "code": "3", "colorCode": "4", "groupCode": "MarketGroup1317", "labelCode": "3653", "sortNum": 0, "title": "里程限制", "type": 3, "groupId": 3}], "licenseTag": "", "fees": [{"amount": 55, "detail": [{"code": "1001", "amount": 58, "amountDesc": "¥58", "name": "租车费"}, {"code": "3783", "amount": 3, "amountDesc": "¥3", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥55", "originalDailyPrice": 58, "subAmount": 55, "name": "车辆租金", "amountStr": "¥55"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 105, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥108", "subAmount": 108, "name": "总价", "amountStr": "¥105"}], "couId": "", "distance": 0, "modifySameStore": false, "pStoreRouteDesc": "携程租车中心取车", "rStoreRouteDesc": "店员免费上门送取车", "ctripVehicleCode": "5241", "secretBox": false, "cyVendorName": "西十出行西十出行西十出行西十出行", "vehicleGroup": 2, "cashbackPre": "", "isSelect": false, "priceDailys": [], "adjustPriceInfo": {}, "freeDeposit": 0, "showVendor": false, "isOrderVehicle": false, "reference": {"fType": 0, "isSelect": false, "noDepositFilter": {}, "rCityId": 43, "rCType": 2, "vendorVehicleCode": "13849_7680_pupai", "pickWayInfo": 1, "packageLevel": "BAS", "pCityId": 43, "vcExtendRequest": {"vendorVehicleId": "0"}, "pStoreNav": "携程租车中心取车", "vehicleCode": "0", "isdShareInfo": {"orginaltotal": 108, "recommendOrder": 0, "mergeId": 0, "vdegree": "0", "rectype": 1, "totalDailyPrice": 55, "grantedcode": "", "isrec": false, "cvid": 5241, "rentalamount": 55}, "klbVersion": 1, "platform": 0, "bizVendorCode": "SD12615", "elct": 1, "pLevel": 8603, "adjustRuleId": "", "vehicleDegree": "0", "gsId": 0, "pCType": 2, "priceType": 1, "isEasyLife": true, "klbPId": 112911, "rStoreNav": "店员免费上门送取车", "priceVersion": "SH-PRICEVERSION_Nzk3NjcyXzUyNDFfMV81OF81OF81OF8xMDguMDBfNTUuMF8xMDUuMF8wXzBfMC4wXzAuMF8zMC4wMF8yMC4wMF8wLjAwXzAuMDBfNDc1NzIzMTY=", "alipay": false, "vendorCode": "15005425", "productCode": "SD12615_0_797672_797672", "pLev": 1368452, "adjustVersion": "", "sendTypeForPickOffCar": 0, "payMode": 2, "pRc": 1, "packageType": 1, "kPSId": 797672, "age": 30, "rCoup": 0, "kRSId": 797672, "packageId": "sec", "freeIllegalDeposit": false, "rLev": 1368452, "pStoreCode": "797672", "pickUpOnDoor": true, "aType": 0, "kVId": 15005425, "sortInfo": {"p": "1", "s": "100.0", "c": "43"}, "kVehicleId": 5241, "comPriceCode": "[c]", "dropOffOnDoor": true, "returnWayInfo": 1, "creditFreeCarDeposit": false, "hot": 0, "labels": [{"category": 3, "sortNum": 10000, "amountTitle": "已减3", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}], "hotType": 0, "rRc": 0, "klb": 1, "promtId": 0, "rStoreCode": "797672", "vendorSupportZhima": true, "sendTypeForPickUpCar": 0, "skuId": 47572316, "rLevel": 8603, "newEnergy": 1}, "easyLifeInfo": {"isEasyLife": true}, "isMinTPriceVendor": true, "addProducts": [], "platformCode": "0"}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减3", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "5241", "highestPrice": 617, "pWay": "可选：免费站内取还车", "minDPrice": 55, "vehicleKey": "0_5241_", "hot": 0, "minTPrice": 105, "lowestDistance": 8.3127, "group": 0, "type": 0, "sortNum": 70, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD12615_0_797672_797672"], "introduce": "当前车型最低价"}, "minDOrinPrice": 58, "isEasy": true, "productTopInfo": 2, "maximumCommentCount": 52131, "isCredit": true, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********2", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "minTOrinPrice": 108, "logicIndex": 0, "isGroup": false, "renderUniqId": "5241_1_2_105_55_58_信用免押_easyLife_黄金贵宾已减3"}, {"groupSort": 0, "lowestPrice": 55, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 55, "detail": [{"code": "1001", "amount": 58, "amountDesc": "¥58", "name": "租车费"}, {"code": "3783", "amount": 3, "amountDesc": "¥3", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥55", "originalDailyPrice": 58, "subAmount": 55, "name": "车辆租金", "amountStr": "¥55"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 105, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥108", "subAmount": 108, "name": "总价", "amountStr": "¥105"}], "reference": {"vehicleCode": "0", "rStoreCode": "2961", "packageId": "Secure", "pLev": 918184, "comPriceCode": "eyJzZWxsZXJpZCI6MTIwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6ODUzNiwiYWN0Z2V0aWQiOjg1MzYsImFjdG9mZmlkIjo4NTM2LCJjYXJ0eXBlaWQiOjE4NTM2LCJ0b3RhbCI6MTA4LCJ0aW1lIjoxNzM2NDE0OTEyfQ==", "bizVendorCode": "SD3012", "pStoreCode": "2961", "packageType": 1, "priceVersion": "SH-PRICEVERSION_Mjk2MV80MTM5XzFfNTguMF81OC4wXzAuMF8xMDguMF81NS4wXzEwNS4wXzBfMF8wLjBfMC4wXzMwLjBfMjAuMF8wXzBfMjI3OTc3", "sendTypeForPickUpCar": 0, "skuId": 227977, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 918184, "vendorCode": "13032", "vendorVehicleCode": "18536"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13031", "vehicleCode": "0", "packageType": 1, "pStoreCode": "78", "skuId": 152539, "bizVendorCode": "SD14450"}}, {"reference": {"vendorCode": "15005425", "vehicleCode": "0", "packageType": 1, "pStoreCode": "797672", "skuId": 46010546, "bizVendorCode": "SD12615"}}, {"reference": {"vendorCode": "13082", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183936", "skuId": 6901044, "bizVendorCode": "SD3926"}}, {"reference": {"vendorCode": "46492", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106965", "skuId": 28097714, "bizVendorCode": "SD3933"}}, {"reference": {"vendorCode": "15007865", "vehicleCode": "0", "packageType": 0, "pStoreCode": "2364391", "skuId": 76069855, "bizVendorCode": "SD15262"}}, {"reference": {"vendorCode": "80431", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114886", "skuId": 1913476, "bizVendorCode": "SD4849"}}, {"reference": {"vendorCode": "53893", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107119", "skuId": 1860920, "bizVendorCode": "SD3942"}}, {"reference": {"vendorCode": "13094", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188084", "skuId": 6900280, "bizVendorCode": "SD3866"}}, {"reference": {"vendorCode": "15006333", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2239938", "skuId": 77319005, "bizVendorCode": "SD13557"}}, {"reference": {"vendorCode": "13119", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193947", "skuId": 30541211, "bizVendorCode": "SD4201"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减3", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "4139", "highestPrice": 83, "rCoup": 0, "minDPrice": 55, "pWay": "可选：免费站内取还车", "vehicleKey": "0_4139_", "hot": 0, "minTPrice": 105, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 0, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3012_0_2961_2961"], "introduce": "当前车型最低价"}, "minDOrinPrice": 58, "isEasy": true, "isCredit": true, "maximumCommentCount": 52788, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 11, "minTOrinPrice": 108, "logicIndex": 1, "isGroup": false, "renderUniqId": "4139_11__105_55_58_信用免押_easyLife_黄金贵宾已减3"}, {"groupSort": 0, "lowestPrice": 207, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 207, "detail": [{"code": "1001", "amount": 218, "amountDesc": "¥218", "name": "租车费"}, {"code": "3783", "amount": 11, "amountDesc": "¥11", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥207", "originalDailyPrice": 218, "subAmount": 207, "name": "车辆租金", "amountStr": "¥207"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 257, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥268", "subAmount": 268, "name": "总价", "amountStr": "¥257"}], "reference": {"vehicleCode": "0", "rStoreCode": "107059", "packageId": "", "pLev": 24801, "comPriceCode": "[c]", "bizVendorCode": "SD3987", "pStoreCode": "107059", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3MDU5XzE3NjIyXzFfMjE4XzIxOF8yMThfMjY4LjAwXzIwNy4wXzI1Ny4wXzBfMF8wLjBfMC4wXzMwLjAwXzIwLjAwXzAuMDBfMC4wMF81OTEwNjgzOQ==", "sendTypeForPickUpCar": 0, "skuId": 59106839, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24801, "vendorCode": "63836", "vendorVehicleCode": "59106839"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15001364", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181671", "skuId": 54498515, "bizVendorCode": "SD13705"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 24649409, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "15004317", "vehicleCode": "0", "packageType": 1, "pStoreCode": "309366", "skuId": 61064956, "bizVendorCode": "SD11430"}}, {"reference": {"vendorCode": "81525", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107474", "skuId": 29208691, "bizVendorCode": "SD4102"}}, {"reference": {"vendorCode": "15007315", "vehicleCode": "0", "packageType": 0, "pStoreCode": "1893900", "skuId": 62926655, "bizVendorCode": "SD14637"}}, {"reference": {"vendorCode": "15003741", "vehicleCode": "0", "packageType": 0, "pStoreCode": "250168", "skuId": 44837990, "bizVendorCode": "SD3848"}}, {"reference": {"vendorCode": "15000295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133807", "skuId": 45470118, "bizVendorCode": "SD7027"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减11", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "17622", "highestPrice": 760, "rCoup": 0, "minDPrice": 207, "pWay": "可选：免费站内取还车", "vehicleKey": "0_17622_", "hot": 0, "minTPrice": 257, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 1, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3987_0_107059_107059"], "introduce": "当前车型最低价"}, "minDOrinPrice": 218, "isEasy": true, "isCredit": true, "maximumCommentCount": 16013, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 8, "minTOrinPrice": 268, "logicIndex": 2, "isGroup": false, "renderUniqId": "17622_8__257_207_218_信用免押_easyLife_黄金贵宾已减11"}, {"groupSort": 5, "lowestPrice": 188, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 188, "detail": [{"code": "1001", "amount": 198, "amountDesc": "¥198", "name": "租车费"}, {"code": "3783", "amount": 10, "amountDesc": "¥10", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥188", "originalDailyPrice": 198, "subAmount": 188, "name": "车辆租金", "amountStr": "¥188"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 248, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥258", "subAmount": 258, "name": "总价", "amountStr": "¥248"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "packageId": "sec", "pLev": 1648907, "comPriceCode": "[c]", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzE3NTYxXzFfMTk4XzE5OF8xOThfMjU4LjAwXzE4OC4wXzI0OC4wXzBfMF8wLjBfMC4wXzQwLjAwXzIwLjAwXzAuMDBfMC4wMF81NTE3MjA0OA==", "sendTypeForPickUpCar": 0, "skuId": 55172048, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1648907, "vendorCode": "13092", "vendorVehicleCode": "85589_32543_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 0, "pStoreCode": "188937", "skuId": 68748135, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "13094", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188084", "skuId": 6900320, "bizVendorCode": "SD3866"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减10", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "17561", "highestPrice": 216, "rCoup": 0, "minDPrice": 188, "pWay": "可选：免费站内取还车", "vehicleKey": "0_17561_", "hot": 0, "minTPrice": 248, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 2, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 198, "isEasy": true, "isCredit": true, "maximumCommentCount": 52131, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 3, "minTOrinPrice": 258, "logicIndex": 3, "isGroup": true, "renderUniqId": "17561_3__248_188_198_信用免押_easyLife_黄金贵宾已减10"}, {"groupSort": 6, "lowestPrice": 188, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 188, "detail": [{"code": "1001", "amount": 198, "amountDesc": "¥198", "name": "租车费"}, {"code": "3783", "amount": 10, "amountDesc": "¥10", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥188", "originalDailyPrice": 198, "subAmount": 188, "name": "车辆租金", "amountStr": "¥188"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 248, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥258", "subAmount": 258, "name": "总价", "amountStr": "¥248"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "packageId": "sec", "pLev": 1648907, "comPriceCode": "[c]", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzQ2NjNfMV8xOThfMTk4XzE5OF8yNTguMDBfMTg4LjBfMjQ4LjBfMF8wXzAuMF8wLjBfNDAuMDBfMjAuMDBfMC4wMF8wLjAwXzU1MTcyNDU0", "sendTypeForPickUpCar": 0, "skuId": 55172454, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1648907, "vendorCode": "13092", "vendorVehicleCode": "85590_44737_pupai"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减10", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "4663", "highestPrice": 188, "rCoup": 0, "minDPrice": 188, "pWay": "可选：免费站内取还车", "vehicleKey": "0_4663_", "hot": 0, "minTPrice": 248, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 3, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 198, "isEasy": true, "isCredit": true, "maximumCommentCount": 44867, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "minTOrinPrice": 258, "logicIndex": 4, "isGroup": true, "renderUniqId": "4663_1__248_188_198_信用免押_easyLife_黄金贵宾已减10"}, {"groupSort": 4, "lowestPrice": 188, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 188, "detail": [{"code": "1001", "amount": 198, "amountDesc": "¥198", "name": "租车费"}, {"code": "3783", "amount": 10, "amountDesc": "¥10", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥188", "originalDailyPrice": 198, "subAmount": 188, "name": "车辆租金", "amountStr": "¥188"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 248, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥258", "subAmount": 258, "name": "总价", "amountStr": "¥248"}], "reference": {"vehicleCode": "0", "rStoreCode": "326061", "packageId": "sec", "pLev": 790777, "comPriceCode": "[c]ODE3ODI1NjAwfDIwfDAuMDEtMTAyNS0wMDA6MCAwMDo4JiYxMCYxOWUmMTUmdHJ1MTAwMTgmJHw5OCYxJjEmMTAwMyY5OCQxLjAwJjEmMjAwJDEwMjAuMCY0MC4wMiYxMC4wMDAwJjQyNS0wJHwyMCAxMDoxLTEwMCYyMDAwOjAxLTExMjUtMDAwOjAgMTA6MjUtMDB8MjAgMTc6MS0wOTUAAAAzMjo0", "bizVendorCode": "SD11813", "pStoreCode": "326061", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MzI2MDYxXzUyODJfMV8xOThfMTk4XzE5OF8yNTguMDBfMTg4LjBfMjQ4LjBfMF8wXzAuMF8wLjBfNDAuMDBfMjAuMDBfMC4wMF8wLjAwXzI1NjA4MTc4", "sendTypeForPickUpCar": 0, "skuId": 25608178, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 790777, "vendorCode": "15004673", "vendorVehicleCode": "6414_32519_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902506, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901326, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "15001073", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174846", "skuId": 44060318, "bizVendorCode": "SD8032"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减10", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "5282", "highestPrice": 245, "rCoup": 0, "minDPrice": 188, "pWay": "可选：免费站内取还车", "vehicleKey": "0_5282_", "hot": 0, "minTPrice": 248, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 5, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11813_0_326061_326061"], "introduce": "当前车型最低价"}, "minDOrinPrice": 198, "isEasy": true, "isCredit": true, "maximumCommentCount": 44867, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 4, "minTOrinPrice": 258, "logicIndex": 5, "isGroup": true, "renderUniqId": "5282_4__248_188_198_信用免押_easyLife_黄金贵宾已减10"}, {"groupSort": 3, "lowestPrice": 188, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 188, "detail": [{"code": "1001", "amount": 198, "amountDesc": "¥198", "name": "租车费"}, {"code": "3783", "amount": 10, "amountDesc": "¥10", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥188", "originalDailyPrice": 198, "subAmount": 188, "name": "车辆租金", "amountStr": "¥188"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 248, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥258", "subAmount": 258, "name": "总价", "amountStr": "¥248"}], "reference": {"vehicleCode": "0", "rStoreCode": "136697", "packageId": "sec", "pLev": 1591899, "comPriceCode": "[c]MTY1NjYyODAwfDIwfDAuMDEtMTAyNS0wMDA6MCAwMDo4JiYxMCYxOXNlJjEmZmFsfDEwMDk4JiQxOTgmMSYxJjEwMDMxOTgkMC4wMCYxJjIwMCQxJjIwLjEmNDAwMDImNDAuMC4wMCYwMjUtMCR8MjAgMTAwMS0xMDAmMjowMDowMS0xMDI1LTowMDoxIDEwMDI1LTAwfDI5IDE3MDEtMDQ1AAA6MzI6", "bizVendorCode": "SD7103", "pStoreCode": "136697", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTM2Njk3XzUyODNfMV8xOThfMTk4XzE5OF8yNTguMDBfMTg4LjBfMjQ4LjBfMF8wXzAuMF8wLjBfNDAuMDBfMjAuMDBfMC4wMF8wLjAwXzYyODAxNjU2", "sendTypeForPickUpCar": 0, "skuId": 62801656, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1591899, "vendorCode": "15000361", "vendorVehicleCode": "93298_3911_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "80545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107268", "skuId": 1858109, "bizVendorCode": "SD4092"}}, {"reference": {"vendorCode": "74373", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116523", "skuId": 1971758, "bizVendorCode": "SD6573"}}, {"reference": {"vendorCode": "15001194", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175686", "skuId": 4545504, "bizVendorCode": "SD8150"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25608157, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "15001364", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181671", "skuId": 54528734, "bizVendorCode": "SD13705"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902505, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901328, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "15007870", "vehicleCode": "0", "packageType": 0, "pStoreCode": "2381555", "skuId": 76178621, "bizVendorCode": "SD15267"}}, {"reference": {"vendorCode": "15004385", "vehicleCode": "0", "packageType": 1, "pStoreCode": "312488", "skuId": 65353709, "bizVendorCode": "SD11501"}}, {"reference": {"vendorCode": "15004317", "vehicleCode": "0", "packageType": 1, "pStoreCode": "309366", "skuId": 61060049, "bizVendorCode": "SD11430"}}, {"reference": {"vendorCode": "30004", "vehicleCode": "0", "packageType": 0, "pStoreCode": "106808", "skuId": 1859332, "bizVendorCode": "SD3705"}}, {"reference": {"vendorCode": "15003741", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250168", "skuId": 6941536, "bizVendorCode": "SD3848"}}, {"reference": {"vendorCode": "15003280", "vehicleCode": "0", "packageType": 1, "pStoreCode": "287666", "skuId": 55479600, "bizVendorCode": "SD10315"}}, {"reference": {"vendorCode": "15002585", "vehicleCode": "0", "packageType": 1, "pStoreCode": "192639", "skuId": 70460608, "bizVendorCode": "SD14949"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减10", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "5283", "highestPrice": 748, "rCoup": 0, "minDPrice": 188, "pWay": "可选：免费站内取还车", "vehicleKey": "0_5283_", "hot": 0, "minTPrice": 248, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 6, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD7103_0_136697_136697"], "introduce": "当前车型最低价"}, "minDOrinPrice": 198, "isEasy": true, "isCredit": true, "maximumCommentCount": 44867, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 15, "minTOrinPrice": 258, "logicIndex": 6, "isGroup": true, "renderUniqId": "5283_15__248_188_198_信用免押_easyLife_黄金贵宾已减10"}, {"groupSort": 1, "lowestPrice": 188, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 188, "detail": [{"code": "1001", "amount": 198, "amountDesc": "¥198", "name": "租车费"}, {"code": "3783", "amount": 10, "amountDesc": "¥10", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥188", "originalDailyPrice": 198, "subAmount": 188, "name": "车辆租金", "amountStr": "¥188"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 248, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥258", "subAmount": 258, "name": "总价", "amountStr": "¥248"}], "reference": {"vehicleCode": "0", "rStoreCode": "17493", "packageId": "Secure", "pLev": -1, "comPriceCode": "eyJzZWxsZXJpZCI6MTIwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6OTM5MSwiYWN0Z2V0aWQiOjkzOTEsImFjdG9mZmlkIjo5MzkxLCJjYXJ0eXBlaWQiOjE5MzUxLCJ0b3RhbCI6MjU4LCJ0aW1lIjoxNzM2NDE1MTEwfQ==", "bizVendorCode": "SD3012", "pStoreCode": "17493", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTc0OTNfMTczOTFfMV8xOTguMF8xOTguMF8wLjBfMjU4LjBfMTg4LjBfMjQ4LjBfMF8wXzAuMF8wLjBfNDAuMF8yMC4wXzBfMF82NTk1Mzg2OA==", "sendTypeForPickUpCar": 0, "skuId": 65953868, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "13032", "vendorVehicleCode": "19351"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "82909", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115295", "skuId": 57104300, "bizVendorCode": "SD5066"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 62802062, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "31239", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115935", "skuId": 24585737, "bizVendorCode": "SD6272"}}, {"reference": {"vendorCode": "15004136", "vehicleCode": "0", "packageType": 1, "pStoreCode": "274541", "skuId": 69856669, "bizVendorCode": "SD11239"}}, {"reference": {"vendorCode": "82843", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115278", "skuId": 54883718, "bizVendorCode": "SD5061"}}, {"reference": {"vendorCode": "15001199", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175820", "skuId": 62721149, "bizVendorCode": "SD8155"}}, {"reference": {"vendorCode": "15007420", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1957775", "skuId": 64711921, "bizVendorCode": "SD14800"}}, {"reference": {"vendorCode": "15000372", "vehicleCode": "0", "packageType": 0, "pStoreCode": "308155", "skuId": 77351660, "bizVendorCode": "SD7114"}}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 3868574, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "80545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107268", "skuId": 69851888, "bizVendorCode": "SD4092"}}, {"reference": {"vendorCode": "15000935", "vehicleCode": "0", "packageType": 1, "pStoreCode": "265301", "skuId": 8549451, "bizVendorCode": "SD7678"}}, {"reference": {"vendorCode": "15001194", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175686", "skuId": 5462232, "bizVendorCode": "SD8150"}}, {"reference": {"vendorCode": "15002585", "vehicleCode": "0", "packageType": 1, "pStoreCode": "192639", "skuId": 6824983, "bizVendorCode": "SD14949"}}, {"reference": {"vendorCode": "15000295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133807", "skuId": 2317191, "bizVendorCode": "SD7027"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25567970, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "81525", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107474", "skuId": 24587725, "bizVendorCode": "SD4102"}}, {"reference": {"vendorCode": "15001364", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181671", "skuId": 54528776, "bizVendorCode": "SD13705"}}, {"reference": {"vendorCode": "46492", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106965", "skuId": 28097917, "bizVendorCode": "SD3933"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901332, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "15007315", "vehicleCode": "0", "packageType": 0, "pStoreCode": "1893900", "skuId": 62920509, "bizVendorCode": "SD14637"}}, {"reference": {"vendorCode": "15002319", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188920", "skuId": 70449100, "bizVendorCode": "SD9323"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333209, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "15004317", "vehicleCode": "0", "packageType": 1, "pStoreCode": "309366", "skuId": 61059993, "bizVendorCode": "SD11430"}}, {"reference": {"vendorCode": "30466", "vehicleCode": "0", "packageType": 0, "pStoreCode": "133340", "skuId": 76388866, "bizVendorCode": "SD6966"}}, {"reference": {"vendorCode": "15007295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1851872", "skuId": 63076833, "bizVendorCode": "SD14617"}}, {"reference": {"vendorCode": "15002466", "vehicleCode": "0", "packageType": 1, "pStoreCode": "192668", "skuId": 7352052, "bizVendorCode": "SD9479"}}, {"reference": {"vendorCode": "15003234", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2370159", "skuId": 75764970, "bizVendorCode": "SD10267"}}, {"reference": {"vendorCode": "13088", "vehicleCode": "0", "packageType": 0, "pStoreCode": "138334", "skuId": 14001464, "bizVendorCode": "SD6991"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减10", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "17391", "highestPrice": 289, "rCoup": 0, "minDPrice": 188, "pWay": "可选：免费站内取还车", "vehicleKey": "0_17391_", "hot": 0, "minTPrice": 248, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 7, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3012_0_17493_17493"], "introduce": "当前车型最低价"}, "minDOrinPrice": 198, "isEasy": true, "isCredit": true, "maximumCommentCount": 19229, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 29, "minTOrinPrice": 258, "logicIndex": 7, "isGroup": true, "renderUniqId": "17391_29__248_188_198_信用免押_easyLife_黄金贵宾已减10"}, {"groupSort": 2, "lowestPrice": 188, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 188, "detail": [{"code": "1001", "amount": 198, "amountDesc": "¥198", "name": "租车费"}, {"code": "3783", "amount": 10, "amountDesc": "¥10", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥188", "originalDailyPrice": 198, "subAmount": 188, "name": "车辆租金", "amountStr": "¥188"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 248, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥258", "subAmount": 258, "name": "总价", "amountStr": "¥248"}], "reference": {"vehicleCode": "0", "rStoreCode": "184270", "packageId": "sec", "pLev": 1230776, "comPriceCode": "[c]MTU2fDczNTh8MjAyMC4wMC0xMCA1LTAxMDowMDAwOjAmJjEmJjE5OCYxODh0cnVlMDAxJiYkfDE4JjE5MSYxOTAzJjE4JDEwMDAmMiYyMC4kMTAwMC4wMDQwLjAyJjEmLjAwJDAmNDA1LTAxfDIwMjEwOjAtMTAgJjIwMjA6MDAtMTEgNS0wMTA6MDAxMDowNS0wMXwyMDIxNzozLTA5IAAAAAAyOjQ1", "bizVendorCode": "SD8841", "pStoreCode": "184270", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTg0MjcwXzU1MjRfMV8xOThfMTk4XzE5OF8yNTguMDBfMTg4LjBfMjQ4LjBfMF8wXzAuMF8wLjBfNDAuMDBfMjAuMDBfMC4wMF8wLjAwXzczNTgxNTY=", "sendTypeForPickUpCar": 0, "skuId": 7358156, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1230776, "vendorCode": "15001863", "vendorVehicleCode": "3447_49572_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 62801768, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "31239", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115935", "skuId": 24585667, "bizVendorCode": "SD6272"}}, {"reference": {"vendorCode": "15001199", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175820", "skuId": 43673561, "bizVendorCode": "SD8155"}}, {"reference": {"vendorCode": "15000295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133807", "skuId": 2317188, "bizVendorCode": "SD7027"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25608584, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "81525", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107474", "skuId": 25009720, "bizVendorCode": "SD4102"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902509, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901330, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "15000372", "vehicleCode": "0", "packageType": 0, "pStoreCode": "308155", "skuId": 76073621, "bizVendorCode": "SD7114"}}, {"reference": {"vendorCode": "15004385", "vehicleCode": "0", "packageType": 1, "pStoreCode": "312488", "skuId": 66211839, "bizVendorCode": "SD11501"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333216, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "15007295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1851872", "skuId": 66149637, "bizVendorCode": "SD14617"}}, {"reference": {"vendorCode": "82843", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115278", "skuId": 54883690, "bizVendorCode": "SD5061"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减10", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "5524", "highestPrice": 376, "rCoup": 0, "minDPrice": 188, "pWay": "可选：免费站内取还车", "vehicleKey": "0_5524_", "hot": 0, "minTPrice": 248, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 8, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD8841_0_184270_184270"], "introduce": "当前车型最低价"}, "minDOrinPrice": 198, "isEasy": true, "isCredit": true, "maximumCommentCount": 44867, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 14, "minTOrinPrice": 258, "logicIndex": 8, "isGroup": true, "renderUniqId": "5524_14__248_188_198_信用免押_easyLife_黄金贵宾已减10"}, {"groupSort": 0, "lowestPrice": 93, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 93, "detail": [{"code": "1001", "amount": 98, "amountDesc": "¥98", "name": "租车费"}, {"code": "3783", "amount": 5, "amountDesc": "¥5", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥93", "originalDailyPrice": 98, "subAmount": 93, "name": "车辆租金", "amountStr": "¥93"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 143, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥148", "subAmount": 148, "name": "总价", "amountStr": "¥143"}], "reference": {"vehicleCode": "0", "rStoreCode": "107059", "packageId": "", "pLev": 24801, "comPriceCode": "[c]NTAxfDE4NjF8MjAyMC4wMC0xMCA1LTAxMDowMDAwOjAmMSZmJjk4JiY5OCZhbHNlMDEmMSR8MTA5OCQxJjk4JjEmMjAwMDMmMjAuMC4wMCYwMiYxMCQxMDAwJjMmMzAuJHwyMDAuMDAxLTEwMjUtMDAwOjAgMTA6MjUtMDAmMjAgMTA6MS0xMTB8MjAwMDowMS0wOTI1LTAzMjo0IDE3OgAAAAA1AAAA", "bizVendorCode": "SD3987", "pStoreCode": "107059", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3MDU5XzE0NDRfMV85OF85OF85OF8xNDguMDBfOTMuMF8xNDMuMF8wXzBfMC4wXzAuMF8zMC4wMF8yMC4wMF8wLjAwXzAuMDBfMTg2MTUwMQ==", "sendTypeForPickUpCar": 0, "skuId": 1861501, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24801, "vendorCode": "63836", "vendorVehicleCode": "1861501"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "58487", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114289", "skuId": 1913681, "bizVendorCode": "SD4374"}}, {"reference": {"vendorCode": "15001364", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181671", "skuId": 54923800, "bizVendorCode": "SD13705"}}, {"reference": {"vendorCode": "15007870", "vehicleCode": "0", "packageType": 0, "pStoreCode": "2381555", "skuId": 76178677, "bizVendorCode": "SD15267"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901275, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "15000372", "vehicleCode": "0", "packageType": 1, "pStoreCode": "308155", "skuId": 77087242, "bizVendorCode": "SD7114"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减5", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "1444", "highestPrice": 337, "rCoup": 0, "minDPrice": 93, "pWay": "可选：店员免费上门送取车", "vehicleKey": "0_1444_", "hot": 0, "minTPrice": 143, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 4, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3987_0_107059_107059"], "introduce": "当前车型最低价"}, "minDOrinPrice": 98, "isEasy": true, "isCredit": true, "maximumCommentCount": 16013, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0.26323208, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 6, "minTOrinPrice": 148, "logicIndex": 9, "isGroup": false, "renderUniqId": "1444_6__143_93_98_信用免押_easyLife_黄金贵宾已减5"}, {"groupSort": 0, "lowestPrice": 55, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 55, "detail": [{"code": "1001", "amount": 58, "amountDesc": "¥58", "name": "租车费"}, {"code": "3783", "amount": 3, "amountDesc": "¥3", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥55", "originalDailyPrice": 58, "subAmount": 55, "name": "车辆租金", "amountStr": "¥55"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 105, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥108", "subAmount": 108, "name": "总价", "amountStr": "¥105"}], "reference": {"vehicleCode": "0", "rStoreCode": "161973", "packageId": "sec", "pLev": 98018, "comPriceCode": "[c]", "bizVendorCode": "SD3127", "pStoreCode": "161973", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTYxOTczXzE3NjM5XzFfNThfNThfNThfMTA4LjAwXzU1LjBfMTA1LjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzI0NjYyNDA4", "sendTypeForPickUpCar": 0, "skuId": 24662408, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 98018, "vendorCode": "15000088", "vendorVehicleCode": "2442_68213_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15006533", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2285298", "skuId": 72907010, "bizVendorCode": "SD7763"}}, {"reference": {"vendorCode": "30912", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115986", "skuId": 6718314, "bizVendorCode": "SD5349"}}, {"reference": {"vendorCode": "13119", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193947", "skuId": 47571756, "bizVendorCode": "SD4201"}}, {"reference": {"vendorCode": "13119", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193947", "skuId": 41070198, "bizVendorCode": "SD4201"}}, {"reference": {"vendorCode": "15000981", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174692", "skuId": 54326315, "bizVendorCode": "SD7724"}}, {"reference": {"vendorCode": "15004163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "408969", "skuId": 51721923, "bizVendorCode": "SD3047"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 69186769, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "15005627", "vehicleCode": "0", "packageType": 0, "pStoreCode": "1335279", "skuId": 58068032, "bizVendorCode": "SD12826"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减3", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "17639", "highestPrice": 551, "rCoup": 0, "minDPrice": 55, "pWay": "可选：免费站内取还车", "vehicleKey": "0_17639_", "hot": 0, "minTPrice": 105, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 9, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3127_0_161973_161973"], "introduce": "当前车型最低价"}, "minDOrinPrice": 58, "isEasy": true, "isCredit": true, "maximumCommentCount": 52788, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 9, "minTOrinPrice": 108, "logicIndex": 10, "isGroup": false, "renderUniqId": "17639_9__105_55_58_信用免押_easyLife_黄金贵宾已减3"}, {"groupSort": 0, "lowestPrice": 112, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 112, "detail": [{"code": "1001", "amount": 118, "amountDesc": "¥118", "name": "租车费"}, {"code": "3783", "amount": 6, "amountDesc": "¥6", "name": "黄金贵宾"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥112", "originalDailyPrice": 118, "subAmount": 112, "name": "车辆租金", "amountStr": "¥112"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 162, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥168", "subAmount": 168, "name": "总价", "amountStr": "¥162"}], "reference": {"vehicleCode": "0", "rStoreCode": "107059", "packageId": "", "pLev": 24801, "comPriceCode": "[c]********************************************************************************************************************************************************************************************************************************", "bizVendorCode": "SD3987", "pStoreCode": "107059", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3MDU5XzUzMjVfMV8xMThfMTE4XzExOF8xNjguMDBfMTEyLjBfMTYyLjBfMF8wXzAuMF8wLjBfMzAuMDBfMjAuMDBfMC4wMF8wLjAwXzY4OTAxNzk5", "sendTypeForPickUpCar": 0, "skuId": 68901799, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24801, "vendorCode": "63836", "vendorVehicleCode": "68901799"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333356, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 46106516, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "80535", "vehicleCode": "0", "packageType": 1, "pStoreCode": "248685", "skuId": 6833586, "bizVendorCode": "SD4091"}}, {"reference": {"vendorCode": "30004", "vehicleCode": "0", "packageType": 0, "pStoreCode": "106808", "skuId": 2398304, "bizVendorCode": "SD3705"}}, {"reference": {"vendorCode": "15006983", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2370390", "skuId": 74762220, "bizVendorCode": "SD14266"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减6", "groupCode": "MarketGroup1369", "code": "30", "title": "黄金贵宾", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3783"}, "vehicleCode": "5325", "highestPrice": 921, "rCoup": 0, "minDPrice": 112, "pWay": "可选：店员免费上门送取车", "vehicleKey": "0_5325_", "hot": 0, "minTPrice": 162, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 11, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3987_0_107059_107059"], "introduce": "当前车型最低价"}, "minDOrinPrice": 118, "isEasy": true, "isCredit": true, "maximumCommentCount": 16013, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 6, "minTOrinPrice": 168, "logicIndex": 11, "isGroup": false, "renderUniqId": "5325_6__162_112_118_信用免押_easyLife_黄金贵宾已减6"}], "groupCode": "all", "dailyPrice": 45, "hasResult": true}, {"sortNum": 0, "groupName": "无忧租一口价", "hasResult": true, "groupCode": "prep", "dailyPrice": 178, "groupAction": 1}, {"sortNum": 1, "groupName": "经济轿车", "hasResult": true, "groupCode": "2", "dailyPrice": 45, "groupAction": 0}, {"sortNum": 3, "groupName": "新能源", "hasResult": true, "groupCode": "newenergy", "dailyPrice": 55, "groupAction": 0}, {"sortNum": 4, "groupName": "舒适轿车", "hasResult": true, "groupCode": "3", "dailyPrice": 93, "groupAction": 0}, {"sortNum": 5, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 64, "groupAction": 0}, {"sortNum": 6, "groupName": "商务车", "hasResult": true, "groupCode": "4", "dailyPrice": 93, "groupAction": 0}, {"sortNum": 7, "groupName": "豪华轿车", "hasResult": true, "groupCode": "5", "dailyPrice": 207, "groupAction": 0}, {"sortNum": 8, "groupName": "跑车", "hasResult": true, "groupCode": "9", "dailyPrice": 159, "groupAction": 0}, {"sortNum": 9, "groupName": "房车", "hasResult": true, "groupCode": "11", "dailyPrice": 800, "groupAction": 0}, {"sortNum": 11, "groupName": "皮卡", "hasResult": true, "groupCode": "10", "dailyPrice": 444, "groupAction": 0}], "resBodySize": 127699, "frontTraceInfo": {"vehicleGroupMap": {"2": 53, "3": 54, "4": 82, "5": 56, "6": 144, "9": 83, "10": 2, "11": 1, "prep": 23, "newenergy": 148}, "normalCount": 2161, "priceCount": 2161, "vehicleList": ["18010", "51087", "89216", "18021", "4980", "17298", "4740", "2438", "2437", "2799", "1466", "4972", "4610", "89202", "89685", "5704", "18236", "18116", "17383", "4870", "4750", "2451", "17380", "10634", "2448", "4629", "1474", "4741", "87179", "10631", "4866", "18118", "18488", "18004", "17394", "18243", "4761", "4883", "17390", "17391", "1491", "1129", "1488", "5601", "4515", "5604", "4758", "4879", "89251", "18572", "4771", "17480", "1140", "2227", "831", "1133", "712", "4763", "89139", "4526", "18586", "17378", "4781", "4660", "4661", "17373", "4663", "1151", "10623", "1149", "2477", "5500", "5502", "5503", "10620", "4899", "5504", "10622", "4659", "608", "10621", "609", "17464", "4790", "4670", "17346", "3101", "3460", "4309", "2247", "1399", "5512", "5517", "88054", "5410", "2384", "3469", "5402", "4555", "4676", "5524", "869", "5526", "4559", "5407", "4319", "4330", "18091", "873", "89979", "874", "1178", "4325", "5658", "4449", "4101", "5311", "5433", "4102", "1069", "768", "5303", "4577", "648", "5427", "4470", "4593", "4112", "5564", "5444", "5686", "5565", "1087", "1083", "2050", "1081", "775", "88789", "656", "4103", "5555", "85156", "658", "87578", "4588", "4106", "4107", "4228", "4108", "87571", "5570", "4482", "5572", "5694", "5453", "5332", "5696", "5576", "540", "300", "51276", "301", "86479", "51038", "4598", "85023", "4478", "5325", "4358", "5569", "4238", "5329", "86472", "4372", "4251", "4010", "5584", "4253", "5342", "5343", "4496", "5101", "5223", "4498", "5466", "3164", "3159", "315", "5214", "5698", "679", "4367", "4489", "5579", "5591", "4261", "5472", "4020", "6320", "5594", "5474", "5353", "5595", "5596", "5475", "5113", "4024", "6324", "6323", "4025", "3176", "18063", "18064", "682", "3169", "326", "5104", "5588", "4378", "5226", "5348", "329", "4139", "5349", "5107", "20357", "5482", "3180", "5241", "4031", "6333", "5245", "5367", "18271", "452", "456", "214", "335", "3059", "216", "4026", "85198", "5371", "5010", "5253", "4043", "5374", "5254", "5497", "5498", "4288", "5499", "340", "341", "587", "5368", "5007", "5249", "86395", "5141", "4053", "5388", "471", "474", "475", "476", "17725", "5258", "18936", "5138", "17727", "17965", "5151", "4062", "5272", "17972", "5396", "5154", "4066", "5277", "5158", "5270", "17619", "247", "248", "17736", "85520", "17616", "17732", "17614", "5283", "5163", "5164", "26", "5167", "6258", "5289", "28", "5047", "4191", "5281", "5282", "4193", "136", "137", "17827", "17941", "17700", "17943", "33", "5173", "5053", "4086", "5175", "5176", "17951", "4088", "5298", "5291", "5050", "267", "17718", "17719", "85786", "17835", "84694", "17837", "86990", "40", "17712", "5063", "6274", "5066", "5187", "5067", "5068", "49", "6271", "2900", "86668", "2905", "2904", "17801", "17922", "2901", "6288", "5191", "6281", "5193", "5194", "166", "287", "1820", "288", "289", "17817", "87620", "17931", "17811", "17812", "65", "6297", "290", "292", "173", "6294", "174", "175", "296", "2801", "88509", "177", "178", "17906", "2808", "73", "75", "182", "184", "86675", "17443", "89", "17440", "17561", "17441", "2706", "17567", "2705", "2704", "97", "98", "17453", "89286", "18544", "18782", "81327", "81320", "84988", "2721", "4907", "17308", "2729", "17424", "17425", "18999", "17427", "17306", "17554", "17555", "17550", "1404", "4916", "1644", "1400", "50982", "17678", "1407", "17437", "18768", "1406", "1405", "17640", "96748", "17764", "4805", "4928", "51724", "17647", "17651", "17652", "17774", "17650", "4936", "3603", "4816", "17655", "17414", "17898", "18747", "17779", "17416", "1437", "2889", "4826", "2888", "18957", "81152", "4940", "17507", "17628", "4943", "17622", "17865", "17631", "17753", "4960", "4958", "4716", "4959", "1446", "3502", "1444", "2895", "86710", "2893", "17639", "4831", "51633", "4953", "4832", "17996", "4834", "84890", "4835", "1449"], "easyLifeCount": 0, "zhimaCount": 2159, "vendorNames": ["车游天下租车", "丰田海南出行", "助旅租车", "宝嘉租车", "小飞侠租车", "途新租车", "海南窝德租车", "一嗨租车", "凯美租车", "众横租车", "耀东方租车", "六六出行", "海南松舍租车", "银天租车", "小简出行", "三亚佳途租车", "普信租车", "凯福华租车", "礼享智行租车", "利资租车", "阿纳迪跑车俱乐部出行", "鸿韵出行", "海南中进租车", "名仕租车", "方达租车", "泰信吉租车", "租租侠租车", "东极出行", "毛蛋租车", "德普租车", "三亚新概念租车", "骑仕租车", "卢米租车", "钰鑫租车", "轩宇租车", "琼驰租车", "田世租车", "三亚旅途中租车", "桐叶租车", "海南点赞租车", "盛京出行", "易代步租车", "汇驰租车", "漫自由租车", "东辉租车", "懒人行租车", "钧通租车", "盛泽租车", "文东租车", "赛富德租车", "鑫路达出行", "海途租车", "壹优租车", "金晟租车", "EVCARD租车", "港梦超跑俱乐部租车", "鲁运昌通出行", "明昊租车", "驰鑫租车", "祥驰租车", "琼城租车", "五行租车", "三亚铭盛租车", "三亚智衡租车", "三亚世纪联合租车", "祥成租车", "海南顺强租车", "三亚鼎豪租车", "乐享好车出行", "铭轩租车", "龙麟租车", "三亚奇迹租车", "顺椰出行", "三亚融泽租车", "三亚五二零租车", "联动出行", "日之星丰田租车", "锐冠租车", "旭辰租车", "西十出行西十出行西十出行西十出行", "逍遥租车", "盛兴隆租车", "器车出行", "无忧九州租车", "爱信出行"]}, "labelCodes": ["3783", "3563", "3510", "3872", "3696", "3653", "3697", "3698", "3731", "3810", "3679", "4229", "3779", "3757", "4243", "4222", "3495", "3504", "3548", "3827", "3503", "3547", "3502", "3828", "3501", "3709", "4236", "3509", "3788", "3789", "3866", "3746"], "isNoTopProduct": false, "quickFilter": [{"sortNum": 1, "groupCode": "PickReturn", "quickSortNum": 1, "mark": "15分钟内", "itemCode": "PickReturn_StationPR", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "positionCode": "1", "name": "站内取车"}, {"sortNum": 1, "groupCode": "Promotion", "quickSortNum": 15, "positionCode": "2", "itemCode": "Promotion_3783", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "黄金贵宾"}, {"sortNum": 1, "groupCode": "SelfService", "quickSortNum": 1, "positionCode": "3", "itemCode": "SelfService_Support", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 6, "groupCode": "StoreService", "quickSortNum": 2, "positionCode": "4", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "mark": "hot", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "positionCode": "6", "name": "携程优选", "icon": ""}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "8", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "9", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "10", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "12", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 5, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "13", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}], "promotMap": {}, "requestInfo": {"rLongitude": 109.414693, "rDate": "20250111100000", "age": 30, "pCityId": 43, "returnDate": "/Date(1736560800000+0800)/", "sourceCountryId": 1, "pLatitude": 18.303395, "rLatitude": 18.303395, "pLongitude": 109.414693, "pDate": "20250110100000", "rCityId": 43, "pickupLocationName": "凤凰国际机场", "returnLocationName": "凤凰国际机场", "pickupDate": "/Date(1736474400000+0800)/"}, "allVehicleCount": 498, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "id": 2, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "23:59", "lat": "18.308537", "lng": "109.413536", "name": "三亚凤凰机场店", "fromTime": "00:00"}, "checkResponseTime": 1736415167478.462, "vehicleList": [{"skylight": "部分车辆支持", "vehicleKey": "0_4139_", "luggageNo": 5, "carPlay": "部分车辆支持原厂互联/映射/CarLife/CarPlay", "displacement": "1.0T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5i12000c7kmh93066A.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "雪佛兰科沃兹", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/Type-C/SD", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "carPhone": true, "vehicleCode": "4139", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "雪佛兰科沃兹", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0612000d5pibur948E.png", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5212000c7mwubp7B39.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0l12000c7mwy55EE30.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000c7mwvvc7936.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3212000c7mx6u22402.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3012000c7mwv0sCA00.jpg?mark=yiche"], "transmissionType": 1, "brandName": "雪佛兰", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "雪佛兰", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "71", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "支持", "subGroupCode": "newenergy", "luggageNo": 5, "carPlay": "不支持", "vehicleKey": "0_17622_", "autoPark": true, "endurance": "工信部续航170km-240km", "fuelType": "增程式", "charge": "快充0.5小时,慢充7.9小时", "imageList": ["https://dimg04.c-ctrip.com/images/0RV1p12000c56d4bk1244.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "理想L7", "doorNo": 5, "autoParkDesc": {"type": 1, "typeDesc": "支持"}, "driveMode": "双电机四驱", "chargeInterface": "支持Type-C", "mediaTypes": [], "carPhone": true, "vehicleCode": "17622", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "理想L7", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4w12000cj9sakkFCD1.png?mark=yiche", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3d12000cj9sbdi81F9.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1q12000cj9s32sAAC2.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5312000cj9ry62C6DC.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4t12000cj9rzhs85EC.png?mark=yiche"], "transmissionType": 1, "brandName": "理想汽车", "oilType": 4, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "理想汽车", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "11", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_17561_", "luggageNo": 5, "carPlay": "支持CarPlay", "displacement": "2.0T-2.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4512000elvdljb183C.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=675&app_ver=10.5", "carPhone": true, "vehicleCode": "17561", "style": "2017/18款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4512000elvdljb183C.png?mark=yiche", "fuel": "95号或92号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0o12000c6eaivoD4D0.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2412000c6eahws4567.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2f12000c6eakzuA728.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1n12000c6eaj9265BC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0f12000c6eacfx9063.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_4663_", "luggageNo": 4, "carPlay": "不支持", "displacement": "2.4L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV1a12000etgoh8a9616.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX", "mediaTypes": [], "carPhone": false, "vehicleCode": "4663", "style": "2015款及以前", "carPhoneDesc": {"type": 0, "typeDesc": "不支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3312000chsx6nf2A98.jpg?mark=yiche", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放4个24寸行李箱", "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5k12000chsx38q8BA9.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6612000chswzfq875E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6n12000chsx6nnE676.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0112000chsxahk5DBD.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3412000chswz0nC16E.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_1444_", "luggageNo": 2, "carPlay": "部分车辆支持CarLife/CarPlay/原厂互联/映射/HUAWEIHiCar", "displacement": "2.0L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5912000c55lhvgC5CA.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "3", "zhName": "丰田凯美瑞", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7724&app_ver=10.5", "vehicleCode": "1444", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "丰田凯美瑞", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2012000d5pqjkm93F7.png", "fuel": "92号或95号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0g12000cf9bf0gDDA0.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0w12000cf9bcvg5E4A.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6712000cf9bcvc5647.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2912000cf9bdsjDE67.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6g12000cf9boqpDAD7.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "三厢车", "groupName": "舒适轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "vehiclesSetId": "88", "guidSys": "部分车辆支持自适应巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5282_", "luggageNo": 5, "carPlay": "支持CarLife/CarPlay", "displacement": "2.0T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4h12000eth16tnAF19.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3460&app_ver=10.5", "carPhone": true, "vehicleCode": "5282", "style": "2020款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4h12000eth16tnAF19.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6g12000c5rmc6b2AB5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1c12000c5rmmb77F7C.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3k12000c5rmazp9E54.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3p12000c5rmm3a4ED6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1q12000c5rmd3r2879.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5283_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV1x12000cf2440k87D7.jpg?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=4037&app_ver=10.5", "carPhone": true, "vehicleCode": "5283", "style": "2021款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1x12000cf2440k87D7.jpg?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5v12000c5rmv0xFB9E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5s12000c5rmrii54A5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5912000c5rmwv3E006.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4c12000c5rn4791853.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3i12000c5rmwvf2837.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "不支持", "vehicleKey": "0_17391_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0m12000c563xuyA0D0.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=8202&app_ver=10.5", "carPhone": true, "vehicleCode": "17391", "style": "2023款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3e12000b76wz3d998E.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6c12000c6e72r9C1EE.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1y12000c6e73t52597.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3912000c6e75un16B1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6112000c6e780q9AF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3y12000c6e71tgC9D9.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5524_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV2d12000eti26ywD966.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=6912&app_ver=10.5", "carPhone": true, "vehicleCode": "5524", "style": "2022款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2d12000eti26ywD966.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5a12000cl0wt2062F4.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0n12000cl0wwqdE31E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0q12000cl0wqwv4BB1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6812000cl0wjx68EE4.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0e12000cl0wu6t7271.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "不支持", "subGroupCode": "newenergy", "luggageNo": 2, "carPlay": "不支持", "vehicleKey": "0_17639_", "autoPark": false, "endurance": "工信部续航333km", "fuelType": "纯电动", "charge": "快充0.58小时,慢充9.5小时", "imageList": ["https://dimg04.c-ctrip.com/images/0RV3b12000c56dmyj20D3.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "五菱缤果", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [], "carPhone": true, "vehicleCode": "17639", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "五菱缤果", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4t12000c5v1bkb78D8.jpg?mark=yiche", "luggageNum": "可放2个24寸行李箱", "passengerNo": 4, "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1e12000c5v1bme5FB5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6k12000c5v1h6j3FDB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2h12000c5v1imhEEFA.jpg?mark=yiche"], "transmissionType": 1, "brandName": "五菱汽车", "oilType": 5, "struct": "两厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "五菱汽车", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "68", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "不支持", "subGroupCode": "newenergy", "luggageNo": 2, "carPlay": "不支持", "vehicleKey": "2_17639_", "autoPark": false, "endurance": "工信部续航333km", "fuelType": "纯电动", "charge": "快充0.58小时,慢充9.5小时", "imageList": ["https://dimg04.c-ctrip.com/images/0RV3b12000c56dmyj20D3.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "五菱缤果", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [], "carPhone": true, "vehicleCode": "17639", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "五菱缤果", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4t12000c5v1bkb78D8.jpg?mark=yiche", "luggageNum": "可放2个24寸行李箱", "passengerNo": 4, "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1e12000c5v1bme5FB5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6k12000c5v1h6j3FDB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2h12000c5v1imhEEFA.jpg?mark=yiche"], "transmissionType": 1, "brandName": "五菱汽车", "oilType": 5, "struct": "两厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "五菱汽车", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "68", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5325_", "luggageNo": 4, "carPlay": "支持CarLife/CarPlay/原厂互联/映射/HUAWEIHiCar", "displacement": "1.5T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0412000c567tciAA87.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "广汽传祺M6（21款及以后）", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5045&app_ver=10.5", "carPhone": true, "vehicleCode": "5325", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "广汽传祺M6（21款及以后）", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3g12000bg3n5qz6FDC.jpg", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放4个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5l12000chm45l9BA47.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2q12000chm42f9F802.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6312000chm4dbiDECF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6u12000chm46h08446.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5e12000chm45rj5240.jpg?mark=yiche"], "transmissionType": 1, "brandName": "广汽传祺", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "广汽传祺", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "subGroupCode": "newenergy", "luggageNo": 2, "carPlay": "不支持", "vehicleKey": "0_5241_", "endurance": "工信部续航200km-403km", "fuelType": "纯电动", "charge": "快充0.6小时,慢充6.5小时", "imageList": ["https://dimg04.c-ctrip.com/images/0RV3c12000c5677s0BBB7.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "零跑T03", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3360&app_ver=10.5", "vehicleCode": "5241", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "零跑T03", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4z12000b78fo4r2A5C.jpg", "luggageNum": "可放2个24寸行李箱", "passengerNo": 4, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6w12000c5rev8c8D62.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3t12000c5rf2vvDEC7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1j12000c5req4i7023.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3e12000c5revxh2AA6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2c12000c5repr1100B.jpg?mark=yiche"], "transmissionType": 1, "brandName": "零跑汽车", "oilType": 5, "struct": "两厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "零跑汽车", "licenseStyle": "2", "vehiclesSetId": "68", "guidSys": "部分车辆支持全速自适应巡航/定速巡航", "transmissionName": "自动挡"}], "storeList": [{"pickOffLevel": 570606, "storeCode": "308155", "pickUpLevel": 570606}, {"pickOffLevel": 55983, "storeCode": "2285298", "pickUpLevel": 55983}, {"pickOffLevel": 61144, "storeCode": "116955", "pickUpLevel": 61144}, {"pickOffLevel": 8004, "storeCode": "115935", "pickUpLevel": 8004}, {"pickOffLevel": 165, "storeCode": "138334", "pickUpLevel": 165}, {"pickOffLevel": 292, "storeCode": "183936", "pickUpLevel": 292}, {"pickOffLevel": 2761, "storeCode": "78", "pickUpLevel": 2761}, {"pickOffLevel": 790777, "storeCode": "326061", "pickUpLevel": 790777}, {"pickOffLevel": 843669, "storeCode": "174846", "pickUpLevel": 843669}, {"pickOffLevel": 1135044, "storeCode": "175820", "pickUpLevel": 1135044}, {"pickOffLevel": 36744, "storeCode": "181671", "pickUpLevel": 36744}, {"pickOffLevel": 872, "storeCode": "188084", "pickUpLevel": 872}, {"pickOffLevel": 620593, "storeCode": "312488", "pickUpLevel": 620593}, {"pickOffLevel": 1658, "storeCode": "193947", "pickUpLevel": 1658}, {"pickOffLevel": 1954429, "storeCode": "192668", "pickUpLevel": 1954429}, {"pickOffLevel": 26867, "storeCode": "107474", "pickUpLevel": 26867}, {"pickOffLevel": 24801, "storeCode": "107059", "pickUpLevel": 24801}, {"pickOffLevel": 6693, "storeCode": "106808", "pickUpLevel": 6693}, {"pickOffLevel": 8536, "storeCode": "2961", "pickUpLevel": 8536}, {"pickOffLevel": 49965, "storeCode": "115295", "pickUpLevel": 49965}, {"pickOffLevel": 30264, "storeCode": "1335279", "pickUpLevel": 30264}, {"pickOffLevel": 26611, "storeCode": "107268", "pickUpLevel": 26611}, {"pickOffLevel": 6635, "storeCode": "181864", "pickUpLevel": 6635}, {"pickOffLevel": 4691, "storeCode": "408969", "pickUpLevel": 47040}, {"pickOffLevel": 574680, "storeCode": "309366", "pickUpLevel": 574680}, {"pickOffLevel": 112379, "storeCode": "174692", "pickUpLevel": 112379}, {"pickOffLevel": 30154, "storeCode": "107119", "pickUpLevel": 30154}, {"pickOffLevel": 43184, "storeCode": "114289", "pickUpLevel": 43184}, {"pickOffLevel": 1908600, "storeCode": "2364391", "pickUpLevel": 1908600}, {"pickOffLevel": 7591, "storeCode": "115986", "pickUpLevel": 7591}, {"pickOffLevel": 72358, "storeCode": "133340", "pickUpLevel": 72358}, {"pickOffLevel": 259543, "storeCode": "250168", "pickUpLevel": 259543}, {"pickOffLevel": 119432, "storeCode": "175686", "pickUpLevel": 119432}, {"pickOffLevel": 1591899, "storeCode": "136697", "pickUpLevel": 1591899}, {"pickOffLevel": 1743407, "storeCode": "1893900", "pickUpLevel": 1743407}, {"pickOffLevel": 145906, "storeCode": "184270", "pickUpLevel": 145906}, {"pickOffLevel": 68967, "storeCode": "114886", "pickUpLevel": 68967}, {"pickOffLevel": -1, "storeCode": "17493", "pickUpLevel": -1}, {"pickOffLevel": 178223, "storeCode": "188920", "pickUpLevel": 178223}, {"pickOffLevel": 1753774, "storeCode": "1957775", "pickUpLevel": 1753774}, {"pickOffLevel": 472151, "storeCode": "274541", "pickUpLevel": 472151}, {"pickOffLevel": 40534, "storeCode": "2370390", "pickUpLevel": 40534}, {"pickOffLevel": 1217371, "storeCode": "265301", "pickUpLevel": 1217371}, {"pickOffLevel": 1969304, "storeCode": "2370159", "pickUpLevel": 1969304}, {"pickOffLevel": 88368, "storeCode": "106965", "pickUpLevel": 88368}, {"pickOffLevel": 1989275, "storeCode": "2381555", "pickUpLevel": 1989275}, {"pickOffLevel": 49923, "storeCode": "115278", "pickUpLevel": 49923}, {"pickOffLevel": 1847700, "storeCode": "192639", "pickUpLevel": 1847700}, {"pickOffLevel": 247905, "storeCode": "248685", "pickUpLevel": 247905}, {"pickOffLevel": 78426, "storeCode": "133807", "pickUpLevel": 78426}, {"pickOffLevel": 1766346, "storeCode": "1851872", "pickUpLevel": 1766346}, {"pickOffLevel": 36722, "storeCode": "2239938", "pickUpLevel": 36722}, {"pickOffLevel": 8603, "storeCode": "797672", "pickUpLevel": 8603}, {"pickOffLevel": 98018, "storeCode": "161973", "pickUpLevel": 98018}, {"pickOffLevel": 596443, "storeCode": "287666", "pickUpLevel": 596443}, {"pickOffLevel": 895, "storeCode": "188937", "pickUpLevel": 895}, {"pickOffLevel": 67912, "storeCode": "116523", "pickUpLevel": 67912}], "promptInfos": [{"jumpUrl": "https://m.ctrip.com/tangram/OTI2MjU=?ctm_ref=vactang_page_92625&isHideNavBar=YES&apppgid=10650039395&statusBarStyle=1&channelid=238407", "type": 19, "locations": [{"groupCode": "all", "index": 1}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg3112000e8l73mt525E.png"}, {"type": 18, "locations": [{"groupCode": "all", "index": 6}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg5t12000cixdsaaBF66.png"}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": false, "isLastPage": false, "uniqSign": "120011398903214795367ZCc1h3479n8A2J1B98I", "pHub": 1, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": []}, "checkRequestTime": 1736415165550.572, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "3885915658686662994", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a2d7ec8-482337-1884285", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1736415167623+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 1945, "environmentCost": 1, "cacheFetchCost": 0, "fetchCost": 1945, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1736415165549, "afterFetch": 1736415167494, "hasRetry": false}}