{"baseResponse": {"extMap": {}, "errorCode": "0", "hasResult": true, "extraIndexTags": {"pcName": "中国", "rcId": "1", "rcName": "中国", "rCityName": "三亚", "rCityId": "43", "pCityId": "43", "pcId": "1", "pCityName": "三亚"}, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "fe463fd8-aa82-42c1-b6ab-84348bbbf513", "isSuccess": true}, "rHub": 0, "isKlbData": true, "allVendorPriceCount": 682, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": true, "extras": {"serverRequestId": "01oO347fI48HwJEN35kO", "abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B", "isNewLicensePlate": "0", "isLicensePlateHideShow": "0", "commodityClass2Version": "1"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"shortName": "车龄", "sortNum": 1, "bitwiseType": 2, "name": "车龄", "groupCode": "CarAge", "filterItems": [{"sortNum": 1, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "5", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 2, "positionCode": "5", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 3, "positionCode": "5", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "手动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "NewEnergy", "itemCode": "NewEnergy_elect", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}, {"sortNum": 2, "groupCode": "NewEnergy", "itemCode": "NewEnergy_mix", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "新能源混动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 3, "groupCode": "VehicleAccessory", "quickSortNum": 7, "positionCode": "6", "itemCode": "VehicleAccessory_radar", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "倒车雷达"}, {"sortNum": 4, "groupCode": "VehicleAccessory", "quickSortNum": 8, "positionCode": "6", "itemCode": "VehicleAccessory_tachograph", "binaryDigit": 64, "isQuickItem": true, "bitwiseType": 1, "name": "行车记录仪"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}, {"sortNum": 7, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Refrigerator", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 1, "name": "车载冰箱"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "HotBrand", "itemCode": "HotBrand_奥迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_别克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_奔驰", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_宝马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotB<PERSON>_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 6, "groupCode": "HotBrand", "itemCode": "HotBrand_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 10, "groupCode": "HotBrand", "itemCode": "HotBrand_Jeep", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 12, "groupCode": "HotBrand", "itemCode": "HotBrand_路虎", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 18, "groupCode": "HotBrand", "itemCode": "HotBrand_日产", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_奥迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_阿斯顿·马丁", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "阿斯顿·马丁", "icon": "https://dimg04.c-ctrip.com/images/0R419120009gwx9cqBA77.png"}], "groupCode": "BrandGroup_a0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_保时捷", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "保时捷", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_别克", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_北京", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "北京", "icon": "//pages.c-ctrip.com/carisd/brandlogo/beijin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔腾", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "奔腾", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benteng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔驰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝沃", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "宝沃", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baowo.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝马", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宾利", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "宾利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/binli.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_本田", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_比亚迪", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "比亚迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_创维汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "创维汽车", "icon": "https://dimg04.c-ctrip.com/images/0yc0v12000aombae248A8.png"}, {"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安欧尚", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "长安欧尚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changanoushang.png"}], "groupCode": "BrandGroup_c0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_法拉利", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "法拉利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_福特", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "福特", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}], "groupCode": "BrandGroup_f0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽传祺", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "广汽传祺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽埃安", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "广汽埃安", "icon": "https://dimg04.c-ctrip.com/images/0R44f120009gwvd13842B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽新能源", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "广汽新能源", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqixingnengyuan.png"}], "groupCode": "BrandGroup_g0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_华颂", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "华颂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/huasong.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_哈弗", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "哈弗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_海马", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "海马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_红旗", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "红旗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}], "groupCode": "BrandGroup_h0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_Jeep", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_吉利汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "吉利汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷达", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "捷达", "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_k0", "itemCode": "BrandGroup_k0_凯迪拉克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "凯迪拉克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "groupCode": "BrandGroup_k0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_LEVC", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "LEVC", "icon": "//pages.c-ctrip.com/carisd/brandlogo/levc.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_兰博基尼", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "兰博基尼", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_劳斯莱斯", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "劳斯莱斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_林肯", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "林肯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/linken.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_理想汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "理想汽车", "icon": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc6i1200000ieok0AE70.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_路虎", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_零跑汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "零跑汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingpaoqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_雷克萨斯", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "雷克萨斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}], "groupCode": "BrandGroup_l0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_MINI", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "MINI", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_玛莎拉蒂", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "玛莎拉蒂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_迈凯伦", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "迈凯伦", "icon": "//pages.c-ctrip.com/carisd/brandlogo/maikailun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_马自达", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "马自达", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mazida.png"}], "groupCode": "BrandGroup_m0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_n0", "itemCode": "BrandGroup_n0_哪吒汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "哪吒汽车", "icon": "https://dimg04.c-ctrip.com/images/0R46n120009gwvgrmC0A4.png"}], "groupCode": "BrandGroup_n0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_奇瑞", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "奇瑞", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qirui.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_起亚", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "起亚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "groupCode": "BrandGroup_q0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_日产", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_荣威", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "荣威", "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "groupCode": "BrandGroup_r0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_上汽大通MAXUS", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "上汽大通MAXUS", "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_思铭", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "思铭", "icon": "//pages.c-ctrip.com/carisd/brandlogo/siming.png"}], "groupCode": "BrandGroup_s0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_坦克", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "坦克", "icon": "https://dimg04.c-ctrip.com/images/0yc3612000aoma5tvACF1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_特斯拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "特斯拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_腾势", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "腾势", "icon": "//pages.c-ctrip.com/carisd/brandlogo/shiteng.png"}], "groupCode": "BrandGroup_t0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_WEY", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "WEY", "icon": "//pages.c-ctrip.com/carisd/brandlogo/wey.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_威马汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "威马汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weimaqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_沃尔沃", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "沃尔沃", "icon": "//pages.c-ctrip.com/carisd/brandlogo/woerwo.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_蔚来", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "蔚来", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weilai.png"}], "groupCode": "BrandGroup_w0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小鹏汽车", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "小鹏汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiaopengqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_现代", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "现代", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪佛兰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "雪佛兰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_依维柯", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "依维柯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yikewei.png"}], "groupCode": "BrandGroup_y0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_飞凡汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "飞凡汽车", "icon": ""}], "groupCode": "BrandGroup_z0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "0-50", "sortNum": 1, "name": "¥50以下", "groupCode": "Price", "itemCode": "Price_0-50"}, {"code": "50-100", "sortNum": 2, "name": "¥50-100", "groupCode": "Price", "itemCode": "Price_50-100"}, {"code": "100-200", "sortNum": 3, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-400", "sortNum": 4, "name": "¥200-400", "groupCode": "Price", "itemCode": "Price_200-400"}, {"code": "400-99999", "sortNum": 5, "name": "¥400以上", "groupCode": "Price", "itemCode": "Price_400-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 3, "groupCode": "PickReturn", "itemCode": "PickReturn_FreeShuttle", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "免费接至门店取车"}, {"sortNum": 4, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupSelf", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "自行到店取车"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "StoreService", "quickSortNum": 4, "positionCode": "3", "itemCode": "StoreService_easyLife", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 1, "name": "无忧租"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 6, "groupCode": "StoreService", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_8", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "台胞证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "驾驶员驾龄", "sortNum": 6, "bitwiseType": 2, "name": "驾驶员驾龄", "groupCode": "DriveAge", "filterItems": [{"sortNum": 1, "groupCode": "DriveAge", "itemCode": "DriveAge_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "不满6个月"}, {"sortNum": 2, "groupCode": "DriveAge", "itemCode": "DriveAge_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "满6个月"}, {"sortNum": 3, "groupCode": "DriveAge", "itemCode": "DriveAge_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "满一年"}]}, {"shortName": "门店评分", "sortNum": 7, "bitwiseType": 2, "name": "门店评分", "groupCode": "Comment", "filterItems": [{"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "9", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 2, "groupCode": "Comment", "itemCode": "Comment_4.5", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4.5分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}]}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "18", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "携程优选", "icon": ""}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30004", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "铭轩酒店租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30027", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "玛雅租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30055", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "准典出行"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30182", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "加加租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30248", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "驰敖天天租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30284", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "旭升租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30466", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "祥成租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31092", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "树德租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31239", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "佳途租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31279", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "游乐美租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32231", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "泰信吉租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32538", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "海越租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32845", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "美凯租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_33419", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "欣岳美行租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_37573", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "凯美租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_38235", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "美行尚盈租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_38236", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "天驹租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_52811", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "蔚蓝租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_53893", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "世纪联合租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_57671", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "捷安利达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_58487", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "丰田海南出行"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61659", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "旺亚租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61816", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "永卓租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61827", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "吉海畅行租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61831", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "金达莱租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61937", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "锋达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61951", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "鑫旺达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61966", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "宝驰租车"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62053", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "联谊租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62072", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "一路平安租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62305", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "皖太租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63457", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "翊霏租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63460", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "小米租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63836", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "租租侠租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_65413", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "哈尔滨奥朗租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_66324", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "环岛租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_66335", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "亿豪租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_66614", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "你我他租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_67661", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "乐途租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_68692", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "利资租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_70693", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "车旺达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_70695", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "龙之祥租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_70697", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "铭车邦租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_70698", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "铭途租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_71515", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "中进通达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_72983", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "吉驰租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_73265", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "车之美租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_73843", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "飞浩租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_73871", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "多浦达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_74365", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "爱尚出行租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_74373", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "中进租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_74569", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "河北唐亚租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_74573", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "器车出行"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_74629", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "通源租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_76105", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "全季租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_76661", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "振亚租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_76665", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "彩车坊租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_76903", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "如亚租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_77081", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "常晟租车"}], "groupCode": "Vendor_1", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_77147", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "美点租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_77151", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "金晟利租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_77287", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "老马出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_78577", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "夏末微凉租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_78579", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "优享旅途租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_78963", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "轩琪租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_79461", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "海南麻豆智行租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_79485", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "立强租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_79695", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "炜晨租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_79723", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "盛豪会租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80115", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "鼎航租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80127", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "新概念租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80145", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "鑫路达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80147", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "浩宇租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80427", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "名都租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80431", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "小飞侠租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80771", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "豫海租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80775", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "车先生租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80777", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "照成租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80977", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "星月租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81003", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "琼州租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81479", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "商旅出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81525", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "钰鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81527", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "业扬租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81529", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "果岭出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81675", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "广源租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81687", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "易达通租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81887", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "热火租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81919", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "福斯特租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81931", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "博利租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_82163", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "启瑞盛租车"}], "groupCode": "Vendor_2", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_82231", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "云超租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_82263", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "聚通达租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_82305", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "畅行无忧租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_82571", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_82671", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_82731", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_82739", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_82819", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": ""}], "groupCode": "Vendor_3", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "3a9qm2015z0PO815Zw1S22323", "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 258, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 258, "detail": [{"code": "1001", "amount": 258, "amountDesc": "¥258", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥258", "subAmount": 258, "name": "车辆租金", "amountStr": "¥258"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 308, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥308", "subAmount": 308, "name": "总价", "amountStr": "¥308"}], "reference": {"vehicleCode": "0", "rStoreCode": "106896", "packageId": "sec", "pLev": 23665, "bizVendorCode": "SD37573", "pStoreCode": "106896", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzUxNDVfMV8yNTguMF8yNTguMF8wLjBfMzA4LjBfMjU4LjBfMzA4LjBfMF8wXzAuMF8wLjBfMzAuMF8yMC4wXzBfMF8zNDMwNjU2", "sendTypeForPickUpCar": 0, "skuId": 3430656, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 23665, "vendorCode": "37573", "vendorVehicleCode": "1424_1362_pupai"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "5145", "highestPrice": 258, "pWay": "可选：店员免费上门送取车", "minDPrice": 258, "hot": 0, "minTPrice": 308, "lowestDistance": 0, "group": 0, "sortNum": 0, "maximumRating": 4.4, "vehicleRecommendProduct": {"productCodes": ["SD37573_0_106896_106896"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 2, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "logicIndex": 0, "isGroup": false, "renderUniqId": "5145_1__308_258_0_免押金_easyLife"}, {"groupSort": 0, "lowestPrice": 58, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 58, "detail": [{"code": "1001", "amount": 58, "amountDesc": "¥58", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥58", "subAmount": 58, "name": "车辆租金", "amountStr": "¥58"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 45, "amountStr": "¥45", "detail": [{"code": "1002", "amount": 45, "amountDesc": "¥45", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 123, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥123", "subAmount": 123, "name": "总价", "amountStr": "¥123"}], "reference": {"vehicleCode": "0", "rStoreCode": "107242", "pLev": 24129, "comPriceCode": "[c]Mjc5fDE4NjJ8MjAyMC4wMC0xNSAzLTExMDowMDAwOjAmMSR8JjU4JiYxJjUxMDAxJDEwMDgmNTgyMC4wMyYxJi4wMCQwJjIwJjEmNDEwMDImNDUuNS4wMDIwMjMwMCR8MTUgMS0xMS06MDAmNjowMC0xMS0yMDIzNjowMDE2IDEyMDIzOjAwfDA2IDEtMTEtOjMwADA6MzY=", "bizVendorCode": "SD57671", "pStoreCode": "107242", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3MjQyXzUxMDRfMV81OF81OF81OF8xMjMuMDBfNThfMTIzLjBfMF8wXzAuMF8wLjBfNDUuMDBfMjAuMDBfMC4wMF8wLjAwXzE4NjIyNzk=", "sendTypeForPickUpCar": 0, "skuId": 1862279, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24129, "vendorCode": "57671", "vendorVehicleCode": "20070049"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "5104", "highestPrice": 58, "pWay": "可选：店员免费上门送取车", "minDPrice": 58, "hot": 0, "minTPrice": 123, "lowestDistance": 0, "group": 0, "sortNum": 1, "maximumRating": 4.3, "vehicleRecommendProduct": {"productCodes": ["SD57671_0_107242_107242"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 79, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "logicIndex": 1, "isGroup": false, "renderUniqId": "5104_1__123_58_0_免押金_easyLife"}, {"groupSort": 0, "lowestPrice": 78, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 78, "detail": [{"code": "1001", "amount": 78, "amountDesc": "¥78", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥78", "subAmount": 78, "name": "车辆租金", "amountStr": "¥78"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1002", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 128, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥128", "subAmount": 128, "name": "总价", "amountStr": "¥128"}], "reference": {"vehicleCode": "0", "rStoreCode": "106896", "packageId": "sec", "pLev": 23665, "bizVendorCode": "SD37573", "pStoreCode": "106896", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzQwNjdfMV83OC4wXzc4LjBfMC4wXzEyOC4wXzc4LjBfMTI4LjBfMF8wXzAuMF8wLjBfMzAuMF8yMC4wXzBfMF8zNDMwNjg5", "sendTypeForPickUpCar": 0, "skuId": 3430689, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 23665, "vendorCode": "37573", "vendorVehicleCode": "1480_8123_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "32231", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107102", "skuId": 1856407, "bizVendorCode": "SD32231"}}], "rCoup": 0, "vehicleCode": "4067", "highestPrice": 188, "pWay": "可选：店员免费上门送取车", "minDPrice": 78, "hot": 0, "minTPrice": 128, "lowestDistance": 0, "group": 0, "sortNum": 2, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD37573_0_106896_106896"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 10893, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 2, "logicIndex": 2, "isGroup": false, "renderUniqId": "4067_2__128_78_0_免押金_easyLife"}, {"groupSort": 0, "lowestPrice": 45, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 45, "detail": [{"code": "1001", "amount": 45, "amountDesc": "¥45", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥45", "subAmount": 45, "name": "车辆租金", "amountStr": "¥45"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 140, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥140", "subAmount": 140, "name": "总价", "amountStr": "¥140"}], "reference": {"vehicleCode": "0", "rStoreCode": "116954", "pLev": 60298, "comPriceCode": "[c]MDUxfDI3MDUzLTExfDIwMjAwOjAtMTUgJjQ1JjA6MDAxMDAxJjEkfDUmNDUmMSY0MyYxJiQxMDAwJjM1MzUuMDEwMDIuMDAkMC4wMCYxJjYwMCR8JjYwLi0xMS0yMDIzNjowMDE1IDEyMDIzOjAwJjE2IDEtMTEtOjAwfDY6MDAtMTEtMjAyMzA6MzYwNiAxAAAAADoyOQA=", "bizVendorCode": "SD77147", "pStoreCode": "116954", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTE2OTU0XzQxMzlfMV80NV80NV80NV8xNDAuMDBfNDVfMTQwLjBfMF8wXzAuMF8wLjBfNjAuMDBfMzUuMDBfMC4wMF8wLjAwXzI3MDUwNTE=", "sendTypeForPickUpCar": 0, "skuId": 2705051, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 60298, "vendorCode": "77147", "vendorVehicleCode": "20076929"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "61831", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114315", "skuId": 1911940, "bizVendorCode": "SD61831"}}, {"reference": {"vendorCode": "74573", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114529", "skuId": 1911622, "bizVendorCode": "SD74573"}}, {"reference": {"vendorCode": "77151", "vehicleCode": "0", "packageType": 0, "pStoreCode": "114590", "skuId": 1908708, "bizVendorCode": "SD77151"}}, {"reference": {"vendorCode": "53893", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107119", "skuId": 1860920, "bizVendorCode": "SD53893"}}, {"reference": {"vendorCode": "77081", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114575", "skuId": 1916013, "bizVendorCode": "SD77081"}}, {"reference": {"vendorCode": "80431", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114886", "skuId": 1913476, "bizVendorCode": "SD80431"}}], "rCoup": 0, "vehicleCode": "4139", "highestPrice": 368, "pWay": "可选：店员免费上门送取车", "minDPrice": 45, "hot": 0, "minTPrice": 140, "lowestDistance": 0, "group": 0, "sortNum": 3, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD77147_0_116954_116954"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 2941, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 7, "logicIndex": 3, "isGroup": false, "renderUniqId": "4139_7__140_45_0_免押金_easyLife"}, {"groupSort": 0, "lowestPrice": 68, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 68, "detail": [{"code": "1001", "amount": 68, "amountDesc": "¥68", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥68", "subAmount": 68, "name": "车辆租金", "amountStr": "¥68"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 153, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥153", "subAmount": 153, "name": "总价", "amountStr": "¥153"}], "reference": {"vehicleCode": "0", "rStoreCode": "115340", "pLev": 48773, "comPriceCode": "[c]", "bizVendorCode": "SD81529", "pStoreCode": "115340", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTE1MzQwXzU2MDRfMV82OF82OF82OF8xNTMuMDBfNjhfMTUzLjBfMF8wXzAuMF8wLjBfNTAuMDBfMzUuMDBfMC4wMF8wLjAwXzE5MTI0MzA=", "sendTypeForPickUpCar": 0, "skuId": 1912430, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 48773, "vendorCode": "81529", "vendorVehicleCode": "20080065"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "5604", "highestPrice": 68, "pWay": "可选：店员免费上门送取车", "minDPrice": 68, "hot": 0, "minTPrice": 153, "lowestDistance": 0, "group": 0, "sortNum": 4, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD81529_0_115340_115340"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false, "logicIndex": 4, "isGroup": false, "renderUniqId": "5604_1__153_68_0_免押金"}, {"groupSort": 0, "lowestPrice": 70, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 98, "detail": [{"code": "1001", "amount": 98, "amountDesc": "¥98", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥98", "subAmount": 98, "name": "车辆租金", "amountStr": "¥98"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 40, "amountStr": "¥40", "detail": [{"code": "1002", "amount": 40, "amountDesc": "¥40", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 158, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥158", "subAmount": 158, "name": "总价", "amountStr": "¥158"}], "reference": {"vehicleCode": "0", "rStoreCode": "106896", "packageId": "sec", "pLev": 23665, "bizVendorCode": "SD37573", "pStoreCode": "106896", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzUxNzdfMV85OC4wXzk4LjBfMC4wXzE1OC4wXzk4LjBfMTU4LjBfMF8wXzAuMF8wLjBfNDAuMF8yMC4wXzBfMF8zNDMwNzA0", "sendTypeForPickUpCar": 0, "skuId": 3430704, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 23665, "vendorCode": "37573", "vendorVehicleCode": "1489_20797_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "77147", "vehicleCode": "0", "packageType": 0, "pStoreCode": "116954", "skuId": 1966815, "bizVendorCode": "SD77147"}}, {"reference": {"vendorCode": "53893", "vehicleCode": "0", "packageType": 0, "pStoreCode": "107119", "skuId": 1857830, "bizVendorCode": "SD53893"}}, {"reference": {"vendorCode": "77081", "vehicleCode": "0", "packageType": 0, "pStoreCode": "114575", "skuId": 1910193, "bizVendorCode": "SD77081"}}, {"reference": {"vendorCode": "80431", "vehicleCode": "0", "packageType": 0, "pStoreCode": "114886", "skuId": 1920814, "bizVendorCode": "SD80431"}}, {"reference": {"vendorCode": "74573", "vehicleCode": "0", "packageType": 0, "pStoreCode": "114529", "skuId": 2331544, "bizVendorCode": "SD74573"}}], "rCoup": 0, "vehicleCode": "5177", "highestPrice": 699, "pWay": "可选：店员免费上门送取车", "minDPrice": 98, "hot": 0, "minTPrice": 158, "lowestDistance": 0, "group": 0, "sortNum": 5, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD77147_0_116954_116954"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 2941, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 6, "logicIndex": 5, "isGroup": false, "renderUniqId": "5177_6__158_98_0_免押金_easyLife"}], "groupCode": "all", "dailyPrice": 58, "hasResult": true}, {"sortNum": 0, "groupName": "经济轿车", "hasResult": true, "groupCode": "2", "dailyPrice": 58}, {"sortNum": 2, "groupName": "新能源", "hasResult": true, "groupCode": "newenergy", "dailyPrice": 68}, {"sortNum": 3, "groupName": "舒适轿车", "hasResult": true, "groupCode": "3", "dailyPrice": 98}, {"sortNum": 4, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 68}, {"sortNum": 5, "groupName": "商务车", "hasResult": true, "groupCode": "4", "dailyPrice": 145}, {"sortNum": 6, "groupName": "豪华轿车", "hasResult": true, "groupCode": "5", "dailyPrice": 269}, {"sortNum": 7, "groupName": "跑车", "hasResult": true, "groupCode": "9", "dailyPrice": 135}, {"sortNum": 8, "groupName": "房车", "hasResult": true, "groupCode": "11", "dailyPrice": 778}], "frontTraceInfo": {"vehicleGroupMap": {"2": 23, "3": 13, "4": 42, "5": 29, "6": 76, "9": 32, "11": 1, "newenergy": 47}, "normalCount": 796, "priceCount": 796, "vehicleList": ["4053", "5145", "4971", "471", "472", "5258", "4840", "5138", "4062", "5151", "5272", "4067", "5158", "4740", "1466", "247", "248", "2794", "4732", "4975", "4734", "5283", "5163", "5167", "5289", "4750", "2451", "17380", "250", "4191", "5281", "5282", "4193", "136", "138", "4866", "33", "5173", "4086", "5176", "5177", "17394", "4088", "39", "4883", "17390", "17391", "2461", "5291", "267", "1488", "5601", "4635", "5604", "4515", "4758", "17366", "5066", "5187", "834", "4764", "2905", "5074", "17378", "4781", "4660", "4663", "5193", "5194", "5508", "166", "287", "1149", "1820", "289", "2477", "5502", "5623", "4897", "5504", "10622", "4659", "608", "3101", "6291", "6294", "1833", "2801", "178", "1951", "75", "5410", "2384", "3469", "4676", "5524", "5526", "89", "4330", "873", "4449", "17326", "2704", "17455", "5433", "1500", "1069", "4577", "5425", "648", "4470", "5320", "5564", "5444", "5686", "4476", "4907", "656", "536", "5557", "2608", "2729", "17425", "17427", "1090", "5570", "5572", "5574", "5453", "1404", "1644", "1400", "301", "5325", "5568", "4358", "5329", "4912", "1406", "1405", "4010", "4495", "5584", "4253", "5342", "4496", "4012", "5465", "4013", "4498", "5466", "2078", "4805", "311", "4929", "315", "679", "4004", "4367", "4489", "4369", "17529", "4249", "4020", "5351", "5594", "5110", "5595", "4022", "5596", "4936", "4816", "326", "5104", "4139", "5349", "5481", "3180", "5241", "6333", "5245", "4036", "1437", "2889", "2888", "334", "214", "216", "5479", "4940", "5359", "5238", "4823", "5371", "5010", "5253", "4043", "5374", "5254", "5497", "5257", "4960", "5499", "1450", "340", "4958", "4716", "220", "4959", "3502", "1444", "468", "5368", "5007", "4039", "5128"], "easyLifeCount": 0, "zhimaCount": 763, "vendorNames": ["吉海畅行租车", "中进租车", "丰田海南出行", "小飞侠租车", "夏末微凉租车", "鼎航租车", "浩宇租车", "哈尔滨奥朗租车", "龙之祥租车", "凯美租车", "金达莱租车", "天驹租车", "星月租车", "热火租车", "小米租车", "博利租车", "名都租车", "车旺达租车", "利资租车", "车先生租车", "果岭出行", "美点租车", "云超租车", "玛雅租车", "老马出行", "泰信吉租车", "租租侠租车", "吉驰租车", "蔚蓝租车", "常晟租车", "豫海租车", "河北唐亚租车", "立强租车", "铭途租车", "钰鑫租车", "环岛租车", "通源租车", "加加租车", "你我他租车", "驰敖天天租车", "广源租车", "永卓租车", "照成租车", "一路平安租车", "中进通达租车", "振亚租车", "海南麻豆智行租车", "聚通达租车", "全季租车", "爱尚出行租车", "欣岳美行租车", "美行尚盈租车", "琼州租车", "锋达租车", "福斯特租车", "铭车邦租车", "飞浩租车", "鑫旺达租车", "旺亚租车", "亿豪租车", "如亚租车", "铭轩酒店租车", "商旅出行", "祥成租车", "盛豪会租车", "佳途租车", "宝驰租车", "畅行无忧租车", "多浦达租车", "优享旅途租车", "树德租车", "新概念租车", "鑫路达租车", "游乐美租车", "准典出行", "启瑞盛租车", "乐途租车", "联谊租车", "捷安利达租车", "翊霏租车", "彩车坊租车", "炜晨租车", "车之美租车", "美凯租车", "世纪联合租车", "业扬租车", "易达通租车", "器车出行", "金晟利租车", "旭升租车", "轩琪租车", "皖太租车", "海越租车"]}, "labelCodes": ["3650", "3563", "3495", "3494", "3548", "3504", "3705", "3827", "3503", "3547", "3502", "3501", "3709", "3510", "3696", "3697", "3653", "3698", "3731", "3509", "3788", "3810", "3789", "3679", "3746"], "quickFilter": [{"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 1, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "5", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "9", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "18", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "携程优选", "icon": ""}], "promotMap": {}, "requestInfo": {"rLongitude": 109.41201, "rDate": "20231116160000", "age": 30, "pCityId": 43, "returnDate": "/Date(1700121600000+0800)/", "sourceCountryId": 1, "pLatitude": 18.30747, "rLatitude": 18.30747, "pLongitude": 109.41201, "pDate": "20231115160000", "rCityId": 43, "pickupLocationName": "凤凰国际机场T1航站楼", "returnLocationName": "凤凰国际机场T1航站楼", "pickupDate": "/Date(1700035200000+0800)/"}, "allVehicleCount": 216, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"filterCode": "Vendor_0"}, "vehicleList": [{"skylight": "分段式电动天窗", "luggageNo": 1, "carPlay": "", "displacement": "1.3T", "endurance": "工信部续航100km", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "奔驰GLB", "doorNo": 5, "driveMode": "前置前驱", "chargeInterface": "Type-C", "carPhone": true, "vehicleCode": "5145", "style": "", "name": "奔驰GLB", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放1个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": [], "transmissionType": 1, "brandName": "奔驰", "oilType": 3, "struct": "SUV", "groupName": "SUV", "groupSubClassCode": "", "brandEName": "奔驰", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "定速巡航", "transmissionName": "自动挡", "mediaTypes": [2, 3]}, {"skylight": "", "luggageNo": 3, "carPlay": "", "displacement": "1.5L", "autoPark": false, "endurance": "工信部续航100km", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "捷达VA3", "doorNo": 4, "driveMode": "前置前驱", "chargeInterface": "USB", "vehicleCode": "5104", "style": "", "name": "捷达VA3", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放3个24寸行李箱", "autoStart": false, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "transmissionType": 1, "brandName": "捷达", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "groupSubClassCode": "", "brandEName": "捷达", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "64", "guidSys": "", "transmissionName": "自动挡", "mediaTypes": [2]}, {"skylight": "", "luggageNo": 1, "carPlay": "", "displacement": "1.5L", "autoPark": false, "endurance": "工信部续航100km", "imageList": ["http://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0n1200000mhzkm217B.png"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "大众宝来", "doorNo": 4, "driveMode": "前置前驱", "chargeInterface": "USB", "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4860", "vehicleCode": "4067", "style": "", "name": "大众宝来", "realityImageUrl": "http://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0n1200000mhzkm217B.png", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放1个24寸行李箱", "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "vehiclesSetId": "64", "guidSys": "", "transmissionName": "自动挡", "mediaTypes": [3]}, {"skylight": "", "luggageNo": 2, "carPlay": "", "displacement": "1.0T-1.5L", "autoPark": false, "endurance": "工信部续航100km", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "雪佛兰科沃兹", "doorNo": 4, "driveMode": "前置前驱", "chargeInterface": "", "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "carPhone": true, "vehicleCode": "4139", "name": "雪佛兰科沃兹", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": false, "isHot": false, "vehicleAccessoryImages": [], "transmissionType": 1, "brandName": "雪佛兰", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "groupSubClassCode": "", "brandEName": "雪佛兰", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "64", "guidSys": "", "transmissionName": "自动挡"}, {"skylight": "", "subGroupCode": "newenergy", "luggageNo": 1, "carPlay": "", "endurance": "工信部续航100km", "fuelType": "纯电动", "charge": "快充0.5小时", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "哪吒U", "doorNo": 5, "driveMode": "前置前驱", "chargeInterface": "USB", "carPhone": true, "vehicleCode": "5604", "style": "", "name": "哪吒U", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "fuel": "", "passengerNo": 5, "luggageNum": "可放1个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": [], "transmissionType": 1, "brandName": "哪吒汽车", "oilType": 5, "struct": "SUV", "groupName": "SUV", "groupSubClassCode": "", "brandEName": "哪吒汽车", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "12", "guidSys": "定速巡航或全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "", "subGroupCode": "newenergy", "luggageNo": 2, "carPlay": "CarPlay", "endurance": "工信部续航100km", "fuelType": "纯电动", "charge": "快充0.67小时,慢充8小时", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0n1200000mhzkm217B.png"], "license": "", "isSpecialized": true, "groupCode": "3", "zhName": "别克微蓝6", "doorNo": 5, "driveMode": "前置前驱", "chargeInterface": "USB", "carPhone": true, "vehicleCode": "5177", "style": "", "name": "别克微蓝6", "realityImageUrl": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3q1200000mfye76789.jpg", "fuel": "", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3s1200000mfyf32B22.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV1a1200000mfykl27B8.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5k1200000mfyf8E236.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6l1200000mfykhD221.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV2w1200000mfyf21D80.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV081200000mfyev79BB.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4o1200000mfyf59CFC.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5p1200000mfykf416E.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6g1200000mfyfaC00F.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV141200000mfyey7E88.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4t1200000mfykeC8C2.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV201200000mfyf1F2C9.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0e1200000mfykjE984.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3x1200000mfyka444B.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV261200000mfykm05C6.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV151200000mfyfc70BC.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV091200000mfyfbD8EA.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV211200000mfyfe0457.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV2x1200000mfyfg9B61.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV321200000mfyko0536.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5l1200000mfyfmFCA6.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0f1200000mfyky2DA9.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV221200000mfyfu173D.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3y1200000mfykq8EAB.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3t1200000mfyfi6634.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4u1200000mfyksB996.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6m1200000mfykw3586.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4p1200000mfyfk0347.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3z1200000mfyl7B4FF.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4v1200000mfyl88670.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4i1200000mhi1t786E.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV331200000mhetqD17A.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3s1200000mfyf32B22.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV1a1200000mfykl27B8.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5k1200000mfyf8E236.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6l1200000mfykhD221.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV2w1200000mfyf21D80.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV081200000mfyev79BB.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4o1200000mfyf59CFC.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5p1200000mfykf416E.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6g1200000mfyfaC00F.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV141200000mfyey7E88.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4t1200000mfykeC8C2.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV201200000mfyf1F2C9.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0e1200000mfykjE984.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3x1200000mfyka444B.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV261200000mfykm05C6.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV151200000mfyfc70BC.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV091200000mfyfbD8EA.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV211200000mfyfe0457.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV2x1200000mfyfg9B61.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV321200000mfyko0536.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5l1200000mfyfmFCA6.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0f1200000mfyky2DA9.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV221200000mfyfu173D.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3y1200000mfykq8EAB.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3t1200000mfyfi6634.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4u1200000mfyksB996.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6m1200000mfykw3586.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4p1200000mfyfk0347.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3z1200000mfyl7B4FF.jpg"], "transmissionType": 1, "brandName": "别克", "oilType": 5, "struct": "两厢车", "groupName": "舒适轿车", "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "83", "guidSys": "", "transmissionName": "自动挡"}, {"skylight": "分段式电动天窗", "luggageNo": 1, "carPlay": "", "displacement": "1.3T", "endurance": "工信部续航100km", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "奔驰GLB", "doorNo": 5, "driveMode": "前置前驱", "chargeInterface": "Type-C", "carPhone": true, "vehicleCode": "5145", "style": "", "name": "奔驰GLB", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放1个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": [], "transmissionType": 1, "brandName": "奔驰", "oilType": 3, "struct": "SUV", "groupName": "SUV", "groupSubClassCode": "", "brandEName": "奔驰", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "定速巡航", "transmissionName": "自动挡", "mediaTypes": [2, 3]}, {"skylight": "", "luggageNo": 3, "carPlay": "", "displacement": "1.5L", "autoPark": false, "endurance": "工信部续航100km", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "捷达VA3", "doorNo": 4, "driveMode": "前置前驱", "chargeInterface": "USB", "vehicleCode": "5104", "style": "", "name": "捷达VA3", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放3个24寸行李箱", "autoStart": false, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "transmissionType": 1, "brandName": "捷达", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "groupSubClassCode": "", "brandEName": "捷达", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "64", "guidSys": "", "transmissionName": "自动挡", "mediaTypes": [2]}, {"skylight": "", "luggageNo": 1, "carPlay": "", "displacement": "1.5L", "autoPark": false, "endurance": "工信部续航100km", "imageList": ["http://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0n1200000mhzkm217B.png"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "大众宝来", "doorNo": 4, "driveMode": "前置前驱", "chargeInterface": "USB", "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4860", "vehicleCode": "4067", "style": "", "name": "大众宝来", "realityImageUrl": "http://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0n1200000mhzkm217B.png", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放1个24寸行李箱", "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "vehiclesSetId": "64", "guidSys": "", "transmissionName": "自动挡", "mediaTypes": [3]}, {"skylight": "", "luggageNo": 2, "carPlay": "", "displacement": "1.0T-1.5L", "autoPark": false, "endurance": "工信部续航100km", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "雪佛兰科沃兹", "doorNo": 4, "driveMode": "前置前驱", "chargeInterface": "", "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "carPhone": true, "vehicleCode": "4139", "name": "雪佛兰科沃兹", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": false, "isHot": false, "vehicleAccessoryImages": [], "transmissionType": 1, "brandName": "雪佛兰", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "groupSubClassCode": "", "brandEName": "雪佛兰", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "64", "guidSys": "", "transmissionName": "自动挡"}, {"skylight": "", "subGroupCode": "newenergy", "luggageNo": 1, "carPlay": "", "endurance": "工信部续航100km", "fuelType": "纯电动", "charge": "快充0.5小时", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "哪吒U", "doorNo": 5, "driveMode": "前置前驱", "chargeInterface": "USB", "carPhone": true, "vehicleCode": "5604", "style": "", "name": "哪吒U", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "fuel": "", "passengerNo": 5, "luggageNum": "可放1个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": [], "transmissionType": 1, "brandName": "哪吒汽车", "oilType": 5, "struct": "SUV", "groupName": "SUV", "groupSubClassCode": "", "brandEName": "哪吒汽车", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "12", "guidSys": "定速巡航或全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "", "subGroupCode": "newenergy", "luggageNo": 2, "carPlay": "CarPlay", "endurance": "工信部续航100km", "fuelType": "纯电动", "charge": "快充0.67小时,慢充8小时", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0n1200000mhzkm217B.png"], "license": "", "isSpecialized": true, "groupCode": "3", "zhName": "别克微蓝6", "doorNo": 5, "driveMode": "前置前驱", "chargeInterface": "USB", "carPhone": true, "vehicleCode": "5177", "style": "", "name": "别克微蓝6", "realityImageUrl": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3q1200000mfye76789.jpg", "fuel": "", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3s1200000mfyf32B22.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV1a1200000mfykl27B8.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5k1200000mfyf8E236.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6l1200000mfykhD221.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV2w1200000mfyf21D80.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV081200000mfyev79BB.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4o1200000mfyf59CFC.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5p1200000mfykf416E.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6g1200000mfyfaC00F.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV141200000mfyey7E88.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4t1200000mfykeC8C2.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV201200000mfyf1F2C9.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0e1200000mfykjE984.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3x1200000mfyka444B.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV261200000mfykm05C6.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV151200000mfyfc70BC.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV091200000mfyfbD8EA.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV211200000mfyfe0457.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV2x1200000mfyfg9B61.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV321200000mfyko0536.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5l1200000mfyfmFCA6.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0f1200000mfyky2DA9.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV221200000mfyfu173D.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3y1200000mfykq8EAB.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3t1200000mfyfi6634.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4u1200000mfyksB996.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6m1200000mfykw3586.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4p1200000mfyfk0347.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3z1200000mfyl7B4FF.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4v1200000mfyl88670.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4i1200000mhi1t786E.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV331200000mhetqD17A.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3s1200000mfyf32B22.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV1a1200000mfykl27B8.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5k1200000mfyf8E236.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6l1200000mfykhD221.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV2w1200000mfyf21D80.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV081200000mfyev79BB.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4o1200000mfyf59CFC.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5p1200000mfykf416E.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6g1200000mfyfaC00F.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV141200000mfyey7E88.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4t1200000mfykeC8C2.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV201200000mfyf1F2C9.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0e1200000mfykjE984.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3x1200000mfyka444B.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV261200000mfykm05C6.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV151200000mfyfc70BC.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV091200000mfyfbD8EA.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV211200000mfyfe0457.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV2x1200000mfyfg9B61.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV321200000mfyko0536.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV5l1200000mfyfmFCA6.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV0f1200000mfyky2DA9.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV221200000mfyfu173D.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3y1200000mfykq8EAB.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3t1200000mfyfi6634.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4u1200000mfyksB996.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6m1200000mfykw3586.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4p1200000mfyfk0347.jpg", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV3z1200000mfyl7B4FF.jpg"], "transmissionType": 1, "brandName": "别克", "oilType": 5, "struct": "两厢车", "groupName": "舒适轿车", "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "83", "guidSys": "", "transmissionName": "自动挡"}], "storeList": [{"pickOffLevel": 47158, "storeCode": "114590", "pickUpLevel": 47158}, {"pickOffLevel": 23665, "storeCode": "106896", "pickUpLevel": 23665}, {"pickOffLevel": 43319, "storeCode": "114315", "pickUpLevel": 43319}, {"pickOffLevel": 30154, "storeCode": "107119", "pickUpLevel": 30154}, {"pickOffLevel": 47132, "storeCode": "114575", "pickUpLevel": 47132}, {"pickOffLevel": 24129, "storeCode": "107242", "pickUpLevel": 24129}, {"pickOffLevel": 23028, "storeCode": "107102", "pickUpLevel": 23028}, {"pickOffLevel": 68967, "storeCode": "114886", "pickUpLevel": 68967}, {"pickOffLevel": 60298, "storeCode": "116954", "pickUpLevel": 60298}, {"pickOffLevel": 48773, "storeCode": "115340", "pickUpLevel": 48773}, {"pickOffLevel": 46866, "storeCode": "114529", "pickUpLevel": 46866}], "promptInfos": [{"button": {"icon": "https://dimg.fws.qa.nt.ctripcorp.com/images/0300a1200000dnqh32FD1.png"}, "backGroundUrl": "https://dimg.fws.qa.nt.ctripcorp.com/images/0306d1200000dnjea0D12.png", "tangChoiceTypes": "[2]", "jumpUrl": "/rn_car_main/_crn_config?CRNModuleName=rn_car_main&CRNType=1&initialPage=Member&apptype=ISD_C_APP", "locations": [{"groupCode": "all", "index": 1}], "contents": [{"contentStyle": "1", "stringObjs": [{"content": "黄金贵宾", "style": "0"}, {"url": "https://dimg.fws.qa.nt.ctripcorp.com/images/0300h1200000dnq527E8C.png"}]}, {"stringObjs": [{"content": "专享租车费95折", "style": "0"}]}], "type": 11, "icon": "https://dimg.fws.qa.nt.ctripcorp.com/images/0300z1200000dnjdo675F.png", "textColor": {"r": 174, "b": 50, "g": 128, "a": 1}}, {"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": false, "isLastPage": false, "uniqSign": "1200113989032147953643I72320Yi3e2701LNS6", "pHub": 0, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"sortNum": 1, "showLayer": 1, "title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "subTitle": "*国定节假日以页面披露为准"}, {"sortNum": 3, "title": "优质车况", "subTitle": "", "type": 1, "description": "3年内车龄", "showLayer": 0}, {"sortNum": 10, "title": "满油取车", "subTitle": "", "type": 0, "description": "保障取车时油量满", "showLayer": 0}, {"sortNum": 99, "title": "车辆守护升级", "subTitle": "*覆盖损失范围以预订页面内披露为准", "type": 0, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧"}]}, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "190694978017997079", "Id": "CLOGGING_TRACE_ID"}, {"Value": "100025527-0a068a23-472010-104351", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1699238192890+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": true, "isCacheValid": true, "networkCost": 0, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 7708, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1699238185010, "afterFetch": 1699238192718, "hasRetry": false, "originNetworkCost": 7708}}