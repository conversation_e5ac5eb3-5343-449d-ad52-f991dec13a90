{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "6eea1a35-8e29-4078-b893-df71540d53ba", "extMap": {"runAsyncCost_2": "0.0", "checkRentCenter_2": "0.0", "apiCost": "957.0", "mergeGroup_9": "0.0", "contextBuilderCost_3": "16.0", "restOriginalCost": "1175.0", "mergeGroupSize_10": "2", "mergeGroupSize_11": "3", "pageName": "List", "ubtProcessCost_8": "1.0", "initBaseData_1": "16.0", "calculatePreAuth_3": "0.0", "mergeGroupSize_9": "55", "uid": "_WeChat372024043", "mergeGroupSize_7": "3", "mergeGroup_newenergy": "3.0", "allCost": "1175.0", "end": "2023-05-24 20:10:10", "dataConvertResCost": "216.0", "dropoffCityId": "43", "IncludeFeesCost": "0.0", "start": "2023-05-24 20:10:09", "shoppingCost_1": "958.0", "mergeGroupSize_2": "66", "mergeGroupSize_newenergy": "97", "mergeGroupSize_5": "38", "mergeGroupSize_6": "148", "mergeGroupSize_3": "42", "gsCost": "1.0", "mergeGroupSize_4": "56", "buildInfoCost_2": "139.0", "buildInfoCost_3": "0.0", "buildInfoCost_4": "139.0", "mergeGroup_11": "0.0", "setProductGroupsHashCodeCostAffect": "0.0", "buildInfoCost_1": "0.0", "mergeGroup_7": "0.0", "productGroupCost_6": "6.0", "mergeGroup_6": "1.0", "lastInfoCost_7": "55.0", "originalCode": "200", "mergeGroup_5": "0.0", "mergeGroup_4": "1.0", "mergeGroup_10": "0.0", "mergeGroup_3": "0.0", "mergeGroup_2": "1.0", "pickupCityId": "43", "7_getFromRedisCost": "2", "11_getFromRedisCost": "2", "newenergy_getFromRedisCost": "79", "2_getFromRedisCost": "35", "4_getFromRedisCost": "39", "needWait": "0", "6_getFromRedisCost": "82", "getPageInfoFromResponse_pre": "0", "requestId": "c970c345-2157-46f5-8baa-b79b1d7804c8", "9_getFromRedisCost": "30", "10_getFromRedisCost": "2", "responseHeadCost": "20", "5_getFromRedisCost": "23", "sourceFrom": "ISD_C_APP", "3_getFromRedisCost": "33", "usePage": "1", "totalCostTime": "111", "restCost": "0"}, "apiResCodes": [], "hasResult": true, "errorCode": "0", "message": ""}, "ResponseStatus": {"Timestamp": "/Date(1684930212872+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "249123779978777647"}, {"Id": "RootMessageId", "Value": "100025527-0a70c3c3-468036-337786"}]}, "requestInfo": {"pickupDate": "/Date(1692842400000+0800)/", "pickupLocationName": "凤凰国际机场T2航站楼", "returnDate": "/Date(1692844200000+0800)/", "returnLocationName": "凤凰国际机场T2航站楼", "sourceCountryId": 1, "age": 30, "pLatitude": 18.308022, "rLatitude": 18.308022, "rLongitude": 109.414204, "pLongitude": 109.414204, "pDate": "20230824100000", "rDate": "20230824103000"}, "allVehicleCount": 413, "allVendorPriceCount": 1466, "vehicleList": [{"brandEName": "奥迪", "brandName": "奥迪", "name": "奥迪Q2L", "zhName": "奥迪Q2L", "vehicleCode": "4883", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 4, "displacement": "1.4T", "struct": "SUV", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz07cn162bh7t05yo0101.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0412n120008asvwxmC22C.jpg", "https://dimg04.c-ctrip.com//images/0410u120008asrbkg1455.jpg", "https://dimg04.c-ctrip.com//images/04109120008asvicn180A.jpg", "https://dimg04.c-ctrip.com//images/0414e120008astcbp22CE.jpg", "https://dimg04.c-ctrip.com//images/0412k120008astk2l5382.jpg", "https://dimg04.c-ctrip.com//images/0416x120008asv8njB476.jpg", "https://dimg04.c-ctrip.com//images/0412l120008asrtxpDD37.jpg", "https://dimg04.c-ctrip.com//images/0412n120008asvwxmC22C.jpg", "https://dimg04.c-ctrip.com//images/0410u120008asrbkg1455.jpg", "https://dimg04.c-ctrip.com//images/04109120008asvicn180A.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0410r120009sl5hhi6026.png", "oilType": 3, "recommendLabels": [{"title": "颜值高", "subTitle": "外观时尚"}], "luggageNum": "可放4个24寸行李箱", "autoPark": false, "carPhone": true, "autoBackUp": true, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5787&app_ver=10.5", "vehiclesSetId": "13"}, {"brandEName": "大众", "brandName": "大众", "name": "大众朗逸", "zhName": "大众朗逸", "vehicleCode": "1069", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.4T-1.6L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://pages.c-ctrip.com/carisd/app/11137.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5812000b02rljv3DAC.jpg", "https://dimg04.c-ctrip.com/images/0RV0l12000b02rbnq4778.jpg", "https://dimg04.c-ctrip.com/images/0RV4d12000b02rmbk9449.jpg", "https://dimg04.c-ctrip.com/images/0RV6312000b02radc5518.jpg", "https://dimg04.c-ctrip.com/images/0RV3y12000b02rhyo8316.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV5812000b02rljv3DAC.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "autoBackUp": false, "vr": "", "vehiclesSetId": "71"}, {"brandEName": "宝马", "brandName": "宝马", "name": "宝马4系 软顶敞篷版", "zhName": "宝马4系 软顶敞篷版", "vehicleCode": "5342", "groupCode": "9", "groupSubClassCode": "", "groupName": "跑车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 4, "doorNo": 2, "luggageNo": 2, "displacement": "2.0T", "struct": "软顶敞篷车", "fuel": "95号", "driveMode": "前置后驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0AS6q120008gyix0k914E.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5t12000axnygcy069C.jpg", "https://dimg04.c-ctrip.com/images/0RV6z12000axnytinDA8B.jpg", "https://dimg04.c-ctrip.com/images/0RV7212000axnz4ma1E21.jpg", "https://dimg04.c-ctrip.com/images/0RV6b12000axnz234C577.jpg", "https://dimg04.c-ctrip.com/images/0RV6s12000axnyuiwEC72.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1e12000b74uplzC011.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": true, "carPhone": true, "autoStart": true, "autoBackUp": true, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3794&app_ver=10.5", "vehiclesSetId": "121"}, {"brandEName": "广汽传祺", "brandName": "广汽传祺", "name": "传祺M8", "zhName": "传祺M8", "vehicleCode": "4088", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 1, "displacement": "2.0T", "struct": "MPV", "fuel": "92号、95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6912000av78b0rE5E1.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1r12000av78kqf41D9.jpg", "https://dimg04.c-ctrip.com/images/0RV2412000av78pxbD627.jpg", "https://dimg04.c-ctrip.com/images/0RV0i12000av78ky66E4B.jpg", "https://dimg04.c-ctrip.com/images/0RV4312000av78qht0DD8.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2u12000av78mro2047.jpg", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=430&app_ver=10.5", "vehiclesSetId": "50"}, {"brandEName": "理想汽车", "brandName": "理想汽车", "name": "理想ONE", "zhName": "理想ONE", "vehicleCode": "5595", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 6, "doorNo": 5, "luggageNo": 2, "struct": "SUV", "fuel": "95号", "driveMode": "双电机四驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0410g120008n3bkx1FEB6.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV2o12000b78eqqt71EC.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2o12000b78eqqt71EC.jpg", "oilType": 4, "recommendLabels": [{"title": "空间大", "subTitle": "6座出游无压力"}], "fuelType": "增程式", "luggageNum": "可放2个24寸行李箱", "endurance": "工信部续航148km-180km", "charge": "快充0.5小时,慢充6小时", "autoPark": true, "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3041&app_ver=10.5", "vehiclesSetId": "20"}, {"brandEName": "大众", "brandName": "大众", "name": "大众宝来", "zhName": "大众宝来", "vehicleCode": "4067", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0qcn143fagmgoxd04D4.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0414f120008at7adg597F.jpg", "https://dimg04.c-ctrip.com//images/04107120008at6v58BAE5.jpg", "https://dimg04.c-ctrip.com//images/0414y120008k97c20F174.jpg", "https://dimg04.c-ctrip.com//images/0415t120008at6ou0D58A.jpg", "https://dimg04.c-ctrip.com//images/0413r120008at5hb7D462.jpg", "https://dimg04.c-ctrip.com//images/0410r120008at5hwz665E.jpg", "https://dimg04.c-ctrip.com//images/0414s120008k9bbmfAD66.jpg", "https://dimg04.c-ctrip.com//images/0411c120008at6gmw507E.jpg", "https://dimg04.c-ctrip.com//images/0414f120008at7554DDE5.jpg", "https://dimg04.c-ctrip.com//images/0416m120008at6vq4D32C.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com//images/04156120008at71ga4625.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5874&app_ver=10.5", "vehiclesSetId": "71"}, {"brandEName": "大众", "brandName": "大众", "name": "大众迈腾", "zhName": "大众迈腾", "vehicleCode": "1149", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.4T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0bcn152vdwzklvl30BE.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0414y120008n8ad4rBC3F.jpg", "https://dimg04.c-ctrip.com//images/0410v120008n88bnw145F.jpg", "https://dimg04.c-ctrip.com//images/0410j120008n898t9FE5A.jpg", "https://dimg04.c-ctrip.com//images/0410v120008n8990u5CDF.jpg", "https://dimg04.c-ctrip.com//images/0415g120008n86kh80FC7.jpg", "https://dimg04.c-ctrip.com//images/0412b120008n8biwqF37C.jpg", "https://dimg04.c-ctrip.com//images/0415a120008n88gm67444.jpg", "https://dimg04.c-ctrip.com//images/0412k120008n8awc1A9DE.jpg", "https://dimg04.c-ctrip.com//images/0412w120008n87s8c00C5.jpg", "https://dimg04.c-ctrip.com//images/0415x120008n889xrA1CA.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0413l120009sl11z62983.png", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "vr": "", "vehiclesSetId": "89"}, {"brandEName": "MINI", "brandName": "MINI", "name": "MINI 软顶敞篷版", "zhName": "MINI 软顶敞篷版", "vehicleCode": "2801", "groupCode": "9", "groupSubClassCode": "", "groupName": "跑车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 4, "doorNo": 2, "luggageNo": 2, "displacement": "1.5T-1.6L", "struct": "软顶敞篷车", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04104120008n39zfp88CF.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04136120009sl4z5887D1.png"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04136120009sl4z5887D1.png", "oilType": 3, "recommendLabels": [{"title": "颜值高", "subTitle": "外观时尚，可爱拉风"}], "luggageNum": "可放2个24寸行李箱", "carPhone": true, "autoStart": true, "autoBackUp": false, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=2641&app_ver=10.5", "vehiclesSetId": "121"}, {"brandEName": "特斯拉", "brandName": "特斯拉", "name": "特斯拉Model Y", "zhName": "特斯拉Model Y", "vehicleCode": "5368", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 5, "struct": "SUV", "driveMode": "双电机四驱、后置后驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04152120008n3ck3h8EF0.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1112000axk2lg07C9B.jpg", "https://dimg04.c-ctrip.com/images/0RV2o12000axk2c6z96FE.jpg", "https://dimg04.c-ctrip.com/images/0RV3112000axk2rq60471.jpg", "https://dimg04.c-ctrip.com/images/0RV2i12000axk2f2e90FC.jpg", "https://dimg04.c-ctrip.com/images/0RV4q12000axk2pq1E982.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0n12000b76j6uj7FA4.jpg", "oilType": 5, "recommendLabels": [{"title": "起步快", "subTitle": "百公里加速最快3.7秒"}], "fuelType": "纯电动", "luggageNum": "可放5个24寸行李箱", "endurance": "工信部续航525km-660km", "charge": "快充1小时,慢充10小时", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5703&app_ver=10.5", "vehiclesSetId": "15"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田 卡罗拉", "zhName": "丰田 卡罗拉", "vehicleCode": "5407", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.2T-1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV2s12000as0bthw755A.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0412h120008n8c3ht677C.jpg", "https://dimg04.c-ctrip.com//images/04159120008n8dpfbCD35.jpg", "https://dimg04.c-ctrip.com//images/0410u120008n8cxkcD9A7.jpg", "https://dimg04.c-ctrip.com//images/0413g120008n8e0r71EC2.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com//images/04145120008n8dwxj79B1.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "carPhone": true, "autoBackUp": true, "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7652&app_ver=10.5", "vehiclesSetId": "71"}, {"brandEName": "大众", "brandName": "大众", "name": "大众ID.4 X", "zhName": "大众ID.4 X", "vehicleCode": "6258", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 5, "struct": "SUV", "driveMode": "后置后驱、双电机四驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0410f120009e01lk243D8.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0410f120009e01lk243D8.png"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0410f120009e01lk243D8.png", "oilType": 5, "fuelType": "纯电动", "luggageNum": "可放5个24寸行李箱", "endurance": "工信部续航425km-607km", "charge": "快充0.67小时,慢充12.5小时", "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=7138&app_ver=10.5", "vehiclesSetId": "15"}, {"brandEName": "哪吒汽车", "brandName": "哪吒汽车", "name": "哪吒V", "zhName": "哪吒V", "vehicleCode": "5453", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "struct": "SUV", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0AS0j120008nbes2dA14A.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5u12000axbp45mFFA8.jpg", "https://dimg04.c-ctrip.com/images/0RV6n12000axbp9voBF36.jpg", "https://dimg04.c-ctrip.com/images/0RV6b12000axbp7o5667C.jpg", "https://dimg04.c-ctrip.com/images/0RV1512000b74jwdv0F5F.jpg", "https://dimg04.c-ctrip.com/images/0RV0z12000axbp9n90A84.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV5612000b62xobn6DB9.jpg", "oilType": 5, "fuelType": "纯电动", "luggageNum": "可放2个24寸行李箱", "endurance": "工信部续航301km-401km", "charge": "快充0.5小时,慢充12小时", "autoStart": true, "subGroupCode": "newenergy", "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5234&app_ver=10.5", "vehiclesSetId": "15"}], "productGroups": [{"groupCode": "all", "groupName": "全部车型", "sortNum": -4, "productList": [{"vehicleCode": "4883", "sortNum": 15, "lowestPrice": 44.0, "highestPrice": 572.0, "maximumRating": 5.0, "maximumCommentCount": 34708, "lowestDistance": 30.227, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4102", "vendorCode": "81525", "pStoreCode": "107474", "rStoreCode": "107474", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]MzE0fDI3MDd8MjAyMC4wMC0yNCAzLTA4MDowMDAwOjAmNTAmJjUwMDAwJHwwLjUwJjEmNTEwMDEwJDEwMDAmNSYyMC4wMyYxMC4wMDAwJjIyJjEmJDEwMDAmMy4zMC4wMjAyMzAwJHwyNCAyLTA1LTowOQAwOjEw", "priceVersion": "SH-PRICEVERSION_MTA3NDc0XzQ4ODNfMV81MDBfNTBfNTBfNzMuMDBfNDk0LjBfNjcuMF8wXzBfMC4wXzAuMF8zLjAwXzIwLjAwXzAuMDBfMC4wMF8yNzA3MzE0", "vendorVehicleCode": "20072153", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 2707314, "pLev": 26867, "rLev": 26867, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 44.0, "amountStr": "¥44", "detail": [{"code": "1001", "name": "租车费", "amount": 50, "amountDesc": "¥50"}, {"code": "3743", "name": "周三福利日", "amount": 6, "amountDesc": "¥6"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 3.0, "amountStr": "¥3", "detail": [{"code": "1002", "name": "基础服务费", "amount": 3.0, "amountDesc": "¥3", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 67.0, "amountStr": "¥67", "subAmount": 73.0, "subAmountStr": "¥73", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 1863489}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1863848}}, {"reference": {"bizVendorCode": "SD3068", "vehicleCode": "0", "packageType": 1, "skuId": 1772206}}, {"reference": {"bizVendorCode": "SD4949", "vehicleCode": "0", "packageType": 1, "skuId": 2865492}}, {"reference": {"bizVendorCode": "SD5613", "vehicleCode": "0", "packageType": 1, "skuId": 4689356}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 4710017}}, {"reference": {"bizVendorCode": "SD4919", "vehicleCode": "0", "packageType": 1, "skuId": 4394156}}, {"reference": {"bizVendorCode": "SD4407", "vehicleCode": "0", "packageType": 1, "skuId": 2428179}}], "reactId": "2010104110", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4102_0_107474_107474"]}, "minTPrice": 67.0, "minDPrice": 44.0, "modifySameVehicle": false, "minDOrinPrice": 50, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}, "priceSize": 9, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "1069", "sortNum": 16, "lowestPrice": 51.0, "highestPrice": 297.0, "maximumRating": 5.0, "maximumCommentCount": 37773, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3068", "vendorCode": "30055", "pStoreCode": "106032", "rStoreCode": "106032", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]MzAxfDE3NzJ8MjAyMC4wMC0yNCAzLTA4MDowMDAwOjA1OCYwJjU4JjAkfDEuNTAwMSY1ODAwMSYxMDAzJjU4JDAuMDAmMSYyMDAkMSYyMC4xJjMwMDAyJjMwLjAuMDAmMDIzLTAkfDI0IDIwMDUtMjA5AAA6MTA6", "priceVersion": "SH-PRICEVERSION_MTA2MDMyXzEwNjlfMV81OF81OF81OF8xMDguMDBfNTEuMF8xMDEuMF8wXzBfMC4wXzAuMF8zMC4wMF8yMC4wMF8wLjAwXzAuMDBfMTc3MjMwMQ==", "vendorVehicleCode": "23076", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1772301, "pLev": 16851, "rLev": 16851, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 51.0, "amountStr": "¥51", "detail": [{"code": "1001", "name": "租车费", "amount": 58, "amountDesc": "¥58"}, {"code": "3743", "name": "周三福利日", "amount": 7, "amountDesc": "¥7"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 30.0, "amountStr": "¥30", "detail": [{"code": "1002", "name": "基础服务费", "amount": 30.0, "amountDesc": "¥30", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 101.0, "amountStr": "¥101", "subAmount": 108.0, "subAmountStr": "¥108", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 1865402}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 3447035}}, {"reference": {"bizVendorCode": "SD4057", "vehicleCode": "0", "packageType": 1, "skuId": 1862623}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 1914999}}], "reactId": "2010104061", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.14233714, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3068_0_106032_106032"]}, "minTPrice": 101.0, "minDPrice": 51.0, "modifySameVehicle": false, "minDOrinPrice": 58, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减7", "groupId": 1, "mergeId": 0}, "priceSize": 5, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5342", "sortNum": 17, "lowestPrice": 187.0, "highestPrice": 4399.0, "maximumRating": 5.0, "maximumCommentCount": 37773, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD6355", "vendorCode": "47522", "pStoreCode": "116135", "rStoreCode": "116135", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTE2MTM1XzUzNDJfMV81NjJfMTk3XzE5N18yNDguMDBfNTUyLjBfMjM4LjBfMF8wXzAuMF8wLjBfMjEuMDBfMzAuMDBfMC4wMF8wLjAwXzE5Njg4ODc=", "vendorVehicleCode": "20071365", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1968887, "pLev": 56985, "rLev": 56985, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 187.0, "amountStr": "¥187", "detail": [{"code": "1001", "name": "租车费", "amount": 197, "amountDesc": "¥197"}, {"code": "3783", "name": "黄金贵宾", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 30.0, "amountStr": "¥30", "detail": [{"code": "1003", "name": "车行手续费", "amount": 30.0, "amountDesc": "¥30", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 21.0, "amountStr": "¥21", "detail": [{"code": "1002", "name": "基础服务费", "amount": 21.0, "amountDesc": "¥21", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 238.0, "amountStr": "¥238", "subAmount": 248.0, "subAmountStr": "¥248", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 1, "skuId": 2288604}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1854623}}, {"reference": {"bizVendorCode": "SD4416", "vehicleCode": "0", "packageType": 1, "skuId": 1915517}}, {"reference": {"bizVendorCode": "SD7103", "vehicleCode": "0", "packageType": 0, "skuId": 3894446}}, {"reference": {"bizVendorCode": "SD4507", "vehicleCode": "0", "packageType": 1, "skuId": 2316930}}, {"reference": {"bizVendorCode": "SD6119", "vehicleCode": "0", "packageType": 0, "skuId": 1979056}}, {"reference": {"bizVendorCode": "SD5775", "vehicleCode": "0", "packageType": 1, "skuId": 2498350}}, {"reference": {"bizVendorCode": "SD6796", "vehicleCode": "0", "packageType": 1, "skuId": 1972899}}, {"reference": {"bizVendorCode": "SD4102", "vehicleCode": "0", "packageType": 1, "skuId": 1864418}}, {"reference": {"bizVendorCode": "SD4949", "vehicleCode": "0", "packageType": 1, "skuId": 1920812}}, {"reference": {"bizVendorCode": "SD6175", "vehicleCode": "0", "packageType": 0, "skuId": 2288379}}, {"reference": {"bizVendorCode": "SD5943", "vehicleCode": "0", "packageType": 0, "skuId": 1971823}}, {"reference": {"bizVendorCode": "SD5097", "vehicleCode": "0", "packageType": 1, "skuId": 4549322}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 1864416}}, {"reference": {"bizVendorCode": "SD4057", "vehicleCode": "0", "packageType": 1, "skuId": 1854583}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 1, "skuId": 1922992}}, {"reference": {"bizVendorCode": "SD3225", "vehicleCode": "0", "packageType": 1, "skuId": 1817033}}, {"reference": {"bizVendorCode": "SD5095", "vehicleCode": "0", "packageType": 0, "skuId": 1919884}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 1909349}}, {"reference": {"bizVendorCode": "SD4130", "vehicleCode": "0", "packageType": 1, "skuId": 1863049}}], "reactId": "2010104120", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD6355_0_116135_116135"]}, "minTPrice": 238.0, "minDPrice": 187.0, "modifySameVehicle": false, "minDOrinPrice": 197, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}, "priceSize": 21, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4088", "sortNum": 18, "lowestPrice": 92.0, "highestPrice": 839.0, "maximumRating": 5.0, "maximumCommentCount": 34708, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD5048", "vendorCode": "82731", "pStoreCode": "115265", "rStoreCode": "115265", "vehicleCode": "0", "packageType": 0, "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTE1MjY1XzQwODhfMV8xOThfOTlfOTlfMTM0LjAwXzE5My4wXzEyOS4wXzBfMF8wLjBfMC4wXzE1LjAwXzIwLjAwXzAuMDBfMC4wMF8xOTIyODA1", "vendorVehicleCode": "20091851", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1922805, "pLev": 49817, "rLev": 49817, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 94.0, "amountStr": "¥94", "detail": [{"code": "1001", "name": "租车费", "amount": 99, "amountDesc": "¥99"}, {"code": "3783", "name": "黄金贵宾", "amount": 5, "amountDesc": "¥5"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 15.0, "amountStr": "¥15", "detail": [{"code": "1002", "name": "基础服务费", "amount": 15.0, "amountDesc": "¥15", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 129.0, "amountStr": "¥129", "subAmount": 134.0, "subAmountStr": "¥134", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 2678265}}, {"reference": {"bizVendorCode": "SD4394", "vehicleCode": "0", "packageType": 1, "skuId": 4153405}}, {"reference": {"bizVendorCode": "SD3068", "vehicleCode": "0", "packageType": 1, "skuId": 1772333}}, {"reference": {"bizVendorCode": "SD3846", "vehicleCode": "0", "packageType": 0, "skuId": 1904695}}, {"reference": {"bizVendorCode": "SD4507", "vehicleCode": "0", "packageType": 1, "skuId": 1907904}}, {"reference": {"bizVendorCode": "SD5613", "vehicleCode": "0", "packageType": 1, "skuId": 4876029}}, {"reference": {"bizVendorCode": "SD4671", "vehicleCode": "0", "packageType": 1, "skuId": 1918697}}, {"reference": {"bizVendorCode": "SD5097", "vehicleCode": "0", "packageType": 0, "skuId": 1909365}}, {"reference": {"bizVendorCode": "SD6414", "vehicleCode": "0", "packageType": 1, "skuId": 4153021}}, {"reference": {"bizVendorCode": "SD4950", "vehicleCode": "0", "packageType": 1, "skuId": 2502182}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 4710002}}, {"reference": {"bizVendorCode": "SD5418", "vehicleCode": "0", "packageType": 1, "skuId": 1976458}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 1, "skuId": 1914391}}, {"reference": {"bizVendorCode": "SD6556", "vehicleCode": "0", "packageType": 1, "skuId": 4153038}}, {"reference": {"bizVendorCode": "SD4579", "vehicleCode": "0", "packageType": 0, "skuId": 1922065}}], "reactId": "20101041011", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD8256_0_107108_107108"]}, "minTPrice": 129.0, "minDPrice": 94.0, "modifySameVehicle": false, "minDOrinPrice": 99, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减5", "groupId": 1, "mergeId": 0}, "priceSize": 16, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5595", "sortNum": 19, "lowestPrice": 81.0, "highestPrice": 3500.0, "maximumRating": 5.0, "maximumCommentCount": 34708, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4579", "vendorCode": "69287", "pStoreCode": "114768", "rStoreCode": "114768", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]ODExfDE5MTF8MjAyMC4wMC0yNCAzLTA4MDowMDAwOjAmOTYmJjI4ODAwJHwwLjUwJjEmMjEwMDE2JDEwODgmOSYyMC4wMyYxMC4wMDAwJjIyJjEmJDEwMDAmMTAzMC4wfDIwMi4wMCQtMjQgMy0wNTA6MDkyMDox", "priceVersion": "SH-PRICEVERSION_MTE0NzY4XzU1OTVfMV8yODhfOTZfOTZfMTI2LjAwXzI3My4wXzExMS4wXzBfMF8wLjBfMC4wXzEwLjAwXzIwLjAwXzAuMDBfMC4wMF8xOTExODEx", "vendorVehicleCode": "20081691", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1911811, "pLev": 45746, "rLev": 45746, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 81.0, "amountStr": "¥81", "detail": [{"code": "1001", "name": "租车费", "amount": 96, "amountDesc": "¥96"}, {"code": "3765", "name": "新能源特惠", "amount": 15, "amountDesc": "¥15"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 10.0, "amountStr": "¥10", "detail": [{"code": "1002", "name": "基础服务费", "amount": 10.0, "amountDesc": "¥10", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 111.0, "amountStr": "¥111", "subAmount": 126.0, "subAmountStr": "¥126", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD5536", "vehicleCode": "0", "packageType": 1, "skuId": 1958417}}, {"reference": {"bizVendorCode": "SD5751", "vehicleCode": "0", "packageType": 0, "skuId": 4899887}}, {"reference": {"bizVendorCode": "SD6833", "vehicleCode": "0", "packageType": 0, "skuId": 4680948}}, {"reference": {"bizVendorCode": "SD6414", "vehicleCode": "0", "packageType": 1, "skuId": 1959831}}, {"reference": {"bizVendorCode": "SD5615", "vehicleCode": "0", "packageType": 1, "skuId": 1960404}}, {"reference": {"bizVendorCode": "SD6355", "vehicleCode": "0", "packageType": 0, "skuId": 4875662}}, {"reference": {"bizVendorCode": "SD3977", "vehicleCode": "0", "packageType": 1, "skuId": 2711215}}, {"reference": {"bizVendorCode": "SD8155", "vehicleCode": "0", "packageType": 0, "skuId": 4609488}}, {"reference": {"bizVendorCode": "SD5566", "vehicleCode": "0", "packageType": 0, "skuId": 1958140}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 1855955}}, {"reference": {"bizVendorCode": "SD4102", "vehicleCode": "0", "packageType": 1, "skuId": 1865282}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 4511076}}, {"reference": {"bizVendorCode": "SD5905", "vehicleCode": "0", "packageType": 1, "skuId": 2709954}}, {"reference": {"bizVendorCode": "SD7103", "vehicleCode": "0", "packageType": 1, "skuId": 2711980}}, {"reference": {"bizVendorCode": "SD3847", "vehicleCode": "0", "packageType": 1, "skuId": 1860389}}, {"reference": {"bizVendorCode": "SD6459", "vehicleCode": "0", "packageType": 1, "skuId": 1961326}}, {"reference": {"bizVendorCode": "SD5613", "vehicleCode": "0", "packageType": 1, "skuId": 4618248}}, {"reference": {"bizVendorCode": "SD8144", "vehicleCode": "0", "packageType": 1, "skuId": 4511752}}, {"reference": {"bizVendorCode": "SD4416", "vehicleCode": "0", "packageType": 1, "skuId": 1923803}}, {"reference": {"bizVendorCode": "SD4949", "vehicleCode": "0", "packageType": 1, "skuId": 1912373}}, {"reference": {"bizVendorCode": "SD5066", "vehicleCode": "0", "packageType": 1, "skuId": 1911063}}, {"reference": {"bizVendorCode": "SD6558", "vehicleCode": "0", "packageType": 1, "skuId": 1977756}}, {"reference": {"bizVendorCode": "SD4919", "vehicleCode": "0", "packageType": 1, "skuId": 3215974}}, {"reference": {"bizVendorCode": "SD6796", "vehicleCode": "0", "packageType": 1, "skuId": 1971271}}, {"reference": {"bizVendorCode": "SD4950", "vehicleCode": "0", "packageType": 1, "skuId": 1920914}}, {"reference": {"bizVendorCode": "SD6628", "vehicleCode": "0", "packageType": 0, "skuId": 1959004}}, {"reference": {"bizVendorCode": "SD3846", "vehicleCode": "0", "packageType": 0, "skuId": 1886171}}, {"reference": {"bizVendorCode": "SD4397", "vehicleCode": "0", "packageType": 0, "skuId": 1921349}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 1856606}}, {"reference": {"bizVendorCode": "SD5095", "vehicleCode": "0", "packageType": 0, "skuId": 1921150}}, {"reference": {"bizVendorCode": "SD3225", "vehicleCode": "0", "packageType": 1, "skuId": 1873814}}, {"reference": {"bizVendorCode": "SD5727", "vehicleCode": "0", "packageType": 1, "skuId": 1969000}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 1, "skuId": 1922263}}, {"reference": {"bizVendorCode": "SD5775", "vehicleCode": "0", "packageType": 0, "skuId": 2777441}}, {"reference": {"bizVendorCode": "SD4057", "vehicleCode": "0", "packageType": 1, "skuId": 1859753}}, {"reference": {"bizVendorCode": "SD4130", "vehicleCode": "0", "packageType": 1, "skuId": 1864128}}], "reactId": "2010104111", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4579_0_114768_114768"]}, "minTPrice": 111.0, "minDPrice": 81.0, "modifySameVehicle": false, "minDOrinPrice": 96, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "新能源特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3765", "groupCode": "MarketGroup1358", "amountTitle": "已减15", "groupId": 1, "mergeId": 0}, "priceSize": 37, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4067", "sortNum": 20, "lowestPrice": 36.0, "highestPrice": 514.0, "maximumRating": 5.0, "maximumCommentCount": 37773, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3977", "vendorCode": "62408", "pStoreCode": "106944", "rStoreCode": "106944", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]NDIyfDE4NjF8MjAyMC4wMC0yNCAzLTA4MDowMDAwOjAmNDImJjExODAwJHwwLjUwJjEmMTEwMDEyJDEwMTgmNCYyMC4wMyYxMC4wMDAwJjIyJjEmJDEwMDAmMTEzMC4wfDIwMi4wMCQtMjQgMy0wNTA6MDkyMDox", "priceVersion": "SH-PRICEVERSION_MTA2OTQ0XzQwNjdfMV8xMThfNDJfNDJfNzMuMDBfMTEyLjBfNjcuMF8wXzBfMC4wXzAuMF8xMS4wMF8yMC4wMF8wLjAwXzAuMDBfMTg2MTQyMg==", "vendorVehicleCode": "14882", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1861422, "pLev": 24630, "rLev": 24630, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 36.0, "amountStr": "¥36", "detail": [{"code": "1001", "name": "租车费", "amount": 42, "amountDesc": "¥42"}, {"code": "3743", "name": "周三福利日", "amount": 6, "amountDesc": "¥6"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 11.0, "amountStr": "¥11", "detail": [{"code": "1002", "name": "基础服务费", "amount": 11.0, "amountDesc": "¥11", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 67.0, "amountStr": "¥67", "subAmount": 73.0, "subAmountStr": "¥73", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 1856767}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1856407}}, {"reference": {"bizVendorCode": "SD8155", "vehicleCode": "0", "packageType": 1, "skuId": 4581968}}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 0, "skuId": 1920948}}, {"reference": {"bizVendorCode": "SD3637", "vehicleCode": "0", "packageType": 1, "skuId": 2375856}}, {"reference": {"bizVendorCode": "SD5727", "vehicleCode": "0", "packageType": 1, "skuId": 1964054}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 3166315}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 1907908}}, {"reference": {"bizVendorCode": "SD4919", "vehicleCode": "0", "packageType": 1, "skuId": 3054112}}], "reactId": "2010104062", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3977_0_106944_106944"]}, "minTPrice": 67.0, "minDPrice": 36.0, "modifySameVehicle": false, "minDOrinPrice": 42, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}, "priceSize": 10, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "1149", "sortNum": 21, "lowestPrice": 61.0, "highestPrice": 614.0, "maximumRating": 5.0, "maximumCommentCount": 37773, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD8256", "vendorCode": "32231", "pStoreCode": "107102", "rStoreCode": "107102", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]NjY1fDE4NTV8MjAyMC4wMC0yNCAzLTA4MDowMDAwOjAmNzAmJjIwMDAwJHwwLjUwJjEmMjEwMDEwJDEwMDAmNyYzMC4wMyYxMC4wMDAwJjMyJjEmJDEwMDAmMTEzMC4wfDIwMi4wMCQtMjQgMy0wNTA6MDkyMDox", "priceVersion": "SH-PRICEVERSION_MTA3MTAyXzExNDlfMV8yMDBfNzBfNzBfMTExLjAwXzE5MS4wXzEwMi4wXzBfMF8wLjBfMC4wXzExLjAwXzMwLjAwXzAuMDBfMC4wMF8xODU1NjY1", "vendorVehicleCode": "20036665", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1855665, "pLev": 23028, "rLev": 23028, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 61.0, "amountStr": "¥61", "detail": [{"code": "1001", "name": "租车费", "amount": 70, "amountDesc": "¥70"}, {"code": "3743", "name": "周三福利日", "amount": 9, "amountDesc": "¥9"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 30.0, "amountStr": "¥30", "detail": [{"code": "1003", "name": "车行手续费", "amount": 30.0, "amountDesc": "¥30", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 11.0, "amountStr": "¥11", "detail": [{"code": "1002", "name": "基础服务费", "amount": 11.0, "amountDesc": "¥11", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 102.0, "amountStr": "¥102", "subAmount": 111.0, "subAmountStr": "¥111", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3271", "vehicleCode": "0", "packageType": 1, "skuId": 3900912}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 1856274}}, {"reference": {"bizVendorCode": "SD3068", "vehicleCode": "0", "packageType": 1, "skuId": 1772257}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 3166309}}, {"reference": {"bizVendorCode": "SD4057", "vehicleCode": "0", "packageType": 1, "skuId": 1862656}}, {"reference": {"bizVendorCode": "SD3954", "vehicleCode": "0", "packageType": 0, "skuId": 1862686}}, {"reference": {"bizVendorCode": "SD4919", "vehicleCode": "0", "packageType": 1, "skuId": 3402198}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 2704685}}], "reactId": "2010104073", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD8256_0_107102_107102"]}, "minTPrice": 102.0, "minDPrice": 61.0, "modifySameVehicle": false, "minDOrinPrice": 70, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减9", "groupId": 1, "mergeId": 0}, "priceSize": 9, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "2801", "sortNum": 22, "lowestPrice": 109.0, "highestPrice": 880.0, "maximumRating": 5.0, "maximumCommentCount": 37773, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD5751", "vendorCode": "32538", "pStoreCode": "116092", "rStoreCode": "116092", "vehicleCode": "0", "packageType": 0, "comPriceCode": "[c]NDA2fDM3MTgzLTA4fDIwMjAwOjAtMjQgJjMyODA6MDAmMC41JjExNXwxMDAwMDAkMzI4JjEmMSYxMDAzMTE1JDAuMDAmMSYyMDAkMSYyMC4xJjUwMDAyJjE4LjAuMDAmMDIzLTAkfDI0IDIwMDUtMjA5AAA6MTA6", "priceVersion": "SH-PRICEVERSION_MTE2MDkyXzI4MDFfMV8zMjhfMTE1XzExNV8xNTMuMDBfMzIyLjBfMTQ3LjBfMF8wXzAuMF8wLjBfMTguMDBfMjAuMDBfMC4wMF8wLjAwXzM3MTg0MDY=", "vendorVehicleCode": "20064466", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 3718406, "pLev": 56249, "rLev": 56249, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 109.0, "amountStr": "¥109", "detail": [{"code": "1001", "name": "租车费", "amount": 115, "amountDesc": "¥115"}, {"code": "3783", "name": "黄金贵宾", "amount": 6, "amountDesc": "¥6"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 18.0, "amountStr": "¥18", "detail": [{"code": "1002", "name": "基础服务费", "amount": 18.0, "amountDesc": "¥18", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 147.0, "amountStr": "¥147", "subAmount": 153.0, "subAmountStr": "¥153", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4579", "vehicleCode": "0", "packageType": 1, "skuId": 1908320}}, {"reference": {"bizVendorCode": "SD4507", "vehicleCode": "0", "packageType": 1, "skuId": 1910520}}, {"reference": {"bizVendorCode": "SD6833", "vehicleCode": "0", "packageType": 1, "skuId": 1974815}}, {"reference": {"bizVendorCode": "SD4102", "vehicleCode": "0", "packageType": 1, "skuId": 2711178}}, {"reference": {"bizVendorCode": "SD3847", "vehicleCode": "0", "packageType": 1, "skuId": 1925832}}, {"reference": {"bizVendorCode": "SD4949", "vehicleCode": "0", "packageType": 1, "skuId": 1925219}}, {"reference": {"bizVendorCode": "SD6119", "vehicleCode": "0", "packageType": 1, "skuId": 1963679}}, {"reference": {"bizVendorCode": "SD4678", "vehicleCode": "0", "packageType": 1, "skuId": 1924071}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 1856525}}, {"reference": {"bizVendorCode": "SD5943", "vehicleCode": "0", "packageType": 0, "skuId": 1964947}}, {"reference": {"bizVendorCode": "SD3225", "vehicleCode": "0", "packageType": 1, "skuId": 1873817}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 1912460}}, {"reference": {"bizVendorCode": "SD4950", "vehicleCode": "0", "packageType": 1, "skuId": 1911042}}, {"reference": {"bizVendorCode": "SD4224", "vehicleCode": "0", "packageType": 1, "skuId": 2677907}}], "reactId": "2010104121", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD5751_0_116092_116092"]}, "minTPrice": 147.0, "minDPrice": 109.0, "modifySameVehicle": false, "minDOrinPrice": 115, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "黄金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3783", "groupCode": "MarketGroup1369", "amountTitle": "已减6", "groupId": 1, "mergeId": 0}, "priceSize": 15, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5368", "sortNum": 23, "lowestPrice": 85.0, "highestPrice": 3500.0, "maximumRating": 5.0, "maximumCommentCount": 37773, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3977", "vendorCode": "62408", "pStoreCode": "106944", "rStoreCode": "106944", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]MTEwfDE4NjF8MjAyMC4wMC0yNCAzLTA4MDowMDAwOjAmMTM2JjM4ODAwMCQmMC41MSYxJnwxMDAxMzYkMzg4JiYxJjIxMDAzJjIwLjAuMDAwMDImMDAkMS4wMCYxJjMwMCR8MjExLjAwNS0yMDIzLToxMDo0IDIwAAAAADA5AAA=", "priceVersion": "SH-PRICEVERSION_MTA2OTQ0XzUzNjhfMV8zODhfMTM2XzEzNl8xNjcuMDBfMzQ3LjBfMTI2LjBfMF8wXzAuMF8wLjBfMTEuMDBfMjAuMDBfMC4wMF8wLjAwXzE4NjExMTA=", "vendorVehicleCode": "20091149", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1861110, "pLev": 24630, "rLev": 24630, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 95.0, "amountStr": "¥95", "detail": [{"code": "1001", "name": "租车费", "amount": 136, "amountDesc": "¥136"}, {"code": "3765", "name": "新能源特惠", "amount": 41, "amountDesc": "¥41"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 11.0, "amountStr": "¥11", "detail": [{"code": "1002", "name": "基础服务费", "amount": 11.0, "amountDesc": "¥11", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 126.0, "amountStr": "¥126", "subAmount": 167.0, "subAmountStr": "¥167", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD7103", "vehicleCode": "0", "packageType": 1, "skuId": 2441377}}, {"reference": {"bizVendorCode": "SD6275", "vehicleCode": "0", "packageType": 1, "skuId": 4858960}}, {"reference": {"bizVendorCode": "SD4394", "vehicleCode": "0", "packageType": 1, "skuId": 1925566}}, {"reference": {"bizVendorCode": "SD5566", "vehicleCode": "0", "packageType": 0, "skuId": 1970816}}, {"reference": {"bizVendorCode": "SD3766", "vehicleCode": "0", "packageType": 0, "skuId": 1859575}}, {"reference": {"bizVendorCode": "SD4102", "vehicleCode": "0", "packageType": 1, "skuId": 2991819}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1865319}}, {"reference": {"bizVendorCode": "SD5074", "vehicleCode": "0", "packageType": 0, "skuId": 4858695}}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 1, "skuId": 1912807}}, {"reference": {"bizVendorCode": "SD8144", "vehicleCode": "0", "packageType": 1, "skuId": 4511749}}, {"reference": {"bizVendorCode": "SD4416", "vehicleCode": "0", "packageType": 1, "skuId": 1957645}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 1861424}}, {"reference": {"bizVendorCode": "SD4949", "vehicleCode": "0", "packageType": 1, "skuId": 1912530}}, {"reference": {"bizVendorCode": "SD4919", "vehicleCode": "0", "packageType": 1, "skuId": 2511765}}, {"reference": {"bizVendorCode": "SD4579", "vehicleCode": "0", "packageType": 1, "skuId": 2704946}}, {"reference": {"bizVendorCode": "SD5066", "vehicleCode": "0", "packageType": 1, "skuId": 1911570}}, {"reference": {"bizVendorCode": "SD6119", "vehicleCode": "0", "packageType": 0, "skuId": 1969540}}, {"reference": {"bizVendorCode": "SD5613", "vehicleCode": "0", "packageType": 1, "skuId": 4684054}}, {"reference": {"bizVendorCode": "SD4397", "vehicleCode": "0", "packageType": 0, "skuId": 1922877}}, {"reference": {"bizVendorCode": "SD6558", "vehicleCode": "0", "packageType": 0, "skuId": 1977542}}, {"reference": {"bizVendorCode": "SD6414", "vehicleCode": "0", "packageType": 0, "skuId": 1975376}}, {"reference": {"bizVendorCode": "SD6796", "vehicleCode": "0", "packageType": 0, "skuId": 1961044}}, {"reference": {"bizVendorCode": "SD5097", "vehicleCode": "0", "packageType": 1, "skuId": 1957705}}, {"reference": {"bizVendorCode": "SD3846", "vehicleCode": "0", "packageType": 0, "skuId": 1887541}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 4532052}}, {"reference": {"bizVendorCode": "SD3225", "vehicleCode": "0", "packageType": 1, "skuId": 2707765}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 1, "skuId": 1914244}}, {"reference": {"bizVendorCode": "SD5727", "vehicleCode": "0", "packageType": 1, "skuId": 1968809}}, {"reference": {"bizVendorCode": "SD4493", "vehicleCode": "0", "packageType": 1, "skuId": 1914309}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 1856034}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 1918256}}, {"reference": {"bizVendorCode": "SD3008", "vehicleCode": "0", "packageType": 1, "skuId": 1777044}}, {"reference": {"bizVendorCode": "SD6434", "vehicleCode": "0", "packageType": 0, "skuId": 2678044}}, {"reference": {"bizVendorCode": "SD4130", "vehicleCode": "0", "packageType": 1, "skuId": 1857766}}], "reactId": "2010104112", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD6275_0_115926_115926"]}, "minTPrice": 126.0, "minDPrice": 95.0, "modifySameVehicle": false, "minDOrinPrice": 136, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "新能源特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3765", "groupCode": "MarketGroup1358", "amountTitle": "已减41", "groupId": 1, "mergeId": 0}, "priceSize": 35, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5407", "sortNum": 24, "lowestPrice": 20.0, "highestPrice": 176.0, "maximumRating": 5.0, "maximumCommentCount": 4239, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD6574", "vendorCode": "74365", "pStoreCode": "116538", "rStoreCode": "116538", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]ODczfDIzMDJ8MjAyMC4xMC0yNCAzLTA4MDowMDAwOjAzNCYwJjk3JjAkfDEuNTAwMSY5NzAwMSYxMDAzJjM0JDAuMDAmMSYyMDAkMSYyMC4xJjMwMDAyJjExLjAuMDAmMDIzLTAkfDI0IDIwMDUtMjA5AAA6MTA6", "priceVersion": "SH-PRICEVERSION_MTE2NTM4XzU0MDdfMV85N18zNF8zNF82NS4wMF85Mi4wXzYwLjBfMF8wXzAuMF8wLjBfMTEuMDBfMjAuMDBfMC4wMF8wLjAwXzIzMDI4NzM=", "vendorVehicleCode": "20103505", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 2302873, "pLev": 59928, "rLev": 59928, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 29.0, "amountStr": "¥29", "detail": [{"code": "1001", "name": "租车费", "amount": 34, "amountDesc": "¥34"}, {"code": "3743", "name": "周三福利日", "amount": 5, "amountDesc": "¥5"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 11.0, "amountStr": "¥11", "detail": [{"code": "1002", "name": "基础服务费", "amount": 11.0, "amountDesc": "¥11", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 60.0, "amountStr": "¥60", "subAmount": 65.0, "subAmountStr": "¥65", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4102", "vehicleCode": "0", "packageType": 1, "skuId": 1858926}}, {"reference": {"bizVendorCode": "SD5615", "vehicleCode": "0", "packageType": 1, "skuId": 2707363}}, {"reference": {"bizVendorCode": "SD3977", "vehicleCode": "0", "packageType": 1, "skuId": 2677426}}, {"reference": {"bizVendorCode": "SD4374", "vehicleCode": "0", "packageType": 1, "skuId": 1910574}}, {"reference": {"bizVendorCode": "SD3271", "vehicleCode": "0", "packageType": 1, "skuId": 1817275}}, {"reference": {"bizVendorCode": "SD3068", "vehicleCode": "0", "packageType": 1, "skuId": 1772352}}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 0, "skuId": 2704627}}, {"reference": {"bizVendorCode": "SD5613", "vehicleCode": "0", "packageType": 1, "skuId": 4628940}}, {"reference": {"bizVendorCode": "SD3637", "vehicleCode": "0", "packageType": 1, "skuId": 1861182}}, {"reference": {"bizVendorCode": "SD3747", "vehicleCode": "0", "packageType": 1, "skuId": 4462313}}, {"reference": {"bizVendorCode": "SD5727", "vehicleCode": "0", "packageType": 1, "skuId": 2428339}}, {"reference": {"bizVendorCode": "SD5095", "vehicleCode": "0", "packageType": 0, "skuId": 1909974}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 3435946}}, {"reference": {"bizVendorCode": "SD4950", "vehicleCode": "0", "packageType": 1, "skuId": 2867868}}], "reactId": "2010104063", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD5615_0_117455_117455"]}, "minTPrice": 60.0, "minDPrice": 29.0, "modifySameVehicle": false, "minDOrinPrice": 34, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周三福利日", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3743", "groupCode": "MarketGroup1344", "amountTitle": "已减5", "groupId": 1, "mergeId": 0}, "priceSize": 15, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "6258", "sortNum": 25, "lowestPrice": 65.0, "highestPrice": 323.0, "maximumRating": 5.0, "maximumCommentCount": 1566, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4949", "vendorCode": "81829", "pStoreCode": "115233", "rStoreCode": "115233", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]MjE3fDI0NDF8MjAyMC4wMC0yNCAzLTA4MDowMDAwOjAmOTQmJjE4ODAwJHwwLjUwJjEmMTEwMDE0JDEwODgmOSYyMC4wMyYxMC4wMDAwJjIyJjEmJDEwMDAmMTUzMC4wfDIwMi4wMCQtMjQgMy0wNTA6MDkyMDox", "priceVersion": "SH-PRICEVERSION_MTE1MjMzXzYyNThfMV8xODhfOTRfOTRfMTI5LjAwXzE1OS4wXzEwMC4wXzBfMF8wLjBfMC4wXzE1LjAwXzIwLjAwXzAuMDBfMC4wMF8yNDQxMjE3", "vendorVehicleCode": "20656301", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 2441217, "pLev": 48992, "rLev": 48992, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 65.0, "amountStr": "¥65", "detail": [{"code": "1001", "name": "租车费", "amount": 94, "amountDesc": "¥94"}, {"code": "3765", "name": "新能源特惠", "amount": 29, "amountDesc": "¥29"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 15.0, "amountStr": "¥15", "detail": [{"code": "1002", "name": "基础服务费", "amount": 15.0, "amountDesc": "¥15", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 100.0, "amountStr": "¥100", "subAmount": 129.0, "subAmountStr": "¥129", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4057", "vehicleCode": "0", "packageType": 0, "skuId": 1925835}}], "reactId": "2010104113", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4949_0_115233_115233"]}, "minTPrice": 100.0, "minDPrice": 65.0, "modifySameVehicle": false, "minDOrinPrice": 94, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "新能源特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3765", "groupCode": "MarketGroup1358", "amountTitle": "已减29", "groupId": 1, "mergeId": 0}, "priceSize": 2, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5453", "sortNum": 26, "lowestPrice": 31.0, "highestPrice": 409.0, "maximumRating": 5.0, "maximumCommentCount": 747, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3977", "vendorCode": "62408", "pStoreCode": "106944", "rStoreCode": "106944", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTA2OTQ0XzU0NTNfMV8xMjhfNDVfNDVfNzYuMDBfMTE0LjBfNjIuMF8wXzBfMC4wXzAuMF8xMS4wMF8yMC4wMF8wLjAwXzAuMDBfMTg2Mjk4Mg==", "vendorVehicleCode": "20090439", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1862982, "pLev": 24630, "rLev": 24630, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 31.0, "amountStr": "¥31", "detail": [{"code": "1001", "name": "租车费", "amount": 45, "amountDesc": "¥45"}, {"code": "3765", "name": "新能源特惠", "amount": 14, "amountDesc": "¥14"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 11.0, "amountStr": "¥11", "detail": [{"code": "1002", "name": "基础服务费", "amount": 11.0, "amountDesc": "¥11", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 62.0, "amountStr": "¥62", "subAmount": 76.0, "subAmountStr": "¥76", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4919", "vehicleCode": "0", "packageType": 1, "skuId": 2346161}}], "reactId": "2010104114", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3977_0_106944_106944"]}, "minTPrice": 62.0, "minDPrice": 31.0, "modifySameVehicle": false, "minDOrinPrice": 45, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "新能源特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3765", "groupCode": "MarketGroup1358", "amountTitle": "已减14", "groupId": 1, "mergeId": 0}, "priceSize": 2, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选/店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}], "dailyPrice": 20.0, "hasResult": true}, {"groupCode": "2", "groupName": "经济轿车", "sortNum": 0, "dailyPrice": 20.0, "hasResult": true}, {"groupCode": "newenergy", "groupName": "新能源", "sortNum": 2, "dailyPrice": 17.0, "hasResult": true}, {"groupCode": "3", "groupName": "舒适轿车", "sortNum": 3, "dailyPrice": 47.0, "hasResult": true}, {"groupCode": "6", "groupName": "SUV", "sortNum": 4, "dailyPrice": 31.0, "hasResult": true}, {"groupCode": "4", "groupName": "商务车", "sortNum": 5, "dailyPrice": 39.0, "hasResult": true}, {"groupCode": "5", "groupName": "豪华轿车", "sortNum": 6, "dailyPrice": 79.0, "hasResult": true}, {"groupCode": "9", "groupName": "跑车", "sortNum": 7, "dailyPrice": 88.0, "hasResult": true}, {"groupCode": "11", "groupName": "房车", "sortNum": 8, "dailyPrice": 199.0, "hasResult": true}, {"groupCode": "7", "groupName": "小巴士", "sortNum": 9, "dailyPrice": 202.0, "hasResult": true}, {"groupCode": "10", "groupName": "皮卡", "sortNum": 10, "dailyPrice": 49.0, "hasResult": true}], "productGroupsHashCode": "95A7L7P2G9i175U6IQE0", "storeList": [{"storeCode": "106944", "pickUpLevel": 24630, "pickOffLevel": 24630}, {"storeCode": "107474", "pickUpLevel": 26867, "pickOffLevel": 26867}, {"storeCode": "115265", "pickUpLevel": 49817, "pickOffLevel": 49817}, {"storeCode": "116092", "pickUpLevel": 56249, "pickOffLevel": 56249}, {"storeCode": "115233", "pickUpLevel": 48992, "pickOffLevel": 48992}, {"storeCode": "116538", "pickUpLevel": 52587, "pickOffLevel": 52587}, {"storeCode": "116135", "pickUpLevel": 56985, "pickOffLevel": 56985}, {"storeCode": "114768", "pickUpLevel": 45746, "pickOffLevel": 45746}, {"storeCode": "106032", "pickUpLevel": 16851, "pickOffLevel": 16851}, {"storeCode": "107102", "pickUpLevel": 23028, "pickOffLevel": 23028}], "commNotices": [], "rentCenter": {"id": 2, "name": "三亚凤凰机场店", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.308537", "lng": "109.413536", "filterCode": "Vendor_0", "fromTime": "00:00", "toTime": "23:59"}, "rRentCenter": {"id": 2, "name": "三亚凤凰机场店", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.308537", "lng": "109.413536", "filterCode": "Vendor_0", "fromTime": "00:00", "toTime": "23:59"}, "promptInfos": [], "basicData": {"sortItems": [{"title": "推荐排序", "type": 1, "code": "1", "sortNum": 1}, {"title": "总价 低→高", "type": 2, "code": "2", "sortNum": 2}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "isAll": false, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isLastPage": false, "hasResultWithoutFilter": true, "isFromSearch": false, "productGroupCodeUesd": "all", "uniqSign": "120011113101347313935XRc087E90722HUC7rH3", "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "pHub": 1, "rHub": 1, "promotMap": {}, "extras": {"abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B,230403_DSJT_cxtjy|B", "isNewLicensePlate": "0", "serverRequestId": "07535LS0663T67ACfr85", "commodityClass2Version": "1"}, "isRecommend": false, "isKlbData": true}