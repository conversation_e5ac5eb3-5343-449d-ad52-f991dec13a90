{"baseResponse": {"extMap": {}, "cost": 42, "hasResult": true, "extraIndexTags": {"pcName": "中国", "rcId": "1", "rcName": "中国", "rCityName": "阿尔山", "rCityId": "1658", "pCityId": "1658", "pcId": "1", "pCityName": "阿尔山"}, "code": "200", "isSuccess": true, "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "f4d6c266-95e7-4d19-98c6-8bc50077544d", "errorCode": "0"}, "rHub": 0, "__saveCacheTimestamp": **********.210738, "isKlbData": true, "allVendorPriceCount": 13, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": false, "extras": {"packageLevelAB": "", "abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B", "isLicensePlateHideShow": "0", "serverRequestId": "t5Zl46M6Y3N4r50612l1", "packageLevelSwitch": "0", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "0"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"sortNum": 1, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "CarAge", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "8", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 2, "positionCode": "8", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}], "groupCode": "CarAge", "shortName": "车龄", "bitwiseType": 2, "name": "车龄"}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 5, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "13", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"filterItems": [{"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "10", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}], "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "shortName": "排挡", "groupCode": "Transmission"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "NewEnergy", "itemCode": "NewEnergy_mix", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "新能源混动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "9", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 3, "groupCode": "VehicleAccessory", "quickSortNum": 7, "positionCode": "9", "itemCode": "VehicleAccessory_radar", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "倒车雷达"}, {"sortNum": 4, "groupCode": "VehicleAccessory", "quickSortNum": 8, "positionCode": "9", "itemCode": "VehicleAccessory_tachograph", "binaryDigit": 64, "isQuickItem": true, "bitwiseType": 1, "name": "行车记录仪"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_别克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 6, "groupCode": "HotBrand", "itemCode": "HotBrand_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_别克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_比亚迪", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "比亚迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}], "groupCode": "BrandGroup_f0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽传祺", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "广汽传祺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}], "groupCode": "BrandGroup_g0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷途", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "捷途", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jietu.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_三菱", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "三菱", "icon": "//pages.c-ctrip.com/carisd/brandlogo/sanling.png"}], "groupCode": "BrandGroup_s0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪佛兰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "雪佛兰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"filterItems": [{"code": "100-200", "sortNum": 3, "name": "¥200以下", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-99999", "sortNum": 4, "name": "¥200以上", "groupCode": "Price", "itemCode": "Price_200-99999"}], "sortNum": 1, "name": "价格", "shortName": "价格", "groupCode": "Price"}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 3, "groupCode": "PickReturn", "itemCode": "PickReturn_CostPickupOnDoor", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "收费送车上门"}, {"sortNum": 5, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupSelf", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "自行到店取车"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "4", "itemCode": "StoreService_easyLife", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 1, "name": "无忧租"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 4, "positionCode": "4", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "信用免押"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 6, "groupCode": "StoreService", "quickSortNum": 2, "positionCode": "4", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "SelfService", "itemCode": "SelfService_UnSupport", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "非自助取还"}], "groupCode": "SelfService", "shortName": "自助取还", "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_8", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "台胞证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"filterItems": [{"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "12", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}], "sortNum": 8, "bitwiseType": 2, "name": "门店评分", "shortName": "门店评分", "groupCode": "Comment"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_45921", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "鑫宇租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_80093", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "逸优租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_15001678", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "闪租租车"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "53o3RN52AuC5C08Es444", "timeInterval": 0.049072265625, "productGroups": [{"sortNum": -4, "groupName": "全部车型", "hasResult": true, "groupCode": "all", "dailyPrice": 240}, {"sortNum": 1, "groupName": "经济轿车", "hasResult": true, "groupCode": "2", "dailyPrice": 240, "groupAction": 0}, {"sortNum": 3, "groupName": "新能源", "hasResult": true, "groupCode": "newenergy", "dailyPrice": 129, "groupAction": 0}, {"sortNum": 4, "groupName": "舒适轿车", "hasResult": true, "groupCode": "3", "dailyPrice": 334, "groupAction": 0}, {"sortNum": 5, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 394, "groupAction": 0}, {"sortNum": 6, "productList": [{"groupSort": 1, "lowestPrice": 253, "modifySameVehicle": false, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"vehicleCode": "0", "rStoreCode": "796916", "packageId": "", "pLev": 1445774, "comPriceCode": "[c]", "bizVendorCode": "SD11977", "pStoreCode": "796916", "packageType": 1, "priceVersion": "SH-PRICEVERSION_Nzk2OTE2XzE3MzkxXzFfMzg4XzI1M18yNTNfMzI5OS4wMF8zODhfMzI5OS4wXzFfMV8wLjBfMC4wXzI2LjAwXzIwLjAwXzE1MDAuMDBfMTUwMC4wMF80NTk4ODY4NQ==", "sendTypeForPickUpCar": 0, "skuId": 45988685, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1445774, "vendorCode": "15001678", "vendorVehicleCode": "45988685"}, "fees": [{"code": "CAR_RENTAL_FEE", "amount": 253, "amountStr": "¥253", "detail": [{"code": "1001", "amount": 253, "amountDesc": "¥253", "name": "租车费"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 3020, "amountStr": "¥3020", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11026", "amount": 1500, "amountDesc": "¥1500", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 1500, "amountDesc": "¥1500", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 26, "amountStr": "¥26", "detail": [{"code": "1002", "amount": 26, "amountDesc": "¥26", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 3299, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥3299", "subAmount": 3299, "name": "总价", "amountStr": "¥3299"}]}], "rCoup": 0, "vehicleCode": "17391", "highestPrice": 253, "pWay": "", "minDPrice": 253, "vehicleKey": "0_17391_", "hot": 0, "minTPrice": 3299, "lowestDistance": 324.8586, "group": 870, "type": 0, "sortNum": 5, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11977_0_796916_796916"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 90, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1}, {"groupSort": 1, "lowestPrice": 799, "modifySameVehicle": false, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"vehicleCode": "0", "rStoreCode": "114876", "packageId": "", "pLev": -1, "comPriceCode": "[c]Nzg2NDU2NTkwfDIwfDAuMDktMTQyNC0wMDA6MCAwMDo5Jjc5MCY3OTUwMDA5JjAuc2UkfCZmYWwmMSY3MTAwMTk5JDE5OSY3MSYzMDAwMyYzMC4wLjAwJjAyJjEwJDEwMDAmNSY1MC4kfDIwMC4wMDktMTQyNC0wMDA6MCAxODoyNC0wMCYyMCAxODo5LTE0MHwyMDMwOjA5LTE0MjQtMDQ2OjMgMTc6AAAAADMAAAA=", "bizVendorCode": "SD4826", "pStoreCode": "114876", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTE0ODc2XzQ2NjBfMV83OTlfNzk5Xzc5OV84NzkuMDBfNzk5Xzg3OS4wXzBfMF8wLjBfMC4wXzUwLjAwXzMwLjAwXzBfMC4wMF81NjU5Nzg2NA==", "sendTypeForPickUpCar": 0, "skuId": 56597864, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 47876, "vendorCode": "80093", "vendorVehicleCode": "56597864"}, "fees": [{"code": "CAR_RENTAL_FEE", "amount": 799, "amountStr": "¥799", "detail": [{"code": "1001", "amount": 799, "amountDesc": "¥799", "name": "租车费"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 879, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥879", "subAmount": 879, "name": "总价", "amountStr": "¥879"}]}], "rCoup": 0, "vehicleCode": "4660", "highestPrice": 799, "pWay": "", "minDPrice": 799, "vehicleKey": "0_4660_", "hot": 0, "minTPrice": 879, "lowestDistance": 1.505, "group": 820, "type": 0, "sortNum": 6, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD4826_0_114876_114876"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 174, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1}, {"groupSort": 0, "lowestPrice": 233, "modifySameVehicle": false, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"vehicleCode": "0", "rStoreCode": "796916", "packageId": "", "pLev": 1445774, "comPriceCode": "[c]", "bizVendorCode": "SD11977", "pStoreCode": "796916", "packageType": 1, "priceVersion": "SH-PRICEVERSION_Nzk2OTE2XzQwODhfMV8zNThfMjMzXzIzM18zMjk0LjAwXzM1OF8zMjk0LjBfMV8xXzAuMF8wLjBfMjYuMDBfMzUuMDBfMTUwMC4wMF8xNTAwLjAwXzQ1OTg5NTUz", "sendTypeForPickUpCar": 0, "skuId": 45989553, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1445774, "vendorCode": "15001678", "vendorVehicleCode": "45989553"}, "fees": [{"code": "CAR_RENTAL_FEE", "amount": 233, "amountStr": "¥233", "detail": [{"code": "1001", "amount": 233, "amountDesc": "¥233", "name": "租车费"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 3035, "amountStr": "¥3035", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11026", "amount": 1500, "amountDesc": "¥1500", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 1500, "amountDesc": "¥1500", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 26, "amountStr": "¥26", "detail": [{"code": "1002", "amount": 26, "amountDesc": "¥26", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 3294, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥3294", "subAmount": 3294, "name": "总价", "amountStr": "¥3294"}]}], "rCoup": 0, "vehicleCode": "4088", "highestPrice": 233, "pWay": "", "minDPrice": 233, "vehicleKey": "0_4088_", "hot": 0, "minTPrice": 3294, "lowestDistance": 324.8586, "group": 0, "type": 0, "sortNum": 10, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11977_0_796916_796916"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": true, "isCredit": true, "maximumCommentCount": 90, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1}], "hasResult": true, "groupCode": "4", "groupAction": 0, "groupName": "商务车", "dailyPrice": 799}], "resBodySize": 27924, "labelCodes": ["3563", "3495", "3504", "3503", "3547", "3501", "3709", "3510", "3696", "3731", "3509", "3788", "3810", "3789", "3679", "3746"], "quickFilter": [{"sortNum": 6, "groupCode": "StoreService", "quickSortNum": 2, "positionCode": "4", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "8", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "9", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "10", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "12", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 5, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "13", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}], "__cachedTime": 90.75411605834961, "promotMap": {}, "requestInfo": {"rLongitude": 119.947666, "rDate": "20240914183000", "age": 30, "pCityId": 1658, "returnDate": "/Date(1726309800000+0800)/", "sourceCountryId": 1, "pLatitude": 47.172609, "rLatitude": 47.172609, "pLongitude": 119.947666, "pDate": "20240914180000", "rCityId": 1658, "pickupLocationName": "阿尔山站", "returnLocationName": "阿尔山站", "pickupDate": "/Date(1726308000000+0800)/"}, "allVehicleCount": 13, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "4", "rentCenter": {"filterCode": "Vendor_0"}, "__isFromCache": true, "checkResponseTime": 1726307320964.8562, "vehicleList": [{"vehicleKey": "0_17391_", "transmissionName": "自动挡", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV0m12000c563xuyA0D0.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=8202&app_ver=10.5", "carPhone": true, "vehicleCode": "17391", "style": "2023款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3e12000b76wz3d998E.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6c12000c6e72r9C1EE.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1y12000c6e73t52597.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3912000c6e75un16B1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6112000c6e780q9AF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3y12000c6e71tgC9D9.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "48", "skylight": "不支持", "guidSys": "部分车辆支持定速巡航"}, {"vehicleKey": "0_4660_", "transmissionName": "自动挡", "luggageNo": 5, "carPlay": "支持CarPlay", "displacement": "2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV1812000etgoh7fCF12.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=675&app_ver=10.5", "carPhone": true, "vehicleCode": "4660", "style": "2017/18款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1812000etgoh7fCF12.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5312000c6e6jqd2785.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4812000c6e6jqt5FEA.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1712000c6e6j7m794E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5m12000c6e6q8z4687.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6612000c6e6hg40B70.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "48", "skylight": "支持", "guidSys": "支持定速巡航/自适应巡航"}, {"vehicleKey": "0_4088_", "transmissionName": "自动挡", "luggageNo": 1, "carPlay": "部分车辆支持CarLife", "displacement": "2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4h12000etghea704DF.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "传祺M8（21-23款）", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=430&app_ver=10.5", "vehicleCode": "4088", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "传祺M8（21-23款）", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4h12000etghea704DF.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放1个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6w12000c5k87luE916.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3e12000c5k85n5D01B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5b12000c5k813d59ED.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5p12000c5k88sq7E71.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0t12000c5k86bpB09C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "广汽传祺", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "广汽传祺", "licenseStyle": "2", "vehiclesSetId": "48", "skylight": "部分车辆支持", "guidSys": "部分车辆支持定速巡航/全速自适应巡航"}], "storeList": [{"pickUpLevel": 1445774, "pickOffLevel": 1445774, "storeCode": "796916"}, {"pickUpLevel": -1, "pickOffLevel": 47876, "storeCode": "114876"}], "promptInfos": [], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": true, "isLastPage": true, "uniqSign": "120011398903214795363356v56c02r8346v411C", "pHub": 0, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"sortNum": 1, "showLayer": 1, "title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "subTitle": "*国定节假日以页面披露为准"}, {"sortNum": 2, "showLayer": 0, "title": "安心保障", "titleExtra": "(需加购尊享服务)", "type": 1, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "subTitle": "*覆盖损失范围以预订页面内披露为准"}, {"sortNum": 10, "title": "满油取车", "subTitle": "", "type": 0, "description": "保障取车时油量满", "showLayer": 0}, {"sortNum": 99, "title": "免加油服务费/充电服务费", "subTitle": "", "type": 0, "description": "还车时，若油量/电量少于取车油量/电量，无需支付加油服务费/充电服务费，所产生的油量/电量差价仍需支付"}]}, "checkRequestTime": 1726307320964.8071, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "1423430907992434030", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a7393e8-479529-2202097", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(**********339+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": true, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&listExtraMaps[packageLevelVersion]=1&listExtraMaps[nonJumpFlow]=0&modify=&orderId=&pickupPointInfo=2024-09-14 18:00:00|阿尔山站|1658|47.172609|119.947666|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=4&productGroupCodeFirst=&queryListCacheId=2cbb7fa8-ae20-4b2c-a39e-5344352bbd7e&returnPointInfo=2024-09-14 18:30:00|阿尔山站|1658|47.172609|119.947666|||&sortType=1&uid=@@PAGENUM@@1", "groupId": "18631/queryProducts?batch=", "networkCost": 16, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 3, "setCacheCost": 0, "cacheFrom": "native", "beforeFetch": 1726307320964, "afterFetch": 1726307320980, "hasRetry": false, "loadDiffCost": 13, "originNetworkCost": 3}}