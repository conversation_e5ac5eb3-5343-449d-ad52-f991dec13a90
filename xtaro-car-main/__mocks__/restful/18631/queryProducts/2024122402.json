{"baseResponse": {"extMap": {}, "cost": 1146, "hasResult": true, "extraIndexTags": {"pcName": "中国", "rcId": "1", "rcName": "中国", "rCityName": "三亚", "rCityId": "43", "pCityId": "43", "pcId": "1", "pCityName": "三亚"}, "code": "200", "errorCode": "0", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "a42df119-35ac-4f43-ac50-37679494c0e1", "isSuccess": true}, "rHub": 1, "isKlbData": true, "allVendorPriceCount": 4237, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": true, "rRentCenter": {"images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "id": 2, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "23:59", "lat": "18.308537", "lng": "109.413536", "name": "三亚凤凰机场店", "fromTime": "00:00"}, "extras": {"packageLevelAB": "B", "serverRequestId": "M01756WT408j2W4n869V", "isLicensePlateHideShow": "0", "abVersion": "241008_DSJT_ykjpx|B,230104_DSJT_fil10|B,241128_DSJT_lqd|A,220323_DSJT_rank2|B,220624_DSJT_spfj1|B", "packageLevelSwitch": "1", "selfServiceSwitch": "1", "isNewLicensePlate": "0", "commodityClass2Version": "1", "prepProductGroupTopSwitch": "0"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"sortNum": 1, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "CarAge", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "6个月内车龄"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "8", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "1年内车龄"}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 2, "positionCode": "8", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "2年内车龄"}], "groupCode": "CarAge", "shortName": "车龄", "bitwiseType": 2, "name": "车龄"}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 2, "groupCode": "SeatGroup", "itemCode": "SeatGroup_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 3, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 4, "groupCode": "SeatGroup", "itemCode": "SeatGroup_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 5, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "13", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 6, "groupCode": "SeatGroup", "itemCode": "SeatGroup_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "10", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "手动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "NewEnergy", "itemCode": "NewEnergy_elect", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}, {"sortNum": 2, "groupCode": "NewEnergy", "itemCode": "NewEnergy_mix", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "新能源混动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "9", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 3, "groupCode": "VehicleAccessory", "quickSortNum": 7, "positionCode": "9", "itemCode": "VehicleAccessory_radar", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "倒车雷达"}, {"sortNum": 4, "groupCode": "VehicleAccessory", "quickSortNum": 8, "positionCode": "9", "itemCode": "VehicleAccessory_tachograph", "binaryDigit": 64, "isQuickItem": true, "bitwiseType": 1, "name": "行车记录仪"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}, {"sortNum": 7, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Refrigerator", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 1, "name": "车载冰箱"}, {"sortNum": 9, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Childseat", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 1, "name": "儿童座椅"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "HotBrand", "itemCode": "HotBrand_奥迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_别克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_奔驰", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_宝马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotB<PERSON>_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 6, "groupCode": "HotBrand", "itemCode": "HotBrand_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 10, "groupCode": "HotBrand", "itemCode": "HotBrand_Jeep", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 12, "groupCode": "HotBrand", "itemCode": "HotBrand_路虎", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 18, "groupCode": "HotBrand", "itemCode": "HotBrand_日产", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_AITO", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "AITO"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_埃安", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "埃安", "icon": "https://dimg04.c-ctrip.com/images/0R44f120009gwvd13842B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_奥迪", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_阿斯顿·马丁", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "阿斯顿·马丁", "icon": "https://dimg04.c-ctrip.com/images/0R419120009gwx9cqBA77.png"}], "groupCode": "BrandGroup_a0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_保时捷", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "保时捷", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_别克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_北京", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "北京", "icon": "//pages.c-ctrip.com/carisd/brandlogo/beijin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔腾", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "奔腾", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benteng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔驰", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝马", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝骏", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "宝骏", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baojun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宾利", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "宾利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/binli.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_本田", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_标致", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "标致", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biaozhi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_比亚迪", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "比亚迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "长安", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安启源", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "长安启源", "icon": "https://dimg04.c-ctrip.com/images/0yc4c12000h4rulocBB7B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安欧尚", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "长安欧尚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changanoushang.png"}], "groupCode": "BrandGroup_c0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风EV新能源", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "东风EV新能源", "icon": "https://dimg04.c-ctrip.com/images/0yc0612000h8yfr3s483E.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风风行", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "东风风行", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengxin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_德宝", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "德宝", "icon": "https://dimg04.c-ctrip.com/images/0yc3x12000h8a806969EB.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_戴纳肯", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "戴纳肯", "icon": "https://dimg04.c-ctrip.com/images/0yc3x12000h8a806969EB.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_道奇", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "道奇", "icon": "//pages.c-ctrip.com/carisd/brandlogo/daoqi.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_法拉利", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "法拉利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_福特", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "福特", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}], "groupCode": "BrandGroup_f0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽传祺", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "广汽传祺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_高合汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "高合汽车", "icon": "https://dimg04.c-ctrip.com/images/0yc6a12000aom31vy37E4.png"}], "groupCode": "BrandGroup_g0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_华晨新日", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "华晨新日", "icon": "https://dimg04.c-ctrip.com/images/0R472120009gwv9rxB7E1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_合创", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "合创", "icon": "https://dimg04.c-ctrip.com/images/0yc0n12000h1t8sa795EB.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_哈弗", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "哈弗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_海马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "海马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_红旗", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "红旗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_鸿蒙智行", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "鸿蒙智行", "icon": "https://dimg04.c-ctrip.com/images/0yc1b12000fb61ba5DB17.png"}], "groupCode": "BrandGroup_h0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_i0", "itemCode": "BrandGroup_i0_iCAR", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "iCAR", "icon": "https://dimg04.c-ctrip.com/images/0yc2712000h4doexu1B83.png"}], "groupCode": "BrandGroup_i0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_Jeep", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_几何汽车", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "几何汽车", "icon": "https://dimg04.c-ctrip.com/images/0R43t120009gwv73p44FD.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_吉利汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "吉利汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_吉利银河", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "吉利银河", "icon": "https://dimg04.c-ctrip.com/images/0yc6q12000h4ytcpuE903.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷豹", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "捷豹", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiebao.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷达", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "捷达", "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷途", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "捷途", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jietu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_极氪", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "极氪", "icon": "https://dimg04.c-ctrip.com/images/0yc0x12000aom51we094D.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_k0", "itemCode": "BrandGroup_k0_凯迪拉克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "凯迪拉克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "groupCode": "BrandGroup_k0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_兰博基尼", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "兰博基尼", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_劳斯莱斯", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "劳斯莱斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_岚图汽车", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "岚图汽车", "icon": "https://dimg04.c-ctrip.com/images/0R45a120009gwvmb34A33.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_林肯", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "林肯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/linken.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_理想汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "理想汽车", "icon": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc6i1200000ieok0AE70.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_路特斯", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "路特斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lutesi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_路虎", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_零跑汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "零跑汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingpaoqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_雷克萨斯", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "雷克萨斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_领克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "领克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingke.png"}], "groupCode": "BrandGroup_l0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_MINI", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "MINI", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_名爵", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "名爵", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mingjue.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_玛莎拉蒂", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "玛莎拉蒂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_迈凯伦", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "迈凯伦", "icon": "//pages.c-ctrip.com/carisd/brandlogo/maikailun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_马自达", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "马自达", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mazida.png"}], "groupCode": "BrandGroup_m0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_n0", "itemCode": "BrandGroup_n0_哪吒汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "哪吒汽车", "icon": "https://dimg04.c-ctrip.com/images/0R46n120009gwvgrmC0A4.png"}], "groupCode": "BrandGroup_n0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_o0", "itemCode": "BrandGroup_o0_欧拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "欧拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/oula.png"}], "groupCode": "BrandGroup_o0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_p0", "itemCode": "BrandGroup_p0_Polestar极星", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "Polestar极星", "icon": "https://dimg04.c-ctrip.com/images/0R43g120009gwuo9r0192.png"}], "groupCode": "BrandGroup_p0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_启辰", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "启辰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qicheng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_奇瑞", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奇瑞", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qirui.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_起亚", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "起亚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "groupCode": "BrandGroup_q0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_日产", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_荣威", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "荣威", "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "groupCode": "BrandGroup_r0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_SONGSAN MOTORS", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "SONGSAN MOTORS", "icon": "https://dimg04.c-ctrip.com/images/0yc2i12000h90hqs2C453.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_smart", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "smart", "icon": "//pages.c-ctrip.com/carisd/brandlogo/smart.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_上汽大通MAXUS", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "上汽大通MAXUS", "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_赛麟", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "赛麟", "icon": "//pages.c-ctrip.com/carisd/brandlogo/sailin.png"}], "groupCode": "BrandGroup_s0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_坦克", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "坦克", "icon": "https://dimg04.c-ctrip.com/images/0yc3612000aoma5tvACF1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_特斯拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "特斯拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_腾势", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "腾势", "icon": "//pages.c-ctrip.com/carisd/brandlogo/shiteng.png"}], "groupCode": "BrandGroup_t0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_五菱汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "五菱汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/wulingqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_威马汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "威马汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weimaqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_沃尔沃", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "沃尔沃", "icon": "//pages.c-ctrip.com/carisd/brandlogo/woerwo.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_蔚来", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "蔚来", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weilai.png"}], "groupCode": "BrandGroup_w0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小米汽车", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "小米汽车", "icon": "https://dimg04.c-ctrip.com/images/0yc1512000h1vkihnBA22.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小鹏", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "小鹏", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiaopengqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_现代", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "现代", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪佛兰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "雪佛兰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪铁龙", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "雪铁龙", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuetelong.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_AITO问界", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "AITO问界"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_智己汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "智己汽车", "icon": "https://dimg04.c-ctrip.com/images/0yc3k12000h8zmxn19B00.png"}], "groupCode": "BrandGroup_z0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "50-100", "sortNum": 2, "name": "¥100以下", "groupCode": "Price", "itemCode": "Price_50-100"}, {"code": "100-200", "sortNum": 3, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-99999", "sortNum": 4, "name": "¥200以上", "groupCode": "Price", "itemCode": "Price_200-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "PickReturn", "itemCode": "PickReturn_AirportPR", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "机场内门店取车"}, {"sortNum": 2, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupInAirport", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "送车至机场"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 6, "groupCode": "StoreService", "quickSortNum": 2, "positionCode": "4", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SelfService", "quickSortNum": 1, "positionCode": "3", "itemCode": "SelfService_Support", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 2, "groupCode": "SelfService", "itemCode": "SelfService_UnSupport", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "非自助取还"}], "groupCode": "SelfService", "shortName": "自助取还", "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "selectedIcon": "https://dimg04.c-ctrip.com/images/0412v12000as15opq2E7C.png", "groupCode": "Promotion", "quickSortNum": 1, "positionCode": "2", "itemCode": "Promotion_3743", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "周三福利日", "icon": "https://dimg04.c-ctrip.com/images/0410v12000as15e7y0B07.png"}], "groupCode": "Promotion", "shortName": "优惠活动", "bitwiseType": 2, "name": "优惠活动"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_8", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "台胞证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "门店评分", "sortNum": 8, "bitwiseType": 2, "name": "门店评分", "groupCode": "Comment", "filterItems": [{"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "12", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 2, "groupCode": "Comment", "itemCode": "Comment_4.5", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4.5分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}]}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "1", "itemCode": "Vendor_0", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "携程租车中心"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "mark": "hot", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "positionCode": "6", "name": "携程优选", "icon": ""}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13088", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "一嗨租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13031", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "桐叶租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13032", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "明昊租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13033", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "枫叶租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13082", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "凯美租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13092", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "易代步租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13094", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "骑仕租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13115", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "港梦超跑俱乐部租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13119", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "懒人行租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30004", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "铭轩租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30234", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "金晟租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30466", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "祥成租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30912", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "卢米租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31092", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "树德出行"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32231", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "泰信吉租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_38236", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "天驹租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_46492", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "漫自由租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_47522", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "祥驰租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_53893", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "三亚世纪联合租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_57671", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "捷安利达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_58487", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "丰田海南出行"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61365", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "普信租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61831", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "金达莱租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61924", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "龙麟租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61937", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "锋达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62072", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "一路平安租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62115", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "三亚启航租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62267", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "琼驰租车"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000088", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "盛泽租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000258", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "三亚五二零租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000269", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "海立达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000295", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "途新租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000361", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "海南点赞租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_15000372", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "田世租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62863", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "文东租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63836", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "租租侠租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_66708", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "旭辰租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_68692", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "利资租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_69279", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "安米租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_71599", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "日之星丰田租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_72983", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "鹏威租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_74373", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "海南中进租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_76665", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "彩车坊租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_78571", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "汪澜租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_78579", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "优享旅途租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80127", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "三亚新概念租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80431", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "小飞侠租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80535", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "汇驰租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80545", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "盛兴隆租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80751", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "联动出行"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80771", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "豫海租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_81525", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "钰鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_81889", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "八骏马租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82105", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "三亚旅途中租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82819", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "助旅租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82843", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "名仕租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82909", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "五行租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_83386", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "虫子邦租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_83528", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "钧通租车"}], "groupCode": "Vendor_1", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15000935", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "众横租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15000981", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "轩宇租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001073", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "海途租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001137", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "毛蛋租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001163", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "欣博祥租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001179", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "定格租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001186", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "北蒙租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001194", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "壹优租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001199", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "逍遥租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001308", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "纳贝拉租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001351", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "海南松舍租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001364", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "方达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001440", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "皓轩跑车俱乐部租车 "}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001454", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "邦尼租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001460", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "黑娃租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001470", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "宇航租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001630", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "铭鸿租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001793", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "皓阳租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001824", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "三亚鼎豪租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001847", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "东星租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001863", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "琼城租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001908", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "小龙人租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15001913", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "木沐租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15002319", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "东辉租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15002466", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "鲁运昌通出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15002585", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "海南顺强租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15002723", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "清晨租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15003234", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "三亚融泽租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15003362", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "领客出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15003454", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "牛牛出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_15003709", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "铭途租车"}], "groupCode": "Vendor_2", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15003741", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "赛富德租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15003785", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "友嘉租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15003850", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "通耀租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15003910", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "海南金域租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15003922", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "大麦超跑俱乐部"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15003935", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "盛京出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004136", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "礼享智行租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004152", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "陇鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004163", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "EVCARD租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004317", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "三亚奇迹租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004351", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "耀东方租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004385", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "银天租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004427", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "博腾出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004512", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "阿纳迪跑车俱乐部出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004592", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "瀚程租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004609", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "海南蚂蚁出游租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004617", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "怪兽超跑出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004673", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "顺椰出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004703", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "海南大脸猫租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004707", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "驰鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004773", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "六六出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15004914", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "德普租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15005408", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "桐叶租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15005425", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "西十出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15005627", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "小简出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15005693", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "北方出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15005781", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "安迪出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15005851", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "凯福华租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15006333", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "无忧九州租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15006533", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "车游天下租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15006884", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "宝嘉租车"}], "groupCode": "Vendor_3", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15006932", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "马达租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15006983", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "乐享好车出行"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15006996", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "诚亚租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15006998", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "圣唯安租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15007295", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "三亚智衡租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15007296", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "鸿韵出行"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15007315", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "鑫路达出行"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15007420", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "三亚铭盛租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15007439", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "资傲租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15007865", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "海南窝德租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15007870", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "锐冠租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15007893", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "爱信出行"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15008054", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "摇钱树出行"}], "groupCode": "Vendor_4", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "UUc528Ni1hu1n9bpE183", "timeInterval": 1212.46533203125, "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 77, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 139, "detail": [{"code": "1001", "amount": 159, "amountDesc": "¥159", "name": "租车费"}, {"code": "3743", "amount": 20, "amountDesc": "¥20", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥78", "originalDailyPrice": 88, "subAmount": 78, "name": "车辆租金", "amountStr": "¥139"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 54, "amountStr": "¥54", "detail": [{"code": "1002", "amount": 54, "amountDesc": "¥54", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 213, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥233", "subAmount": 233, "name": "总价", "amountStr": "¥213"}], "reference": {"vehicleCode": "0", "rStoreCode": "183936", "packageId": "sec", "pLev": 141409, "comPriceCode": "[c]MDQ0fDY5MDF8MjAyMC4wMC0yMiA1LTAxMDowMDAwOjAmMSZmJjg4JiY4OCZhbHNlNS0wMSQyMDIwMDowLTIzICY4OCYwOjAwMy4wMDcxJjJhbHNlMDAmZjcxJHwmODgmJjImODEwMDE5JDEwOCYxNSYyMC4wMyYxMC4wMDAwJjIyJjImJDEwMDAmNTQzMC4wfDIwMi4wMCQtMjIgNS0wMTA6MDAxOTozNS0wMSYyMDIxODozLTI0IHwyMDIwOjAwLTE1IDUtMDE4OjQ4MTk6Mg==", "bizVendorCode": "SD3926", "pStoreCode": "183936", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgzOTM2XzQxMzlfMV84OF8xNTlfODhfMjMzLjAwXzc4LjBfMjEzLjBfMF8wXzAuMF8wLjBfNTQuMDBfMjAuMDBfMC4wMF8wLjAwXzY5MDEwNDQ=", "sendTypeForPickUpCar": 0, "skuId": 6901044, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 141409, "vendorCode": "13082", "vendorVehicleCode": "699_53432_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15006533", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2285298", "skuId": 72913632, "bizVendorCode": "SD7763"}}, {"reference": {"vendorCode": "46492", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106965", "skuId": 28097714, "bizVendorCode": "SD3933"}}, {"reference": {"vendorCode": "15007865", "vehicleCode": "0", "packageType": 0, "pStoreCode": "2364391", "skuId": 76069855, "bizVendorCode": "SD15262"}}, {"reference": {"vendorCode": "74373", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116523", "skuId": 1965861, "bizVendorCode": "SD6573"}}, {"reference": {"vendorCode": "13094", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188084", "skuId": 6900280, "bizVendorCode": "SD3866"}}, {"reference": {"vendorCode": "13031", "vehicleCode": "0", "packageType": 1, "pStoreCode": "78", "skuId": 152539, "bizVendorCode": "SD14450"}}, {"reference": {"vendorCode": "13032", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2961", "skuId": 227977, "bizVendorCode": "SD3012"}}, {"reference": {"vendorCode": "80431", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114886", "skuId": 1913476, "bizVendorCode": "SD4849"}}, {"reference": {"vendorCode": "53893", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107119", "skuId": 1860920, "bizVendorCode": "SD3942"}}, {"reference": {"vendorCode": "13119", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193947", "skuId": 30541211, "bizVendorCode": "SD4201"}}, {"reference": {"vendorCode": "15005425", "vehicleCode": "0", "packageType": 1, "pStoreCode": "797672", "skuId": 46010546, "bizVendorCode": "SD12615"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减20", "groupId": 1}, "vehicleCode": "4139", "highestPrice": 228, "rCoup": 0, "minDPrice": 78, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_4139_", "hot": 0, "minTPrice": 213, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 0, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD7763_0_2285298_2285298"], "introduce": "当前车型最低价"}, "minDOrinPrice": 88, "isEasy": true, "isCredit": true, "maximumCommentCount": 52840, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 12, "minTOrinPrice": 233, "logicIndex": 0, "isGroup": false, "renderUniqId": "4139_12__213_78_88_信用免押_easyLife_周三福利日已减20"}, {"groupSort": 3, "lowestPrice": 192, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 321, "detail": [{"code": "1001", "amount": 365, "amountDesc": "¥365", "name": "租车费"}, {"code": "3743", "amount": 44, "amountDesc": "¥44", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥196", "originalDailyPrice": 218, "subAmount": 196, "name": "车辆租金", "amountStr": "¥321"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 67, "amountStr": "¥67", "detail": [{"code": "1002", "amount": 67, "amountDesc": "¥67", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 408, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥452", "subAmount": 452, "name": "总价", "amountStr": "¥408"}], "reference": {"vehicleCode": "0", "rStoreCode": "1680701", "packageId": "sec", "pLev": 1588861, "comPriceCode": "[c]OTY3OTU5MTQwfDIwfDAuMDEtMjIyNS0wMDA6MCAwMDo4JiYxMCYyMWUmMTUmdHJ1MDI1LTgmJDIzIDAwMDEtMjAwJjI6MDA6NDcmMjE4JjEwMCZ0My4wMDE1OCZydWUmfDEwMDEwNiQyMTgmMSYyJjEwMDMzNjUkMC4wMCYxJjIwMCQxJjIwLjImNDAwMDImNjcuMC4wMCYwMjUtMCR8MjIgMTkwMS0yMDAmMjozMDowMS0yMDI1LTozMDo0IDE4MDI1LTAwfDI1IDE5MDEtMTQ4AAA6Mjg6", "bizVendorCode": "SD14278", "pStoreCode": "1680701", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTY4MDcwMV81MjgzXzFfMjE4XzM2NV8yMThfNDUyLjAwXzE5Ni4wXzQwOC4wXzBfMF8wLjBfMC4wXzY3LjAwXzIwLjAwXzAuMDBfMC4wMF81OTE0OTY3OQ==", "sendTypeForPickUpCar": 0, "skuId": 59149679, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1588861, "vendorCode": "15006996", "vendorVehicleCode": "90268_3912_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 44414147, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15003454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247392", "skuId": 24651460, "bizVendorCode": "SD14476"}}, {"reference": {"vendorCode": "15005693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625283", "skuId": 60938529, "bizVendorCode": "SD12894"}}, {"reference": {"vendorCode": "15001793", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183909", "skuId": 57029582, "bizVendorCode": "SD8766"}}, {"reference": {"vendorCode": "15001194", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175686", "skuId": 4545504, "bizVendorCode": "SD8150"}}, {"reference": {"vendorCode": "74373", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116523", "skuId": 1971758, "bizVendorCode": "SD6573"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 78063322, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15004427", "vehicleCode": "0", "packageType": 1, "pStoreCode": "313573", "skuId": 66213533, "bizVendorCode": "SD11544"}}, {"reference": {"vendorCode": "15003362", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229176", "skuId": 66609131, "bizVendorCode": "SD10401"}}, {"reference": {"vendorCode": "15001364", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181671", "skuId": 54528734, "bizVendorCode": "SD13705"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652881, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901328, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "15004152", "vehicleCode": "0", "packageType": 1, "pStoreCode": "281261", "skuId": 62729577, "bizVendorCode": "SD11257"}}, {"reference": {"vendorCode": "81889", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107104", "skuId": 65943725, "bizVendorCode": "SD4113"}}, {"reference": {"vendorCode": "15006884", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1525903", "skuId": 57196014, "bizVendorCode": "SD14161"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902505, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "30004", "vehicleCode": "0", "packageType": 0, "pStoreCode": "106808", "skuId": 1859332, "bizVendorCode": "SD3705"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25608157, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "81525", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107474", "skuId": 24587536, "bizVendorCode": "SD4102"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 62801656, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "80545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107268", "skuId": 1858109, "bizVendorCode": "SD4092"}}, {"reference": {"vendorCode": "15004385", "vehicleCode": "0", "packageType": 1, "pStoreCode": "312488", "skuId": 65353709, "bizVendorCode": "SD11501"}}, {"reference": {"vendorCode": "15002723", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193183", "skuId": 61060532, "bizVendorCode": "SD9747"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减44", "groupId": 1}, "vehicleCode": "5283", "highestPrice": 1056, "rCoup": 0, "minDPrice": 196, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_5283_", "hot": 0, "minTPrice": 408, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 1, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD8150_0_175686_175686"], "introduce": "当前车型最低价"}, "minDOrinPrice": 218, "isEasy": true, "isCredit": true, "maximumCommentCount": 44882, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 24, "minTOrinPrice": 452, "logicIndex": 1, "isGroup": true, "renderUniqId": "5283_24__408_196_218_信用免押_easyLife_周三福利日已减44"}, {"groupSort": 2, "lowestPrice": 192, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 326, "detail": [{"code": "1001", "amount": 371, "amountDesc": "¥371", "name": "租车费"}, {"code": "3743", "amount": 45, "amountDesc": "¥45", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥196", "originalDailyPrice": 218, "subAmount": 196, "name": "车辆租金", "amountStr": "¥326"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 68, "amountStr": "¥68", "detail": [{"code": "1002", "amount": 68, "amountDesc": "¥68", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 414, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥459", "subAmount": 459, "name": "总价", "amountStr": "¥414"}], "reference": {"vehicleCode": "0", "rStoreCode": "182187", "packageId": "sec", "pLev": 1319396, "comPriceCode": "[c]OTQ1fDY5MDN8MjAyMC4wMC0yMiA1LTAxMDowMDAwOjAmJjEmJjIxOCYxODh0cnVlMjUtMCYkMjAgMDA6MS0yMzAmMjEwMDowMyYyMzgmMTUwJnRyLjAwMDg4JjF1ZSYxMTAwMTMyJHwxOCYzJjImMjAwMyY3MSQxLjAwJjEmMjAwJDEwMjAuMCY0MC4wMiYyOC4wMDAwJjYyNS0wJHwyMCAxOToxLTIyMCYyMDMwOjAxLTI0MjUtMDMwOjAgMTg6MjUtMDB8MjAgMTk6MS0xNTgAAAAyODo0", "bizVendorCode": "SD6835", "pStoreCode": "182187", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgyMTg3XzU1MjRfMV8yMThfMzcxXzIxOF80NTkuMDBfMTk2LjBfNDE0LjBfMF8wXzAuMF8wLjBfNjguMDBfMjAuMDBfMC4wMF8wLjAwXzY5MDM5NDU=", "sendTypeForPickUpCar": 0, "skuId": 6903945, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1319396, "vendorCode": "15001454", "vendorVehicleCode": "2763_49572_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15003454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247392", "skuId": 24651467, "bizVendorCode": "SD14476"}}, {"reference": {"vendorCode": "15005693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625283", "skuId": 60938536, "bizVendorCode": "SD12894"}}, {"reference": {"vendorCode": "15001793", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183909", "skuId": 57029575, "bizVendorCode": "SD8766"}}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 1860337, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "82819", "vehicleCode": "0", "packageType": 0, "pStoreCode": "117430", "skuId": 77304242, "bizVendorCode": "SD6184"}}, {"reference": {"vendorCode": "15002585", "vehicleCode": "0", "packageType": 1, "pStoreCode": "192639", "skuId": 69187322, "bizVendorCode": "SD14949"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 44386742, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 62840051, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15001199", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175820", "skuId": 43673561, "bizVendorCode": "SD8155"}}, {"reference": {"vendorCode": "15003362", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229176", "skuId": 66594739, "bizVendorCode": "SD10401"}}, {"reference": {"vendorCode": "78579", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114931", "skuId": 58528513, "bizVendorCode": "SD4755"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652874, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "83386", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117466", "skuId": 68738251, "bizVendorCode": "SD5613"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901330, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "15004152", "vehicleCode": "0", "packageType": 1, "pStoreCode": "281261", "skuId": 66217152, "bizVendorCode": "SD11257"}}, {"reference": {"vendorCode": "69279", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116480", "skuId": 24653539, "bizVendorCode": "SD6519"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184270", "skuId": 7358156, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902509, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "32231", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107102", "skuId": 76384239, "bizVendorCode": "SD8256"}}, {"reference": {"vendorCode": "81525", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107474", "skuId": 25009720, "bizVendorCode": "SD4102"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25608584, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "15001163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251411", "skuId": 62724012, "bizVendorCode": "SD8119"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 62801768, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "82843", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115278", "skuId": 54883690, "bizVendorCode": "SD5061"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333216, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "15007295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1851872", "skuId": 66149637, "bizVendorCode": "SD14617"}}, {"reference": {"vendorCode": "15000295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133807", "skuId": 2317188, "bizVendorCode": "SD7027"}}, {"reference": {"vendorCode": "15004385", "vehicleCode": "0", "packageType": 1, "pStoreCode": "312488", "skuId": 66211839, "bizVendorCode": "SD11501"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减45", "groupId": 1}, "vehicleCode": "5524", "highestPrice": 605, "rCoup": 0, "minDPrice": 196, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_5524_", "hot": 0, "minTPrice": 414, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 2, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3987_0_107059_107059"], "introduce": "当前车型最低价"}, "minDOrinPrice": 218, "isEasy": true, "isCredit": true, "maximumCommentCount": 44882, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 29, "minTOrinPrice": 459, "logicIndex": 2, "isGroup": true, "renderUniqId": "5524_29__414_196_218_信用免押_easyLife_周三福利日已减45"}, {"groupSort": 1, "lowestPrice": 192, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 321, "detail": [{"code": "1001", "amount": 365, "amountDesc": "¥365", "name": "租车费"}, {"code": "3743", "amount": 44, "amountDesc": "¥44", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥196", "originalDailyPrice": 218, "subAmount": 196, "name": "车辆租金", "amountStr": "¥321"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 67, "amountStr": "¥67", "detail": [{"code": "1002", "amount": 67, "amountDesc": "¥67", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 408, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥452", "subAmount": 452, "name": "总价", "amountStr": "¥408"}], "reference": {"vehicleCode": "0", "rStoreCode": "250114", "packageId": "sec", "pLev": 262523, "comPriceCode": "[c]", "bizVendorCode": "SD10763", "pStoreCode": "250114", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MjUwMTE0XzE3MzkxXzFfMjE4XzM2NV8yMThfNDUyLjAwXzE5Ni4wXzQwOC4wXzBfMF8wLjBfMC4wXzY3LjAwXzIwLjAwXzAuMDBfMC4wMF80Mzc1NzcwOA==", "sendTypeForPickUpCar": 0, "skuId": 43757708, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 262523, "vendorCode": "15003709", "vendorVehicleCode": "9937_56437_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15006996", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1680701", "skuId": 59149497, "bizVendorCode": "SD14278"}}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 7712412, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15003454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247392", "skuId": 24651446, "bizVendorCode": "SD14476"}}, {"reference": {"vendorCode": "15005693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625283", "skuId": 60938557, "bizVendorCode": "SD12894"}}, {"reference": {"vendorCode": "15001793", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183909", "skuId": 57011641, "bizVendorCode": "SD8766"}}, {"reference": {"vendorCode": "46492", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106965", "skuId": 28097917, "bizVendorCode": "SD3933"}}, {"reference": {"vendorCode": "15006932", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2396556", "skuId": 77140855, "bizVendorCode": "SD12823"}}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 3868574, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "15007420", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1957775", "skuId": 64711921, "bizVendorCode": "SD14800"}}, {"reference": {"vendorCode": "15000935", "vehicleCode": "0", "packageType": 1, "pStoreCode": "265301", "skuId": 8549451, "bizVendorCode": "SD7678"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 45603986, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15004427", "vehicleCode": "0", "packageType": 1, "pStoreCode": "313573", "skuId": 68539115, "bizVendorCode": "SD11544"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 62812394, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15001199", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175820", "skuId": 62721149, "bizVendorCode": "SD8155"}}, {"reference": {"vendorCode": "15003362", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229176", "skuId": 63094592, "bizVendorCode": "SD10401"}}, {"reference": {"vendorCode": "78579", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114931", "skuId": 58528527, "bizVendorCode": "SD4755"}}, {"reference": {"vendorCode": "15001364", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181671", "skuId": 54528776, "bizVendorCode": "SD13705"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652867, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "83386", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117466", "skuId": 68640475, "bizVendorCode": "SD5613"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901332, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "69279", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116480", "skuId": 24653511, "bizVendorCode": "SD6519"}}, {"reference": {"vendorCode": "80545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107268", "skuId": 4684110, "bizVendorCode": "SD4092"}}, {"reference": {"vendorCode": "80771", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116982", "skuId": 69864488, "bizVendorCode": "SD6709"}}, {"reference": {"vendorCode": "15004617", "vehicleCode": "0", "packageType": 1, "pStoreCode": "322596", "skuId": 25569188, "bizVendorCode": "SD11749"}}, {"reference": {"vendorCode": "32231", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107102", "skuId": 65083838, "bizVendorCode": "SD8256"}}, {"reference": {"vendorCode": "15004152", "vehicleCode": "0", "packageType": 1, "pStoreCode": "281261", "skuId": 62717488, "bizVendorCode": "SD11257"}}, {"reference": {"vendorCode": "15007315", "vehicleCode": "0", "packageType": 0, "pStoreCode": "1893900", "skuId": 62920509, "bizVendorCode": "SD14637"}}, {"reference": {"vendorCode": "30466", "vehicleCode": "0", "packageType": 0, "pStoreCode": "133340", "skuId": 76388866, "bizVendorCode": "SD6966"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184270", "skuId": 7597906, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "15001460", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182413", "skuId": 56713315, "bizVendorCode": "SD8423"}}, {"reference": {"vendorCode": "47522", "vehicleCode": "0", "packageType": 0, "pStoreCode": "116135", "skuId": 78099057, "bizVendorCode": "SD6355"}}, {"reference": {"vendorCode": "53893", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107119", "skuId": 51441748, "bizVendorCode": "SD3942"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902510, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "15000981", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174692", "skuId": 76276075, "bizVendorCode": "SD7724"}}, {"reference": {"vendorCode": "38236", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114232", "skuId": 77349784, "bizVendorCode": "SD4333"}}, {"reference": {"vendorCode": "80751", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2364349", "skuId": 73772546, "bizVendorCode": "SD13626"}}, {"reference": {"vendorCode": "15000269", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133749", "skuId": 6842937, "bizVendorCode": "SD7000"}}, {"reference": {"vendorCode": "15003234", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2370159", "skuId": 75764970, "bizVendorCode": "SD10267"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25567970, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "15001163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251411", "skuId": 54499544, "bizVendorCode": "SD8119"}}, {"reference": {"vendorCode": "15001470", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184119", "skuId": 30574034, "bizVendorCode": "SD8436"}}, {"reference": {"vendorCode": "15002319", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188920", "skuId": 70449100, "bizVendorCode": "SD9323"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 62802062, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "82843", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115278", "skuId": 54883718, "bizVendorCode": "SD5061"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333209, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "62072", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116252", "skuId": 23720040, "bizVendorCode": "SD6414"}}, {"reference": {"vendorCode": "13031", "vehicleCode": "0", "packageType": 1, "pStoreCode": "78", "skuId": 65947393, "bizVendorCode": "SD14450"}}, {"reference": {"vendorCode": "13032", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2961", "skuId": 65947400, "bizVendorCode": "SD3012"}}, {"reference": {"vendorCode": "15000372", "vehicleCode": "0", "packageType": 1, "pStoreCode": "308155", "skuId": 77351660, "bizVendorCode": "SD7114"}}, {"reference": {"vendorCode": "15000295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133807", "skuId": 2317191, "bizVendorCode": "SD7027"}}, {"reference": {"vendorCode": "15007295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1851872", "skuId": 63076833, "bizVendorCode": "SD14617"}}, {"reference": {"vendorCode": "15003850", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251283", "skuId": 77107031, "bizVendorCode": "SD10907"}}, {"reference": {"vendorCode": "13088", "vehicleCode": "0", "packageType": 0, "pStoreCode": "138334", "skuId": 14001464, "bizVendorCode": "SD6991"}}, {"reference": {"vendorCode": "15001630", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183735", "skuId": 77279406, "bizVendorCode": "SD8601"}}, {"reference": {"vendorCode": "15002723", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193183", "skuId": 62932290, "bizVendorCode": "SD9747"}}, {"reference": {"vendorCode": "72983", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116781", "skuId": 52816478, "bizVendorCode": "SD15526"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减44", "groupId": 1}, "vehicleCode": "17391", "highestPrice": 1500, "rCoup": 0, "minDPrice": 196, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_17391_", "hot": 0, "minTPrice": 408, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 4, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3933_0_106965_106965"], "introduce": "当前车型最低价"}, "minDOrinPrice": 218, "isEasy": true, "isCredit": true, "maximumCommentCount": 46078, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 57, "minTOrinPrice": 452, "logicIndex": 3, "isGroup": true, "renderUniqId": "17391_57__408_196_218_信用免押_easyLife_周三福利日已减44"}, {"groupSort": 4, "lowestPrice": 192, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 326, "detail": [{"code": "1001", "amount": 371, "amountDesc": "¥371", "name": "租车费"}, {"code": "3743", "amount": 45, "amountDesc": "¥45", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥196", "originalDailyPrice": 218, "subAmount": 196, "name": "车辆租金", "amountStr": "¥326"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 68, "amountStr": "¥68", "detail": [{"code": "1002", "amount": 68, "amountDesc": "¥68", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 414, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥459", "subAmount": 459, "name": "总价", "amountStr": "¥414"}], "reference": {"vehicleCode": "0", "rStoreCode": "182187", "packageId": "sec", "pLev": 1319396, "comPriceCode": "[c]MTA0MTQzOTUwfDIwfDAuMDEtMjIyNS0wMDA6MCAwMDo4JiYxMCYyMWUmMTgmdHJ1MDI1LTgmJDIzIDAwMDEtMjAwJjI6MDA6NTMmMjE4JjEwMCZ0My4wMDE4OCZydWUmfDEwMDEzMiQyMTgmMSYyJjEwMDMzNzEkMC4wMCYxJjIwMCQxJjIwLjImNDAwMDImNjguMC4wMCYwMjUtMCR8MjIgMTkwMS0yMDAmMjozMDowMS0yMDI1LTozMDo0IDE4MDI1LTAwfDI1IDE5MDEtMTQ4AAA6Mjg6", "bizVendorCode": "SD6835", "pStoreCode": "182187", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgyMTg3XzUyODJfMV8yMThfMzcxXzIxOF80NTkuMDBfMTk2LjBfNDE0LjBfMF8wXzAuMF8wLjBfNjguMDBfMjAuMDBfMC4wMF8wLjAwXzQzOTUxMDQx", "sendTypeForPickUpCar": 0, "skuId": 43951041, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1319396, "vendorCode": "15001454", "vendorVehicleCode": "10131_32519_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15003454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247392", "skuId": 24651453, "bizVendorCode": "SD14476"}}, {"reference": {"vendorCode": "15005693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625283", "skuId": 60938508, "bizVendorCode": "SD12894"}}, {"reference": {"vendorCode": "15001793", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183909", "skuId": 57029589, "bizVendorCode": "SD8766"}}, {"reference": {"vendorCode": "15003362", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229176", "skuId": 66620443, "bizVendorCode": "SD10401"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652888, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901326, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902506, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25608178, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "15001073", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174846", "skuId": 44060318, "bizVendorCode": "SD8032"}}, {"reference": {"vendorCode": "15004351", "vehicleCode": "0", "packageType": 0, "pStoreCode": "425573", "skuId": 78113932, "bizVendorCode": "SD11466"}}, {"reference": {"vendorCode": "13094", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188084", "skuId": 6900297, "bizVendorCode": "SD3866"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减45", "groupId": 1}, "vehicleCode": "5282", "highestPrice": 528, "rCoup": 0, "minDPrice": 196, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_5282_", "hot": 0, "minTPrice": 414, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 6, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD10401_0_229176_229176"], "introduce": "当前车型最低价"}, "minDOrinPrice": 218, "isEasy": true, "isCredit": true, "maximumCommentCount": 52247, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 12, "minTOrinPrice": 459, "logicIndex": 4, "isGroup": true, "renderUniqId": "5282_12__414_196_218_信用免押_easyLife_周三福利日已减45"}, {"groupSort": 5, "lowestPrice": 192, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 383, "detail": [{"code": "1001", "amount": 436, "amountDesc": "¥436", "name": "租车费"}, {"code": "3743", "amount": 53, "amountDesc": "¥53", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥192", "originalDailyPrice": 218, "subAmount": 192, "name": "车辆租金", "amountStr": "¥383"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 483, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥536", "subAmount": 536, "name": "总价", "amountStr": "¥483"}], "reference": {"vehicleCode": "0", "rStoreCode": "261024", "packageId": "sec", "pLev": 378638, "comPriceCode": "[c]", "bizVendorCode": "SD15525", "pStoreCode": "261024", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MjYxMDI0XzE3NTYxXzFfMjE4XzQzNl8yMThfNTM2LjAwXzE5Mi4wXzQ4My4wXzBfMF8wLjBfMC4wXzgwLjAwXzIwLjAwXzAuMDBfMC4wMF8yNDY1Mjg5NQ==", "sendTypeForPickUpCar": 0, "skuId": 24652895, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 378638, "vendorCode": "15003922", "vendorVehicleCode": "3934_22899_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 68748135, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 55172048, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "15001908", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184116", "skuId": 44383704, "bizVendorCode": "SD8888"}}, {"reference": {"vendorCode": "13094", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188084", "skuId": 6900320, "bizVendorCode": "SD3866"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减53", "groupId": 1}, "vehicleCode": "17561", "highestPrice": 528, "rCoup": 0, "minDPrice": 192, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_17561_", "hot": 0, "minTPrice": 483, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 7, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD15525_0_261024_261024"], "introduce": "当前车型最低价"}, "minDOrinPrice": 218, "isEasy": true, "isCredit": true, "maximumCommentCount": 52247, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 5, "minTOrinPrice": 536, "logicIndex": 5, "isGroup": true, "renderUniqId": "17561_5__483_192_218_信用免押_easyLife_周三福利日已减53"}, {"groupSort": 6, "lowestPrice": 227, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 454, "detail": [{"code": "1001", "amount": 516, "amountDesc": "¥516", "name": "租车费"}, {"code": "3743", "amount": 62, "amountDesc": "¥62", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥227", "originalDailyPrice": 258, "subAmount": 227, "name": "车辆租金", "amountStr": "¥454"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 554, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥616", "subAmount": 616, "name": "总价", "amountStr": "¥554"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "packageId": "sec", "pLev": 1648907, "comPriceCode": "[c]", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzQ2NjNfMV8yNThfNTE2XzI1OF82MTYuMDBfMjI3LjBfNTU0LjBfMF8wXzAuMF8wLjBfODAuMDBfMjAuMDBfMC4wMF8wLjAwXzU1MTcyNDU0", "sendTypeForPickUpCar": 0, "skuId": 55172454, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1648907, "vendorCode": "13092", "vendorVehicleCode": "85590_44737_pupai"}, "isMinTPriceVendor": true}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减62", "groupId": 1}, "vehicleCode": "4663", "highestPrice": 227, "rCoup": 0, "minDPrice": 227, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_4663_", "hot": 0, "minTPrice": 554, "lowestDistance": 0, "group": 870, "type": 0, "sortNum": 3, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 258, "isEasy": true, "isCredit": true, "maximumCommentCount": 44882, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "minTOrinPrice": 616, "logicIndex": 6, "isGroup": true, "renderUniqId": "4663_1__554_227_258_信用免押_easyLife_周三福利日已减62"}, {"groupSort": 0, "lowestPrice": 209, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 350, "detail": [{"code": "1001", "amount": 398, "amountDesc": "¥398", "name": "租车费"}, {"code": "3743", "amount": 48, "amountDesc": "¥48", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥214", "originalDailyPrice": 238, "subAmount": 214, "name": "车辆租金", "amountStr": "¥350"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 51, "amountStr": "¥51", "detail": [{"code": "1002", "amount": 51, "amountDesc": "¥51", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 421, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥469", "subAmount": 469, "name": "总价", "amountStr": "¥421"}], "reference": {"vehicleCode": "0", "rStoreCode": "250114", "packageId": "sec", "pLev": 262523, "comPriceCode": "[c]", "bizVendorCode": "SD10763", "pStoreCode": "250114", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MjUwMTE0XzE3NjIyXzFfMjM4XzM5OF8yMzhfNDY5LjAwXzIxNC4wXzQyMS4wXzBfMF8wLjBfMC4wXzUxLjAwXzIwLjAwXzAuMDBfMC4wMF80Mzk2MDE0MQ==", "sendTypeForPickUpCar": 0, "skuId": 43960141, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 262523, "vendorCode": "15003709", "vendorVehicleCode": "10153_65418_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15006996", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1680701", "skuId": 58601593, "bizVendorCode": "SD14278"}}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 44536297, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15003454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247392", "skuId": 44348340, "bizVendorCode": "SD14476"}}, {"reference": {"vendorCode": "15005693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625283", "skuId": 47765684, "bizVendorCode": "SD12894"}}, {"reference": {"vendorCode": "15001793", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183909", "skuId": 56960247, "bizVendorCode": "SD8766"}}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 59106839, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 51804509, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652307, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 24649409, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "15003785", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250407", "skuId": 77983067, "bizVendorCode": "SD10839"}}, {"reference": {"vendorCode": "15001364", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181671", "skuId": 54498515, "bizVendorCode": "SD13705"}}, {"reference": {"vendorCode": "15001460", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182413", "skuId": 54876347, "bizVendorCode": "SD8423"}}, {"reference": {"vendorCode": "15003362", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229176", "skuId": 63078877, "bizVendorCode": "SD10401"}}, {"reference": {"vendorCode": "15001163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251411", "skuId": 57122164, "bizVendorCode": "SD8119"}}, {"reference": {"vendorCode": "15004427", "vehicleCode": "0", "packageType": 1, "pStoreCode": "313573", "skuId": 66065875, "bizVendorCode": "SD11544"}}, {"reference": {"vendorCode": "81525", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107474", "skuId": 29208691, "bizVendorCode": "SD4102"}}, {"reference": {"vendorCode": "83386", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117466", "skuId": 68649190, "bizVendorCode": "SD5613"}}, {"reference": {"vendorCode": "15004152", "vehicleCode": "0", "packageType": 1, "pStoreCode": "281261", "skuId": 61433590, "bizVendorCode": "SD11257"}}, {"reference": {"vendorCode": "15000295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133807", "skuId": 45470118, "bizVendorCode": "SD7027"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减48", "groupId": 1}, "vehicleCode": "17622", "highestPrice": 800, "rCoup": 0, "minDPrice": 214, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_17622_", "hot": 0, "minTPrice": 421, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 8, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3987_0_107059_107059"], "introduce": "当前车型最低价"}, "minDOrinPrice": 238, "isEasy": true, "isCredit": true, "maximumCommentCount": 16068, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 20, "minTOrinPrice": 469, "logicIndex": 7, "isGroup": false, "renderUniqId": "17622_20__421_214_238_信用免押_easyLife_周三福利日已减48"}, {"groupSort": 1, "lowestPrice": 227, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 340, "detail": [{"code": "1001", "amount": 387, "amountDesc": "¥387", "name": "租车费"}, {"code": "3743", "amount": 47, "amountDesc": "¥47", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥235", "originalDailyPrice": 258, "subAmount": 235, "name": "车辆租金", "amountStr": "¥340"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 420, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥467", "subAmount": 467, "name": "总价", "amountStr": "¥420"}], "reference": {"vehicleCode": "0", "rStoreCode": "107480", "packageId": "sec", "pLev": 1895720, "comPriceCode": "[c]", "bizVendorCode": "SD3963", "pStoreCode": "107480", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3NDgwXzE3MzkwXzFfMjU4XzM4N18yNThfNDY3LjAwXzIzNS4wXzQyMC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF8yNTcxNTk4NQ==", "sendTypeForPickUpCar": 0, "skuId": 25715985, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1895720, "vendorCode": "61937", "vendorVehicleCode": "2477_56448_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15003709", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250114", "skuId": 43671755, "bizVendorCode": "SD10763"}}, {"reference": {"vendorCode": "15006996", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1680701", "skuId": 59147236, "bizVendorCode": "SD14278"}}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 6903946, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15003454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247392", "skuId": 24650984, "bizVendorCode": "SD14476"}}, {"reference": {"vendorCode": "15006998", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1634382", "skuId": 59109597, "bizVendorCode": "SD14280"}}, {"reference": {"vendorCode": "15005693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625283", "skuId": 48049863, "bizVendorCode": "SD12894"}}, {"reference": {"vendorCode": "15001793", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183909", "skuId": 57029519, "bizVendorCode": "SD8766"}}, {"reference": {"vendorCode": "46492", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106965", "skuId": 28097973, "bizVendorCode": "SD3933"}}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 6390411, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "15001194", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175686", "skuId": 6802397, "bizVendorCode": "SD8150"}}, {"reference": {"vendorCode": "80545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107268", "skuId": 4992774, "bizVendorCode": "SD4092"}}, {"reference": {"vendorCode": "15002585", "vehicleCode": "0", "packageType": 1, "pStoreCode": "192639", "skuId": 6825645, "bizVendorCode": "SD14949"}}, {"reference": {"vendorCode": "82105", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117341", "skuId": 4468419, "bizVendorCode": "SD6129"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 45008636, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15004427", "vehicleCode": "0", "packageType": 1, "pStoreCode": "313573", "skuId": 65943046, "bizVendorCode": "SD11544"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 48039713, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "80771", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116982", "skuId": 77082069, "bizVendorCode": "SD6709"}}, {"reference": {"vendorCode": "15001199", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175820", "skuId": 58064168, "bizVendorCode": "SD8155"}}, {"reference": {"vendorCode": "15003362", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229176", "skuId": 63094578, "bizVendorCode": "SD10401"}}, {"reference": {"vendorCode": "78579", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114931", "skuId": 58528639, "bizVendorCode": "SD4755"}}, {"reference": {"vendorCode": "15001364", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181671", "skuId": 56543719, "bizVendorCode": "SD13705"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652832, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "83386", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117466", "skuId": 68640454, "bizVendorCode": "SD5613"}}, {"reference": {"vendorCode": "15004617", "vehicleCode": "0", "packageType": 1, "pStoreCode": "322596", "skuId": 25569195, "bizVendorCode": "SD11749"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901331, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902511, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "69279", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116480", "skuId": 27512325, "bizVendorCode": "SD6519"}}, {"reference": {"vendorCode": "15004152", "vehicleCode": "0", "packageType": 1, "pStoreCode": "281261", "skuId": 61434136, "bizVendorCode": "SD11257"}}, {"reference": {"vendorCode": "80751", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2364349", "skuId": 73772273, "bizVendorCode": "SD13626"}}, {"reference": {"vendorCode": "32231", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107102", "skuId": 65083747, "bizVendorCode": "SD8256"}}, {"reference": {"vendorCode": "30466", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133340", "skuId": 72785280, "bizVendorCode": "SD6966"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25567949, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184270", "skuId": 7334062, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "15004136", "vehicleCode": "0", "packageType": 1, "pStoreCode": "274541", "skuId": 69856634, "bizVendorCode": "SD11239"}}, {"reference": {"vendorCode": "81525", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107474", "skuId": 24587795, "bizVendorCode": "SD4102"}}, {"reference": {"vendorCode": "15006333", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2239938", "skuId": 69762246, "bizVendorCode": "SD13557"}}, {"reference": {"vendorCode": "15003234", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2370159", "skuId": 75764998, "bizVendorCode": "SD10267"}}, {"reference": {"vendorCode": "15004609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "327706", "skuId": 71125342, "bizVendorCode": "SD11741"}}, {"reference": {"vendorCode": "15002319", "vehicleCode": "0", "packageType": 0, "pStoreCode": "188920", "skuId": 72975519, "bizVendorCode": "SD9323"}}, {"reference": {"vendorCode": "15001073", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174846", "skuId": 44060346, "bizVendorCode": "SD8032"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 62801796, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "15004385", "vehicleCode": "0", "packageType": 1, "pStoreCode": "312488", "skuId": 65361815, "bizVendorCode": "SD11501"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333258, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "15004914", "vehicleCode": "0", "packageType": 1, "pStoreCode": "411510", "skuId": 75773762, "bizVendorCode": "SD12091"}}, {"reference": {"vendorCode": "15007295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1851872", "skuId": 63082601, "bizVendorCode": "SD14617"}}, {"reference": {"vendorCode": "13033", "vehicleCode": "0", "packageType": 1, "pStoreCode": "44813", "skuId": 62153687, "bizVendorCode": "SD3008"}}, {"reference": {"vendorCode": "82843", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115278", "skuId": 54883753, "bizVendorCode": "SD5061"}}, {"reference": {"vendorCode": "15004512", "vehicleCode": "0", "packageType": 1, "pStoreCode": "319138", "skuId": 75240411, "bizVendorCode": "SD11636"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减47", "groupId": 1}, "vehicleCode": "17390", "highestPrice": 1184, "rCoup": 0, "minDPrice": 235, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_17390_", "hot": 0, "minTPrice": 420, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 9, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3933_0_106965_106965"], "introduce": "当前车型最低价"}, "minDOrinPrice": 258, "isEasy": true, "isCredit": true, "maximumCommentCount": 44882, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 49, "minTOrinPrice": 467, "logicIndex": 8, "isGroup": true, "renderUniqId": "17390_49__420_235_258_信用免押_easyLife_周三福利日已减47"}, {"groupSort": 4, "lowestPrice": 227, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 379, "detail": [{"code": "1001", "amount": 431, "amountDesc": "¥431", "name": "租车费"}, {"code": "3743", "amount": 52, "amountDesc": "¥52", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥232", "originalDailyPrice": 258, "subAmount": 232, "name": "车辆租金", "amountStr": "¥379"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 67, "amountStr": "¥67", "detail": [{"code": "1002", "amount": 67, "amountDesc": "¥67", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 466, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥518", "subAmount": 518, "name": "总价", "amountStr": "¥466"}], "reference": {"vehicleCode": "0", "rStoreCode": "1680701", "packageId": "sec", "pLev": 1588861, "comPriceCode": "[c]OTY4NjU5MTQwfDIwfDAuMDEtMjIyNS0wMDA6MCAwMDo4JiYxMCYyNWUmMTgmdHJ1MDI1LTgmJDIzIDAwMDEtMjAwJjI6MDA6NzMmMjU4JjEwMCZ0My4wMDE4OCZydWUmfDEwMDEyNiQyNTgmMSYyJjEwMDM0MzEkMC4wMCYxJjIwMCQxJjIwLjImNDAwMDImNjcuMC4wMCYwMjUtMCR8MjIgMTkwMS0yMDAmMjozMDowMS0yMDI1LTozMDo0IDE4MDI1LTAwfDI1IDE5MDEtMTQ4AAA6Mjg6", "bizVendorCode": "SD14278", "pStoreCode": "1680701", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTY4MDcwMV81MjgxXzFfMjU4XzQzMV8yNThfNTE4LjAwXzIzMi4wXzQ2Ni4wXzBfMF8wLjBfMC4wXzY3LjAwXzIwLjAwXzAuMDBfMC4wMF81OTE0OTY4Ng==", "sendTypeForPickUpCar": 0, "skuId": 59149686, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1588861, "vendorCode": "15006996", "vendorVehicleCode": "90269_32530_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 43951027, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15003454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247392", "skuId": 24651544, "bizVendorCode": "SD14476"}}, {"reference": {"vendorCode": "15005693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625283", "skuId": 48049898, "bizVendorCode": "SD12894"}}, {"reference": {"vendorCode": "15001793", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183909", "skuId": 57029561, "bizVendorCode": "SD8766"}}, {"reference": {"vendorCode": "15003362", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229176", "skuId": 66615340, "bizVendorCode": "SD10401"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652853, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901324, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902507, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25608360, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "15007870", "vehicleCode": "0", "packageType": 0, "pStoreCode": "2381555", "skuId": 76178579, "bizVendorCode": "SD15267"}}, {"reference": {"vendorCode": "15002723", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193183", "skuId": 61060469, "bizVendorCode": "SD9747"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减52", "groupId": 1}, "vehicleCode": "5281", "highestPrice": 879, "rCoup": 0, "minDPrice": 232, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_5281_", "hot": 0, "minTPrice": 466, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 11, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD10401_0_229176_229176"], "introduce": "当前车型最低价"}, "minDOrinPrice": 258, "isEasy": true, "isCredit": true, "maximumCommentCount": 44882, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 12, "minTOrinPrice": 518, "logicIndex": 9, "isGroup": true, "renderUniqId": "5281_12__466_232_258_信用免押_easyLife_周三福利日已减52"}, {"groupSort": 2, "lowestPrice": 227, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 379, "detail": [{"code": "1001", "amount": 431, "amountDesc": "¥431", "name": "租车费"}, {"code": "3743", "amount": 52, "amountDesc": "¥52", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥232", "originalDailyPrice": 258, "subAmount": 232, "name": "车辆租金", "amountStr": "¥379"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 67, "amountStr": "¥67", "detail": [{"code": "1002", "amount": 67, "amountDesc": "¥67", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 466, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥518", "subAmount": 518, "name": "总价", "amountStr": "¥466"}], "reference": {"vehicleCode": "0", "rStoreCode": "1680701", "packageId": "sec", "pLev": 1588861, "comPriceCode": "[c]NzI1NzU5MTQwfDIwfDAuMDEtMjIyNS0wMDA6MCAwMDo4JiYxMCYyNWUmMTgmdHJ1MDI1LTgmJDIzIDAwMDEtMjAwJjI6MDA6NzMmMjU4JjEwMCZ0My4wMDE4OCZydWUmfDEwMDEyNiQyNTgmMSYyJjEwMDM0MzEkMC4wMCYxJjIwMCQxJjIwLjImNDAwMDImNjcuMC4wMCYwMjUtMCR8MjIgMTkwMS0yMDAmMjozMDowMS0yMDI1LTozMDo0IDE4MDI1LTAwfDI1IDE5MDEtMTQ4AAA6Mjg6", "bizVendorCode": "SD14278", "pStoreCode": "1680701", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTY4MDcwMV81NTI2XzFfMjU4XzQzMV8yNThfNTE4LjAwXzIzMi4wXzQ2Ni4wXzBfMF8wLjBfMC4wXzY3LjAwXzIwLjAwXzAuMDBfMC4wMF81OTE0NzI1Nw==", "sendTypeForPickUpCar": 0, "skuId": 59147257, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1588861, "vendorCode": "15006996", "vendorVehicleCode": "90262_49566_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 6903934, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15003454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247392", "skuId": 24651537, "bizVendorCode": "SD14476"}}, {"reference": {"vendorCode": "15005693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625283", "skuId": 48049870, "bizVendorCode": "SD12894"}}, {"reference": {"vendorCode": "15001793", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183909", "skuId": 57029547, "bizVendorCode": "SD8766"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 44386581, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 47764662, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15001199", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175820", "skuId": 43673484, "bizVendorCode": "SD8155"}}, {"reference": {"vendorCode": "15003362", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229176", "skuId": 66588558, "bizVendorCode": "SD10401"}}, {"reference": {"vendorCode": "15004703", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326964", "skuId": 52257052, "bizVendorCode": "SD11845"}}, {"reference": {"vendorCode": "78579", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114931", "skuId": 58528541, "bizVendorCode": "SD4755"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652839, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "83386", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117466", "skuId": 68640461, "bizVendorCode": "SD5613"}}, {"reference": {"vendorCode": "15004617", "vehicleCode": "0", "packageType": 1, "pStoreCode": "322596", "skuId": 25569160, "bizVendorCode": "SD11749"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901329, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902508, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "15004152", "vehicleCode": "0", "packageType": 1, "pStoreCode": "281261", "skuId": 66217264, "bizVendorCode": "SD11257"}}, {"reference": {"vendorCode": "69279", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116480", "skuId": 24653546, "bizVendorCode": "SD6519"}}, {"reference": {"vendorCode": "15001351", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181800", "skuId": 4883108, "bizVendorCode": "SD8312"}}, {"reference": {"vendorCode": "15003234", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2370159", "skuId": 75764963, "bizVendorCode": "SD10267"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25567963, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184270", "skuId": 7362307, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "81525", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107474", "skuId": 25009762, "bizVendorCode": "SD4102"}}, {"reference": {"vendorCode": "15001163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251411", "skuId": 62709935, "bizVendorCode": "SD8119"}}, {"reference": {"vendorCode": "15001470", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184119", "skuId": 30574104, "bizVendorCode": "SD8436"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 62801782, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "82843", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115278", "skuId": 54883662, "bizVendorCode": "SD5061"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333244, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "15004609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "327706", "skuId": 71126231, "bizVendorCode": "SD11741"}}, {"reference": {"vendorCode": "15007295", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1851872", "skuId": 63085422, "bizVendorCode": "SD14617"}}, {"reference": {"vendorCode": "15002723", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193183", "skuId": 61060476, "bizVendorCode": "SD9747"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减52", "groupId": 1}, "vehicleCode": "5526", "highestPrice": 1320, "rCoup": 0, "minDPrice": 232, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_5526_", "hot": 0, "minTPrice": 466, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 12, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD5477_0_116645_116645"], "introduce": "当前车型最低价"}, "minDOrinPrice": 258, "isEasy": true, "isCredit": true, "maximumCommentCount": 44882, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 31, "minTOrinPrice": 518, "logicIndex": 10, "isGroup": true, "renderUniqId": "5526_31__466_232_258_信用免押_easyLife_周三福利日已减52"}, {"groupSort": 5, "lowestPrice": 227, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 379, "detail": [{"code": "1001", "amount": 431, "amountDesc": "¥431", "name": "租车费"}, {"code": "3743", "amount": 52, "amountDesc": "¥52", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥232", "originalDailyPrice": 258, "subAmount": 232, "name": "车辆租金", "amountStr": "¥379"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 67, "amountStr": "¥67", "detail": [{"code": "1002", "amount": 67, "amountDesc": "¥67", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 466, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥518", "subAmount": 518, "name": "总价", "amountStr": "¥466"}], "reference": {"vehicleCode": "0", "rStoreCode": "1680701", "packageId": "sec", "pLev": 1588861, "comPriceCode": "[c]OTQ5MDU5MTQwfDIwfDAuMDEtMjIyNS0wMDA6MCAwMDo4JiYxMCYyNWUmMTgmdHJ1MDI1LTgmJDIzIDAwMDEtMjAwJjI6MDA6NzMmMjU4JjEwMCZ0My4wMDE4OCZydWUmfDEwMDEyNiQyNTgmMSYyJjEwMDM0MzEkMC4wMCYxJjIwMCQxJjIwLjImNDAwMDImNjcuMC4wMCYwMjUtMCR8MjIgMTkwMS0yMDAmMjozMDowMS0yMDI1LTozMDo0IDE4MDI1LTAwfDI1IDE5MDEtMTQ4AAA6Mjg6", "bizVendorCode": "SD14278", "pStoreCode": "1680701", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTY4MDcwMV80NjYwXzFfMjU4XzQzMV8yNThfNTE4LjAwXzIzMi4wXzQ2Ni4wXzBfMF8wLjBfMC4wXzY3LjAwXzIwLjAwXzAuMDBfMC4wMF81OTE0OTQ5MA==", "sendTypeForPickUpCar": 0, "skuId": 59149490, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1588861, "vendorCode": "15006996", "vendorVehicleCode": "90265_32537_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 1862272, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 69750794, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652860, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 68748128, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 42922860, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "15004703", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326964", "skuId": 55459888, "bizVendorCode": "SD11845"}}, {"reference": {"vendorCode": "15004592", "vehicleCode": "0", "packageType": 1, "pStoreCode": "322519", "skuId": 24870826, "bizVendorCode": "SD11722"}}, {"reference": {"vendorCode": "15001630", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183735", "skuId": 77279434, "bizVendorCode": "SD8601"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减52", "groupId": 1}, "vehicleCode": "4660", "highestPrice": 999, "rCoup": 0, "minDPrice": 232, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_4660_", "hot": 0, "minTPrice": 466, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 13, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3987_0_107059_107059"], "introduce": "当前车型最低价"}, "minDOrinPrice": 258, "isEasy": true, "isCredit": true, "maximumCommentCount": 44882, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 9, "minTOrinPrice": 518, "logicIndex": 11, "isGroup": true, "renderUniqId": "4660_9__466_232_258_信用免押_easyLife_周三福利日已减52"}, {"groupSort": 3, "lowestPrice": 227, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 379, "detail": [{"code": "1001", "amount": 431, "amountDesc": "¥431", "name": "租车费"}, {"code": "3743", "amount": 52, "amountDesc": "¥52", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥232", "originalDailyPrice": 258, "subAmount": 232, "name": "车辆租金", "amountStr": "¥379"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 67, "amountStr": "¥67", "detail": [{"code": "1002", "amount": 67, "amountDesc": "¥67", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 466, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥518", "subAmount": 518, "name": "总价", "amountStr": "¥466"}], "reference": {"vehicleCode": "0", "rStoreCode": "1680701", "packageId": "sec", "pLev": 1588861, "comPriceCode": "[c]NzI2NDU5MTQwfDIwfDAuMDEtMjIyNS0wMDA6MCAwMDo4JiYxMCYyNWUmMTgmdHJ1MDI1LTgmJDIzIDAwMDEtMjAwJjI6MDA6NzMmMjU4JjEwMCZ0My4wMDE4OCZydWUmfDEwMDEyNiQyNTgmMSYyJjEwMDM0MzEkMC4wMCYxJjIwMCQxJjIwLjImNDAwMDImNjcuMC4wMCYwMjUtMCR8MjIgMTkwMS0yMDAmMjozMDowMS0yMDI1LTozMDo0IDE4MDI1LTAwfDI1IDE5MDEtMTQ4AAA6Mjg6", "bizVendorCode": "SD14278", "pStoreCode": "1680701", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTY4MDcwMV81Mzc0XzFfMjU4XzQzMV8yNThfNTE4LjAwXzIzMi4wXzQ2Ni4wXzBfMF8wLjBfMC4wXzY3LjAwXzIwLjAwXzAuMDBfMC4wMF81OTE0NzI2NA==", "sendTypeForPickUpCar": 0, "skuId": 59147264, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1588861, "vendorCode": "15006996", "vendorVehicleCode": "90263_3908_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 44414126, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15003454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247392", "skuId": 24651187, "bizVendorCode": "SD14476"}}, {"reference": {"vendorCode": "15005693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625283", "skuId": 48049891, "bizVendorCode": "SD12894"}}, {"reference": {"vendorCode": "15001793", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183909", "skuId": 57029554, "bizVendorCode": "SD8766"}}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 4443845, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "15001194", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175686", "skuId": 8328839, "bizVendorCode": "SD8150"}}, {"reference": {"vendorCode": "15000935", "vehicleCode": "0", "packageType": 1, "pStoreCode": "265301", "skuId": 68738531, "bizVendorCode": "SD7678"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 78063343, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 69748281, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15003362", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229176", "skuId": 66603300, "bizVendorCode": "SD10401"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652846, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901327, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902504, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "15004152", "vehicleCode": "0", "packageType": 1, "pStoreCode": "281261", "skuId": 61433835, "bizVendorCode": "SD11257"}}, {"reference": {"vendorCode": "69279", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116480", "skuId": 24653483, "bizVendorCode": "SD6519"}}, {"reference": {"vendorCode": "15004673", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326061", "skuId": 25608164, "bizVendorCode": "SD11813"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184270", "skuId": 23289757, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 62801544, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333237, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "13094", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188084", "skuId": 6900323, "bizVendorCode": "SD3866"}}, {"reference": {"vendorCode": "15004385", "vehicleCode": "0", "packageType": 1, "pStoreCode": "312488", "skuId": 66211734, "bizVendorCode": "SD11501"}}, {"reference": {"vendorCode": "15001308", "vehicleCode": "0", "packageType": 1, "pStoreCode": "176530", "skuId": 64697361, "bizVendorCode": "SD8269"}}, {"reference": {"vendorCode": "15007870", "vehicleCode": "0", "packageType": 0, "pStoreCode": "2381555", "skuId": 76178600, "bizVendorCode": "SD15267"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减52", "groupId": 1}, "vehicleCode": "5374", "highestPrice": 699, "rCoup": 0, "minDPrice": 232, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_5374_", "hot": 0, "minTPrice": 466, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 14, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3987_0_107059_107059"], "introduce": "当前车型最低价"}, "minDOrinPrice": 258, "isEasy": true, "isCredit": true, "maximumCommentCount": 52247, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********0", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 24, "minTOrinPrice": 518, "logicIndex": 12, "isGroup": true, "renderUniqId": "5374_24__466_232_258_信用免押_easyLife_周三福利日已减52"}, {"groupSort": 6, "lowestPrice": 227, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 386, "detail": [{"code": "1001", "amount": 439, "amountDesc": "¥439", "name": "租车费"}, {"code": "3743", "amount": 53, "amountDesc": "¥53", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥232", "originalDailyPrice": 258, "subAmount": 232, "name": "车辆租金", "amountStr": "¥386"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 68, "amountStr": "¥68", "detail": [{"code": "1002", "amount": 68, "amountDesc": "¥68", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 474, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥527", "subAmount": 527, "name": "总价", "amountStr": "¥474"}], "reference": {"vehicleCode": "0", "rStoreCode": "182187", "packageId": "sec", "pLev": 1319396, "comPriceCode": "[c]OTM3fDY5MDN8MjAyMC4wMC0yMiA1LTAxMDowMDAwOjAmJjEmJjI1OCYxODh0cnVlMjUtMCYkMjAgMDA6MS0yMzAmMjUwMDowMSYyMzgmMTgwJnRyLjAwMDg4JjF1ZSYxMTAwMTMyJHw1OCY0JjImMjAwMyYzOSQxLjAwJjEmMjAwJDEwMjAuMCY0MC4wMiYyOC4wMDAwJjYyNS0wJHwyMCAxOToxLTIyMCYyMDMwOjAxLTI0MjUtMDMwOjAgMTg6MjUtMDB8MjAgMTk6MS0xNTgAAAAyODo0", "bizVendorCode": "SD6835", "pStoreCode": "182187", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgyMTg3XzQ2NTlfMV8yNThfNDM5XzI1OF81MjcuMDBfMjMyLjBfNDc0LjBfMF8wXzAuMF8wLjBfNjguMDBfMjAuMDBfMC4wMF8wLjAwXzY5MDM5Mzc=", "sendTypeForPickUpCar": 0, "skuId": 6903937, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1319396, "vendorCode": "15001454", "vendorVehicleCode": "2755_32542_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 23031296, "bizVendorCode": "SD11238"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减53", "groupId": 1}, "vehicleCode": "4659", "highestPrice": 232, "rCoup": 0, "minDPrice": 232, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_4659_", "hot": 0, "minTPrice": 474, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 15, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 258, "isEasy": true, "isCredit": true, "maximumCommentCount": 44882, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********1", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 2, "minTOrinPrice": 527, "logicIndex": 13, "isGroup": true, "renderUniqId": "4659_2__474_232_258_信用免押_easyLife_周三福利日已减53"}, {"groupSort": 7, "lowestPrice": 227, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 454, "detail": [{"code": "1001", "amount": 516, "amountDesc": "¥516", "name": "租车费"}, {"code": "3743", "amount": 62, "amountDesc": "¥62", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥227", "originalDailyPrice": 258, "subAmount": 227, "name": "车辆租金", "amountStr": "¥454"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 554, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥616", "subAmount": 616, "name": "总价", "amountStr": "¥554"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "packageId": "sec", "pLev": 1648907, "comPriceCode": "[c]", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzQ2NjFfMV8yNThfNTE2XzI1OF82MTYuMDBfMjI3LjBfNTU0LjBfMF8wXzAuMF8wLjBfODAuMDBfMjAuMDBfMC4wMF8wLjAwXzU1MTYzMzMz", "sendTypeForPickUpCar": 0, "skuId": 55163333, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1648907, "vendorCode": "13092", "vendorVehicleCode": "85584_43070_pupai"}, "isMinTPriceVendor": true}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减62", "groupId": 1}, "vehicleCode": "4661", "highestPrice": 227, "rCoup": 0, "minDPrice": 227, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_4661_", "hot": 0, "minTPrice": 554, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 16, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 258, "isEasy": true, "isCredit": true, "maximumCommentCount": 44882, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********2", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "minTOrinPrice": 616, "logicIndex": 14, "isGroup": true, "renderUniqId": "4661_1__554_227_258_信用免押_easyLife_周三福利日已减62"}, {"groupSort": 0, "lowestPrice": 51, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 102, "detail": [{"code": "1001", "amount": 116, "amountDesc": "¥116", "name": "租车费"}, {"code": "3743", "amount": 14, "amountDesc": "¥14", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥51", "originalDailyPrice": 58, "subAmount": 51, "name": "车辆租金", "amountStr": "¥102"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 182, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥196", "subAmount": 196, "name": "总价", "amountStr": "¥182"}], "reference": {"vehicleCode": "0", "rStoreCode": "115986", "packageId": "", "pLev": 55308, "comPriceCode": "[c]", "bizVendorCode": "SD5349", "pStoreCode": "115986", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTE1OTg2XzE3NzI0XzFfNThfMTE2XzU4XzE5Ni4wMF81MS4wXzE4Mi4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF81MjI0MzE2NA==", "sendTypeForPickUpCar": 0, "skuId": 52243164, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 55308, "vendorCode": "30912", "vendorVehicleCode": "52243164"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13082", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183936", "skuId": 6901062, "bizVendorCode": "SD3926"}}, {"reference": {"vendorCode": "15006533", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2285298", "skuId": 72907010, "bizVendorCode": "SD7763"}}, {"reference": {"vendorCode": "31092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107528", "skuId": 23750371, "bizVendorCode": "SD3882"}}, {"reference": {"vendorCode": "15000088", "vehicleCode": "0", "packageType": 1, "pStoreCode": "161973", "skuId": 24662408, "bizVendorCode": "SD3127"}}, {"reference": {"vendorCode": "32231", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107102", "skuId": 65084118, "bizVendorCode": "SD8256"}}, {"reference": {"vendorCode": "13119", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193947", "skuId": 47571756, "bizVendorCode": "SD4201"}}, {"reference": {"vendorCode": "13119", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193947", "skuId": 41070198, "bizVendorCode": "SD4201"}}, {"reference": {"vendorCode": "15001913", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184113", "skuId": 6904013, "bizVendorCode": "SD8893"}}, {"reference": {"vendorCode": "15004163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "408969", "skuId": 51721923, "bizVendorCode": "SD3047"}}, {"reference": {"vendorCode": "15004163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "408969", "skuId": 74761310, "bizVendorCode": "SD3047"}}, {"reference": {"vendorCode": "15005627", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1335279", "skuId": 58068032, "bizVendorCode": "SD12826"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 69186769, "bizVendorCode": "SD7103"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减14", "groupId": 1}, "vehicleCode": "17639", "highestPrice": 308, "rCoup": 0, "minDPrice": 51, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_17639_", "hot": 0, "minTPrice": 182, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 17, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD5349_0_115986_115986"], "introduce": "当前车型最低价"}, "minDOrinPrice": 58, "isEasy": true, "isCredit": true, "maximumCommentCount": 52840, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 13, "minTOrinPrice": 196, "logicIndex": 15, "isGroup": false, "renderUniqId": "17639_13__182_51_58_信用免押_easyLife_周三福利日已减14"}, {"groupSort": 0, "lowestPrice": 209, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 314, "detail": [{"code": "1001", "amount": 357, "amountDesc": "¥357", "name": "租车费"}, {"code": "3743", "amount": 43, "amountDesc": "¥43", "name": "周三福利日"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥217", "originalDailyPrice": 238, "subAmount": 217, "name": "车辆租金", "amountStr": "¥314"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 45, "amountStr": "¥45", "detail": [{"code": "1002", "amount": 45, "amountDesc": "¥45", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 379, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥422", "subAmount": 422, "name": "总价", "amountStr": "¥379"}], "reference": {"vehicleCode": "0", "rStoreCode": "107480", "packageId": "sec", "pLev": 1895720, "comPriceCode": "[c]NTkzNjI1NzEwfDIwfDAuMDEtMjIyNS0wMDA6MCAwMDo4JiYxMCYyM2UmMjAmdHJ1MDI1LTgmJDIzIDAwMDEtMjAwJjI6MDA6MTkmMjM4JjEwMCZ0My4wMDIwOCZydWUmfDEwMDEwNCQyMzgmMSYyJjEwMDMzNTckMC4wMCYxJjIwMCQxJjIwLjImMzAwMDImNDUuMC4wMCYwMjUtMCR8MjIgMTkwMS0yMDAmMjozMDowMS0yMDI1LTozMDo0IDE4MDI1LTAwfDI1IDE5MDEtMTQ4AAA6Mjg6", "bizVendorCode": "SD3963", "pStoreCode": "107480", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3NDgwXzUzNjhfMV8yMzhfMzU3XzIzOF80MjIuMDBfMjE3LjBfMzc5LjBfMF8wXzAuMF8wLjBfNDUuMDBfMjAuMDBfMC4wMF8wLjAwXzI1NzE1OTM2", "sendTypeForPickUpCar": 0, "skuId": 25715936, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1895720, "vendorCode": "61937", "vendorVehicleCode": "2457_29412_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15003709", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250114", "skuId": 43671629, "bizVendorCode": "SD10763"}}, {"reference": {"vendorCode": "15006996", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1680701", "skuId": 59087057, "bizVendorCode": "SD14278"}}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 7679015, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15003454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247392", "skuId": 24651061, "bizVendorCode": "SD14476"}}, {"reference": {"vendorCode": "15005693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625283", "skuId": 54505774, "bizVendorCode": "SD12894"}}, {"reference": {"vendorCode": "15001793", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183909", "skuId": 56962487, "bizVendorCode": "SD8766"}}, {"reference": {"vendorCode": "15000088", "vehicleCode": "0", "packageType": 1, "pStoreCode": "161973", "skuId": 24662352, "bizVendorCode": "SD3127"}}, {"reference": {"vendorCode": "15003785", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250407", "skuId": 77900334, "bizVendorCode": "SD10839"}}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 25224466, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "15001194", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175686", "skuId": 4537611, "bizVendorCode": "SD8150"}}, {"reference": {"vendorCode": "15000372", "vehicleCode": "0", "packageType": 1, "pStoreCode": "308155", "skuId": 77351646, "bizVendorCode": "SD7114"}}, {"reference": {"vendorCode": "83528", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115374", "skuId": 1914737, "bizVendorCode": "SD5118"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 77982794, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 48047938, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15003362", "vehicleCode": "0", "packageType": 1, "pStoreCode": "229176", "skuId": 63078884, "bizVendorCode": "SD10401"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652293, "bizVendorCode": "SD15525"}}, {"reference": {"vendorCode": "15004617", "vehicleCode": "0", "packageType": 1, "pStoreCode": "322596", "skuId": 25569293, "bizVendorCode": "SD11749"}}, {"reference": {"vendorCode": "13115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188937", "skuId": 6901271, "bizVendorCode": "SD4130"}}, {"reference": {"vendorCode": "38236", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114232", "skuId": 77349602, "bizVendorCode": "SD4333"}}, {"reference": {"vendorCode": "15001364", "vehicleCode": "0", "packageType": 0, "pStoreCode": "181671", "skuId": 77468252, "bizVendorCode": "SD13705"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902492, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184270", "skuId": 7358219, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "15001460", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182413", "skuId": 55144972, "bizVendorCode": "SD8423"}}, {"reference": {"vendorCode": "32231", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107102", "skuId": 65083866, "bizVendorCode": "SD8256"}}, {"reference": {"vendorCode": "15003850", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251283", "skuId": 77083553, "bizVendorCode": "SD10907"}}, {"reference": {"vendorCode": "81525", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107474", "skuId": 24587760, "bizVendorCode": "SD4102"}}, {"reference": {"vendorCode": "13094", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188084", "skuId": 6900308, "bizVendorCode": "SD3866"}}, {"reference": {"vendorCode": "15001470", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184119", "skuId": 30574069, "bizVendorCode": "SD8436"}}, {"reference": {"vendorCode": "15001163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251411", "skuId": 54499530, "bizVendorCode": "SD8119"}}, {"reference": {"vendorCode": "15004163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "408969", "skuId": 51725297, "bizVendorCode": "SD3047"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 60698247, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "69279", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116480", "skuId": 24653504, "bizVendorCode": "SD6519"}}, {"reference": {"vendorCode": "15004427", "vehicleCode": "0", "packageType": 1, "pStoreCode": "313573", "skuId": 68416125, "bizVendorCode": "SD11544"}}, {"reference": {"vendorCode": "15001073", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174846", "skuId": 44060192, "bizVendorCode": "SD8032"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 61516694, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "78579", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114931", "skuId": 58528772, "bizVendorCode": "SD4755"}}, {"reference": {"vendorCode": "15001308", "vehicleCode": "0", "packageType": 1, "pStoreCode": "176530", "skuId": 64697802, "bizVendorCode": "SD8269"}}, {"reference": {"vendorCode": "13088", "vehicleCode": "0", "packageType": 0, "pStoreCode": "138334", "skuId": 10336355, "bizVendorCode": "SD6991"}}, {"reference": {"vendorCode": "82843", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115278", "skuId": 54883949, "bizVendorCode": "SD5061"}}, {"reference": {"vendorCode": "15004609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "327706", "skuId": 69942398, "bizVendorCode": "SD11741"}}, {"reference": {"vendorCode": "15007439", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1897274", "skuId": 65944012, "bizVendorCode": "SD14779"}}, {"reference": {"vendorCode": "15001630", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183735", "skuId": 77279385, "bizVendorCode": "SD8601"}}, {"reference": {"vendorCode": "83386", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117466", "skuId": 68640391, "bizVendorCode": "SD5613"}}, {"reference": {"vendorCode": "15004152", "vehicleCode": "0", "packageType": 1, "pStoreCode": "281261", "skuId": 61432785, "bizVendorCode": "SD11257"}}, {"reference": {"vendorCode": "15004707", "vehicleCode": "0", "packageType": 0, "pStoreCode": "327538", "skuId": 77291068, "bizVendorCode": "SD11849"}}, {"reference": {"vendorCode": "15002723", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193183", "skuId": 61060560, "bizVendorCode": "SD9747"}}, {"reference": {"vendorCode": "76665", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114573", "skuId": 2777497, "bizVendorCode": "SD4724"}}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "周三福利日", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup1344", "labelCode": "3743", "amountTitle": "已减43", "groupId": 1}, "vehicleCode": "5368", "highestPrice": 800, "rCoup": 0, "minDPrice": 217, "pWay": "可选：携程租车中心取车", "vehicleKey": "0_5368_", "hot": 0, "minTPrice": 379, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 19, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3987_0_107059_107059"], "introduce": "当前车型最低价"}, "minDOrinPrice": 238, "isEasy": true, "isCredit": true, "maximumCommentCount": 52247, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 48, "minTOrinPrice": 422, "logicIndex": 16, "isGroup": false, "renderUniqId": "5368_48__379_217_238_信用免押_easyLife_周三福利日已减43"}], "groupCode": "all", "dailyPrice": 51, "hasResult": true}, {"sortNum": 0, "groupName": "无忧租一口价", "hasResult": true, "groupCode": "prep", "dailyPrice": 219, "groupAction": 1}, {"sortNum": 1, "groupName": "经济轿车", "hasResult": true, "groupCode": "2", "dailyPrice": 51, "groupAction": 0}, {"sortNum": 3, "groupName": "新能源", "hasResult": true, "groupCode": "newenergy", "dailyPrice": 51, "groupAction": 0}, {"sortNum": 4, "groupName": "舒适轿车", "hasResult": true, "groupCode": "3", "dailyPrice": 96, "groupAction": 0}, {"sortNum": 5, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 80, "groupAction": 0}, {"sortNum": 6, "groupName": "商务车", "hasResult": true, "groupCode": "4", "dailyPrice": 115, "groupAction": 0}, {"sortNum": 7, "groupName": "豪华轿车", "hasResult": true, "groupCode": "5", "dailyPrice": 217, "groupAction": 0}, {"sortNum": 8, "groupName": "跑车", "hasResult": true, "groupCode": "9", "dailyPrice": 217, "groupAction": 0}, {"sortNum": 11, "groupName": "皮卡", "hasResult": true, "groupCode": "10", "dailyPrice": 429, "groupAction": 0}], "resBodySize": 218779, "frontTraceInfo": {"vehicleGroupMap": {"2": 72, "3": 69, "4": 97, "5": 74, "6": 218, "9": 99, "10": 3, "prep": 31, "newenergy": 200}, "normalCount": 5420, "priceCount": 5420, "vehicleList": ["18010", "18495", "51087", "89216", "87158", "4722", "4602", "4845", "18021", "4980", "17298", "4861", "4740", "2438", "2437", "2799", "1466", "2796", "2794", "4972", "4610", "4612", "89685", "5704", "2439", "18236", "18116", "4870", "18474", "4750", "4630", "17380", "10634", "2448", "1474", "4741", "87179", "10631", "4866", "10630", "18118", "4626", "18488", "18004", "17394", "18243", "4761", "4883", "17390", "17391", "2461", "2460", "1491", "1129", "5607", "2459", "1488", "1124", "4752", "4511", "5601", "4515", "5604", "4758", "4879", "4516", "17366", "89251", "18572", "4771", "90126", "17480", "1140", "10615", "2227", "2224", "831", "1133", "712", "4763", "89139", "4644", "88047", "4647", "4526", "20301", "17499", "17378", "4781", "4660", "4782", "4661", "17373", "18100", "4663", "2484", "1150", "10623", "1149", "1147", "2477", "5500", "4654", "5502", "5503", "10620", "4899", "5504", "10622", "4659", "608", "10621", "609", "17464", "17344", "4790", "4670", "3101", "3460", "4309", "2247", "1399", "5632", "5512", "4544", "17468", "17589", "4306", "5517", "88054", "5530", "5410", "1176", "3473", "2384", "860", "3469", "5402", "4676", "5524", "868", "5526", "88299", "4559", "5407", "5649", "4319", "4330", "18091", "873", "89979", "874", "1178", "2387", "4325", "5658", "4449", "4101", "5311", "5433", "4102", "1073", "1069", "5303", "4577", "648", "5306", "5427", "4470", "4593", "5320", "4231", "4112", "5564", "5444", "5686", "5565", "1087", "3262", "1083", "2050", "1081", "18071", "530", "652", "775", "88789", "656", "4103", "5555", "536", "85156", "658", "87578", "4588", "4106", "4107", "4228", "5439", "4108", "87571", "5319", "5570", "4482", "5572", "5694", "5453", "5574", "5332", "5696", "5333", "5576", "540", "300", "51276", "301", "86479", "51038", "4598", "5325", "85023", "4478", "5204", "4358", "4117", "5569", "4238", "88530", "5329", "86472", "4372", "4251", "4010", "5584", "4253", "5342", "4496", "5101", "5223", "4498", "5466", "5587", "3164", "51283", "673", "89419", "312", "3159", "555", "315", "5214", "5698", "679", "4246", "4367", "85177", "4489", "5579", "88684", "5591", "4261", "5472", "4020", "5351", "6320", "5594", "5595", "5474", "5353", "18065", "5596", "5475", "5113", "4024", "6324", "6323", "4025", "3176", "18063", "18064", "5590", "682", "3169", "326", "5104", "4378", "5226", "329", "4139", "5349", "5107", "6316", "5360", "20357", "5482", "3180", "5241", "4031", "5484", "6333", "5486", "5245", "5004", "5367", "4036", "452", "333", "456", "214", "51066", "3059", "216", "4026", "218", "85198", "5371", "6340", "5493", "5010", "6342", "5253", "4043", "5374", "6344", "4165", "5254", "5497", "5498", "4288", "5499", "3072", "340", "341", "51199", "587", "468", "589", "347", "5368", "5126", "6336", "5007", "5128", "5249", "6338", "86395", "5383", "5141", "5142", "4053", "17962", "5022", "5145", "5388", "471", "350", "474", "475", "476", "17725", "18936", "4049", "5138", "17727", "17964", "17602", "17965", "5151", "4062", "17970", "5272", "17972", "5396", "4066", "5277", "5158", "5270", "86738", "88915", "86857", "17619", "247", "248", "127", "87942", "17736", "85520", "17978", "17616", "17732", "17614", "5283", "5163", "5285", "5044", "26", "5167", "6258", "5289", "28", "5047", "250", "4191", "5281", "5282", "4193", "136", "137", "17827", "17941", "17700", "17943", "17944", "33", "5294", "5173", "5053", "5295", "4086", "5175", "5176", "17951", "4088", "5298", "5291", "5050", "265", "88817", "267", "17718", "17719", "85786", "17956", "17835", "84694", "17837", "86990", "17831", "40", "51437", "17712", "5063", "6274", "6276", "6275", "5187", "5067", "5068", "49", "96468", "6270", "6272", "6271", "4094", "2900", "86668", "279", "17929", "87998", "2905", "2904", "17801", "17922", "2901", "5074", "5191", "6281", "5071", "5193", "5194", "286", "166", "1820", "288", "289", "17817", "17819", "87620", "17931", "17811", "17812", "65", "6296", "6297", "290", "292", "172", "173", "6294", "174", "175", "296", "2801", "88509", "2800", "177", "1830", "17906", "86444", "2808", "73", "75", "182", "183", "184", "85226", "86675", "88", "17443", "90168", "17440", "17561", "17441", "2706", "95", "17567", "2705", "2704", "97", "98", "17453", "89286", "17696", "18544", "18782", "81327", "81320", "17578", "17421", "84988", "2722", "2721", "4907", "17307", "17308", "2729", "17424", "17425", "50975", "18999", "17427", "17306", "17554", "17555", "17550", "17430", "1404", "4916", "81348", "2731", "1400", "50982", "17556", "17678", "1407", "17437", "18768", "1406", "1405", "17640", "96748", "17764", "96503", "4805", "4928", "4808", "51724", "17647", "4803", "17405", "17651", "17652", "17774", "17412", "17650", "4936", "3603", "4816", "2874", "17655", "17413", "4932", "17414", "17898", "18747", "17779", "17416", "17863", "1437", "2889", "4826", "2888", "81278", "18957", "81152", "4940", "17507", "17628", "4943", "17622", "4701", "17865", "4946", "17630", "17631", "17753", "4960", "1450", "4958", "4716", "4959", "1446", "1445", "3502", "1444", "81145", "2895", "1442", "86710", "2893", "4950", "17639", "4831", "51633", "4953", "4832", "17996", "4834", "84890", "17514", "18845", "1449"], "easyLifeCount": 0, "zhimaCount": 5414, "vendorNames": ["丰田海南出行", "小飞侠租车", "凯美租车", "众横租车", "天驹租车", "六六出行", "海南松舍租车", "小简出行", "安迪出行", "普信租车", "邦尼租车", "树德出行", "博腾出行", "方达租车", "泰信吉租车", "租租侠租车", "八骏马租车", "海南蚂蚁出游租车", "德普租车", "鹏威租车", "琼驰租车", "三亚旅途中租车", "桐叶租车", "清晨租车", "枫叶租车", "东星租车", "怪兽超跑出行", "海南大脸猫租车", "牛牛出行", "木沐租车", "钧通租车", "鑫路达出行", "海途租车", "大麦超跑俱乐部", "壹优租车", "金晟租车", "皓阳租车", "EVCARD租车", "锋达租车", "五行租车", "三亚智衡租车", "祥成租车", "海南顺强租车", "乐享好车出行", "铭轩租车", "优享旅途租车", "三亚奇迹租车", "三亚五二零租车", "日之星丰田租车", "旭辰租车", "西十出行", "海立达租车", "通耀租车", "爱信出行", "车游天下租车", "海南金域租车", "助旅租车", "宝嘉租车", "三亚启航租车", "途新租车", "海南窝德租车", "一嗨租车", "耀东方租车", "金达莱租车", "银天租车", "北蒙租车", "安米租车", "凯福华租车", "礼享智行租车", "瀚程租车", "利资租车", "阿纳迪跑车俱乐部出行", "海南中进租车", "鸿韵出行", "资傲租车", "名仕租车", "汪澜租车", "毛蛋租车", "黑娃租车", "三亚新概念租车", "豫海租车", "诚亚租车", "铭途租车", "骑仕租车", "卢米租车", "钰鑫租车", "轩宇租车", "田世租车", "海南点赞租车", "宇航租车", "盛京出行", "易代步租车", "汇驰租车", "一路平安租车", "漫自由租车", "东辉租车", "懒人行租车", "盛泽租车", "文东租车", "赛富德租车", "虫子邦租车", "圣唯安租车", "铭鸿租车", "港梦超跑俱乐部租车", "鲁运昌通出行", "明昊租车", "驰鑫租车", "祥驰租车", "琼城租车", "领客出行", "北方出行", "三亚铭盛租车", "三亚世纪联合租车", "三亚鼎豪租车", "龙麟租车", "马达租车", "摇钱树出行", "陇鑫租车", "友嘉租车", "顺椰出行", "三亚融泽租车", "欣博祥租车", "联动出行", "捷安利达租车", "彩车坊租车", "定格租车", "皓轩跑车俱乐部租车 ", "锐冠租车", "纳贝拉租车", "逍遥租车", "盛兴隆租车", "无忧九州租车", "小龙人租车"]}, "labelCodes": ["3563", "3510", "3872", "3696", "3653", "3697", "3698", "3731", "3810", "3679", "4229", "3757", "3779", "4243", "4222", "4201", "3495", "3548", "3504", "3503", "3827", "3547", "3502", "3828", "3501", "3709", "4236", "3743", "3509", "3788", "3866", "3789", "3746"], "quickFilter": [{"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "1", "itemCode": "Vendor_0", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "携程租车中心"}, {"sortNum": 4, "selectedIcon": "https://dimg04.c-ctrip.com/images/0416b12000as15ayy5F24.png", "groupCode": "Promotion", "quickSortNum": 1, "positionCode": "2", "itemCode": "Promotion_3743", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "周三福利日", "icon": "https://dimg04.c-ctrip.com/images/0413q12000as152gsB1F5.png"}, {"sortNum": 1, "groupCode": "SelfService", "quickSortNum": 1, "positionCode": "3", "itemCode": "SelfService_Support", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 6, "groupCode": "StoreService", "quickSortNum": 2, "positionCode": "4", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "mark": "hot", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "positionCode": "6", "name": "携程优选", "icon": ""}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "8", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "1年内车龄"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "9", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "10", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "12", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 5, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "13", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}], "promotMap": {}, "requestInfo": {"rLongitude": 109.41235, "rDate": "20250124183000", "age": 30, "pCityId": 43, "returnDate": "/Date(1737714600000+0800)/", "sourceCountryId": 1, "pLatitude": 18.30767, "rLatitude": 18.30767, "pLongitude": 109.41235, "pDate": "20250122193000", "rCityId": 43, "pickupLocationName": "凤凰国际机场-T1航站楼", "returnLocationName": "凤凰国际机场-T1航站楼", "pickupDate": "/Date(1737545400000+0800)/"}, "allVehicleCount": 663, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "id": 2, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "23:59", "lat": "18.308537", "lng": "109.413536", "name": "三亚凤凰机场店", "fromTime": "00:00"}, "checkResponseTime": 1736940529667.0132, "vehicleList": [{"skylight": "部分车辆支持", "vehicleKey": "0_4139_", "luggageNo": 5, "carPlay": "部分车辆支持原厂互联/映射/CarLife/CarPlay", "displacement": "1.0T-1.5L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "6901044:0;72913632:0;28097714:0;76069855:0;1965861:0;6900280:0;152539:0;227977:0;1913476:0;1860920:0;30541211:0;46010546:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV5i12000c7kmh93066A.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "雪佛兰科沃兹", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/Type-C/SD", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "6901044:1;72913632:1;28097714:0;76069855:1;1965861:0;6900280:1;152539:1;227977:1;1913476:1;1860920:1;30541211:1;46010546:1;"}, "carPhone": true, "vehicleCode": "4139", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "雪佛兰科沃兹", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0612000d5pibur948E.png", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "6901044:1;72913632:1;28097714:1;76069855:1;1965861:1;6900280:1;152539:1;227977:1;1913476:1;1860920:1;30541211:1;46010546:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5212000c7mwubp7B39.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0l12000c7mwy55EE30.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000c7mwvvc7936.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3212000c7mx6u22402.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3012000c7mwv0sCA00.jpg?mark=yiche"], "transmissionType": 1, "brandName": "雪佛兰", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "雪佛兰", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "71", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5283_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "59149679:0;44414147:0;24651460:0;60938529:0;57029582:0;4545504:0;1971758:0;78063322:0;66213533:0;66609131:0;54528734:0;24652881:0;6901328:0;62729577:0;65943725:0;57196014:0;6902505:0;1859332:0;25608157:0;24587536:0;62801656:0;1858109:0;65353709:0;61060532:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV1x12000cf2440k87D7.jpg?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=4037&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "59149679:1;44414147:1;24651460:1;60938529:1;57029582:1;4545504:1;1971758:1;78063322:1;66213533:1;66609131:1;54528734:1;24652881:1;6901328:1;62729577:1;65943725:0;57196014:1;6902505:0;1859332:1;25608157:1;24587536:1;62801656:1;1858109:1;65353709:1;61060532:1;"}, "carPhone": true, "vehicleCode": "5283", "style": "2021款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1x12000cf2440k87D7.jpg?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "59149679:1;44414147:1;24651460:1;60938529:1;57029582:1;4545504:1;1971758:1;78063322:1;66213533:1;66609131:1;54528734:1;24652881:1;6901328:1;62729577:1;65943725:0;57196014:1;6902505:1;1859332:1;25608157:1;24587536:1;62801656:1;1858109:1;65353709:1;61060532:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5v12000c5rmv0xFB9E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5s12000c5rmrii54A5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5912000c5rmwv3E006.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4c12000c5rn4791853.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3i12000c5rmwvf2837.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5524_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "6903945:0;24651467:0;60938536:0;57029575:0;1860337:0;77304242:0;69187322:0;44386742:0;62840051:0;43673561:0;66594739:0;58528513:0;24652874:0;68738251:0;6901330:0;66217152:0;24653539:0;7358156:0;6902509:0;76384239:0;25009720:0;25608584:0;62724012:0;62801768:0;54883690:0;55333216:0;66149637:0;2317188:0;66211839:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV2d12000eti26ywD966.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=6912&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "6903945:1;24651467:1;60938536:1;57029575:1;1860337:1;77304242:1;69187322:1;44386742:1;62840051:1;43673561:1;66594739:1;58528513:1;24652874:1;68738251:1;6901330:1;66217152:1;24653539:1;7358156:1;6902509:0;76384239:1;25009720:1;25608584:1;62724012:1;62801768:1;54883690:1;55333216:1;66149637:1;2317188:1;66211839:1;"}, "carPhone": true, "vehicleCode": "5524", "style": "2022款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2d12000eti26ywD966.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "6903945:1;24651467:1;60938536:1;57029575:1;1860337:1;77304242:1;69187322:1;44386742:1;62840051:1;43673561:1;66594739:1;58528513:1;24652874:1;68738251:1;6901330:1;66217152:1;24653539:1;7358156:1;6902509:1;76384239:1;25009720:1;25608584:1;62724012:1;62801768:1;54883690:1;55333216:1;66149637:1;2317188:1;66211839:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5a12000cl0wt2062F4.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0n12000cl0wwqdE31E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0q12000cl0wqwv4BB1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6812000cl0wjx68EE4.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0e12000cl0wu6t7271.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_4663_", "luggageNo": 4, "carPlay": "不支持", "displacement": "2.4L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "55172454:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV1a12000etgoh8a9616.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "55172454:1;"}, "carPhone": false, "vehicleCode": "4663", "style": "2015款及以前", "carPhoneDesc": {"type": 0, "typeDesc": "不支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3312000chsx6nf2A98.jpg?mark=yiche", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放4个24寸行李箱", "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "55172454:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5k12000chsx38q8BA9.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6612000chswzfq875E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6n12000chsx6nnE676.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0112000chsxahk5DBD.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3412000chswz0nC16E.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "不支持", "vehicleKey": "0_17391_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "43757708:0;59149497:0;7712412:0;24651446:0;60938557:0;57011641:0;28097917:0;77140855:0;3868574:0;64711921:0;8549451:0;45603986:0;68539115:0;62812394:0;62721149:0;63094592:0;58528527:0;54528776:0;24652867:0;68640475:0;6901332:0;24653511:0;4684110:0;69864488:0;25569188:0;65083838:0;62717488:0;62920509:0;76388866:0;7597906:0;56713315:0;78099057:0;51441748:0;6902510:0;76276075:0;77349784:0;73772546:0;6842937:0;75764970:0;25567970:0;54499544:0;30574034:0;70449100:0;62802062:0;54883718:0;55333209:0;23720040:0;65947393:0;65947400:0;77351660:0;2317191:0;63076833:0;77107031:0;14001464:0;77279406:0;62932290:0;52816478:0;51441748:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV0m12000c563xuyA0D0.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=8202&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "43757708:1;59149497:1;7712412:1;24651446:1;60938557:1;57011641:1;28097917:0;77140855:0;3868574:1;64711921:1;8549451:1;45603986:1;68539115:1;62812394:1;62721149:1;63094592:1;58528527:1;54528776:1;24652867:1;68640475:1;6901332:1;24653511:1;4684110:1;69864488:1;25569188:1;65083838:1;62717488:1;62920509:1;76388866:1;7597906:1;56713315:1;78099057:1;51441748:1;6902510:0;76276075:1;77349784:1;73772546:1;6842937:1;75764970:1;25567970:1;54499544:1;30574034:0;70449100:0;62802062:1;54883718:1;55333209:1;23720040:1;65947393:1;65947400:1;77351660:1;2317191:1;63076833:1;77107031:1;14001464:0;77279406:1;62932290:1;52816478:1;51441748:1;"}, "carPhone": true, "vehicleCode": "17391", "style": "2023款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3e12000b76wz3d998E.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "43757708:1;59149497:1;7712412:1;24651446:1;60938557:1;57011641:1;28097917:1;77140855:1;3868574:1;64711921:1;8549451:1;45603986:1;68539115:1;62812394:1;62721149:1;63094592:1;58528527:1;54528776:1;24652867:1;68640475:1;6901332:1;24653511:1;4684110:1;69864488:1;25569188:1;65083838:1;62717488:1;62920509:1;76388866:1;7597906:1;56713315:1;78099057:1;51441748:1;6902510:1;76276075:1;77349784:1;73772546:1;6842937:1;75764970:1;25567970:1;54499544:1;30574034:1;70449100:0;62802062:1;54883718:1;55333209:1;23720040:1;65947393:1;65947400:1;77351660:1;2317191:1;63076833:1;77107031:1;14001464:1;77279406:1;62932290:1;52816478:1;51441748:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6c12000c6e72r9C1EE.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1y12000c6e73t52597.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3912000c6e75un16B1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6112000c6e780q9AF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3y12000c6e71tgC9D9.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "不支持", "vehicleKey": "2_17391_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "43757708:0;59149497:0;7712412:0;24651446:0;60938557:0;57011641:0;28097917:0;77140855:0;3868574:0;64711921:0;8549451:0;45603986:0;68539115:0;62812394:0;62721149:0;63094592:0;58528527:0;54528776:0;24652867:0;68640475:0;6901332:0;24653511:0;4684110:0;69864488:0;25569188:0;65083838:0;62717488:0;62920509:0;76388866:0;7597906:0;56713315:0;78099057:0;51441748:0;6902510:0;76276075:0;77349784:0;73772546:0;6842937:0;75764970:0;25567970:0;54499544:0;30574034:0;70449100:0;62802062:0;54883718:0;55333209:0;23720040:0;65947393:0;65947400:0;77351660:0;2317191:0;63076833:0;77107031:0;14001464:0;77279406:0;62932290:0;52816478:0;51441748:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV0m12000c563xuyA0D0.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=8202&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "43757708:1;59149497:1;7712412:1;24651446:1;60938557:1;57011641:1;28097917:0;77140855:0;3868574:1;64711921:1;8549451:1;45603986:1;68539115:1;62812394:1;62721149:1;63094592:1;58528527:1;54528776:1;24652867:1;68640475:1;6901332:1;24653511:1;4684110:1;69864488:1;25569188:1;65083838:1;62717488:1;62920509:1;76388866:1;7597906:1;56713315:1;78099057:1;51441748:1;6902510:0;76276075:1;77349784:1;73772546:1;6842937:1;75764970:1;25567970:1;54499544:1;30574034:0;70449100:0;62802062:1;54883718:1;55333209:1;23720040:1;65947393:1;65947400:1;77351660:1;2317191:1;63076833:1;77107031:1;14001464:0;77279406:1;62932290:1;52816478:1;51441748:1;"}, "carPhone": true, "vehicleCode": "17391", "style": "2023款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3e12000b76wz3d998E.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": true, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "43757708:1;59149497:1;7712412:1;24651446:1;60938557:1;57011641:1;28097917:1;77140855:1;3868574:1;64711921:1;8549451:1;45603986:1;68539115:1;62812394:1;62721149:1;63094592:1;58528527:1;54528776:1;24652867:1;68640475:1;6901332:1;24653511:1;4684110:1;69864488:1;25569188:1;65083838:1;62717488:1;62920509:1;76388866:1;7597906:1;56713315:1;78099057:1;51441748:1;6902510:1;76276075:1;77349784:1;73772546:1;6842937:1;75764970:1;25567970:1;54499544:1;30574034:1;70449100:0;62802062:1;54883718:1;55333209:1;23720040:1;65947393:1;65947400:1;77351660:1;2317191:1;63076833:1;77107031:1;14001464:1;77279406:1;62932290:1;52816478:1;51441748:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6c12000c6e72r9C1EE.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1y12000c6e73t52597.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3912000c6e75un16B1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6112000c6e780q9AF7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3y12000c6e71tgC9D9.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5282_", "luggageNo": 5, "carPlay": "支持CarLife/CarPlay", "displacement": "2.0T", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "43951041:0;24651453:0;60938508:0;57029589:0;66620443:0;24652888:0;6901326:0;6902506:0;25608178:0;44060318:0;78113932:0;6900297:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV4h12000eth16tnAF19.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3460&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "43951041:1;24651453:1;60938508:1;57029589:1;66620443:1;24652888:1;6901326:1;6902506:0;25608178:1;44060318:1;78113932:1;6900297:1;"}, "carPhone": true, "vehicleCode": "5282", "style": "2020款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4h12000eth16tnAF19.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "43951041:1;24651453:1;60938508:1;57029589:1;66620443:1;24652888:1;6901326:1;6902506:1;25608178:1;44060318:1;78113932:0;6900297:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6g12000c5rmc6b2AB5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1c12000c5rmmb77F7C.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3k12000c5rmazp9E54.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3p12000c5rmm3a4ED6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1q12000c5rmd3r2879.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_17561_", "luggageNo": 5, "carPlay": "支持CarPlay", "displacement": "2.0T-2.5L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "24652895:0;68748135:0;55172048:0;44383704:0;6900320:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV4512000elvdljb183C.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=675&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "24652895:1;68748135:1;55172048:1;44383704:1;6900320:0;"}, "carPhone": true, "vehicleCode": "17561", "style": "2017/18款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4512000elvdljb183C.png?mark=yiche", "fuel": "95号或92号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "24652895:1;68748135:1;55172048:1;44383704:1;6900320:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0o12000c6eaivoD4D0.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2412000c6eahws4567.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2f12000c6eakzuA728.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1n12000c6eaj9265BC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0f12000c6eacfx9063.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "subGroupCode": "newenergy", "luggageNo": 5, "carPlay": "不支持", "vehicleKey": "0_17622_", "autoPark": true, "endurance": "工信部续航170km-240km", "fuelType": "增程式", "charge": "快充0.5小时,慢充7.9小时", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "43960141:0;58601593:0;44536297:0;44348340:0;47765684:0;56960247:0;59106839:0;51804509:0;24652307:0;24649409:0;77983067:0;54498515:0;54876347:0;63078877:0;57122164:0;66065875:0;29208691:0;68649190:0;61433590:0;45470118:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV1p12000c56d4bk1244.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "理想L7", "doorNo": 5, "autoParkDesc": {"type": 1, "typeDesc": "支持"}, "driveMode": "双电机四驱", "chargeInterface": "支持Type-C", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "43960141:1;58601593:1;44536297:1;44348340:1;47765684:1;56960247:1;59106839:1;51804509:1;24652307:1;24649409:1;77983067:1;54498515:1;54876347:1;63078877:1;57122164:1;66065875:1;29208691:1;68649190:1;61433590:1;45470118:1;"}, "carPhone": true, "vehicleCode": "17622", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "理想L7", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4w12000cj9sakkFCD1.png?mark=yiche", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "43960141:1;58601593:1;44536297:1;44348340:1;47765684:1;56960247:1;59106839:1;51804509:1;24652307:1;24649409:1;77983067:1;54498515:1;54876347:1;63078877:1;57122164:1;66065875:1;29208691:1;68649190:1;61433590:1;45470118:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3d12000cj9sbdi81F9.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1q12000cj9s32sAAC2.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5312000cj9ry62C6DC.png?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4t12000cj9rzhs85EC.png?mark=yiche"], "transmissionType": 1, "brandName": "理想汽车", "oilType": 4, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "理想汽车", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "11", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_17390_", "luggageNo": 0, "carPlay": "部分车辆支持CarPlay/CarLife", "displacement": "2.0T", "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "25715985:0;43671755:0;59147236:0;6903946:0;24650984:0;59109597:0;48049863:0;57029519:0;28097973:0;6390411:0;6802397:0;4992774:0;6825645:0;4468419:0;45008636:0;65943046:0;48039713:0;77082069:0;58064168:0;63094578:0;58528639:0;56543719:0;24652832:0;68640454:0;25569195:0;6901331:0;6902511:0;27512325:0;61434136:0;73772273:0;65083747:0;72785280:0;25567949:0;7334062:0;69856634:0;24587795:0;69762246:0;75764998:0;71125342:0;72975519:0;44060346:0;62801796:0;65361815:0;55333258:0;75773762:0;63082601:0;62153687:0;54883753:0;75240411:0;65083747:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV3012000elv63aiFD76.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=9309&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "25715985:1;43671755:1;59147236:1;6903946:1;24650984:1;59109597:1;48049863:1;57029519:1;28097973:1;6390411:1;6802397:1;4992774:1;6825645:1;4468419:1;45008636:1;65943046:1;48039713:1;77082069:1;58064168:1;63094578:1;58528639:1;56543719:1;24652832:1;68640454:1;25569195:1;6901331:1;6902511:1;27512325:1;61434136:1;73772273:1;65083747:1;72785280:1;25567949:1;7334062:1;69856634:1;24587795:1;69762246:1;75764998:1;71125342:1;72975519:1;44060346:1;62801796:1;65361815:0;55333258:1;75773762:1;63082601:1;62153687:1;54883753:1;75240411:1;65083747:1;"}, "vehicleCode": "17390", "style": "2023款", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3012000elv63aiFD76.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "25715985:1;43671755:1;59147236:1;6903946:1;24650984:1;59109597:1;48049863:1;57029519:1;28097973:1;6390411:1;6802397:1;4992774:1;6825645:1;4468419:1;45008636:1;65943046:1;48039713:1;77082069:0;58064168:1;63094578:1;58528639:1;56543719:1;24652832:1;68640454:1;25569195:1;6901331:1;6902511:1;27512325:1;61434136:1;73772273:1;65083747:1;72785280:1;25567949:1;7334062:1;69856634:1;24587795:1;69762246:1;75764998:1;71125342:1;72975519:1;44060346:1;62801796:1;65361815:0;55333258:1;75773762:1;63082601:1;62153687:1;54883753:1;75240411:1;65083747:1;"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5512000c6dz8rnD05D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0y12000c6dz4jpEC37.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4m12000c6dz86b6546.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3u12000c6dz6z26A9B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3t12000c6dz6yl7473.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "部分车辆支持全速自适应巡航/定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "2_17390_", "luggageNo": 0, "carPlay": "部分车辆支持CarPlay/CarLife", "displacement": "2.0T", "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "25715985:0;43671755:0;59147236:0;6903946:0;24650984:0;59109597:0;48049863:0;57029519:0;28097973:0;6390411:0;6802397:0;4992774:0;6825645:0;4468419:0;45008636:0;65943046:0;48039713:0;77082069:0;58064168:0;63094578:0;58528639:0;56543719:0;24652832:0;68640454:0;25569195:0;6901331:0;6902511:0;27512325:0;61434136:0;73772273:0;65083747:0;72785280:0;25567949:0;7334062:0;69856634:0;24587795:0;69762246:0;75764998:0;71125342:0;72975519:0;44060346:0;62801796:0;65361815:0;55333258:0;75773762:0;63082601:0;62153687:0;54883753:0;75240411:0;65083747:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV3012000elv63aiFD76.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=9309&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "25715985:1;43671755:1;59147236:1;6903946:1;24650984:1;59109597:1;48049863:1;57029519:1;28097973:1;6390411:1;6802397:1;4992774:1;6825645:1;4468419:1;45008636:1;65943046:1;48039713:1;77082069:1;58064168:1;63094578:1;58528639:1;56543719:1;24652832:1;68640454:1;25569195:1;6901331:1;6902511:1;27512325:1;61434136:1;73772273:1;65083747:1;72785280:1;25567949:1;7334062:1;69856634:1;24587795:1;69762246:1;75764998:1;71125342:1;72975519:1;44060346:1;62801796:1;65361815:0;55333258:1;75773762:1;63082601:1;62153687:1;54883753:1;75240411:1;65083747:1;"}, "vehicleCode": "17390", "style": "2023款", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3012000elv63aiFD76.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "25715985:1;43671755:1;59147236:1;6903946:1;24650984:1;59109597:1;48049863:1;57029519:1;28097973:1;6390411:1;6802397:1;4992774:1;6825645:1;4468419:1;45008636:1;65943046:1;48039713:1;77082069:0;58064168:1;63094578:1;58528639:1;56543719:1;24652832:1;68640454:1;25569195:1;6901331:1;6902511:1;27512325:1;61434136:1;73772273:1;65083747:1;72785280:1;25567949:1;7334062:1;69856634:1;24587795:1;69762246:1;75764998:1;71125342:1;72975519:1;44060346:1;62801796:1;65361815:0;55333258:1;75773762:1;63082601:1;62153687:1;54883753:1;75240411:1;65083747:1;"}, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5512000c6dz8rnD05D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0y12000c6dz4jpEC37.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4m12000c6dz86b6546.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3u12000c6dz6z26A9B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3t12000c6dz6yl7473.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "部分车辆支持全速自适应巡航/定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_5281_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "59149686:0;43951027:0;24651544:0;48049898:0;57029561:0;66615340:0;24652853:0;6901324:0;6902507:0;25608360:0;76178579:0;61060469:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV6112000eth0lxpAD64.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3460&app_ver=10.5", "reverseImage": {"type": 1, "typeDesc": "支持", "description": "59149686:1;43951027:1;24651544:1;48049898:1;57029561:1;66615340:1;24652853:1;6901324:1;6902507:1;25608360:1;76178579:1;61060469:1;"}, "carPhone": true, "vehicleCode": "5281", "style": "2020款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0d12000ap2ve880922.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "59149686:1;43951027:1;24651544:1;48049898:1;57029561:1;66615340:1;24652853:1;6901324:1;6902507:1;25608360:1;76178579:1;61060469:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0f12000c5rm0waEB81.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3r12000c5rm2o7C1DD.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1512000c5rm5bg6504.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4p12000c5rm4s775C2.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1112000c5rlvikC8CD.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "支持全速自适应巡航/定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_5526_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "59147257:0;6903934:0;24651537:0;48049870:0;57029547:0;44386581:0;47764662:0;43673484:0;66588558:0;52257052:0;58528541:0;24652839:0;68640461:0;25569160:0;6901329:0;6902508:0;66217264:0;24653546:0;4883108:0;75764963:0;25567963:0;7362307:0;25009762:0;62709935:0;30574104:0;62801782:0;54883662:0;55333244:0;71126231:0;63085422:0;61060476:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV3u12000enkna1v6DF7.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=6990&app_ver=10.5", "reverseImage": {"type": 1, "typeDesc": "支持", "description": "59147257:1;6903934:1;24651537:1;48049870:1;57029547:1;44386581:1;47764662:1;43673484:1;66588558:1;52257052:1;58528541:1;24652839:1;68640461:1;25569160:1;6901329:1;6902508:1;66217264:1;24653546:1;4883108:1;75764963:1;25567963:1;7362307:1;25009762:1;62709935:1;30574104:1;62801782:1;54883662:1;55333244:1;71126231:1;63085422:1;61060476:1;"}, "carPhone": true, "vehicleCode": "5526", "style": "2022款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0y12000ba4zyqrB197.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "59147257:1;6903934:1;24651537:1;48049870:1;57029547:1;44386581:1;47764662:1;43673484:1;66588558:1;52257052:1;58528541:1;24652839:1;68640461:1;25569160:1;6901329:1;6902508:1;66217264:1;24653546:1;4883108:1;75764963:1;25567963:1;7362307:1;25009762:1;62709935:1;30574104:1;62801782:1;54883662:1;55333244:1;71126231:1;63085422:1;61060476:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3412000c6e2uzs65C6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000c6e2tbiDFA5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1t12000c6e2q84823E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4012000c6e2qr501F9.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000c6e2s5e4141.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "支持全速自适应巡航/定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_4660_", "luggageNo": 5, "carPlay": "支持CarPlay", "displacement": "2.0T", "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "59149490:0;1862272:0;69750794:0;24652860:0;68748128:0;42922860:0;55459888:0;24870826:0;77279434:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV1812000etgoh7fCF12.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=675&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "59149490:1;1862272:1;69750794:1;24652860:1;68748128:1;42922860:1;55459888:1;24870826:1;77279434:0;"}, "carPhone": true, "vehicleCode": "4660", "style": "2017/18款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1812000etgoh7fCF12.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "59149490:1;1862272:1;69750794:1;24652860:1;68748128:1;42922860:1;55459888:1;24870826:1;77279434:0;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5312000c6e6jqd2785.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4812000c6e6jqt5FEA.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1712000c6e6j7m794E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5m12000c6e6q8z4687.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6612000c6e6hg40B70.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "支持定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_5374_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "59147264:0;44414126:0;24651187:0;48049891:0;57029554:0;4443845:0;8328839:0;68738531:0;78063343:0;69748281:0;66603300:0;24652846:0;6901327:0;6902504:0;61433835:0;24653483:0;25608164:0;23289757:0;62801544:0;55333237:0;6900323:0;66211734:0;64697361:0;76178600:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV6w12000eth2x9eDF35.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4535&app_ver=10.5", "reverseImage": {"type": 1, "typeDesc": "支持", "description": "59147264:1;44414126:1;24651187:1;48049891:1;57029554:1;4443845:1;8328839:1;68738531:1;78063343:1;69748281:1;66603300:1;24652846:1;6901327:1;6902504:1;61433835:1;24653483:1;25608164:1;23289757:1;62801544:1;55333237:1;6900323:1;66211734:1;64697361:1;76178600:1;"}, "carPhone": true, "vehicleCode": "5374", "style": "2021款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0d12000ap2ve880922.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "59147264:1;44414126:1;24651187:1;48049891:1;57029554:1;4443845:1;8328839:1;68738531:1;78063343:0;69748281:1;66603300:1;24652846:1;6901327:1;6902504:1;61433835:1;24653483:1;25608164:1;23289757:1;62801544:1;55333237:1;6900323:1;66211734:1;64697361:1;76178600:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6h12000c5s4wm508D9.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5w12000c5s54t8F8BB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5s12000c5s50h32B2C.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6h12000c5s55i5ED78.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0312000c5s51x9F140.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "49", "guidSys": "支持定速巡航/自适应巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_4659_", "luggageNo": 5, "carPlay": "支持CarPlay", "displacement": "2.5L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "6903937:0;23031296:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV5p12000etgn9tpB3A8.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "6903937:1;23031296:1;"}, "carPhone": true, "vehicleCode": "4659", "style": "2017/18款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3c12000c6eb44lAB7C.jpg?mark=yiche", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "6903937:1;23031296:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3g12000c6eb2tfDCA3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3k12000c6eawz3F33E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0y12000c6eb0ghC333.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6w12000c6eb3df5EB1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3h12000c6eb26y9568.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "49", "guidSys": "支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_4661_", "luggageNo": 4, "carPlay": "不支持", "displacement": "2.4L", "autoPark": false, "charge": "", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "55163333:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV5r12000etgn2505C6F.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "55163333:1;"}, "carPhone": false, "vehicleCode": "4661", "style": "2015款及以前", "carPhoneDesc": {"type": 0, "typeDesc": "不支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4u12000cmlb1h5F574.png?mark=yiche", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放4个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "55163333:1;"}, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6712000chqtu49524B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4f12000chqu4e7C301.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0512000chqttg9BA0F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3p12000chqttmgF6EC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0t12000chqu1qa816D.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "49", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "不支持", "subGroupCode": "newenergy", "luggageNo": 2, "carPlay": "不支持", "vehicleKey": "0_17639_", "autoPark": false, "endurance": "工信部续航333km", "fuelType": "纯电动", "charge": "快充0.58小时,慢充9.5小时", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "52243164:0;6901062:0;72907010:0;23750371:0;24662408:0;65084118:0;47571756:0;41070198:0;6904013:0;51721923:0;74761310:0;58068032:0;69186769:0;47571756:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV3b12000c56dmyj20D3.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "五菱缤果", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "52243164:1;6901062:1;72907010:1;23750371:1;24662408:1;65084118:1;47571756:1;41070198:1;6904013:1;51721923:1;74761310:1;58068032:1;69186769:1;47571756:1;"}, "carPhone": true, "vehicleCode": "17639", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "五菱缤果", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4t12000c5v1bkb78D8.jpg?mark=yiche", "luggageNum": "可放2个24寸行李箱", "passengerNo": 4, "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "52243164:1;6901062:1;72907010:1;23750371:1;24662408:1;65084118:1;47571756:1;41070198:1;6904013:1;51721923:1;74761310:1;58068032:1;69186769:1;47571756:1;"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1e12000c5v1bme5FB5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6k12000c5v1h6j3FDB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2h12000c5v1imhEEFA.jpg?mark=yiche"], "transmissionType": 1, "brandName": "五菱汽车", "oilType": 5, "struct": "两厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "五菱汽车", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "68", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "不支持", "subGroupCode": "newenergy", "luggageNo": 2, "carPlay": "不支持", "vehicleKey": "2_17639_", "autoPark": false, "endurance": "工信部续航333km", "fuelType": "纯电动", "charge": "快充0.58小时,慢充9.5小时", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "52243164:0;6901062:0;72907010:0;23750371:0;24662408:0;65084118:0;47571756:0;41070198:0;6904013:0;51721923:0;74761310:0;58068032:0;69186769:0;47571756:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV3b12000c56dmyj20D3.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "五菱缤果", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB", "mediaTypes": [], "reverseImage": {"type": 1, "typeDesc": "支持", "description": "52243164:1;6901062:1;72907010:1;23750371:1;24662408:1;65084118:1;47571756:1;41070198:1;6904013:1;51721923:1;74761310:1;58068032:1;69186769:1;47571756:1;"}, "carPhone": true, "vehicleCode": "17639", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "五菱缤果", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4t12000c5v1bkb78D8.jpg?mark=yiche", "luggageNum": "可放2个24寸行李箱", "passengerNo": 4, "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "reverseSensor": {"type": 1, "typeDesc": "支持", "description": "52243164:1;6901062:1;72907010:1;23750371:1;24662408:1;65084118:1;47571756:1;41070198:1;6904013:1;51721923:1;74761310:1;58068032:1;69186769:1;47571756:1;"}, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1e12000c5v1bme5FB5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6k12000c5v1h6j3FDB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2h12000c5v1imhEEFA.jpg?mark=yiche"], "transmissionType": 1, "brandName": "五菱汽车", "oilType": 5, "struct": "两厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "五菱汽车", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "68", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "支持", "subGroupCode": "newenergy", "luggageNo": 5, "carPlay": "不支持", "vehicleKey": "0_5368_", "autoPark": false, "endurance": "工信部续航525km-688km", "fuelType": "纯电动", "charge": "快充1小时,慢充10小时", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "25715936:0;43671629:0;59087057:0;7679015:0;24651061:0;54505774:0;56962487:0;24662352:0;77900334:0;25224466:0;4537611:0;77351646:0;1914737:0;77982794:0;48047938:0;63078884:0;24652293:0;25569293:0;6901271:0;77349602:0;77468252:0;6902492:0;7358219:0;55144972:0;65083866:0;77083553:0;24587760:0;6900308:0;30574069:0;54499530:0;51725297:0;60698247:0;24653504:0;68416125:0;44060192:0;61516694:0;58528772:0;64697802:0;10336355:0;54883949:0;69942398:0;65944012:0;77279385:0;68640391:0;61432785:0;77291068:0;61060560:0;2777497:0;51725297:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV2612000couxzou3ADF.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "特斯拉Model Y", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "双电机四驱/后置后驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5703&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "25715936:1;43671629:1;59087057:1;7679015:1;24651061:1;54505774:1;56962487:1;24662352:1;77900334:1;25224466:1;4537611:1;77351646:1;1914737:1;77982794:1;48047938:1;63078884:1;24652293:1;25569293:1;6901271:1;77349602:1;77468252:1;6902492:1;7358219:1;55144972:1;65083866:1;77083553:1;24587760:1;6900308:1;30574069:1;54499530:1;51725297:1;60698247:1;24653504:1;68416125:1;44060192:1;61516694:1;58528772:1;64697802:1;10336355:1;54883949:1;69942398:1;65944012:0;77279385:0;68640391:1;61432785:1;77291068:1;61060560:1;2777497:0;51725297:1;"}, "carPhone": true, "vehicleCode": "5368", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "特斯拉Model Y", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0n12000b76j6uj7FA4.jpg", "luggageNum": "可放5个24寸行李箱", "passengerNo": 5, "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "25715936:1;43671629:1;59087057:1;7679015:1;24651061:1;54505774:1;56962487:1;24662352:1;77900334:1;25224466:1;4537611:1;77351646:1;1914737:1;77982794:1;48047938:1;63078884:1;24652293:1;25569293:1;6901271:1;77349602:1;77468252:1;6902492:1;7358219:1;55144972:1;65083866:1;77083553:1;24587760:1;6900308:1;30574069:1;54499530:1;51725297:1;60698247:1;24653504:1;68416125:1;44060192:1;61516694:1;58528772:1;64697802:1;10336355:1;54883949:1;69942398:1;65944012:0;77279385:0;68640391:1;61432785:1;77291068:1;61060560:0;2777497:0;51725297:1;"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5q12000cduyfjg086F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0912000cduyi9cD6F8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2m12000cduyc1pC5A0.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3712000cduyi1u3AF6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000cduybbn27B0.jpg?mark=yiche"], "transmissionType": 1, "brandName": "特斯拉", "oilType": 5, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "特斯拉", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "12", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "subGroupCode": "newenergy", "luggageNo": 5, "carPlay": "不支持", "vehicleKey": "2_5368_", "autoPark": false, "endurance": "工信部续航525km-688km", "fuelType": "纯电动", "charge": "快充1小时,慢充10小时", "snowTyre": {"type": 0, "typeDesc": "不支持", "description": "25715936:0;43671629:0;59087057:0;7679015:0;24651061:0;54505774:0;56962487:0;24662352:0;77900334:0;25224466:0;4537611:0;77351646:0;1914737:0;77982794:0;48047938:0;63078884:0;24652293:0;25569293:0;6901271:0;77349602:0;77468252:0;6902492:0;7358219:0;55144972:0;65083866:0;77083553:0;24587760:0;6900308:0;30574069:0;54499530:0;51725297:0;60698247:0;24653504:0;68416125:0;44060192:0;61516694:0;58528772:0;64697802:0;10336355:0;54883949:0;69942398:0;65944012:0;77279385:0;68640391:0;61432785:0;77291068:0;61060560:0;2777497:0;51725297:0;"}, "imageList": ["https://dimg04.c-ctrip.com/images/0RV2612000couxzou3ADF.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "特斯拉Model Y", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "双电机四驱/后置后驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5703&app_ver=10.5", "reverseImage": {"type": 2, "typeDesc": "部分车辆支持", "description": "25715936:1;43671629:1;59087057:1;7679015:1;24651061:1;54505774:1;56962487:1;24662352:1;77900334:1;25224466:1;4537611:1;77351646:1;1914737:1;77982794:1;48047938:1;63078884:1;24652293:1;25569293:1;6901271:1;77349602:1;77468252:1;6902492:1;7358219:1;55144972:1;65083866:1;77083553:1;24587760:1;6900308:1;30574069:1;54499530:1;51725297:1;60698247:1;24653504:1;68416125:1;44060192:1;61516694:1;58528772:1;64697802:1;10336355:1;54883949:1;69942398:1;65944012:0;77279385:0;68640391:1;61432785:1;77291068:1;61060560:1;2777497:0;51725297:1;"}, "carPhone": true, "vehicleCode": "5368", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "特斯拉Model Y", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0n12000b76j6uj7FA4.jpg", "luggageNum": "可放5个24寸行李箱", "passengerNo": 5, "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "reverseSensor": {"type": 2, "typeDesc": "部分车辆支持", "description": "25715936:1;43671629:1;59087057:1;7679015:1;24651061:1;54505774:1;56962487:1;24662352:1;77900334:1;25224466:1;4537611:1;77351646:1;1914737:1;77982794:1;48047938:1;63078884:1;24652293:1;25569293:1;6901271:1;77349602:1;77468252:1;6902492:1;7358219:1;55144972:1;65083866:1;77083553:1;24587760:1;6900308:1;30574069:1;54499530:1;51725297:1;60698247:1;24653504:1;68416125:1;44060192:1;61516694:1;58528772:1;64697802:1;10336355:1;54883949:1;69942398:1;65944012:0;77279385:0;68640391:1;61432785:1;77291068:1;61060560:0;2777497:0;51725297:1;"}, "isHot": true, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5q12000cduyfjg086F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0912000cduyi9cD6F8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2m12000cduyc1pC5A0.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3712000cduyi1u3AF6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000cduybbn27B0.jpg?mark=yiche"], "transmissionType": 1, "brandName": "特斯拉", "oilType": 5, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "特斯拉", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "12", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}], "storeList": [{"pickOffLevel": 19366, "storeCode": "625283", "pickUpLevel": 19366}, {"pickOffLevel": 145906, "storeCode": "184270", "pickUpLevel": 145906}, {"pickOffLevel": 68967, "storeCode": "114886", "pickUpLevel": 68967}, {"pickOffLevel": 570606, "storeCode": "308155", "pickUpLevel": 570606}, {"pickOffLevel": 57202, "storeCode": "2285298", "pickUpLevel": 57202}, {"pickOffLevel": 63244, "storeCode": "117466", "pickUpLevel": 63244}, {"pickOffLevel": 61144, "storeCode": "116955", "pickUpLevel": 61144}, {"pickOffLevel": 178223, "storeCode": "188920", "pickUpLevel": 178223}, {"pickOffLevel": -1, "storeCode": "425573", "pickUpLevel": -1}, {"pickOffLevel": 916623, "storeCode": "327706", "pickUpLevel": 916623}, {"pickOffLevel": 126500, "storeCode": "176530", "pickUpLevel": 126500}, {"pickOffLevel": 701184, "storeCode": "313573", "pickUpLevel": 701184}, {"pickOffLevel": 47309, "storeCode": "114931", "pickUpLevel": 47309}, {"pickOffLevel": 1758877, "storeCode": "114573", "pickUpLevel": 1758877}, {"pickOffLevel": 1866131, "storeCode": "411510", "pickUpLevel": 1866131}, {"pickOffLevel": 774208, "storeCode": "322519", "pickUpLevel": 774208}, {"pickOffLevel": -1, "storeCode": "2396556", "pickUpLevel": -1}, {"pickOffLevel": 61561, "storeCode": "116982", "pickUpLevel": 61561}, {"pickOffLevel": -1, "storeCode": "138334", "pickUpLevel": -1}, {"pickOffLevel": 222918, "storeCode": "229176", "pickUpLevel": 222918}, {"pickOffLevel": 52406, "storeCode": "251283", "pickUpLevel": 52406}, {"pickOffLevel": 251691, "storeCode": "182413", "pickUpLevel": 251691}, {"pickOffLevel": 292, "storeCode": "183936", "pickUpLevel": 292}, {"pickOffLevel": 1753774, "storeCode": "1957775", "pickUpLevel": 1753774}, {"pickOffLevel": 2761, "storeCode": "78", "pickUpLevel": 2761}, {"pickOffLevel": 790777, "storeCode": "326061", "pickUpLevel": 790777}, {"pickOffLevel": 843669, "storeCode": "174846", "pickUpLevel": 843669}, {"pickOffLevel": 2648, "storeCode": "261024", "pickUpLevel": 2648}, {"pickOffLevel": 472151, "storeCode": "274541", "pickUpLevel": 472151}, {"pickOffLevel": 1217371, "storeCode": "265301", "pickUpLevel": 1217371}, {"pickOffLevel": 36744, "storeCode": "181671", "pickUpLevel": 36744}, {"pickOffLevel": 1135044, "storeCode": "175820", "pickUpLevel": 1135044}, {"pickOffLevel": 1969304, "storeCode": "2370159", "pickUpLevel": 1969304}, {"pickOffLevel": 1135023, "storeCode": "116645", "pickUpLevel": 1135023}, {"pickOffLevel": 56985, "storeCode": "116135", "pickUpLevel": 56985}, {"pickOffLevel": 872, "storeCode": "188084", "pickUpLevel": 872}, {"pickOffLevel": 620593, "storeCode": "312488", "pickUpLevel": 620593}, {"pickOffLevel": 1658, "storeCode": "193947", "pickUpLevel": 1658}, {"pickOffLevel": 50441, "storeCode": "115374", "pickUpLevel": 50441}, {"pickOffLevel": 2525, "storeCode": "182187", "pickUpLevel": 2525}, {"pickOffLevel": 212029, "storeCode": "193183", "pickUpLevel": 212029}, {"pickOffLevel": 26867, "storeCode": "107474", "pickUpLevel": 26867}, {"pickOffLevel": 88368, "storeCode": "106965", "pickUpLevel": 88368}, {"pickOffLevel": 143565, "storeCode": "183735", "pickUpLevel": 143565}, {"pickOffLevel": 54807, "storeCode": "319138", "pickUpLevel": 54807}, {"pickOffLevel": 38930, "storeCode": "107480", "pickUpLevel": 38930}, {"pickOffLevel": 97147, "storeCode": "117430", "pickUpLevel": 97147}, {"pickOffLevel": 77263, "storeCode": "133749", "pickUpLevel": 77263}, {"pickOffLevel": 18885, "storeCode": "1680701", "pickUpLevel": 18885}, {"pickOffLevel": 1989275, "storeCode": "2381555", "pickUpLevel": 1989275}, {"pickOffLevel": 1617813, "storeCode": "114232", "pickUpLevel": 1617813}, {"pickOffLevel": 1185206, "storeCode": "327538", "pickUpLevel": 1185206}, {"pickOffLevel": 1905709, "storeCode": "2364349", "pickUpLevel": 1905709}, {"pickOffLevel": 49923, "storeCode": "115278", "pickUpLevel": 49923}, {"pickOffLevel": 1847700, "storeCode": "192639", "pickUpLevel": 1847700}, {"pickOffLevel": 24801, "storeCode": "107059", "pickUpLevel": 24801}, {"pickOffLevel": 87379, "storeCode": "117341", "pickUpLevel": 87379}, {"pickOffLevel": -1, "storeCode": "106808", "pickUpLevel": -1}, {"pickOffLevel": 8536, "storeCode": "2961", "pickUpLevel": 8536}, {"pickOffLevel": 1000511, "storeCode": "44813", "pickUpLevel": 1000511}, {"pickOffLevel": 30264, "storeCode": "1335279", "pickUpLevel": 30264}, {"pickOffLevel": 231966, "storeCode": "247392", "pickUpLevel": 231966}, {"pickOffLevel": 18871, "storeCode": "1634382", "pickUpLevel": 18871}, {"pickOffLevel": 26611, "storeCode": "107268", "pickUpLevel": 26611}, {"pickOffLevel": 78426, "storeCode": "133807", "pickUpLevel": 78426}, {"pickOffLevel": 37185, "storeCode": "183909", "pickUpLevel": 37185}, {"pickOffLevel": 6635, "storeCode": "181864", "pickUpLevel": 6635}, {"pickOffLevel": 22800, "storeCode": "107528", "pickUpLevel": 22800}, {"pickOffLevel": 47072, "storeCode": "408969", "pickUpLevel": 47072}, {"pickOffLevel": 1766346, "storeCode": "1851872", "pickUpLevel": 1766346}, {"pickOffLevel": 27021, "storeCode": "107104", "pickUpLevel": 27021}, {"pickOffLevel": 491933, "storeCode": "259638", "pickUpLevel": 491933}, {"pickOffLevel": 59026, "storeCode": "116480", "pickUpLevel": 59026}, {"pickOffLevel": 1424123, "storeCode": "251411", "pickUpLevel": 1424123}, {"pickOffLevel": 823376, "storeCode": "322596", "pickUpLevel": 823376}, {"pickOffLevel": 112379, "storeCode": "174692", "pickUpLevel": 112379}, {"pickOffLevel": 30154, "storeCode": "107119", "pickUpLevel": 30154}, {"pickOffLevel": 1075789, "storeCode": "326964", "pickUpLevel": 1075789}, {"pickOffLevel": 36722, "storeCode": "2239938", "pickUpLevel": 36722}, {"pickOffLevel": 8603, "storeCode": "797672", "pickUpLevel": 8603}, {"pickOffLevel": 98018, "storeCode": "161973", "pickUpLevel": 98018}, {"pickOffLevel": 262523, "storeCode": "250114", "pickUpLevel": 262523}, {"pickOffLevel": 1542416, "storeCode": "1525903", "pickUpLevel": 1542416}, {"pickOffLevel": 1908600, "storeCode": "2364391", "pickUpLevel": 1908600}, {"pickOffLevel": 37633, "storeCode": "1897274", "pickUpLevel": 37633}, {"pickOffLevel": 7591, "storeCode": "115986", "pickUpLevel": 7591}, {"pickOffLevel": 72358, "storeCode": "133340", "pickUpLevel": 72358}, {"pickOffLevel": 4185, "storeCode": "116252", "pickUpLevel": 4185}, {"pickOffLevel": 485738, "storeCode": "281261", "pickUpLevel": 485738}, {"pickOffLevel": 59770, "storeCode": "116781", "pickUpLevel": 59770}, {"pickOffLevel": 895, "storeCode": "188937", "pickUpLevel": 895}, {"pickOffLevel": 119432, "storeCode": "175686", "pickUpLevel": 119432}, {"pickOffLevel": 126125, "storeCode": "181800", "pickUpLevel": 126125}, {"pickOffLevel": 67912, "storeCode": "116523", "pickUpLevel": 67912}, {"pickOffLevel": 93919, "storeCode": "136697", "pickUpLevel": 93919}, {"pickOffLevel": 56099, "storeCode": "250407", "pickUpLevel": 56099}, {"pickOffLevel": 1743407, "storeCode": "1893900", "pickUpLevel": 1743407}, {"pickOffLevel": 1101731, "storeCode": "184116", "pickUpLevel": 1101731}, {"pickOffLevel": 210682, "storeCode": "184119", "pickUpLevel": 210682}, {"pickOffLevel": 3475, "storeCode": "184113", "pickUpLevel": 3475}, {"pickOffLevel": 23028, "storeCode": "107102", "pickUpLevel": 23028}], "promptInfos": [{"jumpUrl": "https://m.ctrip.com/tangram/OTI2MjU=?ctm_ref=vactang_page_92625&isHideNavBar=YES&apppgid=10650039395&statusBarStyle=1&channelid=238407", "type": 19, "locations": [{"groupCode": "all", "index": 1}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg3112000e8l73mt525E.png"}, {"type": 18, "locations": [{"groupCode": "all", "index": 5}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg5t12000cixdsaaBF66.png"}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": false, "isLastPage": false, "uniqSign": "12001139890321479536097d0089716FI7x9zVtX", "pHub": 1, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": []}, "checkRequestTime": 1736940528454.5479, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "7787055563914949462", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a0848c8-482483-1216860", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1736940529824+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&listExtraMaps[packageLevelVersion]=1&listExtraMaps[goodsShelvesAB]=B&listExtraMaps[nonJumpFlow]=0&modify=&orderId=&pickupPointInfo=2025-01-22 19:30:00|凤凰国际机场-T1航站楼|43|18.30767|109.41235|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2025-01-24 18:30:00|凤凰国际机场-T1航站楼|43|18.30767|109.41235|||&sortType=1&uid=@@PAGENUM@@1", "groupId": "18631/queryProducts?batch=", "networkCost": 1239, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 1239, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1736940528453, "afterFetch": 1736940529692, "hasRetry": false}}