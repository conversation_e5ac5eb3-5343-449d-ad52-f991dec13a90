{"baseResponse": {"extMap": {"calculatePreAuth_3": "0.0", "allCost": "669.0", "restCost": "1", "shoppingCost_1": "637.0", "mergeGroupSize_2": "4", "setProductGroupsHashCodeCostAffect": "0.0", "usePage": "1", "restOriginalCost": "669.0", "contextBuilderCost_3": "19.0", "start": "2023-02-08 15:46:03", "dataConvertResCost": "31.0", "buildInfoCost_2": "7.0", "buildInfoCost_4": "7.0", "lastInfoCost_7": "3.0", "mergeGroupSize_6": "2", "checkRentCenter_2": "0.0", "runAsyncCost_2": "1.0", "end": "2023-02-08 15:46:04", "mergeGroup_6": "0.0", "pickupCityId": "21786", "recCost": "0.0", "productGroupCost_6": "1.0", "isRec": "1", "pageName": "List", "totalCostTime": "674", "IncludeFeesCost": "0.0", "originalCode": "200", "mergeGroup_2": "1.0", "gsCost": "0.0", "ubtProcessCost_8": "1.0", "buildInfoCost_1": "0.0", "dropoffCityId": "21786", "buildInfoCost_3": "0.0", "apiCost": "635.0", "initBaseData_1": "19.0"}, "errorCode": "0", "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "8d620416-9c5b-4af5-b385-ff952f8c4f7b", "isSuccess": true}, "rHub": 0, "isKlbData": true, "allVendorPriceCount": 6, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": true, "extras": {"serverRequestId": "43N9jE8Y188463kA1UlW", "hasListSign": "71H51924", "abVersion": "230104_DSJT_rc101|B,230104_DSJT_fil10|B,220323_DSJT_rank2|B,221207_DSJT_cxvr|A"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"shortName": "车龄", "sortNum": 1, "bitwiseType": 2, "name": "车龄", "groupCode": "CarAge", "filterItems": [{"sortNum": 1, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "5", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 3, "positionCode": "5", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotB<PERSON>_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_本田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_哈弗", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "哈弗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}], "groupCode": "BrandGroup_h0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷达", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "捷达", "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_起亚", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "起亚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "groupCode": "BrandGroup_q0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "100-200", "sortNum": 3, "name": "¥200以下", "groupCode": "Price", "itemCode": "Price_100-200"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "送车上门"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "驾驶员驾龄", "sortNum": 6, "bitwiseType": 2, "name": "驾驶员驾龄", "groupCode": "DriveAge", "filterItems": [{"sortNum": 3, "groupCode": "DriveAge", "itemCode": "DriveAge_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "满一年"}]}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_80155", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "一哥租车"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "5wd662C78Y77TH0VnIq1", "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 102, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 304, "detail": [{"code": "1001", "amount": 304, "amountDesc": "¥304", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥102", "subAmount": 102, "name": "车辆租金", "amountStr": "¥304"}, {"code": "CAR_SERVICE_FEE", "amount": 270, "amountStr": "¥270", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11006", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "夜间取车费"}, {"code": "11026", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 694, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥694", "subAmount": 694, "name": "总价", "amountStr": "¥694"}], "reference": {"vehicleCode": "0", "rStoreCode": "115043", "pLev": 68959, "comPriceCode": "[r]8b785ab2-42e5-40e5-9b0d-da6f9054787a", "bizVendorCode": "SD80155", "pStoreCode": "115043", "packageType": 0, "priceVersion": "AWEB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCEOGRKuTadQIxHCBN97EijoccC+BNp4ZHH2bbXgn0mDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkcvhm0DKfPMJdGxzOtYrbfPGW/cU512Q1SgC0jL5w50Dhdkj4ZlcFGbGNL04DDuLzYViTVf+KyZDdCWI4g28Z9vRrGj9xfIHE2Uk7pcWUOid1sUaQ936ig1DQfPbwnHXoDLVmbwMDio2LXCYweCHcMLRkxE9aQcPH8/nXkuHmYCaVPjKxlt9fipc6mqeALM80MHBRuJlQ0JBIRzZ+B5++DvCNnci0raRumNn3kTtMpqBQEjAaDK6eD8IZPLQaUdytrsQTiO+nB0E", "sendTypeForPickUpCar": 0, "skuId": 2015569, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 68959, "vendorCode": "80155", "vendorVehicleCode": "20080313"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "5104", "highestPrice": 102, "pWay": "", "minDPrice": 102, "hot": 0, "minTPrice": 694, "lowestDistance": 35.259, "group": 0, "sortNum": 0, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD80155_0_115043_115043"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false, "logicIndex": 0, "isGroup": false, "renderUniqId": "5104_1__694_102_0_免押金"}, {"groupSort": 0, "lowestPrice": 102, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 304, "detail": [{"code": "1001", "amount": 304, "amountDesc": "¥304", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥102", "subAmount": 102, "name": "车辆租金", "amountStr": "¥304"}, {"code": "CAR_SERVICE_FEE", "amount": 270, "amountStr": "¥270", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11006", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "夜间取车费"}, {"code": "11026", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 694, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥694", "subAmount": 694, "name": "总价", "amountStr": "¥694"}], "reference": {"vehicleCode": "0", "rStoreCode": "115043", "pLev": 68959, "comPriceCode": "[r]0ce03473-0a43-4503-8798-24ba04fed697", "bizVendorCode": "SD80155", "pStoreCode": "115043", "packageType": 0, "priceVersion": "AWEB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDLwESTycy5iYxHCBN97EijoccC+BNp4ZHH2bbXgn0mDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkcvhm0DKfPMJdGxzOtYrbfPGW/cU512Q1SgC0jL5w50Dhdkj4ZlcFGbGNL04DDuLzYViTVf+KyZDdCWI4g28Z9vRrGj9xfIHE2Uk7pcWUOid1sUaQ936ig1DQfPbwnHXoDLVmbwMDio2LXCYweCHcMLRkxE9aQcPH8/nXkuHmYCaVPjKxlt9fipc6mqeALM80MHBRuJlQ0JBIRzZ+B5++DvCNnci0raRusAaRuaLehyEQEjAaDK6eD8IZPLQaUdytrsQTiO+nB0E", "sendTypeForPickUpCar": 0, "skuId": 2015575, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 68959, "vendorCode": "80155", "vendorVehicleCode": "20071853"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "4067", "highestPrice": 102, "pWay": "", "minDPrice": 102, "hot": 0, "minTPrice": 694, "lowestDistance": 35.259, "group": 0, "sortNum": 1, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD80155_0_115043_115043"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false, "logicIndex": 1, "isGroup": false, "renderUniqId": "4067_1__694_102_0_免押金"}, {"groupSort": 0, "lowestPrice": 102, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 304, "detail": [{"code": "1001", "amount": 304, "amountDesc": "¥304", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥102", "subAmount": 102, "name": "车辆租金", "amountStr": "¥304"}, {"code": "CAR_SERVICE_FEE", "amount": 270, "amountStr": "¥270", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11006", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "夜间取车费"}, {"code": "11026", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 694, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥694", "subAmount": 694, "name": "总价", "amountStr": "¥694"}], "reference": {"vehicleCode": "0", "rStoreCode": "115043", "pLev": 68959, "comPriceCode": "[r]5edb0bfc-01f8-41e9-ae0e-cd604a300529", "bizVendorCode": "SD80155", "pStoreCode": "115043", "packageType": 0, "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXBVU6Y5j21vVrzvUZ2EqjhHWBoQN/MtsD0OStgtJZLpBQHO2Upf/0pR9xc9PfPJDcVV8RrJfIBqGgz8xTYLcsSeY57uwByHl5NdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPckiLoep94jFIRzZ+B5++DuyhNBk2niSwCr5jR6xgwagUHU1QXv8/AlUOag/HoUHAc/nXkuHmYCa0jlgYMtUyz37iAF2kxaBTAQguT2Xs29/LfJLo8tJEm+05rVZEj792oC2Xfbmg80/7T331tWsN4ZPYPDfztSRQBj4CrOI1G42Co8E5eBpb8Qmexv4KTleLKIP0Fbh1/0lMUGQQ6P5GDhLhKO+5MmWai3EODsl4bOreWWrrNNAqQ+DWfOjciuFLg==", "sendTypeForPickUpCar": 0, "skuId": 2039234, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 68959, "vendorCode": "80155", "vendorVehicleCode": "20001052"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "658", "highestPrice": 102, "pWay": "", "minDPrice": 102, "hot": 0, "minTPrice": 694, "lowestDistance": 35.259, "group": 0, "sortNum": 2, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD80155_0_115043_115043"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false, "logicIndex": 2, "isGroup": false, "renderUniqId": "658_1__694_102_0_免押金"}, {"groupSort": 0, "lowestPrice": 102, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 304, "detail": [{"code": "1001", "amount": 304, "amountDesc": "¥304", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥102", "subAmount": 102, "name": "车辆租金", "amountStr": "¥304"}, {"code": "CAR_SERVICE_FEE", "amount": 270, "amountStr": "¥270", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11006", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "夜间取车费"}, {"code": "11026", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 694, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥694", "subAmount": 694, "name": "总价", "amountStr": "¥694"}], "reference": {"vehicleCode": "0", "rStoreCode": "115043", "pLev": 68959, "comPriceCode": "[r]58e45205-0acc-4f8d-ac28-9cd58334f66c", "bizVendorCode": "SD80155", "pStoreCode": "115043", "packageType": 0, "priceVersion": "AWEB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXBFOAroYlEXzIxHCBN97EijoccC+BNp4ZHH2bbXgn0mDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkcvhm0DKfPMJdGxzOtYrbfPGW/cU512Q1SgC0jL5w50Dhdkj4ZlcFGbGNL04DDuLzYViTVf+KyZDdCWI4g28Z9vRrGj9xfIHE2Uk7pcWUOid1sUaQ936ig1DQfPbwnHXoDLVmbwMDio2LXCYweCHcMLRkxE9aQcPH8/nXkuHmYCaVPjKxlt9fipc6mqeALM80MHBRuJlQ0JBIRzZ+B5++DvCNnci0raRugHXLh7Do8CmQEjAaDK6eD8IZPLQaUdytrsQTiO+nB0E", "sendTypeForPickUpCar": 0, "skuId": 2031424, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 68959, "vendorCode": "80155", "vendorVehicleCode": "20069831"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "5572", "highestPrice": 102, "pWay": "", "minDPrice": 102, "hot": 0, "minTPrice": 694, "lowestDistance": 35.259, "group": 0, "sortNum": 3, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD80155_0_115043_115043"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false, "logicIndex": 3, "isGroup": false, "renderUniqId": "5572_1__694_102_0_免押金"}, {"groupSort": 0, "lowestPrice": 142, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 424, "detail": [{"code": "1001", "amount": 424, "amountDesc": "¥424", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥142", "subAmount": 142, "name": "车辆租金", "amountStr": "¥424"}, {"code": "CAR_SERVICE_FEE", "amount": 270, "amountStr": "¥270", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11006", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "夜间取车费"}, {"code": "11026", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 814, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥814", "subAmount": 814, "name": "总价", "amountStr": "¥814"}], "reference": {"vehicleCode": "0", "rStoreCode": "115043", "pLev": 68959, "comPriceCode": "[r]efbab3fd-a166-4e57-b21f-6bfe246e5539", "bizVendorCode": "SD80155", "pStoreCode": "115043", "packageType": 0, "priceVersion": "AWIB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXCxMy0oNCKlOoxHCBN97EijoccC+BNp4ZHH2bbXgn0mDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkcvhm0DKfPMJdGxzOtYrbfPGW/cU512Q1SgC0jL5w50Dhdkj4ZlcFGbhB5AHBELLWoViTVf+KyZD/TD53RrOdRvRrGj9xfIHE5NZ+fyimFMn1sUaQ936ig1DQfPbwnHXoPJCFIHN+0vSMCe5LaKnV17fBA+bhSxoiTs7sx5Ik0exbXRM6Ol+xehwIwUFOjIW1siuZ5UTuS2rdGxzOtYrbfN6bcAXtzMDaS7kLqhAP22uAravH3ltxQJuPZ8I9+i/bztqtF9OQCEG", "sendTypeForPickUpCar": 0, "skuId": 2015576, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 68959, "vendorCode": "80155", "vendorVehicleCode": "20062446"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "4117", "highestPrice": 142, "pWay": "", "minDPrice": 142, "hot": 0, "minTPrice": 814, "lowestDistance": 35.259, "group": 0, "sortNum": 4, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD80155_0_115043_115043"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false, "logicIndex": 4, "isGroup": false, "renderUniqId": "4117_1__814_142_0_免押金"}, {"groupSort": 0, "lowestPrice": 145, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 434, "detail": [{"code": "1001", "amount": 434, "amountDesc": "¥434", "name": "租车费"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥145", "subAmount": 145, "name": "车辆租金", "amountStr": "¥434"}, {"code": "CAR_SERVICE_FEE", "amount": 270, "amountStr": "¥270", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11006", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "夜间取车费"}, {"code": "11026", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "送车上门服务费"}, {"code": "11027", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "上门取车服务费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 824, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥824", "subAmount": 824, "name": "总价", "amountStr": "¥824"}], "reference": {"vehicleCode": "0", "rStoreCode": "115043", "pLev": 68959, "comPriceCode": "[r]3e831f6d-63b9-4c0c-9569-eda763a7aa50", "bizVendorCode": "SD80155", "pStoreCode": "115043", "packageType": 0, "priceVersion": "AWIB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXByU1H60qg/BYxHCBN97EijoccC+BNp4ZHH2bbXgn0mDg10AIrAPEewatRiX/q57+H92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkcvhm0DKfPMJdGxzOtYrbfPGW/cU512Q1SgC0jL5w50Dhdkj4ZlcFGa5RbeKaelHWYViTVf+KyZDzbq3k86uSrLRrGj9xfIHE8Q3DlVjdYIg1sUaQ936ig1DQfPbwnHXoCECIvajwMnKMCe5LaKnV16TiBGdGMN8VTs7sx5Ik0ex358mHn9YTsBwIwUFOjIW1siuZ5UTuS2rdGxzOtYrbfN6bcAXtzMDacnCNym3TcaJAravH3ltxQJuPZ8I9+i/bztqtF9OQCEG", "sendTypeForPickUpCar": 0, "skuId": 2015573, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 68959, "vendorCode": "80155", "vendorVehicleCode": "20093208"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "5068", "highestPrice": 145, "pWay": "", "minDPrice": 145, "hot": 0, "minTPrice": 824, "lowestDistance": 35.259, "group": 0, "sortNum": 5, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD80155_0_115043_115043"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false, "logicIndex": 5, "isGroup": false, "renderUniqId": "5068_1__824_145_0_免押金"}], "groupCode": "all", "dailyPrice": 102, "hasResult": true}, {"sortNum": 0, "groupName": "经济轿车", "hasResult": true, "groupCode": "2", "dailyPrice": 102}, {"sortNum": 4, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 142}], "frontTraceInfo": {"vehicleGroupMap": {"2": 4, "6": 2}, "normalCount": 6, "priceCount": 6, "vehicleList": ["5572", "4067", "5068", "5104", "658", "4117"], "easyLifeCount": 0, "zhimaCount": 6, "vendorNames": ["一哥租车"]}, "labelCodes": ["3650", "3494", "3504", "3548", "3705", "3547", "3709", "3510", "3696", "3731", "3509", "3788", "3789", "3679", "3746"], "quickFilter": [{"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 1, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "5", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}], "promotMap": {}, "requestInfo": {"age": 30, "pickupLocationName": "清徐火车站", "returnDate": "/Date(1677376800000+0800)/", "pickupDate": "/Date(1677160800000+0800)/", "sourceCountryId": 1, "returnLocationName": "清徐火车站"}, "allVehicleCount": 6, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"filterCode": "Vendor_0"}, "vehicleList": [{"transmissionName": "自动挡", "displacement": "1.5L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "5104", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "捷达VA3", "zhName": "捷达VA3", "brandName": "捷达", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "捷达", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.5L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "4067", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "大众宝来", "zhName": "大众宝来", "brandName": "大众", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "大众", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.5L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "658", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "本田飞度", "zhName": "本田飞度", "brandName": "本田", "passengerNo": 5, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "本田", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.4L", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "5572", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "起亚焕驰", "zhName": "起亚焕驰", "brandName": "起亚", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "2", "vehiclesSetId": "64", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "起亚", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.5T", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "4117", "carPhone": true, "autoStart": true, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "哈弗M6", "zhName": "哈弗M6", "brandName": "哈弗", "passengerNo": 5, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0", "0"], "groupCode": "6", "vehiclesSetId": "10", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "哈弗", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "license": "", "autoPark": false}, {"transmissionName": "自动挡", "displacement": "1.4T", "style": "", "isHot": false, "imageList": ["0"], "realityImageUrl": "0", "vehicleCode": "5068", "carPhone": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "捷达VS5", "zhName": "捷达VS5", "brandName": "捷达", "passengerNo": 5, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["0", "0", "0", "0", "0", "0", "0"], "groupCode": "6", "vehiclesSetId": "10", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "捷达", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "license": "", "autoPark": false}], "storeList": [{"pickOffLevel": 68959, "storeCode": "115043", "pickUpLevel": 68959}], "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": true, "isLastPage": true, "uniqSign": "120010356100002012499cNb5Wr7T4MS898L037g", "pHub": 0, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": false}, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "3920276881633829107", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a04347c-465511-40780", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1675842364261+0800)/"}, "isRecommend": true, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": true, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&modify=&orderId=&pickupPointInfo=2023-02-23 22:00:00|清徐火车站|21786|37.557692|112.313174|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=all&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2023-02-26 10:00:00|清徐火车站|21786|37.557692|112.313174|||&sortType=1&uid=@@PAGENUM@@1", "networkCost": 0, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 1235, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1675842604364, "afterFetch": 1675842604528, "hasRetry": false, "loadDiffCost": 239856, "originNetworkCost": 1235}}