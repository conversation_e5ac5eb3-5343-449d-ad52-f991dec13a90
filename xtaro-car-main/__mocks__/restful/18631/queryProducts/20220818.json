{"rHub": 0, "extras": {"serverRequestId": "Gj708Lyqp1MLpIq16Nwa", "abVersion": "220126_DSJT_lqd|B,220323_DSJT_rank2|B,220901_DSJT_RECV|B", "hasListSign": "ddfdgdgfgfh44516Nwa"}, "storeList": [{"pickOffLevel": 0, "storeCode": "128311", "pickUpLevel": 0}], "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "pHub": 0, "allVendorPriceCount": 1, "vehicleList": [{"transmissionName": "自动挡", "displacement": "1.5T", "style": "", "isHot": false, "imageList": ["https://pages.c-ctrip.com/carisd/app/21097.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com//images/04120120008asyaxc902E.jpg", "vehicleCode": "21097", "fuel": "汽油92号", "licenseStyle": "2", "oilType": 5, "driveMode": "前置前驱", "name": "长安CS75", "zhName": "长安CS75", "brandName": "长安", "brandId": 0, "passengerNo": 5, "doorNo": 5, "isSpecialized": true, "struct": "SUV", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0413u120008asw43x60D2.jpg", "https://dimg04.c-ctrip.com//images/0412t120008asvmugA01E.jpg", "https://dimg04.c-ctrip.com//images/0414g120008asvarnD61D.jpg", "https://dimg04.c-ctrip.com//images/04173120008asxzyiA671.jpg", "https://dimg04.c-ctrip.com//images/0412w120008asyaxd89DD.jpg", "https://dimg04.c-ctrip.com//images/04136120008asw9f70D21.jpg", "https://dimg04.c-ctrip.com//images/0410e120008asy8b465A9.jpg"], "groupCode": "6", "gearbox": "手自一体变速箱(AT)", "luggageNo": 3, "brandEName": "长安", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1}], "commNotices": [], "isAll": true, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "手动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "OilType", "itemCode": "OilType_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "OilType", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "长安", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changan.png"}], "groupCode": "BrandGroup_c0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "0-50", "sortNum": 1, "name": "¥50以下", "groupCode": "Price", "itemCode": "Price_0-50"}, {"code": "50-100", "sortNum": 2, "name": "¥50-100", "groupCode": "Price", "itemCode": "Price_50-100"}, {"code": "100-200", "sortNum": 3, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-99999", "sortNum": 4, "name": "¥200以上", "groupCode": "Price", "itemCode": "Price_200-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupSelf", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "自行到店取车"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "selectedIcon": "https://dimg.fws.qa.nt.ctripcorp.com/images/041251200000glbzf5F5E.png", "groupCode": "Promotion", "quickSortNum": 1, "positionCode": "14", "itemCode": "Promotion_3776", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "", "icon": "https://dimg.fws.qa.nt.ctripcorp.com/images/0415r1200000eg6x3253F.png"}], "groupCode": "Promotion", "shortName": "优惠活动", "bitwiseType": 2, "name": "优惠活动"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_8", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "台胞证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "驾驶员驾龄", "sortNum": 6, "bitwiseType": 2, "name": "驾驶员驾龄", "groupCode": "DriveAge", "filterItems": [{"sortNum": 1, "groupCode": "DriveAge", "itemCode": "DriveAge_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "不满6个月"}]}, {"shortName": "门店评分", "sortNum": 7, "bitwiseType": 2, "name": "门店评分", "groupCode": "Comment", "filterItems": [{"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "9", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 2, "groupCode": "Comment", "itemCode": "Comment_4.5", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4.5分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}]}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62073", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "边塞行"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "rentCenter": {"filterCode": "Vendor_0"}, "uniqSign": "12001044710000201123QBbG5U7xZ5Tu6VGF5rS7", "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "promotMap": {}, "baseResponse": {"extMap": {"usePage": "1", "buildInfoCost_2": "3.0", "setProductGroupsHashCodeCostAffect": "0.0", "mergeGroup_6": "1.0", "originalCode": "200", "IncludeFeesCost": "0.0", "ubtProcessCost_8": "1.0", "shoppingCost_1": "372.0", "mergeGroupSize_6": "1", "dataConvertResCost": "28.0", "buildInfoCost_3": "0.0", "pickupCityId": "124", "lastInfoCost_7": "4.0", "buildInfoCost_4": "3.0", "pageName": "List", "totalCostTime": "411", "restCost": "1", "runAsyncCost_2": "0.0", "gsCost": "1.0", "apiCost": "372.0", "allCost": "401.0", "end": "2022-08-30 15:52:38", "initBaseData_1": "19.0", "buildInfoCost_1": "0.0", "checkRentCenter_2": "0.0", "productGroupCost_6": "1.0", "calculatePreAuth_3": "1.0", "restOriginalCost": "401.0", "dropoffCityId": "124", "start": "2022-08-30 15:52:37", "contextBuilderCost_3": "20.0"}, "errorCode": "0", "hasResult": true, "code": "200", "apiResCodes": [], "message": "", "requestId": "b8bb843c-fbee-43ec-a171-3fa5bcc20e95", "isSuccess": true}, "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "easyLifeInfo": {"isEasyLife": false}, "ResponseStatus": {"Extension": [{"Value": "8317048324022089858", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a050d7d-461623-98204", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1661845958321+0800)/"}, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "requestInfo": {"age": 30, "pickupLocationName": "瑞华园", "returnDate": "/Date(1661932800000+0800)/", "pickupDate": "/Date(1661846400000+0800)/", "sourceCountryId": 1, "returnLocationName": "瑞华园"}, "isLastPage": true, "productGroupsHashCode": "95437BKQP4973g5ib467", "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "labelCodes": ["3548", "3501", "3683", "3696", "3731", "3495", "3776", "3746"], "quickFilter": [{"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "9", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 4, "selectedIcon": "https://dimg.fws.qa.nt.ctripcorp.com/images/0413l1200000eiwfm6FFA.png", "groupCode": "Promotion", "quickSortNum": 1, "positionCode": "14", "itemCode": "Promotion_3776", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "", "icon": "https://dimg.fws.qa.nt.ctripcorp.com/images/041551200000e9wrc5A84.png"}], "allVehicleCount": 1, "isFromSearch": true, "frontTraceInfo": {"vehicleGroupMap": {"6": 1}, "normalCount": 1, "priceCount": 1, "vehicleList": ["21097"], "easyLifeCount": 0, "zhimaCount": 1, "vendorNames": ["边塞行"]}, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 548, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 548, "detail": [{"code": "1001", "amount": 598, "amountDesc": "¥598", "name": "租车费"}, {"code": "3776", "amount": 50, "amountDesc": "¥50", "name": "夏日特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥548", "originalDailyPrice": 598, "subAmount": 548, "name": "车辆租金", "amountStr": "¥548"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "租车保障费", "currencyCode": "¥"}, {"amount": 628, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥678", "subAmount": 678, "name": "总价", "amountStr": "¥628"}], "reference": {"vendorCode": "62073", "bizVendorCode": "32073", "rStoreCode": "128311", "comPriceCode": "eyJSZWRpc1ZlcnNpb25LZXlfUHJpY2VJbmZvIjoiMDgyMjIwMjIxMTEzMzU2NWYyYjE4Ny03OGE2LTQ0OWItYWE1ZC02ZWYzNzA3MWE3YzYifQ==", "priceVersion": "AWYB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXB0f416KgGc/DKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAeMOoDS/WdQDAoZ4Ks8yAlUkLATQm+UuHfGn4PGmJgBeT0694D6+4cLdCOgdSRppRAdMmUrb8GVU2X+2G3PFncli4FoYgOdlRtEdVCNqSfAp4zzwTZaOX9qOSkqgMX7UiGgFqGZ/yln0wFlfIZieua2JrGaYgpt/cFdI3+HGquFjlmD7Fjn1+IP2ulGW6xAydPgjkz2OrAq5QdTVBe/z8CZdysrPA+i/kN7gUtHiwtEIu6xi41ENDX9tTu24C4IkU7T331tWsN4YaMqGAFcV3TTs7sx5Ik0exxlzlC4wQDBFc6mqeALM80MHBRuJlQ0JB1jBK+HXLpuwX+dMipsLRduphaKLH4X+6", "packageType": 0, "pStoreCode": "128311", "vehicleCode": "107763", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0}, "isMinTPriceVendor": true}], "pTag": {"showLayer": 1, "category": 3, "colorCode": "15", "mergeId": 0, "type": 3, "title": "夏日特惠", "code": "30", "sortNum": 10000, "groupCode": "MarketGroup201", "labelCode": "3776", "amountTitle": "已减50", "groupId": 1}, "vehicleCode": "21097", "highestPrice": 548, "rCoup": 0, "minDPrice": 548, "hot": 0, "minTPrice": 628, "lowestDistance": 2.8, "group": 0, "sortNum": 0, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["32073_107763_128311_128311"], "introduce": "当前车型最低价"}, "minDOrinPrice": 598, "isEasy": false, "isCredit": true, "maximumCommentCount": 5, "hotScore": 0, "hotType": 0, "reactId": "1552382970", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "押金双免", "colorCode": "2", "type": 1, "groupId": 2, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": false, "logicIndex": 0, "isGroup": false, "renderUniqId": "21097_1__628_548_598_押金双免_夏日特惠已减50"}], "groupCode": "all", "dailyPrice": 548, "hasResult": true}, {"sortNum": 3, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 548}], "appResponseMap": {"isFromCache": true, "isCacheValid": true, "networkCost": 0, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 508, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1661845955751, "afterFetch": 1661845956259, "hasRetry": false, "originNetworkCost": 508}}