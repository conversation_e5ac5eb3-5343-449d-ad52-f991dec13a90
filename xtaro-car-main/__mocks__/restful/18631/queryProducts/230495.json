{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "dc93ecd8-0bab-46fa-a5b9-c72cec7e554e", "extMap": {"runAsyncCost_2": "0.0", "checkRentCenter_2": "0.0", "mergeGroup_9": "1.0", "restOriginalCost": "1454.0", "mergeGroupSize_10": "3", "mergeGroupSize_11": "3", "pageName": "List", "initBaseData_1": "24.0", "allCost": "1454.0", "restCost": "0", "dataConvertResCost": "390.0", "usePage": "1", "dropoffCityId": "43", "IncludeFeesCost": "0.0", "mergeGroupSize_2": "92", "mergeGroupSize_newenergy": "114", "mergeGroupSize_5": "52", "mergeGroupSize_6": "179", "mergeGroupSize_3": "50", "mergeGroupSize_4": "67", "mergeGroup_7": "0.0", "productGroupCost_6": "11.0", "mergeGroup_6": "1.0", "originalCode": "200", "mergeGroup_5": "1.0", "mergeGroup_4": "1.0", "mergeGroup_3": "0.0", "mergeGroup_2": "1.0", "apiCost": "1062.0", "contextBuilderCost_3": "24.0", "ubtProcessCost_8": "1.0", "calculatePreAuth_3": "0.0", "mergeGroupSize_9": "63", "uid": "3201564125", "mergeGroupSize_7": "3", "mergeGroup_newenergy": "6.0", "end": "2023-05-06 14:34:42", "totalCostTime": "1533", "start": "2023-05-06 14:34:40", "shoppingCost_1": "1063.0", "gsCost": "1.0", "buildInfoCost_2": "233.0", "buildInfoCost_3": "0.0", "buildInfoCost_4": "235.0", "mergeGroup_11": "0.0", "setProductGroupsHashCodeCostAffect": "0.0", "buildInfoCost_1": "1.0", "lastInfoCost_7": "120.0", "mergeGroup_10": "0.0", "pickupCityId": "43"}, "apiResCodes": [], "hasResult": true, "errorCode": "0", "message": ""}, "ResponseStatus": {"Timestamp": "2023-05-06 14:34:42", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "7353406488268388137"}, {"Id": "RootMessageId", "Value": "921822-0a3c7dab-467598-716879"}]}, "requestInfo": {"pickupDate": "2023-07-18 20:30:00", "pickupLocationName": "凤凰国际机场", "returnDate": "2023-07-27 20:30:00", "returnLocationName": "凤凰国际机场", "sourceCountryId": 1, "age": 30, "pLatitude": 18.303421054, "rLatitude": 18.303421054, "rLongitude": 109.414631734, "pLongitude": 109.414631734, "pDate": "20230718203000", "rDate": "20230727203000"}, "allVehicleCount": 512, "allVendorPriceCount": 2142, "filterMenuItems": [{"name": "快速选车", "code": "QuickChoose", "sortNum": 1.0, "hierarchy": 1, "filterGroups": [{"name": "车龄", "sortNum": 1, "groupCode": "CarAge", "bitwiseType": 2, "filterItems": [{"itemCode": "CarAge_3509", "name": "半年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "8"}, {"itemCode": "CarAge_3510", "name": "一年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "8"}, {"itemCode": "CarAge_3547", "name": "两年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 3, "positionCode": "8"}], "shortName": "车龄"}, {"name": "座位数", "sortNum": 2, "groupCode": "SeatGroup", "bitwiseType": 2, "filterItems": [{"itemCode": "SeatGroup_1", "name": "2座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "SeatGroup_2", "name": "4座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "SeatGroup_3", "name": "5座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "SeatGroup_4", "name": "6座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}, {"itemCode": "SeatGroup_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 5, "isQuickItem": true, "quickSortNum": 1, "positionCode": "13"}, {"itemCode": "SeatGroup_6", "name": "8座及以上", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 6, "isQuickItem": false}], "shortName": "座位", "isSupportMulti": true}, {"name": "车辆排挡", "sortNum": 3, "groupCode": "Transmission", "bitwiseType": 2, "filterItems": [{"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "10"}, {"itemCode": "Transmission_2", "name": "手动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}], "shortName": "排挡"}, {"name": "能源类型", "sortNum": 4, "groupCode": "NewEnergy", "bitwiseType": 2, "filterItems": [{"itemCode": "NewEnergy_elect", "name": "纯电动", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "NewEnergy_mix", "name": "新能源混动", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "NewEnergy_gas", "name": "汽油", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}], "shortName": "能源类型", "isSupportMulti": true}, {"name": "车辆配置", "sortNum": 5, "groupCode": "VehicleAccessory", "bitwiseType": 1, "filterItems": [{"itemCode": "VehicleAccessory_ReversingImage", "name": "倒车影像", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 2, "isQuickItem": true, "quickSortNum": 6, "positionCode": "9"}, {"itemCode": "VehicleAccessory_radar", "name": "倒车雷达", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 32, "sortNum": 3, "isQuickItem": true, "quickSortNum": 7, "positionCode": "9"}, {"itemCode": "VehicleAccessory_tachograph", "name": "行车记录仪", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 64, "sortNum": 4, "isQuickItem": true, "quickSortNum": 8, "positionCode": "9"}, {"itemCode": "VehicleAccessory_MobileHolder", "name": "手机支架", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 128, "sortNum": 5, "isQuickItem": false}, {"itemCode": "VehicleAccessory_LeatherSeat", "name": "真皮座椅", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 256, "sortNum": 6, "isQuickItem": false}, {"itemCode": "VehicleAccessory_Refrigerator", "name": "车载冰箱", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 512, "sortNum": 7, "isQuickItem": false}], "shortName": "车辆配置", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 6, "groupCode": "HotBrand", "bitwiseType": 2, "filterItems": [{"itemCode": "HotBrand_奥迪", "name": "奥迪", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"itemCode": "HotBrand_别克", "name": "别克", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "HotBrand_奔驰", "name": "奔驰", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "HotBrand_宝马", "name": "宝马", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "HotB<PERSON>_本田", "name": "本田", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"itemCode": "HotBrand_大众", "name": "大众", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"itemCode": "HotBrand_丰田", "name": "丰田", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "HotBrand_Jeep", "name": "Jeep", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 10, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"itemCode": "HotBrand_路虎", "name": "路虎", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 12, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"itemCode": "HotBrand_日产", "name": "日产", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 18, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "shortName": "热门品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_a0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_a0_AC <PERSON>itzer", "name": "AC Schnitzer", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/acschnitzer.png"}, {"itemCode": "BrandGroup_a0_埃安", "name": "埃安", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_a0_奥迪", "name": "奥迪", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"itemCode": "BrandGroup_a0_阿斯顿·马丁", "name": "阿斯顿·马丁", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R419120009gwx9cqBA77.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_b0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_b0_BEIJING汽车", "name": "BEIJING汽车", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R46u120009gwv7gj857B.png"}, {"itemCode": "BrandGroup_b0_保时捷", "name": "保时捷", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"itemCode": "BrandGroup_b0_别克", "name": "别克", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "BrandGroup_b0_北京", "name": "北京", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/beijin.png"}, {"itemCode": "BrandGroup_b0_北汽威旺", "name": "北汽威旺", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/beiqiweiwang.png"}, {"itemCode": "BrandGroup_b0_奔腾", "name": "奔腾", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benteng.png"}, {"itemCode": "BrandGroup_b0_奔驰", "name": "奔驰", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "BrandGroup_b0_宝沃", "name": "宝沃", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baowo.png"}, {"itemCode": "BrandGroup_b0_宝马", "name": "宝马", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "BrandGroup_b0_宝骏", "name": "宝骏", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baojun.png"}, {"itemCode": "BrandGroup_b0_宾利", "name": "宾利", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/binli.png"}, {"itemCode": "BrandGroup_b0_巴博斯", "name": "巴博斯", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 4, "isQuickItem": false, "icon": "//dimg04.c-ctrip.com/images/0AS47120008bmtwth44BA.png"}, {"itemCode": "BrandGroup_b0_本田", "name": "本田", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"itemCode": "BrandGroup_b0_标致", "name": "标致", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/biaozhi.png"}, {"itemCode": "BrandGroup_b0_比亚迪", "name": "比亚迪", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_c0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_c0_长城", "name": "长城", "groupCode": "BrandGroup_c0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/changcheng.png"}, {"itemCode": "BrandGroup_c0_长安", "name": "长安", "groupCode": "BrandGroup_c0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/changan.png"}, {"itemCode": "BrandGroup_c0_长安欧尚", "name": "长安欧尚", "groupCode": "BrandGroup_c0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/changanoushang.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_d0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_d0_东风风光", "name": "东风风光", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengguang.png"}, {"itemCode": "BrandGroup_d0_东风风行", "name": "东风风行", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengxin.png"}, {"itemCode": "BrandGroup_d0_大众", "name": "大众", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"itemCode": "BrandGroup_d0_道奇", "name": "道奇", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/daoqi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_f0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_f0_丰田", "name": "丰田", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "BrandGroup_f0_法拉利", "name": "法拉利", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"itemCode": "BrandGroup_f0_福特", "name": "福特", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_g0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_g0_广汽传祺", "name": "广汽传祺", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}, {"itemCode": "BrandGroup_g0_广汽埃安", "name": "广汽埃安", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R44f120009gwvd13842B.png"}, {"itemCode": "BrandGroup_g0_广汽新能源", "name": "广汽新能源", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqixingnengyuan.png"}, {"itemCode": "BrandGroup_g0_高合汽车", "name": "高合汽车", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0yc6a12000aom31vy37E4.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_h0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_h0_华晨新日", "name": "华晨新日", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R472120009gwv9rxB7E1.png"}, {"itemCode": "BrandGroup_h0_华颂", "name": "华颂", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/huasong.png"}, {"itemCode": "BrandGroup_h0_哈弗", "name": "哈弗", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}, {"itemCode": "BrandGroup_h0_海马", "name": "海马", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}, {"itemCode": "BrandGroup_h0_红旗", "name": "红旗", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}, {"itemCode": "BrandGroup_h0_黄海", "name": "黄海", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/huanghai.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_j0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_j0_Jeep", "name": "Jeep", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"itemCode": "BrandGroup_j0_几何汽车", "name": "几何汽车", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R43t120009gwv73p44FD.png"}, {"itemCode": "BrandGroup_j0_吉利汽车", "name": "吉利汽车", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"itemCode": "BrandGroup_j0_捷豹", "name": "捷豹", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiebao.png"}, {"itemCode": "BrandGroup_j0_捷达", "name": "捷达", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}, {"itemCode": "BrandGroup_j0_江淮", "name": "江淮", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jianghuan.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_k0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_k0_凯迪拉克", "name": "凯迪拉克", "groupCode": "BrandGroup_k0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_l0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_l0_兰博基尼", "name": "兰博基尼", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"itemCode": "BrandGroup_l0_凌宝汽车", "name": "凌宝汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R41o120009gwvad526A5.png"}, {"itemCode": "BrandGroup_l0_力帆汽车", "name": "力帆汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lifanqiche.png"}, {"itemCode": "BrandGroup_l0_劳斯莱斯", "name": "劳斯莱斯", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"itemCode": "BrandGroup_l0_岚图汽车", "name": "岚图汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R45a120009gwvmb34A33.png"}, {"itemCode": "BrandGroup_l0_理想汽车", "name": "理想汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc6i1200000ieok0AE70.png"}, {"itemCode": "BrandGroup_l0_聊工", "name": "聊工", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_l0_路虎", "name": "路虎", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"itemCode": "BrandGroup_l0_零跑汽车", "name": "零跑汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingpaoqiche.png"}, {"itemCode": "BrandGroup_l0_雷克萨斯", "name": "雷克萨斯", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}, {"itemCode": "BrandGroup_l0_领克", "name": "领克", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingke.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_m0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_m0_MINI", "name": "MINI", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"itemCode": "BrandGroup_m0_玛莎拉蒂", "name": "玛莎拉蒂", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}, {"itemCode": "BrandGroup_m0_迈凯伦", "name": "迈凯伦", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/maikailun.png"}, {"itemCode": "BrandGroup_m0_马自达", "name": "马自达", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mazida.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_n0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_n0_哪吒汽车", "name": "哪吒汽车", "groupCode": "BrandGroup_n0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R46n120009gwvgrmC0A4.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_o0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_o0_欧拉", "name": "欧拉", "groupCode": "BrandGroup_o0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/oula.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_p0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_p0_朋克汽车", "name": "朋克汽车", "groupCode": "BrandGroup_p0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R45q120009gwx3tr6B92.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_q0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_q0_前途", "name": "前途", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiantu.png"}, {"itemCode": "BrandGroup_q0_奇瑞", "name": "奇瑞", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qirui.png"}, {"itemCode": "BrandGroup_q0_起亚", "name": "起亚", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_r0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_r0_日产", "name": "日产", "groupCode": "BrandGroup_r0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"itemCode": "BrandGroup_r0_荣威", "name": "荣威", "groupCode": "BrandGroup_r0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_s0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_s0_三菱", "name": "三菱", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sanling.png"}, {"itemCode": "BrandGroup_s0_上汽大通MAXUS", "name": "上汽大通MAXUS", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}, {"itemCode": "BrandGroup_s0_斯柯达", "name": "斯柯达", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sikeda.png"}, {"itemCode": "BrandGroup_s0_赛麟", "name": "赛麟", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sailin.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_t0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_t0_坦克", "name": "坦克", "groupCode": "BrandGroup_t0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0yc3612000aoma5tvACF1.png"}, {"itemCode": "BrandGroup_t0_特斯拉", "name": "特斯拉", "groupCode": "BrandGroup_t0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}, {"itemCode": "BrandGroup_t0_腾势", "name": "腾势", "groupCode": "BrandGroup_t0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/shiteng.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_w0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_w0_WEY", "name": "WEY", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/wey.png"}, {"itemCode": "BrandGroup_w0_五菱汽车", "name": "五菱汽车", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/wulingqiche.png"}, {"itemCode": "BrandGroup_w0_威马汽车", "name": "威马汽车", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/weimaqiche.png"}, {"itemCode": "BrandGroup_w0_沃尔沃", "name": "沃尔沃", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/woerwo.png"}, {"itemCode": "BrandGroup_w0_蔚来", "name": "蔚来", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/weilai.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_x0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_x0_小鹏汽车", "name": "小鹏汽车", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiaopengqiche.png"}, {"itemCode": "BrandGroup_x0_现代", "name": "现代", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"itemCode": "BrandGroup_x0_雪佛兰", "name": "雪佛兰", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_y0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_y0_依维柯", "name": "依维柯", "groupCode": "BrandGroup_y0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/yikewei.png"}, {"itemCode": "BrandGroup_y0_英菲尼迪", "name": "英菲尼迪", "groupCode": "BrandGroup_y0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/yingfeinidi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_z0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_z0_SONGSAN MOTORS", "name": "SONGSAN MOTORS", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_z0_睿蓝汽车", "name": "睿蓝汽车", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_z0_飞凡汽车", "name": "飞凡汽车", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false}], "shortName": "全部品牌", "isSupportMulti": true}]}, {"name": "更多筛选", "code": "MoreC<PERSON>ose", "sortNum": 2.0, "hierarchy": 1, "filterGroups": [{"name": "价格", "sortNum": 1, "groupCode": "Price", "filterItems": [{"itemCode": "Price_0-50", "name": "¥50以下", "code": "0-50", "groupCode": "Price", "sortNum": 1}, {"itemCode": "Price_50-100", "name": "¥50-100", "code": "50-100", "groupCode": "Price", "sortNum": 2}, {"itemCode": "Price_100-200", "name": "¥100-200", "code": "100-200", "groupCode": "Price", "sortNum": 3}, {"itemCode": "Price_200-99999", "name": "¥200以上", "code": "200-99999", "groupCode": "Price", "sortNum": 4}], "shortName": "价格"}, {"name": "取车方式", "sortNum": 2, "groupCode": "PickReturn", "bitwiseType": 2, "filterItems": [{"itemCode": "PickReturn_StationPR", "name": "站内取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "mark": "15分钟内", "positionCode": "1"}, {"itemCode": "PickReturn_PickupOnDoor", "name": "送车上门", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "1"}, {"itemCode": "PickReturn_FreeShuttle", "name": "免费接至门店取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "PickReturn_PickupSelf", "name": "自行到店取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}], "shortName": "取还方式", "isSupportMulti": true}, {"name": "门店服务", "sortNum": 3, "groupCode": "StoreService", "bitwiseType": 1, "filterItems": [{"itemCode": "StoreService_easyLife", "name": "无忧租", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 2, "positionCode": "3"}, {"itemCode": "StoreService_FreeDepositAllCtrip", "name": "押金双免", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 8, "sortNum": 4, "isQuickItem": true, "quickSortNum": 3, "positionCode": "3"}, {"itemCode": "StoreService_Unlimit", "name": "不限里程", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 5, "isQuickItem": false}, {"itemCode": "StoreService_FreeCancel", "name": "免费取消", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 32, "sortNum": 6, "isQuickItem": false}], "shortName": "门店服务", "isSupportMulti": true}, {"name": "优惠活动", "sortNum": 4, "groupCode": "Promotion", "bitwiseType": 2, "filterItems": [{"itemCode": "Promotion_3641", "name": "铂金贵宾", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 13, "positionCode": "2"}, {"itemCode": "Promotion_3642", "name": "酒店用户专享", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 3, "isQuickItem": true, "quickSortNum": 11, "positionCode": "2"}, {"itemCode": "Promotion_3658", "name": "周租优惠", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": true, "quickSortNum": 12, "positionCode": "2"}], "shortName": "优惠活动", "isSupportMulti": true}, {"name": "取车证件", "sortNum": 5, "groupCode": "Ceritificate", "bitwiseType": 2, "filterItems": [{"itemCode": "Ceritificate_1", "name": "身份证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_2", "name": "护照", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_7", "name": "回乡证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_8", "name": "台胞证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 3, "isQuickItem": false}], "shortName": "取车证件", "isSupportMulti": true}, {"name": "驾驶员驾龄", "sortNum": 6, "groupCode": "DriveAge", "bitwiseType": 2, "filterItems": [{"itemCode": "DriveAge_1", "name": "不满6个月", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "DriveAge_2", "name": "满6个月", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "DriveAge_3", "name": "满一年", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}], "shortName": "驾驶员驾龄"}, {"name": "门店评分", "sortNum": 7, "groupCode": "Comment", "bitwiseType": 2, "filterItems": [{"itemCode": "Comment_4.8", "name": "4.8分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "12"}, {"itemCode": "Comment_4.5", "name": "4.5分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Comment_4.0", "name": "4.0分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}], "shortName": "门店评分"}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_0", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_0", "name": "携程租车中心", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_-1", "name": "携程优选", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": true, "quickSortNum": 1, "icon": "", "positionCode": "4"}, {"itemCode": "Vendor_13088", "name": "一嗨租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false, "mark": "hot"}, {"itemCode": "Vendor_13027", "name": "凹凸出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13031", "name": "桐叶租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13032", "name": "明昊租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13033", "name": "枫叶租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13037", "name": "车速递租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13067", "name": "车游天下", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30055", "name": "准典出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30147", "name": "骑仕租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30164", "name": "懒人行租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30182", "name": "加加租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30234", "name": "金晟租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30283", "name": "龙嘉租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30777", "name": "海南椰林情租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_31218", "name": "大权租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32231", "name": "泰信吉租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32498", "name": "么么达租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32538", "name": "海越租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_33419", "name": "欣岳美行租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_38236", "name": "天驹租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_47522", "name": "祥驰租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_52811", "name": "三亚蔚蓝租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_53893", "name": "三亚世纪联合租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_58487", "name": "丰田海南出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61365", "name": "普信租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61671", "name": "凤翔天涯租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61953", "name": "三亚易云租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61966", "name": "三亚宝驰租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_1", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_62072", "name": "一路平安租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62099", "name": "宏驰智行租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62104", "name": "金森租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62107", "name": "启捷租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62115", "name": "三亚启航租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62166", "name": "小红帽租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62305", "name": "三亚皖太租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62408", "name": "宏广东盈租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62863", "name": "文东租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_63457", "name": "翊霏租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_63460", "name": "小米租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_63836", "name": "租租侠租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_63857", "name": "信华租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_64662", "name": "潆莹租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_65413", "name": "哈尔滨奥朗租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_65451", "name": "荣树租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_65452", "name": "新易达租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_66324", "name": "环岛租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_66614", "name": "你我他租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_68688", "name": "青草兔租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69282", "name": "榴莲租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69287", "name": "华鑫海租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70400", "name": "禧瑞达租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70693", "name": "车旺达租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70695", "name": "龙之祥租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70698", "name": "陵水铭途租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_72621", "name": "细杰租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_72983", "name": "吉驰租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_73265", "name": "车之美租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_73619", "name": "海南锦程租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_73871", "name": "多浦达租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_2", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_74365", "name": "爱尚出行租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_76105", "name": "全季租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_76665", "name": "彩车坊租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_77081", "name": "常晟租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_78579", "name": "优享旅途租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79461", "name": "海南麻豆智行租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79485", "name": "立强租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79747", "name": "鹏顺通租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79797", "name": "麒麟火租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80431", "name": "小飞侠租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80551", "name": "瑞赢租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81479", "name": "商旅出行", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81525", "name": "钰鑫租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81527", "name": "海南辽诚租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81679", "name": "凯信租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81687", "name": "易达通租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81827", "name": "京海租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81829", "name": "诚航租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81831", "name": "潮人商旅租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81889", "name": "八骏马租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82153", "name": "四季嘉行租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82163", "name": "启瑞盛租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82247", "name": "车租婆租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82301", "name": "梵云租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82393", "name": "海鸭鸭租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82627", "name": "宁洋租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82671", "name": "山水云途租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82731", "name": "海南途达租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82867", "name": "叁零租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82909", "name": "五行租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82991", "name": "海心租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_3", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_15000295", "name": "途新租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000361", "name": "海南点赞租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82993", "name": "港梦超跑俱乐部租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83025", "name": "和平租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83115", "name": "慧霏租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83141", "name": "京海亚租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83157", "name": "泽恒租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83221", "name": "昌泰租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83257", "name": "腾越租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83291", "name": "豪享荟租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83386", "name": "虫子邦租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83674", "name": "乐达通租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84185", "name": "龙悦三亚租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84255", "name": "格莱拓租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85129", "name": "时间旅行租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}]}], "quickFilter": [{"itemCode": "PickReturn_StationPR", "name": "站内取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "mark": "15分钟内", "positionCode": "1"}, {"itemCode": "Promotion_3642", "name": "酒店用户专享", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 3, "isQuickItem": true, "quickSortNum": 11, "positionCode": "2"}, {"itemCode": "StoreService_easyLife", "name": "无忧租", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 2, "positionCode": "3"}, {"itemCode": "Vendor_-1", "name": "携程优选", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": true, "quickSortNum": 1, "icon": "", "positionCode": "4"}, {"itemCode": "CarAge_3509", "name": "半年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "8"}, {"itemCode": "VehicleAccessory_ReversingImage", "name": "倒车影像", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 2, "isQuickItem": true, "quickSortNum": 6, "positionCode": "9"}, {"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "10"}, {"itemCode": "Comment_4.8", "name": "4.8分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "12"}, {"itemCode": "SeatGroup_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 5, "isQuickItem": true, "quickSortNum": 1, "positionCode": "13"}], "vehicleList": [{"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "4139", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.0T-1.5L", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0scn143fai2ah4w9B69.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0414k120008at15l04B7B.jpg", "https://dimg04.c-ctrip.com//images/04139120008at3x402339.jpg", "https://dimg04.c-ctrip.com//images/0416n120008at1b3t7DE9.jpg", "https://dimg04.c-ctrip.com//images/04135120008at4it05F01.jpg", "https://dimg04.c-ctrip.com//images/04142120008at10cy3FB8.jpg", "https://dimg04.c-ctrip.com//images/0413g120008at239eCF3C.jpg", "https://dimg04.c-ctrip.com//images/04171120008at46pr9E1E.jpg", "https://dimg04.c-ctrip.com//images/0415b120008at1hgaA2A9.jpg", "https://dimg04.c-ctrip.com//images/0414k120008at2f9v4B12.jpg", "https://dimg04.c-ctrip.com//images/0414w120008at1b3z48A6.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "vehiclesSetId": "71"}, {"brandEName": "红旗", "brandName": "红旗", "name": "红旗E-QM5", "zhName": "红旗E-QM5", "vehicleCode": "5694", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 4, "doorNo": 4, "luggageNo": 5, "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04141120009czlkee51B2.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1f12000axbd7fm43A8.jpg", "https://dimg04.c-ctrip.com/images/0RV1z12000axbd7u2AAFC.jpg", "https://dimg04.c-ctrip.com/images/0RV6a12000axbd6clD655.jpg", "https://dimg04.c-ctrip.com/images/0RV3e12000axbdf0h51F9.jpg", "https://dimg04.c-ctrip.com/images/0RV2x12000aru1a8wD748.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1i12000b60zfdq853A.jpg", "oilType": 5, "fuelType": "纯电动", "luggageNum": "可放5个24寸行李箱", "endurance": "续航431km", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vr": "", "vehiclesSetId": "84"}, {"brandEName": "广汽传祺", "brandName": "广汽传祺", "name": "传祺M8", "zhName": "传祺M8", "vehicleCode": "4088", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 1, "displacement": "2.0T", "fuel": "92号、95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6912000av78b0rE5E1.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1r12000av78kqf41D9.jpg", "https://dimg04.c-ctrip.com/images/0RV2412000av78pxbD627.jpg", "https://dimg04.c-ctrip.com/images/0RV0i12000av78ky66E4B.jpg", "https://dimg04.c-ctrip.com/images/0RV4312000av78qht0DD8.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2u12000av78mro2047.jpg", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "17561", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T-2.5L", "fuel": "95号、92号", "driveMode": "前置前驱", "style": "2017/18款", "imageList": ["https://dimg04.c-ctrip.com/images/0RV2g12000aqhkbeq61EE.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0n12000axmtjvjE802.jpg", "https://dimg04.c-ctrip.com/images/0RV2r12000axmtozd6DBF.jpg", "https://dimg04.c-ctrip.com/images/0RV1g12000axmtslr96F5.jpg", "https://dimg04.c-ctrip.com/images/0RV6912000axn74a7A11C.jpg", "https://dimg04.c-ctrip.com/images/0RV3c12000axn7cgh4C2D.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4o12000aqhkbrkF531.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "5282", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2020款", "imageList": ["https://dimg04.c-ctrip.com/images/0414g120008n387rpD117.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0m12000axnc68s3C3C.jpg", "https://dimg04.c-ctrip.com/images/0RV4b12000axnckg940E8.jpg", "https://dimg04.c-ctrip.com/images/0RV0u12000axnc9nrE4FB.jpg", "https://dimg04.c-ctrip.com/images/0RV1l12000axncdqxD65F.jpg", "https://dimg04.c-ctrip.com/images/0RV5t12000axnco5kA172.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0412s120008n38gqgE28F.png", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "5524", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2022款", "imageList": ["https://dimg04.c-ctrip.com/images/0415l1200096gjnmfBBC3.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04135120009sj6iw05666.jpg", "https://dimg04.c-ctrip.com/images/0416a120009sj6a6j335F.jpg", "https://dimg04.c-ctrip.com/images/04170120009sj6bbm27A7.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04120120009sj6du53129.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "5283", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2021款", "imageList": ["https://dimg04.c-ctrip.com/images/0416b120008n393n0F04E.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6412000axn0p3i4EEE.jpg", "https://dimg04.c-ctrip.com/images/0RV2k12000axn2gru108E.jpg", "https://dimg04.c-ctrip.com/images/0RV4k12000axn2vzjF1B0.jpg", "https://dimg04.c-ctrip.com/images/0RV4t12000axn4xzgD50F.jpg", "https://dimg04.c-ctrip.com/images/0RV5r12000axn56fnD538.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04170120009sl188f1B79.png", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=3475", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "17391", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2023款", "imageList": ["https://dimg04.c-ctrip.com/images/0415q12000a66mlt0FCAA.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV2z12000axkzmphD9F0.jpg", "https://dimg04.c-ctrip.com/images/0RV1a12000axkzm45E8A0.jpg", "https://dimg04.c-ctrip.com/images/0RV7012000axkzuuf307F.jpg", "https://dimg04.c-ctrip.com/images/0RV6g12000axl0cmv08ED.jpg", "https://dimg04.c-ctrip.com/images/0RV4r12000axl0re9487B.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0416z12000a66lvwq002F.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "vehicleCode": "5281", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2020款", "imageList": ["https://dimg04.c-ctrip.com/images/0415p12000a5yobd225E1.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0413q120009atiyas3F99.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0413q120009atiyas3F99.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "carPhone": true, "autoStart": true, "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "vehicleCode": "5526", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 1, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2022款", "imageList": ["https://dimg04.c-ctrip.com/images/0412m12000967yruj5B2A.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04115120009u6pni2C139.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04115120009u6pni2C139.jpg", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "vehicleCode": "5374", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2021款", "imageList": ["https://dimg04.c-ctrip.com/images/0416w120008xdnflq06B8.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0410q120008xdn84q93D3.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0410q120008xdn84q93D3.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "carPhone": true, "autoStart": true, "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "vehicleCode": "17390", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2023款", "imageList": ["https://dimg04.c-ctrip.com/images/0410112000a66lopr6B86.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0413p12000a66lqzxCF69.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0414b12000a66lgegAE6A.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "carPhone": true, "autoStart": true, "autoBackUp": true, "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "vehicleCode": "4660", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2017/18款", "imageList": ["https://dimg04.c-ctrip.com/images/0414p120008n3an3yA376.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0413a120008assza8A771.jpg", "https://dimg04.c-ctrip.com//images/04154120008asr7w7DC02.jpg", "https://dimg04.c-ctrip.com//images/04145120008ast88hD3D3.jpg", "https://dimg04.c-ctrip.com//images/0413u120008asqglk3393.jpg", "https://dimg04.c-ctrip.com/images/0RV0g12000aqe93bf18C2.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com//images/0414k120008asucjg9A76.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "carPhone": true, "autoStart": true, "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "vehicleCode": "4659", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.5L", "fuel": "92号", "driveMode": "前置前驱", "style": "2017/18款", "imageList": ["https://dimg04.c-ctrip.com/images/0413v120008n373qd2A78.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0415u120009jhml09F179.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0415u120009jhml09F179.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": false, "vr": "", "vehiclesSetId": "50"}, {"brandEName": "大众", "brandName": "大众", "name": "大众宝来", "zhName": "大众宝来", "vehicleCode": "4067", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.5L", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0qcn143fagmgoxd04D4.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0414f120008at7adg597F.jpg", "https://dimg04.c-ctrip.com//images/04107120008at6v58BAE5.jpg", "https://dimg04.c-ctrip.com//images/0414y120008k97c20F174.jpg", "https://dimg04.c-ctrip.com//images/0415t120008at6ou0D58A.jpg", "https://dimg04.c-ctrip.com//images/0413r120008at5hb7D462.jpg", "https://dimg04.c-ctrip.com//images/0410r120008at5hwz665E.jpg", "https://dimg04.c-ctrip.com//images/0414s120008k9bbmfAD66.jpg", "https://dimg04.c-ctrip.com//images/0411c120008at6gmw507E.jpg", "https://dimg04.c-ctrip.com//images/0414f120008at7554DDE5.jpg", "https://dimg04.c-ctrip.com//images/0416m120008at6vq4D32C.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com//images/04156120008at71ga4625.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4860", "vehiclesSetId": "71"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "4663", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 2, "displacement": "2.4L", "fuel": "92号", "driveMode": "前置前驱", "style": "2015款及以前", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0pcn152vdyxum4g0C43.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0414l120008ass36rEE80.jpg", "https://dimg04.c-ctrip.com//images/0413o120008assue20852.jpg", "https://dimg04.c-ctrip.com//images/0414q120008ast8fp0728.jpg", "https://dimg04.c-ctrip.com//images/0410j120008astc95F0F6.jpg", "https://dimg04.c-ctrip.com//images/04139120008asqsewA0A2.jpg", "https://pages.c-ctrip.com/carisd/app/dta117303.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com//images/04150120008ast88aBCD3.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "carPhone": false, "autoStart": false, "autoBackUp": false, "vr": "", "vehiclesSetId": "50"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "vehicleCode": "4662", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 2, "displacement": "3.0L", "fuel": "92号", "driveMode": "前置前驱", "style": "2015款及以前", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0pcn152vdyxum4g0C43.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/04141120008astx9s3B3C.jpg", "https://dimg04.c-ctrip.com//images/0414y120008asrn2hF450.jpg", "https://dimg04.c-ctrip.com//images/0413r120008asujygAEAA.jpg", "https://dimg04.c-ctrip.com//images/04163120008asraid214A.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com//images/04151120008assza27119.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "carPhone": false, "autoStart": true, "autoBackUp": false, "vr": "", "vehiclesSetId": "50"}], "productGroups": [{"groupCode": "all", "groupName": "全部车型", "sortNum": -4, "productList": [{"vehicleCode": "4139", "sortNum": 0, "lowestPrice": 42.0, "highestPrice": 533.0, "maximumRating": 5.0, "maximumCommentCount": 37057, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3746", "vendorCode": "32498", "pStoreCode": "107196", "rStoreCode": "107196", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]MDIzfDQ0NjJ8MjAyMC4wMC0xOCAzLTA3MDowMDAwOjAmMSQyJjQ4JjA3LTEwMjMtOjAwOjkgMDA4JiYxMDAmNDMtMDckMjAyMDA6MC0yMCAmNDgmMDowMDAyMy0mMSQyMSAwMDA3LTIwMCY0OjAwOiQyMDI4JiYxLTIyIDMtMDcwOjAwMDA6MCYxJDImNDgmMDctMjAyMy06MDA6MyAwMDgmJjEwMCY0My0wNyQyMDIwMDowLTI0ICY0OCYwOjAwMDIzLSYxJDI1IDAwMDctMjAwJjQ6MDA6JDIwMjgmJjEtMjYgMy0wNzA6MDAwMDowJjEkfCY0OCYmOSY0MTAwMTIkMTA4JjQzJjIwLjAzJjEwLjAwMDAmMjImOSYkMTAwNzAkfDMwJjItMDUtMjAyMzQ6MzQwNiAxAAAAADo0MAA=", "priceVersion": "SH-PRICEVERSION_MTA3MTk2XzQxMzlfMV80OF80MzJfNDhfNzIyLjAwXzQyLjBfNjYyLjBfMF8wXzAuMF8wLjBfMjcwXzIwLjAwXzAuMDBfMC4wMF80NDYyMDIz", "vendorVehicleCode": "20037374", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 4462023, "pLev": 23111, "rLev": 23111, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 372.0, "amountStr": "¥372", "subAmount": 42.0, "subAmountStr": "日均¥42", "originalDailyPrice": 48, "detail": [{"code": "1001", "name": "租车费", "amount": 432, "amountDesc": "¥432"}, {"code": "11037", "name": "优惠券", "amount": 60, "amountDesc": "¥60"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 270, "amountStr": "¥270", "detail": [{"code": "1002", "name": "基础服务费", "amount": 270, "amountDesc": "¥270", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 662.0, "amountStr": "¥662", "subAmount": 722.0, "subAmountStr": "¥722", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3010", "vehicleCode": "0", "packageType": 0, "skuId": 2053450}}, {"reference": {"bizVendorCode": "SD5379", "vehicleCode": "0", "packageType": 1, "skuId": 2704960}}, {"reference": {"bizVendorCode": "SD5727", "vehicleCode": "0", "packageType": 1, "skuId": 1972046}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 1854917}}, {"reference": {"bizVendorCode": "SD5486", "vehicleCode": "0", "packageType": 0, "skuId": 1976197}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1861254}}, {"reference": {"bizVendorCode": "13031", "vehicleCode": "0", "packageType": 1, "skuId": 152539}}, {"reference": {"bizVendorCode": "SD3012", "vehicleCode": "0", "packageType": 1, "skuId": 227977}}, {"reference": {"bizVendorCode": "SD4407", "vehicleCode": "0", "packageType": 1, "skuId": 1908499}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 1912422}}, {"reference": {"bizVendorCode": "SD3039", "vehicleCode": "0", "packageType": 1, "skuId": 787013}}, {"reference": {"bizVendorCode": "SD4057", "vehicleCode": "0", "packageType": 1, "skuId": 1863247}}, {"reference": {"bizVendorCode": "SD4849", "vehicleCode": "0", "packageType": 1, "skuId": 1913476}}, {"reference": {"bizVendorCode": "SD4734", "vehicleCode": "0", "packageType": 1, "skuId": 1916013}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 4345955}}, {"reference": {"bizVendorCode": "SD3942", "vehicleCode": "0", "packageType": 1, "skuId": 1860920}}], "reactId": "1434418930", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3746_0_107196_107196"]}, "minTPrice": 662.0, "minDPrice": 42.0, "modifySameVehicle": false, "minDOrinPrice": 48, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减60", "groupId": 1, "mergeId": 0}, "priceSize": 17, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5694", "sortNum": 1, "lowestPrice": 197.0, "highestPrice": 1592.0, "maximumRating": 5.0, "maximumCommentCount": 37057, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "13031", "vendorCode": "13031", "pStoreCode": "78", "rStoreCode": "78", "vehicleCode": "0", "packageId": "Secure", "packageType": 1, "comPriceCode": "****************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_NzhfNTY5NF8xXzIxOC4wXzE5NjIuMF8wLjBfMjM0Mi4wXzE5Ny4wXzIxNDUuMF8wXzBfMC4wXzAuMF8zNjAuMF8yMC4wXzBfMF8yMzQ2NzY5", "vendorVehicleCode": "18875", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 2346769, "pLev": 129, "rLev": 129, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1765.0, "amountStr": "¥1765", "subAmount": 197.0, "subAmountStr": "日均¥197", "originalDailyPrice": 218.0, "detail": [{"code": "1001", "name": "租车费", "amount": 1962.0, "amountDesc": "¥1962"}, {"code": "3642", "name": "酒店用户专享", "amount": 197, "amountDesc": "¥197"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360.0, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360.0, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2145.0, "amountStr": "¥2145", "subAmount": 2342.0, "subAmountStr": "¥2342", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3012", "vehicleCode": "0", "packageType": 1, "skuId": 2346797}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 2441604}}], "reactId": "1434418940", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["13031_0_78_78"]}, "minTPrice": 2145.0, "minDPrice": 197.0, "modifySameVehicle": false, "minDOrinPrice": 218.0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "酒店用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3642", "groupCode": "MarketGroup502", "amountTitle": "已减197", "groupId": 1, "mergeId": 0}, "priceSize": 3, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4088", "sortNum": 2, "lowestPrice": 176.0, "highestPrice": 2791.0, "maximumRating": 5.0, "maximumCommentCount": 21715, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD5048", "vendorCode": "82731", "pStoreCode": "115265", "rStoreCode": "115265", "vehicleCode": "0", "packageType": 0, "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTE1MjY1XzQwODhfMV8xOThfMTc4Ml8xOThfMjA3Mi4wMF8xNzkuMF8xODkzLjBfMF8wXzAuMF8wLjBfMjcwXzIwLjAwXzAuMDBfMC4wMF8xOTIyODA1", "vendorVehicleCode": "20091851", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1922805, "pLev": 49817, "rLev": 49817, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1603.0, "amountStr": "¥1603", "subAmount": 179.0, "subAmountStr": "日均¥179", "originalDailyPrice": 198, "detail": [{"code": "1001", "name": "租车费", "amount": 1782, "amountDesc": "¥1782"}, {"code": "3642", "name": "酒店用户专享", "amount": 179, "amountDesc": "¥179"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 270, "amountStr": "¥270", "detail": [{"code": "1002", "name": "基础服务费", "amount": 270, "amountDesc": "¥270", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1893.0, "amountStr": "¥1893", "subAmount": 2072.0, "subAmountStr": "¥2072", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3010", "vehicleCode": "0", "packageType": 0, "skuId": 4152853}}, {"reference": {"bizVendorCode": "SD5486", "vehicleCode": "0", "packageType": 0, "skuId": 2511359}}, {"reference": {"bizVendorCode": "SD5097", "vehicleCode": "0", "packageType": 0, "skuId": 1909365}}, {"reference": {"bizVendorCode": "SD4113", "vehicleCode": "0", "packageType": 1, "skuId": 1862319}}, {"reference": {"bizVendorCode": "SD5751", "vehicleCode": "0", "packageType": 0, "skuId": 3722175}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1863823}}, {"reference": {"bizVendorCode": "SD4950", "vehicleCode": "0", "packageType": 1, "skuId": 2502182}}, {"reference": {"bizVendorCode": "SD4671", "vehicleCode": "0", "packageType": 1, "skuId": 1918697}}, {"reference": {"bizVendorCode": "SD4491", "vehicleCode": "0", "packageType": 1, "skuId": 4353744}}, {"reference": {"bizVendorCode": "SD4724", "vehicleCode": "0", "packageType": 1, "skuId": 1919042}}, {"reference": {"bizVendorCode": "SD4507", "vehicleCode": "0", "packageType": 1, "skuId": 1907904}}, {"reference": {"bizVendorCode": "SD3068", "vehicleCode": "0", "packageType": 1, "skuId": 1772333}}, {"reference": {"bizVendorCode": "SD5922", "vehicleCode": "0", "packageType": 0, "skuId": 4152846}}, {"reference": {"bizVendorCode": "SD4394", "vehicleCode": "0", "packageType": 1, "skuId": 4153405}}, {"reference": {"bizVendorCode": "SD3942", "vehicleCode": "0", "packageType": 1, "skuId": 1860813}}, {"reference": {"bizVendorCode": "SD3846", "vehicleCode": "0", "packageType": 0, "skuId": 1904695}}, {"reference": {"bizVendorCode": "SD6414", "vehicleCode": "0", "packageType": 1, "skuId": 4153021}}, {"reference": {"bizVendorCode": "SD3009", "vehicleCode": "0", "packageType": 1, "skuId": 4153378}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 1, "skuId": 1914391}}, {"reference": {"bizVendorCode": "SD5418", "vehicleCode": "0", "packageType": 1, "skuId": 1976458}}, {"reference": {"bizVendorCode": "SD6556", "vehicleCode": "0", "packageType": 1, "skuId": 4153038}}, {"reference": {"bizVendorCode": "SD3987", "vehicleCode": "0", "packageType": 1, "skuId": 1864223}}, {"reference": {"bizVendorCode": "SD4579", "vehicleCode": "0", "packageType": 0, "skuId": 1922065}}], "reactId": "1434419000", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3010_0_104317_104317"]}, "minTPrice": 1893.0, "minDPrice": 179.0, "modifySameVehicle": false, "minDOrinPrice": 198, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "酒店用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3642", "groupCode": "MarketGroup502", "amountTitle": "已减179", "groupId": 1, "mergeId": 0}, "priceSize": 24, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "17561", "sortNum": 3, "lowestPrice": 151.0, "highestPrice": 4640.0, "maximumRating": 5.0, "maximumCommentCount": 32668, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD6517", "vendorCode": "69282", "pStoreCode": "116463", "rStoreCode": "116463", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTE2NDYzXzE3NTYxXzFfMTg4XzE2OTJfMTg4XzIwNzcuMDBfMTcwLjBfMTkwNy4wXzBfMF8wLjBfMC4wXzM2MF8yNS4wMF8wLjAwXzAuMDBfMzMyMjkzNw==", "vendorVehicleCode": "20029702", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 3322937, "pLev": 59039, "rLev": 59039, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1522.0, "amountStr": "¥1522", "subAmount": 170.0, "subAmountStr": "日均¥170", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 1692, "amountDesc": "¥1692"}, {"code": "3642", "name": "酒店用户专享", "amount": 170, "amountDesc": "¥170"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 25.0, "amountStr": "¥25", "detail": [{"code": "1003", "name": "车行手续费", "amount": 25.0, "amountDesc": "¥25", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1907.0, "amountStr": "¥1907", "subAmount": 2077.0, "subAmountStr": "¥2077", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD5413", "vehicleCode": "0", "packageType": 0, "skuId": 3322914}}, {"reference": {"bizVendorCode": "SD3847", "vehicleCode": "0", "packageType": 1, "skuId": 3294650}}, {"reference": {"bizVendorCode": "SD4724", "vehicleCode": "0", "packageType": 1, "skuId": 3322745}}, {"reference": {"bizVendorCode": "SD3010", "vehicleCode": "0", "packageType": 0, "skuId": 3322966}}, {"reference": {"bizVendorCode": "SD6355", "vehicleCode": "0", "packageType": 1, "skuId": 3294744}}, {"reference": {"bizVendorCode": "SD6556", "vehicleCode": "0", "packageType": 0, "skuId": 3322856}}, {"reference": {"bizVendorCode": "SD5379", "vehicleCode": "0", "packageType": 1, "skuId": 3284649}}, {"reference": {"bizVendorCode": "SD5538", "vehicleCode": "0", "packageType": 0, "skuId": 3291369}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 3285885}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 3312998}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 3295965}}, {"reference": {"bizVendorCode": "SD4678", "vehicleCode": "0", "packageType": 1, "skuId": 3291351}}, {"reference": {"bizVendorCode": "SD4671", "vehicleCode": "0", "packageType": 1, "skuId": 3290929}}, {"reference": {"bizVendorCode": "13031", "vehicleCode": "0", "packageType": 1, "skuId": 3281368}}, {"reference": {"bizVendorCode": "SD3012", "vehicleCode": "0", "packageType": 1, "skuId": 3281747}}, {"reference": {"bizVendorCode": "SD6119", "vehicleCode": "0", "packageType": 1, "skuId": 3284364}}, {"reference": {"bizVendorCode": "SD3225", "vehicleCode": "0", "packageType": 1, "skuId": 3322556}}, {"reference": {"bizVendorCode": "SD5592", "vehicleCode": "0", "packageType": 0, "skuId": 3283919}}, {"reference": {"bizVendorCode": "SD6628", "vehicleCode": "0", "packageType": 0, "skuId": 3323375}}, {"reference": {"bizVendorCode": "SD3942", "vehicleCode": "0", "packageType": 1, "skuId": 3294631}}, {"reference": {"bizVendorCode": "SD3009", "vehicleCode": "0", "packageType": 0, "skuId": 3281768}}, {"reference": {"bizVendorCode": "SD3987", "vehicleCode": "0", "packageType": 1, "skuId": 3294642}}, {"reference": {"bizVendorCode": "SD4003", "vehicleCode": "0", "packageType": 0, "skuId": 4508960}}, {"reference": {"bizVendorCode": "SD3637", "vehicleCode": "0", "packageType": 0, "skuId": 3323339}}, {"reference": {"bizVendorCode": "SD4130", "vehicleCode": "0", "packageType": 1, "skuId": 3291354}}], "reactId": "1434419001", "group": 870, "groupSort": 5, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3847_0_107699_107699"]}, "minTPrice": 1907.0, "minDPrice": 170.0, "modifySameVehicle": false, "minDOrinPrice": 188, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "酒店用户专享", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3642", "groupCode": "MarketGroup502", "amountTitle": "已减170", "groupId": 1, "mergeId": 0}, "priceSize": 26, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5282", "sortNum": 4, "lowestPrice": 151.0, "highestPrice": 1790.0, "maximumRating": 5.0, "maximumCommentCount": 34315, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD5573", "vendorCode": "82627", "pStoreCode": "117490", "rStoreCode": "117490", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_MTE3NDkwXzUyODJfMV8xODhfMTY5Ml8xODhfMjA3Mi4wMF8xNTEuMF8xNzMzLjBfMF8wXzAuMF8wLjBfMzYwXzIwLjAwXzBfMF8xOTYzODgy", "vendorVehicleCode": "20068007", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1963882, "pLev": -1, "rLev": -1, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1353.0, "amountStr": "¥1353", "subAmount": 151.0, "subAmountStr": "日均¥151", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 1692, "amountDesc": "¥1692"}, {"code": "3658", "name": "周租优惠", "amount": 339, "amountDesc": "¥339"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1733.0, "amountStr": "¥1733", "subAmount": 2072.0, "subAmountStr": "¥2072", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 1, "skuId": 1907963}}, {"reference": {"bizVendorCode": "SD5538", "vehicleCode": "0", "packageType": 0, "skuId": 1966252}}, {"reference": {"bizVendorCode": "SD6432", "vehicleCode": "0", "packageType": 0, "skuId": 1978439}}, {"reference": {"bizVendorCode": "SD4029", "vehicleCode": "0", "packageType": 1, "skuId": 1859305}}, {"reference": {"bizVendorCode": "SD5486", "vehicleCode": "0", "packageType": 0, "skuId": 1977720}}, {"reference": {"bizVendorCode": "SD3010", "vehicleCode": "0", "packageType": 0, "skuId": 772133}}, {"reference": {"bizVendorCode": "SD3747", "vehicleCode": "0", "packageType": 0, "skuId": 1856378}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 1861400}}, {"reference": {"bizVendorCode": "SD4407", "vehicleCode": "0", "packageType": 1, "skuId": 2710006}}, {"reference": {"bizVendorCode": "13031", "vehicleCode": "0", "packageType": 1, "skuId": 164855}}, {"reference": {"bizVendorCode": "SD3012", "vehicleCode": "0", "packageType": 1, "skuId": 228538}}, {"reference": {"bizVendorCode": "SD4507", "vehicleCode": "0", "packageType": 1, "skuId": 1907606}}, {"reference": {"bizVendorCode": "SD3039", "vehicleCode": "0", "packageType": 1, "skuId": 787021}}, {"reference": {"bizVendorCode": "SD4950", "vehicleCode": "0", "packageType": 0, "skuId": 1913706}}, {"reference": {"bizVendorCode": "SD6414", "vehicleCode": "0", "packageType": 1, "skuId": 1975497}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 1862341}}], "reactId": "1434419002", "group": 870, "groupSort": 4, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD5573_0_117490_117490"]}, "minTPrice": 1733.0, "minDPrice": 151.0, "modifySameVehicle": false, "minDOrinPrice": 188, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周租优惠", "category": 3, "type": 3, "code": "30", "sortNum": 0, "colorCode": "15", "labelCode": "3658", "groupCode": "MarketGroup1322", "amountTitle": "已减339", "groupId": 1, "mergeId": 0}, "priceSize": 17, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5524", "sortNum": 5, "lowestPrice": 151.0, "highestPrice": 4935.0, "maximumRating": 5.0, "maximumCommentCount": 37057, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4029", "vendorCode": "70695", "pStoreCode": "107360", "rStoreCode": "107360", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_MTA3MzYwXzU1MjRfMV8xODhfMTY5Ml8xODhfMjA3Mi4wMF8xNTEuMF8xNzMzLjBfMF8wXzAuMF8wLjBfMzYwXzIwLjAwXzAuMDBfMC4wMF8xODU1ODIy", "vendorVehicleCode": "20092228", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1855822, "pLev": 25629, "rLev": 25629, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1353.0, "amountStr": "¥1353", "subAmount": 151.0, "subAmountStr": "日均¥151", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 1692, "amountDesc": "¥1692"}, {"code": "3658", "name": "周租优惠", "amount": 339, "amountDesc": "¥339"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1733.0, "amountStr": "¥1733", "subAmount": 2072.0, "subAmountStr": "¥2072", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3271", "vehicleCode": "0", "packageType": 1, "skuId": 1817505}}, {"reference": {"bizVendorCode": "SD7027", "vehicleCode": "0", "packageType": 1, "skuId": 2317188}}, {"reference": {"bizVendorCode": "SD6517", "vehicleCode": "0", "packageType": 1, "skuId": 2990140}}, {"reference": {"bizVendorCode": "SD5413", "vehicleCode": "0", "packageType": 0, "skuId": 1977490}}, {"reference": {"bizVendorCode": "SD4406", "vehicleCode": "0", "packageType": 1, "skuId": 1911455}}, {"reference": {"bizVendorCode": "SD5727", "vehicleCode": "0", "packageType": 1, "skuId": 1973995}}, {"reference": {"bizVendorCode": "SD4615", "vehicleCode": "0", "packageType": 1, "skuId": 1920542}}, {"reference": {"bizVendorCode": "SD5592", "vehicleCode": "0", "packageType": 1, "skuId": 1962562}}, {"reference": {"bizVendorCode": "SD4724", "vehicleCode": "0", "packageType": 1, "skuId": 4163961}}, {"reference": {"bizVendorCode": "SD4407", "vehicleCode": "0", "packageType": 1, "skuId": 1915715}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1862418}}, {"reference": {"bizVendorCode": "SD4507", "vehicleCode": "0", "packageType": 1, "skuId": 1911692}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 1923447}}, {"reference": {"bizVendorCode": "SD4057", "vehicleCode": "0", "packageType": 1, "skuId": 1862356}}, {"reference": {"bizVendorCode": "SD3225", "vehicleCode": "0", "packageType": 1, "skuId": 2317172}}, {"reference": {"bizVendorCode": "SD5066", "vehicleCode": "0", "packageType": 1, "skuId": 1915918}}, {"reference": {"bizVendorCode": "SD3987", "vehicleCode": "0", "packageType": 1, "skuId": 1860337}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 1, "skuId": 2037636}}, {"reference": {"bizVendorCode": "SD4470", "vehicleCode": "0", "packageType": 1, "skuId": 1915745}}, {"reference": {"bizVendorCode": "SD3039", "vehicleCode": "0", "packageType": 1, "skuId": 1797469}}, {"reference": {"bizVendorCode": "SD3985", "vehicleCode": "0", "packageType": 1, "skuId": 1861722}}, {"reference": {"bizVendorCode": "SD5613", "vehicleCode": "0", "packageType": 1, "skuId": 4607952}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 1859354}}, {"reference": {"bizVendorCode": "SD4130", "vehicleCode": "0", "packageType": 1, "skuId": 2235671}}, {"reference": {"bizVendorCode": "SD6434", "vehicleCode": "0", "packageType": 0, "skuId": 4155616}}], "reactId": "1434419003", "group": 870, "groupSort": 1, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4029_0_107360_107360"]}, "minTPrice": 1733.0, "minDPrice": 151.0, "modifySameVehicle": false, "minDOrinPrice": 188, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周租优惠", "category": 3, "type": 3, "code": "30", "sortNum": 0, "colorCode": "15", "labelCode": "3658", "groupCode": "MarketGroup1322", "amountTitle": "已减339", "groupId": 1, "mergeId": 0}, "priceSize": 26, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5283", "sortNum": 6, "lowestPrice": 151.0, "highestPrice": 4935.0, "maximumRating": 5.0, "maximumCommentCount": 37057, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD5751", "vendorCode": "32538", "pStoreCode": "116092", "rStoreCode": "116092", "vehicleCode": "0", "packageType": 0, "comPriceCode": "[c]MzM0fDIyODgzLTA3fDIwMjAwOjAtMTggJjE4ODA6MDAyMDIzJiYxJDE5IDAtMDctOjAwJjA6MDAmMSQyMTg4JjA3LTIwMjMtOjAwOjAgMDA4OCYmMDAmMTIzLTAxJDIwIDAwOjctMjEwJjE4MDA6MCQyMDI4JiYxLTIyIDMtMDcwOjAwMDA6MCYmMSQmMTg4LTA3LTIwMjMwOjAwMjMgMDE4OCY6MDAmMDIzLSYxJDI0IDAwMDctMjAwJjE6MDA6MSQyMDg4JiY3LTI1MjMtMDAwOjAgMDA6OCYmMTAmMTgzLTA3JDIwMjAwOjAtMjYgJjE4ODA6MDB8MTAwJiYxJDE4OCYxJjkmJDEwMDE2OTIyMC4wMyYxJi4wMCQwJjIwJjkmNTEwMDIwJHwyMCY0NTA1LTAwMjMtOjM0OjYgMTQAAAAANDAAAA==", "priceVersion": "SH-PRICEVERSION_MTE2MDkyXzUyODNfMV8xODhfMTY5Ml8xODhfMjE2Mi4wMF8xNTEuMF8xODIzLjBfMF8wXzAuMF8wLjBfNDUwXzIwLjAwXzAuMDBfMC4wMF8yMjg4MzM0", "vendorVehicleCode": "20073109", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 2288334, "pLev": 56249, "rLev": 56249, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1353.0, "amountStr": "¥1353", "subAmount": 151.0, "subAmountStr": "日均¥151", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 1692, "amountDesc": "¥1692"}, {"code": "3658", "name": "周租优惠", "amount": 339, "amountDesc": "¥339"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 450, "amountStr": "¥450", "detail": [{"code": "1002", "name": "基础服务费", "amount": 450, "amountDesc": "¥450", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1823.0, "amountStr": "¥1823", "subAmount": 2162.0, "subAmountStr": "¥2162", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4113", "vehicleCode": "0", "packageType": 1, "skuId": 1860849}}, {"reference": {"bizVendorCode": "SD5566", "vehicleCode": "0", "packageType": 1, "skuId": 1978079}}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 0, "skuId": 2848544}}, {"reference": {"bizVendorCode": "SD3766", "vehicleCode": "0", "packageType": 0, "skuId": 1858492}}, {"reference": {"bizVendorCode": "SD6275", "vehicleCode": "0", "packageType": 1, "skuId": 1967121}}, {"reference": {"bizVendorCode": "SD6432", "vehicleCode": "0", "packageType": 0, "skuId": 1977696}}, {"reference": {"bizVendorCode": "SD5486", "vehicleCode": "0", "packageType": 0, "skuId": 1976057}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1861024}}, {"reference": {"bizVendorCode": "SD5592", "vehicleCode": "0", "packageType": 1, "skuId": 1959233}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 4283560}}, {"reference": {"bizVendorCode": "13031", "vehicleCode": "0", "packageType": 1, "skuId": 172986}}, {"reference": {"bizVendorCode": "SD3012", "vehicleCode": "0", "packageType": 1, "skuId": 751659}}, {"reference": {"bizVendorCode": "SD4507", "vehicleCode": "0", "packageType": 1, "skuId": 1909290}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 2036878}}, {"reference": {"bizVendorCode": "SD6175", "vehicleCode": "0", "packageType": 0, "skuId": 1977080}}, {"reference": {"bizVendorCode": "SD3010", "vehicleCode": "0", "packageType": 1, "skuId": 747663}}, {"reference": {"bizVendorCode": "SD4029", "vehicleCode": "0", "packageType": 1, "skuId": 1860061}}, {"reference": {"bizVendorCode": "SD4057", "vehicleCode": "0", "packageType": 1, "skuId": 1861007}}, {"reference": {"bizVendorCode": "SD4949", "vehicleCode": "0", "packageType": 1, "skuId": 2551088}}, {"reference": {"bizVendorCode": "SD3846", "vehicleCode": "0", "packageType": 0, "skuId": 1887537}}, {"reference": {"bizVendorCode": "SD4470", "vehicleCode": "0", "packageType": 1, "skuId": 1913387}}, {"reference": {"bizVendorCode": "SD3009", "vehicleCode": "0", "packageType": 1, "skuId": 1835838}}, {"reference": {"bizVendorCode": "SD3987", "vehicleCode": "0", "packageType": 1, "skuId": 1861557}}, {"reference": {"bizVendorCode": "SD4224", "vehicleCode": "0", "packageType": 1, "skuId": 1919932}}, {"reference": {"bizVendorCode": "SD4407", "vehicleCode": "0", "packageType": 1, "skuId": 2705043}}, {"reference": {"bizVendorCode": "SD4102", "vehicleCode": "0", "packageType": 1, "skuId": 1858371}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 1857951}}, {"reference": {"bizVendorCode": "SD4130", "vehicleCode": "0", "packageType": 1, "skuId": 1859655}}, {"reference": {"bizVendorCode": "SD6434", "vehicleCode": "0", "packageType": 1, "skuId": 1978284}}], "reactId": "1434419004", "group": 870, "groupSort": 3, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD5751_0_116092_116092"]}, "minTPrice": 1823.0, "minDPrice": 151.0, "modifySameVehicle": false, "minDOrinPrice": 188, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周租优惠", "category": 3, "type": 3, "code": "30", "sortNum": 0, "colorCode": "15", "labelCode": "3658", "groupCode": "MarketGroup1322", "amountTitle": "已减339", "groupId": 1, "mergeId": 0}, "priceSize": 30, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "17391", "sortNum": 7, "lowestPrice": 172.0, "highestPrice": 2600.0, "maximumRating": 5.0, "maximumCommentCount": 34315, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4579", "vendorCode": "69287", "pStoreCode": "114768", "rStoreCode": "114768", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTE0NzY4XzE3MzkxXzFfMjE1XzE5MzVfMjE1XzIzMTUuMDBfMTcyLjBfMTkyOC4wXzBfMF8wLjBfMC4wXzM2MF8yMC4wMF8wLjAwXzAuMDBfNDU2NDI0NA==", "vendorVehicleCode": "20655733", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 4564244, "pLev": 45746, "rLev": 45746, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1548.0, "amountStr": "¥1548", "subAmount": 172.0, "subAmountStr": "日均¥172", "originalDailyPrice": 215, "detail": [{"code": "1001", "name": "租车费", "amount": 1935, "amountDesc": "¥1935"}, {"code": "3658", "name": "周租优惠", "amount": 387, "amountDesc": "¥387"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1928.0, "amountStr": "¥1928", "subAmount": 2315.0, "subAmountStr": "¥2315", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD7027", "vehicleCode": "0", "packageType": 1, "skuId": 2317191}}, {"reference": {"bizVendorCode": "SD4615", "vehicleCode": "0", "packageType": 1, "skuId": 4014968}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 2428038}}, {"reference": {"bizVendorCode": "SD6517", "vehicleCode": "0", "packageType": 1, "skuId": 2990473}}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 0, "skuId": 2706799}}, {"reference": {"bizVendorCode": "SD4416", "vehicleCode": "0", "packageType": 1, "skuId": 2457377}}, {"reference": {"bizVendorCode": "SD6628", "vehicleCode": "0", "packageType": 0, "skuId": 2511816}}, {"reference": {"bizVendorCode": "SD4102", "vehicleCode": "0", "packageType": 1, "skuId": 4039238}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 1, "skuId": 2428399}}, {"reference": {"bizVendorCode": "SD3987", "vehicleCode": "0", "packageType": 1, "skuId": 3868574}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 2428547}}], "reactId": "1434419005", "group": 870, "groupSort": 2, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4579_0_114768_114768"]}, "minTPrice": 1928.0, "minDPrice": 172.0, "modifySameVehicle": false, "minDOrinPrice": 215, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周租优惠", "category": 3, "type": 3, "code": "30", "sortNum": 0, "colorCode": "15", "labelCode": "3658", "groupCode": "MarketGroup1322", "amountTitle": "已减387", "groupId": 1, "mergeId": 0}, "priceSize": 12, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4663", "sortNum": 25, "lowestPrice": 712.0, "highestPrice": 712.0, "maximumRating": 5.0, "maximumCommentCount": 164, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3009", "vendorCode": "13037", "pStoreCode": "49033", "rStoreCode": "49033", "vehicleCode": "0", "packageType": 0, "priceVersion": "SH-PRICEVERSION_NDkwMzNfNDY2M18xXzc3Ny4wXzY5OTMuMF8wLjBfNzM4OC4wXzcxMi4wXzY4MDAuMF8wXzBfMC4wXzAuMF8zNjAuMF8zNS4wXzBfMF8yMjk1MTE=", "vendorVehicleCode": "2867", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 229511, "pLev": 8918, "rLev": 8918, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 6405.0, "amountStr": "¥6405", "subAmount": 712.0, "subAmountStr": "日均¥712", "originalDailyPrice": 777.0, "detail": [{"code": "1001", "name": "租车费", "amount": 6993.0, "amountDesc": "¥6993"}, {"code": "11037", "name": "优惠券", "amount": 588, "amountDesc": "¥588"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360.0, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360.0, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 6800.0, "amountStr": "¥6800", "subAmount": 7388.0, "subAmountStr": "¥7388", "currencyCode": "¥"}]}], "reactId": "14344190012", "group": 870, "groupSort": 6, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3009_0_49033_49033"]}, "minTPrice": 6800.0, "minDPrice": 712.0, "modifySameVehicle": false, "minDOrinPrice": 777.0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减588", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isOptim": false, "isEasy": false, "isCredit": true, "rCoup": 0, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5281", "sortNum": 8, "lowestPrice": 151.0, "highestPrice": 4935.0, "maximumRating": 5.0, "maximumCommentCount": 34315, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4988", "vendorCode": "82163", "pStoreCode": "115211", "rStoreCode": "115211", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_MTE1MjExXzUyODFfMV8xODhfMTY5Ml8xODhfMjA3Mi4wMF8xNjAuMF8xODE4LjBfMF8wXzAuMF8wLjBfMzYwXzIwLjAwXzAuMDBfMC4wMF8xOTEzMzI0", "vendorVehicleCode": "20068001", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1913324, "pLev": 49274, "rLev": 49274, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1438.0, "amountStr": "¥1438", "subAmount": 160.0, "subAmountStr": "日均¥160", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 1692, "amountDesc": "¥1692"}, {"code": "3658", "name": "周租优惠", "amount": 254, "amountDesc": "¥254"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1818.0, "amountStr": "¥1818", "subAmount": 2072.0, "subAmountStr": "¥2072", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD5922", "vehicleCode": "0", "packageType": 0, "skuId": 1973050}}, {"reference": {"bizVendorCode": "SD3847", "vehicleCode": "0", "packageType": 1, "skuId": 1855249}}, {"reference": {"bizVendorCode": "SD6275", "vehicleCode": "0", "packageType": 1, "skuId": 2711352}}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 0, "skuId": 2061882}}, {"reference": {"bizVendorCode": "SD5538", "vehicleCode": "0", "packageType": 1, "skuId": 1966251}}, {"reference": {"bizVendorCode": "SD5486", "vehicleCode": "0", "packageType": 0, "skuId": 1977722}}, {"reference": {"bizVendorCode": "SD3766", "vehicleCode": "0", "packageType": 0, "skuId": 1859599}}, {"reference": {"bizVendorCode": "SD3746", "vehicleCode": "0", "packageType": 1, "skuId": 1858159}}, {"reference": {"bizVendorCode": "SD6024", "vehicleCode": "0", "packageType": 1, "skuId": 1974468}}, {"reference": {"bizVendorCode": "SD3747", "vehicleCode": "0", "packageType": 1, "skuId": 1856379}}, {"reference": {"bizVendorCode": "SD4397", "vehicleCode": "0", "packageType": 1, "skuId": 1916048}}, {"reference": {"bizVendorCode": "SD4029", "vehicleCode": "0", "packageType": 1, "skuId": 1859304}}, {"reference": {"bizVendorCode": "SD4949", "vehicleCode": "0", "packageType": 1, "skuId": 2428595}}, {"reference": {"bizVendorCode": "SD3010", "vehicleCode": "0", "packageType": 1, "skuId": 747662}}, {"reference": {"bizVendorCode": "SD6414", "vehicleCode": "0", "packageType": 1, "skuId": 1975494}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 1, "skuId": 2375336}}, {"reference": {"bizVendorCode": "SD5418", "vehicleCode": "0", "packageType": 1, "skuId": 1960320}}, {"reference": {"bizVendorCode": "SD4394", "vehicleCode": "0", "packageType": 1, "skuId": 1915304}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 1862340}}, {"reference": {"bizVendorCode": "SD6434", "vehicleCode": "0", "packageType": 1, "skuId": 1979986}}], "reactId": "1434419006", "group": 820, "groupSort": 1, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3847_0_107699_107699"]}, "minTPrice": 1818.0, "minDPrice": 160.0, "modifySameVehicle": false, "minDOrinPrice": 188, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周租优惠", "category": 3, "type": 3, "code": "30", "sortNum": 0, "colorCode": "15", "labelCode": "3658", "groupCode": "MarketGroup1322", "amountTitle": "已减254", "groupId": 1, "mergeId": 0}, "priceSize": 21, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5374", "sortNum": 10, "lowestPrice": 166.0, "highestPrice": 4935.0, "maximumRating": 5.0, "maximumCommentCount": 34315, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3271", "vendorCode": "84185", "pStoreCode": "106218", "rStoreCode": "106218", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_MTA2MjE4XzUzNzRfMV8xODhfMTY5Ml8xODhfMjA3Mi4wMF8xNzAuMF8xOTAyLjBfMF8wXzAuMF8wLjBfMzYwXzIwLjAwXzAuMDBfMC4wMF8yNDQxNjUz", "vendorVehicleCode": "20074605", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 2441653, "pLev": 17890, "rLev": 17890, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1522.0, "amountStr": "¥1522", "subAmount": 170.0, "subAmountStr": "日均¥170", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 1692, "amountDesc": "¥1692"}, {"code": "3658", "name": "周租优惠", "amount": 170, "amountDesc": "¥170"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1902.0, "amountStr": "¥1902", "subAmount": 2072.0, "subAmountStr": "¥2072", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3746", "vehicleCode": "0", "packageType": 1, "skuId": 3908172}}, {"reference": {"bizVendorCode": "SD3847", "vehicleCode": "0", "packageType": 1, "skuId": 1887993}}, {"reference": {"bizVendorCode": "SD3010", "vehicleCode": "0", "packageType": 1, "skuId": 747664}}, {"reference": {"bizVendorCode": "SD5486", "vehicleCode": "0", "packageType": 0, "skuId": 1960040}}, {"reference": {"bizVendorCode": "SD5922", "vehicleCode": "0", "packageType": 0, "skuId": 1967816}}, {"reference": {"bizVendorCode": "SD5097", "vehicleCode": "0", "packageType": 1, "skuId": 1918759}}, {"reference": {"bizVendorCode": "SD5751", "vehicleCode": "0", "packageType": 0, "skuId": 3722231}}, {"reference": {"bizVendorCode": "SD5592", "vehicleCode": "0", "packageType": 1, "skuId": 2317091}}, {"reference": {"bizVendorCode": "SD4416", "vehicleCode": "0", "packageType": 1, "skuId": 1917329}}, {"reference": {"bizVendorCode": "SD4029", "vehicleCode": "0", "packageType": 1, "skuId": 1862135}}, {"reference": {"bizVendorCode": "SD4057", "vehicleCode": "0", "packageType": 1, "skuId": 1863002}}, {"reference": {"bizVendorCode": "SD4949", "vehicleCode": "0", "packageType": 1, "skuId": 1920882}}, {"reference": {"bizVendorCode": "SD6558", "vehicleCode": "0", "packageType": 1, "skuId": 1969691}}, {"reference": {"bizVendorCode": "SD4615", "vehicleCode": "0", "packageType": 1, "skuId": 1921665}}, {"reference": {"bizVendorCode": "SD6355", "vehicleCode": "0", "packageType": 0, "skuId": 1963211}}, {"reference": {"bizVendorCode": "SD5418", "vehicleCode": "0", "packageType": 1, "skuId": 1965162}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 1865525}}, {"reference": {"bizVendorCode": "SD4130", "vehicleCode": "0", "packageType": 1, "skuId": 1854620}}, {"reference": {"bizVendorCode": "SD6434", "vehicleCode": "0", "packageType": 0, "skuId": 1972918}}], "reactId": "1434419008", "group": 820, "groupSort": 4, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3847_0_107699_107699"]}, "minTPrice": 1902.0, "minDPrice": 170.0, "modifySameVehicle": false, "minDOrinPrice": 188, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周租优惠", "category": 3, "type": 3, "code": "30", "sortNum": 0, "colorCode": "15", "labelCode": "3658", "groupCode": "MarketGroup1322", "amountTitle": "已减170", "groupId": 1, "mergeId": 0}, "priceSize": 20, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5526", "sortNum": 9, "lowestPrice": 170.0, "highestPrice": 7111.0, "maximumRating": 5.0, "maximumCommentCount": 34315, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3271", "vendorCode": "84185", "pStoreCode": "106218", "rStoreCode": "106218", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_MTA2MjE4XzU1MjZfMV8xODhfMTY5Ml8xODhfMjA3Mi4wMF8xNzAuMF8xOTAyLjBfMF8wXzAuMF8wLjBfMzYwXzIwLjAwXzAuMDBfMC4wMF8xODE3NTAx", "vendorVehicleCode": "20092274", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1817501, "pLev": 17890, "rLev": 17890, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1522.0, "amountStr": "¥1522", "subAmount": 170.0, "subAmountStr": "日均¥170", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 1692, "amountDesc": "¥1692"}, {"code": "3658", "name": "周租优惠", "amount": 170, "amountDesc": "¥170"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1902.0, "amountStr": "¥1902", "subAmount": 2072.0, "subAmountStr": "¥2072", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4406", "vehicleCode": "0", "packageType": 1, "skuId": 1911553}}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 0, "skuId": 2441745}}, {"reference": {"bizVendorCode": "SD5486", "vehicleCode": "0", "packageType": 0, "skuId": 2237960}}, {"reference": {"bizVendorCode": "SD5538", "vehicleCode": "0", "packageType": 1, "skuId": 1978982}}, {"reference": {"bizVendorCode": "SD4029", "vehicleCode": "0", "packageType": 1, "skuId": 1861483}}, {"reference": {"bizVendorCode": "SD5592", "vehicleCode": "0", "packageType": 1, "skuId": 1973535}}, {"reference": {"bizVendorCode": "SD5727", "vehicleCode": "0", "packageType": 1, "skuId": 3420803}}, {"reference": {"bizVendorCode": "SD4507", "vehicleCode": "0", "packageType": 1, "skuId": 1921852}}, {"reference": {"bizVendorCode": "SD6796", "vehicleCode": "0", "packageType": 0, "skuId": 1976881}}, {"reference": {"bizVendorCode": "SD4615", "vehicleCode": "0", "packageType": 1, "skuId": 1920524}}, {"reference": {"bizVendorCode": "SD3009", "vehicleCode": "0", "packageType": 1, "skuId": 3021399}}, {"reference": {"bizVendorCode": "SD4416", "vehicleCode": "0", "packageType": 1, "skuId": 1910627}}, {"reference": {"bizVendorCode": "SD4057", "vehicleCode": "0", "packageType": 1, "skuId": 1862347}}, {"reference": {"bizVendorCode": "SD4949", "vehicleCode": "0", "packageType": 1, "skuId": 3408302}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 1, "skuId": 1917429}}, {"reference": {"bizVendorCode": "SD5418", "vehicleCode": "0", "packageType": 1, "skuId": 1978864}}, {"reference": {"bizVendorCode": "SD3985", "vehicleCode": "0", "packageType": 1, "skuId": 2704771}}, {"reference": {"bizVendorCode": "SD5613", "vehicleCode": "0", "packageType": 1, "skuId": 4548622}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 1864892}}, {"reference": {"bizVendorCode": "SD6574", "vehicleCode": "0", "packageType": 0, "skuId": 1965290}}, {"reference": {"bizVendorCode": "SD4130", "vehicleCode": "0", "packageType": 1, "skuId": 1855387}}], "reactId": "1434419007", "group": 820, "groupSort": 3, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3271_0_106218_106218"]}, "minTPrice": 1902.0, "minDPrice": 170.0, "modifySameVehicle": false, "minDOrinPrice": 188, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周租优惠", "category": 3, "type": 3, "code": "30", "sortNum": 0, "colorCode": "15", "labelCode": "3658", "groupCode": "MarketGroup1322", "amountTitle": "已减170", "groupId": 1, "mergeId": 0}, "priceSize": 22, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "17390", "sortNum": 11, "lowestPrice": 175.0, "highestPrice": 2330.0, "maximumRating": 5.0, "maximumCommentCount": 34315, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4579", "vendorCode": "69287", "pStoreCode": "114768", "rStoreCode": "114768", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTE0NzY4XzE3MzkwXzFfMjE4XzE5NjJfMjE4XzIzNTcuMDBfMTc1LjBfMTk2NC4wXzBfMF8wLjBfMC4wXzM2MF8zNS4wMF8wLjAwXzAuMDBfMjUxMTUwNA==", "vendorVehicleCode": "20655751", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 2511504, "pLev": 45746, "rLev": 45746, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1569.0, "amountStr": "¥1569", "subAmount": 175.0, "subAmountStr": "日均¥175", "originalDailyPrice": 218, "detail": [{"code": "1001", "name": "租车费", "amount": 1962, "amountDesc": "¥1962"}, {"code": "3658", "name": "周租优惠", "amount": 393, "amountDesc": "¥393"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1964.0, "amountStr": "¥1964", "subAmount": 2357.0, "subAmountStr": "¥2357", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4615", "vehicleCode": "0", "packageType": 1, "skuId": 2490812}}, {"reference": {"bizVendorCode": "SD5486", "vehicleCode": "0", "packageType": 0, "skuId": 4371418}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1942892}}, {"reference": {"bizVendorCode": "SD4416", "vehicleCode": "0", "packageType": 1, "skuId": 3881393}}, {"reference": {"bizVendorCode": "SD4333", "vehicleCode": "0", "packageType": 1, "skuId": 2370990}}, {"reference": {"bizVendorCode": "SD4755", "vehicleCode": "0", "packageType": 1, "skuId": 2288656}}, {"reference": {"bizVendorCode": "SD3870", "vehicleCode": "0", "packageType": 1, "skuId": 2457246}}], "reactId": "1434419009", "group": 820, "groupSort": 2, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4579_0_114768_114768"]}, "minTPrice": 1964.0, "minDPrice": 175.0, "modifySameVehicle": false, "minDOrinPrice": 218, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周租优惠", "category": 3, "type": 3, "code": "30", "sortNum": 0, "colorCode": "15", "labelCode": "3658", "groupCode": "MarketGroup1322", "amountTitle": "已减393", "groupId": 1, "mergeId": 0}, "priceSize": 8, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4660", "sortNum": 12, "lowestPrice": 177.0, "highestPrice": 4935.0, "maximumRating": 5.0, "maximumCommentCount": 32668, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3746", "vendorCode": "32498", "pStoreCode": "107196", "rStoreCode": "107196", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]NDc3fDQxMDV8MjAyMC4wMC0xOCAzLTA3MDowMDAwOjAmJjEkJjE4OC0wNy0yMDIzMDowMDE5IDAxODgmOjAwJjAyMy0mMSQyMCAwMDA3LTIwMCYxOjAwOjEkMjA4OCYmNy0yMTIzLTAwMDowIDAwOjgmJjEwJjE4My0wNyQyMDIwMDowLTIyICYxODgwOjAwMjAyMyYmMSQyMyAwLTA3LTowMCYwOjAwJjEkMjE4OCYwNy0yMDIzLTowMDo0IDAwODgmJjAwJjEyMy0wMSQyMCAwMDo3LTI1MCYxODAwOjAkMjAyOCYmMS0yNiAzLTA3MDowMDAwOjAmJjEkJjE4ODEmOSZ8MTAwMTY5MjE4OCYzJjEmJDEwMDAmMjAyMC4wMTAwMi4wMCQwJjQ1JjkmNTAyMy0wJHwyNiAxNDA1LTA0MAAAOjM0Og==", "priceVersion": "SH-PRICEVERSION_MTA3MTk2XzQ2NjBfMV8xODhfMTY5Ml8xODhfMjE2Mi4wMF8xNzcuMF8yMDYyLjBfMF8wXzAuMF8wLjBfNDUwXzIwLjAwXzAuMDBfMC4wMF80MTA1NDc3", "vendorVehicleCode": "20068010", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 4105477, "pLev": 23111, "rLev": 23111, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1592.0, "amountStr": "¥1592", "subAmount": 177.0, "subAmountStr": "日均¥177", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 1692, "amountDesc": "¥1692"}, {"code": "11037", "name": "优惠券", "amount": 100, "amountDesc": "¥100"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 450, "amountStr": "¥450", "detail": [{"code": "1002", "name": "基础服务费", "amount": 450, "amountDesc": "¥450", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2062.0, "amountStr": "¥2062", "subAmount": 2162.0, "subAmountStr": "¥2162", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 0, "skuId": 1957756}}, {"reference": {"bizVendorCode": "SD3010", "vehicleCode": "0", "packageType": 0, "skuId": 247445}}, {"reference": {"bizVendorCode": "SD5486", "vehicleCode": "0", "packageType": 0, "skuId": 1977730}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1856106}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 2550751}}, {"reference": {"bizVendorCode": "SD4470", "vehicleCode": "0", "packageType": 1, "skuId": 1922956}}, {"reference": {"bizVendorCode": "SD3225", "vehicleCode": "0", "packageType": 1, "skuId": 1817509}}, {"reference": {"bizVendorCode": "SD4416", "vehicleCode": "0", "packageType": 1, "skuId": 1911827}}, {"reference": {"bizVendorCode": "SD6119", "vehicleCode": "0", "packageType": 1, "skuId": 1969664}}, {"reference": {"bizVendorCode": "SD3009", "vehicleCode": "0", "packageType": 1, "skuId": 231360}}, {"reference": {"bizVendorCode": "SD5613", "vehicleCode": "0", "packageType": 1, "skuId": 3979787}}, {"reference": {"bizVendorCode": "SD5075", "vehicleCode": "0", "packageType": 0, "skuId": 1912054}}], "reactId": "14344190010", "group": 820, "groupSort": 5, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3746_0_107196_107196"]}, "minTPrice": 2062.0, "minDPrice": 177.0, "modifySameVehicle": false, "minDOrinPrice": 188, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}, "priceSize": 13, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4659", "sortNum": 13, "lowestPrice": 177.0, "highestPrice": 1700.0, "maximumRating": 5.0, "maximumCommentCount": 21715, "lowestDistance": 0.1, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD5413", "vendorCode": "65452", "pStoreCode": "116361", "rStoreCode": "116361", "vehicleCode": "0", "packageType": 0, "comPriceCode": "[c]NDI4fDE5NzUzLTA3fDIwMjAwOjAtMTggJjE4ODA6MDAyMDIzJiYxJDE5IDAtMDctOjAwJjA6MDAmMSQyMTg4JjA3LTIwMjMtOjAwOjAgMDA4OCYmMDAmMTIzLTAxJDIwIDAwOjctMjEwJjE4MDA6MCQyMDI4JiYxLTIyIDMtMDcwOjAwMDA6MCYmMSQmMTg4LTA3LTIwMjMwOjAwMjMgMDE4OCY6MDAmMDIzLSYxJDI0IDAwMDctMjAwJjE6MDA6MSQyMDg4JiY3LTI1MjMtMDAwOjAgMDA6OCYmMTAmMTgzLTA3JDIwMjAwOjAtMjYgJjE4ODA6MDB8MTAwJiYxJDE4OCYxJjkmJDEwMDE2OTIzNS4wMyYxJi4wMCQwJjM1JjkmNTEwMDIwJHwyMCY0NTA1LTAwMjMtOjM0OjYgMTQAAAAANDAAAA==", "priceVersion": "SH-PRICEVERSION_MTE2MzYxXzQ2NTlfMV8xODhfMTY5Ml8xODhfMjE3Ny4wMF8xNzcuMF8yMDc3LjBfMF8wXzAuMF8wLjBfNDUwXzM1LjAwXzAuMDBfMC4wMF8xOTc1NDI4", "vendorVehicleCode": "20003271", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1975428, "pLev": 58362, "rLev": 58362, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 1592.0, "amountStr": "¥1592", "subAmount": 177.0, "subAmountStr": "日均¥177", "originalDailyPrice": 188, "detail": [{"code": "1001", "name": "租车费", "amount": 1692, "amountDesc": "¥1692"}, {"code": "11037", "name": "优惠券", "amount": 100, "amountDesc": "¥100"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 450, "amountStr": "¥450", "detail": [{"code": "1002", "name": "基础服务费", "amount": 450, "amountDesc": "¥450", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 2077.0, "amountStr": "¥2077", "subAmount": 2177.0, "subAmountStr": "¥2177", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD6556", "vehicleCode": "0", "packageType": 0, "skuId": 1969657}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1856096}}, {"reference": {"bizVendorCode": "SD5943", "vehicleCode": "0", "packageType": 0, "skuId": 1974551}}, {"reference": {"bizVendorCode": "SD3225", "vehicleCode": "0", "packageType": 1, "skuId": 1816676}}, {"reference": {"bizVendorCode": "SD4224", "vehicleCode": "0", "packageType": 1, "skuId": 1916882}}, {"reference": {"bizVendorCode": "SD3009", "vehicleCode": "0", "packageType": 0, "skuId": 231143}}, {"reference": {"bizVendorCode": "SD3637", "vehicleCode": "0", "packageType": 0, "skuId": 2428327}}], "reactId": "14344190011", "group": 820, "groupSort": 6, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD5413_0_116361_116361"]}, "minTPrice": 2077.0, "minDPrice": 177.0, "modifySameVehicle": false, "minDOrinPrice": 188, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减100", "groupId": 1, "mergeId": 0}, "priceSize": 8, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4662", "sortNum": 26, "lowestPrice": 734.0, "highestPrice": 734.0, "maximumRating": 5.0, "maximumCommentCount": 164, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3009", "vendorCode": "13037", "pStoreCode": "49033", "rStoreCode": "49033", "vehicleCode": "0", "packageType": 0, "priceVersion": "SH-PRICEVERSION_NDkwMzNfNDY2Ml8xXzc5OS4wXzcxOTEuMF8wLjBfNzU4Ni4wXzczNC4wXzY5OTguMF8wXzBfMC4wXzAuMF8zNjAuMF8zNS4wXzBfMF8yMjk5OTQ=", "vendorVehicleCode": "571", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 229994, "pLev": 8918, "rLev": 8918, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 6603.0, "amountStr": "¥6603", "subAmount": 734.0, "subAmountStr": "日均¥734", "originalDailyPrice": 799.0, "detail": [{"code": "1001", "name": "租车费", "amount": 7191.0, "amountDesc": "¥7191"}, {"code": "11037", "name": "优惠券", "amount": 588, "amountDesc": "¥588"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 360.0, "amountStr": "¥360", "detail": [{"code": "1002", "name": "基础服务费", "amount": 360.0, "amountDesc": "¥360", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 6998.0, "amountStr": "¥6998", "subAmount": 7586.0, "subAmountStr": "¥7586", "currencyCode": "¥"}]}], "reactId": "14344190013", "group": 820, "groupSort": 7, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3009_0_49033_49033"]}, "minTPrice": 6998.0, "minDPrice": 734.0, "modifySameVehicle": false, "minDOrinPrice": 799.0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "券", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3769", "groupCode": "MarketGroup1317", "amountTitle": "已减588", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isOptim": false, "isEasy": false, "isCredit": true, "rCoup": 0, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4067", "sortNum": 14, "lowestPrice": 87.0, "highestPrice": 809.0, "maximumRating": 5.0, "maximumCommentCount": 37057, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD5536", "vendorCode": "81527", "pStoreCode": "117181", "rStoreCode": "117181", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]MDA4fDE5NjR8MjAyMC4wMC0xOCAzLTA3MDowMDAwOjAmJjEkJjEwOC0wNy0yMDIzMDowMDE5IDAxMDgmOjAwJjAyMy0mMSQyMCAwMDA3LTIwMCYxOjAwOjEkMjAwOCYmNy0yMTIzLTAwMDowIDAwOjgmJjEwJjEwMy0wNyQyMDIwMDowLTIyICYxMDgwOjAwMjAyMyYmMSQyMyAwLTA3LTowMCYwOjAwJjEkMjEwOCYwNy0yMDIzLTowMDo0IDAwMDgmJjAwJjEyMy0wMSQyMCAwMDo3LTI1MCYxMDAwOjAkMjAyOCYmMS0yNiAzLTA3MDowMDAwOjAmJjEkJjEwODEmOSZ8MTAwOTcyJDEwOCYmMSYyMTAwMyYyMC4wLjAwMDAyJjAwJDEmMjcwOSYzMDIzLTAkfDIwIDE0OjUtMDYwAAAAMzQ6NA==", "priceVersion": "SH-PRICEVERSION_MTE3MTgxXzQwNjdfMV8xMDhfOTcyXzEwOF8xMjYyLjAwXzg3LjBfMTA2Ny4wXzBfMF8wLjBfMC4wXzI3MF8yMC4wMF8wLjAwXzAuMDBfMTk2NDAwOA==", "vendorVehicleCode": "20085823", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1964008, "pLev": 62041, "rLev": 62041, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 777.0, "amountStr": "¥777", "subAmount": 87.0, "subAmountStr": "日均¥87", "originalDailyPrice": 108, "detail": [{"code": "1001", "name": "租车费", "amount": 972, "amountDesc": "¥972"}, {"code": "3658", "name": "周租优惠", "amount": 195, "amountDesc": "¥195"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 270, "amountStr": "¥270", "detail": [{"code": "1002", "name": "基础服务费", "amount": 270, "amountDesc": "¥270", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 1067.0, "amountStr": "¥1067", "subAmount": 1262.0, "subAmountStr": "¥1262", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD5727", "vehicleCode": "0", "packageType": 1, "skuId": 1964054}}, {"reference": {"bizVendorCode": "SD3977", "vehicleCode": "0", "packageType": 1, "skuId": 1861422}}, {"reference": {"bizVendorCode": "SD3866", "vehicleCode": "0", "packageType": 1, "skuId": 1856767}}, {"reference": {"bizVendorCode": "SD5048", "vehicleCode": "0", "packageType": 0, "skuId": 1920948}}, {"reference": {"bizVendorCode": "SD6991", "vehicleCode": "0", "packageType": 0, "skuId": 3166151}}, {"reference": {"bizVendorCode": "SD8256", "vehicleCode": "0", "packageType": 1, "skuId": 1856407}}, {"reference": {"bizVendorCode": "13031", "vehicleCode": "0", "packageType": 1, "skuId": 77282}}, {"reference": {"bizVendorCode": "SD3012", "vehicleCode": "0", "packageType": 1, "skuId": 226171}}, {"reference": {"bizVendorCode": "SD4201", "vehicleCode": "0", "packageType": 1, "skuId": 1907908}}, {"reference": {"bizVendorCode": "SD3847", "vehicleCode": "0", "packageType": 0, "skuId": 2012269}}, {"reference": {"bizVendorCode": "SD3039", "vehicleCode": "0", "packageType": 1, "skuId": 1067851}}, {"reference": {"bizVendorCode": "SD3637", "vehicleCode": "0", "packageType": 1, "skuId": 2375856}}, {"reference": {"bizVendorCode": "SD4919", "vehicleCode": "0", "packageType": 1, "skuId": 3054112}}], "reactId": "1434418931", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD5536_0_117181_117181"]}, "minTPrice": 1067.0, "minDPrice": 87.0, "modifySameVehicle": false, "minDOrinPrice": 108, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周租优惠", "category": 3, "type": 3, "code": "30", "sortNum": 0, "colorCode": "15", "labelCode": "3658", "groupCode": "MarketGroup1322", "amountTitle": "已减195", "groupId": 1, "mergeId": 0}, "priceSize": 14, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}], "dailyPrice": 42.0, "hasResult": true}, {"groupCode": "2", "groupName": "经济轿车", "sortNum": 0, "dailyPrice": 42.0, "hasResult": true}, {"groupCode": "newenergy", "groupName": "新能源", "sortNum": 2, "dailyPrice": 52.0, "hasResult": true}, {"groupCode": "3", "groupName": "舒适轿车", "sortNum": 3, "dailyPrice": 126.0, "hasResult": true}, {"groupCode": "6", "groupName": "SUV", "sortNum": 4, "dailyPrice": 71.0, "hasResult": true}, {"groupCode": "4", "groupName": "商务车", "sortNum": 5, "dailyPrice": 87.0, "hasResult": true}, {"groupCode": "5", "groupName": "豪华轿车", "sortNum": 6, "dailyPrice": 177.0, "hasResult": true}, {"groupCode": "9", "groupName": "跑车", "sortNum": 7, "dailyPrice": 231.0, "hasResult": true}, {"groupCode": "11", "groupName": "房车", "sortNum": 8, "dailyPrice": 640.0, "hasResult": true}, {"groupCode": "7", "groupName": "小巴士", "sortNum": 9, "dailyPrice": 440.0, "hasResult": true}, {"groupCode": "10", "groupName": "皮卡", "sortNum": 10, "dailyPrice": 143.0, "hasResult": true}], "productGroupsHashCode": "225u2F81157114U3g3bw", "storeList": [{"storeCode": "116463", "pickUpLevel": 59039, "pickOffLevel": 59039}, {"storeCode": "49033", "pickUpLevel": 17, "pickOffLevel": 17}, {"storeCode": "78", "pickUpLevel": 2761, "pickOffLevel": 2761}, {"storeCode": "116361", "pickUpLevel": 58362, "pickOffLevel": 58362}, {"storeCode": "115265", "pickUpLevel": 49817, "pickOffLevel": 49817}, {"storeCode": "116092", "pickUpLevel": 56249, "pickOffLevel": 56249}, {"storeCode": "107360", "pickUpLevel": 25629, "pickOffLevel": 25629}, {"storeCode": "115211", "pickUpLevel": 49274, "pickOffLevel": 49274}, {"storeCode": "107196", "pickUpLevel": 23111, "pickOffLevel": 23111}, {"storeCode": "117490", "pickUpLevel": -1, "pickOffLevel": -1}, {"storeCode": "117181", "pickUpLevel": 62041, "pickOffLevel": 62041}, {"storeCode": "106218", "pickUpLevel": 17890, "pickOffLevel": 17890}, {"storeCode": "114768", "pickUpLevel": 45746, "pickOffLevel": 45746}], "commNotices": [], "rentCenter": {"id": 2, "name": "三亚凤凰机场店", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.308537", "lng": "109.413536", "filterCode": "Vendor_0", "fromTime": "00:00", "toTime": "23:59"}, "rRentCenter": {"id": 2, "name": "三亚凤凰机场店", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.308537", "lng": "109.413536", "filterCode": "Vendor_0", "fromTime": "00:00", "toTime": "23:59"}, "promptInfos": [{"contents": [{"contentStyle": "0", "stringObjs": [{"content": "您租车可享受“酒店用户最高立减100元”专属优惠~", "style": "0"}]}], "icon": "https://pages.c-ctrip.com/commerce/promote/car/app/images/1112.png"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "优质车况", "type": 1, "description": "3年内车龄", "sortNum": 3, "subTitle": "", "showLayer": 0}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满", "sortNum": 10, "subTitle": "", "showLayer": 0}, {"title": "车辆守护升级", "type": 0, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 99, "subTitle": "*覆盖损失范围以预订页面内披露为准"}]}, "basicData": {"sortItems": [{"title": "推荐排序", "type": 1, "code": "1", "sortNum": 1}, {"title": "总价 低→高", "type": 2, "code": "2", "sortNum": 2}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "labelCodes": ["3563", "3510", "3696", "3653", "3697", "3698", "3731", "3679", "3757", "3779", "3658", "3650", "3495", "3494", "3548", "3705", "3504", "3827", "3547", "3503", "3502", "3501", "3709", "3641", "3642", "3509", "3788", "3789", "3746", "3769"], "isAll": false, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isLastPage": false, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "sTitle": "9大无忧特权", "hint": "优选门店无忧租超值价"}, {"id": 2, "title": "无忧租 安心首选", "sTitle": "9大无忧特权", "hint": "无忧租超值价"}, {"id": 3, "title": "全国连锁 服务放心", "sTitle": "", "hint": "一嗨租车超值价"}, {"id": 4, "title": "上门送取车 取还超便捷", "sTitle": "", "hint": "送车上门超值价"}, {"id": 5, "title": "信用租 押金双免", "sTitle": "", "hint": "押金双免超值价"}, {"id": 6, "title": "新车保障 车况佳", "sTitle": "", "hint": "新车超值价"}, {"id": 7, "title": "超值特价 高性价比", "sTitle": "", "hint": "超值特价"}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "hasResultWithoutFilter": true, "isFromSearch": true, "productGroupCodeUesd": "all", "frontTraceInfo": {"vehicleList": ["1577", "4722", "916", "4980", "4982", "4740", "1", "2437", "5", "1464", "2794", "4972", "4852", "4732", "4977", "4856", "4735", "4736", "17389", "4992", "4750", "4872", "3783", "4630", "1121", "2451", "17380", "3417", "4628", "10634", "1474", "4741", "4742", "4743", "4866", "4868", "4626", "4506", "819", "17394", "4883", "17390", "17391", "2461", "2460", "1129", "2459", "1488", "4994", "4752", "5600", "4511", "5601", "4513", "4635", "3788", "4515", "4758", "4516", "4517", "17486", "4892", "4771", "1140", "1138", "2224", "1497", "711", "1133", "4764", "4644", "4769", "4649", "17378", "4781", "4660", "17373", "4662", "4421", "4663", "2484", "3450", "1151", "1149", "2479", "1147", "5500", "4774", "5502", "5623", "4897", "4413", "4899", "5504", "10622", "4659", "608", "5506", "10621", "17344", "4790", "17346", "5520", "3460", "2247", "852", "2004", "1399", "1156", "5632", "4302", "4544", "858", "5514", "859", "4427", "4306", "5517", "5410", "1176", "2384", "3469", "5402", "4676", "5524", "5525", "4799", "506", "5526", "5407", "4319", "4571", "4330", "2155", "871", "873", "1058", "874", "1178", "2387", "635", "5413", "4325", "5415", "5658", "4449", "5419", "4462", "4101", "5311", "4344", "5433", "4102", "2165", "1195", "3494", "1069", "5423", "5303", "4577", "648", "5305", "5427", "5306", "5428", "4590", "4470", "4110", "5562", "4112", "5564", "5444", "5686", "4476", "3262", "1083", "3261", "2050", "1082", "1081", "410", "652", "2049", "656", "4103", "5555", "5676", "658", "4588", "659", "4107", "4228", "5439", "4109", "1090", "5570", "4482", "5571", "5572", "4483", "5694", "5331", "5574", "4364", "5453", "4243", "5211", "5332", "5575", "5576", "2066", "2064", "300", "301", "666", "2057", "4598", "4478", "5325", "548", "4358", "4117", "5569", "4238", "5329", "5462", "5341", "4010", "4495", "5584", "4132", "5342", "5222", "4496", "5101", "5465", "5223", "4498", "5466", "2078", "3164", "672", "311", "3159", "315", "5214", "4488", "679", "5698", "4004", "4367", "4489", "4369", "4127", "5591", "4261", "4020", "5351", "5594", "5353", "5474", "5595", "5596", "4023", "5355", "6323", "4025", "3176", "5590", "682", "3169", "326", "5104", "5225", "5467", "6314", "5226", "4139", "5349", "6316", "5360", "5361", "5482", "3180", "4151", "5241", "4031", "6333", "5364", "4276", "5486", "5245", "5125", "4036", "3184", "5480", "452", "696", "333", "456", "214", "3059", "3058", "216", "4268", "5599", "5479", "4269", "4149", "5371", "5010", "5131", "4042", "5253", "4043", "5374", "5254", "6343", "4287", "4288", "5257", "5499", "340", "220", "468", "5368", "5007", "5128", "5141", "4052", "4053", "5144", "5023", "5145", "5025", "4058", "591", "471", "474", "476", "5258", "4048", "5017", "17606", "5393", "5151", "5272", "5274", "4067", "5278", "5158", "3095", "5271", "120", "5150", "17619", "247", "248", "17616", "5283", "5163", "5044", "5045", "26", "5046", "5167", "5289", "6258", "28", "4191", "4071", "5281", "5282", "4193", "135", "257", "138", "5294", "5052", "33", "5173", "5295", "4086", "5175", "5176", "5297", "5177", "4088", "37", "5179", "5170", "5291", "6261", "5050", "265", "267", "5063", "6276", "5066", "5187", "5068", "49", "5189", "4091", "5180", "5060", "6272", "6271", "1811", "2900", "2905", "2901", "5074", "5198", "5077", "280", "5191", "5071", "5193", "286", "166", "287", "1820", "289", "1826", "65", "6297", "5089", "172", "6294", "175", "296", "2801", "177", "178", "1951", "75", "89", "17440", "17561", "17441", "2706", "2704", "97", "98", "2711", "1500", "17421", "2721", "4907", "2720", "1991", "17428", "17308", "2729", "17425", "17427", "17554", "1404", "4916", "2732", "1400", "2730", "1407", "1406", "3945", "1405", "17641", "4925", "4805", "4928", "4808", "1531", "1651", "2860", "4920", "3952", "4921", "17411", "4936", "17414", "4935", "17416", "1560", "1437", "2889", "4826", "4827", "2888", "4940", "17628", "4943", "4823", "3617", "4946", "4960", "1450", "4958", "4837", "4716", "4959", "1445", "3502", "1444", "1442", "1441", "2893", "4950", "4832", "4834", "4835", "1449"], "vehicleGroupMap": {"11": 3, "2": 92, "newenergy": 114, "3": 50, "4": 67, "5": 52, "6": 179, "7": 3, "9": 63, "10": 3}, "priceCount": 2562, "normalCount": 2562, "easyLifeCount": 0, "zhimaCount": 2448, "vendorNames": ["丰田海南出行", "小飞侠租车", "哈尔滨奥朗租车", "龙之祥租车", "天驹租车", "小米租车", "普信租车", "泽恒租车", "时间旅行租车", "信华租车", "叁零租车", "泰信吉租车", "租租侠租车", "八骏马租车", "龙嘉租车", "海鸭鸭租车", "华鑫海租车", "瑞赢租车", "梵云租车", "立强租车", "环岛租车", "桐叶租车", "你我他租车", "枫叶租车", "鹏顺通租车", "海南麻豆智行租车", "么么达租车", "爱尚出行租车", "启捷租车", "潆莹租车", "金晟租车", "小红帽租车", "五行租车", "三亚蔚蓝租车", "商旅出行", "格莱拓租车", "慧霏租车", "细杰租车", "优享旅途租车", "三亚易云租车", "青草兔租车", "榴莲租车", "凹凸出行", "陵水铭途租车", "大权租车", "诚航租车", "海越租车", "四季嘉行租车", "金森租车", "三亚启航租车", "途新租车", "一嗨租车", "豪享荟租车", "海南辽诚租车", "车速递租车", "海南途达租车", "宏广东盈租车", "车旺达租车", "京海租车", "吉驰租车", "三亚皖太租车", "海心租车", "潮人商旅租车", "常晟租车", "海南椰林情租车", "骑仕租车", "钰鑫租车", "加加租车", "海南点赞租车", "荣树租车", "三亚宝驰租车", "一路平安租车", "凯信租车", "昌泰租车", "懒人行租车", "全季租车", "龙悦三亚租车", "乐达通租车", "文东租车", "虫子邦租车", "车游天下", "腾越租车", "京海亚租车", "宏驰智行租车", "欣岳美行租车", "港梦超跑俱乐部租车", "宁洋租车", "明昊租车", "车租婆租车", "祥驰租车", "海南锦程租车", "三亚世纪联合租车", "凤翔天涯租车", "新易达租车", "多浦达租车", "准典出行", "启瑞盛租车", "和平租车", "翊霏租车", "彩车坊租车", "禧瑞达租车", "车之美租车", "易达通租车", "山水云途租车", "麒麟火租车"]}, "uniqSign": "120011757102883844428897sI6t74G7P508u38v", "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "pHub": 1, "rHub": 1, "promotMap": {"105634100": "NTy8EoHDVWsT7bJGDvlCUQ=="}, "extras": {"isNewLicensePlate": "0", "serverRequestId": "V8ccJMW72L3o837777Sv", "abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B"}, "isRecommend": false, "isKlbData": true}