{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "c602c0a4-a7c3-46b6-8652-a49d34c0ef35", "extMap": {"runAsyncCost_2": "0.0", "checkRentCenter_2": "1.0", "mergeGroup_9": "1.0", "secretBoxRequest": "1", "restOriginalCost": "1595.0", "mergeGroupSize_10": "4", "mergeGroupSize_11": "4", "pageName": "List", "initBaseData_1": "20.0", "allCost": "1595.0", "secret_box_vehicle_from_shopping": "0", "usePage": "1", "dropoffCityId": "43", "IncludeFeesCost": "0.0", "mergeGroupSize_2": "55", "mergeGroupSize_newenergy": "141", "mergeGroupSize_5": "50", "mergeGroupSize_6": "224", "mergeGroupSize_3": "22", "mergeGroupSize_4": "73", "mergeGroup_7": "0.0", "productGroupCost_6": "11.0", "mergeGroup_6": "1.0", "originalCode": "200", "mergeGroup_5": "1.0", "mergeGroup_4": "1.0", "mergeGroup_3": "0.0", "mergeGroup_2": "1.0", "apiCost": "1180.0", "contextBuilderCost_3": "21.0", "ubtProcessCost_8": "0.0", "calculatePreAuth_3": "0.0", "mergeGroupSize_9": "71", "uid": "_WeChat3030853149", "mergeGroupSize_7": "1", "allVendorPriceCount": "3278", "mergeGroup_newenergy": "6.0", "end": "2023-10-13 11:16:43", "totalCostTime": "1682", "start": "2023-10-13 11:16:41", "shoppingCost_1": "1181.0", "gsCost": "0.0", "buildInfoCost_2": "307.0", "buildInfoCost_3": "1.0", "buildInfoCost_4": "308.0", "mergeGroup_11": "0.0", "setProductGroupsHashCodeCostAffect": "0.0", "buildInfoCost_1": "0.0", "lastInfoCost_7": "74.0", "mergeGroup_10": "0.0", "build_secret_box_cost": "0.0", "pickupCityId": "43"}, "extraIndexTags": {"rCityId": "43", "pcName": "中国", "rcId": "1", "pcId": "1", "pCityId": "43", "pCityName": "三亚", "rcName": "中国", "rCityName": "三亚"}, "apiResCodes": [], "hasResult": true, "errorCode": "0", "message": ""}, "ResponseStatus": {"Timestamp": "/Date(1697167003678+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "8094043082743317082"}, {"Id": "RootMessageId", "Value": "921822-0a38184f-471435-348298"}]}, "requestInfo": {"pickupDate": "/Date(1697180400000+0800)/", "pickupLocationName": "凤凰国际机场T1航站楼", "returnDate": "/Date(1697353200000+0800)/", "returnLocationName": "凤凰国际机场T1航站楼", "sourceCountryId": 1, "age": 30, "pLatitude": 18.30747, "rLatitude": 18.30747, "rLongitude": 109.41201, "pLongitude": 109.41201, "pDate": "20231013150000", "rDate": "20231015150000", "pCityId": 43, "rCityId": 43}, "allVehicleCount": 504, "allVendorPriceCount": 3278, "filterMenuItems": [{"name": "快速选车", "code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"name": "车龄", "sortNum": 1, "groupCode": "CarAge", "bitwiseType": 2, "filterItems": [{"itemCode": "CarAge_3509", "name": "半年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "8"}, {"itemCode": "CarAge_3510", "name": "一年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "8"}, {"itemCode": "CarAge_3547", "name": "两年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 3, "positionCode": "8"}], "shortName": "车龄"}, {"name": "座位数", "sortNum": 2, "groupCode": "SeatGroup", "bitwiseType": 2, "filterItems": [{"itemCode": "SeatGroup_1", "name": "2座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "SeatGroup_2", "name": "4座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "SeatGroup_3", "name": "5座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "SeatGroup_4", "name": "6座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}, {"itemCode": "SeatGroup_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 5, "isQuickItem": true, "quickSortNum": 1, "positionCode": "13"}, {"itemCode": "SeatGroup_6", "name": "8座及以上", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 6, "isQuickItem": false}], "shortName": "座位", "isSupportMulti": true}, {"name": "车辆排挡", "sortNum": 3, "groupCode": "Transmission", "bitwiseType": 2, "filterItems": [{"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "10"}, {"itemCode": "Transmission_2", "name": "手动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}], "shortName": "排挡"}, {"name": "能源类型", "sortNum": 4, "groupCode": "NewEnergy", "bitwiseType": 2, "filterItems": [{"itemCode": "NewEnergy_elect", "name": "纯电动", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "NewEnergy_mix", "name": "新能源混动", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "NewEnergy_gas", "name": "汽油", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}], "shortName": "能源类型", "isSupportMulti": true}, {"name": "车辆配置", "sortNum": 5, "groupCode": "VehicleAccessory", "bitwiseType": 1, "filterItems": [{"itemCode": "VehicleAccessory_ReversingImage", "name": "倒车影像", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 2, "isQuickItem": true, "quickSortNum": 6, "positionCode": "9"}, {"itemCode": "VehicleAccessory_radar", "name": "倒车雷达", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 32, "sortNum": 3, "isQuickItem": true, "quickSortNum": 7, "positionCode": "9"}, {"itemCode": "VehicleAccessory_tachograph", "name": "行车记录仪", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 64, "sortNum": 4, "isQuickItem": true, "quickSortNum": 8, "positionCode": "9"}, {"itemCode": "VehicleAccessory_MobileHolder", "name": "手机支架", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 128, "sortNum": 5, "isQuickItem": false}, {"itemCode": "VehicleAccessory_LeatherSeat", "name": "真皮座椅", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 256, "sortNum": 6, "isQuickItem": false}, {"itemCode": "VehicleAccessory_Refrigerator", "name": "车载冰箱", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 512, "sortNum": 7, "isQuickItem": false}], "shortName": "车辆配置", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 6, "groupCode": "HotBrand", "bitwiseType": 2, "filterItems": [{"itemCode": "HotBrand_奥迪", "name": "奥迪", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"itemCode": "HotBrand_别克", "name": "别克", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "HotBrand_奔驰", "name": "奔驰", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "HotBrand_宝马", "name": "宝马", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "HotB<PERSON>_本田", "name": "本田", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"itemCode": "HotBrand_大众", "name": "大众", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"itemCode": "HotBrand_丰田", "name": "丰田", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "HotBrand_Jeep", "name": "Jeep", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 10, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"itemCode": "HotBrand_路虎", "name": "路虎", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 12, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"itemCode": "HotBrand_日产", "name": "日产", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 18, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "shortName": "热门品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_a0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_a0_AC <PERSON>itzer", "name": "AC Schnitzer", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/acschnitzer.png"}, {"itemCode": "BrandGroup_a0_AITO", "name": "AITO", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_a0_埃安", "name": "埃安", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R44f120009gwvd13842B.png"}, {"itemCode": "BrandGroup_a0_奥迪", "name": "奥迪", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"itemCode": "BrandGroup_a0_爱驰", "name": "爱驰", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aichi.png"}, {"itemCode": "BrandGroup_a0_阿斯顿·马丁", "name": "阿斯顿·马丁", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R419120009gwx9cqBA77.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_b0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_b0_保时捷", "name": "保时捷", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"itemCode": "BrandGroup_b0_别克", "name": "别克", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "BrandGroup_b0_北京", "name": "北京", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/beijin.png"}, {"itemCode": "BrandGroup_b0_奔腾", "name": "奔腾", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benteng.png"}, {"itemCode": "BrandGroup_b0_奔驰", "name": "奔驰", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "BrandGroup_b0_宝沃", "name": "宝沃", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baowo.png"}, {"itemCode": "BrandGroup_b0_宝马", "name": "宝马", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "BrandGroup_b0_宝骏", "name": "宝骏", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baojun.png"}, {"itemCode": "BrandGroup_b0_宾利", "name": "宾利", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/binli.png"}, {"itemCode": "BrandGroup_b0_本田", "name": "本田", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"itemCode": "BrandGroup_b0_标致", "name": "标致", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/biaozhi.png"}, {"itemCode": "BrandGroup_b0_比亚迪", "name": "比亚迪", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_c0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_c0_长城", "name": "长城", "groupCode": "BrandGroup_c0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/changcheng.png"}, {"itemCode": "BrandGroup_c0_长安", "name": "长安", "groupCode": "BrandGroup_c0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/changan.png"}, {"itemCode": "BrandGroup_c0_长安欧尚", "name": "长安欧尚", "groupCode": "BrandGroup_c0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/changanoushang.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_d0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_d0_东风风光", "name": "东风风光", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengguang.png"}, {"itemCode": "BrandGroup_d0_东风风神", "name": "东风风神", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengshen.png"}, {"itemCode": "BrandGroup_d0_东风风行", "name": "东风风行", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengxin.png"}, {"itemCode": "BrandGroup_d0_大众", "name": "大众", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"itemCode": "BrandGroup_d0_大运", "name": "大运", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R46b120009gwviht186F.png"}, {"itemCode": "BrandGroup_d0_德宝", "name": "德宝", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_d0_道奇", "name": "道奇", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/daoqi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_f0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_f0_丰田", "name": "丰田", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "BrandGroup_f0_法拉利", "name": "法拉利", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"itemCode": "BrandGroup_f0_福特", "name": "福特", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}, {"itemCode": "BrandGroup_f0_飞凡汽车", "name": "飞凡汽车", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_g0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_g0_光冈", "name": "光冈", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/guagngang.png"}, {"itemCode": "BrandGroup_g0_广汽传祺", "name": "广汽传祺", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}, {"itemCode": "BrandGroup_g0_高合汽车", "name": "高合汽车", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0yc6a12000aom31vy37E4.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_h0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_h0_华晨新日", "name": "华晨新日", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R472120009gwv9rxB7E1.png"}, {"itemCode": "BrandGroup_h0_哈弗", "name": "哈弗", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}, {"itemCode": "BrandGroup_h0_海马", "name": "海马", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}, {"itemCode": "BrandGroup_h0_红旗", "name": "红旗", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}, {"itemCode": "BrandGroup_h0_黄海", "name": "黄海", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/huanghai.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_j0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_j0_Jeep", "name": "Jeep", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"itemCode": "BrandGroup_j0_几何汽车", "name": "几何汽车", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R43t120009gwv73p44FD.png"}, {"itemCode": "BrandGroup_j0_吉利汽车", "name": "吉利汽车", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"itemCode": "BrandGroup_j0_捷豹", "name": "捷豹", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiebao.png"}, {"itemCode": "BrandGroup_j0_捷达", "name": "捷达", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}, {"itemCode": "BrandGroup_j0_江铃集团新能源", "name": "江铃集团新能源", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jianglingxin.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_k0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_k0_凯迪拉克", "name": "凯迪拉克", "groupCode": "BrandGroup_k0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_l0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_l0_<PERSON><PERSON>r", "name": "<PERSON><PERSON><PERSON>", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lorinser.png"}, {"itemCode": "BrandGroup_l0_兰博基尼", "name": "兰博基尼", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"itemCode": "BrandGroup_l0_劳斯莱斯", "name": "劳斯莱斯", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"itemCode": "BrandGroup_l0_岚图汽车", "name": "岚图汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R45a120009gwvmb34A33.png"}, {"itemCode": "BrandGroup_l0_林肯", "name": "林肯", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/linken.png"}, {"itemCode": "BrandGroup_l0_理想汽车", "name": "理想汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc6i1200000ieok0AE70.png"}, {"itemCode": "BrandGroup_l0_聊工", "name": "聊工", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_l0_路虎", "name": "路虎", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"itemCode": "BrandGroup_l0_零跑汽车", "name": "零跑汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingpaoqiche.png"}, {"itemCode": "BrandGroup_l0_雷克萨斯", "name": "雷克萨斯", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}, {"itemCode": "BrandGroup_l0_领克", "name": "领克", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingke.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_m0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_m0_MINI", "name": "MINI", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"itemCode": "BrandGroup_m0_玛莎拉蒂", "name": "玛莎拉蒂", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}, {"itemCode": "BrandGroup_m0_迈凯伦", "name": "迈凯伦", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/maikailun.png"}, {"itemCode": "BrandGroup_m0_马自达", "name": "马自达", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mazida.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_n0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_n0_哪吒汽车", "name": "哪吒汽车", "groupCode": "BrandGroup_n0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R46n120009gwvgrmC0A4.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_o0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_o0_欧拉", "name": "欧拉", "groupCode": "BrandGroup_o0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/oula.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_q0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_q0_前途", "name": "前途", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiantu.png"}, {"itemCode": "BrandGroup_q0_启辰", "name": "启辰", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qicheng.png"}, {"itemCode": "BrandGroup_q0_奇瑞", "name": "奇瑞", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qirui.png"}, {"itemCode": "BrandGroup_q0_起亚", "name": "起亚", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_r0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_r0_日产", "name": "日产", "groupCode": "BrandGroup_r0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"itemCode": "BrandGroup_r0_荣威", "name": "荣威", "groupCode": "BrandGroup_r0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_s0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_s0_smart", "name": "smart", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/smart.png"}, {"itemCode": "BrandGroup_s0_三菱", "name": "三菱", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sanling.png"}, {"itemCode": "BrandGroup_s0_上汽大通MAXUS", "name": "上汽大通MAXUS", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}, {"itemCode": "BrandGroup_s0_思铭", "name": "思铭", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/siming.png"}, {"itemCode": "BrandGroup_s0_斯柯达", "name": "斯柯达", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sikeda.png"}, {"itemCode": "BrandGroup_s0_赛麟", "name": "赛麟", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sailin.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_t0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_t0_坦克", "name": "坦克", "groupCode": "BrandGroup_t0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0yc3612000aoma5tvACF1.png"}, {"itemCode": "BrandGroup_t0_特斯拉", "name": "特斯拉", "groupCode": "BrandGroup_t0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}, {"itemCode": "BrandGroup_t0_腾势", "name": "腾势", "groupCode": "BrandGroup_t0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/shiteng.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_w0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_w0_WEY", "name": "WEY", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/wey.png"}, {"itemCode": "BrandGroup_w0_五菱汽车", "name": "五菱汽车", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/wulingqiche.png"}, {"itemCode": "BrandGroup_w0_威马汽车", "name": "威马汽车", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/weimaqiche.png"}, {"itemCode": "BrandGroup_w0_沃尔沃", "name": "沃尔沃", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/woerwo.png"}, {"itemCode": "BrandGroup_w0_蔚来", "name": "蔚来", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/weilai.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_x0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_x0_小鹏", "name": "小鹏", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiaopengqiche.png"}, {"itemCode": "BrandGroup_x0_现代", "name": "现代", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"itemCode": "BrandGroup_x0_雪佛兰", "name": "雪佛兰", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_y0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_y0_英菲尼迪", "name": "英菲尼迪", "groupCode": "BrandGroup_y0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/yingfeinidi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_z0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_z0_SONGSAN MOTORS", "name": "SONGSAN MOTORS", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_z0_合创", "name": "合创", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false}], "shortName": "全部品牌", "isSupportMulti": true}]}, {"name": "更多筛选", "code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"name": "价格", "sortNum": 1, "groupCode": "Price", "filterItems": [{"itemCode": "Price_0-50", "name": "¥50以下", "code": "0-50", "groupCode": "Price", "sortNum": 1}, {"itemCode": "Price_50-100", "name": "¥50-100", "code": "50-100", "groupCode": "Price", "sortNum": 2}, {"itemCode": "Price_100-200", "name": "¥100-200", "code": "100-200", "groupCode": "Price", "sortNum": 3}, {"itemCode": "Price_200-99999", "name": "¥200以上", "code": "200-99999", "groupCode": "Price", "sortNum": 4}], "shortName": "价格"}, {"name": "取车方式", "sortNum": 2, "groupCode": "PickReturn", "bitwiseType": 2, "filterItems": [{"itemCode": "PickReturn_StationPR", "name": "站内取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "mark": "15分钟内", "positionCode": "1"}, {"itemCode": "PickReturn_PickupOnDoor", "name": "送车上门", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "1"}, {"itemCode": "PickReturn_FreeShuttle", "name": "免费接至门店取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "PickReturn_PickupSelf", "name": "自行到店取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}], "shortName": "取还方式", "isSupportMulti": true}, {"name": "门店服务", "sortNum": 3, "groupCode": "StoreService", "bitwiseType": 1, "filterItems": [{"itemCode": "StoreService_easyLife", "name": "无忧租", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 2, "positionCode": "3"}, {"itemCode": "StoreService_FreeDepositAllCtrip", "name": "押金双免", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 8, "sortNum": 4, "isQuickItem": true, "quickSortNum": 3, "positionCode": "3"}, {"itemCode": "StoreService_Unlimit", "name": "不限里程", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 5, "isQuickItem": false}, {"itemCode": "StoreService_FreeCancel", "name": "免费取消", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 32, "sortNum": 6, "isQuickItem": false}], "shortName": "门店服务", "isSupportMulti": true}, {"name": "优惠活动", "sortNum": 4, "groupCode": "Promotion", "bitwiseType": 2, "filterItems": [{"itemCode": "Promotion_3641", "name": "铂金贵宾", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 14, "positionCode": "2"}, {"itemCode": "Promotion_3765", "name": "新能源特惠", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}], "shortName": "优惠活动", "isSupportMulti": true}, {"name": "取车证件", "sortNum": 5, "groupCode": "Ceritificate", "bitwiseType": 2, "filterItems": [{"itemCode": "Ceritificate_1", "name": "身份证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_2", "name": "护照", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_7", "name": "回乡证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_8", "name": "台胞证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 3, "isQuickItem": false}], "shortName": "取车证件", "isSupportMulti": true}, {"name": "驾驶员驾龄", "sortNum": 6, "groupCode": "DriveAge", "bitwiseType": 2, "filterItems": [{"itemCode": "DriveAge_1", "name": "不满6个月", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "DriveAge_2", "name": "满6个月", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "DriveAge_3", "name": "满一年", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}], "shortName": "驾驶员驾龄"}, {"name": "门店评分", "sortNum": 7, "groupCode": "Comment", "bitwiseType": 2, "filterItems": [{"itemCode": "Comment_4.8", "name": "4.8分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "12"}, {"itemCode": "Comment_4.5", "name": "4.5分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Comment_4.0", "name": "4.0分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}], "shortName": "门店评分"}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_0", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_0", "name": "携程租车中心", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_-1", "name": "携程优选", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": true, "quickSortNum": 1, "icon": "", "positionCode": "5"}, {"itemCode": "Vendor_13088", "name": "一嗨租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false, "mark": "hot"}, {"itemCode": "Vendor_13031", "name": "桐叶租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13032", "name": "明昊租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13033", "name": "枫叶租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13036", "name": "大方租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13037", "name": "车速递租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13067", "name": "车游天下租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13071", "name": "EVCARD租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13082", "name": "凯美租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13092", "name": "三亚易安达租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13094", "name": "骑仕租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13115", "name": "港梦超跑俱乐部租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30027", "name": "玛雅租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30055", "name": "准典出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30150", "name": "小木鱼租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30164", "name": "懒人行租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30169", "name": "峰硕租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30182", "name": "加加租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30196", "name": "太平洋租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30234", "name": "金晟租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30777", "name": "海南椰林情租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30912", "name": "卢米租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_31218", "name": "大权租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_31279", "name": "黑桃壹租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32231", "name": "泰信吉租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32498", "name": "么么达租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32538", "name": "海越租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32687", "name": "行者天下租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_1", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_33419", "name": "欣岳美行租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_47522", "name": "祥驰租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_53893", "name": "三亚世纪联合租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_55288", "name": "陶陶租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_57671", "name": "捷安利达租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_58487", "name": "丰田海南出行", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61365", "name": "普信租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61372", "name": "程硕租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61659", "name": "旺亚租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61671", "name": "凤翔天涯租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61816", "name": "永卓租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61831", "name": "金达莱租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61836", "name": "三亚达乐租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61937", "name": "锋达租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61953", "name": "三亚易云租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61966", "name": "三亚宝驰租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62072", "name": "一路平安租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62099", "name": "宏驰智行租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62104", "name": "金森租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62107", "name": "启捷租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62115", "name": "三亚启航租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62119", "name": "三鹤租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62166", "name": "小红帽租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62167", "name": "玖捌陆租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62305", "name": "三亚皖太租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62408", "name": "宏广东盈租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62863", "name": "文东租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_63460", "name": "小米租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_63836", "name": "租租侠租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_63857", "name": "信华租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_64662", "name": "潆莹租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_2", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_65413", "name": "哈尔滨奥朗租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_65452", "name": "新易达租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_66324", "name": "环岛租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_66614", "name": "你我他租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_66708", "name": "旭辰租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_67709", "name": "三亚神风租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_68688", "name": "青草兔租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_68692", "name": "利资租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69279", "name": "安米租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69280", "name": "百募租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69282", "name": "榴莲租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69284", "name": "可爱屋租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69287", "name": "华鑫海租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70136", "name": "远恒租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70400", "name": "禧瑞达租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70695", "name": "龙之祥租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70698", "name": "陵水铭途租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_71599", "name": "日之星丰田租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_72621", "name": "细杰租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_72983", "name": "吉驰租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_73265", "name": "车之美租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_73619", "name": "海南锦程租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_74365", "name": "爱尚出行租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_74373", "name": "海南中进租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_74569", "name": "河北唐亚租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_74573", "name": "器车出行", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_74629", "name": "通源租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_76105", "name": "全季租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_76661", "name": "振亚租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_76665", "name": "彩车坊租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_76903", "name": "如亚租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_3", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_77081", "name": "常晟租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_77287", "name": "恒驰租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_77647", "name": "鼎峰租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_78571", "name": "汪澜租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_78577", "name": "夏末微凉租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_78579", "name": "优享旅途租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_78963", "name": "津池租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79293", "name": "泊隽租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79485", "name": "立强租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79695", "name": "炜晨租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79747", "name": "鹏顺通租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79797", "name": "麒麟火租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79799", "name": "华瑞租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80127", "name": "三亚新概念租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80147", "name": "三亚浩宇租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80311", "name": "三亚蚂蚁租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80427", "name": "名都租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80431", "name": "小飞侠租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80535", "name": "汇驰租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80545", "name": "盛兴隆租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80551", "name": "瑞赢租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80557", "name": "海南鹏程租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80559", "name": "星锐租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80771", "name": "豫海租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80775", "name": "车先生", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80777", "name": "照成租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80781", "name": "车主角租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81003", "name": "琼州租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81479", "name": "商旅出行", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81525", "name": "钰鑫租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81529", "name": "果岭出行", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_4", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_81687", "name": "易达通租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81827", "name": "京海租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81829", "name": "诚航租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81831", "name": "潮人商旅租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81889", "name": "八骏马租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81919", "name": "福斯特租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81931", "name": "博利租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82019", "name": "HZD租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82105", "name": "三亚旅途中租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82153", "name": "四季嘉行租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82163", "name": "启瑞盛租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82231", "name": "云超租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82247", "name": "车租婆租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82263", "name": "聚通达租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82305", "name": "畅行无忧租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82393", "name": "海鸭鸭租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82399", "name": "基福租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82571", "name": "博之纳租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82627", "name": "宁洋租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82671", "name": "山水云途租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82731", "name": "海南途达租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82755", "name": "金爵万象租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82819", "name": "助旅租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82843", "name": "名仕租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82909", "name": "五行租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82987", "name": "海程租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82991", "name": "海心租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83115", "name": "慧霏租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83141", "name": "京海亚租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83173", "name": "军盛租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83257", "name": "腾越租车", "groupCode": "Vendor_4", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_5", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_15000088", "name": "盛泽租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000258", "name": "三亚五二零租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000269", "name": "海立达租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000271", "name": "鲸鹏出行", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000295", "name": "途新租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000316", "name": "海语租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000361", "name": "海南点赞租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000436", "name": "澳泰租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000585", "name": "蔚小理租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000981", "name": "轩宇租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001022", "name": "世通出行", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001073", "name": "海途租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001162", "name": "高福租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001163", "name": "欣博祥租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001169", "name": "博雅租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001186", "name": "北蒙租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001194", "name": "壹优租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001199", "name": "逍遥租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001202", "name": "海南龙驰租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001206", "name": "晨炫租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001211", "name": "恒想租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83291", "name": "豪享荟租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83315", "name": "顺利出行", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83378", "name": "海南悟空汽车租赁", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83386", "name": "虫子邦租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83528", "name": "钧通租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83574", "name": "飞扬租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83674", "name": "乐达通租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84185", "name": "龙运三亚租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84255", "name": "格莱拓租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85129", "name": "时间旅行租车", "groupCode": "Vendor_5", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_6", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_15001218", "name": "超联汇租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001229", "name": "海盗租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001241", "name": "乐意出行", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001249", "name": "呈阜租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001252", "name": "亿鑫租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001296", "name": "钱雨租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001307", "name": "艾思租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001308", "name": "纳贝拉租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001339", "name": "广隆商旅租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001341", "name": "博豪租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001346", "name": "溶阔租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001351", "name": "海南松舍租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001355", "name": "盛京海纳租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001364", "name": "斯方达租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001367", "name": "伟振租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001376", "name": "北新租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001377", "name": "世海租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001381", "name": "海南友途租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001384", "name": "安欣租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001387", "name": "有友租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001391", "name": "金亚租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001399", "name": "豪通租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001418", "name": "泓达租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001440", "name": "皓轩跑车俱乐部租车 ", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001444", "name": "中融顺达租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001447", "name": "摇个车租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001448", "name": "壹阳租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001454", "name": "邦尼租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001457", "name": "新奇租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001460", "name": "黑娃租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001493", "name": "海漫租车", "groupCode": "Vendor_6", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_7", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_15001504", "name": "流浪者租车 ", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001513", "name": "野涵租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001520", "name": "海南启航租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001538", "name": "三亚威途租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001541", "name": "钛万绅租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001547", "name": "盛鸿轩租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001584", "name": "瑞海租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001585", "name": "鑫晟唐租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001597", "name": "三亚洪顺租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001608", "name": "好鑫情租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001613", "name": "海南龙运租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001630", "name": "铭鸿租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001635", "name": "麒麟恒泰租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001639", "name": "森燚租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001646", "name": "拾柒出行", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001655", "name": "三亚恒泰租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001665", "name": "悠逸租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001712", "name": "睿睿租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001725", "name": "安鑫莱租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001732", "name": "铭驱租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001744", "name": "朗速出行", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001799", "name": "海宸租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001806", "name": "三亚百鸿租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001815", "name": "海岛行租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001817", "name": "鸿发租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001827", "name": "三亚永捷租车 ", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001863", "name": "琼城租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001913", "name": "木沐租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001917", "name": "美津租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15001919", "name": "安途生汽车租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15002038", "name": "星钺租车", "groupCode": "Vendor_7", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_8", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_15002143", "name": "安潞通租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15002319", "name": "东辉租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15002432", "name": "欣梦租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15002542", "name": "宜养出行", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15002712", "name": "蜜丝租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15002723", "name": "清晨租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15002755", "name": "悦萌动租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15002784", "name": "晟亚租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15002966", "name": "晨阳租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15002974", "name": "博格达租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15003135", "name": "塞伯坦租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15003234", "name": "三亚融泽租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15003362", "name": "领客出行", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15003454", "name": "芯瑜租车", "groupCode": "Vendor_8", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}]}], "quickFilter": [{"itemCode": "PickReturn_StationPR", "name": "站内取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "mark": "15分钟内", "positionCode": "1"}, {"itemCode": "Promotion_3641", "name": "铂金贵宾", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 14, "positionCode": "2"}, {"itemCode": "StoreService_easyLife", "name": "无忧租", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 2, "positionCode": "3"}, {"itemCode": "Vendor_-1", "name": "携程优选", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": true, "quickSortNum": 1, "icon": "", "positionCode": "5"}, {"itemCode": "CarAge_3509", "name": "半年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "8"}, {"itemCode": "VehicleAccessory_ReversingImage", "name": "倒车影像", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 2, "isQuickItem": true, "quickSortNum": 6, "positionCode": "9"}, {"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "10"}, {"itemCode": "Comment_4.8", "name": "4.8分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "12"}, {"itemCode": "SeatGroup_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 5, "isQuickItem": true, "quickSortNum": 1, "positionCode": "13"}], "vehicleList": [{"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "4139", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.0T-1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0scn143fai2ah4w9B69.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0414k120008at15l04B7B.jpg", "https://dimg04.c-ctrip.com//images/04139120008at3x402339.jpg", "https://dimg04.c-ctrip.com//images/0416n120008at1b3t7DE9.jpg", "https://dimg04.c-ctrip.com//images/04135120008at4it05F01.jpg", "https://dimg04.c-ctrip.com//images/04142120008at10cy3FB8.jpg", "https://dimg04.c-ctrip.com//images/0413g120008at239eCF3C.jpg", "https://dimg04.c-ctrip.com//images/04171120008at46pr9E1E.jpg", "https://dimg04.c-ctrip.com//images/0415b120008at1hgaA2A9.jpg", "https://dimg04.c-ctrip.com//images/0414k120008at2f9v4B12.jpg", "https://dimg04.c-ctrip.com//images/0414w120008at1b3z48A6.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "vehiclesSetId": "69"}, {"brandEName": "红旗", "brandName": "红旗", "name": "红旗E-QM5", "zhName": "红旗E-QM5", "vehicleCode": "5694", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 4, "doorNo": 4, "luggageNo": 5, "struct": "三厢车", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04141120009czlkee51B2.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1f12000axbd7fm43A8.jpg", "https://dimg04.c-ctrip.com/images/0RV1z12000axbd7u2AAFC.jpg", "https://dimg04.c-ctrip.com/images/0RV6a12000axbd6clD655.jpg", "https://dimg04.c-ctrip.com/images/0RV3e12000axbdf0h51F9.jpg", "https://dimg04.c-ctrip.com/images/0RV2x12000aru1a8wD748.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1i12000b60zfdq853A.jpg", "oilType": 5, "fuelType": "纯电动", "luggageNum": "可放5个24寸行李箱", "chargeInterface": "USB", "endurance": "工信部续航431km", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vr": "", "vehiclesSetId": "81"}, {"brandEName": "特斯拉", "brandName": "特斯拉", "name": "特斯拉Model 3", "zhName": "特斯拉Model 3", "vehicleCode": "5596", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "struct": "三厢车", "driveMode": "双电机四驱或后置后驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0413a120008n36k4711D4.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0512000b0au9vd604D.jpg", "https://dimg04.c-ctrip.com/images/0RV0r12000b0aujvo9602.jpg", "https://dimg04.c-ctrip.com/images/0RV2r12000b0auww3CA35.jpg", "https://dimg04.c-ctrip.com/images/0RV2m12000b0autot8100.jpg", "https://dimg04.c-ctrip.com/images/0RV6x12000b0atwx6CC13.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV5f12000b9qtnul9A10.jpg", "oilType": 5, "recommendLabels": [{"title": "起步快", "subTitle": "百公里加速最快3.3秒"}], "fuelType": "纯电动", "luggageNum": "可放5个24寸行李箱", "guidSys": "全速自适应巡航", "chargeInterface": "USB", "skylight": "分段式不可开启天窗", "endurance": "工信部续航445km-675km", "charge": "快充1小时,慢充10小时", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4851", "vehiclesSetId": "87"}, {"brandEName": "大众", "brandName": "大众", "name": "大众宝来", "zhName": "大众宝来", "vehicleCode": "4067", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0qcn143fagmgoxd04D4.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/04107120008at6v58BAE5.jpg", "https://dimg04.c-ctrip.com//images/0414y120008k97c20F174.jpg", "https://dimg04.c-ctrip.com/images/0RV6112000brgue0n4AE2.jpg", "https://dimg04.c-ctrip.com//images/0413r120008at5hb7D462.jpg", "https://dimg04.c-ctrip.com//images/0410r120008at5hwz665E.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com//images/04156120008at71ga4625.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "chargeInterface": "USB", "autoPark": false, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4860", "vehiclesSetId": "69"}, {"brandEName": "别克", "brandName": "别克", "name": "别克微蓝6", "zhName": "别克微蓝6", "vehicleCode": "5472", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "struct": "两厢车", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5e12000c7pivxf58BA.png"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0o12000c5smmhl410F.jpg", "https://dimg04.c-ctrip.com/images/0RV5y12000c5smn9587EF.jpg", "https://dimg04.c-ctrip.com/images/0RV4412000c5smizg7603.jpg", "https://dimg04.c-ctrip.com/images/0RV5812000c5smw2iC956.jpg", "https://dimg04.c-ctrip.com/images/0RV2f12000c5smjfx849F.jpg", "https://dimg04.c-ctrip.com/images/0RV3h12000c5smw2u7B5A.jpg", "https://dimg04.c-ctrip.com/images/0RV6v12000c5smn9nEE8B.jpg", "https://dimg04.c-ctrip.com/images/0RV1j12000c5smf7b779F.jpg", "https://dimg04.c-ctrip.com/images/0RV3012000c5smxvpE336.jpg", "https://dimg04.c-ctrip.com/images/0RV3o12000c5smlc26267.jpg", "https://dimg04.c-ctrip.com/images/0RV2h12000c5smhnj2316.jpg", "https://dimg04.c-ctrip.com/images/0RV2f12000c5smk6pEAAB.jpg", "https://dimg04.c-ctrip.com/images/0RV4712000c5smr680FD1.jpg", "https://dimg04.c-ctrip.com/images/0RV5312000c5smr6c828B.jpg", "https://dimg04.c-ctrip.com/images/0RV0q12000c5smmah091C.jpg", "https://dimg04.c-ctrip.com/images/0RV7212000c5smq0n40A9.jpg", "https://dimg04.c-ctrip.com/images/0RV6w12000c5smna316AB.jpg", "https://dimg04.c-ctrip.com/images/0RV2d12000c5smmnn4BA2.jpg", "https://dimg04.c-ctrip.com/images/0RV5412000c5smf7z86B5.jpg", "https://dimg04.c-ctrip.com/images/0RV2r12000c5smpcu8C70.jpg", "https://dimg04.c-ctrip.com/images/0RV2g12000c5smr6mDA9F.jpg", "https://dimg04.c-ctrip.com/images/0RV1l12000c5smk7jAF23.jpg", "https://dimg04.c-ctrip.com/images/0RV3z12000c5smqzt6F24.jpg", "https://dimg04.c-ctrip.com/images/0RV4e12000c5smwqnE523.jpg", "https://dimg04.c-ctrip.com/images/0RV0g12000c5smo4k894D.jpg", "https://dimg04.c-ctrip.com/images/0RV5612000c5smjhf4D37.jpg", "https://dimg04.c-ctrip.com/images/0RV2d12000c5sn195B8AC.jpg", "https://dimg04.c-ctrip.com/images/0RV1r12000c5smwqx604D.jpg", "https://dimg04.c-ctrip.com/images/0RV3d12000c5smf8d1FF2.jpg", "https://dimg04.c-ctrip.com/images/0RV1i12000c5smmo1A9FB.jpg", "https://dimg04.c-ctrip.com/images/0RV4012000c5smr0aB632.jpg", "https://dimg04.c-ctrip.com/images/0RV1n12000c5smjhnCB52.jpg", "https://dimg04.c-ctrip.com/images/0RV1m12000c5sn0uc838E.jpg", "https://dimg04.c-ctrip.com/images/0RV1x12000c5smqgi0C5C.jpg", "https://dimg04.c-ctrip.com/images/0RV4f12000c5smwr316B1.jpg", "https://dimg04.c-ctrip.com/images/0RV4112000c5smo58E804.jpg", "https://dimg04.c-ctrip.com/images/0RV6212000c5sn0un5D7E.jpg", "https://dimg04.c-ctrip.com/images/0RV2e12000c5smk1iF260.jpg", "https://dimg04.c-ctrip.com/images/0RV5k12000c5smle157AC.jpg", "https://dimg04.c-ctrip.com/images/0RV1n12000c5sn0uu6DAA.jpg", "https://dimg04.c-ctrip.com/images/0RV3612000c5smo5m32BE.jpg", "https://dimg04.c-ctrip.com/images/0RV1u12000c5smq26A7DB.jpg", "https://dimg04.c-ctrip.com/images/0RV2x12000c5sn3o7C74E.jpg", "https://dimg04.c-ctrip.com/images/0RV7112000c5smha85A30.jpg", "https://dimg04.c-ctrip.com/images/0RV1p12000c5smmby7CBF.jpg", "https://dimg04.c-ctrip.com/images/0RV6912000c5smw4vEDF0.jpg", "https://dimg04.c-ctrip.com/images/0RV0312000c5smq2j6F81.jpg", "https://dimg04.c-ctrip.com/images/0RV2k12000c5sn0vcE97A.jpg", "https://dimg04.c-ctrip.com/images/0RV3g12000c5sn0veB872.jpg", "https://dimg04.c-ctrip.com/images/0RV2b12000c5smo61B74F.jpg", "https://dimg04.c-ctrip.com/images/0RV2h12000c5smqu4C454.jpg", "https://dimg04.c-ctrip.com/images/0RV2j12000c5smr804FFA.jpg", "https://dimg04.c-ctrip.com/images/0RV1l12000c5smj1i6D16.jpg", "https://dimg04.c-ctrip.com/images/0RV3h12000c5sn56vDA42.jpg", "https://dimg04.c-ctrip.com/images/0RV3m12000c5smu6p475D.jpg", "https://dimg04.c-ctrip.com/images/0RV6y12000c5smnkg13EC.jpg", "https://dimg04.c-ctrip.com/images/0RV4812000c5sn42bA462.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1712000c5smo2cB28D.jpg", "oilType": 5, "fuelType": "纯电动", "luggageNum": "可放2个24寸行李箱", "guidSys": "定速巡航", "carPlay": "CarPlay", "chargeInterface": "USB", "skylight": "不可开启全景天窗", "endurance": "工信部续航301km-518km", "charge": "快充0.67小时,慢充9.5小时", "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vr": "", "vehiclesSetId": "72"}, {"brandEName": "大众", "brandName": "大众", "name": "大众迈腾", "zhName": "大众迈腾", "vehicleCode": "1149", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.4T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0bcn152vdwzklvl30BE.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0414y120008n8ad4rBC3F.jpg", "https://dimg04.c-ctrip.com//images/0410v120008n88bnw145F.jpg", "https://dimg04.c-ctrip.com//images/0410j120008n898t9FE5A.jpg", "https://dimg04.c-ctrip.com//images/0410v120008n8990u5CDF.jpg", "https://dimg04.c-ctrip.com//images/0415g120008n86kh80FC7.jpg", "https://dimg04.c-ctrip.com//images/0412b120008n8biwqF37C.jpg", "https://dimg04.c-ctrip.com//images/0415a120008n88gm67444.jpg", "https://dimg04.c-ctrip.com//images/0412k120008n8awc1A9DE.jpg", "https://dimg04.c-ctrip.com//images/0412w120008n87s8c00C5.jpg", "https://dimg04.c-ctrip.com//images/0415x120008n889xrA1CA.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0413l120009sl11z62983.png", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "autoPark": false, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=1404", "vehiclesSetId": "85"}], "productGroups": [{"groupCode": "all", "groupName": "全部车型", "sortNum": -4, "productList": [{"vehicleCode": "4139", "sortNum": 0, "lowestPrice": 35, "highestPrice": 35, "maximumRating": 5, "maximumCommentCount": 43448, "lowestDistance": 0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "13031", "vendorCode": "13031", "pStoreCode": "78", "rStoreCode": "78", "vehicleCode": "0", "packageId": "Secure", "packageType": 1, "comPriceCode": "****************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_NzhfNDEzOV8xXzM4LjBfNzYuMF8wLjBfMTU2LjBfMzUuMF8xNDkuMF8wXzBfMC4wXzAuMF82MC4wXzIwLjBfMF8wXzE1MjUzOQ==", "vendorVehicleCode": "18536", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 152539, "pLev": 129, "rLev": 129, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 69, "amountStr": "¥69", "subAmount": 35, "subAmountStr": "日均¥35", "originalDailyPrice": 38, "detail": [{"code": "1001", "name": "租车费", "amount": 76, "amountDesc": "¥76"}, {"code": "3641", "name": "铂金贵宾", "amount": 7, "amountDesc": "¥7"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 149, "amountStr": "¥149", "subAmount": 156, "subAmountStr": "¥156", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3047", "vendorCode": "13071", "pStoreCode": "101002", "vehicleCode": "0", "packageType": 1, "skuId": 1209257}}, {"reference": {"bizVendorCode": "SD3012", "vendorCode": "13032", "pStoreCode": "2961", "vehicleCode": "0", "packageType": 1, "skuId": 227977}}, {"reference": {"bizVendorCode": "SD4849", "vendorCode": "80431", "pStoreCode": "114886", "vehicleCode": "0", "packageType": 1, "skuId": 1913476}}, {"reference": {"bizVendorCode": "SD7763", "vendorCode": "13067", "pStoreCode": "67462", "vehicleCode": "0", "packageType": 1, "skuId": 787013}}, {"reference": {"bizVendorCode": "SD4701", "vendorCode": "74573", "pStoreCode": "114529", "vehicleCode": "0", "packageType": 1, "skuId": 1911622}}, {"reference": {"bizVendorCode": "SD4734", "vendorCode": "77081", "pStoreCode": "114575", "vehicleCode": "0", "packageType": 1, "skuId": 1916013}}, {"reference": {"bizVendorCode": "SD4201", "vendorCode": "30164", "pStoreCode": "114043", "vehicleCode": "0", "packageType": 1, "skuId": 1912422}}, {"reference": {"bizVendorCode": "SD3942", "vendorCode": "53893", "pStoreCode": "107119", "vehicleCode": "0", "packageType": 1, "skuId": 1860920}}, {"reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "vehicleCode": "0", "packageType": 1, "skuId": 5354009}}, {"reference": {"bizVendorCode": "SD5523", "vendorCode": "80559", "pStoreCode": "116966", "vehicleCode": "0", "packageType": 1, "skuId": 1959530}}, {"reference": {"bizVendorCode": "SD4445", "vendorCode": "62863", "pStoreCode": "114447", "vehicleCode": "0", "packageType": 1, "skuId": 1919961}}], "reactId": "1116435080", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["13031_0_78_78"]}, "minTPrice": 149, "minDPrice": 35, "modifySameVehicle": false, "minDOrinPrice": 38, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "铂金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3641", "groupCode": "MarketGroup1320", "amountTitle": "已减7", "groupId": 1, "mergeId": 0}, "priceSize": 12, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5694", "sortNum": 1, "lowestPrice": 48, "highestPrice": 132, "maximumRating": 5, "maximumCommentCount": 38086, "lowestDistance": 0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "13031", "vendorCode": "13031", "pStoreCode": "78", "rStoreCode": "78", "vehicleCode": "0", "packageId": "Secure", "packageType": 1, "comPriceCode": "****************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_NzhfNTY5NF8xXzY4LjBfMTM2LjBfMC4wXzIzNi4wXzQ4LjBfMTk1LjBfMF8wXzAuMF8wLjBfODAuMF8yMC4wXzBfMF8yMzQ2NzY5", "vendorVehicleCode": "18875", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 2346769, "pLev": 129, "rLev": 129, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 95, "amountStr": "¥95", "subAmount": 48, "subAmountStr": "日均¥48", "originalDailyPrice": 68, "detail": [{"code": "1001", "name": "租车费", "amount": 136, "amountDesc": "¥136"}, {"code": "3765", "name": "新能源特惠", "amount": 41, "amountDesc": "¥41"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 195, "amountStr": "¥195", "subAmount": 236, "subAmountStr": "¥236", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3012", "vendorCode": "13032", "pStoreCode": "2961", "vehicleCode": "0", "packageType": 1, "skuId": 2346797}}, {"reference": {"bizVendorCode": "SD4130", "vendorCode": "13115", "pStoreCode": "188937", "vehicleCode": "0", "packageType": 1, "skuId": 6413059}}], "reactId": "1116435090", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["13031_0_78_78"]}, "minTPrice": 195, "minDPrice": 48, "modifySameVehicle": false, "minDOrinPrice": 68, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "新能源特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3765", "groupCode": "MarketGroup1358", "amountTitle": "已减41", "groupId": 1, "mergeId": 0}, "priceSize": 3, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5596", "sortNum": 2, "lowestPrice": 167, "highestPrice": 330, "maximumRating": 5, "maximumCommentCount": 38168, "lowestDistance": 0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3963", "vendorCode": "61937", "pStoreCode": "107479", "rStoreCode": "107479", "vehicleCode": "0", "packageId": "sec", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3NDc5XzU1OTZfMV8yMzguMF80NzYuMF8wLjBfNTc2LjBfMTY3LjBfNDMzLjBfMF8wXzAuMF8wLjBfODAuMF8yMC4wXzBfMF82ODI4ODA1", "vendorVehicleCode": "2478_47859_pupai", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 6828805, "pLev": 82305, "rLev": 82305, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 333, "amountStr": "¥333", "subAmount": 167, "subAmountStr": "日均¥167", "originalDailyPrice": 238, "detail": [{"code": "1001", "name": "租车费", "amount": 476, "amountDesc": "¥476"}, {"code": "3765", "name": "新能源特惠", "amount": 143, "amountDesc": "¥143"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 433, "amountStr": "¥433", "subAmount": 576, "subAmountStr": "¥576", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD8208", "vendorCode": "15001249", "pStoreCode": "176280", "vehicleCode": "0", "packageType": 1, "skuId": 4683095}}, {"reference": {"bizVendorCode": "SD4224", "vendorCode": "30777", "pStoreCode": "114080", "vehicleCode": "0", "packageType": 1, "skuId": 1914619}}, {"reference": {"bizVendorCode": "SD5097", "vendorCode": "83291", "pStoreCode": "115347", "vehicleCode": "0", "packageType": 1, "skuId": 1912136}}, {"reference": {"bizVendorCode": "SD8411", "vendorCode": "15001447", "pStoreCode": "183036", "vehicleCode": "0", "packageType": 0, "skuId": 5058219}}, {"reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "vehicleCode": "0", "packageType": 1, "skuId": 5354050}}, {"reference": {"bizVendorCode": "SD6129", "vendorCode": "82105", "pStoreCode": "117341", "vehicleCode": "0", "packageType": 0, "skuId": 2878902}}, {"reference": {"bizVendorCode": "SD5066", "vendorCode": "82909", "pStoreCode": "115295", "vehicleCode": "0", "packageType": 1, "skuId": 1911412}}, {"reference": {"bizVendorCode": "SD8155", "vendorCode": "15001199", "pStoreCode": "175820", "vehicleCode": "0", "packageType": 1, "skuId": 4567669}}, {"reference": {"bizVendorCode": "SD8150", "vendorCode": "15001194", "pStoreCode": "175686", "vehicleCode": "0", "packageType": 1, "skuId": 4537373}}, {"reference": {"bizVendorCode": "SD8695", "vendorCode": "15001725", "pStoreCode": "183459", "vehicleCode": "0", "packageType": 1, "skuId": 5440994}}, {"reference": {"bizVendorCode": "SD4130", "vendorCode": "13115", "pStoreCode": "188937", "vehicleCode": "0", "packageType": 1, "skuId": 5407588}}, {"reference": {"bizVendorCode": "SD8611", "vendorCode": "15001639", "pStoreCode": "182996", "vehicleCode": "0", "packageType": 1, "skuId": 5643734}}, {"reference": {"bizVendorCode": "SD8584", "vendorCode": "15001613", "pStoreCode": "182916", "vehicleCode": "0", "packageType": 0, "skuId": 5407404}}, {"reference": {"bizVendorCode": "SD8774", "vendorCode": "13092", "pStoreCode": "181864", "vehicleCode": "0", "packageType": 1, "skuId": 4922905}}, {"reference": {"bizVendorCode": "SD6991", "vendorCode": "13088", "pStoreCode": "138344", "vehicleCode": "0", "packageType": 0, "skuId": 3191695}}, {"reference": {"bizVendorCode": "SD8300", "vendorCode": "15001339", "pStoreCode": "182870", "vehicleCode": "0", "packageType": 0, "skuId": 4992914}}], "reactId": "1116435091", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3963_0_107479_107479"]}, "minTPrice": 433, "minDPrice": 167, "modifySameVehicle": false, "minDOrinPrice": 238, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "新能源特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3765", "groupCode": "MarketGroup1358", "amountTitle": "已减143", "groupId": 1, "mergeId": 0}, "priceSize": 17, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4067", "sortNum": 3, "lowestPrice": 53, "highestPrice": 114, "maximumRating": 5, "maximumCommentCount": 38086, "lowestDistance": 0.2078, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "13031", "vendorCode": "13031", "pStoreCode": "78", "rStoreCode": "78", "vehicleCode": "0", "packageId": "Secure", "packageType": 1, "comPriceCode": "****************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_NzhfNDA2N18xXzU4LjBfMTE2LjBfMC4wXzE5Ni4wXzUzLjBfMTg2LjBfMF8wXzAuMF8wLjBfNjAuMF8yMC4wXzBfMF83NzI4Mg==", "vendorVehicleCode": "18667", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 77282, "pLev": 129, "rLev": 129, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 106, "amountStr": "¥106", "subAmount": 53, "subAmountStr": "日均¥53", "originalDailyPrice": 58, "detail": [{"code": "1001", "name": "租车费", "amount": 116, "amountDesc": "¥116"}, {"code": "3641", "name": "铂金贵宾", "amount": 10, "amountDesc": "¥10"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 186, "amountStr": "¥186", "subAmount": 196, "subAmountStr": "¥196", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3012", "vendorCode": "13032", "pStoreCode": "2961", "vehicleCode": "0", "packageType": 1, "skuId": 226171}}, {"reference": {"bizVendorCode": "SD3866", "vendorCode": "13094", "pStoreCode": "188084", "vehicleCode": "0", "packageType": 1, "skuId": 5354011}}, {"reference": {"bizVendorCode": "SD4410", "vendorCode": "62167", "pStoreCode": "114325", "vehicleCode": "0", "packageType": 1, "skuId": 1923000}}, {"reference": {"bizVendorCode": "SD3127", "vendorCode": "15000088", "pStoreCode": "161973", "vehicleCode": "0", "packageType": 1, "skuId": 6828570}}, {"reference": {"bizVendorCode": "SD6991", "vendorCode": "13088", "pStoreCode": "138344", "vehicleCode": "0", "packageType": 0, "skuId": 3166315}}], "reactId": "1116435081", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["13031_0_78_78"]}, "minTPrice": 186, "minDPrice": 53, "modifySameVehicle": false, "minDOrinPrice": 58, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "铂金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3641", "groupCode": "MarketGroup1320", "amountTitle": "已减10", "groupId": 1, "mergeId": 0}, "priceSize": 6, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5472", "sortNum": 4, "lowestPrice": 48, "highestPrice": 419, "maximumRating": 5, "maximumCommentCount": 38086, "lowestDistance": 0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3047", "vendorCode": "13071", "pStoreCode": "101002", "rStoreCode": "101002", "vehicleCode": "0", "packageId": "Secure", "packageType": 1, "comPriceCode": "********************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_MTAxMDAyXzU0NzJfMV82OC4wXzEzNi4wXzAuMF8yMTYuMF80OC4wXzE3NS4wXzBfMF8wLjBfMC4wXzYwLjBfMjAuMF8wXzBfNTExNTIwMg==", "vendorVehicleCode": "19061", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 5115202, "pLev": 13224, "rLev": 13224, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 95, "amountStr": "¥95", "subAmount": 48, "subAmountStr": "日均¥48", "originalDailyPrice": 68, "detail": [{"code": "1001", "name": "租车费", "amount": 136, "amountDesc": "¥136"}, {"code": "3765", "name": "新能源特惠", "amount": 41, "amountDesc": "¥41"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 175, "amountStr": "¥175", "subAmount": 216, "subAmountStr": "¥216", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD3127", "vendorCode": "15000088", "pStoreCode": "161973", "vehicleCode": "0", "packageType": 1, "skuId": 6828563}}, {"reference": {"bizVendorCode": "SD4849", "vendorCode": "80431", "pStoreCode": "114886", "vehicleCode": "0", "packageType": 0, "skuId": 5115193}}, {"reference": {"bizVendorCode": "SD4701", "vendorCode": "74573", "pStoreCode": "114529", "vehicleCode": "0", "packageType": 1, "skuId": 1909488}}, {"reference": {"bizVendorCode": "SD4734", "vendorCode": "77081", "pStoreCode": "114575", "vehicleCode": "0", "packageType": 0, "skuId": 5115192}}, {"reference": {"bizVendorCode": "SD3942", "vendorCode": "53893", "pStoreCode": "107119", "vehicleCode": "0", "packageType": 1, "skuId": 5115191}}, {"reference": {"bizVendorCode": "13031", "vendorCode": "13031", "pStoreCode": "78", "vehicleCode": "0", "packageType": 1, "skuId": 2286911}}, {"reference": {"bizVendorCode": "SD3012", "vendorCode": "13032", "pStoreCode": "2961", "vehicleCode": "0", "packageType": 1, "skuId": 2286912}}, {"reference": {"bizVendorCode": "SD8515", "vendorCode": "15001547", "pStoreCode": "182567", "vehicleCode": "0", "packageType": 1, "skuId": 5197732}}, {"reference": {"bizVendorCode": "SD3926", "vendorCode": "13082", "pStoreCode": "192844", "vehicleCode": "0", "packageType": 1, "skuId": 5662874}}, {"reference": {"bizVendorCode": "SD4552", "vendorCode": "67709", "pStoreCode": "114723", "vehicleCode": "0", "packageType": 1, "skuId": 5115177}}], "reactId": "1116435082", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3047_0_101002_101002"]}, "minTPrice": 175, "minDPrice": 48, "modifySameVehicle": false, "minDOrinPrice": 68, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "新能源特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3765", "groupCode": "MarketGroup1358", "amountTitle": "已减41", "groupId": 1, "mergeId": 0}, "priceSize": 11, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "1149", "sortNum": 5, "lowestPrice": 81, "highestPrice": 183, "maximumRating": 5, "maximumCommentCount": 38086, "lowestDistance": 0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3012", "vendorCode": "13032", "pStoreCode": "2961", "rStoreCode": "2961", "vehicleCode": "0", "packageId": "Secure", "packageType": 1, "comPriceCode": "****************************************************************************************************************************************", "priceVersion": "SH-PRICEVERSION_Mjk2MV8xMTQ5XzFfODguMF8xNzYuMF8wLjBfMjU2LjBfODEuMF8yNDEuMF8wXzBfMC4wXzAuMF82MC4wXzIwLjBfMF8wXzIyNzEyNA==", "vendorVehicleCode": "18671", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 227124, "pLev": 1032, "rLev": 1032, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 161, "amountStr": "¥161", "subAmount": 81, "subAmountStr": "日均¥81", "originalDailyPrice": 88, "detail": [{"code": "1001", "name": "租车费", "amount": 176, "amountDesc": "¥176"}, {"code": "3641", "name": "铂金贵宾", "amount": 15, "amountDesc": "¥15"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 241, "amountStr": "¥241", "subAmount": 256, "subAmountStr": "¥256", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4552", "vendorCode": "67709", "pStoreCode": "114723", "vehicleCode": "0", "packageType": 1, "skuId": 2457059}}, {"reference": {"bizVendorCode": "13031", "vendorCode": "13031", "pStoreCode": "78", "vehicleCode": "0", "packageType": 1, "skuId": 67380}}, {"reference": {"bizVendorCode": "SD7763", "vendorCode": "13067", "pStoreCode": "67462", "vehicleCode": "0", "packageType": 1, "skuId": 5131449}}, {"reference": {"bizVendorCode": "SD3926", "vendorCode": "13082", "pStoreCode": "183936", "vehicleCode": "0", "packageType": 1, "skuId": 5047689}}, {"reference": {"bizVendorCode": "SD6991", "vendorCode": "13088", "pStoreCode": "138344", "vehicleCode": "0", "packageType": 0, "skuId": 3166309}}, {"reference": {"bizVendorCode": "SD4130", "vendorCode": "13115", "pStoreCode": "188937", "vehicleCode": "0", "packageType": 1, "skuId": 6370241}}], "reactId": "1116435092", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3012_0_2961_2961"]}, "minTPrice": 241, "minDPrice": 81, "modifySameVehicle": false, "minDOrinPrice": 88, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "铂金贵宾", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3641", "groupCode": "MarketGroup1320", "amountTitle": "已减15", "groupId": 1, "mergeId": 0}, "priceSize": 7, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选：免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}], "dailyPrice": 27, "hasResult": true}, {"groupCode": "2", "groupName": "经济轿车", "sortNum": 0, "dailyPrice": 27, "hasResult": true}, {"groupCode": "newenergy", "groupName": "新能源", "sortNum": 2, "dailyPrice": 27, "hasResult": true}, {"groupCode": "3", "groupName": "舒适轿车", "sortNum": 3, "dailyPrice": 48, "hasResult": true}, {"groupCode": "6", "groupName": "SUV", "sortNum": 4, "dailyPrice": 40, "hasResult": true}, {"groupCode": "4", "groupName": "商务车", "sortNum": 5, "dailyPrice": 90, "hasResult": true}, {"groupCode": "5", "groupName": "豪华轿车", "sortNum": 6, "dailyPrice": 132, "hasResult": true}, {"groupCode": "9", "groupName": "跑车", "sortNum": 7, "dailyPrice": 179, "hasResult": true}, {"groupCode": "11", "groupName": "房车", "sortNum": 8, "dailyPrice": 680, "hasResult": true}, {"groupCode": "7", "groupName": "小巴士", "sortNum": 9, "dailyPrice": 99, "hasResult": true}, {"groupCode": "10", "groupName": "皮卡", "sortNum": 10, "dailyPrice": 145, "hasResult": true}], "productGroupsHashCode": "G51uj22kC3056MF97p4r", "storeList": [{"storeCode": "114080", "pickUpLevel": 41291, "pickOffLevel": 41291}, {"storeCode": "182996", "pickUpLevel": 135835, "pickOffLevel": 135835}, {"storeCode": "114325", "pickUpLevel": 43591, "pickOffLevel": 43591}, {"storeCode": "176280", "pickUpLevel": 121286, "pickOffLevel": 121287}, {"storeCode": "183036", "pickUpLevel": 143345, "pickOffLevel": 143345}, {"storeCode": "182916", "pickUpLevel": 136429, "pickOffLevel": 136429}, {"storeCode": "183936", "pickUpLevel": 292, "pickOffLevel": 292}, {"storeCode": "78", "pickUpLevel": 2761, "pickOffLevel": 2761}, {"storeCode": "115347", "pickUpLevel": 50259, "pickOffLevel": 50259}, {"storeCode": "175820", "pickUpLevel": 131640, "pickOffLevel": 131640}, {"storeCode": "188084", "pickUpLevel": 872, "pickOffLevel": 872}, {"storeCode": "138344", "pickUpLevel": -1, "pickOffLevel": -1}, {"storeCode": "67462", "pickUpLevel": 20220801, "pickOffLevel": 20220801}, {"storeCode": "117341", "pickUpLevel": 87379, "pickOffLevel": 87379}, {"storeCode": "2961", "pickUpLevel": 8536, "pickOffLevel": 8536}, {"storeCode": "115295", "pickUpLevel": 49965, "pickOffLevel": 49965}, {"storeCode": "182870", "pickUpLevel": 202096, "pickOffLevel": 202096}, {"storeCode": "181864", "pickUpLevel": 1091, "pickOffLevel": 1091}, {"storeCode": "107119", "pickUpLevel": 30154, "pickOffLevel": 30154}, {"storeCode": "114723", "pickUpLevel": 45437, "pickOffLevel": 45437}, {"storeCode": "175686", "pickUpLevel": 119432, "pickOffLevel": 119432}, {"storeCode": "182567", "pickUpLevel": 136515, "pickOffLevel": 136515}, {"storeCode": "101002", "pickUpLevel": 10213, "pickOffLevel": 10213}, {"storeCode": "114886", "pickUpLevel": 68967, "pickOffLevel": 68967}, {"storeCode": "116966", "pickUpLevel": 61439, "pickOffLevel": 61439}, {"storeCode": "114575", "pickUpLevel": 47132, "pickOffLevel": 47132}, {"storeCode": "183459", "pickUpLevel": 136614, "pickOffLevel": 136614}, {"storeCode": "107479", "pickUpLevel": 82305, "pickOffLevel": 82305}, {"storeCode": "114447", "pickUpLevel": 43934, "pickOffLevel": 43934}, {"storeCode": "192844", "pickUpLevel": 1411, "pickOffLevel": 1411}, {"storeCode": "114043", "pickUpLevel": 6947, "pickOffLevel": 6947}, {"storeCode": "161973", "pickUpLevel": 98018, "pickOffLevel": 98018}, {"storeCode": "114529", "pickUpLevel": 97105, "pickOffLevel": 97105}, {"storeCode": "188937", "pickUpLevel": 895, "pickOffLevel": 895}], "commNotices": [], "rentCenter": {"id": 2, "name": "三亚凤凰机场店", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.308537", "lng": "109.413536", "filterCode": "Vendor_0", "fromTime": "00:00", "toTime": "23:59"}, "rRentCenter": {"id": 2, "name": "三亚凤凰机场店", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.308537", "lng": "109.413536", "filterCode": "Vendor_0", "fromTime": "00:00", "toTime": "23:59"}, "promptInfos": [{"contents": [{"contentStyle": "0", "stringObjs": [{"content": "您租车可享受“铂金用户租车费92折起”专属优惠~", "style": "0"}]}], "icon": "https://pages.c-ctrip.com/commerce/promote/car/app/images/1112.png"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "优质车况", "type": 1, "description": "3年内车龄", "sortNum": 3, "subTitle": "", "showLayer": 0}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满", "sortNum": 10, "subTitle": "", "showLayer": 0}, {"title": "车辆守护升级", "type": 0, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 99, "subTitle": "*覆盖损失范围以预订页面内披露为准"}]}, "basicData": {"sortItems": [{"title": "推荐排序", "type": 1, "code": "1", "sortNum": 1}, {"title": "总价 低→高", "type": 2, "code": "2", "sortNum": 2}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "labelCodes": ["3563", "3510", "3696", "3697", "3653", "3698", "3731", "3810", "3679", "3779", "3757", "3650", "3495", "3494", "3504", "3548", "3705", "3547", "3827", "3503", "3502", "3501", "3709", "3641", "3765", "3509", "3788", "3789", "3746"], "isAll": false, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isLastPage": false, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "sTitle": "9大无忧特权", "hint": "优选门店无忧租超值价"}, {"id": 2, "title": "无忧租 安心首选", "sTitle": "9大无忧特权", "hint": "无忧租超值价"}, {"id": 3, "title": "全国连锁 服务放心", "sTitle": "", "hint": "一嗨租车超值价"}, {"id": 4, "title": "上门送取车 取还超便捷", "sTitle": "", "hint": "送车上门超值价"}, {"id": 5, "title": "信用租 押金双免", "sTitle": "", "hint": "押金双免超值价"}, {"id": 6, "title": "新车保障 车况佳", "sTitle": "", "hint": "新车超值价"}, {"id": 7, "title": "超值特价 高性价比", "sTitle": "", "hint": "超值特价"}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "hasResultWithoutFilter": true, "isFromSearch": true, "productGroupCodeUesd": "all", "frontTraceInfo": {"vehicleList": ["4722", "4845", "4846", "4980", "4982", "17298", "4861", "4740", "1", "2437", "1466", "2794", "4972", "4732", "4855", "4977", "4735", "5704", "4736", "17389", "4750", "4993", "2451", "17380", "10634", "4741", "4866", "10630", "4626", "819", "17394", "4880", "4761", "4883", "17390", "17391", "2461", "1129", "1488", "4752", "5600", "4511", "5601", "4997", "4635", "5604", "4515", "4758", "4879", "4771", "17480", "10615", "1498", "2224", "10619", "1497", "953", "4644", "4526", "17378", "4781", "4660", "17373", "4662", "4663", "2243", "2484", "3450", "5508", "1149", "1148", "5622", "5502", "4897", "5623", "4413", "5503", "10620", "5504", "10622", "4659", "608", "5506", "10621", "17464", "17344", "4790", "4670", "3101", "3460", "2247", "1399", "2487", "5632", "4302", "4544", "5513", "5514", "859", "17589", "5517", "17356", "5410", "2384", "5409", "3469", "1048", "5643", "5402", "5644", "4676", "5524", "5525", "869", "506", "5526", "5407", "4319", "4571", "4572", "4330", "5420", "5422", "4454", "873", "1058", "874", "1178", "755", "2387", "999", "4325", "5415", "5658", "4449", "5418", "5419", "4583", "4101", "5433", "4102", "4577", "5303", "5425", "648", "5305", "5428", "4470", "4593", "5320", "5564", "4233", "5444", "5686", "4476", "3262", "3261", "1081", "652", "3499", "656", "4103", "5676", "658", "4588", "5557", "4107", "4228", "4108", "4109", "5570", "4482", "5572", "4483", "5694", "5574", "5453", "5695", "5332", "5575", "5454", "5696", "5576", "1096", "540", "300", "301", "302", "4598", "4478", "5325", "548", "4358", "5205", "5206", "5569", "4238", "5329", "4493", "5341", "4010", "4495", "5584", "5342", "5222", "6310", "4496", "5101", "5465", "5223", "4498", "5466", "5587", "2078", "3164", "672", "311", "3159", "315", "679", "4004", "5698", "4367", "5699", "4489", "5579", "4369", "4249", "5218", "4261", "5472", "5351", "6320", "5594", "5474", "5353", "5595", "5111", "4022", "5596", "5355", "4024", "6324", "6323", "3176", "3052", "5590", "4380", "682", "3169", "326", "5104", "5588", "4378", "5226", "4139", "5349", "5360", "5482", "3180", "5241", "4031", "6333", "5486", "5245", "5004", "5125", "4036", "5480", "4391", "452", "456", "3059", "216", "5599", "5479", "218", "4149", "5359", "5371", "5493", "5010", "4042", "5131", "5253", "4043", "5374", "6344", "4165", "4044", "5254", "4288", "5257", "5499", "340", "220", "468", "589", "5368", "6337", "6336", "5007", "5128", "4294", "5141", "5142", "4053", "17962", "5144", "5023", "5145", "4058", "591", "471", "474", "476", "5258", "5017", "17968", "17605", "17606", "17724", "5393", "5151", "5272", "5396", "4066", "4067", "5399", "3095", "247", "248", "17616", "17739", "17732", "5041", "5283", "5284", "5163", "5164", "5044", "5165", "5046", "5167", "5289", "6258", "28", "5047", "6257", "4191", "5281", "5282", "4193", "255", "138", "17941", "17700", "5294", "33", "5173", "5295", "4086", "5175", "5176", "5297", "4088", "5179", "5058", "39", "5170", "5291", "4082", "267", "17718", "17719", "17712", "5063", "6276", "5187", "5068", "49", "4092", "4093", "6271", "4094", "2900", "2905", "5074", "5198", "6288", "6281", "5071", "5193", "5194", "286", "166", "1820", "288", "289", "17819", "17931", "17811", "17812", "65", "68", "6297", "6290", "173", "6294", "175", "2801", "177", "178", "75", "5092", "5094", "1724", "17443", "89", "17561", "2706", "17567", "2705", "2704", "97", "98", "1500", "17421", "17423", "17780", "2842", "2721", "4907", "1991", "17307", "2729", "17424", "17425", "17426", "2605", "17427", "17554", "17550", "1404", "4916", "1644", "2732", "1401", "17678", "4911", "1407", "17437", "4912", "1406", "1405", "17762", "17641", "17764", "4804", "4805", "4808", "17651", "17774", "17411", "4936", "4819", "17538", "17413", "17414", "4814", "17416", "1437", "4826", "2888", "2884", "4940", "17507", "17628", "4943", "17622", "17864", "4823", "17624", "4946", "17753", "4960", "17870", "1450", "4958", "4837", "4716", "4959", "4717", "3502", "1444", "2893", "17639", "4832", "4834", "17514", "4835", "17998", "1449", "17999"], "vehicleGroupMap": {"2": 55, "3": 22, "4": 73, "5": 50, "6": 224, "7": 1, "9": 71, "10": 4, "11": 4, "newenergy": 141}, "priceCount": 4054, "normalCount": 4054, "easyLifeCount": 0, "zhimaCount": 4015, "vendorNames": ["小飞侠租车", "龙之祥租车", "凯美租车", "海南松舍租车", "普信租车", "三亚威途租车", "超联汇租车", "时间旅行租车", "信华租车", "钱雨租车", "泰信吉租车", "八骏马租车", "海盗租车", "海鸭鸭租车", "华鑫海租车", "斯方达租车", "瑞赢租车", "伟振租车", "立强租车", "三亚旅途中租车", "流浪者租车 ", "你我他租车", "清晨租车", "枫叶租车", "海程租车", "么么达租车", "海途租车", "大方租车", "龙运三亚租车", "顺利出行", "琼州租车", "泓达租车", "豪通租车", "五行租车", "格莱拓租车", "博格达租车", "恒驰租车", "好鑫情租车", "世海租车", "泊隽租车", "海南友途租车", "百募租车", "三亚五二零租车", "榴莲租车", "日之星丰田租车", "三亚浩宇租车", "旭辰租车", "HZD租车", "陵水铭途租车", "海立达租车", "森燚租车", "朗速出行", "海越租车", "四季嘉行租车", "车游天下租车", "金森租车", "三亚启航租车", "车速递租车", "陶陶租车", "海南途达租车", "北蒙租车", "瑞海租车", "宏广东盈租车", "博利租车", "溶阔租车", "海岛行租车", "车先生", "海南中进租车", "安欣租车", "芯瑜租车", "云超租车", "名仕租车", "三亚皖太租车", "新奇租车", "安途生汽车租车", "海心租车", "潮人商旅租车", "常晟租车", "三亚新概念租车", "河北唐亚租车", "通源租车", "海南启航租车", "永卓租车", "野涵租车", "三亚宝驰租车", "东辉租车", "欣梦租车", "懒人行租车", "聚通达租车", "文东租车", "海南龙驰租车", "腾越租车", "京海亚租车", "安鑫莱租车", "欣岳美行租车", "港梦超跑俱乐部租车", "旺亚租车", "蔚小理租车", "领客出行", "凤翔天涯租车", "军盛租车", "海语租车", "畅行无忧租车", "三亚百鸿租车", "悠逸租车", "三亚永捷租车 ", "盛京海纳租车", "有友租车", "金爵万象租车", "欣博祥租车", "世通出行", "捷安利达租车", "彩车坊租车", "皓轩跑车俱乐部租车 ", "广隆商旅租车", "逍遥租车", "易达通租车", "远恒租车", "丰田海南出行", "夏末微凉租车", "哈尔滨奥朗租车", "小米租车", "悦萌动租车", "邦尼租车", "麒麟恒泰租车", "钛万绅租车", "行者天下租车", "租租侠租车", "基福租车", "鼎峰租车", "环岛租车", "蜜丝租车", "桐叶租车", "三亚蚂蚁租车", "鹏顺通租车", "木沐租车", "钧通租车", "爱尚出行租车", "海南鹏程租车", "启捷租车", "潆莹租车", "壹优租车", "恒想租车", "金晟租车", "EVCARD租车", "锋达租车", "福斯特租车", "星锐租车", "小红帽租车", "如亚租车", "商旅出行", "美津租车", "安潞通租车", "鸿发租车", "摇个车租车", "慧霏租车", "海宸租车", "高福租车", "细杰租车", "优享旅途租车", "车主角租车", "睿睿租车", "三亚易云租车", "青草兔租车", "金亚租车", "炜晨租车", "大权租车", "器车出行", "博雅租车", "晟亚租车", "诚航租车", "乐意出行", "助旅租车", "三亚恒泰租车", "中融顺达租车", "海漫租车", "途新租车", "一嗨租车", "可爱屋租车", "豪享荟租车", "黑桃壹租车", "塞伯坦租车", "星钺租车", "金达莱租车", "铭驱租车", "安米租车", "名都租车", "华瑞租车", "利资租车", "太平洋租车", "博之纳租车", "果岭出行", "峰硕租车", "京海租车", "晨阳租车", "汪澜租车", "玛雅租车", "海南悟空汽车租赁", "吉驰租车", "晨炫租车", "呈阜租车", "澳泰租车", "黑娃租车", "豫海租车", "海南椰林情租车", "盛鸿轩租车", "三亚神风租车", "骑仕租车", "卢米租车", "钰鑫租车", "轩宇租车", "加加租车", "博豪租车", "拾柒出行", "海南点赞租车", "汇驰租车", "照成租车", "一路平安租车", "振亚租车", "玖捌陆租车", "津池租车", "三亚洪顺租车", "全季租车", "盛泽租车", "乐达通租车", "虫子邦租车", "飞扬租车", "宏驰智行租车", "铭鸿租车", "小木鱼租车", "艾思租车", "宁洋租车", "宜养出行", "明昊租车", "海南龙运租车", "车租婆租车", "祥驰租车", "琼城租车", "海南锦程租车", "三亚世纪联合租车", "程硕租车", "三亚易安达租车", "新易达租车", "三亚达乐租车", "准典出行", "启瑞盛租车", "三亚融泽租车", "鑫晟唐租车", "北新租车", "鲸鹏出行", "车之美租车", "禧瑞达租车", "纳贝拉租车", "壹阳租车", "三鹤租车", "盛兴隆租车", "山水云途租车", "亿鑫租车", "麒麟火租车"]}, "uniqSign": "320011515102354794085XQ52AU0N418i216qC33", "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "pHub": 1, "rHub": 1, "promotMap": {}, "extras": {"isNewLicensePlate": "0", "serverRequestId": "8aGNVG03Pt5b1U198s66", "abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B", "commodityClass2Version": "1"}, "isRecommend": false, "isKlbData": true}