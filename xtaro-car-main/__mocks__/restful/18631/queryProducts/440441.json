{"baseResponse": {"isSuccess": true, "code": "201", "returnMsg": "SUCCESS", "requestId": "b615821a-400e-45e5-9abc-c5f41d6ea8cc", "extMap": {"apiCost": "2147.0", "dataConvertResCost": "33.0", "allCost": "2180.0", "start": "2023-08-01 18:04:06", "end": "2023-08-01 18:04:08", "pageName": "List", "uid": "M195700972", "pickupCityId": "347", "dropoffCityId": "347", "restCost": "1"}, "extraIndexTags": {"bizVendorCode": "14000", "normalCount": "1"}, "apiResCodes": [], "hasResult": true}, "requestInfo": {"pickupDate": "2023-09-07 10:00:00", "pickupLocationName": "小托奇欧酒店(Little Tokyo Hotel)", "returnDate": "2023-09-08 10:00:00", "returnLocationName": "小托奇欧酒店(Little Tokyo Hotel)", "rentalDay": 1, "sourceCountryId": 1, "age": 30, "pLatitude": 34.050045, "rLatitude": 34.050045, "rLongitude": -118.23997, "pLongitude": -118.23997, "pDate": "20230907100000", "rDate": "20230908100000"}, "allVehicleCount": 6, "allVendorPriceCount": 6, "filterMenuItems": [{"name": "品牌/配置", "code": "BrandAndAccessory", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"name": "座位数", "sortNum": 1, "groupCode": "SeatGroup", "bitwiseType": 2, "filterItems": [{"itemCode": "Seat_1", "name": "2座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Seat_2", "name": "4座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Seat_3", "name": "5座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Seat_4", "name": "6座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Seat_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 1, "isQuickItem": false}, {"itemCode": "Seat_6", "name": "8座及以上", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 1, "isQuickItem": false}], "shortName": "座位", "isSupportMulti": true}, {"name": "车辆排挡", "sortNum": 2, "groupCode": "Transmission", "bitwiseType": 2, "filterItems": [{"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Transmission_2", "name": "手动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}], "shortName": "排挡"}, {"name": "车辆配置", "sortNum": 5, "groupCode": "VehicleAccessory", "bitwiseType": 1, "filterItems": [{"itemCode": "VehicleAccessory_AirConditioner", "name": "空调", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 512, "sortNum": 5, "isQuickItem": false}], "shortName": "车辆配置", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 6, "groupCode": "Brand", "bitwiseType": 2, "filterItems": [{"itemCode": "Brand_51", "name": "大众", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"itemCode": "Brand_49", "name": "丰田", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "Brand_25", "name": "起亚", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}, {"itemCode": "Brand_9", "name": "克莱斯勒", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/kelaisile.png"}, {"itemCode": "Brand_8", "name": "雪佛兰", "groupCode": "Brand", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "shortName": "车辆品牌", "isSupportMulti": true}]}, {"name": "门店/服务", "code": "StoreAndPR", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"name": "门店距离", "sortNum": 2, "groupCode": "DistanceGroup", "bitwiseType": 2, "filterItems": [{"itemCode": "Distance_M500", "name": "500米内", "groupCode": "DistanceGroup", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Distance_K1", "name": "1公里内", "groupCode": "DistanceGroup", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Distance_K2", "name": "2公里内", "groupCode": "DistanceGroup", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Distance_K5", "name": "5公里内", "groupCode": "DistanceGroup", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 2, "isQuickItem": false}], "shortName": "距离"}, {"name": "点评", "sortNum": 3, "groupCode": "Comment", "bitwiseType": 2, "filterItems": [{"itemCode": "Comment_4.5", "name": "4.5分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Comment_4.0", "name": "4.0分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Comment_3.5", "name": "3.5分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}], "shortName": "点评"}, {"name": "门店服务", "sortNum": 10, "groupCode": "StoreService", "bitwiseType": 1, "filterItems": [{"itemCode": "StoreService_3564", "name": "立即确认", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 1, "sortNum": 10, "isQuickItem": true, "quickSortNum": 120001}, {"itemCode": "StoreService_3563", "name": "免费取消", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 2, "sortNum": 10, "isQuickItem": true, "quickSortNum": 70001}], "shortName": "门店服务", "isSupportMulti": true}, {"name": "特色服务", "sortNum": 11, "groupCode": "SpecialService", "bitwiseType": 2, "filterItems": [{"itemCode": "SpecialService_3557", "name": "满油取还", "groupCode": "SpecialService", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 11, "isQuickItem": true, "quickSortNum": 100005, "positionCode": "10"}, {"itemCode": "SpecialService_3491", "name": "24h营业", "groupCode": "SpecialService", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 11, "isQuickItem": true, "quickSortNum": 100006, "positionCode": "10"}], "shortName": "特色服务", "isSupportMulti": true}]}, {"name": "筛选", "code": "filter", "sortNum": 3, "hierarchy": 1, "filterGroups": [{"name": "价格", "sortNum": 1, "groupCode": "Price", "filterItems": [{"itemCode": "Price_0-100", "name": "¥100以下", "code": "0-100", "groupCode": "Price", "sortNum": 0}, {"itemCode": "Price_100-200", "name": "¥100-200", "code": "100-200", "groupCode": "Price", "sortNum": 0}, {"itemCode": "Price_200-300", "name": "¥200-300", "code": "200-300", "groupCode": "Price", "sortNum": 0}, {"itemCode": "Price_300-400", "name": "¥300-400", "code": "300-400", "groupCode": "Price", "sortNum": 0}, {"itemCode": "Price_400-99999", "name": "¥400以上", "code": "400-99999", "groupCode": "Price", "sortNum": 0}], "shortName": "价格"}, {"name": "支付方式", "sortNum": 2, "groupCode": "PayMode", "bitwiseType": 2, "filterItems": [{"itemCode": "PayMode_PayOnLine", "name": "在线支付", "groupCode": "PayMode", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "PayMode_PayOnarrived", "name": "到店支付", "groupCode": "PayMode", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}], "shortName": "支付方式"}, {"name": "驾照要求", "sortNum": 3, "groupCode": "DriverLience", "bitwiseType": 2, "filterItems": [{"itemCode": "DriverLience_lt1001", "name": "仅需中国大陆驾照", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": true, "quickSortNum": 50002}, {"itemCode": "DriverLience_lt1002", "name": "支持中国驾照原件+驾照国际翻译认证件", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 3, "isQuickItem": false}, {"itemCode": "DriverLience_lt1003", "name": "支持中国驾照原件+车行翻译件", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "DriverLience_lt1004", "name": "支持中国驾照原件+当地语言公证件", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 3, "isQuickItem": false}, {"itemCode": "DriverLience_lt1006", "name": "支持香港驾照", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 3, "isQuickItem": false}, {"itemCode": "DriverLience_lt1014", "name": "支持国际驾照IDP+签发国当地驾照", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 3, "isQuickItem": false}], "shortName": "驾照要求", "isSupportMulti": true}, {"name": "押金方式", "sortNum": 4, "groupCode": "CreditCard", "bitwiseType": 2, "filterItems": [{"itemCode": "CreditCard_DepositFree", "name": "免押金", "groupCode": "CreditCard", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": true, "quickSortNum": 9999}, {"itemCode": "CreditCard_SupportUnionLogoNew", "name": "支持银联", "groupCode": "CreditCard", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 3, "isQuickItem": false}, {"itemCode": "CreditCard_SupportNoEmbossed", "name": "支持非凸字信用卡", "groupCode": "CreditCard", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false}, {"itemCode": "CreditCard_SupportNoChip", "name": "支持非芯片信用卡", "groupCode": "CreditCard", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}], "shortName": "押金方式", "isSupportMulti": true}, {"name": "里程限制", "sortNum": 5, "groupCode": "Limit", "bitwiseType": 2, "filterItems": [{"itemCode": "Mileage_Unlimited", "name": "不限里程", "groupCode": "Limit", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 5, "isQuickItem": true, "quickSortNum": 110001}, {"itemCode": "Mileage_Limited", "name": "限里程", "groupCode": "Limit", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 5, "isQuickItem": false}], "shortName": "里程限制"}, {"name": "租车公司", "sortNum": 6, "groupCode": "Vendor_0", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_SD0007", "name": "<PERSON><PERSON>", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_SD0008", "name": "<PERSON><PERSON>", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 7, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}]}], "quickFilter": [{"itemCode": "CreditCard_DepositFree", "name": "免押金", "groupCode": "CreditCard", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": true, "quickSortNum": 9999}, {"itemCode": "DriverLience_lt1001", "name": "仅需中国大陆驾照", "groupCode": "DriverLience", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": true, "quickSortNum": 50002}, {"itemCode": "StoreService_3563", "name": "免费取消", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 2, "sortNum": 10, "isQuickItem": true, "quickSortNum": 70001}, {"itemCode": "SpecialService_3557", "name": "满油取还", "groupCode": "SpecialService", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 11, "isQuickItem": true, "quickSortNum": 100005, "positionCode": "10"}, {"itemCode": "Mileage_Unlimited", "name": "不限里程", "groupCode": "Limit", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 5, "isQuickItem": true, "quickSortNum": 110001}, {"itemCode": "StoreService_3564", "name": "立即确认", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 1, "sortNum": 10, "isQuickItem": true, "quickSortNum": 120001}], "filterItemExts": [], "vehicleList": [{"brandId": 51, "brandEName": "Volkswagen", "name": "大众 捷达 Jetta", "zhName": "大众 捷达 Jetta", "vehicleCode": "10993", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/VW_Jetta.png", "groupCode": "G03", "groupSubClassCode": "S", "groupName": "中大型车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "imageList": ["//pic.c-ctrip.com/car/osd/online/vehicle_new/VW_Jetta.png"], "userRealImageCount": 0, "isSpecialized": false, "isHot": false, "recommendDesc": "常见的家用车型，三人短途首选", "hasConditioner": true, "conditionerDesc": "A/C", "spaceDesc": "建议乘坐3人+2行李箱", "similarCommentDesc": "空间类似福特福克斯", "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/vw-Jetta.jpg", "groupSubName": "标准型轿车"}, {"brandId": 49, "brandEName": "Toyota", "name": "丰田 RAV4", "zhName": "丰田 RAV4", "vehicleCode": "10931", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Toyota_RAV4.png", "groupCode": "G05", "groupSubClassCode": "J", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 4, "imageList": ["//pic.c-ctrip.com/car/osd/online/vehicle_new/Toyota_RAV4.png"], "userRealImageCount": 0, "isSpecialized": false, "isHot": false, "recommendDesc": "操控灵活，视野开阔", "hasConditioner": true, "conditionerDesc": "A/C", "spaceDesc": "建议乘坐4人+2行李箱", "similarCommentDesc": "空间类似大众途观", "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/Toyota--Rav4.jpg", "groupSubName": "中型SUV"}, {"brandId": 25, "brandEName": "<PERSON><PERSON>", "name": "起亚 Soul", "zhName": "起亚 Soul", "vehicleCode": "10484", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Kia_Soul.png", "groupCode": "G02", "groupSubClassCode": "C", "groupName": "小型轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "imageList": ["//pic.c-ctrip.com/car/osd/online/vehicle_new/Kia_Soul.png"], "userRealImageCount": 0, "isSpecialized": false, "isHot": false, "recommendDesc": "经济省油，适合两人短途", "hasConditioner": true, "conditionerDesc": "A/C", "spaceDesc": "建议乘坐2人+2行李箱", "similarCommentDesc": "空间类似大众POLO", "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/kia-Soul.jpg", "groupSubName": "紧凑型轿车"}, {"brandId": 9, "brandEName": "Chrysler", "name": "克莱斯勒 Pacifica", "zhName": "克莱斯勒 Pacifica", "vehicleCode": "11076", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Chrysler_Pacifica.png", "groupCode": "G07", "groupSubClassCode": "K", "groupName": "MPV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 4, "luggageNo": 2, "imageList": ["//pic.c-ctrip.com/car/osd/online/vehicle_new/Chrysler_Pacifica.png"], "userRealImageCount": 0, "isSpecialized": false, "isHot": false, "recommendDesc": "空间较大，乘坐舒适", "hasConditioner": true, "conditionerDesc": "A/C", "spaceDesc": "建议乘坐5人+2行李箱", "similarCommentDesc": "空间类似本田JADE", "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/Chrysler-Pacifica.jpg", "groupSubName": "迷你商务车"}, {"brandId": 49, "brandEName": "Toyota", "name": "丰田 凯美瑞 Camry", "zhName": "丰田 凯美瑞 Camry", "vehicleCode": "10871", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Toyota_Camry.png", "groupCode": "G03", "groupSubClassCode": "P", "groupName": "中大型车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "imageList": ["//pic.c-ctrip.com/car/osd/online/vehicle_new/Toyota_Camry.png"], "userRealImageCount": 0, "isSpecialized": false, "isHot": false, "recommendDesc": "空间较大，乘坐舒适", "hasConditioner": true, "conditionerDesc": "A/C", "spaceDesc": "建议乘坐4人+2行李箱", "similarCommentDesc": "空间类似大众帕萨特", "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/Toyota-Camry.jpg", "groupSubName": "大型轿车"}, {"brandId": 8, "brandEName": "Chevrolet", "name": "雪佛兰 Traverse", "zhName": "雪佛兰 Traverse", "vehicleCode": "10183", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Chevrolet_Traverse.png", "groupCode": "G05", "groupSubClassCode": "R", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 4, "luggageNo": 3, "imageList": ["//pic.c-ctrip.com/car/osd/online/vehicle_new/Chevrolet_Traverse.png"], "userRealImageCount": 0, "isSpecialized": false, "isHot": false, "recommendDesc": "舒适安全，适应多变路况", "hasConditioner": true, "conditionerDesc": "A/C", "spaceDesc": "建议乘坐5人+3行李箱", "similarCommentDesc": "空间类似现代圣达菲", "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/Chevrolet-Traverse.jpg", "groupSubName": "标准型SUV"}], "productGroups": [{"groupCode": "G02", "groupName": "小型轿车", "sortNum": 2, "productList": [{"vehicleCode": "10484", "sortNum": 0, "lowestPrice": 523, "highestPrice": 523, "maximumRating": 4.4, "maximumCommentCount": 5, "lowestDistance": 0.7312, "isSpecialized": false, "vendorPriceList": [{"vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "commentInfo": {"level": "不错", "commentCount": 5, "overallRating": "4.4", "maximumRating": 5, "commentLabel": "", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 523, "currentOriginalDailyPrice": 0, "currentTotalPrice": 523, "currentCurrencyCode": "CNY", "localCurrencyCode": "USD", "deductInfos": []}, "reference": {"bizVendorCode": "14000", "vendorCode": "SD0007", "pStoreCode": "LAXZILAXC18", "rStoreCode": "LAXZILAXC18", "vehicleCode": "10484", "packageId": "2379", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "B_CCAR_GROUPB-KIASOUL"}, "decoratorVendorType": 0, "isEasyLife": false, "payMode": 1, "bomCode": "LAXZILAXC18_10484_CDW_FRFB_Fees_SCDW_TP_ULM_0_0", "productCode": "9126723", "rateCode": "C4", "priceVersion": "ARYA9kGTFYZ0+7M30n+GVmZ63Q6CoRJc4GRe", "pCityId": 347, "rCityId": 347, "vendorVehicleCode": "CCAR_GROUPB-KIASOUL", "age": 30, "unionCardFilter": {}, "noDepositFilter": {}, "skuId": 806985, "sippCode": "CCAR"}, "promotions": [], "isSpecialized": false, "sortNum": 0, "vendorTag": {"title": "国际知名", "sortNum": 0}, "pStoreRouteDesc": "距离小托奇欧酒店(Little Tokyo Hotel) 731.2米，步行可达", "rStoreRouteDesc": "", "easyLifeInfo": {"isEasyLife": false}, "isBroker": false, "platformCode": "0", "allTags": [{"title": "立即确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。", "sortNum": 10, "colorCode": "8", "labelCode": "3564"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}], "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 4, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 512, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 6, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 14, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}], "reactId": "1804087533", "isPStoreSupportCdl": true, "isRStoreSupportCdl": true, "qualityScore": 0, "storeScore": 4.4, "isSelect": false, "orignalPriceStyle": "WithOutStrikethrough", "distance": 0.7312, "extMap": {"freeCancel": "true", "isULM": "true", "isNoOnewayFee": "false", "distance": "0.7312", "confirmRightNow": "true", "isConfirmTimeGth12": "false", "isVendorActive": "0", "isThirdInsurance": "false", "isAsiaPickup": "false", "isSupportZhima": "false"}, "ctripVehicleCode": "10484"}], "reactId": "1804087580", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14000", "vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "similarVehicleInfos": [{"vehicleCode": "10484", "vehicleName": "起亚 Soul", "vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Kia_Soul.png"}]}], "vehicleRecommendProduct": {"introduce": "同组车型最低价", "productCodes": ["9126723"]}, "minTPrice": 523}], "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/economy.png", "allowMerge": true, "dailyPrice": 523}, {"groupCode": "G03", "groupName": "中大型车", "sortNum": 3, "productList": [{"vehicleCode": "10993", "sortNum": 0, "lowestPrice": 581, "highestPrice": 581, "maximumRating": 4.4, "maximumCommentCount": 5, "lowestDistance": 0.7312, "isSpecialized": false, "vendorPriceList": [{"vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "commentInfo": {"level": "不错", "commentCount": 5, "overallRating": "4.4", "maximumRating": 5, "commentLabel": "", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 581, "currentOriginalDailyPrice": 0, "currentTotalPrice": 581, "currentCurrencyCode": "CNY", "localCurrencyCode": "USD", "deductInfos": []}, "reference": {"bizVendorCode": "14000", "vendorCode": "SD0007", "pStoreCode": "LAXZILAXC18", "rStoreCode": "LAXZILAXC18", "vehicleCode": "10993", "packageId": "2379", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "D_SCAR_GROUPD-VOLKSWAGENJETTA"}, "decoratorVendorType": 0, "isEasyLife": false, "payMode": 1, "bomCode": "LAXZILAXC18_10993_CDW_FRFB_Fees_SCDW_TP_ULM_0_0", "productCode": "9134502", "rateCode": "C4", "priceVersion": "ARYAFn+72zEnvqSok3enmkJgAA6CoRJc4GRe", "pCityId": 347, "rCityId": 347, "vendorVehicleCode": "SCAR_GROUPD-VOLKSWAGENJETTA", "age": 30, "unionCardFilter": {}, "noDepositFilter": {}, "skuId": 1318441, "sippCode": "SCAR"}, "promotions": [], "isSpecialized": false, "sortNum": 0, "vendorTag": {"title": "国际知名", "sortNum": 0}, "pStoreRouteDesc": "距离小托奇欧酒店(Little Tokyo Hotel) 731.2米，步行可达", "rStoreRouteDesc": "", "easyLifeInfo": {"isEasyLife": false}, "isBroker": false, "platformCode": "0", "allTags": [{"title": "立即确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。", "sortNum": 10, "colorCode": "8", "labelCode": "3564"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}], "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 1, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 512, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 6, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 14, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}], "reactId": "1804087555", "isPStoreSupportCdl": true, "isRStoreSupportCdl": true, "qualityScore": 0, "storeScore": 4.4, "isSelect": false, "orignalPriceStyle": "WithOutStrikethrough", "distance": 0.7312, "extMap": {"freeCancel": "true", "isULM": "true", "isNoOnewayFee": "false", "distance": "0.7312", "confirmRightNow": "true", "isConfirmTimeGth12": "false", "isVendorActive": "0", "isThirdInsurance": "false", "isAsiaPickup": "false", "isSupportZhima": "false"}, "ctripVehicleCode": "10993"}], "reactId": "1804087580", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14000", "vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "similarVehicleInfos": [{"vehicleCode": "10993", "vehicleName": "大众 捷达 Jetta", "vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/VW_Jetta.png"}]}], "vehicleRecommendProduct": {"introduce": "同组车型最低价", "productCodes": ["9134502"]}, "minTPrice": 581}, {"vehicleCode": "10871", "sortNum": 0, "lowestPrice": 615, "highestPrice": 615, "maximumRating": 4.4, "maximumCommentCount": 5, "lowestDistance": 0.7312, "isSpecialized": false, "vendorPriceList": [{"vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "commentInfo": {"level": "不错", "commentCount": 5, "overallRating": "4.4", "maximumRating": 5, "commentLabel": "", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 615, "currentOriginalDailyPrice": 0, "currentTotalPrice": 615, "currentCurrencyCode": "CNY", "localCurrencyCode": "USD", "deductInfos": []}, "reference": {"bizVendorCode": "14000", "vendorCode": "SD0007", "pStoreCode": "LAXZILAXC18", "rStoreCode": "LAXZILAXC18", "vehicleCode": "10871", "packageId": "2379", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "E_FCAR_GROUPE-TOYOTACAMRY"}, "decoratorVendorType": 0, "isEasyLife": false, "payMode": 1, "bomCode": "LAXZILAXC18_10871_CDW_FRFB_Fees_SCDW_TP_ULM_0_0", "productCode": "9128998", "rateCode": "C4", "priceVersion": "ARYAl/kcb8cbWvcCnCXx+G1hWQ6CoRJc4GRe", "pCityId": 347, "rCityId": 347, "vendorVehicleCode": "FCAR_GROUPE-TOYOTACAMRY", "age": 30, "unionCardFilter": {}, "noDepositFilter": {}, "skuId": 807613, "sippCode": "FCAR"}, "promotions": [], "isSpecialized": false, "sortNum": 0, "vendorTag": {"title": "国际知名", "sortNum": 0}, "pStoreRouteDesc": "距离小托奇欧酒店(Little Tokyo Hotel) 731.2米，步行可达", "rStoreRouteDesc": "", "easyLifeInfo": {"isEasyLife": false}, "isBroker": false, "platformCode": "0", "allTags": [{"title": "立即确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。", "sortNum": 10, "colorCode": "8", "labelCode": "3564"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}], "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 512, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 6, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 14, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}], "reactId": "1804087544", "isPStoreSupportCdl": true, "isRStoreSupportCdl": true, "qualityScore": 0, "storeScore": 4.4, "isSelect": false, "orignalPriceStyle": "WithOutStrikethrough", "distance": 0.7312, "extMap": {"freeCancel": "true", "isULM": "true", "isNoOnewayFee": "false", "distance": "0.7312", "confirmRightNow": "true", "isConfirmTimeGth12": "false", "isVendorActive": "0", "isThirdInsurance": "false", "isAsiaPickup": "false", "isSupportZhima": "false"}, "ctripVehicleCode": "10871"}], "reactId": "1804087581", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14000", "vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "similarVehicleInfos": [{"vehicleCode": "10871", "vehicleName": "丰田 凯美瑞 Camry", "vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Toyota_Camry.png"}]}], "vehicleRecommendProduct": {"introduce": "同组车型最低价", "productCodes": ["9128998"]}, "minTPrice": 615}], "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/standard.png", "allowMerge": true, "dailyPrice": 581}, {"groupCode": "G05", "groupName": "SUV", "sortNum": 5, "productList": [{"vehicleCode": "10931", "sortNum": 0, "lowestPrice": 715, "highestPrice": 715, "maximumRating": 4.4, "maximumCommentCount": 5, "lowestDistance": 0.7312, "isSpecialized": false, "vendorPriceList": [{"vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "commentInfo": {"level": "不错", "commentCount": 5, "overallRating": "4.4", "maximumRating": 5, "commentLabel": "", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 715, "currentOriginalDailyPrice": 0, "currentTotalPrice": 715, "currentCurrencyCode": "CNY", "localCurrencyCode": "USD", "deductInfos": []}, "reference": {"bizVendorCode": "14000", "vendorCode": "SD0007", "pStoreCode": "LAXZILAXC18", "rStoreCode": "LAXZILAXC18", "vehicleCode": "10931", "packageId": "2379", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "F_IFAR_GROUPF-TOYOTARAV4"}, "decoratorVendorType": 0, "isEasyLife": false, "payMode": 1, "bomCode": "LAXZILAXC18_10931_CDW_FRFB_Fees_SCDW_TP_ULM_0_0", "productCode": "9131231", "rateCode": "C4", "priceVersion": "ARYAszHYvzZ09Mj+DiGyAwh8Ow6CoRJc4GRe", "pCityId": 347, "rCityId": 347, "vendorVehicleCode": "IFAR_GROUPF-TOYOTARAV4", "age": 30, "unionCardFilter": {}, "noDepositFilter": {}, "skuId": 3949683, "sippCode": "IFAR"}, "promotions": [], "isSpecialized": false, "sortNum": 0, "vendorTag": {"title": "国际知名", "sortNum": 0}, "pStoreRouteDesc": "距离小托奇欧酒店(Little Tokyo Hotel) 731.2米，步行可达", "rStoreRouteDesc": "", "easyLifeInfo": {"isEasyLife": false}, "isBroker": false, "platformCode": "0", "allTags": [{"title": "立即确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。", "sortNum": 10, "colorCode": "8", "labelCode": "3564"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}], "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 2, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 512, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 6, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 14, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}], "reactId": "1804087511", "isPStoreSupportCdl": true, "isRStoreSupportCdl": true, "qualityScore": 0, "storeScore": 4.4, "isSelect": false, "orignalPriceStyle": "WithOutStrikethrough", "distance": 0.7312, "extMap": {"freeCancel": "true", "isULM": "true", "isNoOnewayFee": "false", "distance": "0.7312", "confirmRightNow": "true", "isConfirmTimeGth12": "false", "isVendorActive": "0", "isThirdInsurance": "false", "isAsiaPickup": "false", "isSupportZhima": "false"}, "ctripVehicleCode": "10931"}], "reactId": "1804087580", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14000", "vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "similarVehicleInfos": [{"vehicleCode": "10931", "vehicleName": "丰田 RAV4", "vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Toyota_RAV4.png"}]}], "vehicleRecommendProduct": {"introduce": "同组车型最低价", "productCodes": ["9131231"]}, "minTPrice": 715}, {"vehicleCode": "10183", "sortNum": 0, "lowestPrice": 909, "highestPrice": 909, "maximumRating": 4.4, "maximumCommentCount": 5, "lowestDistance": 0.7312, "isSpecialized": false, "vendorPriceList": [{"vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "commentInfo": {"level": "不错", "commentCount": 5, "overallRating": "4.4", "maximumRating": 5, "commentLabel": "", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 909, "currentOriginalDailyPrice": 0, "currentTotalPrice": 909, "currentCurrencyCode": "CNY", "localCurrencyCode": "USD", "deductInfos": []}, "reference": {"bizVendorCode": "14000", "vendorCode": "SD0007", "pStoreCode": "LAXZILAXC18", "rStoreCode": "LAXZILAXC18", "vehicleCode": "10183", "packageId": "2379", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "S_RFAR_GROUPS-CHEVROLETTRAVERSE"}, "decoratorVendorType": 0, "isEasyLife": false, "payMode": 1, "bomCode": "LAXZILAXC18_10183_CDW_FRFB_Fees_SCDW_TP_ULM_0_0", "productCode": "9134287", "rateCode": "C4", "priceVersion": "ARYAB7JmRda9gtr5uFn/n7L4+w6CoRJc4GRe", "pCityId": 347, "rCityId": 347, "vendorVehicleCode": "RFAR_GROUPS-CHEVROLETTRAVERSE", "age": 30, "unionCardFilter": {}, "noDepositFilter": {}, "skuId": 1870089, "sippCode": "RFAR"}, "promotions": [], "isSpecialized": false, "sortNum": 0, "vendorTag": {"title": "国际知名", "sortNum": 0}, "pStoreRouteDesc": "距离小托奇欧酒店(Little Tokyo Hotel) 731.2米，步行可达", "rStoreRouteDesc": "", "easyLifeInfo": {"isEasyLife": false}, "isBroker": false, "platformCode": "0", "allTags": [{"title": "立即确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。", "sortNum": 10, "colorCode": "8", "labelCode": "3564"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}], "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 16, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 512, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 6, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 14, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}], "reactId": "1804087500", "isPStoreSupportCdl": true, "isRStoreSupportCdl": true, "qualityScore": 0, "storeScore": 4.4, "isSelect": false, "orignalPriceStyle": "WithOutStrikethrough", "distance": 0.7312, "extMap": {"freeCancel": "true", "isULM": "true", "isNoOnewayFee": "false", "distance": "0.7312", "confirmRightNow": "true", "isConfirmTimeGth12": "false", "isVendorActive": "0", "isThirdInsurance": "false", "isAsiaPickup": "false", "isSupportZhima": "false"}, "ctripVehicleCode": "10183"}], "reactId": "1804087581", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14000", "vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "similarVehicleInfos": [{"vehicleCode": "10183", "vehicleName": "雪佛兰 Traverse", "vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Chevrolet_Traverse.png"}]}], "vehicleRecommendProduct": {"introduce": "同组车型最低价", "productCodes": ["9134287"]}, "minTPrice": 909}], "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/intermediate_suv.png", "allowMerge": true, "dailyPrice": 715}, {"groupCode": "G07", "groupName": "MPV", "sortNum": 7, "productList": [{"vehicleCode": "11076", "sortNum": 0, "lowestPrice": 833, "highestPrice": 833, "maximumRating": 4.4, "maximumCommentCount": 5, "lowestDistance": 0.7312, "isSpecialized": false, "vendorPriceList": [{"vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "commentInfo": {"level": "不错", "commentCount": 5, "overallRating": "4.4", "maximumRating": 5, "commentLabel": "", "hasComment": 1}, "priceInfo": {"currentDailyPrice": 833, "currentOriginalDailyPrice": 0, "currentTotalPrice": 833, "currentCurrencyCode": "CNY", "localCurrencyCode": "USD", "deductInfos": []}, "reference": {"bizVendorCode": "14000", "vendorCode": "SD0007", "pStoreCode": "LAXZILAXC18", "rStoreCode": "LAXZILAXC18", "vehicleCode": "11076", "packageId": "2379", "packageType": 0, "vcExtendRequest": {"vendorVehicleId": "V_MVAR_GROUPV-CHRYSLERPACIFICA"}, "decoratorVendorType": 0, "isEasyLife": false, "payMode": 1, "bomCode": "LAXZILAXC18_11076_CDW_FRFB_Fees_SCDW_TP_ULM_0_0", "productCode": "9132626", "rateCode": "C4", "priceVersion": "ARYAL1Vp7KR2G0VionEXH8ZixQ6CoRJc4GRe", "pCityId": 347, "rCityId": 347, "vendorVehicleCode": "MVAR_GROUPV-CHRYSLERPACIFICA", "age": 30, "unionCardFilter": {}, "noDepositFilter": {}, "skuId": 1869832, "sippCode": "MVAR"}, "promotions": [], "isSpecialized": false, "sortNum": 0, "vendorTag": {"title": "国际知名", "sortNum": 0}, "pStoreRouteDesc": "距离小托奇欧酒店(Little Tokyo Hotel) 731.2米，步行可达", "rStoreRouteDesc": "", "easyLifeInfo": {"isEasyLife": false}, "isBroker": false, "platformCode": "0", "allTags": [{"title": "立即确认", "category": 1, "type": 1, "code": "2", "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。", "sortNum": 10, "colorCode": "8", "labelCode": "3564"}, {"title": "免费取消", "category": 1, "type": 1, "code": "2", "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款；", "sortNum": 25, "colorCode": "8", "labelCode": "3563"}, {"title": "随时可订", "category": 2, "type": 1, "code": "2", "description": "该车型随时可预订。", "sortNum": 56, "colorCode": "2", "labelCode": "3689"}, {"title": "满油取还", "category": 2, "type": 1, "code": "2", "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "sortNum": 80, "colorCode": "2", "labelCode": "3557"}, {"title": "不限里程", "category": 2, "type": 1, "code": "2", "description": "租期内没有公里数限制。", "sortNum": 85, "colorCode": "2", "labelCode": "3562"}], "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 8, "checkType": 0}, {"groupCode": "VehicleAccessory", "binaryDigit": 512, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 6, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 14, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 4, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 16, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}], "reactId": "1804087522", "isPStoreSupportCdl": true, "isRStoreSupportCdl": true, "qualityScore": 0, "storeScore": 4.4, "isSelect": false, "orignalPriceStyle": "WithOutStrikethrough", "distance": 0.7312, "extMap": {"freeCancel": "true", "isULM": "true", "isNoOnewayFee": "false", "distance": "0.7312", "confirmRightNow": "true", "isConfirmTimeGth12": "false", "isVendorActive": "0", "isThirdInsurance": "false", "isAsiaPickup": "false", "isSupportZhima": "false"}, "ctripVehicleCode": "11076"}], "reactId": "1804087580", "vendorSimilarVehicleInfos": [{"bizVendorCode": "14000", "vendorName": "<PERSON><PERSON>", "vendorLogo": "//pages.trip.com/cars/image/totrip/8305edb4-b4c7-40ff-9bc2-6fab83052879.png", "similarVehicleInfos": [{"vehicleCode": "11076", "vehicleName": "克莱斯勒 Pacifica", "vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Chrysler_Pacifica.png"}]}], "vehicleRecommendProduct": {"introduce": "同组车型最低价", "productCodes": ["9132626"]}, "minTPrice": 833}], "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/mpv.png", "allowMerge": true, "dailyPrice": 833}], "productGroupsHashCode": "fb41b3e717dd7e8dd51cdc9c34f215f0ec736d77635e301d01804c2a42c3b66f6ed3b6b0", "needRetry": false, "freePreAuthCode": "", "supportUnionLogoCode": "", "commNotices": [], "licenseInfo": {"pickupLicenseDesc": "持中国大陆驾照可在美国租车", "noticeMsg": "", "pickupSupportCDLType": 1, "pickupCountryName": "美国", "returnCountryName": "美国", "returnSupportCDLType": 1, "returnLicenseDesc": "持中国大陆驾照可在美国租车"}, "promptInfos": [], "basicData": {"sortItems": [{"title": "推荐排序", "type": 1, "code": "1", "sortNum": 1}, {"title": "价格 低→高", "type": 2, "code": "2", "sortNum": 2}], "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}}, "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "labelCodes": ["3557", "3588", "3564", "3563", "3562", "3689"], "uniqSign": "b615821a-400e-45e5-9abc-c5f41d6ea8cc", "isMoreAge": false}