{"baseResponse": {"extMap": {"IncludeFeesCost": "0.0", "checkRentCenter_2": "0.0", "mergeGroupSize_11": "1", "mergeGroup_5": "1.0", "apiCost": "2469.0", "9_getFromRedisCost": "317", "mergeGroup_9": "1.0", "mergeGroup_10": "0.0", "2_getFromRedisCost": "302", "mergeGroupSize_2": "70", "totalCostTime": "421", "gsCost": "1.0", "mergeGroupSize_3": "46", "restCost": "1", "originalCode": "200", "mergeGroupSize_4": "58", "lastInfoCost_7": "105.0", "mergeGroup_2": "2.0", "3_getFromRedisCost": "234", "mergeGroupSize_5": "34", "mergeGroup_6": "2.0", "mergeGroupSize_6": "74", "mergeGroupSize_newenergy": "80", "getPageInfoFromResponse_pre": "4", "mergeGroupSize_7": "1", "needWait": "0", "4_getFromRedisCost": "334", "requestId": "8b6e2515-75ee-4498-82b2-babcfaf56c1b", "mergeGroupSize_9": "48", "start": "2023-05-30 18:49:08", "dataConvertResCost": "329.0", "buildInfoCost_1": "0.0", "10_getFromRedisCost": "3", "buildInfoCost_2": "171.0", "11_getFromRedisCost": "6", "buildInfoCost_3": "0.0", "mergeGroup_3": "1.0", "ubtProcessCost_8": "1.0", "buildInfoCost_4": "172.0", "5_getFromRedisCost": "260", "shoppingCost_1": "2471.0", "mergeGroup_7": "0.0", "allCost": "2801.0", "newenergy_getFromRedisCost": "322", "mergeGroup_11": "0.0", "end": "2023-05-30 18:49:10", "setProductGroupsHashCodeCostAffect": "0.0", "contextBuilderCost_3": "34.0", "restOriginalCost": "2801.0", "6_getFromRedisCost": "281", "uid": "M00588838", "pickupCityId": "43", "dropoffCityId": "43", "mergeGroup_4": "1.0", "usePage": "1", "7_getFromRedisCost": "4", "responseHeadCost": "72", "initBaseData_1": "34.0", "calculatePreAuth_3": "0.0", "mergeGroup_newenergy": "10.0", "productGroupCost_6": "18.0", "runAsyncCost_2": "0.0", "sourceFrom": "ISD_C_APP", "mergeGroupSize_10": "2", "pageName": "List"}, "errorCode": "0", "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "efa09df0-3e70-482a-9c94-263058c5acee", "isSuccess": true}, "rHub": 0, "isKlbData": true, "allVendorPriceCount": 1322, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": false, "extras": {"serverRequestId": "l48980BU16FB9d9756Qt", "abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B", "isNewLicensePlate": "0", "commodityClass2Version": "0"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"shortName": "车龄", "sortNum": 1, "bitwiseType": 2, "name": "车龄", "groupCode": "CarAge", "filterItems": [{"sortNum": 1, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "5", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 2, "positionCode": "5", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 3, "positionCode": "5", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "手动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "NewEnergy", "itemCode": "NewEnergy_elect", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}, {"sortNum": 2, "groupCode": "NewEnergy", "itemCode": "NewEnergy_mix", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "新能源混动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 3, "groupCode": "VehicleAccessory", "quickSortNum": 7, "positionCode": "6", "itemCode": "VehicleAccessory_radar", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "倒车雷达"}, {"sortNum": 4, "groupCode": "VehicleAccessory", "quickSortNum": 8, "positionCode": "6", "itemCode": "VehicleAccessory_tachograph", "binaryDigit": 64, "isQuickItem": true, "bitwiseType": 1, "name": "行车记录仪"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}, {"sortNum": 7, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Refrigerator", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 1, "name": "车载冰箱"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "HotBrand", "itemCode": "HotBrand_奥迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_别克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_奔驰", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_宝马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotB<PERSON>_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 6, "groupCode": "HotBrand", "itemCode": "HotBrand_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 10, "groupCode": "HotBrand", "itemCode": "HotBrand_Jeep", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 12, "groupCode": "HotBrand", "itemCode": "HotBrand_路虎", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 18, "groupCode": "HotBrand", "itemCode": "HotBrand_日产", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_奥迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_阿斯顿·马丁", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "阿斯顿·马丁", "icon": "https://dimg04.c-ctrip.com/images/0R419120009gwx9cqBA77.png"}], "groupCode": "BrandGroup_a0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_BEIJING汽车", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "BEIJING汽车", "icon": "https://dimg04.c-ctrip.com/images/0R46u120009gwv7gj857B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_保时捷", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "保时捷", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_别克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔腾", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "奔腾", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benteng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔驰", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝马", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝骏", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "宝骏", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baojun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宾利", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "宾利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/binli.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_巴博斯", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "巴博斯", "icon": "//dimg04.c-ctrip.com/images/0AS47120008bmtwth44BA.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_本田", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_比亚迪", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "比亚迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "长安", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安欧尚", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "长安欧尚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changanoushang.png"}], "groupCode": "BrandGroup_c0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风风光", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "东风风光", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengguang.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风风行", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "东风风行", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengxin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_道奇", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "道奇", "icon": "//pages.c-ctrip.com/carisd/brandlogo/daoqi.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_法拉利", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "法拉利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_福特", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "福特", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}], "groupCode": "BrandGroup_f0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽传祺", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "广汽传祺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽埃安", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "广汽埃安", "icon": "https://dimg04.c-ctrip.com/images/0R44f120009gwvd13842B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽新能源", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "广汽新能源", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqixingnengyuan.png"}], "groupCode": "BrandGroup_g0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_华晨新日", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "华晨新日", "icon": "https://dimg04.c-ctrip.com/images/0R472120009gwv9rxB7E1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_华颂", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "华颂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/huasong.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_海马", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "海马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_红旗", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "红旗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}], "groupCode": "BrandGroup_h0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_Jeep", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_几何汽车", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "几何汽车", "icon": "https://dimg04.c-ctrip.com/images/0R43t120009gwv73p44FD.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_吉利汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "吉利汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷豹", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "捷豹", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiebao.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷达", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "捷达", "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_极氪", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "极氪", "icon": "https://dimg04.c-ctrip.com/images/0yc0x12000aom51we094D.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_k0", "itemCode": "BrandGroup_k0_凯迪拉克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "凯迪拉克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "groupCode": "BrandGroup_k0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_LEVC", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "LEVC", "icon": "//pages.c-ctrip.com/carisd/brandlogo/levc.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_兰博基尼", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "兰博基尼", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_劳斯莱斯", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "劳斯莱斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_岚图汽车", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "岚图汽车", "icon": "https://dimg04.c-ctrip.com/images/0R45a120009gwvmb34A33.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_林肯", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "林肯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/linken.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_理想汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "理想汽车", "icon": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc6i1200000ieok0AE70.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_路虎", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_零跑汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "零跑汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingpaoqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_雷克萨斯", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "雷克萨斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}], "groupCode": "BrandGroup_l0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_MINI", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "MINI", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_名爵", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "名爵", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mingjue.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_玛莎拉蒂", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "玛莎拉蒂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_迈凯伦", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "迈凯伦", "icon": "//pages.c-ctrip.com/carisd/brandlogo/maikailun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_马自达", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "马自达", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mazida.png"}], "groupCode": "BrandGroup_m0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_o0", "itemCode": "BrandGroup_o0_欧拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "欧拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/oula.png"}], "groupCode": "BrandGroup_o0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_p0", "itemCode": "BrandGroup_p0_Polestar极星", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "Polestar极星", "icon": "https://dimg04.c-ctrip.com/images/0R43g120009gwuo9r0192.png"}], "groupCode": "BrandGroup_p0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_前途", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "前途", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiantu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_奇瑞", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "奇瑞", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qirui.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_起亚", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "起亚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "groupCode": "BrandGroup_q0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_日产", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_荣威", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "荣威", "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "groupCode": "BrandGroup_r0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_smart", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "smart", "icon": "//pages.c-ctrip.com/carisd/brandlogo/smart.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_上汽大通MAXUS", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "上汽大通MAXUS", "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}], "groupCode": "BrandGroup_s0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_特斯拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "特斯拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_腾势", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "腾势", "icon": "//pages.c-ctrip.com/carisd/brandlogo/shiteng.png"}], "groupCode": "BrandGroup_t0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_五菱汽车", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "五菱汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/wulingqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_威马汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "威马汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weimaqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_沃尔沃", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "沃尔沃", "icon": "//pages.c-ctrip.com/carisd/brandlogo/woerwo.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_蔚来", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "蔚来", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weilai.png"}], "groupCode": "BrandGroup_w0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小鹏", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "小鹏", "icon": "https://dimg04.c-ctrip.com/images/0yc5812000aom40h4060E.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小鹏汽车", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "小鹏汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiaopengqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_现代", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "现代", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪佛兰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "雪佛兰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_依维柯", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "依维柯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yikewei.png"}, {"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_英菲尼迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "英菲尼迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yingfeinidi.png"}], "groupCode": "BrandGroup_y0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_SONGSAN MOTORS", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "SONGSAN MOTORS", "icon": ""}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_飞凡汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "飞凡汽车", "icon": ""}], "groupCode": "BrandGroup_z0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "0-50", "sortNum": 1, "name": "¥50以下", "groupCode": "Price", "itemCode": "Price_0-50"}, {"code": "50-100", "sortNum": 2, "name": "¥50-100", "groupCode": "Price", "itemCode": "Price_50-100"}, {"code": "100-200", "sortNum": 3, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-400", "sortNum": 4, "name": "¥200-400", "groupCode": "Price", "itemCode": "Price_200-400"}, {"code": "400-99999", "sortNum": 5, "name": "¥400以上", "groupCode": "Price", "itemCode": "Price_400-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 3, "groupCode": "PickReturn", "itemCode": "PickReturn_FreeShuttle", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "免费接至门店取车"}, {"sortNum": 4, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupSelf", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "自行到店取车"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "StoreService", "quickSortNum": 4, "positionCode": "3", "itemCode": "StoreService_easyLife", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 1, "name": "无忧租"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 6, "groupCode": "StoreService", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "selectedIcon": "https://dimg04.c-ctrip.com/images/0417112000aamiv5bA3CD.png", "groupCode": "Promotion", "itemCode": "Promotion_3662", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "", "icon": "https://dimg04.c-ctrip.com/images/0417112000aamiv5bA3CD.png"}, {"sortNum": 4, "groupCode": "Promotion", "itemCode": "Promotion_3749", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "首租优惠"}], "groupCode": "Promotion", "shortName": "优惠活动", "bitwiseType": 2, "name": "优惠活动"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_8", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "台胞证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "驾驶员驾龄", "sortNum": 6, "bitwiseType": 2, "name": "驾驶员驾龄", "groupCode": "DriveAge", "filterItems": [{"sortNum": 1, "groupCode": "DriveAge", "itemCode": "DriveAge_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "不满6个月"}, {"sortNum": 2, "groupCode": "DriveAge", "itemCode": "DriveAge_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "满6个月"}, {"sortNum": 3, "groupCode": "DriveAge", "itemCode": "DriveAge_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "满一年"}]}, {"shortName": "门店评分", "sortNum": 7, "bitwiseType": 2, "name": "门店评分", "groupCode": "Comment", "filterItems": [{"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "9", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}]}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "18", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "携程优选", "icon": ""}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30004", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "铭轩酒店租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30027", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "玛雅租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30055", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "准典出行"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30147", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "calabi-骑仕租车A"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30150", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "小木鱼租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30164", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "懒人行卡拉比"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30169", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "峰硕租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30182", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "加加租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30196", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "太平洋租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30234", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "金晟租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30248", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "驰敖天天租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30284", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "旭升租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30777", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "椰林情租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31025", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "乐天租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31092", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "树德租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31218", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "大权租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31239", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "佳途租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32231", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "泰信吉租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32498", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "么么达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32538", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "海越租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32687", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "行者天下租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32845", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "美凯租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_33419", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "欣岳美行租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_38235", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "美行尚盈租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_47522", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "祥驰租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_52811", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "蔚蓝租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_53893", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "世纪联合租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_57671", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "捷安利达租车"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_58487", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田海南出行"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61365", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "普信租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61372", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "程硕租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61659", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "旺亚租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61671", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "凤翔天涯租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61816", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "永卓租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61827", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "吉海畅行租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61831", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "金达莱租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61937", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "锋达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61951", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "鑫旺达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61966", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "宝驰租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62053", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "联谊租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62072", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "一路平安租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62099", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "宏驰智行租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62104", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "金森租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62107", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "启捷租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62115", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "启航租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62119", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "三鹤租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62166", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "小红帽租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62305", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "皖太租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62408", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "宏广东盈租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62863", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63457", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "翊霏租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63460", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "小米租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63836", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "租租侠租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63857", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "信华租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_64429", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "十八度阳光租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_64662", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "潆莹租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_65413", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "哈尔滨奥朗租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_65451", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "荣树租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_65452", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "新易达租车"}], "groupCode": "Vendor_1", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_65454", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "信和华租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_66324", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "环岛租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_66614", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "你我他租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_66708", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "旭辰租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_67661", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "乐途租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_67709", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "神风租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_68692", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "利资租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_69279", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "安米租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_69280", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "百募租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_69287", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "华鑫海租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_70693", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "车旺达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_70695", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "龙之祥租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_70697", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "铭车邦租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_70698", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "铭途租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_71515", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "中进通达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_72621", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "细杰租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_72983", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "吉驰租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_73265", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "车之美租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_73619", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "锦程租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_73843", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "飞浩租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_73871", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "多浦达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_74365", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "爱尚出行租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_74373", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "中进租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_74569", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "河北唐亚租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_74573", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "器车出行"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_74629", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "通源租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_76105", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "全季租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_76661", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "振亚租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_76665", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "彩车坊租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_76903", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "如亚租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_77081", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "常晟租车"}], "groupCode": "Vendor_2", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_77147", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "美点租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_77151", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "金晟利租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_77287", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "老马出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_77523", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "博汇租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_78577", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "夏末微凉租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_78579", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "优享旅途租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_78963", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "轩琪租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79461", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "海南麻豆智行租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79485", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "立强租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79695", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "炜晨租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79723", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "盛豪会租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_79797", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "麒麟火租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80115", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "鼎航租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80127", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "新概念租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80145", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "鑫路达租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80147", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "浩宇租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80427", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "名都租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80431", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "小飞侠租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80545", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "盛兴隆租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80559", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "星锐租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80771", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "豫海租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80775", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "车先生租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_80977", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "星月租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_81003", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "琼州租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_81479", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "商旅出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_81525", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "钰鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_81527", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "业扬租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_81529", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "果岭出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_81675", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "广源租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_81687", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "易达通租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_81827", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "京海租车"}], "groupCode": "Vendor_3", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81829", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "诚航商旅"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81831", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "潮人商旅"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81887", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "热火租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81889", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "八骏马租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81919", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "福斯特租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_81931", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "博利租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82105", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "旅途中租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82163", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "启瑞盛租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82231", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "云超租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82263", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "聚通达租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82301", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "梵云租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82305", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "畅行无忧租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82393", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "海鸭鸭租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82671", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82731", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82739", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82819", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82843", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_82867", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": ""}], "groupCode": "Vendor_4", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "15j8k6G7foX55726T33y", "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 41, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 81, "detail": [{"code": "1001", "amount": 116, "amountDesc": "¥116", "name": "租车费"}, {"code": "11037", "amount": 35, "amountDesc": "¥35", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥41", "originalDailyPrice": 58, "subAmount": 41, "name": "车辆租金", "amountStr": "¥81"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 90, "amountStr": "¥90", "detail": [{"code": "1002", "amount": 90, "amountDesc": "¥90", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 191, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥226", "subAmount": 226, "name": "总价", "amountStr": "¥191"}], "reference": {"vehicleCode": "0", "rStoreCode": "107242", "pLev": 24129, "comPriceCode": "[c]Mjc5fDE4NjJ8MjAyMC4wMC0xNCAzLTA2MDowMDAwOjAmMSQyJjU4JjA2LTEwMjMtOjAwOjUgMDA4JiYxMDAmNTAxJjIkfDEwMTE2JCY1OCYmMSYyMTAwMyYyMC4wLjAwMDAyJjAwJDEuMDAmMiY0NTAkfDI5MC4wMDUtMzAyMy06NDk6MCAxOAAAAAAwOQAA", "bizVendorCode": "SD57671", "pStoreCode": "107242", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3MjQyXzUxMDRfMV81OF8xMTZfNThfMjI2LjAwXzQxLjBfMTkxLjBfMF8wXzAuMF8wLjBfOTAuMDBfMjAuMDBfMC4wMF8wLjAwXzE4NjIyNzk=", "sendTypeForPickUpCar": 0, "skuId": 1862279, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24129, "vendorCode": "57671", "vendorVehicleCode": "20070049"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1858316, "bizVendorCode": "SD30147"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减35", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5104", "highestPrice": 91, "rCoup": 0, "minDPrice": 41, "pWay": "可选/店员免费上门送取车", "hot": 0, "minTPrice": 191, "lowestDistance": 0, "group": 0, "sortNum": 0, "maximumRating": 3.9, "vehicleRecommendProduct": {"productCodes": ["SD57671_0_107242_107242"], "introduce": "当前车型最低价"}, "minDOrinPrice": 58, "isEasy": true, "isCredit": true, "maximumCommentCount": 14, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 2, "logicIndex": 0, "isGroup": false, "renderUniqId": "5104_2__191_41_58_免押金_easyLife_券已减35"}, {"groupSort": 0, "lowestPrice": 41, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 81, "detail": [{"code": "1001", "amount": 116, "amountDesc": "¥116", "name": "租车费"}, {"code": "11037", "amount": 35, "amountDesc": "¥35", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥41", "originalDailyPrice": 58, "subAmount": 41, "name": "车辆租金", "amountStr": "¥81"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 90, "amountStr": "¥90", "detail": [{"code": "1002", "amount": 90, "amountDesc": "¥90", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 191, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥226", "subAmount": 226, "name": "总价", "amountStr": "¥191"}], "reference": {"vehicleCode": "0", "rStoreCode": "107242", "pLev": 24129, "comPriceCode": "[c]", "bizVendorCode": "SD57671", "pStoreCode": "107242", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTA3MjQyXzU1NzRfMV81OF8xMTZfNThfMjI2LjAwXzQxLjBfMTkxLjBfMF8wXzAuMF8wLjBfOTAuMDBfMjAuMDBfMC4wMF8wLjAwXzE5MjU3ODQ=", "sendTypeForPickUpCar": 0, "skuId": 1925784, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24129, "vendorCode": "57671", "vendorVehicleCode": "20547"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1912881, "bizVendorCode": "SD58487"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2375988, "bizVendorCode": "SD82263"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2070657, "bizVendorCode": "SD66324"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1960688, "bizVendorCode": "SD33419"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1979601, "bizVendorCode": "SD30196"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减35", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5574", "highestPrice": 350, "rCoup": 0, "minDPrice": 41, "pWay": "可选/店员免费上门送取车", "hot": 0, "minTPrice": 191, "lowestDistance": 2.5207, "group": 0, "sortNum": 1, "maximumRating": 4, "vehicleRecommendProduct": {"productCodes": ["SD57671_0_107242_107242"], "introduce": "当前车型最低价"}, "minDOrinPrice": 58, "isEasy": true, "isCredit": true, "maximumCommentCount": 1, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 6, "logicIndex": 1, "isGroup": false, "renderUniqId": "5574_6__191_41_58_免押金_easyLife_券已减35"}, {"groupSort": 0, "lowestPrice": 42, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 84, "detail": [{"code": "1001", "amount": 120, "amountDesc": "¥120", "name": "租车费"}, {"code": "11037", "amount": 36, "amountDesc": "¥36", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥42", "originalDailyPrice": 60, "subAmount": 42, "name": "车辆租金", "amountStr": "¥84"}, {"code": "CAR_SERVICE_FEE", "amount": 30, "amountStr": "¥30", "detail": [{"code": "1003", "amount": 30, "amountDesc": "¥30", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 214, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥250", "subAmount": 250, "name": "总价", "amountStr": "¥214"}], "reference": {"vehicleCode": "0", "rStoreCode": "116987", "pLev": 61575, "comPriceCode": "[c]MTkxfDI0MTMzLTA2fDIwMjAwOjAtMTQgJjYwJjA6MDAwMjMtJjEkMjUgMDAwNi0xMDAmNjowMDokfDEwMCYmMSY2MCYwMSYyMTAwMzEyMCQwLjAwJjEmMzAwJDEmMzAuMiY1MDAwMiYxMDAuLjAwJjIwMjMwMCR8MzAgMS0wNS06MDkAODo0OQ==", "bizVendorCode": "SD80775", "pStoreCode": "116987", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTE2OTg3XzU1OTdfMV82MF8xMjBfNjBfMjUwLjAwXzQyLjBfMjE0LjBfMF8wXzAuMF8wLjBfMTAwLjAwXzMwLjAwXzAuMDBfMC4wMF8yNDEzMTkx", "sendTypeForPickUpCar": 0, "skuId": 2413191, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 61575, "vendorCode": "80775", "vendorVehicleCode": "20103487"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2677426, "bizVendorCode": "SD62408"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860097, "bizVendorCode": "SD31092"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1863897, "bizVendorCode": "SD30004"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2428339, "bizVendorCode": "SD30234"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1921691, "bizVendorCode": "SD79723"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1910574, "bizVendorCode": "SD58487"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2413064, "bizVendorCode": "SD61365"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1920321, "bizVendorCode": "SD67709"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2302873, "bizVendorCode": "SD74365"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1772352, "bizVendorCode": "SD30055"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1974271, "bizVendorCode": "SD82105"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1862480, "bizVendorCode": "SD79695"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1971297, "bizVendorCode": "SD80127"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1978099, "bizVendorCode": "SD33419"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2704627, "bizVendorCode": "SD82731"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1861182, "bizVendorCode": "SD30182"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减36", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5407", "highestPrice": 420, "rCoup": 0, "minDPrice": 42, "pWay": "可选/店员免费上门送取车", "hot": 0, "minTPrice": 214, "lowestDistance": 0.1, "group": 0, "sortNum": 2, "maximumRating": 4, "vehicleRecommendProduct": {"productCodes": ["SD80775_0_116987_116987"], "introduce": "当前车型最低价"}, "minDOrinPrice": 60, "isEasy": true, "isCredit": true, "maximumCommentCount": 1, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 17, "logicIndex": 2, "isGroup": false, "renderUniqId": "5407_17__214_42_60_免押金_easyLife_券已减36"}, {"groupSort": 0, "lowestPrice": 30, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 60, "detail": [{"code": "1001", "amount": 90, "amountDesc": "¥90", "name": "租车费"}, {"code": "11037", "amount": 30, "amountDesc": "¥30", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥30", "originalDailyPrice": 45, "subAmount": 30, "name": "车辆租金", "amountStr": "¥60"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "amount": 120, "amountDesc": "¥120", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 215, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥245", "subAmount": 245, "name": "总价", "amountStr": "¥215"}], "reference": {"vehicleCode": "0", "rStoreCode": "116954", "pLev": 60298, "comPriceCode": "[c]MDUxfDI3MDUzLTA2fDIwMjAwOjAtMTQgJjQ1JjA6MDAwMjMtJjEkMjUgMDAwNi0xMDAmNDowMDokfDEwNSYmMSY0NSYwMSYyMDAzJjkwJDEuMDAmMSYzNTAkMTAzNS4wJjYwLjAyJjIyMC4wMDAmMTAyMy0wJHwyMCAxODA1LTMwOQAAOjQ5Og==", "bizVendorCode": "SD77147", "pStoreCode": "116954", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTE2OTU0XzQxMzlfMV80NV85MF80NV8yNDUuMDBfMzAuMF8yMTUuMF8wXzBfMC4wXzAuMF8xMjAuMDBfMzUuMDBfMC4wMF8wLjAwXzI3MDUwNTE=", "sendTypeForPickUpCar": 0, "skuId": 2705051, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 60298, "vendorCode": "77147", "vendorVehicleCode": "20076929"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1911940, "bizVendorCode": "SD61831"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860657, "bizVendorCode": "SD31092"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1861254, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1915995, "bizVendorCode": "SD77287"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1854917, "bizVendorCode": "SD30147"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1924514, "bizVendorCode": "SD79723"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1911622, "bizVendorCode": "SD74573"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2704960, "bizVendorCode": "SD33419"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2705001, "bizVendorCode": "SD81827"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1912422, "bizVendorCode": "44444"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1863247, "bizVendorCode": "SD76105"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1916013, "bizVendorCode": "SD77081"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1913476, "bizVendorCode": "SD80431"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减30", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "4139", "highestPrice": 258, "rCoup": 0, "minDPrice": 30, "pWay": "可选/店员免费上门送取车", "hot": 0, "minTPrice": 215, "lowestDistance": 0, "group": 0, "sortNum": 3, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD77147_0_116954_116954"], "introduce": "当前车型最低价"}, "minDOrinPrice": 45, "isEasy": true, "isCredit": true, "maximumCommentCount": 14, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 14, "logicIndex": 3, "isGroup": false, "renderUniqId": "4139_14__215_30_45_免押金_easyLife_券已减30"}, {"groupSort": 0, "lowestPrice": 62, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 123, "detail": [{"code": "1001", "amount": 176, "amountDesc": "¥176", "name": "租车费"}, {"code": "11037", "amount": 53, "amountDesc": "¥53", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥62", "originalDailyPrice": 88, "subAmount": 62, "name": "车辆租金", "amountStr": "¥123"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 238, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥291", "subAmount": 291, "name": "总价", "amountStr": "¥238"}], "reference": {"vehicleCode": "0", "rStoreCode": "116523", "pLev": 67912, "comPriceCode": "[c]ODE0fDIyMzV8MjAyMC4wMC0xNCAzLTA2MDowMDAwOjAmMSQyJjg4JjA2LTEwMjMtOjAwOjUgMDA4JiYxMDAmODAxJjIkfDEwMTc2JCY4OCYmMSYzMTAwMyYzNS41LjAwMDAyJjAwJDEuMDAmMiY0MDAkfDI4MC4wMDUtMzAyMy06NDk6MCAxOAAAAAAwOQAA", "bizVendorCode": "SD74373", "pStoreCode": "116523", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTE2NTIzXzQ0OTNfMV84OF8xNzZfODhfMjkxLjAwXzYyLjBfMjM4LjBfMF8wXzAuMF8wLjBfODAuMDBfMzUuMDBfMC4wMF8wLjAwXzIyMzU4MTQ=", "sendTypeForPickUpCar": 0, "skuId": 2235814, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 67912, "vendorCode": "74373", "vendorVehicleCode": "20105053"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860714, "bizVendorCode": "SD31092"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1921283, "bizVendorCode": "SD79723"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1772301, "bizVendorCode": "SD30055"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1914999, "bizVendorCode": "44444"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1862623, "bizVendorCode": "SD76105"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1865402, "bizVendorCode": "SD30147"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1861157, "bizVendorCode": "SD32231"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减53", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "1069", "highestPrice": 209, "rCoup": 0, "minDPrice": 62, "pWay": "可选/店员免费上门送取车", "hot": 0, "minTPrice": 238, "lowestDistance": 0, "group": 0, "sortNum": 4, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD74373_0_116523_116523"], "introduce": "当前车型最低价"}, "minDOrinPrice": 88, "isEasy": true, "isCredit": true, "maximumCommentCount": 14, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 8, "logicIndex": 4, "isGroup": false, "renderUniqId": "1069_8__238_62_88_免押金_easyLife_券已减53"}, {"groupSort": 0, "lowestPrice": 48, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 165, "detail": [{"code": "1001", "amount": 236, "amountDesc": "¥236", "name": "租车费"}, {"code": "11037", "amount": 71, "amountDesc": "¥71", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥83", "originalDailyPrice": 118, "subAmount": 83, "name": "车辆租金", "amountStr": "¥165"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 245, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥316", "subAmount": 316, "name": "总价", "amountStr": "¥245"}], "reference": {"vehicleCode": "0", "rStoreCode": "107528", "pLev": 22800, "comPriceCode": "[c]NDcxfDE4NTV8MjAyMC4wMC0xNCAzLTA2MDowMDAwOjAmJjEkJjExOC0wNi0yMDIzMDowMDE1IDAxMTgmOjAwJjEwMDEmMSR8MTgmMiYyJjEwMDMmMzYkMS4wMCYxJjIwMCQxMDIwLjAmMzAuMDImMjAuMDAwMCY2MjMtMCR8MjAgMTg6NS0zMDkAAAA0OTow", "bizVendorCode": "SD31092", "pStoreCode": "107528", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3NTI4XzQxMDFfMV8xMThfMjM2XzExOF8zMTYuMDBfODMuMF8yNDUuMF8wXzBfMC4wXzAuMF82MC4wMF8yMC4wMF8wLjAwXzAuMDBfMTg1NTQ3MQ==", "sendTypeForPickUpCar": 0, "skuId": 1855471, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22800, "vendorCode": "31092", "vendorVehicleCode": "21598"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1968672, "bizVendorCode": "SD79485"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1965139, "bizVendorCode": "SD33419"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1861255, "bizVendorCode": "SD81675"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1861534, "bizVendorCode": "SD30147"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1865421, "bizVendorCode": "SD76105"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1923994, "bizVendorCode": "SD63857"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1918561, "bizVendorCode": "SD74573"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1965528, "bizVendorCode": "SD30196"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减71", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "4101", "highestPrice": 280, "rCoup": 0, "minDPrice": 83, "pWay": "可选/店员免费上门送取车", "hot": 0, "minTPrice": 245, "lowestDistance": 2.5207, "group": 0, "sortNum": 5, "maximumRating": 3.9, "vehicleRecommendProduct": {"productCodes": ["SD79485_0_116918_116918"], "introduce": "当前车型最低价"}, "minDOrinPrice": 118, "isEasy": true, "isCredit": true, "maximumCommentCount": 14, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 9, "logicIndex": 5, "isGroup": false, "renderUniqId": "4101_9__245_83_118_免押金_easyLife_券已减71"}], "groupCode": "all", "dailyPrice": 41, "hasResult": true}, {"sortNum": 0, "groupName": "经济轿车", "hasResult": true, "groupCode": "2", "dailyPrice": 41}, {"sortNum": 2, "groupName": "新能源", "hasResult": true, "groupCode": "newenergy", "dailyPrice": 55}, {"sortNum": 3, "groupName": "舒适轿车", "hasResult": true, "groupCode": "3", "dailyPrice": 74}, {"sortNum": 4, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 105}, {"sortNum": 5, "groupName": "商务车", "hasResult": true, "groupCode": "4", "dailyPrice": 102}, {"sortNum": 6, "groupName": "豪华轿车", "hasResult": true, "groupCode": "5", "dailyPrice": 128}, {"sortNum": 7, "groupName": "跑车", "hasResult": true, "groupCode": "9", "dailyPrice": 202}, {"sortNum": 8, "groupName": "房车", "hasResult": true, "groupCode": "11", "dailyPrice": 545}, {"sortNum": 9, "groupName": "小巴士", "hasResult": true, "groupCode": "7", "dailyPrice": 342}, {"sortNum": 10, "groupName": "皮卡", "hasResult": true, "groupCode": "10", "dailyPrice": 622}], "labelCodes": ["3662", "3563", "3510", "3696", "3697", "3698", "3731", "3679", "3650", "3495", "3494", "3504", "3548", "3705", "3749", "3827", "3547", "3503", "3502", "3501", "3709", "3509", "3788", "3789", "3746", "3769"], "quickFilter": [{"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 1, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "5", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 1, "groupCode": "SelfService", "quickSortNum": 1, "positionCode": "3", "itemCode": "SelfService_Support", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "9", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "18", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "携程优选", "icon": ""}], "promotMap": {"149827816": "TglQPrz6ZCKxquiZOJ37ow==", "783151896": "fO1JR51VkCbs7NQf+3/COQ=="}, "requestInfo": {"rLongitude": 109.41201, "rDate": "20230616180000", "age": 30, "returnDate": "/Date(1686909600000+0800)/", "sourceCountryId": 1, "pLatitude": 18.30747, "rLatitude": 18.30747, "pLongitude": 109.41201, "pDate": "20230614180000", "pickupLocationName": "凤凰国际机场T1航站楼", "returnLocationName": "凤凰国际机场T1航站楼", "pickupDate": "/Date(1686736800000+0800)/"}, "allVehicleCount": 334, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"filterCode": "Vendor_0"}, "vehicleList": [{"transmissionName": "自动挡", "displacement": "1.5L", "style": "", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "5104", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "捷达VA3", "zhName": "捷达VA3", "brandName": "捷达", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "三厢车", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "2", "endurance": "工信部续航100km", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "捷达", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false, "vehiclesSetId": "64"}, {"transmissionName": "自动挡", "displacement": "1.5L", "style": "", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "5574", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "丰田YARiS L 致享", "zhName": "丰田YARiS L 致享", "brandName": "丰田", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "三厢车", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "2", "endurance": "工信部续航100km", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "丰田", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false, "vehiclesSetId": "64"}, {"transmissionName": "自动挡", "displacement": "1.2T-1.5L", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "carPhone": true, "vehicleCode": "5407", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": true, "name": "丰田 卡罗拉", "zhName": "丰田 卡罗拉", "brandName": "丰田", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "三厢车", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "2", "endurance": "工信部续航100km", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "丰田", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false, "vehiclesSetId": "64"}, {"luggageNo": 2, "displacement": "1.0T-1.5L", "autoPark": false, "endurance": "工信部续航100km", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "雪佛兰科沃兹", "doorNo": 4, "driveMode": "前置前驱", "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "carPhone": true, "vehicleCode": "4139", "name": "雪佛兰科沃兹", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": false, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "transmissionType": 1, "brandName": "雪佛兰", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "groupSubClassCode": "", "brandEName": "雪佛兰", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "64", "transmissionName": "自动挡"}, {"transmissionName": "自动挡", "displacement": "1.4T-1.6L", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "vehicleCode": "1069", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "大众朗逸", "zhName": "大众朗逸", "brandName": "大众", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "三厢车", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "2", "endurance": "工信部续航100km", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "大众", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false, "vehiclesSetId": "64"}, {"transmissionName": "自动挡", "displacement": "1.0T-1.5L", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "vehiclesSetId": "64", "vehicleCode": "4101", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "别克英朗", "zhName": "别克英朗", "brandName": "别克", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "三厢车", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "2", "endurance": "工信部续航100km", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "别克", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false, "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4915"}], "storeList": [{"pickOffLevel": 61575, "storeCode": "116987", "pickUpLevel": 61575}, {"pickOffLevel": 60298, "storeCode": "116954", "pickUpLevel": 60298}, {"pickOffLevel": 24129, "storeCode": "107242", "pickUpLevel": 24129}, {"pickOffLevel": 22800, "storeCode": "107528", "pickUpLevel": 22800}, {"pickOffLevel": 67912, "storeCode": "116523", "pickUpLevel": 67912}], "promptInfos": [{"button": {"icon": "https://dimg.fws.qa.nt.ctripcorp.com/images/0300a1200000dnqh32FD1.png"}, "backGroundUrl": "https://dimg.fws.qa.nt.ctripcorp.com/images/0306d1200000dnjea0D12.png", "tangChoiceTypes": "[2]", "jumpUrl": "/rn_car_main/_crn_config?CRNModuleName=rn_car_main&CRNType=1&initialPage=Member&apptype=ISD_C_APP", "locations": [{"groupCode": "all", "index": 1}], "contents": [{"contentStyle": "1", "stringObjs": [{"content": "黄金贵宾", "style": "0"}, {"url": "https://dimg.fws.qa.nt.ctripcorp.com/images/0300h1200000dnq527E8C.png"}]}, {"stringObjs": [{"content": "专享租车费95折", "style": "0"}]}], "type": 11, "icon": "https://dimg.fws.qa.nt.ctripcorp.com/images/0300z1200000dnjdo675F.png", "textColor": {"r": 174, "b": 50, "g": 128, "a": 1}}, {"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": false, "isLastPage": false, "uniqSign": "12001145310000201410LcS1E4513H95VL485554", "pHub": 0, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"sortNum": 1, "showLayer": 1, "title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "subTitle": "*国定节假日以页面披露为准"}, {"sortNum": 3, "title": "优质车况", "subTitle": "", "type": 1, "description": "3年内车龄", "showLayer": 0}, {"sortNum": 10, "title": "满油取车", "subTitle": "", "type": 0, "description": "保障取车时油量满", "showLayer": 0}, {"sortNum": 99, "title": "车辆守护升级", "subTitle": "*覆盖损失范围以预订页面内披露为准", "type": 0, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧"}]}, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "5307154494534289123", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a80398f-468179-198", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1685444413902+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": true, "isCacheValid": true, "networkCost": 0, "environmentCost": 1, "cacheFetchCost": 0, "fetchCost": 516, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1685444413292, "afterFetch": 1685444413808, "hasRetry": false, "originNetworkCost": 516}}