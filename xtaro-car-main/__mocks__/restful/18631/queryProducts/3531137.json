{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "5e0b6750-5bde-44e6-ae01-f7f15039a6f2", "cost": 453, "extMap": {}, "extraIndexTags": {"rCityId": "43", "pcName": "中国", "rcId": "1", "pcId": "1", "pCityId": "43", "pCityName": "三亚", "rcName": "中国", "rCityName": "三亚"}, "apiResCodes": [], "hasResult": true, "errorCode": "0", "message": ""}, "ResponseStatus": {"Timestamp": "/Date(1718706949224+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "2988213170524080100"}, {"Id": "RootMessageId", "Value": "921822-0a80a345-477418-73928"}]}, "requestInfo": {"pickupDate": "/Date(1719367200000+0800)/", "pickupLocationName": "凤凰机场站", "returnDate": "/Date(1719453600000+0800)/", "returnLocationName": "凤凰机场站", "sourceCountryId": 1, "age": 30, "pLatitude": 18.308986, "rLatitude": 18.308986, "rLongitude": 109.408787, "pLongitude": 109.408787, "pDate": "20240626100000", "rDate": "20240627100000", "pCityId": 43, "rCityId": 43}, "allVehicleCount": 177, "allVendorPriceCount": 311, "filterMenuItems": [{"name": "快速选车", "code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"name": "车龄", "sortNum": 1, "groupCode": "CarAge", "bitwiseType": 2, "filterItems": [{"itemCode": "CarAge_3510", "name": "一年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "5"}, {"itemCode": "CarAge_3547", "name": "两年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 3, "positionCode": "5"}], "shortName": "车龄", "isSupportMulti": true}, {"name": "座位数", "sortNum": 2, "groupCode": "SeatGroup", "bitwiseType": 2, "filterItems": [{"itemCode": "SeatGroup_1", "name": "2座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "SeatGroup_2", "name": "4座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "SeatGroup_3", "name": "5座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "SeatGroup_4", "name": "6座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}, {"itemCode": "SeatGroup_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 5, "isQuickItem": true, "quickSortNum": 1, "positionCode": "10"}], "shortName": "座位", "isSupportMulti": true}, {"name": "车辆排挡", "sortNum": 3, "groupCode": "Transmission", "bitwiseType": 2, "filterItems": [{"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "7"}, {"itemCode": "Transmission_2", "name": "手动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}], "shortName": "排挡"}, {"name": "能源类型", "sortNum": 4, "groupCode": "NewEnergy", "bitwiseType": 2, "filterItems": [{"itemCode": "NewEnergy_elect", "name": "纯电动", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "NewEnergy_mix", "name": "新能源混动", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "NewEnergy_gas", "name": "汽油", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}], "shortName": "能源类型", "isSupportMulti": true}, {"name": "车辆配置", "sortNum": 5, "groupCode": "VehicleAccessory", "bitwiseType": 1, "filterItems": [{"itemCode": "VehicleAccessory_ReversingImage", "name": "倒车影像", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 2, "isQuickItem": true, "quickSortNum": 6, "positionCode": "6"}, {"itemCode": "VehicleAccessory_radar", "name": "倒车雷达", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 32, "sortNum": 3, "isQuickItem": true, "quickSortNum": 7, "positionCode": "6"}, {"itemCode": "VehicleAccessory_tachograph", "name": "行车记录仪", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 64, "sortNum": 4, "isQuickItem": true, "quickSortNum": 8, "positionCode": "6"}, {"itemCode": "VehicleAccessory_MobileHolder", "name": "手机支架", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 128, "sortNum": 5, "isQuickItem": false}, {"itemCode": "VehicleAccessory_LeatherSeat", "name": "真皮座椅", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 256, "sortNum": 6, "isQuickItem": false}, {"itemCode": "VehicleAccessory_Refrigerator", "name": "车载冰箱", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 512, "sortNum": 7, "isQuickItem": false}, {"itemCode": "VehicleAccessory_Childseat", "name": "儿童座椅", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 2048, "sortNum": 9, "isQuickItem": false}], "shortName": "车辆配置", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 6, "groupCode": "HotBrand", "bitwiseType": 2, "filterItems": [{"itemCode": "HotBrand_奥迪", "name": "奥迪", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"itemCode": "HotBrand_别克", "name": "别克", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "HotBrand_奔驰", "name": "奔驰", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "HotBrand_宝马", "name": "宝马", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "HotB<PERSON>_本田", "name": "本田", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"itemCode": "HotBrand_大众", "name": "大众", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"itemCode": "HotBrand_丰田", "name": "丰田", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "HotBrand_Jeep", "name": "Jeep", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 10, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"itemCode": "HotBrand_路虎", "name": "路虎", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 12, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"itemCode": "HotBrand_日产", "name": "日产", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 18, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "shortName": "热门品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_a0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_a0_AC <PERSON>itzer", "name": "AC Schnitzer", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/acschnitzer.png"}, {"itemCode": "BrandGroup_a0_奥迪", "name": "奥迪", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"itemCode": "BrandGroup_a0_爱驰", "name": "爱驰", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aichi.png"}, {"itemCode": "BrandGroup_a0_阿尔法·罗密欧", "name": "阿尔法·罗密欧", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aerfaluomioun.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_b0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_b0_保时捷", "name": "保时捷", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"itemCode": "BrandGroup_b0_别克", "name": "别克", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "BrandGroup_b0_奔驰", "name": "奔驰", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "BrandGroup_b0_宝马", "name": "宝马", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "BrandGroup_b0_本田", "name": "本田", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"itemCode": "BrandGroup_b0_比亚迪", "name": "比亚迪", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_d0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_d0_大众", "name": "大众", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_f0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_f0_丰田", "name": "丰田", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "BrandGroup_f0_法拉利", "name": "法拉利", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"itemCode": "BrandGroup_f0_福特", "name": "福特", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_g0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_g0_广汽传祺", "name": "广汽传祺", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_h0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_h0_华颂", "name": "华颂", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/huasong.png"}, {"itemCode": "BrandGroup_h0_哈弗", "name": "哈弗", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}, {"itemCode": "BrandGroup_h0_海马", "name": "海马", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}, {"itemCode": "BrandGroup_h0_红旗", "name": "红旗", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_j0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_j0_Jeep", "name": "Jeep", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"itemCode": "BrandGroup_j0_吉利汽车", "name": "吉利汽车", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"itemCode": "BrandGroup_j0_捷达", "name": "捷达", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_k0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_k0_凯迪拉克", "name": "凯迪拉克", "groupCode": "BrandGroup_k0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_l0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_l0_LEVC", "name": "LEVC", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/levc.png"}, {"itemCode": "BrandGroup_l0_兰博基尼", "name": "兰博基尼", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"itemCode": "BrandGroup_l0_劳斯莱斯", "name": "劳斯莱斯", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"itemCode": "BrandGroup_l0_理想汽车", "name": "理想汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc6i1200000ieok0AE70.png"}, {"itemCode": "BrandGroup_l0_路虎", "name": "路虎", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"itemCode": "BrandGroup_l0_雷克萨斯", "name": "雷克萨斯", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_m0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_m0_MINI", "name": "MINI", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"itemCode": "BrandGroup_m0_玛莎拉蒂", "name": "玛莎拉蒂", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_n0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_n0_哪吒汽车", "name": "哪吒汽车", "groupCode": "BrandGroup_n0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R46n120009gwvgrmC0A4.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_q0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_q0_奇瑞", "name": "奇瑞", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qirui.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_r0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_r0_日产", "name": "日产", "groupCode": "BrandGroup_r0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"itemCode": "BrandGroup_r0_荣威", "name": "荣威", "groupCode": "BrandGroup_r0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_s0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_s0_三菱", "name": "三菱", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sanling.png"}, {"itemCode": "BrandGroup_s0_上汽大通MAXUS", "name": "上汽大通MAXUS", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_t0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_t0_坦克", "name": "坦克", "groupCode": "BrandGroup_t0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0yc3612000aoma5tvACF1.png"}, {"itemCode": "BrandGroup_t0_特斯拉", "name": "特斯拉", "groupCode": "BrandGroup_t0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_w0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_w0_威马汽车", "name": "威马汽车", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/weimaqiche.png"}, {"itemCode": "BrandGroup_w0_沃尔沃", "name": "沃尔沃", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/woerwo.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_x0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_x0_现代", "name": "现代", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"itemCode": "BrandGroup_x0_雪佛兰", "name": "雪佛兰", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_y0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_y0_英菲尼迪", "name": "英菲尼迪", "groupCode": "BrandGroup_y0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/yingfeinidi.png"}], "shortName": "全部品牌", "isSupportMulti": true}]}, {"name": "更多筛选", "code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"name": "价格", "sortNum": 1, "groupCode": "Price", "filterItems": [{"itemCode": "Price_0-50", "name": "¥50以下", "code": "0-50", "groupCode": "Price", "sortNum": 1}, {"itemCode": "Price_50-100", "name": "¥50-100", "code": "50-100", "groupCode": "Price", "sortNum": 2}, {"itemCode": "Price_100-200", "name": "¥100-200", "code": "100-200", "groupCode": "Price", "sortNum": 3}, {"itemCode": "Price_200-400", "name": "¥200-400", "code": "200-400", "groupCode": "Price", "sortNum": 4}, {"itemCode": "Price_400-99999", "name": "¥400以上", "code": "400-99999", "groupCode": "Price", "sortNum": 5}], "shortName": "价格"}, {"name": "取车方式", "sortNum": 2, "groupCode": "PickReturn", "bitwiseType": 2, "filterItems": [{"itemCode": "PickReturn_StationPR", "name": "站内取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "mark": "15分钟内", "positionCode": "1"}, {"itemCode": "PickReturn_FreePickupOnDoor", "name": "免费送车上门", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "1"}, {"itemCode": "PickReturn_PickupOnDoor", "name": "送车上门", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "PickReturn_FreeShuttle", "name": "免费接至门店取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false}, {"itemCode": "PickReturn_PickupSelf", "name": "自行到店取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 5, "isQuickItem": false}], "shortName": "取还方式", "isSupportMulti": true}, {"name": "门店服务", "sortNum": 3, "groupCode": "StoreService", "bitwiseType": 1, "filterItems": [{"itemCode": "StoreService_FreeDepositAllCtrip", "name": "信用免押", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 8, "sortNum": 4, "isQuickItem": true, "quickSortNum": 3, "positionCode": "3"}, {"itemCode": "StoreService_Unlimit", "name": "不限里程", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 5, "isQuickItem": false}, {"itemCode": "StoreService_FreeCancel", "name": "免费取消", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 32, "sortNum": 6, "isQuickItem": false}], "shortName": "门店服务", "isSupportMulti": true}, {"name": "自助取还", "sortNum": 4, "groupCode": "SelfService", "bitwiseType": 2, "filterItems": [{"itemCode": "SelfService_Support", "name": "自助取还", "groupCode": "SelfService", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "17"}, {"itemCode": "SelfService_UnSupport", "name": "非自助取还", "groupCode": "SelfService", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}], "shortName": "自助取还", "isSupportMulti": true}, {"name": "优惠活动", "sortNum": 5, "groupCode": "Promotion", "bitwiseType": 2, "filterItems": [{"itemCode": "Promotion_3759", "name": "平台补贴", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false}], "shortName": "优惠活动", "isSupportMulti": true}, {"name": "取车证件", "sortNum": 6, "groupCode": "Ceritificate", "bitwiseType": 2, "filterItems": [{"itemCode": "Ceritificate_1", "name": "身份证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_2", "name": "护照", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 3, "isQuickItem": false}], "shortName": "取车证件", "isSupportMulti": true}, {"name": "驾驶员驾龄", "sortNum": 7, "groupCode": "DriveAge", "bitwiseType": 2, "filterItems": [{"itemCode": "DriveAge_1", "name": "不满6个月", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "DriveAge_2", "name": "满6个月", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "DriveAge_3", "name": "满一年", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}], "shortName": "驾驶员驾龄"}, {"name": "门店评分", "sortNum": 8, "groupCode": "Comment", "bitwiseType": 2, "filterItems": [{"itemCode": "Comment_4.0", "name": "4.0分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}], "shortName": "门店评分"}, {"name": "租车公司", "sortNum": 9, "groupCode": "Vendor_0", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_0", "name": "携程租车中心", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_-1", "name": "携程优选", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": true, "quickSortNum": 1, "icon": "", "mark": "hot", "positionCode": "18"}, {"itemCode": "Vendor_13088", "name": "一嗨租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13031", "name": "桐叶租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30027", "name": "玛雅租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30055", "name": "准典出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30147", "name": "骑仕租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30169", "name": "峰硕租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30182", "name": "加加租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30234", "name": "金晟租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30248", "name": "驰敖天天租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30284", "name": "旭升租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_31025", "name": "乐天出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_31092", "name": "树德出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_31239", "name": "三亚佳途租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_31279", "name": "黑桃壹租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32231", "name": "泰信吉租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32538", "name": "海越租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32687", "name": "行者天下租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32845", "name": "美凯租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_33728", "name": "海友出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_37573", "name": "凯美租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_43973", "name": "夜航租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_52811", "name": "三亚蔚蓝租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_53893", "name": "三亚世纪联合租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_57671", "name": "捷安利达租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_58487", "name": "丰田海南出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61816", "name": "永卓租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61827", "name": "吉海畅行租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_61831", "name": "金达莱租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 9, "groupCode": "Vendor_1", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_62099", "name": "宏驰智行租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62104", "name": "金森租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62107", "name": "启捷租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62115", "name": "三亚启航租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_62305", "name": "三亚皖太租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_63836", "name": "租租侠租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_63857", "name": "信华租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_65413", "name": "哈尔滨奥朗租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_65452", "name": "新易达租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_66324", "name": "环岛租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_67661", "name": "乐享旅途租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_67709", "name": "三亚神风租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69279", "name": "安米租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69280", "name": "百募租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70695", "name": "龙之祥租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70697", "name": "铭车邦租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_71515", "name": "中进通达租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_72983", "name": "吉驰租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_73265", "name": "车之美租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_73619", "name": "海南锦程租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_73871", "name": "多浦达租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_74365", "name": "爱尚出行租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_74373", "name": "海南中进租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_74569", "name": "河北唐亚租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_76105", "name": "全季租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_76903", "name": "如亚租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_77081", "name": "常晟租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_77147", "name": "美点租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_77151", "name": "金晟利租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_78579", "name": "优享旅途租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_78963", "name": "津池租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 9, "groupCode": "Vendor_2", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_79485", "name": "立强租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79695", "name": "炜晨租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79797", "name": "麒麟火租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80115", "name": "鼎航租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80145", "name": "鑫路达租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80431", "name": "小飞侠租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80551", "name": "瑞赢租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80557", "name": "海南信租租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80559", "name": "星锐租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80977", "name": "星月租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81003", "name": "琼州租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81479", "name": "商旅出行", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81527", "name": "北运租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81529", "name": "果岭出行", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81827", "name": "京海租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81829", "name": "诚航租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81831", "name": "潮人商旅租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81889", "name": "八骏马租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81919", "name": "福斯特租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82163", "name": "启瑞盛租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82263", "name": "聚通达租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82819", "name": "助旅租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82843", "name": "名仕租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}]}], "quickFilter": [{"itemCode": "PickReturn_StationPR", "name": "站内取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "mark": "15分钟内", "positionCode": "1"}, {"itemCode": "StoreService_FreeDepositAllCtrip", "name": "信用免押", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 8, "sortNum": 4, "isQuickItem": true, "quickSortNum": 3, "positionCode": "3"}, {"itemCode": "CarAge_3510", "name": "一年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "5"}, {"itemCode": "VehicleAccessory_ReversingImage", "name": "倒车影像", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 2, "isQuickItem": true, "quickSortNum": 6, "positionCode": "6"}, {"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "7"}, {"itemCode": "SeatGroup_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 5, "isQuickItem": true, "quickSortNum": 1, "positionCode": "10"}, {"itemCode": "SelfService_Support", "name": "自助取还", "groupCode": "SelfService", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "17"}, {"itemCode": "Vendor_-1", "name": "携程优选", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": true, "quickSortNum": 1, "icon": "", "mark": "hot", "positionCode": "18"}], "vehicleList": [{"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "5282", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T", "struct": "MPV", "fuel": "95号", "driveMode": "前置前驱", "style": "2020款", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6e1200000mggze066B.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": true, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "44", "mediaTypes": [], "vehicleKey": "0_5282_", "autoParkDesc": {"type": 1, "typeDesc": "支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "奥迪", "brandName": "奥迪", "name": "奥迪A6L新能源", "zhName": "奥迪A6L新能源", "vehicleCode": "4670", "groupCode": "5", "groupSubClassCode": "", "groupName": "豪华轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "2.0T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置四驱/前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "oilType": 4, "fuelType": "插电式", "luggageNum": "可放3个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "endurance": "工信部续航44km-50km", "charge": "快充2.5小时,慢充8.5小时", "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vehiclesSetId": "93", "mediaTypes": [], "vehicleKey": "2_4670_", "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "日产", "brandName": "日产", "name": "日产阳光", "zhName": "日产阳光", "vehicleCode": "3164", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 5, "displacement": "1.5L", "struct": "三厢车", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": [], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": true, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "64", "mediaTypes": [], "vehicleKey": "2_3164_", "autoParkDesc": {"type": 1, "typeDesc": "支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "威马汽车", "brandName": "威马汽车", "name": "威马EX5", "zhName": "威马EX5", "vehicleCode": "4449", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "struct": "SUV", "fuel": "", "driveMode": "前置前驱/前置四驱/null/前置", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "oilType": 5, "fuelType": "纯电动", "luggageNum": "可放2个24寸行李箱", "guidSys": "部分车辆支持自适应巡航", "carPlay": "部分车辆支持支持CarPlay,支持HiCar", "chargeInterface": "部分车辆支持AUX", "skylight": "部分车辆支持", "endurance": "工信部续航200km-520km", "charge": "快充0.67小时,慢充11.2小时", "subGroupCode": "newenergy", "vehiclesSetId": "12", "mediaTypes": [], "vehicleKey": "2_4449_", "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8(2017/18款)", "zhName": "别克GL8(2017/18款)", "vehicleCode": "17529", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T-2.5L", "struct": "MPV", "fuel": "95号或92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV051200000mghqhA727.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "oilType": 5, "luggageNum": "可放5个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": true, "carPhone": true, "autoStart": true, "autoBackUp": true, "mediaTypes": [], "vehicleKey": "2_17529_", "autoParkDesc": {"type": 1, "typeDesc": "支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "奥迪", "brandName": "奥迪", "name": "奥迪Q7", "zhName": "奥迪Q7", "vehicleCode": "121", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 5, "displacement": "3.0T", "struct": "SUV", "fuel": "95号", "driveMode": "前置四驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV1h1200000mi4ui7604.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5j12000chdyrrz0AA3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1t12000chdz3jm9C04.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6g12000chdytss843F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2p12000chdz3jo2B44.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2p12000chdywr9B075.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2212000chdz2snF52D.jpg?mark=yiche", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "10", "mediaTypes": [], "vehicleKey": "2_121_", "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "5282", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T", "struct": "MPV", "fuel": "95号", "driveMode": "前置前驱", "style": "2020款", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV6e1200000mggze066B.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "oilType": 3, "luggageNum": "可放5个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": true, "carPhone": true, "autoStart": true, "autoBackUp": true, "vehiclesSetId": "44", "mediaTypes": [], "vehicleKey": "2_5282_", "autoParkDesc": {"type": 1, "typeDesc": "支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "威马汽车", "brandName": "威马汽车", "name": "威马EX5", "zhName": "威马EX5", "vehicleCode": "4449", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 2, "struct": "SUV", "fuel": "", "driveMode": "前置前驱/前置四驱/null/前置", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/0414h120008n38wwbC2BA.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "oilType": 5, "fuelType": "纯电动", "luggageNum": "可放2个24寸行李箱", "guidSys": "部分车辆支持自适应巡航", "carPlay": "部分车辆支持支持CarPlay,支持HiCar", "chargeInterface": "部分车辆支持AUX", "skylight": "部分车辆支持", "endurance": "工信部续航200km-520km", "charge": "快充0.67小时,慢充11.2小时", "subGroupCode": "newenergy", "vehiclesSetId": "12", "mediaTypes": [], "vehicleKey": "0_4449_", "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8(2017/18款)", "zhName": "别克GL8(2017/18款)", "vehicleCode": "17529", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 5, "displacement": "2.0T-2.5L", "struct": "MPV", "fuel": "95号或92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV051200000mghqhA727.png?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg", "https://dimg04.c-ctrip.com/images/04106120008asiygw0A1C.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04160120008asip6i8C9D.jpg", "oilType": 5, "luggageNum": "可放5个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "charge": "", "autoPark": true, "carPhone": true, "autoStart": true, "autoBackUp": true, "mediaTypes": [], "vehicleKey": "0_17529_", "autoParkDesc": {"type": 1, "typeDesc": "支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}, {"brandEName": "奥迪", "brandName": "奥迪", "name": "奥迪A6L新能源", "zhName": "奥迪A6L新能源", "vehicleCode": "4670", "groupCode": "5", "groupSubClassCode": "", "groupName": "豪华轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 3, "displacement": "2.0T", "struct": "三厢车", "fuel": "95号", "driveMode": "前置四驱/前置前驱", "style": "", "imageList": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0RV4r1200000mj2xxD632.jpg?mark=yiche", "oilType": 4, "fuelType": "插电式", "luggageNum": "可放3个24寸行李箱", "guidSys": "不支持", "carPlay": "不支持", "chargeInterface": "不支持", "skylight": "不支持", "endurance": "工信部续航44km-50km", "charge": "快充2.5小时,慢充8.5小时", "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vehiclesSetId": "93", "mediaTypes": [], "vehicleKey": "0_4670_", "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "autoStartDesc": {"type": 1, "typeDesc": "支持"}}], "productGroups": [{"groupCode": "all", "groupName": "全部车型", "sortNum": -4, "dailyPrice": 0, "hasResult": true}, {"groupCode": "prep", "groupName": "", "sortNum": 0, "productList": [{"vehicleCode": "4670", "sortNum": 1, "lowestPrice": 60, "highestPrice": 60, "maximumRating": 0, "maximumCommentCount": 0, "lowestDistance": 0.1, "vendorPriceList": [{"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "commentInfo": {"level": "", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 60, "currentOriginalDailyPrice": 0, "oTPrice": 60, "currentTotalPrice": 60, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzQ2NzBfMV82MF82MF82MF82MF82MF82MC4wXzBfMF8wLjBfMC4wXzAuMF8wLjBfMC4wMF8wLjAwXzUyODQ5MjQ=", "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "30147", "pStoreCode": "106878", "rStoreCode": "106878", "vehicleCode": "0", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_106878_106878", "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzQ2NzBfMV82MF82MF82MF82MF82MF82MC4wXzBfMF8wLjBfMC4wXzAuMF8wLjBfMC4wMF8wLjAwXzUyODQ5MjQ=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20109095", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": false, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费接您至门店取还车", "rStoreNav": "店员免费接您至门店取还车", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 4, "returnWayInfo": 4, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 60, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 4670, "rentalamount": 60, "totalDailyPrice": 60, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "noLp": 1, "elct": 0, "gsDesc": "低价置顶lyy", "pRc": 0, "rRc": 0, "skuId": 5284924, "klbPId": 201258, "klb": 1, "pCType": 1, "rCType": 1, "pLevel": 94060, "rLevel": 94060, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "4", "s": "100.0", "c": "43", "v": "1937"}, "newEnergy": 1, "platform": 10, "kPSId": 106878, "kRSId": 106878, "kVId": 30147, "pLev": 94060, "rLev": 94060, "klbVersion": 1, "kVehicleId": 4670, "adjustRuleId": "", "packageLevel": "PREP"}, "sortNum": 0, "pStoreRouteDesc": "店员免费接您至门店取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "一年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3510", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 42, "colorCode": "2", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "免加油/充电服务费", "category": 2, "type": 1, "code": "6", "sortNum": 45, "colorCode": "2", "labelCode": "3828", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "比单独加购保障省¥505", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4236", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "storeScore": 100, "isSelect": true, "distance": 0.1, "rDistance": 0.2266, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "4670", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租赁费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "20000001", "name": "无忧租+车损全免保障", "amount": 60, "amountDesc": "¥60"}]}, {"code": "10001", "name": "一口价", "amount": 60, "amountStr": "¥60", "subAmount": 60, "subAmountStr": "¥60", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": "", "type": 2, "vehicleGroup": 5, "secretBox": false, "vehicleKey": "2_4670_", "packageComparison": {"skuId": 5284924, "packageId": 1611, "currentPrice": 60, "comparisionPrice": 565, "diffPrice": 505}}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3866_0_106878_106878"]}, "minTPrice": 60, "minDPrice": 60, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "一年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "8", "labelCode": "3510", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "比单独加购保障省¥505", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4236", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "priceSize": 1, "isEasy": true, "isCredit": false, "rCoup": 0, "pWay": "", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 2, "vehicleKey": "2_4670_"}, {"vehicleCode": "3164", "sortNum": 3, "lowestPrice": 60, "highestPrice": 60, "maximumRating": 0, "maximumCommentCount": 0, "lowestDistance": 0.1, "vendorPriceList": [{"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "commentInfo": {"level": "", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 60, "currentOriginalDailyPrice": 0, "oTPrice": 60, "currentTotalPrice": 60, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzMxNjRfMV82MF82MF82MF82MF82MF82MC4wXzBfMF8wLjBfMC4wXzAuMF8wLjBfMC4wMF8wLjAwXzMzNTQzMDQ=", "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "30147", "pStoreCode": "106878", "rStoreCode": "106878", "vehicleCode": "0", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_106878_106878", "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzMxNjRfMV82MF82MF82MF82MF82MF82MC4wXzBfMF8wLjBfMC4wXzAuMF8wLjBfMC4wMF8wLjAwXzMzNTQzMDQ=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "13362", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费接您至门店取还车", "rStoreNav": "店员免费接您至门店取还车", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 4, "returnWayInfo": 4, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 60, "isrec": false, "recommendOrder": 0, "mergeId": 738, "rectype": 1, "cvid": 3164, "rentalamount": 60, "totalDailyPrice": 60, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "13362", "storeId": "106878"}]}, "adjustVersion": "", "gsId": 0, "noLp": 1, "elct": 0, "gsDesc": "低价置顶lyy", "pRc": 0, "rRc": 0, "skuId": 3354304, "klbPId": 201258, "klb": 1, "pCType": 1, "rCType": 1, "pLevel": 94060, "rLevel": 94060, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "4", "s": "100.0", "c": "43", "v": "1937"}, "newEnergy": 0, "platform": 10, "kPSId": 106878, "kRSId": 106878, "kVId": 30147, "pLev": 94060, "rLev": 94060, "klbVersion": 1, "kVehicleId": 3164, "adjustRuleId": "", "packageLevel": "PREP"}, "sortNum": 0, "pStoreRouteDesc": "店员免费接您至门店取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 42, "colorCode": "2", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "免加油服务费", "category": 2, "type": 1, "code": "6", "sortNum": 45, "colorCode": "2", "labelCode": "4229", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "保障全面", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4243", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "storeScore": 100, "isSelect": true, "distance": 0.1, "rDistance": 0.2266, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "3164", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租赁费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "20000001", "name": "无忧租+车损全免保障", "amount": 60, "amountDesc": "¥60"}]}, {"code": "10001", "name": "一口价", "amount": 60, "amountStr": "¥60", "subAmount": 60, "subAmountStr": "¥60", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": "", "type": 2, "vehicleGroup": 2, "secretBox": false, "vehicleKey": "2_3164_"}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3866_0_106878_106878"]}, "minTPrice": 60, "minDPrice": 60, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "保障全面", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4243", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}, {"title": "券减¥25", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4243", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 2, "vehicleKey": "2_3164_"}, {"vehicleCode": "4449", "sortNum": 5, "lowestPrice": 60, "highestPrice": 60, "maximumRating": 0, "maximumCommentCount": 0, "lowestDistance": 0.1, "vendorPriceList": [{"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "commentInfo": {"level": "", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 60, "currentOriginalDailyPrice": 0, "oTPrice": 60, "currentTotalPrice": 60, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzQ0NDlfMV82MF82MF82MF82MF82MF82MC4wXzBfMF8wLjBfMC4wXzAuMF8wLjBfMC4wMF8wLjAwXzE4NjM3Mjg=", "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "30147", "pStoreCode": "106878", "rStoreCode": "106878", "vehicleCode": "0", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_106878_106878", "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzQ0NDlfMV82MF82MF82MF82MF82MF82MC4wXzBfMF8wLjBfMC4wXzAuMF8wLjBfMC4wMF8wLjAwXzE4NjM3Mjg=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20023889", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费接您至门店取还车", "rStoreNav": "店员免费接您至门店取还车", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 4, "returnWayInfo": 4, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 60, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 4449, "rentalamount": 60, "totalDailyPrice": 60, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "noLp": 1, "elct": 0, "gsDesc": "低价置顶lyy", "pRc": 0, "rRc": 0, "skuId": 1863728, "klbPId": 201258, "klb": 1, "pCType": 1, "rCType": 1, "pLevel": 94060, "rLevel": 94060, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "4", "s": "100.0", "c": "43", "v": "1937"}, "newEnergy": 1, "platform": 10, "kPSId": 106878, "kRSId": 106878, "kVId": 30147, "pLev": 94060, "rLev": 94060, "klbVersion": 1, "kVehicleId": 4449, "adjustRuleId": "", "packageLevel": "PREP"}, "sortNum": 0, "pStoreRouteDesc": "店员免费接您至门店取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "电量保障", "category": 2, "type": 1, "code": "6", "sortNum": 43, "colorCode": "2", "labelCode": "3827", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "免充电服务费", "category": 2, "type": 1, "code": "6", "sortNum": 45, "colorCode": "2", "labelCode": "4222", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "比单独加购保障省¥95", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4236", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "storeScore": 100, "isSelect": true, "distance": 0.1, "rDistance": 0.2266, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "4449", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租赁费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "20000001", "name": "无忧租+车损全免保障", "amount": 60, "amountDesc": "¥60"}]}, {"code": "10001", "name": "一口价", "amount": 60, "amountStr": "¥60", "subAmount": 60, "subAmountStr": "¥60", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": "", "type": 2, "vehicleGroup": 6, "secretBox": false, "vehicleKey": "2_4449_", "packageComparison": {"skuId": 1863728, "packageId": 7174, "currentPrice": 60, "comparisionPrice": 155, "diffPrice": 95}}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3866_0_106878_106878"]}, "minTPrice": 60, "minDPrice": 60, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "比单独加购保障省¥95", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4236", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 2, "vehicleKey": "2_4449_"}, {"vehicleCode": "17529", "sortNum": 7, "lowestPrice": 60, "highestPrice": 60, "maximumRating": 0, "maximumCommentCount": 0, "lowestDistance": 0, "vendorPriceList": [{"vendorName": "桐叶租车", "isMinTPriceVendor": true, "commentInfo": {"level": "", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 60, "currentOriginalDailyPrice": 0, "oTPrice": 60, "currentTotalPrice": 60, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "SH-PRICEVERSION_MTEzOTEyNV8xNzUyOV8xXzYwXzYwXzYwXzYwXzYwXzYwLjBfMF8wXzAuMF8wLjBfMC4wXzAuMF8wXzBfNjQ1NzA5Njk=", "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "13031", "vendorCode": "13031", "pStoreCode": "1139125", "rStoreCode": "1139125", "vehicleCode": "0", "packageId": "201258", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "13031_0_1139125_1139125", "comPriceCode": "eyJzZWxsZXJpZCI6MTEwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6Mjc2MSwiYWN0Z2V0aWQiOjI3NjEsImFjdG9mZmlkIjoyNzYxLCJjYXJ0eXBlaWQiOjE4MTgwLCJ0b3RhbCI6OTcwLCJ0aW1lIjoxNzE4NzA2ODEzfQ==", "priceVersion": "SH-PRICEVERSION_MTEzOTEyNV8xNzUyOV8xXzYwXzYwXzYwXzYwXzYwXzYwLjBfMF8wXzAuMF8wLjBfMC4wXzAuMF8wXzBfNjQ1NzA5Njk=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "18180", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": false, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费上门送取车", "rStoreNav": "店员免费上门送取车", "pickUpOnDoor": true, "dropOffOnDoor": true, "pickWayInfo": 1, "returnWayInfo": 1, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 60, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 17529, "rentalamount": 60, "totalDailyPrice": 60, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "noLp": 1, "elct": 0, "gsDesc": "", "pRc": 0, "rRc": 0, "skuId": 64570969, "klbPId": 201258, "klb": 1, "pCType": 2, "rCType": 2, "pLevel": 2761, "rLevel": 2761, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "1", "s": "100.0", "c": "43"}, "newEnergy": 0, "platform": 0, "kPSId": 1139125, "kRSId": 1139125, "kVId": 13031, "pLev": 113870, "rLev": 113870, "klbVersion": 1, "kVehicleId": 17529, "adjustRuleId": "", "packageLevel": "PREP"}, "sortNum": 0, "pStoreRouteDesc": "店员免费上门送取车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "0", "allTags": [{"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 42, "colorCode": "2", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "免加油服务费", "category": 2, "type": 1, "code": "6", "sortNum": 45, "colorCode": "2", "labelCode": "4229", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "保障全面", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4243", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "storeScore": 100, "isSelect": false, "stock": 99999, "distance": 0, "rDistance": 0.6032, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "桐叶租车", "card": 0, "ctripVehicleCode": "17529", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租赁费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "20000001", "name": "无忧租+车损全免保障", "amount": 60, "amountDesc": "¥60"}]}, {"code": "10001", "name": "一口价", "amount": 60, "amountStr": "¥60", "subAmount": 60, "subAmountStr": "¥60", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": "", "type": 2, "vehicleGroup": 4, "secretBox": false, "vehicleKey": "2_17529_"}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["13031_0_1139125_1139125"]}, "minTPrice": 60, "minDPrice": 60, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "倒车影像/雷达", "category": 2, "type": 1, "code": "6", "sortNum": 14, "colorCode": "1", "labelCode": "3810", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "sortNum": 20, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "保障全面", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4243", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选：店员免费上门送取车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 2, "vehicleKey": "2_17529_"}, {"vehicleCode": "121", "sortNum": 9, "lowestPrice": 60, "highestPrice": 60, "maximumRating": 0, "maximumCommentCount": 0, "lowestDistance": 0.1, "vendorPriceList": [{"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "commentInfo": {"level": "", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 60, "currentOriginalDailyPrice": 0, "oTPrice": 60, "currentTotalPrice": 60, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzEyMV8xXzYwXzYwXzYwXzYwXzYwXzYwLjBfMF8wXzAuMF8wLjBfMC4wXzAuMF8wLjAwXzAuMDBfMTgzODUxMTY=", "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "30147", "pStoreCode": "106878", "rStoreCode": "106878", "vehicleCode": "0", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_106878_106878", "comPriceCode": "[c]", "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzEyMV8xXzYwXzYwXzYwXzYwXzYwXzYwLjBfMF8wXzAuMF8wLjBfMC4wXzAuMF8wLjAwXzAuMDBfMTgzODUxMTY=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20000707", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费接您至门店取还车", "rStoreNav": "店员免费接您至门店取还车", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 4, "returnWayInfo": 4, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 60, "isrec": false, "recommendOrder": 0, "mergeId": 612, "rectype": 1, "cvid": 121, "rentalamount": 60, "totalDailyPrice": 60, "vdegree": "0", "grantedcode": "", "mergeInfo": [{"vehicleId": "20000707", "storeId": "106878"}]}, "adjustVersion": "", "gsId": 0, "noLp": 1, "elct": 0, "gsDesc": "低价置顶lyy", "pRc": 0, "rRc": 0, "skuId": 18385116, "klbPId": 201258, "klb": 1, "pCType": 1, "rCType": 1, "pLevel": 94060, "rLevel": 94060, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "4", "s": "100.0", "c": "43", "v": "1937"}, "newEnergy": 0, "platform": 10, "kPSId": 106878, "kRSId": 106878, "kVId": 30147, "pLev": 94060, "rLev": 94060, "klbVersion": 1, "kVehicleId": 121, "adjustRuleId": "", "packageLevel": "PREP"}, "sortNum": 0, "pStoreRouteDesc": "店员免费接您至门店取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 42, "colorCode": "2", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "免加油服务费", "category": 2, "type": 1, "code": "6", "sortNum": 45, "colorCode": "2", "labelCode": "4229", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "保障全面", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4243", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "storeScore": 100, "isSelect": true, "distance": 0.1, "rDistance": 0.2266, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "121", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租赁费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "20000001", "name": "无忧租+车损全免保障", "amount": 60, "amountDesc": "¥60"}]}, {"code": "10001", "name": "一口价", "amount": 60, "amountStr": "¥60", "subAmount": 60, "subAmountStr": "¥60", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": "", "type": 2, "vehicleGroup": 6, "secretBox": false, "vehicleKey": "2_121_"}], "reactId": "**********", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3866_0_106878_106878"]}, "minTPrice": 60, "minDPrice": 60, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "sortNum": 9, "colorCode": "1", "labelCode": "3548", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "保障全面", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4243", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 2, "vehicleKey": "2_121_"}, {"vehicleCode": "5282", "sortNum": 11, "lowestPrice": 60, "highestPrice": 60, "maximumRating": 0, "maximumCommentCount": 0, "lowestDistance": 0.1, "vendorPriceList": [{"vendorName": "骑仕租车", "isMinTPriceVendor": true, "vendorLogo": "", "commentInfo": {"level": "", "commentCount": 0, "qCommentCount": 0, "qExposed": "0.0", "overallRating": "0.0", "maximumRating": 5, "commentLabel": "", "hasComment": 0}, "priceInfo": {"currentDailyPrice": 60, "currentOriginalDailyPrice": 0, "oTPrice": 60, "currentTotalPrice": 60, "currentCurrencyCode": "CNY", "localCurrencyCode": "CNY", "marginPrice": 0, "naked": true, "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzUyODJfMV82MF82MF82MF82MF82MF82MC4wXzBfMF8wLjBfMC4wXzAuMF8wLjBfMC4wMF8wLjAwXzE4NjE0MDA=", "priceType": 1, "deductInfos": []}, "reference": {"bizVendorCode": "SD3866", "vendorCode": "30147", "pStoreCode": "106878", "rStoreCode": "106878", "vehicleCode": "0", "packageType": 1, "vcExtendRequest": {"vendorVehicleId": "0"}, "isEasyLife": true, "payMode": 2, "productCode": "SD3866_0_106878_106878", "comPriceCode": "[c]NDAwfDE4NjE0LTA2fDIwMjAwOjAtMjYgJjYwJjA6MDAyMDAwJjEkfCYxJjYwMDAxJHwyMDAmNjA2LTI2MjQtMDAwOjAgMTA6MjQtMDAmMjAgMTA6Ni0yNzB8MjAwMDowNi0xODI0LTAzMzo0IDE4OgAAAAA5AAAA", "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzUyODJfMV82MF82MF82MF82MF82MF82MC4wXzBfMF8wLjBfMC4wXzAuMF8wLjBfMC4wMF8wLjAwXzE4NjE0MDA=", "pCityId": 43, "rCityId": 43, "vendorVehicleCode": "20068007", "age": 30, "alipay": false, "aType": 0, "vendorSupportZhima": true, "hotType": 0, "priceType": 1, "vehicleDegree": "0", "fType": 0, "isSelect": true, "noDepositFilter": {}, "labels": [], "pStoreNav": "店员免费接您至门店取还车", "rStoreNav": "店员免费接您至门店取还车", "pickUpOnDoor": false, "dropOffOnDoor": false, "pickWayInfo": 4, "returnWayInfo": 4, "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "hot": 0, "freeIllegalDeposit": false, "creditFreeCarDeposit": false, "isdShareInfo": {"orginaltotal": 60, "isrec": false, "recommendOrder": 0, "mergeId": 0, "rectype": 1, "cvid": 5282, "rentalamount": 60, "totalDailyPrice": 60, "vdegree": "0", "grantedcode": ""}, "adjustVersion": "", "gsId": 0, "noLp": 1, "elct": 0, "gsDesc": "低价置顶lyy", "pRc": 0, "rRc": 0, "skuId": 1861400, "klbPId": 201258, "klb": 1, "pCType": 1, "rCType": 1, "pLevel": 94060, "rLevel": 94060, "promtId": 0, "rCoup": 0, "sortInfo": {"p": "4", "s": "100.0", "c": "43", "v": "1937"}, "newEnergy": 0, "platform": 10, "kPSId": 106878, "kRSId": 106878, "kVId": 30147, "pLev": 94060, "rLev": 94060, "klbVersion": 1, "kVehicleId": 5282, "adjustRuleId": "", "packageLevel": "PREP"}, "sortNum": 0, "pStoreRouteDesc": "店员免费接您至门店取还车", "easyLifeInfo": {"isEasyLife": true}, "platformCode": "10", "allTags": [{"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "限时免费取消", "category": 1, "type": 2, "code": "1", "sortNum": 40, "colorCode": "6", "labelCode": "3679", "groupCode": "MarketGroup1336", "groupId": 3, "mergeId": 1}, {"title": "信用免押", "category": 2, "type": 1, "code": "7", "sortNum": 41, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}, {"title": "满油取车", "category": 2, "type": 1, "code": "6", "sortNum": 42, "colorCode": "2", "labelCode": "3788", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "免加油服务费", "category": 2, "type": 1, "code": "6", "sortNum": 45, "colorCode": "2", "labelCode": "4229", "groupCode": "MarketGroup1325", "groupId": 3, "mergeId": 0}, {"title": "比单独加购保障省¥100", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4236", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "storeScore": 100, "isSelect": true, "distance": 0.1, "rDistance": 0.2266, "adverts": 0, "payModes": [2], "freeDeposit": 0, "actId": "", "couId": "", "adjustPriceInfo": {}, "pickUpFee": 0, "pickOffFee": 0, "showVendor": false, "isOrderVehicle": false, "cyVendorName": "骑仕租车", "card": 0, "ctripVehicleCode": "5282", "cashbackPre": "", "modifySameStore": false, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租赁费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "20000001", "name": "无忧租+车损全免保障", "amount": 60, "amountDesc": "¥60"}]}, {"code": "10001", "name": "一口价", "amount": 60, "amountStr": "¥60", "subAmount": 60, "subAmountStr": "¥60", "currencyCode": "¥"}], "priceDailys": [], "licenseTag": "", "type": 2, "vehicleGroup": 4, "secretBox": false, "vehicleKey": "2_5282_", "packageComparison": {"skuId": 2457215, "packageId": 1611, "currentPrice": 60, "comparisionPrice": 160, "diffPrice": 100}}], "reactId": "**********", "group": 870, "groupSort": 5, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3866_0_106878_106878"]}, "minTPrice": 60, "minDPrice": 60, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "sortNum": 15, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "tagGroups": 1, "groupId": 2, "mergeId": 0}, {"title": "比单独加购保障省¥100", "category": 2, "type": 1, "code": "15", "sortNum": 10000, "colorCode": "1", "labelCode": "4236", "groupCode": "MarketGroup1325", "tagGroups": 3, "groupId": 3, "mergeId": 0}], "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "type": 2, "vehicleKey": "2_5282_"}], "dailyPrice": 60, "hasResult": true, "groupAction": 1}, {"groupCode": "2", "groupName": "经济轿车", "sortNum": 1, "dailyPrice": 0, "hasResult": true, "groupAction": 0}, {"groupCode": "newenergy", "groupName": "新能源", "sortNum": 3, "dailyPrice": 0, "hasResult": true, "groupAction": 0}, {"groupCode": "3", "groupName": "舒适轿车", "sortNum": 4, "dailyPrice": 0, "hasResult": true, "groupAction": 0}, {"groupCode": "6", "groupName": "SUV", "sortNum": 5, "dailyPrice": 0, "hasResult": true, "groupAction": 0}, {"groupCode": "4", "groupName": "商务车", "sortNum": 6, "dailyPrice": 0, "hasResult": true, "groupAction": 0}, {"groupCode": "5", "groupName": "豪华轿车", "sortNum": 7, "dailyPrice": 0, "hasResult": true, "groupAction": 0}, {"groupCode": "9", "groupName": "跑车", "sortNum": 8, "dailyPrice": 0, "hasResult": true, "groupAction": 0}, {"groupCode": "10", "groupName": "皮卡", "sortNum": 11, "dailyPrice": 1100, "hasResult": true, "groupAction": 0}], "productGroupsHashCode": "xDwu8uNTCy0UGsk627sI", "storeList": [{"storeCode": "106878", "pickUpLevel": 94060, "pickOffLevel": 94060}, {"storeCode": "1139125", "pickUpLevel": 2761, "pickOffLevel": 2761}], "commNotices": [], "rentCenter": {"id": 2, "name": "三亚凤凰机场店", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼停车场停车场停车场4楼14", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.305902", "lng": "109.413683", "filterCode": "Vendor_0", "fromTime": "00:05", "toTime": "23:59"}, "rRentCenter": {"id": 2, "name": "三亚凤凰机场店", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼停车场停车场停车场4楼14", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.305902", "lng": "109.413683", "filterCode": "Vendor_0", "fromTime": "00:05", "toTime": "23:59"}, "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"content": "黄金贵宾", "style": "0"}, {"url": "https://dimg.fws.qa.nt.ctripcorp.com/images/0300h1200000dnq527E8C.png"}]}, {"stringObjs": [{"content": "专享租车费95折", "style": "0"}]}], "type": 11, "icon": "https://dimg.fws.qa.nt.ctripcorp.com/images/0300z1200000dnjdo675F.png", "locations": [{"groupCode": "all", "index": 1}], "button": {"icon": "https://dimg.fws.qa.nt.ctripcorp.com/images/0300a1200000dnqh32FD1.png"}, "backGroundUrl": "https://dimg.fws.qa.nt.ctripcorp.com/images/0306d1200000dnjea0D12.png", "textColor": {"r": 174, "g": 128, "b": 50, "a": 1}, "tangChoiceTypes": "[2]", "jumpUrl": "/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Member&apptype=ISD_C_APP"}, {"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}, {"type": 18, "locations": [{"groupCode": "all", "index": 3}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg5t12000cixdsaaBF66.png"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "安心保障", "titleExtra": "(需加购优享服务)", "type": 1, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 2, "subTitle": "*覆盖损失范围以预订页面内披露为准", "showLayer": 0}, {"title": "优质车况", "type": 1, "description": "3年内车龄", "sortNum": 3, "subTitle": "", "showLayer": 0}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满", "sortNum": 10, "subTitle": "", "showLayer": 0}]}, "basicData": {"sortItems": [{"title": "推荐排序", "type": 1, "code": "1", "sortNum": 1}, {"title": "总价 低→高", "type": 2, "code": "2", "sortNum": 2}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "labelCodes": ["3563", "3759", "3510", "3872", "3696", "3697", "3653", "3698", "3731", "3810", "4229", "3679", "3757", "3779", "4243", "4222", "3495", "3494", "3504", "3548", "3827", "3547", "3503", "3502", "3828", "3501", "3709", "4236", "3788", "3866", "3746"], "isAll": false, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isLastPage": false, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "sTitle": "9大无忧特权", "hint": "优选门店无忧租超值价"}, {"id": 2, "title": "无忧租 安心首选", "sTitle": "9大无忧特权", "hint": "无忧租超值价"}, {"id": 3, "title": "全国连锁 服务放心", "sTitle": "", "hint": "一嗨租车超值价"}, {"id": 4, "title": "上门送取车 取还超便捷", "sTitle": "", "hint": "送车上门超值价"}, {"id": 5, "title": "信用租 押金双免", "sTitle": "", "hint": "押金双免超值价"}, {"id": 6, "title": "新车保障 车况佳", "sTitle": "", "hint": "新车超值价"}, {"id": 7, "title": "超值特价 高性价比", "sTitle": "", "hint": "超值特价"}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "hasResultWithoutFilter": true, "isFromSearch": false, "productGroupCodeUesd": "prep", "uniqSign": "12001171410000201388UtA17i36hUtuzsP8877z", "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "pHub": 1, "rHub": 1, "promotMap": {"510323371": "43yBytBvFecwUwrm7GWRHw=="}, "extras": {"packageLevelAB": "B", "selfServiceSwitch": "1", "isLicensePlateHideShow": "0", "packageLevelSwitch": "1", "commodityClass2Version": "1", "abVersion": "240419_DSJT_wyz24|B,230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B,231218_DSJT_zzqh|B,231218_DSJT_zzqh|B", "isNewLicensePlate": "0", "serverRequestId": "3E0UD5TO0M7540Y03GC3"}, "isRecommend": false, "isKlbData": true, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&listExtraMaps[packageLevelVersion]=1&modify=&orderId=&pickupPointInfo=2024-06-26 10:00:00|凤凰机场站|43|18.308986|109.408787|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=prep&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2024-06-27 10:00:00|凤凰机场站|43|18.308986|109.408787|||&sortType=1&uid=*********@@PAGENUM@@1", "groupId": "18631/queryProducts?batch=", "networkCost": 605, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 605, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1718706948350, "afterFetch": 1718706948955}}