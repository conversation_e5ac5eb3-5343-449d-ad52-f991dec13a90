{"baseResponse": {"extMap": {"IncludeFeesCost": "0.0", "checkRentCenter_2": "0.0", "mergeGroupSize_11": "1", "mergeGroup_5": "0.0", "apiCost": "2090.0", "9_getFromRedisCost": "214", "mergeGroup_9": "1.0", "mergeGroup_10": "0.0", "2_getFromRedisCost": "123", "mergeGroupSize_2": "61", "totalCostTime": "278", "gsCost": "0.0", "mergeGroupSize_3": "36", "restCost": "1", "originalCode": "200", "mergeGroupSize_4": "42", "lastInfoCost_7": "100.0", "mergeGroup_2": "1.0", "3_getFromRedisCost": "123", "mergeGroupSize_5": "34", "mergeGroup_6": "2.0", "mergeGroupSize_6": "109", "mergeGroupSize_newenergy": "70", "getPageInfoFromResponse_pre": "1", "mergeGroupSize_7": "1", "needWait": "0", "4_getFromRedisCost": "192", "requestId": "582f9c9a-e2c6-433f-ba08-2715f399c5aa", "mergeGroupSize_9": "53", "start": "2023-05-11 17:51:37", "dataConvertResCost": "330.0", "buildInfoCost_1": "0.0", "10_getFromRedisCost": "4", "buildInfoCost_2": "204.0", "11_getFromRedisCost": "4", "buildInfoCost_3": "1.0", "mergeGroup_3": "0.0", "ubtProcessCost_8": "1.0", "buildInfoCost_4": "206.0", "5_getFromRedisCost": "123", "shoppingCost_1": "2092.0", "mergeGroup_7": "0.0", "allCost": "2423.0", "newenergy_getFromRedisCost": "196", "mergeGroup_11": "0.0", "end": "2023-05-11 17:51:40", "setProductGroupsHashCodeCostAffect": "0.0", "contextBuilderCost_3": "15.0", "restOriginalCost": "2423.0", "6_getFromRedisCost": "222", "uid": "E2236924826", "pickupCityId": "43", "dropoffCityId": "43", "mergeGroup_4": "0.0", "usePage": "1", "7_getFromRedisCost": "3", "responseHeadCost": "43", "initBaseData_1": "15.0", "calculatePreAuth_3": "0.0", "mergeGroup_newenergy": "5.0", "productGroupCost_6": "9.0", "runAsyncCost_2": "0.0", "sourceFrom": "ISD_C_APP", "mergeGroupSize_10": "2", "pageName": "List"}, "errorCode": "0", "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "8cfca18c-5f2f-4726-b930-291ceffc00fb", "isSuccess": true}, "rHub": 0, "isKlbData": true, "allVendorPriceCount": 121, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": false, "extras": {"isNewLicensePlate": "0", "serverRequestId": "Fy68IyOwG3f24178G01y", "abVersion": "230104_DSJT_fil10|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B,221207_DSJT_cxvr|A"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"shortName": "车龄", "sortNum": 1, "bitwiseType": 2, "name": "车龄", "groupCode": "CarAge", "filterItems": [{"sortNum": 1, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "5", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 2, "positionCode": "5", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 3, "positionCode": "5", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "手动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "NewEnergy", "itemCode": "NewEnergy_elect", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}, {"sortNum": 2, "groupCode": "NewEnergy", "itemCode": "NewEnergy_mix", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "新能源混动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 3, "groupCode": "VehicleAccessory", "quickSortNum": 7, "positionCode": "6", "itemCode": "VehicleAccessory_radar", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "倒车雷达"}, {"sortNum": 4, "groupCode": "VehicleAccessory", "quickSortNum": 8, "positionCode": "6", "itemCode": "VehicleAccessory_tachograph", "binaryDigit": 64, "isQuickItem": true, "bitwiseType": 1, "name": "行车记录仪"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}, {"sortNum": 7, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Refrigerator", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 1, "name": "车载冰箱"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "HotBrand", "itemCode": "HotBrand_奥迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_别克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_奔驰", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_宝马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotB<PERSON>_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 6, "groupCode": "HotBrand", "itemCode": "HotBrand_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 10, "groupCode": "HotBrand", "itemCode": "HotBrand_Jeep", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 12, "groupCode": "HotBrand", "itemCode": "HotBrand_路虎", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 18, "groupCode": "HotBrand", "itemCode": "HotBrand_日产", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_奥迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_阿斯顿·马丁", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "阿斯顿·马丁", "icon": "https://dimg04.c-ctrip.com/images/0R419120009gwx9cqBA77.png"}], "groupCode": "BrandGroup_a0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_BEIJING汽车", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "BEIJING汽车", "icon": "https://dimg04.c-ctrip.com/images/0R46u120009gwv7gj857B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_保时捷", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "保时捷", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_别克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_北京", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "北京", "icon": "//pages.c-ctrip.com/carisd/brandlogo/beijin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔腾", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "奔腾", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benteng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔驰", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝沃", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "宝沃", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baowo.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝骏", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "宝骏", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baojun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_巴博斯", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "巴博斯", "icon": "//dimg04.c-ctrip.com/images/0AS47120008bmtwth44BA.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_本田", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_比亚迪", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "比亚迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长城", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "长城", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changcheng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "长安", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changan.png"}], "groupCode": "BrandGroup_c0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风风光", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "东风风光", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengguang.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风风行", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "东风风行", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengxin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_道奇", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "道奇", "icon": "//pages.c-ctrip.com/carisd/brandlogo/daoqi.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_法拉利", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "法拉利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_福特", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "福特", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}], "groupCode": "BrandGroup_f0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽传祺", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "广汽传祺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽埃安", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "广汽埃安", "icon": "https://dimg04.c-ctrip.com/images/0R44f120009gwvd13842B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽新能源", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "广汽新能源", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqixingnengyuan.png"}], "groupCode": "BrandGroup_g0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_华晨新日", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "华晨新日", "icon": "https://dimg04.c-ctrip.com/images/0R472120009gwv9rxB7E1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_哈弗", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "哈弗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_海马", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "海马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_红旗", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "红旗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}], "groupCode": "BrandGroup_h0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_Jeep", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_吉利汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "吉利汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷豹", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "捷豹", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiebao.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷达", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "捷达", "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_极氪", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "极氪", "icon": "https://dimg04.c-ctrip.com/images/0yc0x12000aom51we094D.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_k0", "itemCode": "BrandGroup_k0_凯迪拉克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "凯迪拉克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "groupCode": "BrandGroup_k0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_兰博基尼", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "兰博基尼", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_劳斯莱斯", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "劳斯莱斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_岚图汽车", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "岚图汽车", "icon": "https://dimg04.c-ctrip.com/images/0R45a120009gwvmb34A33.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_理想汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "理想汽车", "icon": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc6i1200000ieok0AE70.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_路虎", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_零跑汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "零跑汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingpaoqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_雷克萨斯", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "雷克萨斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_领克", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "领克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingke.png"}], "groupCode": "BrandGroup_l0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_MINI", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "MINI", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_名爵", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "名爵", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mingjue.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_玛莎拉蒂", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "玛莎拉蒂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_迈凯伦", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "迈凯伦", "icon": "//pages.c-ctrip.com/carisd/brandlogo/maikailun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_马自达", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "马自达", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mazida.png"}], "groupCode": "BrandGroup_m0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_n0", "itemCode": "BrandGroup_n0_哪吒汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "哪吒汽车", "icon": "https://dimg04.c-ctrip.com/images/0R46n120009gwvgrmC0A4.png"}], "groupCode": "BrandGroup_n0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_o0", "itemCode": "BrandGroup_o0_欧拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "欧拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/oula.png"}], "groupCode": "BrandGroup_o0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_p0", "itemCode": "BrandGroup_p0_Polestar极星", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "Polestar极星", "icon": "https://dimg04.c-ctrip.com/images/0R43g120009gwuo9r0192.png"}], "groupCode": "BrandGroup_p0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_前途", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "前途", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiantu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_奇瑞", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "奇瑞", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qirui.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_起亚", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "起亚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "groupCode": "BrandGroup_q0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_日产", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_荣威", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "荣威", "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "groupCode": "BrandGroup_r0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_SERES赛力斯", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "SERES赛力斯", "icon": "https://dimg04.c-ctrip.com/images/0R450120009gwvngnB95E.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_smart", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "smart", "icon": "//pages.c-ctrip.com/carisd/brandlogo/smart.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_上汽大通MAXUS", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "上汽大通MAXUS", "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}], "groupCode": "BrandGroup_s0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_坦克", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "坦克", "icon": "https://dimg04.c-ctrip.com/images/0yc3612000aoma5tvACF1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_特斯拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "特斯拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}], "groupCode": "BrandGroup_t0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_WEY", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "WEY", "icon": "//pages.c-ctrip.com/carisd/brandlogo/wey.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_五菱汽车", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "五菱汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/wulingqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_威马汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "威马汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weimaqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_沃尔沃", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "沃尔沃", "icon": "//pages.c-ctrip.com/carisd/brandlogo/woerwo.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_蔚来", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "蔚来", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weilai.png"}], "groupCode": "BrandGroup_w0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小鹏", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "小鹏", "icon": "https://dimg04.c-ctrip.com/images/0yc5812000aom40h4060E.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小鹏汽车", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "小鹏汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiaopengqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_现代", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "现代", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪佛兰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "雪佛兰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_依维柯", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "依维柯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yikewei.png"}, {"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_英菲尼迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "英菲尼迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yingfeinidi.png"}], "groupCode": "BrandGroup_y0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_SONGSAN MOTORS", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "SONGSAN MOTORS", "icon": ""}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_睿蓝汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "睿蓝汽车", "icon": ""}], "groupCode": "BrandGroup_z0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "0-50", "sortNum": 1, "name": "¥50以下", "groupCode": "Price", "itemCode": "Price_0-50"}, {"code": "50-100", "sortNum": 2, "name": "¥50-100", "groupCode": "Price", "itemCode": "Price_50-100"}, {"code": "100-200", "sortNum": 3, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-400", "sortNum": 4, "name": "¥200-400", "groupCode": "Price", "itemCode": "Price_200-400"}, {"code": "400-99999", "sortNum": 5, "name": "¥400以上", "groupCode": "Price", "itemCode": "Price_400-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 4, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupSelf", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "自行到店取车"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "StoreService", "quickSortNum": 4, "positionCode": "3", "itemCode": "StoreService_easyLife", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 1, "name": "无忧租"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 6, "groupCode": "StoreService", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "Promotion", "itemCode": "Promotion_3662", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "春节特惠"}, {"sortNum": 4, "groupCode": "Promotion", "itemCode": "Promotion_3836", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "盲盒测试"}], "groupCode": "Promotion", "shortName": "优惠活动", "bitwiseType": 2, "name": "优惠活动"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_8", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "台胞证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "驾驶员驾龄", "sortNum": 6, "bitwiseType": 2, "name": "驾驶员驾龄", "groupCode": "DriveAge", "filterItems": [{"sortNum": 1, "groupCode": "DriveAge", "itemCode": "DriveAge_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "不满6个月"}, {"sortNum": 2, "groupCode": "DriveAge", "itemCode": "DriveAge_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "满6个月"}, {"sortNum": 3, "groupCode": "DriveAge", "itemCode": "DriveAge_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "满一年"}]}, {"shortName": "门店评分", "sortNum": 7, "bitwiseType": 2, "name": "门店评分", "groupCode": "Comment", "filterItems": [{"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "9", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}]}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "18", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "携程优选", "icon": ""}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30147", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "calabi-骑仕租车A"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30150", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "小木鱼租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30164", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "懒人行卡拉比"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30169", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "峰硕租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30196", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "太平洋租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31218", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "大权租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32231", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "泰信吉租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32498", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "么么达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32687", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "行者天下租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_33419", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "欣岳美行租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_37573", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "凯美租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_47522", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "祥驰租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61365", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "普信租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61372", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "程硕租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61659", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "旺亚租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61671", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "凤翔天涯租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61937", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "锋达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61951", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "鑫旺达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62099", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "宏驰智行租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62104", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "金森租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62107", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "启捷租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62115", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "启航租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62119", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "三鹤租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62166", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "小红帽租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62167", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "玖捌陆租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62408", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "宏广东盈租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_62863", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_63457", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "翊霏租车"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63460", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "小米租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_64429", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "十八度阳光租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_64662", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "潆莹租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_65451", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "荣树租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_65452", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "新易达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_65454", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "信和华租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_69279", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "安米租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_69280", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "百募租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_69287", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "华鑫海租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_72621", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "细杰租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_73619", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "锦程租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_73871", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "多浦达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_77287", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "老马出行"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_77523", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "博汇租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_78577", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "夏末微凉租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_78579", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "优享旅途租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_79797", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "麒麟火租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_79799", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "华瑞租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80545", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "盛兴隆租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_80559", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "星锐租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_81827", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "京海租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_81829", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "诚航商旅"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_81831", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "潮人商旅"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_81889", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "八骏马租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_81919", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "福斯特租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82105", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "旅途中租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82163", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "启瑞盛租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82393", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "海鸭鸭租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82671", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82843", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": ""}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_82867", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": ""}], "groupCode": "Vendor_1", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "K43x80m1842N1221q6n3", "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 64, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 156, "detail": [{"code": "1001", "amount": 176, "amountDesc": "¥176", "name": "租车费"}, {"code": "11037", "amount": 20, "amountDesc": "¥20", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥78", "originalDailyPrice": 88, "subAmount": 78, "name": "车辆租金", "amountStr": "¥156"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 236, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥256", "subAmount": 256, "name": "总价", "amountStr": "¥236"}], "reference": {"vehicleCode": "0", "rStoreCode": "106896", "pLev": 23665, "comPriceCode": "[c]MTMyfDE4NjN8MjAyMC4wMC0xNyAzLTA1MDowMDAwOjAmMSQyJjg4JjA1LTEwMjMtOjAwOjggMDA4JiYxMDAmODAxJjIkfDEwMTc2JCY4OCYmMSYyMTAwMyYyMC4wLjAwMDAyJjAwJDEmNjAkMiYzMDMtMDV8MjAyMTc6NS0xMSAAAAAAMTozOA==", "bizVendorCode": "SD37573", "pStoreCode": "106896", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2ODk2XzQxMzlfMV84OF8xNzZfODhfMjU2LjAwXzc4LjBfMjM2LjBfMF8wXzAuMF8wLjBfNjBfMjAuMDBfMC4wMF8wLjAwXzE4NjMxMzI=", "sendTypeForPickUpCar": 0, "skuId": 1863132, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 23665, "vendorCode": "37573", "vendorVehicleCode": "20076929"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1861254, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1854917, "bizVendorCode": "SD30147"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1915995, "bizVendorCode": "SD77287"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2705001, "bizVendorCode": "SD81827"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2704960, "bizVendorCode": "SD33419"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1912422, "bizVendorCode": "44444"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1919961, "bizVendorCode": "SD62863"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2362546, "bizVendorCode": "SD62119"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减20", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "4139", "highestPrice": 568, "rCoup": 0, "minDPrice": 78, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 236, "lowestDistance": 0, "group": 0, "sortNum": 3, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106878_106878"], "introduce": "当前车型最低价"}, "minDOrinPrice": 88, "isEasy": true, "isCredit": true, "maximumCommentCount": 13, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "17514053", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 9}, {"groupSort": 0, "lowestPrice": 123, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 246, "detail": [{"code": "1001", "amount": 286, "amountDesc": "¥286", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥123", "originalDailyPrice": 143, "subAmount": 123, "name": "车辆租金", "amountStr": "¥246"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 361, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥401", "subAmount": 401, "name": "总价", "amountStr": "¥361"}], "reference": {"vehicleCode": "0", "rStoreCode": "107380", "pLev": 24251, "comPriceCode": "[c]NzUyfDE4NjR8MjAyMC4xMC0xNyAzLTA1MDowMDAwOjAmJjEkJjE0My0wNS0yMDIzMDowMDE4IDAxNDMmOjAwJjEwMDEmMSR8NDMmMiYyJjEwMDMmODYkMS4wMCYxJjM1MCQxMDM1LjAmNDAmMDImMjIwMjM4MCR8MTEgMS0wNS06NDYAODo0OQ==", "bizVendorCode": "SD61671", "pStoreCode": "107380", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3MzgwXzUyODlfMV8xNDNfMjg2XzE0M180MDEuMDBfMTIzLjBfMzYxLjBfMF8wXzAuMF8wLjBfODBfMzUuMDBfMC4wMF8wLjAwXzE4NjQ3NTI=", "sendTypeForPickUpCar": 0, "skuId": 1864752, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24251, "vendorCode": "61671", "vendorVehicleCode": "20069320"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1861163, "bizVendorCode": "SD81675"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1863356, "bizVendorCode": "SD76105"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5289", "highestPrice": 348, "rCoup": 0, "minDPrice": 123, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 361, "lowestDistance": 0, "group": 0, "sortNum": 10, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD61671_0_107380_107380"], "introduce": "当前车型最低价"}, "minDOrinPrice": 143, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 3}, {"groupSort": 0, "lowestPrice": 85, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 170, "detail": [{"code": "1001", "amount": 276, "amountDesc": "¥276", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}, {"code": "3662", "amount": 66, "amountDesc": "¥66", "name": "春节特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥85", "originalDailyPrice": 138, "subAmount": 85, "name": "车辆租金", "amountStr": "¥170"}, {"code": "CAR_SERVICE_FEE", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}, {"code": "11006", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "夜间取车费"}, {"code": "11007", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "夜间还车费"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 370, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥476", "subAmount": 476, "name": "总价", "amountStr": "¥370"}], "reference": {"vehicleCode": "0", "rStoreCode": "106878", "pLev": 22341, "comPriceCode": "[c]NTM0fDE4NjF8MjAyMC4wMC0xNyAzLTA1MDowMDAwOjAmJjEkJjEzOC0wNS0yMDIzMDowMDE4IDAxMzgmOjAwJjEwMDEmMSR8MzgmMiYyJjEwMDMmNzYkMS4wMCYxJjIwMCQxMDIwLjAmNDAmMDImMjIwMjM4MCR8MTEgMS0wNS06MzgANzo1MQ==", "bizVendorCode": "SD30147", "pStoreCode": "106878", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA2ODc4XzQwNTFfMV8xMzhfMjc2XzEzOF80NzYuMDBfODUuMF8zNzAuMF8wXzBfMC4wXzAuMF84MF8yMC4wMF8wLjAwXzAuMDBfMTg2MTUzNA==", "sendTypeForPickUpCar": 0, "skuId": 1861534, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 22341, "vendorCode": "30147", "vendorVehicleCode": "20034852"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1965139, "bizVendorCode": "SD33419"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减106", "groupCode": "MarketGroup103", "code": "30", "title": "春节特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3662"}, "vehicleCode": "4101", "highestPrice": 130, "rCoup": 0, "minDPrice": 85, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 370, "lowestDistance": 0, "group": 0, "sortNum": 11, "maximumRating": 3.7, "vehicleRecommendProduct": {"productCodes": ["SD30147_0_106878_106878"], "introduce": "当前车型最低价"}, "minDOrinPrice": 138, "isEasy": true, "isCredit": true, "maximumCommentCount": 13, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "17514057", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 2}, {"groupSort": 3, "lowestPrice": 168, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 336, "detail": [{"code": "1001", "amount": 376, "amountDesc": "¥376", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥168", "originalDailyPrice": 188, "subAmount": 168, "name": "车辆租金", "amountStr": "¥336"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 200, "amountStr": "¥200", "detail": [{"code": "1002", "amount": 200, "amountDesc": "¥200", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 571, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥611", "subAmount": 611, "name": "总价", "amountStr": "¥571"}], "reference": {"vehicleCode": "0", "rStoreCode": "115278", "pLev": 49923, "comPriceCode": "[c]ODkxfDE5MjN8MjAyMC4wMC0xNyAzLTA1MDowMDAwOjAmJjEkJjE4OC0wNS0yMDIzMDowMDE4IDAxODgmOjAwJjEwMDEmMSR8ODgmMyYyJjEwMDMmNzYkMS4wMCYxJjM1MCQxMDM1LjAmMTAwMDImMiR8MjAmMjAwNS0xMTIzLTA1MTozIDE3OgAAAAA4AAAA", "bizVendorCode": "SD82843", "pStoreCode": "115278", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTE1Mjc4XzU1MjRfMV8xODhfMzc2XzE4OF82MTEuMDBfMTY4LjBfNTcxLjBfMF8wXzAuMF8wLjBfMjAwXzM1LjAwXzAuMDBfMC4wMF8xOTIzODkx", "sendTypeForPickUpCar": 0, "skuId": 1923891, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 49923, "vendorCode": "82843", "vendorVehicleCode": "20092228"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1911455, "bizVendorCode": "SD62099"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2375079, "bizVendorCode": "SD65454"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1977490, "bizVendorCode": "SD65452"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1862418, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1857298, "bizVendorCode": "SD61937"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1859633, "bizVendorCode": "SD80545"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1861361, "bizVendorCode": "SD64429"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2317172, "bizVendorCode": "SD64662"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1923447, "bizVendorCode": "44444"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5524", "highestPrice": 780, "rCoup": 0, "minDPrice": 168, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 571, "lowestDistance": 0, "group": 870, "sortNum": 21, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD82843_0_115278_115278"], "introduce": "当前车型最低价"}, "minDOrinPrice": 188, "isEasy": true, "isCredit": true, "maximumCommentCount": 2, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "175140114", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 10}, {"groupSort": 1, "lowestPrice": 180, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 360, "detail": [{"code": "1001", "amount": 400, "amountDesc": "¥400", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥180", "originalDailyPrice": 200, "subAmount": 180, "name": "车辆租金", "amountStr": "¥360"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 495, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥535", "subAmount": 535, "name": "总价", "amountStr": "¥495"}], "reference": {"vehicleCode": "0", "rStoreCode": "107380", "pLev": 24251, "comPriceCode": "[c]Mzc4fDE4NTYzLTA1fDIwMjAwOjAtMTcgJjIwMDA6MDAyMDIzJiYxJDE4IDAtMDUtOjAwJjA6MDAmMSR8MjAwJiYyJjIxMDAxMDAkMTAwJjQxJjM1MDAzJjM1LjAuMDAmMDImMjAkMTAxMDAkJjUwJjMtMDV8MjAyMTc6NS0xMSAAAAAAMTozOA==", "bizVendorCode": "SD61671", "pStoreCode": "107380", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTA3MzgwXzUyODJfMV8yMDBfNDAwXzIwMF81MzUuMDBfMTgwLjBfNDk1LjBfMF8wXzAuMF8wLjBfMTAwXzM1LjAwXzAuMDBfMC4wMF8xODU2Mzc4", "sendTypeForPickUpCar": 0, "skuId": 1856378, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24251, "vendorCode": "61671", "vendorVehicleCode": "20068007"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1966252, "bizVendorCode": "SD81827"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1861400, "bizVendorCode": "SD30147"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1973183, "bizVendorCode": "SD30169"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2237585, "bizVendorCode": "SD61937"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1907868, "bizVendorCode": "44444"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5282", "highestPrice": 646, "rCoup": 0, "minDPrice": 180, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 495, "lowestDistance": 0, "group": 870, "sortNum": 16, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD61671_0_107380_107380"], "introduce": "当前车型最低价"}, "minDOrinPrice": 200, "isEasy": true, "isCredit": true, "maximumCommentCount": 13, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "175140111", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 6}, {"groupSort": 4, "lowestPrice": 198, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 460, "detail": [{"code": "1001", "amount": 500, "amountDesc": "¥500", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥230", "originalDailyPrice": 250, "subAmount": 230, "name": "车辆租金", "amountStr": "¥460"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 595, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥635", "subAmount": 635, "name": "总价", "amountStr": "¥595"}], "reference": {"vehicleCode": "0", "rStoreCode": "107380", "pLev": 24251, "comPriceCode": "[c]MjQ3fDE4NTczLTA1fDIwMjAwOjAtMTcgJjI1MDA6MDAyMDIzJiYxJDE4IDAtMDUtOjAwJjA6MDAmMSR8MjUwJiYyJjIxMDAxMDAkMTUwJjUxJjM1MDAzJjM1LjAuMDAmMDImMjAkMTAxMDAkJjUwJjMtMDV8MjAyMTc6NS0xMSAAAAAAMTozOA==", "bizVendorCode": "SD61671", "pStoreCode": "107380", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTA3MzgwXzUyODNfMV8yNTBfNTAwXzI1MF82MzUuMDBfMjMwLjBfNTk1LjBfMF8wXzAuMF8wLjBfMTAwXzM1LjAwXzAuMDBfMC4wMF8xODU3MjQ3", "sendTypeForPickUpCar": 0, "skuId": 1857247, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24251, "vendorCode": "61671", "vendorVehicleCode": "20073109"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1978079, "bizVendorCode": "SD82671"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1977696, "bizVendorCode": "SD62166"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1861024, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2551088, "bizVendorCode": "SD81829"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1970449, "bizVendorCode": "SD61951"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2413169, "bizVendorCode": "SD63457"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1965978, "bizVendorCode": "SD65454"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1977080, "bizVendorCode": "SD82867"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1958849, "bizVendorCode": "SD73871"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1858109, "bizVendorCode": "SD80545"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2036878, "bizVendorCode": "44444"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1915860, "bizVendorCode": "SD77287"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5283", "highestPrice": 868, "rCoup": 0, "minDPrice": 230, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 595, "lowestDistance": 0, "group": 870, "sortNum": 23, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD82671_0_117403_117403"], "introduce": "当前车型最低价"}, "minDOrinPrice": 250, "isEasy": true, "isCredit": true, "maximumCommentCount": 2, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "175140116", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 13}, {"groupSort": 2, "lowestPrice": 288, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 576, "detail": [{"code": "1001", "amount": 616, "amountDesc": "¥616", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥288", "originalDailyPrice": 308, "subAmount": 288, "name": "车辆租金", "amountStr": "¥576"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 200, "amountStr": "¥200", "detail": [{"code": "1002", "amount": 200, "amountDesc": "¥200", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 811, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥851", "subAmount": 851, "name": "总价", "amountStr": "¥811"}], "reference": {"vehicleCode": "0", "rStoreCode": "115278", "pLev": 49923, "comPriceCode": "[c]", "bizVendorCode": "SD82843", "pStoreCode": "115278", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTE1Mjc4XzE3MzkxXzFfMzA4XzYxNl8zMDhfODUxLjAwXzI4OC4wXzgxMS4wXzBfMF8wLjBfMC4wXzIwMF8zNS4wMF8wLjAwXzAuMDBfMjUxMTM4NA==", "sendTypeForPickUpCar": 0, "skuId": 2511384, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 49923, "vendorCode": "82843", "vendorVehicleCode": "20655733"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2428038, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2677428, "bizVendorCode": "SD64429"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "17391", "highestPrice": 968, "rCoup": 0, "minDPrice": 288, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 811, "lowestDistance": 0, "group": 870, "sortNum": 30, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD82843_0_115278_115278"], "introduce": "当前车型最低价"}, "minDOrinPrice": 308, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "175140119", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 3}, {"groupSort": 1, "lowestPrice": 168, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 336, "detail": [{"code": "1001", "amount": 376, "amountDesc": "¥376", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥168", "originalDailyPrice": 188, "subAmount": 168, "name": "车辆租金", "amountStr": "¥336"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 160, "amountStr": "¥160", "detail": [{"code": "1002", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 516, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥556", "subAmount": 556, "name": "总价", "amountStr": "¥516"}], "reference": {"vehicleCode": "0", "rStoreCode": "116115", "pLev": 56333, "comPriceCode": "[c]NDQ0fDE5NjQzLTA1fDIwMjAwOjAtMTcgJjE4ODA6MDAyMDIzJiYxJDE4IDAtMDUtOjAwJjA6MDAmMSR8MTg4JiYyJjExMDAxNzYkMTg4JjMxJjIwMDAzJjIwLjAuMDAmMDImMjAkMTAxNjAkJjgwJjMtMDV8MjAyMTc6NS0xMSAAAAAAMTozOA==", "bizVendorCode": "SD32687", "pStoreCode": "116115", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTE2MTE1XzQ2NjBfMV8xODhfMzc2XzE4OF81NTYuMDBfMTY4LjBfNTE2LjBfMF8wXzAuMF8wLjBfMTYwXzIwLjAwXzAuMDBfMC4wMF8xOTY0NDQ0", "sendTypeForPickUpCar": 0, "skuId": 1964444, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 56333, "vendorCode": "32687", "vendorVehicleCode": "14359"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1856106, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1966310, "bizVendorCode": "SD33419"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1856726, "bizVendorCode": "SD64429"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1914181, "bizVendorCode": "SD61659"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1924766, "bizVendorCode": "SD82843"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "4660", "highestPrice": 868, "rCoup": 0, "minDPrice": 168, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 516, "lowestDistance": 0, "group": 820, "sortNum": 17, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD32687_0_116115_116115"], "introduce": "当前车型最低价"}, "minDOrinPrice": 188, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "175140112", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 6}, {"groupSort": 4, "lowestPrice": 168, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 336, "detail": [{"code": "1001", "amount": 376, "amountDesc": "¥376", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥168", "originalDailyPrice": 188, "subAmount": 168, "name": "车辆租金", "amountStr": "¥336"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 200, "amountStr": "¥200", "detail": [{"code": "1002", "amount": 200, "amountDesc": "¥200", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 571, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥611", "subAmount": 611, "name": "总价", "amountStr": "¥571"}], "reference": {"vehicleCode": "0", "rStoreCode": "115278", "pLev": 49923, "comPriceCode": "[c]OTAyfDE5MTMzLTA1fDIwMjAwOjAtMTcgJjE4ODA6MDAyMDIzJiYxJDE4IDAtMDUtOjAwJjA6MDAmMSR8MTg4JiYyJjExMDAxNzYkMTg4JjMxJjM1MDAzJjM1LjAuMDAmMDImMjAkMTAmMjAwJjEwMDIzLTAkfDIwIDE3OjUtMTE4AAAANTE6Mw==", "bizVendorCode": "SD82843", "pStoreCode": "115278", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTE1Mjc4XzUzNzRfMV8xODhfMzc2XzE4OF82MTEuMDBfMTY4LjBfNTcxLjBfMF8wXzAuMF8wLjBfMjAwXzM1LjAwXzAuMDBfMC4wMF8xOTEzOTAy", "sendTypeForPickUpCar": 0, "skuId": 1913902, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 49923, "vendorCode": "82843", "vendorVehicleCode": "20069034"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1972600, "bizVendorCode": "SD69279"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2511489, "bizVendorCode": "SD62166"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1915619, "bizVendorCode": "SD65451"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1920882, "bizVendorCode": "SD81829"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1972609, "bizVendorCode": "SD65454"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1917311, "bizVendorCode": "SD62099"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1860299, "bizVendorCode": "SD80545"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1887556, "bizVendorCode": "SD64662"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1918557, "bizVendorCode": "SD61659"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1963211, "bizVendorCode": "SD47522"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5374", "highestPrice": 980, "rCoup": 0, "minDPrice": 168, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 571, "lowestDistance": 0, "group": 820, "sortNum": 22, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD82843_0_115278_115278"], "introduce": "当前车型最低价"}, "minDOrinPrice": 188, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "175140115", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 11}, {"groupSort": 3, "lowestPrice": 197, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 416, "detail": [{"code": "1001", "amount": 456, "amountDesc": "¥456", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥208", "originalDailyPrice": 228, "subAmount": 208, "name": "车辆租金", "amountStr": "¥416"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 160, "amountStr": "¥160", "detail": [{"code": "1002", "amount": 160, "amountDesc": "¥160", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 611, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥651", "subAmount": 651, "name": "总价", "amountStr": "¥611"}], "reference": {"vehicleCode": "0", "rStoreCode": "116115", "pLev": 56333, "comPriceCode": "[c]NDgzfDE5NjN8MjAyMC4wMC0xNyAzLTA1MDowMDAwOjAmJjEkJjIyOC0wNS0yMDIzMDowMDE4IDAyMjgmOjAwJjEwMDEmMSR8MjgmNCYyJjIwMDMmNTYkMS4wMCYxJjM1MCQxMDM1LjAmODAmMDImMnwyMDIxNjAkLTExIDMtMDUxOjM4MTc6NQ==", "bizVendorCode": "SD32687", "pStoreCode": "116115", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTE2MTE1XzU1MjZfMV8yMjhfNDU2XzIyOF82NTEuMDBfMjA4LjBfNjExLjBfMF8wXzAuMF8wLjBfMTYwXzM1LjAwXzAuMDBfMC4wMF8xOTYzNDgz", "sendTypeForPickUpCar": 0, "skuId": 1963483, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 56333, "vendorCode": "32687", "vendorVehicleCode": "20104737"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1976036, "bizVendorCode": "SD69279"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1979617, "bizVendorCode": "SD80559"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2678007, "bizVendorCode": "SD78577"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1915069, "bizVendorCode": "SD82843"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1978982, "bizVendorCode": "SD81827"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1911553, "bizVendorCode": "SD62099"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2070598, "bizVendorCode": "SD69280"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 2362568, "bizVendorCode": "SD62119"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5526", "highestPrice": 868, "rCoup": 0, "minDPrice": 208, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 611, "lowestDistance": 0, "group": 820, "sortNum": 26, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD69279_0_116480_116480"], "introduce": "当前车型最低价"}, "minDOrinPrice": 228, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "175140117", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 9}, {"groupSort": 5, "lowestPrice": 210, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 420, "detail": [{"code": "1001", "amount": 460, "amountDesc": "¥460", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥210", "originalDailyPrice": 230, "subAmount": 210, "name": "车辆租金", "amountStr": "¥420"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 555, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥595", "subAmount": 595, "name": "总价", "amountStr": "¥555"}], "reference": {"vehicleCode": "0", "rStoreCode": "107380", "pLev": 24251, "comPriceCode": "[c]Mzc5fDE4NTZ8MjAyMC4xNS0xNyAzLTA1MDowMDAwOjAmJjEkJjIzMC0wNS0yMDIzMDowMDE4IDAyMzAmOjAwJjEwMDEmMSR8MzAmNCYyJjIwMDMmNjAkMS4wMCYxJjM1MCQxMDM1LjAmNTAmMDImMnwyMDIxMDAkLTExIDMtMDUxOjM4MTc6NQ==", "bizVendorCode": "SD61671", "pStoreCode": "107380", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3MzgwXzUyODFfMV8yMzBfNDYwXzIzMF81OTUuMDBfMjEwLjBfNTU1LjBfMF8wXzAuMF8wLjBfMTAwXzM1LjAwXzAuMDBfMC4wMF8xODU2Mzc5", "sendTypeForPickUpCar": 0, "skuId": 1856379, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 24251, "vendorCode": "61671", "vendorVehicleCode": "20068008"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1858159, "bizVendorCode": "SD32498"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2428595, "bizVendorCode": "SD81829"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1974468, "bizVendorCode": "SD79797"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1914334, "bizVendorCode": "SD65451"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1922192, "bizVendorCode": "SD61659"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "5281", "highestPrice": 979, "rCoup": 0, "minDPrice": 210, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 555, "lowestDistance": 0, "group": 820, "sortNum": 20, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD61671_0_107380_107380"], "introduce": "当前车型最低价"}, "minDOrinPrice": 230, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "175140113", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 6}, {"groupSort": 6, "lowestPrice": 280, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 560, "detail": [{"code": "1001", "amount": 600, "amountDesc": "¥600", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥280", "originalDailyPrice": 300, "subAmount": 280, "name": "车辆租金", "amountStr": "¥560"}, {"code": "CAR_SERVICE_FEE", "amount": 25, "amountStr": "¥25", "detail": [{"code": "1003", "amount": 25, "amountDesc": "¥25", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "amount": 100, "amountDesc": "¥100", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 685, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥725", "subAmount": 725, "name": "总价", "amountStr": "¥685"}], "reference": {"vehicleCode": "0", "rStoreCode": "116351", "pLev": 58387, "comPriceCode": "[c]MDc4fDE5NjUzLTA1fDIwMjAwOjAtMTcgJjMwMDA6MDAyMDIzJiYxJDE4IDAtMDUtOjAwJjA6MDAmMSR8MzAwJiYyJjMxMDAxMDAkMTAwJjYxJjI1MDAzJjI1LjAuMDAmMDImMjAkMTAxMDAkJjUwJjMtMDV8MjAyMTc6NS0xMSAAAAAAMTozOA==", "bizVendorCode": "SD65454", "pStoreCode": "116351", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTE2MzUxXzQ2NTlfMV8zMDBfNjAwXzMwMF83MjUuMDBfMjgwLjBfNjg1LjBfMF8wXzAuMF8wLjBfMTAwXzI1LjAwXzAuMDBfMC4wMF8xOTY1MDc4", "sendTypeForPickUpCar": 0, "skuId": 1965078, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 58387, "vendorCode": "65454", "vendorVehicleCode": "20003271"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1856096, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 1910526, "bizVendorCode": "44444"}}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1816676, "bizVendorCode": "SD64662"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "4659", "highestPrice": 568, "rCoup": 0, "minDPrice": 280, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 685, "lowestDistance": 0, "group": 820, "sortNum": 28, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["SD65454_0_116351_116351"], "introduce": "当前车型最低价"}, "minDOrinPrice": 300, "isEasy": true, "isCredit": true, "maximumCommentCount": 2, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "175140118", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 4}, {"groupSort": 2, "lowestPrice": 438, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 876, "detail": [{"code": "1001", "amount": 916, "amountDesc": "¥916", "name": "租车费"}, {"code": "11037", "amount": 40, "amountDesc": "¥40", "name": "优惠券"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥438", "originalDailyPrice": 458, "subAmount": 438, "name": "车辆租金", "amountStr": "¥876"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 170, "amountStr": "¥170", "detail": [{"code": "1002", "amount": 170, "amountDesc": "¥170", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 1081, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥1121", "subAmount": 1121, "name": "总价", "amountStr": "¥1081"}], "reference": {"vehicleCode": "0", "rStoreCode": "114768", "pLev": 45746, "comPriceCode": "[c]", "bizVendorCode": "SD69287", "pStoreCode": "114768", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTE0NzY4XzE3MzkwXzFfNDU4XzkxNl80NThfMTEyMS4wMF80MzguMF8xMDgxLjBfMF8wXzAuMF8wLjBfMTcwXzM1LjAwXzAuMDBfMC4wMF8yNTExNTA0", "sendTypeForPickUpCar": 0, "skuId": 2511504, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 45746, "vendorCode": "69287", "vendorVehicleCode": "20655751"}, "isMinTPriceVendor": true}, {"reference": {"vehicleCode": "0", "packageType": 0, "skuId": 1942892, "bizVendorCode": "SD32231"}}, {"reference": {"vehicleCode": "0", "packageType": 1, "skuId": 2375322, "bizVendorCode": "SD80559"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "已减40", "groupCode": "MarketGroup1317", "code": "30", "title": "券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3769"}, "vehicleCode": "17390", "highestPrice": 468, "rCoup": 0, "minDPrice": 438, "pWay": "可选:店员免费上门送取车", "hot": 0, "minTPrice": 1081, "lowestDistance": 0, "group": 820, "sortNum": 32, "maximumRating": 0, "vehicleRecommendProduct": {"productCodes": ["SD69287_0_114768_114768"], "introduce": "当前车型最低价"}, "minDOrinPrice": 458, "isEasy": true, "isCredit": true, "maximumCommentCount": 0, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 3}], "groupCode": "all", "dailyPrice": 78, "hasResult": true}, {"sortNum": 0, "groupName": "经济轿车", "hasResult": true, "groupCode": "2", "dailyPrice": 78}, {"sortNum": 2, "groupName": "新能源", "hasResult": true, "groupCode": "newenergy", "dailyPrice": 78}, {"sortNum": 3, "groupName": "舒适轿车", "hasResult": true, "groupCode": "3", "dailyPrice": 78}, {"sortNum": 4, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 162}, {"sortNum": 5, "groupName": "商务车", "hasResult": true, "groupCode": "4", "dailyPrice": 180}, {"sortNum": 6, "groupName": "豪华轿车", "hasResult": false, "groupCode": "5"}, {"sortNum": 7, "groupName": "跑车", "hasResult": true, "groupCode": "9", "dailyPrice": 1379}, {"sortNum": 8, "groupName": "房车", "hasResult": false, "groupCode": "11"}, {"sortNum": 9, "groupName": "小巴士", "hasResult": false, "groupCode": "7"}, {"sortNum": 10, "groupName": "皮卡", "hasResult": false, "groupCode": "10"}], "filters": ["HotBrand_别克", "BrandGroup_b0_别克", "BrandGroup_x0_雪佛兰"], "labelCodes": ["3662", "3563", "3836", "3510", "3696", "3697", "3698", "3731", "3679", "3650", "3495", "3494", "3705", "3504", "3548", "3827", "3547", "3503", "3502", "3501", "3709", "3788", "3509", "3789", "3746", "3769"], "quickFilter": [{"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 1, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "5", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "6", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "9", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "18", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "携程优选", "icon": ""}], "promotMap": {"667234322": "5tAD8MxSohZ4Kll2rVMXkQ=="}, "requestInfo": {"rLongitude": 109.41235, "rDate": "20230519183000", "age": 30, "returnDate": "/Date(1684492200000+0800)/", "sourceCountryId": 1, "pLatitude": 18.30767, "rLatitude": 18.30767, "pLongitude": 109.41235, "pDate": "20230517183000", "pickupLocationName": "凤凰国际机场T1航站楼", "returnLocationName": "凤凰国际机场T1航站楼", "pickupDate": "/Date(1684319400000+0800)/"}, "allVehicleCount": 29, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"filterCode": "Vendor_0"}, "vehicleList": [{"transmissionName": "自动挡", "displacement": "1.0T-1.5L", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "isHot": false, "imageList": ["https://pages.c-ctrip.com/carisd/app/14415.jpg"], "carPhone": true, "vehicleCode": "4139", "autoStart": false, "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "brandName": "雪佛兰", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "2", "endurance": "续航100km", "luggageNum": "可放2个24寸行李箱", "luggageNo": 2, "brandEName": "雪佛兰", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false, "vehiclesSetId": "64"}, {"subGroupCode": "newenergy", "luggageNo": 2, "endurance": "续航100km", "fuelType": "纯电动", "charge": "快充0.67小时,慢充8小时", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "isSpecialized": true, "groupCode": "3", "zhName": "别克微蓝6", "doorNo": 5, "driveMode": "前置前驱", "carPhone": true, "vehicleCode": "5177", "style": "", "name": "别克微蓝6", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "fuel": "", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "transmissionType": 1, "brandName": "别克", "oilType": 5, "struct": "", "groupName": "舒适轿车", "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "83", "transmissionName": "自动挡"}, {"subGroupCode": "newenergy", "luggageNo": 2, "displacement": "1.5L", "endurance": "续航100km", "fuelType": "插电式", "charge": "慢充2.6小时", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "别克微蓝6", "doorNo": 5, "driveMode": "前置前驱", "carPhone": true, "vehicleCode": "5289", "style": "", "name": "别克微蓝6", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "transmissionType": 1, "brandName": "别克", "oilType": 4, "struct": "", "groupName": "经济轿车", "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "66", "transmissionName": "自动挡"}, {"transmissionName": "自动挡", "displacement": "1.0T-1.5L", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "vehicleCode": "4101", "fuel": "92号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": false, "name": "别克英朗", "zhName": "别克英朗", "brandName": "别克", "passengerNo": 5, "doorNo": 4, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "2", "endurance": "续航100km", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "别克", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "license": "", "autoPark": false, "vehiclesSetId": "64"}, {"transmissionName": "自动挡", "displacement": "2.0T", "style": "2020款", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz02cn152vdxza6qo25EE.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "5282", "carPhone": true, "autoStart": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "别克GL8", "zhName": "别克GL8", "brandName": "别克", "passengerNo": 7, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "4", "endurance": "续航100km", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "别克", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "license": "", "autoPark": false, "vehiclesSetId": "44"}, {"transmissionName": "自动挡", "displacement": "2.0T", "style": "2017/18款", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "4660", "carPhone": true, "autoStart": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "brandName": "别克", "passengerNo": 7, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "4", "endurance": "续航100km", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "别克", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "license": "", "vehiclesSetId": "44"}, {"transmissionName": "自动挡", "displacement": "2.0T", "style": "2020款", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "5281", "carPhone": true, "autoStart": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "brandName": "别克", "passengerNo": 7, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "4", "endurance": "续航100km", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "别克", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "license": "", "vehiclesSetId": "44"}, {"transmissionName": "自动挡", "displacement": "2.0T", "style": "2022款", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "5524", "carPhone": true, "autoStart": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "别克GL8", "zhName": "别克GL8", "brandName": "别克", "passengerNo": 7, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "4", "endurance": "续航100km", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "别克", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "license": "", "autoPark": false, "vehiclesSetId": "44"}, {"transmissionName": "自动挡", "displacement": "2.0T", "style": "2021款", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "5374", "carPhone": true, "autoStart": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "brandName": "别克", "passengerNo": 7, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "4", "endurance": "续航100km", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "别克", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "license": "", "vehiclesSetId": "44"}, {"transmissionName": "自动挡", "displacement": "2.0T", "style": "2021款", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "5283", "carPhone": true, "autoStart": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "别克GL8", "zhName": "别克GL8", "brandName": "别克", "passengerNo": 7, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "4", "endurance": "续航100km", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "别克", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "license": "", "autoPark": false, "vehiclesSetId": "44"}, {"transmissionName": "自动挡", "displacement": "2.0T", "style": "2022款", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "5526", "carPhone": true, "autoStart": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "brandName": "别克", "passengerNo": 7, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "4", "endurance": "续航100km", "luggageNum": "可放3个24寸行李箱", "luggageNo": 3, "brandEName": "别克", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "license": "", "vehiclesSetId": "44"}, {"luggageNo": 3, "displacement": "2.5L", "autoPark": false, "endurance": "续航100km", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "driveMode": "前置前驱", "carPhone": true, "vehicleCode": "4659", "style": "2017/18款", "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放3个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "", "groupName": "商务车", "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "44", "transmissionName": "自动挡"}, {"luggageNo": 1, "displacement": "2.0T", "autoPark": false, "endurance": "续航100km", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8", "doorNo": 5, "driveMode": "前置前驱", "carPhone": true, "vehicleCode": "17391", "style": "2023款", "name": "别克GL8", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放1个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "", "groupName": "商务车", "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "44", "transmissionName": "自动挡"}, {"transmissionName": "自动挡", "displacement": "2.0T", "style": "2023款", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "vehicleCode": "17390", "carPhone": true, "autoStart": true, "fuel": "95号", "licenseStyle": "2", "oilType": 3, "driveMode": "前置前驱", "autoBackUp": true, "name": "别克GL8豪华版", "zhName": "别克GL8豪华版", "brandName": "别克", "passengerNo": 7, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "groupCode": "4", "endurance": "续航100km", "luggageNum": "可放1个24寸行李箱", "luggageNo": 1, "brandEName": "别克", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "license": "", "vehiclesSetId": "44"}], "storeList": [{"pickOffLevel": 23665, "storeCode": "106896", "pickUpLevel": 23665}, {"pickOffLevel": 58387, "storeCode": "116351", "pickUpLevel": 58387}, {"pickOffLevel": 24251, "storeCode": "107380", "pickUpLevel": 24251}, {"pickOffLevel": 22341, "storeCode": "106878", "pickUpLevel": 22341}, {"pickOffLevel": 56333, "storeCode": "116115", "pickUpLevel": 56333}, {"pickOffLevel": 49923, "storeCode": "115278", "pickUpLevel": 49923}, {"pickOffLevel": 45746, "storeCode": "114768", "pickUpLevel": 45746}], "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": false, "isLastPage": false, "uniqSign": "12001145310000201410a7FZEM48926xU8NCazFb", "pHub": 0, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"sortNum": 1, "showLayer": 1, "title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "subTitle": "*国定节假日以页面披露为准"}, {"sortNum": 2, "showLayer": 0, "title": "安心保障", "titleExtra": "(需加购优享服务)", "type": 1, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "subTitle": "*覆盖损失范围以预订页面内披露为准"}, {"sortNum": 3, "title": "优质车况", "subTitle": "", "type": 1, "description": "3年内车龄", "showLayer": 0}, {"sortNum": 10, "title": "满油取车", "subTitle": "", "type": 0, "description": "保障取车时油量满（不含纯电车）", "showLayer": 0}]}, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "6986905865304203928", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a826d64-467721-78273", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1683798727068+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 448, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 448, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1683798726522, "afterFetch": 1683798726970, "hasRetry": false}}