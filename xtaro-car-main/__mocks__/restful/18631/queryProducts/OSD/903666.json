{"baseResponse": {"isSuccess": false, "code": "200", "returnMsg": "success", "requestId": "24f223ea-2627-4214-8e1e-2dec46bb4da0", "extMap": {}, "extraIndexTags": {"rCityId": "347", "pcName": "美国", "rcId": "66", "pcId": "66", "pCityId": "347", "pCityName": "洛杉矶", "rcName": "美国", "rCityName": "洛杉矶"}, "apiResCodes": [], "hasResult": false}, "requestInfo": {"pickupDate": "2024-06-04 10:00:00", "pickupLocationName": "怀特曼机场", "returnDate": "2024-06-30 10:00:00", "returnLocationName": "怀特曼机场", "sourceCountryId": 1, "age": 99, "pLatitude": 34.26048, "rLatitude": 34.26048, "rLongitude": -118.412426, "pLongitude": -118.412426, "pDate": "20240604100000", "rDate": "20240630100000", "pCityId": 347, "rCityId": 347}, "productGroupsHashCode": "37a6259cc0c1dae299a7866489dff0bd2be88ca4242c76e8253ac62474851065032d6833", "needRetry": false, "recommendInfo": {"promptTitle": "暂无符合要求的车辆", "promptSubTitle": "建议您修改取还车条件", "buttonTitle": "修改取还车条件", "errorCode": "unknown", "type": 3}, "commNotices": [], "basicData": {"sortItems": [{"title": "推荐排序", "type": 1, "code": "1", "sortNum": 1}, {"title": "价格 低→高", "type": 2, "code": "2", "sortNum": 2}], "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}}, "similarVehicleIntroduce": {"introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}, "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "cases": [{"vehicleGroupCode": "default", "vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含"}, {"vehicleGroupCode": "D", "vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特"}, {"vehicleGroupCode": "S", "vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特"}, {"vehicleGroupCode": "R", "vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者"}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "isAll": true, "isFromSearch": true, "uniqSign": "24f223ea-2627-4214-8e1e-2dec46bb4da0", "extras": {"abVersion": "231219_DSJT_Crecv|A", "hasListSign": "8Lti2JDm"}}