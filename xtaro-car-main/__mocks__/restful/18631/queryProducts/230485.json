{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "b6714ba9-1d33-48df-9e84-805dcccf3865", "extMap": {"runAsyncCost_2": "0.0", "checkRentCenter_2": "0.0", "apiCost": "686.0", "mergeGroup_9": "0.0", "contextBuilderCost_3": "52.0", "restOriginalCost": "802.0", "pageName": "List", "ubtProcessCost_8": "1.0", "initBaseData_1": "52.0", "calculatePreAuth_3": "0.0", "mergeGroupSize_9": "7", "uid": "17200000003", "mergeGroup_newenergy": "0.0", "allCost": "802.0", "end": "2023-03-28 11:10:09", "restCost": "0", "dataConvertResCost": "115.0", "usePage": "1", "totalCostTime": "819", "dropoffCityId": "43", "IncludeFeesCost": "0.0", "start": "2023-03-28 11:10:08", "shoppingCost_1": "686.0", "mergeGroupSize_2": "3", "mergeGroupSize_newenergy": "5", "mergeGroupSize_5": "3", "mergeGroupSize_6": "2", "mergeGroupSize_3": "5", "gsCost": "1.0", "mergeGroupSize_4": "7", "buildInfoCost_2": "33.0", "buildInfoCost_3": "1.0", "buildInfoCost_4": "34.0", "setProductGroupsHashCodeCostAffect": "0.0", "buildInfoCost_1": "0.0", "productGroupCost_6": "2.0", "mergeGroup_6": "0.0", "lastInfoCost_7": "19.0", "originalCode": "200", "mergeGroup_5": "0.0", "mergeGroup_4": "0.0", "mergeGroup_3": "1.0", "mergeGroup_2": "0.0", "pickupCityId": "43"}, "apiResCodes": [], "hasResult": true, "errorCode": "0", "message": ""}, "ResponseStatus": {"Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "7176139287278467250"}, {"Id": "RootMessageId", "Value": "921822-0a8065ad-466659-12210"}]}, "requestInfo": {"pickupDate": "2023-03-28 15:30:00", "pickupLocationName": "凤凰国际机场", "returnDate": "2023-03-30 15:30:00", "returnLocationName": "凤凰国际机场", "sourceCountryId": 1, "age": 30, "pLatitude": 18.306675, "rLatitude": 18.306675, "rLongitude": 109.426847, "pLongitude": 109.426847, "pDate": "20230328153000", "rDate": "20230330153000"}, "allVehicleCount": 27, "allVendorPriceCount": 27, "filterMenuItems": [{"name": "快速选车", "code": "QuickChoose", "sortNum": 1.0, "hierarchy": 1, "filterGroups": [{"name": "车龄", "sortNum": 1, "groupCode": "CarAge", "bitwiseType": 2, "filterItems": [{"itemCode": "CarAge_3509", "name": "半年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "5"}, {"itemCode": "CarAge_3510", "name": "一年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "5"}, {"itemCode": "CarAge_3547", "name": "两年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 3, "positionCode": "5"}], "shortName": "车龄"}, {"name": "座位数", "sortNum": 2, "groupCode": "SeatGroup", "bitwiseType": 2, "filterItems": [{"itemCode": "SeatGroup_1", "name": "2座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "SeatGroup_2", "name": "4座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 1, "isQuickItem": false}, {"itemCode": "SeatGroup_3", "name": "5座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": false}, {"itemCode": "SeatGroup_4", "name": "6座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 1, "isQuickItem": false}, {"itemCode": "SeatGroup_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "10"}, {"itemCode": "SeatGroup_6", "name": "8座及以上", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 1, "isQuickItem": false}], "shortName": "座位", "isSupportMulti": true}, {"name": "车辆排挡", "sortNum": 3, "groupCode": "Transmission", "bitwiseType": 2, "filterItems": [{"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 2, "isQuickItem": true, "quickSortNum": 1, "positionCode": "7"}, {"itemCode": "Transmission_2", "name": "手动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}], "shortName": "排挡"}, {"name": "能源类型", "sortNum": 4, "groupCode": "NewEnergy", "bitwiseType": 2, "filterItems": [{"itemCode": "NewEnergy_elect", "name": "纯电动", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "NewEnergy_mix", "name": "新能源混动", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "NewEnergy_gas", "name": "汽油", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}], "shortName": "能源类型", "isSupportMulti": true}, {"name": "车辆配置", "sortNum": 5, "groupCode": "VehicleAccessory", "bitwiseType": 1, "filterItems": [{"itemCode": "VehicleAccessory_tachograph", "name": "行车记录仪", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 64, "sortNum": 4, "isQuickItem": true, "quickSortNum": 8, "positionCode": "6"}, {"itemCode": "VehicleAccessory_LeatherSeat", "name": "真皮座椅", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 256, "sortNum": 6, "isQuickItem": false}], "shortName": "车辆配置", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 6, "groupCode": "HotBrand", "bitwiseType": 2, "filterItems": [{"itemCode": "HotBrand_奥迪", "name": "奥迪", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"itemCode": "HotBrand_别克", "name": "别克", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "HotBrand_奔驰", "name": "奔驰", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "HotBrand_宝马", "name": "宝马", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "HotB<PERSON>_本田", "name": "本田", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"itemCode": "HotBrand_大众", "name": "大众", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"itemCode": "HotBrand_丰田", "name": "丰田", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}], "shortName": "热门品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_a0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_a0_奥迪", "name": "奥迪", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_b0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_b0_保时捷", "name": "保时捷", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"itemCode": "BrandGroup_b0_别克", "name": "别克", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "BrandGroup_b0_奔驰", "name": "奔驰", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "BrandGroup_b0_宝马", "name": "宝马", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "BrandGroup_b0_本田", "name": "本田", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_d0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_d0_东风风光", "name": "东风风光", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengguang.png"}, {"itemCode": "BrandGroup_d0_大众", "name": "大众", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_f0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_f0_丰田", "name": "丰田", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "BrandGroup_f0_福特", "name": "福特", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_g0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_g0_广汽传祺", "name": "广汽传祺", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_m0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_m0_MINI", "name": "MINI", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_q0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_q0_起亚", "name": "起亚", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_r0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_r0_荣威", "name": "荣威", "groupCode": "BrandGroup_r0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_t0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_t0_特斯拉", "name": "特斯拉", "groupCode": "BrandGroup_t0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_x0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_x0_雪佛兰", "name": "雪佛兰", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "shortName": "全部品牌", "isSupportMulti": true}]}, {"name": "更多筛选", "code": "MoreC<PERSON>ose", "sortNum": 2.0, "hierarchy": 1, "filterGroups": [{"name": "价格", "sortNum": 1, "groupCode": "Price", "filterItems": [{"itemCode": "Price_0-50", "name": "¥50以下", "code": "0-50", "groupCode": "Price", "sortNum": 1}, {"itemCode": "Price_50-100", "name": "¥50-100", "code": "50-100", "groupCode": "Price", "sortNum": 2}, {"itemCode": "Price_100-200", "name": "¥100-200", "code": "100-200", "groupCode": "Price", "sortNum": 3}, {"itemCode": "Price_200-99999", "name": "¥200以上", "code": "200-99999", "groupCode": "Price", "sortNum": 4}], "shortName": "价格"}, {"name": "取车方式", "sortNum": 2, "groupCode": "PickReturn", "bitwiseType": 2, "filterItems": [{"itemCode": "PickReturn_StationPR", "name": "站内取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "mark": "15分钟内", "positionCode": "1"}], "shortName": "取还方式", "isSupportMulti": true}, {"name": "门店服务", "sortNum": 3, "groupCode": "StoreService", "bitwiseType": 1, "filterItems": [{"itemCode": "StoreService_easyLife", "name": "无忧租", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 4, "positionCode": "3"}, {"itemCode": "StoreService_FreeDepositAllCtrip", "name": "押金双免", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 8, "sortNum": 4, "isQuickItem": true, "quickSortNum": 3, "positionCode": "3"}, {"itemCode": "StoreService_Unlimit", "name": "不限里程", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 5, "isQuickItem": false}, {"itemCode": "StoreService_FreeCancel", "name": "免费取消", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 32, "sortNum": 6, "isQuickItem": false}], "shortName": "门店服务", "isSupportMulti": true}, {"name": "优惠活动", "sortNum": 4, "groupCode": "Promotion", "bitwiseType": 2, "filterItems": [{"itemCode": "Promotion_3744", "name": "618旅游节", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}, {"itemCode": "Promotion_3814", "name": "周边游特惠", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false}], "shortName": "优惠活动", "isSupportMulti": true}, {"name": "取车证件", "sortNum": 5, "groupCode": "Ceritificate", "bitwiseType": 2, "filterItems": [{"itemCode": "Ceritificate_1", "name": "身份证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_2", "name": "护照", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_7", "name": "回乡证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_8", "name": "台胞证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 3, "isQuickItem": false}], "shortName": "取车证件", "isSupportMulti": true}, {"name": "驾驶员驾龄", "sortNum": 6, "groupCode": "DriveAge", "bitwiseType": 2, "filterItems": [{"itemCode": "DriveAge_1", "name": "不满6个月", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}], "shortName": "驾驶员驾龄"}, {"name": "门店评分", "sortNum": 7, "groupCode": "Comment", "bitwiseType": 2, "filterItems": [{"itemCode": "Comment_4.8", "name": "4.8分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "9"}, {"itemCode": "Comment_4.5", "name": "4.5分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Comment_4.0", "name": "4.0分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}], "shortName": "门店评分"}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_0", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_0", "name": "携程租车中心", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 7, "isQuickItem": false}, {"itemCode": "Vendor_-1", "name": "携程优选", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": true, "quickSortNum": 1, "icon": "", "positionCode": "18"}, {"itemCode": "Vendor_30164", "name": "懒人行卡拉比", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}]}], "quickFilter": [{"itemCode": "PickReturn_StationPR", "name": "站内取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "mark": "15分钟内", "positionCode": "1"}, {"itemCode": "StoreService_FreeDepositAllCtrip", "name": "押金双免", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 8, "sortNum": 4, "isQuickItem": true, "quickSortNum": 3, "positionCode": "3"}, {"itemCode": "CarAge_3509", "name": "半年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "5"}, {"itemCode": "VehicleAccessory_tachograph", "name": "行车记录仪", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 64, "sortNum": 4, "isQuickItem": true, "quickSortNum": 8, "positionCode": "6"}, {"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 2, "isQuickItem": true, "quickSortNum": 1, "positionCode": "7"}, {"itemCode": "Comment_4.8", "name": "4.8分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "9"}, {"itemCode": "SeatGroup_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "10"}, {"itemCode": "Vendor_-1", "name": "携程优选", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": true, "quickSortNum": 1, "icon": "", "positionCode": "18"}], "vehicleList": [{"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "4139", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.0T", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "endurance": "续航100km", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vr": "", "vehiclesSetId": "64"}, {"brandEName": "大众", "brandName": "大众", "name": "大众宝来", "zhName": "大众宝来", "vehicleCode": "4067", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5L", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "endurance": "续航100km", "autoPark": false, "vr": "", "vehiclesSetId": "64"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田凯美瑞", "zhName": "丰田凯美瑞", "vehicleCode": "1445", "groupCode": "3", "groupSubClassCode": "", "groupName": "舒适轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "2.5L", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "endurance": "续航100km", "vr": "", "vehiclesSetId": "81"}, {"brandEName": "丰田", "brandName": "丰田", "name": "丰田赛那SIENNA", "zhName": "丰田赛那SIENNA", "vehicleCode": "5504", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 3, "displacement": "2.5L", "fuel": "92号", "driveMode": "前置前驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 4, "luggageNum": "可放3个24寸行李箱", "endurance": "续航100km", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "vr": "", "vehiclesSetId": "46"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "5283", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 3, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2021款", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "endurance": "续航100km", "autoPark": false, "carPhone": true, "autoStart": true, "vr": "", "vehiclesSetId": "44"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "5282", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 3, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2020款", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "endurance": "续航100km", "autoPark": false, "carPhone": true, "autoStart": true, "vr": "", "vehiclesSetId": "44"}, {"brandEName": "东风风光", "brandName": "东风风光", "name": "东风风光风光E3", "zhName": "东风风光风光E3", "vehicleCode": "5208", "groupCode": "6", "groupSubClassCode": "", "groupName": "SUV", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 5, "luggageNo": 1, "fuel": "92号", "driveMode": "后置后驱", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 4, "fuelType": "增程式", "luggageNum": "可放1个24寸行李箱", "endurance": "续航100km", "charge": "慢充3小时", "autoPark": false, "carPhone": true, "autoStart": true, "autoBackUp": true, "subGroupCode": "newenergy", "vr": "", "vehiclesSetId": "11"}, {"brandEName": "别克", "brandName": "别克", "name": "别克GL8", "zhName": "别克GL8", "vehicleCode": "5524", "groupCode": "4", "groupSubClassCode": "", "groupName": "商务车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 7, "doorNo": 5, "luggageNo": 3, "displacement": "2.0T", "fuel": "95号", "driveMode": "前置前驱", "style": "2022款", "imageList": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg"], "license": "", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com/images/04167120008csw5do5153.jpg", "oilType": 3, "luggageNum": "可放3个24寸行李箱", "endurance": "续航100km", "autoPark": false, "carPhone": true, "autoStart": true, "vr": "", "vehiclesSetId": "44"}], "productGroups": [{"groupCode": "all", "groupName": "全部车型", "sortNum": -4, "productList": [{"vehicleCode": "4139", "sortNum": 0, "lowestPrice": 52.0, "highestPrice": 52.0, "maximumRating": 0.0, "maximumCommentCount": 1, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "44444", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]NDIyfDE5MTJ8MjAyMC4wMC0yOCAzLTAzMDowMDAwOjAmMSQyJjU4JjAzLTIwMjMtOjAwOjkgMDA4JiYxMDAmNTAxJjIkfDEwMTE2JCY1OCYmMSYyMTAwMyYyMC4wLjAwMDAyJjAwJDEmNjAkMiYzMDMtMDN8MjAyMTE6MS0yOCAAAAAAMDowOA==", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDv9YRZGhKVmIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZLwMN3VcLIANdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThkZROc17UIqcY6gkB/DPTQhj4CrOI1G42DQu6ZHpEKDotve49xdKAs2cI9Iks7Tq6pZpEoTnxeD4h9Z91JwCbzCX4u/eBFfWNaA3FdkEq28T6gXdb6UdOq8QWtWQNdQzoiF6u6TIaDPrBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZOik1grn40YHdcJ84KynmbqN8reTen9/Q32aYqXytD2QQ==", "vendorVehicleCode": "20037374", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1912422, "pLev": 40859, "rLev": 40859, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 103.0, "amountStr": "¥103", "subAmount": 52.0, "subAmountStr": "日均¥52", "originalDailyPrice": 58, "detail": [{"code": "1001", "name": "租车费", "amount": 116, "amountDesc": "¥116"}, {"code": "3814", "name": "周边游特惠", "amount": 13, "amountDesc": "¥13"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "name": "基础服务费", "amount": 60, "amountDesc": "¥60", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 183.0, "amountStr": "¥183", "subAmount": 196.0, "subAmountStr": "¥196", "currencyCode": "¥"}]}], "reactId": "1110096400", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["44444_0_114043_114043"]}, "minTPrice": 183.0, "minDPrice": 52.0, "modifySameVehicle": false, "minDOrinPrice": 58, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减13", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "4067", "sortNum": 1, "lowestPrice": 69.0, "highestPrice": 69.0, "maximumRating": 0.0, "maximumCommentCount": 1, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "44444", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]OTA4fDE5MDd8MjAyMC4wMC0yOCAzLTAzMDowMDAwOjAmMSQyJjc4JjAzLTIwMjMtOjAwOjkgMDA4JiYxMDAmNzAxJjIkfDEwMTU2JCY3OCYmMSYzMTAwMyYzNS41LjAwMDAyJjAwJDEmODAkMiY0MDMtMDN8MjAyMTE6MS0yOCAAAAAAMDowOA==", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDLwESTycy5iYxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/CJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThEh3qML5+OVX+jQv42zvRYRj4CrOI1G424Ix1GqsjQU8tve49xdKAs9sFeXkr4HYEpZpEoTnxeD7ynuDgNGWrRCX4u/eBFfWNaA3FdkEq28QS18foxW6zT/QX5rSrUWB0XT9CDLOp2s/BzC0NdlQOxxFQP+5aEs5JtS63iTdzmZMy8y6g7mJRCUM102EtyyLQN8reTen9/Q32aYqXytD2QQ==", "vendorVehicleCode": "20034886", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1907908, "pLev": 40859, "rLev": 40859, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 138.0, "amountStr": "¥138", "subAmount": 69.0, "subAmountStr": "日均¥69", "originalDailyPrice": 78, "detail": [{"code": "1001", "name": "租车费", "amount": 156, "amountDesc": "¥156"}, {"code": "3814", "name": "周边游特惠", "amount": 18, "amountDesc": "¥18"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 253.0, "amountStr": "¥253", "subAmount": 271.0, "subAmountStr": "¥271", "currencyCode": "¥"}]}], "reactId": "1110096401", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["44444_0_114043_114043"]}, "minTPrice": 253.0, "minDPrice": 69.0, "modifySameVehicle": false, "minDOrinPrice": 78, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减18", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "1445", "sortNum": 2, "lowestPrice": 159.0, "highestPrice": 159.0, "maximumRating": 0.0, "maximumCommentCount": 1, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "44444", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]", "priceVersion": "AWEB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXBW8xkjRl2FkIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/Cndj5iZBwSExPsJyD7vyOMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dpk3CpFVjFEZIViTVf+KyZDHzKGEUwz6b/RrGj9xfIHE6Oolt9wEotx1sUaQ936ig1DQfPbwnHXoDIaInF/Q+GrMCe5LaKnV16oYsFjEE8NoDs7sx5Ik0exKTyAiFGStBLBxoSLkNe2Hn71LojuJBTTe8UAj+dITaXCNnci0raRuu+wLsrULIqJQEjAaDK6eD9HNZhvI1KABrsQTiO+nB0E", "vendorVehicleCode": "20011580", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1913095, "pLev": 40859, "rLev": 40859, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 318.0, "amountStr": "¥318", "subAmount": 159.0, "subAmountStr": "日均¥159", "originalDailyPrice": 398, "detail": [{"code": "1001", "name": "租车费", "amount": 796, "amountDesc": "¥796"}, {"code": "3744", "name": "618旅游节", "amount": 478, "amountDesc": "¥478"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 200, "amountStr": "¥200", "detail": [{"code": "1002", "name": "基础服务费", "amount": 200, "amountDesc": "¥200", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 553.0, "amountStr": "¥553", "subAmount": 1031.0, "subAmountStr": "¥1031", "currencyCode": "¥"}]}], "reactId": "1110096400", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["44444_0_114043_114043"]}, "minTPrice": 553.0, "minDPrice": 159.0, "modifySameVehicle": false, "minDOrinPrice": 398, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "618旅游节", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3744", "groupCode": "MarketGroup1346", "amountTitle": "已减478", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5504", "sortNum": 3, "lowestPrice": 167.0, "highestPrice": 167.0, "maximumRating": 0.0, "maximumCommentCount": 1, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "44444", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]", "priceVersion": "AWEB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXBqZVXw8ktSD4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/Cndj5iZBwSExPsJyD7vyOMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dr242sGN6b6YoViTVf+KyZDG5Vqyrdqy0bRrGj9xfIHE1IeuwnDC+4c1sUaQ936ig1DQfPbwnHXoFUC59uVIb6cMCe5LaKnV16TA27N9/+LMDs7sx5Ik0exZAprLkOQtlnBxoSLkNe2Hn71LojuJBTTe8UAj+dITaXCNnci0raRuuThTLhUGKFoQEjAaDK6eD9HNZhvI1KABrsQTiO+nB0E", "vendorVehicleCode": "20087485", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1911747, "pLev": 40859, "rLev": 40859, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 334.0, "amountStr": "¥334", "subAmount": 167.0, "subAmountStr": "日均¥167", "originalDailyPrice": 418, "detail": [{"code": "1001", "name": "租车费", "amount": 836, "amountDesc": "¥836"}, {"code": "3744", "name": "618旅游节", "amount": 502, "amountDesc": "¥502"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 200, "amountStr": "¥200", "detail": [{"code": "1002", "name": "基础服务费", "amount": 200, "amountDesc": "¥200", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 569.0, "amountStr": "¥569", "subAmount": 1071.0, "subAmountStr": "¥1071", "currencyCode": "¥"}]}], "reactId": "1110096410", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["44444_0_114043_114043"]}, "minTPrice": 569.0, "minDPrice": 167.0, "modifySameVehicle": false, "minDOrinPrice": 418, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "618旅游节", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3744", "groupCode": "MarketGroup1346", "amountTitle": "已减502", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5283", "sortNum": 4, "lowestPrice": 188, "highestPrice": 188, "maximumRating": 0.0, "maximumCommentCount": 1, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "44444", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]MjcyfDE5MTJ8MjAyMC4wMC0yOCAzLTAzMDowMDAwOjAmJjEkJjE4OC0wMy0yMDIzMDowMDI5IDAxODgmOjAwJjEwMDEmMSR8ODgmMyYyJjEwMDMmNzYkMS4wMCYxJjIwMCQxMDIwLjAmOTAmMDImMnwyMDIxODAkLTI4IDMtMDMwOjA4MTE6MQ==", "priceVersion": "AV4B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXBN1qO8q+wPmIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOeFB1O+qAq7LsChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dpCvCKG4FD5exj4CrOI1G42kD0lIQgo57gtve49xdKAsyCLlxVPnGgWwsbWidGqqaR/R6B+YrAzD4C2Xfbmg80/7T331tWsN4YV5MUm+s/WlBj4CrOI1G42VO6HsGwvky8mexv4KTleLKIP0Fbh1/0l4sZ3LyTPztgEkfcwRd7Boty7NDeIvHo3F/nTIqbC0XYb+1E0lER8Uw==", "vendorVehicleCode": "20073109", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1912272, "pLev": 40859, "rLev": 40859, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 376, "amountStr": "¥376", "subAmount": 188, "subAmountStr": "日均¥188", "detail": [{"code": "1001", "name": "租车费", "amount": 376, "amountDesc": "¥376"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 180, "amountStr": "¥180", "detail": [{"code": "1002", "name": "基础服务费", "amount": 180, "amountDesc": "¥180", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 576.0, "amountStr": "¥576", "subAmount": 576.0, "subAmountStr": "¥576", "currencyCode": "¥"}]}], "reactId": "1110096411", "group": 870, "groupSort": 1, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["44444_0_114043_114043"]}, "minTPrice": 576.0, "minDPrice": 188, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5282", "sortNum": 5, "lowestPrice": 188, "highestPrice": 188, "maximumRating": 0.0, "maximumCommentCount": 1, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "44444", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]ODY4fDE5MDd8MjAyMC4wMC0yOCAzLTAzMDowMDAwOjAmJjEkJjE4OC0wMy0yMDIzMDowMDI5IDAxODgmOjAwJjEwMDEmMSR8ODgmMyYyJjEwMDMmNzYkMS4wMCYxJjIwMCQxMDIwLjAmOTAmMDImMnwyMDIxODAkLTI4IDMtMDMwOjA4MTE6MQ==", "priceVersion": "AV4B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXC2QhmKxkKtE4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOeFB1O+qAq7LsChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dpCvCKG4FD5exj4CrOI1G42kD0lIQgo57gtve49xdKAsyCLlxVPnGgWwsbWidGqqaR/R6B+YrAzD4C2Xfbmg80/7T331tWsN4YV5MUm+s/WlBj4CrOI1G42VO6HsGwvky8mexv4KTleLKIP0Fbh1/0l4sZ3LyTPztjlAxXfNwpeUuH+bSQBCz4sF/nTIqbC0XYb+1E0lER8Uw==", "vendorVehicleCode": "20068007", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1907868, "pLev": 40859, "rLev": 40859, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 376, "amountStr": "¥376", "subAmount": 188, "subAmountStr": "日均¥188", "detail": [{"code": "1001", "name": "租车费", "amount": 376, "amountDesc": "¥376"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 180, "amountStr": "¥180", "detail": [{"code": "1002", "name": "基础服务费", "amount": 180, "amountDesc": "¥180", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 576.0, "amountStr": "¥576", "subAmount": 576.0, "subAmountStr": "¥576", "currencyCode": "¥"}]}], "reactId": "1110096412", "group": 870, "groupSort": 3, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["44444_0_114043_114043"]}, "minTPrice": 576.0, "minDPrice": 188, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5524", "sortNum": 7, "lowestPrice": 194.0, "highestPrice": 194.0, "maximumRating": 0.0, "maximumCommentCount": 1, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "44444", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]NDQ3fDE5MjN8MjAyMC4wMC0yOCAzLTAzMDowMDAwOjAmJjEkJjIxOC0wMy0yMDIzMDowMDI5IDAyMTgmOjAwJjEwMDEmMSR8MTgmNCYyJjIwMDMmMzYkMS4wMCYxJjM1MCQxMDM1LjAmOTAmMDImMnwyMDIxODAkLTI4IDMtMDMwOjA4MTE6MQ==", "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAIRWGbFFfSQ4xHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/aWPVGfDQaOeFB1O+qAq7LsChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/drTdiX8YqlaXIViTVf+KyZDJ1uTRNmjFd/RrGj9xfIHEw8KVDNQzvny1sUaQ936ig1DQfPbwnHXoDx+MNbYJu4KMCe5LaKnV16LLr9c38iRADs7sx5Ik0ex931q1ia9gcRwIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLumAfuqKrCaeEeWWrrNNAqQ/tLSUbLQbUzQ==", "vendorVehicleCode": "20092228", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 1923447, "pLev": 40859, "rLev": 40859, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 388.0, "amountStr": "¥388", "subAmount": 194.0, "subAmountStr": "日均¥194", "originalDailyPrice": 218, "detail": [{"code": "1001", "name": "租车费", "amount": 436, "amountDesc": "¥436"}, {"code": "3814", "name": "周边游特惠", "amount": 48, "amountDesc": "¥48"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35.0, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35.0, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 180, "amountStr": "¥180", "detail": [{"code": "1002", "name": "基础服务费", "amount": 180, "amountDesc": "¥180", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 603.0, "amountStr": "¥603", "subAmount": 651.0, "subAmountStr": "¥651", "currencyCode": "¥"}]}], "reactId": "1110096413", "group": 870, "groupSort": 2, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["44444_0_114043_114043"]}, "minTPrice": 603.0, "minDPrice": 194.0, "modifySameVehicle": false, "minDOrinPrice": 218, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "周边游特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "showLayer": 1, "colorCode": "15", "labelCode": "3814", "groupCode": "MarketGroup1352", "amountTitle": "已减48", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}, {"vehicleCode": "5208", "sortNum": 6, "lowestPrice": 224.0, "highestPrice": 224.0, "maximumRating": 0.0, "maximumCommentCount": 1, "lowestDistance": 0.0, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "44444", "vendorCode": "30164", "pStoreCode": "114043", "rStoreCode": "114043", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "[c]", "priceVersion": "AWIB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXA97WjFcZ+rbIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2L0PxUHLXe2ZaWPVGfDQaOdsk4krFSZ62sChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/dpP7EqEhv0mW4ViTVf+KyZD6SND2P0uXPzRrGj9xfIHEwD7hOzc49oM1sUaQ936ig1DQfPbwnHXoB1ERx7gckOqMCe5LaKnV14rqym+OqRbxowpXuON/EJqLJqeFz1wLKnffOXC7xY0H6UPqA45HwM/kS+hn0SGT8F6bcAXtzMDaRjxAJ++10FdAravH3ltxQLyztKnntAvjjtqtF9OQCEG", "vendorVehicleCode": "20063565", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0, "skuId": 2704688, "pLev": 40859, "rLev": 40859, "klbVersion": 1}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 447.0, "amountStr": "¥447", "subAmount": 224.0, "subAmountStr": "日均¥224", "originalDailyPrice": 559, "detail": [{"code": "1001", "name": "租车费", "amount": 1118, "amountDesc": "¥1118"}, {"code": "3744", "name": "618旅游节", "amount": 671, "amountDesc": "¥671"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 20.0, "amountStr": "¥20", "detail": [{"code": "1003", "name": "车行手续费", "amount": 20.0, "amountDesc": "¥20", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 120, "amountStr": "¥120", "detail": [{"code": "1002", "name": "基础服务费", "amount": 120, "amountDesc": "¥120", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 587.0, "amountStr": "¥587", "subAmount": 1258.0, "subAmountStr": "¥1258", "currencyCode": "¥"}]}], "reactId": "1110096410", "group": 0, "groupSort": 0, "scoreSort": 0.0, "hot": 0, "hotType": 0, "hotScore": 0.0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["44444_0_114043_114043"]}, "minTPrice": 587.0, "minDPrice": 224.0, "modifySameVehicle": false, "minDOrinPrice": 559, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "618旅游节", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3744", "groupCode": "MarketGroup1346", "amountTitle": "已减671", "groupId": 1, "mergeId": 0}, "priceSize": 1, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:免费站内取还车", "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}}], "dailyPrice": 52.0, "hasResult": true}, {"groupCode": "2", "groupName": "经济轿车", "sortNum": 0, "dailyPrice": 52.0, "hasResult": true}, {"groupCode": "newenergy", "groupName": "新能源", "sortNum": 2, "dailyPrice": 224.0, "hasResult": true}, {"groupCode": "3", "groupName": "舒适轿车", "sortNum": 3, "dailyPrice": 159.0, "hasResult": true}, {"groupCode": "6", "groupName": "SUV", "sortNum": 4, "dailyPrice": 224.0, "hasResult": true}, {"groupCode": "4", "groupName": "商务车", "sortNum": 5, "dailyPrice": 167.0, "hasResult": true}, {"groupCode": "5", "groupName": "豪华轿车", "sortNum": 6, "dailyPrice": 212.0, "hasResult": true}, {"groupCode": "9", "groupName": "跑车", "sortNum": 7, "dailyPrice": 207.0, "hasResult": true}], "productGroupsHashCode": "8j56409Z95H0k9zakOl2", "storeList": [{"storeCode": "114043", "pickUpLevel": 40859, "pickOffLevel": 40859}], "commNotices": [], "rentCenter": {"id": 2, "name": "三亚凤凰机场店", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.305902", "lng": "109.413683", "filterCode": "Vendor_0", "fromTime": "00:00", "toTime": "23:59"}, "rRentCenter": {"id": 2, "name": "三亚凤凰机场店", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.305902", "lng": "109.413683", "filterCode": "Vendor_0", "fromTime": "00:00", "toTime": "23:59"}, "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "安心保障", "titleExtra": "(需加购优享服务)", "type": 1, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 2, "subTitle": "*覆盖损失范围以预订页面内披露为准", "showLayer": 0}, {"title": "优质车况", "type": 1, "description": "3年内车龄", "sortNum": 3, "subTitle": "", "showLayer": 0}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满（不含纯电车）", "sortNum": 10, "subTitle": "", "showLayer": 0}]}, "basicData": {"sortItems": [{"title": "推荐排序", "type": 1, "code": "1", "sortNum": 1}, {"title": "总价 低→高", "type": 2, "code": "2", "sortNum": 2}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "labelCodes": ["3563", "3814", "3548", "3504", "3705", "3827", "3547", "3709", "3510", "3696", "3731", "3509", "3788", "3744", "3789", "3679", "3746", "3779"], "isAll": false, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isLastPage": false, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "sTitle": "9大无忧特权", "hint": "优选门店无忧租超值价"}, {"id": 2, "title": "无忧租 安心首选", "sTitle": "9大无忧特权", "hint": "无忧租超值价"}, {"id": 3, "title": "全国连锁 服务放心", "sTitle": "", "hint": "一嗨租车超值价"}, {"id": 4, "title": "上门送取车 取还超便捷", "sTitle": "", "hint": "送车上门超值价"}, {"id": 5, "title": "信用租 押金双免", "sTitle": "", "hint": "押金双免超值价"}, {"id": 6, "title": "新车保障 车况佳", "sTitle": "", "hint": "新车超值价"}, {"id": 7, "title": "超值特价 高性价比", "sTitle": "", "hint": "超值特价"}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "hasResultWithoutFilter": true, "isFromSearch": true, "productGroupCodeUesd": "all", "frontTraceInfo": {"vehicleList": ["5283", "17421", "5342", "4012", "17380", "5282", "2801", "3059", "656", "75", "33", "5010", "4482", "5594", "4067", "5596", "5193", "286", "287", "1149", "1445", "5368", "5524", "4139", "5504", "5208", "5329"], "vehicleGroupMap": {"2": 3, "newenergy": 5, "3": 5, "4": 7, "5": 3, "6": 2, "9": 7}, "priceCount": 32, "normalCount": 32, "easyLifeCount": 0, "zhimaCount": 32, "vendorNames": ["懒人行卡拉比"]}, "uniqSign": "12001050110000114083TXZy4T6V1n2nqQ1HwuJd", "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "pHub": 1, "rHub": 1, "promotMap": {}, "extras": {"serverRequestId": "56I12kW1111484972703", "abVersion": "230104_DSJT_fil10|A,220323_DSJT_rank2|B"}, "isRecommend": false, "isKlbData": true}