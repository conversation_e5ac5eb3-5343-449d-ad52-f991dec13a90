{"recommendInfo": {"promptTitle": "暂无符合要求的车辆哦", "buttonTitle": "修改取还车条件", "type": 3, "promptSubTitle": "建议您修改取还车条件"}, "extras": {"serverRequestId": "tgDa0it0X9371321Lrf9", "hasListSign": "254Ww8Mf", "abVersion": "230104_DSJT_rc101|B"}, "baseResponse": {"extMap": {"recCost": "1.0", "allCost": "408.0", "start": "2023-02-08 15:36:33", "shoppingCost_1": "407.0", "end": "2023-02-08 15:36:33", "dropoffCityId": "175", "pageName": "List", "apiCost": "406.0", "restCost": "1", "isRec": "1", "pickupCityId": "175"}, "errorCode": "35105", "hasResult": false, "code": "200", "showMessage": "暂无符合要求的车辆哦,建议您修改取还车条件", "apiResCodes": ["35105"], "returnMsg": "暂无符合要求的车辆哦,建议您修改取还车条件", "message": "暂无符合要求的车辆哦,建议您修改取还车条件", "requestId": "ffbaf4d2-9af6-4a91-ae30-bec6cb1e2331", "isSuccess": false}, "allVehicleCount": 0, "commNotices": [], "hasResultWithoutFilter": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "isRecommend": true, "uniqSign": "120010356100002012491aK4i6K1C82669X3P1hd", "allVendorPriceCount": 0, "isAll": true, "ResponseStatus": {"Extension": [{"Value": "7537246417229729377", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a04ad05-465511-30411", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1675841793936+0800)/"}, "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&modify=&orderId=&pickupPointInfo=2023-02-08 16:00:00|富蕴客运站|175|46.989905|89.526772|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=all&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2023-02-08 16:30:00|富蕴客运站|175|46.989905|89.526772|||&sortType=1&uid=@@PAGENUM@@1", "networkCost": 710, "environmentCost": 1, "cacheFetchCost": 0, "fetchCost": 680, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1675841793312, "afterFetch": 1675841794022, "hasRetry": false, "loadDiffCost": 30, "originNetworkCost": 680}}