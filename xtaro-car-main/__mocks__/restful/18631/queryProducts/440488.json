{"freePreAuthCode": "", "licenseInfo": {"pickupCountryName": "美国", "returnCountryName": "美国", "returnSupportCDLType": 1, "pickupSupportCDLType": 1, "pickupLicenseDesc": "持中国大陆驾照可在美国租车", "noticeMsg": "", "returnLicenseDesc": "持中国大陆驾照可在美国租车"}, "allVendorPriceCount": 4, "needRetry": false, "vehicleList": [{"transmissionName": "自动挡", "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/HYUNDAI-Elantra.jpg", "isHot": false, "imageList": ["//pic.c-ctrip.com/car/osd/online/vehicle_new/Hyundai_Elantra.png"], "vehicleCode": "10387", "recommendDesc": "高性价比，可胜任中短途", "similarCommentDesc": "空间类似大众速腾", "name": "现代伊兰特El<PERSON>ra", "zhName": "现代伊兰特El<PERSON>ra", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Hyundai_Elantra.png", "brandId": 20, "passengerNo": 5, "hasConditioner": false, "doorNo": 4, "isSpecialized": false, "groupCode": "G03", "userRealImageCount": 0, "luggageNo": 3, "brandEName": "Hyundai", "groupSubClassCode": "I", "groupName": "中大型车", "transmissionType": 1, "spaceDesc": "建议乘坐4人+2行李箱", "groupSubName": "中型轿车"}, {"transmissionName": "自动挡", "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/NISSAN-Altima.jpg", "isHot": false, "imageList": ["//pic.c-ctrip.com/car/osd/online/vehicle_new/Nissan_Altima.png"], "vehicleCode": "10570", "recommendDesc": "空间较大，乘坐舒适", "similarCommentDesc": "空间类似大众帕萨特", "name": "日产Altima", "zhName": "日产Altima", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Nissan_Altima.png", "brandId": 34, "passengerNo": 5, "hasConditioner": false, "doorNo": 4, "isSpecialized": false, "groupCode": "G03", "userRealImageCount": 0, "luggageNo": 3, "brandEName": "Nissan", "groupSubClassCode": "P", "groupName": "中大型车", "transmissionType": 1, "spaceDesc": "建议乘坐4人+2行李箱", "groupSubName": "大型轿车"}, {"transmissionName": "自动挡", "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/NISSAN-Versa.jpg", "isHot": false, "imageList": ["//pic.c-ctrip.com/car/osd/online/vehicle_new/Nissan_Versa.png"], "vehicleCode": "10619", "recommendDesc": "常见的家用车型，三人短途首选", "similarCommentDesc": "空间类似福特福克斯", "name": "日产Versa", "zhName": "日产Versa", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Nissan_Versa.png", "brandId": 34, "passengerNo": 5, "hasConditioner": false, "doorNo": 4, "isSpecialized": false, "groupCode": "G02", "userRealImageCount": 0, "luggageNo": 2, "brandEName": "Nissan", "groupSubClassCode": "C", "groupName": "小型轿车", "transmissionType": 1, "spaceDesc": "建议乘坐3人+2行李箱", "groupSubName": "紧凑型轿车"}, {"transmissionName": "自动挡", "realityImageUrl": "//pic.c-ctrip.com/car/osd/mobile/vehimg/vehcrn/NISSAN-Rogue.jpg", "isHot": false, "imageList": ["//pic.c-ctrip.com/car/osd/online/vehicle_new/Nissan_Rogue.png"], "vehicleCode": "10606", "recommendDesc": "操控灵活，视野开阔", "similarCommentDesc": "空间类似大众途观", "name": "日产Rogue", "zhName": "日产Rogue", "imageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Nissan_Rogue.png", "brandId": 34, "passengerNo": 5, "hasConditioner": false, "doorNo": 4, "isSpecialized": false, "groupCode": "G05", "userRealImageCount": 0, "luggageNo": 1, "brandEName": "Nissan", "groupSubClassCode": "J", "groupName": "SUV", "transmissionType": 1, "spaceDesc": "建议乘坐4人+2行李箱", "groupSubName": "中型SUV"}], "commNotices": [], "isMoreAge": false, "filterMenuItems": [{"code": "BrandAndAccessory", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"sortNum": 1, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_5", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "7座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "Seat_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 2, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "手动挡"}]}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 6, "groupCode": "Brand", "itemCode": "Brand_20", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "现代", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"sortNum": 6, "groupCode": "Brand", "itemCode": "Brand_34", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "groupCode": "Brand", "shortName": "车辆品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "品牌/配置"}, {"code": "StoreAndPR", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "门店位置", "sortNum": 1, "bitwiseType": 2, "name": "门店位置", "groupCode": "Location", "filterItems": [{"sortNum": 1, "groupCode": "Location", "itemCode": "Location_OusideOfAirport", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "机场外"}, {"sortNum": 1, "groupCode": "Location", "quickSortNum": 10000, "itemCode": "Location_InsideOfAirport", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "机场内"}]}, {"shortName": "距离", "sortNum": 2, "bitwiseType": 2, "name": "门店距离", "groupCode": "DistanceGroup", "filterItems": [{"sortNum": 2, "groupCode": "DistanceGroup", "itemCode": "Distance_M500", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "500米内"}, {"sortNum": 2, "groupCode": "DistanceGroup", "itemCode": "Distance_K1", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "1公里内"}, {"sortNum": 2, "groupCode": "DistanceGroup", "itemCode": "Distance_K2", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "2公里内"}, {"sortNum": 2, "groupCode": "DistanceGroup", "itemCode": "Distance_K5", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "5公里内"}]}, {"shortName": "点评", "sortNum": 3, "bitwiseType": 2, "name": "点评", "groupCode": "Comment", "filterItems": [{"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.5", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.5分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_3.5", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "3.5分以上"}]}, {"sortNum": 10, "isSupportMulti": true, "filterItems": [{"sortNum": 10, "groupCode": "StoreService", "quickSortNum": 120001, "itemCode": "StoreService_3564", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 1, "name": "立即确认"}, {"sortNum": 10, "groupCode": "StoreService", "quickSortNum": 70001, "itemCode": "StoreService_3563", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 11, "isSupportMulti": true, "filterItems": [{"sortNum": 11, "groupCode": "SpecialService", "quickSortNum": 100005, "positionCode": "10", "itemCode": "SpecialService_3557", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "满油取还"}, {"sortNum": 11, "groupCode": "SpecialService", "quickSortNum": 60002, "itemCode": "SpecialService_3554", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 2, "name": "送第三者责任险"}], "groupCode": "SpecialService", "shortName": "特色服务", "bitwiseType": 2, "name": "特色服务"}], "name": "门店/服务"}, {"code": "filter", "sortNum": 3, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "0-100", "sortNum": 0, "name": "¥100以下", "groupCode": "Price", "itemCode": "Price_0-100"}, {"code": "100-200", "sortNum": 0, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-300", "sortNum": 0, "name": "¥200-300", "groupCode": "Price", "itemCode": "Price_200-300"}, {"code": "300-400", "sortNum": 0, "name": "¥300-400", "groupCode": "Price", "itemCode": "Price_300-400"}, {"code": "400-99999", "sortNum": 0, "name": "¥400以上", "groupCode": "Price", "itemCode": "Price_400-99999"}]}, {"shortName": "支付方式", "sortNum": 2, "bitwiseType": 2, "name": "支付方式", "groupCode": "PayMode", "filterItems": [{"sortNum": 2, "groupCode": "PayMode", "itemCode": "PayMode_Prepaid", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "在线支付"}]}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "DriverLience", "quickSortNum": 50002, "itemCode": "DriverLience_lt1001", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "仅需中国大陆驾照"}, {"sortNum": 3, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1002", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "支持中国驾照原件+驾照国际翻译认证件"}, {"sortNum": 3, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1003", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "支持中国驾照原件+车行翻译件"}, {"sortNum": 3, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1004", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "支持中国驾照原件+当地语言公证件"}, {"sortNum": 3, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1006", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "支持香港驾照"}, {"sortNum": 3, "groupCode": "DriverLience", "itemCode": "DriverLience_lt1014", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "支持国际驾照IDP+签发国当地驾照"}], "groupCode": "DriverLience", "shortName": "驾照要求", "bitwiseType": 2, "name": "驾照要求"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "CreditCard", "quickSortNum": 9999, "itemCode": "CreditCard_DepositFree", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "免押金"}, {"sortNum": 3, "groupCode": "CreditCard", "itemCode": "CreditCard_SupportUnionLogoNew", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "支持银联"}, {"sortNum": 4, "groupCode": "CreditCard", "itemCode": "CreditCard_SupportNoEmbossed", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "支持非凸字信用卡"}, {"sortNum": 4, "groupCode": "CreditCard", "itemCode": "CreditCard_SupportNoChip", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "支持非芯片信用卡"}], "groupCode": "CreditCard", "shortName": "押金方式", "bitwiseType": 2, "name": "押金方式"}, {"shortName": "里程限制", "sortNum": 5, "bitwiseType": 2, "name": "里程限制", "groupCode": "Limit", "filterItems": [{"sortNum": 5, "groupCode": "Limit", "quickSortNum": 110001, "itemCode": "Mileage_Unlimited", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "不限里程"}, {"sortNum": 5, "groupCode": "Limit", "itemCode": "Mileage_Limited", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "限里程"}]}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "itemCode": "Vendor_SD0131", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "Green Motion"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "筛选"}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "价格 低→高", "type": 2}], "trunkInfo": {"content": "行李箱说明", "url": "https://pic.c-ctrip.com/car/osd/mobile/crn/osd/baggagedescription.png"}}, "supportUnionLogoCode": "", "baseResponse": {"extMap": {"end": "2023-07-28 14:52:51", "allCost": "848.0", "start": "2023-07-28 14:52:50", "uid": "M2251710888", "dropoffCityId": "347", "dataConvertResCost": "143.0", "apiCost": "692.0", "pageName": "List", "restCost": "4", "pickupCityId": "347"}, "hasResult": true, "code": "201", "apiResCodes": [], "returnMsg": "SUCCESS", "requestId": "6d055c27-07f7-491f-9c59-7e227cfda59a", "isSuccess": true}, "filterItemExts": [], "promptInfos": [], "ResponseStatus": {"Extension": [{"Value": "2687138445913678868", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a8b4d1b-469590-97834", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1690527171340+0800)/"}, "similarVehicleIntroduce": {"cases": [{"vehicleGroupName": "车型组", "representativeVehicleName": "代表车型", "vehicleGroupItems": "车组包含", "vehicleGroupCode": "default"}, {"vehicleGroupName": "紧凑型轿车", "representativeVehicleName": "福特嘉年华", "vehicleGroupItems": "丰田卡罗拉、现代雅绅特", "vehicleGroupCode": "D"}, {"vehicleGroupName": "中大型轿车", "representativeVehicleName": "丰田凯美瑞", "vehicleGroupItems": "雪佛兰迈锐宝、大众帕萨特", "vehicleGroupCode": "S"}, {"vehicleGroupName": "中大型SUV", "representativeVehicleName": "大众途观", "vehicleGroupItems": "丰田RAV4、吉普指南者", "vehicleGroupCode": "R"}], "carProtection": {"title": "取车保障", "description": "有库存时尽量提供所选车型\n若所选车型无库存，保证实际所取为同级或升级车型组车型\n门店主动调整车型，不额外收取车型变更费用"}, "introduce": {"title": "同组车型", "description": "在境外租车时，境外车行通常根据车型的功能、大小、座位数进行划分。您选择的车型并非保证为到店取到的车型，它仅为该类“同组车型”下的一种可能性。例如"}}, "requestInfo": {"rLongitude": -118.40853, "rDate": "20230808120000", "age": 30, "returnDate": "/Date(1691467200000+0800)/", "sourceCountryId": 1, "pLatitude": 33.941589, "rLatitude": 33.941589, "pLongitude": -118.40853, "pDate": "20230801100000", "pickupLocationName": "洛杉矶国际机场", "returnLocationName": "洛杉矶国际机场", "pickupDate": "/Date(1690855200000+0800)/", "rentalDay": 8}, "extras": {}, "productGroupsHashCode": "c653bf9a2e32f37cd517fca577257b9556b6943b38ecfb1c474cc74eb34e9e32b6612fd8", "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "labelCodes": ["3557", "3588", "3554", "3564", "3563", "3580"], "quickFilter": [{"sortNum": 3, "groupCode": "CreditCard", "quickSortNum": 9999, "itemCode": "CreditCard_DepositFree", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "免押金"}, {"sortNum": 1, "groupCode": "Location", "quickSortNum": 10000, "itemCode": "Location_InsideOfAirport", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "机场内"}, {"sortNum": 3, "groupCode": "DriverLience", "quickSortNum": 50002, "itemCode": "DriverLience_lt1001", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "仅需中国大陆驾照"}, {"sortNum": 11, "groupCode": "SpecialService", "quickSortNum": 60002, "itemCode": "SpecialService_3554", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 2, "name": "送第三者责任险"}, {"sortNum": 10, "groupCode": "StoreService", "quickSortNum": 70001, "itemCode": "StoreService_3563", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}, {"sortNum": 11, "groupCode": "SpecialService", "quickSortNum": 100005, "positionCode": "10", "itemCode": "SpecialService_3557", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "满油取还"}, {"sortNum": 5, "groupCode": "Limit", "quickSortNum": 110001, "itemCode": "Mileage_Unlimited", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "不限里程"}, {"sortNum": 10, "groupCode": "StoreService", "quickSortNum": 120001, "itemCode": "StoreService_3564", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 1, "name": "立即确认"}], "allVehicleCount": 4, "productGroups": [{"sortNum": 2, "productList": [{"vehicleCode": "10619", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "Green Motion", "vendorLogo": "//images4.c-ctrip.com/target/430o0i0000009hz2d6A87.jpg", "reference": {"bizVendorCode": "14027", "packageId": "10055", "vendorCode": "SD0131", "rStoreCode": "LAXGM60509", "vehicleCode": "10619", "decoratorVendorType": 0, "bomCode": "LAXGM60509_10619_CDW_FRFB_Fees_LM_TP_TPL_Taxes_0_0", "productCode": "NEWAVB619RPM1CLD6CHK", "vendorVehicleCode": "CCAR_CL-B1_NISSANVERSA", "payMode": 2, "rCityId": 347, "unionCardFilter": {}, "pStoreCode": "LAXGM60509", "sippCode": "CCAR", "vcExtendRequest": {"responseReturnLocationId": "60509", "vendorVehicleId": "87448_53483528931", "responsePickUpLocationId": "60509"}, "pCityId": 347, "isEasyLife": false, "packageType": 0, "age": 30, "noDepositFilter": {}, "rateCode": "$BRO98", "priceVersion": "ARcA1+SLAhazZlOgvy9hGiNh5/mHhytv/UCr"}, "isRStoreSupportCdl": true, "extMap": {"confirmRightNow": "true", "distance": "3.3427", "freeCancel": "true", "isVendorActive": "0", "isAsiaPickup": "false", "isConfirmTimeGth12": "false", "isThirdInsurance": "true", "isULM": "false", "isNoOnewayFee": "false", "isSupportZhima": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款。", "labelCode": "3563"}, {"category": 2, "sortNum": 65, "code": "2", "title": "送第三者责任险", "colorCode": "2", "type": 1, "description": "该产品赠送第三者责任险。", "labelCode": "3554"}, {"category": 2, "sortNum": 80, "code": "2", "title": "满油取还", "colorCode": "2", "type": 1, "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "labelCode": "3557"}, {"category": 4, "sortNum": 125, "code": "4", "title": "里程限制每天322KM", "colorCode": "4", "type": 2, "description": "里程限制每天322KM，超出里程按照US$0.55/mile计算。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "labelCode": "3580"}], "promotions": [], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 8, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 12, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 1, "checkType": 0}], "distance": 3.3427, "pStoreRouteDesc": "机场外", "reactId": "1452513253", "orignalPriceStyle": "WithStrikethrough", "priceInfo": {"currentTotalPrice": 6872, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 859, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {"title": "当地连锁", "sortNum": 0}}], "vehicleRecommendProduct": {"productCodes": ["NEWAVB619RPM1CLD6CHK"], "introduce": "同组车型最低价"}, "maximumCommentCount": 0, "reactId": "1452513330", "vendorSimilarVehicleInfos": [{"vendorLogo": "//images4.c-ctrip.com/target/430o0i0000009hz2d6A87.jpg", "similarVehicleInfos": [{"vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Nissan_Versa.png", "vehicleCode": "10619", "vehicleName": "日产Versa"}], "vendorName": "Green Motion", "bizVendorCode": "14027"}], "sortNum": 0, "lowestDistance": 3.3427, "minTPrice": 6872, "lowestPrice": 859, "highestPrice": 859}], "groupCode": "G02", "groupName": "小型轿车", "dailyPrice": 859, "allowMerge": true, "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/economy.png"}, {"sortNum": 3, "productList": [{"vehicleCode": "10387", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "Green Motion", "vendorLogo": "//images4.c-ctrip.com/target/430o0i0000009hz2d6A87.jpg", "reference": {"bizVendorCode": "14027", "packageId": "10055", "vendorCode": "SD0131", "rStoreCode": "LAXGM60509", "vehicleCode": "10387", "decoratorVendorType": 0, "bomCode": "LAXGM60509_10387_CDW_FRFB_Fees_LM_TP_TPL_Taxes_0_0", "productCode": "JOBSVCAUTOAVB387RAINHJEJ8WN", "vendorVehicleCode": "XXAR_CL-X_HYUNDAIELANTRA", "payMode": 2, "rCityId": 347, "unionCardFilter": {}, "pStoreCode": "LAXGM60509", "sippCode": "XXAR", "vcExtendRequest": {"responseReturnLocationId": "60509", "vendorVehicleId": "87543_53483528931", "responsePickUpLocationId": "60509"}, "pCityId": 347, "isEasyLife": false, "packageType": 0, "age": 30, "noDepositFilter": {}, "rateCode": "$BRO98", "priceVersion": "ARcA3PsCSjal4UOndSVj3IwEjfmHhytv/UCr"}, "isRStoreSupportCdl": true, "extMap": {"confirmRightNow": "true", "distance": "3.3427", "freeCancel": "true", "isVendorActive": "0", "isAsiaPickup": "false", "isConfirmTimeGth12": "false", "isThirdInsurance": "true", "isULM": "false", "isNoOnewayFee": "false", "isSupportZhima": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款。", "labelCode": "3563"}, {"category": 2, "sortNum": 65, "code": "2", "title": "送第三者责任险", "colorCode": "2", "type": 1, "description": "该产品赠送第三者责任险。", "labelCode": "3554"}, {"category": 2, "sortNum": 80, "code": "2", "title": "满油取还", "colorCode": "2", "type": 1, "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "labelCode": "3557"}, {"category": 4, "sortNum": 125, "code": "4", "title": "里程限制每天322KM", "colorCode": "4", "type": 2, "description": "里程限制每天322KM，超出里程按照US$0.55/mile计算。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "labelCode": "3580"}], "promotions": [], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 1, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 8, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 12, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 1, "checkType": 0}], "distance": 3.3427, "pStoreRouteDesc": "机场外", "reactId": "1452513180", "orignalPriceStyle": "WithStrikethrough", "priceInfo": {"currentTotalPrice": 6338, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 792, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {"title": "当地连锁", "sortNum": 0}}], "vehicleRecommendProduct": {"productCodes": ["JOBSVCAUTOAVB387RAINHJEJ8WN"], "introduce": "同组车型最低价"}, "maximumCommentCount": 0, "reactId": "1452513330", "vendorSimilarVehicleInfos": [{"vendorLogo": "//images4.c-ctrip.com/target/430o0i0000009hz2d6A87.jpg", "similarVehicleInfos": [{"vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Hyundai_Elantra.png", "vehicleCode": "10387", "vehicleName": "现代伊兰特El<PERSON>ra"}], "vendorName": "Green Motion", "bizVendorCode": "14027"}], "sortNum": 0, "lowestDistance": 3.3427, "minTPrice": 6338, "lowestPrice": 792, "highestPrice": 792}, {"vehicleCode": "10570", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "Green Motion", "vendorLogo": "//images4.c-ctrip.com/target/430o0i0000009hz2d6A87.jpg", "reference": {"bizVendorCode": "14027", "packageId": "10055", "vendorCode": "SD0131", "rStoreCode": "LAXGM60509", "vehicleCode": "10570", "decoratorVendorType": 0, "bomCode": "LAXGM60509_10570_CDW_FRFB_Fees_LM_TP_TPL_Taxes_0_0", "productCode": "NEWAVB570RTCYJTT1T7B", "vendorVehicleCode": "FCAR_CL-E1_NISSANALTIMA", "payMode": 2, "rCityId": 347, "unionCardFilter": {}, "pStoreCode": "LAXGM60509", "sippCode": "FCAR", "vcExtendRequest": {"responseReturnLocationId": "60509", "vendorVehicleId": "87451_53483528931", "responsePickUpLocationId": "60509"}, "pCityId": 347, "isEasyLife": false, "packageType": 0, "age": 30, "noDepositFilter": {}, "rateCode": "$BRO98", "priceVersion": "ARcAGpwTPdpdXWNearQL1XeUlfmHhytv/UCr"}, "isRStoreSupportCdl": true, "extMap": {"confirmRightNow": "true", "distance": "3.3427", "freeCancel": "true", "isVendorActive": "0", "isAsiaPickup": "false", "isConfirmTimeGth12": "false", "isThirdInsurance": "true", "isULM": "false", "isNoOnewayFee": "false", "isSupportZhima": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款。", "labelCode": "3563"}, {"category": 2, "sortNum": 65, "code": "2", "title": "送第三者责任险", "colorCode": "2", "type": 1, "description": "该产品赠送第三者责任险。", "labelCode": "3554"}, {"category": 2, "sortNum": 80, "code": "2", "title": "满油取还", "colorCode": "2", "type": 1, "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "labelCode": "3557"}, {"category": 4, "sortNum": 125, "code": "4", "title": "里程限制每天322KM", "colorCode": "4", "type": 2, "description": "里程限制每天322KM，超出里程按照US$0.55/mile计算。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "labelCode": "3580"}], "promotions": [], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 8, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 12, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 1, "checkType": 0}], "distance": 3.3427, "pStoreRouteDesc": "机场外", "reactId": "1452513201", "orignalPriceStyle": "WithStrikethrough", "priceInfo": {"currentTotalPrice": 7445, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 931, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {"title": "当地连锁", "sortNum": 0}}], "vehicleRecommendProduct": {"productCodes": ["NEWAVB570RTCYJTT1T7B"], "introduce": "同组车型最低价"}, "maximumCommentCount": 0, "reactId": "1452513331", "vendorSimilarVehicleInfos": [{"vendorLogo": "//images4.c-ctrip.com/target/430o0i0000009hz2d6A87.jpg", "similarVehicleInfos": [{"vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Nissan_Altima.png", "vehicleCode": "10570", "vehicleName": "日产Altima"}], "vendorName": "Green Motion", "bizVendorCode": "14027"}], "sortNum": 0, "lowestDistance": 3.3427, "minTPrice": 7445, "lowestPrice": 931, "highestPrice": 931}], "groupCode": "G03", "groupName": "中大型车", "dailyPrice": 792, "allowMerge": true, "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/standard.png"}, {"sortNum": 5, "productList": [{"vehicleCode": "10606", "isSpecialized": false, "maximumRating": 0, "vendorPriceList": [{"vendorName": "Green Motion", "vendorLogo": "//images4.c-ctrip.com/target/430o0i0000009hz2d6A87.jpg", "reference": {"bizVendorCode": "14027", "packageId": "10055", "vendorCode": "SD0131", "rStoreCode": "LAXGM60509", "vehicleCode": "10606", "decoratorVendorType": 0, "bomCode": "LAXGM60509_10606_CDW_FRFB_Fees_LM_TP_TPL_Taxes_0_0", "productCode": "NEWAVB606R7BQ7VYHA39", "vendorVehicleCode": "SFAR_CL-F_NISSANROGUE", "payMode": 2, "rCityId": 347, "unionCardFilter": {}, "pStoreCode": "LAXGM60509", "sippCode": "SFAR", "vcExtendRequest": {"responseReturnLocationId": "60509", "vendorVehicleId": "604_53483528931", "responsePickUpLocationId": "60509"}, "pCityId": 347, "isEasyLife": false, "packageType": 0, "age": 30, "noDepositFilter": {}, "rateCode": "$BRO98", "priceVersion": "ARgA8HzxTvbsSCZJl0mTcQ61VU/xvW3V64NI"}, "isRStoreSupportCdl": true, "extMap": {"confirmRightNow": "true", "distance": "3.3427", "freeCancel": "true", "isVendorActive": "0", "isAsiaPickup": "false", "isConfirmTimeGth12": "false", "isThirdInsurance": "true", "isULM": "false", "isNoOnewayFee": "false", "isSupportZhima": "false"}, "rStoreRouteDesc": "", "qualityScore": 0, "isPStoreSupportCdl": true, "sortNum": 0, "allTags": [{"category": 1, "sortNum": 10, "code": "2", "title": "立即确认", "colorCode": "8", "type": 1, "description": "预订此产品后将立即确认订单并在取车时间为您安排好车辆。", "labelCode": "3564"}, {"category": 1, "sortNum": 25, "code": "2", "title": "免费取消", "colorCode": "8", "type": 1, "description": "取车（当地时间）前可免费取消；延迟取车或提前还车不退还剩余租金；超过取车时间取消或未取车，将不退还预付款。", "labelCode": "3563"}, {"category": 2, "sortNum": 65, "code": "2", "title": "送第三者责任险", "colorCode": "2", "type": 1, "description": "该产品赠送第三者责任险。", "labelCode": "3554"}, {"category": 2, "sortNum": 80, "code": "2", "title": "满油取还", "colorCode": "2", "type": 1, "description": "取车时油箱已满，还车时也得把油箱加满，否则租车公司将会收取未加满部分的燃油费用以及工作人员去加油的工时费。在某些特殊情况下您取车时的油箱可能是不满的，您还车时只要保证油量和取车时一致即可。请保留最后一次加油时的单据以及取还车时显示的总里程和油量表的照片以备用。", "labelCode": "3557"}, {"category": 4, "sortNum": 125, "code": "4", "title": "里程限制每天322KM", "colorCode": "4", "type": 2, "description": "里程限制每天322KM，超出里程按照US$0.55/mile计算。*注意：对于里程限制【每天】的情况，实际里程限制按照整个租车期间计算。例：租期3天，里程限制每天200km，则实际里程总限制为600km（单天可超200km）；当租期不满1天时按整日计算，如租期为3天2小时，里程限制为800km（租期按4天计算）。", "labelCode": "3580"}], "promotions": [], "isSpecialized": false, "easyLifeInfo": {"isEasyLife": false}, "commentInfo": {"commentCount": 0, "maximumRating": 5, "commentLabel": "", "level": "", "hasComment": 0, "overallRating": "0.0"}, "filterAggregations": [{"groupCode": "Brand", "binaryDigit": 2, "checkType": 0}, {"groupCode": "DriverLience", "binaryDigit": 8, "checkType": 0}, {"groupCode": "DistanceGroup", "binaryDigit": 8, "checkType": 0}, {"groupCode": "SpecialService", "binaryDigit": 12, "checkType": 0}, {"groupCode": "Transmission", "binaryDigit": 1, "checkType": 0}, {"groupCode": "SeatGroup", "binaryDigit": 4, "checkType": 0}, {"groupCode": "StoreService", "binaryDigit": 3, "checkType": 0}, {"groupCode": "Limit", "binaryDigit": 2, "checkType": 0}, {"groupCode": "Vendor_0", "binaryDigit": 1, "checkType": 0}, {"groupCode": "PayMode", "binaryDigit": 1, "checkType": 0}, {"groupCode": "Location", "binaryDigit": 1, "checkType": 0}], "distance": 3.3427, "pStoreRouteDesc": "机场外", "reactId": "1452513232", "orignalPriceStyle": "WithStrikethrough", "priceInfo": {"currentTotalPrice": 8171, "localCurrencyCode": "USD", "deductInfos": [], "currentOriginalDailyPrice": 0, "currentDailyPrice": 1021, "currentCurrencyCode": "CNY"}, "isBroker": false, "isSelect": false, "vendorTag": {"title": "当地连锁", "sortNum": 0}}], "vehicleRecommendProduct": {"productCodes": ["NEWAVB606R7BQ7VYHA39"], "introduce": "同组车型最低价"}, "maximumCommentCount": 0, "reactId": "1452513330", "vendorSimilarVehicleInfos": [{"vendorLogo": "//images4.c-ctrip.com/target/430o0i0000009hz2d6A87.jpg", "similarVehicleInfos": [{"vehicleImageUrl": "//pic.c-ctrip.com/car/osd/online/vehicle_new/Nissan_Rogue.png", "vehicleCode": "10606", "vehicleName": "日产Rogue"}], "vendorName": "Green Motion", "bizVendorCode": "14027"}], "sortNum": 0, "lowestDistance": 3.3427, "minTPrice": 8171, "lowestPrice": 1021, "highestPrice": 1021}], "groupCode": "G05", "groupName": "SUV", "dailyPrice": 1021, "allowMerge": true, "groupImg": "//pic.ctrip.com//car/osd/mobile/21_car_group/intermediate_suv.png"}], "appResponseMap": {"isFromCache": false, "isCacheValid": false, "cacheKey": "18631%2FqueryProducts_M2251710888_%7B%22age%22%3A30%2C%22adultNumbers%22%3A2%2C%22childrenNumbers%22%3A0%2C%22pickupPointInfo%22%3A%7B%22cityId%22%3A347%2C%22date%22%3A%222023-08-01%2010%3A00%3A00%22%2C%22locationCode%22%3A%22LAX%22%2C%22locationName%22%3A%22%E6%B4%9B%E6%9D%89%E7%9F%B6%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BA%22%2C%22locationType%22%3A1%2C%22poi%22%3A%7B%22latitude%22%3A33.941589%2C%22longitude%22%3A-118.40853%2C%22radius%22%3A0%7D%2C%22pickupOnDoor%22%3A0%2C%22dropOffOnDoor%22%3A0%7D%2C%22returnPointInfo%22%3A%7B%22cityId%22%3A347%2C%22date%22%3A%222023-08-08%2012%3A00%3A00%22%2C%22locationCode%22%3A%22LAX%22%2C%22locationName%22%3A%22%E6%B4%9B%E6%9D%89%E7%9F%B6%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BA%22%2C%22locationType%22%3A1%2C%22poi%22%3A%7B%22latitude%22%3A33.941589%2C%22longitude%22%3A-118.40853%2C%22radius%22%3A0%7D%2C%22pickupOnDoor%22%3A0%2C%22dropOffOnDoor%22%3A0%7D%2C%22searchType%22%3A1%2C%22modify%22%3Anull%2C%22queryListCacheId%22%3A%22%22%2C%22vendorGroup%22%3A0%2C%22extraMaps%22%3A%7B%22klbVersion%22%3Anull%2C%22orignScenes%22%3Anull%2C%22isMarketing%22%3Anull%7D%7D", "groupId": "18631/queryProducts", "networkCost": 948, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 948, "setCacheCost": 0, "cacheFrom": "", "uniqRequestKey": "OSD_C_APP_875a4d9c-42e3-4379-a5f4-85530986a278/true", "beforeFetch": 1690527170311, "afterFetch": 1690527171259, "hasRetry": false, "isSuccess": true, "hasResult": true}}