{"baseResponse": {"extMap": {}, "cost": 1723, "hasResult": true, "extraIndexTags": {"pcName": "中国", "rcId": "1", "rcName": "中国", "rCityName": "三亚", "rCityId": "43", "pCityId": "43", "pcId": "1", "pCityName": "三亚"}, "code": "200", "errorCode": "0", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "5b91e52c-8539-45fe-bce5-e8a63e1c84ed", "isSuccess": true}, "rHub": 1, "isKlbData": true, "allVendorPriceCount": 6063, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": true, "rRentCenter": {"images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "id": 2, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "23:59", "lat": "18.308537", "lng": "109.413536", "name": "三亚凤凰机场店", "fromTime": "00:00"}, "extras": {"packageLevelAB": "B", "serverRequestId": "9294qS1m91K8F2lBB833", "isLicensePlateHideShow": "0", "abVersion": "240419_DSJT_wyz24|B,230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B,220624_DSJT_spfj1|B,231218_DSJT_zzqh|B", "packageLevelSwitch": "1", "selfServiceSwitch": "1", "isNewLicensePlate": "0", "commodityClass2Version": "1"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"sortNum": 1, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "CarAge", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "8", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 2, "positionCode": "8", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}], "groupCode": "CarAge", "shortName": "车龄", "bitwiseType": 2, "name": "车龄"}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 2, "groupCode": "SeatGroup", "itemCode": "SeatGroup_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 3, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 4, "groupCode": "SeatGroup", "itemCode": "SeatGroup_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 5, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "13", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 6, "groupCode": "SeatGroup", "itemCode": "SeatGroup_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "10", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 2, "groupCode": "Transmission", "itemCode": "Transmission_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "手动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "NewEnergy", "itemCode": "NewEnergy_elect", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}, {"sortNum": 2, "groupCode": "NewEnergy", "itemCode": "NewEnergy_mix", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "新能源混动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "9", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 3, "groupCode": "VehicleAccessory", "quickSortNum": 7, "positionCode": "9", "itemCode": "VehicleAccessory_radar", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "倒车雷达"}, {"sortNum": 4, "groupCode": "VehicleAccessory", "quickSortNum": 8, "positionCode": "9", "itemCode": "VehicleAccessory_tachograph", "binaryDigit": 64, "isQuickItem": true, "bitwiseType": 1, "name": "行车记录仪"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}, {"sortNum": 7, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Refrigerator", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 1, "name": "车载冰箱"}, {"sortNum": 9, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Childseat", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 1, "name": "儿童座椅"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "HotBrand", "itemCode": "HotBrand_奥迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_别克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_奔驰", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_宝马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotB<PERSON>_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 6, "groupCode": "HotBrand", "itemCode": "HotBrand_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 10, "groupCode": "HotBrand", "itemCode": "HotBrand_Jeep", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 12, "groupCode": "HotBrand", "itemCode": "HotBrand_路虎", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 18, "groupCode": "HotBrand", "itemCode": "HotBrand_日产", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_AITO", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "AITO"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_ARCFOX极狐", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "ARCFOX极狐", "icon": "https://dimg04.c-ctrip.com/images/0R45n120009gwwm678554.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_埃安", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "埃安", "icon": "https://dimg04.c-ctrip.com/images/0R44f120009gwvd13842B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_奥迪", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_爱驰", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "爱驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aichi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_阿斯顿·马丁", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "阿斯顿·马丁", "icon": "https://dimg04.c-ctrip.com/images/0R419120009gwx9cqBA77.png"}], "groupCode": "BrandGroup_a0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_BEIJING汽车", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "BEIJING汽车", "icon": "https://dimg04.c-ctrip.com/images/0R46u120009gwv7gj857B.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_保时捷", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "保时捷", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_别克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_北京", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "北京", "icon": "//pages.c-ctrip.com/carisd/brandlogo/beijin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔腾", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "奔腾", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benteng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔驰", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝马", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝骏", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "宝骏", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baojun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宾利", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "宾利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/binli.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_标致", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "标致", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biaozhi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_比亚迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "比亚迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长城", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "长城", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changcheng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "长安", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_c0", "itemCode": "BrandGroup_c0_长安欧尚", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "长安欧尚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/changanoushang.png"}], "groupCode": "BrandGroup_c0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风风神", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "东风风神", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengshen.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_东风风行", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "东风风行", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dongfengfengxin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_德宝", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "德宝"}, {"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_道奇", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "道奇", "icon": "//pages.c-ctrip.com/carisd/brandlogo/daoqi.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_法拉利", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "法拉利", "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_福特", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "福特", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}], "groupCode": "BrandGroup_f0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_光冈", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "光冈", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guagngang.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽传祺", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "广汽传祺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_高合汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "高合汽车", "icon": "https://dimg04.c-ctrip.com/images/0yc6a12000aom31vy37E4.png"}], "groupCode": "BrandGroup_g0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_华晨新日", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "华晨新日", "icon": "https://dimg04.c-ctrip.com/images/0R472120009gwv9rxB7E1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_合创", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "合创"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_哈弗", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "哈弗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_海马", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "海马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_红旗", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "红旗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}], "groupCode": "BrandGroup_h0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_Jeep", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_几何汽车", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "几何汽车", "icon": "https://dimg04.c-ctrip.com/images/0R43t120009gwv73p44FD.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_吉利汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "吉利汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷豹", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "捷豹", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiebao.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷达", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "捷达", "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷途", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "捷途", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jietu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_极氪", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "极氪", "icon": "https://dimg04.c-ctrip.com/images/0yc0x12000aom51we094D.png"}, {"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_江淮瑞风", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "江淮瑞风", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jianghuan.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_k0", "itemCode": "BrandGroup_k0_克罗迪", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "克罗迪"}, {"sortNum": 4, "groupCode": "BrandGroup_k0", "itemCode": "BrandGroup_k0_凯迪拉克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "凯迪拉克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "groupCode": "BrandGroup_k0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_兰博基尼", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "兰博基尼", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_劳斯莱斯", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "劳斯莱斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_岚图汽车", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "岚图汽车", "icon": "https://dimg04.c-ctrip.com/images/0R45a120009gwvmb34A33.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_林肯", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "林肯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/linken.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_理想汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "理想汽车", "icon": "https://dimg04.fws.qa.nt.ctripcorp.com/images/0yc6i1200000ieok0AE70.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_路特斯", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "路特斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lutesi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_路虎", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "路虎", "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_零跑汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "零跑汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingpaoqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_雷克萨斯", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "雷克萨斯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_l0", "itemCode": "BrandGroup_l0_领克", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "领克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingke.png"}], "groupCode": "BrandGroup_l0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_MINI", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "MINI", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_名爵", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "名爵", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mingjue.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_玛莎拉蒂", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "玛莎拉蒂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_迈凯伦", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "迈凯伦", "icon": "//pages.c-ctrip.com/carisd/brandlogo/maikailun.png"}, {"sortNum": 4, "groupCode": "BrandGroup_m0", "itemCode": "BrandGroup_m0_马自达", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "马自达", "icon": "//pages.c-ctrip.com/carisd/brandlogo/mazida.png"}], "groupCode": "BrandGroup_m0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_n0", "itemCode": "BrandGroup_n0_哪吒汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "哪吒汽车", "icon": "https://dimg04.c-ctrip.com/images/0R46n120009gwvgrmC0A4.png"}], "groupCode": "BrandGroup_n0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_o0", "itemCode": "BrandGroup_o0_欧拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "欧拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/oula.png"}], "groupCode": "BrandGroup_o0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_p0", "itemCode": "BrandGroup_p0_Polestar极星", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "Polestar极星", "icon": "https://dimg04.c-ctrip.com/images/0R43g120009gwuo9r0192.png"}], "groupCode": "BrandGroup_p0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_前途", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "前途", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiantu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_启辰", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "启辰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qicheng.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_奇瑞", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "奇瑞", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qirui.png"}, {"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_起亚", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "起亚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "groupCode": "BrandGroup_q0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_日产", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "日产", "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"sortNum": 4, "groupCode": "BrandGroup_r0", "itemCode": "BrandGroup_r0_荣威", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "荣威", "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "groupCode": "BrandGroup_r0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_smart", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "smart", "icon": "//pages.c-ctrip.com/carisd/brandlogo/smart.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_上汽大通MAXUS", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "上汽大通MAXUS", "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_思铭", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "思铭", "icon": "//pages.c-ctrip.com/carisd/brandlogo/siming.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_斯巴鲁", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "斯巴鲁", "icon": "//pages.c-ctrip.com/carisd/brandlogo/sibalu.png"}, {"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_赛麟", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "赛麟", "icon": "//pages.c-ctrip.com/carisd/brandlogo/sailin.png"}], "groupCode": "BrandGroup_s0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_坦克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "坦克", "icon": "https://dimg04.c-ctrip.com/images/0yc3612000aoma5tvACF1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_特斯拉", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "特斯拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_腾势", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "腾势", "icon": "//pages.c-ctrip.com/carisd/brandlogo/shiteng.png"}], "groupCode": "BrandGroup_t0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_五菱汽车", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "五菱汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/wulingqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_威马汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "威马汽车", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weimaqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_沃尔沃", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "沃尔沃", "icon": "//pages.c-ctrip.com/carisd/brandlogo/woerwo.png"}, {"sortNum": 4, "groupCode": "BrandGroup_w0", "itemCode": "BrandGroup_w0_蔚来", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "蔚来", "icon": "//pages.c-ctrip.com/carisd/brandlogo/weilai.png"}], "groupCode": "BrandGroup_w0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小米汽车", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "小米汽车"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_小鹏", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "小鹏", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiaopengqiche.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_现代", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "现代", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪佛兰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "雪佛兰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_依维柯", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "依维柯", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yikewei.png"}, {"sortNum": 4, "groupCode": "BrandGroup_y0", "itemCode": "BrandGroup_y0_英菲尼迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "英菲尼迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/yingfeinidi.png"}], "groupCode": "BrandGroup_y0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_AITO问界", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "AITO问界"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_SONGSAN MOTORS", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "SONGSAN MOTORS"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_东风EV新能源", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "东风EV新能源"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_吉利几何", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "吉利几何"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_智己汽车", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "智己汽车"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_江淮钇为", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "江淮钇为"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_电动屋", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "电动屋"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_长安启源", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "长安启源"}, {"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_魏牌", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "魏牌"}], "groupCode": "BrandGroup_z0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "0-50", "sortNum": 1, "name": "¥50以下", "groupCode": "Price", "itemCode": "Price_0-50"}, {"code": "50-100", "sortNum": 2, "name": "¥50-100", "groupCode": "Price", "itemCode": "Price_50-100"}, {"code": "100-200", "sortNum": 3, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-99999", "sortNum": 4, "name": "¥200以上", "groupCode": "Price", "itemCode": "Price_200-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "PickReturn", "quickSortNum": 1, "mark": "15分钟内", "itemCode": "PickReturn_StationPR", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "positionCode": "1", "name": "站内取车"}, {"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_FreePickupOnDoor", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 2, "name": "免费送车上门"}, {"sortNum": 2, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 3, "groupCode": "PickReturn", "itemCode": "PickReturn_CostPickupOnDoor", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "收费送车上门"}, {"sortNum": 4, "groupCode": "PickReturn", "itemCode": "PickReturn_FreeShuttle", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "免费接至门店取车"}, {"sortNum": 5, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupSelf", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "自行到店取车"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 4, "positionCode": "4", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "信用免押"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 6, "groupCode": "StoreService", "quickSortNum": 2, "positionCode": "4", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SelfService", "quickSortNum": 1, "positionCode": "3", "itemCode": "SelfService_Support", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 2, "groupCode": "SelfService", "itemCode": "SelfService_UnSupport", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "非自助取还"}], "groupCode": "SelfService", "shortName": "自助取还", "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "Promotion", "quickSortNum": 13, "positionCode": "2", "itemCode": "Promotion_3783", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "黄金贵宾"}, {"sortNum": 4, "selectedIcon": "https://dimg04.c-ctrip.com/images/0416n12000bsyl15v069A.png", "groupCode": "Promotion", "quickSortNum": 2, "positionCode": "2", "itemCode": "Promotion_3752", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "", "icon": "https://dimg04.c-ctrip.com/images/0416o12000bsykzj8BFD7.png"}, {"sortNum": 4, "groupCode": "Promotion", "quickSortNum": 4, "positionCode": "2", "itemCode": "Promotion_3782", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "超级品牌周"}, {"sortNum": 4, "selectedIcon": "https://dimg04.c-ctrip.com/images/0416612000elk9kmj8630.png", "groupCode": "Promotion", "quickSortNum": 3, "positionCode": "2", "itemCode": "Promotion_4425", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 2, "name": "", "icon": "https://dimg04.c-ctrip.com/images/0411q12000elk9ilu6AB7.png"}], "groupCode": "Promotion", "shortName": "优惠活动", "bitwiseType": 2, "name": "优惠活动"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_8", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "台胞证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "门店评分", "sortNum": 8, "bitwiseType": 2, "name": "门店评分", "groupCode": "Comment", "filterItems": [{"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "12", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 2, "groupCode": "Comment", "itemCode": "Comment_4.5", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4.5分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}]}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "itemCode": "Vendor_0", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "携程租车中心"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "mark": "hot", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "positionCode": "6", "name": "携程优选", "icon": ""}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13088", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "一嗨租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13027", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "凹凸出行"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13031", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "桐叶租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13032", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "明昊租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13033", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "枫叶租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13037", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "车速递租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13067", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "车游天下租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13082", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "凯美租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13092", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "易代步租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13094", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "骑仕租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13119", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "懒人行租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30004", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "铭轩租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30169", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "峰硕租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30234", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "金晟租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30912", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "卢米租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31092", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "树德出行"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_31279", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "黑桃壹租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32231", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "泰信吉租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_32498", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "么么达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_33419", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "欣岳美行租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_46492", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "漫自由租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_47522", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "祥驰租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_53893", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "三亚世纪联合租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_57671", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "捷安利达租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_58487", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "丰田海南出行"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61365", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "普信租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61372", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "程硕租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_61659", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "旺亚租车"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61831", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "金达莱租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61937", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "锋达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61951", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "鑫旺达租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_61953", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "三亚易云租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62099", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "宏驰智行租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62115", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "三亚启航租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62119", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "三鹤租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62167", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "玖捌陆租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62267", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "琼驰租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62305", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "皖太租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62534", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "租车宝测试账号"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_62863", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "文东租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63460", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "小米租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_63836", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "租租侠租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_64662", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "潆莹租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_66324", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "环岛租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_66614", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "你我他租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_66708", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "旭辰租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_67709", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "三亚神风租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_69280", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "百募租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_69287", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "华鑫海租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_70698", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "陵水铭途租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_71599", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "日之星丰田租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_73265", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "车之美租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_74373", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "海南中进租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_74569", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "河北唐亚租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_74573", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "器车出行"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_76661", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "振亚租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_76903", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "如亚租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_77151", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "金晟利租车"}, {"sortNum": 8, "groupCode": "Vendor_1", "itemCode": "Vendor_78571", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "汪澜租车"}], "groupCode": "Vendor_1", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_78577", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "夏末微凉租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_78579", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "优享旅途租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_79485", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "立强租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_79695", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "炜晨租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_79749", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "三亚大拇指租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_79797", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "麒麟火租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80127", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "三亚新概念租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80147", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "三亚浩宇租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80427", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "名都租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80431", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "小飞侠租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80535", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "汇驰租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80545", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "盛兴隆租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80557", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "海南信租租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80559", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "星锐租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_80777", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "照成租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81003", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "琼州租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81525", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "钰鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81827", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "京海租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81829", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "诚航租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81831", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "潮人商旅租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81889", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "八骏马租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_81931", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "博利租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_82105", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "三亚旅途中租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_82153", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "四季嘉行租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_82163", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "启瑞盛租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_82231", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "云超租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_82305", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "畅行无忧租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_82571", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "博之纳租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_82731", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "海南途达租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_82819", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "助旅租车"}, {"sortNum": 8, "groupCode": "Vendor_2", "itemCode": "Vendor_82843", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "名仕租车"}], "groupCode": "Vendor_2", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15000088", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "盛泽租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15000258", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "三亚五二零租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15000269", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "海立达租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15000295", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "途新租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15000361", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "海南点赞租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15000935", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "众横租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15000981", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "轩宇租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001022", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "世通出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001073", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "海途租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001163", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "欣博祥租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001186", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "北蒙租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001194", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "壹优租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001199", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "逍遥租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001200", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "领路者租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001202", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "海南龙驰租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001218", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "超联汇租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001227", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "卓誉租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001296", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "钱雨租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001307", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "艾思租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001308", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "纳贝拉租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_15001346", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "溶阔租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_82909", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "五行租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_82987", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "海程租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_83141", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "京海亚租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_83173", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "军盛租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_83257", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "天际线超跑租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_83291", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "豪享荟租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_83315", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "顺利出行"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_83386", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "虫子邦租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_83528", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "钧通租车"}, {"sortNum": 8, "groupCode": "Vendor_3", "itemCode": "Vendor_84185", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "龙运三亚租车"}], "groupCode": "Vendor_3", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001351", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "海南松舍租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001364", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "方达租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001375", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "佰隆租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001376", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "北新租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001377", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "世海租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001381", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "海南友途租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001440", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "皓轩跑车俱乐部租车 "}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001447", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "摇个车租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001448", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "壹阳租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001454", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "邦尼租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001457", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "新奇租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001460", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "黑娃租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001466", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "锦珩租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001504", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "流浪者租车 "}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001513", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "野涵租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001520", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "海南启航租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001538", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "三亚威途租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001590", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "三亚立行租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001591", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "瑞光租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001608", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "好鑫情租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001613", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "海南龙运租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001630", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "铭鸿租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001632", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "众腾租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001635", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "麒麟恒泰租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001639", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "森燚租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001655", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "恒泰租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001665", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "悠逸租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001684", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "海南玖玖租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001695", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "开心果租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001725", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "安鑫莱租车"}, {"sortNum": 8, "groupCode": "Vendor_4", "itemCode": "Vendor_15001801", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "海南易安达租车"}], "groupCode": "Vendor_4", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15001806", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "三亚百鸿租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15001817", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "鸿发租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15001824", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "三亚鼎豪租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15001827", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "三亚永捷租车 "}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15001863", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "琼城租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15001908", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "小龙人租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15001913", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "木沐租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15001919", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "安途生汽车租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15002466", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "鲁运昌通出行"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15002555", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "轻松无忧租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15002585", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "三亚顺强租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15002604", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "鑫风向出行"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15002712", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "蜜丝租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15002755", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "悦萌动租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15002784", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "晟亚租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15002974", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "博格达租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15002979", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "好好超跑租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003040", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "栖橙租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003234", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "三亚融泽租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003255", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "三亚橘子租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003348", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "海南世纪出行"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003443", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "云行租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003472", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "蚂蚁猪车出行"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003495", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "君峰租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003609", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "热爱租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003622", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "凯虹租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003632", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "国澳租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003656", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "冀邯出行"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003675", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "永宏租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003690", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "威克士租车"}, {"sortNum": 8, "groupCode": "Vendor_5", "itemCode": "Vendor_15003709", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "铭途租车"}], "groupCode": "Vendor_5", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15003729", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "十八度阳光出行"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15003741", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "赛富德租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15003752", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "鑫浩租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15003785", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "友嘉租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15003850", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "通耀租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15003910", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "海南金域租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15003922", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "火火超跑俱乐部租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15003935", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "盛京出行"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15003981", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "汶汶出行"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004001", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "腾途出行"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004027", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "皖车汇出行"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004045", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "海南嘉桐租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004097", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "超速度租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004132", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "鑫通商旅租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004136", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "礼享智行租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004152", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "陇鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004163", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "EVCARD租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004165", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "皖阜租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004183", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "三亚楠哥租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004240", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "宜行租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004306", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "亿梦出行"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004351", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "耀东方租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004400", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "巨量租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004427", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "博腾出行"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004455", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "博汇天下租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004515", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "百亿租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004520", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "锐恒租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004534", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "镨森租车"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004595", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "郸盛出行"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004604", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "盛鸿轩出行"}, {"sortNum": 8, "groupCode": "Vendor_6", "itemCode": "Vendor_15004610", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "聚成超跑俱乐部租车"}], "groupCode": "Vendor_6", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004617", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "怪兽超跑出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004644", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "及客租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004656", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "鑫刊出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004673", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "顺椰出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004677", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "启航商旅租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004686", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "山与海出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004703", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "海南大脸猫租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004707", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "驰鑫租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004760", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "诚扬租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004765", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "潜飞出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004773", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "六六出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004807", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "三亚租呗租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004977", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "年旭租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15004979", "binaryDigit": 8192, "isQuickItem": false, "bitwiseType": 2, "name": "海芝梦租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005023", "binaryDigit": 16384, "isQuickItem": false, "bitwiseType": 2, "name": "林福租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005072", "binaryDigit": 32768, "isQuickItem": false, "bitwiseType": 2, "name": "墨源租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005104", "binaryDigit": 65536, "isQuickItem": false, "bitwiseType": 2, "name": "仙雅租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005106", "binaryDigit": 131072, "isQuickItem": false, "bitwiseType": 2, "name": "徽亚出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005174", "binaryDigit": 262144, "isQuickItem": false, "bitwiseType": 2, "name": "海南千汇出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005203", "binaryDigit": 524288, "isQuickItem": false, "bitwiseType": 2, "name": "瑾凡租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005238", "binaryDigit": 1048576, "isQuickItem": false, "bitwiseType": 2, "name": "路晨商旅租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005279", "binaryDigit": 2097152, "isQuickItem": false, "bitwiseType": 2, "name": "博然租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005283", "binaryDigit": 4194304, "isQuickItem": false, "bitwiseType": 2, "name": "亿喆出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005408", "binaryDigit": 8388608, "isQuickItem": false, "bitwiseType": 2, "name": "桐叶租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005423", "binaryDigit": 16777216, "isQuickItem": false, "bitwiseType": 2, "name": "诚功租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005676", "binaryDigit": 33554432, "isQuickItem": false, "bitwiseType": 2, "name": "心航租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005698", "binaryDigit": 67108864, "isQuickItem": false, "bitwiseType": 2, "name": "塞纳超跑俱乐部租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005740", "binaryDigit": 134217728, "isQuickItem": false, "bitwiseType": 2, "name": "三亚臻鑫汽车出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005781", "binaryDigit": 268435456, "isQuickItem": false, "bitwiseType": 2, "name": "安迪出行"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005851", "binaryDigit": 536870912, "isQuickItem": false, "bitwiseType": 2, "name": "凯福华租车"}, {"sortNum": 8, "groupCode": "Vendor_7", "itemCode": "Vendor_15005890", "binaryDigit": 1073741824, "isQuickItem": false, "bitwiseType": 2, "name": "鑫和润租车"}], "groupCode": "Vendor_7", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15005984", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "徽蕴租车"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006124", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "驿合顺通出行"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006174", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "海南奥朗国际租车"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006261", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "千里行出行"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006310", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "鸿吉亚租车"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006318", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "海南潮人租车"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006333", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "无忧九州租车"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006407", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "腾新出行"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006412", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "盛竹租车"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006460", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 2, "name": "尔和租车"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006668", "binaryDigit": 1024, "isQuickItem": false, "bitwiseType": 2, "name": "嘉旅出行"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006693", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 2, "name": "念念出行"}, {"sortNum": 8, "groupCode": "Vendor_8", "itemCode": "Vendor_15006710", "binaryDigit": 4096, "isQuickItem": false, "bitwiseType": 2, "name": "木马租车"}], "groupCode": "Vendor_8", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "4f25fA74363WN4k09hWP", "timeInterval": 1802.2421875, "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 136, "amountDesc": "¥136", "name": "租车费"}, {"code": "11037", "amount": 119, "amountDesc": "¥119", "name": "优惠券"}, {"code": "4425", "amount": 17, "amountDesc": "¥17", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 68, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 80, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥216", "subAmount": 216, "name": "总价", "amountStr": "¥80"}], "reference": {"vehicleCode": "0", "rStoreCode": "78", "packageId": "Secure", "pLev": 129, "comPriceCode": "eyJzZWxsZXJpZCI6MTEwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6Mjc2MSwiYWN0Z2V0aWQiOjI3NjEsImFjdG9mZmlkIjoyNzYxLCJjYXJ0eXBlaWQiOjE4NTM2LCJ0b3RhbCI6MjE2LCJ0aW1lIjoxNzIzMTg3NTEwfQ==", "bizVendorCode": "13031", "pStoreCode": "78", "packageType": 1, "priceVersion": "SH-PRICEVERSION_NzhfNDEzOV8xXzY4LjBfMTM2LjBfMC4wXzIxNi4wXzBfODAuMF8wXzBfMC4wXzAuMF82MC4wXzIwLjBfMF8wXzE1MjUzOQ==", "sendTypeForPickUpCar": 0, "skuId": 152539, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 129, "vendorCode": "13031", "vendorVehicleCode": "18536"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13094", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188084", "skuId": 6900280, "bizVendorCode": "SD3866"}}, {"reference": {"vendorCode": "74573", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114529", "skuId": 40565344, "bizVendorCode": "SD4701"}}, {"reference": {"vendorCode": "53893", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107119", "skuId": 1860920, "bizVendorCode": "SD3942"}}, {"reference": {"vendorCode": "13082", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183936", "skuId": 6901044, "bizVendorCode": "SD3926"}}, {"reference": {"vendorCode": "15001538", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182401", "skuId": 4929070, "bizVendorCode": "SD8506"}}, {"reference": {"vendorCode": "74373", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116523", "skuId": 1965861, "bizVendorCode": "SD6573"}}, {"reference": {"vendorCode": "80431", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114886", "skuId": 1913476, "bizVendorCode": "SD4849"}}, {"reference": {"vendorCode": "15005072", "vehicleCode": "0", "packageType": 1, "pStoreCode": "421268", "skuId": 29681870, "bizVendorCode": "SD12256"}}, {"reference": {"vendorCode": "61831", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114315", "skuId": 1911940, "bizVendorCode": "SD4387"}}, {"reference": {"vendorCode": "46492", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106965", "skuId": 28097714, "bizVendorCode": "SD3933"}}, {"reference": {"vendorCode": "15006407", "vehicleCode": "0", "packageType": 0, "pStoreCode": "834184", "skuId": 55066628, "bizVendorCode": "SD13636"}}, {"reference": {"vendorCode": "61659", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114307", "skuId": 52104347, "bizVendorCode": "SD4382"}}, {"reference": {"vendorCode": "13032", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2961", "skuId": 227977, "bizVendorCode": "SD3012"}}, {"reference": {"vendorCode": "13067", "vehicleCode": "0", "packageType": 1, "pStoreCode": "67462", "skuId": 787013, "bizVendorCode": "SD7763"}}, {"reference": {"vendorCode": "80559", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116966", "skuId": 1959530, "bizVendorCode": "SD5523"}}, {"reference": {"vendorCode": "30234", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117740", "skuId": 1972046, "bizVendorCode": "SD5727"}}, {"reference": {"vendorCode": "82987", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107759", "skuId": 24656409, "bizVendorCode": "SD3850"}}, {"reference": {"vendorCode": "33419", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115913", "skuId": 2704960, "bizVendorCode": "SD5379"}}, {"reference": {"vendorCode": "15004677", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326481", "skuId": 25267446, "bizVendorCode": "SD11817"}}, {"reference": {"vendorCode": "15005890", "vehicleCode": "0", "packageType": 0, "pStoreCode": "740013", "skuId": 47726841, "bizVendorCode": "SD13098"}}, {"reference": {"vendorCode": "62863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114447", "skuId": 1919961, "bizVendorCode": "SD4445"}}, {"reference": {"vendorCode": "15001163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251411", "skuId": 54499593, "bizVendorCode": "SD8119"}}, {"reference": {"vendorCode": "62115", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115473", "skuId": 1908499, "bizVendorCode": "SD4407"}}, {"reference": {"vendorCode": "15004644", "vehicleCode": "0", "packageType": 1, "pStoreCode": "323352", "skuId": 24954791, "bizVendorCode": "SD11777"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减136", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "4139", "highestPrice": 379, "rCoup": 0, "minDPrice": 0, "pWay": "可选：免费站内取还车", "vehicleKey": "0_4139_", "hot": 0, "minTPrice": 80, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 0, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["13031_0_78_78"], "introduce": "当前车型最低价"}, "minDOrinPrice": 68, "isEasy": true, "isCredit": true, "maximumCommentCount": 50370, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 25, "minTOrinPrice": 216}, {"groupSort": 3, "lowestPrice": 46, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 124, "detail": [{"code": "1001", "amount": 596, "amountDesc": "¥596", "name": "租车费"}, {"code": "11037", "amount": 400, "amountDesc": "¥400", "name": "优惠券"}, {"code": "4425", "amount": 72, "amountDesc": "¥72", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥62", "originalDailyPrice": 298, "subAmount": 62, "name": "车辆租金", "amountStr": "¥124"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 224, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥696", "subAmount": 696, "name": "总价", "amountStr": "¥224"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "packageId": "sec", "pLev": 1249907, "comPriceCode": "[c]NTA0fDY5MDJ8MjAyMC4wMC0yMSA0LTA4MDowMDAwOjAmJjEmJjI5OGUkMjBmYWxzOC0yMjI0LTAwMDowIDAwOjgmJjEwJjI5c2UkfCZmYWwmMiYyMTAwMTk2JDE5OCY1MSYyMDAwMyYyMC4wLjAwJjAyJjIwJDEwMDAmOCY0MC4kfDIwMC4wMDgtMjEyNC0wMzA6MCAxOToyNC0wMCYyMCAxOTo4LTIzMHwyMDMwOjA4LTA5MjQtMDExOjUgMTU6AAAAADEAAAA=", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzUzNzRfMV8yOThfNTk2XzI5OF82OTYuMDBfNjIuMF8yMjQuMF8wXzBfMC4wXzAuMF84MC4wMF8yMC4wMF8wLjAwXzAuMDBfNjkwMjUwNA==", "sendTypeForPickUpCar": 0, "skuId": 6902504, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1249907, "vendorCode": "13092", "vendorVehicleCode": "353_3908_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "32231", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107102", "skuId": 9034110, "bizVendorCode": "SD8256"}}, {"reference": {"vendorCode": "13094", "vehicleCode": "0", "packageType": 0, "pStoreCode": "188084", "skuId": 6900323, "bizVendorCode": "SD3866"}}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 4443845, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "15003443", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247275", "skuId": 44388457, "bizVendorCode": "SD10486"}}, {"reference": {"vendorCode": "15004703", "vehicleCode": "0", "packageType": 0, "pStoreCode": "326964", "skuId": 55094698, "bizVendorCode": "SD11845"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333237, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "15002979", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193972", "skuId": 7955564, "bizVendorCode": "SD10005"}}, {"reference": {"vendorCode": "15003675", "vehicleCode": "0", "packageType": 1, "pStoreCode": "249861", "skuId": 54550560, "bizVendorCode": "SD10729"}}, {"reference": {"vendorCode": "61951", "vehicleCode": "0", "packageType": 0, "pStoreCode": "116225", "skuId": 43663068, "bizVendorCode": "SD6406"}}, {"reference": {"vendorCode": "79695", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107033", "skuId": 1857959, "bizVendorCode": "SD4074"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652846, "bizVendorCode": "SD10997"}}, {"reference": {"vendorCode": "83386", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117466", "skuId": 30593116, "bizVendorCode": "SD5613"}}, {"reference": {"vendorCode": "15005283", "vehicleCode": "0", "packageType": 1, "pStoreCode": "426217", "skuId": 43786779, "bizVendorCode": "SD12471"}}, {"reference": {"vendorCode": "15005423", "vehicleCode": "0", "packageType": 1, "pStoreCode": "615511", "skuId": 41491367, "bizVendorCode": "SD12613"}}, {"reference": {"vendorCode": "62305", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114430", "skuId": 40472125, "bizVendorCode": "SD4416"}}, {"reference": {"vendorCode": "15001504", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182455", "skuId": 54514209, "bizVendorCode": "SD8470"}}, {"reference": {"vendorCode": "15004610", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326551", "skuId": 28311585, "bizVendorCode": "SD7857"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 44386623, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "83141", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106211", "skuId": 43977760, "bizVendorCode": "SD3263"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184270", "skuId": 23289757, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "80545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107268", "skuId": 1860299, "bizVendorCode": "SD4092"}}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 44414126, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "80777", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116983", "skuId": 1959109, "bizVendorCode": "SD6707"}}, {"reference": {"vendorCode": "15003609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250167", "skuId": 45637782, "bizVendorCode": "SD10659"}}, {"reference": {"vendorCode": "79485", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116918", "skuId": 1960040, "bizVendorCode": "SD5486"}}, {"reference": {"vendorCode": "76903", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116598", "skuId": 1977339, "bizVendorCode": "SD6575"}}, {"reference": {"vendorCode": "81831", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115138", "skuId": 44246420, "bizVendorCode": "SD4950"}}, {"reference": {"vendorCode": "15001827", "vehicleCode": "0", "packageType": 0, "pStoreCode": "183883", "skuId": 55253864, "bizVendorCode": "SD8801"}}, {"reference": {"vendorCode": "15004677", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326481", "skuId": 25266858, "bizVendorCode": "SD11817"}}, {"reference": {"vendorCode": "15004595", "vehicleCode": "0", "packageType": 1, "pStoreCode": "322435", "skuId": 24924740, "bizVendorCode": "SD11725"}}, {"reference": {"vendorCode": "15005698", "vehicleCode": "0", "packageType": 1, "pStoreCode": "721176", "skuId": 44553986, "bizVendorCode": "SD12899"}}, {"reference": {"vendorCode": "80557", "vehicleCode": "0", "packageType": 0, "pStoreCode": "114922", "skuId": 23621207, "bizVendorCode": "SD4859"}}, {"reference": {"vendorCode": "15003981", "vehicleCode": "0", "packageType": 1, "pStoreCode": "256579", "skuId": 7634446, "bizVendorCode": "SD11062"}}, {"reference": {"vendorCode": "15001381", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182206", "skuId": 23433306, "bizVendorCode": "SD8343"}}, {"reference": {"vendorCode": "15001665", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183430", "skuId": 7972007, "bizVendorCode": "SD8638"}}, {"reference": {"vendorCode": "15001919", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184166", "skuId": 5092270, "bizVendorCode": "SD8899"}}, {"reference": {"vendorCode": "15001684", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183502", "skuId": 31162111, "bizVendorCode": "SD8656"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减472", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "5374", "highestPrice": 1012, "rCoup": 0, "minDPrice": 62, "pWay": "可选：免费站内取还车", "vehicleKey": "0_5374_", "hot": 0, "minTPrice": 224, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 16, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD8256_0_107102_107102"], "introduce": "当前车型最低价"}, "minDOrinPrice": 298, "isEasy": true, "isCredit": true, "maximumCommentCount": 50370, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 38, "minTOrinPrice": 696}, {"groupSort": 7, "lowestPrice": 62, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 124, "detail": [{"code": "1001", "amount": 596, "amountDesc": "¥596", "name": "租车费"}, {"code": "11037", "amount": 400, "amountDesc": "¥400", "name": "优惠券"}, {"code": "4425", "amount": 72, "amountDesc": "¥72", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥62", "originalDailyPrice": 298, "subAmount": 62, "name": "车辆租金", "amountStr": "¥124"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 224, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥696", "subAmount": 696, "name": "总价", "amountStr": "¥224"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "pLev": 1249907, "comPriceCode": "[c]", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzQ2NjFfMV8yOThfNTk2XzI5OF82OTYuMDBfNjIuMF8yMjQuMF8wXzBfMC4wXzAuMF84MC4wMF8yMC4wMF8wLjAwXzAuMDBfNTUxNjMzMzM=", "sendTypeForPickUpCar": 0, "skuId": 55163333, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1249907, "vendorCode": "13092", "vendorVehicleCode": "85584_43070_pupai"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减472", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "4661", "highestPrice": 62, "rCoup": 0, "minDPrice": 62, "pWay": "可选：免费站内取还车", "vehicleKey": "0_4661_", "hot": 0, "minTPrice": 224, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 2, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 298, "isEasy": false, "isCredit": true, "maximumCommentCount": 44249, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "minTOrinPrice": 696, "isOptim": true}, {"groupSort": 4, "lowestPrice": 62, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 124, "detail": [{"code": "1001", "amount": 596, "amountDesc": "¥596", "name": "租车费"}, {"code": "11037", "amount": 400, "amountDesc": "¥400", "name": "优惠券"}, {"code": "4425", "amount": 72, "amountDesc": "¥72", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥62", "originalDailyPrice": 298, "subAmount": 62, "name": "车辆租金", "amountStr": "¥124"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 224, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥696", "subAmount": 696, "name": "总价", "amountStr": "¥224"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "packageId": "sec", "pLev": 1249907, "comPriceCode": "[c]NTA3fDY5MDJ8MjAyMC4wMC0yMSA0LTA4MDowMDAwOjAmJjEmJjI5OGUkMjBmYWxzOC0yMjI0LTAwMDowIDAwOjgmJjEwJjI5c2UkfCZmYWwmMiYyMTAwMTk2JDE5OCY1MSYyMDAwMyYyMC4wLjAwJjAyJjIwJDEwMDAmOCY0MC4kfDIwMC4wMDgtMjEyNC0wMzA6MCAxOToyNC0wMCYyMCAxOTo4LTIzMHwyMDMwOjA4LTA5MjQtMDExOjUgMTU6AAAAADEAAAA=", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzUyODFfMV8yOThfNTk2XzI5OF82OTYuMDBfNjIuMF8yMjQuMF8wXzBfMC4wXzAuMF84MC4wMF8yMC4wMF8wLjAwXzAuMDBfNjkwMjUwNw==", "sendTypeForPickUpCar": 0, "skuId": 6902507, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1249907, "vendorCode": "13092", "vendorVehicleCode": "356_32522_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15004455", "vehicleCode": "0", "packageType": 1, "pStoreCode": "319453", "skuId": 23766814, "bizVendorCode": "SD11572"}}, {"reference": {"vendorCode": "32498", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107196", "skuId": 1858159, "bizVendorCode": "SD3746"}}, {"reference": {"vendorCode": "74373", "vehicleCode": "0", "packageType": 0, "pStoreCode": "116523", "skuId": 5526854, "bizVendorCode": "SD6573"}}, {"reference": {"vendorCode": "15001591", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182684", "skuId": 7967898, "bizVendorCode": "SD8562"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652853, "bizVendorCode": "SD10997"}}, {"reference": {"vendorCode": "15004610", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326551", "skuId": 28311501, "bizVendorCode": "SD7857"}}, {"reference": {"vendorCode": "79695", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107033", "skuId": 2015498, "bizVendorCode": "SD4074"}}, {"reference": {"vendorCode": "13027", "vehicleCode": "0", "packageType": 0, "pStoreCode": "45956", "skuId": 5543323, "bizVendorCode": "SD3010"}}, {"reference": {"vendorCode": "15004183", "vehicleCode": "0", "packageType": 1, "pStoreCode": "308267", "skuId": 9042349, "bizVendorCode": "SD11286"}}, {"reference": {"vendorCode": "82987", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107759", "skuId": 24656353, "bizVendorCode": "SD3850"}}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 43951027, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 44386812, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15003609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250167", "skuId": 45637817, "bizVendorCode": "SD10659"}}, {"reference": {"vendorCode": "81831", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115138", "skuId": 51447278, "bizVendorCode": "SD4950"}}, {"reference": {"vendorCode": "79485", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116918", "skuId": 1977722, "bizVendorCode": "SD5486"}}, {"reference": {"vendorCode": "15001684", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183502", "skuId": 31162258, "bizVendorCode": "SD8656"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减472", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "5281", "highestPrice": 1012, "rCoup": 0, "minDPrice": 62, "pWay": "可选：免费站内取还车", "vehicleKey": "0_5281_", "hot": 0, "minTPrice": 224, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 4, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 298, "isEasy": true, "isCredit": true, "maximumCommentCount": 44249, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 17, "minTOrinPrice": 696}, {"groupSort": 1, "lowestPrice": 62, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 124, "detail": [{"code": "1001", "amount": 596, "amountDesc": "¥596", "name": "租车费"}, {"code": "11037", "amount": 400, "amountDesc": "¥400", "name": "优惠券"}, {"code": "4425", "amount": 72, "amountDesc": "¥72", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥62", "originalDailyPrice": 298, "subAmount": 62, "name": "车辆租金", "amountStr": "¥124"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 224, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥696", "subAmount": 696, "name": "总价", "amountStr": "¥224"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "packageId": "sec", "pLev": 1249907, "comPriceCode": "[c]NTA4fDY5MDJ8MjAyMC4wMC0yMSA0LTA4MDowMDAwOjAmJjEmJjI5OGUkMjBmYWxzOC0yMjI0LTAwMDowIDAwOjgmJjEwJjI5c2UkfCZmYWwmMiYyMTAwMTk2JDE5OCY1MSYyMDAwMyYyMC4wLjAwJjAyJjIwJDEwMDAmOCY0MC4kfDIwMC4wMDgtMjEyNC0wMzA6MCAxOToyNC0wMCYyMCAxOTo4LTIzMHwyMDMwOjA4LTA5MjQtMDExOjUgMTU6AAAAADEAAAA=", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzU1MjZfMV8yOThfNTk2XzI5OF82OTYuMDBfNjIuMF8yMjQuMF8wXzBfMC4wXzAuMF84MC4wMF8yMC4wMF8wLjAwXzAuMDBfNjkwMjUwOA==", "sendTypeForPickUpCar": 0, "skuId": 6902508, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1249907, "vendorCode": "13092", "vendorVehicleCode": "357_49568_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15004703", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326964", "skuId": 52257052, "bizVendorCode": "SD11845"}}, {"reference": {"vendorCode": "74569", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114549", "skuId": 1920866, "bizVendorCode": "SD4700"}}, {"reference": {"vendorCode": "15005781", "vehicleCode": "0", "packageType": 1, "pStoreCode": "722289", "skuId": 48984636, "bizVendorCode": "SD12983"}}, {"reference": {"vendorCode": "15001590", "vehicleCode": "0", "packageType": 0, "pStoreCode": "182720", "skuId": 6911857, "bizVendorCode": "SD8561"}}, {"reference": {"vendorCode": "15001199", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175820", "skuId": 43673484, "bizVendorCode": "SD8155"}}, {"reference": {"vendorCode": "15004097", "vehicleCode": "0", "packageType": 1, "pStoreCode": "265308", "skuId": 55813010, "bizVendorCode": "SD11195"}}, {"reference": {"vendorCode": "15001346", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181818", "skuId": 23460375, "bizVendorCode": "SD8307"}}, {"reference": {"vendorCode": "15003234", "vehicleCode": "0", "packageType": 1, "pStoreCode": "228908", "skuId": 23034208, "bizVendorCode": "SD10267"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652839, "bizVendorCode": "SD10997"}}, {"reference": {"vendorCode": "83386", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117466", "skuId": 4548622, "bizVendorCode": "SD5613"}}, {"reference": {"vendorCode": "76661", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114559", "skuId": 54551428, "bizVendorCode": "SD4723"}}, {"reference": {"vendorCode": "81827", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117944", "skuId": 54405758, "bizVendorCode": "SD5538"}}, {"reference": {"vendorCode": "15005423", "vehicleCode": "0", "packageType": 1, "pStoreCode": "615511", "skuId": 41491402, "bizVendorCode": "SD12613"}}, {"reference": {"vendorCode": "15003443", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247275", "skuId": 44388464, "bizVendorCode": "SD10486"}}, {"reference": {"vendorCode": "83141", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106211", "skuId": 51794107, "bizVendorCode": "SD3263"}}, {"reference": {"vendorCode": "15004610", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326551", "skuId": 28311599, "bizVendorCode": "SD7857"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 44386581, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "80557", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114922", "skuId": 1916798, "bizVendorCode": "SD4859"}}, {"reference": {"vendorCode": "64662", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106190", "skuId": 46134586, "bizVendorCode": "SD3225"}}, {"reference": {"vendorCode": "78579", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114931", "skuId": 24628360, "bizVendorCode": "SD4755"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184270", "skuId": 7362307, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "62305", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114430", "skuId": 40472202, "bizVendorCode": "SD4416"}}, {"reference": {"vendorCode": "83173", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117434", "skuId": 1958805, "bizVendorCode": "SD5591"}}, {"reference": {"vendorCode": "80545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107268", "skuId": 4874760, "bizVendorCode": "SD4092"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333244, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "62099", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114327", "skuId": 1911553, "bizVendorCode": "SD4406"}}, {"reference": {"vendorCode": "82987", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107759", "skuId": 24656332, "bizVendorCode": "SD3850"}}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 6903934, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15003609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250167", "skuId": 45638377, "bizVendorCode": "SD10659"}}, {"reference": {"vendorCode": "15004979", "vehicleCode": "0", "packageType": 1, "pStoreCode": "417677", "skuId": 46054898, "bizVendorCode": "SD12159"}}, {"reference": {"vendorCode": "13037", "vehicleCode": "0", "packageType": 1, "pStoreCode": "49033", "skuId": 3021399, "bizVendorCode": "SD3009"}}, {"reference": {"vendorCode": "81831", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115138", "skuId": 44246427, "bizVendorCode": "SD4950"}}, {"reference": {"vendorCode": "15005104", "vehicleCode": "0", "packageType": 1, "pStoreCode": "421835", "skuId": 29697802, "bizVendorCode": "SD12290"}}, {"reference": {"vendorCode": "15005676", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625150", "skuId": 52806034, "bizVendorCode": "SD12877"}}, {"reference": {"vendorCode": "15002755", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193338", "skuId": 8277130, "bizVendorCode": "SD9781"}}, {"reference": {"vendorCode": "15001827", "vehicleCode": "0", "packageType": 0, "pStoreCode": "183883", "skuId": 55253878, "bizVendorCode": "SD8801"}}, {"reference": {"vendorCode": "79485", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116918", "skuId": 2237960, "bizVendorCode": "SD5486"}}, {"reference": {"vendorCode": "15004977", "vehicleCode": "0", "packageType": 1, "pStoreCode": "422885", "skuId": 29773409, "bizVendorCode": "SD8985"}}, {"reference": {"vendorCode": "15001801", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183861", "skuId": 29388437, "bizVendorCode": "SD9357"}}, {"reference": {"vendorCode": "15005698", "vehicleCode": "0", "packageType": 1, "pStoreCode": "721176", "skuId": 44554021, "bizVendorCode": "SD12899"}}, {"reference": {"vendorCode": "15003622", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251213", "skuId": 7601077, "bizVendorCode": "SD10675"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 47764662, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15004165", "vehicleCode": "0", "packageType": 0, "pStoreCode": "313013", "skuId": 23284535, "bizVendorCode": "SD11268"}}, {"reference": {"vendorCode": "15001381", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182206", "skuId": 5665590, "bizVendorCode": "SD8343"}}, {"reference": {"vendorCode": "15001684", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183502", "skuId": 31162027, "bizVendorCode": "SD8656"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减472", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "5526", "highestPrice": 1012, "rCoup": 0, "minDPrice": 62, "pWay": "可选：免费站内取还车", "vehicleKey": "0_5526_", "hot": 0, "minTPrice": 224, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 6, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 298, "isEasy": true, "isCredit": true, "maximumCommentCount": 44249, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 46, "minTOrinPrice": 696}, {"groupSort": 6, "lowestPrice": 62, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 124, "detail": [{"code": "1001", "amount": 596, "amountDesc": "¥596", "name": "租车费"}, {"code": "11037", "amount": 400, "amountDesc": "¥400", "name": "优惠券"}, {"code": "4425", "amount": 72, "amountDesc": "¥72", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥62", "originalDailyPrice": 298, "subAmount": 62, "name": "车辆租金", "amountStr": "¥124"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 224, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥696", "subAmount": 696, "name": "总价", "amountStr": "¥224"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "packageId": "sec", "pLev": 1249907, "comPriceCode": "[c]MTI5NjIzMDMwfDIwfDAuMDgtMjEyNC0wMDA6MCAwMDo4JiYxMCYyOXNlJDImZmFsMDgtMjAyNC06MDA6MiAwMDk4JiYwMCYybHNlJDEmZmExJjImfDEwMDU5NiQyOTgmJjEmMjEwMDMmMjAuMC4wMDAwMiYwMCQxLjAwJjImNDAwJHwyODAuMDA4LTIwMjQtOjMwOjEgMTkwMjQtMDAmMjMgMTkwOC0yMDB8MjozMDowOC0wMDI0LToxMTo5IDE1AAAAADUxAAA=", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzQ2NTlfMV8yOThfNTk2XzI5OF82OTYuMDBfNjIuMF8yMjQuMF8wXzBfMC4wXzAuMF84MC4wMF8yMC4wMF8wLjAwXzAuMDBfMjMwMzEyOTY=", "sendTypeForPickUpCar": 0, "skuId": 23031296, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1249907, "vendorCode": "13092", "vendorVehicleCode": "4638_32542_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "64662", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106190", "skuId": 46053715, "bizVendorCode": "SD3225"}}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 6903937, "bizVendorCode": "SD6835"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减472", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "4659", "highestPrice": 375, "rCoup": 0, "minDPrice": 62, "pWay": "可选：免费站内取还车", "vehicleKey": "0_4659_", "hot": 0, "minTPrice": 224, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 12, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 298, "isEasy": true, "isCredit": true, "maximumCommentCount": 44249, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 3, "minTOrinPrice": 696}, {"groupSort": 5, "lowestPrice": 62, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 124, "detail": [{"code": "1001", "amount": 596, "amountDesc": "¥596", "name": "租车费"}, {"code": "11037", "amount": 400, "amountDesc": "¥400", "name": "优惠券"}, {"code": "4425", "amount": 72, "amountDesc": "¥72", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥62", "originalDailyPrice": 298, "subAmount": 62, "name": "车辆租金", "amountStr": "¥124"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 224, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥696", "subAmount": 696, "name": "总价", "amountStr": "¥224"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "pLev": 1249907, "comPriceCode": "[c]Mjg2MDQyOTIyNC0wfHwyMCAwMDo4LTIxMCYyOTAwOjAmZmFsOCYmMTAyNC1zZSQyMiAwMDA4LTIwMCYyOjAwOjEmZmE5OCYmfDEwMGxzZSQyOTgmMSYyJjEwMDM1OTYkMC4wMCYxJjIwMCQxJjIwLjImNDAwMDImODAuMC4wMCYwMjQtMCR8MjEgMTkwOC0yMDAmMjozMDowOC0yMDI0LTozMDozIDE5MDI0LTAwfDI5IDE1MDgtMDUxAAA6MTE6", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzQ2NjBfMV8yOThfNTk2XzI5OF82OTYuMDBfNjIuMF8yMjQuMF8wXzBfMC4wXzAuMF84MC4wMF8yMC4wMF8wLjAwXzAuMDBfNDI5MjI4NjA=", "sendTypeForPickUpCar": 0, "skuId": 42922860, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1249907, "vendorCode": "13092", "vendorVehicleCode": "9282_22892_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "82305", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107190", "skuId": 5697226, "bizVendorCode": "SD3683"}}, {"reference": {"vendorCode": "78579", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114931", "skuId": 1920597, "bizVendorCode": "SD4755"}}, {"reference": {"vendorCode": "15004703", "vehicleCode": "0", "packageType": 0, "pStoreCode": "326964", "skuId": 55459888, "bizVendorCode": "SD11845"}}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 1862272, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 44386735, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 0, "pStoreCode": "184270", "skuId": 55749359, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652860, "bizVendorCode": "SD10997"}}, {"reference": {"vendorCode": "15001632", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183841", "skuId": 7373248, "bizVendorCode": "SD8603"}}, {"reference": {"vendorCode": "64662", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106190", "skuId": 46053813, "bizVendorCode": "SD3225"}}, {"reference": {"vendorCode": "83141", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106211", "skuId": 45998128, "bizVendorCode": "SD3263"}}, {"reference": {"vendorCode": "15005104", "vehicleCode": "0", "packageType": 1, "pStoreCode": "421835", "skuId": 44528527, "bizVendorCode": "SD12290"}}, {"reference": {"vendorCode": "15005203", "vehicleCode": "0", "packageType": 1, "pStoreCode": "424236", "skuId": 43826427, "bizVendorCode": "SD12389"}}, {"reference": {"vendorCode": "15001801", "vehicleCode": "0", "packageType": 0, "pStoreCode": "183861", "skuId": 55275515, "bizVendorCode": "SD9357"}}, {"reference": {"vendorCode": "15002604", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193285", "skuId": 5594161, "bizVendorCode": "SD9625"}}, {"reference": {"vendorCode": "15001684", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183502", "skuId": 31162720, "bizVendorCode": "SD8656"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减472", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "4660", "highestPrice": 1012, "rCoup": 0, "minDPrice": 62, "pWay": "可选：免费站内取还车", "vehicleKey": "0_4660_", "hot": 0, "minTPrice": 224, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 14, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 298, "isEasy": true, "isCredit": true, "maximumCommentCount": 44249, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 16, "minTOrinPrice": 696}, {"groupSort": 2, "lowestPrice": 141, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 282, "detail": [{"code": "1001", "amount": 776, "amountDesc": "¥776", "name": "租车费"}, {"code": "11037", "amount": 400, "amountDesc": "¥400", "name": "优惠券"}, {"code": "4425", "amount": 94, "amountDesc": "¥94", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥141", "originalDailyPrice": 388, "subAmount": 141, "name": "车辆租金", "amountStr": "¥282"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "amount": 80, "amountDesc": "¥80", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 382, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥876", "subAmount": 876, "name": "总价", "amountStr": "¥382"}], "reference": {"vehicleCode": "0", "rStoreCode": "181864", "packageId": "sec", "pLev": 1249907, "comPriceCode": "[c]", "bizVendorCode": "SD11238", "pStoreCode": "181864", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTgxODY0XzE3MzkwXzFfMzg4Xzc3Nl8zODhfODc2LjAwXzE0MS4wXzM4Mi4wXzBfMF8wLjBfMC4wXzgwLjAwXzIwLjAwXzAuMDBfMC4wMF82OTAyNTEx", "sendTypeForPickUpCar": 0, "skuId": 6902511, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1249907, "vendorCode": "13092", "vendorVehicleCode": "360_56446_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "76661", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114559", "skuId": 52806839, "bizVendorCode": "SD4723"}}, {"reference": {"vendorCode": "15004427", "vehicleCode": "0", "packageType": 1, "pStoreCode": "313573", "skuId": 23997947, "bizVendorCode": "SD11544"}}, {"reference": {"vendorCode": "15001520", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182598", "skuId": 44683038, "bizVendorCode": "SD8486"}}, {"reference": {"vendorCode": "15001655", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183428", "skuId": 5005826, "bizVendorCode": "SD8627"}}, {"reference": {"vendorCode": "15004703", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326964", "skuId": 52256086, "bizVendorCode": "SD11845"}}, {"reference": {"vendorCode": "15006333", "vehicleCode": "0", "packageType": 1, "pStoreCode": "831181", "skuId": 52021089, "bizVendorCode": "SD13557"}}, {"reference": {"vendorCode": "15003443", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247275", "skuId": 44388219, "bizVendorCode": "SD10486"}}, {"reference": {"vendorCode": "15001827", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183883", "skuId": 47735192, "bizVendorCode": "SD8801"}}, {"reference": {"vendorCode": "15003675", "vehicleCode": "0", "packageType": 1, "pStoreCode": "249861", "skuId": 54532129, "bizVendorCode": "SD10729"}}, {"reference": {"vendorCode": "80127", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116955", "skuId": 55333258, "bizVendorCode": "SD6673"}}, {"reference": {"vendorCode": "15004097", "vehicleCode": "0", "packageType": 1, "pStoreCode": "265308", "skuId": 55812821, "bizVendorCode": "SD11195"}}, {"reference": {"vendorCode": "15001635", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183156", "skuId": 31187815, "bizVendorCode": "SD8606"}}, {"reference": {"vendorCode": "15003234", "vehicleCode": "0", "packageType": 1, "pStoreCode": "228908", "skuId": 6400994, "bizVendorCode": "SD10267"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652832, "bizVendorCode": "SD10997"}}, {"reference": {"vendorCode": "15004520", "vehicleCode": "0", "packageType": 1, "pStoreCode": "319992", "skuId": 24320003, "bizVendorCode": "SD11644"}}, {"reference": {"vendorCode": "80557", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114922", "skuId": 2053135, "bizVendorCode": "SD4859"}}, {"reference": {"vendorCode": "81827", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117944", "skuId": 54405681, "bizVendorCode": "SD5538"}}, {"reference": {"vendorCode": "61372", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107352", "skuId": 24304057, "bizVendorCode": "SD3955"}}, {"reference": {"vendorCode": "30004", "vehicleCode": "0", "packageType": 0, "pStoreCode": "106808", "skuId": 2704910, "bizVendorCode": "SD3705"}}, {"reference": {"vendorCode": "15001460", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182413", "skuId": 54513152, "bizVendorCode": "SD8423"}}, {"reference": {"vendorCode": "64662", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106190", "skuId": 46053876, "bizVendorCode": "SD3225"}}, {"reference": {"vendorCode": "15006693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1486563", "skuId": 55154891, "bizVendorCode": "SD13949"}}, {"reference": {"vendorCode": "81829", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115233", "skuId": 24656864, "bizVendorCode": "SD4949"}}, {"reference": {"vendorCode": "15002555", "vehicleCode": "0", "packageType": 1, "pStoreCode": "189209", "skuId": 6904666, "bizVendorCode": "SD9573"}}, {"reference": {"vendorCode": "15005984", "vehicleCode": "0", "packageType": 1, "pStoreCode": "741168", "skuId": 49231848, "bizVendorCode": "SD13194"}}, {"reference": {"vendorCode": "15003709", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250114", "skuId": 43671755, "bizVendorCode": "SD10763"}}, {"reference": {"vendorCode": "83386", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117466", "skuId": 5245947, "bizVendorCode": "SD5613"}}, {"reference": {"vendorCode": "82571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115257", "skuId": 2672662, "bizVendorCode": "SD5030"}}, {"reference": {"vendorCode": "15005283", "vehicleCode": "0", "packageType": 1, "pStoreCode": "426217", "skuId": 30648682, "bizVendorCode": "SD12471"}}, {"reference": {"vendorCode": "15003472", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247490", "skuId": 24905231, "bizVendorCode": "SD10518"}}, {"reference": {"vendorCode": "15005203", "vehicleCode": "0", "packageType": 1, "pStoreCode": "424236", "skuId": 43141869, "bizVendorCode": "SD12389"}}, {"reference": {"vendorCode": "15001376", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181711", "skuId": 44383137, "bizVendorCode": "SD8338"}}, {"reference": {"vendorCode": "15004610", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326551", "skuId": 28311606, "bizVendorCode": "SD7857"}}, {"reference": {"vendorCode": "79695", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107033", "skuId": 4989985, "bizVendorCode": "SD4074"}}, {"reference": {"vendorCode": "15001504", "vehicleCode": "0", "packageType": 0, "pStoreCode": "182455", "skuId": 54514132, "bizVendorCode": "SD8470"}}, {"reference": {"vendorCode": "78579", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114931", "skuId": 2288656, "bizVendorCode": "SD4755"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184270", "skuId": 7334062, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "15004979", "vehicleCode": "0", "packageType": 1, "pStoreCode": "417677", "skuId": 46055647, "bizVendorCode": "SD12159"}}, {"reference": {"vendorCode": "62305", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114430", "skuId": 40472419, "bizVendorCode": "SD4416"}}, {"reference": {"vendorCode": "82231", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117286", "skuId": 3899348, "bizVendorCode": "SD6143"}}, {"reference": {"vendorCode": "80545", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107268", "skuId": 5720491, "bizVendorCode": "SD4092"}}, {"reference": {"vendorCode": "15002784", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193192", "skuId": 5576527, "bizVendorCode": "SD9809"}}, {"reference": {"vendorCode": "82843", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115278", "skuId": 54883753, "bizVendorCode": "SD5061"}}, {"reference": {"vendorCode": "15004534", "vehicleCode": "0", "packageType": 1, "pStoreCode": "320167", "skuId": 24948645, "bizVendorCode": "SD11658"}}, {"reference": {"vendorCode": "62267", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116244", "skuId": 42225548, "bizVendorCode": "SD5809"}}, {"reference": {"vendorCode": "62099", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114327", "skuId": 6806767, "bizVendorCode": "SD4406"}}, {"reference": {"vendorCode": "15001200", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175747", "skuId": 55009550, "bizVendorCode": "SD12373"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 45008636, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15004760", "vehicleCode": "0", "packageType": 1, "pStoreCode": "411342", "skuId": 29597177, "bizVendorCode": "SD11924"}}, {"reference": {"vendorCode": "79749", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116843", "skuId": 29339647, "bizVendorCode": "SD6627"}}, {"reference": {"vendorCode": "83173", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117434", "skuId": 4515198, "bizVendorCode": "SD5591"}}, {"reference": {"vendorCode": "15004807", "vehicleCode": "0", "packageType": 1, "pStoreCode": "407345", "skuId": 27862745, "bizVendorCode": "SD11974"}}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 6903946, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15003609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250167", "skuId": 45703848, "bizVendorCode": "SD10659"}}, {"reference": {"vendorCode": "13037", "vehicleCode": "0", "packageType": 1, "pStoreCode": "49033", "skuId": 3021409, "bizVendorCode": "SD3009"}}, {"reference": {"vendorCode": "76903", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116598", "skuId": 6804440, "bizVendorCode": "SD6575"}}, {"reference": {"vendorCode": "80777", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116983", "skuId": 4385500, "bizVendorCode": "SD6707"}}, {"reference": {"vendorCode": "30169", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115880", "skuId": 6802659, "bizVendorCode": "SD5330"}}, {"reference": {"vendorCode": "83141", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106211", "skuId": 45998527, "bizVendorCode": "SD3263"}}, {"reference": {"vendorCode": "15006261", "vehicleCode": "0", "packageType": 0, "pStoreCode": "801739", "skuId": 47360454, "bizVendorCode": "SD13482"}}, {"reference": {"vendorCode": "82731", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115265", "skuId": 5352391, "bizVendorCode": "SD5048"}}, {"reference": {"vendorCode": "15001639", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182996", "skuId": 6801918, "bizVendorCode": "SD8611"}}, {"reference": {"vendorCode": "61659", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114307", "skuId": 44004430, "bizVendorCode": "SD4382"}}, {"reference": {"vendorCode": "81831", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115138", "skuId": 51802542, "bizVendorCode": "SD4950"}}, {"reference": {"vendorCode": "15004977", "vehicleCode": "0", "packageType": 1, "pStoreCode": "422885", "skuId": 29765576, "bizVendorCode": "SD8985"}}, {"reference": {"vendorCode": "15004132", "vehicleCode": "0", "packageType": 1, "pStoreCode": "274422", "skuId": 8277606, "bizVendorCode": "SD11232"}}, {"reference": {"vendorCode": "81931", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117220", "skuId": 2704805, "bizVendorCode": "SD6811"}}, {"reference": {"vendorCode": "15001801", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183861", "skuId": 24662086, "bizVendorCode": "SD9357"}}, {"reference": {"vendorCode": "15001608", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182827", "skuId": 6408770, "bizVendorCode": "SD8579"}}, {"reference": {"vendorCode": "15003040", "vehicleCode": "0", "packageType": 0, "pStoreCode": "248954", "skuId": 39507196, "bizVendorCode": "SD10070"}}, {"reference": {"vendorCode": "15005698", "vehicleCode": "0", "packageType": 1, "pStoreCode": "721176", "skuId": 44554056, "bizVendorCode": "SD12899"}}, {"reference": {"vendorCode": "15001227", "vehicleCode": "0", "packageType": 1, "pStoreCode": "176097", "skuId": 6808123, "bizVendorCode": "SD8186"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 48039713, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15005676", "vehicleCode": "0", "packageType": 1, "pStoreCode": "625150", "skuId": 52806062, "bizVendorCode": "SD12877"}}, {"reference": {"vendorCode": "15006668", "vehicleCode": "0", "packageType": 0, "pStoreCode": "1458752", "skuId": 54509421, "bizVendorCode": "SD13921"}}, {"reference": {"vendorCode": "15001022", "vehicleCode": "0", "packageType": 0, "pStoreCode": "174725", "skuId": 51432984, "bizVendorCode": "SD7980"}}, {"reference": {"vendorCode": "15003495", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247802", "skuId": 6825346, "bizVendorCode": "SD10542"}}, {"reference": {"vendorCode": "15001684", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183502", "skuId": 31161894, "bizVendorCode": "SD8656"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减494", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "17390", "highestPrice": 1012, "rCoup": 0, "minDPrice": 141, "pWay": "可选：免费站内取还车", "vehicleKey": "0_17390_", "hot": 0, "minTPrice": 382, "lowestDistance": 0, "group": 820, "type": 0, "sortNum": 18, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11238_0_181864_181864"], "introduce": "当前车型最低价"}, "minDOrinPrice": 388, "isEasy": true, "isCredit": true, "maximumCommentCount": 44249, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 79, "minTOrinPrice": 876}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 416, "amountDesc": "¥416", "name": "租车费"}, {"code": "11037", "amount": 366, "amountDesc": "¥366", "name": "优惠券"}, {"code": "4425", "amount": 50, "amountDesc": "¥50", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 208, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 80, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥496", "subAmount": 496, "name": "总价", "amountStr": "¥80"}], "reference": {"vehicleCode": "0", "rStoreCode": "107119", "packageId": "", "pLev": 23994, "comPriceCode": "[c]MTMwfDE4NTZ8MjAyMC4wMC0yMSA0LTA4MDowMDAwOjAmJjEmJjIwOGUkMjBmYWxzOC0yMjI0LTAwMDowIDAwOjgmJjEwJjIwc2UkfCZmYWwmMiYyMTAwMTE2JDEwOCY0MSYyMDAwMyYyMC4wLjAwJjAyJjIwJDEwMDAmNiYzMC4kfDIwMC4wMDgtMjEyNC0wMzA6MCAxOToyNC0wMCYyMCAxOTo4LTIzMHwyMDMwOjA4LTA5MjQtMDExOjUgMTU6AAAAADEAAAA=", "bizVendorCode": "SD3942", "pStoreCode": "107119", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3MTE5XzUzNzFfMV8yMDhfNDE2XzIwOF80OTYuMDBfMF84MC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF8xODU2MTMw", "sendTypeForPickUpCar": 0, "skuId": 1856130, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 23994, "vendorCode": "53893", "vendorVehicleCode": "20073903"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "32231", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107102", "skuId": 1860282, "bizVendorCode": "SD8256"}}, {"reference": {"vendorCode": "15003752", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250339", "skuId": 6938941, "bizVendorCode": "SD10808"}}, {"reference": {"vendorCode": "15004677", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326481", "skuId": 29112735, "bizVendorCode": "SD11817"}}, {"reference": {"vendorCode": "15000981", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174692", "skuId": 44054620, "bizVendorCode": "SD7724"}}, {"reference": {"vendorCode": "15000088", "vehicleCode": "0", "packageType": 1, "pStoreCode": "161973", "skuId": 24662303, "bizVendorCode": "SD3127"}}, {"reference": {"vendorCode": "15003675", "vehicleCode": "0", "packageType": 1, "pStoreCode": "249861", "skuId": 54538093, "bizVendorCode": "SD10729"}}, {"reference": {"vendorCode": "64662", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106190", "skuId": 46199840, "bizVendorCode": "SD3225"}}, {"reference": {"vendorCode": "81829", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115233", "skuId": 24656738, "bizVendorCode": "SD4949"}}, {"reference": {"vendorCode": "61372", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107352", "skuId": 6868453, "bizVendorCode": "SD3955"}}, {"reference": {"vendorCode": "83291", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115347", "skuId": 1922701, "bizVendorCode": "SD5097"}}, {"reference": {"vendorCode": "15001376", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181711", "skuId": 44383277, "bizVendorCode": "SD8338"}}, {"reference": {"vendorCode": "15003443", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247275", "skuId": 44388240, "bizVendorCode": "SD10486"}}, {"reference": {"vendorCode": "15003785", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250407", "skuId": 46192210, "bizVendorCode": "SD10839"}}, {"reference": {"vendorCode": "15004760", "vehicleCode": "0", "packageType": 1, "pStoreCode": "411342", "skuId": 29612143, "bizVendorCode": "SD11924"}}, {"reference": {"vendorCode": "15004136", "vehicleCode": "0", "packageType": 1, "pStoreCode": "274541", "skuId": 42944798, "bizVendorCode": "SD11239"}}, {"reference": {"vendorCode": "15004979", "vehicleCode": "0", "packageType": 1, "pStoreCode": "417677", "skuId": 46105508, "bizVendorCode": "SD12159"}}, {"reference": {"vendorCode": "15003741", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250168", "skuId": 6909031, "bizVendorCode": "SD3848"}}, {"reference": {"vendorCode": "15001824", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183880", "skuId": 5181255, "bizVendorCode": "SD8797"}}, {"reference": {"vendorCode": "83141", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106211", "skuId": 47584524, "bizVendorCode": "SD3263"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 45008594, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "62305", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114430", "skuId": 40472629, "bizVendorCode": "SD4416"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652678, "bizVendorCode": "SD10997"}}, {"reference": {"vendorCode": "76903", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116598", "skuId": 2412874, "bizVendorCode": "SD6575"}}, {"reference": {"vendorCode": "15004610", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326551", "skuId": 29489783, "bizVendorCode": "SD7857"}}, {"reference": {"vendorCode": "15001919", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184166", "skuId": 5078840, "bizVendorCode": "SD8899"}}, {"reference": {"vendorCode": "76661", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114559", "skuId": 52806860, "bizVendorCode": "SD4723"}}, {"reference": {"vendorCode": "15001200", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175747", "skuId": 54511724, "bizVendorCode": "SD12373"}}, {"reference": {"vendorCode": "15004595", "vehicleCode": "0", "packageType": 1, "pStoreCode": "322435", "skuId": 24875481, "bizVendorCode": "SD11725"}}, {"reference": {"vendorCode": "15002979", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193972", "skuId": 6411219, "bizVendorCode": "SD10005"}}, {"reference": {"vendorCode": "15003622", "vehicleCode": "0", "packageType": 0, "pStoreCode": "251213", "skuId": 7599404, "bizVendorCode": "SD10675"}}, {"reference": {"vendorCode": "80557", "vehicleCode": "0", "packageType": 0, "pStoreCode": "114922", "skuId": 40540396, "bizVendorCode": "SD4859"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 51804047, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15004132", "vehicleCode": "0", "packageType": 1, "pStoreCode": "274422", "skuId": 8179949, "bizVendorCode": "SD11232"}}, {"reference": {"vendorCode": "15003609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250167", "skuId": 52361380, "bizVendorCode": "SD10659"}}, {"reference": {"vendorCode": "15004534", "vehicleCode": "0", "packageType": 1, "pStoreCode": "320167", "skuId": 24950647, "bizVendorCode": "SD11658"}}, {"reference": {"vendorCode": "15005238", "vehicleCode": "0", "packageType": 1, "pStoreCode": "424663", "skuId": 44995182, "bizVendorCode": "SD12420"}}, {"reference": {"vendorCode": "15004604", "vehicleCode": "0", "packageType": 0, "pStoreCode": "322064", "skuId": 27010607, "bizVendorCode": "SD11736"}}, {"reference": {"vendorCode": "81831", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115138", "skuId": 44588139, "bizVendorCode": "SD4950"}}, {"reference": {"vendorCode": "15001801", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183861", "skuId": 24662170, "bizVendorCode": "SD9357"}}, {"reference": {"vendorCode": "15004773", "vehicleCode": "0", "packageType": 0, "pStoreCode": "406995", "skuId": 27780446, "bizVendorCode": "SD11939"}}, {"reference": {"vendorCode": "15001827", "vehicleCode": "0", "packageType": 0, "pStoreCode": "183883", "skuId": 55254704, "bizVendorCode": "SD8801"}}, {"reference": {"vendorCode": "82843", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115278", "skuId": 54883795, "bizVendorCode": "SD5061"}}, {"reference": {"vendorCode": "15001227", "vehicleCode": "0", "packageType": 1, "pStoreCode": "176097", "skuId": 27878432, "bizVendorCode": "SD8186"}}, {"reference": {"vendorCode": "15001375", "vehicleCode": "0", "packageType": 1, "pStoreCode": "248654", "skuId": 54513446, "bizVendorCode": "SD8337"}}, {"reference": {"vendorCode": "15005890", "vehicleCode": "0", "packageType": 0, "pStoreCode": "740013", "skuId": 46127705, "bizVendorCode": "SD13098"}}, {"reference": {"vendorCode": "15006668", "vehicleCode": "0", "packageType": 0, "pStoreCode": "1458752", "skuId": 54509834, "bizVendorCode": "SD13921"}}, {"reference": {"vendorCode": "15001684", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183502", "skuId": 39120803, "bizVendorCode": "SD8656"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减416", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "5371", "highestPrice": 874, "rCoup": 0, "minDPrice": 0, "pWay": "可选：免费站内取还车", "vehicleKey": "0_5371_", "hot": 0, "minTPrice": 80, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 8, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD3942_0_107119_107119"], "introduce": "当前车型最低价"}, "minDOrinPrice": 208, "isEasy": true, "isCredit": true, "maximumCommentCount": 36123, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 48, "minTOrinPrice": 496}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 396, "amountDesc": "¥396", "name": "租车费"}, {"code": "11037", "amount": 348, "amountDesc": "¥348", "name": "优惠券"}, {"code": "4425", "amount": 48, "amountDesc": "¥48", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 198, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 80, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥476", "subAmount": 476, "name": "总价", "amountStr": "¥80"}], "reference": {"vehicleCode": "0", "rStoreCode": "193947", "packageId": "sec", "pLev": 1139587, "comPriceCode": "[c]MTEyMDMwNTQwfDIwfDAuMDgtMjEyNC0wMDA6MCAwMDo4JiYxMCYxOXNlJDImZmFsMDgtMjAyNC06MDA6MiAwMDk4JiYwMCYxbHNlJDEmZmExJjImfDEwMDM5NiQxOTgmJjEmMjEwMDMmMjAuMC4wMDAwMiYwMCQxLjAwJjImMzAwJHwyNjAuMDA4LTIwMjQtOjMwOjEgMTkwMjQtMDAmMjMgMTkwOC0yMDB8MjozMDowOC0wMDI0LToxMTo5IDE1AAAAADUwAAA=", "bizVendorCode": "SD4201", "pStoreCode": "193947", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTkzOTQ3XzExNDlfMV8xOThfMzk2XzE5OF80NzYuMDBfMF84MC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF8zMDU0MTEyMA==", "sendTypeForPickUpCar": 0, "skuId": 30541120, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 1139587, "vendorCode": "13119", "vendorVehicleCode": "2180_32422_pupai"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13082", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183936", "skuId": 6901020, "bizVendorCode": "SD3926"}}, {"reference": {"vendorCode": "81525", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107474", "skuId": 24984772, "bizVendorCode": "SD4102"}}, {"reference": {"vendorCode": "15001199", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175820", "skuId": 46102519, "bizVendorCode": "SD8155"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 47764669, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "13067", "vehicleCode": "0", "packageType": 1, "pStoreCode": "67462", "skuId": 5131449, "bizVendorCode": "SD7763"}}, {"reference": {"vendorCode": "74373", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116523", "skuId": 1968263, "bizVendorCode": "SD6573"}}, {"reference": {"vendorCode": "15004136", "vehicleCode": "0", "packageType": 1, "pStoreCode": "274541", "skuId": 42942355, "bizVendorCode": "SD11239"}}, {"reference": {"vendorCode": "67709", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114723", "skuId": 44384852, "bizVendorCode": "SD4552"}}, {"reference": {"vendorCode": "15001665", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183430", "skuId": 7971986, "bizVendorCode": "SD8638"}}, {"reference": {"vendorCode": "15000088", "vehicleCode": "0", "packageType": 1, "pStoreCode": "161973", "skuId": 24662240, "bizVendorCode": "SD3127"}}, {"reference": {"vendorCode": "81829", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115233", "skuId": 24656787, "bizVendorCode": "SD4949"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 51958362, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15001520", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182598", "skuId": 44682751, "bizVendorCode": "SD8486"}}, {"reference": {"vendorCode": "15001538", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182401", "skuId": 4927938, "bizVendorCode": "SD8506"}}, {"reference": {"vendorCode": "83141", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106211", "skuId": 54532927, "bizVendorCode": "SD3263"}}, {"reference": {"vendorCode": "64662", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106190", "skuId": 46208163, "bizVendorCode": "SD3225"}}, {"reference": {"vendorCode": "15003609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250167", "skuId": 54172168, "bizVendorCode": "SD10659"}}, {"reference": {"vendorCode": "82987", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107759", "skuId": 24656444, "bizVendorCode": "SD3850"}}, {"reference": {"vendorCode": "15001375", "vehicleCode": "0", "packageType": 1, "pStoreCode": "248654", "skuId": 55067468, "bizVendorCode": "SD8337"}}, {"reference": {"vendorCode": "15001684", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183502", "skuId": 39471965, "bizVendorCode": "SD8656"}}, {"reference": {"vendorCode": "33419", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115913", "skuId": 1958696, "bizVendorCode": "SD5379"}}, {"reference": {"vendorCode": "15006460", "vehicleCode": "0", "packageType": 0, "pStoreCode": "1257474", "skuId": 51447166, "bizVendorCode": "SD13696"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减396", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "1149", "highestPrice": 800, "rCoup": 0, "minDPrice": 0, "pWay": "可选：免费站内取还车", "vehicleKey": "0_1149_", "hot": 0, "minTPrice": 80, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 10, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD4201_0_193947_193947"], "introduce": "当前车型最低价"}, "minDOrinPrice": 198, "isEasy": true, "isCredit": true, "maximumCommentCount": 51871, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 23, "minTOrinPrice": 476}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 156, "amountDesc": "¥156", "name": "租车费"}, {"code": "11037", "amount": 137, "amountDesc": "¥137", "name": "优惠券"}, {"code": "4425", "amount": 19, "amountDesc": "¥19", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 78, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 80, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥236", "subAmount": 236, "name": "总价", "amountStr": "¥80"}], "reference": {"vehicleCode": "0", "rStoreCode": "78", "packageId": "Secure", "pLev": 129, "comPriceCode": "eyJzZWxsZXJpZCI6MTEwMSwicGFja2FnZWlkIjoiU2VjdXJlIiwic3RvcmVpZCI6Mjc2MSwiYWN0Z2V0aWQiOjI3NjEsImFjdG9mZmlkIjoyNzYxLCJjYXJ0eXBlaWQiOjE5MDYzLCJ0b3RhbCI6MjM2LCJ0aW1lIjoxNzIzMTg3NTEwfQ==", "bizVendorCode": "13031", "pStoreCode": "78", "packageType": 1, "priceVersion": "SH-PRICEVERSION_NzhfMTc2MzlfMV83OC4wXzE1Ni4wXzAuMF8yMzYuMF8wXzgwLjBfMF8wXzAuMF8wLjBfNjAuMF8yMC4wXzBfMF8yNDkyOTQ4Ng==", "sendTypeForPickUpCar": 0, "skuId": 24929486, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 129, "vendorCode": "13031", "vendorVehicleCode": "19063"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "13119", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193947", "skuId": 41070198, "bizVendorCode": "SD4201"}}, {"reference": {"vendorCode": "13082", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183936", "skuId": 6901062, "bizVendorCode": "SD3926"}}, {"reference": {"vendorCode": "32231", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107102", "skuId": 5305434, "bizVendorCode": "SD8256"}}, {"reference": {"vendorCode": "30912", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115986", "skuId": 6718314, "bizVendorCode": "SD5349"}}, {"reference": {"vendorCode": "13067", "vehicleCode": "0", "packageType": 1, "pStoreCode": "67462", "skuId": 47760945, "bizVendorCode": "SD7763"}}, {"reference": {"vendorCode": "31092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107528", "skuId": 23750371, "bizVendorCode": "SD3882"}}, {"reference": {"vendorCode": "15000088", "vehicleCode": "0", "packageType": 1, "pStoreCode": "161973", "skuId": 24662408, "bizVendorCode": "SD3127"}}, {"reference": {"vendorCode": "15001022", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174725", "skuId": 7673737, "bizVendorCode": "SD7980"}}, {"reference": {"vendorCode": "13032", "vehicleCode": "0", "packageType": 1, "pStoreCode": "2961", "skuId": 24929472, "bizVendorCode": "SD3012"}}, {"reference": {"vendorCode": "15001913", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184113", "skuId": 6904013, "bizVendorCode": "SD8893"}}, {"reference": {"vendorCode": "15003609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250167", "skuId": 51795157, "bizVendorCode": "SD10659"}}, {"reference": {"vendorCode": "15002979", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193972", "skuId": 7391126, "bizVendorCode": "SD10005"}}, {"reference": {"vendorCode": "64662", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106190", "skuId": 46208688, "bizVendorCode": "SD3225"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减156", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "17639", "highestPrice": 253, "rCoup": 0, "minDPrice": 0, "pWay": "可选：免费站内取还车", "vehicleKey": "0_17639_", "hot": 0, "minTPrice": 80, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 20, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["13031_0_78_78"], "introduce": "当前车型最低价"}, "minDOrinPrice": 78, "isEasy": true, "isCredit": true, "maximumCommentCount": 51871, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 14, "minTOrinPrice": 236}, {"groupSort": 0, "lowestPrice": 0, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 0, "detail": [{"code": "1001", "amount": 440, "amountDesc": "¥440", "name": "租车费"}, {"code": "11037", "amount": 387, "amountDesc": "¥387", "name": "优惠券"}, {"code": "4425", "amount": 53, "amountDesc": "¥53", "name": "暑期特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥0", "originalDailyPrice": 220, "subAmount": 0, "name": "车辆租金", "amountStr": "¥0"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 60, "amountStr": "¥60", "detail": [{"code": "1002", "amount": 60, "amountDesc": "¥60", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 80, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥520", "subAmount": 520, "name": "总价", "amountStr": "¥80"}], "reference": {"vehicleCode": "0", "rStoreCode": "107102", "packageId": "", "pLev": 23028, "comPriceCode": "[c]MzE5fDE4NjV8MjAyMC4wMC0yMSA0LTA4MDowMDAwOjAmJjEmJjIyMGUkMjBmYWxzOC0yMjI0LTAwMDowIDAwOjAmJjEwJjIyc2UkfCZmYWwmMiYyMTAwMTQwJDEyMCY0MSYyMDAwMyYyMC4wLjAwJjAyJjIwJDEwMDAmNiYzMC4kfDIwMC4wMDgtMjEyNC0wMzA6MCAxOToyNC0wMCYyMCAxOTo4LTIzMHwyMDMwOjA4LTA5MjQtMDExOjUgMTU6AAAAADEAAAA=", "bizVendorCode": "SD8256", "pStoreCode": "107102", "packageType": 1, "priceVersion": "SH-PRICEVERSION_MTA3MTAyXzUzNjhfMV8yMjBfNDQwXzIyMF81MjAuMDBfMF84MC4wXzBfMF8wLjBfMC4wXzYwLjAwXzIwLjAwXzAuMDBfMC4wMF8xODY1MzE5", "sendTypeForPickUpCar": 0, "skuId": 1865319, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": 23028, "vendorCode": "32231", "vendorVehicleCode": "20091149"}, "isMinTPriceVendor": true}, {"reference": {"vendorCode": "15004163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "408969", "skuId": 51725297, "bizVendorCode": "SD3047"}}, {"reference": {"vendorCode": "13119", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193947", "skuId": 30540987, "bizVendorCode": "SD4201"}}, {"reference": {"vendorCode": "13094", "vehicleCode": "0", "packageType": 1, "pStoreCode": "188084", "skuId": 6900308, "bizVendorCode": "SD3866"}}, {"reference": {"vendorCode": "13092", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181864", "skuId": 6902492, "bizVendorCode": "SD11238"}}, {"reference": {"vendorCode": "63836", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107059", "skuId": 25224466, "bizVendorCode": "SD3987"}}, {"reference": {"vendorCode": "15003752", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250339", "skuId": 49482441, "bizVendorCode": "SD10808"}}, {"reference": {"vendorCode": "15003443", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247275", "skuId": 44388205, "bizVendorCode": "SD10486"}}, {"reference": {"vendorCode": "81829", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115233", "skuId": 24656654, "bizVendorCode": "SD4949"}}, {"reference": {"vendorCode": "15003472", "vehicleCode": "0", "packageType": 1, "pStoreCode": "247490", "skuId": 6766046, "bizVendorCode": "SD10518"}}, {"reference": {"vendorCode": "15004400", "vehicleCode": "0", "packageType": 1, "pStoreCode": "312747", "skuId": 52020046, "bizVendorCode": "SD11516"}}, {"reference": {"vendorCode": "74569", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114549", "skuId": 6837822, "bizVendorCode": "SD4700"}}, {"reference": {"vendorCode": "15001655", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183428", "skuId": 41502042, "bizVendorCode": "SD8627"}}, {"reference": {"vendorCode": "76661", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114559", "skuId": 52806951, "bizVendorCode": "SD4723"}}, {"reference": {"vendorCode": "62305", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114430", "skuId": 40472223, "bizVendorCode": "SD4416"}}, {"reference": {"vendorCode": "15004703", "vehicleCode": "0", "packageType": 0, "pStoreCode": "326964", "skuId": 54875689, "bizVendorCode": "SD11845"}}, {"reference": {"vendorCode": "15001202", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175872", "skuId": 4684302, "bizVendorCode": "SD8159"}}, {"reference": {"vendorCode": "15002555", "vehicleCode": "0", "packageType": 1, "pStoreCode": "189209", "skuId": 5415326, "bizVendorCode": "SD9573"}}, {"reference": {"vendorCode": "15006693", "vehicleCode": "0", "packageType": 1, "pStoreCode": "1486563", "skuId": 55157789, "bizVendorCode": "SD13949"}}, {"reference": {"vendorCode": "15003850", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251283", "skuId": 7337450, "bizVendorCode": "SD10907"}}, {"reference": {"vendorCode": "15002784", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193192", "skuId": 5724776, "bizVendorCode": "SD9809"}}, {"reference": {"vendorCode": "15000361", "vehicleCode": "0", "packageType": 1, "pStoreCode": "136697", "skuId": 2441377, "bizVendorCode": "SD7103"}}, {"reference": {"vendorCode": "83386", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117466", "skuId": 7604934, "bizVendorCode": "SD5613"}}, {"reference": {"vendorCode": "15001351", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181800", "skuId": 4874517, "bizVendorCode": "SD8312"}}, {"reference": {"vendorCode": "15004045", "vehicleCode": "0", "packageType": 1, "pStoreCode": "263075", "skuId": 7908363, "bizVendorCode": "SD11137"}}, {"reference": {"vendorCode": "15004427", "vehicleCode": "0", "packageType": 1, "pStoreCode": "313573", "skuId": 54373166, "bizVendorCode": "SD11544"}}, {"reference": {"vendorCode": "15001163", "vehicleCode": "0", "packageType": 1, "pStoreCode": "251411", "skuId": 54499600, "bizVendorCode": "SD8119"}}, {"reference": {"vendorCode": "62267", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116244", "skuId": 27861464, "bizVendorCode": "SD5809"}}, {"reference": {"vendorCode": "15002466", "vehicleCode": "0", "packageType": 1, "pStoreCode": "192668", "skuId": 27087922, "bizVendorCode": "SD9479"}}, {"reference": {"vendorCode": "15005984", "vehicleCode": "0", "packageType": 1, "pStoreCode": "741168", "skuId": 49231365, "bizVendorCode": "SD13194"}}, {"reference": {"vendorCode": "15004610", "vehicleCode": "0", "packageType": 1, "pStoreCode": "326551", "skuId": 28320769, "bizVendorCode": "SD7857"}}, {"reference": {"vendorCode": "15001364", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181671", "skuId": 54498361, "bizVendorCode": "SD13705"}}, {"reference": {"vendorCode": "15001073", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174846", "skuId": 44060192, "bizVendorCode": "SD8032"}}, {"reference": {"vendorCode": "76903", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116598", "skuId": 1959527, "bizVendorCode": "SD6575"}}, {"reference": {"vendorCode": "15001827", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183883", "skuId": 47736200, "bizVendorCode": "SD8801"}}, {"reference": {"vendorCode": "62119", "vehicleCode": "0", "packageType": 1, "pStoreCode": "133797", "skuId": 5355754, "bizVendorCode": "SD7041"}}, {"reference": {"vendorCode": "15005698", "vehicleCode": "0", "packageType": 1, "pStoreCode": "721176", "skuId": 44483545, "bizVendorCode": "SD12899"}}, {"reference": {"vendorCode": "15001457", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182915", "skuId": 5639169, "bizVendorCode": "SD8420"}}, {"reference": {"vendorCode": "15003255", "vehicleCode": "0", "packageType": 1, "pStoreCode": "228910", "skuId": 52392327, "bizVendorCode": "SD10288"}}, {"reference": {"vendorCode": "79695", "vehicleCode": "0", "packageType": 1, "pStoreCode": "107033", "skuId": 2015471, "bizVendorCode": "SD4074"}}, {"reference": {"vendorCode": "15001376", "vehicleCode": "0", "packageType": 1, "pStoreCode": "181711", "skuId": 44383333, "bizVendorCode": "SD8338"}}, {"reference": {"vendorCode": "15003922", "vehicleCode": "0", "packageType": 1, "pStoreCode": "261024", "skuId": 24652293, "bizVendorCode": "SD10997"}}, {"reference": {"vendorCode": "15001504", "vehicleCode": "0", "packageType": 0, "pStoreCode": "182455", "skuId": 54514167, "bizVendorCode": "SD8470"}}, {"reference": {"vendorCode": "82843", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115278", "skuId": 54883949, "bizVendorCode": "SD5061"}}, {"reference": {"vendorCode": "15005106", "vehicleCode": "0", "packageType": 1, "pStoreCode": "421863", "skuId": 29710507, "bizVendorCode": "SD12292"}}, {"reference": {"vendorCode": "80777", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116983", "skuId": 2705763, "bizVendorCode": "SD6707"}}, {"reference": {"vendorCode": "78571", "vehicleCode": "0", "packageType": 1, "pStoreCode": "116645", "skuId": 45011968, "bizVendorCode": "SD5477"}}, {"reference": {"vendorCode": "15003741", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250168", "skuId": 29117012, "bizVendorCode": "SD3848"}}, {"reference": {"vendorCode": "81827", "vehicleCode": "0", "packageType": 1, "pStoreCode": "117944", "skuId": 54406122, "bizVendorCode": "SD5538"}}, {"reference": {"vendorCode": "83291", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115347", "skuId": 1957705, "bizVendorCode": "SD5097"}}, {"reference": {"vendorCode": "78579", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114931", "skuId": 54442739, "bizVendorCode": "SD4755"}}, {"reference": {"vendorCode": "15001635", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183156", "skuId": 31708951, "bizVendorCode": "SD8606"}}, {"reference": {"vendorCode": "83141", "vehicleCode": "0", "packageType": 1, "pStoreCode": "106211", "skuId": 47584419, "bizVendorCode": "SD3263"}}, {"reference": {"vendorCode": "15001801", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183861", "skuId": 24662142, "bizVendorCode": "SD9357"}}, {"reference": {"vendorCode": "15004979", "vehicleCode": "0", "packageType": 1, "pStoreCode": "417677", "skuId": 46057327, "bizVendorCode": "SD12159"}}, {"reference": {"vendorCode": "15003910", "vehicleCode": "0", "packageType": 1, "pStoreCode": "259638", "skuId": 48047938, "bizVendorCode": "SD10983"}}, {"reference": {"vendorCode": "15003609", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250167", "skuId": 45637866, "bizVendorCode": "SD10659"}}, {"reference": {"vendorCode": "15001200", "vehicleCode": "0", "packageType": 1, "pStoreCode": "175747", "skuId": 55009914, "bizVendorCode": "SD12373"}}, {"reference": {"vendorCode": "15004183", "vehicleCode": "0", "packageType": 1, "pStoreCode": "308267", "skuId": 9005312, "bizVendorCode": "SD11286"}}, {"reference": {"vendorCode": "15001520", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182598", "skuId": 44682758, "bizVendorCode": "SD8486"}}, {"reference": {"vendorCode": "15005104", "vehicleCode": "0", "packageType": 1, "pStoreCode": "421835", "skuId": 29753011, "bizVendorCode": "SD12290"}}, {"reference": {"vendorCode": "15001454", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182187", "skuId": 7679015, "bizVendorCode": "SD6835"}}, {"reference": {"vendorCode": "15005890", "vehicleCode": "0", "packageType": 0, "pStoreCode": "740013", "skuId": 46127894, "bizVendorCode": "SD13098"}}, {"reference": {"vendorCode": "15005238", "vehicleCode": "0", "packageType": 1, "pStoreCode": "424663", "skuId": 45499952, "bizVendorCode": "SD12420"}}, {"reference": {"vendorCode": "13088", "vehicleCode": "0", "packageType": 0, "pStoreCode": "138334", "skuId": 10336355, "bizVendorCode": "SD6991"}}, {"reference": {"vendorCode": "15003348", "vehicleCode": "0", "packageType": 0, "pStoreCode": "229148", "skuId": 6406510, "bizVendorCode": "SD10386"}}, {"reference": {"vendorCode": "30169", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115880", "skuId": 44079211, "bizVendorCode": "SD5330"}}, {"reference": {"vendorCode": "15002974", "vehicleCode": "0", "packageType": 0, "pStoreCode": "193737", "skuId": 7821465, "bizVendorCode": "SD10001"}}, {"reference": {"vendorCode": "67709", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114723", "skuId": 44384915, "bizVendorCode": "SD4552"}}, {"reference": {"vendorCode": "15001863", "vehicleCode": "0", "packageType": 1, "pStoreCode": "184270", "skuId": 7358219, "bizVendorCode": "SD8841"}}, {"reference": {"vendorCode": "15003632", "vehicleCode": "0", "packageType": 1, "pStoreCode": "249915", "skuId": 6937843, "bizVendorCode": "SD10685"}}, {"reference": {"vendorCode": "15004132", "vehicleCode": "0", "packageType": 1, "pStoreCode": "274422", "skuId": 8136969, "bizVendorCode": "SD11232"}}, {"reference": {"vendorCode": "82153", "vehicleCode": "0", "packageType": 0, "pStoreCode": "117250", "skuId": 52797067, "bizVendorCode": "SD6119"}}, {"reference": {"vendorCode": "83528", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115374", "skuId": 1914737, "bizVendorCode": "SD5118"}}, {"reference": {"vendorCode": "15004534", "vehicleCode": "0", "packageType": 1, "pStoreCode": "320167", "skuId": 24403051, "bizVendorCode": "SD11658"}}, {"reference": {"vendorCode": "15001608", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182827", "skuId": 4991100, "bizVendorCode": "SD8579"}}, {"reference": {"vendorCode": "15004097", "vehicleCode": "0", "packageType": 1, "pStoreCode": "265308", "skuId": 55812989, "bizVendorCode": "SD11195"}}, {"reference": {"vendorCode": "13027", "vehicleCode": "0", "packageType": 0, "pStoreCode": "45956", "skuId": 5543315, "bizVendorCode": "SD3010"}}, {"reference": {"vendorCode": "82731", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115265", "skuId": 1912807, "bizVendorCode": "SD5048"}}, {"reference": {"vendorCode": "15001513", "vehicleCode": "0", "packageType": 1, "pStoreCode": "182515", "skuId": 6766038, "bizVendorCode": "SD8479"}}, {"reference": {"vendorCode": "15002755", "vehicleCode": "0", "packageType": 1, "pStoreCode": "193338", "skuId": 5580709, "bizVendorCode": "SD9781"}}, {"reference": {"vendorCode": "15001227", "vehicleCode": "0", "packageType": 1, "pStoreCode": "176097", "skuId": 44657131, "bizVendorCode": "SD8186"}}, {"reference": {"vendorCode": "62099", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114327", "skuId": 1924105, "bizVendorCode": "SD4406"}}, {"reference": {"vendorCode": "15003785", "vehicleCode": "0", "packageType": 1, "pStoreCode": "250407", "skuId": 6940753, "bizVendorCode": "SD10839"}}, {"reference": {"vendorCode": "15003040", "vehicleCode": "0", "packageType": 0, "pStoreCode": "248954", "skuId": 39507364, "bizVendorCode": "SD10070"}}, {"reference": {"vendorCode": "15001632", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183841", "skuId": 7948711, "bizVendorCode": "SD8603"}}, {"reference": {"vendorCode": "66708", "vehicleCode": "0", "packageType": 1, "pStoreCode": "114936", "skuId": 41085640, "bizVendorCode": "SD4518"}}, {"reference": {"vendorCode": "15001022", "vehicleCode": "0", "packageType": 1, "pStoreCode": "174725", "skuId": 24290918, "bizVendorCode": "SD7980"}}, {"reference": {"vendorCode": "15001375", "vehicleCode": "0", "packageType": 1, "pStoreCode": "248654", "skuId": 55067454, "bizVendorCode": "SD8337"}}, {"reference": {"vendorCode": "15005279", "vehicleCode": "0", "packageType": 0, "pStoreCode": "426091", "skuId": 34106906, "bizVendorCode": "SD12466"}}, {"reference": {"vendorCode": "15003981", "vehicleCode": "0", "packageType": 1, "pStoreCode": "256579", "skuId": 8522361, "bizVendorCode": "SD11062"}}, {"reference": {"vendorCode": "15001613", "vehicleCode": "0", "packageType": 0, "pStoreCode": "182916", "skuId": 5005004, "bizVendorCode": "SD8584"}}, {"reference": {"vendorCode": "15001817", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183929", "skuId": 5638669, "bizVendorCode": "SD8790"}}, {"reference": {"vendorCode": "15001630", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183735", "skuId": 5057065, "bizVendorCode": "SD8601"}}, {"reference": {"vendorCode": "82909", "vehicleCode": "0", "packageType": 1, "pStoreCode": "115295", "skuId": 1911570, "bizVendorCode": "SD5066"}}, {"reference": {"vendorCode": "15006668", "vehicleCode": "0", "packageType": 0, "pStoreCode": "1458752", "skuId": 54516022, "bizVendorCode": "SD13921"}}, {"reference": {"vendorCode": "15001684", "vehicleCode": "0", "packageType": 1, "pStoreCode": "183502", "skuId": 36002100, "bizVendorCode": "SD8656"}}, {"reference": {"vendorCode": "13037", "vehicleCode": "0", "packageType": 1, "pStoreCode": "49033", "skuId": 1887563, "bizVendorCode": "SD3009"}}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减440", "groupCode": "MarketGroup201", "code": "30", "title": "暑期特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "4425"}, "vehicleCode": "5368", "highestPrice": 844, "rCoup": 0, "minDPrice": 0, "pWay": "可选：免费站内取还车", "vehicleKey": "0_5368_", "hot": 0, "minTPrice": 80, "lowestDistance": 0, "group": 0, "type": 0, "sortNum": 22, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD8256_0_107102_107102"], "introduce": "当前车型最低价"}, "minDOrinPrice": 220, "isEasy": true, "isCredit": true, "maximumCommentCount": 51871, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 98, "minTOrinPrice": 520}], "groupCode": "all", "dailyPrice": 0, "hasResult": true}, {"sortNum": 0, "groupName": "", "hasResult": true, "groupCode": "prep", "dailyPrice": 148, "groupAction": 1}, {"sortNum": 1, "groupName": "经济轿车", "hasResult": true, "groupCode": "2", "dailyPrice": 0, "groupAction": 0}, {"sortNum": 3, "groupName": "新能源", "hasResult": true, "groupCode": "newenergy", "dailyPrice": 0, "groupAction": 0}, {"sortNum": 4, "groupName": "舒适轿车", "hasResult": true, "groupCode": "3", "dailyPrice": 0, "groupAction": 0}, {"sortNum": 5, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 0, "groupAction": 0}, {"sortNum": 6, "groupName": "商务车", "hasResult": true, "groupCode": "4", "dailyPrice": 0, "groupAction": 0}, {"sortNum": 7, "groupName": "豪华轿车", "hasResult": true, "groupCode": "5", "dailyPrice": 0, "groupAction": 0}, {"sortNum": 8, "groupName": "跑车", "hasResult": true, "groupCode": "9", "dailyPrice": 0, "groupAction": 0}, {"sortNum": 9, "groupName": "房车", "hasResult": true, "groupCode": "11", "dailyPrice": 760, "groupAction": 0}, {"sortNum": 11, "groupName": "皮卡", "hasResult": true, "groupCode": "10", "dailyPrice": 229, "groupAction": 0}], "resBodySize": 213672, "frontTraceInfo": {"vehicleGroupMap": {"2": 92, "3": 67, "4": 92, "5": 81, "6": 253, "9": 103, "10": 3, "11": 1, "prep": 18, "newenergy": 203}, "normalCount": 7695, "priceCount": 7695, "vehicleList": ["18010", "18495", "1578", "51087", "4722", "4602", "4845", "4603", "18149", "18021", "4980", "4861", "4740", "2438", "2437", "2799", "1226", "1466", "3402", "2794", "4972", "4610", "5700", "85086", "5704", "4736", "2439", "18236", "17389", "18116", "4870", "18474", "4750", "4751", "2451", "17380", "4749", "2448", "4629", "5719", "1474", "4741", "4501", "10631", "4866", "18118", "4626", "18488", "18004", "17394", "18243", "4761", "4883", "17390", "17391", "2461", "1129", "5607", "2459", "1488", "1124", "4752", "5600", "4511", "5601", "4755", "4635", "5604", "4515", "4758", "4516", "4771", "17480", "18691", "1140", "10615", "1499", "2224", "831", "10619", "1133", "10618", "712", "4764", "4644", "4402", "88047", "4647", "4526", "18586", "17499", "17378", "4781", "4660", "4782", "4661", "17373", "18100", "4663", "2484", "1152", "3450", "1151", "10623", "1149", "2477", "5500", "4895", "4654", "5622", "5502", "5623", "5503", "10620", "4899", "5504", "10622", "4659", "608", "10621", "609", "17464", "17344", "4790", "4670", "17346", "3101", "3460", "4309", "2247", "1399", "2487", "5632", "4785", "5512", "5633", "4302", "5513", "4788", "5514", "17468", "17589", "5517", "88054", "17593", "5410", "5411", "5653", "2385", "2384", "5409", "2019", "3469", "1048", "5522", "5402", "4555", "4676", "504", "5524", "868", "5526", "88299", "5407", "5649", "4319", "4330", "873", "874", "1178", "1177", "2387", "635", "5655", "4446", "4325", "5658", "4449", "5419", "5430", "4101", "5433", "4102", "1073", "3491", "5423", "4577", "5303", "5425", "648", "5306", "5427", "5428", "4590", "4470", "4593", "5320", "5441", "5562", "4112", "5564", "5201", "5444", "5565", "3262", "3261", "2050", "1081", "18071", "652", "656", "4103", "5555", "5676", "85156", "658", "4588", "4106", "4228", "5439", "4108", "87571", "4109", "5570", "4482", "5572", "4484", "5694", "5574", "5453", "5211", "5695", "4122", "5575", "5696", "5576", "18086", "540", "300", "51276", "301", "86479", "4598", "85023", "4478", "5325", "548", "5204", "4236", "4358", "5205", "5206", "5569", "4238", "5329", "88530", "86472", "87592", "4493", "4251", "4010", "4495", "5584", "4253", "5342", "5343", "4496", "6310", "5101", "5465", "5223", "5344", "4013", "4498", "5466", "5587", "2078", "3164", "51283", "673", "3159", "555", "315", "5214", "679", "5698", "4246", "4004", "4367", "4489", "5579", "4369", "5591", "5592", "4261", "5472", "4020", "5351", "6320", "5594", "5353", "5474", "5595", "5232", "5111", "5596", "5475", "5113", "4024", "6324", "6323", "4025", "3176", "18063", "18064", "5590", "682", "3169", "326", "5104", "5346", "5588", "4378", "5226", "329", "4139", "5349", "5107", "6316", "5109", "6318", "5360", "3181", "4392", "20357", "5482", "4030", "3180", "5241", "4394", "4031", "6333", "5485", "4396", "5002", "5486", "5245", "5004", "5367", "5125", "4036", "18271", "5480", "452", "212", "333", "456", "214", "51066", "3059", "3058", "216", "4026", "5599", "218", "5359", "4149", "85198", "3071", "5371", "6340", "5494", "5010", "6342", "5253", "4043", "5374", "6344", "4165", "4044", "5254", "5497", "18166", "5498", "4288", "5499", "3072", "5491", "340", "462", "341", "468", "589", "5368", "5005", "5489", "5007", "5128", "5249", "86395", "4053", "17962", "5022", "5024", "4177", "5145", "5388", "4058", "591", "471", "475", "476", "5137", "18936", "5017", "17605", "17727", "17964", "17602", "17724", "5393", "5151", "4062", "17970", "5272", "5274", "17972", "5396", "5276", "4066", "5277", "5278", "5158", "120", "86857", "17619", "247", "126", "248", "127", "17736", "85520", "17616", "17732", "17612", "17613", "17614", "4073", "5283", "5163", "5164", "5044", "26", "5167", "6258", "5289", "28", "6257", "250", "4191", "5281", "5282", "4193", "86528", "136", "138", "86766", "17941", "17700", "5294", "33", "5173", "5053", "5295", "4086", "5175", "5176", "17951", "4088", "5298", "38", "5291", "5050", "4082", "5172", "267", "17718", "17719", "85786", "17835", "17956", "84694", "17714", "17837", "86990", "17831", "51437", "17712", "5063", "6276", "6275", "5067", "5068", "5189", "49", "6270", "5060", "5182", "6272", "4094", "2900", "86668", "279", "17929", "87998", "50", "2905", "2904", "17922", "2901", "5074", "6288", "6281", "5191", "5071", "5194", "286", "166", "1820", "288", "289", "17817", "17819", "51213", "87620", "17931", "17811", "17812", "65", "6296", "6297", "5089", "290", "292", "173", "6294", "174", "175", "296", "2801", "2800", "177", "178", "17906", "2808", "73", "75", "182", "184", "5094", "17912", "1724", "88", "17443", "17440", "17561", "17441", "2820", "2706", "95", "17567", "2705", "2704", "97", "98", "18782", "81327", "1500", "81320", "17784", "17421", "17423", "17781", "84988", "2722", "2842", "2721", "4907", "17428", "17308", "2729", "17424", "17425", "17667", "18999", "17427", "18642", "17554", "17555", "17671", "17550", "1404", "2975", "4916", "81348", "1400", "50982", "17556", "17314", "17678", "1407", "17437", "18768", "1406", "1405", "17640", "17764", "4805", "4927", "4928", "4808", "1531", "4920", "51724", "4921", "17646", "17647", "17651", "17652", "17411", "17774", "17412", "17770", "4936", "3603", "4816", "84631", "17539", "3609", "17655", "17414", "17898", "18747", "17779", "4814", "17416", "17863", "87928", "1437", "2889", "4826", "4827", "2884", "18957", "81152", "17506", "4940", "17628", "17507", "4943", "17622", "4823", "17865", "3617", "4946", "17994", "17631", "17753", "18600", "4960", "1450", "4958", "4716", "4959", "1446", "1445", "3502", "1444", "81145", "2895", "1442", "86710", "2893", "85982", "4950", "17639", "51633", "4953", "4832", "4834", "4835", "1449"], "easyLifeCount": 0, "zhimaCount": 7656, "vendorNames": ["小飞侠租车", "皖阜租车", "凯美租车", "海南松舍租车", "普信租车", "凯虹租车", "郸盛出行", "塞纳超跑俱乐部租车", "三亚威途租车", "博然租车", "超联汇租车", "博腾出行", "钱雨租车", "宜行租车", "泰信吉租车", "八骏马租车", "超速度租车", "华鑫海租车", "三亚橘子租车", "立强租车", "路晨商旅租车", "冀邯出行", "三亚旅途中租车", "流浪者租车 ", "你我他租车", "枫叶租车", "百亿租车", "海程租车", "么么达租车", "海途租车", "龙运三亚租车", "顺利出行", "琼州租车", "鑫旺达租车", "五行租车", "热爱租车", "博格达租车", "好鑫情租车", "世海租车", "鑫刊出行", "海南友途租车", "百募租车", "巨量租车", "海南易安达租车", "三亚五二零租车", "凹凸出行", "日之星丰田租车", "三亚浩宇租车", "旭辰租车", "陵水铭途租车", "徽亚出行", "海立达租车", "通耀租车", "森燚租车", "四季嘉行租车", "盛竹租车", "车游天下租车", "三亚启航租车", "卓誉租车", "车速递租车", "北蒙租车", "海南途达租车", "博利租车", "溶阔租车", "凯福华租车", "礼享智行租车", "启航商旅租车", "海南中进租车", "佰隆租车", "轻松无忧租车", "云超租车", "名仕租车", "新奇租车", "安途生汽车租车", "领路者租车", "潮人商旅租车", "三亚新概念租车", "河北唐亚租车", "海南启航租车", "三亚大拇指租车", "野涵租车", "漫自由租车", "汶汶出行", "鑫通商旅租车", "懒人行租车", "亿梦出行", "文东租车", "赛富德租车", "千里行出行", "海南龙驰租车", "京海亚租车", "安鑫莱租车", "欣岳美行租车", "火火超跑俱乐部租车", "鲁运昌通出行", "好好超跑租车", "林福租车", "驰鑫租车", "旺亚租车", "军盛租车", "畅行无忧租车", "三亚百鸿租车", "悠逸租车", "海南玖玖租车", "三亚永捷租车 ", "三亚楠哥租车", "友嘉租车", "顺椰出行", "欣博祥租车", "世通出行", "捷安利达租车", "潜飞出行", "皓轩跑车俱乐部租车 ", "诚功租车", "鸿吉亚租车", "鑫风向出行", "逍遥租车", "无忧九州租车", "皖太租车", "小龙人租车", "国澳租车", "及客租车", "丰田海南出行", "夏末微凉租车", "众腾租车", "海南奥朗国际租车", "众横租车", "六六出行", "小米租车", "三亚臻鑫汽车出行", "安迪出行", "心航租车", "锐恒租车", "悦萌动租车", "邦尼租车", "树德出行", "海南信租租车", "麒麟恒泰租车", "聚成超跑俱乐部租车", "念念出行", "方达租车", "租租侠租车", "琼驰租车", "环岛租车", "十八度阳光出行", "蜜丝租车", "桐叶租车", "怪兽超跑出行", "海南大脸猫租车", "三亚租呗租车", "海芝梦租车", "木沐租车", "栖橙租车", "钧通租车", "云行租车", "潆莹租车", "威克士租车", "壹优租车", "金晟租车", "EVCARD租车", "锋达租车", "星锐租车", "如亚租车", "三亚顺强租车", "年旭租车", "铭轩租车", "鸿发租车", "腾新出行", "摇个车租车", "鑫浩租车", "天际线超跑租车", "永宏租车", "优享旅途租车", "三亚易云租车", "腾途出行", "皖车汇出行", "亿喆出行", "仙雅租车", "炜晨租车", "瑞光租车", "徽蕴租车", "尔和租车", "器车出行", "租车宝测试账号", "金晟利租车", "晟亚租车", "诚航租车", "盛鸿轩出行", "海南金域租车", "助旅租车", "途新租车", "一嗨租车", "豪享荟租车", "耀东方租车", "黑桃壹租车", "金达莱租车", "名都租车", "海南千汇出行", "木马租车", "博之纳租车", "峰硕租车", "京海租车", "海南潮人租车", "汪澜租车", "锦珩租车", "海南嘉桐租车", "山与海出行", "诚扬租车", "黑娃租车", "三亚神风租车", "铭途租车", "骑仕租车", "钰鑫租车", "卢米租车", "轩宇租车", "墨源租车", "恒泰租车", "开心果租车", "海南点赞租车", "盛京出行", "易代步租车", "汇驰租车", "照成租车", "海南世纪出行", "振亚租车", "玖捌陆租车", "盛泽租车", "博汇天下租车", "虫子邦租车", "嘉旅出行", "宏驰智行租车", "铭鸿租车", "艾思租车", "明昊租车", "蚂蚁猪车出行", "海南龙运租车", "祥驰租车", "琼城租车", "三亚立行租车", "三亚世纪联合租车", "程硕租车", "三亚鼎豪租车", "陇鑫租车", "启瑞盛租车", "镨森租车", "三亚融泽租车", "北新租车", "车之美租车", "纳贝拉租车", "壹阳租车", "鑫和润租车", "三鹤租车", "盛兴隆租车", "君峰租车", "瑾凡租车", "麒麟火租车", "驿合顺通出行"]}, "labelCodes": ["3782", "3783", "3563", "3510", "3872", "3696", "3653", "3697", "3752", "3698", "3731", "4425", "3810", "3679", "4229", "3757", "3779", "4243", "4222", "3495", "3548", "3504", "3827", "3503", "3547", "3828", "3502", "3501", "3709", "3509", "3788", "3789", "3866", "3746", "3769"], "quickFilter": [{"sortNum": 1, "groupCode": "PickReturn", "quickSortNum": 1, "mark": "15分钟内", "itemCode": "PickReturn_StationPR", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "positionCode": "1", "name": "站内取车"}, {"sortNum": 4, "selectedIcon": "https://dimg04.c-ctrip.com/images/0414e12000bsykstpC026.png", "groupCode": "Promotion", "quickSortNum": 2, "positionCode": "2", "itemCode": "Promotion_3752", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "", "icon": "https://dimg04.c-ctrip.com/images/0416612000bsyl4e3C6D1.png"}, {"sortNum": 1, "groupCode": "SelfService", "quickSortNum": 1, "positionCode": "3", "itemCode": "SelfService_Support", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 6, "groupCode": "StoreService", "quickSortNum": 2, "positionCode": "4", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "mark": "hot", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "positionCode": "6", "name": "携程优选", "icon": ""}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "8", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "9", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "10", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "12", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 5, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "13", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}], "promotMap": {"605988495": "XfwZxS793xKg0BD6y94YJA=="}, "requestInfo": {"rLongitude": 109.41235, "rDate": "20240823193000", "age": 30, "pCityId": 43, "returnDate": "/Date(1724412600000+0800)/", "sourceCountryId": 1, "pLatitude": 18.30767, "rLatitude": 18.30767, "pLongitude": 109.41235, "pDate": "20240821193000", "rCityId": 43, "pickupLocationName": "凤凰国际机场-T1航站楼", "returnLocationName": "凤凰国际机场-T1航站楼", "pickupDate": "/Date(1724239800000+0800)/"}, "allVehicleCount": 710, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg", "https://dimg04.c-ctrip.com/images/0yc4712000azl5ez2FD1A.jpg", "https://dimg04.c-ctrip.com/images/0yc5312000azlcq64F1AF.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "id": 2, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "23:59", "lat": "18.308537", "lng": "109.413536", "name": "三亚凤凰机场店", "fromTime": "00:00"}, "checkResponseTime": 1723187512506.32, "vehicleList": [{"skylight": "部分车辆支持", "vehicleKey": "0_4139_", "luggageNo": 5, "carPlay": "部分车辆支持原厂互联/映射/CarLife/CarPlay", "displacement": "1.0T-1.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5i12000c7kmh93066A.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "雪佛兰科沃兹", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/Type-C/SD", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=2364", "carPhone": true, "vehicleCode": "4139", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "雪佛兰科沃兹", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0612000d5pibur948E.png", "fuel": "92号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStart": false, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5212000c7mwubp7B39.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0l12000c7mwy55EE30.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000c7mwvvc7936.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3212000c7mx6u22402.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3012000c7mwv0sCA00.jpg?mark=yiche"], "transmissionType": 1, "brandName": "雪佛兰", "oilType": 3, "struct": "三厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "雪佛兰", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "68", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "支持", "subGroupCode": "newenergy", "luggageNo": 0, "carPlay": "不支持", "vehicleKey": "2_17639_", "autoPark": false, "charge": "", "fuelType": "纯电动", "imageList": ["https://dimg04.c-ctrip.com/images/0RV3b12000c56dmyj20D3.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "五菱缤果", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "不支持", "mediaTypes": [], "carPhone": false, "vehicleCode": "17639", "style": "", "carPhoneDesc": {"type": 0, "typeDesc": "不支持"}, "name": "五菱缤果", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4t12000c5v1bkb78D8.jpg?mark=yiche", "autoStart": false, "passengerNo": 4, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1e12000c5v1bme5FB5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6k12000c5v1h6j3FDB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2h12000c5v1imhEEFA.jpg?mark=yiche"], "transmissionType": 1, "brandName": "五菱汽车", "oilType": 5, "struct": "两厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "五菱汽车", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "65", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_4661_", "luggageNo": 4, "carPlay": "不支持", "displacement": "2.4L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5r12000etgn2505C6F.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD", "mediaTypes": [], "carPhone": false, "vehicleCode": "4661", "style": "2015款及以前", "carPhoneDesc": {"type": 0, "typeDesc": "不支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4u12000cmlb1h5F574.png?mark=yiche", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放4个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6712000chqtu49524B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4f12000chqu4e7C301.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0512000chqttg9BA0F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3p12000chqttmgF6EC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0t12000chqu1qa816D.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "47", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_5281_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6112000eth0lxpAD64.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=3460&app_ver=10.5", "carPhone": true, "vehicleCode": "5281", "style": "2020款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0d12000ap2ve880922.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV0f12000c5rm0waEB81.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3r12000c5rm2o7C1DD.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1512000c5rm5bg6504.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4p12000c5rm4s775C2.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1112000c5rlvikC8CD.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "47", "guidSys": "支持全速自适应巡航/定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_5526_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV3u12000enkna1v6DF7.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=6990&app_ver=10.5", "carPhone": true, "vehicleCode": "5526", "style": "2022款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0y12000ba4zyqrB197.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3412000c6e2uzs65C6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1212000c6e2tbiDFA5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1t12000c6e2q84823E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4012000c6e2qr501F9.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000c6e2s5e4141.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "47", "guidSys": "支持自适应巡航/全速自适应巡航/定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_5371_", "luggageNo": 2, "carPlay": "支持原厂互联/映射/CarPlay", "displacement": "2.0T-3.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV1r12000c5683sg7433.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "坦克300", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置四驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=6815&app_ver=10.5", "carPhone": true, "vehicleCode": "5371", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "坦克300", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3u12000b868wxvED91.jpg", "fuel": "92号或95号", "passengerNo": 5, "luggageNum": "可放2个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3x12000c5s3p7m9857.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1412000c5s3ykr96F8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5u12000c5s3un6F748.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4012000c5s40b53A5A.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0j12000c5s3un96595.jpg?mark=yiche"], "transmissionType": 1, "brandName": "坦克", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "坦克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "支持定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_1149_", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife/原厂互联/映射/AndroidAuto", "displacement": "1.4T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6u12000c55k3z88634.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "3", "zhName": "大众迈腾", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=1404", "vehicleCode": "1149", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "大众迈腾", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1g12000d5pt9ai8977.png", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3m12000ceviynl2CA6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4j12000ceviyo32015.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0g12000cevj9zbB5BF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4912000cevj9teE3D7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5c12000cevj7bl4DE9.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "舒适轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "vehiclesSetId": "86", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_4659_", "luggageNo": 5, "carPlay": "支持CarPlay", "displacement": "2.5L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV5p12000etgn9tpB3A8.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX", "mediaTypes": [], "carPhone": true, "vehicleCode": "4659", "style": "2017/18款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3c12000c6eb44lAB7C.jpg?mark=yiche", "fuel": "92号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3g12000c6eb2tfDCA3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3k12000c6eawz3F33E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0y12000c6eb0ghC333.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6w12000c6eb3df5EB1.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3h12000c6eb26y9568.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "47", "guidSys": "支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "2_1149_", "luggageNo": 5, "carPlay": "部分车辆支持CarPlay/CarLife/原厂互联/映射/AndroidAuto", "displacement": "1.4T", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6u12000c55k3z88634.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "3", "zhName": "大众迈腾", "doorNo": 4, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB/AUX/SD", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=1404", "vehicleCode": "1149", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "大众迈腾", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1g12000d5pt9ai8977.png", "fuel": "95号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV3m12000ceviynl2CA6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4j12000ceviyo32015.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0g12000cevj9zbB5BF.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4912000cevj9teE3D7.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5c12000cevj7bl4DE9.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "三厢车", "groupName": "舒适轿车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "vehiclesSetId": "86", "guidSys": "部分车辆支持定速巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_4660_", "luggageNo": 5, "carPlay": "支持CarPlay", "displacement": "2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV1812000etgoh7fCF12.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/AUX", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=675&app_ver=10.5", "carPhone": true, "vehicleCode": "4660", "style": "2017/18款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV1812000etgoh7fCF12.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5312000c6e6jqd2785.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4812000c6e6jqt5FEA.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1712000c6e6j7m794E.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5m12000c6e6q8z4687.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6612000c6e6hg40B70.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "47", "guidSys": "支持定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_5374_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6w12000eth2x9eDF35.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoOutNew.html?albumId=4535&app_ver=10.5", "carPhone": true, "vehicleCode": "5374", "style": "2021款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0d12000ap2ve880922.jpg", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6h12000c5s4wm508D9.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5w12000c5s54t8F8BB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5s12000c5s50h32B2C.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6h12000c5s55i5ED78.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0312000c5s51x9F140.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "vehiclesSetId": "47", "guidSys": "支持定速巡航/自适应巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "vehicleKey": "0_17390_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife", "displacement": "2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV3012000elv63aiFD76.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "别克GL8豪华版", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=9309&app_ver=10.5", "carPhone": true, "vehicleCode": "17390", "style": "2023款", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "别克GL8豪华版", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3012000elv63aiFD76.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放5个24寸行李箱", "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5512000c6dz8rnD05D.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0y12000c6dz4jpEC37.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4m12000c6dz86b6546.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3u12000c6dz6z26A9B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3t12000c6dz6yl7473.jpg?mark=yiche"], "transmissionType": 1, "brandName": "别克", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "别克", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "47", "guidSys": "支持全速自适应巡航/定速巡航/自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "subGroupCode": "newenergy", "luggageNo": 0, "carPlay": "不支持", "vehicleKey": "0_17639_", "autoPark": false, "charge": "", "fuelType": "纯电动", "imageList": ["https://dimg04.c-ctrip.com/images/0RV3b12000c56dmyj20D3.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "2", "zhName": "五菱缤果", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱", "chargeInterface": "不支持", "mediaTypes": [], "carPhone": false, "vehicleCode": "17639", "style": "", "carPhoneDesc": {"type": 0, "typeDesc": "不支持"}, "name": "五菱缤果", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4t12000c5v1bkb78D8.jpg?mark=yiche", "autoStart": false, "passengerNo": 4, "autoStartDesc": {"type": 0, "typeDesc": "不支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV1e12000c5v1bme5FB5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6k12000c5v1h6j3FDB.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2h12000c5v1imhEEFA.jpg?mark=yiche"], "transmissionType": 1, "brandName": "五菱汽车", "oilType": 5, "struct": "两厢车", "groupName": "经济轿车", "autoBackupDesc": {"type": 0, "typeDesc": "不支持"}, "groupSubClassCode": "", "brandEName": "五菱汽车", "licenseStyle": "2", "autoBackUp": false, "vehiclesSetId": "65", "guidSys": "不支持", "transmissionName": "自动挡"}, {"skylight": "支持", "subGroupCode": "newenergy", "luggageNo": 5, "carPlay": "不支持", "vehicleKey": "0_5368_", "autoPark": false, "endurance": "工信部续航525km-688km", "fuelType": "纯电动", "charge": "快充1小时,慢充10小时", "imageList": ["https://dimg04.c-ctrip.com/images/0RV2612000couxzou3ADF.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "特斯拉Model Y", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "双电机四驱/后置后驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5703&app_ver=10.5", "carPhone": true, "vehicleCode": "5368", "recommendLabels": [{"title": "", "subTitle": "百公里加速3.7秒"}], "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "特斯拉Model Y", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0n12000b76j6uj7FA4.jpg", "luggageNum": "可放5个24寸行李箱", "passengerNo": 5, "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5q12000cduyfjg086F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0912000cduyi9cD6F8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2m12000cduyc1pC5A0.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3712000cduyi1u3AF6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000cduybbn27B0.jpg?mark=yiche"], "transmissionType": 1, "brandName": "特斯拉", "oilType": 5, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "特斯拉", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "12", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "支持", "subGroupCode": "newenergy", "luggageNo": 5, "carPlay": "不支持", "vehicleKey": "2_5368_", "autoPark": false, "endurance": "工信部续航525km-688km", "fuelType": "纯电动", "charge": "快充1小时,慢充10小时", "imageList": ["https://dimg04.c-ctrip.com/images/0RV2612000couxzou3ADF.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "特斯拉Model Y", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "双电机四驱/后置后驱", "chargeInterface": "支持USB/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=5703&app_ver=10.5", "carPhone": true, "vehicleCode": "5368", "recommendLabels": [{"title": "", "subTitle": "百公里加速3.7秒"}], "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "特斯拉Model Y", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV0n12000b76j6uj7FA4.jpg", "luggageNum": "可放5个24寸行李箱", "passengerNo": 5, "autoStart": true, "autoStartDesc": {"type": 1, "typeDesc": "支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5q12000cduyfjg086F.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0912000cduyi9cD6F8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2m12000cduyc1pC5A0.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3712000cduyi1u3AF6.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5i12000cduybbn27B0.jpg?mark=yiche"], "transmissionType": 1, "brandName": "特斯拉", "oilType": 5, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "特斯拉", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "12", "guidSys": "支持全速自适应巡航", "transmissionName": "自动挡"}], "storeList": [{"pickOffLevel": 138937, "storeCode": "183502", "pickUpLevel": 138937}, {"pickOffLevel": 1467593, "storeCode": "407345", "pickUpLevel": 1467593}, {"pickOffLevel": 7625, "storeCode": "115138", "pickUpLevel": 7625}, {"pickOffLevel": 717802, "storeCode": "319453", "pickUpLevel": 717802}, {"pickOffLevel": 132724, "storeCode": "181711", "pickUpLevel": 132724}, {"pickOffLevel": 56608, "storeCode": "115913", "pickUpLevel": 56608}, {"pickOffLevel": 1271320, "storeCode": "721176", "pickUpLevel": 1271320}, {"pickOffLevel": 61144, "storeCode": "116955", "pickUpLevel": 61144}, {"pickOffLevel": 1493913, "storeCode": "1458752", "pickUpLevel": 1493913}, {"pickOffLevel": 135835, "storeCode": "182996", "pickUpLevel": 135835}, {"pickOffLevel": 55049, "storeCode": "117740", "pickUpLevel": 55049}, {"pickOffLevel": 38797, "storeCode": "114327", "pickUpLevel": 38797}, {"pickOffLevel": 878795, "storeCode": "117220", "pickUpLevel": 878795}, {"pickOffLevel": 47309, "storeCode": "114931", "pickUpLevel": 47309}, {"pickOffLevel": 43319, "storeCode": "114315", "pickUpLevel": 43319}, {"pickOffLevel": 17, "storeCode": "49033", "pickUpLevel": 17}, {"pickOffLevel": 1234423, "storeCode": "615511", "pickUpLevel": 1234423}, {"pickOffLevel": 43275, "storeCode": "114307", "pickUpLevel": 43275}, {"pickOffLevel": 1434224, "storeCode": "834184", "pickUpLevel": 1434224}, {"pickOffLevel": 61587, "storeCode": "116983", "pickUpLevel": 61587}, {"pickOffLevel": 243526, "storeCode": "247802", "pickUpLevel": 243526}, {"pickOffLevel": -1, "storeCode": "138334", "pickUpLevel": -1}, {"pickOffLevel": 137652, "storeCode": "183430", "pickUpLevel": 137652}, {"pickOffLevel": 28549, "storeCode": "107759", "pickUpLevel": 28549}, {"pickOffLevel": 136609, "storeCode": "183428", "pickUpLevel": 136609}, {"pickOffLevel": 292, "storeCode": "183936", "pickUpLevel": 292}, {"pickOffLevel": 136429, "storeCode": "182916", "pickUpLevel": 136429}, {"pickOffLevel": 124167, "storeCode": "176097", "pickUpLevel": 124167}, {"pickOffLevel": 2761, "storeCode": "78", "pickUpLevel": 2761}, {"pickOffLevel": 50259, "storeCode": "115347", "pickUpLevel": 50259}, {"pickOffLevel": 487600, "storeCode": "265308", "pickUpLevel": 487600}, {"pickOffLevel": 843669, "storeCode": "174846", "pickUpLevel": 843669}, {"pickOffLevel": 2648, "storeCode": "261024", "pickUpLevel": 2648}, {"pickOffLevel": 189237, "storeCode": "193192", "pickUpLevel": 189237}, {"pickOffLevel": 1385945, "storeCode": "181671", "pickUpLevel": 1385945}, {"pickOffLevel": 1135044, "storeCode": "175820", "pickUpLevel": 1135044}, {"pickOffLevel": 222126, "storeCode": "229148", "pickUpLevel": 222126}, {"pickOffLevel": 872, "storeCode": "188084", "pickUpLevel": 872}, {"pickOffLevel": 4780, "storeCode": "326551", "pickUpLevel": 4780}, {"pickOffLevel": 635559, "storeCode": "312747", "pickUpLevel": 635559}, {"pickOffLevel": 1658, "storeCode": "193947", "pickUpLevel": 1658}, {"pickOffLevel": 818434, "storeCode": "322435", "pickUpLevel": 818434}, {"pickOffLevel": 2525, "storeCode": "182187", "pickUpLevel": 2525}, {"pickOffLevel": 259829, "storeCode": "192668", "pickUpLevel": 259829}, {"pickOffLevel": 26867, "storeCode": "107474", "pickUpLevel": 26867}, {"pickOffLevel": 1352996, "storeCode": "741168", "pickUpLevel": 1352996}, {"pickOffLevel": 143565, "storeCode": "183735", "pickUpLevel": 143565}, {"pickOffLevel": 17841, "storeCode": "106211", "pickUpLevel": 17841}, {"pickOffLevel": 1343119, "storeCode": "625150", "pickUpLevel": 1343119}, {"pickOffLevel": 20220801, "storeCode": "67462", "pickUpLevel": 20220801}, {"pickOffLevel": 49817, "storeCode": "115265", "pickUpLevel": 49817}, {"pickOffLevel": 142239, "storeCode": "183880", "pickUpLevel": 142239}, {"pickOffLevel": 1076111, "storeCode": "248654", "pickUpLevel": 1076111}, {"pickOffLevel": 1117425, "storeCode": "421863", "pickUpLevel": 1117425}, {"pickOffLevel": 1037331, "storeCode": "411342", "pickUpLevel": 1037331}, {"pickOffLevel": 1131579, "storeCode": "422885", "pickUpLevel": 1131579}, {"pickOffLevel": 24801, "storeCode": "107059", "pickUpLevel": 24801}, {"pickOffLevel": -1, "storeCode": "106808", "pickUpLevel": -1}, {"pickOffLevel": 8536, "storeCode": "2961", "pickUpLevel": 8536}, {"pickOffLevel": 49965, "storeCode": "115295", "pickUpLevel": 49965}, {"pickOffLevel": 60975, "storeCode": "116843", "pickUpLevel": 60975}, {"pickOffLevel": 1152180, "storeCode": "421835", "pickUpLevel": 1152180}, {"pickOffLevel": 450269, "storeCode": "263075", "pickUpLevel": 450269}, {"pickOffLevel": 26611, "storeCode": "107268", "pickUpLevel": 26611}, {"pickOffLevel": 438810, "storeCode": "256579", "pickUpLevel": 438810}, {"pickOffLevel": 6635, "storeCode": "181864", "pickUpLevel": 6635}, {"pickOffLevel": 1216433, "storeCode": "326481", "pickUpLevel": 1216433}, {"pickOffLevel": 60174, "storeCode": "116598", "pickUpLevel": 60174}, {"pickOffLevel": 5121, "storeCode": "408969", "pickUpLevel": 5121}, {"pickOffLevel": 26384, "storeCode": "107033", "pickUpLevel": 26384}, {"pickOffLevel": 1424123, "storeCode": "251411", "pickUpLevel": 1424123}, {"pickOffLevel": 57507, "storeCode": "116225", "pickUpLevel": 57507}, {"pickOffLevel": 112379, "storeCode": "174692", "pickUpLevel": 112379}, {"pickOffLevel": 30154, "storeCode": "107119", "pickUpLevel": 30154}, {"pickOffLevel": 262523, "storeCode": "250114", "pickUpLevel": 262523}, {"pickOffLevel": 1063322, "storeCode": "417677", "pickUpLevel": 1063322}, {"pickOffLevel": 253357, "storeCode": "249861", "pickUpLevel": 253357}, {"pickOffLevel": 7591, "storeCode": "115986", "pickUpLevel": 7591}, {"pickOffLevel": 1033705, "storeCode": "406995", "pickUpLevel": 1033705}, {"pickOffLevel": 48992, "storeCode": "115233", "pickUpLevel": 48992}, {"pickOffLevel": 45437, "storeCode": "114723", "pickUpLevel": 45437}, {"pickOffLevel": 254184, "storeCode": "249915", "pickUpLevel": 254184}, {"pickOffLevel": 259543, "storeCode": "250168", "pickUpLevel": 259543}, {"pickOffLevel": 1299138, "storeCode": "740013", "pickUpLevel": 1299138}, {"pickOffLevel": 126125, "storeCode": "181800", "pickUpLevel": 126125}, {"pickOffLevel": 93919, "storeCode": "136697", "pickUpLevel": 93919}, {"pickOffLevel": 219610, "storeCode": "193285", "pickUpLevel": 219610}, {"pickOffLevel": 260440, "storeCode": "250407", "pickUpLevel": 260440}, {"pickOffLevel": 559546, "storeCode": "189209", "pickUpLevel": 559546}, {"pickOffLevel": 79789, "storeCode": "115257", "pickUpLevel": 79789}, {"pickOffLevel": 24223, "storeCode": "107352", "pickUpLevel": 24223}, {"pickOffLevel": 3475, "storeCode": "184113", "pickUpLevel": 3475}, {"pickOffLevel": 1375648, "storeCode": "801739", "pickUpLevel": 1375648}, {"pickOffLevel": 751675, "storeCode": "322064", "pickUpLevel": 751675}, {"pickOffLevel": 125036, "storeCode": "181818", "pickUpLevel": 125036}, {"pickOffLevel": 145906, "storeCode": "184270", "pickUpLevel": 145906}, {"pickOffLevel": 766431, "storeCode": "247490", "pickUpLevel": 766431}, {"pickOffLevel": 68967, "storeCode": "114886", "pickUpLevel": 68967}, {"pickOffLevel": -1, "storeCode": "182720", "pickUpLevel": -1}, {"pickOffLevel": 63244, "storeCode": "117466", "pickUpLevel": 63244}, {"pickOffLevel": 27179, "storeCode": "107190", "pickUpLevel": 27179}, {"pickOffLevel": 732082, "storeCode": "319992", "pickUpLevel": 732082}, {"pickOffLevel": 23111, "storeCode": "107196", "pickUpLevel": 23111}, {"pickOffLevel": 1493101, "storeCode": "1257474", "pickUpLevel": 1493101}, {"pickOffLevel": 701184, "storeCode": "313573", "pickUpLevel": 701184}, {"pickOffLevel": 48117, "storeCode": "114922", "pickUpLevel": 48117}, {"pickOffLevel": 229765, "storeCode": "247275", "pickUpLevel": 229765}, {"pickOffLevel": 114376, "storeCode": "174725", "pickUpLevel": 114376}, {"pickOffLevel": 1155183, "storeCode": "175747", "pickUpLevel": 1155183}, {"pickOffLevel": 61439, "storeCode": "116966", "pickUpLevel": 61439}, {"pickOffLevel": 261114, "storeCode": "250339", "pickUpLevel": 261114}, {"pickOffLevel": 1, "storeCode": "45956", "pickUpLevel": 1}, {"pickOffLevel": 606229, "storeCode": "182515", "pickUpLevel": 606229}, {"pickOffLevel": 44791, "storeCode": "114936", "pickUpLevel": 44791}, {"pickOffLevel": 201117, "storeCode": "193737", "pickUpLevel": 201117}, {"pickOffLevel": 39038, "storeCode": "114430", "pickUpLevel": 39038}, {"pickOffLevel": 972266, "storeCode": "320167", "pickUpLevel": 972266}, {"pickOffLevel": 266208, "storeCode": "193972", "pickUpLevel": 266208}, {"pickOffLevel": 326852, "storeCode": "251283", "pickUpLevel": 326852}, {"pickOffLevel": 251691, "storeCode": "182413", "pickUpLevel": 251691}, {"pickOffLevel": 137747, "storeCode": "182915", "pickUpLevel": 137747}, {"pickOffLevel": 129059, "storeCode": "182401", "pickUpLevel": 129059}, {"pickOffLevel": 688311, "storeCode": "106190", "pickUpLevel": 688311}, {"pickOffLevel": 220578, "storeCode": "228908", "pickUpLevel": 220578}, {"pickOffLevel": 1187957, "storeCode": "228910", "pickUpLevel": 1187957}, {"pickOffLevel": 1195573, "storeCode": "424236", "pickUpLevel": 1195573}, {"pickOffLevel": 130112, "storeCode": "182684", "pickUpLevel": 130112}, {"pickOffLevel": 472151, "storeCode": "274541", "pickUpLevel": 472151}, {"pickOffLevel": 1511448, "storeCode": "1486563", "pickUpLevel": 1511448}, {"pickOffLevel": 1135023, "storeCode": "116645", "pickUpLevel": 1135023}, {"pickOffLevel": 54987, "storeCode": "115880", "pickUpLevel": 54987}, {"pickOffLevel": 50441, "storeCode": "115374", "pickUpLevel": 50441}, {"pickOffLevel": 13540, "storeCode": "831181", "pickUpLevel": 13540}, {"pickOffLevel": 1204848, "storeCode": "426217", "pickUpLevel": 1204848}, {"pickOffLevel": 63140, "storeCode": "117434", "pickUpLevel": 63140}, {"pickOffLevel": 90305, "storeCode": "117944", "pickUpLevel": 90305}, {"pickOffLevel": 88368, "storeCode": "106965", "pickUpLevel": 88368}, {"pickOffLevel": 745984, "storeCode": "313013", "pickUpLevel": 745984}, {"pickOffLevel": 126692, "storeCode": "182206", "pickUpLevel": 126692}, {"pickOffLevel": 128931, "storeCode": "182455", "pickUpLevel": 128931}, {"pickOffLevel": 1295043, "storeCode": "722289", "pickUpLevel": 1295043}, {"pickOffLevel": 60758, "storeCode": "116918", "pickUpLevel": 60758}, {"pickOffLevel": 143531, "storeCode": "183883", "pickUpLevel": 143531}, {"pickOffLevel": 129618, "storeCode": "182598", "pickUpLevel": 129618}, {"pickOffLevel": 49923, "storeCode": "115278", "pickUpLevel": 49923}, {"pickOffLevel": 270460, "storeCode": "251213", "pickUpLevel": 270460}, {"pickOffLevel": 561520, "storeCode": "308267", "pickUpLevel": 561520}, {"pickOffLevel": 1152971, "storeCode": "424663", "pickUpLevel": 1152971}, {"pickOffLevel": 144583, "storeCode": "184166", "pickUpLevel": 144583}, {"pickOffLevel": 1013986, "storeCode": "175872", "pickUpLevel": 1013986}, {"pickOffLevel": 22800, "storeCode": "107528", "pickUpLevel": 22800}, {"pickOffLevel": 72225, "storeCode": "133797", "pickUpLevel": 72225}, {"pickOffLevel": 43934, "storeCode": "114447", "pickUpLevel": 43934}, {"pickOffLevel": 257797, "storeCode": "248954", "pickUpLevel": 257797}, {"pickOffLevel": 873706, "storeCode": "183929", "pickUpLevel": 873706}, {"pickOffLevel": 133456, "storeCode": "183156", "pickUpLevel": 133456}, {"pickOffLevel": 47053, "storeCode": "114559", "pickUpLevel": 47053}, {"pickOffLevel": 491933, "storeCode": "259638", "pickUpLevel": 491933}, {"pickOffLevel": 62466, "storeCode": "117250", "pickUpLevel": 62466}, {"pickOffLevel": 212724, "storeCode": "193338", "pickUpLevel": 212724}, {"pickOffLevel": 1183435, "storeCode": "114549", "pickUpLevel": 1183435}, {"pickOffLevel": 1075789, "storeCode": "326964", "pickUpLevel": 1075789}, {"pickOffLevel": 98018, "storeCode": "161973", "pickUpLevel": 98018}, {"pickOffLevel": 57771, "storeCode": "116244", "pickUpLevel": 57771}, {"pickOffLevel": 132053, "storeCode": "115473", "pickUpLevel": 132053}, {"pickOffLevel": 470891, "storeCode": "274422", "pickUpLevel": 470891}, {"pickOffLevel": 120135, "storeCode": "114529", "pickUpLevel": 120135}, {"pickOffLevel": 214261, "storeCode": "182827", "pickUpLevel": 214261}, {"pickOffLevel": 8154, "storeCode": "250167", "pickUpLevel": 8154}, {"pickOffLevel": 705174, "storeCode": "183841", "pickUpLevel": 705174}, {"pickOffLevel": 62523, "storeCode": "117286", "pickUpLevel": 62523}, {"pickOffLevel": 67912, "storeCode": "116523", "pickUpLevel": 67912}, {"pickOffLevel": 1247205, "storeCode": "426091", "pickUpLevel": 1247205}, {"pickOffLevel": 835031, "storeCode": "323352", "pickUpLevel": 835031}, {"pickOffLevel": 3599, "storeCode": "183861", "pickUpLevel": 3599}, {"pickOffLevel": 23028, "storeCode": "107102", "pickUpLevel": 23028}, {"pickOffLevel": 1107541, "storeCode": "421268", "pickUpLevel": 1107541}], "promptInfos": [{"jumpUrl": "https://m.ctrip.com/tangram/OTI2MjU=?ctm_ref=vactang_page_92625&isHideNavBar=YES&apppgid=10650039395&statusBarStyle=1&channelid=238407", "type": 19, "locations": [{"groupCode": "all", "index": 1}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg3112000e8l73mt525E.png"}, {"type": 18, "locations": [{"groupCode": "all", "index": 5}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg5t12000cixdsaaBF66.png"}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": false, "isLastPage": false, "uniqSign": "120011398903214795362J9B0lt4646y5437v5A0", "pHub": 1, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": []}, "checkRequestTime": 1723187510704.078, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "3207331685099756306", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a389467-478663-910773", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1723187512491+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&listExtraMaps[packageLevelVersion]=1&modify=&orderId=&pickupPointInfo=2024-08-21 19:30:00|凤凰国际机场-T1航站楼|43|18.30767|109.41235|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=all&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2024-08-23 19:30:00|凤凰国际机场-T1航站楼|43|18.30767|109.41235|||&sortType=1&uid=M180970538@@PAGENUM@@1", "networkCost": 1854, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 1824, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1723187510702, "afterFetch": 1723187512556, "hasRetry": false, "loadDiffCost": 30, "originNetworkCost": 1824}}