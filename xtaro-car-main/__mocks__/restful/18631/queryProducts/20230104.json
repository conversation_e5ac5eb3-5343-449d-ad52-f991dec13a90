{"baseResponse": {"extMap": {"IncludeFeesCost": "0.0", "checkRentCenter_2": "0.0", "mergeGroup_5": "0.0", "apiCost": "424.0", "9_getFromRedisCost": "5", "mergeGroup_9": "0.0", "2_getFromRedisCost": "9", "mergeGroupSize_2": "4", "totalCostTime": "22", "gsCost": "1.0", "mergeGroupSize_3": "6", "restCost": "1", "originalCode": "200", "mergeGroupSize_4": "10", "lastInfoCost_7": "14.0", "mergeGroup_2": "0.0", "3_getFromRedisCost": "10", "mergeGroupSize_5": "6", "mergeGroup_6": "1.0", "mergeGroupSize_6": "6", "mergeGroupSize_newenergy": "2", "getPageInfoFromResponse_pre": "2", "needWait": "0", "4_getFromRedisCost": "11", "requestId": "6a2a08f7-3cd0-41cd-87b7-ecea7d869066", "mergeGroupSize_9": "2", "start": "2023-01-09 12:11:17", "dataConvertResCost": "62.0", "buildInfoCost_1": "0.0", "buildInfoCost_2": "31.0", "buildInfoCost_3": "1.0", "mergeGroup_3": "0.0", "ubtProcessCost_8": "1.0", "buildInfoCost_4": "32.0", "5_getFromRedisCost": "10", "shoppingCost_1": "426.0", "newenergy_getFromRedisCost": "3", "allCost": "489.0", "end": "2023-01-09 12:11:17", "setProductGroupsHashCodeCostAffect": "0.0", "contextBuilderCost_3": "14.0", "restOriginalCost": "489.0", "6_getFromRedisCost": "9", "uid": "M00020422", "pickupCityId": "43", "dropoffCityId": "43", "mergeGroup_4": "0.0", "usePage": "1", "responseHeadCost": "2", "initBaseData_1": "14.0", "calculatePreAuth_3": "0.0", "mergeGroup_newenergy": "1.0", "productGroupCost_6": "2.0", "runAsyncCost_2": "0.0", "sourceFrom": "ISD_C_APP", "pageName": "List"}, "errorCode": "0", "hasResult": true, "code": "200", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "004e8e48-63f3-4309-bb85-53f1bdc99c2f", "isSuccess": true}, "rHub": 1, "isKlbData": true, "allVendorPriceCount": 2, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": false, "rRentCenter": {"images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "id": 2, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "23:59", "lat": "18.305902", "lng": "109.413683", "name": "三亚凤凰机场店", "fromTime": "00:00"}, "extras": {"serverRequestId": "9H2K51Bl0B7g08fZhUPi", "abVersion": "230104_DSJT_fil10|B,220323_DSJT_rank2|B,221207_DSJT_cxvr|A,230117_DSJT_sxwjg|B"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"shortName": "车龄", "sortNum": 1, "bitwiseType": 2, "name": "车龄", "groupCode": "CarAge", "filterItems": [{"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 3, "positionCode": "5", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "2座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_2", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "4座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 1, "groupCode": "SeatGroup", "itemCode": "SeatGroup_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "NewEnergy", "itemCode": "NewEnergy_elect", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "HotBrand", "itemCode": "HotBrand_奥迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_别克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_奔驰", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_宝马", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotB<PERSON>_本田", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 6, "groupCode": "HotBrand", "itemCode": "HotBrand_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"sortNum": 10, "groupCode": "HotBrand", "itemCode": "HotBrand_Jeep", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_奥迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}], "groupCode": "BrandGroup_a0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_保时捷", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "保时捷", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_别克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_奔驰", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "奔驰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_宝马", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "宝马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_本田", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "本田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}], "groupCode": "BrandGroup_f0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_华颂", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "华颂", "icon": "//pages.c-ctrip.com/carisd/brandlogo/huasong.png"}, {"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_海马", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "海马", "icon": "//pages.c-ctrip.com/carisd/brandlogo/haima.png"}], "groupCode": "BrandGroup_h0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_Jeep", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "Jeep", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jeep.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_q0", "itemCode": "BrandGroup_q0_起亚", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "起亚", "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "groupCode": "BrandGroup_q0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_上汽大通MAXUS", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "上汽大通MAXUS", "icon": "https://dimg04.c-ctrip.com/images/0R42z120009gww9j0F779.png"}], "groupCode": "BrandGroup_s0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_特斯拉", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "特斯拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}], "groupCode": "BrandGroup_t0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪佛兰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "雪佛兰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "0-50", "sortNum": 1, "name": "¥50以下", "groupCode": "Price", "itemCode": "Price_0-50"}, {"code": "50-100", "sortNum": 2, "name": "¥50-100", "groupCode": "Price", "itemCode": "Price_50-100"}, {"code": "100-200", "sortNum": 3, "name": "¥100-200", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-99999", "sortNum": 4, "name": "¥200以上", "groupCode": "Price", "itemCode": "Price_200-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "PickReturn", "quickSortNum": 1, "mark": "15分钟内", "itemCode": "PickReturn_StationPR", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "positionCode": "1", "name": "站内取车"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "StoreService", "quickSortNum": 4, "positionCode": "3", "itemCode": "StoreService_easyLife", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 1, "name": "无忧租"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 6, "groupCode": "StoreService", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "Promotion", "itemCode": "Promotion_3487", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "元旦特惠"}], "groupCode": "Promotion", "shortName": "优惠活动", "bitwiseType": 2, "name": "优惠活动"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "门店评分", "sortNum": 7, "bitwiseType": 2, "name": "门店评分", "groupCode": "Comment", "filterItems": [{"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "9", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}]}, {"sortNum": 8, "isSupportMulti": true, "filterItems": [{"sortNum": 7, "groupCode": "Vendor_0", "itemCode": "Vendor_0", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "携程租车中心"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "4", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "携程优选", "icon": ""}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_30164", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "懒人行卡拉比"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "W32NJrWQi551V10738o8", "productGroups": [{"sortNum": -4, "groupName": "全部车型", "hasResult": false, "groupCode": "all"}, {"sortNum": 0, "groupName": "经济轿车", "hasResult": false, "groupCode": "2"}, {"sortNum": 2, "groupName": "新能源", "hasResult": false, "groupCode": "newenergy"}, {"sortNum": 3, "groupName": "舒适轿车", "hasResult": false, "groupCode": "3"}, {"sortNum": 4, "groupName": "SUV", "hasResult": false, "groupCode": "6"}, {"sortNum": 5, "groupName": "商务车", "hasResult": false, "groupCode": "4"}, {"sortNum": 6, "groupName": "豪华轿车", "hasResult": false, "groupCode": "5"}, {"sortNum": 7, "groupName": "跑车", "hasResult": false, "groupCode": "9"}], "filters": ["NewEnergy_elect", "Promotion_3487"], "labelCodes": ["3705", "3547", "3709", "3487", "3563", "3731", "3788", "3789", "3746", "3769"], "quickFilter": [{"sortNum": 1, "groupCode": "PickReturn", "quickSortNum": 1, "mark": "15分钟内", "itemCode": "PickReturn_StationPR", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "positionCode": "1", "name": "站内取车"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "3", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "押金双免"}, {"sortNum": 7, "groupCode": "Vendor_0", "quickSortNum": 1, "positionCode": "4", "itemCode": "Vendor_-1", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "携程优选", "icon": ""}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 3, "positionCode": "5", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}, {"sortNum": 2, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "7", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "9", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 1, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "10", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}], "promotMap": {"783151896": "fO1JR51VkCbs7NQf+3/COQ=="}, "requestInfo": {"age": 30, "pickupLocationName": "凤凰国际机场", "returnDate": "/Date(1674034200000+0800)/", "pickupDate": "/Date(1673854200000+0800)/", "sourceCountryId": 1, "returnLocationName": "凤凰国际机场"}, "allVehicleCount": 2, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "id": 2, "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "filterCode": "Vendor_0", "toTime": "23:59", "lat": "18.305902", "lng": "109.413683", "name": "三亚凤凰机场店", "fromTime": "00:00"}, "filteredRecommendProducts": [{"groupSort": 0, "lowestPrice": 513, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 1275, "detail": [{"code": "1001", "amount": 2250, "amountDesc": "¥2250", "name": "租车费"}, {"code": "11037", "amount": 675, "amountDesc": "¥675", "name": "优惠券"}, {"code": "3487", "amount": 300, "amountDesc": "¥300", "name": "元旦特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥513", "originalDailyPrice": 1000, "subAmount": 513, "name": "车辆租金", "amountStr": "¥1275"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 113, "amountStr": "¥113", "detail": [{"code": "1002", "amount": 113, "amountDesc": "¥113", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 1423, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥2398", "subAmount": 2398, "name": "总价", "amountStr": "¥1423"}], "reference": {"vehicleCode": "0", "rStoreCode": "100028206", "pLev": -1, "comPriceCode": "63bb93e5aedf4a451ef3eefd", "bizVendorCode": "44444", "pStoreCode": "100028206", "packageType": 0, "priceVersion": "AWIB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAlVBt9Zm2aoDKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAJc3NZfAKPu3AoZ4Ks8yAlZlNHkKUOxZX+hKrlD37X0xQL9KoBkRKOZD5XCJO4OtUET0W6PBA/0pygpFDL5W4SLwbsmBELwBy4znzgix4OpNlG5cmxSG/+TAZ33xPHXivOXyr+jVqmAUvQQPJOpjpURj4CrOI1G42omvMudF89q37iAF2kxaBTH36HN3Vpdmg1sUaQ936ig1DQfPbwnHXoFB4+LrwKiZlwDBBa/JUOEiI7PPmDCpoDtqRSuTJKHmTjacRdCqrZStn91sz60r8BYc5EU9n5w3354Z+sCdJhzOyeoOPhfOwiijEcssatXMms2DBNghjgLo1jko+ukBUcnZEBAUr/HfM", "sendTypeForPickUpCar": 0, "skuId": 432551, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "30164", "vendorVehicleCode": "20038425"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减975", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "13791", "highestPrice": 513, "rCoup": 0, "minDPrice": 513, "pWay": "可选:免费站内取还车", "hot": 0, "minTPrice": 1423, "lowestDistance": 0.0071, "group": 0, "sortNum": 26, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["44444_0_100028206_100028206"], "introduce": "当前车型最低价"}, "minDOrinPrice": 1000, "isEasy": false, "isCredit": true, "maximumCommentCount": 25322, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 10, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "免押金", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}], "scoreSort": 0, "priceSize": 1, "isOptim": true}, {"groupSort": 0, "lowestPrice": 1175, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"amount": 2850, "detail": [{"code": "1001", "amount": 4500, "amountDesc": "¥4500", "name": "租车费"}, {"code": "11037", "amount": 1350, "amountDesc": "¥1350", "name": "优惠券"}, {"code": "3487", "amount": 300, "amountDesc": "¥300", "name": "元旦特惠"}], "code": "CAR_RENTAL_FEE", "subAmountStr": "日均¥1175", "originalDailyPrice": 2000, "subAmount": 1175, "name": "车辆租金", "amountStr": "¥2850"}, {"code": "CAR_SERVICE_FEE", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "amount": 35, "amountDesc": "¥35", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 135, "amountStr": "¥135", "detail": [{"code": "1002", "amount": 135, "amountDesc": "¥135", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 3020, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥4670", "subAmount": 4670, "name": "总价", "amountStr": "¥3020"}], "reference": {"vehicleCode": "0", "rStoreCode": "100028206", "pLev": -1, "comPriceCode": "63bb93e5aedf4a451ef3eeeb", "bizVendorCode": "44444", "pStoreCode": "100028206", "packageType": 0, "priceVersion": "AWMB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXBqxYU+SiXepTKGDqFNEE9ucoKRQy+VuEgJlELDneRhGs1icCWWSMpAJc3NZfAKPu3AoZ4Ks8yAlZlNHkKUOxZX+hKrlD37X0zqOQu7xQLaDZD5XCJO4OtUET0W6PBA/0pygpFDL5W4SLwbsmBELwBy4znzgix4OpNlG5cmxSG/+TAZ33xPHXivF+p7O9FMfsxVTLL8RzYaVs/nXkuHmYCa+i9k86uW707RrGj9xfIHEzKJ2NTODxrHmUy8p6Ubmw5AI7DGBPDsCBfV7Na5937d3yk2wE2srtcEFY4uxpam6KZvsCNf3ch8BHz6j2hE8TJDqreNh+ZvRMHMLQ12VA7Hoja10oAp0A7CNnci0raRul2sEZWld0aCeWWrrNNAqQ8bfwuKhfX3EV/E59t4PTDv", "sendTypeForPickUpCar": 0, "skuId": 432575, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "30164", "vendorVehicleCode": "1036"}, "isMinTPriceVendor": true}], "pTag": {"category": 3, "sortNum": 10000, "amountTitle": "共减1650", "groupCode": "MarketGroup102", "code": "30", "title": "元旦特惠+券", "colorCode": "15", "mergeId": 0, "type": 3, "groupId": 1, "labelCode": "3487"}, "vehicleCode": "16972", "highestPrice": 1175, "rCoup": 0, "minDPrice": 1175, "pWay": "可选:免费站内取还车", "hot": 0, "minTPrice": 3020, "lowestDistance": 0.0071, "group": 0, "sortNum": 32, "maximumRating": 4.9, "vehicleRecommendProduct": {"productCodes": ["44444_0_100028206_100028206"], "introduce": "当前车型最低价"}, "minDOrinPrice": 2000, "isEasy": false, "isCredit": false, "maximumCommentCount": 25322, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [], "scoreSort": 0, "priceSize": 1, "isOptim": true}], "vehicleList": [{"subGroupCode": "newenergy", "luggageNo": 3, "autoPark": false, "endurance": "续航480km-664km", "fuelType": "纯电动", "charge": "快充1小时,慢充10小时", "imageList": ["https://dimg04.c-ctrip.com/images/0410g120008n3a8t6A06F.png"], "license": "", "isSpecialized": true, "groupCode": "5", "zhName": "特斯拉Model3(进口)", "doorNo": 4, "driveMode": "后置后驱、双电机四驱", "carPhone": true, "vehicleCode": "13791", "style": "", "name": "特斯拉Model3(进口)", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0415h120008n38pao6C65.jpg", "fuel": "", "passengerNo": 5, "luggageNum": "可放3个24寸行李箱", "autoStart": true, "isHot": false, "vehicleAccessoryImages": ["https://pages.c-ctrip.com/carisd/app/dta148666.jpg"], "transmissionType": 1, "brandName": "特斯拉", "oilType": 5, "struct": "", "groupName": "豪华轿车", "groupSubClassCode": "", "brandEName": "特斯拉", "licenseStyle": "2", "autoBackUp": true, "transmissionName": "自动挡"}, {"transmissionName": "自动挡", "realityImageUrl": "https://dimg04.c-ctrip.com//images/0416t120008as53tq8B30.jpg", "style": "", "isHot": false, "imageList": ["https://dimg04.c-ctrip.com/images/0414y120009bjdlw86A0A.png"], "carPhone": true, "vehicleCode": "16972", "autoStart": true, "subGroupCode": "newenergy", "fuel": "", "fuelType": "纯电动", "licenseStyle": "2", "oilType": 5, "driveMode": "后置后驱、双电机四驱、三电机四驱", "name": "特斯拉Model S", "zhName": "特斯拉Model S", "brandName": "特斯拉", "passengerNo": 5, "doorNo": 5, "isSpecialized": true, "struct": "", "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0416d120008as6ctuF2D8.jpg", "https://dimg04.c-ctrip.com//images/0410v120008as3msgF4EA.jpg", "https://dimg04.c-ctrip.com//images/0411o120008as2y0399BC.jpg", "https://dimg04.c-ctrip.com//images/0410o120008as5cft40F2.jpg", "https://dimg04.c-ctrip.com//images/04114120008as3y4oF5FF.jpg", "https://dimg04.c-ctrip.com//images/0414s120008as54lv36CE.jpg", "https://dimg04.c-ctrip.com//images/0416d120008as6ctuF2D8.jpg", "https://dimg04.c-ctrip.com//images/0410v120008as3msgF4EA.jpg", "https://dimg04.c-ctrip.com//images/0411o120008as2y0399BC.jpg", "https://dimg04.c-ctrip.com//images/0410o120008as5cft40F2.jpg"], "groupCode": "5", "endurance": "续航840km", "luggageNum": "可放5个24寸行李箱", "luggageNo": 5, "brandEName": "特斯拉", "groupSubClassCode": "", "groupName": "豪华轿车", "transmissionType": 1, "license": "", "charge": "快充1小时,慢充10小时"}], "storeList": [{"pickOffLevel": -1, "storeCode": "100028206", "pickUpLevel": -1}], "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": true, "isLastPage": true, "uniqSign": "120010765100002012324n5t4A234y99s7t826E9", "pHub": 1, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"sortNum": 1, "showLayer": 1, "title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "subTitle": "*国定节假日以页面披露为准"}, {"sortNum": 2, "showLayer": 0, "title": "安心保障", "titleExtra": "(需加购优享服务)", "type": 1, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "subTitle": "*覆盖损失范围以预订页面内披露为准"}, {"sortNum": 3, "title": "优质车况", "subTitle": "", "type": 1, "description": "3年内车龄", "showLayer": 0}, {"sortNum": 10, "title": "电量保障", "type": 0, "description": "纯电车保障续航350KM", "showLayer": 0}, {"sortNum": 10, "title": "满油取车", "subTitle": "", "type": 0, "description": "保障取车时油量满（不含纯电车）", "showLayer": 0}, {"sortNum": 99, "title": "免加油/充电服务费", "subTitle": "油量/电量差价仍需支付", "type": 0, "description": "还车时，若油量/电量少于取车油量/电量，无需支付加油/充电服务费"}]}, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "5275620543105283310", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a056b21-464788-9274", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1673237580316+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 97, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 97, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1673237580237, "afterFetch": 1673237580334, "hasRetry": false}}