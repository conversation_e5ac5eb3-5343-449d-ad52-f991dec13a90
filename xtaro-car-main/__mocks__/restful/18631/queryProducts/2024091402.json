{"baseResponse": {"extMap": {}, "cost": 24, "hasResult": true, "extraIndexTags": {"pcName": "中国", "rcId": "1", "rcName": "中国", "rCityName": "阿尔山", "rCityId": "1658", "pCityId": "1658", "pcId": "1", "pCityName": "阿尔山"}, "code": "200", "errorCode": "0", "apiResCodes": [], "returnMsg": "OK", "message": "", "requestId": "ad20265b-ddd8-42e6-8649-d2944892bb14", "isSuccess": true}, "rHub": 0, "isKlbData": true, "allVendorPriceCount": 3, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isFromSearch": false, "extras": {"packageLevelAB": "", "abVersion": "230104_DSJT_fil10|B,221011_DSJT_lqdn|B,220323_DSJT_rank2|B", "isLicensePlateHideShow": "0", "serverRequestId": "jgceID0822ms8itg6G62", "packageLevelSwitch": "0", "selfServiceSwitch": "1", "commodityClass2Version": "1", "isNewLicensePlate": "0", "prepProductGroupTopSwitch": "0"}, "filterMenuItems": [{"code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"sortNum": 1, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "CarAge", "itemCode": "CarAge_3509", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "半年内车龄"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "8", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 3, "groupCode": "CarAge", "quickSortNum": 2, "positionCode": "8", "itemCode": "CarAge_3547", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "两年内车龄"}], "groupCode": "CarAge", "shortName": "车龄", "bitwiseType": 2, "name": "车龄"}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "SeatGroup", "itemCode": "SeatGroup_3", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "5座"}, {"sortNum": 4, "groupCode": "SeatGroup", "itemCode": "SeatGroup_4", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "6座"}, {"sortNum": 5, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "13", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}, {"sortNum": 6, "groupCode": "SeatGroup", "itemCode": "SeatGroup_6", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "8座及以上"}], "groupCode": "SeatGroup", "shortName": "座位", "bitwiseType": 2, "name": "座位数"}, {"shortName": "排挡", "sortNum": 3, "bitwiseType": 2, "name": "车辆排挡", "groupCode": "Transmission", "filterItems": [{"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "10", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}]}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "NewEnergy", "itemCode": "NewEnergy_elect", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "纯电动"}, {"sortNum": 2, "groupCode": "NewEnergy", "itemCode": "NewEnergy_mix", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "新能源混动"}, {"sortNum": 3, "groupCode": "NewEnergy", "itemCode": "NewEnergy_gas", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "汽油"}], "groupCode": "NewEnergy", "shortName": "能源类型", "bitwiseType": 2, "name": "能源类型"}, {"sortNum": 5, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "9", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 3, "groupCode": "VehicleAccessory", "quickSortNum": 7, "positionCode": "9", "itemCode": "VehicleAccessory_radar", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "倒车雷达"}, {"sortNum": 4, "groupCode": "VehicleAccessory", "quickSortNum": 8, "positionCode": "9", "itemCode": "VehicleAccessory_tachograph", "binaryDigit": 64, "isQuickItem": true, "bitwiseType": 1, "name": "行车记录仪"}, {"sortNum": 5, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_MobileHolder", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 1, "name": "手机支架"}, {"sortNum": 6, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_LeatherSeat", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 1, "name": "真皮座椅"}, {"sortNum": 7, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Refrigerator", "binaryDigit": 512, "isQuickItem": false, "bitwiseType": 1, "name": "车载冰箱"}, {"sortNum": 9, "groupCode": "VehicleAccessory", "itemCode": "VehicleAccessory_Childseat", "binaryDigit": 2048, "isQuickItem": false, "bitwiseType": 1, "name": "儿童座椅"}], "groupCode": "VehicleAccessory", "shortName": "车辆配置", "bitwiseType": 1, "name": "车辆配置"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "HotBrand", "itemCode": "HotBrand_奥迪", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"sortNum": 2, "groupCode": "HotBrand", "itemCode": "HotBrand_别克", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "HotBrand", "itemCode": "HotBrand_大众", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"sortNum": 6, "groupCode": "HotBrand", "itemCode": "HotBrand_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}], "groupCode": "HotBrand", "shortName": "热门品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_a0", "itemCode": "BrandGroup_a0_奥迪", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "奥迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}], "groupCode": "BrandGroup_a0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_别克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "别克", "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_北京", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "北京", "icon": "//pages.c-ctrip.com/carisd/brandlogo/beijin.png"}, {"sortNum": 4, "groupCode": "BrandGroup_b0", "itemCode": "BrandGroup_b0_比亚迪", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "比亚迪", "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "groupCode": "BrandGroup_b0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_d0", "itemCode": "BrandGroup_d0_大众", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "大众", "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}], "groupCode": "BrandGroup_d0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_f0", "itemCode": "BrandGroup_f0_丰田", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "丰田", "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}], "groupCode": "BrandGroup_f0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_g0", "itemCode": "BrandGroup_g0_广汽传祺", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "广汽传祺", "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}], "groupCode": "BrandGroup_g0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_h0", "itemCode": "BrandGroup_h0_哈弗", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "哈弗", "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}], "groupCode": "BrandGroup_h0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_j0", "itemCode": "BrandGroup_j0_捷途", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "捷途", "icon": "//pages.c-ctrip.com/carisd/brandlogo/jietu.png"}], "groupCode": "BrandGroup_j0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_s0", "itemCode": "BrandGroup_s0_三菱", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "三菱", "icon": "//pages.c-ctrip.com/carisd/brandlogo/sanling.png"}], "groupCode": "BrandGroup_s0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_坦克", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "坦克", "icon": "https://dimg04.c-ctrip.com/images/0yc3612000aoma5tvACF1.png"}, {"sortNum": 4, "groupCode": "BrandGroup_t0", "itemCode": "BrandGroup_t0_特斯拉", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "特斯拉", "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}], "groupCode": "BrandGroup_t0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_x0", "itemCode": "BrandGroup_x0_雪佛兰", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "雪佛兰", "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "groupCode": "BrandGroup_x0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}, {"sortNum": 7, "isSupportMulti": true, "filterItems": [{"sortNum": 4, "groupCode": "BrandGroup_z0", "itemCode": "BrandGroup_z0_隆翠", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "隆翠"}], "groupCode": "BrandGroup_z0", "shortName": "全部品牌", "bitwiseType": 2, "name": "车辆品牌"}], "name": "快速选车"}, {"code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"shortName": "价格", "sortNum": 1, "name": "价格", "groupCode": "Price", "filterItems": [{"code": "100-200", "sortNum": 3, "name": "¥200以下", "groupCode": "Price", "itemCode": "Price_100-200"}, {"code": "200-99999", "sortNum": 4, "name": "¥200以上", "groupCode": "Price", "itemCode": "Price_200-99999"}]}, {"sortNum": 2, "isSupportMulti": true, "filterItems": [{"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_FreePickupOnDoor", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 2, "name": "免费送车上门"}, {"sortNum": 2, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupOnDoor", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "送车上门"}, {"sortNum": 3, "groupCode": "PickReturn", "itemCode": "PickReturn_CostPickupOnDoor", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "收费送车上门"}, {"sortNum": 4, "groupCode": "PickReturn", "itemCode": "PickReturn_FreeShuttle", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "免费接至门店取车"}, {"sortNum": 5, "groupCode": "PickReturn", "itemCode": "PickReturn_PickupSelf", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "自行到店取车"}], "groupCode": "PickReturn", "shortName": "取还方式", "bitwiseType": 2, "name": "取车方式"}, {"sortNum": 3, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "StoreService", "quickSortNum": 3, "positionCode": "4", "itemCode": "StoreService_easyLife", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 1, "name": "无忧租"}, {"sortNum": 4, "groupCode": "StoreService", "quickSortNum": 4, "positionCode": "4", "itemCode": "StoreService_FreeDepositAllCtrip", "binaryDigit": 8, "isQuickItem": true, "bitwiseType": 1, "name": "信用免押"}, {"sortNum": 5, "groupCode": "StoreService", "itemCode": "StoreService_Unlimit", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 1, "name": "不限里程"}, {"sortNum": 6, "groupCode": "StoreService", "quickSortNum": 2, "positionCode": "4", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}], "groupCode": "StoreService", "shortName": "门店服务", "bitwiseType": 1, "name": "门店服务"}, {"sortNum": 4, "isSupportMulti": true, "filterItems": [{"sortNum": 1, "groupCode": "SelfService", "quickSortNum": 1, "positionCode": "3", "itemCode": "SelfService_Support", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 2, "groupCode": "SelfService", "itemCode": "SelfService_UnSupport", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "非自助取还"}], "groupCode": "SelfService", "shortName": "自助取还", "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 6, "isSupportMulti": true, "filterItems": [{"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_1", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "身份证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_2", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "护照"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_7", "binaryDigit": 4, "isQuickItem": false, "bitwiseType": 2, "name": "回乡证"}, {"sortNum": 3, "groupCode": "Ceritificate", "itemCode": "Ceritificate_8", "binaryDigit": 2, "isQuickItem": false, "bitwiseType": 2, "name": "台胞证"}], "groupCode": "Ceritificate", "shortName": "取车证件", "bitwiseType": 2, "name": "取车证件"}, {"shortName": "门店评分", "sortNum": 8, "bitwiseType": 2, "name": "门店评分", "groupCode": "Comment", "filterItems": [{"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "12", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 3, "groupCode": "Comment", "itemCode": "Comment_4.0", "binaryDigit": 1, "isQuickItem": false, "bitwiseType": 2, "name": "4.0分以上"}]}, {"sortNum": 9, "isSupportMulti": true, "filterItems": [{"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_13088", "binaryDigit": 8, "isQuickItem": false, "bitwiseType": 2, "name": "一嗨租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_45921", "binaryDigit": 16, "isQuickItem": false, "bitwiseType": 2, "name": "鑫宇租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_80093", "binaryDigit": 32, "isQuickItem": false, "bitwiseType": 2, "name": "逸优租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_15001678", "binaryDigit": 64, "isQuickItem": false, "bitwiseType": 2, "name": "闪租租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_15002260", "binaryDigit": 128, "isQuickItem": false, "bitwiseType": 2, "name": "长江租车"}, {"sortNum": 8, "groupCode": "Vendor_0", "itemCode": "Vendor_15006225", "binaryDigit": 256, "isQuickItem": false, "bitwiseType": 2, "name": "公明出行"}], "groupCode": "Vendor_0", "shortName": "租车公司", "bitwiseType": 2, "name": "租车公司"}], "name": "更多筛选"}], "productGroupsHashCode": "q29cY8935a2K19TcmP40", "timeInterval": 61.302001953125, "productGroups": [{"sortNum": -4, "groupName": "全部车型", "productList": [{"groupSort": 0, "lowestPrice": 419, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 419, "amountStr": "¥419", "detail": [{"code": "1001", "amount": 419, "amountDesc": "¥419", "name": "租车费"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 489, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥489", "subAmount": 489, "name": "总价", "amountStr": "¥489"}], "reference": {"vehicleCode": "0", "rStoreCode": "182861", "pLev": -1, "bizVendorCode": "SD6991", "pStoreCode": "182861", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTgyODYxXzE0ODhfMV80MTkuMF80MTkuMF8wLjBfNDg5LjBfNDE5LjBfNDg5LjBfMF8wXzAuMF8wLjBfNTAuMF8yMC4wXzBfMF80ODAzNjczMQ==", "sendTypeForPickUpCar": 0, "skuId": 48036731, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "13088", "vendorVehicleCode": "4205"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "1488", "highestPrice": 419, "pWay": "", "minDPrice": 419, "vehicleKey": "0_1488_", "hot": 0, "minTPrice": 489, "lowestDistance": 0.0824, "group": 0, "type": 0, "sortNum": 4, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11977_0_183198_183198"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 1249, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 459, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 459, "amountStr": "¥459", "detail": [{"code": "1001", "amount": 459, "amountDesc": "¥459", "name": "租车费"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 50, "amountStr": "¥50", "detail": [{"code": "1002", "amount": 50, "amountDesc": "¥50", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 529, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥529", "subAmount": 529, "name": "总价", "amountStr": "¥529"}], "reference": {"vehicleCode": "0", "rStoreCode": "182861", "pLev": -1, "bizVendorCode": "SD6991", "pStoreCode": "182861", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTgyODYxXzQ3ODFfMV80NTkuMF80NTkuMF8wLjBfNTI5LjBfNDU5LjBfNTI5LjBfMF8wXzAuMF8wLjBfNTAuMF8yMC4wXzBfMF8xMzUyNjQyMw==", "sendTypeForPickUpCar": 0, "skuId": 13526423, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "13088", "vendorVehicleCode": "4108"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "4781", "highestPrice": 459, "pWay": "", "minDPrice": 459, "vehicleKey": "0_4781_", "hot": 0, "minTPrice": 529, "lowestDistance": 0.0824, "group": 0, "type": 0, "sortNum": 6, "maximumRating": 4.8, "vehicleRecommendProduct": {"productCodes": ["SD6991_0_182861_182861"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 67, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}, {"groupSort": 0, "lowestPrice": 869, "modifySameVehicle": false, "vendorPriceList": [{"fees": [{"code": "CAR_RENTAL_FEE", "amount": 869, "amountStr": "¥869", "detail": [{"code": "1001", "amount": 869, "amountDesc": "¥869", "name": "租车费"}], "name": "车辆租金"}, {"code": "CAR_SERVICE_FEE", "amount": 20, "amountStr": "¥20", "detail": [{"code": "1003", "amount": 20, "amountDesc": "¥20", "showFree": false, "name": "车行手续费", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等"}], "name": "服务/手续费", "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "amount": 70, "amountStr": "¥70", "detail": [{"code": "1002", "amount": 70, "amountDesc": "¥70", "showFree": false, "name": "基础服务费"}], "name": "车行保障服务费", "currencyCode": "¥"}, {"amount": 959, "code": "10001", "currencyCode": "¥", "subAmountStr": "¥959", "subAmount": 959, "name": "总价", "amountStr": "¥959"}], "reference": {"vehicleCode": "0", "rStoreCode": "182861", "pLev": -1, "bizVendorCode": "SD6991", "pStoreCode": "182861", "packageType": 0, "priceVersion": "SH-PRICEVERSION_MTgyODYxXzQwODhfMV84NjkuMF84NjkuMF8wLjBfOTU5LjBfODY5LjBfOTU5LjBfMF8wXzAuMF8wLjBfNzAuMF8yMC4wXzBfMF80NDMwNTU3MA==", "sendTypeForPickUpCar": 0, "skuId": 44305570, "sendTypeForPickOffCar": 0, "klbVersion": 1, "rLev": -1, "vendorCode": "13088", "vendorVehicleCode": "4215"}, "isMinTPriceVendor": true}], "rCoup": 0, "vehicleCode": "4088", "highestPrice": 869, "pWay": "", "minDPrice": 869, "vehicleKey": "0_4088_", "hot": 0, "minTPrice": 959, "lowestDistance": 0.0824, "group": 0, "type": 0, "sortNum": 19, "maximumRating": 5, "vehicleRecommendProduct": {"productCodes": ["SD11977_0_796916_796916"], "introduce": "当前车型最低价"}, "minDOrinPrice": 0, "isEasy": false, "isCredit": true, "maximumCommentCount": 90, "productRef": {"license": "", "licenseStyle": "2", "licenseTag": ""}, "hotScore": 0, "hotType": 0, "reactId": "**********", "outTags": [{"category": 2, "sortNum": 41, "mergeId": 0, "groupCode": "MarketGroup1347", "code": "7", "title": "信用免押", "colorCode": "2", "type": 1, "groupId": 3, "labelCode": "3746"}, {"category": 2, "sortNum": 1, "mergeId": 0, "groupCode": "MarketGroup1325", "code": "6", "title": "自助取还", "showLayer": 1, "colorCode": "1", "type": 1, "groupId": 3, "labelCode": "3866"}], "scoreSort": 0, "priceSize": 1, "isOptim": false}], "groupCode": "all", "dailyPrice": 419, "hasResult": true}, {"sortNum": 1, "groupName": "经济轿车", "hasResult": false, "groupCode": "2", "groupAction": 0}, {"sortNum": 3, "groupName": "新能源", "hasResult": false, "groupCode": "newenergy", "groupAction": 0}, {"sortNum": 4, "groupName": "舒适轿车", "hasResult": false, "groupCode": "3", "groupAction": 0}, {"sortNum": 5, "groupName": "SUV", "hasResult": true, "groupCode": "6", "dailyPrice": 419, "groupAction": 0}, {"sortNum": 6, "groupName": "商务车", "hasResult": true, "groupCode": "4", "dailyPrice": 869, "groupAction": 0}, {"sortNum": 9, "groupName": "房车", "hasResult": false, "groupCode": "11", "groupAction": 0}], "filters": ["SelfService_Support", "StoreService_FreeCancel"], "resBodySize": 31988, "labelCodes": ["3563", "3495", "3548", "3504", "3827", "3547", "3503", "3502", "3501", "3709", "3510", "3696", "3698", "3731", "3509", "3788", "3810", "3866", "3789", "3679", "3746", "3757"], "quickFilter": [{"sortNum": 2, "groupCode": "PickReturn", "quickSortNum": 2, "positionCode": "1", "itemCode": "PickReturn_FreePickupOnDoor", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 2, "name": "免费送车上门"}, {"sortNum": 1, "groupCode": "SelfService", "quickSortNum": 1, "positionCode": "3", "itemCode": "SelfService_Support", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自助取还"}, {"sortNum": 6, "groupCode": "StoreService", "quickSortNum": 2, "positionCode": "4", "itemCode": "StoreService_FreeCancel", "binaryDigit": 32, "isQuickItem": true, "bitwiseType": 1, "name": "免费取消"}, {"sortNum": 2, "groupCode": "CarAge", "quickSortNum": 1, "positionCode": "8", "itemCode": "CarAge_3510", "binaryDigit": 2, "isQuickItem": true, "bitwiseType": 2, "name": "一年内车龄"}, {"sortNum": 2, "groupCode": "VehicleAccessory", "quickSortNum": 6, "positionCode": "9", "itemCode": "VehicleAccessory_ReversingImage", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 1, "name": "倒车影像"}, {"sortNum": 1, "groupCode": "Transmission", "quickSortNum": 1, "positionCode": "10", "itemCode": "Transmission_1", "binaryDigit": 1, "isQuickItem": true, "bitwiseType": 2, "name": "自动挡"}, {"sortNum": 1, "groupCode": "Comment", "quickSortNum": 1, "positionCode": "12", "itemCode": "Comment_4.8", "binaryDigit": 4, "isQuickItem": true, "bitwiseType": 2, "name": "4.8分以上"}, {"sortNum": 5, "groupCode": "SeatGroup", "quickSortNum": 1, "positionCode": "13", "itemCode": "SeatGroup_5", "binaryDigit": 16, "isQuickItem": true, "bitwiseType": 2, "name": "7座"}], "promotMap": {}, "requestInfo": {"rLongitude": 119.947666, "rDate": "20240914230000", "age": 30, "pCityId": 1658, "returnDate": "/Date(1726326000000+0800)/", "sourceCountryId": 1, "pLatitude": 47.172609, "rLatitude": 47.172609, "pLongitude": 119.947666, "pDate": "20240914223000", "rCityId": 1658, "pickupLocationName": "阿尔山站", "returnLocationName": "阿尔山站", "pickupDate": "/Date(1726324200000+0800)/"}, "allVehicleCount": 3, "commNotices": [], "hasResultWithoutFilter": true, "productGroupCodeUesd": "all", "rentCenter": {"filterCode": "Vendor_0"}, "checkResponseTime": 1726310105983.1619, "vehicleList": [{"skylight": "部分车辆支持", "vehicleKey": "0_1488_", "luggageNo": 4, "carPlay": "部分车辆支持CarLife/原厂互联/映射/CarPlay/HiCar", "displacement": "2.0L", "autoPark": false, "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV2512000c55lsub222D.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "丰田RAV4荣放", "doorNo": 5, "autoParkDesc": {"type": 0, "typeDesc": "不支持"}, "driveMode": "前置前驱/前置四驱", "chargeInterface": "部分车辆支持USB/AUX/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=2669&app_ver=10.5", "vehicleCode": "1488", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "丰田RAV4荣放", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV2g12000b5us43096F8.jpg", "fuel": "92号或95号", "passengerNo": 5, "luggageNum": "可放4个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5h12000c5gxh4g6155.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4v12000c5gxsbv4FA8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV6q12000c5gxhi065E8.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4512000c5gxk3w8B9C.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV2u12000c5gxh4qDDFD.jpg?mark=yiche"], "transmissionType": 1, "brandName": "丰田", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "丰田", "licenseStyle": "2", "vehiclesSetId": "10", "guidSys": "部分车辆支持定速巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_4781_", "luggageNo": 5, "carPlay": "支持CarPlay/CarLife/原厂互联/映射", "displacement": "2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV6r12000c5645or0B7C.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "6", "zhName": "大众探岳", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置四驱/前置前驱", "chargeInterface": "支持USB/SD/Type-C", "mediaTypes": [3, 2], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=8032&app_ver=10.5", "carPhone": true, "vehicleCode": "4781", "style": "", "carPhoneDesc": {"type": 1, "typeDesc": "支持"}, "name": "大众探岳", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV3l12000ba5id609317.jpg", "fuel": "95号或98号", "passengerNo": 5, "luggageNum": "可放5个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV5z12000c5o3g4d64C5.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV1f12000c5o3juv0487.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3o12000c5o3dyk7FF3.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV4n12000c5o3orr5CCC.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0o12000c7ncdifD1F3.jpg?mark=yiche"], "transmissionType": 1, "brandName": "大众", "oilType": 3, "struct": "SUV", "groupName": "SUV", "autoBackupDesc": {"type": 1, "typeDesc": "支持"}, "groupSubClassCode": "", "brandEName": "大众", "licenseStyle": "2", "autoBackUp": true, "vehiclesSetId": "10", "guidSys": "支持定速巡航/全速自适应巡航", "transmissionName": "自动挡"}, {"skylight": "部分车辆支持", "vehicleKey": "0_4088_", "luggageNo": 1, "carPlay": "部分车辆支持CarLife", "displacement": "2.0T", "charge": "", "imageList": ["https://dimg04.c-ctrip.com/images/0RV4h12000etghea704DF.png?mark=yiche"], "license": "", "isSpecialized": true, "groupCode": "4", "zhName": "传祺M8（21-23款）", "doorNo": 5, "autoParkDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "driveMode": "前置前驱", "chargeInterface": "部分车辆支持USB", "mediaTypes": [3], "vr": "https://vrcar.yiche.com/vr360/PanoInnerNew.html?albumId=430&app_ver=10.5", "vehicleCode": "4088", "style": "", "carPhoneDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "name": "传祺M8（21-23款）", "realityImageUrl": "https://dimg04.c-ctrip.com/images/0RV4h12000etghea704DF.png?mark=yiche", "fuel": "95号", "passengerNo": 7, "luggageNum": "可放1个24寸行李箱", "autoStartDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com/images/0RV6w12000c5k87luE916.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV3e12000c5k85n5D01B.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5b12000c5k813d59ED.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV5p12000c5k88sq7E71.jpg?mark=yiche", "https://dimg04.c-ctrip.com/images/0RV0t12000c5k86bpB09C.jpg?mark=yiche"], "transmissionType": 1, "brandName": "广汽传祺", "oilType": 3, "struct": "MPV", "groupName": "商务车", "autoBackupDesc": {"type": 2, "typeDesc": "部分车辆支持"}, "groupSubClassCode": "", "brandEName": "广汽传祺", "licenseStyle": "2", "vehiclesSetId": "48", "guidSys": "部分车辆支持定速巡航/全速自适应巡航", "transmissionName": "自动挡"}], "storeList": [{"pickOffLevel": -1, "storeCode": "182861", "pickUpLevel": -1}], "promptInfos": [{"type": 18, "locations": [{"groupCode": "all", "index": 1}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/1tg5t12000cixdsaaBF66.png"}], "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "isAll": true, "isLastPage": true, "uniqSign": "12001139890321479536gf4z5r7agAN5oRy0wXe4", "pHub": 0, "feeMap": [{"subName": "（含车损、第三者责任等保障）", "code": "1002"}, {"subName": "（含车辆清洁、单据打印等）", "code": "1003"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"sortNum": 1, "showLayer": 1, "title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "subTitle": "*国定节假日以页面披露为准"}, {"sortNum": 2, "showLayer": 0, "title": "安心保障", "titleExtra": "(需加购尊享服务)", "type": 1, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "subTitle": "*覆盖损失范围以预订页面内披露为准"}, {"sortNum": 10, "title": "满油取车", "subTitle": "", "type": 0, "description": "保障取车时油量满", "showLayer": 0}, {"sortNum": 99, "title": "免加油服务费/充电服务费", "subTitle": "", "type": 0, "description": "还车时，若油量/电量少于取车油量/电量，无需支付加油服务费/充电服务费，所产生的油量/电量差价仍需支付"}]}, "checkRequestTime": 1726310105921.8599, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "hint": "优选门店无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 2, "title": "无忧租 安心首选", "hint": "无忧租超值价", "sTitle": "9大无忧特权"}, {"id": 3, "title": "全国连锁 服务放心", "hint": "一嗨租车超值价", "sTitle": ""}, {"id": 4, "title": "上门送取车 取还超便捷", "hint": "送车上门超值价", "sTitle": ""}, {"id": 5, "title": "信用租 押金双免", "hint": "押金双免超值价", "sTitle": ""}, {"id": 6, "title": "新车保障 车况佳", "hint": "新车超值价", "sTitle": ""}, {"id": 7, "title": "超值特价 高性价比", "hint": "超值特价", "sTitle": ""}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "ResponseStatus": {"Extension": [{"Value": "7474609429160293305", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a2a7799-479530-2271496", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1726310106226+0800)/"}, "isRecommend": false, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=SelfService_Support|StoreService_FreeCancel&listExtraMaps[packageLevelVersion]=1&listExtraMaps[nonJumpFlow]=0&modify=&orderId=&pickupPointInfo=2024-09-14 22:30:00|阿尔山站|1658|47.172609|119.947666|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=all&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2024-09-14 23:00:00|阿尔山站|1658|47.172609|119.947666|||&sortType=1&uid=@@PAGENUM@@1", "groupId": "18631/queryProducts?batch=", "networkCost": 78, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 65, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1726310105920, "afterFetch": 1726310105998, "hasRetry": false, "loadDiffCost": 13, "originNetworkCost": 65}}