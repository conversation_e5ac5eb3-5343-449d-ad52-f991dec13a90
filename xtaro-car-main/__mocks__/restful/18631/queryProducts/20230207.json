{"recommendInfo": {"promptTitle": "暂时没有符合要求的车辆哦", "buttonTitle": "修改取还车条件", "type": 3, "promptSubTitle": "建议您修改取还车条件"}, "extras": {"serverRequestId": "Pufbj62695kSxUH6yV93", "abVersion": "220126_DSJT_lqd|B,220323_DSJT_rank2|B,220901_DSJT_RECV|B", "hasListSign": "ddfdgdgfgfh44516Nwa"}, "baseResponse": {"extMap": {"end": "2022-08-30 15:24:57", "allCost": "241.0", "start": "2022-08-30 15:24:56", "shoppingCost_1": "241.0", "uid": "M00020422", "dropoffCityId": "1658", "pageName": "List", "apiCost": "240.0", "restCost": "1", "pickupCityId": "1658"}, "errorCode": "32101", "hasResult": false, "code": "200", "showMessage": "暂时没有符合要求的车辆哦,建议您修改取还车条件", "apiResCodes": ["32101"], "returnMsg": "暂时没有符合要求的车辆哦,建议您修改取还车条件", "message": "暂时没有符合要求的车辆哦,建议您修改取还车条件", "requestId": "7e037d7e-6013-4a4f-bd8f-20eb1514453d", "isSuccess": false}, "allVehicleCount": 0, "commNotices": [], "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "uniqSign": "", "allVendorPriceCount": 0, "isAll": true, "ResponseStatus": {"Extension": [{"Value": "4783224755497007460", "Id": "CLOGGING_TRACE_ID"}, {"Value": "100025527-0a068a24-461623-116543", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1661844297176+0800)/"}, "basicData": {"sortItems": [{"code": "1", "sortNum": 1, "title": "推荐排序", "type": 1}, {"code": "2", "sortNum": 2, "title": "总价 低→高", "type": 2}]}, "promptInfos": [{"contents": [{"contentStyle": "1", "stringObjs": [{"style": "4", "url": "https://m.ctrip.com/tangram/MjE5NDc=?ctm_ref=vactang_page_21947&isHideNavBar=YES"}]}]}], "appResponseMap": {"isFromCache": false, "isCacheValid": true, "cacheKey": "adultNumbers=2&age=30&baseRequest=||||&childrenNumbers=0&filters=&modify=&orderId=&pickupPointInfo=2022-08-30 19:30:00|伊尔施机场|1658|47.311892|119.918574|||&productFilter[vendorIds]=&productFilter[storeIds]=&productGroupCode=all&productGroupCodeFirst=&queryListCacheId=&returnPointInfo=2022-09-01 19:30:00|伊尔施机场|1658|47.311892|119.918574|||&sortType=1&uid=M00020422@@PAGENUM@@1", "networkCost": 376, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 375, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1661844294720, "afterFetch": 1661844295096, "hasRetry": false, "loadDiffCost": 1, "originNetworkCost": 375, "isSuccess": false, "hasResult": false}}