{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "f0bcff13-b751-4424-a764-8f956f11a89f", "extMap": {"runAsyncCost_2": "0.0", "checkRentCenter_2": "0.0", "apiCost": "1251.0", "mergeGroup_9": "1.0", "contextBuilderCost_3": "32.0", "restOriginalCost": "2731.0", "pageName": "List", "ubtProcessCost_8": "1.0", "initBaseData_1": "32.0", "calculatePreAuth_3": "0.0", "mergeGroupSize_9": "47", "uid": "13122186775", "mergeGroupSize_7": "3", "mergeGroup_newenergy": "7.0", "allCost": "2730.0", "end": "2022-12-28 22:59:59", "dataConvertResCost": "1477.0", "dropoffCityId": "17", "IncludeFeesCost": "0.0", "start": "2022-12-28 22:59:57", "shoppingCost_1": "1252.0", "mergeGroupSize_2": "65", "mergeGroupSize_newenergy": "69", "mergeGroupSize_5": "69", "mergeGroupSize_6": "114", "mergeGroupSize_3": "47", "gsCost": "1.0", "mergeGroupSize_4": "44", "buildInfoCost_2": "330.0", "buildInfoCost_3": "1.0", "buildInfoCost_4": "332.0", "setProductGroupsHashCodeCostAffect": "0.0", "buildInfoCost_1": "0.0", "mergeGroup_7": "0.0", "productGroupCost_6": "17.0", "mergeGroup_6": "3.0", "lastInfoCost_7": "1096.0", "originalCode": "200", "mergeGroup_5": "1.0", "mergeGroup_4": "2.0", "mergeGroup_3": "1.0", "mergeGroup_2": "2.0", "pickupCityId": "17", "7_getFromRedisCost": "16", "newenergy_getFromRedisCost": "80", "2_getFromRedisCost": "50", "4_getFromRedisCost": "48", "needWait": "0", "6_getFromRedisCost": "74", "getPageInfoFromResponse_pre": "6", "requestId": "797c3f76-f1a9-46c9-8ba2-5fc1f83fd6f8", "9_getFromRedisCost": "36", "responseHeadCost": "39", "5_getFromRedisCost": "73", "3_getFromRedisCost": "45", "sourceFrom": "ISD_C_APP", "usePage": "1", "totalCostTime": "136", "restCost": "1"}, "apiResCodes": [], "hasResult": true, "errorCode": "0", "message": ""}, "ResponseStatus": {"Errors": [], "Extension": []}, "requestInfo": {"pickupDate": "2023-01-02 12:00:00", "pickupLocationName": "萧山国际机场", "returnDate": "2023-01-04 12:00:00", "returnLocationName": "萧山国际机场", "sourceCountryId": 1, "age": 30}, "allVehicleCount": 389, "allVendorPriceCount": 1039, "filterMenuItems": [{"name": "快速选车", "code": "QuickChoose", "sortNum": 1, "hierarchy": 1, "filterGroups": [{"name": "车龄", "sortNum": 1, "groupCode": "CarAge", "bitwiseType": 2, "filterItems": [{"itemCode": "CarAge_3509", "name": "半年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "8"}, {"itemCode": "CarAge_3510", "name": "一年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "8"}, {"itemCode": "CarAge_3547", "name": "两年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 3, "positionCode": "8"}], "shortName": "车龄"}, {"name": "座位数", "sortNum": 2, "groupCode": "SeatGroup", "bitwiseType": 2, "filterItems": [{"itemCode": "SeatGroup_1", "name": "2座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "SeatGroup_2", "name": "4座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "SeatGroup_3", "name": "5座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "SeatGroup_4", "name": "6座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}, {"itemCode": "SeatGroup_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 5, "isQuickItem": true, "quickSortNum": 1, "positionCode": "13"}, {"itemCode": "SeatGroup_6", "name": "8座及以上", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 6, "isQuickItem": false}], "shortName": "座位", "isSupportMulti": true}, {"name": "车辆排挡", "sortNum": 3, "groupCode": "Transmission", "bitwiseType": 2, "filterItems": [{"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "10"}, {"itemCode": "Transmission_2", "name": "手动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}], "shortName": "排挡"}, {"name": "能源类型", "sortNum": 4, "groupCode": "NewEnergy", "bitwiseType": 2, "filterItems": [{"itemCode": "NewEnergy_elect", "name": "纯电动", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "NewEnergy_mix", "name": "新能源混动", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "NewEnergy_gas", "name": "汽油", "groupCode": "NewEnergy", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}], "shortName": "能源类型", "isSupportMulti": true}, {"name": "车辆配置", "sortNum": 5, "groupCode": "VehicleAccessory", "bitwiseType": 1, "filterItems": [{"itemCode": "VehicleAccessory_HangzhouLicense", "name": "浙A牌", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 8, "sortNum": 1, "isQuickItem": true, "quickSortNum": 5, "positionCode": "9"}, {"itemCode": "VehicleAccessory_ReversingImage", "name": "倒车影像", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 2, "isQuickItem": true, "quickSortNum": 6, "positionCode": "9"}, {"itemCode": "VehicleAccessory_radar", "name": "倒车雷达", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 32, "sortNum": 3, "isQuickItem": true, "quickSortNum": 7, "positionCode": "9"}, {"itemCode": "VehicleAccessory_tachograph", "name": "行车记录仪", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 64, "sortNum": 4, "isQuickItem": true, "quickSortNum": 8, "positionCode": "9"}, {"itemCode": "VehicleAccessory_LeatherSeat", "name": "真皮座椅", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 256, "sortNum": 6, "isQuickItem": false}], "shortName": "车辆配置", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 6, "groupCode": "HotBrand", "bitwiseType": 2, "filterItems": [{"itemCode": "HotBrand_奥迪", "name": "奥迪", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"itemCode": "HotBrand_别克", "name": "别克", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "HotBrand_奔驰", "name": "奔驰", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "HotBrand_宝马", "name": "宝马", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "HotB<PERSON>_本田", "name": "本田", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 2, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"itemCode": "HotBrand_大众", "name": "大众", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}, {"itemCode": "HotBrand_丰田", "name": "丰田", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 6, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "HotBrand_路虎", "name": "路虎", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 12, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"itemCode": "HotBrand_日产", "name": "日产", "groupCode": "HotBrand", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 18, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}], "shortName": "热门品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_a0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_a0_奥迪", "name": "奥迪", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aodi.png"}, {"itemCode": "BrandGroup_a0_爱驰", "name": "爱驰", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/aichi.png"}, {"itemCode": "BrandGroup_a0_阿斯顿·马丁", "name": "阿斯顿·马丁", "groupCode": "BrandGroup_a0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R419120009gwx9cqBA77.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_b0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_b0_保时捷", "name": "保时捷", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoshijie.png"}, {"itemCode": "BrandGroup_b0_别克", "name": "别克", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bieke.png"}, {"itemCode": "BrandGroup_b0_奔驰", "name": "奔驰", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/benchi.png"}, {"itemCode": "BrandGroup_b0_宝马", "name": "宝马", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/baoma.png"}, {"itemCode": "BrandGroup_b0_宾利", "name": "宾利", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/binli.png"}, {"itemCode": "BrandGroup_b0_本田", "name": "本田", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/bentian.png"}, {"itemCode": "BrandGroup_b0_标致", "name": "标致", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/biaozhi.png"}, {"itemCode": "BrandGroup_b0_比亚迪", "name": "比亚迪", "groupCode": "BrandGroup_b0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/biyadi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_d0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_d0_大众", "name": "大众", "groupCode": "BrandGroup_d0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/dazhong.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_f0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_f0_丰田", "name": "丰田", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fengtian.png"}, {"itemCode": "BrandGroup_f0_法拉利", "name": "法拉利", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/falali.png"}, {"itemCode": "BrandGroup_f0_福特", "name": "福特", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/fute.png"}, {"itemCode": "BrandGroup_f0_菲亚特", "name": "菲亚特", "groupCode": "BrandGroup_f0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/feiyate.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_g0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_g0_广汽传祺", "name": "广汽传祺", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqichuanqi.png"}, {"itemCode": "BrandGroup_g0_广汽新能源", "name": "广汽新能源", "groupCode": "BrandGroup_g0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/guangqixingnengyuan.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_h0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_h0_哈弗", "name": "哈弗", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/hafu.png"}, {"itemCode": "BrandGroup_h0_红旗", "name": "红旗", "groupCode": "BrandGroup_h0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/hongqi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_j0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_j0_吉利汽车", "name": "吉利汽车", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiliqiche.png"}, {"itemCode": "BrandGroup_j0_捷豹", "name": "捷豹", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jiebao.png"}, {"itemCode": "BrandGroup_j0_捷达", "name": "捷达", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//dimg04.c-ctrip.com/images/0AS6z1200087hzyigAC8A.png"}, {"itemCode": "BrandGroup_j0_金杯", "name": "金杯", "groupCode": "BrandGroup_j0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/jinbei.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_k0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_k0_凯迪拉克", "name": "凯迪拉克", "groupCode": "BrandGroup_k0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/kaidilake.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_l0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_l0_兰博基尼", "name": "兰博基尼", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lanbojini.png"}, {"itemCode": "BrandGroup_l0_力帆汽车", "name": "力帆汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lifanqiche.png"}, {"itemCode": "BrandGroup_l0_劳斯莱斯", "name": "劳斯莱斯", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/laosilaisi.png"}, {"itemCode": "BrandGroup_l0_林肯", "name": "林肯", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/linken.png"}, {"itemCode": "BrandGroup_l0_路虎", "name": "路虎", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/luhu.png"}, {"itemCode": "BrandGroup_l0_零跑汽车", "name": "零跑汽车", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingpaoqiche.png"}, {"itemCode": "BrandGroup_l0_雷克萨斯", "name": "雷克萨斯", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/leikesasi.png"}, {"itemCode": "BrandGroup_l0_雷诺", "name": "雷诺", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/leinuo.png"}, {"itemCode": "BrandGroup_l0_领克", "name": "领克", "groupCode": "BrandGroup_l0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/lingke.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_m0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_m0_MINI", "name": "MINI", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mini.png"}, {"itemCode": "BrandGroup_m0_名爵", "name": "名爵", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mingjue.png"}, {"itemCode": "BrandGroup_m0_玛莎拉蒂", "name": "玛莎拉蒂", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/mashaladi.png"}, {"itemCode": "BrandGroup_m0_迈凯伦", "name": "迈凯伦", "groupCode": "BrandGroup_m0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/maikailun.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_p0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_p0_Polestar极星", "name": "Polestar极星", "groupCode": "BrandGroup_p0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "https://dimg04.c-ctrip.com/images/0R43g120009gwuo9r0192.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_q0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_q0_奇瑞", "name": "奇瑞", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qirui.png"}, {"itemCode": "BrandGroup_q0_起亚", "name": "起亚", "groupCode": "BrandGroup_q0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/qiya.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_r0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_r0_日产", "name": "日产", "groupCode": "BrandGroup_r0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/richan.png"}, {"itemCode": "BrandGroup_r0_荣威", "name": "荣威", "groupCode": "BrandGroup_r0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/rongwei.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_s0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_s0_smart", "name": "smart", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/smart.png"}, {"itemCode": "BrandGroup_s0_三菱", "name": "三菱", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sanling.png"}, {"itemCode": "BrandGroup_s0_斯巴鲁", "name": "斯巴鲁", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sibalu.png"}, {"itemCode": "BrandGroup_s0_斯柯达", "name": "斯柯达", "groupCode": "BrandGroup_s0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/sikeda.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_t0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_t0_特斯拉", "name": "特斯拉", "groupCode": "BrandGroup_t0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/tesila.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_w0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_w0_五菱汽车", "name": "五菱汽车", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/wulingqiche.png"}, {"itemCode": "BrandGroup_w0_沃尔沃", "name": "沃尔沃", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/woerwo.png"}, {"itemCode": "BrandGroup_w0_蔚来", "name": "蔚来", "groupCode": "BrandGroup_w0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/weilai.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_x0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_x0_小鹏汽车", "name": "小鹏汽车", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiaopengqiche.png"}, {"itemCode": "BrandGroup_x0_现代", "name": "现代", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xiandai.png"}, {"itemCode": "BrandGroup_x0_雪佛兰", "name": "雪佛兰", "groupCode": "BrandGroup_x0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/xuefulan.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_y0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_y0_英菲尼迪", "name": "英菲尼迪", "groupCode": "BrandGroup_y0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false, "icon": "//pages.c-ctrip.com/carisd/brandlogo/yingfeinidi.png"}], "shortName": "全部品牌", "isSupportMulti": true}, {"name": "车辆品牌", "sortNum": 7, "groupCode": "BrandGroup_z0", "bitwiseType": 2, "filterItems": [{"itemCode": "BrandGroup_z0_合创", "name": "合创", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_z0_坦克", "name": "坦克", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_z0_奇瑞新能源", "name": "奇瑞新能源", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_z0_极氪", "name": "极氪", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_z0_理想汽车", "name": "理想汽车", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": false}, {"itemCode": "BrandGroup_z0_高合汽车", "name": "高合汽车", "groupCode": "BrandGroup_z0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 4, "isQuickItem": false}], "shortName": "全部品牌", "isSupportMulti": true}]}, {"name": "更多筛选", "code": "MoreC<PERSON>ose", "sortNum": 2, "hierarchy": 1, "filterGroups": [{"name": "价格", "sortNum": 1, "groupCode": "Price", "filterItems": [{"itemCode": "Price_0-50", "name": "¥50以下", "code": "0-50", "groupCode": "Price", "sortNum": 1}, {"itemCode": "Price_50-100", "name": "¥50-100", "code": "50-100", "groupCode": "Price", "sortNum": 2}, {"itemCode": "Price_100-200", "name": "¥100-200", "code": "100-200", "groupCode": "Price", "sortNum": 3}, {"itemCode": "Price_200-99999", "name": "¥200以上", "code": "200-99999", "groupCode": "Price", "sortNum": 4}], "shortName": "价格"}, {"name": "取车方式", "sortNum": 2, "groupCode": "PickReturn", "bitwiseType": 2, "filterItems": [{"itemCode": "PickReturn_PickupOnDoor", "name": "送车上门", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "1"}, {"itemCode": "PickReturn_FreeShuttle", "name": "免费接至门店取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "PickReturn_PickupSelf", "name": "自行到店取车", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 4, "isQuickItem": false}], "shortName": "取还方式", "isSupportMulti": true}, {"name": "门店服务", "sortNum": 3, "groupCode": "StoreService", "bitwiseType": 1, "filterItems": [{"itemCode": "StoreService_easyLife", "name": "无忧租", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 2, "positionCode": "3"}, {"itemCode": "StoreService_FreeDepositAllCtrip", "name": "押金双免", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 8, "sortNum": 4, "isQuickItem": true, "quickSortNum": 3, "positionCode": "3"}, {"itemCode": "StoreService_Unlimit", "name": "不限里程", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 16, "sortNum": 5, "isQuickItem": false}, {"itemCode": "StoreService_FreeCancel", "name": "免费取消", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 32, "sortNum": 6, "isQuickItem": false}], "shortName": "门店服务", "isSupportMulti": true}, {"name": "优惠活动", "sortNum": 4, "groupCode": "Promotion", "bitwiseType": 2, "filterItems": [{"itemCode": "Promotion_3641", "name": "", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 13, "icon": "https://dimg04.c-ctrip.com/images/0412c120009soj3dvADBB.png", "positionCode": "2"}, {"itemCode": "Promotion_3487", "name": "", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": true, "quickSortNum": 1, "icon": "https://dimg04.c-ctrip.com/images/0412h12000aakcx1k3040.png", "positionCode": "2", "selectedIcon": "https://dimg04.c-ctrip.com/images/0412h12000aakcx1k3040.png"}, {"itemCode": "Promotion_3766", "name": "新车特惠", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 4, "isQuickItem": false}], "shortName": "优惠活动", "isSupportMulti": true}, {"name": "取车证件", "sortNum": 5, "groupCode": "Ceritificate", "bitwiseType": 2, "filterItems": [{"itemCode": "Ceritificate_1", "name": "身份证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_2", "name": "护照", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_7", "name": "回乡证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}, {"itemCode": "Ceritificate_8", "name": "台胞证", "groupCode": "Ceritificate", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 3, "isQuickItem": false}], "shortName": "取车证件", "isSupportMulti": true}, {"name": "驾驶员驾龄", "sortNum": 6, "groupCode": "DriveAge", "bitwiseType": 2, "filterItems": [{"itemCode": "DriveAge_1", "name": "不满6个月", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": false}, {"itemCode": "DriveAge_2", "name": "满6个月", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "DriveAge_3", "name": "满一年", "groupCode": "DriveAge", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 3, "isQuickItem": false}], "shortName": "驾驶员驾龄"}, {"name": "门店评分", "sortNum": 7, "groupCode": "Comment", "bitwiseType": 2, "filterItems": [{"itemCode": "Comment_4.8", "name": "4.8分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "12"}, {"itemCode": "Comment_4.5", "name": "4.5分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": false}, {"itemCode": "Comment_4.0", "name": "4.0分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 3, "isQuickItem": false}], "shortName": "门店评分"}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_0", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_-1", "name": "携程优选", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": true, "quickSortNum": 1, "icon": "", "positionCode": "4"}, {"itemCode": "Vendor_9787", "name": "一嗨租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false, "mark": "hot"}, {"itemCode": "Vendor_13027", "name": "凹凸出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13033", "name": "枫叶租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13036", "name": "大方租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_13037", "name": "车速递租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30026", "name": "安致租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_30105", "name": "富君租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_31037", "name": "君祥租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_31204", "name": "嘉铭租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_31213", "name": "泓顺租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32219", "name": "熬烧租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32279", "name": "协途租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_32683", "name": "普达租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_33961", "name": "泰华租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_33996", "name": "大吽租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_34224", "name": "钺和租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_34303", "name": "小维租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_47275", "name": "随变租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_48567", "name": "杭州爱尚租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_52951", "name": "路路顺租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_53653", "name": "皇宏租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_57574", "name": "都达租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_63167", "name": "冠通租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_64270", "name": "驾道出行", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_64766", "name": "立乘租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_64997", "name": "空港租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_65158", "name": "咖斯黛尔租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_66107", "name": "沙玛租车", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_1", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_66340", "name": "良芯租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_66479", "name": "捷约租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_67058", "name": "广丰出行", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69655", "name": "百鸿出行", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_69767", "name": "莫干云游出行", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70086", "name": "浙江羽图出行", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_70408", "name": "杭州爱旅租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_71925", "name": "亿顺租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_72527", "name": "麻豆智行租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_72931", "name": "百艺租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_73085", "name": "明灿租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_75831", "name": "浙江叮咚租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_76669", "name": "包租丫租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_76919", "name": "玲珑租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_77079", "name": "炫速租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_77743", "name": "Bee豪车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79323", "name": "龙卷风租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79643", "name": "世爵租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_79841", "name": "睿行租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80015", "name": "尚品租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80041", "name": "韵速租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80043", "name": "迎丰租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80139", "name": "美曦租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80347", "name": "捷承租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80673", "name": "云达租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80679", "name": "飓豪租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80871", "name": "杭兴租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_80885", "name": "山东超然租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81009", "name": "伙伴租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81119", "name": "漫游纪租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81223", "name": "汉雄租车", "groupCode": "Vendor_1", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_2", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_81755", "name": "悦享租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_81809", "name": "博睿租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82147", "name": "乐童租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82239", "name": "极速出行", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82335", "name": "亦博租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82499", "name": "聚速租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82533", "name": "启优租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82553", "name": "超凡租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82727", "name": "杭州明路租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_82913", "name": "艺佳租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 512, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83071", "name": "藤原租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1024, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83099", "name": "鼎逸租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2048, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83514", "name": "尊通租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4096, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83598", "name": "浙江漫自由租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8192, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83786", "name": "至博租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16384, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_83902", "name": "芯隆租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 32768, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84024", "name": "杭州小马租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 65536, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84056", "name": "星熠租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 131072, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84137", "name": "麦丰租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 262144, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84181", "name": "昊神租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 524288, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84477", "name": "杭州一哥租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1048576, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84625", "name": "森禹租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 2097152, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84637", "name": "飓行租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 4194304, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_84755", "name": "金娴租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 8388608, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85021", "name": "欧速租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 16777216, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85105", "name": "汉堡租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 33554432, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85165", "name": "车豪租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 67108864, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85189", "name": "岂彧出行", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 134217728, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85207", "name": "昕奥出行", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 268435456, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85239", "name": "梦影租车", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 536870912, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85401", "name": "涵宇出行", "groupCode": "Vendor_2", "bitwiseType": 2, "binaryDigit": 1073741824, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}, {"name": "租车公司", "sortNum": 8, "groupCode": "Vendor_3", "bitwiseType": 2, "filterItems": [{"itemCode": "Vendor_15000096", "name": "佳丰租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 32, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000123", "name": "耀出行租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 64, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000154", "name": "极氪订阅出行", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 128, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_15000250", "name": "穿驰出行", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 256, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85501", "name": "安旅出行", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85761", "name": "同驰出行", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85815", "name": "广汽传祺出行", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85841", "name": "旭翔租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 8, "sortNum": 8, "isQuickItem": false}, {"itemCode": "Vendor_85861", "name": "华谊惠租车", "groupCode": "Vendor_3", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 8, "isQuickItem": false}], "shortName": "租车公司", "isSupportMulti": true}]}], "quickFilter": [{"itemCode": "PickReturn_PickupOnDoor", "name": "送车上门", "groupCode": "PickReturn", "bitwiseType": 2, "binaryDigit": 2, "sortNum": 2, "isQuickItem": true, "quickSortNum": 2, "positionCode": "1"}, {"itemCode": "Promotion_3487", "name": "", "groupCode": "Promotion", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 4, "isQuickItem": true, "quickSortNum": 1, "icon": "https://dimg04.c-ctrip.com/images/0413c12000aakcqdo5E50.png", "positionCode": "2", "selectedIcon": "https://dimg04.c-ctrip.com/images/0413c12000aakcqdo5E50.png"}, {"itemCode": "StoreService_easyLife", "name": "无忧租", "groupCode": "StoreService", "bitwiseType": 1, "binaryDigit": 4, "sortNum": 3, "isQuickItem": true, "quickSortNum": 2, "positionCode": "3"}, {"itemCode": "Vendor_-1", "name": "携程优选", "groupCode": "Vendor_0", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 7, "isQuickItem": true, "quickSortNum": 1, "icon": "", "positionCode": "4"}, {"itemCode": "CarAge_3509", "name": "半年内车龄", "groupCode": "CarAge", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "8"}, {"itemCode": "VehicleAccessory_HangzhouLicense", "name": "浙A牌", "groupCode": "VehicleAccessory", "bitwiseType": 1, "binaryDigit": 8, "sortNum": 1, "isQuickItem": true, "quickSortNum": 5, "positionCode": "9"}, {"itemCode": "Transmission_1", "name": "自动挡", "groupCode": "Transmission", "bitwiseType": 2, "binaryDigit": 1, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "10"}, {"itemCode": "Comment_4.8", "name": "4.8分以上", "groupCode": "Comment", "bitwiseType": 2, "binaryDigit": 4, "sortNum": 1, "isQuickItem": true, "quickSortNum": 1, "positionCode": "12"}, {"itemCode": "SeatGroup_5", "name": "7座", "groupCode": "SeatGroup", "bitwiseType": 2, "binaryDigit": 16, "sortNum": 5, "isQuickItem": true, "quickSortNum": 1, "positionCode": "13"}], "vehicleList": [{"brandEName": "大众", "brandName": "大众", "name": "大众宝来", "zhName": "大众宝来", "vehicleCode": "4067", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5L", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0qcn143fagmgoxd04D4.jpg"], "isSpecialized": true, "isHot": false, "license": "浙A牌", "licenseStyle": "2", "realityImageUrl": "https://dimg04.c-ctrip.com//images/04156120008at71ga4625.jpg", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "vr": ""}, {"brandEName": "大众", "brandName": "大众", "name": "大众宝来", "zhName": "大众宝来", "vehicleCode": "4067", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 1, "displacement": "1.5L", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0qcn143fagmgoxd04D4.jpg"], "isSpecialized": true, "isHot": false, "license": "外牌", "licenseStyle": "6", "realityImageUrl": "https://dimg04.c-ctrip.com//images/04156120008at71ga4625.jpg", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "vr": ""}, {"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "4139", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "luggageNo": 2, "displacement": "1.0T-1.5L", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0scn143fai2ah4w9B69.jpg"], "isSpecialized": true, "isHot": false, "license": "外牌", "licenseStyle": "6", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vr": ""}, {"brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "zhName": "雪佛兰科沃兹", "vehicleCode": "4139", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "luggageNo": 2, "displacement": "1.0T-1.5L", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz0scn143fai2ah4w9B69.jpg"], "isSpecialized": true, "isHot": false, "license": "浙A牌", "licenseStyle": "2", "oilType": 3, "luggageNum": "可放2个24寸行李箱", "autoPark": false, "carPhone": true, "autoStart": false, "autoBackUp": false, "vr": ""}, {"brandEName": "别克", "brandName": "别克", "name": "别克英朗", "zhName": "别克英朗", "vehicleCode": "834", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "luggageNo": 1, "displacement": "1.0T-1.5L", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz08cn143fajgdgjyA066.jpg"], "isSpecialized": true, "isHot": false, "license": "外牌", "licenseStyle": "6", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoBackUp": false, "vr": ""}, {"brandEName": "别克", "brandName": "别克", "name": "别克英朗", "zhName": "别克英朗", "vehicleCode": "834", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济轿车", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "luggageNo": 1, "displacement": "1.0T-1.5L", "style": "", "imageList": ["https://dimg04.c-ctrip.com/images/98/carisd_newcarimage/image/uz08cn143fajgdgjyA066.jpg"], "isSpecialized": true, "isHot": false, "license": "浙A牌", "licenseStyle": "2", "oilType": 3, "luggageNum": "可放1个24寸行李箱", "autoPark": false, "autoBackUp": false, "vr": ""}], "productGroups": [{"groupCode": "all", "groupName": "全部车型", "sortNum": -4, "productList": [{"vehicleCode": "4067", "sortNum": 1, "lowestPrice": 51, "highestPrice": 285, "maximumRating": 5, "maximumCommentCount": 222, "lowestDistance": 22.5625, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3350", "vendorCode": "83902", "pStoreCode": "106607", "rStoreCode": "106607", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "63ac59ed3e499c44ac395ffd", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDLwESTycy5iYxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/CJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThkZROc17UIqdrT2I/YFHxgRj4CrOI1G42Q7BZE1vcIG4tve49xdKAs9Bh0CtCxf/YpZpEoTnxeD6F8p1rnyUbKiX4u/eBFfWNaA3FdkEq28REbV3nTauX0QgX6/K0W2IgmQ79sP5WiTHBzC0NdlQOxxFQP+5aEs5JtS63iTdzmZNqp6S2UR5Gf/iU9AVsXdJ7mVShtdZkZv3lDCKs+H8CnQ==", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 102, "amountStr": "¥102", "subAmount": 51, "subAmountStr": "日均¥51", "originalDailyPrice": 66, "detail": [{"code": "1001", "name": "租车费", "amount": 132, "amountDesc": "¥132"}, {"code": "3766", "name": "新车特惠", "amount": 30, "amountDesc": "¥30"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 217, "amountStr": "¥217", "subAmount": 247, "subAmountStr": "¥247", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4355", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4589", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD3013", "vehicleCode": "0", "packageType": 0}}, {"reference": {"bizVendorCode": "SD4941", "vehicleCode": "0", "packageType": 0}}], "reactId": "2259586700", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3350_0_106607_106607"]}, "minTPrice": 217, "minDPrice": 51, "modifySameVehicle": false, "minDOrinPrice": 66, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "新车特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3766", "groupCode": "MarketGroup1359", "amountTitle": "已减30", "groupId": 1, "mergeId": 0}, "priceSize": 5, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "浙A牌", "licenseStyle": "2", "licenseTag": "浙A牌"}}, {"vehicleCode": "4067", "sortNum": 1, "lowestPrice": 68, "highestPrice": 678, "maximumRating": 5, "maximumCommentCount": 3905, "lowestDistance": 1.6783, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD6445", "vendorCode": "63167", "pStoreCode": "116281", "rStoreCode": "116281", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "63ac59ed3e499c44ac395f1f", "priceVersion": "AV0B7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDLwESTycy5iYxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/CJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThf0egfmKwMw/slGNCRnWxpkYr0UA14RpGL64AGHcSoZ2F2SPhmVwUZjYlQlnelHKFpZpEoTnxeD4/a6A38g/swCXsYnPabo32gmTzkg7piu0do/YXKGp3O0Yr0UA14RpG4ahcyBEAzIsoWgX+u0SHxiXhyIqn1eR4pQ7fuev/+jU8EKac/LeUy5Mo2M2C4+uNojv8fX8gmVSe013sbBtl9Q==", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 206, "amountStr": "¥206", "subAmount": 103, "subAmountStr": "日均¥103", "detail": [{"code": "1001", "name": "租车费", "amount": 206, "amountDesc": "¥206"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 321, "amountStr": "¥321", "subAmount": 321, "subAmountStr": "¥321", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD6990", "vehicleCode": "0", "packageType": 0}}, {"reference": {"bizVendorCode": "SD4111", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4605", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD5029", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4354", "vehicleCode": "0", "packageType": 1}}], "reactId": "2259586701", "group": 0, "groupSort": 0, "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4111_0_107085_107085"]}, "minTPrice": 321, "minDPrice": 103, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 6, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "外牌", "licenseStyle": "6", "licenseTag": ""}}, {"vehicleCode": "4139", "sortNum": 3, "lowestPrice": 59, "highestPrice": 527, "maximumRating": 5, "maximumCommentCount": 243, "lowestDistance": 18.5978, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4067", "vendorCode": "79323", "pStoreCode": "106916", "rStoreCode": "106916", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "63ac59ed3e499c44ac395971", "priceVersion": "AVsB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDz0wFHwmfn6oxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2McdjIuJc0b/aWPVGfDQaOcxPsJyD7vyOMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/doCVJRs5rVuqOaBD74/86GBW+jTpAfwnEOF2SPhmVwUZjkP0MIAGW7+N7gUtHiwtELp7u3xEUvht2qX95ytHOvo+stpzj5Mgd1M74u/v9GaOqlQ432MJXtyZ/dbM+tK/AWHORFPZ+cN9yYrDcq1PDL3Ly8p67UobGUpJoVh0Z0f6uNiyTjyVk7ZC9E6ozoz0nHQ8skUfduJ6Q==", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 118, "amountStr": "¥118", "subAmount": 59, "subAmountStr": "日均¥59", "detail": [{"code": "1001", "name": "租车费", "amount": 118, "amountDesc": "¥118"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "name": "基础服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 253, "amountStr": "¥253", "subAmount": 253, "subAmountStr": "¥253", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD6445", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4605", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4111", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD3013", "vehicleCode": "0", "packageType": 0}}, {"reference": {"bizVendorCode": "SD4251", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD3387", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD5029", "vehicleCode": "0", "packageType": 0}}, {"reference": {"bizVendorCode": "SD6723", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4354", "vehicleCode": "0", "packageType": 1}}], "reactId": "2259586702", "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4067_0_106916_106916"]}, "minTPrice": 253, "minDPrice": 59, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 10, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "外牌", "licenseStyle": "6", "licenseTag": ""}}, {"vehicleCode": "4139", "sortNum": 3, "lowestPrice": 79, "highestPrice": 230, "maximumRating": 5, "maximumCommentCount": 1423, "lowestDistance": 14.7219, "vendorPriceList": [{"reference": {"bizVendorCode": "SD3325", "vehicleCode": "0", "packageType": 1}}, {"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3197", "vendorCode": "83598", "pStoreCode": "106212", "rStoreCode": "106212", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "63ac59ed3e499c44ac396009", "priceVersion": "AWAB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXDv9YRZGhKVmIxHCBN97EijoccC+BNp4ZGFV2OPE9VlDg10AIrAPEewDZub8tL8naz92kZ1scww2Csq2PzvOWjqaWPVGfDQaOcxPsJyD7vyOMChngqzzICVrrYkqMBZKtWhxwL4E2nhkWOKdCVQBx4BdVQee/Ugv394fNp5Q6tmEi53cKzF+eI+tOa1WRI+/do3aBNXMwu04IViTVf+KyZDEO1BAvbvo7vRrGj9xfIHEyZItSXb6un21sUaQ936ig1DQfPbwnHXoGYKhQetELiMMCe5LaKnV16MpGT6AEIpUTs7sx5Ik0exdV9UoRZvgXZwIwUFOjIW1siuZ5UTuS2rdVQee/Ugv3/n5EY9vHpLumS+2STaHdbUeWWrrNNAqQ+jdpCkpz8hpA==", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 238, "amountStr": "¥238", "subAmount": 119, "subAmountStr": "日均¥119", "originalDailyPrice": 134, "detail": [{"code": "1001", "name": "租车费", "amount": 268, "amountDesc": "¥268"}, {"code": "3487", "name": "元旦特惠", "amount": 30, "amountDesc": "¥30"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 25, "amountStr": "¥25", "detail": [{"code": "1003", "name": "车行手续费", "amount": 25, "amountDesc": "¥25", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "name": "基础服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 363, "amountStr": "¥363", "subAmount": 393, "subAmountStr": "¥393", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD5081", "vehicleCode": "0", "packageType": 0}}, {"reference": {"bizVendorCode": "SD3609", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4355", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4193", "vehicleCode": "0", "packageType": 1}}], "reactId": "2259586703", "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD5081_0_115328_115328"]}, "minTPrice": 363, "minDPrice": 119, "modifySameVehicle": false, "minDOrinPrice": 134, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "pTag": {"title": "元旦特惠", "category": 3, "type": 3, "code": "30", "sortNum": 10000, "colorCode": "15", "labelCode": "3487", "groupCode": "MarketGroup102", "amountTitle": "已减30", "groupId": 1, "mergeId": 0}, "priceSize": 6, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "浙A牌", "licenseStyle": "2", "licenseTag": "浙A牌"}}, {"vehicleCode": "834", "sortNum": 5, "lowestPrice": 66, "highestPrice": 240, "maximumRating": 5, "maximumCommentCount": 148, "lowestDistance": 18.5978, "vendorPriceList": [{"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD4067", "vendorCode": "79323", "pStoreCode": "106916", "rStoreCode": "106916", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "63ac59ed3e499c44ac39596e", "priceVersion": "AVoB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAO8L7FmRHCvbzvUZ2EqjhHWBoQN/MtsD3oqpDSw5xzJQHO2Upf/0pRA4Snsl5wt3lV8RrJfIBqGqUUa6/nBNx5ZaZoiE9Dv5NdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPa7q60R3XNCY5Ia0TcOZcDWlIYwWs89QVS9Kox4fmbThEh3qML5+OVVb8NAin1RNklp4rxRceG2LpQKHSWmaNHVQdTVBe/z8CZrN5js8ZTqp6leq55zxkzGoin7PETlbld8pNsBNrK7X/I5tmzCjokrakUrkySh5kw2RQsrqoFHK33zlwu8WNB+lD6gOOR8DP5EvoZ9Ehk/Bem3AF7czA2ltFO+LPA1x/wK2rx95bcUC21Df2gjzQL92RAQFK/x3zA==", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 132, "amountStr": "¥132", "subAmount": 66, "subAmountStr": "日均¥66", "detail": [{"code": "1001", "name": "租车费", "amount": 132, "amountDesc": "¥132"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 35, "amountStr": "¥35", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 100, "amountStr": "¥100", "detail": [{"code": "1002", "name": "基础服务费", "amount": 100, "amountDesc": "¥100", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 267, "amountStr": "¥267", "subAmount": 267, "subAmountStr": "¥267", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD6445", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4002", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD3013", "vehicleCode": "0", "packageType": 0}}, {"reference": {"bizVendorCode": "SD4251", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD6723", "vehicleCode": "0", "packageType": 0}}], "reactId": "2259586704", "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0.4153904, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD4067_0_106916_106916"]}, "minTPrice": 267, "minDPrice": 66, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 6, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "外牌", "licenseStyle": "6", "licenseTag": ""}}, {"vehicleCode": "834", "sortNum": 5, "lowestPrice": 76, "highestPrice": 228, "maximumRating": 5, "maximumCommentCount": 1423, "lowestDistance": 18.6575, "vendorPriceList": [{"reference": {"bizVendorCode": "SD3197", "vehicleCode": "0", "packageType": 1}}, {"isMinTPriceVendor": true, "reference": {"bizVendorCode": "SD3325", "vendorCode": "84137", "pStoreCode": "106469", "rStoreCode": "106469", "vehicleCode": "0", "packageId": "", "packageType": 1, "comPriceCode": "63ac59ed3e499c44ac395e3a", "priceVersion": "AVwB7Xm8oQb+Y6ACjzbD6rx3qbrCkMWtm1YO5WoI/pSdd7U/cjArUssFoK/fDYP2vO9VYDau4juWTXAsJ+v9Mh3TpYxHCBN97EijoccC+BNp4ZHH2bbXgn0mDg10AIrAPEewatRiX/q57+H92kZ1scww2McdjIuJc0b/CJO9j9gLhrxdTVaIb0KVV+Fogl/98O/BKXGUfJlT9T5YGhA38y2wPckiLoep94jFAYyJThhD/sx4fNp5Q6tmEi53cKzF+eI+tOa1WRI+/drmmP8UkVW5iUYr0UA14RpGh91lV3/EEmaF2SPhmVwUZjC/4FK5qzqGN7gUtHiwtEIt2x4mnYNpMWqX95ytHOvo8AtNvKbJF4EomfwiPv572kO6LXuLW3QuZ/dbM+tK/AWHORFPZ+cN9yPaVlZdzE6rtS63iTdzmZNY2kaLljXI9J1zRVVyiXlimVShtdZkZv05WuPHvNNLww==", "sendTypeForPickUpCar": 0, "sendTypeForPickOffCar": 0}, "fees": [{"code": "CAR_RENTAL_FEE", "name": "车辆租金", "amount": 152, "amountStr": "¥152", "subAmount": 76, "subAmountStr": "日均¥76", "detail": [{"code": "1001", "name": "租车费", "amount": 152, "amountDesc": "¥152"}]}, {"code": "CAR_SERVICE_FEE", "name": "服务/手续费", "amount": 115, "amountStr": "¥115", "detail": [{"code": "1003", "name": "车行手续费", "amount": 35, "amountDesc": "¥35", "desc": "用于车辆清洁，车辆保养，单据制作，人员服务等", "showFree": false}, {"code": "11026", "name": "送车上门服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}, {"code": "11027", "name": "上门取车服务费", "amount": 40, "amountDesc": "¥40", "showFree": false}], "currencyCode": "¥"}, {"code": "CAR_GUARANTEE_FEE", "name": "车行保障服务费", "amount": 80, "amountStr": "¥80", "detail": [{"code": "1002", "name": "基础服务费", "amount": 80, "amountDesc": "¥80", "showFree": false}], "currencyCode": "¥"}, {"code": "10001", "name": "总价", "amount": 347, "amountStr": "¥347", "subAmount": 347, "subAmountStr": "¥347", "currencyCode": "¥"}]}, {"reference": {"bizVendorCode": "SD4864", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4985", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4712", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD3609", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4475", "vehicleCode": "0", "packageType": 1}}, {"reference": {"bizVendorCode": "SD4616", "vehicleCode": "0", "packageType": 0}}], "reactId": "2259586705", "scoreSort": 0, "hot": 0, "hotType": 0, "hotScore": 0.4153904, "vehicleRecommendProduct": {"introduce": "当前车型最低价", "productCodes": ["SD3325_0_106469_106469"]}, "minTPrice": 347, "minDPrice": 76, "modifySameVehicle": false, "minDOrinPrice": 0, "outTags": [{"title": "免押金", "category": 2, "type": 1, "code": "7", "sortNum": 10, "colorCode": "2", "labelCode": "3746", "groupCode": "MarketGroup1347", "groupId": 3, "mergeId": 0}], "priceSize": 8, "isEasy": true, "isCredit": true, "rCoup": 0, "pWay": "可选:店员免费上门送取车", "productRef": {"license": "浙A牌", "licenseStyle": "2", "licenseTag": "浙A牌"}}], "dailyPrice": 51, "hasResult": true}, {"groupCode": "2", "groupName": "经济轿车", "sortNum": 0, "dailyPrice": 51, "hasResult": true}, {"groupCode": "newenergy", "groupName": "新能源", "sortNum": 2, "dailyPrice": 88, "hasResult": true}, {"groupCode": "3", "groupName": "舒适轿车", "sortNum": 3, "dailyPrice": 172, "hasResult": true}, {"groupCode": "6", "groupName": "SUV", "sortNum": 4, "dailyPrice": 114, "hasResult": true}, {"groupCode": "4", "groupName": "商务车", "sortNum": 5, "dailyPrice": 111, "hasResult": true}, {"groupCode": "5", "groupName": "豪华轿车", "sortNum": 6, "dailyPrice": 202, "hasResult": true}, {"groupCode": "9", "groupName": "跑车", "sortNum": 7, "dailyPrice": 318, "hasResult": true}, {"groupCode": "7", "groupName": "小巴士", "sortNum": 9, "dailyPrice": 94, "hasResult": true}], "productGroupsHashCode": "BW07jBd0djE6E579i581", "storeList": [{"storeCode": "106916", "pickUpLevel": 26297, "pickOffLevel": 26297}, {"storeCode": "106469", "pickUpLevel": 108399, "pickOffLevel": 108399}, {"storeCode": "106212", "pickUpLevel": 17860, "pickOffLevel": 17860}, {"storeCode": "106607", "pickUpLevel": 19213, "pickOffLevel": 19213}, {"storeCode": "116281", "pickUpLevel": 58047, "pickOffLevel": 58047}], "commNotices": [], "rentCenter": {"filterCode": "Vendor_0"}, "promptInfos": [{"contents": [{"contentStyle": "0", "stringObjs": [{"content": "您租车可享受“铂金用户租车费92折起”专属优惠~", "style": "0"}]}], "icon": "https://pages.c-ctrip.com/commerce/promote/car/app/images/1112.png"}, {"contents": [{"contentStyle": "1", "stringObjs": []}, {"stringObjs": []}], "type": 11, "icon": "", "locations": [{"groupCode": "all", "index": 1}], "backGroundUrl": "https://dimg04.c-ctrip.com/images/0306u12000aaslea7EA1A.jpg", "textColor": {"r": 255, "g": 255, "b": 255, "a": 1}, "tangChoiceTypes": "[]", "jumpUrl": "https://m.ctrip.com/tangram/MzAyNzc=?ctm_ref=vactang_page_30277&amp;isHideNavBar=YES&amp;apppgid=10650039393"}], "easyLifeInfo": {"isEasyLife": true, "tagList": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "安心保障", "titleExtra": "(需加购尊享服务)", "type": 1, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 2, "subTitle": "*覆盖损失范围以预订页面内披露为准", "showLayer": 0}, {"title": "优质车况", "type": 1, "description": "3年内车龄", "sortNum": 3, "subTitle": "", "showLayer": 0}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满（不含纯电车）", "sortNum": 99, "subTitle": ""}]}, "basicData": {"sortItems": [{"title": "推荐排序", "type": 1, "code": "1", "sortNum": 1}, {"title": "总价 低→高", "type": 2, "code": "2", "sortNum": 2}]}, "designatedVehicleIntroduce": {"title": "指定车型", "description": "您选择的产品为“指定车型”，取车时门店承诺为您提供当前下单的车型{0}（指定范围不含车况、年款和颜色）"}, "labelCodes": ["3650", "3563", "3494", "3548", "3504", "3705", "3547", "3503", "3501", "3709", "3510", "3487", "3696", "3641", "3653", "3731", "3509", "3788", "3810", "3766", "3789", "3679", "3746", "3757"], "isAll": false, "includeFees": {"tFees": ["租车基本费用", "基础服务费", "车行手续费等"], "dFees": ["租车基本费用"], "desc": "* 具体费用明细可在选择门店后查看"}, "isLastPage": false, "gs": {"gsTypes": [{"id": 1, "title": "无忧租 安心首选", "sTitle": "9大无忧特权", "hint": "优选门店无忧租超值价"}, {"id": 2, "title": "无忧租 安心首选", "sTitle": "9大无忧特权", "hint": "无忧租超值价"}, {"id": 3, "title": "全国连锁 服务放心", "sTitle": "", "hint": "一嗨租车超值价"}, {"id": 4, "title": "上门送取车 取还超便捷", "sTitle": "", "hint": "送车上门超值价"}, {"id": 5, "title": "信用租 押金双免", "sTitle": "", "hint": "押金双免超值价"}, {"id": 6, "title": "新车保障 车况佳", "sTitle": "", "hint": "新车超值价"}, {"id": 7, "title": "超值特价 高性价比", "sTitle": "", "hint": "超值特价"}], "expLabs": ["信用双免", "优质车况", "油量保障", "在线认证"], "mCount": 3}, "hasResultWithoutFilter": true, "isFromSearch": false, "productGroupCodeUesd": "all", "uniqSign": "12001091210266356615Eq8Ig295o8MR0O11QOe5", "feeMap": [{"code": "1002", "subName": "（含车损、第三者责任等保障）"}, {"code": "1003", "subName": "（含车辆清洁、单据打印等）"}], "pHub": 0, "rHub": 0, "promotMap": {}, "extras": {"serverRequestId": "12JZI97T6l0WS41XW85l", "abVersion": "220323_DSJT_rank2|B,220624_DSJT_spfj1|B,221207_DSJT_cxvr|A,221118_DSJT_cxtjx|B"}, "isRecommend": false, "isKlbData": true}