{"baseResponse": {"code": "200", "returnMsg": "", "isSuccess": true}, "alipayAuthInfo": {"texts": [{"title": "芝麻分650分·", "type": 1, "code": ""}, {"title": "享免押金租车", "type": 1, "code": "{\"color\": \"#333333\",\"fontFamily\": \"PingFangSC-Semibold\",\"fontWeight\": \"700\"}"}], "authStatusName": "立即验证", "ctripSatisfy": false, "current": "CNY", "amount": 500000, "defaultInfo": {"title": "抱歉，验证未通过", "button": {"title": "知道了"}, "contents": [{"stringObjs": [{"content": "可能原因：", "style": "3"}]}, {"stringObjs": [{"content": "1.芝麻信用未达650分", "style": "3"}]}, {"stringObjs": [{"content": "2.未通过支付宝安全认证", "style": "3"}]}, {"stringObjs": [{"content": "详情请咨询支付宝客服", "style": "7"}]}]}, "authedCountEqOne": false, "isSupportZhima": true, "requestId": "osd_zm_20051907114841810ri", "promptInfo": {"title": "抱歉，验证未通过", "button": {"title": "知道了"}, "contents": [{"stringObjs": [{"content": "可能原因：", "style": "3"}]}, {"stringObjs": [{"content": "1.芝麻信用未达650分", "style": "3"}]}, {"stringObjs": [{"content": "2.未通过支付宝安全认证", "style": "3"}]}, {"stringObjs": [{"content": "详情请咨询支付宝客服", "style": "7"}]}]}, "orderId": "3177953026", "authStatus": 3, "authInfos": [{"title": "", "contents": [{"contentStyle": "1", "stringObjs": [{"content": "芝麻分满650", "style": "1"}, {"content": "有机会享免押金租车", "style": "1"}]}], "type": 1, "button": {"title": "立即验证"}}]}, "ResponseStatus": {"Extension": [{"Value": "6566729990950205873", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a023047-441631-18486", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1589872308422+0800)/"}, "appResponseMap": {"networkCost": 332}}