{"alipayAuthInfo": {"authCount": 0, "authInfos": [{"button": {"title": "立即验证"}, "contents": [{"contentStyle": "1", "stringObjs": [{"content": "芝麻分满650", "style": "1"}, {"content": "有机会享免押金租车", "style": "2"}]}], "title": "", "type": 1}], "authOrderCount": 0, "authStatus": 0, "authUrl": "", "authedCountEqOne": false, "defaultInfo": {"button": {"title": "知道了"}, "contents": [{"stringObjs": [{"content": "可能原因：", "style": "3"}]}, {"stringObjs": [{"content": "1.芝麻信用未达650分", "style": "3"}]}, {"stringObjs": [{"content": "2.未通过支付宝安全认证", "style": "3"}]}, {"stringObjs": [{"content": "详情请咨询支付宝客服", "style": "7"}]}], "title": "抱歉，验证未通过"}, "idNo": "", "orderId": "12988053426", "promptInfo": {"button": {"title": "立即认证"}, "contents": [{"stringObjs": [{"content": "芝麻信用满650分", "style": "1"}]}, {"stringObjs": [{"content": "享免押金租车", "style": "2"}]}], "title": ""}, "requestId": "alipay200806025654991uuh6", "userName": ""}, "baseResponse": {"isSuccess": false}, "responseStatus": {"ack": "Success", "errors": [], "extension": [{"id": "CLOGGING_TRACE_ID", "value": "8484612565890017861"}, {"id": "RootMessageId", "value": "921822-0a3d94aa-443522-6597566"}], "timestamp": "2020-08-06 10:56:54"}}