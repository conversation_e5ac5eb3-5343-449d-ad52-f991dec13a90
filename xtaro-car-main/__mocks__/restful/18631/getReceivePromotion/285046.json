{"baseResponse": {"extMap": {}, "errorCode": "0", "message": "操作成功", "isSuccess": true, "showMessage": "操作成功"}, "title": "新客特惠", "promotions": [{"shortDesc": "租车费满100可用", "status": 94, "limitText": "", "deductionList": [], "valueType": 0, "isActive": true, "briefName": "立享7.5折", "promotionSecretId": "af30992f653b86840a24c3447f4a0db160668721f7d0746aa640ae7b0d680f72025956be70a2b346ab1aacc1886f14841580cccbfa4a17d625e07e74cfce86dc", "name": "国内租车满减券", "validPeriodDesc": "有效期至 2021-05-29", "cornerText": "", "currencyCode": "¥", "couponCode": "", "invaildReason": "", "discountText": "折", "value": "70", "scenes": 1, "statusName": "生日月可领", "promotionId": 976501635, "longDesc": "本优惠券通过携程租车频道，以预付方式（在线支付）预订“枫叶租车”产品，可享受租车费7.5折优惠；\\\\优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\ 优惠券自领取日起后7天内使用有效，过期则自动失效；\\\\ 优惠券限预订取、还车时间均在2021年12月20日（含）前的订单可用；\\\\优惠券可与带有“超级品牌周”标签的产品优惠叠加适用，不可与其它优惠券与优惠活动同享；\\\\优惠券可用城市：深圳，成都，上海，杭州，北京，重庆，南京，武汉，昆明，贵阳，长沙，广州，三亚，合肥，厦门，西安，宁波，郑州，青岛，太原，南宁，珠海，沈阳，乌鲁木齐，伊宁；具体以列表页实际展示为准；\\\\ 每个订单限用一张优惠券，每张优惠券限用一次；领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；"}, {"shortDesc": "枫叶租车产品专享", "status": 1, "limitText": "", "deductionList": [], "valueType": 0, "isActive": true, "briefName": "立享7.5折", "promotionSecretId": "5fb45816116e602c793779a8b790f5ac324192dbf3a5dcabbac9d05af894a36466d465e617295a7645f70f0a2c5e109b", "name": "国内租车7.5折立减券", "validPeriodDesc": "领取后7天内有效", "cornerText": "", "currencyCode": "", "couponCode": "", "invaildReason": "", "discountText": "折", "value": "7.5", "scenes": 1, "statusName": "领取", "promotionId": 976501637, "longDesc": "本优惠券通过携程租车频道，以预付方式（在线支付）预订“枫叶租车”产品，可享受租车费7.5折优惠；\\\\优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用；\\\\ 优惠券自领取日起后7天内使用有效，过期则自动失效；\\\\ 优惠券限预订取、还车时间均在2021年12月20日（含）前的订单可用；\\\\优惠券可与带有“超级品牌周”标签的产品优惠叠加适用，不可与其它优惠券与优惠活动同享；\\\\优惠券可用城市：深圳，成都，上海，杭州，北京，重庆，南京，武汉，昆明，贵阳，长沙，广州，三亚，合肥，厦门，西安，宁波，郑州，青岛，太原，南宁，珠海，沈阳，乌鲁木齐，伊宁；具体以列表页实际展示为准；\\\\ 每个订单限用一张优惠券，每张优惠券限用一次；领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；"}], "ResponseStatus": {"Extension": [{"Value": "4048014632245765939", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a0eff68-455216-3950535", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1638779459632+0800)/"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 792, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 792, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1638779459546, "afterFetch": 1638779460338}}