{"baseResponse": {"extMap": {}, "errorCode": "0", "message": "操作成功", "isSuccess": true, "showMessage": "操作成功"}, "title": "每日好券", "promotions": [{"shortDesc": "国内阶梯满减", "status": 2, "limitText": "最高减", "meetCouponStartDate": true, "deductionList": ["满¥100减¥20", "满¥200减¥40"], "valueType": 0, "isActive": true, "briefName": "最高减¥40", "promotionSecretId": "", "name": "国内阶梯满减满减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "¥", "couponCode": "zwgjgxuzye", "invaildReason": "", "discountText": "", "value": "40", "scenes": 1, "statusName": "已领取", "promotionId": 667234322, "longDesc": "国内阶梯满减"}, {"shortDesc": "国内无门槛立减50", "status": 1, "limitText": "", "meetCouponStartDate": true, "deductionList": [], "valueType": 0, "isActive": true, "briefName": "立减¥50", "promotionSecretId": "c7292efccb8be2ca7803fd028b820334df0987b5f1e469f6fb19847f28cbe6b0be0f997295bb62407155da4d797ab162", "name": "国内无门槛立减50立减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "¥", "couponCode": "", "invaildReason": "", "discountText": "", "value": "50", "scenes": 1, "statusName": "领取", "promotionId": 765745525, "longDesc": "国内无门槛立减50"}, {"shortDesc": "国内阶梯折扣满减", "status": 1, "limitText": "最低", "meetCouponStartDate": true, "deductionList": ["满¥100享受9.0折", "满¥200享受7.0折"], "valueType": 0, "isActive": true, "briefName": "最低7.0折", "promotionSecretId": "cf1db2568f4731a6501e088c23a296f46a17840b41043c11564281d10c8ea1ddbe0f997295bb62407155da4d797ab162", "name": "国内阶梯折扣满减满减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "", "couponCode": "", "invaildReason": "", "discountText": "折", "value": "7.0", "scenes": 1, "statusName": "领取", "promotionId": 633909833, "longDesc": "国内阶梯折扣满减"}, {"shortDesc": "国内立减折扣9折", "status": 1, "limitText": "", "meetCouponStartDate": true, "deductionList": [], "valueType": 0, "isActive": true, "briefName": "立享9.0折", "promotionSecretId": "84f448d36180dc34c836084966f0264afeee84bb2840ae63a22429390ae83d56be0f997295bb62407155da4d797ab162", "name": "国内立减折扣9折立减券", "validPeriodDesc": "有效期至2023-06-30", "cornerText": "", "currencyCode": "", "couponCode": "", "invaildReason": "", "discountText": "折", "value": "9.0", "scenes": 1, "statusName": "领取", "promotionId": 288845477, "longDesc": "国内立减折扣9折"}], "ResponseStatus": {"Extension": [{"Value": "4048014632245765939", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a0eff68-455216-3950535", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1638779459632+0800)/"}, "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 792, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 792, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1638779459546, "afterFetch": 1638779460338}}