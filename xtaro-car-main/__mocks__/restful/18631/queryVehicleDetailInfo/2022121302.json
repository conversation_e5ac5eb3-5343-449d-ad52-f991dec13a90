{"baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "OK", "requestId": "465b5be1-3151-4c4b-8c57-ea6a1f34e789", "extMap": {"storeAndUserPic": "11", "vehicle end": "585", "staticDetail": "578", "restful end": "599", "easyLifeServiceLabel": "27", "soa end": "578", "store end": "582"}, "apiResCodes": []}, "ResponseStatus": {"Timestamp": "/Date(1671010577779+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "9039853610670612613"}, {"Id": "RootMessageId", "Value": "921822-0a715c24-464169-8873463"}]}, "vendorInfo": {"bizVendorCode": "13031", "vendorName": "桐叶租车", "vendorCode": "13031", "isBroker": false, "platformCode": "", "platformName": "", "haveCoupon": false, "preAuthType": 0, "supportPreAuth": 0, "prePayType": 0, "supplierId": 1294151, "vendorFullName": "桐叶网络科技有限公司", "vendorLicenseUrl": "https://dimg04.c-ctrip.com/images/03018120008wjovek7432.jpg?proc=watermark/t_6JCl5Lia5omn54Wn5L-h5oGv5YWs56S65LiT55So,s_70,l_center,r_315,d_40,c_696969,F732"}, "isSelected": true, "pickupStoreInfo": {"storeCode": "150693", "bizVendorCode": "13031", "telephone": "86-15508983166;**********", "storeName": "三亚凤凰机场店", "address": "三亚凤凰国际机场停车楼4楼", "longitude": 109.413683, "latitude": 18.305902, "storeGuild": "免费站内取还车", "storeGuildSplit": "免费站内取车", "storeWay": "免费送车上门取还车", "workTime": {"openTimeDesc": "{\"\":\"24小时营业\"}", "openTime": "00:00", "closeTime": "23:59"}, "storeType": 1, "countryId": 1, "countryName": "中国", "provinceId": 31, "cityId": 43, "cityName": "三亚", "pickUpOnDoor": true, "freeShuttle": false, "wayInfo": 16, "showType": 1}, "returnStoreInfo": {"storeCode": "150693", "bizVendorCode": "13031", "telephone": "86-15508983166;**********", "storeName": "三亚凤凰机场店", "address": "三亚凤凰国际机场停车楼4楼", "longitude": 109.413683, "latitude": 18.305902, "storeGuild": "免费站内取还车", "storeGuildSplit": "免费站内还车", "storeWay": "免费上门取车", "workTime": {"openTimeDesc": "{\"\":\"24小时营业\"}", "openTime": "00:00", "closeTime": "23:59"}, "storeType": 1, "countryId": 1, "countryName": "中国", "provinceId": 31, "cityId": 43, "cityName": "三亚", "returnOnDoor": true, "freeShuttle": false, "wayInfo": 16, "showType": 1}, "rentCenter": {"id": 2, "name": "三亚凤凰机场店", "backImage": "https://pic.c-ctrip.com/car_isd/app/newcenter/sy_back_new.jpg", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.305902", "lng": "109.413683", "filterCode": "Vendor_0", "fromTime": "00:00", "toTime": "23:59", "cityId": 43}, "rRentCenter": {"id": 2, "name": "三亚凤凰机场店", "backImage": "https://pic.c-ctrip.com/car_isd/app/newcenter/sy_back_new.jpg", "images": ["https://dimg04.c-ctrip.com/images/041581200090esk1xCFA5.jpg"], "isNew": 1, "address": "三亚凤凰国际机场停车楼4楼", "labels": ["站内便捷取还", "24小时服务", "延误时免费留车"], "lat": "18.305902", "lng": "109.413683", "filterCode": "Vendor_0", "fromTime": "00:00", "toTime": "23:59", "cityId": 43}, "vehicleInfo": {"brandId": 0, "brandEName": "雪佛兰", "brandName": "雪佛兰", "name": "雪佛兰科沃兹", "vehicleCode": "14415", "imageUrl": "//pages.c-ctrip.com/carisd/app/14415.jpg", "groupCode": "2", "groupSubClassCode": "", "groupName": "经济型", "transmissionType": 1, "transmissionName": "自动挡", "passengerNo": 5, "doorNo": 4, "luggageNo": 2, "displacement": "1.5L", "struct": "三厢", "fuel": "92号", "gearbox": "手自一体变速箱(AT)", "driveMode": "前置前驱", "imageList": ["https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png", "https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg", "https://dimg04.c-ctrip.com//images/0410u120008at350uDF48.jpg", "https://dimg04.c-ctrip.com//images/0414c120008at2ypz0D5C.jpg", "https://dimg04.c-ctrip.com//images/0412t120008at3ett66A0.jpg", "https://dimg04.c-ctrip.com//images/0414w120008at5huo1DE9.jpg"], "storeRealImageList": [], "vedio": "http://video.c-ctrip.com/videos/410125000001ii7o56860.mp4", "isSpecialized": true, "isHot": false, "vehicleAccessoryImages": ["https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg", "https://dimg04.c-ctrip.com//images/0410u120008at350uDF48.jpg", "https://dimg04.c-ctrip.com//images/0414c120008at2ypz0D5C.jpg", "https://dimg04.c-ctrip.com//images/0412t120008at3ett66A0.jpg", "https://dimg04.c-ctrip.com//images/0414w120008at5huo1DE9.jpg"], "license": "", "licenseStyle": "2", "sourcePicInfos": [{"source": 3, "type": 2, "sourceName": "年款/颜色等以实物为准丨图片来源懂车帝", "picList": [{"imageUrl": "https://dimg04.c-ctrip.com//images/0410a120008at2pjh7577.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0410u120008at350uDF48.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0412t120008at3ett66A0.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0414c120008at2ypz0D5C.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com//images/0414w120008at5huo1DE9.jpg", "sortNum": 1}, {"imageUrl": "https://dimg04.c-ctrip.com/images/04128120009soq8nw22FC.png", "sortNum": 0}]}], "oilType": 5, "fuelType": "汽油"}, "vehicleTags": [{"title": "三年内车龄", "category": 2, "type": 1, "code": "6", "description": "行驶证注册年限小于三年。", "sortNum": 4, "colorCode": "8", "labelCode": "3548", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车影像", "category": 2, "type": 1, "code": "6", "description": "车内配有倒车影像。", "sortNum": 5, "colorCode": "1", "labelCode": "3501", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "description": "车内配有倒车雷达。", "sortNum": 6, "colorCode": "1", "labelCode": "3503", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "行车记录仪", "category": 2, "type": 1, "code": "6", "description": "车内配有行车记录仪。", "sortNum": 7, "colorCode": "1", "labelCode": "3504", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "手机支架", "category": 2, "type": 1, "code": "6", "description": "车内配备有手机支架。", "sortNum": 8, "colorCode": "1", "labelCode": "3495", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "倒车雷达", "category": 2, "type": 1, "code": "6", "description": "车内配备有手机支架。", "sortNum": 8, "colorCode": "1", "labelCode": "3495", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}, {"title": "雪地胎", "category": 2, "type": 1, "code": "6", "description": "车内配备有手机支架。", "sortNum": 8, "colorCode": "1", "labelCode": "3495", "groupCode": "MarketGroup1201", "groupId": 2, "mergeId": 0}], "easyLifeInfo": {"isEasyLife": true, "subTitle": "免费取消·满油取车", "tagList": [{"title": "免费取消", "titleExtra": "(国定节假日除外)", "type": 0, "description": "订单取车时间前可免费取消", "sortNum": 1, "subTitle": "*国定节假日以页面披露为准", "showLayer": 1}, {"title": "安心保障", "titleExtra": "(需加购尊享服务)", "type": 1, "description": "付费加购尊享服务后，可更全面覆盖您车辆的损失，包括玻璃、轮胎、停运费及部分情况下的折旧费，免除后顾之忧", "sortNum": 2, "subTitle": "*覆盖损失范围以预订页面内披露为准", "showLayer": 0}, {"title": "优质车况", "type": 1, "description": "3年内车龄", "sortNum": 3, "subTitle": "", "showLayer": 0}, {"title": "满油取车", "type": 0, "description": "保障取车时油量满（不含纯电车）", "sortNum": 99, "subTitle": ""}]}, "commentInfo": {"level": "很好", "commentCount": 24006, "overallRating": "4.9", "maximumRating": 5, "commentLabel": "车辆很新", "hasComment": 1, "link": "/rn_vac_comment/_crn_config?CRNModuleName=ctrip-app&CRNType=1&channel=car-rental&scene=STORE_QUERY&storeId=150693&vehicleId=127517&vehicleName=雪佛兰科沃兹&productCategoryId=35&isHideNavBar=YES"}, "cancelRuleInfo": {"title": "取消政策", "subTitle": "支付完成至2022-12-15 10:00可免费取消", "complexSubTitle": {"contentStyle": "1", "stringObjs": [{"content": "支付完成至2022-12-15 10:00可免费取消", "style": "5"}, {"content": "，", "style": "6"}, {"content": "支付完成至2022-12-15 10:00可免费取消", "style": "6"}]}, "code": "FreeCancel", "type": 300, "showFree": true, "items": [{"title": "支付完成至2022-12-15 10:00", "subTitle": "支付完成-取车时间", "description": "免费取消", "showFree": true}, {"title": "2022-12-15 10:00后", "subTitle": "取车时间后", "description": "扣订单全额", "showFree": false}], "tableTitle": "取消时间|扣费标准"}, "promptInfos": [{"title": "延误时免费留车", "subTitle": "延后取车时，还车时间不变，若取消订单，产生的违约费用不变。", "content": ["预订时段内车辆免费保留，如有延误，到店后店员将为您协调取车。"], "type": 0, "code": "delay", "sortNum": 2}, {"title": "不限里程", "content": ["租期内没有里程数限制"], "type": 0, "code": "mileage", "sortNum": 3}, {"title": "禁行区域", "subTitle": "*若违反上述禁行规则，门店可拒绝提供或强制收回车辆；强制收回车辆时，产生的相关费用由用户承担，剩余租期费用退还规则以门店为准。", "content": ["车辆不允许进入西藏自治区、新疆、泸沽湖、湖南省娄底市、四川省阿坝州小金县、四姑娘山、不允许出境", "车辆不允许驶出、海南"], "type": 1, "code": "limitArea", "sortNum": 4}], "pickUpMaterials": {"title": "取车材料", "subTitle": "（需驾驶员本人证件）", "subObject": [{"title": "身份证", "subTitle": "或护照/回乡证/台胞证", "note": "", "content": ["有效期需1个月以上"]}, {"title": "驾驶证", "subTitle": "", "note": "支持12123电子驾照", "content": ["有效期需2个月以上", "驾龄需6个月以上"]}]}, "depositInfo": {"title": "押金说明", "items": [{"title": "租车押金", "description": "", "code": "RentalDeposit", "type": 305, "currencyCode": "¥", "currentTotalPrice": 3000, "showFree": true, "positiveDesc": "在线授权成功后免收", "needDeposit": false, "retractable": false, "sortNum": 1}, {"title": "违章押金", "description": "", "code": "IllegalDeposit", "type": 306, "currencyCode": "¥", "currentTotalPrice": 1200, "showFree": true, "positiveDesc": "在线授权成功后免收", "needDeposit": false, "retractable": false, "sortNum": 1}], "freezeDeposit": false, "showCreditCard": false}, "vendorImUrl": "ctrip://wireless/chat_customerServiceChat?isPreSale=1&sceneCode=0&bizType=2029&pageId=10650068646&ext=eyJvcmRlckluZm8iOnsiYnUiOiJwcmVjYXItbmF0aW9uYWwiLCJzdXBwbGllcklkIjoiMTUwNjkzIiwic3VwcGxpZXJOYW1lIjoi5qGQ5Y+256ef6L2mKOS4ieS6muWHpOWHsOacuuWcuuW6lykiLCJ0aXRsZSI6IumbquS9m+WFsOenkeayg+WFuSJ9LCJjYXJkSW5mbyI6e319&thirdPartytoken=698efaaa-c644-4a58-9e54-e947158d22a3", "appResponseMap": {"isFromCache": false, "isCacheValid": true, "networkCost": 901, "environmentCost": 0, "cacheFetchCost": 0, "fetchCost": 901, "setCacheCost": 0, "cacheFrom": "", "beforeFetch": 1671010576863, "afterFetch": 1671010577764}}