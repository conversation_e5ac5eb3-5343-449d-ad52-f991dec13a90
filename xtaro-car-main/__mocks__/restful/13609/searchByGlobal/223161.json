{"locations": [{"id": "47648", "type": "2", "label": "火车站", "word": "上海站", "name": "上海站", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.455611", "lat": "31.249670", "address": "静安区，静安区 秣陵路303号", "business": true, "highlight": ["上海"], "subAreas": [{"id": "199240", "type": "22", "name": "南进站口", "lon": "121.454976", "lat": "31.248574"}, {"id": "3050806", "type": "21", "name": "南广场东停车场", "lon": "121.457588", "lat": "31.247793"}, {"id": "199242", "type": "22", "name": "东南出口", "lon": "121.456312", "lat": "31.248036"}, {"id": "199243", "type": "22", "name": "北进站口", "lon": "121.456383", "lat": "31.251040"}, {"id": "3050790", "type": "19", "name": "北广场铁路售票处", "lon": "121.456956", "lat": "31.250741"}, {"id": "199245", "type": "22", "name": "西南出口", "lon": "121.453898", "lat": "31.249003"}]}, {"id": "47651", "type": "20", "label": "地标", "word": "上海博物馆", "name": "上海博物馆", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.475580", "lat": "31.228291", "address": "黄浦区，黄浦区 人民大道201号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "19974", "type": "2", "label": "火车站", "word": "上海虹桥站", "name": "上海虹桥站", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.320611", "lat": "31.194096", "address": "闵行区，闵行区 申贵路1500号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "309794", "type": "6", "label": "地铁", "word": "上海火车站-地铁站", "name": "上海火车站-地铁站", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.457516", "lat": "31.248921", "address": "静安区，静安区", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "47650", "type": "2", "label": "火车站", "word": "上海南站", "name": "上海南站", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.429457", "lat": "31.153133", "address": "徐汇区，徐汇区 沪闵路9001号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "224113", "type": "11", "label": "地标", "word": "上海大剧院", "name": "上海大剧院", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.471870", "lat": "31.229375", "address": "黄浦区，黄浦区 人民大道300号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "79928", "type": "20", "label": "地标", "word": "上海自然博物馆", "name": "上海自然博物馆", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.462664", "lat": "31.235020", "address": "静安区，静安区 北京西路510号(静安雕塑公园内)", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "151260", "type": "20", "label": "地标", "word": "上海书城(福州路店)", "name": "上海书城(福州路店)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.481460", "lat": "31.233351", "address": "黄浦区，黄浦区 福州路465号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "62123", "type": "7", "label": "地标", "word": "上海中心大厦", "name": "上海中心大厦", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.505328", "lat": "31.233557", "address": "浦东新区，浦东新区 陆家嘴银城中路501号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "12454", "type": "8", "label": "酒店", "word": "全季酒店(上海浦东机场自贸区店)", "name": "全季酒店(上海浦东机场自贸区店)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.762860", "lat": "31.143950", "address": "浦东新区，施湾六路566号2幢", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "199247", "type": "8", "label": "酒店", "word": "上海骑士王国酒店", "name": "上海骑士王国酒店", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.706990", "lat": "31.153952", "address": "浦东新区，川沙川图路300号20号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "62162", "type": "8", "label": "酒店", "word": "上海Rest-上海徐汇区民宿(漕溪北路分店)", "name": "上海Rest-上海徐汇区民宿(漕溪北路分店)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.437004", "lat": "31.192920", "address": "上海徐汇区实业公寓(漕溪北路28号)", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "44758", "type": "8", "label": "酒店", "word": "上海静安逸扉酒店", "name": "上海静安逸扉酒店", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.436260", "lat": "31.226694", "address": "静安区，武定西路1185号（武宁南路口）", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "12458", "type": "8", "label": "酒店", "word": "如家酒店·neo(上海浦东机场店)", "name": "如家酒店·neo(上海浦东机场店)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.756874", "lat": "31.154606", "address": "浦东新区，航城路508号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "7498", "type": "8", "label": "酒店", "word": "桔子酒店(上海浦东机场店)", "name": "桔子酒店(上海浦东机场店)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.763044", "lat": "31.143603", "address": "浦东新区，祝桥镇施湾六路566号3幢", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "19984", "type": "8", "label": "酒店", "word": "汉庭酒店(上海虹桥机场店)", "name": "汉庭酒店(上海虹桥机场店)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.356540", "lat": "31.183062", "address": "长宁区，沪青平公路58号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "199248", "type": "8", "label": "酒店", "word": "上海镛舍酒店", "name": "上海镛舍酒店", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.462770", "lat": "31.229870", "address": "静安区，石门一路366号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "12457", "type": "8", "label": "酒店", "word": "布丁酒店(上海浦东机场店)", "name": "布丁酒店(上海浦东机场店)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.749250", "lat": "31.172676", "address": "浦东新区，江镇水闸南路10号", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "199250", "type": "8", "label": "酒店", "word": "上海胡桃夹子酒店", "name": "上海胡桃夹子酒店", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.709640", "lat": "31.155598", "address": "浦东新区，川图路300号13幢", "business": true, "highlight": ["上海"], "subAreas": []}, {"id": "199249", "type": "8", "label": "酒店", "word": "上海迪斯尼民宿", "name": "上海迪斯尼民宿", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.709490", "lat": "31.185550", "address": "浦东新区，鼎鑫名流苑8682弄", "business": true, "highlight": ["上海"], "subAreas": []}], "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "1c84a6fc-be3a-455a-92d2-9e05e8837e57", "cost": 270}, "ResponseStatus": {"Timestamp": "2023-04-06 17:39:10", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "2009789385573357340"}, {"Id": "RootMessageId", "Value": "921822-0a715c46-466881-1912775"}]}, "wideRangeTip": "结果包含范围较大地址，建议选择具体地址。", "version": 4, "keyWord": "上海"}