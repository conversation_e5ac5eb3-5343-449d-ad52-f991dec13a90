{"locations": [{"id": "2", "type": "0", "label": "城市", "word": "上海", "name": "上海", "ename": "", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "address": "中国", "sortNum": 0, "business": true, "highlight": ["上海"], "childLocations": [{"id": "8988390", "type": "1", "label": "机场", "word": "虹桥国际机场", "name": "虹桥国际机场", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.339785", "lat": "31.196056", "highlight": [], "business": true, "timezone": 8.0}, {"id": "8988389", "type": "1", "label": "机场", "word": "浦东国际机场", "name": "浦东国际机场", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.803535", "lat": "31.150964", "highlight": [], "business": true, "timezone": 8.0}, {"id": "19974", "type": "2", "label": "火车站", "word": "上海虹桥火车站", "name": "上海虹桥火车站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.320611", "lat": "31.194096", "highlight": ["上海"], "business": true, "timezone": 8.0}, {"id": "47650", "type": "2", "label": "火车站", "word": "上海南站", "name": "上海南站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.429457", "lat": "31.153133", "highlight": ["上海"], "business": true, "timezone": 8.0}, {"id": "47648", "type": "2", "label": "火车站", "word": "上海市火车站", "name": "上海市火车站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.455611", "lat": "31.24967", "highlight": ["上海"], "business": true, "timezone": 8.0}]}, {"id": "12445", "type": "1", "label": "机场", "word": "上海浦东国际机场", "name": "上海浦东国际机场", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.808685", "lat": "31.151152", "address": "上海，中国", "business": true, "highlight": ["上海"]}, {"id": "507016", "type": "13", "label": "地标", "word": "上海大学(宝山校区)", "name": "上海大学(宝山校区)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.392460", "lat": "31.315730", "address": "上海，中国", "business": true, "highlight": ["上海"]}, {"id": "11334", "type": "13", "label": "地标", "word": "上海交通大学(闵行校区)", "name": "上海交通大学(闵行校区)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.436885", "lat": "31.025652", "address": "上海，中国", "business": true, "highlight": ["上海"]}, {"id": "496899", "type": "13", "label": "地标", "word": "国家会展中心(上海)", "name": "国家会展中心(上海)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.301964", "lat": "31.189895", "address": "上海，中国", "business": true, "highlight": ["上海"]}, {"id": "500757", "type": "13", "label": "地标", "word": "上海师范大学(徐汇校区)", "name": "上海师范大学(徐汇校区)", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.418869", "lat": "31.161760", "address": "上海，中国", "business": true, "highlight": ["上海"]}, {"id": "19975", "type": "1", "label": "机场", "word": "上海虹桥国际机场", "name": "上海虹桥国际机场", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.337425", "lat": "31.196295", "address": "上海，中国", "business": true, "highlight": ["上海"]}, {"id": "264447", "type": "20", "label": "地标", "word": "上海迪士尼度假区", "name": "上海迪士尼度假区", "cityId": 2, "timezone": 8.0, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.665534", "lat": "31.142003", "address": "上海，中国", "business": true, "highlight": ["上海"]}, {"id": "38210", "type": "0", "label": "城市", "word": "上海尔迈", "name": "上海尔迈", "ename": "", "cityId": 38210, "timezone": 2.0, "cityName": "上海尔迈", "channel": 1, "isDomestic": false, "provinceid": 100725, "provinceName": "南博滕区", "countryid": 34, "countryname": "芬兰", "address": "南博滕区，芬兰", "sortNum": 1, "business": true, "highlight": ["上海"]}, {"id": "29792", "type": "0", "label": "城市", "word": "上海因巴赫", "name": "上海因巴赫", "ename": "", "cityId": 29792, "timezone": 1.0, "cityName": "上海因巴赫", "channel": 1, "isDomestic": false, "provinceid": 10153, "provinceName": "莱茵兰-普法尔茨州", "countryid": 28, "countryname": "德国", "address": "莱茵兰-普法尔茨州，德国", "sortNum": 2, "business": true, "highlight": ["上海"]}], "baseResponse": {"isSuccess": true, "code": "200", "returnMsg": "success", "requestId": "922f0bcb-00d8-4dbb-bc03-8c752dfcd608", "cost": 289}, "ResponseStatus": {"Timestamp": "2023-04-01 21:26:17", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "4502728860520948597"}, {"Id": "RootMessageId", "Value": "921822-0a6e90d6-466765-449206"}]}, "version": 4, "keyWord": "上海"}