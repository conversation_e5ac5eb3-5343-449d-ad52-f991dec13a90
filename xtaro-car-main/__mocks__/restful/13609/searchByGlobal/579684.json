{"locations": [{"id": "2005390687", "type": "1", "label": "机场", "word": "浦东国际机场T1航站楼-dd", "name": "浦东国际机场T1航站楼-dd", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.470569", "lat": "31.224748", "address": "上海，中国", "sortNum": 1, "business": true, "highlight": ["浦东"], "meetingPointId": 687, "meetingPointRecTips": "说的是第三代的速度速度速", "subPoiType": 1}, {"id": "1149330687", "type": "1", "label": "机场", "word": "浦东国际机场T2航站楼-dd", "name": "浦东国际机场T2航站楼-dd", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.470569", "lat": "31.224748", "address": "上海，中国", "sortNum": 1, "business": true, "highlight": ["浦东"], "meetingPointId": 687, "meetingPointRecTips": "说的是第三代的速度速度速", "subPoiType": 1}, {"id": "12445", "type": "1", "label": "机场", "word": "上海浦东国际机场", "name": "上海浦东国际机场", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.808685", "lat": "31.151152", "address": "上海，中国", "business": true, "highlight": ["浦东"]}, {"id": "2", "type": "0", "label": "城市", "word": "上海（浦东老宅）", "name": "上海（浦东老宅）", "ename": "", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "address": "中国", "sortNum": 0, "business": true, "highlight": ["浦东"], "childLocations": [{"id": "1598583778", "type": "1", "label": "机场", "word": "航站楼名称超长测试中-虹桥国际机场T1航站楼-测试中-testing-testing-testing-测试", "name": "航站楼名称超长测试中-虹桥国际机场T1航站楼-测试中-testing-testing-testing-测试", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.36", "lat": "31.22", "business": true, "timezone": 8, "meetingPointId": "778", "meetingPointRecTips": "sasaasassasa", "subPoiType": 2, "hubName": "航站楼名称超长测试中-虹桥国际机场T1航站楼-测试中-testing-testing-testing", "meetingPointName": "-测试"}, {"id": "2005390687", "type": "1", "label": "机场", "word": "浦东国际机场T1航站楼-dd", "name": "浦东国际机场T1航站楼-dd", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.470569", "lat": "31.224748", "business": true, "timezone": 8, "meetingPointId": "687", "meetingPointRecTips": "说的是第三代的速度速度速", "subPoiType": 1, "hubName": "浦东国际机场T1航站楼", "meetingPointName": "-dd"}, {"id": "1149330687", "type": "1", "label": "机场", "word": "浦东国际机场T2航站楼-dd", "name": "浦东国际机场T2航站楼-dd", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.470569", "lat": "31.224748", "business": true, "timezone": 8, "meetingPointId": "687", "meetingPointRecTips": "说的是第三代的速度速度速", "subPoiType": 1, "hubName": "浦东国际机场T2航站楼", "meetingPointName": "-dd"}, {"id": "4857219778", "type": "1", "label": "机场", "word": "虹桥国际机场T2航站楼-测试", "name": "虹桥国际机场T2航站楼-测试", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.36", "lat": "31.22", "business": true, "timezone": 8, "meetingPointId": "778", "meetingPointRecTips": "sasaasassasa", "subPoiType": 2, "hubName": "虹桥国际机场T2航站楼", "meetingPointName": "-测试"}, {"id": "47650", "type": "2", "label": "火车站", "word": "上海南站", "name": "上海南站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.429457", "lat": "31.153133", "highlight": [], "business": true, "timezone": 8}, {"id": "19974", "type": "2", "label": "火车站", "word": "上海虹桥火车站", "name": "上海虹桥火车站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.320611", "lat": "31.194096", "highlight": [], "business": true, "timezone": 8}, {"id": "47648", "type": "2", "label": "火车站", "word": "上海市火车站", "name": "上海市火车站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.455611", "lat": "31.24967", "highlight": [], "business": true, "timezone": 8}]}, {"id": "12451", "type": "8", "label": "酒店", "word": "上海浦东香格里拉", "name": "上海浦东香格里拉", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.499548", "lat": "31.235805", "address": "上海，中国", "business": true, "highlight": ["浦东"]}, {"id": "12447", "type": "20", "label": "地标", "word": "上海浦东国际机场T2航站楼-国内出发", "name": "上海浦东国际机场T2航站楼-国内出发", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.808231", "lat": "31.151879", "address": "上海，中国", "business": true, "highlight": ["浦东"]}, {"id": "256258", "type": "20", "label": "地标", "word": "上海市浦东新区人民政府", "name": "上海市浦东新区人民政府", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.544280", "lat": "31.221575", "address": "上海，中国", "business": true, "highlight": ["浦东"]}, {"id": "65571", "type": "8", "label": "酒店", "word": "上海浦东嘉里大酒店", "name": "上海浦东嘉里大酒店", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.563776", "lat": "31.213093", "address": "上海，中国", "business": true, "highlight": ["浦东"]}, {"id": "12453", "type": "5", "label": "购物", "word": "浦东嘉里城", "name": "浦东嘉里城", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.563703", "lat": "31.212615", "address": "上海，中国", "business": true, "highlight": ["浦东"]}, {"id": "237624", "type": "20", "label": "地标", "word": "浦东国际机场-地铁站", "name": "浦东国际机场-地铁站", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.806196", "lat": "31.150274", "address": "上海，中国", "business": true, "highlight": ["浦东"]}, {"id": "12450", "type": "20", "label": "地标", "word": "浦东大道-地铁站", "name": "浦东大道-地铁站", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.519406", "lat": "31.240163", "address": "上海，中国", "business": true, "highlight": ["浦东"]}, {"id": "65570", "type": "8", "label": "酒店", "word": "上海浦东星河湾酒店", "name": "上海浦东星河湾酒店", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.538053", "lat": "31.191983", "address": "上海，中国", "business": true, "highlight": ["浦东"]}], "BaseResponse": {"IsSuccess": true, "Code": "200", "ReturnMsg": "success", "RequestId": "2b6e34c8-b6a5-4385-b053-368491c9d889", "Cost": 902}, "ResponseStatus": {"Timestamp": "/Date(1699349396797+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "5139037264946944169"}, {"Id": "RootMessageId", "Value": "921822-0a9042a1-472041-57727"}]}, "version": 4, "keyWord": "浦东"}