{"wideRangeTip": "结果包含范围较大地址，建议选择具体地址。", "version": 4, "keyWord": "shanghai", "ResponseStatus": {"Extension": [{"Value": "7092795116866302015", "Id": "CLOGGING_TRACE_ID"}, {"Value": "921822-0a0448df-466996-46053", "Id": "RootMessageId"}], "Ack": "Success", "Errors": [], "Timestamp": "/Date(1681189052880+0800)/"}, "locations": [{"business": true, "word": "上海站", "countryname": "中国", "lon": "121.455611", "channel": 1, "subAreas": [{"id": "199240", "lat": "31.248574", "type": "22", "name": "南进站口", "lon": "121.454976"}, {"id": "3050806", "lat": "31.247793", "type": "21", "name": "南广场东停车场", "lon": "121.457588"}, {"id": "199242", "lat": "31.248036", "type": "22", "name": "东南出口", "lon": "121.456312"}, {"id": "199243", "lat": "31.251040", "type": "22", "name": "北进站口", "lon": "121.456383"}, {"id": "3050790", "lat": "31.250741", "type": "19", "name": "北广场铁路售票处", "lon": "121.456956"}, {"id": "199245", "lat": "31.249003", "type": "22", "name": "西南出口", "lon": "121.453898"}], "name": "上海站", "type": "2", "id": "47648", "cityId": 2, "provinceid": 2, "label": "火车站", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.249670", "countryid": 1, "cityName": "上海", "address": "静安区，静安区 秣陵路303号", "highlight": []}, {"business": true, "word": "上海博物馆", "countryname": "中国", "lon": "121.475580", "channel": 1, "subAreas": [], "name": "上海博物馆", "type": "20", "id": "47651", "cityId": 2, "provinceid": 2, "label": "地标", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.228291", "countryid": 1, "cityName": "上海", "address": "黄浦区，黄浦区 人民大道201号", "highlight": []}, {"business": true, "word": "上海滨城宾馆", "countryname": "中国", "lon": "121.453590", "channel": 1, "subAreas": [], "name": "上海滨城宾馆", "type": "8", "id": "6396943", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "30.929585", "countryid": 1, "cityName": "上海", "address": "奉贤区，南桥镇沪杭公路1799号", "highlight": []}, {"business": true, "word": "上海何遇酒店", "countryname": "中国", "lon": "121.712234", "channel": 1, "subAreas": [], "name": "上海何遇酒店", "type": "8", "id": "1422726", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.189980", "countryid": 1, "cityName": "上海", "address": "浦东新区，川沙川环南路81号", "highlight": []}, {"business": true, "word": "上海欢乐颂轰趴别墅(凌空北路分店)", "countryname": "中国", "lon": "121.687530", "channel": 1, "subAreas": [], "name": "上海欢乐颂轰趴别墅(凌空北路分店)", "type": "8", "id": "19325807", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.272888", "countryid": 1, "cityName": "上海", "address": "浦东新区，上海浦东新区欢乐颂别墅轰趴", "highlight": []}, {"business": true, "word": "美豪酒店(上海虹桥机场国展中心店)", "countryname": "中国", "lon": "121.309296", "channel": 1, "subAreas": [], "name": "美豪酒店(上海虹桥机场国展中心店)", "type": "8", "id": "657783", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.220428", "countryid": 1, "cityName": "上海", "address": "闵行区，申长北路166弄3号", "highlight": []}, {"business": true, "word": "上海驿站(10号店)", "countryname": "中国", "lon": "121.678635", "channel": 1, "subAreas": [], "name": "上海驿站(10号店)", "type": "8", "id": "12548116", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.177359", "countryid": 1, "cityName": "上海", "address": "界龙村吴陆家宅36号楼", "highlight": []}, {"business": true, "word": "上海118旅馆连锁", "countryname": "中国", "lon": "121.373116", "channel": 1, "subAreas": [], "name": "上海118旅馆连锁", "type": "8", "id": "5826793", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.093786", "countryid": 1, "cityName": "上海", "address": "闵行区，沁春路397号5F", "highlight": []}, {"business": true, "word": "上海外滩全景民宿(北外滩111分店)", "countryname": "中国", "lon": "121.493980", "channel": 1, "subAreas": [], "name": "上海外滩全景民宿(北外滩111分店)", "type": "8", "id": "740819", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.248804", "countryid": 1, "cityName": "上海", "address": "虹口区，上海虹口区东长治路299弄", "highlight": []}, {"business": true, "word": "上海虹桥郁锦香宾馆", "countryname": "中国", "lon": "121.410210", "channel": 1, "subAreas": [], "name": "上海虹桥郁锦香宾馆", "type": "8", "id": "19997", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.204012", "countryid": 1, "cityName": "上海", "address": "长宁区，延安西路2000号", "highlight": []}, {"business": true, "word": "上海Amarissa精品民宿@上海@曼谷(3号店)", "countryname": "中国", "lon": "121.246450", "channel": 1, "subAreas": [], "name": "上海Amarissa精品民宿@上海@曼谷(3号店)", "type": "8", "id": "965358", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.060678", "countryid": 1, "cityName": "上海", "address": "富悦", "highlight": []}, {"business": true, "word": "上海虹桥站", "countryname": "中国", "lon": "121.320611", "channel": 1, "subAreas": [], "name": "上海虹桥站", "type": "2", "id": "19974", "cityId": 2, "provinceid": 2, "label": "火车站", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.194096", "countryid": 1, "cityName": "上海", "address": "闵行区，闵行区 申贵路1500号", "highlight": []}, {"business": true, "word": "上海火车站-地铁站", "countryname": "中国", "lon": "121.457516", "channel": 1, "subAreas": [], "name": "上海火车站-地铁站", "type": "6", "id": "309794", "cityId": 2, "provinceid": 2, "label": "地铁", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.248921", "countryid": 1, "cityName": "上海", "address": "静安区，静安区", "highlight": []}, {"business": true, "word": "上海南站", "countryname": "中国", "lon": "121.429457", "channel": 1, "subAreas": [], "name": "上海南站", "type": "2", "id": "47650", "cityId": 2, "provinceid": 2, "label": "火车站", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.153133", "countryid": 1, "cityName": "上海", "address": "徐汇区，徐汇区 沪闵路9001号", "highlight": []}, {"business": true, "word": "上海自然博物馆", "countryname": "中国", "lon": "121.462664", "channel": 1, "subAreas": [], "name": "上海自然博物馆", "type": "20", "id": "79928", "cityId": 2, "provinceid": 2, "label": "地标", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.235020", "countryid": 1, "cityName": "上海", "address": "静安区，静安区 北京西路510号(静安雕塑公园内)", "highlight": []}, {"business": true, "word": "上海广场", "countryname": "中国", "lon": "121.478109", "channel": 1, "subAreas": [], "name": "上海广场", "type": "5", "id": "199246", "cityId": 2, "provinceid": 2, "label": "购物", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.225388", "countryid": 1, "cityName": "上海", "address": "黄浦区，黄浦区 淮海路东段中路138号", "highlight": []}, {"business": true, "word": "虹桥火车站-地铁站", "countryname": "中国", "lon": "121.320132", "channel": 1, "subAreas": [], "name": "虹桥火车站-地铁站", "type": "6", "id": "19977", "cityId": 2, "provinceid": 2, "label": "地铁", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.193920", "countryid": 1, "cityName": "上海", "address": "闵行区，闵行区", "highlight": []}, {"business": true, "word": "上海图书馆", "countryname": "中国", "lon": "121.444700", "channel": 1, "subAreas": [], "name": "上海图书馆", "type": "13", "id": "390482", "cityId": 2, "provinceid": 2, "label": "地标", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.207485", "countryid": 1, "cityName": "上海", "address": "徐汇区，徐汇区 淮海中路1555号", "highlight": []}, {"business": true, "word": "美豪丽致酒店(上海嘉定新城中心店)", "countryname": "中国", "lon": "121.255860", "channel": 1, "subAreas": [], "name": "美豪丽致酒店(上海嘉定新城中心店)", "type": "8", "id": "939478", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.346380", "countryid": 1, "cityName": "上海", "address": "嘉定区，永盛路1190号", "highlight": []}, {"business": true, "word": "上海徐汇云睿酒店", "countryname": "中国", "lon": "121.443080", "channel": 1, "subAreas": [], "name": "上海徐汇云睿酒店", "type": "8", "id": "62163", "cityId": 2, "provinceid": 2, "label": "酒店", "provinceName": "上海", "timezone": 8, "isDomestic": true, "lat": "31.176218", "countryid": 1, "cityName": "上海", "address": "徐汇区，龙华西路515号", "highlight": []}], "BaseResponse": {"IsSuccess": true, "ReturnMsg": "success", "Cost": 257, "Code": "200", "RequestId": "e2966441-ce7f-47f7-8247-697ca98796db"}}