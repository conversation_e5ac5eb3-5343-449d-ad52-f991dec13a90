{"locations": [{"id": "2", "type": "0", "label": "城市", "word": "上海", "name": "上海", "ename": "", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.4737", "lat": "31.230415", "address": "浦东国际机场", "sortNum": 0, "business": true, "highlight": ["上海"], "childLocations": [{"id": "8988389", "type": "1", "label": "机场", "word": "浦东国际机场", "name": "浦东国际机场", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.803535", "lat": "31.150964", "address": "浦东国际机场", "highlight": [], "business": true, "timezone": 8, "poiId": "PVG", "poiSource": "basic_apt"}, {"id": "8988390", "type": "1", "label": "机场", "word": "虹桥国际机场", "name": "虹桥国际机场", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.339785", "lat": "31.196056", "address": "虹桥国际机场", "highlight": [], "business": true, "timezone": 8, "poiId": "SHA", "poiSource": "basic_apt", "childLocations": [{"id": "6974541128", "type": "1", "label": "机场", "word": "T2航站楼-停车库4号电梯厅", "name": "T2航站楼-停车库4号电梯厅", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "120.435734", "lat": "30.234979", "business": true, "timezone": 8, "meetingPointId": "1122", "meetingPointRecTips": "到达口步行3分钟", "subPoiType": 1, "hubName": "T2航站楼", "meetingPointName": "-停车库4号电梯厅"}, {"id": "6974541138", "type": "1", "label": "机场", "word": "T1航站楼", "name": "T1航站楼", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "120.435734", "lat": "30.234979", "business": true, "timezone": 8}]}, {"id": "47650", "type": "2", "label": "火车站", "word": "上海南站", "name": "上海南站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.429457", "lat": "31.153133", "address": "上海市徐汇区沪闵路9001号", "highlight": ["上海"], "business": true, "timezone": 8, "poiId": "905220f3f41e706bdb131806", "poiSource": "Baidu"}, {"id": "19974", "type": "2", "label": "火车站", "word": "上海虹桥火车站", "name": "上海虹桥火车站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.320611", "lat": "31.194096", "address": "上海市闵行区申贵路1500号", "highlight": ["上海"], "business": true, "timezone": 8, "poiId": "6763c15f0a0bf9e96140a9c8", "poiSource": "Baidu", "childLocations": [{"id": "6974541128", "type": "1", "label": "火车站", "word": "P2停车库入口", "name": "P2停车库入口", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "120.435734", "lat": "30.234979", "business": true, "timezone": 8, "meetingPointId": "1128", "meetingPointRecTips": "到达口步行3分钟", "subPoiType": 1, "hubName": "", "meetingPointName": "P2停车库入口"}, {"id": "6974541128", "type": "1", "label": "火车站", "word": "P3停车库入口", "name": "P3停车库入口", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "120.435734", "lat": "30.234979", "business": true, "timezone": 8, "meetingPointId": "1121", "meetingPointRecTips": "到达口步行4分钟", "subPoiType": 1, "hubName": "", "meetingPointName": "P3停车库入口"}]}, {"id": "47648", "type": "2", "label": "火车站", "word": "上海市火车站", "name": "上海市火车站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.455611", "lat": "31.24967", "address": "上海市静安区秣陵路303号", "highlight": ["上海"], "business": true, "timezone": 8, "poiId": "967e86248da9dd3708b93abc", "poiSource": "Baidu"}, {"id": "47648", "type": "2", "label": "火车站", "word": "上海市虹桥火车站", "name": "上海市虹桥火车站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.455611", "lat": "31.24967", "address": "上海市静安区秣陵路303号", "highlight": ["上海"], "business": true, "timezone": 8, "poiId": "967e86248da9dd3708b93abc", "poiSource": "Baidu"}, {"id": "47648", "type": "2", "label": "火车站", "word": "上海市车站", "name": "上海市车站", "cityId": 2, "cityName": "上海", "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.455611", "lat": "31.24967", "address": "上海市静安区秣陵路303号", "highlight": ["上海"], "business": true, "timezone": 8, "poiId": "967e86248da9dd3708b93abc", "poiSource": "Baidu"}], "isMatchFromCountry": false}, {"id": "264447", "type": "20", "label": "地标", "word": "上海迪士尼度假区", "name": "上海迪士尼度假区", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.665534", "lat": "31.142003", "address": "上海，中国", "business": true, "highlight": ["上海"], "poiId": "81ad5716b2c9c7fbe8e58272", "poiSource": "Baidu"}, {"id": "19975", "type": "1", "label": "机场", "word": "上海虹桥国际机场", "name": "上海虹桥国际机场", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.340365", "lat": "31.197171", "address": "上海，中国", "business": true, "highlight": ["上海"], "poiId": "d6411a264581c801e81e97a5", "poiSource": "Baidu"}, {"id": "12445", "type": "1", "label": "机场", "word": "上海浦东国际机场", "name": "上海浦东国际机场", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.808685", "lat": "31.151152", "address": "上海，中国", "business": true, "highlight": ["上海"], "poiId": "41b461a937e4a0528c1a890e", "poiSource": "Baidu"}, {"id": "11334", "type": "13", "label": "地标", "word": "上海交通大学(闵行校区)", "name": "上海交通大学(闵行校区)", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.436885", "lat": "31.025652", "address": "上海，中国", "business": true, "highlight": ["上海"], "poiId": "5ffb1816bbf0a6466f476037", "poiSource": "Baidu"}, {"id": "507016", "type": "13", "label": "地标", "word": "上海大学(宝山校区)", "name": "上海大学(宝山校区)", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.392460", "lat": "31.315730", "address": "上海，中国", "business": true, "highlight": ["上海"], "poiId": "74aeaab37a6e83bc38221ca4", "poiSource": "Baidu"}, {"id": "151285", "type": "13", "label": "地标", "word": "上海交通大学(徐汇校区)", "name": "上海交通大学(徐汇校区)", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.433034", "lat": "31.199228", "address": "上海，中国", "business": true, "highlight": ["上海"], "poiId": "59d211f784cdf5fdfa0cad6b", "poiSource": "Baidu"}, {"id": "500757", "type": "13", "label": "地标", "word": "上海师范大学(徐汇校区)", "name": "上海师范大学(徐汇校区)", "cityId": 2, "timezone": 8, "cityName": "上海", "channel": 1, "isDomestic": true, "provinceid": 2, "provinceName": "上海", "countryid": 1, "countryname": "中国", "lon": "121.418869", "lat": "31.161760", "address": "上海，中国", "business": true, "highlight": ["上海"], "poiId": "acfcdb01990f6bf1725e80fb", "poiSource": "Baidu"}, {"id": "38210", "type": "0", "label": "城市", "word": "上海尔迈", "name": "上海尔迈", "ename": "", "cityId": 38210, "timezone": 2, "cityName": "上海尔迈", "channel": 1, "isDomestic": false, "provinceid": 100725, "provinceName": "南博滕区", "countryid": 34, "countryname": "芬兰", "lon": "22.794819", "lat": "63.145473", "address": "南博滕区，芬兰", "sortNum": 1, "business": true, "highlight": ["上海"], "isMatchFromCountry": false}, {"id": "340200", "type": "0", "label": "城市", "word": "Shanghai", "name": "Shanghai", "ename": "", "cityId": 340200, "timezone": 0, "cityName": "Shanghai", "channel": 1, "isDomestic": false, "provinceid": 10111, "provinceName": "弗吉尼亚州", "countryid": 66, "countryname": "美国", "lon": "-76.75674", "lat": "37.58868", "address": "弗吉尼亚州，美国", "sortNum": 2, "business": true, "highlight": [], "isMatchFromCountry": false}, {"id": "29792", "type": "0", "label": "城市", "word": "上海因巴赫", "name": "上海因巴赫", "ename": "", "cityId": 29792, "timezone": 1, "cityName": "上海因巴赫", "channel": 1, "isDomestic": false, "provinceid": 10153, "provinceName": "莱茵兰-普法尔茨州", "countryid": 28, "countryname": "德国", "lon": "7.7846165", "lat": "50.022736", "address": "莱茵兰-普法尔茨州，德国", "sortNum": 3, "business": true, "highlight": ["上海"], "isMatchFromCountry": false}, {"id": "98030", "type": "0", "label": "城市", "word": "上海日马尼采", "name": "上海日马尼采", "ename": "", "cityId": 98030, "timezone": 0, "cityName": "上海日马尼采", "channel": 1, "isDomestic": false, "provinceid": 10478, "provinceName": "帕尔杜比采州", "countryid": 162, "countryname": "捷克", "lon": "16.711195", "lat": "49.960995", "address": "帕尔杜比采州，捷克", "sortNum": 4, "business": true, "highlight": ["上海"], "isMatchFromCountry": false}], "BaseResponse": {"IsSuccess": true, "Code": "200", "ReturnMsg": "success", "RequestId": "55f1d5ae-cb78-4870-be19-353e3672a621", "Cost": 290}, "ResponseStatus": {"Timestamp": "/Date(1709280396105+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "6336497191964615297"}, {"Id": "RootMessageId", "Value": "100025527-0a063db3-474800-35525"}]}, "version": 4, "keyWord": "上海", "extras": {}}