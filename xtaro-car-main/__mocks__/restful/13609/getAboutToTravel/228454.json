{"travelItems": [{"itemInfo": {"type": 3, "typeLabel": "火", "fromCity": "上海", "toCity": "郑州", "isRoundTrip": true, "itemName": "郑州·郑州东站", "dateList": ["01-24 21:28 苏州北 到达", "01-24 21:28 苏州南 返程"]}, "pickupPoint": {"locationType": 2, "cityId": 1082, "cityName": "上海", "locationCode": "SDU", "locationName": "上海虹桥", "date": "2022-01-24 21:30:00", "poi": {"longitude": 117.056532, "latitude": 31.48812}, "isDomestic": true}, "returnPoint": {"locationType": 2, "cityId": 1082, "cityName": "郑州", "locationCode": "SDU", "locationName": "郑州东站", "date": "2022-01-26 21:30:00", "poi": {"longitude": 117.056532, "latitude": 31.48812}, "isDomestic": true}}], "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "57d034c2-c9b0-4cbd-a202-9d8d2617fa5a", "cost": 26}, "ResponseStatus": {"Timestamp": "2022-01-10 17:07:33", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "3671953275020330109"}, {"Id": "RootMessageId", "Value": "921822-0a3d3ace-456057-925827"}]}}