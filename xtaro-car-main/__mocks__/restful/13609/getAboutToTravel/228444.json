{"travelItems": [{"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "上海", "toCity": "北京", "itemName": "北京", "isRoundTrip": true, "dateList": ["01-01 11:30 #上海虹桥# 到达", "01-01 13:30 #首都T3# 出发"]}, "pickupPoint": {"locationType": 1, "cityId": 1, "cityName": "上海", "locationCode": "71529173", "locationName": "上海虹桥机场", "date": "2032-01-01 11:30:00", "poi": {"longitude": 0.0, "latitude": 0.0}, "isDomestic": true}, "returnPoint": {"locationType": 1, "cityId": 1, "cityName": "北京", "locationCode": "71571545", "locationName": "首都国际机场3号航站楼", "date": "2032-01-01 13:30:00", "poi": {"longitude": 116.*********, "latitude": 40.*********}, "isDomestic": true}}, {"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "北京", "toCity": "海口", "itemName": "海口·美兰", "isRoundTrip": true, "dateList": ["03-02 12:55 到达", "03-04 11:25 出发"]}, "pickupPoint": {"locationType": 1, "cityId": 42, "cityName": "海口", "locationCode": "61131079", "locationName": "美兰国际机场航站楼", "date": "2022-03-02 13:00:00", "poi": {"longitude": 110.46878, "latitude": 19.939508}, "isDomestic": true}, "returnPoint": {"locationType": 1, "cityId": 42, "cityName": "海口", "locationCode": "61131079", "locationName": "美兰国际机场航站楼", "date": "2022-03-04 08:15:00", "poi": {"longitude": 110.46878, "latitude": 19.939508}, "isDomestic": true}}], "BaseResponse": {"IsSuccess": true, "Code": "unknown", "ReturnMsg": "success", "RequestId": "5d644b23-fea4-4b6f-8f0a-8664d531a9b1", "Cost": 32}, "ResponseStatus": {"Timestamp": "/Date(1642410140147+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "2913825001040153440"}, {"Id": "RootMessageId", "Value": "100025527-0a068a23-456225-9905"}]}}