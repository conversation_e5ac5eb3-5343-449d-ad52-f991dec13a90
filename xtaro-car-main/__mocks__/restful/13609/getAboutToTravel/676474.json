{"travelItems": [{"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "纽约", "toCity": "纳什维尔", "itemName": "纳什维尔·纳什维尔国际机场", "isRoundTrip": false, "dateList": ["11-28 23:00 到达"]}, "pickupPoint": {"locationType": 1, "cityId": 3228, "cityName": "纳什维尔", "locationCode": "BNA", "locationName": "纳什维尔国际机场", "date": "2023-11-29 02:00:00", "poi": {"longitude": -86.677371, "latitude": 36.126317}, "isDomestic": false, "countryId": 66, "countryName": "美国"}, "returnPoint": {"locationType": 1, "cityId": 3228, "cityName": "纳什维尔", "locationCode": "BNA", "locationName": "纳什维尔国际机场", "date": "2023-12-06 02:00:00", "poi": {"longitude": -86.677371, "latitude": 36.126317}, "isDomestic": false, "countryId": 66, "countryName": "美国"}}, {"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "纳什维尔", "toCity": "洛杉矶", "itemName": "洛杉矶·洛杉矶国际机场", "isRoundTrip": false, "dateList": ["11-29 08:20 到达"]}, "pickupPoint": {"locationType": 1, "cityId": 347, "cityName": "洛杉矶", "locationCode": "LAX", "locationName": "洛杉矶国际机场", "date": "2023-11-29 11:30:00", "poi": {"longitude": -118.40853, "latitude": 33.941589}, "isDomestic": false, "countryId": 66, "countryName": "美国"}, "returnPoint": {"locationType": 1, "cityId": 347, "cityName": "洛杉矶", "locationCode": "LAX", "locationName": "洛杉矶国际机场", "date": "2023-12-06 11:30:00", "poi": {"longitude": -118.40853, "latitude": 33.941589}, "isDomestic": false, "countryId": 66, "countryName": "美国"}}, {"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "新加坡", "toCity": "曼谷", "itemName": "曼谷·廊曼国际机场", "isRoundTrip": true, "dateList": ["12-01 13:35 到达", "12-05 07:40 返程"]}, "pickupPoint": {"locationType": 1, "cityId": 359, "cityName": "曼谷", "locationCode": "DMK", "locationName": "廊曼国际机场", "date": "2023-12-01 17:00:00", "poi": {"longitude": 100.604199, "latitude": 13.91326}, "isDomestic": false, "countryId": 4, "countryName": "泰国"}, "returnPoint": {"locationType": 1, "cityId": 359, "cityName": "曼谷", "locationCode": "DMK", "locationName": "廊曼国际机场", "date": "2023-12-05 04:30:00", "poi": {"longitude": 100.604199, "latitude": 13.91326}, "isDomestic": false, "countryId": 4, "countryName": "泰国"}}, {"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "洛杉矶", "toCity": "奥克兰", "itemName": "奥克兰·奥克兰国际机场", "isRoundTrip": false, "dateList": ["01-17 11:08 到达"]}, "pickupPoint": {"locationType": 1, "cityId": 1435, "cityName": "奥克兰", "locationCode": "OAK", "locationName": "奥克兰国际机场", "date": "2024-01-17 14:30:00", "poi": {"longitude": -122.237252, "latitude": 37.712607}, "isDomestic": false, "countryId": 66, "countryName": "美国"}, "returnPoint": {"locationType": 1, "cityId": 1435, "cityName": "奥克兰", "locationCode": "OAK", "locationName": "奥克兰国际机场", "date": "2024-01-24 14:30:00", "poi": {"longitude": -122.237252, "latitude": 37.712607}, "isDomestic": false, "countryId": 66, "countryName": "美国"}}], "BaseResponse": {"IsSuccess": true, "Code": "200", "ReturnMsg": "success", "RequestId": "9a040a07-9950-4ac8-844f-20ad5e10726c", "Cost": 225}, "ResponseStatus": {"Timestamp": "/Date(1701055264110+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "3882718626906255747"}, {"Id": "RootMessageId", "Value": "921822-0a1666db-472515-21637"}]}}