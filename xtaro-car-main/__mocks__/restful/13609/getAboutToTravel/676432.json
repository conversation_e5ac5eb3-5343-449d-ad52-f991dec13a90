{"travelItems": [{"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "纽约", "toCity": "纳什维尔", "itemName": "纳什维尔·纳什维尔国际纳什维尔国际机场", "isRoundTrip": false, "dateList": ["11-28 23:00 到达"]}, "pickupPoint": {"locationType": 1, "cityId": 3228, "cityName": "纳什维尔", "locationCode": "BNA", "locationName": "纳什维尔国际机场", "date": "2023-11-29 02:00:00", "poi": {"longitude": -86.677371, "latitude": 36.126317}, "isDomestic": false, "countryId": 66, "countryName": "美国"}, "returnPoint": {"locationType": 1, "cityId": 3228, "cityName": "纳什维尔", "locationCode": "BNA", "locationName": "纳什维尔国际机场", "date": "2023-12-06 02:00:00", "poi": {"longitude": -86.677371, "latitude": 36.126317}, "isDomestic": false, "countryId": 66, "countryName": "美国"}}], "BaseResponse": {"IsSuccess": true, "Code": "200", "ReturnMsg": "success", "RequestId": "9a040a07-9950-4ac8-844f-20ad5e10726c", "Cost": 225}, "ResponseStatus": {"Timestamp": "/Date(1701055264110+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "3882718626906255747"}, {"Id": "RootMessageId", "Value": "921822-0a1666db-472515-21637"}]}}