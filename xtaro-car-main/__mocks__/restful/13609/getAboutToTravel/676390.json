{"travelItems": [{"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "上海", "toCity": "曼谷", "itemName": "曼谷·素万那普国际机场", "isRoundTrip": false, "dateList": ["01-09 00:40 到达"]}, "pickupPoint": {"locationType": 1, "cityId": 359, "cityName": "曼谷", "locationCode": "BKK", "locationName": "素万那普国际机场", "date": "2024-01-09 04:00:00", "poi": {"longitude": 100.750112, "latitude": 13.689999}, "isDomestic": false, "countryId": 4, "countryName": "泰国"}, "returnPoint": {"locationType": 1, "cityId": 359, "cityName": "曼谷", "locationCode": "BKK", "locationName": "素万那普国际机场", "date": "2024-01-16 04:00:00", "poi": {"longitude": 100.750112, "latitude": 13.689999}, "isDomestic": false, "countryId": 4, "countryName": "泰国"}}, {"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "大阪", "toCity": "东京", "itemName": "东京·羽田机场", "isRoundTrip": true, "dateList": ["02-21 08:15 到达", "02-22 09:00 返程"]}, "pickupPoint": {"locationType": 1, "cityId": 228, "cityName": "东京", "locationCode": "HND", "locationName": "羽田机场", "date": "2024-02-21 11:30:00", "poi": {"longitude": 139.779839, "latitude": 35.549393}, "isDomestic": false, "countryId": 78, "countryName": "日本"}, "returnPoint": {"locationType": 1, "cityId": 228, "cityName": "东京", "locationCode": "HND", "locationName": "羽田机场", "date": "2024-02-22 06:00:00", "poi": {"longitude": 139.779839, "latitude": 35.549393}, "isDomestic": false, "countryId": 78, "countryName": "日本"}}, {"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "上海", "toCity": "曼谷", "isRoundTrip": true, "dateList": ["03-13 00:55 到达", "03-16 21:15 返程"], "itemNames": ["曼谷·素万那普国际机场", "曼谷·廊曼国际机场"]}, "pickupPoint": {"locationType": 1, "cityId": 359, "cityName": "曼谷", "locationCode": "BKK", "locationName": "素万那普国际机场", "date": "2024-03-13 04:00:00", "poi": {"longitude": 100.750112, "latitude": 13.689999}, "isDomestic": false, "countryId": 4, "countryName": "泰国"}, "returnPoint": {"locationType": 1, "cityId": 359, "cityName": "曼谷", "locationCode": "DMK", "locationName": "廊曼国际机场", "date": "2024-03-16 18:00:00", "poi": {"longitude": 100.604199, "latitude": 13.91326}, "isDomestic": false, "countryId": 4, "countryName": "泰国"}}, {"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "旧金山", "toCity": "纽约", "itemName": "纽约·纽瓦克国际机场", "isRoundTrip": false, "dateList": ["03-20 16:42 到达"]}, "pickupPoint": {"locationType": 1, "cityId": 633, "cityName": "纽约", "locationCode": "EWR", "locationName": "纽瓦克国际机场", "date": "2024-03-20 20:00:00", "poi": {"longitude": -74.174462, "latitude": 40.689531}, "isDomestic": false, "countryId": 66, "countryName": "美国"}, "returnPoint": {"locationType": 1, "cityId": 633, "cityName": "纽约", "locationCode": "EWR", "locationName": "纽瓦克国际机场", "date": "2024-03-27 20:00:00", "poi": {"longitude": -74.174462, "latitude": 40.689531}, "isDomestic": false, "countryId": 66, "countryName": "美国"}}, {"itemInfo": {"type": 1, "typeLabel": "机", "fromCity": "洛杉矶", "toCity": "温哥华", "itemName": "温哥华·温哥华国际机场", "isRoundTrip": false, "dateList": ["03-27 09:17 到达"]}, "pickupPoint": {"locationType": 1, "cityId": 476, "cityName": "温哥华", "locationCode": "YVR", "locationName": "温哥华国际机场", "date": "2024-03-27 12:30:00", "poi": {"longitude": -123.181512, "latitude": 49.196691}, "isDomestic": false, "countryId": 47, "countryName": "加拿大"}, "returnPoint": {"locationType": 1, "cityId": 476, "cityName": "温哥华", "locationCode": "YVR", "locationName": "温哥华国际机场", "date": "2024-04-03 12:30:00", "poi": {"longitude": -123.181512, "latitude": 49.196691}, "isDomestic": false, "countryId": 47, "countryName": "加拿大"}}], "BaseResponse": {"IsSuccess": true, "Code": "200", "ReturnMsg": "success", "RequestId": "4636d213-cd2c-4197-afdf-3c3742b8d4d1", "Cost": 273}, "ResponseStatus": {"Timestamp": "/Date(1701402950639+0800)/", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "6986205132269212198"}, {"Id": "RootMessageId", "Value": "921822-0a90108b-472611-62921"}]}}