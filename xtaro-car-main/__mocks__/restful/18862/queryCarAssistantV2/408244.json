{"ResponseStatus": {"Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "1559108721864894430"}, {"Id": "RootMessageId", "Value": "100016183-0a3c7efc-463179-36197"}]}, "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 235}, "summary": [{"content": "取车必备驾驶员本人身份证+中国大陆驾照", "style": "background:pink"}, {"content": "了解取还车流程"}], "attrDto": {"sort": 5, "history": "true"}, "guidInfoGroupList": [{"isDefault": true, "groupTitle": "before", "guidItemList": [{"code": "necessaryMaterials", "title": "取车必备材料", "desc": "均需驾驶员测试人朱杰本人证件", "items": [{"code": "necessaryMaterialsSon", "linkContent": [{"id": 1, "title": "身份证原件", "desc": "有效期3个月以上", "type": 1}, {"id": 2, "title": "中国大陆驾驶证原件\n或 “交管12123”APP发放的电子驾驶证", "desc": "有效期1个月以上", "type": 2}]}]}, {"code": "pickUpProcess", "title": "取车流程", "items": [{"code": "pickUpProcessPickWay", "title": "店员收费送车上门", "desc": "静安寺-地铁站", "underButton": {"title": "地图及指引"}}, {"code": "clerkVerification", "title": "店员核验证件", "desc": "取车前，店员需要对您的身份证件及驾驶证件进行拍照核验，确认有效并为本人持有。核验通过才可取车。", "underButton": {"title": "继续认证", "description": "在线认证驾驶员，取车免核验"}}, {"code": "testVehicle", "title": "店员检验车辆", "desc": "拍照记录仪表盘油量及里程，检验车身外部、座椅、玻璃、轮胎是否有划痕或凹陷。如有请拍照记录，并要求店员在验车单上记录"}, {"code": "pickUpSignContract", "title": "签署合同", "desc": "合同签署后，取车完成"}]}]}, {"groupTitle": "driving", "guidItemList": [{"code": "driving", "title": "驾驶注意事项", "items": [{"code": "cityLimit", "title": "城市限行政策", "button": {"title": "详情"}, "desc": "上海工作日外牌车辆部分道路分时段限行"}, {"code": "mileageLimit", "title": "里程限制", "contentObject": [{"stringObjs": [{"content": "租期内里程数限制信息，请以门店告知为准。"}]}]}]}, {"code": "accidentHandling", "title": "发生事故怎么办", "items": [{"code": "accidentHandlingSon", "title": "事故处理流程", "button": {"title": "查看详情", "description": "", "statusType": 0, "actionUrl": "", "icon": "", "type": 38}, "linkContent": [{"title": "立即联系门店及交通警察", "desc": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店已获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。\n注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"title": "拍照并留存记录信息", "desc": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"title": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"title": "请您配合交警处理完事故后，方可离开事故现场"}, {"title": "准备材料，进行保险理赔流程"}], "desc": "发生事故时，请及时联系门店及交通警察"}]}]}, {"groupTitle": "after", "guidItemList": [{"code": "return<PERSON><PERSON><PERSON>", "title": "还车准备", "items": [{"code": "fuelOil", "title": "确认油量与取车时一致", "contentObject": [{"contentStyle": "13", "stringObjs": [{"content": "还车时需保持与取车时油量一致"}]}, {"stringObjs": [{"content": "若还车油量少于取车油量，门店将收取油费。"}]}, {"stringObjs": [{"content": "若还车油量多于取车油量，门店将返还多余油费。"}]}, {"stringObjs": [{"content": "油费=格数差/总格数*油箱容量*油价"}]}]}, {"code": "return<PERSON><PERSON><PERSON>", "title": "若提前或延迟还车，请及时与门店联系", "button": {"title": "门店政策"}, "desc": "相关收费标准可查看门店政策", "underButton": {"description": "订单支持在线续租，取车后可操作"}}]}, {"code": "returnProcess", "title": "还车流程", "items": [{"code": "returnWay", "title": "店员收费上门取车", "desc": "静安寺-地铁站", "underButton": {"title": "地图及指引"}}, {"code": "returnTestVehicle", "title": "店员检验车辆", "desc": "车辆停靠指定位置后，等待工作人员检验车辆"}, {"code": "returnSignContract", "title": "签署还车验车单", "desc": "若对车辆核验结果和费用结算无异议，您需在还车验车单上签字，完成还车"}]}]}], "isAddWarningTitle": true}