{"ResponseStatus": {"Timestamp": "2021-05-07 16:45:04", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "9039677794474693147"}, {"Id": "RootMessageId", "Value": "100025527-0a063db3-450104-185030"}]}, "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 14}, "feeInfo": {"payAmount": 78.0, "totalAmount": 543.0, "contents": [{"title": "在线补款后，修改即可生效。修改成功后，退款¥651将在1-5个工作日原路退还，您可在订单详情页查询退款进度。", "code": "gray", "type": 0}, {"title": "修改后订单总额", "code": "bold", "type": 1, "typeDesc": "-¥573"}, {"title": "在线补款", "code": "bold", "type": 1, "typeDesc": "¥78"}, {"title": "修改后退款", "code": "gray", "type": 1, "typeDesc": "¥651"}], "feeInfoList": [{"serviceCode": "1001", "title": "租车费", "changeType": 2, "type": 0, "oldPrice": {"subTitle": "¥415×2天", "amount": 830.0, "price": 415.0, "count": 2}, "newPrice": {"subTitle": "¥430×1天", "amount": 430.0, "price": 430.0, "count": 1}, "fixed": true}, {"serviceCode": "1002", "title": "基础服务费", "changeType": 2, "type": 0, "oldPrice": {"amount": 120.0, "price": 60.0, "count": 2}, "newPrice": {"amount": 60.0, "price": 60.0, "count": 1}, "fixed": true}, {"serviceCode": "1003", "title": "车行手续费", "changeType": 0, "type": 0, "oldPrice": {"amount": 25.0, "price": 25.0, "count": 1}, "newPrice": {"amount": 25.0, "price": 25.0, "count": 1}, "fixed": true}, {"serviceCode": "2000896", "title": "人身及财物险", "changeType": 2, "type": 0, "oldPrice": {"subTitle": "修改成功后自动退", "amount": 156.0, "price": 156.0, "count": 1}, "newPrice": {"subTitle": "重新预订费", "amount": 78.0, "price": 78.0, "count": 1}, "fixed": false}, {"serviceCode": "coupon_jtfvifdjyh", "title": "优惠券", "subTitle": "修改订单zj立减券", "changeType": 2, "type": 2, "oldPrice": {"subTitle": "无"}, "newPrice": {"amount": 50}, "fixed": true}, {"serviceCode": "total", "title": "订单总额", "subTitle": "在线支付", "fieldStyle": "b", "changeType": 2, "type": 4, "oldPrice": {"amount": 1116.0}, "newPrice": {"amount": 543.0}, "fixed": true}]}}