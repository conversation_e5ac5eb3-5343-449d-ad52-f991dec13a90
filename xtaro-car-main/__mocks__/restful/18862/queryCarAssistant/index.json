{"ResponseStatus": {"Timestamp": "2021-11-15 17:51:10", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "2137428575176689312"}, {"Id": "RootMessageId", "Value": "921822-0a19ad5a-454713-3703148"}]}, "resultCode": 1, "resultMessage": "success", "carAssistantInfo": {"safeRentList": [{"commonItem": {"code": "", "title": "2021-11-17 21:30 还车", "subTitles": [{"title": "导航回还车门店还车地址:尚美科技大厦", "url": ""}], "button": {"title": "地图 · 指引", "statusType": 1, "type": 21}}}, {"commonItem": {"code": "", "title": "门店实时还车服务进度", "subTitles": [], "button": {"title": "用车指导", "actionUrl": "", "type": 101}}, "safeRentProcess": true, "safeInfo": {"licenseNo": "粤A88669", "color": "白色", "sendPersonName": "lixubin", "sendPersonPhone": "15999999999", "storePhone": "13489881541", "processList": [{"code": "", "title": "已分配取车员", "subTitles": [{"title": "lixubin,15999999999", "url": ""}], "button": {"title": "联系取车员", "actionUrl": "15999999999", "type": 100}, "status": 2}, {"code": "", "title": "上门取车", "subTitles": [], "status": 1}]}}, {"commonItem": {"code": "", "title": "提前还车", "subTitles": [{"title": "提前还车请联系门店，若门店同意退款，款项将原路退还，预计5个工作日内到账。", "url": ""}], "button": {"title": "费用标准", "actionUrl": "", "type": 36}}, "safeRentProcess": false}, {"commonItem": {"code": "", "title": "燃油说明：等量取还", "subTitles": [{"title": "还车时咨询门店，若可退费，则款项预计还车后5个工作日内退回携程钱包", "url": ""}], "button": {"title": "费用标准", "actionUrl": "", "type": 37}}, "safeRentProcess": false}, {"commonItem": {"code": "", "title": "事故处理", "subTitles": [{"title": "行程中如遇问题，请及时致电车行或门店，并拍照记录。", "url": ""}], "linkContent": [{"title": "立即联系门店及交通警察", "desc": "若车辆发生事故，请您注意人身安全，并拨打122交通事故报警电话（上海地区请拨打110），同时请立即致电车行或门店已获得保险方面的咨询和建议，车辆需由保险公司定损后再修理。注：无论是本车与第三方发生事故，还是本车单独发生事故等，只要发生车损，都需要走车辆事故处理流程，否则无法获取保险理赔。"}, {"title": "拍照并留存记录信息", "desc": "请及时对车辆进行拍照并留存记录信息，包含不仅限于车牌号、车辆品牌、颜色、司机电话、肇事地点及车辆损伤情况等。"}, {"title": "请您明确涉事方事故责任，或等待交警抵达现场进行处理"}, {"title": "请您配合交警处理完事故后，方可离开事故现场"}, {"title": "准备材料，进行保险理赔流程"}], "button": {"title": "处理流程", "description": "", "statusType": 0, "actionUrl": "", "icon": "", "type": 38}}}]}, "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 563}}