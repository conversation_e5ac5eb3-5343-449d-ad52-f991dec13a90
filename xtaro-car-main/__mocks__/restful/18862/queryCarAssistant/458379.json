{"resultCode": 1, "resultMessage": "success", "carAssistantInfo": {"guidInfoGroupList": [{"isDefault": true, "groupTitle": "出发前准备", "groupSubTitle": "证件一览，取车指引", "guidItemList": [{"code": "PickupCertificate", "title": "提车凭证及材料一览", "subTitle": "证件快速办理入口", "button": {"title": "查看详情", "description": "", "statusType": 1, "actionUrl": "OsdRentalCarMaterial", "icon": "", "type": 2}, "desc": "", "jsonData": ""}, {"code": "PickupGuid", "title": "取车", "subTitle": "机场外", "button": {"title": "取车指引", "description": "", "statusType": 1, "actionUrl": "StoreInfo?fromurl=orderindex&ispick=0&id=26475743051&isEasyLife=0", "icon": "", "type": 2}, "desc": "（当地时间：2023-12-01 10:00:00）", "jsonData": ""}, {"code": "PickupStoreService", "title": "门店服务", "subTitle": "电子提车凭证", "button": {"title": "门店详情", "description": "", "statusType": 1, "actionUrl": "StoreInfo?fromurl=orderindex&ispick=0&id=26475743051&isEasyLife=0&anchor=true", "icon": "", "type": 2}, "desc": "", "jsonData": ""}]}, {"isDefault": false, "groupTitle": "行中帮助", "groupSubTitle": "快速缴费，保险理赔", "guidItemList": [{"code": "ReturnGuid", "title": "还车", "subTitle": "机场外", "button": {"title": "还车指引", "description": "", "statusType": 1, "actionUrl": "StoreInfo?fromurl=orderindex&ispick=1&id=26475743051&isEasyLife=0", "icon": "", "type": 2}, "desc": "（当地时间：2023-12-02 10:00:00）", "jsonData": ""}]}, {"isDefault": false, "groupTitle": "还车帮助", "groupSubTitle": "还车指引，我要点评", "guidItemList": [{"code": "Invoice", "title": " 报销凭证", "subTitle": "仅支持提供电子ITINERARY作为租车报销凭证 ", "button": {"title": "查看凭证", "description": "请在用车完成后点击", "statusType": 1, "actionUrl": "", "icon": "", "type": 1}, "desc": "", "jsonData": ""}]}]}, "tipMsg": "取车 2023-12-01 10:00(当地时间)", "dropOffMsg": "还车相关信息", "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 147}}