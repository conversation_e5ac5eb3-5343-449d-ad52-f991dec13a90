{"ResponseStatus": {"Timestamp": "2021-06-11 12:17:58", "Ack": "Success", "Errors": [], "Extension": [{"Id": "CLOGGING_TRACE_ID", "Value": "7296449316072144595"}, {"Id": "RootMessageId", "Value": "100025527-0a3d532e-450940-4251060"}]}, "baseResponse": {"isSuccess": true, "code": "unknown", "returnMsg": "success", "requestId": "", "cost": 2887}, "resultType": 1, "resultInfo": {"groupCode": "1", "title": "修改订单提醒", "msg": "依据车行政策，当前已不支持修改。如仍需修改，您可重新预定。新订单确认后，当前订单将自动取消并退款。", "modifyFeeMsg": "（取消费用预计¥40，实际扣除费用以最终结果为准。）"}, "info": {"pickUpLocation": {"name": "凤凰国际机场T1航站楼", "latitude": 18.30747, "longitude": 109.41201, "cityId": 43, "cityName": "三亚", "areaType": 1, "time": "2021-06-20 16:30:00", "bjTime": "2021-06-20 16:30:00"}, "returnLocation": {"name": "凤凰国际机场T1航站楼", "latitude": 18.30747, "longitude": 109.41201, "cityId": 43, "cityName": "三亚", "areaType": 1, "time": "2021-06-23 16:30:00", "bjTime": "2021-06-23 16:30:00"}, "driver": {"name": "TEST", "certificateType": 1, "certificateNumber": "", "mobile": "13788889999"}}, "comPriceCode": "1541897920974cac373-2033-42cc-b454-75a87bce3484", "priceCode": "e3734b5f09674fac972c5dfec411810d", "feeInfo": {"payAmount": 78.0, "totalAmount": 274.0, "contents": [{"title": "在线补款后，修改即可生效。修改成功后，退款¥52将在1-5个工作日原路退还，您可在订单详情页查询退款进度。", "code": "gray", "type": 0}, {"title": "修改后订单总额", "code": "bold", "type": 1, "typeDesc": "+¥26"}, {"title": "在线补款", "code": "bold", "type": 1, "typeDesc": "¥78"}, {"title": "修改后退款", "code": "gray", "type": 1, "typeDesc": "¥52"}], "feeInfoList": [{"serviceCode": "1001", "title": "租车费", "changeType": 1, "type": 0, "oldPrice": {"subTitle": "¥48×2天", "amount": 96.0, "price": 48.0, "count": 2, "unit": "份"}, "newPrice": {"subTitle": "¥48×3天", "amount": 144.0, "price": 48.0, "count": 3, "unit": "份"}, "fixed": true}, {"serviceCode": "1002", "title": "基础服务费", "changeType": 1, "type": 0, "oldPrice": {"amount": 80.0, "price": 40.0, "count": 2, "unit": "份"}, "newPrice": {"amount": 120.0, "price": 40.0, "count": 3, "unit": "份"}, "fixed": true}, {"serviceCode": "1003", "title": "车行手续费", "changeType": 0, "type": 0, "oldPrice": {"amount": 20.0, "price": 20.0, "count": 1, "unit": "份"}, "newPrice": {"amount": 20.0, "price": 20.0, "count": 1, "unit": "份"}, "fixed": true}, {"serviceCode": "2000896", "title": "人身及财物险", "changeType": 2, "type": 0, "oldPrice": {"subTitle": "修改成功后自动退", "amount": 52.0, "price": 52.0, "count": 1, "unit": ""}, "newPrice": {"subTitle": "无"}, "fixed": false}, {"serviceCode": "coupon_mmjpslxflv", "title": "优惠券", "subTitle": "国内租车满减券", "changeType": 2, "type": 2, "oldPrice": {"subTitle": "无"}, "newPrice": {"amount": 10}, "fixed": true}, {"serviceCode": "total", "title": "订单总额", "subTitle": "在线支付", "fieldStyle": "b", "changeType": 1, "type": 4, "oldPrice": {"amount": 248.0}, "newPrice": {"amount": 274.0}, "fixed": true}]}, "productInfo": {"explain": "修改取还车时间后，已购买的人身及财物险将失效并退款，您可在此重新加购。", "products": [{"name": "人身及财物险", "code": "2000896", "title": "人身及财物险", "description": ["人身意外伤害每人可赔付20万，医疗费用每人可赔付3万元。新冠确诊津贴", "个人财物损失赔付2000元", "提供专业道路救援服务"], "targetTag": [{"title": "仅支持线上购买"}], "specificName": 0, "sourceFrom": 2, "price": 78.0, "localCurrencyCode": "CNY", "localTotalPrice": 78.0, "localDailyPrice": 78.0, "currentTotalPrice": 78.0, "currentDailyPrice": 78.0, "currentCurrencyCode": "CNY", "quantity": 1, "quantityName": "", "maxQuantity": 1, "group": 1, "status": 0, "canUpgrade": false, "extDesc": "", "toDetailStatus": 0, "insBottomDesc": {"title": "以下情况无法为您提供保障服务：", "desc": ["发生事故时未及时通知租车公司或未申报保险", "无事故证明材料或无。保险理赔材料", "无证驾驶、酒驾、超速等其他保险公司不予理赔或责任免除的场景"]}, "insAndXProductLabelInfos": [{"sort": "1", "name": "携程提供", "color": "0"}]}]}, "cancelRules": [{"free": 1, "title": "免费取消", "context": "取车时间前", "time": "2021-06-20 16:30前"}, {"free": 0, "title": "扣订单全额", "context": "取车时间后", "time": "2021-06-20 16:30后"}], "cancelExplain": "修改取还车信息后，请留意取消政策的更新。", "pageInfo": {"type": 2, "title": "请确认已修改的取还车信息", "freeDeposit": false}, "discountInfo": {"explain": "请留意可使用的优惠更新。", "activityDetail": {"title": "周租优惠", "status": 1, "promotion": {"title": "周租优惠", "description": "减免30%", "deductionAmount": 101, "payofftype": 2, "code": "PMS_SAT15791019124", "isFromCtrip": false, "selected": true, "promotionId": 44074, "discountType": 0}}, "couponList": {"usableCoupons": [{"title": "立减券", "description": "", "couponDesc": "本优惠券通过携程国内自驾租车频道，以预付方式（在线支付）预订国内租车产品可享受租车费9折优惠，租期满7天及以上可用（最高减300元）；优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用； 该优惠券可与带有“暑期特惠”标签的产品优惠叠加使用，不可与其他优惠同享； 优惠券仅限2021年6月15日-8月31日期间预订取、还车时间均在2021年7月1日-8月31日期间的订单可用；部分供应商及车型不可用券，具体以列表页预订展示为准； 有效期内每个用户限领取两张优惠券，每张优惠券限用一次； 领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；", "deductionAmount": 34, "payofftype": 2, "payoffName": "立减券", "code": "yhbzajgngb", "isFromCtrip": true, "isEnabled": true, "actionedDate": "2021-06-09 00:00", "expiredDate": "2021-08-31 23:59", "selected": true, "promotionId": 932852480, "couponName": "国内租车立减券", "unitName": "¥", "unionType": 0, "couponType": 2}, {"title": "立减券", "description": "", "couponDesc": "本优惠券通过携程国内自驾租车频道，以预付方式（在线支付）预订国内租车产品可享受租车费94折优惠，租期满4天及以上可用（最高减200元）；优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用； 该优惠券可与带有“暑期特惠”标签的产品优惠叠加使用，不可与其他优惠同享； 优惠券仅限2021年6月15日-8月31日期间预订取、还车时间均在2021年7月1日-8月31日期间的订单可用；部分供应商及车型不可用券，具体以列表页预订展示为准； 有效期内每个用户限领取两张优惠券，每张优惠券限用一次； 领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；", "deductionAmount": 21, "payofftype": 2, "payoffName": "立减券", "code": "ymeyyqhhlm", "isFromCtrip": true, "isEnabled": true, "actionedDate": "2021-06-09 00:00", "expiredDate": "2021-08-31 23:59", "selected": false, "promotionId": 360014238, "couponName": "国内租车立减券", "unitName": "¥", "unionType": 0, "couponType": 2}], "unusableCoupons": [{"title": "立减券", "description": "", "couponDesc": "本优惠券通过携程国内自驾租车频道，以预付方式（在线支付）预订国内租车产品可享受租车费85折优惠，租期满10天及以上可用（最高减500元）；优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用； 该优惠券可与带有“暑期特惠”标签的产品优惠叠加使用，不可与其他优惠同享； 优惠券仅限2021年6月15日-8月31日期间预订取、还车时间均在2021年7月1日-8月31日期间的订单可用；部分供应商及车型不可用券，具体以列表页预订展示为准； 有效期内每个用户限领取两张优惠券，每张优惠券限用一次； 领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；", "deductionAmount": 51, "payofftype": 2, "payoffName": "立减券", "code": "mtdwicdmvy", "isFromCtrip": true, "isEnabled": false, "actionedDate": "2021-06-09 00:00", "expiredDate": "2021-08-31 23:59", "extDesc": "租期未满240小时", "selected": false, "promotionId": 661893099, "couponName": "国内租车立减券", "unitName": "¥", "unionType": 0, "couponType": 2}, {"title": "立减券", "description": "", "couponDesc": "本优惠券通过携程国内自驾租车频道，以预付方式（在线支付）预订国内租车产品可享受租车费8折优惠，租期满14天及以上可用（最高减1000元）；优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用； 该优惠券可与带有“暑期特惠”标签的产品优惠叠加使用，不可与其他优惠同享； 优惠券仅限2021年6月15日-8月31日期间预订取、还车时间均在2021年7月1日-8月31日期间的订单可用；部分供应商及车型不可用券，具体以列表页预订展示为准； 有效期内每个用户限领取两张优惠券，每张优惠券限用一次； 领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；", "deductionAmount": 68, "payofftype": 2, "payoffName": "立减券", "code": "lppcnyfegs", "isFromCtrip": true, "isEnabled": false, "actionedDate": "2021-06-09 00:00", "expiredDate": "2021-08-31 23:59", "extDesc": "租期未满336小时", "selected": false, "promotionId": 718564651, "couponName": "国内租车立减券", "unitName": "¥", "unionType": 0, "couponType": 2}], "selectedCoupon": {"title": "立减券", "description": "", "couponDesc": "本优惠券通过携程国内自驾租车频道，以预付方式（在线支付）预订国内租车产品可享受租车费9折优惠，租期满7天及以上可用（最高减300元）；优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用； 该优惠券可与带有“暑期特惠”标签的产品优惠叠加使用，不可与其他优惠同享； 优惠券仅限2021年6月15日-8月31日期间预订取、还车时间均在2021年7月1日-8月31日期间的订单可用；部分供应商及车型不可用券，具体以列表页预订展示为准； 有效期内每个用户限领取两张优惠券，每张优惠券限用一次； 领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；", "deductionAmount": 34, "payofftype": 2, "payoffName": "立减券", "code": "yhbzajgngb", "isFromCtrip": true, "isEnabled": true, "actionedDate": "2021-06-09 00:00", "expiredDate": "2021-08-31 23:59", "selected": true, "promotionId": 932852480, "couponName": "国内租车立减券", "unitName": "¥", "unionType": 0, "couponType": 2}, "status": 1, "title": ""}}, "payDeadline": "2021-07-16 19:30:00"}