{"responseStatus": {"timestamp": "/Date(1592819710168+0800)/", "ack": "Success", "errors": [{"message": "String", "errorCode": "String", "stackTrace": "String", "severityCode": "Error", "errorFields": [{"fieldName": "String", "errorCode": "String", "message": "String"}], "errorClassification": "ServiceError"}], "build": "String", "version": "String", "extension": [{"id": "String", "version": "String", "contentType": "String", "value": "String"}]}, "baseResponse": {"isSuccess": true, "code": "1", "returnMsg": "success", "requestId": "String", "cost": 100}, "totalSize": 2, "totalAmount": 500, "toPayCount": 1, "toPayAmount": 300, "reason": "String", "payedCount": 1, "payedAmount": 200, "currency": "CNY", "additionalPaymentList": [{"orderId": 123456789, "amount": 300, "reasonCode": 0, "remark": "String", "payStatus": 1, "additionalPaymentId": 123566, "payTime": 215646, "bizScene": 1, "createTime": 23465464, "vehicleDamageLst": [{"carModelName": "String", "carNo": "String", "occurrenceTime": "String", "totalAmount": 0, "expenseDetailLst": [{"expenseType": 0, "expenseName": "String", "expenseAmount": 0}], "imgLst": ["String"]}], "violationLst": [{"carModelName": "违章", "carNo": " 沪145256", "occurrenceTime": "2020-06-22 18:00:00", "location": " 上海市 上海市长宁区", "behavior": "闯红灯", "penaltyPoint": 6, "penaltyAmount": 100, "payStatus": 0, "imgLst": ["String"]}]}, {"orderId": 123456789, "amount": 200, "reasonCode": 0, "remark": "String", "payStatus": 1, "additionalPaymentId": 123566, "payTime": 215646, "bizScene": 1, "createTime": 23465464, "vehicleDamageLst": [{"carModelName": "String", "carNo": "String", "occurrenceTime": "String", "totalAmount": 0, "expenseDetailLst": [{"expenseType": 1, "expenseName": "维修费", "expenseAmount": 100}, {"expenseType": 2, "expenseName": " 停运 ", "expenseAmount": 100}], "imgLst": ["String"]}], "violationLst": [{"carModelName": "String", "carNo": "String", "occurrenceTime": "String", "location": "String", "behavior": "String", "penaltyPoint": 0, "penaltyAmount": 0, "payStatus": 0, "imgLst": ["String"]}]}]}