const fs = require('fs');
const path = require('path');

function traverseDirectory(dir) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // 如果是文件夹，则递归遍历
      traverseDirectory(filePath);
    } else {
      // 如果是文件，则处理文件
      if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
        addTsNoCheck(filePath);
      }
    }
  });
}

function addTsNoCheck(filePath) {
  const fileContent = fs.readFileSync(filePath, 'utf8');
  const modifiedContent = `// @ts-nocheck\n${fileContent}`;

  fs.writeFileSync(filePath, modifiedContent);
}


// 忽略npm包TS类型
traverseDirectory('node_modules/@ctrip/xtaro-crn');
traverseDirectory('node_modules/@ctrip/xtaro-car-library-crn');

console.log('忽略npm包ts类型✅')
