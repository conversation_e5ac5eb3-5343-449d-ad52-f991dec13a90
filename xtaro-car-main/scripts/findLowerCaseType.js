const fs = require('fs');
const path = require('path');
const glob = require('glob');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const t = require('@babel/types');
const generator = require('@babel/generator').default;
const { exec } = require('child_process');
// 获取所有 ts 和 tsx 文件
let pascalCaseCount = 0;
let autoFixPascalCaseCount = 0;
const srcDir = './src/pages/xcar/';
const files = glob.sync(path.join(srcDir, '**/*.{ts,tsx}'), {
  nodir: true,
  ignore: path.join(srcDir, '**/*.d.ts')
});

files.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const ast = parser.parse(content, {
    sourceType: 'module',
    plugins: [
      'typescript',
      'jsx',
      'classProperties',
      'dynamicImport',
      'decorators',
    ]
  });
  let modified = false;
  const typeRenameMap = new Map();
  traverse(ast, {
    TSInterfaceDeclaration(path) {
      const id = path.node.id;
      if (id && id.name && id.name[0] === id.name[0].toLowerCase()) {
        pascalCaseCount += 1;
        const isExported = path.parent.type === 'ExportNamedDeclaration';
        if (!isExported) {
          const newName = id.name.charAt(0).toUpperCase() + id.name.slice(1);
          typeRenameMap.set(id.name, newName);
          path.node.id.name = newName;
          modified = true;
          autoFixPascalCaseCount += 1;
        } else {
          console.log(`Found TypeAlias with lowercase name: ${id.name} in file: ${file}`);
        }
      }
    },
    TSTypeAliasDeclaration(path) {
      const id = path.node.id;
      if (id && id.name && id.name[0] === id.name[0].toLowerCase()) {

        pascalCaseCount += 1;
        const isExported = path.parent.type === 'ExportNamedDeclaration';
        if (!isExported) {
          const newName = id.name.charAt(0).toUpperCase() + id.name.slice(1);
          typeRenameMap.set(id.name, newName);
          path.node.id.name = newName;
          modified = true;
          autoFixPascalCaseCount += 1;
        } else {
          console.log(`Found TypeAlias with lowercase name: ${id.name} in file: ${file}`);
        }
      }
    },


  });
  traverse(ast, {
    TSExpressionWithTypeArguments(path) {
      const id = path.node;
      if (id && id.expression && typeRenameMap.has(id.expression.name)) {
        const newName = typeRenameMap.get(id.expression.name);
        if (id.expression.name !== newName) {
          path.node.expression.name = newName;
          modified = true;
        } else {
          console.log(`替换异常 ${id.expression} in file: ${file}`);
        }
      }
    },
    TSTypeReference(path) {
      const id = path.node;
      if (id && id.typeName && typeRenameMap.has(id.typeName.name)) {
        const newName = typeRenameMap.get(id.typeName.name);
        if (id.typeName.name !== newName) {
          path.node.typeName.name = newName;
          modified = true;
        } else {
          console.log(`替换异常 ${id.typeName} in file: ${file}`);
        }
      }
    },

  });
  if (modified) {
    const newCode = generator(ast, { retainLines: true }, content).code;
    fs.writeFileSync(file, newCode, 'utf8');
    exec(`npx prettier --write ${file}`);
    console.log(`Modified file: ${file}`);
  }
});
console.log(`类型首字母小写个数：${pascalCaseCount}`, `自动修复个数：${autoFixPascalCaseCount}`);