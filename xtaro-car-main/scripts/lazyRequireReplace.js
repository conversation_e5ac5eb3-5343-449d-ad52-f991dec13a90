const path = require('path');
const replace = require('./addIndexModule');
const { recursion } = require('./utils');

const indexRoots = [
  '../src/taro/pages/xcar/ComponentBusiness/Tips/index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/HeaderAd/index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/HomeAD/index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/SearchPanel/index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/Common/index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/SesameCard/index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/DepositIntroduceModal/Index.ts',
  '../src/taro/pages/xcar/ComponentBusiness/ServiceAdmition/Index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/VehicleSupplement/Index.tsx',
  '../src/taro/pages/xcar/Constants/PageId/Index.ts',
  '../src/taro/pages/xcar/Pages/Home/Components/Index.tsx',
  '../src/taro/pages/xcar/Pages/OrderDetail/Components/Index.tsx',
  '../src/taro/pages/xcar/Pages/OrderDetail/Containers/Index.ts',
  '../src/taro/pages/xcar/ComponentBusiness/OrderConfirmModal/Index.ts',
  '../src/taro/pages/xcar/Pages/Product/Components/Index.tsx',
  '../src/taro/pages/xcar/Constants/TextIndex.ts',
  '../src/taro/pages/xcar/Components/Index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/Payment/index.tsx',
  '../src/taro/pages/xcar/Helpers/Index.ts',
  '../src/taro/pages/xcar/Components/ThirdNpm/Index.ts',
  '../src/taro/pages/xcar/Pages/Home/Logic/Index.ts',
  '../src/taro/pages/xcar/Types/Index.ts',
  '../src/taro/pages/xcar/ComponentBusiness/SesameCard/index.tsx',
  '../src/taro/pages/xcar/Containers/ListIndex.ts',
  '../src/taro/pages/xcar/Containers/VendorListIndex.ts',
  '../src/taro/pages/xcar/Pages/VendorList/Components/Index.tsx',
  '../src/taro/pages/xcar/Pages/List/Components/Index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/PackageIncludes/index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/VendorListEnter/index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/CarPriceDescribe/index.tsx',
  '../src/taro/pages/xcar/ComponentBusiness/CouponEntry/index.tsx',
  '../src/taro/pages/xcar/Pages/Rerent/Components/Index.tsx',
  '../src/taro/pages/xcar/State/SelectorIndex.ts',
  '../src/taro/pages/xcar/Constants/Index.ts',
  '../src/taro/pages/xcar/Global/Cache/Index.ts',
  '../src/taro/pages/xcar/Util/Index.ts',
];

const lazyRequireReplace = (file, match) => {
  if (
    file.includes('/index.') ||
    file.includes('/Index.') ||
    file.includes('/TextIndex.') ||
    file.includes('/ListIndex.') ||
    file.includes('/VendorListIndex.') ||
    file.includes('/SelectorIndex.')
  ) {
    let success = true;
    try {
      success = replace({
        indexPath: file,
        outPath: file,
      });
    } catch (e) {
      console.log(`lazyRequireReplace file error: ${file}`, e);
      success = false;
    }
    if (success) {
      console.log(`lazyRequireReplace file: ${file}`);
    }
  }
};

indexRoots.map(root => {
  lazyRequireReplace(path.resolve(__dirname, root));
  // recursion(path.resolve(__dirname, root), (file) => lazyRequireReplace(file, '/index.'));
});

// recursion(path.resolve(__dirname, '../build'), (file) => lazyRequireReplace(file, '/Index.'));
