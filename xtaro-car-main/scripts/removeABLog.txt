请输入AB实验相关的函数表达式，例如 GetAB.isISDInterestPoints 
 
 
你输入的函数表达式是：GetABCache.isLicenseApproveCVer 
 
 
请输入AB实验相关的函数表达式的值，B 代表true 其它为false 
 
 
你输入的函数表达式的值是：false 
 
 

开始下线AB实验相关代码，GetABCache.isLicenseApproveCVer函数相关，取值为false 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Components/Business/BookingFormOsd.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 VariableDeclarator表达式，start： 6116  before： const isLicenseApproveCVer = GetABCache.isLicenseApproveCVer();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 6455  before： isLicenseApproveCVer &&
    !driverLicenseName?.isEnable  after： GetABCache.isLicenseApproveCVer() &&
    !driverLicenseName?.isEnable
 
 
2、成功，start： 8291  before： isLicenseApproveCVer && isHasTips  after： GetABCache.isLicenseApproveCVer() && isHasTips
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Components/Business/BookingFormOsd.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 LogicalExpression表达式，start： 6496  before： GetABCache.isLicenseApproveCVer() &&
    !driverLicenseName?.isEnable  after： false
 
 
2、成功 LogicalExpression表达式，start： 8341  before： GetABCache.isLicenseApproveCVer() && isHasTips  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isLicenseApproveCVer(), 替换数量=5 
 
1、成功 LogicalExpression表达式，start： 6455  before： false &&
    driverLicenseName?.error  after： GetABCache.isLicenseApproveCVer() &&
    driverLicenseName?.error
 
 
2、成功 LogicalExpression表达式，start： 8240  before： false && (
            <BookingFormTip
              extraCharge={driverFeeDesc}
              driverYear={
                pickUpRequirement?.licenseAgeRequirement?.stringObjs?.[0]
                  .content
              }
              requirements={curDriverLicense?.requirements}
              testID={CarLog.LogExposure({
                name: '曝光_填写页_驾驶员收费政策',
                info: logInfo,
              })}
            />
          )  after： GetABCache.isLicenseApproveCVer() && (
            <BookingFormTip
              extraCharge={driverFeeDesc}
              driverYear={
                pickUpRequirement?.licenseAgeRequirement?.stringObjs?.[0]
                  .content
              }
              requirements={curDriverLicense?.requirements}
              testID={CarLog.LogExposure({
                name: '曝光_填写页_驾驶员收费政策',
                info: logInfo,
              })}
            />
          )
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Components/Business/BookingFormOsd.tsx, 第3次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 LogicalExpression表达式，start： 6496  before： GetABCache.isLicenseApproveCVer() &&
    driverLicenseName?.error  after： false
 
 
2、成功 LogicalExpression表达式，start： 8319  before： GetABCache.isLicenseApproveCVer() && (
            <BookingFormTip
              extraCharge={driverFeeDesc}
              driverYear={
                pickUpRequirement?.licenseAgeRequirement?.stringObjs?.[0]
                  .content
              }
              requirements={curDriverLicense?.requirements}
              testID={CarLog.LogExposure({
                name: '曝光_填写页_驾驶员收费政策',
                info: logInfo,
              })}
            />
          )  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isLicenseApproveCVer(), 替换数量=2 
 
1、成功 LogicalExpression表达式，start： 6455  before： false &&
    Texts.driverlicenseError  after： GetABCache.isLicenseApproveCVer() &&
    Texts.driverlicenseError
 
 
2、成功 JSXExpressionContainer表达式，start： 7382  before： <View testID={testID} className={c2xStyles.block}>
          <FormOsd
            driverInfo={[
              { value: age, type: 'age', error: false },
              ...driverInfo,
            ]}
            onPressDriverLicense={onPressDriverLicense}
            changeFormData={changeFormData}
            passengerInfo={passenger}
            currentCountry={currentCountry}
            sequences={driverSequences}
            isIsd={false}
            isNewStyle={false}
            renderPassenger={
              <PassengerOSDNew
                passenger={passenger}
                label={depositLabel?.title}
                errorTip={driverlicenseError}
                onPress={onPressDriver}
              />
            }
            baseLogInfo={logInfo}
          />

          {/** 驾驶员信息表单提示信息 */}
          {false}
        </View>  after： <View testID={testID} className={c2xStyles.block}>
          <FormOsd
            driverInfo={[
              { value: age, type: 'age', error: false },
              ...driverInfo,
            ]}
            onPressDriverLicense={onPressDriverLicense}
            changeFormData={changeFormData}
            passengerInfo={passenger}
            currentCountry={currentCountry}
            sequences={driverSequences}
            isIsd={false}
            isNewStyle={false}
            renderPassenger={
              <PassengerOSDNew
                passenger={passenger}
                label={depositLabel?.title}
                errorTip={driverlicenseError}
                onPress={onPressDriver}
              />
            }
            baseLogInfo={logInfo}
          />

          {/** 驾驶员信息表单提示信息 */}
          
        </View>
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Components/Business/BookingFormOsd.tsx, 第4次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 LogicalExpression表达式，start： 6496  before： GetABCache.isLicenseApproveCVer() &&
    Texts.driverlicenseError  after： false
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/Booking/Index.tsx, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 LogicalExpression表达式，start： 41599  before： GetABCache.isLicenseApproveCVer() &&
      nextProps.passenger?.nationality !== this.props.passenger?.nationality  after： false
 
 
2、成功 VariableDeclarator表达式，start： 49667  before： const isLicenseApproveCVer = GetABCache.isLicenseApproveCVer();  after： 
 
 
6、成功 VariableDeclarator表达式，start： 83605  before： const isLicenseApproveCVer = GetABCache.isLicenseApproveCVer();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 49596  before： if (isLicenseApproveCVer) {
        sequence = OSD_LICENSE_APPROVE_SEQUENCE;
      } else if (isEasyLife) {
        sequence = OSD_SEQUENCE;
      } else {
        sequence = OSD_NORMAL_SEQUENCE;
      }  after： if (GetABCache.isLicenseApproveCVer()) {
        sequence = OSD_LICENSE_APPROVE_SEQUENCE;
      } else if (isEasyLife) {
        sequence = OSD_SEQUENCE;
      } else {
        sequence = OSD_NORMAL_SEQUENCE;
      }
 
 
3、成功，start： 83457  before： isLicenseApproveCVer
      ? BookingFormOsdContainer
      : BookingFormContainer  after： GetABCache.isLicenseApproveCVer()
      ? BookingFormOsdContainer
      : BookingFormContainer
 
 
4、成功，start： 99059  before： isLicenseApproveCVer && (
              <DriverLicenseModal
                visible={driverLicenseModalVisible}
                passenger={passenger}
                handleClose={this.closeDriverLicenseModal}
                changeFormData={changeFormData}
                driverLicenseItems={driverLicenseItems}
                driverLicenseGeoInfo={curDriverLicense?.geoInfo}
                selectCurDriverLicense={selectCurDriverLicense}
              />
            )  after： GetABCache.isLicenseApproveCVer() && (
              <DriverLicenseModal
                visible={driverLicenseModalVisible}
                passenger={passenger}
                handleClose={this.closeDriverLicenseModal}
                changeFormData={changeFormData}
                driverLicenseItems={driverLicenseItems}
                driverLicenseGeoInfo={curDriverLicense?.geoInfo}
                selectCurDriverLicense={selectCurDriverLicense}
              />
            )
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isLicenseApproveCVer(), 替换数量=2 
 
1、成功 LogicalExpression表达式，start： 32458  before： lodashGet(productRes, 'isSelected') || false  after： lodashGet(productRes, 'isSelected') || GetABCache.isLicenseApproveCVer()
 
 
2、成功 IfStatement表达式，start： 41545  before： if (
      false
    ) {
      this.props.queryLicencePolicy({
        nationalityCode: nextProps.passenger?.nationality,
      });
    }  after： if (
      GetABCache.isLicenseApproveCVer()
    ) {
      this.props.queryLicencePolicy({
        nationalityCode: nextProps.passenger?.nationality,
      });
    }
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/Pages/Booking/Index.tsx, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 LogicalExpression表达式，start： 32458  before： lodashGet(productRes, 'isSelected') || GetABCache.isLicenseApproveCVer()  after： lodashGet(productRes, 'isSelected')
 
 
2、成功 IfStatement表达式，start： 41573  before： if (
      GetABCache.isLicenseApproveCVer()
    ) {
      this.props.queryLicencePolicy({
        nationalityCode: nextProps.passenger?.nationality,
      });
    }  after： 
 
 
3、成功 IfStatement表达式，start： 49652  before： if (GetABCache.isLicenseApproveCVer()) {
        sequence = OSD_LICENSE_APPROVE_SEQUENCE;
      } else if (isEasyLife) {
        sequence = OSD_SEQUENCE;
      } else {
        sequence = OSD_NORMAL_SEQUENCE;
      }  after： if (isEasyLife) {
        sequence = OSD_SEQUENCE;
      } else {
        sequence = OSD_NORMAL_SEQUENCE;
      }
 
 
5、成功 ConditionalExpression表达式，start： 83600  before： GetABCache.isLicenseApproveCVer()
      ? BookingFormOsdContainer
      : BookingFormContainer  after： BookingFormContainer
 
 
6、成功 LogicalExpression表达式，start： 99194  before： GetABCache.isLicenseApproveCVer() && (
              <DriverLicenseModal
                visible={driverLicenseModalVisible}
                passenger={passenger}
                handleClose={this.closeDriverLicenseModal}
                changeFormData={changeFormData}
                driverLicenseItems={driverLicenseItems}
                driverLicenseGeoInfo={curDriverLicense?.geoInfo}
                selectCurDriverLicense={selectCurDriverLicense}
              />
            )  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isLicenseApproveCVer(), 替换数量=1 
 
1、成功 JSXExpressionContainer表达式，start： 95147  before： <>
            {isHasPenalty && (
              <ApplyPenaltyInputModal
                orderId={ctripOrderId}
                setRebookPenalty={setRebookPenalty}
                rebookPenalty={rebookPenalty}
                visible={isShowApplyPenaltyInputModal}
                onMaskPress={this.hideApplyPenaltyInputModal}
                resCancelFee={resCancelFeeRebook}
                headerDom={
                  <RebookTip
                    isHasPenalty={isHasPenalty}
                    isHasReBookPenalty={!!rebookPenalty}
                    isShowApplyPenaltyInputModal={isShowApplyPenaltyInputModal}
                    tip={tipText}
                    showApplyPenaltyInputModal={this.hideApplyPenaltyInputModal}
                  />
                }
              />
            )}
            <PersonalAuthCheckModal
              visible={personalInfoAuthModalVisible}
              onMaskPress={this.hidePersonalInfoAuthModal}
              style={styles.shadow}
              onPressBtn={this.handleBookPress}
              onCheckBarCheck={this.onCheckBarCheck}
              onCheckBarPress={this.showApproveExplainModal}
              buttonName={agreeSubmitName}
              personalInfoCheck={personalInfoChecked}
              showPersonalAuthCheck={remoteQConfig?.personalInfoAuthCheck}
            />

            <ApproveExplainModal
              visible={approveExplainModalVisible}
              onMaskPress={this.hideApproveExplainModal}
              style={styles.shadow}
              data={approveExplain}
              onPressBtn={this.getApproveExplainModalBtnFn()}
              hasFooterBtn={remoteQConfig?.personalInfoAuthCheck}
              buttonName={
                (personalInfoChecked || personalInfoAuthModalVisible) &&
                agreeSubmitName
              }
            />

            <HalfPageModal
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...this.getActivityNoteModalProps()}
            />

            {Utils.isCtripIsd() && (
              <ProductConfirmModalNew
                page={this}
                visible={productConfirmModalVisible}
                anchor={productConfirmAnchor}
                showFooter={false}
                onPressEasyLife={this.showEasyLifeModal}
                onClose={this.closeProductConfirmModal}
                onPressOptimize={this.showOptimizeStoreModal}
                showOptimizationStrengthenModal={
                  this.openOptimizationStrengthenModal
                }
                isSecretBox={isSecretBox}
                pageName="Booking"
              />
            )}
            {Utils.isCtripIsd() && (
              <ServiceClaimMoreModal
                visible={serviceClaimMoreVisible}
                onCancel={this.hideServiceClaimMore}
                data={rentalGuaranteeV2?.vendorServiceDetail}
              />
            )}
            {Utils.isCtripIsd() && (
              <CarServiceDetailModal
                visible={carServiceDetailVisible}
                onCancel={this.hideCarServiceDetail}
                data={rentalGuaranteeV2?.packageDetailList}
                purchasingNotice={rentalGuaranteeV2?.purchasingNotice}
                ptime={ptime}
                rtime={rtime}
                selectedInsuranceIds={addOnCodes}
                changeSelectInsurance={changeSelectInsurance}
                fromPage={CarServiceFromPageTypes.booking}
                showServiceDetailCode={showServiceDetailCode}
                haveFooter={!isEasyLife2024}
              />
            )}
            {false}
            <BookingEasyLifeModal />
            <BookingPriceDetail
              visible={priceDetailModalVisible}
              onClose={this.closePriceDetailModal}
              onContinue={this.closePriceDetailModal}
              style={styles.priceDetailStyle}
              role={PageRole.BOOKING}
            />

            {Utils.isCtripOsd() && (
              <PriceDetailModalOsd
                visible={osdPriceDetailModalVisible}
                role={PageRole.BOOKING}
                data={getFeeDetailData()}
                onPressBtn={this.handleBookPress}
                onClose={this.closeOsdPriceDetailModal}
              />
            )}
            <SesameContainer />
            {isShowModifyOrderModal && (
              // eslint-disable-next-line react/jsx-props-no-spreading
              <ModifyOrderModal {...this.getModifyOrderModalData()} />
            )}
            <StoreModal
              onMaskPress={this.hideOptimizeStoreModal}
              visible={isShowOptimizeStoreModal}
            />

            <EhiFreeDepositRuleModalContainer isBooking={true} />

            {/* 境外免押规则弹层 */}
            {Utils.isCtripOsd() && <FreeDepositRuleModalContainer />}

            <CancelZhimaModal />
            {Utils.isCtripIsd() && (
              <PriceAlert
                visible={isShowPriceConfirm}
                priceAlertHandler={this.priceAlertHandler}
                popToVendorList={this.popToVendorList}
              />
            )}
            {Utils.isCtripIsd() && <PickupDownGradePop />}
            <VendorListOptimizationStrengthenModal
              visible={optimizationStrengthenModalVisible}
              onCancel={this.closeOptimizationStrengthenModal}
              isSecretBox={isSecretBox}
              pageName="Booking"
            />

            {Utils.isCtripIsd() && !isHasEtcIntroModal && (
              <EtcIntroModalContainer
                visible={isEtcIntroModalVisible}
                onClose={this.handleEtcIntroModalClose}
              />
            )}
            <BookingCreateInsLoading />
            <BookingCreateInsFailPop
              onBack={this.handleCreateInsFailPopLeftButton}
              onPay={this.handleCreateInsFailPopRightButton}
            />

            <AddInstructModal
              pageModalProps={pageModalProps}
              addInstructData={addInstructData}
            />

            {Utils.isCtripOsd() && <DepositRateDescriptionModal />}
            {Utils.isCtripOsd() && isKlb && (
              <FlightDelayRulesModal
                testID={CarLog.LogExposure({
                  name: '曝光_填写页_航班延误政策_曝光弹层',

                  info: logBaseInfo,
                })}
              />
            )}
            {Utils.isCtripIsd() && (
              <BookingConfirmModal
                visible={showConfirm}
                onConfirm={this.closeBookingConfirmModal}
                onCancel={this.closeBookingConfirmModalWithPop}
              />
            )}
            {Utils.isCtripIsd() && (
              <BusinessLicenseModal
                visible={isBusinessLicenseModalVisible}
                onClose={this.handleBusinessLicenseClose}
              />
            )}
            {Utils.isCtripOsd() && (
              <FuelDescriptionModalContainer
                visible={isFuelDescriptionModalVisible}
                onClose={this.handleFuelDescClose}
              />
            )}
            {Utils.isCtripOsd() && (
              <LocalContactsModal
                visible={localContactsModalVisible}
                onClose={this.hideLocalContactsModal}
                onSelectContact={this.onSelectContactType}
                pickUpAreaCode={pickUpAreaCode}
              />
            )}
          </>  after： <>
            {isHasPenalty && (
              <ApplyPenaltyInputModal
                orderId={ctripOrderId}
                setRebookPenalty={setRebookPenalty}
                rebookPenalty={rebookPenalty}
                visible={isShowApplyPenaltyInputModal}
                onMaskPress={this.hideApplyPenaltyInputModal}
                resCancelFee={resCancelFeeRebook}
                headerDom={
                  <RebookTip
                    isHasPenalty={isHasPenalty}
                    isHasReBookPenalty={!!rebookPenalty}
                    isShowApplyPenaltyInputModal={isShowApplyPenaltyInputModal}
                    tip={tipText}
                    showApplyPenaltyInputModal={this.hideApplyPenaltyInputModal}
                  />
                }
              />
            )}
            <PersonalAuthCheckModal
              visible={personalInfoAuthModalVisible}
              onMaskPress={this.hidePersonalInfoAuthModal}
              style={styles.shadow}
              onPressBtn={this.handleBookPress}
              onCheckBarCheck={this.onCheckBarCheck}
              onCheckBarPress={this.showApproveExplainModal}
              buttonName={agreeSubmitName}
              personalInfoCheck={personalInfoChecked}
              showPersonalAuthCheck={remoteQConfig?.personalInfoAuthCheck}
            />

            <ApproveExplainModal
              visible={approveExplainModalVisible}
              onMaskPress={this.hideApproveExplainModal}
              style={styles.shadow}
              data={approveExplain}
              onPressBtn={this.getApproveExplainModalBtnFn()}
              hasFooterBtn={remoteQConfig?.personalInfoAuthCheck}
              buttonName={
                (personalInfoChecked || personalInfoAuthModalVisible) &&
                agreeSubmitName
              }
            />

            <HalfPageModal
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...this.getActivityNoteModalProps()}
            />

            {Utils.isCtripIsd() && (
              <ProductConfirmModalNew
                page={this}
                visible={productConfirmModalVisible}
                anchor={productConfirmAnchor}
                showFooter={false}
                onPressEasyLife={this.showEasyLifeModal}
                onClose={this.closeProductConfirmModal}
                onPressOptimize={this.showOptimizeStoreModal}
                showOptimizationStrengthenModal={
                  this.openOptimizationStrengthenModal
                }
                isSecretBox={isSecretBox}
                pageName="Booking"
              />
            )}
            {Utils.isCtripIsd() && (
              <ServiceClaimMoreModal
                visible={serviceClaimMoreVisible}
                onCancel={this.hideServiceClaimMore}
                data={rentalGuaranteeV2?.vendorServiceDetail}
              />
            )}
            {Utils.isCtripIsd() && (
              <CarServiceDetailModal
                visible={carServiceDetailVisible}
                onCancel={this.hideCarServiceDetail}
                data={rentalGuaranteeV2?.packageDetailList}
                purchasingNotice={rentalGuaranteeV2?.purchasingNotice}
                ptime={ptime}
                rtime={rtime}
                selectedInsuranceIds={addOnCodes}
                changeSelectInsurance={changeSelectInsurance}
                fromPage={CarServiceFromPageTypes.booking}
                showServiceDetailCode={showServiceDetailCode}
                haveFooter={!isEasyLife2024}
              />
            )}
            
            <BookingEasyLifeModal />
            <BookingPriceDetail
              visible={priceDetailModalVisible}
              onClose={this.closePriceDetailModal}
              onContinue={this.closePriceDetailModal}
              style={styles.priceDetailStyle}
              role={PageRole.BOOKING}
            />

            {Utils.isCtripOsd() && (
              <PriceDetailModalOsd
                visible={osdPriceDetailModalVisible}
                role={PageRole.BOOKING}
                data={getFeeDetailData()}
                onPressBtn={this.handleBookPress}
                onClose={this.closeOsdPriceDetailModal}
              />
            )}
            <SesameContainer />
            {isShowModifyOrderModal && (
              // eslint-disable-next-line react/jsx-props-no-spreading
              <ModifyOrderModal {...this.getModifyOrderModalData()} />
            )}
            <StoreModal
              onMaskPress={this.hideOptimizeStoreModal}
              visible={isShowOptimizeStoreModal}
            />

            <EhiFreeDepositRuleModalContainer isBooking={true} />

            {/* 境外免押规则弹层 */}
            {Utils.isCtripOsd() && <FreeDepositRuleModalContainer />}

            <CancelZhimaModal />
            {Utils.isCtripIsd() && (
              <PriceAlert
                visible={isShowPriceConfirm}
                priceAlertHandler={this.priceAlertHandler}
                popToVendorList={this.popToVendorList}
              />
            )}
            {Utils.isCtripIsd() && <PickupDownGradePop />}
            <VendorListOptimizationStrengthenModal
              visible={optimizationStrengthenModalVisible}
              onCancel={this.closeOptimizationStrengthenModal}
              isSecretBox={isSecretBox}
              pageName="Booking"
            />

            {Utils.isCtripIsd() && !isHasEtcIntroModal && (
              <EtcIntroModalContainer
                visible={isEtcIntroModalVisible}
                onClose={this.handleEtcIntroModalClose}
              />
            )}
            <BookingCreateInsLoading />
            <BookingCreateInsFailPop
              onBack={this.handleCreateInsFailPopLeftButton}
              onPay={this.handleCreateInsFailPopRightButton}
            />

            <AddInstructModal
              pageModalProps={pageModalProps}
              addInstructData={addInstructData}
            />

            {Utils.isCtripOsd() && <DepositRateDescriptionModal />}
            {Utils.isCtripOsd() && isKlb && (
              <FlightDelayRulesModal
                testID={CarLog.LogExposure({
                  name: '曝光_填写页_航班延误政策_曝光弹层',

                  info: logBaseInfo,
                })}
              />
            )}
            {Utils.isCtripIsd() && (
              <BookingConfirmModal
                visible={showConfirm}
                onConfirm={this.closeBookingConfirmModal}
                onCancel={this.closeBookingConfirmModalWithPop}
              />
            )}
            {Utils.isCtripIsd() && (
              <BusinessLicenseModal
                visible={isBusinessLicenseModalVisible}
                onClose={this.handleBusinessLicenseClose}
              />
            )}
            {Utils.isCtripOsd() && (
              <FuelDescriptionModalContainer
                visible={isFuelDescriptionModalVisible}
                onClose={this.handleFuelDescClose}
              />
            )}
            {Utils.isCtripOsd() && (
              <LocalContactsModal
                visible={localContactsModalVisible}
                onClose={this.hideLocalContactsModal}
                onSelectContact={this.onSelectContactType}
                pickUpAreaCode={pickUpAreaCode}
              />
            )}
          </>
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/State/DriverList/Logic.ts, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 VariableDeclarator表达式，start： 8194  before： const isLicenseApproveCVer = GetABCache.isLicenseApproveCVer();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 8199  before： if (isLicenseApproveCVer) {
      const state = yield select();
      const curDriverLicense = getCurDriverLicense(state);
      const driverLicenseItems = getDriverLicenseItems(state);
      const placeHoldTips = getPlaceHoldTips(state);
      let driverLicense = '';
      let driverLicenseName = '';
      let placeholder = '';
      let isEnable = true;
      if (driverLicenseItems?.length > 0) {
        if (!!curDriverLicense?.areaCode && !!curDriverLicense?.code) {
          driverLicense = `${curDriverLicense?.areaCode}|${curDriverLicense?.code}`;
          driverLicenseName = curDriverLicense?.title;
        }
      } else {
        placeholder = placeHoldTips || '';
        isEnable = false;
      }

      fromArray.push({
        type: IInputType.driverLicense,
        // 默认值必须是空字符，否则不会进行非空校验
        value: driverLicense,
        error: false,
      });
      fromArray.push({
        type: IInputType.driverLicenseName,
        // 默认值必须是空字符，否则不会进行非空校验
        value: driverLicenseName,
        placeholder,
        isEnable,
        error: false,
      });
    }  after： if (GetABCache.isLicenseApproveCVer()) {
      const state = yield select();
      const curDriverLicense = getCurDriverLicense(state);
      const driverLicenseItems = getDriverLicenseItems(state);
      const placeHoldTips = getPlaceHoldTips(state);
      let driverLicense = '';
      let driverLicenseName = '';
      let placeholder = '';
      let isEnable = true;
      if (driverLicenseItems?.length > 0) {
        if (!!curDriverLicense?.areaCode && !!curDriverLicense?.code) {
          driverLicense = `${curDriverLicense?.areaCode}|${curDriverLicense?.code}`;
          driverLicenseName = curDriverLicense?.title;
        }
      } else {
        placeholder = placeHoldTips || '';
        isEnable = false;
      }

      fromArray.push({
        type: IInputType.driverLicense,
        // 默认值必须是空字符，否则不会进行非空校验
        value: driverLicense,
        error: false,
      });
      fromArray.push({
        type: IInputType.driverLicenseName,
        // 默认值必须是空字符，否则不会进行非空校验
        value: driverLicenseName,
        placeholder,
        isEnable,
        error: false,
      });
    }
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/State/DriverList/Logic.ts, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 IfStatement表达式，start： 8199  before： if (GetABCache.isLicenseApproveCVer()) {
      const state = yield select();
      const curDriverLicense = getCurDriverLicense(state);
      const driverLicenseItems = getDriverLicenseItems(state);
      const placeHoldTips = getPlaceHoldTips(state);
      let driverLicense = '';
      let driverLicenseName = '';
      let placeholder = '';
      let isEnable = true;
      if (driverLicenseItems?.length > 0) {
        if (!!curDriverLicense?.areaCode && !!curDriverLicense?.code) {
          driverLicense = `${curDriverLicense?.areaCode}|${curDriverLicense?.code}`;
          driverLicenseName = curDriverLicense?.title;
        }
      } else {
        placeholder = placeHoldTips || '';
        isEnable = false;
      }

      fromArray.push({
        type: IInputType.driverLicense,
        // 默认值必须是空字符，否则不会进行非空校验
        value: driverLicense,
        error: false,
      });
      fromArray.push({
        type: IInputType.driverLicenseName,
        // 默认值必须是空字符，否则不会进行非空校验
        value: driverLicenseName,
        placeholder,
        isEnable,
        error: false,
      });
    }  after： 
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/State/Product/Logic.ts, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 IfStatement表达式，start： 13486  before： if (GetABCache.isLicenseApproveCVer()) {
          // 境外取车材料优化项目，查询驾照政策接口
          yield put(
            queryLicencePolicy({
              requestId: param?.requestId,
              nationalityCode: defaultPassenger?.nationality,
            }),
          );
        }  after： 
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isLicenseApproveCVer(), 替换数量=3 
 
1、成功 LogicalExpression表达式，start： 4138  before： lodashGet(res, 'isSelected') || false  after： lodashGet(res, 'isSelected') || GetABCache.isLicenseApproveCVer()
 
 
2、成功 LogicalExpression表达式，start： 22955  before： lodashGet(res, 'baseResponse.isSuccess') || false  after： lodashGet(res, 'baseResponse.isSuccess') || GetABCache.isLicenseApproveCVer()
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/State/Product/Logic.ts, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 LogicalExpression表达式，start： 4138  before： lodashGet(res, 'isSelected') || GetABCache.isLicenseApproveCVer()  after： lodashGet(res, 'isSelected')
 
 
2、成功 LogicalExpression表达式，start： 22983  before： lodashGet(res, 'baseResponse.isSuccess') || GetABCache.isLicenseApproveCVer()  after： lodashGet(res, 'baseResponse.isSuccess')
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/State/Product/Method.ts, 第1次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 VariableDeclarator表达式，start： 2058  before： const isLicenseApproveCVer = GetABCache.isLicenseApproveCVer();  after： 
 
 
步骤2: 开始反向还原因下线布尔常量赋值表达式引起的变量缺失 
 
1、成功，start： 2139  before： isLicenseApproveCVer &&
    (!firstName ||
      !lastName ||
      !age ||
      !nationality ||
      (minDriverAge > 0 && age < minDriverAge) ||
      (maxDriverAge > 0 && age > maxDriverAge))  after： GetABCache.isLicenseApproveCVer() &&
    (!firstName ||
      !lastName ||
      !age ||
      !nationality ||
      (minDriverAge > 0 && age < minDriverAge) ||
      (maxDriverAge > 0 && age > maxDriverAge))
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/State/Product/Method.ts, 第2次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 LogicalExpression表达式，start： 2181  before： GetABCache.isLicenseApproveCVer() &&
    (!firstName ||
      !lastName ||
      !age ||
      !nationality ||
      (minDriverAge > 0 && age < minDriverAge) ||
      (maxDriverAge > 0 && age > maxDriverAge))  after： false
 
 
步骤3: 开始替换布尔常量表达式const a = true; Or const b = false; 为函数调用表达式GetABCache.isLicenseApproveCVer(), 替换数量=2 
 
1、成功 IfStatement表达式，start： 2130  before： if (
    false
  ) {
    return {};
  }  after： if (
    GetABCache.isLicenseApproveCVer()
  ) {
    return {};
  }
 
 
下线AB实验，文件路径Path=/Users/<USER>/workarea/xtaro-car-main/src/pages/xcar/State/Product/Method.ts, 第3次调用 
 
步骤1: 开始替换AB实验获取的函数调用表达式GetABCache.isLicenseApproveCVer() 
 
1、成功 IfStatement表达式，start： 2130  before： if (
    GetABCache.isLicenseApproveCVer()
  ) {
    return {};
  }  after： 
 
 

下线AB实验相关代码，GetABCache.isLicenseApproveCVer函数相关，取值为false，全部执行完成 
 
