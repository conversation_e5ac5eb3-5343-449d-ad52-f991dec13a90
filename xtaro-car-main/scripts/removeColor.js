const fs = require('fs');
const path = require('path');

// 定义 color.qunar.ts 的路径
const colorFilePath = path.join(__dirname, '..', 'src', 'pages', 'xcar', 'Common', 'src', 'Tokens', 'tokens', 'color.qunar.ts');
const srcDir = path.join(__dirname, '..', 'src'); // 遍历的根目录

// 读取 color.qunar.ts 文件内容
const colorFileContent = fs.readFileSync(colorFilePath, 'utf-8');

// 提取所有通过 export const 导出的变量
const colorExports = colorFileContent.match(/export const \w+ = .+;/g);

// 创建一个对象来存储导出的变量及其是否被引用
const colorUsage = {};
if (colorExports) {
    colorExports.forEach(exportLine => {
        const varName = exportLine.match(/export const (\w+) =/)[1]; // 提取变量名
        colorUsage[varName] = false;
    });
} else {
    console.log('No exports found in color.qunar.ts');
    process.exit(0);
}

// 检查 color.qunar.ts 文件内部是否有对颜色变量的引用
Object.keys(colorUsage).forEach(varName => {
    // 使用正则表达式匹配变量名出现的次数
    const regex = new RegExp(`\\b${varName}\\b`, 'g');
    const matches = colorFileContent.match(regex);
    if (matches && matches.length >= 2) { // 出现两次或以上，说明有引用
        colorUsage[varName] = true; // 标记为被引用
    }
});

// 遍历 src 目录下的所有文件
function traverseDirectory(dir) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            traverseDirectory(filePath);
        } else if (stat.isFile() && filePath !== colorFilePath) {
            const fileContent = fs.readFileSync(filePath, 'utf-8');
            Object.keys(colorUsage).forEach(varName => {
                if (fileContent.includes(`color.${varName}`)) {
                    colorUsage[varName] = true;
                }
            });
        }
    });
}

traverseDirectory(srcDir);

// 记录未使用的变量
const unusedVars = Object.keys(colorUsage).filter(varName => !colorUsage[varName]);

// 删除未使用的变量
const newColorFileContent = colorFileContent.split('\n').filter(line => {
    const varNameMatch = line.match(/export const (\w+) =/);
    return !varNameMatch || !unusedVars.includes(varNameMatch[1]);
}).join('\n');

// 写入新的 color.qunar.ts 文件
fs.writeFileSync(colorFilePath, newColorFileContent, 'utf-8');

// 记录删除的导出
console.log('Deleted exports:');
unusedVars.forEach(varName => {
    console.log(varName);
});