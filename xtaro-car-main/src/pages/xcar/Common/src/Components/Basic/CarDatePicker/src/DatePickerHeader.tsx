import { xMergeStyles } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React from 'react';
import c2xStyles from './datePickerHeaderC2xStyles.module.scss';

import Touchable from '../../Touchable/src';
import Header from '../../Header/src';
import Text from '../../Text';
import { BbkUtils } from '../../../../Utils';
import { font, color } from '../../../../Tokens';
import { texts } from './Texts';

const { getPixel } = BbkUtils;

type IDatePickerHeader = {
  title: string;
  onCancel: () => void;
  onConfirm: () => void;
  bbkBaseBuleColor: string;
  leftIconTestID?: string;
  rightIconTestID?: string;
};
const DatePickerHeader: React.FC<IDatePickerHeader> = ({
  title,
  onCancel,
  onConfirm,
  bbkBaseBuleColor,
  leftIconTestID,
  rightIconTestID,
}) => (
  <Header
    isLeftIconCross={true}
    title={title}
    titleStyle={styles.titleStyle}
    onPressLeft={onCancel}
    onPressRight={onCancel}
    leftIconTestID={leftIconTestID}
    renderRight={
      <Confirm
        testID={rightIconTestID}
        onPress={onConfirm}
        bbkBaseBuleColor={bbkBaseBuleColor}
      />
    }
    style={styles.headerWrapper}
    styleInner={styles.header}
    contentStyle={styles.content}
  />
);

const Confirm: React.FC<{
  onPress: () => void;
  bbkBaseBuleColor?: string;
  testID;
}> = ({ onPress, bbkBaseBuleColor, testID }) => (
  <Touchable
    testID={testID}
    onPress={onPress}
    className={c2xStyles.headerRightSide}
  >
    <Text
      style={xMergeStyles([
        font.title2BoldStyle,
        {
          color: bbkBaseBuleColor || color.blueBase,
        },
      ])}
      fontWeight="bold"
    >
      {texts.done}
    </Text>
  </Touchable>
);
const styles = StyleSheet.create({
  headerWrapper: { marginTop: 0, elevation: 0, paddingTop: 0 },
  header: { paddingTop: getPixel(35), paddingBottom: getPixel(35) },
  content: { paddingLeft: getPixel(180), paddingRight: getPixel(180) },
  titleStyle: {
    textAlign: 'center',
  },
});

export default DatePickerHeader;
