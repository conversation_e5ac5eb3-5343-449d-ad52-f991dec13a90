import { xMergeStyles, XView as View } from '@ctrip/xtaro';
import StyleSheet from '@c2x/apis/StyleSheet';
import React, { useState, useEffect, CSSProperties } from 'react';
import c2xStyles from './collapseC2xStyles.module.scss';

import Text from '../../Text';
import Touchable from '../../Touchable/src';
import { color, font, layout, icon, space } from '../../../../Tokens';
import { BbkUtils } from '../../../../Utils';

const { getPixel } = BbkUtils;

interface ICollapse {
  title: string;
  children: React.ReactNode;
  expand?: boolean;
  onPress?: (open: boolean) => void;
  titleStyle?: CSSProperties;
  iconStyle?: CSSProperties;
  testID?: string;
  contentTestID?: string;
}

export const Collapse: React.FC<ICollapse> = ({
  title,
  children,
  expand = false,
  onPress = () => {},
  titleStyle,
  iconStyle,
  testID,
  contentTestID,
}) => {
  const [open, setOpen] = useState(expand);

  const toggleCollapsed = () => {
    setOpen(!open);
    onPress && onPress(!open);
  };

  useEffect(() => {
    setOpen(expand);
  }, [expand]);

  return (
    <View style={layout.justifyStart}>
      <Touchable
        onPress={toggleCollapsed}
        testID={testID}
        className={c2xStyles.headTitle}
        style={layout.betweenHorizontal}
      >
        <Text
          style={xMergeStyles([
            font.title2MediumStyle,
            { color: color.fontPrimary },
            titleStyle,
          ])}
          fontWeight="medium"
        >
          {title}
        </Text>
        <Text type="icon" className={c2xStyles.icon} style={iconStyle}>
          {open ? icon.arrowUp : icon.arrowDown}
        </Text>
      </Touchable>
      {open ? <View testID={contentTestID}>{children}</View> : null}
    </View>
  );
};

export default Collapse;
