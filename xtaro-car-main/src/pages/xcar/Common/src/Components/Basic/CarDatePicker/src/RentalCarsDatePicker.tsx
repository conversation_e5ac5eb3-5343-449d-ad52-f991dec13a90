import StyleSheet from '@c2x/apis/StyleSheet';
import React, { Component } from 'react';
import { XView as View } from '@ctrip/xtaro';
import c2xStyles from './rentalCarsDatePickerC2xStyles.module.scss';
/* eslint-disable */
// @ts-ignore
import dayjs from '../../../../Dayjs/src/index';

import { BbkUtils } from '../../../../Utils';
import { color } from '../../../../Tokens';
import BbkComponentModal, {
  BbkComponentModalAnimationPreset,
} from '../../Modal';
import { DatePicker } from '../../DatePicker';
import { withTheme, getThemeAttributes } from '../../../../Theming';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
import { texts } from './Texts';
import DatePickerHeader from './DatePickerHeader';
const getLocalTime = (date, timezone, relativeTimezone?: number) => {
  let relativeTimezoneOffset;
  if (relativeTimezone) {
    relativeTimezoneOffset = relativeTimezone * -60;
  } else {
    relativeTimezoneOffset = date.getTimezoneOffset();
  }
  timezone = Number(timezone);
  timezone = isNaN(timezone) ? relativeTimezoneOffset / -60 : timezone;

  if (relativeTimezoneOffset === timezone * -60) {
    return date;
  }
  return dayjs(date)
    .add(relativeTimezoneOffset, 'minutes')
    .add(timezone, 'hours')
    .toDate();
};
const { selector, ensureFunctionCall, getPixel, vw, getDayGap } = BbkUtils;

export type IRentalCarsDatePickerProps = {
  visible: boolean;
  focusOnRtime: boolean;
  maxmonths: number;
  date: string;
  minTime: string;
  maxTime: string;
  ptime: string;
  rtime: string;
  minuteInterval: 15 | 30;
  ptimezone?: number;
  rtimezone?: number;
  onConfirm?: (data) => void;
  onCancel?: () => void;
  mode?: 'date' | 'time' | 'datetime';
  pickerType?: number;
  pickTitle?: string;
  dateType?: 'date' | 'time' | 'datetime';
  options?: {
    dropoffChain?: boolean;
    PickupGap?: Number;
    Dropoff_Interval?: Number;
    Default_Interval_Days?: Number;
    TimeGap?: Number;
  };
  showDetailDiff?: boolean;
  theme?: any;
  closeModalBtnTestID?: string;
  leftIconTestID?: string;
  rightIconTestID?: string;
};

type IRentalCarsDatePickerState = {
  activeTab: string;
  pickupDate: string | Date;
  dropoffDate: string | Date;
  minDate: string | Date;
  modalVisible: boolean;
  pickupMinDate: string | Date;
  currentDate?: string | Date;
};
class RentalCarsDatePicker extends Component<
  IRentalCarsDatePickerProps,
  IRentalCarsDatePickerState
> {
  constructor(props) {
    super(props);
    const state = this._resetDatePickerData(props);
    this.state = {
      ...state,
      modalVisible: false,
    };
  }

  static defaultProps = {
    visible: false,
    focusOnRtime: false,
    maxmonths: 12,
  };
  options = Object.assign(
    {},
    {
      // 还车时间是否跟随取车时间，默认跟随
      dropoffChain: true,
      PickupGap: 0,
      Dropoff_Interval: 0.5,
      Default_Interval_Days: 7,
      TimeGap: 15,
    },
    this.props.options,
  );
  _modal = null;
  showTime = 0;
  UNSAFE_componentWillReceiveProps(nextProps) {
    if (
      nextProps.options &&
      nextProps.options.Default_Interval_Days !==
        this.options.Default_Interval_Days
    ) {
      this.options.Default_Interval_Days =
        nextProps.options.Default_Interval_Days;
    }
  }
  _closestDate = (date, gap) => {
    date = date || dayjs();
    gap = gap || 30;
    const m_date = dayjs(date).startOf('minute');
    const minute = m_date.minute();
    const count = Math.floor(60 / gap);
    if (minute > gap * count) {
      return m_date.clone().add(1, 'hours').minute(0);
    }
    for (let i = 0; i < count; i++) {
      const min = gap * i,
        max = gap * (i + 1);
      if (minute >= min && minute < max) {
        if (i + 1 == count) {
          return m_date.clone().add(1, 'hours').minute(0);
        } else {
          return m_date.clone().minute(max);
        }
      }
    }
    return m_date.clone();
  };
  _resetDatePickerData = (nextProps: any) => {
    let { ptime, rtime, focusOnRtime, date, minTime } = nextProps;
    if (!!date) {
      const pickupDate = date && dayjs(date);
      const dropoffDate = date && dayjs(date);
      const minDate = minTime && dayjs(minTime);
      const pickupMinDate = minTime && dayjs(minTime);
      return {
        activeTab: focusOnRtime ? 'dropoff' : 'pickup',
        pickupMinDate: pickupMinDate.toDate(),
        pickupDate: pickupDate.toDate(),
        dropoffDate: dropoffDate.toDate(),
        minDate: minDate.toDate(),
      };
    }
    const now = dayjs();
    let pickupDate, dropoffDate;
    const pickupMinDate = dayjs(this._getPickupMinDate());
    const mptime = ptime && dayjs(ptime);
    const mrtime = rtime && dayjs(rtime);
    if (mptime && mptime.isValid()) {
      pickupDate = mptime.diff(pickupMinDate) > 0 ? mptime : pickupMinDate;
    } else {
      pickupDate = now
        .clone()
        .startOf('hour')
        .add(this.options.Default_Interval_Days, 'd')
        .hour(10);
    }
    const dropoffMinDate = dayjs(this._getDropoffMinDate(pickupDate));
    if (mrtime && mrtime.isValid()) {
      this.options.dropoffChain = false;
      dropoffDate = mrtime.diff(dropoffMinDate) > 0 ? mrtime : dropoffMinDate;
    } else {
      dropoffDate = pickupDate
        .clone()
        .add(this.options.Default_Interval_Days, 'd');
    }
    const minDate = focusOnRtime ? dropoffMinDate : pickupMinDate;
    return {
      activeTab: focusOnRtime ? 'dropoff' : 'pickup',
      pickupMinDate: pickupMinDate.toDate(),
      pickupDate: pickupDate.toDate(),
      dropoffDate: dropoffDate.toDate(),
      minDate: minDate.toDate(),
    };
  };
  _getPickupMinDate = () => {
    const { ptimezone } = this.props;
    const hours = this.options.PickupGap;
    const date = this._closestDate(dayjs(), this.options.TimeGap).add(
      hours,
      'hours',
    );
    return getLocalTime(date.toDate(), ptimezone);
  };
  _getDropoffMinDate = (pickuptime: string | Date) => {
    const { ptimezone, rtimezone } = this.props;
    const hours = this.options.Dropoff_Interval;
    const date = dayjs(pickuptime).add(hours, 'hours');
    return getLocalTime(date.toDate(), rtimezone, ptimezone);
  };
  _getMaxDate = (activeTab, month) => {
    let maxDate;
    const now = dayjs();
    maxDate = now.clone().add(month, 'M');
    if (activeTab === 'dropoff') {
      maxDate = maxDate.add(this.options.Default_Interval_Days, 'd');
    }
    return maxDate.toDate();
  };
  dismiss = callback => {
    if (this.showTime) {
      this.showTime = 0;
    }
    this.setState({ modalVisible: false }, callback);
  };
  show = props => {
    this.showTime = +new Date();
    const data = {
      ...this.props,
      ...props,
    };
    this.setState({ modalVisible: true });
    this.setState(this._resetDatePickerData(data));
  };
  validateCurrentDate = (date, activeTab) => {
    const { maxmonths } = this.props;
    const { minDate } = this.state;
    let mdate = dayjs(date);
    const maxDate = this._getMaxDate(activeTab, maxmonths);
    if (mdate.valueOf() > dayjs(maxDate).valueOf()) {
      mdate = dayjs(maxDate).hour(10).minute(0);
    } else if (mdate.valueOf() < dayjs(minDate).valueOf()) {
      let hour = 0,
        minute = Math.ceil(dayjs(minDate).minute() / 15) * 15;
      if (minute > 59) {
        minute = 0;
        hour = 1;
      }
      mdate = dayjs(minDate)
        .add(hour, 'h')
        .add(minute + 30, 'm');
    }
    return mdate.toDate();
  };
  commonDateChange = (date, activeTab, dropoffDate) => {
    const { Default_Interval_Days } = this.options;
    let obj = {},
      key,
      mdate = dayjs(date),
      mrdate = dayjs(dropoffDate);
    if (activeTab === 'pickup') {
      key = 'pickupDate';
      // 取车时间如果大于还车时间，则还车时间跟随
      if (mrdate.diff(mdate, 'hours', true) <= Default_Interval_Days)
        this.options.dropoffChain = true;
      if (this.options.dropoffChain)
        obj['dropoffDate'] = dayjs(date)
          .add(Default_Interval_Days, 'd')
          .toDate();
    } else {
      this.options.dropoffChain = false;
      key = 'dropoffDate';
    }
    obj[key] = date;
    return obj;
  };
  handleDialogDatePickerChange = (date, activeTab, dropoffDate) => {
    return this.commonDateChange(date, activeTab, dropoffDate);
  };
  handleDateChange = date => {
    const { activeTab, dropoffDate } = this.state;
    const obj = this.commonDateChange(date, activeTab, dropoffDate);
    this.setState(obj);
  };
  _handleDateChange = date => {
    this.setState({
      currentDate: date,
    });
  };
  _pickupSelector = (activeTab?: string) => {
    if (!activeTab) {
      activeTab = this.state.activeTab;
    }
    return selector(activeTab === 'pickup');
  };
  handleTabPress = role => {
    const { activeTab, pickupDate } = this.state;
    let obj = {} as any;
    if (activeTab === role) return;
    obj.activeTab = role;
    if (role === 'dropoff') {
      obj.minDate = this._getDropoffMinDate(pickupDate);
      this.setState(obj);
    } else {
      // 强制触发两次render
      this.setState({
        minDate: this._getPickupMinDate(),
      });
      setTimeout(() => {
        this.setState(obj);
      }, 0);
    }
  };
  handleConfirm = () => {
    const { pickupDate, dropoffDate } = this.state;
    const { onConfirm } = this.props;
    this.dismiss(() => {
      ensureFunctionCall(onConfirm, this, {
        ptime: pickupDate,
        rtime: dropoffDate,
      });
    });
  };
  handleCancel = () => {
    const { onCancel } = this.props;
    this.dismiss(() => {
      ensureFunctionCall(onCancel);
    });
  };
  render() {
    const { activeTab, pickupDate, dropoffDate, minDate } = this.state;
    const {
      maxmonths,
      theme,
      mode,
      pickerType = 2,
      date,
      dateType,
      minTime,
      maxTime,
      showDetailDiff,
      closeModalBtnTestID,
      leftIconTestID,
      rightIconTestID,
    } = this.props;
    const currentDate = this._pickupSelector()(pickupDate, dropoffDate) || date;
    const maxDate = this._getMaxDate(activeTab, maxmonths);
    const diff = getDayGap(pickupDate, dropoffDate);
    if (!DatePicker) return null;
    const themes =
      (getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as any) ||
      ({} as any);
    const { backgroundColor = color.white, bbkBaseBuleColor = color.blueBase } =
      themes;
    const isOnlyDate = dateType === 'date';
    const detailDiff = BbkUtils.isd_dhm(pickupDate, dropoffDate);
    const diffVal = showDetailDiff ? detailDiff : texts.day(diff);

    return (
      <BbkComponentModal
        modalVisible={this.state.modalVisible}
        {...BbkComponentModalAnimationPreset('bottom')}
        onRequestClose={this.handleCancel}
        closeModalBtnTestID={closeModalBtnTestID}
        className={c2xStyles.wrapper}
      >
        <View className={c2xStyles.wrapper} style={{ backgroundColor }}>
          <DatePickerHeader
            title={!isOnlyDate ? diffVal : ' '}
            onCancel={this.handleCancel}
            onConfirm={this.handleConfirm}
            leftIconTestID={leftIconTestID}
            rightIconTestID={rightIconTestID}
            bbkBaseBuleColor={bbkBaseBuleColor}
          />

          <DatePicker
            mode={mode}
            pickerType={pickerType}
            minDate={minTime || minDate}
            maxDate={maxTime || maxDate}
            date={currentDate}
            style={styles.datePicker}
            onDateChange={this.handleDateChange}
            minuteInterval={this.props.minuteInterval}
          />
        </View>
      </BbkComponentModal>
    );
  }
}
const styles = StyleSheet.create({
  datePicker: {
    marginTop: 0,
    borderTopWidth: 0,
    height: 216,
  },
});

export default withTheme(RentalCarsDatePicker);
