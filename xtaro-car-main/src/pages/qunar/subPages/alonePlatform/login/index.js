import { View, Text } from '@tarojs/components';
import Taro, {
  getCurrentInstance,
  getApp,
  showToast,
  reLaunch,
  redirectTo,
  navigateBack,
  navigateTo,
} from '@tarojs/taro';
import React from 'react';
import QunarLoading from '@platformComponents/QunarLoading/index';
import PlatformLoginPhone from '@platformComponents/PlatformLoginPhone/index';
import PlatformLoginQuick from '@platformComponents/PlatformLoginQuick/index';
import PlatformLoginToast from '@platformComponents/PlatformLoginToast/index';
import PlatformLoginFirst from '@platformComponents/PlatformLoginFirst/index';
import PlatformLoginToastQuick from '@platformComponents/PlatformLoginToastQuick/index';
import PlatformLoginModal from '@platformComponents/PlatformLoginModal/index';
import { XViewExposure, xExposure } from '@ctrip/xtaro';
import user from '@/common/utils/user';
import EventEmitter from '@/common/utils/EventEmitter';
import util from '@/common/utils/util';
import watcher from '@/common/utils/watcher';
import cb from '@/common/utils/pageCb';
import Log from '@/common/utils/log';
import LogQmark from '@/common/utils/logQmark';
import getPId from '@/common/utils/pid';
import {
  QunarProtocolURL,
  QunarPrivacyPolicyURL,
} from '../../../../xcar/Diff/constants/constant';
import CPage from '../../../../xcar/Components/App/CPage';
import {
  AppContext,
  CarLog,
  Channel,
  DistributionChannel,
} from '../../../../xcar/Util/Index';

import './index.scss';

class P extends CPage {
  constructor(props) {
    super(props);
    this.currentInstance = getCurrentInstance();
    const query = this.currentInstance.router.params || {};
    this.state = {
      showToast: false,
      avatarUrl: '',
      phone: '',
      inputPhone: '',
      // 微信小框文案不通
      smsBtnText: '获取验证码',
      smsBtnText2: '获取验证码',
      code: '',
      wait: 60,
      source: '',
      canSendSmsFlag: true,
      isBind: false,
      networkData: {
        status: 4,
        loadingDesc: '加载中...',
        showButton: true,
      },
      // h5跳过来带这个参数，成功后直接跳转h5
      successUrl: '',
      activityPath: '', // 出现h5小程序登录成功后跳原生小程序的情况
      successPath: '/pages/qunar/subPages/alonePlatform/webView/index', // 成功登录后跳转的小程序中间页
      codeLock: false,
      submitLock: false,
      loaddingSuccess: false,
      noNeedBack: false,
      env: process.env.ANU_ENV,
      // 删除的x号的样式
      clearIconClass: '',
      hasOpenId: false,
      showQuickBrandLogin: false,
      isActWeb: '', // 活动页web-view 入口
      // 快应用唤起弹窗 需要一套新的code
      smsBtnTextQuick: '获取验证码',
      codeQuick: '',
      waitQuick: 60,
      canSendSmsFlagQuick: true,
      showToastQuick: false,
      codeLockQuick: false,
      submitLockQuick: false,
      codeNew: '',
      token: '',
      phoneTitle: '',
      authLock: false,
      // 手机号加密的
      phoneTitleEnc: '',
      // h5 跳登录页，设置分享卡片的参数需要单独传，要不然设置不进去
      needCustomShare: '',
      titleText: '',
      titleBarHeight: 88,
      statusBarHeight: 40,
      showLoginConfirmModal: false,
      buttonDecorationUrl: query.buttonDecorationUrl || '',
      rightsUrl: query.rightsUrl || '',
      newStyle:
        process.env.ANU_ENV === 'wx'
          ? query.newStyle === 'false'
          : query.newStyle === 'true',
      titleBarColor: '#fff',
    };
    xExposure.initExposure(
      {
        threshold: 1, // threshold： 显示百分比<可选>:   （0<threshold<=1） 默认是1，完全展示时触发
        exposeTime: 500, // 曝光时间 1000 默认500ms 当超过这个时间就作为有效埋点发送
        autoExpose: true, // 是否自动曝光
      },
      this,
    );
  }

  static protected = {
    successUrl: '',
  };

  /* eslint-disable class-methods-use-this */
  getPageId() {
    return Channel.getPageId().Login.ID;
  }

  componentDidMount() {
    const query = this.currentInstance.router.params;
    // 在这个页面判断是否绑定
    const _state = {
      source: query.source || 'ucenter',
      noNeedBack: query.noNeedBack || false,
      successUrl: query.successUrl || (this.wx && this.wx.successUrl) || '',
      successPath:
        query.successPath ||
        '/pages/qunar/subPages/alonePlatform/webView/index',
      activityPath: query.activityPath || '',
      needCustomShare: query.needCustomShare || '',
      showLoginToastFullPage: query.from === 'busOfflineOrder',
      inputPhone: query.phone,
      showBackBtn: !(query.showBackBtn === 'false'),
      isReLaunch: query.isReLaunch === 'true',
    };
    if (_state.successUrl.length > 1 && _state.successUrl.indexOf('http') < 0) {
      _state.successUrl = `https:${query.successUrl}`;
    }
    if (process.env.ANU_ENV === 'quick') {
      _state.successUrl = util.decodeURL(_state.successUrl);
      this.checkQuickAccount();
    }
    this.setState({
      ..._state,
    });
    this.byNavigate();
    // 设置自定义导航栏高度
    util.getSystemInfo().then(res => {
      let {
        titleBarHeight,
        statusBarHeight,
        windowWidth,
        navigationBarHeight,
      } = res;
      titleBarHeight = titleBarHeight || navigationBarHeight || 44;
      titleBarHeight =
        this.state.env === 'quick'
          ? statusBarHeight
          : (titleBarHeight * 750) / windowWidth;
      statusBarHeight =
        this.state.env === 'quick' ? 0 : (statusBarHeight * 750) / windowWidth;
      this.setState({
        titleBarHeight,
        statusBarHeight,
      });
    });
    LogQmark({
      page: 'authLogin',
      module: 'default',
      id: 'default',
      ext: {
        ...(this.currentInstance.router.params || {}),
      },
    });
  }

  componentWillUnmount() {
    // 清除倒计时
    clearTimeout(this.countdownTimer);
    clearTimeout(this.countdownTimerQuick);
  }

  checkQuickAccount = async () => {
    const accountLogin = await user.quickBrandIsLogin();
    if (
      this.state.env !== 'quick' ||
      getApp().globalData.accountProvider !== 'vivo' ||
      !accountLogin ||
      !user.hasVivoApi()
    ) {
      return;
    }
    this.setState({
      showQuickBrandLogin: true,
    });
  };

  // 快应用厂商快速登录
  logByQuickBrand = async () => {
    if (this.state.authLock) {
      showToast({
        icon: 'none',
        title: '正在授权中...',
      });
    }
    this.setState({
      authLock: true,
    });
    const res = await user.quickBrandGetAuth();
    if (!res.ret || !res.data.code) {
      showToast({
        icon: 'none',
        title: '抱歉，没有授权成功呢',
      });
      // 授权失败 重置
      this.setState({
        authLock: false,
      });
      return;
    }
    const { code } = res.data;
    // 进行授权
    const token = await user.getQunarToken(code, 'vivo');
    const qunarToken = token.data && token.data.qunarToken;
    if (!token.ret || !qunarToken) {
      showToast({
        icon: 'none',
        title: '服务器错误，请稍后再试',
      });
      // 授权失败 重置
      this.setState({
        authLock: false,
      });
      return;
    }
    this.setState({
      token: qunarToken,
    });
    // 校验是否绑定
    const checkBind = await user.checkBind(qunarToken, 'vivo');
    if (!checkBind.ret) {
      showToast({
        icon: 'none',
        title: '查询绑定失败，请稍后再试',
      });
      // 授权失败 重置
      this.setState({
        authLock: false,
      });
      return;
    }
    const isBind = checkBind?.data?.isBind;
    // 如果绑定 快速登录
    if (isBind) {
      this.confirm(qunarToken);
      // 直接登陆成功 重置
      this.setState({
        authLock: false,
      });
      return;
    }
    // 没绑定 唤起手机号
    const phoneRes = await user.quickBrandGetPhoneh();
    if (!phoneRes.ret || !phoneRes.data.phoneNumber) {
      showToast({
        icon: 'none',
        title: '获取手机号失败，请稍后再试',
      });
      // 授权失败 重置
      this.setState({
        authLock: false,
      });
      return;
    }
    const phone = phoneRes.data.phoneNumber;
    this.setState({
      phoneTitle: phone,
      phoneTitleEnc: phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
    });
    // 授权失败 重置
    this.setState({
      authLock: false,
    });
    this.getCodeQuick(true);
  };

  byNavigate = async () => {
    const env = process.env.ANU_ENV;
    // 快应用只走手机号
    if (env === 'quick') {
      this.setState({
        networkData: {
          status: 0,
        },
        isBind: false,
        loaddingSuccess: true,
      });
      this.setTitle(false);
      return;
    }
    let isBind;
    // 支付宝也不需要高级授权了
    // 先检测是否绑定成功 这里可能是别的平台的其他错误返回
    // 微信 qq不需要 再来的时候已经查询过绑定了
    if (env !== 'wx' && env !== 'qq') {
      const res = await user.checkBind();
      if (!res.ret) {
        // 这里改为手机号验证码登录
        this.setState({
          networkData: {
            status: 0,
          },
          isBind: false,
          loaddingSuccess: true,
        });
        this.setTitle(isBind);
        return;
      }
    }
    // 百度逻辑，没登录手百账号使用全屏手机号样式，登录手百账号使用微信手机号登录
    const { cookies } = util.getGlobalInfo();
    isBind = util.getGlobalInfo().user.isBind;
    this.setState({
      isBind,
      hasOpenId: !!cookies.openId,
      networkData: {
        status: 0,
      },
      loaddingSuccess: true,
    });
    this.setTitle(isBind);
    this.updateUser();
  };

  setTitle = isAuth => {
    this.setState({
      titleText: isAuth ? '授权登录' : '登录',
    });
    // React.api.setNavigationBarTitle({
    //     title: isAuth ? '授权登录' : '手机号登录'
    // });
  };

  // 更新快速登录的手机号头像
  updateUser = () => {
    const userInfo = util.getGlobalInfo().user;
    this.setState({
      avatarUrl: userInfo.avatarUrl,
      phone: userInfo.phone,
    });
  };

  bindPhoneChange = e => {
    const { value } = e;
    this.setState(
      {
        inputPhone: value.trim(),
      },
      () => {
        this.checkNum();
      },
    );
    // setTimeout(() => {this.checkNum();}, 100);
  };

  bindCodeChange = e => {
    this.setState(
      {
        code: e.value,
      },
      () => {
        this.checkNum();
      },
    );
    // setTimeout(() => {this.checkNum();}, 100);
  };

  checkNum = () => {
    const { inputPhone = '' } = this.state || {};
    // 按钮颜色
    if (this.state.code.length === 6 && inputPhone?.length === 11) {
      this.setState({
        canSubmit: true,
      });
    } else {
      this.setState({
        canSubmit: false,
      });
    }
    // 删除符号
    if (inputPhone?.length === 0) {
      this.setState({
        clearIconClass: '',
      });
    } else {
      this.setState({
        clearIconClass: 'show',
      });
    }
  };

  getCode = async () => {
    if (!this.state.canSendSmsFlag) {
      showToast({
        icon: 'none',
        title: '正在获取，请稍候',
        duration: 1000,
      });
      return;
    }
    if (!/^1[0-9]{10}/.test(this.state.inputPhone)) {
      showToast({
        icon: 'none',
        title: '请输入正确手机号',
        duration: 1000,
      });
      return;
    }
    const params = {
      mobile: this.state.inputPhone,
      source: this.state.source,
    };
    if (this.state.codeLock) return;
    this.setState({
      codeLock: true,
    });
    const res = await user.sendSMSCode(params);
    this.setState({
      codeLock: false,
    });
    // 验证码发送失败
    if (!res.ret) {
      showToast({
        icon: 'none',
        title: res.errMsg || res.errmsg,
      });
      return;
    }
    this.changeBtn();
    this.setState({
      canSendSmsFlag: false,
    });
  };

  changeBtn = () => {
    if (this.state.wait === 0) {
      this.enableBtn();
    } else {
      this.setState({
        smsBtnText: `${this.state.wait}s后重发`,
        smsBtnText2: `${this.state.wait}s后重发`,
        wait: this.state.wait - 1,
      });
      clearTimeout(this.countdownTimer);
      this.countdownTimer = setTimeout(() => this.changeBtn(), 1000);
    }
  };

  user_login_clearInput = () => {
    this.setState(
      {
        inputPhone: '',
      },
      () => {
        this.checkNum();
      },
    );
    // setTimeout(() => {this.checkNum();}, 100);
  };

  enableBtn = () => {
    clearTimeout(this.countdownTimer);
    clearTimeout(this.countdownTimerQuick);
    this.setState({
      wait: 60,
      waitQuick: 60,
      smsBtnText: '获取验证码',
      smsBtnText2: '获取验证码',
      smsBtnTextQuick: '重新获取',
      smsBtnClass: '',
      canSendSmsFlag: true,
      canSendSmsFlagQuick: true,
    });
  };

  getQueryData = () => {
    const query = this.currentInstance.router.params;
    const { appShowOptions = {} } = getApp().globalData || {};
    const appQuery = appShowOptions.query || {};
    return {
      ...query,
      ...appQuery,
      pid: query.pid || appQuery.pid || getPId(),
      ref:
        query.ref ||
        appQuery.ref ||
        'pages/qunar/subPages/alonePlatform/login/index',
    };
  };

  logIn = async () => {
    // 输入手机号验证码登录
    if (!this.state.canSubmit) {
      return;
    }
    if (this.state.submitLock) return;
    if (!this.state.hasAgreedProtocol) {
      showToast({
        icon: 'none',
        title: '请勾选同意并遵行去哪儿网《用户服务协议》及《隐私政策》',
      });
      return;
    }

    this.setState({
      submitLock: true,
    });
    const queryData = this.getQueryData();
    const params = {
      ...queryData,
      mobile: this.state.inputPhone,
      logincode: this.state.code,
      source: this.state.source || 'ucenter',
    };
    const res = await user.loginByPhone(params);
    this.setState({
      submitLock: false,
    });

    CarLog.LogCode({
      name: '境内_登录页_登录方式',
      info: {
        distributionChannelId: (AppContext.MarketInfo.childChannelId || DistributionChannel),
        scookie: AppContext.Cookie?.cookies?._s || '',
        loginMethod: '输入手机号验证码登录',
        isLoginSuccessful: !res.ret ? 0 : 1,
      },
    });

    // 登录失败
    if (!res.ret) {
      this.loginFailed();
      showToast({
        icon: 'none',
        title: res.errMsg || res.errmsg || '登录失败，请重试',
      });
      if (
        res.errcode == 21023 ||
        res.errcode == 21027 ||
        res.errcode == 11005
      ) {
        this.setState({ code: '' });
      }
      return;
    }

    if (res.isReg) {
      showToast({
        icon: 'none',
        title: '注册成功，您可以使用该手机号在去哪儿网登录',
        duration: 1000,
      });
    } else {
      showToast({
        icon: 'none',
        title: '登录成功',
        duration: 1000,
      });
    }
    // 处理页面传入回调
    this.loginSuccess();
  };

  showLoginModal = confirmFnName => {
    if (
      this.state.env === 'wx' ||
      this.state.env === 'ali' ||
      this.state.env === 'qq'
    ) {
      this.setState({
        showLoginConfirmModal: true,
        clickLoginFunction: confirmFnName,
      });
      LogQmark({
        page: 'authLogin',
        module: 'default',
        id: 'showLoginModal',
        operType: 'click',
      });
      return;
    }
    showToast({
      icon: 'none',
      title: '请勾选同意并遵行去哪儿网《用户服务协议》及《隐私政策》',
    });
  };

  clickPlatformLoginQuick = token => {
    LogQmark({
      page: 'authLogin',
      module: 'default',
      id: 'quickLogin',
      operType: 'click',
    });
    // wx bu小程序未授权时去授权登录
    if (!this.state.hasAgreedProtocol) {
      this.showLoginModal('confirm');
      return;
    }

    this.confirm(token);
  };

  confirm = async token => {
    // // 近期授权过，快速登录
    // if (!this.state.hasAgreedProtocol) {
    //     React.api.showToast({
    //         icon: 'none',
    //         title: '请勾选同意并遵行去哪儿网《用户服务协议》及《隐私政策》'
    //     });
    //     return;
    // }
    if (this.state.submitLock) return;
    this.setState({
      submitLock: true,
    });
    LogQmark({
      page: 'authLogin',
      module: 'default',
      id: 'confirm',
      operType: 'click',
    });
    const res = await user.loginByQuick({
      source: this.state.source,
      // 下面俩给快应用快速登录用
      brand: getApp().globalData.accountProvider,
      token: token || '',
    });
    this.setState({
      submitLock: false,
    });
    CarLog.LogCode({
      name: '境内_登录页_登录方式',
      info: {
        distributionChannelId: (AppContext.MarketInfo.childChannelId || DistributionChannel),
        scookie: AppContext.Cookie?.cookies?._s || '',
        loginMethod: '快捷登录',
        isLoginSuccessful: !res.ret ? 0 : 1,
      },
    });
    if (!res.ret) {
      showToast({
        icon: 'none',
        title: res.errMsg,
        duration: 1000,
      });
      return;
    }
    showToast({
      icon: 'none',
      title: '登录成功',
      duration: 1000,
    });
    // 处理页面传入回调
    this.loginSuccess();
  };

  loginFailed = () => {
    if (this.state.source === 'hotel') {
      LogQmark({
        page: 'authLogin',
        module: 'default',
        id: 'loginFailed',
        operType: 'click',
        ext: {
          ...(this.currentInstance.router.params || {}),
        },
      });
    }
  };

  loginSuccess = () => {
    const successCallbackFunc = EventEmitter.lookFunc(
      'cmsLoginSuccessCallBack',
    )[0];
    if (successCallbackFunc) {
      EventEmitter.dispatch('cmsLoginSuccessCallBack');
      return;
    }
    // 如果有url 就不处理回调直接跳走 只有h5
    const func = EventEmitter.lookFunc('loginSuccessCallback')[0];
    const noNeedBack =
      func &&
      (func.indexOf('navigateTo') > 0 || func.indexOf('redirectTo') > 0);
    if (this.state.source === 'hotel') {
      LogQmark({
        page: 'authLogin',
        module: 'default',
        id: 'loginSuccess',
        operType: 'click',
        ext: {
          ...(this.currentInstance.router.params || {}),
        },
      });
    }
    setTimeout(() => {
      const { cookies = {} } = util.getGlobalInfo();
      if (!cookies._q || cookies._q.length === 0) {
        watcher.watchCount('cookie_lost_after_login');
      }
      if (this.state.activityPath) {
        const path = decodeURIComponent(this.state.activityPath);

        if (this.state.isReLaunch) {
          reLaunch({
            url: path,
          });
        } else {
          redirectTo({
            url: path,
          });
        }
        return;
      }
      if (this.state.successUrl) {
        const path = decodeURIComponent(this.state.successPath);
        redirectTo({
          url: `${path}?url=${encodeURIComponent(this.state.successUrl)}&loginSync=${'true'}${this.state.needCustomShare ? `&needCustomShare=${this.state.needCustomShare}` : ''}`,
        });
        return;
      }
      // 返回在前的原因是 ios回退动画 先执行回调（如果这个回调有跳转）就会出问题
      // 执行回调还要定时的原因是  百度IOS如果在后执行回调跳转，会随着上面的回退一起gg
      if (!noNeedBack && !this.state.noNeedBack) {
        navigateBack();
      }
      // 快应用定时器套定时器不执行
      if (process.env.ANU_ENV === 'quick') {
        EventEmitter.dispatch('loginSuccessCallback');
      } else {
        setTimeout(() => {
          EventEmitter.dispatch('loginSuccessCallback');
        }, 1000);
      }
    }, 1000);
  };

  clickPlatformLoginFirst = event => {
    LogQmark({
      page: 'authLogin',
      module: 'default',
      id: 'firstLogin',
      operType: 'click',
    });
    // wx bu qq小程序未授权时去授权登录
    if (!this.state.hasAgreedProtocol) {
      this.showLoginModal('getPhoneNumber');
      return;
    }
    this.getPhoneNumber(event);
  };

  // 用授权的加密信息登录
  getPhoneNumber = event => {
    if (
      this.state.env === 'qq' &&
      event.type === 'getphonenumber' &&
      !event.encryptedData
    ) {
      showToast({
        icon: 'none',
        title: '请先在QQ-设置-手机号码中授权',
      });
      return;
    }
    const queryData = this.getQueryData();
    cb.wxGetPhoneCb(event, async obj => {
      const params = {
        ...queryData,
        ...obj,
      };
      let res = null;
      if (this.state.env === 'ali') {
        res = await user.loginByAliPhone(params);
      } else if (this.state.env === 'qq') {
        res = await user.loginByQQPhone(params);
      } else if (params.code) {
        res = await user.loginByWxPhoneNew(params);
      } else {
        res = await user.loginByWxPhone(params);
      }
      // 登录失败
      if (!res.ret) {
        this.loginFailed();
        showToast({
          icon: 'none',
          title: res.errMsg || res.errmsg || '登录失败，请重试',
        });
        if (
          res.errcode == 21023 ||
          res.errcode == 21027 ||
          res.errcode == 11005
        ) {
          this.setState({ code: '' });
        }
        return;
      }
      if (res.isReg) {
        showToast({
          icon: 'none',
          title: '注册成功，您可以使用该手机号在去哪儿网登录',
          duration: 1000,
        });
      } else {
        showToast({
          icon: 'none',
          title: '登录成功',
          duration: 1000,
        });
      }
      // 处理页面传入回调
      this.loginSuccess();
    });
  };

  clickPlatformLoginQuickChangePhoneUserInfo = event => {
    LogQmark({
      page: 'authLogin',
      module: 'default',
      id: 'switchPhoneNumber',
      operType: 'click',
    });
    if (!this.state.hasAgreedProtocol) {
      this.showLoginModal('changePhoneUserInfo');
      return;
    }
    this.changePhoneUserInfo(event);
  };

  // 用其他手机号登录 这里需要授权
  changePhoneUserInfo = event => {
    if (event.userInfo) {
      this.switchPhone(true);
    }
    LogQmark({
      page: 'authLogin',
      module: 'default',
      id: 'otherAccount',
      operType: 'click',
    });
  };

  clickPlatformLoginFirstSwitchPhone = bol => {
    // wx bu小程序未授权时 使用其他手机号短信验证码登录
    LogQmark({
      page: 'authLogin',
      module: 'default',
      id: 'switchPhoneNumber',
      operType: 'click',
    });
    if (!this.state.hasAgreedProtocol) {
      this.showLoginModal('switchPhone');
      return;
    }
    this.switchPhone(bol);
  };

  // 微信的换手机号绑定
  switchPhone = bol => {
    this.setState({
      showToast: bol,
    });
  };

  /**
   * 下面是快应用弹窗相关 todo拆成组件
   */
  switchPhoneQuick = bol => {
    this.setState({
      showToastQuick: bol,
    });
  };

  bindCodeChangeQuick = e => {
    this.setState({
      codeNew: e.value,
    });
  };

  getCodeQuick = async first => {
    if (!this.state.canSendSmsFlagQuick) {
      showToast({
        icon: 'none',
        title: '正在获取，请稍候',
        duration: 1000,
      });
      return;
    }
    const params = {
      mobile: this.state.phoneTitle,
      source: this.state.source,
    };
    if (this.state.codeLockQuick) return;
    this.setState({
      codeLockQuick: true,
    });
    const res = await user.sendSMSCode(params);
    this.setState({
      codeLockQuick: false,
    });
    // 验证码发送失败
    if (!res.ret) {
      showToast({
        icon: 'none',
        title: res.errMsg || res.errmsg,
      });
      return;
    }
    if (first) {
      this.switchPhoneQuick(true);
    }
    this.changeBtnQuick();
    this.setState({
      canSendSmsFlagQuick: false,
    });
  };

  changeBtnQuick = () => {
    if (this.state.waitQuick === 0) {
      this.enableBtn();
    } else {
      this.setState({
        smsBtnTextQuick: `${this.state.waitQuick}s`,
        waitQuick: this.state.waitQuick - 1,
      });
      clearTimeout(this.countdownTimerQuick);
      this.countdownTimerQuick = setTimeout(() => this.changeBtnQuick(), 1000);
    }
  };

  quickCheck = async () => {
    if (this.state.codeNew.length < 6) {
      showToast({
        icon: 'none',
        title: '请填写正确验证码',
      });
      return;
    }
    if (this.state.submitLockQuick) return;
    this.setState({
      submitLockQuick: true,
    });
    const queryData = this.getQueryData();
    const params = {
      mobile: this.state.phoneTitle,
      logincode: this.state.codeNew,
      source: this.state.source,
      brand: 'vivo',
      token: this.state.token,
      ...queryData,
    };
    const res = await user.loginByPhone(params);
    this.setState({
      submitLockQuick: false,
    });
    // 登录失败
    if (!res.ret) {
      this.loginFailed();
      showToast({
        icon: 'none',
        title: res.errMsg || res.errmsg || '登录失败，请重试',
      });
      if (
        res.errcode == 21023 ||
        res.errcode == 21027 ||
        res.errcode == 11005
      ) {
        this.setState({ code: '' });
      }
      return;
    }
    if (res.isReg) {
      showToast({
        icon: 'none',
        title: '注册成功，您可以使用该手机号在去哪儿网登录',
        duration: 1000,
      });
    } else {
      showToast({
        icon: 'none',
        title: '登录成功',
        duration: 1000,
      });
    }
    // 处理页面传入回调
    this.loginSuccess();
  };

  changeSelected = agree => {
    // 点击复选框
    this.setState({
      hasAgreedProtocol: agree || !this.state.hasAgreedProtocol,
    });
    this.sendLog({
      button_id: 'applet_agreement_agree',
      button_name: '同意框',
    });
    LogQmark({
      page: 'authLogin',
      module: 'default',
      id: 'check',
      operType: 'click',
    });
  };

  clickProtocolText = () => {
    // 微信端为了减少用户登陆流程，直接跳转到用户服务协议页面
    if (this.state.env === 'wx') {
      navigateTo({
        url: `/pages/qunar/subPages/alonePlatform/webView/index?url=${encodeURIComponent(QunarProtocolURL)}`,
      });
      this.sendLog({
        button_id: 'applet_agreement_detail',
        button_name: '用户协议细则',
      });
      return;
    }

    // 点击用户协议
    navigateTo({
      url: `/pages/qunar/subPages/alonePlatform/webView/index?url=${encodeURIComponent(QunarProtocolURL)}`,
    });
    this.sendLog({
      button_id: 'applet_agreement_detail',
      button_name: '用户协议细则',
    });
  };

  clickProtoColToPrivacyPolicy = () => {
    if (this.state.env === 'wx' && wx.canIUse('getPrivacySetting')) {
      // wx.openPrivacyContract();
      navigateTo({
        url: `/pages/qunar/subPages/alonePlatform/webView/index?url=${encodeURIComponent(QunarPrivacyPolicyURL)}`,
      });
    } else {
      navigateTo({
        url: `/pages/qunar/subPages/alonePlatform/webView/index?url=${encodeURIComponent(QunarPrivacyPolicyURL)}`,
      });
    }
  };

  sendLog = params => {
    const { button_id, button_name } = params;
    const storage = util.getGlobalInfo() || {};
    const { _q } = storage.cookies || {};
    const { openId } = storage.user || {};
    Log({
      action_type: 'click',
      action_time: new Date(),
      button_id,
      button_name,
      cookie: openId,
      openid: openId,
      user_name: _q,
    });
  };

  clickLoginModalCancel = () => {
    this.setState({ showLoginConfirmModal: false });
  };

  render() {
    return (
      <XViewExposure
        testID={{
          ubtKey: 'c_car_trace_exposure',
          data: {
            name: '境内_登录页_PV曝光',
            distributionChannelId: (AppContext.MarketInfo.childChannelId || DistributionChannel),
            scookie: AppContext.Cookie?.cookies?._s || '',
            pageId: this.getPageId(),
          },
        }}
      >
        <View
          class="p-login-wrap"
          style={{
            paddingTop: `${this.state.statusBarHeight + this.state.titleBarHeight}rpx`,
          }}
        >
          {!this.state.loaddingSuccess && (
            <QunarLoading networkData={this.state.networkData} />
          )}

          {this.state.env === 'wx' && this.state.showBackBtn ? (
            <View
              class="back-btn"
              onClick={() => {
                LogQmark({
                  module: 'default',
                  page: 'authLogin',
                  id: 'back_btn',
                  operType: 'click',
                  ext: { newStyle: this.state.newStyle },
                });
                navigateBack();
              }}
              style={{
                height: `${this.state.titleBarHeight}rpx`,
                top: `${this.state.statusBarHeight}rpx`,
                backgroundColor: this.state.titleBarColor,
              }}
            >
              <Text class="g-q-iconfont back-icon"></Text>
            </View>
          ) : null}

          {!this.state.showLoginToastFullPage && (
            <View
              class="title-bar"
              style={{
                height: `${this.state.titleBarHeight}rpx`,
                paddingTop: `${this.state.statusBarHeight}rpx`,
                backgroundColor: this.state.titleBarColor,
              }}
            >
              <Text class="title-text">{this.state.titleText}</Text>
            </View>
          )}
          {/* <div className='p-login-wrap-protocol'>
                <div className="p-login-wrap-changeSelected" onClick={this.changeSelected.bind(this)}>
                    <text className="p-login-wrap-checkbox g-q-iconfont">{this.state.hasAgreedProtocol ? '' : ''}</text>
                    <text  className="p-login-wrap-text">同意并遵行去哪儿网</text>
                </div>
                <text className="p-login-wrap-protocolText" onClick={this.clickProtocolText.bind(this)}>《用户协议》</text>
            </div> */}
          {this.state.env !== 'wx' &&
            this.state.env !== 'bu' &&
            this.state.env !== 'ali' &&
            this.state.env !== 'qq' &&
            !this.state.isBind &&
            this.state.loaddingSuccess &&
            !this.state.showLoginToastFullPage && (
              <PlatformLoginPhone
                logIn={this.logIn.bind(this)}
                changeSelected={this.changeSelected.bind(this)}
                hasAgreedProtocol={this.state.hasAgreedProtocol}
                clickProtocolText={this.clickProtocolText.bind(this)}
                clickProtoColToPrivacyPolicy={this.clickProtoColToPrivacyPolicy.bind(
                  this,
                )}
                bindPhoneChange={this.bindPhoneChange.bind(this)}
                user_login_clearInput={this.user_login_clearInput.bind(this)}
                getCode={this.getCode.bind(this)}
                bindCodeChange={this.bindCodeChange.bind(this)}
                canSendSmsFlag={this.state.canSendSmsFlag}
                smsBtnText={this.state.smsBtnText}
                showQuickBrandLogin={this.state.showQuickBrandLogin}
                logByQuickBrand={this.logByQuickBrand.bind(this)}
                canSubmit={this.state.canSubmit}
                newStyle={this.state.newStyle}
                rightsUrl={this.state.rightsUrl}
                buttonDecorationUrl={this.state.buttonDecorationUrl}
              />
            )}

          {(this.state.env === 'wx' ||
            this.state.env === 'bu' ||
            this.state.env === 'ali' ||
            this.state.env === 'qq') &&
            !this.state.isBind &&
            !this.state.showLoginToastFullPage &&
            this.state.loaddingSuccess && (
              <PlatformLoginFirst
                hasAgreedProtocol={this.state.hasAgreedProtocol}
                changeSelected={this.changeSelected.bind(this)}
                user_login_clearInput={this.user_login_clearInput.bind(this)}
                clickProtoColToPrivacyPolicy={this.clickProtoColToPrivacyPolicy.bind(
                  this,
                )}
                clickProtocolText={this.clickProtocolText.bind(this)}
                getPhoneNumber={this.clickPlatformLoginFirst.bind(this)}
                switchPhone={this.clickPlatformLoginFirstSwitchPhone.bind(this)}
                newStyle={this.state.newStyle}
                rightsUrl={this.state.rightsUrl}
                buttonDecorationUrl={this.state.buttonDecorationUrl}
              />
            )}

          {this.state.isBind &&
            this.state.loaddingSuccess &&
            !this.state.showLoginToastFullPage && (
              <PlatformLoginQuick
                confirm={this.clickPlatformLoginQuick.bind(this)}
                hasAgreedProtocol={this.state.hasAgreedProtocol}
                user_login_clearInput={this.user_login_clearInput.bind(this)}
                changeSelected={this.changeSelected.bind(this)}
                clickProtoColToPrivacyPolicy={this.clickProtoColToPrivacyPolicy.bind(
                  this,
                )}
                clickProtocolText={this.clickProtocolText.bind(this)}
                changePhoneUserInfo={this.clickPlatformLoginQuickChangePhoneUserInfo.bind(
                  this,
                )}
                env={this.state.env}
                phone={this.state.phone}
                avatarUrl={this.state.avatarUrl}
                newStyle={this.state.newStyle}
                rightsUrl={this.state.rightsUrl}
                buttonDecorationUrl={this.state.buttonDecorationUrl}
              />
            )}

          {(this.state.showLoginToastFullPage || this.state.showToast) && (
            <PlatformLoginToast
              code={this.state.code}
              show={this.state.showToast || this.state.showLoginToastFullPage}
              isFullPage={this.state.showLoginToastFullPage}
              logIn={this.logIn.bind(this)}
              switchPhone={this.switchPhone.bind(this)}
              hasAgreedProtocol={this.state.hasAgreedProtocol}
              changeSelected={this.changeSelected.bind(this)}
              clickProtoColToPrivacyPolicy={this.clickProtoColToPrivacyPolicy.bind(
                this,
              )}
              clickProtocolText={this.clickProtocolText.bind(this)}
              user_login_clearInput={this.user_login_clearInput.bind(this)}
              bindPhoneChange={this.bindPhoneChange.bind(this)}
              getCode={this.getCode.bind(this)}
              bindCodeChange={this.bindCodeChange.bind(this)}
              inputPhone={this.state.inputPhone}
              clearIconClass={this.state.clearIconClass}
              canSendSmsFlag={this.state.canSendSmsFlag}
              smsBtnText2={this.state.smsBtnText2}
              buttonDecorationUrl={this.state.buttonDecorationUrl}
              newStyle={this.state.newStyle}
              canSubmit={this.state.canSubmit}
            />
          )}

          <PlatformLoginToastQuick
            code={this.state.codeNew}
            show={this.state.showToastQuick}
            logIn={this.quickCheck.bind(this)}
            switchPhone={this.switchPhoneQuick.bind(this)}
            hasAgreedProtocol={this.state.hasAgreedProtocol}
            changeSelected={this.changeSelected.bind(this)}
            clickProtoColToPrivacyPolicy={this.clickProtoColToPrivacyPolicy.bind(
              this,
            )}
            clickProtocolText={this.clickProtocolText.bind(this)}
            getCode={this.getCodeQuick.bind(this)}
            bindCodeChange={this.bindCodeChangeQuick.bind(this)}
            canSendSmsFlag={this.state.canSendSmsFlagQuick}
            smsBtnText={this.state.smsBtnTextQuick}
            phoneTitle={this.state.phoneTitleEnc}
            canSubmit={this.state.canSubmitQuick}
          />

          {this.state.showLoginConfirmModal && (
            <PlatformLoginModal
              changeSelected={this.changeSelected.bind(this)}
              confirm={this.confirm.bind(this)}
              getPhoneNumber={this.getPhoneNumber.bind(this)}
              changePhoneUserInfo={this.changePhoneUserInfo.bind(this)}
              switchPhone={this.switchPhone.bind(this)}
              cancel={this.clickLoginModalCancel.bind(this)}
              clickLoginFunction={this.state.clickLoginFunction}
              user_login_clearInput={this.user_login_clearInput.bind(this)}
              clickProtoColToPrivacyPolicy={this.clickProtoColToPrivacyPolicy.bind(
                this,
              )}
              clickProtocolText={this.clickProtocolText.bind(this)}
              env={this.state.env}
            />
          )}
        </View>
      </XViewExposure>
    );
  }
}

export default P;
