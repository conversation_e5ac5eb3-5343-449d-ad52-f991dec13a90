import { getCurrentInstance, redirectTo, switchTab } from "@tarojs/taro";
import React from "react";
import user from "@/common/utils/user";
import QunarLoading from "@platformComponents/QunarLoading/index";
import LogQmark from "@/common/utils/logQmark";

class AutoLogin extends React.Component {
    constructor(props) {
        super(props);
        this.currentInstance = getCurrentInstance();
    }
    async componentDidMount() {
        const query = this.currentInstance.router.params || {};
        const { activityPath, source } = query;

        LogQmark({
            page: "autoLogin",
            module: "default",
            id: "default",
            operType: "show",
            ext: {
                ...(query || {}),
            },
        });

        // 非阿里出行不处理任何逻辑（是否需要加来源控制）
        // if (source !== 'alichuxing') return

        // 没有目标页面，返回首页
        if (!activityPath) {
            this.toHomePage();
            return;
        }

        // 检查是否登录
        const checkRes = await user.checkLogin();
        if (checkRes?.data?.isLogin) {
            this.jumpPage(activityPath);
            return;
        }

        // 校验是否绑定
        const checkBindRes = await user.checkBind();

        // 未绑定 返回登录页
        if (!checkBindRes?.data?.isBind) {
            this.toLoginPage();
            return;
        }

        // 快速登录
        const loginRes = await user.loginByQuick();
        if (loginRes?.ret) {
            this.jumpPage(activityPath);
        } else {
            this.toLoginPage();
        }
    }

    // 跳转目标页面
    jumpPage = (activityPath) => {
        const path = decodeURIComponent(activityPath);
        redirectTo({ url: path });
    };

    // 跳转登录页
    toLoginPage = () => {
        let path = "/pages/qunar/subPages/alonePlatform/login/index";

        const query = this.currentInstance.router.params || {};

        // 序列化query对象为search字符串
        const search = Object.keys(query)
            .map((key) => `${key}=${query[key]}`)
            .join("&");

        if (search) {
            path = `${path}?${search}`;
        }
        redirectTo({ url: path });
    };

    toHomePage = () => {
        const ANU_ENV = process.env.ANU_ENV;
        if (ANU_ENV === "wx") {
            switchTab({
                url: "/pages/platform/indexWx/index",
            });
        } else if (ANU_ENV === "ali") {
            switchTab({
                url: "/pages/platform/indexAli/index",
            });
        } else {
            switchTab({
                url: "/pages/platform/index/index",
            });
        }
    };
    render() {
        return (
            <QunarLoading
                networkData={{ status: 4, loadingDesc: "努力加载中" }}
            />
        );
    }
}
export default AutoLogin;
