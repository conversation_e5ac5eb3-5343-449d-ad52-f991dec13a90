
.nav-cmp{
    display: flex;
    flex-direction: row;
    width: 100%;
    overflow: hidden;
    align-items: center;
    justify-content: center;
  .left-box {
    position: absolute;
    left: 16px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }
  .headLeftImg {
    width: 20px;
    height: 20px;
    margin-left: 5px;
    -webkit-align-items: center;
    align-items: center;
    // margin-top: 15rpx;
  }
  .title-box {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  .title-text{
    width: 180px;
    font-size: 18px;
    color: #333333;
    text-align: center;
    font-weight: 500;
    text-overflow: ellipsis;
    box-sizing: border-box;
    overflow: hidden;
    white-space: nowrap;
  }
}