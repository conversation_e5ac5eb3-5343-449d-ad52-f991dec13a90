import { View, Image, Text } from "@tarojs/components";
import { getSystemInfo, navigateBack } from "@tarojs/taro";
import React from "react";
import "./index.scss";

class Nav extends React.Component {
    static config = {
        component: true,
    };

    constructor(props) {
        super(props);
        this.state = {
            titleBarHeight: 88,
            statusBarHeight: 88,
        };
    }

    displayName = "安全收银台";

    componentDidMount() {
        // 设置自定义导航栏高度
        getSystemInfo().then((res) => {
            let { titleBarHeight = 44, statusBarHeight, windowWidth } = res;
            titleBarHeight =
                process.env.ANU_ENV === "quick"
                    ? statusBarHeight
                    : (titleBarHeight * 750) / windowWidth;
            statusBarHeight =
                process.env.ANU_ENV === "quick"
                    ? 0
                    : (statusBarHeight * 750) / windowWidth;
            this.setState({
                titleBarHeight,
                statusBarHeight,
            });
        });
    }

    onLeft = () => {
        const { onBack } = this.props || {};
        if (onBack) {
            onBack();
        } else {
            navigateBack();
        }
    };

    render() {
        return (
            <View
                class="nav-cmp"
                style={{
                    height: `${this.state.titleBarHeight}rpx`,
                    paddingTop: `${this.state.statusBarHeight}rpx`,
                }}
            >
                <View class="left-box" onClick={this.onLeft}>
                    <Image
                        src="https://picbed.qunarzz.com/2381e5043df342379f7bf187e25b7fc3.png"
                        class="headLeftImg"
                    ></Image>
                </View>
                <View class="title-box">
                    <Text class="title-text">{this.props.title}</Text>
                </View>
            </View>
        );
    }
}

export default Nav;
