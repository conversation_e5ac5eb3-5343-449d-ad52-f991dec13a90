import { View, Image, Button } from "@tarojs/components";
import React from "react";
import "./index.scss";

class WxRealNameGuideTpl extends React.Component {
    render() {
        return (
            <View className="realnameguide" name="WxRealNameGuideTpl">
                <View className="realnameguide-img">
                    <Image
                        className="authimg"
                        src="https://s.qunarzz.com/mobile_pay/image/wachatapp/reaalname_guide.png"
                    />
                </View>
                <View className="realnameguide-title">
                    通过微信进行账户实名认证？
                </View>
                <View className="realnameguide-desc">
                    实名认证后，可有效保障去哪儿账户的资金安全，更尊享“拿去花、闪住、信用抢票”等特权服务。
                </View>
                <View className="realnameguide-btn">
                    <Button
                        className="authbutton realname-not"
                        onClick={() => {
                            this.props.gotoRealName(0);
                        }}
                    >
                        暂不认证
                    </Button>
                    <Button
                        className="authbutton realname-goto"
                        onClick={() => this.props.gotoRealName(1)}
                    >
                        立即认证
                    </Button>
                </View>
                <View className="tips">
                    请放心，跳过实名认证不会影响任何预订或支付
                </View>
            </View>
        );
    }
}
export default WxRealNameGuideTpl;
