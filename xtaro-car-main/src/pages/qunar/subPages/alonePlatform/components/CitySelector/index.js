import {
    setStorage,
    getApp,
    navigateBack,
    setNavigationBarTitle,
} from "@tarojs/taro"; /* eslint-disable */
// 如需修改请通知gaoxiaolin.gao
import React from "react";
import RealCitySelector from "../RealCitySelector/index";
import util from "@/common/utils/util.js";
import request from "@/common/utils/request.js";
import EventEmitter from "@/common/utils/EventEmitter.js";
import config from "@/common/utils/config/config.js";
import cityListUtil from "../../common/utils/cityListUtil.js";
import watcher from "@/common/utils/watcher";

const HOT_CITY = "_HOT_CITY";
const HISTORY_CITY = "_HISTORY_CITY";
const ALL_CITIES = "_ALL_CITIES";
const HOT_CITY_GJ = "HOT_CITY_GJ";
const ALL_CITIES_GJ = "ALL_CITIES_GJ";

const defaultHost = config.settings.requestDomain;

class CitySelector extends React.Component {
    constructor(props) {
        super(props);
        this.method = "GET"; // 数据请求类型
        this.busCityList = [];
        this.DEP_TYPE = ""; // 用来标记出发城市和到达城市(FROM / TO)
        this.state = {
            activeCity: "domestic", // 当前激活的分页
            showCurrent: false, // 是否显示当前城市
            hotCities: [], // 热门城市列表
            allCities: [], // 所有城市列表
            historyCities: [], // 历史城市列表
            cities: [], // 搜索的城市列表，
            param: null, // 路由传过来的参数
            showOverseas: false, // 是否显示海外分页
            prefix: null, // 选择器的类型，flight | train | bus | hotel | travel
        };
        this.handleConditionChange = util.debounce(
            this.handleConditionChange.bind(this),
            200,
            false,
        );
    }

    handleConditionChange = (word) => {
        if (word === null || word === "") {
            this.setState({
                cities: [],
            });
        } else {
            this.fetchSuggestList(word);
        }
    };

    changeCityToDomestic = () => {
        // let newParam = deepEqual(this.state.param);
        // newParam.cityListService = '/api/hotel/city/en';
        let cityListService = "/api/hotel/city/en";
        if (this.state.param.type === 4) {
            cityListService = "/api/nav";
        }
        let self = this;
        this.setState(
            {
                activeCity: "domestic",
                param: {
                    ...this.state.param,
                    cityListService: cityListService,
                },
            },
            function () {
                self.readHotAndAllCity();
            },
        );
    };

    changeCityToOverseas = () => {
        // newParam.cityListService = '/api/hotel/city/gj';
        let self = this;
        let cityListService = "/api/hotel/city/gj";
        if (this.state.param.type === 4) {
            cityListService = "/api/nav"; // ??nana - why just ok?
        }
        this.setState(
            {
                activeCity: "overseas",
                param: {
                    ...this.state.param,
                    cityListService: cityListService,
                },
            },
            function () {
                self.readHotAndAllCity();
            },
        );
    };

    cityTap = (e) => {
        try {
            var self = this;
            var dataset = e.currentTarget.dataset,
                selectedCity = dataset.city.split("(")[0], // 去掉类似 怀化(huaihua) 这种形式的拼音
                cityUrl = dataset.cityurl || "",
                position = dataset.position || "",
                display = dataset.display || "";
            // 处理机票中suggest展示与选择不同的问题
            if (this.state.prefix === "FLIGHT") {
                selectedCity = dataset.flightcity || selectedCity;
            }
            var historyItem = selectedCity;

            var source = dataset.source;
            var cityObj = {
                city: selectedCity,
                cityUrl: cityUrl,
                position,
                display,
                isDep: self.state.param.isDep,
                source: source,
            };
            if ("FLIGHT" === self.state.prefix && self._qWatcher) {
                self._qWatcher.addCount("tap_" + source + "_city");
            }
            var historyCities = self.state.historyCities;

            if (historyCities) {
                var needSave = true;
                for (var i = 0; i < historyCities.length; i++) {
                    if (
                        historyCities[i].city == selectedCity ||
                        historyCities[i] == selectedCity ||
                        (("HOTEL" === self.state.prefix ||
                            "TRAVEL" === self.state.prefix) &&
                            source == "current")
                    ) {
                        needSave = false;
                        break;
                    }
                }
                if (needSave) {
                    if (historyCities.length >= 3) {
                        historyCities.pop();
                    }

                    if (
                        "HOTEL" === self.state.prefix ||
                        "TRAVEL" === self.state.prefix
                    ) {
                        historyItem = {
                            city: selectedCity,
                            cityUrl: cityUrl,
                            en_gj: position,
                            display,
                        };
                    }
                    historyCities.unshift(historyItem);
                    setStorage({
                        key: self.state.prefix + HISTORY_CITY + this.DEP_TYPE,
                        data: historyCities,
                    });
                }
            }
            var event = self.state.param && self.state.param.eventType;
            getApp().globalData && (getApp().globalData[event] = cityObj);
            // 汽车票的输出补丁
            if (this.state.prefix === "BUS" && cityObj.city.indexOf(" -") > 0) {
                var c = cityObj.city;
                cityObj.city = c.replace(" - ", "[") + "]";
            }
            EventEmitter.dispatch(event, cityObj);
            navigateBack();
        } catch (err) {
            watcher.watchCount("cityselector-pageback-err");
        }
    };

    componentDidMount() {
        const param = this.props.param;
        var data = util.json_parse(decodeURIComponent(param)) || {};
        var type = "";
        switch (data.type) {
            case 0:
                type = "flight";
                break;
            case 1:
                type = "train";
                break;
            case 2:
                type = "bus";
                this.DEP_TYPE = data.isDep ? "_FROM" : "_TO";

                break;
            case 3:
                type = "hotel";
                break;
            case 4:
                type = "travel";
                break;
            default:
                type = "other";
                break;
        }
        this.setState({
            isFail: false,
            failMessage: "",
            param: data,
            prefix: type.toUpperCase(),
        });
        if (type === "hotel" || type === "travel") {
            this.setState({
                showOverseas: true,
            });
        }
        this.setState({
            showCurrent: type !== "travel" ? true : false,
        });

        this.readHistory();
        this.readHotAndAllCity();
        this._showTitle();
    }

    onShow() {
        this._showTitle();
    }

    _showTitle = () => {
        var data = this.state.param || {};
        var barTitle = "";
        if (data.isDep === true) {
            barTitle = "出发城市";
        } else if (data.isDep === false) {
            barTitle = "到达城市";
        }
        barTitle = data.title || barTitle;
        barTitle &&
            setNavigationBarTitle({
                title: barTitle,
            });
    };

    readHistory = () => {
        let self = this;
        Taro.getStorage({
            key: self.state.prefix + HISTORY_CITY + this.DEP_TYPE,
            success: function (res) {
                let vHistoryCities = res.data;
                if (vHistoryCities && vHistoryCities.length) {
                    self.setState({
                        historyCities: vHistoryCities,
                    });
                }
            },
        });
    };

    readHotAndAllCity = async () => {
        //首先在storage里获取，失败则通过网络获取,最后静态数据兜底
        try {
            var vHotCities = await util.getGlobalInfoAsyncByKey(
                this.state.prefix + HOT_CITY,
            );
            var allCities = await util.getGlobalInfoAsyncByKey(
                this.state.prefix + ALL_CITIES,
            );
            if (this.state.activeCity === "overseas") {
                vHotCities = await util.getGlobalInfoAsyncByKey(
                    this.state.prefix + HOT_CITY_GJ,
                );
                allCities = await util.getGlobalInfoAsyncByKey(
                    this.state.prefix + ALL_CITIES_GJ,
                );
            }
            var isReqHot =
                vHotCities && (vHotCities.length || vHotCities["热门城市"]);
            var isReqAll = allCities;
            var currentTime = new Date().getTime();
            var time = await util.getGlobalInfoAsyncByKey(
                this.state.prefix + HOT_CITY + "_TIME",
            );
            // 汽车票的热门城市经常更新，且出发城市和到达城市的数据不同，故本期汽车票的热门城市数据不读localstorage
            if (
                isReqHot &&
                isReqAll &&
                this.state.prefix !== "BUS" &&
                time &&
                currentTime - time <= 3 * 86400000
            ) {
                // 热门（所有）城市缓存过期时间 3天 = 3*86400000 ms
                // this.setHotCity(vHotCities);
                // this.setAllCities(allCities);
                this.setCities(vHotCities, allCities);
            } else {
                this.requestCities();
            }
        } catch (error) {
            this.requestCities();
        }
    };

    // 设置热门城市和全部城市
    setCities = (hotCitiesData, allCitiesData) => {
        hotCitiesData = Array.isArray(hotCitiesData)
            ? hotCitiesData
            : hotCitiesData["热门城市"];
        hotCitiesData &&
            hotCitiesData.forEach((city) => {
                city.name ||
                    (city.cityName &&
                        ((city.name = city.cityName), delete city.cityName));
            });
        allCitiesData &&
            allCitiesData.forEach((city) => {
                city.name || (city.cityName && (city.name = city.cityName));
                city.value &&
                    city.value.forEach((item) => {
                        item.name ||
                            (item.cityName &&
                                ((item.name = item.cityName),
                                delete item.cityName));
                    });
            });
        this.setState({
            hotCities: hotCitiesData,
            allCities: allCitiesData,
        });
        let hotDataName =
            this.state.activeCity === "overseas" ? HOT_CITY_GJ : HOT_CITY;
        setStorage({
            key: this.state.prefix + hotDataName,
            data: hotCitiesData,
        });
        let allDataName =
            this.state.activeCity === "overseas" ? ALL_CITIES_GJ : ALL_CITIES;
        setStorage({
            key: this.state.prefix + allDataName,
            data: allCitiesData,
        });
    };

    // 获取并设置城市列表，全部城市、热门城市
    requestCities = () => {
        // console.log('FFFFFFFF');
        if (this.state.prefix === "TRAIN") {
            var trainCity = [
                { name: "北京" },
                { name: "上海" },
                { name: "广州" },
                { name: "深圳" },
                { name: "南京" },
                { name: "武汉" },
                { name: "杭州" },
                { name: "西安" },
                { name: "郑州" },
                { name: "成都" },
                { name: "长沙" },
                { name: "天津" },
            ];

            this.setCities(trainCity, []);
            this.setState({
                isShow: true,
            });
            return;
        }

        var _cityParam = this.state.param?.citySuggestParam || {};
        var _cityData = this.state.param?.citySuggestData || {};

        if (this.state.prefix === "BUS") {
            _cityData.b = {
                bizType: this.state.param.isDep ? 1 : 5,
                localVer: 0,
            };
            if (!this.state.param.isDep) {
                // 加入出发城市
                _cityData.fromCity = this.state.param.fromCity;
            }
            this.method = "POST";
            // _cityData.c = requester.getParamC()
        }
        // 汽车票的请求补丁
        if (
            this.state.prefix === "BUS" &&
            _cityData.fromCity &&
            _cityData.fromCity.indexOf("[") > 0
        ) {
            var c = _cityData.fromCity;
            _cityData.fromCity = c.slice(0, c.indexOf("["));
        }
        var reqData = {
            host: this.state.param.cityListHost
                ? decodeURIComponent(this.state.param.cityListHost)
                : defaultHost,
            service: decodeURIComponent(this.state.param.cityListService),
            method: this.method,
            param: _cityParam,
            data: _cityData,
            ignoreStatus: true,
            success: (res) => {
                switch (this.state.prefix) {
                    case "FLIGHT": {
                        if (res && res.length) {
                            var cityObj = [];
                            if (res[0] && res[0].n) {
                                cityObj = res[0].n;
                            }
                            this.setCities(cityObj, []);
                            setStorage({
                                key: this.state.prefix + HOT_CITY + "_TIME",
                                data: new Date().getTime(),
                            });
                        }
                        break;
                    }
                    case "TRAIN": {
                        break;
                    }
                    case "BUS": {
                        let newData = [];
                        if (!this.state.param.isDep) {
                            // 到达城市这里需要进行一下数据处理
                            (res.data || []).forEach(function (item) {
                                var d = {
                                    cs: item.citySort,
                                    gd: item.grade,
                                    nm: item.name,
                                    py: item.pinyin,
                                    sp: item.shortPinyin,
                                };
                                newData.push(d);
                            });
                        } else {
                            newData = res.data;
                        }
                        this.busCityList = newData;

                        let busCity = cityListUtil.formatCityData(newData);

                        this.setCities(busCity.hotCities, busCity.cityMainList);
                        break;
                    }
                    case "HOTEL":
                    case "OTHER": {
                        if (res && res.data) {
                            let arrData = Array.isArray(res.data)
                                ? res.data
                                : Array.isArray(res.data.data)
                                  ? res.data.data
                                  : [];
                            let data = [];
                            if (arrData.length > 1) {
                                data = util.arrayTransform(arrData.slice(1));
                            }
                            this.setCities(arrData[0], data);
                            setStorage({
                                key: this.state.prefix + HOT_CITY + "_TIME",
                                data: new Date().getTime(),
                            });
                        }
                        break;
                    }
                    case "TRAVEL": {
                        if (res && res.data) {
                            var hotCitiesData =
                                this.state.activeCity === "overseas"
                                    ? res.data.overseasHotDist
                                    : res.data.domesticHotDist;
                            // 攻略的接口里国内国外数据都在all里，在这里区分并调用setCities
                            hotCitiesData.forEach((item) => {
                                item.cityUrl = item.id;
                            });
                            let letterMap = [
                                "A",
                                "B",
                                "C",
                                "D",
                                "E",
                                "F",
                                "G",
                                "H",
                                "I",
                                "J",
                                "K",
                                "L",
                                "M",
                                "N",
                                "O",
                                "P",
                                "Q",
                                "R",
                                "S",
                                "T",
                                "U",
                                "V",
                                "W",
                                "X",
                                "Y",
                                "Z",
                            ];
                            if (this.state.activeCity === "overseas") {
                                let abroadCitiesTravel = [];
                                // 处理顺序混乱问题，替换掉for..in
                                for (let i = 0; i < letterMap.length; i++) {
                                    if (res.data.all[letterMap[i]]) {
                                        // if ( letter === 'D') break;
                                        let temp = {};
                                        temp.key = letterMap[i];
                                        temp.value = [];
                                        res.data.all[letterMap[i]].forEach(
                                            (item) => {
                                                if (item.abroad) {
                                                    item.cityUrl = item.id;
                                                    temp.value.push(item);
                                                }
                                            },
                                        );
                                        abroadCitiesTravel.push(temp);
                                    }
                                }
                                // for (let letter in res.data.all){
                                //     // if ( letter === 'D') break;
                                //     let temp = {};
                                //     temp.key = letter;
                                //     temp.value = [];
                                //     res.data.all[letter].forEach( item => {
                                //         if (item.abroad){
                                //             item.cityUrl = item.id;
                                //             temp.value.push(item);
                                //         }
                                //     });
                                //     abroadCitiesTravel.push(temp);
                                // }
                                this.setCities(
                                    hotCitiesData,
                                    abroadCitiesTravel,
                                );
                            } else {
                                let domesCitiesTravel = [];
                                // 处理顺序混乱问题，替换掉for..in
                                for (let i = 0; i < letterMap.length; i++) {
                                    if (res.data.all[letterMap[i]]) {
                                        // if ( letter === 'D') break;
                                        let temp = {};
                                        temp.key = letterMap[i];
                                        temp.value = [];
                                        res.data.all[letterMap[i]].forEach(
                                            (item) => {
                                                if (!item.abroad) {
                                                    item.cityUrl = item.id;
                                                    temp.value.push(item);
                                                }
                                            },
                                        );
                                        domesCitiesTravel.push(temp);
                                    }
                                }
                                // for (let letter in res.data.all){
                                //     // if ( letter === 'D') break;
                                //     let temp = {};
                                //     temp.key = letter;
                                //     temp.value = [];
                                //     res.data.all[letter].forEach( item => {
                                //         if (!item.abroad){
                                //             item.cityUrl = item.id;
                                //             temp.value.push(item);
                                //         }
                                //     });
                                //     domesCitiesTravel.push(temp);
                                // }
                                this.setCities(
                                    hotCitiesData,
                                    domesCitiesTravel,
                                );
                            }
                            setStorage({
                                key: this.state.prefix + HOT_CITY + "_TIME",
                                data: new Date().getTime(),
                            });
                        }
                    }
                }
            },
            fail: () => {
                this.setState({
                    isFail: true,
                    failMessage: "小驼出错啦，请稍后重试",
                });
            },
            complete: () => {
                this.setState({
                    isShow: true,
                });
            },
        };
        request(reqData);
    };
    // 获取搜索城市列表
    fetchSuggestList = (word) => {
        //// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
        var updateCities = (flag, data) => {
            if (flag && data && data.length) {
                this.setState({
                    cities: formatDate(data),
                });
            } else {
                this.setState({
                    cities: [],
                    noResult: true,
                });
            }
        };

        var formatDate = (data) => {
            if (this.state.prefix === "FLIGHT") {
                data.map((item) => {
                    return (item.name = item.display);
                });
            }

            return data;
        };

        let self = this;

        if (this.state.prefix === "BUS") {
            if (word) {
                word = word.toLowerCase();
            }
            let suggestList = cityListUtil.getMatchedCityList(
                word,
                this.busCityList,
            );
            updateCities(suggestList && suggestList.length, suggestList);
            return;
        }
        var _cityParam = this.state.param?.citySuggestParam || {};
        var _cityData = this.state.param?.citySuggestData || {};
        var _param = { q: word, keyword: word, queryValue: word, city: word };
        var _data = {
            q: word,
            keyword: word,
            b: { queryValue: word },
            city: word,
        };
        Object.keys(_cityParam).forEach(function (k) {
            _param[k] = self.state.param?.citySuggestParam[k];
        });
        Object.keys(_cityData).forEach(function (k) {
            _data[k] = self.state.param.citySuggestData[k];
        });

        var reqData = {
            host: this.state.param.citySuggestHost
                ? decodeURIComponent(this.state.param.citySuggestHost)
                : defaultHost,
            service:
                decodeURIComponent(this.state.param.citySuggestService || "") ||
                "/h-api/hotel/suggest/c",
            param: _param,
            // 攻略添加data参数不返回搜索数据
            data: this.state.prefix === "TRAVEL" ? {} : _data,
            ignoreStatus: true,
            success: (res) => {
                switch (this.state.prefix) {
                    case "FLIGHT": {
                        // 0
                        if (res.result && res.result.length) {
                            updateCities(
                                res.result && res.result.length,
                                res.result,
                            );
                        }
                        break;
                    }
                    case "TRAIN": {
                        // 1
                        if (res.dataMap.result && res.dataMap.result.length) {
                            res.dataMap.result.forEach((item) => {
                                item.name = item.display;
                            });
                            updateCities(
                                res.dataMap.result && res.dataMap.result.length,
                                res.dataMap.result,
                            );
                        }
                        break;
                    }
                    case "OTHER":
                    case "HOTEL": {
                        // 3
                        var hotelData = (res.data && res.data.result) || [];
                        var newData = [];

                        if (res.data && hotelData.length > 0) {
                            var key = res.data.userInput,
                                keyLen = key.length;
                            hotelData.forEach(function (item) {
                                var text = item.display,
                                    pos = text
                                        .toLowerCase()
                                        .indexOf(key.toLowerCase());
                                if (pos > -1) {
                                    //截取高亮展示文本，重组数据结构
                                    newData.push({
                                        key: item.key,
                                        display: item.display,
                                        cityUrl: item.cityUrl,
                                        en_gj: item.internal ? "en" : "gj",
                                        text: [
                                            {
                                                text: text.substr(0, pos),
                                                isHL: false,
                                            },
                                            {
                                                text: text.substr(pos, keyLen),
                                                isHL: true,
                                            },
                                            {
                                                text: text.substr(pos + keyLen),
                                                isHL: false,
                                            },
                                        ],
                                    });
                                } else {
                                    newData.push({
                                        key: item.key,
                                        cityUrl: item.cityUrl,
                                        display: item.display,
                                        en_gj: item.internal ? "en" : "gj",
                                        text: [
                                            {
                                                text,
                                                isHL: false,
                                            },
                                        ],
                                    });
                                }
                            });
                        }
                        updateCities(res.data, newData);
                        break;
                    }
                    case "TRAVEL": {
                        // 4
                        // var highlightInputResult = this.toHighLight(res.data.dists, 'name');
                        var highlightInputResult = [];
                        if (
                            res.data &&
                            res.data.dists &&
                            res.data.dists.length > 0
                        ) {
                            var trkey = word,
                                trkeyLen = trkey.length;
                            res.data.dists.forEach(function (item) {
                                var text = item.name,
                                    pos = text
                                        .toLowerCase()
                                        .indexOf(trkey.toLowerCase());
                                if (pos > -1) {
                                    //截取高亮展示文本，重组数据结构
                                    highlightInputResult.push({
                                        key: item.id,
                                        name: item.name,
                                        id: item.id,
                                        cityUrl: item.id,
                                        text: [
                                            {
                                                text: text.substr(0, pos),
                                                isHL: false,
                                            },
                                            {
                                                text: text.substr(
                                                    pos,
                                                    trkeyLen,
                                                ),
                                                isHL: true,
                                            },
                                            {
                                                text: text.substr(
                                                    pos + trkeyLen,
                                                ),
                                                isHL: false,
                                            },
                                        ],
                                    });
                                } else {
                                    highlightInputResult.push({
                                        key: item.trkey,
                                        name: item.name,
                                        id: item.id,
                                        cityUrl: item.id,
                                        text: [
                                            {
                                                text,
                                                isHL: false,
                                            },
                                        ],
                                    });
                                }
                            });
                        }
                        updateCities(
                            res.data && res.data.dists && res.data.dists.length,
                            highlightInputResult,
                        );
                        break;
                    }
                }
            },
        };
        request(reqData);
    };

    render() {
        return (
            <RealCitySelector
                onConditionChange={this.handleConditionChange}
                suggest={true}
                tab={
                    this.state.showOverseas && [
                        {
                            name: "国内城市",
                            onTap: this.changeCityToDomestic.bind(this),
                            active: this.state.activeCity === "domestic",
                        },
                        {
                            name: "海外城市",
                            onTap: this.changeCityToOverseas.bind(this),
                            active: this.state.activeCity === "overseas",
                        },
                    ]
                }
                current={{
                    show: this.state.showCurrent,
                    onTap: this.cityTap.bind(this),
                }}
                hot={{
                    data: this.state.hotCities,
                    onTap: this.cityTap.bind(this),
                }}
                all={{
                    data: this.state.allCities,
                    onTap: this.cityTap.bind(this),
                }}
                searchResults={{
                    data: this.state.cities,
                    onTap: this.cityTap.bind(this),
                }}
                history={{
                    data: this.state.historyCities,
                    onTap: this.cityTap.bind(this),
                }}
                placeholder={
                    this.state.param ? this.state.param.placeholder : ""
                }
            />
        );
    }
}

/**
 * RealCitySelector.props
 * type: 0: 机票 1: 火车票 2: 汽车票 3: 酒店 4: 攻略
 * citySuggestService: 搜索城市列表的路径， citySuggestService 和 citySuggestHost 会拼成一个完整的接口地址
 * citySuggestHost: 搜索城市列表的 host
 * cityListHost: 获取热门 / 全部城市的 host
 * cityListService: 获取热门 / 全部城市的路径， cityListHost 和 cityListService 会拼成一个完整的接口地址
 * eventType: 事件类型， 点击城市的时候， 会设置 globalData[eventType] = cityObj，并触发名称为 eventType 的回调函数，参数就为 cityObj
                cityObj = {
                    city: selectedCity, // 选择的城市
                    cityUrl: cityUrl, // 取自点击节点的 dataset
                    isDep: self.state.param.isDep,
                    source: source // 来源，取自点击节点的 dataset
                };
 * placeholder: 搜索框的 placeholder
 * isDep: true 出发城市 | false 到达城市
 */

export default CitySelector;
