
@import '@platformStyle/iconfont.scss';
@import '@platformStyle/components.scss';
// @import '../../pages/alonePlatform/wxPay/realNameAuth/index.scss';

.account-exceeding-container {
    overflow: hidden;
}
.excedding-image-wrapper {
    height: 85px;
    background-image: url('https://s.qunarzz.com/wechatapp/common/images/realNameAuth/trafficLight.jpg');
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: contain;
    margin: 25px 0 10px;
}

.head-section {
    text-align: center;
}

.account-exceeding-title {
    font-size: 36px;
    color: #333;
}

.account-exceeding-info {
    font-family: PingFangSC-Light;
    font-size: 16px;
    color: #212121;
    padding: 0 28px;
}

.account-list {
    margin: 50px 0 100px;
}

.account-list .item-unselect::after {
    content: '\f438';
    color: #00bcd4;
    font-size: 22.5px;
}

.account-list .item-selected::after {
    content: '\f3f6';
    color: #00bcd4;
    font-size: 22.5px;
}

.account-list .account-item {
    background-color: #f8fdfe;
    padding: 20px;
    border-top: 1px #dbf6f9 solid;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.account-list .account-name{
    font-family: PingFangSC-Medium;
    color: #333;
    font-size: 16px;
}

.account-list .account-number{
    font-family: PingFangSC-Light;
    font-size: 14px;
    color: #9e9e9e;
}

.account-exceeding-btn-wrapper {
    position: fixed;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 10px 15px;
    background-color: #fff;
}