import { Text, View } from "@tarojs/components";
import React from "react";
import "./index.scss";

class CountDown extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            lineEnd: false,
            date: "",
            endTime: null,
        };
        this.timer = null;
    }

    componentDidMount() {
        const time = this.props.time;
        this.setEndTime(time);
    }

    componentWillUnmount() {
        clearTimeout(this.timer);
    }

    zeroPadding = (num) => {
        num = num.toString();
        return num[1] ? num : "0" + num;
    };
    setEndTime = (newVal) => {
        this.setState(
            {
                endTime: new Date().getTime() + newVal * 1000 + 10,
                lineEnd: false,
            },
            () => {
                this.init();
            },
        );
    };

    init = () => {
        clearInterval(this.timer);
        this.getLatestTime.call(this);
    };

    getLatestTime = () => {
        const { endTime } = this.state;
        const time = endTime - new Date().getTime();

        setTime.call(this);
        clearTimeout(this.timer);
        if (time <= 0) {
            this._getTimeValue(0);
            clearTimeout(this.timer);
            this.CountdownEnd();
            return;
        }

        this.timer = setTimeout(() => {
            setTime.call(this);
            this.getLatestTime();
        }, 1000);

        function setTime() {
            const dateString = this._getTimeValue(time);
            // console.log('getLatestTime', dateString);
            this.setState({
                date: dateString,
            });
        }
    };

    _getTimeValue = (time) => {
        return this.formatTime(time / 1000);
    };

    formatTime = (time) => {
        const format = (n) => this.zeroPadding(Math.floor(n));
        const second = format(time % 60);
        const minute = format((time / 60) % 60);
        const hour = format(time / 3600);
        return `${hour}:${minute}:${second}`;
    };

    CountdownEnd = () => {
        this.setState({
            lineEnd: true,
        });
        if (this.props.time > 0) {
            this.props.onLineEnd && this.props.onLineEnd();
        }
    };

    render() {
        if (!this.props.time) return <Text></Text>;
        return (
            <View class="l-countdown l-class">
                {this.state.lineEnd ? (
                    <View class="l-countdown-item">{this.props.doneText}</View>
                ) : (
                    <View class="l-countdown-item">
                        <Text class="l-countdown-prefix">
                            {this.props.prefix}
                        </Text>
                        <Text class="l-countdown-time">{this.state.date}</Text>
                    </View>
                )}
            </View>
        );
    }
}

export default CountDown;
