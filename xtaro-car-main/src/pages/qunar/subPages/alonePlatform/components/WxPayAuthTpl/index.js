import { View, Button, Image } from "@tarojs/components";
import React from "react";
import "./index.scss";

class WxPayAuthTpl extends React.Component {
    constructor(props) {
        super(props);
    }
    render() {
        return (
            <View name="WxPayAuthTpl">
                <View className="auth-container">
                    <View className="auth-top-block">
                        <View className="auth-title">授权微信实名认证</View>
                        <View className="subtitle-wrapper">
                            <View className="subtitle-wrapper">
                                <View className="line left-line" />
                                <View className="square-icon" />
                            </View>
                            <View className="auth-subtitle">
                                认证信息享更多特权服务
                            </View>
                            <View className="subtitle-wrapper">
                                <View className="square-icon" />
                                <View className="line right-line" />
                            </View>
                        </View>
                    </View>
                    <View className="auth-bottom-block">
                        {this.props.showBusinessNewText ? (
                            <View className="auth-intro">
                                <View className="auth-intro-item">
                                    <View className="g-q-iconfont item-icon icon-1" />
                                    <View>
                                        <View className="item-title">
                                            提升安全等级
                                        </View>
                                        <View className="item-detail">
                                            防止个人信息冒用
                                        </View>
                                    </View>
                                </View>
                                <View className="auth-intro-item">
                                    <View className="g-q-iconfont item-icon icon-3" />
                                    <View>
                                        <View className="item-title">
                                            尊享金融特权
                                        </View>
                                        <View className="item-detail">
                                            先出行后付款
                                        </View>
                                    </View>
                                </View>
                                <View className="auth-intro-item">
                                    <View className="g-q-iconfont item-icon icon-2" />
                                    <View>
                                        <View className="item-title">
                                            尊享旅行权益
                                        </View>
                                        <View className="item-detail">
                                            分期旅行游世界
                                        </View>
                                    </View>
                                </View>
                            </View>
                        ) : (
                            <View className="auth-intro">
                                <View className="auth-intro-item">
                                    <View className="g-q-iconfont item-icon icon-1" />
                                    <View>
                                        <View className="item-title">
                                            提升安全等级
                                        </View>
                                        <View className="item-detail">
                                            去哪儿有效保障
                                        </View>
                                        <View className="item-detail">
                                            防止个人信息冒用
                                        </View>
                                    </View>
                                </View>
                                <View className="auth-intro-item">
                                    <View className="g-q-iconfont item-icon icon-2" />
                                    <View>
                                        <View className="item-title">
                                            尊享旅行特权
                                        </View>
                                        <View className="item-detail">
                                            酒店先住后付
                                        </View>
                                        <View className="item-detail">
                                            火车票先抢后付
                                        </View>
                                    </View>
                                </View>
                                <View className="auth-intro-item">
                                    <View className="g-q-iconfont item-icon icon-3" />
                                    <View>
                                        <View className="item-title">
                                            尊享金融特权
                                        </View>
                                        <View className="item-detail">
                                            20万随心借
                                        </View>
                                        <View className="item-detail">
                                            先消费后付款
                                        </View>
                                    </View>
                                </View>
                            </View>
                        )}

                        <View className="btn-wrapper">
                            {this.props.guideLogin ? (
                                <Button
                                    class="auth-btn"
                                    onClick={this.props.gotoLogin}
                                >
                                    去登录
                                </Button>
                            ) : (
                                <Button
                                    open-type="getRealnameAuthInfo"
                                    onGetrealnameauthinfo={
                                        this.props.getAuthToken
                                    }
                                    category-id={[110, 116]}
                                    className="auth-btn"
                                >
                                    去授权
                                </Button>
                            )}
                        </View>
                    </View>
                    <View className="bottom-logo">
                        <Image src="https://s.qunarzz.com/wechatapp/common/images/realNameAuth/qunarLogo.png" />
                    </View>
                </View>
            </View>
        );
    }
}

export default WxPayAuthTpl;
