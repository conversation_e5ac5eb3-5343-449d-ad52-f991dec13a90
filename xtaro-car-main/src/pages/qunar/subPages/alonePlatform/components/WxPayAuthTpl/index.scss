@import '@platformStyle/iconfont.scss';
@import '@platformStyle/components.scss';
// @import '../../pages/alonePlatform/wxPay/realNameAuth/index.scss';



.auth-top-block {
    background-image: url('https://s.qunarzz.com/wechatapp/common/images/realNameAuth/auth-bg-new.png');
    background-size: cover;
    height: 260px;
    overflow: hidden;
}
.auth-title {
    font-family: PingFangSC-Medium;
    font-size: 36px;
    color: #FFFFFF;
    text-align: center;
    height: 50px;
    margin: 42px 0 4px;
}
.subtitle-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}
.auth-subtitle {
    font-family: PingFangSC-Regular;
    font-size: 16px;
    color: #FFFFFF;
    text-align: center;
    padding: 0 11px;
}
.square-icon {
    width: 5px;
    height: 5px;
    background-color: rgba(255,255,255,0.8);
    transform: rotate(45deg);
}

.subtitle-wrapper .line {
    width: 45px;
    height: 1px;
    margin: 0 4px;
}

.subtitle-wrapper .left-line {
    background: linear-gradient(to right, #0BB82E, rgba(255,255,255,0.6)); 
}

.subtitle-wrapper .right-line {
    background: linear-gradient(to right, rgba(255,255,255,0.6), #0BB82E);  
}

.auth-bottom-block {
    width: 345px;
    border-radius: 5px;
    box-shadow: 0 6px 20px 0 rgba(0,0,0,0.08);
    overflow: hidden;
    padding: 45px 0;
    margin: 0 auto;
    background-color: #FFF;
    position: relative;
    top: -100px;
}

.auth-intro {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.auth-intro-item {
    text-align: center;
}

.auth-intro-item .item-icon {
    width: 55px;
    height: 55px;
    margin: 0 auto 9px;
    opacity: 0.72;
    border: 0.5px solid rgba(255,152,0,0.26);
    border-radius: 27.5px;
    box-shadow: 0 0 11px 0 rgba(255,152,0,0.14);
    font-size: 32px;
    color: transparent;
    background-image: linear-gradient(-225deg, #FAD961 0%, #F47910 100%);
    -webkit-background-clip: text;
    background-clip: text;
    display: inline-block;
    line-height: 55px;
    text-align: center;
}

.icon-1::before {
    content: '\e12d';
}

.icon-2::before {
    content: '\e329';
}

.icon-3::before {
    content: '\f08f';
}

.auth-intro-item .item-title {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #000000;
    text-align: center;
    margin-bottom: 4px;
}

.auth-intro-item .item-detail {
    font-family: PingFangSC-Regular;
    font-size: 11px;
    color: #888888;
    text-align: center;
    line-height: 16px;
}

.btn-wrapper {
    padding: 32px 20px 0;
}

.bottom-logo {
    position: fixed;
    bottom: 25px;
    display: block;
    width: 100%;
    text-align: center;
}
.bottom-logo image{
    width: 99px;
    height: 37.5px;
}
