import { View, Text, Input, Button } from "@tarojs/components";
import { getCurrentInstance } from "@tarojs/taro";
import React from "react";
import { sendUbt, reportErrorLog } from "@/common/utils/pay/utils/midstage.js";
import { sendSms, checkSms } from "./common/SmsController.js";
// import paymentStore from '../../common/utils/cqpay/models/stores.js';
import paymentStore from "./common/stores.js";
import "./index.scss";

function generatePhoneAreaCodeText(areaCode) {
    let result = areaCode ? `${areaCode}` : "";

    if (result && result[0] === "+") {
        // 如果第一个字符为 + 号，去掉 + 号
        result = result.substring(1);
    }
    if (result && result === "86") {
        result = ""; // 86 不展示
    } else if (result && result !== "86") {
        result = `+${result} `; // 注意带空格
    }
    return result;
}

const payParamsStore = paymentStore.PayParamsStore();
class SmsCmp extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            codeValue: "", // 验证码的值
            countDown: 0, // 倒计时时间

            // 短信输入区

            item: "",
            // 渲染用数组
            inputItems: new Array(6).fill(""),
            // 聚焦到input
            focus: true,
            phoneAreaCodeText: generatePhoneAreaCodeText(
                this.props.sendPhoneAreaCode,
            ),
        };
    }

    // props属性
    // showPhoneNo
    // payToken
    // sendPhone

    componentDidMount() {
        try {
            const currentPage = getCurrentInstance().router;
            const currentPath = currentPage.path;
            const isPaynew = currentPath
                ? currentPath.includes("pages/qunar/subPages/alonePlatform/pay")
                : false;
            // eslint-disable-next-line no-console
            console.log("currentPath: ", currentPath);
            if (currentPath && !isPaynew) {
                reportErrorLog({
                    errorType: "30012_7",
                    errorMessage: "SmsCmp组件被异常加载了",
                    extendInfo: {
                        currentPath,
                        payToken: this.props.payToken,
                    },
                });
                if (
                    process.env.ANU_ENV === "ali" ||
                    process.env.ANU_ENV === "wx"
                ) {
                    // TODO: 支付宝余额支付 bug，支付页面componentDidMount有可能会在别的页面被触发，应该是框架问题，但是框架暂时没办法解决
                    // 这里临时解决办法是判断只有当前页面时支付路由时才继续走，否则直接退出
                    // 等框架修复了这个问题可以把这段逻辑去掉
                    return;
                }
            }
        } catch (e) {
            // eslint-disable-next-line no-console
            console.error("currentPath error", e);
        }
        // 在组件实例进入页面节点树时执行
        const smsCountDownDate = payParamsStore.getAttr("smsCountDownDate");
        const nowDate = new Date().getTime();
        if (smsCountDownDate && nowDate - smsCountDownDate < 60 * 1000) {
            this.startCountDown(
                60 - ((nowDate - smsCountDownDate) / 1000).toFixed(0),
            );
        } else {
            payParamsStore.setAttr("smsCountDownDate", "");
            this.getSms();
        }
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.sendPhoneAreaCode !== this.props.sendPhoneAreaCode) {
            this.setState({
                phoneAreaCodeText: generatePhoneAreaCodeText(
                    nextProps.sendPhoneAreaCode,
                ),
            });
        }
    }

    componentWillUnmount() {
        // 在组件实例被从页面节点树移除时执行
        this.dealCountNumber();
    }

    dealCountNumber = () => {
        // 如果倒计时未结束，先缓存倒计时开始时间戳
        if (this.state.countDown > 0) {
            payParamsStore.setAttr(
                "smsCountDownDate",
                new Date().getTime() - (60 - this.state.countDown) * 1000,
            );
        } else {
            payParamsStore.setAttr("smsCountDownDate", "");
        }
    };

    onSubmit = async () => {
        sendUbt({
            a: "c_pay_sms_submit",
            type: "click",
            dd: "短信点击提交",
            codes: this.state.codeValue,
        });
        if (this.state.codeValue.length < 6) return;
        const res = await checkSms({
            codes: this.state.codeValue,
        });
        this.props.onSmsSubmitpay && this.props.onSmsSubmitpay(res);
    };

    // 发送短信
    getSms = async () => {
        const res = await sendSms({
            payToken: this.props.payToken,
            sendPhone: this.props.sendPhone,
            sendPhoneAreaCode: this.props.sendPhoneAreaCode,
        });
        if (res.result == "success") {
            this.startCountDown();
        }
    };

    // 倒计时
    startCountDown = (count = 60) => {
        this.setState({
            countDown: count,
        });
        const timer = setInterval(() => {
            let countDown = this.state.countDown;
            countDown--;
            this.setState({
                countDown,
            });
            if (countDown <= 0) {
                clearInterval(timer);
            }
        }, 1000);
    };
    // 重新获取短信
    onResend = () => {
        sendUbt({
            a: "c_pay_sms_resend",
            type: "click",
            dd: "短信点击重新发送",
        });
        this.getSms();
    };

    onInput = (e) => {
        const value = e.detail.value.replace(/[\D]/g, "");
        this.setState({
            codeValue: value,
            inputItems: value.padEnd(6).split(""),
        });
    };
    // 整个组件点击事件： 定位到输入框
    onBoxTap = () => {
        this.setState({
            focus: true,
        });
    };
    onBlur = () => {
        this.setState({ focus: false });
    };

    onClose = () => {
        this.props.onSmsClose && this.props.onSmsClose();
    };

    render() {
        return (
            <View class="sms-comp">
                <View class="sms-back-modal"></View>
                <View class="sms-main">
                    {/* <!-- 标题栏 --> */}
                    <View class="sms-head">
                        <View
                            class="sms-icon close"
                            onClick={this.onClose}
                        ></View>
                        <View class="sms-title">请输入短信验证码</View>
                    </View>

                    {/* <!-- 提示文案 --> */}
                    <View class="sms-tip-wrap">
                        <Text>
                            验证码已发送至{this.state.phoneAreaCodeText}
                            {this.props.showPhoneNo}
                        </Text>
                        {this.state.countDown > 0 ? (
                            <View>{this.state.countDown}s后重发</View>
                        ) : (
                            <View onClick={this.onResend} class="resend">
                                重新获取
                            </View>
                        )}
                    </View>

                    {/* <!-- 输入区 --> */}
                    <View>
                        <View class="code-input" onClick={this.onBoxTap}>
                            {this.state.inputItems.map((item, idx) => (
                                <View class="input-item">
                                    {item && item != " " ? (
                                        <View class="input-number">{item}</View>
                                    ) : null}
                                    {idx == this.state.codeValue.length ? (
                                        <View class="cursor"></View>
                                    ) : null}
                                </View>
                            ))}
                            <Input
                                class="hidden-input"
                                type="number"
                                value={this.state.codeValue}
                                onInput={this.onInput}
                                onBlur={this.onBlur}
                                maxlength="6"
                                hold-keyboard
                                focus={this.state.focus}
                            />
                        </View>
                    </View>

                    {/* <!-- 提交按钮 --> */}
                    <Button
                        onClick={this.onSubmit}
                        class={`submit-btn ${this.state.codeValue.length == 6 && "active"}`}
                    >
                        确定
                    </Button>
                </View>
            </View>
        );
    }
}

export default SmsCmp;
