// @import '../../style/iconfont.scss';
@import '@platformStyle/iconfont.scss';
.anu-row {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.anu-col {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.anu-col-flex {
    flex-direction: column;
}

.anu-center {
  justify-content: center;
}

.anu-middle {
  align-items: center;
}

.anu-flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

page {
  height: 100%;
}

.anu-city-selector {
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.city-container {
  // background-color: #efeff4;
  width: 100%;
  height: 100%;

  .search-content {
    background-color: #ffffff;
    width: 100%;
    z-index: 91;
    .search-content-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: nowrap;
    }
  }
  .list-content {
      flex-direction: column;
      flex: 1;
      overflow: auto;
      width: 120%;
  }
  .input-content {
    height: 30px;
    margin: 5px 10px;
    display: flex;
    background-color: #efeff4;
    flex:1;
    border-radius: 5px;
    transition: all ease-in-out 0.3s;
  }

  .input-div {
    padding-left:5px;
    width: 100%;
  }

  .fixed-content {
    position: fixed;
    width: 100%;
  }


  /*.image-search {
      position: absolute;
      left: 16rpx;
      top: 16rpx;
      width: 28rpx;
      height: 28rpx;
  }*/

  .icon-search {
    position: absolute;
    left: 8px;
    top: 8px;
    width: 14px;
    height: 14px;
    line-height: 14px;
  }

  .icon-search:before {
    content: '\e082';
    color: #ccc;
    font-size: 16px;
  }

  .input-view {

    background-color: #efeff4;
    left: 25px;
    right: 5px;
    height: 30px;
  }

  .input-city {
    width: 100%;
    height: 100%;
    font-size: 14px;
    border-radius: 4px;
    background-color: transparent;
  }

  .cityTab {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 44px;
    font-size: 16px;
    background-color: #ffffff;

    color: #616161;
    line-height: 22px;
  }

  .cityTab-content {
    border-bottom: 1px solid #e5e5e5;
  }

  .domestic,
  .overseas {
    width: 80px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    margin: 0 8px;
  }
  .cityActive {
    color: #00afc7;
    border-bottom: 1px solid #00afc7;
  }

  .relocation-icon {
    font-size: 14px;
    vertical-align: middle;
  }
  .relocation-icon::before {
    content: '\f055';
  }

  // .relocation:before {
  //   content: '\f055';
  //   font-size: 28rpx;
  //   vertical-align: middle;
  // }

  .cancel {
    text-align: center;
    width: 52px;
    line-height: 44px;
    font-size: 15px;
    color: #00bcd4;
    transition: all ease-in-out 0.3s;
  }

  .cancel-before {
    margin-right:0;
  }

  .cancel-after {
    transform: translateX(52px);
  }

  .list-content-low {
    margin-top: 88px;
  }

  .list-content-high {
    margin-top: 44px;
  }

  .city-content {
    background-color: #ffffff;
    height: 45px;
    width: 100%;
  }
  .city-search-list {
    position: fixed;
    top: 44px;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 90;
    border-bottom: 1px solid #e5e5e5;
    background-color: rgba(0, 0, 0, 0.4);
    width: 100%;
  }
  .search-list-wrapper {
    background-color: #efeff4;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    width: 100%;
  }
  .city-view {
    font-size: 14px;
    color: #666666;
    line-height: 44px;
    padding-left: 20px;
    border-top: 1px solid #e5e5e5;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%;
  }
  .city-view .qt-blue {
    color: #29bcce;
  }
  .city-view-disable {
    font-size: 12px;
    line-height: 29px;
    color: #999999;
    background-color: #f0eff5;
  }
  .city-none {
    font-size: 14px;
    color: #aaaaaa;
    margin: 10px 0;
    text-align: center;
  }

  .hot-city,
  .history-content {
    display: flex;
    flex-wrap: wrap;
  }

  .label-title {
    color: #333333;
    font-size: 14px;
    height: 44px;
    line-height: 44px;
    margin-left: 5px;
  }
  .city-wrapper {
    margin: 0 10px;
  }
  .hotCity-content {
    height: 45px;
    width: 100%;
    display: flex;
  }

  .hotCity-label {
    color: #333333;
    font-size: 14px;
    height: 35px;
    width: 108px;
    margin: 0 5px 10px 5px;
    display: inline-block;
    background-color: #efeff4;
    line-height: 35px;
    text-align: center;
    border-radius: 3px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .hotCity-label:active,
  .city-label:active {
    background-color: #999999;
  }
  .hotCity-label-empty {
    background-color: #efeff4;
    width: 100%;
    height: 35px;
    line-height: 35px;
  }
  .all-city-hide {
    display: none;
  }
  .all-city .label-title {
    padding-left: 10px;
    // background-color: #efeff4;
  }
  .all-city .citylist {
    padding-left: 37.5px;
    border-top: 0.5px solid #e5e5e5;
    border-bottom: 0.5px solid #e5e5e5;
    background-color: #fff;
  }
  .all-city .city-line {
    padding: 14px 0 14px 20px;
    font-size: 14px;
    border-bottom: 0.5px solid #e5e5e5;
  }
  // .all-city .city-line:last-child {
  //     // border: none;
  // }
  .city-fail {
    width: 100%;
    padding: 20px 0;
    text-align: center;
    font-size: 14px;
    color: #00bcd4;
  }

  .city-module_letter{
    position:fixed;
    right:0;
    top: 100px;
    width:25px;
    background-color:#fff;
    z-index:10;
    box-sizing:border-box;
    padding-left:8px;


  }

  .city-module_letter-text {
    color:#000000;
    font-size:10px;
    text-align: center;
    font-weight: 600

  }

  .city-module_letter-key {
    display: flex;
    flex-direction: column;
    align-content: flex-end;
    height:16px;
    justify-content:center;

  }
}
