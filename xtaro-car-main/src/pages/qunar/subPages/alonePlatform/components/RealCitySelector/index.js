import { View, Input, Text, ScrollView } from "@tarojs/components";
import Taro, {
    setStorage,
    showModal,
    showToast,
    vibrateShort,
} from "@tarojs/taro";
import React from "react";
import getLocation from "@/common/utils/location.js";
import "./index.scss";
import utils from "@/common/utils/util";
const CURRENT_CITY = "_CURRENT_CITY";
const PREFIX = "_QUNAR_CITYSELECTER_STORAGE_FREFIX";
const ITEM_HEIGHT = 16;

class RealCitySelector extends React.Component {
    constructor(props) {
        super(props);
        this.showCurrent = true;
        this.startIndex = 0;
        this.startY = "";
        this.state = {
            inputValue: "", // input 输入值
            isFocus: false, // 焦点在 input 上
            isSearch: false, // 正在搜索
            noResult: true, // 是否有搜索结果
            currentCity: null, // 当前城市
            height: "",
            env: process.env.ANU_ENV,
            itemHeight: ITEM_HEIGHT, // 每个侧边栏item的高度
        };

        this.handleFocus = this.handleFocus.bind(this);
        this.handleBlur = this.handleBlur.bind(this);
        this.handleKeyInput = this.handleKeyInput.bind(this);
        this.handelCancel = this.handelCancel.bind(this);
    }

    async componentDidMount() {
        const { windowHeight, windowWidth } = await utils.getSystemInfo();

        this.setState({
            height: `${(windowHeight * 750) / windowWidth}rpx`,
        });
    }

    // 获取当前城市
    getCurrentCity = () => {
        getLocation({
            success: (data) => {
                var cityName = data.city || "";
                this.setState({
                    currentCity: cityName.replace(/市$/, ""),
                });
                setStorage({
                    key: PREFIX + CURRENT_CITY,
                    data: cityName.replace(/市$/, ""),
                });
            },
            fail: () => {
                this.setState({
                    currentCity: "",
                });
                showModal({
                    title: "提示",
                    content:
                        "请确认您的手机是否打开定位服务且允许访问。如您确认已启动服务，请稍后重试",
                    showCancel: false,
                    cancelText: "取消",
                    confirmText: "确定",
                });
            },
            complete: () => {
                this.setState({
                    isShow: true,
                });
            },
        });
    };

    // 读取当前城市
    readCurrentCity = () => {
        this.getCurrentCity();
    };
    // 重新获取当前位置信息
    relocation = () => {
        this.getCurrentCity();
    };

    handleFocus = () => {
        if (!this.state.isFocus) {
            this.setState({
                isFocus: true,
                isSearch: true,
            });
        }
    };

    handleBlur = () => {
        var word = this.state.inputValue;
        if (word.length > 0) {
            this.setState({
                isFocus: false,
            });
        } else {
            this.setState({
                isFocus: false,
                isSearch: false,
                noResult: false,
            });
        }
    };

    handleKeyInput = (e) => {
        const { onConditionChange } = this.props;
        var word = e.currentTarget.value.replace(/\s/g, "");
        if (word.length > 0) {
            this.setState({
                isFocus: true,
                isSearch: true,
                inputValue: word,
                noResult: false,
            });
        } else {
            this.setState({
                isSearch: false,
                inputValue: word,
                noResult: false,
            });
        }
        onConditionChange && onConditionChange(word, e);
    };

    handelCancel = () => {
        if (process.env.ANU_ENV === "quick") {
            this.wx.$element("ANU_INPUT").focus({ focus: false });
        }
        this.setState({
            isSearch: false,
            isFocus: false,
            noResult: false,
            inputValue: "",
        });
        const { onConditionChange } = this.props;
        onConditionChange && onConditionChange(null);
    };

    componentWillReceiveProps(nextProps) {
        if (nextProps.current && nextProps.current.show && this.showCurrent) {
            this.showCurrent = false;
            this.readCurrentCity();
        }

        // nextProps.current && nextProps.current.show && this.readCurrentCity();
    }
    clickNavigate = (cityTag) => {
        this.setState({
            toView: cityTag,
        });
        showToast({
            title: cityTag,
            icon: "none",
            duration: 800,
        });
    };

    handelCityTab = (tab) => {
        let toView = this.props.current.show ? "positon" : "hot";
        this.setState({
            toView,
        });
        tab && tab();
    };

    letternMove = (e) => {
        //滑动变化坐标
        let touchMoveY = e.pageY - this.startY;
        // 滑动变化距离
        let touchY = Math.round(touchMoveY / ITEM_HEIGHT);
        // 当前移动的位置
        let current = this.props.all.data[this.startIndex + touchY];
        if (current?.key !== this.currentKey) {
            showToast({
                title: current?.key,
                icon: "none",
                duration: 200,
            });
            vibrateShort();
        }
        this.currentKey = current?.key;
    };

    letterStart = (index, e) => {
        this.startIndex = index;
        this.startY = e.pageY;
    };

    letterEnd = () => {
        if (this.currentKey) {
            this.setState({
                toView: this.currentKey,
            });
        }
    };

    render() {
        return (
            <View
                class="anu-city-selector"
                style={{ height: this.state.height }}
            >
                <View class="city-container anu-col">
                    {/* 搜索 */}
                    {this.props.suggest && (
                        <View class="search-content">
                            <View class="search-content-wrapper">
                                <View class="input-content">
                                    <View class="input-div">
                                        {/* 输入框 */}
                                        <Input
                                            id="ANU_INPUT"
                                            class="input-city"
                                            value={this.state.inputValue}
                                            placeholder={this.props.placeholder}
                                            placeholder-style="color:#b2b2b2;"
                                            onFocus={this.handleFocus}
                                            onBlur={this.handleBlur}
                                            onInput={this.handleKeyInput}
                                        />
                                    </View>
                                </View>
                                {/* 取消按钮 */}
                                {(this.state.isSearch ||
                                    this.state.isFocus) && (
                                    <View
                                        class={
                                            "cancel" +
                                            (this.state.isSearch ||
                                            this.state.isFocus
                                                ? " cancel-before"
                                                : " cancel-after")
                                        }
                                        style={{ width: "104rpx" }}
                                        onClick={this.handelCancel}
                                    >
                                        <Text>取消</Text>
                                    </View>
                                )}
                            </View>
                        </View>
                    )}
                    {/* Search Result */}
                    {(this.state.isSearch || this.state.isFocus) &&
                        this.props.searchResults && (
                            <ScrollView
                                class="search-list city-search-list"
                                scroll-y
                            >
                                <View
                                    type="serach-content"
                                    class="search-list-wrapper"
                                    style={{
                                        height:
                                            this.props.searchResults.data &&
                                            this.props.searchResults.data
                                                .length > 0
                                                ? "100%"
                                                : "auto",
                                    }}
                                >
                                    {this.props.searchResults.data &&
                                        this.props.searchResults.data.map(
                                            (city, index) => {
                                                if (city.name) {
                                                    return (
                                                        <View
                                                            key={
                                                                city.id || index
                                                            }
                                                            onClick={
                                                                this.props
                                                                    .searchResults
                                                                    .onTap
                                                            }
                                                            data-city={
                                                                city.name
                                                            }
                                                            data-cityurl={
                                                                city.cityUrl ||
                                                                ""
                                                            }
                                                            data-flightcity={
                                                                city.key
                                                            }
                                                            data-position={
                                                                city.en_gj || ""
                                                            }
                                                            class="city-content"
                                                        >
                                                            <View class="city-view">
                                                                {city.name}
                                                            </View>
                                                            {index ===
                                                            this.props
                                                                .searchResults
                                                                .data.length -
                                                                1 ? (
                                                                <View class="line-view" />
                                                            ) : (
                                                                <View class="city-line-view" />
                                                            )}
                                                        </View>
                                                    );
                                                } else if (city.text) {
                                                    return (
                                                        <View
                                                            key={city || index}
                                                            class="city-content city-view qt-font13 suggest-line"
                                                            onClick={
                                                                this.props
                                                                    .searchResults
                                                                    .onTap
                                                            }
                                                            data-city={city.key}
                                                            data-cityurl={
                                                                city.cityUrl
                                                            }
                                                            data-display={
                                                                city.display ||
                                                                ""
                                                            }
                                                            data-position={
                                                                city.en_gj || ""
                                                            }
                                                        >
                                                            {city.text.map(
                                                                (item) => {
                                                                    return (
                                                                        <Text
                                                                            key={
                                                                                item
                                                                            }
                                                                            class={
                                                                                item.isHL
                                                                                    ? "qt-blue"
                                                                                    : ""
                                                                            }
                                                                        >
                                                                            {
                                                                                item.text
                                                                            }
                                                                        </Text>
                                                                    );
                                                                },
                                                            )}
                                                        </View>
                                                    );
                                                }
                                            },
                                        )}
                                </View>
                            </ScrollView>
                        )}

                    {/* Tab */}
                    {this.props.tab ? (
                        <View class="cityTab-content">
                            <View class="cityTab">
                                {this.props.tab.map((item) => {
                                    return (
                                        <View
                                            key={item.name}
                                            class={
                                                "domestic anu-flex-center " +
                                                (item.active
                                                    ? "cityActive"
                                                    : "")
                                            }
                                            onClick={this.handelCityTab.bind(
                                                this,
                                                item.onTap,
                                            )}
                                        >
                                            {item.name}
                                        </View>
                                    );
                                })}
                            </View>
                        </View>
                    ) : null}
                    {/* 侧边导航栏 */}
                    {this.state.env !== "quick" && (
                        <View
                            class="city-module_letter"
                            onTouchend={this.letterEnd}
                        >
                            {this.props.all &&
                                this.props.all.data &&
                                this.props.all.data.map((item, index) => {
                                    return (
                                        <View
                                            class="city-module_letter-key"
                                            style={{
                                                height:
                                                    this.state.itemHeight +
                                                    "PX",
                                            }}
                                            catchTap={this.clickNavigate.bind(
                                                this,
                                                item.key,
                                            )}
                                            catchTouchmove={this.letternMove}
                                            onTouchstart={this.letterStart.bind(
                                                this,
                                                index,
                                            )}
                                        >
                                            {item.key && (
                                                <Text class="city-module_letter-text">
                                                    {item.key}
                                                </Text>
                                            )}
                                        </View>
                                    );
                                })}
                        </View>
                    )}

                    <ScrollView
                        class="list-content"
                        scroll-into-view={this.state.toView}
                        scroll-y
                        style={{ height: this.state.height }}
                    >
                        <View class="anu-col" type="content">
                            {/* 当前城市 */}
                            {this.props.current && this.props.current.show ? (
                                <View class="anu-col-flex">
                                    <View class="label-title" id="positon">
                                        当前城市
                                    </View>
                                    {this.props.current.name ||
                                    this.state.currentCity ? (
                                        <View
                                            class="hotCity-label anu-flex-center"
                                            data-city={
                                                this.props.current.name ||
                                                this.state.currentCity
                                            }
                                            data-source="current"
                                            onClick={this.props.current.onTap}
                                        >
                                            {this.props.current.name ||
                                                this.state.currentCity}
                                        </View>
                                    ) : (
                                        <View
                                            class="hotCity-label anu-flex-center"
                                            onClick={this.relocation}
                                            style={{ color: "#00AFC7" }}
                                        >
                                            <Text class="g-q-iconfont relocation-icon">
                                                &#xf055;
                                            </Text>
                                            <Text class="relocation">
                                                重新定位
                                            </Text>
                                        </View>
                                    )}
                                </View>
                            ) : null}
                            {/* 历史城市 */}
                            {this.props.history &&
                                this.props.history.data &&
                                this.props.history.data.length > 0 && (
                                    <View class="anu-col-flex">
                                        <View class="label-title" id="history">
                                            历史城市
                                        </View>
                                        <View class="anu-row ">
                                            {this.props.history.data.map(
                                                (item) => {
                                                    return (
                                                        <View
                                                            class="hotCity-label anu-flex-center"
                                                            key={
                                                                item.city ||
                                                                item
                                                            }
                                                            data-city={
                                                                item.city ||
                                                                item
                                                            }
                                                            data-cityurl={
                                                                item.cityUrl ||
                                                                ""
                                                            }
                                                            data-position={
                                                                item.en_gj || ""
                                                            }
                                                            data-display={
                                                                item.display ||
                                                                ""
                                                            }
                                                            onClick={
                                                                this.props
                                                                    .history
                                                                    .onTap
                                                            }
                                                            data-source="history"
                                                        >
                                                            {item.city || item}
                                                        </View>
                                                    );
                                                },
                                            )}
                                        </View>
                                    </View>
                                )}
                            {/* 热门城市 */}
                            {this.props.hot && (
                                <View class="anu-col-flex">
                                    <View class="label-title" id="hot">
                                        热门城市
                                    </View>
                                    <View class="anu-row ">
                                        {this.props.hot.data &&
                                            this.props.hot.data.map((item) => {
                                                return (
                                                    <View
                                                        class="hotCity-label city-package anu-flex-center"
                                                        key={item.name || item}
                                                        data-city={
                                                            item.name || item
                                                        }
                                                        data-cityurl={
                                                            item.cityUrl
                                                        }
                                                        data-position={
                                                            item.en_gj || ""
                                                        }
                                                        onClick={
                                                            this.props.hot.onTap
                                                        }
                                                    >
                                                        <View class="city-grid">
                                                            {item.name || item}
                                                        </View>
                                                    </View>
                                                );
                                            })}
                                    </View>
                                </View>
                            )}

                            {/* 所有城市 */}
                            {this.props.all && (
                                <View class="all-city anu-col-flex">
                                    {this.props.all.data &&
                                        this.props.all.data.map(
                                            (item, index) => {
                                                if (item.key) {
                                                    return (
                                                        <View
                                                            class="anu-col"
                                                            key={item.key}
                                                            id={item.key}
                                                        >
                                                            <View class="label-title title">
                                                                {item.key}
                                                            </View>
                                                            <View class="citylist anu-col-flex">
                                                                {item.value.map(
                                                                    (
                                                                        listItem,
                                                                        i,
                                                                    ) => {
                                                                        return (
                                                                            <View
                                                                                class="city-line"
                                                                                key={
                                                                                    listItem.name ||
                                                                                    listItem
                                                                                }
                                                                                data-city={
                                                                                    listItem.name ||
                                                                                    listItem
                                                                                }
                                                                                data-cityurl={
                                                                                    listItem.cityUrl
                                                                                }
                                                                                data-position={
                                                                                    listItem.en_gj ||
                                                                                    ""
                                                                                }
                                                                                data-index={
                                                                                    i
                                                                                }
                                                                                onClick={
                                                                                    this
                                                                                        .props
                                                                                        .all
                                                                                        .onTap
                                                                                }
                                                                            >
                                                                                {listItem.name ||
                                                                                    listItem}
                                                                            </View>
                                                                        );
                                                                    },
                                                                )}
                                                            </View>
                                                        </View>
                                                    );
                                                } else {
                                                    return (
                                                        <View class="citylist anu-col">
                                                            <View
                                                                class="city-line"
                                                                data-city={item}
                                                                data-cityurl={
                                                                    item.cityUrl
                                                                }
                                                                data-index={
                                                                    index
                                                                }
                                                                onClick={
                                                                    this.props
                                                                        .all
                                                                        .onTap
                                                                }
                                                            >
                                                                {item}
                                                            </View>
                                                        </View>
                                                    );
                                                }
                                            },
                                        )}
                                </View>
                            )}
                        </View>
                    </ScrollView>
                </View>
            </View>
        );
    }
}

/**
 * RealCitySelector props
 * onConditionChange: 搜索框内容改变时触发
 * tab: 水平分页
[
    {
        name: 名称,
        onTap: Function,
        active: 是否为当前所在分页
    },
    ...
]
 * current
{
    show: true | false 是否显示当前城市
    name: 城市名称，优先级高于 this.state.currentCity
    onTap: Function
}
 * hot: 热门城市
{
    data: [{
        name: 城市名称
    } | 城市名称]
}
 * history: 历史城市
{
    data: [
        { name: 城市名称 } | 城市名称
    ]
}
 * all: 全部城市
        {
            data: [
                {
                    key: 'A',
                    value: [
                        cityUrl: 'aba',
                        group: 'A',
                        en_gi: 'en',
                        name: '阿坝'
                    ]
                },
                ...
            ],
            onTap: Function
        }
 * searchResults: 搜索结果
{
    data: [
        {
            cityUrl: 'haerbin',
            key: '哈尔滨'
        }
    ]
}
 * placeholder: 搜索框 placeholder
 */

export default RealCitySelector;
