import { View } from "@tarojs/components";
import { reLaunch, getCurrentInstance } from "@tarojs/taro";
import React from "react";
import util from "@/common/utils/util";

class P extends React.Component {
    constructor(props){
        super(props);
        this.currentInstance = getCurrentInstance();
    }
    static outerQuery =
        process.env.ANU_ENV === "quick"
            ? {
                  filter: "",
              }
            : {};

    componentDidMount() {
        let url = "/pages/qunar/subPages/orderList/orderList/index?";
        // if ( process.env.ANU_ENV === 'wx' ){ // 多端订单列表页
        //     url = '/pages/order_remax/list/index?';
        // }
        reLaunch({
            url:
                url +
                util.stringifyURLParam(this.currentInstance.router.params),
        });
    }
    render() {
        return (
            <View style={{ backgroundColor: "#FFFFFF", height: "100%" }}></View>
        );
    }
}
export default P;
