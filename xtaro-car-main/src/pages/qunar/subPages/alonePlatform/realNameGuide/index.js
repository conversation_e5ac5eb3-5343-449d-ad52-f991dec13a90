import { View, Image } from "@tarojs/components";
import { getCurrentInstance, navigateBack } from "@tarojs/taro";
import React from "react";
import WxRealNameGuideTpl from "../components/WxRealNameGuide/index";
import "./index.scss";
import auth from "../common/utils/auth";
import EventEmitter from "@/common/utils/EventEmitter";
import { addRealNameListener } from "@/common/utils/pay/utils/realname";

class RealNameGuide extends React.Component {
    constructor(props) {
        super(props);
        this.currentInstance = getCurrentInstance();
        console.log(props, "props");
        this.state = {
            disturbanceUrl: this.currentInstance.router.params
                ? decodeURIComponent(
                      this.currentInstance.router.params.disturbanceUrl || "",
                  )
                : "",
        };

        this.gotoRealName = this.gotoRealName.bind(this);
    }
    componentWillUnmount() {
        EventEmitter.dispatch("onAuthCompleteToGuide");
    }
    componentWillMount() {
        addRealNameListener("onAuthComplete", () => {
            navigateBack({});
        });
    }
    gotoRealName = (status) => {
        //status 是否操作实名0不实名1去实名
        if (status) {
            auth.openAuth("cashier", this.state.disturbanceUrl);
        } else {
            //暂不实名
            navigateBack({});
        }
    };
    render() {
        return (
            <View class="guide-container">
                <View class="title">
                    <Image
                        class="pay-icon"
                        src="https://s.qunarzz.com/mobile_pay/image/wachatapp/paysuccess.png"
                    />
                    支付提交成功
                </View>
                <WxRealNameGuideTpl gotoRealName={this.gotoRealName} />
            </View>
        );
    }
}

export default RealNameGuide;
