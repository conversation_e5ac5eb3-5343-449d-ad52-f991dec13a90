
@font-face {
  font-family:"ctrip-number-bold";
  src: url('https://s.qunarzz.com/member_mobile/fonts/crn_font_ctripNumberBold.ttf') format('truetype');
}

.page {
  background-color: #eeeeee;
  min-height: 100vh;
}

.pay-index-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: rgb(239, 239, 239);
}

.pay-order-detail {
  padding: 17px 0 28px;
}

.detail-titlebox {
  font-family: ctrip-number-bold;
  padding: 0 15px 0;
}

.pay-order-detail .title {
  font-size: 18px;
  color: rgb(0, 0, 0);
}

.title-mainbox {
  max-height: 56px;
  height: auto;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.title-moreopen {
  max-height: none;
  overflow: visible;
}

.title-maintxt {
  text-align: center;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.title-detail {
  display: flex;
  flex-direction: row;
  font-size: 12px;
  color: #0086f6;
  margin-left: 5px;
  min-width: 45px;
}

.detail-moretitle {
  position: absolute;
  line-height: 30px;
  padding: 0 3px 0 16px;
  background: linear-gradient(
    to right,
    rgba(135, 224, 253, 0) 0%,
    rgba(255, 255, 255, 1) 20%,
    rgba(255, 255, 255, 1) 50%
  );
  color: #000;
  font-size: 18px;
  right: 0;
  bottom: -4px;
}

.pay-order-detail .other {
  margin: 13px 0 0;
  font-size: 16px;
  color: rgb(153, 153, 153);
}

.pay-order-detail .price {
  margin: 0;
  text-align: center;
  padding: 24px 0 5px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: baseline;
}

.pay-order-detail .price-title {
  font-size: 16px;
  color: rgb(51, 51, 51);
  width: 75px;
}

.pay-order-detail .price-content {
  font-size: 39px;
  color: #333;
}

.payway-list {
  font-size: 15px;
  margin: 0 12px;
}

.pay-order-method {
  padding: 17px 15px;
  background-color: rgb(255, 255, 255);
  // border: 1px solid rgb(223, 223, 223);
  // box-shadow: 0 0 5px rgba(223, 223, 223, 0.5);
  border-radius: 8px;
  margin-bottom: 10px;
}

.pay-order-method.disable {
  .info,
  .name {
    color: rgb(170, 170, 170);
  }
  .pay-order-item .wallet {
    background: url(https://pages.c-ctrip.com/Finance/miniprogram/qWalletNoUse.png)
      no-repeat;
    background-size: 100% 100%;
  }
}

.pay-order-item {
  display: flex;
  align-items: center;
  width: 100%;
  flex-direction: row;
}

.pay-order-item .icon {
  display: flex;
  flex-direction: row;
  width: 18px;
  height: 18px;
  max-height: 18px;
  margin: 0 10px 0 0;
  background: url(https://webresource.c-ctrip.com/resh5paymentcommononline/R9/dest/res/h5/webchatprogram/icon-wechatPay.png)
    no-repeat;
  background-size: 100% 100%;
}

.pay-order-item .wx-icon {
  background: url(https://webresource.c-ctrip.com/resh5paymentcommononline/R9/dest/res/h5/webchatprogram/icon-wechatPay.png)
  no-repeat;
  background-size: 100% 100%;
}

.pay-order-item .ali-icon {
  background: url(https://pic.c-ctrip.com/picaresonline/h5paymentsdk/smallprogram/img/alipay-icon.15099709.png) no-repeat;
  background-size: 100% 100%;
}

.pay-order-item .wallet {
  background: url(https://pages.c-ctrip.com/Finance/miniprogram/qwalletUse.png)
    no-repeat;
  background-size: 100% 100%;
}

.icon.select {
  background-image: url(https://pages.c-ctrip.com/Finance/miniprogram/duigouQ.png);
  background-position: center;
  border-radius: 100%;
  margin: 0;
}

.icon.select.walletNotFull {
  background-image: url(https://pages.c-ctrip.com/Finance/miniprogram/switchOn.png);
}

.icon.down {
  background-image: url(https://pages.c-ctrip.com/Finance/miniprogram/down.png);
  background-size: 110% 110%;
  background-position: center;
  border-radius: 100%;
  margin: 3px;
  width: 10px;
  height: 10px;
}

.icon.unselect {
  background-image: url(https://pages.c-ctrip.com/Finance/miniprogram/duigouQ2.png);
  background-position: center;
  border-radius: 100%;
  margin: 0;
}

.icon.unselect.walletNotFull {
  background-image: url(https://pages.c-ctrip.com/Finance/miniprogram/switchOff.png);
}

.icon.walletNotFull {
  width: 29.75px;
  border-radius: 0%;
}

.pay-order-item .info {
  color: rgb(51, 51, 51);
  margin-right: auto;
  display: flex;
  flex-direction: column;
}

.pay-order-item .name {
  color: rgb(0, 0, 0);
  font-size: 15px;
}

.pay-order-item .maintain-txt {
  font-size: 12px;
}

.pay-order-item .marketing {
  margin: 10px 0 0 0;
  font-size: 13px;
}

.pay-order-item1 .icon {
  opacity: 0.6;
  color: rgb(187, 187, 187);
}

.pay-order-item .using-text {
  font-size: 13px;
  color: "#666";
  margin-right: 6px;
}

.pay-order-item .icon-right-arrow {
  background-image: url(https://pages.c-ctrip.com/Finance/miniprogram/arrow_right.png);
  width: 12px;
  height: 12px;
  background-size: 100%;
  display: inline-block;
  margin-left: 8.5px;
}

.pay-btn-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 14px 10px 30px;
  background-color: #fff;
}

.pay-button-full {
}
.pay-button-full button {
  height: 44px;
  background: linear-gradient(
    90deg,
    rgb(255, 165, 10) 0%,
    rgb(255, 119, 0) 100%
  );
  font-size: 18px;
  color: rgb(255, 255, 255);
  text-align: center;
  border-radius: 6px;
  border: 0;
}
.pay-button-full button[loading] {
  background-color: rgb(229, 138, 17);
  color: rgba(255, 255, 255, 0.6);
}
.pay-button-full .hover {
  background-color: rgba(229, 138, 17, 0.6);
  color: rgba(255, 255, 255, 0.6);
}
.pay-button-bottom {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding: 5px 15px 50px;
}
.pay-intro-link {
  margin: 14px 0 0;
  font-size: 14px;
  color: rgb(9, 159, 222);
  padding: 0 5px;
}

.modalview {
  text-align: center;
}

.hnopay {
  background: #f3f5f8;
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.hpayeicon {
  width: 124px;
  height: 124px;
  background: url("https://pic.c-ctrip.com/picaresonline/h5paymentsdk/smallprogram/img/holderr.86ab12be.png")
    no-repeat 0 0;
  background-size: 124px 124px;
  margin-top: 125px;
  margin-bottom: 24px;
}

.hpayetext {
  font-size: 20px;
  color: #474747;
  padding: 0 30px;
  line-height: 30px;
  text-align: center;
}

.price-ncy {
  font-size: 24px;
  color: #333;
}

.hideview {
  display: none;
}

.pay-modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
}

.pay-modal {
  box-sizing: border-box;
  position: fixed;
  left: 50%;
  top: 50%;
  width: 100%;
  transform: translate(-50%, -50%);
  padding: 0 20px;
  z-index: 1001;
}

.pay-modal-content {
  position: relative;
  padding: 20px 35px;
  border-radius: 5px;
  font-size: 15px;
  word-wrap: break-word;
  word-break: break-all;
  color: rgb(153, 153, 153);
  background-color: rgb(255, 255, 255);
}

.pay-modal-header {
  padding: 0 0 15px;
  color: rgb(0, 0, 0);
  text-align: center;
}
.pay-modal-title {
  font-size: 24px;
}
.pay-modal-close {
  position: absolute;
  top: 10px;
  right: 10px;
}
.pay-modal-body {
}

.txt-orange {
  color: #f60;
}

.flex {
  display: flex;
  flex-direction: row;
}

.center {
  align-items: center;
}
