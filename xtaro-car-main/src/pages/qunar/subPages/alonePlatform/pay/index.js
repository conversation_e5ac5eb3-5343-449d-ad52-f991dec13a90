import { View, Text, Button } from "@tarojs/components";
import Taro, {
    getCurrentInstance,
    showModal,
    redirectTo,
    navigateBack,
} from "@tarojs/taro";

import React from "react";
import Pay from "@platformComponents/Pay/index";
import SmsCmp from "../components/SmsCmp/index";
import Nav from "../components/Nav/index";
import util from "@/common/utils/util.js";
import queryPay from "@/common/utils/pay";
import {
    sendUbt,
    Loading,
    reportErrorLog,
} from "@/common/utils/pay/utils/midstage.js";

import "./index.scss";

class PayPage extends React.Component {
    constructor(props) {
        super(props);
        // const { cookies } = util.getGlobalInfo();
        this.currentInstance = getCurrentInstance();
        this.state = {
            pageUrl: this.currentInstance.router.params
                ? decodeURIComponent(
                      this.currentInstance.router.params.pageUrl || "",
                  )
                : "",
            payData: {
                cashierUrl: "",
                openId: "",
            },
            anuEnv: process.env.ANU_ENV,
            res_102: null, // 102报文
            status_102: 0, // 102请求状态。 0：初始化、1：请求中、 2：获取成功
            no_payment: false, // 没有支付方式
            displayInfo: {}, // 页面订单信息
            CashAccountPay: null, // 钱包payway对象
            showWalletInfo: false,
            wxPayway: null, //微信payway对象
            isSelectWallet: false, // 是否选择余额
            selectWalletAmount: 0, // 余额已选金额
            restAmount: 0, // 仍需支付金额
            walletAvailableAmount: 0, // 钱包可用金额
            isWalletEnough: false, //钱包是否能足额

            showSms: false, // 是否显示短信弹窗
            showPhoneNo: "", // 短信显示手机号
            sendPhone: "", // 短信发送手机号
            sendPhoneAreaCode: "", // 短信区号
            payToken: "",
        };
        this.onFail = this.onFail.bind(this);
        this.onCancel = this.onCancel.bind(this);
        this.onSuccess = this.onSuccess.bind(this);
        this.res303 = null; //303 报文
    }

    // 华为快应用跳转补丁
    static innerQuery = {
        pageUrl: "",
        cashierUrl: "",
    };

    // 获取页面显示信息
    getDisplayInfo = ({ res_102, walletWay }) => {
        try {
            let { orderInfo, payCatalogInfo } = res_102;
            if (!orderInfo || !payCatalogInfo) {
                reportErrorLog({
                    errorType: "30012_6",
                    errorMessage: "102返回数据获取异常",
                    extendInfo: {
                        res_102,
                        walletWay,
                    },
                });
            }
            // const { ctripPayInfoList } = payCatalogInfo.walletInfo;
            const ctripPayInfoList =
                (payCatalogInfo &&
                    payCatalogInfo.walletInfo &&
                    payCatalogInfo.walletInfo.ctripPayInfoList) ||
                [];
            const thirdPartyList =
                (payCatalogInfo && payCatalogInfo.thirdPartyList) || []; // 走到这一步都会有值，这里只做个兜底
            const targetBrandId =
                this.state.anuEnv === "wx"
                    ? ["WEAPP", "OGP_WEAPP"]
                    : ["EB_MobileAlipay", "OGP_Alipay"];
            const wxPayway = thirdPartyList.find((item) =>
                targetBrandId.find((id) => id === item.brandId),
            );
            const CashAccountPay = ctripPayInfoList.find(
                (item) => item.brandId == "CashAccountPay",
            );
            const targeOgp =
                wxPayway &&
                (wxPayway.brandId === "OGP_WEAPP" ||
                    wxPayway.brandId === "OGP_Alipay"); // 去哪儿小程序不支持担保场景，如果有告警处理
            if (!wxPayway || (walletWay && !CashAccountPay) || targeOgp) {
                reportErrorLog({
                    errorType: "30012_4",
                    errorMessage: "102下发参数异常_没有相应的支付方式",
                    extendInfo: {
                        ctripPayInfoList,
                        thirdPartyList,
                        res_102,
                        walletWay,
                    },
                });
            }
            const walletAvailableAmount = CashAccountPay
                ? CashAccountPay.availableAmount
                : 0;
            const isWalletEnough =
                walletAvailableAmount - orderInfo.orderAmount >= 0;
            const info = {
                orderTitle: orderInfo.orderTitle,
                orderAmount: orderInfo.orderAmount,
                walletName: (walletWay && walletWay.name) || "",
                maintainTxt: (walletWay && walletWay.maintainTxt) || "",
                walletAvailable: walletWay && walletWay.status == 1,
            };
            this.setState(
                {
                    displayInfo: info,
                    walletAvailableAmount,
                    CashAccountPay,
                    showWalletInfo: walletWay && CashAccountPay,
                    wxPayway,
                    isWalletEnough,
                    isSelectWallet: false,
                    selectWalletAmount: 0,
                    status_102: 2,
                    no_payment: false,
                    res_102,
                    showSms: false, // 是否显示短信弹窗
                    showPhoneNo: "", // 短信显示手机号
                    sendPhone: "", // 短信发送手机号
                    sendPhoneAreaCode: "", // 短信区号
                },
                () => {
                    this.getPayBtnText();
                },
            );
        } catch (error) {
            // 正常情况不会到这里，告警处理
            reportErrorLog({
                errorType: "30012_5",
                errorMessage: "102下发参数异常_getDisplayInfo_error",
                extendInfo: {
                    error,
                    errMsg: error instanceof Error ? error.toString() : "",
                    res_102,
                    walletWay,
                    query: this.currentInstance.router.params,
                },
            });
            return {};
        }
    };

    // 102成功后开始显示页面
    doDirect = ({ res_102, walletWay, isNoPaymentWay }) => {
        if (isNoPaymentWay) {
            this.setState({
                no_payment: true,
            });
        } else {
            this.getDisplayInfo({ res_102, walletWay });
            // this.setState({
            //     status_102: 2,
            //     no_payment: false,
            //     res_102,
            //     isSelectWallet: false,
            // }, () => {
            //     // this.onSelectWallet(); // 默认不自动选中余额
            // });
        }
    };

    async componentDidMount() {
        const initUserData = await util.getGlobalInfoAsync();
        const { cookies } = initUserData;
        const pages = getCurrentPages()
        const  currentPages = pages[pages.length - 1];
        const params = currentPages.options || {}
        let payData = this.state.payData
        if (Object.keys(params).length === 0) {
          // 旧代码逻辑：适配快应用，迁移成 qtaro 后不支持快应用，所以正常不会走到这里 - 2024-10-14
            // const { pageUrl, cashierUrl } = currentPages; // this.wx;
            const { pageUrl, cashierUrl } = this.currentInstance?.router?.params || {}; // this.wx;
            payData = {
              cashierUrl: decodeURIComponent(cashierUrl), // 从业务线后端获取的
              openId: cookies.openId, // user_id
              pageUrl: decodeURIComponent(pageUrl),
            }
            this.setState({
                payData,
                pageUrl,
            });
        } else {
            payData = {
              cashierUrl: decodeURIComponent(
                  this.currentInstance.router.params.cashierUrl,
              ), // 从业务线后端获取的
              openId: cookies.openId, // user_id
              pageUrl: decodeURIComponent(this.currentInstance?.router?.params?.pageUrl),
            }
            this.setState({
                payData
            });
        }
        if (this.state.anuEnv === "wx" || this.state.anuEnv === "ali") {
            try {
                const currentPage = getCurrentInstance().router;
                const currentPath = currentPage.path;
                const isPaynew = currentPath
                    ? currentPath.includes("pages/qunar/subPages/alonePlatform/pay")
                    : false;
                // eslint-disable-next-line no-console
                console.log("currentPath: ", currentPath, isPaynew);
                if (currentPath && !isPaynew) {
                    reportErrorLog({
                        errorType: "30012_6",
                        errorMessage: "pay页面被异常加载了",
                        extendInfo: {
                            currentPath,
                            payData,
                            anuEnv: this.state.anuEnv,
                        },
                    });
                    if (
                        this.state.anuEnv === "ali" ||
                        this.state.anuEnv === "wx"
                    ) {
                        // TODO: 支付页面componentDidMount有可能会在别的页面被触发，应该是框架问题，但是框架暂时没办法解决
                        // 这里临时解决办法是判断只有当前页面时支付路由时才继续走，否则直接退出
                        // 等框架修复了这个问题可以把这段逻辑去掉
                        return;
                    }
                }
            } catch (e) {
                // eslint-disable-next-line no-console
                console.error("currentPath error", e);
            }

            const status_102 =
                this.currentInstance.router.params.status_102 || 0;
            this.setState({
                status_102,
            });
            queryPay.payPageDidMount();
            if (status_102 == 2) {
                this.doDirect(queryPay.getRes_102());
            } else {
                queryPay.openCashier({
                    ...payData,
                    isDirect: true, // 直连页面
                    doDirect: this.doDirect,
                    success: this.onSuccess,
                    fail: this.onFail,
                    cancel: this.onCancel,
                    complete: this.onComplete,
                });
            }
        }
    }

    componentWillUnmount() {
        queryPay.submitting = false;
        queryPay.midstagePaying = false;
        queryPay.payPageUnmountCheck();
    }

    // 微信被点击
    onSelectWechat = () => {
        // 剩余金额大于0，不能撤销微信；
        // 剩余金额等于0，取消掉余额，使用微信支付
        if (this.state.restAmount > 0) {
            return;
        } else {
            this.onSelectWallet();
        }
    };

    // 余额被点击
    onSelectWallet = () => {
        // 不可用时不能选择
        if (!this.state.displayInfo.walletAvailable) return;
        let selectWalletAmount = 0;
        if (this.state.isSelectWallet) {
            selectWalletAmount = 0;
        } else {
            // 计算余额是否足额，计算剩下支付金额
            selectWalletAmount = Math.min(
                this.state.displayInfo.orderAmount,
                this.state.walletAvailableAmount,
            );
        }
        this.setState(
            {
                isSelectWallet: !this.state.isSelectWallet,
                selectWalletAmount: selectWalletAmount.toFixed(2),
            },
            () => this.getPayBtnText(),
        );
    };

    // 计算支付按钮文案
    getPayBtnText = () => {
        const restAmount =
            this.state.displayInfo.orderAmount - this.state.selectWalletAmount;
        let payBtnText = "";
        if (restAmount > 0) {
            payBtnText = `${this.state.anuEnv === "wx" ? "微信支付" : "支付宝支付"} ￥${restAmount.toFixed(2)}`;
        } else {
            payBtnText = "使用余额全额抵扣";
        }
        this.setState({
            restAmount: restAmount.toFixed(2),
            payBtnText,
        });
    };

    onSmsClose = () => {
        this.setState({
            showSms: false,
        });
    };

    onSmsSubmitpay = (param) => {
        const riskParam = {
            vChainToken: this.res303.vChainToken,
            riskAndPwdInfos: [
                {
                    riskVerifyToken: param.riskVerifyToken,
                    verifyCodeType: param.verifyCodeType,
                    verifyRequestId: param.verifyRequestId,
                },
            ],
        };
        Loading.show("支付提交中...");
        queryPay.payMidstagePlugin({
            isSmsSubmit: true,
            paymentTraceId: this.res303.paymentTraceId,
            walletInfos: this.getPaymentWalletInfos(),
            restAmount: this.state.restAmount,
            riskParam,
            smsSubmitCallBack: () => {
                this.setState({
                    showSms: false,
                });
            },
            paywayModelData: this.state.res_102,
            paymentWayToken: this.state.wxPayway.paymentWayToken,
        });
    };

    // 返回收银台的参数： 选择的钱包抵扣详情
    getPaymentWalletInfos = () => {
        if (this.state.CashAccountPay) {
            const res = {
                payAmount: this.state.selectWalletAmount,
                routerInfo: {
                    routerWayId: this.state.CashAccountPay.routerWayId,
                    paymentWayToken: this.state.CashAccountPay.paymentWayToken,
                },
                brandId: this.state.CashAccountPay.brandId,
                ticketType: this.state.CashAccountPay.type,
            };
            return [res];
        } else {
            return [];
        }
    };

    //   点击支付
    weicatPaysubmit = () => {
        sendUbt({
            clickName: "pay-submit-click",
            chainName: "pay-submit-click",
            isClickTrace: "1",
            a: "d-weichat-submit-start",
            c: 100005,
            d: "d-weichat-submit-start",
            dd: "开始提交支付 weichat-submit-start",
        });
        Loading.show("支付提交中...");
        queryPay.payMidstagePlugin({
            walletInfos: this.getPaymentWalletInfos(),
            restAmount: this.state.restAmount,
            submitCallBack: (res) => {
                this.res303 = res;
                this.setState({
                    showSms: true,
                    showPhoneNo: res.showPhoneNo,
                    sendPhone: res.sendPhone,
                    sendPhoneAreaCode: res.sendPhoneAreaCode || "",
                    payToken: res.tradeNo,
                });
            },
            paywayModelData: this.state.res_102,
            paymentWayToken: this.state.wxPayway.paymentWayToken,
        });
    };

    componentDidHide() {
        queryPay.break_pay(true);
    }

    onSuccess = () => {
        showModal({
            title: "支付成功",
            showCancel: false,
            confirmColor: "#00bcd4",
            confirmText: "确定",
            complete: () => {
                this.handleJump(this.state.pageUrl);
            },
        });
    };

    handleJump = (url) => {
        if (url.indexOf("http") === 0) {
            util.openWebviewRe({
                url: url,
                loginSync: true,
            });
        } else {
            redirectTo({
                url: url,
            });
        }
    };

    onFail = (option) => {
        const { showModal: needShowModal = true } = option;
        const complete = () => {
            if (
                this.currentInstance.router.params &&
                this.currentInstance.router.params.keepJump
            ) {
                this.handleJump(this.state.pageUrl);
                return;
            }
            navigateBack();
        };
        if (!needShowModal) {
            complete();
            return;
        }
        showModal({
            title: "支付失败啦",
            showCancel: false,
            confirmColor: "#00bcd4",
            confirmText: "确定",
            complete,
        });
    };
    onCancel = () => {
        showModal({
            title: "支付取消啦",
            showCancel: false,
            confirmColor: "#00bcd4",
            confirmText: "确定",
            complete: () => {
                if (
                    this.currentInstance.router.params &&
                    this.currentInstance.router.params.keepJump
                ) {
                    this.handleJump(this.state.pageUrl);
                    return;
                }
                navigateBack();
            },
        });
    };
    onComplete = () => {};

    onBack = () => {
        showModal({
            content: "您的订单尚未完成支付，确认要离开吗？",
            confirmText: "确认离开",
            cancelText: "继续支付",
            success: ({ confirm }) => {
                // 查询支付结果
                queryPay.getPayRes({
                    success: (res) => {
                        // api跳钱包的，执行success
                        if (res.isApiJumpCashier) {
                            res.payOptions.success({
                                code: "0",
                                message: "支付成功",
                            });
                            res.payOptions.complete({
                                code: "3",
                                message: "支付完成",
                            });
                        } else {
                            // 直连的，跳转pageUrl
                            this.handleJump(this.state.pageUrl);
                        }
                        sendUbt({
                            a: "onBack-success",
                            c: 100006,
                            d: "onBack-success",
                            dd: "挽留查询： 支付成功",
                        });
                    },
                    fail: (res) => {
                        if (confirm) {
                            // api跳钱包的，执行cancel
                            if (res.isApiJumpCashier) {
                                res.payOptions.cancel({
                                    code: "2",
                                    message: "支付取消",
                                });
                                res.payOptions.complete({
                                    code: "3",
                                    message: "支付完成",
                                });
                            } else {
                                // 直连的，回退
                                // keepJump: 强制跳转
                                if (
                                    this.currentInstance.router.params &&
                                    this.currentInstance.router.params.keepJump
                                ) {
                                    this.handleJump(this.state.pageUrl);
                                } else {
                                    navigateBack();
                                }
                            }
                        }
                        sendUbt({
                            a: "onBack-fail",
                            c: 100006,
                            d: "onBack-fail",
                            dd: "挽留查询： 支付失败",
                        });
                    },
                });
            },
        });
    };

    onNoPaymentBack = () => {
        if (
            this.currentInstance.router.params &&
            this.currentInstance.router.params.keepJump
        ) {
            this.handleJump(this.state.pageUrl);
        } else {
            navigateBack();
        }
    };

    render() {
        if (this.state.anuEnv === "wx" || this.state.anuEnv === "ali") {
            if (this.state.no_payment) {
                return (
                    <View class="page">
                        {this.state.anuEnv === "wx" ? (
                            <Nav
                                title="安全收银台"
                                onBack={this.onNoPaymentBack}
                            />
                        ) : null}

                        <View class="hnopay">
                            <View class="hpayeicon"></View>
                            <Text class="hpayetext">
                                您预订的产品无可用支付方式，请至去哪儿应用程序订购或电话联系我们
                            </Text>
                        </View>
                    </View>
                );
            } else if (this.state.status_102 == 2) {
                return (
                    <View class="page">
                        {/* {this.state.anuEnv === "wx" ? (
                            <Nav title="安全收银台" onBack={this.onBack} />
                        ) : null} */}

                        <View class="pay-index-container">
                            <View class="pay-order-detail">
                                <View class="detail-titlebox">
                                    <View class="price">
                                        <Text class="price-ncy">￥</Text>
                                        <Text class="price-content">
                                            {this.state.displayInfo.orderAmount}
                                        </Text>
                                    </View>
                                    <View class="title">
                                        <View
                                            id="detail-titlewrap"
                                            class="title-mainbox"
                                        >
                                            <View
                                                class="title-maintxt"
                                                id="detail-titletxt"
                                            >
                                                {
                                                    this.state.displayInfo
                                                        .orderTitle
                                                }
                                            </View>
                                            {/* <div wx: if="{{util.isNoEmptyObject(orderSummary)}}" class="title-detail" bindtap="onTapDetail">
                      详情
                      <div class="icon down"></div>
                      </div> */}
                                        </View>
                                    </View>
                                </View>
                            </View>
                            <View class="payway-list">
                                {this.state.showWalletInfo ? (
                                    <View
                                        if="{{walletShowInfo}}"
                                        class={`pay-order-method ${this.state.displayInfo.walletAvailable ? "" : "disable"}`}
                                        onClick={this.onSelectWallet}
                                    >
                                        <View class="pay-order-item wechat-pay">
                                            <View class="icon wallet"></View>
                                            <View class="info">
                                                <View class="name">
                                                    {
                                                        this.state.displayInfo
                                                            .walletName
                                                    }
                                                </View>
                                                {this.state.displayInfo
                                                    .maintainTxt ? (
                                                    <View class="maintain-txt">
                                                        {
                                                            this.state
                                                                .displayInfo
                                                                .maintainTxt
                                                        }
                                                    </View>
                                                ) : null}
                                            </View>
                                            {this.state.displayInfo
                                                .walletAvailable ? (
                                                this.state.isSelectWallet ? (
                                                    <View class="flex center">
                                                        <View>
                                                            <Text class="using-text txt-orange">
                                                                -￥
                                                                {
                                                                    this.state
                                                                        .selectWalletAmount
                                                                }{" "}
                                                            </Text>
                                                        </View>
                                                        <View>
                                                            <View
                                                                class={`icon select ${this.state.isWalletEnough ? "" : "walletNotFull"}`}
                                                            ></View>
                                                        </View>
                                                    </View>
                                                ) : (
                                                    <View>
                                                        <View
                                                            class={`icon unselect ${this.state.isWalletEnough ? "" : "walletNotFull"}`}
                                                        ></View>
                                                    </View>
                                                )
                                            ) : null}
                                            <View></View>
                                        </View>
                                    </View>
                                ) : null}

                                <View
                                    class="pay-order-method"
                                    onClick={this.onSelectWechat}
                                >
                                    <View class="pay-order-item wechat-pay">
                                        <View
                                            class={`icon ${this.state.anuEnv === "wx" ? "wx-icon" : "ali-icon"}`}
                                        ></View>
                                        <View class="info">
                                            <View class="name">
                                                {this.state.anuEnv === "wx"
                                                    ? "微信支付"
                                                    : "支付宝支付"}
                                            </View>
                                        </View>
                                        <View>
                                            {this.state.restAmount > 0 ? (
                                                <View class="icon select"></View>
                                            ) : (
                                                <View class="icon unselect"></View>
                                            )}
                                        </View>
                                    </View>
                                </View>
                                <View class="pay-btn-wrap">
                                    <View class="pay-button-full">
                                        <Button
                                            onClick={this.weicatPaysubmit}
                                            hover-class="hover"
                                        >
                                            {this.state.payBtnText}
                                        </Button>
                                    </View>
                                </View>
                            </View>
                        </View>
                        {this.state.showSms ? (
                            <SmsCmp
                                showPhoneNo={this.state.showPhoneNo}
                                onSmsSubmitpay={this.onSmsSubmitpay}
                                payToken={this.state.payToken}
                                sendPhone={this.state.sendPhone}
                                sendPhoneAreaCode={this.state.sendPhoneAreaCode}
                                onSmsClose={this.onSmsClose}
                            />
                        ) : null}
                    </View>
                );
            } else {
                return <View></View>;
            }
        } else {
            return (
                <View>
                    {/* 引入支付组件：这里不能替换成 queryPay.openCashier 方法，因为有些平台需要支持 togglePayDlg */}
                    <Pay
                        data={this.state.payData}
                        onSuccess={this.onSuccess}
                        onFail={this.onFail}
                        onCancel={this.onCancel}
                        onComplete={this.onComplete}
                        isDirect={true}
                    ></Pay>
                </View>
            );
        }
    }
}

export default PayPage;
