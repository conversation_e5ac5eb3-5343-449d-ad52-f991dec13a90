import { View } from "@tarojs/components";
import {
    hideShareMenu,
    getCurrentInstance,
    getApp,
    switchTab,
    showLoading,
    request,
    hideLoading,
    showToast,
    navigateBack,
} from "@tarojs/taro"; /* eslint-disable */
import React from "react";
import "./index.scss";
import WxPayAuthTpl from "../../components/WxPayAuthTpl/index";
import WxPayAuthSuccessTpl from "../../components/WxPayAuthSuccessTpl/index";
import WxPayAuthFailTpl from "../../components/WxPayAuthFailTpl/index";
// import WxPayHasAuthTpl from '@platformComponents/WxPayHasAuthTpl/index';
import WxPayAccountExceedingTpl from "../../components/WxPayAccountExceedingTpl/index";
import config from "@/common/utils/config/config.js";
import auth from "../../common/utils/auth.js";
import EventEmitter from "@/common/utils/EventEmitter.js";
import watcher from "@/common/utils/oldWatcher.js";
import login from "@/common/utils/login.js";
import { Base64 } from "@/common/utils/base64.js";

class P extends React.Component {
    constructor(props) {
        super(props);
        this.currentInstance = getCurrentInstance();
        this.state = {
            userIdToken: "",
            source: "",
            authCode: "",
            wechatCertified: "",
            identityType: "",
            userName: "",
            identityCodeEncrypted: "",
            conflictList: [],
            guideLogin: false,
            // 去授权首页是否引导登录
            pageNo: 0,
            // 认证页: 0 ， 认证成功页: 1 ， 身份已被认证页: 2 ， 账号超限页: 3 ， 暂无法认证: 4
            hasSelected: false,
            // 【账号超限页】 是否有选中信息
            from_s: "",
            // simpleProcess表示 走简化流程。
            failPageText: "",
            // 认证失败页 文案
            simplifySuccessPageText: true,
            // true表示：已实名的用户，打开认证首次看到的是认证成功的结果页(无实名信息)，结果页文案简化（不展示实名信息）
            // 跳转APP的地址
            toApp_bindCard: "",
            toApp_authWay: "hy",
            toApp_toAppeal: "",
            toApp_eraseRealNameInfo: "",
            toApp_Cashier:
                "pay/wechatcallback?type=realname&status=1&certWay=wechat",
            toApp_busiResult: "",
        };
    }

    fromAuthOpen = false;
    pageName = "";
    simpleSourceList = [
        "cashier_wx",
        "DK_WECHAT",
        "WXMYINFO",
        "cashier_wxlogin",
        "cashier_wxnologin",
        "Nano_Activate_AliWx_Qunar",
    ];
    wechatMiniSourceList = ["WXMYINFO", "cashier_wxlogin", "cashier_wxnologin"];
    BusinessSourceList = ["Nano_Activate_AliWx_Qunar"];

    componentWillMount() {
        hideShareMenu();
        let queryObj = this.currentInstance.router.params;
        this.initData(queryObj);
    }

    initData = (queryObj) => {
        this.env = process.env.BUILD_ENV;
        this.domain =
            this.env === "beta"
                ? "https://memnewmobilek.beta.qunar.com"
                : "https://pay.qunar.com";
        const {
            userIdToken = "",
            source = "",
            pageNo = "", // 指定直接跳实名引导页或结果页，比如，小程序内部引导实名，有可能用户已实名，这时需要直接跳成功页
            guideLogin, // 小程序内部引导实名参数，指明是否需要引导登录
            fromAuthOpen, // 是否来自小程序内部引导实名
            activeWxAuth = false, // 是否主动实名，来自会员实名认证选择页
            resultUrl = "", // 成功后需要跳转的APP内的结果页，目前只有拿去花实名需要   https%3A%2F%2Fmemnewmobilek.beta.qunar.com%2Flego%2Fwallet%2Fdefault%2Findex
        } = queryObj;
        let toApp_busiResult = "";
        if (resultUrl) {
            const decodeBase64Url = Base64.decode(resultUrl);
            toApp_busiResult = `hy?url=${encodeURIComponent(decodeBase64Url)}`;
        }
        // const from_s = this.simpleSourceList.indexOf(source) !== -1 ? 'simpleProcess' : '';
        const from_s = activeWxAuth ? "complexProcess" : "simpleProcess";
        const isGuideLogin = guideLogin === "true" || guideLogin === true;
        this.fromAuthOpen = fromAuthOpen == "true" || fromAuthOpen == true;
        this.pageName = isGuideLogin
            ? "realNameAuthHomePage_guideLogin"
            : "realNameAuthHomePage";

        const me = this;
        this.setState(
            {
                userIdToken: userIdToken,
                source,
                from_s,
                guideLogin: isGuideLogin,
                pageNo: +pageNo || 0,
                toApp_busiResult,
            },
            () => {
                setTimeout(function () {
                    watcher.pv({
                        page: me.pageName,
                        from: me.state.source,
                        userIdToken,
                    });
                }, 1000);
                this.sendWatcher("realNameAuthFirstPage");
            },
        );
    };

    gotoLogin = () => {
        login(this.loginCallback.bind(this), {
            source: "pay-auth",
        });
        this.sendWatcher("loginBeforeAuth");
    };

    loginCallback = () => {
        const me = this;
        EventEmitter.addListener("getRealNameAuthData", (data) => {
            me.initData(data);
        });
        auth.openAuth();
    };

    componentDidShow() {
        const { globalData = {} } = getApp();
        const scene =
            (globalData.appShowOptions && globalData.appShowOptions.scene) ||
            "";

        if (scene === 1069 || this.fromAuthOpen) {
            return;
        } // 如果不是从APP跳转过来的 或者 不是小程序内部封装的openAuth来的，直接到小程序首页

        switchTab({
            url: "/pages/platform/indexWx/index",
        });
    }

    getAuthToken = (res) => {
        // const detail = res.detail || {};
        const auth_token = res["auth_token"] || ""; // 以下两种情况

        if (auth_token) {
            // 第一种 成功拿到auth_token，进行实名认证
            this.getRealNameInfo(auth_token);
            this.sendWatcher("getAuthToken_success");
        } else if (res.errMsg !== "openRealnameAuth:cancel") {
            // 第二种 暂无身份信息可授权（失败原因：微信未实名）
            this.gotoNextPage(4);
            this.sendWatcher("getAuthToken_fail");
        }
        this.sendWatcher("getAuthToken");
    };

    getRealNameInfo = (authCode) => {
        showLoading({
            title: "认证中",
        });
        const me = this;
        const { userIdToken, source } = me.state;
        const url = `${config.settings.requestDomain}/mobile/h5/member/realName/certifyMemberByWechat.htm?userIdToken=${userIdToken}&authCode=${authCode}&source=${source}`;
        request({
            url,
            method: "POST",
            success: function (res) {
                hideLoading();
                const data = (res && res.data) || {};

                if (
                    data.status === 0 &&
                    data.data &&
                    data.data.wechatCertified
                ) {
                    me.initAuthData(data.data);
                } else {
                    me.gotoNextPage(4); // me.setData({ failPageText: data.message || ''});

                    me.sendWatcher("realNameAuth_fail", {
                        status: data.status,
                        message: data.message,
                    });
                }
            },
            fail: function () {
                hideLoading();
                me.gotoNextPage(4);
                me.setState({
                    failPageText: "网络异常，请稍后重试",
                });
                me.sendWatcher("realNameAuth_fail", {
                    message: "网络请求失败",
                });
            },
        });
    };

    initAuthData = (data) => {
        const me = this;
        const {
            wechatCertified,
            identityType = "",
            userName = "",
            identityCodeEncrypted = "",
            conflictList = [],
            mobile = "",
        } = data;
        let pageNo = 0,
            toApp_Cashier =
                "pay/wechatcallback?type=realname&status=1&certWay=wechat",
            failPageText = "";
        const { from_s } = this.state;

        if (from_s === "simpleProcess") {
            // 客户端收银台引导实名
            if (conflictList.length < 6) {
                // 成功
                pageNo = 1;
                toApp_Cashier =
                    "pay/wechatcallback?type=realname&status=0&certWay=wechat";
            } else {
                // 失败
                pageNo = 4;
                failPageText = "您的实名认证账号超限，暂无法继续认证";
            }
        } else {
            // 正常实名认证
            if (conflictList.length < 6) {
                pageNo = 1;
            } else {
                //认证失败，冲突
                pageNo = 3;
            }
        }

        me.setState(
            {
                wechatCertified,
                identityType,
                userName,
                mobile: mobile ? me.transformEncryptedCode(mobile, 3, 4) : "",
                identityCodeEncrypted,
                //me.transformEncryptedCode(identityCodeEncrypted, 4,3),
                conflictList: me.updateConflictList(conflictList),
                toApp_Cashier,
                failPageText,
                simplifySuccessPageText: false,
            },
            () => {
                if (from_s !== "simpleProcess") {
                    // 会员（不属于简化流程）需要获取申诉schema和绑卡schema
                    // me.getToAppAppealParams(); // 去掉身份已认证页，so不需要申诉schema
                    me.getToAppBindCardParams();
                }
            },
        );
        me.gotoNextPage(pageNo);
    };

    // 把一串数字中间的加密乱码转换成星号
    transformEncryptedCode = (code, startIdx, endIdx) => {
        if (code === "") {
            return "";
        }

        const strLen = code.length;
        const starLen = strLen - startIdx - endIdx;
        const preStr = code.slice(0, startIdx);
        const endStr = code.slice(-endIdx);
        const starStr = Array(starLen).fill("*").join("");
        return `${preStr}${starStr}${endStr}`;
    };

    // 给冲突列表的每个item加上checked属性
    updateConflictList = (list) => {
        return list.map((item) => {
            item.checked = false;
            return item;
        });
    };

    // 跳转到下一页，并埋点
    gotoNextPage = (pageNo) => {
        if (!pageNo) {
            return;
        }

        this.setState({
            pageNo,
        });
        let pageMap;

        if (this.state.from_s === "simpleProcess") {
            pageMap = {
                1: "authSuccess",
                4: "authAccountExceeding",
            };
        } else {
            pageMap = {
                1: "authSuccess",
                2: "accountHasAuth",
                3: "authAccountExceeding",
                4: "getRealNameInfoFail",
            };
        }

        const pageNameMap = {
            1: "authSuccessPage",
            2: "accountHasAuthPage",
            3: "authAccountExceedingPage",
            4: "getRealNameInfoFailPage",
        };
        this.pageName = pageNameMap[pageNo];
        this.sendWatcher(pageMap[pageNo]);
    };

    getCERT_ID = () => {
        const { userName, identityType, identityCodeEncrypted } = this.state;
        return {
            cardHolder: userName,
            identityCode: identityCodeEncrypted,
            identityType: identityType,
            _cardHolder: userName,
            _identityCode: identityCodeEncrypted,
            _identityType: identityType,
        };
    };

    // 【会员】需要获取申诉页schema
    // getToAppAppealParams() {
    //     const cert_id = this.getCERT_ID();
    //     const resultPage = `${this.domain}/lego/cert/${this.state.source}/identified/result`;
    //     const url = `${this.domain}/lego/cert/${this.state.source}/card/wipe#CERT_ID=${encodeURIComponent(
    //         JSON.stringify(cert_id)
    //     )}&CERT_CONFLICT=${encodeURIComponent(JSON.stringify(this.state.conflictList))}&CERT_RES=${encodeURIComponent(
    //         resultPage
    //     )}&certWay=wechat`;
    //     const toAppealUrl = encodeURIComponent(url);
    //     this.setState({
    //         toApp_toAppeal: 'hy?url=' + toAppealUrl
    //     });
    // }

    // 【会员】需要获取绑卡页schema
    getToAppBindCardParams = () => {
        // const bindCardUrl = encodeURIComponent(`${this.domain}/lego/cert/${this.data.source}/index?certWay=wechat`);
        // const test = encodeURIComponent('https://memnewmobilem.beta.qunar.com/lego/cert/CERTIFY_wallet/identified/from/Conflict/cardid?identityCode=1422hIxO7NfRTQ0223&identityType=IDENTITYCARD&cardHolder=%E7%94%B0%E7%87%95%E9%A3%9E#CERT_ID=%7B%22cardHolder%22%3A%22%E7%94%B0%E7%87%95%E9%A3%9E%22%2C%22identityCode%22%3A%221422hIxO7NfRTQ0223%22%2C%22identityType%22%3A%22IDENTITYCARD%22%2C%22_cardHolder%22%3A%22%E7%94%B0%E7%87%95%E9%A3%9E%22%2C%22_identityCode%22%3A%221422hIxO7NfRTQ0223%22%2C%22_identityType%22%3A%22IDENTITYCARD%22%7D&CertRisk=&CERT_RES=https%3A%2F%2Fmemnewmobilem.beta.qunar.com%2Flego%2Fcert%2FCERTIFY_wallet%2Fidentified%2Fresult');
        const { userName, identityType, identityCodeEncrypted } = this.state;
        const cert_id = encodeURIComponent(JSON.stringify(this.getCERT_ID()));
        const resultPage = `${this.domain}/lego/cert/${this.state.source}/identified/result`;
        const url = `${this.domain}/lego/cert/${
            this.state.source
        }/identified/from/Conflict/cardid?identityCode=${identityCodeEncrypted}&identityType=${identityType}&cardHolder=${encodeURIComponent(
            userName,
        )}#CERT_ID=${cert_id}&CertRisk=&CERT_RES=${encodeURIComponent(resultPage)}&certWay=wechat`;
        const toBindCardUrl = encodeURIComponent(url);
        this.setState({
            toApp_bindCard: "hy?url=" + toBindCardUrl,
        });
    };

    //【会员流程】身份已被认证页【继续认证】按钮handler
    // gotoAuth() {
    //     this.gotoNextPage(1);
    // }

    //【会员流程】账号超限页 账号列表选中状态切换
    toggleSelectStatus = (e) => {
        const data = e.currentTarget.dataset;
        const { index, checked } = data;
        const newList = this.state.conflictList.map((item, idx) => {
            if (idx === index) {
                item.checked = !checked;
            }
            return item;
        });
        this.setState(
            {
                conflictList: newList,
            },
            () => {
                this.getToAppEraseRealNameInfoParam();
                this.toggleBtnStatus();
            },
        );
    };

    //【会员流程】账号超限页 获取抹除实名信息的schema
    getToAppEraseRealNameInfoParam = () => {
        const cert_id = this.getCERT_ID();
        const resultPage = `${this.domain}/lego/cert/${this.state.source}/identified/result`;
        const url = `${this.domain}/lego/cert/${
            this.state.source
        }/identified/ONLINE_OCCUPIED/appeal#CERT_CONFLICT=${encodeURIComponent(
            JSON.stringify(this.state.conflictList),
        )}&CERT_ID=${encodeURIComponent(JSON.stringify(cert_id))}&CERT_RES=${encodeURIComponent(
            resultPage,
        )}&certWay=wechat`;
        const eraseRealNameInfoUrl = encodeURIComponent(url);
        this.setState({
            toApp_eraseRealNameInfo: "hy?url=" + eraseRealNameInfoUrl,
        });
    };

    //【会员流程】账号超限页 抹除实名信息按钮置灰状态切换
    toggleBtnStatus = () => {
        const hasitemSelected = this.state.conflictList.some(
            (item) => item.checked,
        );
        this.setState({
            hasSelected: hasitemSelected,
        });
    };

    forbidenGotoEaseInfo = () => {
        showToast({
            title: "请选择要抹除的实名信息",
            icon: "none",
            duration: 1500,
        });
    };

    launchAppError = () => {
        showToast({
            title: "跳转APP失败",
            icon: "none",
            duration: 1000,
        });
        this.sendWatcher("toAppFail");
    };

    launchAppSuccess = () => {
        showToast({
            title: "跳转APP成功",
            icon: "none",
            duration: 1000,
        });
        this.sendWatcher("toAppSuccess");
        setTimeout(() => {
            switchTab({
                url: "/pages/platform/indexWx/index",
            });
        }, 500);
    };

    goBack = () => {
        navigateBack({
            delta: 1,
        });
        this.sendWatcher("auth_complete");
    };

    callPayCallback = () => {
        EventEmitter.dispatch("onAuthComplete");
    };

    componentWillUnmount() {
        const { source } = this.state;

        if (source === "cashier_wxlogin" || source === "cashier_wxnologin") {
            this.callPayCallback();
        }

        this.fromAuthOpen = false;
        this.sendWatcher("auth_page_back");
    }

    sendWatcher = (actionType, query) => {
        query = query || {};
        query["action-type"] = actionType;
        query.from = this.state.source;
        query.page = this.pageName;
        query.userIdToken = this.state.userIdToken;
        watcher.click(query);
    };

    render() {
        return (
            <View>
                <View hidden={!(this.state.pageNo === 0)}>
                    <WxPayAuthTpl
                        guideLogin={this.state.guideLogin}
                        gotoLogin={this.gotoLogin.bind(this)}
                        getAuthToken={this.getAuthToken.bind(this)}
                        showBusinessNewText={
                            this.BusinessSourceList.indexOf(
                                this.state.source,
                            ) !== -1
                        }
                    />
                </View>
                <View hidden={!(this.state.pageNo === 1)}>
                    <WxPayAuthSuccessTpl
                        userName={this.state.userName}
                        mobile={this.state.mobile}
                        toApp_bindCard={this.state.toApp_bindCard}
                        from_s={this.state.from_s}
                        toApp_Cashier={this.state.toApp_Cashier}
                        simplifySuccessPageText={
                            this.state.simplifySuccessPageText
                        }
                        source={this.state.source}
                        goBack={this.goBack.bind(this)}
                        launchAppError={this.launchAppError.bind(this)}
                        launchAppSuccess={this.launchAppSuccess.bind(this)}
                        fromwechatMini={this.fromAuthOpen}
                        toApp_busiResult={this.state.toApp_busiResult}
                    />
                </View>
                {/* <div hidden={!(this.state.pageNo === 2)}>
             <WxPayHasAuthTpl
                 conflictList={this.state.conflictList}
                 toApp_toAppeal={this.state.toApp_toAppeal}
                 launchAppError={this.launchAppError.bind(this)}
                 launchAppSuccess={this.launchAppSuccess.bind(this)}
                 gotoAuth={this.gotoAuth.bind(this)}
             />
          </div> */}
                <View hidden={!(this.state.pageNo === 3)}>
                    <WxPayAccountExceedingTpl
                        conflictList={this.state.conflictList}
                        hasSelected={this.state.hasSelected}
                        toApp_eraseRealNameInfo={
                            this.state.toApp_eraseRealNameInfo
                        }
                        launchAppError={this.launchAppError.bind(this)}
                        launchAppSuccess={this.launchAppSuccess.bind(this)}
                        toggleSelectStatus={this.toggleSelectStatus.bind(this)}
                        forbidenGotoEaseInfo={this.forbidenGotoEaseInfo.bind(
                            this,
                        )}
                    />
                </View>
                <View hidden={!(this.state.pageNo === 4)}>
                    <WxPayAuthFailTpl
                        toApp_authWay={this.state.toApp_authWay}
                        from_s={this.state.from_s}
                        failPageText={this.state.failPageText}
                        toApp_Cashier={this.state.toApp_Cashier}
                        source={this.state.source}
                        goBack={this.goBack.bind(this)}
                        launchAppError={this.launchAppError.bind(this)}
                        launchAppSuccess={this.launchAppSuccess.bind(this)}
                        fromwechatMini={this.fromAuthOpen}
                    />
                </View>
            </View>
        );
    }
}

export default P;
