/* pages/pay/pay.wxss */
.pay-result-container {
  background-color: #F5F5F5;
  min-height: 100vh;
}

.header {
  height: 35px;
  background-color: #00BCD4;
}

.pay-result {
  padding: 50px 30px;
  background-color: #fff;
  text-align: center;
}

.icon-success:before {
  content: '\f0ff';
  color: #28B155;
  font-size: 57px;
  width: 57px;
  height: 57px; 
}

.pay-result .title {
  font-size: 18px;
  line-height: 25px;
  margin-top: 20px;
}

.pay-result .money {
  font-size: 35px;
  line-height: 49px;
}

.btn-bar {
  background-color: #fff;
  width:100%;
  padding: 15px;
  box-sizing: border-box;
}

.btn {
  width:100%;
  height: 47.5px;
  border-radius: 2px;
  background-color: #FD9727;
  color:#fff;
  box-shadow: 0;
}

.btn::after {
  border: 0;
}

.tips {
  font-size: 12px;
  color: #9E9E9E ;
  padding: 20px 18px 85px;
}