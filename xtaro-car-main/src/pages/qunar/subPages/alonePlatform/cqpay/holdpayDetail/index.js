import { Block } from "@tarojs/components";
import {
    getCurrentInstance,
    showModal,
} from "@tarojs/taro"; /* eslint-disable */
import React from "react";
import "./index.scss";
import request from "@/common/utils/request";
import Log from "@/common/utils/log";

// import { WITHHOLDPAYCALLBACKEVENT } from '../../common/utils/cqpay/const/index';
import { queryHoldpayDetailModel } from "@/common/utils/payTinyAppLibs.js";
import util from "@/common/utils/util.js";
const { openWebviewRe } = util;

let out_order_no = "";

const failMsg = "商家订单查询异常，请尝试到商家APP中查看订单信息";

class P extends React.Component {
    constructor(props) {
        super(props);
        this.currentInstance = getCurrentInstance();
        this.state = {};
    }
    pageName = "holdpayDetail";

    componentWillMount() {
        let options = this.currentInstance.router.params;
        this.sendNanachiLog("HoldDetailInit", options);
        if (!options.out_order_no) {
            this.failAction({
                message: failMsg,
            });
            this.sendNanachiLog("no_out_order_no", options);
            return;
        }
        out_order_no = options.out_order_no;
        try {
            this.getJumpUrl(options);
        } catch (error) {
            this.failAction({
                message: failMsg,
            });
        }
    }

    failAction = (res) => {
        this.sendNanachiLog("HoldDetailFail", {
            type: "chain",
            chainName: "HoldDetailFail",
            a: "jumpUrl",
            extend: res,
        });
        showModal({
            content: res.message || failMsg,
            confirmText: "确定",
            showCancel: false,
            success() {
                wx.exitMiniProgram();
            },
        });
    };

    getJumpUrl = async (options) => {
        wx.showLoading({
            title: "提交中...",
        });
        const param = {
            thirdPayTransId: options.out_order_no,
        };
        const res = await this.queryApi(param);
        wx.hideLoading();
        if (res.head && res.head.code == 100000 && res.merchantOrderDetailUrl) {
            this.jumpUrl(res.merchantOrderDetailUrl);
        } else {
            this.failAction({
                message: res.head ? res.head.message : failMsg,
            });
        }
    };

    // 请求接口
    queryApi = (requestData) => {
        return new Promise((resolve, reject) =>
            queryHoldpayDetailModel({
                data: requestData,
                h5plat: 29,
                context: {
                    request: request,
                    util: util,
                    subEnv: "fat18",
                },
                success: (res) => {
                    resolve(res);
                },
                fail: (res) => {
                    resolve({});
                },
            }).excute(),
        );
    };

    jumpUrl = (url) => {
        this.sendNanachiLog("HoldDetailJump", {
            type: "chain",
            chainName: "HoldDetailJump",
            a: "jumpUrl",
            extend: { url },
        });
        if (/^http/.test(url)) {
            openWebviewRe({ url });
        } else {
            wx.redirectTo({
                url: url,
                fail: () => {
                    this.failAction({
                        message: failMsg,
                    });
                },
            });
        }
    };

    sendNanachiLog = (actionType, exParams) => {
        var opts = exParams || {};
        opts["action-type"] = actionType;
        opts["pay"] = "pay";
        opts["orderNo"] = out_order_no;
        Log(opts);
    };

    render() {
        return <Block></Block>;
    }
}

export default P;
