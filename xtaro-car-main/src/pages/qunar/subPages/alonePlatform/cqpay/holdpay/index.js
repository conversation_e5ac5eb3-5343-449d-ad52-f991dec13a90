import { Block, View, Text, Button, Navigator } from "@tarojs/components";
import {
    hideShareMenu,
    getCurrentInstance,
    getApp,
    navigateBack,
    switchTab,
    navigateTo,
} from "@tarojs/taro"; /* eslint-disable */
import React from "react";
import "./index.scss";
import EventEmitter from "@/common/utils/EventEmitter.js";

import Business from "../../common/utils/cqpay/common/business";
import paymodels from "../../common/utils/cqpay/models/models";
import HoldUi from "../../common/utils/cqpay/components/holdui";
import HoldPayCtrl from "../../common/utils/cqpay/holdpay/ctrl";
import paymentStore from "../../common/utils/cqpay/models/stores";
import { WITHHOLDPAYCALLBACKEVENT } from "../../common/utils/cqpay/const/index";

const isObject = function (str) {
    return typeof str == "object";
};

let orderDetailStore = paymentStore.HoldTokenStore();
let payResultOStore = paymentStore.HoldResultOrderStore();
let wxscoreStore = paymentStore.WxscoreStore();
let orderDetailParamStore = paymentStore.OrderDetailParamStore();

const UnWxscoreState = paymodels.UnWxscoreStateModel; //查询微信支付分开通状态服务

let GetHoldResult = HoldPayCtrl.getHoldResult;
let SubmitHold = HoldPayCtrl.submitPayhold;
//查询微信分开通模式状态查询
const WxscoreConfirmResultQueryModel = paymodels.WxscoreConfirmResultQueryModel;

class P extends React.Component {
    constructor(props) {
        super(props);
        this.currentInstance = getCurrentInstance();
        this.state = {
            payTitle: "",
            nopayment: false,
            descTxt: "微信支付授权扣款",
            payTxt: "去开通",
            descSubTxt: "",
            loading: true,
            amt: false,
            fromed: false,
            hasHold: false, //102服务下发是否已经开通过授权
            directed: false,
            isWXHF: false,
            navigatorData: {
                appId: "wxbd687630cd02ce1d",
                pathUrl: "pages/index/index",
            },
            extData: {},
        };
        Business.sendUbt({
            actionType: "holdpay_constructor",
            a: "holdpay-constructor",
            c: 30011,
            d: "",
        });
    }
    pageName = "holdpay";
    optionsData = {
        token: "",
        pageSource: "",
        scene: "",
        miniAppBack: false,
        onceLogin: false,
        isNavBack: false,
    };

    componentWillMount() {
        hideShareMenu();
        let queryObj = this.currentInstance.router.params;
        Business.sendUbt({
            actionType: "holdpay_componentWillMount",
            a: "holdpay-componentWillMount",
            c: 30011,
            d: "参数为: " + JSON.stringify(queryObj),
        });
        this.initData(queryObj);
    }

    initData = (options) => {
        let that = this;
        let navData = wxscoreStore.get(); //小程序跳转过来带的data参数
        let directToken = options.token; //h5页面跳转过来带的token参数
        let directPayToken = options.payToken; //h5页面跳转过来的服务端传值
        let payresultInfo = payResultOStore.get() || {};
        const orderDetail = orderDetailStore.get() || {};

        Business.sendUbt({
            actionType: "holdpay_initData",
            a: "holdpay-initData",
            c: 30011,
            d: "orderDetail为: " + JSON.stringify(orderDetail),
        });
        console.log("---------orderDetail start----------------");
        console.log(orderDetail);
        console.log(payresultInfo);
        console.log("---------orderDetail end------------------");

        if (isObject(navData)) {
            if (navData.nopayment) {
                that.setState({
                    nopayment: navData.nopayment,
                });
                return;
            }
            const {
                extData,
                wxScoreChannel,
                resultParams,
                isWxScoreConfirmChannel,
                _payToken,
            } = navData;
            that.isWxScoreConfirmChannel = isWxScoreConfirmChannel;
            that.payToken = _payToken;

            if (isWxScoreConfirmChannel) {
                that.resultParams = resultParams;
                that.setState({
                    directed: false,
                    payTitle: "",
                    descTxt: payresultInfo.brandname,
                    descSubTxt: payresultInfo.paytip,
                    payTxt: "使用 微信支付分 授权",
                    loading: false,
                    navigatorData: {
                        appId: "wxd8f3793ea3b935b8",
                        pathUrl: "pages/use/use",
                    },
                    extData: extData,
                });
            } else if (wxScoreChannel) {
                Business.sendUbt({
                    actionType: "holdpay_initData_wxScoreChannel",
                    a: "holdpay-initData",
                    c: 30011,
                    d: "payresultInfo为: " + JSON.stringify(payresultInfo),
                });

                that.resultParams = resultParams;
                that.setState({
                    directed: false,
                    payTitle: "",
                    descTxt: payresultInfo.brandname,
                    descSubTxt: payresultInfo.paytip,
                    payTxt: "使用 微信支付分 授权",
                    loading: false,
                    navigatorData: {
                        appId: "wxd8f3793ea3b935b8",
                        pathUrl: "pages/use/enable",
                    },
                    extData: extData,
                });
            } else {
                that.setState({
                    directed: false,
                    payTitle: navData.payTitle,
                    payTxt: "去开通",
                    isWXHF: true,
                });

                that.autoSubmitHold();
            }
        }
    };

    //自动处理开通接口服务
    autoSubmitHold = () => {
        let that = this;
        that.submitPayhold();
    };

    submitHoldCtrl = (res) => {
        const that = this;
        let appId = Business.getAppId();
        let extData = {
            appid: appId,
            contract_code: res.contract_code,
            contract_display_account: res.contract_display_account,
            mch_id: res.mch_id,
            notify_url: res.notify_url,
            plan_id: res.plan_id,
            request_serial: res.request_serial,
            timestamp: res.timestamp,
            sign: res.sign,
        };
        try {
            Business.sendUbt({
                a: "navigateToMiniProgram start",
                c: 3003,
                d: "跳转开通免密支付小程序开始",
            });
        } catch (e) {}
        that.setState({
            loading: false,
            extData: extData,
        });
        try {
            Business.sendUbt({
                a: "navigateToMiniProgram end",
                c: 3003,
                d: "跳转开通免密支付小程序完成",
            });
        } catch (e) {}
    };

    submitPayhold = () => {
        let that = this;
        let payTxt = that.state.payTxt;
        if (payTxt === "确认") {
            HoldUi.showLoading("确认中..");
        }
        Business.sendUbt({
            actionType: "submitPayhold_start",
            a: "submitPayhold",
            c: 30010,
            d: "submitPayhold start!",
        });
        that.holdData = {
            fromHoldPage: true,
        };
        HoldUi.showLoading("服务中..");
        SubmitHold.call(that, function (res = {}) {
            Business.sendUbt({
                actionType: "submitPayhold_SubmitHold_response",
                a: "submitPayhold-SubmitHold-response",
                c: 30010,
                d: "res: " + JSON.stringify(res),
            });

            let rescode = res.rc;
            HoldUi.hideLoading();
            if (rescode == 1) {
                //errno:1:服务端错误即原errorInformation， res; 2:解析错误
                HoldUi.modalConfirm(
                    res.rmsg || "系统异常，请稍后再试 -5121",
                    function () {
                        Business.goBackNav("eback", 4);
                    },
                );
                return;
            } else if (rescode == 0) {
                Business.goBackNav("sback");
            } else if (rescode == 16 || rescode == 17) {
                //1.处理风控返回 2.用户修改了手机号或者新卡输入了手机号 需传到风控页
                let _msg = "支付提交失败，如有疑问，请联系携程客服：95010";
                HoldUi.modalConfirm(_msg, function () {
                    Business.goBackNav("rback", 4);
                });
            } else if (rescode == 24) {
                //重复支付
                HoldUi.modalConfirm("您已重复提交订单！", function () {
                    Business.goBackNav("sback");
                });
            } else if (rescode == 18) {
                //需要开通免密支付
                let sigData;
                try {
                    sigData = JSON.parse(res.sig || "");
                } catch (e) {
                    HoldUi.modalConfirm("系统异常，请稍后再试 -5122");
                    Business.sendUbt({
                        actionType: "submitPayhold_SubmitHold_response_r18",
                        a: "WxholdPayModel",
                        c: 200218,
                        dd: "200218 JSON parse res.sig error",
                        d: JSON.stringify(sigData),
                    });
                }
                if (isObject(sigData)) {
                    that.submitHoldCtrl(sigData);
                } else {
                    HoldUi.modalConfirm("系统异常，请稍后再试 -5122.1");
                }
            } else if (rescode > 100) {
                Business.sendUbt({
                    actionType: "submitPayhold_SubmitHold_response_r100",
                    a: "WxholdPayModel",
                    c: 200210,
                    dd: "20020 success callback rc>100",
                    d: JSON.stringify(res),
                });
                Business.goBackNav("eback", 4);
            } else {
                HoldUi.modalConfirm(res.rmsg || "系统异常，请稍后再试 -5123");
                Business.sendUbt({
                    actionType: "submitPayhold_SubmitHold_response_rother",
                    a: "WxholdPayModel",
                    c: 200211,
                    dd: "200211 RC小于100的错误",
                    d: JSON.stringify(res),
                });
            }
        });
    };

    async componentDidShow(options) {
        const that = this;
        const { globalData = {} } = getApp();
        const scene =
            (globalData.appShowOptions && globalData.appShowOptions.scene) ||
            "";
        const referrerInfo =
            (globalData.appShowOptions &&
                globalData.appShowOptions.referrerInfo) ||
            {};
        Business.sendUbt({
            actionType: "onShow_start",
            a: "onShow-start",
            c: 200211,
            dd: "scene : " + scene,
            d: JSON.stringify(referrerInfo),
        });

        if (scene === 1038) {
            console.log("that.optionsData.miniAppBack start");
            console.log(that.optionsData.miniAppBack);
            console.log("that.optionsData.miniAppBack end");
            if (that.optionsData.miniAppBack) {
                const { appId, extraData } = referrerInfo;
                that.optionsData.miniAppBack = false;
                that.setState({
                    submiting: true,
                });

                if (appId == "wxbd687630cd02ce1d" && that.navigatorState) {
                    Business.sendUbt({
                        actionType: "onShow-start-houfu",
                        a: "onShow-start-houfu",
                        c: 200211,
                        dd: "scene : " + scene,
                        d: JSON.stringify(referrerInfo),
                    });
                    that.navigatorState = 0;
                    HoldUi.showLoading("加载中...");
                    setTimeout(function () {
                        that.getholdResult();
                    }, 300);
                } else if (
                    appId == "wxd8f3793ea3b935b8" &&
                    that.navigatorState
                ) {
                    that.navigatorState = 0;
                    HoldUi.showLoading("加载中..");
                    if (that.isWxScoreConfirmChannel) {
                        try {
                            Business.sendUbt({
                                a: "navigateToMiniProgram back 进入需确认模式",
                                c: 30033,
                                d: "",
                            });
                        } catch (e) {}

                        setTimeout(function () {
                            that.getScoreConfrimResult.call(that);
                        }, 300);
                        return;
                    }
                    Business.sendUbt({
                        actionType: "onShow-start-zffmm",
                        a: "onShow-start-houfu",
                        c: 200211,
                        dd: "scene : " + scene,
                        d: JSON.stringify(referrerInfo),
                    });
                    setTimeout(function () {
                        that.getScoreResult.call(that);
                    }, 300);
                }
            }
        }
    }

    TestResultEvent = (options) => {
        const that = this;
        const { globalData = {} } = getApp();
        const scene =
            (globalData.appShowOptions && globalData.appShowOptions.scene) ||
            "";
        const referrerInfo =
            (globalData.appShowOptions &&
                globalData.appShowOptions.referrerInfo) ||
            {};

        const { extraData } = referrerInfo;
        that.optionsData.miniAppBack = false;
        that.setState({
            submiting: true,
        });

        that.navigatorState = 0;
        HoldUi.showLoading("加载中..");
        //debug
        that.isWxScoreConfirmChannel = false;
        if (that.isWxScoreConfirmChannel) {
            try {
                Business.sendUbt({
                    a: "navigateToMiniProgram back 进入需确认模式",
                    c: 30033,
                    d: "",
                });
            } catch (e) {}

            setTimeout(function () {
                that.getScoreConfrimResult.call(that);
            }, 300);
            return;
        }
        setTimeout(function () {
            that.getScoreResult.call(that);
        }, 300);
    };

    componentWillUnmount() {
        Business.sendUbt({
            actionType: "componentWillUnmount",
            a: "onShow-start-houfu",
            c: 200211,
            dd: "",
            d: "",
        });
    }

    //设置执行返回函数
    goBackNav = (action = "", rc) => {
        let that = this;
        Business.sendUbt({
            actionType: "goBackNav",
            a: "goBackNav",
            c: 30050,
            d: "返回上一页面 action: " + action,
        });
        Business.clearStore(); //清除缓存
        navigateBack({
            delta: 1,
            success: () => {
                EventEmitter.dispatch(WITHHOLDPAYCALLBACKEVENT, {
                    type: action,
                });
            },
        });
    };

    launchAppError = (e = {}) => {
        const detail = e.detail || {};
        const errMsg = detail.errMsg;
        Business.sendUbt({
            actionType: "c_payWallet_toHomeAppErr1",
            a: "c_payWallet_toHomeAppErr",
            c: 30050,
            d: errMsg,
        });
    };
    launchAppSuccess = () => {
        const returnUrlLink = this.state.appReturnData;
        Business.sendUbt({
            actionType: "c_wallet_wechatrealname_returnapp1",
            a: "c_wallet_wechatrealname_returnapp",
            c: 30050,
            d: returnUrlLink,
        });
        setTimeout(() => {
            switchTab({
                url: "/pages/platform/indexWx/index",
            });
        }, 500);
    };

    //获取公共返回参数
    getBackParams = (rc, status) => {
        let orderinfo = orderDetailStore.get() || {};
        let payresultInfo = payResultOStore.get() || {};
        let param = {
            orderID: orderinfo.oid || "",
            busType: orderinfo.bustype || "",
            price: orderinfo.amount || "",
        };

        if (typeof status !== "undefined") {
            param.Status = status;
        }

        if (payresultInfo.realoid) {
            param.orderID = payresultInfo.realoid;
        }

        if (rc == 2) {
            delete param.payType;
            param.ErrorCode = 888;
            param.ErrorMessage = "";
        } else if (rc == 4) {
            delete param.payType;
            param.ErrorCode = orderinfo.ErrorCode;
            param.ErrorMessage = orderinfo.ErrorMessage;
        } else if (rc == 3) {
            delete param.payType;
        }

        return param;
    };

    getScoreConfrimResult = () => {
        var that = this;
        const orderDetail = orderDetailStore.get() || {};

        const param = {
            requestId: orderDetail.requestid,
            paytoken: that.payToken,
        };
        Business.sendUbtTrace({
            a: "getScoreConfrimResult-server-start",
            c: 10010,
            dd: "获取微信支付分确认模式开通状态开始！",
        });
        WxscoreConfirmResultQueryModel({
            data: param,
            success: function (res) {
                try {
                    Business.sendUbt({
                        a: "getScoreConfrimResult  response",
                        c: 30033,
                        d: JSON.stringify(res),
                    });
                } catch (e) {}
                let rescode = res.rc;
                HoldUi.hideLoading();
                if (rescode == 1) {
                    //errno:1:服务端错误即原errorInformation， res; 2:解析错误
                    try {
                        Business.sendUbt({
                            a: "getScoreConfrimResult-server-response",
                            c: 20021,
                            dd: "20020 success callback rc=1",
                            d: JSON.stringify(res),
                        });
                    } catch (e) {}
                    // HoldUi.modalConfirm(res.rmsg || '系统异常，请稍后再试 -5300');
                    HoldUi.modalConfirm("微信支付分授权未成功");
                    return;
                } else if (rescode == 24) {
                    try {
                        Business.sendUbt({
                            a: "getScoreConfrimResult-server-response",
                            c: 20020,
                            dd: "20020 success callback rc=0",
                            d: JSON.stringify(res),
                        });
                    } catch (e) {}
                    Business.goBackNav("sback");
                }
            },
            fail: function (res = {}) {
                try {
                    Business.sendUbt({
                        a: "getScoreConfrimResult-server-fail",
                        c: 30010,
                        d:
                            "submitPayholdfrominit callback fall! res: " +
                            JSON.stringify(res || ""),
                    });
                } catch (e) {}
                Business.goBackNav("rback", 4);
            },
            complete: function (res) {},
        }).excute();
    };

    queryScoreStatus = (callBack) => {
        let data = {};
        let that = this;
        let payParam = orderDetailParamStore.get();
        let payresultInfo = payResultOStore.get() || {};
        data.requestid = payParam.requestId;
        data.paytoken = payParam.payToken;
        data.orderid = payParam.orderId;
        data.bid = payresultInfo.brandid;
        data.collectionid = payresultInfo.collectionid;
        data.status = payresultInfo.status;
        let appId = Business.getAppId();

        // 添加appid
        if (appId) {
            data.extend = appId;
        }

        UnWxscoreState({
            data: data,
            success: function (res) {
                if (res.rc == 0) {
                    if (res.status === 1) {
                        return callBack({
                            scoreState: 1,
                            msg: "微信支付分已开通",
                        });
                    } else {
                        HoldUi.hideLoading();
                        return callBack({
                            scoreState: 0,
                            msg: "微信支付分未开通",
                        });
                    }
                } else {
                    HoldUi.hideLoading();
                    return callBack({
                        scoreState: 0,
                        msg: res.rmsg || "服务返回异常！",
                    });
                }
            },
            fail: function () {
                HoldUi.hideLoading();
                // console.log("查询失败，请重试！")
                return callBack({ scoreState: 0, msg: "查询失败，请重试！" });
            },
            complete: function () {},
        }).excute();
    };

    getScoreResult = () => {
        const that = this;

        that.queryScoreStatus((res) => {
            if (res.scoreState == 1) {
                HoldUi.showLoading("订单提交中..");
                try {
                    Business.sendUbt({
                        a: "getScoreResult",
                        c: 30011,
                        d: "getScoreResult res: " + JSON.stringify(res || ""),
                    });
                } catch (e) {}
                SubmitHold.call(that, function (res = {}) {
                    HoldUi.hideLoading();
                    try {
                        Business.sendUbt({
                            a: "SubmitScoreback",
                            c: 30011,
                            d:
                                "SubmitScoreback res: " +
                                JSON.stringify(res || ""),
                        });
                    } catch (e) {}
                    if (res.result === 1) {
                        //开通授权成功
                        try {
                            Business.sendUbt({
                                a: "SubmitScoreback sbackcallback",
                                c: 30011,
                                d:
                                    "SubmitScorebackcallback" +
                                    busData.sbackCallback,
                            });
                        } catch (e) {}
                        that.goBackNav("sback");
                    } else if (res.result == 16 || res.result == 17) {
                        //1.处理风控返回 2.用户修改了手机号或者新卡输入了手机号 需传到风控页
                        let _msg =
                            "支付提交失败，如有疑问，请联系携程客服：95010";
                        HoldUi.modalConfirm(_msg, function () {
                            that.goBackNav("rback", 4);
                        });
                    } else if (res.result == 18) {
                        HoldUi.modalConfirm("请先开通微信支付分扣款授权");
                    } else {
                        that.goBackNav("eback", 4);
                    }
                });
            } else {
                HoldUi.modalConfirm(res.msg, function () {});
            }
        });
    };

    showamt = () => {
        navigateTo({
            url: "/pages/alonePlatform/cqpay/protocol/index",
            callback: function (json) {},
            success: function (data) {},
            fail: function (data) {},
            complete: function (data) {},
        });
    };
    getholdResult = () => {
        let that = this;
        let orderDetail = orderDetailStore.get() || {};
        let resData = payResultOStore.get() || {};
        let bustype = orderDetail.bustype;
        let rData = {
            bustype: bustype,
            oid: orderDetail.oid,
        };

        if (resData) {
            if (resData.brandid) {
                rData.bid = resData.brandid;
            }

            if (resData.collectionid) {
                rData.collectionid = resData.collectionid;
            }
        }

        try {
            Business.sendUbt({
                a: "getholdResult",
                c: 30011,
                d: "getholdResult start!",
            });
        } catch (e) {}
        that.holdData = {
            oid: orderDetail.oid,
            fromHoldPage: true,
        };
        GetHoldResult.call(that, rData, function (res = {}) {
            let rescode = res.rc;
            HoldUi.hideLoading();
            if (rescode == 1) {
                //errno:1:服务端错误即原errorInformation， res; 2:解析错误
                try {
                    Business.exceptionInfoCollect(
                        {
                            bustype: 4,
                            excode: 3003,
                            extype: 1,
                            exdesc:
                                "20010 服务返回RC=1错误, " +
                                JSON.stringify(res),
                        },
                        "1",
                    );
                    Business.sendUbt({
                        a: "WxholdResultModel",
                        c: 20031,
                        dd: "20031 success callback rc=1",
                        d: JSON.stringify(res),
                    });
                } catch (e) {}
                HoldUi.modalConfirm(
                    res.rmsg || "系统异常，请稍后再试 -5555",
                    function () {
                        return that.getholdResult();
                    },
                    true,
                    "重试",
                );
            } else if (rescode == 0) {
                try {
                    Business.sendUbt({
                        a: "WxholdResultModel",
                        c: 20030,
                        dd: "20030 success callback rc=0",
                        d: JSON.stringify(res),
                    });
                } catch (e) {}
                if (res.status == 1) {
                    that.setState({
                        payTxt: "确认",
                        fromed: true,
                    });

                    setTimeout(function () {
                        that.submitPayhold();
                    }, 600);
                } else {
                    HoldUi.modalConfirm(
                        "开通微信支付授权扣款失败",
                        function () {},
                        true,
                        "重试",
                    );
                }
            }
        });
    };

    requestFail = (res) => {
        HoldUi.hideLoading();
        if (res && res.retCode && res.retCode != 2) {
            HoldUi.modalConfirm("系统异常，请稍后再试 -505-1", function () {
                // cwx.navigateBack({});
                Business.goBackNav("eback");
            });
        } else {
            HoldUi.modalConfirm("系统异常，请稍后再试 -505-2", function () {
                // cwx.navigateBack({});
                Business.goBackNav("eback");
            });
        }
        try {
            Business.sendUbt({
                actionType: "requestFail",
                a: "requestfail",
                c: 300500,
                d: JSON.stringify(res || ""),
            });
        } catch (e) {}
    };
    rquestTimeout = (res = {}, scode) => {
        let that = this;
        let rmsg = res.errMsg || "";
        let _scode = scode.substring(4);
        try {
            Business.sendUbt({
                actionType: "rquestTimeout",
                a: "request complete",
                c: 300510,
                d: "服务号：" + scode + "微信响应:" + rmsg,
            });
        } catch (e) {}

        if (rmsg.indexOf("request:fail timeout") > -1) {
            try {
                Business.sendUbt({
                    a: "request timeout",
                    c: 300512,
                    d: "服务号：" + scode + "微信响应:" + rmsg,
                });
            } catch (e) {}

            HoldUi.modalConfirm(
                "网络不给力，请稍候重试 - 521-1 " + _scode,
                function () {
                    Business.goBackNav("eback");
                },
            );
            return;
        }
        if (rmsg.indexOf("request:fail") > -1) {
            try {
                Business.sendUbt({
                    a: "request fail",
                    c: 300513,
                    d: "服务号：" + scode + "微信响应:" + rmsg,
                });
            } catch (e) {}

            HoldUi.modalConfirm(
                "网络不给力，请稍候重试 - 522-1 " + _scode,
                function () {
                    Business.goBackNav("eback");
                },
            );
        }
    };

    miniSuccess = (res = "") => {
        try {
            Business.sendUbt({
                actionType: "miniSuccess",
                a: "navigateToMiniProgram success",
                c: 30030,
                d: JSON.stringify(res),
            });
        } catch (e) {}
        this.navigatorState = 1;

        this.optionsData.miniAppBack = true;
        const setStateData = {
            submiting: false,
        };

        this.setState(setStateData);
    };
    miniFail = (res = "") => {
        try {
            let errMsg = res && res.detail && res.detail.errMsg;
            let type = res && res.type;
            Business.sendUbt({
                actionType: "miniFail",
                a: "navigateToMiniProgram fail",
                c: 30031,
                d: "errMsg: " + errMsg + "; type: " + type,
            });
        } catch (e) {}
        HoldUi.modalConfirm("唤醒服务失败，请稍候重试 - 1038.");
    };
    miniComplete = () => {};

    render() {
        return (
            <Block>
                <View
                    className={
                        this.state.nopayment ? `hpaybox hidediv` : `hpaybox`
                    }
                >
                    <View className="hpbox-inner">
                        <Text className="hpaytip">{this.state.payTitle} </Text>

                        {this.state.isWXHF ? (
                            <View className="hpayitem">
                                <Text className="hpaytextwxf">
                                    {this.state.descTxt}
                                </Text>
                            </View>
                        ) : (
                            <View className="hpayitemwxf">
                                <Text className="hpaytext">
                                    {this.state.descTxt}
                                </Text>
                                <Text className="hpaysubtext">
                                    {this.state.descSubTxt}
                                </Text>
                            </View>
                        )}

                        {this.state.fromed ? (
                            <Button className="hpaybtn" onClick="submitPayhold">
                                {this.state.payTxt}
                            </Button>
                        ) : this.state.loading ? (
                            <Button
                                loading="{this.state.loading}"
                                disabled="{this.state.loading}"
                                className="hpaybtn"
                            >
                                {this.state.payTxt}
                            </Button>
                        ) : (
                            <Navigator
                                hover-className="none"
                                target="miniProgram"
                                open-type="navigate"
                                app-id={this.state.navigatorData.appId}
                                path={this.state.navigatorData.pathUrl}
                                extra-data={this.state.extData}
                                version="release"
                                onSuccess={this.miniSuccess.bind(this)}
                                onFail={this.miniFail.bind(this)}
                                onComplete={this.miniComplete.bind(this)}
                            >
                                <Button className="hpaybtn">
                                    {this.state.payTxt}
                                </Button>
                            </Navigator>
                        )}

                        <View className="hpayamtw">
                            请仔细阅读并充分理解
                            <Text
                                className="hpayamt"
                                onClick={this.showamt.bind(this)}
                            >
                                《代扣服务协议》
                            </Text>
                        </View>

                        {/* <div className="hpayamtw" onTap={this.TestResultEvent.bind(this)}>测试查询结果</div> */}
                    </View>
                </View>

                <View
                    className={
                        this.state.nopayment ? `hnopay` : `hnopay hideview`
                    }
                >
                    <View className="hpayeicon"></View>
                    <Text className="hpayetext">
                        您预订的产品无法进行授权支付，请至去哪儿应用程序订购或电话联系我们
                    </Text>
                </View>
            </Block>
        );
    }
}

export default P;
