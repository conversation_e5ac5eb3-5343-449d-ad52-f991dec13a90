import { Block, View, Text, Button } from "@tarojs/components";
import {
    hideShareMenu,
    getCurrentInstance,
    getApp,
    showToast,
    navigateTo,
    showModal,
} from "@tarojs/taro"; /* eslint-disable */
import React from "react";
import "./index.scss";

import Business from "../../common/utils/cqpay/common/business";
import HoldUi from "../../common/utils/cqpay/components/holdui";
import HoldPayCtrl from "../../common/utils/cqpay/holdpayNew/ctrlNew";
import Profile from "../../common/utils/cqpay/holdpayNew/profile.wx.js";
import paymentStore from "../../common/utils/cqpay/models/stores";
// import { WITHHOLDPAYCALLBACKEVENT } from '../../common/utils/cqpay/const/index';
import CountDown from "../../components/CountDown/index.js";

const isObject = function (str) {
    return typeof str == "object";
};

let orderDetailStore = paymentStore.HoldTokenStore();
let payItemDisplayStore = paymentStore.HoldResultOrderStore();
let wxscoreStore = paymentStore.WxscoreStore();

class P extends React.Component {
    constructor(props) {
        super(props);
        this.currentInstance = getCurrentInstance();
        this.state = {
            payTitle: "",
            nopayment: false,
            descTxt: "微信支付授权扣款",
            payTxt: "去开通",
            descSubTxt: "",
            loading: true,
            amt: false,
            fromed: false,
            hasHold: false, //102服务下发是否已经开通过授权
            directed: false,
            isWXHF: false,
            expireTime: 0,
        };
        Business.sendUbt({
            actionType: "holdpay_constructor",
            a: "holdpay-constructor",
            c: 30011,
            d: "",
        });
    }
    pageName = "holdpay";
    optionsData = {
        token: "",
        pageSource: "",
        scene: "",
        miniAppBack: false,
        onceLogin: false,
        isNavBack: false,
    };

    componentWillMount() {
        hideShareMenu();
        let queryObj = this.currentInstance.router.params;
        Business.sendUbt({
            actionType: "holdpay_componentWillMount",
            a: "holdpay-componentWillMount",
            c: 30011,
            d: "参数为: " + JSON.stringify(queryObj),
        });
        this.checkNeedFetchData(queryObj);
    }

    // 有cashierUrl或tradeNo的是h5直连，需要请求服务获取签名,cashierUrl用于兼容已有BU
    checkNeedFetchData = (options) => {
        // 如果传入cashierUrl，则请求接口获取数据
        if (options.cashierUrl || options.tradeNo) {
            Profile.init({
                success(res) {
                    console.log(">>>>>>[success] app.js - init res:", res);
                    Profile.getClientID((cid) => {
                        console.log(">>>>>>[test] onLoad, cid: ", cid);
                        wxscoreStore.set({});
                        wxscoreStore.setAttr("cid", cid);
                    });
                },
                fail(err) {
                    console.log(">>>>>>[fail] app.js - init err:", err);
                },
            });
            const optionData = {
                tradeNo: options.tradeNo,
                cashierUrl: decodeURIComponent(options.cashierUrl),
                sBackUrl:
                    options.sBackUrl && decodeURIComponent(options.sBackUrl),
                eBackUrl:
                    options.eBackUrl && decodeURIComponent(options.eBackUrl),
            };
            HoldPayCtrl.initH5(optionData, () => this.initData());
        } else {
            this.initData();
        }
    };

    initData = () => {
        let that = this;
        let navData = wxscoreStore.get(); //小程序跳转过来带的data参数
        let payItemDisplay = payItemDisplayStore.get() || {};
        const orderDetail = orderDetailStore.get() || {};

        Business.sendUbt({
            actionType: "holdpay_initData",
            a: "holdpay-initData",
            c: 30011,
            d: "orderDetail为: " + JSON.stringify(orderDetail),
        });
        console.log("---------orderDetail start----------------");
        console.log(orderDetail);
        console.log(payItemDisplay);
        console.log("---------orderDetail end------------------");

        if (isObject(navData)) {
            if (navData.nopayment) {
                that.setState({
                    nopayment: navData.nopayment,
                });
                return;
            }
            const { wxScoreChannel, isWxScoreConfirmChannel } = navData;
            that.isWxScoreConfirmChannel = isWxScoreConfirmChannel;

            if (isWxScoreConfirmChannel) {
                that.setState({
                    directed: false,
                    payTitle: "",
                    descTxt: payItemDisplay.name,
                    descSubTxt: payItemDisplay.caption,
                    payTxt: "使用 微信支付分 授权",
                    loading: false,
                });
            } else if (wxScoreChannel) {
                Business.sendUbt({
                    actionType: "holdpay_initData_wxScoreChannel",
                    a: "holdpay-initData",
                    c: 30011,
                    d: "payresultInfo为: " + JSON.stringify(payItemDisplay),
                });

                that.setState({
                    directed: false,
                    payTitle: "",
                    descTxt: payItemDisplay.name,
                    descSubTxt: payItemDisplay.caption,
                    payTxt: "使用 微信支付分 授权",
                    loading: false,
                });
            } else {
                that.setState({
                    directed: false,
                    payTitle: navData.payTitle,
                    payTxt: "去开通",
                    isWXHF: true,
                });

                //自动处理开通接口服务
                // that.autoSubmitHold();
            }
            // 有效期时间优先取 orderValidity， 兜底为7200
            that.setState({
                expireTime:
                    orderDetail.orderValidity >= 0
                        ? orderDetail.orderValidity
                        : 0,
            });
        }
    };
    async componentDidShow() {
        const that = this;
        const { globalData = {} } = getApp();
        const scene =
            (globalData.appShowOptions && globalData.appShowOptions.scene) ||
            "";
        const referrerInfo =
            (globalData.appShowOptions &&
                globalData.appShowOptions.referrerInfo) ||
            {};
        Business.sendUbt({
            actionType: "onShow_start",
            a: "onShow-start",
            c: 200211,
            dd: "scene : " + scene,
            d: JSON.stringify(referrerInfo),
        });

        if (scene === 1038) {
            // React.api.showToast({ title: '微信分返回' })
            console.log("that.optionsData.miniAppBack start");
            console.log(that.optionsData);
            console.log(that.optionsData.miniAppBack);
            console.log("that.optionsData.miniAppBack end");
            if (that.optionsData.miniAppBack) {
                const { appId } = referrerInfo;
                that.optionsData.miniAppBack = false;
                that.setState({
                    submiting: true,
                });
                showToast({ title: "查询签约状态..." });
                console.log("查询签约状态 appid", appId);
                if (appId == "wxbd687630cd02ce1d") {
                    // 这个appid是啥？？？
                    Business.sendUbt({
                        actionType: "onShow-start-houfu",
                        a: "onShow-start-houfu",
                        c: 200211,
                        dd: "scene : " + scene,
                        d: JSON.stringify(referrerInfo),
                    });
                    that.navigatorState = 0;
                    HoldUi.showLoading("加载中...");
                    setTimeout(function () {
                        that.getholdResult();
                    }, 300);
                } else if (appId == "wxd8f3793ea3b935b8") {
                    // 从微信分回来
                    that.navigatorState = 0;
                    HoldUi.showLoading("加载中..");
                    if (that.isWxScoreConfirmChannel) {
                        try {
                            Business.sendUbt({
                                a: "navigateToMiniProgram back 进入需确认模式",
                                c: 30033,
                                d: "",
                            });
                        } catch (e) {}

                        setTimeout(function () {
                            HoldPayCtrl.getScoreConfrimResult();
                        }, 300);
                        return;
                    }
                    Business.sendUbt({
                        actionType: "onShow-start-zffmm",
                        a: "onShow-start-houfu",
                        c: 200211,
                        dd: "scene : " + scene,
                        d: JSON.stringify(referrerInfo),
                    });
                    setTimeout(function () {
                        HoldPayCtrl.GetHoldResult();
                    }, 300);
                }
            }
        }
    }

    TestResultEvent = () => {
        that.isWxScoreConfirmChannel
            ? HoldPayCtrl.getScoreConfrimResult()
            : HoldPayCtrl.GetHoldResult();
    };

    componentWillUnmount() {
        Business.sendUbt({
            actionType: "componentWillUnmount",
            a: "onShow-start-houfu",
            c: 200211,
            dd: "",
            d: "",
        });
    }
    // 跳转协议
    showamt = () => {
        navigateTo({
            url: "/pages/qunar/subPages/alonePlatform/cqpay/protocol/index",
            callback: function () {},
            success: function () {},
            fail: function () {},
            complete: function () {},
        });
    };

    // 查询微信分授权结果-免确认
    getholdResult = () => {
        HoldPayCtrl.GetHoldResult();
    };

    // 点击授权
    handlePay = () => {
        if (this.state.expireTime > 0) {
            HoldPayCtrl.toInvokeWxScore({
                success: () => {
                    this.optionsData.miniAppBack = true;
                },
            });
        } else {
            this.handldCountEnd();
        }
    };

    handldCountEnd = () => {
        showModal({
            title: "超过授权时限，请重新下单",
            confirmText: "知道了",
            showCancel: false,
            success() {
                // 1、调用关单接口关闭授权单
                // 2、返回BU前端回调页，返回“授权超时状态”
                HoldPayCtrl.eBack({
                    rc: 5,
                    delay: 0,
                    title: "授权超时",
                });
            },
        });
    };

    render() {
        return (
            <Block>
                <View
                    className={
                        this.state.nopayment ? `hpaybox hidediv` : `hpaybox`
                    }
                >
                    <View className="hpbox-inner">
                        <View className="tip-box">
                            <Text className="hpaytip">
                                {this.state.payTitle}{" "}
                            </Text>
                            {this.state.expireTime ? (
                                <CountDown
                                    onLineEnd={this.handldCountEnd}
                                    doneText="授权超时，请重新下单"
                                    time={this.state.expireTime}
                                    prefix="剩余时间："
                                ></CountDown>
                            ) : null}
                        </View>

                        {this.state.isWXHF ? (
                            <View className="hpayitem">
                                <Text className="hpaytextwxf">
                                    {this.state.descTxt}
                                </Text>
                            </View>
                        ) : (
                            <View className="hpayitemwxf">
                                <Text className="hpaytext">
                                    {this.state.descTxt}
                                </Text>
                                <Text className="hpaysubtext">
                                    {this.state.descSubTxt}
                                </Text>
                            </View>
                        )}

                        {this.state.fromed ? (
                            <Button className="hpaybtn" onClick="submitPayhold">
                                {this.state.payTxt}
                            </Button>
                        ) : this.state.loading ? (
                            <Button
                                loading="{this.state.loading}"
                                disabled="{this.state.loading}"
                                className="hpaybtn"
                            >
                                {this.state.payTxt}
                            </Button>
                        ) : (
                            <Button
                                className="hpaybtn"
                                onClick={this.handlePay}
                            >
                                {this.state.payTxt}
                            </Button>
                        )}

                        <View className="hpayamtw">
                            请仔细阅读并充分理解
                            <Text
                                className="hpayamt"
                                onClick={this.showamt.bind(this)}
                            >
                                《代扣服务协议》
                            </Text>
                        </View>

                        {/* <div className="hpayamtw" onTap={this.TestResultEvent.bind(this)}>测试查询结果</div> */}
                    </View>
                </View>

                <View
                    className={
                        this.state.nopayment ? `hnopay` : `hnopay hideview`
                    }
                >
                    <View className="hpayeicon"></View>
                    <Text className="hpayetext">
                        您预订的产品无法进行授权支付，请至去哪儿应用程序订购或电话联系我们
                    </Text>
                </View>
            </Block>
        );
    }
}

export default P;
