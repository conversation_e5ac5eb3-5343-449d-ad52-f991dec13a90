import { View, RichText, WebView } from "@tarojs/components";
import { getCurrentInstance } from "@tarojs/taro";
import React from "react";
import request from "@/common/utils/request";
import util from "@/common/utils/util.js";
import HoldUi from "../../common/utils/cqpay/components/holdui";
import "./index.scss";
import paymentStore from "../../common/utils/cqpay/models/stores.js";

class P extends React.Component {
    constructor(props) {
        super(props);
        this.currentInstance = getCurrentInstance();
        this.state = {
            htmlSnip: "",
            htmlUrl: "",
            isShowText: true,
            isShowWeb: false,
        };
    }

    optionsData = {
        token: "",
        pageSource: "",
        scene: "",
        miniAppBack: false,
        onceLogin: false,
        isNavBack: false,
    };
    getAppId = () => {
        //   this.sendWatcher('getAppId');
        let accountInfo = {};
        if (process.env.ANU_ENV === "wx") {
            accountInfo = wx.getAccountInfoSync();
        }
        const miniProgram = accountInfo.miniProgram || {};
        return miniProgram.appId || "";
    };
    //EN Get market OPENID
    //CN 获取市场OPENID
    getMktOpenid = () => {
        const { cookies } = util.getGlobalInfo();
        const cuserOpenId = cookies.openId || "";
        return cuserOpenId;
    };

    componentDidMount() {
        console.log("base componentDidMount");
        this.initData(this.currentInstance.router.params);
    }
    initData = (queryObj) => {
        const reqparam = queryObj.payagreeId;
        console.log("reqparam", reqparam);
        if (reqparam) {
            HoldUi.showLoading("加载中...");
            const data = {
                reqparam,
                reqtype: 6,
            };
            this.sendRequest("********", data)
                .then((res) => {
                    HoldUi.hideLoading();
                    console.log(res);
                    this.setState({
                        htmlSnip: res.text,
                    });
                })
                .catch((err) => {
                    HoldUi.hideLoading();
                    console.log(error);
                });
        } else {
            let wxscoreStore = paymentStore.WxscoreStore();
            const scoreData = wxscoreStore.get() || {};
            if (scoreData.frontData) {
                this.setState({
                    htmlUrl: scoreData.frontData.withholdProtocolUrl,
                    isShowText: false,
                    isShowWeb: true,
                });
            }
        }
    };

    sendRequest = (serverCode, data) => {
        const that = this;
        const requestUrl = "/mobile/member/cqmiddleplatform/dispatcher.htm";
        const optionsData = that.optionsData;
        const openid = that.getMktOpenid();
        const appId = that.getAppId();
        const { token, pageSource, scene, onceLogin } = optionsData;

        let globalPload = {};
        globalPload.payload = {
            ver: "8.23",
            cver: "8.13",
            plat: 5,
            mchid: "CTRP",
            openId: openid,
            appId: appId,
            scene: scene,
        };

        if (token) {
            globalPload.payload.token = token;
        } else {
            globalPload.payload.pageSource = decodeURIComponent(pageSource);
        }

        globalPload.head = {
            cid: "",
            ctok: "",
            cver: "1.0",
            lang: "01",
            sid: "8888",
            syscode: "09",
            auth: null,
        };
        globalPload.requestHead = JSON.stringify({
            serviceCode: serverCode,
            loginType: "QUNAR",
        });

        const payloadData = Object.assign(globalPload.payload, data);
        globalPload.payload = JSON.stringify(payloadData);
        const initUserData = util.getGlobalInfo();
        return new Promise((resolve, reject) => {
            if (openid == "") {
                //   that.sendWatcher('nothas_openid');
                return reject();
            }
            request(
                {
                    service: requestUrl,
                    method: "POST",
                    returnAll: true,
                    data: globalPload,
                    success: function (res = {}) {
                        const { ResponseStatus, payload } = res.data;
                        const { Ack, Errors } = ResponseStatus;
                        let resData = {};
                        if (Ack == "Success" && payload) {
                            resData = JSON.parse(payload);
                            resolve(resData);
                        } else {
                            let errMsgs = {};
                            if (Array.isArray(Errors)) {
                                errMsgs = Errors[0];
                            } else {
                                if (
                                    Object.prototype.toString
                                        .call(Errors)
                                        .toLowerCase() == "[object object]" &&
                                    !Errors.length
                                ) {
                                    errMsgs = Errors;
                                } else {
                                    errMsgs.Message = Errors;
                                }
                            }

                            const messageStr = errMsgs.Message || "";
                            if (
                                messageStr.includes("auth for authentication")
                            ) {
                                if (!onceLogin) {
                                    that.gotoLogin();
                                }
                                //   that.sendWatcher('no_auth_for_authentication', messageStr + 'onceLogin: ' + onceLogin);
                                return;
                            }

                            if (messageStr.includes("Unable to deserialize")) {
                                //   that.sendWatcher('unable_to_deserialize', messageStr);
                            } else {
                                //   that.sendWatcher('request_ackfail_errmsg', messageStr);
                            }
                            reject(res);
                        }
                    },
                    fail: function (err) {
                        reject(err);
                    },
                },
                initUserData,
            );
        });
    };
    render() {
        return (
            <View class="content">
                {this.state.isShowText ? (
                    <RichText nodes={this.state.htmlSnip}></RichText>
                ) : null}
                {this.state.isShowWeb ? (
                    <WebView src={this.state.htmlUrl}></WebView>
                ) : null}
            </View>
        );
    }
}

export default P;
