import { View } from "@tarojs/components";
import {
    getCurrentInstance,
    setNavigationBarTitle,
    hideShareMenu,
    showToast,
} from "@tarojs/taro";

import React from "react";
import WebView from "@platformComponents/WebView/index";
import QunarLoading from "@platformComponents/QunarLoading/index";
import request from "@/common/utils/request";
import LogQmark from "@/common/utils/logQmark";
import CommonUtils from "@/common/utils/util.js";

class Data extends React.Component {
    constructor(props) {
        super(props);
        this.currentInstance = getCurrentInstance();
        this.state = {
            url: this.currentInstance.router.params.url,
            loginSync: this.currentInstance.router.params.loginSync,
            platform_webview_not_need_params:
                this.currentInstance.router.params
                    .platform_webview_not_need_params,
            islogin: true,
            networkData: {
                status: 4,
                loadingDesc: "",
            },
        };
        this.setNavBar();
    }
    setNavBar = () => {
        const { nfc, nbc } = this.currentInstance.router.params; //默认走config
        if (process.env.ANU_ENV === "wx" && nfc && nbc) {
            wx.setNavigationBarColor({
                frontColor: nfc, //按钮、标题、状态栏的颜色，仅支持 #ffffff 和 #000000
                backgroundColor: nbc, //背景颜色
            });
        }
    };
    // 华为快应用跳转补丁
    static protected =
        process.env.ANU_ENV === "quick"
            ? {
                  url: "",
                  loginSync: "",
              }
            : {};

    componentDidMount() {
        const { url, loginSync, title, hideShareMenu } =
            this.currentInstance.router.params;
        if (title) {
            setNavigationBarTitle({
                title: decodeURIComponent(title),
            });
        }
        // 隐藏分享功能按钮
        if (hideShareMenu) {
            hideShareMenu();
        }
        this.setState({
            url,
            loginSync,
        });
    }

    componentDidShow() {
        this.checkNetwork();
    }
    // 检测网络状况
    checkNetwork = () => {
        request({
            service: "/mpx/getQconfig",
            param: {
                name: "qunar_miniprogram_ads.json",
            },
            success: () => {
                this.setState({
                    networkData: {
                        status: 0,
                        loadingDesc: "",
                    },
                    islogin: false,
                });
            },
            fail: () => {
                this.setState({
                    networkData: {
                        status: -1,
                        loadingDesc: "网络连接失败 ",
                        showButton: true,
                    },
                });
            },
        });
    };

    networkRetry = () => {
        this.checkNetwork();
    };

    syncShareData = (data) => {
        this.shareTitle = data.title;
        this.shareImageUrl = data.imageUrl;
        this.sharePath = data.path;
    };

    // 分享未完成
    onShareAppMessage() {
        LogQmark({
            module: "default",
            id: "shareClick",
            page: "webView",
            operType: "click",
            ext: {
                url: this.state.url,
            },
        });
        const baseUrl = decodeURIComponent(this.state.url).split("?")[0];
        // 分享调后端接口,后端落库和统计监测
        if (baseUrl.indexOf("shark/active") > 0) {
            const pathNameArr = baseUrl && baseUrl.split("/");
            const cid =
                pathNameArr &&
                pathNameArr.length &&
                pathNameArr[pathNameArr.length - 1];
            const { cookies = {} } = CommonUtils.getGlobalInfo();
            const openId = cookies.openId;
            request({
                host: "https://m.flight.qunar.com",
                service: "/gw/u/gw/wechatofficial/shareEvent",
                data: {
                    openId,
                    cid,
                },
            });
        }

        const share_url = this.state.platform_webview_not_need_params
            ? encodeURIComponent(
                  decodeURIComponent(this.state.url).split("?")[0],
              )
            : this.state.url;
        let options = {
            title: this.shareTitle || "去哪儿旅行",
            path:
                this.sharePath ||
                `/pages/platform/index/index?nt=${encodeURIComponent(`/pages/qunar/subPages/alonePlatform/webView/index?url=${share_url}&loginSync=${this.state.loginSync}`)}`,
            success: function () {
                showToast({
                    icon: "success",
                    title: "分享成功",
                    duration: 2000,
                });
            },
            fail: function () {
                // 转发失败
                showToast("");
                showToast({
                    icon: "none",
                    title: "分享失败或取消",
                    duration: 2000,
                });
            },
        };
        if (this.shareImageUrl) {
            options.imageUrl = this.shareImageUrl;
        }
        return options;
    }

    render() {
        return (
            <View>
                {this.state.islogin ? (
                    <QunarLoading
                        networkRetry={this.networkRetry.bind(this)}
                        networkData={this.state.networkData}
                    />
                ) : (
                    <WebView
                        src={this.state.url}
                        loginSync={this.state.loginSync}
                        syncShareData={this.syncShareData.bind(this)}
                    ></WebView>
                )}
            </View>
        );
    }
}
export default Data;
