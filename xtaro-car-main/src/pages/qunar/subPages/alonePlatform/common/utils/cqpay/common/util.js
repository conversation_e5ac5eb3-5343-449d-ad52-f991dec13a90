var ret = {};

ret.isUrl = (url) => /https?:/.test(url);

ret.getParam = (param, url) => {
    try {
        const paramStrArr = url.split("?");
        if (!paramStrArr.length) {
            return null;
        }
        const paramStr = paramStrArr[1];
        const tempArray = paramStr.split("&");
        var tempObj = {};
        for (let i = 0; i < tempArray.length; i++) {
            let obj = tempArray[i];
            let innerTempArr = obj.split("=");
            tempObj[innerTempArr[0]] = innerTempArr[1];
        }
        if (tempObj[param]) {
            return tempObj[param];
        } else {
            return null;
        }
    } catch (error) {
        return null;
    }
};

ret.appendQuery = function (url, query) {
    var urlquery = url + "&" + query || "";
    return urlquery.replace(/[&?]{1,2}/, "?");
};

ret.pageQueryStr = function (data) {
    return Object.keys(data)
        .map(function (key) {
            let v = encodeURIComponent(data[key]);
            if (v === "undefined") {
                v = "";
            }
            return encodeURIComponent(key) + "=" + v;
        })
        .join("&");
};

ret.transNumToFixedArray = function (num, maxlength, currency) {
    num = num + "";
    if (!num) {
        return "";
    }
    //判断num是否是数字字符串
    var reg = /^\d*\.*\d+$/;
    if (!reg.test(num)) {
        return num;
    }
    maxlength = maxlength || 2;

    var array = num.split(".");
    var hzStr = "";
    var curlength = 0;
    if (array.length > 1) {
        curlength = array[1].length;
        hzStr = array[1];
    }

    for (var i = 0; i < maxlength - curlength; i++) {
        hzStr += "0";
    }
    array[1] = hzStr;
    return array;
};

ret.dateFormat = function (fmt, date) {
    let regArr;
    const opt = {
        "Y+": date.getFullYear().toString(),
        "m+": (date.getMonth() + 1).toString(),
        "d+": date.getDate().toString(),
        "H+": date.getHours().toString(),
        "M+": date.getMinutes().toString(),
        "S+": date.getSeconds().toString(),
    };
    for (let k in opt) {
        regArr = new RegExp("(" + k + ")").exec(fmt);
        if (regArr) {
            fmt = fmt.replace(
                regArr[1],
                regArr[1].length == 1
                    ? opt[k]
                    : opt[k].padStart(regArr[1].length, "0"),
            );
        }
    }
    return fmt;
};

ret.Object2String = function (json) {
    let key;
    let retArr = [];
    for (key in json) {
        retArr.push(key + "=" + json[key]);
    }
    return retArr.join("&");
};

ret.queryToParam = function (query) {
    const params = {};
    const arr = query.split("&");
    let key, value;
    for (var i = 0; i < arr.length; i++) {
        [key = "", value = ""] = arr[i].split("=");
        // 给对象赋值
        params[key] = value;
    }
    return params;
};

module.exports = ret;
