import {
    showLoading,
    navigateTo,
    hideLoading,
    request,
    showToast,
    getCurrentPages,
} from "@tarojs/taro"; /* eslint-disable */
import React from "react";
import config from "@/common/utils/config/config.js";
import watcher from "@/common/utils/oldWatcher.js"; //埋点
import EventEmitter from "@/common/utils/EventEmitter.js";
import util from "@/common/utils/util.js";
import user from "@/common/utils/user.js";

var source = "cashier";

async function init(s, AntiDisturbanceUrl = "") {
    source = s || source;
    if (source === "cashier") {
        // 微信支付后实名引导 走新流程
        showLoading({
            title: "正在跳转",
        });

        const res = await user.checkLogin();
        const isLogin = res.data && res.data.isLogin;
        if (isLogin && AntiDisturbanceUrl) {
            // 登录情况下处理
            AntiDisturbanceRequest(AntiDisturbanceUrl); // 防打扰处理
        }
        const url = `/pages/cwallet/setrealname/index?pageSource=q_wechatmini_payment`;
        navigateTo({
            url,
            complete: () => {
                hideLoading();
            },
        });
        return;
    }
    getToken(AntiDisturbanceUrl);
}

function getToken(AntiDisturbanceUrl) {
    const url = `${config.settings.requestDomain}/mobile/h5/member/realName/queryUserIdToken.htm`; // TODO default?
    request({
        url: url,
        method: "GET",
        header: {
            "content-Type": "application/x-www-form-urlencoded",
            Cookie: getCookies(),
        },
        success: function (res) {
            let data = res.data || {};
            data.AntiDisturbanceUrl = AntiDisturbanceUrl;
            doData(data);
        },
        fail: function (res) {
            showToast({ title: "网络异常，请稍后重试" });
            sendWatcher("queryUserIdToken_net_error");
        },
    });
}

// 三种情况： 1）没登录 2）已登录，已实名 3）已登录，未实名
function doData(data) {
    const {
        status,
        message,
        data: { userIdToken, isRealName },
    } = data;
    let param = {
        guideLogin: false,
        pageNo: 0,
        source,
        userIdToken: "",
        fromAuthOpen: "true",
    };
    if (status === 0) {
        if (isRealName) {
            // 已实名
            if (source === "cashier") {
                // 在【用户已登录】的情况下，还未展示实名引导页面，且查询到用户已实名，则直接回调业务线，不展示实名成功页
                EventEmitter.dispatch("onAuthComplete");
                return;
            }
            param.pageNo = 1; // 1表示实名成功页
            sendWatcher("user_has_auth");
        } else {
            // 未实名
            if (source === "cashier") {
                // 在还未展示登录实名引导页面之前，若用户已登录未实名，需要调后端防打扰的接口，无需关注返回结果。
                AntiDisturbanceRequest(data.AntiDisturbanceUrl);
            }
            param.userIdToken = userIdToken || "";
            param.pageNo = 0; // 0表示实名认证去授权页
            sendWatcher("queryUserIdToken_success", { userIdToken });
        }
        source = source === "cashier" ? "cashier_wxlogin" : source;
    } else if (status === -10 && message === "用户未登录") {
        param.guideLogin = true;
        source = "cashier_wxnologin";
        sendWatcher("user_no_login");
    } else {
        wx.showToast({ title: "获取获取用户信息失败，请稍后重试" });
        sendWatcher("queryUserIdToken_fail");
        return;
    }

    const currentPages = getCurrentPages();
    const len = currentPages.length;
    const authPagePath = "pages/platform/wxPay/realNameAuth/index";
    if (
        currentPages[len - 1].route == authPagePath ||
        (len > 1 && currentPages[len - 2].route == authPagePath)
    ) {
        EventEmitter.dispatch("getRealNameAuthData", param);
        return;
    }
    gotoAuthPage(param);
}

function AntiDisturbanceRequest(url) {
    if (!url) {
        return;
    }
    request({
        url,
        method: "GET",
        header: {
            "content-Type": "application/x-www-form-urlencoded",
            Cookie: getCookies(),
        },
    });
}

function gotoAuthPage({ userIdToken = "", pageNo = 0, guideLogin = false }) {
    const url = `/pages/alonePlatform/wxPay/realNameAuth/index?source=${source}&userIdToken=${userIdToken}&pageNo=${pageNo}&guideLogin=${guideLogin}&fromAuthOpen=true`;
    navigateTo({ url });
}

function sendWatcher(actionType, exParams) {
    var opts = exParams || {};
    opts["action-type"] = actionType || "";
    opts["page"] = "auth_wechat";
    opts.from = source;
    watcher.click(opts);
}

function getCookies() {
    const globalInfo = util.getGlobalInfo() || {};
    const cookies = globalInfo.cookies || {};
    const cookiesArr = [
        "_q=" + cookies._q,
        "_v=" + cookies._v,
        "_t=" + cookies._t,
        "_s=" + cookies._s,
    ];
    return cookiesArr.join(";");
}
export default {
    openAuth: init,
};
