import { getApp, navigateBack } from "@tarojs/taro";
import paymentStore from "../models/stores.js";
import util from "@/common/utils/util.js";
import React from "react";
import EventEmitter from "@/common/utils/EventEmitter.js";
import { WITHHOLDPAYCALLBACKEVENT } from "../const/index.js";
import watcher from "@/common/utils/oldWatcher.js";
import Util from "./util";

var orderDetailStore = paymentStore.OrderDetailStore();
var extStore = paymentStore.OrderDetailExtendStore();
var payResultOBStore = paymentStore.PayResultOrderStore();
var HoldTokenStore = paymentStore.HoldTokenStore();
var HoldResultOrderStore = paymentStore.HoldResultOrderStore();
var orderDetailParamStore = paymentStore.OrderDetailParamStore();
var ret = {};
ret.exceptionInfoCollect = function (settings, type) {};
/**
 * 设置oid
 * @function
 * @name setTempOid
 * @description 设置oid
 * <AUTHOR>
 * @memberof common/business
 * @inner
 */
ret.setTempOid = function (data) {
    //门票刚进支付的时候，传的是临时单号，301返回的是正式单号
    if (data && data["oidex"] && data["oidex"] != 0) {
        payResultOBStore.setAttr("realoid", data["oidex"]);
    }
};

/**
 * 设置清除相应的缓存数据
 * @function
 * @name setTempOid
 * @description 设置oid
 * <AUTHOR>
 * @memberof common/business
 * @inner
 */
ret.clearStore = function () {
    orderDetailStore.remove();
    extStore.remove();
    payResultOBStore.remove();
    HoldTokenStore.remove();
    HoldResultOrderStore.remove();
};

/**
 * 发送UBT埋点信息
 * @function
 * @name sendUbtTrace
 * @description 发送UBT埋点信息
 * <AUTHOR>
 * @memberof common/business
 * @inner
 */

ret.sendUbtTrace = function (ubtdata) {
    ret.sendUbt(ubtdata);
};

ret.getAppId = function () {
    let accountInfo = {};
    if (process.env.ANU_ENV === "wx") {
        accountInfo = wx.getAccountInfoSync();
    }
    const miniProgram = accountInfo.miniProgram || {};
    return miniProgram.appId || "";
};

ret.getMktOpenid = function () {
    const { cookies } = util.getGlobalInfo();
    const cuserOpenId = cookies.openId || "";
    return cuserOpenId;
};

ret.goBackNav = function (action = "", rc) {
    console.log("============");
    console.log(getApp());
    let that = this;
    const pagePath = getApp().$$pagePath;
    try {
        wx.hideLoading();
        ret.sendUbt({
            actionType: "goBackNav_in",
            a: "goBackNav",
            c: 30050,
            d:
                "返回上一页面 action: " +
                action +
                " /rc:" +
                rc +
                "  /direct:" +
                JSON.stringify(direct || ""),
        });
    } catch (e) {}

    ret.clearStore(); //清除缓存

    if (/cqpay/gim.test(pagePath)) {
        navigateBack({
            delta: 1,
            success: () => {
                setTimeout(() => {
                    EventEmitter.dispatch(WITHHOLDPAYCALLBACKEVENT, {
                        type: action,
                    });
                }, 200);
            },
        });
    } else {
        setTimeout(() => {
            EventEmitter.dispatch(WITHHOLDPAYCALLBACKEVENT, {
                type: action,
            });
        }, 200);
    }
};

ret.sendUbt = function (json) {
    console.log("ubtlog", JSON.stringify(json));
    const query = {};
    query["action-type"] = json.actionType.replace("-", "_");
    // query.from  = json.pageSource;
    Object.assign(json, orderDetailParamStore.get() || {});
    query.scene = json.scene || "";
    query.page = json.pageName || "holdpay";
    query.messageStr = Util.Object2String(json);
    query._exclude = false;
    console.log("-------------------messageStr start-----------------------");
    console.log(query.messageStr);
    console.log("-------------------messageStr end--------------------------");
    // console.log(query.messageStr);
    watcher.click(query);
};

export default ret;
