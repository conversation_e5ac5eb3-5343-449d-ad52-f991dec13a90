// 重复数据去重
function unique(arr) {
    if (!Array.isArray(arr)) {
        return;
    }
    let arrry = [arr[0]];
    for (var i = 1; i < arr.length; i++) {
        if (arr[i].name !== arr[i - 1].name) {
            arrry.push(arr[i]);
        }
    }
    return arrry;
}

// 处理汽车票的cityList 数据
// interShow: 处理国际数据
function formatCityData(data) {
    var hotCityList = [],
        mainList = {},
        wordArr = [];
    // 先排个序
    data = data.sort(function (item1, item2) {
        return item1.cs - item2.cs;
    });
    for (var i = 0, len = data.length; i < len; i++) {
        var item = data[i];
        // 脏数据
        if (!item.nm || !item.sp) {
            continue;
        }
        // hot city
        if (item.gd == 1) {
            hotCityList.push({
                name: item.nm,
            });
            hotCityList = unique(hotCityList);
        }
        // city groupby word
        var word = item.sp[0].toUpperCase();
        if (!mainList[word]) {
            mainList[word] = [];
            wordArr.push(word);
        }
        mainList[word].push({
            cityName: item.nm,
        });
    }
    wordArr = wordArr.sort();
    // 为 cityMainList 排序
    var cityMainList = [];
    wordArr.forEach(function (item) {
        let obj = {
            key: item,
            value: mainList[item],
        };
        cityMainList.push(obj);
    });

    return {
        hotCities: hotCityList,
        cityMainList: cityMainList,
    };
}

var type = function (obj) {
    var ret = "";
    if (obj === null) {
        ret = "null";
    } else if (obj === undefined) {
        ret = "undefined";
    } else {
        var t = Object.prototype.toString.call(obj);
        var arr = t.match(/^\[object (\w+?)\]$/);
        if (arr) {
            ret = arr[1].toLowerCase();
        } else {
            ret = t;
        }
    }
    return ret;
};
function deepCopy(obj) {
    var ret;
    switch (type(obj)) {
        case "array":
            ret = obj.map(deepCopy);
            break;
        case "object":
            ret = {};
            for (var key in obj) {
                if (obj.hasOwnProperty(key)) {
                    ret[key] = deepCopy(obj[key]);
                }
            }
            break;
        case "date":
            ret = new Date(+obj);
            break;
        default:
            ret = obj;
            break;
    }
    return ret;
}

function getMatchedCityList(srcStr, srcList) {
    function isMatchedCity(srcStr, srcObj) {
        srcObj = srcObj || {};
        var patten = null;
        try {
            patten = new RegExp("(^" + srcStr + "|\\|" + srcStr + ")", "i");
        } catch (ex) {
            return false;
        }
        if ((srcObj.nm || "").indexOf(srcStr) >= 0) {
            return true;
        }
        if (patten.test((srcObj.py || "") + "|" + (srcObj.sp || ""))) {
            return true;
        }
        return false;
    }

    function getMatchedStations(srcStr, srcList) {
        srcList = srcList || [];
        var matchedList = [];
        if (!srcStr || !srcList || srcList.length <= 0) {
            return matchedList;
        }
        var patten = null;
        try {
            patten = new RegExp("(^" + srcStr + "|\\|" + srcStr + ")", "i");
        } catch (ex) {
            return matchedList;
        }
        srcList.forEach(function (item) {
            item = item || {};
            var bFlag =
                (item.nm || "").indexOf(srcStr) >= 0 ||
                patten.test((item.py || "") + "|" + (item.sp || ""));

            bFlag && matchedList.push(deepCopy(item));
        });
        return matchedList;
    }

    function formatMatchedList(srcList) {
        srcList = srcList || [];
        srcList = srcList.sort(function (item1, item2) {
            return item1.cs * item1.gd - item2.cs * item2.gd;
        });
        var mFormated = [];
        srcList.forEach(function (item) {
            mFormated.push({
                name: item.nm,
                srcCity: item.nm,
                srcStation: "",
            });
            var mStations = item.sl || [];
            mStations = mStations.sort(function (item1, item2) {
                return parseInt(item2.gd - item1.gd);
            });
            mStations.forEach(function (sItem) {
                mFormated.push({
                    name: item.nm + " - " + sItem.nm,
                    srcCity: item.nm,
                    srcStation: sItem.nm,
                });
            });
        });
        return mFormated;
    }

    srcList = srcList || [];
    var matchedList = [];

    if (!srcStr) {
        return matchedList;
    }

    srcList.forEach(function (item) {
        item = item || {};
        if (isMatchedCity(srcStr, item)) {
            matchedList.push(Object.assign({}, item));
        } else {
            var srcStations = item.sl || [];
            var mStations = getMatchedStations(srcStr, srcStations);
            if (!!mStations && mStations.length > 0) {
                var mItem = Object.assign({}, item);
                mItem.sl = mStations;
                matchedList.push(mItem);
            }
        }
    });
    var mFormated = formatMatchedList(matchedList);
    return mFormated;
}

export default {
    formatCityData,
    getMatchedCityList,
};
