export default {
    31002001: {
        ResponseStatus: {
            Timestamp: "/Date(1628071133095+0800)/",
            Ack: "Success",
            Errors: [],
            Build: null,
            Version: null,
            Extension: [
                {
                    Id: "CLOGGING_TRACE_ID",
                    Version: null,
                    ContentType: null,
                    Value: "7503344564449423482",
                },
                {
                    Id: "RootMessageId",
                    Version: null,
                    ContentType: null,
                    Value: "*********-0a06b391-452241-6882",
                },
            ],
        },
        rc: 0,
        subcode: "QUICKPAY_SEARCH_WEAPP_0",
        rmsg: "获取支付方式成功",
        dbgmsg: "获取快捷支付方式成功",
        resinfo: {
            ispoint: true,
            needpwd: true,
            rusetype: 0,
            payetype: 4,
        },
        walletdetal: null,
        bankcardinfo: null,
        thirdpay: [
            {
                paymentwayid: "EB_MobileAlipay",
                brandid: "AlipayQuick",
                brandtype: "2",
                channelid: "906",
                status: 10,
                supdisckeys: null,
                collectionid: "TRD.TP.ALIPAY.APP.PAYSCORE",
                brandname: "芝麻免密支付",
                paytip: "芝麻分550分以上有机会可享",
                authfailmsg: "芝麻免密支付授权未成功",
                supportnormalpay: false,
                hasothersupportpayway: false,
            },
            {
                paymentwayid: "WechatScanCode",
                brandid: "WechatQuick",
                brandtype: "2",
                channelid: "849",
                status: 16,
                supdisckeys: null,
                collectionid: "TRD.TP.WECHAT.MINI.PAYSCORE",
                brandname: "微信先享后付",
                paytip: "微信支付分550分以上有机会可享",
                authfailmsg: "微信先享后付授权未成功",
                supportnormalpay: false,
                hasothersupportpayway: false,
            },
        ],
        dsettings: [
            {
                type: 1,
                value: "{0} {1}(尾号{2})",
            },
            {
                type: 2,
                value: "授权消费后,自动从您的银行卡中扣除款项",
            },
            {
                type: 3,
                value: "消费后,将从{0}{1}(尾号{2})中扣除款项",
            },
            {
                type: 4,
                value: "",
            },
            {
                type: 5,
                value: "",
            },
            {
                type: 6,
                value: "{0} {1}(尾号{2})",
            },
            {
                type: 7,
                value: "授权消费后,自动从您的银行卡中扣除款项",
            },
            {
                type: 8,
                value: "授权消费后,自动从您的银行卡中扣除款项",
            },
            {
                type: 9,
                value: "在您订单中,使用礼品卡抵扣的金额部分,不提供报销凭证。",
            },
            {
                type: 21,
                value: "{&quot;SenderFilterRule&quot;:&quot;^[+]?(86)?10698[0-9]+&quot;,&quot;ContentFilterRule&quot;:&quot;^.*(证码\\\\s)([0-9]{6}).*&quot;}",
            },
            {
                type: 22,
                value: "{&quot;SenderFilterRule&quot;:&quot;^[+]?(86)?((106(55|57|59|90|98))|(95533)|(95516))([0-9]+)?&quot;,&quot;ContentFilterRule&quot;:&quot;^.*(验证码|程网】|证码\\\\s)([0-9]{6}).*&quot;}",
            },
            {
                type: 23,
                value: "^1(3|4|5|7|8)\\d{9}$",
            },
            {
                type: 25,
                value: "微信支付扣款授权",
            },
            {
                type: 26,
                value: "授权消费后，自动从您的微信账户中扣除款项",
            },
            {
                type: 27,
                value: "携程钱包+微信支付授权扣款",
            },
            {
                type: 28,
                value: "授权消费后，自动从携程钱包中扣除款项，不足部分从微信账户中扣除",
            },
            {
                type: 29,
                value: "授权消费后，自动从您的微信账户中扣除款项",
            },
            {
                type: 30,
                value: "授权消费后，自动从携程钱包中扣除款项，不足部分从微信账户中扣除",
            },
            {
                type: 31,
                value: "支付宝扣款授权",
            },
            {
                type: 32,
                value: "授权消费后，自动从您的支付宝账户中扣除款项",
            },
            {
                type: 33,
                value: "携程钱包+支付宝授权扣款",
            },
            {
                type: 34,
                value: "授权消费后，自动从携程钱包中扣除款项，不足部分从支付宝账户中扣除",
            },
            {
                type: 35,
                value: "授权消费后，自动从您的支付宝账户中扣除款项",
            },
            {
                type: 36,
                value: "授权消费后，自动从携程钱包中扣除款项，不足部分从支付宝账户中扣除",
            },
            {
                type: 37,
                value: "使用礼品卡抵扣的金额，不提供报销凭证",
            },
            {
                type: 38,
                value: "扣款：携程钱包＋银行卡",
            },
            {
                type: 39,
                value: "授权消费后，自动从携程钱包中扣除款项，不足部分从{0}{1}(尾号{2})中扣除",
            },
            {
                type: 40,
                value: "消费后，将从携程钱包中扣除款项，不足部分从您绑定的银行卡中扣除。",
            },
            {
                type: 41,
                value: "携程拿去花",
            },
            {
                type: 42,
                value: "扣款：携程钱包 + 拿去花",
            },
            {
                type: 55,
                value: "芝麻免密支付",
            },
            {
                type: 56,
                value: "芝麻分550分以上有机会可享",
            },
            {
                type: 57,
                value: "微信先享后付",
            },
            {
                type: 58,
                value: "微信支付分550分以上有机会可享",
            },
            {
                type: 45,
                value: "",
            },
            {
                type: 46,
                value: "",
            },
        ],
        touchpay: null,
        paytoute: 1,
        icourl: "https://webresource.fws.qa.nt.ctripcorp.com/resh5paymentcommononline/R1/dest/res/bankico",
        paysign: "",
        daver: "98",
        isrn: false,
        merchantid: 200537,
        fncexpayway: [],
        payorderinfo:
            '{"header":{"version":"1.0","scene":"IQP","local":"zh-CN","busType":"3303","requestId":"************","clientType":"Online","clientIp":"12001078310088230130","timeStamp":"","locale":"zh-CN","outUserId":"R899087602","uid":"R899087602"},"paymentType":{"payType":17,"payee":1,"paySubType":1,"autoPay":false,"paySourceType":0,"payTitle":"支付租车押金","paySubTitle":"离店后，自动从您授权的账户中扣款","optType":0},"order":{"orderId":"************","orderAmount":10,"orderCurrency":"CNY","orderTitle":"这只是一个快捷支付的订单描述","subOrderType":"1","payTitle":"只是测试","paySubTitle":"离店后，自动从您授权的账户中扣款","showOrderAmount":true},"merchant":{"busType":"3303","merchantId":"200537","notifyUrl":"http://payment.fat5069.qa.nt.ctripcorp.com/javaonlinepaydemo/PayNotifyForApp.jsp","fromUrl":"http://www.baidu.com","sback":"http://payment.fat5069.qa.nt.ctripcorp.com/javaonlinepaydemo/PayReturn.jsp","eback":"www.ctrip.com","rback":"http://www.ctrip.com","returnUrl":"http://payment.fat5069.qa.nt.ctripcorp.com/javaonlinepaydemo/PayReturn.jsp","isAppPaymentNotify":1,"notifyOptType":0},"payRestrict":{"payWayTypes":0,"blackPayWayTypes":0,"subPayWayTypes":0,"defaultPayType":0,"restrictBit":0},"payExtend":{"travelerList":[],"activityMaxCount":0,"cashReceiverRanch":0,"cashReceiveSite":0,"disableDiscount":false,"integralGuranteeAmount":0,"attach":"test","participateDiscAmount":0,"buSource":0,"selectedPayCategory":0,"bizParam":"{\\"riskInfo\\":{\\"riskMark\\":\\"T\\"},\\"contactInfo\\":{\\"contactName\\":\\"\\",\\"contactEMail\\":\\"\\",\\"contactMobile\\":\\"8615195676928\\"},\\"orderInfo\\":{\\"orderAmount\\":\\"10\\",\\"serviceIntroduction\\":\\"携程酒店后付服务\\",\\"suborderType\\":\\"1\\"},\\"userInfo\\":{\\"userName\\":\\"是的\\",\\"userNationality\\":\\"\\",\\"userPhone\\":\\"15195670000\\",\\"userEmail\\":\\"\\",\\"userId\\":\\"\\",\\"address\\":\\"\\"},\\"fees\\":{\\"feename\\":\\"携程酒店后付服务\\"},\\"attachItems\\":{\\"balanceType\\":\\"PP\\",\\"busScene\\":\\"ORDER_SUB\\",\\"canCancel\\":\\"F\\",\\"checkInTime\\":\\"2020-07-10 00:00:00.000\\",\\"checkOutTime\\":\\"2020-07-11 00:00:00.000\\",\\"clientId\\":\\"12001144210040117256\\",\\"hotelID\\":\\"15130990\\",\\"hotelName\\":\\"成都机场空港嘉陵商务酒店\\",\\"isIbu\\":\\"F\\",\\"lastCancelTime\\":\\"2020-07-09 18:01:44.720\\",\\"masterHotelID\\":\\"\\",\\"merchantID\\":\\"200487\\",\\"orderAmount\\":\\"10\\",\\"star\\":\\"0\\",\\"uid\\":\\"\\"}}","avoidServiceCharge":false,"disableRealNameGuid":false,"supportNormalPay":false},"payOrderFlag":0}',
        paytoken: "872532043454783488",
        isunified: true,
    },
    31102001: {
        ResponseStatus: {
            Timestamp: "/Date(1628071133095+0800)/",
            Ack: "Success",
            Errors: [],
            Build: null,
            Version: null,
            Extension: [
                {
                    Id: "CLOGGING_TRACE_ID",
                    Version: null,
                    ContentType: null,
                    Value: "7503344564449423482",
                },
                {
                    Id: "RootMessageId",
                    Version: null,
                    ContentType: null,
                    Value: "*********-0a06b391-452241-6882",
                },
            ],
        },
        rc: 0,
        subcode: "QUICKPAY_SEARCH_WEAPP_0",
        rmsg: "获取支付方式成功",
        dbgmsg: "获取快捷支付方式成功",
        resinfo: {
            ispoint: true,
            needpwd: true,
            rusetype: 0,
            payetype: 4,
        },
        walletdetal: null,
        bankcardinfo: null,
        thirdpay: [
            {
                paymentwayid: "EB_MobileAlipay",
                brandid: "AlipayQuick",
                brandtype: "2",
                channelid: "906",
                status: 10,
                supdisckeys: null,
                collectionid: "TRD.TP.ALIPAY.APP.PAYSCORE",
                brandname: "芝麻免密支付",
                paytip: "芝麻分550分以上有机会可享",
                authfailmsg: "芝麻免密支付授权未成功",
                supportnormalpay: false,
                hasothersupportpayway: false,
            },
            {
                paymentwayid: "WechatScanCode",
                brandid: "WechatQuick",
                brandtype: "2",
                channelid: "849",
                status: 16,
                supdisckeys: null,
                collectionid: "TRD.TP.WECHAT.MINI.PAYSCORE",
                brandname: "微信先享后付",
                paytip: "微信支付分550分以上有机会可享",
                authfailmsg: "微信先享后付授权未成功",
                supportnormalpay: false,
                hasothersupportpayway: false,
            },
        ],
        dsettings: [
            {
                type: 1,
                value: "{0} {1}(尾号{2})",
            },
            {
                type: 2,
                value: "授权消费后,自动从您的银行卡中扣除款项",
            },
            {
                type: 3,
                value: "消费后,将从{0}{1}(尾号{2})中扣除款项",
            },
            {
                type: 4,
                value: "",
            },
            {
                type: 5,
                value: "",
            },
            {
                type: 6,
                value: "{0} {1}(尾号{2})",
            },
            {
                type: 7,
                value: "授权消费后,自动从您的银行卡中扣除款项",
            },
            {
                type: 8,
                value: "授权消费后,自动从您的银行卡中扣除款项",
            },
            {
                type: 9,
                value: "在您订单中,使用礼品卡抵扣的金额部分,不提供报销凭证。",
            },
            {
                type: 21,
                value: "{&quot;SenderFilterRule&quot;:&quot;^[+]?(86)?10698[0-9]+&quot;,&quot;ContentFilterRule&quot;:&quot;^.*(证码\\\\s)([0-9]{6}).*&quot;}",
            },
            {
                type: 22,
                value: "{&quot;SenderFilterRule&quot;:&quot;^[+]?(86)?((106(55|57|59|90|98))|(95533)|(95516))([0-9]+)?&quot;,&quot;ContentFilterRule&quot;:&quot;^.*(验证码|程网】|证码\\\\s)([0-9]{6}).*&quot;}",
            },
            {
                type: 23,
                value: "^1(3|4|5|7|8)\\d{9}$",
            },
            {
                type: 25,
                value: "微信支付扣款授权",
            },
            {
                type: 26,
                value: "授权消费后，自动从您的微信账户中扣除款项",
            },
            {
                type: 27,
                value: "携程钱包+微信支付授权扣款",
            },
            {
                type: 28,
                value: "授权消费后，自动从携程钱包中扣除款项，不足部分从微信账户中扣除",
            },
            {
                type: 29,
                value: "授权消费后，自动从您的微信账户中扣除款项",
            },
            {
                type: 30,
                value: "授权消费后，自动从携程钱包中扣除款项，不足部分从微信账户中扣除",
            },
            {
                type: 31,
                value: "支付宝扣款授权",
            },
            {
                type: 32,
                value: "授权消费后，自动从您的支付宝账户中扣除款项",
            },
            {
                type: 33,
                value: "携程钱包+支付宝授权扣款",
            },
            {
                type: 34,
                value: "授权消费后，自动从携程钱包中扣除款项，不足部分从支付宝账户中扣除",
            },
            {
                type: 35,
                value: "授权消费后，自动从您的支付宝账户中扣除款项",
            },
            {
                type: 36,
                value: "授权消费后，自动从携程钱包中扣除款项，不足部分从支付宝账户中扣除",
            },
            {
                type: 37,
                value: "使用礼品卡抵扣的金额，不提供报销凭证",
            },
            {
                type: 38,
                value: "扣款：携程钱包＋银行卡",
            },
            {
                type: 39,
                value: "授权消费后，自动从携程钱包中扣除款项，不足部分从{0}{1}(尾号{2})中扣除",
            },
            {
                type: 40,
                value: "消费后，将从携程钱包中扣除款项，不足部分从您绑定的银行卡中扣除。",
            },
            {
                type: 41,
                value: "携程拿去花",
            },
            {
                type: 42,
                value: "扣款：携程钱包 + 拿去花",
            },
            {
                type: 55,
                value: "芝麻免密支付",
            },
            {
                type: 56,
                value: "芝麻分550分以上有机会可享",
            },
            {
                type: 57,
                value: "微信先享后付",
            },
            {
                type: 58,
                value: "微信支付分550分以上有机会可享",
            },
            {
                type: 45,
                value: "",
            },
            {
                type: 46,
                value: "",
            },
        ],
        touchpay: null,
        paytoute: 1,
        icourl: "https://webresource.fws.qa.nt.ctripcorp.com/resh5paymentcommononline/R1/dest/res/bankico",
        paysign: "",
        daver: "98",
        isrn: false,
        merchantid: 200537,
        fncexpayway: [],
        payorderinfo:
            '{"header":{"version":"1.0","scene":"IQP","local":"zh-CN","busType":"3303","requestId":"************","clientType":"Online","clientIp":"12001078310088230130","timeStamp":"","locale":"zh-CN","outUserId":"R899087602","uid":"R899087602"},"paymentType":{"payType":17,"payee":1,"paySubType":1,"autoPay":false,"paySourceType":0,"payTitle":"支付租车押金","paySubTitle":"离店后，自动从您授权的账户中扣款","optType":0},"order":{"orderId":"************","orderAmount":10,"orderCurrency":"CNY","orderTitle":"这只是一个快捷支付的订单描述","subOrderType":"1","payTitle":"只是测试","paySubTitle":"离店后，自动从您授权的账户中扣款","showOrderAmount":true},"merchant":{"busType":"3303","merchantId":"200537","notifyUrl":"http://payment.fat5069.qa.nt.ctripcorp.com/javaonlinepaydemo/PayNotifyForApp.jsp","fromUrl":"http://www.baidu.com","sback":"http://payment.fat5069.qa.nt.ctripcorp.com/javaonlinepaydemo/PayReturn.jsp","eback":"www.ctrip.com","rback":"http://www.ctrip.com","returnUrl":"http://payment.fat5069.qa.nt.ctripcorp.com/javaonlinepaydemo/PayReturn.jsp","isAppPaymentNotify":1,"notifyOptType":0},"payRestrict":{"payWayTypes":0,"blackPayWayTypes":0,"subPayWayTypes":0,"defaultPayType":0,"restrictBit":0},"payExtend":{"travelerList":[],"activityMaxCount":0,"cashReceiverRanch":0,"cashReceiveSite":0,"disableDiscount":false,"integralGuranteeAmount":0,"attach":"test","participateDiscAmount":0,"buSource":0,"selectedPayCategory":0,"bizParam":"{\\"riskInfo\\":{\\"riskMark\\":\\"T\\"},\\"contactInfo\\":{\\"contactName\\":\\"\\",\\"contactEMail\\":\\"\\",\\"contactMobile\\":\\"8615195676928\\"},\\"orderInfo\\":{\\"orderAmount\\":\\"10\\",\\"serviceIntroduction\\":\\"携程酒店后付服务\\",\\"suborderType\\":\\"1\\"},\\"userInfo\\":{\\"userName\\":\\"是的\\",\\"userNationality\\":\\"\\",\\"userPhone\\":\\"15195670000\\",\\"userEmail\\":\\"\\",\\"userId\\":\\"\\",\\"address\\":\\"\\"},\\"fees\\":{\\"feename\\":\\"携程酒店后付服务\\"},\\"attachItems\\":{\\"balanceType\\":\\"PP\\",\\"busScene\\":\\"ORDER_SUB\\",\\"canCancel\\":\\"F\\",\\"checkInTime\\":\\"2020-07-10 00:00:00.000\\",\\"checkOutTime\\":\\"2020-07-11 00:00:00.000\\",\\"clientId\\":\\"12001144210040117256\\",\\"hotelID\\":\\"15130990\\",\\"hotelName\\":\\"成都机场空港嘉陵商务酒店\\",\\"isIbu\\":\\"F\\",\\"lastCancelTime\\":\\"2020-07-09 18:01:44.720\\",\\"masterHotelID\\":\\"\\",\\"merchantID\\":\\"200487\\",\\"orderAmount\\":\\"10\\",\\"star\\":\\"0\\",\\"uid\\":\\"\\"}}","avoidServiceCharge":false,"disableRealNameGuid":false,"supportNormalPay":false},"payOrderFlag":0}',
        paytoken: "872532043454783488",
        isunified: true,
    },
    31002301: {
        serviceCode: "31002301",
    },
    31102002: {
        ResponseStatus: {
            Timestamp: "/Date(1626921438795+0800)/",
            Ack: "Success",
            Errors: [],
            Build: null,
            Version: null,
            Extension: [
                {
                    Id: "CLOGGING_TRACE_ID",
                    Version: null,
                    ContentType: null,
                    Value: "7903320464390805104",
                },
                {
                    Id: "RootMessageId",
                    Version: null,
                    ContentType: null,
                    Value: "*********-0a06b392-451922-4238",
                },
            ],
        },
        rc: 18,
        subcode: "cbu_20025",
        rmsg: "您需要开通微信支付分",
        dbgmsg: null,
        oid: ************,
        seqid: "210722023714378tkky",
        bilno: null,
        sphone: null,
        sig: "mch_id=1400347502&nonce_str=f3dc0713d5e14d5bbbc654726b023c0d&package=AAQTnZoAAAABAAAAAAA7AA_9hF-BW1CT3dn4YCAAAABcwQVtru-5k9MmEOZJ_Pv_Nq7Cw56dNKKN5EjZKnt5jY3grQWxkEzOwRYDbgRl44LkN__zJNYwUnMGTd-CgIPwsvfufU5xQqYagMHWHW4pJ5XfvB8f8XnHCOX5VZYawmnaHI9Bl7mmJA6BDIytMj_cRaix4dIPxlf5EZ-ZHAQRtodkBGp2PUFwyzYoYKh0sN33PWREbcsiRqdv&sign=0826A48D89468BC198171594083AA62980C679D0E736889C8AA4EDDBD6A6F77E&sign_type=HMAC-SHA256&timestamp=1626921437",
        riskcode: "0",
    },
    31003802: {
        serviceCode: "31003802",
    },
    31003701: {
        ResponseStatus: {
            Timestamp: "/Date(1628078134779+0800)/",
            Ack: "Success",
            Errors: [],
            Build: null,
            Version: null,
            Extension: [
                {
                    Id: "CLOGGING_TRACE_ID",
                    Version: null,
                    ContentType: null,
                    Value: "1701750593378378255",
                },
                {
                    Id: "RootMessageId",
                    Version: null,
                    ContentType: null,
                    Value: "*********-0a06b391-452243-5531",
                },
            ],
        },
        rc: 24,
        subcode: null,
        dbgmsg: "",
        rmsg: "开启微信支付分服务失败",
        extradata: null,
    },
    31003901: {
        ResponseStatus: {
            Timestamp: "/Date(1628078134779+0800)/",
            Ack: "Success",
            Errors: [],
            Build: null,
            Version: null,
            Extension: [
                {
                    Id: "CLOGGING_TRACE_ID",
                    Version: null,
                    ContentType: null,
                    Value: "1701750593378378255",
                },
                {
                    Id: "RootMessageId",
                    Version: null,
                    ContentType: null,
                    Value: "*********-0a06b391-452243-5531",
                },
            ],
        },
        rc: 1,
        subcode: null,
        dbgmsg: "",
        rmsg: "开启微信支付分服务失败",
        extradata: null,
    },
    31001401: {
        serviceCode: "31001401",
    },
};
