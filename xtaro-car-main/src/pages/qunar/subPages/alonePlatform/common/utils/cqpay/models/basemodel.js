/* eslint-disable */

import request from "@/common/utils/request";
import util from "@/common/utils/util.js";
import mock from "./mock";
import Business from "../common/business.js";

function BaseModel(settings) {
    this.settings = Object.assign(
        {
            url: "",
            method: "POST",
            data: {},
            success: function () {},
            fail: function () {},
            serviceCode: "",
        },
        settings || {},
    );
}

Object.assign(BaseModel.prototype, {
    constructor: BaseModel,
    buildUrl: function () {
        return "/mobile/member/cqpayment/dispatcher.htm";
    },

    getData: function () {
        let that = this;
        let globalPload = {};
        globalPload.payload = {
            ver: 846004,
            cver: "8.38",
            plat: 5,
            h5plat: 3,
        };

        let head = {
            cid: "8888888888888888888888888888888",
            ctok: "",
            cver: "1.0",
            lang: "01",
            sid: "8888",
            syscode: "09",
            auth: null,
            appid: "5125",
        };
        globalPload.requestHead = JSON.stringify({
            serviceCode: that.settings.serviceCode,
            loginType: "QUNAR",
        });

        const payloadData = Object.assign(
            globalPload.payload,
            that.settings.data || {},
        );
        payloadData.head = head;
        globalPload.head = head;
        globalPload.payload = JSON.stringify(payloadData);

        return globalPload;
    },

    excute() {
        const that = this;
        const initUserData = util.getGlobalInfo();
        if (that.settings.isMock) {
            that.settings.success(mock[that.settings.serviceCode]);
            return;
        }
        Business.sendUbt({
            actionType: "each_service_start",
            a: "each_service_start",
            c: 300513,
            d: "服务号：" + that.settings.serviceCode,
        });
        request(
            {
                service: that.buildUrl(),
                method: "POST",
                returnAll: true,
                param: {
                    subEnv: "fat5069",
                },
                data: that.getData(),
                success: function (res = {}) {
                    Business.sendUbt({
                        actionType: "each_service_success",
                        a: "each_service_success",
                        c: 300513,
                        d:
                            "服务号：" +
                            that.settings.serviceCode +
                            ";http status : " +
                            res.statusCode,
                    });
                    const { statusCode } = res;
                    if (statusCode !== 200) {
                        that.settings.fail(res);
                        return;
                    }
                    const { ResponseStatus, payload } = res.data;
                    const { Ack, Errors } = ResponseStatus;
                    let resData = {};
                    if (Ack == "Success" && payload) {
                        Business.sendUbt({
                            actionType: "each_service_success_success",
                            a: "each_service_success_success",
                            c: 300513,
                            d:
                                "服务号：" +
                                that.settings.serviceCode +
                                ";http status : " +
                                res.statusCode,
                        });
                        resData = JSON.parse(payload);
                        console.log(
                            "-------------------start-----------------------",
                        );
                        that.settings.success(resData);
                        console.log(resData);
                        console.log(
                            "-------------------end--------------------------",
                        );
                    } else {
                        let errMsgs = {};
                        if (Array.isArray(Errors)) {
                            errMsgs = Errors[0];
                        } else {
                            if (
                                Object.prototype.toString
                                    .call(Errors)
                                    .toLowerCase() == "[object object]" &&
                                !Errors.length
                            ) {
                                errMsgs = Errors;
                            } else {
                                errMsgs.Message = Errors;
                            }
                        }
                        const messageStr = errMsgs.Message || "";
                        Business.sendUbt({
                            actionType: "each_service_success_fail",
                            a: "each_service_success_fail",
                            c: 300513,
                            d:
                                "服务号：" +
                                that.settings.serviceCode +
                                ";error message : " +
                                messageStr,
                        });
                        that.settings.fail(res);
                    }
                },
                fail: function (err) {
                    Business.sendUbt({
                        actionType: "each_service_fail",
                        a: "each_service_fail",
                        c: 300513,
                        d:
                            "服务号：" +
                            that.settings.serviceCode +
                            ";error message : " +
                            JSON.stringify(res),
                    });
                    that.settings.fail(err);
                },
            },
            initUserData,
        );
        Business.sendUbt({
            actionType: "each_service_end",
            a: "each_service_end",
            c: 300513,
            d: "服务号：" + that.settings.serviceCode,
        });
    },
    sendWatcher: () => {},
});

export default BaseModel;
