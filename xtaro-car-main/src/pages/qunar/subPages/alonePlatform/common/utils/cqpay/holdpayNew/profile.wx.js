let systemCode = "5252";
let clientIDCache; // 初始值为非字符串，后续会通过类型来判断cid的值是否有效，是不是人为塞进去的
let callbackQueue = [];
let wxSystemInfoCache = null;
const clientIDStorageKey = "profile_clientID"; // localStorage 中，clientID 相应的 Key
let retryTimeout = 300; // CID服务重试间隔
let retryCountMax = 20; //CID 服务失败重试次数

function _getClientID() {
    clientIDCache = clientIDCache || wx.getStorageSync(clientIDStorageKey);
    if (clientIDCache && clientIDCache.length) {
        return flushCBQueue();
    }
    requestCID(0);
}

function flushCBQueue(cb) {
    if (cb && typeof cb === "function") {
        callbackQueue.push(cb); // 不管有没有请求过，都先存下来，等请求cid 执行完成后
    }

    if (clientIDCache && clientIDCache.length) {
        if (callbackQueue && callbackQueue.length) {
            let cbItem = null;
            while ((cbItem = callbackQueue.shift())) {
                if (cbItem && typeof cbItem === "function") {
                    cbItem(clientIDCache);
                }
            }
        }
        return;
    }
}

// 这个只管发 createclientid 的请求
function requestCID(retryCount) {
    console.log(
        "请求CID, 重试间隔：",
        retryTimeout,
        "允许服务失败最大重试次数：",
        retryCountMax,
    );
    console.log("请求CID callbackQueue", callbackQueue);
    if (retryCount > retryCountMax) {
        console.error("请求CID 超过最大重试次数：", retryCount);
        clientIDCache = "00000000000000000000";
        flushCBQueue({
            type: "error",
            message: "get clientId failed.",
        });
        return;
    }

    const url = "https://m.ctrip.com/restapi/soa2/10290/createclientid";
    const data = {
        SystemCode: systemCode,
    };

    let success = (res) => {
        if (
            res.statusCode == 200 &&
            res.data &&
            res.data.ClientID &&
            res.data.ClientID.length
        ) {
            clientIDCache = res.data.ClientID;
            wx.setStorage({
                key: clientIDStorageKey,
                data: clientIDCache,
            });
            // clientID 获取成功
            console.log(
                "请求CID 获取服务成功，重试次数：",
                retryCount,
                "当前的 clientID：",
                clientIDCache,
            );
            flushCBQueue();
        } else {
            fail && fail(res);
        }
    };

    let fail = (res) => {
        // clientID 获取失败
        console.log(
            "请求CID 获取服务失败，重试次数：",
            retryCount,
            "当前的 clientID：",
            clientIDCache,
            " statusCode ",
            (res && res.statusCode) || "",
        );
        setTimeout(() => {
            requestCID(retryCount + 1);
        }, retryTimeout);
    };

    wx.request({
        url: url,
        data: data,
        success: success,
        fail: fail,
    });
    console.warn("查看一下当前的 clientID", clientIDCache);
}

function init(options) {
    if (typeof options.systemCode !== "undefined") {
        systemCode = options.systemCode;
    }
    flushCBQueue((res) => {
        if (res.type === "error") {
            options.fail && options.fail(res);
            return;
        }
        getWxSystemInfo({
            success() {
                uploadDeviceProfile(options);
            },
            fail() {
                options.extension = "wx.getSystemInfo接口调用失败";
                uploadDeviceProfile(options, true);
            },
        });
    });
    _getClientID();
}

function uploadDeviceProfile(options, isSystemInfoFail) {
    let obj = {
        appId: null, // 必填！！！String。即 systemCode
        voipToken: null, // String
        clientId: null, // String。用 getClientID({callback}) 取值
        platform: 3, // 小程序平台对应的值为3，hardcoding
        openUUID: null, // String
        iMEI: null, // String
        mAC: null, // String
        iDFA: null, // String
        vendor: null, // String。厂商。可从 wxSystemInfoCache 中取 brand
        deviceType: null, // String。机型信息，比如 "SM-G9008W"。可从 wxSystemInfoCache 中取 model
        deviceName: null, // String
        oS: null, // String。操作系统类型 android, ios。跟 platform 取值方法一样
        oSVersion: null, // String
        androidId: null, // String
        appVersion: null, // String。微信版本号，可从 wxSystemInfoCache 中取 version
        sourceId: null, // String
        fcmToken: null, // String。google token
        fcmStatus: null, // String。google 推送开关
        locale: null, // String。参考文档：00. Trip.com 标准（http://conf.ctripcorp.com/pages/viewpage.action?pageId=69875753）
        deviceId: null, // String
        timeZone: null, // int。时区
        pushSwitch: 0, // 接口文档说必填，但小程序很可能没有，所以做了【兜底】，默认值是 0.int。1-开启，0-关闭 （system level）。
        appPushSwitch: null, // int。app push switch
        marketPushSwitch: null, // int。1-开启，0-关闭
        extension: null, // String。将 wxSystemInfoCache 转成 String
    };

    obj.clientId = clientIDCache;

    // (1) 处理内部能取到值的，并且注意(2.1)不要改掉它们: platform, vendor, deviceType, oS, appVersion, extension
    const sysInfoPropsArr = [
        "platform",
        "vendor",
        "deviceType",
        "oS",
        "appVersion",
        "extension",
    ];
    if (wxSystemInfoCache && wxSystemInfoCache.brand) {
        obj.vendor = wxSystemInfoCache.brand;
        obj.deviceType = wxSystemInfoCache.model;
        obj.oS = wxSystemInfoCache.system;
        obj.appVersion = wxSystemInfoCache.version;
        obj.extension = JSON.stringify(wxSystemInfoCache);
    }
    // 如果 options 的 extension 属性 有值，应该是之前存的错误信息
    if (options.extension) {
        obj.extension = options.extension;
    }

    // (2) 把 options 中的值挪到 obj 中
    // (2.1) 处理必填的、需要用户入参的
    obj.appId = systemCode;
    if (typeof options.token !== "undefined") {
        obj["token"] = options.token;
    }
    if (typeof options.pushSwitch !== "undefined") {
        obj.pushSwitch = options.pushSwitch;
    }

    // (2.2) 处理非必填的，需要用户入参的
    Object.keys(obj).forEach((key) => {
        if (options.hasOwnProperty(key) && !sysInfoPropsArr.includes(key)) {
            obj[key] = options[key];
        }
    });

    let url = "https://m.ctrip.com/restapi/soa2/12538/json/uploadDeviceProfile";
    let reMsg = {
        message: isSystemInfoFail ? "get systemInfo failed." : "",
    };

    wx.request({
        url: url,
        method: "POST",
        header: options.header || {},
        data: obj,
        success(res) {
            if (typeof options.success === "function") {
                options.success(reMsg);
            }
            console.log("uploadDeviceProfile - success, res: ", res);
        },
        fail(err) {
            console.log("uploadDeviceProfile - fail, res: ", res);
            reMsg.message += ";uploadDeviceProfile failed.";
            reMsg.type = "error";
            if (typeof options.fail === "function") {
                options.fail(reMsg);
            }
        },
    });
}

function getWxSystemInfo(options) {
    wx.getSystemInfo({
        success(res) {
            wxSystemInfoCache = res;
            if (options.success && typeof options.success === "function") {
                options.success();
            }
        },
        fail(err) {
            wxSystemInfoCache = null;
            console.error(err);
            if (options.fail && typeof options.fail === "function") {
                options.fail();
            }
        },
    });
}

export default {
    init: init,
    getClientID: function (callback) {
        flushCBQueue(callback);
    },
};
