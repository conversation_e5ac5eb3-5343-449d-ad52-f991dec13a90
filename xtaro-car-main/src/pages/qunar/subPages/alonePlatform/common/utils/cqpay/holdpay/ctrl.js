import { navigateTo } from "@tarojs/taro";
import React from "react";
import request from "@/common/utils/request";
import util from "@/common/utils/util.js";

import Business from "../common/business.js";
import paymodels from "../models/models.js";
import paymentStore from "../models/stores.js";

// let HoldDatas = require("../components/getholddata.js");
import HoldDatas from "../components/getholddata";

let HoldUi = require("../components/holdui.js");

let GetHoldata = HoldDatas.getData;
let orderDetailStore = paymentStore.HoldTokenStore();
let payResultOStore = paymentStore.HoldResultOrderStore();
let wxscoreStore = paymentStore.WxscoreStore();
var orderDetailParamStore = paymentStore.OrderDetailParamStore();

let WxholdWayModel = paymodels.WxholdWayModel;
let WxholdResultModel = paymodels.WxholdResultModel;
let WxholdPayModel = paymodels.WxholdPayModel;

const WxscoreState = paymodels.WxscoreStateModel; //查询微信支付分开通状态服务
const WxscoreData = paymodels.WxscoreDataModel; //获取开通微信支付分所需数据

let cwx = {};
cwx.appId = Business.getAppId();
cwx.cwx_mkt = {};

let holdData;
let payToken = "";

function transStr2Obj(str) {
    var tempArray = str.split("&");
    var retObj = {};
    for (let i = 0; i < tempArray.length; i++) {
        let obj = tempArray[i];
        let innerTempArr = obj.split("=");
        retObj[innerTempArr[0]] = innerTempArr[1];
    }

    return retObj;
}

//获取公共request参数
let getParams = function (cparams, serverData) {
    let orderDetail = orderDetailStore.get() || {};
    let paramJson = {
        rextend: {
            sbitmap: 7,
            extend: "",
        },
    };

    if (serverData) {
        paramJson.requestid = orderDetail.requestid;
        paramJson.paytype = orderDetail.paytype;
        paramJson.payee = orderDetail.payee;
    } else {
        paramJson.reqpayInfo = {
            requestid: orderDetail.requestid,
            paytype: orderDetail.paytype,
            payee: orderDetail.payee,
        };
        paramJson.oinfo = {
            bustype: orderDetail.bustype,
            oid: orderDetail.oid,
            currency: orderDetail.currency,
            oamount: orderDetail.amount,
        };

        // subordertype
        if (orderDetail.suborderType) {
            paramJson.oinfo.subordertype = orderDetail.suborderType;
        }
    }

    //! 添加支付分bizparam字段
    if (orderDetail.bizparam) {
        paramJson.bizparam = orderDetail.bizparam;
    }

    if (cwx.appId) {
        paramJson.rextend.extend = cwx.appId;
        paramJson.extend = cwx.appId;
    }

    if (cparams) {
        paramJson = Object.assign(paramJson, cparams);
    }

    return paramJson;
};

//设置跳转到代扣页函数
let holdNavigate = function (hdata, settings) {
    Business.sendUbt({
        actionType: "holdNavigate_start",
        a: "holdNavigate",
        c: 80010,
        d: "holdNavigate start!",
    });
    // console.log(hdata);
    wxscoreStore.set(hdata);
    navigateTo({
        url: "/pages/qunar/subPages/alonePlatform/cqpay/holdpay/index",
        data: hdata,
        callback: function (json) {
            if (json && json.type) {
                switch (json.type) {
                    case "sback":
                        settings.sbackCallback(json.data);
                        break;
                    case "eback":
                        settings.ebackCallback(json.data);
                        break;
                    case "rback":
                        if (typeof settings.rbackCallback === "function") {
                            return settings.rbackCallback(json.data);
                        }
                        break;
                }
            }
        },
        success: function (data) {
            try {
                Business.sendUbt({
                    actionType: "holdNavigate_success",
                    a: "holdNavigate_success",
                    c: 8002,
                    d: "currentPage.navigateTo success",
                    dd: "callback返回结果" + JSON.stringify(data),
                });
            } catch (e) {}
        },
        fail: function (data) {
            try {
                Business.sendUbt({
                    actionType: "holdNavigate_fail",
                    a: "currentPage.navigateTo",
                    c: 8001,
                    d: "currentPage.navigateTo fail",
                    dd: "callback返回结果" + JSON.stringify(data),
                });
            } catch (e) {}

            wx.showModal({
                title: "提示",
                content: "系统异常，请稍后再试 -8002",
                showCancel: false,
                success: function (res) {},
            });
        },
        complete: function (data) {},
    });
};

let errComplete = function (res = {}, scode, callback) {
    let that = this;
    let rmsg = res.errMsg || "";
    let mscode = scode.substring(4);
    try {
        Business.sendUbt({
            actionType: "errComplete_start",
            a: "request errComplete_start",
            c: 300510,
            d: "服务号：" + scode + "微信响应:" + rmsg,
        });
    } catch (e) {}

    if (rmsg.indexOf("request:fail timeout") > -1) {
        try {
            Business.sendUbt({
                actionType: "errComplete_timeout",
                a: "request timeout",
                c: 300512,
                d: "服务号：" + scode + "微信响应:" + rmsg,
            });
        } catch (e) {}

        callback({
            errCode: 6,
            errMsg: "网络超时，请稍候重试 - 521-1 " + mscode,
        });
    }
    if (rmsg.indexOf("request:fail") > -1) {
        try {
            Business.sendUbt({
                actionType: "errComplete_fail",
                a: "request fail",
                c: 300513,
                d: "服务号：" + scode + "微信响应:" + rmsg,
            });
        } catch (e) {}

        callback({
            errCode: 5,
            errMsg: "网络不给力，请稍候重试 - 522-1 " + mscode,
        });
    }
};

export default {
    /**
     * 获取微信支付分开通状态
     * @function
     * @name getScoreState
     * @param {Function} callBack 执行完成后的回调函数
     * @param {Object} uictx ui交互操作对象
     * @return {undefined} 默认无返回
     * @description 获取微信支付分开通状态
     * <AUTHOR>
     * @inner
     */
    getScoreState: function (callBack, uictx) {
        const that = this;
        callBack = callBack || function () {};
        const vData = that.valiData;
        let mktopenid = Business.getMktOpenid();
        vData.openid = mktopenid;
        //微信小程序 APPID
        vData.extend = Business.getAppId();
        WxscoreState({
            data: vData,
            success: function (res) {
                if (res.rc == 0) {
                    return callBack({ scoreState: 1, msg: "微信支付分已开通" });
                } else if (res.rc == 1) {
                    return callBack({ scoreState: 0, msg: "微信支付分未开通" });
                } else {
                    return callBack({ scoreState: 0, msg: "服务返回异常！" });
                }
            },
            fail: function () {
                // console.log("查询失败，请重试！")
                return callBack({ scoreState: 0, msg: "查询失败，请重试！" });
            },
            complete: function () {
                uictx.hideLoading();
            },
        }).excute();
    },

    /**
     * 开启微信支付分
     * @function
     * @name openWxScore
     * @param {Function} callBack 执行完成后的回调函数
     * @param {Object} uictx ui交互操作对象
     * @return {undefined} 默认无返回
     * @description 开启微信支付分
     * <AUTHOR>
     * @inner
     */
    openWxScore: function (callBack, uictx, scoreDatas) {
        const that = this;
        callBack = callBack || function () {};
        const vData = that.valiData;
        //微信小程序 APPID
        if (cwx.appId) {
            vData.extend = cwx.appId;
        }
        WxscoreData({
            data: vData,
            success: function (res) {
                //debug
                // res.rc = 0;
                if (res.rc == 0) {
                    let extData = res.extradata || "";
                    try {
                        extData = JSON.parse(extData);
                    } catch (e) {
                        Business.sendUbt({
                            a: "parseextradataErr",
                            c: 1010,
                            dd:
                                "JSON格式化extradata字段失败, extradata: " +
                                res.extradata,
                            d: "openWxScore err!",
                        });
                        extData = {};
                    }

                    if (scoreDatas.isSelfCall) {
                        const resultParams = {
                            bustype: vData.bustype,
                        };
                        if (vData.requestid) {
                            resultParams.requestid = vData.requestid;
                        }

                        return holdNavigate(
                            {
                                extData,
                                resultParams,
                                wxScoreChannel: scoreDatas.wxScoreChannel,
                            },
                            scoreDatas.busData,
                        );
                    }

                    that.openWxScoreView(extData, (info) => {
                        if (info) {
                            if (info.errCode === 0) {
                                return callBack({
                                    scoreState: 1,
                                    msg: "开通微信支付分成功!",
                                });
                            } else if (info.errCode === 2) {
                                return callBack({
                                    scoreState: 2,
                                    msg: "开通微信支付分跳转授权页成功!",
                                });
                            } else if (info.errCode === -3) {
                                return callBack({
                                    scoreState: -3,
                                    msg: "取消了开通微信支付分!",
                                });
                            } else {
                                return callBack({
                                    scoreState: 0,
                                    msg: "开通微信支付分失败!",
                                });
                            }
                        }
                    });
                } else {
                    // callBack({scoreState: 0, msg: "获取微信支付分sign服务返回失败!"});
                    Business.goBackNav("eback", 4);
                }
                try {
                    Business.sendUbt({
                        a: "openWxScoreErr",
                        c: 1010,
                        dd: "调用openWxScore服务返回失败, rc: " + res.rc,
                        d: "openWxScore err!",
                    });
                } catch (e) {}
            },
            fail: function () {
                try {
                    Business.sendUbt({
                        a: "openWxScorefail",
                        c: 1010,
                        dd: "调用openWxScore服务返回fail",
                        d: "openWxScore fail!",
                    });
                } catch (e) {}
                Business.goBackNav("eback", 4);
                // return callBack({scoreState: 0, msg: "获取微信支付分sign服务fail"});
            },
            complete: function () {
                uictx.hideLoading();
            },
        }).excute();
    },

    /**
     * 开启微信支付分API
     * @function
     * @name openWxScoreView
     * @param {Json} extraData 开通微信支付分具体参数
     * @param {Function} callBack 执行完成后的回调函数
     * @return {undefined} 默认无返回
     * @description 开启微信支付分
     * <AUTHOR>
     * @inner
     */
    openWxScoreView: function (extraData = {}, callBack) {
        callBack = callBack || function () {};

        wx.navigateToMiniProgram({
            appId: "wxd8f3793ea3b935b8",
            path: "pages/use/enable",
            extraData: extraData,
            success(info) {
                console.log(info);
                callBack({ errCode: 2, errMsg: "开通免密服务跳转授权页成功" });
                try {
                    Business.sendUbt({
                        a: "openWxScoreViewSuccess",
                        c: 1010,
                        dd:
                            "调用navigateToMiniProgram返回success" +
                            JSON.stringify(info || ""),
                        d: "openBusinessView success!",
                    });
                } catch (e) {}
            },
            fail() {
                try {
                    Business.sendUbt({
                        a: "openBusinessViewfail",
                        c: 1010,
                        dd:
                            "调用openBusinessViewAPI返回fail" +
                            JSON.stringify(err || ""),
                        d: "openBusinessView fail!",
                    });
                } catch (e) {}
                return callBack({ errCode: 1, errMsg: "开通免密服务API FAIL" });
            },
            complete(res) {
                console.log(JSON.stringify(res), "++++++++++++++++++++");
            },
        });
    },
    //提交2002服务: 后付支付提交
    submitPayhold: function (callback) {
        let that = this;
        let requestData = getParams(null, true);
        let orderDetail = orderDetailStore.get() || {};
        let resData = payResultOStore.get() || {};
        let resInfo = resData.resinfo;
        let mktopenid = Business.getMktOpenid(); //市场openid
        let holdData = that.holdData || {}; //代扣页面自己调用时通过作用域传值
        let fromHoldPage = holdData.fromHoldPage;
        const busType = orderDetail.bustype;
        const appId = Business.getAppId();

        requestData.payToken = payToken;

        requestData.ispoint = resInfo.ispoint;
        requestData.payex = 1;
        requestData.payetype = resInfo.payetype;

        requestData.submitthirdpay = {
            paymentwayid: resData.paymentwayid,
            brandid: resData.brandid,
            brandtype: resData.brandtype,
            channelid: resData.channelid,
            collectionid: resData.collectionid,
            status: resData.status,
            amount: orderDetail.amount,
            extend: appId,
            origOpenid: mktopenid,
        };
        requestData.paysign = resData.paysign;

        try {
            Business.sendUbt({
                a: "submitPayhold",
                c: 30010,
                d: "submitPayhold start!",
            });
        } catch (e) {}
        WxholdPayModel({
            data: requestData,
            success: function (res = {}) {
                //debug
                // res = {"ResponseStatus":{"Timestamp":"/Date(1626921438795+0800)/","Ack":"Success","Errors":[],"Build":null,"Version":null,"Extension":[{"Id":"CLOGGING_TRACE_ID","Version":null,"ContentType":null,"Value":"7903320464390805104"},{"Id":"RootMessageId","Version":null,"ContentType":null,"Value":"100028317-0a06b392-451922-4238"}]},"rc":18,"subcode":"cbu_20025","rmsg":"您需要开通微信支付分","dbgmsg":null,"oid":814034464040,"seqid":"210722023714378tkky","bilno":null,"sphone":null,"sig":"mch_id=1400347502&nonce_str=f3dc0713d5e14d5bbbc654726b023c0d&package=AAQTnZoAAAABAAAAAAA7AA_9hF-BW1CT3dn4YCAAAABcwQVtru-5k9MmEOZJ_Pv_Nq7Cw56dNKKN5EjZKnt5jY3grQWxkEzOwRYDbgRl44LkN__zJNYwUnMGTd-CgIPwsvfufU5xQqYagMHWHW4pJ5XfvB8f8XnHCOX5VZYawmnaHI9Bl7mmJA6BDIytMj_cRaix4dIPxlf5EZ-ZHAQRtodkBGp2PUFwyzYoYKh0sN33PWREbcsiRqdv&sign=0826A48D89468BC198171594083AA62980C679D0E736889C8AA4EDDBD6A6F77E&sign_type=HMAC-SHA256&timestamp=1626921437","riskcode":"0"};

                let rescode = res.rc;
                let riskcode = res.riskcode;
                try {
                    Business.sendUbt({
                        a: "submitPayholdfrominit",
                        c: 30010,
                        d: "submitPayholdfrominit callback ok rc:" + rescode,
                    });
                } catch (e) {}

                if (fromHoldPage) {
                    try {
                        Business.sendUbt({
                            a: "submitPayholdfromHoldPage",
                            c: 30010,
                            d: "submitPayholdfromHoldPage:" + fromHoldPage,
                        });
                    } catch (e) {}

                    return callback(res);
                } else {
                    try {
                        Business.sendUbt({
                            a: "submitPayholdfromHoldPageelse",
                            c: 30010,
                            d: "submitPayholdfromHoldPageelse",
                        });
                    } catch (e) {}
                    if (rescode == 0 || rescode == 24) {
                        return callback({
                            errCode: 0,
                            result: 1,
                            errMsg: "开通免密服务2002返回成功",
                        });
                    } else if (rescode == 18) {
                        return callback({
                            errCode: 0,
                            result: 18,
                            response: res,
                            errMsg: "需要开通免密服务",
                        });
                    } else {
                        //Train ticket add risk tip
                        if (busType == 4 && rescode == 1 && riskcode == 300) {
                            wx.hideLoading();
                            wx.hideToast();
                            wx.showModal({
                                title: "提示",
                                content: res.rmsg,
                                showCancel: false,
                                success: function (res) {},
                            });
                            return;
                        }

                        return callback({
                            errCode: 1,
                            errMsg: "开通免密服务2002返回失败 - 1",
                        });
                    }
                }
            },
            fail: function (res = {}) {
                if (fromHoldPage) {
                    return that.requestFail(res);
                } else {
                    try {
                        Business.sendUbt({
                            a: "submitPayholdfrominit",
                            c: 30010,
                            d:
                                "submitPayholdfrominit callback fall! res: " +
                                JSON.stringify(res || ""),
                        });
                    } catch (e) {}
                    let resCode = res.retCode;
                    if (resCode == 1) {
                        return callback({
                            errCode: 2,
                            errMsg: "SOA执行失败！",
                        });
                    } else {
                        return callback({
                            errCode: 3,
                            errMsg: "获取代扣服务返回Fail - 2",
                        });
                    }
                }
            },
            complete: function (res) {
                if (fromHoldPage) {
                    that.rquestTimeout(res, "31002002");
                } else {
                    errComplete(res, "31002002", callback);
                }
            },
        }).excute();
    },
    //2301服务接口
    getHoldResult: function (params, callback) {
        let that = this;
        let respJson;
        let bustype;
        if (typeof params == "object") {
            bustype = params.bustype;
        } else {
            try {
                Business.sendUbt({
                    a: "params error",
                    c: 30010,
                    d: "params error! params: " + JSON.stringify(params || ""),
                });
            } catch (e) {}
            return callback({
                errCode: 2,
                errMsg: "params参数必须是json数据！",
            });
        }
        let tempOid = 0; //no oid tag
        holdData = that.holdData; //代扣页面自己调用时通过作用域传值
        if (holdData && holdData.oid) tempOid = holdData.oid; //代扣页面自己调用传递BU oid

        let rData = {
            bustype: bustype,
            bid: params.bid || "WechatQuick",
            oid: tempOid,
            extend: cwx.appId,
        };

        if (payToken) {
            rData.paytoken = payToken;
        }

        if (params.collectionid) {
            rData.collectionid = params.collectionid;
        }
        try {
            Business.sendUbt({
                a: "getholdResult",
                c: 30011,
                d: "getholdResult start! payFrom:" + holdData.fromHoldPage,
            });
        } catch (e) {}
        WxholdResultModel({
            data: rData,
            success: function (res = {}) {
                let rescode = res.rc;
                if (holdData && holdData.fromHoldPage) {
                    return callback(res);
                } else {
                    try {
                        Business.sendUbt({
                            a: "getHoldResultfrombu",
                            c: 30011,
                            d:
                                "getholdResult callback! rescode:" +
                                holdData +
                                "  /status：" +
                                res.status,
                        });
                    } catch (e) {}

                    if (rescode == 0 && res.status == 1) {
                        return callback({
                            errCode: 0,
                            result: 1, //1为代扣授权成功， 0为代扣授权失败
                            errMsg: "该用户已经授权过代扣服务",
                        });
                    } else {
                        return callback({
                            errCode: 2,
                            result: 0, //1为代扣授权成功， 0为代扣授权失败
                            errMsg: "该用户未授权过代扣服务",
                        });
                    }
                }
            },
            fail: function (res = {}) {
                try {
                    Business.sendUbt({
                        a: "getHoldResultfrombu",
                        c: 30011,
                        d:
                            "getholdResult fail! res:" +
                            JSON.stringify(res || ""),
                    });
                } catch (e) {}

                if (holdData && holdData.fromHoldPage) {
                    return that.requestFail(res);
                } else {
                    let resCode = res.retCode;
                    if (resCode == 1) {
                        return callback({
                            errCode: 3,
                            errMsg: "SOA执行失败！",
                        });
                    } else {
                        return callback({
                            errCode: 4,
                            errMsg: "获取代扣服务返回Fail - 2",
                        });
                    }
                }
            },
            complete: function (res) {
                if (holdData && holdData.fromHoldPage) {
                    that.rquestTimeout(res, "31002301");
                } else {
                    errComplete(res, "31002301", callback);
                }
            },
        }).excute();
        try {
            Business.sendUbt({
                a: "getholdResultEnd",
                c: 30011,
                d: "getholdResult End! oid:" + tempOid,
            });
        } catch (e) {}
    },
    //Processing opening payment points
    initWxScore: function (params) {
        var that = this;
        params.isSelfCall = true;
        that.init(params, 0, () => {});
    },

    // 配置支付页title
    // 获取后付的参数并保存到 payResultOStore
    // 获取微信分的开通情况及免密模式（是否需确认）
    // 调用 submitPayhold
    holdWaysCtrl: function (resData, busdata, uicxt) {
        Business.sendUbt({
            actionType: "holdWaysCtrl_Start_2001_respnse",
            a: "getholdWays",
            c: 30011,
            d: "holdWaysCtrl res:" + JSON.stringify(resData.thirdpay || ""),
        });

        const that = this;
        let thirdPayData = resData.thirdpay || [];
        let thirdPayRes = null;
        let resinfo = resData.resinfo || {};
        let wxScoreChannel = false;
        let isWxScoreConfirmChannel = false; // 默认免确认
        let scoreParams = {};
        let status;

        // 找到 WechatQuick（微信后付） 的支付方式，赋值给 thirdPayRes
        thirdPayData.forEach(function (item, i) {
            if (item.brandid == "WechatQuick") {
                thirdPayRes = {
                    paymentwayid: item.paymentwayid,
                    brandid: item.brandid,
                    brandtype: item.brandtype,
                    channelid: item.channelid,
                    collectionid: item.collectionid,
                    status: item.status,
                    paysign: resData.paysign || "",
                    merchantid: resData.merchantid,
                    brandname: item.brandname || "",
                    paytip: item.paytip || "",
                };
            }
        });

        Business.sendUbt({
            actionType: "holdWaysCtrl_Start_2001_respnse_in",
            a: "getholdWays",
            c: 30011,
            d: "holdWaysCtrl res:" + JSON.stringify(thirdPayData || ""),
        });

        //更新 title文案   文案使用顺序 BU传->CBU下发->默认文案
        let orderDetail = orderDetailStore.get() || {};
        let displayTitle = "购票成功后自动扣款";
        let navData = {};
        let payresultInfo = payResultOStore.get() || {};
        //返回BU回调函数
        let backParam = {
            orderID: orderDetail.oid || "",
            busType: orderDetail.bustype || "",
            price: orderDetail.amount || "",
        };

        if (payresultInfo.realoid) {
            backParam.orderID = payresultInfo.realoid;
        }

        // PayDisplaySettings， 找到type 43的对象 {type, value}
        // 43=支付页Head处提示文案
        let ds = resData.dsettings || [];
        let serverTitleObj = {};
        for (let j = 0, len = ds.length; j < len; j++) {
            if (ds[j] && ds[j].type == 43) {
                serverTitleObj = ds[j];
                break;
            }
        }

        // let serverTitleObj = _.find(, function(object){
        //     return object.type == 43;
        // }) || {};

        // 优先使用 dsettings下发的title
        if (serverTitleObj && serverTitleObj.value) {
            displayTitle = serverTitleObj.value;
        } else if (orderDetail && orderDetail.paytitle) {
            displayTitle = orderDetail.paytitle;
        }

        navData.payTitle = displayTitle;
        navData.frontData = resData.frontData;

        if (thirdPayRes) {
            thirdPayRes.resinfo = resinfo;
            payResultOStore.set(thirdPayRes);

            //判断是支付分还是免密支付
            if ((thirdPayRes.status & 2) === 2) {
                //这种情况是微信支付分
                if ((thirdPayRes.status & 16) === 16) {
                    //这种情况是微信支付分需确认模式
                    status = 5;
                } else {
                    if ((thirdPayRes.status & 1) === 1) {
                        //这种情况是微信支付分免确认模式已经签约情况
                        status = 3;
                    } else {
                        //这种情况是微信支付分免确认模式未签约情况
                        status = 4;
                    }
                }
            } else {
                //这种情况是免密支付-已下线
                if ((thirdPayRes.status & 1) === 1) {
                    //这种情况是微信免密已经签约情况
                    status = 1;
                } else {
                    //这种情况是微信免密未签约情况
                    status = 0;
                }
            }

            Business.sendUbt({
                actionType: "holdWaysCtrl_Start_2001_respnse_status",
                a: "getholdWays",
                c: 30011,
                d: "holdWaysCtrl status:" + status,
            });
            //无需确认
            if (status === 3 || status === 4) {
                wxScoreChannel = true;

                scoreParams = {
                    bustype: orderDetail.bustype,
                    scoreType: 2,
                    serverOpened: true,
                };

                if (orderDetail.suborderType) {
                    scoreParams.suborderType = orderDetail.suborderType;
                }
            }

            //需确认 微信分需确认模式，前端需要根据16=微信分需确认模式，直接唤起微信的确认页面 2021-7-8
            if (status === 5) {
                isWxScoreConfirmChannel = true;
                wxScoreChannel = false;

                scoreParams = {
                    bustype: orderDetail.bustype,
                    scoreType: 2,
                    serverOpened: true,
                };

                if (orderDetail.suborderType) {
                    scoreParams.suborderType = orderDetail.suborderType;
                }
            }

            if (status === 0) {
                //没有开通
                //微信支付分路由 status=2为微信支付分
                return holdNavigate(navData, busdata);
            } else {
                uicxt.showLoading("提交中..");
                Business.sendUbt({
                    actionType: "holdWaysCtrl_Start_2002_start",
                    a: "getholdWays",
                    c: 30011,
                    d: "holdWaysCtrl status:" + status,
                });
                that.submitPayhold(function (res) {
                    //debug
                    // res.result = 18;
                    uicxt.hideLoading();
                    Business.sendUbt({
                        actionType: "holdWaysCtrl_Start_2002_response_res",
                        a: "SubmitPayholdback",
                        c: 30011,
                        d:
                            "SubmitPayholdback res: " +
                            JSON.stringify(res || ""),
                    });
                    if (res.result === 1) {
                        //开通授权成功
                        Business.sendUbt({
                            actionType: "holdWaysCtrl_Start_2002_response_r1",
                            a: "SubmitPayholdback sbackcallback",
                            c: 30011,
                            d:
                                "SubmitPayholdbacksbackcallback" +
                                busdata.sbackCallback,
                        });
                        return busdata.sbackCallback(backParam);
                    } else if (res.result === 18) {
                        if (isWxScoreConfirmChannel) {
                            let tempObj = {};
                            tempObj.isWxScoreConfirmChannel =
                                isWxScoreConfirmChannel;
                            tempObj._payToken = payToken;
                            tempObj.extData = JSON.parse(res.response.sig);
                            holdNavigate(
                                Object.assign(tempObj, navData),
                                busdata,
                            );
                            Business.sendUbt({
                                actionType:
                                    "holdWaysCtrl_Start_2002_response_r18",
                                a: "isWxScoreConfirmChannel",
                                c: 30011,
                                d: "navData" + Object.assign(tempObj, navData),
                            });
                            return;
                        }

                        if (wxScoreChannel) {
                            Business.sendUbt({
                                actionType:
                                    "holdWaysCtrl_Start_2002_response_r18",
                                a: "wxScoreChannel",
                                c: 30011,
                                d: "navData" + busdata,
                            });
                            // that.initWxScore({
                            //     data: scoreParams,
                            //     busData: busdata,
                            //     backData: backParam,
                            // 	wxScoreChannel : wxScoreChannel
                            // }, uicxt);
                            let tempObj = {};
                            tempObj.wxScoreChannel = wxScoreChannel;
                            tempObj._payToken = payToken;
                            tempObj.extData = JSON.parse(res.response.sig);
                            holdNavigate(
                                Object.assign(tempObj, navData),
                                busdata,
                            );
                        } else {
                            holdNavigate(navData, busdata);
                        }
                        return;
                    } else {
                        return busdata.ebackCallback(backParam);
                    }
                });
            }
        } else {
            //H5直联逻辑
            if (that.direct) {
                return that.directCallback({
                    payTitle: displayTitle,
                    nopayment: true,
                });
            }
            /////////////////////////

            navData.nopayment = true;
            return holdNavigate(navData, busdata);
        }
    },
    //2001服务接口
    getholdWay: function (uicxt, busdata) {
        let that = this;
        let rData = getParams();
        const orderStore = orderDetailStore.get();
        const serverDatas = HoldDatas.setServerDatas(orderStore);
        rData.payorderinfo = serverDatas;
        try {
            Business.sendUbt({
                a: "getholdWay",
                c: 30009,
                d: "getholdWay fun start!",
            });
        } catch (e) {}
        WxholdWayModel({
            data: rData,
            success: function (res = {}) {
                uicxt.hideLoading();
                let rescode = res.rc;
                try {
                    Business.sendUbt({
                        a: "WayModelgetholdWay",
                        c: 20010,
                        dd: "20010 success callback",
                        d: "",
                    });
                } catch (e) {}

                // 0=成功
                // 1=失败
                // 2=账户冻结
                // 3=没有绑卡
                // 4=已绑卡但不能用
                // 5=只包含第三方或拿去花
                if (rescode == 0 || rescode == 3 || rescode == 5) {
                    if (!payToken) {
                        payToken = res.paytoken;
                    }
                    return that.holdWaysCtrl(res, busdata, uicxt);
                } else {
                    //errno:1:服务端错误即原errorInformation， res; 2:解析错误
                    try {
                        Business.exceptionInfoCollect(
                            {
                                bustype: 4,
                                excode: 3003,
                                extype: 1,
                                exdesc:
                                    "2001服务返回RC=1错误, " +
                                    JSON.stringify(res),
                            },
                            "1",
                        );
                        Business.sendUbt({
                            a: "WxholdWayModel",
                            c: 20011,
                            dd: "2001server success callback rc=1",
                            d: JSON.stringify(res),
                        });
                    } catch (e) {}
                    uicxt.modalConfirm(
                        res.rmsg || "系统异常，请稍后再试 -5121",
                        function () {
                            //H5直联逻辑
                            if (that.direct) {
                                wx.navigateBack({
                                    delta: 1,
                                });
                            }
                            ////////////////////////////
                        },
                    );

                    return;
                }
            },
            fail: function (res) {
                try {
                    Business.sendUbt({
                        a: "requestfail",
                        c: 300500,
                        d: JSON.stringify(res || ""),
                    });
                } catch (e) {}
                uicxt.hideLoading();
                if (res && res.retCode && res.retCode != 2) {
                    uicxt.modalConfirm(
                        "系统异常，请稍后再试 -505",
                        function () {
                            //H5直联逻辑
                            if (that.direct) {
                                wx.navigateBack({
                                    delta: 1,
                                });
                            }
                            ////////////////////////////
                        },
                    );
                } else {
                    uicxt.modalConfirm(
                        "系统异常，请稍后再试 -505.2",
                        function () {
                            //H5直联逻辑
                            if (that.direct) {
                                wx.navigateBack({
                                    delta: 1,
                                });
                            }
                            ////////////////////////////
                        },
                    );
                }
            },
            complete: function (res) {
                try {
                    Business.sendUbt({
                        a: "complete",
                        c: 300501,
                        d: JSON.stringify(res || ""),
                    });
                } catch (e) {}
                errComplete(res, "31002001");
            },
        }).excute();
    },
    //代扣接口API初始化
    init: function (busdata, direct = false, callBack = function () {}) {
        const that = this;
        let loadingTxt = "连接中.";

        //scoreType 1:查询  2：开通
        let type = busdata.scoreType; //根据前置接口路由到相应的功能
        let scoreDatas = {}; //设置支付分数据
        const isSelfCall = busdata.isSelfCall; //内部自己调用
        that.direct = direct; //H5直联
        that.directCallback = callBack; //H5直联回调函数或查询接口回调
        if (isSelfCall) {
            scoreDatas = busdata;
            that.holdpayData = busdata.data;
            type = busdata.data.scoreType;
        } else {
            that.holdpayData = busdata; //保存数据到Pay
        }

        try {
            Business.sendUbt({
                a: "holdpayinit",
                c: 1011,
                d: "holdpayinit start!",
            });
        } catch (e) {}

        GetHoldata.call(that, function (tokenJson) {
            const self = this;
            if (direct) {
                loadingTxt = "服务中.";
            }
            try {
                Business.sendUbt({
                    a: "GetHoldWay",
                    c: 1010,
                    dd: "发起2001服务开始",
                    d: "GetHoldWay start!",
                });
            } catch (e) {}
            that.valiData = tokenJson;
            self.showLoading(loadingTxt);
            switch (type) {
                case 1:
                    that.getScoreState(callBack, self);
                    break;
                case 2:
                    that.openWxScore(callBack, self, scoreDatas);
                    break;
                default:
                    payToken = "";
                    that.getholdWay(self, busdata);
                    break;
            }
        });
    },

    // 获取支付方式：
    // 在payload中添加 appId 到 extend;
    // 请求 31104001；
    // 转化payorderinfo为微信分参数
    getholdWays: function (holdDatas) {
        const that = this;
        HoldUi.showLoading("服务发送中.");
        const payLoads = holdDatas.payLoad;
        Business.sendUbt({
            actionType: "getholdWays_Start",
            a: "getholdWays",
            c: 30011,
            d: "",
        });

        // 添加appid
        if (cwx.appId) {
            payLoads.rextend = {
                sbitmap: 7,
                extend: cwx.appId,
            };
        }

        paymodels
            .WxholdWaysModel({
                data: payLoads,
                success: function (res = {}) {
                    Business.sendUbt({
                        actionType: "getholdWays_Start_WxholdWaysModel",
                        a: "WxholdWaysModel",
                        c: 30011,
                        d: "res: " + JSON.stringify(res),
                    });

                    HoldUi.hideLoading();
                    const {
                        rc,
                        rmsg,
                        payorderinfo,
                        resinfo,
                        thirdpay,
                        paysign,
                        merchantid,
                        dsettings,
                        frontdata = "",
                    } = res;
                    if (rc == 0 || rc == 7) {
                        const reBuildDatas = HoldDatas.buildSData(
                            payorderinfo,
                            payLoads.requestId,
                        );
                        const frontData = JSON.parse(frontdata || "{}");
                        if (reBuildDatas) {
                            const holdsData = {
                                fromServer: true,
                                data: reBuildDatas,
                            };
                            that.holdpayData = holdsData;
                            GetHoldata.call(that, function (tokenJson) {
                                const self = this;
                                that.valiData = tokenJson;
                                const resData = {
                                    resinfo: resinfo,
                                    paysign: paysign,
                                    thirdpay: thirdpay,
                                    dsettings: dsettings,
                                    merchantid: merchantid,
                                    frontData,
                                };
                                that.holdWaysCtrl(resData, holdDatas, self);
                            });
                        }
                    } else {
                        HoldUi.modalConfirm(
                            rmsg || "系统异常，请稍后再试 -5104",
                            function () {
                                //H5直联逻辑
                                if (that.direct) {
                                    wx.navigateBack({
                                        delta: 1,
                                    });
                                }
                            },
                        );
                    }
                },
                fail: function (res = {}) {
                    try {
                        Business.sendUbt({
                            actionType:
                                "getholdWays_Start_WxholdWaysModel_fail",
                            a: "getholdWays",
                            c: 30011,
                            d:
                                "getholdWays fail! res:" +
                                JSON.stringify(res || ""),
                        });
                    } catch (e) {}

                    const resCode = res.retCode;
                    HoldUi.hideLoading();
                    if (resCode && resCode != 2) {
                        HoldUi.modalConfirm(
                            "系统异常，请稍后再试 -5105",
                            function () {
                                //H5直联逻辑
                                if (that.direct) {
                                    wx.navigateBack({
                                        delta: 1,
                                    });
                                }
                            },
                        );
                    } else {
                        HoldUi.modalConfirm(
                            "系统异常，请稍后再试 -5105.2",
                            function () {
                                //H5直联逻辑
                                if (that.direct) {
                                    wx.navigateBack({
                                        delta: 1,
                                    });
                                }
                            },
                        );
                    }
                },
            })
            .excute();
    },
    //服务端传值
    init2: function ({ busdata, isDirect }, callBack = () => {}) {
        Business.sendUbt({
            actionType: "init2_Start",
            a: "getholdWays",
            c: 30011,
            d: "getholdWays fail! res:" + JSON.stringify(busdata || ""),
        });
        const that = this;
        const tokenData = busdata.data;
        that.direct = isDirect; //H5直联
        that.directCallback = callBack; //H5直联回调函数或查询接口回调
        payToken = tokenData.payToken;
        const getSRequest = HoldDatas.getServerData(tokenData);

        orderDetailParamStore.set({
            payToken: tokenData.payToken,
            orderID: tokenData.orderId,
            requestId: tokenData.requestId,
        });

        if (getSRequest) {
            const holdDatas = {
                payLoad: getSRequest,
            };

            if (!isDirect) {
                holdDatas.sbackCallback = busdata.sbackCallback;
                holdDatas.ebackCallback = busdata.ebackCallback;
                holdDatas.rbackCallback = busdata.rbackCallback;
                holdDatas.fromCallback = busdata.fromCallback;
            }
            Business.sendUbt({
                actionType: "init2_End",
                a: "getholdWays",
                c: 30011,
                d: "getholdWays fail! res:" + JSON.stringify(busdata || ""),
            });

            that.getholdWays(holdDatas);
        }
    },
};
