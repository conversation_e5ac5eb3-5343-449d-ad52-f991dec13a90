import BaseModel from "./basemodel";

var WxholdWayModel = function (settings) {
    settings = settings || {};
    settings.url = "/restful/soa2/13578/bindPayListSearch";
    settings.serviceCode = "31004001";
    settings.isMock = false;

    return new BaseModel(settings);
};

var WxholdWaysModel = function (settings) {
    settings = settings || {};
    settings.url = "/restful/soa2/13578/UnifiedBindPayListSearch";
    settings.serviceCode = "31104001";
    settings.isMock = false;
    return new BaseModel(settings);
};

var WxholdResultModel = function (settings) {
    settings = settings || {};
    settings.url = "/restful/soa2/13578/queryThirdPayStatus";
    settings.serviceCode = "31002301";
    settings.isMock = false;
    return new BaseModel(settings);
};

var WxholdPayModel = function (settings) {
    settings = settings || {};
    settings.url = "/restful/soa2/13578/UnifiedBindPaySubmit";
    settings.serviceCode = "31104002";
    settings.isMock = false;
    return new BaseModel(settings);
};

var WxscoreStateModel = function (settings) {
    settings = settings || {};
    settings.url = "/restful/soa2/13578/queryPayPointStatus";
    settings.serviceCode = "31003802";
    settings.isMock = false;
    return new BaseModel(settings);
};

//微信支付分结果查询
var WxscoreConfirmResultQueryModel = function (settings) {
    settings = settings || {};
    settings.url = "/restful/soa2/13578/UnifiedQuickPaymentQuery";
    settings.serviceCode = "31003701";
    settings.isMock = false;
    return new BaseModel(settings);
};

var WxscoreDataModel = function (settings) {
    settings = settings || {};
    settings.url = "/restful/soa2/13578/openPayPointService";
    settings.serviceCode = "31003901";
    settings.isMock = false;
    return new BaseModel(settings);
};

var ExceptionInfoCollectModel = function (settings) {
    settings = settings || {};
    settings.url = "/restful/soa2/10289/exceptioninfo/update";
    settings.serviceCode = "31001401";
    settings.isMock = false;
    return new BaseModel(settings);
};

var ProtocolModel = function (settings) {
    settings = settings || {};
    settings.url = "/restful/soa2/13578/openPayPointService";
    settings.serviceCode = "32005601";
    settings.isMock = false;
    return new BaseModel(settings);
};

var UnWxscoreStateModel = function (settings) {
    settings = settings || {};
    settings.url = "/restful/soa2/13578/unifiedQueryThirdPayStatus";
    settings.serviceCode = "31102301";
    return new BaseModel(settings);
};

export default {
    WxholdWayModel: WxholdWayModel,
    WxholdWaysModel: WxholdWaysModel,
    WxholdPayModel: WxholdPayModel,
    WxholdResultModel: WxholdResultModel,
    WxscoreStateModel: WxscoreStateModel,
    WxscoreDataModel: WxscoreDataModel,
    ExceptionInfoCollectModel: ExceptionInfoCollectModel,
    WxscoreConfirmResultQueryModel: WxscoreConfirmResultQueryModel,
    ProtocolModel: ProtocolModel,
    UnWxscoreStateModel: UnWxscoreStateModel,
};
