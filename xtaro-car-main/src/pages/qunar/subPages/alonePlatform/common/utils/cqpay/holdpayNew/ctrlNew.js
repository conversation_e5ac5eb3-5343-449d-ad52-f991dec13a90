import {
    showToast,
    navigateBack,
    redirectTo,
    showModal,
    showLoading,
    hideLoading,
    navigateTo,
} from "@tarojs/taro";
import React from "react";
import { xEnv } from '@ctrip/xtaro';
import paymentStore from "../models/stores.js";
import Util from "../common/util.js";
import CommonUtils from "@/common/utils/util.js";
import {
    getParam,
    getRequestExtend,
    reportErrorLog,
} from "@/common/utils/pay/utils/midstage.js";
import Business from "../common/business";

import request from "@/common/utils/request";
import {
    paywayModel,
    getOrderInfo,
    paywaySubmitModel,
    payResModel,
    queryThirdPayStatus,
} from "@/common/utils/payTinyAppLibs.js";

const ANU_ENV = process.env.ANU_ENV;

const sendWatcher = function (actionType, extend) {
    Business.sendUbtTrace({
        actionType,
        ...extend,
    });
};

const subEnv = xEnv.getDevEnv()?.toLowerCase() === 'fat' ? "fat5069" : "";
const sendUbt = (data) => {
    sendWatcher(data.c || "后付埋点", data);
};
const { appendQuery } = Util;
const isCtrip = false;
const wxR = React.api;

// 微信分的支付方式
const brandMap = {
    wx: "WechatQuick",
    ali: "AlipayAuth",
};
const MatchBrandId = brandMap[ANU_ENV]; //当前环境的支付brandid

const requestContext = {
    request: request,
    util: CommonUtils,
    subEnv,
};

function getOpenid() {
    const { cookies } = CommonUtils.getGlobalInfo();
    const openId = cookies.openId;
    return openId;
}

const { CreateGuid } = CommonUtils;

let shouldNavBack = false;
const holdpayPath = "/pages/alonePlatform/cqpay/holdpayNew/index";

let payOptions = {}; // 原数据
let payData = {}; // 处理后的数据
let payItem = null; // 从102提取到的支付方式
let isWxScoreConfirmChannel = false;
let orderInfo = {};
let res303 = null;

let orderDetailStore = paymentStore.HoldTokenStore(); // 保存整个orderInfo
let payItemDisplayStore = paymentStore.HoldResultOrderStore(); // 从102提取到的支付方式显示信息
let wxscoreStore = paymentStore.WxscoreStore(); // 给微信分页的参数
const PayParamsStore = paymentStore.PayParamsStore;

const emptyFn = () => {};
let initCallback = emptyFn;

// ____________________________________ 以下代码直接复制到C小程序可用，已经抹平差异 ________________________________________________________________

// 返回状态： rc： 1 成功、 2 取消、 3、 提交支付时失败 4、微信支付分开通失败 5、 超时 6、转预付
const rcMap = {
    suc: 1,
    cancel: 2,
    fail: 3,
    failOpen: 4,
    timeout: 5,
    toPrePay: 6,
};
// scoreState  0 失败、 1 成功、2 跳转授权页成功、 -3 取消
// rc映射为scoreState
const scoreMapRc = {
    1: 1,
    2: -3,
    3: 2,
    4: 0,
    5: 5,
    6: 6,
};

const callbackDelay = 1500; // BU回调的延迟
const showToastkDelay = 3000; // 提示信息留存时间

// 返回地址优先级： BU直传>服务下发>回上一页
function sBack({
    rc = rcMap.suc,
    delay = showToastkDelay,
    title = "下单成功",
    isShowToast = true,
} = {}) {
    sendUbt({ a: "sBack", b: "5001", c: "微信分授权成功", title, payOptions });
    try {
        isShowToast && showToast({
            title,
        });
        const payOrderInfo = orderInfo.payOrderInfo || "";
        const merchant = payOrderInfo.merchant || {};
        const callbackParam = {
            rc,
            title,
            scoreState: scoreMapRc[rc] || rc,
        };
        // 跳转授权的，要直接返回，这是老逻辑
        if (payOptions.sbackCallback) {
            console.log("callbackParam", callbackParam);
            if (shouldNavBack) {
                const navBackParam = {
                    type: "sback",
                    data: {
                        orderID: orderInfo.outTradeNo || "",
                        busType: merchant.busType || "",
                        price: Number(orderInfo.orderAmount),
                    },
                };
                sendUbt({
                    a: "sBack",
                    b: "5004",
                    c: "需要nav回退",
                    callbackParam,
                });
                navigateBack(navBackParam);
            }
            //   延迟调用，支持BU立即跳转结果页
            setTimeout(() => {
                payOptions.sbackCallback(callbackParam);
            }, callbackDelay);
            return;
        }
        let jumpUrl = "";
        const sback = payOrderInfo.sback || merchant.sback;
        if (payOptions.sBackUrl) {
            jumpUrl = payOptions.sBackUrl;
        } else if (sback) {
            jumpUrl = sback;
        }
        jumpPage(jumpUrl, rc, delay);
    } catch (error) {
        console.log("sBack err", error);
    }
}

function eBack(options = {}) {
    const thisOption = {
        rc: rcMap.fail,
        delay: showToastkDelay,
        title: "下单失败",
        needCheckSwitchPrePay: false, // 是否需要检查转预付逻辑
        ...options,
    };
    const { rc, delay, title, needCheckSwitchPrePay } = thisOption;
    sendUbt({ a: "eBack", b: "5002", c: "微信分授权失败", title });
    if (needCheckSwitchPrePay) {
        // 303看 head 100001， 且有directPayButtons
        const checkFrom303 =
            res303.head && res303.head.code === 1 && res303.directPayButtons;
        const supportDirectPay = checkFrom303;
        if (supportDirectPay) {
            sendUbt({
                a: "supportDirectPay",
                b: "65264",
                c: "后付失败-支持转预付",
                thisOption,
            });
            const title =
                (res303.head && res303.head.message) ||
                "无法发起授权，试试在线支付吧";
            const directPayButtons = res303.directPayButtons || [];
            doSwitchPrePay({
                ...thisOption,
                title,
                directPayButtons,
            });
            return;
        }
    }
    options?.isShowToast && showToast({
        title,
        icon: "none",
    });
    const payOrderInfo = orderInfo.payOrderInfo || "";
    const merchant = payOrderInfo.merchant || {};
    const callbackParam = {
        rc,
        title,
        scoreState: scoreMapRc[rc] || rc,
        orderID: orderInfo.outTradeNo || "",
        busType: merchant.busType || "",
        price: Number(orderInfo.orderAmount),
    };
    if (payOptions.ebackCallback) {
        // 跳转授权的，要直接返回，这是老逻辑
        if (shouldNavBack) {
            sendUbt({ a: "sBack", b: "5005", c: "需要nav回退", callbackParam });
            navigateBack({
                type: "eback",
                data: callbackParam,
            });
        }
        //   延迟调用，支持BU立即跳转结果页
        setTimeout(() => {
            payOptions.ebackCallback(callbackParam);
        }, callbackDelay);
        return;
    }
    let jumpUrl = "";
    const eback =
        payOrderInfo.eback ||
        payOrderInfo.fromUrl ||
        merchant.eback ||
        merchant.fromUrl;
    if (payOptions.eBackUrl) {
        jumpUrl = payOptions.eBackUrl;
    } else if (eback) {
        jumpUrl = eback;
    }
    jumpPage(jumpUrl, rc, delay);
}

function jumpPage(jumpUrl, rc, delay) {
    sendUbt({
        a: "jumpPage",
        b: "5003",
        c: "微信分授权跳转",
        jumpUrl,
        rc,
        delay,
    });
    setTimeout(() => {
        if (jumpUrl) {
            let resUrl = appendQuery(jumpUrl, "rc=" + rc);
            resUrl = appendQuery(resUrl, "scoreState=" + scoreMapRc[rc]);
            if (Util.isUrl(resUrl)) {
                sendUbt({
                    a: "jumpPage",
                    b: "5004",
                    c: "微信分授权跳转 cwebview",
                    jumpUrl,
                });
                CommonUtils.openWebview({ url: resUrl });
            } else {
                redirectTo({
                    url: resUrl,
                });
            }
        } else {
            navigateBack();
        }
    }, delay);
}

// 转预付方法，弹窗并调用回调或跳转url
function doSwitchPrePay(ebackOptions) {
    const { rc, delay, title, directPayButtons = [] } = ebackOptions;
    // const title = res303.head && res303.head.message || (res102.displayInfo.tips.find(item=>item.key === 'noPayWayTip')|| {}).value || '无法发起授权，试试在线支付吧'
    // const directPayButtons = res303.directPayButtons || res102.displayInfo.directPayButtons || []
    const confirmBtn =
        directPayButtons.find((item) => item.buttonType === "pay") || {};
    const confirmText = confirmBtn.buttonName || "在线支付";
    const cancelText =
        (directPayButtons.find((item) => item.buttonType === "cancel") || {})
            .buttonName || "取消";
    showModal({
        title,
        confirmText,
        cancelText,
        success(res) {
            if (res.confirm) {
                // 直连的跳url，API的调用回调
                sendUbt({
                    a: "doSwitchPrePay",
                    b: "89456",
                    c: "用户点击转预付-确认",
                });
                const paylink = confirmBtn.jumpUrl;
                if (payOptions.ebackCallback) {
                    sendUbt({
                        a: "doSwitchPrePay",
                        b: "7894",
                        c: "确认跳转预付，调用回调-ebackCallback",
                    });
                    const callbackParam = {
                        rc: rcMap.toPrePay, // 转预付code
                        title,
                        scoreState: rcMap.toPrePay,
                    };
                    if (shouldNavBack) {
                        const navBackParam = {
                            type: "switchPrePay",
                            data: {
                                orderID: orderInfo.outTradeNo || "",
                            },
                        };
                        sendUbt({
                            a: "switchPrePay",
                            b: "5004",
                            c: "switchPrePay需要nav回退",
                            callbackParam,
                        });
                        navigateBack(navBackParam);
                    }
                    //   延迟调用，支持BU立即跳转结果页
                    setTimeout(() => {
                        payOptions.ebackCallback(callbackParam);
                    }, callbackDelay);
                    return;
                } else {
                    jumpPage(paylink, rc, delay);
                }
            } else if (res.cancel) {
                sendUbt({
                    a: "doSwitchPrePay",
                    b: "89456",
                    c: "用户点击转预付-取消",
                });
                eBack({ ...ebackOptions, needCheckSwitchPrePay: false });
            }
        },
    });
}

// api调用
const init = (options, callback) => {
    sendUbt({ a: "init", b: "4001", c: "api接入后付开始", options });
    payOptions = { ...options, isApiInit: true };
    payData = {
        h5: false, // api
    };
    doPay(options, callback);
};

/**
 * h5直连后付
 * 步骤：
 * 1.中台请求102， paytoken为后付链接中的参数
 * 2.请求303发起支付
 *     code:70 处理（授权|需确认）
 */
const initH5 = (options, callback) => {
    sendUbt({ a: "initH5", b: "4000", c: "h5直连后付开始", options });
    payOptions = options;
    payData = {
        h5: true,
    }; // h5直连
    doPay(options, callback);
};

const doPay = (options, callback) => {
    initCallback = callback || emptyFn;
    shouldNavBack = false;
    //   vChainToken 在rebind场景才需要，初始清空
    wxscoreStore.setAttr("vChainToken", "");
    const tradeNo = Util.getParam("tradeNo", options.cashierUrl);
    const _payToken = Util.getParam("payToken", options.cashierUrl);
    const payToken =
        options.tradeNo || options.payToken || tradeNo || _payToken;
    PayParamsStore().setAttr("payToken", payToken);
    if (!payToken) {
        eBack({
            title: "系统异常，请稍后再试 -1002",
        });
        reportErrorLog({
            errorType: "31001",
            errorMessage: "没有传tradeNo",
        });
        return;
    }
    payData.payToken = payToken;

    showLoading({
        title: "服务获取中..",
    });
    sendUbt({ a: "paywayModel", b: "4002", c: "paywayModel 请求102 开始" });
    const {
        h5plat
    } = getParam() || '';
    // 请求102
    paywayModel({
        data: {
            payToken: payToken,
        },
        h5plat: h5plat, // ANU_ENV === 'ali' ? 30 : 29,
        context: requestContext,
        requestHead: {
            payScence: "4",
        },
        success: (res) => {
            sendUbt({
                a: "paywayModel_suc",
                b: "4002",
                c: "paywayModel 请求102 success",
            });
            // res102 = res
            hideLoading();
            // 设置协议链接
            wxscoreStore.setAttr("frontData", {
                ...res.displayInfo,
                withholdProtocolUrl:
                    res.displayInfo && res.displayInfo.agreementUrl,
            });
            const thirdList =
                res.payCatalogInfo && res.payCatalogInfo.thirdPartyList;
            if (!thirdList) {
                eBack({
                    title: res.head.message || "系统异常，请稍后再试 -1003",
                    needCheckSwitchPrePay: true,
                });
                reportErrorLog({
                    errorType: "31005",
                    errorMessage: "没有传三方支付方式",
                    extendInfo: res,
                });
                return;
            }
            // 微信分的支付方式
            payItem = thirdList.find((item) => item.brandId === MatchBrandId);
            if (!payItem) {
                sendUbt({
                    a: "paywayModel_suc_no_wx",
                    b: "4003",
                    c: "paywayModel 请求102 没有下发微信分",
                });
                eBack({
                    title: "系统异常，请稍后再试 -1004",
                    needCheckSwitchPrePay: true,
                });
                reportErrorLog({
                    errorType: "31009",
                    errorMessage: "没有下发支付方式-微信分",
                });
                return;
            }
            // 微信分支付方式显示信息
            const thirdPartyDisplayInfoList =
                res.displayInfo.thirdPartyDisplayInfoList;
            let payItemDisplay = {};
            if (thirdPartyDisplayInfoList) {
                payItemDisplay =
                    thirdPartyDisplayInfoList.find(
                        (item) => item.brandId === MatchBrandId,
                    ) || {};
            }
            payItemDisplayStore.set(payItemDisplay);
            orderDetailStore.set(res.orderInfo);
            orderInfo = res.orderInfo || {};
            // 微信分需确认模式
            if (payItem.attachAttributes.includes("12")) {
                isWxScoreConfirmChannel = true;
                wxscoreStore.setAttr("wxScoreChannel", false);
                wxscoreStore.setAttr("isWxScoreConfirmChannel", true);
            } else {
                isWxScoreConfirmChannel = false;
                wxscoreStore.setAttr("wxScoreChannel", true);
                wxscoreStore.setAttr("isWxScoreConfirmChannel", false);
            }
            sendUbt({
                a: "paywayModel_suc_channel",
                b: "4004",
                c: "paywayModel 是否需确认模式",
                isWxScoreConfirmChannel,
            });
            // 请求303
            submitPay.call(this, res);
        },
        fail: () => {
            hideLoading();
            eBack({
                rc: rcMap.fail,
                title: "系统异常，请稍后再试 -1009",
                needCheckSwitchPrePay: true,
            });
        },
        complete: () => {},
    }).excute();
};

// 请求303
const submitPay = async (paywayModelData) => {
    const { amount } = getOrderInfo(paywayModelData) || "";
    const { h5plat, appId, thirdSubTypeID } = getParam() || "";

    let mktopenid = getOpenid(); //市场openid

    const extendJson = {
        openid: payOptions.openId || mktopenid,
        wechatOpenId: mktopenid,
        thirdSubTypeID,
        extend: appId,
    };

    // 风控token，防止风控重复返回rebind
    const vChainToken = wxscoreStore.getAttr("vChainToken") || "";

    const dataParam = {
        payToken: payData.payToken,
        payTypes: ["3"],
        paymentMethodInfo: {
            thirdPayInfos: [
                {
                    payAmount: amount,
                    routerInfo: {
                        paymentWayToken: payItem.paymentWayToken,
                    },
                    extend: JSON.stringify(extendJson),
                },
            ],
        },
        vChainToken,
    };
    showLoading({
        title: "支付提交中..",
    });
    const cid = wxscoreStore.getAttr("cid") || Math.random();
    const nonce303 = CreateGuid();
    wxscoreStore.setAttr("nonce303", nonce303);
    sendUbt({ a: "submitPay", b: "5001", c: "submitPay 开始" });
    const requestHead = {
        extend: await getRequestExtend(payOptions.cashierUrl),
        payScence: "4",
        nonce: nonce303,
        deviceInfo: {
            userAgent: "",
            clientId: cid,
            sourceId: "8888",
            userIp: "",
            rmsToken: "",
        },
    };
    if (isCtrip) {
        delete requestHead.deviceInfo;
    }
    paywaySubmitModel({
        data: dataParam,
        h5plat,
        requestHead,
        context: requestContext,
        success: function (res) {
            sendUbt({ a: "submitPay_suc", b: "5002", c: "submitPay 成功" });
            hideLoading();
            res303 = res;
            wxscoreStore.setAttr("vChainToken", res.vChainToken || "");
            res.head = res.head || {};
            if (res.head.code == 70) {
                // 需确认或未授权
                shouldNavBack = true;
                if (!res.thirdPartyInfo) {
                    eBack({
                        title: "系统异常，请稍后再试 -1007",
                        needCheckSwitchPrePay: true,
                    });
                    return;
                }

                wxscoreStore.setAttr("_payToken", payData.payToken);
                if (payData.h5) {
                    initCallback();
                } else {
                    navigateTo({
                        url: `${holdpayPath}?suc303=1&isConfirm=${isWxScoreConfirmChannel}`,
                    });
                }
            } else if (res.head.code === 100000) {
                sBack();
            } else if (res.head.code === 24) {
                // code: 24 已提交
                sBack({
                    title: res.head.message || "已授权，请勿重复提交",
                });
            }
            // 73是支付宝预授权
            else if (res.head?.code === 73) {
                my.tradePay({
                    // 调用资金冻结接口（alipay.fund.auth.order.app.freeze），获取支付宝预授权参数
                    orderStr: res.thirdPartyInfo.sig,
                    success: (res) => {
                        if (res.resultCode == 9000) {
                            // 成功
                            sBack({ isShowToast: false });
                        } else if (
                            res.resultCode == 8000 ||
                            res.resultCode == 6004
                        ) {
                            // 待查询
                            GetHoldResult();
                        } else if(res.resultCode == 6001) {
                            // 取消
                            eBack({
                              rc: rcMap.cancel,
                              isShowToast: false
                            })
                        } else {
                            GetHoldResult();
                            showToast({
                                title: '授权未成功，请重试',
                                icon: 'none'
                            });
                        }
                    },
                    fail: () => {
                        my.alert({
                            content: "调用授权失败，请重试",
                        });
                    },
                });
            }
            // code: 74, 走到rebind风控，重新提交303，带vchainToken
            else if (res.head.code === 74) {
                submitPay(paywayModelData);
            }
            // code: 81 倒计时结束，授权超时
            else if (res.head.code === 81) {
                eBack({
                    rc: rcMap.timeout,
                    title: res.head.message || "超过授权时限, 请重新下单",
                    needCheckSwitchPrePay: true,
                });
            } else {
                // 失败
                eBack({
                    rc: rcMap.fail,
                    title: res.head.message || "系统异常，请稍后再试 -1006",
                    needCheckSwitchPrePay: true,
                });
            }
        },
        fail: (e) => {
            hideLoading();
            sendUbt({ c: "paywaySubmitModel_fail", a: "提交后付失败" });
            reportErrorLog({
                errorType: "31006",
                errorMessage: "303 request:fail 网络问题",
                extendInfo: e,
            });
            eBack({
                rc: rcMap.fail,
                title: "系统异常，请稍后再试 -1005",
                needCheckSwitchPrePay: true,
            });
        },
    }).excute();
};

// 唤起微信支付分
const toInvokeWxScore = ({ success = () => {} }) => {
    // 需确认，调用确认
    if (isWxScoreConfirmChannel) {
        wx.openBusinessView({
            businessType: "wxpayScoreUse",
            extraData: getExtraData(),
            success: (e) => {
                sendUbt({ c: "openBusinessView_wxpayScoreUse_success" });
                success();
            },
            fail: (e) => {
                sendUbt({ c: "openBusinessView_wxpayScoreUse_fail" });
                eBack({
                    rc: rcMap.failOpen,
                    needCheckSwitchPrePay: true,
                });
                showToast({
                    title: "授权微信分失败",
                    icon: "none",
                });
                reportErrorLog({
                    errorType: "31203",
                    errorMessage: "唤起需确认失败",
                    extendInfo: e,
                });
            },
            complete: () => {
                sendUbt({ c: "openBusinessView_wxpayScoreUse_complete" });
            },
        });
    } else {
        /**
         * 免确认、需授权
         * 去授权
         * 查询2301
         * 查询授权成功继续调用303
         */
        // 授权
        wx.openBusinessView({
            businessType: "wxpayScoreEnable",
            extraData: getExtraData(),
            success: (e) => {
                // 查询 2301
                sendUbt({ c: "openBusinessView_wxpayScoreUse_success" });
                success();
            },
            fail: (e) => {
                showToast({
                    title: "授权微信分失败",
                    icon: "none",
                });
                if (/cancel/.test(e.errMsg)) {
                    reportErrorLog({
                        errorType: "31207",
                        errorMessage: "免确认，用户取消",
                        extendInfo: e,
                    });
                } else if (/not supported/.test(e.errMsg)) {
                    reportErrorLog({
                        errorType: "31208",
                        errorMessage: "免确认，不支持微信支付分",
                        extendInfo: e,
                    });
                } else {
                    reportErrorLog({
                        errorType: "31205",
                        errorMessage: "唤起免确认失败",
                        extendInfo: e,
                    });
                }
            },
        });
    }
};

// 需确认模式下，确认成功返回
// 查询2101
const getScoreConfrimResult = async () => {
    const dataParam = {
        payToken: payData.payToken,
        payNo: res303.payNo,
        payRefNo: wxscoreStore.getAttr("nonce303"),
    };
    sendUbt({
        a: "getScoreConfrimResult",
        b: "5021",
        c: "需确认查询结果-开始",
        dataParam,
    });
    const { h5plat } = getParam() || "";
    const requestExtend = await getRequestExtend(payOptions.cashierUrl);
    payResModel({
        data: dataParam,
        h5plat,
        requestHead: {
            extend: requestExtend,
            payScence: "4",
        },
        context: requestContext,
        success: (res) => {
            hideLoading();
            sendUbt({
                a: "payResModel",
                b: "5022",
                c: "需确认查询结果-结束",
                res,
            });
            const code = res.head.code;
            if (code === 100000) {
                sBack();
            } else if (code === 1 && res.directPayButtons) {
                // code为1， 检查是否有转预付配置
                doSwitchPrePay({
                    rc: rcMap.toPrePay,
                    title: res.head.message || "无法发起授权，试试在线支付吧",
                    directPayButtons: res.directPayButtons,
                });
            } else {
                reportErrorLog({
                    errorType: "31201",
                    errorMessage: "2101 需确认查询结果：未确认",
                });
                showToast({
                    title: res.head.message || "查询失败",
                    icon: "none",
                });
                // eBack({
                //  title: res.head.message || '微信支付分授权未成功'
                // })
            }
        },
        fail() {
            showModal({
                title: "授权查询失败",
                confirmText: "确认",
                success() {
                    // sendUbt
                },
            });
        },
    }).excute();
};

// 免确认模式下，确认成功返回
// 查询2301, 成功后请求303
// 失败则退出
const GetHoldResult = async () => {
    const dataParam = {
        payToken: payData.payToken,
        paymentWayToken: payItem.paymentWayToken,
        routerWayId: payItem.routerWayId,
    };
    sendUbt({
        a: "GetHoldResult",
        b: "5020",
        c: "免确认查询结果-开始",
        dataParam,
    });
    const { h5plat } = getParam() || "";
    const requestExtend = await getRequestExtend(payOptions.cashierUrl);
    queryThirdPayStatus({
        data: dataParam,
        h5plat,
        requestHead: {
            extend: requestExtend,
            payScence: "4",
        },
        context: requestContext,
        success(res) {
            sendUbt({
                a: "queryThirdPayStatus",
                b: "5023",
                c: "免确认查询结果-结束",
                res,
            });
            if (res.head.code == 100000 && res.thirdPayStatus === 1) {
                sendUbt({
                    a: "queryThirdPayStatus",
                    b: "5023",
                    c: "免确认查询结果-授权成功",
                });
                // 请求303
                submitPay();
            } else {
                showToast({
                    title: res.head.message || (ANU_ENV === 'ali' ? '芝麻信用授权未成功': '微信支付分授权未成功'),
                    icon: "none",
                });
                reportErrorLog({
                    errorType: "31008",
                    errorMessage: ANU_ENV === 'ali' ? '芝麻信用授权未成功': '微信支付分授权未成功',
                    extendInfo: res,
                });
            }
        },
        fail(e) {
            const tip = ANU_ENV === 'ali' ? '微信支付分授权未成功芝麻信用授权查询失败' : '微信支付分授权查询失败'
            showToast({
                title: tip,
                icon: "none",
            });
            sendUbt({
                a: "queryThirdPayStatus",
                b: "5025",
                c: tip,
            });
            eBack({
                rc: rcMap.fail,
                needCheckSwitchPrePay: true,
            });
        },
    }).excute();
};

export default {
    init,
    initH5,
    toInvokeWxScore,
    GetHoldResult,
    getScoreConfrimResult,
    eBack,
    sBack,
};

// 获取微信分签名信息 包括需确认和免确认
function getExtraData() {
    let result;
    res303.thirdPartyInfo = res303.thirdPartyInfo || {};
    try {
        // 免确认
        result = JSON.parse(res303.thirdPartyInfo.sig || "{}");
    } catch (error) {
        // 需确认
        result = Util.queryToParam(res303.thirdPartyInfo.sig || "");
    }
    return result;
}
