// 身份证号码校验
var validation = {
    /**
     * [getValidId description]
     * @param  String num 需要校验的字符串
     * @return Number 1 无错误,-1 长度错误,-2 验证错误
     */
    getValidId: function (num) {
        num = num && (num + "").toUpperCase();
        num = num.replace(/\s/g, ""); //统一去除空格

        //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X。
        if (!/(^\d{15}$)|(^\d{17}(\d|X)$)/.test(num)) {
            return -1;
        }

        //校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
        //下面分别分析出生日期和校验位

        var len, re;
        len = num.length;
        if (len == 15) {
            re = new RegExp(/^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/);
            var arrSplit = num.match(re);

            //检查生日日期是否正确
            var dtmBirth = new Date(
                "19" + arrSplit[2] + "/" + arrSplit[3] + "/" + arrSplit[4],
            );
            var bGoodDay =
                dtmBirth.getYear() == Number(arrSplit[2]) &&
                dtmBirth.getMonth() + 1 == Number(arrSplit[3]) &&
                dtmBirth.getDate() == Number(arrSplit[4]);

            if (!bGoodDay) {
                return -2;
            } else {
                return 1;
            }
        }

        if (len == 18) {
            re = new RegExp(/^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})(\d|X)$/);
            arrSplit = num.match(re);

            //检查生日日期是否正确
            dtmBirth = new Date(
                arrSplit[2] + "/" + arrSplit[3] + "/" + arrSplit[4],
            );
            bGoodDay =
                dtmBirth.getFullYear() == Number(arrSplit[2]) &&
                dtmBirth.getMonth() + 1 == Number(arrSplit[3]) &&
                dtmBirth.getDate() == Number(arrSplit[4]);

            if (!bGoodDay) {
                return -2;
            } else {
                //检验18位身份证的校验码是否正确。
                //校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
                var valnum;
                var arrInt = [
                    7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2,
                ];
                var arrCh = [
                    "1",
                    "0",
                    "X",
                    "9",
                    "8",
                    "7",
                    "6",
                    "5",
                    "4",
                    "3",
                    "2",
                ];
                var nTemp = 0,
                    i;
                for (i = 0; i < 17; i++) {
                    nTemp += num.substr(i, 1) * arrInt[i];
                }

                valnum = arrCh[nTemp % 11];

                if (valnum != num.substr(17, 1)) {
                    return -2;
                }

                return 1;
            }
        }

        return -2;
    },
};

export default validation;
