import { View } from "@tarojs/components";
import { getCurrentInstance, redirectTo } from "@tarojs/taro";
import React from "react";
import util from "@/common/utils/util";

class P extends React.Component {
    constructor(props){
        super(props);
        this.currentInstance = getCurrentInstance();
    }
    componentDidMount() {
        let url =
            "/pages/qunar/subPages/coupon/list/index?" +
            util.stringifyURLParam(this.currentInstance.router.params);
        redirectTo({
            url,
        });
    }
    render() {
        return (
            <View style={{ backgroundColor: "#FFFFFF", height: "100%" }}></View>
        );
    }
}
export default P;
