import { getCurrentInstance } from "@tarojs/taro"; // 快应用打开webview没有title
import React from "react";
import WebView from "@platformComponents/WebView/index";
import utils from "@/common/utils/util";
import request from "@/common/utils/request";
import LogQmark from "@/common/utils/logQmark";
import CommonUtils from "@/common/utils/util.js";
class P extends React.Component {
    constructor(props) {
        super(props);
        this.currentInstance = getCurrentInstance();
        this.state = {
            url: this.currentInstance.router.params.url,
            loginSync: this.currentInstance.router.params.loginSync,
        };
        this.shareInfo = {
            shareTitle: "优惠活动",
            shareDesc: "去哪儿旅行，总有你要的低价",
            shareUrl:
                "https://pwapp.qunar.com/mp/quickShare?%2Fpages%2Fplatform%2Findex%3Fbd_origin%3Dfrom-quick-share",
            scene: "",
        };
    }

    componentDidMount() {
        const { url, loginSync } = this.currentInstance.router.params;

        const deUrl = utils.decodeURL(url);
        const search = this.urlParser(deUrl).search;
        const scene = utils.queryToParam(search).scene;
        const shareUrl = `https://pwapp.qunar.com/mp/quickShare?%2Fpages%2Fplatform%2Findex%3Fbd_origin%3Dfrom-quick-share%26uri%3D${encodeURIComponent(deUrl)}`;
        this.setState({
            url,
            loginSync,
        });
        this.shareInfo = {
            ...this.shareInfo,
            url,
            loginSync,
            scene,
            shareUrl,
        };
        this.setShare(scene);
    }

    syncShareData = (data) => {
        this.shareTitle = data.title;
        this.shareImageUrl = data.imageUrl;
        this.sharePath = data.path;
    };

    setShare = (scene) => {
        request({
            service: "/mpx/getQconfig",
            param: {
                name: "qunar_miniprogram_config2.json",
            },
            success: (res) => {
                const { data } = res;
                const info = data[scene];
                this.shareInfo = Object.assign(this.shareInfo, {
                    shareTitle:
                        info && info.title
                            ? info.title
                            : this.shareInfo.shareTitle,
                    shareDesc:
                        info && info.desc
                            ? info.desc
                            : this.shareInfo.shareDesc,
                });
            },
        });
    };
    onShareAppMessage() {
        LogQmark({
            module: "default",
            id: "shareClick",
            page: "activeWebView",
            operType: "click",
            ext: {
                url: this.state.url,
            },
        });
        const baseUrl = utils.decodeURL(this.state.url).split("?")[0];

        // 分享调后端接口,后端落库和统计监测
        if (baseUrl.indexOf("shark/active") > 0) {
            const pathNameArr = baseUrl && baseUrl.split("/");
            const cid =
                pathNameArr &&
                pathNameArr.length &&
                pathNameArr[pathNameArr.length - 1];
            const { cookies = {} } = CommonUtils.getGlobalInfo();
            const openId = cookies.openId;
            request({
                host: "https://m.flight.qunar.com",
                service: "/gw/u/gw/wechatofficial/shareEvent",
                data: {
                    openId,
                    cid,
                },
            });
        }

        if (this.shareTitle || this.shareImageUrl || this.sharePath) {
            return {
                imageUrl: this.shareImageUrl,
                path: this.sharePath,
                title: this.shareTitle,
            };
        }

        if (process.env.ANU_ENV !== "quick") {
            return null;
        }

        return {
            shareType: 0,
            title: this.shareInfo.shareTitle,
            summary: this.shareInfo.shareDesc,
            imagePath: "@platformAssets/image/qlog.png",
            targetUrl: this.shareInfo.shareUrl,
            platforms: ["WEIXIN"],
        };
    }
    urlParser = (url) => {
        let obj = {
            href: url,
            get beforeSearch() {
                return this.href.split("#")[0].split("?")[0];
            },
            get search() {
                let afterSearch = this.href.split("?")[1] || "";
                return afterSearch.split("#")[0];
            },
            set search(value) {
                this.href =
                    this.beforeSearch +
                    "?" +
                    value +
                    (this.hash ? "#" + this.hash : "");
            },
            get hash() {
                return this.href.split("#")[1] || "";
            },
        };

        return obj;
    };
    render() {
        return (
            <WebView
                src={this.state.url}
                loginSync={this.state.loginSync}
                syncShareData={this.syncShareData.bind(this)}
            ></WebView>
        );
    }
}
export default P;
