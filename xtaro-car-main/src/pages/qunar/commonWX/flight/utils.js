import utils from '../utils/util';

// parse上一页传入的参数，入参为 props.query
function parsePageQuery (query) {
    if (query && query.data) {
        return utils.json_parse(decodeURIComponent(query.data)) || {};
    } else {
        return {};
    }
}

// 首页跳转开关
function getEntranceQconfig () {
    return new Promise((resolve, reject) => {
        wx.request({
            url: 'https://m.flight.qunar.com/flight/api/getMiniQconfig',
            method: 'GET',
            success: (data) => {
                resolve(data);
            },
            fail: () => {
                resolve({
                    data: {
                        ret: false
                    }
                });
            }
        });
    }
    );
}

async function getBuId () {
    const storage = await utils.getGlobalInfoAsync();
    const { user } = storage;
    return new Promise(resolve => {
        if (process.env.ANU_ENV !== 'bu') {
            resolve('');
            return;
        }
        if (user.swanid) {
            resolve(user.swanid);
            return;
        }
    });
}

export {
    parsePageQuery,
    getEntranceQconfig,
    getBuId
};
