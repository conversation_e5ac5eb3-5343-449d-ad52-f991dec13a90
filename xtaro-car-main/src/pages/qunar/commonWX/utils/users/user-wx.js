/* eslint-disable2 */
import config from '../config/config.js';
import request from '../request.js';
import util from '../util';
import watcher from '../watcher';
import Log from '../log';
import getPId from '../pid.js';
import LogQmark from '../logQmark.js';

import oldWarcher from '../oldWatcher';
import AppContext from '../../../../xcar/Util/AppContext';

let newCustomerPerception = false;
let loginOpts = {
    //有默认值，根据第一次调用登录login时传的为准login(callback, exParams)
    exParams: {
        // platform: process.env.platform || 'wechat$$$small',
        platform: 'wechat$$$scar2',
        source: 'ucenter',
        bd_source: 'wx'
    },
    openIdReTime: 0
};

const defaultQmarkData = {
    page: 'login',
    module: 'login',
    operType: 'click'
};

const defaultHrader = {
    'content-type': 'application/x-www-form-urlencoded'
};

// 检查绑定 可以拿到头像昵称手机号（加密）  给用户中心 快速登录用
// 首次进入注入绑定信息
async function checkBind(token, extProps = {}) {
    if (!token) {
        const tokenRes = await getQunarToken(false, extProps);
        if (!tokenRes.ret) {
            return { ret: false, data: tokenRes.data, errMsg: tokenRes.errMsg || '获取token失败' };
        }
        token = tokenRes.data.token || '';
    }
    const res = await promiseRequest({
        service: getUrl('checkBind'),
        data: {
            token,
            platform: loginOpts.exParams.platform
        }
    });
    if (!res.ret) {
        LogQmark({
            ...defaultQmarkData,
            id: 'checkBind_fail',
            ext: { loginType: extProps.loginType || 'default', resData: res }
        });
        watcher.watchCount('check-bind-fail-wx');
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'checkBind_success',
            ext: { loginType: extProps.loginType || 'default'  }
        });
        watcher.watchCount('check-bind-success-wx');
    }
    updateUserInfo({
        user: {
            isBind: res?.data?.isBind,
            avatarUrl: res?.data?.imgUrl || '',
            nickName: res?.data?.nickname || '',
            phone: res?.data?.markMobile || ''
        }
    });
    return res;
}
async function sendSMSCode(data) {
    const extProps = {
        loginType: 'phone_sms_code_login'
    };
    const { user } = util.getGlobalInfo();
    const tokenRes = await getQunarToken(false, extProps);
    if (!tokenRes.ret) {
        return { ret: false, data: tokenRes.data, errMsg: tokenRes.errMsg || '获取token失败' };
    }
    const res = await promiseRequest({
        service: getUrl('sendSMSCode'),
        method: 'POST',
        data: {
            ...data,
            action: 'register',
            type: 'implicit',
            origin: loginOpts.exParams.platform,
            openId: user.openId,
            token: tokenRes.data.token
        },
        header: {
            'content-type': 'application/x-www-form-urlencoded'
        }
    });
    if (!res.ret) {
        LogQmark({
            ...defaultQmarkData,
            id: 'logincode_fail',
            ext: { loginType: extProps.loginType || 'default'  }

        });
        watcher.watchCount('send-sms-fail-wx');
        sendWatcher('sendSMS_fail');
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'logincode_success',
            ext: { loginType: extProps.loginType || 'default'  }
        });
        watcher.watchCount('send-sms-success-wx');
        sendWatcher('sendSMS_success');
    }
    return res;
}
// 快速登录 
async function loginByQuick() {
    const extProps = {
        loginType: 'login_quick'
    };
    const authInfos = await getQunarToken(true, extProps);
    if (!authInfos.ret) {
        watcher.watchCount('login-by-quick-fail-wx');
        LogQmark({
            ...defaultQmarkData,
            id: 'login_by_quick_fail_wx',
            ext: { loginType: extProps.loginType || 'default'  }
        });
        // DECRYPT_FAIL 微信后台解密失败
        if (authInfos.errMsg === 'DECRYPT_FAIL') {
            authInfos.errMsg = '登录失败，请稍后重试';
            watcher.watchCount('DECRYPT_FAIL-loginByQuick');
            LogQmark({
                ...defaultQmarkData,
                id: 'decrypt_fail_login_quick',
                ext: { loginType: extProps.loginType || 'default'  }
            });
        }
        return { ret: false, data: authInfos.data, errMsg: authInfos.errMsg || '登录失败请稍后重试' };
    } else {
        watcher.watchCount('login-by-quick-success-wx');
        LogQmark({
            ...defaultQmarkData,
            id: 'login_by_quick_success_wx',
            ext: { loginType: extProps.loginType || 'default'  }
        });
    }
    const { user } = util.getGlobalInfo();
    let od,
        ud;
    if (authInfos.data.openId && authInfos.data.openId.length > 0) {
        od = authInfos.data.openId;
    } else {
        od = user.openId;
    }
    if (authInfos.data.unionId && authInfos.data.unionId.length > 0) {
        ud = authInfos.data.unionId;
    } else {
        ud = user.unionId;
    }
    checkBind('', extProps);

    updateUserInfo({
        user: {
            openId: od,
            token: authInfos.data.token,
            unionId: ud || '',
            logOut: false
        },
        cookies: {
            bd_source: loginOpts.exParams.bd_source,
            openId: od,
            unionId: ud || '',
            ...authInfos.data.cookies
        }
    });
    return { ret: true, data: {} };
}
// 授权手机号登录
async function loginByWxPhone({ iv, encryptedData, source, ...params }) {
    const extProps = {
        loginType: 'login_by_wx_phone_old'
    };
    const tokenRes = await getQunarToken(false, extProps);
    if (!tokenRes.ret) {
        return { ret: false, data: tokenRes.data, errMsg: tokenRes.errMsg || '获取token失败' };
    }
    const sourceFrom = wx.getStorageSync('sourceFrom');
    let res = await promiseRequest({
        service: getUrl('wechatPhoneLogin'),
        data: {
            token: tokenRes.data.token,
            encryptedData,
            iv,
            platform: loginOpts.exParams.platform,
            origin: loginOpts.exParams.platform,
            usersource: loginOpts.exParams.source,
            source: source || sourceFrom || loginOpts.exParams.source,
            business: params.business || '',
            pid: params.pid || getPId() || '',
            originChannel: params.originChannel || '',
            activityCode: params.activityCode || '',
            ref: params.ref || ''
        }
    });
    LogQmark({
        id: 'loginByWxPhone',
        ...defaultQmarkData,
        ext: { ...params, loginType: extProps.loginType || 'default'  }

    });
    if (!res.ret) {
        LogQmark({
            ...defaultQmarkData,
            id: 'wechatPhoneLogin_fail',
            ext: { loginType: extProps.loginType || 'default', resData: res }
        });
        watcher.watchCount('login-by-phone-auth-fail-wx');
        sendWatcher('wxponelogin_fail');
        if (res.errMsg === 'DECRYPT_FAIL') {
            res.errMsg = '登录失败，若再出现请关闭后再试';
            watcher.watchCount('DECRYPT_FAIL-loginByWxPhone');
        }
        return res;
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'wechatPhoneLogin_success',
            ext: {loginType: extProps.loginType || 'default' }
        });
        watcher.watchCount('login-by-phone-auth-success-wx');
    }
    const qvts = {
        _q: res.data.qcookie,
        _v: res.data.vcookie,
        _t: res.data.tcookie,
        _s: res.data.scookie
    };
    // 同步登录态
    updateUserInfo({
        cookies: qvts,
        user: {
            encryptedData,
            logOut: false
        }
    });
    sendWatcher('wxponelogin_success');
    // 切换绑定
    const bindRes = await unbindByToken(tokenRes.data.token, extProps);
    // 头像昵称 还是通过checkbind来拿
    // DECRYPT_FAIL 微信后台解密失败
    if (res.errMsg === 'DECRYPT_FAIL') {
        bindRes.errMsg = '登录失败，若再出现请关闭后再试';
        watcher.watchCount('DECRYPT_FAIL-loginByWxPhone');
        LogQmark({
            ...defaultQmarkData,
            id: 'decrypt_fail_login_wx_Phone',
            ext: { loginType: extProps.loginType || 'default'  }
        });
    }
    checkBind('', extProps);
    // 如果isReg为true，则是新注册的用户
    return {
        ret: bindRes.ret,
        isReg: res.data.isReg,
        errMsg: bindRes.errMsg || '登录失败，攻城狮正在紧急修复，请稍后重试'
    };
}
// 通过手机号验证码登录 登录完了再调绑定
async function loginByPhone(data) {
    const extProps = {
        loginType: 'phone_sms_code_login'
    };
    const authInfos = await getQunarToken(false, extProps);
    const res = await promiseRequest({
        service: getUrl('loginByPhone'),
        data: {
            ...data,
            action: 'register',
            type: 'implicit',
            origin: loginOpts.exParams.platform,
            qunarToken: authInfos.data.token
        }
    });
    LogQmark({
        id: 'loginByPhone',
        ...defaultQmarkData,
        ext: { ...data, loginType: extProps.loginType || 'default'  }

    });
    if (!res.ret) {
        LogQmark({
            ...defaultQmarkData,
            id: 'loginByPhone_fail',
            ext: { loginType: extProps.loginType || 'default', resData: res }
        });
        watcher.watchCount('login-by-phone-fail-wx');
        sendWatcher('login_fail');
        return res;
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'loginByPhone_success',
            ext: { loginType: extProps.loginType || 'default'  }
        });
        watcher.watchCount('login-by-phone-success-wx');
    }
    const qvts = {
        _q: res.data.qcookie,
        _v: res.data.vcookie,
        _t: res.data.tcookie,
        _s: res.data.scookie
    };
    // 同步登录态
    updateUserInfo({
        cookies: qvts,
        user: {
            logOut: false
        }
    });
    // 切换绑定
    const bindRes = await unbindByToken(authInfos.data.token, extProps);
    // 头像昵称 还是通过checkbind来拿
    checkBind('', extProps);
    // DECRYPT_FAIL 微信后台解密失败
    if (bindRes.errMsg === 'DECRYPT_FAIL') {
        bindRes.errMsg = '登录失败，请稍后重试';
        watcher.watchCount('DECRYPT_FAIL-loginByPhone');
        LogQmark({
            ...defaultQmarkData,
            id: 'decrypt_fail_login_Phone',
            ext: {loginType: extProps.loginType || 'default'  }
        });
    }
    // 如果isReg为true，则是新注册的用户
    return {
        ret: bindRes.ret,
        isReg: res.data.isReg,
        errMsg: bindRes.errMsg || '登录失败，攻城狮正在紧急修复，请稍后重试'
    };
}
// 解除绑定 ———— 绑定其他qunar ———— 没绑定过就返回，绑定过执行抢占绑定
async function unbindByToken(token, extProps = {}) {
    const res = await promiseRequest({
        service: getUrl('unbindByToken'),
        data: {
            platform: loginOpts.exParams.platform,
            token: token
        }
    });
    if (!res.ret) {
        LogQmark({
            ...defaultQmarkData,
            id: 'unbindByToken_fail',
            ext: { loginType: extProps.loginType || 'default' }
        });
        sendWatcher('unbind_fail');
        watcher.watchCount('unbind-fail-wx');
        return { ret: false, errMsg: res.errMsg || '解除绑定失败，请稍后再试' };
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'unbindByToken_success',
            ext: {loginType: extProps.loginType || 'default'  }
        });
        watcher.watchCount('unbind-success-wx');
    }
    sendWatcher('unbind_success');
    return await bindThirdUser(token, extProps);
}
// 重新建立绑定关系
async function bindThirdUser(token, extProps = {}) {
    const { cookies, user = {} } = util.getGlobalInfo();
    const res = await promiseRequest({
        service: getUrl('bindThirdUser'),
        data: {
            ...cookies,
            rob_type: true,
            platform: loginOpts.exParams.platform,
            token: token, 
            encryptedData: user.encryptedData || ''
        }
    });
    // 如果账号已绑定 则抢占绑定 12016
    if (res && res.errCode === 12016) {
        LogQmark({
            ...defaultQmarkData,
            id: 'bindThirdUser_need_rob_wx',
            ext: {loginType: extProps.loginType || 'default' }

        });
        watcher.watchCount('bind-need-rob-wx');
        sendBindType();
        return await robAndBind(res.data.robToken, extProps);
    } else if (res.ret) {
        LogQmark({
            ...defaultQmarkData,
            id: 'bindThirdUser_success',
            ext: {loginType: extProps.loginType || 'default'  }
        });
        watcher.watchCount('bind-success-wx');
        sendBindType();
        return res;
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'bindThirdUser_fail',
            ext: {loginType: extProps.loginType || 'default'  }
        });
        watcher.watchCount('bind-fail-wx');
        sendWatcher('bindThird_fail');
        return { ret: false, errMsg: res.errMsg || '绑定失败，请稍后再试' };
    }
}
// 记录微信授权方式 手机号授权、或者手机号验证码
function sendBindType() {
    const { loginType } = getApp().globalData;
    if (!loginType) return;
    Log({
        type: 'wxBindType',
        loginType
    });
    getApp().globalData.loginType = null;
}
async function robAndBind(token, extProps={}) {
    const res = await promiseRequest({
        service: getUrl('robAndBind'),
        data: {
            platform: loginOpts.exParams.platform,
            robToken: token
        }
    });
    if (res.ret) {
        LogQmark({
            ...defaultQmarkData,
            id: 'robAndBind_success',
            ext: { loginType: extProps.loginType || 'default' }
        });
        watcher.watchCount('robAndBind-success-wx');
        sendWatcher('robAndBind_success');
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'robAndBind_fail',
            ext: { loginType: extProps.loginType || 'default' }
        });
        watcher.watchCount('robAndBind-fail-wx');
        sendWatcher('robAndBind_fail');
    }
    return res;
}
// 退出登录 并且删掉qvts
async function logOut() {
    const qvts = util.getGlobalInfo().cookies;
    const res = await promiseRequest({
        service: getUrl('logOut'),
        data: {
            ...qvts,
            platform: loginOpts.exParams.platform
        }
    });
    if (!res.ret) {
        LogQmark({
            ...defaultQmarkData,
            id: 'logout_fail'
        });
        watcher.watchCount('logout-fail-wx');
        return { ret: false, data: res.data };
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'logout_success'
        });
        watcher.watchCount('logout-success-wx');
    }
    // 同步登录态
    updateUserInfo({
        cookies: {
            _q: '',
            _v: '',
            _t: '',
            _s: ''
        },
        user: {
            logOut: true
        }
    });
    return { ret: true, data: {} };
}
// 检测登录态、token是否有效
async function checkLogin() {
    const tokenRes = await getQunarToken();
    const { cookies, user } = util.getGlobalInfo();
    if (!tokenRes.ret) {
        return { ret: false, data: tokenRes.data, errMsg: tokenRes.errMsg || '获取token失败' };
    }
    const res = await promiseRequest({
        service: getUrl('validate'),
        data: {
            platform: loginOpts.exParams.platform,
            token: tokenRes.data.token,
            autoLogin: false,
            ...cookies
        }
    });
    if (!res.ret) {
        LogQmark({
            ...defaultQmarkData,
            id: 'validate_fail',
            ext: { res, cookies }
        });
        watcher.watchCount('check-login-fail-wx');
        return res.data || {};
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'validate_success'
        });
        watcher.watchCount('check-login-success-wx');
    }
    // 如果登录态过期  需要清空
    if (!res.data.cookies) {
        updateUserInfo({
            cookies: {
                _q: '',
                _v: '',
                _t: '',
                _s: ''
            }
        });
    }
    return {
        ret: true,
        data: {
            isLogin: res.data.cookies && res.data.cookies._q ? true : false,
            imgUrl: user.avatarUrl,
            nickname: user.nickName
        }
    };

}
async function appLanchInfos() {
    // loginOpts.openIdReTime ++;
    const { user } = util.getGlobalInfo();
    // 这个是用wx.login的code  后端换取openId
    const authInfos = await getQunarToken(!user.logOut);
    checkBind(authInfos.data.token);
    if (!authInfos.data.openId) {
        setTimeout(() => {
            // if (loginOpts.openIdReTime > 3) return;
            appLanchInfos();
        }, 3000);
    }

    let od,
        ud;
    if (authInfos.data.openId && authInfos.data.openId.length > 0) {
        od = authInfos.data.openId;
    } else {
        od = user.openId;
    }
    if (authInfos.data.unionId && authInfos.data.unionId.length > 0) {
        ud = authInfos.data.unionId;
    } else {
        ud = user.unionId;
    }
    // homeAbTest();
    updateUserInfo({
        user: {
            openId: od,
            // 好像没人用
            token: authInfos.data.token,
            unionId: ud || ''
        },
        cookies: {
            bd_source: loginOpts.exParams.bd_source,
            openId: od,
            unionId: ud || '',
            ...authInfos.data.cookies
        }
    });
    util.clearTimeStorage();
    if (!newCustomerPerception) {
        // 新客感知接口通知
        newCustomerPerception = true;
        request({
            host: 'https://m.flight.qunar.com',
            service: '/gw/f/flight/miniStart',
            method: 'POST',
            ignoreStatus: true,
            success: (res) => {
                if (res && res.bstatus && res.bstatus.code === 0) {
                    // 预搜规则存入全局变量中
                    if (res.data && res.data.preSearch) {
                        getApp().globalData.preSearchRules = {
                            ...res.data.preSearch,
                            maxCacheTime: res.data.preSearch.maxCacheTime * 1000 // 缓存数据时长转化成毫秒
                        };
                    } else {
                        getApp().globalData.preSearchRules = {};
                    }
                } else {
                    getApp().globalData.preSearchRules = {};
                }
            },
            fail: (err) => {
                getApp().globalData.preSearchRules = {};
            }
        });
    }
}
let tokenArr = null;
// 授权接口 可自动登录
function getQunarToken(login, extProps = {}) {
    if (tokenArr) {
        // console.log('触发tokensingle');
        return tokenArr;
    } else {
        const res = getQunarTokenSingle(login, extProps);
        tokenArr = res;
        return res;
    }
}
async function getQunarTokenSingle(login, extProps = {}) {
    const { user, cookies = {} } = util.getGlobalInfo();
    let authCode = '';

    try {
        authCode = await getAuth(extProps);
    } catch (err) {
        LogQmark({ 
            ...defaultQmarkData,
            id: 'wxLogin_fail',
            ext: {
                loginType: extProps.loginType || 'default',
                errorMsg: JSON.stringify(err)
            }
        });
        return { ret: false, data: {} };
    }
    // 传参q,v,t,s如果登录态未过期不会重新生成q,t,v,s
    const getTokenParams = {
        _q: cookies._q || '',
        _v: cookies._v || '',
        _t: cookies._t || '',
        _s: cookies._s || '',
        code: authCode,
        platform: loginOpts.exParams.platform,
        autoLogin: login || false,
        encryptedData: user.encryptedData || ''
    };
    const res = await promiseRequest({
        service: getUrl('getQunarToken'),
        data: getTokenParams
    });
    tokenArr = null;
    if (!res.ret) {
        LogQmark({
            ...defaultQmarkData,
            id: 'smallLogin_fail',
            ext: { loginType: extProps.loginType || 'default', param: getTokenParams, resData: res  }
        });
        watcher.watchCount(`get-qunar-token-${login || !user.logOut}-wx`);
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'smallLogin_success',
            ext: { loginType: extProps.loginType || 'default', param: getTokenParams, resData: res  }
        });
        watcher.watchCount('get-qunar-token-success-wx');
    }
    if (!(res.data && res.data.openId)) {
        LogQmark({
            ...defaultQmarkData,
            id: 'smallLogin_no_openId',
            ext: { loginType: extProps.loginType || 'default', param: getTokenParams, resData: res  }
        });
        watcher.watchCount(`get-qunar-token-openId-${login || !user.logOut}-wx`);
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'smallLogin_has_openId',
            ext: { loginType: extProps.loginType || 'default', param: getTokenParams, resData: res  }
        });
        watcher.watchCount('get-qunar-token-openId-success-wx');
    }
    try {
        updateUserInfo({
            user: {
                token: res.data.token,
                unionId: res.data.unionId || user.unionId || '',
                openId: res.data.openId || user.openId || '',
                authCode
            },
            cookies: {
                ...res.data.cookies,
                unionId: res.data.unionId || user.unionId || '',
                openId: res.data.openId || user.openId || ''
            }
        });
    } catch (e) {
        if (!res.data) {
            return { ret: false, data: {} };
        }
    }
    return res;
}
async function bindQunarUserByToken(params) {
    const token = await getQunarToken(false);
    const getTokenParams = {
        platform: loginOpts.exParams.platform,
        qunarToken: token.data.token,
        ...params
    };
    const res = await promiseRequest({
        service: getUrl('bindQunarUserByToken'),
        data: getTokenParams,
        header: {
            'content-type': 'application/json'
        }
    });
    if (res.status === 0) {
        LogQmark({
            ...defaultQmarkData,
            id: 'bind_qunar_user_by_token_success'
        });
        watcher.watchCount('bind_qunar_user_by_token_success');
    } else {
        LogQmark({
            ...defaultQmarkData,
            id: 'bind_qunar_user_by_token_fail'
        });
        watcher.watchCount('bind_qunar_user_by_token_fail');
    }
    return res;
}

async function updateThirdInfo(params, successCb, failCb) {
    const token = await getQunarToken(false);
    const getTokenParams = {
        platform: loginOpts.exParams.platform,
        qunarToken: token.data.token,
        ...params
    };
    const res = await promiseRequest({
        service: getUrl('updateThirdInfo'),
        data: getTokenParams,
        header: {
            'content-type': 'application/json'
        }
    });
    if (res.status === 0) {
        successCb && successCb();
        watcher.watchCount('update_third_info_success');
    } else {
        failCb && failCb();
        watcher.watchCount('update_third_info_fail');
    }

}

// 授权弹框 拿加密信息 在这里塞用户信息
function getPlatUserInfo() {
    return new Promise(resolve => {
        wx.getUserInfo({
            success: res => {
                watcher.watchCount('get-user-info-success-wx');
                // 这里拿的是微信的信息  qunar账号信息由checkbind获得
                updateUserInfo({
                    user: {
                        wxInfo: res.userInfo,
                        encryptedData: res.encryptedData
                    }
                });
                resolve({ ret: true, data: res });
            },
            fail: err => {
                watcher.watchCount('get-user-info-fail-wx');
                // 没有授权时，使用openId
                resolve({
                    ret: false, data: Object.assign(err, {
                        encryptedData: '',
                        iv: ''
                    })
                });
            }
        });
    });
}

function getUrl(apiName) {
    return config.service[apiName];
}

// 封装promise请求
function promiseRequest(data) {
    const header = data.header || defaultHrader;

    return new Promise(resolve => {
        request({
            ...data,
            header,
            method: data.method || 'POST',
            success: res => {
                resolve(res);
            },
            fail: err => {
                resolve(err);
            }
        });
    });
}

function getAuth(extProps = {}) {
    return new Promise((resolve, reject) => {
        wx.login({
            success: res => {
                LogQmark({
                    ...defaultQmarkData,
                    id: 'wx_login_success',
                    ext: { loginType: extProps.loginType || 'default'  }
                });
                resolve(res.code);
            },
            fail: err => {
                LogQmark({
                    ...defaultQmarkData,
                    id: 'wx_login_fail',
                    ext: { 
                        loginType: extProps.loginType || 'default',
                        errorMsg: JSON.stringify(err)
                    }
                });
                reject(err);
            }
        });
    });
}

// 类似于原有微信getUserInfo方法
async function getUserInfo() {
    const storage = util.getGlobalInfo();
    return {
        ret: true,
        data: {
            exParams: loginOpts.exParams,
            qunar: Object.assign(storage.cookies, {
                token: storage.user.token
            }),
            wechat: Object.assign(storage.user.wxInfo || {}, {
                openId: storage.user.openId,
                unionId: storage.user.unionId,
                // 以前的微信是这么判断登陆的
                isQunarUser: !!storage.cookies._q
            })
        }
    };
}
//同步登录态数据
function updateUserInfo(data) {
    if (!data) {
        return;
    }
    const { cookies, user } = util.getGlobalInfo();
    // 如果带了登录态，替换
    let newCookies = data.cookies || {};
    let newUser = data.user || {};
    for (let k in newCookies) {
        cookies[k] = newCookies[k];
    }
    for (let k in newUser) {
        user[k] = newUser[k];
    }
    AppContext.setCookie({cookies});
    AppContext.setUser(user)
    let UserData = { cookies: cookies, user: user };
    wx.setStorageSync('UserData', UserData);
    wx.setStorageSync('InitUserData', UserData);
}

function sendWatcher(actionType, exParams) {
    var opts = exParams || {};
    opts['action-type'] = actionType || '';
    opts['page'] = 'login';
    opts.from = 'login';
    oldWarcher.click(opts);
}

const getMiniProgramUserInfo = getPlatUserInfo;

// 用户当前的授权状态
function getSetting() {
    return new Promise(resolve => {
        wx.getSetting({
            success: (res) => {
                if (util.isEmptyObject(res.authSetting)) {
                    resolve(true);
                }
                resolve(false);
            },
            fail: () => {
                resolve(false);
            }
        });
    });
}

let abArr = null;
// abtest实验
async function homeAbTest() {
    if (abArr) {
        // console.log('触发absingle');
        return abArr;
    } else {
        const res = homeAbTestMain();
        abArr = res;
        return res;
    }
}
// onLaunch、小程序首页进入时都会调用
// 存到缓存内
async function homeAbTestMain() {
    const homeAb = wx.getStorageSync('homeAb');
    if (homeAb && homeAb !== 'abErr') {
        abArr = null;
        return homeAb;
    }
    let openId;
    const { user } = util.getGlobalInfo();
    if (user.openId) {
        openId = user.openId;
    } else {
        const toekn = await getQunarToken();
        openId = toekn.data.openId;
    }
    const res = await promiseRequest({
        host: 'https://abtest.qunar.com',
        service: '/api/prod/getAlter',
        method: 'GET',
        param: {
            expIds: '190814_sc_other_wxt',
            code: openId
        },
    });
    // console.log(res, '返回数据');
    let abRes;
    if (res.data && res.data[0] && res.data[0].mod > -2) {
        abRes = res.data[0].version;
        watcher.watchCount('abtest-request-fail-success');
    } else {
        watcher.watchCount('abtest-request-fail-wx');
        abRes = 'abErr';
    }
    wx.setStorageSync('homeAb', abRes);
    abArr = null;
    return abRes;
}
async function loginByWxPhoneNew(props) {
    const extProps = {
        loginType: 'login_by_wx_phone_new'
    };
    const { user } = util.getGlobalInfo();
    let token = user.token, authKey;
    try {
        if (!user.token) {
            authKey = await getAuth(extProps);
        }
    } catch(err) {
        LogQmark({
            ...defaultQmarkData,
            id: 'loginByWxPhoneNew_fail',
            ext: {
                errorMsg: JSON.stringify(err)
            }
        });

        return {
            ret: false,
            isReg: ''
        };
    }
    return await phoneLoginWithBinding({ ...props, token, authKey, extProps });
}
async function phoneLoginWithBinding(props) {
    const { token, iv, encryptedData, code, source, authKey, extProps={} } = props;
    LogQmark({
        id: 'loginByPhoneNew',
        ...defaultQmarkData,
        ext: { loginType: extProps.loginType || 'default' },

    });
    let res = await promiseRequest({
        service: getUrl('phoneLoginWithBinding'),
        header: {
            'content-type': 'application/json'
        },
        data: {
            platform: loginOpts.exParams.platform,
            qunarToken: token,
            encryptedData,
            iv,
            mobileAuthKey: code,
            source: source || 'ucenter',
            authKey
        }
    });
    //三方用户不存在,可能是登录态被刷新session_key不一致，导致数据解密失败
    if (res.status !== 0) {
        LogQmark({
            ...defaultQmarkData,
            id: 'phoneLoginWithBinding_fail',
            ext: { loginType: extProps.loginType || 'default', resData: res, code }
        });
        if (res.status === 12015) {
            updateUserInfo({
                user: {
                    token: ''
                }
            });
            LogQmark({
                ...defaultQmarkData,
                id: 'phoneLoginWithBinding12015_fail',
                ext: { loginType: extProps.loginType || 'default', resData: res }
            });
            //重新获取登录态
            return await loginByWxPhoneNew(props);
        }
    } else if (res.status === 0) {
        const qvts = res.data.cookies;
        await checkBind('', extProps);// 同步登录态
        updateUserInfo({
            cookies: qvts,
            user: {
                encryptedData,
                logOut: false
            }
        });
        LogQmark({
            ...defaultQmarkData,
            id: 'phoneLoginWithBinding_success',
            ext: { loginType: extProps.loginType || 'default'  }
        });
    }
    return {
        ret: res.status === 0,
        isReg: '',
        errMsg: res.message
    };

}
export default {
    loginOpts,
    checkBind,
    sendSMSCode,
    loginByQuick,
    loginByPhone,
    logOut,
    getAuth,
    getMiniProgramUserInfo,
    checkLogin,
    appLanchInfos,
    loginByWxPhone,
    getUserInfo,
    updateUserInfo,
    getQunarToken,
    getSetting,
    homeAbTest,
    bindQunarUserByToken,
    updateThirdInfo,
    loginByWxPhoneNew
};
