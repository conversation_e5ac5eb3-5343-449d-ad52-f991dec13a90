/*
 * @Author: tianbao.wu <EMAIL>
 * @Date: 2023-03-09 19:32:43
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-03-11 18:34:59
 * @FilePath: /nnc_module_qunar_platform/source/common/utils/logQmark.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
  * Qmark埋点
 */
// import QMark from '../../npm/@qnpm/qmark/dist/qmark.mini.umd.js';
import request from './request';

function LogQmark(params, sendImmediate = false, editconfig) {
    const { bizType, page, module: pageModule, appcode, id, operType, ext, time } = params;
    let logParams = {
        bizType: bizType || 'market',
        page,
        module: pageModule,
        appcode: appcode || 'nnc_module_qunar_platform',
        id,
        operType: operType || 'show',
        ext
    };

    if (operType === 'monitor' && time) {
        logParams.time = time;
    }
    try {
        // QMark.log(logParams, sendImmediate, editconfig);
    } catch (e) {
        console.log(e);
    }
}

export function logQmarkTTI() {
    // if (!QMark.setTimeTTI) return null;
    // return QMark.setTimeTTI(Date.now());
    return null;
}

export function LogQmarkHoc (params) {
    // return QMark.hoc(params);
    return null;
}

export function apiQmark (page, appcode) {
    return function (params) {
        const { success, fail, service, resCheckField, bizType } = params;
        const apiStr = service.replace(/\./g, '').split('/').join('_').split('?')[0];
        const start = Date.now();
        params.success = function (res) {
            LogQmark({
                page,
                appcode,
                bizType,
                module: 'request',
                operType: 'monitor',
                time: Date.now() - start,
                id: `request_${process.env.ANU_ENV}${apiStr}`
            });
            const isExists = res.hasOwnProperty(resCheckField);
            // 接口错误上报
            switch (resCheckField) {
                case 'status':
                    // 校验ret格式
                    if (!isExists || res.status !== 0) {
                        LogQmark({
                            page,
                            appcode,
                            bizType,
                            module: 'request',
                            id: `request_${process.env.ANU_ENV}${apiStr}_fail`,
                        });
                    }
                    break;
                case 'ret':
                    // 校验ret格式
                    if (!isExists || !(typeof res.ret == 'boolean' && res.ret)) {
                        LogQmark({
                            page,
                            appcode,
                            bizType,
                            module: 'request',
                            id: `request_${process.env.ANU_ENV}${apiStr}_fail`,
                        });
                    }
                    break;
                case 'data':
                    // 校验data格式
                    if (!isExists) {
                        LogQmark({
                            page,
                            appcode,
                            bizType,
                            module: 'request',
                            id: `request_${process.env.ANU_ENV}${apiStr}_fail`,
                        });
                    }
                    break;
            }
            success && success(...arguments);
        };
        params.fail = function () {
            LogQmark({
                page,
                appcode,
                bizType,
                module: 'request',
                operType: 'monitor',
                time: Date.now() - start,
                id: `request_${process.env.ANU_ENV}${apiStr}`
            });
            LogQmark({
                page,
                appcode,
                bizType,
                module: 'request',
                id: `request_${process.env.ANU_ENV}${apiStr}_fail`
            });
            fail && fail(...arguments);
        };
        delete params.resCheckField;
        request(params);
    };
}

export default LogQmark;