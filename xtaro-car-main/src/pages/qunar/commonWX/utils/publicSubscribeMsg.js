import QMark from '../../npm/@qnpm/qmark/dist/qmark.mini.umd.js';
import subscribe from './subscribe';

function publicSubscribeMsg() {
    return new Promise(async resolve => {
        let listRes = wx.getStorageSync('publicSubscribeMsg');
        if (!listRes) {
            resolve({
                status: 1,
                msg: '获取订阅列表失败'
            });
            return;
        }

        if (listRes.lastTime) {
            const timeDiff = new Date().getTime() - listRes.lastTime;
            if (timeDiff <= 30 * 24 * 60 * 60 * 1000) {
                resolve({
                    status: 1,
                    msg: '30天内已经订阅过，上一次订约时间为' + listRes.lastTime
                });
                return;
            }
        }

        subscribe.requestSubscribeMessage(listRes, () => {
            // 设置缓存
            wx.setStorageSync('publicSubscribeMsg', {
                ...listRes,
                lastTime: new Date().getTime(),
            });
            markLog('0');
            resolve({
                status: 0,
                msg: '订阅消息成功'
            });
        }, () => {
            markLog('1');
            resolve({
                status: 1,
                msg: '订阅消息失败'
            });
        });
    });
}

/**
 * 埋点
 * http://mark.corp.qunar.com/markpage/rdc/markpoint/732/0
 * @param {*} status
 */
function markLog(status) {
    QMark && QMark.log && QMark.log({
        'ext': {
            status
        },
        'bizType': 'rdc',
        'module': 'default',
        'appcode': 'nnc_app_qunar_platform',
        'page': 'subscribeMsg',
        'id': 'subscribeMsg',
        'time': '*',
        'operType': 'monitor',
        'key': 'rdc/subscribeMsg/default/monitor/subscribeMsg',
        'operTime': '*'
    });
}
export {
    publicSubscribeMsg,
    markLog
};
