const captchaCheck = 'https://antifp.qunar.com/captcha/wx/check';

/**
 * 调用方式
 *  - check('', () => {}), 此时 appCode 默认 wx
 *  - check('', 'abc', () => {})
 * @param {*} cst
 * @param {*} appCode
 * @param {*} callback
 */
function check(cst, appCode = 'wx', callback) {
    wx
        .request({
            url: captcha<PERSON><PERSON>ck,
            method: 'POST',
            data: {
                token: cst,
                appCode,
                key: 'lnRqV6Q3UruijY0X'
            }
        })
        .then(r => {
            if (r.data?.data?.valid) {
                callback && callback({ valid: true });
            } else {
                callback && callback({ valid: false });
            }
        });
}

export default { check };
