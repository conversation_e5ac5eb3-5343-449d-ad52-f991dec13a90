
import { xEnv } from '@ctrip/xtaro';

const BETA = xEnv.getDevEnv()?.toLowerCase() === 'fat' ? true : false;

// const debug = process.env.DEBUG === 'true' ? true : false;
const debug = false;
const settings = {
    requestDomain: `https://pwapp${BETA ? '.beta' : ''}.qunar.com`
};
// 如果是debug模式 优先读取缓存
const debugDomain = wx.getStorageSync('debugDomain');
if (debug && debugDomain) {
    const debugDomain = wx.getStorageSync('debugDomain');
    settings.requestDomain = debugDomain.main.domain;
}
const service = {
    // // 登录接口
    // autoLogin: '/oauth-client/oauth/login',
    // // 检测是否登录
    // checkLogin: '',
    // // 检测绑定
    // checkBind: '/oauth-client/oauth/checkBind',
    // // 拿qunarToken
    getQunarToken: '/oauth-client/wechatSmall/smallLogin',
    // getQunarToken: '/oauth-client/oauth/authorization',
    // // 验证码
    sendSMSCode: '/ucenter/webApi/logincode.jsp',
    // // 根据手机号登录
    loginByPhone: '/ucenter/webApi/logincodeverify.jsp',
    // // 快速登录以及绑定支付宝账户
    // loginByQuick: '/oauth-client/oauth/login',
    // // 退出登录
    logOut: '/oauth-client/wechatSmall/logout',
    // 校验登录
    //校验
    validate: '/oauth-client/wechatSmall/validate',
    // 查询绑定 并返回对应头像
    checkBind: '/oauth-client/wechatSmall/checkBind',
    // 联系人
    queryContact: '/webapi/contact/query',
    addContact: '/webapi/contact/save',
    updateContact: '/webapi/contact/update',
    // 解除绑定
    unbindByToken: '/oauth-client/wechatSmall/unbindByToken',
    // 绑定第三方
    bindThirdUser: '/oauth-client/platform/bindThirdUser.jsp',
    //监控地址
    watcherUrl: '/mp/watcher',
    // 微信授权手机号登录
    wechatPhoneLogin: '/oauth-client/wechatSmall/wechatPhoneLogin',
    //获取订单列表
    getOrders: '/api/wechat/order/query.do',
    // 查询未登录订单
    hasOrder: '/api/wechat/order/hasOrder.do',
    deleteOrder:'/api/wireless/recycled/delete.do',
    deleteContact: '/webapi/contact/delete',
    // 同步登录
    syncCookie: '/mpx/syncCookie',
    postPayInfo: '/wechatSmall/pay/postPayInfo.do', //支付成功发送模板消息
    // couponList: '/wechatSmall/coupon/list.do', //优惠券列表
    couponList: '/api/market/coupon/couponList', 
    couponDetail: '/wechatSmall/coupon/detail.do', //优惠券详情,
    couponObtain: '/wechatSmall/coupon/obtain.do', //代金券领取
    getURLByScene: '/wechatSmall/getUrlByScene.do',
    extraIndex: '/wechatSmall/extra/index.do',//首页整合接口，
    robAndBind: '/oauth-client/platform/robAndBind', //抢占
    storageQuery: '/wechatSmall/storage/query.do',
    storageSave: '/wechatSmall/storage/save.do',
    // 通过鉴权token绑定用户关系
    bindQunarUserByToken: '/restapi/wechatSmall/bindQunarUserByToken',
    // 更新第三方用户信息
    updateThirdInfo: '/restapi/wechatSmall/updateThirdInfo',
    phoneLoginWithBinding:'/restapi/oauth/phoneLoginWithBinding'
};


export default {
    settings,
    service
};
