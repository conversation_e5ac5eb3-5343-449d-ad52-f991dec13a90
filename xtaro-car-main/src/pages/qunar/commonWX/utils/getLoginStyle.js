import request from './request';

// 判断登录页面是否使用新样式 
function getLoginStyle() {
    return new Promise((resolve, reject) => {
        let env = process.env.ANU_ENV;
        if (!(process.env.ANU_ENV === 'qq' || process.env.ANU_ENV === 'wx')) {
            resolve({
                newStyle: false,
                rightsUrl: '',
                buttonDecorationUrl: '',
                titleBarColor: '#fff'
            });
        }
        if (process.env.ANU_ENV === 'wx') {
            env = 'wechat';
        }
        request({
            service: '/gw/m/api/applet/login/card',
            host: 'https://m.flight.qunar.com',
            param: {
                source: env,
                // appletSource: process.env.platform || 'wechat$$$small'
                appletSource: 'wechat$$$small'
            },
            success: res => {
                const { cardContent, cardType } = res?.data || {};
                resolve({
                    newStyle: cardType === 'NEW_LOGIN',
                    rightsUrl: cardContent?.rightsUrl || '',
                    buttonDecorationUrl: cardContent?.buttonDecorationUrl || '',
                    titleBarColor: cardType === 'NEW_LOGIN' ? '#ffddcd' : '#fff'
                });
            },
            fail: () => {
                resolve({
                    newStyle: false,
                    rightsUrl: '',
                    buttonDecorationUrl: '',
                    titleBarColor: '#fff'
                });

            }
        });
    });
}
export default getLoginStyle;