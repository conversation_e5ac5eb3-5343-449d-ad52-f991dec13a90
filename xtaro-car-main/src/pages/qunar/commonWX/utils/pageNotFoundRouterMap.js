// 新旧页面的路由映射
export default {
    'hotel/pages/hotel/hotel-index/hotel-index': '/pages/hotel/pages/hotel-index/hotel-index',
    'hotel/pages/hotel/hotel-list/hotel-list': '/pages/hotel/pages/hotel-list/hotel-list',
    'hotel/pages/hotel/hotel-detail/hotel-detail': '/pages/hotel/pages/hotel-detail/hotel-detail',
    'hotel/pages/hotel/hotel-detail/sub-page/comment-list/comment-list': '/pages/hotel/pages/hotel-detail/sub-page/comment-list/comment-list',
    'hotel/pages/hotel/hotel-detail-imagelist/hotel-detail-imagelist': '/pages/hotel/pages/hotel-detail-imagelist/hotel-detail-imagelist',
    'hotel/pages/hotel/hotel-order-info/hotel-order-info': '/pages/hotel/pages/hotel-order-info/hotel-order-info',
    'hotel/pages/hotel/hotel-order-detail/hotel-order-detail': '/pages/hotel/pages/hotel-order-detail/hotel-order-detail',
    'hotel/pages/hotel/hotel-share-detail/hotel-share-detail': '/pages/hotel/pages/hotel-share-detail/hotel-share-detail',
    'hotel/pages/hotel/hotel-comment-detail/hotel-comment-detail': '/pages/hotel/pages/hotel-comment-detail/hotel-comment-detail',
    'hotel/pages/common/keyword/keyword': '/pages/hotel/pages/keyword/keyword',
    'hotel/pages/activity/webview/webview': '/pages/hotel/pages/activity/webview/webview',
    'hotel/pages/activity/wifi/login/index': '/pages/hotel/pages/activity/wifi/login/index',
    'hotel/pages/activity/wifi/adrMiddlePage/index': '/pages/hotel/pages/activity/wifi/adrMiddlePage/index',
    'hotel/pages/activity/wifi/result/index': '/pages/hotel/pages/activity/wifi/result/index',
    'hotel/pages/activity/wifi/adsWebview/index': '/pages/hotel/pages/activity/wifi/adsWebview/index',
    'hotel/pages/hotel/hotel-order-info/sub-page/coupons-select/coupons-select': '/pages/hotel/pages/hotel-order-info/sub-page/coupons-select/coupons-select',
    'hotel/pages/hotel/hotel-order-info/sub-page/invoice-title-suggest/index': '/pages/hotel/pages/hotel-order-info/sub-page/invoice-title-suggest/index',
    'hotel/pages/hotel/hotel-order-info/sub-page/invoice-address/index': '/pages/hotel/pages/hotel-order-info/sub-page/invoice-address/index',
    'hotel/pages/hotel/hotel-order-info/sub-page/invoice-edit-address/index': '/pages/hotel/pages/hotel-order-info/sub-page/invoice-edit-address/index',
    'hotel/pages/hotel/hotel-comment/hotel-comment': '/pages/hotel/pages/hotel-comment/hotel-comment',
    'hotel/pages/hotel/hotel-order-info/sub-page/room-count-select/room-count-select': '/pages/hotel/pages/hotel-order-info/sub-page/room-count-select/room-count-select',
    'hotel/pages/hotel/hotel-detail-licenseimg/hotel-detail-licenseimg': '/pages/hotel/pages/hotel-detail-licenseimg/hotel-detail-licenseimg',
    'pages/hotel/hotel-detail/index': '/pages/hotel/pages/hotel-detail/hotel-detail',
    'ticket/pages/list/list': '/pages/ticket/index/index',
    'ticket/pages/detail/detail': '/pages/ticket/poiDetail/index',
    'ticket/pages/daytrip/daytrip': '/pages/ticket/daytrip/index',
    'ticket/pages/booking/booking': '/pages/ticket/booking/index',
    'ticket/pages/orderdetail/orderdetail': '/pages/ticket/orderDetail/index',
    'ticket/pages/refund/refund': '/pages/ticket/refund/index',
    'ticket/pages/shop/shop': '/pages/ticket/shop/index',
    'ticket/pages/map/map': '/pages/ticket/map/index',
    'old/page/path': '/new/page/path',
    'common/pages/realNameAuth/index': '/pages/alonePlatform/wxPay/realNameAuth/index',
    'vacation/pages/familycard/index/index': '/pages/vacation/familycard/index/index',
    'vacation/pages/familycard/myCards/index': '/pages/vacation/familycard/myCards/index',
    'vacation/pages/familycard/card/index': '/pages/vacation/familycard/card/index',
    'vacation/pages/familycard/bindCard/index': '/pages/vacation/familycard/bindCard/index',
    'vacation/pages/familycard/cardDetail/index': '/pages/vacation/familycard/cardDetail/index',
    'common/pages/search/index': '/pages/train/index/index',
    'train/pages/searchList/searchList': '/pages/train/searchList/index',
    'train/pages/orderDetail/orderDetail': '/pages/vice_train/orderDetail/index',
    'train/pages/fightGroup/fightGroup': '/pages/train/fightGroup/index',
    'train/pages/welfareList/welfareList': '/pages/train/welfareList/index',
    'train/pages/QPShareGB/QPShareGB': '/pages/platform/indexWx/index',
    'train/pages/QPShare/QPShare': '/pages/platform/indexWx/index',
    'train/pages/QPShare/index': '/pages/platform/indexWx/index',
    'train/pages/VipExpCard/VipExpCard': '/pages/platform/indexWx/index',
    'train/pages/QPVIPShare/QPVIPShare': '/pages/platform/indexWx/index',
    'train/pages/assistancerob/assistancerob': '/pages/platform/indexWx/index',
    'train/pages/assistancerobTestB/assistancerobTestB': '/pages/platform/indexWx/index',
    'train/pages/webview/webview': '/pages/train/webview/index',
    'train/pages/webview/webviewLandView': '/pages/train/webviewLandView/index',
    'pages/train/assistancerob/index': '/pages/platform/indexWx/index',
    'pages/train/assistancerobTestB/index': '/pages/platform/indexWx/index',
    'pages/train/jointTicketOrderFill/index': '/pages/functional_train/jointTicketOrderFill/index',
    'pages/train/login12306/index': '/pages/functional_train/login12306/index',
    'pages/train/orderDetail/index': '/pages/vice_train/orderDetail/index',
    'pages/train/searchList/index': '/pages/functional_train/searchList/index',
    'pages/train/verify666/index': '/pages/auxiliary_train/verify666/index',
    'pages/vice_train/HelpGrabTicketLandingPage/index': '/pages/auxiliary_train/HelpGrabTicketLandingPage/index',
    'pages/vice_train/ViceTrainMyRight/index': '/pages/train/TrainMyRight/index',
    'pages/vice_train/selectRiskControl/index': '/pages/train/selectRiskControl/index',
    'pages/vice_train/shareFriendForVerification/index': '/pages/auxiliary_train/shareFriendForVerification/index',
    'pages/vice_train/shareOrder/index': '/pages/auxiliary_train/shareOrder/index',
    'pages/vice_train/searchList/index': '/pages/functional_train/searchList/index',
    'pages/train/QPShareGB/index': '/pages/platform/indexWx/index',
    'pages/auxiliary_train/QPShareGB/index': '/pages/platform/indexWx/index',
    'pages/train/shareAddPassenger/index': '/pages/platform/indexWx/index',
    'pages/auxiliary_train/shareAddPassenger/index': '/pages/platform/indexWx/index',
    'pages/train/welfareList/index': '/pages/platform/indexWx/index',
    'pages/train/contactVerify/index': '/pages/auxiliary_train/contactVerify/index',
    'pages/train/VipExpCard/index': '/pages/platform/indexWx/index',
    'pages/train/QPVIPShare/index': '/pages/platform/indexWx/index',
    'pages/vice_train/ThousandPeopleGrabTicketLeague/index': '/pages/auxiliary_train/ThousandPeopleGrabTicketLeague/index',
    'pages/vice_train/grabingTicketShareLandingPage/index': '/pages/vice_train/grabingTicketHelpPage/index',
    'pages/vice_train/grabingTicketHelpPage/index': '/pages/auxiliary_train/grabingTicketHelpPage/index',
    'pages/vice_train/robRelativeFriendsLandingPage/index': '/pages/platform/indexWx/index',
    'pages/vice_train/passenger/index': '/pages/functional_train/passenger/index',
    'pages/vice_train/register12306/index': '/pages/train/register12306/index',
    'pages/vice_train/refundCheck12306/index': '/pages/functional_train/refundCheck12306/index',


    'bus/pages/orderDetail/orderDetail': '/pages/bus/orderDetail/index',
    'bus/pages/searchlist/searchlist': '/pages/bus/searchlist/index',
    'bus/pages/fillorder/fillorder': '/pages/bus/fillorder/index',
    'bus/pages/discount/index': '/pages/bus/discount/index',
    'bus/pages/insurances/insurances': '/pages/bus/insurances/index',
    'bus/pages/faq/faq': '/pages/bus/faq/index',
    'bus/pages/router/index': '/pages/bus/router/index',
    'bus/pages/share/index': '/pages/bus/share/index',
    'bus/pages/cityList/index': '/pages/bus/web/index',
    'bus/pages/index/index': '/pages/bus/index/index',
    'common/pages/activity/webview/index': '/pages/alonePlatform/actWebWx/index',

    'vacation/pages/list/index': '/pages/vacation/list/index',
    'activity/pages/goddessFestivalToArea/address/index': '/pages/activity/goddessFestivalToArea/address/index',
    'activity/pages/goddessFestivalToArea/home/<USER>': '/pages/activity/goddessFestivalToArea/home/<USER>',

    'activity/pages/marketGift/index': '/pages/activity/marketGift/index',
    'activity/pages/loadingError/loadingError': 'pages/activity/loadingError/index',
    'activity/pages/giveVip/home/<USER>': '/pages/activity/giveVip/home/<USER>',
    'activity/pages/giveVip/getPage/index': '/pages/activity/giveVip/getPage/index',
    'activity/pages/giveVip/friends/index': '/pages/activity/giveVip/friends/index',
    'activity/pages/wxstep/home/<USER>': '/pages/activity/wxstep/home/<USER>',
    'activity/pages/wxstep/buy/index': '/pages/activity/wxstep/buy/index',
    'activity/pages/wxstep/invitelist/index': '/pages/activity/wxstep/invitelist/index',
    'activity/pages/wxstep/moneylist/index': '/pages/activity/wxstep/moneylist/index',
    'activity/pages/wxstep/myproducts/index': '/pages/activity/wxstep/myproducts/index',
    'activity/pages/wxstep/product/index': '/pages/activity/wxstep/product/index',
    'activity/pages/wxstep/setting/index': '/pages/activity/wxstep/setting/index',
    'activity/pages/wxstep/steplist/index': '/pages/activity/wxstep/steplist/index',
    'activity/pages/wxstep/task/index': '/pages/activity/wxstep/task/index',
    'activity/pages/wxstep/rule/index': '/pages/activity/wxstep/rule/index',
    'activity/pages/bargain/list/index': '/pages/activity/bargain/list/index',
    'activity/pages/bargain/detailed/index': '/pages/activity/bargain/detailed/index',
    'activity/pages/bargain/helper/index': '/pages/activity/bargain/helper/index',
    'activity/pages/bargain/ticket/index': '/pages/activity/bargain/award/index',
    'activity/pages/bargain/award/index': '/pages/activity/bargain/ticket/index',
    'vacation/pages/home/<USER>': '/pages/vacation/index/index',
    'bnb/pages/home/<USER>' : '/pages/bnb/home/<USER>',
    'bnb/pages/pay/index' :  '/pages/bnb/pay/index',
    'bnb/pages/map/index' :  '/pages/bnb/map/index',
    'bnb/pages/webview/index' :  '/pages/bnb/webview/index',
    'pages/alonePlatform/webView/index':'/pages/platform/webView/index',
    'pages/ppTrip/tripList/index':'/pages/tripP/tripList/index',
};