/*
 * @Chinese description: enter your description
 * @English description: enter your description
 * @Autor: wjp
 * @Date: 2022-06-10 15:10:36
 * @LastEditors: wjp
 * @LastEditTime: 2022-06-10 20:13:54
 */
import util from '../../util';
import { isOldBuApi, thirdsUtilCollect, writeLogModel, recordUserOrder, setUbtOrder, getPageTraceId, setPageTraceId, getOrderInfo, formatDate } from '../../../utils/payTinyAppLibs';
import request from '../../request';
import { clearPaymentTraceId } from './paymentTraceId';
import Log from '../../log';
import watcher from '../../oldWatcher';

let sysInfo = {};
wx.getSystemInfo({
    success: (result) => {
        sysInfo = result;
    },
});

let ubtBusiness = 'pre_payment';
let extend = null;
let payToken = '';
let paymentType = 'SDK';
let paySubmitRes = {};

export const initFullChain = (bus, type = 'SDK') => {
    ubtBusiness = bus;
    extend = null;
    setUbtOrder(0);
    setPageTraceId(util.getUniid());
    clearPaymentTraceId();
    paymentType = type;
};

export const midstageBrandIdAndOldQMap = {
    'aliPayPlugin': 'EB_MobileAlipay',
    'weixinJSPay': 'WechatScanCode',
};
// isOldQunarOrder： 是否老touch订单
export function getParam(isOldQunarOrder) {
    const ANU_ENV = process.env.ANU_ENV;
    let h5plat;
    let appId;
    let brandId;
    let thirdSubTypeID;
    if (ANU_ENV === 'wx') {
        h5plat = 29;
        brandId = isOldQunarOrder ? ['WechatScanCode'] : ['WEAPP', 'OGP_WEAPP'];//'WechatScanCode'
        appId = wx.getAccountInfoSync().miniProgram.appId;
        thirdSubTypeID = 4;
    }

    return { h5plat, brandId, appId, thirdSubTypeID };
}

export async function getRequestExtend(cashierUrl = '') {
    const query = cashierUrl.split('?');
    const urlParams = util.queryToParam(query[1]);
    const { cookies } = await util.getGlobalInfoAsync();
    const oldQtouch = getIsOldQunarOrder(cashierUrl) ? '1' : '0';
    const getQLoginToken = (() => {
        return {
            _q: cookies['_q'],
            _v: cookies._v,
            _t: cookies._t,
            _s: cookies._s,
        };
    })();
    const extend = JSON.stringify({
        qtouch_orderNo: urlParams.orderNo,
        qtouch_token: urlParams.token,
        qtouch_sign: urlParams.sign,
        qtouch_qmpVersion: urlParams.qmpVersion,
        oldQtouch: oldQtouch,
        // q端登录态cookie： _q;_v;_t;_s;
        'pay-q-cookie': `_q=${getQLoginToken._q};_v=${getQLoginToken._v};_s=${getQLoginToken._s};_t=${getQLoginToken._t}`,
        qtouch_isVouch: urlParams.isVouch,
        qtouch_isXmfappNew: process.env.ANU_ENV === 'quick',
    });
    return extend;
}
export function getIsOldQunarOrder(url) {
    return /payment7/g.test(url) || /payment6\/index3/.test(url);
}
export function getCurrentPayment(thirdPartyList, brandIdArr) {
    return thirdPartyList.find(item => brandIdArr.indexOf(item.brandId) > -1);
}
export function updateUrlSearch(url, newSearch) {
    const urlArr = url.split('?');
    return `${urlArr[0]}?${newSearch}`;
}
export function showPayResutlModal(cb) {
    setTimeout(() => {
        wx.showModal({
            title: '支付结果',
            content: '您是否已完成支付？',
            confirmText: '支付成功',
            cancelText: '未成功',
            success: () => {
                if (typeof cb === 'function') {
                    cb();
                }
            }
        });
    }, 1000);
}

export const Loading = {
    show: (content) => {
        wx.showLoading({ title: content, mask: true });
    },
    hide: () => {
        wx.hideLoading();
    },
};

export function midstageSdkUtilInit(midstageSendWatcher) {
    const initUtils = {
        loading: Loading,
        traceLog: midstageSendWatcher,
    };
    thirdsUtilCollect(initUtils);
}

// 31104404 阻塞问题告警
export const reportErrorLog = function (data) {
    try {
        const reportPayToken = data.payToken || payToken || 'defaultPayToken';
        const extendInfo = {
            plat: 'Q-' + process.env.ANU_ENV,
            ...data.extendInfo,
            payToken: reportPayToken,
        };
        delete data.extendInfo;
        writeLogModel({
            data: {
                errorType: '30000',
                errorMessage: '系统异常',
                payToken: reportPayToken,
                pageId: '10650080117',
                extendInfo: JSON.stringify(extendInfo),
                ...data
            },
            h5plat: '29',
            context: {
                request: request,
                util: util,
                subEnv: 'fat5069',
            },
            success: () => console.log('reportErrorLog success'),
            fail: (e) => console.log('reportErrorLog fail', e),
            complete: (e) => console.log('reportErrorLog complete', e)
        }).excute();
        sendUbt('reportErrorLog', {
            ...data,
            type: 'warning',
            warningCode: data.errorType,
            desc: data.errorMessage,
            level: data.level || 'p2'
        });
    } catch (error) {
        console.log('reportErrorLog error', error);
    }
};

function getOrderid() {
    try {
        const payOrderInfo = getOrderInfo(paySubmitRes);
        if (!payOrderInfo) return '';
        return payOrderInfo.orderId;
    } catch (error) {
        console.log(error);
    }
}
export function getMerchantId() {
    try {
        const payOrderInfo = getOrderInfo(paySubmitRes);
        if (!payOrderInfo) return '';
        return payOrderInfo.merchantId;
    } catch (error) {
        console.log(error);
    }
}

const sendNanachiLog = (actionType, exParams) => {
    var opts = exParams || {};
    opts['action-type'] = actionType;
    opts['pay'] = 'pay';
    Log(opts);
};
const sendWatcher_old = (actionType, exParams) => {
    var opts = exParams || {};
    const suffix_page = process.env.ANU_ENV === 'wx' ? 'wechat' : process.env.ANU_ENV;
    opts['action-type'] = actionType;
    opts['pay'] = 'pay';
    opts['page'] = 'cashier_' + suffix_page;
    opts['_exclude'] = false;
    watcher.click_pay(opts);
};

export const sendUbt = (actionType, ubtData) => {
    if (typeof actionType !== 'string'){
        ubtData = actionType;
        actionType = actionType.a  || actionType.devOriKey;
    }
    const data = {
        type: 'info',
        ...ubtData,
        plat: 'mini',
        miniType: process.env.ANU_ENV,
        company: 'qunar',
        business: ubtBusiness,
        orderId: getOrderid(),
        payToken,
        version: getApp().globalData.pVersion,
        merchId: getMerchantId(),
        devOriKey: ubtData.devOriKey || ubtData.a || '',
        desc: ubtData.desc || ubtData.dd || '',
        deviceBrand: sysInfo.brand,
        osName: sysInfo.platform,
        locale: sysInfo.language,
        paymentType,
    };
    console.log(data);
    sendNanachiLog(actionType, data);
    sendWatcher_old(actionType, data);
};
// 记录用户trace
export function reportTraceLog(param = {}) {
    try {
        if (!extend) {
            const pages = getCurrentPages()
            let currentPages = pages[pages.length - 1];
            const route = currentPages ? currentPages.route : '';
            extend = {
                enterTime: formatDate(new Date()) +
                    new Date()
                        .getMilliseconds()
                        .toString()
                        .padStart(3, '0'),
                // FSP: 0,
                reqPaywayTime: param.reqPaywayTime,
                pageWidth: sysInfo.screenWidth,
                pageHeight: sysInfo.screenHeight,
                URL: route,
                version: getApp().globalData.pVersion,
                company: 'qunar',
                plat: 'mini',
                business: 'pre_payment',
                deviceBrand: sysInfo.brand,
                miniType: process.env.ANU_ENV,
            };
        }
        extend = {
            ...extend,
            business: ubtBusiness,
            ...param
        };
        payToken = this.payOptions && this.payOptions.midstageData && this.payOptions.midstageData.payToken;
        recordUserOrder({
            data: {
                payToken,
                pageTraceId: getPageTraceId(),
                orderId: payToken,
                extend: JSON.stringify(extend),
            },
            context: {
                request: request,
                util: util,
                subEnv: 'fat18',
            },
        }).excute();
    } catch (error) {
        console.log(error);
    }
}

export function setPaySubmitRes(res) {
    paySubmitRes = res;
}