import Log from '../log';
import nanachi_request from '../request.js';
import payFactory from './payFactory.js';
import watcher from '../oldWatcher';
import payFactoryMidstage from './pay-midstage.js';
import { updateUrlSearch, reportErrorLog, reportTraceLog, initFullChain } from './utils/midstage';

const ANU_ENV = process.env.ANU_ENV;
let timeLastCall = 0;  //节流时间戳

export default class PayCommon {
    constructor() {
        this.payOptions = {};
        // this.payComponent= null;
        this.submitting = false;
        this.hasSubmitPay = false;
        this.payInfo = {};
        this.venderId = '';
        this.payPlugin = payFactory.getPayPlugin();
        this.payWayHandler = payFactory.getPayWayHandler();
        this.payMidstagePlugin = payFactoryMidstage.toPay;
        this.payWayMidstageHandler = payFactoryMidstage.payWayHandle;
        this.toPayWithType = payFactoryMidstage.toPayWithType;
        this.getPayRes = payFactoryMidstage.getPayRes;
        this.getRes_102 = payFactoryMidstage.getRes_102;
        this.reportTraceLog = reportTraceLog;
        this.midstagePaying = false;
        this.paymentTraceId = '';
        this.isApiJumpCashier = false;
        this.isProcessEnd = false;
        this.isPayPageUnmount = false;
        this.isJumpMidstageCashier = false; // 没有钱包时是直接唤起微信支付的，不会进到收银台页面，改标志是记录本次支付是否进入过收银台页面（主要是处理进入收银台后钱包余额变为 0 了，然后用户又刷新页面，此时用户没有钱包余额，需要通过此标志来实现没有余额也能进入收银台的效果）
        this.is_201_confirm = false;
        this.isWalletNotEnough = false;
    }
    resetSubmitStatus = () => {
        this.hasSubmitPay = false;
        this.submitting = false;
        this.midstagePaying = false;
    }
    openCashier = (options) => {
        this.sendWatcher('openCashier');
        // 节流
        if (new Date() - timeLastCall < 3000) {
            this.sendWatcher('命中节流');
            reportErrorLog({
                errorType: '31011',
                errorMessage: '命中节流',
                extendInfo: options
            });
            timeLastCall = new Date();
            return;
        } else {
            timeLastCall = new Date();
        }
        if (this.submitting && !options.cashierUrl) {
            return;
        }
        initFullChain('pre_payment');
        // 业务传入支持变价： supportChangePrice
        // options.supportChangePrice= true
        this.submitting = true;
        this.isApiJumpCashier = false; // API场景，为了使用钱包跳转到支付收银台
        this.isJumpMidstageCashier = false;
        this.is_201_confirm = false;
        this.isWalletNotEnough = false;
        this.isProcessEnd = false; // 支付流程是否结束
        // this.payComponent = thisComponent;
        const { getQueryString, getFunction, generateProcessEndCallback, generateCompleteCallback } = this;
        this.payOptions = { ...options };
        this.payOptions.cashierUrl = options.cashierUrl || '';
        this.payOptions.openId = options.openId || '';
        this.payOptions.orderNo = getQueryString(options.cashierUrl, 'orderNo') || '';
        this.payOptions.directUse = options.directUse || 'no';
        this.payOptions.isDirect = options.isDirect || false;
        this.payOptions.userInfo = options.userInfo || {};
        this.payOptions.success = generateProcessEndCallback(options.success);
        this.payOptions.fail = generateProcessEndCallback(options.fail);
        this.payOptions.cancel = generateProcessEndCallback(options.cancel);
        this.payOptions.complete = generateCompleteCallback(options.complete);
        this.payOptions.beforeOpen = getFunction(options.beforeOpen);
        this.payOptions.afterOpen = getFunction(options.afterOpen);
        this.payOptions.beforeShowPriceChangeModal = getFunction(options.beforeShowPriceChangeModal);
        this.payOptions.afterShowPriceChangeModal = getFunction(options.afterShowPriceChangeModal);
        this.payOptions.togglePayDlg = getFunction(options.togglePayDlg);
        // this.payOptions.cashierUrl = "https://bindcardcert.beta.qunar.com/webapp/payment6/index2?orderId=076142974923926&merchantId=8000001&requestId=20220407104038TP0297299855986284298240&locale=zh-CN&payToken=20220407104038TP0297299855986284298240";
        // this.payOptions.midstageSwitchFlag = this.payOptions.cashierUrl.indexOf('payment6') >= 0;
        this.payOptions.midstageSwitchFlag = /payment[6|7]/g.test(options.cashierUrl);
        // 初始化中台信息
        if (options.isFriendPay) {
            reportErrorLog({
                errorType: '31012',
                errorMessage: '发现好友代付单',
                extendInfo: options
            });
        }
        if (!options.openId) {
            this.sendWatcher('openId_is_empty');
        }
        this.payOptions.midstageData = payFactoryMidstage.payMidstageData.call(this);
       
        if (this.payOptions.midstageSwitchFlag === true) {
            this.sendWatcher('midstagepay', options);
            // midstageSdkUtilInit();

            // 防止连续提交点击
            if (this.midstagePaying) {
                return;
            }
            this.midstagePaying = true;
            try {
                // 发送全链路数据
                this.reportTraceLog();
                this.payWayMidstageHandler();
            } catch (e) {
                this.sendWatcher('catch start', e);
            }
        } else {
            this.getPayInfos(options.cashierUrl, { p: 'pad', isXmfappNew: (ANU_ENV === 'quick') });
        }

        this.sendWatcher('pay_start', { // 添加支付前埋点数据
            ANU_ENV,
            options: JSON.stringify(options)
        });
    }
    //更新中台信息，在微信变价时
    updateMidstageData = (newData) => {
        this.payOptions.cashierUrl = updateUrlSearch(this.payOptions.cashierUrl, newData);
        this.payOptions.midstageData = payFactoryMidstage.payMidstageData.call(this);
    }
    payPageDidMount = () => {
        this.isProcessEnd = false;
    }
    // api 跳转收银台页面退出时调用，确保页面销毁时会触发回调
    payPageUnmountCheck = () => {
        // api跳转收银台，页面销毁时判断是否已经回调过（isProcessEnd为 true），如果没有回调过说明页面退出，触发取消回调
        if (this.isApiJumpCashier && !this.isProcessEnd) {
            this.sendWatcher('payPageUnmountCheck');
            this.isPayPageUnmount = true;
            this.payOptions.cancel({ code: '2', message: '支付取消' });
            this.payOptions.complete({ code: '3', message: '支付完成' });
            this.isPayPageUnmount = false; // 用完清空
        }
    }
    // 生成流程结束回调函数，调用回调前，检查是否需要退回上一页
    generateProcessEndCallback = (fun) => {
        let funRes = this.empty;
        if (typeof fun === 'function') {
            funRes = fun;
        }
        const that = this;
        return (param)=>{
            try { that.sendWatcher('backAction_call'); } catch (e) {console.error(e);}
            this.isProcessEnd = true; // 标记流程结束
            if (this.isApiJumpCashier && !this.isPayPageUnmount){
                wx.showLoading({title: '正在查询支付结果…'});
                setTimeout(() => {
                    wx.navigateBack();
                    setTimeout(() => {
                        wx.hideLoading();
                        funRes(param);
                        try { that.sendWatcher('backAction_call_success'); } catch (e) {console.error(e);}
                    }, 1000);
                }, 1000);
            } else {
                funRes(param);
                try { that.sendWatcher('backAction_call_success'); } catch (e) {console.error(e);}
            }
        };
    }
    // 生成流程complete回调函数: 需要返回上一页的，这里不用再次navigateBack
    // 隐藏loading并延迟2000ms即可
    generateCompleteCallback = (fun) => {
        let funRes = this.empty;
        if (typeof fun === 'function') {
            funRes = fun;
        }
        return (param)=>{
            if (this.isApiJumpCashier && !this.isPayPageUnmount){
                setTimeout(() => {
                    wx.hideLoading();
                    funRes(param);
                }, 2010); // 多加 10 ms 是为了确保 complete 比 cancel/success 晚触发
            } else {
                funRes(param);
            }
        };
    }
    getFunction = (fun) => {
        if (typeof fun === 'function') {
            return fun;
        }
        return this.empty;
    }
    payFail = (res) => {
        this.resetSubmitStatus();
        this.payOptions.fail({ code: '1', message: '支付失败', data: res });
        this.payOptions.complete({ code: '3', message: '支付完成', data: res });
    }
    showModal = (content, result = {}, callback) => {
        this.resetSubmitStatus();
        const me = this;
        wx.showModal({
            title: '支付提示',
            content: content || result.message,
            showCancel: false,
            confirmColor: '#00bcd4',
            confirmText: '确认',
            cancelText: '取消',
            success: function () {
                if (typeof callback === 'function') {
                    callback();
                } else {
                    result.errMsg = (result.errMsg || result.message);
                    result.hasShowTip = true;
                    me.payFail(result);
                }
            }
        });
    }
    // 获取收银台信息
    getPayInfos = (url, data) => {
        this.request({ url, data }).then(
            this.getPayInfosCallback
        ).catch(
            (e) => { this.sendWatcher('getPayInfos_fail', {e}); }
        );
    }
    getPayInfosCallback = (data) => {
        this.payInfo = data.dataObj || {};
        this.payWayHandler(data);
    }
    togglePayDlg = (st) => {
        this.payOptions.togglePayDlg(st);
    }

    // 参数payData是支持变价时，传来的新的支付信息，而不能用老的保存在this的payInfo
    getSubmitPayData = (payTypeName, payData) => {
        const payInfo = payData || this.payInfo;
        var payTypeData = payInfo.bankList[payTypeName] || {};
        this.venderId = payTypeData.venderId;
        let data = {
            orderNo: payInfo.orderNo,
            token: payInfo.token,
            avers: payInfo.avers,
            vid: payInfo.vid,
            sign: payTypeData.sign,
            venderId: payTypeData.venderId,
            touchPayUrl: payTypeData.touchPayUrl,
            openId: this.payOptions.openId,
            p: 'pad'
        };
        if (ANU_ENV === 'qq' || (ANU_ENV === 'quick' && payTypeName === 'aliPayPlugin') || payTypeName === 'alipayRealPreAuthPlugin') {
            data.index = payInfo.index;
            data.nowBank = payInfo.nowBank;
            data.bd_source = payInfo.bd_source;
        }
        if (ANU_ENV === 'quick' && payTypeName === 'aliPayPlugin') {
            data.isAliMSPayExist = payInfo.isAliMSPayExist;
            data.isAlipayUserFee = payInfo.isAlipayUserFee;
            data.userFeeRate = payInfo.userFeeRate;
        }
        return data;
    }
    // 选择某种支付方式，下单
    // 参数payData是支持变价时，传来的新的支付信息，而不能用老的保存在this的payInfo
    private_submitPay = (payTypeName, payData) => {
        if (this.hasSubmitPay) {
            return;
        }
        this.hasSubmitPay = true;
        // 中台
        if (this.payOptions.midstageSwitchFlag === true) {
            this.toPayWithType(payTypeName);
            return;
        }
        wx.showLoading({ title: '正在跳转' });
        const data = this.getSubmitPayData(payTypeName, payData);
        const url = data.touchPayUrl;
        this.request({ url, data }).then(
            (data) => {
                const res = { payType: payTypeName, ...data };
                this.submitCallback(res);
                wx.hideLoading();
            }
        ).catch(
            () => {
                this.sendWatcher('submitPay_fail');
                wx.hideLoading();
            }
        );
        this.sendWatcher('submitPay');
    }

    submitCallback = (data) => {
        this.resetSubmitStatus();
        //变价逻辑提示
        var changePrice = data.changePrice;
        if (changePrice) {
            var res = { ret: false, errMsg: changePrice };
            var payData = data.dataObj;
            this.payOptions.beforeShowPriceChangeModal();
            if (ANU_ENV === 'wx' && this.payOptions.supportChangePrice) {
                const me = this;
                this.showModal(changePrice, null, function () {
                    me.submitting = true;
                    me.payOptions.afterShowPriceChangeModal();
                    me.private_submitPay('weixinJSPay', payData);
                });
            } else {
                this.showModal('订单已变价，无法支付，请重新提交订单', res);
            }
            return;
        }
        // 调起第三方支付
        this.payPlugin(data);
    }
    request = ({ url, data = {}, method = 'GET', noPayFail = false }) => {
        const me = this;
        const { host, service, param } = me.splitUrl(url);
        return new Promise((resolve, reject) => {
            nanachi_request({
                host,
                service,
                param,
                data,
                method,
                success: res => {
                    // var data = res.data || {}; // 注意支付状态查询 的 返回
                    resolve(res);
                },
                fail: res => {
                    wx.hideLoading();
                    if (res.alertInfo && res.alertInfo.showMsg) {
                        me.showModal(res.alertInfo.showMsg, res);
                        me.sendWatcher('pay_intercepter');
                    } else if (res.message || res.statusmsg) {
                        me.showModal(res.message || res.statusmsg, res);
                        reject(res);
                    } else if (noPayFail) {
                        reject(res);
                    } else {
                        me.payFail(res);
                        reject(res);
                    }
                }
            });
        });
    }

    // 埋点
    sendWatcher = (actionType, exParams) => {
        this.sendWatcher_old(actionType, exParams);
        this.sendNanachiLog(actionType, exParams);
    }
    sendNanachiLog = (actionType, exParams) => {
        var opts = exParams || {};
        opts['action-type'] = actionType;
        opts['pay'] = 'pay';
        opts['orderNo'] = this.payOptions.orderNo;
        Log(opts);
    }
    sendWatcher_old = (actionType, exParams) => {
        var opts = exParams || {};
        const suffix_page = ANU_ENV === 'wx' ? 'wechat' : ANU_ENV;
        opts['action-type'] = actionType;
        opts['pay'] = 'pay';
        opts['page'] = 'cashier_' + suffix_page;
        opts['orderNo'] = this.payOptions.orderNo;
        opts['_exclude'] = false;
        watcher.click_pay(opts);
    }
    // 用于支付流程中途停止时 或 关闭支付弹框时，重置状态。参数为是否需要调用cancel和complete回调，默认不传会调。
    break_pay = (notCallBack) => {
        if (!notCallBack) {
            this.payOptions.cancel({ code: '2', message: '调起支付前，取消支付', data: {} });
            this.payOptions.complete({ code: '3', message: '支付完成', data: {} });
        }
        this.resetSubmitStatus();
    }
    // 为了兼容微信小程序老的方法
    destroy = () => {
        this.resetSubmitStatus();
    }
    getQueryUrl = (data) => {
        const sign = data.sign || '';
        const queryUrl = data.queryURL || '';
        const orderInfo = data.orderInfo || {};
        const { orderNo = '', token = '' } = orderInfo;
        return `${queryUrl}&sign=${sign}&orderNo=${orderNo}&token=${token}&venderId=${this.venderId}`;
    }
    splitUrl = (url) => {
        const urlReg = new RegExp(/(^https?:\/\/[^/]*)(.*$)/);
        if (!(url && url.match(urlReg))) {
            return {};
        }
        let param = {};
        let urlArr = url.split('?');
        let hostServiceStr = urlArr[0];
        let [, host, service] = hostServiceStr.match(urlReg);
        let paramStr = urlArr[1];
        if (paramStr) {
            paramStr.split('&').forEach(element => {
                let keyValue = element.split('=');
                param[keyValue[0]] = keyValue[1];
            });
        }
        return { host, service, param };
    }
    empty = () => { }
    getQueryString = (url, name) => {
        if (!name || !url) {
            return;
        }
        var result = url.match(new RegExp('[?&]' + name + '=([^&]+)', 'i'));
        if (result != null && result.length > 0 && result[1] != null) {
            return result[1];
        }
    }
}