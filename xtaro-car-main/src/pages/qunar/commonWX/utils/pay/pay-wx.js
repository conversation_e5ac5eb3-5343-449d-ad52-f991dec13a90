import EventEmitter from '../EventEmitter.js';
import config from '../config/config.js';
import { toRealNameGuide } from './utils/realname.js';


// 乎起第三方（微信）支付页面
function toPay(data) {
    // console.log(config.settings.requestDomain + config.service.postPayInfo);
    const me = this;
    let options = {};
    const isGuideAuth = judgeGuideAuth(data, me.payOptions);
    options.success = function(res){
        EventEmitter.dispatch('pay_PayDlg_toggleState', false);
        if (isGuideAuth) {
            me.sendWatcher('toguideauth');
            const AntiDisturbanceUrl = getAntiDisturbanceUrl(data);
            toRealNameGuide(AntiDisturbanceUrl);
            // auth.openAuth('cashier', AntiDisturbanceUrl);
            const authCallback = function() {
                setTimeout(function() {
                    me.payOptions.success({code: '0', message: '支付成功', data:res});
                    me.payOptions.complete({code: '3', message: '支付完成', data:res});
                }, 300);
                sendTemplateMessage.call(me, options.package);
            };
            addListener('onAuthCompleteToGuide', () => {
                authCallback();
            });
        } else {
            me.payOptions.success({code: '0', message: '支付成功', data:res});
            sendTemplateMessage.call(me, options.package);
        }
        me.sendWatcher('weixinJSPay_success');
    };
    options.fail = function(res){
        if (res.errMsg === 'requestPayment:fail cancel'){
            me.payOptions.cancel({code: '2', message: '取消支付', data:res});
            me.sendWatcher('pay_cancel');
        } else {
            me.payOptions.fail({code: '1', message: '支付失败', data:res});
            me.sendWatcher('pay_fail');
        }
    };
    options.complete = function(res){
        if (!(res.errMsg === 'requestPayment:ok' && isGuideAuth)) {
            me.payOptions.complete({code: '3', message: '支付完成', data:res});
        }
        me.sendWatcher('pay_complete');
    };
    try {
        options = data.payURL && Object.assign(options, JSON.parse(data.payURL)) || options;
    } catch (ex){
        me.showModal('JSON.parse(" + data.payURL + ")解析出错', ex);
        me.sendWatcher('pay_json_parse_error');
        return;
    }
    wx.requestPayment(options);
}

function getMemberRealNameGuide (data) {
    const { paySuccessGuide = {} } =  data || {};
    const { memberRealNameGuide = {} } = paySuccessGuide;
    return memberRealNameGuide;
}

function getAntiDisturbanceUrl(data) {
    const memberRealNameGuide = getMemberRealNameGuide(data);
    return memberRealNameGuide.realNameInfoQueryUrl || '';
}

function judgeGuideAuth(data) {
    const memberRealNameGuide = getMemberRealNameGuide(data);
    const { isNeedGuide } = memberRealNameGuide;
    const isGuideAuth = isNeedGuide;
    return isGuideAuth == 'true';
}

//支付成功发送模板消息
function sendTemplateMessage(payPackage){
    //"package":"prepay_id=wx20170619155448c5d10fae4c0476693116"
    var me = this;
    var prepayId = payPackage && payPackage.replace('prepay_id=','');
    if (!prepayId)
        return;
    var app = getApp();
    var { version = ''} = app.globalData.systemInfo;
    var param = {
        openId: me.payOptions.openId,
        orderNo: me.payOptions.orderNo,
        prepayId: prepayId,
        weChatVersion: version || '',
        weChatPlatform: (config.exParams && config.exParams.platform) || 'wechat$$$small'
    };
    var url = config.settings.requestDomain + config.service.postPayInfo;
    me.request({url, data: param, noPayFail: true}).then(
        () => { me.sendWatcher('sendTemplateMessage_success', { prepayId: prepayId });}, 
        () => { me.sendWatcher('sendTemplateMessage_fail', { prepayId: prepayId });});
}

function payWayHandle (data) {
    // 方便beta联调时，错误提示
    if (process.env.BUILD_ENV === 'beta' && data.qmpChannel !== 'qmpwxsapp') {
        this.showModal('qmpChannel传参错误，应为qmpwxsapp');
        return;
    }
    
    var payInfo = data.dataObj || {};
    var payTypeNames = payInfo.index || [];
    
    var hasWeixinPay = (payTypeNames.indexOf('weixinJSPay') >= 0); //包含微信支付方式
    
    //是否包含微信支付方式判断    
    if (!hasWeixinPay){
        this.showModal('支付方式配置不正确', data);  // "只支持微信支付，赞不支持其它支付方式"
        this.sendWatcher('not_weixinJSPay');
        return;
    }
    
    const payWayHandlerMap = {
        'directUse_weixinJSPay': () => {this.private_submitPay('weixinJSPay');},
        'directUse_no' : () => {
            this.private_submitPay('weixinJSPay');
        }
    };
    payWayHandlerMap[`directUse_${this.payOptions.directUse}`]();
}

function addListener(eventName, callback) {
    EventEmitter.removeListener(eventName);
    EventEmitter.addListener(eventName, callback);
}

export default {
    toPay,
    payWayHandle
};