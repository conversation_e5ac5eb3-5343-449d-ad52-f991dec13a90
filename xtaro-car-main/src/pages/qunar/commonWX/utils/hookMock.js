
const getPartUrlByParam = (url, param) => {
    const reg = /^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/;
    const res = reg.exec(url);
    const fields = ['url', 'scheme', 'slash', 'host', 'port', 'path', 'query', 'hash'];
    return res[fields.indexOf(param)];
};

const search2Json = search => {
    if (search != undefined) {
        let object = {};
        const arr = search.split('&');
        arr.forEach(item => {
            const [ key, value ] = item.split('=');
            object[key] = value;
        });
        return object;
    } else {
        return {};
    }
};

// path && method是否匹配
const urlMethodIsEqual = (reqPath, reqMethod, mockPath, mockMethod) => {
    reqPath = reqPath ? `/${reqPath}` : '';
    reqMethod = reqMethod || 'GET';
    return (reqPath == mockPath) && (reqMethod.toUpperCase() == mockMethod.toUpperCase());
};

// 验证参数是否相同，post接口需要验证body
const requestParamsIsEqual = (reqQuery, reqBody, mockQuery, mockBody) => {
    const __reqQuery = search2Json(reqQuery);
    reqBody = reqBody || {};
    try {
        return (JSON.stringify(__reqQuery) == mockQuery) && (JSON.stringify(reqBody) == mockBody);
    } catch (e) {
        return false;
    }
};

const matchUrlRequest = (options) => {
    let mockList = wx.getStorageSync('apiMockData') || [];
    let matchRequest = false;
    for (let key in mockList) {
        const mockItem = mockList[key];
        const __isOpen = mockItem.isOpen;
        if (__isOpen) {
            const path = getPartUrlByParam(options.url,'path');
            const query = getPartUrlByParam(options.url,'query');
            const __urlMethodIsEqual = urlMethodIsEqual(path, options.method, mockItem.path, mockItem.method);
            const __requestParamsIsEqual = requestParamsIsEqual(query, options.data, mockItem.query, mockItem.body);
            if (__urlMethodIsEqual && __requestParamsIsEqual) {
                matchRequest = mockItem;
                break;
            }
        }
         
    }
    return matchRequest;

};

const hookMock = (options) => {
    let __options = Object.assign({},options);
    const __matchUrlRequest = matchUrlRequest(options);
    if (__matchUrlRequest) {
        __options.method = 'POST';
        __options.url = 'https://ued.qunar.com/api/mini/mock';
        const { pid, id } = __matchUrlRequest;
        __options.data = { pid, id };
    }
    return __options;
};

export default hookMock;