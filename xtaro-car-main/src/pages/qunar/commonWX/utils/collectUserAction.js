

import { USER_ACTION, publishMessage } from './urs';
const collectUserAction = () => {
    const originPage = Page;
    Page = function (options) {
        const originOnShow = options.onShow || function () { };
        options.onShow = function (args) {
            // 收集用户行为
            const userActionParams = {
                code: USER_ACTION.C_PAGE_ENTER,
                page: this.route,
                timestamp: +new Date(),
            };
            publishMessage(userActionParams);

            originOnShow.call(this, args);

        };


        originPage.call(this, options);
    };
};

export default collectUserAction;