import Mark from '../../npm/@qnpm/qmark/dist/qmark.mini.umd.js';

/**
 * 埋点配置地址
 * http://mark.corp.qunar.com/markpage/rdc/markpoint/785/2659
 */
const markConf = {
    appcode: 'pf_nnc_trace',
    bizType: 'rdc',
    page: 'trace',
    module: 'mini',
    operType: 'show',
    id: 'lifecycle'
};
const qMark = Mark.hoc(markConf);

/**
 * SOURCE: 一期不做区分, 只在微信小程序中使用
 */
const SOURCE = 'mini';
/**
 * DATA_SET_KEY
 * 预留逻辑, 业务在元素上挂载的属性 `data-mark="xxx"` 会被记录下来
 */
const DATA_SET_KEY = 'mark';
/**
 * globalData 上挂载的埋点数据的 key 值
 */
const GLOBAL_TRACE_KEY = Symbol('__traceKey');
const TRACE_IDS = {
    CLICK: 'click',
    LIFECYCLE: 'lifecycle',
    TO: 'to',
    STAY_TIME: 'stayTime',
    TEXT: 'text'
};

const QMarkTrace = {
    // log: qMark.log.bind(qMark),
    log: function (log, globalData) {
        const common = this.get(globalData);
        const finalLog = Object.assign({}, log, { common });

        qMark.log(finalLog);
    },

    /**
     *
     * @param {string} path 页面路径
     * @param {string} type 类型 show: 前台, hide: 后台
     */
    setTime(path, type, d) {
        const g = this.getGlobalTrace(d);

        // 默认时间为 0
        g.stayTime = g.stayTime || 0;
        /**
         * 记录时间的队列, 以两个为一组, 计数下标为 show 的时间点, 偶数下标为 hide 的时间点
         */
        if (!g.times) g.times = [];

        // 如果没有, 设置为当前的页面路径
        if (!g.path) {
            g.path = path;
        }
        /**
         * 当前页面和缓存页面是同一个, 表示没有切换页面, 记录时间即可
         */
        if (g.path === path) {
            g.times.push(now());
        } else {
            // 已经是不同页面, 开始计算页面的停留时长
            const tLen = g.times.length;
            for (let i = 0; i < tLen; i += 2) {
                const start = g.times[i];
                const end = g.times[i + 1] || now();
                g.stayTime += end - start;
            }
            QMarkTrace.stayTime({ pageId: g.path, stayTime: g.stayTime });

            /**
             * 计算上一个停留时间后, 设置新页面
             */
            g.path = path;
            g.times = [now()];
            g.stayTime = 0;
        }
    },

    /**
     * 设置结束时间点
     */
    setTimeEnd() {
        const g = this.getGlobalTrace();
        if (!g.times) return;
        g.times.push(now());
    },

    /**
     * 点击
     * @param {object} data
     * ```json
     * {
     *   "action": "tap,longtap,change",
     *   "source": "mini",
     *   "pageId": "*"
     * }
     * ```
     */
    click: async function (data, node, fiber) {
        if (!['tap', 'longtap', 'change'].includes(data.action)) return;

        const path = getPath();
        const text = await getContext(node, fiber);
        this.log({
            id: TRACE_IDS.CLICK,
            ext: {
                source: SOURCE,
                pageId: path,
                text,
                ...data
            }
        });
    },

    /**
     * 路由跳转
     * @param {object} data
     * ```json
     * {
     *   "toPageId": "*",
     *   "fromPageId": "*",
     *   "action": "navigateTo,redirectTo,reLaunch,navigateBack,switchTab",
     *   "source": "mini"
     * }
     * ```
     */
    to: function (data) {
        this.log({
            id: TRACE_IDS.TO,
            ext: {
                source: SOURCE,
                ...data
            }
        });
    },

    /**
     *
     * @param {object} navParams
     * @param {string} action "navigateTo","redirectTo","reLaunch","navigateBack","switchTab"
     */
    nav: function (navParams, action) {
        const path = getPath();
        const logExt = {
            toPageId: navParams && navParams.url && navParams.url.split('?')[0],
            fromPageId: path,
            action,
            source: SOURCE
        };
        if (action === 'navigateBack') {
            try {
                const routes = getCurrentPages();
                const rLen = routes.length;
                if (rLen > 1) {
                    logExt.toPageId = routes[rLen - 2].route;
                } else {
                    logExt.toPageId = 'unknow';
                }
            } catch (error) {
                logExt.toPageId = 'unknow';
            }
        }
        this.to(logExt);
    },

    /**
     * 整个小程序生命周期, onLaunch, onShow 时需要传入第二个参数,
     * 受限于启动流程, 此时使用 getApp 获取不到数据
     * @param {object} data
     * ```json
     * {
     *   "lifecycleType": "appShow,appHide,launch",
     *   "source": "mini"
     * }
     * ```
     */
    lifecycle: function (data, globalData) {
        this.log(
            {
                id: TRACE_IDS.LIFECYCLE,
                ext: {
                    source: SOURCE,
                    ...data
                }
            },
            globalData
        );
    },

    /**
     * 微信中计算页面停留时间
     */
    wxGlobalShow() {
        /**
         * getCurrentPages 是微信小程序的 api
         */
        // eslint-disable-next-line no-undef
        const pages = getCurrentPages();
        const pLen = pages.length;
        const p = pages[pLen - 1];
        if (p && p.route) {
            QMarkTrace.setTime(p.route, 'show', this.globalData);
        }
    },

    /**
     * 页面停留时间
     * @param {object} data
     * ```json
     * {
     *   "stayTime": "*",
     *   "source": "mini",
     *   "pageId": "*"
     * }
     * ```
     */
    stayTime: function (data) {
        this.log({
            id: TRACE_IDS.STAY_TIME,
            ext: {
                source: SOURCE,
                ...data
            }
        });
    },

    GLOBAL_TRACE_KEY,
    get: function (data) {
        const g = this.getGlobalTrace(data);

        g.common.traceIndex++;
        const common = Object.assign({}, g.common);
        return common;
    },
    getGlobalTrace: function (data) {
        if (!data) {
            /**
             * getApp 是微信小程序的 api
             */
            // eslint-disable-next-line no-undef
            data = getApp().globalData;
        }

        if (!data[GLOBAL_TRACE_KEY]) {
            data[GLOBAL_TRACE_KEY] = {
                /**
                 * https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=594426632
                 */
                common: {
                    /**
                     * g2105: 微信小程序 pid
                     */
                    pid: 'g2105',
                    /**
                     * traceId: 只作为id使用，不做计算
                     */
                    traceId: Date.now(),
                    traceIndex: 0
                }
            };
        }

        return data[GLOBAL_TRACE_KEY];
    }
};

/**
 * 获取点击时热区的文本或者图标或者图片
 * 因为获取 background 时调用微信api为回调形式,所以此处需要用 async
 */
async function getContext(node, fiber) {
    if (!node || !fiber || (fiber && !fiber._owner)) return '';

    const texts = [];
    const childs = node.props && node.props.children;
    await loop(childs, node.props);

    /**
     * 深层遍历
     * child 和 props 是同级关系,为了在 child 为空时拿到 class 的值
     */
    async function loop(child, props) {
        const type = Type(child);
        if (['number', 'string'].includes(type)) {
            texts.push(child);
        }

        if ('object' === type) {
            if (child.type === 'image') {
                texts.push(child.props.src);
            }
            const children = child && child.props && child.props.children;
            if ('array' === Type(children)) {
                children.forEach((c) => loop(c, child.props));
            } else {
                loop(children, child.props);
            }
        }

        if ('array' === type) {
            child.forEach((c) => loop(c, props));
        }

        /**
         * 没有文本信息, 尝试通过 css 获取背景图, 后续扩展收集 icon 部分也在这里
         */
        if ('undefined' === type) {
            await new Promise((resolve) => {
                const klass = getClass(props);
                /**
                 * 如果没有 class 直接返回, 微信 api 查找需要 class
                 */
                if (!klass) resolve();
                wx.createSelectorQuery()
                    .in(fiber._owner.wx)
                    .select(klass)
                    .fields(
                        {
                            dataset: true,
                            computedStyle: ['background-image', 'background']
                        },
                        function (res) {
                            const bgi = res && res['background-image'];
                            const bg = res && res['background'];
                            const mark = res && res.dataset && res.dataset[DATA_SET_KEY];
                            const bgUrl = getBGUrl(bgi) || getBGUrl(bg);
                            const result = [];
                            if (mark) result.push(mark);
                            if (bgUrl) result.push(bgUrl);
                            texts.push(result.join(''));
                            resolve();
                        }
                    )
                    .exec();
                /**
                 * fields 回调函数有可能不会执行, 超过 600ms 没有返回就直接返回
                 */
                setTimeout(resolve, 600);
            });
        }
    }

    return texts.join('');
}

/**
 * ios 上真机测试时发现不支持 performance
 * 用 performance 的原因是防止手机端更改系统时间导致记录时间不准
 * 自行翻阅 api 了解
 */
function now() {
    try {
        return performance.now();
    } catch (error) {
        return Date.now();
    }
}

/**
 * 判断是否在 微信小程序 中
 * 一期只在微信小程序中使用
 */
function isWX() {
    return process.env.ANU_ENV === 'wx';
}

/**
 * 小程序 page 地址
 */
function getPath() {
    try {
        const pages = getCurrentPages()
        let currentPages = pages[pages.length - 1];
        const path = currentPages ? currentPages.route : '';
        return path;
    } catch (error) {
        return 'unknow';
    }
}

/**
 * 获取 className
 * eg: class="  classA classB   classC " => .classA.classB.classC
 * 拿到的数据可能是两个 key 值(class, className)
 */
function getClass(props) {
    const cn = (props && (props.className || props.class)) || '';

    if (!cn) return '';

    return '.' + cn.trim().replace(/\s+/, '.');
}

/**
 * 匹配字符串提取图片地址
 * eg: url("https://s.qunarzz.com/open_m_train/miniprogram/indexChange/train_icon.png")
 */
function getBGUrl(str) {
    const reg = /.*url\(['"](.+)['"]\).*/;
    if (!reg.test(str)) return;

    const match = str.match(reg);
    if (match) {
        return match[1];
    }

    return '';
}

/**
 *
 * @param {any} any
 * @returns function,string,object,array,undefined,boolean,number,symbol,regexp
 */
function Type(any) {
    return Object.prototype.toString.call(any).slice(8, -1).toLowerCase();
}

/**
 * 增加一层防护
 */
Object.keys(QMarkTrace).forEach((key) => {
    if (typeof QMarkTrace[key] !== 'function') return;
    const originFn = QMarkTrace[key];
    Object.defineProperty(QMarkTrace, key, {
        value: function () {
            if (!isWX()) return;
            try {
                return originFn.apply(this, arguments);
            // eslint-disable-next-line no-empty
            } catch (error) {}
        }
    });
});

export default QMarkTrace;
