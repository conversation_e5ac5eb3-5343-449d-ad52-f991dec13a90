// 封装页面回调
import util from './util';
import user from './user';
import watcher from './watcher';
import LogQmark from './logQmark';

// 微信授权回调 登录使用
// 有些情况下希望登录必须授权，传needAuth为true即可
const wxGetUserInfoCb = function(event, successCb, failCb, needAuth) {
    const env = process.env.ANU_ENV;
    if (env !== 'wx' && env !== 'qq') return;
    
    const data = util.getGlobalInfo();
    const isBind = data.user.isBind || false;
    // watcher处理
    if (event.userInfo) {
        watcher.watchCount('get-user-info-auth-success-wx');
    } else {
        watcher.watchCount('get-user-info-auth-fail-wx');
    }
    
    // 如果没绑定也没授权 直接返回
    if ((!event.userInfo && !isBind) || (needAuth && !event.userInfo)) {
        failCb && failCb();
        return;
    }
    user.updateUserInfo({
        user: {
            wxInfo: event.userInfo
        }
    });
    successCb && successCb.call(this, isBind);
};

// 手机号授权回调
const wxGetPhoneCb = function(event, successCb, failCb) {
    const env = process.env.ANU_ENV;
    if (env !== 'wx' && env !== 'qq' && env !== 'bu' && env !== 'ali') return;
    if (event.detail && event.detail.errno === 1400001) {
        LogQmark({
            page: 'login',
            module: 'login',
            operType: 'click',
            id: 'login_platform_no_money',
            ext: event
        });
        wx.showToast({
            icon: 'none',
            title: '请稍后再试',
            duration: 1000
        });
        return;
    }
    if (event.encryptedData) {
        watcher.watchCount(`get-phone-auth-success-${env}`);
        LogQmark({
            page: 'login',
            module: 'login',
            operType: 'click',
            id: `get_phone_auth_success_${env}`,
        });
    } else {
        watcher.watchCount(`get-phone-auth-fail-${env}`);
        LogQmark({
            page: 'login',
            module: 'login',
            operType: 'click',
            id: `get_phone_auth_fail_${env}`,
            ext: event
        });
    }
    if (!event.encryptedData) {
        failCb && failCb();
        return;
    }
    successCb && successCb.call(this, {
        encryptedData: event.encryptedData,
        iv: event.iv,
        code:event.code
    });
};

// 拿unionId
// 20200116 如果有unionId就直接返回 不用再await
const wxGetUnionIdCb = async function(event, successCb, failCb) {
    const env = process.env.ANU_ENV;
    if (env !== 'wx' && env !== 'qq') return;
    const storage = util.getGlobalInfo();
    const { cookies } = storage;
    if (cookies.unionId) {
        successCb && successCb.call(this);
        return;
    }
    if (!event.userInfo) {
        failCb && failCb();
        return;
    }
    await user.getQunarToken();
    user.updateUserInfo({
        user: {
            wxInfo: event.userInfo
        }
    });
    successCb && successCb.call(this);
};

// 单纯的拿平台信息
// 百度失败会返回
const wxGetPlatInfoCb = function(event, successCb) {
    const env = process.env.ANU_ENV;
    let wxParse = {};
    if (env === 'ali') {
        user.getMiniProgramUserInfo().then(res => {
            successCb.call(this, res);
        });
        return;
    }
    if (env === 'bu') {
        wxParse = event.userInfo;
        successCb.call(this, {ret: true, data: {userInfo: event.userInfo}});
    }
    if (env === 'wx') {
        if (event.userInfo) {
            wxParse = event.userInfo;
            successCb.call(this, {ret: true, data: {userInfo: event.userInfo}});
        } else {
            successCb.call(this, {ret: false, data: {}});
        }
    }
    user.updateUserInfo({
        user: {
            wxInfo: wxParse
        }
    });
};

export default {
    wxGetUserInfoCb,
    wxGetPhoneCb,
    wxGetUnionIdCb,
    wxGetPlatInfoCb
};