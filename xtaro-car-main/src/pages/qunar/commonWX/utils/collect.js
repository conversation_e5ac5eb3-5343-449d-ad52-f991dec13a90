// import QMark from '../../npm/@qnpm/qmark/dist/qmark.mini.umd.js';
import { xEnv } from '@ctrip/xtaro';
import to from 'await-to-js';
import md5 from '../flight/md5';
import CryptoJS from '../anticraw/utils/crypto';
import util from './util';
import user from './user';

const isBetaEnv = xEnv.getDevEnv()?.toLowerCase() === 'fat';
// 加密key
const SECRET_KRY = 'A1rdiRZ8KmptrQ8X';
const crdf = isBetaEnv
  ? 'https://antifp.qunar.com/wx/v2/crdf'
  : 'https://antifp.qunar.com/wx/v2/crdf';
const collect = isBetaEnv
  ? 'https://antifp.qunar.com/active/collect'
  : 'https://antifp.qunar.com/active/collect';
const URLS = { crdf, collect };
const Api = util.getNameSpace();
// 设备id本地储存字段
const MINIAPP_DEVIEC_ID = 'ql7qGD';
const SIGN_AES_KEY = 'aNTZ0iANhRPBLPBC';
const SIGN_AES_IV = 'E9J1DxO4pzjOaxj2';
const relevanceId = util.randomString(32);
const isWx = process.env.ANU_ENV === 'wx';

// 小程序 api promise 化
const promisify = fn => {
  if (typeof fn !== 'function') return fn;
  return (args = {}) => {
    return new Promise((rel, rej) => {
      const mergedOptions = Object.assign(args, {
        success: rel,
        fail: rej,
      });
      fn(mergedOptions);
    });
  };
};

const jscodeValue = '';
let envVersionInAliLowVersion;
// 去哪儿逻辑，不需要
// const refreshJscode = async(once)=>{
//     let code
//     try {
//         code = await user.getAuth('auth_base');
//     } catch (err) {
//         console.log("err",err)
//     }
//     jscodeValue = code;

//     if (once) {
//         if (process.env.ANU_ENV === 'ali' && !Api.getAccountInfoSync){
//             envVersionInAliLowVersion = (await promisify(Api.getRunScene)()).envVersion;
//         }
//     }
// };
// refreshJscode(true);
// setInterval(()=>{
//     refreshJscode();
// },180000);

const getCurWifi = () => {
  return promisify(Api.startWifi)().then(() => {
    return promisify(Api.getConnectedWifi)();
  });
};

// https://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid
const uuidv4 = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

const disKey = (arr, key) => {
  return arr === null || arr.includes(key);
};

/**
 *
 * @param {Array} mesKey 指定的收数key
 * @returns
 */
async function getDeviceInfo(mesKey) {
  return {
    loginRet: disKey(mesKey, 'loginRet')
      ? await to(promisify(Api.login)())
      : null,
    systemRet: disKey(mesKey, 'systemRet')
      ? await to(promisify(Api.getSystemInfo)())
      : null,
    screenBrightnessRet: disKey(mesKey, 'screenBrightnessRet')
      ? await to(promisify(Api.getScreenBrightness)())
      : null,
    netWorkTypeRet: disKey(mesKey, 'netWorkTypeRet')
      ? await to(promisify(Api.getNetworkType)())
      : null,
    connectedWifiRet:
      disKey(mesKey, 'connectedWifiRet') && isWx
        ? await to(promisify(Api.getConnectedWifi)())
        : null,
    batteryInfoRet: disKey(mesKey, 'batteryInfoRet')
      ? await to(promisify(Api.getBatteryInfo)())
      : null,
    locationRet: disKey(mesKey, 'locationRet')
      ? await to(promisify(Api.getLocation)())
      : null,
    curWifiRet:
      disKey(mesKey, 'curWifiRet') && isWx ? await to(getCurWifi()) : null,
    relevanceId,
  };
}

// 抄的
function encode64(input) {
  // base64加密开始
  const keyStr =
    'ABCDEFGHIJKLMNOP' +
    'QRSTUVWXYZabcdef' +
    'ghijklmnopqrstuv' +
    'wxyz0123456789+/' +
    '=';
  let output = '';
  let chr1;
  let chr2;
  let chr3 = '';
  let enc1;
  let enc2;
  let enc3;
  let enc4 = '';
  let i = 0;
  do {
    chr1 = input.charCodeAt(i++);
    chr2 = input.charCodeAt(i++);
    chr3 = input.charCodeAt(i++);
    enc1 = chr1 >> 2;
    enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
    enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
    enc4 = chr3 & 63;
    if (isNaN(chr2)) {
      enc3 = enc4 = 64;
    } else if (isNaN(chr3)) {
      enc4 = 64;
    }
    output =
      output +
      keyStr.charAt(enc1) +
      keyStr.charAt(enc2) +
      keyStr.charAt(enc3) +
      keyStr.charAt(enc4);
    chr1 = chr2 = chr3 = '';
    enc1 = enc2 = enc3 = enc4 = '';
  } while (i < input.length);

  return output;
}

const parseDeviceInfo = rets => {
  const {
    loginRet,
    systemRet,
    screenBrightnessRet,
    netWorkTypeRet,
    batteryInfoRet,
    locationRet,
    curWifiRet,
  } = rets;
  const errors = Object.keys(rets).map(key => rets[key] && rets[key][0]);

  const keyMap = {
    SDKVersion: 'sdkVersion',
  };
  const sys = [
    'brand',
    'model',
    'pixelRatio',
    'screenWidth',
    'screenHeight',
    'statusBarHeight',
    'windowHeight',
    'windowWidth',
    'language',
    'version',
    'system',
    'platform',
    'fontSizeSetting',
    'SDKVersion',
    'batteryLevel',
    'safeArea',
    'deviceOrientation',
  ].reduce((acc, curKey) => {
    const retKey = keyMap[curKey] ? keyMap[curKey] : curKey;
    acc[retKey] = systemRet && systemRet[1][curKey];
    return acc;
  }, {});

  return {
    fields: {
      ...sys,
      charging:
        batteryInfoRet && batteryInfoRet[1] ? batteryInfoRet[1].isCharging : '',
      networkType:
        netWorkTypeRet && netWorkTypeRet[1]
          ? netWorkTypeRet[1].networkType
          : '',
      latitude: locationRet && locationRet[1] ? locationRet[1].latitude : '',
      longitude: locationRet && locationRet[1] ? locationRet[1].longitude : '',
      screenBrightness:
        screenBrightnessRet && screenBrightnessRet[1]
          ? screenBrightnessRet[1].value
          : '',
      currentWifi: curWifiRet && curWifiRet[1] ? curWifiRet[1].wifi : {},
      jsCode: loginRet && loginRet[1] ? loginRet[1].code : '',
    },
    errors,
  };
};

// eslint-disable-next-line no-console
const xlog = (...args) => console.log.apply(console, ['collect.js', ...args]);

const getUserData = (() => {
  let closureData = '';
  return () => {
    if (closureData) return closureData;
    const info = util.getGlobalInfo();
    return (closureData = info || { user: {}, cookies: {} });
  };
})();

export const openId = getUserData().user.openId || '';

export function encryptionParms(parmas) {
  const srcs = CryptoJS.enc.Utf8.parse(
    typeof parmas === 'object' ? JSON.stringify(parmas) : parmas,
  );
  // key 必须为16位或32位
  const key = CryptoJS.enc.Utf8.parse('A-16-Byte-keyValA-16-Byte-keyVal');
  const iv = CryptoJS.enc.Utf8.parse('A-16-Byte-String');
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  const params = encode64(encrypted.toString());
  return params;
}

export function aesEncryp(parmas, keyStr, ivStr) {
  const srcs = CryptoJS.enc.Utf8.parse(
    typeof parmas === 'object' ? JSON.stringify(parmas) : parmas,
  );
  // key 必须为16位或32位
  const key = CryptoJS.enc.Utf8.parse(keyStr);
  const iv = CryptoJS.enc.Utf8.parse(ivStr);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  const params = encode64(encrypted.toString());
  return params;
}

async function getFields(extCanvas, noLocation) {
  const customInfoFields = !noLocation
    ? null
    : [
        'loginRet',
        'systemRet',
        'screenBrightnessRet',
        'netWorkTypeRet',
        'connectedWifiRet',
        'batteryInfoRet',
        'curWifiRet',
      ];
  const sysInfoFields = parseDeviceInfo(await getDeviceInfo(customInfoFields));
  let appId = '';
  let smallKey = process.env.ANU_ENV;
  if (process.env.ANU_ENV === 'wx') {
    smallKey = 'wechat';
  }
  const smallPlatform = process.env.USER_CENTER_ID
    ? `${smallKey}$$$${process.env.USER_CENTER_ID}`
    : `${smallKey}$$$small`;

  try {
    const { miniProgram } = Api.getAccountInfoSync();
    appId = miniProgram && miniProgram.appId;
  } catch (error) {
    appId = '';
  }

  return {
    errors: sysInfoFields.errors,
    ...sysInfoFields.fields,
    time: +new Date(),
    smallPlatform,
    miniProgramType: process.env.ANU_ENV,
    appId,
    openId,
    ...extCanvas,
  };
}

/**
 * DeviceId 组件拆分, 手机设备信息部分会有其他地方调用.
 * @param {*} type 请求地址类型, 目前只有两个
 *      - crdf 用于设备指纹包含 canvas 信息
 *      - collect 不包含 canvas, 反扒用在酒店的三个页面
 * @param {*} extCanvas 扩展 canvas 字段
 * @param {*} pageUrl 页面路径
 * @param {*} query 页面请求参数
 * @param {*} triggerType https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=*********
 */
export async function fetchDeviceId(
  type,
  extCanvas = {},
  pageUrl = '',
  query = '',
  triggerType = '',
  noLocation,
) {
  const url = URLS[type];
  if (!url) return;

  const { errors, ...resetFields } = await getFields(
    { ...extCanvas, triggerType },
    noLocation,
  );
  // 加密
  const baseInfo = encryptionParms(resetFields);
  // warning抛警告
  errors.forEach(err => {
    if (err) {
      // eslint-disable-next-line
            console.warn(`[${new Date()}] [warn] - ${err.errMsg}`);
    }
  });

  const webCode = md5(`f_${uuidv4()}_${~~(Math.random() * 100)}`);
  const userData = encryptionParms(getUserData());

  const t = encryptionParms({ webCode });
  const cookie = `${MINIAPP_DEVIEC_ID}=${encodeURIComponent(t)}`;

  return new Promise((resolve, reject) => {
    Api.request({
      url,
      method: 'POST',
      data: {
        pageUrl,
        baseInfo,
        query,
        cookies: userData,
      },
      header: { cookie },
      success: r => resolve(r, webCode),
      fail: err => reject(err, webCode),
    });
  });
}

/**
 * @param {*} action 包括: 'navigateTo', 'redirectTo', 'reLaunch', 'navigateBack', 'switchTab'
 * @param {*} obj 包含跳转的全路径. 如: /pages/abc/index?mode=test
 * {
 *    url: '',
 *    query: ''
 * }
 * 酒店页面path: https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=206331320
 * 风控: https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=423947402
 *
 */
export function sendDeviceInfo(action, obj, bus, noLocation) {
  xlog(
    'function sendDeviceInfo:::',
    '\n',
    'action',
    action,
    '\n',
    'query and path:::',
    obj,
  );

  // 不走娜娜奇的生命周期
  if (bus) {
    // 跳转的页面路径
    const { url } = obj;
    const { query } = obj;
    const needAction = ['navigateTo', 'redirectTo', 'AppOnShow'].includes(
      action,
    );
    if (!url || !needAction) return;
    fetchDeviceId('collect', null, url, query, action, noLocation);
    return '';
  }

  // 跳转的页面路径
  const { url } = obj;
  // 如果不是微信/QQ小程序 不执行
  const notWx = process.env.ANU_ENV !== 'wx' && process.env.ANU_ENV !== 'qq';
  const needAction = ['navigateTo', 'redirectTo', 'AppOnShow'].includes(action);

  if (!url || notWx || !needAction) return;

  /**
   * appjs 中过来的 url 是没有最前面的 '/' 的
   */
  const page = (action === 'AppOnShow' ? '/' : '') + url.split('?')[0];
  const query = (obj && obj.query && obj.query.url) || '';

  const triggerCollect = Api.getStorageSync('triggerCollectArr') || [];
  if (triggerCollect.includes(page)) {
    // 请求就好,不需要结果
    fetchDeviceId('collect', null, url, query, action, noLocation);
  }
}

/**
 *
 * @param {*} behavior 行为 click/show 等等
 * @param {*} extParams 后续可以额外扩展参数
 * {
 *    url: '',
 *    query: ''
 * }
 */
export function sendDeviceInfoWithBehavior(behavior, extParams = {}) {
  if (!behavior) {
    throw new Error('behavior 为必填字符串');
  }
  if (!extParams || (extParams && !extParams.url)) {
    throw new Error('第二个参数为必填项, object 类型,且必须包含 url');
  }
  const { url, query } = extParams;

  fetchDeviceId('collect', null, url, query, behavior);
}

/**
 * 对secretKey加密
 * @param {*} secretKey
 * @returns
 */
function culSecretKey(secretKey) {
  const keyChars = secretKey.split('');
  const midIndex = Math.floor(keyChars.length / 2);
  let rightIndex = midIndex;

  for (let left = 0; left < midIndex; left++) {
    const key1 = keyChars[left];
    const key2 = keyChars[rightIndex];
    keyChars[left] = key2;
    keyChars[rightIndex++] = key1;
  }

  return keyChars.join('');
}

const ENCRY_SECRET_KRY = culSecretKey(SECRET_KRY);

/**
 * json按字典排序
 * @param {*} obj
 * @returns
 */
function jsonSort(obj) {
  if (Object.prototype.toString.call(obj) !== '[object Object]') {
    return obj;
  }

  const sdic = Object.keys(obj).sort();
  const res = {};
  sdic.forEach(key => {
    const value = jsonSort(obj[key]);
    res[key] = value;
  });
  return res;
}

/**
 * 加密json字符串
 * @param {string} beforeKey
 * @returns
 */
function encryptValue(beforeKey) {
  if ((beforeKey.length & 1) == 1) {
    beforeKey = `${beforeKey}boyce`;
  }
  const b_key = beforeKey.split('');
  const beforeKeyLen = beforeKey.length;
  const midIndex = Math.floor(beforeKeyLen / 2);
  const r_key = new Array(beforeKeyLen);
  for (let i = 0; i < midIndex; ++i) {
    let temp = ((b_key[i].charCodeAt(0) ^ 0x1e21) % 100) + 27;
    if (temp - 32 <= 0) {
      temp += 32;
    }

    let temp2 =
      ((b_key[beforeKeyLen - 1 - i].charCodeAt(0) ^ 0x1ea2) % 100) + 27;

    if (temp2 - 32 <= 0) {
      temp2 += 32;
    }
    r_key[i] = String.fromCharCode(temp2);
    r_key[beforeKeyLen - 1 - i] = String.fromCharCode(temp);
  }

  return r_key.join('');
}

function hmacSHA1(str, key) {
  return CryptoJS.HmacSHA1(str, key).toString();
}

function getAppInfo() {
  let appId;
  let envVersion;
  let version;

  // 兼容ali低版本
  if (process.env.ANU_ENV === 'ali' && !Api.getAccountInfoSync) {
    appId = my.getAppIdSync().appId;
    envVersion = envVersionInAliLowVersion;
    version = getApp().globalData.pVersion;
  } else {
    const accountInfo = Api.getAccountInfoSync();
    ({ appId, envVersion, version } = accountInfo.miniProgram);
  }

  const { _q, _s } = util.getGlobalInfo().cookies;

  const {
    version: wxVer,
    model = '',
    platform = '',
    system = '',
    SDKVersion: sdkVersion,
  } = my.getSystemInfoSync();

  const { launchRandom } = getApp().globalData;

  return {
    appId,
    envVersion,
    version,
    openid: openId,
    jsCode: jscodeValue,
    _q,
    _s,
    wxVer,
    model,
    platform,
    osVer: system,
    launchRandom,
    relevanceId,
    sdkVersion: process.env.ANU_ENV === 'ali' ? Api.SDKVersion : sdkVersion,
  };
}

/**
 * 加密内容
 * @param {*} jsonSrting
 * @param {*} key
 * @returns
 */
function eor(jsonSrting, key) {
  const arr = jsonSrting.split('');
  for (let i = 0; i < arr.length; i++) {
    const value = arr[i].charCodeAt(0) ^ key;
    if (value != '\0') {
      arr[i] = String.fromCharCode(value);
    }
  }
  return arr.join('');
}

function realSign(params) {
  // 1、排序、加密业务入参
  let businessParams = jsonSort(params);
  if (Object.prototype.toString.call(businessParams) === '[object Object]') {
    businessParams = JSON.stringify(businessParams);
  }

  const bizParam = encryptValue(businessParams);

  // 2、时间戳+随机数
  const betime = `${new Date().getTime()}_${util.randomString(15)}`;

  // 3、sha1加密
  const becheck = hmacSHA1(
    JSON.stringify({
      bizParam,
      betime,
    }),
    ENCRY_SECRET_KRY,
  );

  // 4、eor、aes加密小程序数据
  const appInfo = getAppInfo();
  appInfo.becheck = becheck;
  appInfo.betime = betime;
  const eorAppInfo = eor(JSON.stringify(appInfo), 0x1a31);
  const bmagic = aesEncryp(eorAppInfo, SIGN_AES_KEY, SIGN_AES_IV);

  // 5、返回结果
  return {
    becheck,
    bmagic,
    betime,
  };
}

export function sign(params) {
  const startTime = new Date().getTime();

  const p1 = new Promise(resolve => {
    const res = realSign(params);
    resolve(res);
  });

  const p2 = new Promise(resolve => {
    setTimeout(() => {
      resolve({
        becheck: 'sign timeout',
        bmagic: 'SnFRQUlOQmpGNEdXUFNvZzJzZTN3dz09', // aesEncryp('sign timeout', SIGN_AES_KEY, SIGN_AES_IV)的结果
        betime: new Date().getTime(),
      });
    }, 1000);
  });

  return Promise.race([p1, p2]).then(result => {
    // const execTIme = new Date().getTime() - startTime;
    // QMark && QMark.log && QMark.log({
    //     'bizType': 'rdc',
    //     'page': 'collect',
    //     'module': 'default',
    //     'appcode': 'nnc_app_qunar_wx',
    //     'id': 'sign',
    //     'operType': 'monitor',
    //     'operTime': startTime,
    //     'time': execTIme,
    //     'ext': {},
    //     'key': 'rdc/collect/default/monitor/sign'
    // });
    return result;
  });
}
