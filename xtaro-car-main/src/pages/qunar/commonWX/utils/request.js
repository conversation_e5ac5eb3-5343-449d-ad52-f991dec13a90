import Requester from './requester/requester.js';
import util from './util';
// if process.env.ANU_ENV === 'wx||qq||ali'
import { sign } from './collect';
// import QMark from '../../npm/@qnpm/qmark/dist/qmark.mini.umd.js';

/**
 * 用户相关的key, 在 cookies 中
 */
const USER_RELATED_KEY = {'_q': 1,'_s':1,'_v': 1,'_t': 1,'HTTPOnly,_q':1,'HTTPOnly,_s':1,'HTTPOnly,_t':1, 'HTTPOnly,_v':1};
/**
 * 可以修改 UserData 中 cookies 的接口
 * bindThirdUser: _q 为空为正常业务逻辑.
 * 2023年05月30日 这部分逻辑暂时不上线，先注释掉
 */
// const ACCESS_CHANGE_USER_RELATED_KEY = ['/oauth-client/platform/bindThirdUser.jsp'];

const emptyFunc = () => { };

async function request(obj) {
    const extraCookie = await util.getGlobalInfoAsyncByKey('extraCookie');
    const initUserData = await util.getGlobalInfoAsync();
    if (process.env.ANU_ENV === 'wx' || process.env.ANU_ENV === 'qq' || process.env.ANU_ENV === 'ali') {
        await useSign(obj);
    }
    quickRequest(obj, initUserData, extraCookie);
}

function requestLog(opt) {
    // QMark && QMark.log && QMark.log(opt);
}

/**
 * 使用方法：request({ ... , useSign: true }), 需要添加签名的请求，添加 useSign 属性, 并且设置为 true
 * https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=569742052
 */
async function useSign(obj) {
    // 如果没有 useSign 属性，直接返回, 不做处理
    if (!Object.hasOwnProperty.call(obj, 'useSign')) return;

    // 如果 请求参数(obj.data) 不为对象，直接返回, 不做处理
    if (!(obj && obj.data && Object.prototype.toString.call(obj.data).slice(8, -1) === 'Object')) return;

    if (obj.useSign) {
        const result = await sign(obj.data);
        obj.data = Object.assign({}, obj.data, result);
        // 处理结束后删除 useSign 属性, 防止影响下游逻辑
        delete obj.useSign;
    }
}

// function setResponseCookieMark(res, initUserData, requestOpts) {
//     // 只在微信小程序中打点
//     if (process.env.ANU_ENV !== 'wx') return;

//     // 拿到 响应头的header
//     if (process.env.ANU_ENV === 'quick') {
//         res.header = res.headers;
//     }
//     if (!res.header) return;
//     const setCookie = res.header['Set-Cookie'] || res.header['set-cookie'];
//     let setCookieString = '';
//     if (!setCookie) return;
//     if (Array.isArray(setCookie)) {
//         setCookie.forEach(v => {
//             setCookieString = setCookieString + v + ';';
//         });
//         setCookieString = setCookieString.replace(/\s+/g, '');
//     } else if (typeof (setCookie) === 'string') {
//         setCookieString = setCookie.replace(/\s+/g, '');
//     }
//     const setCookieObj = util.queryToCookie(setCookieString);

//     const reqUrl = requestOpts.service;
//     const resCookieStr = JSON.stringify(setCookieObj);
//     // http://mark.corp.qunar.com/markpage/rdc/markpoint/1072/0
//     QMark && QMark.log && QMark.log({
//         ext: { reqUrl, cookies: resCookieStr },
//         bizType: 'rdc',
//         module: 'default',
//         appcode: 'nnc_app_qunar_wx',
//         page: 'cookies',
//         id: 'resCookies',
//         operType: 'req',
//         key: 'rdc/cookies/default/req/resCookies',
//         operTime: Date.now()
//     });

//     const oldSetCookieObj = initUserData.cookies || {};
//     for (let k in setCookieObj) {
//         oldSetCookieObj[k] = setCookieObj[k];
//     }
//     React.api.setStorage({
//         key: 'UserData',
//         data: initUserData
//     });
// }


// todo [关于extraCookie] 临时方案，后续使用 setResponseCookieMark --- START
// eslint-disable-next-line no-unused-vars
function setResponseCookid(res, initUserData, extraCookie, requestOpts) {
    // 拿到 响应头的header
    if (!res.header) return;
    const setCookie = res.header['Set-Cookie'] || res.header['set-cookie'];

    let setCookieString = '';
    if (!setCookie) return;
    if (Array.isArray(setCookie)) {
        setCookie.forEach(v => {
            setCookieString = setCookieString + v + ';'; // ; 为旧代码使用分隔符，, 为新代码使用分隔符
        });
        setCookieString = setCookieString.replace(/\s+/g, '');
    } else if (typeof (setCookie) === 'string') {
        setCookieString = setCookie.replace(/\s+/g, '');
    }

    const reqUrl = requestOpts.service;
    // 有 _q 的点
    if (setCookieString.includes('_q=')){
        requestLog({
            ext: {
                url: reqUrl,
                cookies: JSON.stringify(setCookie)
            },
            bizType: 'rdc',
            module: 'default',
            appcode: 'nnc_app_qunar_wx',
            page: 'cookies',
            id: 'repeat',
            operType: 'req',
            key: 'rdc/cookies/default/req/repeat',
            operTime: '*'
        });
    }
    
    // 只要有 set-cookie 的点
    requestLog({
        ext: {
            url: reqUrl,
            cookies: JSON.stringify(setCookie)
        },
        bizType: 'rdc',
        module: 'default',
        appcode: 'nnc_app_qunar_wx',
        page: 'cookies',
        id: 'newc',
        operType: 'show',
        key: 'rdc/cookies/default/show/newc',
        operTime: '*'
    });

    const setCookieObj = util.queryToCookie(setCookieString);

    /**
     * 存到 UserData 中: START
     * 对于 ACCESS_CHANGE_USER_RELATED_KEY 中的接口，需要
     * 将 cookie 信息中的 _q,_s,_v,_t 字段存储到 UserData 中
     * 2023年05月30日 这部分逻辑暂时不上线，先注释掉
     */
    // const reqUrl = requestOpts.service;
    // if (ACCESS_CHANGE_USER_RELATED_KEY.includes(reqUrl)) {
    //     initUserData.cookies = initUserData.cookies || {};
    //     for (let k in setCookieObj) {
    //         if (!USER_RELATED_KEY.includes(k)) continue;
    //         initUserData.cookies[k] = setCookieObj[k];
    //     }

    //     React.api.setStorage({
    //         key: 'UserData',
    //         data: initUserData
    //     });
    // }
    /**
     * 存到 UserData 中: END
     */

    const oldSetCookieObj = extraCookie || {};
    for (let k in setCookieObj) {
        oldSetCookieObj[k] = setCookieObj[k];
    }
    wx.setStorage({
        key: 'extraCookie',
        data: oldSetCookieObj
    });
}
// todo [关于extraCookie] 临时方案，后续使用 setResponseCookieMark --- END

// 针对快应用不支持同步拿缓存
function quickRequest(obj, initUserData, extraCookie) {
    const originalSuccessCb = obj.success || emptyFunc;
    const originalFailCb = obj.fail || emptyFunc;
    const originalCompleteCb = obj.complete || emptyFunc;
    obj.header || (obj.header = {});

    // cookie处理
    const cookie = [];
    for (const name in initUserData.cookies) {
        cookie.push(`${name}=${initUserData.cookies[name]}`);
    }

    // 增加一个合并 cookies 和 extraCookie 中间区分开来的标志 cookie
    // 用于协助处理故障问题，不会存在太久
    cookie.push('_dangerDivide' + '=' + 'true');


    for (const name in extraCookie) {
        /**
         * 对于请求携带的 cookie 信息中的 _q,_s,_v,_t 字段,
         * 需要在 extraCookie 中过滤掉
         */
        if (USER_RELATED_KEY[name]) continue;
        cookie.push(`${name}=${extraCookie[name]}`);
    }
    obj.header.cookie = cookie.join(';');

    // 封装回调函数
    // eslint-disable-next-line
    const finalFailCb = (res, isNetworkError = true) => {
        originalFailCb(res);
    };

    // 忽略Status直接执行success，否则不为0时走fail
    const finalSuccessCb = (res) => {
        // setResponseCookieMark(res, initUserData, obj);

        // todo [关于extraCookie] 临时方案，后续使用 setResponseCookieMark --- START
        setResponseCookid(res, initUserData, extraCookie, obj);
        // todo [关于extraCookie] 临时方案，后续使用 setResponseCookieMark --- END

        // const { data } = res;
        const data = res.data || {};
        // 兼容火车票代码 不解构直接返回所有数据
        if (obj.returnAll) {
            originalSuccessCb(res);
            return;
        }
        // ignoreStatus
        if (obj.ignoreStatus) {
            originalSuccessCb(data);
        } else {
            if (typeof data === 'string' || data.status === 0 || data.errcode === 0 || data.ret) {
                originalSuccessCb(data);
            } else {
                // 此参数表示返回所有response
                if (obj.responseAll) {
                    finalFailCb(res, false);
                } else {
                    finalFailCb(data, false);
                }
            }
        }
    };

    const finalCompleteCb = () => {
        originalCompleteCb();
    };

    obj.success = finalSuccessCb;
    if (process.env.ANU_ENV === 'wx') {
        obj.fail = err => {
            // 劫持访问到非微信域名白名单的接口，如果出现有人篡改白名单，会上报
            if (err.errMsg && err.errMsg.includes('url not in domain list')) {
                requestLog({
                    ext: {
                        host: obj.host,
                        service: obj.service,
                        data: JSON.stringify(obj.data),
                        method: obj.method
                    },
                    bizType: 'rdc',
                    module: 'default',
                    appcode: 'nnc_app_qunar_wx',
                    page: 'domain',
                    id: 'whitelist',
                    operType: 'show',
                    key: 'rdc/domain/default/show/whitelist',
                    operTime: '*'
                });
            }  
            finalFailCb(err);
        };
    } else {
        obj.fail = finalFailCb;
    }
    
    
    obj.complete = finalCompleteCb;
    // requestLog({
    //     ext: {
    //         openId: initUserData.cookies.openId,
    //         reqUrl: obj.service,
    //         cookies: obj.header.cookie
    //     },
    //     bizType: 'rdc',
    //     module: 'utils',
    //     appcode: 'nnc_app_qunar_wx',
    //     page: 'platform',
    //     id: 'request',
    //     operType: 'req',
    //     key: 'rdc/platform/utils/req/request',
    //     operTime: Date.now()
    // });
    Requester(obj, initUserData);
}
export default request;
