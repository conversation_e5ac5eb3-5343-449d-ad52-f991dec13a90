import channelAttribution from '../channelAttribution.js';
// import QMark from '../../../npm/@qnpm/qmark/dist/qmark.mini.umd.js';
import CommonUtils from '../util.js';

var URL_MAP = require('./url');

const miniRouterFn = ['reLaunch', 'redirectTo', 'navigateTo'];

const ENV = CommonUtils.getNameSpace();

if (ENV) { // 小程序
    miniRouterFn.forEach(function (hook) {
        const old = ENV[hook];
        // 不加react，wx.和React.api都生效。加了react，wx.生效。
        Object.defineProperty(ENV, hook, {
            value: function (params) {
                hookUrl(params, old, hook);
            },
            writable: true
        });

        // 兼容React.api的使用
        const oldTmp = wx[hook] || (() => { });
        wx[hook] = (obj) => {
            hookUrl(obj, oldTmp, hook);
        };

    });


}

function hookUrl(params, oldHook, action) {
    const urlTmp = params.url;
    hookFun(params, urlTmp, action);

    if (typeof params.url === 'string' && !params.url) {
        oldHook(params);
        return;
    }

    const completeURL = params.url;
    const index = completeURL && completeURL.indexOf('?');
    let url = '';
    let urlParams = '';

    // 判断 跳转 url 中是否带参数
    try {
        // console.log('==== test navigate hook ====');
        if (index != -1) {
            url = completeURL.slice(0, index);
            urlParams = completeURL.slice(index);
        } else {
            url = completeURL;
        }
    } catch (e) {
        // QMark.log({
        //     'ext': {
        //         completeURL,
        //         params,
        //     },
        //     'bizType': 'rdc',
        //     'module': 'default',
        //     'appcode': 'nnc_app_qunar_wx',
        //     'page': 'platform',
        //     'id': 'hookNaviFail',
        //     'operType': 'show',
        //     'key': 'rdc/platform/default/show/hookNaviFail',
        //     'operTime': '*'
        // });
        throw new Error(e);
    }

    // 含有参数就拼接, 否则正常跳转
    if (URL_MAP[url]) {
        params.url = urlParams ? `${URL_MAP[url]}${urlParams}` : `${URL_MAP[url]}`;
    }

    oldHook(params);
}

/**
 * hook结果
 * @param {*} obj
 * @param {*} url
 */
function hookFun(obj, url, action) {
    const complete = obj.complete;
    obj.complete = () => {
        complete && complete();

        // regCid上报。截取regCid，然后上报给服务端（这里不用obj.url，是因为直接调用wx.navigateTo后url会变，原因是其他位置也做了hook。所以直接用源url）
        channelAttribution.checkRegCidInChangePath(url);
    };

    const pages = getCurrentPages()
    let currentPages = pages[pages.length - 1];
    const route = currentPages ? currentPages.route : '';
    const options = currentPages ? currentPages.options : {};

    const fail = obj.fail;
    obj.fail = (err) => {

        fail && fail(err);

        /**
         * 埋点
         * 不等于说明在别的地方做了映射，且结果是成功的
         */
        if (obj.url !== url){
            return;
        }

        const query = Object.keys(options).map(key => {
            return `${key}=${options[key]}`;
        });
        const fromUrl = query.length > 0 ? route + '?' + query.join('&') : route;

        // QMark.log({
        //     'ext': {
        //         toUrl: url,
        //         fromUrl,
        //         action,
        //         platform: process.env.ANU_ENV
        //     },
        //     'bizType': 'rdc',
        //     'module': 'mini',
        //     'appcode': 'pf_nnc_trace',
        //     'page': 'trace',
        //     'id': 'toFail',
        //     'operType': 'show',
        //     'key': 'rdc/trace/mini/show/toFail',
        //     'operTime': '*'
        // });

    };
}
