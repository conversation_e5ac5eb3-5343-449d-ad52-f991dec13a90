import EventEmitter from './EventEmitter';
import util from './util';
// import user from './user';

// 需要业务线传source
let lock = false;
const login = async (loginSuccessCallback, exParams, jumpType = 'navigateTo') => {
    if (lock) return;
    lock = true;
    setTimeout(() => {lock = false;}, 1000);
    const params = util.stringifyURLParam(exParams);

    // 微信登陆成功直接成功回调
    const data = util.getGlobalInfo();
    if (process.env.ANU_ENV === 'wx' && data.cookies._q && data.cookies._q.length > 0 && typeof loginSuccessCallback === 'function'&& data.user.logOut!== true) {
        loginSuccessCallback.call(this);
        return;
    }

    if (jumpType) {
        wx[jumpType]({
            url: `/pages/qunar/subPages/alonePlatform/login/index?${params}`
        });
    } else {
        wx.navigateTo({
            url: `/pages/qunar/subPages/alonePlatform/login/index?${params}`
        });
    }

    EventEmitter.removeListener('loginSuccessCallback');
    if (typeof loginSuccessCallback === 'function') {
        EventEmitter.addListener('loginSuccessCallback', loginSuccessCallback);
    }
};

export default login;
