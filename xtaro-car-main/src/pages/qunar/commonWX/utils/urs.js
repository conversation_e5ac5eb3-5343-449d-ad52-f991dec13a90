import util from './util';
import QMark from '../../npm/@qnpm/qmark/dist/qmark.mini.umd.js';
import request from './request';
import mqtt from './subscribe/mqtt';
import { publisher } from './subscribe/subscribeService';


let nanoid = (size = 21) => {
    let id = '';
    let i = size;

    while (i--) {
        id += 'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'[Math.random() * 64 | 0];
    }

    return id;
};
// urs 通道连接地址
const CHECK_CONN_HOST = process.env.BUILD_ENV === 'beta' ? 'http://in-starlord.fd-280481-market-urouter-jizhun.inner3.beta.qunar.com' : 'https://wxapp.qunar.com';
// urs 通道连接地址
const URS_URL = process.env.BUILD_ENV === 'beta' ? 'wx://tc-emqx-mqtt-server-wss.beta.qunar.com:80/mqtt' : 'wxs://ws-qmqtt.qunar.com/mqtt';
// 订阅的topic前缀
const SUBSCRIBE_TOPIC_PRE = 'topic/common/strategy/';
// 上行的topic名称
const PUBLISH_TOPIC_NAME = 'topic/common/action/global';
// 检查ping的topic
const CHECK_PING_TOPIC_NAME = 'ping';

// 各页面订阅的主题
const URS_SUBSCRIBE_THEME = 'URS';

// 消息队列相关
const MESSAGELIST = Object.create(null);
const IDMAPPING = {};
const PUBLISH_NUM = 5;
let client = null;
let openId = '';
let openIdTimer = null;
let TopicTimer = null;
let isConnectSuccess = false;
let id = 0;
let isLoopStart = false;
/**
 * 总开关。默认是开
 */
let mainSwitch = true;
/**
 * 网络状态,默认连接
 */
let networkIsConnect = true;
/**
 * 存储消息的最大条数
 */
let MSG_MAX_NUM = 120;

// 用户行为
const USER_ACTION = {
    C_APP_START: 'C_APP_START',
    C_APP_PAUSE: 'C_APP_PAUSE',
    C_APP_RESUME: 'C_APP_RESUME',
    C_PAGE_ENTER: 'C_PAGE_ENTER',
};

// 各个阶段的状态
const Status = {
    // 执行urs程序启动
    URS_START: 'urs_start',
    // 获取openid成功
    URS_GET_OPENID_SUCCSSS: 'urs_get_openid_succsss',
    // 开始建连
    URS_START_CON: 'urs_start_con',
    // 建连成功，包含初始化连接和断连后重接
    URS_CON_SUCCESS: 'urs_con_success',
    // 连接出错
    URS_CON_ERROR: 'urs_con_error',
    // 连接关闭
    URS_CON_CLOSE: 'urs_con_close',
    // 连接断开
    URS_CON_DISCONNECT: 'urs_con_disconnect',
    // 连接断开
    URS_CON_OFFLINE: 'urs_con_offline',
    // 在app onshow时重新连接
    URS_CON_RECONN_IN_APP_SHOW: 'urs_con_reconn_in_app_show',
    // 上行消息
    URS_PUBLISH: 'urs_publish',
    // 上行消息成功。后端会返回是否成功
    URS_PUBLISH_SUCCESS: 'urs_publish_success',
    // 上行消息重新发送。
    URS_PUBLISH_RESEND: 'urs_publish_resend',
    // 订阅成功
    URS_SUBSCRIBE_SUCCESS: 'urs_subscribe_success',
    // 订阅消息失败
    URS_SUBSCRIBE_FAIL: 'urs_subscribe_fail',
    // 接收到所有消息
    URS_RECEIVED_ALL: 'urs_received_all',
    // 接收到topic为空的消息
    URS_RECEIVED_EMPTY: 'urs_received_empty',
    // 接收到urs消息
    URS_RECEIVED_URS: 'urs_received_urs',
    // 接收urs消息-biz:urs
    URS_RECEIVED_URS_URS: 'urs_received_urs_urs',
    // 接收urs消息-biz:ack
    URS_RECEIVED_URS_ACK: 'urs_received_urs_ack',
    // 接收urs消息广播到页面
    URS_RECEIVED_URS_URS_PUBLISH_TO_PAGE: 'urs_received_urs_publish_to_page',

};

/**
 * 检查连接状态
 * @returns 
 */
function checkPin() {
    return new Promise((resolve) => {
        client.publish(CHECK_PING_TOPIC_NAME, '0', null, () => {
            resolve(true);
        });
    });
}

async function reconnInAppShow() {
    if (mainSwitch && client && client.connected && !client.reconnecting) {
        
        const isConn = await Promise.race([
            checkPin(),
            new Promise(resolve => setTimeout(() => resolve(false), 1000))
        ]);
        if (!isConn) {
            client.reconnect();
        }

        log(Status.URS_CON_RECONN_IN_APP_SHOW);
    }
}

function getClientOptions() {
    return {
        keepalive: 30,
        clientId: `bigim|mini|${openId}`,
        protocolId: 'MQTT',
        protocolVersion: 5,
        clean: false,
        reconnectPeriod: 1000,
        connectTimeout: 4000,
        properties: {
            sessionExpiryInterval: 900,
            receiveMaximum: 1024,
            topicAliasMaximum: 0,
        },
        username: openId,
        password: openId,
    };
}

// 建联
async function connectUrsMqtt() {

    log(Status.URS_START);

    // 获取非空的openid
    openId = await getNotEmptyOpenId();
    log(Status.URS_GET_OPENID_SUCCSSS);

    // 和后端发起请求，判断是否要和通道连接
    const connMqtt = await checkIsConnMqtt();
    if (!connMqtt) {
        mainSwitch = false;
        return;
    }

    // 通道连接
    log(Status.URS_START_CON);
    const options = getClientOptions();
    client = mqtt.connect(URS_URL, options);

    // 成功连接
    client.on('connect', () => {
        log(Status.URS_CON_SUCCESS);
        isConnectSuccess = true;

        subscribeTopic();
    });

    // 接收后端发送数据
    client.on('message', onMessage);

    // 出错
    client.on('error', function () {
        log(Status.URS_CON_ERROR);
    });

    // 关闭
    client.on('close', function () {
        log(Status.URS_CON_CLOSE);
    });

    client.on('disconnect', function () {
        isConnectSuccess = false;
        log(Status.URS_CON_DISCONNECT);
        // 在这里可以进行重连操作
        if (!client.reconnecting) {
            const res = client.reconnect();
            console.log('res', res);
        }

    });

    // 断连
    client.on('offline', function () {
        isConnectSuccess = false;
        log(Status.URS_CON_OFFLINE);
    });

    // 监听网络切换，如果为连接状态，则重连
    wx.onNetworkStatusChange(function (res) {
        if (res.isConnected) {
            if (!networkIsConnect && client) {
                client.reconnect();
                networkIsConnect = true;
            }
        } else {
            networkIsConnect = false;
        }
    });

    
}

// URS相关回调函数
let URSCALLBACK = Object.create(null);
URSCALLBACK.REQUIRE_CPARAM = function () {
    publishMessage({
        code: 'C_UPLOAD_CPARAM',
        timestamp: Date.now(),
        data: util.getSimpleCParam()
    });
};
URSCALLBACK.SHOW_SURVEY = function (mid, payload) {
    log(Status.URS_RECEIVED_URS_URS_PUBLISH_TO_PAGE, { mid });
    publisher.publishOneSubscriber(URS_SUBSCRIBE_THEME, payload.pageId, payload);
};

// message处理函数
function onMessage (topic, message, packet) {
    log(Status.URS_RECEIVED_ALL);
    // debug(topic, message, packet);

    if (topic === ''){
        log(Status.URS_RECEIVED_EMPTY);
    }
    // if (topic != `${SUBSCRIBE_TOPIC_PRE}${Urs.openId}`) {
    //     return;
    // }

    const userProperties = packet.properties.userProperties;
    const { biz, mid } = userProperties;
    log(Status.URS_RECEIVED_URS, { mid });

    if (biz === 'urs') {// 要发送给业务端的数据
        log(Status.URS_RECEIVED_URS_URS, { mid });

        // 把UserProperty中的biz，加到Payload中。
        let payload = JSON.parse(message.toString() || '{}');
        payload.bizcode = biz;

        let callback = URSCALLBACK[payload.code];
        if (callback) {
            callback(mid, payload);
        }
    } else if (biz === 'ack') {// 上行数据成功后的结果
        destroyMsg(mid);
        log(Status.URS_RECEIVED_URS_ACK, { mid });
    }
}

// 订阅Topic
function subscribeTopic() {
    // 订阅后端发送的消息
    client.subscribe(`${SUBSCRIBE_TOPIC_PRE}${openId}`, {
        qos: 1
    }, (err) => {
        if (err) {
            log(Status.URS_SUBSCRIBE_FAIL);
            // 订阅失败后，轮询5s后继续订阅
            TopicTimer = setTimeout(() => {
                subscribeTopic();
            }, 5000);
            return;
        } else {
            clearTimeout(TopicTimer);
            log(Status.URS_SUBSCRIBE_SUCCESS);
        }

        // 判断连接前是否有消息存储，如果有，先发送这些。
        // 避免订阅行为多次触发时，导致loop多个实例运行
        if (!isLoopStart) {
            isLoopStart = true;
            loop();
        }
    });
}

// 主动清空消息
function sendMessages() {
    let msgs = Object.keys(MESSAGELIST);
    let l = msgs.length;
    
    for (let i = 0; i < l; i += PUBLISH_NUM) {
        const payloads = [];
        // 获取当前批次的数据
        let Ids = msgs.slice(i, i + PUBLISH_NUM);
        Ids.forEach(i => {
            payloads.push(MESSAGELIST[i]);
        });
        // 发送当前批次的数据
        publishMessageToServer(payloads, Ids);
    }
}

// 对外暴露的发布消息
function publishMessage(userParams) {
    // 如果总开关关闭，后续收数没意义了，暂停收数。
    if (!mainSwitch) {
        return;
    }

    // 处理消息
    id = id + 1;
    const payload = buildPayload(userParams, id);
    MESSAGELIST[id] = payload;
    
    // 限制长度
    util.limitObjectLength(MESSAGELIST, MSG_MAX_NUM);

    // 未建联时不发送
    if (!client || !isConnectSuccess) return;
    publishMessageToServer(payload, id);
}


function loop() {
    let l = Object.keys(MESSAGELIST).length;
    if (l) { sendMessages(); }
    setTimeout(loop, 20 * 1000);
}

// 真正发布消息给后端
function publishMessageToServer(payload, id) {
    // id是number表示是单条消息
    const isSingle = typeof(id) === 'number';
    const mid = nanoid();
    const Ids = Object.keys(MESSAGELIST);
    IDMAPPING[mid] = isSingle ? [id.toString()] : [];
    var p = isSingle ? [payload]: payload;
    
    // 如果消息队列中有消息，而发布的消息没到上限，会携带消息上传
    // @todo 短时间触发多条消息， 后端未及时ack会重复上传； 使用setTimeout，收集单次循环中的话，又不够时事了
    if (Ids.length > 1 && isSingle) {
        Ids.slice(0, PUBLISH_NUM - 1).forEach(i => {
            // 去重。不能用三等，i是string类型，payload.increaseId是number类型
            if (i != payload.increaseId) {
                p.unshift(MESSAGELIST[i]);
                IDMAPPING[mid].unshift(i);
            }
        });
    } else if (Array.isArray(id)) {
        // 如果是清消息的情况，则id是数组，直接使用id作为mid的数据
        IDMAPPING[mid] = id;
    }
    p = p.map(item => {
        if (item.code === USER_ACTION.C_APP_START){
            item.data = util.getSimpleCParam();
        }
        return item;
    });
    let msg = JSON.stringify(p);
    client.publish(PUBLISH_TOPIC_NAME, msg, {
        properties: {
            userProperties: {
                biz: 'ue', // bizcode 业务属性，上传指定
                cid: openId, // 设备id，客户端/小程序指定，对应设备uid，openId
                mid, // 消息id，客户端/小程序生成，生成的uuid算法，随机串保证唯一
                plat: 'mini', // 平台
                ts: Date.now() // 时间戳
            }
        }
    });
    log(Status.URS_PUBLISH, {
        mid
    });
}

// 构建消息体
function buildPayload(params, id) {
    return {
        code: params.code,    // 上行业务code
        increaseId: id, // 小程序自增id
        pageId: params.page,    // 上行页面id
        timestamp: params.timestamp,  // 数据收集的时间戳
        data: params.data
    };
}

// c参构建 直接使用util.getSimpleCParam()


// 销毁消息
function destroyMsg(mid) {
    let rawIds = IDMAPPING[mid];
    if (rawIds) {
        /**
         * IDMAPPING 示例：{ a: [1], b: [1, 2], c: [1, 2, 3], d: [3, 5] }
         * 如果此刻 mid是c，那么删除a b c,最终结果保留d(d中包含c不存在的值)
         */
        const ids = IDMAPPING[mid];
        delete IDMAPPING[mid];
        for (const key in IDMAPPING) {
            if (IDMAPPING.hasOwnProperty(key)) {
                const values = IDMAPPING[key];
                // 检查当前值是否包含要删除的值
                const shouldDelete = values.every(value => ids.includes(value));
                if (shouldDelete) {
                    delete IDMAPPING[key];
                }
            }
        }
        rawIds.forEach(id => delete MESSAGELIST[id]);
    }
}

/**
 * 埋点
 * http://mark.corp.qunar.com/markpage/rdc/markpoint/4494/0
 * @param {*} status 
 */
const log = (status, data) => {
    const params = {
        'ext': {
            status,
            mid: '',
            ...data
        },
        'bizType': 'rdc',
        'module': 'mini',
        'appcode': 'nnc_app_qunar_wx_deal',
        'page': 'urs',
        'id': 'urs_status',
        'operType': 'show',
        'key': 'rdc/urs/mini/show/urs_status',
        'operTime': '*'
    };
    QMark.log(params);
};

/**
 * 轮询获取 openId
 * @param {*} resolve 
 */
const getOpenIdInCycly = (resolve) => {
    const { user } = util.getGlobalInfo();
    if (user.openId) {
        clearTimeout(openIdTimer);
        resolve(user.openId);
    } else {
        openIdTimer = setTimeout(() => {
            getOpenIdInCycly(resolve);
        }, 5000);
    }
};

/**
 * 获取非空的 openId
 * @returns 
 */
const getNotEmptyOpenId = () => {
    return new Promise((resolve) => {
        getOpenIdInCycly(resolve);
    });
};

/**
 * 发请求，根据后端返回结果检查是否连接mqtt
 */
const checkIsConnMqtt = () => {
    return new Promise(resolve => {
        request({
            service: '/starlord/wxHomePage/switch/long/connection',
            host: CHECK_CONN_HOST,
            success: res => {
                let { status, data, } = res || {};
                if (status && status !== 0) {
                    resolve(false);
                    return;
                }

                let { result } = data || {};
                resolve(result === 1);// result：1（开始）、0（关闭）
            },
            fail: () => {
                resolve(false);
            }
        });
    });

};

export {
    USER_ACTION,
    URS_SUBSCRIBE_THEME,
    connectUrsMqtt,
    publishMessage,
    reconnInAppShow,
};
