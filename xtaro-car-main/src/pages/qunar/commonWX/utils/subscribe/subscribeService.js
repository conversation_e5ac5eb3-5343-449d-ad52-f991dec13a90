//发布者类
class Publisher {
    constructor() {
        this.subscribers = new Map(); // 存储主题及其对应的订阅者
    }
    //添加 指定类型的订阅者
    subscribe(theme, subs) {
        // 如果主题尚未注册，初始化一个空数组
        if (!this.subscribers.has(theme)) {
            this.subscribers.set(theme, []);
        }
        // 将订阅者添加到订阅者数组
        this.subscribers.get(theme).push(subs);
    }
    //通知所有订阅者
    publish(theme, data) {
        // 获取该主题的所有订阅者
        const subscribers = this.subscribers.get(theme);
        if (subscribers) {
            // 遍历并执行所有订阅者的回调函数
            subscribers.forEach(subs => {
                subs.callback(data);
            });
        }
    }
    //通知某一个订阅者
    publishOneSubscriber(theme, subsName, data) {
        // 获取该主题的一个订阅者
        const subscribers = this.subscribers.get(theme);
        if (subscribers) {
            // 找到订阅者的回调函数
            const sub = subscribers.find(subs => subs.name === subsName);
            sub && sub.callback(data);
        }
    }
    //移除订阅者中指定业务
    unsubscribe(theme, subsName) {
        // 获取该主题的订阅者数组
        const subscribers = this.subscribers.get(theme);
        if (subscribers) {
            // 找到对应的订阅者并从数组中移除
            const index = subscribers.findIndex(subs => subs.name === subsName);
            if (index > -1) {
                subscribers.splice(index, 1);
            }
        }
    }
}

//订阅者类
class Subscriber {
    constructor(name, cb) {
        this.name = name; //订阅者id
        this.callback = cb; //订阅者函数
    }
}

// 创建一个发布者
const publisher = new Publisher();

export {
    publisher,
    Subscriber,
};