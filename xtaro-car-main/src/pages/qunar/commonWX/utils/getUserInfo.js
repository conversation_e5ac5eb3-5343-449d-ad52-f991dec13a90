let userInfoCache = null; 

if (process.env.ANU_ENV === 'wx' && wx && wx.canIUse('getUserProfile')){
    const hookUserInfo = function(config){
        if (userInfoCache){
            config.success && config.success(userInfoCache);        
        } else {
            wx.getUserProfile({
                desc: '获取用户信息',
                ...config,
                success: resProfile => {
                    userInfoCache = resProfile;
                    config.success && config.success(resProfile);
                },
                fail: errProfile => {
                    config.fail && config.fail(errProfile);
                }
            });
        }
    };
    
    wx.getUserInfo = hookUserInfo;
    Object.defineProperty(wx, 'getUserInfo', {
        value: hookUserInfo,
        writable:true
    });
}

