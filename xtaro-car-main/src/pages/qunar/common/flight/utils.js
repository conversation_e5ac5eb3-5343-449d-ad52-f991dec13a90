import utils from '../utils/util';
import Hook<PERSON><PERSON> from '../utils/hookApi';

// parse上一页传入的参数，入参为 props.query
function parsePageQuery (query) {
    if (query && query.data) {
        return utils.json_parse(decodeURIComponent(query.data)) || {};
    } else {
        return {};
    }
}

// 首页跳转开关
function getEntranceQconfig () {
    return new Promise((resolve, reject) => {
        HookApi.request({
            url: 'https://m.flight.qunar.com/flight/api/getMiniQconfig',
            method: 'GET',
            success: (data) => {
                resolve(data);
            },
            fail: () => {
                resolve({
                    data: {
                        ret: false
                    }
                });
            }
        });
    }
    );
}

async function getBuId () {
    return new Promise(resolve => {
        resolve('');
    });
}

export {
    parsePageQuery,
    getEntranceQconfig,
    getBuId
};
