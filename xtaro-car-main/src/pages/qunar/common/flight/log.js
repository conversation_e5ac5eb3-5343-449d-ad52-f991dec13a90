import { getBuId } from './utils';
import utils from '../utils/util';
import Hook<PERSON><PERSON> from '../utils/hookApi';

async function log (params) {
    const globalData = getApp().globalData;
    const { cookies } = await utils.getGlobalInfoAsync();
    // 百度小程序使用bu的swanid标识用户
    let swanid = await getBuId();

    if (!params || !params.r) {
        return;
    }
    // globalData.sending = true;
    const queryComponents = [];
    for (const key in params) {
        const value = params[key] || '';
        queryComponents.push(key + '=' + encodeURIComponent(value));
    }

    const et = globalData.et || new Date().getTime(); // 进入时间
    globalData.et = et;

    params.p || queryComponents.push('p=' + 'wechat_home'); // 没定义发送到首页埋点
    queryComponents.push('at=' + new Date().getTime()); // 发送时间
    queryComponents.push('et=' + et);
    queryComponents.push('bd_source=' + cookies.bd_source);
    queryComponents.push('bd_origin=' + globalData.bd_origin);
    try {
        HookApi.request({
            url: 'https://log.flight.qunar.com/l?' + queryComponents.join('&'),
            header: {
                cookie: getCookies(swanid, cookies, globalData)
            },
            method: 'POST',
            fail: () => {},
            complete: () => {
                // globalData.isSending = false
            }
        });
    } catch (e) {

    }
}

function getCookies(swanid, cookies, globalData) {
    const cookieArr = ['QN601=' + swanid, 'bd_origin=' + globalData.bd_origin];
    for (let name in cookies) {
        if (name !== 'QN601') {
            cookieArr.push(`${name}=${cookies[name]}`);
        }
    }
    return cookieArr.join(';');
}

export default log;
