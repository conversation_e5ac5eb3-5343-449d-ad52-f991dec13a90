import request from './request.js';
import util from './util.js';
import LogQmark from './logQmark.js';
var location = null;
var wxLocationFail = false;

function _location(opt) {

    my.getLocation({
        success: function (res) {
            location = {};
            location.latitude = res.latitude;
            location.longitude = res.longitude;
            // location.speed = res.speed;
            // location.accuracy = res.accuracy;
            var _param = {
                latitude: res.latitude,
                longitude: res.longitude, // 用户定位地址
                coordConvert: 0 // 0-gps，1-google，2-baidu
            };
            // console.log('request', request.default());
            request({
                service: '/h_gateway',
                ignoreStatus: true,
                host: 'https://wxapp.qunar.com',
                header: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                method: 'POST',
                data: {
                    b: JSON.stringify(_param),
                    c: JSON.stringify(util.getCParam('h_hotelLocation')),
                    qrt: 'h_hotelLocation',
                    source: 'mini'
                },
                success: function (data = {}) {

                    try {
                        if (!(data instanceof Object) || (data instanceof Array)) {
                            LogQmark({
                                module: 'default',
                                appcode: 'nnc_module_qunar_platform',
                                page: 'commonLocation',
                                id: 'wx_common_location_request_error',
                                ext: {
                                    errormsg: 'data is not object',
                                    errordata: JSON.stringify(data)
                                }
                            });
                            data = {}
                        }
                        var adr = data && data.data ? data.data || data.data.data : {};
                        var addrDetail = adr.addrDetail || {};
                        location.address = adr && adr.address;
                        location.province = addrDetail && addrDetail.province;
                        location.city = addrDetail && addrDetail.city;
                        location.district = addrDetail && addrDetail.distinct;
                        location.street = addrDetail && addrDetail.street;
                        location.streetNumber = addrDetail && addrDetail.streetNumber;
                        location.cityUrl = addrDetail && addrDetail.parentCityUrl || addrDetail.cityUrl;
                        location.timestamp = Date.now();
                        location.realCityName = addrDetail && addrDetail.cityName;
                        location.realCityUrl = addrDetail && addrDetail.cityUrl;
                        opt.success && opt.success(location);

                    } catch (error) {
                        LogQmark({
                            module: 'default',
                            appcode: 'nnc_module_qunar_platform',
                            page: 'commonLocation',
                            id: 'wx_common_location_request_error',
                            ext: {
                                errormsg: error.stack,
                                errordata: JSON.stringify(data)
                            }
                        });
                        opt.fail && opt.fail();
                    }

                },
                fail: function (error) {
                    location = null;
                    opt.fail && opt.fail();
                    LogQmark({
                        module: 'default',
                        appcode: 'nnc_module_qunar_platform',
                        page: 'commonLocation',
                        id: 'wx_common_location_request_error',
                        ext: {
                            errormsg: error.stack
                        }
                    });
                },
                complete: function () {
                    opt.complete && opt.complete();
                }
            });
        },
        fail: function () {
            location = null;
            wxLocationFail = true;
            opt.fail && opt.fail();
        },
        complete: function () {
            wxLocationFail && opt.complete && opt.complete();
            wxLocationFail = false;
        }
    });
}

function getLocation(opt) {
    opt = opt || {};
    var expired = opt.expired === undefined ? 10 * 60 * 1000 : opt.expired; // 默认缓存有效期 10 分钟
    var timestamp = location && location.timestamp ? location.timestamp : 0; // location 获取时间
    var refresh = Date.now() - timestamp >= expired ? true : false;
    var sync = !!opt.sync;
    if (sync) {
        // 同步获取，有缓存直接返回，否则返回 null
        var res = refresh ? null : location;
        opt.success && opt.success(res);
        opt.complete && opt.complete();
    } else {
        if (refresh) {
            _location(opt);
        } else {
            if (location) {
                opt.success && opt.success(location);
                opt.complete && opt.complete();
            } else {
                _location(opt);
            }
        }
    }
}

export default getLocation;
