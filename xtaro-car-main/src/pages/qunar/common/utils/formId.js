/* 
* 第一个传事件对象e 第二个传origin。
*/
import util from './util';
import request from './request.js';
const source = '/wechatSmall/saveFormId.do';
const buSource = '/starlord/baidu/event';

function initFormIdConfig(e, origin, success, fail, complete) {
    const env = process.env.ANU_ENV;
    if (env !== 'wx' && env !== 'bu') return;
    origin = origin || '';
    const { formId } = e.detail;
    const storage = util.getGlobalInfo();
    const globalData = getApp().globalData;
    let data = {};
    let method = 'GET';
    let header = {};
    if (env === 'wx') {
        data = {
            formId,
            origin,
            wxUnionId: storage.cookies.unionId,
            wxOpenId: storage.cookies.openId,
            bd_origin: util.bdOrigin.getV(),
            version: globalData.pVersion,
            device: globalData.systemInfo?.model,
            platform: globalData.systemInfo?.platform
        };
    } else if (env === 'bu') {
        data = {
            formId,
            origin,
            biz: 'all'
        };
        method = 'POST';
        header = {
            'content-type': 'application/x-www-form-urlencoded'
        };
    }
    request({
        service: env === 'wx' ? source : buSource,
        method,
        header,
        data,
        success(res) {
            if (res && res.data === true) {
                success && success();
            }
            // eslint-disable-next-line
            console.warn(`Save FormId ${res.errmsg || res.message}`);
        },
        fail() {
            fail && fail();
        },
        complete() {
            complete && complete();
        }
    });
}

export default initFormIdConfig;
