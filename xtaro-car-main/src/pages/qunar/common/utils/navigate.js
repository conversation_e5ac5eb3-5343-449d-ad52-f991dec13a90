// 重新封装路由 方便记录埋点
import Log from './log';
import util from './util';

function autoLog(obj) {
    const globalData = getApp().globalData;
    let logs = globalData.logs || (globalData.logs = []);
    logs.push(obj);
    if (logs.length > 20) {
        var uploadLogs = logs.splice(0, 10);//截取前十条；
        Log(uploadLogs, 'ue', true);
    }
}

Array('navigateTo', 'redirectTo', 'reLaunch', 'navigateBack', 'switchTab').forEach(function (hook) {
    var oldHook = my[hook];
    my[hook] = function (a) {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const path = currentPage ? currentPage.route : '';
        const query = currentPage ? JSON.stringify(currentPage.options) : {};
        
        // 防止无限循环
        let isFirst = true;
        try {
            oldHook(Object.assign(a, {
                fail: () => {
                    const fixFunc = () => {
                        a.url = util.fixWxFailUrl(a.url);
                        isFirst = false;
                        oldHook(a);
                    };

                    // 如果这个路径能找到，就不用替换了
                    if (a && a.url && isFirst) {

                        const targetUrl = a.url;
                        if (!targetUrl) {
                            return;
                        }

                        let targetPage = targetUrl.split('?')[0];
                        let first = targetUrl.indexOf('/') === 0 ? 1 : 0;
                        if (!targetPage) {
                            return;
                        }

                        targetPage = targetPage.slice(first);
                        if (targetPage.startsWith('pages/train') || targetPage.startsWith('pages/vice_train') ||
                            targetPage.startsWith('pages/auxiliary_train') || targetPage.startsWith('pages/functional_train')) {
                            fixFunc();
                        }
                    }
                }
            }));
        } catch (err) {
            oldHook(a);
        }
        autoLog({
            action: 'nav',
            path,
            query,
            time: +new Date(),
            type: hook,
            to: hook === 'navigateBack' ? '' : a.url,
        });
    };
});
