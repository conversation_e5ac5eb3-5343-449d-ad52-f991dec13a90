/**
 * 20181011 简化版本 直接调用娜娜奇的request
 * 别引这个，引上层的request.js
 *
 * requester.request({
 *      // 以下三个字段会拼接成 url
 *      host: "https://wxapp.qunar.com"
 * 	    service: requester.service.SEARCH, // 接口名：'/train/product/h5/train/TrainDetail' 必须
 *      param: { // 请求参数，拼在 url 后面 可省略
 *          searchType: 'stasta',
 *          startStation: param.startStation,
 *          endStation: param.endStation,
 *          date: param.date
 *      },
 *
 *      data: {}, // 可省略
 *      header: {}, // 可省略
 *      method: 'POST', // 默认：'GET' 可省略
 *      dataType: 'json', // 默认为 json。如果设置了 dataType 为 json，则会尝试对响应的数据做一次 JSON.parse 可省略
 *      success: function() { }, // 可省略
 *      fail: function() { }, // 可省略
 *      complete: function() { }, // 可省略
 *  });
 *
 * 注：c 参，默认会拼在 url 后面，如果需要放在其他地方，可通过 requester.getParamC() 获取 c 参，自行添加。
 *
 */

import Config from '../config/config.js';
import RequestManager from './requestManager.js';
import utils from '../util.js';
// import util from '../util';

function request(obj, initUserData) {
    const service = obj.service;
    const globalData = getApp().globalData;
    let param = obj.param || {};

    if (!service || !service.length) {
        // console.error('service 为空');
        return false;
    }

    let queryComponents = [];
    // 加入请求里的param
    for (let key in param) {
        let value = param[key] || '';
        queryComponents.push(key + '=' + encodeURIComponent(value));
    }
    // 加入缓存里的cookies
    // 微信不需要加QunarGlobal csrfToken
    let { cookies, user } = initUserData;

    cookies.bd_source = 'ali';

    for (let key in cookies) {
        let value = cookies[key] || '';
        if (key !== 'QunarGlobal' && key !== 'csrfToken' && key !== '_i'){
            if (key === 'token') {
                if (obj && obj.needUrlToken) {
                    queryComponents.push(key + '=' + encodeURIComponent(value));
                    delete obj.needUrlToken;
                }
            } else {
                queryComponents.push(key + '=' + encodeURIComponent(value));
            }
        }
    }
    // 加一些埋点数据
    if (globalData.bd_origin && process.env.ANU_ENV !== 'wx') queryComponents.push(`bd_origin=${globalData.bd_origin}`);
    if (globalData.hd_origin) queryComponents.push(`hd_origin=${globalData.hd_origin}`);
    
    
    // todo 小程序登录bug 去掉smallLogin url上的token参数
    // if (obj.notToken && queryComponents.length) {
    //     delete obj.notToken;
    //     const tokenIndex = queryComponents.findIndex(item => item.startsWith('token='));
    //     if (tokenIndex >= 0) {
    //         queryComponents.splice(tokenIndex, 1);
    //     }
    // }
    // todo config配置 暂时没有 先写个微信的beta,这个到时候在
    let url = (obj.host ? obj.host : Config.settings.requestDomain) + service + '?' + queryComponents.join('&');
    // 处理某些业务线需要在header中传参数的情况
    let header = obj.header || {'Content-Type': 'application/json'};
    // TODO: 字段目前传入是overtime，数字类型的,后续评估一下透传的影响范围
    let timeoutTime = 30000
    let timeout = typeof obj.overtime === 'number' ? {timeout: obj.overtime || timeoutTime} : {}
    let req = {
        url: url,
        data: obj.data,
        ...timeout,
        header,
        headers: header,
        responseType: obj.responseType,
        method: (obj.method && obj.method.toUpperCase()) || 'GET',
        dataType: obj.dataType || 'json',
        success: obj.success,
        fail: obj.fail,
        complete: obj.complete
    };
    RequestManager.request(req);

    return true;
}

export default request;
