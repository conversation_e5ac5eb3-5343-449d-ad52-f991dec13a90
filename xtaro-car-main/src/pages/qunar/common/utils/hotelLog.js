
import Log from './log';
import watcher from './watcher';
import util from './util';
function hotelLog(path, query) {
    if (query.bd_origin && query.bd_origin.length > 0) {
        util.bdOrigin.setV(query.bd_origin);
    }
    Log({
        action: 'pv',
        path: path,
        query,
        time: +new Date(),
    }, 'ue', true);
    Log({
        name: 'hotelLogPv',
        path,
        query
    });
    watcher.watchCount(`page-load-hotel-${path}`);
}

export default hotelLog;