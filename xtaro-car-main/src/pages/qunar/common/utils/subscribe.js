import request from './request';
import utils from './util';
import watcher from './watcher';
// import QMark from '../../npm/@qnpm/qmark/dist/qmark.mini.umd.js';
import HookA<PERSON> from './hookApi';

/**
 * 错误码：
 * -3001没找到对应模板
 * 其他是系统错误
 */
const requestSubscribeMessage = async function (data, successCb, failCb, beforeSubscribeCb, openLoading = true) {
    const env = process.env.ANU_ENV;
    if (env !== 'wx') return;
    const list = data.data && data.data.templateInfoList;
    const scene = data.data && data.data.scene;
    const activityId = data && data.activityId;
    if (!data.ret || list.length === 0) {
        failCb && failCb.call(this, {
            ret: false,
            data: data.data || {},
            message: '授权列表为空',
            status: 1001
        });
        return;
    }
    const res = await wxGetList(list, scene, beforeSubscribeCb);
    if (res.status === 20004) {
        HookApi.showToast({ icon: 'none', title: '为避免遗漏订单消息提醒，请设置接收订阅消息' });
    }
    if (!res.ret) {
        failCb && failCb.call(this, res);
        return;
    }
    openLoading && HookApi.showLoading();
    const { openId } = utils.getGlobalInfo().cookies;
    const authRes = await promiseRequest({
        service: '/starlord/subscribe/auth',
        data: {
            openId,
            activityId,
            scene,
            templateList: res.data
        }
    });
    openLoading && my.hideLoading();
    if (!authRes.ret) {
        failCb && failCb.call(this, authRes);
    } else {
        successCb && successCb.call(this, authRes, res);
    }
};

/**
 *requestSubscribeMessage必须在onTap后执行，中间不能插别的事件循环
 *错误码：
 * -3001没找到对应模板
 * -3002时间限制
 *其他是系统错误
 */
const querySubscribeList = async function (scene) {
    const env = process.env.ANU_ENV;
    if (env !== 'wx') return;
    return await promiseRequest({
        method: 'GET',
        service: '/starlord/subscribe/templateList',
        param: {
            scene
        }
    });
};

function wxGetList(data, scene, beforeSubscribeCb) {
    const systmeInfo = getApp().globalData.systemInfo || {};
    const { platform, version } = systmeInfo;
    const ver = parseInt(version.replace(/\./g, ''));
    // iOS客户端7.0.6版本、Android客户端7.0.7版本之后的一次性订阅/长期订阅才支持多个模板消息
    if ((platform === 'ios' && ver < 706) || (platform === 'android' && ver < 707)) {
        data = data.slice(0, 1);
    }
    let tmplIds = data.map(v => {
        return v.id;
    });
    return new Promise(resolve => {
        if (!my.requestSubscribeMessage) {
            resolve({
                ret: false,
                message: '请升级微信版本',
                status: 1002
            });
        }

        const { openId } = utils.getGlobalInfo().cookies;
        // markLog({
        //     openId,
        //     scene,
        //     tmplIds
        // });
        beforeSubscribeCb && beforeSubscribeCb.call(this);        
        my.requestSubscribeMessage({
            tmplIds,
            success: res => {
                /**
                 * 返回结构
                 * errMsg
                 * errorCode0
                 * 列表值reject——accept
                 */
                watcher.watchCount('subscribe-success-wx-code}');
                const arr = data.map(v => {
                    return {
                        templateId: v.id,
                        type: v.type,
                        bizType: v.bizType,
                        status: res[v.id] === 'accept' ? 0 : -1
                    };
                });
                resolve({
                    ret: true,
                    message: '',
                    data: arr,
                    status: 0
                });
            },
            fail: err => {
                // 微信api  安卓是errorCode，苹果是errCode
                const code = err.errCode || err.errorCode;
                watcher.watchCount(`subscribe-fail-wx-code-${code}`);
                if (code === 20004) {
                    resolve({
                        ret: false,
                        message: '请打开设置——消息订阅——接受',
                        status: code,
                        ...err
                    });
                } else if (code === 20002) {
                    //20002是模板重复
                    watcher.watchCount(`subscribe-fail-wx-${scene}`);
                    resolve({
                        ret: false,
                        message: '',
                        status: code,
                        ...err
                    });
                } else {
                    //用户点了取消
                    resolve({
                        ret: false,
                        message: '',
                        status: code,
                        ...err
                    });
                }
            }
        });
    });
}

/**
 * 埋点
 * http://mark.corp.qunar.com/markpage/rdc/markpoint/732/0
 * --- 全部订阅消息模版曝光统计
 * @param {*} status 
 */
// function markLog(ext) {
//     QMark && QMark.log && QMark.log({
//         ext,
//         'bizType': 'rdc',
//         'module': 'default',
//         'appcode': 'nnc_app_qunar_platform',
//         'page': 'subscribeMsg',
//         'id': 'AllSubscribeMsg',
//         'operType': 'show',
//         'key': 'rdc/subscribeMsg/default/show/AllSubscribeMsg',
//         'operTime': '*',
//     });
// }

function promiseRequest(data) {
    return new Promise(resolve => {
        request({
            ...data,
            method: data.method || 'POST',
            success: res => {
                if (res.status === 0) {
                    resolve({
                        ret: true,
                        data: res.data
                    });
                    return;
                }
                resolve({
                    ret: false,
                    message: res.message
                });
            },
            fail: err => {
                resolve({
                    ret: false,
                    message: err.message || '网络错误',
                    ...err
                });
            }
        });
    });
}

export default {
    requestSubscribeMessage,
    querySubscribeList
};