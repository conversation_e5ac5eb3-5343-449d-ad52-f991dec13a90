

import config from  './config/config';

import request from '../../common/utils/request';

const BETA = process.env.BUILD_ENV === 'beta' ? true : false;
const host = `https://pwapp${BETA ? '.beta' : ''}.qunar.com`;
function watchCount( page ) {
    if (page){
        request({
            host,
            service: config.service.watcherUrl,
            dataType: 'text',
            data: {
                page,
                type: 'count'
            }
        });
    }
}

function watchTime( key, value ) {
    if (key && value){
        request({
            host,
            service: config.service.watcherUrl,
            dataType: 'text',
            data: {
                key,
                value,
                type: 'time'
            }
        });
    }
}
// 记录请求成功失败
function apiWatcher (pageName) {
    return function (params) {
        const { success, fail, service, resCheckField } = params;
        const apiStr = service.replace(/\./g, '').split('/').join('_').split('?')[0];
        params.success = function(res) {
            watchCount(`${process.env.ANU_ENV}-${pageName}-success_${apiStr}`);
            const isExists = res.hasOwnProperty(resCheckField);
            // 接口错误上报
            switch (resCheckField) {
                case 'data':
                    // 校验data格式
                    if (!isExists) {
                        watchCount(`${process.env.ANU_ENV}-${pageName}-use-fail_${apiStr}`);
                    }
                    break;
                case 'ret':
                    // 校验ret格式
                    if (!isExists || !(typeof res.ret == 'boolean' && res.ret)) {
                        watchCount(`${process.env.ANU_ENV}-${pageName}-use-fail_${apiStr}`);
                    }
                    break;
            }
            success && success(...arguments);
        };
        params.fail = function() {
            watchCount(`${process.env.ANU_ENV}-${pageName}-fail_${apiStr}`);
            fail && fail(...arguments);
        };
        delete params.resCheckField;
        request(params);
    };
}

export default {
    watchCount,
    watchTime,
    apiWatcher
};
