import request from './request';
import utils from './util';
import user from './user';
/**
 * onShow进入小程序
 * @param {*} e 
 * @returns 
 */
function onShowRegCid(e) {
    let regCid = e && e.query && e.query.regCid;
    if (!regCid) {
        const url = e && e.query && e.query.url;
        if (!url) {
            return;
        }

        regCid = utils.getParamFromPath(decodeURIComponent(url), 'regCid');
        if (!regCid) {
            return;
        }
    }

    promiseRequest(regCid);
}

/**
 * 更改path后，检查regCid是否变化，变化即上报。
 * @param {*} url 
 * @returns 
 */
function checkRegCidInChangePath(url) {
    const regCid = utils.getParamFromPath(url, 'regCid');
    if (!regCid) {
        return;
    }

    const globalData = getApp().globalData;
    const lastRegCid = globalData && globalData.regCid;
    if (regCid === lastRegCid) {
        return;
    }

    // 上报
    promiseRequest(regCid);

    // 重置
    getApp().globalData.regCid = regCid;
}

/**
 * 构建上报的参数
 * 有token先传token，没有则传code值
 * @param {*} regCid 
 * @returns 
 */
async function promiseRequest(regCid) {
    const userData = await utils.getGlobalInfoAsync();
    const token = userData.user.token || '';

    let param = {
        regCid,
        platform: user.loginOpts.exParams.platform || '',
        productSource: process.env.ANU_ENV
    };
    if (token) {
        param.token = token;
    } else {
        let code = await user.getAuth();
        param.code = code;
    }

    let i = 0, res = false;
    while (i < 2) {
        res = await logAuthThird(param);
        if (!res.ret) {
            i++;
        } else {
            return;
        }
    }
}

function logAuthThird(param) {
    return new Promise(resolve => {
        request({
            method: 'GET',
            service: '/restapi/collectthirdlog/logAuthThird',
            param,
            success: res => {
                if (res.status === 0) {
                    resolve({
                        ret: true,
                        data: res.data
                    });
                    return;
                }
                resolve({
                    ret: false,
                    message: res.message
                });
            },
            fail: () => {
                resolve({
                    ret: false
                });
            }
        });
    });
}

export default {
    onShowRegCid,
    checkRegCidInChangePath,
};
