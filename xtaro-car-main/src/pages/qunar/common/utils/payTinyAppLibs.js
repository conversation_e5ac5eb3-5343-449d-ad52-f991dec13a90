!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("JSSDK",[],t):"object"==typeof exports?exports.JSSDK=t():e.JSSDK=t()}(this,(()=>(()=>{"use strict";var e={d:(t,s)=>{for(var r in s)e.o(s,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:s[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{alipayTradePay:()=>L,appendQuery:()=>F,clearTraceCtxData:()=>U,fetchWalletToken:()=>te,formatDate:()=>X,getAlipaySignData:()=>ee,getOrderInfo:()=>K,getPageTraceId:()=>_,getThirdsList:()=>H,getTraceCtxData:()=>E,handleCheckBeforePay:()=>Q,handlePaymentCallback:()=>Z,handleSubmitCallback:()=>B,initPwdAuth:()=>re,isJsonStr:()=>W,isOldBuApi:()=>G,pageQueryStr:()=>V,payResModel:()=>R,paywayModel:()=>M,paywaySubmitModel:()=>I,pwdAuth:()=>ne,pwdAuthSendSms:()=>ae,queryHoldpayDetailModel:()=>O,queryMiddlegroundRouteInfo:()=>S,queryOrderExtend:()=>T,queryPayResult:()=>q,querySignAndSaveResult:()=>A,queryThirdPayStatus:()=>N,recordUserOrder:()=>se,sendUbt:()=>D,sendWatcher:()=>$,setPageTraceId:()=>j,setTraceCtxData:()=>J,setUbtOrder:()=>Y,thirdsUtilCollect:()=>d,ttPay:()=>z,writeLogModel:()=>x,wxholdResultModel:()=>k});const s={warning:{logkey:"231675",type:1},info:{logkey:"231671",type:2},api:{logkey:"231672",type:3},click:{logkey:"231673",type:4},error:{logkey:"231674",type:5},chain:{logkey:"237590",type:6}};function r(e,t){try{var r=t&&t.cwx,o=r.getCurrentPage();if(o&&o.ubtDevTrace){var u=y(new Date)+"."+(new Date).getMilliseconds().toString().padStart(3,"0"),c=(e=e||{},e=Object.assign(e,i),Object.keys(e).forEach((function(t){void 0===e[t]&&delete e[t]})),r.util.systemInfo||{});if(e.devicemode=c.model||"",e.devicesystem=c.system||"",e.wxversion=c.version||"",e.sdkv=c.SDKVersion||"",e.isPayNew=!0,e.logTime=u,o.pageId&&(e.pageId=o.pageId),e.pageTraceId=e.pageTraceId||a,e.extend&&"string"!=typeof e.extend)try{e.extend=JSON.stringify(e.extend)}catch(t){}if(e.result&&"string"!=typeof e.result)try{e.result=JSON.stringify(e.result)}catch(t){}var l={...e,order:++n,type:s[e.type].type},d=s[e.type].logkey;if(o.ubtDevTrace(d||"231671",l),e.isPrdTrace){var[p,f={}]=[l,t];try{const e=(f&&f.cwx).getCurrentPage();if(!e||!e.ubtDevTrace)return;e.ubtTrace(f.ubtKey||"250642",p)}catch(t){console.log(t)}}}}catch(t){}}let a="",n=1,i={};function o(e,t){return(e+"&"+t||"").replace(/[&?]{1,2}/,"?")}const u=function({sign:e}={}){if(e)try{if(JSON.parse(e))return!1}catch(e){return!0}return e=swan.getSystemInfoSync().host,-1=="heytapbrowser|vivobrowser|mibrowser|bdlite|iqiyi|tomas|bdnetdisk|bdmap|duershow|xhsdiscover|bdminivideo|baiduhaokan|tieba|iovauto|zhidao|hex|bdappsearch|superapp|askmybaby|baiduinput|gamenow|baiduwenkuapp|fcapp|reliao|youjia|fortunecat|lemon|iqiyipps|baikeapp|baiduboxvision|baidudict|baijiahaoapp|baiduyunapp|bjjiguanfuwuapp|bdwkstapp|yymobile|fangtetourism|digitalcityapp|wywdazhu|meiguangong|ysdqapp|sousouapp|aifanfan|kktvapp|ysdqkkapp".indexOf(e)},c=(e,t)=>(e=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),1<(t=t.split("?")).length&&null!=(t=t[1].match(e))?decodeURIComponent(t[2]):null);let l={};const d=e=>{const{loading:t,traceLog:s}=e;l.loading=t,l.traceLog=function(e,t={}){s(e,t)}};function p(e){if("string"!=typeof e)return!1;try{return JSON.parse(e),!0}catch(e){return!1}}const f={y:function(e,t){return(e=e.getFullYear())<0?"BC"+-e:t<3&&e<2e3?e%100:e},M:function(e){return e.getMonth()+1},d:function(e){return e.getDate()},H:function(e){return e.getHours()},m:function(e){return e.getMinutes()},s:function(e){return e.getSeconds()},e:function(e,t){return(1===t?"":2===t?"周":"星期")+[2===t?"日":"天","一","二","三","四","五","六"][e.getDay()]}};function y(e,t){return e instanceof Date||(e=function(e,t){return e=!e||e instanceof Date?e||new Date:"string"==typeof e&&/^(\d{4})[-/]?(\d{1,2})[-/]?(\d{1,2})( +(\d+):(\d+)(?::(\d+))?)?$/.test(e)?new Date(0|RegExp.$1,(0|RegExp.$2)-1,0|RegExp.$3,0|(RegExp.$5||0),0|(RegExp.$6||0),0|(RegExp.$7||0)):new Date(e)}(e)),(t||"yyyy-MM-dd HH:mm:ss").replace(/(\w)\1*/g,(function(t,s){if(s in f){for(s=""+f[s](e,t.length);s.length<t.length;)s="0"+s;t=s}return t}))}let g;function m(e){this.settings=Object.assign({url:"",method:"POST",data:{},requestHead:{},success:function(){},fail:function(){},serviceCode:"",context:{cwx:null}},e||{})}Object.assign(m.prototype,{constructor:m,isCtrip:function(){return!(!this.settings.context||!this.settings.context.cwx)},buildUrlAndSubEnv:function(){var e=this,t=e.isCtrip();let s="prd";var r=(s=e.settings.context.env?e.settings.context.env:s).toLowerCase();let a="https://gateway.secure.ctrip.com",n="fat5068",i="";return e.settings.context.subEnv&&(n=e.settings.context.subEnv),"prd"!==r&&(a="http://gateway.secure.fws.qa.nt.ctripcorp.com"),i=o(a+this.settings.url,"paytimestamp="+ +new Date),n&&(i+="&subEnv="+n),t||(t=(e=>({submit:e?"/mobile/member/cqtouchprepaidsubmit/dispatcher.htm":"/mobile/member/cqprepaidsubmit/dispatcher.htm",route:e?"/mobile/member/cqtouchprepaidroute/dispatcher.htm":"/mobile/member/cqprepaidroute/dispatcher.htm"}))(r=e.settings.context.isOldQunarOrder||!1),["31100102","31102301","31104404","31104406","31001503","31001703","31001702"].includes(e.settings.serviceCode)?i=t.route:["31100303","31102101","31002397"].includes(e.settings.serviceCode)&&(i=t.submit)),{requestUrl:i,subEnv:n}},excute:function(){const e=this;var t=e.isCtrip();const s=e.settings.context.cwx,a=new Date,n=e.settings.serviceCode;var i,o,u=e.buildUrlAndSubEnv();r({type:"api",devOriKey:"send_"+n,serviceCode:n,desc:`请求服务${n}开始`,apiMode:"send"},{cwx:s}),t?s.request({url:u.requestUrl,data:e.getData(),method:"POST",timeout:e.settings.timeout||6e4,header:{"Content-Type":"application/json"},success:function(t){var i=null;t.statusCode,t.data.ResponseStatus,t&&t.statusCode&&200==t.statusCode&&t.data&&t.data.ResponseStatus&&t.data.ResponseStatus.Ack&&"Success"===t.data.ResponseStatus.Ack?((i=t.data||{}).ResponseStatus=i.ResponseStatus||{},i.retCode=0,i.retMsg="SOA2执行成功",r({type:"api",devOriKey:"recieve_"+n,serviceCode:n,apiMode:"receive",cost:(new Date).getTime()-a.getTime(),desc:`请求服务${n}结束`,rc:i.head&&i.head.code},{cwx:s}),e.settings.success(i)):(!(t=(i=t.data||{}).ResponseStatus&&i.ResponseStatus.Errors&&i.ResponseStatus.Errors.length&&i.ResponseStatus.Errors[0].Message||"")||/No auth/gim.test(t)||/authentication/gim.test(t)||/deserialize/gim.test(t)||/RuntimeException/gim.test(t),e.settings.fail({retCode:1,retMsg:"SOA2执行失败"}))},fail:function(t){r({type:"error",devOriKey:`recieve_${n}_fail`,serviceCode:n,apiMode:"receive",cost:(new Date).getTime()-a.getTime(),desc:`请求服务${n}失败`,extend:t},{cwx:s}),e.settings.fail({retCode:2,retMsg:"SOA2执行失败",e:JSON.stringify(t)})},complete:function(t){e.settings.complete&&e.settings.complete(t)}}):(t=this.settings.context.util,i=this.settings.context.request,t=t.getGlobalInfo(),o={subEnv:u.subEnv,serviceCode:e.settings.serviceCode,isnochecklogin:1},o=Object.assign(o,e.settings.urlParams||{}),i({service:u.requestUrl,method:"POST",returnAll:!0,header:{"Content-Type":"application/json"},param:o,data:e.getData(),timeout:e.settings.timeout||6e4,success:function(t={}){var s;if(200!==(s=t.statusCode))e.settings.fail(t);else{var{ResponseStatus:s,payload:r}=t.data,{Ack:s,Errors:a}=s;if("Success"==s&&r)s=JSON.parse(r),e.settings.success(s);else{let s={};Array.isArray(a)?s=a[0]:"[object object]"!=Object.prototype.toString.call(a).toLowerCase()||a.length?s.Message=a:s=a,s.Message,e.settings.fail(t)}}},fail:function(t){e.settings.fail(t)}},t))},getData:function(){var e=this,t=e.isCtrip(),s=e.settings.requestHead||{},r=function(e,t){let s="",r="";return e&&(r=e.clientID,s=e.user.auth),{cid:r,ctok:"",cver:"1.0",lang:"01",sid:"8888",syscode:t.sysCode||"09",auth:s,extension:[{name:"locale",value:"zh-CN"}]}}(this.settings.context.cwx,s),a=(s=function(e,t){let s="",r="5125";return g=t.nonce||function(){for(var e=[],t="0123456789abcdef",s=0;s<36;s++)e[s]=t.substr(Math.floor(16*Math.random()),1);return e[14]="4",e[19]=t.substr(3&e[19]|8,1),e[8]=e[13]=e[18]=e[23]="-",e.join("")}(),e&&(s=e.clientID,r="99999999"),e={locale:"zh-CN",sysCode:t.sysCode||"09",version:"999001",payVersion:"999.001",applicationId:"B000001",appId:r,platform:t.platform||"mini",nonce:g,deviceInfo:{userAgent:"",clientId:s,sourceId:"8888",userIp:"",rmsToken:""}},Object.assign(e,t)}(this.settings.context.cwx,s),JSON.stringify({alg:"RS256",key:"200565",serviceCode:e.settings.serviceCode,auth:"",loginType:"QUNAR",clientIp:""})),n=(e.settings.data=e.settings.data||{},[]),i=e.settings.h5plat||29;return n.push(i+=""),i=Object.assign({head:r,markInfo:{userFrom:n,supportPayInfos:["7"]},requestHead:s},e.settings.data),t?i:{payload:JSON.stringify(i),head:r,requestHead:a}}});var h=function(e){return(e=e||{}).url="/restful/soa2/22888/queryPayResult",e.serviceCode="31102101",new m(e)};function C(e,t){return e=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),null!=(t=t.match(e))?unescape(t[2]):null}const b=(e,t)=>(e=new RegExp(`<${e}>(.*?)</${e}>`,"g").exec(t))?e[1]:"";let v=function(e){let t=e.thirdPartyInfo&&e.thirdPartyInfo.sig||"",s="",r="";return p(t)&&((e=JSON.parse(t)).apimode&&(s=e.apimode,r=e.bizType),t=e.tradeNO||""),{bizType:r,apiMode:s,sign:t}},w=function(e,t){try{var s,r=e.reactApi,a=e.thirdPartyInfo||{};let n=a.sig,i=b("PrepayId",n),o=(b("AppId",n),b("PartnerId",n),b("NonceStr",n),b("TimeStamp",n),b("Sign",n),b("PackageValue",n)),u="";if(!i)try{/^https?:/.test(a.sig)?(n=a.sig&&a.sig.split("?")[1]||"",i=C("prepay_id",n),o=C("package",n),u=a.sig):(i=(s=JSON.parse(e.thirdPartyInfo.sig)).prepayid,u=s.extra&&s.extra.mweb_url,o=s.package_value)}catch(s){return void t({resultCode:5010,resultMessage:"_quickWechatPay api fail: sig parse fail",res:e})}let c={},l=()=>{},d=null;l="undefined"==typeof qa?(d=r.wxpayGetType(),r.wxpay):(d=qa.getWxPaymentType(),qa.requestWxPayment.bind(qa)),d&&"none"!==d?("MWEB"==d&&(c={mweb_url:u,prepay_id:i,package:o},"undefined"!=typeof qa)&&(c.mweb_url="https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb"),l({prepayid:i,extra:c,success:function(e){e.wxType=d,t({resultCode:0,resultMessage:"_quickWechatPay success",res:e})},fail:function(e){t({resultCode:5010,resultMessage:"_quickWechatPay api fail",res:e})},cancel:function(e){t({resultCode:4002,resultMessage:"wechatpayRequestPayment cancel",res:e})}})):t({resultCode:5010,resultMessage:"_quickWechatPay api fail: wx no install",res:e})}catch(s){t({resultCode:5011,resultMessage:s.message,res:e})}},P=function(e,t){function s(e){var s=e.resultStatus;t("9000"==s||"8000"==s?{resultCode:0,resultMessage:"_quickWechatPay success",res:e}:"4000"==s?{resultCode:4e3,resultMessage:"_quickAliPay api fail",res:e}:"6001"==s?{resultCode:1001,resultMessage:"alipayRequestPayment cancel",res:e}:"6002"==s||"6004"==s?{resultCode:s,resultMessage:"_quickAliPay api fail",res:e}:{resultCode:s,resultMessage:"_quickAliPay api fail"+JSON.stringify(e),res:e})}var r=(e.thirdPartyInfo||{}).sig||JSON.parse(e.extend||"{}").url;let a=()=>{};a="undefined"==typeof qa?e.reactApi.alipay:qa.requestAliPayment;try{a({orderInfo:r,payCallback:s,callback:s})}catch(e){t({resultCode:5010,resultMessage:"_quickAliPay api fail"+e.toString(),res:e})}},x=function(e){return(e=e||{}).url="/restful/soa2/22882/WriteLog",e.serviceCode="31104404",new m(e)},S=function(e){return(e=e||{}).url="/restful/soa2/22882/queryMiddlegroundRouteInfo",e.serviceCode="31101119",new m(e)},q=h,M=function(e){return(e=e||{}).url="/restful/soa2/22882/paymentListSearch",e.serviceCode="31100102",new m(e)},I=function(e){return(e=e||{}).url="/restful/soa2/22888/submitPayment",e.serviceCode="31100303",new m(e)},O=function(e){return(e=e||{}).url="/restful/soa2/22888/queryThirdPayDetail",e.serviceCode="31002397",new m(e)},T=function(e){return(e=e||{}).url="/restful/soa2/22882/queryOrderExtend",e.serviceCode="31100103",new m(e)},k=function(e){return(e=e||{}).url="/restful/soa2/13578/queryThirdPayStatus",e.serviceCode="31002301",new m(e)},R=h,N=function(e){return(e=e||{}).url="/restful/soa2/22882/queryThirdPayStatus",e.serviceCode="31102301",new m(e)},A=function(e){return(e=e||{}).url="/restful/soa2/22888/querySignAndSaveResult",e.serviceCode="31103306",new m(e)},D=r,_=()=>a,j=function(e){a=e},E=()=>i,J=(e={},t="")=>{try{Object.keys(e).forEach((s=>{t?i[t+"_"+s]=e[s]:i[s]=e[s]}))}catch(e){}},U=()=>{i={}},W=p,$=function(e,t){t=t&&t.watcher;var s=((new Date).toString(),(e=e||{}).actionType=e.actionType||"qunar_smallprogram_watcher",{});s.scene=e.scene||"",s.page=e.pageName||"holdpay",s.messageStr=Util.Object2String(e),s._exclude=!1,t.click(s)},L=function(e){my&&my.tradePay&&my.tradePay(e)},z=function(e){tt&&tt.pay&&tt.pay(e)},H=function(e){try{const{thirdPartyList:r=[]}=e.payCatalogInfo;var{thirdPartyDisplayInfoList:t=[]}=e.displayInfo,s=t.map((e=>{let t=e.brandId;var s=r.find((e=>e.brandId===t));return{...e,...s||{},status:e.status}}));return s}catch(e){}return[]},K=function(e){if(e)try{var t,s,r=(e=e||{}).orderInfo;return r?(t={},"string"==typeof r.payOrderInfo&&(r.payOrderInfo=JSON.parse(r.payOrderInfo)),s=r.payOrderInfo&&r.payOrderInfo.merchant,Object.assign(t,s||{}),t.title=r.orderTitle,t.currency=r.orderCurrency,t.amount=r.orderAmount,t.orderInfo=r,t.payType=r.payType,t):{}}catch(e){}return{}},B=function(e){var t,s,r=(e=Object.assign({h5plat:29,submitRes:{},extend:{confirmModalFn:()=>{}},success:function(){},fail:function(){}},e||{})).submitRes;if(r.rc=r.head.code,r.rmsg=r.head.message,r.reactApi=e.reactApi||{},r.thirdPartyInfo&&r.thirdPartyInfo.sig)if(1e5==r.head.code?r.rc=0:100001<=r.head.code&&(r.rc=1),1==r.rc)e.fail({resultCode:r.rc,resultMessage:r.rmsg});else if(0==r.rc)if(29===e.h5plat||55===e.h5plat)r.isNeedPushOrder=e.isNeedPushOrder,function(e,t){var s=e.isNeedPushOrder,r=e.thirdPartyInfo&&e.thirdPartyInfo.sig||"{}";try{var a={timeStamp:(e="string"==typeof r?JSON.parse(r):e).timeStamp,nonceStr:e.nonceStr,package:e.package,signType:e.signType,paySign:e.paySign,success:function(e){t({resultCode:0,resultMessage:"wxRequestPayment success",res:e})},fail:function(e={}){let s=e.errCode?"3001_"+e.errCode:3001;try{(/cancel/.test(e.errMsg)||"requestPayment:fail user close qrcode"==e.payStatus||/cancel/.test(e.payStatus))&&(s=1001)}catch(e){}t({resultCode:s,resultMessage:"支付失败",res:e})},complete:function(e){}};s?wx.requestOrderPayment(a):wx.requestPayment(a)}catch(e){t({resultCode:3002,resultMessage:s?"视频号支付失败":"微信支付失败",res:e})}}(r,(function(t){let s=e.success;(s=0!==t.resultCode?e.fail:s)(t)}));else if(30===e.h5plat||56===e.h5plat||58===e.h5plat||60===e.h5plat){var a=r,n=function(t){let s=e.success;(s=0!==t.resultCode?e.fail:s)(t)};let s={};try{var i={},o=v(a),l=o.sign;"JSAPI"===o.apiMode?(i.tradeNO=l,o.bizType&&(i.bizType=o.bizType)):i.orderStr=l,s=Object.assign({},i),i.success=function(e){var t=e.resultCode;n("9000"==t||"8000"==t?{resultCode:0,resultMessage:"alipayRequestPayment success",res:e,param:s}:"4000"==t?{resultCode:4e3,resultMessage:"alipayRequestPayment fail",res:e,param:s}:"6001"==t?{resultCode:1001,resultMessage:"alipayRequestPayment cancel",res:e,param:s}:"6002"==t?{resultCode:6002,resultMessage:"alipayRequestPayments fail",res:e,param:s}:{resultCode:t,resultMessage:"alipayRequestPayments fail",res:e,param:s})},abridge.tradePay(i)}catch(t){n({resultCode:4004,resultMessage:"alipayRequestPayment api fail",res:t,param:s})}}else if(35===e.h5plat){var d=function(t){let s=e.success;(s=0!==t.resultCode?e.fail:s)(t)};let s,n;a=(a=r).thirdPartyInfo&&a.thirdPartyInfo.sig||"";try{var p=JSON.parse(a);tt.pay({orderInfo:p,service:5,success:function(e){s="500"+e.code,0==e.code?d({resultCode:0,resultMessage:"ttRequestPayment success",res:e}):2==e.code||3==e.code||4==e.code?(2==e.code?n="支付失败":3==e.code?n="支付关闭":4==e.code&&(n="支付取消",s=1001),d({resultCode:parseInt(s),resultMessage:n,res:e})):9==e.code?d({resultCode:0,sendBuCode:"9",resultMessage:"ttRequestPayment notstatus",res:e}):1==e.code&&d({resultCode:0,resultMessage:"ttRequestPayment complete",res:e})},fail:function(e){d({resultCode:5010,resultMessage:"ttRequestPayment api fail",res:e})}})}catch(t){d({resultCode:5011,resultMessage:t.message,res:{}})}}else if(44===e.h5plat)!function(e,t,s){var r=e.thirdPartyInfo&&e.thirdPartyInfo.sig||"";const{confirmModalFn:a,request:n=null,cwx:i=null,util:o=null,env:l,subEnv:d,h5plat:p,loading:f}=t;t=u({sign:r});var y=e.buPayType.toLowerCase();const g=/ali/g.test(y);function m(e){s({resultCode:0,resultMessage:"支付成功",res:{}})}function C(e,t,r={}){s({resultCode:parseInt(e),resultMessage:t||"buRequestPayment api fail",res:r})}y=!!i;const b=(e,t="show")=>{f&&("show"===t?f.show(e):f.hide())},v=()=>{const{payNo:t,payToken:s}=e;"function"==typeof a&&a((()=>{let e=0;b("查询中","show"),function r(){h({data:{payToken:s,payNo:t},h5plat:p,context:{request:n,cwx:i,util:o,env:l,subEnv:d},success:t=>{var{head:t={}}=t;1e5===(t=t.code)?(b("","hide"),m()):5<=++e?(b("","hide"),C("6102","支付未成功",{msg:"轮询结束，未成功"})):setTimeout((function(){r()}),400)},fail:e=>{b("","hide"),C("6103","支付结果查询失败",e)}}).excute()}()}))};if(t)if(g)swan.requestAliPayment({orderInfo:r,success:e=>{"9000"===(e=e.resultStatus)||"8000"===e?m():"4000"==e?C("6003","支付宝支付失败"):"6001"==e?C(1001,"取消支付"):"6002"==e&&C("6005","网络不给力")},fail:e=>{let t="6007_"+(e&&e.errCode||0);e&&(2==e.errCode||/取消|cancel/.test(e.errMsg))&&(t=1001),C(t,"支付失败",{e,fn:"swan.requestAliPayment"})}});else{t=c("prepay_id",r);var w=c("package",r);let e={};y={prepayId:t,url:y?"https://secure.ctrip.com/webapp/payment2/resulttransit/wxrdforxiaomi.html":"https://tp.qunar.com/touch/wechatTransition",extraData:e=y?{mweb_url:"https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb",package:w,prepay_id:t}:{mweb_url:r}},swan.requestWeChatPayment({...y,fail:(e,t)=>{C("6101_"+(e.errCode||t||0),"支付失败",{err:e,code:t})},success:e=>{v()}})}else{let e={};try{e=JSON.parse(r)}catch(e){return s({resultCode:5020,resultMessage:"bupayment-signparse-error",res:e})}swan.requestThirdPayment({orderInfo:e,success:e=>{(g?m:v)()},fail:(e,t)=>{C("6021_"+(e.errCode||t||0),"支付失败",{err:e,code:t})}})}}(r,o=e.extend,(function(t){let s=e.success;(s=0!==t.resultCode?e.fail:s)(t)}));else if(6===e.h5plat){i=function(t){let s=e.success;(s=0!==t.resultCode?e.fail:s)(t)},p=(l=r).quickPayType;try{(/alipay/i.test(p)?P:w)(l,i)}catch(t){i({resultCode:5011,resultMessage:t.message,res:{}})}}else if(50===e.h5plat){var f=r,y=function(t){let s=e.success;(s=0!==t.resultCode?e.fail:s)(t)};let t;f=f.thirdPartyInfo&&f.thirdPartyInfo.sig||"{}",f=JSON.parse(f),ks.pay({serviceId:"1",orderInfo:f,success:function(e){t="500"+e.code,y({resultCode:0,resultMessage:"_ksRequestPayment success",res:e})},fail:function(e){y({resultCode:5010,resultMessage:"快手支付失败",res:e})}})}else 33===e.h5plat?(s=function(t){let s=e.success;(s=0!==t.resultCode?e.fail:s)(t)},f={url:f=(f=r).thirdPartyInfo&&f.thirdPartyInfo.sig||"",referer:"http://qunar.com/",success:function(e){s({resultCode:0,resultMessage:"_qqRequestWxPayment success",res:e})},fail:function(e){s({resultCode:5010,resultMessage:"QQ调用微信支付失败",res:e})}},qq.requestWxPayment(f)):e.fail&&e.fail({resultMessage:"lib未支持此支付方式",resultCode:1233});else r.rc<100?4==r.rc||8==r.rc||24==r.rc?e.success({resultCode:0,resultMessage:r.rmsg||"订单已提交支付，请勿重复提交支付！"}):12==r.rc?e.success({resultCode:0,resultMessage:r.rmsg}):66==r.rc?e.fail({resultCode:r.rc,resultMessage:"中风控"}):e.fail({resultCode:r.rc,resultMessage:r.rmsg}):201===r.rc?e.success({resultCode:201,resultMessage:"",extend:r.jsonExtend||""}):e.fail({resultCode:r.rc,resultMessage:r.rmsg});else t=r.head||{},e.fail({resultCode:"30009",resultMessage:r.rmsg||t.message||"没有下发签名"})},Q=async function({res303:e,wx:t,isDirect:s=!0,payOptions:r={}}){try{const n=e.head&&e.head.code;if(![201,202,203].includes(n))return{doNext:!0};t.hideLoading();var a=JSON.parse(e.jsonExtend||"{}");const{newTradeInfo:i="{}",title:o="",content:u="",buttonInfo:c=[],qtouchExtend:l=""}=a,d=JSON.parse(i),p=l&&JSON.parse(l);return new Promise(((e,a)=>{var i;201==n?(i={title:o,confirmText:f("continuePay")||"继续支付",cancelText:f("cancel")||"取消",content:u,success(t){let s="init";t.confirm||(s="initCancel"),e({doNext:!1,type:s,payToken:d.payToken,qtouchExtendObj:p})}},s||(i.showCancel=!0,i.cancelText=f("cancel")||"取消"),r.beforeShowPriceChangeModal&&r.beforeShowPriceChangeModal(),t.showModal(i)):202==n?t.showModal({title:o,confirmText:f("confirm")||f("cancel"),showCancel:!1,content:u,success(){e({doNext:!1,type:"back",...y("confirm")||y("cancel")})}}):203==n&&t.showModal({title:o,confirmText:f("continuePay")||"继续支付",cancelText:f("cancel")||"取消",content:u,success(t){t.confirm?e({...y("continuePay"),type:"notifyContinue",doNext:!1}):e({...y("cancel"),doNext:!1})}})}));function f(e="confirm"){return((c.find((t=>t.buttonAction===e))||{}).buttonContent||"").substring(0,4)}function y(e="continuePay"){return c.find((t=>t.buttonAction===e))}}catch(e){return Promise.resolve({doNext:!1})}},F=o,V=function(e){return Object.keys(e).map((function(t){let s=encodeURIComponent(e[t]);return"undefined"===s&&(s=""),encodeURIComponent(t)+"="+s})).join("&")},G=u,Y=function(e){n=e||0},X=y,Z=function(e={}){return new Promise(((t,s)=>{var{code:r,message:a}=e&&e.head||{};let n;1e5===r?t():s({status:n=101101===r?1:101102===r?2:-1,requestCode:r,message:a})}))},ee=v,te=function(e){return(e=e||{}).url="/restful/soa2/22882/applyWalletBindCard",e.serviceCode="31101302",new m(e)},se=function(e){return(e=e||{}).url="/restful/soa2/22882/recordUserOrder",e.serviceCode="31104406",new m(e)},re=function(e){return(e=e||{}).url="/restful/soa2/22882/initPwdAuth",e.serviceCode="31001503",new m(e)},ae=function(e){return(e=e||{}).url="/restful/soa2/22882/pwdAuthSendSms",e.serviceCode="31001703",new m(e)},ne=function(e){return(e=e||{}).url="/restful/soa2/22882/pwdAuth",e.serviceCode="31001702",new m(e)};return t})()));