import request from './request';
import utils from './util';
// const watcherUrl = '/pt/pageTrace.do';
const name = process.env.BUILD_ENV === 'beta' ?  'wechatqunarapp_beta' : 'wechatqunarapp';
function GlobalParamInstance() {
    return {
        aid: 3,
        an: name,
        curl: name,
        pid: 233333
    };
}

function isiOS(plat){
    return /(ipad)|(iphone)/.test(plat.toLowerCase());
}

function getParams(query = {}, wType = 3) {
    const app = getApp();
    var { model = '', platform = '', version = ''} = app.globalData?.systemInfo || {};
    var deviceInfo = isiOS(model) ? '_wx_iOS' : '_wx_android';
    var q = GlobalParamInstance();
	
    q.aid = wType; //0:pv, 3:click
    q.sc = 200;
    q._t = new Date().getTime();
    
    query.from = query?.from || utils.bdOrigin.getV() || 'common';
    query.device = query?.device || deviceInfo;
    query.version = app.globalData?.pVersion || '0'; 
    query.model = model;
    query.platform = platform;
    query.wx_version = version;
    query.scene = app.globalData?.scene;
    
    q.ctx = JSON.stringify(query);
    return q;
}

async function getUser() {
    let {cookies, user} = await utils.getGlobalInfoAsync();
    return {
        cookies,
        user
    };
}

function getCookies({cookies, user}) {
    var ret = ['QunarGlobal=' + cookies.QunarGlobal, '_q=' + (cookies._q || 'U.' + user.unionId)];
    return ret.join(';');
}
async function send( query, wType, fromPay ) {
    if (process.env.ANU_ENV !== 'wx' && !fromPay ) return; 
    var userInfo = await getUser();
    var queryData = getParams(query, wType);
    var cookie =  getCookies(userInfo);
    var { unionId = '', openId = '' } = userInfo.user;
    var ctx = JSON.parse(queryData.ctx);
    ctx = {
        ...ctx,
        unionId,
        openId
    };
    queryData.ctx = JSON.stringify(ctx);
    // request({
    //     service: watcherUrl,
    //     header: { Cookie: cookie },
    //     dataType: 'text',
    //     data: queryData
    // });
}
function click(query, fromPay) {
    send(query, 3, fromPay);
}
//delay: 延迟发送，默认1s
function pv(query, delay, fromPay) {
    setTimeout(function () {
        send(query, 0, fromPay);
    }, delay || 1000);
}
function click_pay(query) {
    click(query, true);
}
function pv_pay(query, delay) {
    pv(query, delay, true);
}
export default {
    click,
    pv,
    click_pay,
    pv_pay
};