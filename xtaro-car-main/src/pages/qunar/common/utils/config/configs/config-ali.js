import HookApi from '../../hookApi';
import { xEnv } from '@ctrip/xtaro';

const BETA = xEnv.getDevEnv()?.toLowerCase() === 'fat' ? true : false;
const debug = process.env.DEBUG === 'true' ? true : false;

const settings = {
    requestDomain: `https://pwapp${BETA ? '.beta' : ''}.qunar.com`
};
// 如果是debug模式 优先读取缓存
const debugDomain = HookApi.getStorageSync('debugDomain');
if (debug && debugDomain){
    const debugDomain = HookApi.getStorageSync('debugDomain');
    settings.requestDomain = debugDomain.main.domain;
}
const service = {
    // 登录接口
    autoLogin: '/oauth-client/oauth/login',
    // 检测绑定
    checkBind: '/oauth-client/oauth/checkBind',
    // 使用qvts检查绑定
    checkQBind: '/oauth-client/oauth/checkQBind',
    // 拿qunarToken
    getQunarToken: '/oauth-client/oauth/authorization',
    // 验证码
    sendSMSCode: '/ucenter/webApi/logincode.jsp',
    // 根据手机号登录
    loginByPhone: '/ucenter/webApi/logincodeverify.jsp',
    // 快速登录以及绑定支付宝账户
    loginByQuick: '/oauth-client/oauth/login',
    // 退出登录
    logOut: '/oauth-client/oauth/logout',
    // 拿支付宝用户信息
    getInfoByToken: '/oauth-client/oauth/getInfoByToken',
    // 是否登录
    checkLogin: '/oauth-client/oauth/isLogin',
    // 同步登录
    syncCookie: '/mpx/syncCookie',
    //监控地址
    watcherUrl: '/mp/watcher',
    //获取订单列表
    getOrders: '/api/mini/order/query.do',
    // 同步登陆cookie
    syncLoginState: '/oauth-client/oauth/syncLoginState',
    // 查询未登录订单
    hasOrder: '/api/mini/order/hasOrder.do',
    //优惠券列表
    couponList: '/api/market/coupon/couponList', 
    couponDetail: '/wechatSmall/coupon/detail/qq.do', //优惠券详情,
    // 联系人
    queryContact: '/webapi/contact/query',
    addContact: '/webapi/contact/save',
    updateContact: '/webapi/contact/update',
    // 使用手机号授权登录
    wechatPhoneLogin: '/oauth-client/oauth/phoneLogin',
    aliPhoneLogin: '/restapi/oauth/phoneLoginDirectly',
    unBindByQvts: '/oauth-client/oauth/unBind',
    phoneLogin: '/oauth-client/restapi/oauth/phoneLogin'
};


export default {
    settings,
    service
};
