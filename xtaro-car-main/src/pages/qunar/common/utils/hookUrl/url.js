// 由于优化公共包 size. url变化较多, 则建立文件做了 url 映射。
module.exports = {
    '/pages/ppTrip/tripList/index': '/pages/tripP/tripList/index',
    '/pages/ppTrip/focusWechat/index': '/pages/tripP/focusWechat/index',
    '/pages/ppTrip/tripListOfTimeLine/index':'/pages/tripP/tripListOfTimeLine/index',
    '/pages/ppTrip/tripShare/index':'/pages/tripP/tripShare/index',
    '/pages/platform/activeWebView/index':'/pages/alonePlatform/activeWebView/index',
    '/pages/platform/marketWebView/index':'/pages/alonePlatform/marketWebView/index',
    '/pages/platform/actWebWx/index':'/pages/alonePlatform/actWebWx/index',
    '/pages/platform/calendar/index':'/pages/alonePlatform/calendar/index',
    '/pages/platform/citySelect/index':'/pages/alonePlatform/citySelect/index',
    '/pages/platform/contact/editList/index':'/pages/alonePlatform/contact/editList/index',
    '/pages/platform/contact/list/index':'/pages/alonePlatform/contact/list/index',
    '/pages/platform/coupon/detail/index':'/pages/alonePlatform/coupon/detail/index',
    '/pages/platform/coupon/list/index':'/pages/qunar/subPages/alonePlatform/coupon/list/index',
    '/pages/platform/debugger/index':'/pages/debugger/home/<USER>',
    '/pages/platform/feedback/index':'/pages/alonePlatform/feedback/index',
    '/pages/platform/flight/index':'/pages/alonePlatform/flight/index',
    '/pages/platform/gift/index':'/pages/alonePlatform/gift/index',
    '/pages/platform/login/index':'/pages/qunar/subPages/alonePlatform/login/index',
    '/pages/platform/loginAuth/index':'/pages/alonePlatform/loginAuth/index',
    '/pages/platform/lowPriceAuth/index':'/pages/alonePlatform/lowPriceAuth/index',
    '/pages/platform/noOrderList/index':'/pages/alonePlatform/noOrderList/index',
    '/pages/platform/orderList/index':'/pages/alonePlatform/orderList/index',
    '/pages/platform/pay/index':'/pages/alonePlatform/pay/index',
    '/pages/platform/phoneOrderList/index':'/pages/alonePlatform/phoneOrderList/index',
    '/pages/platform/pushMiddlePage/index':'/pages/alonePlatform/pushMiddlePage/index',
    '/pages/platform/queryOrderPhone/index':'/pages/alonePlatform/queryOrderPhone/index',
    '/pages/platform/subscribePage/index':'/pages/alonePlatform/subscribePage/index',
    '/pages/platform/wxPay/realNameAuth/index':'/pages/alonePlatform/wxPay/realNameAuth/index',
    '/pages/activity_train/webview/index': '/pages/functional_train/activityWebview/index'
};