function getFormatPrice(price) {
    price = price && (price / 10000);
    if (price && price > 99999) {// 如果新的价格大于99999
        price = Number(price / 10000).toFixed(2) + '万+';
    }
    return price;
}

function formatdata(data) {
    if (!data) {
        return;
    }
    let rg = /\d[!\w]*/g;
    data.forEach(function (k) {
        k.price = getFormatPrice(k.price);
        if (k.businessType == 'carcar') { //处理车车的特殊情况
            k.priceOrigin = getFormatPrice(k.priceOrigin);
            k.dynamicPrice = getFormatPrice(k.dynamicPrice);
        }
        let orders = k.trainList || k.busList || k.flightList || [];
        let l = orders.length;
        for (let i = 0;i < l;i++) {
            let orderi = orders[i];
            let keyfr = '';
            let keyto = '';
            if (orderi.departure && orderi.arrival) {
                keyfr = 'departure';
                keyto = 'arrival';
            } else if (orderi.fromDate && orderi.toDate) {
                keyfr = 'fromDate';
                keyto = 'toDate';
            }
            let depm = orderi[keyfr] && orderi[keyfr].match(rg) || '';
            let arrm = orderi[keyto] && orderi[keyto].match(rg) || '';
            if (!depm.length || !arrm.length) {
                return;
            }
            // 组装时间段
            orderi.timerange = depm[0]+'-'+depm[1]+'-'+depm[2]+'   '+depm[3]+':'+depm[4]+'~'+arrm[3]+':'+arrm[4];
            // 计算跨天
            let depmi = parseInt(depm[1])-1;
            let arrmi =parseInt(arrm[1])-1;
            depm[1] = (depmi < 10) ? ('0'+depmi) : depmi;
            arrm[1] = (arrmi < 10) ? ('0'+arrmi) : arrmi;
            let secs = new Date(arrm[0], arrm[1], arrm[2], arrm[3], arrm[4]).getTime() - new Date(depm[0], depm[1], depm[2], depm[3], depm[4]).getTime();
            if (Math.floor(secs/86400000) > 0) {
                let tip = '+'+Math.floor(secs/86400000)+'天';
                orderi.tip = tip;
            }
        }
    });
    return data;
}

const businessTypeMaps = {
    flight: ['flight'],
    hotel: ['hotel,hotel_sight'],
    hotelAll: ['hotel_w,hotel_group_w,huiyi,hotel_sight,bnb,coupon'],
    train: ['train'],
    bus: ['bus', 'directbus'],
    food: ['food'],
    sight: ['ticket', 'hotel_sight'],
    carcar: ['car,icar'],
    vacation: ['travel'],
    ddr: ['local'],
    orderinsure: ['insure'],
    qmall: ['qmall'],
    shopping: ['shopping']
};


function getBusinessType(key) {
    key = key || 'all';
    if (!businessTypeMaps[key]) { //如果是all的情况下
        let allTypes = [];
        Object.keys(businessTypeMaps).forEach(function(value) {
            allTypes = allTypes.concat(businessTypeMaps[value]);
        });
        return allTypes.join(',');
    }
    return businessTypeMaps[key].join(',');
}

export default {
    formatdata,
    getBusinessType
};