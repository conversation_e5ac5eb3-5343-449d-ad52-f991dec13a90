function Event(eventName, callback, message) {
    this.eventName = eventName;
    this.callback = callback;
    this.message = message;
}

Event.prototype.removeListener = function() {
    var { subscribers = [] } = getApp().globalData ; 
    var index = subscribers.indexOf(this);
    if (index != -1) {
        subscribers.splice(index, 1);
    }
};

var EventEmitter = {
    
    addListener: function(eventName, callback, autoTrriggerOnce) {
        const app = getApp();
        let subscribers = app.globalData.subscribers || []; // 获取引用
        if (autoTrriggerOnce) {
            var listener = null;
            subscribers.forEach(function(event) {
                if (event.eventName === eventName) {
                    listener = event;
                    event.callback = callback;
                    event.message && event.callback(event.message);
                }
            });
            if (listener) {
                return listener;
            }
        }
        var event = new Event(eventName, callback);
        subscribers.push(event);
        app.globalData.subscribers = subscribers; // 同步回全局
        return event;
    },
    removeListener: function(event) {       //event 类型： string || Event || Array
        const app = getApp();
        const globalData = app.globalData;
        let subscribers = globalData.subscribers || [];
        var rm = function(e) {
            var index = subscribers.indexOf(e);
            if (index != -1) {
                globalData.subscribers = subscribers; // 同步1：Event实例删除
            }
        };

        if (typeof event == 'string') {
            subscribers = subscribers.filter(e => e.eventName !== event); // 改用filter
            globalData.subscribers = subscribers; // 同步2：字符串类型删除
        } else if (event instanceof Event) {
            rm(event);
        } else if (event instanceof Array) {
            event.forEach(function(e) {
                EventEmitter.removeListener(e);
            });
        }
    },
    dispatch: function(eventName, param) {
        const app = getApp();
        let subscribers = app.globalData.subscribers || [];
        var hasListener = false;
        subscribers.forEach(function(event) {
            if (event.eventName === eventName) {
                hasListener = true;
                event.callback && event.callback(param);
            }
        });
        if (!hasListener && param) {
            subscribers.push(new Event(eventName, null, param));
            app.globalData.subscribers = subscribers; // 同步
        }
    },
    lookFunc: function(eventName) {
        let funcArr = [];
        var { subscribers = [] } = getApp().globalData ; 
        subscribers.forEach(function(event) {
            if (event.eventName === eventName) {
                // event.callback && event.callback(param);
                funcArr.push(event.callback.toString());
            }
        });
        return funcArr;
    }
};

export default EventEmitter;
