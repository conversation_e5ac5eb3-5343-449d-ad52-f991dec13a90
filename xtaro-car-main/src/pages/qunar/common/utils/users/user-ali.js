/**
 * 注意 这里授权和token是分两种的
 * 低级code换低级token，低级token查信息拿不到三方数据，并且绑定时会没有头像昵称
 * 登录、查询三方信息时必须跳过token缓存，用高级code重新拿高级token （后台只对token过期做了判断，没有高低级的判断）
 * 每个暴露出去的接口 都会返回ret，可能存在errMsg，业务根据ret判断成功和失败信息
 */

import config from '../config/config.js';
import request from '../request.js';
import util from '../util';
import watcher from '../watcher';
import getPId from '../pid';
import LogQmark from '../logQmark';
import HookApi from '../hookApi';
import AppContext from '../../../../xcar/Util/AppContext';

let loginOpts = {
    //有默认值，根据第一次调用登录login时传的为准login(callback, exParams)
    exParams: {
        platform: 'alipay$$$CCar',
        source: 'ucenter',
        bd_source: 'ali'
    },
    openIdReTime: 0
};

const defaultQmarkData = {
    page: 'loginAli',
    module: 'login',
    operType: 'click'
};

// 查询是否和去哪儿账户绑定 只有checkBind是base授权
async function checkBind() {
    // 静默授权 一般不会失败
    const authCode = await getAuth('auth_base');
    const authInfos = await getQunarToken(authCode, 'auth_base');
    // 授权失败
    if (!authInfos.ret) {
        return {ret: false, errMsg: '查询绑定失败'};
    }
    try {
        const bindInfos = await promiseRequest({
            service: getUrl('checkBind'),
            method: 'POST',
            data: {
                qunarToken: authInfos.data.qunarToken,
                platform: loginOpts.exParams.platform
            },
            header: {
                'content-type': 'application/x-www-form-urlencoded'
            }
        });
        if (!bindInfos.ret) {
            LogQmark({
                ...defaultQmarkData,
                id: 'checkBind_fail_ali',
                ext: {
                    errorMsg: JSON.stringify(bindInfos)
                }
            });
        } else {
            LogQmark({
                ...defaultQmarkData,
                id: 'checkBind_success_ali',
            });
        }
        updateUserInfo({
            user: {
                isBind: bindInfos.data.isBind,
                avatarUrl: bindInfos.data.imgUrl || '',
                nickName: bindInfos.data.nickname || '',
                phone: bindInfos.data.markMobile || ''
            }
        });
        return {ret: true, data: bindInfos.data};
    } catch (err) {
        LogQmark({
            ...defaultQmarkData,
            id: 'checkBind_fail_ali',
            ext: {
                errorMsg: JSON.stringify(err)
            }
        });
        watcher.watchCount('check-bind-fail-ali');
        return {ret: false, errMsg: '查询绑定失败'};
    }
}

// 验证码
// 五个参数，手机号和来源登录页面提供
async function sendSMSCode(data) {
    try {
        const res = await promiseRequest({
            service: getUrl('sendSMSCode'),
            method: 'POST',
            data: {
                ...data,
                action: 'register',
                type: 'implicit',
                origin: loginOpts.exParams.platform
            },
            header: {
                'content-type': 'application/x-www-form-urlencoded'
            }
        });
        if (!res.ret) {
            LogQmark({
                ...defaultQmarkData,
                id: 'logincode_fail_ali',
                ext: {
                    errorMsg: JSON.stringify(res)
                }
            });
        } else {
            LogQmark({
                ...defaultQmarkData,
                id: 'logincode_success_ali',
            });
        }
        return res;
    } catch (err) {
        LogQmark({
            ...defaultQmarkData,
            id: 'logincode_fail_ali',
            ext: {
                errorMsg: JSON.stringify(err)
            }
        });
        watcher.watchCount('send-code-ali');
        return err;
    }
}

// 授权手机号登录
async function loginByAliPhone({ iv, encryptedData, source, ...params }) {
    const tokenRes = await getQunarToken();
    if (!tokenRes.ret) {
        return { ret: false, data: tokenRes.data, errMsg: tokenRes.errMsg || '获取token失败' };
    }
    const sourceFrom = HookApi.getStorageSync('sourceFrom');
    try {
        const res = await promiseRequest({
            service: getUrl('aliPhoneLogin'),
            method: 'POST',
            data: {
                token: tokenRes.data.token,
                encryptedData,
                platform: loginOpts.exParams.platform,
                origin: loginOpts.exParams.platform,
                usersource: loginOpts.exParams.source,
                source: source || sourceFrom || loginOpts.exParams.source,
                business: params.business || '',
                pid: params.pid || getPId() || '',
                originChannel: params.originChannel || '',
                activityCode: params.activityCode || '',
                ref: params.ref || ''
            }
        });
        LogQmark({
            id: 'loginByAliPhone',
            ...defaultQmarkData,
            ext: { ...params  }
        });
        if (res.status !== 0) {
            LogQmark({
                ...defaultQmarkData,
                id: 'aliPhoneLogin_fail',
                ext: {
                    errorMsg: JSON.stringify(res)
                }
            });
            watcher.watchCount('login-by-phone-auth-fail-ali');
            if (res.errMsg === 'DECRYPT_FAIL') {
                res.errMsg = '登录失败，若再出现请关闭后再试';
                watcher.watchCount('DECRYPT_FAIL-loginByAliPhone');
            }
            return res;
        } else {
            LogQmark({
                ...defaultQmarkData,
                id: 'aliPhoneLogin_success',
            });
            watcher.watchCount('login-by-phone-auth-success-ali');
        }
        const qvts = {
            _q: res.data.qcookie,
            _v: res.data.vcookie,
            _t: res.data.tcookie,
            _s: res.data.scookie
        };
        // 同步登录态
        updateUserInfo({
            cookies: qvts,
            user: { encryptedData }
        });
        try {
            await this.syncLoginState();
        } catch (err) {
            watcher.watchCount('syncLoginState-fail-ali');
        }
        // 绑定支付宝账号
        loginByQuick(qvts);
        // 如果isReg为true，则是新注册的用户
        return {
            ret: res.status === 0,
            isReg: res.data.isReg,
            errMsg: res.errMsg || '登录失败，攻城狮正在紧急修复，请稍后重试'
        };
    } catch (err) {
        LogQmark({
            ...defaultQmarkData,
            id: 'aliPhoneLogin_fail',
            ext: {
                errorMsg: JSON.stringify(err)
            }
        });
        return err;
    }
}
// 通过手机号验证码登录 登录完了再调绑定
async function loginByPhone(data) {
    const authCode = await getAuth('auth_base');
    const authInfos = await getQunarToken(authCode, 'auth_base', true);
    try {
        const res = await promiseRequest({
            service: getUrl('loginByPhone'),
            method: 'POST',
            data: {
                ...data,
                action: 'register',
                type: 'implicit',
                origin: loginOpts.exParams.platform,
                qunarToken: authInfos.data.qunarToken
            },
            header: {
                'content-type': 'application/x-www-form-urlencoded'
            }
        });
        LogQmark({
            id: 'loginByPhone',
            ...defaultQmarkData,
            ext: { ...data  }
        });
        if (!res.ret) {
            LogQmark({
                ...defaultQmarkData,
                id: 'loginByPhone_fail_ali',
                ext: {
                    errorMsg: JSON.stringify(res)
                }
            });
        } else {
            LogQmark({
                ...defaultQmarkData,
                id: 'loginByPhone_success_ali',
            });
        }
        const qvts = {
            _q: res.data.qcookie,
            _v: res.data.vcookie,
            _t: res.data.tcookie,
            _s: res.data.scookie
        };
        // 同步登录态
        updateUserInfo({
            cookies: qvts
        });
        try {
            await this.syncLoginState();
        } catch (err) {
            watcher.watchCount('syncLoginState-fail-ali');
        }
        // 绑定支付宝账号
        loginByQuick(qvts);
        // 如果isReg为true，则是新注册的用户
        return {ret: true, isReg: res.data.isReg};
    } catch (err) {
        LogQmark({
            ...defaultQmarkData,
            id: 'loginByPhone_fail_ali',
            ext: {
                errorMsg: JSON.stringify(err)
            }
        });
        watcher.watchCount('login-by-phone-fail-ali');
        return err;
    }
}

// 快速登录 以及绑定账户
async function loginByQuick(qvts) {
    // 绑定需要穿qvts，如果是上面绑定的就带，否则为空
    qvts = qvts || {
        _q: '',
        _v: '',
        _t: '',
        _s: ''
    };
    // 重新拿token
    // 进来的时候已经user了 情况比较极端
    let authCode = await getAuth('auth_base');
    let authInfos = await getQunarToken(authCode, 'auth_base', true);
    // 授权失败
    if (!authInfos.ret) {
        // 拿失败了 取缓存的
        const storage = util.getGlobalInfo();
        authInfos.data = {
            qunarToken: storage.cookies.token || ''
        };
    }
    try {
        const res = await promiseRequest({
            service: getUrl('loginByQuick'),
            method: 'POST',
            data: {
                ...qvts,
                qunarToken: authInfos.data.qunarToken,
                platform: loginOpts.exParams.platform
            },
            header: {
                'content-type': 'application/x-www-form-urlencoded'
            }
        });
        if (!res.ret) {
            LogQmark({
                ...defaultQmarkData,
                id: 'login_by_quick_fail_ali',
                ext: {
                    errorMsg: JSON.stringify(res)
                }
            });
        } else {
            LogQmark({
                ...defaultQmarkData,
                id: 'login_by_quick_success_ali',
            });
        }
        // 同步登录态
        updateUserInfo({
            cookies: {
                _q: res.data.qcookie,
                _v: res.data.vcookie,
                _t: res.data.tcookie,
                _s: res.data.scookie
            }
        });
        try {
            await this.syncLoginState();
        } catch (err) {
            watcher.watchCount('syncLoginState-fail-ali');
        }
        return {ret: true};
    } catch (err) {
        LogQmark({
            ...defaultQmarkData,
            id: 'login_by_quick_fail_ali',
            ext: {
                errorMsg: JSON.stringify(err)
            }
        });
        watcher.watchCount('quick-login-fail-ali');
        return (err);
    }
}

// 退出登录 并且删掉qvts
async function logOut() {
    try {
        const storage = util.getGlobalInfo();
        const qvts = storage.cookies;
        const res = await promiseRequest({
            service: getUrl('logOut'),
            method: 'POST',
            data: {
                ...qvts,
                platform: loginOpts.exParams.platform
            },
            header: {
                'content-type': 'application/x-www-form-urlencoded'
            }
        });
        if (!res.ret) {
            LogQmark({
                ...defaultQmarkData,
                id: 'logout_fail_ali',
                ext: {
                    errorMsg: JSON.stringify(res)
                }
            });
        } else {
            LogQmark({
                ...defaultQmarkData,
                id: 'logout_success_ali'
            });
        }
        // 同步登录态
        updateUserInfo({
            cookies: {
                _q: '',
                _v: '',
                _t: '',
                _s: ''
            }
        });
        try {
            await this.syncLoginState();
        } catch (err) {
            watcher.watchCount('syncLoginState-fail-ali');
        }
        return {ret: true};
    } catch (err) {
        LogQmark({
            ...defaultQmarkData,
            id: 'logout_fail_ali',
            ext: {
                errorMsg: JSON.stringify(err)
            }
        });
        watcher.watchCount('logout-fail-ali');
        return (err);
    }
}

// 拿平台信息，需要高级授权
async function getMiniProgramUserInfo() {
    return new Promise(resolve => {
        my.getAuthCode({
            scopes: 'auth_user',
            success: () => {
                my.getAuthUserInfo({
                    success: (res) => {
                        const wxParse = {
                            nickName: res.nickName,
                            avatarUrl: res.avatar
                        };
                        updateUserInfo({
                            user: {
                                wxInfo: wxParse
                            }
                        });
                        resolve({ret: true, data: {userInfo: wxParse}});
                    },
                    fail: () => {
                        resolve({ret: false, data: {}});
                    }
                });
            },
            fail: () => {
                resolve({ret: false, data: {}});
            }
        });
    });
}
let tokenArr = null;
function getQunarToken(authCode, scope, useNewToken) {
    if (tokenArr) {
        // console.log('触发tokensingle');
        return tokenArr;
    } else {
        const res = getQunarTokenSingle(authCode, scope, useNewToken);
        tokenArr = res;
        return res;
    }
}
// 授权接口 这里要先检查token是否可用
async function getQunarTokenSingle(authCode, scope, useNewToken) {
    let user;
    if (!useNewToken) {
        user = await checkToken();
        if (user.token) {
            tokenArr = null;
            return {
                ret: true,
                data: {
                    qunarToken: user.token,
                    thirdId: user.thirdId
                }
            };
        }
    }
    try {
        const res = await promiseRequest({
            service: getUrl('getQunarToken'),
            data: {
                authCode,
                platform: loginOpts.exParams.platform,
                scope
            },
            method: 'POST',
            header: {
                'content-type': 'application/x-www-form-urlencoded'
            }
        });
        tokenArr = null;
        if (!res.ret) {
            LogQmark({
                ...defaultQmarkData,
                id: 'smallLogin_fail_ali',
                ext: {
                    errorMsg: JSON.stringify(res)
                }
            });
        } else {
            LogQmark({
                ...defaultQmarkData,
                id: 'smallLogin_success_ali',
                ext: { resData: res  }
            });
        }
        // eslint-disable-next-line
        console.log('token不可用， 下发新token', res.data.qunarToken);
        updateUserInfo({
            user: {
                openId: res.data.thirdId,
                token: res.data.qunarToken
            }
        });
        return res;
    } catch (err) {
        tokenArr = null;
        LogQmark({
            ...defaultQmarkData,
            id: 'smallLogin_fail_ali',
            ext: {
                errorMsg: JSON.stringify(err)
            }
        });
        watcher.watchCount('get-new-token-fail-ali');
        return err;
    }
}

// 用token拿信息，有就说明可用
async function checkToken() {
    const { user } = util.getGlobalInfo();
    if (!user.token || !user.openId) {
        return false;
    }
    try {
        await promiseRequest({
            service: getUrl('getInfoByToken'),
            method: 'POST',
            data: {
                qunarToken: user.token,
                platform: loginOpts.exParams.platform
            },
            header: {
                'content-type': 'application/x-www-form-urlencoded'
            }
        });
        // eslint-disable-next-line
        console.log('token可用');
        return {
            token: user.token,
            thirdId: user.openId
        };
    } catch (err) {
        return false;
    }

}

// 小程序打开，拿openId和token
async function appLanchInfos() {
    loginOpts.openIdReTime ++;
    const authCode = await getAuth('auth_base');
    const authInfos = await getQunarToken(authCode, 'auth_base');
    if (!(authInfos && authInfos.data && authInfos.data.thirdId)) {
        setTimeout(() => {
            if (loginOpts.openIdReTime > 3) return;
            appLanchInfos();
        }, 3000);
        return;
    }
    updateUserInfo({
        user: {
            openId: authInfos && authInfos.data && authInfos.data.thirdId,
            token: authInfos && authInfos.data && authInfos.data.qunarToken
        },
        cookies: {
            openId: authInfos && authInfos.data && authInfos.data.thirdId,
            bd_source: loginOpts.exParams.bd_source
        }
    });
    checkLogin();
}

// 支付宝授权
function getAuth(type = 'auth_base') {
    return new Promise((resolve, reject) => {
        my.getAuthCode({
            scopes: type,
            success: res => {
                resolve(res.authCode);
            },
            fail: err => {
                reject(err);
            }
        });
    });
}

// 封装promise请求
function promiseRequest(data) {
    return new Promise((resolve, reject) => {
        request({
            ...data,
            success: res => {
                resolve(res);
            },
            fail: err => {
                reject(err);
            }
        });
    });
}

function getUrl(apiName) {
    return config.service[apiName];
}

//同步登录态数据
function updateUserInfo(data) {
    if (!data) {
        return;
    }
    const storage = util.getGlobalInfo();
    const { cookies, user } = storage;
    // 如果带了登录态，替换
    let newCookies = data.cookies || {};
    let newUser = data.user || {};
    for (let k in newCookies) {
        cookies[k] = newCookies[k];
    }
    for (let k in newUser) {
        user[k] = newUser[k];
    }
    AppContext.setCookie({cookies});
    AppContext.setUser(user)
    let UserData = { cookies: cookies, user: user };
    HookApi.setStorageSync('UserData',  UserData );
}

// 检测是否登录  一进入访问 可以拿头像
async function checkLogin() {
    const { cookies } = util.getGlobalInfo();
    try {
        const res = await promiseRequest({
            service: getUrl('checkLogin'),
            data: {
                ...cookies
            },
            method: 'POST',
            header: {
                'content-type': 'application/x-www-form-urlencoded'
            }
        });
        if (!res.ret) {
            LogQmark({
                ...defaultQmarkData,
                id: 'check_login_fail_ali',
                ext: {
                    cookies,
                    errorMsg: JSON.stringify(res),
                }
            });
        } else {
            LogQmark({
                ...defaultQmarkData,
                id: 'check_login_success_ali'
            });
        }
        updateUserInfo({
            user: {
                avatarUrl: res.data.imgUrl || '',
                nickName: res.data.nickname || ''
            }
        });
        if (!res.data.isLogin){
            updateUserInfo({
                cookies: {
                    _q: '',
                    _v: '',
                    _t: '',
                    _s: ''
                }
            });
        }
        try {
            await this.syncLoginState();
        } catch (err) {
            watcher.watchCount('syncLoginState-fail-ali');
        }
        return res;
    } catch (err) {
        LogQmark({
            ...defaultQmarkData,
            id: 'check_login_fail_ali',
            ext: {
                cookies,
                errorMsg: JSON.stringify(err),
            }
        });
        watcher.watchCount('check-login-fail-ali');
        return err;
    }
}

// 将登录态同步到cookie内
async function syncLoginState() {
    const { cookies } = util.getGlobalInfo();
    await promiseRequest({
        service: getUrl('syncLoginState'),
        data: {
            ...cookies
        }
    });
}


// 类似于原有微信getUserInfo方法
async function getUserInfo() {
    const storage = util.getGlobalInfo();
    return {
        ret: true,
        data: {
            exParams: loginOpts.exParams,
            qunar: Object.assign(storage.cookies, {
                token: storage.user.token
            }),
            wechat: Object.assign(storage.user.wxInfo || {}, {
                openId: storage.user.openId,
                unionId: storage.user.unionId,
                // 以前的微信是这么判断登陆的
                isQunarUser: !!storage.cookies._q
            })
        }
    };
}

export default {
    loginOpts,
    checkBind,
    sendSMSCode,
    loginByQuick,
    loginByPhone,
    logOut,
    getAuth,
    loginByAliPhone,
    getMiniProgramUserInfo,
    checkLogin,
    appLanchInfos,
    syncLoginState,
    getUserInfo,
    getQunarToken
};