/*
 * 平台：微信小程序、支付宝小程序、头条小程序
 * 支付方式：微信支付、支付宝支付、头条支付（中台）
*/
import request from '../request';
import util from '../util.js';
import userUtil from '../user';
import CommonUtils from '../util.js';
import { paywayModel, getThirdsList, getOrderInfo, paywaySubmitModel, handleSubmitCallback, payResModel, handlePaymentCallback, handleCheckBeforePay, getPageTraceId } from '../payTinyAppLibs';
import { getParam, getRequestExtend, getIsOldQunarOrder, getCurrentPayment, midstageBrandIdAndOldQMap, showPayResutlModal, Loading, reportErrorLog, setPaySubmitRes } from './utils/midstage';
import { addRealNameListener, getAntiDisturbanceUrl, needRealNameGuide, toRealNameGuide } from './utils/realname';
import { getWalletDisplayWay, getAbTest } from './utils/utilWallet';
import { getOrCreatePaymentTraceId, setPaymentTraceId } from './utils/paymentTraceId';
import HookApi from '../hookApi';

const subEnv = process.env.BUILD_ENV === 'beta' ? 'fat5068' : '';
const ANU_ENV = process.env.ANU_ENV;

let quickPayType = ''; //快应用支付类型：weixinJSPay|aliPayPlugin
let tempPayToken = ''; //payToken
let thirdPartyList = [];//102返回三方列表
let walletWay = null;//102钱包display对象
let paylistRes = {};//102 response
let paySubmitRes = {};//303 response
let nonce303 = '';
let notifyContinue = '';
let recallInfo = '';
let wxVideoData = {
    TraceId: '',
    isNeedPushOrder: '',
    RequiredFundType: ''
};

// paymentSubmitTraceFlag：paymentSubmit 埋点 Flag，当该 Flag 为 false 时，paymentSubmit 埋点才会上送
// paymentSubmit埋点主要用于如短信风控、未签约等导致的支付提交属于正常流程，不应该算一次新的 paymentSubmit
let paymentSubmitTraceFlag = false;
let paySubmitWay = [];
let isAlipayJsApi = false;
let walletNotEnoughTraceFlag = false; // 钱包不足场景监控点，临时的，监控完可以删除

const isWxType = (type) => /weixin|we/i.test(type);
const isAliType = (type) => /alipay/i.test(type);
// 获取公共通用信息
function payMidstageData() {
    const me = this;
    const cashierUrl = me.payOptions.cashierUrl;
    const isOldQunarOrder = getIsOldQunarOrder(cashierUrl);
    const payToken = isOldQunarOrder ? me.getQueryString(cashierUrl, 'token')
        : (me.getQueryString(cashierUrl, 'payToken') || me.getQueryString(cashierUrl, 'tradeNo') || me.getQueryString(cashierUrl, 'token') || '');
    return {
        cashierUrl,
        isOldQunarOrder,
        payToken,
        params: getParam(isOldQunarOrder),
    };
}

// 指定方式后调用支付： 弹窗按钮事件
function toPayWithType(type) {
    quickPayType = type;
    let item = {};
    if (isAliType(type)) {
        item = thirdPartyList.find(payway => isAliType(payway.brandId)) || {};
    } else if (isWxType(type)) {
        item = thirdPartyList.find(payway => isWxType(payway.brandId)) || {};
    }
    Loading.show('提交支付');
    this.payMidstagePlugin({ payToken: tempPayToken, paymentWayToken: item.paymentWayToken, paywayModelData: paylistRes });
}
async function paySuccessLog() {
    try {
        const me = this;
        me.sendWatcher('paySuccess');
        if (paySubmitWay.includes('wallet')) {
            me.sendWatcher('pay_success_wallet');
        }
        if (isAlipayJsApi) {
            me.sendWatcher('paySuccess_jsApi');
        }
        if (walletNotEnoughTraceFlag) {
            console.log('zyc paySuccess_wallet_not_enough');
            me.sendWatcher('paySuccess_wallet_not_enough');
        }
    } catch (err) {
        // eslint-disable-next-line no-console
        console.error(err);
    }
}
// 乎起第三方支付页面
async function toPay(options) {
    const { payToken = tempPayToken, paymentWayToken, paywayModelData, walletInfos, restAmount, submitCallBack, riskParam, isSmsSubmit, smsSubmitCallBack } = options;
    const me = this;
    const { amount } = getOrderInfo(paywayModelData) || '';
    const submitTimeOut = paywayModelData && paywayModelData.orderInfo && paywayModelData.orderInfo.submitTimeOut || 30000;
    const thirdAmount = typeof restAmount == 'undefined' ? amount: restAmount;
    const { isOldQunarOrder, params: paymentParams } = me.payOptions.midstageData;
    const { h5plat, appId, thirdSubTypeID } = paymentParams;
    nonce303 = util.CreateGuid();
    let requestHead = { nonce: nonce303 };
    requestHead.extend = JSON.stringify({
        pageTraceId: getPageTraceId(),
        paymentTraceId: getOrCreatePaymentTraceId()
    });
    if (isOldQunarOrder) {
        requestHead.extend = await getRequestExtend(me.payOptions.cashierUrl);
    }
    let subTypeID = '';
    if (typeof thirdSubTypeID === 'function') {
        subTypeID = thirdSubTypeID(quickPayType);
    } else {
        subTypeID = thirdSubTypeID;
    }
    try {
        if (!me.payOptions.openId){
            me.sendWatcher('toPay_get_openid_again');
            const res = await userUtil.getQunarToken();
            me.sendWatcher('getQunarToken_result', { res });
            const initUserData = await util.getGlobalInfoAsync() || {};
            const { cookies={}, user={} } = initUserData;
            me.payOptions.openId = cookies.openId || user.openId;
            if (!me.payOptions.openId) {
                me.sendWatcher('toPay_get_openid_error');
                reportErrorLog({
                    errorType: '30003',
                    errorMessage: 'openId为空',
                    extendInfo: initUserData
                });
            }
        }
        me.sendWatcher(`openId-${me.payOptions.openId || 'undefined'}`);
    } catch (error) {
        try {
            me.sendWatcher('toPay_get_openid_catch_error', { errMsg: error instanceof Error ? error.toString() : '', error  });
        } catch (e) {
            // eslint-disable-next-line no-console
            console.error(e);
        }
    }
    const extendJson = {
        openid: me.payOptions.openId,
        thirdSubTypeID: subTypeID,
        TraceId: wxVideoData.TraceId,
        IsNeedPushOrder: wxVideoData.isNeedPushOrder,
        RequiredFundType: wxVideoData.RequiredFundType,
        ...(isOldQunarOrder || !appId ? {} : { extend: appId }),
    };
    const alipayApiMode = 'JSAPI';
    if (ANU_ENV === 'ali' && me.payOptions.openId) {
        extendJson.apimode = alipayApiMode;
        isAlipayJsApi = true;
    } else {
        isAlipayJsApi = false;
    }
    let dataParam = {
        payToken: payToken,
        payTypes: [],
        paymentMethodInfo: {}
    };
    paySubmitWay = [];
    // 三方金额大于0：加入三方的支付方式
    if (thirdAmount > 0) {
        dataParam.payTypes.push('3');
        dataParam.paymentMethodInfo.thirdPayInfos = [{
            payAmount: thirdAmount,
            routerInfo: {
                paymentWayToken: paymentWayToken
            },
            extend: JSON.stringify(extendJson)
        }];
        paySubmitWay.push('thirdPay');
    }

    // 短信风控
    if (isSmsSubmit) {
        dataParam = {
            ...dataParam,
            ...riskParam
        };
    }

    // 加入钱包支付方式
    if (thirdAmount - amount < 0) {
        if (walletInfos && walletInfos.length > 0) {
            dataParam.payTypes.push('1');
            dataParam.paymentMethodInfo.walletInfos = walletInfos;
            paySubmitWay.push('wallet');
        } else {
            try {
                reportErrorLog({
                    errorType: '30015',
                    errorMessage: '支付提交异常-walletInfos为空',
                    extendInfo: { thirdAmount, amount, payToken, walletInfos, options }
                });
            } catch (err) {
                // eslint-disable-next-line no-console
                console.error(err);
            }
        }

    }
    // 203 软拦截： 需要加参数 notifyContinue、payNo
    if (notifyContinue) {
        dataParam.extend = JSON.stringify({
            notifyContinue: '1',
            recallInfo
        });
        dataParam.payNo = paySubmitRes.payNo;
        notifyContinue = '';
    }

    try {
        if (!paymentSubmitTraceFlag) {
            paymentSubmitTraceFlag = true;
            me.sendWatcher('paymentSubmit');
            if (paySubmitWay.includes('wallet')) {
                me.sendWatcher('pay_submit_wallet', { ANU_ENV });
            }
            if (isAlipayJsApi) {
                me.sendWatcher('paymentSubmit_jsApi');
            }
            // 临时埋点
            if (walletNotEnoughTraceFlag) {
                console.log('zyc paymentSubmit_wallet_not_enough');
                walletNotEnoughTraceFlag = true;
                me.sendWatcher('paymentSubmit_wallet_not_enough');
            }
        } else if (isSmsSubmit) {
            me.sendWatcher('paymentSubmit_sms');
        } else {
            me.sendWatcher('paymentSubmit_other');
        }
        me.sendWatcher('send303');
    } catch (e) {
        // eslint-disable-next-line no-console
        console.error(e);
    }

    // 清空钱包不足标志
    me.isWalletNotEnough = false;

    paywaySubmitModel({
        data: dataParam,
        h5plat,
        timeout: submitTimeOut,
        requestHead,
        context: {
            request: request,
            util: util,
            subEnv: 'fat5068',
            isOldQunarOrder,
        },
        success: async function (submitRes) {
            Loading.hide();
            me.sendWatcher('receive303');
            me.midstagePaying = false;
            paySubmitRes = submitRes;
            setPaySubmitRes(submitRes);
            setPaymentTraceId(submitRes.paymentTraceId || '');
            try {
                const filterCodes = [66];
                if (!filterCodes.includes(submitRes.head ? Number(submitRes.head.code) : 0)) {
                    paymentSubmitTraceFlag = false;
                }
            } catch (err) {
                me.sendWatcher('catch_error');
            }
            // 短信验证，弹出短信浮层
            if (submitRes.head && submitRes.head.code == '66' && submitRes.riskAndPwdInfos && submitRes.riskAndPwdInfos.find(i => i.verifyCodeType == 1) && submitCallBack) {
                submitCallBack({
                    ...submitRes,
                    tradeNo: submitRes.tradeNo || tempPayToken, 
                });
                return;
            }
            smsSubmitCallBack && smsSubmitCallBack();
            if (submitRes.head && submitRes.head.code == '5') {
                me.sendWatcher('wallet_not_enough');
                const modalContent = submitRes.head && submitRes.head.message || '您的钱包金额不足，已为您重新计算付款金额';
                HookApi.showModal({
                    content: modalContent,
                    confirmText: '知道了',
                    showCancel: false,
                    success: () => {
                        me.sendWatcher('wallet_not_enough_confirm');
                        me.isWalletNotEnough = true;
                        me.payWayMidstageHandler();
                    }
                });
                return;
            }
            // 直接成功
            if (submitRes.head && submitRes.head.code == 12) {
                me.payOptions.success({ code: '0', message: '支付成功', data: submitRes });
                me.payOptions.complete({ code: '3', message: '支付完成', data: submitRes });
                paySuccessLog.call(me);
                return;
            }
            let res = {};
            const isOldQunarOrder = getIsOldQunarOrder(me.payOptions.cashierUrl);
            // 支付前校验
            me.is_201_confirm = false;
            res = await handleCheckBeforePay({ res303: submitRes, wx: my, isDirect: me.payOptions.isDirect, payOptions: me.payOptions }) || {};
            // 要拦截
            if (!res.doNext) {
                // 201 变价，刷新页面
                if (res.type === 'init') {
                    me.sendWatcher('handleSubmitCallback_continue 201 变价-继续支付');
                    let newPayUrl = '';
                    if (res.qtouchExtendObj && res.qtouchExtendObj.newPayUrl) {
                        newPayUrl = res.qtouchExtendObj.newPayUrl;
                    } else {
                        newPayUrl = me.payOptions.cashierUrl.replace(tempPayToken, res.payToken);
                    }
                    tempPayToken = res.payToken;
                    me.is_201_confirm = true;
                    me.payOptions.afterShowPriceChangeModal && me.payOptions.afterShowPriceChangeModal();
                    //更新cashierurl并重新发起支付
                    me.updateMidstageData(newPayUrl);
                    me.payWayMidstageHandler();
                }
                // 201 变价取消， 返回
                else if (res.type === 'initCancel') {
                    me.sendWatcher('handleSubmitCallback_fail 201 变价-取消');
                    if (me.payOptions.isDirect && res.redirectUrl) {
                        CommonUtils.openWebviewRe({ url: res.redirectUrl });
                        return;
                    }
                    me.payOptions.fail({ code: '201', message: '变价取消', data: submitRes, showModal: false });
                }
                // 202 硬拦截
                else if (res.type === 'back') {
                    me.sendWatcher('handleSubmitCallback_fail 202 支付拦截');
                    if (me.payOptions.isDirect && res.redirectUrl) {
                        CommonUtils.openWebviewRe({ url: res.redirectUrl });
                    } else {
                        me.payOptions.fail({ code: '202', message: '支付拦截', data: submitRes, showModal: false });
                    }
                }
                // 203 软拦截 重新发起支付提交
                else if (res.type === 'notifyContinue') {
                    me.sendWatcher('handleSubmitCallback_notifyContinue 203 继续支付');
                    notifyContinue = '1';
                    recallInfo = res.recallInfo;
                    Loading.show('提交支付');
                    me.payMidstagePlugin(options);
                }
                else {
                    me.sendWatcher('handleSubmitCallback_checkBeforePay 支付前校验取消');
                    if (me.payOptions.isDirect && res.redirectUrl) {
                        CommonUtils.openWebviewRe({ url: res.redirectUrl });
                    } else {
                        me.payOptions.fail({ code: '200', message: '支付失败', data: submitRes, showModal: false });
                    }
                }
                return;
            }
            submitRes.quickPayType = quickPayType;
            submitRes.payToken = payToken;
            submitRes.buPayType = midstageBrandIdAndOldQMap[quickPayType];
            me.sendWatcher('midstage-request-submitPay');
            Loading.hide();
            handleSubmitCallback({
                h5plat,
                isNeedPushOrder: wxVideoData.isNeedPushOrder == 1,
                submitRes: submitRes,
                reactApi: my,
                extend: {
                    //支付查询信息
                    confirmModalFn: (cb) => {
                        showPayResutlModal(cb);
                    },
                    loading: Loading,
                    subEnv,
                    h5plat,
                    request: request,
                    util: util,
                },
                success: function (result) {
                    // return

                    if ((isWxType(quickPayType) && ANU_ENV === 'quick') || ANU_ENV == 'qq') {
                        setTimeout(function () {
                            getPayResult.call(me, result, me.payOptions);
                        }, 1000);
                    } else {
                        const { resultCode } = result;
                        me.resetSubmitStatus();
                        if (resultCode === 0) {
                            me.togglePayDlg(false);
                            me.sendWatcher('handleSubmitCallback_success');
                            paySuccessLog.call(me);
                            if (ANU_ENV === 'wx' && needRealNameGuide(submitRes, isOldQunarOrder)) {
                                me.sendWatcher('toguideauth');
                                toRealNameGuide(getAntiDisturbanceUrl(submitRes, isOldQunarOrder));
                                const authCallback = function () {
                                    setTimeout(function () {
                                        me.payOptions.success({ code: '0', message: '支付成功', data: result });
                                        me.payOptions.complete({ code: '3', message: '支付完成', data: result });
                                    }, 300);
                                };
                                addRealNameListener('onAuthCompleteToGuide', () => {
                                    authCallback();
                                });
                                return;
                            }
                            me.payOptions.success({ code: '0', message: '支付成功', data: result });
                            me.payOptions.complete({ code: '3', message: '支付完成', data: result });
                        } else if (resultCode === 201) {
                            const { extend = '' } = result;
                            submitPaayChangePrice.call(me, extend);
                        }
                    }
                },
                fail: function (result = {}) {
                    // return
                    // resultCode =0支付成功，1接口请求异常1001支付取消66中风控201支付变价<100支付内部错误>1000为调起三方支付内部错误
                    const { resultCode, resultMessage } = result;
                    reportErrorLog({
                        errorType: resultCode,
                        errorMessage: 'Q小程序拉起支付客户端异常',
                        extendInfo: { resultCode, payToken, result }
                    });
                    me.sendWatcher('handleSubmitCallback', result);
                    me.resetSubmitStatus();
                    if (resultCode === 66 || resultCode === 1) {
                        // me.payOptions.fail({code: '1', message: '支付失败', data:result});
                        me.showModal(resultMessage || '支付失败，如有疑问请联系客服', result); // 这里会调用fail  -401
                        me.sendWatcher('handleSubmitCallback_fengkong');
                    } else if (resultCode === 1001) {
                        me.payOptions.cancel({ code: '2', message: '支付取消', data: result });
                        me.payOptions.complete({ code: '3', message: '支付完成', data: result });
                    } else if (resultCode > 1000) {
                        me.showModal(resultMessage || '支付失败，如有疑问请联系客服', result); // 这里会调用fail -402
                    } else {
                        //code:202,203提示及其他异常提示
                        me.showModal(resultMessage || '支付未成功，请重试', result); // 531
                        me.sendWatcher('handleSubmitCallback_fail');
                    }
                }
            });
        },
        fail: (result) => {
            // return
            Loading.hide();
            paymentSubmitTraceFlag = false;
            me.sendWatcher('paywaySubmitModel_fail', result);
            reportErrorLog({
                errorType: '30013',
                errorMessage: '提交支付303返回fail',
                extendInfo: { result, payToken }
            });
            me.hasSubmitPay = false;
            me.showModal('网络异常，请重试', { message: '请求支付异常' }); // 503
        }
    }).excute();
}

async function getPayResult(res, payOptions) {
    const me = this;
    const { params: paymentParams } = me.payOptions.midstageData;
    const { h5plat } = paymentParams;
    const requestExtend = await getRequestExtend(me.payOptions.cashierUrl);
    const isOldQunarOrder = getIsOldQunarOrder(me.payOptions.cashierUrl);
    HookApi.showModal({
        title: '支付结果',
        content: '您是否已完成支付？',
        confirmText: '支付成功',
        cancelText: '未成功',
        success: () => {
            const extend = JSON.stringify({
                qextend: paySubmitRes.qextend
            });
            var dataParam = {
                'payToken': tempPayToken,
                'payRefNo': nonce303, //支付的流水号 303header中传入的nonce
                'payNo': paySubmitRes.payNo || '',
                extend,
            };
            payResModel({
                data: dataParam,
                h5plat,
                requestHead: {
                    extend: requestExtend,
                },
                context: {
                    request: request,
                    util: util,
                    isOldQunarOrder,
                    subEnv: 'fat5068',
                },
                success: function (res) {
                    me.hasSubmitPay = false;
                    if (res.head && res.head.code === 100000) {
                        me.togglePayDlg(false);
                        payOptions.success({ code: '0', message: '支付成功', data: res });
                        payOptions.complete({ code: '3', message: '支付完成', data: res });
                        me.sendWatcher('quick_wxpay_success');
                        paySuccessLog.call(me);
                    } else {
                        payOptions.cancel({ code: '2', message: '支付取消', data: res });
                        payOptions.complete({ code: '3', message: '支付完成', data: res });
                        me.sendWatcher('quick_wxpay_fail');
                    }
                },
                fail: function (res) {
                    me.hasSubmitPay = false;
                    payOptions.cancel({ code: '2', message: '支付取消', data: res });
                    payOptions.complete({ code: '3', message: '支付完成', data: res });
                    me.sendWatcher('quick_wxpay_fail');
                },
            }).excute();
        }
    });
}

async function payWayHandle() {
    const me = this;
    const cashierUrl = me.payOptions.cashierUrl;
    const ANU_ENV = process.env.ANU_ENV;
    paymentSubmitTraceFlag = false;

    const { payToken, isOldQunarOrder, params = {} } = me.payOptions.midstageData;
    tempPayToken = payToken;
    if (!payToken) {
        me.showModal('请求支付异常', { message: '缺少token' }); // 301
        me.sendWatcher(`${ANU_ENV}_midstageData_error`, { cashierUrl });
        reportErrorLog({
            errorType: '30001',
            errorMessage: 'payToken为空',
            extendInfo: me.payOptions
        });
        return;
    }
    
    if (!me.payOptions.openId){
        const initUserData = await util.getGlobalInfoAsync() || {};
        const { cookies={}, user={} } = initUserData;
        me.payOptions.openId = cookies.openId || user.openId;
        if (!me.payOptions.openId) {
            me.sendWatcher('payWayHandle_get_openid_error');
            reportErrorLog({
                errorType: '30003',
                errorMessage: 'openId为空',
                extendInfo: initUserData
            });
        }
    }

    // const { h5plat } = params;
    const startTime = new Date();
    Loading.show('正在跳转');
    //微信视频号
    if (ANU_ENV === 'wx') {
        try {
            await new Promise(function (resolve, reject) {
                my.checkBeforeAddOrder({
                    success(res) {
                        //1代表视频号
                        if (res.data && res.data.requireOrder == 1) {
                            me.sendWatcher(`${ANU_ENV}_checkBeforeAddOrder_sucess`, res.data);
                            params.h5plat = 55;
                            wxVideoData.RequiredFundType = res.data.requiredFundType || '';
                            wxVideoData.TraceId = res.data.traceId || '';
                            wxVideoData.isNeedPushOrder = 1;

                        } else {
                            params.h5plat = 29;
                            me.sendWatcher(`${ANU_ENV}_checkBeforeAddOrder_notVideo`);

                        }
                        resolve({});
                    },
                    fail(res) {
                        me.sendWatcher(`${ANU_ENV}_checkBeforeAddOrder_fail`, res);
                        reject({});
                    }
                });
            });
        } catch (error) {
            me.sendWatcher(`${ANU_ENV}_checkBeforeAddOrder_unuse`);
        }
    }

    me.sendWatcher('send102');

    paywayModel({
        data: {
            payToken: payToken,
        },
        h5plat: params.h5plat,
        requestHead: {
            ...(isOldQunarOrder ? { extend: await getRequestExtend(cashierUrl) } : {
                extend: JSON.stringify({
                    pageTraceId: getPageTraceId()
                })
            }),
        },
        context: {
            request: request,
            util: util,
            isOldQunarOrder: isOldQunarOrder,
            subEnv: 'fat5068',
        },
        success: function (res) {
            me.sendWatcher('receive102');
            me.reportTraceLog({
                status102: res && res.head && res.head.code,
                reqPaywayTime: new Date().getTime() - startTime.getTime()
            });
            handlePaymentCallback(res).then(() => {
                thirdPartyList = getThirdsList(res) || [];
                walletWay = getWalletDisplayWay(res);
                paylistRes = res;
                afterGetPayWay.call(me, res);
                me.sendWatcher(`${ANU_ENV}_paywayModel_success`);
            }).catch((data = {}) => {
                const { status, message } = data;
                Loading.hide();
                // 订单已支付
                if (status === 1) {
                    me.showModal(message, {}, () => {
                        me.payOptions.success({ code: '0', message: '支付成功', data });
                        paySuccessLog.call(me);
                    });
                } else {
                    me.showModal(message || '请求支付异常', data); // 505
                    me.sendWatcher(`${ANU_ENV}_paywayModel_exception`, { ...data });
                    reportErrorLog({
                        errorType: '30014',
                        errorMessage: '获取支付方式102返回异常',
                        extendInfo: { data, payToken }
                    });
                }
            });
        },
        fail: (e) => {
            me.showModal('网络异常，请重试', { message: '获取支付方式列表异常' }); // 504
            Loading.hide();
            me.sendWatcher(`${ANU_ENV}_paywayModel_fail`);
            reportErrorLog({
                errorType: '30002',
                errorMessage: '102 网络错误',
                payToken: tempPayToken,
                extendInfo: e
            });
        },
        complete: () => { }
    }).excute();
}
// 获取支付方式后的处理，或调起，或展示列表框
function afterGetPayWay(res) {
    const thirdPartyList = getThirdsList(res) || [];
    const me = this;
    const cashierUrl = me.payOptions.cashierUrl;
    const { params, isOldQunarOrder, payToken } = me.payOptions.midstageData;
    const canSubmitPaymentId = params.brandId;
    if (thirdPartyList.length >= 1) {
        me.sendWatcher('cashierShowDone');
        const currentPayment = getCurrentPayment(thirdPartyList, canSubmitPaymentId);
        if (currentPayment) {
            const walletWay = getWalletDisplayWay(res);
            const abTest = getAbTest(res);
            const abTrue = abTest['240428_alipay_account'] == 'B';
            // const abTrue = true;
            // 微信全量放开，支付宝 AB 分流
            const needJumpCashier = (ANU_ENV == 'wx' || (ANU_ENV == 'ali' && abTrue)) && walletWay && !isOldQunarOrder;
            walletNotEnoughTraceFlag = me.isWalletNotEnough;
            if (me.isJumpMidstageCashier || needJumpCashier) {
                me.isJumpMidstageCashier = true; // 满足条件进过一次收银台，后面重新请求 102 都直接显示收银台（防止余额不足刷新页面走不到这里）
                me.sendWatcher('pay_start_wallet', { ANU_ENV });
                Loading.hide();
                // 直连：返回展示收银台
                if (me.payOptions.isDirect) {
                    me.payOptions.doDirect({ res_102: res, walletWay });
                }
                // api： 跳转收银台
                else {
                    me.isApiJumpCashier = true;
                    const url = `/pages/qunar/subPages/alonePlatform/pay/index?cashierUrl=${encodeURIComponent(cashierUrl)}&status_102=2`;
                    // 变价或钱包余额不足，走重定向
                    if (me.is_201_confirm || me.isWalletNotEnough) {
                        me.isProcessEnd = true; // 重定向认为当前流程结束，redirect 后是新的流程；因重定向导致的页面销毁也不需要触发 cancel 回调
                        me.isWalletNotEnough = false; // 用完清空
                        my.redirectTo({
                            url
                        });
                    } else {
                        my.navigateTo({
                            url
                        });
                    }
                }
                return;
            } else {
                Loading.show('提交支付');
                me.payMidstagePlugin({ payToken, paymentWayToken: currentPayment.paymentWayToken, paywayModelData: res });
            }
        } else {
            Loading.hide();
            // 微信直连模式没有支付方式展示页面，支付宝因为无法拦截返回按钮，通过弹框提示
            if (ANU_ENV == 'wx' && me.payOptions.isDirect && me.payOptions.doDirect) {
                me.payOptions.doDirect({ res_102: res, isNoPaymentWay: true });
            } else {
                me.showModal('小程序端暂无法支付，请到APP端支付');
            }
            reportErrorLog({
                errorType: '30012_2',
                errorMessage: '下发支付方式不对',
                payToken,
            });
        }
    } else {
        Loading.hide();
        // 微信直连模式没有支付方式展示页面，支付宝因为无法拦截返回按钮，通过弹框提示
        if (ANU_ENV == 'wx' && me.payOptions.isDirect && me.payOptions.doDirect) {
            me.payOptions.doDirect({ res_102: res, isNoPaymentWay: true });
        } else {
            me.showModal('小程序端暂无法支付，请到APP端支付', { message: '没有下发对应的支付方式' });
        }
        me.sendWatcher(`${ANU_ENV}_paywayModel_not_thirdList`);
        reportErrorLog({
            errorType: '30012_3',
            errorMessage: '没有下发支付方式',
            payToken,
        });
    }
}

// 提交支付后变价
function submitPaayChangePrice(submitExtend) {
    const me = this;
    Loading.hide();
    if (ANU_ENV === 'wx' && me.payOptions.supportChangePrice && me.payOptions.midstageData.isOldQunarOrder) {
        const { qtouchExtend } = JSON.parse(submitExtend || '');
        let qExtendObj = {};
        if (qtouchExtend) {
            try {
                qExtendObj = JSON.parse(qtouchExtend);
            } catch (e) {
                qExtendObj = {};
            }
        }
        me.payOptions.beforeShowPriceChangeModal();
        me.showModal(qExtendObj.changePrice, null, function () {
            me.payOptions.afterShowPriceChangeModal();
            //更新cashierurl并重新发起支付
            me.updateMidstageData(qExtendObj.newPayUrl);
            me.payWayMidstageHandler();
        });
    } else {
        this.showModal('订单已变价，无法支付，请重新提交订单');
    }
}

// 查询支付结果：支付回退挽留专用（没有payNo,必定失败，需要后端兼容)
function getPayRes(param) {
    const me = this;
    const dataParam = {
        'payToken': param.payToken || tempPayToken,
    };
    const { params: paymentParams } = me.payOptions.midstageData;
    const { h5plat } = paymentParams;
    Loading.show('查询中……');
    payResModel({
        data: dataParam,
        h5plat,
        context: {
            request: request,
            util: util,
            subEnv: 'fat5068',
        },
        success: function (res) {
            Loading.hide();
            if (res.head && res.head.code === 100000) {
                param.success && param.success({
                    payOptions: me.payOptions,
                    isApiJumpCashier: me.isApiJumpCashier,
                    code: '0'
                });
            } else {
                param.fail && param.fail({
                    payOptions: me.payOptions,
                    isApiJumpCashier: me.isApiJumpCashier,
                    code: '2'
                });
            }
        },
        fail: function () {
            Loading.hide();
            param.fail && param.fail({
                payOptions: me.payOptions,
                isApiJumpCashier: me.isApiJumpCashier,
                code: '2'
            });
        },
    }).excute();
}

function getRes_102() {
    return {
        res_102: paylistRes,
        walletWay
    };
}

export default {
    toPay,
    payWayHandle,
    payMidstageData,
    toPayWithType,
    getPayRes,
    getRes_102,
};
