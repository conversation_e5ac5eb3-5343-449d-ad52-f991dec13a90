import util from '../../util.js';
let paymentTraceId = '';

export const getPaymentTraceId = () => {
    return paymentTraceId;
};

export const createPaymentTraceId = () => {
    paymentTraceId = util.getUniid();
    return paymentTraceId;
};

export const getOrCreatePaymentTraceId = () => {
    paymentTraceId = paymentTraceId || util.getUniid();
    return paymentTraceId;
};

export const setPaymentTraceId = (id) => {
    paymentTraceId = id;
};

export const clearPaymentTraceId = () => {
    paymentTraceId = '';
};
