import EventEmitter from '../../EventEmitter';

export const toRealNameGuide = (DisturbanceUrl) => {
    const url = `/pages/alonePlatform/realNameGuide/index?disturbanceUrl=${ encodeURIComponent(DisturbanceUrl)}`;
    my.navigateTo({ 
        url,
        complete: () => {
            my.hideLoading();
        }
    });
};
function getMemberRealNameGuide (data) {
    const { paySuccessGuide = {} } =  data || {};
    const { memberRealNameGuide = {} } = paySuccessGuide;
    return memberRealNameGuide;
}

export function getAntiDisturbanceUrl(payResultData, isOldQunarOrder) {
    if (!isOldQunarOrder) {
        return '';
    }
    const { extend = '' } = payResultData;
    try {
        return getMemberRealNameGuide(JSON.parse(extend)).realNameInfoQueryUrl || '';
    } catch (e) {
        return '';
    }
}
// 老q实名引导判断
function judgeGuideAuth(data) {
    const memberRealNameGuide = getMemberRealNameGuide(data);
    const { isNeedGuide } = memberRealNameGuide;
    return isNeedGuide == 'true';
}
export function addRealNameListener(eventName, callback) {
    EventEmitter.removeListener(eventName);
    EventEmitter.addListener(eventName, callback);
}

export function needRealNameGuide(payResultData, isOldQunarOrder) {
    const { extend = '' , guideInfos = [] } = payResultData;
    if (isOldQunarOrder && extend) {
        try {
            return judgeGuideAuth(JSON.parse(extend));
        } catch (e) {
            return false;
        }
    }
    //中台订单返回
    let hasGuide = false;
    try {
        hasGuide = guideInfos.findIndex(i => i && i.realNameGuide && i.realNameGuide.realNameType === 4) > -1;
    } catch (e) { }
    return hasGuide;
}