/*
 * 平台：支付宝小程序
 * 支付方式：支付宝支付
*/

// 乎起第三方（支付宝）支付页面
function toPay(data) {
    let options = {};
    const me = this;
    const payType = data.payType || '';
    const paytypeText = (payType === 'alipayRealPreAuthPlugin') ? '预授权' : '支付';
    options.success = function(res){
        // 支付宝小程序 根据resultCode执行相应回调
        const resultCode = res.resultCode || '';
        switch (resultCode) {
            case '9000':  // 成功
                me.payOptions.success({code: '0', message: `${paytypeText}成功`, data:res});
                me.sendWatcher('aliPay_success');
                break;
            case '4000':  // 失败
                me.payOptions.fail({code: '1', message: `${paytypeText}失败`, data:res});
                me.sendWatcher('pay_fail');
                break;
            case '6001':  // 取消
                me.payOptions.cancel({code: '2', message: `取消${paytypeText}`, data:res});
                me.sendWatcher('pay_cancel');
                break;
            default:
                me.payOptions.success({code: '0', message: `${paytypeText}结果未知`, data:res});
                me.sendWatcher('aliPay_unknown');
                break;
        }
    };
    options.fail = function(res){
        me.payOptions.fail({code: '1', message: `${paytypeText}失败`, data:res});
        me.sendWatcher('pay_fail');
    };
    options.complete = function(res){
        me.payOptions.complete({code: '3', message: `${paytypeText}完成`, data:res});
        me.sendWatcher('pay_complete');
    };
    if (data.tradeNo){
        options.tradeNO = data.tradeNo; //后端接入的是支付宝小程序支付
    } else if (data.payURL){
        options.orderStr = data.payURL;//后端接入的是支付宝app支付 or 支付宝预授权
    }
    my.tradePay(options);
}


function payWayHandle(data) {
    // 方便beta联调时，错误提示
    if (process.env.BUILD_ENV === 'beta' && data.qmpChannel !== 'qmpalipaysapp') {
        this.showModal('qmpChannel传参错误，应为qmpalipaysapp');
        return;
    }
    const payInfo = data.dataObj || {};
    const payTypeNames = payInfo.index || [];
    const hasAliPay = (payTypeNames.indexOf('aliPayPlugin') >= 0);
    const hasAliPreAuth = (payTypeNames.indexOf('alipayRealPreAuthPlugin') >= 0); 
    if (payTypeNames.length == 1 && (hasAliPay || hasAliPreAuth)) {
        this.private_submitPay(payTypeNames[0]);
    } else {
        this.showModal('支付方式配置不正确', data); // 没有支付方式
        this.sendWatcher('no_pay_way');
    }
}

export default {
    toPay,
    payWayHandle
};