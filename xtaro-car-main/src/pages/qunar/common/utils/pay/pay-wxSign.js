import watcher from '../oldWatcher';
import nanachi_request from '../request.js';


var signOptions;

function getParam(){
    return {
        version: getQueryString(signOptions.signWechatUrl,'version'),
        busiTypeId: getQueryString(signOptions.signWechatUrl,'busiTypeId'),
        token: getQueryString(signOptions.signWechatUrl,'token'),
        appId: getQueryString(signOptions.signWechatUrl,'appid')
    };
}

function getQueryString(url, name) {
    if (!name || !url) {
        return;
    }
    var result = url.match(new RegExp('[?&]' + name + '=([^&]+)' , 'i'));
    if (result != null && result.length > 0 && result[1] != null){
        return result[1];
    }
}

function requestSignStatus(url, successCallback, failCallBack){
    let [, host, service] = url.match(/(^https?:\/\/[^/]*)(\/.*$)/);
    nanachi_request({
        host,
        service,
        data: getParam(),
        method: 'POST',
        ignoreStatus: true,
        header: { 
            'content-Type': 'application/x-www-form-urlencoded'
        },
        success: res => {
            successCallback && successCallback(res);
        },
        fail: res => {
            failCallBack && failCallBack(res);
        }
    });
}

function signWechatPay(){
    var url = signOptions.signWechatUrl.split('?')[0];
    requestSignStatus(url, function(res){
        if ( res && res.status === 1 && res.signWechatMiniParam){   //未签约，返回签约参数
            signWxWatcher('signWechatPay_jumpWxSign',{ res: res }); 
            signOptions.getSignParam(JSON.parse(res.signWechatMiniParam));
        } else if ( res && res.status === 0 && res.protocolNo) {         //已签约，返回协议号
            res.signStatus = false;    //已经签约，不需要呼起签约
            signOptions.success(res);
            signWxWatcher('signWechatPay_alreadySign',{ res: res });
        } else {                              //不允许签约,status=0  
            signOptions.fail(res);
            signWxWatcher('signWechatPay_fail',{ res: res });
        }
    },function(res){   
        signOptions.fail(res);
        signWxWatcher('signWechatPay_fail',{ res: res });
    });
}
function empty() {}
function contractWechatPay(options){
    signOptions = options || {};
    signOptions.signWechatUrl = signOptions.signWechatUrl || empty;
    signOptions.success = signOptions.success || empty;
    signOptions.fail = signOptions.fail || empty;
    signOptions.cancel = signOptions.cancel || empty;

    signWechatPay();
}

function showWechatSignResult(res){
    let { appId, extraData } = res.referrerInfo || {};
    //只有微信签约小程序返回成功时，才进行查询
    if (appId == 'wxbd687630cd02ce1d') { // appId为wxbd687630cd02ce1d：从签约小程序跳转回来
        if (extraData && extraData.return_code == 'SUCCESS'){ // 客户端小程序签约成功，需要向商户侧后台请求确认签约结果
            onlyQuerySignStatus();
        } else {
            signOptions.fail(transformWxSignResult(extraData));
        }
    }
}

function onlyQuerySignStatus(retryCount){
    retryCount = retryCount || 0;
    var url = signOptions.signWechatUrl.split('?')[0];
    requestSignStatus(url, function(res){
        if ( res && res.status === 0 && res.protocolNo) {     //已签约，返回协议号
            res.signStatus = true;    //呼起签约后的回调
            signOptions.success(res);
            signWxWatcher('onlyQuerySignStatus_success',{ res:res });
        } else {
            querySignStatusRetry(retryCount, res); //前3次失败则重试
        }
    },function(res){
        querySignStatusRetry(retryCount, res);   //前3次失败则重试
    });
}
//如果获取不到数据、需要重试
function querySignStatusRetry(retryCount, res){    
    retryCount = retryCount || 0;
    if (retryCount < 3){
        retryCount = retryCount + 1;
        setTimeout(function(){
            onlyQuerySignStatus(retryCount);
            signWxWatcher('querySignStatusRetry',{ res:res });
        }, 1000);
    } else {
        signOptions.fail(res);
        signWxWatcher('onlyQuerySignStatus_fail',{ res:res });
    }
}

function transformWxSignResult(extraData){
    extraData = extraData || { return_msg: '取消签约', return_code: 'cancel' };
    return {
        data : {
            errMsg : extraData.return_msg,
            errCode : extraData.return_code,
            status : 2 //微信免密代扣签约失败状态
        }
    };
}

function signWxWatcher(actionType, exParams) {
    var opts = exParams || {};
    opts['action-type'] = actionType;
    opts['page'] = 'signWxContractPage';
    watcher.click(opts);
}

export default {
    contractWechatPay,
    showWechatSignResult
};