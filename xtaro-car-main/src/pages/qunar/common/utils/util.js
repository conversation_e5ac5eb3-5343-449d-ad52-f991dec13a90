// import watcher from './watcher';
import pageRouterMap from './pageNotFoundRouterMap';
// 为了holidayDate文件通过代码质量检测，需要在主包引用
import { holidays } from './holidayDate';
import HookApi from './hookApi';

// 返回全局对象
function getGlobalInfo() {
    const json = HookApi.getStorageSync('UserData');
    if (!json || json === '[object Object]') {
        // const err = new Error(`util.js getGlobalInfo 111 >>> __${json}__`);
        // Promise.reject(err);
        return { user: {}, cookies: {} };
    }
    if (typeof (json) === 'string') {
        try {
            return JSON.parse(HookApi.getStorageSync('UserData'));
        } catch (err) {
            /**
             * 目前(2021-02-22)没有手动上报错误的api
             * Promise.reject 会被自动上报捕捉
             */
            err.message = 'util.js getGlobalInfo 222 >>> ' + err.message;
            Promise.reject(err);

            return { user: {}, cookies: {} };
        }

    }
    return HookApi.getStorageSync('UserData');
}
// 快应用 异步
async function getGlobalInfoAsync() {
    return new Promise((resolve, reject) => {
        my.getStorage({
            key: 'UserData',
            success: res => {
                const storage = res.data || { user: {}, cookies: {}, extraCookie: {} };
                if (typeof (storage) === 'string') {
                    resolve(JSON.parse(storage));
                }
                resolve(storage);
            },
            fail: err => {
                // 这里为了兼容之前nnc getStorage被重写的逻辑
                resolve({ user: {}, cookies: {}, extraCookie: {} });
            }
        });
    });
}
async function getGlobalInfoAsyncByKey(key) {
    return new Promise((resolve, reject) => {
        my.getStorage({
            key,
            success: res => {
                resolve(res.data);
            },
            fail: err => {
                // 这里为了兼容之前nnc getStorage被重写的逻辑
                resolve(undefined);
            }
        });
    });
}
// 返回系统信息 快应用没有同步方法 首页获取会因底bar导致高度拿错
async function getSystemInfo() {
    return new Promise((resolve, reject) => {
        my.getSystemInfo({
            success: res => {
                resolve(res);
            },
            fail: err => {
                reject(err);
            }
        });
    });
}

Date.prototype.format = function (fmt) {
    var o = {
        'M+': this.getMonth() + 1, //月份
        'd+': this.getDate(), //日
        'h+': this.getHours(), //小时
        'm+': this.getMinutes(), //分
        's+': this.getSeconds(), //秒
        'q+': Math.floor((this.getMonth() + 3) / 3), //季度
        S: this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, `${this.getFullYear()}`.substr(4 - RegExp.$1.length));
    }
    for (var k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1,
                RegExp.$1.length == 1 ? o[k] : `00${o[k]}`.substr(`${o[k]}`.length)
            );
        }
    }
    return fmt;
};

function isEmptyString(property) {
    return !property || property.length === 0;
}

function isEmptyArray(array) {
    return !array || array.length === 0;
}

function isEmptyObject(obj) {
    for (var key in obj) {
        return !key;
    }
    return true;
}

function isValidPhone(phoneNumber) {
    return phoneNumber.length == 11 && phoneNumber.charAt(0) == '1';
}

function stringifyURLParam(param) {
    var rstString = '';
    for (var key in param) {
        rstString += `${key}=${param[key]}&`;
    }
    rstString = rstString.substr(0, rstString.length - 1);
    return rstString;
}

function parseURLParam(param) {
    var rst = {};
    for (var key in param) {
        rst[key] = JSON.parse(param[key]);
    }
    return rst;
}

function mixin(sub = {}, sup = {}) {
    for (const name in sup) {
        if (name == 'data') {
            sub.data = Object.assign(sub.data || {}, sup.data);
        } else if (sub[name] === undefined) {
            sub[name] = sup[name];
        }
    }

    return sub;
}

function queryToParam(query = '') {
    const params = {};
    const arr = query.split('&');
    let key, value;
    for (var i = 0; i < arr.length; i++) {
        [key = '', value = ''] = arr[i].split('=');
        // 给对象赋值
        params[key] = value;
    }
    return params;
}

function queryToCookie(query) {
    const params = {};
    const arr = query.split(';');
    let key, value;
    for (var i = 0; i < arr.length; i++) {
        const _index = arr[i].indexOf('=');
        key = arr[i].slice(0, _index);
        value = arr[i].slice(_index + 1);
        // 给对象赋值
        params[key] = value;
    }
    return params;
}


// -------- START --------
// FD-185849 解决 cookie 解析问题
// 全新解析 cookie 的工具函数，参考了开源库 set-cookie-parser
// 使用方式：解除注释，把上边的 queryToCookie 注释上即可
// 另外需要修改 request 上的 setResponseCookid 中的 setCookieString = setCookieString + v + ';'; 把分号改为 ',' 才多符合请求头拼接规范
// function isNonEmptyString(str) {
//   return typeof str === "string" && !!str.trim();
// }

// // "_i=ShcRyxiucDJ551;Domain=.qunar.com;Expires=Sat,01-Sep-209110:00:40GMT',"
// // ->
// // { name: '_i', value: 'ShcRyxiucDJ551, domain: '.qunar.com;', expires: 'xxxxx'}
// function parseStringWithoutOptions(setCookieValue) {
//     let parts = setCookieValue.split(";").filter(isNonEmptyString);
  
//     let nameValuePairStr = parts.shift();
//     let parsed = parseNameValuePair(nameValuePairStr);

//     // 因为实际 cookie 存在 storage 中，所以我们默认不对 cookie.value 进行编码
//     // 另外对于 setCookieValue 中每个 cookie 额外的 options，因为小程序的原因，用不上所以也不需要存储
//     let name = parsed.name;
//     let value = parsed.value;
  
//     let cookie = {
//       name: name,
//       value: value,
//     };
  
//     return cookie;
//   }

// function parseNameValuePair(nameValuePairStr) {

//   let name = "";
//   let value = "";
//   let nameValueArr = nameValuePairStr.split("=");
//   if (nameValueArr.length > 1) {
//     name = nameValueArr.shift();
//     value = nameValueArr.join("=");
//   } else {
//     value = nameValuePairStr;
//   }

//   return { name: name, value: value };
// }

// // QN42=jh8w8m;Domain=.qunar.com;Path=/,_s=s_TRU6PNZODGRH;Domain=.qunar.com;Path=/,
// // ->
// // [ 'QN42=jh8w8m;Domain=.qunar.com;Path=/', '_s=s_TRU6PNZODGRH;Domain=.qunar.com;Path=/']
// // 支持 expires path doamin max-age secure httponly samesite，支持跳过任意空格
// function splitCookiesString(cookiesString) {
//   if (Array.isArray(cookiesString)) {
//     return cookiesString;
//   }
//   if (typeof cookiesString !== "string") {
//     return [];
//   }

//   // 主要识别方式：逐字符判断，双指针 start & pos
//   let cookiesStrings = [];
//   let pos = 0;
//   let start;
//   let ch;
//   let lastComma;
//   let nextStart;
//   let cookiesSeparatorFound;

//   function skipWhitespace() {
//     while (pos < cookiesString.length && /\s/.test(cookiesString.charAt(pos))) {
//       pos += 1;
//     }
//     return pos < cookiesString.length;
//   }

//   function notSpecialChar() {
//     ch = cookiesString.charAt(pos);

//     return ch !== "=" && ch !== ";" && ch !== ",";
//   }

//   while (pos < cookiesString.length) {
//     start = pos;
//     cookiesSeparatorFound = false;

//     while (skipWhitespace()) {
//       ch = cookiesString.charAt(pos);
//       if (ch === ",") {
//         lastComma = pos;
//         pos += 1;

//         skipWhitespace();
//         nextStart = pos;

//         while (pos < cookiesString.length && notSpecialChar()) {
//           pos += 1;
//         }

//         if (pos < cookiesString.length && cookiesString.charAt(pos) === "=") {
//           cookiesSeparatorFound = true;
//           pos = nextStart;
//           cookiesStrings.push(cookiesString.substring(start, lastComma));
//           start = pos;
//         } else {
//           pos = lastComma + 1;
//         }
//       } else {
//         pos += 1;
//       }
//     }

//     if (!cookiesSeparatorFound || pos >= cookiesString.length) {
//       cookiesStrings.push(cookiesString.substring(start, cookiesString.length));
//     }
//   }

//   return cookiesStrings;
// }

// function queryToCookie(query) {
//     const params = {};

//     try {
//         const splitCookies = splitCookiesString(query);
//         splitCookies.forEach((item) => {
//             const {name, value} = parseStringWithoutOptions(item);
//             params[name] = value;
//         })
//     }catch (e) {
//         console.log(e);
//         return {};
//     }

//     return params;
// }

// -------- END --------

var debounce = function (func, wait, immediate) {
    var timeout, args, context, timestamp, result;

    var later = function () {
        // 据上一次触发时间间隔
        var last = new Date() - timestamp;

        // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
        if (last < wait && last > 0) {
            timeout = setTimeout(later, wait - last);
        } else {
            timeout = null;
            // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
            if (!immediate) {
                result = func.apply(context, args);
                if (!timeout) {
                    context = args = null;
                }
            }
        }
    };

    return function () {
        context = this;
        args = arguments;
        timestamp = new Date();
        var callNow = immediate && !timeout;
        // 如果延时不存在，重新设定延时
        if (!timeout) {
            timeout = setTimeout(later, wait);
        }
        if (callNow) {
            result = func.apply(context, args);
            context = args = null;
        }

        return result;
    };
};
// [{A: []}, {B: []}] ==> [{key: 'A', value: []}, {key: 'B', value: []}]
var arrayTransform = function (arr) {
    return arr.map(function (item) {
        let obj = {};
        for (let i in item) {
            obj.key = i;
            obj.value = item[i];
            break;
        }

        return obj;
    });
};

function json_parse(data) {
    try {
        return JSON.parse(data);
    } catch (e) {
        return data;
    }
}


function openWebview({ url = '', params = {}, loginSync = false }) {

    if (Object.keys(params).length) {
        url += url.indexOf('?') !== -1 ? '&' : '?' + stringifyURLParam(params);
    }


    url = encodeURIComponent(url);
    let type = 'webView';
    my.navigateTo({
        url: `/pages/qunar/subPages/alonePlatform/${type}/index?url=${url}&loginSync=${loginSync}`
    });
}

function openWebviewRe({ url = '', params = {}, loginSync = false, webViewBaseUrl = '' }) {

    if (Object.keys(params).length) {
        url += url.indexOf('?') !== -1 ? '&' : '?' + stringifyURLParam(params);
    }


    url = encodeURIComponent(url);
    let type = 'webView';
    let _webViewBaseUrl = webViewBaseUrl || `/pages/qunar/subPages/alonePlatform/${type}/index`;
    my.redirectTo({
        url: `${_webViewBaseUrl}?url=${url}&loginSync=${loginSync}`
    });
}

function decodeURL(url) {
    // eslint-disable-next-line
    while (1) {
        if (url.indexOf('://') >= 0) {
            return url;
        }

        if (url === decodeURIComponent(url)) {
            return url;
        }
        url = decodeURIComponent(url);

    }
}

function toPay({ cashierUrl }) {

    cashierUrl = encodeURIComponent(cashierUrl);

    my.navigateTo({
        url: `/pages/qunar/subPages/alonePlatform/pay/index?cashierUrl=${cashierUrl}`
    });
}

// copy自度假项目
// iphone机型model定义，详见：
// https://everyi.com/by-identifier/ipod-iphone-ipad-specs-by-model-identifier.html
const iphoneModelMap = {
    JUST_IPHONE: '',
    5: '(5[^\\d]?[1-4]|6[^\\d]?[1-2])',
    6: '(7[^\\d]?[1-2]|8[^\\d]?[1-2])',
    SE: '8[^\\d]?4',
    7: '9[^\\d]?[1-4]',
    8: '10[^\\d]?[1-24-5]',
    X: '(10[^\\d]?[36]|11[^\\d]?[2468])' // x xs xsmax xr
};

// 传具体型号（见上面key），或者不传（判断是否iphone）
const isIphone = async (model = 'JUST_IPHONE') => {
    model = model.toUpperCase();
    if (!(model in iphoneModelMap)) {
        throw new Error('暂未收录该种iPhone机型');
    }
    const cacheKey = 'iph' + model;
    const iPhoneRegExp = new RegExp('iPhone\\s?' + iphoneModelMap[model], 'i');
    const systemInfo = await getSystemInfo();
    const systemModel = systemInfo.model;
    const r = isIphone[cacheKey] = iPhoneRegExp.test(systemModel);
    return r;
};
const isIphoneX = async () => await isIphone('X');

// 需要绑定到React组件实例上
const setIsIphoneX = async function () {
    const isIpx = await isIphoneX();
    this.setState({
        isIphoneX: isIpx
    });
};

// 兼容API处理
export const makePhoneCall = async (phoneNumber, title) => {
    const isIph = await isIphone();
    !isIph ?
        HookApi.showModal({
            title: title || '拨打号码',
            content: phoneNumber,
            cancelText: '取消',
            confirmText: '呼叫',
            success(res) {
                if (res.confirm) {
                    HookApi.makePhoneCall({
                        phoneNumber
                    });
                }
            }
        }) : HookApi.makePhoneCall({
            phoneNumber
        });
};

// 处理微信  订单路径转换
function fixWxOrderUrl(url) {
    const page = url.slice(0, url.indexOf('?'));
    const query = url.slice(url.indexOf('?'));
    let u;
    switch (page) {
        case '/hotel/pages/hotel/hotel-order-detail/hotel-order-detail':
            u = '/pages/hotel/pages/hotel-order-detail/hotel-order-detail';
            break;
        case '/train/pages/orderDetail/orderDetail':
            u = '/pages/vice_train/orderDetail/index';
            break;
        case '/vacation/pages/orderDetail/index':
            u = '/pages/vacation/orderDetail/index';
            break;
        case '/ticket/pages/orderdetail/orderdetail':
            u = '/pages/ticket/orderDetail/index';
            break;
        case '/pages/business/bus/pages/orderDetail/orderDetail':
            u = '/pages/bus/orderDetail/index';
            break;
        case '/pages/business/flight/orderDetail/orderDetail':
            u = '/flight/pages/orderDetail/orderDetail';
            break;
        default:
            u = page;
            break;

    }
    return (u + query);
}
//  兼容老微信的bdo
const BD_ORIGIN_DEFAULT = 'common'; // bd_origin 默认值，该值永不过期
const BD_ORIGIN_STORAGE = 'bd_origin';
const bdOrigin = {
    /**
     * 设置bd_origin
     * @param  {String} bdOrigin 将要设置的bd_origin
     * 如果没传bdOrigin，则存为默认值，该值永不过期
     * 如果传过来bdOrigin，不管已有的bd_origin 和传过来的是不是一样，都会更新生命周期
     * 理论上下单之前的页面都应该在onLoad的时候调用该函数
     * utils.bdOrigin.setV(params.bd_origin);
     */
    setV(bdOrigin) {
        if (process.env.ANU_ENV !== 'wx') return;
        let originObj = { bdOrigin: this.getV() };
        if (bdOrigin && bdOrigin !== BD_ORIGIN_DEFAULT) {
            originObj.bdOrigin = bdOrigin;
        }
        let bdOriginContent = JSON.stringify(originObj);

        HookApi.setStorageSync(BD_ORIGIN_STORAGE, bdOriginContent);


    },
    /**
     * 获取bd_origin
     * @return {String} bd_origin
     * 1. 默认值 -> 返回默认值
     * 2. 非默认值
     *     1. 未过期 -> 返回
     *     2. 过期 -> 返回默认值
     * 需要获取的时候调用。
     */
    getV() {

        if (process.env.ANU_ENV !== 'wx') {
            let { bd_origin } = (getApp() || {}).globalData;
            return bd_origin;
        }
        let bdOriginContent, originObj;
        // let currentTime = new Date().getTime();
        try {
            bdOriginContent = HookApi.getStorageSync(BD_ORIGIN_STORAGE);
        } catch (e) {
            throw e;
        }
        try {
            originObj = JSON.parse(bdOriginContent);
        } catch (e) {
            originObj = {
                bdOrigin: BD_ORIGIN_DEFAULT
            };
        }

        return (originObj && originObj.bdOrigin);

    }
};

function fixWxFailUrl(url) {
    let [page, query] = url.split('?');
    let first = url.indexOf('/') === 0 ? 1 : 0;
    page = page.slice(first);
    let u;

    let routerMap = HookApi.getStorageSync('pageNotFoundRouterMap') || pageRouterMap;
    if (routerMap[page]) {
        u = [routerMap[page], query].join('?');
    } else {
        u = url;
    }
    return u;
}
function setTimeStorage(key, value, time) {
    if (!(key && value)) return;
    let json = HookApi.getStorageSync('timeStorage') || {};
    json[key] = {
        data: value,
        nowDate: +new Date(),
        endTime: +new Date() + 86400000 * time
    };
    HookApi.setStorageSync('timeStorage', json);
}
function getTimeStorage(key) {
    let json = HookApi.getStorageSync('timeStorage') || {};
    return json[key] && json[key].data || {};
}
function clearTimeStorage() {
    const json = HookApi.getStorageSync('timeStorage');
    if (!json || typeof (json) !== 'object') return;
    for (let key in json) {
        if (+new Date() >= json[key].endTime) {
            delete json[key];
        }
    }
    HookApi.setStorageSync('timeStorage', json);
}
const getNetworkType = new Promise(resolve => {
    my.getNetworkType({
        success(res) {
            const networkType = res.networkType;
            resolve(networkType);
        },
        fail() {
            resolve('');
        }
    });
});

// 版本比对 return含义: 1 => v1>v2; -1 => v1<v2; 0 => v1===v2
function compareVersion(v1, v2) {
    if (!v1 || !v2) {
        return;
    }
    v1 = v1.split('.');
    v2 = v2.split('.');
    var len = Math.max(v1.length, v2.length);

    while (v1.length < len) {
        v1.push('0');
    }
    while (v2.length < len) {
        v2.push('0');
    }

    for (var i = 0; i < len; i++) {
        var num1 = parseInt(v1[i]);
        var num2 = parseInt(v2[i]);

        if (num1 > num2) {
            return 1;
        } else if (num1 < num2) {
            return -1;
        }
    }

    return 0;
}


// 微信小程序需要的风控数据
async function getRiskParams() {
    // openId 在数据请求中会加入openId
    // const { user } = util.getGlobalInfo();
    // let openId = user.openId || '';


    // 小程序设备信息
    let systemInfo = await getSystemInfo();
    let { model, version, system, platform } = systemInfo;
    let userIpNetworkType = await getNetworkType;
    let { location } = getApp().globalData;
    let clientGps = location.lat + ',' + location.lgt;

    return {
        channel: 'APPLET',
        // openId,
        wxVersion: version,
        system,
        platform,
        model,
        userIpNetworkType,
        clientGps,
        network: userIpNetworkType

    };
}

// 快应用校验是否是第一次安装
function checkFirstOpenQuick() {
    return new Promise(resolve => {
        my.getStorage({
            key: 'bInstallQunarApp',
            success(res) {
                let ret = res && res.data;
                if (ret === true) {
                    resolve(false);
                } else {
                    resolve(true);
                }
            },
            fail() {
                // 请求失败，则为第一次安装				
                resolve(true);
            },
        });
    });
}

function randomString(len) {
    len = len || 32;
    var $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890';
    var maxPos = $chars.length;
    var pwd = '';
    for (let i = 0; i < len; i++) {
        pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
}

function generateRandomNumber() {
    const s = randomString(8);
    const chars = s.split('');
    let result = new Array(chars.length);
    let resultStr = '';

    for (let i = 0; i < chars.length; i += 2) {
        let i1b = chars[i].charCodeAt(0);
        let i2b = chars[i + 1].charCodeAt(0);
        const i1 = ((i1b + i2b) & 1) > 0;
        if (i1) {
            if ((i1b & 1) > 0) {
                ++i1b;
            } else {
                ++i2b;
            }
        }
        result[i] = i1b;
        resultStr += String.fromCharCode(result[i]);
        result[i + 1] = i2b;
        resultStr += String.fromCharCode(result[i + 1]);
    }

    return resultStr;
}
/**
 * @description 生成sn
 * @returns {String} sn
 */
function CreateGuid() {
    function S1() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(4);
    }

    function newGuid() {
        var guid = '';
        for (var i = 1; i <= 20; i++) {
            guid += S1();
            if (i === 8 || i === 12 || i === 16 || i === 20) {
                guid += '-';
            }
        }
        var num = parseInt(8 * Math.random());
        var date = new Date().getTime() + '';
        guid += date.slice(0, num);
        for (var j = 0; j < 4; j++) {
            guid += S1();
        }
        guid += date.slice(num + 5, 13);
        return guid;
    }
    return newGuid();
}

// 生成uuid
const getUniid = () => {
    let d = new Date().getTime();
    const traceid = 'xxxxxxxx-xxxx-2xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
        // eslint-disable-next-line no-bitwise
        const r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        // eslint-disable-next-line no-bitwise
        return (c == 'x' ? r : (r & 0x7) | 0x8).toString(16);
    });
    return traceid;
};


/**
 * 从当前url中解析regCid
 * /pages/coupon/list/index?regCid=123
 * /pages/qunar/subPages/alonePlatform/webView/index?url=https://m.flight.qunar.com/shark/active/ae243658cb59b5159bb3ba0bb9dfb45c?regCid=123
 * @param {*} url 
 * @param {*} key 
 * @returns 
 */
function getParamFromPath(url, key) {
    if (!url) {
        return null;
    }

    // 解析regCid
    if (url.indexOf(key) === -1) {
        return null;
    }

    const firstIndex = url.indexOf('?');
    if (firstIndex === -1) {
        return null;
    }

    const query = url.substr(firstIndex + 1);
    const param = queryToJson(query);
    if (param[key]) {
        return param[key];
    }

    if (!param['url']) {
        return null;
    }

    return getParamFromPath(decodeURIComponent(param['url']), key);

}

/**
 * query变json
 * @param {*} query 
 * @returns 
 */

function queryToJson(query) {
    const params = {};
    const arr = query.split('&');
    let key, value;
    for (var i = 0; i < arr.length; i++) {
        const spIndex = arr[i].indexOf('=');
        key = arr[i].substr(0, spIndex);
        value = arr[i].substr(spIndex + 1);
        params[key] = value;
    }
    return params;
}

const VID = '91010000';
const PID = '10060';
const bdsourceMap = {
    wx: {
        bdsource: 'smart_app'
    },
    ali: {
        bdsource: 'ali'
    },
};

function getSimpleCParam() {
    const {
        user = {},
        cookies
    } = HookApi.getStorageSync('UserData') || {};
    const userName = (cookies && cookies._q || '').replace(/^U\./, '');
    const { openId = '' } = user;
    const { model, system } = my.getSystemInfoSync() || {};
    const { bdsource } = bdsourceMap[process.env.ANU_ENV];

    const { envVersion, version } = my.getAccountInfoSync().miniProgram;
    return {
        pid: PID,
        uid: openId,
        un: userName,
        osVersion: system,
        model,
        vid: version,
        envVersion,
        gid: openId,
        cid: bdsource
    };
}

function getCParam(t) {
    const {
        user = {},
        cookies
    } = HookApi.getStorageSync('UserData') || {};
    const userName = (cookies && cookies._q || '').replace(/^U\./, '');
    const { openId = '' } = user;
    const { SDKVersion, model, version, system } = my.getSystemInfoSync() || {};
    const { bdsource } = bdsourceMap[process.env.ANU_ENV];
    const uid = `${bdsource}_${openId}`;
    return {
        // 端定义 hotel 定义的 client-type
        'h_ct': 'MINI',
        'adid': '',
        'brush': '',
        'cas': '',
        'catom': '',
        'cid': bdsource,
        'gid': uid,
        'ke': '',
        'lat': '',
        'lgt': '',
        'ma': '',
        'mno': '',
        model,
        'msg': '',
        'nt': '',
        'osVersion': system,
        'pid': PID,
        'ref': '',
        'sid': '',
        't': t,
        'un': userName,
        // 'usid': '', // userId，小程序暂时没有
        'vid': VID,
        uid,
        sdkVersion: SDKVersion,
        wxVersion: version,
        from: 'index'
    };
}

/**
 * qq一定写在wx前，因为qq环境支持wx，而wx环境不支持qq
 * @returns 
 */
function getNameSpace() {
    try { return qq; } catch (e) { }
    try { return wx; } catch (e) { }
    try { return swan; } catch (e) { }
    try { return my; } catch (e) { }
    try { return tt; } catch (e) { }
}

// 获取市场活跃日志参数

const getSystemInfoCache = (function () {
    let systemInfo = {};
    return function () {
        const isTrue = Object.keys(systemInfo).length > 0;
        if (isTrue) return systemInfo;
        try {
            return (systemInfo = my.getSystemInfoSync());
        } catch (err) {
            return systemInfo;
        }
    };
})();

const getLocalStorage = (() => {
    const cache = {};
    return function (key, disCache) {
        if (cache[key] && !disCache) return cache[key];
        try {
            const value = HookApi.getStorageSync(key);
            if (value) {
                if (!disCache) {
                    cache[key] = value.data;
                }
                return (cache[key] = value);
            }
            return '';
        } catch (e) {
            return '';
        }
    };
})();

const getReffer = (() => {
    const pageStack = [];
    return page => {
        const prevPage = pageStack.shift();
        pageStack.push(page);
        return prevPage || '';
    };
})();

function getWholePath(path, query) {
    var cid = query.cid || '';
    var result = path.toLowerCase().includes('/cms') ? `${path}/${cid}?` : path + '?';
    var queryKeys = Object.prototype.toString.call(query) && Object.keys(query);
    if (queryKeys && queryKeys.length) {
        for (let i = 0; i < queryKeys.length; i++) {
            var _key = queryKeys[i];
            var afterTag = i !== queryKeys.length - 1 ? '&' : '';
            result += _key + '=' + query[_key] + afterTag;
        }
        return result;
    } else {
        return path;
    }
}


function getRoutes() {
    // eslint-disable-next-line prefer-const
    let defaultRouteInfo = {
        path: '',
        query: {},
        wholePath: '',
        routeStack: []
    };
    let pages = [];
    try {
        // eslint-disable-next-line no-undef
        pages = getCurrentPages();
    } catch (e) {
        pages = [];
    }
    let curPage = {};
    if (pages.length) {
        curPage = pages[pages.length - 1];
        const { route = '', options = {} } = curPage;
        defaultRouteInfo.path = route;
        defaultRouteInfo.query = options;
        defaultRouteInfo.wholePath = getWholePath(route, options);
        // https://developers.weixin.qq.com/miniprogram/dev/framework/app-service/route.html
    }
    return defaultRouteInfo;
}


function getLog() {
    const { path: pageUrl, wholePath } = getRoutes();

    return {
        reffer: getReffer(pageUrl),
        wholePath
    };
}


const getHost = () => {
    let host = 'https://m.flight.qunar.com';
    const debugDomain = HookApi.getStorageSync('debugDomain');
    if (debugDomain) {
        const type = debugDomain.flight.type;
        if (type) {
            host = debugDomain.flight.domain;
        }
    }
    return host;
};

const getMarketOpenParams = () => {
    const { cookies = {}, user = {} } = getLocalStorage('UserData', true) || {};
    const { QN48 = '' } = cookies || {};
    const fp = getLocalStorage('ql7qGD') || '';
    const bdOriginObj = getLocalStorage('bd_origin');
    const { bdOrigin = '' } = bdOriginObj ? JSON.parse(bdOriginObj) : {};
    const { reffer, wholePath } = getLog();
    return {
        nanoId: getLocalStorage('NANO_ID') || '',
        uName: user.nickName || '',
        userName: (cookies._q || '').slice(2),
        qunarGlobal: cookies.QunarGlobal || '',
        openId: user.openId || '',
        unionId: user.unionId || '',
        bdSource: cookies.bd_source || '',
        fp,
        pageUrl: wholePath,
        urlfrom: reffer,
        bdOrigin,
        model: getSystemInfoCache().model || '',
        os: getSystemInfoCache().system || '',
        scene: (getApp() && String(getApp().globalData.scene)) || '',
        pVersion: (getApp() && getApp().globalData.pVersion) || '',
        uid: QN48,
        platform: getSystemInfoCache().platform || '',
        vid: getSystemInfoCache().version || '',
        cid: process.env.BUILD_ENV
    };
};

const isObjectSafe = (obj) => typeof obj == 'object' && Object.getOwnPropertyNames(obj).length > 0;

const getCookie = () => {
    const cookie = [];
    const USER_RELATED_KEY = {
        _q: 1,
        _s: 1,
        _v: 1,
        _t: 1,
        'HTTPOnly,_q': 1,
        'HTTPOnly,_s': 1,
        'HTTPOnly,_t': 1,
        'HTTPOnly,_v': 1,
    };
    const initUserData = HookApi.getStorageSync('UserData') || {};
    const extraCookie = HookApi.getStorageSync('extraCookie') || {};
    for (const name in initUserData.cookies) {
        cookie.push(`${name}=${initUserData.cookies[name]}`);
    }
    // 增加一个合并 cookies 和 extraCookie 中间区分开来的标志 cookie
    // 用于协助处理故障问题，不会存在太久
    cookie.push('_dangerDivide' + '=' + 'true');
    for (const name in extraCookie) {
        /**
         * 对于请求携带的 cookie 信息中的 _q,_s,_v,_t 字段,
         * 需要在 extraCookie 中过滤掉
         */
        if (USER_RELATED_KEY[name]) continue;
        cookie.push(`${name}=${extraCookie[name]}`);
    }
    return cookie.join(';');
}

function encodeObjectToQuery(params) {
    let queryStr = '';
    for (const key in params) {
        if (params.hasOwnProperty(key)) {
            let value = params[key];
            if (typeof value === 'object') {
                value = JSON.stringify(value);
            }
            queryStr += `${key}=${encodeURIComponent(value)}&`;
        }
    }
    return queryStr.slice(0, -1);
}

/**
 * 限制对象的键值对数量
 * @param {*} res 
 * @param {*} maxLength 
 * @returns 
 */
function limitObjectLength(res, maxLength) {
    const keys = Object.keys(res);
    if (keys.length <= maxLength) {
        return res; // 如果长度不超过最大值，则无需删除
    }

    const sortedKeys = keys.sort((a, b) => a - b); // 按数字顺序排序键
    const keysToDelete = sortedKeys.slice(0, keys.length - maxLength); // 要删除的键

    for (let key of keysToDelete) {
        delete res[key]; // 删除要删除的键值对
    }

    return res;
}

export default {
    getUniid,
    CreateGuid,
    isEmptyObject: isEmptyObject,
    isEmptyArray: isEmptyArray,
    isEmptyString: isEmptyString,
    isValidPhone: isValidPhone,
    stringifyURLParam: stringifyURLParam,
    parseURLParam: parseURLParam,
    mixin,
    queryToParam,
    debounce,
    arrayTransform,
    json_parse,
    getGlobalInfo,
    openWebview,
    openWebviewRe,
    toPay,
    getSystemInfo,
    queryToCookie,
    getGlobalInfoAsync,
    setIsIphoneX,
    getGlobalInfoAsyncByKey,
    makePhoneCall,
    decodeURL,
    fixWxOrderUrl,
    bdOrigin,
    fixWxFailUrl,
    setTimeStorage,
    getTimeStorage,
    clearTimeStorage,
    getRiskParams,
    compareVersion,
    checkFirstOpenQuick,
    generateRandomNumber,
    randomString,
    getParamFromPath,
    queryToJson,
    getSimpleCParam,
    getCParam,
    getNameSpace,
    getMarketOpenParams,
    getHost,
    isObjectSafe,
    getCookie,
    encodeObjectToQuery,
    limitObjectLength,
};
