// 埋点方法
/*eslint-disable */
import util from './util';
import request from './request.js';
import userObj from './user';
import watcher from './watcher';

/**
 * data针对数组和单对象
 * checkAuto 针对手动埋点 不传默认手动埋点 自动埋点传true 不会校验数据类型
 * 手动埋点只单一对象
 * 注意多平台影响 很容易翻车
 */
async function Log(data, logType, checkAuto) {
    const storage = await util.getGlobalInfoAsync();
    const randid = await util.getGlobalInfoAsyncByKey('mobileId');
    const { cookies, user } = storage;
    let { systemInfo, location, bd_origin, hd_origin, pVersion, regId, scene } = getApp().globalData;
    if(process.env.ANU_ENV === 'wx') {
        bd_origin = util.bdOrigin.getV()
    }
    // 微信、支付宝、QQ一定会有openId，等待openId返回
    if(!(cookies.openId && cookies.openId.length > 0) && (process.env.ANU_ENV === 'wx' || process.env.ANU_ENV === 'ali' || process.env.ANU_ENV === 'qq')) {
        setTimeout(() => {
            Log(data, logType, checkAuto);
        }, 200)
        return;
    }
    // 针对百度做swanId的设置 const muid = await getBuId();
    const muid = '';
    const fastUserId = '';
    if(!systemInfo.brand) {
        systemInfo = await getSys();
    }
    const c = {
        // wx ali
        bd_source: userObj.loginOpts.exParams.bd_source,
        // 例如 12.1.1
        os: systemInfo.system,
        // 机型
        device: systemInfo.model,
        // 平台类型 ios android
        platform: systemInfo.platform,
        // 小程序版本号
        version: systemInfo.version,
        _q: cookies._q || '',
        _v: cookies._v || '',
        _t: cookies._t || '',
        _s: cookies._s || '',
        _i: cookies._i || '',
        username: (cookies._q || '').slice(2),
        scene,
        openId: cookies.openId || '',
        unionId: cookies.unionId || '',
        lat: location.lat || '',
        lgt: location.lgt || '',
        bd_origin: bd_origin || data.bd_origin,
        hd_origin,
        pVersion,
        muid,
        // 快应用相关参数
        brand: systemInfo.brand && systemInfo.brand.toLowerCase().replace(/\s/g, ''),
        model: systemInfo.model,
        quickos: systemInfo.version,
        randid: randid || '',
        andid: cookies.andid || '',
        newLog: 1,
        fastUserId,
        regId,
        xcxType: "main" // 区分主小程序和马甲小程序
        // abtest相关
        // AbtestGroup: '190814_sc_other_wxt',
        // Keg: homeAb
    };

    if (!checkAuto) {
        if (typeof(data) !== 'object') {
            throw new Error('请传单一对象');
        }
        data.time = +new Date();
        data = {
            action: 'cus',
            info: data
        };
    }
    let arr = [];
    if (typeof(data) === 'object' && !Array.isArray(data)) {
        arr.push(data);
    } else {
        arr = data;
    }
    const log = {
        p: 'xcx',
        logType: logType || 'ue',
        c: JSON.stringify(c),
        b: JSON.stringify(arr)
    };
    // ide错误不发送
    if(logType === 'err' && (systemInfo.platform === 'devtools' || systemInfo.brand === 'devtools')) {
        return;
    }
    request({
        service: '/api/log/unifiedLog_new/',
        data: log,
        method: 'POST',
        header: {
            'content-type': 'application/x-www-form-urlencoded'
        }
    });
}

// 快应用fastuserid 拿到再发
export async function getUserId() {
    const storage = await util.getGlobalInfoAsync();
    const { user } = storage;
    return new Promise(resolve => {
        resolve('');
        return;
    });
}

// 莫名其妙的情况下拿不到系统信息
async function getSys() {
    return new Promise(resolve => {
        my.getSystemInfo({
            success: res => {
                resolve(res);
            },
            fail: () => {
                resolve({
                    brand: 'GET-BRAND-FAIL'
                });
            }
        });
    });
}

export default Log;