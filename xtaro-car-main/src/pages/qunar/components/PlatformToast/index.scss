@import '../../style/iconfont.scss';
@import '../../style/g-define.scss';

.plat-toast {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 99;
    animation: opacityIncrease 500ms;

    .plat-toast-bg {
        background: #000;
        opacity: 0.6;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        z-index: -1;
    }

    .center {
        position: absolute;
        left: 0;
        bottom: -67px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-content: center;
    }

    .toast-wrap {
        position: relative;
        width: 285px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .toast-img {
        border-radius: 8px;
        overflow: hidden;

        .img {
            width: 285px;
            border-radius: 8px;
            background-color: transparent;
        }
    }

    .g-q-iconfont {
        font-size: 40px;
        color: #FFFFFF;
        text-align: center;
    }

    .icon::before {
        content: '\f3f4';
    }
}

.float {
    position: fixed;
    right: 0px;
    bottom: 16px;
    width: 80px;
    height: 80px;
    z-index: 9;

    .float-img {
        width: 100%;
        height: 100%;
    }
}

.rewrite-btn {
    background: transparent;
}

.logo-shake {
    animation: logoShake 2.8s 200ms linear;
}

@keyframes logoShake {

    10%,
    14%,
    18%,
    22%,
    26%,
    80%,
    84%,
    88%,
    92%,
    96% {
        transform: rotate(-8deg);
    }

    12%,
    16%,
    20%,
    24%,
    28%,
    82%,
    86%,
    90%,
    94%,
    98% {
        transform: rotate(8deg);
    }

    0%,
    9%,
    29%,
    79%,
    100% {
        transform: rotate(0deg);
    }
}

@keyframes opacityIncrease {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}