import { View, Form, Button, Image, Text } from "@tarojs/components";
import React from "react";
import initFormIdConfig from "@/common/utils/formId";
import LogQmark from "@/common/utils/logQmark";
import "./index.scss";

class PlatformToast extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            env: process.env.ANU_ENV,
            showToast: false,
            toastImg: "",
        };
    }
    componentWillReceiveProps(nextProps) {
        this.setState({
            ...nextProps.data,
        });
    }
    go = () => {
        this.props.go && this.props.go();
        this.props.hide && this.props.hide();
    };
    goFloat = () => {
        this.props.goFloat && this.props.goFloat();
    };
    hideToast = () => {
        this.props.hide && this.props.hide();
        LogQmark({
            module: "popUp",
            page: "homeEntrance",
            id: "popup_close",
            operType: "click",
            ext: { index: this.props.data?.type },
        });
    };
    sendFormId = (origin, e) => {
        initFormIdConfig(e, origin);
    };
    render() {
        return (
            <View>
                {this.props.float &&
                    this.props.float.img &&
                    this.state.env === "wx" && (
                        <Form
                            onSubmit={this.sendFormId.bind(
                                this,
                                "firstpage-signal",
                            )}
                            report-submit="true"
                        >
                            <Button
                                class="rewrite-btn"
                                hover-class="none"
                                formType="submit"
                            >
                                <View
                                    className="logo-shake float "
                                    onClick={this.goFloat.bind(this)}
                                >
                                    <Image
                                        class="float-img"
                                        src={this.props.float.img}
                                    />
                                </View>
                            </Button>
                        </Form>
                    )}

                {this.state.showToast && (
                    <View className="plat-toast" catchTouchmove="noop">
                        <View className="plat-toast-bg"></View>
                        <View className="toast-wrap">
                            {/* fix: 跳转白边的问题，模板消息不继续使用，故删除掉form button节点 */}
                            {/* <form onSubmit={this.sendFormId.bind(this, 'firstpage-pop')} report-submit="true">
                 <button
                     class="rewrite-btn"
                     style="height: 100%;"
                     hover-class="button-hover"
                     formType="submit">
                     <div onTap={this.go.bind(this)} className="toast-img">
                         <image className="img" mode="widthFix" src={this.state.toastImg} />
                     </div>
                 </button>
              </form>
              <form onSubmit={this.sendFormId.bind(this, 'firstpage-popcancel')} report-submit="true">
                 <button
                     class="rewrite-btn center"
                     hover-class="button-hover"
                     formType="submit">
                     <text onTap={this.hideToast.bind(this)} className="g-q-iconfont icon">&#xf3f4;</text>
                 </button>
              </form> */}
                            <View
                                onClick={this.go.bind(this)}
                                className="toast-img"
                            >
                                <Image
                                    className="img"
                                    mode="widthFix"
                                    src={this.state.toastImg}
                                />
                            </View>
                            {!this.state.hide_login_close_btn ? (
                                <Text
                                    onClick={this.hideToast.bind(this)}
                                    className="g-q-iconfont icon"
                                >
                                </Text>
                            ) : null}
                        </View>
                    </View>
                )}
            </View>
        );
    }
}

export default PlatformToast;
