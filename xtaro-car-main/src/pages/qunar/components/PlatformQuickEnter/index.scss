.p-enter-wrap{
    width: 375px;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin-top: 12px;
}
.p-enter{
    width: 355px;
    height: 102px;
    background-color: #fff;
    border-radius: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 25px;
    .item{
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        width: 80px;
        height: 72px;
    }
    .img{
        width: 44px;
        height: 44px;
    }
    .text{
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #333333;
        white-space:nowrap;
    }
}
.p-enter{
    width: 355px;
    height: 102px;
    background-color: #fff;
    border-radius: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 25px;
    .item{
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        width: 80px;
        height: 72px;
    }
    .img{
        width: 44px;
        height: 44px;
    }
    .text{
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #333333;
        white-space:nowrap;
    }
}