import { View, Image, Text } from "@tarojs/components";
import { navigateTo } from "@tarojs/taro";
import React from "react";
import Log from "@/common/utils/log";
import "./index.scss";

class PlatformQuickEnter extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            navList: [
                {
                    bg: "https://picbed.qunarzz.com/0cc637fa61c4c1c746b8635595be0ca3.png",
                    type: "flight",
                    title: "机票",
                    path: "/pages/alonePlatform/flight/index",
                },
                {
                    bg: "https://picbed.qunarzz.com/308d6862905f604d542a8daa94265d6a.png",
                    type: "train",
                    title: "火车票",
                    path: "/pages/train/index/index",
                },
                {
                    bg: "https://picbed.qunarzz.com/6bccba607869c30fb22314395aef7bf1.png",
                    type: "hotel",
                    title: "酒店",
                    path: "/pages/hotel/hotel-index/index",
                },
                {
                    bg: "https://picbed.qunarzz.com/6d044f4d7bcaa8fc94369cf1e8577bb8.png",
                    type: "ticket",
                    title: "景点·门票",
                    path: "/pages/ticket/index/index",
                },
            ],
            nav: [],
        };
    }
    componentDidMount() {
        let arr = [];
        if (this.props.type === "hotel") {
            arr = ["flight", "ticket", "train"];
        } else if (this.props.type === "train") {
            arr = ["hotel", "ticket", "flight"];
        } else {
            arr = ["hotel", "ticket", "flight", "train"];
        }
        this.setList(arr);
    }
    setList = (arr) => {
        const nav = [];
        arr.forEach((v) => {
            this.state.navList.forEach((_v) => {
                if (v === _v.type) {
                    nav.push(_v);
                }
            });
        });
        this.setState({
            nav,
        });
    };
    go = (url, target) => {
        // console.log('埋点信息', {
        //     type: 'quick-enter',
        //     from: this.props.type,
        //     target
        // });
        Log({
            type: "quick-enter",
            name: this.props.type,
            target,
        });
        navigateTo({ url });
    };

    render() {
        return (
            <View
                class="p-enter-wrap"
                style={{
                    marginTop: this.props.top || "24rpx",
                    marginBottom: this.props.bottom,
                }}
            >
                <View class="p-enter">
                    {this.state.nav.map((v, i) => {
                        return (
                            <View
                                key={i}
                                class="item"
                                onClick={this.go.bind(this, v.path, v.type)}
                            >
                                <Image class="img" src={v.bg} />
                                <Text class="text">{v.title}</Text>
                            </View>
                        );
                    })}
                </View>
            </View>
        );
    }
}
export default PlatformQuickEnter;
