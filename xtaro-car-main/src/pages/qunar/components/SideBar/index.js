import { View, Text } from "@tarojs/components";
import React from "react";
import "./index.scss";

/**
 * 介绍: 底部弹出单选框,多用于填单及表单选择较多地方
 */

class SideBar extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            show: false,
            className: "",
            animationClass: "",
            dataLength: props.data.length,
        };
    }

    componentDidMount() {
        this._updateVisible(this.props.showSideBar);
    }

    _updateVisible = (visible) => {
        this.timeoutId && clearTimeout(this.timeoutId); //防止更改太快
        if (visible) {
            this.setState({
                show: true,
                className: "quist-overlay-enter",
                animationClass: "pickerenter",
            });
        } else {
            this.setState({
                className: "quist-overlay-leave",
                animationClass: "pickerleave",
            });

            this.timeoutId = setTimeout(() => {
                this.setState({
                    show: false,
                });
            }, 500);
        }
    };

    componentWillReceiveProps(nextProps) {
        if (this.props.showSideBar !== nextProps.showSideBar) {
            this._updateVisible(nextProps.showSideBar);
        }
    }

    closePops = () => {
        this.setState({
            showSideBar: false,
        });
    };

    // 点击遮罩层的事件
    layerEvent = () => {
        if (this.props.layerEvent) {
            this.props.layerEvent();
        }
        this.closePops();
    };

    // 选择回调
    select = (value, index, key) => {
        this.setState({
            curIndex: index,
            show: false,
        });
        this.props.curIndex = index;
        this.props.selectEvent(value, index, key);
        this.closePops();
    };

    overlayClick = () => {
        this.setState({
            show: false,
        });
    };

    render() {
        return (
            <View>
                <View
                    className={"quist-overlay-mask  " + this.state.className}
                    style={{
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        display: this.state.show ? "flex" : "none",
                    }}
                    onClick={this.overlayClick.bind(this)}
                    catchTouchMove={this.handleTouchMove}
                />

                {this.state.show && (
                    <View
                        className={"qt-sidebar " + this.state.animationClass}
                        hidden={!this.state.show}
                        style={{ height: this.props.data.length * 45 + "PX" }}
                    >
                        <View className="header">
                            <View className=" title">{this.props.title}</View>
                            {this.props.tips && (
                                <View className="tips">{this.props.tips}</View>
                            )}
                        </View>
                        <View class="content">
                            {this.props.data.map((item, index) => {
                                return (
                                    <View
                                        className="item"
                                        key={item.key}
                                        onClick={this.select.bind(
                                            this,
                                            item.value,
                                            index,
                                            item.key,
                                        )}
                                    >
                                        {this.props.curIndex == index && (
                                            <View className="icon-c checkmark" />
                                        )}
                                        <Text
                                            class={
                                                this.props.curIndex == index
                                                    ? "active"
                                                    : ""
                                            }
                                        >
                                            {item.key}
                                        </Text>
                                    </View>
                                );
                            })}
                        </View>
                    </View>
                )}

                {this.state.showSideBar && (
                    <View class="layer" onClick={this.layerEvent} />
                )}
            </View>
        );
    }
}

SideBar.defaultProps = {
    title: "", // sidebar title
    tips: "", // title下方的提示,可见线上填单页房间等选择
    curIndex: 0, // 当前选中index
    style: {},
    data: [],
    showSideBar: true,
    selectEvent: function () {},
    layerEvent: function () {},
};

export default SideBar;
