.qt-sidebar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  // height: 300px;
  // animation-fill-mode: forwards;
  background-color: #ffffff;
  flex-direction: column;
  z-index: 300;
  overflow: hidden;
  overflow-y: scroll;
}


.quist-overlay-mask {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  justify-content: center;
  align-items: center;
  animation-duration: 200ms;
  animation-fill-mode: forwards;
}

@keyframes quistOverlayEnter {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes quistOverlayLeave {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}


.quist-overlay-enter {
  animation-name: quistOverlayEnter;
}

.quist-overlay-leave {
  animation-name: quistOverlayLeave;
}


.pickerenter {
  animation-name: pickerenter;
  animation-duration: 400ms;
  animation-timing-function: ease-in;
}

.pickerleave {
  animation-name: pickerleave;
  animation-duration: 300ms;
  animation-timing-function: ease-in;
}

@keyframes pickerenter {
  0% {
    height: 0;
    transform: translateY(300px);
  }

  100% {

    transform: translateY(0);
  }
}

@keyframes pickerleave {
  0% {

    transform: translateY(0);
  }

  100% {
    height: 0;
    transform: translateY(300px);
  }
}

.content {
  // height: 238px;
  // overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.content .item {
  padding: 0 10px 10px 15px;
  height: 44px;
  margin-left: 40px;
  align-items: center;
  display: flex;
}

.content .active {
  color: #00bcd4;
}