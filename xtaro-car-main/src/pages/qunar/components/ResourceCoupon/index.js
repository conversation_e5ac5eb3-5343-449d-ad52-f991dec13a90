import { View, Image } from "@tarojs/components";
import React from "react";
import MoreCouponList from "../MoreCouponList";
import SingleCoupon from "../SingleCoupon";
import "./index.scss";

class ResourceCoupon extends React.Component {
    constructor(props) {
        super(props);
    }
    render() {
        return (
            <View class="resource-coupon">
                {this.props.resourcesData?.length > 0 && (
                    <View
                        className="coupon-center-container"
                        style={{
                            width: this.props.width,
                            height: this.props.height,
                            margin: this.props.margin,
                            paddingTop: this.props.paddingTop,
                            // backgroundImage: this.props.groupBackUpImage ? `url(${this.props.groupBackUpImage})` : ''
                        }}
                    >
                        <Image
                            src={
                                this.props.groupBackUpImage ||
                                "https://qimgs.qunarzz.com/wpf_newmpic_001/3275ff80c0dcdbcb5417bf960fdecfa8.png"
                            }
                            className="coupon-center-background-image"
                        />
                        {this.props.resourcesData.length > 1 ? (
                            <MoreCouponList
                                couponList={this.props.resourcesData}
                                hideButton={this.props.hideButton}
                                paddingWidth={this.props.paddingWidth}
                                isNeedScroll={
                                    this.props.resourcesData?.length >=
                                    (this.props.needScrollCouponNum || 3)
                                }
                                couponItemStyle={this.props.couponItemStyle}
                                handleCouponJumpLog={
                                    this.props.handleCouponJumpLog
                                }
                            ></MoreCouponList>
                        ) : (
                            <SingleCoupon
                                couponData={this.props.resourcesData[0]}
                                hideButton={this.props.hideButton}
                                couponItemStyle={this.props.couponItemStyle}
                                handleCouponJumpLog={
                                    this.props.handleCouponJumpLog
                                }
                            ></SingleCoupon>
                        )}
                    </View>
                )}
            </View>
        );
    }
}

export default ResourceCoupon;
