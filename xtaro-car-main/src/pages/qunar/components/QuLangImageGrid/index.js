import { View, Image } from "@tarojs/components";
import React from "react";

import "./index.scss";
class ImageGrid extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            mediaList:
                (props.data.mediaList && props.data.mediaList.slice(0, 9)) ||
                [],
            windowWidth: 750,
            oneItemWidth: 718,
            twoItemWidth: 356,
            threeItemWidth: 235,
        };
    }
    // async componentDidMount() {
    //     const {windowWidth, windowHeight, screenWidth} = await utils.getSystemInfo();
    //     console.log('>>>>>>windowWidth>>>>>', windowWidth, windowHeight)
    //     this.setState({
    //         windowWidth,
    //         oneItemWidth: windowWidth - 16,
    //         twoItemWidth: (windowWidth - 19) / 2, // 8 + 3 + 8
    //         threeItemWidth: (windowWidth - 22) / 3 // 8 + 3 + 3 + 8
    //     })
    // }

    onClickImageCard = (index) => {
        this.props.onClickImageCard &&
            this.props.onClickImageCard(this.state.mediaList, index);
    };
    render() {
        return (
            <View class="container" key={this.props.index}>
                {this.state.mediaList.map((item, index) => {
                    return (
                        <View
                            class="img-container"
                            style={`width: ${
                                this.props.length === 1
                                    ? this.state.oneItemWidth
                                    : this.props.length === 2 ||
                                        this.props.length === 4
                                      ? this.state.twoItemWidth
                                      : this.props.length === 5 && index < 2
                                        ? this.state.twoItemWidth
                                        : this.state.threeItemWidth
                            }px; height: ${
                                this.props.length === 1
                                    ? this.state.oneItemWidth
                                    : this.props.length === 2 ||
                                        this.props.length === 4
                                      ? this.state.twoItemWidth
                                      : this.props.length === 5 && index < 2
                                        ? this.state.twoItemWidth
                                        : this.state.threeItemWidth
                            }px`}
                            onClick={this.onClickImageCard.bind(this, index)}
                        >
                            <Image
                                class="img"
                                src={item.src}
                                mode="aspectFill"
                            />
                        </View>
                    );
                })}
                {this.state.mediaList.length === 8 ? (
                    <View
                        class="img-container"
                        style={`width: ${this.state.threeItemWidth}px`}
                    ></View>
                ) : (
                    ""
                )}
            </View>
        );
    }
}
export default ImageGrid;
