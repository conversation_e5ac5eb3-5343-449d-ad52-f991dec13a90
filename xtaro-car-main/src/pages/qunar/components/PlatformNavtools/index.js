import { View, Image, Navigator } from "@tarojs/components";
import { getCurrentPages } from "@tarojs/taro";
import React from "react";
import "./index.scss";

// 其他小工具列表
const config = [
    {
        title: "旅行账本",
        url: "/page/book/index",
        appId: "wx9c9b45db7a992a44",
        icon: "https://s.qunarzz.com/interact_community/toolsIcon/book2.png",
    },
    {
        title: "打包助手",
        url: "/page/checkList/index",
        appId: "wxe5ca79d1b4b68679",
        icon: "https://s.qunarzz.com/interact_community/toolsIcon/package2.png",
    },
    {
        title: "航班动态",
        url: "/pages/index/index",
        appId: "wxd0681552bc8ed633",
        icon: "https://s.qunarzz.com/interact_community/toolsIcon/flight2.png",
    },
    {
        title: "行程助手",
        url: "/pages/alonePlatform/tools/travelAssistant/index/index",
        appId: "wx799d4d93a341b368",
        icon: "https://s.qunarzz.com/interact_community/toolsIcon/travel2.png",
    },
    {
        title: "在线值机",
        url: "/pages/h5/h5",
        appId: "wxd7464735f4dc21b7",
        icon: "https://s.qunarzz.com/interact_community/toolsIcon/online2.png",
    },
    {
        title: "旅友群聊",
        url: "/guide/pages/chatMain/index",
        appId: "wx63503dace8b3fc32",
        icon: "https://s.qunarzz.com/interact_community/toolsIcon/talk2.png",
    },
];

class PlatformNavtools extends React.Component {
    constructor(props) {
        super(props);
        const { tools, lineItemNum, right, width } = this._handleData(props);
        this.state = {
            active: false,
            tools,
            jumping: false,
            lineItemNum,
            right,
            width,
        };
    }

    _handleData = (props) => {
        const that = this;
        const currentPages = getCurrentPages();
        that.currentPage = "/" + currentPages[currentPages.length - 1].route;
        const tools = config.filter(function (item) {
            return item.appId !== props.appId || item.url !== that.currentPage;
        });
        const lineItemNum =
            Math.ceil(tools.length / 2) > 3 ? Math.ceil(tools.length / 2) : 3;
        const right = 450 + (lineItemNum - 3) * 132;
        const width = 366 + (lineItemNum - 3) * 134;
        return {
            tools,
            lineItemNum,
            right,
            width,
        };
    };

    changeFlexStation = () => {
        this.setState({
            active: !this.state.active,
        });
        const myEventDetail = {
            navClick: "changeFlexStation",
            status: this.state.active,
            title: "switch", // detail对象，提供给事件监听函数
        };
        this.props.onWatchevent && this.props.onWatchevent(myEventDetail);
    };

    navcomplete = (e) => {
        const { fromappid, toappid, url: toUrl } = e.currentTarget.dataset;
        const { type } = e;
        const fromUrl = this.currentPage;
        const myEventDetail = {
            fromUrl,
            navStatus: type,
            fromappid,
            toappid,
            toUrl, // detail对象，提供给事件监听函数
        };
        this.props.onWatchevent && this.props.onWatchevent(myEventDetail);
        this.setState({
            active: false,
        });
    };

    render() {
        return (
            <View>
                <View
                    className={
                        "navMiniToolsMask " +
                        (this.state.active ? "active" : "")
                    }
                    onClick={this.changeFlexStation}
                ></View>
                <View
                    className={
                        "navMiniTools " + (this.state.active ? "active" : "")
                    }
                    style={
                        "bottom: " +
                        this.props.bottom +
                        "rpx; right: -" +
                        this.state.right +
                        "rpx; transform:translateX(" +
                        (this.state.active ? -this.state.right : 0) +
                        "rpx);"
                    }
                >
                    <View
                        className={
                            "flexibleButton " +
                            (this.state.active ? "active" : "")
                        }
                        onClick={this.changeFlexStation}
                    >
                        <Image
                            className="buttonImage"
                            mode="scaleToFill"
                            src="https://s.qunarzz.com/interact_community/miniIcon/button.png"
                        ></Image>
                    </View>
                    <View className="miniToolsCon">
                        <View
                            className="miniToolsLine"
                            style={"width: " + this.state.width + "rpx"}
                        >
                            {this.state.tools.map((item, index) => {
                                return (
                                    <View>
                                        {this.state.lineItemNum > index ? (
                                            <Navigator
                                                target={
                                                    item.appId ==
                                                    this.props.appId
                                                        ? "self"
                                                        : "miniProgram"
                                                }
                                                className="miniToolsItem"
                                                app-id={item.appId}
                                                path={item.url}
                                                url={item.url}
                                                open-type="navigate"
                                                data-fromappid={
                                                    this.props.appId
                                                }
                                                data-toappid={item.appId}
                                                data-url={item.url}
                                                onFail={this.navcomplete}
                                                onSuccess={this.navcomplete}
                                            >
                                                <Image
                                                    className="miniToolsItemIcon"
                                                    mode="scaleToFill"
                                                    src={item.icon}
                                                ></Image>
                                                <View className="toolsName">
                                                    {item.title}
                                                </View>
                                            </Navigator>
                                        ) : null}
                                    </View>
                                );
                            }, this)}
                        </View>
                        <View
                            className="miniToolsLine"
                            style={"width: " + this.state.width + "rpx"}
                        >
                            {this.state.tools.map((item, index) => {
                                return (
                                    <View>
                                        {this.state.lineItemNum <= index ? (
                                            <Navigator
                                                target={
                                                    item.appId ==
                                                    this.props.appId
                                                        ? "self"
                                                        : "miniProgram"
                                                }
                                                className="miniToolsItem"
                                                app-id={item.appId}
                                                path={item.url}
                                                url={item.url}
                                                open-type="navigate"
                                            >
                                                <Image
                                                    className="miniToolsItemIcon"
                                                    mode="scaleToFill"
                                                    src={item.icon}
                                                ></Image>
                                                <View className="toolsName">
                                                    {item.title}
                                                </View>
                                            </Navigator>
                                        ) : null}
                                    </View>
                                );
                            }, this)}
                        </View>
                    </View>
                </View>
            </View>
        );
    }
}

PlatformNavtools.defaultProps = {
    appId: "",
    bottom: "",
};

export default PlatformNavtools;
