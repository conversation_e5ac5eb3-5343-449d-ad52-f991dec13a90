.navMiniToolsMask {
    display: none;
  }
  .navMiniToolsMask.active {
    display: block;
    opacity: 0.3;
    background: #000000;
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 998;
  }
  .navMiniTools {
    position: fixed;
    background: transparent;
    transition: all 0.8s;
    right: -499.5px;
    z-index: 999;
    display: flex;
    flex-direction: row;
    bottom: 50px;
  }
  .navMiniTools.active {
    transform:translateX(-225px);
    box-shadow: 0;
  }
  .flexibleButton {
    color: green;
    width: 44px;
    height: 44px;
    background: #FFFFFF;
    border: 1px solid #F5F5F5;
    border-right: 0;
    box-shadow: 0 4px 8px 0 #EEEEEE;
    border-radius: 20px 0 0 20px;
    text-align: center;
    margin-top: 72px;
    position: relative;
    left: 1px;
    opacity: 0.92;
  }
  .flexibleButton.active {
    box-shadow: 0 0 0 0;
    opacity: 1;
  }
  .flexibleButton .buttonImage{
    margin: 7px 0;
    background: transparent;
    width: 30px;
    height: 30px;
  }
  .miniToolsCon {
    box-sizing: border-box;
    height: 190px;
    padding: 20px;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 20px 0 0 20px;
    border: 1px solid #F5F5F5;
  }
  .miniToolsLine {
    display: flex;
    flex-direction: row;
    width: 183.5px;
    height: 70px;
  }
  .miniToolsItem {
    background: #fff;
    width: 50px;
    margin-right: 17.5px;
  }
//   .miniToolsItem:last-child {
//     margin-right: 0rpx !important
//   }
  .miniToolsItemIcon {
    background: #EEEEEE;
    width: 45px;
    height: 45px;
    border-radius: 50%;
  }
  .toolsName {
    font-size: 12px;
    color: #212121;
    letter-spacing: 0;
    text-align: center;
  }