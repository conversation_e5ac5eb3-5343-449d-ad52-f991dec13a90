import { View, Image, Text, Button } from "@tarojs/components";
import React from "react";
import Ubutton from "@platformComponents/Ubutton/index";
import util from "@/common/utils/util";
import Log from "@/common/utils/log";
import LogQmark from "@/common/utils/logQmark";
import "./index.scss";

const defaultLogParams = {
    page: "authLogin",
    module: "login_quick",
    operType: "click",
};
class PlatformLoginQuick extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            hasAgreedProtocol: false,
        };
    }

    componentDidMount() {
        LogQmark({
            ...defaultLogParams,
            operType: "show",
            id: "login_quick",
            ext: {
                newStyle: this.props.newStyle,
            },
        });
    }

    changePhoneUserInfo = (e) => {
        const { detail = {} } = e || {}
        LogQmark({
            ...defaultLogParams,
            id: "switch_phone",
            ext: {
                newStyle: this.props.newStyle,
            },
        });
        this.props.changePhoneUserInfo({ e, ...detail });
    };

    confirm = () => {
        LogQmark({
            ...defaultLogParams,
            id: "login_button",
        });
        this.props.confirm();
    };

    changeSelected = () => {
        LogQmark({
            ...defaultLogParams,
            id: "checkbox",
        });
        this.props.changeSelected();
    };

    clickProtocolText = () => {
        this.props.clickProtocolText();
    };

    clickProtoColToPrivacyPolicy = () => {
        this.props.clickProtoColToPrivacyPolicy();
    };

    render() {
        return (
            <View>
                {this.props.newStyle !== undefined ? (
                    <View class="c-login-wrap">
                        {this.props.newStyle ? (
                            <View class="p-login-quick-new">
                                <View class="hello-box">
                                    <View className="userImg">
                                        <Image
                                            class="pic"
                                            src={this.props.avatarUrl}
                                        ></Image>
                                    </View>
                                    <View className="phone-number">{`Hi, ${this.props.phone}`}</View>
                                </View>
                                <Image
                                    class="rights"
                                    src={this.props.rightsUrl}
                                ></Image>
                                <View className="p-login-wrap-protocol-platformLoginQuick">
                                    <View
                                        className="p-login-wrap-changeSelected"
                                        onClick={this.changeSelected.bind(this)}
                                    >
                                        <Text
                                            className={`p-login-wrap-checkbox ${this.props.hasAgreedProtocol ? "radio radio-check" : "radio"}`}
                                        ></Text>
                                        <Text className="p-login-wrap-text">
                                            同意并遵行去哪儿网
                                        </Text>
                                    </View>
                                    <Text
                                        className="p-login-wrap-protocolText"
                                        onClick={this.clickProtocolText.bind(
                                            this,
                                        )}
                                    >
                                        《用户服务协议》
                                    </Text>
                                    <Text className="p-login-wrap-text">
                                        、
                                    </Text>
                                    <Text
                                        className="p-login-wrap-protocolText"
                                        onClick={this.clickProtoColToPrivacyPolicy.bind(
                                            this,
                                        )}
                                    >
                                        《隐私政策》
                                    </Text>
                                </View>
                                <View class="button-box">
                                    <Button
                                        className="confirm"
                                        onClick={this.confirm.bind(this)}
                                    >
                                        一键登录
                                    </Button>
                                    <Image
                                        class="button-decoration-img"
                                        src={this.props.buttonDecorationUrl}
                                    ></Image>
                                </View>
                                {this.props.env === "wx" && (
                                    <Ubutton
                                        class="switch-phone"
                                        open-type="getUserInfo"
                                        onGetuserinfo={this.changePhoneUserInfo.bind(
                                            this,
                                        )}
                                    >
                                        其他手机号登录
                                    </Ubutton>
                                )}
                            </View>
                        ) : (
                            <View class="p-login-quick">
                                <View className="userImg">
                                    <Image
                                        class="pic"
                                        src={this.props.avatarUrl}
                                    ></Image>
                                </View>
                                <View className="phone-number">
                                    {this.props.phone}
                                </View>
                                <View className="desc">
                                    <View>
                                        近期您已经授权以上账号登录过去哪儿网
                                    </View>
                                    <View>点击确认可自动登录</View>
                                </View>
                                <View className="p-login-wrap-protocol-platformLoginQuick">
                                    <View
                                        className="p-login-wrap-changeSelected"
                                        onClick={this.changeSelected.bind(this)}
                                    >
                                        {/* <text className="p-login-wrap-checkbox g-q-iconfont">{this.props.hasAgreedProtocol ? '' : ''}</text> */}
                                        <Text
                                            className={`p-login-wrap-checkbox ${this.props.hasAgreedProtocol ? "radio radio-check" : "radio"}`}
                                        ></Text>
                                        <Text className="p-login-wrap-text">
                                            同意并遵行去哪儿网
                                        </Text>
                                    </View>
                                    <Text
                                        className="p-login-wrap-protocolText"
                                        onClick={this.clickProtocolText.bind(
                                            this,
                                        )}
                                    >
                                        《用户服务协议》
                                    </Text>
                                    <Text className="p-login-wrap-text">
                                        及
                                    </Text>
                                    <Text
                                        className="p-login-wrap-protocolText"
                                        onClick={this.clickProtoColToPrivacyPolicy.bind(
                                            this,
                                        )}
                                    >
                                        《隐私政策》
                                    </Text>
                                </View>
                                <Button
                                    className={
                                        !this.props.hasAgreedProtocol
                                            ? "confirm default rewrite-btn"
                                            : "confirm rewrite-btn"
                                    }
                                    onClick={this.confirm.bind(this)}
                                >
                                    确认
                                </Button>
                                {this.props.env === "wx" && (
                                    <Ubutton
                                        class="switch-phone"
                                        open-type="getUserInfo"
                                        onGetuserinfo={this.changePhoneUserInfo.bind(
                                            this,
                                        )}
                                    >
                                        使用其他手机号码登录
                                    </Ubutton>
                                )}
                            </View>
                        )}
                    </View>
                ) : (
                    <View />
                )}
            </View>
        );
    }
}

export default PlatformLoginQuick;
