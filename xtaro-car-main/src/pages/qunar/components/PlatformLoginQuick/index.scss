@import '../../style/g-define.scss';
@import '@platformStyle/iconfont.scss';


@font-face {
    font-family: 'g-q-iconfont';
    // src: url('/assets/css/minprogram_nanachi.ttf')
    // src: url("https://ss.qunarzz.com/yo/font/1.0.3/yofont.ttf");
    src: url('https://s.qunarzz.com/nanachi/score/font/0.0.96/minprogram_nanachi.ttf');
}
.c-login-wrap{
    margin-top:-1px;
}
.g-q-iconfont {
    font-family: 'g-q-iconfont';
    font-style: normal;
}

.p-login-quick {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .userImg {
        width: 100px;
        height: 100px;
        margin-top: 40px;
        border: 3px solid #03CDE9;
        border-radius: 50px;
        display: initial;
        box-sizing: initial;

        .pic {
            width: 100px;
            height: 100px;
            border-radius: 50px;
        }
    }

    .phone-number {
        margin-top: 10px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        line-height: 20px;
        font-weight: 400;
    }

    .desc {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 20px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        line-height: 20px;
        font-weight: 400;
    }

    .confirm {
        width: 315px;
        height: 44px;
        line-height: 44px;
        margin-top: 20px;
        background-color: #00D4E3;
        color: #fff;
        font-size: 18px;
        border-radius: 44px;
        text-align: center;

        text {
            margin: 0 auto;
            color: #fff;
        }
    }
    .default{
        //background: #E6E6E6;
    }

    .switch-phone {
        margin-top: 42px;
        font-size: 13px;
        color: #00bcd4;
        background-color: transparent;
        padding: 0 0 0 0;

        &:after {
            border: 0;
        }
    }
    .p-login-wrap-protocol-platformLoginQuick {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        margin-top: 55px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #666666;
        text-align: center;
        line-height: 17px;
        font-weight: 400;
        flex-wrap: wrap;

       .p-login-wrap-changeSelected {
            display: flex;
            flex-direction: row;
            align-items: center;
            .radio {
                width: 14px;
                height: 14px;
                margin: 4px;
                border-radius: 7px;
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629788534261_062532121.png");
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            .radio-check {
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629786073626_012462090.png");
            }
    
       }
        .p-login-wrap-checkbox.g-q-iconfont {
            padding: 3.5px 4px 2px;
            font-size: 14px;
            color: #00CAD8;
        }
    
        .p-login-wrap-text {
            margin-left: 1px;
        }

        .p-login-wrap-protocolText {
            color: rgb(0, 188, 212);
        }
    }
}


.p-login-quick-new {
    width: 100vw;
    display: flex;
    background-image: url('https://s.qunarzz.com/f_cms/2023/1688027485128_972154482.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    flex-direction: column;
    align-items: center;

    .hello-box{
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        margin-top:28px;
        margin-left: 29px;
        align-self: flex-start;


        .userImg {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 12px;
    
            .pic {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                border: 1px solid #fff;
            }
        }
    
        .phone-number {
            font-family: PingFangSC-Medium;
            font-size: 18px;
            color: #222222;
            letter-spacing: 0;
            font-weight: 500;
            text-align: center;
          
        }
    }

    .rights{
        margin-top:28px;
        margin-left: 29px;
        align-self: flex-start;
        width: 280px;
        height: 77px;
    }

    .button-box{
        position: relative;
        width: 317px;
        height: 77px;
        margin-top: 38px;
        background-size: cover;
        background-image: url(https://s.qunarzz.com/f_cms/2023/1688367106359_789162283.png);
        
        .button-decoration-img{
            position: absolute;
            top: -31px;
            right: 0px;
            z-index: 2;
            width: 146px;
            height: 56px; 
        }

        .confirm {
            width: 317px;
            height: 44px;
            line-height: 44px;
            background-image: linear-gradient(103deg, #FF8743 0%, #FB4C1A 97%);
            border-radius: 26.5px;
            color: #fff;
            font-size: 18px;
            border-radius: 44px;
            text-align: center;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text {
                margin: 0 auto;
                color: #fff;
            }
            &:after {
                border: 0;
            }
        }
    }
    

    .switch-phone {
        font-family: PingFangSC-Regular;
        margin-top: 16px;
        font-size: 14px;
        color: #00CAD8 ;
        font-weight: 400;
        padding: 0 0 0 0;
        background-color: transparent;

        &:after {
            border: 0;
        }
    }
    .p-login-wrap-protocol-platformLoginQuick {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 107.5px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #666666;
        text-align: center;
        line-height: 17px;
        font-weight: 400;
        flex-wrap: wrap;

       .p-login-wrap-changeSelected {
            display: flex;
            flex-direction: row;
            align-items: center;
            .radio {
                width: 14px;
                height: 14px;
                margin: 4px;
                border-radius: 7px;
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629788534261_062532121.png");
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            .radio-check {
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629786073626_012462090.png");
            }
    
       }
        .p-login-wrap-checkbox.g-q-iconfont {
            padding: 3.5px 4px 2px;
            font-size: 14px;
            color: #00CAD8;
        }
    
        .p-login-wrap-text {
            color: #999999;
            margin-left: 1px;
        }

        .p-login-wrap-protocolText {
            color: #00CAD8;
        }
    }
}