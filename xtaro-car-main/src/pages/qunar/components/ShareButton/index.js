import { View, But<PERSON>, Text } from "@tarojs/components";
import React from "react";
import "./index.scss";
class ShareButton extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            env: process.env.ANU_ENV,
        };
    }

    render() {
        return (
            <View class="fixed-style" style={{ bottom: this.props.bottom }}>
                {this.state.env === "qq" && (
                    <Button
                        formType="submit"
                        className=" rewrite-btn share-btn center"
                        open-type="share"
                        style={{ backgroundColor: this.props.backgroundColor }}
                    >
                        <Text className="g-q-iconfont share"></Text>
                    </Button>
                )}
            </View>
        );
    }
}

ShareButton.defaultProps = {
    backgroundColor: "rgb(90,192,238)", // 默认背景颜色
    bottom: "30px",
};
export default ShareButton;
