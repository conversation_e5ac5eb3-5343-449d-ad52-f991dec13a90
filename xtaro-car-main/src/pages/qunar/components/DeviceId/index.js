import { View, Canvas } from "@tarojs/components";
import Taro, {
    request,
    setStorage,
    setStorageSync,
    getStorageSync,
} from "@tarojs/taro";
import React from "react";
import md5 from "@/common/flight/md5";
import to from "@/npm/await-to-js/dist/await-to-js.umd.js";
import { fetchDeviceId, openId } from "@/common/utils/collect";
import util from "@/common/utils/util";
import "./index.scss";

const isBetaEnv = process.env.BUILD_ENV === "beta";

const beaconApi = isBetaEnv
    ? "https://antifp.qunar.com/wx/f1"
    : "https://antifp.qunar.com/wx/f1";

// 设备id本地储存字段
const MINIAPP_DEVIEC_ID = "ql7qGD";
const USER_DATA = "UserData";
const INIT_USER_DATA = "InitUserData";

class DeviceId extends React.Component {
    constructor(props) {
        super(props);
        this.timer = -1;
    }

    pageLifetimes = () => {
        return {
            show() {
                this.timer = setTimeout(() => {
                    // this实例不是react组件实例导致报错
                    this?.reactInstance?.sendbeaconApi?.("home");
                    this?.reactInstance?.genDeviceId?.();
                }, 1000);
            },
        };
    };

    componentWillUnmount() {
        clearTimeout(this.timer);
    }

    genDeviceId = () => {
        Taro.getStorage({
            key: MINIAPP_DEVIEC_ID,
            success: (r) => {
                // 兼容第一次的写法,需要改掉第一次的内容,否则下面的if不会进入,值也不回更新
                if (r.data && r.data.includes("___")) {
                    this.fetch();
                }
                // 校验 如果存在就不发送请求
                if (!r.data) {
                    // eslint-disable-next-line no-console
                    console.info(`[${new Date()}] [info]] - 本地无加密cookie`);
                    this.fetch();
                }
            },
        });
    };

    sendbeaconApi = (p) => {
        request({
            url: beaconApi,
            method: "GET",
            data: {
                id: openId,
                info: p,
            },
            success: () => {
                // eslint-disable-next-line no-console
                console.info(`[${new Date()}] [info]] - 埋点接口请求成功`);
            },
        });
    };

    fetch = async () => {
        const canvasInfo = await this.getCanvasInfo();
        fetchDeviceId("crdf", canvasInfo)
            .then((r) => {
                if (r.data?.data) {
                    this.setStorage(r.data.data);
                }
            })
            .catch((err) => {
                // eslint-disable-next-line
                console.warn(`[${new Date()}] [warn] - ${err.errMsg || err}`);
            });
    };

    /**
     * 目前微信 storage 中已经存在的两个 key(UserData, initUserData) 的值一样, 历史原因不可追溯
     * 也不清楚业务线的取值逻辑, 所以在这两个字段中都放入 value
     * @param {string} value
     */
    setStorage = (value) => {
        // 第一版 value 的存放位置, 已经有 api 通过这个字段使用了. 所以要保留原有的设置可用
        setStorage({
            key: MINIAPP_DEVIEC_ID,
            data: value,
        });
        this.setInitUserData(value);
        this.setUserData(value);
    };

    setUserData = (value) => {
        try {
            const json = util.getGlobalInfo();
            json.cookies[MINIAPP_DEVIEC_ID] = value;
            setStorageSync(USER_DATA, json);
        } catch (err) {
            /**
             * 目前(2021-02-22)没有手动上报错误的api
             * Promise.reject 会被自动上报捕捉
             */
            err.message = "DeviceId/index.js setUserData >>> " + err.message;
            Promise.reject(err);
        }
    };

    setInitUserData = (value) => {
        try {
            const json = getStorageSync(INIT_USER_DATA) || {};
            json.cookies ? "" : (json.cookies = {});
            json.cookies[MINIAPP_DEVIEC_ID] = value;
            setStorageSync(INIT_USER_DATA, json);
        } catch (err) {
            err.message =
                "DeviceId/index.js setInitUserData >>> " + err.message;
            Promise.reject(err);
        }
    };

    getCanvasInfo = async () => {
        const canvas2dRet = await to(this.getCanvas2D("J_device_canvas_2d"));
        const canvasWgRet = await to(this.getCanvasWGL("J_device_canvas_wgl"));
        return {
            cvs2d: canvas2dRet[1] || "",
            wgl: canvasWgRet[1] || "",
        };
    };

    // 抄的
    getCanvasWGL = (domId) => {
        // eslint-disable-next-line
        const self = this;
        return new Promise((rest) => {
            wx.createSelectorQuery()
                .in(this.wx)
                .select(`#${domId}`)
                .fields({ node: true, size: true })
                .exec((res) => {
                    const canvas = res[0].node;
                    const gl = canvas.getContext("webgl");
                    if (!gl) {
                        // eslint-disable-next-line no-console
                        console.warn(
                            `[${new Date()}] [warn]] - canvas webgl 不支持`,
                        );
                        self?.sendbeaconApi("wf");
                        return null;
                    }
                    var vShaderTemplate =
                        "attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}";
                    var fShaderTemplate =
                        "precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}";
                    var vertexPosBuffer = gl.createBuffer();
                    gl.bindBuffer(gl.ARRAY_BUFFER, vertexPosBuffer);
                    var vertices = new Float32Array([
                        -0.2, -0.9, 0, 0.4, -0.26, 0, 0, 0.732134444, 0,
                    ]);
                    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
                    vertexPosBuffer.itemSize = 3;
                    vertexPosBuffer.numItems = 3;
                    var program = gl.createProgram();
                    var vshader = gl.createShader(gl.VERTEX_SHADER);
                    gl.shaderSource(vshader, vShaderTemplate);
                    gl.compileShader(vshader);
                    var fshader = gl.createShader(gl.FRAGMENT_SHADER);
                    gl.shaderSource(fshader, fShaderTemplate);
                    gl.compileShader(fshader);
                    gl.attachShader(program, vshader);
                    gl.attachShader(program, fshader);
                    gl.linkProgram(program);
                    gl.useProgram(program);
                    program.vertexPosAttrib = gl.getAttribLocation(
                        program,
                        "attrVertex",
                    );
                    program.offsetUniform = gl.getUniformLocation(
                        program,
                        "uniformOffset",
                    );
                    //这行真机报错
                    // gl.enableVertexAttribArray(program.vertexPosArray)
                    gl.vertexAttribPointer(
                        program.vertexPosAttrib,
                        vertexPosBuffer.itemSize,
                        gl.FLOAT,
                        !1,
                        0,
                        0,
                    );
                    gl.uniform2f(program.offsetUniform, 1, 1);
                    gl.drawArrays(
                        gl.TRIANGLE_STRIP,
                        0,
                        vertexPosBuffer.numItems,
                    );
                    const b64 = gl.canvas
                        .toDataURL("image/png")
                        .replace("data:image/png;base64,", "");
                    var fp = md5(b64);
                    self?.sendbeaconApi("ws");
                    rest(fp);
                });
        });
    };

    // 抄的
    getCanvas2D = (domId) => {
        // eslint-disable-next-line
        const self = this;
        return new Promise((rest) => {
            this.wx
                .createSelectorQuery()
                .select(`#${domId}`)
                // .boundingClientRect()
                .node((res) => {
                    const canvas = res.node;
                    if (!canvas) {
                        // eslint-disable-next-line no-console
                        console.warn(
                            `[${new Date()}] [warn]] - canvas 2d 不支持`,
                        );
                        self?.sendbeaconApi("cf");
                        return;
                    }
                    const ctx = canvas.getContext("2d");
                    if (!ctx) {
                        return null;
                    }
                    ctx.rect(0, 0, 10, 10);
                    ctx.rect(2, 2, 6, 6);
                    ctx.textBaseline = "alphabetic";
                    ctx.fillStyle = "#f60";
                    ctx.fillRect(125, 1, 62, 20);
                    ctx.fillStyle = "#069";
                    ctx.font = "11pt Arial";
                    ctx.fillText(
                        "Cwm fjordbank glyphs vext quiz, \ud83d\ude03",
                        2,
                        15,
                    );
                    ctx.fillStyle = "rgba(102, 204, 0, 0.2)";
                    ctx.font = "18pt Arial";
                    ctx.fillText(
                        "Cwm fjordbank glyphs vext quiz, \ud83d\ude03",
                        4,
                        45,
                    );
                    // // canvas blending
                    // // http://blogs.adobe.com/webplatform/2013/01/28/blending-features-in-canvas/
                    // // http://jsfiddle.net/NDYV8/16/
                    ctx.globalCompositeOperation = "multiply";
                    ctx.fillStyle = "rgb(255,0,255)";
                    ctx.beginPath();
                    ctx.arc(50, 50, 50, 0, Math.PI * 2, true);
                    ctx.closePath();
                    ctx.fill();
                    ctx.fillStyle = "rgb(0,255,255)";
                    ctx.beginPath();
                    ctx.arc(100, 50, 50, 0, Math.PI * 2, true);
                    ctx.closePath();
                    ctx.fill();
                    ctx.fillStyle = "rgb(255,255,0)";
                    ctx.beginPath();
                    ctx.arc(75, 100, 50, 0, Math.PI * 2, true);
                    ctx.closePath();
                    ctx.fill();
                    ctx.fillStyle = "rgb(255,0,255)";
                    // // canvas winding
                    // // http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/
                    // // http://jsfiddle.net/NDYV8/19/
                    ctx.arc(75, 75, 75, 0, Math.PI * 2, true);
                    ctx.arc(75, 75, 25, 0, Math.PI * 2, true);
                    ctx.fill();
                    // ctx.fill('evenodd')
                    const b64 = ctx.canvas
                        .toDataURL("image/png")
                        .replace("data:image/png;base64,", "");
                    var fp = md5(b64);
                    self?.sendbeaconApi("cs");
                    rest(fp);
                })
                .exec();
        });
    };

    render() {
        return (
            <View>
                <Canvas
                    id="J_device_canvas_2d"
                    className="cvs"
                    type="2d"
                ></Canvas>
                <Canvas
                    id="J_device_canvas_wgl"
                    className="cvs"
                    type="webgl"
                ></Canvas>
            </View>
        );
    }
}

export default DeviceId;
