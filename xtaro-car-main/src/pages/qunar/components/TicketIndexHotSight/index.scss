.mp-homePage-around-sight {
    background-color: #fff;
    border-radius: 12px;
    margin: 12px 8px 8px 8px;
    padding: 12px;
    .mp-homePage-hot-sight-title-hot {
        font-size: 16px;
        font-weight: bold;
        width: 130px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .mp-homePage-hot-sight-title {
        font-size: 14px;
        font-weight: bold;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        margin-bottom: 1px;
    }
    .mp-homePage-around-sight-title {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 12px;

        .mp-homePage-around-sight-title-pri {
            font-weight: bold;
            font-size: 18px;
        }
        .mp-homePage-around-sight-title-right {
            font-size: 12px;
            color: #999;
            position: relative;
            .right {
                width: 5px;
                height: 5px;
                border-top: 1px solid #999;
                border-right: 1px solid #999;
                transform: rotate(45deg);
                position: absolute;
                right: -9px;
                top: 6px;
            }
        }
    }
    .mp-goApp-tip-container{
        margin: 0 10px;
        height: 20px;
        line-height: 20px;
        .mp-goApp-tip-text{
            font-size: 12px;
            color: #ff9800;
        }
    }
    .mp-homePage-around-sight-title-subtitle {
        display: flex;
        align-items: center;
        position: relative;
        font-size: 15px;
        margin-top: 15px;
        .mp-homePage-around-sight-title-active {
            font-size: 15px;
            font-weight: bold;
            z-index: 2;
        }
        .mp-homePage-around-sight-title-noActive {
            font-size: 15px;
        }
        .mp-homePage-around-sight-title-bg {
            width: 56px;
            height: 8px;
            border-radius: 12px;
            background-image: linear-gradient(90deg,white,#00d4e3);
            position: absolute;
            margin-left: 6px;
            // z-index: -1;
            bottom: 0;
            z-index: -2;
        }
        .mp-homePage-around-sight-title-bg-left {
            left: 0px;
        }
        .mp-homePage-around-sight-title-bg-right {
            left: 76px;
        }
        .mp-homePage-around-sight-title-bg-right-one {
            left: 30px;
        }
        .gap {
            margin-right: 22px;
        }
    }
    .mp-hotel-banner-wrapper{
        display: flex;
        margin-bottom: -10px;
    }
    .mp-hotel-banner {
        width: 350px;
        height: 40px;
        margin-top: 9px;
    }
    .mp-homePage-hot-sight-imgList {
        .box {
            margin-bottom: 5px;
        }
        .mp-homePage-around-sight-imgBox {
            height:100px;
            margin-top: 20px;
            position: relative;
        }
        image {
            width: 108px;
            height: 103px;
            border-radius: 8px;
            margin-right:10px;
        }
        .mp-homePage-around-sight-distance {
            display: inline-block;
            font-size: 12px;
            color: #666;
            margin-left: 5px
        }
        .mp-homePage-around-sight-area {
            display: inline-block;
            font-size: 12px;
            color: #666;
            margin-left: 5px;
            width: 100px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: middle;
        }
        .mp-homePage-around-sight-title {
            font-size: 14px;
            font-weight: bold;
        }
        .mp-homePage-around-sight-score-box {
            margin-top: 20px;
            .mp-homePage-around-sight-score {
                font-family: hotel_rn_num;
                color: #00CAD8;
                font-size: 14px;
                margin-right: 4px;
                .mp-homePage-around-sight-score-unit {
                    font-size: 12px;
                }
            }
            .mp-homePage-around-sight-comment {
                color: #666;
                font-size: 12px;
            }
            .mp-homePage-around-sight-desc {
                color: #666;
                font-size: 12px;
                margin-top: 8px;
                margin-bottom: 3px;
                width: 220px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .mp-homePage-around-sight-price-box {
            color: #FF6600;
            font-size: 16px;
            margin-top: 4px;
            .mp-homePage-around-sight-price-desc {
                font-size: 10px;
            }
            .mp-homePage-around-sight-price-number {
                font-size: 18px;
                font-family: hotel_rn_num;
            }
        }
        .mp-homePage-hot-sight-box {
            display: flex;
        }
    }
    .mp-homePage-around-sight-line {
        width: 1px;
        height: 10px;
        border-right: 0.5px solid #C0C0C0;
        display: inline-block;
        margin-left: 4px;
    }
}
.new-homePage-around-sight-extra-margin {
    margin: 12px 12px 12px 12px;
}