import { View, Text, Image } from "@tarojs/components";
import { getLocation, getStorageSync, setStorageSync, navigateTo } from "@tarojs/taro";
import QMark from "@/npm/@qnpm/qmark/dist/qmark.mini.umd.js";
import React from "react";
import Log from "@/common/utils/log";
import Request from "@/common/utils/request.js";
import EventEmitter from "@/common/utils/EventEmitter";
import "./index.scss";

const requestService = {
    getAroundSight:"https://wxapp.qunar.com/ticket/pw" + "/getAroundAndHotSight.json",
};

class TicketIndexHotSight extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            networkData: {
                status: 4,
                loadingDesc: "加载中...",
                showButton: true,
            },
            indexDataList: [],
            indexName: "hotSight",
            hotSight: [],
            hotSightTitle: "",
            dayTripListTitle: "",
            dayTripList: [],
            suggestList: {
                title: "",
                items: [],
            },
            searchValue: "",
            isShowSuggest: false,
            isShowAround: true,
            isShowCouponList: false,
            isShowShopHistory: false,
            shopHistoryList: [],
            defaultCity: "",
            showDayTrip: false,
            showTicket: false,
        };
    }

    componentWillMount() {
        this.initModuleList();
    }

    componentDidMount() {
        EventEmitter.addListener("TICKET_SELECT_CITY", () => {
            this.getListData();
        });
    }
    // 模块列表
    initModuleList = () => {
        getLocation({
            type: "gcj02",
            success: (res) => {
                this.lat = res.latitude;
                this.lng = res.longitude;
                this.getListData();
            },
            fail: () => {
                this.getListData();
            },
        });
    };
    getListData = () => {
        const currentCity = getStorageSync("ticket-city");
        Request({
            host: "https://wxapp.qunar.com/ticket/pw",
            service: "/getAroundAndHotSight.json",
            data: {
                lat: this.lat ? this.lat : "",
                lng: this.lng ? this.lng : "",
                searchCity: currentCity,
            },
            success: (response) => {
                let data = response.data;
                let bannerData = data.banner ? data.banner : {};
                let hotSight = data.hotSight ? data.hotSight : [];
                let dayTripList = data.dayTripList ? data.dayTripList : [];
                let city = data.city ? data.city : "北京";
                this.setState({
                    networkData: {
                        status: 0,
                    },
                    indexDataList: hotSight.items.length
                        ? hotSight.items
                        : dayTripList.items,
                    indexName: hotSight.items.length
                        ? "hotSight"
                        : "dayTripList",
                    hotSight: hotSight.items,
                    hotSightTitle: hotSight.title,
                    dayTripList: dayTripList.items,
                    dayTripListTitle: dayTripList.title,
                    bannerData: bannerData,
                    isShowBanner: Object.keys(bannerData).length ? true : false,
                    isShowSuggest: false,
                    defaultCity: city,
                    hotelUserApolloImage:
                        data.wechat_hotel_user_apollo_image || "",
                });
                if (hotSight && hotSight.items.length) {
                    QMark &&
                        QMark.log &&
                        QMark.log({
                            bizType: "ticket",
                            module: "recommendList",
                            appcode: "wx",
                            page: "ticketHome_WeChat",
                            id: "ticketShow",
                            operType: "show",
                            key: "ticket/ticketHome_WeChat/recommendList/show/ticketShow",
                            operTime: "*",
                        });
                }
                if (dayTripList && dayTripList.items.length) {
                    QMark &&
                        QMark.log &&
                        QMark.log({
                            bizType: "ticket",
                            module: "recommendList",
                            appcode: "wx",
                            page: "ticketHome_WeChat",
                            id: "onedaytripShow",
                            operType: "show",
                            key: "ticket/ticketHome_WeChat/recommendList/show/onedaytripShow",
                            operTime: "*",
                        });
                }
            },
            fail: () => {},
            complete: () => {},
        });
    };

    changeSight = (index) => {
        if (index === "dayTripList") {
            QMark &&
                QMark.log &&
                QMark.log({
                    bizType: "ticket",
                    module: "recommendList",
                    appcode: "wx",
                    page: "ticketHome_WeChat",
                    id: "onedaytripClick",
                    operType: "click",
                    key: "ticket/ticketHome_WeChat/recommendList/click/onedaytripClick",
                    operTime: "*",
                });
        } else {
            QMark &&
                QMark.log &&
                QMark.log({
                    bizType: "ticket",
                    module: "recommendList",
                    appcode: "wx",
                    page: "ticketHome_WeChat",
                    id: "ticketClick",
                    operType: "click",
                    key: "ticket/ticketHome_WeChat/recommendList/click/ticketClick",
                    operTime: "*",
                });
        }
        Log({
            info: {
                area: "ticketList",
                act: "click",
                type: "homeEnter",
                name: "ticketList",
            },
        });
        this.setState({
            indexDataList: this.state[index],
            indexName: index,
        });
    };

    goTicketList = () => {
        QMark &&
            QMark.log &&
            QMark.log({
                bizType: "ticket",
                module: "recommendList",
                appcode: "wx",
                page: "ticketHome_WeChat",
                id: "moreSightClick",
                operType: "click",
                key: "ticket/ticketHome_WeChat/recommendList/click/moreSightClick",
                operTime: "*",
            });
        const traceId = getStorageSync("ticketTraceId");
        setStorageSync("ticketFromArea", "index_hot");
        const newCatQuery = encodeURIComponent(
            `from_area=index_hot&log_id=${traceId}_home`,
        );
        let url = `/pages/ticket/index/index?catQuery=${newCatQuery}&from_wxHome=index_hot`;
        Log({
            info: {
                area: "ticketList",
                act: "click",
                type: "homeEnter",
                name: "ticketList",
            },
        });
        navigateTo({url});
    };

    goTicketListDetail = (id) => {
        const traceId = getStorageSync("ticketTraceId");

        Log({
            info: {
                area: "ticketList",
                act: "click",
                type: "homeEnter",
                name: "ticketList",
            },
        });
        let url;
        if (this.state.indexName === "hotSight") {
            setStorageSync("ticketFromArea", "index_hot_ticket_detail");
            const newCatQuery = encodeURIComponent(
                `from_area=index_hot_ticket_detail&log_id=${traceId}_home`,
            );
            url = `/pages/ticket/poiDetail/index?sightId=${id}&catQuery=${newCatQuery}&from_wxHome=index_hot_ticket_detail`;
        } else {
            setStorageSync("ticketFromArea", "index_hot_dayTrip_detail");
            const newCatQuery = encodeURIComponent(
                `from_area=index_hot_dayTrip_detail&log_id=${traceId}_home`,
            );
            url = `/pages/ticket/daytrip/index?spuId=${id}&catQuery=${newCatQuery}&from_wxHome=index_hot_dayTrip_detail`;
        }
        navigateTo({ url });
    };

    render() {
        return (
            <View class={this.props.isNew ? "mp-homePage-around-sight new-homePage-around-sight-extra-margin" : "mp-homePage-around-sight"}>
                <View class="mp-homePage-around-sight-title">
                    <View class="mp-homePage-around-sight-title-pri">
                        本地热销推荐
                    </View>
                    <Text class="mp-homePage-around-sight-title-right" onClick={this.goTicketList}>
                        更多景点<Text class="right"></Text>
                    </Text>
                </View>
                <View class="mp-homePage-around-sight-title-subtitle">
                    {this.state.hotSight.length ? (
                        <Text
                            class={this.state.indexName === "hotSight" ? "mp-homePage-around-sight-title-active gap" : "mp-homePage-around-sight-title-noActive gap"}
                            onClick={() => this.changeSight("hotSight")}
                        >
                            <Text class={this.state.indexName === "hotSight" && "mp-homePage-around-sight-title-bg"}
                            ></Text>
                            景点门票
                        </Text>
                    ) : null}

                    {this.state.dayTripList.length ? (
                        <Text
                            class={this.state.indexName === "dayTripList" ? "mp-homePage-around-sight-title-active" : "mp-homePage-around-sight-title-noActive"}
                            onClick={() => this.changeSight("dayTripList")}
                        >
                            <Text class={this.state.indexName === "dayTripList" && "mp-homePage-around-sight-title-bg"}></Text>
                            一日游产品
                        </Text>
                    ) : null}
                </View>
                {this.state.hotelUserApolloImage ? (
                    <View class="mp-hotel-banner-wrapper">
                        <Image
                            class="mp-hotel-banner"
                            src={this.state.hotelUserApolloImage}
                        ></Image>
                    </View>
                ) : null}

                <View class="mp-homePage-hot-sight-imgList">
                    {this.state.indexDataList.map((item) => (
                        <View class="box">
                            <View onClick={() => this.goTicketListDetail(item.id)} class="mp-homePage-hot-sight-box">
                                <View class="mp-homePage-around-sight-imgBox">
                                    <Image src={item.img}></Image>
                                </View>
                                <View class="mp-homePage-around-sight-score-box">
                                    {this.state.indexName === "hotSight" ? (
                                        <View class="mp-homePage-hot-sight-title-hot">
                                            {item.name}
                                        </View>
                                    ) : (
                                        <View class="mp-homePage-hot-sight-title">
                                            {item.name}
                                        </View>
                                    )}
                                    {this.state.indexName === "hotSight" && item.desc ? (
                                        <View class="mp-homePage-around-sight-desc">{`“${item.desc}”`}</View>
                                    ) : null}
                                    <Text class="mp-homePage-around-sight-score">
                                        {item.commentScore}
                                        <Text class="mp-homePage-around-sight-score-unit">
                                            分
                                        </Text>
                                        <Text class="mp-homePage-around-sight-line"></Text>
                                    </Text>
                                    {this.state.indexName === "hotSight" ? (
                                        <Text>
                                            <Text class="mp-homePage-around-sight-comment">
                                                {item.commentCount}条评价
                                            </Text>
                                            {item.distance ? (
                                                <Text class="mp-homePage-around-sight-line"></Text>
                                            ) : null}

                                            {item.distance ? (
                                                <Text class="mp-homePage-around-sight-distance">
                                                    {`距您${item.distance}`}km
                                                </Text>
                                            ) : null}
                                        </Text>
                                    ) : (
                                        <Text>
                                            {item.saleCount ? (
                                                <Text class="mp-homePage-around-sight-comment">
                                                    已售:{item.saleCount}
                                                </Text>
                                            ) : null}

                                            {item.saleCount ? (
                                                <Text class="mp-homePage-around-sight-line"></Text>
                                            ) : null}

                                            <Text class="mp-homePage-around-sight-area">
                                                {item.area}
                                            </Text>
                                        </Text>
                                    )}

                                    <View class="mp-homePage-around-sight-price-box">
                                        <Text class="mp-homePage-around-sight-price-desc">
                                            ￥
                                        </Text>
                                        <Text class="mp-homePage-around-sight-price-number">
                                            {item.qunarPrice}
                                        </Text>
                                        <Text class="mp-homePage-around-sight-price-desc">
                                            起
                                        </Text>
                                    </View>
                                </View>
                            </View>
                        </View>
                    ))}
                </View>
            </View>
        );
    }
}

export default TicketIndexHotSight;
