import {
    View,
    Text,
    Image,
    But<PERSON>,
    <PERSON><PERSON>View,
    Swiper,
    SwiperItem,
} from "@tarojs/components";
import {
    getStorageSync,
    showModal,
    showToast,
    setStorageSync,
    setStorage,
    redirectTo,
    navigateTo,
    reLaunch,
    removeStorageSync,
} from "@tarojs/taro";
import React from "react";
import "./index.scss";
import CommonUtils from "@/common/utils/util.js";
import TrainRequest from "@/common/train/requester";
import TrainService from "@/common/train/requestService";
import LoadingMixin from "@/common/train/LoadingMixin";
import EventEmitter from "@/common/utils/EventEmitter";
import dateFormat from "@/common/utils/date/format";
import watcher from "@/common/train/watcher";
import storageUtils from "@/common/train/storageUtil";
import TrainSearchListTools from "@/common/train/trainSearchListTools";
import cb from "@/common/utils/pageCb";
import handlePhoneLoginAuth, {
    initGetPhoneNumber,
    checkLoginFn,
} from "@/common/train/loginPhone";
import Api from "@/common/train/api";
import { holidaysDays } from "@/common/train/holidays";
import qPoint from "@/common/train/qmark";
import TrainDef from "@/common/train/define";
import User from "@/common/utils/user";
import Log from "@/common/utils/log";
import promiseUtil from "@/common/train/promiseUtil";
import login from "@/common/utils/login";
import subscribe from "@/common/utils/subscribe";
import { ABTestKey } from "@/common/train/trainABTest";
import TrainEntools from "@/common/train/entools";
import { markLog } from "@/common/utils/publicSubscribeMsg";
import newWatcher from "@/common/utils/watcher";
const sendRequest = newWatcher.apiWatcher("train");
import utils from "@/common/utils/util";
import { guardService, requestHost } from "@/common/train/guardService";
import { Base64 } from "@/common/utils/base64.js";
const { isEmptyObject, isEmptyArray } = utils;
import { trainRightList } from "./common/defaultTrainRightList";

var CACHE_KEY = TrainDef.TRAIN_CACHE_KEY;
var EVENT_NAME = TrainDef.TRAIN_EVENT_NAME;

const scene = "train_wxuv";
const serviceList = ["低价保障", "出行保障", "售后保障"];
function delay(ms) {
    return new Promise((resolve) => {
        setTimeout(resolve, ms);
    });
}
/** 代金券展示权重 **/
const CouponTypeSortMap = {
    // COMMON: 0, // 普通
    // DISCOUNT: 1, // 折扣
    // CUSTOM: 2, // 指定金额
    // REDUCE: 3, // 立减
    // FIXED: 4 //固定金额

    REDUCE: 0, // 立减
    CUSTOM: 1, // 指定金额
    RETURN_CASH: 2, //返现
    COMMON: 3, // 普通
    DISCOUNT: 4, // 折扣
    FIXED: 5, //固定金额
};
// 是否已经通知中台预热设备
let notifiedStartUp = false;
class TrainSearchBoxContainer extends React.Component {
    constructor(props) {
        super(props);
        const { cookies } = CommonUtils.getGlobalInfo();
        let openId = cookies.openId;
        let searchHistoryObj = getStorageSync("searchHistoryNew") || {};
        let searchHistory = searchHistoryObj[openId] || [];
        let query = this.props.query || {};
        this.defaultTrainRightsList =
            trainRightList?.[process.env.ANU_ENV] || [];
        this.state = {
            serviceList,
            depCity: "北京", //城市|车站
            arrCity: "上海",
            exchangeStatus: false,
            displayMonth: "8",
            displayDay: "28",
            dateWeek: "周二",
            equityData: "",
            bdSourceCode: "",
            isOnlyGaotie: false,
            anuEnv: process.env.ANU_ENV,
            isRebook: false, // 是否是改签页面
            enableChangeArrCity: true, // 是否允许变更到站
            isShowYellowTip: true,
            isShowSpecialTip: true,
            isShowCloseTipIcon: true,
            isShowNewUserProcessBarQconfig: false,
            isShowNewUserModal: false,
            trainNewUserModalOpenOrCloseStatus: false,
            tipDetailUrl: "",
            yellowTip: {}, //'这是测试tip这是测试tip这是测试tip这是测试tip这是测试tip这是测试tip',
            specialTip: {},
            isHightSpeedSelected: false,
            isStudentSelected: false,
            searchHistory,
            btnConfig: {}, // 搜索按钮 汽车票 船票 我的订单 按钮，换肤配置
            openId,
            largestAmount: 0,
            couponData: {},
            stock_id: query.stock_id || "", //微信卡券
            associate: query.associate || "", //微信卡券
            ciphertext: query.ciphertext || "", //微信卡券
            nonce: query.nonce || "", // 微信卡券
            isNotWxCoupon: true,
            banner_source: [
                {
                    bg_img: "https://s.qunarzz.com/open_m_train/wechat/houbu_bg_750x400.jpg",
                    content_img:
                        "https://s.qunarzz.com/open_m_train/wechat/houbu_670x200px.jpg",
                    url: "https://hy.train.qunar.com/train_sino_hy/index.html?hybridid=train_sino_hy#/candidateIntroduction",
                },
            ],

            isOpenSpringFestivalRight: false, //是否开启春运免费抢票特权
            holidayStr: "", // 特殊春节日期标识
            isLoginedQunar: false,
            isSubscribe: false,
            trainRightsList: this.defaultTrainRightsList || [],
            trainBannerList: [],
            trainRightsListTitleMap: {},
            trainCitySelectPage: true, //车站搜索
            searchBizType: "train",
            moveStyle: {},
            moveDupStyle: {},
            trainListInterfaceIsB: false,
            subscribeInfo: {},
            guideToSubscribe: true, //是否引导订阅
            jumpToStudentVerify: false, // 判断是否跳转学生资质核验页（登录12306成功回调处判断）
            depCityObj: {},
            arrCityObj: {},
            skipEnv: process.env.SKIP,
            reducePriceBubble: "", //搜索按钮气泡
            sourcePositionData: {},
        };
        if (!this.props.query) {
            this.props.query = {};
        }
        this.isLoginedQunar = false;
        this.onSearch = this.onSearch.bind(this);

        this.preRequestSearchListData =
            this.preRequestSearchListData.bind(this);
        this.setSearchListOptionalDates =
            this.setSearchListOptionalDates.bind(this);
        this.gotoMyRight = this.gotoMyRight.bind(this);
        this.handleSubMessage = this.handleSubMessage.bind(this);
        this.loginSuccess = this.loginSuccess.bind(this);
        this.gotoReleaseTip = this.gotoReleaseTip.bind(this);
        this.gotoPolicySearch = this.gotoPolicySearch.bind(this);
        this.clearSearchHistory = this.clearSearchHistory.bind(this);
        this.onSearchByHistory = this.onSearchByHistory.bind(this);
        this.resourcePositionClick = this.resourcePositionClick.bind(this);

        this.trainBannerListShow = [];
        this.voucherList = [];
    }

    async componentWillMount() {
        await this.checkLoginQunar();
        this.isLoginQunar();
        this.wxCouponInit();
        this.notifyDeviceStartUp();
        if (this.state.anuEnv === "wx" || this.state.anuEnv === "ali") {
            this.getTrainSubscribeInfo();
            this.initSendCoupon();
        }

        if (this.state.anuEnv === "wx") {
            this.getTrainBanner();
            this.searchCardResourcePosition("wxBizBottom");
        }

        if (this.state.anuEnv === "ali") {
            this.trainDidAmount();
        }
    }

    //搜索卡片资源位
    searchCardResourcePosition = async (posCode) => {
        const storage = await utils.getGlobalInfoAsync();
        const { openId } = storage.user;
        const params = {
            openId,
            resourcePosCode: posCode,
            biz: "train",
        };
        const response = await Api.requestSearchCardResourcePosition(
            params,
        ).catch(() => {});
        const res = response?.data || {};
        if (
            res?.resources &&
            res?.resources.length > 0 &&
            res?.resources[0]?.sourceMaterial?.length > 0
        ) {
            const sourceMaterial = res?.resources[0]?.sourceMaterial?.[0] || {};
            const type = res?.resources[0]?.type;
            sourceMaterial.posCode = posCode;
            sourceMaterial.isShow = true;
            sourceMaterial.positionType = type;
            if (posCode === "wxBizBottom") {
                const copyWriting = sourceMaterial?.copyWriting || "";
                const copyWritings =
                    copyWriting &&
                    copyWriting
                        .split(/(\d+.\d+|\d+)/)
                        .map((num, index) => ({
                            text: num,
                            isNum: /\d+.\d+|\d+/.test(num),
                        }));
                sourceMaterial.copyWritings = copyWritings;
                qPoint(
                    `train/new_train_index/resourcePosition/show/${posCode}`,
                    {
                        positionType: type,
                    },
                );
                this.setState({
                    sourcePositionData: sourceMaterial,
                });
            } else if (posCode === "wxBizPopup") {
                const { couponData = {} } = sourceMaterial || {};
                let newCouponData = (couponData && couponData.success) || [];
                if (newCouponData.length > 0) {
                    newCouponData.forEach((item = {}) => {
                        item.couponValue.formatValue =
                            item.couponValue &&
                            parseFloat(item.couponValue.number);
                    });
                    this.voucherList = newCouponData;
                    this.openVoucherModal();
                }
            }
        }
    };

    openVoucherModal = () => {
        if (this.voucherList.length > 0) {
            this.props.openVoucherModal({
                voucherList: this.voucherList,
            });
        }
    };

    getTrainSubscribeInfo = async () => {
        const res = await this.getConfig("subScribe_trainWx");
        const {
            data: {
                trainSubscribeScene = "",
                trainSubscribeBiz = "",
                trainRightSubscribeScene = "",
            } = {},
        } = res;
        const [trainSubscribeList, trainRightSubscribeList] = await Promise.all(
            [
                subscribe.querySubscribeList(trainSubscribeScene),
                subscribe.querySubscribeList(trainRightSubscribeScene),
            ],
        );
        const [isSubscribePop, isRightSubscribePop] = await Promise.all([
            this.subscribeCanPop(trainSubscribeBiz, trainSubscribeScene),
            this.subscribeCanPop(trainSubscribeBiz, trainRightSubscribeScene),
        ]);
        this.setState({
            subscribeInfo: {
                biz: trainSubscribeBiz,
                scene: trainSubscribeScene,
                list: trainSubscribeList,
                trainRightSubscribeScene,
                trainRightSubscribeList,
                isSubscribePop,
                isRightSubscribePop,
            },
        });
    };

    initSendCoupon = async () => {
        const {
            activityNo = "",
            channel = "",
            shareToken,
        } = this.props.query || {};
        if (activityNo.length === 0 || channel.length === 0) {
            return;
        }
        let isLoginQunar = await checkLoginFn();
        if (!isLoginQunar) {
            //未登录弹窗引导登录
            showModal({
                title: "登录提示",
                content: "登录成功才可领取优惠券。",
                confirmColor: "#00bcd4",
                confirmText: "确认",
                cancelText: "取消",
                success: () => {
                    this.initSendCouponToLogin();
                },
            });
            return;
        }
        const params = {
            activityNo,
            thirdPartyNo: shareToken,
            channel,
        };

        const response = await Api.requestActivitySendCoupon(params).catch(
            () => {},
        );
        if (
            response?.responseBizHead?.code === 0 &&
            response?.listBenefit.length > 0
        ) {
            const amount = response?.listBenefit?.[0]?.amount;
            watcher.sendWatcher("FrontGuideSendCouponSuccess_new");
            showToast({
                title: `已成功领取${amount}元优惠券`,
                duration: 3000,
                icon: "none",
            });
        } else {
            watcher.sendWatcher("FrontGuideSendCouponFail_new");
            showToast({
                title:
                    response?.responseBizHead?.message ||
                    "网络异常，请稍后再试",
                duration: 3000,
                icon: "none",
            });
        }
    };

    initSendCouponToLogin = () => {
        const that = this;
        login(
            () => {
                that.setState({
                    isLoginedQunar: true,
                });
                this.initSendCoupon && this.initSendCoupon(this);
            },
            {
                source: "train",
            },
        );
    };

    // 获取火车新banner
    getTrainBanner = async () => {
        const params = {
            resourceNo: "train_home_tip_top",
        };
        const response = await Api.requestDisplaySpaceAdList(params).catch(
            () => {},
        );
        const marketList = response?.displaySpaceAdItemList || [];
        if (marketList && !isEmptyArray(marketList)) {
            this.setState({
                trainBannerList: marketList,
            });
            this.onLogChangeBanner({
                current: 0,
            });
        }
    };

    subscribeCanPop = async (biz, scene) => {
        return new Promise(async (resolve) => {
            const storage = await utils.getGlobalInfoAsync();
            const { openId } = storage.user;
            // http://market-urouter-jizhun-starlord.beta.qunar.com/starlord/subscribe/canPop
            sendRequest({
                service: "/gw/m/api/subscribe/can/pop",
                host: "https://m.flight.qunar.com",
                // host: 'http://market-urouter-jizhun-starlord.beta.qunar.com',
                // service: '/starlord/subscribe/canPop',
                param: {
                    biz,
                    openId,
                    scene,
                },
                success: (response) => {
                    const { status, data } = response || {};
                    if (status === 0) {
                        resolve(data);
                    } else {
                        resolve(false);
                    }
                },
                fail: () => {
                    resolve(false);
                },
            });
        });
    };

    getConfig = (key) => {
        return new Promise((resolve) => {
            sendRequest({
                service: "/mpx/getQconfig",
                resCheckField: "data",
                param: {
                    name: key + ".json",
                },
                success: (res) => {
                    resolve(res);
                },
            });
        });
    };

    onShow() {
        this.getHotConfig();
        if (this.state.anuEnv === "wx" || this.state.anuEnv === "ali") {
            this.getTrainSubscribeInfo();
        }
    }

    componentWillReceiveProps(nextProps) {
        if (this.props.isLoginQunar !== nextProps.isLoginQunar) {
            this.setState({
                isLoginedQunar: nextProps.isLoginQunar,
            });
            this.dealWithReducePrice();
        }
    }

    componentDidMount() {
        if (this.state.anuEnv === "wx") {
            this.trainDidAmount();
        }
    }

    trainDidAmount = async () => {
        this.pageParams = this.props.query;
        let isSubscribe = this.judgeBindWeChat();
        this._init();
        this.getAbTest();
        this._registerListeners();
        await this.getHotConfig();
        this.dealWithReducePrice();
        this.getSubscribeList(scene);
        this.setState({
            isSubscribe,
        });
        this.dealWithYellowTipScroller();
    };

    /**
     * 搜索按钮气泡
     * 优先级：先券后全流程感知
     * @param type 来源
     * **/
    dealWithReducePrice = async (type) => {
        const couponMark = await this.requestCouponList();
        const displayAdMark = await this.refreshRequestDisplaySpaceAdList(type);
        let reducePriceBubble = "";
        if (
            couponMark &&
            couponMark.largestAmount > 0 &&
            couponMark.resultCouponAmount
        ) {
            reducePriceBubble = couponMark.resultCouponAmount;
        } else if (!isEmptyObject(displayAdMark)) {
            const { indexDesc = "" } = displayAdMark;
            reducePriceBubble = indexDesc;
        }
        this.setState({
            reducePriceBubble,
        });
    };

    _getABHash = (abKeys) => {
        return this._getSimpleHash(JSON.stringify(abKeys));
    };

    _getSimpleHash = (str) => {
        let hash = 0;
        if (str.length === 0) return hash;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = (hash << 5) - hash + char;
            hash |= 0; // Convert to 32bit integer
        }
        return `${hash}`;
    };

    getAbTest = async () => {
        try {
            const ABTestResults = await Api.fetchTheABTestResultList();
            const trainListInterface = ABTestKey.trainListInterface;
            const trainListStyle = ABTestKey.trainListStyle;
            const trainListInterfaceIsB =
                ABTestResults[trainListInterface] === "B";
            const trainListStyleIsB = ABTestResults[trainListStyle] === "B";
            const isTrainRN2TaroB =
                ABTestResults[ABTestKey.trainRN2Taro] === "B";

            this.setState({
                trainListInterfaceIsB,
                trainListStyleIsB,
                isTrainRN2TaroB,
            });

            const key = this._getABHash([ABTestKey.trainRN2Taro]);
            storageUtils.setStorageSync(key, ABTestResults);
        } catch (e) {
            console.warn(e);
        }
    };

    dealWithYellowTipScroller = () => {
        let { yellowTip = {} } = this.state;
        const { tipContent = "" } = yellowTip;
        if (!tipContent) {
            return;
        }
        let moveStyle = {},
            moveDupStyle = {},
            delay = 1;
        if (tipContent.length > 25) {
            let time1 = Math.round(tipContent.length / 4);
            let time2 = Math.round((tipContent.length + 25) / 4);
            moveDupStyle.paddingLeft = "100%";
            moveDupStyle.display = "flex";
            moveDupStyle.animation = `scroll ${time2}s linear ${time1 + delay}s infinite normal`;

            moveStyle.animation = `scroll ${time1}s linear ${delay}s 1 normal forwards`;
            this.setState({
                moveStyle,
                moveDupStyle,
            });
        }
    };
    getSubscribeList = async (scene) => {
        const listRes = await subscribe.querySubscribeList(scene);
        if (
            listRes &&
            listRes.ret &&
            listRes.data &&
            listRes.data.templateInfoList &&
            listRes.data.templateInfoList.length > 0
        ) {
            setStorageSync(scene, listRes);
        }
    };

    loginQunar = (func) => {
        if (this.state.isLoginedQunar) {
            func && func(this);
        } else {
            let that = this;
            login(
                () => {
                    that.setState({
                        isLoginedQunar: true,
                    });
                    func && func(this);
                },
                {
                    source: "train",
                },
            );
        }
    };

    refreshRequestDisplaySpaceAdList = async (type) => {
        const {
            depCity,
            arrCity,
            isShowNewUserProcessBarQconfig,
            isLoginedQunar,
        } = this.state || {};
        if (isShowNewUserProcessBarQconfig === false) {
            return {};
        }
        const params = {
            resourceNo: "train_search_button_tap",
            departureStation: depCity,
            arrivalStation: arrCity,
        };
        const response = await Api.requestDisplaySpaceAdList(params).catch(
            () => {},
        );
        const marketList = response?.displaySpaceAdItemList || [];
        if (isLoginedQunar === true || type === "loginSuccess") {
            // 记录 uv
            watcher.sendWatcher("showLoginedNewUserRightsIcon");
            Api.sendNewUserProcessUV("newUserProcessIndexPage");
        }
        if (marketList && !isEmptyArray(marketList)) {
            return marketList[0];
        } else {
            return {};
        }
    };

    componentWillUnmount() {
        this._removeListeners();
    }

    requestCouponList = async () => {
        const { couponDisplayDescObj = {} } = this.state || {};
        const params = {
            pageSource: "home_index",
            verification: 1,
        };
        const response = await Api.requestCouponList(params).catch(() => {});
        LoadingMixin.hideNetwork.call(this);
        const couponData = response?.data?.trainCouponResult || [];
        this.setState({
            couponData,
        });
        storageUtils.setStorageSync("couponData", couponData);
        const coupon = this.getMaxCouponWithCoupons(couponData);
        const couponType = coupon?.couponType || "";
        const amount = coupon?.amount || 0;
        const couponDisplayDesc = couponDisplayDescObj[couponType] || "";
        const _couponDisplayDesc =
            couponDisplayDesc && couponDisplayDesc.replace(/%s/, amount);

        return {
            largestAmount: amount || 0,
            resultCouponAmount: _couponDisplayDesc,
        };
    };

    wxCouponInit = async () => {
        // 判断来源，是否是微信卡券
        if (this.state.stock_id) {
            // 微信卡券来源
            watcher.sendWatcher("trainIndex_page_source_wx_coupon_new");
            // 判断是否登录，已登录直接 请求后端接口，未登录的先登录在请求后端接口
            this.isLoginQunar(this.wxBindCoupon.bind(this));
        } else {
            this.isLoginQunar();
        }
    };
    // 微信用户绑定卡券
    wxBindCoupon = async () => {
        const { stock_id, associate, ciphertext, nonce } = this.state;
        let params = {
            bdSource: "wxcard",
            stockId: stock_id,
            associate,
            ciphertext,
            nonce,
        };
        try {
            await promiseUtil.postRequest(
                TrainService.requestService.WX_COUPON,
                params,
            );
        } catch (e) {
            showToast({ title: "出错啦，请检查代金券是否失效", icon: "none" });
        }
    };

    checkLoginQunar = () => {
        return new Promise(async (resolve) => {
            let isLoginQunar = await checkLoginFn();
            this.setState(
                {
                    isLoginedQunar: isLoginQunar,
                },
                resolve,
            );
        });
    };

    isLoginQunar = async (callback) => {
        let login = this.state.isLoginedQunar;
        if (login) {
            // 已登录，直接搜索
            let data = this.state;
            data.isLoginedQunar = true;
            data.isNotWxCoupon = callback ? false : true;
            this.setState({
                ...data,
            });
            callback && callback();
        } else {
            // 未登录，先去授权登录后，然后再请求后端接口
            let data = this.state;
            data.isLoginedQunar = false;
            data.isNotWxCoupon = callback ? false : true;
            this.setState({
                ...data,
            });
        }
    };
    /** 获取可用券金额从大到小的排序 */
    getMaxCouponWithCoupons = (coupons) => {
        const filterCoupons = coupons || [];
        return filterCoupons.sort((a, b) => {
            const aCouponTypeSort = CouponTypeSortMap[a.couponType];
            const bCouponTypeSort = CouponTypeSortMap[b.couponType];
            if (bCouponTypeSort === aCouponTypeSort) {
                let minSortCouponType = ["DISCOUNT", "FIXED"];
                if (minSortCouponType.includes(a.couponType)) {
                    return a.amount - b.amount;
                } else {
                    return b.amount - a.amount;
                }
            }
            return bCouponTypeSort - aCouponTypeSort;
        })[0];
    };
    getHotConfig = () => {
        var that = this;
        return new Promise((resolve, reject) => {
            TrainRequest.request({
                service: TrainService.requestService.HotConfig,
                param: { conf_type: 200 },
                success: function (res) {
                    var data = res.data;
                    if (data && data.status == 0 && data.conf) {
                        var content = JSON.parse(data.conf);
                        let yellowTip = "",
                            specialTips = "",
                            btnConfig = {},
                            isShowNewUserProcessBarQconfig = false,
                            searchBtn = "",
                            trainRightsList = [...that.defaultTrainRightsList],
                            trainCitySelectPage =
                                that.state.trainCitySelectPage,
                            couponDisplayDescObj = {},
                            trainRightsListTitleMap = {},
                            guideToSubscribe = false,
                            aliSkipLogin = false;
                        try {
                            yellowTip = JSON.parse(content["json.yellowTip"]);
                        } catch (e) {}
                        try {
                            specialTips = JSON.parse(
                                content["json.trainIndexPage.specialTip"],
                            );
                        } catch (e) {}
                        try {
                            btnConfig = JSON.parse(
                                content["json.trainIndex.btnConfig"] || "{}",
                            );
                            searchBtn = JSON.parse(btnConfig.searchBtn || "{}");
                            btnConfig = Object.assign(btnConfig, {
                                searchBtn,
                            });
                        } catch (e) {}
                        try {
                            isShowNewUserProcessBarQconfig =
                                content["switch.newUserProcessBar"] === "true";
                        } catch (e) {}
                        try {
                            trainCitySelectPage =
                                content["switch.mini.trainCitySelectPage"] ===
                                "true";
                        } catch (e) {}
                        try {
                            const rightList =
                                content["json.homePage.rightList"];
                            const confitList =
                                JSON.parse(rightList)?.[that.state.anuEnv];
                            if (confitList) {
                                trainRightsList = confitList;
                                trainRightsList.splice(
                                    4,
                                    trainRightsList.length,
                                );
                            }
                        } catch (e) {}
                        try {
                            trainRightsListTitleMap =
                                JSON.parse(
                                    content[
                                        "json.trainIndex.rightsListTitleMap"
                                    ],
                                ) || {};
                        } catch (e) {}
                        try {
                            const couponDisplayRules =
                                JSON.parse(
                                    content[
                                        "json.train.couponDisplayRules.config"
                                    ],
                                ) || {};
                            couponDisplayDescObj = (couponDisplayRules &&
                                couponDisplayRules.indexCouponDescObj) || {
                                FIXED: "%s元购票",
                                DISCOUNT: "%s折购票",
                                COMMON: "补贴%s元",
                                RETURN_CASH: "返%s元",
                                CUSTOM: "补贴%s元",
                                REDUCE: "补贴%s元",
                            };
                        } catch (e) {}
                        try {
                            aliSkipLogin = content['switch.mini.aliSkipLogin'] === 'true';
                        } catch (e) {}
                        const tipContent =
                            specialTips && specialTips.tipContent;
                        const tipContentArr = tipContent.split("/");
                        that.setState(
                            {
                                btnConfig,
                                yellowTip,
                                isShowNewUserProcessBarQconfig,
                                specialTip: specialTips,
                                specialTipContentArr: tipContentArr,
                                isOpenSpringFestivalRight:
                                    content["switch.springFestivalRight"] ==
                                    "true"
                                        ? true
                                        : false,
                                trainRightsList,
                                trainCitySelectPage,
                                couponDisplayDescObj,
                                guideToSubscribe:
                                    content[
                                        "switch.trainIndex.guideToSubscribe"
                                    ] === "true"
                                        ? true
                                        : false,
                                trainRightsListTitleMap,
                                aliSkipLogin
                            },
                            resolve,
                        );
                    }
                },
                fail: function () {
                    reject();
                },
            });
        });
    };

    /**
     * 如果本地有 123 账号，通知中台设备接口预热
     */
    notifyDeviceStartUp = async () => {
        if (notifiedStartUp || !this.state.isLoginedQunar) return;
        notifiedStartUp = true;
        const userInfo12306 = getStorageSync("userInfo12306");
        console.log("userInfo12306", userInfo12306);
        if (userInfo12306 && userInfo12306.username) {
            Api.notifyStartUp({
                channel: "wx",
                username: userInfo12306.username,
            });
        }
    };
    handleGetPhoneNumber = async (flag, e) => {
        Log({
            info: {
                area: "trainOther",
                act: "click",
                type: "homeEnter",
                name: "trainOther",
            },
        });
        if (!(e && e.detail && e.detail.encryptedData)) {
            return;
        }
        initGetPhoneNumber(e.detail.encryptedData, e.detail.iv);
        // iv, encryptedData 微信给的秘钥
        cb.wxGetPhoneCb(e, async ({ encryptedData, iv }) => {
            let data = await User.loginByWxPhone({ iv, encryptedData });
            if (data && data.ret) {
                // 登录成功
                this.isLoginedQunar = true;
                this.setState({
                    isLoginedQunar: true,
                });
                if (flag === "welfare") {
                    this.requestSendWelfare();
                }
            } else {
                this.isLoginedQunar = false;
                this.setState({
                    isLoginedQunar: false,
                });
                showToast({
                    title: "登录失败，请稍后再试",
                    icon: "none",
                    duration: 3000,
                });
            }
        });
    };

    _init = () => {
        var that = this,
            depCityObj,
            arrCityObj,
            depCity,
            arrCity,
            depDate,
            oneDay = 86400000;
        var pageParams = that.pageParams;
        this._rebookDateLimit();
        //初始化日期和城市
        depDate = getStorageSync(CACHE_KEY.DEP_DATE_KEY);
        depCityObj = getStorageSync(CACHE_KEY.DEP_CITY_KEY_OBJ) || {};
        arrCityObj = getStorageSync(CACHE_KEY.ARR_CITY_KEY_OBJ) || {};
        depCity = depCityObj?.city || "";
        arrCity = arrCityObj?.city || "";
        if (pageParams.dep) {
            depCity = decodeURIComponent(pageParams.dep);
            if (pageParams.dep !== depCityObj.city) {
                setStorageSync(CACHE_KEY.DEP_CITY_KEY_OBJ, {});
                depCityObj = {};
            }
        }
        if (pageParams.arr) {
            arrCity = decodeURIComponent(pageParams.arr);
            if (pageParams.arr !== depCityObj.city) {
                setStorageSync(CACHE_KEY.ARR_CITY_KEY_OBJ, {});
                arrCityObj = {};
            }
        }
        if (pageParams.date) {
            var depDateString = decodeURIComponent(pageParams.date);
            var date = new Date(depDateString.replace(/-/g, "/"));
            if (date) {
                depDate = date.getTime();
                setStorageSync(CACHE_KEY.DEP_DATE_KEY, depDate);
            }
        }
        var shiftTime = (pageParams.shiftDay || 0) * oneDay;
        depDate = (depDate >= Date.now() && depDate) || Date.now() + shiftTime;
        that.setState({
            arrCity: arrCity || this.state.arrCity,
            depCity: depCity || this.state.depCity,
            depDate: depDate || this.state.depDate,
            arrCityObj,
            depCityObj,
        });
        that._setDate(new Date(depDate));
    };

    // 预设置车次列表页日期数据
    setSearchListOptionalDates = () => {
        try {
            let depDate = getStorageSync(CACHE_KEY.DEP_DATE_KEY);
            setTimeout(() => {
                TrainSearchListTools.setGlobalDataSearchListOptionalDates(
                    +new Date(),
                    60,
                    depDate,
                ); // 预设值 车次列表顶部日期数据
            }, 200);
        } catch (error) {}
    };

    //注册事件监听
    _registerListeners = () => {
        var that = this;
        that._dateListener = EventEmitter.addListener(
            EVENT_NAME.CALENDAR_DATE_CHANGE,
            function (date) {
                that._setDate(date);
            },
        );
        that._dateChangeListener = EventEmitter.addListener(
            EVENT_NAME.SEARCHLIST_DATE_CHANGE,
            function (date) {
                that._setDate(date);
            },
        );
        that._cityListener = EventEmitter.addListener(
            EVENT_NAME.CITY_CHANGE,
            function (cityObj) {
                that._setCity(cityObj);
            },
        );
        this._login12306Listener = EventEmitter.addListener(
            EVENT_NAME.LOGIN_12306_SUCCESS,
            function (res) {
                if (res.loginSuccess) {
                    // 判断是否为跳转学生资质核验
                    if (that.state.jumpToStudentVerify) {
                        that.jumpToStudentVerify();
                        return;
                    }
                }
            },
        );
    };

    // 移除事件监听
    _removeListeners = () => {
        var that = this;
        that._dateListener && that._dateListener.removeListener();
        that._dateChangeListener && that._dateChangeListener.removeListener();
        that._cityListener && that._cityListener.removeListener();
        that._login12306Listener && that._login12306Listener.removeListener();
    };

    //设置日期
    _setDate = (date) => {
        try {
            let now = new Date(Date.now());
            var displayDate = dateFormat(date, "m月d日"),
                searchDate = dateFormat(date, "yyyy-mm-dd"),
                dateWeek = dateFormat(date, "周w"),
                oneDay = 86400000,
                today = new Date(
                    now.getFullYear() +
                        "/" +
                        (now.getMonth() + 1) +
                        "/" +
                        now.getDate(),
                ).getTime(),
                days = parseInt((date - today) / oneDay),
                displayMonth = displayDate.split("月")[0],
                displayDay = displayDate.match(/[0-9]+月([0-9]+)日/)[1];
            var dayStr = ["今天", "明天", "后天"];
            if (days < 3) {
                dateWeek += " " + dayStr[days];
            }
            setStorage({
                key: CACHE_KEY.DEP_DATE_KEY,
                data: date.getTime(),
            });
            // todo, 设置节假日 holidaysDays
            let holidayStr = holidaysDays[searchDate] || "";
            this.setState({
                displayMonth,
                displayDay,
                searchDate,
                dateWeek,
                holidayStr,
            });
        } catch (error) {
            // console.error(error);
        }
    };

    _setCity = (cityObj) => {
        var obj = {},
            keyObj = cityObj.isDep
                ? CACHE_KEY.DEP_CITY_KEY_OBJ
                : CACHE_KEY.ARR_CITY_KEY_OBJ,
            viewDataKey = cityObj.isDep ? "depCity" : "arrCity",
            viewDataKeyObj = cityObj.isDep ? "depCityObj" : "arrCityObj";
        obj[viewDataKey] = cityObj.city;
        obj[viewDataKeyObj] = cityObj;
        this.setState(obj);

        setStorage({
            key: keyObj,
            data: cityObj,
        });
    };

    onSearchByHistory = (dep = {}, arr = {}) => {
        watcher.sendWatcher("search_by_history_new");
        // let history = _history.split('-');
        let depCity = dep?.city || "",
            arrCity = arr?.city || "";
        EventEmitter.dispatch(EVENT_NAME.CITY_CHANGE, { isDep: true, ...dep });
        EventEmitter.dispatch(EVENT_NAME.CITY_CHANGE, { isDep: false, ...arr });
        this.setState({
            depCity,
            arrCity,
            depCityObj: dep,
            arrCityObj: arr,
        });
    };

    afterSubscribe = (type) => {
        if (type === "search") {
            this.onSearch({ skipSubscribe: true });
        } else if (type === "right") {
            this.gotoMyRight({ skipSubscribe: true });
        }
    };

    trainSubscribeMsg = (list, type) => {
        return new Promise((resolve) => {
            if (!list) {
                resolve({
                    status: "1",
                    msg: "获取订阅列表失败",
                });
                this.afterSubscribe(type);
                return;
            }
            subscribe.requestSubscribeMessage(
                list,
                () => {
                    const markData = {
                        status: "0",
                        msg: "订阅消息成功",
                    };
                    markLog("0");
                    this.afterSubscribe(type);

                    resolve(markData);
                },
                () => {
                    const markData = {
                        status: "1",
                        msg: "订阅消息失败",
                    };
                    this.afterSubscribe(type);

                    markLog("1");
                    resolve(markData);
                },
            );
        });
    };

    /**
     * @param type 搜索类型
     * @param fromAuthLogin 是否为登录授权后搜索 默认false
     * @param skipSubscribe 是否跳过订阅
     * **/
    onSearch = async ({
        type = "",
        fromAuthLogin = false,
        skipSubscribe = false,
        isRedirect = false,
    }) => {
        const {
            anuEnv,
            guideToSubscribe,
            subscribeInfo = {},
            depCityObj = {},
            arrCityObj = {},
            trainListStyleIsB,
            isTrainRN2TaroB = false,
        } = this.state;
        const { list, isSubscribePop } = subscribeInfo || {};

        //微信渠道 且 非点击历史搜索 且 非授权登录Q后的搜索 且开关打开
        if (
            (anuEnv === "wx" || this.state.anuEnv === "ali") &&
            type !== "history" &&
            guideToSubscribe &&
            isSubscribePop &&
            !skipSubscribe
        ) {
            await this.trainSubscribeMsg(list, "search"); //返回了订阅的结果。业务线自行判定是否二次处理，一般来说是不需要的。
        }
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        var that = this,
            now = new Date().getTime(),
            data = that.state;

        // if (data.arrCity === data.depCity) {
        //     React.api.showModal({
        //         title: '小驼提示',
        //         content: '出发城市和到达城市不能相同哦\n',
        //         showCancel: false,
        //         confirmColor: '#00bcd4'
        //     });
        //     return;
        // }

        if (data.exchangeStatus) {
            return;
        }
        //防止多次点击，锁定500ms
        if (now - data.lastTap < 500) {
            return;
        }
        that.setState({
            lastTap: now,
        });

        watcher.sendWatcher("search_button_click_new");
        // 埋点
        qPoint("train/new_train_index/default/click/search_btn_new", {
            depCity: data.depCity,
            arrCity: data.arrCity,
            sdate: this.state.searchDate,
        });
        const startTime = this.props.query.startTime;

        let nowSearch = [{ city: data.depCity }, { city: data.arrCity }];
        if (
            !isEmptyObject(depCityObj) &&
            depCityObj.city === data.depCity &&
            !isEmptyObject(arrCityObj) &&
            arrCityObj.city === data.arrCity
        ) {
            nowSearch = [{ ...depCityObj }, { ...arrCityObj }];
        }
        let searchHistory = this.state.searchHistory;

        try {
            const sameIndex = searchHistory.findIndex(
                ([dep, arr]) =>
                    dep.city === data.depCity && arr.city === data.arrCity,
            );
            if (sameIndex !== -1) {
                searchHistory.splice(sameIndex, 1);
            }
        } catch (e) {}

        searchHistory.unshift(nowSearch);
        if (searchHistory.length > 20) {
            searchHistory.pop();
        }

        this.setState({ searchHistory });
        let searchHistoryObj = getStorageSync("searchHistoryNew") || {};
        searchHistoryObj[this.state.openId] = searchHistory;
        setStorageSync("searchHistoryNew", searchHistoryObj);

        if (this.state.isRebook) {
            const option = {
                url:
                    "/pages/functional_train/searchList/index?startStation=" +
                    this.state.depCity +
                    "&endStation=" +
                    this.state.arrCity +
                    "&date=" +
                    this.state.searchDate +
                    "&onlyGD=" +
                    this.state.isHightSpeedSelected +
                    (this.state.isRebook ? "&from=rebook" : "") +
                    "&orderNo=" +
                    this.props.query.orderNo +
                    "&token=" +
                    this.props.query.token +
                    "&phone=" +
                    this.props.query.phone +
                    "&passengers=" +
                    this.props.query.passengers +
                    "&originalPrice=" +
                    this.props.query.originalPrice +
                    "&hasStudentTicket=" +
                    this.props.query.hasStudentTicket +
                    "&startTime=" +
                    startTime,
            };
            if (isRedirect) {
                redirectTo(option);
            } else {
                navigateTo(option);
            }
        } else {
            await delay(250); // 性能优化
            const option = {
                url: `/pages/train/searchListV2/index?startStation=${this.state.depCity}&endStation=${this.state.arrCity}&date=${this.state.searchDate}&onlyGD=${this.state.isHightSpeedSelected}&type=${type}&isStudentSelected=${this.state.isStudentSelected}&depCityObj=${JSON.stringify(depCityObj)}&arrCityObj=${JSON.stringify(arrCityObj)}&trainListStyleIsB=${trainListStyleIsB}&isTrainRN2TaroB=${isTrainRN2TaroB}`,
            };
            if (isRedirect) {
                redirectTo(option);
            } else {
                navigateTo(option);
            }
        }
    };

    //提示黄条点击事件
    yellowTipClick = () => {
        if (this.state.yellowTip.tipDetailUrl) {
            this.gotoTipDetail();
        } else if (this.state.yellowTip.isShowCloseTipIcon) {
            this.closeYellowTip();
        }
    };
    gotoTipDetail = () => {
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        if (this.state.yellowTip.tipDetailUrl) {
            //跳转黄条详情
            if (
                this.state.yellowTip.tipDetailUrl.indexOf(
                    "/pages/platform/indexWx/index",
                ) == 0
            ) {
                reLaunch({
                    url: this.state.yellowTip.tipDetailUrl,
                });
            } else if (this.state.yellowTip.tipDetailUrl.indexOf("http") == 0) {
                navigateTo({
                    url:
                        "/pages/platform/webView/index?url=" +
                        encodeURIComponent(this.state.yellowTip.tipDetailUrl),
                });
            } else {
                navigateTo({
                    url: this.state.yellowTip.tipDetailUrl,
                });
            }
        }
    };
    gotoSpecialDetail = () => {
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        if (this.state.specialTip.tipDetailUrl) {
            if (
                this.state.specialTip.tipDetailUrl.indexOf(
                    "/pages/platform/indexWx/index",
                ) == 0
            ) {
                reLaunch({
                    url: this.state.specialTip.tipDetailUrl,
                });
            } else if (
                this.state.specialTip.tipDetailUrl.indexOf("http") == 0
            ) {
                navigateTo({
                    url:
                        "/pages/platform/webView/index?url=" +
                        encodeURIComponent(this.state.specialTip.tipDetailUrl),
                });
            } else {
                navigateTo({
                    url: this.state.specialTip.tipDetailUrl,
                });
            }
        }
    };

    closeYellowTip = () => {
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        this.setState({ isShowYellowTip: false });
    };

    selectHighSpeed = () => {
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        let isHightSpeedSelected = this.state.isHightSpeedSelected;
        qPoint("train/new_train_index/default/click/selectHighSpeed_new");
        this.setState({
            isHightSpeedSelected: !isHightSpeedSelected,
        });
    };

    selectStudent = () => {
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        watcher.sendWatcher("select_student_new");
        qPoint("train/new_train_index/default/click/selectStudent_new");
        let isStudentSelected = this.state.isStudentSelected;
        this.setState({
            isStudentSelected: !isStudentSelected,
        });
    };
    jumpToStudentTip = () => {
        const path = `/pages/functional_train/activityWebview/index?url=${encodeURIComponent("https://m.flight.qunar.com/shark/active/0ecabac10a4926c6ae54155f19d97320")}`;
        navigateTo({
            url: path,
        });
    };

    clearSearchHistory = () => {
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        watcher.sendWatcher("clear_search_history_new");
        const { cookies } = CommonUtils.getGlobalInfo();
        let openId = cookies.openId;
        let searchHistoryObj = getStorageSync("searchHistoryNew") || {};
        searchHistoryObj[openId] = [];
        setStorageSync("searchHistoryNew", searchHistoryObj);
        this.setState({ searchHistory: [] });
    };
    _rebookDateLimit = () => {
        if (this.state.isRebook) {
            const startTime = parseInt(this.props.query.startTime, 10);
            let nowDate = new Date();
            const difference =
                (startTime - nowDate.getTime()) / (1000 * 60 * 60);
            if (difference < 48) {
                let d = nowDate.getDate();
                let td = new Date(startTime).getDate();
                if (td > d) {
                    storageUtils.setStorageSync(CACHE_KEY.CALENDAR, {
                        pre12306: 2,
                    });
                } else if (td == d) {
                    storageUtils.setStorageSync(CACHE_KEY.CALENDAR, {
                        pre12306: 1,
                    });
                }
            }
        } else {
            removeStorageSync(CACHE_KEY.CALENDAR);
        }
    };

    //事件
    onChooseDate = () => {
        var that = this,
            now = new Date().getTime(),
            data = that.state;
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        //防止多次点击，锁定500ms
        if (now - data.lastTap < 500) {
            return;
        }
        let depCity = data.depCity;
        let arrCity = data.arrCity;

        watcher.sendWatcher("search_choose_date_clk_new");
        qPoint("train/new_train_index/default/click/select_date_new");
        that.setState({
            lastTap: now,
        });
        var params = {
            bizType: "train", // 业务线
            date: data.searchDate, // 默认单选日期；多选的第一个日期 （不传的话展示今天）
            eventType: EVENT_NAME.CALENDAR_DATE_CHANGE, // 选择日期成功之后触发的事件，通过监听这个事件来得到响应
            dataUrlHost: encodeURIComponent(TrainService.requestHost),
            url: encodeURIComponent(TrainService.requestService.Calendar),
            reqData: { bizType: 0 }, // 请求日历数据的 参数
        };
        if (this.state.isRebook) {
            const startTime = parseInt(this.props.query.startTime, 10);
            let nowDate = new Date();
            const difference =
                (startTime - nowDate.getTime()) / (1000 * 60 * 60);
            if (difference < 48) {
                let d = nowDate.getDate();
                let td = new Date(startTime).getDate();
                if (td > d) {
                    params.calendarDays = 2;
                    storageUtils.setStorageSync(CACHE_KEY.CALENDAR, {
                        pre12306: 2,
                    });
                } else if (td == d) {
                    params.calendarDays = 1;
                    storageUtils.setStorageSync(CACHE_KEY.CALENDAR, {
                        pre12306: 1,
                    });
                }
            }
            params.reqData = {
                bizType: 5,
                trainStartTime: new Date(startTime).format("yyyyMMddhhmm"),
                dep: depCity,
                arr: arrCity,
            };
        } else {
            removeStorageSync(CACHE_KEY.CALENDAR);
        }
        navigateTo({
            url:
                "/pages/alonePlatform/calendar/index?data=" +
                JSON.stringify(params),
        });
    };
    onChooseCity = (e) => {
        var that = this,
            now = Date.now(),
            data = that.state;
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        //防止多次点击，锁定500ms  切换中无视点击
        if (data.exchangeStatus || now - data.lastTap < 500) {
            return;
        }
        that.setState({
            lastTap: now,
        });
        var dateSet = e.currentTarget.dataset,
            type = dateSet.type,
            enable = dateSet.enable,
            isDep = type === "depCity" ? true : false;
        if (!enable) {
            return;
        }
        if (isDep) {
            watcher.sendWatcher("depcity_click_new");
            qPoint("train/new_train_index/default/click/select_dcity_new");
        } else {
            watcher.sendWatcher("arrcity_click_new");
            qPoint("train/new_train_index/default/click/select_acity_new");
        }

        var params = {
            type: 1, // 0:机票 1:火车票 2:汽车票 3:酒店
            citySuggestService: encodeURIComponent(
                TrainService.requestService.SUGGEST,
            ),
            citySuggestHost: encodeURIComponent(TrainService.requestHost),
            eventType: EVENT_NAME.CITY_CHANGE,
            placeholder: encodeURIComponent("城市、车站的中文或拼音"),
            isDep: isDep,
        };
        let url = "";
        if (that.state.trainCitySelectPage) {
            url =
                "/pages/auxiliary_train/citySelect/index?data=" +
                JSON.stringify(params);
        } else {
            url =
                "/pages/alonePlatform/citySelect/index?data=" +
                JSON.stringify(params);
        }
        navigateTo({ url });
    };

    onCityExchange = () => {
        var that = this,
            now = new Date().getTime(),
            data = that.state;
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });

        if (data.exchangeStatus || now - data.lastTap < 500) {
            return;
        }
        that.setState({
            lastTap: now,
        });

        if (data.exchangeStatus) {
            return;
        }
        that.setState({
            exchangeStatus: true,
        });
        EventEmitter.dispatch(EVENT_NAME.CITY_CHANGE, {
            ...data.arrCityObj,
            city: data.arrCity,
            isDep: true,
        });
        EventEmitter.dispatch(EVENT_NAME.CITY_CHANGE, {
            ...data.depCityObj,
            city: data.depCity,
            isDep: false,
        });
        setTimeout(function () {
            that.setState({
                exchangeStatus: false,
                arrCity: data.depCity,
                depCity: data.arrCity,
            });
        }, 0);

        watcher.sendWatcher("city_exchange_click_new");
        qPoint("train/new_train_index/default/click/exchange_city_new");
    };

    gotoMyRight = async ({ skipSubscribe = false }) => {
        const { anuEnv, guideToSubscribe, subscribeInfo = {} } = this.state;
        const { trainRightSubscribeList, isRightSubscribePop } =
            subscribeInfo || {};

        if (
            (anuEnv === "wx" || this.state.anuEnv === "ali") &&
            guideToSubscribe &&
            isRightSubscribePop &&
            !skipSubscribe
        ) {
            await this.trainSubscribeMsg(trainRightSubscribeList, "right"); //返回了订阅的结果。业务线自行判定是否二次处理，一般来说是不需要的。
        }
        Log({
            info: {
                area: "trainOther",
                act: "click",
                type: "homeEnter",
                name: "trainOther",
            },
        });
        watcher.sendWatcher("my_right_icon_new");
        qPoint("train/new_train_index/default/click/my_right");
        navigateTo({
            url: "/pages/train/TrainMyRight/index",
        });
    };
    gotoPolicySearch = () => {
        let { depCity, arrCity } = this.state;
        var url = `https://m.suanya.com/webapp/train/activity/ztrip-isolation-policy-query?from=${encodeURIComponent(depCity)}&to=${encodeURIComponent(arrCity)}&source=qunar`;
        navigateTo({
            url: "/pages/platform/webView/index?url=" + encodeURIComponent(url),
        });
    };

    gotoReleaseTip = () => {
        navigateTo({
            url: "/pages/cms/entry/train/index?cid=3fe04585422e9e7ce4bd8327b23dc377",
        });
    };

    gotoTrainCoupon = () => {
        navigateTo({
            url: "/pages/cms/entry/train/index?activityCode=student_right_bargain_train_f&packageCode=student_right_bargain_train_f&processCode=commonCreateBargainProcess&activityType=bargain_common&cid=21088&sourceRecordNo=80504680423955880&bargainId=308903064",
        });
    };

    gotoBusPage = () => {
        Log({
            info: {
                area: "trainOther",
                act: "click",
                type: "homeEnter",
                name: "trainOther",
            },
        });
        watcher.sendWatcher("bus_icon_new");
        qPoint("train/new_train_index/default/click/bus");
        navigateTo({
            url: "/pages/bus/index/index?bizType=bus",
        });
    };

    judgeBindWeChat = async () => {
        try {
            return await Api.queryFollow();
        } catch (error) {
            // 不知道关注没关注 按未关注处理
            watcher.sendWatcher(
                `${this.props.source}ShowFollowPublicAccount_new`,
            );
            return false;
        }
    };
    handleSubMessage = () => {
        Log({
            info: {
                area: "trainOther",
                act: "click",
                type: "homeEnter",
                name: "trainOther",
            },
        });
        watcher.sendWatcher("handleSubMessage_new");
        qPoint("train/new_train_index/default/click/handleSubMessage_new");
        let listRes = getStorageSync(scene);
        if (listRes) {
            subscribe.requestSubscribeMessage(
                listRes,
                () => {
                    this.setState({
                        isSubscribe: true,
                    });
                    showToast({
                        title: "订阅成功~",
                        duration: 1000,
                        icon: "none",
                    });
                    watcher.sendWatcher("buyTicketSubMessageSuccess_new");
                },
                () => {
                    showToast({
                        title: "订阅失败~",
                        duration: 1000,
                        icon: "none",
                    });
                    watcher.sendWatcher("shareOrderSubMessageFail_new");
                },
            );
        } else {
            showToast({ title: "订阅失败~", duration: 1000, icon: "none" });
        }
    };

    getSearchListParams = () => {
        let {
            depCity,
            arrCity,
            searchDate,
            depCityObj,
            arrCityObj,
            trainListInterfaceIsB,
        } = this.state;
        let source =
            process.env.ANU_ENV === "wx"
                ? "wx_app_search"
                : process.env.ANU_ENV === "ali"
                  ? "ali_app_search"
                  : "bd_app_search";
        let reqParam = {
            departure: depCity,
            arrival: arrCity,
            date: searchDate,
            searchType: "NORMAL",
            trafficTabControlParam: {
                supportTabCombinationList: ["WECHAT_TRAIN_JOINT"],
                selectedTab: null,
            },
            requestBizHead: {
                cat: source,
            },
        };
        let service = guardService.searchList;

        if (trainListInterfaceIsB) {
            service = guardService.getTrainSearchCore;
            const extParams = {
                trainDirectParam: {
                    requirementList: ["rob_success_rate"],
                },
                trainJointParam: {
                    requirementList: ["rob_success_rate"],
                },
                arrivalType: arrCityObj?.stationType || "",
                departureType: depCityObj?.stationType || "",
                arrivalAreaId: arrCityObj?.areaId,
                departureAreaId: depCityObj?.areaId || "",
            };
            if (this.state.isStudentSelected) {
                extParams.trainDirectParam.requirementList.push(
                    "student_ticket",
                );
            }
            Object.assign(reqParam, extParams);
        } else {
            service = guardService.searchList;

            let trafficRecommendobj = {};
            let supportTypeList = [];
            if (process.env.ANU_ENV === "bu") {
                supportTypeList = ["FLIGHT_DIRECT", "TRAIN_JOINT"];
            }
            if (process.env.ANU_ENV === "bu" || process.env.ANU_ENV === "ali") {
                trafficRecommendobj.supportTypeList = supportTypeList;
            }
            let presetParam = {
                trainJointExposureParam: {},
                trafficRecommendCardParam: trafficRecommendobj,
                trainJointParam: {},
                trainDirectParam: {
                    requirementList: ["rob_success_rate"],
                },
            };
            if (this.state.isStudentSelected) {
                presetParam.trainDirectParam.requirementList.push(
                    "student_ticket",
                );
            }
            // 直达
            reqParam.mustParamSet = ["trainDirectParam"];
            reqParam.requestLaterCount = 0;
            reqParam.trainDirectParam = presetParam.trainDirectParam;
            reqParam.trainJointExposureParam =
                presetParam.trainJointExposureParam;
            reqParam.trafficRecommendCardParam =
                presetParam.trafficRecommendCardParam;
        }
        TrainEntools.addEncodeParam(reqParam);
        return { reqParam, service };
    };

    // 预请求车次列表数据
    preRequestSearchListData = () => {
        const params = this.getSearchListParams();
        Api.requestSearchListFromIndex({ ...params });
    };

    afterLogin = (item, isRedirect = false) => {
        const {
            guideToSubscribe,
            isNotWxCoupon,
            subscribeInfo = {},
        } = this.state;
        if (isNotWxCoupon === true) {
            const { isSubscribePop } = subscribeInfo || {};
            if (!guideToSubscribe || !isSubscribePop) {
                if (item && typeof item === "string") {
                    // todo: 判断是从搜索历史点击的  这块逻辑没有走，所以没有做处理
                    this.onSearchByHistory(item);
                } else {
                    this.onSearch({
                        type: "",
                        fromAuthLogin: true,
                        isRedirect,
                    });
                }
            }
        }
    };
    getPhone = async (item, e) => {
        const { aliSkipLogin = false, anuEnv = ''} = this.state;
        var event = e || item;
        event = Object.assign({}, event);
        if (event.detail) {
            Object.assign(event, event.detail)
        }

        if (anuEnv === "ali") {
            this.afterLogin(event);
            // 支付宝小程序不进行授权登录
            if (!aliSkipLogin) {
                setTimeout(() => {
                    handlePhoneLoginAuth(
                        event,
                        fail.bind(this, event),
                        success.bind(this, event),
                    );
                }, 1500);
            }
            
        } else {
            //微信渠道 且 非点击历史搜索 且 非授权登录Q后的搜索 且开关打开
            handlePhoneLoginAuth(
                event,
                fail.bind(this, event),
                success.bind(this, event),
            );
        }

        function fail(item) {
            if (anuEnv === "wx") {
                this.afterLogin(item);
            }
        }

        function success(item) {
            if (anuEnv === "wx") {
                this.afterLogin(item);
            } else {
                this.afterLogin(item, true);
            }
            this.loginSuccess();
            this.dealWithReducePrice("loginSuccess");
        }
    };

    loginSuccess = () => {
        if (this.state.isNotWxCoupon === true) {
            this.refuseLogin();
            return;
        }
        this.refuseLogin();
        this.wxBindCoupon();
    };
    // 已登录，不需要授权
    refuseLogin = () => {
        this.setState({
            isLoginedQunar: true,
        });
    };

    /**
     * 展示 新客权益模态框
     */
    handleShowNewUserModal = () => {
        this.setState({
            isShowNewUserModal: true,
            trainNewUserModalOpenOrCloseStatus: true,
        });
    };
    /**
     * 关闭 新客权益模态框
     */
    handleCloseNewUserModal = () => {
        this.setState({
            trainNewUserModalOpenOrCloseStatus: false,
        });
        setTimeout(() => {
            this.setState({
                isShowNewUserModal: false,
            });
        }, 400);
    };
    /**
     * 发放权益
     */
    requestSendWelfare = async () => {
        const { userName = "" } = this.props.query;

        const { bdSourceCode } = this.state;

        TrainRequest.request({
            service:
                TrainService.requestService.SEND_TRAIN_INDEX_NEW_USER_EQUITY,
            param: {
                loginMark: true,
                bdSourceCode: bdSourceCode,
                userName: userName,
            },
            success: function (response) {
                let data = response.data || {};
                const _status = data?.status;
                if (_status == 0) {
                    showToast({
                        title: "领取成功，可在“我的权益”查看",
                        duration: 3000,
                        icon: "none",
                    });
                } else if (_status == 3) {
                    showToast({
                        title: "你已领取过了~",
                        duration: 3000,
                        icon: "none",
                    });
                } else if (_status == 4) {
                    showToast({
                        title: "不可以帮自己助力哦~",
                        duration: 3000,
                        icon: "none",
                    });
                } else {
                    showToast({
                        title: "网络异常，请稍后再试",
                        duration: 3000,
                        icon: "none",
                    });
                }
                watcher.sendWatcher(
                    `FrontGuideSendSpeedPackageSuccess_${_status}_new`,
                );
            },
            fail: function () {
                watcher.sendWatcher("FrontGuideSendSpeedPackageFail_new");
                showToast({
                    title: "网络异常，请稍后再试",
                    duration: 3000,
                    icon: "none",
                });
            },
            complete: () => {
                this.handleCloseNewUserModal();
            },
        });
    };

    loginToStudentVerify = (e) => {
        handlePhoneLoginAuth(
            e,
            () => {},
            this.judgeJumpToStudentVerify.bind(this),
        );
    };

    loginSuccessCallback = (
        { key, afterLoginNeedJump = false, jumpUrl = "" },
        e,
    ) => {
        const that = this;
        let callback = () => {};
        if (key === "myRight") {
            callback = () => {
                that.refuseLogin();
                setTimeout(() => {
                    that.gotoMyRight({});
                }, 0);
            };
        } else if (key === "studentVerify") {
            callback = () => {
                that.refuseLogin();
                setTimeout(() => {
                    that.judgeJumpToStudentVerify();
                }, 0);
            };
        } else if (afterLoginNeedJump && jumpUrl) {
            callback = () => {
                that.refuseLogin();
                setTimeout(() => {
                    navigateTo({
                        url: jumpUrl,
                    });
                }, 0);
            };
        }

        handlePhoneLoginAuth(e, () => {}, callback);
    };

    judgeJumpToStudentVerify = async () => {
        const userInfo12306 = getStorageSync("userInfo12306");
        this.setState({ jumpToStudentVerify: true });
        if (userInfo12306 && userInfo12306.username) {
            this.jumpToStudentVerify();
        } else {
            const path =
                "/pages/functional_train/login12306/index?studentVerify=true";
            navigateTo({
                url: path,
            });
        }
    };

    jumpToStudentVerify = () => {
        const userInfo12306 = getStorageSync("userInfo12306") || {};
        const { memberStatus, username = "" } = userInfo12306;
        const { cookies } = CommonUtils.getGlobalInfo();
        const qName = cookies._q ? cookies._q.slice(2) : "";
        // 跳转刷脸
        if (memberStatus !== "0") {
            this.jumpToFacePage(userInfo12306);
            return;
        }
        const param = {
            username: qName,
            account12306Name: username,
            urlPath: "/train/student/verification",
        };
        TrainRequest.request({
            service: guardService.getRailWayResult,
            host: requestHost,
            method: "POST",
            data: param,
            success(res) {
                let { responseBizHead = {}, jumpUrl } = res.data || {};
                const { code } = responseBizHead;
                if (code === 0 && jumpUrl) {
                    // 在跳转链接中插入编码后的&hideNavBar=YES，隐藏页面navbar
                    const insertIndex = jumpUrl.indexOf("&errorurl=");
                    const hideNaveBarStr = "%26hideNavBar%3DYES";
                    if (insertIndex >= 0) {
                        jumpUrl =
                            jumpUrl.slice(0, insertIndex) +
                            hideNaveBarStr +
                            jumpUrl.slice(insertIndex);
                    }
                    // platFormWebView 与 alonePlatFormWebView打开白屏
                    navigateTo({
                        url:
                            "/pages/train/webview/index?url=" +
                            encodeURIComponent(jumpUrl),
                    });
                }
            },
        });
    };

    addWatcher = (item) => {
        const { key = "", markKey = "" } = item;
        switch (key) {
            case "wxSubscribe": // 微信订阅
                watcher.sendWatcher("wxSubscribe_new");
                qPoint("train/new_train_index/default/click/wxSubscribe_new");
                break;
            case "bus": // 汽车票
                Log({
                    info: {
                        area: "trainOther",
                        act: "click",
                        type: "homeEnter",
                        name: "trainOther",
                    },
                });
                watcher.sendWatcher("bus_icon_new");
                qPoint("train/new_train_index/default/click/bus");
                break;
            case "gotoGrabVote": // 预约抢票
                Log({
                    info: {
                        area: "trainOther",
                        act: "click",
                        type: "homeEnter",
                        name: "trainOther",
                    },
                });
                watcher.sendWatcher("grab_votes_icon_new");
                qPoint(
                    "train/new_train_index/default/click/grab_order_fill_new",
                );
                // 预加载qconfig
                Api.getHotConfigPromise(1);
                break;
            case "freeTake": // 0元免费拿
                Log({
                    info: {
                        area: "trainOther",
                        act: "click",
                        type: "homeEnter",
                        name: "trainOther",
                    },
                });
                watcher.sendWatcher("freeTake_new");
                qPoint("train/new_train_index/default/click/freeTake_new");
                break;
            default:
                if (markKey) {
                    qPoint(`train/new_train_index/default/click/${markKey}`);
                }
                break;
        }
    };

    jumpTo = (item) => {
        const { jumpUrl = "", key = "" } = item;
        this.addWatcher(item);
        // 有些有特殊逻辑的单独处理
        if (key === "studentVerify") {
            // 学生核验
            this.judgeJumpToStudentVerify();
            return;
        }

        if (key === "myRight") {
            // 我的权益
            this.gotoMyRight({});
            return;
        }
        if (key === "covidPolicy") {
            // 隔离政策
            this.gotoPolicySearch();
            return;
        }
        // 这是直接跳转的 汽车票、预约抢票、放票提醒、15元火车券、微信订阅
        if (jumpUrl.includes("http")) {
            navigateTo({
                url:
                    "/pages/qunar/subPages/alonePlatform/webView/index?url=" +
                    encodeURIComponent(jumpUrl),
            });
        } else {
            navigateTo({
                url: jumpUrl,
            });
        }
    };

    // 点击banner跳转
    jumpToBanner = (banner) => {
        const { touchBannerJumpUrl } = banner;
        watcher.sendWatcher("train_banner_item");
        qPoint("train/new_train_banner/default/click/train_banner_item", {
            jumpUrl: touchBannerJumpUrl,
        });
        if (touchBannerJumpUrl.includes("http")) {
            navigateTo({
                url:
                    "/pages/qunar/subPages/alonePlatform/webView/index?url=" +
                    encodeURIComponent(touchBannerJumpUrl),
            });
        } else if (touchBannerJumpUrl) {
            navigateTo({
                url: touchBannerJumpUrl,
            });
        }
    };

    onLogChangeBanner = (e) => {
        const { trainBannerList = [] } = this.state;
        if (
            e &&
            this.trainBannerListShow &&
            !this.trainBannerListShow[e.current]
        ) {
            const currentBanner = trainBannerList[e.current];
            if (currentBanner) {
                this.trainBannerListShow[e.current] = true;
                watcher.sendWatcher("train_banner_item_count");
                qPoint(
                    "train/new_train_banner/default/click/train_banner_item_count",
                    {
                        banner: currentBanner,
                    },
                );
            }
        }
    };

    onSearchTap = () => {
        if (this.state.anuEnv === "ali") {
            this.getPhone();
        }
    };

    jumpToFacePage = (userInfo12306) => {
        let { username = "", pwd = "" } = userInfo12306;
        pwd = pwd && Base64.decode(pwd);
        let typeError = "accountIllegal94";
        let sceneType = "3";
        let type = "isNeedFaceIdentified";
        let callbackUrl = "/pages/platform/indexWx/index";
        let url = `/pages/train/selectRiskControl/index?type=${type}&username=${username}&typeError=${typeError}&sceneType=${sceneType}&password=${Base64.decode(pwd)}&callbackUrl=${encodeURIComponent(callbackUrl)}`;
        navigateTo({ url });
    };

    resourcePositionClick = () => {
        const { sourcePositionData = {} } = this.state;
        const { frontCouponUrl, posCode, positionType } =
            sourcePositionData || {};
        qPoint(`train/new_train_index/resourcePosition/click/${posCode}`, {
            positionType: positionType,
        });
        //代金券类型的资源位提示
        if (frontCouponUrl && frontCouponUrl.length > 0) {
            this.searchCardResourcePosition("wxBizPopup");
        } else {
            if (sourcePositionData.jumpUrl) {
                navigateTo({
                    url: sourcePositionData.jumpUrl,
                });
            }
        }
    };

    render() {
        return (
            <View>
                {this.state.isShowYellowTip &&
                    this.state.yellowTip.tipContent && (
                        <View
                            className={`yellow_tip_wraper ${this.props.indexVersionGroup === "A" ? "yellow_tip_wraper-new" : ""}`}
                        >
                            <View
                                className="yellow_tip_content"
                                onClick={this.yellowTipClick.bind(this)}
                            >
                                <View className="g-q-iconfont-train tip-notice-icon">
                                    &#xe4ee;
                                </View>
                                <View className="scorll-wrapper">
                                    <View
                                        class="scorll-container"
                                        style={{ height: "32rpx" }}
                                    >
                                        <View
                                            class="move-content"
                                            style={this.state.moveStyle}
                                        >
                                            <Text class="move-box">
                                                {
                                                    this.state.yellowTip
                                                        .tipContent
                                                }
                                            </Text>
                                        </View>
                                        <View
                                            class="move-content duplication"
                                            style={this.state.moveDupStyle}
                                        >
                                            <Text class="move-box">
                                                {
                                                    this.state.yellowTip
                                                        .tipContent
                                                }
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                                {this.state.yellowTip.tipDetailUrl ? (
                                    <View className="g-q-iconfont-train tip-detail-icon">
                                        &#xe4c5;
                                    </View>
                                ) : this.state.yellowTip.isShowCloseTipIcon ? (
                                    <View className="g-q-iconfont-train tip-close-icon">
                                        &#xe49a;
                                    </View>
                                ) : (
                                    <View></View>
                                )}
                            </View>
                        </View>
                    )}

                <View
                    className={`search_container_inner_search_container ${this.state.skipEnv !== "WX_DEAL" && this.state.trainBannerList.length > 0 ? "search_container_inner_banner" : "search_container_inner_no_banner"} ${this.props.indexVersionGroup === "A" ? "search_container_inner_new" : ""}`}
                    style={{
                        paddingTop: this.state.anuEnv === "ali" ? "48rpx" : "",
                    }}
                >
                    <View className="citySelector">
                        <View
                            className="cityTap"
                            onClick={this.onChooseCity.bind(this)}
                            data-type="depCity"
                            data-enable={this.state.isRebook ? false : true}
                        >
                            <View
                                className={
                                    "depCityContent " +
                                    (this.state.exchangeStatus
                                        ? "depCity-changing"
                                        : "")
                                }
                            >
                                {this.state.depCity}
                            </View>
                        </View>
                        <View
                            className={
                                "city_change " +
                                (this.state.exchangeStatus
                                    ? "btn-rotating"
                                    : "")
                            }
                            onClick={this.onCityExchange.bind(this)}
                        ></View>

                        <View
                            className="cityTap arrCityTap"
                            onClick={this.onChooseCity.bind(this)}
                            data-type="arrCity"
                            data-enable={
                                this.state.isRebook &&
                                !this.state.enableChangeArrCity
                                    ? false
                                    : true
                            }
                        >
                            <View
                                className={
                                    "arrCityContent " +
                                    (this.state.exchangeStatus
                                        ? "arrCity-changing"
                                        : "") +
                                    (this.state.isRebook &&
                                    !this.state.enableChangeArrCity
                                        ? " disable"
                                        : "")
                                }
                            >
                                {this.state.arrCity}
                            </View>
                        </View>
                    </View>
                    <View
                        className="dateSelector"
                        onClick={this.onChooseDate.bind(this)}
                    >
                        <Text>
                            <Text className="number">
                                {this.state.displayMonth}
                            </Text>
                            <Text className="date-text">月</Text>
                            <Text className="number">
                                {this.state.displayDay}
                            </Text>
                            <Text className="date-text">日</Text>
                        </Text>
                        <Text className="date-week">{this.state.dateWeek}</Text>
                        {this.state.holidayStr && (
                            <Text className="date-festival">
                                {this.state.holidayStr}
                            </Text>
                        )}
                    </View>
                    {!this.state.isRebook && (
                        <View className="switch-content">
                            <View
                                className="switch-item-wrap"
                                onClick={this.selectHighSpeed.bind(this)}
                            >
                                <View className="switch-item-txt">
                                    高铁动车
                                </View>

                                {this.props.indexVersionGroup === "A" ? (
                                    this.state.isHightSpeedSelected ? (
                                        <View className="selectedIcon-new switchIcon-new g-q-iconfont-train">
                                            &#xea0b;
                                        </View>
                                    ) : (
                                        <View className="unselectedIcon-new switchIcon-new g-q-iconfont-train">
                                            &#xea0c;
                                        </View>
                                    )
                                ) : this.state.isHightSpeedSelected ? (
                                    <View className="selectedIcon switchIcon"></View>
                                ) : (
                                    <View className="unselectedIcon switchIcon"></View>
                                )}
                            </View>
                            <View className="switch-item-wrap">
                                {this.state.anuEnv !== "ali" &&
                                    this.state.isStudentSelected && (
                                        <View
                                            className="item-txt-tip"
                                            onClick={this.jumpToStudentTip.bind(
                                                this,
                                            )}
                                        >
                                            学生票须知
                                        </View>
                                    )}

                                <View
                                    onClick={this.selectStudent.bind(this)}
                                    className="switch-item-wrap"
                                >
                                    <View className="switch-item-txt">
                                        学生票
                                    </View>
                                    {this.state.isStudentSelected ? (
                                        <View className="switchIcon selectedIcon"></View>
                                    ) : (
                                        <View className="switchIcon unselectedIcon"></View>
                                    )}
                                </View>
                            </View>
                        </View>
                    )}

                    {this.props.indexVersionGroup === "A" &&
                        this.state.sourcePositionData &&
                        this.state.sourcePositionData.posCode ===
                            "wxBizBottom" &&
                        this.state.sourcePositionData.isShow &&
                        this.state.sourcePositionData.imageUrl && (
                            <View
                                className="resource-position-content"
                                onClick={this.resourcePositionClick}
                            >
                                <Image
                                    className="resource-position-image"
                                    src={this.state.sourcePositionData.imageUrl}
                                />

                                <View className="resource-position-text">
                                    {this.state.sourcePositionData.copyWritings.map(
                                        (item, index) => {
                                            return (
                                                <View
                                                    className={`item-text ${item.isNum ? "num" : ""}`}
                                                >
                                                    {item.text}
                                                </View>
                                            );
                                        },
                                    )}
                                </View>
                                <View className="g-q-iconfont-train resource-position-right-icon">
                                    &#xe4c5;
                                </View>
                            </View>
                        )}

                    {
                        // 未登录q && 不是马甲小程序
                        !this.state.isLoginedQunar &&
                        this.state.skipEnv !== "WX_DEAL" ? (
                            <Button
                                className={`rewrite-btn search-btn search-button ${(this.state.anuEnv === "wx") & !this.state.isRebook && this.state.reducePriceBubble.length > 0 ? "has-discount" : ""}`}
                                open-type={
                                    this.state.anuEnv === "ali"
                                        ? "getAuthorize"
                                        : "getPhoneNumber"
                                }
                                scope={
                                    this.state.anuEnv === "ali"
                                        ? "phoneNumber"
                                        : ""
                                }
                                // onTap={this.onSearchTap.bind(this)}
                                onGetphonenumber={this.getPhone.bind(this)}
                                bindgetphonenumber={this.getPhone.bind(this)}
                                onGetAuthorize={this.getPhone.bind(this)}
                                onTouchStart={this.preRequestSearchListData}
                                style={{
                                    background:
                                        (this.state.btnConfig &&
                                            this.state.btnConfig.isOpen ==
                                                "true" &&
                                            this.state.btnConfig.searchBtn &&
                                            this.state.btnConfig.searchBtn
                                                .background) ||
                                        "linear-gradient(270deg, #FC2B4E 0%, #FFB25A 100%);",
                                    backgroundSize:
                                        (this.state.btnConfig &&
                                            this.state.btnConfig.isOpen ==
                                                "true" &&
                                            this.state.btnConfig.searchBtn &&
                                            this.state.btnConfig.searchBtn
                                                .backgroundSize) ||
                                        "",
                                    boxShadow:
                                        (this.state.btnConfig &&
                                            this.state.btnConfig.isOpen ==
                                                "true" &&
                                            this.state.btnConfig.searchBtn &&
                                            this.state.btnConfig.searchBtn
                                                .boxShadow) ||
                                        "",
                                }}
                            >
                                {this.props.indexVersionGroup === "A"
                                    ? "火车查询"
                                    : "火车票查询"}
                                {this.state.anuEnv === "wx" &&
                                    !this.state.isRebook &&
                                    this.state.reducePriceBubble.length > 0 && (
                                        <Text className="discounts">
                                            {this.state.reducePriceBubble}
                                        </Text>
                                    )}
                            </Button>
                        ) : (
                            <View
                                className={`search-btn search-button ${
                                    (this.state.anuEnv === "wx" ||
                                        this.state.anuEnv === "ali") &&
                                    !this.state.isRebook &&
                                    this.state.reducePriceBubble.length > 0
                                        ? "has-discount"
                                        : ""
                                }`}
                                onTouchStart={this.preRequestSearchListData}
                                onClick={this.onSearch}
                                style={{
                                    background:
                                        (this.state.btnConfig &&
                                            this.state.btnConfig.isOpen ==
                                                "true" &&
                                            this.state.btnConfig.searchBtn &&
                                            this.state.btnConfig.searchBtn
                                                .background) ||
                                        "linear-gradient(270deg, #FC2B4E 0%, #FFB25A 100%);",
                                    backgroundSize:
                                        (this.state.btnConfig &&
                                            this.state.btnConfig.isOpen ==
                                                "true" &&
                                            this.state.btnConfig.searchBtn &&
                                            this.state.btnConfig.searchBtn
                                                .backgroundSize) ||
                                        "",
                                    boxShadow:
                                        (this.state.btnConfig &&
                                            this.state.btnConfig.isOpen ==
                                                "true" &&
                                            this.state.btnConfig.searchBtn &&
                                            this.state.btnConfig.searchBtn
                                                .boxShadow) ||
                                        "",
                                    color: '#FFFFFF'
                                }}
                            >
                                {this.props.indexVersionGroup === "A"
                                    ? "火车查询"
                                    : "火车票查询"}
                                {this.state.anuEnv === "wx" &&
                                    !this.state.isRebook &&
                                    this.state.reducePriceBubble.length > 0 && (
                                        <Text className="discounts">
                                            {this.state.reducePriceBubble}
                                        </Text>
                                    )}
                            </View>
                        )
                    }

                    <View className="search-btn-bottom-shadow"></View>
                    {!this.state.isRebook &&
                        this.state.searchHistory.length > 0 && (
                            <ScrollView
                                className="searchHistory-wrap"
                                scroll-x="true"
                                enable-flex="true"
                            >
                                {this.state.searchHistory.map((item) => {
                                    return item.length === 2 &&
                                        item[0].city &&
                                        item[1].city ? (
                                        <View
                                            className="history-item"
                                            onClick={this.onSearchByHistory.bind(
                                                this,
                                                item[0],
                                                item[1],
                                            )}
                                        >
                                            {`${item[0].city}-${item[1].city}`}
                                        </View>
                                    ) : null;
                                })}

                                <View
                                    className="delete-history-btn"
                                    onClick={this.clearSearchHistory}
                                >
                                    <Text className="g-q-iconfont-train delete-icon">
                                        &#xe2a2;
                                    </Text>
                                    <Text className="delete-text">清空</Text>
                                </View>
                            </ScrollView>
                        )}

                    <View class="dashed-line"></View>
                </View>

                {this.props.indexVersionGroup !== "A" &&
                this.state.skipEnv !== "WX_DEAL" &&
                this.state.trainBannerList.length > 0 ? (
                    <View class="train-banners">
                        <Swiper
                            class="swiper-train-banners"
                            autoplay={true}
                            circular={true}
                            interval={6000}
                            duration={500}
                            onChange={this.onLogChangeBanner.bind(this)}
                            // vertical={true}
                        >
                            {this.state.trainBannerList.map((banner, index) => (
                                <SwiperItem
                                    class="train-banner-item"
                                    key={index}
                                >
                                    <View
                                        class="train-banner-container"
                                        onClick={() =>
                                            this.jumpToBanner(banner)
                                        }
                                    >
                                        <Image
                                            class="train-banner-icon"
                                            src={banner.bannerIconUrl}
                                            onClick={this.closeBigImg}
                                        />

                                        <View class="train-banner-content">
                                            <View
                                                class="train-banner-title"
                                                style={{
                                                    color:
                                                        banner.bannerTitleColor ||
                                                        "#222222",
                                                }}
                                            >
                                                {banner.bannerTitle}
                                            </View>
                                            {banner.indexDesc ? (
                                                <View class="train-banner-desc">
                                                    {banner.indexDesc}
                                                </View>
                                            ) : null}
                                        </View>
                                        <Text class="g-q-iconfont-train arrow">
                                            &#xf07f;
                                        </Text>
                                    </View>
                                </SwiperItem>
                            ))}
                        </Swiper>
                    </View>
                ) : null}

                {this.state.skipEnv !== "WX_DEAL" && (
                    <View
                        className={`train-rights-search-container ${this.props.indexVersionGroup === "A" ? "train-rights-new-search-container" : ""}`}
                    >
                        {this.state.trainRightsList.length > 0
                            ? this.state.trainRightsList.map((item) => {
                                  if (
                                      item.isNeedLoginQunar &&
                                      !this.state.isLoginedQunar
                                  ) {
                                      return (
                                          <View
                                              className="train-right train-right-not-login"
                                              key={item.title + item.key}
                                          >
                                              <Image
                                                  src={
                                                      this.props
                                                          .indexVersionGroup ===
                                                      "A"
                                                          ? item.newImgUrl
                                                          : item.imgUrl
                                                  }
                                                  className="train-right-icon-search-container"
                                              ></Image>
                                              <View className="train-right-text">
                                                  {item.title}
                                              </View>
                                              <Button
                                                  open-type="getPhoneNumber"
                                                  className="train-right-button rewrite-btn"
                                                  onGetphonenumber={this.loginSuccessCallback.bind(
                                                      this,
                                                      item,
                                                  )}
                                              ></Button>
                                          </View>
                                      );
                                  } else if (
                                      item.isNeedSubscribe &&
                                      !this.state.isSubscribe
                                  ) {
                                      return (
                                          <View
                                              className="train-right"
                                              onClick={() => {
                                                  this.handleSubMessage();
                                              }}
                                              key={item.title + item.key}
                                          >
                                              <Image
                                                  src={
                                                      this.props
                                                          .indexVersionGroup ===
                                                      "A"
                                                          ? item.newImgUrl
                                                          : item.imgUrl
                                                  }
                                                  className="train-right-icon-search-container"
                                              ></Image>
                                              <View className="train-right-text">
                                                  {item.title}
                                              </View>
                                          </View>
                                      );
                                  } else {
                                      return (
                                          <View
                                              className="train-right"
                                              onClick={() => this.jumpTo(item)}
                                              key={item.title + item.key}
                                          >
                                              <Image
                                                  src={
                                                      this.props
                                                          .indexVersionGroup ===
                                                      "A"
                                                          ? item.newImgUrl
                                                          : item.imgUrl
                                                  }
                                                  className="train-right-icon-search-container"
                                              ></Image>
                                              <View className="train-right-text">
                                                  {item.title}
                                              </View>
                                          </View>
                                      );
                                  }
                              })
                            : null}
                    </View>
                )}

                {this.props.indexVersionGroup === "A" &&
                this.state.skipEnv !== "WX_DEAL" &&
                this.state.trainBannerList.length > 0 ? (
                    <View class="train-banners train-banners-new">
                        <Swiper
                            class="swiper-train-banners"
                            autoplay={true}
                            circular={true}
                            interval={6000}
                            duration={500}
                            onChange={this.onLogChangeBanner.bind(this)}
                            // vertical={true}
                        >
                            {this.state.trainBannerList.map((banner, index) => (
                                <SwiperItem
                                    class="train-banner-item"
                                    key={index}
                                >
                                    <View
                                        class="train-banner-container"
                                        onClick={() =>
                                            this.jumpToBanner(banner)
                                        }
                                    >
                                        <Image
                                            class="train-banner-icon"
                                            src={banner.bannerIconUrl}
                                            onClick={this.closeBigImg}
                                        />

                                        <View class="train-banner-content">
                                            <View
                                                class="train-banner-title"
                                                style={{
                                                    color:
                                                        banner.bannerTitleColor ||
                                                        "#222222",
                                                }}
                                            >
                                                {banner.bannerTitle}
                                            </View>
                                            {banner.indexDesc ? (
                                                <View class="train-banner-desc">
                                                    {banner.indexDesc}
                                                </View>
                                            ) : null}
                                        </View>
                                        <Text class="g-q-iconfont-train arrow">
                                            &#xf07f;
                                        </Text>
                                    </View>
                                </SwiperItem>
                            ))}
                        </Swiper>
                    </View>
                ) : null}
            </View>
        );
    }
}

export default TrainSearchBoxContainer;
