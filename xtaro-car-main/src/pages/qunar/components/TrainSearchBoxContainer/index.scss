@font-face {
  font-family: 'g-q-iconfont';
  src: url('https://s.qunarzz.com/nanachi/score/font/0.0.100/minprogram_nanachi.ttf');
}

.g-q-iconfont-train {
  font-family: 'g-q-iconfont';
  font-family: 'g-q-iconfont' !important;
  font-size: 12px;
  font-style: normal;
}
.train-search-container {
  background-color: #F5F6F9;
}
.search-container {
  // padding-top: 4rpx;
  // padding-bottom: 2rpx;
  margin: 0 8px;
  border-radius: 10px;
  position: relative;
  // top: -76rpx;
  z-index: 50;
  background-image: #fff;
  .tabItemList{
    display: flex;
    width: 100%;
    .tabItem{
      font-family: PingFangSC-Light;
      font-size: 16px;
      color: #999999;
      letter-spacing: 0;
      display: inline-block;
      text-align: center;
      flex: 0.5;
      color: #616161;
      &.on{
        font-family: PingFangSC-Semibold;
        color: #222222;
        font-weight: 600;
        .tabItem-box {
          position: relative;
        }

        .tabItem-world {
          position: relative;
          z-index: 2;
        }

        .bottom-line{
          position: absolute;
          bottom: 1px;
          z-index: 1;
          width: 28px;
          height: 6px;
          border-radius: 6px;
          background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
    .line{
      width: 1px;
      height: 16px;
      background: #E6E6E6;
      margin: 5.5px 0;
    }
  }
}

.search_container_inner_search_container{
    // background: #ffffff;
    background-image: linear-gradient(180deg, #F5F6F9 0%, #FFFFFF 50%);
    border-radius: 16px;
    padding: 0 16px 0
    
    &-banner{
      border-radius: 0;
    }
    &_no_banner {
      border-radius: 16px;
    }

    .dateSelector {
      display: flex;
      align-items: center;
      position: relative;
      height: 54px;
      line-height: 54px;
      letter-spacing: 0;
      border-bottom: 0.5px solid #F2F2F2;
      font-family: PingFangSC-Semibold;
      font-size: 22px;
      color: #222222;
      padding: 12px 0px;
      box-sizing: border-box;
      .number {
        font-family: 'hotel_rn_num';
        font-size: 24px;
        color: #222222;
        letter-spacing: 0;
        font-weight: 700;
        position: relative;
        top: 1.5px;
        // vertical-align: bottom;
        // margin-top: 2rpx;
      }
      .date-text {
        font-family: PingFangSC-Semibold;
        font-size: 22px;
        color: #222222;
        font-weight: 700;
      }
      .dateSelector:active {
        color: rgba(51,51,51,0.4);
      }
      .date-week{
        font-family: PingFangSC-Light;
        font-size: 14px;
        color: #222222;
        letter-spacing: 0;
        margin-left: 4px;
        position: relative;
        top: 3px;
      }
    
      .date-festival{
        height: 16px;
        line-height: 16px;
        padding: 0 4px;
        margin-left: 4px;
        background: #FF8300;
        border-radius: 4px;
        font-size: 10px;
        color: #FFFFFF;
      }
    }

    .citySelector {
      position: relative;
      padding: 20px 0 14px;
      height: 62px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 0.5px solid #F2F2F2;
      box-sizing: border-box;
      .cityTap {
        // flex: 1;
        &.arrCityTap {
          display: flex;
          justify-content: flex-end;
          .depCityContent {
            text-align: right;
          }
        }
        .arrCityContent, .depCityContent{
          width: 110px;
          height: 30px;
          font-family: PingFangSC-Semibold;
          font-size: 22px;
          color: #222222;
          letter-spacing: 0;
          font-weight: 700;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
    
        }
      }
      .arrCity {
        text-align: right;
      }
      .arrCityContent{
        text-align: right;
      }
      .city_change {
        background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/train_icon.png');
        background-repeat: no-repeat;
        background-position: center top;
        background-size: 35px;
        text-align: center;
        height: 35px;
        width: 35px;
        flex: 1;
      }
      // .btn-rotating {
      //   transition: transform .3s linear;
      //   transform: rotate(180deg);
      // }
    }
    
    .citySelector .cityTap:active .arrCityContent, .citySelector .cityTap:active .depCityContent{
      color:rgba(51,51,51,0.4);
    }
    .citySelector .cityTap .depCity-changing {
      -webkit-transition: -webkit-transform 1s ease;
      transition: transform 1s ease;
      -webkit-transform: translateX(200%);
      transform: translateX(200%);
      text-align: right;
    }
    .citySelector .cityTap .arrCity-changing {
      -webkit-transition: -webkit-transform 1s ease;
      transition: transform 1s ease;
      -webkit-transform: translateX(-200%);
      transform: translateX(-200%);
      text-align: left;
    }
}

.yellow_tip_wraper{
    padding: 0 16px;
    box-sizing: border-box;
    width: 100%;
    .yellow_tip_content{
        width: 100%;
        box-sizing: border-box;
        padding: 5px 10px;
        background: #FFF8E5;
        border-radius: 4px;
        display: flex;
        flex-flow: row nowrap;
        justify-content: flex-start;
        align-items: center;
        .tip-notice-icon{
            padding-right: 8px;
            font-size: 16px;
            color: #FF8300;
        }
        .scorll-wrapper{
            flex: 1;
            position: relative;
            overflow: hidden;
            .scorll-container {
                -webkit-box-flex: 1;
                -webkit-flex: 1 1 0;
                -moz-flex: 1 1 0;
                flex: 1 1 0;
                display: -webkit-box;
                display: -webkit-flex;
                display: -moz-flex;
                display: flex;
                overflow: hidden;

                // height: 32rpx;
                position: relative;
                align-items: center;

                .move-content {
                    display: -webkit-box;
                    display: -webkit-flex;
                    display: -moz-flex;
                    display: flex;
                    position: absolute;
                    left: 0;

                    &.duplication {
                        display: none;
                    }

                    .move-box {
                        color: #FF8300;
                        font-size: 12px;
                        white-space: nowrap;
                        line-height: 16px;
                    }
                }
            }
            @keyframes scroll {
                0% {
                    transform: translate3d(0, 0, 0);
                }
                100% {
                    transform: translate3d(-100%, 0, 0);
                }
            }
        }
        .tip-detail-icon, .tip-close-icon{
            padding-left: 8px;
            font-size: 18px;
            color: #FF8300;
            width: 18px;
        }
    }
}

.yellow_tip_wraper-new {
  background-color: #fff;
  padding-bottom: 8px;
  .yellow_tip_content {
    padding: 5px 10px;
  }

}
.search_container_inner_new {
  background-color: #fff;
  background-image: none;
  padding: 0 16px 0;
  border-radius: 0 0 12px 12px;
  .citySelector {
    padding: 0;
    border-bottom: 0;
    height: auto;
    .cityTap {
      .depCityContent,
      .arrCityContent {
        font-size: 24px;
        font-weight: 700;
        height: auto;
      }
    }
    .city_change {
      background-image: url('https://s.qunarzz.com/f_cms/2024/1710209044463_439931773.png');
      width: 32px;
      height: 32px;
      background-size: 32px;
  
    }
  }
  
  
  .dateSelector {
    padding: 13px 0 18px;
    height: auto;
    line-height: normal;
    border-bottom: none;
    .number {
      font-size: 22px;
    }
    .date-text {
      font-size: 20px;
    }
  }
  .switch-content .switch-item-wrap {
    margin: 0;
    height: auto;
    .switch-item-txt {
      font-size: 16px;
    }
    .item-txt-tip{
      padding-right: 6px;
      font-size: 12px;
      color: #00C9E2;
    }
  }
  .switchIcon-new {
    font-size: 18px;
    color: #ccc;
    margin-left: 4px;

  }
  .selectedIcon-new {
    color: #00CAD8;
  }
  .resource-position-content{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 0 0;
    height: 17px;
    .resource-position-image{
      height: 16px;
      width: 16px;
      flex-shrink: 0;
    }
    .resource-position-text{
      max-width: 291px;
      padding: 0 2px 0 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .item-text{
        font-size: 12px;
        color: #333333;
        line-height: 17px;
        &.num{
          padding: 0 2px;
          color: #F52000;
          font-weight: bold;
        }
      }
    }
    .resource-position-right-icon{
      flex-shrink: 0;
      font-size: 12px;
      color: #999999;
    }
  }
  .search-btn {
    background-image: linear-gradient(270deg, #FC4650 0%, #FF9658 100%);
    border-radius: 25px;
    font-size: 18px;
    font-weight: 700;
    text-align: center;
    line-height: 48px;
    margin-top: 10px;
    color: #FFFFFF;
    &.has-discount{
      margin-top: 19px;
    }
  }
  .search-btn-bottom-shadow {
    width: 100%;
    height: 16px;
    background-image: url('https://s.qunarzz.com/f_cms/2024/1709889419165_445841728.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: left top;
  }
  .searchHistory-wrap {
    height: 20px;
    padding-bottom: 16px;
    border-bottom: none;
    .history-item {
      padding: 0 8px;
      color: #666666;
      background-color: rgba(240,242,247, 0.5);
    }
    .delete-icon {
      font-size: 16px;
      color: #666
    }
  }
  .dashed-line {
    width: 100%;
    border-top: 1px dashed #f2f4f7;
  }
}
.resource-position-modal-content{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  .modal-mask{
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
  }
  .modal-content{
    position: absolute;
    width: 150px;
    height: 200px;
    background: #ffffff;
    border-radius: 10px;
  }
}




//
//.yellow_tip_wraper{
//    padding-top: 20rpx;
//}
//.yellow-tip-wrap{
//  display: flex;
//  flex-direction: row;
//  border-radius: 12rpx;
//  padding: 16rpx 24rpx;
//  margin-left: 24rpx;
//  margin-right: 24rpx;
//  align-items: center;
//  justify-content: space-between;
//  background-color: #FFF8E5;
//}
//.tip-content-wrap{
//  width:558rpx;
//  font-family: PingFangSC-Regular;
//  font-size: 24rpx;
//  color: #FF8300;
//  line-height: 32rpx;
//}
//.gotoTipDetail-icon{
//  position: relative;
//  font-size: 32rpx;
//  line-height: 32rpx;
//  width: 32rpx;
//  height:32rpx;
//  display: inline-block;
//  top:8rpx;
//}
//.tip-close-icon{
//  font-size: 32rpx;
//  color: #FF8300;
//}


.search-btn {
  height: 48px;
  line-height: 48px;
  margin-top: 8px;
  border-radius: 24px;
  width: auto;
  position: relative;
  overflow: unset;
  font-family: PingFangSC-Semibold;
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  text-align: center;
  font-weight: 700;
  background-image: linear-gradient(270deg, #FC2B4E 0%, #FFB25A 100%);
  
  &::after {
    border: none;
  }
}
.search-btn-bottom-shadow {
  width: 311px;
  height: 16px;
  background-image: url('https://s.qunarzz.com/hotel_mp_source/images/linearbg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  overflow: hidden;
}

.discounts {
  position: absolute;
  line-height: 18px;
  padding: 0 9px;
  left: 0;
  // top: -50%;
  transform: translateY(-50%);
  text-align: center;
  height: 18px;
  background-image: linear-gradient(270deg, #ED223A 0%, #BF3BB5 100%);
  border: 0.5px solid #FFFFFF;
  box-shadow: inset 0px 1px 3px 0px rgba(255,255,255,0.5);
  border-radius: 12px 10px 0px 12px;
  font-family: PingFangSC-Medium;
  font-size: 12px;
  color: #FFFFFF;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
}

// .search-container .search-button {
//    background-image: linear-gradient(270deg, #FC650F 0%, #FF982E 100%);
//    box-shadow: 0 10rpx 10rpx 0 rgba(255,85,0,0.20);
// }

.search-container .search-button:active {
  background-image: linear-gradient(90deg, #E58829 0%, #E25A0D 100%);
  box-shadow: 0 5px 5px 0 rgba(255,85,0,0.20);
}
.search-container .search-button-for-share {
  background: #00C9E2;
  margin-top:14px !important;
  margin-bottom: 10px;
}

.searchHistory-wrap{
  width: 311px;
  // margin-top: 23rpx;
  height: 30px;
  display: flex;
  flex-direction: row;
  // align-items: center;
  justify-content: flex-start;
  // padding-top: 16rpx;
  padding-bottom: 12px;
  white-space: nowrap;
  border-bottom: 0.5px solid #F2F2F2;
  
}
/*隐藏滚动条*/
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
.searchHistory-wrap .history-item{
  height: 20px;
  padding-left: 10px;
  padding-right: 10px;
  font-family: PingFangSC-Light;
  font-size: 12px;
  color: #777777;
  text-align: center;
  background-color: rgba(179,206,228,0.10);
  border-radius: 12px;
  margin-right: 8px;
  line-height: 20px;
  display: inline-block;
}
.searchHistory-wrap .history-item-button{
  height: 20px;
  padding-left:8px;
  padding-right:8px;
  line-height: 24px;
  font-size: 12px;
  color: #777777;
  text-align: center;
  background-color: rgba(179,206,228,0.10);
  border-radius: 12px;
  margin-right: 8px;
  display: inline-block;
  &:after {
    border: none;
  }
  // background-color: red;
}
.searchHistory-wrap .delete-history-btn{
  height: 20px;
  padding-left:10px;
  padding-right:10px;
  line-height: 20px;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  color: #777777;
  text-align: center;
  background-color: rgba(179,206,228,0.10);
  border-radius: 12px;
  margin-right: 8px;
  display: inline-block;
}
.searchHistory-wrap .delete-history-btn .delete-icon{
  position: relative;
  font-size: 12px;
  color: #777777;
  margin-right: 2px;
  display: inline-block;
}
.searchHistory-wrap .delete-history-btn .delete-text{
  position: relative;
  top: -1px;
  display: inline-block;
  width: 29px;
}

.switch-content {
  display: flex;
  flex-direction: row;
  // height: 120rpx;
  justify-content: space-between;
  // align-items: center;
  .switch-item-wrap{
    // height: 120rpx;
    margin: 12px 0;
    height: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    .switchIcon {
      width: 16px;
      height: 16px;
      margin-left: 4px;
      background-repeat: no-repeat;
      background-size: cover;
    }
    .selectedIcon {
      background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/selected.png');
    }
    .unselectedIcon{
      background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/unselected.png');
    }
    .switch-item-txt{
      font-size: 14px;
      letter-spacing: 0;
      font-family: PingFangSC-Light;
      color: #222222;
      letter-spacing: 0;
      
    }
  }
}

.one-way-icon {
  display: flex;
  align-items: center;
  // border-bottom: 1px solid #e7e7e7;
  color: #9E9E9E;
  font-size: 30px;
  
}
.one-way-icon::before {
  content: '\f43a';
}

//特殊条提示
.special_tip_wraper{
    padding-top: 16px;
    margin: 0;
    // width: 662rpx;
    height: auto;
    .special_tip_wrap{
        margin: 0 12px;
        padding: 2px;
        // width: 662rpx;
        height: 48px;
        background: rgba(255,85,0,0.08);
        border-radius: 10px;
        // padding: 4rpx;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        .special_tip_inner{
            width: 100%;
            height: 44px;
            border: 0.5px solid rgba(255,85,0,0.10);
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
            padding: 0 10px;
            .special_content{
                flex:1;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                .special_img{
                    width: 32px;
                    height: 32px;
                }
                .content{
                    padding-left: 12px;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: center;
                    .title{
                        font-family: PingFangSC-Medium;
                        font-size: 12px;
                        color: #FF5500;
                        line-height: 16px;
                    }
                    .desc{
                        padding-top: 2px;
                        font-family: PingFangSC-Regular;
                        font-size: 10px;
                        color: #999999;
                        line-height: 14px;
                    }
                }
                .one_content{
                    font-family: PingFangSC-Medium;
                    font-size: 12px;
                    color: #FF5500;
                    line-height: 16px;
                }
            }
            .icon_next{
                font-size: 16px;
                color: #FF5500;
            }
        }
    }
}

.train-rights-search-container {
  display: flex;
  margin: 12px 0 0;
  justify-content: space-between;
  .train-right-icon-search-container {
      width: 60px;
      height: 46px;
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
  }
  .train-right-not-login {
    position: relative;
    
  }

  
  .train-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    
    &-button {
      margin-left: 0;
      padding: 0;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #222222;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      margin-right: 0;
      width: 89px;
      height: 63px;
      position: absolute;
      top: 0;
      left: 0;
      opacity: 0;
      &::after{
        display: none;
      }

    }

    &-text {
      font-size: 12px;
      color: #222222;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      height: 17px;
      line-height: 17px;
      box-sizing: border-box;
    }
  }

}

.train-banners {
  width: 100%;
  height: 60px;
  background-size: 100% 100%;
  background-image: url("https://s.qunarzz.com/f_cms/2024/1706088190339_90659804.png");
  .swiper-train-banners {
    height: 60px;
  }
  .train-banner-icon {
    width: 32px;
    height: 32px;
  }
  .train-banner-container {
    display: flex;
    align-items: center;
    height: 60px;
    padding: 3px 42px 0 26px;
    padding-top: 3px;
  }
  .train-banner-content {
    margin-left: 10px;
    font-size: 12px;
    font-family: PingFangSC-Semibold;
    color: #222222;
    letter-spacing: 0;
    line-height: 16px;
  }
  .train-banner-title {
    font-size: 12px;
    font-family: PingFangSC-Semibold;
    color: #222222;
    letter-spacing: 0;
    line-height: 16px;
  }
  .train-banner-desc {
    font-family: PingFangSC-Regular;
    font-size: 10px;
    color: #8B8B8D;
    letter-spacing: 0;
  }
  /* icon */
  .arrow {
    height:18px;
    width:16px;
    padding:0;
    font-size: 18px;
    color: #8B8B8D;
    line-height: 16px;
    position: absolute;
    right: 24px;
  }
  .arrow::before {
    position:relative;
    content:'\f03e';
    color:#ccc;
  }
}

.service {
  padding: 12px 0 0;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 12px;
  height: 17px;
  &.service-no-history {
    padding: 0;
  }
}
.anxin {
 width: 43px;
 height: 13.5px;
 background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/anxingou.png');
 background-size: 100% 100%;
}
.divline {
 margin-right: 8px;
 width: 20px;
 height: 1px;
 opacity: 0.5;
 background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
}
.divline-right{
 margin-right: 0;
  margin-left: 8px;
  background-image: linear-gradient(270deg, #ffffff 0%, #00d5e7 100%);
}
.text-list{
 margin-bottom: 1px;
}
.service-text{
 margin-left: 7px;
 color: #000;
 opacity: 0.8;
}
.dot {
  padding-right: 2px;
  color: #00CAD8;
  font-size: 14px;
}
.service-item {
  opacity: 0.8;
  font-family: PingFangSC-Light;
  font-size: 12px;
  color: #000000;
  letter-spacing: 0;
}

.train-rights-new-search-container {
  margin: 0;
  background-color: #fff;
  border-radius: 12px;
  height: 64px;
  .train-right-icon-search-container {
    width: 20px;
    height: 20px;
    margin-bottom: 2px;
  }
  .train-right-text {
    height: auto;
    line-height: normal;
    color: #666666;
  }
}
.train-banners-new {
  .train-banner-container {
    padding-top: 0;
  }
}
