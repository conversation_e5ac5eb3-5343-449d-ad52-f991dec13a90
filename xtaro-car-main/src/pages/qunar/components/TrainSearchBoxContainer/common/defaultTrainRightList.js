export const trainRightList = {
    wx: [
        {
            title: "秒赚6元",
            imgUrl: "https://qimgs.qunarzz.com/wpf_newmpic_001/1c2139ada83c49a0ce485c673d4211b2.png",
            newImgUrl:
                "https://s.qunarzz.com/f_cms/2024/1709884014685_889931713.png",
            isNeedLoginQunar: true,
            isNeedSubscribe: false,
            jumpUrl:
                "https://m.flight.qunar.com/shark/active/7ee0907cd42f308cee360025b6860b2a?packageCode=train_app_store_bargain&activityCode=train_app_store_bargain&activityType=bargain_common&processCode=commonCreateBargainProcess",
            key: "appstoredownload",
            markKey: "appstoredownload",
            afterLoginNeedJump: true,
        },
        {
            title: "预约抢票",
            imgUrl: "https://s.qunarzz.com/open_m_train/miniprogram/indexChange/appointRobNew.png",
            newImgUrl:
                "https://s.qunarzz.com/f_cms/2024/1709883936826_490722284.png",
            isNeedLoginQunar: false,
            isNeedSubscribe: false,
            jumpUrl:
                "/pages/vice_train/robOrderFillV2/index?qconfigUseCache=2&type=single_page&from=home",
            key: "gotoGrabVote",
            markKey: "",
            afterLoginNeedJump: false,
        },
        {
            title: "购买火车盲盒",
            imgUrl: "https://s.qunarzz.com/f_cms/2024/1709090227325_511671450.png",
            newImgUrl:
                "https://s.qunarzz.com/f_cms/2024/1710123972204_794901749.png",
            isNeedLoginQunar: true,
            isNeedSubscribe: false,
            jumpUrl:
                "/pages/sharkTrain/entry/train/index?cid=b52ff3b0faee4df506367c5c7e3a5e1a&bd_source=wx",
            key: "goldenFinger",
            markKey: "golden_finger",
            afterLoginNeedJump: true,
        },
        {
            title: "10元火车券",
            imgUrl: "https://qimgs.qunarzz.com/wpf_newmpic_001/eee1b797890a02383978ce978f5601ad.png",
            newImgUrl:
                "https://s.qunarzz.com/f_cms/2024/1709884030178_030841723.png",
            isNeedLoginQunar: false,
            isNeedSubscribe: false,
            jumpUrl:
                "/pages/cms/entry/train/index?activityCode=student_right_bargain_train_f&processCode=commonCreateBargainProcess&packageCode=student_right_bargain_train_f&sourceRecordNo=80490019359563691&bargainId=271182795&activityType=bargain_common&cid=21088",
            key: "tenCoupon",
            markKey: "ten_coupon",
            afterLoginNeedJump: false,
        },
    ],

    ali: [
        {
            title: "我的订单",
            imgUrl: "https://s.qunarzz.com/f_cms/2024/1709884000289_811211722.png",
            newImgUrl:
                "https://s.qunarzz.com/f_cms/2024/1709884000289_811211722.png",
            isNeedLoginQunar: false,
            isNeedSubscribe: false,
            jumpUrl: "/pages/orderList/orderList/index",
            key: "",
            markKey: "",
            afterLoginNeedJump: false,
        },
        {
            title: "预约抢票",
            imgUrl: "https://s.qunarzz.com/open_m_train/miniprogram/indexChange/appointRobNew.png",
            newImgUrl:
                "https://s.qunarzz.com/f_cms/2024/1709883936826_490722284.png",
            isNeedLoginQunar: false,
            isNeedSubscribe: false,
            jumpUrl:
                "/pages/vice_train/robOrderFillV2/index?qconfigUseCache=2&type=single_page&from=home",
            key: "gotoGrabVote",
            markKey: "",
            afterLoginNeedJump: false,
        },
    ],
};
