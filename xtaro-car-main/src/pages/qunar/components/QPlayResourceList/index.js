import { View, Image } from "@tarojs/components";
import { navigateToMiniProgram, navigateTo } from "@tarojs/taro";
import React from "react";
import LogQmark from "@/common/utils/logQmark";
import "./index.scss";
class QPlayResourceList extends React.Component {
    constructor(props) {
        super(props);
    }
    jumpToResourceUrl = (item) => {
        LogQmark({
            module: "default",
            id: "resourceItemClick",
            page: "qplay",
            operType: "click",
            ext: {
                url: item.jumpUrl,
                appId: item.copyWriting,
                sourceMaterialId: item.sourceMaterialId,
                resourcePosCode: this.props.resourcePosCode,
            },
        });
        if (item.copyWriting) {
            navigateToMiniProgram({
                appId: item.copyWriting,
                path: item.jumpUrl,
            });
        } else {
            navigateTo({
                url: item.jumpUrl,
            });
        }
    };
    render() {
        return (
            <View className="resource-list-container">
                <Image
                    className="resource-list-header-image"
                    src={this.props.resourceList[0]?.imageUrl}
                ></Image>
                <View className={`resource-list ${this.props.resourcePosCode}`}>
                    {this.props.resourceList?.map((item, index) => {
                        return (
                            <Image
                                key={index}
                                onClick={() => this.jumpToResourceUrl(item)}
                                className="resource-item"
                                src={
                                    item.imageUrlTwo ||
                                    "http://qimgs.qunarzz.com/wpf_newmpic_001/f7cf9ed28672d5792114cd5faf34098a.png"
                                }
                            ></Image>
                        );
                    })}
                </View>
            </View>
        );
    }
}

export default QPlayResourceList;
