import { Button, Image, View, Text } from "@tarojs/components";
import { navigateTo } from "@tarojs/taro";
import React from "react";
import Log from "@/common/utils/log";
import "./index.scss";

const defaultImageMap = {
    shopping: "https://s.qunarzz.com/mavericks_assets/rn_detail/shopping.png",
    food: "https://s.qunarzz.com/mavericks_assets/rn_detail/food.png",
    hotel: "https://s.qunarzz.com/mavericks_assets/rn_detail/hotel.png",
    play: "https://s.qunarzz.com/mavericks_assets/rn_detail/play.png",
    spot: "https://s.qunarzz.com/mavericks_assets/rn_detail/spot.png",
    traffic: "https://s.qunarzz.com/mavericks_assets/rn_detail/traffic.png",
};
class QuLangProductItem extends React.Component {
    constructor(props) {
        super(props);
        const { pictureUrl, type } = this.props.data;
        const thumbUrl = pictureUrl || this.getDefaultImageByCode(type);
        this.state = {
            thumbUrl,
            tags: this.getTags(),
        };
        Log({
            name: "showProduct",
            productId: props.data.poiId,
            productType: props.data.type,
            productName: props.data.name,
        });
    }

    getTags = () => {
        const tags = this.props.data.newTags;
        if (this.props.data.style) {
            tags.unshift(this.props.data.style);
        }
        return tags;
    };

    getDefaultImageByCode = (code) => {
        let res;
        switch (code) {
            case 2:
                res = "hotel";
                break;
            case 3:
                res = "shopping";
                break;
            case 4:
                res = "spot";
                break;
            case 5:
                res = "food";
                break;
            case 6:
                res = "play";
                break;
            case 7:
            case 8:
            case 9:
            case 10:
            case 21:
                res = "traffic";
                break;
            default:
                res = "traffic";
        }
        return defaultImageMap[res];
    };

    handleClick = () => {
        if (this.props.data.fastAppUrl) {
            Log({
                name: "clickProduct",
                productId: this.props.data.poiId,
                productType: this.props.data.type,
                productName: this.props.data.name,
            });
            navigateTo({
                url: this.props.data.fastAppUrl,
            });
        }
    };

    render() {
        return (
            <Button
                class="product"
                style={{ width: `${this.props.width}rpx` }}
                onClick={this.handleClick.bind(this)}
            >
                <Image
                    class="product-image"
                    src={this.state.thumbUrl}
                    mode="aspectFill"
                />

                <View class="product-detail">
                    <Text class="product-name">{this.props.data.name}</Text>
                    <View class="product-score">
                        {this.props.data.score ? (
                            <View class="score">
                                <Text>
                                    <Text class="score-value">
                                        {this.props.data.score}
                                    </Text>
                                    <Text class="score-unit">分</Text>
                                </Text>
                            </View>
                        ) : (
                            <Text class="no-score">暂无评分</Text>
                        )}

                        <Text class="distance no-score">
                            {this.props.data.sightAreaName}
                        </Text>
                    </View>
                    <View
                        class={
                            this.props.data.newTags.length > 0
                                ? "product-tag"
                                : "product-noTag"
                        }
                    >
                        {this.props.data.newTags.length &&
                            this.state.tags.map((item, index) => {
                                return (
                                    <Text
                                        class={
                                            index == 0
                                                ? "tag tag-first"
                                                : "tag tag-notFirst"
                                        }
                                    >
                                        {item}
                                    </Text>
                                );
                            }, this)}
                    </View>
                </View>
                <View class="product-price">
                    {this.props.data.price ? (
                        <Text>
                            <Text class="price-unit">¥</Text>
                            <Text class="price">{this.props.data.price}</Text>
                            <Text class="price-end">起</Text>
                        </Text>
                    ) : (
                        <Text class="product-dislike-btn">
                            <Text class="product-dislike-text">去拔草</Text>
                        </Text>
                    )}
                </View>
            </Button>
        );
    }
}

QuLangProductItem.defaultProps = {
    itemData: {},
    data: {},
    index: 0,
    width: 686,
};
export default QuLangProductItem;
