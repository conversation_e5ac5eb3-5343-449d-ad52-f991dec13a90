.single {
  background-color: rgba(245, 245, 245, 0.3);
  padding: 8px 16px 8px 16px;
}

.multiple {
  background-color: rgba(245, 245, 245, 0.3);
  padding: 8px 8px 8px 16px;
}

.container {
  display: flex;
  height: 80px;
}

.product {
  display: flex;
  background-color: #fff;
  border-radius: 2px;
  padding: 8px 0 8px 8px;
  box-sizing: content-box;
  box-shadow: 0 3px 10px 0 rgba(222, 226, 236, 0.5);
  .product-image {
    width: 64px;
    height: 64px;
    border-radius: 2px;
  }
  .product-detail {
    flex: 1;
    padding-left: 8px;
    display: flex;
    flex-direction: column;
  }
  .product-name {
    color: #212626;
    font-size: 14px;
    font-weight: bold;
    text-align: left;
    flex: 1;
    display: flex;
    align-items: center;
    lines: 1;
    text-overflow: ellipsis;
    width: 168px;
  }
  .product-score {
    display: flex;
    align-items: center;
    flex: 1;
    padding-bottom: 4.5px;
    .score {
      z-index: 2;
      position: relative;
      color: #0bd1d5;
      font-size: 15px;
      font-weight: bold;
      .score-value {
        margin-left: 2px;
        color: #00CCB6;
      }
      .score-unit {
        font-weight: normal;
        font-size: 12px;
        color: #00CCB6;
      }
  }
    .no-score {
      padding-top: 2px;
      font-size: 10px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      color: rgba(153, 153, 153, 1);
    }
    .distance {
        margin-left: 11px
    }
  }
  .product-noTag {
    flex: 0;
  }
  .product-tag {
    display: flex;
    flex-wrap: wrap;
    height: 15px;
    .tag {
      margin-right: 2px;
      padding: 1.5px 6px 0.5px 6px;
      font-size: 10px;
      border-radius: 2px;
    }
    .tag-first {
      color: #feac59;
      background: #fff8eb;
    }
    .tag-notFirst {
      color: #647281;
      background: #f5f7fa;
    }
  }
  .product-price {
    padding-right: 17px;
    display: flex;
    align-items: center;
    .price {
      vertical-align: middle;
      font-size: 16px;
      line-height: 16px;
      color: #FF5040;
      font-weight: bold;
    }
    .price-unit {
      color: #FF5040;
      font-size: 10px;
      margin-right: 1px;
      font-weight: bold;
    }
    .price-end {
      color: #647281;
      font-size: 10px;
      line-height: 16px;
      margin-left: 3px;
      vertical-align: middle;
    }
  }
  .product-dislike-btn {
    width: 53px;
    height: 20px;
    background: linear-gradient(
      137deg,
      rgba(255, 130, 95, 1) 0%,
      rgba(255, 76, 81, 1) 100%
    );
    border-radius: 14px;
    text-align: center;
  }
  .product-dislike-text {
    font-size: 12px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    color: rgba(255, 255, 255, 1);
    line-height: 16px;
    text-align: center;
  }
}
