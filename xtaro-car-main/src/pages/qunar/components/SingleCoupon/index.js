import { View, Text, Image } from "@tarojs/components";
import { navigateToMiniProgram, navigateTo } from "@tarojs/taro";
import React from "react";

import "./index.scss";

class SingleCoupon extends React.Component {
    constructor(props) {
        super(props);
    }

    handleCouponJump = (item) => {
        if (this.props.hideButton) return null;
        this.props.handleCouponJumpLog(item);
        if (!item.jumpUrl) return null;
        if (item.copyWriting) {
            navigateToMiniProgram({
                appId: item.copyWriting,
                path: item.jumpUrl,
            });
        } else {
            navigateTo({ url: item.jumpUrl });
        }
    };

    render() {
        return (
            <View
                className="single-coupon-item"
                style={this.props.couponItemStyle}
                onClick={() =>
                    this.handleCouponJump({
                        ...this.props.couponData,
                        index: 1,
                    })
                }
            >
                <View className="single-coupon-item-left">
                    <View className="coupon-item-tag">
                        <Text className="coupon-item-tag-text">
                            {this.props.couponData?.tagTitle}
                        </Text>
                    </View>
                    <View className="amount-content">
                        {this.props.couponData?.type !== "1" &&
                        this.props.couponData?.type !== "9" ? (
                            <Text className="amount-unit">￥</Text>
                        ) : (
                            <Text></Text>
                        )}
                        <Text className="amount-num">
                            {this.props.couponData?.title}
                        </Text>
                        {this.props.couponData?.type === "1" ? (
                            <Text className="amount-discount">折</Text>
                        ) : (
                            <Text></Text>
                        )}
                    </View>
                </View>
                <View className="single-coupon-item-right">
                    <View
                        className={`coupon-desc ${this.props.hideButton ? "coupon-desc-equity" : ""}`}
                    >
                        <Text className="coupon-title">
                            {this.props.couponData?.name}
                        </Text>
                        <Text className="coupon-desc-text">
                            {this.props.couponData?.desc}
                        </Text>
                    </View>
                    {!this.props.hideButton && (
                        <View className="coupon-button">
                            <Text className="coupon-button-text">去使用</Text>
                        </View>
                    )}
                </View>
                <Image
                    src={this.props.couponData?.iconImage}
                    className={`${this.props.hideButton ? "single-coupon-item-business-img-equity" : "single-coupon-item-business-img"}`}
                />
            </View>
        );
    }
}

export default SingleCoupon;
