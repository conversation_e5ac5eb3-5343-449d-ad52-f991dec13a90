@font-face {
    font-family: "hotel_rn_num";
    src: url("https://s.qunarzz.com/flight_shark/lowPriceRn/hotel_rn_num.ttf");
}

.single-coupon-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 335px;
    height: 56px;
    overflow: hidden;
    margin: 0 auto;
    position: relative;
    background: url("https://qimgs.qunarzz.com/wpf_newmpic_001/788b53cab4d85a4f1dfac42b7eb102ab.png") no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;

    .single-coupon-item-left {
        position: relative;
        width: 80px;
        height: 56px;
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
    }

    .coupon-item-tag {
        position: absolute;
        left: 0;
        top: 0;
        padding: 1px 4px;
        // border: 2rpx solid rgba(255, 255, 255, 0.2);
        display: flex;
        background: rgba(255, 102, 0, 0.2);
        align-items: center;
        border-radius: 8px 0 8px 0;
        justify-content: center;
    }

    .coupon-item-tag-text {
        color: #ff6600;
        font-size: 9px;
        text-align: left;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        line-height: 12px;
        white-space: nowrap;
    }


    .single-coupon-item-business-img {
        position: absolute;
        width: 36px;
        height: 32px;
        left: 0;
        bottom: 0;
    }

    .single-coupon-item-business-img-equity {
        position: absolute;
        width: 40px;
        height: 40px;
        top: 0;
        right: 0;
    }

    .amount-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        height: 34px;
    }

    .amount-num {
        display: inline-block;
        color: #ff6600;
        font-size: 24px;
        font-family: hotel_rn_num;
        height: 34px;
        line-height: 34px;
        white-space: nowrap;
    }

    .amount-unit,
    .amount-discount {
        display: inline-block;
        color: #ff6600;
        font-size: 16px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        height: 34px;
        line-height: 34px;
        white-space: nowrap;
        margin-top: 4px;
    }

    .amount-unit {
        margin-left: -4px;
        margin-top: 7px;
    }

    .amount-discount {
        margin-left: 1px;
    }

    .single-coupon-item-right {
        flex: 1;
        display: flex;
        padding: 0 13.5px;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        overflow: hidden;
    }

    .coupon-desc {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 66%;
        position: relative;
    }

    .coupon-desc-equity {
        width: 85%;
    }

    .coupon-title {
        display: inline-block;
        color: #333333;
        font-size: 14px;
        text-align: left;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        height: 18px;
        line-height: 18px;
        white-space: nowrap;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .coupon-desc-text {
        display: inline-block;
        color: #666666;
        font-size: 10px;
        text-align: left;
        font-family: PingFangSC-Regular;
        height: 12px;
        line-height: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .coupon-button {
        flex-shrink: 0;
        width: 70px;
        height: 28px;
        display: flex;
        background: linear-gradient(-90deg, #ff5a15, #ff954b);
        align-items: center;
        border-radius: 15px;
        justify-content: center;
    }

    .coupon-button-text {
        color: #ffffff;
        font-size: 12px;
        display: flex;
        align-items: center;
        font-family: PingFangSC-Medium;
        font-weight: 600;
        white-space: nowrap;
    }
}