import { View, Image, Text } from "@tarojs/components";
import { getStorageSync, setStorageSync, navigateTo } from "@tarojs/taro";
import React from "react";
import Log from "@/common/utils/log";
import qPoint from "@/common/train/qmark";
import "./index.scss";
import TrainSearchBoxContainer from "../TrainSearchBoxContainer/index";
import BusSearchBoxContainer from "../BusSearchBoxContainer/index";
import Api from "@/common/train/api";
import utils from "@/common/utils/util";
import EventCenter from "@/common/train/EventCenter";

// 顶部资源位关闭之后再次展示的时间间隔，前端写死，产品侧不会轻易改动,目前设定12个小时
const topBannerShowAgainTime = 12 * 60 * 60 * 1000;

class TrainSearchBox extends React.Component {
    constructor(props) {
        super(props);
        const {
            busOrder = false,
            manualSwitch = false,
            query = "",
        } = this.props;
        let searchBizType = getStorageSync("searchBizType") || "train";
        this.defaultTrainRightsList = [
            "rob-appointment",
            "my-right",
            "free",
            "wx-subscribe",
        ]; // bus covid-policy release-tip
        //非wx渠道 不是train强制置为train
        if (process.env.ANU_ENV !== "wx" && searchBizType !== "train") {
            searchBizType = "train";
        } else if (!manualSwitch && process.env.ANU_ENV === "wx" && busOrder) {
            //wx渠道 不是手动切换一级tab & 7天内有汽车订单
            searchBizType = "bus";
            setStorageSync("searchBizType", searchBizType);
        }

        const subBiz = query.subBiz;
        if (subBiz) {
            searchBizType = subBiz;
            setStorageSync("searchBizType", searchBizType);
        }

        this.state = {
            searchBizType: searchBizType, //搜索类型
            subscribeInfo: {},
            anuEnv: process.env.ANU_ENV, //渠道
            sourcePositionTopData: {}, //顶部资源位
        };
        this.closeResourcePosition = this.closeResourcePosition.bind(this);
        this.jumpResourcePosition = this.jumpResourcePosition.bind(this);
    }

    async componentDidMount() {
        await this.searchCardResourcePosition("wxBizTop");
    }

    //搜索卡片资源位
    searchCardResourcePosition = async (posCode) => {
        const lastLogin12306Timer =
            getStorageSync("closeTopResourcePosition") || "";
        //关闭时间存在 且 当前时间小于限定时间 不请求
        if (
            lastLogin12306Timer &&
            +new Date() <= lastLogin12306Timer + topBannerShowAgainTime
        ) {
            return;
        }

        if (process.env.ANU_ENV != "wx") {
            return;
        }

        const storage = await utils.getGlobalInfoAsync();
        const { openId } = storage.user;
        const params = {
            openId,
            resourcePosCode: posCode,
            biz: "train",
        };
        const response = await Api.requestSearchCardResourcePosition(
            params,
        ).catch(() => {});
        const res = response?.data || {};
        if (
            res &&
            res.resources &&
            res.resources.length > 0 &&
            res?.resources[0]?.sourceMaterial?.length > 0
        ) {
            const sourceMaterial = res?.resources[0]?.sourceMaterial?.[0] || {};
            sourceMaterial.posCode = posCode;
            sourceMaterial.isShow = true;
            qPoint(`train/new_train_index/resourcePosition/show/${posCode}`);
            this.setState({
                sourcePositionTopData: sourceMaterial,
            });
        }
    };

    componentWillReceiveProps(nextProps) {
        const {
            busOrder: newBusOrder = false,
            manualSwitch: newManualSwitch = false,
        } = nextProps || {};
        const { searchBizType } = this.state || {};
        const { busOrder = false, query = {} } = this.props || {};
        const subBiz = query.subBiz;
        if (subBiz === "train" || subBiz === "bus") {
            return;
        }

        let tempSearchBizType = searchBizType;
        //非手动切换 & 微信渠道
        if (
            !newManualSwitch &&
            process.env.ANU_ENV === "wx" &&
            busOrder !== newBusOrder
        ) {
            tempSearchBizType = newBusOrder ? "bus" : searchBizType;
            this.setState({
                searchBizType: tempSearchBizType,
            });
        }
    }

    handleActiveSearchBizType = (event) => {
        const { searchBizType } = this.state;
        let bizType = event.currentTarget.dataset.biz_type;
        if (bizType === searchBizType) {
            return;
        }
        // 埋点
        qPoint("train/new_train_index/default/click/switchTab", {
            bizType: bizType,
        });
        this.setState({
            searchBizType: bizType,
        });
        setStorageSync("searchBizType", bizType);
    };

    closeResourcePosition = () => {
        const { sourcePositionTopData = {} } = this.state;
        const { posCode, sourceMaterialId } = sourcePositionTopData;
        qPoint(`train/new_train_index/resourcePosition/click/close_${posCode}`);
        setStorageSync("closeTopResourcePosition", +new Date());
        this.setState({
            sourcePositionTopData: {
                ...this.state.sourcePositionTopData,
                isShow: false,
            },
        });
    };

    jumpResourcePosition = () => {
        const { sourcePositionTopData = {} } = this.state;
        const { posCode, sourceMaterialId } = sourcePositionTopData;
        qPoint(`train/new_train_index/resourcePosition/click/jump_${posCode}`, {
            sourceMaterialId: sourceMaterialId,
        });
        if (sourcePositionTopData.jumpUrl) {
            navigateTo({
                url: this.state.sourcePositionTopData.jumpUrl,
            });
        }
    };

    render() {
        return (
            <View
                className={`train-search-container ${this.props.indexVersionGroup === "A" ? "train-search-container-new" : ""}`}
                style={this.props.style}
            >
                <View className="search-container">
                    {this.props.indexVersionGroup === "A" &&
                        this.state.sourcePositionTopData.posCode ===
                            "wxBizTop" &&
                        this.state.sourcePositionTopData.isShow &&
                        this.state.sourcePositionTopData.imageUrl && (
                            <View className="resource-position-content">
                                <Image
                                    className="resource-position-image"
                                    src={
                                        this.state.sourcePositionTopData
                                            .imageUrl
                                    }
                                    onClick={this.jumpResourcePosition}
                                />

                                <View
                                    className="g-q-iconfont-train resource-position-close-icon"
                                    onClick={this.closeResourcePosition}
                                >
                                    &#xe49a;
                                </View>
                            </View>
                        )}

                    <View
                        className={`tabItemList ${this.state.anuEnv !== "wx" ? "tab-none" : ""} ${this.props.indexVersionGroup === "A" && this.state.sourcePositionTopData.posCode === "wxBizTop" && this.state.sourcePositionTopData.isShow && this.state.sourcePositionTopData.imageUrl ? "has-position" : ""}`}
                    >
                        <View
                            className={
                                this.state.searchBizType === "train"
                                    ? "tabItem on tabItem-1"
                                    : "tabItem tabItem-1"
                            }
                            data-biz_type="train"
                            onClick={this.handleActiveSearchBizType}
                        >
                            <View className="tabItem-box">
                                <Text className="tabItem-world">火车</Text>
                                <View className="bottom-line" />
                            </View>
                        </View>
                        <View className="line"></View>
                        <View
                            className={
                                this.state.searchBizType === "bus"
                                    ? "tabItem on tabItem-2"
                                    : "tabItem tabItem-2"
                            }
                            data-biz_type="bus"
                            onClick={this.handleActiveSearchBizType}
                        >
                            <View className="tabItem-box">
                                <Text className="tabItem-world">汽车</Text>
                                <View className="bottom-line" />
                            </View>
                        </View>
                    </View>
                    {this.state.searchBizType === "train" && (
                        <TrainSearchBoxContainer
                            query={this.props.query}
                            isLoginQunar={this.props.isLoginQunar}
                            indexVersionGroup={this.props.indexVersionGroup}
                            openVoucherModal={this.props.openVoucherModal}
                        />
                    )}

                    {this.state.searchBizType === "bus" && (
                        <BusSearchBoxContainer
                            query={this.props.query}
                            indexVersionGroup={this.props.indexVersionGroup}
                        />
                    )}
                </View>
            </View>
        );
    }
}

export default TrainSearchBox;
