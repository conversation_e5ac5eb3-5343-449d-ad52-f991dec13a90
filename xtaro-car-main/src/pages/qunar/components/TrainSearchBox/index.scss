@font-face {
  font-family: 'g-q-iconfont';
  src: url('https://s.qunarzz.com/nanachi/score/font/0.0.100/minprogram_nanachi.ttf');
}

.g-q-iconfont-train {
  font-family: 'g-q-iconfont';
  font-family: 'g-q-iconfont' !important;
  font-size: 12px;
  font-style: normal;
}
.train-search-container {
  background-color: #F5F6F9;
}
.search-container {
  // padding-top: 4rpx;
  // padding-bottom: 2rpx;
  margin: 0 16px 12px;
  border-radius: 16px 16px 12px 12px;
  position: relative;
  // top: -76rpx;
  z-index: 50;
  background-image: #fff;
  .tabItemList{
    display: flex;
    width: 100%;
    &.tab-none{
      display: none;
    }
    .tabItem{
      font-family: PingFangSC-Light;
      font-size: 16px;
      letter-spacing: 0;
      display: inline-block;
      text-align: center;
      flex: 0.5;
      color: #616161;
      &.on{
        font-family: PingFangSC-Semibold;
        color: #222222;
        font-weight: 600;
        .tabItem-box {
          position: relative;
        }

        .tabItem-world {
          position: relative;
          z-index: 2;
        }

        .bottom-line{
          position: absolute;
          bottom: 1px;
          z-index: 1;
          width: 28px;
          height: 6px;
          border-radius: 6px;
          background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
    .line{
      width: 1px;
      height: 16px;
      background-color: #e6e6e6;
      margin-top: 11px;
    }
  }
}

.search_container_inner{
    // background: #ffffff;
    background-image: linear-gradient(180deg, #F5F6F9 0%, #FFFFFF 50%);
    border-radius: 16px;
    padding: 0 24px;
}
.yellow_tip_wraper{
    padding-top: 10px;
}
.yellow-tip-wrap{
  display: flex;
  flex-direction: row;
  border-radius: 6px;
  padding: 8px 12px;
  margin-left: 12px;
  margin-right: 12px;
  align-items: center;
  justify-content: space-between;
  background-color: #FFF8E5;
}
.tip-content-wrap{
  width:279px;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  color: #FF8300;
  line-height: 16px;
}
.gotoTipDetail-icon{
  position: relative;
  font-size: 16px;
  line-height: 16px;
  width: 16px;
  height:16px;
  display: inline-block;
  top:4px;
}
.tip-close-icon{
  font-size: 16px;
  color: #FF8300;
}


.citySelector {
  position: relative;
  padding: 20px 0 14px;
  height: 62px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 0.5px solid #F2F2F2;
  box-sizing: border-box;
  .cityTap {
    // flex: 1;
    &.arrCityTap {
      display: flex;
      justify-content: flex-end;
      .depCityContent {
        text-align: right;
      }
    }
    .arrCityContent, .depCityContent{
      width: 110px;
      height: 30px;
      font-family: PingFangSC-Semibold;
      font-size: 22px;
      color: #222222;
      letter-spacing: 0;
      font-weight: 700;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

    }
  }
  .arrCity {
    text-align: right;
  }
  .arrCityContent{
    text-align: right;
  }
  .city_change {
    background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/train_icon.png');
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 35px;
    text-align: center;
    height: 35px;
    width: 35px;
    flex: 1;
  }
  // .btn-rotating {
  //   transition: transform .3s linear;
  //   transform: rotate(180deg);
  // }
}

.citySelector .cityTap:active .arrCityContent, .citySelector .cityTap:active .depCityContent{
  color:rgba(51,51,51,0.4);
}
.citySelector .cityTap .depCity-changing {
  -webkit-transition: -webkit-transform 1s ease;
  transition: transform 1s ease;
  -webkit-transform: translateX(200%);
  transform: translateX(200%);
  text-align: right;
}
.citySelector .cityTap .arrCity-changing {
  -webkit-transition: -webkit-transform 1s ease;
  transition: transform 1s ease;
  -webkit-transform: translateX(-200%);
  transform: translateX(-200%);
  text-align: left;
}

.dateSelector {
  display: flex;
  align-items: center;
  position: relative;
  height: 54px;
  line-height: 54px;
  letter-spacing: 0;
  border-bottom: 0.5px solid #F2F2F2;
  font-family: PingFangSC-Semibold;
  font-size: 22px;
  color: #222222;
  padding: 12px 0px;
  box-sizing: border-box;
  .number {
    font-family: 'hotel_rn_num';
    font-size: 24px;
    color: #222222;
    letter-spacing: 0;
    font-weight: 700;
    position: relative;
    top: 2px;
    // vertical-align: bottom;
    // margin-top: 2rpx;
  }
  .date-text {
    font-family: PingFangSC-Semibold;
    font-size: 22px;
    color: #222222;
    font-weight: 700;
  }
  .dateSelector:active {
    color: rgba(51,51,51,0.4);
  }
  .date-week{
    font-family: PingFangSC-Light;
    font-size: 14px;
    color: #222222;
    letter-spacing: 0;
    margin-left: 4px;
    position: relative;
    top: 3px;
  }

  .date-festival{
    height: 16px;
    line-height: 16px;
    padding: 0 4px;
    margin-left: 4px;
    background: #FF8300;
    border-radius: 4px;
    font-size: 10px;
    color: #FFFFFF;
  }
}
.search-btn {
  height: 48px;
  line-height: 48px;
  margin-top: 8px;
  border-radius: 24px;
  width: auto;
  position: relative;
  overflow: unset;
  font-family: PingFangSC-Semibold;
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  text-align: center;
  font-weight: 700;
  background-image: linear-gradient(270deg, #FC2B4E 0%, #FFB25A 100%);
  &::after {
    border: none;
  }
}
.search-btn-bottom-shadow {
  width: 311px;
  height: 16px;
  background-image: url('https://s.qunarzz.com/hotel_mp_source/images/linearbg.png');
  background-size: 100% 100%;
  overflow: hidden;
}

.discounts {
  position: absolute;
  line-height: 18px;
  padding: 0 9px;
  left: 0;
  // top: -50%;
  transform: translateY(-50%);
  text-align: center;
  height: 18px;
  background-image: linear-gradient(90deg, #FFD688 0%, #FFC192 100%);
  border: 0.5px solid #FFFFFF;
  box-shadow: inset 0px 1px 3px 0px rgba(255,255,255,0.5);
  border-radius: 12px 10px 0px 12px;
  font-family: PingFangSC-Medium;
  font-size: 10px;
  color: #F23106;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
}

// .search-container .search-button {
//    background-image: linear-gradient(270deg, #FC650F 0%, #FF982E 100%);
//    box-shadow: 0 10rpx 10rpx 0 rgba(255,85,0,0.20);
// }

.search-container .search-button:active {
  background-image: linear-gradient(90deg, #E58829 0%, #E25A0D 100%);
  box-shadow: 0 5px 5px 0 rgba(255,85,0,0.20);
}
.search-container .search-button-for-share {
  background: #00C9E2;
  margin-top:14px !important;
  margin-bottom: 10px;
}

.searchHistory-wrap{
  width: 311px;
  // margin-top: 23rpx;
  height: 20px;
  display: flex;
  flex-direction: row;
  // align-items: center;
  justify-content: flex-start;
  // padding-top: 16rpx;
  padding-bottom: 12px;
  white-space: nowrap;
  border-bottom: 0.5px solid #F2F2F2;
  
}
/*隐藏滚动条*/
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
.searchHistory-wrap .history-item{
  height: 20px;
  padding-left: 8px;
  padding-right: 8px;
  font-family: PingFangSC-Light;
  font-size: 12px;
  color: #777777;
  text-align: center;
  background-color: rgba(179,206,228,0.10);
  border-radius: 12px;
  margin-right: 8px;
  line-height: 20px;
  display: inline-block;
}
.searchHistory-wrap .history-item-button{
  height: 20px;
  padding-left:10px;
  padding-right:10px;
  line-height: 24px;
  font-size: 12px;
  color: #777777;
  text-align: center;
  background-color: rgba(179,206,228,0.10);
  border-radius: 12px;
  margin-right: 8px;
  display: inline-block;
  &:after {
    border: none;
  }
  // background-color: red;
}
.searchHistory-wrap .delete-history-btn{
  height: 20px;
  padding-left:8px;
  padding-right:8px;
  line-height: 20px;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  color: #777777;
  text-align: center;
  background-color: rgba(179,206,228,0.10);
  border-radius: 12px;
  margin-right: 8px;
  display: inline-block;
}
.searchHistory-wrap .delete-history-btn .delete-icon{
  position: relative;
  font-size: 12px;
  color: #777777;
  margin-right: 2px;
  display: inline-block;
}
.searchHistory-wrap .delete-history-btn .delete-text{
  position: relative;
  top: -1px;
  display: inline-block;
  width: 29px;
}

.switch-content {
  display: flex;
  flex-direction: row;
  // height: 120rpx;
  justify-content: space-between;
  // align-items: center;
  .switch-item-wrap{
    // height: 120rpx;
    margin: 12px 0;
    height: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    .switchIcon {
      width: 16px;
      height: 16px;
      margin-left: 4px;
      background-repeat: no-repeat;
      background-size: cover;
    }
    .selectedIcon {
      background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/selected.png');
    }
    .unselectedIcon{
      background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/unselected.png');
    }
    .switch-item-txt{
      font-size: 14px;
      letter-spacing: 0;
      font-family: PingFangSC-Light;
      color: #222222;
      letter-spacing: 0;
      
    }
  }
}

.one-way-icon {
  display: flex;
  align-items: center;
  // border-bottom: 1px solid #e7e7e7;
  color: #9E9E9E;
  font-size: 30px;
}
.one-way-icon::before {
  content: '\f43a';
}

//特殊条提示
.special_tip_wraper{
    padding-top: 16px;
    margin: 0;
    // width: 662rpx;
    height: auto;
    .special_tip_wrap{
        margin: 0 12px;
        padding: 2px;
        // width: 662rpx;
        height: 48px;
        background: rgba(255,85,0,0.08);
        border-radius: 10px;
        // padding: 4rpx;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        .special_tip_inner{
            width: 100%;
            height: 44px;
            border: 0.5px solid rgba(255,85,0,0.10);
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
            padding: 0 10px;
            .special_content{
                flex:1;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                .special_img{
                    width: 32px;
                    height: 32px;
                }
                .content{
                    padding-left: 12px;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: center;
                    .title{
                        font-family: PingFangSC-Medium;
                        font-size: 12px;
                        color: #FF5500;
                        line-height: 16px;
                    }
                    .desc{
                        padding-top: 2px;
                        font-family: PingFangSC-Regular;
                        font-size: 10px;
                        color: #999999;
                        line-height: 14px;
                    }
                }
                .one_content{
                    font-family: PingFangSC-Medium;
                    font-size: 12px;
                    color: #FF5500;
                    line-height: 16px;
                }
            }
            .icon_next{
                font-size: 16px;
                color: #FF5500;
            }
        }
    }
}

.train-rights {
  display: flex;
  margin: 12px 0 15px;
  justify-content: space-between;
  .rob-icon {
    width: 60px;
    height: 46px;
  }
  .wx-icon {
    background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/wx.png');
  }
  .right-icon {
    background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/my-right.png');
  }
  .free-icon {
    background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/free-take.png');
  }
  .policy-icon {
    background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/icon_vx_antiepidemic.png');
  }
  .release-icon {
    background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/icon_vx_calendar.png');
  }
  .bus-icon {
    background-image: url("https://s.qunarzz.com/open_m_train/miniprogram/indexChange/bus_icon.png");
  }
  .train-right-icon {
      width: 60px;
      height: 46px;
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
  }
  .train-right-not-login {
    position: relative;
    .train-right-button {
      position: absolute;
      top: 0;
      left: 0;
      opacity: 0;
      width: 89px;
      height: 63px;

    }
  }
  .train-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    
    &-button {
      margin-left: 0;
      padding: 0;
      height: 17px;
      line-height: 17px;
      // background-color: #FFFFFF;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #222222;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      margin-right: 0;
      .train-right-text {
        height: 17px;
        line-height: 17px;
        box-sizing: border-box;
      }
      &::after{
        display: none;
      }

    }
    &-text {
      font-size: 12px;
      color: #222222;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
    }
  }

}

.service {
  padding: 12px 0 0;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 12px;
  height: 17px;
  &.service-no-history {
    padding: 0;
  }
}
.anxin {
 width: 43px;
 height: 13.5px;
 background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/anxingou.png');
 background-size: 100% 100%;
}
.divline {
 margin-right: 8px;
 width: 20px;
 height: 1px;
 opacity: 0.5;
 background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
}
.divline-right{
 margin-right: 0;
  margin-left: 8px;
  background-image: linear-gradient(270deg, #ffffff 0%, #00d5e7 100%);
}
.text-list{
 margin-bottom: 1px;
}
.service-text{
 margin-left: 7px;
 color: #000;
 opacity: 0.8;
}
.dot {
  padding-right: 2px;
  color: #00CAD8;
  font-size: 14px;
}
.service-item {
  opacity: 0.8;
  font-family: PingFangSC-Light;
  font-size: 12px;
  color: #000000;
  letter-spacing: 0;
}
.train-search-container-new {
  background-color: #fff;
  padding-top: 0;
  .search-container {
    margin: 0;
    border-radius: 0;
    // background-color: #f3f4f7;
    .resource-position-content{
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 12px 0;
      height: 32px;
      position: relative;
      background: #ffffff;
      .resource-position-image{
        height: 32px;
        width: 327px;
      }
      .resource-position-close-icon{
        width: 18px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 10px;
        color: #999999;
        position: absolute;
        right: 12px;
      }
    }
  }
  .tabItemList {
    background-color: #fff;
    align-items: center;
    padding: 16px 0;
    &.has-position{
      padding: 12px 0 16px;
    }
    .tabItem {
      font-size: 14px;
      color: #333;
      padding: 0;
      &:first-child {
        text-align: right;
        padding-right: 40px;
      }
      &:last-child {
        text-align: left;
        padding-left: 40px;
      }
      &.tabItem-1 {
          &.on {
            .bottom-line {
              left: auto;
              right: 0;
              width: 32px;
              transform: translateX(0);
            }
          }
      }
      &.tabItem-2 {
        &.on {
          .bottom-line {
            width: 32px;
            left: 0;
            transform: translateX(0);
          }
        }
    }
      &.on {
        font-size: 16px;
        font-weight: 700;
      }
    }
    .line {
      height: 14px;
      background: #F3F4F7;
      margin-top: 0px;
    }
  }
  
}