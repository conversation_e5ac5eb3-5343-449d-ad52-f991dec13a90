@import '../../style/g-define.scss';
@import '../../style/iconfont.scss';
.change-phone-wrap-quick {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    // display: flex;
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .login-container {
        flex-direction: column;
        align-items: center;
        background-color: #fff;
        width: 280px;
        border-radius: 8px;
        transform: translateY(-100px);
        box-sizing: border-box;
        padding: 10px 0;
    }
    // align-items 左右
    // justify-content 上下
    .header{
        width: 100%;
        height: 18px;
        box-sizing: border-box;
        padding-right: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-end;
    }
    .cancel{
        font-size: 18px;
        color: #9E9E9E;
    }
    .title{
        width: 100%;
        height: 40px;
        font-family: PingFangSC-Regular;
        font-size: 18px;
        color: #333333;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
    }
    .desc{
        width: 100%;
        height: 20px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #9E9E9E;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        margin-bottom: 30px;
    }
    .main{
        box-sizing: border-box;
        width: 100%;
        height: 44px;
        padding: 0 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
    }
    .code-input{
        box-sizing: border-box;
        width: 156px;
        height: 44px;
        border: 1px solid #E0E0E0;
        border-radius: 4px;
        margin-right: 4px;
        padding-left: 8px;
        font-size: 16px;
        color: #333333;
    }
    .sms-code{
        box-sizing: border-box;
        width: 80px;
        height: 44px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        text-align: center;
        border-radius: 4px;
        border: 1px solid #00BCD4;
        color: #00BCD4;
    }
    .gray{
        background: #EEEEEE;
        color: #BDBDBD;
        border: none;
    }
    .button{
        box-sizing: border-box;
        width: 240px;
        height: 44px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        background: #00D4E3;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #ffffff;
        text-align: center;
        border-radius: 44px;
        margin-bottom: 10px;
    }
    .default{
        background: #E6E6E6;
    }
    .p-login-wrap-protocol-platformLoginToastQuick {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top:40px;
        flex-wrap: wrap;

       .p-login-wrap-changeSelected {
            display: flex;
            flex-direction: row;
            align-items: center;
       }
        .p-login-wrap-checkbox {
            width: 15px;
            height: 15px;
            display: block;
            border: 1px solid #333;
            text-align: center;
            line-height: 15px;
            overflow: hidden;
        }
    
        .p-login-wrap-text {
            font-size: 14px;
            margin-left: 5px;
        }

        .p-login-wrap-protocolText {
            font-size: 14px;
            color: rgb(0, 188, 212);
        }
    }

    
}
.show-true{
    display: flex;
}