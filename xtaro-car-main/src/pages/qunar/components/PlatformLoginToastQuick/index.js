import { View, Text, Input } from "@tarojs/components";
import React from "react";
import "./index.scss";

class PlatformLoginToastQuick extends React.Component {
    logIn = () => {
        this.props.logIn();
    };
    bindPhoneChange = (e) => {
        this.props.bindPhoneChange(e);
    };
    getCode = () => {
        this.props.getCode();
    };
    bindCodeChange = (e) => {
        this.props.bindCodeChange(e);
    };
    user_login_clearInput = () => {
        this.props.user_login_clearInput();
    };
    switchPhone = (bol) => {
        this.props.switchPhone(bol);
    };
    changeSelected = () => {
        this.props.changeSelected();
    };

    clickProtocolText = () => {
        this.props.clickProtocolText();
    };

    clickProtoColToPrivacyPolicy = () => {
        this.props.clickProtoColToPrivacyPolicy();
    };
    render() {
        return (
            <View class={`change-phone-wrap-quick show-${this.props.show}`}>
                <View class="login-container">
                    <View class="header">
                        <Text
                            class="g-q-iconfont cancel"
                            onClick={this.switchPhone.bind(this, false)}
                        >
                            
                        </Text>
                    </View>
                    <Text class="title">请输入验证码</Text>
                    <Text class="desc">
                        验证码已发送至：{this.props.phoneTitle}
                    </Text>
                    <View class="main">
                        <Input
                            type="number"
                            class="code-input"
                            placeholder="请输入验证码"
                            maxlength="6"
                            onInput={this.bindCodeChange}
                            value={this.props.code}
                        />
                        <Text
                            class={
                                "sms-code " +
                                (!this.props.canSendSmsFlag ? "gray" : "")
                            }
                            onClick={this.getCode.bind(this)}
                        >
                            {this.props.smsBtnText}
                        </Text>
                    </View>
                    <View className="p-login-wrap-protocol-platformLoginToastQuick">
                        <View
                            className="p-login-wrap-changeSelected"
                            onClick={this.changeSelected.bind(this)}
                        >
                            <Text className="p-login-wrap-checkbox g-q-iconfont">
                                {this.props.hasAgreedProtocol ? "" : ""}
                            </Text>
                            <Text className="p-login-wrap-text">
                                同意并遵行去哪儿网
                            </Text>
                        </View>
                        <Text
                            className="p-login-wrap-protocolText"
                            onClick={this.clickProtocolText.bind(this)}
                        >
                            《用户协议》
                        </Text>
                        <Text className="p-login-wrap-text">及</Text>
                        <Text
                            className="p-login-wrap-protocolText"
                            onClick={this.clickProtoColToPrivacyPolicy.bind(
                                this,
                            )}
                        >
                            《隐私政策》
                        </Text>
                    </View>
                    <Text
                        class={
                            "button" +
                            (!this.props.hasAgreedProtocol ? " default" : "")
                        }
                        onClick={this.logIn.bind(this)}
                    >
                        确定
                    </Text>
                </View>
            </View>
        );
    }
}

export default PlatformLoginToastQuick;
