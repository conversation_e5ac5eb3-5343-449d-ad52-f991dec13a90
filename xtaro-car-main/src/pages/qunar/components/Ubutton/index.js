import { Block, Button } from "@tarojs/components";
import { getCurrentInstance, getUserInfo } from "@tarojs/taro";
import React from "react";
import "./index.scss";
import CommonUtils from "@/common/utils/util.js";

const apiNameSpace = CommonUtils.getNameSpace();
class Ubutton extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            isPrivacyEquityAuth: true,
            supportGetUserProfile:
                apiNameSpace && apiNameSpace.getUserProfile ? true : false,
            isWx: process.env.ANU_ENV === "wx",
        };
        this.loop = function () {};
    }

    componentDidMount() {
        if (this.state.isWx && wx.canIUse("getPrivacySetting")) {
            wx.getPrivacySetting({
                success: (res) => {
                    if (res.needAuthorization) {
                        // 需要弹出隐私协议
                        this.setState({
                            isPrivacyEquityAuth: false,
                        });
                    }
                },
                fail: () => {
                    this.setState({
                        isPrivacyEquityAuth: false,
                    });
                },
            });
        }
    }

    config = {
        styleIsolation: "apply-shared",
    };

    getUserProfile (e) {
        const ctx = this;
        getUserInfo({
            success: (res) => {
                (
                    this.props.onGetUserInfo ||
                    this.props.onGetuserinfo ||
                    this.loop
                ).call(
                    ctx,
                    Object.assign(e, res, {
                        detail: {
                            ...e.detail,
                            encryptedData: res.encryptedData,
                            errMsg: "getUserInfo:ok",
                            iv: res.iv,
                            rawData: res.rawData,
                            signature: res.signature,
                            userInfo: res.userInfo,
                        },
                    }),
                );
            },
            fail: (e) => {
                console.error(e);
            },
        });

        // 用户层的 tap 事件
        // button. onGetUserInfo = {xxx} onClick={yyy}
        (this.props.onClick || this.props.onTap || this.loop).apply(ctx, Array.from(arguments));
    };

    render() {
        return (
            <Block>
                {this.state.isWx &&
                this.state.supportGetUserProfile &&
                this.state.isPrivacyEquityAuth ? (
                    <Button
                        className={this.props.className || this.props.class}
                        onClick={this.getUserProfile.bind(this)}
                        formType={this.props.formType || ""}
                        lang={this.props.lang || "zh_CN"}
                        data-id={this.props["data-id"] || ""}
                        data-url={this.props["data-url"] || ""}
                        style={this.props.style}
                    >
                        {this.props.children}
                    </Button>
                ) : (
                    <Button
                        class={this.props.className || this.props.class}
                        open-type={this.props["open-type"]}
                        onClick={
                            this.props.onClick || this.props.onTap || this.loop
                        }
                        onGetUserInfo={
                            this.props.onGetUserInfo ||
                            this.props.onGetuserinfo ||
                            this.props.ongetuserinfo ||
                            this.props.bindgetuserinfo
                        }
                        formType={this.props.formType || ""}
                        lang={this.props.lang || "zh_CN"}
                        data-id={this.props["data-id"] || ""}
                        data-url={this.props["data-url"] || ""}
                        style={this.props.style}
                    >
                        {this.props.children}
                    </Button>
                )}
            </Block>
        );
    }
}

export default Ubutton;
