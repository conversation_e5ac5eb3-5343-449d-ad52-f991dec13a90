import {
    showToast,
    getApp,
    getStorageSync,
    setStorage,
    getSystemInfo,
    showModal,
} from "@tarojs/taro";
import React from "react";
import dateFormat from "@/common/utils/date/format";
import User from "@/common/utils/user";
import log from "@/common/flight/log.js";
import preSearch from "../../../pages/platform/indexWx/preSearch";
import request from "@/common/utils/request.js";
import utils from "@/common/utils/util";

const logic = {
    loginStatus: false,
    // 搜索前校验输入城市
    checkCity(depCity, arrCity) {
        if (!depCity || !arrCity) {
            showToast({
                title: `${!depCity ? "出发" : ""}${!arrCity ? "到达" : ""}城市不能为空哦~`,
                icon: "none",
                duration: 1000,
            });
            return false;
        }
        if (depCity === arrCity) {
            showToast({
                title: "选择的出发到达城市不能相同哦~",
                icon: "none",
                duration: 1000,
            });
            return false;
        }
        return true;
    },
    // 填充搜索参数
    getParams(isInner, searchType, originParams, entranceType) {
        const globalData = getApp().globalData;
        const params = {};
        const innerBaseParams = {
            depCity: encodeURI(originParams.depCity),
            arrCity: encodeURI(originParams.arrCity),
            depAirport: encodeURI(originParams.depAirport),
            arrAirport: encodeURI(originParams.arrAirport),
            goDate: encodeURI(originParams.goDate),
            from: process.env.ANU_ENV,
        };
        const newTouchBaseParams = {
            startCity: encodeURI(originParams.depCity),
            destCity: encodeURI(originParams.arrCity),
            startDate: encodeURI(originParams.goDate),
            from: process.env.ANU_ENV,
        };
        if (isInner && searchType === "oneway") {
            Object.assign(params, innerBaseParams);
            params.child = 0;
            params.baby = 0;
            params.cat = "flight_home";
            params.cabinType = 0;
        } else if (isInner && searchType === "roundway") {
            Object.assign(params, innerBaseParams);
            params.backDate = encodeURI(originParams.backDate);
            params._firstScreen = 1;
            params.cat = "flight_home";
            if (
                entranceType &&
                entranceType.inner_roundway_list === "new_touch" &&
                globalData &&
                globalData.bd_origin
            ) {
                params.bd_origin = globalData.bd_origin;
            }
        } else if (!isInner && searchType === "oneway") {
            if (entranceType && entranceType.inter_type === "new_touch") {
                Object.assign(params, innerBaseParams);
                if (globalData && globalData.bd_origin) {
                    params.bd_origin = globalData.bd_origin;
                }
            } else {
                Object.assign(params, newTouchBaseParams);
            }
            params.backDate = "";
            params.flightType = "oneWay";
            params.adultNum = 1;
            params.childNum = 0;
            params.cabin = 0;
            params.isFlush = 1;
        } else if (!isInner && searchType === "roundway") {
            if (entranceType && entranceType.inter_type === "new_touch") {
                Object.assign(params, innerBaseParams);
                if (globalData && globalData.bd_origin) {
                    params.bd_origin = globalData.bd_origin;
                }
            } else {
                Object.assign(params, newTouchBaseParams);
            }
            params.backDate = encodeURI(originParams.backDate);
            params.flightType = "roundWay";
            params.adultNum = 1;
            params.childNum = 0;
            params.cabin = 0;
            params.isFlush = 1;
        } else {
            return false;
        }
        return params;
    },

    // 针对出行日期为：当日、次日、后日特殊展示
    compareDateWithToday(startDate) {
        const today = new Date();
        today.setHours(0, 0, 0, 0); // 忽略时间，只比较日期

        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const dayAfterTomorrow = new Date(today);
        dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

        const givenDate = new Date(startDate);
        givenDate.setHours(0, 0, 0, 0); // 忽略时间，只比较日期

        if (givenDate.getTime() === today.getTime()) {
            return "今天";
        } else if (givenDate.getTime() === tomorrow.getTime()) {
            return "明天";
        } else if (givenDate.getTime() === dayAfterTomorrow.getTime()) {
            return "后天";
        } else {
            return "";
        }
    },

    // 初始化日期和时间相关参数
    initDate(query = {}) {
        const oneDay = 86400000;
        log({
            p: "mini_home",
            r: "init_date_query_go_date",
            goDate: query.goDate,
        });
        if (
            query.goDate &&
            query.goDate.replace &&
            isNaN(new Date(query.goDate.replace(/-/g, "/")).getDay())
        ) {
            log({
                p: "mini_home",
                r: "query_go_date_nan_error",
                goDate: query.goDate,
            });
            query.goDate = undefined;
        }

        if (
            query.goDate &&
            query.goDate !== "null" &&
            query.goDate !== "undefined"
        ) {
            query.goTime = new Date(query.goDate.replace(/-/g, "/")).getTime();
            if (isNaN(query.goTime)) {
                log({
                    p: "mini_home",
                    r: "query_go_time_nan_error",
                    goTime: query.goTime,
                    goDate: query.goDate,
                });
                query.goTime = undefined;
            }
        }

        if (
            query.backDate &&
            query.backDate !== "null" &&
            query.backDate !== "undefined"
        ) {
            query.backTime = new Date(
                query.backDate.replace(/-/g, "/"),
            ).getTime();
        }
        const flightGoDate = getStorageSync("FlightGoDate");
        const flightBackDate = getStorageSync("FlightBackDate");

        // 初始化日期和城市
        let goDate = query.goTime || flightGoDate, // Times
            backDate = query.backTime || flightBackDate, // Times
            days = 1,
            now = new Date(),
            today = new Date(
                now.getFullYear() +
                    "/" +
                    (now.getMonth() + 1) +
                    "/" +
                    now.getDate(),
            );

        /** 默认明天 往返三天 如果出发日期过期,取今天 */
        goDate = goDate
            ? goDate >= today.getTime()
                ? goDate
                : today.getTime()
            : today.getTime() + oneDay;
        backDate =
            backDate && backDate > goDate ? backDate : goDate + 2 * oneDay; // 返回时间大于去程时间 取参数或缓存 否则取去程时间后三天

        let goTime = new Date(goDate),
            backTime = new Date(backDate),
            bg =
                now.getHours() >= 19
                    ? "https://s.qunarzz.com/nnc_flight/image/nightbg.png"
                    : "https://s.qunarzz.com/nnc_flight/image/daybg.png";

        if (isNaN(goTime.getDay())) {
            log({
                p: "mini_home",
                r: "go_time_date_error",
                goTime: goTime,
            });
            goTime = new Date();
        }
        if (isNaN(backTime.getDay())) {
            log({
                p: "mini_home",
                r: "back_time_date_error",
                backTime: backTime,
            });
            backTime = new Date();
        }
        const calTimes = new Date(
            goTime.getFullYear() +
                "/" +
                (goTime.getMonth() + 1) +
                "/" +
                goTime.getDate(),
        ); // 将godate初始化为当天的00：00
        days =
            (backDate - calTimes) % oneDay === 0
                ? Math.ceil((backDate - calTimes) / oneDay) + 1
                : Math.ceil((backDate - calTimes) / oneDay);

        // 更新缓存
        setStorage({
            key: "FlightGoDate",
            data: goDate,
        });
        setStorage({
            key: "FlightBackDate",
            data: backDate,
        });

        const initGoDate = dateFormat(goTime, "yyyy-mm-dd");
        const initBackDate = dateFormat(backTime, "yyyy-mm-dd");

        log({
            p: "mini_home",
            r: "flight_storage_date",
            queryData: JSON.stringify(query),
            storageFlightGoDate: flightGoDate,
            storageFlightBackDate: flightBackDate,
            todayTime: today.getTime(),
            goTime,
            backTime,
            initGoDate,
            initBackDate,
        });

        // 设置预搜数据
        preSearch.setDateData({
            goDate: initGoDate,
            backDate: initBackDate,
        });

        return {
            goDate: initGoDate,
            displayGoMonth: dateFormat(goTime, "m"),
            displayGoDate: dateFormat(goTime, "d"),
            goWeek: dateFormat(goTime, "周w"),
            goDateTip: this.compareDateWithToday(goTime),
            backDate: initBackDate,
            backDateTip: this.compareDateWithToday(backTime),
            displayBackMonth: dateFormat(backTime, "m"),
            displayBackDate: dateFormat(backTime, "d"),
            backWeek: dateFormat(backTime, "周w"),

            days,
            bg,
        };
    },

    // 初始化城市信息
    initCity(query = {}) {
        let depCity = "北京",
            depCityType = "inner",
            arrCity = "上海",
            arrCityType = "inner",
            isInner = true;

        // 只有两者都传的情况才使用，否则会出现city和cityType不一致的情况
        if (
            query.depCity &&
            query.depCityType &&
            query.depCity !== "null" &&
            query.depCityType !== "null" &&
            query.depCity !== "undefined" &&
            query.depCityType !== "undefined"
        ) {
            depCity = query.depCity;
            depCityType = query.depCityType;

            // 更新缓存
            setStorage({
                key: "FlightDepCity",
                data: depCity,
            });
            setStorage({
                key: "FlightDepType",
                data: depCityType,
            });
        } else {
            depCity = getStorageSync("FlightDepCity") || depCity;
            depCityType = getStorageSync("FlightDepType") || depCityType;
        }

        if (
            query.arrCity &&
            query.arrCityType &&
            query.arrCity !== "null" &&
            query.arrCityType !== "null" &&
            query.arrCity !== "undefined" &&
            query.arrCityType !== "undefined"
        ) {
            arrCity = query.arrCity;
            arrCityType = query.arrCityType;

            // 更新缓存
            setStorage({
                key: "FlightArrCity",
                data: arrCity,
            });
            setStorage({
                key: "FlightArrType",
                data: arrCityType,
            });
        } else {
            arrCity = getStorageSync("FlightArrCity") || arrCity;
            arrCityType = getStorageSync("FlightArrType") || arrCityType;
        }

        isInner = depCityType === "inner" && arrCityType === "inner";

        // 设置预搜数据
        preSearch.setCityData({
            depCity,
            depCityType,
            arrCity,
            arrCityType,
        });

        return {
            depCity,
            depCityType,

            arrCity,
            arrCityType,

            isInner,
        };
    },

    // 初始化数据
    initData(sysInfo, env) {
        const {
            system,
            screenHeight,
            screen,
            windowHeight,
            statusBarHeight,
            navigationBarHeight = 44,
        } = sysInfo;
        const isIOS = system.indexOf("iOS") !== -1;
        /** 或得scroll-view的高度 */
        let height = Math.max(
            (process.env.ANU_ENV === "ali" ? screen.height : screenHeight) || 0,
            windowHeight || 0,
        );
        height = height - statusBarHeight - navigationBarHeight;
        let headFittable = true;

        return {
            isIOS: isIOS,
            statusBarHeight: statusBarHeight,
            navigationBarHeight: navigationBarHeight,
            height: height,

            headFittable: headFittable,
            lastTap: new Date().getTime(),
        };
    },

    // 更新设置日期数据
    setDate(date, preGoDate, preBackDate) {
        const obj = {};
        if (date.startDate) {
            obj.displayGoMonth = dateFormat(date.startDate, "m");
            obj.displayGoDate = dateFormat(date.startDate, "d");
            obj.goDate = dateFormat(date.startDate, "yyyy-mm-dd");
            obj.goWeek = dateFormat(date.startDate, "周w");
            obj.goDateTip = this.compareDateWithToday(date.startDate);
        }
        if (date.endDate) {
            obj.displayBackMonth = dateFormat(date.endDate, "m");
            obj.displayBackDate = dateFormat(date.endDate, "d");
            obj.backDate = dateFormat(date.endDate, "yyyy-mm-dd");
            obj.backWeek = dateFormat(date.endDate, "周w");
            obj.backDateTip = this.compareDateWithToday(date.endDate);
        }

        const goDate = date.startDate
                ? date.startDate
                : new Date(preGoDate.replace(/-/g, "/")),
            goTime = new Date(
                goDate.getFullYear() +
                    "/" +
                    (goDate.getMonth() + 1) +
                    "/" +
                    goDate.getDate(),
            ).getTime(),
            backTime = date.endDate
                ? date.endDate.getTime()
                : new Date(preBackDate.replace(/-/g, "/")).getTime(),
            oneDay = 86400000;

        obj.days =
            (backTime - goTime) % oneDay === 0
                ? Math.ceil((backTime - goTime) / oneDay) + 1
                : Math.ceil((backTime - goTime) / oneDay);
        setStorage({
            key: "FlightGoDate",
            data: goTime,
        });
        setStorage({
            key: "FlightBackDate",
            data: backTime,
        });

        // 设置预搜数据
        preSearch.setDateData(
            {
                goDate: obj.goDate || preGoDate,
                backDate: obj.backDate,
            },
            "DATE_ACTION",
        );

        return obj;
    },

    // 更新设置城市数据
    setCity(cityData, preDepCityType, preArrCityType) {
        const obj = {},
            key = cityData.isDep ? "FlightDepCity" : "FlightArrCity",
            typeKey = cityData.isDep ? "FlightDepType" : "FlightArrType";
        if (cityData.isDep) {
            obj.depCity = cityData.city || "";
            obj.depAirport = cityData.airport || "";
            obj.depAirportCode = cityData.airportCode || "";
            obj.depCityType = cityData.isInner ? "inner" : "inter";
            obj.isInner = cityData.isInner && preArrCityType === "inner"; // 新的出发城市和原有的到达城市都为国内 则isInner 为true
        } else {
            obj.arrAirport = cityData.airport || "";
            obj.arrAirportCode = cityData.airportCode || "";
            obj.arrCity = cityData.city || "";
            obj.arrCityType = cityData.isInner ? "inner" : "inter";
            obj.isInner = cityData.isInner && preDepCityType === "inner"; // 新的到达城市和原有的出发城市都是国内，则 isInner 为true
        }
        setStorage({
            key: key,
            data: cityData.city || "",
        });
        setStorage({
            key: typeKey,
            data: cityData.isInner ? "inner" : "inter",
        });

        // 设置预搜数据
        preSearch.setCityData(
            obj,
            cityData.isDep ? "DEP_CITY_ACTION" : "ARR_CITY_ACTION",
        );

        return obj;
    },

    // 获取设备和百度app信息进行兼容
    getSystemInfo() {
        return new Promise((resolve, reject) => {
            getSystemInfo({
                success: (data) => {
                    resolve(data);
                },
                fail: () => {
                    showModal({
                        title: "提示",
                        content: "获取本地信息失败，请重试",
                        showCancel: false,
                        cancelText: "取消",
                        confirmColor: "#00bcd4",
                        confirmText: "确定",
                    });
                    resolve("");
                },
                complete: () => {},
            });
        });
    },

    // 检测用户是否登录
    async checkLogin() {
        const res = await User.checkLogin();
        // 声明但是未使用 先注释了
        // let status = false;
        if (res?.ret && res.data.isLogin) {
            logic.loginStatus = true;
        } else {
            logic.loginStatus = false;
        }
    },

    /**
     * 获取市场侧资源位数据，目前只接了顶部资源位，接口目前只支持单独获取资源位的数据，不支持一次性返回所有资源位数据
     * @returns
     */
    async getResourcePositionData(resourcePosCode) {
        const storage = await utils.getGlobalInfoAsync();
        const { openId } = storage.user;
        return new Promise((resolve, reject) => {
            request({
                service: "/gw/m/api/applet/resource",
                host: "https://m.flight.qunar.com",
                method: "POST",
                data: {
                    openId,
                    resourcePosCode,
                    biz: "flight", // 业务线字段
                },
                ignoreStatus: true,
                success: (res) => {
                    if (res && res.status === 0 && res.data) {
                        const { resources = [] } = res.data || {};
                        const { sourceMaterial = [] } = resources[0] || {};
                        if (
                            sourceMaterial[0] &&
                            sourceMaterial[0].sourceMaterialId
                        ) {
                            // 只返回了顶部资源位的数据
                            resolve(sourceMaterial[0]);
                        } else {
                            resolve();
                        }
                    } else {
                        resolve();
                    }
                },
                fail: () => {
                    reject();
                },
            });
        });
    },
};

export default logic;
