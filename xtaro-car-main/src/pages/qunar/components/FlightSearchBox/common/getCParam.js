import { getStorageSync, getSystemInfoSync } from "@tarojs/taro";
import React from "react";

const defaultVid = "91010000";
const vidMap = {
    wx: "71010001",
    ali: "72010001",
};
const PID = "10060";
const bdsourceMap = {
    wx: {
        bdsource: "smart_app",
    },
    ali: {
        bdsource: "ali",
    },
};

function getCParam(t) {
    const { user = {}, cookies } = getStorageSync("UserData") || {};
    const userName = (cookies?._q || "").replace(/^U\./, "");
    const env = process.env.ANU_ENV;
    const { openId = "" } = user;
    const { SDKVersion, model, version, system } = getSystemInfoSync() || {};
    const { bdsource } = bdsourceMap[env];
    const uid = `${bdsource}_${openId}`;
    const vid = vidMap[env] || defaultVid;
    return {
        // 端定义 hotel 定义的 client-type
        h_ct: "MINI",
        adid: "",
        brush: "",
        cas: "",
        catom: "",
        cid: bdsource,
        gid: uid,
        ke: "",
        lat: "",
        lgt: "",
        ma: "",
        mno: "",
        model,
        msg: "",
        nt: "",
        osVersion: system,
        pid: PID,
        ref: "",
        sid: "",
        t: t,
        un: userName,
        uname: userName,
        // 'usid': '', // userId，小程序暂时没有
        vid: vid,
        uid,
        sdkVersion: SDKVersion,
        wxVersion: version,
    };
}

export const FROM_MAPS = {
    wx: "WEIXIN_MINI_PROGRAM",
    ali: "ALIPAY_MINI_PROGRAM",
};

export default getCParam;
