import { View, Text, Image, RichText, Button } from "@tarojs/components";
import {
    getStorageSync,
    setStorageSync,
    navigateTo,
    showToast,
    setStorage,
} from "@tarojs/taro";
import React from "react";
import EventEmitter from "@/common/utils/EventEmitter";
import request from "@/common/utils/request.js";
import dateFormat from "@/common/utils/date/format";
import user from "@/common/utils/user";
import utils from "@/common/utils/util";
import login from "@/common/utils/login";
import log from "@/common/flight/log.js";
import Log from "@/common/utils/log";
import logic from "./common/logic";
import LogQmark from "@/common/utils/logQmark";
import newWatcher from "@/common/utils/watcher";
import subscribe from "@/common/utils/subscribe";
import preSearch from "../../pages/platform/indexWx/preSearch";
import getCParam, { FROM_MAPS } from "./common/getCParam";
import SubscribeMonitor from "./components/SubscribeMonitor/index";
import TopBanner from "./components/TopBanner/index";

import { parsePageQuery, getEntranceQconfig } from "@/common/flight/utils";
import "./index.scss";

const sendRequest = newWatcher.apiWatcher("flight");

const defaultVoucherIcon =
    "https://s.qunarzz.com/f_cms/2024/1706087891061_621481444.png";

// 顶部资源位关闭之后再次展示的时间间隔，前端写死，产品侧不会轻易改动,目前设定12个小时
const topBannerShowAgainTime = 12 * 60 * 60 * 1000;

const defaultQconfig = {
    wx: {
        inner_oneway_list: "wx",
        inner_oneway_ota: "wx",
        inner_oneway_booking: "wx",
        inner_roundway_list: "wx",
        inner_roundway_ota: "wx",
        inner_roundway_booking: "wx",
        inter_type: "new_touch",
    },
    ali: {
        inner_oneway_list: "ali",
        inner_oneway_ota: "ali",
        inner_oneway_booking: "ali",
        inner_roundway_list: "ali",
        inner_roundway_ota: "ali",
        inner_roundway_booking: "ali",
        inter_type: "new_touch",
    },
};

const qmarkData = {
    appcode: "nnc_module_qunar_flight",
    bizType: "flight",
    page: "flight_index",
    module: "flight_search",
};

const HOST = {
    FLIGHT: utils.getHost(),
    MP_FLIGHT: "mpflight.qunar.com",
};

class FlightSearchBox extends React.Component {
    constructor(props) {
        super(props);
        this.checkLogin();
        const query = parsePageQuery(props.query) || {};
        const initDate = logic.initDate(query);
        const initCity = logic.initCity(query);
        /**
         * 国际机票 AB 实验结果
         * @type {{
         *  240626_fl_gj_flight_ota_wx: 'A' | 'B' // 国际机票新版 search AB实验
         * }}
         *
         */
        this.abtest = {};

        this.state = {
            query,
            ...initDate,
            ...initCity,
            arrAirport: "",
            arrAirportCode: "",
            depAirport: "",
            depAirportCode: "",
            isLogin: props.isLoginQunar,
            isTestTheme: props.versionGroup === "A",
            entranceType: defaultQconfig[process.env.ANU_ENV],
            searchType:
                query.searchType === "oneway" || query.searchType === "roundway"
                    ? query.searchType
                    : "oneway", // oneway 单程， roundway 往返
            serviceList: ["低价保障", "出行保障", "售后保障"],
            subscribeInfo: {}, // 机票订阅消息,
            selectFlightCabin: getStorageSync("selectFlightCabin") || "economy", // economy 经济舱， bussiness 商务舱,
            flightRightsList: [],
            voucherInfo: {}, // 代金券文案和icon信息
            subscribeMonitorData: null, // 订阅监测信息
            // 顶部资源位数据
            topBannerResourceData: {},
            topBannerShow: false, // 控制顶部banner展示隐藏
        };

        this.handleActiveOneway = this.handleActiveOneway.bind(this);
        this.handleActiveMoreWay = this.handleActiveMoreWay.bind(this);
        this.handleSwipCity = this.handleSwipCity.bind(this);
        this.handleCityClick = this.handleCityClick.bind(this);
        this.handleDateClick = this.handleDateClick.bind(this);
        this.handleSelectInfo = this.handleSelectInfo.bind(this);
        this.handleSearchList = this.handleSearchList.bind(this);
        this.flightSubscribeMessage = this.flightSubscribeMessage.bind(this);
        this.handleChangeFlightCabin = this.handleChangeFlightCabin.bind(this);
        this.setDate = this.setDate.bind(this);
        this.setCity = this.setCity.bind(this);
        this.handleClickFlightRight = this.handleClickFlightRight.bind(this);
        this.getFlightMiniAppAB = this.getFlightMiniAppAB.bind(this);
        this.voucherList = []; // 代金券list
    }

    componentDidMount() {
        this._init();
        this.registerListeners();
        this.dispatchEvent();
        this.getFlightSubscribeInfo();
        this.requestFlightRights();
        this.requestFlightVoucherInfo();
        this.getSubscribeMonitorData();
        this.judgeLoadResourcePositionData();
        // 目前只有一个实验，实验下线，暂时先注释掉了，不请求
        // this.getFlightMiniAppAB();
    }

    componentWillReceiveProps(nextProps) {
        if (this.props.isLoginQunar !== nextProps.isLoginQunar) {
            this.setState({
                isLogin: nextProps.isLoginQunar,
            });
        }
    }

    dispatchEvent = () => {
        EventEmitter.dispatch("GET_DEPCITY", this.state.depCity);
    };

    registerListeners = () => {
        this.dateEvent = EventEmitter.addListener("CHOOSEDATE", this.setDate);
        this.cityEvent = EventEmitter.addListener("CITY_SELECT", this.setCity);
        //从个人中心切换回来后
        this.onShowEvent = EventEmitter.addListener(
            "FLIGHT_SEARCH_BOX",
            this.onShowEventHandler.bind(this),
        );
    };

    onShowEventHandler = async () => {
        await this.checkLogin();
        this.detectDomesticCoupon();
        this.requestFlightVoucherInfo();
        this.getSubscribeMonitorData();
        this.judgeLoadResourcePositionData();
    };

    /**
     * 机票首页 AB 实验
     */
    getFlightMiniAppAB = async () => {
        // 限制在 wx 小程序里请求，其他小程序请求 404
        if (process.env.ANU_ENV !== "wx") {
            return;
        }
        const storage = await utils.getGlobalInfoAsync();
        const { openId } = storage.user;
        request({
            service: "/sylas",
            method: "POST",
            param: {
                qrt: "flight_mini_app_ab",
                openId,
            },
            data: {
                openId,
            },
            ignoreStatus: true,
            success: (res) => {
                this.abtest = (res.data && res.data.abtest) || {};
            },
        });
    };

    // 判断是否请求资源位数据的方法
    judgeLoadResourcePositionData = () => {
        if (process.env.ANU_ENV !== "wx") return;
        const pageShowTimestamp = new Date().getTime();
        const closeTopBannerTimestamp = getStorageSync(
            "CloseTopBannerTimestamp",
        ); // 关闭顶部资源位的时间戳

        if (
            !closeTopBannerTimestamp ||
            (closeTopBannerTimestamp &&
                pageShowTimestamp - closeTopBannerTimestamp >=
                    topBannerShowAgainTime)
        ) {
            this.getResourcePositionData();
        }
    };

    _init = async () => {
        const sysInfo = await logic.getSystemInfo();
        // 开关配置信息
        const configRes = await getEntranceQconfig();
        const initData = logic.initData(sysInfo, this.state.env);

        if (configRes.data.ret && configRes.data.config) {
            let config = configRes.data.config[process.env.ANU_ENV];
            initData.entranceType = Object.assign(
                {},
                this.state.entranceType,
                config,
            );
        }

        this.setState(initData, () => {
            this.detectDomesticCoupon();
        });
    };

    requestFlightRights = () => {
        const { versionGroup } = this.props;
        request({
            service: "/gw/f/flight/wx/tabbar",
            host: "https://m.flight.qunar.com",
            method: "POST",
            data: {
                versionGroup,
            },
            ignoreStatus: true,
            success: (res) => {
                if (res.bstatus.code === 0 && res.data) {
                    this.setState({
                        flightRightsList: res.data.menuList || [],
                    });
                }
            },
            fail: () => {
                newWatcher.watchCount("get-flight-rights-fail");
            },
        });
    };

    // 获取阿波罗代金券信息，入参的三个字段虽然传了但是接口没有用
    requestFlightVoucherInfo = () => {
        // 代金券开关，该组件被多处应用
        if (!this.props.showVoucher) return;
        const { depCity, arrCity, goDate } = this.state;
        if (!depCity || !arrCity || !goDate) return;
        request({
            service: "/gw/f/flight/wx/home/<USER>",
            host: "https://m.flight.qunar.com",
            method: "POST",
            ignoreStatus: true,
            data: {
                depCity,
                arrCity,
                depDate: goDate,
            },
            success: (res) => {
                if (res.bstatus.code === 0 && res.data) {
                    let couponList = [],
                        voucherText = "",
                        voucherIcon = "";
                    if (res.data.bottomLineVO?.couponList) {
                        try {
                            couponList = JSON.parse(
                                res.data.bottomLineVO.couponList,
                            );
                            couponList = couponList.map((item = {}) => ({
                                ...item,
                                couponValue: {
                                    ...(item.couponValue || {}),
                                    formatValue:
                                        item.couponValue &&
                                        item.couponValue.number
                                            ? parseFloat(
                                                  item.couponValue.number,
                                              )
                                            : "",
                                },
                            }));
                        } catch (err) {
                            //
                        }
                        // 展示文案
                        voucherText = res.data.bottomLineVO.bottomLineText;
                        // icon
                        voucherIcon =
                            res.data.bottomLineVO.bottomLineImg ||
                            defaultVoucherIcon;
                    }
                    if (couponList.length > 0) {
                        this.voucherList = couponList;
                        this.setState({
                            voucherInfo: {
                                voucherText,
                                voucherIcon,
                            },
                        });
                    }
                }
            },
        });
    };

    getConfig = (key) => {
        return new Promise((resolve) => {
            sendRequest({
                service: "/mpx/getQconfig",
                resCheckField: "data",
                param: {
                    name: key + ".json",
                },
                success: (res) => {
                    resolve(res);
                },
            });
        });
    };

    // 机票订阅scene
    getFlightSubscribeInfo = async () => {
        // 订阅相关：订阅模板 + 后端返回标识
        const subscribeRes = (await this.getConfig("subScribe_indexWx")) || {};
        const { flightSubscribeScene = "" } = subscribeRes.data || {};
        const flightSubscribeList =
            await this.getSubscribeList(flightSubscribeScene);
        const isSubscribePop = await this.subscribeCanPop(flightSubscribeScene);
        this.setState({
            subscribeInfo: {
                scene: flightSubscribeScene,
                list: flightSubscribeList,
                isSubscribePop,
            },
        });
    };

    // 获取订阅列表
    getSubscribeList = async (scene) => {
        const listRes = await subscribe.querySubscribeList(scene);
        if (
            listRes &&
            listRes.ret &&
            listRes.data &&
            listRes.data.templateInfoList &&
            listRes.data.templateInfoList.length > 0
        ) {
            return listRes;
        }
        return;
    };

    //订阅弹窗是否弹出
    subscribeCanPop = async (scene) => {
        return new Promise(async (resolve) => {
            const storage = await utils.getGlobalInfoAsync();
            const { openId } = storage.user;
            sendRequest({
                service: "/gw/m/api/subscribe/can/pop",
                host: "https://m.flight.qunar.com",
                param: {
                    biz: this.props.bizType || "flight",
                    openId,
                    scene,
                },
                success: (response) => {
                    const { status, data } = response;
                    if (status === 0) {
                        resolve(data);
                    }
                },
                fail: () => {
                    resolve(false);
                },
            });
        });
    };

    componentWillUnmount() {
        this.dateEvent && this.dateEvent.removeListener("CHOOSEDATE");
        this.cityEvent && this.cityEvent.removeListener("CITY_SELECT");
        this.onShowEvent &&
            this.onShowEvent.removeListener("FLIGHT_SEARCH_BOX");
    }
    // 我的订单 跳平台的小程序的订单页 storage是为了能够做出筛选只机票
    orderClick = () => {
        setStorageSync("my_page_order_filter", "flight");
        navigateTo({
            url: "/pages/qunar/subPages/orderList/orderList/index",
        });
    };

    setCity = (cityData) => {
        this.citySelectShow = true;
        const obj = logic.setCity(
            cityData,
            this.state.depCityType,
            this.state.arrCityType,
        );
        this.setState(obj, () => {
            this.detectDomesticCoupon();
            this.dispatchEvent();
        });

        setTimeout(() => {
            this.citySelectShow = false;
        }, 1000);
    };

    detectDomesticCoupon = async () => {
        if (
            process.env.ANU_ENV !== "wx" &&
            process.env.ANU_ENV !== "qq" &&
            process.env.ANU_ENV !== "ali"
        ) {
            return;
        }
        const { depCityType, arrCityType } = this.state;
        const domesticSign = "inner";
        let btnCouponType = "";
        let btnCouponCont = "";
        if (depCityType === domesticSign && arrCityType === domesticSign) {
            await logic.checkLogin();
            if (logic.loginStatus) {
                btnCouponType = "custom";
                const queryData = await this.queryCouponData();
                const { voucher = {} } = queryData.data || {};
                btnCouponCont = voucher.voucherText || "";
            } else if (process.env.SKIP !== "WX_DEAL") {
                // 马甲小程序去掉用户登录的引导
                btnCouponType = "guide";
                btnCouponCont = "新人登录有礼";
            }
        } else {
            btnCouponType = "none";
        }

        if (
            btnCouponType !== this.state.btnCouponType &&
            btnCouponCont !== this.state.btnCouponCont
        ) {
            this.setState(
                {
                    btnCouponType,
                    btnCouponCont,
                },
                () => {
                    this.popCouponLog(btnCouponType, btnCouponCont, "show");
                },
            );
        }
    };

    popCouponLog = (btnCouponType, btnCouponCont, type) => {
        const p = "mini_home";
        const rf = process.env.ANU_ENV;
        if (btnCouponType === "custom") {
            if (btnCouponCont) {
                // 已登录且有券
                log({ p, r: `btn_coupon_custom_${type}`, rf });
            } else {
                // 已登录无券
                log({ p, r: `btn_coupon_custom_empty_${type}`, rf });
            }
        } else if (btnCouponType === "guide") {
            // 未登录
            log({ p, r: `btn_coupon_not_login_${type}`, rf });
        }
    };

    // 查询是否有可用券
    queryCouponData = () => {
        return new Promise((resolve, reject) => {
            request({
                host: "https://m.flight.qunar.com",
                service: "/flight/api/miniappHomeVoucher",
                method: "POST",
                ignoreStatus: true,
                success: (res) => {
                    resolve(res);
                },
            });
        });
    };

    setDate = (date) => {
        log({
            p: "mini_home",
            r: "choose_date",
            date: JSON.stringify(date),
        });
        const obj = logic.setDate(date, this.state.goDate, this.state.backDate);
        this.setState(obj);
    };

    clickSearchBtn = (e) => {
        Log({
            info: {
                area: "flightSearch",
                act: "click",
                type: "homeEnter",
                name: "flightSearch",
            },
        });

        if (this.state.isLogin) {
            this.handleSearchList();
            log({
                p: "mini_home",
                r: "click_search_list",
                rf: process.env.ANU_ENV,
            });
        } else {
            this.getPhoneNumber(e);
            log({
                p: "mini_home",
                r: "click_go_login",
                rf: process.env.ANU_ENV,
            });
        }

        log({
            p: "mini_home",
            r: "search_click",
        });
    };

    // 订阅消息
    flightSubscribeMessage = () => {
        return new Promise(async (resolve) => {
            const { subscribeInfo } = this.state;
            const { list, isSubscribePop } = subscribeInfo;
            if (!list) {
                resolve({
                    status: "1",
                    msg: "获取订阅列表失败",
                });
                return;
            }
            if (isSubscribePop) {
                subscribe.requestSubscribeMessage(
                    list,
                    () => {
                        LogQmark({
                            ...qmarkData,
                            operType: "req",
                            page: "homeEnter",
                            id: "flight_subscribe_success",
                        });
                        resolve({
                            status: 0,
                            msg: "订阅消息成功",
                        });
                    },
                    () => {
                        LogQmark({
                            ...qmarkData,
                            operType: "req",
                            page: "homeEnter",
                            id: "flight_subscribe_fail",
                        });
                        resolve({
                            status: 1,
                            msg: "订阅消息失败",
                        });
                    },
                );
            } else {
                resolve({
                    status: "1",
                    msg: "",
                });
            }
        });
    };
    // 搜索点击
    handleSearchList = async () => {
        const {
            isLogin,
            entranceType,
            lastTap,
            searchType,
            depCity,
            arrCity,
            goDate,
            backDate,
            depAirport,
            depAirportCode,
            arrAirport,
            arrAirportCode,
        } = this.state;
        if (isLogin) {
            await this.flightSubscribeMessage(); //调用订阅
        }
        const now = new Date().getTime();
        if (now - lastTap < 500) {
            return;
        }
        this.setState({
            lastTap: now,
        });
        let { isInner } = this.state || {};
        if (!logic.checkCity(depCity, arrCity)) {
            return;
        }

        const specialCity = [
            "香港",
            "澳门",
            "台北",
            "高雄",
            "台南",
            "台中",
            "台东",
            "嘉义",
            "花莲",
        ];
        if (
            specialCity.indexOf(depCity) > -1 ||
            specialCity.indexOf(arrCity) > -1
        ) {
            isInner = false;
            if (process.env.ANU_ENV === "qq" || process.env.ANU_ENV === "ali") {
                showToast({
                    icon: "none",
                    title: "暂无法查询港澳台与国际机票",
                });
                return;
            }
        }

        const storage = await utils.getGlobalInfoAsync();
        const { cookies } = storage;

        const params = logic.getParams(
            isInner,
            searchType,
            {
                depAirport: depAirportCode || depAirport,
                arrAirport: arrAirportCode || arrAirport,
                depCity,
                arrCity,
                goDate,
                backDate,
            },
            entranceType,
        );
        if (!params) {
            return;
        }
        // 传入的优先级最高，说明上一级希望设置
        params.from =
            this.state.query.from ||
            (process.env.ANU_ENV === "wx"
                ? "wx_app_qunar"
                : cookies.bd_source || "");
        params.bd_source =
            this.state.query.bd_source ||
            (process.env.ANU_ENV === "wx" ? "wx_app" : cookies.bd_source || "");
        params.mptoken = cookies.openId || "|";
        // 强登处理
        params.isForceLogin =
            process.env.SKIP === "WX_DEAL"
                ? entranceType.is_force_login_deal
                : entranceType.is_force_login;
        // 马甲小程序，url参数中增加此字段
        if (process.env.SKIP === "WX_DEAL") {
            params.wxSource = "wxDeal";
        }

        // 屏蔽其他小程序，其他小程序舱位不限
        if (process.env.ANU_ENV === "wx") {
            // 选择【经济舱】时不做舱位筛选
            params.cabinType = this.getCabinType();
        }

        const url = this.getJumpUrl(isInner, searchType, entranceType);

        // 国内单程跳转到原生list
        if (
            isInner &&
            searchType === "oneway" &&
            entranceType.inner_oneway_list &&
            entranceType.inner_oneway_list === process.env.ANU_ENV
        ) {
            preSearch.dispatchCurrentPreSearchResult();

            navigateTo({
                url: "/pages/flight/list/index?data=" + JSON.stringify(params),
            });
            return;
        }
        // TTI 计算开始时间
        params.startTime = Date.now();
        // 国际机票小程序数据串联，小程序渠道标识
        params.interFlightWxSource = "interFlightWx";
        params.startFrom = "home";
        if (params.flightType === "oneWay" && process.env.ANU_ENV === "wx") {
            try {
                const configRes = await getEntranceQconfig();
                const expire =
                    configRes.data?.config?.wx?.inter_location_expire;
                if (expire) {
                    let location_info = getStorageSync("location_info") || "{}";
                    location_info = JSON.parse(location_info);
                    const nowTimestamp = new Date().getTime();
                    if (
                        nowTimestamp - location_info.lastTime <=
                        expire * 60 * 60 * 1000
                    ) {
                        params.lat = location_info.lat;
                        params.lgt = location_info.lng;
                    }
                }
            } catch (e) {
                LogQmark({
                    module: "default",
                    page: "webview",
                    id: "location_error",
                    ext: { error: e },
                });
            }
            const queryStr = Object.keys(params).reduce((queryStr, key) => {
                return `${queryStr}${key}=${params[key]}&`;
            }, "");
            navigateTo({
                url: `/pages/gflight/view/list/index?${queryStr.slice(0, -1)}`,
            });
        } else {
            utils.openWebview({
                url,
                params,
                loginSync: true,
            });
        }

        const { btnCouponType, btnCouponCont } = this.state;
        this.popCouponLog(btnCouponType, btnCouponCont, "click");
    };

    getJumpUrl = (isInner, searchType, entranceType) => {
        let url = "";
        if (!isInner) {
            // 国际
            url =
                entranceType.inter_type === "new_touch"
                    ? `${HOST.FLIGHT}/ncs/page/interlist`
                    : `https://${HOST.MP_FLIGHT}/inter/flight_list`;
        } else {
            // 国内
            if (searchType === "roundway") {
                // 往返
                url =
                    entranceType.inner_roundway_list === "new_touch"
                        ? `${HOST.FLIGHT}/ncs/page/flightDoubleList`
                        : `https://${HOST.MP_FLIGHT}/ncs/page/flightlist`;
            } else {
                // 单程
                url =
                    entranceType.inner_oneway_list === "new_touch"
                        ? `${HOST.FLIGHT}/ncs/page/flightlist`
                        : `https://${HOST.MP_FLIGHT}/ncs/page/flightlist`;
            }
        }
        return url;
    };

    // 用于在 searchBox 组件唤起中间页登录，点击中间页"登录"按钮之前，获取到用户在机票首页填写的信息
    handleSelectInfo = async () => {
        const {
            entranceType,
            lastTap,
            isInner,
            searchType,
            depCity,
            arrCity,
            goDate,
            backDate,
        } = this.state;
        const now = new Date().getTime();
        if (now - lastTap < 500) {
            return;
        }
        this.setState({
            lastTap: now,
        });
        if (!logic.checkCity(depCity, arrCity)) {
            return;
        }
        const storage = await utils.getGlobalInfoAsync();
        const { cookies } = storage;

        const params = logic.getParams(
            isInner,
            searchType,
            { depCity, arrCity, goDate, backDate },
            entranceType,
        );
        if (!params) {
            return;
        }
        // 传入的优先级最高，说明上一级希望设置
        params.from =
            this.state.query.from ||
            (process.env.ANU_ENV === "wx"
                ? "wx_app_qunar"
                : cookies.bd_source || "");
        params.bd_source =
            this.state.query.bd_source ||
            (process.env.ANU_ENV === "wx" ? "wx_app" : cookies.bd_source || "");
        params.mptoken = cookies.openId || "|";
        // 马甲小程序，url参数中增加此字段
        if (process.env.SKIP === "WX_DEAL") {
            params.wxSource = "wxDeal";
        }

        // 屏蔽其他小程序，其他小程序舱位不限
        if (process.env.ANU_ENV === "wx") {
            // 选择【经济舱】时不做舱位筛选
            params.cabinType = this.getCabinType();
        }

        const url = this.getJumpUrl(isInner, searchType, entranceType);

        this.setState({
            jumpToListInfo: {
                isInner,
                searchType,
                entranceType,
                url,
                params,
            },
        });
    };

    checkLogin = async () => {
        const res = await user.checkLogin();
        if (res.ret && res.data.isLogin) {
            this.setState({
                isLogin: true,
                getLoginStatus: true,
            });
        } else {
            this.setState({
                isLogin: false,
                getLoginStatus: true,
            });
        }
    };

    getPhoneNumber = async (e) => {
        let detail = e.detail;
        const env = process.env.ANU_ENV;
        if (env === "wx" && e?.detail?.errno === 1400001) {
            LogQmark({
                page: "login",
                module: "login",
                operType: "click",
                id: "login_platform_no_money",
            });
            showToast({
                icon: "none",
                title: "请稍后再试",
                duration: 1000,
            });
            return;
        }
        if (detail.iv && process.env.ANU_ENV !== "qq") {
            // 用户允许授权手机号
            let source = "wx_global_unknown";
            let loginByWxPhoneRes = await user.loginByWxPhone({
                encryptedData: detail.encryptedData,
                iv: detail.iv,
                source: source,
            });
            if (loginByWxPhoneRes && loginByWxPhoneRes.ret) {
                // 登录成功  在本地记住登录状态，并直接跳转到 list 页
                this._init();
                this.handleSearchList();
                user.updateUserInfo({
                    user: {
                        logOut: false,
                    },
                });
                this.setState({
                    isLogin: true,
                });
            } else {
                showToast({
                    icon: "none",
                    title: loginByWxPhoneRes
                        ? loginByWxPhoneRes.errMsg || "登录失败"
                        : "登录失败",
                    duration: 1000,
                });
            }
        } else {
            const { entranceType } = this.state;
            const isForceLogin =
                process.env.SKIP === "WX_DEAL"
                    ? entranceType.is_force_login_deal
                    : entranceType.is_force_login;
            // 用户点击拒绝使用微信登录，就转入中间页登录
            if (isForceLogin) {
                login(
                    () => {
                        this._init();
                        this.handleSearchList();
                        this.setState({
                            isLogin: true,
                        });
                    },
                    {
                        source: "flight_home",
                    },
                );
            } else {
                this._init();
                this.handleSearchList();
                this.setState({
                    isLogin: false,
                });
            }
        }
    };

    timeStampVerify = (now, that = this) => {
        Log({
            info: {
                area: "flightSearch",
                act: "click",
                type: "homeEnter",
                name: "flightSearch",
            },
        });
        return now - that.state.lastTap < 500;
    };

    /** 单程 */
    handleActiveOneway = () => {
        const now = new Date().getTime();
        if (this.timeStampVerify(now)) {
            return;
        }
        this.setState({
            searchType: "oneway",
            lastTap: now,
        });

        // 添加预搜数据
        preSearch.setSearchType("oneway", "ONEWAY_TAB_ACTION");

        log({
            p: "mini_home",
            r: "oneWay_click",
        });
    };

    /** 往返 */
    handleActiveMoreWay = () => {
        const now = new Date().getTime();
        if (this.timeStampVerify(now)) {
            return;
        }

        const oneDay = 86400000;
        const obj = {
            searchType: "roundway",
            lastTap: now,
        };
        const goDate = new Date(this.state.goDate.replace(/-/g, "/"));
        let backDate = new Date(this.state.backDate.replace(/-/g, "/"));
        /** 出发日期大于返程日期，则将返程日期改成出发日期后三天。一般在往返之后再单程修改出发日期的情况 */
        if (backDate.getTime() < goDate.getTime()) {
            backDate = new Date(goDate.getTime() + 2 * oneDay);
            obj.backDate = dateFormat(backDate, "yyyy-mm-dd");
            obj.backWeek = dateFormat(backDate, "周w");
            obj.displayBackMonth = dateFormat(backDate, "m");
            obj.displayBackDate = dateFormat(backDate, "d");
            obj.days = 3;
            setStorage({
                key: "FlightBackDate",
                data: backDate.getTime(),
            });
        }
        this.setState(obj);

        // 添加预搜数据
        preSearch.setSearchType("roundway", "ROUND_TAB_ACTION");

        log({
            p: "mini_home",
            r: "roundWay_click",
        });
    };

    // 城市选择
    handleCityClick = (e) => {
        const now = new Date().getTime();
        if (this.timeStampVerify(now)) {
            return;
        }
        this.setState({
            lastTap: now,
        });
        const dataSet = e.currentTarget.dataset,
            type = dataSet.type,
            cityType = dataSet.citytype,
            isDep = type === "depCity",
            isInner = cityType === "inner"; // 判断是国际城市还是国内城市，用户城市选择页中的tab状态
        const params = {
            eventType: "CITY_SELECT",
            isDep, // 判断是否是出发城市选择 城市选择组件和城市名一起透传回来
            isInner,
        };
        preSearch.addUserActionRouteNameExclude("home_to_city_page");
        navigateTo({
            url:
                "/pages/flight/citySelect/index?data=" + JSON.stringify(params),
        });

        log({
            p: "mini_home",
            r: "city_choose_click",
        });
    };

    // 日期选择
    handleDateClick = (e) => {
        const now = new Date().getTime();
        const that = this;
        if (this.timeStampVerify(now, that)) {
            return;
        }
        that.setState({
            lastTap: now,
        });
        const dataSet = e.currentTarget.dataset,
            type = dataSet.type,
            isGoDate = type === "goDate" ? 0 : 1;

        const params = {
            eventType: "CHOOSEDATE", // 选择日期成功之后触发的事件，通过监听这个事件来得到响应
            isRoundWay: that.state.searchType === "roundway", // 单程/往返
            calendarDays: 365, // 日历时间跨度，从当前时间往后算
            currentSelectStatus: isGoDate, // 当前要选择的日期类型，0为选择去程时间，1为选择返程时间（选择往返时间可用，单程不传）
            startDay: that.state.goDate, // 默认去程时间
            endDay: that.state.backDate, // 默认返程时间
            depCity: that.state.depCity, // 出发城市
            arrCity: that.state.arrCity, // 到达城市
            // isGoDate: isGoDate // 判断是否是出发日期 日历组件和日期一起透传回来
        };
        preSearch.addUserActionRouteNameExclude("home_to_date_page");
        navigateTo({
            url: "/pages/flight/calendar/index?data=" + JSON.stringify(params),
        });
        log({
            p: "mini_home",
            r: "date_choose_click",
        });
    };

    /** 交换出发城市和目的城市 */
    handleSwipCity = () => {
        const now = new Date().getTime();
        if (this.timeStampVerify(now)) {
            return;
        }

        if (this.state.exchangeStatus) {
            return;
        }
        this.setState({
            exchangeStatus: true,
        });

        const {
            depCity,
            arrCity,
            depCityType,
            arrCityType,
            arrAirport,
            arrAirportCode,
            depAirport,
            depAirportCode,
        } = this.state;
        setTimeout(() => {
            this.setState(
                {
                    exchangeStatus: false,
                    depCity: arrCity,
                    arrCity: depCity,
                    depAirportCode: arrAirportCode,
                    depAirport: arrAirport,
                    arrAirportCode: depAirportCode,
                    arrAirport: depAirport,
                    depCityType: arrCityType,
                    arrCityType: depCityType,
                    lastTap: now,
                },
                () => {
                    this.dispatchEvent();

                    // 更新预搜数据
                    preSearch.setCityData(
                        {
                            depCity: arrCity,
                            arrCity: depCity,
                            depAirportCode: arrAirportCode,
                            depAirport: arrAirport,
                            arrAirportCode: depAirportCode,
                            arrAirport: depAirport,
                            depCityType: arrCityType,
                            arrCityType: depCityType,
                        },
                        "CHANGE_CITY_ACTION",
                    );
                },
            );
        }, 300);
        setStorage({
            key: "FlightDepCity",
            data: arrCity,
        });
        setStorage({
            key: "FlightArrCity",
            data: depCity,
        });
        setStorage({
            key: "FlightDepType",
            data: arrCityType,
        });
        setStorage({
            key: "FlightArrType",
            data: depCityType,
        });

        log({
            p: "mini_home",
            r: "swip_city_click",
        });
    };

    getCabinType = () => {
        // 选择【经济舱】时不做舱位筛选
        return this.state.selectFlightCabin === "economy" ? 0 : 1;
    };

    handleChangeFlightCabin = (e) => {
        const dataSet = e.currentTarget.dataset,
            type = dataSet.type;
        this.setState({
            selectFlightCabin: type,
            changeFlightCabin: true,
        });

        setStorageSync("selectFlightCabin", type);
        log({
            p: "mini_home",
            r: "change_flight_cabin",
            type,
        });
    };

    handleClickFlightRight = (e) => {
        const scheme =
            e.currentTarget.dataset && e.currentTarget.dataset.scheme;
        const text = e.currentTarget.dataset && e.currentTarget.dataset.text;
        log({
            p: "mini_home",
            r: "flight_right_click",
            text,
        });
        navigateTo({
            url: scheme,
        });
    };

    // 打开代金券弹窗
    openVoucherModal = () => {
        if (this.voucherList.length > 0) {
            this.props.openVoucherModal({
                voucherList: this.voucherList,
            });
        }
        log({
            p: "mini_home",
            r: "flight_home_voucher_click",
        });
    };

    // 获取订阅监测接口数据
    getSubscribeMonitorData = () => {
        const ANU_ENV = process.env.ANU_ENV;
        if (ANU_ENV !== "wx") return;
        request({
            host: utils.getHost(),
            service: "/gw/f/flight/homepage/subscribe/touch",
            method: "POST",
            data: {
                ...getCParam("f_flight_homepage_subscribe_touch"),
                from: FROM_MAPS[ANU_ENV] || "",
                cat: "wx-flight-home-banner", // 机票首页banner位置
            },
            ignoreStatus: true,
            success: (res) => {
                if (
                    res &&
                    typeof res === "object" &&
                    Object.keys(res).length > 0
                ) {
                    this.setState({
                        subscribeMonitorData: res,
                    });
                } else {
                    this.setState({
                        subscribeMonitorData: null,
                    });
                }
            },
            fail: (error) => {
                this.setState({
                    subscribeMonitorData: null,
                });
            },
            complete: () => {},
        });
    };

    // 获取资源位数据方法
    getResourcePositionData = () => {
        logic
            .getResourcePositionData("wxBizTop")
            .then((res) => {
                this.setState({
                    topBannerResourceData: res || {},
                    topBannerShow: !!res,
                });
            })
            .catch(() => {
                this.setState({
                    topBannerResourceData: {},
                    topBannerShow: false,
                });
            });
    };

    // 控制顶部资源位显示隐藏的方法
    toggleTopBanner = () => {
        this.setState(
            {
                topBannerShow: !this.state.topBannerShow,
            },
            () => {
                // 顶部资源位关闭，暂存关闭的时间戳
                if (!this.state.topBannerShow) {
                    setStorage({
                        key: "CloseTopBannerTimestamp",
                        data: new Date().getTime(),
                    });
                }
            },
        );
    };

    render() {
        return (
            <View className="flight-search-wrapper">
                <View
                className={
                    this.state.isTestTheme
                        ? "home-search-wrapper home-search-test"
                        : "home-search-wrapper"
                }
            >
                <View className="flight-search">
                    {this.state.topBannerShow && (
                        <TopBanner
                            dataSource={this.state.topBannerResourceData}
                            onClose={this.toggleTopBanner}
                        />
                    )}
                    <View className="tabItemList">
                        <View
                            className={
                                this.state.searchType === "oneway"
                                    ? "tabItem on"
                                    : "tabItem"
                            }
                            onClick={this.handleActiveOneway}
                        >
                            <View className="tabItem-box">
                                <Text className="tabItem-world">单程</Text>
                                <View className="bottom-line" />
                            </View>
                        </View>
                        <View className="line" />
                        <View
                            className={
                                this.state.searchType === "roundway"
                                    ? "tabItem on"
                                    : "tabItem"
                            }
                            onClick={this.handleActiveMoreWay}
                        >
                            <View className="tabItem-box">
                                <Text className="tabItem-world">往返</Text>
                                <View className="bottom-line" />
                            </View>
                        </View>
                    </View>

                    <View className="searchCity">
                        <View
                            className="select-left-city city-select-item"
                            onClick={this.handleCityClick}
                            data-type="depCity"
                            data-citytype={this.state.depCityType}
                        >
                            <Text
                                className={
                                    "cityName " +
                                    (this.state.exchangeStatus
                                        ? "depCity-changing"
                                        : "")
                                }
                            >
                                {this.state.depAirport === ""
                                    ? this.state.depCity
                                    : this.state.depAirport}
                            </Text>
                        </View>
                        <Text className="swap" onClick={this.handleSwipCity} />
                        <View
                            className="select-right-city city-select-item"
                            onClick={this.handleCityClick}
                            data-type="arrCity"
                            data-citytype={this.state.arrCityType}
                        >
                            <Text
                                className={
                                    "cityName " +
                                    (this.state.exchangeStatus
                                        ? "arrCity-changing"
                                        : "")
                                }
                            >
                                {this.state.arrAirport === ""
                                    ? this.state.arrCity
                                    : this.state.arrAirport}{" "}
                            </Text>
                        </View>
                    </View>
                    <View className="city-bottom-line" />
                    <View className="searchDate">
                        <View
                            className="select-l"
                            onClick={this.handleDateClick}
                            data-type="goDate"
                        >
                            <View className="day-date">
                                <Text>{this.state.displayGoMonth}</Text>
                                <Text className="day-date-text">月</Text>
                                <Text>{this.state.displayGoDate}</Text>
                                <Text className="day-date-text">日</Text>
                            </View>
                            <Text className="week-date">
                                {this.state.goDateTip
                                    ? this.state.goDateTip
                                    : this.state.goWeek}
                            </Text>
                        </View>
                        <View
                            className={
                                this.state.searchType === "roundway"
                                    ? "days"
                                    : "hide"
                            }
                        >
                            <View className="days_count">
                                {this.state.days}
                            </View>
                            天
                        </View>
                        <View
                            className={
                                this.state.searchType === "roundway"
                                    ? "select-r"
                                    : "hide"
                            }
                            onClick={this.handleDateClick}
                            data-type="backDate"
                        >
                            <View className="day-date">
                                <Text>{this.state.displayBackMonth}</Text>
                                <Text className="day-date-text">月</Text>
                                <Text>{this.state.displayBackDate}</Text>
                                <Text className="day-date-text">日</Text>
                            </View>
                            <Text className="week-date">
                                {this.state.backDateTip
                                    ? this.state.backDateTip
                                    : this.state.backWeek}
                            </Text>
                        </View>
                    </View>
                    {process.env.ANU_ENV === "wx" && (
                        <View className="city-bottom-line" />
                    )}
                    {process.env.ANU_ENV === "wx" && (
                        <View className="select-flight-cabin">
                            <View
                                className={
                                    this.state.selectFlightCabin === "economy"
                                        ? "economy select"
                                        : "economy"
                                }
                                data-type="economy"
                                onClick={this.handleChangeFlightCabin}
                            >
                                经济舱
                            </View>
                            <View
                                className={
                                    this.state.selectFlightCabin === "bussiness"
                                        ? "bussiness select"
                                        : "bussiness"
                                }
                                data-type="bussiness"
                                onClick={this.handleChangeFlightCabin}
                            >
                                公务/商务舱
                            </View>
                            <View
                                className={
                                    this.state.selectFlightCabin === "bussiness"
                                        ? "bussiness-float-box"
                                        : "economy-float-box"
                                }
                                style={
                                    this.state.changeFlightCabin
                                        ? "transition: 0.2s"
                                        : ""
                                }
                            ></View>
                        </View>
                    )}
                    {/* 代金券提示条-仅在微信小程序展示 && 已登录状态 && 国内机票 && 有代金券数据*/}
                    {process.env.ANU_ENV === "wx" &&
                        this.state.isLogin &&
                        this.state.isInner &&
                        this.state.voucherInfo.voucherText && (
                            <View
                                className="voucher-guide"
                                onClick={this.openVoucherModal}
                            >
                                <Image
                                    src={this.state.voucherInfo.voucherIcon}
                                    className="icon"
                                />
                                <RichText
                                    nodes={this.state.voucherInfo.voucherText}
                                ></RichText>
                                <Text className="flight-icon more-arrow">
                                    
                                </Text>
                            </View>
                        )}
                    <View className="submit">
                        {/*支付宝小程序基础库版本较低，不支持onGetphonenumber api，会有点击无反应的问题*/}
                        {this.state.isLogin ||
                        process.env.ANU_ENV === "ali" ||
                        process.env.SKIP === "WX_DEAL" ? (
                            <View
                                className="search-btn"
                                onClick={this.clickSearchBtn}
                            >
                                {this.state.btnCouponType &&
                                this.state.btnCouponType !== "none" &&
                                this.state.btnCouponCont ? (
                                    <Text className="pop-coupon">
                                        {this.state.btnCouponCont}
                                    </Text>
                                ) : null}

                                <Text>机票查询</Text>
                            </View>
                        ) : (
                            <Button
                                className="search-btn"
                                onGetphonenumber={this.clickSearchBtn}
                                open-type="getPhoneNumber"
                            >
                                {this.state.btnCouponType &&
                                this.state.btnCouponType !== "none" &&
                                this.state.btnCouponCont ? (
                                    <Text className="pop-coupon">
                                        {this.state.btnCouponCont}
                                    </Text>
                                ) : null}

                                <Text>机票查询</Text>
                            </Button>
                        )}
                    </View>
                    <View className="search-btn-shadow" />
                    {this.props.showOrder ? (
                        <View className="order-line">
                            <View
                                className="order"
                                onClick={this.orderClick.bind(this)}
                            >
                                <Text className="flight-icon order-icon">
                                    
                                </Text>
                                <Text
                                    className="order-tip"
                                    style={
                                        !this.state.isIOS
                                            ? "margin-top:1px"
                                            : ""
                                    }
                                >
                                    我的订单
                                </Text>
                            </View>
                        </View>
                    ) : null}
                </View>
                {process.env.ANU_ENV === "wx" &&
                    this.state.flightRightsList.length && (
                        <View className="flight-rights-container">
                            <View className="flight-rights">
                                {this.state.flightRightsList.map(
                                    (item, index) => {
                                        return (
                                            <View
                                                className="flight-right"
                                                key={index}
                                                onClick={
                                                    this.handleClickFlightRight
                                                }
                                                data-scheme={item.scheme}
                                                data-text={item.text}
                                            >
                                                <Image
                                                    src={item.imageUrl}
                                                    className="flight-right-image"
                                                ></Image>
                                                <Text className="flight-right-text">
                                                    {item.text}
                                                </Text>
                                            </View>
                                        );
                                    },
                                )}
                            </View>
                            {/* 阴影 */}
                            <Image
                                className="search-box-shadow"
                                src="https://s.qunarzz.com/f_cms/2024/1709711263238_397091653.png"
                            />
                        </View>
                    )}

                {/* 订阅监测 */}
                {process.env.ANU_ENV === "wx" &&
                    this.state.subscribeMonitorData && (
                        <SubscribeMonitor
                            data={this.state.subscribeMonitorData}
                            isTestTheme={this.state.isTestTheme}
                        />
                    )}
            </View>
        </View> 
        );
    }
}

export default FlightSearchBox;
