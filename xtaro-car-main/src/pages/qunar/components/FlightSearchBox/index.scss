
@font-face {
    font-family: 'flight-iconfont';
    src: url('https://s.qunarzz.com/nnc_flight/fonts/0.0.26/nnc_flight.ttf');
}

.flight-icon {
    font-family: 'flight-iconfont' !important;
    font-size: 12px;
    font-style: normal;
}

.flight-search-wrapper{

.home-search-wrapper{
    background-color: #f5f6f9;
    overflow: hidden;

    .flight-search {
        margin: 8px 8px 12px;
        font-family: PingFangSC-Light;
        background-image: linear-gradient(180deg, #f5f6f9 0%, #FFFFFF 45%);
        border-radius: 16px;
        padding-bottom: 5px;
    }

    .hide{
        display: none;
    }
    .tabItemList{
        display: flex;
        margin-bottom: 13px;
        width: 100%;
        .tabItem{
            font-family: PingFangSC-Light;
            font-size: 16px;
            color: #999999;
            letter-spacing: 0;
            display: inline-block;
            text-align: center;
            flex: 0.5;
            color: #616161;
            padding: 8px 0;
            &.on{
                font-family: PingFangSC-Semibold;
                color: #222222;
                font-weight: 600;
                .tabItem-box {
                    position: relative;
                }

                .tabItem-world {
                    position: relative;
                    z-index: 2;
                }

                .bottom-line{
                    position: absolute;
                    bottom: 1px;
                    z-index: 1;
                    width: 28px;
                    height: 6px;
                    border-radius: 6px;
                    background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
        }
        .line{
            width: 1px;
            height: 16px;
            background: #E6E6E6;
            margin-top: 11px;
        }
    }

    .city-bottom-line {
        width: 311px;
        height: 0.5px;
        background-color: #F2F2F2;
        margin: 0 auto;
    }
    .searchCity {
        position: relative;
        justify-content: space-between;
    }
    .searchCity,.searchDate{
        display: flex;
        align-items: center;
        padding: 0 24px;

        .days_count {
            font-size: 16px;
            font-family: hotel_rn_num;
        }
        .days{
            display: flex;
            text-align: center;
            font-size: 14px;
            color: #999;
            margin-top: 6px;
            align-items: baseline;
        }
        .swap{
            background-image: url('https://picbed.qunarzz.com/6797b7f943c9b46c09756664b43d1964.png');
            background-size: 100%;
            width: 35px;
            height: 35px;
            position: absolute;
            left: 50%;
            bottom: 10px;
            transform: translateX(-50%);
        }
        .city-select-item{
            width: 39%;
            font-size: 22px;
            color: #222222;
            font-family: PingFangSC-Semibold;
            line-height: 35px;
            height: 35px;
            white-space: nowrap;
            font-weight: 600;
            margin-bottom: 9px;
            .cityName{
                font-weight: 700;
                display: block;
                font-size: 22px;
                white-space:nowrap;
                text-overflow:ellipsis;
                overflow:hidden;
            }
            .depCity-changing {
                -webkit-transition: -webkit-transform 1.5s ease;
                transition: transform 1.5s ease;
                -webkit-transform: translateX(200%);
                transform: translateX(200%);
                text-align: right;
            }
            .arrCity-changing {
                -webkit-transition: -webkit-transform 1.5s ease;
                transition: transform 1.5s ease;
                -webkit-transform: translateX(-200%);
                transform: translateX(-200%);
                text-align: left;
            }
        }
        .select-right-city {
            text-align: right;
        }
        .select-l,.select-r{
            flex: 0.5;
        }
        .select-r{
            float: right;
            text-align: right;
        }
    }
    .searchDate{
        margin: 10px auto 19px auto;
        font-size: 18px;
        color: #212121;

        .week-date{
            font-size: 14px;
            display: inline-block;
            margin-left: 4px;
            color: #666666;;
        }

        .day-date {
            font-family: "hotel_rn_num";
            font-size: 24px;
            color: #222222;
            letter-spacing: 0;
            font-weight: 700;
            display: inline-flex;
        }

        .day-date-text {
            font-family: PingFangSC-Regular;
            font-size: 22px;
        }

    }
    .home-condition-selection{
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 60px;
        padding: 0 20px;
        .cabin-wrapper{
            font-family: PingFangSC-Regular;
            height: 25px;
            line-height: 25px;
            font-size: 18px;
            color: #BDBDBD;
        }
        .cabin-selected{
            color:#212121;
        }
        .a2c-wrapper{
            display: flex;
            align-items: center;
        }
    }
    .select-flight-cabin{
        width: 177px;
        height: 32px;
        opacity: 0.7;
        background: #E6E9F0;
        border-radius: 8px;
        font-size: 14px;
        margin: 12px 0px 20px 24px;
        position: relative;
        display: flex;
        align-items: center;
        .economy{
            width: 68px;
            line-height: 28px;
            text-align: center;
        }
        .bussiness{
            width: 105px;
            line-height: 28px;
            text-align: center;
        }
        .economy-float-box{
            background-color: #fff;
            border-radius: 6px;
            position: absolute;  
            z-index: -1;
            left: 2px;
            height: 28px;
            width: 68px;
        }
        .bussiness-float-box{
            background-color: #fff;
            border-radius: 6px;
            position: absolute;  
            z-index: -1;
            left: 70px;
            height: 28px;
            width: 105px;
        }
        .select{
            color: #222;
            font-weight: 600;
        }
    }

    .voucher-guide {
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #333333;
        font-weight: 400;
        margin-bottom: 16px;

        .icon {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }

        .more-arrow {
            color: #999999;
            margin-left: 2px;
        }
    }
    .submit{
        margin-top: 3px;
        padding: 0 24px;
        .search-btn{
            box-sizing: border-box;
            overflow: visible;
            position: relative;
            height: 48px;
            line-height: 48px;
            color: #FFFFFF;
            display: block;
            width: 100%;
            border-radius: 100px;
            text-align: center;
            font-family: PingFangSC-Semibold;
            font-size: 20px;
            background-image:  linear-gradient(270deg, #FC4C51 0%, #FE9557 100%);
            font-weight: 700;
            .pop-coupon{
                position: absolute;
                top: -8px;
                left: 0;
                padding: 0 5px 0 8px;
                border: 1px solid #fff;
                background-image: linear-gradient(to right, #CE6FC6 0%, #ED432E 100%);
                color: #fff;
                font-size: 11px;
                line-height: 18px;
                border-radius: 10px 10px 0 10px;
            }
        }
        .search-btn::after{
            display: none;
        }
    }
    .search-btn-shadow {
        width: 311px;
        height: 16.5px;
        background-image: url('https://s.qunarzz.com/hotel_mp_source/images/linearbg.png');
        background-size: 100% 100%;
        margin: 0 auto;
        background-repeat: no-repeat;
    }

    .service {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding-bottom: 12px;
        text-align: center;
        font-size: 12px;
        height: 17px;

        .anxin {
            width: 43px;
            height: 13.5px;
            background-image: url('https://s.qunarzz.com/hotel_mp_source/images/anxin.png');
            background-size: 100% 100%;
        }
        .divline {
            margin-right: 8px;
            width: 20px;
            height: 1px;
            background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
        }
        .divline-right{
            margin-right: 0;
             margin-left: 8px;
             background-image: linear-gradient(270deg, #ffffff 0%, #00d5e7 100%);
        }
        .text-list{
            margin-bottom: 1px;
        }
        .service-text{
            margin-left: 7px;
            color: #000;
            opacity: 0.8;
        }
        .dot {
             padding-right: 2px;
             color: #00CAD8;
             font-size: 14px;
        }
   }


   .order-line{
        width: 100%;
        display: flex;
        margin-bottom: 1px;
        flex-direction: row;
        justify-content: center;
        .order{
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            height: 46px;
            width: 90px;
            font-size: 14px;
            .order-icon{
                font-size: 14px;
                line-height: 14px;
                color: #616161;
            }
            .order-tip{
                line-height: 14px;
                color: #212121;
            }
        }

    }
    .flight-rights-container {
        position: relative;
    
        .search-box-shadow{
            width: 351px;
            height: 60px;
            display: block;
            margin: 0 auto;
            margin-top: -28px;
            margin-bottom: -20px;
            z-index: -1;
            position: relative;
        }
    }
    
    .flight-rights{
        display: flex;
        justify-content: space-around;
        padding: 8px 12px;
        margin-bottom: 10px;
        .flight-right{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .flight-right-image{
                width: 32px;
                height: 32px;
                margin-bottom: 4px;
            }
            .flight-right-text{
                width: 70px;
                font-size: 12px;
                color: #222222;
                text-align: center;
            }
        }
    }
}

.home-search-test {
    background: none;
    .flight-search {
        position: relative;
        z-index: 2;
        margin: 0;
        padding: 8px 0 3px;
        background: #fff;
        border-radius: 16px 16px 12px 12px;
        &::after {
            content: '\0020';
            position: absolute;
            z-index: 2;
            bottom: 0;
            left: 4%;
            width: 92%;
            height: 1px;
            border-bottom: 1px dashed #F3F4F7;
        }
    }
    .tabItemList {
        margin-bottom: 8px;
        justify-content: center;
        .tabItem {
            // flex: none;
            // padding-right: 80rpx;
            // padding-left: 80rpx;
            width: 32px;
            font-size: 14px;
            &:first-child {
                padding-right: 40px;
                text-align: right;
            }
            &:last-child {
                padding-left: 40px;
                text-align: left;
            }
            &.on {
                font-weight: bold;
                font-size: 16px;
                .bottom-line {
                    width: 32px;
                    transform: none;
                }
            }
        }
        .on {
            &.tabItem:first-child .bottom-line {
                left: auto;
                right: 0;
            }
            &.tabItem:last-child .bottom-line {
                left: 0;
            }
        }
    }
    .searchCity,
    .searchDate,
    .submit {
        padding: 0 16px;
    }
    .searchCity,
    .searchDate {
        .swap {
            width: 32px;
            height: 32px;
        }
        .city-select-item {
            height: 33px;
            line-height: 33px;
            .cityName {
                font-size: 24px;
            }
        }
    }
    .searchDate {
        height: 28px;
        .day-date {
            height: 28px;
            font-size: 22px;
            line-height: 29px;
        }
        .day-date-text {
            font-size: 20px;
        }
        .week-date {
            vertical-align: 2px;
        }
    }
    .submit {
        margin-top: 0;
        .search-btn {
            font-size: 18px;
        }
    }
    .select-flight-cabin {
        margin-top: 20px;
        margin-bottom: 24px;
        margin-left: 16px;
        width: 170px;
        height: 28px;
        .economy,
        .economy-float-box,
        .bussiness,
        .bussiness-float-box {
            height: 24px;
            font-size: 14px;
            line-height: 24px;
        }
        .economy,
        .economy-float-box {
            width: 66px;
        }
        .bussiness,
        .bussiness-float-box {
            width: 100px;
        }
        .bussiness-float-box {
            left: 68px;
        }
    }
    .city-bottom-line {
        display: none;
    }
    .search-btn-shadow {
        width: 281px;
        height: 13px;
    }
    .flight-rights {
        margin: 0;
        padding: 0;
        background: #fff;
        border-radius: 12px;
        .flight-right {
            flex: 1;
            padding: 12px 0;
            .flight-right-image {
                margin-bottom: 2px;
                width: 20px;
                height: 20px;
            }
            .flight-right-text {
                width: auto;
                color: #666;
            }
        }
    }
}
}

