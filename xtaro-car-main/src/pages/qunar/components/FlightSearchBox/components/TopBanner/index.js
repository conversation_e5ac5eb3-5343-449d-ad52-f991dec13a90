import { View, Image } from "@tarojs/components";
import { navigateTo } from "@tarojs/taro";
import React from "react";
import log from "@/common/flight/log.js";
import "./index.scss";

class TopBanner extends React.Component {
    componentDidMount() {
        log({
            p: "mini_home",
            r: "flight_home_top_banner_show",
        });
    }

    handleJump = () => {
        log({
            p: "mini_home",
            r: "flight_home_top_banner_click",
        });
        if (this.props.dataSource.jumpUrl) {
            navigateTo({
                url: this.props.dataSource.jumpUrl,
            });
        }
    };

    handleClose = () => {
        log({
            p: "mini_home",
            r: "flight_home_top_banner_close",
        });
        this.props.onClose();
    };

    render() {
        return (
            <View className="top-banner-box">
                <View className="container">
                    <Image
                        src={this.props.dataSource.imageUrl}
                        className="banner-image"
                        mode="widthFix"
                        onClick={this.handleJump}
                    />

                    <View className="banner-close" onClick={this.handleClose}>
                        <View className="flight-icon close-icon"></View>
                    </View>
                </View>
            </View>
        );
    }
}

export default TopBanner;
