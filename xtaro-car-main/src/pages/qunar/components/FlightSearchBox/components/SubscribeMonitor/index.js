import { View, Text, Image } from "@tarojs/components";
import { navigateTo } from "@tarojs/taro";
import React from "react";
import log from "@/common/flight/log.js";
import "./index.scss";

const monitorIconMap = {
    sale_flight_scanning:
        "https://s.qunarzz.com/flight_home_rn/images/sale_flight_scanning.png",
    sale_flight_scan_finish:
        "https://s.qunarzz.com/flight_home_rn/images/sale_flight_scan_finish.png",
    sale_flight_ok:
        "https://s.qunarzz.com/flight_home_rn/images/sale_flight_ok.png",
    sale_flight_changelow:
        "https://s.qunarzz.com/flight_home_rn/images/sale_flight_changelow.png",
    flight_type_1_icon:
        "https://qimgs.qunarzz.com/wpf_newmpic_001/cfdc7e580059694cdc8798be9e9bae7e.png", // 单程箭头icon
    flight_type_2_icon:
        "https://qimgs.qunarzz.com/wpf_newmpic_001/e00aa69612eed1faac6c86eea4a5ea60.png", // 往返箭头icon
};

// 航程类型
const flightTypeMap = {
    oneway: 1, // 单程
    roundway: 2, // 往返
};

/**
 * 监控状态
 * changeType_low_expected: 低于期望价
 * changeType_low_follow: 大于等于期望价小于关注价
 * changeType_high_follow: 大于等于关注价
 */
const subscribeMonitorStatusMap = {
    changeType_low_expected: -1,
    changeType_low_follow: 0,
    changeType_high_follow: 1,
};

class SubscribeMonitor extends React.Component {
    state = {
        monitorIcon: "",
        fligntTypeIcon: "",
    };

    componentWillMount() {
        this.handeData(this.props.data);
    }

    componentWillReceiveProps(nextProps) {
        this.handeData(nextProps.data);
    }

    handeData = (data) => {
        const { changeType, flightType } = data;
        this.setState({
            // 监控状态图，做动画效果
            monitorIcon:
                changeType ===
                    subscribeMonitorStatusMap.changeType_low_expected ||
                changeType === subscribeMonitorStatusMap.changeType_low_follow
                    ? monitorIconMap.sale_flight_scan_finish
                    : monitorIconMap.sale_flight_scanning,
            // 监控状态图标，静态图
            monitorStatusIcon:
                changeType === subscribeMonitorStatusMap.changeType_low_expected
                    ? monitorIconMap.sale_flight_ok
                    : changeType ===
                        subscribeMonitorStatusMap.changeType_low_follow
                      ? monitorIconMap.sale_flight_changelow
                      : "",
            // 航程类型图标，单程、往返
            fligntTypeIcon:
                flightType === flightTypeMap.oneway
                    ? monitorIconMap.flight_type_1_icon
                    : monitorIconMap.flight_type_2_icon,
            // 价格描述数据处理，数组格式，接口
            priceDescArr: this.handlePriceDesc(),
        });
    };

    handlePriceDesc = () => {
        const { priceDesc } = this.props.data;
        if (!priceDesc) return [];
        /**
         * priceDesc值的格式：可能含有特殊字符，比如:‘比期望价<低¥17>’ ,需要对有特殊字符的字符串做处理，截取出‘低¥17’需要做高亮处理
         * prefixSt: '比期望价'
         * priceStr: '低¥17'
         */
        let prefixStr = "",
            priceStr = "";
        try {
            const [, desc2, desc3] = priceDesc.match(/^(.*)<(.*)>/) || [];
            if (desc2 && desc3) {
                prefixStr = desc2;
                priceStr = desc3;
            }
        } catch (err) {
            //
        }
        return prefixStr && priceStr ? [prefixStr, priceStr] : [priceDesc];
    };

    // 卡片上头部右上角区域点击事件
    headerClick = () => {
        log({ p: "mini_home", r: "subscribe_monitor_banner_header_click" });
        const { titleJumpUrl } = this.props.data;
        titleJumpUrl &&
            navigateTo({
                url: titleJumpUrl,
            });
    };

    // 卡片主体区域点击事件
    cardClick = () => {
        log({ p: "mini_home", r: "subscribe_monitor_banner_body_click" });
        const { jumpUrl } = this.props.data;
        jumpUrl &&
            navigateTo({
                url: jumpUrl,
            });
    };

    render() {
        return (
            <View
                className={`subscribe-monitor-card${this.props.isTestTheme ? " subscribe-monitor-test" : ""}`}
            >
                <View className="subscribe-monitor-card-header">
                    <View className="title">{this.props.data.title}</View>
                    <View className="label" onClick={this.headerClick}>
                        <Text>{this.props.data.subTitle}</Text>
                        <Text
                            className={`icon ${this.props.data.jumpIcon === "add" ? "add-icon" : "right-arrow-icon"}`}
                        />
                    </View>
                </View>
                <View
                    className="subscribe-monitor-card-body"
                    onClick={this.cardClick}
                >
                    <View className="flight-info">
                        <Image
                            className="monitor-icon"
                            src={this.state.monitorIcon}
                        />
                        {this.state.monitorStatusIcon && (
                            <Image
                                className="monitor-status-icon"
                                src={this.state.monitorStatusIcon}
                            />
                        )}
                        <View className="flight-info-content">
                            <View className="flight-info-city">
                                <Text className="flight-info-city-dep">
                                    {this.props.data.depCity}
                                </Text>
                                <View className="flight-type">
                                    <Image
                                        src={this.state.fligntTypeIcon}
                                        className="flight-type-icon"
                                    />
                                    {/* 单程才展示中转标记 */}
                                    {this.props.data.flightType === 1 &&
                                    this.props.data.transfer ? (
                                        <Text className="transfer-tag">
                                            {this.props.data.transfer}
                                        </Text>
                                    ) : null}
                                </View>
                                <Text className="flight-info-city-arr">
                                    {this.props.data.arrCity}
                                </Text>
                            </View>
                            {/* 日期 */}
                            <View className="flight-info-date">
                                <Text>{this.props.data.dateStr}</Text>
                                {this.props.data.crossDay &&
                                    this.props.data.flightType === 1 && (
                                        <Text className="over-one-day">
                                            {this.props.data.crossDay}
                                        </Text>
                                    )}
                            </View>
                        </View>
                    </View>
                    <View className="price-info">
                        <View className="price-info-price">
                            {this.props.data.currentPrice ? (
                                <View className="price-info-money">
                                    <Text>当前最低</Text>
                                    <Text className="light-text">
                                        ¥{this.props.data.currentPrice}
                                    </Text>
                                </View>
                            ) : (
                                <View className="price-info-empty">
                                    暂无报价
                                </View>
                            )}
                        </View>
                        {this.state.priceDescArr.length > 0 && (
                            <View className="price-info-desc">
                                {this.state.priceDescArr[0] && (
                                    <Text>{this.state.priceDescArr[0]}</Text>
                                )}
                                {this.state.priceDescArr[1] && (
                                    <Text className="light-color">
                                        {this.state.priceDescArr[1]}
                                    </Text>
                                )}
                            </View>
                        )}
                    </View>
                </View>
                {this.props.data.monitorDesc && (
                    <View
                        className="subscribe-monitor-card-footer"
                        onClick={this.cardClick}
                    >
                        {this.props.data.monitorDesc}
                    </View>
                )}
            </View>
        );
    }
}

export default SubscribeMonitor;
