.subscribe-monitor-card {
    margin: 0 8px;
    background: #FFF;
    border-radius: 16px;
    font-family: PingFangSC-Regular;
    margin-bottom: 10px;

    &-header {
        padding: 0 16px;
        color: #4D4D4D;
        display: flex;
        justify-content: space-between;
        padding-top: 10px;

        .title {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            font-weight: 500;
        }

        .label {
            font-size: 12px;
            font-weight: 400;
            display: flex;
            align-items: center;
        }

        .icon {
            display: inline-block;
            width: 12px;
            height: 13px;
        }

        .add-icon {
            background: url('https://qimgs.qunarzz.com/wpf_newmpic_001/fc3d1166985fd535b985fb7716a891f1.png') no-repeat center/100%;
        }
        .right-arrow-icon {
            background: url('https://qimgs.qunarzz.com/wpf_newmpic_001/6768869c0d90226adba72df3015b4b07.png') no-repeat center/100%;
        }
    }

    &-body {
        padding: 0 16px;
        margin-top: 6px;
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #212121;

        .monitor-icon {
            width: 40px;
            height: 40px;
            animation: monitorIconRotate 1.5s infinite linear;
            margin-top: 6px;
        }

        .monitor-status-icon {
            position: absolute;
            width: 40px;
            height: 40px;
            top: 6px;
        }

        .flight-info {
            display: flex;
            align-items: center;
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        .flight-info-content {
            margin-left: 12px;
            flex: 1;
            overflow: hidden;
        }

        .flight-info-city {
            display: flex;
            font-size: 14px;
            font-weight: 500;
            font-family: PingFangSC-Medium;
            padding-top: 6px;
            height: 26px;
            box-sizing: border-box;
        }

        .flight-type {
            position: relative;
            flex-shrink: 0;
            margin: 0 6px;
        }
        .flight-type-icon {
            width: 24px;
            height: 20px;
        }

        .transfer-tag {
            position: absolute;
            left: 50%;
            top: -6px;
            transform: translate(-50%);
            font-size: 10px;
            color: #9E9E9E;
            text-align: center;
            font-weight: 400;
        }

        .flight-info-city-dep {
            white-space: nowrap;
        }

        .flight-info-city-arr {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .flight-info-date {
            font-size: 12px;
            font-weight: 400;
            margin-top: 2px;
            font-family: PingFangSC-Medium;
            display: flex;
            align-items: center;
        }

        .over-one-day {
            font-size: 10px;
            color: #9E9E9E;
            font-weight: 400;
            margin-left: 2px;
        }

        .price-info {
            font-size: 12px;
            font-weight: 400;
        }

        .price-info-price {
            height: 26px;
            display: flex;
            align-items: flex-end;
            justify-content: flex-end;
        }

        .price-info-money {
            display: flex;
            align-items: center;
        }

        .light-text {
            font-family: PingFangSC-Medium;
            font-size: 18px;
            color: #FF6600;
            font-weight: 500;
            margin-left: 2px;
        }

        .price-info-empty {
            font-family: PingFangSC-Medium;
            font-size: 18px;
            color: #CCCCCC;
            font-weight: 500;
        }

        .price-info-desc {
            font-size: 12px;
            font-weight: 400;
            height: 17px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .light-color {
            color: #FF6600;
        }
    }

    &-footer {
        font-size: 12px;
        color: #9E9E9E;
        font-weight: 400;
        line-height: 17px;
        border-top: 0.5px solid #E0E0E0;
        padding: 6px 16px 6px;
    }
}

.subscribe-monitor-test {
    margin: 0 0 12px;
    border-radius: 12px;
    box-shadow: none;
}

@keyframes monitorIconRotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}