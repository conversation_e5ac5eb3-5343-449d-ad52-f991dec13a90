import { View, Text, Image } from "@tarojs/components";
import { getStorageSync, setStorageSync, navigateTo } from "@tarojs/taro";
import QMark from "@/npm/@qnpm/qmark/dist/qmark.mini.umd.js";
import React from "react";
import Log from "@/common/utils/log";
import EventEmitter from "@/common/utils/EventEmitter";
import Request from "@/common/utils/request.js";
import getLocation from "@/common/utils/location";
import "./index.scss";

class TicketIndexAroundSight extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            networkData: {
                status: 4,
                loadingDesc: "加载中...",
                showButton: true,
            },
            indexDataList: [],
            suggestList: {
                title: "",
                items: [],
            },
            searchValue: "",
            isShowSuggest: false,
            isShowAround: true,
            isShowCouponList: false,
            isShowShopHistory: false,
            shopHistoryList: [],
            defaultCity: "",
        };
    }

    componentWillMount() {
        QMark &&
            QMark.log &&
            QMark.log({
                bizType: "ticket",
                module: "nearbySight",
                appcode: "wx",
                page: "ticketHome_WeChat",
                id: "nearbySightShow",
                operType: "show",
                key: "ticket/ticketHome_WeChat/nearbySight/show/nearbySightShow",
                operTime: "*",
            });
        this.initModuleList();
    }

    componentDidMount() {
        EventEmitter.addListener("TICKET_SELECT_CITY", () => {
            this.getListData();
        });
    }

    // 模块列表
    initModuleList = () => {
        getLocation({
            success: (res) => {
                this.lat = res.latitude || "";
                this.lng = res.longitude || "";
                this.getListData();
            },
            fail: () => {
                this.getListData();
            },
        });
    };
    getListData = () => {
        const currentCity = getStorageSync("ticket-city");
        Request({
            host: "https://wxapp.qunar.com/ticket/pw",
            service: "/getAroundAndHotSight.json",
            data: {
                lat: this.lat ? this.lat : "",
                lng: this.lng ? this.lng : "",
                searchCity: currentCity,
            },
            success: (response) => {
                let data = response.data;
                let bannerData = data.banner ? data.banner : {};
                let aroundSight = data.aroundSight ? data.aroundSight : {};
                let city = data.city ? data.city : "北京";

                this.setState({
                    networkData: {
                        status: 0,
                    },
                    indexDataList: aroundSight.items || [],
                    bannerData: bannerData,
                    isShowBanner: Object.keys(bannerData).length ? true : false,
                    isShowSuggest: false,
                    defaultCity: city,
                });
            },
            fail: () => {},
            complete: () => {},
        });
    };

    goTicketList = () => {
        QMark &&
            QMark.log &&
            QMark.log({
                bizType: "ticket",
                module: "nearbySight",
                appcode: "wx",
                page: "ticketHome_WeChat",
                id: "moreSightClick",
                operType: "click",
                key: "ticket/ticketHome_WeChat/nearbySight/click/moreSightClick",
                operTime: "*",
            });
        const traceId = getStorageSync("ticketTraceId");
        setStorageSync("ticketFromArea", "index_around");
        const newCatQuery = encodeURIComponent(
            `from_area=index_around&log_id=${traceId}_home`,
        );
        let url = `/pages/ticket/index/index?catQuery=${newCatQuery}&from_wxHome=index_around`;
        Log({
            info: {
                area: "ticketList",
                act: "click",
                type: "homeEnter",
                name: "ticketList",
            },
        });
        navigateTo({
            url,
        });
    };

    goTicketListDetail = (id) => {
        QMark &&
            QMark.log &&
            QMark.log({
                bizType: "ticket",
                module: "nearbySight",
                appcode: "wx",
                page: "ticketHome_WeChat",
                id: "nearbySightClick",
                operType: "click",
                key: "ticket/ticketHome_WeChat/nearbySight/click/nearbySightClick",
                operTime: "*",
            });
        Log({
            info: {
                area: "ticketList",
                act: "click",
                type: "homeEnter",
                name: "ticketList",
            },
        });
        const traceId = getStorageSync("ticketTraceId");
        setStorageSync("ticketFromArea", "index_around_detail");
        const newCatQuery = encodeURIComponent(
            `from_area=index_around_detail&log_id=${traceId}_home`,
        );
        let url = `/pages/ticket/poiDetail/index?sightId=${id}&catQuery=${newCatQuery}&from_wxHome=index_around_detail`;
        navigateTo({url});
    };

    render() {
        return this.state.indexDataList.length ? (
            <View
                class={this.props.isNew ? "mp-homePage-around-sight new-mp-around-sight-margin" : "mp-homePage-around-sight"}>
                <View class="mp-homePage-around-sight-title">
                    <Text class="mp-homePage-around-sight-title-left">
                        附近热门景点
                    </Text>
                    <Text class="mp-homePage-around-sight-title-right" onClick={this.goTicketList}>
                        当地更多景点<Text class="right"></Text>
                    </Text>
                </View>
                <View class="mp-homePage-around-sight-imgList">
                    {this.state.indexDataList.map((item) =>
                        this.state.indexDataList.length !== 1 ? (
                            <View onClick={() => this.goTicketListDetail(item.id)}>
                                <View class="mp-homePage-around-sight-imgBox">
                                    <Image src={item.img}></Image>
                                    {item.distance ? (
                                        <Text class="mp-homePage-around-sight-distance">
                                            距您{`${item.distance}`}km
                                        </Text>
                                    ) : null}
                                </View>
                                <View class="mp-homePage-around-sight-title-sub">
                                    {item.name}
                                </View>
                                <View class="mp-homePage-around-sight-score-box">
                                    {item.commentScore ? (
                                        <Text class="mp-homePage-around-sight-score">
                                            {item.commentScore}分
                                        </Text>
                                    ) : null}

                                    {item.commentScore ? (
                                        <Text class="mp-homePage-around-sight-line"></Text>
                                    ) : null}

                                    <Text class="mp-homePage-around-sight-comment">
                                        {item.commentCount || 0}条评价
                                    </Text>
                                </View>
                                <View class="mp-homePage-around-sight-price-box">
                                    <Text class="mp-homePage-around-sight-price-desc">
                                        ￥
                                    </Text>
                                    <Text class="mp-homePage-around-sight-price-number">
                                        {item.qunarPrice}
                                    </Text>
                                    <Text class="mp-homePage-around-sight-price-desc">
                                        起
                                    </Text>
                                </View>
                            </View>
                        ) : (
                            <View class="mp-homePage-around-sight-box" onClick={() => this.goTicketListDetail(item.id)}>
                                <View class="mp-homePage-around-sight-imgBox-single">
                                    <Image src={item.img}></Image>
                                </View>
                                <View>
                                    <View class="mp-homePage-around-sight-title-sub-pri">
                                        {item.name}
                                    </View>
                                    <View class="mp-homePage-around-sight-score-box">
                                        {item.desc ? (
                                            <View class="mp-homePage-around-sight-desc">{`“${item.desc}”`}</View>
                                        ) : null}

                                        {item.commentScore ? (
                                            <Text class="mp-homePage-around-sight-score">
                                                {item.commentScore}分
                                            </Text>
                                        ) : null}

                                        <Text class="mp-homePage-around-sight-comment">
                                            {item.commentCount || 0}条评价
                                        </Text>
                                        {item.distance ? (
                                            <Text class="mp-homePage-around-sight-distance-style">
                                                距您{`${item.distance}`}km
                                            </Text>
                                        ) : null}
                                    </View>
                                    <View class="mp-homePage-around-sight-price-box">
                                        <Text class="mp-homePage-around-sight-price-desc">
                                            ￥
                                        </Text>
                                        <Text>{item.qunarPrice}</Text>
                                        <Text class="mp-homePage-around-sight-price-desc">
                                            起
                                        </Text>
                                    </View>
                                </View>
                            </View>
                        )
                    )}
                </View>
            </View>
        ) : null;
    }
}

export default TicketIndexAroundSight;
