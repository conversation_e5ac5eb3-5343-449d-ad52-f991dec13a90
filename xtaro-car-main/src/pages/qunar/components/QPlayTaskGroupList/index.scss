.task-list-container {
    margin-top: 8px;
    flex-direction: column;
    justify-content: flex-start;
    display: flex;
    padding: 16px 22px 16px 12px;
    border-radius: 12px;
    margin-left: 10px;
    margin-right: 10px;
    background-color: #ffffff;
}

.task-list-header-image {
    align-self: flex-start;
    margin-left: 4px;
    width: 116px;
    height: 18px;
}

.task-list {
    margin-top: 18px;
}

.task-item {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    display: flex;
    margin-bottom: 24px;
}

.task-item:last-child {
    margin-bottom: 0;
}

.task-item-icon {
    width: 56px;
    height: 56px;
    overflow: hidden;
    margin-right: 7px;
}

.task-item-icon-image {
    width: 56px;
    height: 56px;
    border-radius: 10px;
}

.task-item-description {
    flex: 1;
    flex-direction: column;
    align-items: flex-start;
    display: flex;
}

.task-item-title {
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #333333;
    font-size: 14px;
    height: 20px;
    line-height: 20px;
    margin-bottom: 2px;
    max-width: 180px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.task-item-earnings-info {
    display: flex;
    align-items: center;
    flex-direction: row;
    height: 17px;
}

.task-item-earnings-icon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
}

.task-item-earnings-description {
    font-family: PingFangSC-Medium;
    font-weight: 400;
    color: #666666;
    font-size: 12px;
    height: 17px;
    line-height: 17px;
    max-width: 160px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.task-item-action {
    width: 68px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 16px;
    background: linear-gradient(-90deg, #ff4505, #ff8f5a);
}

.task-item-action-text {
    display: flex;
    align-items: center;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: #ffffff;
    font-size: 14px;
}