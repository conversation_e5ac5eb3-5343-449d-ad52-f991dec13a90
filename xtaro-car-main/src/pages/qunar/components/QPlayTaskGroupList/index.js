import { View, Image, Text } from "@tarojs/components";
import { navigateTo } from "@tarojs/taro";
import React from "react";
import login from "@/common/utils/login";
import LogQmark from "@/common/utils/logQmark";
import "./index.scss";

class QPlayTaskGroupList extends React.Component {
    constructor(props) {
        super(props);
    }

    doTask = (taskInfo) => {
        const { activityCode, banner } = taskInfo || {};
        const { schema, bargainStatus } = banner;
        LogQmark({
            module: "default",
            id: "doTask",
            page: "qplay",
            operType: "click",
            ext: {
                url: schema,
                activityCode,
                bargainStatus,
            },
        });
        if (bargainStatus === "NOT_LOGIN") {
            login(() => {}, { source: "qPlay" });
            return null;
        }
        if (!schema) return null;
        navigateTo({
            url: schema,
        });
    };

    render() {
        if (!this.props.taskList || this.props.taskList.length === 0)
            return <View></View>;
        return (
            <View className="task-list-container">
                <Image
                    className="task-list-header-image"
                    src="http://qimgs.qunarzz.com/wpf_newmpic_001/3cb1a5e5ed9bf8bb60c6a4c0359d67a5.png"
                ></Image>
                <View className="task-list">
                    {this.props.taskList.map((item, index) => {
                        return (
                            <View className="task-item" key={item.activityCode}>
                                <View className="task-item-icon">
                                    <Image
                                        className="task-item-icon-image"
                                        src={item.banner.icon}
                                    ></Image>
                                </View>
                                <View className="task-item-description">
                                    <Text className="task-item-title">
                                        {item.banner.tipText}
                                    </Text>
                                    <View className="task-item-earnings-info">
                                        <Image
                                            className="task-item-earnings-icon"
                                            src="http://qimgs.qunarzz.com/wpf_newmpic_001/2702aa0cce4b265fc285402dd8eb2143.png"
                                        ></Image>
                                        <Text className="task-item-earnings-description">
                                            {item.banner.subTitle}
                                        </Text>
                                    </View>
                                </View>
                                <View
                                    className="task-item-action"
                                    onClick={() => this.doTask(item)}
                                >
                                    <Text className="task-item-action-text">
                                        {item.banner.buttonText}
                                    </Text>
                                </View>
                            </View>
                        );
                    })}
                </View>
            </View>
        );
    }
}

export default QPlayTaskGroupList;
