
@font-face {
    font-family: "hotel-iconfont";
    src: url("https://s.qunarzz.com/mp_hotel/fonts/1.0.19/mp_hotel.ttf");
    font-weight: normal;
    font-style: normal;
}
.bhotel-container{
    background-color: #fff;
    overflow: hidden;
}

.bhotel-search {
    padding: 7px 16px 0;
    font-family: PingFangSC-Light;
    border-radius: 16px;
}
.bhotel-tab {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 7px;
}
.bhotel-margin {
    height: 9px;
}
.flex-tab {
    display: flex;
    align-items: center;
    padding-top: 8px;
    padding-bottom: 8px;
}
.tab-item {
    display: flex;
    position: relative;
    justify-content: center;
    flex: 1;
    font-family: PingFangSC-Light;
    font-size: 14px;
    color: #333;
    font-weight: 200;
}
.item-box {
    position: relative;
    overflow: hidden;
}
.tab-text {
    white-space: nowrap;
    position: relative;
    z-index: 2;
}
.linearBar{
    position: absolute;
    bottom: 0;
    z-index: 1;
    width: 95%;
    height: 6px;
    border-radius: 6px;
    background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
}
.flex-tab:nth-child(2) .tab-item,.old-tab-item {
    justify-content: center;
}
.flex-tab:nth-child(3) .tab-item,.old-tab-item {
    justify-content: center;
}
.tab-tag {
    display: inline-block;
    position: absolute;
    right: -16px;
    top: -11px;
    width: 40px;
    height: 12px;
    background-image: url('https://s.qunarzz.com/hotel_mp_source/images/indulgence_tag.png');
    background-size: 100% 100%;
    animation: 1s linear 0s 1 move_shake;
}
@keyframes move_shake { 
    0% {
        transform: scale(0); 
        transform-origin: top left;
    }
    20% {
        transform: scale(1);
    }
    25% { 
        transform: rotate(0deg);
        transform-origin: top left;
    } 
    50% { 
        transform: rotate(-7deg);
        transform-origin: top left;
    } 
    85% { 
        transform: rotate(4deg);
        transform-origin: top left;
    } 
    100% { 
        transform: rotate(0deg);
        transform-origin: top left;
    }  
}
.septal-line {
    width: 1px;
    height: 12px;
    background-color: #e6e6e6;
}
.tab-item-cur {
    font-size: 16px;
    color: rgba($color: #222222, $alpha: 1);
    font-weight: bold;
    font-family: PingFangSC-Semibold;
}
.hotel-address{
    display: flex;
    padding-bottom: 14px;
    align-items: center;
}
.address{
    flex: 1;
    width: 240px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 24px;
    color: #222;
    font-weight: bold;
    font-family: PingFangSC-Semibold;
}
.locate-txt {
    white-space: nowrap;
}
.position{
    display: flex;
    flex-direction: column;
    font-family: PingFangTC-Light;
    text-align: center;
    font-size: 10px;
    color: #333;
    font-weight: 200;
}
.position-icon{
   margin: 0 auto;
   font-size: 18px;
   color: #333;
   font-weight: 400;
}
.hotel-date{
    padding-bottom: 16px;
    margin: 6px 0;
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    font-size: 0;
}
.select-date{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.total {
    width: 47px;
    text-align: right;
    font-size: 14px;
    line-height: 1;
    font-weight: 200;
    color: #222;
}
.total-num {
    font-family: 'hotel_rn_num';
}
.line {
   width: 8px;
   height: 1px;
   background-color: #ccc;
   margin: 0 5px;
}
.small-txt{
   margin-left: 4px;
   font-size: 14px;
   color: #222;
   font-weight: 200;
}
.date-box {
    display: flex;
    align-items: flex-end;
    line-height: 1;
}
.date {
    font-family: 'hotel_rn_num';
    font-size: 22px;
    color: #222;
    margin-bottom: -1px;
}
.desc {
    font-size: 20px;
    color: #222;
    font-weight: bold;
}
.zeroRoom-tips {
   height: 32px;
   line-height: 32px;
   background-color: rgba($color: #FF6600, $alpha: 0.06);
   border-radius: 4px;
   font-size: 12px;
   margin-bottom: 12.5px;
   margin-top: -8px;
}
.tips {
    padding-left: 12px;
    color: #FF6600;
}
.hotel-keywords {
   display: flex;
   flex-direction: row;
    padding-bottom: 24px;
}
.input-keyword{
    flex: 1;
    font-size: 16px;
    font-family: PingFangSC-Light;
    color: #666;
    font-weight: 200;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.clearkeyword {
    width: 30px;
    text-align: right;
}
.icon-c {
    font-size: 15px;
    color: #ccc;
    font-family: "hotel-iconfont";
    font-weight: normal;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    vertical-align: baseline;
    background-image: none;
    background-position: 0% 0%;
    background-repeat: repeat;
    margin: 0 3px;
}
.icon-l {
    font-size: 18px;
    font-weight: 400;
    color: #333;
    font-family: "hotel-iconfont";
    font-weight: normal;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    vertical-align: baseline;
    background-image: none;
    background-position: 0% 0%;
    background-repeat: repeat;
    margin: 0 3px;
}
.search-btn {
    position: relative;
    height: 48px;
    background-image: linear-gradient(270deg, #FC4650 0%, #FF9658 100%);
    border-radius: 25px;
    text-align: center;
    .btn-icon-right {
        position: absolute;
        height: 25px;
        right: 0;
        top: 0;
        vertical-align: top;
    }
}
.search-btn-shadow {
    width: 100%;
    height: 16px;
    background-image: url('https://s.qunarzz.com/f_cms/2024/1709796384445_261952254.png');
    background-size: 100% 100%;
}
.search-text {
    line-height: 48px;
    color: #fff;
    font-size: 20px;
    font-family: PingFangSC-Semibold;
    font-weight: bold;
}
.service {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding-bottom: 12px;
    text-align: center;
    font-size: 12px;
    height: 17px;
}
.anxin {
   width: 43px;
   height: 13.5px;
   background-image: url('https://s.qunarzz.com/hotel_mp_source/images/anxin.png');
   background-size: 100% 100%;
}
.divline {
   margin-right: 8px;
   width: 20px;
   height: 1px;
   background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
}
.divline-right{
   margin-right: 0;
    margin-left: 8px;
    background-image: linear-gradient(270deg, #ffffff 0%, #00d5e7 100%);
}
.text-list{
   margin-bottom: 1px;
}
.service-text{
   margin-left: 7px;
   color: #000;
   opacity: 0.8;
}
.dot {
    padding-right: 2px;
    color: #00CAD8;
    font-size: 14px;
}


.old-hotel-container{
  background-color: #f5f6f9;
  overflow: hidden;
}
.old-hotel-search {
  margin: 8px 8px 12px;
  padding: 0 24px;
  font-family: PingFangSC-Light;
  background-image: linear-gradient(180deg, #f5f6f9 0%, #FFFFFF 45%);
  border-radius: 16px;
}
.old-tab-item {
    display: flex;
    position: relative;
    justify-content: center;
    flex: 1;
    font-size: 16px;
    color: rgba($color: #222222, $alpha: 0.5);
}
.old-septal-line {
    width: 1px;
    height: 18px;
    background-color: #e6e6e6;
}
.old-tab-item-cur {
    color: rgba($color: #222222, $alpha: 1);
    font-weight: 700;
    font-family: PingFangSC-Semibold;
}
.old-hotel-address{
    display: flex;
    padding-bottom: 10px;
    align-items: center;
}
.old-address{
    flex: 1;
    width: 240px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 22px;
    color: #222;
    font-weight: 700;
    font-family: PingFangSC-Semibold;
}
.old-position{
    text-align: center;
    font-size: 12px;
    color: #00CAD8;
}
.old-position-icon{
   margin: 0 auto;
   width: 18px;
   height: 18px;
   background-image: url('https://s.qunarzz.com/hotel_mp_source/images/position.png');
   background-size: 100% 100%;
}
.old-hotel-date{
    border-top: 0.5px solid #F2F2F2;
    border-bottom: 0.5px solid #F2F2F2;
    padding: 12.5px 0 12.5px;
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 0;
}
.old-select-date{
    width: 225px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.old-line {
   width: 9px;
   height: 1px;
   background-color: #222;
}
.old-small-txt{
   height: 17px;
   line-height: 17px;
   margin-right: 4px;
   font-size: 12px;
   color: #222;
}
.old-date {
    font-family: 'hotel_rn_num';
    font-size: 18px;
    color: #222;
    font-weight: 700;
}
.old-desc {
    font-size: 18px;
    color: #222;
    font-weight: 700;
}
.old-hotel-keywords {
   display: flex;
   flex-direction: row;
   padding: 12.5px 0 10px;
}
.old-input-keyword{
    flex: 1;
    font-size: 18px;
    font-family: PingFangSC-Light;
    color: #999;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.old-search-btn {
    position: relative;
    margin-top: 8px;
    height: 48px;
    background-image: linear-gradient(270deg, #FC4C51 0%, #FE9557 100%);
    border-radius: 48px;
    text-align: center;
}
.old-search-btn-shadow {
    width: 311px;
    height: 16px;
    background-image: url('https://s.qunarzz.com/hotel_mp_source/images/linearbg.png');
    background-size: 100% 100%;
}

.old-zeroRoom-tips {
    height: 32px;
    line-height: 32px;
    background-color: rgba($color: #FF6600, $alpha: 0.06);
    border-radius: 4px;
    font-size: 12px;
 }