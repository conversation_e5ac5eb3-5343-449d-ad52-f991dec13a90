import { View, Text } from "@tarojs/components";
import {
    getApp,
    getStorageSync,
    setStorageSync,
    navigateTo,
    setStorage,
} from "@tarojs/taro";
import React from "react";
import "./index.scss";
import request from "@/common/utils/request.js";
import EventEmitter from "@/common/utils/EventEmitter";
import newWatcher from "@/common/utils/watcher";
import getLocation from "@/common/utils/location.js";
import dateFormat from "@/common/utils/date/format";
import user from "@/common/utils/user";
import LogQmark from "@/common/utils/logQmark";

const PI = 3.14159265358979324;
const x_pi = (3.14159265358979324 * 3000.0) / 180.0;

// 定位失败或未授权定位时，默认城市为北京
const defaultSearchInfo = {
    cityId: 48,
    cityName: "北京",
    pinYin: "beijing",
    // 可能为number或string 1：国内含港澳台；2：海外
    keywordSuggestType: "1",
    enumSuggestionConditionType: 0,
    standardName: "",
    suggestLinkUrl: "",
    suggestionConditionValue: "",
};
const storageKey = {
    // 酒店城市信息
    dayRoom: "WX_HOTEL_CITY",
    // 存储上次酒店进入民宿时的城市信息，用于和本次对比，判断是否修改
    dayRoomLast: "WX_HOTEL_CITY_LAST",
    // 酒店日期
    hotelDate: "HOTEL_Date_Format",
    // 存储上次酒店进入民宿时的城市日期，用于和本次对比，判断是否修改
    hotelDateLast: "HOTEL_Date_Format_Last",
    hotelTab: "HOTEL_TAB",
    // 民宿数据缓存，多处使用，字段修改请和民宿同步
    bnb: "SEARCH_RESULT",
    // 民宿数据缓存，多处使用，字段修改请和民宿同步
    bnbDate: "GLOBAL_DATES",
};
// 1天对应的毫秒数 86400000
const millisecond = 60 * 60 * 1000 * 24;
const eventType = {
    bnbSearchBox: "BNB_SEARCH_BOX",
};

const pagePath = {
    bnbCalendar: "/pages/tbnb/pages/common/calendarV3/index",
    bnbHotelListCn: "/pages/tbnb/pages/main/unitList/index",
};

const qmarkData_bnb = {
    appcode: "nnc_module_qunar_tbnb",
    bizType: "tujia",
    page: "xcx_home",
    module: "homecard",
};
const qmarkData_hotel_bnb = {
    appcode: "nnc_module_qunar_tbnb",
    bizType: "tujia",
    page: "xcx_hotel_home",
    module: "hotelhomecard",
};
const qmarkData_hotel = {
    appcode: "nnc_module_qunar_hotel",
    bizType: "hotel",
    page: "hotel_index",
    module: "hotel_search",
};

const bnbCode = "1690";
const hotelCode = "qmphome";

const sendRequest = newWatcher.apiWatcher("hotel");

function isIOS() {
    const app = getApp();
    var { model = "" } = app.globalData.systemInfo || {};
    return /(ipad)|(iphone)/.test(model.toLowerCase());
}
class BnbSearchBox extends React.Component {
    constructor(props) {
        super(props);
        this.checkLogin();
        this.state = {
            tabList: [
                {
                    text: "国内·港澳台",
                    type: "dayRoom",
                    onClick: this.setCurrentTab,
                    isHoursRoom: false,
                },
                {
                    renderLine: true,
                },
                {
                    text: "海外",
                    type: "intlRoom",
                    onClick: this.setCurrentTab,
                    isHoursRoom: false,
                },
                {
                    renderLine: true,
                },
                {
                    text: "钟点房",
                    type: "hoursRoom",
                    onClick: this.setCurrentTab,
                    isHoursRoom: true,
                },
                {
                    renderLine: true,
                },
                {
                    text: "民宿",
                    type: "bnb",
                    onClick: this.setCurrentTab,
                    isHoursRoom: false,
                },
            ],

            currentTab: "bnb", // 当前tab
            isLocating: false, //标识是否正在定位中
            // 搜索条件信息对象
            searchInfo: {
                ...defaultSearchInfo,
            },
            // 日期信息对象
            dates: {},
            // 凌晨房，白名单配置信息
            yesterdayAndTodayConfig: {
                midNightFlag: false, // 是否开启选择昨天今日房
                cityId: 0, // 当前配置信息的城市id
            },
            // 本地时间信息
            localTimeAndCityType: {
                cityId: 48, // 城市id
                localTime: new Date().getTime(), // 当地时间
                localTimeStr: "",
                cityTerritoryType: 1, // 城市类型
            },
            qmarkData:
                props.source == "bnb" ? qmarkData_bnb : qmarkData_hotel_bnb,
            code: props.source == "bnb" ? bnbCode : hotelCode,
        };
        // 民宿tab是否用新卡片
        // this.isTJNew = false;
    }

    componentDidMount() {
        console.log("=====didmount");
        this.initSearch(this);
        // // 触发页面onShow
        this.pageOnShowListener = EventEmitter.addListener(
            eventType.bnbSearchBox,
            this.pageOnShowEmitted,
        );
        this.getToken();
    }

    componentWillUnmount() {
        this.pageOnShowListener &&
            this.pageOnShowListener.removeListener(eventType.bnbSearchBox);
    }

    // 触发onShow后更新
    pageOnShowEmitted = () => {
        this.getQueryCityInfo();
        this.checkLogin();
    };

    // 初始化搜索信息
    initSearch = async (_this) => {
        // 初始化城市
        const { city: lastCity, cityUrl: lastCityUrl } =
            getStorageSync(storageKey.dayRoomLast) || {};
        const { city, cityUrl } = getStorageSync(storageKey.dayRoom) || {};
        const { qmarkData } = _this.state;
        // const _this = this;
        // 名称和url都不同，说明酒店切换了城市
        if (city && cityUrl && city !== lastCity && cityUrl !== lastCityUrl) {
            try {
                const searchInfo = {
                    cityId: cityUrl,
                    cityName: city,
                    pinYin: "",
                };
                // 通过反查获取途家城市id
                const tjCityRes = await _this.promiseRequest({
                    host: "https://mpclient.tujia.com",
                    service:
                        "/bnbapp-node-h5/h5/city/getCitiesOfInlandProvince/bnb",
                    data: {
                        parameter: { city },
                    },
                });
                if (tjCityRes?.data) {
                    searchInfo.cityId = Number(tjCityRes.data);
                }
                _this.getDates(
                    _this.isTjCity(searchInfo.cityId)
                        ? searchInfo.cityId
                        : defaultSearchInfo.cityId,
                );
                _this.setState({
                    searchInfo: {
                        ...defaultSearchInfo,
                        ...searchInfo,
                        latitude: 0,
                        longitude: 0,
                    },
                });
                // 修改 WX_HOTEL_CITY_LAST 缓存
                setStorageSync(storageKey.dayRoomLast, { city, cityUrl });
                // 同步到民宿缓存SEARCH_RESULR缓存 与searchInfo区别 不存经纬度
                _this.setBnbSearchStorage({
                    ...defaultSearchInfo,
                    ...searchInfo,
                });
                LogQmark({
                    ...qmarkData,
                    operType: "show",
                    id: "homeshow",
                    ext: {
                        city: searchInfo?.cityName,
                        cityid: searchInfo?.cityId,
                    },
                });
            } catch (error) {
                LogQmark({
                    ...qmarkData,
                    operType: "show",
                    id: "homeshow",
                    ext: {
                        city: "",
                        cityid: "",
                    },
                });
                console.log("error", error);
            }
        } else {
            // 酒店缓存无城市 或未切换城市 获取民宿缓存城市
            const { data } = _this.getBnbSearchStorage();
            if (!data?.cityId || !data?.cityName) {
                // 民宿缓存无城市 进行定位
                _this.getMyLocation();
            } else {
                const keywordSuggestType =
                    data.keywordSuggestType &
                        parseInt(data.keywordSuggestType, 10) || "1";
                const standardName = data.standardName || "";
                _this.setState({
                    searchInfo: {
                        ...data,
                        keywordSuggestType,
                        standardName,
                    },
                });
                _this.getDates(
                    _this.isTjCity(data.cityId)
                        ? data.cityId
                        : defaultSearchInfo.cityId,
                );
                LogQmark({
                    ...qmarkData,
                    operType: "show",
                    id: "homeshow",
                    ext: {
                        city: data?.cityName,
                        cityid: data?.cityId,
                    },
                });
            }
        }
    };

    // 初始化日期
    getDates = (cityId) => {
        const { checkInDate, checkOutDate } =
            getStorageSync(storageKey.hotelDate) || {};
        const { checkInDate: lastCheckInDate, checkOutDate: lastCheckOutDate } =
            getStorageSync(storageKey.hotelDateLast) || {};
        let begin = "";
        let end = "";
        // 酒店日期存在且不等于上次出入的日期 说明酒店切换了日期
        if (
            checkInDate &&
            checkOutDate &&
            checkInDate != lastCheckInDate &&
            checkOutDate != lastCheckOutDate
        ) {
            begin = checkInDate;
            end = checkOutDate;
            this.setStorage(storageKey.hotelDateLast, {
                checkInDate: checkInDate,
                checkOutDate: checkOutDate,
            });
        } else {
            const dateStrs = this.getBnbDate();
            begin = dateStrs?.beginDate;
            end = dateStrs?.endDate;
        }
        if (begin) {
            begin = new Date(begin);
        }
        if (end) {
            end = new Date(end);
        }
        const sDate = begin || new Date();
        const eDate = end || this.addDays(new Date(), 1);

        this.setDate(sDate, eDate, cityId);
    };

    /**
     * 日期赋值 初始化（可能带参数）；城市关键词列表页返回；日历返回
     * @param sDate 入住日期
     * @param eDate 离店日期
     * @param cityId 城市id
     */
    setDate = async (sDate, eDate, cityId) => {
        let isOpenYesterday = false;
        let localTimeAndCityType = {};
        try {
            const yesterdayAndTodayConfig =
                await this.getYesterdayConfig(cityId);
            isOpenYesterday = yesterdayAndTodayConfig?.midNightFlag;
            localTimeAndCityType = await this.getLocalTimeConfig(cityId);
        } catch (error) {
            isOpenYesterday = this.state.yesterdayAndTodayConfig?.midNightFlag;
            localTimeAndCityType = this.state.localTimeAndCityType;
        }

        const {
            beginDate,
            endDate,
            checkInMonth,
            checkOutMonth,
            checkInDay,
            checkOutDay,
            interval,
            sWeekText,
            eWeekText,
            isBeginYesterday,
            isEndToday,
            zeroRoomTipDate,
        } = this.getCheckDate(
            sDate,
            eDate,
            localTimeAndCityType,
            isOpenYesterday,
        );
        const dateStrs = {
            beginDate,
            endDate,
        };

        this.setBnbDate(dateStrs);
        this.setState({
            dates: {
                beginDate,
                endDate,
                checkInMonth,
                checkOutMonth,
                checkInDay,
                checkOutDay,
                interval,
                sWeekText,
                eWeekText,
                isBeginYesterday,
                isEndToday,
                zeroRoomTipDate,
            },
        });
    };

    checkLogin = async () => {
        const res = (await user.checkLogin()) || {};
        if (res && res.ret && res.data.isLogin) {
            this.setState({
                isLogin: true,
            });
        } else {
            this.setState({
                isLogin: false,
            });
        }
    };

    getToken = () => {
        const _this = this;
        sendRequest({
            param: {
                qrt: "h_querymini_version",
                source: "mini",
            },
            service: "/h_gateway",
            ignoreStatus: true,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            success: (res) => {
                const { bstatus: { code } = {}, data } = res;
                if (code === 0 && data && data.wechat) {
                    _this.token = data.wechat;
                }
            },
            fail: (err) => {},
        });
    };

    // 获取民宿缓存城市、关键词、日期
    getQueryCityInfo = () => {
        const { data } = this.getBnbSearchStorage();
        const { searchInfo } = this.state;
        // 民宿缓存存在
        if (data?.cityId) {
            // 一框或二框修改过，经纬度置空 使用!=是防止null和空字符串情况
            // 因为存在点击一二框但是没选中直接返回的情况
            if (
                data.cityId != searchInfo.cityId ||
                data.standardName != searchInfo.standardName
            ) {
                const keywordSuggestType =
                    data.keywordSuggestType &
                        parseInt(data.keywordSuggestType, 10) || "1";
                const standardName = data.standardName || "";
                this.setState({
                    searchInfo: {
                        ...data,
                        keywordSuggestType,
                        standardName,
                        latitude: 0,
                        longitude: 0,
                    },
                });
            }
            this.getDates(
                this.isTjCity(data.cityId)
                    ? data.cityId
                    : defaultSearchInfo.cityId,
            );
        }
    };

    // 获取定位
    getMyLocation = (bool) => {
        const hideLoadingText = (time) =>
            new Promise((resolve) => setTimeout(resolve, time));
        const { qmarkData } = this.state;
        this.setState(
            {
                isLocating: true,
            },
            () => {
                Promise.all([
                    hideLoadingText(600),
                    this.getCurrentCityUrl(),
                ]).then((res) => {
                    const locationInfo = res[1];
                    const { latitude, longitude } = locationInfo;
                    // 经纬度存在
                    this.setState({
                        searchInfo: { ...locationInfo },
                        isLocating: false,
                    });
                    if (bool) {
                        // 点击触发
                        LogQmark({
                            ...qmarkData,
                            operType: "click",
                            id: "locationclick",
                            ext: {
                                city: locationInfo?.cityName,
                                cityid: locationInfo?.cityId,
                            },
                        });
                    } else {
                        // 非点击触发 及页面曝光触发，上报曝光埋点
                        LogQmark({
                            ...qmarkData,
                            operType: "show",
                            id: "homeshow",
                            ext: {
                                city: locationInfo?.cityName,
                                cityid: locationInfo?.cityId,
                            },
                        });
                    }
                    this.getDates(
                        this.isTjCity(locationInfo.cityId)
                            ? locationInfo.cityId
                            : defaultSearchInfo.cityId,
                    );
                    if (latitude && longitude) {
                        delete locationInfo.latitude;
                        delete locationInfo.latitude;
                    }
                    this.setBnbSearchStorage(locationInfo);
                });
            },
        );
    };

    // 获取定位信息
    getCurrentCityUrl = () => {
        // getLocation未返回cityUrl, 先获取到经纬度再获取cityUrl
        const _this = this;
        return new Promise((resolve) => {
            this.getLocationData().then((res) => {
                const { latitude, longitude } = res;
                const _param = {
                    latitude: latitude,
                    longitude: longitude,
                    // 坐标系：0，百度经纬度；1，国测局经纬度; 2, GPS经纬度
                    coordType: 2,
                };
                request({
                    host: "https://mpclient.tujia.com",
                    service: "/bingo/mp/city/geocoder",
                    method: "POST",
                    ignoreStatus: true,
                    data: {
                        ..._param,
                    },
                    success: function (geoRes) {
                        let locationInfo = {};
                        if (!geoRes?.data?.cityId) {
                            // 定位信息获取失败
                            locationInfo = {
                                ...defaultSearchInfo,
                            };
                        } else {
                            const {
                                cityId,
                                cityName,
                                address,
                                oversea,
                                cityPinYin,
                            } = geoRes?.data;

                            locationInfo = {
                                cityId,
                                cityName,
                                pinYin: cityPinYin,
                                keywordSuggestType: oversea ? "2" : "1",
                                enumSuggestionConditionType: "5",
                                standardName: address,
                                suggestionConditionValue: "",
                                suggestLinkUrl: "",
                            };
                            if (latitude && longitude) {
                                const coordData = _this.gcj_encrypt(
                                    latitude,
                                    longitude,
                                );
                                locationInfo.latitude = coordData.lat;
                                locationInfo.longitude = coordData.lon;
                                locationInfo.suggestionConditionValue = `14_${coordData.lon},${coordData.lat}`;
                            }
                        }
                        resolve(locationInfo);
                    },
                    fail: function () {
                        resolve(defaultSearchInfo);
                    },
                });
            });
        });
    };

    // 获取定位信息
    getLocationData = () => {
        return new Promise((resolve) => {
            getLocation({
                success: (res) => {
                    resolve(res);
                },
                fail: () => {
                    resolve(defaultSearchInfo);
                },
            });
        });
    };

    // 选择城市
    selectCity = () => {
        const { searchInfo, dates, qmarkData, code } = this.state;
        LogQmark({
            ...qmarkData,
            operType: "click",
            id: "firstbarclick",
        });

        const params = {
            isOversea: searchInfo.keywordSuggestType == "2",
            checkInDate: dates.beginDate,
            checkOutDate: dates.endDate,
            code,
        };
        // 所有参数拼到h5链接：https://m.tujia.com/tbnb/common/citysuggest
        const jumpUrl = `https://m.tujia.com/tbnb/common/citysuggest?${this.stringifyURLParam(params)}`;
        // 最终跳转城市页链接（需要encode）
        const jumpNavigateUrl = `/pages/tbnb/pages/common/webview/index?h5url=${encodeURIComponent(jumpUrl)}&needLogin=false`;
        navigateTo({ url: jumpNavigateUrl });
    };

    // 关键词搜索
    selectKeywords = () => {
        const { searchInfo, dates, qmarkData, code } = this.state;
        LogQmark({
            ...qmarkData,
            operType: "click",
            id: "secondbarclick",
        });
        const { cityId, cityName, pinYin, keywordSuggestType, standardName } =
            searchInfo;

        const params = {
            cityId: cityId,
            cityName: cityName,
            cityPinYin: pinYin,
            // 因为进入时已经做过城市反查，这里的cityType只作为兜底使用，即反查失败时使用
            cityType: this.isTjCity(cityId) ? "tujia" : "qunar",
            isOversea: keywordSuggestType == "2",
            standardName: standardName,
            useRouterGo: true,
            checkInDate: dates.beginDate,
            checkOutDate: dates.endDate,
            code,
        };
        // 所有参数拼到h5链接：https://m.tujia.com/tbnb/common/citysuggest
        const jumpUrl = `https://m.tujia.com/tbnb/common/keywordsuggest?${this.stringifyURLParam(params)}`;
        // 最终跳转城市页链接（需要encode）
        const jumpNavigateUrl = `/pages/tbnb/pages/common/webview/index?h5url=${encodeURIComponent(jumpUrl)}&needLogin=false`;
        navigateTo({
            url: jumpNavigateUrl,
        });
    };

    // 开始搜索 进入列表页
    searchHotel = async () => {
        const { searchInfo, dates, qmarkData, code } = this.state;
        const {
            cityId,
            standardName,
            enumSuggestionConditionType,
            suggestionConditionValue,
            latitude = "",
            longitude = "",
        } = searchInfo;
        const params = {
            cityId,
            standardName,
            startDate: dates.beginDate,
            endDate: dates.endDate,
            pageType: "hotel",
            // 因为进入时已经做过城市反查，这里的cityType只作为兜底使用，即反查失败时使用
            cityType: this.isTjCity(cityId) ? "tujia" : "qunar",
            code,
        };
        if (enumSuggestionConditionType) {
            if (enumSuggestionConditionType == 13) {
                params.cityId = `scenic${cityId}`;
            } else if (enumSuggestionConditionType == 14) {
                params.cityId = `scenic${cityId}`;
            } else if (
                latitude &&
                longitude &&
                enumSuggestionConditionType == 5
            ) {
                params.location = `${enumSuggestionConditionType}-14_${longitude},${latitude},g`;
            } else if (suggestionConditionValue) {
                const val = `${enumSuggestionConditionType}-${suggestionConditionValue}`;
                if (enumSuggestionConditionType == 5) {
                    params.location = val;
                } else if (enumSuggestionConditionType == 99) {
                    params.noEntity = val;
                } else {
                    params.filters = val;
                }
            }
        }
        LogQmark({
            ...qmarkData,
            operType: "click",
            id: "searchbar",
            ext: {
                city: searchInfo?.cityName,
                cityid: searchInfo?.cityId,
            },
        });
        navigateTo({
            url: `${pagePath.bnbHotelListCn}?${this.stringifyURLParam(params)}`,
        });
    };

    // 清除关键词
    clearKeywords = () => {
        const { searchInfo } = this.state;
        searchInfo.standardName = "";
        searchInfo.enumSuggestionConditionType = 0;
        searchInfo.latitude = 0;
        searchInfo.longitude = 0;
        searchInfo.suggestLinkUrl = "";
        searchInfo.suggestionConditionValue = "";
        this.setState({
            searchInfo: { ...searchInfo },
        });

        delete searchInfo.latitude;
        delete searchInfo.longitude;
        this.setBnbSearchStorage(searchInfo);
    };

    // 选择日期，打开日历
    selectDate = () => {
        const { searchInfo, dates, code } = this.state;
        let cityId = searchInfo.cityId;
        if (!this.isTjCity(cityId)) {
            // 如果cityId不为途家id，走默认城市兜底
            cityId = defaultSearchInfo.cityId;
        }
        const params = {
            beginDate: dates.beginDate,
            endDate: dates.endDate,
            cityId,
            code,
        };
        const bnbCalendar = pagePath.bnbCalendar;
        navigateTo({ url: `${bnbCalendar}?${this.stringifyURLParam(params)}` });
    };

    // 封装promise请求
    promiseRequest = (data) => {
        return new Promise((resolve) => {
            request({
                ignoreStatus: true,
                ...data,
                method: data.method || "POST",
                success: (res) => {
                    resolve(res);
                },
                fail: (err) => {
                    resolve(err);
                },
            });
        });
    };
    /**
     * 获取昨日售卖配置信息
     * @param {String} cityId 城市id
     * @returns {Promise<void>}
     */
    getYesterdayConfig = async (cityId) => {
        const { searchInfo, yesterdayAndTodayConfig } = this.state;
        cityId = Number(cityId);
        if (!cityId) {
            cityId = Number(searchInfo?.cityId);
        }
        // 不同城市id 才会去请求接口
        if (cityId !== yesterdayAndTodayConfig.cityId) {
            try {
                const { data } = await this.promiseRequest({
                    host: "https://mpclient.tujia.com",
                    service: "/bingo/h5/midnight/getwhitecity/bnb",
                    data: {
                        cityId,
                    },
                });
                this.setState({
                    yesterdayAndTodayConfig: {
                        midNightFlag: data?.midNightFlag || false,
                        cityId,
                    },
                });
                return Promise.resolve(data);
            } catch (error) {
                // 报错或者没有配置信息 设置默认值
                this.setState({
                    yesterdayAndTodayConfig: {
                        midNightFlag: false, // 是否开启选择昨天今日房
                        cityId: 0,
                    },
                });
                return Promise.reject(error);
            }
        } else {
            return Promise.resolve(yesterdayAndTodayConfig);
        }
    };

    /**
     * 获取目的城市时间和城市类型
     * @param {String} cityId 城市id
     * @returns {Promise<void>}
     */
    getLocalTimeConfig = async (cityId) => {
        const { searchInfo, localTimeAndCityType } = this.state;
        cityId = Number(cityId);
        if (!cityId) {
            cityId = Number(searchInfo?.cityId);
        }
        // 不同城市id 才会去请求接口
        if (cityId !== localTimeAndCityType.cityId) {
            try {
                const { data } = await this.promiseRequest({
                    host: "https://mpclient.tujia.com",
                    service: "/bnbapp-node-h5/h5/city/getLocalTime/bnb",
                    data: {
                        cityId,
                    },
                });
                this.setState({
                    localTimeAndCityType: {
                        localTime: data?.localTime || new Date().getTime(), // 当地时间
                        localTimeStr: data?.localTimeStr || "",
                        cityTerritoryType: data?.cityTerritoryType || 1, // 城市类型
                        cityId,
                    },
                });
                return Promise.resolve(data);
            } catch (error) {
                // 报错或者没有配置信息 设置默认值
                this.setState({
                    localTimeAndCityType: {
                        cityId: 48,
                        localTime: new Date().getTime(),
                        localTimeStr: "",
                        cityTerritoryType: 1,
                    },
                });
                return Promise.reject(error);
            }
        } else {
            return Promise.resolve(localTimeAndCityType);
        }
    };

    /**
     * 获取正确日期
     * @param sDate 入住日期 Date格式
     * @param eDate  离店日期 Date格式
     * @param localTimeAndCityType 本地时间对象包括时间和海内外
     * @param isOpenYesterday 是否凌晨入住 boolean
     * @returns 实际入离日期 显示入离日期 间夜
     */
    getCheckDate = (
        sDate,
        eDate,
        localTimeAndCityType = {},
        isOpenYesterday = false,
    ) => {
        const day1 = 24 * 60 * 60 * 1000;
        const zeroRoomTipDate =
            isOpenYesterday &&
            dateFormat(new Date(new Date().getTime() - millisecond), "m月d日");
        let yesterday = new Date(new Date().setHours(0, 0, 0, 0) - day1);
        let currentDay = new Date(new Date().setHours(0, 0, 0, 0));
        let tomorrow = new Date(new Date().setHours(0, 0, 0, 0) + day1);
        // 凌晨入住今天显示日期加一
        // let adddOneDay = false;
        // 凌晨入住显示凌晨
        let isBeginYesterday = false;
        // 凌晨入住显示中午
        let isEndToday = false;
        // HW-105 海外的时间赋值为当地的时间
        if (localTimeAndCityType?.localTimeStr) {
            yesterday = new Date(
                new Date(
                    localTimeAndCityType.localTimeStr + "T00:00:00",
                ).setHours(0, 0, 0, 0) - day1,
            );
            currentDay = new Date(
                new Date(
                    localTimeAndCityType.localTimeStr + "T00:00:00",
                ).setHours(0, 0, 0, 0),
            );
            tomorrow = new Date(
                new Date(
                    localTimeAndCityType.localTimeStr + "T00:00:00",
                ).setHours(0, 0, 0, 0) + day1,
            );
        } else if (
            localTimeAndCityType?.cityTerritoryType === 3 &&
            localTimeAndCityType?.localTime
        ) {
            // HW-118替换toLocaleDateString方法
            yesterday = new Date(
                new Date(localTimeAndCityType.localTime).setHours(0, 0, 0, 0) -
                    day1,
            );
            currentDay = new Date(
                new Date(localTimeAndCityType.localTime).setHours(0, 0, 0, 0),
            );
            tomorrow = new Date(
                new Date(localTimeAndCityType.localTime).setHours(0, 0, 0, 0) +
                    day1,
            );
        }
        sDate = isNaN(sDate) ? currentDay : sDate;
        eDate = isNaN(eDate) ? tomorrow : eDate;
        // 是否开启凌晨入住
        if (isOpenYesterday) {
            // 开启凌晨入住判断开始时间是否比今天还早 是的话改为昨天
            if (this.dateCompare(sDate, currentDay) < 0) {
                sDate = yesterday;
                // adddOneDay = true;
                isBeginYesterday = true;
            }
            // 开启凌晨入住判断离店时间是否比明天天还早 是的话改为今天
            if (this.dateCompare(eDate, tomorrow) < 0) {
                eDate = currentDay;
                isEndToday = true;
            }
        } else {
            // 未开凌晨入住 判断今住明离
            if (this.dateCompare(sDate, currentDay) < 0) {
                sDate = currentDay;
            }
            if (this.dateCompare(eDate, tomorrow) < 0) {
                eDate = tomorrow;
            }
        }

        const beginDate = dateFormat(sDate, "yyyy-mm-dd");
        const endDate = dateFormat(eDate, "yyyy-mm-dd");
        const checkInDay = dateFormat(new Date(sDate), "d");
        const checkOutDay = dateFormat(new Date(eDate), "d");
        const checkInMonth = dateFormat(new Date(sDate), "m");
        const checkOutMonth = dateFormat(new Date(eDate), "m");
        const interval = Math.floor(
            (eDate - sDate) / (1 * 24 * 60 * 60 * 1000),
        );
        const sWeekText = this.dateWeek(beginDate, isOpenYesterday, "start");
        const eWeekText = this.dateWeek(endDate, isOpenYesterday, "end");

        return {
            beginDate,
            endDate,
            checkInMonth,
            checkOutMonth,
            checkInDay,
            checkOutDay,
            interval,
            sWeekText,
            eWeekText,
            isBeginYesterday,
            isEndToday,
            zeroRoomTipDate,
        };
    };

    // 日期大小比较
    dateCompare = (date1, date2) => {
        if (date1 instanceof Date && date2 instanceof Date) {
            return date1 < date2 ? -1 : date1 > date2 ? 1 : 0;
        } else {
            throw new Error(date1 + "-" + date2);
        }
    };

    // 日期+
    addDays = (date, dayNum) => {
        return new Date(date.getTime() + 86400000 * dayNum);
    };

    // 计算日期星期几
    /**
     * someDate:计算日期
     * todayFlag:是否根据今天日期计算昨天今天明天
     * isOpenYesterday:是否是凌晨房
     * type:入店日期还是离店日期
     */
    dateWeek = (someDate, isOpenYesterday = false, type = "") => {
        const { keywordSuggestType } = this.state.searchInfo;
        const currentDate =
            typeof someDate === "string"
                ? someDate.replace(/-/g, "/")
                : someDate;
        const today = new Date(dateFormat(new Date(), "yyyy/mm/dd"));
        const caldate = new Date(
            dateFormat(new Date(currentDate), "yyyy/mm/dd"),
        );
        const weekdayArr = [
            "周日",
            "周一",
            "周二",
            "周三",
            "周四",
            "周五",
            "周六",
        ];
        let weekday = "";
        // 避免海外今明天显示不准，只显示星期几
        if (keywordSuggestType == 1) {
            const someDateDay = (
                    caldate.getTime() /
                    (24 * 60 * 60 * 1000)
                ).toFixed(2),
                todayDay = (today.getTime() / (24 * 60 * 60 * 1000)).toFixed(2),
                dayGap = someDateDay - todayDay;
            if (dayGap === -1) {
                weekday =
                    isOpenYesterday && type === "start"
                        ? "今晨"
                        : weekdayArr[caldate.getDay()];
            } else if (dayGap === 0) {
                weekday = isOpenYesterday && type === "end" ? "中午" : "今天";
            } else if (dayGap === 1) {
                weekday = "明天";
            } else if (dayGap === 2) {
                weekday = "后天";
            } else {
                weekday = weekdayArr[caldate.getDay()];
            }
        } else {
            weekday = weekdayArr[caldate.getDay()];
        }
        return weekday;
    };

    // 获取民宿缓存
    getBnbSearchStorage = () => {
        const { data = {}, from = "" } = getStorageSync(storageKey.bnb) || {};
        return { data, from };
    };

    /**
     * 设置民宿缓存 目前仅在初始化、获取定位、清除关键词时使用
     * @param {*} value 缓存数据
     */
    setBnbSearchStorage = (data) => {
        const { from } = this.getBnbSearchStorage();
        this.setStorage(storageKey.bnb, {
            from,
            data,
        });
    };

    // 设置缓存
    setStorage = (key, value, successCallback, errorCallback) => {
        setStorage({
            key: key,
            data: value,
        })
            .then(() => {
                if (typeof successCallback === "function") {
                    successCallback();
                }
            })
            .catch(() => {
                if (typeof errorCallback === "function") {
                    errorCallback();
                }
            });
    };

    getBnbDate = () => {
        const globalDateStr = getStorageSync(storageKey.bnbDate);
        let dates = {};
        try {
            const storageDate = JSON.parse(globalDateStr) || {};
            dates.beginDate =
                storageDate?.beginDate || dateFormat(new Date(), "yyyy-mm-dd");
            dates.endDate =
                storageDate?.endDate ||
                dateFormat(this.addDays(new Date(), 1), "yyyy-mm-dd");
        } catch (error) {
            console.log(error);
            dates = { beginDate: "", endDate: "" };
        }
        return dates;
    };

    setBnbDate = ({ beginDate = "", endDate = "" }) => {
        const dates = {
            beginDate,
            endDate,
        };
        this.setStorage(storageKey.bnbDate, JSON.stringify(dates));
    };

    // tab点击
    setCurrentTab = (type, isHoursRoom) => {
        LogQmark({
            ...qmarkData_hotel,
            operType: "click",
            id: "tab",
            ext: { tabName: type },
        });
        if (type !== "bnb") {
            // hideBnb在回调中执行，避免缓存未更新完成，tab就已切换过去，导致数据不一致
            this.setStorage(storageKey.hotelTab, { currentTab: type }, () => {
                this.props.hideBnb({ type, isHoursRoom });
            });
        }
    };

    // 经纬度转换 WGS-84 to GCJ-02
    gcj_encrypt = (wgsLat, wgsLon) => {
        if (this.outOfChina(wgsLat, wgsLon)) {
            return { lat: wgsLat, lon: wgsLon };
        }

        const d = this.delta(wgsLat, wgsLon);
        return this.bd_encrypt(wgsLat + d.lat, wgsLon + d.lon);
    };
    // 经纬度转换 GCJ-02 to BD-09
    bd_encrypt = (gcjLat, gcjLon) => {
        const x = gcjLon;
        const y = gcjLat;
        const z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
        const theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
        const bdLon = z * Math.cos(theta) + 0.0065;
        const bdLat = z * Math.sin(theta) + 0.006;
        return { lat: bdLat, lon: bdLon };
    };
    // 经纬度转换使用
    delta = (lat, lon) => {
        const a = 6378245.0;
        const ee = 0.00669342162296594323;
        let dLat = this.transformLat(lon - 105.0, lat - 35.0);
        let dLon = this.transformLon(lon - 105.0, lat - 35.0);
        const radLat = (lat / 180.0) * PI;
        let magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        let sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtMagic)) * PI);
        dLon = (dLon * 180.0) / ((a / sqrtMagic) * Math.cos(radLat) * PI);
        return { lat: dLat, lon: dLon };
    };
    // 经纬度转换使用
    outOfChina = (latitude, longitude) => {
        if (longitude < 72.004 || longitude > 137.8347) {
            return true;
        }
        if (latitude < 0.8293 || latitude > 55.8271) {
            return true;
        }
        return false;
    };
    // 经纬度转换使用
    transformLat = (x, y) => {
        let ret =
            -100.0 +
            2.0 * x +
            3.0 * y +
            0.2 * y * y +
            0.1 * x * y +
            0.2 * Math.sqrt(Math.abs(x));
        ret +=
            ((20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) *
                2.0) /
            3.0;
        ret +=
            ((20.0 * Math.sin(y * PI) + 40.0 * Math.sin((y / 3.0) * PI)) *
                2.0) /
            3.0;
        ret +=
            ((160.0 * Math.sin((y / 12.0) * PI) +
                320 * Math.sin((y * PI) / 30.0)) *
                2.0) /
            3.0;
        return ret;
    };
    // 经纬度转换使用
    transformLon = (x, y) => {
        let ret =
            300.0 +
            x +
            2.0 * y +
            0.1 * x * x +
            0.1 * x * y +
            0.1 * Math.sqrt(Math.abs(x));
        ret +=
            ((20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) *
                2.0) /
            3.0;
        ret +=
            ((20.0 * Math.sin(x * PI) + 40.0 * Math.sin((x / 3.0) * PI)) *
                2.0) /
            3.0;
        ret +=
            ((150.0 * Math.sin((x / 12.0) * PI) +
                300.0 * Math.sin((x / 30.0) * PI)) *
                2.0) /
            3.0;
        return ret;
    };

    /**
     * 判断是否是途家city
     * @param {*} cityId
     */
    isTjCity = (cityId) => {
        // 非空 && (数字 或 以scenic、region开头的字符串（大景区）)
        return (
            cityId &&
            (!isNaN(cityId) ||
                cityId.startsWith("scenic") ||
                cityId.startsWith("region"))
        );
    };

    // 拼接链接参数
    stringifyURLParam = (param) => {
        let rstString = "";
        for (let key in param) {
            rstString += `${key}=${param[key]}&`;
        }
        rstString = rstString.substr(0, rstString.length - 1);
        return rstString;
    };

    render() {
        return (
            <View
                className={
                    this.props.isNew
                        ? "bhotel-container"
                        : "old-hotel-container"
                }
            >
                <View
                    className={
                        this.props.isNew ? "bhotel-search" : "old-hotel-search"
                    }
                >
                    {this.props.source === "hotel" ? (
                        <View className="bhotel-tab">
                            {this.state.tabList.map((tabItem) => {
                                return (
                                    <View>
                                        {tabItem.renderLine ? (
                                            <View className="flex-tab">
                                                <View
                                                    className={
                                                        this.props.isNew
                                                            ? "septal-line"
                                                            : "old-septal-line"
                                                    }
                                                ></View>
                                            </View>
                                        ) : (
                                            <View
                                                className="flex-tab"
                                                onClick={() =>
                                                    tabItem.onClick(
                                                        tabItem.type,
                                                        tabItem.isHoursRoom,
                                                    )
                                                }
                                            >
                                                <View
                                                    className={`${this.props.isNew ? "tab-item" : "old-tab-item"} ${tabItem.type === this.state.currentTab ? (this.props.isNew ? "tab-item-cur" : "old-tab-item-cur") : ""}`}
                                                >
                                                    <View className="item-box">
                                                        <Text className="tab-text">
                                                            {tabItem.text}
                                                        </Text>
                                                        {tabItem.type ===
                                                        this.state
                                                            .currentTab ? (
                                                            <View className="linearBar"></View>
                                                        ) : null}
                                                    </View>
                                                    {/* 民宿·客栈 特惠tag */}
                                                    {/* {tabItem.type === 'bnb' ? <span class="tab-tag"></span> : ''} */}
                                                </View>
                                            </View>
                                        )}
                                    </View>
                                );
                            })}
                        </View>
                    ) : (
                        <View className={"bhotel-margin"}></View>
                    )}
                    <View
                        className={
                            this.props.isNew
                                ? "hotel-address"
                                : "old-hotel-address"
                        }
                    >
                        <View
                            className={
                                this.props.isNew ? "address" : "old-address"
                            }
                            onClick={this.selectCity}
                        >
                            <Text class="locate-txt">
                                {this.state.isLocating
                                    ? "努力定位中..."
                                    : this.state.searchInfo.cityName}
                            </Text>
                        </View>
                        <View
                            className={
                                this.props.isNew ? "position" : "old-position"
                            }
                            onClick={() => this.getMyLocation(true)}
                        >
                            {/* <div class="position-icon"></div> */}
                            {this.props.isNew ? (
                                <Text className="position-icon icon-l"></Text>
                            ) : (
                                <View class="old-position-icon"></View>
                            )}

                            <View>当前位置</View>
                        </View>
                    </View>
                    <View
                        className={
                            this.props.isNew ? "hotel-date" : "old-hotel-date"
                        }
                        onClick={this.selectDate}
                        style={{
                            borderBottomWidth: this.state.dates.zeroRoomTipDate
                                ? 0
                                : 1,
                        }}
                    >
                        <View
                            className={
                                this.props.isNew
                                    ? "select-date"
                                    : "old-select-date"
                            }
                        >
                            <View className="check-box">
                                {!this.props.isNew ? (
                                    <React.Fragment>
                                        <Text className="old-small-txt">
                                            入住
                                        </Text>
                                        <Text className="old-small-txt">
                                            {this.state.dates.sWeekText}
                                        </Text>
                                    </React.Fragment>
                                ) : null}

                                <View className="date-box">
                                    <Text
                                        className={
                                            this.props.isNew
                                                ? "date"
                                                : "old-date"
                                        }
                                        style={{
                                            marginBottom:
                                                !isIOS() && this.props.isNew
                                                    ? "-1rpx"
                                                    : "-2rpx",
                                        }}
                                    >
                                        {this.state.dates.checkInMonth}
                                    </Text>
                                    <Text
                                        className={
                                            this.props.isNew
                                                ? "desc"
                                                : "old-desc"
                                        }
                                    >
                                        月
                                    </Text>
                                    <Text
                                        className={
                                            this.props.isNew
                                                ? "date"
                                                : "old-date"
                                        }
                                        style={{
                                            marginBottom:
                                                !isIOS() && this.props.isNew
                                                    ? "-1rpx"
                                                    : "-2rpx",
                                        }}
                                    >
                                        {this.state.dates.checkInDay}
                                    </Text>
                                    <Text
                                        className={
                                            this.props.isNew
                                                ? "desc"
                                                : "old-desc"
                                        }
                                    >
                                        日
                                    </Text>
                                    {this.props.isNew ? (
                                        <Text className="small-txt">
                                            {this.state.dates.sWeekText}
                                        </Text>
                                    ) : null}
                                </View>
                            </View>
                            <View
                                className={
                                    this.props.isNew ? "line" : "old-line"
                                }
                            ></View>
                            <View className="check-box">
                                {!this.props.isNew ? (
                                    <React.Fragment>
                                        <Text className="old-small-txt">
                                            离店
                                        </Text>
                                        <Text className="old-small-txt">
                                            {this.state.dates.eWeekText}
                                        </Text>
                                    </React.Fragment>
                                ) : null}

                                <View className="date-box">
                                    <Text
                                        className={
                                            this.props.isNew
                                                ? "date"
                                                : "old-date"
                                        }
                                        style={{
                                            marginBottom:
                                                !isIOS() && this.props.isNew
                                                    ? "-1rpx"
                                                    : "-2rpx",
                                        }}
                                    >
                                        {this.state.dates.checkOutMonth}
                                    </Text>
                                    <Text
                                        className={
                                            this.props.isNew
                                                ? "desc"
                                                : "old-desc"
                                        }
                                    >
                                        月
                                    </Text>
                                    <Text
                                        className={
                                            this.props.isNew
                                                ? "date"
                                                : "old-date"
                                        }
                                        style={{
                                            marginBottom:
                                                !isIOS() && this.props.isNew
                                                    ? "-1rpx"
                                                    : "-2rpx",
                                        }}
                                    >
                                        {this.state.dates.checkOutDay}
                                    </Text>
                                    <Text
                                        className={
                                            this.props.isNew
                                                ? "desc"
                                                : "old-desc"
                                        }
                                    >
                                        日
                                    </Text>
                                    {this.props.isNew ? (
                                        <Text className="small-txt">
                                            {this.state.dates.eWeekText}
                                        </Text>
                                    ) : null}
                                </View>
                            </View>
                        </View>
                        <View className="total">
                            共
                            {this.props.isNew ? (
                                <React.Fragment>
                                    {this.state.dates.interval}
                                </React.Fragment>
                            ) : (
                                <Text className="total-num">
                                    {this.state.dates.interval}
                                </Text>
                            )}
                            晚
                        </View>
                    </View>
                    {/* 凌晨房提示 */}
                    {this.state.dates.zeroRoomTipDate ? (
                        <View
                            className={
                                this.props.isNew
                                    ? "zeroRoom-tips"
                                    : "old-zeroRoom-tips"
                            }
                        >
                            <Text className="tips">
                                今晨6点前入住，请选择
                                {this.state.dates.zeroRoomTipDate}入住
                            </Text>
                        </View>
                    ) : null}

                    <View
                        className={
                            this.props.isNew
                                ? "hotel-keywords"
                                : "old-hotel-keywords"
                        }
                    >
                        <View
                            className={
                                this.props.isNew
                                    ? "input-keyword"
                                    : "old-input-keyword"
                            }
                            onClick={this.selectKeywords}
                        >
                            <Text>
                                {this.state.searchInfo.standardName
                                    ? this.state.searchInfo.standardName
                                    : "搜索景点/地标/房源"}
                            </Text>
                        </View>
                        {this.state.searchInfo.standardName ? (
                            <View
                                className="clearkeyword"
                                onClick={this.clearKeywords}
                            >
                                <Text className="icon-c"></Text>
                            </View>
                        ) : null}
                    </View>
                    <View
                        className={
                            this.props.isNew ? "search-btn" : "old-search-btn"
                        }
                        onClick={this.searchHotel}
                    >
                        <View class="btn-icon-right"></View>
                        <Text className="search-text">{"民宿查询"}</Text>
                    </View>
                    <View
                        className={
                            this.props.isNew
                                ? "search-btn-shadow"
                                : "old-search-btn-shadow"
                        }
                    ></View>
                </View>
            </View>
        );
    }
}

export default BnbSearchBox;
