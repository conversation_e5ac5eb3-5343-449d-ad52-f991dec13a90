import { View, Image, Text, Input } from "@tarojs/components";
import React from "react";
import "./index.scss";
class PlatformLoginPhone extends React.Component {
    logIn = () => {
        this.props.logIn();
    };
    bindPhoneChange = (e) => {
        const { detail = {} } = e || {}
        this.props.bindPhoneChange({ e, ...detail });
    };
    user_login_clearInput = () => {
        this.props.user_login_clearInput();
    };
    getCode = () => {
        this.props.getCode();
    };
    bindCodeChange = (e) => {
        const { detail = {} } = e || {}
        this.props.bindCodeChange({ e, ...detail });
    };
    changeSelected = () => {
        this.props.changeSelected();
    };

    clickProtocolText = () => {
        this.props.clickProtocolText();
    };

    clickProtoColToPrivacyPolicy = () => {
        this.props.clickProtoColToPrivacyPolicy();
    };
    render() {
        return (
            <View>
                {this.props.newStyle !== undefined ? (
                    <View class="c-login-wrap">
                        {this.props.newStyle ? (
                            <View class="p-login-new">
                                <View class="hello-box">
                                    <View className="userImg">
                                        <Image
                                            class="pic"
                                            src="https://s.qunarzz.com/f_cms/2023/1688395582353_578962327.png"
                                        ></Image>
                                    </View>
                                    <View className="phone-number">
                                        Hi, 欢迎来到去哪儿
                                    </View>
                                </View>
                                <Image
                                    class="rights"
                                    src={this.props.rightsUrl}
                                ></Image>
                                <View class="content">
                                    <View class="item mobile-item">
                                        <View class="mark">
                                            +86
                                            <Text class="mark-icon g-q-iconfont">
                                                
                                            </Text>
                                        </View>
                                        <View class="input-field mobile-field hide">
                                            <Input
                                                type="number"
                                                maxlength="11"
                                                placeholder={"请输入手机号"}
                                                placeholder-style="color: #CCC;"
                                                onInput={this.bindPhoneChange.bind(
                                                    this,
                                                )}
                                            />
                                            <View
                                                class={
                                                    "clear " +
                                                    this.props.clearIconClass
                                                }
                                                onClick={this.user_login_clearInput.bind(
                                                    this,
                                                )}
                                            >
                                                <Image
                                                    class="clear-img"
                                                    src="https://s.qunarzz.com/wechatapp/common/images/login/clear.png"
                                                ></Image>
                                            </View>
                                        </View>
                                    </View>
                                    <View class="item">
                                        <Text class="mark">验证码</Text>
                                        <View class="input-field sms-code-field">
                                            <Input
                                                type="number"
                                                class="sms-input"
                                                maxlength="6"
                                                placeholder={"请输入验证码"}
                                                placeholder-style="color: #CCC;"
                                                onInput={this.bindCodeChange}
                                            />
                                            <Text
                                                class={
                                                    this.props.canSendSmsFlag
                                                        ? "sms-code "
                                                        : "gray"
                                                }
                                                onClick={this.getCode.bind(
                                                    this,
                                                )}
                                            >
                                                {this.props.smsBtnText}
                                            </Text>
                                        </View>
                                    </View>

                                    <View className="p-login-wrap-protocol-platformLoginPhone">
                                        <View
                                            className="p-login-wrap-changeSelected"
                                            onClick={this.changeSelected.bind(
                                                this,
                                            )}
                                        >
                                            <Text
                                                className={`p-login-wrap-checkbox ${this.props.hasAgreedProtocol ? "radio radio-check" : "radio"}`}
                                            ></Text>
                                            <Text className="p-login-wrap-text">
                                                同意并遵行去哪儿网
                                            </Text>
                                        </View>
                                        <Text
                                            className="p-login-wrap-protocolText"
                                            onClick={this.clickProtocolText.bind(
                                                this,
                                            )}
                                        >
                                            《用户协议》
                                        </Text>
                                        <Text className="p-login-wrap-text">
                                            、
                                        </Text>
                                        <Text
                                            className="p-login-wrap-protocolText"
                                            onClick={this.clickProtoColToPrivacyPolicy.bind(
                                                this,
                                            )}
                                        >
                                            《隐私政策》
                                        </Text>
                                    </View>
                                    <View class="button-box">
                                        <View
                                            class="submit"
                                            onClick={this.logIn.bind(this)}
                                            open-type="getUserInfo"
                                        >
                                            <Text
                                                class={`${this.props.canSubmit && this.props.hasAgreedProtocol ? "active" : ""}`}
                                            >
                                                登录
                                            </Text>
                                        </View>
                                        <Image
                                            class="button-decoration-img"
                                            src={this.props.buttonDecorationUrl}
                                        ></Image>
                                    </View>
                                    <View className="info">
                                        如手机号未注册，验证后将自动注册
                                    </View>
                                </View>
                            </View>
                        ) : (
                            <View class="p-login">
                                <Image
                                    className="logo"
                                    src="https://s.qunarzz.com/f_cms/2021/1628233309178_023411800.png"
                                />
                                <View class="content">
                                    <View class="item mobile-item">
                                        <View class="mark">
                                            +86
                                            <Text class="mark-icon g-q-iconfont">
                                                
                                            </Text>
                                        </View>
                                        <View class="input-field mobile-field hide">
                                            <Input
                                                type="number"
                                                maxlength="11"
                                                placeholder={"请输入手机号"}
                                                placeholder-style="color: #CCC;"
                                                onInput={this.bindPhoneChange.bind(
                                                    this,
                                                )}
                                            />
                                            <View
                                                class={
                                                    "clear " +
                                                    this.props.clearIconClass
                                                }
                                                onClick={this.user_login_clearInput.bind(
                                                    this,
                                                )}
                                            >
                                                <Image
                                                    class="clear-img"
                                                    src="https://s.qunarzz.com/wechatapp/common/images/login/clear.png"
                                                ></Image>
                                            </View>
                                        </View>
                                    </View>
                                    <View class="item">
                                        <Text class="mark">验证码</Text>
                                        <View class="input-field sms-code-field">
                                            <Input
                                                type="number"
                                                class="sms-input"
                                                maxlength="6"
                                                placeholder={"请输入验证码"}
                                                placeholder-style="color: #CCC;"
                                                onInput={this.bindCodeChange}
                                            />
                                            <Text
                                                class={
                                                    this.props.canSendSmsFlag
                                                        ? "sms-code "
                                                        : "gray"
                                                }
                                                onClick={this.getCode.bind(
                                                    this,
                                                )}
                                            >
                                                {this.props.smsBtnText}
                                            </Text>
                                        </View>
                                    </View>

                                    <View className="p-login-wrap-protocol-platformLoginPhone">
                                        <View
                                            className="p-login-wrap-changeSelected"
                                            onClick={this.changeSelected.bind(
                                                this,
                                            )}
                                        >
                                            <Text className="p-login-wrap-checkbox g-q-iconfont">
                                                {this.props.hasAgreedProtocol
                                                    ? ""
                                                    : ""}
                                            </Text>
                                            <Text className="p-login-wrap-text">
                                                同意并遵行去哪儿网
                                            </Text>
                                        </View>
                                        <Text
                                            className="p-login-wrap-protocolText"
                                            onClick={this.clickProtocolText.bind(
                                                this,
                                            )}
                                        >
                                            《用户协议》
                                        </Text>
                                        <Text className="p-login-wrap-text">
                                            及
                                        </Text>
                                        <Text
                                            className="p-login-wrap-protocolText"
                                            onClick={this.clickProtoColToPrivacyPolicy.bind(
                                                this,
                                            )}
                                        >
                                            《隐私政策》
                                        </Text>
                                    </View>
                                    <Text
                                        class={
                                            "submit " +
                                            (this.props.canSubmit
                                                ? "submit-can"
                                                : "") +
                                            (!this.props.hasAgreedProtocol
                                                ? " default"
                                                : "")
                                        }
                                        onClick={this.logIn.bind(this)}
                                        open-type="getUserInfo"
                                    >
                                        登录
                                    </Text>
                                    <View className="info">
                                        如手机号未注册，验证后将自动注册
                                    </View>
                                </View>

                                {/*{this.props.showQuickBrandLogin &&*/}
                                {/*    <div class='submit-quick' onTap={this.props.logByQuickBrand.bind(this)} open-type="getUserInfo"><image class="submit-img" src="https://picbed.qunarzz.com/a00be5ab26193fde9bf89d41b2c62e66.png" />VIVO账号授权登录</div>*/}
                                {/*}*/}
                            </View>
                        )}
                    </View>
                ) : (
                    <View />
                )}
            </View>
        );
    }
}

export default PlatformLoginPhone;
