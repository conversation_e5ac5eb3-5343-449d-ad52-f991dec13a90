@import '../../style/g-define.scss';

// @import '@platformStyle/iconfont.scss';
@font-face {
    font-family: 'g-q-iconfont';
    // src: url('/assets/css/minprogram_nanachi.ttf')
    // src: url("https://ss.qunarzz.com/yo/font/1.0.3/yofont.ttf");
    src: url('https://s.qunarzz.com/nanachi/score/font/0.0.96/minprogram_nanachi.ttf');
}
.c-login-wrap{
    margin-top:-1px;
}
.g-q-iconfont {
    font-family: 'g-q-iconfont';
    font-style: normal;
}

.p-login {
    padding: 0 28px;
    padding-top: 60px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;

    .logo {
        display: block;
        width: 120px;
        height: 146px;
        margin: 50px auto;
        margin-top: 0;
        margin-bottom: 16px;
    }

    /* contentStyle */
    .content {
        display: flex;
        flex-direction: column;
    }

    .item {
        display: -webkit-flex;
        display: flex;
        -webkit-align-items: center;
        align-items: center;
        // height: 61px;
        border-bottom: 1px solid #eee;
        font-size: 16px;
        padding: 32px 0 7px;

    }

    .mobile-item {
        position: relative;
    }

    .mark {
        width: 92px;
        height: 22px;
        font-size: 16px;
        color: #212121;
        line-height: 22px;
    }

    .mobile-item .mark {
        position: relative;
    }

    .mobile-item .mark-icon {
        font-family: 'g-q-iconfont';
        position: absolute;
        height: 22px;
        top: 0;
        line-height: 22px;
        left: 50px;
        font-size: 14px;
        color: #999999;
        font-weight: 400;

    }

    .clear {
        visibility: hidden;
        padding: 10px;
    }

    .clear-img {
        width: 14px;
        height: 14px;
    }

    .input-field {
        z-index: 0;
        height: 22px;
        font-size: 16px;
    }

    .input-field input {
        height: 22px;
        line-height: 22px;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        letter-spacing: 0;
        font-weight: 400;
    }

    .input-field,
    .mobile-input,
    .sms-input {
        -webkit-flex: 1;
        flex: 1;
    }

    .sms-code-field,
    .mobile-field {
        display: -webkit-flex;
        display: flex;
        -webkit-align-items: center;
        align-items: center;
    }

    .mobile-input {
        display: inline-block;
        width: 100%;
    }

    .sms-code {
        display: inline-block;
        height: 22px;
        padding: 0 6px;
        text-align: center;
        font-size: 16px;
        line-height: 22px;
        color: #00CAD8;
        letter-spacing: 0;
        font-weight: 400;
        background-color: #fff !important;
    }

    .sms-code::after {
        border: none;
    }

    .gray {
        color: #bdbdbd;
        display: inline-block;
        height: 22px;
        padding: 0 6px;
        text-align: center;
        font-size: 16px;
        line-height: 22px;
        letter-spacing: 0;
        font-weight: 400;
    }

    .btn-wrapper {
        margin-top: 30px;
    }

    .login-btn {
        height: 44px;
        border-radius: 44px;
        background-color: #E6E6E6;
        line-height: 44px;
        text-align: center;
        color: #fff;
        font-size: 18px;
    }

    .lighter .login-btn {
        background-color: #00BCD4;
    }

    .default {
        background: #E6E6E6;
    }



    .submit {
        width: 100%;
        height: 47px;
        border: none;
        border-radius: 47px;
        font-family: PingFangSC-Regular;
        font-size: 18px;
        line-height: 47px;
        color: #FFFFFF;
        text-align: center;
        background: #E6E6E6;
        margin-bottom: 13px;
    }

    .submit-can {
        background: #00D4E3;
    }

    .default {
        background: #E6E6E6;
    }

    .submit-quick {
        width: 155px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #415FFF;
        font-size: 14px;
        border: 1px solid #415FFF;
        border-radius: 20px;
        margin-top: 140px;

        text {
            color: #415FFF;
        }
    }

    .submit-img {
        width: 20px;
        height: 20px;
        margin-right: 3px;
    }

    .info {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #00CAD8;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
        text-align: center;
        display: flex;
        justify-content: center;
        margin-top: 30px;
    }

    .info text {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #00CAD8;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
        text-align: center;
    }

    .p-login-wrap-protocol-platformLoginPhone {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 30px 0 15px;
        font-size: 12px;
        color: #666666;
        text-align: center;
        line-height: 17px;
        font-weight: 400;
        flex-wrap: wrap;

        .p-login-wrap-changeSelected {
            display: flex;
            align-items: center;
            flex-direction: row;
        }

        .p-login-wrap-checkbox {
            font-size: 14px;
            color: #00CAD8;
        }

        .p-login-wrap-text {
            font-size: 14px;
            margin-left: 5px;
        }

        .p-login-wrap-protocolText {
            font-size: 14px;
            color: rgb(0, 188, 212);
        }
    }
}

.p-login-new {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url(https://s.qunarzz.com/f_cms/2023/1688027485128_972154482.png);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;

    .hello-box {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        margin-top: 28px;
        margin-left: 29px;
        align-self: flex-start;


        
        .userImg {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 12px;
    
            .pic {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                border: 1px solid #fff;
            }
        }

        .phone-number {
            font-family: PingFangSC-Medium;
            font-size: 18px;
            color: #222222;
            letter-spacing: 0;
            font-weight: 500;
            text-align: center;

        }
    }

    .rights {
        margin-top: 28px;
        margin-left: 29px;
        align-self: flex-start;
        width: 280px;
        height: 77px;
    }

    /* contentStyle */
    .content {
        display: flex;
        flex-direction: column;
        padding: 0 29px;
        margin-top: 20px;
    }

    .item {
        display: -webkit-flex;
        display: flex;
        -webkit-align-items: center;
        align-items: center;
        // height: 61px;
        border-bottom: 1px solid rgba(242,191,173,1);
        font-size: 16px;
        padding: 32px 0 7px;

    }

    .mobile-item {
        position: relative;
    }

    .mark {
        width: 92px;
        height: 22px;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #333333;
        letter-spacing: 0;
        line-height:22px;
        font-weight: 400;
    }

    .mobile-item .mark {
        position: relative;
    }

    .mobile-item .mark-icon {
        font-family: 'g-q-iconfont';
        position: absolute;
        height: 22px;
        top: 0;
        line-height: 22px;
        left: 50px;
        font-size: 14px;
        color: #999999;
        font-weight: 400;

    }

    .clear {
        visibility: hidden;
        padding: 10px;
    }

    .clear-img {
        width: 14px;
        height: 14px;
    }

    .input-field {
        z-index: 0;
        height: 22px;
        font-size: 16px;
    }

    .input-field input {
        height: 22px;
        line-height: 22px;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        letter-spacing: 0;
        font-weight: 400;
    }

    .input-field,
    .mobile-input,
    .sms-input {
        -webkit-flex: 1;
        flex: 1;
    }

    .sms-code-field,
    .mobile-field {
        display: -webkit-flex;
        display: flex;
        -webkit-align-items: center;
        align-items: center;
    }

    .mobile-input {
        display: inline-block;
        width: 100%;
    }

    .sms-code {
        display: inline-block;
        height: 22px;
        padding: 0 6px;
        text-align: center;
        font-size: 16px;
        line-height: 22px;
        color: #00CAD8;
        letter-spacing: 0;
        font-weight: 400;
        background-color: #fff !important;
    }

    .sms-code::after {
        border: none;
    }

    .gray {
        color: #bdbdbd;
        display: inline-block;
        height: 22px;
        padding: 0 6px;
        text-align: center;
        font-size: 16px;
        line-height: 22px;
        letter-spacing: 0;
        font-weight: 400;
    }

    .btn-wrapper {
        margin-top: 30px;
    }

    .login-btn {
        height: 44px;
        border-radius: 44px;
        background-color: #E6E6E6;
        line-height: 44px;
        text-align: center;
        color: #fff;
        font-size: 18px;
    }

    .lighter .login-btn {
        background-color: #00BCD4;
    }

    .default {
        background: #E6E6E6;
    }

    .button-box {
        position: relative;
        width: 317px;
        height: 77px;
        margin-top: 38px;
        background-size: cover;
        background-image: url(https://s.qunarzz.com/f_cms/2023/1688367106359_789162283.png);

        .button-decoration-img {
            position: absolute;
            top: -31px;
            right: 0px;
            z-index: 2;
            width: 146px;
            height: 56px;
        }

        .submit {
            width: 317px;
            height: 44px;
            line-height: 44px;
            background-image: linear-gradient(103deg, #FF8743 0%, #FB4C1A 97%);
            border-radius: 26.5px;
            color: #fff;
            font-size: 18px;
            border-radius: 44px;
            text-align: center;
            font-family: PingFangSC-Medium;
            font-weight: 500;

            text {
                margin: 0 auto;
                color: #fff;
                opacity: 0.6;
            }

            .active {
                opacity: 1;
            }

            &:after {
                border: 0;
            }
        }
    }

    .info {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #00CAD8;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
        text-align: center;
        display: flex;
        justify-content: center;
        margin-top: 30px;

        text {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #00CAD8;
            letter-spacing: 0;
            line-height: 20px;
            font-weight: 400;
            text-align: center;
        }
    
    }

    .p-login-wrap-protocol-platformLoginPhone {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #666666;
        text-align: center;
        line-height: 17px;
        font-weight: 400;
        flex-wrap: wrap;
        margin-top:45px;

        .p-login-wrap-changeSelected {
            display: flex;
            flex-direction: row;
            align-items: center;
            .radio {
                width: 14px;
                height: 14px;
                margin: 4px;
                border-radius: 7px;
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629788534261_062532121.png");
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            .radio-check {
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629786073626_012462090.png");
            }

        }

     

        .p-login-wrap-text {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #666666;
            letter-spacing: 0;
            line-height: 17px;
            font-weight: 400;
        }

        .p-login-wrap-protocolText {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #00CAD8;
            letter-spacing: 0;
            line-height: 17px;
            font-weight: 400;
        }
    }
}