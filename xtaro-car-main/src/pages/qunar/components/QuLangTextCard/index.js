import { View, Text } from "@tarojs/components";
import React from "react";
import "./index.scss";

class QuLangTextCard extends React.Component {
    constructor(props) {
        super(props);

        // nanachi不能在render里写function
        let {
            title,
            content: _content,
            textList: [{ data: { html: content } = {} } = {}] = [],
        } = props.data;
        content = this.filterHTML(content);
        this.state = {
            content: _content || content,
            title,
        };
    }

    filterHTML = (str) => {
        return str && str.replace(/<br\/?>/g, "\n");
    };

    render() {
        return (
            <View class="container">
                {this.state.title && (
                    <Text class="title">{this.state.title}</Text>
                )}
                <Text class="content">{this.state.content}</Text>
            </View>
        );
    }
}
QuLangTextCard.defaultProps = {
    data: {
        title: "",
        content: "",
        textList: [],
    },
};
export default QuLangTextCard;
