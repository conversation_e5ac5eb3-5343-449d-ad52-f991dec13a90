import { View, Text, Button } from "@tarojs/components";
import { getApp } from "@tarojs/taro";
import React from "react";
import "./index.scss";

import LogQmark from "@/common/utils/logQmark";
import Ubutton from "@platformComponents/Ubutton/index";

// 用户没有点击统一协议时，点击登陆出现的统一协议的弹窗
class PlatformLoginModal extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            env: process.env.ANU_ENV,
        };
    }

    clickProtocolText = () => {
        this.props.clickProtocolText();
    };

    clickProtoColToPrivacyPolicy = () => {
        this.props.clickProtoColToPrivacyPolicy();
    };

    cancel = () => {
        LogQmark({
            page: "authLogin",
            module: "default",
            id: "loginModalCancel",
            operType: "click",
        });
        this.props.cancel();
    };

    confirm = () => {
        LogQmark({
            page: "authLogin",
            module: "default",
            id: "loginModalConfirm",
            operType: "click",
        });
        this.props.changeSelected(true);
        this.props.cancel();
        this.props.confirm();
    };

    getPhoneNumber = (e) => {
        const { detail = {} } = e || {}
        LogQmark({
            page: "authLogin",
            module: "default",
            id: "loginModalConfirm",
            operType: "click",
        });
        this.props.changeSelected(true);
        // 阿里小程序onTap和onGetAuthorize 都会触发getPhoneNumber 所以限制了下
        if (this.state.env === "ali") {
            if (e.type === 'getauthorize') {
                my.getPhoneNumber({
                    success: (res) => {
                        this.props.cancel();
                        let encryptedData = res.response;
                        this.props.getPhoneNumber({ encryptedData });
                        getApp().globalData.loginType = "auth";
                    },
                    fail: (res) => {
                        this.props.cancel();
                        console.log("getPhoneNumber_fail");
                    },
                });
            } else if (e.type !== "getphonenumber") {
                this.props.getPhoneNumber({ e, ...detail });
                getApp().globalData.loginType = "auth";
            }
        } else {
            if (e.type === 'getphonenumber') {
                this.props.getPhoneNumber({ e, ...detail });
                getApp().globalData.loginType = "auth";
            }
        }
    };

    changePhoneUserInfo = (event) => {
        LogQmark({
            page: "authLogin",
            module: "default",
            id: "loginModalConfirm",
            operType: "click",
        });
        this.props.changeSelected(true);
        this.props.changePhoneUserInfo(event);
    };

    switchPhone = (bol) => {
        LogQmark({
            page: "authLogin",
            module: "default",
            id: "loginModalConfirm",
            operType: "click",
        });
        this.props.changeSelected(true);
        this.props.switchPhone(bol);
        this.props.cancel();
        getApp().globalData.loginType = "code";
    };

    render() {
        return (
            <View class="p-login-confirm-modal">
                <View class="p-login-modal-bg"></View>
                <View class="p-login-modal-container">
                    <View class="p-login-modal-tittle">
                        请阅读并同意以下协议
                    </View>
                    <View class="p-login-modal-content">
                        为保障您的个人信息安全，使用登陆注册功能需要您先阅读并同意
                        <Text
                            class="p-login-modal-protocolText"
                            onClick={this.clickProtocolText.bind(this)}
                        >
                            《用户服务协议》
                        </Text>
                        <Text>、</Text>
                        <Text
                            class="p-login-modal-protocolText"
                            onClick={this.clickProtoColToPrivacyPolicy.bind(
                                this,
                            )}
                        >
                            《隐私政策》
                        </Text>
                        <Text>。</Text>
                    </View>
                    <View class="p-login-modal-button-area">
                        <Button
                            className="p-login-modal-cancel"
                            onClick={this.cancel.bind(this)}
                        >
                            不同意
                        </Button>
                        <View class="p-login-modal-middle-line"></View>
                        {this.props.clickLoginFunction === "confirm" && (
                            <Button
                                className="p-login-modal-confirm"
                                onClick={this.confirm.bind(this)}
                            >
                                同意
                            </Button>
                        )}

                        {this.props.clickLoginFunction === "getPhoneNumber" && (
                            <Button
                                className="p-login-modal-confirm"
                                open-type={
                                    this.state.env === "ali"
                                        ? "getAuthorize"
                                        : "getPhoneNumber"
                                }
                                scope={
                                    this.state.env === "ali"
                                        ? "phoneNumber"
                                        : ""
                                }
                                onClick={this.getPhoneNumber.bind(this)}
                                onGetphonenumber={this.getPhoneNumber.bind(
                                    this,
                                )}
                                onGetAuthorize={this.getPhoneNumber.bind(this)}
                                bindgetphonenumber={this.getPhoneNumber.bind(
                                    this,
                                )}
                            >
                                同意
                            </Button>
                        )}

                        {this.props.clickLoginFunction ===
                            "changePhoneUserInfo" && (
                            <Ubutton
                                className="p-login-modal-confirm"
                                open-type="getUserInfo"
                                onGetuserinfo={this.changePhoneUserInfo.bind(
                                    this,
                                )}
                            >
                                同意
                            </Ubutton>
                        )}

                        {this.props.clickLoginFunction === "switchPhone" && (
                            <Button
                                className="p-login-modal-confirm"
                                onClick={this.switchPhone.bind(this, true)}
                            >
                                同意
                            </Button>
                        )}
                    </View>
                </View>
            </View>
        );
    }
}

export default PlatformLoginModal;
