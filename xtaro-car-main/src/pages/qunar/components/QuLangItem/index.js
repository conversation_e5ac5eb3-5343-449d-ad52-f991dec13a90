import { View, Image, Text } from "@tarojs/components";
import React from "react";
import Log, { getUserId } from "@/common/utils/log";

import QuLangMediaCard from "@platformComponents/QuLangMediaCard/index.js";
import QuLangProductList from "@platformComponents/QuLangProductList/index.js";
import QuLangLocationBar from "@platformComponents/QuLangLocationBar/index.js";
import QuLangTextCard from "@platformComponents/QuLangTextCard/index.js";
import "./index.scss";
class QuLangItem extends React.Component {
    constructor(props) {
        super(props);
        this.mediaType = this.getMediaType();
        this.logShow();
    }
    logShow = async () => {
        const fastUserId = await getUserId();

        Log({
            name: "itemShow",
            globalKey: this.props.data.globalKey,
            user: fastUserId,
        });
    };
    getMediaType = () => {
        const { data: { mediaList = [] } = {} } = this.props;

        // 每条帖子只能是 图文0 或者 视频1， 否则下面判断错误
        if (mediaList && mediaList[0]) {
            return mediaList[0].mediaType;
        }
        return 0;
    };

    static defaultProps = {};

    render() {
        return (
            <View
                class="item"
                style={this.props.index === 0 ? "" : "margin-top: 6px"}
            >
                {this.props.index > 0 && <View class="gap" />}
                <View class="author-content">
                    <View class="author-image">
                        <Image
                            src={
                                (this.props.data.user &&
                                    this.props.data.user.avatar) ||
                                "https://s.qunarzz.com/mavericks_assets/rn_home/user_default_icon.png"
                            }
                            className="user-head"
                        />
                        {this.props.data.user &&
                            this.props.data.user.accountType !== 0 && (
                                <Image
                                    src="https://picbed.qunarzz.com/f40cc43ce599ca2213305624261844ac.png"
                                    class="user-vip"
                                />
                            )}
                    </View>
                    <Text class="user-name">
                        {this.props.data.user && this.props.data.user.nickName}
                    </Text>
                </View>
                <QuLangMediaCard
                    // watcher={this.props.watcher}
                    mediaType={this.mediaType}
                    index={this.props.index}
                    data={this.props.data}
                    onClickImageCard={this.props.onClickImageCard}
                />

                {this.props.bd_origin !== "car-hw" &&
                    this.props.data.productDataList &&
                    this.props.data.productDataList.length > 0 && (
                        <QuLangProductList data={this.props.data} />
                    )}
                <QuLangLocationBar data={this.props.data} />
                <QuLangTextCard data={this.props.data} />
            </View>
        );
    }
}

QuLangItem.defaultProps = {
    index: 0,
    data: {},
    onClickImageCard: function () {},
};
export default QuLangItem;
