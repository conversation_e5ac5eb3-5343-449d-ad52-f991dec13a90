.item {
    background: #fff;
    display: flex;
    flex-direction: column;
}
.gap {
    width: 100%;
    height: 12px;
    background: #F5F7FA;
}
.author-content {
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #FFFFFF;
    padding: 0 16px 0 8px;
    margin-bottom: 5px;
    .author-image {
        display: flex;
        align-items: flex-end;
        position: relative;
        width: 30px;
        height: 30px;
    }
    .user-head {
        flex-shrink: 0;
        width: 30px;
        height: 30px;
        border-radius: 15px;
        // margin-right: 16rpx;
    }
    .user-vip {
        margin-left: -8px;
        width: 8px;
        height: 8px;
        border-radius: 4px;
    }
    .user-name {
        width: 300px;
        lines: 1;
        text-overflow: ellipsis;
        color: #0E0F0F;
    }
}
