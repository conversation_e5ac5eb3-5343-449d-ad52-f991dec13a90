import { View, Image, Text } from "@tarojs/components";
import { getMenuButtonBoundingClientRect, navigateBack } from "@tarojs/taro";
import React from "react";
import utils from "@/common/utils/util";
import "./index.scss";

class PlatformNavigationBar extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            originMenuTop: 0,
            menuHeight: 0,
        };
    }

    config = {
        styleIsolation: "shared",
    };

    componentWillMount() {
        this.initHeader();
    }

    getMenuInfo = () => {
        return this.state;
    };

    initHeader = () => {
        utils.getSystemInfo().then((res) => {
            let { windowWidth } = res;
            const menuInfo = wx
                ? getMenuButtonBoundingClientRect()
                : {
                      height: 36,
                  };

            this.setState({
                menuHeight: this.handlePixelRatio(menuInfo.height, windowWidth),
                originMenuTop: this.handlePixelRatio(menuInfo.top, windowWidth),
            });
        });
    };

    handlePixelRatio = (item, windowWidth) => {
        return Math.ceil((item * 750) / windowWidth);
    };

    handleClickBack = () => {
        navigateBack({
            delta: 1,
        });
    };

    render() {
        return (
            <View
                className="platform-navigation-bar"
                style={`height: ${this.state.menuHeight}rpx;padding: ${this.state.originMenuTop}rpx 0 12rpx 0;`}
            >
                <View class="title-container">
                    <View class="icon-container">
                        <Image
                            src="https://s.qunarzz.com/f_cms/2023/1695622431560_37159267.png"
                            class="icon-arrow"
                            mode="aspectFit"
                            onClick={this.handleClickBack.bind(this)}
                        ></Image>
                    </View>
                    <Text class="title">{this.props.title}</Text>
                </View>
            </View>
        );
    }
}

export default PlatformNavigationBar;
