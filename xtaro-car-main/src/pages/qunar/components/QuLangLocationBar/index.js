import { View, Image, Text } from "@tarojs/components";
import React from "react";

import "./index.scss";

class LocationBar extends React.Component {
    constructor(props) {
        super(props);
    }

    render() {
        return (
            <View class="location">
                {(this.props.data.cityName || this.props.data.poiName) && (
                    <View class="location-bg">
                        <Image
                            class="loading-icon-c"
                            src="https://picbed.qunarzz.com/872a71f119d538c4945ca9b19760c69d.png"
                        />
                        <View class="location-content">
                            {this.props.data.cityName && (
                                <Text class="text">{`  ${this.props.data.cityName}  `}</Text>
                            )}
                            {this.props.data.cityName &&
                                this.props.data.poiName && (
                                    <Text className="iconfont g-q-iconfont text-dot">
                                        
                                    </Text>
                                )}
                            {this.props.data.poiName && (
                                <Text class="text text-poi">{`  ${this.props.data.poiName}`}</Text>
                            )}
                        </View>
                    </View>
                )}
            </View>
        );
    }
}

LocationBar.defaultProps = {
    data: {
        cityName: "",
        poiName: "",
    },
};
export default LocationBar;
