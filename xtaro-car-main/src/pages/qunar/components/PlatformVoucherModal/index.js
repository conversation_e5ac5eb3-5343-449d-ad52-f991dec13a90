import { View, Text } from "@tarojs/components";
import React from "react";
import "./index.scss";
import LogQmark from "@/common/utils/logQmark";
const VOUCHER_MODAL_BG =
    "https://s.qunarzz.com/f_cms/2021/1638359134571_336811594.png";
const VOUCHER_FRONT_BG =
    "https://s.qunarzz.com/f_cms/2021/1639038427604_473151902.png";

class PlatformVoucherModal extends React.Component {
    handleFrontClick = () => {
        LogQmark({
            appcode: "nnc_module_qunar_platform",
            page: "homeEntrance",
            module: "newCustomerImprove",
            id: "old_new_customer_modal_click",
            operType: "click",
        });
        this.props.closeModal();
    };

    render() {
        const { voucherModalImgs = {} } = this.props;

        return (
            <View class="voucher-modal">
                <View class="voucher-modal-bg"></View>
                <View class="container">
                    <View
                        class="bg"
                        style={{
                            backgroundImage: `url(${voucherModalImgs.bg || VOUCHER_MODAL_BG})`,
                        }}
                    />
                    <View
                        class="list"
                        style={{ maxHeight: this.props.maxHeight }}
                    >
                        {this.props.voucherList &&
                            this.props.voucherList.map((item) => {
                                return (
                                    <View class="item">
                                        <View class="quanprice">
                                            {item.couponValue.type === 0 && (
                                                <View class="price">
                                                    <Text class="quandiscount">
                                                        ￥
                                                    </Text>
                                                    <Text class="quannum">
                                                        {
                                                            item.couponValue
                                                                .formatValue
                                                        }
                                                    </Text>
                                                </View>
                                            )}

                                            {item.couponValue.type === 1 && (
                                                <View class="price">
                                                    <Text class="quannum">
                                                        {
                                                            item.couponValue
                                                                .formatValue
                                                        }
                                                    </Text>
                                                    <Text class="quandiscount">
                                                        折
                                                    </Text>
                                                </View>
                                            )}

                                            {item.couponValue.type === 3 && (
                                                <View class="price">
                                                    <Text class="quannum">
                                                        {
                                                            item.couponValue
                                                                .formatValue
                                                        }
                                                    </Text>
                                                    <Text class="quandiscount">
                                                        {item.couponValue
                                                            .measure || "个"}
                                                    </Text>
                                                </View>
                                            )}
                                        </View>
                                        <View class="quaninfo">
                                            <View class="quaniname">
                                                {item.couponName}
                                            </View>
                                            <View class="quanitext">
                                                {item.couponCondition}
                                            </View>
                                        </View>
                                    </View>
                                );
                            })}
                    </View>
                    <View
                        class="front"
                        style={{
                            backgroundImage:
                                voucherModalImgs.fontBg || VOUCHER_FRONT_BG,
                        }}
                        onClick={this.handleFrontClick.bind(this)}
                    />

                    <View
                        class="close"
                        onClick={() => this.props.closeModal()}
                    />
                </View>
            </View>
        );
    }
}

export default PlatformVoucherModal;
