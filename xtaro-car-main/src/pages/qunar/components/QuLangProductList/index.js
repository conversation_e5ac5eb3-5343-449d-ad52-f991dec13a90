import { View, Swiper, SwiperItem } from "@tarojs/components";
import React from "react";
import QuLangProductItem from "@platformComponents/QuLangProductItem/index.js";
import "../QuLangProductItem/index.scss";
import "./index.scss";

class QuLangProductList extends React.Component {
    constructor(props) {
        super(props);
    }
    render() {
        return (
            <View
                className={
                    this.props.data.productDataList.length === 1
                        ? "single"
                        : "multiple"
                }
            >
                {this.props.data.productDataList.length === 1 ? (
                    <QuLangProductItem
                        itemData={this.props.data}
                        data={this.props.data.productDataList[0]}
                        index={0}
                        width="686"
                    />
                ) : (
                    <Swiper class="container">
                        {this.props.data.productDataList.map((item, index) => {
                            return (
                                <SwiperItem key={index}>
                                    <ProductItem
                                        itemData={this.props.data}
                                        data={item}
                                        index={index}
                                        width="634"
                                    />
                                </SwiperItem>
                            );
                        }, this)}
                    </Swiper>
                )}
            </View>
        );
    }
}
QuLangProductList.defaultProps = {
    data: {},
};
export default QuLangProductList;
