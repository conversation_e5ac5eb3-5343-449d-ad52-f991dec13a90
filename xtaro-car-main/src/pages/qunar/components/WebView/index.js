import { View, WebView } from "@tarojs/components";
import Taro, { getApp, getStorageSync, setStorageSync } from "@tarojs/taro";
import React from "react";
import config from "@/common/utils/config/config";
import util from "@/common/utils/util.js";
import webViewMsg from "@/common/utils/webViewMsg.js";
import Log from "@/common/utils/log";
import "./index.scss";
import login from "@/common/utils/login";
import user from "@/common/utils/user";
import LogQmark from "@/common/utils/logQmark";
import { getEntranceQconfig } from "@/common/flight/utils";
import webviewMessageHandlers from './webviewMessageHandlers';
/**
 * @param  {object}
 *    src    必传 内嵌的web页面的url,302跳转的页面  url
 *    loginSync  非必传 是否需要同步登录 true 同步  /  false 不同步
 */

class WebViewCom extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            loginSync: props.loginSync,
            src: "", //this.initUrl(props.loginSync, props.src),
            show: false,
            trust: ["https://pwapp.beta.qunar.com"],
        };
        if (process.env.ANU_ENV === "quick") return;
        // React.webview.instance = this;
        // React.webview.cb = this.onMsgFunc;
        this.injectUrl = this.injectUrl.bind(this);
    }

    getQueryParam = (name, url) => {
        let uri = url || window.location.search;
        let reg = new RegExp("[?&]" + name + "=([^&?#]*)", "i");
        let re = uri.match(reg);
        return (re && re[1]) || "";
    };

    initUrl = async (loginSync, src = "") => {
        let url = util.decodeURL(src);
        const globalData = getApp().globalData;
        const storage = await util.getGlobalInfoAsync();
        let { cookies, user } = storage;
        cookies.bd_origin = globalData.bd_origin;
        cookies.hd_origin = globalData.hd_origin;
        // 快应用在未知情况下 存不进bds
        if (process.env.ANU_ENV === "quick") {
            cookies.bd_source = "fastApp";
            cookies.fastUserId = user.fastUserId;
        }
        let params = "";
        if (!cookies.hasOwnProperty("_q")) {
            cookies["_q"] = "";
        }
        if (!cookies.hasOwnProperty("bd_source")) {
            cookies.bd_source = process.env.ANU_ENV;
        }
        // 如果是机票 往QN205种bdo
        if (
            process.env.ANU_ENV !== "wx" &&
            (url.indexOf("mpflight") > 0 || url.indexOf("m.flight") > 0)
        ) {
            cookies.QN300 = globalData.bd_origin;
            cookies.QN66 = cookies.bd_source;
        }

        if (
            process.env.ANU_ENV === "wx" &&
            (url.indexOf("/page/interlist") > 0 ||
                url.indexOf("/page/interdetail") > 0)
        ) {
            try {
                const configRes = await getEntranceQconfig();
                const expire =
                    configRes.data?.config?.wx?.inter_location_expire;
                if (expire) {
                    let location_info = getStorageSync("location_info");
                    location_info = JSON.parse(location_info);
                    const nowTimestamp = new Date().getTime();
                    if (
                        nowTimestamp - location_info.lastTime <=
                        expire * 60 * 60 * 1000
                    ) {
                        url += `${url.indexOf("?") > -1 ? "&" : "?"}lat=${location_info.lat}&lgt=${location_info.lng}`;
                    }
                }
            } catch (e) {
                LogQmark({
                    module: "default",
                    page: "webview",
                    id: "location_error",
                });
            }
        }

        for (let k in cookies) {
            params += `${k}=${cookies[k]}&`;
        }
        if (process.env.ANU_ENV === "wx" && url.indexOf("/shark") > -1) {
            if (
                !this.getQueryParam("from", url) ||
                !this.getQueryParam("notUseFrom", url)
            ) {
                if (url.indexOf("?") > -1) {
                    url += "&from=wx_app_qunar";
                } else {
                    url += "?from=wx_app_qunar";
                }
            }
            if (!this.getQueryParam("appNo", url)) {
                if (url.indexOf("?") > -1) {
                    url += `&appNo=${globalData.appNo || ""}`;
                } else {
                    url += `?appNo=${globalData.appNo || ""}`;
                }
            }
            
        }
        // 微信链接上带openid
        if(process.env.ANU_ENV === "wx" &&!!cookies?._s){
          if (url.indexOf("?") > -1) {
            url += `&scookie=${cookies._s}`;
          } else {
            url += `?scookie=${cookies._s}`;
          }
        }
        let syncUrl = `${config.settings.requestDomain}${
            config.service.syncCookie
        }?${params}pageUrl=${encodeURIComponent(url)}`;
        console.log('====syncUrl',syncUrl)
        return syncUrl;
    };

    async componentDidMount() {
        const env = process.env.ANU_ENV;
        if (env === "wx") {
            const { loginToken, bizCode, source, needGoLoginPage } = this.props;
            const storage = await util.getGlobalInfoAsync() || {};
            let { cookies } = storage;
            try {
                if (loginToken && bizCode && source) {
                    const res = await user.checkLogin();
                    if (!res?.data?.isLogin) {
                        await user.bindQunarUserByToken({
                            loginToken,
                            bizCode,
                            source,
                        });
                        await user.loginByQuick();
                    }
                    await this.injectUrl();
                } else if (needGoLoginPage) {
                    const res = await user.checkLogin();
                    if (!res?.data?.isLogin) {
                        login(this.injectUrl, { source: "actWebWx" });
                    } else {
                        await this.injectUrl();
                    }
                } else if (!cookies.openId) {
                    await user.appLanchInfos();
                    await this.injectUrl();
                } else {
                    await this.injectUrl();
                }
            } catch (e) {
                await this.injectUrl();
            }
        } else {
            await this.injectUrl();
        }
    }

    injectUrl = async () => {
        const src = await this.initUrl(this.props.loginSync, this.props.src);
        if(process.env.ANU_ENV === "ali") {
            this.webViewContext = my.createWebViewContext('web');
        }
        this.setState({
            src,
        });
        Log({
            type: "webviewLoad",
            src,
        });
        LogQmark({
            module: "default",
            page: "webview",
            id: "default",
            ext: {
                url: src,
            },
        });
    };

    sendMessageToWebView = (message) => {
        // console.log('sendMessageToWebView===', this.webViewContext)
        // console.log('sendMessageToWebView===message', message)
        if (this.webViewContext) {
            this.webViewContext.postMessage(message);
        }
    }

    onMsgFunc = (e) => {
        const name = e.detail.name;
        // console.log('onMsgFunc===', e)
        if(name && process.env.ANU_ENV === "ali") {
            // 处理特定类型的消息
            const handler = webviewMessageHandlers[name];
            if (typeof handler === 'function') {
                handler(e.detail, (result) => {
                    // console.log({ [name]: result });
                    this.sendMessageToWebView({ [name]: result });
                });
            }
        }
        const list = e.detail.data || [];
        if(Array.isArray(list)) {
            list?.forEach((v) => {
                const matchItem = this.findMsgFunc(v);
                if (!v.name || !matchItem) {
                    return;
                }
                matchItem.func(v, this);
            });
        }
    };

    findMsgFunc = (msg) => {
        return webViewMsg.find((v) => {
            return v.name === msg.name;
        });
    };

    updateShareData = (data) => {
        this.props.syncShareData &&
            this.props.syncShareData({
                title: data.title,
                imageUrl: data.imageUrl,
                path: data.path,
            });
    };

    pagefinish = (e) => {
        // eslint-disable-next-line
        this.props.getWebViewInstance &&
            this.props.getWebViewInstance(this.wx.$element("web"));
    };

    pageerror = (e) => {
        // eslint-disable-next-line
        const { url, fullUrl } = e.detail || {};
        LogQmark({
            module: "default",
            page: "webview",
            id: "pageLoadFail",
            ext: {
                url,
                fullUrl,
                injectUrl: this.state.src,
            },
        });
    };

    pageLoaded = (e) => {
        // eslint-disable-next-line
        const { src } = e.detail || {};
        LogQmark({
            module: "default",
            page: "webview",
            id: "pageLoaded",
            ext: {
                url: src,
                injectUrl: this.state.src,
            },
        });
    };
    syncNavigateBackData = (data) => {
        setStorageSync(data.storageKey, data.data);
    };

    render() {
        return (
            <View>
                {this.state.src ? (
                    <WebView
                        id="web"
                        src={this.state.src}
                        onMessage={this.onMsgFunc.bind(this)}
                        syncNavigateBackData={this.syncNavigateBackData.bind(
                            this,
                        )}
                        trustedurl={this.state.trust}
                        onPagefinish={this.pagefinish}
                        onLoad={this.pageLoaded}
                        onError={this.pageerror}
                    />
                ) : null}
            </View>
        );
    }
}
export default WebViewCom;
