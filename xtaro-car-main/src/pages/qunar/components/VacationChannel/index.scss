@font-face {
    font-family: 'g-q-iconfont';
    // src: url('/assets/css/minprogram_nanachi.ttf')
    // src: url("https://ss.qunarzz.com/yo/font/1.0.3/yofont.ttf");
    src: url('https://s.qunarzz.com/nanachi/score/font/0.0.22/minprogram_nanachi.ttf');
}

.g-q-iconfont {
    font-family: 'g-q-iconfont';
    font-family: 'g-q-iconfont' !important;
    font-size: 12px;
    font-style: normal;
}
.startPosition, .endPosition{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

button {
    box-sizing: content-box;
    background: #FFFFFF;
    border: none;
    outline: none;
    background: none;
    &:after {
        border: none;
    }
}

button:active {
    background: #FFFFFF;
}

.vacation-channel {
    &.content {
        margin: 0px 8px 12px;
        width: auto;
        background-image: linear-gradient(180deg, #F6F7FA 0%, #FFFFFF 45%);
        border-bottom-right-radius: 12px;
        border-bottom-left-radius: 12px;
        padding-top: 8px;
        padding-bottom: 2px;
    }
    
    
    .channel {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: row;
        flex-wrap: wrap;
        width: 100%;
        &-item {
            width: 20%;
            margin-bottom: 10px;
            text-align: center;
        }
        .icon-img {
            width: 44px;
            height: 44px;
        }
        .text {
            text-align: center;
            height: 18px;
            font-size: 12px;
            line-height: 1;
            color: #333333;
            vertical-align: text-top;
            margin-top: -2px;
        }
    }
    
    &.new-content {
        margin-left: 0;
        margin-right: 0;
        padding-top: 0;
        padding-bottom: 12px;
        margin-bottom: 0;
        background: #FFFFFF;
        .channel-item {
            margin: 0;
            padding-top: 12px;
            padding-bottom: 5px;
            font-size: 0;
            .icon-img {
                width: 30px;
                height: 30px;
            }
            .text {
                margin-top: 6px;
                line-height: 1;
                height: 17px;
            }
        }
    }

}
