import { Block, View, Image } from "@tarojs/components";
import { navigateTo } from "@tarojs/taro";
import React from "react";
import Request from "@/common/utils/request.js";
import EventEmitter from "@/common/utils/EventEmitter";
import Log from "@/common/utils/log";
import sk from "@/common/vacation/sk";
import "./index.scss";

const pagePath = {
    vacationWebview: "/pages/vacation/webview/index",
    vacationList: "/pages/vacation/list/index",
};

class VacationChannel extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            channel: [],
            dep: "北京",
        };
        this._initListener();
        this.getChannel();
    }

    componentWillUnmount() {
        this._cityListener && this._cityListener.removeListener();
    }

    _initListener = () => {
        this._cityListener = EventEmitter.addListener(
            "HOME_VACATION_CITY_SELECT",
            (data) => {
                this.setDep(data);
            },
        );
    };

    setDep = (res) => {
        const { city = "北京" } = res;
        this.setState({
            dep: city,
        });
    };

    getChannel = () => {
        Request({
            service: "/vacation/oss/cg/detail",
            data: {
                pos: "sapp_entrance_ball",
                modules: "miniProgramEntry",
            },
            success: (res) => {
                if (res.status === 0 && res.data) {
                    const { miniProgramEntry = [] } = res.data;
                    const ossConfigBalls = miniProgramEntry
                        .map((item) => {
                            const { tags = [] } = item;
                            return {
                                tag: tags[0] && tags[0].name,
                                ...item.data,
                            };
                        })
                        .filter(
                            (item) =>
                                item.tag === (process.env.ANU_ENV || "wx"),
                        );

                    let channel = [];
                    ossConfigBalls.forEach((v) => {
                        if (
                            ["imageUrl", "title"].every((key) => v[key]) &&
                            (v.query || v.url)
                        ) {
                            channel.push(v);
                        }
                    });

                    this.setState({
                        ...this.state,
                        channel,
                    });
                } else {
                    this.setState({
                        ...this.state,
                        channel: [],
                    });
                }
            },
        });
    };

    toList = (item) => {
        let { dep } = this.state;

        Log({
            info: {
                area: "vacationOther",
                act: "click",
                type: "homeEnter",
                name: "vacationOther",
            },
        });
        sk.click("wxapp_dujia_index", {
            edata: {
                dep,
                indexName: item.title,
            },
        });

        if (item && item.url) {
            if (this.checkURL(item.url)) {
                const url = encodeURIComponent(
                    item.url + `${item.url.match(/\?/) ? "&" : "?"}dep=${dep}`,
                );
                navigateTo({ url: `${pagePath.vacationWebview}?url=${url}` });
            } else {
                wx.navigateToMiniProgram({
                    appId: item.url,
                    path: "",
                });
            }
        } else {
            let query = item.query;
            navigateTo({
                url: `${pagePath.vacationList}?dep=${dep}&query=${query}`,
            });
        }
    };

    checkURL = (URL) => {
        let str = URL;
        //判断URL地址的正则表达式为:http(s)?://([\w-]+\.)+[\w-]+(/[\w- ./?%&=]*)?
        //下面的代码中应用了转义字符"\"输出一个字符"/"
        let Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
        let objExp = new RegExp(Expression);
        if (objExp.test(str) == true) {
            return true;
        } else {
            return false;
        }
    };

    render() {
        return (
            <Block>
                <View
                    class={`vacation-channel content ${this.props.isNew ? "new-content" : ""}`}
                >
                    <View class="channel">
                        {this.state.channel.map((item, index) => {
                            return (
                                <View
                                    class="channel-item"
                                    key={index}
                                    onClick={() => {
                                        this.toList(item);
                                    }}
                                >
                                    <Image
                                        class="icon-img"
                                        src={item.imageUrl}
                                    />
                                    <View class="text">{item.title}</View>
                                </View>
                            );
                        })}
                    </View>
                </View>
            </Block>
        );
    }
}

export default VacationChannel;
