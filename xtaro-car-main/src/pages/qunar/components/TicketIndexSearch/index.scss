@font-face {
    font-family: 'piao_wx';
    src: url('//s.qunarzz.com/piao/wx/font/0.0.34/piao_wx.eot'); /* IE9*/
    src: url('//s.qunarzz.com/piao/wx/font/0.0.34/piao_wx.woff') format('woff'), /* chrome、firefox */
    url('//s.qunarzz.com/piao/wx/font/0.0.34/piao_wx.ttf') format('truetype'), /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
    url('//s.qunarzz.com/piao/piao_font/wx/font/0.0.34/piao_wx.svg#iconfont') format('svg'); /* iOS 4.1- */
    font-weight: normal;
    font-style: normal;
}

.mp-homePage-search-box {
    background-color: #f5f6f9;
    overflow: hidden;
    padding-left: 8px;
    padding-right: 8px;
    margin-bottom: 12px;
    padding-top: 24px;
}
.mp-homePage-search-box-bg {
    background-image: linear-gradient(180deg, #F3F4F7 0%, #FFFFFF 50%);
    border-radius: 0px 0px 16px 16px;
}
.mp-homePage-search {
    // padding-top: 24px;
}

.ticket-search-container {
    background-color: #F5F6F9;
    padding-top: 8px;
    margin-bottom: 12px;
}
.search-container {
    position: relative;
    z-index: 50;
    background-image: #fff;
    margin: 0 8px;
}

.new-search-container {
    position: relative;
    z-index: 50;
    background-image: #fff;    
}

.search_container_inner{
    background-image: linear-gradient(180deg, #F5F6F9 0%, #FFFFFF 50%);
    padding: 16px;
    border-radius: 16px;
}
.new-search_container_inner{
    background-color: #FFFFFF;
    padding: 15px;
    border-radius: 16px 16px 12px 12px;
}

.search_container_inner_wrapper{
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: linear-gradient(90deg, #00E3CE 0%, #01D5E3 99%);
    border-radius: 22px;
    padding: 1px
}

.mp-homePage-search-inputBox {
    border: 1px solid #00cad8;
    border-radius: 22px;
    padding: 4px;
    padding-left: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    
    .city-select{
        display: flex;
        font-size: 16px;
        color: #222222;
    }
    .line {
        height: 14px;
        border-left: 1px solid #cccccc;
        margin: 0 8px;
    }
    .p-wx-iconfont {
        font-size: 18px;
        color: #666;
        padding-right: 2px;
        font-family: "piao_wx";
    }
    .text {
        max-width: 128px;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;      
    }
}

.new-mp-homePage-search-inputBox {
    flex: 1;
    border-radius: 22px;
    padding: 4px;
    padding-left: 16px;
    display: flex;
    align-items: center;
    flex-direction: row;
    background-color: #fff;    
    .city-select{
        display: flex;
        font-size: 14px;
        color: #222222;
        margin-right: 8px;
    }
    .line {
        height: 14px;
        border-left: 1px solid #E8E8E8;
        margin-right: 12px;
    }
    .p-wx-iconfont {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        color: #666;
        font-family: "piao_wx";
    }
    .text {
        margin-right: 4px;
        max-width: 128px;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14px;
        color: #333;
        font-weight: 700;
    }
}

.mp-homePage-search-input {
    max-width: 150px;
}

.new-mp-homePage-search-input {
    flex: 1;
}

.mp-homePage-search-button {
    background: #00CAD8;
    color: #fff;
    padding: 4px 12px;
    border-radius: 22px;
    font-size: 16px;
    font-weight: bold;
    flex-shrink:0 
}

.new-mp-homePage-search-button {
    color: #fff;
    padding: 6px 12px;
    border-radius: 22px;
    line-height: 16px;
    font-size: 14px;
    font-weight: bold;
    flex-shrink:0;
    background-image:  linear-gradient(90deg, #00E3CE 0%, #01D5E3 99%);
}

.mp-homePage-search-desc {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 38px;
    margin-right: 38px;
    padding-bottom: 12px;
    font-size: 12px;

    image {
        width: 42px;
        height: 13px;
    }
}

.mp-homePage-search-line {
    display: block;
    width: 20px;
    height: 1px;
    opacity: 0.5;
    background-image: linear-gradient(90deg, #FFFFFF 0%, #00CAD8 100%);
}

.mp-homePage-search-line-right {
    display: block;
    width: 20px;
    height: 1px;
    opacity: 0.5;
    background-image: linear-gradient(90deg, #00CAD8 0%, #FFFFFF 100%);
}

.mp-homePage-search-dot {
    color: #00CAD8;
}

.mp-homePage-search-text {
    opacity: 0.6;
}

.service {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding-bottom: 12px;
    text-align: center;
    font-size: 12px;
    height: 17px;
}
.anxin {
   width: 43px;
   height: 13.5px;
   background-image: url('https://s.qunarzz.com/hotel_mp_source/images/anxin.png');
   background-size: 100% 100%;
}
.divline {
   margin-right: 8px;
   width: 20px;
   height: 1px;
   background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
}
.divline-right{
   margin-right: 0;
    margin-left: 8px;
    background-image: linear-gradient(270deg, #ffffff 0%, #00d5e7 100%);
}
.text-list{
   margin-bottom: 1px;
}
.service-text{
   margin-left: 7px;
   color: #000;
   opacity: 0.8;
}

.service-item {
    opacity: 0.8;
    font-family: PingFangSC-Light;
    font-size: 12px;
    color: #000000;
    letter-spacing: 0;
}

.dot {
    padding-right: 2px;
    color: #00CAD8;
    font-size: 14px;
}