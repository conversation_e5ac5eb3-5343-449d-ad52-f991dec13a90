import { View, Text, Input } from "@tarojs/components";
import {
    getStorageSync,
    getApp,
    getLocation,
    setStorageSync,
    navigateTo,
} from "@tarojs/taro";
import QMark from "@/npm/@qnpm/qmark/dist/qmark.mini.umd.js";
import React from "react";
import Log from "@/common/utils/log";
import Util from "@/common/utils/util";
import EventEmitter from "@/common/utils/EventEmitter";
import Request from "@/common/utils/request.js";
import "./index.scss";

class TicketIndexSearch extends React.Component {
    constructor(props) {
        super(props);
        const currentCity = getStorageSync("ticket-city");

        this.state = {
            searchValue: "",
            serviceList: ["低价保障", "出行保障", "售后保障"],
            currentCity,
        };
    }

    componentWillMount() {
        const { cookies } = Util.getGlobalInfo();
        let openId = cookies.openId;
        if (this.props?.queryData?.ticketchannel) {
            QMark &&
                QMark.log &&
                QMark.log({
                    bizType: "ticket",
                    module: "default",
                    appcode: "wx",
                    page: "ticketHome_WeChat",
                    id: "xhsChannelCount",
                    operType: "show",
                    key: "ticket/ticket`Home_WeChat/default/show/xhsChannelCount",
                    operTime: "",
                    ext: {
                        channel: this.props.queryData.ticketchannel,
                        openId: openId,
                    },
                });
        }

        this.initModuleList();
        QMark &&
            QMark.log &&
            QMark.log({
                bizType: "ticket",
                module: "homeSearchBox",
                appcode: "wx",
                page: "ticketHome_WeChat",
                id: "searchBoxShow",
                operType: "show",
                key: "ticket/ticketHome_WeChat/homeSearchBox/show/searchBoxShow",
                operTime: "",
                ext: {
                    scene: getApp().globalData.scene,
                },
            });
    }
    componentDidMount() {
        EventEmitter.addListener("TICKET_SELECT_CITY", () => {
            const currentCity = getStorageSync("ticket-city");
            this.setState({ currentCity });
            this.getListData();
        });
    }

    // 模块列表
    initModuleList = () => {
        getLocation({
            type: "gcj02",
            success: (res) => {
                this.lat = res.latitude;
                this.lng = res.longitude;
                this.getListData();
            },
            fail: () => {
                this.getListData();
            },
        });
    };

    goTicketList = () => {
        Log({
            info: {
                area: "ticketSearch",
                act: "click",
                type: "homeEnter",
                name: "ticketSearch",
            },
        });
        QMark &&
            QMark.log &&
            QMark.log({
                bizType: "ticket",
                module: "homeSearchBox",
                appcode: "wx",
                page: "ticketHome_WeChat",
                id: "searchBoxClick",
                operType: "click",
                title: `${this.state.searchValue}`,
                key: "ticket/ticketHome_WeChat/homeSearchBox/click/searchBoxClick",
                operTime: "*",
            });
        const traceId = getStorageSync("ticketTraceId");
        setStorageSync("ticketFromArea", "index_search");
        const newCatQuery = encodeURIComponent(
            `from_area=index_search&log_id=${traceId}_home`,
        );
        let url = `/pages/ticket/index/index?query=${this.state.searchValue}&catQuery=${newCatQuery}&from_wxHome=index_search`;
        navigateTo({
            url,
        });
    };

    getListData = () => {
        const traceId = Util.getUniid();
        Request({
            host: "https://wxapp.qunar.com/ticket/pw",
            service: "/getAroundAndHotSight.json",
            data: {
                lat: this.lat ? this.lat : "",
                lng: this.lng ? this.lng : "",
                cat: `log_id=${traceId}_home&traceId=${traceId}`,
                traceId,
                searchCity: this.state.currentCity,
            },
            success: (response) => {
                let data = response.data;
                let city;
                if (!this.state.currentCity) {
                    city = data.city || "北京";
                    setStorageSync("ticket-city", city);
                } else {
                    city = this.state.currentCity;
                }
                this.setState({
                    currentCity: city,
                });
            },
            fail: () => {},
            complete: () => {},
        });
        setStorageSync("ticketTraceId", traceId);
    };

    inputName = (e) => {
        this.setState({
            searchValue: e.detail.value,
        });
    };
    goToCitySelect = () => {
        Util.openWebview({
            url: "https://touch.piao.qunar.com/touch/toNewCityListWx.htm",
            loginSync: true,
        });
        QMark &&
            QMark.log &&
            QMark.log({
                bizType: "ticket",
                module: "homeSearchBox",
                appcode: "wx",
                page: "ticketHome_WeChat",
                id: "citySelect",
                operType: "click",
                key: "ticket/ticketHome_WeChat/homeSearchBox/click/citySelect",
                operTime: "*",
            });
    };

    render() {
        return (
            <View class={this.props.isNew ? "" : "ticket-search-container"}>
                <View class={this.props.isNew ? "new-search-container" : "search-container"}>
                    <View class={this.props.isNew ? "new-search_container_inner" : "search_container_inner"}>
                        <View class={this.props.isNew ? "search_container_inner_wrapper" : ""}>
                            <View class={this.props.isNew ? "new-mp-homePage-search-inputBox" : "mp-homePage-search-inputBox"}>
                                <View
                                    class="city-select"
                                    onClick={this.goToCitySelect}
                                >
                                    <Text class="text">
                                        {this.state.currentCity || "定位中"}
                                    </Text>
                                    <Text class="p-wx-iconfont">&#xe3f7;</Text>
                                </View>
                                <View class="line"></View>
                                <Input
                                    class={this.props.isNew ? "new-mp-homePage-search-input" : "mp-homePage-search-input"}
                                    placeholder={"请输入景点名称"}
                                    placeholder-style="color: #CCC;font-size: 14px"
                                    name="sightName"
                                    onInput={this.inputName.bind(this)}
                                />
                                <Text class={this.props.isNew ? "new-mp-homePage-search-button" : "mp-homePage-search-button"} onClick={this.goTicketList}>
                                    搜索
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>
            </View>
        );
    }
}

export default TicketIndexSearch;
