import { View, Text } from "@tarojs/components";
import React from "react";
import "./index.scss";
/**
 * 通用倒计时组件
 * @param {Number} endTime 倒计时结束时间戳 毫秒数 例如倒计时十秒的时候传入 10000
 * @param {Function} timeEndCallback 倒计时结束后的回调
 * @param {Object} containerStyle 容器样式
 * @param {Object} numStyle 数字样式
 * @param {Object} dotStyle 冒号样式
 * @param {Object} postTextStyle 后缀文字样式
 * @param {String} postText 后缀文字 默认为 '后过期'
 */
class CommonCountDown extends React.Component {
    constructor(props) {
        super(props);
        this.timer = null;
        this.state = {
            hour: "00",
            minute: "00",
            second: "00",
            lastTime: 0,
        };
        this.calcLastTime = this.calcLastTime.bind(this);
    }
    componentDidMount() {
        const lastTime = Math.round(this.props.endTime / 1000);
        this.calcLastTime(lastTime);
    }

    shouldComponentUpdate(nextProps) {
        if (nextProps.endTime && this.props.endTime !== nextProps.endTime) {
            const lastTime = Math.round(this.props.endTime / 1000);
            this.calcLastTime(lastTime);
        }
        return true;
    }

    componentWillUnmount() {
        this.timer && clearTimeout(this.timer);
    }

    calcLastTime = (lastTime = 0) => {
        this.timer && clearTimeout(this.timer);
        this.timer = null;
        if (lastTime <= 0) {
            lastTime = 0;
            this.setState({
                hour: "00",
                minute: "00",
                second: "00",
                lastTime,
            });
            // 倒计时结束后的回调
            this.props.timeEndCallback && this.props.timeEndCallback();
            return;
        }

        let hour = Math.floor(lastTime / 3600);
        let lastMin = lastTime % 3600;
        let minute = Math.floor(lastMin / 60);
        let second = lastTime % 60;

        this.setState({
            hour: hour < 10 ? "0" + hour : hour,
            minute: minute < 10 ? "0" + minute : minute,
            second: second < 10 ? "0" + second : second,
            lastTime: lastTime - 1,
        });

        this.timer = setTimeout(() => this.calcLastTime(lastTime - 1), 1000);
    };

    render() {
        return (
            <View
                class="common-countdown-container"
                style={this.props.containerStyle}
            >
                <View class="countdown-box">
                    <Text class="time-box" style={this.props.numStyle}>
                        {this.state.hour}
                    </Text>
                    <Text class="time-dot" style={this.props.dotStyle}>
                        :
                    </Text>
                    <Text class="time-box" style={this.props.numStyle}>
                        {this.state.minute}
                    </Text>
                    <Text class="time-dot" style={this.props.dotStyle}>
                        :
                    </Text>
                    <Text class="time-box" style={this.props.numStyle}>
                        {this.state.second}
                    </Text>
                    <Text class="time-after" style={this.props.postTextStyle}>
                        {this.props.postText || "后过期"}
                    </Text>
                </View>
            </View>
        );
    }
}

export default CommonCountDown;
