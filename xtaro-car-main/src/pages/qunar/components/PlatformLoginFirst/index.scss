@import '../../style/g-define.scss';
@import '../../style/iconfont.scss';

.c-login-wrap{
    margin-top:-1px;
}
.c-login-first {
    padding: 66px 20px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;

    .logo {
        display: block;
        width: 120px;
        height: 146px;
        margin: 0 auto;
    }

    .phoneNumber_login {
        width: 100%;
        height: 47px;
        border-radius: 47px;
        line-height: 47px;
        background-color: #00D4E3;
        text-align: center;
        color: #fff;
        font-size: 18px;
        margin-top: 30px;
        margin-bottom: 20px;

        &:after {
            border: 1px solid #00D4E3;
        }

        text {
            display: inline-block;
            line-height: 47px;
            vertical-align: top;
        }

        .wx-icon {
            display: inline-block;
            width: 25px;
            line-height: 47px;
            vertical-align: top;
        }

        .wx-icon::before {
            font-size: 23px;
            content: '\e0b9';
        }
    }

    .default {

        //background: #E6E6E6;
        &:after {
            border: 1px solid #E6E6E6;
        }
    }

    .pl-bu {
        background-color: rgba(59, 120, 251);

        &:after {
            border: 1px solid rgba(59, 120, 251);
        }
    }

    .switch-phone {
        width: 100%;
        margin-top: 60px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #00CAD8;
        letter-spacing: 0;
        text-align: center;
        line-height: 20px;
        font-weight: 400;
        background-color: transparent;
        border: 0;

        &:after {
            border: 1px solid #fff;
        }
    }

    .p-login-wrap-protocol-platformLoginFirst {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 67px;
        flex-wrap: wrap;
        flex-direction: row;

        .p-login-wrap-changeSelected {
            display: flex;
            align-items: center;
            flex-direction: row;
        }

        .p-login-wrap-checkbox.g-q-iconfont {
            font-size: 16px;
            color: #00CAD8;
            height: 17px;
            line-height: 17px;
        }

        .p-login-wrap-text {
            height: 17px;
            line-height: 17px;
            font-size: 12px;
            margin-left: 5px;
        }

        .p-login-wrap-protocolText {
            height: 17px;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #00CAD8;
            text-align: center;
            line-height: 17px;
            font-weight: 400;
        }
    }
}

// swan-button {
//   position: relative;
// }

.c-login-first-new {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url('https://s.qunarzz.com/f_cms/2023/1688027485128_972154482.png');
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;

    .hello-box {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        margin-top: 28px;
        margin-left: 29px;
        align-self: flex-start;


        
        .userImg {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 12px;
    
            .pic {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                border: 1px solid #fff;
            }
        }

        .phone-number {
            font-family: PingFangSC-Medium;
            font-size: 18px;
            color: #222222;
            letter-spacing: 0;
            font-weight: 500;
            text-align: center;

        }
    }

    .rights {
        margin-top: 28px;
        margin-left: 29px;
        align-self: flex-start;
        width: 280px;
        height: 77px;
    }

    .button-box {
        position: relative;
        width: 317px;
        height: 77px;
        margin-top: 38px;
        background-size: contain;
        background-image: url(https://s.qunarzz.com/f_cms/2023/1688367106359_789162283.png);

        .phoneNumber_login {
            width: 317px;
            height: 44px;
            line-height: 44px;
            background-image: linear-gradient(103deg, #FF8743 0%, #FB4C1A 97%);
            border-radius: 26.5px;
            color: #fff;
            font-size: 18px;
            border-radius: 44px;
            text-align: center;
            font-family: PingFangSC-Medium;
            font-weight: 500;

            text {
                margin: 0 auto;
                color: #fff;
            }

            &:after {
                border: none;
            }

            text {
                display: inline-block;
                line-height: 47px;
                vertical-align: top;
            }

            .wx-icon {
                display: inline-block;
                width: 25px;
                line-height: 47px;
                vertical-align: top;
            }

            .wx-icon::before {
                font-size: 23px;
                content: '\e0b9';
            }
        }

        .button-decoration-img {
            position: absolute;
            top: -31px;
            right: 0px;
            z-index: 2;
            width: 146px;
            height: 56px;
        }
    }

    .default {

        //background: #E6E6E6;
        &:after {
            border: 1px solid #E6E6E6;
        }
    }

    .pl-bu {
        background-color: rgba(59, 120, 251);

        &:after {
            border: 1px solid rgba(59, 120, 251);
        }
    }

    .switch-phone {
        font-family: PingFangSC-Regular;
        margin-top: 16px;
        font-size: 14px;
        color: #00CAD8 ;
        font-weight: 400;
        padding: 0 0 0 0;
        background-color: transparent;

        &:after {
            border: 0;
        }
    }

    .p-login-wrap-protocol-platformLoginFirst {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 107.5px;
        flex-wrap: wrap;

        .p-login-wrap-changeSelected {
            display: flex;
            align-items: center;
            flex-direction: row;
        }

        .p-login-wrap-changeSelected {
            display: flex;
            align-items: center;
            flex-direction: row;

            .radio {
                width: 14px;
                height: 14px;
                margin: 4px;
                border-radius: 7px;
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629788534261_062532121.png");
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            .radio-check {
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629786073626_012462090.png");
            }

        }

        .p-login-wrap-text {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #666666;
            letter-spacing: 0;
            line-height: 17px;
            font-weight: 400;
            height: 17px;
            margin-left: 5px;
        }

        .p-login-wrap-protocolText {
            height: 17px;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #00CAD8;
            text-align: center;
            line-height: 17px;
            font-weight: 400;
        }
    }
}

.footer {
    position: fixed;
    bottom: 31px;
    font-size: 12px;
    color: #555;
}