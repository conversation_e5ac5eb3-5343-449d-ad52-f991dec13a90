import { View, Image, Text, Button } from "@tarojs/components";
import { getApp } from "@tarojs/taro";
import React from "react";
import "./index.scss";
import LogQmark from "@/common/utils/logQmark";

const defaultLogParams = {
    page: "authLogin",
    module: "login_first",
    operType: "click",
};
class PlatformLoginFirst extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            env: process.env.ANU_ENV,
        };
    }
    componentDidMount() {
        LogQmark({
            ...defaultLogParams,
            id: "login_first",
            operType: "show",
            ext: {
                newStyle: this.props.newStyle,
            },
        });
    }
    // 微信记录 登陆的方式
    switchPhone = (bol) => {
        LogQmark({
            ...defaultLogParams,
            id: "switch_phone",
            ext: { newStyle: this.props.newStyle },
        });
        this.props.switchPhone(bol);
        getApp().globalData.loginType = "code";
    };
    getPhoneNumber = (e) => {
        const { detail = {} } = e || {}
        // 阿里小程序onTap和onGetAuthorize 都会触发getPhoneNumber 所以限制了下
        if (this.state.env === "ali") {
            if (e.type === 'getauthorize') {
                my.getPhoneNumber({
                    success: (res) => {
                        let encryptedData = res.response;
                        this.props.getPhoneNumber({ encryptedData });
                        getApp().globalData.loginType = "auth";
                    },
                    fail: (res) => {
                        console.log("getPhoneNumber_fail");
                    },
                });
            //onGetphonenumber也会触发增加拦截
            } else if (e.type !== "getphonenumber") {
                this.props.getPhoneNumber({ e, ...detail });
                getApp().globalData.loginType = "auth";
            }
        } else {
            this.props.getPhoneNumber({ e, ...detail });
            getApp().globalData.loginType = "auth";
        }
        LogQmark({
            ...defaultLogParams,
            id: "login_button",
            ext: { newStyle: this.props.newStyle },
        });
    };
    changeSelected = () => {
        LogQmark({
            ...defaultLogParams,
            id: "checkbox",
            ext: { newStyle: this.props.newStyle },
        });

        this.props.changeSelected();
    };

    clickProtocolText = () => {
        this.props.clickProtocolText();
    };

    clickProtoColToPrivacyPolicy = () => {
        this.props.clickProtoColToPrivacyPolicy();
    };

    render() {
        return (
            <View>
                {this.props.newStyle !== undefined ? (
                    <View class="c-login-wrap">
                        {this.props.newStyle ? (
                            <View class="c-login-first-new">
                                <View class="hello-box">
                                    <View className="userImg">
                                        <Image
                                            class="pic"
                                            src="https://s.qunarzz.com/f_cms/2023/1688395582353_578962327.png"
                                        ></Image>
                                    </View>
                                    <View className="phone-number">
                                        Hi, 欢迎来到去哪儿
                                    </View>
                                </View>
                                <Image
                                    class="rights"
                                    src={this.props.rightsUrl}
                                ></Image>

                                <View className="p-login-wrap-protocol-platformLoginFirst">
                                    <View
                                        className="p-login-wrap-changeSelected"
                                        onClick={this.changeSelected.bind(this)}
                                    >
                                        <Text
                                            className={`p-login-wrap-checkbox ${this.props.hasAgreedProtocol ? "radio radio-check" : "radio"}`}
                                        ></Text>
                                        <Text className="p-login-wrap-text">
                                            同意并遵行去哪儿网
                                        </Text>
                                    </View>
                                    <Text
                                        className="p-login-wrap-protocolText"
                                        onClick={this.clickProtocolText.bind(
                                            this,
                                        )}
                                    >
                                        《用户服务协议》
                                    </Text>
                                    <Text className="p-login-wrap-text">
                                        、
                                    </Text>
                                    <Text
                                        className="p-login-wrap-protocolText"
                                        onClick={this.clickProtoColToPrivacyPolicy.bind(
                                            this,
                                        )}
                                    >
                                        《隐私政策》
                                    </Text>
                                </View>
                                <View class="button-box">
                                    <Button
                                        class={
                                            "phoneNumber_login pl-" +
                                            process.env.ANU_ENV +
                                            (!this.props.hasAgreedProtocol
                                                ? " default"
                                                : "")
                                        }
                                        open-type={
                                            this.props.hasAgreedProtocol
                                                ? this.state.env === "ali"
                                                    ? "getAuthorize"
                                                    : "getPhoneNumber"
                                                : ""
                                        }
                                        scope={
                                            this.state.env === "ali"
                                                ? "phoneNumber"
                                                : ""
                                        }
                                        onClick={this.getPhoneNumber.bind(this)}
                                        onGetPhoneNumber={this.getPhoneNumber.bind(
                                            this,
                                        )}
                                        bindgetphonenumber={this.getPhoneNumber.bind(
                                            this,
                                        )}
                                        onGetAuthorize={this.getPhoneNumber.bind(
                                            this,
                                        )}
                                    >
                                        {this.state.env === "wx" && (
                                            <Text>使用手机号授权登录</Text>
                                        )}

                                        {this.state.env === "ali" && (
                                            <Text>使用支付宝授权登录</Text>
                                        )}
                                    </Button>
                                    <Image
                                        class="button-decoration-img"
                                        src={this.props.buttonDecorationUrl}
                                    ></Image>
                                </View>

                                <Button
                                    class="switch-phone"
                                    onClick={this.switchPhone.bind(this, true)}
                                >
                                    使用其他手机号登录
                                </Button>
                            </View>
                        ) : (
                            <View class="c-login-first">
                                <Image
                                    className="logo"
                                    src="https://s.qunarzz.com/f_cms/2021/1628172396265_914651792.png"
                                />
                                <View className="p-login-wrap-protocol-platformLoginFirst">
                                    <View
                                        className="p-login-wrap-changeSelected"
                                        onClick={this.changeSelected.bind(this)}
                                    >
                                        <Text className="p-login-wrap-checkbox g-q-iconfont">
                                            {this.props.hasAgreedProtocol
                                                ? ""
                                                : ""}
                                        </Text>
                                        <Text className="p-login-wrap-text">
                                            同意并遵行去哪儿网
                                        </Text>
                                    </View>

                                    <Text
                                        className="p-login-wrap-protocolText"
                                        onClick={this.clickProtocolText.bind(
                                            this,
                                        )}
                                    >
                                        《用户服务协议》
                                    </Text>
                                    <Text className="p-login-wrap-text">
                                        及
                                    </Text>
                                    <Text
                                        className="p-login-wrap-protocolText"
                                        onClick={this.clickProtoColToPrivacyPolicy.bind(
                                            this,
                                        )}
                                    >
                                        《隐私政策》
                                    </Text>
                                </View>
                                <Button
                                    class={
                                        "phoneNumber_login pl-" +
                                        process.env.ANU_ENV +
                                        (!this.props.hasAgreedProtocol
                                            ? " default"
                                            : "")
                                    }
                                    open-type={
                                        this.props.hasAgreedProtocol
                                            ? this.state.env === "ali"
                                                ? "getAuthorize"
                                                : "getPhoneNumber"
                                            : ""
                                    }
                                    scope={
                                        this.state.env === "ali"
                                            ? "phoneNumber"
                                            : ""
                                    }
                                    onClick={this.getPhoneNumber.bind(this)}
                                    onGetphonenumber={this.getPhoneNumber.bind(
                                        this,
                                    )}
                                    bindgetphonenumber={this.getPhoneNumber.bind(
                                        this,
                                    )}
                                    onGetAuthorize={this.getPhoneNumber.bind(
                                        this,
                                    )}
                                >
                                    {this.state.env === "wx" && (
                                        <Text>手机号授权登录</Text>
                                    )}

                                    {this.state.env === "ali" && (
                                        <Text>使用支付宝授权登录</Text>
                                    )}
                                </Button>
                                <Button
                                    class="switch-phone"
                                    onClick={this.switchPhone.bind(this, true)}
                                >
                                    使用其他手机号登录
                                </Button>
                            </View>
                        )}
                    </View>
                ) : (
                    <View />
                )}
            </View>
        );
    }
}

export default PlatformLoginFirst;
