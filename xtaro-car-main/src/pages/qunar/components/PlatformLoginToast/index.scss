@import '../../style/g-define.scss';
@import '../../style/iconfont.scss';
.change-phone-wrap {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.4);

    .login-container {
        display: -webkit-flex;
        display: flex;
        -webkit-flex-direction: column;
        flex-direction: column;
        visibility: hidden;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 12px 12px 0 0;
    }

    .login-container.pop-up {
        top: 0;
    }

    /* show way styles */
    .show-page {
        visibility: visible;
    }

    .show-page .cancel {
        display: none;
    }

    .show-popup {
        position: fixed;
        bottom: 0;
        right: 0;
        height: 100%;
        width: 100%;
        z-index: 100;
        visibility: visible;
    }

    .show-popup-on {
        top: 110px;
        transition: all .4s ease-in-out;
    }

    // .login-popup-mask {
    //     position: fixed;
    //     top: 0;
    //     left: 0;
    //     z-index: 99;
    //     height: 100%;
    //     width: 100%;
    //     background-color: rgba(0, 0, 0, 0.4);
    // }

    .popup-on {
        display: block;
    }

    .popup-off {
        transition: all .4s ease-in-out;
        display: none;
    }


    /* header styles */
    .login-container .header {
        display: -webkit-flex;
        display: flex;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: center;
        align-items: center;
    }

    .login-container .title {
        margin-top: 61px;
        font-family: PingFangSC-Semibold;
        font-size: 24px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        line-height: 24px;
        font-weight: 600;
    }

    .login-container .sub-title {
        margin-top: 19px;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #666666;
        text-align: center;
        line-height: 16px;
        font-weight: 400;
    }

    /* content styles */
    .login-container .content {
        margin-left: 30px;
        margin-right: 30px;
        margin-top: 10px;
    }

    .login-container .item {
        display: -webkit-flex;
        display: flex;
        flex-direction: row;
        -webkit-align-items: center;
        align-items: center;
        // height: 61px;
        border-bottom: 1px solid #eee;
        font-size: 16px;
        padding: 32px 0 7px;

    }

    .login-container .mobile-item {
        position: relative;
    }

    .login-container .mark {
        width: 92px;
        height: 22px;
        font-size: 16px;
        color: #212121;
        line-height: 22px;
    }

    .mobile-item .mark {
        position: relative;
    }

    .mobile-item .mark::after {
        font-family: 'g-q-iconfont';
        position: absolute;
        content: '';
        left: 50px;
        font-size: 14px;
        top: 1px;
        color: #999999;
        font-weight: 400;

    }

    .login-container .clear {
        visibility: hidden;
        padding: 10px;
    }

    .login-container .clear-img {
        width: 14px;
        height: 14px;
    }

    .login-container .input-field {
        z-index: 0;
        height: 22px;
        font-size: 16px;
    }

    .login-container .input-field input {
        height: 22px;
        line-height: 22px;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        letter-spacing: 0;
        font-weight: 400;
    }

    .login-container .input-field,
    .login-container .mobile-input,
    .login-container .sms-input {
        -webkit-flex: 1;
        flex: 1;
    }

    .login-container .sms-code-field,
    .login-container .mobile-field {
        display: -webkit-flex;
        display: flex;
        flex-direction: row;
        -webkit-align-items: center;
        align-items: center;
    }

    .login-container .mobile-input {
        display: inline-block;
        width: 100%;
    }

    .login-container .sms-code {
        display: inline-block;
        height: 22px;
        padding: 0 6px;
        text-align: center;
        font-size: 16px;
        line-height: 22px;
        color: #00CAD8;
        letter-spacing: 0;
        font-weight: 400;
        background-color: #fff !important;
    }

    .login-container .sms-code::after {
        border: none;
    }

    .login-container .gray {
        color: #bdbdbd;
        background-color: #eee;
    }

    .login-container .btn-wrapper {
        margin-top: 30px;
    }

    .login-container .login-btn {
        height: 44px;
        border-radius: 44px;
        background-color: #E6E6E6;
        line-height: 44px;
        text-align: center;
        color: #fff;
        font-size: 18px;
    }

    .login-container .lighter .login-btn {
        background-color: #00BCD4;
    }
    .default{
        background: #E6E6E6;
    }

    .login-container .cancel {
        position: absolute;
        top: 0;
        right: 0;
        height: 45px;
        width: 50px;
        line-height: 45px;
        text-align: center;
        font-size: 19px;
        color: #919191;
    }

    .login-container .cancel::before {
        content: '\f3f3';
    }

    .login-container .show {
        visibility: visible;
    }

    /* self define toast styles  */
    // .login-container .toast-container {
    //     top: 30%;
    //     z-index: 10;
    // }
    .p-login-wrap-protocol-platformLoginToast {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top:40px;

       .p-login-wrap-changeSelected {
            display: flex;
            flex-direction: row;
            align-items: center;
       }
       .p-login-wrap-checkbox.g-q-iconfont {
        font-size: 14px;
        color: #00CAD8;
    }
    
        .p-login-wrap-text {
            font-size: 14px;
            margin-left: 5px;
        }

        .p-login-wrap-protocolText {
            font-size: 14px;
            color: rgb(0, 188, 212);
        }
    }

    .p-login-wrap-protocol-platformLoginQuick {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 55px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #666666;
        text-align: center;
        line-height: 17px;
        font-weight: 400;
        flex-wrap: wrap;

       .p-login-wrap-changeSelected {
            display: flex;
            flex-direction: row;
            align-items: center;
            .radio {
                width: 14px;
                height: 14px;
                margin: 4px;
                border-radius: 7px;
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629788534261_062532121.png");
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            .radio-check {
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629786073626_012462090.png");
            }
    
       }
        .p-login-wrap-checkbox.g-q-iconfont {
            padding: 3.5px 4px 2px;
            font-size: 14px;
            color: #00CAD8;
        }
    
        .p-login-wrap-text {
            margin-left: 1px;
        }

        .p-login-wrap-protocolText {
            color: rgb(0, 188, 212);
        }
    }
}

.full-page {
    position: relative;
    background-color: transparent;

    .show-popup-full {
        position: relative;
        background-color: transparent;
        transition: none;

        .header {
            .title {
                margin-top: 13px;
            }
        }

        .content {
            margin-top: 10px;
        }
    }
}

.change-phone-wrap-new {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.4);

    .login-container {
        display: -webkit-flex;
        display: flex;
        -webkit-flex-direction: column;
        flex-direction: column;
        visibility: hidden;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 16px 16px 0 0;
        background-size: contain;
        background-image: url('https://s.qunarzz.com/f_cms/2023/1688386209801_2837617661.png');
        background-repeat: no-repeat;
    }

    .login-container.pop-up {
        top: 0;
    }

    /* show way styles */
    .show-page {
        visibility: visible;
    }

    .show-page .cancel {
        display: none;
    }

    .show-popup {
        position: fixed;
        bottom: 0;
        right: 0;
        height: 100%;
        width: 100%;
        z-index: 100;
        visibility: visible;
    }

    .show-popup-on {
        top: 110px;
        transition: all .4s ease-in-out;
    }


    .popup-on {
        display: block;
    }

    .popup-off {
        transition: all .4s ease-in-out;
        display: none;
    }


    /* header styles */
    .login-container .header {
        display: -webkit-flex;
        display: flex;
        -webkit-flex-direction: column;
        flex-direction: column;
        -webkit-align-items: center;
        align-items: center;
    }

    .login-container .title {
        margin-top: 61px;
        font-family: PingFangSC-Semibold;
        font-size: 24px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        line-height: 24px;
        font-weight: 700;
    }

    .login-container .sub-title {
        margin-top: 19px;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #666666;
        text-align: center;
        line-height: 16px;
        font-weight: 400;
    }

    /* content styles */
    .login-container .content {
        margin-left: 30px;
        margin-right: 30px;
        margin-top: 10px;
    }

    .login-container .item {
        display: -webkit-flex;
        display: flex;
        -webkit-align-items: center;
        align-items: center;
        border-bottom: 1px solid #eee;
        font-size: 16px;
        padding: 32px 0 7px;

    }

    .login-container .mobile-item {
        position: relative;
    }

    .login-container .mark {
        width: 92px;
        height: 22px;
        font-size: 16px;
        color: #212121;
        line-height: 22px;
    }

    .mobile-item .mark {
        position: relative;
    }

    .mobile-item .mark::after {
        font-family: 'g-q-iconfont';
        position: absolute;
        content: '';
        left: 50px;
        font-size: 14px;
        top: 1px;
        color: #999999;
        font-weight: 400;

    }

    .login-container .clear {
        visibility: hidden;
        padding: 10px;
        padding-right: 0px;
    }

    .login-container .clear-img {
        width: 14px;
        height: 14px;
    }

    .login-container .input-field {
        z-index: 0;
        height: 22px;
        font-size: 16px;
    }

    .login-container .input-field input {
        height: 22px;
        line-height: 22px;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        letter-spacing: 0;
        font-weight: 400;
    }

    .login-container .input-field,
    .login-container .mobile-input,
    .login-container .sms-input {
        -webkit-flex: 1;
        flex: 1;
        justify-content: space-between;

    }

    .login-container .sms-code-field,
    .login-container .mobile-field {
        display: -webkit-flex;
        display: flex;
        flex-direction: row;
        -webkit-align-items: center;
        align-items: center;
    }

    .login-container .mobile-input {
        display: inline-block;
        width: 100%;
    }

    .login-container .sms-code {
        display: inline-block;
        height: 22px;
        padding: 0 6px;
        text-align: center;
        font-size: 16px;
        line-height: 22px;
        color: #00CAD8;
        letter-spacing: 0;
        font-weight: 400;
        background-color: #fff !important;
    }

    .login-container .sms-code::after {
        border: none;
    }

    .login-container .gray {
        color: #bdbdbd;
        background-color: #eee;
    }

    .login-container .content {
        .button-box{
            position: relative;
            width: 317px;
            height: 77px;
            margin-top: 38px;
            background-size: cover;
            background-image: url(https://s.qunarzz.com/f_cms/2023/1688367106359_789162283.png);
            
            .button-decoration-img{
                position: absolute;
                top: -31px;
                right: 0px;
                z-index: 2;
                width: 146px;
                height: 56px; 
            }
    
            .confirm {
                width: 317px;
                height: 44px;
                line-height: 44px;
                background-image: linear-gradient(103deg, #FF8743 0%, #FB4C1A 97%);
                border-radius: 26.5px;
                color: #fff;
                font-size: 18px;
                border-radius: 44px;
                text-align: center;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text {
                    margin: 0 auto;
                    color: #fff;
                    opacity: 0.6;
                }
                .active{
                    opacity: 1;
                }
                &:after {
                    border: 0;
                }
            }
            
            
        }
    }
    .login-container .cancel {
        position: absolute;
        top: 0;
        right: 0;
        height: 45px;
        width: 50px;
        line-height: 45px;
        text-align: center;
        font-size: 19px;
        color: #666666;
    }

    .login-container .cancel::before {
        content: '\f3f3';
    }

    .login-container .show {
        visibility: visible;
    }

    /* self define toast styles  */
    // .login-container .toast-container {
    //     top: 30%;
    //     z-index: 10;
    // }
    .p-login-wrap-protocol-platformLoginToast {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        margin-top:40px;

       .p-login-wrap-changeSelected {
            display: flex;
            flex-direction: row;
            align-items: center;
       }
       .p-login-wrap-checkbox.g-q-iconfont {
        font-size: 14px;
        color: #00CAD8;
    }
    
        .p-login-wrap-text {
            font-size: 14px;
            margin-left: 5px;
        }

        .p-login-wrap-protocolText {
            font-size: 14px;
            color: rgb(0, 188, 212);
        }
    }

    .p-login-wrap-protocol-platformLoginQuick {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        margin-top: 55px;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #666666;
        text-align: center;
        line-height: 17px;
        font-weight: 400;
        flex-wrap: wrap;

       .p-login-wrap-changeSelected {
            display: flex;
            flex-direction: row;
            align-items: center;
            .radio {
                width: 14px;
                height: 14px;
                margin: 4px;
                border-radius: 7px;
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629788534261_062532121.png");
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            .radio-check {
                background-image: url("https://s.qunarzz.com/f_cms/2021/1629786073626_012462090.png");
            }
    
       }
        .p-login-wrap-checkbox.g-q-iconfont {
            padding: 3.5px 4px 2px;
            font-size: 14px;
            color: #00CAD8;
        }
    
        .p-login-wrap-text {
            margin-left: 1px;
        }

        .p-login-wrap-protocolText {
            color: rgb(0, 188, 212);
        }
    }
}