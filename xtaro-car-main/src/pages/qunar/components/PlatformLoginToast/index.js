import { View, Text, Input, Image, Button } from "@tarojs/components";
import React from "react";
import "./index.scss";
import LogQmark from "@/common/utils/logQmark";

const defaultLogParams = {
    page: "authLogin",
    module: "login_toast",
    operType: "click",
};
class PlatformLoginToast extends React.Component {
    componentDidMount() {
        LogQmark({
            ...defaultLogParams,
            id: "login_toast",
            operType: "show",
            ext: {
                newStyle: this.props.newStyle,
            },
        });
    }

    logIn = () => {
        LogQmark({
            ...defaultLogParams,
            id: "login_button",
            ext: {
                newStyle: this.props.newStyle,
            },
        });
        this.props.logIn();
    };
    bindPhoneChange = (e) => {
        const { detail = {} } = e || {}
        this.props.bindPhoneChange({ e, ...detail });
    };
    getCode = () => {
        this.props.getCode();
    };
    bindCodeChange = (e) => {
        const { detail = {} } = e || {}
        this.props.bindCodeChange({ e, ...detail });
    };
    user_login_clearInput = () => {
        this.props.user_login_clearInput();
    };
    switchPhone = (bol) => {
        // console.log(bol, this.props.switchPhone);
        this.props.switchPhone(bol);
    };
    changeSelected = () => {
        this.props.changeSelected();
    };

    clickProtocolText = () => {
        this.props.clickProtocolText();
    };

    clickProtoColToPrivacyPolicy = () => {
        this.props.clickProtoColToPrivacyPolicy();
    };

    render() {
        return (
            <View>
                {this.props.newStyle !== undefined ? (
                    <View
                        class={`${this.props.newStyle ? "change-phone-wrap-new" : "change-phone-wrap"} ${this.props.isFullPage ? "full-page" : ""}`}
                    >
                        <View
                            class={`login-container show-popup ${this.props.isFullPage ? "show-popup-full" : "show-popup-on"}`}
                        >
                            <View class="header">
                                <Text class="title">短信验证码登录</Text>
                                <Text class="sub-title">
                                    如手机号未注册，验证后将自动注册
                                </Text>
                            </View>
                            <View class="content">
                                <View class="item mobile-item">
                                    <Text class="mark">+86</Text>
                                    <View class="input-field mobile-field hide">
                                        <Input
                                            type="number"
                                            maxlength="11"
                                            placeholder={"请输入手机号"}
                                            value={this.props.inputPhone}
                                            placeholder-style="color: #CCC;"
                                            onInput={this.bindPhoneChange.bind(
                                                this,
                                            )}
                                        />
                                        <View
                                            class={
                                                "clear " +
                                                this.props.clearIconClass
                                            }
                                            onClick={this.user_login_clearInput.bind(
                                                this,
                                            )}
                                        >
                                            <Image
                                                class="clear-img"
                                                src="https://s.qunarzz.com/wechatapp/common/images/login/clear.png"
                                            ></Image>
                                        </View>
                                    </View>
                                </View>
                                <View class="item">
                                    <Text class="mark">验证码</Text>
                                    <View class="input-field sms-code-field">
                                        <Input
                                            type="number"
                                            class="sms-input"
                                            maxlength="6"
                                            placeholder={"请输入验证码"}
                                            placeholder-style="color: #CCC;"
                                            onInput={this.bindCodeChange}
                                            value={this.props.code}
                                        />
                                        <Button
                                            type="default"
                                            class={
                                                "sms-code " +
                                                (!this.props.canSendSmsFlag
                                                    ? "gray"
                                                    : "")
                                            }
                                            onClick={this.getCode.bind(this)}
                                        >
                                            {this.props.smsBtnText2}
                                        </Button>
                                    </View>
                                </View>
                                {this.props.isFullPage && (
                                    <View className="p-login-wrap-protocol-platformLoginQuick">
                                        <View
                                            className="p-login-wrap-changeSelected"
                                            onClick={this.changeSelected.bind(
                                                this,
                                            )}
                                        >
                                            <Text
                                                className={`p-login-wrap-checkbox ${this.props.hasAgreedProtocol ? "radio radio-check" : "radio"}`}
                                            ></Text>
                                            <Text className="p-login-wrap-text">
                                                同意并遵行去哪儿网
                                            </Text>
                                        </View>
                                        <Text
                                            className="p-login-wrap-protocolText"
                                            onClick={this.clickProtocolText.bind(
                                                this,
                                            )}
                                        >
                                            《用户协议》
                                        </Text>
                                        <Text className="p-login-wrap-text">
                                            及
                                        </Text>
                                        <Text
                                            className="p-login-wrap-protocolText"
                                            onClick={this.clickProtoColToPrivacyPolicy.bind(
                                                this,
                                            )}
                                        >
                                            《隐私政策》
                                        </Text>
                                    </View>
                                )}
                                {this.props.newStyle ? (
                                    <View class="button-box">
                                        <Button
                                            class="confirm"
                                            onClick={this.logIn.bind(this)}
                                        >
                                            <Text
                                                class={`${this.props.canSubmit && this.props.hasAgreedProtocol ? "active" : ""}`}
                                            >
                                                登录
                                            </Text>
                                        </Button>
                                        <Image
                                            class="button-decoration-img"
                                            src={this.props.buttonDecorationUrl}
                                        ></Image>
                                    </View>
                                ) : (
                                    <View
                                        class={
                                            "btn-wrapper " +
                                            (this.props.canSubmit
                                                ? "lighter"
                                                : "")
                                        }
                                    >
                                        <View
                                            class={
                                                "login-btn" +
                                                (!this.props.hasAgreedProtocol
                                                    ? " default"
                                                    : "")
                                            }
                                            onClick={this.logIn.bind(this)}
                                        >
                                            登录
                                        </View>
                                    </View>
                                )}
                            </View>
                            {!this.props.isFullPage && (
                                <View
                                    class="g-q-iconfont cancel"
                                    onClick={this.switchPhone.bind(this, false)}
                                ></View>
                            )}
                        </View>
                    </View>
                ) : (
                    <View />
                )}
            </View>
        );
    }
}

export default PlatformLoginToast;
