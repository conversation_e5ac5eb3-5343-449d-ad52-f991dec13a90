.vertical-card-box {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    background-color: #ffffff;
    border-radius: 12px;
    padding: 16px 0 16px 16px;
    font-family: PingFangSC-Medium;
    box-sizing: border-box;
    margin-top: 8px;
    margin-left: 10px;
    margin-right: 10px;
}

.vertical-header-image {
    width: 72px;
    height: 18px;
    margin-bottom: 18px;
}

.vertical-card-title {
    padding-left: 16px;
}

.custom-title {
    margin-top: 34px;
}

.vertical-card-title-text {
    font-family: PingFangSC-Semibold;
    font-size: 16px;
    color: #212121;
    letter-spacing: 0;
    font-weight: 600;
}

.vertical-card-scroll-view {
    height: 202px;
    display: flex;
    flex-direction: row;
    overflow-x: auto;
}

.vertical-card-scroll-view::-webkit-scrollbar {
    display: none;
}

.vertical-card-ticket-box {
    display: flex;
    flex-direction: row;
    padding: 16px 6px 0 16px;
    width: max-content;
}

.vertical-card-ticket-item {
    flex-shrink: 0;
    margin-right: 8px;
    position: relative;
    height: 202px;
    background-image: url(https://s.qunarzz.com/f_cms/2023/1694072502827_351258513.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 152px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
}

.item-center-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    flex: 1;
}

.item-title {
    margin-top: 14px;
    font-size: 14px;
    color: #AB3838;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
    flex-shrink: 0;
}

.item-discount-info {
    display: flex;
    flex-direction: row;
    align-items: baseline;
}

.item-money {
    font-size: 16px;
    color: #FF341F;
    letter-spacing: 0;
    font-weight: 700;
    position: relative;
    bottom: 2px;
}

.item-number {
    font-family: hotel_rn_num;
    font-size: 30px;
    color: #FF341F;
    letter-spacing: -0.9px;
    margin: 0 1px;
}

.item-discount {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #FF341F;
    letter-spacing: 0;
    font-weight: 500;
    position: relative;
    bottom: 2px;
}

.item-desc {
    font-family: PingFangSC-Medium;
    font-size: 10px;
    color: #FF341F;
    line-height: 10px;
    letter-spacing: 0;
    font-weight: 500;
}

.item-share {
    margin-top: 5px;
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #AB3838;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
}

.item-share-info {
    margin-top: 1px;
}

.item-bottom-box {
    flex-shrink: 0;
    height: 68px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.item-button {
    margin-top: 4px;
    width: 105px;
    height: 45px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    background-color: transparent;
    background-size: 100%;
    border: 0;
    outline: 0;
    padding: 0;
    background-image: url('https://qimgs.qunarzz.com/wpf_newmpic_001/e8fb99b39f898dc144780b05a12e3204.png');
}

.item-button::after {
    border: 0;
}

.item-button-text {
    margin-top: 2px;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #FF341F;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
}

.item-countdown {
    height: 14px;
    margin-top: -4px;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.item-clock {
    width: 12px;
    height: 12px;
}

.item-countdown-time {
    font-family: PingFangSC-Regular;
    color: #FFF8BA;
    letter-spacing: 0;
    font-weight: 400;
    display: flex;
    flex-direction: row;
}

.vertical-card-ticket-item.success {
    background-image: url(https://qimgs.qunarzz.com/wpf_newmpic_001/53656fa9a1cbb028363dc4cd45e8a656.png);
}

.vertical-card-ticket-item.success .item-share-info {
    display: none;
}

.vertical-card-ticket-item.success .item-share,
.vertical-card-ticket-item.success .item-countdown {
    display: none;
}

.vertical-card-ticket-item.fail {
    background-image: url(https://s.qunarzz.com/f_cms/2023/1695216060386_002269705.png);
}

.vertical-card-ticket-item.fail .item-title {
    margin-top: 20px;
    font-size: 16px;
}

.vertical-card-ticket-item.fail .item-share-info {
    display: none;
}

.vertical-card-ticket-item.fail .item-share,
.vertical-card-ticket-item.fail .item-countdown {
    display: none;
}

.vertical-card-empty {
    height: 202px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex-direction: column;
}

.vertical-card-empty .vertical-card-empty-img {
    width: 246px;
    height: 119px;
}

.vertical-card-empty .vertical-card-empty-desc {
    margin-top: -20px;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #999999;
    letter-spacing: 0;
    font-weight: 400;
}