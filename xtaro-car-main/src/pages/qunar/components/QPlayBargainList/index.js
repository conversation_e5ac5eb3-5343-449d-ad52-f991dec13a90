import { View, Image, ScrollView, Text, Button } from "@tarojs/components";
import { showToast, reLaunch, navigateTo } from "@tarojs/taro";
import React from "react";
import login from "@/common/utils/login";
import QPlayBoostList from "../QPlayBoostList";
import QPlayCountDown from "../QPlayCountDown";
import LogQmark from "@/common/utils/logQmark";
import { apiQmark } from "@/common/utils/logQmark";
import "./index.scss";
const tabPages = [
    "/pages/platform/indexWx/index",
    "/pages/qunar/subPages/orderList/orderList/index",
    "/pages/platform/qPlay/index",
    "/pages/platform/userCenterWx/index",
];

// 记录请求失败和耗时埋点
const sendRequest = apiQmark("qPlay", "nnc_module_qunar_platform");
class QPlayBargainList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            cardItemClassMap: {
                200: "success",
                203: "fail",
            },
        };
        this.btnLock = false;
        this.getBargainList = this.getBargainList.bind(this);
    }

    getBargainList = () => {
        this.props.getBargainList(true);
    };

    fetchInfo = (params = {}) => {
        this.btnLock = true;
        return new Promise((resolve) => {
            sendRequest({
                host: "https://m.flight.qunar.com",
                ignoreStatus: true,
                service: "/gw/f/pallas/sc/bargain/create",
                data: params,
                success: (res) => {
                    this.btnLock = false;
                    const { status, msg } = res;
                    if (status === 0) {
                        this.getBargainList();
                    } else {
                        showToast({
                            icon: "none",
                            title: msg || "网络异常，请稍候再试~",
                        });
                    }
                },
                fail: (e) => {
                    this.btnLock = false;
                    showToast({
                        icon: "none",
                        title: msg || "网络异常，请稍候再试~",
                    });
                    resolve(null);
                },
            });
        });
    };

    handleButtonClick = (data = {}) => {
        const { buttonVo = {}, extMap, activityCode } = data;
        LogQmark({
            module: "default",
            id: "bargainButtonClick",
            page: "qplay",
            operType: "click",
            ext: {
                url: buttonVo.jumpSchema,
                activityCode,
            },
        });
        switch (buttonVo.buttonType) {
            case "create":
                this.btnLock === false && this.fetchInfo(extMap);
                break;
            case "login":
                login(() => {}, { source: "qPlay" });
                break;
            case "jump":
                if (tabPages.includes(buttonVo.jumpSchema.split("?")[0])) {
                    reLaunch({
                        url: buttonVo.jumpSchema,
                    });
                } else {
                    navigateTo({
                        url: buttonVo.jumpSchema,
                    });
                }
                break;
        }
    };

    render() {
        if (!this.props.bargainList || this.props.bargainList.length === 0)
            return <View></View>;
        return (
            <View className="vertical-card-box">
                <Image
                    className="vertical-header-image"
                    src="https://qimgs.qunarzz.com/wpf_newmpic_001/635f1244e7f7da076b5b75a28a312656.png"
                ></Image>
                <View className="vertical-card-content">
                    <ScrollView
                        type="list"
                        scroll-x="true"
                        show-scrollbar={false}
                        enhanced={true}
                        className="vertical-card-scroll-view"
                        enable-flex="true"
                    >
                        {this.props.bargainList.map((item, index) => {
                            return (
                                <View
                                    key={item.activityCode}
                                    className={`vertical-card-ticket-item ${this.state.cardItemClassMap[item.bargainStatus]}`}
                                >
                                    <Text className="item-title">
                                        {item.itemName}
                                    </Text>
                                    <View className="item-center-box">
                                        <View className="item-discount-info">
                                            {item.itemType === "money" ? (
                                                <Text className="item-money">
                                                    ¥
                                                </Text>
                                            ) : (
                                                <Text></Text>
                                            )}
                                            <Text className="item-number">
                                                {item.title}
                                            </Text>
                                            {item.itemType === "discount" ? (
                                                <Text className="item-discount">
                                                    折
                                                </Text>
                                            ) : (
                                                <Text></Text>
                                            )}
                                        </View>
                                        <Text className="item-desc">
                                            {item.deadLineDesc}
                                        </Text>
                                        <Text className="item-share">
                                            {item.topText || ""}
                                        </Text>
                                        <View className="item-share-info">
                                            <QPlayBoostList
                                                activityCode={item.activityCode}
                                                boostRecordList={
                                                    item.boostRecordList || []
                                                }
                                                qrCode={
                                                    item.buttonVo?.shareVo
                                                        ?.imageUrl
                                                }
                                                title={
                                                    item.buttonVo?.shareVo
                                                        ?.title
                                                }
                                                completionBoostThreshold={
                                                    item.completionBoostThreshold
                                                }
                                                wxSchema={item.wxSchema}
                                                btnInfo={item.buttonVo}
                                            ></QPlayBoostList>
                                        </View>
                                    </View>
                                    <View className="item-bottom-box">
                                        <Button
                                            open-type={
                                                item.buttonVo.buttonType ===
                                                "share"
                                                    ? "share"
                                                    : ""
                                            }
                                            className="item-button"
                                            data-title={
                                                item.buttonVo?.shareVo?.title ||
                                                "我正在领优惠券，帮我助力一下哦！"
                                            }
                                            data-image={
                                                item.buttonVo?.shareVo
                                                    ?.imageUrl ||
                                                "https://s.qunarzz.com/f_cms/2023/1695630201407_66988298.png"
                                            }
                                            data-path={item.wxSchema || ""}
                                            onClick={() =>
                                                this.handleButtonClick(item)
                                            }
                                        >
                                            <Text className="item-button-text">
                                                {item.buttonVo.buttonText}
                                            </Text>
                                        </Button>
                                        {item.endTime ? (
                                            <View className="item-countdown">
                                                <Image
                                                    src="https://s.qunarzz.com/f_cms/2023/1694077653365_2288821385.png"
                                                    className="item-clock"
                                                />

                                                <View className="item-countdown-time">
                                                    <QPlayCountDown
                                                        endTime={item.endTime}
                                                        getBargainList={
                                                            this.getBargainList
                                                        }
                                                    ></QPlayCountDown>
                                                </View>
                                            </View>
                                        ) : (
                                            <View></View>
                                        )}
                                    </View>
                                </View>
                            );
                        })}
                    </ScrollView>
                    {/* )} */}
                </View>
            </View>
        );
    }
}

export default QPlayBargainList;
