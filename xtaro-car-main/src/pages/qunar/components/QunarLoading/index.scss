
@import '../../style/iconfont.scss';

/*****************************image base64*****************************/
.qunar-loading-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 170px;
    height: 170px;
    border-radius: 8px;
    overflow: hidden;
}

.qunar-loading-running-camel {
    position: absolute;
    top: 35px;
    width: 152px;
    height: 87px;
}

.qunar-loading-content-bg {
   width: 140px;
   overflow: hidden;
}
.qunar-loading-content-bg-img {
    width: 280px;
    height: 36px;
    margin-top: 62px;
    animation-name: bgRunningX;
    animation-duration: 4s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
}

.network {
  width: 100%;
  position: fixed;
  background: #fff;
  left: 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 150px;
}

.loading-camel-gif {

  background-size: 52px 39px;
}
// 增加选择器权重，防止被抹平样式覆盖
view.network-transparent {
  height: 100%;
  width: 100%;
  position: fixed;
  left: 0;
  text-align: center;
  z-index: 998;
  display: flex;
  flex-direction: row;
  align-items: center;
  // justify-content: center;
}

view.loading-mask-c {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: #000;
  opacity: 0.2;
}

view.loading-style-c {
  position: relative;
  border-radius: 8px;
  width: 164px;
  height: 164px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  // margin: 360rpx auto 0rpx auto;
  // text-align: center;
  z-index: 999;
  transform: translateX(114.5px)
}
view.loading-style-c-bg {
    background: #FFF;
    // opacity: 0.6;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: -1;
    border-radius: 8px;
}
.qunar-loading-camle {
  width: 52px;
  height: 39px;
}

.loading-icon-c {
  width: 47px;
  height: 35px;
  line-height: 35px;
  margin-top: 25px;
  position: relative;
  left: -3px;
  display: inline-block;
  color: #eeeeee;
  font-size: 46px;
}

.loading-icon-c:before {
  content: '\e048';

}

.loading-text-c {
//   margin-top: 20rpx;
//   color: #ffffff;
//   font-size: 32rpx;
  display: block;
  clear: both;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
  position: absolute;
  bottom: 23px;
}

.loading-point-container {
  width: 36px;
  margin-top: 5px;
  height: 6px;
  display: flex;
  flex-direction: row;

}

.loading-point {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  margin: 0 3px;
  background: #ffffff;
  animation-name: dotsLoadding;
  animation-duration: 1.04s;
  animation-timing-function: ease;
  animation-iteration-count: infinite;
}

.qunar-network {
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: column;
  background: #fff;
}

.q-net-wrap {
  margin-top: 180px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  width: 100%;
}

.qunar-loading-bg {
  top: 0;
  // left: 142rpx;
  width: 233px;
  height: 233px;
  opacity: 0.5;
  z-index: -1;
  animation-name: bgRunning;
  animation-duration: 5s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;

}

.qunar-abs {
  position: absolute;
}

.qunar-loading-mask {
  position: absolute;
  top: 50px;
  // left: 75rpx;
  width: 300px;
  height: 300px;
  background: #fff;
  // margin-top: 100rpx;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: row;
}

.qunar-loading-text {
  position: relative;
  /*top:-460rpx;*/
  /*margin-top:13rpx;*/
  color: #00bcd4;
  font-size: 14px;
}

.error-image {
//   width: 200rpx;
//   height: 120rpx;
//   margin: 140rpx auto 40rpx auto;
  display: block;
  clear: both;
  width: 250px;
  height: 144px;
}

.error-text {
//   font-size: 28rpx;
//   width: 60%;
//   line-height: 40rpx;
//   color: #00bcd4;
//   margin: 26rpx 0;
//   text-align: center;
  font-family: PingFangSC-Regular;
font-size: 14px;
color: #999999;
letter-spacing: 0;
text-align: center;
font-weight: 400;
margin-top: 14px;
}

.error-button {
//   background-color: #fff;
//   margin: 64rpx 150rpx;
//   border: 2rpx solid #00bcd4;
//   padding: 32rpx 80rpx;
//   border-radius: 12rpx;
  width: 88px;
  height: 36px;
  background: #00D4E3;
  border-radius: 18px;
  margin-top: 14px;

}

.error-button-text {
//   color: #00bcd4;
//   font-size: 32rpx;
    font-family: PingFangSC-Semibold;
    font-size: 14px;
    color: #FFFFFF;
    font-weight: 600;
    line-height: 36px;

}

.error-button::after {
  border: 0;
}

.error-button-hover {
  background-color: #b2eaf2;
  border: 1px solid #0089a1;
  color: #0089a1;
}

@keyframes bgRunning {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes bgRunningX {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-70px);
    }
}

@keyframes dotsLoadding {
  0% {
    opacity: 1;
    transform: scale(1.2);
  }

  100% {
    opacity: 0.2;
    transform: scale(0.2);
  }
}

.loading-point-1 {
  animation-delay: 0.13s;
}

.loading-point-2 {
  animation-delay: 0.26s;
}

.loading-point-3 {
  animation-delay: 0.39s;
}

/*自定义toast*/

.white-background {
  background: #fff;
}

.network-toast-container {
  height: 100%;
  width: 100%;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  text-align: center;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.network-margin-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-height: 80%;
  max-width: 80%;
  background-color: rgba(0, 0, 0, 0.8);
  padding: 10px;
  border-radius: 5px;
}

.network-toast-text {
  padding: 10px 10px;
  color: #fff;
  font-size: 16px;
}

.qunar-loading {
  width: 35px;
  height: 35px;
}