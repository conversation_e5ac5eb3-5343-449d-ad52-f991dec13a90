import { View, Text, Image } from "@tarojs/components";
import React from "react";
import utils from "@/common/utils/util";

import "./index.scss";
class QunarLoading extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            status: 4,
            loadingDesc: "努力加载中",
            showButton: true,
            networkRetry: "networkRetry",
            loadingDescColor: "#00BCD4",
            ANU_ENV: process.env.ANU_ENV,
        };
    }

    async componentDidMount() {
        const { windowHeight, windowWidth } = await utils.getSystemInfo();

        this.setState({
            height:
                this.props.height || `${(windowHeight * 750) / windowWidth}rpx`,
        });
    }

    retry = () => {
        this.props.networkRetry && this.props.networkRetry();
    };

    // 纯文字，透明背景  -3
    // 转圈loading和文字，白色背景1
    // 转圈loading和文字，透明背景2
    // 浮层骆驼，半透明背景 3
    // 全屏骆驼，白色背景 4
    // 全屏骆驼错误页，白色背景，网络错误-1
    // 全屏骆驼错误页，白色背景，无结果-2

    render() {
        return (
            <View>
                {this.props.show && (
                    <View style={{ zIndex: this.props.zIndex }}>
                        {/* 浮层半透明，文字 */}
                        {this.props.networkData.status === -3 && (
                            <View class="network-toast-container">
                                <View class="network-margin-container">
                                    <Text class="network-toast-text">
                                        {this.props.networkData.loadingDesc ||
                                            "未知错误"}
                                    </Text>
                                </View>
                            </View>
                        )}

                        {/* 浮层半透明，转圈loading */}
                        {(this.props.networkData.status == 1 ||
                            this.props.networkData.status == 2) && (
                            <View
                                class={
                                    "network-toast-container " +
                                    (this.props.networkData.status == 1
                                        ? "white-background"
                                        : "")
                                }
                                style={{
                                    top: this.props.top,
                                    bottom: this.props.bottom,
                                }}
                            >
                                <View class="network-margin-container">
                                    <Image
                                        src="https://qimgs.qunarzz.com/wpf_newmpic_001/3a1285d1bc530c208c52373e18020ef8.gif"
                                        class="qunar-loading"
                                    />
                                    <Text class="network-toast-text">
                                        {this.props.networkData.loadingDesc ||
                                            "加载中..."}
                                    </Text>
                                </View>
                            </View>
                        )}

                        {/* 浮层半透明，页面中心，加载中，无文字 */}
                        {this.props.networkData.status == 3 && (
                            <View
                                class="network-transparent"
                                style={{
                                    top: this.props.top,
                                    bottom: this.props.bottom,
                                }}
                            >
                                <View class="loading-mask-c" />
                                <View class="loading-style-c">
                                    <View class="loading-style-c-bg"></View>
                                    <View className="qunar-loading-container">
                                        <Image
                                            className="qunar-loading-running-camel"
                                            src="https://qimgs.qunarzz.com/wpf_newmpic_001/7613bd9167d244780ebd5c79fb7ffafd.gif"
                                        />
                                        <Text class="loading-text-c">
                                            {this.props.networkData
                                                .loadingDesc || ""}
                                        </Text>
                                    </View>
                                </View>
                            </View>
                        )}

                        {/* 全屏，努力加载中 */}
                        {this.props.networkData.status == 4 && (
                            <View
                                class="qunar-network"
                                style={{
                                    top: this.props.top,
                                    bottom: this.props.bottom,
                                    height: this.state.height,
                                    zIndex: this.props.zIndex,
                                }}
                            >
                                <View class="q-net-wrap q-net-wrap-text">
                                    <View className="qunar-loading-container">
                                        <Image
                                            className="qunar-loading-running-camel"
                                            src="https://qimgs.qunarzz.com/wpf_newmpic_001/7613bd9167d244780ebd5c79fb7ffafd.gif"
                                        />
                                        <Text class="loading-text-c">
                                            {this.props.networkData
                                                .loadingDesc || ""}
                                        </Text>
                                    </View>
                                </View>
                            </View>
                        )}

                        {/* 全屏，网络错误、未知错误 */}
                        {(this.props.networkData.status == -1 ||
                            this.props.networkData.status == -2) && (
                            <View
                                class="network"
                                style={{
                                    top: this.props.top,
                                    bottom: this.props.bottom,
                                    height: this.state.height,
                                    zIndex: this.props.zIndex,
                                }}
                            >
                                <Image
                                    class="error-image"
                                    src={
                                        this.props.networkData.status == -1
                                            ? "https://qimgs.qunarzz.com/wpf_newmpic_001/dce1b745dd146a50db52a11d83920d23.png"
                                            : "https://qimgs.qunarzz.com/wpf_newmpic_001/6e2a725c4f5b999f47d942053b021a6c.png"
                                    }
                                />

                                <Text class="error-text">
                                    {this.props.networkData.loadingDesc ||
                                        (this.props.networkData.status == -1
                                            ? "哎呀，网络连接出现问题，请检查下网络吧～"
                                            : "没有符合条件的搜索结果")}
                                </Text>
                                {!(
                                    this.props.networkData.showButton === false
                                ) && (
                                    <View
                                        class="error-button"
                                        hover-class="error-button-hover"
                                        onClick={this.retry.bind(this)}
                                    >
                                        <Text class="error-button-text">
                                            点击重试
                                        </Text>
                                    </View>
                                )}
                            </View>
                        )}
                    </View>
                )}
            </View>
        );
    }
}

QunarLoading.defaultProps = {
    networkData: {
        status: 4, // { -2:'没有符合条件的搜索结果', -1:'网络连接失败', 0:success,
        //   1:'toast white', 2:'toast transparent', 3:''qunar加载中mini', 4:'qunar加载中' }
        loadingDesc: "加载中...", // loading描述(可选)
        showButton: true, // 显示重新加载 button
    },

    width: "100%",
    top: 0,
    bottom: 0,
    zIndex: 100,
    show: true,
};

export default QunarLoading;
