import { View, Image, Button } from "@tarojs/components";
import React from "react";
import LogQmark from "@/common/utils/logQmark";
import "./index.scss";
class QPlayBoostList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            showBoostRecordList: [],
        };
    }

    shouldComponentUpdate(nextProps) {
        if (
            this.props.completionBoostThreshold !==
                nextProps.completionBoostThreshold ||
            this.props.boostRecordList !== nextProps.boostRecordList
        ) {
            const maxShowInviteNumber =
                nextProps.completionBoostThreshold ||
                this.props.completionBoostThreshold ||
                4;
            let boostRecordList =
                nextProps.boostRecordList || this.props.boostRecordList || [];
            this.handleBoostRecordList(maxShowInviteNumber, boostRecordList);
        }
        return true;
    }

    componentDidMount() {
        const maxShowInviteNumber = this.props.completionBoostThreshold || 4;
        let boostRecordList = this.props.boostRecordList || [];
        this.handleBoostRecordList(maxShowInviteNumber, boostRecordList);
    }

    handleBoostRecordList = (maxShowInviteNumber, boostRecordList) => {
        let showBoostRecordList = [];
        if (maxShowInviteNumber > boostRecordList.length) {
            showBoostRecordList = boostRecordList.concat(
                new Array(maxShowInviteNumber - boostRecordList.length).fill(
                    {},
                ),
            );
        } else {
            showBoostRecordList = boostRecordList.slice(0, maxShowInviteNumber);
        }
        this.setState({
            showBoostRecordList,
        });
    };

    invite = (item) => {
        LogQmark({
            module: "default",
            id: "boostItemClick",
            page: "qplay",
            operType: "click",
            ext: {
                path: this.props.wxSchema,
                buttonType: item.buttonType,
                activityCode: this.props.activityCode,
            },
        });
    };

    render() {
        return (
            <View class="m-cms-share-info-view">
                {this.state.showBoostRecordList?.map((item) => {
                    return item.userLogo ? (
                        <View className="boost-info-box">
                            <Image
                                className="booster-info-img"
                                src={item.userLogo}
                            />
                        </View>
                    ) : this.props.btnInfo.buttonType === "share" ? (
                        <Button
                            open-type="share"
                            data-title={
                                this.props.title ||
                                "我正在领优惠券，帮我助力一下哦！"
                            }
                            data-image={
                                this.props.qrCode ||
                                "https://s.qunarzz.com/f_cms/2023/1695630201407_66988298.png"
                            }
                            data-path={this.props.wxSchema}
                            className="boost-info-box plus"
                            onClick={() => this.invite(item)}
                        ></Button>
                    ) : (
                        <View className="boost-info-box plus"></View>
                    );
                })}
            </View>
        );
    }
}

export default QPlayBoostList;
