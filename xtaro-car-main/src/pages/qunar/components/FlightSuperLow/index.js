import { View, Text, Swiper, SwiperItem, Image } from "@tarojs/components";
import { navigateTo } from "@tarojs/taro";
import React from "react";
import EventEmitter from "@/common/utils/EventEmitter";

import utils from "@/common/utils/util";
import version from "@/common/utils/version";
import request from "@/common/utils/request.js";
import log from "@/common/flight/log.js";
import Log from "@/common/utils/log";
import "./index.scss";

class FlightSuperLow extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            curIndex: 0,
            isTestTheme: props.versionGroup === "A",
            superLow: {},
        };

        this.registerListener();
    }

    registerListener = () => {
        EventEmitter.addListener("GET_DEPCITY", (data) => {
            this.setState({ depCity: data }, () => {
                this.superLow();
            });
        });
    };

    superLow = async () => {
        const queryData = await this.getSuperLowData();
        // 为了修复轮播到第二屏和第三屏展示问题
        const data =
            queryData &&
            queryData.data &&
            queryData.data.homeSuperLowRecommendItems &&
            Array.isArray(queryData.data.homeSuperLowRecommendItems)
                ? queryData.data
                : {
                      homeSuperLowRecommendItems: [],
                  };
        data.homeSuperLowRecommendItems = [
            ...data.homeSuperLowRecommendItems,
            ...data.homeSuperLowRecommendItems,
        ];
        this.setState({
            superLow: data,
        });
    };

    getSuperLowData = async () => {
        const systemInfo = await utils.getSystemInfo();
        const { cookies, user } = await utils.getGlobalInfoAsync();
        const buQpVersion = version.versionName;
        const { depCity } = this.state;

        const dataParam = {
            depCity,
            cat: "miniapp_bargain_recommend_item",
            rnVersion: "",
            batom: "",
            qpInfos: {},
            mpVersion: systemInfo.version,
            mpQpVersion: buQpVersion,
            from: cookies.bd_source,
            // 和公共默认传的的区分开，因为公共请求值在query中携带
            flightOpenId: user.openId,
        };

        return new Promise((resolve, reject) => {
            request({
                host: "https://m.flight.qunar.com",
                service: "/gw/f/flight/home/<USER>/low/price",
                data: dataParam,
                method: "POST",
                ignoreStatus: true,
                success: (res) => {
                    resolve(res);
                },
            });
        });
    };

    change = (e) => {
        const curIndex = e.current;
        this.setState({
            curIndex,
        });
    };

    openNew = (e) => {
        Log({
            info: {
                area: "flightList",
                act: "click",
                type: "homeEnter",
                name: "flightList",
            },
        });
        let url = e.currentTarget.dataset.url;

        navigateTo({
            url: `/flight/pages/h5/h5?url=${encodeURIComponent(url)}&loginSync=true`,
        });

        const type = e.currentTarget.dataset.type;
        const r =
            type === "more" ? "super_low_more_click" : "super_low_list_click";

        log({
            p: "mini_home",
            r,
            rf: process.env.ANU_ENV,
        });
    };

    render() {
        return (
            <View
                className={
                    this.state.isTestTheme
                        ? "home-super-low home-super-test"
                        : "home-super-low"
                }
            >
                {this.state.superLow.homeSuperLowRecommendItems &&
                    this.state.superLow.homeSuperLowRecommendItems.length && (
                        <View>
                            {this.state.superLow.subTitle && (
                                <View className="title-box">
                                    <View className="super-title">
                                        限时超值低价
                                    </View>
                                    <View
                                        className="super-more"
                                        data-url={
                                            this.state.superLow.subTitleScheme
                                        }
                                        data-type="more"
                                        onClick={this.openNew}
                                    >
                                        {this.state.superLow.subTitle}
                                        <Text className="flight-icon"></Text>
                                    </View>
                                </View>
                            )}

                            <Swiper
                                className="home-super-list"
                                display-multiple-items={2}
                                circular
                                next-margin={
                                    process.env.ANU_ENV === "ali"
                                        ? "206rpx"
                                        : "80rpx"
                                }
                                onChange={this.change}
                            >
                                {this.state.superLow.homeSuperLowRecommendItems.map(
                                    (item, index) => {
                                        return (
                                            <SwiperItem
                                                className="super-item"
                                                key={index}
                                            >
                                                <View className="super-item-cont">
                                                    <View className="super-top">
                                                        <View
                                                            className="top-title"
                                                            style={
                                                                "color:" +
                                                                item.titleColor
                                                            }
                                                        >
                                                            {item.title}
                                                        </View>
                                                        <Image
                                                            className="top-img"
                                                            src={item.goPicUrl}
                                                        />
                                                        <Image
                                                            className="top-img-bg"
                                                            src={item.bgPicUrl}
                                                        />
                                                    </View>
                                                    {item.flightInfoList.map(
                                                        (value, index) => {
                                                            return (
                                                                <View
                                                                    className="super-subject"
                                                                    key={index}
                                                                    data-url={
                                                                        value.scheme
                                                                    }
                                                                    data-type="list"
                                                                    onClick={
                                                                        this
                                                                            .openNew
                                                                    }
                                                                >
                                                                    <View className="subject-line">
                                                                        <Text className="line-from">
                                                                            {
                                                                                value.depCity
                                                                            }
                                                                        </Text>
                                                                        <Text className="flight-icon-arr flight-icon">
                                                                            {value.travelType ===
                                                                            "1"
                                                                                ? ""
                                                                                : ""}
                                                                        </Text>
                                                                        <Text className="line-to">
                                                                            {
                                                                                value.arrCity
                                                                            }
                                                                        </Text>
                                                                    </View>
                                                                    {value.travelType ===
                                                                        "1" && (
                                                                        <View className="subject-desc">
                                                                            {value.depDate +
                                                                                " " +
                                                                                value.depWeekDay}
                                                                        </View>
                                                                    )}
                                                                    {value.travelType ===
                                                                        "2" && (
                                                                        <View>
                                                                            <View className="subject-desc">
                                                                                {value.depDate +
                                                                                    " " +
                                                                                    value.depWeekDayDesc}
                                                                            </View>
                                                                            <View className="subject-desc">
                                                                                {value.backDate +
                                                                                    " " +
                                                                                    value.backWeekDayDesc}
                                                                            </View>
                                                                        </View>
                                                                    )}
                                                                    <View className="subject-price">
                                                                        <Text className="y">
                                                                            ¥
                                                                        </Text>
                                                                        <Text className="num">
                                                                            {
                                                                                value.price
                                                                            }
                                                                        </Text>
                                                                    </View>
                                                                </View>
                                                            );
                                                        },
                                                    )}

                                                    {item.pictureUrl && (
                                                        <View className="super-item-bottom-bg">
                                                            <Image
                                                                className="item-bottom-bg"
                                                                src={
                                                                    item.pictureUrl
                                                                }
                                                            />
                                                        </View>
                                                    )}
                                                </View>
                                            </SwiperItem>
                                        );
                                    },
                                )}
                            </Swiper>
                            <View className="super-point">
                                {this.state.superLow.homeSuperLowRecommendItems.map(
                                    (item, index) => {
                                        return (
                                            <Text
                                                className={
                                                    this.state.curIndex ===
                                                    index
                                                        ? "point-item cur"
                                                        : "point-item"
                                                }
                                                key={index}
                                            />
                                        );
                                    },
                                )}
                            </View>
                        </View>
                    )}
            </View>
        );
    }
}

export default FlightSuperLow;
