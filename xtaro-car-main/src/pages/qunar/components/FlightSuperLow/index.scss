@font-face {
    font-family: 'flight-iconfont';
    src: url('https://s.qunarzz.com/nnc_flight/fonts/0.0.26/nnc_flight.ttf');
}

@font-face {
    font-family: "hotel_rn_num";
    src: url("https://s.qunarzz.com/flight_shark/lowPriceRn/hotel_rn_num.ttf");
}

.flight-icon {
    font-family: 'flight-iconfont' !important;
    font-size: 12px;
    font-style: normal;
}


.home-super-low{
    // background-color: #FFF;
    padding-bottom: 8px;
    margin-top: 20px;
}
.home-super-low .title-box{
    padding: 15px 5px 4px 15.5px;
    height: auto;
    overflow: hidden;
    margin-bottom: -2.5px;
}
.home-super-low .super-title{
    font-size: 18px;
    height: 33.5px;
    list-style: 47px;
    font-weight: bold;
    float: left;
}
.home-super-low .super-more{
    padding-top:4px;
    float: right;
    font-size: 12px;
    color:#717171;
    .flight-icon{
        vertical-align: -5px;
        font-size:18px;
    }
}
.home-super-low .home-super-list{
    white-space: nowrap;
    height: 281px !important;
}
.home-super-low .home-super-list .super-item{
    display: inline-block;
}
.home-super-low .home-super-list .super-item-cont{
    width: 140px;
    height: 260px;
    border-radius:12px;
    background:#fff;
    padding:10px;
    overflow: hidden;
    position: relative;
    //box-shadow: 0 8px 16px 2px rgba(51,51,51,0.04);
    margin-left:8px;
}
.home-super-low .home-super-list .super-item-cont .super-item-bottom-bg{
    position: absolute;
    bottom:0;
    left:0;
    width: 167.5px;
    height: 42px;
    border-radius:0 0 4px 4px;
    overflow: hidden;
}
.home-super-low .home-super-list .super-item-cont .super-item-bottom-bg .item-bottom-bg{
    width: 167.5px;
    height: 42px;
}
.home-super-list .super-item .super-top{
    height: 45px;
    font-size: 0;
    position: relative;
}
.home-super-list .super-item .super-top .top-title{
    font-size:15px;
    margin-bottom: 1.5px;
    font-weight: bold;
}
.home-super-list .super-item .super-top .top-img{
    width: 34px;
    height: 17px;
}
.home-super-list .super-item .super-top .top-img-bg{
    position: absolute;
    height: 56px;
    width: 80px;
    right: -6.5px;
    top: -9.5px;
}
.home-super-list .super-item .super-subject{
    background: #F7F8FA;
    border-radius:8px;
    padding: 7px 10px 10px;
    margin:0 0px 4px 0px;
    position: relative;
}
.home-super-list .flight-icon-arr{
    margin:0 3px;
    color:#BDBDBD;
}

.home-super-list .super-item .super-subject .subject-line {
    height: 22px;
    line-height: 22px;
}

.home-super-list .super-item .super-subject .subject-line .line-from,
.home-super-list .super-item .super-subject .subject-line .line-to{
    font-size: 14px;
    color:#333;
    font-weight: bold;
}
.home-super-list .super-item .super-subject .subject-desc{
    font-size:10px;
    color:#999;
    margin-top:2px;
    line-height: 1.1;
}
.home-super-list .super-item .super-subject .subject-price{
    position: absolute;
    bottom: 5px;
    right: 10px;
    color: #FF6600;
    font-size: 15px;
}
.home-super-list .super-item .super-subject .subject-price .y{
    font-size:8px;
}
.home-super-list .super-item .super-subject .subject-price .num{
    font-family: hotel_rn_num;
}

.home-super-low .super-point{
    margin-top:8px;
    text-align: center;
    font-size: 0;
}
.home-super-low .super-point .point-item{
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius:4px;
    background: #dedede;
    margin: 0 2px;
}
.home-super-low .super-point .point-item.cur{
    width: 9px;
    background: #909090;
}
.super-low-bottom{
    text-align: center;
    padding:38px 0 44px;
    font-size: 12px;
    color: #9E9E9E;
    .flight-icon{
        font-size: 16px;
        vertical-align: middle;
    }
}

.home-super-test {
    margin-top: 9px;
}
.home-super-test .title-box {
    padding-top: 0;
    padding-left: 24px;
}

.home-super-test .home-super-list {
    padding-left: 4px;
}
