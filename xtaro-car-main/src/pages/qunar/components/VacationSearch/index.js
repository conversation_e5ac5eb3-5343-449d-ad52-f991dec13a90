import { Block, View, Text, Input } from "@tarojs/components";
import { getStorageSync, setStorageSync, navigateTo } from "@tarojs/taro";
import React from "react";
import util from "@/common/utils/util.js";
import getLocation from "@/common/utils/location";
import EventEmitter from "@/common/utils/EventEmitter";
import Log from "@/common/utils/log";
import sk from "@/common/vacation/sk";

import "./index.scss";

const pagePath = {
    wxCitySelect: "/pages/vacation/citySelect/index",
    vacationSearch: "/pages/vacation/search/index",
};

const HOME_VACATION_CITY = "HOME_VACATION_CITY";

class VacationSearch extends React.Component {
    constructor(props) {
        super(props);
        const { cookies } = util.getGlobalInfo() || {};
        this.state = {
            dep: "",
            province: "",
            _q: cookies._q,
        };
        this._initListener();
        // 获取缓存
        let cacheLocation = this.getQueryCity();

        if (cacheLocation && cacheLocation.city) {
            setTimeout(() => {
                EventEmitter.dispatch(
                    "HOME_VACATION_CITY_SELECT",
                    cacheLocation,
                );
            }, 100);
        } else {
            this.location((data) => {
                // 发布一次消息
                EventEmitter.dispatch("HOME_VACATION_CITY_SELECT", data);
            });
        }
    }

    componentDidMount() {}

    componentWillUnmount() {
        this._cityListener && this._cityListener.removeListener();
    }

    // 获取缓存城市
    getQueryCity = () => {
        // 获取城市位置信息
        return getStorageSync(HOME_VACATION_CITY);
    };

    location = (callback) => {
        const getDep = () => {
            return new Promise((resolve) => {
                let timer = setTimeout(() => {
                    resolve(["北京", ""]);
                }, 3000);
                getLocation({
                    success: function (res) {
                        clearTimeout(timer);
                        let dep = res.city || "";
                        let province = res.province || "";
                        //截去'市'
                        dep.length > 0 && (dep = dep.replace(/市$/, ""));
                        resolve([dep, province]);
                    },
                    fail: function () {
                        clearTimeout(timer);
                        resolve(["", ""]);
                    },
                });
            });
        };

        getDep().then(_setDep);

        function _setDep(str) {
            let city = str[0] || "北京";
            let data = {
                city: decodeURIComponent(city),
                province: decodeURIComponent(str[1]),
            };
            typeof callback === "function" && callback(data);
        }
    };

    _initListener = () => {
        this._cityListener = EventEmitter.addListener(
            "HOME_VACATION_CITY_SELECT",
            (data) => {
                this.setDep(data);
                setStorageSync(HOME_VACATION_CITY, data);
            },
        );
    };

    setDep = (res) => {
        const { city = "北京" } = res;
        this.setState({
            dep: city,
        });
    };

    selectCity = () => {
        Log({
            info: {
                area: "vacationSearch",
                act: "click",
                type: "homeEnter",
                name: "vacationSearch",
            },
        });
        sk.click("wxapp_dujia_search_dep", { edata: { dep: this.state.dep } });
        const params = {
            type: 3,
            eventType: "HOME_VACATION_CITY_SELECT",
            cityListService: "/vacation/sgt/suggest/cities",
            citySuggestService: "/vacation/sgt/suggest/mpDepSuggest",
            placeholder: "请输入城市名称或首字母",
        };
        const citySelectPath = pagePath.wxCitySelect;
        navigateTo({ url: `${citySelectPath}?data=${JSON.stringify(params)}` });
    };

    gotoSearch = () => {
        Log({
            info: {
                area: "vacationSearch",
                act: "click",
                type: "homeEnter",
                name: "vacationSearch",
            },
        });
        sk.click("wxapp_dujia_search_dest");
        let { dep } = this.state;
        const vacationSearch = pagePath.vacationSearch;
        navigateTo({ url: `${vacationSearch}?dep=${dep}&type=1` });
    };

    render() {
        return (
            <Block>
                <View
                    class={`vacation-search content ${this.props.isNew ? "new-content" : ""}`}
                >
                    <View class="con">
                        {this.props.isNew ? (
                            <View class="main">
                                <View class="main-box">
                                    {this.state.dep ? (
                                        <View
                                            class="dep"
                                            onClick={this.selectCity.bind(this)}
                                        >
                                            <Text class="dep-text">
                                                {this.state.dep}
                                            </Text>
                                        </View>
                                    ) : (
                                        <View class="dep">定位中...</View>
                                    )}

                                    <Text class="p-wx-iconfont">&#xe3f7;</Text>
                                    <View class="line"></View>
                                    <View
                                        class="query"
                                        name="searchName"
                                        onClick={this.gotoSearch.bind(this)}
                                    >
                                        目的地/关键词
                                    </View>
                                    <View
                                        class="query-btn"
                                        onClick={this.gotoSearch.bind(this)}
                                    >
                                        搜索
                                    </View>
                                </View>
                            </View>
                        ) : (
                            <View class="main">
                                <Text class="dep-icon g-q-iconfont">
                                    &#xf3e5;
                                </Text>
                                {this.state.dep ? (
                                    <View
                                        class="dep"
                                        onClick={this.selectCity.bind(this)}
                                    >
                                        <Text class="dep-text">
                                            {this.state.dep}
                                        </Text>
                                        出发
                                    </View>
                                ) : (
                                    <View class="dep">定位中...</View>
                                )}

                                <View class="line"></View>
                                <Input
                                    class="query"
                                    disabled="disabled"
                                    placeholder={"目的地/关键词"}
                                    placeholder-style="color: #CCC;font-size: 28rpx"
                                    name="searchName"
                                    onClick={this.gotoSearch.bind(this)}
                                />
                                <View
                                    class="query-btn"
                                    onClick={this.gotoSearch.bind(this)}
                                >
                                    搜索
                                </View>
                            </View>
                        )}
                    </View>
                </View>
            </Block>
        );
    }
}

export default VacationSearch;
