@font-face {
    font-family: 'g-q-iconfont';
    // src: url('/assets/css/minprogram_nanachi.ttf')
    // src: url("https://ss.qunarzz.com/yo/font/1.0.3/yofont.ttf");
    src: url('https://s.qunarzz.com/nanachi/score/font/0.0.22/minprogram_nanachi.ttf');
}

@font-face {
    font-family: 'wechat_cameler';
    src: url("https://s.qunarzz.com/package_mobile/fonts/wxapp/0.1.51/wechat_cameler.woff") format("woff"),
    url("https://s.qunarzz.com/package_mobile/fonts/wxapp/0.1.51/wechat_cameler.ttf") format("truetype");
}

.g-q-iconfont {
    font-family: 'g-q-iconfont';
    font-family: 'g-q-iconfont' !important;
    font-style: normal;
}
.startPosition, .endPosition{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

button {
    box-sizing: content-box;
    background: #FFFFFF;
    border: none;
    outline: none;
    background: none;
    &:after {
        border: none;
    }
}

button:active {
    background: #FFFFFF;
}
.vacation-search {
    &.content {
        background-color: #F6F7FA;
        width: auto;
        padding-top: 8px;
        padding-bottom: 12px;
        padding-left: 8px;
        padding-right: 8px;
    }
    .con {
        padding-top: 16px;
    }
    .main {
        margin-left: 16px;
        margin-right: 16px;
        border: 1px solid rgba(0,202,216,1);
        border-radius: 22px;
        padding: 4px;
        padding-left: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
    }
    
    .dep-icon {
        font-size: 18px;
        color: #666;
        padding-right: 2px;
    }
    .dep {
        display: flex;
        font-size: 16px;
        color: #222222;   
    }
    .dep-text {
        display: inline-block;
        max-width: 65px;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .line {
        height: 14px;
        border-left: 1px solid rgba(204,204,204,1);
        margin: 0 8px;
    }
    .query {
        flex: 1;
        color: #CCC;
        font-size: 14px
    }
    
    .query-btn {
        background: #00CAD8;
        color: #fff;
        padding: 4px 12px;
        border-radius: 22px;
        font-size: 16px;
        font-weight: bold;
    }
    
    &.new-content {
        padding-left: 0;
        padding-right: 0;
        padding-top: 0;
        padding-bottom: 0;
        background-color: #FFFFFF;
    
        .main {
            border: 0;
            background-image: linear-gradient(90deg, #00E3CE 0%, #01D5E3 99%);
            border-radius: 22px;
            padding: 1px
        }
    
        .main-box {
            width: 100%;
            border-radius: 22px;
            padding: 4px;
            padding-left: 16px;
            display: flex;
            align-items: center;
            flex-direction: row;
            background-color: #fff;  
        }
    
        .p-wx-iconfont {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            color: #666;
            font-family: "wechat_cameler";
        }
    
        .line {
            height: 14px;
            border-left: 1px solid #E8E8E8;
            margin-right: 12px;
        }
    
        .dep {
            .dep-text {
                font-size: 14px;
                color: #333;
                margin-right: 4px;
                font-weight: 700;
            }
        }
        .query {
            height: 20px;
            line-height: 20px;
        }
        .query-btn {
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 16px;
            padding: 6px 13px;
            font-size: 14px;
            background-image: linear-gradient(90deg, #00E3CE 0%, #01D5E3 99%);
        }
    }
}
