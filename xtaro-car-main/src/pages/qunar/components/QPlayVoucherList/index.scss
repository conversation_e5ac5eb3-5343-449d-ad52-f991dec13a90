@font-face {
    font-family: "g-q-iconfont";
    src: url("https://s.qunarzz.com/nanachi/score/font/0.0.101/minprogram_nanachi.ttf");
}

@font-face {
    font-family: "hotel_rn_num";
    src: url("https://s.qunarzz.com/flight_shark/lowPriceRn/hotel_rn_num.ttf");
}

.coupon-container {
    .g-q-iconfont {
        font-family: "g-q-iconfont";
        font-family: "g-q-iconfont" !important;
        font-size: 12px;
        font-style: normal;
    }

    .coupon-section {
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        display: flex;
        padding: 16px 10px 12px;
        border-width: 1px;
        border-style: solid;
        border-color: #ffffff;
        border-radius: 16px;
        margin-left: 10px;
        margin-right: 10px;
        background: linear-gradient(-180deg, rgba(255, 255, 255, 0.85), #ffffff);
        box-sizing: border-box;
    }

    .coupon-header-content {
        display: flex;
        flex-direction: row;
        width: 100%;
        justify-content: space-between;
    }

    .highlight-image {
        margin-left: 2px;
        width: 170px;
        height: 18px;
    }

    .div-all-link {
        display: flex;
        align-items: center;
        margin-right: 5px;
        text-align: right;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #666666;
        font-size: 12px;
    }

    .div-all-link-icon {
        font-size: 12px;
    }

    .coupon-list {
        height: auto;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-top: 10px;
    }

    .coupon-item {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 0 12px 0 0;
        width: 335px;
        height: 72px;
        margin-top: 8px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-image: url("https://qimgs.qunarzz.com/wpf_newmpic_001/d1f3ef4e1556d8e34feb1466a73c173f.png");
        overflow: hidden;
    }

    .amount-container {
        flex-shrink: 0;
        margin: 0 16px 0 0;
        width: 88px;
        height: 100%;
        position: relative;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
    }

    .amount-content {
        display: flex;
        color: #FF6600;
        flex-direction: row;
        align-items: baseline;
    }

    .amount-num {
        line-height: 34px;
        text-align: left;
        font-family: hotel_rn_num;
        font-weight: 400;
        font-size: 28px;
        letter-spacing: 0;
    }

    .amount-unit,
    .amount-discount {
        text-align: left;
        font-family: PingFangSC-Regular;
        font-weight: 500;
        font-size: 16px;
        letter-spacing: 0;
    }

    .amount-unit {
        font-size: 20px;
        margin-right: -3px;
    }

    .coupon-label {
        line-height: normal;
        text-align: center;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #ff6600;
        font-size: 12px;
        letter-spacing: 0;
    }

    .coupon-details {
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        display: flex;
    }

    .coupon-description {
        max-width: 150px;
        text-align: left;
        font-family: PingFangSC-Regular;
        font-weight: 500;
        color: #ff6600;
        font-size: 14px;
        letter-spacing: 0;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .expiration-date {
        max-width: 150px;
        text-align: left;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #ff6600;
        font-size: 11px;
        letter-spacing: 0;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .use-coupon-button {
        flex-shrink: 0;
        width: 68px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 16px;
        background: linear-gradient(-90deg, #ff4505, #ff8f5a);
    }

    .button-text {
        display: flex;
        align-items: center;
        font-family: PingFangSC-Regular;
        font-weight: 500;
        color: #ffffff;
        font-size: 14px;

    }

    .coupon-label {
        line-height: normal;
        text-align: center;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        color: #ff6600;
        font-size: 12px;
        letter-spacing: 0;
    }

    .coupon-image {
        align-self: flex-end;
        width: 1px;
        height: 45px;
    }

}