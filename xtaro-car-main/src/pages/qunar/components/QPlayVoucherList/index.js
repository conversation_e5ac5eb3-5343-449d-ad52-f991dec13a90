import { View, Image, Text } from "@tarojs/components";
import { navigateTo } from "@tarojs/taro";
import React from "react";
import LogQmark from "@/common/utils/logQmark";
import config from "@/common/utils/config/config.js";
import utils from "@/common/utils/util";
import { logQmarkTTI, apiQmark } from "@/common/utils/logQmark";
import "./index.scss";

// 记录请求失败和耗时埋点
const sendRequest = apiQmark("qplay", "nnc_module_qunar_platform");
class QPlayVoucherList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            businessTypeMap: {
                hotel: "酒店劵",
                bus: "汽车劵",
                train: "门票劵",
                ticket: "门票劵",
                flight: "机票劵",
                bnb: "民宿劵",
                mall: "商城劵",
                vacation: "度假劵",
                vacationRedEnvelop: "度假红包",
            },
        };
    }

    toUse = (item) => {
        LogQmark({
            module: "default",
            id: "toUserVoucher",
            page: "qplay",
            operType: "click",
            ext: {
                couponId: item.couponId,
                useUrl: item.useUrl,
            },
        });
        if (!item.useUrl) return null;
        navigateTo({
            url: item.useUrl,
        });
    };

    goToVoucherCenter = () => {
        LogQmark({
            module: "default",
            id: "jumpCouponCenter",
            page: "qplay",
            operType: "click",
        });
        navigateTo({
            url: "/pages/qunar/subPages/coupon/list/index",
        });
    };

    render() {
        return (
            <View className="coupon-container">
                {this.props.couponList.length > 0 ? (
                    <View className="coupon-section">
                        <View className="coupon-header-content">
                            <Image
                                className="highlight-image"
                                src="https://qimgs.qunarzz.com/wpf_newmpic_001/612a59f1ee298cf3b184d788da3d9620.png"
                            ></Image>
                            <View
                                className="div-all-link"
                                onClick={() => this.goToVoucherCenter()}
                            >
                                <Text>查看全部</Text>
                                <Text className="g-q-iconfont div-all-link-icon">
                                    
                                </Text>
                            </View>
                        </View>
                        <View className="coupon-list">
                            {this.props.couponList.map((item, index) => {
                                return (
                                    <View
                                        className="coupon-item"
                                        key={item.couponId}
                                    >
                                        <View className="amount-container">
                                            <View className="amount-content">
                                                {item.couponValue.type === 0 ? (
                                                    <Text className="amount-unit">
                                                        ￥
                                                    </Text>
                                                ) : (
                                                    <Text></Text>
                                                )}
                                                <Text className="amount-num">
                                                    {item.couponValue.number}
                                                </Text>
                                                {item.couponValue.type === 1 ? (
                                                    <Text className="amount-discount">
                                                        折
                                                    </Text>
                                                ) : (
                                                    <Text></Text>
                                                )}
                                            </View>
                                            <Text className="coupon-label">
                                                {
                                                    this.state.businessTypeMap[
                                                        item.biz
                                                    ]
                                                }
                                            </Text>
                                        </View>
                                        <View className="coupon-details">
                                            <Text
                                                className="coupon-description"
                                                overflow="ellipsis"
                                            >
                                                {item.couponName}
                                            </Text>
                                            <Text
                                                className="expiration-date"
                                                overflow="ellipsis"
                                            >{`有效期至：${item.endDate}`}</Text>
                                            <Text
                                                className="expiration-date"
                                                overflow="ellipsis"
                                            >
                                                {item.desc}
                                            </Text>
                                        </View>
                                        <View
                                            className="use-coupon-button"
                                            onClick={() => this.toUse(item)}
                                        >
                                            <Text className="button-text">
                                                去使用
                                            </Text>
                                        </View>
                                    </View>
                                );
                            })}
                        </View>
                    </View>
                ) : (
                    <View></View>
                )}
            </View>
        );
    }
}

export default QPlayVoucherList;
