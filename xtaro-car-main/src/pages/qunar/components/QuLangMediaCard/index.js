import { View, Video } from "@tarojs/components";
import Taro, { showModal, onNetworkStatusChange } from "@tarojs/taro";
import React from "react";
import "./index.scss";

import QuLangImageGrid from "@platformComponents/QuLangImageGrid/index.js";

let cellularNetTypeSwitch = false; //是否允许在蜂窝网络下播放视频

class QuLangMediaCard extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            showBigImg: false,
            videoMuted: false,
        };
    }
    handleNetworkStatusChange = ({ isConnected, networkType }) => {
        if (!cellularNetTypeSwitch && networkType != "wifi" && isConnected) {
            // if (!cellularNetTypeSwitch && networkType != 'wifi') {
            this.wx.$element("video").pause();
            showModal({
                // title: '关于',
                content: "当前为非WIFI网络将使用流量播放",
                confirmText: "继续播放",
                cancelText: "取消",
                showCancel: true,
                success: ({ confirm }) => {
                    if (confirm) {
                        cellularNetTypeSwitch = true;
                        this.wx.$element("video").start();
                    }
                },
            });
        }
    };
    onScreenChange = (e) => {
        const { fullScreen } = e;
        if (!fullScreen) {
            this.props.onFullScreenExit();
        }
    };

    onChangeMuted = () => {
        this.setState({
            videoMuted: !this.state.videoMuted,
        });
    };
    onStart = () => {
        onNetworkStatusChange(this.handleNetworkStatusChange);
    };
    handlePause = () => {
        // console.log('video handlePause');
    };
    handleFinish = () => {
        // console.log('video handleFinish');
    };
    render() {
        return (
            <View style="width: 100%">
                {this.props.mediaType ? (
                    <Video
                        id="video"
                        loop={true}
                        muted={this.state.videoMuted}
                        onFullScreenChange={this.onScreenChange}
                        style="width:100%; height: 300px"
                        src={this.props.data.mediaList[0].videoUrl}
                        poster={this.props.data.mediaList[0].src}
                        onClick={this.onChangeMuted}
                        onStart={this.onStart}
                        onPause={this.handlePause}
                        onFinish={this.handleFinish}
                        controls={true}
                    />
                ) : this.props.data.mediaList ? (
                    <QuLangImageGrid
                        data={this.props.data}
                        length={this.props.data.mediaList.length || 0}
                        index={this.props.index}
                        onClickImageCard={this.props.onClickImageCard}
                    />
                ) : null}
            </View>
        );
    }
}

QuLangMediaCard.defaultProps = {
    mediaType: 0,
    data: {},
    index: 0,
    onClickImageCard: function () {},
};
export default QuLangMediaCard;
