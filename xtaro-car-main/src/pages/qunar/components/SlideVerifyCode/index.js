import { View, Text } from "@tarojs/components";
import { request, setStorageSync } from "@tarojs/taro";
import React from "react";
import "./index.scss";
import commonUtil from "@/common/utils/util";
import user from "@/common/utils/user";
import captcha from "@/common/utils/captcha";
import throttle from "@/common/utils/throttle";
import CryptoJS from "@/common/anticraw/utils/crypto";

const isBeta = process.env.BUILD_ENV === "beta";
const captchaSnapshot = "https://antifp.qunar.com/captcha/snapshot";
const Tips = {
    info: "按住滑块,拖到最右边",
    retry: "验证失败,请重试",
    success: "验证成功",
};
const Api = commonUtil.getNameSpace();

// eslint-disable-next-line no-console
const xlog = (...args) =>
    isBeta && console.info.apply(console, ["[SLIDER]", ...args]);

/**
 * 滑块验证
 * snapshot接口不通过的话会一直重试
 */
class SlideVerifyCode extends React.Component {
    static defaultProps = {
        appCode: "wx",
        delayClose: 0,
    };

    constructor(props) {
        super(props);

        this.state = {
            // 滑块滑动距离
            left: 0,
            tip: Tips.info,
            show: false,
        };

        /**
         * 滑动结束
         */
        this.isFinish = false;
        this.startPageX = 0;
        /**
         * 发送给后端的数据需要加密
         */
        this.sliderInfo = {
            // 滑动轨迹
            track: [],
            // 罗盘数据
            compass: [],
            // 设备方向
            deviceMotion: [],
            // 陀螺仪
            gyroscope: [],
        };
        this.tmp = {
            // 罗盘数据
            compass: [],
            // 设备方向
            deviceMotion: [],
            // 陀螺仪
            gyroscope: [],
        };
        this.sliderWidth = 264;
        this.sliderBlockWidth = 40;
    }

    // 开始滑动
    handleTouchStart = (event) => {
        this.isFinish = false;
        // 开始滑动时间
        this.sliderInfo.startTime = Date.now();
        const startPageX = this.getEventPageX(event);
        this.startPageX = startPageX;

        // 滑动开始时开始记录 陀螺仪,罗盘,设备方向
        Api.startCompass();
        if (process.env.ANU_ENV == "ali") {
            this.onDeviceMotionChange();
        } else {
            Api.startDeviceMotionListening({ interval: "game" });
        }
        Api.startGyroscope({ interval: "game" });
    };

    /**
     * 滑动过程, 节流处理
     * 后端需要每 20ms 拿取数据
     */
    handleTouchMove = throttle((event) => {
        const _this = this;
        let moveDistance = this.getEventPageX(event) - this.startPageX;
        // 滑块最大可滑动距离
        const maxMoveDistance = this.sliderWidth - this.sliderBlockWidth;
        // 左侧临界点
        if (moveDistance < 0) moveDistance = 0;
        // 右侧临界点, 表示滑到了最右侧, 应该结束了
        if (moveDistance > maxMoveDistance) {
            moveDistance = maxMoveDistance;
        }

        // 滑动没结束的时候才更新滑块位置
        if (!this.isFinish) {
            xlog("handleTouchMove >>>>> this.isFinish", this.isFinish);
            this.collectInfos(event, moveDistance);

            this.setState({ left: moveDistance }, () => {
                if (moveDistance === maxMoveDistance) {
                    _this.isFinish = true;
                    _this.sliderFinished();
                }
            });
        }
    }, 20);

    // 收集活动过程中滑块的状态
    collectInfos = (e, moved) => {
        const info = (e.changedTouches || e.touches || [e])[0];
        const c = this.tmp.compass.pop();
        if (c) this.sliderInfo.compass.push(c);

        const d = this.tmp.deviceMotion.pop();
        if (d) this.sliderInfo.deviceMotion.push(d);

        const g = this.tmp.gyroscope.pop();
        if (g) this.sliderInfo.gyroscope.push(g);

        this.resetTmp();

        // 时间戳,取后5位
        const u = Date.now() % 100000;
        const x = info.clientX.toFixed(2);
        const y = info.clientY.toFixed(2);
        const m = moved.toFixed(2);
        // 单条滑动轨迹
        const singleTrack = `${u};${x};${y};${m}`;
        xlog("singleTrack:::", singleTrack);
        this.sliderInfo.track.push(singleTrack);
    };

    // 手指离开屏幕
    handleTouchEnd = (event) => {
        // 滑块最大可滑动距离
        let moveDistance = this.getEventPageX(event) - this.startPageX;
        // 滑块最大可滑动距离
        const maxMoveDistance = this.sliderWidth - this.sliderBlockWidth;
        if (moveDistance < maxMoveDistance) {
            this.isFinish = true;
            this.setState({ left: 0 });
            this.reset();
        }
        xlog("handleTouchEnd >>>>> this.isFinish", this.isFinish);
    };

    sliderFinished = () => {
        this.sliderInfo.endTime = Date.now();
        user.getAuth().then((code) => {
            this.sliderInfo.userCode = code;
            xlog("发送验证请求之前打印 data:", JSON.stringify(this.sliderInfo));
            this.verify();
        });
    };

    // 加密滑动数据
    encryption = () => {
        const key = "227V2xYeHTARSh1R";
        const sliderStr = JSON.stringify(this.sliderInfo);
        const encrypted = CryptoJS.AES.encrypt(
            CryptoJS.enc.Utf8.parse(sliderStr),
            CryptoJS.enc.Utf8.parse(key),
            { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 },
        );
        return encrypted.toString();
    };

    /**
     * 去后端校验滑动轨迹数据
     */
    verify = () => {
        const { onFinished, appCode, delayClose } = this.props;
        const data = this.encryption();
        request({
            url: captchaSnapshot,
            method: "POST",
            data: {
                data,
                // orca: 后端给的,就是2
                orca: 2,
                appCode,
                cs: process.env.ANU_ENV,
            },
        }).then((r) => {
            this.reset();
            if (r.data?.data?.vcd) {
                this.setResCookie(r.data.data.vcd);
            }
            if (r.data?.data?.code === 0) {
                const cst = r.data.data.cst;
                onFinished && onFinished({ valid: true, cst });

                this.check(cst);
                this.setState({ tip: Tips.success });
                // 延时关闭,可以先看到成功提示
                if (delayClose) {
                    setTimeout(this.hide, 360);
                } else {
                    this.hide();
                }
            } else {
                this.retry();
            }
        });
    };

    /**
     * 植入 cookies
     */
    setResCookie = (obj) => {
        const info = commonUtil.getGlobalInfo();
        let hasCookie = false;
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                hasCookie = true;
                let v = obj[key];

                if (typeof v === "object") {
                    v = v.value;
                }

                info.cookies[key] = v;
            }
        }
        if (hasCookie) {
            try {
                setStorageSync("UserData", info);
            } catch (err) {
                /**
                 * 目前(2021-02-22)没有手动上报错误的api
                 * Promise.reject 会被自动上报捕捉
                 */
                err.message =
                    "util.js setResCookies catch error >>> " + err.message;
                // eslint-disable-next-line promise/catch-or-return
                Promise.reject(err);
            }
        }
    };

    // 重试
    retry = () => {
        this.setState({ tip: Tips.retry, left: 0 });
        this.reset();
    };

    // 释放内存,防止数据过多
    resetTmp = () => {
        this.tmp.compass = [];
        this.tmp.deviceMotion = [];
        this.tmp.gyroscope = [];
    };

    show = () => {
        // 滑块弹出时间
        this.sliderInfo.openTime = Date.now();
        this.setState({ show: true, tip: Tips.info });
        xlog("我展示出来了");

        this.getAnuEnv();
    };

    hide = () => {
        this.setState({ show: false });
    };

    reset = () => {
        this.sliderInfo.track = [];
        this.sliderInfo.compass = [];
        this.sliderInfo.deviceMotion = [];
        this.sliderInfo.gyroscope = [];

        this.resetTmp();

        Api.stopCompass();
        if (process.env.ANU_ENV === "ali") {
            Api.offDeviceMotionChange();
        } else {
            Api.stopDeviceMotionListening();
        }
        Api.stopGyroscope();
    };

    getEventPageX = (e) => {
        const pageX = (e.changedTouches || e.touches || [e])[0].pageX;
        return pageX;
    };

    componentDidMount() {
        const userData = commonUtil.getGlobalInfo();
        const openId = userData.user.openId;
        if (openId) this.sliderInfo.openId = openId;

        const v = Api.getSystemInfoSync().SDKVersion;
        xlog("SDKVersion", v);
    }

    pxToRpx = (num) => {
        return (num * 750) / Api.getSystemInfoSync().windowWidth + "rpx";
    };

    componentWillUnmount() {
        this.reset();
    }

    getAnuEnv = () => {
        Api.onCompassChange((res) => {
            if (!this.isFinish) this.tmp.compass.push(res);
        });
        Api.onGyroscopeChange((res) => {
            if (!this.isFinish) this.tmp.gyroscope.push(res);
        });

        this.onDeviceMotionChange();
    };

    onDeviceMotionChange = () => {
        Api.onDeviceMotionChange((res) => {
            if (!this.isFinish) this.tmp.deviceMotion.push(res);
        });
    };

    check = (cst) => {
        const { check, appCode } = this.props;
        if (!(check && typeof check === "function")) return;

        captcha.check(cst, appCode, check);
    };

    render() {
        return (
            <View
                class="verify-wrap"
                style={{ display: this.state.show ? "flex" : "none" }}
                catchTouchMove={() => {}}
            >
                <View class="verify-container show-popup">
                    <View class="header">
                        <Text class="title">安全验证</Text>
                    </View>
                    <View class="slider-bg">
                        <View class="slider-tip">{this.state.tip}</View>
                        <View
                            class="slider-block"
                            onTouchStart={this.handleTouchStart}
                            catchTouchMove={this.handleTouchMove}
                            onTouchEnd={this.handleTouchEnd}
                            style={{ left: this.pxToRpx(this.state.left) }}
                        >
                            <Text className="g-q-iconfont mob-shuangjiantouxiangxiae043-f block-icon">
                                \uE043
                            </Text>
                        </View>
                    </View>
                </View>
            </View>
        );
    }
}

export default SlideVerifyCode;
