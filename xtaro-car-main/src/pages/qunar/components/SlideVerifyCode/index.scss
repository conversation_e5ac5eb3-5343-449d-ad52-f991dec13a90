@import '../../style/g-define.scss';
@import '../../style/iconfont.scss';
.verify-wrap {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 199;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: none;
    justify-content: center;

    .verify-container {
        display: -webkit-flex;
        display: flex;
        -webkit-flex-direction: column;
        flex-direction: column;
        visibility: hidden;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 8px;
    }

    .show-popup {
        position: fixed;
        transition: all 0.4s ease-in-out;
        // transform: translateY(-50%);
        margin-top: 50%;
        // height: 256rpx;
        visibility: visible;
        padding: 16px;
    }

    .popup-off {
        transition: all 0.4s ease-in-out;
        display: none;
    }

    /* header styles */
    .verify-container .header {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .verify-container .title {
        margin-bottom: 16px;
        color: #212121;
        font-size: 20px;
    }

    /* content styles */
    .verify-container .slider-bg {
        position: relative;
        margin: 0 auto;
        height: 40px;
        width: 264px;
        background-color: #e8e8e8;
        border-radius: 3px;
    }

    .verify-container .slider-block {
        position: absolute;
        top: 0;
        left: 0;
        height: 38px;
        width: 40px;
        border: 1px solid gray;
        background-color: white;
        border-radius: 3px;
        line-height: 36px;
        text-align: center;
        transform: rotate(-90deg);

        .block-icon {
            font-size: 16px;
        }
    }

    .verify-container .slider-tip {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #808080;
    }

    .verify-container .show {
        visibility: visible;
    }

    .verify-container .hide {
        visibility: hidden;
    }
}
