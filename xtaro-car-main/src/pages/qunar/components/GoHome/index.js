import { View, Text } from "@tarojs/components";
import { getCurrentPages, switchTab } from "@tarojs/taro";
import React from "react";
import "./index.scss";
class GoHome extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            env: process.env.ANU_ENV,
            isShow: true,
        };
    }

    componentDidMount() {
        if (process.env.ANU_ENV !== "qq") return;
        let isShow = true;
        try {
            var pages = getCurrentPages();
            for (var i = 0; i < pages.length; i++) {
                if (pages[i].route == "pages/platform/index/index") {
                    isShow = false;
                    break;
                }
            }
        } catch (err) {
            // eslint-disable-next-line
        }
        this.setState({
            isShow,
        });
    }

    goHome = () => {
        switchTab({
            url: "/pages/platform/index/index",
        });
    };

    render() {
        return (
            <View class="fixed-style" style={{ top: this.props.top }}>
                {this.state.env === "qq" && this.state.isShow && (
                    <View
                        className=" rewrite-btn share-btn center"
                        onClick={this.goHome}
                        style={{ backgroundColor: this.props.backgroundColor }}
                    >
                        <Text className="g-q-iconfont share"></Text>
                    </View>
                )}
            </View>
        );
    }
}

GoHome.defaultProps = {
    backgroundColor: "#000", // 默认背景颜色
    top: "0px",
};
export default GoHome;
