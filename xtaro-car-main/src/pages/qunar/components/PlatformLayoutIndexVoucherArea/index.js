import { View, Image, Text } from "@tarojs/components";
import { navigateTo } from "@tarojs/taro";
import React from "react";
import newWatcher from "@/common/utils/watcher";
import utils from "@/common/utils/util";
import VoucherModal from "./voucherModal.js";
import login from "@/common/utils/login";
import Log from "@/common/utils/log";
import "./index.scss";

const sendRequest = newWatcher.apiWatcher("home");

class PlatformLayoutIndexVoucherArea extends React.Component {
    constructor(data) {
        super(data);
        this.state = {};
    }

    sendLog = (params) => {
        let { button_id, button_name } = params;
        let storage = utils.getGlobalInfo() || {};
        let { _q } = storage.cookies || {};
        let { openId } = storage.user || {};
        Log({
            action_type: "click",
            action_time: new Date(),
            button_id,
            button_name,
            cookie: openId,
            openid: openId,
            user_name: _q,
        });
    };

    initPage = async () => {
        this.defaultQuan = await this.getQuanPackagesFromQconfig();
        this.requestCoupon();
    };

    componentDidMount() {
        this.initPage();
    }

    // 查询是否可发券 + 已领券
    requestCoupon = async () => {
        // 查询
        this.systemInfo = await utils.getSystemInfo();
        const { model } = this.systemInfo;
        sendRequest({
            service: "/gw/f/hermes/detail/gift/list",
            host: "https://m.flight.qunar.com",
            ignoreStatus: true,
            method: "POST",
            param: {
                model,
            },
            success: (res) => {
                const { couponList, grantStatus = {} } = res;
                const { status, data } = grantStatus;
                const { status: couponListStatus, data: couponListData } =
                    couponList;
                if (couponListStatus === 10013) {
                    // 未登录，展示兜底
                    this.noLogin = true;
                    this.setState({
                        quanList: this.dealDataList(
                            this.defaultQuan.couponList,
                        ),
                        hasAcquired: false,
                        dataDone: true,
                    });
                } else if (status === 0) {
                    const { giftId, canGrant, coupons } = data;
                    const { success: receivedCoupons } = couponListData || {};
                    if (canGrant) {
                        // 可以发券，取coupons
                        if (coupons.length === 0) {
                            // 可以发券，但是可发券为空，隐藏
                            this.setState({
                                hideArea: true,
                            });
                        } else {
                            // 可以发券，展示可以领的券
                            this.setState({
                                quanList: this.dealDataList(coupons),
                                hasAcquired: false,
                                dataDone: true,
                                giftId,
                            });
                        }
                    } else {
                        // 不可发券，取couponList里的success展示
                        if (
                            couponListStatus === 0 &&
                            receivedCoupons?.length > 0
                        ) {
                            // 接口正常 + 有已领券
                            this.setState({
                                quanList: this.dealDataList(receivedCoupons),
                                hasAcquired: true,
                                dataDone: true,
                            });
                        } else {
                            // 接口异常 或 没有已领券
                            this.setState({
                                hideArea: true,
                            });
                        }
                    }
                } else {
                    // 兜底
                    this.setState({
                        quanList: this.dealDataList(
                            this.defaultQuan.couponList,
                        ),
                        hasAcquired: false,
                        dataDone: true,
                    });
                }
            },
            fail: () => {
                wx.showToast({
                    title: "网络异常，请稍后再试",
                    icon: "none",
                });
            },
        });
    };

    // 获取兜底礼包（qconfig）
    getQuanPackagesFromQconfig = () => {
        return new Promise((resolve) => {
            sendRequest({
                service: "/mpx/getQconfig",
                resCheckField: "data",
                param: {
                    name: "applet_marketgift.json",
                },
                success: (res) => {
                    if (this.props.isNewer === "new") {
                        resolve(res.data["MarketGift-show-new"]);
                    } else {
                        resolve(res.data["MarketGift-show-old"]);
                    }
                },
            });
        });
    };

    // 领券
    requestAcquireVoucher = () => {
        this.sendLog({
            button_id: "1-1-1",
            button_name: "一键领取",
        });
        const { giftId } = this.state;
        const { model, platform } = this.systemInfo;
        sendRequest({
            service: "/gw/m/hermes/client/grant",
            host: "https://m.flight.qunar.com",
            method: "POST",
            ignoreStatus: true,
            header: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            param: {
                giftId,
                channel: "WxMiniProgram",
                innerGrant: "true",
                model,
                platform,
            },
            success: (res) => {
                const { status, data = {} } = res;
                if (status === 0 && data.success && data.success.length > 0) {
                    this.setState({
                        showModal: true,
                        modalQuanList: data.success,
                        quanList: this.dealDataList(data.success),
                        hasAcquired: true,
                    });
                } else {
                    wx.showToast({
                        title: "网络异常，请稍后再试",
                        icon: "none",
                    });
                }
            },
            fail: () => {
                wx.showToast({
                    title: "网络异常，请稍后再试",
                    icon: "none",
                });
            },
        });
    };

    // 关闭弹窗
    closeModal = () => {
        this.sendLog({
            button_name: "随便逛逛",
            button_id: "1-2-1",
        });
        this.setState({
            showModal: false,
        });
    };

    // 点击领券按钮
    clickCard = () => {
        if (this.noLogin) {
            // 去登录
            login(() => {
                wx.switchTab({
                    url: "/pages/platform/indexWx/index",
                });
                this.noLogin = false;
                this.requestCoupon();
            });
        } else {
            this.requestAcquireVoucher();
        }
    };

    // 去使用券
    goUseQuan = (url, id) => {
        this.sendLog({
            button_id: id,
            button_name: "去使用",
        });

        this.setState({
            showModal: false,
        });

        navigateTo({ url });
    };

    // 处理列表数据
    dealDataList = (data) => {
        let dataWithTip = this.handleBizType(data);
        const DEFAULT_SHOW_QUAN_NUMBER = 4;
        if (!dataWithTip || dataWithTip.length === 0) {
            return [];
        } else if (dataWithTip.length >= DEFAULT_SHOW_QUAN_NUMBER) {
            return dataWithTip;
        } else {
            let newData = [];
            for (let i = 0; i < DEFAULT_SHOW_QUAN_NUMBER; i++) {
                newData[i] = dataWithTip[i] || undefined;
            }
            return newData;
        }
    };

    handleBizType = (data = []) => {
        const { acquireQuan } = this.props;
        const { quanBizMap } = acquireQuan;
        data.forEach((item) => {
            item.bizTip =
                quanBizMap[item.biz.toLowerCase()] || quanBizMap["default"];
        });
        return data;
    };

    render() {
        if (this.state.hideArea || !this.state.dataDone) {
            return <View></View>;
        }

        return (
            <View
                class="m-quan-area"
                style={{
                    backgroundImage:
                        this.props.isNewer === "new"
                            ? "url(https://picbed.qunarzz.com/24df8fe5ce2fe7e4869d4c633f4056a8.png)"
                            : "url(https://picbed.qunarzz.com/3625d4904561ea726103f0648f3e076b.png)",
                }}
            >
                <View class="m-quan-area-container">
                    {this.state.hasAcquired ? (
                        <View class="m-quan-area-text">以下福利已放入背包</View>
                    ) : (
                        <Image
                            mode="widthFix"
                            class="m-quan-area-image"
                            src="https://picbed.qunarzz.com/f0b9e7ab7e0e276789f67e57fead75fb.png"
                            onClick={() => this.clickCard()}
                        />
                    )}

                    <View class="m-quan-area-list">
                        {this.state.quanList &&
                            this.state.quanList.map((quan) =>
                                quan ? (
                                    <View class="m-quan-area-item">
                                        <View class="m-quan-area-tip">
                                            {quan.bizTip}
                                        </View>
                                        {quan.couponValue.type === 0 ? (
                                            <View class="m-quan-area-price">
                                                <Text class="m-quan-area-price-icon">
                                                    ￥
                                                </Text>
                                                <Text class="m-quan-area-price-num">
                                                    {quan.couponValue.number}
                                                </Text>
                                            </View>
                                        ) : (
                                            <View class="m-quan-area-price">
                                                <Text class="m-quan-area-price-num">
                                                    {quan.couponValue.number}
                                                </Text>
                                                <Text class="m-quan-area-price-icon">
                                                    折
                                                </Text>
                                            </View>
                                        )}

                                        <View class="m-quan-area-name">
                                            {quan.couponName}
                                        </View>
                                        {this.state.hasAcquired && (
                                            <View
                                                class="m-quan-area-btn"
                                                onClick={() =>
                                                    this.goUseQuan(
                                                        quan.bizProperty
                                                            .wxMiniUrl,
                                                        "1-1-2",
                                                    )
                                                }
                                            >
                                                去使用
                                            </View>
                                        )}
                                    </View>
                                ) : (
                                    <View class="m-quan-area-item m-quan-area-itemimg" />
                                ),
                            )}
                    </View>
                </View>
                {this.state.showModal && (
                    <VoucherModal
                        data={this.state.modalQuanList}
                        closeModal={() => this.closeModal()}
                        goUseQuan={(url) => this.goUseQuan(url, "1-2-2")}
                    />
                )}
            </View>
        );
    }
}
export default PlatformLayoutIndexVoucherArea;
