@font-face {
    font-family: 'g-q-iconfont';
    // src: url('/assets/css/minprogram_nanachi.ttf')
    // src: url("https://ss.qunarzz.com/yo/font/1.0.3/yofont.ttf");
    src: url('https://s.qunarzz.com/nanachi/score/font/0.0.22/minprogram_nanachi.ttf');
}

.g-q-iconfont {
    font-family: 'g-q-iconfont';
    font-family: 'g-q-iconfont' !important;
    font-style: normal;
}
.startPosition, .endPosition{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

button {
    box-sizing: content-box;
    background: #FFFFFF;
    border: none;
    outline: none;
    background: none;
    &:after {
        border: none;
    }
}

button:active {
    background: #FFFFFF;
}

.vacation-hotdestination {
    &.content {
        background-color: #FFFFFF;
        margin: 12px 8px;
        width: auto;
        border-radius: 12px;
        padding: 12px;
    }
    
    .header {
        display: flex;
        align-items: center;
        position: relative;
        font-size: 16px;
        margin-top: 4px;
    }
    
    .tab-item {
        font-size: 15px;
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
    }
    
    .tab-item-title {
        position: relative;
        white-space: nowrap;
    }
    
    .active-tab-item-bg {
        display: inline-block;
        position: absolute;
        width: 100%;
        height: 10px;
        border-radius: 6px;
        background-image: linear-gradient(90deg, rgba(7,232,246,0.00) 9%, #03CDE9 50%);
        bottom: 0;
        right: -2px;
        z-index: -2;
    }
    
    .tab-item-tag-view {
        min-width: 27px;
        font-size: 10px;
        margin-right: 5px;
        font-weight: 400;
        display: flex;
        height: 100%;
    }
    
    .tab-item-tag {
        position: relative;
        left: -1px;
        transform: translateY(-10.5px);
        height: 14px;
        line-height: 14px;
        background-image: linear-gradient(270deg, #FF7171 0%, #FF5745 100%);
        border-radius: 20.5px 20.5px 20.5px 0px;
        font-size: 10px;
        color: #FFFFFF;
        padding: 0 3px;
        z-index: 4;
    }
    
    .active-tab-item {
        font-size: 18px;
        color: #111111;
        font-weight: 600;
    }
    
    
    .main {
        margin-top: 14px;
        &-img-list {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-direction: row;
            flex-wrap: wrap;
        }
        .item-tag {
            display: inline-block;
            height: 14px;
            line-height: 13px;
            background-image: linear-gradient(270deg, #FF7171 0%, #FF5745 100%);
            border-radius: 8px 0px 8px 0px;
            font-size: 10px;
            color: #FFFFFF;
            padding: 0 4px;
        }
        &-img-item {
            width: 107px;
            height: 106px;
            border-radius: 8px;
            margin-bottom: 8px;
            background-position: center;
            background-size: cover;
            overflow: hidden;
            .item-con {
                position: relative;
                font-size: 0;
                height: 100%;
            }
            .img-item-info {
                position: absolute;
                height: 50%;
                width: 100%;
                bottom: 0;
                left: 0;
                background-image: linear-gradient(180deg, rgba(0,0,0,0.00) 0%, rgba(0,0,0,0.4) 100%);
                padding-left: 12px;
                padding-bottom: 8px;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: flex-end;
                box-sizing: border-box;
            }
            .item-name {
                height: 20px;
                font-size: 14px;
                color: #FFFFFF;
                font-weight: 500;
                display: inline-block;
                white-space: nowrap;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .item-people {
                height: 14px;
                line-height: 14px;
                font-size: 10px;
                color: #FFFFFF;
            }
        }
        &-text-item {
            width: 78px;
            height: 42px;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 8px;
            padding: 0 6px;
            box-sizing: border-box;
            .item-tag {
                position: absolute;
                top: 0;
                left: 0;
            }
            .item-name {
                height: 42px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: #222222;
                text-align: center;
                font-weight: 500;
                &-text {
                    display: -webkit-box;
                    overflow: hidden; 
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }
            }
            &-0 {
                background-color: #EBF2EB;
            }
            &-1 {
                background-color: #E9EEF4;
            }
            &-2 {
                background-color: rgba(237,236,240,0.7);
            }
            &-3 {
                background-color: #F7EBEB;
            }
            &-4 {
                background-color: rgba(237,236,240,0.7);
            }
            &-5 {
                background-color: #F4F1E8;
            }
            &-6 {
                background-color: #E9EEF4;
            }
            &-7 {
                background-color: rgba(237,236,240,0.7);
            }
        }
        &-bottom {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            text-align: center;
            height: 42px;
            line-height: 42px;
            background: #EFF6F7;
            border-radius: 8px;
            font-size: 12px;
            color: #222222;
            font-weight: 500;
            &-icon {
                font-size: 15px;
                margin-left: 2px;
            }
        }
    }
    
    
    &.new-content {
        position: relative;
        z-index: 5;
        margin-left: 12px;
        margin-right: 12px;
        margin-top: 0;
        .header {
            align-items: baseline;
        }
        .active-tab-item {
            line-height: 25px;
            // font-size: 30rpx;
        }
    }
}

