import { View } from "@tarojs/components";
import React from "react";

import initFormIdConfig from "@/common/utils/formId";
import "./index.scss";

// import flightSearchBox from '@common/components/flightSearchBox/index';
// import hotelSearchBox from '@common/components/hotelSearchBox/index';
// import trainSearchBox from '@common/components/trainSearchBox/index';
// import ticketSearchBox from '@common/components/ticketSearchBox/index';
import HotelSearchBox from "@platformComponents/HotelSearchBox/index";
// import FlightSearchBox from '@platformComponents/FlightSearchBox/index';

class SearchBox extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            env: process.env.ANU_ENV,
            showToast: false,
            toastImg: "",
        };
    }

    render() {
        return (
            <View class="mainSearchBox">
                {/* {
             this.props.business === 'flight' && <div>
                 <FlightSearchBox />
             </div>
          } */}
                {this.props.business === "hotel" && <HotelSearchBox />}

                {this.props.business === "train" && <View>火车组件</View>}

                {this.props.business === "ticket" && <View>门票组件</View>}

                {/* {
             this.props.business === 'flight' && <flightSearchBox />
          }
          {
             this.props.business === 'hotel' && <hotelSearchBox />
          }
          {
             this.props.business === 'train' && <trainSearchBox />
          }
          {
             this.props.business === 'ticket' && <ticketSearchBox />
          } */}
            </View>
        );
    }
}

export default SearchBox;
