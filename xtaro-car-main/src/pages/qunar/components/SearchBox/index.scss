@import '../../style/iconfont.scss';
@import '../../style/g-define.scss';

.mainSearchBox {
    background-color: #fff;
}

.plat-toast {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 99;
    animation: opacityIncrease 500ms;

    .center {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-content: center;
    }

    .toast-wrap {
        width: 285px;
        height: 461px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .toast-img {
        width: 285px;
        height: 383px;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 38px;

        .img {
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }
    }

    .g-q-iconfont {
        font-size: 40px;
        color: #FFFFFF;
        text-align: center;
    }

    .icon::before {
        content: '\f3f4';
    }
}

.float {
    position: fixed;
    right: 0px;
    bottom: 70px;
    width: 77.5px;
    height: 90px;
    z-index: 9;

    .float-img {
        width: 100%;
        height: 100%;
    }
}

.rewrite-btn {
    background: transparent;
}

.logo-shake {
    animation: logoShake 2.8s 200ms linear;
}

@keyframes logoShake {

    10%,
    14%,
    18%,
    22%,
    26%,
    80%,
    84%,
    88%,
    92%,
    96% {
        transform: rotate(-8deg);
    }

    12%,
    16%,
    20%,
    24%,
    28%,
    82%,
    86%,
    90%,
    94%,
    98% {
        transform: rotate(8deg);
    }

    0%,
    9%,
    29%,
    79%,
    100% {
        transform: rotate(0deg);
    }
}

@keyframes opacityIncrease {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.tab{
    width: 359px;
    height: 313px;
    font-size: 12px;
}
.hotel-search{
    width: 359px;
    height: 313px;
    background-color: #f00;
}