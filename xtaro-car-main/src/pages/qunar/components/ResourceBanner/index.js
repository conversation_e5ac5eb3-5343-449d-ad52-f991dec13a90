import { View, Swiper, SwiperItem, Image } from "@tarojs/components";
import { navigateToMiniProgram, navigateTo } from "@tarojs/taro";
import React from "react";
import EventEmitter from "@/common/utils/EventEmitter";
import { EVENT_EMITTER_NAME, JUMP_TYPE } from "@/common/constants/index";
import "./index.scss";

class ResourceBanner extends React.Component {
    constructor(props) {
        super(props);
    }
    handleSwiperJump = (item = {}) => {
        this.props.handleSwiperJumpLog(item);

        if (this.props.jumpType === JUMP_TYPE.SEMI_FLOAT) {
            EventEmitter.dispatch(
                EVENT_EMITTER_NAME.CHANGE_NEW_CUSTOMER_SEMI_MODAL_STATE,
                {
                    status: true,
                    scene: "userCenter",
                    enterScene: "myPageBanner",
                },
            );
            return;
        }

        if (!item.jumpUrl) return null;
        if (item.copyWriting) {
            navigateToMiniProgram({
                appId: item.copyWriting,
                path: item.jumpUrl,
            });
        } else {
            navigateTo({ url: item.jumpUrl });
        }
    };
    render() {
        return (
            <View>
                {this.props.bannerList?.length > 0 && (
                    <View
                        class="banner-list"
                        style={{
                            width: this.props.width,
                            height: this.props.height,
                            margin: this.props.margin,
                        }}
                    >
                        {this.props.bannerList.length > 1 ? (
                            <Swiper
                                indicator-color={"rgba(255, 255, 255, .4)"}
                                indicator-active-color={"rgb(255, 255, 255)"}
                                indicator-dots={true}
                                autoplay={true}
                                circular={true}
                                interval={
                                    this.props.bannerList[0]
                                        ?.rotationInterval || 5000
                                }
                                duration={500}
                                class="swiper-banner"
                            >
                                {this.props.bannerList?.map((item) => (
                                    <SwiperItem>
                                        <View
                                            class="banner-item"
                                            key={item.sourceMaterialId}
                                            onClick={() =>
                                                this.handleSwiperJump(item)
                                            }
                                        >
                                            <Image
                                                class="banner-item-img"
                                                src={item.imageUrl}
                                            />
                                        </View>
                                    </SwiperItem>
                                ))}
                            </Swiper>
                        ) : (
                            <View
                                class="swiper-banner"
                                onClick={this.handleSwiperJump.bind(
                                    this,
                                    this.props.bannerList[0],
                                )}
                            >
                                <Image
                                    class="banner-item-img"
                                    src={this.props.bannerList[0]?.imageUrl}
                                />
                            </View>
                        )}
                    </View>
                )}
            </View>
        );
    }
}

export default ResourceBanner;
