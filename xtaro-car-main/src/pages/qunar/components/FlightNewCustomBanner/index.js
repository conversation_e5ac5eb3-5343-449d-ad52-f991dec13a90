import { View, Image, Text } from "@tarojs/components";
import React from "react";
import request from "@/common/utils/request.js";

import "./index.scss";

class FlightNewCustomBanner extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            resourceData: {},
        };
        this.init();
    }

    init = async () => {
        let resourceData = await this.getResourceDataData();
        this.setState({
            resourceData: resourceData?.data || {},
        });
    };

    // handleNeedResource() {
    //     return new Promise((resolve, reject) => {
    //         request({
    //             host: 'https://m.flight.qunar.com',
    //             service: '/flight/api/miniappHomePopup',
    //             method: 'POST',
    //             ignoreStatus: true,
    //             success: res => {
    //                 resolve(res);
    //             }
    //         });
    //     });
    // }
    getResourceDataData = () => {
        return new Promise((resolve, reject) => {
            request({
                host: "https://m.flight.qunar.com",
                service: "/flight/api/miniappHomeNewUserPrivilege",
                method: "POST",
                ignoreStatus: true,
                success: (res) => {
                    resolve(res);
                },
            });
        });
    };

    render() {
        return (
            <View>
                {this.state.resourceData.resourceUrl && (
                    <View className="new-user-right">
                        <Image
                            src={this.state.resourceData.resourceUrl}
                            mode="widthFix"
                        />
                        {this.state.resourceData.ext &&
                            this.state.resourceData.ext.price && (
                                <View className="coupon">
                                    <Text>
                                        {this.state.resourceData.ext.price}
                                    </Text>
                                    <Text className="unit">元</Text>
                                </View>
                            )}
                    </View>
                )}
            </View>
        );
    }
}

export default FlightNewCustomBanner;
