import { View, Text, ScrollView } from "@tarojs/components";
import Taro, { navigateTo, showToast } from "@tarojs/taro";
import React from "react";
import LogQmark from "@/common/utils/logQmark";
import { apiQmark } from "@/common/utils/logQmark";
import login from "@/common/utils/login";
import "./index.scss";
const sendRequest = apiQmark("qplay", "nnc_module_qunar_platform");
const jumpData = {
    ruleUrl:
        "/pages/platform/webView/index?url=https%3A%2F%2Fm.flight.qunar.com%2Fshark%2Factive%2Fe129074fd7db39d90a9eb672ffeff971",
    voucherUrl:
        "/pages/market/entry/marketing/index?cid=9859dcf25b6d241dac5ba6dcfe2cac82",
};
const payNormalParam = {
    moduleId: "639069",
    source: "activity",
    sourceFrom: "paidcouponpackage",
    buyCount: 1,
    qmpChannel: "qmpwxsapp",
};
// 付费券包
class QPlayPaidVoucherList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {};
    }
    btnClick = (item) => {
        const {
            buttonClickToast,
            jumpUrl,
            buttonType,
            mallPid,
            salePrice,
            payLeftTimeMills,
            orderNo,
            couponName,
            buttonText,
            jumpType,
            activityNo,
        } = item;
        LogQmark({
            module: "default",
            id: "btnClick",
            page: "qplay",
            operType: "click",
            ext: {
                buttonType,
                buttonText,
            },
        });
        switch (buttonType) {
            case "login":
                login(() => {}, { source: "qPlay" });
                break;
            case "jump":
                if (jumpType) {
                    Taro[jumpType]({
                        url: jumpUrl,
                    });
                } else {
                    navigateTo({
                        url: jumpUrl,
                    });
                }
                break;

            case "toast": {
                showToast({
                    icon: "none",
                    title: buttonClickToast,
                });
                break;
            }
            case "buy": {
                this.goPay({
                    pid: mallPid,
                    singlePrice: salePrice,
                    payPageTitle: couponName,
                    sourceFrom: activityNo,
                });
                break;
            }
            case "orderPlaced": {
                let time =
                    new Date().getTime() -
                    this.props.paidVoucherData.currentTime;
                if (time > payLeftTimeMills) {
                    this.goPay({
                        pid: mallPid,
                        singlePrice: salePrice,
                        payPageTitle: couponName,
                        sourceFrom: activityNo,
                    });
                } else {
                    this.handleGoCashier({
                        pid: mallPid,
                        singlePrice: salePrice,
                        orderNo,
                        payPageTitle: couponName,
                        sourceFrom: activityNo,
                    });
                }
                break;
            }
        }
    };
    handleGoCashier = (payParams) => {
        this.goCashierLocking = true;
        // https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=550340450
        const { orderNo, singlePrice } = payParams || {};
        let params = {
            orderNo,
            qmpChannel: "qmpwxsapp",
        };
        sendRequest({
            host: "https://m.flight.qunar.com",
            service: "/gw/f/flight/akihabara/order/touch/goPay",
            ignoreStatus: true,
            data: params,
            method: "POST",
            header: {
                "Content-Type": "application/json; charset=utf-8",
            },
            success: (res) => {
                const { status, data } = res || {};
                if (status === 0) {
                    const { fKey, hbToken, touchUrl } = data || {};
                    const cashierParams = {
                        fKey,
                        hbToken,
                        touchUrl,
                        price: singlePrice,
                        payBackUrl:
                            "/pages/platform/qPlay/index?source=payOrder",
                        orderNo,
                        payPageTitle: payParams.payPageTitle,
                    };
                    this.openCashier(cashierParams);
                } else if (status === 100) {
                    login(() => {}, { source: "qPlay" });
                    this.goCashierLocking = false;
                } else {
                    let toastMsg = this.calcPayErrorToast(status);
                    showToast({
                        icon: "none",
                        title: toastMsg,
                    });
                    this.goCashierLocking = false;
                }
                LogQmark({
                    module: "default",
                    id: "goPayStatus",
                    page: "qplay",
                    operType: "click",
                    ext: {
                        status,
                    },
                });
            },
            fail: () => {
                console.log(e);
                showToast({
                    icon: "none",
                    title: "活动太火爆了，请稍后再试",
                });
                this.goCashierLocking = false;
                LogQmark({
                    module: "default",
                    id: "goPayStatus",
                    page: "qplay",
                    operType: "click",
                    ext: {
                        status: "fail",
                    },
                });
            },
        });
    };
    goPay = (payParams) => {
        if (this.locking) {
            return;
        }
        this.locking = true;
        let params = {
            ...payNormalParam,
            ...payParams,
        };
        sendRequest({
            host: "https://m.flight.qunar.com",
            service: "/gw/f/flight/akihabara/order/touch/pay",
            ignoreStatus: true,
            data: params,
            method: "POST",
            header: {
                "Content-Type": "application/json; charset=utf-8",
            },
            success: (res) => {
                let { status, data } = res || {};
                if (status === 0) {
                    const {
                        fKey,
                        hbToken,
                        touchUrl,
                        orderNo,
                        freePay = false,
                    } = data || {};
                    const cashierParams = {
                        fKey,
                        hbToken,
                        touchUrl,
                        price: params.singlePrice,
                        payBackUrl:
                            "/pages/platform/qPlay/index?source=payOrder",
                        orderNo,
                        payPageTitle: payParams.payPageTitle,
                    };
                    freePay && (this.locking = false);
                    !freePay && this.openCashier(cashierParams);
                } else if (status === 100) {
                    login(() => {}, { source: "qPlay" });
                    this.locking = false;
                } else {
                    let toastMsg = this.calcPayErrorToast(status);
                    toastMsg &&
                        showToast({
                            icon: "none",
                            title: toastMsg,
                        });
                    this.locking = false;
                }
                LogQmark({
                    module: "default",
                    id: "payStatus",
                    page: "qplay",
                    operType: "click",
                    ext: {
                        status,
                    },
                });
            },
            fail: () => {
                showToast({
                    icon: "none",
                    title: "活动太火爆了，请稍后再试",
                });
                this.locking = false;
                LogQmark({
                    module: "default",
                    id: "payStatus",
                    page: "qplay",
                    operType: "click",
                    ext: {
                        status: "fail",
                    },
                });
            },
        });
    };
    openCashier = ({
        touchUrl,
        price,
        payBackUrl,
        webViewBaseUrl = "",
        payPageTitle,
    }) => {
        let paramArr = [
            `payUrl=${encodeURIComponent(touchUrl)}`,
            `ret=${encodeURIComponent(payBackUrl)}`,
            `backType=switchTab`,
            `price=${price}`,
            `title=${payPageTitle}`,
            "jumpType=redirect",
            `webViewBaseUrl=${encodeURIComponent(webViewBaseUrl)}`,
        ];

        let miniPayUrl =
            "/flight/pages/payOrder/payOrder?" + paramArr.join("&");
        navigateTo({
            url: miniPayUrl,
        });
        this.locking = false;
        this.goCashierLocking = false;
    };
    calcPayErrorToast = (status) => {
        let toastMsg;
        const toastText = {};
        const { overNumber = "您购买的券包数已达到上限，暂不支持购买" } =
            toastText || {};

        switch (status) {
            case -30008:
                toastMsg = overNumber;
                break;
            default:
                toastMsg = "活动太火爆了，请稍后再试";
                break;
        }
        return toastMsg;
    };
    goToVoucherCenter = () => {
        LogQmark({
            module: "default",
            id: "jumpCouponCenter",
            page: "qplay",
            operType: "click",
        });
        navigateTo({
            url: jumpData.voucherUrl,
        });
    };
    jumpRule = () => {
        LogQmark({
            module: "default",
            id: "jumpRule",
            page: "qplay",
            operType: "click",
        });
        navigateTo({
            url: jumpData.ruleUrl,
        });
    };
    render() {
        return (
            <View className="paid-coupon-container">
                {this.props.paidVoucherData?.voucherList?.length > 0 ? (
                    <View className="coupon-section">
                        <View
                            className="coupon-header-content"
                            onClick={() => this.goToVoucherCenter()}
                        >
                            <Text className="more-text">更多</Text>
                            <Text className="g-q-iconfont div-all-link-icon">
                                
                            </Text>
                        </View>
                        <ScrollView
                            show-scrollbar={false}
                            enable-flex
                            enhanced={true}
                            type="custom"
                            scroll-x={true}
                            className="coupon-list"
                        >
                            {this.props.paidVoucherData?.voucherList?.map(
                                (item, index) => {
                                    return (
                                        <View
                                            className="coupon-item"
                                            key={item.couponId}
                                        >
                                            <View className="coupon-details-container">
                                                <View className="offer-details-wrapper">
                                                    <Text className="hotel-exclusive-text">
                                                        {item.voucherLabel}
                                                    </Text>
                                                    <View className="price-quantity-wrapper">
                                                        <Text className="price-tag">
                                                            {
                                                                item.signalVoucherPrice
                                                            }
                                                        </Text>
                                                        <Text className="currency-unit">
                                                            元
                                                        </Text>
                                                        <Text className="multiplication-sign">
                                                            ×
                                                        </Text>
                                                        <Text className="quantity-tag">
                                                            {item.voucherNumber}
                                                        </Text>
                                                        <Text className="quantity-unit">
                                                            张
                                                        </Text>
                                                    </View>
                                                </View>
                                                <Text className="offer-conditions-text">
                                                    {item.limitations}
                                                </Text>
                                            </View>
                                            <View className="coupon-price-container">
                                                <View className="pricing-container">
                                                    <View className="discount-price-wrapper">
                                                        <Text className="currency-symbol">
                                                            ¥
                                                        </Text>
                                                        <Text className="discount-price-value">
                                                            {item.salePrice}
                                                        </Text>
                                                    </View>
                                                    {item.historyPrice && (
                                                        <Text className="original-price-text">
                                                            ¥{item.historyPrice}
                                                        </Text>
                                                    )}
                                                </View>
                                                {item.buttonType !==
                                                    "hidden" && (
                                                    <View
                                                        className={`purchase-link-container ${item.buttonType === "disable" ? "purchase-link-container-disabled" : ""} 
                                            ${item.buttonText === "去退款" ? "purchase-link-container-custom" : "purchase-link-container-normal"}`}
                                                        onClick={() =>
                                                            this.btnClick(item)
                                                        }
                                                    >
                                                        {item.buttonText}
                                                    </View>
                                                )}
                                            </View>
                                            {item.description && (
                                                <View className="coupon-text-container">
                                                    {item.description}
                                                </View>
                                            )}
                                        </View>
                                    );
                                },
                            )}
                        </ScrollView>
                        <View className="coupon-bottom-content">
                            {this.props.paidVoucherData.listDesc}｜
                            <Text onClick={this.jumpRule}>查看更多规则</Text>
                        </View>
                    </View>
                ) : (
                    <View></View>
                )}
            </View>
        );
    }
}

export default QPlayPaidVoucherList;
