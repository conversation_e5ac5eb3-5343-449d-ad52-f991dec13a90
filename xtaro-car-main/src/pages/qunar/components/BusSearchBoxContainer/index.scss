@font-face {
    font-family: 'g-q-iconfont';
    src: url('https://s.qunarzz.com/nanachi/score/font/0.0.100/minprogram_nanachi.ttf');
}

// @font-face {
//     font-family: "hotel_rn_num";
//     src: url("https://s.qunarzz.com/flight_shark/lowPriceRn/hotel_rn_num.ttf");
// }

.g-q-iconfont-train {
    font-family: 'g-q-iconfont';
    font-family: 'g-q-iconfont' !important;
    font-size: 12px;
    font-style: normal;
}

.container-old {
    position: relative;
    z-index: 10;
    .card-container {
        margin-bottom: 12px;
        border-radius: 16px;
        overflow: hidden;
        background-image: linear-gradient(180deg, #F5F6F9 0%, #FFFFFF 46%);
        .yellow_tip_wraper{
            padding: 0 16px;
            box-sizing: border-box;
            width: 100%;
            .yellow_tip_content{
                width: 100%;
                box-sizing: border-box;
                padding: 5px 10px;
                background: #FFF8E5;
                border-radius: 4px;
                display: flex;
                flex-flow: row nowrap;
                justify-content: flex-start;
                align-items: center;
                .tip-notice-icon{
                    padding-right: 8px;
                    font-size: 16px;
                    color: #FF8300;
                }
                .scorll-wrapper{
                    flex: 1;
                    position: relative;
                    overflow: hidden;
                    .scorll-container {
                        -webkit-box-flex: 1;
                        -webkit-flex: 1 1 0;
                        -moz-flex: 1 1 0;
                        flex: 1 1 0;
                        display: -webkit-box;
                        display: -webkit-flex;
                        display: -moz-flex;
                        display: flex;
                        overflow: hidden;

                        // height: 32rpx;
                        position: relative;
                        align-items: center;

                        .move-content {
                            display: -webkit-box;
                            display: -webkit-flex;
                            display: -moz-flex;
                            display: flex;
                            position: absolute;
                            left: 0;

                            &.duplication {
                                display: none;
                            }

                            .move-box {
                                color: #FF8300;
                                font-size: 12px;
                                white-space: nowrap;
                                line-height: 16px;
                            }
                        }
                    }
                    @keyframes scroll {
                        0% {
                            transform: translate3d(0, 0, 0);
                        }
                        100% {
                            transform: translate3d(-100%, 0, 0);
                        }
                    }
                }
                .tip-detail-icon, .tip-close-icon{
                    padding-left: 8px;
                    font-size: 18px;
                    color: #FF8300;
                    width: 18px;
                }
            }
        }
        .search-container {
            padding: 0 24px 12px;
            border-top-right-radius: 16px;
            position: relative;
            overflow: hidden;
            .service {
                padding: 12px 0 0;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                text-align: center;
                font-size: 12px;
                height: 17px;
                &.service-no-history {
                    padding: 0;
                }
                .anxin {
                    width: 43px;
                    height: 13.5px;
                    background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/anxingou.png');
                    background-size: 100% 100%;
                }
                .divline {
                    margin-right: 8px;
                    width: 20px;
                    height: 1px;
                    opacity: 0.5;
                    background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
                }
                .divline-right{
                    margin-right: 0;
                    margin-left: 8px;
                    background-image: linear-gradient(270deg, #ffffff 0%, #00d5e7 100%);
                }
                .text-list{
                    margin-bottom: 1px;
                    .service-text{
                        margin-left: 7px;
                        color: #000;
                        opacity: 0.8;
                        .dot {
                            padding-right: 2px;
                            color: #00CAD8;
                            font-size: 14px;
                        }
                        .service-item {
                            opacity: 0.8;
                            font-family: PingFangSC-Light;
                            font-size: 12px;
                            color: #000000;
                            letter-spacing: 0;
                        }
                    }
                }
            }
        }
    }
    .CitySelectorBus {
        position: relative;
        height: 62px;
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        box-sizing: content-box;
        flex-direction: row;
        align-items: center;
    }
    .flex1 {
        flex: 1;
    }
    
    .CitySelectorBus .cityTap {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        font-size: 21px;
        max-width: 40%;
    }
    
    .CitySelectorBus .noCity {
        color: #cccccc;
    }
    
    .CitySelectorBus .arrCity {
        text-align: right;
    }
    
    .CitySelectorBus .arrCityContent {
        text-align: right;
    }
    
    .CitySelectorBus .arrCityContent,
    .CitySelectorBus .depCityContent {
        height: 100%;
        line-height: 50px;
        overflow: hidden;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 22px;
        font-weight: 700;
    }
    
    .CitySelectorBus .cityTap .depCity-changing {
        -webkit-transition: -webkit-transform .3s ease;
        transition: transform .3s ease;
        -webkit-transform: translateX(calc(177.5px + 22.5px));
        transform: translateX(calc(177.5px + 22.5px));
        text-align: right;
    }
    
    .CitySelectorBus .cityTap .arrCity-changing {
        -webkit-transition: -webkit-transform .3s ease;
        transition: transform .3s ease;
        -webkit-transform: translateX(calc(-177.5px - 22.5px));
        transform: translateX(calc(-177.5px - 22.5px));
        text-align: left;
    }
    
    .CitySelectorBus .cityTap:active,
    .search-container .dateSelector:active,
    .dateSelector:active .date-week {
        color: #1fbcd2 !important;
    }
    
    .search-container .dateSelector {
        position: relative;
        height: 62px;
        line-height: 62px;
        font-size: 22px;
        color: #222222;
        font-weight: bold;
        border-bottom: 0.5px solid #eeeeee;
        border-top: 0.5px solid #eeeeee;
    }
    
    .search-container .city_change {
        position: relative;
        width: 35px;
        height: 35px;
        margin: 13px 20px;
    }
    
    .search-container .exchange-logo {
        width: 35px;
        height: 40px;
        position: absolute;
    }
    
    .search-container .exchange-btn {
        width: 35px;
        height: 35px;
        margin-bottom: 5px;
    }
    
    .search-container .btn-rotating {
        -webkit-transition: -webkit-transform .3s ease;
        transition: transform .3s ease;
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
    }
    
    .date-week {
        display: inline;
        font-size: 14px;
        // margin-left: 15px;
        // float: right;
        color: #666;
    }
    .search-button {
        background-image: linear-gradient(270deg, #FC650F 0%, #FF982E 100%);
        box-shadow: 0 8prx 10px 0 rgba(255, 85, 0, 0.20);
        border-radius: 50px;
        color: #fff;
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        height: 48px;
        line-height: 48px;
        margin: 15px 0;
        background-size: cover;
        background-repeat: no-repeat;
    
    
    }
    .search-container .search-button:active {
        filter: brightness(0.8) grayscale(0.3);
    }
    
    .index-historyList {
        position: relative;
        height: 39px;
        overflow: hidden;
        border-bottom: 0.5px solid #F2F2F2;
    }
    
    .index-history-scroll {
        display: flex;
        flex-direction: row;
        // align-items: center;
        overflow-x: auto;
        height: 50px;
    }
    
    
    .index-historyList .his,
    .index-historyList .disable {
        font-size: 12px;
        color: #999999;
        line-height: 24px;
        /* border-bottom-color: #666666;
        border-bottom-width: 1px;
        border-bottom-style: dashed; */
    }
    
    .index-historyList .his-button {
        background-color: rgba(240,242,247, 0.5);
        padding: 0 10px;
        line-height: inherit;
        border-radius: 12px;
        outline: #f9f9fc;
    }
    
    .index-historyList .row {
        display: flex;
        height: 24px;
        justify-content: center;
        white-space: nowrap;
        margin-left: 10px;
    }
    
    .index-historyList .row:first-child {
        margin-left: 0;
    }
    
    // .index-historyList .row:last-child {
    //     padding-right: 30rpx;
    // }
    
    
    .index-historyList .shadow {
        position: absolute;
        height: 100%;
        width: 24.5px;
        right: 0;
        background: rgba(255, 255, 255, 0);
        /*一些不支持背景渐变的浏览器*/
        background: -webkit-linear-gradient(to left, rgba(255, 255, 255, 0), #ffffff);
        background: linear-gradient(to left, rgba(255, 255, 255, 0), #ffffff);
        z-index: 10;
    }
    
    .close:before,
    .close-text:before {
        content: '\f3f3';
    }
    
    .tip-view {
        position: fixed;
        left: 10px;
        right: 10px;
        border-radius: 4px;
        height: 32px;
        top: 0;
        padding-left: 10px;
        padding-right: 10px;
        justify-content: space-between;
        display: flex;
        align-items: center;
    }
    
    .tip-text {
        font-size: 13px;
        color: #fff;
    }
    
    .close-text {
        font-size: 17px;
        color: #fff;
    }
    
    
    

}

.container {
    .card-container {
        padding: 0 16px;
        border-radius: 0 0 12px 12px;
        margin-bottom: 0;
        background-image: none;
        background: #fff;
        .yellow_tip_wraper{
            padding: 0 16px;
            box-sizing: border-box;
            width: 100%;
            .yellow_tip_content{
                width: 100%;
                box-sizing: border-box;
                padding: 5px 10px;
                background: #FFF8E5;
                border-radius: 4px;
                display: flex;
                flex-flow: row nowrap;
                justify-content: flex-start;
                align-items: center;
                .tip-notice-icon{
                    padding-right: 8px;
                    font-size: 16px;
                    color: #FF8300;
                }
                .scorll-wrapper{
                    flex: 1;
                    position: relative;
                    overflow: hidden;
                    .scorll-container {
                        -webkit-box-flex: 1;
                        -webkit-flex: 1 1 0;
                        -moz-flex: 1 1 0;
                        flex: 1 1 0;
                        display: -webkit-box;
                        display: -webkit-flex;
                        display: -moz-flex;
                        display: flex;
                        overflow: hidden;

                        // height: 32rpx;
                        position: relative;
                        align-items: center;

                        .move-content {
                            display: -webkit-box;
                            display: -webkit-flex;
                            display: -moz-flex;
                            display: flex;
                            position: absolute;
                            left: 0;

                            &.duplication {
                                display: none;
                            }

                            .move-box {
                                color: #FF8300;
                                font-size: 12px;
                                white-space: nowrap;
                                line-height: 16px;
                            }
                        }
                    }
                    @keyframes scroll {
                        0% {
                            transform: translate3d(0, 0, 0);
                        }
                        100% {
                            transform: translate3d(-100%, 0, 0);
                        }
                    }
                }
                .tip-detail-icon, .tip-close-icon{
                    padding-left: 8px;
                    font-size: 18px;
                    color: #FF8300;
                    width: 18px;
                }
            }
        }
        .search-container {
            border-top-right-radius: 0;
            padding: 0;
            .service {
                padding: 12px 0 0;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                text-align: center;
                font-size: 12px;
                height: 17px;
                &.service-no-history {
                    padding: 0;
                }
                .anxin {
                    width: 43px;
                    height: 13.5px;
                    background-image: url('https://s.qunarzz.com/open_m_train/miniprogram/indexChange/anxingou.png');
                    background-size: 100% 100%;
                }
                .divline {
                    margin-right: 8px;
                    width: 20px;
                    height: 1px;
                    opacity: 0.5;
                    background-image: linear-gradient(90deg, #ffffff 0%, #00d5e7 100%);
                }
                .divline-right{
                    margin-right: 0;
                    margin-left: 8px;
                    background-image: linear-gradient(270deg, #ffffff 0%, #00d5e7 100%);
                }
                .text-list{
                    margin-bottom: 1px;
                    .service-text{
                        margin-left: 7px;
                        color: #000;
                        opacity: 0.8;
                        .dot {
                            padding-right: 2px;
                            color: #00CAD8;
                            font-size: 14px;
                        }
                        .service-item {
                            opacity: 0.8;
                            font-family: PingFangSC-Light;
                            font-size: 12px;
                            color: #000000;
                            letter-spacing: 0;
                        }
                    }
                }
            }
        }
    }
    .dashed-line {
        width: 100%;
        border-top: 1px dashed #f2f4f7;
    }
    
    .CitySelectorBus {
        justify-content: space-between;
        height: auto;
    }
    
    
 
    
    .CitySelectorBus .cityTap {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        flex: 1;
        font-size: 21px;
        max-width: 40%;
    }
    
    
    .CitySelectorBus .arrCity {
        text-align: right;
    }
    
    .CitySelectorBus .arrCityContent {
        text-align: right;
    }
    
    .CitySelectorBus .arrCityContent,
    .CitySelectorBus .depCityContent {
        font-size: 24px;
        color: #222222;
        font-weight: 700;
        height: auto;
        line-height: normal;
        &.noCity {
            color: #cccccc;
        }
    }
    
    .CitySelectorBus .cityTap .depCity-changing {
        -webkit-transition: -webkit-transform .3s ease;
        transition: transform .3s ease;
        -webkit-transform: translateX(calc(177.5px + 22.5px));
        transform: translateX(calc(177.5px + 22.5px));
        text-align: right;
    }
    
    .CitySelectorBus .cityTap .arrCity-changing {
        -webkit-transition: -webkit-transform .3s ease;
        transition: transform .3s ease;
        -webkit-transform: translateX(calc(-177.5px - 22.5px));
        transform: translateX(calc(-177.5px - 22.5px));
        text-align: left;
    }
    
    .CitySelectorBus .cityTap:active,
    .search-container .dateSelector:active,
    .dateSelector:active .date-week {
        color: #1fbcd2 !important;
    }
    
    .search-container .dateSelector {
        position: relative;
        height: auto;
        line-height: 20px;
        margin-top: 18px;
        margin-bottom: 28px;
        border: none;
        font-size: 20px;
        color: #222222;
        font-weight: 600;
        font-family: PingFangSC-Semibold;
        .date_month, .date_day {
            font-family: "hotel_rn_num";
            font-size: 22px;
            font-weight: 700;
            position: relative;
            top: 2px;
        }
    }
    
    .search-container .city_change {
        position: relative;
        width: 32px;
        height: 32px;
        margin: 0;
        // margin: 13px 20px;
    }
    
    .search-container .exchange-logo {
        width: 32px;
        height: 32px;
        position: absolute;
    }
    
    .search-container .exchange-btn {
        width: 35px;
        height: 35px;
        margin-bottom: 5px;
    }
    

    .date-week {
        font-family: PingFangSC-Light;
        display: inline;
        font-size: 14px;
        margin-left: 4px;
        // float: right;
        font-weight: normal;
        color: #222222;
    }

    
    .search-container .search-button {
        background-image: linear-gradient(270deg, #FC4650 0%, #FF9658 100%);
        box-shadow: 0 8prx 10px 0 rgba(255, 85, 0, 0.20);
        border-radius: 25px;
        font-weight: 700;
        height: 48px;
        line-height: 48px;
        margin: 0;
        background-size: cover;
        background-repeat: no-repeat;
        font-family: PingFangSC-Semibold;
    }
    
    .search-container .search-button:active {
        filter: brightness(0.8) grayscale(0.3);
    }
 
    .search-btn-bottom-shadow {
        height: 16px;
        background-size: 100% 100%;
        overflow: hidden;
        width: 100%;
        background-image: url('https://s.qunarzz.com/f_cms/2024/1709889419165_445841728.png');
    }
    .index-historyList {
        // position: relative;
        height: 36px;
        border-bottom: 0;
        // overflow: hidden;
    }
    
    .index-history-scroll {
        height: 36px;
    }

    
    
    .index-historyList .his,
    .index-historyList .disable {
        font-size: 12px;
        color: #666;
        font-family: PingFangSC-Light;
        line-height: 20px;
    }
    .del-icon {
        font-size: 16px;
        color: #666;
    }
    
    .index-historyList .his-button {
        background-color: #F0F2F7;
        padding: 0 8px;
        line-height: inherit;
        border-radius: 12px;
    }
    
    .index-historyList .row {
        height: 20px;
        margin-left: 8px;
    }
    
   
    .index-historyList .shadow {
        position: absolute;
        height: 100%;
        width: 24.5px;
        right: 0;
        background: rgba(255, 255, 255, 0);
        /*一些不支持背景渐变的浏览器*/
        background: -webkit-linear-gradient(to left, rgba(255, 255, 255, 0), #ffffff);
        background: linear-gradient(to left, rgba(255, 255, 255, 0), #ffffff);
        z-index: 10;
    }
    
    .close:before,
    .close-text:before {
        content: '\f3f3';
    }
    
    .tip-view {
        position: fixed;
        left: 10px;
        right: 10px;
        border-radius: 4px;
        height: 32px;
        top: 0;
        padding-left: 10px;
        padding-right: 10px;
        justify-content: space-between;
        display: flex;
        align-items: center;
    }
    
    .tip-text {
        font-size: 13px;
        color: #fff;
    }
    
    .close-text {
        font-size: 17px;
        color: #fff;
    }
    
    
}

.order-block-old {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
}

.order-block-old .order-button {
    line-height: 70px;
    display: flex;
    flex-direction: row;
    border-radius: 16px;
    justify-content: center;
    align-items: center;
    background-color: #ffffff;
    font-size: 16px;
    width: 175px;
    // padding: 0 50rpx;
}

.order-block-old .order-button-icon {
    height: 42px;
    width: 42px;
    margin-right: 12.5px;
}

.my-right-not-login {
    position: relative;
    .my-right-button {
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
        width: 100%;
        height: 100%;
    }
}

.order-block {
    border-radius: 12px;
    overflow: hidden;
    .order-button {
        height: 64px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #666;
        line-height: normal;
        flex: 1;
        border-radius: 0;
        position: relative;
    }
    .sperate-line {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 20px;
        width: 1px;
        background-color: rgba(204,204,204, 0.5);
    }
    .order-button-icon {
        height: 20px;
        width: 20px;
        margin-bottom: 2px;
        margin-right: 0;
    }
}

