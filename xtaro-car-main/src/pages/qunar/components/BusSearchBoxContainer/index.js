import { Block, View, Text, Image, Button } from "@tarojs/components";
import {
    setNavigationBarTitle,
    getStorageSync,
    setStorageSync,
    setStorage,
    navigateTo,
    showModal,
    reLaunch,
    showToast,
} from "@tarojs/taro";
import React from "react";

import { checkLogin } from "@/common/bus/utils/userUtils";
import EventEmitter from "@/common/utils/EventEmitter.js";
import BusConstant from "@/common/bus/config/Constant";
import dateFormat from "@/common/utils/date/format.js";
import Promiser from "@/common/bus/requester/promiseRequester.js";
import Service from "@/common/bus/config/requestService.js";
import Log from "@/common/utils/log";
import navigation from "@/common/bus/utils/navigation.js";
import qPoint from "@/common/bus/qmark.js";
import watcher from "@/common/train/watcher";
import cb from "@/common/utils/pageCb";
import User from "@/common/utils/user";
import { initGetPhoneNumber } from "@/common/train/loginPhone";
import "./index.scss";

var constant = {
    event: {
        CALENDAR_DATE_CHANGE: "CALENDAR_DATE_CHANGE_BUS",
        CITY_CHANGE: "CITY_CHANGE_BUS",
        SEARCHLIST_DATE_CHANGE: "SEARCHLIST_DATE_CHANGE",
    },
    STORAGE_KEY: {
        DEP_DATE: "DepDate",
        DEP_CITY: "DepCity",
        ARR_CITY: "ArrCity",
        HISTORY_CITY: "HistoryCity",
        HOT_CITY: "HotCity",
        PASSENGER_FILL: "FillData",
    },
    bus: {
        name: "bus",
        displayName: "汽车票",
        stDate: 1,
        navUrl: "/pages/newbus/searchlist/index?dep={depCity}&arr={arrCity}&date={searchDate}",
        share: {
            title: "汽车票预订 - 去哪儿旅行",
            desc: "可预订全国20万+车站线路、直达景区旅游专线，线上购票无需排队，极速出票，支持预约抢票",
            path: "/pages/bus/home/<USER>",
        },
        cityList: {
            type: 2,
            eventType: "CITY_CHANGE",
            placeholder: "城市、车站的中文或拼音",
        },
    },
};
var closeTipViewTimes = 0;

class BusSearchBoxContainer extends React.Component {
    static config = {
        backgroundColor: "#F5F5F5",
        navigationBarTextStyle: "white",
        navigationStyle: "custom",
    };

    constructor(props) {
        super(props);
        this.state = {
            pageParams: {},
            displayName: "去哪儿旅行",
            bizType: "bus",
            depCity: "",
            arrCity: "",
            DEP_DATE_KEY: "",
            DEP_CITY_KEY: "",
            ARR_CITY_KEY: "",
            historyList: [],
            isShowTipView: true,
            SLOGAN: {
                bus: "全程预订保障，想去哪儿就去哪儿",
            },
            homeStyleConfig: {
                bus: {
                    backgroundUrl:
                        "https://s.qunarzz.com/f_cms/2022/1659606085527_015301157.png",
                    backgroundColor: "#F5F5F9",
                },
            },
            selectedTab: 0,
            busBannerResultList: [],
            isShowYellowTip: true,
            yellowTip: {},
            mainBtnConfig: {},
            rightList: [],
            serviceList: ["低价保障", "出行保障", "售后保障"],
        };
    }

    async componentWillMount() {
        qPoint("bus/new_bus_index/default/enter/bus_home_enter");
        let options = this.props.query || {};
        let me = this;
        options.bizType = options.bizType || me.state.bizType;
        this.swithcBiz(options.bizType);

        me._registerListeners();

        this.checkLogin(options, (isLogin) => {
            this.setState({
                isLogin: isLogin,
            });
        });
        await this.getHotConfig();
        this.dealWithYellowTipScroller();
    }

    getHotConfig = async () => {
        try {
            let params = {
                keyList: ["json.yellowTip", "json.busIndex.mainBtnConfig"],
            };
            const res = await Promiser.postRequest(
                Service.requestService.HotConfig,
                params,
            );
            const data = res && res.data;
            const resData = (data && data.data) || {};
            const yellowTip = JSON.parse(resData["json.yellowTip"]) || {};
            const mainBtnConfig =
                JSON.parse(resData["json.busIndex.mainBtnConfig"]) || {};
            this.setState({
                yellowTip,
                mainBtnConfig,
            });
        } catch (e) {}
    };
    dealWithYellowTipScroller = () => {
        let { yellowTip = {} } = this.state;
        const { tipContent = "" } = yellowTip;
        if (!tipContent) {
            return;
        }
        let moveStyle = {},
            moveDupStyle = {},
            delay = 1;
        if (tipContent.length > 25) {
            let time1 = Math.round(tipContent.length / 4);
            let time2 = Math.round((tipContent.length + 25) / 4);
            moveDupStyle.paddingLeft = "100%";
            moveDupStyle.display = "flex";
            moveDupStyle.animation = `scroll ${time2}s linear ${time1 + delay}s infinite normal`;

            moveStyle.animation = `scroll ${time1}s linear ${delay}s 1 normal forwards`;
            this.setState({
                moveStyle,
                moveDupStyle,
            });
        }
    };
    componentWillUnmount() {
        this._removeListeners();
    }

    swithcBiz = (bizType) => {
        var bizTypeCfg = constant[bizType];
        this.bizTypeCfg = bizTypeCfg;
        var upperCaseBizType = bizType.toUpperCase();

        this.setState(
            {
                pageParams: bizTypeCfg,
                bizType: bizType,
                DEP_DATE_KEY:
                    constant.STORAGE_KEY.DEP_DATE + "_" + upperCaseBizType,
                DEP_CITY_KEY:
                    constant.STORAGE_KEY.DEP_CITY + "_" + upperCaseBizType,
                ARR_CITY_KEY:
                    constant.STORAGE_KEY.ARR_CITY + "_" + upperCaseBizType,
            },
            function () {
                this._init(true);
                this.updateHistoryList();
                setNavigationBarTitle({
                    title: bizTypeCfg.displayName,
                });
            }.bind(this),
        );
    };

    _init = (inSwitch) => {
        var me = this,
            pageDefaultData = me.state,
            depCity,
            arrCity,
            depDate,
            oneDay = 86400000;
        var pageParams = pageDefaultData.pageParams; //初始化日期和城市
        depDate = getStorageSync(pageDefaultData.DEP_DATE_KEY);
        depCity = getStorageSync(pageDefaultData.DEP_CITY_KEY) || "北京";
        arrCity = getStorageSync(pageDefaultData.ARR_CITY_KEY);

        if (inSwitch) {
        } else {
            try {
                if (pageParams.dep) {
                    depCity = decodeURIComponent(pageParams.dep);
                    setStorageSync(pageDefaultData.DEP_CITY_KEY, depCity);
                }

                if (pageParams.arr) {
                    arrCity = decodeURIComponent(pageParams.arr);
                    setStorageSync(pageDefaultData.ARR_CITY_KEY, arrCity);
                }

                if (pageParams.date) {
                    var depDateString = decodeURIComponent(pageParams.date);
                    var date = new Date(depDateString.replace(/-/g, "/"));

                    if (date) {
                        depDate = date.getTime();
                        setStorageSync(pageDefaultData.DEP_DATE_KEY, depDate);
                    }
                }
            } catch (error) {}
        }

        if (depDate && depDate >= Date.now()) {
            depDate = depDate;
        } else {
            var shiftDay = pageParams.shiftDay || 0;
            var nowDate = new Date(); ///超过下午3点默认明天

            if (shiftDay <= 0 && nowDate.getHours() >= 15) {
                shiftDay = 1;
            }

            var shiftTime = shiftDay * oneDay;
            var now = Date.now();
            depDate = now + shiftTime;
        }

        me.setState({
            arrCity: arrCity || "",
            depCity: depCity || "",
            depDate: depDate || "",
        });

        me._setDate(new Date(depDate));
    };
    _setDate = (date) => {
        var me = this,
            displayDate = dateFormat(date, "m月d日"),
            displayDateMonth = date.getMonth() + 1,
            displayDateDay = date.getDate(),
            searchDate = dateFormat(date, "yyyy-mm-dd"),
            dateWeek = dateFormat(date, "周w"),
            now = new Date(),
            oneDay = 86400000,
            today = new Date(
                now.getFullYear() +
                    "/" +
                    (now.getMonth() + 1) +
                    "/" +
                    now.getDate(),
            ).getTime(),
            days = parseInt((date - today) / oneDay),
            dayStr = "";
        var dayStrs = ["今天", "明天", "后天"];

        if (days < 3 && days >= 0) {
            dayStr = dayStrs[days];
        }
        setStorage({
            key: me.state.DEP_DATE_KEY,
            data: date.getTime(),
        });
        this.setState({
            displayDate,
            searchDate,
            dateWeek,
            dayStr,
            displayDateMonth,
            displayDateDay,
        });
    };

    _registerListeners = () => {
        var me = this;
        me._dateListener = EventEmitter.addListener(
            constant.event.CALENDAR_DATE_CHANGE,
            function (date) {
                me._setDate(date);
            },
        );

        me._dateChangeListener = EventEmitter.addListener(
            constant.event.SEARCHLIST_DATE_CHANGE,
            function (date) {
                me._setDate(date);
            },
        );
        me._cityListener = EventEmitter.addListener(
            constant.event.CITY_CHANGE,
            function (cityObj) {
                me._setCity(cityObj);
            },
        );
    };

    _removeListeners = () => {
        var me = this;
        me._dateListener && me._dateListener.removeListener();
        me._dateChangeListener && me._dateChangeListener.removeListener();
        me._cityListener && me._cityListener.removeListener();
    };

    closeTipView = () => {
        closeTipViewTimes += 1;
        var isShowTipView = closeTipViewTimes === 0;
        this.setState({
            isShowTipView: isShowTipView,
        });
    };

    onChooseDate = () => {
        var me = this,
            now = new Date().getTime(),
            data = me.state,
            bizType = data.bizType; //防止多次点击，锁定500ms

        if (now - data.lastTap < 500) {
            return;
        }

        me.setState({
            lastTap: now,
        });
        qPoint("bus/new_bus_index/default/click/select_date_new");
        var params = {
            bizType: bizType,
            // 业务线
            date: data.searchDate,
            // 默认单选日期；多选的第一个日期 （不传的话展示今天）
            eventType: constant.event.CALENDAR_DATE_CHANGE,
            // 选择日期成功之后触发的事件，通过监听这个事件来得到响应
            storageType: data.DEP_DATE_KEY,
        };
        navigateTo({
            url: "/pages/newbus/calendar/index?data=" + JSON.stringify(params),
        });
    };

    onChooseCity = (e) => {
        var me = this,
            now = Date.now(),
            data = me.state; //防止多次点击，锁定500ms  切换中无视点击

        if (data.exchangeStatus || now - data.lastTap < 500) {
            return;
        }

        me.setState({
            lastTap: now,
        });
        var dateSet = e.currentTarget.dataset,
            type = dateSet.type,
            isDep = type === "depCity" ? true : false;

        if (isDep) {
            qPoint("bus/new_bus_index/default/click/select_dcity_new");
        } else {
            qPoint("bus/new_bus_index/default/click/select_acity_new");
        }

        var params = {
            type: 2,
            eventType: "CITY_CHANGE_BUS",
            placeholder: "城市、车站的中文或拼音",
            fromCity: this.state.depCity,
            isDep: isDep,
        };
        const url =
            "/pages/newbus/citySelect/index?data=" + JSON.stringify(params);
        navigateTo({
            url,
        });
    };

    onHistoryClear = () => {
        qPoint("bus/new_bus_index/default/click/clear_history");
        setStorage({
            key: BusConstant.BUS_CACHE_KEY.SEARCH_HISTORY,
            data: [],
        });
        this.setState({
            historyList: [],
        });
    };

    onHistory = (e) => {
        var data = e.currentTarget.dataset.data;

        if (this.ubtTrace) {
            this.ubtTrace(100875, data);
        }

        qPoint("bus/new_bus_index/default/click/choice_history");
        var json = {
            depCity: data.fromCity,
            arrCity: data.toCity,
        };
        this.setState(json, () => {
            // this.onSearch();
            this._setCity({ isDep: true, city: data.fromCity });
            this._setCity({ isDep: false, city: data.toCity });
        });
    };

    _setCity = (cityObj) => {
        var me = this,
            obj = {},
            key = cityObj.isDep ? me.state.DEP_CITY_KEY : me.state.ARR_CITY_KEY,
            viewDataKey = cityObj.isDep ? "depCity" : "arrCity";
        obj[viewDataKey] = cityObj.city;

        if (cityObj.isDep) {
            obj.inLoading = false;
        }

        me.setState(obj);
        setStorage({
            key: key,
            data: cityObj.city,
        });
    };

    onCityExchange = () => {
        var me = this,
            data = me.state;

        if (data.exchangeStatus) {
            return;
        }

        qPoint("bus/new_bus_index/default/click/exchange_city_new");
        me.setState({
            exchangeStatus: true,
        });
        setTimeout(function () {
            me.setState({
                exchangeStatus: false,
                arrCity: data.depCity,
                depCity: data.arrCity,
            });
        }, 300);
    };

    checkLogin = (options, callback) => {
        if (options.isLogin) {
            callback(options.isLogin);
        } else {
            checkLogin().then((res) => {
                callback(res.isLogin || res.ret);
            });
        }
    };

    onSearch = () => {
        var me = this,
            now = new Date().getTime(),
            data = me.state;

        if (data.inLoading) {
            return;
        }

        if (!data.depCity) {
            showModal({
                title: "小驼提示",
                content: "请选择出发城市\n",
                showCancel: false,
                confirmColor: "#00bcd4",
            });
            return;
        }

        if (!data.arrCity) {
            showModal({
                title: "小驼提示",
                content: "还没选到达城市呢\n",
                showCancel: false,
                confirmColor: "#00bcd4",
            });
            return;
        }

        if (data.exchangeStatus) {
            return;
        } //防止多次点击，锁定500ms

        if (now - data.lastTap < 500) {
            return;
        }

        me.setState({
            lastTap: now,
        });

        qPoint("bus/new_bus_index/default/click/search_btn_new", {
            depCity: data.depCity,
            arrCity: data.arrCity,
            sdate: this.state.searchDate,
        });

        if (this.state.bizType === "bus") {
            this.updateHistoryList(data);
        }

        var url = me.bizTypeCfg.navUrl
            .replace(/{depCity}/, data.depCity)
            .replace(/{arrCity}/, data.arrCity)
            .replace(/{searchDate}/, data.searchDate);
        url += "&cat=" + (this.props.query.cat || "wx_app_home_page");
        url += "&eventType=" + constant.event.SEARCHLIST_DATE_CHANGE;

        navigateTo({
            url,
        });
    };

    updateHistoryList = ({ depCity = "", arrCity = "" } = {}) => {
        var fromCity = (depCity || "").replace(/(\[.*\])/g, "");
        var toCity = (arrCity || "").replace(/(\[.*\])/g, "");
        var historyList =
            getStorageSync(BusConstant.BUS_CACHE_KEY.SEARCH_HISTORY) || [];

        if (fromCity && toCity) {
            var index = -1;
            historyList.forEach((item, idx) => {
                if (item.fromCity === fromCity && item.toCity === toCity) {
                    index = idx;
                }
            });

            if (index >= 0) {
                console.log(index);
                historyList.splice(index, 1);
            }

            historyList.unshift({
                fromCity: fromCity,
                toCity: toCity,
            });

            if (historyList.length > 10) {
                historyList = historyList.slice(0, 10);
            }

            setStorage({
                key: BusConstant.BUS_CACHE_KEY.SEARCH_HISTORY,
                data: historyList,
            });
        }

        this.setState({
            historyList: historyList,
        });
    };

    jumpToOrderList = () => {
        qPoint("bus/new_bus_index/default/click/order_list");
        setStorageSync("my_page_order_filter", "bus");
        navigation.navigate({
            url: "/pages/qunar/subPages/orderList/orderList/index", //switchTab传不了url参数
        });
    };
    jumpToPolicy = () => {
        qPoint("bus/new_bus_index/default/click/policy");
        let { depCity, arrCity } = this.state;
        var url = `https://m.suanya.com/webapp/train/activity/ztrip-isolation-policy-query?from=${encodeURIComponent(depCity)}&to=${encodeURIComponent(arrCity)}&source=qunar`;
        navigateTo({
            url: "/pages/platform/webView/index?url=" + encodeURIComponent(url),
        });
    };
    //提示黄条点击事件
    yellowTipClick = () => {
        if (this.state.yellowTip.tipDetailUrl) {
            this.gotoTipDetail();
        } else if (this.state.yellowTip.isShowCloseTipIcon) {
            this.closeYellowTip();
        }
    };
    gotoTipDetail = () => {
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        if (this.state.yellowTip.tipDetailUrl) {
            //跳转黄条详情
            if (
                this.state.yellowTip.tipDetailUrl.indexOf(
                    "/pages/platform/indexWx/index",
                ) == 0
            ) {
                reLaunch({
                    url: this.state.yellowTip.tipDetailUrl,
                });
            } else if (this.state.yellowTip.tipDetailUrl.indexOf("http") == 0) {
                navigateTo({
                    url:
                        "/pages/platform/webView/index?url=" +
                        encodeURIComponent(this.state.yellowTip.tipDetailUrl),
                });
            } else {
                navigateTo({
                    url: this.state.yellowTip.tipDetailUrl,
                });
            }
        }
    };
    closeYellowTip = () => {
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        this.setState({ isShowYellowTip: false });
    };

    gotoMyRight = async () => {
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });
        watcher.sendWatcher("my_right_icon_new");
        qPoint("train/new_bus_index/default/click/my_right");
        navigateTo({
            url: "/pages/train/TrainMyRight/index",
        });
    };

    handleGetPhoneNumber = async (flag, e) => {
        Log({
            info: {
                area: "trainSearch",
                act: "click",
                type: "homeEnter",
                name: "trainSearch",
            },
        });

        var event = e;
        event = Object.assign({}, event);
        if (event.detail) {
            Object.assign(event, event.detail)
        }

        if (!(event && event.detail && event.detail.encryptedData)) {
            return;
        }
        initGetPhoneNumber(event.detail.encryptedData, event.detail.iv);
        // iv, encryptedData 微信给的秘钥
        cb.wxGetPhoneCb(event, async ({ encryptedData, iv }) => {
            let data = await User.loginByWxPhone({ iv, encryptedData });
            if (data && data.ret) {
                // 登录成功
                this.setState({
                    isLogin: true,
                });
            } else {
                this.setState({
                    isLogin: false,
                });
                showToast({
                    title: "登录失败，请稍后再试",
                    icon: "none",
                    duration: 3000,
                });
            }
        });
    };

    render() {
        return (
            <Block>
                <View
                    className={`container-old ${this.props.indexVersionGroup === "A" ? "container" : ""}`}
                >
                    <View className="card-container">
                        {this.state.isShowYellowTip &&
                            this.state.yellowTip &&
                            this.state.yellowTip.tipContent && (
                                <View className="yellow_tip_wraper">
                                    <View
                                        className="yellow_tip_content"
                                        onClick={this.yellowTipClick.bind(this)}
                                    >
                                        <View className="g-q-iconfont-train tip-notice-icon">
                                            &#xe4ee;
                                        </View>
                                        <View className="scorll-wrapper">
                                            <View
                                                class="scorll-container"
                                                style={{ height: "32rpx" }}
                                            >
                                                <View
                                                    class="move-content"
                                                    style={this.state.moveStyle}
                                                >
                                                    <Text class="move-box">
                                                        {
                                                            this.state.yellowTip
                                                                .tipContent
                                                        }
                                                    </Text>
                                                </View>
                                                <View
                                                    class="move-content duplication"
                                                    style={
                                                        this.state.moveDupStyle
                                                    }
                                                >
                                                    <Text class="move-box">
                                                        {
                                                            this.state.yellowTip
                                                                .tipContent
                                                        }
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                        {this.state.yellowTip.tipDetailUrl ? (
                                            <View className="g-q-iconfont-train tip-detail-icon">
                                                &#xe4c5;
                                            </View>
                                        ) : this.state.yellowTip
                                              .isShowCloseTipIcon ? (
                                            <View className="g-q-iconfont-train tip-close-icon">
                                                &#xe49a;
                                            </View>
                                        ) : (
                                            <View></View>
                                        )}
                                    </View>
                                </View>
                            )}

                        <View className="search-container">
                            <View className="CitySelectorBus">
                                <View
                                    className="cityTap"
                                    onClick={this.onChooseCity}
                                    data-type="depCity"
                                >
                                    <View
                                        className={
                                            "depCityContent " +
                                            (this.state.exchangeStatus
                                                ? "depCity-changing"
                                                : "") +
                                            " " +
                                            (this.state.depCity ? "" : "noCity")
                                        }
                                    >
                                        {this.state.depCity || "出发城市"}
                                    </View>
                                </View>
                                <View
                                    className="city_change"
                                    onClick={this.onCityExchange}
                                >
                                    <Image
                                        className="exchange-logo"
                                        // mode='widthFix'
                                        src={
                                            "https://s.qunarzz.com/f_cms/2024/1710209165806_258052333.png"
                                        }
                                    />
                                </View>
                                <View
                                    className="cityTap"
                                    onClick={this.onChooseCity}
                                    data-type="arrCity"
                                >
                                    <View
                                        className={
                                            "arrCityContent " +
                                            (this.state.exchangeStatus
                                                ? "arrCity-changing"
                                                : "") +
                                            " " +
                                            (this.state.arrCity ? "" : "noCity")
                                        }
                                    >
                                        {this.state.arrCity || "到达城市"}
                                    </View>
                                </View>
                            </View>
                            <View
                                className="dateSelector"
                                onClick={this.onChooseDate}
                            >
                                {this.props.indexVersionGroup === "A" ? (
                                    <Text>
                                        {" "}
                                        <Text className="date_month">
                                            {this.state.displayDateMonth}
                                        </Text>
                                        月
                                        <Text className="date_day">
                                            {this.state.displayDateDay}
                                        </Text>
                                        日
                                    </Text>
                                ) : (
                                    <Text>{this.state.displayDate}</Text>
                                )}

                                {/* {} */}
                                <View className="date-week">{`${this.state.dateWeek}  ${this.state.dayStr}`}</View>
                            </View>
                            <View
                                className="search-button"
                                style={{
                                    background:
                                        (this.state.mainBtnConfig &&
                                            this.state.mainBtnConfig.isOpen ==
                                                "true" &&
                                            this.state.mainBtnConfig
                                                .searchBtn &&
                                            this.state.mainBtnConfig.searchBtn
                                                .background) ||
                                        "",
                                    backgroundSize:
                                        (this.state.mainBtnConfig &&
                                            this.state.mainBtnConfig.isOpen ==
                                                "true" &&
                                            this.state.mainBtnConfig
                                                .searchBtn &&
                                            this.state.mainBtnConfig.searchBtn
                                                .backgroundSize) ||
                                        "",
                                    boxShadow:
                                        (this.state.mainBtnConfig &&
                                            this.state.mainBtnConfig.isOpen ==
                                                "true" &&
                                            this.state.mainBtnConfig
                                                .searchBtn &&
                                            this.state.mainBtnConfig.searchBtn
                                                .boxShadow) ||
                                        "",
                                }}
                            >
                                <View
                                    className="in-search-button"
                                    onClick={this.onSearch}
                                >
                                    {this.props.indexVersionGroup === "A"
                                        ? "汽车查询"
                                        : "汽车票查询"}
                                </View>
                            </View>
                            <View className="search-btn-bottom-shadow"></View>

                            {this.state.historyList &&
                                this.state.historyList.length > 0 && (
                                    <View className="index-historyList">
                                        {/* <div className='shadow' /> */}
                                        <View className="index-history-scroll">
                                            <Block>
                                                {this.state.historyList.map(
                                                    (item, index) => {
                                                        return (
                                                            <View
                                                                key={index}
                                                                className="row"
                                                            >
                                                                <View className="his-button">
                                                                    <View
                                                                        className="his"
                                                                        onClick={
                                                                            this
                                                                                .onHistory
                                                                        }
                                                                        data-data={
                                                                            item
                                                                        }
                                                                    >
                                                                        {
                                                                            <Text>
                                                                                {" "}
                                                                                {
                                                                                    item.fromCity
                                                                                }
                                                                                -
                                                                                {
                                                                                    item.toCity
                                                                                }{" "}
                                                                            </Text>
                                                                        }
                                                                    </View>
                                                                </View>
                                                            </View>
                                                        );
                                                    },
                                                    this,
                                                )}
                                                {this.state.historyList.length >
                                                    0 && (
                                                    <View className="row">
                                                        <View className="his-button">
                                                            <View
                                                                className="his"
                                                                onClick={
                                                                    this
                                                                        .onHistoryClear
                                                                }
                                                            >
                                                                {this.props
                                                                    .indexVersionGroup ===
                                                                "A" ? (
                                                                    <View
                                                                        style={{
                                                                            display:
                                                                                "flex",
                                                                            "flex-direction":
                                                                                "row",
                                                                            "align-items":
                                                                                "center",
                                                                        }}
                                                                    >
                                                                        <View className="g-q-iconfont-train del-icon">
                                                                            &#xe2a2;
                                                                        </View>
                                                                        清空
                                                                    </View>
                                                                ) : (
                                                                    <Text>
                                                                        清除历史
                                                                    </Text>
                                                                )}
                                                            </View>
                                                        </View>
                                                    </View>
                                                )}
                                            </Block>
                                        </View>
                                    </View>
                                )}

                            <View class="dashed-line"></View>
                        </View>
                    </View>
                </View>
                <View
                    className={`order-block-old ${this.props.indexVersionGroup === "A" ? "order-block" : ""}`}
                >
                    <View
                        className="order-button"
                        onClick={this.jumpToOrderList}
                    >
                        <Image
                            className="order-button-icon"
                            src={
                                this.props.indexVersionGroup === "A"
                                    ? "https://s.qunarzz.com/f_cms/2024/1709884000289_811211722.png"
                                    : "https://s.qunarzz.com/f_cms/2022/1659664249535_618721213.png"
                            }
                        />
                        <Text>我的订单</Text>
                        <View className="sperate-line"></View>
                    </View>
                    {this.state.isLogin ? (
                        <View
                            className="order-button"
                            onClick={this.gotoMyRight}
                        >
                            <Image
                                className="order-button-icon"
                                src={
                                    this.props.indexVersionGroup === "A"
                                        ? "https://s.qunarzz.com/f_cms/2024/1709883981117_959252285.png"
                                        : "https://s.qunarzz.com/f_cms/2023/1687780593368_3475217327.png"
                                }
                            />
                            <View>我的权益</View>
                        </View>
                    ) : (
                        <View className="order-button my-right-not-login">
                            <Image
                                className="order-button-icon"
                                src={
                                    this.props.indexVersionGroup === "A"
                                        ? "https://s.qunarzz.com/f_cms/2024/1709883981117_959252285.png"
                                        : "https://s.qunarzz.com/f_cms/2023/1687780593368_3475217327.png"
                                }
                            />
                            <View>我的权益</View>
                            <Button
                                open-type="getPhoneNumber"
                                className="my-right-button rewrite-btn"
                                onGetphonenumber={this.handleGetPhoneNumber.bind(
                                    this,
                                    "right",
                                )}
                            ></Button>
                        </View>
                    )}
                </View>
            </Block>
        );
    }
}

export default BusSearchBoxContainer;
