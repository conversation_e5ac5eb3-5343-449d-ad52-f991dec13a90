import { View, Text } from "@tarojs/components";
import React from "react";
import "./index.scss";

class QPlayCountDown extends React.Component {
    constructor(props) {
        super(props);
        this.timer = null;
        this.state = {
            hour: "00",
            minute: "00",
            second: "00",
            lastTime: 0,
        };
        this.calcLastTime = this.calcLastTime.bind(this);
    }
    componentDidMount() {
        let timerData = (this.props.endTime || "00:00").split(":");
        let now = Date.now();
        let endTimeState =
            Date.now() +
            (timerData[0] * 3600 + timerData[1] * 60 + timerData[2] * 1) * 1000;
        let lastTime = Math.round((endTimeState - now) / 1000);
        this.calcLastTime(lastTime);
    }

    shouldComponentUpdate(nextProps) {
        if (nextProps.endTime && this.props.endTime !== nextProps.endTime) {
            let timerData = (nextProps.endTime || "00:00").split(":");
            let now = Date.now();
            let endTimeState =
                Date.now() +
                (timerData[0] * 3600 + timerData[1] * 60 + timerData[2] * 1) *
                    1000;
            let lastTime = Math.round((endTimeState - now) / 1000);
            this.calcLastTime(lastTime);
        }
        return true;
    }

    componentWillUnmount() {
        this.timer && clearTimeout(this.timer);
    }

    calcLastTime = (lastTime = 0) => {
        this.timer && clearTimeout(this.timer);
        this.timer = null;
        if (lastTime <= 0) {
            lastTime = 0;
            this.setState({
                hour: "00",
                minute: "00",
                second: "00",
                lastTime,
            });
            this.props.getBargainList();
            return;
        }

        let hour = Math.floor(lastTime / 3600);
        let lastMin = lastTime % 3600;
        let minute = Math.floor(lastMin / 60);
        let second = lastTime % 60;

        this.setState({
            hour: hour < 10 ? "0" + hour : hour,
            minute: minute < 10 ? "0" + minute : minute,
            second: second < 10 ? "0" + second : second,
            lastTime: lastTime - 1,
        });

        this.timer = setTimeout(() => this.calcLastTime(lastTime - 1), 1000);
    };

    render() {
        return (
            <View class="m-countdown-component-container">
                <View class="countdown-box">
                    <Text class="time-box">{this.state.hour}</Text>
                    <Text class="time-dot">:</Text>
                    <Text class="time-box">{this.state.minute}</Text>
                    <Text class="time-dot">:</Text>
                    <Text class="time-box">{this.state.second}</Text>
                    <Text class="time-after">后失效</Text>
                </View>
            </View>
        );
    }
}

export default QPlayCountDown;
