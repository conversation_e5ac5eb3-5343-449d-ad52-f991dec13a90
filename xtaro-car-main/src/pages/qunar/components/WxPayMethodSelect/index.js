import { View } from "@tarojs/components"; /* eslint-disable */
import React from "react";
import Ubutton from "@platformComponents/Ubutton/index";
import "./index.scss";

import EventEmitter from "@/common/utils/EventEmitter";

class WxPayMethodSelect extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            isShowPayMethodDialog: false,
        };
        const me = this;
        EventEmitter.removeListener("pay_PayDlg_toggleState");
        EventEmitter.addListener("pay_PayDlg_toggleState", (isShow) => {
            me.setState({
                isShowPayMethodDialog: isShow,
            });
        });
    }

    componentDidMount() {}
    payDlg_onClosePayMethodSelect = () => {
        EventEmitter.dispatch("pay_payDlg_onClosePayMethodSelect");
    };
    payDlg_onWxPay = () => {
        EventEmitter.dispatch("pay_payDlg_onWxPay");
    };
    render() {
        return (
            <View name="WxPayMethodSelect">
                {this.state.isShowPayMethodDialog && (
                    <View className="pay-method-modal-wx">
                        <View className="pay-method-container">
                            <View className="pay-nav-bar">
                                <View className="bar-title">
                                    请选择支付方式
                                </View>
                                <View
                                    className="g-q-iconfont pay-icon-close"
                                    onClick={this.payDlg_onClosePayMethodSelect}
                                />
                            </View>
                            <View
                                className="wechat-pay pay-method-list-item"
                                onClick={this.payDlg_onWxPay}
                            >
                                <View className="g-q-iconfont pay-icon-wxpay pay-icon" />
                                <View className="wxp-text">微信支付</View>
                            </View>
                        </View>
                    </View>
                )}
            </View>
        );
    }
}

export default WxPayMethodSelect;
