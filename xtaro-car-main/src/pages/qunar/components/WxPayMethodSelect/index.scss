/* 引用方式
 * wxml: <import src="payTpl/payMethodSelect.wxml" />
 *       <template is="payMethodSelect" data="{{isShowPayMethodDialog}}"></template>
 * wxss: @import "payTpl/payMethodSelect.wxss";
 */
 
 @import '../../style/iconfont.scss';
 
.pay-method-modal-wx {
    position:fixed;
    top:0;
    left:0;
    width:100%;
    height:100%;

    display:flex;
    flex:1;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    z-index: 10;
    background-color: rgba(0, 0, 0, 0.7);
    
    
    .pay-method-container {
        color: #212121;
        width: 315px;
        background-color: #fff;
        border-radius: 3px;
        overflow: hidden;
    }
    
    .pay-nav-bar {
        font-size: 18px;
        height: 53px;
        padding-left: 16px;
        padding-right: 14px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .pay-icon-close::before {
        content: '\f3f3';
        font-size: 23px;
        color: #9E9E9E;
    }
    
    .pay-method-list-item {
        font-size: 16px;
        padding-left: 15px;
        height: 62px;
        border-top: 0.5px solid #F7F7F7;
        display: flex;
        align-items: center;
    }
    
    .pay-icon-friend::before {
        content: '\e0b9';
    }
    
    .pay-icon-wxpay::before {
        content: '\e15f';
    }
    
    .pay-icon {
        color: #41B035;
        font-size: 24px;
        margin-right: 15px;
    }
    .stf-text {
        background-color: transparent;
        font-size: 16px;
        margin: 0;
        padding: 0;
        width: 100%;
        text-align: left;
    }
    .stf-text::after { 
        border: none; 
    }
}


