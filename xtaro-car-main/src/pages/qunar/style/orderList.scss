$bgColor: #edeff2;
page{
    margin-top:0px;
    height: 100%;
    background: #edeff2;
    overflow: hidden;
  }
  .container-order {
    position: relative;
    width: 100%;
    min-height: 100%;
    padding: 0;
    background: #edeff2;
    border-top:0.25px solid #ddd;
    display: flex;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    .headBar {
        // background-color: #edeff2;
        display: flex;
        justify-content: space-between;
        align-items: center;
        z-index: 101;
        align-items: flex-end;
        padding-bottom: 10px;
        //position: fixed;
        width: 100%;
        .headMiddleImg {
            width: 20px;
            height: 20px;
            -webkit-align-items: center;
            align-items: center;
        }
        .trip-box {
            display: flex;
            align-items: center;
            margin-left: 5px;
            .mob-trip-f{
                font-size: 14px;
                color: #222222;
                margin: 0 2px 0 2px;
            }
            .my-trip-text {
                font-size: 12px;
                color: #222222;
                font-weight: 400;
            }
        }
    }
    .all-order-box {
        position: absolute;
        left: 70px;
        right: 70px;
        .all-order{
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }
    }
  }

  .business-iconfont {
    font-size: 14px;
}

  /* common */
  .flex {
      display: -webkit-flex;
      display: flex;
      -webkit-flex-direction: row;
      flex-direction: row;
      -webkit-justify-content: space-between;
      justify-content: space-between;
  }

  .flex-start {
      display: -webkit-flex;
      display: flex;
      -webkit-flex-direction: row;
      flex-direction: row;
      -webkit-justify-content: flex-start;
      justify-content: flex-start;
  }
  .flex-between {
      display: -webkit-flex;
      display: flex;
      -webkit-flex-direction: row;
      flex-direction: row;
      -webkit-justify-content: space-between;
      justify-content: space-between;
  }
  /* header */
  // .header-holder {
  // 	width:100%;
  // 	height: 92rpx;
  // 	background-color: #edebee;
  // 	z-index: 10;
  // }
  .order-header {
    width: 95%;
    z-index: 10;
    margin: 0 auto;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 0;
    // top: 0;
    // left: 0;
    .tactive {
        color: rgb(0, 202, 216);
        border: none;
    }
  }
  .orderTip {
    line-height: 17.5px;
    font-size: 14px;
    width: 19%;
    //padding: 12rpx 0;
    text-align: center;
    // border-top: 1px solid #CCC;
    color: rgb(102, 102, 102);
    //   border-bottom: 2px solid #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    //background-color: white;
    /* width: 74.5px; */
    border-radius: 13px;
    height: 26px;
    background: #fff;
    margin-right: 8px;
  }
  .active {
    color: rgb(0, 202, 216);
    background-color: rgb(191, 244, 247);
    border-color: rgb(0, 212, 227);
    border-width: 0.5px;
    border: 0.5px solid rgb(0, 212, 227);
    .text {
        color: rgb(0, 202, 216);
    }
}

  .bar {
      background: white;
      width: 100%;
      height: 13px;
      margin-bottom: 2.5px;
  }
  /* content */
  .content {
    //   margin-top: 6rpx;
      display: flex;
      display: -webkit-flex;
      -webkit-flex-direction: column;
      flex-direction: column;
      width: 95%;
      margin:auto;
  }

  /* common order list item style */
  .listitem {
      background: white;
  }
  .scroll-container{
      background-color: #fff;
  }
  .list-item {
      //border-bottom: 1px solid #eee;
      display: flex;
      display: -webkit-flex;
      -webkit-flex-direction: column;
      flex-direction: column;
      background-color: #fff;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
  }
  .scroll-container {
    display: flex;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    flex-direction: column;
  }
  .con-header {
      background: white;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      //border-bottom: 1px solid #eee;
      width: 100%;
      padding-top: 18px;
  }
  .title {
      display: -webkit-flex;
      display: flex;
  }
  .iconfont {
      width: 18px;
      height: 18px;
      line-height: 18px;
      margin-top: 1px;
      padding: 1px;
      border-radius: 3px;
      background: #47a0e5;
      font-size:14px;
      color:#fff;
      text-align:center;
  }
  .ordertype {
      margin-left: 15px;
  }
  .ordertypetxt {
      text-align: left;
      font-weight: bold;
      font-size: 16px;
      line-height: 20px;
      margin-left: 7.5px;
      color: #333;
      -webkit-align-items: center;
      align-items: center;
      white-space: nowrap;
  }
  .paytype {
      margin-right: 15px;
      text-align: right;
      font-size: 12px;
      color: #00bcd4;
      margin-top: 1.5px;
      vertical-align: middle;
      height: 20px;
      line-height: 22px;
  }
  .listseparate {
      background-color: $bgColor;
      height: 10px;
  }

  /* multi flight */
  .orderi {
      padding-bottom: 18px;
      // width: 65%;
      display: flex;
      display: -webkit-flex;
      -webkit-flex-direction: column;
      flex-direction: column;
  }
  .orderi0 {
      border-top: none;
      width: 100%;
  }

  .fr-to {
      color: #333;
      font-size: 16px;
      padding: 13px 0 10px 0;
  }
  .turnto {
      width: 40px;
      height: 10px;
      margin-left: 5px;
      -webkit-align-items: center;
      align-items: center;
      margin-top: 7.5px;
  }
  .turn {
    width: 12.5px;
    height: 10px;
    margin-left: 2.5px;
    margin-right: 7.5px;
    margin-bottom: 2.5px;
    -webkit-align-items: center;
    align-items: center;
}
  .arrow-right {
      color: #ccc;
      margin-left: 10px;
      margin-right: 10px
  }

  .ft-info {
      display: flex;
      width: 264px;
      line-height: 1.5;
      margin-left: 15px;
  }
  .fr-to .price {
      text-align: right;
      margin-right: 15px;
      line-height: 1.5;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .pay-way {
          font-size: 10px;
          color: #666666;
      }

  }
  .time-range {
    display: flex;
    font-size: 12px;
    margin-left: 15px;
    line-height: 17px;
    color: #666;
    font-family: Helvetica Neue, Helvetica, STHeiTi, sans-serif;

    .range-color {
        font-size: 12px;
        color: #666;
    }
    .rangeday {
        display: inline-block;
        color: rgb(242,87,23);
        font-size: 12px;
        margin-left: 5px;
    }
  }

  .noList {
    margin-top: 5px;
    margin-left: 15px;
    font-size: 12px;
    color: #999;
    .text {
        font-size: 12px;
        color: #999;
    }
    .noItem {
        color: #999;
        font-size: 12px;
    }
  }

  .deadline-time {
    font-size: 12px;
    margin-left: 15px;
    color: #666;
    font-family: Helvetica Neue, Helvetica, STHeiTi, sans-serif;
  }

  .total-time {
    margin-top: 5px;
    margin-left: 15px;
    font-size: 12px;
    color: #999;
  }

  .special-price::before {
      content: ""
  }
  .hideno {
      display: none;
  }
  .noItem::before {
      content: '/';
      display: inline-block;
  }
  .noList .no0::before {
      content: '';
  }

  /* order action */
  .list-action {
      display: flex;
      justify-content: flex-end;
      background-color: #fff;
      padding: 5px 15px 10px;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
  }
  .list-action-content {
    display: flex;
    justify-content: flex-end;
    flex: 1;
    // padding: 10rpx 0 24rpx;
    // border-top: 2rpx solid #F2F2F2;
  }
  .topay {
    background: white;
    font-size: 12px;
    color: rgb(102, 102, 102);
    border: 1px solid rgb(231, 231, 231);
    margin-left: 10px;
    border-radius: 99px;
    height: 28px;
    padding-left: 12px;
    padding-right: 12px;
    display: flex;
    align-items: center;
  }
  .topay.showbtn {
      padding: 6px 10px;
      margin: 10px 0;
      border: 1px solid #ff4400;
      line-height: 1;
      margin-right: 7px;
  }
// //   快应用不能上面那么些
//   .tsb {
//     box-sizing: border-box;
//     height: 52rpx;
//     padding: 12rpx 20rpx;
//     margin: 20rpx 0;
//     border: 1px solid #ff4400;
//     line-height: 52rpx;
//     margin-right: 24rpx;
// }
.tsb-qq {
    display: flex;
    justify-content: center;
    align-items: center;
}
  .ismr {
      margin-right:0px;
  }
  .show {
    //   position: absolute;
      z-index: 1;
      padding: 30px 0;
      text-align: center;
      width: 100%;
      background-color: #ffffff;
  }

  .noorders {
      width: 133px;
      height: 93px;
      display: block;
      margin: auto;
  }
  .noorderstxt {
      width: 100%;
      font-size: 13px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgb(153, 153, 153);
      margin-top: 10px;
      font-family: Helvetica Neue, Helvetica, STHeiTi, sans-serif;
      .gray-text{
        color: rgb(153, 153, 153);
        font-size: 13px;
      }
      .serach-by-phone{
          font-size: 13px;
          color: #00bcd4;
      }
  }
  .hasmore {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #00bcd4;
      font-size: 13px;
      text-align: center;
      padding-bottom: 10px;
      background: $bgColor;
  }
  .hasmore-phone {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9E9E9E;
    font-size: 13px;
    text-align: center;
    padding-bottom: 10px;
    background: $bgColor;
    .gray-text{
        color: #9E9E9E;
        font-size: 13px;
    }
}
.serach-by-phone{
    color: #00bcd4;
    margin-left: 4px;
}

  /* refresh */
  .show-dr {
      color: #00bcd4;
      background: #edebee;
      padding-bottom: 5px;
      width: 100%;
      height: 20px;
      font-size: 15px;
      -webkit-justify-content: center;
      justify-content: center;
      display: flex;
      display: -webkit-flex;
  }
  .show-dr.down-r image {
      align-items: center;
      height: 15px;
      width: 15px;
      color: #00bcd4;
  }
  .hide {
      display: none;
  }
  /* network */
  .network {
      height:100%;
      width: 100%;
      position: fixed;
      background: #fff;
      top:0;
      left: 0;
      text-align: center;
  }



  .error-image {
      width:97px;
      height:55px;
      margin:50px auto 30px auto;
      display: block;
      clear: both;
  }

  .error-text {
      font-size:20px;
      color:#000000;
  }

  .error-button {
      background-color: #fbfafc;
      margin:32px 15px;
  }

  .error-button-hover {
      background-color: #dfdfdf;
  }

  .tips {
      display: flex;
      justify-content: center;
      align-items: center;
      background:#edeff2;
      color:#98989a;
      font-size:13px;
      text-align:center;
      height:27px;
      margin-bottom: 5px;
      /* line-height:54rpx; */
  }
  .tips-local{
      box-sizing: border-box;
      padding: 0 15px;
      font-size: 12px;
      background-color: #FFF9D9;
      justify-content: space-between;
      text{
        color:#FF7043;
        font-size: 12px;
      }
      // 快应用居中不了
      .tips-local-text{
          padding-bottom: 1px;
      }
      .arrow{
          margin-left: 5px;
      }
  }

  .nodata-wrapper {
      display: flex;
      flex-direction: column;

      .img-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
      }
  }

  .tips .tips-icon {
      color:#bdcad3;
      font-size:20px;
      margin-right: 5px;
  }

//   .tips .tips-icon:before {
//       content: '\e0ca';
//   }

  // .tips .chaik-icon::before {
  //   content: '\f3f4';
  // }