// 全局样式 待更新
.g-text-center {
    text-align: center;
}

.anu-text-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
}

.g-flexbox {
    display: flex;
    flex-direction: row;
}

.g-touchable-opacity {
    opacity: .7;
}

.g-hide {
    display: none;
}

.g-text-left {
    text-align: left;
}

.g-flex-direction-column {
    flex-direction: column;
    >.g-flex {
        height: 0.05px;
    }
}

.g-fullscreen {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.g-align-items-center {
    align-items: center;
}

.g-align-items-end {
    align-items: flex-end;
}

.g-justify-content-center {
    justify-content: center;
}
.g-justify-content-end {
    justify-content: flex-end;
}
.g-justify-content-around {
    justify-content: space-around;
}

.g-justify-content-between {
    justify-content: space-between;
}

.g-ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.g-ellipsis-more {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.g-img {
    width: 100%;
    height: 100%;
}

.g-text-right {
    text-align: right;
}

.g-flex-direction-column {
    flex-direction: column;
}

.g-flex {
    flex: 1;
}

.g-flex-wrap {
    flex-wrap: wrap;
}

.g-align-items-end {
    align-items: end;
}

.loading {
    height: 100%;
    width: 100%;
    background-color: #ffffff;
    z-index: 1000;
    position: fixed; 
}
// .loading__img {
//     width: 160rpx!important;
//     height: 160rpx;
//     position: absolute;
//     top: 50%;
//     left: 50%;
//     margin: -80rpx auto auto -80rpx; 
// }
.rewrite-btn {
    // position: static;
    display: block;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 0;
    padding-right: 0;
    font-size: 12px;
    text-align: left;
    text-decoration: none;
    line-height: 1.4;
    border-radius: 0px;
	outline: none;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
    color: #333;
    background-color: #fff;
    border: none;
}
.rewrite-btn::after{
    display: none;
}

