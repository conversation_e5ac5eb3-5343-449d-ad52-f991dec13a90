import React from 'react';
import CPage, { IStateType } from '../../../xcar/Components/App/CPage';
import { IBasePageProps } from '@c2x/components/Page';
import { xRedirectTo } from '@ctrip/xtaro';
import { Constant } from '../../../xcar/Diff/constants';
interface PropsType extends IBasePageProps {}

interface StateType extends IStateType {}

class Home extends CPage<PropsType, StateType> {
  /* eslint-disable class-methods-use-this */
  onLoad(options?: any): void {
    let homeUrl = Constant.HOME_URL;
    if (Object.keys(options || {}).length > 0) {
      homeUrl += '?';
      for (let key in options) {
        const val = options[key];
        homeUrl += `${key}=${encodeURIComponent(typeof val === 'object' ? JSON.stringify(val) : val)}&`;
        homeUrl.slice(0, -1); // 去除末尾&
      }
    }
    xRedirectTo({ url: homeUrl });
  }
  render() {
    return <></>;
  }
}

export default Home;
