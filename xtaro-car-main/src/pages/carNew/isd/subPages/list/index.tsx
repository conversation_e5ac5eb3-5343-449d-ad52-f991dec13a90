import React from 'react';
import CPage, { IStateType } from '../../../../xcar/Components/App/CPage';
import { IBasePageProps } from '@c2x/components/Page';
import { xRedirectTo } from '@ctrip/xtaro';
import { Constant } from '../../../../xcar/Diff/constants';
interface PropsType extends IBasePageProps {}

interface StateType extends IStateType {}

class List extends CPage<PropsType, StateType> {
  /* eslint-disable class-methods-use-this */
  onLoad(options?: any): void {
    let listUrl = Constant.LIST_URL;
    if (Object.keys(options || {}).length > 0) {
      listUrl += '?';
      for (let key in options) {
        const val = options[key];
        listUrl += `${key}=${encodeURIComponent(typeof val === 'object' ? JSON.stringify(val) : val)}&`;
        listUrl.slice(0, -1); // 去除末尾&
      }
    }
    xRedirectTo({ url: listUrl });
  }
  render() {
    return <></>;
  }
}

export default List;
