# 自动化文档

[自动化测试二期计划](http://conf.ctripcorp.com/pages/viewpage.action?pageId=1237365603)

# 测试用例规范

[Test Case 录入规范](http://conf.ctripcorp.com/pages/viewpage.action?pageId=1498990942)

[Test Case 验收规范]()

[测试计划](https://testhub.package.ctripcorp.com/library/336/plan/2770)

[用例库](	https://testhub.package.ctripcorp.com/library/336?pageIndex=1&pageSize=20)

# 测试用例编写

目录： ```/__test__/Pages/```

示例:  ```/__test__/Pages/Home.test.tsx```

```
test(createInterTestName({
  testId: 112260, // - testhub 对应用例ID
  name: '送车上门选项', - testhub 对应用例标题
}), async () => {
  // 配置融合首页标识
  AppContext.setIsHomeCombine(true);
  // Apptype ISD
  jest.spyOn(Utils, 'isCtripIsd').mockReturnValue(true);
  const Page = await renderPage(); // 获取渲染DOM实例，实例存在若干方法用于查询元素

  // 112260 验证送车上门选项是否存在
  const pickUpDoor = await Page.getByTestId(UITestID.car_testid_page_home_pickup_ondoor);
  expect(pickUpDoor).toBeTruthy();
});

```


## 元素查找

- [**基础 DOM 查询 API**](https://testing-library.com/docs/guide-which-query)
  - API 前缀
    - 查询单个元素
      - `getBy`
      - `findBy`：异步化（Promise）
      - `queryBy`
    - 查询多个元素(返回数组)
      - `getAllBy`
      - `findAllBy`：异步化（Promise）
      - `queryAllBy`
  - API 后缀
    - 主要
      - `ByText`：查询 `TextNode`
      - `ByLabelText`：用于表单
      - `ByPlaceholderText`：用于表单
      - `ByDisplayValue`：输入框等当前值
    - 显式测试标签
      - `ByTestId`：查找 `testID` 属性
- **`fireEvent`**（触发事件）
  - `fireEvent(element, 'click')`
  - `fireEvent.press(element, options?)`
  - `fireEvent.changeText(element, options?)`
  - `fireEvent.scroll(element, options?)`

- **查找文案：toHaveTextContent**

  例：expect(Page.container).toHaveTextContent('用户评价4.9超棒');

### 要基于用户视角编写 Case，查找元素方法的使用优先级如下图：

<img src='imgs/TA-priority.png' width=600 />

### 各查找函数区别

<img src='imgs/TA-query-api.png' width=600 />



## 生成页面渲染结构

```
expect(Page.toJSON()).toMatchSnapshot();
```

## 本地测试

```sh
npm run test:watch __test__/Pages/Home.test.tsx
```

## 添加 Mock 文件

地址：http://git.dev.sh.ctripcorp.com/-/ide/project/faas/car-mock-server-function/tree/master/-/data/restful/

可在线编辑，编辑完成提交 commit，job运行完成即可自动发
布

mock 文件命名使用 testhub 用例ID，在上面编写测试用例中会标注测试用例，test 执行会默认获取对应 ID 的 mock 数据

## 触发自动化测试

在 Tripal 自动化测试群中触发，找 @cay 进群，发送消息触发测试执行

@度假前端小助手 testhub update plan rn_car_main 分支dev/zxy-ta id:2770

> @度假前端小助手 testhub update plan [仓库名] 分支[git分支名] id:[测试计划ID]


# 问题汇总

一、**注意如果页面组件渲染报错，可排查是否是因为循环依赖导致页面无法渲染，或者是因为 CRN 组件引入但没有 Mock 导致渲染失败**

二、**TextInput 使用 `fireEvent.changeText(searchInput, '上海');` 没有触发 `onChange`事件**

> 代码兼容解决

```js
const onChangeText = useMemoizedFn((value: string) => {
  setKeyword(value);

  // TA: fireEvent.changeText 改变value时不会触发onChange事件，这里需要手动触发
  // @ts-ignore
  // eslint-disable-next-line no-underscore-dangle
  if (global.__TESTING_AUTOMATION__) {
    onChange(value);
  }
});
```
