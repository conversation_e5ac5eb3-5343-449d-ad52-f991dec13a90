image: hub.cloud.ctripcorp.com/mini.pipeline/mini_taro_standalone_pipeline_3_5_12:latest
stages:
  - Install

.node-variables: &node-variables
  NODE_OPTIONS: "--max-old-space-size=4096"

.proxy-variables: &proxy-variables
  PROXY: http://proxygate2.ctripcorp.com:8080
  HTTP_PROXY: $PROXY
  HTTPS_PROXY: $PROXY
  NO_PROXY: "*.ctripcorp.com"

variables:
  GIT_SUBMODULE_STRATEGY: normal
git:
  stage: Install
  variables:
    <<: *node-variables
    <<: *proxy-variables
  before_script:
    - git submodule update --remote

  script:
    #    - cd /tmp/tarobaseproject/ && ls
    - mkdir ../outputs
    - echo $BUNDLE_NAME
    - echo $MCD_APP_ID
    - echo $MINI_TYPE
    - if [ -z $BUNDLE_NAME ] ; then echo "BUNDLE_NAME IS NOT EXISTS" && exit 1 ; fi
    - if [ -z $MCD_APP_ID ] ; then echo "MCD_APP_ID IS NOT EXISTS" && exit 1 ; fi
    - if [ -z $MINI_TYPE ] ; then export MINI_TYPE="weapp" ; fi
    - LATEST_ARGS=""
    - echo $FORCE_LATEST
    - if [ ! -z $FORCE_LATEST ] ; then LATEST_ARGS="--forceLatest" ; else echo "Do not force the latest" ; fi
    - PIPE_ARGS="--isPipeline"
    - echo $SIGNATURE_KEY
    - if [ ! -z $SIGNATURE_KEY ] ; then PIPE_ARGS="--isMCDPipeline" ; fi
    - echo $CODE_BRANCH
    - echo $MCD_BUILD_ID
    - node --version
    - npm install @ctrip/miniTools@1.0.307-test.1 -g
    - miniTools --version
    - taroTools --version
    - if [ $BUNDLE_NAME = "main" ] && [ ! -z $CODE_BRANCH ] ; then git checkout $CODE_BRANCH && git pull ; fi
    - taroTools  $PIPE_ARGS --isStandalone --project ./ --miniType $MINI_TYPE --mcdAppId $MCD_APP_ID --bundleName $BUNDLE_NAME --branch $CODE_BRANCH --commitHash $COMMIT_HASH --pipelineId $CI_JOB_ID --IMData "$IM_CALLBACKDATA" --payload $TRIGGER_PAYLOAD $LATEST_ARGS  --mcdBuildId $MCD_BUILD_ID --signatureKey $SIGNATURE_KEY
    - if [ ! -z $SIGNATURE_KEY ] ; then exit 0 ; else echo "continue to do preview-pipeline" ; fi
    - miniTools tPipeline --preview --projectPath ./ --outputDir ../outputs --miniProgramType $MINI_TYPE --mcdAppId $MCD_APP_ID --bundleName $BUNDLE_NAME --branch $CODE_BRANCH --commitHash $COMMIT_HASH --pipelineId $CI_JOB_ID --IMData "$IM_CALLBACKDATA" --payload $TRIGGER_PAYLOAD $LATEST_ARGS --previewPath $PREVIEW_PATH 
    - cd ../outputs
    - if [ -f pipeline_failed.json ] ; then exit 1 ; else echo "pipeline_failed not exists" ; fi
    - if [ -f git_commits.json ] ; then curl --request POST --header "PRIVATE-TOKEN:$PRIVATE_KEY" --header "Content-Type:application/json" --data @git_commits.json http://git.dev.sh.ctripcorp.com/api/v4/projects/$CI_PROJECT_ID/repository/commits ; fi
  only:
    - master
