{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "setting": {"urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": false, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "useStaticServer": true, "showES6CompileOption": false, "checkInvalidKey": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": false}, "compileType": "miniprogram", "libVersion": "3.0.1", "appid": "wxf03cf55ff5945ba1", "projectname": "WechatApp", "scripts": {"beforeCompile": "npm run compile", "beforePreview": "npm run compile", "beforeUpload": "npm run compile"}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "skeleton-config": {"global": {"loading": "shine", "text": {"color": "#EEEEEE"}, "image": {"shape": "", "color": "#EFEFEF", "shapeOpposite": []}, "button": {"color": "#EFEFEF", "excludes": []}, "pseudo": {"color": "#EFEFEF", "shape": "react", "shapeOpposite": []}, "excludes": ["navbar", ".slogan-container"], "remove": [], "empty": [], "hide": [], "grayBlock": [], "showNative": false, "backgroundColor": "transparent", "mode": "fullscreen", "templateName": "skeleton", "cssUnit": "rpx", "decimal": 4}, "pages": {"pages/bus/list/list": {"mode": "auto", "excludes": ["nav-bar", ".bus-listdate"], "remove": [".tag-back-invert"]}, "pages/bus/book/book": {"mode": "auto"}, "pages/bus/orderdetail/orderdetail": {"mode": "auto"}}}, "condition": {"miniprogram": {"list": [{"name": "首页", "pathName": "pages/xcar/entry/homemini/index"}, {"name": "列表页", "pathName": "pages/xcar/entry/list/index"}]}}, "packOptions": {"ignore": [], "include": [{"value": "commonAPI", "type": "folder"}, {"value": "commonComponent", "type": "folder"}, {"value": "functional-pages", "type": "folder"}, {"value": "taroBase", "type": "folder"}]}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}