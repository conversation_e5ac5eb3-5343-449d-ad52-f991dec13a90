
        let miniBuildVersion = 'taro_1733299541171_0';
        let miniBuildId = 'undefined';
        try {
            if (typeof wx !== 'undefined') {
                wx['buildVersion'] = miniBuildVersion;
                wx['buildId'] = miniBuildId;
            }
        } catch (e) {
        }
        module.exports = {
            miniBuildVersion: miniBuildVersion,
            miniBuildId: miniBuildId
        };
    