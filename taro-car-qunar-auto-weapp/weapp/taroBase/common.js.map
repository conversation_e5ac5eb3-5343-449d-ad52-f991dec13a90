{"version": 3, "file": "common.js", "mappings": "AAAA,cACCA,GAAG,mBAAqBA,GAAG,oBAAsB,IAAIC,KAAK,CAAC,CAAC,MAAM,CAE7D,MACA,SAAUC,EAAyBC,EAAqBC,GAEzCA,EAAoBC,EAAEF,EAAqB,CACzC,EAAK,WAAa,OAAqBG,CAAU,IAEnD,IAAIC,EAAkIH,EAAoB,KACtJI,EAA4IJ,EAAoB,OAChKK,EAAkIL,EAAoB,OACtJM,EAAgIN,EAAoB,OACpJO,EAA6HP,EAAoB,OACjJQ,EAA6HR,EAAoB,OACjJS,EAAmIT,EAAoB,OACvJU,EAA0HV,EAAoB,OAC9IW,EAAqCX,EAAoB,OACzDY,EAAgDZ,EAAoB,OACpEa,EAAqEb,EAAoBc,EAAEF,GAC3FG,EAAgEf,EAAoB,OAEpFgB,EAAuDhB,EAAoB,OAC3EiB,EAA4EjB,EAAoBc,EAAEE,GAClGE,EAA4ClB,EAAoB,OAChEmB,EAAiEnB,EAAoBc,EAAEI,GACvFE,EAAqEpB,EAAoB,OAEzFqB,EAAiErB,EAAoB,OACrFsB,EAAsFtB,EAAoBc,EAAEO,GACvGE,EAAWvB,EAAoB,OAAO,YAShEwB,EAAY,CAAC,SAAU,WAAY,YACrCC,EAAa,CAAC,YAQZC,EAAa,EACjB,SAASC,EAAkBC,GACzB,IAAIC,EAAM,GACV,IAAK,IAAIC,KAAKF,EACZ,GAAU,gBAANE,EAAqB,CACvB,IAAIC,GAAI,EAAGrB,EAA0IsB,GAAGJ,EAAIE,IAClJ,WAANC,GAAwB,WAANA,GAAwB,YAANA,GACtCF,EAAIhC,KAAKoC,mBAAmBH,GAAK,IAAMG,mBAAmBL,EAAIE,IAElE,CAEF,OAAOD,EAAIK,OAAS,EAAI,IAAML,EAAIM,KAAK,KAAO,EAChD,CACA,IAAIC,EAAO,WACT,IAAIP,EAAM,GACNQ,EAAcpB,IAAyE,YAAK,CAAC,EAUjG,OATIoB,EAAWC,QAAUD,EAAWC,OAAOC,KACzCV,EAAMQ,EAAWC,OAAOC,KAAKC,KAAI,SAAUC,GACzC,OAAOA,EAAKC,QACd,IAEKzB,IAAqE,SACxEY,EAAOZ,IAAqE,QAGzEY,CACT,CAbW,GAcX,SAASc,EAAWP,EAAMQ,GAExB,IADA,IAAIC,GAAS,EACJC,EAAI,EAAGA,EAAIV,EAAKF,OAAQY,IAAK,CACpC,IAAIC,EAAIX,EAAKU,GACb,IAAyB,GAArBC,EAAEC,QAAQJ,GAAc,CAC1BC,EAAQC,EACR,KACF,CACF,CACA,OAAOD,CACT,CACA,IAAII,EAAO,WAAiB,EAExBC,EAAe,CAAC,SAEpB,mBAEA,UAEA,mBAEA,WAEA,wBAEIC,EAAkB,CAAC,oBAAqB,mBACxCC,EAAgB,CAElBC,WAAY,CAAC,aAAc,kBAAmB,iBAAkB,gBAAiB,oBAEjFC,iBAAkB,CAAC,oBAEnBC,QAAS,CAAC,6BAERC,EAAe,CACjBC,IAAK,CAAC,YAAa,YAAa,WAAY,YAAa,cAAe,cAAe,gBAAiB,SAAU,oBAAqB,SAAU,aAAc,eAAgB,kBAC/KC,IAAK,CAAC,eACNH,QAAS,CAAC,yBAKRrD,EAAwB,SAAUyD,GACpC,SAASzD,EAAS0D,GAChB,IAAIC,EAyBJ,OAxBA,EAAGxD,EAAkJ2B,GAAG8B,KAAM5D,GAC9J2D,GAAS,EAAGtD,EAA6IyB,GAAG8B,KAAM5D,EAAU,CAAC0D,KAG7K,EAAGnD,EAAmJuB,GAAG6B,EAAQ,YAAQ,IAEzK,EAAGpD,EAAmJuB,GAAG6B,EAAQ,cAAU,IAC3K,EAAGpD,EAAmJuB,GAAG6B,EAAQ,mBAAmB,WAClLhD,IAAwDkD,QAAQ,yBAA2BlD,IAAwDmD,qBAAqBH,EAAOI,gBACjL,IACAJ,EAAOK,UAAW,EAClBL,EAAOM,IAAOtD,IACdgD,EAAOO,aAAe,CAAC,EACvBP,EAAOQ,YACPR,EAAOS,QAAUV,GAASA,EAAMW,KAAOX,EAAMW,IAAIC,MAAM,KAAK,GAGxDX,EAAOY,0BAA4BZ,EAAOS,SAE5CT,EAAOa,aAAa,eAEtBb,EAAOc,gBACPd,EAAOe,0BACPf,EAAOgB,sBACAhB,CACT,CAEA,OADA,EAAGrD,EAA6IwB,GAAG9B,EAAUyD,IACtJ,EAAGrD,EAAgJ0B,GAAG9B,EAAU,CAAC,CACtK4E,IAAK,sBACLC,MAAO,WAEL,GAAKlE,IAAwDkD,QAAQ,oBAArE,CAGA,IAAIiB,EAAW7D,IAAoD8D,qBAC/DC,EAAOF,GAAYA,EAASG,QAAUH,EAASG,OAAOC,QAAUJ,EAASG,OAAOC,OAAOC,gBAAkB,GAI7G,GAHKH,IACHA,EAAOF,GAAYA,EAASM,MAAQN,EAASM,KAAKC,SAAWP,EAASM,KAAKC,QAAQF,gBAAkB,IAElGH,EAAL,CAGAM,QAAQC,IAAI,iCAAkC,cAAeP,GAC7D,IAAIQ,EAAa7E,IAAwD8E,oBAAoBT,GAC7FM,QAAQC,IAAI,uCAAwC,cAAeC,GAC/DA,IAEEV,GAAYA,EAASG,QAAUH,EAASG,OAAOC,QACjDQ,OAAOC,OAAOb,EAASG,OAAOC,OAAQM,GAEpCV,GAAYA,EAASM,MAAQN,EAASM,KAAKC,SAC7CK,OAAOC,OAAOb,EAASM,KAAKC,QAASG,GAVzC,CARA,CAqBF,GACC,CACDZ,IAAK,UACLC,MAAO,YACL,EAAG3D,EAAmE0E,SAAShC,KACjF,GACC,CACDgB,IAAK,gBACLC,MAAO,WACL,IAAIgB,EAASjC,KACbZ,EAAa8C,SAAQ,SAAUC,GAC7B,IAAIC,EAAQH,EAAOE,IAAWhD,EAC1BkD,EAAQ,SAAeC,GACzB,GAAIvF,IAAwDkD,QAAQ,oBAAqB,CACvF,GAAe,WAAXkC,IAIGpF,IAAwDwF,iBAAiB,CAC5EC,WAAYP,EAAOO,WACnB5D,SAAUqD,EAAOtB,wBACjB8B,UAAWH,IACT,CACF,IAAIvF,IAAwDkD,QAAQ,qCAAsClD,IAAwD2F,kCAOhK,OAFAT,EAAOU,eAAgB,OACvBV,EAAO,IAAME,GAAQS,KAAKX,EAAQK,GAL9BL,EAAOY,SACTZ,EAAOY,OAAOC,sBAAuB,EAO3C,CAIF,GAAIb,EAAOU,cAET,YADAV,EAAO,IAAME,GAAQS,KAAKX,EAAQK,EAGtC,CACAL,EAAO,IAAME,GAAQS,KAAKX,EAAQK,GAE9BvF,IAAwDkD,QAAQ,oBAAsBgC,EAAOc,iBAAmBhG,IAAwDiG,mBAG5KZ,EAAMQ,KAAKX,EAAQK,EACrB,EACAL,EAAOE,GAAUE,EAAMY,KAAKhB,EAC9B,GACF,GAGC,CACDjB,IAAK,0BACLC,MAAO,WACL,IAAIiC,EAASlD,KACbX,EAAgB6C,SAAQ,SAAUC,GAChCT,QAAQC,IAAI,kDAAkD,EAAG/E,EAA0IsB,GAAGgF,EAAOf,KAErN,IAAIC,EAAQ,KACVe,EAAS,GAOX,GAN8B,oBAAnBD,EAAOf,GAChBC,EAAQc,EAAOf,GACNiB,MAAMC,QAAQH,EAAOf,MAC9BC,EAAQc,EAAOf,GAAQ,GACvBgB,EAASD,EAAOf,GAAQmB,MAAM,IAE5BlB,EAAO,CACT,IAAIC,EAAQ,SAAeC,GACzBZ,QAAQC,IAAI,oCAAqCQ,GACjDT,QAAQC,IAAI,kCAAmCW,GAC/C,IAAIiB,EAAMnB,EAAMQ,KAAKM,EAAQZ,GAC7BZ,QAAQC,IAAI,iCAAkC4B,GAC9C,IAAIC,EAAeN,EAAO,IAAIO,OAAOtB,IAASS,KAAKM,EAAQZ,EAAMiB,GACjE,OAAOC,CACT,EACAnB,EAAMqB,kBAAmB,EACzBR,EAAOf,GAAU,CAACE,GAAOoB,OAAON,EAClC,CACF,GACF,GACC,CACDnC,IAAK,wBACLC,MAAO,SAA+BO,EAAMmC,GAC1C,GAAK5G,IAA4E,qBAC/E,OAAOA,IAAwD6G,qBAAqBpC,EAAMmC,GAG5F,IAAIE,EAAcrC,GAAQzE,IAAwD+G,iBAC9EhF,EAAQ+E,IAAgBA,EAAYE,WAAaF,EAAY/E,QAAU,GAS3E,OARIA,GAAS6E,IAEX7E,EAAQA,EAAM4B,MAAM,KAAK,GAEE,IAAvB5B,EAAMI,QAAQ,OAChBJ,EAAQA,EAAMwE,MAAM,KAGjBxE,CACT,GACC,CACDkC,IAAK,mBACLC,MAAO,SAA0BqB,GAC/B,IAAI0B,EAAYC,UAAU7F,OAAS,QAAsB8F,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAMrF,OALAvC,QAAQC,IAAI,sCAAmBqC,GAC3BA,GAAkC,mBAAdA,GAAqH,oBAAlFjH,IAA4E,uBACrIiH,EAAYjH,IAAwDoH,qBAAqBnE,KAAKoE,gBAEhG1C,QAAQC,IAAI,sCAAmBqC,GACxBA,CACT,GACC,CACDhD,IAAK,qBACLC,MAAO,SAA4BqB,GACjC,IAAI0B,EAAYC,UAAU7F,OAAS,QAAsB8F,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAOrF,OANAvC,QAAQC,IAAI,iCAAcW,GAC1BZ,QAAQC,IAAI,sCAAmBqC,GAC1BjH,IAAwE,mBAC3EiH,EAAYjH,IAAwDsH,iBAAiB/B,EAAM0B,IAE7FtC,QAAQC,IAAI,sCAAmBqC,GACxBA,CACT,GACC,CACDhD,IAAK,qBACLC,MAAO,SAA4BqD,GACjC,IAAIC,EAASvE,KACTwE,EAAU9E,EAAa4E,IAAS,GACpCE,EAAQtC,SAAQ,SAAUvD,GACxB4F,EAAO1B,OAAOlE,GAAQ4F,EAAO5F,GAAMsE,KAAKsB,EAC1C,GACF,GACC,CACDvD,IAAK,oBACLC,MAAO,SAA2BkB,GAChC,IAAIsC,EAAQzE,KACZ,OAAO,SAAsB0E,GAC3B,GAA6B,oBAAlBD,EAAMtC,GAAwB,CAEvC,IACEpF,IAAwD4H,iBAAiBD,EAAGvC,EAAQsC,EACtF,CAAE,MAAOC,GAAI,CACbD,EAAMtC,GAAQyC,MAAMH,EAAOR,UAC7B,CACF,CACF,GACC,CACDjD,IAAK,YACLC,MAAO,SAAmBQ,GAKxBA,EAAUA,GAAW,CAAC,EACtB,IAAIoD,EAAWpD,EACbqD,EAASD,EAASC,OAClBC,EAAWF,EAASE,SACpBC,EAAWH,EAASG,SACpBC,GAAQ,EAAG3I,EAA4J4B,GAAG2G,EAAUnH,GAClLsC,KAAKkF,OAASlF,KAAKmF,OACrBnF,KAAKoF,cAAc3D,IAEnBzB,KAAKmF,OAASL,EAEd9E,KAAKkF,KAAO1H,IAAyE6H,SAAS,CAC5FP,OAAQA,EACRC,SAAUA,EACVC,UAAU,EAAG3I,EAAkJ6B,IAAG,EAAG7B,EAAkJ6B,IAAG,EAAG7B,EAAkJ6B,GAAG,CAAC,EAAG8G,GAAWC,GAAQ,CAAC,EAAG,CAC3eK,cAAe,YAIvB,GACC,CACDtE,IAAK,YACLC,MAAO,SAAmBsE,GACxB/H,IAAyEgI,KAAK,CAC5ExE,IAAK,SACLsD,KAAM,SACNiB,KAAMA,GACLvF,KAAKkF,KACV,GACC,CACDlE,IAAK,WACLC,MAAO,SAAkBwE,EAAMxE,EAAOyE,GACpC,IAAIC,EAAa1E,EAEjB,GAAkB,OAAZyE,QAAgC,IAAZA,IAAsBA,EAAQE,MACtD,OAAQ7I,IAAwD8I,KAAKvB,KAAKrD,IACxE,IAAK,SACL,IAAK,SACH0E,EAAa1E,EACb,MACF,QACE0E,EAAaG,KAAKC,UAAU9E,GAC5B,MAGNzD,IAAyEgI,KAAK,CAC5ElB,KAAM,QACNtD,IAAKyE,EACLF,KAAMI,GACL3F,KAAKkF,KACV,GACC,CACDlE,IAAK,cACLC,MAAO,SAAqBwE,EAAMxE,GAChCzD,IAAyEgI,KAAK,CAC5ElB,KAAM,YACNtD,IAAKyE,EACLF,KAAMtE,GACLjB,KAAKkF,KACV,GACC,CACDlE,IAAK,YACLC,MAAO,SAAmB+E,GACxBA,EAASA,GAAU,CAAC,EACpBxI,IAAyEgI,KAAK,CAC5ElB,KAAM,SACNtD,IAAKgF,EAAOP,KACZF,KAAM,CACJU,IAAKD,EAAOC,IACZhF,MAAO+E,EAAO/E,QAEfjB,KAAKkF,KACV,GACC,CACDlE,IAAK,gBACLC,MAAO,SAAuBQ,GAC5BjE,IAAyEgI,KAAK,CAC5ElB,KAAM,QACNiB,KAAM9D,GACLzB,KAAKkF,KACV,GACC,CACDlE,IAAK,cACLC,MAAO,SAAqBwE,EAAMxE,EAAOyE,GACvC,IAAIQ,EAAOjF,GAAS,CAAC,EAEnBkF,GADWD,EAAKE,UACT,EAAG9J,EAA4J4B,GAAGgI,EAAMvI,IACjLwI,EAAKE,MAAStJ,IAA6D,OAAK,GAChF,IAAIwI,EAAOY,EAELT,GAAWA,EAAQE,QACvBL,EAAOO,KAAKC,UAAUI,IAExB3I,IAAyEgI,KAAK,CAC5ElB,KAAM,WACNtD,IAAKyE,EACLF,KAAMA,GACLvF,KAAKkF,KACV,GAOC,CACDlE,IAAK,SACLC,MAAO,SAAgBwE,EAAMa,GAC3B,OAAO9I,IAAyE+I,IAAI,CAClFd,KAAMA,EACNe,cAAeF,GACdtG,KAAKkF,KACV,GAOC,CACDlE,IAAK,SACLC,MAAO,SAAgBwE,EAAMxE,GAG3BjB,KAAKoF,eAAc,EAAGzI,EAAmJuB,GAAG,CAAC,EAAGuH,EAAMxE,GACxL,GAOC,CACDD,IAAK,gBACLC,MAAO,SAAuBQ,GAC5B,IACE,GAAgK,WAA5J,EAAG7E,EAA0IsB,GAAGuD,GAAsB,OAI1K,MAH6B,oBAAlBA,EAAQqD,SACjB9E,KAAKmF,OAAS1D,EAAQqD,QAEjBtH,IAAyEiJ,KAAI,EAAGpK,EAAkJ6B,IAAG,EAAG7B,EAAkJ6B,GAAG,CAAC,EAAGuD,GAAU,CAAC,EAAG,CACpZiF,QAASjF,EAAQkF,QACjBC,OAAQnF,EAAQ,UAAY,EAAI,IAC9BzB,KAAKkF,KACX,CAAE,MAAO2B,GACPnF,QAAQmF,MAAM,iBAAkBA,EAClC,CACF,GACC,CACD7F,IAAK,eACLC,MAAO,SAAsBqD,GAM3B,IAAItE,KAAK8G,YAAT,CAGA,IAAIC,EAAW1J,IAAoD2J,kBAC/DC,EAAejH,KAAKW,wBACxBX,KAAK6C,OAASkE,EAASA,EAAS3I,OAAS,GACzC,IAAI8I,EAAiBnK,IAAwD+G,iBAC7E,KACM9D,KAAK6C,QAAUqE,IACjBnK,IAAwDoK,cAAcC,YAAY,yBAA0B,CAC1G9C,KAAMA,EACN+C,UAAWrH,KAAKQ,SAAW,GAE3B8G,YAAaL,EAEbM,cAAevH,KAAK6C,QAAU7C,KAAK6C,OAAOkB,WAAa,IAG7D,CAAE,MAAOW,GAAI,CACb,GAAI1E,KAAK6C,UAAY7C,KAAKQ,SAAWR,KAAKW,sBAAsBX,KAAK6C,UAAY7C,KAAKQ,SAAU,CAW9F,GAVAzD,IAAwDoK,cAAcC,YAAY,0BAA2B,CAC3G9C,KAAMA,EACN+C,UAAWrH,KAAKQ,SAAW,GAE3B8G,YAAatH,KAAKW,sBAAsBX,KAAK6C,QAE7C0E,cAAevH,KAAK6C,QAAU7C,KAAK6C,OAAOkB,WAAa,GAEvDyD,mBAAoBxH,KAAK6C,OAAO2E,qBAE9BxH,KAAK6C,OAAO2E,mBACd,OAiBF,OAfAxH,KAAK6C,OAAO2E,oBAAqB,EAIjCxH,KAAK8G,aAAc,EACnB9G,KAAK6C,OAAOiC,OAAS9E,KAAKyH,aAC1BzH,KAAK6C,OAAO6E,QAAU,CACpBC,kBAAmB3H,KAAK2H,kBACxB7C,OAAQ9E,KAAKyH,aACbG,aAAc,QAAUhK,KAE1BoC,KAAK6H,mBAAmB,OACxB7H,KAAK6H,mBAAmB,OACxB7H,KAAK6H,mBAAmB,yBACxB9K,IAAwDoK,cAAcW,OAExE,CA/CA,CAoDF,GACC,CACD9G,IAAK,aACLC,MAAO,WACL,OAAOjB,KAAK8E,QAAU9E,KAAK+H,QAAoC,oBAAnB/H,KAAKgI,WAA4BhI,KAAKgI,WACpF,GACC,CACDhH,IAAK,cACLC,MAAO,SAAqBgH,GAC1B,GAAIxK,GAAYA,EAASyK,eAAgB,CACvC,IAAIC,EAAU1K,EAASyK,eAAeD,GACtC,OAAOE,GAAWA,EAAQrI,OAAS,IACrC,CACA,OAAO,IACT,GACC,CACDkB,IAAK,yBACLC,MAAO,WACL,MAAO,CACLmH,QAASpI,KAAKoI,SAAW,GACzBC,WAAYrI,KAAKqI,YAAc,GAC/BlI,gBAAiBH,KAAKG,iBAAmB,GACzCmI,WAAY,OACZ1J,SAAUoB,KAAKW,sBAAsBX,KAAK6C,QAAQ,GAEtD,GACC,CACD7B,IAAK,UACLC,MAAO,WACL,IAAIQ,EAAUwC,UAAU7F,OAAS,QAAsB8F,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAMnF,GAJAjE,KAAKoE,cAAgB3C,EACjB1E,IAAwDkD,QAAQ,4BAClElD,IAAwDwL,wBAAwB9G,GAE9EzB,KAAKR,kBAAoBzC,IAAwDkD,QAAQ,mBAAoB,CAE/G,IAAIrB,EAAWoB,KAAKW,sBAAsBX,KAAK6C,QAAQ,GACvD7C,KAAKG,gBAAkBpD,IAAwDyL,gBAAgB5J,EACjG,CACI6C,EAAQ2G,UACVpI,KAAKoI,QAAU3G,EAAQ2G,SAGrB3G,EAAQ4G,aACVrI,KAAKqI,WAAa5G,EAAQ4G,YAE5BtL,IAAwD0L,SAASC,KAAK,gBAAgB,EAAGrM,EAAkJ6B,IAAG,EAAG7B,EAAkJ6B,GAAG,CAAC,EAAGuD,GAAUzB,KAAK2I,2BACzZ,IAEE5L,IAAwD6L,IAAIC,SAASpH,EACvE,CAAE,MAAOiD,GACPhD,QAAQC,IAAI,kCAAmC+C,EACjD,CACA1E,KAAK8I,sBAAwB,EAC7B9I,KAAK2H,kBAAoB9J,EAAkB4D,GAC3CzB,KAAKY,aAAa,UAElBZ,KAAK+I,kBAAoB,IAAIC,KAC7BhJ,KAAKiJ,cAAe,EACpBjJ,KAAKkJ,kBAAmB,EACxB,IAAIpE,EAAS9E,KAAKyH,aACI,qBAAX3C,GAA0B9E,KAAK6C,SACxC7C,KAAK6C,OAAO,UAAYiC,EACxB9E,KAAK6C,OAAO6E,UAAY1H,KAAK6C,OAAO6E,QAAQ,UAAY5C,IAE6B,IAAnF7H,EAA8DkM,UAAU/K,QAAgB4B,KAAK6C,SAAuD,IAA7ChE,EAAWP,EAAM0B,KAAK6C,OAAOkB,WACtI9G,EAA8DkM,UAAUC,OAAO,EAAG,EAAGpJ,KAAK6C,OAAOkB,WAEjG/D,KAAK6C,QAAU5F,EAA8DkM,UAAUpN,KAAKiE,KAAK6C,OAAOkB,WAE1G,IAAIsF,EAAM,KAEV,UADOrJ,KAAKsJ,oBACR7H,GAAWA,EAAQ,eAAgB,CACrC4H,EAAM5H,EAAQ8H,mBACP9H,EAAQ8H,YACf,IAAIC,EAAOvM,EAA8DwM,cAAcJ,GACnFG,IACFxJ,KAAKsJ,oBAAsBD,EAC3B5H,EAAQ8D,KAAOiE,EAAKjE,KAExB,CACA,IAAImE,EAAqBzM,EAA8D0M,qBAAsB,EAAG1M,EAA8D0M,sBAC9K,GAAID,EAAoB,CACtB,IAAIE,EAAkBF,EAClBA,EAAmBnE,OACrBqE,EAAkBF,EAAmBnE,MAEvC,IAAI1B,EAAc9G,IAAwD+G,mBACrE4F,EAAmBG,MAAQH,EAAmBG,MAAQH,EAAmBG,OAAShG,EAAY/E,SAC7F2C,EACFA,EAAQmI,gBAAkBA,EAE1BnI,EAAU,CACRmI,gBAAiBA,GAIrB3M,EAA8D6M,qBAAsB,EAAG7M,EAA8D6M,oBAAoB,MAE7K,CACA9J,KAAK+J,oBAAqB,EAC1B/J,KAAKgK,wBAAyB,EAC7BjN,IAA0E,mBAAIM,IAAoD2J,kBACnI,IACGjK,IAAoE,aAAKA,IAA0E,mBAAGA,IAAwDkN,mBAAyB,OAAI,EAC9O,CAAE,MAAOvF,GAAI,CAGb3H,IAAwDkD,QAAQ,kBAAoBlD,IAAwDoK,cAAcC,YAAY,iCAAkC,CACtM8C,QAASlK,KAAK6C,SAAW9F,IAAwD+G,mBAEnF/G,IAAwDkD,QAAQ,kBAAoBlD,IAAwDoN,cAAc,CACxJ7F,KAAM,QAEV,GACC,CACDtD,IAAK,qBACLC,MAEA,SAA4BqD,GAC1B,IAAI8F,EAASpK,KACTqK,EAAW/K,EAAcgF,IAAS,GACtC+F,EAASnI,SAAQ,SAAUoI,GACA,qBAAdF,EAAOE,KAChBF,EAAOvH,OAAOyH,GAAKF,EAAOE,GAE9B,GACF,GACC,CACDtJ,IAAK,WACLC,MAAO,WACL,IAAIsJ,EAASvK,KAEToD,MAAMC,QAAQrD,KAAKwK,oBAAsBxK,KAAKwK,kBAAkBpM,OAAS,IAAM4B,KAAKwK,kBAAkB,GAAG9G,kBAC3G1D,KAAKc,0BAE4B,oBAA5Bd,KAAKyK,oBAAqCzK,KAAKyK,qBACrB,oBAAtBzK,KAAK0K,cAA+B1K,KAAK6C,SAClD7C,KAAK6C,OAAO8H,WAAa3K,KAAK0K,gBAEhC3N,IAAwD0L,SAASC,KAAK,iBAAiB,EAAGrM,EAAkJ6B,GAAG,CAAC,EAAG8B,KAAK2I,2BACpP3I,KAAKT,YACPqL,YAAW,WAETL,EAAOM,mBAAmB,cAC1B9N,IAAwDkD,QAAQ,kBAAoBlD,IAAwD+N,cAAcC,QAAQR,EAAO1H,OAC3K,GAAG,IAEA7C,KAAKI,WACRJ,KAAKY,aAAa,WAEbZ,KAAKiJ,eACRjJ,KAAKgL,oBAAsB,IAAIhC,MAEjChJ,KAAKiL,UAAU,CACbxF,KAAM,OAENxE,OAAQ,IAAI+H,KAAShJ,KAAK+I,oBAG9B/I,KAAKI,UAAW,CAClB,GACC,CACDY,IAAK,oBACLC,MAAO,WAoBL,GAnBAS,QAAQC,IAAI,+BAAgC3B,MAAQA,KAAKQ,SAAW,WACpEzD,IAAwD0L,SAASC,KAAK,gBAAgB,EAAGrM,EAAkJ6B,IAAG,EAAG7B,EAAkJ6B,GAAG,CAAC,EAAG8B,KAAK2I,0BAA2B,CAAC,EAAG,CAC5auC,cAAelC,KAAKmC,SAEtBnL,KAAKoL,cAAe,EACpBpL,KAAKY,aAAa,oBAClB7D,IAAwDkD,QAAQ,kBAAoBlD,IAAwDsO,cAAcC,qBAAqB,cAG3KtL,KAAKiJ,eACPjJ,KAAKgL,oBAAsB,IAAIhC,MAE7BhJ,KAAKuL,eAAe,iCACfvL,KAAKgK,uBAEZhK,KAAK+J,oBAAqB,EAG5BrI,QAAQC,IAAI,0CAA2C3B,KAAK+J,oBACxD/J,KAAK+J,mBAAoB,CAC4D,IAAnF9M,EAA8DkM,UAAU/K,QAAgB4B,KAAK6C,SAAmD,IAAzCvE,EAAKY,QAAQc,KAAK6C,OAAOkB,YAClI9G,EAA8DkM,UAAUC,OAAO,EAAG,EAAGpJ,KAAK6C,OAAOkB,WAEnG,IAAIsF,EAAMrJ,KAAKwL,kBACXnC,GAAOpM,EAA8DwM,cAAcJ,KAAUtM,IAAsE,iBACjKE,EAA8DwM,cAAcJ,GAAKoC,UACnFxO,EAA8DwM,cAAcJ,GAAKqC,UAAUxJ,QAAQ,SAAUqD,GAC3GtI,EAA8DwM,cAAcJ,GAAKoC,SAAS7I,KAAK5C,KAAK6C,OAAQ0C,EAC9G,EAAEtC,KAAKjD,OAEL/C,EAA8DwM,cAAcJ,GAAKsC,aACnF1O,EAA8DwM,cAAcJ,GAAKsC,YAAY/I,KAAK5C,KAAK6C,eAElG7C,KAAKwL,mBAEkE,GAA3EzO,IAAsE,iBACxEA,IAAsE,gBAAI,EAE/E,CACCA,IAA0E,mBAAIM,IAAoD2J,kBACnI,IACGjK,IAAoE,aAAKA,IAA0E,mBAAGA,IAAwDkN,mBAAyB,OAAI,EAC9O,CAAE,MAAOvF,GAAI,CAET1E,KAAKuL,eAAe,2BACfvL,KAAKkJ,iBAEZlJ,KAAKiJ,cAAe,EAItB,IAAI2C,EAAQ5L,KAAK6L,oBACjBD,EAAME,IAAM,GAAK9L,KAAKF,MAAMW,KACI,IAA5BmL,EAAME,IAAI5M,QAAQ,OACpB0M,EAAME,KAAO9L,KAAK2H,mBAEpBiE,EAAMhF,OAAS5G,KAAKiJ,aAEpB2C,EAAMtG,cAAgB,QAED,mBAAjBsG,EAAM9G,QACR9E,KAAKO,UAAUqL,EAEnB,GAMC,CACD5K,IAAK,YACLC,MAAO,WAELlE,IAAwD0L,SAASC,KAAK,kBAAkB,EAAGrM,EAAkJ6B,GAAG,CAAC,EAAG8B,KAAK2I,2BACzP5L,IAAwDkD,QAAQ,kBAAoBlD,IAAwDsO,cAAcC,qBAAqB,gBAC3KtL,KAAK6C,QAAU5F,EAA8DkM,UAAUlM,EAA8DkM,UAAU/K,OAAS,IAAM4B,KAAK6C,OAAOkB,WAC5L9G,EAA8DkM,UAAU4C,MAE1EhP,IAAwDkN,mBAAmB8B,MAC3E,IACGhP,IAAoE,aAAKA,IAA0E,mBAAGA,IAAwDkN,mBAAyB,OAAI,EAC9O,CAAE,MAAOvF,GAAI,CACf,GACC,CACD1D,IAAK,wBACLC,MAAO,WAEP,GACC,CACDD,IAAK,oBACLC,MAAO,WAELlE,IAAwD0L,SAASC,KAAK,gBAAgB,EAAGrM,EAAkJ6B,GAAG,CAAC,EAAG8B,KAAK2I,2BACnP3I,KAAKT,YACPxC,IAAwDkD,QAAQ,kBAAoBlD,IAAwD+N,cAAckB,WAAWhM,KAAK6C,QAE5K7C,KAAKiM,YACP,GACC,CACDjL,IAAK,oBACLC,MAAO,WACL,MAAO,CACL6D,OAAQ,IAAM9E,KAAKyH,cAAgB,KAEvC,GACC,CACDzG,IAAK,aACLC,MAAO,WACDjB,KAAKoL,eAGTpL,KAAKoL,cAAe,EACe,oBAAxBpL,KAAKkM,gBACdjP,EAA8D6M,qBAAsB,EAAG7M,EAA8D6M,oBAAoB9J,KAAKkM,kBAElL,GACC,CACDlL,IAAK,aACLC,MAAO,SAAoBuI,GAEzB,IAAIH,GAAM,EAAGpM,EAA8DkP,mBACvEL,EAAMtC,EAAKsC,IACXM,EAAU,CACZN,IAAKA,GAAO,KAAKO,KAAKP,GAAO,IAAM,KAAO,eAAiB3N,mBAAmBkL,GAC9EiD,QAAS9C,EAAK8C,QAAU9C,EAAK8C,QAAQrJ,KAAKjD,KAAK6C,QAAU,KACzD0J,KAAM/C,EAAK+C,KAAO/C,EAAK+C,KAAKtJ,KAAKjD,KAAK6C,QAAU,KAChD2J,SAAUhD,EAAKgD,SAAWhD,EAAKgD,SAASvJ,KAAKjD,KAAK6C,QAAU,MAE9D,GAAI7C,KAAKyM,gBAAkB,GAAI,CAC7B,IAAIC,EAAM,CACR7F,MAAO,+CACP8F,UAAW,OAOb,OALAjL,QAAQC,IAAI,qBAAsB+K,EAAKZ,GAGvCM,EAAQG,MAAQH,EAAQG,KAAKG,QAC7BN,EAAQI,UAAYJ,EAAQI,SAASE,GAEvC,CACAzP,EAA8DwM,cAAcJ,GAAO,CACjF9D,KAAMiE,EAAKjE,KACXqH,kBAAmBpD,EAAKoD,kBAAoBpD,EAAKoD,kBAAkB3J,KAAKjD,KAAK6C,QAAU,KACvF4I,SAAUjC,EAAKiC,SAAWjC,EAAKiC,SAASxI,KAAKjD,KAAK6C,QAAU,KAC5D8I,YAAanC,EAAKmC,YAAcnC,EAAKmC,YAAY1I,KAAKjD,KAAK6C,QAAU,KACrE6I,UAAW,IAEb1L,KAAKiM,aACLjM,KAAKwL,kBAAoBnC,EACzBtM,IAAwD8P,WAAWT,EACrE,GACC,CACDpL,IAAK,eACLC,MAAO,SAAsBsE,GAC3B,IAAI8D,EAAMrJ,MAAQA,KAAKsJ,qBAAuB,GAC1CD,GAAOpM,EAA8DwM,cAAcJ,IAAQpF,UAAU7F,OAAS,IAChHnB,EAA8DwM,cAAcJ,GAAKqC,UAAU3P,KAAKwJ,GAChGtI,EAA8DwM,cAAcJ,GAAKuD,mBAAqB3P,EAA8DwM,cAAcJ,GAAKuD,kBAAkBrH,IAE3MxI,IAAwD+P,cAC1D,GACC,CACD9L,IAAK,iBACLC,MAAO,SAAwBsE,GAC7B,IAAI8D,EAAMrJ,MAAQA,KAAKsJ,qBAAuB,GAC1CD,GAAOpM,EAA8DwM,cAAcJ,KACrFpM,EAA8DwM,cAAcJ,GAAKqC,UAAU3P,KAAKwJ,GAChGtI,EAA8DwM,cAAcJ,GAAKuD,mBAAqB3P,EAA8DwM,cAAcJ,GAAKuD,kBAAkBrH,GAE7M,GACC,CACDvE,IAAK,eACLC,MAAO,WACL,OAAOlE,IAAwD8I,KAAKkH,KAAK9P,EAA8DkM,UACzI,GACC,CACDnI,IAAK,eACLC,MAAO,WACL,OAAOjB,KAAKgN,eAAe5O,MAC7B,IAEJ,CAhwB4B,CAgwB1BvB,EAAmCoQ,UAG9B", "sources": ["webpack://tarobaseproject/common.js"], "sourcesContent": ["\"use strict\";\n(wx[\"tripTaroGlobal5\"] = wx[\"tripTaroGlobal5\"] || []).push([[8592],{\n\n/***/ 65542:\n/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"Z\": function() { return /* binding */ taroBase; }\n/* harmony export */ });\n/* harmony import */ var _builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(298);\n/* harmony import */ var _builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(68277);\n/* harmony import */ var _builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(57042);\n/* harmony import */ var _builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(24460);\n/* harmony import */ var _builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_callSuper_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(20597);\n/* harmony import */ var _builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_inherits_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(21867);\n/* harmony import */ var _builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(45023);\n/* harmony import */ var _builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_typeof_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(93212);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(90201);\n/* harmony import */ var _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(56884);\n/* harmony import */ var _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(79181);\n/* harmony import */ var _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _miniapp_cwx_ext_global__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(65238);\n/* harmony import */ var _miniapp_cwx_ext_global__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_miniapp_cwx_ext_global__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(92954);\n/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _miniapp_cwx_ext_performance_checkTTI__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(78297);\n/* harmony import */ var _miniapp_cwx_ext_performance_checkTTI__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_miniapp_cwx_ext_performance_checkTTI__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(38641);\n/* harmony import */ var _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6__);\n/* provided dependency */ var document = __webpack_require__(32180)[\"document\"];\n\n\n\n\n\n\n\n\nvar _excluded = [\"pageId\", \"settings\", \"business\"],\n  _excluded2 = [\"duration\"];\n\n\n\n\n\n\n\nvar instanceId = 0;\nfunction serializeQueryObj(obj) {\n  var ret = [];\n  for (var k in obj) {\n    if (k !== '__navigator') {\n      var t = (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_typeof_js__WEBPACK_IMPORTED_MODULE_7__/* [\"default\"] */ .Z)(obj[k]);\n      if (t === 'string' || t === 'number' || t === 'boolean') {\n        ret.push(encodeURIComponent(k) + '=' + encodeURIComponent(obj[k]));\n      }\n    }\n  }\n  return ret.length > 0 ? '?' + ret.join('&') : '';\n}\nvar tabs = function () {\n  var ret = [];\n  var __wxConfig = (_miniapp_cwx_ext_global__WEBPACK_IMPORTED_MODULE_3___default().__wxConfig) || {};\n  if (__wxConfig.tabBar && __wxConfig.tabBar.list) {\n    ret = __wxConfig.tabBar.list.map(function (item) {\n      return item.pagePath;\n    });\n  } else {\n    if ((_miniapp_cwx_ext_global__WEBPACK_IMPORTED_MODULE_3___default().tabbar)) {\n      ret = (_miniapp_cwx_ext_global__WEBPACK_IMPORTED_MODULE_3___default().tabbar); //安卓检测不到tabbar\n    }\n  }\n  return ret;\n}();\nfunction __getIndex(tabs, route) {\n  var index = -1;\n  for (var i = 0; i < tabs.length; i++) {\n    var r = tabs[i];\n    if (r.indexOf(route) != -1) {\n      index = i;\n      break;\n    }\n  }\n  return index;\n}\nvar noop = function noop() {};\n// 注意：Taro 页面没有 onShow, onHide ( 20230321记 )\nvar lifeCycleFns = [\"onLoad\",\n// 可访问页面路由参数：通过访问 options 参数或调用 getCurrentInstance().router。\n\"componentDidShow\",\n// 页面显示/切入前台时触发。【页面没有 onShow, 只有页面组件才会触发 onShow 生命周期】\n\"onReady\",\n// 可访问小程序渲染层的 DOM 节点：使用 createCanvasContext 或 createSelectorQuery 等 API 。【使用时，需查看官方文档中的注意事项】\n\"componentDidHide\",\n// 在下一个页面的 onLoad 之前触发。页面隐藏/切入后台时触发。【页面没有 onHide, 只有页面组件才会触发 onHide 生命周期】\n\"onUnload\",\n// 【在下一个页面的 onLoad 之前触发】\n\"componentWillUnmount\" // 【在下一个页面的 onLoad 之后触发】页面退出时触发。在微信小程序中这一生命周期方法对应 onUnload\n];\nvar eventHandlerFns = [\"onShareAppMessage\", \"onShareTimeline\"];\nvar extraAttrsMap = {\n  // 曝光埋点相关属性\n  autoExpose: [\"autoExpose\", \"exposeThreshold\", \"exposeDuration\", \"getExposeData\", \"sendExposeExtend\"],\n  // 性能检测相关属性\n  checkPerformance: [\"checkPerformance\" // 白屏检测\n  ],\n  perinfo: [\"isForceShowAuthorization\"]\n};\nvar extraApisMap = {\n  ubt: [\"ubtSendPV\", \"ubtAction\", \"ubtTrace\", \"ubtMetric\", \"ubtDevTrace\", \"ubtExposure\", \"ubtTrackError\", \"ubtGet\", \"__ubt_getPageInfo\", \"ubtSet\", \"navigateTo\", \"navigateBack\", \"invokeCallback\"],\n  dom: [\"getDOMProps\"],\n  perinfo: [\"_customizedReporting\"]\n  // checkPerformance: [\n  //   \"viewReadyHandle\", // 监听 CSS动画结束 事件，这里注释是因为直接在 base 里写了，不需要额外绑定\n  // ],\n};\nvar taroBase = /*#__PURE__*/function (_Component) {\n  function taroBase(props) {\n    var _this2;\n    (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_8__/* [\"default\"] */ .Z)(this, taroBase);\n    _this2 = (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_callSuper_js__WEBPACK_IMPORTED_MODULE_9__/* [\"default\"] */ .Z)(this, taroBase, [props]);\n    //= 创建PV\n    //UBT实例key\n    (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_10__/* [\"default\"] */ .Z)(_this2, \"pvId\", void 0);\n    //UBT实例关联PageId\n    (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_10__/* [\"default\"] */ .Z)(_this2, \"pvPage\", void 0);\n    (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_10__/* [\"default\"] */ .Z)(_this2, \"viewReadyHandle\", function () {\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"handleViewReadyEvent\") && _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().handleViewReadyEvent(_this2.whiteScreenGUID);\n    });\n    _this2.runReady = false;\n    _this2.cwx = (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default());\n    _this2.__ubt_events = {};\n    _this2.ubtSendPV();\n    _this2.__route = props && props.tid && props.tid.split(\"?\")[0];\n    // console.log(\"[constructor] this.__route:\", this && this.__route || \"unknown\");\n    // console.log(\"[constructor] this._getCurrentPageRouter:\", this._getCurrentPageRouter());\n    if (_this2._getCurrentPageRouter() === _this2.__route) {\n      //绑定当前页面的ubt\n      _this2._bindPageIns(\"constructor\");\n    }\n    _this2.wrapLifeCycle();\n    _this2.wrapPopularEventHandler(); // 处理分享内容\n    _this2.bindEntryQueryToIns(); // 个保整改3期，将首个页面参数绑定到页面实例的2个参数相关的属性上\n    return _this2;\n  }\n  (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_inherits_js__WEBPACK_IMPORTED_MODULE_11__/* [\"default\"] */ .Z)(taroBase, _Component);\n  return (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_12__/* [\"default\"] */ .Z)(taroBase, [{\n    key: \"bindEntryQueryToIns\",\n    value: function bindEntryQueryToIns() {\n      // 判断小程序壳中是否包含 checkRediToGuide 方法，即是否包含个保整改3期的个保指引页面重定向逻辑\n      if (!_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"checkRediToGuide\")) {\n        return;\n      }\n      var instance = _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default().getCurrentInstance();\n      var guid = instance && instance.router && instance.router.params && instance.router.params.enterQueryGUID || \"\";\n      if (!guid) {\n        guid = instance && instance.page && instance.page.options && instance.page.options.enterQueryGUID || \"\";\n      }\n      if (!guid) {\n        return;\n      }\n      console.log(\"%c [bindEntryQueryToIns] guid:\", \"color:#0f0;\", guid);\n      var enterQuery = _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().getEnterQueryByGUID(guid);\n      console.log(\"%c [bindEntryQueryToIns] enterQuery:\", \"color:#0f0;\", enterQuery);\n      if (enterQuery) {\n        // 处理 Taro 页面实例：如果有 guid, 塞入真实的参数值\n        if (instance && instance.router && instance.router.params) {\n          Object.assign(instance.router.params, enterQuery);\n        }\n        if (instance && instance.page && instance.page.options) {\n          Object.assign(instance.page.options, enterQuery);\n        }\n      }\n    }\n  }, {\n    key: \"sendTTI\",\n    value: function sendTTI() {\n      (0,_miniapp_cwx_ext_performance_checkTTI__WEBPACK_IMPORTED_MODULE_5__.sendTTI)(this);\n    }\n  }, {\n    key: \"wrapLifeCycle\",\n    value: function wrapLifeCycle() {\n      var _this3 = this;\n      lifeCycleFns.forEach(function (fnName) {\n        var oldFn = _this3[fnName] || noop;\n        var newFn = function newFn(args) {\n          if (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"checkRediToGuide\")) {\n            if (fnName === 'onLoad') {\n              // 经过 constructor 的处理后，因为 instance.router.params === args, 所以 args 里已经包含 enterQuery, 因此不需要再次将 enterQuery 绑定到 args 上\n\n              // 个人信息保护指引相关处理：不是 个保指引页，并且 生命周期是 onLoad，并且用户未同意授权，则需要重定向至 个保指引页\n              if (!_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().checkRediToGuide({\n                isPIPGPage: _this3.isPIPGPage,\n                pagePath: _this3._getCurrentPageRouter(),\n                pageQuery: args\n              })) {\n                if (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"checkUsePerInfoProtectComponent\") && _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().checkUsePerInfoProtectComponent()) {\n                  if (_this3.__page) {\n                    _this3.__page.isShowGuideComponent = true; // 绑定到小程序的页面实例上\n                  }\n                } else {\n                  _this3.isRediToGuide = true;\n                  _this3[\"_\" + fnName].call(_this3, args);\n                  return;\n                }\n              }\n            }\n\n            // 重定向到个保指引页的首屏页面，不执行业务生命周期\n            if (_this3.isRediToGuide) {\n              _this3[\"_\" + fnName].call(_this3, args);\n              return;\n            }\n          }\n          _this3[\"_\" + fnName].call(_this3, args);\n          // BU主动入参，控制：如果是在朋友圈打开，是否执行业务生命周期逻辑；\n          if (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"checkInTimeline\") && _this3.stopRunLifeInTL && _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().checkInTimeline()) {\n            return;\n          }\n          oldFn.call(_this3, args);\n        };\n        _this3[fnName] = newFn.bind(_this3);\n      });\n    }\n\n    // 包裹分享、收藏事件处理函数，一般是要 return 一个东西出去的\n  }, {\n    key: \"wrapPopularEventHandler\",\n    value: function wrapPopularEventHandler() {\n      var _this4 = this;\n      eventHandlerFns.forEach(function (fnName) {\n        console.log('[wrapPopularEventHandler] typeof this[fnName]:', (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_typeof_js__WEBPACK_IMPORTED_MODULE_7__/* [\"default\"] */ .Z)(_this4[fnName]));\n        // todo? 暂不添加 showShareToTimeline || (fnName === \"onShareTimeline\" && this.showShareToTimeline)\n        var oldFn = null,\n          fnList = [];\n        if (typeof _this4[fnName] === 'function') {\n          oldFn = _this4[fnName];\n        } else if (Array.isArray(_this4[fnName])) {\n          oldFn = _this4[fnName][0];\n          fnList = _this4[fnName].slice(1);\n        }\n        if (oldFn) {\n          var newFn = function newFn(args) {\n            console.log('[wrapPopularEventHandler] fnName:', fnName);\n            console.log('[wrapPopularEventHandler] args:', args);\n            var res = oldFn.call(_this4, args);\n            console.log('[wrapPopularEventHandler] res:', res);\n            var processedRes = _this4[\"_\".concat(fnName)].call(_this4, args, res); // 执行 _ 的函数\n            return processedRes;\n          };\n          newFn.isWrapperHandler = true;\n          _this4[fnName] = [newFn].concat(fnList);\n        }\n      });\n    }\n  }, {\n    key: \"_getCurrentPageRouter\",\n    value: function _getCurrentPageRouter(page, isClean) {\n      if ((_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().getCurrentPageRouter)) {\n        return _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().getCurrentPageRouter(page, isClean);\n      }\n      // 保留原有逻辑，兼容 其他平台主板 及 独立小程序（兼容尚未添加 cwx.getCurrentPageRouter 的情况）\n      var currentPage = page || _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().getCurrentPage();\n      var route = currentPage && (currentPage.__route__ || currentPage.route) || \"\";\n      if (route && isClean) {\n        // 删除路径后拼接的参数\n        route = route.split(\"?\")[0];\n        // 如果 以 / 作为开头，删除 /\n        if (route.indexOf(\"/\") === 0) {\n          route = route.slice(1);\n        }\n      }\n      return route;\n    }\n  }, {\n    key: \"_onShareTimeline\",\n    value: function _onShareTimeline(args) {\n      var shareData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      console.log('处理前的 shareData:', shareData);\n      if (shareData && typeof shareData === \"boolean\" && typeof (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().processShareTimeline) === \"function\") {\n        shareData = _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().processShareTimeline(this.originOptions);\n      }\n      console.log('处理后的 shareData:', shareData);\n      return shareData;\n    }\n  }, {\n    key: \"_onShareAppMessage\",\n    value: function _onShareAppMessage(args) {\n      var shareData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      console.log('处理前的 args:', args);\n      console.log('处理前的 sharedata:', shareData);\n      if ((_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().processShareData)) {\n        shareData = _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().processShareData(args, shareData);\n      }\n      console.log('处理后的 sharedata:', shareData);\n      return shareData;\n    }\n  }, {\n    key: \"bindExtraApiToPage\",\n    value: function bindExtraApiToPage(type) {\n      var _this5 = this;\n      var apiList = extraApisMap[type] || [];\n      apiList.forEach(function (item) {\n        _this5.__page[item] = _this5[item].bind(_this5);\n      });\n    }\n  }, {\n    key: \"userActionWrapper\",\n    value: function userActionWrapper(fnName) {\n      var _this = this;\n      return function eventWrapper(e) {\n        if (typeof _this[fnName] === 'function') {\n          //上报event\n          try {\n            _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().uploadUserAction(e, fnName, _this);\n          } catch (e) {}\n          _this[fnName].apply(_this, arguments);\n        }\n      };\n    }\n  }, {\n    key: \"ubtSendPV\",\n    value: function ubtSendPV(options) {\n      /**\n       * 如果产生了新的PV需要更新当前page下的ubt.pv实例对象\n       * 避免新PV下的埋点数据（tracelog,metric）关联到上一个PV\n       */\n      options = options || {};\n      var _options = options,\n        pageId = _options.pageId,\n        settings = _options.settings,\n        business = _options.business,\n        other = (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_13__/* [\"default\"] */ .Z)(_options, _excluded);\n      if (this.pvId && !this.pvPage) {\n        this.ubtSetOptions(options); // 之前是 Pageview.instance.init(option);\n      } else {\n        this.pvPage = pageId;\n        //新注册PV，覆盖更新pvId\n        this.pvId = _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default().register({\n          pageId: pageId,\n          settings: settings,\n          business: (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)((0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)((0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)({}, business), other), {}, {\n            __pageFrame__: \"xtaro\"\n          })\n        });\n      }\n    }\n  }, {\n    key: \"ubtAction\",\n    value: function ubtAction(data) {\n      _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default().send({\n        key: 'action',\n        type: 'action',\n        data: data\n      }, this.pvId);\n    }\n  }, {\n    key: \"ubtTrace\",\n    value: function ubtTrace(name, value, _extend) {\n      var traceValue = value;\n      //默认不平铺\n      if (!(_extend !== null && _extend !== void 0 && _extend.tiled)) {\n        switch (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().util.type(value)) {\n          case \"string\":\n          case \"number\":\n            traceValue = value;\n            break;\n          default:\n            traceValue = JSON.stringify(value);\n            break;\n        }\n      }\n      _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default().send({\n        type: 'trace',\n        key: name,\n        data: traceValue\n      }, this.pvId);\n    }\n  }, {\n    key: \"ubtDevTrace\",\n    value: function ubtDevTrace(name, value) {\n      _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default().send({\n        type: 'dev_trace',\n        key: name,\n        data: value\n      }, this.pvId);\n    }\n  }, {\n    key: \"ubtMetric\",\n    value: function ubtMetric(option) {\n      option = option || {};\n      _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default().send({\n        type: 'metric',\n        key: option.name,\n        data: {\n          tag: option.tag,\n          value: option.value\n        }\n      }, this.pvId);\n    }\n  }, {\n    key: \"ubtTrackError\",\n    value: function ubtTrackError(options) {\n      _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default().send({\n        type: 'error',\n        data: options\n      }, this.pvId);\n    }\n  }, {\n    key: \"ubtExposure\",\n    value: function ubtExposure(name, value, _extend) {\n      var _ref = value || {},\n        duration = _ref.duration,\n        rest = (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_13__/* [\"default\"] */ .Z)(_ref, _excluded2);\n      rest.scene = (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().scene) || \"\";\n      var data = rest;\n      //默认不平铺\n      if (!(_extend && _extend.tiled)) {\n        data = JSON.stringify(rest);\n      }\n      _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default().send({\n        type: 'exposure',\n        key: name,\n        data: data\n      }, this.pvId);\n    }\n\n    /**\n     * 获取ubt信息\n     * @param {*} name \"bfa\"|\"vid\"|\"batHeader\"\n     * @returns \n     */\n  }, {\n    key: \"ubtGet\",\n    value: function ubtGet(name, cb) {\n      return _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default().get({\n        name: name,\n        handleResolve: cb\n      }, this.pvId);\n    }\n\n    /**\n     * UBT设置\n     * @param {*} name \n     * @param {*} value \n     */\n  }, {\n    key: \"ubtSet\",\n    value: function ubtSet(name, value) {\n      // // 想办法让ubt取到pageId\n      // ubt_cwx.ubtSet && ubt_cwx.ubtSet(name, value);\n      this.ubtSetOptions((0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_10__/* [\"default\"] */ .Z)({}, name, value));\n    }\n\n    /**\n     * ubt设置\n     * @param {*} options JSONOBJECT\n     * @returns \n     */\n  }, {\n    key: \"ubtSetOptions\",\n    value: function ubtSetOptions(options) {\n      try {\n        if ((0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_typeof_js__WEBPACK_IMPORTED_MODULE_7__/* [\"default\"] */ .Z)(options) != \"object\") return;\n        if (typeof options.pageId != 'undefined') {\n          this.pvPage = options.pageId;\n        }\n        return _miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default().set((0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)((0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)({}, options), {}, {\n          orderId: options.orderid,\n          isBack: options['isBack'] ? 1 : 0\n        }), this.pvId);\n      } catch (error) {\n        console.error('setOptions err', error);\n      }\n    }\n  }, {\n    key: \"_bindPageIns\",\n    value: function _bindPageIns(type) {\n      // if (this.bindTimer) {\n      //   clearTimeout(this.bindTimer);\n      //   this.bindTimer = null;\n      // }\n\n      if (this.isBindedUbt) {\n        return;\n      }\n      var allPages = _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default().getCurrentPages(); // Taro 给出的页面堆栈\n      var currentRoute = this._getCurrentPageRouter();\n      this.__page = allPages[allPages.length - 1];\n      var cwxCurrentPage = _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().getCurrentPage(); // 微信原生API 给出的页面实例\n      try {\n        if (this.__page || cwxCurrentPage) {\n          _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().sendUbtByPage.ubtDevTrace(\"tpage_bindPageIns_type\", {\n            type: type,\n            thisRoute: this.__route || \"\",\n            // constructor 的 props && props.tid && props.tid.split(\"?\")[0];\n            wxGetPRoute: currentRoute,\n            // getCurrentPages()\n            taroGetPRoute: this.__page && this.__page.__route__ || \"\" // Taro.getCurrentPages()\n          });\n        }\n      } catch (e) {}\n      if (this.__page && (!this.__route || this._getCurrentPageRouter(this.__page) === this.__route)) {\n        _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().sendUbtByPage.ubtDevTrace(\"tpage_bindPageIns_start\", {\n          type: type,\n          thisRoute: this.__route || \"\",\n          // constructor 的 props && props.tid && props.tid.split(\"?\")[0];\n          wxGetPRoute: this._getCurrentPageRouter(this.__page),\n          // getCurrentPages()\n          taroGetPRoute: this.__page && this.__page.__route__ || \"\",\n          // Taro.getCurrentPages()\n          _isAlreadyBindPage: this.__page._isAlreadyBindPage // 标志位\n        });\n        if (this.__page._isAlreadyBindPage) {\n          return;\n        }\n        this.__page._isAlreadyBindPage = true;\n        // console.log(\"currentPage.__route__:\",this.__page.__route__);\n        // console.log(\"this.__route__:\",this && this.__route || \"unknown\");\n        // console.log(\"type:\",type);\n        this.isBindedUbt = true;\n        this.__page.pageId = this._getPageId();\n        this.__page.__cpage = {\n          __ubt_querystring: this.__ubt_querystring,\n          pageId: this._getPageId(),\n          __instanceId: \"taro_\" + instanceId++\n        };\n        this.bindExtraApiToPage(\"ubt\");\n        this.bindExtraApiToPage(\"dom\");\n        this.bindExtraApiToPage(\"checkPerformance\");\n        _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().sendUbtByPage.flush(); // 发送ubt埋点\n        return;\n      }\n      // this.bindTimer = setTimeout(() => {\n      //   this.bindTimer = null;\n      //   this._bindPageIns(\"retry\");\n      // }, 20);\n    }\n  }, {\n    key: \"_getPageId\",\n    value: function _getPageId() {\n      return this.pageId || this.pageid || typeof this.getPageId === 'function' && this.getPageId();\n    }\n  }, {\n    key: \"getDOMProps\",\n    value: function getDOMProps(id) {\n      if (document && document.getElementById) {\n        var element = document.getElementById(id);\n        return element && element.props || null;\n      }\n      return null;\n    }\n  }, {\n    key: \"handleLifeCycleNotiOpt\",\n    value: function handleLifeCycleNotiOpt() {\n      return {\n        fcpGUID: this.fcpGUID || \"\",\n        bridgeGUID: this.bridgeGUID || \"\",\n        whiteScreenGUID: this.whiteScreenGUID || \"\",\n        __pageType: \"taro\",\n        pagePath: this._getCurrentPageRouter(this.__page, true)\n      };\n    }\n  }, {\n    key: \"_onLoad\",\n    value: function _onLoad() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      // console.log('[taroBase] _onLoad', this && this.__route || \"unknown\");\n      this.originOptions = options;\n      if (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"clearCacheByOptionsPath\")) {\n        _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().clearCacheByOptionsPath(options);\n      }\n      if (this.checkPerformance && _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"initWhiteScreen\")) {\n        // console.log(\"%c --- Taro页面的 checkPerformance 属性为 true\", \"color:#0f0\");\n        var pagePath = this._getCurrentPageRouter(this.__page, true);\n        this.whiteScreenGUID = _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().initWhiteScreen(pagePath);\n      }\n      if (options.fcpGUID) {\n        this.fcpGUID = options.fcpGUID;\n      }\n      // 供 引用了 bridge.js 的 webview 内嵌 h5 使用\n      if (options.bridgeGUID) {\n        this.bridgeGUID = options.bridgeGUID;\n      }\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().Observer.noti(\"cpage_onLoad\", (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)((0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)({}, options), this.handleLifeCycleNotiOpt()));\n      try {\n        //添加同步市场营销数据的逻辑\n        _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().mkt.setUnion(options);\n      } catch (e) {\n        console.log(\"CPage cwx.mkt.setUnion error = \", e);\n      }\n      this.__ubt_totalActiveTime = 0;\n      this.__ubt_querystring = serializeQueryObj(options);\n      this._bindPageIns(\"onLoad\");\n      // loadTime\n      this.__ubt_onLoadTime = +new Date();\n      this.__ubt_isBack = false;\n      this.__ubt_isBackFlag = false;\n      var pageId = this._getPageId();\n      if (typeof pageId !== 'undefined' && this.__page) {\n        this.__page['pageId'] = pageId;\n        this.__page.__cpage && (this.__page.__cpage['pageId'] = pageId);\n      }\n      if (_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.pageStack.length === 1 && this.__page && __getIndex(tabs, this.__page.__route__) !== -1) {\n        _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.pageStack.splice(0, 1, this.__page.__route__);\n      } else {\n        this.__page && _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.pageStack.push(this.__page.__route__);\n      }\n      var uid = null;\n      delete this.__navigator_fromUid;\n      if (options && options['__navigator']) {\n        uid = options.__navigator;\n        delete options.__navigator;\n        var opts = _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid];\n        if (opts) {\n          this.__navigator_fromUid = uid;\n          options.data = opts.data;\n        }\n      }\n      var prePageLoadPromise = _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.getCWXPageLoadData && (0,_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.getCWXPageLoadData)();\n      if (prePageLoadPromise) {\n        var lastPagePreData = prePageLoadPromise;\n        if (prePageLoadPromise.data) {\n          lastPagePreData = prePageLoadPromise.data;\n        }\n        var currentPage = _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().getCurrentPage();\n        if (!prePageLoadPromise.path || prePageLoadPromise.path && prePageLoadPromise.path === currentPage.route) {\n          if (options) {\n            options.lastPagePreData = lastPagePreData;\n          } else {\n            options = {\n              lastPagePreData: lastPagePreData\n            };\n          }\n          //直接置空，只能相邻页面使用\n          _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.setCWXPageLoadData && (0,_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.setCWXPageLoadData)(null);\n        }\n      }\n      this.__navigator_isBack = false;\n      this.__navigator_isBackFlag = false;\n      (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._wxGetCurrentPages) = _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default().getCurrentPages();\n      try {\n        (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._currentPage) = (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._wxGetCurrentPages)[(_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._wxGetCurrentPages.length) - 1];\n      } catch (e) {}\n      // 兼容cwx上不存在该API的情况\n      // 比较 onLoad 时，this 和 微信原生API返回的页面实例是否绝对相等\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"sendUbtByPage\") && _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().sendUbtByPage.ubtDevTrace(\"weapp_tpage_onLoad_comparePage\", {\n        compare: this.__page === _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().getCurrentPage()\n      });\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"sendPageRoute\") && _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().sendPageRoute({\n        type: \"taro\"\n      });\n    }\n  }, {\n    key: \"_appendAttrsToPage\",\n    value:\n    // 将 CPage 实例上的指定属性绑定到 Page 页面实例上\n    function _appendAttrsToPage(type) {\n      var _this6 = this;\n      var attrList = extraAttrsMap[type] || [];\n      attrList.forEach(function (a) {\n        if (typeof _this6[a] !== \"undefined\") {\n          _this6.__page[a] = _this6[a];\n        }\n      });\n    }\n  }, {\n    key: \"_onReady\",\n    value: function _onReady() {\n      var _this7 = this;\n      // console.log('[taroBase] _onReady', this && this.__route || \"unknown\");\n      if (Array.isArray(this.onShareAppMessage) && this.onShareAppMessage.length > 0 && !this.onShareAppMessage[0].isWrapperHandler) {\n        this.wrapPopularEventHandler(); // 处理分享内容\n      }\n      typeof this._sendXTaroPageInfo === \"function\" && this._sendXTaroPageInfo();\n      if (typeof this._getBaseInfo === 'function' && this.__page) {\n        this.__page._xBaseInfo = this._getBaseInfo();\n      }\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().Observer.noti(\"cpage_onReady\", (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)({}, this.handleLifeCycleNotiOpt()));\n      if (this.autoExpose) {\n        setTimeout(function () {\n          // 加 50 ms 的延迟，能够确保 页面的展示正常触发曝光回调（经过多次测试发现：不加延迟有 50% 的概率不触发）\n          _this7._appendAttrsToPage(\"autoExpose\");\n          _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"sendUbtExpose\") && _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().sendUbtExpose.observe(_this7.__page);\n        }, 50);\n      }\n      if (!this.runReady) {\n        this._bindPageIns(\"onReady\");\n        // active\n        if (!this.__ubt_isBack) {\n          this.__ubt_onActiveTime = +new Date();\n        }\n        this.ubtMetric({\n          name: 100359,\n          //perf.weixin.ready\n          value: +new Date() - this.__ubt_onLoadTime\n        });\n      }\n      this.runReady = true;\n    }\n  }, {\n    key: \"_componentDidShow\",\n    value: function _componentDidShow() {\n      console.log('[taroBase] _componentDidShow', this && this.__route || \"unknown\");\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().Observer.noti(\"cpage_onShow\", (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)((0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)({}, this.handleLifeCycleNotiOpt()), {}, {\n        cpageOnShowTS: Date.now()\n      }));\n      this.isRunPreLoad = false;\n      this._bindPageIns(\"componentDidShow\");\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"sendUbtGather\") && _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().sendUbtGather.getPageDurationProps('pageOnShow');\n\n      // active\n      if (this.__ubt_isBack) {\n        this.__ubt_onActiveTime = +new Date();\n      }\n      if (this.hasOwnProperty('__navigator_isBackFlag')) {\n        delete this.__navigator_isBackFlag;\n      } else {\n        this.__navigator_isBack = true;\n      }\n      // console.log('[_componentDidShow] uid:', this.__navigator_toUid);\n      console.log('[_componentDidShow] __navigator_isBack:', this.__navigator_isBack);\n      if (this.__navigator_isBack) {\n        if (_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.pageStack.length === 1 && this.__page && tabs.indexOf(this.__page.__route__) !== -1) {\n          _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.pageStack.splice(0, 1, this.__page.__route__);\n        }\n        var uid = this.__navigator_toUid;\n        if (uid && _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid] && !(_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().__skipCallback)) {\n          if (_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].callback) {\n            _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].backDatas.forEach(function (data) {\n              _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].callback.call(this.__page, data);\n            }.bind(this));\n          }\n          if (_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].navComplete) {\n            _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].navComplete.call(this.__page);\n          }\n          delete this.__navigator_toUid;\n        }\n        if ((_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().__skipCallback) == true) {\n          (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().__skipCallback) = false;\n        }\n      }\n      (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._wxGetCurrentPages) = _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default().getCurrentPages();\n      try {\n        (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._currentPage) = (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._wxGetCurrentPages)[(_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._wxGetCurrentPages.length) - 1];\n      } catch (e) {}\n      // back\n      if (this.hasOwnProperty('__ubt_isBackFlag')) {\n        delete this.__ubt_isBackFlag;\n      } else {\n        this.__ubt_isBack = true;\n      }\n      // log pv\n      //处理ubt返回相关\n      var ubtPv = this.__ubt_getPageInfo();\n      ubtPv.url = '' + this.props.tid;\n      if (ubtPv.url.indexOf(\"?\") === -1) {\n        ubtPv.url += this.__ubt_querystring;\n      }\n      ubtPv.isBack = this.__ubt_isBack;\n      // 使用 xTaro 框架开发的页面，PV 新增字段 __pageFrame__, 值为 xtaro\n      ubtPv.__pageFrame__ = \"xtaro\";\n      //=发送PV数据，包含是否需要生成新PV的逻辑\n      if (ubtPv.pageId !== 'ignore_page_pv') {\n        this.ubtSendPV(ubtPv);\n      }\n    }\n\n    // 由于 Taro页面 跳转到 原生页面的情况下，\n    // Taro 页面的 componentWillUnmount 是在原生页面的 _onUnload 之后触发的，\n    // 因此需要把之前写在 _componentWillUnmount 里的逻辑，挪到 _onUnload 里，\n    // 才能保证时机的准确性（尤其是获取的页面实例是真正卸载的页面实例）\n  }, {\n    key: \"_onUnload\",\n    value: function _onUnload() {\n      // console.log('[taroBase] _onUnload', this && this.__route || \"unknown\");\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().Observer.noti(\"cpage_onUnload\", (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)({}, this.handleLifeCycleNotiOpt()));\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"sendUbtGather\") && _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().sendUbtGather.getPageDurationProps('pageOnUnload');\n      if (this.__page && _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.pageStack[_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.pageStack.length - 1] == this.__page.__route__) {\n        _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.pageStack.pop();\n      }\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._wxGetCurrentPages.pop();\n      try {\n        (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._currentPage) = (_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._wxGetCurrentPages)[(_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default()._wxGetCurrentPages.length) - 1];\n      } catch (e) {}\n    }\n  }, {\n    key: \"_componentWillUnmount\",\n    value: function _componentWillUnmount() {\n      // console.log('[taroBase] _componentWillUnmount', this && this.__route || \"unknown\");\n    }\n  }, {\n    key: \"_componentDidHide\",\n    value: function _componentDidHide() {\n      // console.log('[taroBase] _componentDidHide', this && this.__route || \"unknown\");\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().Observer.noti(\"cpage_onHide\", (0,_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__/* [\"default\"] */ .Z)({}, this.handleLifeCycleNotiOpt()));\n      if (this.autoExpose) {\n        _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().canIUse(\"sendUbtExpose\") && _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().sendUbtExpose.disconnect(this.__page);\n      }\n      this.runPreLoad();\n    }\n  }, {\n    key: \"__ubt_getPageInfo\",\n    value: function __ubt_getPageInfo() {\n      return {\n        pageId: '' + (this._getPageId() || '0')\n      };\n    }\n  }, {\n    key: \"runPreLoad\",\n    value: function runPreLoad() {\n      if (this.isRunPreLoad) {\n        return;\n      }\n      this.isRunPreLoad = true;\n      if (typeof this.cwxPrePageLoad === 'function') {\n        _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.setCWXPageLoadData && (0,_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.setCWXPageLoadData)(this.cwxPrePageLoad());\n      }\n    }\n  }, {\n    key: \"navigateTo\",\n    value: function navigateTo(opts) {\n      // console.log('[taroBase] navigateTo', this && this.__route || \"unknown\");\n      var uid = (0,_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.getNavigatorUid)();\n      var url = opts.url;\n      var navOpts = {\n        url: url + (/\\?/.test(url) ? '&' : '?') + '__navigator=' + encodeURIComponent(uid),\n        success: opts.success ? opts.success.bind(this.__page) : null,\n        fail: opts.fail ? opts.fail.bind(this.__page) : null,\n        complete: opts.complete ? opts.complete.bind(this.__page) : null\n      };\n      if (this.getPageLevel() >= 10) {\n        var err = {\n          error: '页面层级超过10层',\n          errorCode: '500'\n        };\n        console.log(\"CPage.navigateTo :\", err, url);\n        // console.log( \"CPage.stack :\", this.getPageStack() );\n\n        navOpts.fail && navOpts.fail(err);\n        navOpts.complete && navOpts.complete(err);\n        return;\n      }\n      _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid] = {\n        data: opts.data,\n        immediateCallback: opts.immediateCallback ? opts.immediateCallback.bind(this.__page) : null,\n        callback: opts.callback ? opts.callback.bind(this.__page) : null,\n        navComplete: opts.navComplete ? opts.navComplete.bind(this.__page) : null,\n        backDatas: []\n      };\n      this.runPreLoad();\n      this.__navigator_toUid = uid;\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().navigateTo(navOpts);\n    }\n  }, {\n    key: \"navigateBack\",\n    value: function navigateBack(data) {\n      var uid = this && this.__navigator_fromUid || \"\";\n      if (uid && _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid] && arguments.length > 0) {\n        _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].backDatas.push(data);\n        _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].immediateCallback && _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].immediateCallback(data);\n      }\n      _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().navigateBack();\n    }\n  }, {\n    key: \"invokeCallback\",\n    value: function invokeCallback(data) {\n      var uid = this && this.__navigator_fromUid || \"\";\n      if (uid && _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid]) {\n        _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].backDatas.push(data);\n        _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].immediateCallback && _miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.navigatorOpts[uid].immediateCallback(data);\n      }\n    }\n  }, {\n    key: \"getPageStack\",\n    value: function getPageStack() {\n      return _miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default().util.copy(_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__.pageStack);\n    }\n  }, {\n    key: \"getPageLevel\",\n    value: function getPageLevel() {\n      return this.getPageStack().length;\n    }\n  }]);\n}(react__WEBPACK_IMPORTED_MODULE_0__.Component);\n\n\n/***/ })\n\n}]);"], "names": ["wx", "push", "__unused_webpack_module", "__webpack_exports__", "__webpack_require__", "d", "taroBase", "_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_14__", "_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_objectWithoutProperties_js__WEBPACK_IMPORTED_MODULE_13__", "_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_8__", "_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_12__", "_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_callSuper_js__WEBPACK_IMPORTED_MODULE_9__", "_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_inherits_js__WEBPACK_IMPORTED_MODULE_11__", "_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_10__", "_builds_tinyapp_taro_auto_tarobaseproject_node_modules_babel_runtime_helpers_esm_typeof_js__WEBPACK_IMPORTED_MODULE_7__", "react__WEBPACK_IMPORTED_MODULE_0__", "_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1__", "_miniapp_cwx_cwx__WEBPACK_IMPORTED_MODULE_1___default", "n", "_miniapp_cwx_cpage_initNavigator__WEBPACK_IMPORTED_MODULE_2__", "_miniapp_cwx_ext_global__WEBPACK_IMPORTED_MODULE_3__", "_miniapp_cwx_ext_global__WEBPACK_IMPORTED_MODULE_3___default", "_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__", "_tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default", "_miniapp_cwx_ext_performance_checkTTI__WEBPACK_IMPORTED_MODULE_5__", "_miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6__", "_miniapp_cwx_cpage_ubt_miniapp_js__WEBPACK_IMPORTED_MODULE_6___default", "document", "_excluded", "_excluded2", "instanceId", "serializeQueryObj", "obj", "ret", "k", "t", "Z", "encodeURIComponent", "length", "join", "tabs", "__wxConfig", "tabBar", "list", "map", "item", "pagePath", "__getIndex", "route", "index", "i", "r", "indexOf", "noop", "lifeCycleFns", "eventHandlerFns", "extraAttrsMap", "autoExpose", "checkPerformance", "perinfo", "extraApisMap", "ubt", "dom", "_Component", "props", "_this2", "this", "canIUse", "handleViewReadyEvent", "whiteScreenGUID", "runReady", "cwx", "__ubt_events", "ubtSendPV", "__route", "tid", "split", "_getCurrent<PERSON>ageRouter", "_bindPageIns", "wrapLifeCycle", "wrapPopularEventHandler", "bindEntryQueryToIns", "key", "value", "instance", "getCurrentInstance", "guid", "router", "params", "enterQueryGUID", "page", "options", "console", "log", "enterQuery", "getEnterQueryByGUID", "Object", "assign", "sendTTI", "_this3", "for<PERSON>ach", "fnName", "oldFn", "newFn", "args", "checkRediToGuide", "isPIPGPage", "pageQuery", "checkUsePerInfoProtectComponent", "isRediToGuide", "call", "__page", "isShowGuideComponent", "stopRunLifeInTL", "checkInTimeline", "bind", "_this4", "fnList", "Array", "isArray", "slice", "res", "processedRes", "concat", "isWrapperHandler", "isClean", "getCurrentPageRouter", "currentPage", "getCurrentPage", "__route__", "shareData", "arguments", "undefined", "processShareTimeline", "originOptions", "processShareData", "type", "_this5", "apiList", "_this", "e", "uploadUserAction", "apply", "_options", "pageId", "settings", "business", "other", "pvId", "pvPage", "ubtSetOptions", "register", "__pageFrame__", "data", "send", "name", "_extend", "traceValue", "tiled", "util", "JSON", "stringify", "option", "tag", "_ref", "rest", "duration", "scene", "cb", "get", "handleResolve", "set", "orderId", "orderid", "isBack", "error", "isBindedUbt", "allPages", "getCurrentPages", "currentRoute", "cwxCurrentPage", "sendUbtByPage", "ubtDevTrace", "thisRoute", "wxGetPRoute", "taroGetPRoute", "_isAlreadyBindPage", "_getPageId", "__cpage", "__ubt_querystring", "__instanceId", "bindExtraApiToPage", "flush", "pageid", "getPageId", "id", "getElementById", "element", "fcpGUID", "bridgeGUID", "__pageType", "clearCacheByOptionsPath", "initWhiteScreen", "Observer", "noti", "handleLifeCycleNotiOpt", "mkt", "setUnion", "__ubt_totalActiveTime", "__ubt_onLoadTime", "Date", "__ubt_isBack", "__ubt_isBackFlag", "pageStack", "splice", "uid", "__navigator_fromUid", "__navigator", "opts", "navigatorOpts", "prePageLoadPromise", "getCWXPageLoadData", "lastPagePreData", "path", "setCWXPageLoadData", "__navigator_isBack", "__navigator_isBackFlag", "_wxGetCurrentPages", "compare", "sendPageRoute", "_this6", "attrList", "a", "_this7", "onShareAppMessage", "_sendXTaroPageInfo", "_getBaseInfo", "_xBaseInfo", "setTimeout", "_appendAttrsToPage", "sendUbtExpose", "observe", "__ubt_onActiveTime", "ubtMetric", "cpageOnShowTS", "now", "isRunPreLoad", "sendUbtGather", "getPageDurationProps", "hasOwnProperty", "__navigator_toUid", "callback", "backDatas", "navComplete", "ubtPv", "__ubt_getPageInfo", "url", "pop", "disconnect", "runPreLoad", "cwxPrePageLoad", "getNavigatorUid", "navOpts", "test", "success", "fail", "complete", "getPageLevel", "err", "errorCode", "immediateCallback", "navigateTo", "navigateBack", "copy", "getPageStack", "Component"], "sourceRoot": ""}