<wxs module="xs" src="./utils.wxs" />
<template name="taro_tmpl">
  <block wx:for="{{root.cn}}" wx:key="sid">
    <template is="tmpl_0_container" data="{{i:item,l:''}}" />
  </block>
</template>

<template name="tmpl_0_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_0_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_0_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_0_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_0_24">
  <icon type="{{i.p2}}" size="{{xs.b(i.p1,23)}}" color="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></icon>
</template>

<template name="tmpl_0_44">
  <progress percent="{{i.p8}}" stroke-width="{{xs.b(i.p10,6)}}" color="{{xs.b(i.p5,'#09BB07')}}" activeColor="{{xs.b(i.p2,'#09BB07')}}" backgroundColor="{{xs.b(i.p3,'#EBEBEB')}}" active="{{xs.b(i.p0,!1)}}" active-mode="{{xs.b(i.p1,'backwards')}}" show-info="{{xs.b(i.p9,!1)}}" border-radius="{{xs.b(i.p4,0)}}" font-size="{{xs.b(i.p7,16)}}" duration="{{xs.b(i.p6,30)}}" bindactiveend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></progress>
</template>

<template name="tmpl_0_47">
  <rich-text nodes="{{xs.b(i.p0,[])}}" space="{{i.p1}}" user-select="{{xs.b(i.p2,false)}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></rich-text>
</template>

<template name="tmpl_0_4">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_0_6">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_0_13">
  <button size="{{xs.b(i.p18,'default')}}" type="{{i.p19}}" plain="{{xs.b(i.p12,!1)}}" disabled="{{i.p2}}" loading="{{xs.b(i.p9,!1)}}" form-type="{{i.p3}}" open-type="{{i.p11}}" hover-class="{{xs.b(i.p4,'button-hover')}}" hover-stop-propagation="{{xs.b(i.p7,!1)}}" hover-start-time="{{xs.b(i.p5,20)}}" hover-stay-time="{{xs.b(i.p6,70)}}" name="{{i.p10}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" lang="{{xs.b(i.p8,en)}}" session-from="{{i.p16}}" send-message-title="{{i.p15}}" send-message-path="{{i.p14}}" send-message-img="{{i.p13}}" app-parameter="{{i.p0}}" show-message-card="{{xs.b(i.p17,false)}}" business-id="{{i.p1}}" bindgetuserinfo="eh" bindcontact="eh" bindgetphonenumber="eh" bindchooseavatar="eh" binderror="eh" bindopensetting="eh" bindlaunchapp="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </button>
</template>

<template name="tmpl_0_17">
  <checkbox value="{{i.p4}}" disabled="{{i.p2}}" checked="{{xs.b(i.p0,!1)}}" color="{{xs.b(i.p1,'#09BB07')}}" name="{{i.p3}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </checkbox>
</template>

<template name="tmpl_0_18">
  <checkbox-group bindchange="eh" name="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </checkbox-group>
</template>

<template name="tmpl_0_22">
  <form report-submit="{{xs.b(i.p1,!1)}}" bindsubmit="eh" bindreset="eh" name="{{i.p0}}" report-submit-timeout="{{xs.b(i.p2,0)}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </form>
</template>

<template name="tmpl_0_25">
  <template is="{{xs.c(i, 'tmpl_0_')}}" data="{{i:i}}" />
</template>

<template name="tmpl_0_25_focus">
  <input value="{{i.p24}}" type="{{xs.b(i.p23,'')}}" password="{{xs.b(i.p11,!1)}}" placeholder="{{i.p12}}" placeholder-style="{{i.p14}}" placeholder-class="{{xs.b(i.p13,'input-placeholder')}}" disabled="{{i.p7}}" maxlength="{{xs.b(i.p9,140)}}" cursor-spacing="{{xs.b(i.p6,0)}}" focus="{{xs.b(i.focus,!1)}}" confirm-type="{{xs.b(i.p4,'done')}}" confirm-hold="{{xs.b(i.p3,!1)}}" cursor="{{xs.b(i.p5,i.value.length)}}" selection-start="{{xs.b(i.p22,-1)}}" selection-end="{{xs.b(i.p21,-1)}}" bindinput="eh" bindfocus="eh" bindblur="eh" bindconfirm="eh" name="{{i.p10}}" always-embed="{{xs.b(i.p1,false)}}" adjust-position="{{xs.b(i.p0,true)}}" hold-keyboard="{{xs.b(i.p8,false)}}" safe-password-cert-path="{{i.p15}}" safe-password-length="{{i.p17}}" safe-password-time-stamp="{{i.p20}}" safe-password-nonce="{{i.p18}}" safe-password-salt="{{i.p19}}" safe-password-custom-hash="{{i.p16}}" auto-fill="{{i.p2}}" bindkeyboardheightchange="eh" bindnicknamereview="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></input>
</template>

<template name="tmpl_0_25_blur">
  <input value="{{i.p24}}" type="{{xs.b(i.p23,'')}}" password="{{xs.b(i.p11,!1)}}" placeholder="{{i.p12}}" placeholder-style="{{i.p14}}" placeholder-class="{{xs.b(i.p13,'input-placeholder')}}" disabled="{{i.p7}}" maxlength="{{xs.b(i.p9,140)}}" cursor-spacing="{{xs.b(i.p6,0)}}" confirm-type="{{xs.b(i.p4,'done')}}" confirm-hold="{{xs.b(i.p3,!1)}}" cursor="{{xs.b(i.p5,i.value.length)}}" selection-start="{{xs.b(i.p22,-1)}}" selection-end="{{xs.b(i.p21,-1)}}" bindinput="eh" bindfocus="eh" bindblur="eh" bindconfirm="eh" name="{{i.p10}}" always-embed="{{xs.b(i.p1,false)}}" adjust-position="{{xs.b(i.p0,true)}}" hold-keyboard="{{xs.b(i.p8,false)}}" safe-password-cert-path="{{i.p15}}" safe-password-length="{{i.p17}}" safe-password-time-stamp="{{i.p20}}" safe-password-nonce="{{i.p18}}" safe-password-salt="{{i.p19}}" safe-password-custom-hash="{{i.p16}}" auto-fill="{{i.p2}}" bindkeyboardheightchange="eh" bindnicknamereview="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></input>
</template>

<template name="tmpl_0_27">
  <label for="{{i.p0}}" name="{{i.p1}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </label>
</template>

<template name="tmpl_0_41">
  <picker mode="{{xs.b(i.p5,'selector')}}" disabled="{{i.p1}}" range="{{i.p7}}" range-key="{{i.p8}}" value="{{i.p10}}" start="{{i.p9}}" end="{{i.p2}}" fields="{{xs.b(i.p3,'day')}}" custom-item="{{i.p0}}" name="{{i.p6}}" bindcancel="eh" bindchange="eh" bindcolumnchange="eh" header-text="{{i.p4}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </picker>
</template>

<template name="tmpl_0_42">
  <picker-view value="{{i.p6}}" indicator-style="{{i.p2}}" indicator-class="{{i.p1}}" mask-style="{{i.p4}}" mask-class="{{i.p3}}" bindchange="eh" name="{{i.p5}}" immediate-change="{{xs.b(i.p0,false)}}" bindpickstart="eh" bindpickend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </picker-view>
</template>

<template name="tmpl_0_43">
  <picker-view-column name="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </picker-view-column>
</template>

<template name="tmpl_0_45">
  <radio value="{{i.p4}}" checked="{{xs.b(i.p0,!1)}}" disabled="{{i.p2}}" color="{{xs.b(i.p1,'#09BB07')}}" name="{{i.p3}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </radio>
</template>

<template name="tmpl_0_46">
  <radio-group bindchange="eh" name="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </radio-group>
</template>

<template name="tmpl_0_51">
  <slider min="{{xs.b(i.p7,0)}}" max="{{xs.b(i.p6,100)}}" step="{{xs.b(i.p11,1)}}" disabled="{{i.p5}}" value="{{xs.b(i.p12,0)}}" activeColor="{{xs.b(i.p0,'#1aad19')}}" backgroundColor="{{xs.b(i.p1,'#e9e9e9')}}" block-size="{{xs.b(i.p3,28)}}" block-color="{{xs.b(i.p2,'#ffffff')}}" show-value="{{xs.b(i.p10,!1)}}" bindchange="eh" bindchanging="eh" name="{{i.p8}}" color="{{xs.b(i.p4,'#e9e9e9')}}" selected-color="{{xs.b(i.p9,'#1aad19')}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></slider>
</template>

<template name="tmpl_0_56">
  <switch checked="{{xs.b(i.p0,!1)}}" disabled="{{i.p2}}" type="{{xs.b(i.p4,'switch')}}" color="{{xs.b(i.p1,'#04BE02')}}" bindchange="eh" name="{{i.p3}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></switch>
</template>

<template name="tmpl_0_19">
  <cover-image src="{{i.p0}}" bindload="eh" binderror="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-image>
</template>

<template name="tmpl_0_57">
  <template is="{{xs.c(i, 'tmpl_0_')}}" data="{{i:i}}" />
</template>

<template name="tmpl_0_57_focus">
  <textarea value="{{i.p19}}" placeholder="{{i.p13}}" placeholder-style="{{i.p15}}" placeholder-class="{{xs.b(i.p14,'textarea-placeholder')}}" disabled="{{i.p8}}" maxlength="{{xs.b(i.p11,140)}}" auto-focus="{{xs.b(i.p1,!1)}}" focus="{{xs.b(i.focus,!1)}}" auto-height="{{xs.b(i.p2,!1)}}" fixed="{{xs.b(i.p9,!1)}}" cursor-spacing="{{xs.b(i.p6,0)}}" cursor="{{xs.b(i.p5,-1)}}" selection-start="{{xs.b(i.p17,-1)}}" selection-end="{{xs.b(i.p16,-1)}}" bindfocus="eh" bindblur="eh" bindlinechange="eh" bindinput="eh" bindconfirm="eh" name="{{i.p12}}" show-confirm-bar="{{xs.b(i.p18,true)}}" adjust-position="{{xs.b(i.p0,true)}}" hold-keyboard="{{xs.b(i.p10,false)}}" disable-default-padding="{{xs.b(i.p7,false)}}" confirm-type="{{xs.b(i.p4,'return')}}" confirm-hold="{{xs.b(i.p3,false)}}" bindkeyboardheightchange="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></textarea>
</template>

<template name="tmpl_0_57_blur">
  <textarea value="{{i.p19}}" placeholder="{{i.p13}}" placeholder-style="{{i.p15}}" placeholder-class="{{xs.b(i.p14,'textarea-placeholder')}}" disabled="{{i.p8}}" maxlength="{{xs.b(i.p11,140)}}" auto-focus="{{xs.b(i.p1,!1)}}" auto-height="{{xs.b(i.p2,!1)}}" fixed="{{xs.b(i.p9,!1)}}" cursor-spacing="{{xs.b(i.p6,0)}}" cursor="{{xs.b(i.p5,-1)}}" selection-start="{{xs.b(i.p17,-1)}}" selection-end="{{xs.b(i.p16,-1)}}" bindfocus="eh" bindblur="eh" bindlinechange="eh" bindinput="eh" bindconfirm="eh" name="{{i.p12}}" show-confirm-bar="{{xs.b(i.p18,true)}}" adjust-position="{{xs.b(i.p0,true)}}" hold-keyboard="{{xs.b(i.p10,false)}}" disable-default-padding="{{xs.b(i.p7,false)}}" confirm-type="{{xs.b(i.p4,'return')}}" confirm-hold="{{xs.b(i.p3,false)}}" bindkeyboardheightchange="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></textarea>
</template>

<template name="tmpl_0_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_0_32">
  <movable-area scale-area="{{xs.b(i.p0,!1)}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </movable-area>
</template>

<template name="tmpl_0_33">
  <movable-view direction="{{xs.b(i.p2,none)}}" inertia="{{xs.b(i.p6,!1)}}" out-of-bounds="{{xs.b(i.p7,!1)}}" x="{{i.p13}}" y="{{i.p14}}" damping="{{xs.b(i.p1,20)}}" friction="{{xs.b(i.p4,2)}}" disabled="{{i.p3}}" scale="{{xs.b(i.p8,!1)}}" scale-min="{{xs.b(i.p10,0.5)}}" scale-max="{{xs.b(i.p9,10)}}" scale-value="{{xs.b(i.p11,1)}}" bindchange="eh" bindscale="eh" bindhtouchmove="eh" bindvtouchmove="eh" width="{{xs.b(i.p12,'10px')}}" height="{{xs.b(i.p5,'10px')}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </movable-view>
</template>

<template name="tmpl_0_49">
  <scroll-view scroll-x="{{xs.b(i.p18,!1)}}" scroll-y="{{xs.b(i.p19,!1)}}" upper-threshold="{{xs.b(i.p21,50)}}" lower-threshold="{{xs.b(i.p6,50)}}" scroll-top="{{i.p16}}" scroll-left="{{i.p15}}" scroll-into-view="{{i.p14}}" scroll-with-animation="{{xs.b(i.p17,!1)}}" enable-back-to-top="{{xs.b(i.p2,!1)}}" bindscrolltoupper="eh" bindscrolltolower="eh" bindscroll="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" enable-flex="{{xs.b(i.p3,false)}}" scroll-anchoring="{{xs.b(i.p13,false)}}" refresher-enabled="{{xs.b(i.p10,false)}}" refresher-threshold="{{xs.b(i.p11,45)}}" refresher-default-style="{{xs.b(i.p9,'black')}}" refresher-background="{{xs.b(i.p8,'#FFF')}}" refresher-triggered="{{xs.b(i.p12,false)}}" enhanced="{{xs.b(i.p4,false)}}" bounces="{{xs.b(i.p1,true)}}" show-scrollbar="{{xs.b(i.p20,true)}}" paging-enabled="{{xs.b(i.p7,false)}}" fast-deceleration="{{xs.b(i.p5,false)}}" binddragstart="eh" binddragging="eh" binddragend="eh" bindrefresherpulling="eh" bindrefresherrefresh="eh" bindrefresherrestore="eh" bindrefresherabort="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </scroll-view>
</template>

<template name="tmpl_0_54">
  <swiper indicator-dots="{{xs.b(i.p8,!1)}}" indicator-color="{{xs.b(i.p7,'rgba(0, 0, 0, .3)')}}" indicator-active-color="{{xs.b(i.p6,'#000000')}}" autoplay="{{xs.b(i.p0,!1)}}" current="{{xs.b(i.p2,0)}}" interval="{{xs.b(i.p9,5000)}}" duration="{{xs.b(i.p4,500)}}" circular="{{xs.b(i.p1,!1)}}" vertical="{{xs.b(i.p13,!1)}}" previous-margin="{{xs.b(i.p11,'0px')}}" next-margin="{{xs.b(i.p10,'0px')}}" display-multiple-items="{{xs.b(i.p3,1)}}" bindchange="eh" bindtransition="eh" bindanimationfinish="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" snap-to-edge="{{xs.b(i.p12,false)}}" easing-function="{{xs.b(i.p5,'default')}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </swiper>
</template>

<template name="tmpl_0_55">
  <swiper-item item-id="{{i.p0}}" skip-hidden-item-layout="{{xs.b(i.p1,false)}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </swiper-item>
</template>

<template name="tmpl_0_36">
  <navigator url="{{i.p10}}" open-type="{{xs.b(i.p7,'navigate')}}" delta="{{xs.b(i.p1,1)}}" hover-class="{{xs.b(i.p3,'navigator-hover')}}" hover-stop-propagation="{{xs.b(i.p6,!1)}}" hover-start-time="{{xs.b(i.p4,50)}}" hover-stay-time="{{xs.b(i.p5,600)}}" bindsuccess="eh" bindfail="eh" bindcomplete="eh" target="{{xs.b(i.p9,'self')}}" app-id="{{i.p0}}" path="{{i.p8}}" extra-data="{{i.p2}}" version="{{xs.b(i.p11,'version')}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </navigator>
</template>

<template name="tmpl_0_11">
  <audio id="{{i.p2}}" src="{{i.p6}}" loop="{{xs.b(i.p3,!1)}}" controls="{{xs.b(i.p1,!1)}}" poster="{{i.p5}}" name="{{i.p4}}" author="{{i.p0}}" binderror="eh" bindplay="eh" bindpause="eh" bindtimeupdate="eh" bindended="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></audio>
</template>

<template name="tmpl_0_14">
  <camera device-position="{{xs.b(i.p0,'back')}}" flash="{{xs.b(i.p1,'auto')}}" bindstop="eh" binderror="eh" mode="{{xs.b(i.p3,'normal')}}" resolution="{{xs.b(i.p4,'medium')}}" frame-size="{{xs.b(i.p2,'medium')}}" bindinitdone="eh" bindscancode="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </camera>
</template>

<template name="tmpl_0_3">
  <image src="{{i.p3}}" mode="{{xs.b(i.p1,'scaleToFill')}}" lazy-load="{{xs.b(i.p0,!1)}}" webp="{{xs.b(i.p4,false)}}" show-menu-by-longpress="{{xs.b(i.p2,false)}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </image>
</template>

<template name="tmpl_0_1">
  <image src="{{i.p3}}" mode="{{xs.b(i.p1,'scaleToFill')}}" lazy-load="{{xs.b(i.p0,!1)}}" binderror="eh" bindload="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" webp="{{xs.b(i.p4,false)}}" show-menu-by-longpress="{{xs.b(i.p2,false)}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </image>
</template>

<template name="tmpl_0_28">
  <live-player src="{{i.p13}}" autoplay="{{xs.b(i.p3,!1)}}" muted="{{xs.b(i.p8,!1)}}" orientation="{{xs.b(i.p10,'vertical')}}" object-fit="{{xs.b(i.p9,'contain')}}" background-mute="{{xs.b(i.p4,!1)}}" min-cache="{{xs.b(i.p6,1)}}" max-cache="{{xs.b(i.p5,3)}}" bindstatechange="eh" bindfullscreenchange="eh" bindnetstatus="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" mode="{{xs.b(i.p7,'live')}}" sound-mode="{{xs.b(i.p12,'speaker')}}" auto-pause-if-navigate="{{xs.b(i.p1,true)}}" auto-pause-if-open-native="{{xs.b(i.p2,true)}}" picture-in-picture-mode="{{xs.b(i.p11,[])}}" bindaudiovolumenotify="eh" bindenterpictureinpicture="eh" bindleavepictureinpicture="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </live-player>
</template>

<template name="tmpl_0_58">
  <video src="{{i.p33}}" duration="{{i.p10}}" controls="{{xs.b(i.p6,!0)}}" danmu-list="{{i.p8}}" danmu-btn="{{i.p7}}" enable-danmu="{{i.p12}}" autoplay="{{xs.b(i.p4,!1)}}" loop="{{xs.b(i.p16,!1)}}" muted="{{xs.b(i.p17,!1)}}" initial-time="{{xs.b(i.p15,0)}}" page-gesture="{{xs.b(i.p19,!1)}}" direction="{{i.p9}}" show-progress="{{xs.b(i.p30,!0)}}" show-fullscreen-btn="{{xs.b(i.p27,!0)}}" show-play-btn="{{xs.b(i.p29,!0)}}" show-center-play-btn="{{xs.b(i.p26,!0)}}" enable-progress-gesture="{{xs.b(i.p14,!0)}}" object-fit="{{xs.b(i.p18,'contain')}}" poster="{{i.p22}}" show-mute-btn="{{xs.b(i.p28,!1)}}" bindplay="eh" bindpause="eh" bindended="eh" bindtimeupdate="eh" bindfullscreenchange="eh" bindwaiting="eh" binderror="eh" animation="{{i.p1}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" title="{{i.p34}}" play-btn-position="{{xs.b(i.p21,'bottom')}}" enable-play-gesture="{{xs.b(i.p13,false)}}" auto-pause-if-navigate="{{xs.b(i.p2,true)}}" auto-pause-if-open-native="{{xs.b(i.p3,true)}}" vslide-gesture="{{xs.b(i.p35,false)}}" vslide-gesture-in-fullscreen="{{xs.b(i.p36,true)}}" ad-unit-id="{{i.p0}}" poster-for-crawler="{{i.p23}}" show-casting-button="{{xs.b(i.p25,false)}}" picture-in-picture-mode="{{xs.b(i.p20,[])}}" enable-auto-rotation="{{xs.b(i.p11,false)}}" show-screen-lock-button="{{xs.b(i.p31,false)}}" show-snapshot-button="{{xs.b(i.p32,false)}}" show-background-playback-button="{{xs.b(i.p24,false)}}" background-poster="{{i.p5}}" bindprogress="eh" bindloadedmetadata="eh" bindcontrolstoggle="eh" bindenterpictureinpicture="eh" bindleavepictureinpicture="eh" bindseekcomplete="eh" bindadload="eh" bindaderror="eh" bindadclose="eh" bindadplay="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </video>
</template>

<template name="tmpl_0_15">
  <canvas canvas-id="{{i.p0}}" disable-scroll="{{xs.b(i.p1,!1)}}" binderror="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongtap="eh" type="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </canvas>
</template>

<template name="tmpl_0_9">
  <ad unit-id="{{i.p3}}" ad-intervals="{{i.p0}}" bindload="eh" binderror="eh" bindclose="eh" ad-type="{{xs.b(i.p2,'banner')}}" ad-theme="{{xs.b(i.p1,'white')}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></ad>
</template>

<template name="tmpl_0_60">
  <web-view src="{{i.p0}}" bindmessage="eh" bindload="eh" binderror="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </web-view>
</template>

<template name="tmpl_0_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_0_30">
  <map longitude="{{i.p15}}" latitude="{{i.p13}}" scale="{{xs.b(i.p22,16)}}" markers="{{xs.b(i.p16,[])}}" covers="{{i.p2}}" polyline="{{xs.b(i.p20,[])}}" circles="{{xs.b(i.p0,[])}}" controls="{{xs.b(i.p1,[])}}" include-points="{{xs.b(i.p12,[])}}" show-location="{{i.p25}}" layer-style="{{xs.b(i.p14,1)}}" bindmarkertap="eh" bindcontroltap="eh" bindcallouttap="eh" bindupdated="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" polygons="{{xs.b(i.p19,[])}}" subkey="{{i.p28}}" rotate="{{xs.b(i.p21,0)}}" skew="{{xs.b(i.p27,0)}}" max-scale="{{xs.b(i.p17,20)}}" min-scale="{{xs.b(i.p18,3)}}" enable-3D="{{xs.b(i.p3,false)}}" show-compass="{{xs.b(i.p24,false)}}" show-scale="{{xs.b(i.p26,false)}}" enable-overlooking="{{xs.b(i.p5,false)}}" enable-zoom="{{xs.b(i.p11,true)}}" enable-scroll="{{xs.b(i.p9,true)}}" enable-rotate="{{xs.b(i.p7,false)}}" enable-satellite="{{xs.b(i.p8,false)}}" enable-traffic="{{xs.b(i.p10,false)}}" enable-poi="{{xs.b(i.p6,true)}}" enable-building="{{xs.b(i.p4,true)}}" setting="{{xs.b(i.p23,[])}}" bindlabeltap="eh" bindregionchange="eh" bindpoitap="eh" bindanchorpointtap="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </map>
</template>

<template name="tmpl_0_52">
  <view slot="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_0_53">
  <view slot="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_0_34">
  <slot name="{{i.p0}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </slot>
</template>

<template name="tmpl_0_21">
  <template is="{{xs.c(i, 'tmpl_0_')}}" data="{{i:i,cid:cid}}" />
</template>

<template name="tmpl_0_21_focus">
  <editor read-only="{{xs.b(i.p2,false)}}" placeholder="{{i.p1}}" show-img-size="{{xs.b(i.p4,false)}}" show-img-toolbar="{{xs.b(i.p5,false)}}" show-img-resize="{{xs.b(i.p3,false)}}" focus="{{xs.b(i.focus,false)}}" bindready="eh" bindfocus="eh" bindblur="eh" bindinput="eh" bindstatuschange="eh" name="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </editor>
</template>

<template name="tmpl_0_21_blur">
  <editor read-only="{{xs.b(i.p2,false)}}" placeholder="{{i.p1}}" show-img-size="{{xs.b(i.p4,false)}}" show-img-toolbar="{{xs.b(i.p5,false)}}" show-img-resize="{{xs.b(i.p3,false)}}" bindready="eh" bindfocus="eh" bindblur="eh" bindinput="eh" bindstatuschange="eh" name="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </editor>
</template>

<template name="tmpl_0_31">
  <match-media min-width="{{i.p4}}" max-width="{{i.p2}}" width="{{i.p6}}" min-height="{{i.p3}}" max-height="{{i.p1}}" height="{{i.p0}}" orientation="{{i.p5}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </match-media>
</template>

<template name="tmpl_0_23">
  <functional-page-navigator version="{{xs.b(i.p2,'release')}}" name="{{i.p1}}" args="{{i.p0}}" bindsuccess="eh" bindfail="eh" bindcancel="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </functional-page-navigator>
</template>

<template name="tmpl_0_29">
  <live-pusher url="{{i.p24}}" mode="{{xs.b(i.p20,'RTC')}}" autopush="{{xs.b(i.p6,false)}}" muted="{{xs.b(i.p21,false)}}" enable-camera="{{xs.b(i.p13,true)}}" auto-focus="{{xs.b(i.p5,true)}}" orientation="{{xs.b(i.p22,'vertical')}}" beauty="{{xs.b(i.p8,0)}}" whiteness="{{xs.b(i.p29,0)}}" aspect="{{xs.b(i.p1,'9:16')}}" min-bitrate="{{xs.b(i.p18,200)}}" max-bitrate="{{xs.b(i.p17,1000)}}" audio-quality="{{xs.b(i.p2,'high')}}" waiting-image="{{i.p27}}" waiting-image-hash="{{i.p28}}" zoom="{{xs.b(i.p30,false)}}" device-position="{{xs.b(i.p10,'front')}}" background-mute="{{xs.b(i.p7,false)}}" mirror="{{xs.b(i.p19,false)}}" remote-mirror="{{xs.b(i.p23,false)}}" local-mirror="{{xs.b(i.p16,false)}}" audio-reverb-type="{{xs.b(i.p3,0)}}" enable-mic="{{xs.b(i.p14,true)}}" enable-agc="{{xs.b(i.p11,false)}}" enable-ans="{{xs.b(i.p12,false)}}" audio-volume-type="{{xs.b(i.p4,'voicecall')}}" video-width="{{xs.b(i.p26,360)}}" video-height="{{xs.b(i.p25,640)}}" beauty-style="{{xs.b(i.p9,'smooth')}}" filter="{{xs.b(i.p15,'standard')}}" animation="{{i.p0}}" bindstatechange="eh" bindnetstatus="eh" bindbgmstart="eh" bindbgmprogress="eh" bindbgmcomplete="eh" bindaudiovolumenotify="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </live-pusher>
</template>

<template name="tmpl_0_37">
  <official-account bindload="eh" binderror="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></official-account>
</template>

<template name="tmpl_0_38">
  <open-data type="{{i.p4}}" open-gid="{{i.p3}}" lang="{{xs.b(i.p2,'en')}}" default-text="{{i.p1}}" default-avatar="{{i.p0}}" binderror="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></open-data>
</template>

<template name="tmpl_0_35">
  <navigation-bar title="{{i.p5}}" loading="{{xs.b(i.p4,false)}}" front-color="{{i.p3}}" background-color="{{i.p0}}" color-animation-duration="{{xs.b(i.p1,0)}}" color-animation-timing-func="{{xs.b(i.p2,'linear')}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></navigation-bar>
</template>

<template name="tmpl_0_40">
  <page-meta background-text-style="{{i.p3}}" background-color="{{i.p0}}" background-color-top="{{i.p2}}" background-color-bottom="{{i.p1}}" scroll-top="{{xs.b(i.p7,'')}}" scroll-duration="{{xs.b(i.p6,300)}}" page-style="{{xs.b(i.p4,'')}}" root-font-size="{{xs.b(i.p5,'')}}" bindresize="eh" bindscroll="eh" bindscrolldone="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </page-meta>
</template>

<template name="tmpl_0_59">
  <voip-room openid="{{i.p2}}" mode="{{xs.b(i.p1,'camera')}}" device-position="{{xs.b(i.p0,'front')}}" binderror="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}"></voip-room>
</template>

<template name="tmpl_0_10">
  <ad-custom unit-id="{{i.p1}}" ad-intervals="{{i.p0}}" bindload="eh" binderror="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </ad-custom>
</template>

<template name="tmpl_0_39">
  <page-container show="{{xs.b(i.p7,false)}}" duration="{{xs.b(i.p2,300)}}" z-index="{{xs.b(i.p8,100)}}" overlay="{{xs.b(i.p3,true)}}" position="{{xs.b(i.p5,'bottom')}}" round="{{xs.b(i.p6,false)}}" close-on-slide-down="{{xs.b(i.p0,false)}}" overlay-style="{{i.p4}}" custom-style="{{i.p1}}" bindbeforeenter="eh" bindenter="eh" bindafterenter="eh" bindbeforeleave="eh" bindleave="eh" bindafterleave="eh" bindclickoverlay="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </page-container>
</template>

<template name="tmpl_0_50">
  <share-element key="{{i.p2}}" transform="{{xs.b(i.p3,false)}}" duration="{{xs.b(i.p0,300)}}" easing-function="{{xs.b(i.p1,'ease-out')}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </share-element>
</template>

<template name="tmpl_0_48">
  <root-portal style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </root-portal>
</template>

<template name="tmpl_0_16">
  <channel-live feedId="{{i.p0}}" finderUserName="{{i.p1}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </channel-live>
</template>

<template name="tmpl_0_8">
  <block>{{i.v}}</block>
</template>

<template name="tmpl_0_nav-bar">
  <nav-bar navbarData="{{i.navbarData}}" bindback="eh" bindhome="eh" bindback="eh" bindhome="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </nav-bar>
</template>
  
<template name="tmpl_0_captcha">
  <captcha settings="{{i.settings}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha>
</template>
  
<template name="tmpl_0_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_0_perinfo-protect-float">
  <perinfo-protect-float forceExitMiniapp="{{i.forceExitMiniapp}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </perinfo-protect-float>
</template>
  
<template name="tmpl_0_get-phone-num-btn">
  <get-phone-num-btn btnReadyClassName="{{i.btnReadyClassName}}" btnLoadingClassName="{{i.btnLoadingClassName}}" btnErrorClassName="{{i.btnErrorClassName}}" sourceKey="{{i.sourceKey}}" btnLoadingText="{{i.btnLoadingText}}" btnReadyText="{{i.btnReadyText}}" btnErrorText="{{i.btnErrorText}}" disableLoading="{{i.disableLoading}}" bindcgetphonenumber="eh" limitFrequency="{{i.limitFrequency}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </get-phone-num-btn>
</template>
  
<template name="tmpl_0_ad-sdk">
  <ad-sdk impId="{{i.impId}}" width="{{i.width}}" height="{{i.height}}" extension="{{i.extension}}" lonAndLat="{{i.lonAndLat}}" site="{{i.site}}" bind:get-ad-data="eh" bind:get-ad-data="eh" bindgetAdData="eh" slideVideo="{{i.slideVideo}}" showSign="{{i.showSign}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </ad-sdk>
</template>
  
<template name="tmpl_0_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_0_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_0_taskAssist">
  <taskAssist tempid="{{i.tempid}}" compid="{{i.compid}}" bindbatchReceiveResult="eh" bind:batch-receive-result="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </taskAssist>
</template>
  
<template name="tmpl_0_taskList">
  <taskList tempid="{{i.tempid}}" compid="{{i.compid}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </taskList>
</template>
  
<template name="tmpl_0_coupon">
  <coupon  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </coupon>
</template>
  
<template name="tmpl_0_nps-score">
  <nps-score title="{{i.title}}" positiveDesc="{{i.positiveDesc}}" negativeDesc="{{i.negativeDesc}}" bizBindType="{{i.bizBindType}}" bizBindId="{{i.bizBindId}}" bizId="{{i.bizId}}" env="{{i.env}}" pageId="{{i.pageId}}" auth="{{i.auth}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </nps-score>
</template>
  
<template name="tmpl_0_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_0_mp-html">
  <mp-html content="{{i.content}}" copyLink="{{i.copyLink}}" bindlinktap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </mp-html>
</template>
  
<template name="tmpl_0_cemoji-converter">
  <cemoji-converter inputObj="{{i.inputObj}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cemoji-converter>
</template>
  
<template name="tmpl_0_task-count-down">
  <task-count-down  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </task-count-down>
</template>
  
<template name="tmpl_0_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_0_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_0_mp-navigation-bar">
  <mp-navigation-bar  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </mp-navigation-bar>
</template>
  
<template name="tmpl_0_live-room-play">
  <live-room-play liveAppID="{{i.liveAppID}}" playUrl="{{i.playUrl}}" pictureInPictureMode="{{i.pictureInPictureMode}}" mode="{{i.mode}}" debug="{{i.debug}}" autoplay="{{i.autoplay}}" objectFit="{{i.objectFit}}" minCache="{{i.minCache}}" maxCache="{{i.maxCache}}" autopause="{{i.autopause}}" bind:play-event="eh" bind:net-status="eh" bindPlayEvent="eh" bindNetStatus="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </live-room-play>
</template>
  
<template name="tmpl_0_mini-sk-product">
  <mini-sk-product custom_scene_class="{{i.custom_scene_class}}" idx="{{i.idx}}" sceneCode="{{i.sceneCode}}" renderData="{{i.renderData}}" bind:click-handlers="eh" bindclickHandlers="eh" type="{{i.type}}" updateType="{{i.updateType}}" renderDataList="{{i.renderDataList}}" sceneTotal="{{i.sceneTotal}}" bind:load-next-page="eh" bindloadNextPage="eh" bind:exposure-trace="eh" bindexposureTrace="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </mini-sk-product>
</template>
  
<template name="tmpl_0_waterfall-placeholder">
  <waterfall-placeholder  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall-placeholder>
</template>
  
<template name="tmpl_0_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_0_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_0_container">
  <template is="{{xs.a(0, i.nn, l)}}" data="{{i:i,cid:0,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_1_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_1_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_1_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_1_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_1_4">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_1_6">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_1_22">
  <form report-submit="{{xs.b(i.p1,!1)}}" bindsubmit="eh" bindreset="eh" name="{{i.p0}}" report-submit-timeout="{{xs.b(i.p2,0)}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </form>
</template>

<template name="tmpl_1_27">
  <label for="{{i.p0}}" name="{{i.p1}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </label>
</template>

<template name="tmpl_1_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_1_49">
  <scroll-view scroll-x="{{xs.b(i.p18,!1)}}" scroll-y="{{xs.b(i.p19,!1)}}" upper-threshold="{{xs.b(i.p21,50)}}" lower-threshold="{{xs.b(i.p6,50)}}" scroll-top="{{i.p16}}" scroll-left="{{i.p15}}" scroll-into-view="{{i.p14}}" scroll-with-animation="{{xs.b(i.p17,!1)}}" enable-back-to-top="{{xs.b(i.p2,!1)}}" bindscrolltoupper="eh" bindscrolltolower="eh" bindscroll="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" enable-flex="{{xs.b(i.p3,false)}}" scroll-anchoring="{{xs.b(i.p13,false)}}" refresher-enabled="{{xs.b(i.p10,false)}}" refresher-threshold="{{xs.b(i.p11,45)}}" refresher-default-style="{{xs.b(i.p9,'black')}}" refresher-background="{{xs.b(i.p8,'#FFF')}}" refresher-triggered="{{xs.b(i.p12,false)}}" enhanced="{{xs.b(i.p4,false)}}" bounces="{{xs.b(i.p1,true)}}" show-scrollbar="{{xs.b(i.p20,true)}}" paging-enabled="{{xs.b(i.p7,false)}}" fast-deceleration="{{xs.b(i.p5,false)}}" binddragstart="eh" binddragging="eh" binddragend="eh" bindrefresherpulling="eh" bindrefresherrefresh="eh" bindrefresherrestore="eh" bindrefresherabort="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </scroll-view>
</template>

<template name="tmpl_1_54">
  <swiper indicator-dots="{{xs.b(i.p8,!1)}}" indicator-color="{{xs.b(i.p7,'rgba(0, 0, 0, .3)')}}" indicator-active-color="{{xs.b(i.p6,'#000000')}}" autoplay="{{xs.b(i.p0,!1)}}" current="{{xs.b(i.p2,0)}}" interval="{{xs.b(i.p9,5000)}}" duration="{{xs.b(i.p4,500)}}" circular="{{xs.b(i.p1,!1)}}" vertical="{{xs.b(i.p13,!1)}}" previous-margin="{{xs.b(i.p11,'0px')}}" next-margin="{{xs.b(i.p10,'0px')}}" display-multiple-items="{{xs.b(i.p3,1)}}" bindchange="eh" bindtransition="eh" bindanimationfinish="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" snap-to-edge="{{xs.b(i.p12,false)}}" easing-function="{{xs.b(i.p5,'default')}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </swiper>
</template>

<template name="tmpl_1_55">
  <swiper-item item-id="{{i.p0}}" skip-hidden-item-layout="{{xs.b(i.p1,false)}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </swiper-item>
</template>

<template name="tmpl_1_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_1_52">
  <view slot="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_1_53">
  <view slot="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_1_nav-bar">
  <nav-bar navbarData="{{i.navbarData}}" bindback="eh" bindhome="eh" bindback="eh" bindhome="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </nav-bar>
</template>
  
<template name="tmpl_1_captcha">
  <captcha settings="{{i.settings}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha>
</template>
  
<template name="tmpl_1_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_1_ad-sdk">
  <ad-sdk impId="{{i.impId}}" width="{{i.width}}" height="{{i.height}}" extension="{{i.extension}}" lonAndLat="{{i.lonAndLat}}" site="{{i.site}}" bind:get-ad-data="eh" bind:get-ad-data="eh" bindgetAdData="eh" slideVideo="{{i.slideVideo}}" showSign="{{i.showSign}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </ad-sdk>
</template>
  
<template name="tmpl_1_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_1_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_1_taskList">
  <taskList tempid="{{i.tempid}}" compid="{{i.compid}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </taskList>
</template>
  
<template name="tmpl_1_coupon">
  <coupon  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </coupon>
</template>
  
<template name="tmpl_1_nps-score">
  <nps-score title="{{i.title}}" positiveDesc="{{i.positiveDesc}}" negativeDesc="{{i.negativeDesc}}" bizBindType="{{i.bizBindType}}" bizBindId="{{i.bizBindId}}" bizId="{{i.bizId}}" env="{{i.env}}" pageId="{{i.pageId}}" auth="{{i.auth}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </nps-score>
</template>
  
<template name="tmpl_1_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_1_mp-html">
  <mp-html content="{{i.content}}" copyLink="{{i.copyLink}}" bindlinktap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </mp-html>
</template>
  
<template name="tmpl_1_cemoji-converter">
  <cemoji-converter inputObj="{{i.inputObj}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cemoji-converter>
</template>
  
<template name="tmpl_1_task-count-down">
  <task-count-down  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </task-count-down>
</template>
  
<template name="tmpl_1_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_1_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_1_mp-navigation-bar">
  <mp-navigation-bar  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </mp-navigation-bar>
</template>
  
<template name="tmpl_1_live-room-play">
  <live-room-play liveAppID="{{i.liveAppID}}" playUrl="{{i.playUrl}}" pictureInPictureMode="{{i.pictureInPictureMode}}" mode="{{i.mode}}" debug="{{i.debug}}" autoplay="{{i.autoplay}}" objectFit="{{i.objectFit}}" minCache="{{i.minCache}}" maxCache="{{i.maxCache}}" autopause="{{i.autopause}}" bind:play-event="eh" bind:net-status="eh" bindPlayEvent="eh" bindNetStatus="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </live-room-play>
</template>
  
<template name="tmpl_1_mini-sk-product">
  <mini-sk-product custom_scene_class="{{i.custom_scene_class}}" idx="{{i.idx}}" sceneCode="{{i.sceneCode}}" renderData="{{i.renderData}}" bind:click-handlers="eh" bindclickHandlers="eh" type="{{i.type}}" updateType="{{i.updateType}}" renderDataList="{{i.renderDataList}}" sceneTotal="{{i.sceneTotal}}" bind:load-next-page="eh" bindloadNextPage="eh" bind:exposure-trace="eh" bindexposureTrace="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </mini-sk-product>
</template>
  
<template name="tmpl_1_waterfall-placeholder">
  <waterfall-placeholder  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall-placeholder>
</template>
  
<template name="tmpl_1_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_1_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_1_container">
  <template is="{{xs.a(1, i.nn, l)}}" data="{{i:i,cid:1,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_2_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_2_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_2_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_2_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_2_4">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_2_6">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_2_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_2_49">
  <scroll-view scroll-x="{{xs.b(i.p18,!1)}}" scroll-y="{{xs.b(i.p19,!1)}}" upper-threshold="{{xs.b(i.p21,50)}}" lower-threshold="{{xs.b(i.p6,50)}}" scroll-top="{{i.p16}}" scroll-left="{{i.p15}}" scroll-into-view="{{i.p14}}" scroll-with-animation="{{xs.b(i.p17,!1)}}" enable-back-to-top="{{xs.b(i.p2,!1)}}" bindscrolltoupper="eh" bindscrolltolower="eh" bindscroll="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" enable-flex="{{xs.b(i.p3,false)}}" scroll-anchoring="{{xs.b(i.p13,false)}}" refresher-enabled="{{xs.b(i.p10,false)}}" refresher-threshold="{{xs.b(i.p11,45)}}" refresher-default-style="{{xs.b(i.p9,'black')}}" refresher-background="{{xs.b(i.p8,'#FFF')}}" refresher-triggered="{{xs.b(i.p12,false)}}" enhanced="{{xs.b(i.p4,false)}}" bounces="{{xs.b(i.p1,true)}}" show-scrollbar="{{xs.b(i.p20,true)}}" paging-enabled="{{xs.b(i.p7,false)}}" fast-deceleration="{{xs.b(i.p5,false)}}" binddragstart="eh" binddragging="eh" binddragend="eh" bindrefresherpulling="eh" bindrefresherrefresh="eh" bindrefresherrestore="eh" bindrefresherabort="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </scroll-view>
</template>

<template name="tmpl_2_54">
  <swiper indicator-dots="{{xs.b(i.p8,!1)}}" indicator-color="{{xs.b(i.p7,'rgba(0, 0, 0, .3)')}}" indicator-active-color="{{xs.b(i.p6,'#000000')}}" autoplay="{{xs.b(i.p0,!1)}}" current="{{xs.b(i.p2,0)}}" interval="{{xs.b(i.p9,5000)}}" duration="{{xs.b(i.p4,500)}}" circular="{{xs.b(i.p1,!1)}}" vertical="{{xs.b(i.p13,!1)}}" previous-margin="{{xs.b(i.p11,'0px')}}" next-margin="{{xs.b(i.p10,'0px')}}" display-multiple-items="{{xs.b(i.p3,1)}}" bindchange="eh" bindtransition="eh" bindanimationfinish="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" snap-to-edge="{{xs.b(i.p12,false)}}" easing-function="{{xs.b(i.p5,'default')}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </swiper>
</template>

<template name="tmpl_2_55">
  <swiper-item item-id="{{i.p0}}" skip-hidden-item-layout="{{xs.b(i.p1,false)}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </swiper-item>
</template>

<template name="tmpl_2_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_2_52">
  <view slot="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_2_53">
  <view slot="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_2_nav-bar">
  <nav-bar navbarData="{{i.navbarData}}" bindback="eh" bindhome="eh" bindback="eh" bindhome="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </nav-bar>
</template>
  
<template name="tmpl_2_captcha">
  <captcha settings="{{i.settings}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha>
</template>
  
<template name="tmpl_2_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_2_ad-sdk">
  <ad-sdk impId="{{i.impId}}" width="{{i.width}}" height="{{i.height}}" extension="{{i.extension}}" lonAndLat="{{i.lonAndLat}}" site="{{i.site}}" bind:get-ad-data="eh" bind:get-ad-data="eh" bindgetAdData="eh" slideVideo="{{i.slideVideo}}" showSign="{{i.showSign}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </ad-sdk>
</template>
  
<template name="tmpl_2_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_2_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_2_taskList">
  <taskList tempid="{{i.tempid}}" compid="{{i.compid}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </taskList>
</template>
  
<template name="tmpl_2_coupon">
  <coupon  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </coupon>
</template>
  
<template name="tmpl_2_nps-score">
  <nps-score title="{{i.title}}" positiveDesc="{{i.positiveDesc}}" negativeDesc="{{i.negativeDesc}}" bizBindType="{{i.bizBindType}}" bizBindId="{{i.bizBindId}}" bizId="{{i.bizId}}" env="{{i.env}}" pageId="{{i.pageId}}" auth="{{i.auth}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </nps-score>
</template>
  
<template name="tmpl_2_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_2_mp-html">
  <mp-html content="{{i.content}}" copyLink="{{i.copyLink}}" bindlinktap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </mp-html>
</template>
  
<template name="tmpl_2_cemoji-converter">
  <cemoji-converter inputObj="{{i.inputObj}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cemoji-converter>
</template>
  
<template name="tmpl_2_task-count-down">
  <task-count-down  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </task-count-down>
</template>
  
<template name="tmpl_2_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_2_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_2_mp-navigation-bar">
  <mp-navigation-bar  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </mp-navigation-bar>
</template>
  
<template name="tmpl_2_live-room-play">
  <live-room-play liveAppID="{{i.liveAppID}}" playUrl="{{i.playUrl}}" pictureInPictureMode="{{i.pictureInPictureMode}}" mode="{{i.mode}}" debug="{{i.debug}}" autoplay="{{i.autoplay}}" objectFit="{{i.objectFit}}" minCache="{{i.minCache}}" maxCache="{{i.maxCache}}" autopause="{{i.autopause}}" bind:play-event="eh" bind:net-status="eh" bindPlayEvent="eh" bindNetStatus="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </live-room-play>
</template>
  
<template name="tmpl_2_mini-sk-product">
  <mini-sk-product custom_scene_class="{{i.custom_scene_class}}" idx="{{i.idx}}" sceneCode="{{i.sceneCode}}" renderData="{{i.renderData}}" bind:click-handlers="eh" bindclickHandlers="eh" type="{{i.type}}" updateType="{{i.updateType}}" renderDataList="{{i.renderDataList}}" sceneTotal="{{i.sceneTotal}}" bind:load-next-page="eh" bindloadNextPage="eh" bind:exposure-trace="eh" bindexposureTrace="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </mini-sk-product>
</template>
  
<template name="tmpl_2_waterfall-placeholder">
  <waterfall-placeholder  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall-placeholder>
</template>
  
<template name="tmpl_2_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_2_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_2_container">
  <template is="{{xs.a(2, i.nn, l)}}" data="{{i:i,cid:2,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_3_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_3_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_3_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_3_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_3_4">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_3_6">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_3_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_3_49">
  <scroll-view scroll-x="{{xs.b(i.p18,!1)}}" scroll-y="{{xs.b(i.p19,!1)}}" upper-threshold="{{xs.b(i.p21,50)}}" lower-threshold="{{xs.b(i.p6,50)}}" scroll-top="{{i.p16}}" scroll-left="{{i.p15}}" scroll-into-view="{{i.p14}}" scroll-with-animation="{{xs.b(i.p17,!1)}}" enable-back-to-top="{{xs.b(i.p2,!1)}}" bindscrolltoupper="eh" bindscrolltolower="eh" bindscroll="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" enable-flex="{{xs.b(i.p3,false)}}" scroll-anchoring="{{xs.b(i.p13,false)}}" refresher-enabled="{{xs.b(i.p10,false)}}" refresher-threshold="{{xs.b(i.p11,45)}}" refresher-default-style="{{xs.b(i.p9,'black')}}" refresher-background="{{xs.b(i.p8,'#FFF')}}" refresher-triggered="{{xs.b(i.p12,false)}}" enhanced="{{xs.b(i.p4,false)}}" bounces="{{xs.b(i.p1,true)}}" show-scrollbar="{{xs.b(i.p20,true)}}" paging-enabled="{{xs.b(i.p7,false)}}" fast-deceleration="{{xs.b(i.p5,false)}}" binddragstart="eh" binddragging="eh" binddragend="eh" bindrefresherpulling="eh" bindrefresherrefresh="eh" bindrefresherrestore="eh" bindrefresherabort="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </scroll-view>
</template>

<template name="tmpl_3_54">
  <swiper indicator-dots="{{xs.b(i.p8,!1)}}" indicator-color="{{xs.b(i.p7,'rgba(0, 0, 0, .3)')}}" indicator-active-color="{{xs.b(i.p6,'#000000')}}" autoplay="{{xs.b(i.p0,!1)}}" current="{{xs.b(i.p2,0)}}" interval="{{xs.b(i.p9,5000)}}" duration="{{xs.b(i.p4,500)}}" circular="{{xs.b(i.p1,!1)}}" vertical="{{xs.b(i.p13,!1)}}" previous-margin="{{xs.b(i.p11,'0px')}}" next-margin="{{xs.b(i.p10,'0px')}}" display-multiple-items="{{xs.b(i.p3,1)}}" bindchange="eh" bindtransition="eh" bindanimationfinish="eh" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" snap-to-edge="{{xs.b(i.p12,false)}}" easing-function="{{xs.b(i.p5,'default')}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </swiper>
</template>

<template name="tmpl_3_55">
  <swiper-item item-id="{{i.p0}}" skip-hidden-item-layout="{{xs.b(i.p1,false)}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </swiper-item>
</template>

<template name="tmpl_3_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_3_52">
  <view slot="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_3_53">
  <view slot="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_3_nav-bar">
  <nav-bar navbarData="{{i.navbarData}}" bindback="eh" bindhome="eh" bindback="eh" bindhome="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </nav-bar>
</template>
  
<template name="tmpl_3_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_3_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_3_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_3_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_3_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_3_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_3_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_3_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_3_container">
  <template is="{{xs.a(3, i.nn, l)}}" data="{{i:i,cid:3,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_4_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_4_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_4_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_4_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_4_4">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_4_6">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_4_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_4_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_4_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_4_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_4_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_4_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_4_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_4_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_4_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_4_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_4_container">
  <template is="{{xs.a(4, i.nn, l)}}" data="{{i:i,cid:4,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_5_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_5_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_5_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_5_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_5_4">
  <text selectable="{{xs.b(i.p1,!1)}}" space="{{i.p2}}" decode="{{xs.b(i.p0,!1)}}" user-select="{{xs.b(i.p3,false)}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </text>
</template>

<template name="tmpl_5_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_5_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_5_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_5_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_5_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_5_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_5_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_5_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_5_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_5_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_5_container">
  <template is="{{xs.a(5, i.nn, l)}}" data="{{i:i,cid:5,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_6_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_6_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_6_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_6_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_6_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_6_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_6_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_6_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_6_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_6_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_6_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_6_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_6_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_6_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_6_container">
  <template is="{{xs.a(6, i.nn, l)}}" data="{{i:i,cid:6,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_7_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_7_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_7_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_7_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_7_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_7_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_7_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_7_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_7_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_7_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_7_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_7_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_7_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_7_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_7_container">
  <template is="{{xs.a(7, i.nn, l)}}" data="{{i:i,cid:7,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_8_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_8_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_8_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_8_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_8_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_8_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_8_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_8_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_8_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_8_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_8_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_8_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_8_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_8_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_8_container">
  <template is="{{xs.a(8, i.nn, l)}}" data="{{i:i,cid:8,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_9_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_9_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_9_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_9_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_9_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_9_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_9_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_9_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_9_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_9_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_9_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_9_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_9_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_9_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_9_container">
  <template is="{{xs.a(9, i.nn, l)}}" data="{{i:i,cid:9,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_10_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_10_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_10_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_10_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_10_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_10_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_10_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_10_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_10_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_10_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_10_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_10_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_10_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_10_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_10_container">
  <template is="{{xs.a(10, i.nn, l)}}" data="{{i:i,cid:10,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_11_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_11_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_11_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_11_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_11_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_11_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_11_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_11_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_11_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_11_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_11_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_11_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_11_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_11_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_11_container">
  <template is="{{xs.a(11, i.nn, l)}}" data="{{i:i,cid:11,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_12_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_12_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_12_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_12_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_12_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_12_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_12_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_12_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_12_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_12_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_12_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_12_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_12_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_12_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_12_container">
  <template is="{{xs.a(12, i.nn, l)}}" data="{{i:i,cid:12,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_13_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_13_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_13_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_13_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_13_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_13_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_13_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_13_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_13_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_13_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_13_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_13_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_13_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_13_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_13_container">
  <template is="{{xs.a(13, i.nn, l)}}" data="{{i:i,cid:13,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_14_0">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh" catchtouchmove="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_14_5">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" animation="{{i.p0}}" style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_14_2">
  <view style="{{i.st}}" class="{{i.cl}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_14_7">
  <view hover-class="{{xs.b(i.p1,'none')}}" hover-stop-propagation="{{xs.b(i.p4,!1)}}" hover-start-time="{{xs.b(i.p2,50)}}" hover-stay-time="{{xs.b(i.p3,400)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" animation="{{i.p0}}" bindanimationstart="eh" bindanimationiteration="eh" bindanimationend="eh" bindtransitionend="eh" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </view>
</template>

<template name="tmpl_14_20">
  <cover-view scroll-top="{{xs.b(i.p1,!1)}}" bindtouchstart="eh" bindtouchmove="eh" bindtouchend="eh" bindtouchcancel="eh" bindlongpress="eh" marker-id="{{i.p0}}" slot="{{i.p2}}" style="{{i.st}}" class="{{i.cl}}" bindtap="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cover-view>
</template>

<template name="tmpl_14_12">
  <block  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </block>
</template>

<template name="tmpl_14_cimage">
  <cimage c_className="{{i.c_className}}" src="{{i.src}}" bindimageerror="eh" mode="{{i.mode}}" showMenuByLongpress="{{i.showMenuByLongpress}}" bindimagetap="eh" c_style="{{i.c_style}}" style="{{i.st}}" fadeIn="{{i.fadeIn}}" webp="{{i.webp}}" lazyLoad="{{i.lazyLoad}}" bindimageload="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </cimage>
</template>
  
<template name="tmpl_14_captcha-min">
  <captcha-min settings="{{i.settings}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha-min>
</template>
  
<template name="tmpl_14_collection">
  <collection isCustomNav="{{i.isCustomNav}}" delayTime="{{i.delayTime}}" daysBetween="{{i.daysBetween}}" collectText="{{i.collectText}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </collection>
</template>
  
<template name="tmpl_14_authorization-float">
  <authorization-float  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </authorization-float>
</template>
  
<template name="tmpl_14_waterfall">
  <waterfall source="{{i.source}}" appId="{{i.appId}}" ignoreLocation="{{i.ignoreLocation}}" globalInfo="{{i.globalInfo}}" isNeedCallback="{{i.isNeedCallback}}" bindcallback="eh" bindgetref="eh" hotelInfo="{{i.hotelInfo}}" bindgetref="eh"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </waterfall>
</template>
  
<template name="tmpl_14_timelineFloat">
  <timelineFloat  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </timelineFloat>
</template>
  
<template name="tmpl_14_captcha_min">
  <captcha_min settings="{{i.settings}}" refresh="{{i.refresh}}" reload="{{i.reload}}"  id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
    <block wx:for="{{i.cn}}" wx:key="sid">
      <template is="{{xs.e(cid+1)}}" data="{{i:item,l:l}}" />
    </block>
  </captcha_min>
</template>
  
<template name="tmpl_14_custom-wrapper">
  <custom-wrapper i="{{i}}" l="{{l}}" id="{{i.uid||i.sid}}" data-sid="{{i.sid}}">
  </custom-wrapper>
</template>
  
<template name="tmpl_14_container">
  <template is="{{xs.a(14, i.nn, l)}}" data="{{i:i,cid:14,l:xs.f(l,i.nn)}}" />
</template>

<template name="tmpl_15_container">
  <block wx:if="{{i.nn === '#text'}}">
    <template is="tmpl_0_#text" data="{{i:i}}" />
  </block>
  <block wx:else>
    <comp i="{{i}}" l="{{l}}" />
  </block>
</template>
