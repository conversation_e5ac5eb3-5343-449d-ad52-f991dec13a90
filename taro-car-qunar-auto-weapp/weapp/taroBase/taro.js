(wx["tripTaroGlobal5"]=wx["tripTaroGlobal5"]||[]).push([[2107],{72049:function(e,t,n){"use strict";n.r(t)},99671:function(e,t,n){"use strict";n.d(t,{Z:function(){return I}});var r=n(93212),i=n(32180),o=n(298),a=n(57042),u=n(24460),c=n(45023);function s(e){return"function"===typeof e}function l(e){return"undefined"===typeof e}function d(e){return e&&"object"===(0,r.Z)(e)}var f=function(e){return!d(e)};function h(e){throw new TypeError(e)}s(Object.assign)||(Object.assign=function(e){null==e&&h("Cannot convert undefined or null to object");for(var t=Object(e),n=1;n<arguments.length;n++){var r=arguments[n];if(null!=r)for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}),s(Object.defineProperties)||(Object.defineProperties=function(e,t){function n(e){function t(e,t){return Object.prototype.hasOwnProperty.call(e,t)}f(e)&&h("bad desc");var n={};if(t(e,"enumerable")&&(n.enumerable=!!e.enumerable),t(e,"configurable")&&(n.configurable=!!e.configurable),t(e,"value")&&(n.value=e.value),t(e,"writable")&&(n.writable=!!e.writable),t(e,"get")){var r=e.get;s(r)||l(r)||h("bad get"),n.get=r}if(t(e,"set")){var i=e.set;s(i)||l(i)||h("bad set"),n.set=i}return("get"in n||"set"in n)&&("value"in n||"writable"in n)&&h("identity-confused descriptor"),n}f(e)&&h("bad obj"),t=Object(t);for(var r=Object.keys(t),i=[],o=0;o<r.length;o++)i.push([r[o],n(t[r[o]])]);for(var a=0;a<i.length;a++)Object.defineProperty(e,i[a][0],i[a][1]);return e});var v={WEAPP:"WEAPP",WEB:"WEB",RN:"RN",SWAN:"SWAN",ALIPAY:"ALIPAY",TT:"TT",QQ:"QQ",JD:"JD"};function p(){return v.WEAPP}var g=function(){function e(t,n,r){(0,a.Z)(this,e),this.index=r||0,this.requestParams=t,this.interceptors=n||[]}return(0,u.Z)(e,[{key:"proceed",value:function(e){if(this.requestParams=e,this.index>=this.interceptors.length)throw new Error("chain \u53c2\u6570\u9519\u8bef, \u8bf7\u52ff\u76f4\u63a5\u4fee\u6539 request.chain");var t=this._getNextInterceptor(),n=this._getNextChain(),r=t(n),i=r.catch((function(e){return Promise.reject(e)}));return Object.keys(r).forEach((function(e){return s(r[e])&&(i[e]=r[e])})),i}},{key:"_getNextInterceptor",value:function(){return this.interceptors[this.index]}},{key:"_getNextChain",value:function(){return new e(this.requestParams,this.interceptors,this.index+1)}}]),e}(),m=function(){function e(t){(0,a.Z)(this,e),this.taroInterceptor=t,this.chain=new g}return(0,u.Z)(e,[{key:"request",value:function(e){var t=this.chain,n=this.taroInterceptor;return t.interceptors=t.interceptors.filter((function(e){return e!==n})).concat(n),t.proceed((0,o.Z)({},e))}},{key:"addInterceptor",value:function(e){this.chain.interceptors.push(e)}},{key:"cleanInterceptors",value:function(){this.chain=new g}}]),e}();function b(e){var t,n=e.requestParams,r=new Promise((function(r,i){var o=setTimeout((function(){o=null,i(new Error("\u7f51\u7edc\u94fe\u63a5\u8d85\u65f6,\u8bf7\u7a0d\u540e\u518d\u8bd5\uff01"))}),n&&n.timeout||6e4);t=e.proceed(n),t.then((function(e){o&&(clearTimeout(o),r(e))})).catch((function(e){o&&clearTimeout(o),i(e)}))}));return!l(t)&&s(t.abort)&&(r.abort=t.abort),r}function y(e){var t=e.requestParams;t.method,t.data,t.url;var n=e.proceed(t),r=n.then((function(e){return e}));return s(n.abort)&&(r.abort=n.abort),r}var k=Object.freeze({__proto__:null,timeoutInterceptor:b,logInterceptor:y});function w(e){return e}function C(e){return function(t,n){e.preloadData=d(t)?t:(0,c.Z)({},t,n)}}var T=750,S={640:1.17,750:1,828:.905},P=20;function E(e){return function(t){var n=t.designWidth,r=void 0===n?T:n,i=t.deviceRatio,o=void 0===i?S:i,a=t.baseFontSize,u=void 0===a?P:a;e.config=e.config||{},e.config.designWidth=r,e.config.deviceRatio=o,e.config.baseFontSize=u}}function x(e){return function(t){var n=e.config||{},r=n.designWidth,i=void 0===r?T:r,o=n.deviceRatio,a=void 0===o?S:o;if(!(i in a))throw new Error("deviceRatio \u914d\u7f6e\u4e2d\u4e0d\u5b58\u5728 ".concat(i," \u7684\u8bbe\u7f6e\uff01"));return parseInt(t,10)*a[i]+"rpx"}}var I={Behavior:w,getEnv:p,ENV_TYPE:v,Link:m,interceptors:k,Current:i.Current,getCurrentInstance:i.getCurrentInstance,options:i.options,nextTick:i.nextTick,eventCenter:i.eventCenter,Events:i.Events,getInitPxTransform:E};I.initPxTransform=E(I),I.preload=C(i.Current),I.pxTransform=x(I)},88744:function(e,t,n){"use strict";n.d(t,{Ox:function(){return $}});var r=n(45023),i=n(90129),o=n(57042),a=n(24460),u=n(20597),c=n(21867),s=n(22276),l=n(74455),d=n(32180),f={PageContext:l.kT,R:l.kT},h="taro-app";function v(e,t){var n,r=t.prototype;return!(null===(n=t.displayName)||void 0===n?void 0:n.includes("Connect"))&&((0,l.mf)(t.render)||!!(null===r||void 0===r?void 0:r.isReactComponent)||r instanceof e.Component)}function p(e){return(0,l.kJ)(e)?e:e?[e]:[]}function g(e){return e.writable=!0,e.enumerable=!0,e}function m(e){d.Current.router=Object.assign({params:null===e||void 0===e?void 0:e.query},e)}var b,y,k=function(e){return function(t){var n=f.R,r=f.PageContext,i=n.useContext(r)||h,o=n.useRef(),a=n.useRef(t);a.current!==t&&(a.current=t),n.useLayoutEffect((function(){var t=o.current=(0,d.getPageInstance)(i),n=!1;t||(n=!0,o.current=Object.create(null),t=o.current);var r=function(){return a.current.apply(a,arguments)};return(0,l.mf)(t[e])?t[e]=[t[e],r]:t[e]=[].concat((0,s.Z)(t[e]||[]),[r]),n&&(0,d.injectPageInstance)(t,i),function(){var t=o.current;if(t){var n=t[e];n===r?t[e]=void 0:(0,l.kJ)(n)&&(t[e]=n.filter((function(e){return e!==r}))),o.current=void 0}}}),[])}},w=k("componentDidHide"),C=k("componentDidShow"),T=k("onError"),S=k("onUnhandledRejection"),P=k("onLaunch"),E=k("onPageNotFound"),x=k("onLoad"),I=k("onPageScroll"),A=k("onPullDownRefresh"),N=k("onPullIntercept"),L=k("onReachBottom"),B=k("onResize"),O=k("onUnload"),Z=k("onAddToFavorites"),_=k("onOptionMenuClick"),R=k("onSaveExitState"),j=k("onShareAppMessage"),M=k("onShareTimeline"),D=k("onTitleClick"),F=k("onReady"),U=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=f.R;return e?d.Current.router:t.useMemo((function(){return d.Current.router}),[])},V=k("onTabItemTap"),z=function(){},W=Object.freeze({__proto__:null,useDidHide:w,useDidShow:C,useError:T,useUnhandledRejection:S,useLaunch:P,usePageNotFound:E,useLoad:x,usePageScroll:I,usePullDownRefresh:A,usePullIntercept:N,useReachBottom:L,useResize:B,useUnload:O,useAddToFavorites:Z,useOptionMenuClick:_,useSaveExitState:R,useShareAppMessage:j,useShareTimeline:M,useTitleClick:D,useReady:F,useRouter:U,useTabItemTap:V,useScope:z}),q=(0,d.incrementId)();function G(e){l.PT.tap("getLifecycle",(function(e,t){return t=t.replace(/^on(Show|Hide)$/,"componentDid$1"),e[t]})),l.PT.tap("modifyMpEvent",(function(e){e.type=e.type.replace(/-/g,"")})),l.PT.tap("batchedEventUpdates",(function(t){e.unstable_batchedUpdates(t)})),l.PT.tap("mergePageInstance",(function(e,t){e&&t&&("constructor"in e||Object.keys(e).forEach((function(n){var r=e[n],i=p(t[n]);t[n]=i.concat(r)})))}))}function H(e,t){return function(n){var r=v(e,n),i=function(e){return e&&(0,d.injectPageInstance)(e,t)},s=r?{ref:i}:{forwardedRef:i,reactReduxForwardedRef:i};return f.PageContext===l.kT&&(f.PageContext=e.createContext("")),function(e){function r(){var e;return(0,o.Z)(this,r),e=(0,u.Z)(this,r,arguments),e.state={hasError:!1},e}return(0,c.Z)(r,e),(0,a.Z)(r,[{key:"componentDidCatch",value:function(e,t){0}},{key:"render",value:function(){var e=this.state.hasError?[]:b(f.PageContext.Provider,{value:t},b(n,Object.assign(Object.assign({},this.props),s)));return b("root",{id:t},e)}}],[{key:"getDerivedStateFromError",value:function(e){var t,n;return null===(n=null===(t=d.Current.app)||void 0===t?void 0:t.onError)||void 0===n||n.call(t,e.message+e.stack),{hasError:!0}}}])}(e.Component)}}function $(e,t,n,s){f.R=t,b=t.createElement,y=n,t.Fragment;var p,k,w=t.createRef(),C=v(t,e),T=new Promise((function(e){return k=e}));function S(){return w.current}function P(e){p?e():T.then((function(){return e()}))}function E(){var e,n,r="app";var i=d.document.getElementById(r);if((t.version||"").startsWith("18")){var o=y.createRoot(i);null===(e=o.render)||void 0===e||e.call(o,b(x))}else null===(n=y.render)||void 0===n||n.call(y,b(x),i)}G(y);var x=function(n){function r(e){var t;return(0,o.Z)(this,r),t=(0,u.Z)(this,r,[e]),t.pages=[],t.elements=[],p=t,k(t),t}return(0,c.Z)(r,n),(0,a.Z)(r,[{key:"mount",value:function(e,n,r){var i=H(t,n)(e),o=n+q(),a=function(){return b(i,{key:o,tid:n})};this.pages.push(a),this.forceUpdate(r)}},{key:"unmount",value:function(e,t){var n=this.elements,r=n.findIndex((function(t){return t.props.tid===e}));n.splice(r,1),this.forceUpdate(t)}},{key:"render",value:function(){var t=this.pages,n=this.elements;while(t.length>0){var r=t.pop();n.push(r())}var i=null;return C&&(i={ref:w}),b(e,i,n.slice())}}])}(t.Component);E();var I=(0,i.Z)(l.PT.call("getMiniLifecycleImpl").app,3),A=I[0],N=I[1],L=I[2],B=Object.create({render:function(e){p.forceUpdate(e)},mount:function(e,t,n){p?p.mount(e,t,n):T.then((function(r){return r.mount(e,t,n)}))},unmount:function(e,t){p.unmount(e,t)}},(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({config:g({configurable:!0,value:s})},A,g({value:function(e){var t=this;m(e);var n=function(){var n,r=S();if(t.$app=r,r){if(r.taroGlobalData){var i=r.taroGlobalData,o=Object.keys(i),a=Object.getOwnPropertyDescriptors(i);o.forEach((function(e){Object.defineProperty(t,e,{configurable:!0,enumerable:!0,get:function(){return i[e]},set:function(t){i[e]=t}})})),Object.defineProperties(t,a)}null===(n=r.onLaunch)||void 0===n||n.call(r,e)}O("onLaunch",e)};P(n)}})),N,g({value:function(e){m(e);var t=function(){var t,n=S();null===(t=null===n||void 0===n?void 0:n.componentDidShow)||void 0===t||t.call(n,e),O("onShow",e)};P(t)}})),L,g({value:function(){var e=function(){var e,t=S();null===(e=null===t||void 0===t?void 0:t.componentDidHide)||void 0===e||e.call(t),O("onHide")};P(e)}})),"onError",g({value:function(e){var t=function(){var t,n=S();null===(t=null===n||void 0===n?void 0:n.onError)||void 0===t||t.call(n,e),O("onError",e)};P(t)}})),"onUnhandledRejection",g({value:function(e){var t=function(){var t,n=S();null===(t=null===n||void 0===n?void 0:n.onUnhandledRejection)||void 0===t||t.call(n,e),O("onUnhandledRejection",e)};P(t)}})),"onPageNotFound",g({value:function(e){var t=function(){var t,n=S();null===(t=null===n||void 0===n?void 0:n.onPageNotFound)||void 0===t||t.call(n,e),O("onPageNotFound",e)};P(t)}})));function O(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var i=(0,d.getPageInstance)(h);if(i){var o=S(),a=l.PT.call("getLifecycle",i,e);Array.isArray(a)&&a.forEach((function(e){return e.apply(o,n)}))}}return d.Current.app=B,B}(0,d.incrementId)();l.PT.tap("initNativeApi",(function(e){for(var t in W)e[t]=W[t]}))},71515:function(e,t,n){"use strict";n.r(t),n.d(t,{Ad:function(){return R},AdCustom:function(){return Y},Audio:function(){return N},Block:function(){return M},Button:function(){return c},Camera:function(){return L},Canvas:function(){return _},ChannelLive:function(){return re},Checkbox:function(){return s},CheckboxGroup:function(){return l},CoverImage:function(){return w},CoverView:function(){return T},CustomWrapper:function(){return V},Editor:function(){return z},Form:function(){return d},FunctionalPageNavigator:function(){return q},Icon:function(){return i},Image:function(){return B},Input:function(){return f},KeyboardAccessory:function(){return te},Label:function(){return h},LivePlayer:function(){return O},LivePusher:function(){return G},Map:function(){return D},MatchMedia:function(){return W},MovableArea:function(){return S},MovableView:function(){return P},NativeSlot:function(){return U},NavigationBar:function(){return J},Navigator:function(){return A},OfficialAccount:function(){return H},OpenData:function(){return $},PageContainer:function(){return X},PageMeta:function(){return K},Picker:function(){return v},PickerView:function(){return p},PickerViewColumn:function(){return g},Progress:function(){return o},Radio:function(){return m},RadioGroup:function(){return b},RichText:function(){return a},RootPortal:function(){return ne},ScrollView:function(){return E},ShareElement:function(){return ee},Slider:function(){return y},Slot:function(){return F},Swiper:function(){return x},SwiperItem:function(){return I},Switch:function(){return k},Text:function(){return u},Textarea:function(){return C},Video:function(){return Z},View:function(){return r},VoipRoom:function(){return Q},WebView:function(){return j}});var r="view",i="icon",o="progress",a="rich-text",u="text",c="button",s="checkbox",l="checkbox-group",d="form",f="input",h="label",v="picker",p="picker-view",g="picker-view-column",m="radio",b="radio-group",y="slider",k="switch",w="cover-image",C="textarea",T="cover-view",S="movable-area",P="movable-view",E="scroll-view",x="swiper",I="swiper-item",A="navigator",N="audio",L="camera",B="image",O="live-player",Z="video",_="canvas",R="ad",j="web-view",M="block",D="map",F="slot",U="native-slot",V="custom-wrapper",z="editor",W="match-media",q="functional-page-navigator",G="live-pusher",H="official-account",$="open-data",J="navigation-bar",K="page-meta",Q="voip-room",Y="ad-custom",X="page-container",ee="share-element",te="keyboard-accessory",ne="root-portal",re="channel-live"},17488:function(e,t,n){"use strict";var r=n(74455),i=new Set(["authPrivateMessage","chooseContact","cropImage","disableAlertBeforeUnload","editImage","enableAlertBeforeUnload","getBackgroundFetchData","getFuzzyLocation","getGroupEnterInfo","getLocalIPAddress","getShareInfo","getUserProfile","getWeRunData","join1v1Chat","openCustomerServiceChat","openVideoEditor","saveFileToDisk","scanItem","setEnable1v1Chat","setWindowSize","sendBizRedPacket","startFacialRecognitionVerify"]);function o(e){(0,r.Ig)(e,wx,{needPromiseApis:i,modifyApis:function(e){e.delete("lanDebug")},transformMeta:function(e,t){var n;return"showShareMenu"===e&&(t.menus=null===(n=t.showShareItems)||void 0===n?void 0:n.map((function(e){return"wechatFriends"===e?"shareAppMessage":"wechatMoment"===e?"shareTimeline":e}))),{key:e,options:t}}}),e.cloud=wx.cloud,e.getTabBar=function(e){var t;if("function"===typeof(null===e||void 0===e?void 0:e.getTabBar))return null===(t=e.getTabBar())||void 0===t?void 0:t.$taroInstances}}var a="true",u="false",c="",s="0",l={Progress:{"border-radius":s,"font-size":"16",duration:"30",bindActiveEnd:c},RichText:{space:c,"user-select":u},Text:{"user-select":u},Map:{polygons:"[]",subkey:c,rotate:s,skew:s,"max-scale":"20","min-scale":"3","enable-3D":u,"show-compass":u,"show-scale":u,"enable-overlooking":u,"enable-zoom":a,"enable-scroll":a,"enable-rotate":u,"enable-satellite":u,"enable-traffic":u,"enable-poi":a,"enable-building":a,setting:"[]",bindLabelTap:c,bindRegionChange:c,bindPoiTap:c,bindAnchorPointTap:c},Button:{lang:"en","session-from":c,"send-message-title":c,"send-message-path":c,"send-message-img":c,"app-parameter":c,"show-message-card":u,"business-id":c,bindGetUserInfo:c,bindContact:c,bindGetPhoneNumber:c,bindChooseAvatar:c,bindError:c,bindOpenSetting:c,bindLaunchApp:c},Form:{"report-submit-timeout":s},Input:{"always-embed":u,"adjust-position":a,"hold-keyboard":u,"safe-password-cert-path":"","safe-password-length":"","safe-password-time-stamp":"","safe-password-nonce":"","safe-password-salt":"","safe-password-custom-hash":"","auto-fill":c,bindKeyboardHeightChange:c,bindNicknameReview:c},Picker:{"header-text":c},PickerView:{"immediate-change":u,bindPickStart:c,bindPickEnd:c},Slider:{color:"'#e9e9e9'","selected-color":"'#1aad19'"},Textarea:{"show-confirm-bar":a,"adjust-position":a,"hold-keyboard":u,"disable-default-padding":u,"confirm-type":"'return'","confirm-hold":u,bindKeyboardHeightChange:c},ScrollView:{"enable-flex":u,"scroll-anchoring":u,"refresher-enabled":u,"refresher-threshold":"45","refresher-default-style":"'black'","refresher-background":"'#FFF'","refresher-triggered":u,enhanced:u,bounces:a,"show-scrollbar":a,"paging-enabled":u,"fast-deceleration":u,bindDragStart:c,bindDragging:c,bindDragEnd:c,bindRefresherPulling:c,bindRefresherRefresh:c,bindRefresherRestore:c,bindRefresherAbort:c},Swiper:{"snap-to-edge":u,"easing-function":"'default'"},SwiperItem:{"skip-hidden-item-layout":u},Navigator:{target:"'self'","app-id":c,path:c,"extra-data":c,version:"'version'"},Camera:{mode:"'normal'",resolution:"'medium'","frame-size":"'medium'",bindInitDone:c,bindScanCode:c},Image:{webp:u,"show-menu-by-longpress":u},LivePlayer:{mode:"'live'","sound-mode":"'speaker'","auto-pause-if-navigate":a,"auto-pause-if-open-native":a,"picture-in-picture-mode":"[]",bindstatechange:c,bindfullscreenchange:c,bindnetstatus:c,bindAudioVolumeNotify:c,bindEnterPictureInPicture:c,bindLeavePictureInPicture:c},Video:{title:c,"play-btn-position":"'bottom'","enable-play-gesture":u,"auto-pause-if-navigate":a,"auto-pause-if-open-native":a,"vslide-gesture":u,"vslide-gesture-in-fullscreen":a,"ad-unit-id":c,"poster-for-crawler":c,"show-casting-button":u,"picture-in-picture-mode":"[]","enable-auto-rotation":u,"show-screen-lock-button":u,"show-snapshot-button":u,"show-background-playback-button":u,"background-poster":c,bindProgress:c,bindLoadedMetadata:c,bindControlsToggle:c,bindEnterPictureInPicture:c,bindLeavePictureInPicture:c,bindSeekComplete:c,bindAdLoad:c,bindAdError:c,bindAdClose:c,bindAdPlay:c},Canvas:{type:c},Ad:{"ad-type":"'banner'","ad-theme":"'white'"},CoverView:{"marker-id":c,slot:c},Editor:{"read-only":u,placeholder:c,"show-img-size":u,"show-img-toolbar":u,"show-img-resize":u,focus:u,bindReady:c,bindFocus:c,bindBlur:c,bindInput:c,bindStatusChange:c,name:c},MatchMedia:{"min-width":c,"max-width":c,width:c,"min-height":c,"max-height":c,height:c,orientation:c},FunctionalPageNavigator:{version:"'release'",name:c,args:c,bindSuccess:c,bindFail:c,bindCancel:c},LivePusher:{url:c,mode:"'RTC'",autopush:u,muted:u,"enable-camera":a,"auto-focus":a,orientation:"'vertical'",beauty:s,whiteness:s,aspect:"'9:16'","min-bitrate":"200","max-bitrate":"1000","audio-quality":"'high'","waiting-image":c,"waiting-image-hash":c,zoom:u,"device-position":"'front'","background-mute":u,mirror:u,"remote-mirror":u,"local-mirror":u,"audio-reverb-type":s,"enable-mic":a,"enable-agc":u,"enable-ans":u,"audio-volume-type":"'voicecall'","video-width":"360","video-height":"640","beauty-style":"'smooth'",filter:"'standard'",animation:c,bindStateChange:c,bindNetStatus:c,bindBgmStart:c,bindBgmProgress:c,bindBgmComplete:c,bindAudioVolumeNotify:c},OfficialAccount:{bindLoad:c,bindError:c},OpenData:{type:c,"open-gid":c,lang:"'en'","default-text":c,"default-avatar":c,bindError:c},NavigationBar:{title:c,loading:u,"front-color":c,"background-color":c,"color-animation-duration":s,"color-animation-timing-func":"'linear'"},PageMeta:{"background-text-style":c,"background-color":c,"background-color-top":c,"background-color-bottom":c,"scroll-top":"''","scroll-duration":"300","page-style":"''","root-font-size":"''",bindResize:c,bindScroll:c,bindScrollDone:c},VoipRoom:{openid:c,mode:"'camera'","device-position":"'front'",bindError:c},AdCustom:{"unit-id":c,"ad-intervals":c,bindLoad:c,bindError:c},PageContainer:{show:u,duration:"300","z-index":"100",overlay:a,position:"'bottom'",round:u,"close-on-slide-down":u,"overlay-style":c,"custom-style":c,bindBeforeEnter:c,bindEnter:c,bindAfterEnter:c,bindBeforeLeave:c,bindLeave:c,bindAfterLeave:c,bindClickOverlay:c},ShareElement:{mapkey:c,transform:u,duration:"300","easing-function":"'ease-out'"},KeyboardAccessory:{},RootPortal:{},ChannelLive:{feedId:c,finderUserName:c}},d={initNativeApi:o,getMiniLifecycle:function(e){var t=e.page[5];return-1===t.indexOf("onSaveExitState")&&t.push("onSaveExitState"),e}};(0,r.xi)(d),(0,r.ku)(l)},88967:function(e,t,n){"use strict";n.r(t),n.d(t,{createPortal:function(){return I},createRoot:function(){return T},default:function(){return A},findDOMNode:function(){return E},render:function(){return C},unmountComponentAtNode:function(){return P},unstable_batchedUpdates:function(){return S}});var r=n(57042),i=n(24460),o=n(74455),a=n(32180),u=n(15104),c=n.n(u),s=n(87503);function l(e){return"o"===e[0]&&"n"===e[1]}var d=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function f(e,t,n){var r=v(e,t,n);r&&h(e,t,r)}function h(e,t,n){for(var r=0;r<n.length;r+=2){var i=n[r],o=n[r+1],a=t[i];m(e,i,o,a)}}function v(e,t,n){var r,i=null;for(r in t)r in n||(i=i||[]).push(r,null);var o=e instanceof a.FormElement;for(r in n)(t[r]!==n[r]||o&&"value"===r)&&(i=i||[]).push(r,n[r]);return i}function p(e,t,n,r){var i=t.endsWith("Capture"),a=t.toLowerCase().slice(2);i&&(a=a.slice(0,-7));var u=(0,o.kC)((0,o.CA)(e.tagName.toLowerCase()));"click"===a&&u in o.rD&&(a="tap"),(0,o.mf)(n)?r?(e.removeEventListener(a,r,!1),e.addEventListener(a,n,{isCapture:i,sideEffect:!1})):e.addEventListener(a,n,i):e.removeEventListener(a,r)}function g(e,t,n){"-"!==t[0]?e[t]=(0,o.hj)(n)&&!1===d.test(t)?n+"px":null==n?"":n:e.setProperty(t,n.toString())}function m(e,t,n,r){var i,a;if(t="className"===t?"class":t,"key"===t||"children"===t||"ref"===t);else if("style"===t){var u=e.style;if((0,o.HD)(n))u.cssText=n;else{if((0,o.HD)(r)&&(u.cssText="",r=null),(0,o.Kn)(r))for(var c in r)n&&c in n||g(u,c,"");if((0,o.Kn)(n))for(var s in n)r&&n[s]===r[s]||g(u,s,n[s])}}else if(l(t))p(e,t,n,r);else if("dangerouslySetInnerHTML"===t){var d=null!==(i=null===n||void 0===n?void 0:n.__html)&&void 0!==i?i:"",f=null!==(a=null===r||void 0===r?void 0:r.__html)&&void 0!==a?a:"";(d||f)&&f!==d&&(e.innerHTML=d)}else(0,o.mf)(n)||(null==n?e.removeAttribute(t):e.setAttribute(t,n))}var b={getPublicInstance:function(e){return e},getRootHostContext:function(){return{}},getChildHostContext:function(e){return e},prepareForCommit:function(){return null},resetAfterCommit:o.ZT,createInstance:function(e){return a.document.createElement(e)},appendInitialChild:function(e,t){e.appendChild(t)},finalizeInitialChildren:function(e,t,n){return f(e,{},n),!1},prepareUpdate:function(e,t,n,r){return v(e,n,r)},shouldSetTextContent:function(){return!1},createTextInstance:function(e){return a.document.createTextNode(e)},scheduleTimeout:setTimeout,cancelTimeout:clearTimeout,noTimeout:-1,isPrimaryRenderer:!0,warnsIfNotActing:!0,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,getInstanceFromNode:function(){return null},beforeActiveInstanceBlur:o.ZT,afterActiveInstanceBlur:o.ZT,preparePortalMount:o.ZT,prepareScopeUpdate:o.ZT,getInstanceFromScope:function(){return null},getCurrentEventPriority:function(){return s.DefaultEventPriority},detachDeletedInstance:o.ZT,supportsMicrotasks:!0,scheduleMicrotask:(0,o.o8)(Promise)?setTimeout:function(e){return Promise.resolve(null).then(e).catch((function(e){setTimeout((function(){throw e}))}))},appendChild:function(e,t){e.appendChild(t)},appendChildToContainer:function(e,t){e.appendChild(t)},commitTextUpdate:function(e,t,n){e.nodeValue=n},commitMount:o.ZT,commitUpdate:function(e,t,n,r){h(e,r,t)},insertBefore:function(e,t,n){e.insertBefore(t,n)},insertInContainerBefore:function(e,t,n){e.insertBefore(t,n)},removeChild:function(e,t){e.removeChild(t)},removeChildFromContainer:function(e,t){e.removeChild(t)},resetTextContent:o.ZT,hideInstance:function(e){var t=e.style;t.setProperty("display","none")},hideTextInstance:function(e){e.nodeValue=""},unhideInstance:function(e,t){var n=t.style,r=(null===n||void 0===n?void 0:n.hasOwnProperty("display"))?n.display:null;r=null==r||(0,o.jn)(r)||""===r?"":(""+r).trim(),e.style["display"]=r},unhideTextInstance:function(e,t){e.nodeValue=t},clearContainer:function(e){e.childNodes.length>0&&(e.textContent="")}},y=c()(b),k=new WeakMap,w=function(){function e(t,n,i){(0,r.Z)(this,e),this.renderer=t,this.initInternalRoot(t,n,i)}return(0,i.Z)(e,[{key:"initInternalRoot",value:function(e,t,n){var r=t;if(n){var i=1,o=!1,a=!1,u="",c=function(e){return console.error(e)},s=null;!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(u=n.identifierPrefix),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&(s=n.unstable_transitionCallbacks),this.internalRoot=e.createContainer(r,i,null,a,o,u,c,s)}else{var l=0;this.internalRoot=e.createContainer(r,l,null,!1,!1,"",(function(){}),null)}}},{key:"render",value:function(e,t){var n=this.renderer,r=this.internalRoot;return n.updateContainer(e,r,null,t),n.getPublicRootInstance(r)}},{key:"unmount",value:function(e){this.renderer.updateContainer(null,this.internalRoot,null,e)}}])}();function C(e,t,n){var r=k.get(t);if(null!=r)return r.render(e,n);var i=new w(y,t);return k.set(t,i),i.render(e,n)}function T(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=k.get(e);if(null!=n)return n;var r=new w(y,e,t);return k.set(e,r),r}var S=y.batchedUpdates;function P(e){(0,o.zx)(e&&[1,8,9,11].includes(e.nodeType),"unmountComponentAtNode(...): Target container is not a DOM element.");var t=k.get(e);return!!t&&(S((function(){t.unmount((function(){k.delete(e)}))}),null),!0)}function E(e){if(null==e)return null;var t=e.nodeType;return 1===t||3===t?e:y.findHostInstance(e)}var x=(0,o.mf)(Symbol)&&Symbol.for?Symbol.for("react.portal"):60106;function I(e,t,n){return{$$typeof:x,key:null==n?null:String(n),children:e,containerInfo:t,implementation:null}}var A={render:C,createRoot:T,unstable_batchedUpdates:S,unmountComponentAtNode:P,findDOMNode:E,createPortal:I}},32180:function(e,t,n){"use strict";n.r(t),n.d(t,{Events:function(){return v.zW},hooks:function(){return v.PT},Current:function(){return wn},FormElement:function(){return Qt},MutationObserver:function(){return ce},SVGElement:function(){return kn},Style:function(){return et},TaroElement:function(){return it},TaroEvent:function(){return Ht},TaroNode:function(){return Ne},TaroRootElement:function(){return nn},TaroText:function(){return rn},addLeadingSlash:function(){return In},cancelAnimationFrame:function(){return mn},createComponentConfig:function(){return Rn},createEvent:function(){return $t},createPageConfig:function(){return _n},createRecursiveComponentConfig:function(){return jn},document:function(){return tn},eventCenter:function(){return bn},eventHandler:function(){return Kt},eventSource:function(){return Se},getComputedStyle:function(){return un},getCurrentInstance:function(){return Cn},getPageInstance:function(){return En},hydrate:function(){return Ee},incrementId:function(){return se},injectPageInstance:function(){return Pn},navigator:function(){return hn},nextTick:function(){return Mn},now:function(){return cn},options:function(){return ot},removePageInstance:function(){return xn},requestAnimationFrame:function(){return gn},safeExecute:function(){return An},stringify:function(){return Nn},window:function(){return vn}});var r=n(90129),i=n(92490),o=n(27415),a=n(45023),u=n(20597),c=n(21867),s=n(91865),l=n(28009),d=n(22276),f=n(57042),h=n(24460),v=n(74455),p=n(32180)["requestAnimationFrame"],g=n(32180)["cancelAnimationFrame"],m="\u5c0f\u7a0b\u5e8f setData",b="\u9875\u9762\u521d\u59cb\u5316",y="root",k="html",w="head",C="body",T="app",S="container",P="#document",E="document-fragment",x="id",I="uid",A="class",N="style",L="focus",B="view",O="static-view",Z="pure-view",_="value",R="input",j="change",M="custom-wrapper",D="target",F="currentTarget",U="type",V="confirm",z="timeStamp",W="keyCode",q="touchmove",G="catchMove",H="catch-view",$="comment",J="onLoad",K="onReady",Q="onShow",Y="onHide",X="options",ee="externalClasses",te="behaviors",ne=[],re=function(e,t){return!!e&&e.sid===(null===t||void 0===t?void 0:t.sid)},ie=function(e,t){var n=t.characterData,r=t.characterDataOldValue,i=t.attributes,o=t.attributeOldValue,a=t.childList;switch(e.type){case"characterData":return!!n&&(r||(e.oldValue=null),!0);case"attributes":return!!i&&(o||(e.oldValue=null),!0);case"childList":return!!a}},oe=!1;function ae(e,t){e.records.push(t),oe||(oe=!0,Promise.resolve().then((function(){oe=!1,ne.forEach((function(e){return e.callback(e.takeRecords())}))})))}function ue(e){ne.forEach((function(t){for(var n=t.options,r=e.target;r;r=r.parentNode){if(re(t.target,r)&&ie(e,n)){ae(t,e);break}if(!n.subtree)break}}))}var ce=function(){function e(t){(0,f.Z)(this,e),this.core={observe:v.ZT,disconnect:v.ZT,takeRecords:v.ZT}}return(0,h.Z)(e,[{key:"observe",value:function(){var e;(e=this.core).observe.apply(e,arguments)}},{key:"disconnect",value:function(){this.core.disconnect()}},{key:"takeRecords",value:function(){return this.core.takeRecords()}}],[{key:"record",value:function(e){ue(e)}}])}(),se=function(){for(var e=[],t=65;t<=90;t++)e.push(t);for(var n=97;n<=122;n++)e.push(n);var r=e.length-1,i=[0,0];return function(){var t=i.map((function(t){return e[t]})),n=String.fromCharCode.apply(String,(0,d.Z)(t)),o=i.length-1;i[o]++;while(i[o]>r){if(i[o]=0,o-=1,o<0){i.push(0);break}i[o]++}return n}};function le(e){return 1===e.nodeType}function de(e){return 3===e.nodeType}function fe(e){return e.nodeName===$}function he(e){var t=Object.keys(e.props).find((function(e){return!(/^(class|style|id)$/.test(e)||e.startsWith("data-"))}));return Boolean(t)}function ve(e,t){var n,r=!1;while((null===e||void 0===e?void 0:e.parentElement)&&e.parentElement._path!==y){if(null===(n=e.parentElement.__handlers[t])||void 0===n?void 0:n.length){r=!0;break}e=e.parentElement}return r}function pe(e){switch(e){case N:return"st";case x:return I;case A:return"cl";default:return e}}var ge,me=new Map;function be(e,t,n){(0,v.mf)(n)&&(n={value:n}),Object.defineProperty(e.prototype,t,Object.assign({configurable:!0,enumerable:!0},n))}function ye(){return ge||(ge=(0,v.W)(v.rD)),ge}var ke,we,Ce=function(e){function t(e,n){var r;return(0,f.Z)(this,t),r=(0,u.Z)(this,t),e.trim().split(/\s+/).forEach((0,s.Z)(t,"add",r,1).bind(r)),r.el=n,r}return(0,c.Z)(t,e),(0,h.Z)(t,[{key:"value",get:function(){return(0,d.Z)(this).filter((function(e){return""!==e})).join(" ")}},{key:"add",value:function(e){return(0,s.Z)(t,"add",this,3)([e]),this._update(),this}},{key:"length",get:function(){return this.size}},{key:"remove",value:function(e){(0,s.Z)(t,"delete",this,3)([e]),this._update()}},{key:"toggle",value:function(e){(0,s.Z)(t,"has",this,3)([e])?(0,s.Z)(t,"delete",this,3)([e]):(0,s.Z)(t,"add",this,3)([e]),this._update()}},{key:"replace",value:function(e,n){(0,s.Z)(t,"delete",this,3)([e]),(0,s.Z)(t,"add",this,3)([n]),this._update()}},{key:"contains",value:function(e){return(0,s.Z)(t,"has",this,3)([e])}},{key:"toString",value:function(){return this.value}},{key:"_update",value:function(){this.el.className=this.value}}])}((0,l.Z)(Set)),Te=function(e){function t(){return(0,f.Z)(this,t),(0,u.Z)(this,t,arguments)}return(0,c.Z)(t,e),(0,h.Z)(t,[{key:"removeNode",value:function(e){var t=e.sid,n=e.uid;this.delete(t),n!==t&&n&&this.delete(n)}},{key:"removeNodeTree",value:function(e){var t=this;this.removeNode(e);var n=e.childNodes;n.forEach((function(e){return t.removeNodeTree(e)}))}}])}((0,l.Z)(Map)),Se=new Te,Pe={window:v.kT,document:v.kT};function Ee(e){we||(we=ye()),ke||(ke=v.PT.call("getSpecialNodes"));var t=e.nodeName;if(de(e))return(0,a.Z)((0,a.Z)({},"v",e.nodeValue),"nn",we[t]._num);var n=(0,a.Z)((0,a.Z)({},"nn",t),"sid",e.sid);e.uid!==e.sid&&(n.uid=e.uid),!e.isAnyEventBinded()&&ke.indexOf(t)>-1&&(n["nn"]="static-".concat(t),t!==B||he(e)||(n["nn"]=Z));var r=e.props;for(var i in r){var o=(0,v.CA)(i);i.startsWith("data-")||i===A||i===N||i===x||o===G||(n[o]=r[i]),t===B&&o===G&&!1!==r[i]&&(n["nn"]=H)}var u=e.childNodes;u=u.filter((function(e){return!fe(e)})),u.length>0?n["cn"]=u.map(Ee):n["cn"]=[],""!==e.className&&(n["cl"]=e.className);var c=e.cssText;""!==c&&"swiper-item"!==t&&(n["st"]=c),v.PT.call("modifyHydrateData",n);var s=n["nn"],l=we[s];if(l)for(var d in n["nn"]=l._num,n)d in l&&(n[l[d]]=n[d],delete n[d]);return n}var xe=function(){function e(){(0,f.Z)(this,e),this.__handlers={}}return(0,h.Z)(e,[{key:"addEventListener",value:function(e,t,n){if(e=e.toLowerCase(),v.PT.call("onAddEvent",e,t,n,this),"regionchange"===e)return this.addEventListener("begin",t,n),void this.addEventListener("end",t,n);Boolean(n);var r=!1;if((0,v.Kn)(n)&&(Boolean(n.capture),r=Boolean(n.once)),r){var i=function(){t.apply(this,arguments),this.removeEventListener(e,i)};this.addEventListener(e,i,Object.assign(Object.assign({},n),{once:!1}))}else{var o=t;t=function(){o.apply(this,arguments)},t.oldHandler=o;var a=this.__handlers[e];(0,v.kJ)(a)?a.push(t):this.__handlers[e]=[t]}}},{key:"removeEventListener",value:function(e,t){if(e=e.toLowerCase(),"regionchange"===e)return this.removeEventListener("begin",t),void this.removeEventListener("end",t);if(t){var n=this.__handlers[e];if((0,v.kJ)(n)){var r=n.findIndex((function(e){if(e===t||e.oldHandler===t)return!0}));n.splice(r,1)}}}},{key:"isAnyEventBinded",value:function(){var e=this.__handlers,t=Object.keys(e).find((function(t){return e[t].length}));return Boolean(t)}}])}(),Ie="cn",Ae=se(),Ne=function(e){function t(){var e;return(0,f.Z)(this,t),e=(0,u.Z)(this,t),e.parentNode=null,e.childNodes=[],e.hydrate=function(e){return function(){return Ee(e)}},e.uid="_"+Ae(),e.sid=e.uid,Se.set(e.sid,e),e}return(0,c.Z)(t,e),(0,h.Z)(t,[{key:"updateChildNodes",value:function(e){var t=this,n=function(){return[]},r=function(){var e=t.childNodes.filter((function(e){return!fe(e)}));return e.map(Ee)};this.enqueueUpdate({path:"".concat(this._path,".").concat(Ie),value:e?n:r})}},{key:"_root",get:function(){var e;return(null===(e=this.parentNode)||void 0===e?void 0:e._root)||null}},{key:"findIndex",value:function(e){var t=this.childNodes.indexOf(e);return(0,v.zx)(-1!==t,"The node to be replaced is not a child of this node."),t}},{key:"_path",get:function(){var e=this.parentNode;if(e){var t=e.childNodes.filter((function(e){return!fe(e)})),n=t.indexOf(this),r=v.PT.call("getPathIndex",n);return"".concat(e._path,".").concat(Ie,".").concat(r)}return""}},{key:"nextSibling",get:function(){var e=this.parentNode;return(null===e||void 0===e?void 0:e.childNodes[e.findIndex(this)+1])||null}},{key:"previousSibling",get:function(){var e=this.parentNode;return(null===e||void 0===e?void 0:e.childNodes[e.findIndex(this)-1])||null}},{key:"parentElement",get:function(){var e=this.parentNode;return 1===(null===e||void 0===e?void 0:e.nodeType)?e:null}},{key:"firstChild",get:function(){return this.childNodes[0]||null}},{key:"lastChild",get:function(){var e=this.childNodes;return e[e.length-1]||null}},{key:"textContent",set:function(e){var t=this.childNodes.slice(),n=[];while(this.firstChild)this.removeChild(this.firstChild,{doUpdate:!1});if(""===e)this.updateChildNodes(!0);else{var r=Pe.document.createTextNode(e);n.push(r),this.appendChild(r),this.updateChildNodes()}ce.record({type:"childList",target:this,removedNodes:t,addedNodes:n})}},{key:"insertBefore",value:function(e,t,n){var r=this;if(e.nodeName===E)return e.childNodes.reduceRight((function(e,t){return r.insertBefore(t,e),t}),t),e;if(e.remove({cleanRef:!1}),e.parentNode=this,t){var i=this.findIndex(t);this.childNodes.splice(i,0,e)}else this.childNodes.push(e);if(this._root)if(t)n?this.enqueueUpdate({path:e._path,value:this.hydrate(e)}):this.updateChildNodes();else{var o=1===this.childNodes.length;o?this.updateChildNodes():this.enqueueUpdate({path:e._path,value:this.hydrate(e)})}return ce.record({type:"childList",target:this,addedNodes:[e],removedNodes:n?[t]:[],nextSibling:n?t.nextSibling:t||null,previousSibling:e.previousSibling}),e}},{key:"appendChild",value:function(e){return this.insertBefore(e)}},{key:"replaceChild",value:function(e,t){if(t.parentNode===this)return this.insertBefore(e,t,!0),t.remove({doUpdate:!1}),t}},{key:"removeChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.cleanRef,r=t.doUpdate;!1!==n&&!1!==r&&ce.record({type:"childList",target:this,removedNodes:[e],nextSibling:e.nextSibling,previousSibling:e.previousSibling});var i=this.findIndex(e);return this.childNodes.splice(i,1),e.parentNode=null,!1!==n&&Se.removeNodeTree(e),this._root&&!1!==r&&this.updateChildNodes(),e}},{key:"remove",value:function(e){var t;null===(t=this.parentNode)||void 0===t||t.removeChild(this,e)}},{key:"hasChildNodes",value:function(){return this.childNodes.length>0}},{key:"enqueueUpdate",value:function(e){var t;null===(t=this._root)||void 0===t||t.enqueueUpdate(e)}},{key:"ownerDocument",get:function(){return Pe.document}}],[{key:"extend",value:function(e,n){be(t,e,n)}}])}(xe),Le="webkit",Be=["all","appearance","blockOverflow","blockSize","bottom","clear","contain","content","continue","cursor","direction","display","filter","float","gap","height","inset","isolation","left","letterSpacing","lightingColor","markerSide","mixBlendMode","opacity","order","position","quotes","resize","right","rowGap","tabSize","tableLayout","top","userSelect","verticalAlign","visibility","voiceFamily","volume","whiteSpace","widows","width","zIndex","pointerEvents"];function Oe(e,t,n){!n&&Be.push(e),t.forEach((function(t){Be.push(e+t),e===Le&&Be.push("Webkit"+t)}))}var Ze="Color",_e="Style",Re="Width",je="Image",Me="Size",De=[Ze,_e,Re],Fe=["FitLength","FitWidth",je],Ue=[].concat(Fe,["Radius"]),Ve=[].concat(De,Fe),ze=["EndRadius","StartRadius"],We=["Bottom","Left","Right","Top"],qe=["End","Start"],Ge=["Content","Items","Self"],He=["BlockSize","Height","InlineSize",Re],$e=["After","Before"];function Je(e){ce.record({type:"attributes",target:e._element,attributeName:"style",oldValue:e.cssText})}function Ke(e){var t=e._element;t._root&&t.enqueueUpdate({path:"".concat(t._path,".","st"),value:e.cssText})}function Qe(e,t){var n=this[t];n!==e&&(!this._pending&&Je(this),(0,v.Ft)(e)||(0,v.o8)(e)?(this._usedStyleProp.delete(t),delete this._value[t]):(this._usedStyleProp.add(t),this._value[t]=e),!this._pending&&Ke(this))}function Ye(e){for(var t={},n=function(){var e=Be[r];t[e]={get:function(){var t=this._value[e];return(0,v.Ft)(t)||(0,v.o8)(t)?"":t},set:function(t){Qe.call(this,t,e)}}},r=0;r<Be.length;r++)n();Object.defineProperties(e.prototype,t)}function Xe(e){return/^--/.test(e)}Oe("borderBlock",De),Oe("borderBlockEnd",De),Oe("borderBlockStart",De),Oe("outline",[].concat(De,["Offset"])),Oe("border",[].concat(De,["Boundary","Break","Collapse","Radius","Spacing"])),Oe("borderFit",["Length",Re]),Oe("borderInline",De),Oe("borderInlineEnd",De),Oe("borderInlineStart",De),Oe("borderLeft",Ve),Oe("borderRight",Ve),Oe("borderTop",Ve),Oe("borderBottom",Ve),Oe("textDecoration",[Ze,_e,"Line"]),Oe("textEmphasis",[Ze,_e,"Position"]),Oe("scrollMargin",We),Oe("scrollPadding",We),Oe("padding",We),Oe("margin",[].concat(We,["Trim"])),Oe("scrollMarginBlock",qe),Oe("scrollMarginInline",qe),Oe("scrollPaddingBlock",qe),Oe("scrollPaddingInline",qe),Oe("gridColumn",qe),Oe("gridRow",qe),Oe("insetBlock",qe),Oe("insetInline",qe),Oe("marginBlock",qe),Oe("marginInline",qe),Oe("paddingBlock",qe),Oe("paddingInline",qe),Oe("pause",$e),Oe("cue",$e),Oe("mask",["Clip","Composite",je,"Mode","Origin","Position","Repeat",Me,"Type"]),Oe("borderImage",["Outset","Repeat","Slice","Source","Transform",Re]),Oe("maskBorder",["Mode","Outset","Repeat","Slice","Source",Re]),Oe("font",["Family","FeatureSettings","Kerning","LanguageOverride","MaxSize","MinSize","OpticalSizing","Palette",Me,"SizeAdjust","Stretch",_e,"Weight","VariationSettings"]),Oe("transform",["Box","Origin",_e]),Oe("background",[Ze,je,"Attachment","BlendMode","Clip","Origin","Position","Repeat",Me]),Oe("listStyle",[je,"Position","Type"]),Oe("scrollSnap",["Align","Stop","Type"]),Oe("grid",["Area","AutoColumns","AutoFlow","AutoRows"]),Oe("gridTemplate",["Areas","Columns","Rows"]),Oe("overflow",["Block","Inline","Wrap","X","Y"]),Oe("transition",["Delay","Duration","Property","TimingFunction"]),Oe("color",["Adjust","InterpolationFilters","Scheme"]),Oe("textAlign",["All","Last"]),Oe("page",["BreakAfter","BreakBefore","BreakInside"]),Oe("animation",["Delay","Direction","Duration","FillMode","IterationCount","Name","PlayState","TimingFunction"]),Oe("flex",["Basis","Direction","Flow","Grow","Shrink","Wrap"]),Oe("offset",[].concat($e,qe,["Anchor","Distance","Path","Position","Rotate"])),Oe("perspective",["Origin"]),Oe("clip",["Path","Rule"]),Oe("flow",["From","Into"]),Oe("align",["Content","Items","Self"],!0),Oe("alignment",["Adjust","Baseline"],!0),Oe("borderStart",ze,!0),Oe("borderEnd",ze,!0),Oe("borderCorner",["Fit",je,"ImageTransform"],!0),Oe("borderTopLeft",Ue,!0),Oe("borderTopRight",Ue,!0),Oe("borderBottomLeft",Ue,!0),Oe("borderBottomRight",Ue,!0),Oe("column",["s","Count","Fill","Gap","Rule","RuleColor","RuleStyle","RuleWidth","Span",Re],!0),Oe("break",[].concat($e,["Inside"]),!0),Oe("wrap",[].concat($e,["Flow","Inside","Through"]),!0),Oe("justify",Ge,!0),Oe("place",Ge,!0),Oe("max",[].concat(He,["Lines"]),!0),Oe("min",He,!0),Oe("line",["Break","Clamp","Grid","Height","Padding","Snap"],!0),Oe("inline",["BoxAlign",Me,"Sizing"],!0),Oe("text",["CombineUpright","GroupAlign","Height","Indent","Justify","Orientation","Overflow","Shadow","SpaceCollapse","SpaceTrim","Spacing","Transform","UnderlinePosition","Wrap"],!0),Oe("shape",["ImageThreshold","Inside","Margin","Outside"],!0),Oe("word",["Break","Spacing","Wrap"],!0),Oe("object",["Fit","Position"],!0),Oe("box",["DecorationBreak","Shadow","Sizing","Snap"],!0),Oe(Le,["LineClamp","BoxOrient","TextFillColor","TextStroke","TextStrokeColor","TextStrokeWidth"],!0);var et=function(){function e(t){(0,f.Z)(this,e),this._element=t,this._usedStyleProp=new Set,this._value={}}return(0,h.Z)(e,[{key:"setCssVariables",value:function(e){var t=this;this.hasOwnProperty(e)||Object.defineProperty(this,e,{enumerable:!0,configurable:!0,get:function(){return t._value[e]||""},set:function(n){Qe.call(t,n,e)}})}},{key:"cssText",get:function(){var e=this;if(!this._usedStyleProp.size)return"";var t=[];return this._usedStyleProp.forEach((function(n){var r=e[n];if(!(0,v.Ft)(r)&&!(0,v.o8)(r)){var i=Xe(n)?n:(0,v.eu)(n);0!==i.indexOf("webkit")&&0!==i.indexOf("Webkit")||(i="-".concat(i)),t.push("".concat(i,": ").concat(r,";"))}})),t.join(" ")},set:function(e){var t=this;if(this._pending=!0,Je(this),this._usedStyleProp.forEach((function(e){t.removeProperty(e)})),""===e||(0,v.o8)(e)||(0,v.Ft)(e))return this._pending=!1,void Ke(this);for(var n=e.split(";"),r=0;r<n.length;r++){var i=n[r].trim();if(""!==i){var a=i.split(":"),u=(0,o.Z)(a),c=u[0],s=u.slice(1),l=s.join(":");(0,v.o8)(l)||this.setProperty(c.trim(),l.trim())}}this._pending=!1,Ke(this)}},{key:"setProperty",value:function(e,t){"-"===e[0]?this.setCssVariables(e):e=(0,v.CA)(e),(0,v.Ft)(t)||(0,v.o8)(t)?this.removeProperty(e):this[e]=t}},{key:"removeProperty",value:function(e){if(e=(0,v.CA)(e),!this._usedStyleProp.has(e))return"";var t=this[e];return this[e]=void 0,t}},{key:"getPropertyValue",value:function(e){e=(0,v.CA)(e);var t=this[e];return t||""}}])}();function tt(){return!0}function nt(e,t){var n=[],r=null!==t&&void 0!==t?t:tt,i=e;while(i)1===i.nodeType&&r(i)&&n.push(i),i=rt(i,e);return n}function rt(e,t){var n=e.firstChild;if(n)return n;var r=e;do{if(r===t)return null;var i=r.nextSibling;if(i)return i;r=r.parentElement}while(r);return null}Ye(et);var it=function(e){function t(){var e;return(0,f.Z)(this,t),e=(0,u.Z)(this,t),e.props={},e.dataset=v.kT,e.nodeType=1,e.style=new et(e),v.PT.call("patchElement",e),e}return(0,c.Z)(t,e),(0,h.Z)(t,[{key:"_stopPropagation",value:function(e){var t=this;while(t=t.parentNode){var n=t.__handlers[e.type];if((0,v.kJ)(n))for(var r=n.length;r--;){var i=n[r];i._stop=!0}}}},{key:"id",get:function(){return this.getAttribute(x)},set:function(e){this.setAttribute(x,e)}},{key:"className",get:function(){return this.getAttribute(A)||""},set:function(e){this.setAttribute(A,e)}},{key:"cssText",get:function(){return this.getAttribute(N)||""}},{key:"classList",get:function(){return new Ce(this.className,this)}},{key:"children",get:function(){return this.childNodes.filter(le)}},{key:"attributes",get:function(){var e=this.props,t=Object.keys(e),n=this.style.cssText,r=t.map((function(t){return{name:t,value:e[t]}}));return r.concat(n?{name:N,value:n}:[])}},{key:"textContent",get:function(){for(var e="",t=this.childNodes,n=0;n<t.length;n++)e+=t[n].textContent;return e},set:function(e){(0,i.Z)(t,"textContent",e,this,1,1)}},{key:"hasAttribute",value:function(e){return!(0,v.o8)(this.props[e])}},{key:"hasAttributes",value:function(){return this.attributes.length>0}},{key:"focus",get:function(){return function(){this.setAttribute(L,!0)}},set:function(e){this.setAttribute(L,e)}},{key:"blur",value:function(){this.setAttribute(L,!1)}},{key:"setAttribute",value:function(e,t){var n=this.nodeName===B&&!he(this)&&!this.isAnyEventBinded();switch(e!==N&&ce.record({target:this,type:"attributes",attributeName:e,oldValue:this.getAttribute(e)}),e){case N:this.style.cssText=t;break;case x:this.uid!==this.sid&&Se.delete(this.uid),t=String(t),this.props[e]=this.uid=t,Se.set(t,this);break;default:this.props[e]=t,e.startsWith("data-")&&(this.dataset===v.kT&&(this.dataset=Object.create(null)),this.dataset[(0,v.CA)(e.replace(/^data-/,""))]=t);break}if(this._root){var r=ye(),i=r[this.nodeName],o=r[B]._num,a=r[O]._num,u=r[H]._num,c=this._path;e=pe(e);var s=(0,v.CA)(e),l={path:"".concat(c,".").concat(s),value:(0,v.mf)(t)?function(){return t}:t};if(v.PT.call("modifySetAttrPayload",this,e,l,r),i){var d=i[s]||e;l.path="".concat(c,".").concat((0,v.CA)(d))}this.enqueueUpdate(l),this.nodeName===B&&(s===G?this.enqueueUpdate({path:"".concat(c,".","nn"),value:t?u:this.isAnyEventBinded()?o:a}):n&&he(this)&&this.enqueueUpdate({path:"".concat(c,".","nn"),value:a}))}}},{key:"removeAttribute",value:function(e){var t=this.nodeName===B&&he(this)&&!this.isAnyEventBinded();if(ce.record({target:this,type:"attributes",attributeName:e,oldValue:this.getAttribute(e)}),e===N)this.style.cssText="";else{var n=v.PT.call("onRemoveAttribute",this,e);if(n)return;if(!this.props.hasOwnProperty(e))return;delete this.props[e]}if(this._root){var r=ye(),i=r[this.nodeName],o=r[B]._num,a=r[O]._num,u=r[Z]._num,c=this._path;e=pe(e);var s=(0,v.CA)(e),l={path:"".concat(c,".").concat(s),value:""};if(v.PT.call("modifyRmAttrPayload",this,e,l,r),i){var d=i[s]||e;l.path="".concat(c,".").concat((0,v.CA)(d))}this.enqueueUpdate(l),this.nodeName===B&&(s===G?this.enqueueUpdate({path:"".concat(c,".","nn"),value:this.isAnyEventBinded()?o:he(this)?a:u}):t&&!he(this)&&this.enqueueUpdate({path:"".concat(c,".","nn"),value:u}))}}},{key:"getAttribute",value:function(e){var t=e===N?this.style.cssText:this.props[e];return null!==t&&void 0!==t?t:""}},{key:"getElementsByTagName",value:function(e){var t=this;return nt(this,(function(n){return n.nodeName===e||"*"===e&&t!==n}))}},{key:"getElementsByClassName",value:function(e){return nt(this,(function(t){var n=t.classList,r=e.trim().split(/\s+/);return r.every((function(e){return n.has(e)}))}))}},{key:"dispatchEvent",value:function(e){var t=e.cancelable,n=this.__handlers[e.type];if(!(0,v.kJ)(n))return!1;for(var r=n.length;r--;){var i=n[r],o=void 0;if(i._stop?i._stop=!1:(v.PT.call("modifyDispatchEvent",e,this),o=i.call(this,e)),(!1===o||e._end)&&t&&(e.defaultPrevented=!0),e._end&&e._stop)break}return e._stop?this._stopPropagation(e):e._stop=!0,null!=n}},{key:"addEventListener",value:function(e,n,r){var i=this.nodeName,o=v.PT.call("getSpecialNodes"),a=!0;if((0,v.Kn)(r)&&!1===r.sideEffect&&(a=!1,delete r.sideEffect),!1!==a&&!this.isAnyEventBinded()&&o.indexOf(i)>-1){var u=ye(),c=u[i]._num;this.enqueueUpdate({path:"".concat(this._path,".","nn"),value:c})}(0,s.Z)(t,"addEventListener",this,3)([e,n,r])}},{key:"removeEventListener",value:function(e,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];(0,s.Z)(t,"removeEventListener",this,3)([e,n]);var i=this.nodeName,o=v.PT.call("getSpecialNodes");if(!1!==r&&!this.isAnyEventBinded()&&o.indexOf(i)>-1){var a=ye(),u=he(this)?"static-".concat(i):"pure-".concat(i),c=a[u]._num;this.enqueueUpdate({path:"".concat(this._path,".","nn"),value:c})}}}],[{key:"extend",value:function(e,n){be(t,e,n)}}])}(Ne),ot={prerender:!0,debug:!1};function at(){return{index:0,column:0,line:0}}function ut(e,t,n){for(var r=e.index,i=e.index=r+n,o=r;o<i;o++){var a=t.charAt(o);"\n"===a?(e.line++,e.column=0):e.column++}}function ct(e,t,n){var r=n-e.index;return ut(e,t,r)}function st(e){return{index:e.index,line:e.line,column:e.column}}var lt=/\s/;function dt(e){return lt.test(e)}var ft=/=/;function ht(e){return ft.test(e)}function vt(e){var t=e.toLowerCase();return!!ot.html.skipElements.has(t)}var pt=/[A-Za-z0-9]/;function gt(e,t){while(1){var n=e.indexOf("<",t);if(-1===n)return n;var r=e.charAt(n+1);if("/"===r||"!"===r||pt.test(r))return n;t=n+1}}function mt(e,t,n){if(!dt(n.charAt(e)))return!1;for(var r=n.length,i=e-1;i>t;i--){var o=n.charAt(i);if(!dt(o)){if(ht(o))return!1;break}}for(var a=e+1;a<r;a++){var u=n.charAt(a);if(!dt(u))return!ht(u)}}var bt=function(){function e(t){(0,f.Z)(this,e),this.tokens=[],this.position=at(),this.html=t}return(0,h.Z)(e,[{key:"scan",value:function(){var e=this.html,t=this.position,n=e.length;while(t.index<n){var r=t.index;if(this.scanText(),t.index===r){var i=e.startsWith("!--",r+1);if(i)this.scanComment();else{var o=this.scanTag();vt(o)&&this.scanSkipTag(o)}}}return this.tokens}},{key:"scanText",value:function(){var e="text",t=this.html,n=this.position,r=gt(t,n.index);if(r!==n.index){-1===r&&(r=t.length);var i=st(n),o=t.slice(n.index,r);ct(n,t,r);var a=st(n);this.tokens.push({type:e,content:o,position:{start:i,end:a}})}}},{key:"scanComment",value:function(){var e="comment",t=this.html,n=this.position,r=st(n);ut(n,t,4);var i=t.indexOf("--\x3e",n.index),o=i+3;-1===i&&(i=o=t.length);var a=t.slice(n.index,i);ct(n,t,o),this.tokens.push({type:e,content:a,position:{start:r,end:st(n)}})}},{key:"scanTag",value:function(){this.scanTagStart();var e=this.scanTagName();return this.scanAttrs(),this.scanTagEnd(),e}},{key:"scanTagStart",value:function(){var e="tag-start",t=this.html,n=this.position,r=t.charAt(n.index+1),i="/"===r,o=st(n);ut(n,t,i?2:1),this.tokens.push({type:e,close:i,position:{start:o}})}},{key:"scanTagEnd",value:function(){var e="tag-end",t=this.html,n=this.position,r=t.charAt(n.index),i="/"===r;ut(n,t,i?2:1);var o=st(n);this.tokens.push({type:e,close:i,position:{end:o}})}},{key:"scanTagName",value:function(){var e="tag",t=this.html,n=this.position,r=t.length,i=n.index;while(i<r){var o=t.charAt(i),a=!(dt(o)||"/"===o||">"===o);if(a)break;i++}var u=i+1;while(u<r){var c=t.charAt(u),s=!(dt(c)||"/"===c||">"===c);if(!s)break;u++}ct(n,t,u);var l=t.slice(i,u);return this.tokens.push({type:e,content:l}),l}},{key:"scanAttrs",value:function(){var e=this.html,t=this.position,n=this.tokens,r=t.index,i=null,o=r,a=[],u=e.length;while(r<u){var c=e.charAt(r);if(i){var s=c===i;s&&(i=null),r++}else{var l="/"===c||">"===c;if(l){r!==o&&a.push(e.slice(o,r));break}if(mt(r,o,e))r!==o&&a.push(e.slice(o,r)),o=r+1,r++;else{var d="'"===c||'"'===c;d?(i=c,r++):r++}}}ct(t,e,r);for(var f=a.length,h="attribute",v=0;v<f;v++){var p=a[v],g=p.includes("=");if(g){var m=a[v+1];if(m&&m.startsWith("=")){if(m.length>1){var b=p+m;n.push({type:h,content:b}),v+=1;continue}var y=a[v+2];if(v+=1,y){var k=p+"="+y;n.push({type:h,content:k}),v+=1;continue}}}if(p.endsWith("=")){var w=a[v+1];if(w&&!w.includes("=")){var C=p+w;n.push({type:h,content:C}),v+=1;continue}var T=p.slice(0,-1);n.push({type:h,content:T})}else n.push({type:h,content:p})}}},{key:"scanSkipTag",value:function(e){var t=this.html,n=this.position,r=e.toLowerCase(),i=t.length;while(n.index<i){var o=t.indexOf("</",n.index);if(-1===o){this.scanText();break}ct(n,t,o);var a=this.scanTag();if(r===a.toLowerCase())break}}}])}();function yt(e){var t=e.charAt(0),n=e.length-1,r='"'===t||"'"===t;return r&&t===e.charAt(n)?e.slice(1,n):e}var kt="{",wt="}",Ct=".",Tt="#",St=">",Pt="~",Et="+",xt=function(){function e(){(0,f.Z)(this,e),this.styles=[]}return(0,h.Z)(e,[{key:"extractStyle",value:function(e){var t=this,n=/<style\s?[^>]*>((.|\n|\s)+?)<\/style>/g,r=e;return r=r.replace(n,(function(e,n){var r=n.trim();return t.stringToSelector(r),""})),r.trim()}},{key:"stringToSelector",value:function(e){var t=this,n=e.indexOf(kt),r=function(){var r=e.indexOf(wt),i=e.slice(0,n).trim(),o=e.slice(n+1,r);o=o.replace(/:(.*);/g,(function(e,t){var n=t.trim().replace(/ +/g,"+++");return":".concat(n,";")})),o=o.replace(/ /g,""),o=o.replace(/\+\+\+/g," "),/;$/.test(o)||(o+=";"),i.split(",").forEach((function(e){var n=t.parseSelector(e);t.styles.push({content:o,selectorList:n})})),e=e.slice(r+1),n=e.indexOf(kt)};while(n>-1)r()}},{key:"parseSelector",value:function(e){var t=e.trim().replace(/ *([>~+]) */g," $1").replace(/ +/g," ").replace(/\[\s*([^[\]=\s]+)\s*=\s*([^[\]=\s]+)\s*\]/g,"[$1=$2]").split(" "),n=t.map((function(e){var t=e.charAt(0),n={isChild:t===St,isGeneralSibling:t===Pt,isAdjacentSibling:t===Et,tag:null,id:null,class:[],attrs:[]};return e=e.replace(/^[>~+]/,""),e=e.replace(/\[(.+?)\]/g,(function(e,t){var i=t.split("="),o=(0,r.Z)(i,2),a=o[0],u=o[1],c=-1===t.indexOf("="),s={all:c,key:a,value:c?null:u};return n.attrs.push(s),""})),e=e.replace(/([.#][A-Za-z0-9-_]+)/g,(function(e,t){return t[0]===Tt?n.id=t.substr(1):t[0]===Ct&&n.class.push(t.substr(1)),""})),""!==e&&(n.tag=e),n}));return n}},{key:"matchStyle",value:function(e,t,n){var r=this,i=At(this.styles).reduce((function(i,o,a){var u=o.content,c=o.selectorList,s=n[a],l=c[s],d=c[s+1];((null===d||void 0===d?void 0:d.isGeneralSibling)||(null===d||void 0===d?void 0:d.isAdjacentSibling))&&(l=d,s+=1,n[a]+=1);var f=r.matchCurrent(e,t,l);if(f&&l.isGeneralSibling){var h=It(t);while(h){if(h.h5tagName&&r.matchCurrent(h.h5tagName,h,c[s-1])){f=!0;break}h=It(h),f=!1}}if(f&&l.isAdjacentSibling){var v=It(t);if(v&&v.h5tagName){var p=r.matchCurrent(v.h5tagName,v,c[s-1]);p||(f=!1)}else f=!1}if(f){if(s===c.length-1)return i+u;s<c.length-1&&(n[a]+=1)}else l.isChild&&s>0&&(n[a]-=1,r.matchCurrent(e,t,c[n[a]])&&(n[a]+=1));return i}),"");return i}},{key:"matchCurrent",value:function(e,t,n){if(n.tag&&n.tag!==e)return!1;if(n.id&&n.id!==t.id)return!1;if(n.class.length)for(var r=t.className.split(" "),i=0;i<n.class.length;i++){var o=n.class[i];if(-1===r.indexOf(o))return!1}if(n.attrs.length)for(var a=0;a<n.attrs.length;a++){var u=n.attrs[a],c=u.all,s=u.key,l=u.value;if(c&&!t.hasAttribute(s))return!1;var d=t.getAttribute(s);if(d!==yt(l||""))return!1}return!0}}])}();function It(e){var t=e.parentElement;if(!t)return null;var n=e.previousSibling;return n?1===n.nodeType?n:It(n):null}function At(e){return e.sort((function(e,t){var n=Nt(e.selectorList),r=Nt(t.selectorList);if(n!==r)return n-r;var i=Lt(e.selectorList),o=Lt(t.selectorList);if(i!==o)return i-o;var a=Bt(e.selectorList),u=Bt(t.selectorList);return a-u}))}function Nt(e){return e.reduce((function(e,t){return e+(t.id?1:0)}),0)}function Lt(e){return e.reduce((function(e,t){return e+t.class.length+t.attrs.length}),0)}function Bt(e){return e.reduce((function(e,t){return e+(t.tag?1:0)}),0)}function Ot(e,t){for(var n=Object.create(null),r=e.split(","),i=0;i<r.length;i++)n[r[i]]=!0;return t?function(e){return!!n[e.toLowerCase()]}:function(e){return!!n[e]}}var Zt={img:"image",iframe:"web-view"},_t=Object.keys(v.rD).map((function(e){return e.toLowerCase()})).join(","),Rt=Ot(_t,!0),jt=Ot("a,i,abbr,iframe,select,acronym,slot,small,span,bdi,kbd,strong,big,map,sub,sup,br,mark,mark,meter,template,canvas,textarea,cite,object,time,code,output,u,data,picture,tt,datalist,var,dfn,del,q,em,s,embed,samp,b",!0),Mt=Ot("address,fieldset,li,article,figcaption,main,aside,figure,nav,blockquote,footer,ol,details,form,p,dialog,h1,h2,h3,h4,h5,h6,pre,dd,header,section,div,hgroup,table,dl,hr,ul,dt",!0),Dt={li:["ul","ol","menu"],dt:["dl"],dd:["dl"],tbody:["table"],thead:["table"],tfoot:["table"],tr:["table"],td:["table"]};function Ft(e,t){var n=Dt[e];if(n){var r=t.length-1;while(r>=0){var i=t[r].tagName;if(i===e)break;if(n&&n.includes(i))return!0;r--}}return!1}function Ut(e){return ot.html.renderHTMLTag?e:Zt[e]?Zt[e]:Rt(e)?e:Mt(e)?"view":jt(e)?"text":"view"}function Vt(e){var t="=",n=e.indexOf(t);if(-1===n)return[e];var r=e.slice(0,n).trim(),i=e.slice(n+t.length).trim();return[r,i]}function zt(e,t,n,i){return e.filter((function(e){return"comment"!==e.type&&("text"!==e.type||""!==e.content)})).map((function(e){if("text"===e.type){var o=t.createTextNode(e.content);return(0,v.mf)(ot.html.transformText)&&(o=ot.html.transformText(o,e)),null===i||void 0===i||i.appendChild(o),o}var a=t.createElement(Ut(e.tagName));a.h5tagName=e.tagName,null===i||void 0===i||i.appendChild(a),ot.html.renderHTMLTag||(a.className="h5-".concat(e.tagName));for(var u=0;u<e.attributes.length;u++){var c=e.attributes[u],s=Vt(c),l=(0,r.Z)(s,2),d=l[0],f=l[1];if("class"===d)a.className+=" "+yt(f);else{if("o"===d[0]&&"n"===d[1])continue;a.setAttribute(d,null==f||yt(f))}}var h=n.styleTagParser,p=n.descendantList,g=p.slice(),m=h.matchStyle(e.tagName,a,g);return a.setAttribute("style",m+a.style.cssText),zt(e.children,t,{styleTagParser:h,descendantList:g},a),(0,v.mf)(ot.html.transformElement)?ot.html.transformElement(a,e):a}))}function Wt(e,t){var n=new xt;e=n.extractStyle(e);var r=new bt(e).scan(),i={tagName:"",children:[],type:"element",attributes:[]},o={tokens:r,options:ot,cursor:0,stack:[i]};return qt(o),zt(i.children,t,{styleTagParser:n,descendantList:Array(n.styles.length).fill(0)})}function qt(e){var t=e.tokens,n=e.stack,r=e.cursor,i=t.length,o=n[n.length-1].children;while(r<i){var a=t[r];if("tag-start"===a.type){var u=t[++r];r++;var c=u.content.toLowerCase();if(a.close){var s=n.length,l=!1;while(--s>-1)if(n[s].tagName===c){l=!0;break}while(r<i){var d=t[r];if("tag-end"!==d.type)break;r++}if(l){n.splice(s);break}}else{var f=ot.html.closingElements.has(c),h=f;if(h&&(h=!Ft(c,n)),h){var v=n.length-1;while(v>0){if(c===n[v].tagName){n.splice(v);var p=v-1;o=n[p].children;break}v-=1}}var g=[],m=void 0;while(r<i){if(m=t[r],"tag-end"===m.type)break;g.push(m.content),r++}r++;var b=[],y={type:"element",tagName:u.content,attributes:g,children:b};o.push(y);var k=!(m.close||ot.html.voidElements.has(c));if(k){n.push({tagName:c,children:b});var w={tokens:t,cursor:r,stack:n};qt(w),r=w.cursor}}}else o.push(a),r++}e.cursor=r}function Gt(e,t){while(e.firstChild)e.removeChild(e.firstChild);for(var n=Wt(t,e.ownerDocument),r=0;r<n.length;r++)e.appendChild(n[r])}ot.html={skipElements:new Set(["style","script"]),voidElements:new Set(["!doctype","area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),closingElements:new Set(["html","head","body","p","dt","dd","li","option","thead","th","tbody","tr","td","tfoot","colgroup"]),renderHTMLTag:!1},Ne.extend("innerHTML",{set:function(e){Gt.call(this,this,e)},get:function(){return""}});var Ht=function(){function e(t,n,r){(0,f.Z)(this,e),this._stop=!1,this._end=!1,this.defaultPrevented=!1,this.timeStamp=Date.now(),this.type=t.toLowerCase(),this.mpEvent=r,this.bubbles=Boolean(n&&n.bubbles),this.cancelable=Boolean(n&&n.cancelable)}return(0,h.Z)(e,[{key:"stopPropagation",value:function(){this._stop=!0}},{key:"stopImmediatePropagation",value:function(){this._end=this._stop=!0}},{key:"preventDefault",value:function(){this.defaultPrevented=!0}},{key:"target",get:function(){var e,t,n=this.cacheTarget;if(n)return n;var r=Object.create((null===(e=this.mpEvent)||void 0===e?void 0:e.target)||null),i=Pe.document.getElementById(r.id);for(var o in r.dataset=null!==i?i.dataset:v.kT,null===(t=this.mpEvent)||void 0===t?void 0:t.detail)r[o]=this.mpEvent.detail[o];return this.cacheTarget=r,r}},{key:"currentTarget",get:function(){var e,t,n,r,i=this.cacheCurrentTarget;if(i)return i;var o=Pe.document,a=Object.create((null===(e=this.mpEvent)||void 0===e?void 0:e.currentTarget)||null),u=o.getElementById(a.id),c=o.getElementById((null===(n=null===(t=this.mpEvent)||void 0===t?void 0:t.target)||void 0===n?void 0:n.id)||null);if(null===u||u&&u===c)return this.cacheCurrentTarget=this.target,this.target;for(var s in a.dataset=u.dataset,null===(r=this.mpEvent)||void 0===r?void 0:r.detail)a[s]=this.mpEvent.detail[s];return this.cacheCurrentTarget=a,a}}])}();function $t(e,t){if("string"===typeof e)return new Ht(e,{bubbles:!0,cancelable:!0});var n=new Ht(e.type,{bubbles:!0,cancelable:!0},e);for(var r in e)r!==F&&r!==D&&r!==U&&r!==z&&(n[r]=e[r]);return n.type===V&&(null===t||void 0===t?void 0:t.nodeName)===R&&(n[W]=13),n}var Jt={};function Kt(e){var t;v.PT.call("modifyMpEventImpl",e),e.currentTarget||(e.currentTarget=e.target);var n=e.currentTarget,r=(null===(t=n.dataset)||void 0===t?void 0:t.sid)||n.id||"",i=Pe.document.getElementById(r);if(i){var o=function(){var t=$t(e,i);v.PT.call("modifyTaroEvent",t,i),i.dispatchEvent(t)};if(v.PT.isExist("batchedEventUpdates")){var a=e.type;!v.PT.call("isBubbleEvents",a)||!ve(i,a)||a===q&&i.props.catchMove?v.PT.call("batchedEventUpdates",(function(){Jt[a]&&(Jt[a].forEach((function(e){return e()})),delete Jt[a]),o()})):(Jt[a]||(Jt[a]=[])).push(o)}else o()}}var Qt=function(e){function t(){return(0,f.Z)(this,t),(0,u.Z)(this,t,arguments)}return(0,c.Z)(t,e),(0,h.Z)(t,[{key:"value",get:function(){var e=this.props[_];return null==e?"":e},set:function(e){this.setAttribute(_,e)}},{key:"dispatchEvent",value:function(e){if(e.mpEvent){var n=e.mpEvent.detail.value;e.type===j?this.props.value=n:e.type===R&&(this.value=n)}return(0,s.Z)(t,"dispatchEvent",this,3)([e])}}])}(it),Yt=function(){function e(){(0,f.Z)(this,e),this.recorder=new Map}return(0,h.Z)(e,[{key:"start",value:function(e){ot.debug&&this.recorder.set(e,Date.now())}},{key:"stop",value:function(e){if(ot.debug){var t=Date.now(),n=this.recorder.get(e);this.recorder.delete(e);var r=t-n;console.log("".concat(e," \u65f6\u957f\uff1a ").concat(r,"ms"))}}}])}(),Xt=new Yt;function en(e,t){var n,r=t.slice(1),i=e,o="";if(r.some((function(e,r){var a=e.replace(/^\[(.+)\]$/,"$1").replace(/\bcn\b/g,"childNodes");if(i=i[a],(0,v.o8)(i))return!0;if(i.nodeName===M){var u=me.get(i.sid);u&&(n=u,o=t.slice(r+2).join("."))}})),n)return{customWrapper:n,splitedPath:o}}var tn,nn=function(e){function t(){var e;return(0,f.Z)(this,t),e=(0,u.Z)(this,t),e.updatePayloads=[],e.updateCallbacks=[],e.pendingUpdate=!1,e.ctx=null,e.nodeName=y,e.tagName=y.toUpperCase(),e}return(0,c.Z)(t,e),(0,h.Z)(t,[{key:"_path",get:function(){return y}},{key:"_root",get:function(){return this}},{key:"enqueueUpdate",value:function(e){this.updatePayloads.push(e),!this.pendingUpdate&&this.ctx&&this.performUpdate()}},{key:"performUpdate",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1?arguments[1]:void 0;this.pendingUpdate=!0;var r=this.ctx;setTimeout((function(){var i="".concat(m," \u5f00\u59cb\u65f6\u95f4\u6233 ").concat(Date.now());Xt.start(i);var o=Object.create(null),u=new Set(t?["root.cn.[0]","root.cn[0]"]:[]);while(e.updatePayloads.length>0){var c=e.updatePayloads.shift(),s=c.path,l=c.value;s.endsWith("cn")&&u.add(s),o[s]=l}var d=function(e){u.forEach((function(t){e.includes(t)&&e!==t&&delete o[e]}));var t=o[e];(0,v.mf)(t)&&(o[e]=t())};for(var f in o)d(f);if((0,v.mf)(n))return n(o);e.pendingUpdate=!1;var h={},p=new Map;if(t)h=o;else for(var g in o){var y=g.split("."),k=en(e,y);if(k){var w=k.customWrapper,C=k.splitedPath;p.set(w,Object.assign(Object.assign({},p.get(w)||{}),(0,a.Z)({},"i.".concat(C),o[g])))}else h[g]=o[g]}var T=p.size,S=Object.keys(h).length>0,P=T+(S?1:0),E=0,x=function(){++E===P&&(Xt.stop(i),e.flushUpdateCallback(),t&&Xt.stop(b))};T&&p.forEach((function(e,t){t.setData(e,x)})),S&&r.setData(h,x)}),0)}},{key:"enqueueUpdateCallback",value:function(e,t){this.updateCallbacks.push((function(){t?e.call(t):e()}))}},{key:"flushUpdateCallback",value:function(){var e=this.updateCallbacks;if(e.length){var t=e.slice(0);this.updateCallbacks.length=0;for(var n=0;n<t.length;n++)t[n]()}}}])}(it),rn=function(e){function t(e){var n;return(0,f.Z)(this,t),n=(0,u.Z)(this,t),n.nodeType=3,n.nodeName="#text",n._value=e,n}return(0,c.Z)(t,e),(0,h.Z)(t,[{key:"textContent",get:function(){return this._value},set:function(e){ce.record({target:this,type:"characterData",oldValue:this._value}),this._value=e,this.enqueueUpdate({path:"".concat(this._path,".","v"),value:e})}},{key:"nodeValue",get:function(){return this._value},set:function(e){this.textContent=e}},{key:"data",get:function(){return this._value},set:function(e){this.textContent=e}}])}(Ne),on=function(e){function t(){var e;return(0,f.Z)(this,t),e=(0,u.Z)(this,t),e.createEvent=$t,e.nodeType=9,e.nodeName=P,e}return(0,c.Z)(t,e),(0,h.Z)(t,[{key:"createElement",value:function(e){if(e===y)return new nn;var t=v._c.has(e)?new Qt:new it;return t.nodeName=e,t.tagName=e.toUpperCase(),t}},{key:"createElementNS",value:function(e,t){return this.createElement(t)}},{key:"createTextNode",value:function(e){return new rn(e)}},{key:"getElementById",value:function(e){var t=Se.get(e);return(0,v.o8)(t)?null:t}},{key:"querySelector",value:function(e){return/^#/.test(e)?this.getElementById(e.slice(1)):null}},{key:"querySelectorAll",value:function(){return[]}},{key:"createComment",value:function(){var e=new rn("");return e.nodeName=$,e}},{key:"defaultView",get:function(){return Pe.window}}])}(it),an=function(){var e=new on,t=e.createElement.bind(e),n=t(k),r=t(w),i=t(C),o=t(T);o.id=T;var a=t(S);return e.appendChild(n),n.appendChild(r),n.appendChild(i),i.appendChild(a),a.appendChild(o),e.documentElement=n,e.head=r,e.body=i,e};function un(e){return e.style}tn=Pe.document=an();var cn,sn="Macintosh",ln="Intel Mac OS X 10_14_5",dn="AppleWebKit/534.36 (KHTML, like Gecko) NodeJS/v4.1.0 Chrome/76.0.3809.132 Safari/534.36",fn="("+sn+"; "+ln+") "+dn,hn={appCodeName:"Mozilla",appName:"Netscape",appVersion:"5.0 "+fn,cookieEnabled:!0,mimeTypes:[],onLine:!0,platform:"MacIntel",plugins:[],product:"Taro",productSub:"20030107",userAgent:"Mozilla/5.0 "+fn,vendor:"Joyent",vendorSub:""};(function(){var e;"undefined"!==typeof performance&&null!==performance&&performance.now?cn=function(){return performance.now()}:Date.now?(e=Date.now(),cn=function(){return Date.now()-e}):(e=(new Date).getTime(),cn=function(){return(new Date).getTime()-e})})();var vn,pn=0,gn="undefined"!==typeof p&&null!==p?p:function(e){var t=cn(),n=Math.max(pn+16,t);return setTimeout((function(){e(pn=n)}),n-t)},mn="undefined"!==typeof g&&null!==g?g:function(e){clearTimeout(e)},bn=v.PT.call("getEventCenter",v.zW),yn=function(e){function t(){var e;(0,f.Z)(this,t),e=(0,u.Z)(this,t),e.navigator=hn,e.requestAnimationFrame=gn,e.cancelAnimationFrame=mn,e.getComputedStyle=un;var r=[].concat((0,d.Z)(Object.getOwnPropertyNames(n.g||{})),(0,d.Z)(Object.getOwnPropertySymbols(n.g||{})));return r.forEach((function(t){"atob"!==t&&"document"!==t&&(Object.prototype.hasOwnProperty.call(e,t)||(e[t]=n.g[t]))})),e.Date||(e.Date=Date),e}return(0,c.Z)(t,e),(0,h.Z)(t,[{key:"document",get:function(){return Pe.document}},{key:"addEventListener",value:function(e,t){(0,v.HD)(e)&&this.on(e,t,null)}},{key:"removeEventListener",value:function(e,t){(0,v.HD)(e)&&this.off(e,t,null)}},{key:"setTimeout",value:function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(){return setTimeout.apply(void 0,arguments)}))},{key:"clearTimeout",value:function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(){return clearTimeout.apply(void 0,arguments)}))}])}(v.zW);vn=Pe.window=new yn;var kn=function(e){function t(){return(0,f.Z)(this,t),(0,u.Z)(this,t,arguments)}return(0,c.Z)(t,e),(0,h.Z)(t)}(it),wn={app:null,router:null,page:null},Cn=function(){return wn},Tn=new Map,Sn=se();function Pn(e,t){v.PT.call("mergePageInstance",Tn.get(t),e),Tn.set(t,e)}function En(e){return Tn.get(e)}function xn(e){Tn.delete(e)}function In(e){return null==e?"":"/"===e.charAt(0)?e:"/"+e}function An(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];var o=Tn.get(e);if(null!=o){var a=v.PT.call("getLifecycle",o,t);if((0,v.kJ)(a)){var u=a.map((function(e){return e.apply(o,r)}));return u[0]}if((0,v.mf)(a))return a.apply(o,r)}}function Nn(e){if(null==e)return"";var t=Object.keys(e).map((function(t){return t+"="+e[t]})).join("&");return""===t?t:"?"+t}function Ln(e,t){var n=e.indexOf("?");return"".concat(n>-1?e.substring(0,n):e).concat(Nn(t))}function Bn(e){return e+"."+K}function On(e){return e+"."+Q}function Zn(e){return e+"."+Y}function _n(e,t,n,i){var o,u,c=null!==t&&void 0!==t?t:"taro_page_".concat(Sn()),s=(0,r.Z)(v.PT.call("getMiniLifecycleImpl").page,7),l=s[0],d=s[1],f=s[2],h=s[3],p=s[4],g=s[5],m=s[6],y=null,k=!1,w=[];function C(e){var t=e.route||e.__route__||e.$taroPath;wn.router={params:e.$taroParams,path:In(t),$taroPath:e.$taroPath,onReady:Bn(c),onShow:On(c),onHide:Zn(c)},(0,v.o8)(e.exitState)||(wn.router.exitState=e.exitState)}var T=(0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)((0,a.Z)({},l,(function(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;u=new Promise((function(e){o=e})),Xt.start(b),wn.page=this,this.config=i||{};var a=Object.assign({},n,{$taroTimestamp:Date.now()}),s=this.$taroPath=Ln(c,a);null==this.$taroParams&&(this.$taroParams=a),C(this);var l=function(){wn.app.mount(e,s,(function(){y=Pe.document.getElementById(s),(0,v.zx)(null!==y,"\u6ca1\u6709\u627e\u5230\u9875\u9762\u5b9e\u4f8b\u3002"),An(s,J,t.$taroParams),o(),y.ctx=t,y.performUpdate(!0,r)}))};k?w.push(l):l()})),d,(function(){var e=this.$taroPath;An(e,d),k=!0,wn.app.unmount(e,(function(){k=!1,Tn.delete(e),y&&(y.ctx=null,y=null),w.length&&(w.forEach((function(e){return e()})),w=[])}))})),f,(function(){var e=this;u.then((function(){An(e.$taroPath,K),gn((function(){return bn.trigger(Bn(c))})),e.onReady.called=!0}))})),h,(function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};u.then((function(){wn.page=e,C(e),An(e.$taroPath,Q,t),gn((function(){return bn.trigger(On(c))}))}))})),p,(function(){wn.page===this&&(wn.page=null,wn.router=null),An(this.$taroPath,Y),bn.trigger(Zn(c))}));return g.forEach((function(e){T[e]=function(){return An.apply(void 0,[this.$taroPath,e].concat(Array.prototype.slice.call(arguments)))}})),m.forEach((function(t){var n;(e[t]||(null===(n=e.prototype)||void 0===n?void 0:n[t])||e[t.replace(/^on/,"enable")])&&(T[t]=function(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];var o=null===(e=r[0])||void 0===e?void 0:e.target;if(null===o||void 0===o?void 0:o.id){var a=o.id,u=Pe.document.getElementById(a);u&&(o.dataset=u.dataset)}return An.apply(void 0,[this.$taroPath,t].concat(r))})})),T.eh=Kt,(0,v.o8)(n)||(T.data=n),v.PT.call("modifyPageObject",T),T}function Rn(e,t,n){var i=null!==t&&void 0!==t?t:"taro_component_".concat(Sn()),o=null,u=(0,r.Z)(v.PT.call("getMiniLifecycleImpl").component,2),c=u[0],s=u[1],l=(0,a.Z)((0,a.Z)((0,a.Z)({},c,(function(){var t,n=this;Xt.start(b);var r=Ln(i,{id:(null===(t=this.getPageId)||void 0===t?void 0:t.call(this))||Sn()});wn.app.mount(e,r,(function(){o=Pe.document.getElementById(r),(0,v.zx)(null!==o,"\u6ca1\u6709\u627e\u5230\u7ec4\u4ef6\u5b9e\u4f8b\u3002"),n.$taroInstances=Tn.get(r),An(r,J),o.ctx=n,o.performUpdate(!0)}))})),s,(function(){var e=Ln(i,{id:this.getPageId()});wn.app.unmount(e,(function(){Tn.delete(e),o&&(o.ctx=null)}))})),"methods",{eh:Kt});return(0,v.o8)(n)||(l.data=n),[X,ee,te].forEach((function(t){var n;l[t]=null!==(n=e[t])&&void 0!==n?n:v.kT})),l}function jn(e){var t=e===M,n=(0,r.Z)(v.PT.call("getMiniLifecycleImpl").component,2),i=n[0],o=n[1],u=t?(0,a.Z)((0,a.Z)({},i,(function(){var e,t,n=(null===(e=this.data.i)||void 0===e?void 0:e.sid)||(null===(t=this.props.i)||void 0===t?void 0:t.sid);(0,v.HD)(n)&&me.set(n,this)})),o,(function(){var e,t,n=(null===(e=this.data.i)||void 0===e?void 0:e.sid)||(null===(t=this.props.i)||void 0===t?void 0:t.sid);(0,v.HD)(n)&&me.delete(n)})):v.kT;return Object.assign({properties:{i:{type:Object,value:(0,a.Z)({},"nn",(0,v.W)(v.rD)[B]._num)},l:{type:String,value:""}},options:{addGlobalClass:!0,virtualHost:!t},methods:{eh:Kt}},u)}var Mn=function(e,t){var n=wn.router,r=function(){setTimeout((function(){t?e.call(t):e()}),1)};if(null!==n){var i=null,o=n.$taroPath;i=Pe.document.getElementById(o),(null===i||void 0===i?void 0:i.pendingUpdate)?i.enqueueUpdateCallback(e,t):r()}else r()}},74455:function(e,t,n){"use strict";n.d(t,{kT:function(){return be},zW:function(){return ie},kC:function(){return Ce},_c:function(){return re},zx:function(){return Te},W:function(){return Ie},PT:function(){return me},rD:function(){return ne},kJ:function(){return he},jn:function(){return se},mf:function(){return le},Ft:function(){return ue},hj:function(){return de},Kn:function(){return ce},HD:function(){return oe},o8:function(){return ae},ku:function(){return xe},xi:function(){return Ae},ZT:function(){return ye},Ig:function(){return _e},CA:function(){return we},eu:function(){return ke}});var r=n(22276),i=n(20597),o=n(21867),a=n(93212),u=n(57042),c=n(24460),s="[]",l="",d="!0",f="!1",h={bindTouchStart:l,bindTouchMove:l,bindTouchEnd:l,bindTouchCancel:l,bindLongTap:l},v={animation:l,bindAnimationStart:l,bindAnimationIteration:l,bindAnimationEnd:l,bindTransitionEnd:l};function p(e){return"'".concat(e,"'")}var g=Object.assign(Object.assign({"hover-class":p("none"),"hover-stop-propagation":f,"hover-start-time":"50","hover-stay-time":"400"},h),v),m={type:l,size:"23",color:l},b=Object.assign({longitude:l,latitude:l,scale:"16",markers:s,covers:l,polyline:s,circles:s,controls:s,"include-points":s,"show-location":l,"layer-style":"1",bindMarkerTap:l,bindControlTap:l,bindCalloutTap:l,bindUpdated:l},h),y={percent:l,"stroke-width":"6",color:p("#09BB07"),activeColor:p("#09BB07"),backgroundColor:p("#EBEBEB"),active:f,"active-mode":p("backwards"),"show-info":f},k={nodes:s},w={selectable:f,space:l,decode:f},C=Object.assign({size:p("default"),type:l,plain:f,disabled:l,loading:f,"form-type":l,"open-type":l,"hover-class":p("button-hover"),"hover-stop-propagation":f,"hover-start-time":"20","hover-stay-time":"70",name:l},h),T={value:l,disabled:l,checked:f,color:p("#09BB07"),name:l},S={bindChange:l,name:l},P={"report-submit":f,bindSubmit:l,bindReset:l,name:l},E={value:l,type:p(l),password:f,placeholder:l,"placeholder-style":l,"placeholder-class":p("input-placeholder"),disabled:l,maxlength:"140","cursor-spacing":"0",focus:f,"confirm-type":p("done"),"confirm-hold":f,cursor:"i.value.length","selection-start":"-1","selection-end":"-1",bindInput:l,bindFocus:l,bindBlur:l,bindConfirm:l,name:l},x={for:l,name:l},I={mode:p("selector"),disabled:l,range:l,"range-key":l,value:l,start:l,end:l,fields:p("day"),"custom-item":l,name:l,bindCancel:l,bindChange:l,bindColumnChange:l},A={value:l,"indicator-style":l,"indicator-class":l,"mask-style":l,"mask-class":l,bindChange:l,name:l},N={name:l},L={value:l,checked:f,disabled:l,color:p("#09BB07"),name:l},B={bindChange:l,name:l},O={min:"0",max:"100",step:"1",disabled:l,value:"0",activeColor:p("#1aad19"),backgroundColor:p("#e9e9e9"),"block-size":"28","block-color":p("#ffffff"),"show-value":f,bindChange:l,bindChanging:l,name:l},Z={checked:f,disabled:l,type:p("switch"),color:p("#04BE02"),bindChange:l,name:l},_={value:l,placeholder:l,"placeholder-style":l,"placeholder-class":p("textarea-placeholder"),disabled:l,maxlength:"140","auto-focus":f,focus:f,"auto-height":f,fixed:f,"cursor-spacing":"0",cursor:"-1","selection-start":"-1","selection-end":"-1",bindFocus:l,bindBlur:l,bindLineChange:l,bindInput:l,bindConfirm:l,name:l},R={src:l,bindLoad:"eh",bindError:"eh"},j=Object.assign({"scroll-top":f},h),M={"scale-area":f},D=Object.assign(Object.assign({direction:"none",inertia:f,"out-of-bounds":f,x:l,y:l,damping:"20",friction:"2",disabled:l,scale:f,"scale-min":"0.5","scale-max":"10","scale-value":"1",bindChange:l,bindScale:l,bindHTouchMove:l,bindVTouchMove:l,width:p("10px"),height:p("10px")},h),v),F=Object.assign(Object.assign({"scroll-x":f,"scroll-y":f,"upper-threshold":"50","lower-threshold":"50","scroll-top":l,"scroll-left":l,"scroll-into-view":l,"scroll-with-animation":f,"enable-back-to-top":f,bindScrollToUpper:l,bindScrollToLower:l,bindScroll:l},h),v),U=Object.assign({"indicator-dots":f,"indicator-color":p("rgba(0, 0, 0, .3)"),"indicator-active-color":p("#000000"),autoplay:f,current:"0",interval:"5000",duration:"500",circular:f,vertical:f,"previous-margin":p("0px"),"next-margin":p("0px"),"display-multiple-items":"1",bindChange:l,bindTransition:l,bindAnimationFinish:l},h),V={"item-id":l},z={url:l,"open-type":p("navigate"),delta:"1","hover-class":p("navigator-hover"),"hover-stop-propagation":f,"hover-start-time":"50","hover-stay-time":"600",bindSuccess:l,bindFail:l,bindComplete:l},W={id:l,src:l,loop:f,controls:f,poster:l,name:l,author:l,bindError:l,bindPlay:l,bindPause:l,bindTimeUpdate:l,bindEnded:l},q={"device-position":p("back"),flash:p("auto"),bindStop:l,bindError:l},G=Object.assign({src:l,mode:p("scaleToFill"),"lazy-load":f,bindError:l,bindLoad:l},h),H=Object.assign({src:l,autoplay:f,muted:f,orientation:p("vertical"),"object-fit":p("contain"),"background-mute":f,"min-cache":"1","max-cache":"3",bindStateChange:l,bindFullScreenChange:l,bindNetStatus:l},v),$=Object.assign({src:l,duration:l,controls:d,"danmu-list":l,"danmu-btn":l,"enable-danmu":l,autoplay:f,loop:f,muted:f,"initial-time":"0","page-gesture":f,direction:l,"show-progress":d,"show-fullscreen-btn":d,"show-play-btn":d,"show-center-play-btn":d,"enable-progress-gesture":d,"object-fit":p("contain"),poster:l,"show-mute-btn":f,bindPlay:l,bindPause:l,bindEnded:l,bindTimeUpdate:l,bindFullScreenChange:l,bindWaiting:l,bindError:l},v),J=Object.assign({"canvas-id":l,"disable-scroll":f,bindError:l},h),K={"unit-id":l,"ad-intervals":l,bindLoad:l,bindError:l,bindClose:l},Q={src:l,bindMessage:l,bindLoad:l,bindError:l},Y={},X={name:l},ee={name:l},te={name:l},ne={View:g,Icon:m,Progress:y,RichText:k,Text:w,Button:C,Checkbox:T,CheckboxGroup:S,Form:P,Input:E,Label:x,Picker:I,PickerView:A,PickerViewColumn:N,Radio:L,RadioGroup:B,Slider:O,Switch:Z,CoverImage:R,Textarea:_,CoverView:j,MovableArea:M,MovableView:D,ScrollView:F,Swiper:U,SwiperItem:V,Navigator:z,Audio:W,Camera:q,Image:G,LivePlayer:H,Video:$,Canvas:J,Ad:K,WebView:Q,Block:Y,Map:b,Slot:ee,SlotView:X,NativeSlot:te},re=new Set(["input","checkbox","picker","picker-view","radio","slider","switch","textarea"]),ie=(new Set(["input","textarea"]),new Set(["progress","icon","rich-text","input","textarea","slider","switch","audio","ad","official-account","open-data","navigation-bar"]),new Map([["view",-1],["catch-view",-1],["cover-view",-1],["static-view",-1],["pure-view",-1],["block",-1],["text",-1],["static-text",6],["slot",8],["slot-view",8],["label",6],["form",4],["scroll-view",4],["swiper",4],["swiper-item",4]]),function(){function e(t){var n;(0,u.Z)(this,e),this.callbacks=null!==(n=null===t||void 0===t?void 0:t.callbacks)&&void 0!==n?n:{}}return(0,c.Z)(e,[{key:"on",value:function(t,n,r){var i,o,a,u;if(!n)return this;t=t.split(e.eventSplitter),this.callbacks||(this.callbacks={});var c=this.callbacks;while(i=t.shift())u=c[i],o=u?u.tail:{},o.next=a={},o.context=r,o.callback=n,c[i]={tail:a,next:u?u.next:o};return this}},{key:"once",value:function(e,t,n){var r=this,i=function(){for(var o=arguments.length,a=new Array(o),u=0;u<o;u++)a[u]=arguments[u];t.apply(r,a),r.off(e,i,n)};return this.on(e,i,n),this}},{key:"off",value:function(t,n,r){var i,o,a,u,c,s;if(!(o=this.callbacks))return this;if(!(t||n||r))return delete this.callbacks,this;t=t?t.split(e.eventSplitter):Object.keys(o);while(i=t.shift())if(a=o[i],delete o[i],a&&(n||r)){u=a.tail;while((a=a.next)!==u)c=a.callback,s=a.context,(n&&c!==n||r&&s!==r)&&this.on(i,c,s)}return this}},{key:"trigger",value:function(t){var n,r,i,o;if(!(i=this.callbacks))return this;t=t.split(e.eventSplitter);var a=[].slice.call(arguments,1);while(n=t.shift())if(r=i[n]){o=r.tail;while((r=r.next)!==o)r.callback.apply(r.context||this,a)}return this}}])}());function oe(e){return"string"===typeof e}function ae(e){return"undefined"===typeof e}function ue(e){return null===e}function ce(e){return null!==e&&"object"===(0,a.Z)(e)}function se(e){return!0===e||!1===e}function le(e){return"function"===typeof e}function de(e){return"number"===typeof e}ie.eventSplitter=/\s+/;var fe,he=Array.isArray;(function(e){e[e["SINGLE"]=0]="SINGLE",e[e["MULTI"]=1]="MULTI",e[e["WATERFALL"]=2]="WATERFALL"})(fe||(fe={}));var ve={app:["onLaunch","onShow","onHide"],page:["onLoad","onUnload","onReady","onShow","onHide",["onPullDownRefresh","onReachBottom","onPageScroll","onResize","onTabItemTap","onTitleClick","onOptionMenuClick","onPopMenuClick","onPullIntercept","onAddToFavorites"],["onShareAppMessage","onShareTimeline"]],component:["attached","detached"]};function pe(e,t){return{type:e,initial:t||null}}var ge=function(e){function t(e,n){var r;for(var o in(0,u.Z)(this,t),r=(0,i.Z)(this,t,[n]),r.hooks=e,e){var a=e[o].initial;le(a)&&r.on(o,a)}return r}return(0,o.Z)(t,e),(0,c.Z)(t,[{key:"tapOneOrMany",value:function(e,t){var n=this,r=le(t)?[t]:t;r.forEach((function(t){return n.on(e,t)}))}},{key:"tap",value:function(e,t){var n=this.hooks,r=n[e],i=r.type,o=r.initial;i===fe.SINGLE?(this.off(e),this.on(e,le(t)?t:t[t.length-1])):(o&&this.off(e,o),this.tapOneOrMany(e,t))}},{key:"call",value:function(e){var t,n=this.hooks[e];if(n){var r=n.type,i=this.callbacks;if(i){var o=i[e];if(o){for(var a=o.tail,u=o.next,c=arguments.length,s=new Array(c>1?c-1:0),l=1;l<c;l++)s[l-1]=arguments[l];var d,f=s;while(u!==a){if(d=null===(t=u.callback)||void 0===t?void 0:t.apply(u.context||this,f),r===fe.WATERFALL){var h=[d];f=h}u=u.next}return d}}}}},{key:"isExist",value:function(e){var t;return Boolean(null===(t=this.callbacks)||void 0===t?void 0:t[e])}}])}(ie),me=new ge({getMiniLifecycle:pe(fe.SINGLE,(function(e){return e})),getMiniLifecycleImpl:pe(fe.SINGLE,(function(){return this.call("getMiniLifecycle",ve)})),getLifecycle:pe(fe.SINGLE,(function(e,t){return e[t]})),getPathIndex:pe(fe.SINGLE,(function(e){return"[".concat(e,"]")})),getEventCenter:pe(fe.SINGLE,(function(e){return new e})),isBubbleEvents:pe(fe.SINGLE,(function(e){var t=new Set(["touchstart","touchmove","touchcancel","touchend","touchforcechange","tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend"]);return t.has(e)})),getSpecialNodes:pe(fe.SINGLE,(function(){return["view","text","image"]})),onRemoveAttribute:pe(fe.SINGLE),batchedEventUpdates:pe(fe.SINGLE),mergePageInstance:pe(fe.SINGLE),modifyPageObject:pe(fe.SINGLE),createPullDownComponent:pe(fe.SINGLE),getDOMNode:pe(fe.SINGLE),modifyHydrateData:pe(fe.SINGLE),modifySetAttrPayload:pe(fe.SINGLE),modifyRmAttrPayload:pe(fe.SINGLE),onAddEvent:pe(fe.SINGLE),modifyMpEvent:pe(fe.MULTI),modifyMpEventImpl:pe(fe.SINGLE,(function(e){try{this.call("modifyMpEvent",e)}catch(e){console.warn("[Taro modifyMpEvent hook Error]: ",e)}})),modifyTaroEvent:pe(fe.MULTI),modifyDispatchEvent:pe(fe.MULTI),initNativeApi:pe(fe.MULTI),patchElement:pe(fe.MULTI)}),be={},ye=function(){};function ke(e){return e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}function we(e){for(var t="",n=!1,r=0;r<e.length;r++)"-"!==e[r]?(t+=n?e[r].toUpperCase():e[r],n=!1):n=!0;return t}function Ce(e){return e.charAt(0).toUpperCase()+e.slice(1)}Object.prototype.hasOwnProperty;function Te(e,t){if(!e)throw new Error(t)}var Se=1,Pe=(new Date).getTime().toString();function Ee(){return Pe+Se++}function xe(e){return Object.keys(e).forEach((function(t){t in ne?Object.assign(ne[t],e[t]):ne[t]=e[t]})),ne}function Ie(e){var t={},n=e.View,r={"#text":{},StaticView:n,StaticImage:e.Image,StaticText:e.Text,PureView:n,CatchView:n};return e=Object.assign(Object.assign({},e),r),Object.keys(e).sort((function(e,t){var n=/^(Static|Pure|Catch)*(View|Image|Text)$/,r=n.test(e),i=n.test(t);return r&&i?e>t?1:-1:r?-1:i||e>=t?1:-1})).forEach((function(n,r){var i={_num:String(r)};Object.keys(e[n]).filter((function(e){return!/^bind/.test(e)&&!["focus","blur"].includes(e)})).sort().forEach((function(e,t){i[we(e)]="p"+t})),t[ke(n)]=i})),t}function Ae(e,t){var n=t||me,r=Object.keys(e);r.forEach((function(t){n.tap(t,e[t])}))}function Ne(e){return function(){console.warn("\u5c0f\u7a0b\u5e8f\u6682\u4e0d\u652f\u6301 ".concat(e))}}function Le(e,t){var n="__key_",r=["navigateTo","redirectTo","reLaunch","switchTab"];if(r.indexOf(e)>-1){var i=t.url=t.url||"",o=i.indexOf("?")>-1,a=Ee();t.url+=(o?"&":"?")+"".concat(n,"=").concat(a)}}var Be=new Set(["addPhoneContact","authorize","canvasGetImageData","canvasPutImageData","canvasToTempFilePath","checkSession","chooseAddress","chooseImage","chooseInvoiceTitle","chooseLocation","chooseVideo","clearStorage","closeBLEConnection","closeBluetoothAdapter","closeSocket","compressImage","connectSocket","createBLEConnection","downloadFile","exitMiniProgram","getAvailableAudioSources","getBLEDeviceCharacteristics","getBLEDeviceServices","getBatteryInfo","getBeacons","getBluetoothAdapterState","getBluetoothDevices","getClipboardData","getConnectedBluetoothDevices","getConnectedWifi","getExtConfig","getFileInfo","getImageInfo","getLocation","getNetworkType","getSavedFileInfo","getSavedFileList","getScreenBrightness","getSetting","getStorage","getStorageInfo","getSystemInfo","getUserInfo","getWifiList","hideHomeButton","hideShareMenu","hideTabBar","hideTabBarRedDot","loadFontFace","login","makePhoneCall","navigateBack","navigateBackMiniProgram","navigateTo","navigateToBookshelf","navigateToMiniProgram","notifyBLECharacteristicValueChange","hideKeyboard","hideLoading","hideNavigationBarLoading","hideToast","openBluetoothAdapter","openDocument","openLocation","openSetting","pageScrollTo","previewImage","queryBookshelf","reLaunch","readBLECharacteristicValue","redirectTo","removeSavedFile","removeStorage","removeTabBarBadge","requestSubscribeMessage","saveFile","saveImageToPhotosAlbum","saveVideoToPhotosAlbum","scanCode","sendSocketMessage","setBackgroundColor","setBackgroundTextStyle","setClipboardData","setEnableDebug","setInnerAudioOption","setKeepScreenOn","setNavigationBarColor","setNavigationBarTitle","setScreenBrightness","setStorage","setTabBarBadge","setTabBarItem","setTabBarStyle","showActionSheet","showFavoriteGuide","showLoading","showModal","showShareMenu","showTabBar","showTabBarRedDot","showToast","startBeaconDiscovery","startBluetoothDevicesDiscovery","startDeviceMotionListening","startPullDownRefresh","stopBeaconDiscovery","stopBluetoothDevicesDiscovery","stopCompass","startCompass","startAccelerometer","stopAccelerometer","showNavigationBarLoading","stopDeviceMotionListening","stopPullDownRefresh","switchTab","uploadFile","vibrateLong","vibrateShort","writeBLECharacteristicValue"]);function Oe(e){return function(){var t,n=null===(t=e.getSystemInfoSync)||void 0===t?void 0:t.call(e);if(!n)return!1;var r=n.platform,i=r.toLowerCase();return"android"===i||"devtools"===i}}function Ze(e){return function(t){t=t?oe(t)?{url:t}:t:{};var n,r=t.success,i=t.fail,o=t.complete,a=new Promise((function(a,u){t.success=function(e){r&&r(e),a(e)},t.fail=function(e){i&&i(e),u(e)},t.complete=function(e){o&&o(e)},n=e.request(t)}));return je(n,a),a.abort=function(e){return e&&e(),n&&n.abort(),a},a}}function _e(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=n.needPromiseApis||[],o=new Set([].concat((0,r.Z)(i),(0,r.Z)(Be))),a=["getEnv","interceptors","Current","getCurrentInstance","options","nextTick","eventCenter","Events","preload","webpackJsonp"],u=new Set(n.isOnlyPromisify?i:Object.keys(t).filter((function(e){return-1===a.indexOf(e)})));n.modifyApis&&n.modifyApis(u),u.forEach((function(r){if(o.has(r)){var i=r;e[i]=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length,o=new Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];var u=i;if("string"===typeof e)return o.length?t[u].apply(t,[e].concat(o)):t[u](e);if(n.transformMeta){var c=n.transformMeta(u,e);if(u=c.key,e=c.options,!t.hasOwnProperty(u))return Ne(u)()}var s=null,l=Object.assign({},e);Le(u,e);var d=new Promise((function(r,i){l.success=function(t){var i,o;null===(i=n.modifyAsyncResult)||void 0===i||i.call(n,u,t),null===(o=e.success)||void 0===o||o.call(e,t),r("connectSocket"===u?Promise.resolve().then((function(){return s?Object.assign(s,t):t})):t)},l.fail=function(t){var n;null===(n=e.fail)||void 0===n||n.call(e,t),i(t)},l.complete=function(t){var n;null===(n=e.complete)||void 0===n||n.call(e,t)},s=o.length?t[u].apply(t,[l].concat(o)):t[u](l)}));return["uploadFile","downloadFile"].includes(u)&&(je(s,d),d.progress=function(e){return null===s||void 0===s||s.onProgressUpdate(e),d},d.abort=function(e){return null===e||void 0===e||e(),null===s||void 0===s||s.abort(),d}),d}}else{var a=r;if(n.transformMeta&&(a=n.transformMeta(r,{}).key),!t.hasOwnProperty(a))return void(e[r]=Ne(r));le(t[r])?e[r]=function(){for(var e=arguments.length,i=new Array(e),o=0;o<e;o++)i[o]=arguments[o];return n.handleSyncApis?n.handleSyncApis(r,t,i):t[a].apply(t,i)}:e[r]=t[a]}})),!n.isOnlyPromisify&&Re(e,t,n)}function Re(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e.canIUseWebp=Oe(e),e.getCurrentPages=getCurrentPages||Ne("getCurrentPages"),e.getApp=getApp||Ne("getApp"),e.env=t.env||{};try{e.requirePlugin=requirePlugin||Ne("requirePlugin")}catch(t){e.requirePlugin=Ne("requirePlugin")}var r=n.request||Ze(t);function i(e){return r(e.requestParams)}var o=new e.Link(i);e.request=o.request.bind(o),e.addInterceptor=o.addInterceptor.bind(o),e.cleanInterceptors=o.cleanInterceptors.bind(o),e.miniGlobal=e.options.miniGlobal=t}function je(e,t){if(e&&t){var n=["abort","onHeadersReceived","offHeadersReceived","onProgressUpdate","offProgressUpdate","onChunkReceived","offChunkReceived"];e&&n.forEach((function(n){n in e&&(t[n]=e[n].bind(e))}))}}},92954:function(e,t,n){var r=n(32180),i=r.hooks,o=n(99671).Z;i.isExist("initNativeApi")&&i.call("initNativeApi",o),e.exports=o,e.exports["default"]=e.exports},44560:function(e,t,n){"use strict";var r=n(32180);Component((0,r.createRecursiveComponentConfig)())},99313:function(e,t,n){"use strict";var r=n(32180);Component((0,r.createRecursiveComponentConfig)("custom-wrapper"))}}]);
//# sourceMappingURL=taro.js.map