{"pages": ["pages/carNew/isd/indexNew/index", "cwx/component/cwebview/cwebview"], "subPackages": [{"root": "cwx/component/perInfoProtectGuide", "pages": ["index", "pwebview"]}, {"root": "pages/testconfig", "pages": ["testconfig"]}, {"root": "cwx/component/extraCweb", "pages": ["cweb1", "cweb2", "cweb3", "cweb4", "cweb5", "cweb6", "cweb7", "cweb8"]}, {"root": "cwx/component/extraScweb", "pages": ["scweb1", "scweb2", "scweb3", "scweb4", "scweb5", "scweb6"]}, {"root": "cwx/component/messageRecommend", "pages": ["index"]}, {"root": "cwx/component/calendar", "pages": ["calendar"]}, {"root": "cwx/component/city", "pages": ["city"]}, {"root": "cwx/component/country", "pages": ["country"]}, {"root": "cwx/component/ocr", "pages": ["ocr"]}, {"root": "cwx/component/navbar", "pages": ["navbar"]}, {"root": "cwx/component/perInfoProtectFloat", "pages": ["index"]}, {"root": "cwx/component/authorizationFloat", "pages": ["index"]}, {"root": "cwx/component/shareFloat", "pages": ["shareFloat"]}, {"root": "pages/pay/", "pages": ["directback/index", "directpay/index", "realname/index", "holdpay/index", "refund/index"]}, {"root": "pages/market/advertiseNew/", "pages": ["ad-sdk"]}, {"root": "pages/paynew/", "pages": ["directpay/index", "paywallet/paywallet", "holdpay/index", "holdpayDetail/index"]}], "permission": {"scope.userLocation": {"desc": "将获取您设备所在定位，为您推荐所在城市附近租车网点"}}, "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "backgroundColor": "#efefef"}, "functionalPages": {"independent": true}, "usingComponents": {"perinfo-protect-float": "/cwx/component/perInfoProtectFloat/index", "CustomWrapper": "/taroBase/custom-wrapper", "authorizationFloat": "/cwx/component/authorizationFloat/index"}, "lazyCodeLoading": "requiredComponents", "sitemapLocation": "sitemap.json"}